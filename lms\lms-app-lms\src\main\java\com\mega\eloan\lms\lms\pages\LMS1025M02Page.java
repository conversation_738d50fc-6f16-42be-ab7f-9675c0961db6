
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/7/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/7/29,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1025m02")
public class LMS1025M02Page extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		super.execute(model, params);
		
		renderJsI18N(LMS1025M02Page.class);
	}

	public Class<? extends Meta> getDomainClass() {
		return null;
	}

}
