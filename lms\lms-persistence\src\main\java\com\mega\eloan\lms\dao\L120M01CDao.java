/* 
 * L120M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01C;

/** 簽報書額度明細關聯檔 **/
public interface L120M01CDao extends IGenericDao<L120M01C> {

	L120M01C findByOid(String oid);

	L120M01C findoneByRefMainId(String mainId);

	List<L120M01C> findByMainId(String mainId);

	List<L120M01C> findByMainIdandItemType(String mainId, String itemType);

	L120M01C findByUniqueKey(String mainId, String itemType, String refMainId);

}