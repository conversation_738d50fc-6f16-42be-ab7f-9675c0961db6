/* 
 * L140S02DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140S02DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S02D;

/** 分段利率明細檔 **/
@Repository
public class L140S02DDaoImpl extends LMSJpaDao<L140S02D, String> implements
		L140S02DDao {

	@Override
	public L140S02D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S02D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140S02D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02D> findByMainIdOrderbyPhase(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("phase");
		List<L140S02D> list = createQuery(search).getResultList();
		return list;
	}
	
	
	@Override
	public L140S02D findByUniqueKey(String mainId, Integer seq, Integer phase) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (phase != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "phase", phase);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140S02D> findByIndex01(String mainId, Integer seq,
			Integer phase) {
		ISearch search = createSearchTemplete();
		List<L140S02D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (seq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if (phase != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "phase", phase);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140S02D> findByMainidSeq(String mainId, Integer seq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addOrderBy("phase");
		List<L140S02D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02D> findByMainidSeqIsUseBox(String mainId, Integer seq,
			String IsUseBox) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "isUseBox", IsUseBox);
		List<L140S02D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140S02D> findByMainidSeqIsUseBoxOrderbyPhase(String mainId, Integer seq, String isUseBox) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		if(isUseBox!=null){
			search.addSearchModeParameters(SearchMode.EQUALS, "isUseBox", isUseBox);
		}
		search.addOrderBy("phase");
		List<L140S02D> list = createQuery(search).getResultList();
		return list;
	}
}