package com.mega.eloan.lms.batch.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.lms.batch.service.LmsBatchCommonService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C122M01A;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  SLMS-00014 企金個人基本資料補行業別(含海外個人戶)
 * </pre>
 * 
 * @since 2013/8/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/8/13,007625,new
 *          </ul>
 */
@Service("lmsbatch0002serviceimpl")
public class LmsBatch0002ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(LmsBatch0002ServiceImpl.class);

	@Resource
	LmsBatchCommonService lmsBatchCommonService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = new JSONObject();

		try {

			JSONObject request = json.getJSONObject("request");
			String type = request.getString("type");

			if (Util.equals(type, "2")) {
				// e-Loan授信對象別新舊轉換
				String errMsg = lmsBatchCommonService.doLmsBatch0002();
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0002 執行失敗！==>"
									+ errMsg);
					return result;
				}

			} else if (Util.equals(type, "3")) {
				// DB2 (MIS、AS400)授信對象別新舊轉換
				String errMsg = lmsBatchCommonService.doLmsBatch0003();
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0003 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "4")) {
				// G-104-0333-001 配合萬磅分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權。
				String errMsg = lmsBatchCommonService.doLmsBatch0004(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0004 執行失敗！==>"
									+ errMsg);
					return result;
				}

			} else if (Util.equals(type, "5")) {
				// J-105-0144-001 Web e-Loan海外授信管理系統，修改Enbridge
				// Inc.簽報書之借款人統編.，由原「USZ01803120」改為「CAZ00584030」。
				String errMsg = lmsBatchCommonService.doLmsBatch0005(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0005 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "6")) {
				// J-105-0185-001
				// 請提供103年度及104年度國內所有分行之新作、增額及續約之授信件數及額度金額(包含企金及消金)
				String errMsg = lmsBatchCommonService.doLmsBatch0006(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0006 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "7")) {
				// J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
				// SLMS-00041 e-Loan交換票據抵用科目調整補上傳a-Loan
				String errMsg = lmsBatchCommonService.doLmsBatch0007(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0007 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "8")) {
				String errMsg = lmsBatchCommonService.doLmsBatch0008(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0008 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "9")) {
				String errMsg = lmsBatchCommonService.doLmsBatch0009(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0009 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "10")) {
				String errMsg = lmsBatchCommonService.doLmsBatch0010(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0010 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "11")) {
				String errMsg = lmsBatchCommonService.doLmsBatch0011(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0011 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "12")) {
				// G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
				String errMsg = lmsBatchCommonService.doLmsBatch0012(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0012 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "13")) {
				// G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
				String errMsg = lmsBatchCommonService.doLmsBatch0013(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0013 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "14")) {
				// J-107-0263-001 Web e-Loan 授信系統配合國金部企金覆審清單檢核機制修改資料
				String errMsg = lmsBatchCommonService.doLmsBatch0014();
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0014 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "15")) {
				// G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
				String errMsg = lmsBatchCommonService.doLmsBatch0015(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0015 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "16")) {
				// J-107-0357_05097_B1002 Web
				// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
				String errMsg = lmsBatchCommonService.doLmsBatch0016(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0016執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "17")) {
				// J-108-0023_05097_B1001 修改web e-loan授信覆審系統，南京東路分行授信戶覆審案件為不覆審。
				String errMsg = lmsBatchCommonService.doLmsBatch0017(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0017執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "18")) {
				// J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
				String errMsg = lmsBatchCommonService.doLmsBatch0018(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0018執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "19")) {
				// J-108-0086 電子文件清理
				String errMsg = lmsBatchCommonService.doLmsBatch0019(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0019執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "20")) {
				// J-108-0116 企金處共同行銷
				String errMsg = lmsBatchCommonService.doLmsBatch0020(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0020執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "21")) {
				// J-108-0116 企金處共同行銷 塞 MIS.SYNBANK 資料
				String errMsg = lmsBatchCommonService.doLmsBatch0021();
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0021 執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "22")) {
				// J-108-0212_07625_B1001 修正三多分行********異常通報
				lmsBatchCommonService.doLmsBatch0022();

			} else if (Util.equals(type, "23")) {
				// J-108-0210_05097_B1001 Web e-Loan企金授信新增上傳借款人基本資料的實收資本額
				String errMsg = lmsBatchCommonService.doLmsBatch0023(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0023執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "24")) {
				// J-108-0242_05097_B1001 Web
				// e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
				String errMsg = lmsBatchCommonService.doLmsBatch0024(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0024執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "25")) {
				// M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料
				String errMsg = lmsBatchCommonService.doLmsBatch0025(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0025執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "26")) {
				// Oracle reject list 測試
				String errMsg = lmsBatchCommonService.doLmsBatch0026(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0025執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "27")) {
				// J-109-0050_05097_B1001
				// 修改國金部簽報書2019國金行(兆)授字第00122號借戶ID由INZ0002372換成INZ0000120
				String errMsg = lmsBatchCommonService.doLmsBatch0027(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0027執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "28")) {
				// J-109-0135 發簡訊
				String errMsg = lmsBatchCommonService.doLmsBatch0028(request);
				if (!errMsg.contains("Exception")) {
					result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl執行成功！" + errMsg);
					result.element(WebBatchCode.P_RC_MSG, errMsg);
					return result;
				} else {
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0028執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "29")) {
				// J-109-0135 查簡訊發送結果
				String errMsg = lmsBatchCommonService.doLmsBatch0029(request);
				if (!errMsg.contains("Exception")) {
					result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl執行成功！" + errMsg);
					result.element(WebBatchCode.P_RC_MSG, errMsg);
					return result;
				} else {
					result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0029執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "30")) {
				// J-109-0235_05097_B1003 Web e-loan國內企金授信新增兆元振興融資方案
				String errMsg = lmsBatchCommonService.doLmsBatch0030(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0030執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "31")) {
				// J-108-0345 貸後管理 最新一筆維護資料之附件檔案
				String errMsg = lmsBatchCommonService.doLmsBatch0031(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0031執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "32")) {
				// 定期檢視 BDOCFILE
				String errMsg = lmsBatchCommonService.doLmsBatch0032(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0032執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "33")) {
				// M-109-0210_05097_B1001 Web
				// e-Loan企金授信配合DW產生額度未引入帳務系統之報表，上傳授信科目到ELF447N
				String errMsg = lmsBatchCommonService.doLmsBatch0033(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0033執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "34")) {
				// J-109-0290 實地覆審提醒mail
				String errMsg = lmsBatchCommonService.doLmsBatch0034(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0034執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "35")) {
				// J-108-0345 貸後管理 最新一筆維護資料之證明文件說明
				String errMsg = lmsBatchCommonService.doLmsBatch0035(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0035執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "36")) {
				// 更新主從債務人最佳信用品質保證人的國別註記
				String errMsg = lmsBatchCommonService.doLmsBatch0036(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0036執行失敗！==>"
									+ errMsg);
					return result;
				}

			} else if (Util.equals(type, "37")) {
				// 配合2021/02專案金檢，產生擔保品檔案
				String errMsg = lmsBatchCommonService.doLmsBatch0037(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0037執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "38")) {
				// 配合2021/02專案金檢，產生擔保品檔案
				String errMsg = lmsBatchCommonService.doLmsBatch0038(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0038執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "39")) {
				// J-109-0496 貸後管理 理財商品贖回追蹤
				String errMsg = lmsBatchCommonService.doLmsBatch0039(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0039執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "40")) {
				// 南京東路客戶移轉國外部
				String errMsg = lmsBatchCommonService.doLmsBatch0040(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0040執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "41")) {
				// J-110-0182_05097_B1001 Web
				// e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
				String errMsg = lmsBatchCommonService.doLmsBatch0041(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0041執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "42")) {
				// J-108-0245_05097_B1001 Web e-Loan 配合雪梨行授信戶TECO AUSTRALIA PTY
				// LTD原ID-TWZ0008288修改為AUZ0042888
				String errMsg = lmsBatchCommonService.doLmsBatch0042(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0042執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "43")) {
				// 小規增補合約 - L120S14 各Table補 cntrNo
				String errMsg = lmsBatchCommonService.doLmsBatch0043(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0043執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "45")) {
				// J-110-0209 更新QUOTAPPR RESCUEITEM_EL
				String errMsg = lmsBatchCommonService.doLmsBatch0045(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0045執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "46")) {
				// 整批重送KYC婉卻
				String errMsg = lmsBatchCommonService.doLmsBatch0046(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0046執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "47")) {
				// Web e-Loan配合紐行Oracle系統建置，修改AML相關功能
				String errMsg = lmsBatchCommonService.doLmsBatch0047(request);
				if (Util.notEquals(errMsg, "")) {
					result = WebBatchCode.RC_ERROR;
					result.element(WebBatchCode.P_RESPONSE,
							"LmsBatch0002ServiceImpl-doLmsBatch0047執行失敗！==>"
									+ errMsg);
					return result;
				}
			} else if (Util.equals(type, "48")) {
                // J-110-0272 配合授審處「本行授信覆審作業須知」修訂，修改E-Loan企金與個金授信管理系統之「授信覆審作業」，如以下修改內容。
                // 產生企金抽樣覆審名單
                String errMsg = lmsBatchCommonService.doLmsBatch0048(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0048執行失敗！==>"
                                    + errMsg);
                    return result;
                }
			} else if (Util.equals(type, "51")) {
                // J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
                // 將先行動用的動審表資料，寫入貸後管理追蹤事項檔，相關通知機制依現有貸後管理追蹤機制辦理
                String errMsg = lmsBatchCommonService.doLmsBatch0051(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0051執行失敗！==>"
                                    + errMsg);
                    return result;
                }
			} else if (Util.equals(type, "52")) {
                String errMsg = lmsBatchCommonService.doLmsBatch0052(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0052執行失敗！==>"
                                    + errMsg);
                    return result;
                }
			} else if (Util.equals(type, "53")) {
				Map<DocFile, C122M01A> docFileMetaMap = lmsBatchCommonService.doLmsBatch0053(request);
                String errMsg = lmsBatchCommonService.doLmsBatch0054(docFileMetaMap);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0053執行失敗！==>"
                                    + errMsg);
                    return result;
                }
            } else if (Util.equals(type, "55")) {
                // J-113-0143_07623_B1005 配合SBTi貸後名單通知營業單位更新
                String errMsg = lmsBatchCommonService.doLmsBatch0055(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0055執行失敗！==>"
                                    + errMsg);
                    return result;
                }
            } else if (Util.equals(type, "56")) {
                // J-113-0402_07623_B1001 依本行「企業授信ESG風險評級作業須知」第七條，為利追蹤ESG風險評級辦理頻率，
                // 新增貸後通知及報表(LLDLN350.LLWLN350)內容如下：
                //1.請每月於授信管理系統「貸後管理追蹤檢核表」，針對ESG風險評級即將屆期之案件進行通知(中、低ESG風險至少每五年辦理一次、
                // 高ESG風險至少每年辦理一次，例如中風險最近一次評等日期112.12.10，屆期日為117.12.10，貸後通知117.11月)，
                // 且於本行尚有有效額度者，發送額度最大分行，通知分行重新辦理評級。
                //2.追蹤事項通知內容提示「ESG風險評級即將屆期，請重新辦理ESG風險評級」。
                String errMsg = lmsBatchCommonService.doLmsBatch0056(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0056執行失敗！==>"
                                    + errMsg);
                    return result;
                }
            } else if (Util.equals(type, "57")) {
                // J-113-0490,007623 公司訪談紀錄表批次調閱,企金上傳CUSTID抓取全行貸後公司訪談紀錄表並將附件zip回傳
                String errMsg = lmsBatchCommonService.doLmsBatch0057(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0057執行失敗！==>"
                                    + errMsg);
                    return result;
                }
            } else if (Util.equals(type, "58")) {
            	// 用既有額度明細表資料重新產生建議審核層級
                String errMsg = lmsBatchCommonService.doLmsBatch0058(request);
                if (Util.notEquals(errMsg, "")) {
                    result = WebBatchCode.RC_ERROR;
                    result.element(WebBatchCode.P_RESPONSE,
                            "LmsBatch0002ServiceImpl-doLmsBatch0058執行失敗！==>"
                                    + errMsg);
                    return result;
                }
            } else {
				result = WebBatchCode.RC_ERROR;
				result.element(WebBatchCode.P_RESPONSE,
						"LmsBatch0002ServiceImpl執行失敗！==>" + "傳入參數 type=["
								+ type + "] 無可對應執行功能!!");
			}

			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0002ServiceImpl執行成功！");

		} catch (Exception ex) {
			ex.printStackTrace();
			result = WebBatchCode.RC_ERROR;
			result.element(
					WebBatchCode.P_RESPONSE,
					"LmsBatch0002ServiceImpl執行失敗！==>"
							+ ex.getLocalizedMessage());

		}

		return result;
	}

}
