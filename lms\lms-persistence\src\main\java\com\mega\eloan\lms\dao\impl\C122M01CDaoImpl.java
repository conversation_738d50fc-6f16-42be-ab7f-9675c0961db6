package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C122M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122M01C;


@Repository
public class C122M01CDaoImpl extends LMSJpaDao<C122M01C, String>
	implements C122M01CDao {

	@Override
	public C122M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C122M01C findByMainIdSeq(String mainId, int seq){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C122M01C> findByMainIdOrderBySeqAsc(String mainId){
		return findByMainIdOrderBySeqAssign(mainId, "asc");
	}
	
	@Override
	public List<C122M01C> findByMainIdOrderBySeqDesc(String mainId){
		return findByMainIdOrderBySeqAssign(mainId, "desc");
	}
	
	private List<C122M01C> findByMainIdOrderBySeqAssign(String mainId, String ordStr){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		if(Util.equals(ordStr, "asc")){
			search.addOrderBy("seq", false);
		}else if(Util.equals(ordStr, "desc")){
			search.addOrderBy("seq", true);
		}
		List<C122M01C> list = createQuery(search).getResultList();
		return list;
	}
}