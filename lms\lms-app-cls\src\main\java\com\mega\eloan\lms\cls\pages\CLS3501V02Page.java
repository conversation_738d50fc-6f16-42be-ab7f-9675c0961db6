
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 中小信保整批申請 - 待覆核
 * </pre>
 */
@Controller
@RequestMapping("/cls/cls3501v02")
public class CLS3501V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_待覆核);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.FCheck, LmsButtonEnum.View);
		renderJsI18N(CLS3501M01Page.class);
		renderJsI18N(CLS3501V01Page.class);

		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS3501V01Page');");
	}

}
