<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
		 <script type="text/javascript">loadScript('pagejs/ctr/LMS9990Common');</script>
        <script type="text/javascript">loadScript('pagejs/ctr/LMS9990M09Page');</script>
			<div class="button-menu funcContainer" id="buttonPanel">	
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}"><!--儲存--></th:block>
				</button>
				<button type="button" id="btnPrint">
					<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>
				<button id="btnQuery"  class="forview">
                	<span class="ui-icon ui-icon-jcs-102"></span>
					<th:block th:text="#{'button.queryByRate'}"><!--查詢利率--></th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}"><!--離開--></th:block>
				</button>
				
            </div>
			<form id="ActionMForm">
				<div id="showTitle">
					    <div class=" tit2 color-black" >
						<th:block th:text="#{'L999M01AM09.showTitle01'}"><!--種類--></th:block>：<span name="contractType" id="contractType" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM09.showTitle02'}"><!--客戶名稱--></th:block>：<span name="custData" id="custData" class="color-blue"></span>
					    <br/>
						<th:block th:text="#{'L999M01AM09.showTitle03'}"><!--約定書字號--></th:block>：
						<input name="contractWord" id="contractWord" type="text" size="15" maxlength="30"/>
						<th:block th:text="#{'L999M01AM09.showTitle0301'}"><!--字第 --></th:block>
						<input name="contractNo" id="contractNo" type="text" size="20" class="alphanum" maxlength="20"/>
						<th:block th:text="#{'L999M01AM09.showTitle0302'}"><!--號 --></th:block>
					    <br/>
						<th:block th:text="#{'L999M01AM09.showTitle07'}"><!--帳號--></th:block>：
						<input name="accNo" id="accNo" type="text" size="19" maxlength="32" maxlengthC="10" />
						<br/>
						<!--
						<th:block th:text="#{'L999M01AM09.showTitle04'}">立約人</th:block>：<span name="custData" id="custData" class="color-blue"></span>
					    <br/>
						-->
						<th:block th:text="#{'L999M01AM09.showTitle06'}"><!--到期日--></th:block>：
						<th:block th:text="#{'L999M01ASCOMMON.yearType'}"><!--民國--></th:block>
						<input type="text" name="dueSDateY" id="dueSDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                        <th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
						<input type="text" name="dueSDateM" id="dueSDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                        <th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
						<input type="text" name="dueSDateD" id="dueSDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                        <th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>&nbsp;
						<br/>			
						<th:block th:text="#{'L999M01AM09.showTitle05'}"><!--幣別/金額--></th:block>：
						<input name="coverLoanCurr" id="coverLoanCurr" type="text" size="10" maxlength="30" maxlengthC="10" />
						<input name="coverLoanAmt" id="coverLoanAmt" type="text" size="19" maxlength="60" maxlengthC="20" />
						<th:block th:text="#{'L999M01ASCOMMON.unit'}"><!--元 --></th:block>
					    </div>
				</div>
			</form>
			<div class="tabs doc-tabs">
                <ul>
                	<li id="tabs_1" > <a href="#tab-01" goto="01"><b><th:block th:text="#{'L999M01AM09.title01'}"><!--  一般條款--></th:block></b></a></li>
                	<li id="tabs_2" > <a href="#tab-02" goto="02"><b><th:block th:text="#{'L999M01AM09.title02'}"><!--  其他約定事項--></th:block></b></a></li>
                	<li id="tabs_3" > <a href="#tab-03" goto="03"><b><th:block th:text="#{'L999M01AM09.title03'}"><!--  連保人清單--></th:block></b></a></li>
                </ul>
                <div class="tabCtx-warp">
                	<form id="ActionSForm" name="ActionSForm" >
                		<div id="tabs-00" th:id="${tabID}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
					</form>
				</div>
			</div>
		</th:block>
    </body>
</html>
