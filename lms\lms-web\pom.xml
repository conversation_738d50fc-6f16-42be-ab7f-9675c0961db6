<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lms</artifactId>
		<groupId>com.mega.eloan</groupId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>lms-web</artifactId>
	<packaging>war</packaging>

	<dependencies>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-config</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-persistence</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-cls</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-las</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-fms</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-lms</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-lns</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-lrs</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-crs</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>		
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-ctr</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-app-rpt</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<!-- 
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-batch</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency> 
		-->
	</dependencies>
	<build>
		<plugins>
			<!-- <plugin>
				<groupId>org.mortbay.jetty</groupId>
				<artifactId>maven-jetty-plugin</artifactId>
				<version>6.1.26</version>
			</plugin> -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.2</version>
			</plugin>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.44.v20210927</version>
                <configuration>
                    <httpConnector>
                        <port>8090</port>
                    </httpConnector>
                    <webApp>
                        <contextPath>/lms-web</contextPath>
                        <jettyEnvXml>${project.basedir}/src/main/webapp/WEB-INF/jetty-env.xml</jettyEnvXml>
                        <!-- <extraClasspath>${project.basedir}/../ces-config/src/main/resources</extraClasspath> -->
                    </webApp>
                    <scanIntervalSeconds>0</scanIntervalSeconds>
                    <reload>manual</reload>
                </configuration>
            </plugin>
		</plugins>
	</build>
	<profiles>
        <profile>
            <id>css</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>nl.geodienstencentrum.maven</groupId>
                        <artifactId>sass-maven-plugin</artifactId>
                        <version>3.7.2</version>
                        <executions>
                            <execution>
                                <phase>compile</phase>
                                <goals>
                                    <goal>update-stylesheets</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <sassOptions>
                                <always_update>true</always_update>
                                <style>:compressed</style>
                                <debug_info>false</debug_info>
                            </sassOptions>
                            <resources>
                                <resource>
                                    <source>
                                        <directory>${basedir}/src/WebDev/compass/sass</directory>
                                        <includes>
                                            <include>*.scss</include>
                                        </includes>
                                        <excludes />
                                    </source>
                                    <destination>${basedir}/src/main/webapp/css</destination>
                                </resource>
                            </resources>
                            <useCompass>false</useCompass>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- mvn -Preq exec:exec@merge exec:exec@uglifyjs -->
            <id>req</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>merge</id>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                	<!-- Nashorn, Java 8+'s JavaScript engine -->
                                    <executable>${java.home}\..\bin\jjs.exe</executable>
                                    <!-- <workingDirectory>${basedir}/src/main/webapp/js</workingDirectory> -->
                                    <!-- <executable>node</executable> --><!-- 改用 Node.js -->
                                    <arguments>
                                        <argument>-scripting</argument> <!-- 這個參數是 Nashorn 獨有，Node.js 不需要它 -->
                                        <argument>${basedir}/src/WebDev/requirejs/2.3.6/r.js</argument>
                                        <argument>--</argument>
                                        <argument>-o</argument>
                                        <argument>${basedir}/src/WebDev/build.js</argument>
                                        <argument>name=js/main</argument>
                                        <argument>out=${basedir}/src/main/webapp/js/build-main.js</argument>
                                        <argument>optimize=none</argument> <!-- r.js 使用 uglifyjs2，不支援 ES6 語法處理 -->
                                        <argument>baseUrl=${basedir}/src/main/webapp</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>uglifyjs</id>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <workingDirectory>${basedir}/src/main/webapp/js</workingDirectory>
                                    <executable>uglifyjs</executable> <!-- npm install uglify-js -g -->
                                    <arguments>
                                        <argument>-c</argument>
                                        <argument>--source-map</argument>
                                        <argument>"url='build-main.min.js.map'"</argument>
                                        <argument>-o</argument>
                                        <argument>build-main.min.js</argument>
                                        <argument>-m</argument>
                                        <argument>--</argument>
                                        <argument>build-main.js</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>	
</project>