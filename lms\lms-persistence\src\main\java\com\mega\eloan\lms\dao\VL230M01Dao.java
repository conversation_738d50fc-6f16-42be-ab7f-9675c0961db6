/* 
 * VL230M01Dao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.VL230M01;


/**
 * <pre>
 * 簽約未動用取得簽報書內容 view
 * </pre>
 * 
 * @since 2012/7/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/23,REX,new
 *          </ul>
 */
public interface VL230M01Dao extends IGenericDao<VL230M01> {
	List<VL230M01> findByCustIdDupId(String custId,String DupNo);
}