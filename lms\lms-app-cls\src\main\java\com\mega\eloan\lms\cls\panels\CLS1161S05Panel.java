/* 
 * CLS1161S04Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 動用審核表 - 先行動用呈核及控制表
 * </pre>
 * 
 * @since 2012/12/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/25,Fantasy,new
 *          </ul>
 */
public class CLS1161S05Panel extends Panel {

	public CLS1161S05Panel(String id) {
		super(id);
	}

	public CLS1161S05Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
