// jqGrid 設定已在 common.jqgrid.js 中處理，無需額外設定 regional

var CLS1161S02Action = {
    handler: 'cls1161formhandler',
    gridhandler: 'cls1161gridhandler',
    ready: false,
    C160S01AGrid: null, // 擔保品資料表
    C160S01BGrid: null, // 主從債務人資料表
    C160S01CGrid: null, // 個金產品種類資料表
    C160S01FGrid: null, // 代償轉貸借新還舊明細資料表
    lnf030Grid: null, // 放款帳號資料表
    BorrowersGrid: null, // 借款人資料表
    BorrowersId: null,
    AccountGrid: null, // 帳號資料表
    jcicGrid: null, // 聯徵結果
    C160S01COid: null, // 產品資訊抄寫OID
    L140S02CFlag: null, // 授信期間算頭不算尾flag
    calcLnEndDateFlag: null, // 授信期間算頭不算尾flag
    L140S02AAMTFlag: null, 
    L140S02A_RMRCTAMT: null, 
    data: {},
    recordTabClick: [], // 記錄是否按過頁籤
    L140S02F_FavChgCase: "",//原 房貸資訊取得轉貸前後為相同性質之政策性貸款
    /**
     * 初始化
     */
    init: function(data){
        // set data
        CLS1161S02Action.data = $.extend({}, data || {});
        // set refmainId
        responseJSON['refmainId'] = CLS1161S02Action.data.refmainId;
        CLS1161S02Action.recordTabClick = [];
        // default tab
        $('#C160M01BTab').tabs({
            selected: 0
        });
        
    },
    /**
     * 建置
     */
    build: function(){
        console.log('開始 build 函數');
        
        // 檢查必要的依賴是否已載入
        if (typeof $ === 'undefined' || typeof $.jgrid === 'undefined') {
            console.error('jQuery 或 jqGrid 尚未載入完成');
            setTimeout(CLS1161S02Action.build, 100);
            return false;
        }
        
        // 重要：修復 getRegional 'p' 屬性錯誤 - 確保 jqGrid 完整初始化
        try {
            // 檢查並初始化 $.jgrid.regional 結構
            if (typeof $.jgrid.regional === 'undefined') {
                $.jgrid.regional = {};
            }
            
            // 設定中文區域配置（避免 getRegional 錯誤）
            if (!$.jgrid.regional['zh-TW']) {
                $.jgrid.regional['zh-TW'] = {
                    defaults: {
                        recordtext: "第 {0} - {1} 筆，共 {2} 筆",
                        emptyrecords: "無資料",
                        pgtext: "第 {0} 頁，共 {1} 頁",
                        loadtext: "讀取中...",
                        savetext: "儲存中...",
                        bSubmit: "送出",
                        bCancel: "取消",
                        bClose: "關閉",
                        saveData: "資料已變更！要儲存嗎？",
                        bYes: "是",
                        bNo: "否",
                        bExit: "離開"
                    }
                };
            }
            
            // 確保 defaults 設定存在並與區域設定同步
            if (typeof $.jgrid.defaults === 'undefined') {
                $.jgrid.defaults = {};
            }
            
            // 合併區域設定到全域 defaults（這是關鍵步驟）
            $.extend($.jgrid.defaults, $.jgrid.regional['zh-TW'].defaults);
            
            console.log('jqGrid 區域設定初始化完成，避免 getRegional 錯誤');
            
        } catch (e) {
            console.error('jqGrid 初始化失敗:', e);
            // 即使初始化失敗也繼續執行，使用預設設定
            if (typeof $.jgrid.defaults === 'undefined') {
                $.jgrid.defaults = {
                    recordtext: "第 {0} - {1} 筆，共 {2} 筆",
                    emptyrecords: "無資料",
                    pgtext: "第 {0} 頁，共 {1} 頁",
                    loadtext: "讀取中..."
                };
            }
        }
        
        var $div = $('#C160M01BTab');
        console.log('找到 C160M01BTab:', $div.length);
        
        if ($div.length === 0) {
            console.error('找不到 C160M01BTab 容器，延後初始化');
            setTimeout(CLS1161S02Action.build, 100);
            return false;
        }
        
        var $gridElement = $div.find('#C160S01AGrid');
        console.log('找到 C160S01AGrid 元素:', $gridElement.length);
        
        if ($gridElement.length === 0) {
            console.error('找不到 C160S01AGrid 元素，跳過此 Grid');
        } else {
            // 擔保品資料表
            console.log('開始初始化 C160S01AGrid...');
            try {
                // 額外防護：確保 iGrid 初始化時 jqGrid 有正確的區域設定
                if (typeof $.fn.jqGrid !== 'undefined') {
                    // 設定預設的區域標識符，避免 getRegional 訪問 undefined.p
                    $.jgrid.regional = $.jgrid.regional || {};
                    if (!$.jgrid.regional['zh-TW']) {
                        $.jgrid.regional['zh-TW'] = { defaults: $.jgrid.defaults };
                    }
                }
                
                CLS1161S02Action.C160S01AGrid = $gridElement.iGrid({
                    localFirst: true,
                    handler: CLS1161S02Action.gridhandler,
                    height: 250,
                    action: 'C160S01AQuery',
                    rowNum: 15,
                    rownumbers: true,
            colModel: [{
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                name: 'unitChg',
                hidden: true
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.collNo"], // 擔保品編號
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'collNo',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    CLS1161S02Action.openC160S01A(rowObject);
                }
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.collTyp1"], // 擔保品大類
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'collTyp1' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.docStatus"], // 擔保品文件狀態
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'docStatus' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.loanTwd"], // 放款值（TWD）
                align: "right",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                formatter: GridFormatter.number['addComma'],
                name: 'loanTwd' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.cmsDesc"], // 擔保品內容
                align: "left",
                width: 300, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'cmsDesc' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01AGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01A(data);
            }
        });
            console.log('C160S01AGrid 初始化成功');
            } catch (e) {
                console.error('C160S01AGrid 初始化失敗:', e);
                console.error('跳過此 Grid，繼續初始化其他元件...');
            }
        }
        
        // 主從債務人資料表
        CLS1161S02Action.C160S01BGrid = $div.find('#C160S01BGrid').iGrid({
            localFirst: true,
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 250, // 設定高度
            action: 'C160S01BQuery', // 執行的Method
            rowNum: 15,
            rownumbers: true,
            divWidth: 0,
            colModel: [{
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: i18n.cls1161s02a["C160S01B.rId"], // 從債務人統編
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'rId',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    CLS1161S02Action.openC160S01B(rowObject);
                }
            }, {
                colHeader: i18n.cls1161s02a["C160S01B.rDupNo"], // 重覆碼
                align: "left",
                width: 50, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'rDupNo' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01B.rName"], // 從債務人名稱
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'rName' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01B.rKindD"], // 關係類別細項
                align: "left",
                width: 100, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'rKindDStr'
            }, {
                colHeader: i18n.cls1161s02["C160S01B.rType"], // 相關身分
                align: "center",
                width: 60, // 設定寬度
                sortable: false, // 是否允許排序,
                name: 'rType'
            }, {
                colHeader: i18n.cls1161s02["C160S01B.rCountry"], // 國別
                align: "center",
                width: 60, // 設定寬度
                sortable: false, // 是否允許排序,
                name: 'rCountry'
            }, {
            	colHeader: i18n.cls1161s02["C160S01B.guaPercentStr"],//保證人負担保證責任比率
                name: 'guaPercentStr',
                align: "right",
                width: 20,
                sortable: false
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01BGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01B(data);
            }
        });
        
        // 個金產品種類資料表
        CLS1161S02Action.C160S01CGrid = $div.find('#C160S01CGrid').iGrid({
            localFirst: true,
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 250, // 設定高度
            action: 'C160S01CQuery', // 執行的Method
            rowNum: 15,
            rownumbers: true,
            divWidth: 0,
            colModel: [{
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                name: 'mainId',
                hidden: true
                // 是否隱藏
            }, {
                name: 'refmainId',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: i18n.cls1161s02a["C160S01C.seq"], // 序號
                align: "right",
                width: 50, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'seq' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01C.prodKind"], // 產品種類
                align: "left",
                width: 150, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'prodKind',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    CLS1161S02Action.openC160S01C(rowObject);
                }
            }, {
                colHeader: i18n.cls1161s02a["C160S01C.subjCode"], // 科目
                align: "left",
                width: 150, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'subjCode' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01C.loanAmt"], // 動撥金額  (本因用loanAmt但因為已有一堆舊案改了approvedAmt所以只好延用)
                align: "right",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                formatter: GridFormatter.number.addComma,
                name: 'approvedAmt'
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01CGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01C(data);
            }
        });
     	// 聯徵查詢結果
        CLS1161S02Action.jcicGrid = $div.find('#jcicGrid').iGrid({
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 180, // 設定高度
            action: 'queryJcicResultCompare', // 執行的Method
            divWidth: 0,
            colModel: [{
                colHeader: i18n.cls1161s02a["C160M01B.worse"], // 資信轉差
                align: "left",
                width: 20,
                sortable: false,
                name: 'worse'
            }, {
                colHeader: i18n.cls1161s02a["C160M01B.ejcicItem"], // 比較聯徵項目
                align: "left",
                width: 200,
                sortable: false,
                name: 'ejcicItem'
            }, {
                colHeader: i18n.cls1161s02a["C160S02A.reportFile"], // 報表檔案
                align: "center",
                width: 20,
                sortable: false,
				formatter: 'click',
                name: 'openFile',
				onclick: CLS1161S02Action.gridEjcicQueryResult
            }, {
                colHeader: i18n.cls1161s02a["C160M01B.ejcicContentC120"], // 簽報書聯徵結果
                align: "left",
                width: 50,
                sortable: false,
                name: 'ejcicContentC120'
			}, {
                colHeader: i18n.cls1161s02a["C160M01B.ejcicContentC160"], // 動審表聯徵結果
                align: "left",
                width: 50,
                sortable: false,
                name: 'ejcicContentC160'
            }, {
                colHeader: i18n.cls1161s02a["C160M01B.dataDateC120"], // 查詢日期
                align: "left",
                width: 50,
                sortable: false,
                name: 'dataDateC120'
            }, {
                colHeader: i18n.cls1161s02a["C160M01B.dataDateC160"], // 查詢日期
                align: "left",
                width: 50,
                sortable: false,
                name: 'dataDateC160'
            },{name: 'dataSrcMemo',hidden: true}
			 ,{name: 'dataType',hidden: true}
			 ,{name: 'dataStatus',hidden: true}
			 ,{name: 'oid',hidden: true}
			]
        });
        // build button
        // 黑名單
        $div.find('#btBlackQuery').click(function(){
        	API.showMessage("為與AML系統整合，請改至【AML/CFT】頁籤查詢，查詢結果將帶回本欄"+"<br/>"
        			+"若在查詢後未顯示資料，請於【AML/CFT】頁籤按 Ctrl+F5 後，再重新查詢。");
        	/*
            $('#blackDataDate').html('');
            $('#blackDesc').html('');
            
            $.ajax({
                handler: CLS1161S02Action.handler,
                action: 'proc_l120s09a',
                formId: '',
                data: {
                },
                success: function(json){
                	$('#blackDataDate').html(json.blackDataDate);
                    $('#blackDesc').html(json.blackDesc);
                    //不需要, 已在 server端儲存, 只是更新前端呈現的值
                    //CLS1161S02Action.saveDetial('C160M01BForm');
                }
            });*/            
            
            /*
             * BlackNameAction.query($.extend($('#C160M01BForm').serializeData(), {
             * callback: function(response){
             * $('#blackDesc').html(response.message);
             * CLS1161S02Action.saveDetial('C160M01BForm'); //save } }));
             */
            /*
             * $.ajax({ handler: CLS1161S02Action.handler, action:
             * 'queryBlackName', formId: 'C160M01BForm', data :
             * CLS1161S02Action.data, success: function(response){
             * MegaApi.showPopMessage(i18n.def['confirmTitle'],
             * i18n.def['runSuccess']); if (response.message)
             * $('#blackDesc').html(response.message); if
             * (response.blackDataDate)
             * $('#blackDataDate').html(response.blackDataDate); } });
             */
        }) // 共同行銷
.end().find('#btJoinMarkQuery').click(function(){
            $('#joinMarketingDate').html('');
            $('#joinMarkDesc').html('');
            $.ajax({
                handler: CLS1161S02Action.handler,
                action: 'queryJoinMark2',
                formId: 'empty',
                data: {
                    cust: JSON.stringify(CLS1161S02Action.queryCustList() || {})
                },
                success: function(response){
                    $('#joinMarketingDate').html(util.getToday());
                    $('#joinMarkDesc').html(DOMPurify.sanitize(response.message));
                    CLS1161S02Action.saveDetial('C160M01BForm'); // save
                }
            });
            
            /*
             * $.ajax({ handler: CLS1161S02Action.handler, action:
             * 'queryJoinMark', formId: 'C160M01BForm', data: {},
             * success: function(response){
             * MegaApi.showPopMessage(i18n.def['confirmTitle'],
             * i18n.def['runSuccess']); if (response.message)
             * $('#joinMarkDesc').html(response.message); } });
             */
		}) // 聯徵查詢結果
.end().find('#btjcicRefresh').click(function(){
            $.ajax({
                handler: CLS1161S02Action.handler,
                action: 'recordEjcicResultData',
                formId: 'empty',
                data: CLS1161S02Action.data,
                success: function(response){
					CLS1161S02Action.jcicGrid.trigger("reloadGrid");
					if (response.msg) {
						return CommonAPI.showMessage(response.msg);
					}
                }
            });
        }) // 擔保品-編輯
.end().find('#btEdit02').click(function(){
            var data = CLS1161S02Action.C160S01AGrid.getSingleData();
            if (data) 
                CLS1161S02Action.openC160S01A(data);
        }) // 主從債務人-重新引進
.end().find('#btPullinAgain03').click(function(){
            MegaApi.confirmMessage(i18n.def['confirmRun'], function(action){
                if (action) {
                    $.ajax({
                        handler: CLS1161S02Action.handler,
                        action: 'importC160S01B',
                        formId: 'C160S01BForm',
                        data: {},
                        success: function(response){
                            MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);
                            CLS1161S02Action.reload('C160S01BForm'); // reload
                            // grid
                        }
                    });
                }
            });
        }) // 主從債務人-新增
.end().find('#btAdd03').click(function(){
            CLS1161S02Action.openC160S01B({});
        }) // 主從債務人-編輯
.end().find('#btEdit03').click(function(){
            var data = CLS1161S02Action.C160S01BGrid.getSingleData();
            if (data) 
                CLS1161S02Action.openC160S01B(data);
        }) // 主從債務人-刪除
.end().find('#btDelete03').click(function(){
            var data = CLS1161S02Action.C160S01BGrid.getSingleData();
            if (data) 
                CLS1161S02Action.deleteDetial('C160S01BForm', data);
        }) // 產品種類-複抄
.end().find('#btCopy04').click(function(){
            var data = CLS1161S02Action.C160S01CGrid.getSingleData();
            if (data) {
                CLS1161S02Action.openCopyC160S01C(data);
                // CLS1161S02Action.copyDetial('C160S01CForm', data);
            }
        }) // 產品種類-編輯
.end().find('#btEdit04').click(function(){
            var data = CLS1161S02Action.C160S01CGrid.getSingleData();
            if (data) 
                CLS1161S02Action.openC160S01C(data);
        }) // 產品種類-刪除
.end().find('#btChangeAmt').click(function(){
		$.ajax({
				handler: "cls1161formhandler",
                action: "queryC160M01BAmt",
				data: {
                   loanTotAmt: $("#new_loanTotAmt").val()
               		},
                success: function(obj){
					$("#bfLoanTotAmt").val(obj.bfLoanTotAmt);
					
                    $("#isChangeAmt").thickbox({
                        title: i18n.cls1161s02a['C160M01B.ChangeLoanTotAmt'],//'調整核准額度',
                        width: 350,
                        height: 200,
                        align: 'left',
                        valign: 'top',
                        modal: false,
                        modal: false,
                        open: function(){
                        
                        },
                        buttons: API.createJSON([{
                            key: i18n.def['sure'],
                            value: function(){
                            
                                $.ajax({
                                    handler: "cls1161formhandler",
                                    action: 'changeC160M01BAmt',
                                    data: {
                                        loanTotAmt: $("#new_loanTotAmt").val()
                                    },
                                    success: function(responseData){
                                        $("#s_loanTotAmt").val( responseData.s_loanTotAmt );
										$("#loanTotal").val( responseData.loanTotal );
										$("#loanCount").val( responseData.loanCount );
                                        $.thickbox.close();                            
                                    }
                                });
                                
                            }
                        }, {
                            key: i18n.def['cancel'],
                            value: function(){
                                //$("#isChangeAmt").unwrap(wrapForm);
                                $.thickbox.close();
                            }
                        }])
                    });
                    
                    
                }
            })
 
        })
.end().find('#btDelete04').click(function(){
            var data = CLS1161S02Action.C160S01CGrid.getSingleData();
            if (data) 
                CLS1161S02Action.deleteDetial('C160S01CForm', data);
        });
        
        // set tabs
        $div.tabs({
            select: function(event, ui){
                var id = ui.panel.id || '';
                var value = CLS1161S02Action.recordTabClick[id];
                if (!value) { // grid reload
                    CLS1161S02Action.recordTabClick[id] = true;
                    switch (id) {
                        case 'C160M01BTab02':
                            CLS1161S02Action.C160S01AGrid.reload();
                            break;
                        case 'C160M01BTab03':
                            CLS1161S02Action.C160S01BGrid.reload();
                            break;
                        case 'C160M01BTab04':
                            CLS1161S02Action.C160S01CGrid.reload();
                            break;
                    }
                }
            }
        });
        
        // 借保人
        var $C160S01BDiv = $('#C160S01BThickBox');
        // 從債務人引進
        $C160S01BDiv.find('#rIdPullin').click(function(){
            CommonAPI.openQueryBox({
                defaultValue: $('#C160S01BForm').find('#rId').val(),// 指定時會自動查詢
                defaultCustType: '1', // 2.英文名
                divId: 'C160S01BForm', // 在哪個div 底下
                autoResponse: { // 是否自動回填資訊
                    id: 'rId', // 統一編號欄位ID
                    dupno: 'rDupNo', // 重覆編號欄位ID
                    name: 'rName' // 客戶名稱欄位ID
                }
            });
        }) // 關係類別
.end().find('#rKindM').change(function(){
            var value = $(this).val() + '';
            var $form = $('#C160S01BForm');
            $form.find('.rKindD').hide().val('');
            switch (value) {
                case '1':
                case '2':
                    var $obj = $form.find('#rKindD' + value);
                    $obj.val($form.find('#rKindD').val()).show();
                    break;
                case '3':
                    var s = ($form.find('#rKindD').val() || '').split('');
                    $form.find('#rKindD31').val(s.length >= 2 ? s[0] : '').show();
                    $form.find('#rKindD32').val(s.length >= 2 ? s[1] : '').show();
                    break;
            }
        }) // 細項
.end().find('#rKindD1,#rKindD2').change(function(){
            $('#rKindD').val($(this).val());
        }).end().find('#rKindD31,#rKindD32').change(function(){
            $('#rKindD').val($('#rKindD31').val() + $('#rKindD32').val());
        }) // 相關身份
.end().find('#rType').change(function(){
            $('#lms1605s03_rType').hide();
            if ($(this).val() == 'N') 
                $('#lms1605s03_rType').show();
        }) // 借保原因
.end().find('#reson').change(function(){
            $('#resonOther').hide();
            if ($(this).val() == '99') 
                $('#resonOther').show();
        });
        
        // 代償轉貸借新還舊明細資料表
        var $C160S01CDiv = $('#C160S01CThickBox');
        CLS1161S02Action.C160S01FGrid = $C160S01CDiv.find('#C160S01FGrid').iGrid({
            localFirst: true,
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 150, // 設定高度
            action: 'C160S01FQuery', // 執行的Method
            rowNum: 15,
            rownumbers: true,
            divWidth: 0,
            colModel: [{
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                name: 'bankNo',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: i18n.cls1161s02a["C160S01F.bankName"], // 行庫名稱
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'bankName',
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                    CLS1161S02Action.openC160S01F(rowObject);
                }
            }, {
                colHeader: i18n.cls1161s02a["C160S01F.branchName"], // 分行名稱
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'branchName' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01F.subAmt"], // 代償金額
                align: "left",
                width: 100, // 設定寬度
                sortable: true, // 是否允許排序n,
                name: 'subAmt' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01FGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01F(data);
            }
        });
        
        // 新增-代償轉貸借新還舊明細
        $C160S01CDiv.find('#C160S01F_add').click(function(){
            CLS1161S02Action.openC160S01F({});
        }) // 刪除-代償轉貸借新還舊明細
.end().find('#C160S01F_delete').click(function(){
            var data = CLS1161S02Action.C160S01FGrid.getSingleData();
            if (data) 
                CLS1161S02Action.deleteDetial('C160S01FForm', data)
        }) // 授信期間
.end().find('#lnStartDate').change(function(){
            $('#C160S01CForm').find('#lnEndDate').val(CLS1161S02Action.getLnEndDate());
            
            if($('#C160S01CForm').find('#prodKind').val()=="67" 
            	|| $('#C160S01CForm').find('#prodKind').val()=="68"
            	|| $('#C160S01CForm').find('#prodKind').val()=="70"){
            	$('#C160S01CForm').find('#useStartDate').val($('#C160S01CForm').find('#lnStartDate').val());
            	$('#C160S01CForm').find('#useEndDate').val($('#C160S01CForm').find('#lnEndDate').val());
            }
        })        /*
         * .end().find('#lnSelect').change(function(){ var $form =
         * $('#C160S01CForm'); $form.find('.lnSelect').hide(); switch
         * ($(this).val()+''){ case '1':
         * $form.find('#lnSelectSpan1').show(); break; case '2':case
         * '3':case '4': $form.find('#lnSelectSpan2').show(); break;
         * case '5': $form.find('#lnOther').show(); break; } })
         */
        // 動用期間
        /*
         * .end().find('#useLine').change(function(){ var $form =
         * $('#C160S01CForm'); $form.find('.useLine').hide(); switch
         * ($(this).val()+''){ case '1':
         * $form.find('#useLineSpan1').show(); break; case '2':case
         * '3':case '4': $form.find('#useLineSpan2').show(); break; case
         * '5': $form.find('#useOther').show(); break; } })
         */
        // 引進行銷分行
        .end().find('#efctBHPullin').click(function(){
            CommonAPI.showAllBranch({
                btnAction: function(a, b){
                    $.thickbox.close();
                    $('#efctBH').html(b.brNo);
                    $('#efctBHName').html(b.brName);
                }
            });
        }) // 是否收取手續費
.end().find('input[name=chargeFlag]').click(function(){
            $('#spanChargeAmt').hide();
            if ($(this).val() == 'Y') 
                $('#spanChargeAmt').show();
        }) // 扣款帳號引進
.end().find('#atpayNoPullin').click(function(){
            CLS1161S02Action.BorrowersId = 'atpayNo';
            CLS1161S02Action.openBorrowers({
                rType: ''
            });
        }) // 存款帳號/進帳帳號引進
.end().find('#accNoPullin').click(function(){
            CLS1161S02Action.BorrowersId = 'accNo';
            CLS1161S02Action.openBorrowers({
                rType: 'C'
            });
        }) // 扣帳方式
.end().find('#payType').change(function(){
            //$('.ach').hide();
			$('#achBranchNo,#achBranchNm,#achAccount').readOnly(true);
			$('#achBankNoPullin').hide();
            if ($(this).val() == '01') {
            	//$('.ach').show();
            	if(!thickboxOptions.lockDoc){
            		$('#achBranchNo,#achBranchNm,#achAccount').readOnly(false);
                    $('#achBankNoPullin').show();
                    
                    //儲存成功以後會重新走這段，eDDA案件鎖定ACH相關欄位也要在這邊重新判斷一次
                    if( $('#ploanIsNeedACH').val() == 'Y' ){//進ploanIsNeedACH=Y為eDDA案件，要鎖定ACH相關欄位
                		$('#payType,#achBranchNo,#achBranchNm,#achAccount').readOnly(true);
                    	$('#achBankNoPullin').hide();
                	}
                    $('#atpayNo').val('');
                    $('#atpayNo').readOnly(true);
                    $('#atpayNoPullin').hide();
            	}
            } else{
            	if(!thickboxOptions.lockDoc){
            		$('#achBankNo,#achBankNm,#achBranchNo,#achBranchNm,#achAccount').val('');
            		$('#atpayNo').readOnly(false);
            		$('#atpayNoPullin').show();
            	}
            }
                
        }) // ACH-引進銀行,分行
.end().find('#achBankNoPullin').click(function(){
            QueryBranch.open({
                // removeKey : [ '03', '06', '99' ],//
                // 刪除不需要的選項
                fn: function(data){
                    var $form = $('#C160S01CForm');
                    $form.find('#achBankNo').val(data.bankCode || '');
                    $form.find('#achBankNm').val(data.bankCodeCn || '');
                    $form.find('#achBranchNo').val((data.bankCode == '017' ? data.bankCode +
                    data.branchCode : data.branchCode) ||
                    '');
                    $form.find('#achBranchNm').val(data.branchName || '');
                }
            });
		}) // 撥款他行-引進銀行,分行
.end().find('#appOtherBankNoPullin').click(function(){
            QueryBranch.open({
                // removeKey : [ '03', '06', '99' ],//
                // 刪除不需要的選項
                fn: function(data){
                    var $form = $('#C160S01CForm');
                    $form.find('#appOtherBankNo').val(data.bankCode || '');
                    $form.find('#appOtherBankNm').val(data.bankCodeCn || '');
                    $form.find('#appOtherBranchNo').val((data.bankCode == '017' ? data.bankCode +
                    data.branchCode : data.branchCode) ||
                    '');
                    $form.find('#appOtherBranchNm').val(data.branchName || '');
                }
            });
        }) // 自動進帳
.end().find('input[name=autoRct]').click(function(){
            var $form = $('#C160S01CForm');
            $form.find('#autoRctTr').hide();
            if ($(this).val() == 'Y') {
                $form.find('#autoRctTr').show();
                
                //當 自動進帳   由N改Y
                if( $form.find('#rctAMT').val()=="" && $form.find('#rctDate').val()=="" ){
                	if ($form.find('#prodKind').val() === '69') {
                		$form.find('#rctAMT').val( $form.find('#approvedAmt').val() );	
                	}                	
                }
            }
            else {
                $form.find('#rctAMT').val('');
                $form.find('#rctDate').val('');
            }
		}) //契約書種類
.end().find('input[name=ctrType]').click(function(){
			var $form = $('#C160S01CForm');
			$form.find('div.section_penaltyMaxContTm').show();
			
			$form.find('#dRateAdd').removeAttr('readonly');
			$form.find('#penaltyMaxContTm').removeAttr('readonly');
			$form.find('#dMonth1').removeAttr('readonly');
			$form.find('#dRate1').removeAttr('readonly');
			$form.find('#dMonth2').removeAttr('readonly');
			$form.find('#dRate2').removeAttr('readonly');
			var val = $(this).val();
			
			if (val == '1' || val == '2' || val == '3' || val == '4') { // J-108-0195 
				$form.find('#dRateAdd').val("0");
				$form.find('#dRateAdd').attr('readonly', 'readonly');
				if(true){
					$form.find('#penaltyMaxContTm').val("9");	
					$form.find('#penaltyMaxContTm').attr('readonly', 'readonly');
				}	
				$form.find('#dMonth1').val("6");	
				$form.find('#dMonth1').attr('readonly', 'readonly');
				$form.find('#dRate1').val("10");
				$form.find('#dRate1').attr('readonly', 'readonly');
				$form.find('#dMonth2').val("6");
				$form.find('#dMonth2').attr('readonly', 'readonly');
				$form.find('#dRate2').val("20");
				$form.find('#dRate2').attr('readonly', 'readonly');
			}
			/*if (val == '1' || val == '2') {
				$form.find('#dRateAdd').val("0");
				$form.find('#dRateAdd').attr('readonly', 'readonly');
				if(true){
					$form.find('#penaltyMaxContTm').val("9");	
				}				
			}else if (val == '3') {
				$form.find('#dRateAdd').val("1");
				if(true){
					$form.find('#penaltyMaxContTm').val("");
					$form.find('div.section_penaltyMaxContTm').hide();
				}
			}else if (val == '4') {
				$form.find('#dRateAdd').val("1");
				//$form.find('#dRateAdd').attr('readonly', 'readonly');
				if(true){
					$form.find('#penaltyMaxContTm').val("");
					$form.find('div.section_penaltyMaxContTm').hide();
				}
			}*/
		}) // 是否辦理「代償/轉貸/借新還舊」
.end().find('input[name=chgOther]').click(function(){
            $('.chgCase').hide();
            if ($(this).val() == 'Y') {
                $('.chgCase').show();
            }
            else {
                $('.chgCase').find('input[type=text]').each(function(){
                    $(this).val('');
                }).end().find('input[type=radio]').each(function(){
                    $(this).attr('checked', false);
                });
            }
        }) // 是否辦理「代償/轉貸/借新還舊」
        .end().find('button[id=rmCalc]').click(function(){
        	var $form = $('#C160S01CForm');
        	
        	$.ajax({
                handler: CLS1161S02Action.handler,
                action: 'rmCalc_cls1161',
                formId: 'empty',
                data: {
                    'rctAMT': $form.find('#rctAMT').val()
                    ,'year'	: $form.find('#year').val()
                    ,'month': $form.find('#Month').val()
                    ,'prodKind'	: $form.find('#prodKind').val()
                },
                success: function(json){
                	$form.find('#approvedAmt').val( json.approvedAmt );
                	$form.find('#rmIntMax').val( json.rmIntMax );        
                }
            });
        	
        }) ;
        
        // 借款人資料表
        CLS1161S02Action.BorrowersGrid = $('#BorrowersGrid').iGrid({
            localFirst: true,
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 200, // 設定高度
            action: 'BorrowersQuery', // 執行的Method
            rowNum: 15,
            rownumbers: true,
            divWidth: 0,
            colModel: [{
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.custId"], // 統一編號
                align: "left",
                width: 100, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'custId' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.dupNo"], // 重覆序號
                align: "left",
                width: 100, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'dupNo' // col.id
            }, {
                colHeader: i18n.cls1161s02a["C160S01A.custName"], // 客戶名稱
                align: "left",
                width: 100, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'custName' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01BGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01B(data);
            }
        });
        
        // 帳號資料表
        CLS1161S02Action.AccountGrid = $('#AccountGrid').iGrid({
            localFirst: true,
            handler: CLS1161S02Action.gridhandler, // 設定handler
            height: 200, // 設定高度
            action: 'AccountQuery', // 執行的Method
            rowNum: 15,
            rownumbers: true,
            divWidth: 0,
            colModel: [{
                colHeader: i18n.cls1161s02a["C160S01C.accNo"], // 帳號
                align: "center",
                width: 100, // 設定寬度
                sortable: false, // 是否允許排序
                name: 'Account'
            }],
            ondblClickRow: function(rowid){
                var data = CLS1161S02Action.C160S01BGrid.getRowData(rowid);
                CLS1161S02Action.openC160S01B(data);
            }
        });
        
        var $C160S01FDiv = $('#C160S01FThickBox');
        // 轉出之原金融機構代碼-登錄
        var $subACNoTr = $('.subACNoTr');
        var $oLNEndDate = $("#oLNEndDate,#oLNAppDate");
        $C160S01FDiv.find('#bankNoLogin').click(function(){
            QueryBranch.open({
                // removeKey : [ '03', '06', '99' ],// 刪除不需要的選項
                fn: function(data){
                    var $form = $('#C160S01FForm');
                    if (data.bankCode == "017") {
                        data.branchName = data.bankCodeCn + data.branchName;
                        data.branchCode = data.bankCode + data.branchCode;
                        $subACNoTr.show();
                        if (CLS1161S02Action.L140S02F_FavChgCase == "Y") {
                            $oLNEndDate.addClass('required');
                        }
                    }
                    else {
                        $subACNoTr.hide();
                        $subACNoTr.find("input").val("");
                        $oLNEndDate.removeClass('required')
                    }
                    $form.find('#bankNo').val(data.bankCode || '');
                    $form.find('#bankName').val(data.bankCodeCn || '');
                    $form.find('#branchNo').val(data.branchCode || '');
                    $form.find('#branchName').val(data.branchName || '');
                }
            });
        }) // 轉出之原金融機構代碼
.end().find('#bankNo').each(function(){
            if (!thickboxOptions.readOnly) {
                var $subACNoTr = $('.subACNoTr');
                var $oLNEndDate = $("#oLNEndDate,#oLNAppDate");
                // $.browser.msie 已棄用，改用 navigator.userAgent 檢測
                if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident/') !== -1) {
                    $(this).bind('propertychange', function(){
                        $subACNoTr.hide();
                        $subACNoTr.find("input").val("");
                        var check = ($(this).val() == '017');
                        if (check) {
                            $subACNoTr.show();
                            if (CLS1161S02Action.L140S02F_FavChgCase == "Y") {
                                $oLNEndDate.addClass('required')
                            }
                        }
                        else {
                            $oLNEndDate.removeClass('required')
                        }
                        
                        $('#branchNo').readOnly(check);
                        $('#branchName').readOnly(check);
                    });
                }
                else {
                    // $.watch() 已棄用，改用 change 事件監聽
                    $(this).on('change keyup', function(){
                        var newval = $(this).val();
                        $subACNoTr.hide();
                        $subACNoTr.find("input").val("");
                        var check = (newval == '017');
                        if (check) {
                            $subACNoTr.show();
                            if (CLS1161S02Action.L140S02F_FavChgCase == "Y") {
                                $oLNEndDate.addClass('required')
                            }
                        }
                        else {
                            $oLNEndDate.removeClass('required')
                        }
                        $('#branchNo').readOnly(check);
                        $('#branchName').readOnly(check);
                    });
                }
            }
        }) // 代償本行帳號-引進
.end().find('#subACNoPullin').click(function(){
            CLS1161S02Action.openInputCustIdByLNF030();
            //CLS1161S02Action.openLNF030();
        }) // 代償同業房貸原因
.end().find('#subReason').change(function(){
            $('#subReaOth').hide();
            if ($('input:radio:checked[name="subReason"]').val() === '4') {
                $('#subReaOth').show();
            }
            else {
                $('#subReaOth').val('');
            }
});


		/*** J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制 ***/
		var item = API.loadCombos("lms7600_adoptFg");
		$("#adoptFg").setItems({
			size: "4",
	        item: item,
			clear : true,
			itemType: 'checkbox'
	    });
		
		$div.find("#btnApplyClearLand").click(function(){
			CLS1161S02Action.applyClearLand($div);
			CLS1161S02Action.initClearLand($div);//還原預設值
		})
        .end().find("input[name='isClearLand']").change(function(){ 
				var isClearLand = $("input[name='isClearLand']:radio:checked" ).val(); 

	        	if (isClearLand == "Y") {
                    $("#showClearLand").show();
                }else{
                	$("#showClearLand").hide();
                }
        })
		//是否變更預計動工日
		.end().find("input[name='isChgStDate']").change(function(){ 
			var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val();
	    	if (isChgStDate == "Y") {
	    		$(".showChgStDate").show();
	            $(".showIsChgRate").hide();
	            $(".showIsLegal").show();
	            
	    	}else if (isChgStDate == "N") {
	    		$(".showChgStDate").hide();
	            $(".showIsChgRate").show();   
	            
	            if($div.find("[name=isChgRate]:checked").val() == "Y"){
	        		$(".showIsLegal").show();
	        	}else{
	        		$(".showIsLegal").hide();
	        	}
	            
	        }else{
	        	$(".showChgStDate").hide();
	        	$(".showIsChgRate").hide();
	        	if($div.find("[name=isChgRate]:checked").val() == "Y"){
	         		$(".showIsLegal").show();
	         	}else{
	         		$(".showIsLegal").hide();
	         	}
	        }
	    	
	    	$div.find("#showClearLand").find("input:checkbox").trigger("change");
			
    	}).end().find("input[name='adoptFg']").change(function(){

        	//增加4.無以上措施(再加碼幅度為0%)
            if ($div.find("[name=adoptFg][value=4]").attr("checked")) {
            	$div.find("[name='adoptFg']").filter("[value !='4']").attr("checked", false).attr("disabled", true);  
            	$div.find(".showChgRate").hide();
            	$div.find("[name=isChgRate][value=N]").click();
            }
            else {
            	$div.find("[name='adoptFg']").attr("disabled", false);
            	$div.find("[name='adoptFg']").filter("[value='4']").attr("checked", false);
            }

            if ($div.find("[name=adoptFg][value=3]").attr("checked")) {
                $div.find(".showChgRate").show();
                $div.find("[name=isChgRate][value=Y]").click();   
            }
            else {
            	$div.find(".showChgRate").hide();
            	$div.find("[name=isChgRate][value=N]").click();
                
            }
        })
		.end().find("[name=isChgRate]").change(function(){ 
	    	var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
	    	if (isChgRate == "Y") {
	            $(".showChgRate").show();
	            $(".showIsLegal").show();
	        }else{
	        	$(".showChgRate").hide();
	        	$("#custRoa,#relRoa,#rateAdd,#roaBgnDate,#roaEndDate").val('');
	        	
	        	if($("[name=isChgStDate]:checked").val() == "Y"){
	        		$(".showIsLegal").show();
	        	}else{
	        		$(".showIsLegal").hide();
	        	}
	        }
    	})
		.end().find("#btnApplyClearLandRoa").click(function(){
            CLS1161S02Action.applyClearLandRoa($div.find("#cntrNo").val());
        })
		.end().find("#btnApplyIsLegal").click(function(){
	    	CLS1161S02Action.checkIsLegal();
	    });
		/*** End *** J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制 ***/
     
        // 放款帳號資料表
        CLS1161S02Action.lnf030Grid = $('#lnf030Grid').iGrid({
            localFirst: true,
            handler: 'cls1151gridhandler',
            height: 230,
            rownumbers: true,
            rowNum: 100,
            action: 'queryLNF030',
            divWidth: 0,
            postData: {
                tabFormMainId: responseJSON['refmainId'] || ''
            },
            colModel: [{
                colHeader: i18n.cls1161s02a['L140S02H.subACNo'],// 代償本行帳號,
                name: 'LOANNO',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1161s02a['L140S02H.subAmt'],// 代償本行金額,
                name: 'BAL',
                align: "left",
                width: 100,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            }]
        });
        
        // set readOnly
        // if (typeof pageAction != "undefined" && pageAction.readOnly){
        // $div.lockDoc(true);
        // }
        
        return true;
    },
	
	applyClearLandRoa : function(){
		var $L161M01AForm = $("#C160M01BForm");
		var cntrNo = $L161M01AForm.find("#cntrNo").val();
		var uid = $L161M01AForm.find("#uid").val();

		$.ajax({
			handler: "lmscommonformhandler",
	        formId: "L161M01AForm",
	        action: "applyClearLandRoa",
	        data: {
	            cntrNo: cntrNo,
	            mainMainId : responseJSON.mainId,
	            subMainId : uid,
	            custId: $L161M01AForm.find("#custId").val(),
				dupNo: $L161M01AForm.find("#dupNo").val(),
				custName: $L161M01AForm.find("#custName").val(),
				callFrom : "C160M01B"
	        },
	        success: function(obj){
	
	            var memoObj = {};
	            memoObj["custRoa"] = obj.custRoa;
	            memoObj["relRoa"] = obj.relRoa  ;
				memoObj["roaBgnDate"] = obj.roaBgnDate  ;
				memoObj["roaEndDate"] = obj.roaEndDate;
	
				$L161M01AForm.injectData(memoObj);
				CLS1161S02Action.checkIsLegal();
	        }
	    });
	},
	
	//J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	changeVacantLandValue : function (c160m01bObj){
		
		if(c160m01bObj != null && c160m01bObj.adoptFg){
			var vals = c160m01bObj.adoptFg.split("|");
            for (var i in vals) {
                $("[name=adoptFg][value=" + vals[i] + "]").attr("checked", true);
            }
		}

		$("input[name='adoptFg']").trigger('change');
		
		$("input[name='isClearLand']").trigger('change');
		
		$("input[name='isChgStDate']").trigger('change');
	},
	applyClearLand : function (c160m01btabDiv){
		var cntrNo = c160m01btabDiv.find("#cntrNo").val();
		$.ajax({
	        handler: "lmscommonformhandler",
	        formId: "L161M01AForm",
	        action: "applyClearLand",
	        data: {
	            cntrNo: cntrNo 
	        },
	        success: function(obj){
				$("input[name='isClearLand'][value='"+obj.isClearLand+"']:radio" ).attr( "checked" , "checked");   //塞值
				c160m01btabDiv.find("#C160M01BForm").injectData(obj);
				c160m01btabDiv.find("input:radio[name='isClearLand']").trigger('change');
	        }
	    });
	},
	
	initClearLand: function(divObject){
		//J-108-0083 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
    	divObject.find("#showClearLand").find("input:radio:checked" ).attr( "checked" ,false);
    	divObject.find("#showClearLand").find("input:checkbox").removeAttr("checked" );
    	divObject.find("#showClearLand").find("input:text").val('');
    	divObject.find("#showClearLand").find(".field").val('');
    	divObject.find("#showClearLand").find("select").val('');

    	divObject.find("#showClearLand").find("input:radio" ).trigger("change");
    	divObject.find("#showClearLand").find("input:checkbox").trigger("change");
    },
	
	checkIsLegal : function (){
		var isClearLand = $("input[name='isClearLand']:radio:checked" ).val();
		var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val();
		var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
		
		if(isClearLand == "Y"){
			if( isChgStDate== "Y" || isChgRate== "Y"){
				 //屬於要控管，且有變更預計動工日/調整利率，要檢核是否符合本行規定
			}else{
				return;
			}   
		}else{
			return;
		}
		
		var data = [];
	    $("[name=adoptFg]:checked").each(function(v, k){
	        data.push($(k).val());
	    });
		
	    $.ajax({
	    	handler: "lmscommonformhandler",
	        action: "checkIsLegal",
	        async: false ,
	        data: {
	        	isClearLand :isClearLand,
	        	isChgStDate:isChgStDate,
	        	isChgRate:isChgRate,
	        	ctlType:$("#ctlType").val(),
	        	fstDate:$("#fstDate").val(),
	        	lstDate:$("#lstDate").val(),
	        	cstDate:$("#cstDate").val(),
	        	adoptFg:data.join("|"),
	        	rateAdd:$("#rateAdd").val(),
	        	custRoa:$("#custRoa").val(),
	        	relRoa:$("#relRoa").val()
	        },
	        success: function(obj){
	        	$("[name='isLegal'][value='" + obj.isLegal + "']:radio").attr("checked", "checked");
	        }
		});
	},
	
	checkVacantLandValue : function(){
		var $L161M01AForm = $("#C160M01BForm");
		var isClearLand = $("input[name='isClearLand']:radio:checked" ).val();
		var isChgStDate = $("input[name='isChgStDate']:radio:checked" ).val();
		var isChgRate = $("input[name='isChgRate']:radio:checked" ).val();
		
		if(!isClearLand){
    		return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.isClearLand"]+ i18n.def['val.required']);
    	}
    	 
    	if(isClearLand == "Y"){
			
    		if(!isChgStDate){
    			return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.isChgStDate"]+ i18n.def['val.required']);
    		}
    		
    		if($L161M01AForm.find("#fstDate").val() == ""){
				return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.fstDate"]+ i18n.def['val.required']);
			}
    		
    		
    		if($L161M01AForm.find("#lstDate").val() == ""){
				return CommonAPI.showMessage(i18n.lmsl140m01m["L140M01M.lstDate"]+ i18n.def['val.required']);
			}
    		
			if(isChgStDate == "Y"){
				if($L161M01AForm.find("#cstDate").val() == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.cstDate"]+ i18n.def['val.required']);
				}
				
				if($L161M01AForm.find("#cstReason").val() == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.cstReason"]+ i18n.def['val.required']);
				}
				
				var hasAdoptFg = "";
		        $("[name=adoptFg]:checked").each(function(v, k){
		        	hasAdoptFg ="Y";
		        }); 
		        if( hasAdoptFg == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.adoptFg"]+ i18n.def['val.required']);
				}   
			}else{
				if(!isChgRate){
        			return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.isChgRate"]+ i18n.def['val.required']);
        		}
			}

        	if(isChgRate == "Y" || (isChgStDate=="Y" && $L161M01AForm.find("[name=adoptFg][value=3]").attr("checked") )  ){
        		if($L161M01AForm.find("#rateAdd").val() == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.rateAdd"]+ i18n.def['val.required']);
				}
        		
        		if($L161M01AForm.find("#custRoa").val() == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.custRoa"]+ i18n.def['val.required']);
				}
        		
        		if($L161M01AForm.find("#relRoa").val() == ""){
					return CommonAPI.showMessage(i18n.cls1161s02a["L140M01M.relRoa"]+ i18n.def['val.required']);
				}
        	}
        	
        	//重新計算是否符合本行規定
        	if( isChgStDate== "Y" || isChgRate== "Y"){
				CLS1161S02Action.checkIsLegal();
			} 

    	} 
    	
        if (!$L161M01AForm.valid()) {
            $("input.data-error,select.data-error").eq(0).focus();
            return;
        }
	},
	
    /**
     * 開啟
     */
    open: function(data){
        try {
            // 不需要設定 regional，系統已經在 common.jqgrid.js 中透過 $.extend($.jgrid.defaults, {...}) 設定了
            console.log('開始初始化 CLS1161S02Action');
            
            if (!CLS1161S02Action.ready) 
                CLS1161S02Action.ready = CLS1161S02Action.build();
            
            // init
            CLS1161S02Action.init(data);
            
            $('#C160M01BThickBox').thickbox({
                title: (i18n.cls1161s02a['title'] || '額度明細表')+(data.cntrNo||''),
                width: 968,
                height: 500, // 550
                buttons: {
                    "saveData": function(){
                        //J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
                        var errMsg = CLS1161S02Action.checkVacantLandValue();
                        if(errMsg){
                            return;
                        }
                        CLS1161S02Action.saveDetial('C160M01BForm');
                        CLS1161S02Action.changeVacantLandValue(null);
                    },
                    // 'sure' : function() {
                    // $.thickbox.close();
                    // },
                    'close': function(){
                        CLS1161S02Action.dataCheck();
                        //$.thickbox.close();
                    }
                }
            });
            
            // load data
            CLS1161S02Action.load('C160M01BForm', data);
        } catch (e) {
            console.error('CLS1161S02Action.open 發生錯誤:', e);
            // 如果發生錯誤，嘗試直接開啟簡化版視窗
            CLS1161S02Action.fallbackOpen(data);
        }
    },
    
    /**
     * 備用開啟方法
     */
    fallbackOpen: function(data) {
        $('#C160M01BThickBox').thickbox({
            title: '編輯額度明細',
            width: 850,
            height: 600,
            modal: true,
            open: function() {
                if (data) {
                    // 簡單的資料載入
                    for (var key in data) {
                        var $field = $('#' + key);
                        if ($field.length > 0) {
                            if ($field.is(':input')) {
                                $field.val(data[key]);
                            } else {
                                $field.text(data[key]);
                            }
                        }
                    }
                }
            },
            buttons: {
                'saveData': function() {
                    $.thickbox.close();
                },
                'close': function() {
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 讀取資料
     */
    load: function(formName, data){
    	var my_dfd = $.Deferred();
    	// 設定主債務人資料 & reset
        if ($.isArray(formName)) {
            for (var o in formName) {
                var $fm = $('#' + formName[o])
                $fm.setValue(CLS1161S02Action.data);
                $fm.attr('formOid', '');
            }
        }
        else {
            var $fm = $('#' + formName);
            $fm.setValue(CLS1161S02Action.data);
            $fm.attr('formOid', '');
        }
        
        if (data) {
            $.ajax({
                handler: CLS1161S02Action.handler,
                action: 'loadDetial',
                formId: formName,
				async:false,
                data: $.extend({
                    formName: formName
                }, data || {}),
                success: function(response){
                    if ($.isArray(formName)) {
                        for (var o in formName) {
                            var fName = formName[o];
                            if (fName == 'C160S01CForm') {
                                CLS1161S02Action.L140S02F_FavChgCase = response.L140S02F_FavChgCase;
                            }
                            $('#' + fName).setValue(response[fName], false);
                        }
                    }
                    else {
                        var $form = $('#' + formName);
                        $form.setValue(response[formName], false);
                        if (formName == "C160S01FForm") {
                            /*
                             * 	C160S01F.subReason{1.買賣,  2.利率,  3.額度,  4.其它}
                             */
                        	if (response[formName].subReason == '4') {
                                $('#subReaOth').show();
                            }
                            else {
                                $('#subReaOth').hide();
                            }
                            
                        }
						CLS1161S02Action.changeVacantLandValue(response[formName]);
                    }
                    FormAction.init(formName);
                    //=======
                    my_dfd.resolve();
                }
            });
        }
        return my_dfd.promise();
    },
    /**
     * 複製資料
     */
    copyDetial: function(formName, data){
        // MegaApi.confirmMessage(i18n.def['confirmCopy'], function(action) {
        MegaApi.confirmMessage(i18n.def['confirmRun'], function(action){
            if (action) {
                $.ajax({
                    handler: CLS1161S02Action.handler,
                    action: 'copyDetial',
                    formId: formName,
                    data: $.extend({
                        formName: formName
                    }, data || {}),
                    success: function(response){
                        // 關閉複抄視窗
                        if (formName == 'C160S01CForm') {
                            $.thickbox.close();
                        }
                        // MegaApi.showPopMessage(i18n.def['confirmTitle'],i18n.def['confirmCopySuccess']);
                        MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);
                        CLS1161S02Action.reload(formName);
                    }
                });
            }
        });
    },
    /**
     * 儲存資料
     */
    saveDetial: function(formName, data){
        var check = true;
        if ($.isArray(formName)) {
            for (var o in formName) {
                if (check) 
                    check = $('#' + formName[o]).valid();
            }
        }
        else {
            check = $('#' + formName).valid();
        }
        
        if (check) {
            $.ajax({
                handler: CLS1161S02Action.handler,
                action: 'saveDetial',
                formId: formName,
				async:false,
                data: $.extend({
                    formName: formName
                }, CLS1161S02Action.data, data || {}),
                success: function(response){
                    if (response.message) {
                        var message = i18n.cls1161s02a['C160S01C.warn3'] || '';
                        message += '<br/>';
                        message += response.message;
                        MegaApi.showPopMessage(i18n.def['saveSuccess'], message);
                    }
                    else {
                        // MegaApi.showPopMessage(i18n.def['confirmTitle'],
                        // i18n.def['saveSuccess']);
                        MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['runSuccess']);
                    }
                    
                    if ($.isArray(formName)) {
                        for (var o in formName) {
                            var fName = formName[o];
                            $('#' + fName).setValue(response[fName], false);
                            CLS1161S02Action.reload(fName); // reload grid
                        }
                    }
                    else {
                        $('#' + formName).setValue(response[formName], false);
                        CLS1161S02Action.reload(formName); // reload grid
                    }
                    // check init
                    FormAction.init(formName);
                }
            });
        }
    },
    /**
     * 刪除資料
     */
    deleteDetial: function(formName, data){
        MegaApi.confirmMessage(i18n.def['confirmDelete'], function(action){
            if (action) {
                $.ajax({
                    handler: CLS1161S02Action.handler,
                    action: 'deleteDetial',
                    formId: formName,
                    data: $.extend({
                        formName: formName
                    }, data || {}),
                    success: function(response){
                        MegaApi.showPopMessage(i18n.def['confirmTitle'], i18n.def['confirmDeleteSuccess']);
                        CLS1161S02Action.reload(formName); // reload grid
                    }
                });
            }
        });
    },
    /**
     * 重整資料表
     */
    reload: function(formName){
        switch (formName) {
            case 'C160S01AForm':
                CLS1161S02Action.C160S01AGrid.reload();
                break;
            case 'C160S01BForm':
                CLS1161S02Action.C160S01BGrid.reload();
                break;
            case 'C160S01CForm':
                CLS1161S02Action.C160S01CGrid.reload();
                break;
            case 'C160S01FForm':
                CLS1161S02Action.C160S01FGrid.reload();
                break;
        }
    },
    isC160S01Aevent: false,
    /**
     * 開啟擔保品明細
     */
    openC160S01A: function(data){
    
        // 綁訂擔保品事件
        if (!this.isC160S01Aevent) {
        
            $(".showMan").hide();
            $(".showSen").hide();
            if (data.unitChg == "Y") {
                $(".showSen").show();
            }
            else {
                $(".showMan").show();
            }
            
            this.isC160S01Aevent = true;
            var $form = $("#C160S01AForm");
            // 擔保品係
            $form.find("#term1").change(function(){
                var term1Str = "";
                switch ($(this).val()) {
                    case "1":
                        // C160S01A.027=，應由借款人出具「同意書」予本行。
                    	// J-113-0149 E-LOAN簽報書額度明細表擔保品明細切結同意事項修改
                        // C160S01A.027.V2=，應由擔保品提供人出具「不動產使用狀況暨擔保借款抵押權設定種類聲明書」
                        // 及借款人出具「自住切結書」或申請日於民國113年4月1日後，
                        // 得改出具「消費金融專用借款申請書暨個人資料表之房屋貸款擔保品用途聲明切結事項」予本行。
                        term1Str = i18n.cls1161s02a["C160S01A.027.V2"];
                        break;
                    case "2":
                    case "4":
                        // C160S01A.028=，應由承租人出具「同意書」予本行【應徵提租賃合約書】。
                        term1Str = i18n.cls1161s02a["C160S01A.028"];
                        break;
                    case "3":
                        // C160S01A.029=，應由借款人出具「同意書」予本行。
                        term1Str = i18n.cls1161s02a["C160S01A.029"];
                        break;
                    default:
                        break;
                }
                $("#term1Str").html(term1Str);
            });
            
            // 擔保品 加建增建未辦保存登記之建物
            var $term2ByValue1Span = $form.find("#term2ByValue1Span");
            $form.find("#term2").change(function(){
                if ($(this).val() == "1") {
                    $term2ByValue1Span.show();
                }
                else {
                    $term2ByValue1Span.hide().find("input").val("");
                }
                
            });
        }
        
        if (!/^01/.test(data.collTyp1)) {
            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.cls1161s02a['C160S01A.editMessage']);
            return;
        }
        // init
        // thickbox
        $('#C160S01AThickBox').thickbox({
            title: i18n.cls1161s02a['title.tab02'] || '',
            width: 900,
            height: 450,
            buttons: {
                'saveData': function(){
                    CLS1161S02Action.saveDetial('C160S01AForm');
                },
                'close': function(){
                    FormAction.check('C160S01AForm', function(){
                        $.thickbox.close();
                    });
                }
            }
        });
        // load data
        CLS1161S02Action.load('C160S01AForm', data);
    },
    /**
     * 開啟借保明細
     */
    openC160S01B: function(data){
        // init
        var $div = $('#C160S01BThickBox');
        $div.find('.rKindD,#lms1605s03_rType,#resonOther').hide();
        // thickbox
        $div.thickbox({
            title: i18n.cls1161s02a['title.tab03'] || '',
            width: 950,
            height: 350,
            buttons: {
                'saveData': function(){
                    CLS1161S02Action.saveDetial('C160S01BForm');
                },
                'close': function(){
                    FormAction.check('C160S01BForm', function(){
                        $.thickbox.close();
                    });
                }
            }
        });
        // load data
        CLS1161S02Action.load('C160S01BForm', data);
    },
    /**
     * 開啟產品種類明細
     */
    openC160S01C: function(data){
        responseJSON['seq'] = data.seq; // set seq
        $.ajax({
            handler: CLS1161S02Action.handler,
            action: 'L140S02CintWay',
            data: {
                refmainId: CLS1161S02Action.data.refmainId,
                mainId: CLS1161S02Action.data.mainId,
                seq: data.seq
            },
            success: function(response){
                var $form = $('#C160S01CForm');
                CLS1161S02Action.L140S02CFlag = response.l140s02cflag;
                CLS1161S02Action.calcLnEndDateFlag = response.calcLnEndDateFlag;
                CLS1161S02Action.L140S02AAMTFlag = response.l140s02aamtflag;
                CLS1161S02Action.L140S02A_RMRCTAMT = response.l140s02a_rmRctAmt ; 
                
                ilog.debug(CLS1161S02Action.handler+"::L140S02CintWay"
                		+", seq=["+(data.seq)+"]"
                		+", json.l140s02cflag["+CLS1161S02Action.L140S02CFlag+"], json.calcLnEndDateFlag["+CLS1161S02Action.calcLnEndDateFlag+"]");
                
                if (CLS1161S02Action.L140S02CFlag == 0) {
                    $form.find('[flag="nowFrom"]').show();
                } else {
                    $form.find('[flag="nowFrom"]').hide();
                }
            }
        });
        // init
        var $div = $('#C160S01CThickBox');
        $div.find('.lnSelect,.useLine,#spanChargeAmt,#autoRctTr,.chgCase').hide();
        
        //依不同的契約書種類呈現
        $div.find('div.spec_contractType').show();
        
        $div.find('#C160S01CTab').tabs({
            selected: 0
        });
        // 團貸 ACH扣帳方式
        // 扣帳方式為全部顯示，僅團貸開放編輯
        //$div.find('.payType,.ach').hide();
        $div.find('#payType,#achBranchNo,#achBranchNm,#achAccount').readOnly(true);
        $div.find('#achBankNoPullin').hide();
        if ($('#baseForm #caseType').val() == '2' && !thickboxOptions.lockDoc) {
        	//$div.find('.payType').show();
            $div.find('#payType,#achBranchNo,#achBranchNm,#achAccount').readOnly(false);
            $div.find('#achBankNoPullin').show();
        }
        //進帳他行相關欄位-全部案件皆不可編輯
        $div.find('#appOtherBranchNo,#appOtherBranchNm,#appOtherAccount').readOnly(true);
        $div.find('#appOtherBankNoPullin').hide();
        
        // thickbox
        $div.thickbox({
            title: i18n.cls1161s02a['title.tab04'] || '',
            width: 900,
            height: 500,
            buttons: {
                'saveData': function(){
                    var alertMsg = '';
                    var $form = $('#C160S01CForm');
                    var lnStartDate = $form.find('#lnStartDate').val() ||
                    '';
                    var lnEndDate = $form.find('#lnEndDate').val() ||
                    '';
                    var useStartDate = $form.find('#useStartDate').val() ||
                    '';
                    var useEndDate = $form.find('#useEndDate').val() ||
                    '';
                    var rctDate = $form.find('#rctDate').val() || '';
                    
                    var loanAmt = parseInt(($form.find('#loanAmt').val() ||
                    '').replace(/[,]/g, ''), 10);
                    var approvedAmt = parseInt(($form.find('#approvedAmt').val() ||
                    '').replace(/[,]/g, ''), 10);
                    var rctAMT = parseInt(($form.find('#rctAMT').val() || '').replace(/[,]/g, ''), 10);
                    var L140S02AAMT = parseInt((CLS1161S02Action.L140S02AAMTFlag || '').replace(/[,]/g, ''), 10);
                    
                    var autoRct =$form.find('[name=autoRct]:checked').val()  ||
                    '';
                    
                    ilog.debug("loanAmt= "+ loanAmt+" , approvedAmt= "+ approvedAmt
                    		+" , rctAMT= "+ rctAMT+" , l140s02a="+L140S02AAMT);
                    
                    if ($('input:radio:checked[name="chgOther"]').val() == 'Y' &&
                    CLS1161S02Action.C160S01FGrid.getGridParam("records") ==
                    0) {
                        CommonAPI.showErrorMessage(i18n.cls1161s02a['C160S01F.error003']);
                        return false;
                    }
                    if (approvedAmt && L140S02AAMT) {
                        if (approvedAmt > L140S02AAMT) {
                            //errorMsg.010=不可大於額度明細表產品種類分項金額。
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['C160S01C.loanAmt']+" "
                            	+i18n.cls1161s02a['errorMsg.010'] +
                            '(' +
                            (CLS1161S02Action.L140S02AAMTFlag ||
                            '') +
                            ')');
                            return false;
                        }
                    }
                    if (approvedAmt && loanAmt) {
                        if (approvedAmt > loanAmt) {
                            // errorMsg.001=核准額度需小於動撥金額
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.001'] +
                            '(' +
                            ($form.find('#loanAmt').val() ||
                            '') +
                            ')');
                            return false;
                        }
                    }
                    if (approvedAmt && rctAMT) {
                        if (rctAMT > approvedAmt) {
                            // errorMsg.002=進帳金額需小於
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.002']+approvedAmt);
                            return false;
                        }
                    }
                    
                    var lnEndDate2 = CLS1161S02Action.getLnEndDate();
                    if (lnEndDate2) {
                        if (lnEndDate > lnEndDate2) {
                            alertMsg = i18n.cls1161s02a['errorMsg.003'] + lnEndDate2;
                            //輸入的lnEndDate 超出 【 lnStartDate+年/月，所推算出的值】
                        }
                    }
                    if (lnStartDate && useEndDate) {
                        if (lnStartDate > useEndDate) {
                            // errorMsg.004=授信期間起日不可小於動用迄日。
                        	// errorMsg.013=授信起日不可超過動用迄日
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.013']);
                            return false;
                        }
                    }
                    if (useStartDate && useEndDate) {
                        if (useStartDate > useEndDate) {
                            // errorMsg.005=動用期間，迄日不可小於起日。
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.005']);
                            return false;
                        }
                        if (rctDate) {
                            if (rctDate < useStartDate ||
                            rctDate > useEndDate) {
                                // errorMsg.006=進帳日期需於動用期間之區間內。
                                CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.006']);
                                return false;
                            }
                        }
                    }
                    // 產品&所有權取得日
                    if ($form.find('#prodKind').val() === '59') {
                        if (!$form.find('#getDate').val()) {
                            // errorMsg.007=所有權取得日為必填欄位。
                            CommonAPI.showErrorMessage(i18n.cls1161s02a['errorMsg.007']);
                            return false;
                        }
                    }
                    
                    ilog.debug("autoRct= "+ autoRct+" , rctDate="+rctDate+ ", cmp="+(rctDate<=util.getToday()));
                    if(autoRct=="Y" && rctDate && rctDate<=util.getToday()){
                    	alertMsg = "<br/>"+i18n.cls1161s02a["C160S01C.autoRct"]+"為是，"+
                    	i18n.cls1161s02a["C160S01C.rctDate"]+"("+rctDate+")應大於營業日"
                    }
                    
                    // 調整動審表的授信期間如果超過只提醒不要擋死 add by fantasy 2013/06/13
                    if (alertMsg) {
                        alertMsg += '<br/>' +
                        (i18n.def['confirmRun'] || '');
                        MegaApi.confirmMessage(alertMsg, function(action){
                            if (action) 
                                CLS1161S02Action.saveDetial(['C160S01CForm', 'C160S01EForm']);
                        });
                    }
                    else {
                        CLS1161S02Action.saveDetial(['C160S01CForm', 'C160S01EForm']);
                    }
                },
                'close': function(){
                    FormAction.check(['C160S01CForm', 'C160S01EForm'], function(){
                        $.thickbox.close();
                    });
                }
            }
        });
        // load data
        CLS1161S02Action.load(['C160S01CForm', 'C160S01EForm'], data).done(function(){
        	ilog.debug("@openC160S01C, exec CLS1161S02Action.load(['C160S01CForm', 'C160S01EForm'], data)");
        	
        	var $frm = $("#C160S01CForm"); 
        	var prodKind = $frm.find("#prodKind").val()||"";
        	$frm.find("[name=autoPay]").removeAttr("disabled");
    		$frm.find("[name=autoRct]").removeAttr("disabled");
        	if(prodKind=="67"){
        		$frm.find("[name=autoPay][value=N]").attr("disabled", "disabled");
        		$frm.find("[name=autoRct][value=N]").attr("disabled", "disabled");
        		
        		$frm.find("#l140s02a_rmRctAmt").val(CLS1161S02Action.L140S02A_RMRCTAMT);
        		//～～～～～～～～～～～～
        		$frm.find("#span_memo_rctAmt").html(i18n.cls1161s02a['label.memo_rmRctAmt']); //label.memo_rmRctAmt=簽報書>產品資訊[以房養老每期進帳金額]
        		$frm.find(".for_prod_ReverseMortgage").show();
        	}else if(prodKind=="70"){
        		$frm.find("[name=autoPay][value=N]").attr("disabled", "disabled");
        		$frm.find("[name=autoRct][value=N]").attr("disabled", "disabled");

        		$frm.find("#l140s02a_rmRctAmt").val(CLS1161S02Action.L140S02A_RMRCTAMT);
        		//～～～～～～～～～～～～
        		$frm.find("#span_memo_rctAmt").html(i18n.cls1161s02a['label.memo_rmRctAmt.prodKind70']); //label.memo_rmRctAmt.prodKind70=簽報書>產品資訊[以房養老每期撥款額度]
        		$frm.find(".for_prod_ReverseMortgage").show();
        	}else{
        		//～～～～～～～～～～～～
        		$frm.find(".for_prod_ReverseMortgage").hide();
        	}
        	if(true){
        		if(prodKind=="67"){
            		$frm.find("#span_c160s01c_rctAmt").val(i18n.cls1161s02a['C160S01C.rctAMT']);
        		}else if(prodKind=="70"){
            		$frm.find("#span_c160s01c_rctAmt").val(i18n.cls1161s02a['label.rmRctAmt.prodKind70']);
        		}else{
        			$frm.find("#span_c160s01c_rctAmt").val(i18n.cls1161s02a['C160S01C.rctAMT']);
        		}
        	}
            
        	if($frm.find('#appOtherAccount').val()!= ''){
        		//撥款至他行目前僅PLOAN線上簽約對保案，若進帳他行帳號有值則為此類型案件，不開放編輯自動進/扣帳及本行帳號欄位
        		$frm.find('#autoPay,#atpayNo,#autoRct,#accNo').readOnly(true);
        		$frm.find('#atpayNoPullin,#accNoPullin').hide();
        	}else{
        		if(!thickboxOptions.lockDoc){
        			$frm.find('#autoPay,#atpayNo,#autoRct,#accNo').readOnly(false);
            		$frm.find('#atpayNoPullin,#accNoPullin').show();
        		}
        	}
            if($frm.find('#payType').val()=='01' ){//ACH扣帳
            	//鎖定本行扣帳欄位
            	$frm.find('#atpayNo').readOnly(true);
            	$frm.find('#atpayNoPullin').hide();
            	if( $frm.find('#ploanIsNeedACH').val() == 'Y' ){//進ploanIsNeedACH=Y為eDDA案件，要鎖定ACH相關欄位
            		$frm.find('#payType,#achBranchNo,#achBranchNm,#achAccount').readOnly(true);
                	$frm.find('#achBankNoPullin').hide();
            	}
            }
        });
        
        // load grid
        CLS1161S02Action.C160S01FGrid.reload();
    },
    /**
     * 開啟產品種類明細-代償
     */
    openC160S01F: function(data){
        // init
        var $div = $('#C160S01FThickBox');
        var $form = $("#C160S01FForm");
        // 原貸放日期
        var $oLNAppDate = $form.find("#oLNAppDate,#oLNEndDate");
        if (data && data.bankNo == '017') {
            if (CLS1161S02Action.L140S02F_FavChgCase == "Y") {
                $oLNAppDate.addClass("required");
            }
            else {
                $oLNAppDate.removeClass("required");
            }
            $('.subACNoTr').show();
        }
        else {
            $oLNAppDate.removeClass("required");
            $('.subACNoTr').hide();
        }
        // thickbox
        $div.thickbox({
            title: i18n.cls1161s02a['C160S01C.tab02'] || '',
            width: 850,
            height: 400,
            buttons: {
                'saveData': function(){
                    if ($form.valid()) {
                        // 原貸放日期
                        var oLNAppDate = $form.find("#oLNAppDate").val();
                        // 原貸款到期日
                        var oLNEndDate = $form.find("#oLNEndDate").val();
                        // 原貸款到期日 不可小於上面的原貸放日期
                        if (oLNEndDate && oLNEndDate && (oLNEndDate < oLNAppDate)) {
                            // C160S01F.error001=原貸款到期日 不可小於原貸放日期
                            return API.showMessage(i18n.cls1161s02a["C160S01F.error001"]);
                        }
                        var subACNo = $.trim($form.find("#subACNo").val());
                        var bankNo = $form.find("#bankNo").val();
                        var branchNo = $form.find("#branchNo").val();
                        if (bankNo == "017" && subACNo != '' &&
                        subACNo.length >= 3) {
                            if (branchNo.substring(3, 6) !=
                            subACNo.substring(0, 3)) {
                                // C160S01F.error002=代償本行帳號需為轉出之原金融機構代碼之分行！！
                                return API.showMessage(i18n.cls1161s02a["C160S01F.error002"]);
                            }
                        }
                    }
                    CLS1161S02Action.saveDetial('C160S01FForm');
                },
                'close': function(){
                    FormAction.check('C160S01FForm', function(){
                        $.thickbox.close();
                    });
                }
            }
        });
        // load data
        CLS1161S02Action.load('C160S01FForm', data);
        
        
    },
    /**
     * 開啟輸入custId視窗
     */
    openInputCustIdByLNF030: function(){
        var $form = $("#LNF030BoxForm");
        $form.reset();
        $("#openInputCustIdByLNF030Box").thickbox({
            //compID=統一編號
            title: i18n.def["compID"],
            width: 350,
            height: 160,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    // UPGRADETODO: 修正確定按鈕的表單驗證和資料傳遞
                    try {
                        if ($form && $form.valid && $form.valid()) {
                            var custId = $form.find("#LNF030Box_custId").val();
                            var dupNo = $form.find("#LNF030Box_dupNo").val();
                            
                            if (custId && custId.trim() !== '') {
                                $.thickbox.close();
                                CLS1161S02Action.openLNF030(custId, dupNo || '');
                            } else {
                                MegaApi.showErrorMessage('提示', '請輸入統編');
                                return false;
                            }
                        } else {
                            console.error('表單驗證失敗或表單不存在');
                        }
                    } catch (e) {
                        console.error('確定按鈕執行失敗:', e);
                        MegaApi.showErrorMessage('錯誤', '操作失敗，請重試');
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 放款帳號資料表
     */
    openLNF030: function(custId, dupNo){
        $('#lnf030ThickBox').thickbox({
            title: i18n.cls1161s02a['L140S02H.subACNo'],
            width: 500,
            height: 400,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    // UPGRADETODO: 修正確定按鈕的資料處理和錯誤檢查
                    try {
                        var data = CLS1161S02Action.lnf030Grid.getSingleData();
                        if (data && typeof data === 'object') {
                            var $form = $('#C160S01FForm');
                            if ($form.length > 0) {
                                $form.find("#subAmt").val(data.BAL || '');
                                $form.find("#subACNo").val(data.LOANNO || '');
                                $form.find("#oLNAppDate").val(data.OLNAPPDATE || '');
                                $form.find("#oLNEndDate").val(data.OLNENDDATE || '');
                                $.thickbox.close();
                            } else {
                                console.error('找不到 C160S01FForm 表單');
                                MegaApi.showErrorMessage('錯誤', '表單載入失敗，請重新操作');
                            }
                        } else {
                            console.error('未選取資料或資料格式錯誤');
                            MegaApi.showErrorMessage('提示', '請先選擇一筆資料');
                        }
                    } catch (e) {
                        console.error('確定按鈕處理失敗:', e);
                        MegaApi.showErrorMessage('錯誤', '操作失敗，請重試');
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
        CLS1161S02Action.lnf030Grid.reload({
            tabFormMainId: responseJSON['refmainId'] || '',
            custId: custId,
            dupNo: dupNo
        });
    },
    /**
     * 開啟借款人資料表
     */
    openBorrowers: function(data){
        $('#BorrowersThickBox').thickbox({
            title: i18n.cls1161s02a['title.tab03'], //
            width: 550,
            height: 350,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    // UPGRADETODO: 修正借款人資料表確定按鈕的資料檢查
                    try {
                        var data = CLS1161S02Action.BorrowersGrid.getSingleData();
                        if (data && typeof data === 'object') {
                            CLS1161S02Action.openAccount(data);
                        } else {
                            MegaApi.showErrorMessage('提示', '請先選擇一筆借款人資料');
                        }
                    } catch (e) {
                        console.error('借款人確定按鈕執行失敗:', e);
                        MegaApi.showErrorMessage('錯誤', '操作失敗，請重試');
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
        // reload grid data
        CLS1161S02Action.BorrowersGrid.reload($.extend({}, CLS1161S02Action.data, data));
    },
    /**
     * 開啟帳號資料表
     */
    openAccount: function(data){
        $('#AccountThickBox').thickbox({
            title: i18n.cls1161s02a['C160S01C.accNo'],
            width: 450,
            height: 350,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    // UPGRADETODO: 修正帳號資料表確定按鈕的資料檢查和表單操作
                    try {
                        var data = CLS1161S02Action.AccountGrid.getSingleData();
                        if (data && data.Account) {
                            var $targetField = $('#C160S01CForm #' + CLS1161S02Action.BorrowersId);
                            if ($targetField.length > 0) {
                                $targetField.val(data.Account);
                                $.thickbox.close();
                                $.thickbox.close(); // 關閉兩層 thickbox
                            } else {
                                console.error('找不到目標欄位:', CLS1161S02Action.BorrowersId);
                                MegaApi.showErrorMessage('錯誤', '表單欄位載入失敗');
                            }
                        } else {
                            MegaApi.showErrorMessage('提示', '請先選擇一筆帳號資料');
                        }
                    } catch (e) {
                        console.error('帳號確定按鈕執行失敗:', e);
                        MegaApi.showErrorMessage('錯誤', '操作失敗，請重試');
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
        // reload grid data
        CLS1161S02Action.AccountGrid.reload(data);
    },
    /**
     * 開啟複抄功能
     */
    openCopyC160S01C: function(data){
        CLS1161S02Action.C160S01COid = data.oid;
        $('#CopyC160S01CThickBox').thickbox({
            // title.copyTitle=請選擇產品資訊複抄來源序號
            title: (i18n.cls1161s02a['title.copyTitle'] || ''),
            width: 910,
            height: 400,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var sdata = CLS1161S02Action.C160S01CGrid.getSingleData();
                    if (sdata) {
                        CLS1161S02Action.copyDetial('C160S01CForm', $.extend(sdata, {
                            editOid: CLS1161S02Action.C160S01COid
                        }));
                    }
                },
                'cancel': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 計算授信期間迄日
     */
    getLnEndDate: function(){
        var $form = $('#C160S01CForm');
        var lnStartDate = $form.find('#lnStartDate').val() || '';
        var lnEndDate = ''
        if (lnStartDate) {
            try {
                var d = new Date($.trim(lnStartDate).replace(/-/g, '/'));
                d.setFullYear(d.getFullYear() +
                parseInt($form.find('#year').html() || '0'));
                d.setMonth(d.getMonth() +
                parseInt($form.find('#Month').html() || '0'));
                lnEndDate = d.getFullYear() + '-' +
                util.addZeroBefore((d.getMonth() + 1) + '', 2) +
                '-' +
                util.addZeroBefore((d.getDate()) + '', 2);
                
                //透過此 flag 去判斷是否要減1天（CLS1161FormHandler:: L140S02CintWay(...) ）
                //  短期 11-20 ~ 11-19
                //中長期 11-20 ~ 11-20
                var day = CLS1161S02Action.calcLnEndDateFlag;
                var tempdate = lnEndDate.replace(/-/g, "/");
                tempdate = new Date(tempdate);
                tempdate = tempdate.valueOf();
                tempdate = tempdate - day * 24 * 60 * 60 * 1000;
                tempdate = new Date(tempdate);
                var month = tempdate.getMonth() + 1;
                if (month <= 9) {
                    month = '0' + month;
                }
                var days = tempdate.getDate();
                if (days <= 9) {
                    days = '0' + days;
                }
                
                lnEndDate = (tempdate.getFullYear() + '-' + month + '-' + days);
            } 
            catch (e) {
            }
        }
        return lnEndDate;
    },
    /**
     * 查詢主債務人+從債務人名單
     */
    queryCustList: function(){
        var result = {};
        $.ajax({
            async: false,
            handler: CLS1161S02Action.handler,
            action: 'queryCustList',
            formId: 'C160M01BForm',
            data: CLS1161S02Action.data,
            success: function(response){
                $.extend(result, response.cust);
            }
        });
        
        return result;
    },
    /**
     * 檢查資料是否完成
     */
    dataCheck: function(){
        if (thickboxOptions.lockDoc) {
            $.thickbox.close();
        }
        else {
            $.ajax({
                //async : false,
                handler: CLS1161S02Action.handler,
                action: 'dataCheck',
                formId: 'empty',
                data: CLS1161S02Action.data,
                success: function(response){
                    if (response.message) {
                        var msg = '[' + i18n.def['close'] + '] ' + i18n.def['actoin_001'] + '<br/><br/>';
                        MegaApi.confirmMessage(msg + response.message, function(action){
                            if (action) 
                                $.thickbox.close();
                        });
                    }
                    else {
                        $.thickbox.close();
                    }
                    //reload
                    if (panelAction && panelAction.grid) 
                        panelAction.grid.reload();
                }
            });
        }
    },
	
	gridEjcicQueryResult: function(cellvalue, options, rowObject){	
		var _oid = rowObject.oid;
		var _dataSrcMemo = rowObject.dataSrcMemo;
		var _dataType = rowObject.dataType;
		var _reportFileType = rowObject.reportFileType;
		
		ilog.debug("gridEjcicQueryResult[_oid="+_oid+"][dataSrcMemo="+_dataSrcMemo+"][dataType="+_dataType+"][reportFileType="+_reportFileType+"]");
		switch (_dataSrcMemo) {
			
			case 'C101S01S':    //銀行法或金控法44條利害關係人
				// JSON格式
				$.form.submit({
		            url: webroot + '/app/cls/cls1131p03',
		            target: "_blank",
		            data: $.extend({
		            	dataType: _dataType,
		                isC120M01A: "N"
		            }, CLS1161S02Action.data)
		        });
				break;
				
			case 'C101S01U':   //聯徵B29, B33, B68
				if(_dataType = 'ejcic'){
		            $.form.submit({
		                url: webroot + '/app/cls/cls1131p02',
		                target: CLS1161S02Action.data +"_"+_dataType,
		                data: $.extend({
		                    isC120M01A: "N",
							isC160M01A: "Y"
		                }, CLS1161S02Action.data)
		            });
		    	}else{
		    		API.showMessage("無法開啟["+txId+"]");
		    	}
				break;
				
			case 'C101S04W': //司法院受監護/輔助宣告資料
			
				if(rowObject.dataStatus != '有案件' && rowObject.dataStatus != '查無資料'){
					API.showMessage('查無資料');
					return;
				}
			
				$.form.submit({
					url: webroot + '/app/simple/FileProcessingPage',
					target: "_blank",
					data: {
						oid: rowObject.oid,
						mainId: rowObject.mainId,
						serviceName: 'cls1131RpaFileService',
						fileDownloadName: "xxx.jpg",
						dataSource: _dataSrcMemo
					}
				});
				break;
			default:
				ilog.debug("oid["+rowObject.oid+"], unKnown dataSrcMemo["+rowObject.dataSrcMemo+"]");
				API.showMessage("無法開啟未定義的連結");
		}
	}
	
	
}
