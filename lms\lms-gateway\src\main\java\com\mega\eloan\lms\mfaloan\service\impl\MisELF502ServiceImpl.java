/* 
 * MisELF501ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF502;
import com.mega.eloan.lms.mfaloan.service.MisELF502Service;

/**
 * <pre>
 * 消金額度介面檔 MIS.ELF501
 * </pre>
 * 
 * @since 2013/1/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/17,REX,new
 *          </ul>
 */
@Service
public class MisELF502ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF502Service {

	@Override
	public List<ELF502> findByKey(String custId, String dupNo, String cntrNo,
			int seq) {

		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF502_findBykey",
				new Object[] { custId, dupNo, cntrNo, seq });

		List<ELF502> list = new ArrayList<ELF502>();
		for (Map<String, Object> row : rowData) {
			ELF502 model = new ELF502();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

}
