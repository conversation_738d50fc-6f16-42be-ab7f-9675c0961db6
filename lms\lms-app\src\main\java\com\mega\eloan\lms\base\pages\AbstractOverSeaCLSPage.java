package com.mega.eloan.lms.base.pages;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * 讓海外消金的頁面(各 subPanel)能共用一份 properties
 * 不必每個 page 都再 repeat 一次 
 * 
 * 共用的 Panel 
 * 	LMS1115S02PanelB1
 * 	LMS1115S02PanelB5
 * 	LMS1115S02PanelC1
 * 	LMS1115S02PanelC2
 * 	LMS1115S02PanelC3
 * 	LMS1115S02PanelC4
 * 	LMS1115S02PanelC5
 * 
 * JP模型的 Panel 
 * 	LMS1015S02PanelC1 	 
 */
public abstract class AbstractOverSeaCLSPage extends AbstractEloanForm {

	public AbstractOverSeaCLSPage() {
		super();
	}

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model,parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false);  // 不顯示 _headerPanel
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
}
