package com.mega.eloan.cls.dc.action;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.cls.dc.util.ClsDXLUtil;
import com.mega.eloan.lms.dc.base.XMLHandler;

/**
 * <pre>
 * ClsChkFldName
 * </pre>
 * 
 * @since 2013/2/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/22,Bang,new
 *          </ul>
 */
public class ClsChkFldName {

	/**
	 * 
	 * @param xmlHandler
	 *            XMLHandler
	 * @param domDoc
	 *            Document :dxl轉換為DOM XML後的xml檔
	 * @param fldName
	 *            String :db2Xml中的db2欄位名稱
	 * @param dxlFromName
	 *            String :當前dxl檔對應的FormName名稱
	 * @param tableName
	 *            String :當前dxl Form對應的TableName名稱
	 * @param itemValue
	 *            String: 從.dxl中對應到db2XML後取得的值
	 * @param occurs
	 *            int :occurs欄位目前執行序號
	 * @return String:fldValue
	 * @throws Exception
	 */
	public String chkAllFldName(XMLHandler xmlHandler, Document domDoc,
			String fldName, String dxlFromName, String tableName,
			String itemValue, int occurs) throws Exception {
		String fldValue = itemValue;
		// 特殊Table:C120S01C,fldName:credit 複選處理
		//if ("credit".equalsIgnoreCase(fldName)&& tableName.equalsIgnoreCase("C120S01C")) {
		if ("credit".equalsIgnoreCase(fldName)){
			if (StringUtils.isNotBlank(itemValue)) {
				StringBuffer sbValue = new StringBuffer();
				String tmp = itemValue;
				String[] spValue = tmp.split(";");
				for (int a = 0; a < spValue.length; a++) {
					if (a + 1 != spValue.length) {
						sbValue.append(spValue[a]).append("|");
					} else {
						sbValue.append(spValue[a]);
					}
				}
				fldValue = sbValue.toString().trim();
			}

		}

		// L140M01A------------------------------------------------------------------------
		if(tableName.equalsIgnoreCase("L140M01A")){
			// 保證成數
			if ("gutPercent".equalsIgnoreCase(fldName)) {
				// 1.假如FCLS110M01/02且code_1='36'則填入80
				if ("FCLS110M01".equalsIgnoreCase(dxlFromName)
						|| "FCLS110M02".equalsIgnoreCase(dxlFromName)) {
					String code_1 = xmlHandler.getItemValue(domDoc, "code_1");
					if ("36".equals(code_1)) {
						fldValue = "80";
					} else {
						fldValue = "0";
					}
				}
				// 2.假如FCLS119M01/02則捉gua_atm
				else if ("FCLS119M01".equalsIgnoreCase(dxlFromName)
						|| "FCLS119M02".equalsIgnoreCase(dxlFromName)) {
					String guaAtm = xmlHandler.getItemValue(domDoc, "gua_atm");
					fldValue = guaAtm;
				}
				// 3.其他皆為0
				else {
					fldValue = "0";
				}
			}
			if("cntrNo".equalsIgnoreCase(fldName)
					&&("FCLS115M01".equalsIgnoreCase(dxlFromName) 
					|| "FCLS715M01".equalsIgnoreCase(dxlFromName))
					&&xmlHandler.getItemValue(domDoc, "Sno").length()==2){
				fldValue =xmlHandler.getItemValue(domDoc, "ParentSno");
			}
			
			
			
		}
		// L140M01L------------------------------------------------------------------------
		// 團貸案種類:
		else if(tableName.equalsIgnoreCase("L140M01L")){
			if ("loanType".equalsIgnoreCase(fldName)) {
				String build = xmlHandler.getItemValue(domDoc, "BuildName");
				String grp = xmlHandler.getItemValue(domDoc, "GrpName");
				// 1.假如BuildName不等於空白則填入1；
				if (StringUtils.isNotBlank(build)) {
					fldValue = "1";
				}
				// 假如GrpName不等於空白則填入2
				else if (StringUtils.isNotBlank(grp)) {
					fldValue = "2";
				}
			}
		}
		// L140M01O-------------------------------------------------------
		// 稅籍地址
		else if(tableName.equalsIgnoreCase("L140M01O")){
			if ("taxAddr".equalsIgnoreCase(fldName)) {
				String value = xmlHandler.getItemValueByMup(domDoc, "LArea");
				int idx = value.indexOf(";");
				if (idx > -1) {
					fldValue = value.substring(0, idx);
				} else {
					fldValue = value;
				}
			}
			// 特殊Table:L140M01O,fldName:fireIns 複選處理
			if ("fireIns".equalsIgnoreCase(fldName)) {
				StringBuffer sbValue = new StringBuffer();
				int MAX_LOOP = 3;// 最多跑3個欄位:fireIns1(~3)
				for (int a = 1; a <= MAX_LOOP; a++) {
					String value = xmlHandler.getItemValue(domDoc,
							"fireIns" + a);
					if ("Y".equalsIgnoreCase(value)) {// dxl內此欄位若有值會是"Y"
						if (a != MAX_LOOP) {
							sbValue.append(a).append("|");
						} else {
							sbValue.append(a);
						}
					}
				}
				fldValue = sbValue.toString().trim();
			}
		}
		// L140M03A------------------------------------------------------------------------
		else if(tableName.equalsIgnoreCase("L140M03A")){
			// 團貸編號:若pack=Y時放入ParentSno
			if ("grpCntrNo".equalsIgnoreCase(fldName)) {
				String pack = xmlHandler.getItemValue(domDoc, "pack");
				if ("Y".endsWith(pack)) {
					fldValue = xmlHandler.getItemValue(domDoc, "ParentSno");
				}
			}
			//FCLS119M01&FCLS119M02
			if ("assisType".equalsIgnoreCase(fldName)
					&& "assisTypeYN".equalsIgnoreCase(fldName)) {
				String assisType = "";
				String pack = xmlHandler.getItemValue(domDoc, "pack");
				String excellent = xmlHandler.getItemValue(domDoc, "Excellent");
				if (StringUtils.isNotBlank(excellent)) {
					assisType = "1";
				}
				// else if Notes.Pack有值，assisType =2;
				else if (StringUtils.isNotBlank(pack)) {
					assisType = "2";		
				}
				// else若兩者均空，則出error
				else {
					assisType = "Error";
				}
				fldValue =assisType;
				if ("assisTypeYN".equalsIgnoreCase(fldName)){
					if (StringUtils.isNotBlank(excellent)) {
						fldValue = excellent;
					}
					// else if Notes.Pack有值，assisTypeYN=pack
					else if (StringUtils.isNotBlank(assisType)) {
						fldValue = assisType;
					}
					// else若兩者均空，則出error
					else {
						fldValue = "Error";
					}
				}

			}		
		}
		// L140S01A------------------------------------------------------------------------
		else if(tableName.equalsIgnoreCase("L140S01A")){
			// 性質:
			if ("custPos".equalsIgnoreCase(fldName)) {
				String guaKind = xmlHandler.getItemValue(domDoc, "gua_kind");
				// 1.若[gua_kind]有值，放[gua_kind]；
				if (StringUtils.isNotBlank(guaKind)) {
					fldValue = guaKind;
				} else {
					// 2.若[gua_kind]為空，[KeyMan]為空，則放C
					String keyMan = xmlHandler.getItemValue(domDoc, "KeyMan");
					if (StringUtils.isBlank(keyMan)) {
						fldValue = "C";
					} else {
						// 3.若[gua_kind]為空，[KeyMan]不為空，則放空白
						fldValue = "";
					}
				}
			}
		}
		// L140S02A-----------------------------------------------------------
		// L140S02A授信期間選項
		else if (tableName.equalsIgnoreCase("L140S02A")){
			
			if ("FCLS115M01".equalsIgnoreCase(dxlFromName) 
					|| "FCLS715M01".equalsIgnoreCase(dxlFromName)){
				if ("lnYear".equalsIgnoreCase(fldName)
						|| "lnMonth".equalsIgnoreCase(fldName)
						|| "lnFromDate".equalsIgnoreCase(fldName)
						|| "lnEndDate".equalsIgnoreCase(fldName)){
				// 授信期間選項 :DB2.LNRange
				String lnSelect = xmlHandler.getItemValue(domDoc, "LNRange");
				// 授信期間:假如DB2.LNRange為1時清空授信期間-年數,授信期間-月數，
	
				if ("1".equals(lnSelect)) {
					if ("lnYear".equalsIgnoreCase(fldName)
							|| "lnMonth".equalsIgnoreCase(fldName)) {
						fldValue = "";
					}
				}
				// 為2 時清空授信期間-起始日期,授信期間-截止日期 (為3或4時暫不動作)
				else if ("2".equals(lnSelect)) {
					if ("lnFromDate".equalsIgnoreCase(fldName)
							|| "lnEndDate".equalsIgnoreCase(fldName)) {
						fldValue = "";
					}
				}
			}
				
				if(xmlHandler.getItemValue(domDoc, "Sno").startsWith("918")){
					//2013-06-19當案件為團貸案時，產品種類依科目決定
					//20130626 用Sno開頭為918判斷為團貸案
					//02:12100100、12600100、12600200、12600500
					//03:13100100
					//07:13101000
					//31:13500100、14501000、14501500、14502000
					if("prodKind".equalsIgnoreCase(fldName)){
						String scode = xmlHandler.getItemValue(domDoc, "S_CODE");//科目
						if(scode.equalsIgnoreCase("12100100")
								|| scode.equalsIgnoreCase("12600100")
								|| scode.equalsIgnoreCase("12600200")
								|| scode.equalsIgnoreCase("12600500")){
							fldValue = "02";
						}else if(scode.equalsIgnoreCase("13100100")){
							fldValue = "03";
						}else if(scode.equalsIgnoreCase("13101000")){
							fldValue = "07";
						}else if(scode.equalsIgnoreCase("13500100")
								|| scode.equalsIgnoreCase("14501000")
								|| scode.equalsIgnoreCase("14501500")
								|| scode.equalsIgnoreCase("14502000")){
							fldValue = "31";
						}else{
							fldValue ="";//對不到就空白
						}
					}
					//若為團貸案，則改取ParentSno作為額度序號
					if("cntrNo".equalsIgnoreCase(fldName)){
						fldValue =xmlHandler.getItemValue(domDoc, "ParentSno");
					}
				}	
			}
		}
		// L140S02C------------------------------------------------------------------------
		//2013-05-21 modified by Sandra 依明澤要求追加程式，並移除原xml的對應設定
		else if (tableName.equalsIgnoreCase("L140S02C")) {
			if ("intWay".equalsIgnoreCase(fldName)) {
				String value = xmlHandler.getItemValue(domDoc, "intWay");
				if (StringUtils.isNotBlank(value.trim())) {
					fldValue = value.substring(0,1);
				}else{
					value = xmlHandler.getItemValue(domDoc, "rint_way");
					if(StringUtils.isNotBlank(value)&& value.startsWith("6")){
						fldValue ="2";
					}else{
						fldValue ="1";
					}
				}
			}	
		}
		// L140S02D------------------------------------------------------------------------
		else if (tableName.equalsIgnoreCase("L140S02D")) {
			// 段別:若check_(x)不為空，則寫入x,為空則不寫入整筆資料(已設於XML:<Occurs>)
			if ("phase".equalsIgnoreCase(fldName)) {
				if (StringUtils.isNotBlank(fldValue)) {
					fldValue = String.valueOf(occurs);
				}
			}
			// 利率變動方式:
			if ("rateChgWay2".equalsIgnoreCase(fldName)) {
				String value = xmlHandler.getItemValue(domDoc, "c_rate"
						+ occurs + "_1");
				fldValue = ClsDXLUtil.codeConvert("rateChgWay2", value);
			}
		}
		//L140S02F------------
		else if(tableName.equalsIgnoreCase("L140S02F")){
			// 特殊Table:L140S02F,itemType:tnf 複選處理
			if ("tnf".equalsIgnoreCase(fldName)) {
				StringBuffer sbValue = new StringBuffer();
				int MAX_LOOP = 3;// 最多跑3個欄位:tnf1(~4)_1(~3)
				for (int k = 1; k <= MAX_LOOP; k++) {
					String value = xmlHandler.getItemValue(domDoc, "tnf"
							+ occurs + "_" + k);
					if ("Y".equalsIgnoreCase(value)) {// dxl內此欄位若有值會是"Y"
						if (k != MAX_LOOP) {
							sbValue.append(k).append("|");
						} else {
							sbValue.append(k);
						}
					}
				}
				fldValue = sbValue.toString().trim();
			}
		}
		
		//C120S01B.C101S01B-------------------------------------------------
		else if(tableName.equalsIgnoreCase("C120S01B") || tableName
				.equalsIgnoreCase("C101S01B")){
			// 特殊Table:C120S01B.C101S01B,fldName:othType 複選處理
			if ("othType".equalsIgnoreCase(fldName)) {
				if (StringUtils.isNotBlank(itemValue)) {
					fldValue = ClsDXLUtil.getOthType(itemValue);
				}	
			}
		}
		
		//C120S01C,C101S01C-------------------------------------------------		
		else if(tableName.equalsIgnoreCase("C120S01C") || tableName
				.equalsIgnoreCase("C101S01C")){
			//若個人負債比率或家庭負債比率大於999，則一律寫入999
			if ("dRate".equalsIgnoreCase(fldName)||"yRate".equalsIgnoreCase(fldName)) {
				if (StringUtils.isNotBlank(itemValue)&&Double.parseDouble(itemValue)>999) {
					fldValue = "999";
				}else if (StringUtils.isNotBlank(itemValue)&&Double.parseDouble(itemValue)<0) {//2013-06-13 modify by Sandra 柏翰來信追加條件
					fldValue = "0";
				}	
			}
		}
				
		// C102M01A------------------------------------------------------------------------
		else if(tableName.equalsIgnoreCase("C102M01A")){
			// 是否為自用住宅:
			if ("selfChk".equalsIgnoreCase(fldName)) {
				String riskNo1 = xmlHandler.getItemValue(domDoc, "RiskNo_1_1");
				String riskNo2 = xmlHandler.getItemValue(domDoc, "RiskNo_1_2");
				String riskNo3 = xmlHandler.getItemValue(domDoc, "RiskNo_1_3");
				String riskNo4 = xmlHandler.getItemValue(domDoc, "RiskNo_1_4");
				// 1.上述四個條件皆為「Y」即為Y else N
				if ("Y".equalsIgnoreCase(riskNo1) && "Y".equalsIgnoreCase(riskNo2)
						&& "Y".equalsIgnoreCase(riskNo3)
						&& "Y".equalsIgnoreCase(riskNo4)) {
					fldValue = "Y";
				} else {
					fldValue = "N";
				}
			}
		}

		return fldValue;
	}

}
