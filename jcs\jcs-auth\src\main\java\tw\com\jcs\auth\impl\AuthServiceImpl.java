package tw.com.jcs.auth.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import tw.com.jcs.auth.Auth;
import tw.com.jcs.auth.AuthRule;
import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.auth.CodeItemService;
import tw.com.jcs.auth.MemberService;
import tw.com.jcs.auth.model.CodeItem;
import tw.com.jcs.auth.util.StringUtil;

/**
 * <pre>
 * 權限檢核
 * </pre>
 * 
 * @since 2022年12月21日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月21日
 *          </ul>
 */
public class AuthServiceImpl implements AuthService {

    MemberService memberService;

    CodeItemService codeItemService;

    @Resource
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Resource
    public void setCodeItemService(CodeItemService codeItemService) {
        this.codeItemService = codeItemService;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#auth(java.lang.String, tw.com.jcs.auth.Auth)
     */
    @Override
    public boolean auth(String userId, Auth auth) {
        return auth(userId, auth.code(), auth.type(), auth.rule());
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#auth(java.lang.String, int, int, int)
     */
    @SuppressWarnings("deprecation")
    @Override
    public boolean auth(String userId, int code, int type, int rule) {
        int value = memberService.getAuthTypeByUserAndAuth(userId, code);
        value &= type;
        if (rule == AuthRule.All) {
            return value == type;
        }
        return value > 0;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#auth(java.lang.String, java.util.Set, int, int, int)
     */
    @Override
    public boolean auth(String pgmDept, Set<String> roles, int code, int type, int rule) {
        // mark by fantasy 2012/04/13 because auths come from docid
        // int value = memberService.getAuthTypeByRoles(pgmDept, roles, code);

        CodeItem ci = codeItemService.getCodeItemByCode(code);
        String docid = null;
        if (ci != null)
            docid = ci.getDocid();
        int value = memberService.getAuthTypeByRoles(pgmDept, roles, code, docid);

        /*
         * 因為都還未設定文件ID,所以查無權限時,再向pgmcode上取得權限,使PG可繼續coding 待文件ID設定UI開放後解除~ by fantasy 2012/04/13
         */
        if (value == 0)
            value = memberService.getAuthTypeByRoles(pgmDept, roles, code);

        value &= type;
        if (rule == AuthRule.All) {
            return value == type;
        }
        return value > 0;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#auth(java.lang.String, java.util.Set, int, int)
     */
    @Override
    public boolean auth(String pgmDept, Set<String> roles, int code, int type) {
        return this.auth(pgmDept, roles, code, type, AuthType.Query);
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#auth(java.lang.String, java.util.Set, int, java.lang.String)
     */
    @Override
    public boolean auth(String pgmDept, Set<String> roles, int code, String type) {
        return auth(pgmDept, roles, code, authType(type));
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#authType(java.lang.String)
     */
    @Override
    public int authType(String type) {
        String temp = (type == null ? "" : type).toLowerCase();
        String[] types = temp.split("\\|");
        int authType = 0;
        for (int i = 0; i < types.length; i++) {
            String at = (types[i] == null ? "" : types[i].trim().toLowerCase());
            if ("modify".equals(at)) {
                authType += AuthType.Modify;
            } else if ("query".equals(at)) {
                authType += AuthType.Query;
            } else if ("accept".equals(at)) {
                authType += AuthType.Accept;
            }
        }
        return authType;
    }

    private static int[] DEFAULT_STEPS = { 1, 2, 3, 4, 5, 6 };

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.jcs.auth.AuthService#getDocIdList(java.util.Set, int)
     */
    @Override
    public List<String> getDocIdList(Set<String> roles, int pgmCode1st) {
        Set<String> docIdSet = new HashSet<String>();
        List<CodeItem> codeItemList = this.codeItemService.findByParentAndSteps(null, roles, pgmCode1st, DEFAULT_STEPS);
        for (CodeItem ci : codeItemList) {
            if (!"".equals(StringUtil.trim(ci.getDocid()))) {
                docIdSet.add(ci.getDocid());
            }
        }
        return new ArrayList<String>(docIdSet);
    }
}
