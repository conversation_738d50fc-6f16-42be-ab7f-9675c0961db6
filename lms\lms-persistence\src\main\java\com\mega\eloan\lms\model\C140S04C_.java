package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * <pre>
 * The persistent class for the C140S04C database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140S04C.class)
public class C140S04C_ {
	public static volatile SingularAttribute<C140S04C, BigDecimal> buAmtUnit;
	public static volatile SingularAttribute<C140S04C, String> buAddr;
	public static volatile SingularAttribute<C140S04C, BigDecimal> buBuM;
	public static volatile SingularAttribute<C140S04C, BigDecimal> buMm;
	public static volatile SingularAttribute<C140S04C, String> buMp;
	public static volatile SingularAttribute<C140S04C, String> buNum;
	public static volatile SingularAttribute<C140S04C, BigDecimal> buP;
	public static volatile SingularAttribute<C140S04C, String> buStru;
	public static volatile SingularAttribute<C140S04C, String> buUnit;
	public static volatile SingularAttribute<C140S04C, String> buUse;
	public static volatile SingularAttribute<C140S04C, String> buCurr;
	public static volatile SingularAttribute<C140S04C, C140M04A> c140m04a;
}
