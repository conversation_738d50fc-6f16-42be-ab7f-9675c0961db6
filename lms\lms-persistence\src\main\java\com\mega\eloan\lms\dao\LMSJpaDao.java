/* 
 * LMSJpaDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.io.Serializable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import tw.com.iisi.cap.dao.impl.GenericDao;

/**
 * <pre>
 * </pre>
 * 
 * @since 2011/6/16
 * <AUTHOR> @version <ul>
 *          <li>2011/6/16,,new
 *          </ul>
 */
public abstract class LMSJpaDao<T, PK extends Serializable> extends
		GenericDao<T, PK> {

	@PersistenceContext(unitName = "pu-lms")
	protected EntityManager entityManager;

	@Override
	public EntityManager getEntityManager() {
		return entityManager;
	}

}
