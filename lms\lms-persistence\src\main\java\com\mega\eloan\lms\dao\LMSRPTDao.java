/* 
 * LMSRPTDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.LMSRPT;

/** 授信批次報表歷史檔 **/
public interface LMSRPTDao extends IGenericDao<LMSRPT> {

	LMSRPT findByOid(String oid);

	List<LMSRPT> findByMainId(String mainId);

	List<LMSRPT> findByIndex01(String branch, Date endDate, String rptNo,
			String nowRpt);

	List<LMSRPT> findByIndex02(String branch, Date bgnDate, Date endDate,
			String rptNo, String nowRpt);

	LMSRPT findByIndex03(String mainId);


	List<LMSRPT> findByIndex04(String branch, String rptNo, Date dataDate,String nowRpt);
	
	<T> Page<T> findPage(Class<T> clazz, ISearch search);
}