package com.mega.eloan.lms.fms.handler.form;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS8500V01Page;
import com.mega.eloan.lms.fms.service.LMS8500Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L850M01A;
import com.mega.eloan.lms.model.L850M01B;
import com.mega.eloan.lms.model.L850M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 資料上傳作業
 * </pre>
 * 
 * @since 2019
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8500m01formhandler")
// @DomainClass(L850M01A.class)
public class LMS8500M01FormHandler extends AbstractFormHandler {

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS8500Service lms8500Service;

	@Resource
	MisdbBASEService misdbBASEService;

	// 有需要共用的就放在V01的properties
	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMS8500V01Page.class);

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getAppCodeBySsoUnitno(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		// 透過登入者分行判斷要撈出哪些申請類別
		Map<String, String> selectMap = lms8500Service.getAppCodeBySsoUnitno();
		result.putAll(selectMap);
		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newL850m01a(PageParameters params)
			throws CapException {

		String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		String appCodeFull = Util.trim(params.getString("appCode"));

		String appCode = appCodeFull.split("-")[0];
		String version = appCodeFull.split("-").length > 1 ? appCodeFull
				.split("-")[1] : "";

		CapAjaxFormResult result = new CapAjaxFormResult();
		L850M01A l850m01a = lms8500Service
				.newL850m01a(txCode, appCode, version);

		result.set(EloanConstants.OID, l850m01a.getOid());
		result.set(EloanConstants.MAIN_ID, l850m01a.getMainId());
		result.set("docURL", l850m01a.getDocURL());

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL850m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		lms8500Service.deleteL850m01a(oids);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL850m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 撈L850M01A
		// 撈L850M01B
		if (!Util.isEmpty(oid)) {
			L850M01A l850m01a = lms8500Service.findModelByOid(L850M01A.class,
					oid);

			// 第一次啟案自動塞入前案資訊
			result = formatResultShow(result, l850m01a);
		}
		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L850M01A l850m01a) throws CapException {
		String mainId = l850m01a.getMainId();

		// result = DataParse.toResult(l850m01a);
		// step1.撈出主檔的資訊
		result.set("ownBrName",
				" " + branchService.getBranchName(l850m01a.getOwnBrId()));
		result.set("creator", lmsService.getUserName(l850m01a.getCreator()));
		result.set("updater", lmsService.getUserName(l850m01a.getUpdater()));
		result.set("createTime",
				Util.nullToSpace(TWNDate.valueOf(l850m01a.getCreateTime())));
		result.set("updateTime",
				Util.nullToSpace(TWNDate.valueOf(l850m01a.getUpdateTime())));
		result.set("docStatus",
				getMessage("docStatus." + l850m01a.getDocStatus()));
		result.set("docStatusVal", l850m01a.getDocStatus());
		result.set("randomCode", l850m01a.getRandomCode());

		// step2.撈出簽章欄的資訊
		result = showFlowData(mainId, result);

		// step3.處理L850M01C的顯示，各appCode請自行發揮
		String appCode = Util.trim(l850m01a.getAppCode());
		if ("001".equals(appCode)) {
			result = showData001(mainId, result);
		}
		return result;
	}

	/**
	 * 顯示畫面資料for appCode 001
	 * 
	 * @param mainId
	 * @param result
	 * @return
	 */
	private CapAjaxFormResult showData001(String mainId,
			CapAjaxFormResult result) {
		List<L850M01C> l850m01cList = (List<L850M01C>) lms8500Service
				.findListByMainId(L850M01C.class, mainId);

		// 照理來說只會有一筆L850M01C
		if (l850m01cList != null && l850m01cList.size() != 0) {
			L850M01C l850m01c = l850m01cList.get(0);
			result.set("lmsBisSelfCapitalDate",
					Util.trim(l850m01c.getDetail1()));// 資料日期
			result.set("lmsBisSelfCapital", Util.trim(l850m01c.getDetail2()));// 自有資本(E46)
			result.set("lmsBisMegaRwa", Util.trim(l850m01c.getDetail3()));// 風險性資產(E61)
		}

		return result;
	}

	/**
	 * 共用的顯示流程簽章欄資料
	 * 
	 * @param mainId
	 * @param result
	 * @return
	 */
	private CapAjaxFormResult showFlowData(String mainId,
			CapAjaxFormResult result) {

		List<L850M01B> l850m01blist = (List<L850M01B>) lms8500Service
				.findListByMainId(L850M01B.class, mainId);
		if (!Util.isEmpty(l850m01blist)) {
			// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管
			// L5. 經副襄理L6. 總行經辦 L7.總行主管

			StringBuilder bossId = new StringBuilder("");
			for (L850M01B l850m01b : l850m01blist) {
				// 要加上人員代碼
				String type = Util.trim(l850m01b.getStaffJob());
				String userId = Util.trim(l850m01b.getStaffNo());
				String value = Util.trim(lmsService.getUserName(userId));
				if ("L1".equals(type)) {
					result.set("showApprId", userId + " " + value);
				} else if ("L3".equals(type)) {
					bossId.append(bossId.length() > 0 ? "<br/>" : "");
					bossId.append(userId);
					bossId.append(" ");
					bossId.append(value);
				} else if ("L4".equals(type)) {
					result.set("reCheckId", userId + " " + value);
				} else if ("L5".equals(type)) {
					result.set("managerId", userId + " " + value);
				} else if ("L6".equals(type)) {
					result.set("mainApprId", userId + " " + value);
				} else if ("L7".equals(type)) {
					result.set("mainReCheckId", userId + " " + value);
				}
			}
			result.set("bossId", bossId.toString());
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL850m01a(PageParameters params)
			throws CapException {
		// 不確定用途
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		// 儲存會跳訊息，送呈並儲存不跳訊息
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		// 畫面上要儲存的欄位
		String form = Util.trim(params.getString("mainPanel"));

		if (Util.isNotEmpty(mainId)) {
			L850M01A l850m01a = lms8500Service.saveL850m01a(mainId, form);

			// 格式化訊息長回畫面上
			result = formatResultShow(result, l850m01a);

			// 處例錯誤訊息放在service各隻自己去維護
			if (showMsg) {
				String showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);
			}
		}

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/*** 呈主管覆核(呈主管 主管覆核 拆2個method) */
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);

		String[] formSelectBoss = params.getStringArray("selectBoss");// 多選的多位授信主管
		String manager = Util.trim(params.getString("manager"));// 經副襄理，只有一格

		boolean haveCheckDate = params.containsKey("checkDate");// 核定日期，js傳來的就是今天
		boolean haveFlowAction = params.containsKey("flowAction");// 核准是true，退回是false
		boolean isApprove = params.getBoolean("flowAction");// 同上，用於確認是否為核准動作

		try {
			if (!Util.isEmpty(formSelectBoss) || Util.isNotEmpty(manager)) {
				// 清空等等要insert的簽章欄，不然會觸發unique key
				// 經辦->主管的時候才需要清
				lms8500Service.deleteL850m01bList(mainId, false);
			}

			// 處理簽章欄並執行流程
			lms8500Service.processL850m01bAndFlowAction(oid, formSelectBoss,
					manager, haveCheckDate, haveFlowAction, isApprove);
		} catch (FlowException t1) {
			logger.error(
					"[flowAction] lms8500Service.flowAction FlowException!!",
					t1);
			throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
		} catch (Throwable t1) {
			logger.error("[flowAction]  lms8500Service.flowAction EXCEPTION!!",
					t1);
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		return new CapAjaxFormResult();
	}
}
