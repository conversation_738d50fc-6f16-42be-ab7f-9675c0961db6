/* 
 * L140M04ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M04A;

/** 查核事項檔明細檔 **/
public interface L140M04ADao extends IGenericDao<L140M04A> {

	L140M04A findByOid(String oid);

	List<L140M04A> findByMainId(String mainId);

	L140M04A findByMainIdCheckCode(String mainId, String checkCode);

	List<L140M04A> findByIndex01(String mainId);
}