<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script type="text/javascript">
				loadScript('pagejs/fms/LMS8000M01Page');
			</script>
			<div class="button-menu funcContainer" id="buttonPanel">
				<!--Z-->
				<!--編製中 -->
				<th:block th:if="${_btnDOC_EDITING}">
	        		<button id="btnSave"> 
	        			<span class="ui-icon ui-icon-jcs-04" ></span>
	        			<th:block th:text="#{'button.save'}">儲存</th:block>
	        		</button>
					<button id="btnSend" >
	        			<span class="ui-icon ui-icon-jcs-02" ></span>
	        			<th:block th:text="#{'button.send'}">呈主管覆核</th:block>
	        		</button>
		        </th:block>		
				
				<!--待覆核 -->
				<th:block th:if="${_btnWAIT_APPROVE}">
	        		<button id="btnCheck" >
	        			<span class="ui-icon ui-icon-jcs-106" ></span>
	        			<th:block th:text="#{'button.check'}">覆核</th:block>
	        		</button>
		        </th:block>

				<th:block th:if="${_btn_QUERY}">
					<button type="button" id="btnPrintR02" class="forview">
						<th:block th:text="#{'button.printR02'}">列印檢核表</th:block>
					</button>
					<!--<button type="button" id="btnToEdit" class="forview">
						<th:block th:text="#{'button.toEdit'}">傳送至維護作業</th:block>
					</button>-->
				</th:block>
				
				<th:block th:if="${_btn_BackApprove}">
					<button type="button" id="btnRtnCopy" class="forview">
						<th:block th:text="#{'button.rtnCopy'}">退回修改</th:block>
					</button>
				</th:block>

				<button id="btnPrint" class="forview">
                	<span class="ui-icon ui-icon-jcs-03"></span>
					<th:block th:text="#{'button.print'}">列印</th:block>
				</button>
                <button id="btnExit"  class="forview">
                	<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>

				<th:block th:if="${_btn_EDITING}">
					<button type="button" id="btn_printLatestL140M01A">
						<th:block th:text="#{'button.printLatestL140M01A'}">列印最新額度批覆書</th:block>
					</button>
					<button type="button" id="btn_openLMS9535V01">
						<th:block th:text="#{'button.openLMS9535V01'}">關係戶往來彙總查詢</th:block>
					</button>
				</th:block>

			</div>
			<div class="tit2 color-black">
				<th:block th:text="#{'title'}"></th:block> - <span id="showCustId" class="color-blue" ></span>
			</div>
			<div class="tit2 color-black">
				<th:block th:text="#{'cntrNo'}"></th:block>：	<span id="cntrNo" class="color-blue" ></span>
				<br>
				<span id="showloanNo" style="display:none">
					<span class="overSeaHide" style="display:none">
						<th:block th:text="#{'loanNo'}"></th:block>
					</span>
					<span class="overSeaShow" style="display:none">
						<th:block th:text="#{'loanNoOvs'}"></th:block>
					</span>：<span id="loanNo" class="color-blue" ></span>
				</span>
			</div>
			
			<div class="tit2 color-black" >
				<span id="showRtnModifyReason" style="display:none">
 				  <th:block th:text="#{'rtnModifyReason'}"></th:block>：<span id="rtnModifyReason" class="color-blue" ></span>
				</span>
			</div>
						
			<form id="mainPanel">
				<span id="mainId" style="display:none"></span>
				<span id="custId" style="display:none"></span>
				<span id="dupNo" style="display:none"></span>
				<span id="custName" style="display:none"></span>
				<fieldset>
					<legend>
						<th:block th:text="#{'L260M01C.title'}">追蹤檢核項目</th:block>
					</legend>
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>
							<tr>
								<td>
									<button type="button" id="addL260M01C"><th:block th:text="#{'button.add'}">新增</th:block></button>
									<button type="button" id="delL260M01C"><th:block th:text="#{'button.delete'}">刪除</th:block></button>
									<button class="overSeaHide" type="button" id="impL260M01C"><th:block th:text="#{'button.impL260M01C'}">匯入其他敘做條件</th:block></button>
                                    <span class="text-red"><th:block th:text="#{'L260M01C.WORDING02'}"></th:block></span>
									<div id="l260m01cGridView">
										<div id="l260m01cGrid"></div>
									</div>
									<br>
									<span class="text-red">
                                        <span id="WORDING"></span>
		                                <!--<th:block th:text="#{'WORDING01'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING02'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING03'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING04'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING05'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING06'}"></th:block>
										<br>
										<th:block th:text="#{'WORDING07'}"></th:block>-->
		                            </span>
								</td>
							</tr>
						</tbody>
					</table>
				</fieldset>
				<fieldset>
					<legend>
						<th:block th:text="#{'L260M01D.title'}">紀錄清單</th:block>
					</legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
							<tr>
								<td>
									<button type="button" id="addL260M01D"><th:block th:text="#{'button.add'}">新增</th:block></button>
									<button type="button" id="delL260M01D"><th:block th:text="#{'button.delete'}">刪除</th:block></button>
                                    <button type="button" id="impL260M01D"><th:block th:text="#{'button.impL260M01D'}">匯入未完成追蹤紀錄</th:block></button>
									<div id="l260m01dGridView">
										<div id="l260m01dGrid"></div>
									</div>
								</td>
							</tr>
                        </tbody>
                    </table>
                </fieldset>
			</form>

			<div id="docPanel">
				<fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄 </th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer"><!-- 文件異動紀錄--> 
                        	<div th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div>
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                            	<td width="35%" class="hd1">
                                    <th:block th:text="#{'doc.creator'}">文件建立者</th:block>&nbsp;&nbsp;
                                </td>
                                <td width="15%">
                                    <span id='creator'></span>(<span id='createTime'></span>)
                                </td>
                                <td width="30%" class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">最後異動者</th:block>&nbsp;&nbsp;
                                </td>
                                <td width="20%">
                                    <span id='updater'></span>(<span id='updateTime'></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                </td>
                                <td>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}">文件亂碼</th:block>&nbsp;&nbsp;
                                </td>
                                <td>
                                    <span id="randomCode" ></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
				
				<!--<fieldset>
	                <div id="tabs-appr" class="tabs" style='width:99%;'>
	                    <ul>
	                        <li>
	                            <a href="#tabs-appr01"><b><th:block th:text="#{'L260M01B.title01'}"></th:block></b></a>
	                        </li>
	                    </ul>
						<div class="tabCtx-warp">
	                        <div id="tabs-appr01" class="content">
 							<table width="100%">
                                <tr>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'L260M01B.managerId'}">經副襄理</th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="managerId" ></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'L260M01B.bossId'}">授信主管</th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="bossId" ></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'L260M01B.reCheckId'}">覆核主管</th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="reCheckId"></span>
                                    </td>
                                    <td width="12%" class="rt">
                                        <b class="text-red">
                                            <th:block th:text="#{'L260M01B.apprId'}">經辦</th:block>：
                                        </b>
                                    </td>
                                    <td width="12%" class="lt">
                                        <span id="showApprId"></span>
                                    </td>
                                </tr>
                            </table>
							</div>
	                    </div>
	               </div>				   
	            </fieldset>-->
			</div>
			
			<div id="openCheckBox" style="display:none"> 
				<div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><th:block th:text="#{'accept'}">核准</th:block></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><th:block th:text="#{'back'}">退回經辦修改</th:block></label>
				</span>
				</div>
			</div>
			<div id="selectBossBox"  style="display:none;">
			  <form id="selectBossForm">
	         	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                 <tr>
	            		<td class="hd1" width="60%"><th:block th:text="#{'L260M01B.selectBoss'}">授信主管人數</th:block>&nbsp;&nbsp;</td>
	                    <td width="40%"><select id="numPerson" name="numPerson">
	                    		<option value="1">1</option>
	                    		<option value="2">2</option>
	                            <option value="3">3</option>
								<option value="4">4</option>
	                    		<option value="5">5</option>
	                            <option value="6">6</option>
								<option value="7">7</option>
	                    		<option value="8">8</option>
	                            <option value="9">9</option>
								<option value="10">10</option>
	                    	</select>
						</td>
	                 </tr>
	                 <tr >
	                 	<td class="hd1" ><th:block th:text="#{'L260M01B.bossId'}">授信主管</th:block>&nbsp;&nbsp;</td>
	            		<td >
	            			<div id="bossItem"></div>
	                 	</td>
	                 </tr>
	                 <tr >
	            		<td class="hd1"><th:block th:text="#{'L260M01B.managerId'}">經副襄理</th:block>&nbsp;&nbsp;</td>
	                    <td><div id="managerItem"></div></td>
	                 </tr>
	           	 </table>
				</form>
  			</div>

			<div id="detailThickbox" style="display:none;">
				<form id="formDetail">
					<span id="cantEdit" style="display:none"></span><font color="red" style="font-size:18px"><b><span id="showCantEdit" style="display:none"></span></b></font>
					<div id="divL260M01C">
						<span id="oidL260M01C" style="display:none"></span>
						<span id="unidL260M01C" style="display:none"></span>
						<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr class="divL260M01Csub ovsHide">
									<td class="hd1">
										<th:block th:text="#{'L260M01C.staff'}">應辦理追蹤</th:block>
									</td>
									<td colspan="5">
										<input type="radio" id="staff" name="staff"/>
									</td>
								</tr>
								<tr class="divL260M01Csub ovsHide">
									<td class="hd1">
										<th:block th:text="#{'L260M01C.staffId'}">目前追蹤人員</th:block>
									</td>
									<td colspan="5">
                                        授信人員為<select id="fo_staffNo" name="fo_staffNo"></select>、
										AO為<select id="ao_staffNo" name="ao_staffNo"></select>(一律以額度序號進行設定)
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L260M01C.followKind'}">類別</th:block>										
									</td>
									<td colspan="5">
										<input type="checkbox" id="followKind" name="followKind"/>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L260M01C.followContent'}">追蹤事項通知內容</th:block>
									</td>
									<td colspan="5">
										<textarea id="followContent" name="followContent" cols="100" rows="8" maxlengthC="200"></textarea>
									</td>
								</tr>
								<!--<div id="divL260M01Csub">-->
									<tr class="divL260M01Csub">
										<td class="hd1" style="width:16%">
											<th:block th:text="#{'L260M01C.followWay'}">追蹤方式</th:block>
										</td>
										<td colspan="3" style="width:48%">
											<input type="radio" id="followWay" name="followWay"/>
											<button type="button" id="calNextDt"><th:block th:text="#{'button.calNextDt'}">計算下次追蹤日</th:block></button>
										</td>
										<td class="hd1" style="width:16%">
											<th:block th:text="#{'L260M01C.nextFollowDate'}">下次追蹤日</th:block>
										</td>
										<td style="width:16%">
											<input type="text" name="nextFollowDate" id="nextFollowDate" maxlength="10" size="8" readonly="true"/>
										</td>
									</tr>
									<tr class="divL260M01Csub followWayCycle">
										<td class="hd1">
											<th:block th:text="#{'L260M01C.followCycle'}">循環追蹤週期</th:block>
										</td>
										<td>
											<input type="text" id="followCycle" name="followCycle" size="2" integer="2" class="digits"/>
											&nbsp;&nbsp;<th:block th:text="#{'month'}">個月</th:block>
										</td>
										<td class="hd1">
											<th:block th:text="#{'L260M01C.followBgnDate'}">循環追蹤起日</th:block>
										</td>
										<td>
											<input type="text" class="date" name="followBgnDate" id="followBgnDate"/>
										</td>
										<td class="hd1">
											<th:block th:text="#{'L260M01C.followEndDate'}">循環追蹤止日</th:block>
										</td>
										<td>
											<input type="text" class="date" name="followEndDate" id="followEndDate"/>
											<br/>
											<span class="text-red">
		                                		<th:block th:text="#{'L260M01C.WORDING01'}"></th:block>
											</span>
										</td>
										<input type="hidden" id="openFormKind" />
									</tr>
									<div id ="nextFollowDateMemo" class="text-red" style="display:none;">
										<th:block th:text="#{'L260M01C.nextFollowDateMemo1'}">註1.所維護之下次追蹤日期必須晚於本案覆核日後之下一個營業日。</th:block><br/>
									    <th:block th:text="#{'L260M01C.nextFollowDateMemo2'}">主機系統以系統執行的當日時間起算，產生追蹤日介於未來兩個營業日之需追蹤事項:</th:block><br/>
									    <th:block th:text="#{'L260M01C.nextFollowDateMemo3'}">112/7/28 晚上，系統產生追蹤日介於7/31(次營業日)~8/01(不含次次營業日)之貸後項目</th:block><br/>
									    <th:block th:text="#{'L260M01C.nextFollowDateMemo4'}">112/8/24 晚上，系統產生追蹤日介於112/8/25(次營業日)~112/8/28(不含次次營業日) 之貸後項目</th:block><br/>																				
								    </div>	
									
									<div id ="memo" class="text-red" >
									    <br/>								    													
								    </div>	
									
							</tbody>
						</table>
					</div>

					<div id="divL260M01D" style="display:none;">
						<span id="oidL260M01D" style="display:none"></span>
						<span id="unidL260M01D" style="display:none"></span>
						<div id="tabsL260M01D" class="tabs">
							<ul>
		                        <li>
		                            <a id="li-tab1" href="#tab-1_1"><b><th:block th:text="#{'L260M01D.tab1'}"></th:block></b></a>
		                        </li>
		                        <li class="hide isShowTab2">
		                            <a href="#tab-1_2"><b><th:block th:text="#{'L260M01D.tab2'}"></th:block></b></a>
		                        </li>
		                        <li class="hide isShowTab3">
		                            <a href="#tab-1_3"><b><th:block th:text="#{'L260M01D.tab3'}"></th:block></b></a>
		                        </li>
								<li class="hide isShowTab4">
									<a href="#tab-1_4"><b><th:block th:text="#{'L260M01D.tab4'}"></th:block></b></a>
								</li>
								<li class="hide isShowTab5">
									<a href="#tab-1_5"><b><th:block th:text="#{'L260M01D.tab5'}"></th:block></b></a>
								</li>
								<li class="hide isShowTab6">
									<a href="#tab-1_6"><b><th:block th:text="#{'L260M01D.tab6'}"></th:block></b></a>
								</li>
								<!-- J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。-->
								<li class="hide isShowTab7">
									<a href="#tab-1_7"><b><th:block th:text="#{'L260M01D.tab7'}"></th:block></b></a>
								</li>
								<!-- J-113-0035 為利ESG案件之貸後管控, ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制 -->
								<li class="hide isShowTab8">
									<a href="#tab-1_8"><b><th:block th:text="#{'L260M01D.tab8'}"></th:block></b></a>
								</li>
								
		                    </ul>
							<div class="tabCtx-warp">
		                        <div id="tab-1_1">
		                        	<div>
			                        	<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr class="overSeaHide">
													<td class="hd1">
														<th:block th:text="#{'L260M01C.staff'}">應辦理追蹤</th:block>
													</td>
													<td colspan="3">
														<input type="radio" id="followStaff" name="followStaff"/>
													</td>
												</tr>
												<tr>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.followDate'}">追蹤事項通知日期</th:block>
													</td>
													<td>
														<span id="followDate"></span>
													</td>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.handlingStatus'}">辦理狀況</th:block>
													</td>
													<td>
														<select id ="handlingStatus" name="handlingStatus"></select>
													</td>
												</tr>
												<tr>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.chkDate'}">檢核日期</th:block>
													</td>
													<td>
														<input type="text" class="date" name="chkDate" id="chkDate"/>
													</td>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.conformFg'}">符合註記</th:block>
													</td>
													<td>
														<!--<input type="radio" id="conformFg" name="conformFg"/>-->
														<label><input type="radio" id="conformFg" name="conformFg" value="Y"/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
														<label><input type="radio" name="conformFg" value="N"/><th:block th:text="#{'no'}">否</th:block>&nbsp;</label>
													</td>
												</tr>
												<tr>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.followMemo'}">追蹤說明</th:block>
													</td>
													<td colspan="3">
														<textarea id="followMemo" name="followMemo" cols="100" rows="3" maxlengthC="165"></textarea>
													</td>
												</tr>
												<tr>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.certifiedFile'}">證明文件</th:block>
													</td>
													<td colspan="3">
														<th:block th:text="#{'L260M01D.fileDesc'}">證明文件說明</th:block>：
														<input type="text" id="fileDesc" name="fileDesc" maxlengthC="50"/>
														<br>
														<button type="button" id="uploadCertifiedFile"><th:block th:text="#{'uploadFile'}">選擇附加檔案</th:block></button>
														<button type="button" id="deleteCertifiedFile"><th:block th:text="#{'delteFile'}">刪除附加檔案</th:block></button>
                                                        <button class="overSeaHide" type="button" id="query0320"><th:block th:text="#{'btn.query0320'}">查詢0320</th:block>、0060交易</button>
														<button class="overSeaHide" type="button" id="query8250"><th:block th:text="#{'btn.query8250'}">查詢8250</th:block></button>
														<button class="overSeaHide loanNoShow" type="button" id="query8410"><th:block th:text="#{'btn.query8410'}">查詢當日匯入匯款</th:block></button>
														<button class="overSeaHide eLandShow" type="button" id="queryEland"><th:block th:text="#{'btn.queryEland'}">電子謄本</th:block></button>
														<button class="overSeaHide raspShow" type="button" id="queryRasp"><th:block th:text="#{'btn.queryRasp'}">實價登錄</th:block></button>
                                                        <br>
														<span class="text-red"><th:block th:text="#{'WORDING07'}">如文件未出現於列表，請點選左下角🔄更新列表</th:block></span>
														<div id="certifiedFileGridView">
															<div id="certifiedFileGrid"></div>
														</div>
													</td>
												</tr>
												<tr>
													<td class="hd1" width="25%">
														<th:block th:text="#{'L260M01D.repayUnusualFg'}">還款來源異</th:block>
													</td>
													<td width="25%">
														<label><input type="radio" id="repayUnusualFg" name="repayUnusualFg" value="Y"/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
														<label><input type="radio" name="repayUnusualFg" value="N"/><th:block th:text="#{'no'}">否</th:block>&nbsp;</label>
														<br><span class="text-red"><th:block th:text="#{'L260M01D.WORDING01'}"></th:block></span>
													</td>
													<td class="hd1" width="25%">
                                                        <span class="overSeaHide">
														    <th:block th:text="#{'L260M01D.isFinProd'}">是否申購本行理財商品</th:block>
                                                        </span>
													</td>
													<td width="25%">
                                                        <span class="overSeaHide">
                                                            <label><input id="finProdFg" name="finProdFg" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
                                                            <!--
                                                            <label><input type="radio" id="finProdFg" name="finProdFg" value="Y"/><th:block th:text="#{'yes'}">是</th:block></label>
                                                            <label><input type="radio" name="finProdFg" value="N"/><th:block th:text="#{'no'}">否</th:block>&nbsp;</label>
                                                            -->
                                                            <br><span class="text-red"><th:block th:text="#{'L260M01D.WORDING02'}"></th:block></span>
                                                        </span>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
		                    	</div>
								<div id="tab-1_2">
									<div>
										<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.repayUnusualFile'}">還款來源異常調查紀錄表及佐證資料</th:block>
													</td>
													<td colspan="3">
														<span class="text-red" style="width:800px;display: inline-block;"><th:block th:text="#{'L260M01D.WORDING01'}"></th:block></span>
														<br>
														<br>
														<button type="button" id="uploadRepayFile"><th:block th:text="#{'uploadFile'}">選擇附加檔案</th:block></button>
														<button type="button" id="deleteRepayFile"><th:block th:text="#{'delteFile'}">刪除附加檔案</th:block></button>
														<div id="repayFileGridView">
															<div id="repayFileGrid"></div>
														</div>
													</td>
												</tr>
												<tr class="divL260M01Dsub">
													<td class="hd1">
														<th:block th:text="#{'L260M01D.unusualDesc'}">理由敘述（留存調查記錄）</th:block>
													</td>
													<td colspan="3">
														<textarea id="unusualDesc" name="unusualDesc" cols="100" rows="8" maxlengthC="200"></textarea>
													</td>
												</tr>
												<tr class="divL260M01Dsub">
													<td class="hd1">
														<th:block th:text="#{'L260M01D.isNotional'}">是否承做</th:block>
													</td>
													<td>
														<label><input type="radio" id="isNotional" name="isNotional" value="Y"/><th:block th:text="#{'L260M01D.isNotional_Y'}">承做</th:block>&nbsp;</label>
														<label><input type="radio" name="isNotional" value="N"/><th:block th:text="#{'L260M01D.isNotional_N'}">婉卻</th:block>&nbsp;</label>
													</td>
													<td class="hd1">
														<th:block th:text="#{'L260M01D.isAML'}">是否申報疑似洗錢</th:block>
													</td>
													<td>
														<label><input type="radio" id="isAML" name="isAML" value="Y"/><th:block th:text="#{'L260M01D.isAML_Y'}">申報</th:block>&nbsp;</label>
														<label><input type="radio" name="isAML" value="N"/><th:block th:text="#{'L260M01D.isAML_N'}">不申報</th:block>&nbsp;</label>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
		                    	</div>
								<div id="tab-1_3">
									<div>
										<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tbody>
												<span class="text-red"><th:block th:text="#{'L260M01D.WORDING03'}"></th:block>
												<br><th:block th:text="#{'L260M01D.WORDING04'}"></th:block>
												<br><th:block th:text="#{'L260M01D.WORDING05'}"></th:block></span><br>
												<button type="button" id="importFinProdData"><th:block th:text="#{'btn.importFinProdData'}"></th:block></button>
												<!--
												<button type="button" id="deleteFinProdData"><th:block th:text="#{'btn.deleteFinProdData'}"></th:block></button>
												<button type="button" id="getLstFinProdData"><th:block th:text="#{'btn.getLstFinProdData'}"></th:block></button>
												-->
												<div id="finProdGridView">
													<div id="finProdGrid"></div>
												</div>
											</tbody>
										</table>
									</div>
		                    	</div>
								<div id="tab-1_4">
									<div>
										<span id="checkBeg" style="display:none"></span>
										<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td class="hd1" style="width:25%">
													<th:block th:text="#{'L260S01B.buildName'}">建案名稱</th:block>
												</td>
												<td style="width:25%">
													<input type="text" id="buildName" name="buildName" maxlengthC="30" class="required"/>
												</td>
												<td class="hd1" style="width:25%">
													<th:block th:text="#{'L260S01B.isSameCase'}"></th:block>
												</td>
												<td style="width:25%">
													<label><input type="radio" id="isSameCase" name="isSameCase" value="Y"/><th:block th:text="#{'yes'}"></th:block>&nbsp;</label>
													<label><input type="radio" name="isSameCase" value="N"/><th:block th:text="#{'no'}"></th:block>&nbsp;</label>
													<label><input type="radio" name="isSameCase" value="X"/>尚未確認&nbsp;</label>
												</td>
											</tr>
											<tr>
												<td class="hd1">
													<th:block th:text="#{'L260S01B.begForSell'}">初貸餘屋戶數</th:block>
												</td>
												<td>
													<input type="text" id="begForSell" name="begForSell" class="numeric required" positiveonly="true" size="5" integer="5" fraction="0"/>
												</td>
												<td class="hd1">
													<th:block th:text="#{'L260S01B.soldNumber'}">已售戶數</th:block>
												</td>
												<td>
													<input type="text" id="soldNumber" name="soldNumber" class="numeric required" positiveonly="true" size="5" integer="5" fraction="0"/>
												</td>
											</tr>
											<tr>
												<td class="hd1">
													<th:block th:text="#{'L260S01B.proStatus'}">進度狀態</th:block>
												</td>
												<td colspan="3">
													<select id="proStatus" name="proStatus">
														<option value=""><th:block th:text="#{'checkSelect'}">請選擇</th:block></option>
														<option value="E">相符</option>
														<option value="A">超前</option>
														<option value="B">落後</option>
													</select>
													<div id="proStatusB" style="display: none;">
														<br/>
														<th:block th:text="#{'L260S01B.behindDesc'}">落後原因</th:block>
														<input type="text" id="behindDesc" name="behindDesc" maxlengthC="200" class="required"/>
													</div>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div id="tab-1_5">
									<div>
										<!--
										<span class="text-red"><th:block th:text="#{'L260M01D.WORDING06'}"></th:block></span>
										<br>
										-->
										<div id="raspGrid"></div>
									</div>
								</div>
								<div id="tab-1_6">
									<div>
										<table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td class="hd1" style="width:25%">
													<th:block th:text="#{'L260M01D.begConstr'}">是否已動工興建</th:block>
												</td>
												<td style="width:25%">
													<label><input type="radio" name="begConstr" id="begConstr" value="Y"/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
													<label><input type="radio" name="begConstr" value="N"/><th:block th:text="#{'no'}">否</th:block>&nbsp;</label>
												</td>
											</tr>
											<tr id="constrHide" style="display:none;">
												<td class="hd1" style="width:25%">
													<th:block th:text="#{'L260M01D.actStDate'}">實際動工日</th:block>
												</td>
												<td style="width:25%">
													<input type="text" name="actStDate" id="actStDate" maxlength="10" size="8" readonly="true"/>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<!-- J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。-->
								<div id="tab-1_7">
									<div>
										<table  class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td  colspan="6">
													  <button type="button" id="btnprintVisitComp" class="forview" ><th:block th:text="#{'L260S01D.prtVisitCom'}">列印訪問紀錄表</th:block></button>
													  <button type="button" id="btnautoProLMS9535V01"><th:block th:text="#{'button.autoProLMS9535V01'}">產製關係戶往來彙總表</th:block></button>		
													  <button type="button" id="btnmodCompVisitVer"><th:block th:text="#{'button.modCompVisitVer'}">修改公司訪問紀錄表格式為最新版本</th:block></button>				   						   	
												</td>
											</tr>
											<tr>
												<td class="hd1">
													   <th:block th:text="#{'L260S01D.visitCompName'}"></th:block>
												</td>
												
												<td  colspan="5">
													<textarea cols="90" rows="2" class="txt_mult" id="visitCompName" name="visitCompName" maxlengthC="166" style="width:725px;height:40px;"></textarea>						

												</td>
											</tr>
											<tr>
												<td class="hd1">
													   <th:block th:text="#{'L260S01D.visitWay'}">訪問方式：</th:block>
												</td>
												
												<td  colspan="5">													
													<input type="radio" id="visitWay" name="visitWay" value="1"/><th:block th:text="#{'L260S01D.visitWayOption1'}"></th:block><br/>	
													<input type="radio" id="visitWay" name="visitWay" value="2"/><th:block th:text="#{'L260S01D.visitWayOption2'}"></th:block>	<th:block th:text="#{'L260S01D.visitWayMsg'}"></th:block>																													
												</td>
											</tr>
											<tr>	
												<td class="hd1">
													    <th:block th:text="#{'L260S01D.visitDt'}">訪問時間：</th:block>
												</td>
												<td colspan="2">
														<input type="text" class="date" name="visitDt" id="visitDt"/>
												</td>
												<td class="hd1">																
														<th:block th:text="#{'L260S01D.visitPlace'}">訪問地點：</th:block>
												</td >
												<td colspan="2">
														<input type="text" name="visitPlace" id="visitPlace" maxlengthC="40" size="30" />
												</td>
											</tr>
											
											<tr>
												<td class="hd1" >																
														<th:block th:text="#{'L260S01D.visitorJobTitle'}">受訪人職稱：</th:block>
												</td>
												<td >
													    <input type="text" name="visitorJobTitle" id="visitorJobTitle" maxlengthC="40" size="15" />
												</td>
												<td class="hd1" >																
														<th:block th:text="#{'L260S01D.visitorName'}">受訪人姓名：</th:block>
												</td>
												<td >
													    <input type="text" name="visitorName" id="visitorName" maxlengthC="40" size="10" />
												</td>
												<td class="hd1" >																
														<th:block th:text="#{'L260S01D.visitorPhone'}">受訪人電話：</th:block>
												</td>
												<td >
													    <input type="text" name="visitorPhone" id="visitorPhone" maxlength="20" size="20" />
												</td>
											</tr>
											<tr >	
												<td class="hd1">
													    <th:block th:text="#{'L260S01D.unitMgr'}">單位主管：</th:block>
														<button type="button" id="btnimportMgrForm"><th:block th:text="#{'button.reImport'}">重新引進</th:block></button>	
												</td>
												<td colspan="2" >
														<span id="unitMgr_S01D" name="unitMgr_S01D"  class="field" ></span>
														<br/>
														<span id="unitMgrName_S01D" name="unitMgrName_S01D"  class="field" ></span>
												</td>
												<td class="hd1">																
														<th:block th:text="#{'L260S01D.accountMgr'}">帳戶管理員：</th:block>
												</td>
												<td colspan="2" >
														<span id="accountMgr_S01D" name="accountMgr_S01D" class="field" ></span>
														<br/>
														<span id="accountMgrName_S01D" name="accountMgrName_S01D"  class="field" ></span>														
												</td>
										  </tr>            

										</table>
										
										<table  id="table_L260S01E" class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">

						   						   	
										</table>

                                        <table class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">
                                        <tbody>       
                                           <tr>                
                                                <td class="hd1">
                                                     <th:block th:text="#{'doc.docCode'}">文件亂碼</th:block>&nbsp;&nbsp;
                                                </td>
                                                <td>
                                                    <span id="randomCode_S01D" ></span>
                                                </td>
                                           </tr>
                                        </tbody>
                                        </table>
										
									</div>								
								</div>
								<!-- J-113-0150 配合分行，E-LOAN授信管理系統修改建檔維護-貸後管理追蹤檢核表維護-查詢未完成案件等。 -->
								<div id="tab-1_8">
									<div>		
									    <table>
									        <tr>
												<td  colspan="3">
													  <button type="button" id="btnViewESGContent" class="forview" ><th:block th:text="#{'button.viewESGContent'}">檢視應注意/承諾/追蹤ESG連結條款明細內容</th:block></button>											   						   	
												</td>
											</tr>
									    </table>								
										<table  id="table_L260S01F" class="tb2" width="95%" border="0" cellspacing="0" cellpadding="0">	   	
										</table>																																																				
									</div>								
								</div>
								
							</div>
						</div>
					</div>
				</form>
			</div>

			<!-- J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表 -->
			<div id="inputSearchRelationRptForm" style="display: none;"><!--Z-->
			<form id="relationRptForm">
				<table class="tb2" border="1" width="100%" cellspacing="0" cellpadding="0">														
					<tr>
						<td width="40%" class="hd1">							
							<th:block th:text="#{'L260S01D.strDate'}">起始年月</th:block>&nbsp;&nbsp;
						</td>
						<td width="60%">
							<input type="text" id="qryDtS0" name="qryDtS0"
							class="required max " maxlength="4" minlength="4" size="4"  style="text-align: right;" /><th:block th:text="#{'L260S01D.YYY'}">年</th:block>&nbsp;<input type="text" id="qryDtS1" name="qryDtS1"
							class="required max number" maxlength="2" size="2" /><th:block th:text="#{'L260S01D.MM'}">月</th:block>
						</td>
					</tr>
					<tr>
						<td class="hd1">
							<th:block th:text="#{'L260S01D.endDate'}">迄至年月</th:block>&nbsp;&nbsp;
						</td>
						<td>
							<input type="text" id="qryDtE0" name="qryDtE0"
							class="required max " maxlength="4" minlength="4" size="4"  style="text-align: right;" /><th:block th:text="#{'L260S01D.YYY'}">年</th:block>&nbsp;<input type="text" id="qryDtE1" name="qryDtE1"
							class="required max number" maxlength="2" size="2" /><th:block th:text="#{'L260S01D.MM'}">月</th:block>
						</td>
					</tr>
				</table>
			</form>
			</div>

            <!-- J-112-0307 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表 -->
			<div id="importMgrThickbox" style="display:none">
				 <form id="importMgrForm">
						<table border="0" cellpadding="0" cellspacing="0" class="tb4" width="100%">
							<tr>
								<td class="hd1" style="width:40%"><th:block th:text="#{'L260S01D.unitMgr'}">單位主管</th:block></td>
								<td style="width:60%"><select id="_unitMgr" name="_unitMgr"  space="true"  class="required"></select></td>
							</tr>
							<tr>
								<td class="hd1" style="width:40%"><th:block th:text="#{'L260S01D.accountMgr'}">帳戶管理員</th:block></td>
								<td style="width:60%"><select id="_accountMgr"  name="_accountMgr" space="true" class="required" ></select></td>
							</tr>
						</table>
				 </form>
			</div>
			
			
			<div id="rtnCopyBox" style="display:none">
				<form id="rtnCopyForm">
					<table class="tb2" width="100%">
						<tbody>
						<tr>
							<td class="hd1" style="width:100%;">
							    <span class="text-red">＊</span>
								<th:block th:text="#{'L260M01A.rtnModifyReason'}">請輸入退回修改理由</th:block>
							</td>
							<td>
								<textarea id="rtnModifyReasonStr" name="rtnModifyReasonStr" cols="30" rows="10" maxlengthC="100" class="trim required"></textarea>
							</td>
						</tr>						
						</tbody>
					</table>
				</form>
			</div>

			<div id="updStatus" style="display:none">
				<table>
					<tr>
						<td>
							<label>
								<th:block th:text="#{'L260M01D.handlingStatus'}">辦理狀況</th:block>：
								<select id="updStatusVal" name="updStatusVal"></select>
							</label>
						</td>
					</tr>
				</table>
			</div>
			
			<!-- J-113-0035 為利ESG案件之貸後管控, ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制  -->
			<div id="viewESGContentThickbox" style="display:none">				 
						<table border="0" cellpadding="0" cellspacing="0" class="tb4" width="100%">
							<tr>								
								<td style="width:100%"><textarea id="ESGContent" name="ESGContent"  rows="25" cols="90" disabled ></textarea></td>
							</tr>
						</table>				 
			</div>

			<!-- J-110-0363 By ID -->
			<div id="addById" style="display:none">
				<table>
					<tr>
						<td>
							<label>
								<th:block th:text="#{'addById'}">ID階層</th:block>：
								<input name="byIdFlag" type="radio" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
								<input name="byIdFlag" type="radio" value="N"/><th:block th:text="#{'no'}">否</th:block>
							</label>
						</td>
					</tr>
				</table>
			</div>

			<div id="addFollowDate" style="display:none">
				<table>
					<tr>
						<td>
							<label>
								<th:block th:text="#{'L260M01D.followDate'}"></th:block>：
								<input type="text" class="date required" name="addFoDate" id="addFoDate" maxlength="10" size="8"/>
							</label>
						</td>
					</tr>
				</table>
			</div>

			<div id="loadLMS9535V01" style="display:none"></div>

			<div id="printView" style="display:none;">
				<div id="printGrid" ></div>
			</div>

            <div id="undoneElf602View" style="display:none;">
                <div id="undoneElf602Grid" ></div>
            </div>
			
			<div id="dwFinProdView" style="display:none;">
				<div id="dwFinProdGrid" ></div>
			</div>
			
			<div id="finProdDetail" style="display:none">
				<form id="formFinProdDetail">
					<table>
						<tr>
							<td colspan="2" align="right">
								<th:block th:text="#{'L260S01A.dataDt'}"></th:block>：<span id="dataDt"></span>
							</td>
						</tr>
						<tr>
							<td>
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.cust'}"></th:block></b></span>：<span id="custInfo"></span>
							</td>
							<td>
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.proType'}"></th:block></b></span>：<span id="proType" style="display:none"></span><span id="proTypeStr"></span>
							</td>
						</tr>
						<tr>
							<td>
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.tranType'}"></th:block></b></span>：<span id="tranType" style="display:none"></span><span id="tranTypeStr"></span>
							</td>
							<td>
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.accNo'}"></th:block></b></span>：<span id="accNo"></span>
							</td>
						</tr>
						<tr>
							<td colspan="2" >
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.bankPro'}"></th:block></b></span>：<span id="bankProCode"></span>&nbsp;<span id="bankProName"></span>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.lstBuy'}"></th:block></b></span>：
								<span id="lstBuyBrCd"></span><span id="lstBuyBrCdName"></span><br/><span id="lstBuyDt"></span>&nbsp;&nbsp;<span id="lstBuyCurCd"></span>&nbsp;<span id="lstBuyAmt"></span>
							</td>
						</tr>
						<tr id="hideSell">
							<td colspan="2">
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.lstSell'}"></th:block></b></span>：
								<span id="lstSellBrCd"></span><span id="lstSellBrCdName"></span><br/><span id="lstSellDt"></span>&nbsp;&nbsp;<span id="lstSellCurCd"></span>&nbsp;<span id="lstSellAmt"></span>
							</td>
						</tr>
						<tr id="hideInvAmt">
							<td colspan="2">
								<span style="color:brown;"><b><th:block th:text="#{'L260S01A.invAmt'}"></th:block></b></span>：<span id="invAmt"></span>
							</td>
						</tr>
					</table>
				</form>
			</div>

			<div id="query0320View" style="display:none;">
				<span class="text-red"><th:block th:text="#{'WORDING06'}">如為警示戶，請至BTT查0320</th:block></span>
				<br>
				<!--<br>
				<button type="button" id="query0060s"><th:block th:text="#{'btn.query0060s'}">查詢多筆0060</th:block></button>
				<br>-->
				<div id="query0320Grid" ></div>
			</div>

			<div id="datePeriod" style="display:none">
				<form id="datePeriodForm">
					<table class="tb1" width="95%">
						<tr class="show8250">
							<td class="hd1" align="right">
								<span class="text-red">＊</span>
								<th:block th:text="#{'btt.ioFlag'}">匯出入別</th:block>
							</td>
							<td>
								<label>
									<input name="ioFlag" type="radio" value="1" checked="checked"/><th:block th:text="#{'radio.io1'}">匯入</th:block>
								</label>
								<label>
									<input name="ioFlag" type="radio" value="2"/><th:block th:text="#{'radio.io2'}">匯出</th:block>
								</label>
							</td>
						</tr>
						<tr class="show8250">
							<td class="hd1" align="right">
								<span class="text-red">＊</span>
								<th:block th:text="#{'btt.remitType'}">Remittance Species匯款種類</th:block>
							</td>
							<td>
								<select id="remitType" name="remitType"></select>
							</td>
						</tr>
						<tr class="showDatePeriod">
							<td class="hd1" align="right">
								<span class="text-red">＊</span>
								<th:block th:text="#{'datePeriod'}">起迄日期區間</th:block>
							</td>
							<td>
								<input type="text" maxlength="10" size="8" class="date required" name="dateBgn" id="dateBgn">
								~<input type="text" maxlength="10" size="8" class="date required" name="dateEnd" id="dateEnd">
								<span class="text-red">
									<br/>
									<span id="msg8250"><th:block th:text="#{'btt.msg04'}">查詢期間：三個月內~今日</th:block></span>
									<span id="msg0060"><th:block th:text="#{'btt.msg05'}">查詢期間：六個月內~今日</th:block></span>
								</span>
							</td>
						</tr>
						<tr>
							<td class="hd1" align="right">
								<span class="text-red">＊</span>
								<th:block th:text="#{'btt.act'}">執行動作</th:block>
								<span class="text-red">
									<br/>
									<span id="msg8410"><th:block th:text="#{'btt.msg06'}">資料來源：BTT 8410 交易</th:block></span>
								</span>
							</td>
							<td>
								<label>
									<input name="actType" type="radio" value="1"/><th:block th:text="#{'radio.act1'}">僅查詢</th:block>
								</label>
								<label>
									<input name="actType" type="radio" value="2" checked="checked"/><th:block th:text="#{'radio.act2'}">查詢並產生附加檔案</th:block>
								</label>
							</td>
						</tr>
						<tr class="show8250">
							<td class="hd1" align="right">
								<th:block th:text="#{'btt.begAmt'}">起始金額</th:block>~<th:block th:text="#{'btt.endAmt'}">結束金額</th:block>
							</td>
							<td>
								<input type="text" id="begAmt" name="begAmt" size="14" maxlength="14" integer="11" fraction="2"/>
								~<input type="text" id="endAmt" name="endAmt" size="14" maxlength="14" integer="11" fraction="2"/>
							</td>
						</tr>
						<tr class="show8250">
							<td class="hd1" valign="top" align="right">
								<th:block th:text="#{'btt.bankId'}">對方行代號</th:block>
							</td>
							<td>
								<input type="text" id="bankId" name="bankId" size="7" maxlength="7" _requiredLength="7" class="alphanum"/>
								<span class="text-red">
									<br/>
									<th:block th:text="#{'btt.msg01'}">查詢匯入時為匯出行；匯出為解款行</th:block>
									<br/>
									<th:block th:text="#{'btt.msg02'}">若依總行查詢請輸入：總行代碼+XXXX，EX：008XXXX</th:block>
								</span>
							</td>
						</tr>
						<tr class="show8250">
							<td class="hd1" valign="top" align="right">
								<th:block th:text="#{'btt.ractNo'}">收款人帳號</th:block>
							</td>
							<td>
								<input type="text" id="ractNo" name="ractNo" size="14" maxlength="14" class="numText"/>
								<span class="text-red">
									<br/>
									<th:block th:text="#{'btt.msg03'}">若不足14碼，請前補零</th:block>
								</span>
							</td>
						</tr>
					</table>
				</form>
			</div>

			<div id="queryElandView" style="display:none;">
				<span class="text-red"><th:block th:text="#{'WORDING08'}">查詢內容限通知日期前一個月至後六個月內查詢成功之謄本，若無資料，請自行重新調閱謄本。</th:block></span>
				<br>
				<div id="queryElandGrid"></div>
			</div>

			<div id="elandDetailView" style="display:none;">
				<button type="button" id="downloadEland"><th:block th:text="#{'btn.toAttach'}">上傳至附件</th:block></button>
				<br>
				<div id="elandDetailGrid"></div>
			</div>

			<div id="queryRaspView" style="display:none;">
				<button type="button" id="downloadRasp"><th:block th:text="#{'btn.toAttach'}">上傳至附件</th:block></button>
				<br>
				<div id="queryRaspGrid"></div>
			</div>

			<div id="raspBox" style="display:none">
				<form id="raspForm">
					<table class="tb2" width="100%">
						<span id="oidL260S01C" style="display:none"></span>
						<tr>
							<td class="hd1"><th:block th:text="#{'L260S01C.collNo'}">擔保品編號</th:block></td>
							<td width="30%"><span id="collNo"></span></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L260S01C.queryRaspDate'}">查詢實價登錄日期</th:block></td>
							<td><span id="queryRaspDate"></span></td>
						</tr>
						<tr>
							<td class="hd1"><span class="color-red">＊</span><th:block th:text="#{'L260S01C.contractPrice'}">契約購價</th:block></td>
							<td><span id="contractPrice"></span></td>
						</tr>
						<tr>
							<td class="hd1"><span class="color-red">＊</span><th:block th:text="#{'L260S01C.cntrDate'}">買賣契約日期</th:block></td>
							<td><input type="text" id="cntrDate" name="cntrDate" class="date required"/></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L260S01C.raspStat'}">實價登錄狀態</th:block></td>
							<td><select id="raspStat" name="raspStat" class="required"></select></td>
						</tr>
						<tr id="trRaspWay">
							<td class="hd1"><th:block th:text="#{'L260S01C.raspWay'}">實價登錄因應方案</th:block></td>
							<td><select id="raspWay" name="raspWay" class="required"></select></td>
						</tr>
						<tr id="trRWayDt">
							<td class="hd1"><th:block th:text="#{'L260S01C.rWayDt'}">實價登錄因應方案完成日期</th:block></td>
							<td><input type="text" id="rWayDt" name="rWayDt" class="date required"/></td>
						</tr>
						<tr id="trRaspAmt">
							<td class="hd1"><th:block th:text="#{'L260S01C.raspAmt'}">實價登錄(佐證資料)價格</th:block><br>
								<span class="color-red">(查無價格請填0)</span></td>
							<td><input type="text" id="raspAmt" name="raspAmt" class="numeric required" integer="13"/></td>
						</tr>
						<tr id="trRaspDscr">
							<td class="hd1"><th:block th:text="#{'L260S01C.raspDscr'}">實價登錄說明</th:block></td>
							<td><input type="text" id="raspDscr" name="raspDscr" maxlengthC="29" size="30" maxlength="60" class="required"/></td>
						</tr>
						<tr>
							<td class="hd1"><th:block th:text="#{'L260S01C.fileName'}">附加檔案</th:block></td>
							<td>
								<a href="#" id="aFileLink"><span id="fileName" name="fileName"></span></a>
								<input type="hidden" id="fileOid" name="fileOid"/>
								<button type="button" id="uploadRaspFile"><span class="text-only"><th:block th:text="#{'button.upload'}">上傳</th:block></span></button>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<div id="qRaspFileGrid"></div>
							</td>
						</tr>
					</table>
				</form>
			</div>
		</th:block>
    </body>
</html>
