var LMS8300M01 = {
	openSingleMaintain: function(parm){
		ilog.debug("@LMS8300M01Page.js > open[parm.isNew="+(parm.isNew||"")+"][parm.mainId="+(parm.mainId||"")+"]"); 
		var thickBoxButtons = {};
		var docStatus = parm.docstatus;
		if(docStatus == "01O"){ //編制中
			thickBoxButtons[i18n.lms8300m01['saveData']] = function(){ //儲存
				var obj = {
					maintainType : 'S',
					actionType : 'save',
					isNew : parm.isNew,
					mainId : parm.mainId
				}
				LMS8300M01.saveData(obj);
	        }
			thickBoxButtons[i18n.lms8300m01['sendBoss']] = function(){ //呈主管覆核
				var obj = {
					maintainType : 'S',
					actionType : 'send',
					isNew : parm.isNew,
					mainId : parm.mainId
				}
				LMS8300M01.saveData(obj);
	        }
		}
		
		if(docStatus == "02O"){ //待覆核
			thickBoxButtons[i18n.lms8300m01['approved']] = function(){ //覆核
			var obj = {
				mainId : parm.mainId
			}
			LMS8300M01.btnCheckAction(obj);
	        }
		}
		
		thickBoxButtons[i18n.lms8300m01['close']] = function(){ //關閉
			$.thickbox.close();
			var mainId = parm.mainId;
			if(mainId != ""){ //有mainId的才會被鎖住
				$.ajax({ //仿 common.properties.js 的 onunload
		            handler: 'checkOpenerhandler', global:false, async: false, type: 'post',
		            data: {
		            	checkOpenerAction: 'ClosePage',
		                mainId: mainId
		            }
		        });
			}
        }
		
		
		$("#div_singleMaintain").thickbox({
            title: i18n.lms8300m01['L830M01B.SingleMaintain'], //單筆維護
            width: 950,
            height: 550,
            buttons: thickBoxButtons
        });
		
		$("#form_singleMaintain").find("#getCustName").click(function(){
			API.includeIdCes({
				defaultValue: $("#form_singleMaintain").find("#custId").val(),
				autoRespones: {
					custid: "custId",
					dupno: "dupNo",
					name: "custName"
				},
				fn: function(obj){
					obj.isNew = true;
					obj.mainId = "";
					obj.docstatus = parm.docstatus;
					LMS8300M01.SignleInquire(obj);
				}
	       
			});
		});
	},
	openBatchMaintain: function(parm){
		ilog.debug("@LMS8300M01Page.js > open[parm.isNew="+(parm.isNew||"")+"][parm.mainId="+(parm.mainId||"")+"]");  
		var thickBoxButtons = {};
		var docStatus = parm.docstatus;
		if(docStatus == "01O"){ //編制中
			thickBoxButtons[i18n.lms8300m01['saveData']] = function(){ //儲存
				var obj = {
					maintainType : 'B',
					actionType : 'save',
					isNew : parm.isNew,
					mainId : parm.mainId
				}
				LMS8300M01.saveData(obj);
	        }
			thickBoxButtons[i18n.lms8300m01['sendBoss']] = function(){ //呈主管覆核
				var obj = {
					maintainType : 'B',
					actionType : 'send',
					isNew : parm.isNew,
					mainId : parm.mainId
				}
				LMS8300M01.saveData(obj);
	        }
		}
		if(docStatus == "02O"){ //待覆核
			thickBoxButtons[i18n.lms8300m01['approved']] = function(){ //覆核
				var obj = {
					mainId : parm.mainId
				}
				LMS8300M01.btnCheckAction(parm);
	        }
		}
		
		thickBoxButtons[i18n.lms8300m01['close']] = function(){ //關閉
			$.thickbox.close();
			var mainId = parm.mainId;
			if(mainId != ""){ //有mainId的才會被鎖住
				$.ajax({ //仿 common.properties.js 的 onunload
		            handler: 'checkOpenerhandler', global:false, async: false, type: 'post',
		            data: {
		            	checkOpenerAction: 'ClosePage',
		                mainId: mainId
		            }
		        });
			}
        }
		$("#div_batchMaintain").thickbox({
            title: i18n.lms8300m01['L830M01B.BatchMaintain'], //批次維護
            width: 950,
            height: 550,
            buttons: thickBoxButtons
        });
		$("#form_batchMaintain").find("#btnBatchInquire").click(function(){ //批次維護 >> 搜尋原帳戶管理員
			if($("#form_batchMaintain").find("[id=origAOid]").val() == "" ){//
		        CommonAPI.showErrorMessage(i18n.lms8300v01['BatchMaintain.error.01']);
		        return;
		    }
			//開始搜尋
			LMS8300M01.BatchInquire(parm);
		});
	},
	setEmp: function(){
		$.ajax({
			type : "POST",
			handler : "lms8300m01formhandler",
			action: "getMegaEmpInfo",
			success:function(responseData){
				if(responseData.Success){ 
					var singleNewAOid = $("#singleNewAOid");
					var batchNewAOid = $("#batchNewAOid");
					singleNewAOid.setItems({
	                    item: responseData.bossListAO,
	                    space: true,
	                    format: "{value} {key}"
	                });
					batchNewAOid.setItems({
	                    item: responseData.bossListAO,
	                    space: true,
	                    format: "{value} {key}"
	                });
				}
			}
		});
	},SignleInquire: function(obj){
		if(obj){
			var docStatus = obj.docstatus;
			var action = 'queryLNF013';
			var select = true;
			if(docStatus == "01O"){
			}else{
				var action = 'queryL830m01A';
				var select = false;
			}
			//開始搜尋
			var _post_data = {
				formAction : action,
	    		maintainType : 'S',
	    		custId : obj.custid,
	    		dupNo : obj.dupno,
	    		isNew : obj.isNew,
	    		mainId : obj.mainId
	        };
			if($("#grid_singleMaintain.ui-jqgrid-btable").length >0){
	    		$("#grid_singleMaintain").jqGrid("setGridParam", {
	    			postData : _post_data,
	    			search : true
	    		}).trigger("reloadGrid");	        		
	    	}else{
	    		pageAction.singleGrid = $("#grid_singleMaintain").iGrid({
	        		handler : 'lms8300gridhandler',
	        		height : 160,
	        		rownumbers: true,
	        		sortname: 'printSeq',
	        		sortorder: 'asc',
	        		multiselect: select,
	        		postData : _post_data,			
	        		colModel : [ {
	        			colHeader: i18n.lms8300v01['LNF013.LNF013_BR_NO'],
	        			align: "left", width: 120, sortable: false, name: 'LNF013_BR_NO'
	        		}, {
	        			colHeader: i18n.lms8300v01['LNF013.LNF013_STAFF_NO'],
	        		    align: "left", width: 90, sortable: false, name: 'LNF013_STAFF_NO'
	        		}, {
	        			colHeader: i18n.lms8300v01['LNF013.LNF013_CUST_ID'],
	        		    align: "left", width: 110, sortable: false, name: 'LNF013_CUST_ID'
	        		}, {
	        			colHeader: i18n.lms8300v01['CUSTDATA.CNAME'],
	        		    align: "left", width: 110, sortable: false, name: 'CNAME'
	        		}, {
	        			name : 'CHFLAG',
	        			hidden : true
	        		} ], 
	        		 loadComplete: function(){
	     				// 已經存在的資料要幫他預設勾起來
	     				var length = pageAction.singleGrid.getGridParam("records"); 
	     				for(var i=1 ;i<=length ; i++){
	     					var singleData = pageAction.singleGrid.getRowData(i);
	     					if(singleData.CHFLAG=='Y'){
	     						pageAction.singleGrid.setSelection(i, true);
	     					}
	     				}
	     			}
	        	});
	    	}
		}
	},BatchInquire: function(obj){
		if(obj){
			var docStatus = obj.docstatus;
			var action = 'queryLNF013';
			var select = true;
			if(docStatus == "01O"){
			}else{
				var action = 'queryL830m01A';
				var select = false;
			}
			var _post_data = {
					formAction : action,
			    	maintainType : 'B',
			    	origAOId : $("#form_batchMaintain").find("[id=origAOid]").val(),
			    	isNew : obj.isNew,
			    	mainId : obj.mainId
			    };
				if($("#grid_batchMaintain.ui-jqgrid-btable").length >0){
					$("#grid_batchMaintain").jqGrid("setGridParam", {
						postData : _post_data,
						search : true
					}).trigger("reloadGrid");	        		
				}else{
					pageAction.batchGrid = $("#grid_batchMaintain").iGrid({
		    			handler : 'lms8300gridhandler',
		    			height : 300,
		    			rowNum: 1000,
		    			rownumbers: true,
		    			sortname: 'printSeq',
		    			sortorder: 'asc',
		    			multiselect: select,
		    			postData : _post_data,
		    			colModel : [ {
		    				colHeader: i18n.lms8300v01['LNF013.LNF013_BR_NO'],
		    				align: "left", width: 120, sortable: false, name: 'LNF013_BR_NO'
		    			}, {
		    				colHeader: i18n.lms8300v01['LNF013.LNF013_STAFF_NO'],
		    		        align: "left", width: 90, sortable: false, name: 'LNF013_STAFF_NO'
		    		    }, {
		    		    	colHeader: i18n.lms8300v01['LNF013.LNF013_CUST_ID'],
		    		    	align: "left", width: 110, sortable: false, name: 'LNF013_CUST_ID'
		    		    }, {
		    		    	colHeader: i18n.lms8300v01['CUSTDATA.CNAME'],
		    		    	align: "left", width: 110, sortable: false, name: 'CNAME'
		    		    }, {
		        			name : 'CHFLAG',
		        			hidden : true
		        		}],
		    		    loadComplete: function(){
		    				// 已經存在的資料要幫他預設勾起來
		    				var length = pageAction.batchGrid.getGridParam("records"); 
		    				for(var i=1 ;i<=length ; i++){
		    					var singleData = pageAction.batchGrid.getRowData(i);
		    					if(singleData.CHFLAG=='Y'){
		 							pageAction.batchGrid.setSelection(i, true);
		 						}
		    				}
		    			}
		    		});
				}
		}
		
	},saveData: function(obj){ //儲存
		if(obj.maintainType == 'S'){ //單筆維護
			if($('#singleNewAOid').val() == ""){
				CommonAPI.showErrorMessage(i18n.lms8300v01['Message.error.02']);
				return;
			}
			var datas = pageAction.singleGrid.getSelRowDatas();
			if (datas) {
				var rows = [];
		        for (var name in datas) {
		            var data = datas[name];
		            rows.push(data.LNF013_STAFF_NO+";"+data.LNF013_CUST_ID+";"+data.CNAME);
		        }		
				$.ajax({
					type : "POST",
					handler : "lms8300m01formhandler",
					data : {
						formAction : "saveAction",
						rows: rows,
						maintainType : obj.maintainType,
						actionType : obj.actionType,
						isNew : obj.isNew,
						mainId : obj.mainId,
	        			custId : $('#custId').val(),
	        			dupNo : $('#dupNo').val(),
	        			custName : $('#custName').val(),
	        			newAOid : $('#singleNewAOid').val()
					},
					success:function(responseData){
						if(responseData.Success){
							$.thickbox.close();
							pageAction.reloadGrid();
							CommonAPI.showMessage(i18n.lms8300v01['Message.success.01']);
						}else{
							CommonAPI.showErrorMessage(responseData.errorMessage);
						}
					}
				});
			}else{
				CommonAPI.showErrorMessage(i18n.lms8300v01['Message.error.01']);
				return;
			}
		}else if(obj.maintainType == 'B'){ //批次維護
			if($('#batchNewAOid').val() == ""){
				CommonAPI.showErrorMessage(i18n.lms8300v01['Message.error.02']);
				return;
			}
			var datas = pageAction.batchGrid.getSelRowDatas();
    		if (datas) {
    			var rows = [];
		        for (var name in datas) {
		            var data = datas[name];
		            rows.push(data.LNF013_STAFF_NO+";"+data.LNF013_CUST_ID+";"+data.CNAME);
		        }	
    			$.ajax({
					type : "POST",
					handler : "lms8300m01formhandler",
					data : {
						formAction : "saveAction",
						rows: rows,
						maintainType : obj.maintainType,
						actionType : obj.actionType,
						isNew : obj.isNew,
						mainId : obj.mainId,
						origAOId : $("#form_batchMaintain").find("[id=origAOid]").val(),
	        			newAOid : $('#batchNewAOid').val()
					},
					success:function(responseData){
						if(responseData.Success){
							$.thickbox.close();
							pageAction.reloadGrid();
							CommonAPI.showMessage(i18n.lms8300v01['Message.success.01']);
						}else{
							CommonAPI.showErrorMessage(responseData.errorMessage);
						}
					}
				});
			}else{
				CommonAPI.showErrorMessage(i18n.lms8300v01['Message.error.01']);
				return;
			}
		}
	},viewData: function(data){
		$.ajax({
			type : "POST",
			handler : "lms8300m01formhandler",
			action: "getViewData",
			data : data,
			success:function(responseData){
				if(responseData.maintainType == 'S'){ //單筆維護
					LMS8300M01.openSingleMaintain(responseData); //開啟視窗
					$("#form_signleMaintain").injectData(responseData); //將資料帶入畫面				
					LMS8300M01.SignleInquire(responseData);
					if(data.readonly){ //所有欄位鎖定
						$('#form_singleMaintain input').attr('disabled', true);
						$('#form_singleMaintain select').attr('disabled', true);
						$('#getCustName').hide();
					}else{ //部分欄位鎖定
						$('#form_singleMaintain input').attr('disabled', true);
						$('#getCustName').hide();
					}
				}else if(responseData.maintainType == 'B'){ //批次維護
					LMS8300M01.openBatchMaintain(responseData);	 //開啟視窗
					$("#form_batchMaintain").injectData(responseData); //將資料帶入畫面
					LMS8300M01.BatchInquire(responseData);
					if(data.readonly){ //所有欄位鎖定
						$('#form_batchMaintain input').attr('disabled', true);
						$('#form_batchMaintain select').attr('disabled', true);		
						$('#btnBatchInquire').hide();
					}else{ //部分欄位鎖定
						$('#form_batchMaintain input').attr('disabled', true);		
						$('#btnBatchInquire').hide();
					}
				}
			}
		});
	},load: function(data){
		ilog.debug("@CLS1131S01Page.js > load【ajax:loadData】begin{custId="+(data.custId||'')+", mainId="+(data.mainId||'')+"}");
		
	},btnCheckAction: function(obj){
		//跳彈窗
		$("#checkBoxSea").thickbox({
            title: i18n.lms8300m01['approved'], //覆核
            width: 200,
            height: 150,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var checked = $('input[name*=radioName]:checked').val();
                	$.ajax({
            			type : "POST",
            			handler : "lms8300m01formhandler",
            			action: "approveAction",
            			data : {  					
    						mainId : obj.mainId,
    						checked : $('input[name=qButtonTypeSea]:checked').val()
    					},
            			success:function(responseData){
            				$.thickbox.close();
            				$.thickbox.close(); //覆核會有兩層thickbox，所以要做兩次關閉視窗的動作
							pageAction.reloadGrid();
							CommonAPI.showMessage(i18n.lms8300v01['Message.success.01']);
//							window.close();
            			}
            		});
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
	}
	
};

$(function() {
//	var obj;//預留
//	pageAction.build(obj);
});
