package com.mega.eloan.lms.base.pages;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.utils.SpringContextHelper;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.base.pages.AbstractCapPage;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;

public abstract class AbstractFileDownloadPage extends AbstractCapPage {
	
	@Autowired
	protected HttpServletResponse response;

	private static ConcurrentMap<String, Object> runningUsers = new ConcurrentHashMap<String, Object>();

	protected String fileDownloadName;

	protected String serviceName;

	protected static String CONTENT_TTYPE_UNKONW = "application/octet-stream";
	protected static String CONTENT_TTYPE_PDF = "application/pdf";
	protected static String CONTENT_TTYPE_WORD = "application/msword";
	protected static String CONTENT_TTYPE_EXCEL = "application/vnd.ms-excel";
	protected static String CONTENT_TTYPE_JPEG = "image/jpeg";

	protected static Map<String, String> mimeTypeMap = new HashMap<String, String>();

	static {
		mimeTypeMap.put(".pdf", CONTENT_TTYPE_PDF);
		mimeTypeMap.put(".doc", CONTENT_TTYPE_WORD);
		mimeTypeMap.put(".xls", CONTENT_TTYPE_EXCEL);
		mimeTypeMap.put(".jpeg", CONTENT_TTYPE_JPEG);
		mimeTypeMap.put(".jpg", CONTENT_TTYPE_JPEG);
	}

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(AbstractFileDownloadPage.class);

	/**
	 * 檔案下載的名稱
	 * 
	 * @return 檔案名稱
	 */
	public abstract String getDownloadFileName();

	/**
	 * 實作檔案下載Service的名稱
	 * 
	 * @return 檔案下載Service的名稱
	 */
	public abstract String getFileDownloadServiceName();

	/**
	 * constructor
	 * 
	 * @param parameters
	 *            PageParameters object
	 */
	public AbstractFileDownloadPage() {
		super();
	}

	// UPGRADE: CES也是註解掉AbstractResourceStreamWriter的段落，待確認是否會影響報表產出功能
//	final class FileDownloadStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//
//		/**
//		 * constructor
//		 * 
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getContent(params));
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 * 
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return getFileContentType();
//		}
//
//	}

	/**
	 * 產生線上太多使用者的列印畫面
	 */
//	final class FileDownloadTooManyStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//
//		/**
//		 * constructor
//		 *
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadTooManyStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getTooManyContent());
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return "text/html";
//		}
//
//	}

	/**
	 * 產生重複user執行列印動作的畫面
	 */
//	final class FileDownloadDupStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//		MegaSSOUserDetails users = null;
//
//		/**
//		 * constructor
//		 *
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadDupStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		public FileDownloadDupStreamWriter(PageParameters params, MegaSSOUserDetails users) {
//			super();
//			this.params = params;
//			this.users = users;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getDupContent(this.users));
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return "text/html";
//		}
//
//	}

	public void execute(ModelMap model, PageParameters params) throws Exception {
		this.fileDownloadName = params.getString("fileDownloadName");
		this.serviceName = params.getString("serviceName");

		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();
		if (runningUsers.containsKey(users.getUserId())) {
			// 限制不能重覆列印
			// UPGRADE: CapByteArrayDownloadResult 需確認升級後的作法，是否可防止同一user重複列印
			// getRequestCycle().setRequestTarget(
			// new ResourceStreamRequestTarget(new FileDownloadDupStreamWriter(
			// params, users)));
			IResult result = new CapByteArrayDownloadResult(getContent(params), getFileContentType(), fileDownloadName, true);
			result.respondResult(response);
		}
		//else if (runningUsers.size() >= 15){
		//	// 限制線上列印人數只能有15人
		//	getRequestCycle().setRequestTarget(
		//			new ResourceStreamRequestTarget(new FileDownloadTooManyStreamWriter(
		//					params)));
		//}
		else {
			// getRequestCycle().setRequestTarget(
			// new ResourceStreamRequestTarget(new FileDownloadStreamWriter(
			// params)));
			IResult result = new CapByteArrayDownloadResult(getContent(params), getFileContentType(), fileDownloadName, true);
			result.respondResult(response);
		}

	}

	private byte[] getDupContent(MegaSSOUserDetails users) {
		Timestamp s = (Timestamp) runningUsers.get(users.getUserId());
		String now = s == null ? "" : s.toString();
		return ("<h1>您已重複執行列印，請確認上次列印動作已執行完畢後，再執行本次列印動作，如本狀況一直持續發生，請聯絡資訊處。</h1>" + "<br><h1>上次執行列印時間為：" + now +
				"</h1><br>")
				.getBytes();

	}


	private byte[] getTooManyContent() {
		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();
		return ("<h1>目前列印動作已超過系統上限，請稍後再試，如本狀況一直持續發生，請聯絡資訊處</h1>").getBytes();
	}

	/**
	 * 取得下載檔案內容
	 * 
	 * @param params
	 *            PageParameters object
	 * @return 檔案內容
	 * @throws Exception
	 * @throws IOException
	 * @throws ReportException
	 * @throws FileNotFoundException
	 */
	protected byte[] getContent(PageParameters params) throws Exception {
		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();
		byte[] bytes = null;
		ByteArrayOutputStream baos = null;
		
		// J-111-0011 新增於eloan授信管理系統(906稽核處)稽核工作底稿，增列「已核准額度明細表及案件簽報書」功能鍵
		// 如果有要一次開多視窗印PDF時可以使用這個參數chkAlreadyPrint : false，即可不受重複執行列印的檢核
		boolean chkAlreadyPrint = params.containsKey("chkAlreadyPrint")?
				params.getAsBoolean("chkAlreadyPrint") : true;
		try {
			if(chkAlreadyPrint){
				runningUsers.put(users.getUserId(), CapDate.getCurrentTimestamp());
			}
			bytes = ((FileDownloadService) SpringContextHelper.getBean(this
					.getFileDownloadServiceName())).getContent(params);
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(ex));
			rptGenerator.setVariableData(rptVariableMap);
			baos = (ByteArrayOutputStream) rptGenerator
					.generateExceptionReport(LMSUtil.getLocale());
			bytes = baos.toByteArray();
			// throw new CapException("[getContent]BeansException!",
			// ex.getCause(), this.getClass());
		} finally {
			if(chkAlreadyPrint){
				runningUsers.remove(users.getUserId());
			}
		}
		return bytes;
	}

	/**
	 * 取得檔案下載的content type
	 * 
	 * @return content type
	 */
	protected String getFileContentType() {
		String fn;
		LOGGER.info("fileDownloadName = " + this.fileDownloadName);
		if (!"".equals(Util.nullToSpace(this.fileDownloadName))) {
			fn = this.fileDownloadName;
		} else {
			fn = this.getDownloadFileName();
		}
		if (fn != null) {
			int pos = fn.lastIndexOf(".");
			if (pos != -1) {
				String contentType = mimeTypeMap.get(fn.substring(pos));
				if (contentType != null) {
					LOGGER.info("contentType = " + contentType);
					return contentType;
				}
			}
		}
		LOGGER.info("contentType = " + CONTENT_TTYPE_UNKONW);
		return CONTENT_TTYPE_UNKONW;
	}
}
