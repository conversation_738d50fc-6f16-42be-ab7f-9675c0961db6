package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <pre>
 * 海外貸後管理	ELF603-貸後追蹤分項紀錄檔
 * </pre>
 */
public interface ObsdbELF603Service {

	/**
	 * @param brNo
	 * 			  分行
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽報書核准時間
	 * @param cntrNo
	 *            額度序號
	 * @param seqno
	 *            序號

	 * @param esgtype
	 *            類別
	 * @param esgmodel
	 *            ESG模板
	 * @param tracond
	 *            起始追蹤日
	 * @param traprofik
	 *            追蹤週期
	 * @param tramonth
	 *            週期月
	 * @param content
	 *            內容
	 * @param updater
	 *            異動人員號碼
	 * @param updatetime
	 *            異動日期
	 */
	void insertForInside(String brNo, String uid, String apptime, String cntrNo, int seqno, 
			String esgtype, String esgmodel, String tracond, String traprofik,
			int tramonth, String content, String updater, BigDecimal updatetime);

	/**
	 * 刪除
	 * 
	 * @param brNo
	 * 			  分行
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽案核准時間
	 */
	void delByUidIdAppDate(String brNo, String uid, String apptime);
	
	/**
	 * 刪除
	 * 
	 * @param brNo
	 * 			  分行
	 * @param uid
	 *            簽報書mainid
	 * @param seqno
	 *            序號
	 */
	void delByUid(String brno, String uid);
	
	/**
	 * 刪除
	 * 
	 * @param brNo
	 * 			  分行
	 * @param uid
	 *            簽報書mainid
	 * @param apptime
	 *            簽案核准時間(要轉文字)
	 * @param cntrNo
	 *            額度序號
	 *            
	 */
	void delByUidIdAppDateCntrNo(String brNo, String uid, String apptime, String cntrNo);
	
	/**
	 * 取得ELF603
	 * @param brNo
	 * @param from602SUid
	 * @param from602SApptime
	 * @param cntrno
	 * @param from602SSeqno
	 * @return
	 */
	Map<String, Object> getElf603ByKey(String brNo, String from602SUid,
			String from602SApptime, String cntrno, String from602SSeqno);

}
