<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="innerPageBody">
		<script type="text/javascript">loadScript('pagejs/lms/LMS1505M01Page');</script>
		<div class="button-menu funcContainer" id="buttonPanel">
			<th:block th:if="${_btnDOC_EDITING_visible}">
				<button id="btnSave"> 
					<span class="ui-icon ui-icon-jcs-04"></span>
					<th:block th:text="#{'button.save'}"></th:block>
				</button>
			</th:block>	

			<button id="btnPrint" class="forview">
				<span class="ui-icon ui-icon-jcs-03"></span>
				<th:block th:text="#{'button.print'}"></th:block>
			</button>
			<button id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}"></th:block>
			</button>
		</div>

		<div class="tit2 color-black">
			<th:block th:text="#{'L150M01a.tit02'}"></th:block>：
		</div>

		<div class="tabs doc-tabs">
			<ul>
				<li><a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}"></th:block></b></a></li>
				<li><a href="#tab-02" goto="02"><b><th:block th:text="#{'L150M01a.tit02'}"></th:block></b></a></li>
			</ul>
			<div class="tabCtx-warp">
				<div th:id="${tabIdx}" th:insert="${panelName} :: ${panelFragmentName}"></div>
			</div>
		</div>
	</th:block>

</body>
</html>
