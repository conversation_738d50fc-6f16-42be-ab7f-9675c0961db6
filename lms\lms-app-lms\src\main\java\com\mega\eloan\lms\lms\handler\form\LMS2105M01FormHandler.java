/* 
 *  LMS2105FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS2105M01Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 修改資料特殊流程
 * </pre>
 * 
 * @since 2011/01/10
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/01/10,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2105m01formhandler")
@DomainClass(L210M01A.class)
public class LMS2105M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	LMS2105Service lms2105Service;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	DwLnquotovService dwLnquotovService;
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMSService lmsService;

	Properties pop = MessageBundleScriptCreator
			.getComponentResource(LMS2105M01Page.class);

	/**
	 * 查詢分行為海外還是國內
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryShareBrIdType(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String shareBrId = params.getString("shareBrId");
		String chgFlag = params.getString("chgFlag");

		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(shareBrId)
				.getBrNoFlag())) {
			result.set("type", TypCdEnum.海外.getCode());
		} else {
			result.set("type", "");
		}
		L210S01A l210s01a = lms2105Service
				.findL210s01saByMainIdAndChgFlagAndBrid(mainId, chgFlag,
						shareBrId);
		// 檢查目前分行是否已經登錄
		result.set("have", l210s01a != null ? true : false);
		return result;
	}

	/**
	 * 查詢L210M01A 修改資料特殊流程主檔
	 * 
	 * @param params
	 *            前端回傳資料
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL210m01a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class,
					oid);
			result = formatResultShow(l210m01a, page);
		}
		return result;
	}

	/**
	 * 刪除L210M01A 修改資料特殊流程主檔
	 * 
	 * @param params
	 *            前端回傳資料
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult deleteL210m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class,
					oid);
			if (l210m01a != null) {
				String srcMainId = l210m01a.getSrcMainId();
				L120M01A l120m01a = lmsService.findModelByMainId(
						L120M01A.class, srcMainId);
				l210m01a.setDeletedTime(CapDate.getCurrentTimestamp());

				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
				if (l120m01a != null) {
					l120m01a.setReEstFlag("");
				}
				lms2105Service.save(l210m01a);
				lmsService.save(l120m01a);

			}
		}
		return result;
	}

	/**
	 * 儲存L210M01A 修改資料特殊流程主檔
	 * 
	 * @param params
	 *            前端回傳資料
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveL210m01a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (!Util.isEmpty(mainOid)) {
			L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class,
					mainOid);
			l210m01a.setRandomCode(IDGenerator.getRandomCode());
			l210m01a.setApprId(user.getUserId());
			lms2105Service.save(l210m01a);

			result = formatResultShow(l210m01a, page);
			// 檢查是否有同行聯貸
			if (!UtilConstants.editDoc.caseType.同業聯貸.equals(l210m01a
					.getCaseType())) {
				List<L210S01A> l210s01asnew = lms2105Service
						.findL210s01saByMainIdAndChgFlag(l210m01a.getMainId(),
								UtilConstants.editDoc.chanFlag.變動後);
				// 檢查攤貸比率是否攤貸完畢
				result.set("l140m01eAmt",
						NumConverter.addComma(this.checkL140m01e(l210s01asnew)));

			}
		}
		Boolean showMsg = params.getAsBoolean("showMsg", true);
		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}

		return result;
	}

	/**
	 * 檢查攤貸比率是否攤貸完畢並將尚未攤貸餘額加到該額度明細表上的分行， 若沒有則跳出視窗選擇加在哪一筆上
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return BigDecimal 回傳剩餘金額
	 */
	private BigDecimal checkL140m01e(List<L210S01A> l210s01as) {
		// 是否有餘額剩下
		BigDecimal tempAmt = BigDecimal.ZERO;
		if (l210s01as != null && !l210s01as.isEmpty()) {
			BigDecimal total = l210s01as.get(0).getTotalAmt();
			// 加總分子
			BigDecimal shareRate1Count = BigDecimal.ZERO;
			// 分母
			BigDecimal shareRate2temp = BigDecimal.ZERO;
			// 計算總金額
			BigDecimal amtCount = BigDecimal.ZERO;

			for (L210S01A l140m01e : l210s01as) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e
						.getShareFlag())) {
					shareRate1Count = shareRate1Count.add(l140m01e
							.getShareRate1());
					shareRate2temp = l140m01e.getShareRate2();
				}
				amtCount = amtCount.add(l140m01e.getShareAmt());
			}
			// 檢核分子是否攤貸完
			if (shareRate1Count.compareTo(shareRate2temp) == 0) {
				// 當加總額小於現請額度
				if (amtCount.compareTo(total) == -1) {
					tempAmt = total.subtract(amtCount);
				}
			}
		}
		return tempAmt;

	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param meta
	 *            修改資料特殊流程主檔
	 * @param page
	 *            頁碼
	 * @return 前端顯示資料
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShow(L210M01A meta, Integer page)
			throws CapException {
		CapAjaxFormResult result = DataParse.toResult(meta);
		switch (page) {
		case 1:
			result.set("creator", lmsService.getUserName(meta.getCreator()));

			result.set("updater", lmsService.getUserName(meta.getUpdater()));
			result.set("managerId", Util.trim(meta.getManagerId()) + " "
					+ lmsService.getUserName(meta.getManagerId()));
			result.set(
					"bossId",
					Util.trim(meta.getBossId()) + " "
							+ lmsService.getUserName(meta.getBossId()));
			result.set("reCheckId", Util.trim(meta.getReCheckId()) + " "
					+ lmsService.getUserName(meta.getReCheckId()));
			result.set(
					"apprId",
					Util.trim(meta.getApprId()) + " "
							+ lmsService.getUserName(meta.getApprId()));

			result.set(
					"docStatus",
					getMessage(StrUtils.concat("docStatus.",
							meta.getDocStatus())));
			result.set(
					"ownBrId",
					StrUtils.concat(meta.getOwnBrId(), " ",
							branchService.getBranchName(meta.getOwnBrId())));
		case 2:
		case 3:
			break;
		case 4:
			List<L210S01A> l210s01as = lms2105Service
					.findL210s01saByMainIdAndChgFlag(meta.getMainId(),
							UtilConstants.editDoc.chanFlag.變動後);
			result.set("showWord", this.showL210S01AStr(l210s01as));
			List<L210S01B> l210s01bs = lms2105Service
					.findL210s01bsByMainIdAndChgFlag(meta.getMainId(),
							UtilConstants.editDoc.chanFlag.變動後);
			result.set("showTotalMoney", this.showTotalMoney(l210s01bs));

			break;
		}// close switch case
		String typcd = ("None"
				.equals(TypCdEnum.getEnum(meta.getTypCd()).name())) ? TypCdEnum.DBU
				.name() : this.getMessage("typCd." + meta.getTypCd());
		result.set("docStatusVal", meta.getDocStatus());

		result.set("typCd", typcd);
		result.set("showTypCd", typcd);
		result.set("showCustId", StrUtils.concat(meta.getCustId(), " ",
				meta.getDupNo(), " ", meta.getCustName()));
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(meta.getOid()));
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(meta.getMainId()));
		return result;

	}

	/**
	 * 查詢 攤貸比例 並告訴使用者目前剩餘可攤貸金額，和刪除以攤貸分行
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL210s01a(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String chgFlag = params.getString("chgFlag");

		BigDecimal ShareRate1Count = BigDecimal.ZERO;
		BigDecimal ShareAmtCount = BigDecimal.ZERO;
		Map<String, String> m = new TreeMap<String, String>();

		List<IBranch> bank = branchService.getAllBranch();
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		L210S01A l210s01a = null;
		if (!Util.isEmpty(oid)) {
			l210s01a = lms2105Service.findModelByOid(L210S01A.class, oid);
			CapAjaxFormResult formData = DataParse.toResult(l210s01a,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });
			result.set("formData", formData);
		}

		List<L210S01A> l210s01as = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId, chgFlag);
		if (!l210s01as.isEmpty()) {// 看目前筆數是否為空
			BigDecimal ShareRate2 = l210s01as.get(0).getShareRate2();
			BigDecimal TotalAmt = l210s01as.get(0).getTotalAmt();
			result.set("role", String.valueOf(ShareRate2));// 取得目前分母
			String shareFlag = l210s01as.get(0).getShareFlag();
			for (L210S01A f : l210s01as) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(f
						.getShareFlag())) {
					ShareRate1Count = ShareRate1Count.add(f.getShareRate1());
				}

				ShareAmtCount = ShareAmtCount.add(f.getShareAmt());
				if (m.containsKey(f.getShareBrId())) {
					if (!Util.isEmpty(oid)
							&& f.getShareBrId().equals(l210s01a.getShareBrId())) {
						continue;
					}
					m.remove(f.getShareBrId());
				}
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(shareFlag)) {
				ShareRate2 = ShareRate2.subtract(ShareRate1Count);
			}

			TotalAmt = TotalAmt.subtract(ShareAmtCount);
			result.set("tips", String.valueOf(ShareRate2));// 增加目前分子剩下提示
			result.set("tips2", String.valueOf(TotalAmt));// 增加目前可攤貸餘額提示
			result.set("shareFlag", shareFlag);
		} else {
			result.set("role", "0");
		}
		result.set("item", new CapAjaxFormResult(m));
		List<L210S01A> l210s01asfor1 = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動前);
		BigDecimal total = BigDecimal.ZERO;
		if (!l210s01asfor1.isEmpty()) {
			total = l210s01asfor1.get(0).getTotalAmt();
		}
		result.set("totalAmt", NumConverter.addComma(total));
		return result;

	}// close queryShareItem

	/**
	 * 刪除 L210S01A 自行聯貸攤貸比率檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL210s01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		if (oids.length > 0) {
			lms2105Service.deleteListByOid(L210S01A.class, oids);
		}
		List<L210S01A> l210s01as = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		result.set("desc", this.showL210S01AStr(l210s01as));
		return result;

	}

	/**
	 * 刪除 L210S01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL210s01b(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		if (oids.length > 0) {
			lms2105Service.deleteListByOid(L210S01B.class, oids);
		}
		List<L210S01B> l210s01bs = lms2105Service
				.findL210s01bsByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		result.set("showTotalMoney", this.showTotalMoney(l210s01bs));
		return result;

	}

	/**
	 * 查詢L210S01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL210s01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		L210S01B l210s01b = lms2105Service.findModelByOid(L210S01B.class, oid);
		if (!Util.isEmpty(l210s01b)) {
			result = DataParse.toResult(l210s01b);
		}
		return result;
	}

	/**
	 * 儲存L210S01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL210s01b(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formL210s01b = params.getString("L210S01BForm");
		JSONObject jsonL210s01b = JSONObject.fromObject(formL210s01b);

		L210S01B l210s01b = lms2105Service.findModelByOid(L210S01B.class, oid);

		if (Util.isEmpty(l210s01b)) {
			l210s01b = new L210S01B();
			DataParse.toBean(jsonL210s01b, l210s01b);
			l210s01b.setSeq(lms2105Service.findL210s01bMaxSeq(mainId));
			l210s01b.setCreator(user.getUserId());
			l210s01b.setCreateTime(CapDate.getCurrentTimestamp());
			l210s01b.setChgFlag(UtilConstants.editDoc.chanFlag.變動後);
			l210s01b.setMainId(mainId);
		} else {
			DataParse.toBean(jsonL210s01b, l210s01b);
		}
		lms2105Service.save(l210s01b);
		List<L210S01B> l210s01bs = lms2105Service
				.findL210s01bsByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		result.set("showTotalMoney", this.showTotalMoney(l210s01bs));

		return result;

	}

	/**
	 * 儲存攤貸比率 自行聯貸攤貸比率檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL210s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String forml210s01a = params.getString("L210S01AForm"); // 指定的form
		JSONObject jsonl210s01a = JSONObject.fromObject(forml210s01a);
		String shareBrId = jsonl210s01a.getString("shareBrId");

		L210M01A l210m01a = lms2105Service.findL210M01AByMainId(mainId);
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l210m01a
				.getSrcMainId());
		L210S01A l210s01a = lms2105Service
				.findL210s01saByMainIdAndChgFlagAndBrid(mainId,
						UtilConstants.editDoc.chanFlag.變動後, shareBrId);

		// 計算分母
		BigDecimal shareRate1Count = BigDecimal.ZERO;

		List<L210S01A> l210s01as = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		// 用來放額度序號
		Map<String, Object> cntrNo = null;
		for (L210S01A modele : l210s01as) {
			// 如果已存在這間分行不將金額納入計算，以免會重複計算到
			if (shareBrId.equals(modele.getShareBrId())) {
				continue;
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(modele
					.getShareFlag())) {
				shareRate1Count = shareRate1Count.add(modele.getShareRate1());
			}

		}
		if (l210s01a == null) {
			// 判斷額度序號給號
			String type = params.getString("type");
			l210s01a = new L210S01A();
			l210s01a.setMainId(mainId);
			l210s01a.setChgFlag(UtilConstants.editDoc.chanFlag.變動後);
			DataParse.toBean(jsonl210s01a, l210s01a);
			String numberType = params.getString("numberType");
			String originalCntrNo = params.getString("originalCntrNo");
			// 2012-04-10_當該分行已有自己的額度序號，就不需重新起號
			String cntrNoTemp = Util.trim(l210m01a.getCntrNo());
			// 目前的程式規格只for海外
			// 當額度序號等於空 或 額度明細表額度序號 前三碼不等於目前攤貸行，給新號
			if (Util.isEmpty(cntrNoTemp)
					|| !shareBrId.equals(cntrNoTemp.substring(0, 3))) {

				if ("1".equals(numberType)) {
					// 是否為遠匯 ，是 =X 、否=0
					String selectBrNo = params.getString("selectBrNo");
					// 是否為遠匯 ，是 =X 、否=0
					String classCd = "0";
					if (LMSUtil.isNeedX(l140m01a)) {
						classCd = "X";
					}
					String ownBrName = branchService.getBranchName(selectBrNo);
					if (ownBrName == null) {
						// EFD0037=WARN|找不到該分行代號之分行名稱，請洽資訊室!!|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0037"), getClass());
					}
					cntrNo = lms1405Service.queryLnsp0050(
							selectBrNo.toUpperCase(), type, classCd);
					if (!"YES".equals(cntrNo.get("chkFlag"))) {
						HashMap<String, String> msg = new HashMap<String, String>();
						msg.put("msg", Util.trim((String) cntrNo.get("errMsg")));
						// 錯誤訊息
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
								getClass());
					}
					cntrNoTemp = (String) cntrNo.get("cntrNo");
				} else {
					cntrNoTemp = originalCntrNo;
				}

			}
			l210s01a.setShareNo(cntrNoTemp);
			// 再存的時候把是國內國外的flag 加進去
			l210s01a.setFlag(branchService.getBranch(shareBrId).getBrNoFlag());
			l210s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l210s01a.setCreator(user.getUserId());
			Long shareAmt = Util.parseLong(NumConverter
					.delCommaString(jsonl210s01a.getString("shareAmt")));
			l210s01a.setShareAmt(new BigDecimal(shareAmt));
		} else {
			DataParse.toBean(jsonl210s01a, l210s01a);
		}
		// 當如果是以比例計算就判斷比例
		if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l210s01a
				.getShareFlag())) {
			// 檢核分子總和是否大於分母
			if (shareRate1Count.add(l210s01a.getShareRate1()).compareTo(
					l210s01a.getShareRate2()) == 1) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				// L140M01e.lmterror 分子總和大於分母無法儲存
				String msg = prop.getProperty("L140M01e.lmterror");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		} else {
			l210s01a.setShareRate1(null);
			l210s01a.setShareRate2(null);
		}
		List<L210S01A> l210s01asfor1 = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動前);

		BigDecimal total = BigDecimal.ZERO;
		if (!l210s01asfor1.isEmpty()) {
			total = l210s01asfor1.get(0).getTotalAmt();

		}
		l210s01a.setTotalAmt(total);
		// 當目前儲存的分行為國內分行自動帶入其他國內的額度序號
		if (!UtilConstants.BrNoType.國外.equals(l210s01a.getFlag())) {
			for (L210S01A oldL210S01A : l210s01asfor1) {
				if (!UtilConstants.BrNoType.國外.equals(oldL210S01A.getFlag())) {
					l210s01a.setShareNo(oldL210S01A.getShareNo());
				}
			}
		}
		lms2105Service.save(l210s01a);
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("desc", this.showL210S01AStr(l210s01as));
		return result;// 傳回執行這個動作的AjAX
	}// ;

	/**
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	// @DomainAuth(AuthType.Modify + AuthType.Accept)
	@DomainAuth(value = AuthType.Modify + AuthType.Accept, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {

		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class, oid);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String account = Util.trim(params.getString("account"));
		if (!Util.isEmpty(account)) {
			String manager = Util.trim(params.getString("manager"));
			l210m01a.setBossId(account);
			l210m01a.setManagerId(manager);
		}

		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l210m01a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			l210m01a.setApprover(user.getUserId());
			l210m01a.setReCheckId(user.getUserId());
		}

		if (!Util.isEmpty(l210m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回不檢查
					if (!"back".equals(params.getString("flowAction"))) {
						if (user.getUserId().equals(l210m01a.getApprId())) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}
					}

				} else {
					l210m01a.setApprId(user.getUserId());
				}
				lms2105Service.flowAction(l210m01a.getOid(), l210m01a,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowException t1) {
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class, oid);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS2105M01Page.class);
		if (l210m01a != null) {
			BigDecimal countAmt = BigDecimal.ZERO;
			BigDecimal totalAmt = BigDecimal.ZERO;
			// 分子加總
			BigDecimal shareRateCount = BigDecimal.ZERO;
			// 暫存分母
			BigDecimal shareRate = BigDecimal.ZERO;
			String mainId = l210m01a.getMainId();

			if (UtilConstants.editDoc.caseType.同行聯貸.equals(l210m01a
					.getCaseType())
					|| UtilConstants.editDoc.caseType.同業聯貸和同行聯貸.equals(l210m01a
							.getCaseType())) {
				List<L210S01A> l210s01asnew = lms2105Service
						.findL210s01saByMainIdAndChgFlag(mainId,
								UtilConstants.editDoc.chanFlag.變動後);

				if (l210s01asnew.isEmpty()) {
					// L210M01A.message10=同行攤貸比率一覽表不得為空
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.注意,
							prop.getProperty("L210M01A.message10")), getClass());
				}
				for (L210S01A l210s01a : l210s01asnew) {
					totalAmt = l210s01a.getTotalAmt();
					countAmt = countAmt.add(l210s01a.getShareAmt());
					// 以比例計算的時候在要判斷
					if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l210s01a
							.getShareFlag())) {
						shareRateCount = shareRateCount.add(l210s01a
								.getShareRate1());
						shareRate = l210s01a.getShareRate2();
					}
				}

				if (shareRateCount.compareTo(shareRate) != 0) {
					// L210M01A.message06=同行攤貸比率一覽表分子總合不等於分母
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.注意,
							prop.getProperty("L210M01A.message06")), getClass());
				}

				if (totalAmt.compareTo(countAmt) != 0) {
					// L210M01A.message07=同行攤貸比率一覽表攤貸總合不等於總額
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.注意,
							prop.getProperty("L210M01A.message07")), getClass());
				}

			}
			if (UtilConstants.editDoc.caseType.同業聯貸.equals(l210m01a
					.getCaseType())
					|| UtilConstants.editDoc.caseType.同業聯貸和同行聯貸.equals(l210m01a
							.getCaseType())) {
				totalAmt = l210m01a.getLtAmt();
				List<L210S01B> L210S01bsnew = lms2105Service
						.findL210s01bsByMainIdAndChgFlag(mainId,
								UtilConstants.editDoc.chanFlag.變動後);

				if (L210S01bsnew.isEmpty()) {
					// L210M01A.message09=聯貸案參貸比率一覽表不得為空
					throw new CapMessageException(RespMsgHelper.getMessage(
							UtilConstants.AJAX_RSP_MSG.注意,
							prop.getProperty("L210M01A.message09")), getClass());
				}
				countAmt = BigDecimal.ZERO;
				for (L210S01B l210s01a : L210S01bsnew) {
					countAmt = countAmt.add(l210s01a.getSlAmt());
				}
				// 2013-03-09_建霖(同業聯貸比率異動加總後允許與動審表上總額度不同)
				// if (totalAmt.compareTo(countAmt) != 0) {
				// // L210M01A.message08=聯貸案參貸比率一覽表總合不等於總額
				// throw new CapMessageException(RespMsgHelper.getMessage(
				// parent, UtilConstants.AJAX_RSP_MSG.注意,
				// prop.getProperty("L210M01A.message08")), getClass());
				// }
			}
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 檢查原案額度序號
	 * 
	 * @param params
	 *            <pre>
	 *            {String}cntrNo 額度序號 
	 *            {String}justSave  1.為查詢舊額度序號2.儲存預約額度序號 3,聯行攤貸比例  
	 *            {String}oid額度明細表文件編號
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkCntrno(PageParameters params)
			throws CapException {
		String cntrNo = params.getString("cntrNo", "");
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L210M01A l210m01a = lms2105Service.findModelByOid(L210M01A.class,
				mainOid);
		if (Util.isEmpty(cntrNo)) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		if (l210m01a == null) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("ownBrName",
				branchService.getBranchName(cntrNo.substring(0, 3)));

		String custId = l210m01a.getCustId();
		String dupNo = l210m01a.getDupNo();

		// 2012_05_22_ 建霖說看已核准簽報書的額度明細表額度序號與攤貸行(l140m01e)的
		List<L140M01A> l140m01as = lms1405Service.findL140m01aBycntrNo(cntrNo,
				custId, dupNo);
		int countE = eloandbBASEService.findL140M01EByCustIdAndDupNoAndCntrno(
				custId, dupNo, cntrNo);
		// 在額度明細表查詢不到時在到帳務系統內查詢
		if (l140m01as.isEmpty() && countE == 0) {
			int count = 0;
			// 海外查 DW_ASLNQUOT
			if (TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
				count = dwLnquotovService.findByCntrNoAndCustIdAndDupNo(cntrNo,
						custId, dupNo);
				// 以下為測試用資料
				// count = dwASLNQUOTService.findByCntrNoAndCustIdAndDupNo(
				// "238109800150", "A100008276", "0");

			} else {
				custId = Util.addSpaceWithValue(custId, 10);
				// X為 遠匯
				if ("X".equals(cntrNo.substring(7, 8))) {
					count = misdbBASEService.findLNF197BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				} else {
					count = misdbBASEService.findMISLN20BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				}
			}
			if (count == 0) {
				// L140M01a.message69=此舊額度序號：{0} 並未存在於帳務系統, 請確認後再輸入!
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				String msg = MessageFormat.format(
						prop.getProperty("L140M01a.message69"), cntrNo);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		return result;
	}

	/**
	 * 儲存攤貸剩餘餘額
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140m01eAmt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String amt = NumConverter.delCommaString(params.getString("amt"));
		L210S01A l210s01a = lms2105Service.findModelByOid(L210S01A.class, oid);
		if (l210s01a != null) {
			l210s01a.setShareAmt(l210s01a.getShareAmt()
					.add(new BigDecimal(amt)));
			lms2105Service.save(l210s01a);
			List<L210S01A> l210s01as = lms2105Service
					.findL210s01saByMainIdAndChgFlag(l210s01a.getMainId(),
							UtilConstants.editDoc.chanFlag.變動後);
			result.set("desc", this.showL210S01AStr(l210s01as));
		}

		return result;
	}// ;

	/**
	 * 查詢變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryChangesShareRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		HashMap<String, String> msg = new HashMap<String, String>();
		List<L210S01A> l210s01as = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		if (l210s01as.isEmpty()) {
			// L140M01a.message79=請先登錄聯行攤貸比例
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1405S02Panel.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01a.message79")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		if (UtilConstants.Cntrdoc.shareType.以金額計算.equals(l210s01as.get(0)
				.getShareFlag())) {
			// L140M01a.message80=以比例計算時才可使用此功能
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1405S02Panel.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01a.message80")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		return result;
	}

	/**
	 * 儲存變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveChangesShareRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID, "");
		List<L210S01A> l210s01as = lms2105Service
				.findL210s01saByMainIdAndChgFlag(mainId,
						UtilConstants.editDoc.chanFlag.變動後);
		String shareRate = NumConverter.delCommaString(params.getString(
				"shareRate", ""));
		// 原始分母
		BigDecimal orgRate = l210s01as.get(0).getShareRate2();
		// 新分母
		BigDecimal newRate = new BigDecimal(shareRate);
		// 當新分母與舊分母不同 才做更新
		if (orgRate.compareTo(newRate) != 0) {
			for (L210S01A l210s01a : l210s01as) {
				l210s01a.setShareRate2(newRate);
				// 重算攤貸額度金額 攤貸總額 * 分子 /　分母
				BigDecimal shareAmt = Arithmetic.div_floor(l210s01a
						.getTotalAmt().multiply(l210s01a.getShareRate1()),
						l210s01a.getShareRate2(), 0);
				l210s01a.setShareAmt(shareAmt);
			}
			lms2105Service.saveL210s01as(l210s01as);
		}
		result.set("desc", this.showL210S01AStr(l210s01as));
		return result;
	}

	/**
	 * 組成同行聯貸文字
	 * 
	 * @param l210s01as
	 * @return
	 */
	private String showL210S01AStr(List<L210S01A> l210s01as) {
		StringBuffer temp = new StringBuffer();
		String MARKCOLON = "：";
		String MARKVIRGULE = "/";
		String MARKCOMMA = "，";
		for (L210S01A l210s01a : l210s01as) {
			temp.append(temp.length() > 0 ? MARKCOMMA : "");
			temp.append(Util.trim(branchService.getBranchName(l210s01a
					.getShareBrId())));
			temp.append(MARKCOLON);
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l210s01a
					.getShareFlag())) {
				temp.append(l210s01a.getShareRate1() + MARKVIRGULE);
				temp.append(l210s01a.getShareRate2());
			} else {
				temp.append(NumConverter.addComma(l210s01a.getShareAmt()));
				// L210M01A.s04message09=元
				temp.append(pop.get("L210M01A.s04message09"));
			}
			// 當額度序號與額度明細表不相同或者為國外分行時列印出額度序號
			if (UtilConstants.BrNoType.國外.equals(l210s01a.getFlag())) {
				temp.append("(");
				temp.append(Util.trim(l210s01a.getShareNo()));
				temp.append(")");
			}

		}
		if (temp.length() > 0) {
			// L210M01A.s04message08=聯行攤貸比率
			temp.insert(0, "※" + pop.getProperty("L210M01A.s04message08")
					+ MARKCOLON);
			temp.append("。");
		}
		return temp.toString();
	}

	/**
	 * 組成同行聯貸加總金額
	 * 
	 * @param l210s01bs
	 * @return
	 */
	private String showTotalMoney(List<L210S01B> l210s01bs) {
		BigDecimal total = BigDecimal.ZERO;
		for (L210S01B l210s01b : l210s01bs) {
			total = total.add(Util.parseBigDecimal(l210s01b.getSlAmt()));
		}
		return NumConverter.addComma(total);

	}
}
