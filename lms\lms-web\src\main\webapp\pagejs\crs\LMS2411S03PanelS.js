var initDfd = initDfd || new $.Deferred();
initDfd.done(function(json){
	build_c241m01c(json);
	//在 build 完頁面後,若 M01 是 lockDoc, 也跟著 lock
	if(json['initControl_lockDoc']){
		$("#tabForm").lockDoc();
	}
	//===================================================
	$("#btn_c241m01c_latestVer").click(function(){
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				$.ajax({
		           type: "POST",
		           handler: _handler,
		           data: {
		               formAction: "c241m01c_latestVer",
		               mainOid: $("#mainOid").val()
		           },
		           success: function(responseData){
		        	   	var page = responseJSON.page;
	        	   		var tData = {'mainDocStatus': $("#mainDocStatus").val()
   		        	   		, 'mainId': $("#mainId").val()
   		        	   		, 'mainOid': $("#mainOid").val()
		        	   	};
		        	   	$.form.submit({ url: page , data: tData });
		           }
		       });
			}
        });
	});
	//===================================================
	$("#btn_c241m01c_defaultVal").click(function(){
		$.ajax({
           type: "POST",
           handler: _handler,
           data: {
               formAction: "c241m01c_defaultVal",
               mainOid: $("#mainOid").val()
           },
           success: function(responseData){
           	var tabForm = $("#tabForm");
           	
           	var map = responseData.defVal;
           	$.each(map, function(def_key, def_val) {
           		$("[name=_chkResult_"+def_key+"][value="+def_val+"]").attr("checked", "checked");
           	});
           				
           }
       });
	});	
	//===================================================
	function build_c241m01c(json){
		{
			
			var $c241m01c_content = $("#c241m01c_content");
			$c241m01c_content.find("tr.tr_itemType").css("background-color","lightgray");
			
			$.each(['S_ITEM'], function(idx_itemType, itemType) {
				
				var arr = json.c241m01c_list[itemType];
				var c241m01c_chkText_maxlength = json.c241m01c_chkText_maxlength; 
				var	c241m01c_chkText_maxlengthC = json.c241m01c_chkText_maxlengthC;
				if(arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						//LMS2411M01Formhandler :: setC241M01C
						//每一個 itemType 包含的項目
					
						var $tr = $c241m01c_content.find("tr#tr_"+jsonItem.itemNo);
						//填入 tr 的 attr
						if(true){
							$tr.attr("c241m01c_oid", "c241m01c_"+jsonItem.oid);
							$tr.attr("c241m01c_itemSeq", jsonItem.itemSeq);
						}
						
						//覆審條文
						if(true){
							$tr.find("td.td_chkItem").append(jsonItem.chkItem+"<div class='item' style='display:none'>"+jsonItem.itemNo+":"+jsonItem.chkResult+"</div>");
							
						}							
						
						//覆審結果 	
						//[]是 []否 [] 一
						if(true){
							var _name_chkResult = "_chkResult_"+(jsonItem.itemNo);
							var fmt = jsonItem._chkResult_fmt;
							var dyna = []
							build_radio(dyna, _name_chkResult, fmt, jsonItem.chkResult);	
							$tr.find("td.td_chkResult").append(dyna.join(""));				
							
						}
						
						//文字欄位
						if(true){
							var _name_chkText = "_chkText_"+(jsonItem.itemNo);								
							$tr.find("td.td_memo").append("<textarea name='"+_name_chkText+"' id='"+_name_chkText+"' "
									+"maxlength='"+c241m01c_chkText_maxlength+"' maxlengthC='"+c241m01c_chkText_maxlengthC+"' "
									+"class='my_taClass' rows='1' ></textarea>");
								
						}
						
					});	
				}else{
					//itemType 下的項目若為0,不顯示
				}
			});

			$.each(['S_ITEM'], function(idx_itemType, itemType) {				
				var arr = json.c241m01c_list[itemType];
				if(arr.length>0){
					$.each(arr, function(idx, jsonItem) {
						var _name_chkText = "_chkText_"+(jsonItem.itemNo);
						$("textarea.my_taClass[name="+_name_chkText+"]").val(jsonItem.chkText);
					});
				}
			});
		}
		
		
		//=====================
		{//特別處理 textarea 的高度
			$("textarea.my_taClass").css('overflow-y','hidden').css('width', '150px').bind("keyup focus", expandText );
	        //在初始化時,若 textarea 有N列,展開
	        $.each( $("textarea.my_taClass"), function (idx, element) {
	            if ( $(element).val().length > 0) {                
	                $(element).trigger('focus');            
	            }            
	        });	
		}
	}	
	
	function build_radio(dyna, radioName, srcStr, chooseVal){
		$.each(srcStr.split("|"), function(idx, val_item) {
			var radioVal = val_item.substring(0, 1);
			var attr = (chooseVal==radioVal)?" checked ":"";
			dyna.push("<label><input name='"+radioName+"' id='"+(radioName+"_v_"+radioVal)+"' type='radio' value='"+radioVal +"' "+attr+">"+i18n.lms2411m01[("label."+val_item)]+"</label>");
		});
	}

});

//http://perplexed.co.uk/596_expanding_textarea_as_you_type.htm
var expandText = function(){    
    var el = this;
    //if(el.tagName!=="textarea"){return;}
    // has the scroll height changed?, we do this because we can successfully change the height
    var prvLen = el.preValueLength;
    el.preValueLength = el.value.length;
    if(el.scrollHeight===el.prvScrollHeight&&el.prvOffsetHeight===el.offsetHeight&&el.value.length>=prvLen){    	
        return;
    }
    while(el.rows>1 && el.scrollHeight<el.offsetHeight){
        el.rows--;
    }
    var h=0;
    while(el.scrollHeight > el.offsetHeight && h!==el.offsetHeight && (h=el.offsetHeight) ){
        el.rows++;
    }    
    el.rows++;    
    el.prvScrollHeight = el.scrollHeight;
    el.prvOffsetHeight = el.offsetHeight;     
};

function showItemNo(){
	$(".item").show().css("color","blue");
}