#grid
C123M01A.custId=Principal <PERSON><PERSON><PERSON>'s Mega ID(UBN)
C123M01A.custName=Principal <PERSON><PERSON>er
C123M01A.caseNo=Rating Doc. No.
l120s01a.custid=Unified Business Number
tab02.btnImport=Import
l120s01a.custname=Name
l120s01a.other16=UBN input Category Description: <br/>Natural persons-<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Taiwan ID No.(Ex:A123456789)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1Ministry of Interior UBN(Ex: GC00367938)<br/>(2) Non Taiwan ID-AD date of birth + The first two characters in the English name(Ex:20120202DU)<br/><br/>Non-Natural persons -<br/>(1)&nbsp;Taiwan ID-&nbsp;\u24d0Number issued by Ministry of Economic Affairs/Revenue Service Office (Ex:********)<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\u24d1In the Bank Coding Mega&nbsp;Id(Ex:AUZ0034022)<br/>(2)&nbsp;Non Taiwan ID-Query by English company before take a number
alert.01=UBN, Name Select Alternative input or import
alert.02=Please input UBN or Name!