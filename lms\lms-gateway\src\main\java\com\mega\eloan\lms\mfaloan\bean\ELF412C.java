package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 覆審控制檔 **/
public class ELF412C extends GenericBean {
	/**
	 * 分行代號
	 */
	@Column(name = "ELF412C_BRANCH", length = 3, columnDefinition = "CHAR(3)", nullable = false, unique = true)
	private String elf412c_branch;

	/**
	 * 借款人統一編號
	 */
	@Column(name = "ELF412C_CUSTID", length = 10, columnDefinition = "CHAR(10)", nullable = false, unique = true)
	private String elf412c_custId;

	/**
	 * 重複序號
	 */
	@Column(name = "ELF412C_DUPNO", length = 1, columnDefinition = "CHAR(1)", nullable = false, unique = true)
	private String elf412c_dupNo;

	/**
	 * 主要授信戶 <br/>
	 * 1.新戶增額由ELF411 <br/>
	 * 2.舊案由STORE PROCEDURE
	 */
	@Column(name = "ELF412C_MAINCUST", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_mainCust;

	/**
	 * 資信評等類別 <br/>
	 * B=DBU、大型企業、L=DBU中小型企業、O=OBU
	 */
	@Column(name = "ELF412C_CRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_crdType;

	/**
	 * 資信評等 <br/>
	 * MIS.CRDTTBL
	 */
	@Column(name = "ELF412C_CRDTTBL", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_crdtTbl;

	/**
	 * 信用模型評等類別 <br/>
	 * 1:DBU大型企業 2:DBU中型企業 3:DBU中小型企業 4:DBU不動產有建案規劃 5:DBU專案融資 6:DBU本國證券公司
	 * 8:DBU投資公司一般情況 9:DBU租賃公司 A:DBU一案建商 B:DBU非一案建商(擔保/土融) C:DBU非一案建商(無擔)
	 * D:投資公司情況一 E:投資公司情況二 F:境外船舶與航空器 G:境外貿易型控股型 H:國金部實體營運企業 I:國金部租賃業
	 * J:信託、基金及其他金融工具 K:澳洲不動產租售業
	 * 
	 * <br/>
	 * 參考 select substr(CODEVALUE, 2, 3) as mowType,CODEDESC from com.bcodetype
	 * where codetype='CRDType' and CODEVALUE like 'M%' and LOCALE='zh_TW' <br/>
	 * 參考 UtilConstants.mowType
	 */
	@Column(name = "ELF412C_MOWTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_mowType;

	/**
	 * 信用模型評等 <br/>
	 * MIS.MOWTBL1
	 */
	@Column(name = "ELF412C_MOWTBL1", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_mowTbl1;

	/**
	 * 上上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_LLRDATE", columnDefinition = "DATE")
	private Date elf412c_llrDate;

	/**
	 * 上次覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_LRDATE", columnDefinition = "DATE")
	private Date elf412c_lrDate;

	/**
	 * 異常週期 <br/>
	 * A.一年覆審一次。 <br/>
	 * B.半年覆審一次(主要授信戶並符合信評條件)。 <br/>
	 * C.新戶/增額戶。 <br/>
	 * D.異常戶已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * E.首次通報之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * F.會計師出具保留意見已三個月覆審過- 爾後半年覆審一次。 <br/>
	 * G.首次通報有會計師出具保留意見之異常戶。（必需在首次通報日後3月內覆審） <br/>
	 * H.主管機關指定覆審案件。
	 */
	@Column(name = "ELF412C_RCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_rckdLine;

	/**
	 * 原始週期 <br/>
	 * 如果沒有異常通報、增額、主管機關指定時之週期，只會為A或B
	 */
	@Column(name = "ELF412C_OCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_ockdLine;

	/**
	 * 戶況 <br/>
	 * 0.無餘額 <br/>
	 * 1.正常 <br/>
	 * 2.逾期 <br/>
	 * 3.催收 <br/>
	 * 4.呆帳(該戶項下最嚴重的代碼)
	 */
	@Column(name = "ELF412C_CSTATE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_cState;

	/**
	 * 新作/增額 <br/>
	 * N.新做/ C.增貸 <br/>
	 * 來自 ELF411_NEWADD {N:新作 , C:增額, R:逾放轉正} <br/>
	 * 參考 UtilConstants.NEWADD
	 */
	@Column(name = "ELF412C_NEWADD", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_newAdd;

	/**
	 * 新作增額資料日期 <br/>
	 * ELF411_DATAYM 之資料
	 */
	@Column(name = "ELF412C_NEWDATE", length = 6, columnDefinition = "CHAR(6)")
	private String elf412c_newDate;

	/**
	 * 不覆審代碼 <br/>
	 * 1.本行或同業主辦之聯貸案件，非擔任管理行。 <br/>
	 * 2.十成定存。 <br/>
	 * 3.純進出押戶。 <br/>
	 * 4.對政府或政府所屬機關、學校之授信案件。 <br/>
	 * 5.拆放同業或對同業之融通。 <br/>
	 * 6.已列報為逾期放款或轉列催收款項之案件。 <br/>
	 * 7.銷戶 <br/>
	 * 8.本次暫不覆審 <br/>
	 * 9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。
	 * 10.企業戶之外勞保證中長期授信案件，除於新作後辦理一次覆審外，免再辦理覆審，但嗣後如有增額、
	 * 減額、變更條件或續約時，仍應依本要點第五條規定辦理一次覆審
	 */
	@Column(name = "ELF412C_NCKDFLAG", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_nckdFlag;

	/**
	 * 不覆審日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_NCKDDATE", columnDefinition = "DATE")
	private Date elf412c_nckdDate;

	/**
	 * 不覆審備註
	 */
	@Column(name = "ELF412C_NCKDMEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412c_nckdMemo;

	/**
	 * 銷戶日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_CANCELDT", columnDefinition = "DATE")
	private Date elf412c_cancelDt;

	/**
	 * 人工維護日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_UPDDATE", columnDefinition = "DATE")
	private Date elf412c_upddate;

	/**
	 * 人工調整ID
	 */
	@Column(name = "ELF412C_UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String elf412c_updater;

	/**
	 * 其他備註
	 */
	@Column(name = "ELF412C_MEMO", length = 202, columnDefinition = "CHAR(202)")
	private String elf412c_memo;

	/**
	 * 資料更新日
	 */
	@Column(name = "ELF412C_TMESTAMP", columnDefinition = "TIMESTAMP")
	private Timestamp elf412c_tmestamp;

	/**
	 * 人工調整週期 <br/>
	 * 目前資料['Y', 'N', '']
	 */
	@Column(name = "ELF412C_UCKDLINE", length = 2, columnDefinition = "CHAR(2)")
	private String elf412c_uckdLine;

	/**
	 * 人工調整週期有效日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_UCKDDT", columnDefinition = "DATE")
	private Date elf412c_uckdDt;

	/**
	 * 資料日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_DATADT", columnDefinition = "DATE")
	private Date elf412c_dataDt;

	/**
	 * 最新一次下次恢復覆審日 <br/>
	 * 不覆審代碼註記為不覆審時，可設定下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_NEXTNWDT", columnDefinition = "DATE")
	private Date elf412c_nextNwDt;

	/**
	 * 上次設定之下次恢復覆審日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF412C_NEXTLTDT", columnDefinition = "DATE")
	private Date elf412c_nextLtDt;

	/**
	 * 外部評等類別 <br/>
	 * 標準普爾 | 1 <br/>
	 * 穆迪信評 | 2 <br/>
	 * 惠譽信評 | 3 <br/>
	 * 中華信評 | 4
	 */
	@Column(name = "ELF412C_FCRDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_fcrdType;

	/**
	 * 外部評等地區別 <br/>
	 * 國際 | 1 <br/>
	 * 本國 | 2
	 */
	@Column(name = "ELF412C_FCRDAREA", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_fcrdArea;

	/**
	 * 外部評等期間別 <br/>
	 * 長期 | 1 <br/>
	 * 短期 | 2
	 */
	@Column(name = "ELF412C_FCRDPRED", length = 1, columnDefinition = "CHAR(1)")
	private String elf412c_fcrdPred;

	/**
	 * 外部評等等級
	 */
	@Column(name = "ELF412C_FCRDGRAD", length = 30, columnDefinition = "CHAR(30)")
	private String elf412c_fcrdGrad;

	public String getElf412c_branch() {
		return elf412c_branch;
	}

	public void setElf412c_branch(String elf412c_branch) {
		this.elf412c_branch = elf412c_branch;
	}

	public String getElf412c_custId() {
		return elf412c_custId;
	}

	public void setElf412c_custId(String elf412c_custId) {
		this.elf412c_custId = elf412c_custId;
	}

	public String getElf412c_dupNo() {
		return elf412c_dupNo;
	}

	public void setElf412c_dupNo(String elf412c_dupNo) {
		this.elf412c_dupNo = elf412c_dupNo;
	}

	public String getElf412c_mainCust() {
		return elf412c_mainCust;
	}

	public void setElf412c_mainCust(String elf412c_mainCust) {
		this.elf412c_mainCust = elf412c_mainCust;
	}

	public String getElf412c_crdType() {
		return elf412c_crdType;
	}

	public void setElf412c_crdType(String elf412c_crdType) {
		this.elf412c_crdType = elf412c_crdType;
	}

	public String getElf412c_crdtTbl() {
		return elf412c_crdtTbl;
	}

	public void setElf412c_crdtTbl(String elf412c_crdtTbl) {
		this.elf412c_crdtTbl = elf412c_crdtTbl;
	}

	public String getElf412c_mowType() {
		return elf412c_mowType;
	}

	public void setElf412c_mowType(String elf412c_mowType) {
		this.elf412c_mowType = elf412c_mowType;
	}

	public String getElf412c_mowTbl1() {
		return elf412c_mowTbl1;
	}

	public void setElf412c_mowTbl1(String elf412c_mowTbl1) {
		this.elf412c_mowTbl1 = elf412c_mowTbl1;
	}

	public Date getElf412c_llrDate() {
		return elf412c_llrDate;
	}

	public void setElf412c_llrDate(Date elf412c_llrDate) {
		this.elf412c_llrDate = elf412c_llrDate;
	}

	public Date getElf412c_lrDate() {
		return elf412c_lrDate;
	}

	public void setElf412c_lrDate(Date elf412c_lrDate) {
		this.elf412c_lrDate = elf412c_lrDate;
	}

	public String getElf412c_rckdLine() {
		return elf412c_rckdLine;
	}

	public void setElf412c_rckdLine(String elf412c_rckdLine) {
		this.elf412c_rckdLine = elf412c_rckdLine;
	}

	public String getElf412c_ockdLine() {
		return elf412c_ockdLine;
	}

	public void setElf412c_ockdLine(String elf412c_ockdLine) {
		this.elf412c_ockdLine = elf412c_ockdLine;
	}

	public String getElf412c_cState() {
		return elf412c_cState;
	}

	public void setElf412c_cState(String elf412c_cState) {
		this.elf412c_cState = elf412c_cState;
	}

	public String getElf412c_newAdd() {
		return elf412c_newAdd;
	}

	public void setElf412c_newAdd(String elf412c_newAdd) {
		this.elf412c_newAdd = elf412c_newAdd;
	}

	public String getElf412c_newDate() {
		return elf412c_newDate;
	}

	public void setElf412c_newDate(String elf412c_newDate) {
		this.elf412c_newDate = elf412c_newDate;
	}

	public String getElf412c_nckdFlag() {
		return elf412c_nckdFlag;
	}

	public void setElf412c_nckdFlag(String elf412c_nckdFlag) {
		this.elf412c_nckdFlag = elf412c_nckdFlag;
	}

	public Date getElf412c_nckdDate() {
		return elf412c_nckdDate;
	}

	public void setElf412c_nckdDate(Date elf412c_nckdDate) {
		this.elf412c_nckdDate = elf412c_nckdDate;
	}

	public String getElf412c_nckdMemo() {
		return elf412c_nckdMemo;
	}

	public void setElf412c_nckdMemo(String elf412c_nckdMemo) {
		this.elf412c_nckdMemo = elf412c_nckdMemo;
	}

	public Date getElf412c_cancelDt() {
		return elf412c_cancelDt;
	}

	public void setElf412c_cancelDt(Date elf412c_cancelDt) {
		this.elf412c_cancelDt = elf412c_cancelDt;
	}

	public Date getElf412c_upddate() {
		return elf412c_upddate;
	}

	public void setElf412c_upddate(Date elf412c_upddate) {
		this.elf412c_upddate = elf412c_upddate;
	}

	public String getElf412c_updater() {
		return elf412c_updater;
	}

	public void setElf412c_updater(String elf412c_updater) {
		this.elf412c_updater = elf412c_updater;
	}

	public String getElf412c_memo() {
		return elf412c_memo;
	}

	public void setElf412c_memo(String elf412c_memo) {
		this.elf412c_memo = elf412c_memo;
	}

	public Timestamp getElf412c_tmestamp() {
		return elf412c_tmestamp;
	}

	public void setElf412c_tmestamp(Timestamp elf412c_tmestamp) {
		this.elf412c_tmestamp = elf412c_tmestamp;
	}

	public String getElf412c_uckdLine() {
		return elf412c_uckdLine;
	}

	public void setElf412c_uckdLine(String elf412c_uckdLine) {
		this.elf412c_uckdLine = elf412c_uckdLine;
	}

	public Date getElf412c_uckdDt() {
		return elf412c_uckdDt;
	}

	public void setElf412c_uckdDt(Date elf412c_uckdDt) {
		this.elf412c_uckdDt = elf412c_uckdDt;
	}

	public Date getElf412c_dataDt() {
		return elf412c_dataDt;
	}

	public void setElf412c_dataDt(Date elf412c_dataDt) {
		this.elf412c_dataDt = elf412c_dataDt;
	}

	public Date getElf412c_nextNwDt() {
		return elf412c_nextNwDt;
	}

	public void setElf412c_nextNwDt(Date elf412c_nextNwDt) {
		this.elf412c_nextNwDt = elf412c_nextNwDt;
	}

	public Date getElf412c_nextLtDt() {
		return elf412c_nextLtDt;
	}

	public void setElf412c_nextLtDt(Date elf412c_nextLtDt) {
		this.elf412c_nextLtDt = elf412c_nextLtDt;
	}

	public String getElf412c_fcrdType() {
		return elf412c_fcrdType;
	}

	public void setElf412c_fcrdType(String elf412c_fcrdType) {
		this.elf412c_fcrdType = elf412c_fcrdType;
	}

	public String getElf412c_fcrdArea() {
		return elf412c_fcrdArea;
	}

	public void setElf412c_fcrdArea(String elf412c_fcrdArea) {
		this.elf412c_fcrdArea = elf412c_fcrdArea;
	}

	public String getElf412c_fcrdPred() {
		return elf412c_fcrdPred;
	}

	public void setElf412c_fcrdPred(String elf412c_fcrdPred) {
		this.elf412c_fcrdPred = elf412c_fcrdPred;
	}

	public String getElf412c_fcrdGrad() {
		return elf412c_fcrdGrad;
	}

	public void setElf412c_fcrdGrad(String elf412c_fcrdGrad) {
		this.elf412c_fcrdGrad = elf412c_fcrdGrad;
	}

}
