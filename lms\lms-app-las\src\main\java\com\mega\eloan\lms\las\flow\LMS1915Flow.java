package com.mega.eloan.lms.las.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * 房貸業務工作稿底稿流程
 * 
 * <AUTHOR>
 * 
 */
@Component
public class LMS1915Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;

	@Transition(node = "start1", value = "起案")
	public void strat(FlowInstance instance) {
		String path = "";

		L192M01A meta = (L192M01A) metaDao.findByOid(getDomainClass(), instance
				.getId().toString());

		String innerAudit = meta.getInnerAudit();

		if ("Y".equals(innerAudit)) {
			path = "分行_起案";
		} else if ("N".equals(innerAudit)) {
			path = "稽核_起案";
		}
		instance.setAttribute("action", path); // <--------路徑判斷
	}

//	@Transition(node = "稽核_分行_編製中", value = "稽核_傳送")
//	public void move(FlowInstance instance) {
//		// System.out.println("flow -> handle(accept) ");
//		String instanceId = instance.getId().toString();
//		// System.out.println("instanceId : " + instanceId);
//		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
//		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
//		meta.setApprover(user.getUserId());
//		meta.setApproveTime(CapDate.getCurrentTimestamp());
//	}

	@Transition(node = "稽核_編製中", value = "稽核_呈主管覆核")
	public void forward(FlowInstance instance) {

	}

	@Transition(node = "稽核_覆核", value = "稽核_核准")
	public void accept(FlowInstance instance) {
		String instanceId = instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
	}

	@Transition(node = "稽核_覆核", value = "稽核_退回")
	public void reject(FlowInstance instance) {
		String instanceId = instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
	}

	@Transition(node = "分行_編製中", value = "分行_呈主管覆核")
	public void forward_2(FlowInstance instance) {

	}

	@Transition(node = "分行_覆核", value = "分行_核准")
	public void accept_2(FlowInstance instance) {
		String instanceId = instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
	}

	@Transition(node = "分行_覆核", value = "分行_退回")
	public void reject_2(FlowInstance instance) {
		String instanceId = instance.getId().toString();
		Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		meta.setApprover(user.getUserId());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L192M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return LasDocStatusEnum.class;
	}
}
