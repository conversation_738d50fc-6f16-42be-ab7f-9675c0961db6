package com.mega.eloan.lms.lns.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 借款人基本資料(企金授權外)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMSS02BPanel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMSS02BPanel(String id) {
		super(id);
	}
	
	public LMSS02BPanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}
}
