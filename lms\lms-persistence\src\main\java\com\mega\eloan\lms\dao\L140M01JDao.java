/* 
 * L140M01JDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01J;


/** 共同借款人檔 **/
public interface L140M01JDao extends IGenericDao<L140M01J> {

	L140M01J findByOid(String oid);

	List<L140M01J> findByOids(String[] oids);

	List<L140M01J> findByMainId(String mainId);

	L140M01J findByUniqueKey(String mainId, String custId, String dupNo);

	List<L140M01J> findByIndex01(String mainId, String custId, String dupNo);
	
	List<L140M01J> findByCustIdDupId(String custId,String DupNo);
}