package com.mega.eloan.lms.lms.service.impl;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dao.F101M01ADao;
import com.mega.eloan.lms.lms.service.FSSGridService;
import com.mega.eloan.lms.model.F101M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

@Service
public class FSSGridServiceImpl
  implements FSSGridService
{

  @Resource
  F101M01ADao f101m01aDao;


  public Page<F101M01A> getF1010V01(ISearch search)
  {
    return this.f101m01aDao.findPage(search);
  }

  public Page<F101M01A> getF1020V01(ISearch pageSetting)
  {
    return this.f101m01aDao.findPage(pageSetting);
  }

  public Page<F101M01A> findPreDocsPages(ISearch search)
  {
    return this.f101m01aDao.findPage(search);
  }

}