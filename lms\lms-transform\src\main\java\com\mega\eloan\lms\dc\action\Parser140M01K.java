package com.mega.eloan.lms.dc.action;

import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L140M01KBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01K
 * </pre>
 * 
 * @since 2013/04/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/04/17,<PERSON>,new
 *          </ul>
 */
public class Parser140M01K extends AbstractLMSCustParser {

	private String class_name = Parser140M01K.class.getSimpleName();

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01K(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {

			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String mainId = "";
			if (k2.length == 2) {
				mainId = k2[1];// 主檔
			} else {
				mainId = k2[2];// 明細檔之UNID
			}
			//20130417 Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			//20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId  =getItemValue(domDoc, "WEBELOANMAINID");
			mainId = cntrDocId.isEmpty()?mainId:cntrDocId;

			String[] cpCurrs = this.getItemValueByMupArray(domDoc, "CPCurr2");
			String[] collectPays = this.getItemValueByMupArray(domDoc,
					"CollectPay2");
			String[] caCurrs = this.getItemValueByMupArray(domDoc, "CACurr2");
			String[] collectAccepts = this.getItemValueByMupArray(domDoc,
					"CollectAccept2");
			//20130503 modified by Sandra取兩組大的值來做為迴圈條件，並加判斷避免exception
			int a =0;
			if(cpCurrs.length>=caCurrs.length) a =cpCurrs.length;
			else a =caCurrs.length;
			
			for (int i = 0; i < a; i++) {
				L140M01KBean bean = new L140M01KBean();
				bean.setOid(Util.getOID());
				bean.setMainId(mainId);
				if(i<cpCurrs.length){
					bean.setCpCurr(cpCurrs[i]);
					bean.setCollectPay(getAmtString(collectPays[i]));
				}
				if(i<caCurrs.length){
					bean.setCaCurr(caCurrs[i]);
					bean.setCollectAccept(getAmtString(collectAccepts[i]));
				}
				if (StringUtils.isNotBlank(bean.getCpCurr())) {
					this.txtWrite.println(bean.toString());
					this.parserTotal++;
				}
			}

		} catch (Exception e) {
			String errmsg = "【" + strBrn + "】分行執行" + class_name
					+ " 之transferDXL時產生錯誤,dxl檔名:" + dxlName + ",dxlPath="
					+ dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}
	
	private String getAmtString(String amt) {
		return amt.replaceAll("[,|元]", "");
	}
}