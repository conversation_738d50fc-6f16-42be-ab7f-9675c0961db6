/* 
 *ObsdbELF461ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF461Service;

/**
 * <pre>
 * 授信消金狀態報送  ELF461
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF461ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF461Service {
	@Override
	public List<Map<String, Object>> findByKey(String BRNID, String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc(BRNID).queryForList("ELF461.selByKey",
				new Object[] { custId, dupNo, cntrNo });
	}

	@Override
	public void insert(String BRNID, String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String cType, String updater, BigDecimal gutcDate,
			String proJno, String property, BigDecimal timestamp,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID,
			String ELF461_ISOFCLCGA, String ELF461_CGA_COUNTRY,
			String ELF461_CGA_CRDTYPE, String ELF461_CGA_CRDAREA,
			String ELF461_CGA_CRDPRED, String ELF461_CGA_CRDGRAD,
			BigDecimal ELF461_CGA_RSKRTO, BigDecimal ELF461_CGA_GRADSCR,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, 
			String isHighQualityProjOpt_3, String isHighQualityProjOpt_4, 
			String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String appDate, String appNo, String loancontype,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL) {
		this.getJdbc(BRNID).update(
				"ELF461.insert",
				new Object[] { custId, dupNo, cntrNo, brNo, status, apprYY,
						apprMM, cType, gutcDate, proJno, property, updater,
						timestamp, ELF461_ISREVIVE_Y, ELF461_MAINID,
						ELF461_ISOFCLCGA, ELF461_CGA_COUNTRY,
						ELF461_CGA_CRDTYPE, ELF461_CGA_CRDAREA,
						ELF461_CGA_CRDPRED, ELF461_CGA_CRDGRAD,
						ELF461_CGA_RSKRTO, ELF461_CGA_GRADSCR,
						isSpecialFinRisk, specialFinRiskType, isCmsAdcRisk,
						isProjectFinOperateStag, isHighQualityProjOpt_1,
						isHighQualityProjOpt_2, isHighQualityProjOpt_3, 
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, 
						isHighQualityProjResult, appDate,
						loancontype, appNo,
						curr, curAmt, currL, curAmtL});

	}

	@Override
	public void update(String BRNID, String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String cType, String updater, BigDecimal gutcDate,
			String proJno, String property, BigDecimal timestamp,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID,
			String ELF461_ISOFCLCGA, String ELF461_CGA_COUNTRY,
			String ELF461_CGA_CRDTYPE, String ELF461_CGA_CRDAREA,
			String ELF461_CGA_CRDPRED, String ELF461_CGA_CRDGRAD,
			BigDecimal ELF461_CGA_RSKRTO, BigDecimal ELF461_CGA_GRADSCR,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, 
			String isHighQualityProjOpt_3, String isHighQualityProjOpt_4, 
			String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String appDate, String appNo, String loancontype,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL) {
		this.getJdbc(BRNID).update(
				"ELF461.update",
				new Object[] { brNo, status, apprYY, apprMM, cType, updater,
						gutcDate, proJno, property, timestamp,
						ELF461_ISREVIVE_Y, ELF461_MAINID, ELF461_ISOFCLCGA,
						ELF461_CGA_COUNTRY, ELF461_CGA_CRDTYPE,
						ELF461_CGA_CRDAREA, ELF461_CGA_CRDPRED,
						ELF461_CGA_CRDGRAD, ELF461_CGA_RSKRTO,
						ELF461_CGA_GRADSCR, isSpecialFinRisk,
						specialFinRiskType, isCmsAdcRisk,
						isProjectFinOperateStag, 
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, 
						isHighQualityProjOpt_3, isHighQualityProjOpt_4, 
						isHighQualityProjOpt_5, isHighQualityProjResult,
						appDate, loancontype, appNo,
						curr, curAmt, currL, curAmtL,
						custId, dupNo, cntrNo});

	}

	@Override
	public void updateOnlyRevive(String BRNID, String custId, String dupNo,
			String cntrNo, String ELF461_ISREVIVE_Y, String ELF461_MAINID) {
		this.getJdbc(BRNID).update(
				"ELF461.updateOnlyRevive",
				new Object[] { ELF461_ISREVIVE_Y, ELF461_MAINID, custId, dupNo,
						cntrNo });

	}

	@Override
	public void updateOnlySpecialFinRisk(String BRNID, String custId,
			String dupNo, String cntrNo, BigDecimal timeStamp,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult) {
		this.getJdbc(BRNID).update(
				"ELF461.updateOnlySpecialFinRisk",
				new Object[] { isSpecialFinRisk, specialFinRiskType,
						isCmsAdcRisk, isProjectFinOperateStag, 
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult,
						custId, dupNo,
						cntrNo, timeStamp });

	}

	@Override
	public void insertOnlySpecialFinRisk(String BRNID, String custId,
			String dupNo, String cntrNo, String brNo, String status,
			String apprYY, String apprMM, String updater, BigDecimal timeStamp,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult) {
		this.getJdbc(BRNID).update(
				"ELF461.insertOnlySpecialFinRisk",
				new Object[] { custId, dupNo, cntrNo, brNo, status, apprYY,
						apprMM, updater, timeStamp, isSpecialFinRisk,
						specialFinRiskType, isCmsAdcRisk,
						isProjectFinOperateStag,
						isHighQualityProjOpt_1, isHighQualityProjOpt_2, isHighQualityProjOpt_3,
						isHighQualityProjOpt_4, isHighQualityProjOpt_5, isHighQualityProjResult});

	}

}
