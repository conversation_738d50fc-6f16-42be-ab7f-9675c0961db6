package com.mega.eloan.lms.cls.service;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C124M01B;

import java.util.List;

public interface CLS3501Service extends AbstractService{
	public List<C124M01A> findC124m01aByCustIdAndDupNo(String custId, String dupNo, Integer verNo);
	public boolean deleteC124M01As(String[] oids);
	public void deleteC124m01bs(List<C124M01B> c124m01bs, boolean isAll);
	public void saveC124m01bList(List<C124M01B> list);
	public C124M01B findC124m01b(String mainId, String branchType, String branchId, String staffNo, String staffJob);
	public void flowAction(String mainOid, C124M01A model, boolean setResult, boolean resultType, boolean upMis) throws Throwable;

	void saveC124m01a(C124M01A meta);
}
