/* 
 * L140MM1BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140MM1BDao;
import com.mega.eloan.lms.model.L140MM1B;

/** 央行註記異動作業簽章欄檔 **/
@Repository
public class L140MM1BDaoImpl extends LMSJpaDao<L140MM1B, String>
	implements L140MM1BDao {

	@Override
	public L140MM1B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140MM1B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140MM1B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140MM1B findByUniqueKey(String mainId, String branchType, String branchId, String staffNo, String staffJob){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType", branchType);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140MM1B> findByIndex01(String mainId, String branchType, String branchId, String staffNo, String staffJob){
		ISearch search = createSearchTemplete();
		List<L140MM1B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType", branchType);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}