package com.mega.eloan.lms.eloandb.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * DW_ELF412OVS 海外覆審控制檔
 * </pre>
 * 
 * @since 2011/12/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/5,jessica,new
 *          </ul>
 */
public interface Dw_elf412ovsService {

     /**
	 * (企金)引進主要授信戶 Maincust
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	Map<String, Object> findDwElf412ovsMaincust(String custId, String dupNo,
			String brNo);

	List<Map<String, Object>> findDwElf412ovsByBranch(String branch,
			Date dataDate);

}
