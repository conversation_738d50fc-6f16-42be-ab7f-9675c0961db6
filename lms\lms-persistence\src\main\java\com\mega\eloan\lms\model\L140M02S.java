/* 
 * L140M02S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 應收帳款買方額度資訊主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140M02S", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","type","itemSeq"}))
public class L140M02S extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 類型<p/>
	 * B.附表B(買方全行額度)
	 */
	@Size(max=1)
	@Column(name="TYPE", length=1, columnDefinition="CHAR(1)")
	private String type;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="ITEMSEQ", columnDefinition="DECIMAL(5,0)")
	private Integer itemSeq;

	/** 
	 * 統一編號<p/>
	 * 為買方ID<br/>
	 *  合計 9999999999
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 屬應收帳款承購無追索權買方額度- IMPORT FACTOR承購額度或保險承保額度(1) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFAMTS", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAmtS;

	/** 屬應收帳款承購無追索權買方額度- 經合格保險公司承保額度(2) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFAMTI", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAmtI;

	/** 屬應收帳款承購無追索權買方額度- 無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFAMTN", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAmtN;

	/** 小計(A)=(1)+(2) +(3) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFAMTT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAmtT;
	
	
	/** 屬應收帳款承購無追索權買方額度- 調整後IMPORT FACTOR承購額度或保險承保額度(1) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFADJAMTS", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAdjAmtS;

	/** 屬應收帳款承購無追索權買方額度- 調整後經合格保險公司承保額度(2) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFADJAMTI", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAdjAmtI;

	/** 屬應收帳款承購無追索權買方額度- 調整後無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFADJAMTN", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAdjAmtN;

	/** 調整後小計(A)=(1)+(2) +(3) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="IMFADJAMTT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal imfAdjAmtT;
	

	/** 應收帳款債務人在本行之授信有效額度有-擔保(4) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTS", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtS;

	/** 應收帳款債務人在本行之授信有效額度有-無擔保(5) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTN", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtN;

	/** 應收帳款債務人在本行之授信有效額度有-小計(B)=(4)+(5) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtT;

	/** 合   計(C)=(A)+(B) **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalAmt;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATADATE", columnDefinition="Date")
	private Date dataDate;

	/** 備註-匯率 **/
	@Size(max=600)
	@Column(name="REFRATE", length=600, columnDefinition="VARCHAR(600)")
	private String refRate;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得類型<p/>
	 * B.附表B(買方全行額度)
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定類型<p/>
	 *  B.附表B(買方全行額度)
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得序號 **/
	public Integer getItemSeq() {
		return this.itemSeq;
	}
	/** 設定序號 **/
	public void setItemSeq(Integer value) {
		this.itemSeq = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * 為買方ID<br/>
	 *  合計 9999999999
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  為買方ID<br/>
	 *  合計 9999999999
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得屬應收帳款承購無追索權買方額度- IMPORT FACTOR承購額度或保險承保額度(1) **/
	public BigDecimal getImfAmtS() {
		return this.imfAmtS;
	}
	/** 設定屬應收帳款承購無追索權買方額度- IMPORT FACTOR承購額度或保險承保額度(1) **/
	public void setImfAmtS(BigDecimal value) {
		this.imfAmtS = value;
	}

	/** 取得屬應收帳款承購無追索權買方額度- 經合格保險公司承保額度(2) **/
	public BigDecimal getImfAmtI() {
		return this.imfAmtI;
	}
	/** 設定屬應收帳款承購無追索權買方額度- 經合格保險公司承保額度(2) **/
	public void setImfAmtI(BigDecimal value) {
		this.imfAmtI = value;
	}

	/** 取得屬應收帳款承購無追索權買方額度- 無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	public BigDecimal getImfAmtN() {
		return this.imfAmtN;
	}
	/** 設定屬應收帳款承購無追索權買方額度- 無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	public void setImfAmtN(BigDecimal value) {
		this.imfAmtN = value;
	}

	/** 取得小計(A)=(1)+(2) +(3) **/
	public BigDecimal getImfAmtT() {
		return this.imfAmtT;
	}
	/** 設定小計(A)=(1)+(2) +(3) **/
	public void setImfAmtT(BigDecimal value) {
		this.imfAmtT = value;
	}

	/** 取得應收帳款債務人在本行之授信有效額度有-擔保(4) **/
	public BigDecimal getFactAmtS() {
		return this.factAmtS;
	}
	/** 設定應收帳款債務人在本行之授信有效額度有-擔保(4) **/
	public void setFactAmtS(BigDecimal value) {
		this.factAmtS = value;
	}

	/** 取得應收帳款債務人在本行之授信有效額度有-無擔保(5) **/
	public BigDecimal getFactAmtN() {
		return this.factAmtN;
	}
	/** 設定應收帳款債務人在本行之授信有效額度有-無擔保(5) **/
	public void setFactAmtN(BigDecimal value) {
		this.factAmtN = value;
	}

	/** 取得應收帳款債務人在本行之授信有效額度有-小計(B)=(4)+(5) **/
	public BigDecimal getFactAmtT() {
		return this.factAmtT;
	}
	/** 設定應收帳款債務人在本行之授信有效額度有-小計(B)=(4)+(5) **/
	public void setFactAmtT(BigDecimal value) {
		this.factAmtT = value;
	}

	/** 取得合   計(C)=(A)+(B) **/
	public BigDecimal getTotalAmt() {
		return this.totalAmt;
	}
	/** 設定合   計(C)=(A)+(B) **/
	public void setTotalAmt(BigDecimal value) {
		this.totalAmt = value;
	}

	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}
	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得備註-匯率 **/
	public String getRefRate() {
		return this.refRate;
	}
	/** 設定備註-匯率 **/
	public void setRefRate(String value) {
		this.refRate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 設定屬應收帳款承購無追索權買方額度- 調整後IMPORT FACTOR承購額度或保險承保額度(1) **/
	public void setImfAdjAmtS(BigDecimal imfAdjAmtS) {
		this.imfAdjAmtS = imfAdjAmtS;
	}
	
	/** 取得屬應收帳款承購無追索權買方額度- 調整後IMPORT FACTOR承購額度或保險承保額度(1) **/
	public BigDecimal getImfAdjAmtS() {
		return imfAdjAmtS;
	}
	
	/** 設定屬應收帳款承購無追索權買方額度- 調整後經合格保險公司承保額度(2) **/
	public void setImfAdjAmtI(BigDecimal imfAdjAmtI) {
		this.imfAdjAmtI = imfAdjAmtI;
	}
	
	/** 取得屬應收帳款承購無追索權買方額度- 調整後經合格保險公司承保額度(2) **/
	public BigDecimal getImfAdjAmtI() {
		return imfAdjAmtI;
	}
	
	/** 設定屬應收帳款承購無追索權買方額度- 調整後無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	public void setImfAdjAmtN(BigDecimal imfAdjAmtN) {
		this.imfAdjAmtN = imfAdjAmtN;
	}
	
	/** 取得屬應收帳款承購無追索權買方額度- 調整後無IMPORT FACTOR承購額度或無保險承保額度(3) **/
	public BigDecimal getImfAdjAmtN() {
		return imfAdjAmtN;
	}
	
	/** 設定調整後小計(A)=調整後(1)+(2)+(3) **/
	public void setImfAdjAmtT(BigDecimal imfAdjAmtT) {
		this.imfAdjAmtT = imfAdjAmtT;
	}
	
	/** 取得調整後小計(A)=調整後(1)+(2)+(3) **/
	public BigDecimal getImfAdjAmtT() {
		return imfAdjAmtT;
	}
}
