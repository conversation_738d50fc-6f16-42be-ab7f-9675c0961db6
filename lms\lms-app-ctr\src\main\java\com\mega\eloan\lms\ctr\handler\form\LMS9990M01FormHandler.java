/* 
 * LMS9990FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ctr.handler.form;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.ctr.constants.CtrConstants;
import com.mega.eloan.lms.ctr.constants.CtrConstants.L999M01BType;
import com.mega.eloan.lms.ctr.constants.CtrConstants.L999M01DItemType;
import com.mega.eloan.lms.ctr.service.LMS9990Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L999M01A;
import com.mega.eloan.lms.model.L999M01B;
import com.mega.eloan.lms.model.L999M01C;
import com.mega.eloan.lms.model.L999M01D;
import com.mega.eloan.lms.model.L999S01A;
import com.mega.eloan.lms.model.L999S01B;
import com.mega.eloan.lms.model.L999S02A;
import com.mega.eloan.lms.model.L999S04A;
import com.mega.eloan.lms.model.L999S04B;
import com.mega.eloan.lms.model.L999S07A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 約據書
 * </pre>
 * 
 * @since 2012/2/12
 * <AUTHOR>
 * @version <ul>
 *          2012/2/12,Ice,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9990m01formhandler")
@DomainClass(L999M01A.class)
public class LMS9990M01FormHandler extends LMS9990M00FormHandler {

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS9990Service lms9990Service;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	BranchService branchService;
	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	LMSService lmsService;

	/**
	 * 儲存L999M01A 約據書主檔(新增約據書頁面)
	 * 
	 * @param params
	 *            <pre>
	 *            { 
	 *            srcMainId:簽報書mainId 
	 *            }
	 * </pre>
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = null;
		String srcMainId = Util.trim(params.getString("srcMainId"));
		String[] contractType = Util.trim(params.getString("contractType"))
				.split(UtilConstants.Mark.SPILT_MARK);
		String[] l140m01a_oids = params.getStringArray("l140m01a_Oids");
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));

		L120M01A l120m01a = null;
		List<L120S01A> l120s01aList = null;
		List<L120S01B> l120s01bList = null;

		try {
			result = new CapAjaxFormResult();

			l120m01a = lms1205Service.findL120m01aByMainId(srcMainId);
			if (l120m01a == null) {
				l120m01a = new L120M01A();
			}
			List<L140M01A> l140m01as = lms9990Service
					.findL140M01AByOids(l140m01a_oids);
			l120s01aList = lms1205Service.findL120s01aByMainId(srcMainId);
			l120s01bList = lms1205Service.findL120s01bByMainId(srcMainId);
			for (String value : contractType) {
				this.creatNewL999M01A(l140m01as, l120m01a, l120s01aList,
						l120s01bList, value, txCode);
			}

			// TODO 先註解
			// result.set("contractType",
			// Util.trim(l999m01a.getContractType()));
			// result.set(EloanConstants.MAIN_ID,
			// Util.trim(l999m01a.getMainId()));
			// result.set(EloanConstants.MAIN_OID,
			// Util.trim(l999m01a.getOid()));
			// result.set(EloanConstants.MAIN_DOC_STATUS,
			// Util.trim(l999m01a.getDocStatus()));
			// result.set(EloanConstants.TRANSACTION_CODE, Util.trim(txCode));
			// result.set("srcMainId", Util.trim(l999m01a.getSrcMainId()));
			//
			// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
			// .getMainMessage(this.getComponent(),
			// UtilConstants.AJAX_RSP_MSG.儲存成功));
			// TODO 先註解
		} finally {
			if (l120s01aList != null) {
				// Miller edited at 2012/07/23
				new ArrayList<L120S01A>(l120s01aList).clear();
			}
			if (l120s01bList != null) {
				// Miller edited at 2012/07/23
				new ArrayList<L120S01B>(l120s01bList).clear();
			}
		}

		return result;

	}

	/**
	 * 產生約據書明細檔
	 * 
	 * @param l120m01a
	 * @param l120s01aList
	 * @param l120s01bList
	 */
	private void creatNewL999M01A(List<L140M01A> l140m01as, L120M01A l120m01a,
			List<L120S01A> l120s01aList, List<L120S01B> l120s01bList,
			String contractType, String txCode) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		ArrayList<L999M01A> l999m01as = new ArrayList<L999M01A>();
		ArrayList<L999S01A> l999s01as = new ArrayList<L999S01A>();
		ArrayList<L999M01B> l999m01bs = new ArrayList<L999M01B>();
		ArrayList<L999M01C> l999m01cs = new ArrayList<L999M01C>();
		ArrayList<L999M01D> l999m01ds = new ArrayList<L999M01D>();
		HashMap<String, L120S01B> l120s01b_map = new HashMap<String, L120S01B>();
		HashMap<String, L120S01A> l120s01a_map = new HashMap<String, L120S01A>();
		for (L120S01A L120S01A : l120s01aList) {
			if (L120S01A != null) {
				l120s01a_map.put(L120S01A.getCustId() + L120S01A.getDupNo(),
						L120S01A);
			}
		}
		for (L120S01B l120s01b : l120s01bList) {
			if (l120s01b != null) {
				l120s01b_map.put(l120s01b.getCustId() + l120s01b.getDupNo(),
						l120s01b);
			}
		}

		for (L140M01A l140m01a : l140m01as) {
			String mainId = IDGenerator.getUUID();
			BigDecimal totLoanAmt = l140m01a.getCurrentApplyAmt();
			String totLoanCurr = l140m01a.getCurrentApplyCurr();
			Date usedSDate = null;
			Date usedEDate = null;
			if ("1".equals(l140m01a.getUseDeadline())) {
				String desp1 = Util.trim(l140m01a.getDesp1());
				if (Util.isNotEmpty(desp1)) {
					String[] dates = desp1.split("~");
					if (dates.length == 2) {
						usedSDate = Util.parseDate(dates[0]);
						usedEDate = Util.parseDate(dates[1]);
					}
				}
			}else if ("8".equals(l140m01a.getUseDeadline())) {
				// J-110-0320 為符營業單位陳報團貸實務作業，於ELOAN系統新增團貸動用期限選項
				// 自核准日起~YYYY-MM-DD
				String desp1 = Util.trim(l140m01a.getDesp1());
				if (Util.isNotEmpty(desp1)) {
					String[] dates = desp1.split("~");
					if (dates.length == 2) {
						usedSDate = l120m01a.getEndDate();// 核准日期
						usedEDate = Util.parseDate(dates[1]);
					}
				}
			}

			L999M01D l999m01dC = new L999M01D();
			l999m01dC.setMainId(mainId);
			l999m01dC.setItemType(CtrConstants.L999M01DItemType.授信總額度);
			l999m01dC.setItemContent(Util.trim(l140m01a.getCurrentApplyCurr())
					+ " "
					+ NumConverter.addComma(Util.parseBigDecimal(l140m01a
							.getCurrentApplyAmt())) + " ");

			L999S01A l999s01a = new L999S01A();
			l999s01a.setMainId(mainId);
			l999s01a.setUsedSDate(usedSDate);
			l999s01a.setUsedEDate(usedEDate);
			l999s01a.setTotLoanAmt(totLoanAmt);
			l999s01a.setTotLoanCurr(totLoanCurr);
			l999s01as.add(l999s01a);
			L999M01A l999m01a = new L999M01A();
			// 將Uid帶入額度明細表mainId
			l999m01a.setUid(l140m01a.getMainId());

			l999m01a.setMainId(mainId);
			l999m01a.setSrcMainId(l120m01a.getMainId());
			l999m01a.setTypCd(l140m01a.getTypCd());
			String custId = l140m01a.getCustId();
			l999m01a.setCustId(custId);
			String dupNo = l140m01a.getDupNo();
			l999m01a.setDupNo(dupNo);
			l999m01a.setCustName(l140m01a.getCustName());
			l999m01a.setUnitType(UnitTypeEnum.convertToUnitType(l120m01a
					.getUnitType()));
			l999m01a.setOwnBrId(l140m01a.getOwnBrId());
			l999m01a.setCaseYear(l120m01a.getCaseYear());
			l999m01a.setCaseBrId(l120m01a.getCaseBrId());
			l999m01a.setCaseSeq(l120m01a.getCaseSeq());
			l999m01a.setCaseNo(l140m01a.getCaseNo());
			l999m01a.setCaseDate(l140m01a.getCaseDate());
			l999m01a.setContractType(contractType);
			l999m01a.setCreator(user.getUserId());
			l999m01a.setCreateTime(CapDate.getCurrentTimestamp());
			Map<String, Object> map = misCustdataService.findByIdDupNo(
					Util.trim(l120m01a.getCustId()),
					Util.trim(l120m01a.getDupNo()));

			l999m01a.setDocStatus(FlowDocStatusEnum.編製中.toString());
			// 給save使用的(因為剛新增的資料會沒有文件亂碼)
			// l999m01a.setRandomCode(IDGenerator.getRandomCode());
			l999m01a.setTxCode(txCode);
			l999m01as.add(l999m01a);
			L999M01B l999m01b = new L999M01B();
			IBranch branch = branchService.getBranch(l120m01a.getCaseBrId());
			l999m01b.setChairman(userInfoService.getUserName(branch.getBrnMgr()));
			l999m01b.setChairmanId(branch.getBrnMgr());
			l999m01b.setCustId(branch.getBrnMgr());
			l999m01b.setDupNo("0");

			l999m01ds.add(l999m01dC);

			map = misdbBASEService.findSYNBANK(l120m01a.getCaseBrId());
			if (map != null) {
				l999m01b.setAddr(Util.trim(map.get("BRNADDR")));
			}
			l999m01b.setMainId(l999m01a.getMainId());
			l999m01b.setType(L999M01BType.銀行甲方);
			l999m01bs.add(l999m01b);
			for (L120S01A l120s01a : l120s01aList) {
				if (Util.nullToSpace(l120s01a.getCustId()).equals(custId)
						&& Util.nullToSpace(l120s01a.getDupNo()).equals(dupNo)) {
					for (L120S01B l120s01b : l120s01bList) {
						if (Util.nullToSpace(l120s01a.getCustId()).equals(
								l120s01b.getCustId())
								&& Util.nullToSpace(l120s01a.getDupNo())
										.equals(l120s01b.getDupNo())) {

							l999m01b = new L999M01B();
							map = misCustdataService.findByIdDupNo(
									Util.trim(l120s01a.getCustId()),
									Util.trim(l120s01a.getDupNo()));

							if (map != null) {
								l999m01b.setCustId(Util.trim(map.get("CUSTID")));
								l999m01b.setDupNo(Util.trim(map.get("DUPNO")));

								// J-105-0233-001 Web
								// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
								// l999m01b.setAddr(Util.trim(map.get("CITYR"))
								// + Util.trim(map.get("TOWNR"))
								// + Util.trim(map.get("LINR"))
								// + Util.trim(map.get("ADDRR")));
								l999m01b.setAddr(Util.trim(map.get("FULLADDRR")));

								l999m01b.setCustName(Util.trim(map.get("CNAME")));
							} else {
								l999m01b.setCustId(l120s01a.getCustId());
								l999m01b.setDupNo(l120s01a.getDupNo());
								l999m01a.setAddr(l120s01b.getCmpAddr());
								l999m01b.setAddr(l120s01b.getCmpAddr());
								l999m01b.setCustName(l120s01a.getCustName());
							}
							l999m01b.setChairman(l120s01b.getChairman());
							l999m01b.setChairmanId(l120s01b.getChairmanId());
							l999m01b.setChairmanDupNo(l120s01b
									.getChairmanDupNo());
							l999m01b.setMainId(l999m01a.getMainId());
							l999m01b.setType(L999M01BType.借款人乙方);
							l999m01bs.add(l999m01b);
						}
					}
				}
			}
			if (l140m01a.getL140m01i() != null) {
				for (L140M01I l140m01i : l140m01a.getL140m01i()) {
					if (Util.equals(Util.trim(l140m01i.getRType()), UtilConstants.lngeFlag.連帶保證人)) {
						// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式

						String rId = l140m01i.getRId();
						String rDupNo = l140m01i.getRDupNo();
						L999M01C l999m01c = new L999M01C();

						l999m01c.setMainId(l999m01a.getMainId());

						l999m01c.setCustId(rId);

						l999m01c.setDupNo(rDupNo);
						l999m01c.setCustName(l140m01i.getRName());
						String custPos = "";
						if (l120s01a_map.containsKey(rId + rDupNo)) {
							custPos = l120s01a_map.get(rId + rDupNo)
									.getCustPos();
						}
						String addr = "";
						if (l120s01b_map.containsKey(rId + rDupNo)) {
							addr = l120s01b_map.get(rId + rDupNo).getCmpAddr();
						}
						if (Util.isEmpty(addr)) {
							Map<String, Object> custData = misCustdataService
									.findByIdDupNo(Util.trim(rId),
											Util.trim(rDupNo));
							if (custData != null) {
								addr = Util.trim(custData.get("ADDRR"));
							}
						}
						l999m01c.setCustPos(custPos);
						l999m01c.setAddr(Util.truncateString(addr, 300));
						l999m01cs.add(l999m01c);
					}
				}
			}

		}

		lms9990Service.saveAll(l999m01as, l999m01bs, l999m01cs, l999s01as,
				l999m01ds);
	}

	/**
	 * 查詢01.兆豐綜合授信契約書的資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM02(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = null;
		L999M01A l999m01a = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		try {
			result = new CapAjaxFormResult();
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			result = this.formatResultShowM2(result, l999m01a, page);
		} catch (CapException e) {
			logger.error(
					"[LMS9990M01FormHandler] queryL999m01aM02 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		} finally {

		}

		return result;
	}

	/**
	 * 儲存L999M01A 約據書主檔(01.兆豐綜合授信契約書畫面)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01aM02(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.否));
		return L999m01aM02SaveAction(params);

	}

	/**
	 * tempSave 約據書主檔(01.兆豐綜合授信契約書畫面)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempsaveL999m01aM02(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.是));
		return L999m01aM02SaveAction(params);
	}

	/**
	 * 儲存的動作 L999M01A 約據書主檔(01.兆豐綜合授信契約書畫面)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public CapAjaxFormResult L999m01aM02SaveAction(PageParameters params) throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		L999M01A l999m01a = null;
		L999S01A l999s01a = null;
		L999S01B l999s01b = null;
		L999M01D l999m01dA = null;
		L999M01D l999m01dB = null;
		L999M01D l999m01dC = null;

		String itemType = null;
		CapAjaxFormResult result = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formActionS = params.getString("ActionSForm", "");
		String formActionM = params.getString("ActionMForm", "");
		JSONObject jsonAormActionS = null;
		JSONObject jsonAormActionM = null;
		Boolean showMsg = params.getBoolean("showMsg");
		List<GenericBean> GenericBeans = new ArrayList<GenericBean>();
		L999M01B l999m01b = lms9990Service.findL999m01bByMainIdType(mainId,
				L999M01BType.借款人乙方);

		try {
			result = new CapAjaxFormResult();
			jsonAormActionS = JSONObject.fromObject(formActionS);
			jsonAormActionM = JSONObject.fromObject(formActionM);

			// 由於JPA機制 A查詢完畢後作set在查詢B A的rollback機制就被取消了
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			switch (page) {
			case 1:
				try {
					l999s01a = lms9990Service.findL999s01aByMainId(mainId);
					if (l999s01a == null) {
						l999s01a = new L999S01A();
					}
					l999m01dC = lms9990Service.findL999m01dByMainIdItemType(
							mainId, CtrConstants.L999M01DItemType.授信總額度);

					if (l999m01dC == null) {
						l999m01dC = new L999M01D();
						l999m01dC.setMainId(mainId);
						l999m01dC
								.setItemType(CtrConstants.L999M01DItemType.授信總額度);
					}

					l999m01a.setContractNo(jsonAormActionM
							.getString("contractNo"));
					l999m01a.setContractRate(this.getDecimal(jsonAormActionS
							.getString("contractRate")));
					DataParse.toBean(jsonAormActionS, l999s01a);
					l999s01a.setDRateAdd1(this.getDecimal(jsonAormActionS
							.getString("dRateAdd1")));
					l999s01a.setDRateAdd2(this.getDecimal(jsonAormActionS
							.getString("dRateAdd2")));
					l999m01dC.setItemContent(jsonAormActionS
							.getString("L999M01D_ItemTypeC"));
					// l999s01a.setTotLoanAmt(this.getDecimal(jsonAormActionS
					// .getString("totLoanAmt")));
					// l999s01a.setMainId(mainId);
					// checkbox不會解析到該欄位 必須自行處理
					JSONArray itemTypes = null;
					StringBuffer itemTypeStr = new StringBuffer();
					// checkbox不會解析到該欄位 必須自行處理
					if (jsonAormActionS.containsKey("itemType")) {
						itemTypes = jsonAormActionS.optJSONArray("itemType");
						if (itemTypes != null) {
							itemTypes = jsonAormActionS
									.getJSONArray("itemType");
							int size = itemTypes.size();
							for (int i = 0; i < size; i++) {
								itemTypeStr.append(itemTypes.get(i));
							}
							l999s01a.setItemType(itemTypeStr.toString());
						} else if (!"".equals(jsonAormActionS
								.getString("itemType"))) {
							l999s01a.setItemType(jsonAormActionS
									.getString("itemType"));
						} else {
							l999s01a.setItemType(null);
						}
					}
					l999s01a.setMainId(mainId);
					GenericBeans.add(l999m01a);
					GenericBeans.add(l999s01a);
					GenericBeans.add(l999m01dC);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM02 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			case 2:
			case 3:
			case 4:
			case 5:
			case 6:
			case 7:
			case 8:
			case 9:
				try {
					switch (page) {
					case 2:
						itemType = CtrConstants.L999S01BItemType.購料借款;
						break;
					case 3:
						itemType = CtrConstants.L999S01BItemType.外銷借款;
						break;
					case 4:
						itemType = CtrConstants.L999S01BItemType.營運週轉借款;
						break;
					case 5:
						itemType = CtrConstants.L999S01BItemType.貼現;
						break;
					case 6:
						itemType = CtrConstants.L999S01BItemType.透支;
						break;
					case 7:
						itemType = CtrConstants.L999S01BItemType.委任票據保證;
						break;
					case 8:
						itemType = CtrConstants.L999S01BItemType.委任票據承兌;
						break;
					case 9:
						itemType = CtrConstants.L999S01BItemType.委任保證;
						break;
					}
					// S04~S11
					l999s01b = lms9990Service.findL999s01bByMainIdItemType(
							mainId, itemType);
					if (l999s01b == null)
						l999s01b = new L999S01B();
					l999m01a.setContractNo(jsonAormActionM
							.getString("contractNo"));
					DataParse.toBean(jsonAormActionS, l999s01b);
					l999s01b.setItemType(itemType);
					l999s01b.setMainId(mainId);
					GenericBeans.add(l999m01a);
					GenericBeans.add(l999s01b);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM02 page 2-9 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 10:
				try {
					l999m01dA = lms9990Service.findL999m01dByMainIdItemType(
							mainId, CtrConstants.L999M01DItemType.特別條款);
					l999m01dB = lms9990Service.findL999m01dByMainIdItemType(
							mainId, CtrConstants.L999M01DItemType.修正條款說明);
					if (l999m01dA == null)
						l999m01dA = new L999M01D();
					if (l999m01dB == null)
						l999m01dB = new L999M01D();
					l999m01a.setContractNo(jsonAormActionM
							.getString("contractNo"));
					l999m01dA.setMainId(mainId);
					l999m01dA.setItemType(CtrConstants.L999M01DItemType.特別條款);
					l999m01dA.setItemContent(jsonAormActionS
							.getString("itemContentA"));
					l999m01dB.setMainId(mainId);
					l999m01dB.setItemType(CtrConstants.L999M01DItemType.修正條款說明);
					// l999m01dB.setItemContent(jsonAormActionS
					// .getString("itemContentB"));
					GenericBeans.add(l999m01a);
					GenericBeans.add(l999m01dA);
					GenericBeans.add(l999m01dB);
					GenericBeans.add(l999m01b);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM02 page 10 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			}

			if (l999m01b != null) {
				if (jsonAormActionM.containsKey("L999M01B_custName")) {
					l999m01b.setCustName(jsonAormActionM
							.getString("L999M01B_custName"));
				}

			}

			GenericBeans.add(l999m01b);
			lms9990Service.saveAll(GenericBeans);
			result = formatResultShowM2(result, l999m01a, page);
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}

		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] saveL999m01aM02 EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		return result;

	}

	/**
	 * 格式化顯示訊息(01.兆豐綜合授信契約書頁面)
	 * 
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @param page
	 *            第幾個頁面
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShowM2(CapAjaxFormResult result, L999M01A l999m01a, Integer page)
			throws CapException {
		String mainId = l999m01a.getMainId();
		String itemType = null;
		Map<String, String> contractTypeMap = null;
		Map<String, Object> custdata = misCustdataService.findCustdataSelCname(
				l999m01a.getCustId(), l999m01a.getDupNo());
		Map<String, String> itemTypeMap = null;
		L999S01A l999s01a = null;
		L999S01B l999s01b = null;
		List<L999M01D> l999m01dList = null;
		L999M01B l999m01b = null;
		if (custdata == null) {
			custdata = new LinkedHashMap<String, Object>();
		}

		try {
			contractTypeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.企金約據書種類);
			if (contractTypeMap == null) {
				contractTypeMap = new LinkedHashMap<String, String>();
			}

			l999m01b = lms9990Service.findL999m01bByMainIdType(mainId,
					L999M01BType.借款人乙方);

			switch (page) {
			case 1:
				try {
					L999M01D l999m01dC = lms9990Service
							.findL999m01dByMainIdItemType(mainId,
									CtrConstants.L999M01DItemType.授信總額度);
					if (l999m01dC != null) {
						result.set("L999M01D_ItemTypeC",
								Util.trim(l999m01dC.getItemContent()));
					}

					itemTypeMap = codeTypeService
							.findByCodeType(CtrConstants.CodeType.綜合授信契約書借款種類);
					if (itemTypeMap == null)
						itemTypeMap = new LinkedHashMap<String, String>();
					l999s01a = lms9990Service.findL999s01aByMainId(mainId);
					if (l999s01a == null)
						l999s01a = new L999S01A();
					char[] temp = Util.trim(l999s01a.getItemType())
							.toCharArray();
					StringBuffer str = new StringBuffer();
					for (char key : temp) {
						if (!"".equals(key)) {
							str.append(key).append("|");
						}
					}
					if (str.length() == 0) {
						str.append("|");
					}
					result.set("itemType", str.substring(0, str.length() - 1));
					// result.set("totLoanAmt",
					// NumConverter.addComma(l999s01a.getTotLoanAmt()));
					result.set("usedSDate",
							Util.getDate(l999s01a.getUsedSDate()));
					result.set("usedEDate",
							Util.getDate(l999s01a.getUsedEDate()));
					result.set("contractRate",
							Util.trim(l999m01a.getContractRate()));
					result.set("dMonth1",
							NumConverter.addComma(l999s01a.getDMonth1()));
					result.set("dRate1",
							NumConverter.addComma(l999s01a.getDRate1()));
					result.set("dMonth2",
							NumConverter.addComma(l999s01a.getDMonth2()));
					result.set("dRate2",
							NumConverter.addComma(l999s01a.getDRate2()));
					result.set("dRateAdd1",
							NumConverter.addComma(l999s01a.getDRateAdd1()));
					result.set("dRateAdd2",
							NumConverter.addComma(l999s01a.getDRateAdd2()));
					result.set("lms9990s01_itemType", new CapAjaxFormResult(
							itemTypeMap));

				} catch (GWException e) {

				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] formatResultShowM2 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 2:
			case 3:
			case 4:
			case 5:
			case 6:
			case 7:
			case 8:
			case 9:
				try {
					/**
					 * 借款種類 <br/>
					 * A.購料借款<br/>
					 * B.外銷借款<br/>
					 * C.營運週轉借款<br/>
					 * D.貼現<br/>
					 * E.透支<br/>
					 * F.委任票據保證<br/>
					 * G.委任票據承兌<br/>
					 * H.委任保證
					 */
					switch (page) {
					case 2:
						itemType = CtrConstants.L999S01BItemType.購料借款;
						break;
					case 3:
						itemType = CtrConstants.L999S01BItemType.外銷借款;
						break;
					case 4:
						itemType = CtrConstants.L999S01BItemType.營運週轉借款;
						break;
					case 5:
						itemType = CtrConstants.L999S01BItemType.貼現;
						break;
					case 6:
						itemType = CtrConstants.L999S01BItemType.透支;
						break;
					case 7:
						itemType = CtrConstants.L999S01BItemType.委任票據保證;
						break;
					case 8:
						itemType = CtrConstants.L999S01BItemType.委任票據承兌;
						break;
					case 9:
						itemType = CtrConstants.L999S01BItemType.委任保證;
						break;
					}
					l999s01b = lms9990Service.findL999s01bByMainIdItemType(
							mainId, itemType);
					if (l999s01b == null) {
						l999s01b = new L999S01B();
					}

					result.set("item01", Util.trim(l999s01b.getItem01()));
					result.set("item02", Util.trim(l999s01b.getItem02()));
					result.set("item03",
							NumConverter.addComma(l999s01b.getItem03()));
					result.set("item03Curr",
							Util.trim(l999s01b.getItem03Curr()));
					result.set("item03Unit",
							Util.trim(l999s01b.getItem03Unit()));
					result.set("item04", Util.trim(l999s01b.getItem04()));
					result.set("item05",
							NumConverter.addComma(l999s01b.getItem05()));
					result.set("item06",
							NumConverter.addComma(l999s01b.getItem06()));
					result.set("item07",
							NumConverter.addComma(l999s01b.getItem07()));
					result.set("item08",
							NumConverter.addComma(l999s01b.getItem08()));
					result.set("item09",
							NumConverter.addComma(l999s01b.getItem09()));
					result.set("item10",
							NumConverter.addComma(l999s01b.getItem10()));
					result.set("item11", Util.trim(l999s01b.getItem11()));
					result.set("item12", Util.trim(l999s01b.getItem12()));
					result.set("item13", Util.trim(l999s01b.getItem13()));
					result.set("item14", Util.trim(l999s01b.getItem14()));
					result.set("item15", Util.trim(l999s01b.getItem15()));
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] formatResultShowM2 page 2-9 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 10:
				try {
					l999m01dList = lms9990Service.findL999m01dByMainId(mainId);
					for (L999M01D l999m01d : l999m01dList) {
						result.set("itemContent" + l999m01d.getItemType(),
								Util.trim(l999m01d.getItemContent()));
					}
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] formatResultShowM2 page 10 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			}
			// L999M01A的資料
			result.set("docStatus",
					getMessage("docStatus." + l999m01a.getDocStatus()));
			result.set("contractType",
					Util.trim(contractTypeMap.get(l999m01a.getContractType())));
			result.set(
					"custData",
					Util.trim(l999m01a.getCustId()) + " "
							+ Util.trim(l999m01a.getDupNo()) + " "
							+ Util.trim(custdata.get("CNAME")));
			result.set("contractNo", Util.trim(l999m01a.getContractNo()));

			result.set(EloanConstants.MAIN_OID, Util.trim(l999m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, Util.trim(l999m01a.getMainId()));

			if (l999m01b != null) {
				result.set("L999M01B_custName", l999m01b.getCustName());
			}
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] formatResultShowM2 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;

	}

	/**
	 * 查詢約據書資料(02.兆豐連帶保證書)
	 * 
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM03(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = null;
		L999M01A l999m01a = null;
		L999S02A l999s02a = null;
		try {
			result = new CapAjaxFormResult();
			String mainId = params.getString(EloanConstants.MAIN_ID);
			// String txCode =
			// params.getString(EloanConstants.TRANSACTION_CODE);
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999s02a = lms9990Service.findL999s02aByMainId(mainId);
			if (l999s02a == null)
				l999s02a = new L999S02A();
			result = formatResultShowM3(result, l999m01a, l999s02a,
					page);
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] queryL999m01aM03 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		return result;
	}

	/**
	 * 儲存L999M01A 約據書主檔(02.兆豐連帶保證書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01aM03(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.否));
		return L999m01aM03SaveAction(params);

	}

	/**
	 * tempSave 約據書主檔(02.兆豐連帶保證書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempsaveL999m01aM03(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.是));
		return L999m01aM03SaveAction(params);
	}

	/**
	 * 儲存L999M01A 約據書主檔(02.兆豐連帶保證書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public CapAjaxFormResult L999m01aM03SaveAction(PageParameters params) throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		L999M01A l999m01a = null;
		L999S02A l999s02a = null;
		L999M01D l999m01d = null;
		CapAjaxFormResult result = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formActionS = params.getString("ActionSForm", "");
		String formActionM = params.getString("ActionMForm", "");
		JSONObject jsonAormActionS = null;
		JSONObject jsonAormActionM = null;
		Boolean showMsg = params.getBoolean("showMsg");
		try {
			result = new CapAjaxFormResult();
			jsonAormActionS = JSONObject.fromObject(formActionS);
			jsonAormActionM = JSONObject.fromObject(formActionM);

			// 由於JPA機制 A查詢完畢後作set在查詢B A的rollback機制就被取消了
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			l999s02a = lms9990Service.findL999s02aByMainId(mainId);
			if (l999s02a == null) {
				l999s02a = new L999S02A();
			}

			switch (page) {
			case 1:
				try {
					DataParse.toBean(jsonAormActionM, l999m01a);
					l999m01a.setCourtCode(jsonAormActionS
							.getString("courtCode"));
					l999m01a.setDataUseFlag(jsonAormActionS
							.getString("dataUseFlag"));
					l999m01a.setMainId(mainId);
					DataParse.toBean(jsonAormActionS, l999s02a);
					l999s02a.setGuaAmt(this.getDecimal(jsonAormActionM
							.getString("guaAmt")));
					l999s02a.setMainId(mainId);

					// checkbox不會解析到該欄位 必須自行處理
					JSONArray dataUseItems = null;
					Integer dataUseItemcount = null;
					boolean itemResult = false;
					if (jsonAormActionS.containsKey("dataUseItem")) {
						dataUseItems = jsonAormActionS
								.optJSONArray("dataUseItem");
						if (dataUseItems != null) {
							dataUseItemcount = 0;
							dataUseItems = jsonAormActionS
									.getJSONArray("dataUseItem");
							int size = dataUseItems.size();
							for (int i = 0; i < size; i++) {
								String item = dataUseItems.getString(i);
								if ("0".equals(item)) {
									itemResult = true;
								} else {
									dataUseItemcount = dataUseItemcount
											+ dataUseItems.getInt(i);
								}
							}
							if (itemResult) {
								l999m01a.setDataUseItem(0);
							} else {
								l999m01a.setDataUseItem(dataUseItemcount);
							}
						} else if (!"".equals(jsonAormActionS
								.getString("dataUseItem"))) {
							l999m01a.setDataUseItem(jsonAormActionS
									.getInt("dataUseItem"));
						} else {
							l999m01a.setDataUseItem(null);
						}
					}
					lms9990Service.save(l999m01a, l999s02a);
					result = formatResultShowM3(result, l999m01a,
							l999s02a, page);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM03 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			case 2:
				try {
					l999m01d = lms9990Service.findL999m01dByMainIdItemType(
							mainId, L999M01DItemType.特別條款);
					if (l999m01d == null)
						l999m01d = new L999M01D();
					DataParse.toBean(jsonAormActionM, l999m01a);
					l999m01a.setMainId(mainId);
					DataParse.toBean(jsonAormActionS, l999m01d);
					l999m01d.setMainId(mainId);
					l999m01d.setItemType(L999M01DItemType.特別條款);
					l999s02a.setGuaAmt(this.getDecimal(jsonAormActionM
							.getString("guaAmt")));
					l999s02a.setMainId(mainId);
					lms9990Service.save(l999m01a, l999s02a, l999m01d);
					result = formatResultShowM3(result, l999m01a,
							l999s02a, page);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM03 page 2 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			case 3:

				break;
			default:

				break;
			}
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] saveL999m01aM03 EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;

	}

	/**
	 * 格式化顯示訊息(02.兆豐連帶保證書)
	 * 
	 * @param parent
	 *            Component
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @param l999s02a
	 *            L999S02A
	 * @param page
	 *            第幾頁
	 * @return
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShowM3(CapAjaxFormResult result, L999M01A l999m01a, L999S02A l999s02a,
			Integer page) throws CapException {
		String mainId = l999m01a.getMainId();
		Map<String, String> contractTypeMap = null;
		Map<String, Object> custdata = misCustdataService.findCustdataSelCname(
				l999m01a.getCustId(), l999m01a.getDupNo());
		L999M01D l999m01d = null;

		try {
			contractTypeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.企金約據書種類);
			if (contractTypeMap == null) {
				contractTypeMap = new LinkedHashMap<String, String>();
			}

			if (custdata == null) {
				custdata = new LinkedHashMap<String, Object>();
			}

			switch (page) {
			case 1:
				try {

					result.set("guaSDateY", Util.trim(l999s02a.getGuaSDateY()));
					result.set("guaSDateM", Util.trim(l999s02a.getGuaSDateM()));
					result.set("guaSDateD", Util.trim(l999s02a.getGuaSDateD()));
					result.set("guaEDateY", Util.trim(l999s02a.getGuaEDateY()));
					result.set("guaEDateM", Util.trim(l999s02a.getGuaEDateM()));
					result.set("guaEDateD", Util.trim(l999s02a.getGuaEDateD()));
					result.set("courtCode", Util.trim(l999m01a.getCourtCode()));

				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM03 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 2:
				try {
					l999m01d = lms9990Service.findL999m01dByMainIdItemType(
							mainId, CtrConstants.L999M01DItemType.特別條款);
					if (l999m01d == null)
						l999m01d = new L999M01D();
					result.set("itemContent",
							Util.trim(l999m01d.getItemContent()));
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM03 page 2 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 3:

				break;
			}

			// L999S02A的資料
			result.set("guaAmt", NumConverter.addComma(l999s02a.getGuaAmt()));
			// L999M01A的資料
			result.set("docStatus",
					getMessage("docStatus." + l999m01a.getDocStatus()));
			result.set("contractType",
					Util.trim(contractTypeMap.get(l999m01a.getContractType())));
			result.set("dataUseFlag", Util.trim(l999m01a.getDataUseFlag()));
			result.set(
					"custData",
					Util.trim(l999m01a.getCustId()) + " "
							+ Util.trim(l999m01a.getDupNo()) + " "
							+ Util.trim(custdata.get("CNAME")));
			result.set("contractWord", Util.trim(l999m01a.getContractWord()));
			result.set("contractNo", Util.trim(l999m01a.getContractNo()));
			result.set("dataUseItem",
					this.checkBoxOption(l999m01a.getDataUseItem()));

			result.set(EloanConstants.MAIN_OID, Util.trim(l999m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, Util.trim(l999m01a.getMainId()));
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] formatResultShowM3 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		} finally {
			if (contractTypeMap != null) {
				contractTypeMap.clear();
			}
			if (custdata != null) {
				custdata.clear();
			}
		}
		return result;

	}

	/**
	 * 查詢約據書頁面資料(03.兆豐授信約定書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM04(PageParameters params)
			throws CapException {
		// int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// String txCode = params.getString(EloanConstants.TRANSACTION_CODE);
		L999M01A l999m01a = null;
		L999M01B l999m01b = null;
		try {
			result = new CapAjaxFormResult();
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999m01b = lms9990Service.findL999m01bByMainIdType(mainId,
					L999M01BType.借款人乙方);
			if (l999m01b == null)
				l999m01b = new L999M01B();
			result = formatResultShowM4(result, l999m01a, l999m01b);
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] queryL999m01aM04 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;
	}

	/**
	 * 儲存L999M01A 約據書(03.兆豐授信約定書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01aM04(PageParameters params)
			throws CapException {
		// int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		L999M01A l999m01a = null;
		L999M01B l999m01b = null;
		L999M01D l999m01d = null;
		CapAjaxFormResult result = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		// String contractType = Util.trim(params.getString("contractType"));
		String formActionM = params.getString("ActionMForm", "");
		JSONObject jsonAormActionM = null;
		Boolean showMsg = params.getBoolean("showMsg");
		try {
			result = new CapAjaxFormResult();
			jsonAormActionM = JSONObject.fromObject(formActionM);
			// 由於JPA機制 A查詢完畢後作set在查詢B A的rollback機制就被取消了
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999m01b = lms9990Service.findL999m01bByMainIdType(mainId,
					L999M01BType.借款人乙方);
			if (l999m01b == null)
				l999m01b = new L999M01B();
			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
				l999m01d.setMainId(mainId);
				l999m01d.setItemType(L999M01DItemType.特別條款);
			}
			String itemContent = Util.trim(jsonAormActionM
					.getString("itemContent"));
			l999m01d.setItemContent(itemContent);
			DataParse.toBean(jsonAormActionM, l999m01a);
			// checkbox不會解析到該欄位 必須自行處理
			JSONArray dataUseItems = null;
			Integer dataUseItemcount = null;
			boolean itemResult = false;
			if (jsonAormActionM.containsKey("dataUseItem")) {
				dataUseItems = jsonAormActionM.optJSONArray("dataUseItem");
				if (dataUseItems != null) {
					dataUseItemcount = 0;
					dataUseItems = jsonAormActionM.getJSONArray("dataUseItem");
					int size = dataUseItems.size();
					for (int i = 0; i < size; i++) {
						String item = dataUseItems.getString(i);
						if ("0".equals(item)) {
							itemResult = true;
						} else {
							dataUseItemcount = dataUseItemcount
									+ dataUseItems.getInt(i);
						}
					}
					if (itemResult) {
						l999m01a.setDataUseItem(0);
					} else {
						l999m01a.setDataUseItem(dataUseItemcount);
					}
				} else if (!"".equals(jsonAormActionM.getString("dataUseItem"))) {
					l999m01a.setDataUseItem(jsonAormActionM
							.getInt("dataUseItem"));
				} else {
					l999m01a.setDataUseItem(null);
				}
			}
			l999m01a.setMainId(mainId);
			lms9990Service.save(l999m01a, l999m01d);
			result = formatResultShowM4(result, l999m01a, l999m01b);
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}

		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] saveL999m01aM04 EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		return result;

	}

	/**
	 * 格式化顯示訊息(03.兆豐授信約定書)
	 * 
	 * @param parent
	 *            Component
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @param l999m01b
	 *            L999M01B
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShowM4(CapAjaxFormResult result, L999M01A l999m01a, L999M01B l999m01b)
			throws CapException {
		// String mainId = l999m01a.getMainId();
		Map<String, String> contractTypeMap = null;
		Map<String, Object> custdata = null;
		Map<String, Object> custdata2 = null;
		L999M01D l999m01d = null;
		try {
			if (l999m01a != null) {
				l999m01d = lms9990Service.findL999m01dByMainIdItemType(
						l999m01a.getMainId(),
						CtrConstants.L999M01DItemType.特別條款);
			} else {
				logger.debug("[formatResultShowM4]l999m01a is null");
			}

			if (l999m01d == null) {
				logger.debug("[formatResultShowM4]l999m01d is null");
				l999m01d = new L999M01D();
			}
			result.set("itemContent", Util.trim(l999m01d.getItemContent()));

			contractTypeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.企金約據書種類);
			if (contractTypeMap == null) {
				contractTypeMap = new LinkedHashMap<String, String>();

			}
			custdata = misCustdataService.findCustdataSelCname(
					l999m01a.getCustId(), l999m01a.getDupNo());
			if (custdata == null) {
				custdata = new LinkedHashMap<String, Object>();
			}

			custdata2 = misCustdataService.findCustdataSelCname(
					l999m01b.getCustId(), l999m01b.getDupNo());
			if (custdata2 == null) {
				custdata2 = new LinkedHashMap<String, Object>();
			}

			// L999M01B的資料
			result.set(
					"custData2",
					Util.trim(l999m01b.getCustId()) + " "
							+ Util.trim(l999m01b.getDupNo()) + " "
							+ Util.trim(custdata2.get("CNAME")));
			// L999M01A的資料
			result.set("courtCode", Util.trim(l999m01a.getCourtCode()));
			result.set("docStatus",
					getMessage("docStatus." + l999m01a.getDocStatus()));
			result.set("contractType",
					Util.trim(contractTypeMap.get(l999m01a.getContractType())));
			result.set("dataUseFlag", Util.trim(l999m01a.getDataUseFlag()));
			result.set(
					"custData",
					Util.trim(l999m01a.getCustId()) + " "
							+ Util.trim(l999m01a.getDupNo()) + " "
							+ Util.trim(custdata.get("CNAME")));
			result.set("contractNo", Util.trim(l999m01a.getContractNo()));
			result.set("dataUseItem",
					this.checkBoxOption(l999m01a.getDataUseItem()));

			result.set(EloanConstants.MAIN_OID, Util.trim(l999m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, Util.trim(l999m01a.getMainId()));
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] formatResultShowM4 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		} finally {
			if (contractTypeMap != null) {
				contractTypeMap.clear();
			}
			if (custdata != null) {
				custdata.clear();
			}
			if (custdata2 != null) {
				custdata2.clear();
			}
		}

		return result;

	}

	/**
	 * 查詢約據書頁面資料(04.兆豐中長期契約書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM05(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = null;
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L999M01A l999m01a = null;
		try {
			result = new CapAjaxFormResult();
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			result = formatResultShowM5(result, l999m01a);
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] queryL999m01aM05 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		return result;
	}

	/**
	 * 儲存L999M01A 約據書(04.兆豐中長期契約書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01aM05(PageParameters params)
			throws CapException {
		L999M01A l999m01a = null;
		L999M01D l999m01d = null;
		L999S04A l999s04a = null;
		L999S04B l999s04bA = null;
		L999S04B l999s04bB = null;
		L999S04B l999s04bC = null;
		L999S04B l999s04bD = null;
		L999S04B l999s04bE = null;
		L999S04B l999s04bF = null;
		L999S04B l999s04bG = null;
		L999S04B l999s04bH = null;
		L999S04B l999s04bI = null;
		L999S04B l999s04bJ = null;
		CapAjaxFormResult result = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formActionM = params.getString("ActionMForm", "");
		JSONObject jsonAormActionM = null;
		Boolean showMsg = params.getBoolean("showMsg");
		try {
			result = new CapAjaxFormResult();
			jsonAormActionM = JSONObject.fromObject(formActionM);

			// 由於JPA機制 A查詢完畢後作set在查詢B A的rollback機制就被取消了
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null)
				l999m01a = new L999M01A();
			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null)
				l999m01d = new L999M01D();
			l999s04a = lms9990Service.findL999s04aByMainId(mainId);
			if (l999s04a == null)
				l999s04a = new L999S04A();

			/**
			 * A.授信用途<br/>
			 * B.授信金額<br/>
			 * C.動用方式及條件<br/>
			 * D.撥款方式<br/>
			 * E.償還期限及方式<br/>
			 * F.利息手續費計付<br/>
			 * G.違約金及遲延利息計付<br/>
			 * H.期前清償違約金計付<br/>
			 * I.承諾費<br/>
			 * J.其他個別商議條件
			 */
			l999s04bA = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.授信用途);
			if (l999s04bA == null)
				l999s04bA = new L999S04B();
			l999s04bB = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.授信金額);
			if (l999s04bB == null)
				l999s04bB = new L999S04B();
			l999s04bC = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.動用方式及條件);
			if (l999s04bC == null)
				l999s04bC = new L999S04B();
			l999s04bD = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.撥款方式);
			if (l999s04bD == null)
				l999s04bD = new L999S04B();
			l999s04bE = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.償還期限及方式);
			if (l999s04bE == null)
				l999s04bE = new L999S04B();
			l999s04bF = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.利息手續費計付);
			if (l999s04bF == null)
				l999s04bF = new L999S04B();
			l999s04bG = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.違約金及遲延利息計付);
			if (l999s04bG == null)
				l999s04bG = new L999S04B();
			l999s04bH = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.期前清償違約金計付);
			if (l999s04bH == null)
				l999s04bH = new L999S04B();
			l999s04bI = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.承諾費);
			if (l999s04bI == null)
				l999s04bI = new L999S04B();
			l999s04bJ = lms9990Service.findL999s04bByMainIdType(mainId,
					CtrConstants.L999S04BItemType.其他個別商議條件);
			if (l999s04bJ == null)
				l999s04bJ = new L999S04B();

			DataParse.toBean(jsonAormActionM, l999m01a);
			l999m01a.setMainId(mainId);
			l999m01a.setContractRate(this.getDecimal(jsonAormActionM
					.getString("contractRate")));
			// checkbox不會解析到該欄位 必須自行處理
			JSONArray dataUseItems = null;
			Integer dataUseItemcount = null;
			boolean itemResult = false;
			if (jsonAormActionM.containsKey("dataUseItem")) {
				dataUseItems = jsonAormActionM.optJSONArray("dataUseItem");
				if (dataUseItems != null) {
					dataUseItemcount = 0;
					dataUseItems = jsonAormActionM.getJSONArray("dataUseItem");
					int size = dataUseItems.size();
					for (int i = 0; i < size; i++) {
						String item = dataUseItems.getString(i);
						if ("0".equals(item)) {
							itemResult = true;
						} else if ("".equals(item)) {

						} else {
							dataUseItemcount = dataUseItemcount
									+ dataUseItems.getInt(i);
						}
					}
					if (itemResult) {
						l999m01a.setDataUseItem(0);
					} else {
						l999m01a.setDataUseItem(dataUseItemcount);
					}
				} else if (!"".equals(jsonAormActionM.getString("dataUseItem"))) {
					l999m01a.setDataUseItem(jsonAormActionM
							.getInt("dataUseItem"));
				} else {
					l999m01a.setDataUseItem(null);
				}
			}
			DataParse.toBean(jsonAormActionM, l999s04a);
			l999s04a.setMainId(mainId);
			l999m01d.setItemContent(Util.trim(jsonAormActionM
					.getString("itemContentAA")));
			l999m01d.setItemType(CtrConstants.L999M01DItemType.特別條款);
			l999m01d.setMainId(mainId);
			/**
			 * A.授信用途<br/>
			 * B.授信金額<br/>
			 * C.動用方式及條件<br/>
			 * D.撥款方式<br/>
			 * E.償還期限及方式<br/>
			 * F.利息手續費計付<br/>
			 * G.違約金及遲延利息計付<br/>
			 * H.期前清償違約金計付<br/>
			 * I.承諾費<br/>
			 * J.其他個別商議條件
			 */
			l999s04bA = this.setL999S04BData(l999s04bA, mainId,
					CtrConstants.L999S04BItemType.授信用途, jsonAormActionM);
			l999s04bB = this.setL999S04BData(l999s04bB, mainId,
					CtrConstants.L999S04BItemType.授信金額, jsonAormActionM);
			l999s04bC = this.setL999S04BData(l999s04bC, mainId,
					CtrConstants.L999S04BItemType.動用方式及條件, jsonAormActionM);
			l999s04bD = this.setL999S04BData(l999s04bD, mainId,
					CtrConstants.L999S04BItemType.撥款方式, jsonAormActionM);
			l999s04bE = this.setL999S04BData(l999s04bE, mainId,
					CtrConstants.L999S04BItemType.償還期限及方式, jsonAormActionM);
			l999s04bF = this.setL999S04BData(l999s04bF, mainId,
					CtrConstants.L999S04BItemType.利息手續費計付, jsonAormActionM);
			l999s04bG = this.setL999S04BData(l999s04bG, mainId,
					CtrConstants.L999S04BItemType.違約金及遲延利息計付, jsonAormActionM);
			l999s04bH = this.setL999S04BData(l999s04bH, mainId,
					CtrConstants.L999S04BItemType.期前清償違約金計付, jsonAormActionM);
			l999s04bI = this.setL999S04BData(l999s04bI, mainId,
					CtrConstants.L999S04BItemType.承諾費, jsonAormActionM);
			l999s04bJ = this.setL999S04BData(l999s04bJ, mainId,
					CtrConstants.L999S04BItemType.其他個別商議條件, jsonAormActionM);

			lms9990Service.save(l999m01a, l999m01d, l999s04a, l999s04bA,
					l999s04bB, l999s04bC, l999s04bD, l999s04bE, l999s04bF,
					l999s04bG, l999s04bH, l999s04bI, l999s04bJ);
			result = formatResultShowM5(result, l999m01a);
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] saveL999m01aM05 EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;

	}

	/**
	 * 格式化顯示訊息(04.兆豐中長期契約書)
	 * 
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShowM5(CapAjaxFormResult result, L999M01A l999m01a) throws CapException {
		String mainId = l999m01a.getMainId();

		Map<String, String> contractTypeMap = null;
		Map<String, Object> custdata = misCustdataService.findCustdataSelCname(
				l999m01a.getCustId(), l999m01a.getDupNo());
		L999M01B l999m01b1 = null;
		L999M01B l999m01b2 = null;
		List<L999M01C> l999m01cList = null;
		L999M01D l999m01d = null;
		L999S04A l999s04a = null;
		List<L999S04B> l999s04bList = null;
		StringBuffer custData3 = new StringBuffer();
		try {
			l999m01b1 = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.銀行甲方);
			if (l999m01b1 == null) {
				l999m01b1 = new L999M01B();
			}

			l999m01b2 = lms9990Service.findL999m01bByMainIdType(mainId,
					CtrConstants.L999M01BType.借款人乙方);
			if (l999m01b2 == null) {
				l999m01b2 = new L999M01B();
			}

			l999m01d = lms9990Service.findL999m01dByMainIdItemType(mainId,
					CtrConstants.L999M01DItemType.特別條款);
			if (l999m01d == null) {
				l999m01d = new L999M01D();
			}

			l999s04a = lms9990Service.findL999s04aByMainId(mainId);
			if (l999s04a == null) {
				l999s04a = new L999S04A();
			}

			l999m01cList = lms9990Service.findL999m01cByMainId(mainId);
			l999s04bList = lms9990Service.findL999s04bByMainId(mainId);
			contractTypeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.企金約據書種類);
			if (custdata == null) {
				custdata = new LinkedHashMap<String, Object>();
			}
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			IBranch branch = branchService.getBranch(user.getUnitNo());
			// L999M01B的資料
			// 甲方是
			result.set("custData1", "兆豐國際商業銀行股份有限公司" + branch.getBrName());
			result.set(
					"custData2",
					Util.trim(l999m01b2.getCustId()) + " "
							+ Util.trim(l999m01b2.getDupNo()) + " "
							+ Util.trim(l999m01b2.getCustName()));

			// L999M01C的資料
			for (L999M01C l999m01c : l999m01cList) {
				custData3.append(custData3.length() > 0 ? "、" : "");
				custData3.append(Util.trim(l999m01c.getCustId())).append(" ")
						.append(Util.trim(l999m01c.getDupNo())).append(" ")
						.append(Util.trim(l999m01c.getCustName()));
			}
			result.set("custData3", custData3.toString());

			// L999M01D的資料
			result.set("itemContentAA", Util.trim(l999m01d.getItemContent()));
			// L999S04B的資料
			for (L999S04B l999s04b : l999s04bList) {
				result.set("itemContent" + l999s04b.getItemType(),
						Util.trim(l999s04b.getItemContent()));
			}
			// L999M01A的資料
			result.set("courtCode", Util.trim(l999m01a.getCourtCode()));
			result.set("docStatus",
					getMessage("docStatus." + l999m01a.getDocStatus()));
			result.set("contractType",
					Util.trim(contractTypeMap.get(l999m01a.getContractType())));
			result.set("dataUseFlag", Util.trim(l999m01a.getDataUseFlag()));
			result.set(
					"custData",
					Util.trim(l999m01a.getCustId()) + " "
							+ Util.trim(l999m01a.getDupNo()) + " "
							+ Util.trim(custdata.get("CNAME")));
			result.set("contractNo", Util.trim(l999m01a.getContractNo()));
			result.set("contractRate", Util.trim(l999m01a.getContractRate()));
			result.set("dataUseItem",
					this.checkBoxOption(l999m01a.getDataUseItem()));
			// L999S04A的資料
			result.set("loanReason", Util.trim(l999s04a.getLoanReason()));
			result.set("totOriginal", Util.trim(l999s04a.getTotOriginal()));
			result.set("totCopy", Util.trim(l999s04a.getTotCopy()));
			result.set("aOriginal", Util.trim(l999s04a.getAOriginal()));
			result.set("aCopy", Util.trim(l999s04a.getACopy()));
			result.set("bOriginal", l999s04a.getBOriginal());
			result.set("bCopy", Util.trim(l999s04a.getBCopy()));

			result.set(EloanConstants.MAIN_OID, Util.trim(l999m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, Util.trim(l999m01a.getMainId()));
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] formatResultShowM5 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		} finally {
			if (custdata != null) {
				custdata.clear();
			}
			if (contractTypeMap != null) {
				contractTypeMap.clear();
			}
		}
		return result;
	}

	/**
	 * set L999S04B值
	 * 
	 * @param l999s04b
	 *            中長期契約書授信內容及條件檔
	 * @param mainId
	 *            文件編號
	 * @param itemType
	 *            中長期契約書授信內容及條件檔 種類
	 * @param jsonAormActionM
	 *            form 的物件
	 * @return L999S04B
	 */
	private L999S04B setL999S04BData(L999S04B l999s04b, String mainId,
			String itemType, JSONObject jsonAormActionM) {
		try {
			l999s04b.setItemContent(Util.trim(jsonAormActionM
					.getString("itemContent" + itemType)));
			l999s04b.setItemType(itemType);
			l999s04b.setMainId(mainId);
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] setL999S04BData EXCEPTION!!",
					e);
		}
		return l999s04b;
	}

	/**
	 * 處理L999M01A.dataUseItem的checkbox功能
	 * 
	 * @param dataUseItem
	 *            資料保密_同意項目 兆豐所屬公司
	 * @return String
	 */
	private String checkBoxOption(Integer dataUseItem) {
		StringBuffer dataUseItemstr = new StringBuffer();
		try {
			if (dataUseItem == null) {

			} else if (dataUseItem == 0) {
				dataUseItemstr.append("0");
			} else {
				for (int i = 1; i <= 8; i++) {
					if (Util.valueOfCheckBoxOption(dataUseItem, i)) {
						if (dataUseItemstr.length() == 0) {
							dataUseItemstr.append((int) Math.pow(2, i - 1));
						} else {
							dataUseItemstr.append("|").append(
									(int) Math.pow(2, i - 1));
						}

					}

				}
			}
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] checkBoxOption EXCEPTION!!",
					e);
		}

		return dataUseItemstr.toString();
	}

	/**
	 * 查詢利率
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		try {
			L999M01A l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			String srcMainId = "";
			if (l999m01a != null) {
				srcMainId = l999m01a.getSrcMainId();
			}
			L120M01A l120m01a = lms1205Service.findL120m01aByMainId(srcMainId);

			Map<String, String> l140m01bs = eloandbBASEService
					.findL140M01BByL120M01AMainId(srcMainId,
							lmsService.checkL140M01AItemType(l120m01a),
							UtilConstants.Cntrdoc.l140m01bItemType.利費率);
			StringBuffer temp = new StringBuffer();
			if (l140m01bs != null) {
				for (String key : l140m01bs.keySet()) {
					String drc = Util.trim(l140m01bs.get(key))
							.replace("\r\n", "<br/>").replace("\r", "<br/>")
							.replace("\n", "<br/>");
					if (Util.isEmpty(drc)) {
						continue;
					}
					temp.append(temp.length() > 0 ? "<BR/>" : "");
					temp.append("<B>");
					temp.append(key);
					temp.append(" : </B> ");
					temp.append("<BR/>");
					temp.append(drc);
				}
			}
			result.set("L140M01B_ItemDscr", temp.toString());
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] queryRate EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;

	}

	// TODO
	// *******************************************************************************
	/**
	 * 查詢約據書資料(07.信用狀開狀約定書)
	 * 
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL999m01aM09(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		CapAjaxFormResult result = null;
		L999M01A l999m01a = null;
		L999S01A l999s01a = null;
		L999S07A l999s07a = null;
		boolean hasL999S01A = false;
		try {
			result = new CapAjaxFormResult();
			String mainId = params.getString(EloanConstants.MAIN_ID);
			// String txCode =
			// params.getString(EloanConstants.TRANSACTION_CODE);
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);

			if (l999m01a == null) {
				l999m01a = new L999M01A();
			} else {
				l999s01a = lms9990Service.findL999s01aByMainId(mainId);
				if (l999s01a != null) {
					hasL999S01A = true;
				}
			}

			l999s07a = lms9990Service.findL999s07aByMainId(mainId);
			if (l999s07a == null && hasL999S01A == true) {
				// 帶入預設值

				l999s07a = new L999S07A();
				l999s07a.setMainId(mainId);
				Date useSDate = l999s01a.getUsedSDate();
				Date useEDate = l999s01a.getUsedEDate();
				if (useSDate != null) {
					String taiwanDate = CapDate.formatDate(useSDate, "YYYMMDD");
					l999s07a.setUseSDateY(taiwanDate.substring(0, 3));
					l999s07a.setUseSDateM(taiwanDate.substring(3, 5));
					l999s07a.setUseSDateD(taiwanDate.substring(5, 7));
				}
				if (useEDate != null) {
					String taiwanDate = CapDate.formatDate(useEDate, "YYYMMDD");
					l999s07a.setUseEDateY(taiwanDate.substring(0, 3));
					l999s07a.setUseEDateM(taiwanDate.substring(3, 5));
					l999s07a.setUseEDateD(taiwanDate.substring(5, 7));

					l999s07a.setDueSDateY(taiwanDate.substring(0, 3));
					l999s07a.setDueSDateM(taiwanDate.substring(3, 5));
					l999s07a.setDueSDateD(taiwanDate.substring(5, 7));
				}

				BigDecimal orgTotLoanAmt = l999s01a.getTotLoanAmt() != null ? l999s01a
						.getTotLoanAmt() : BigDecimal.ZERO;
				BigDecimal newTotLoanAmt = orgTotLoanAmt;
				if (Util.notEquals(l999s01a.getTotLoanCurr(), "")) {
					if (Util.notEquals(l999s01a.getTotLoanCurr(), "USD")) {
						// 換算為美元
						String caseBrID = MegaSSOSecurityContext.getUnitNo();
						BranchRate branchRate = lmsService
								.getBranchRate(caseBrID);
						newTotLoanAmt = branchRate.toUSDAmt(
								l999s01a.getTotLoanCurr(), orgTotLoanAmt);
					}
				}

				String TotLoanAmt = newTotLoanAmt.toString();
				if (newTotLoanAmt.compareTo(BigDecimal.ZERO) >= 0) {
					TotLoanAmt = NumConverter.addComma(newTotLoanAmt);
				}

				// l999s07a.setCoverLoanCurr(l999s01a.getTotLoanCurr());
				l999s07a.setCoverLoanCurr("USD");
				l999s07a.setCoverLoanAmt(TotLoanAmt);
				l999s07a.setTotLoanAmt(TotLoanAmt);

			}

			result = formatResultShowM9(result, l999m01a, l999s07a,
					page);
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] queryL999m01aM09 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		return result;
	}

	/**
	 * 儲存L999M01A 約據書主檔(07.信用狀開狀約定書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL999m01aM09(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.否));
		return L999m01aM09SaveAction(params);

	}

	/**
	 * tempSave 約據書主檔(07.信用狀開狀約定書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempsaveL999m01aM09(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", UtilConstants.DEFAULT.是));
		return L999m01aM09SaveAction(params);
	}

	/**
	 * 儲存L999M01A 約據書主檔(07.信用狀開狀約定書)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public CapAjaxFormResult L999m01aM09SaveAction(PageParameters params) throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		L999M01A l999m01a = null;
		L999S07A l999s07a = null;
		L999M01D l999m01d = null;
		CapAjaxFormResult result = null;
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String formActionS = params.getString("ActionSForm", "");
		String formActionM = params.getString("ActionMForm", "");
		JSONObject jsonAormActionS = null;
		JSONObject jsonAormActionM = null;
		Boolean showMsg = params.getBoolean("showMsg");
		try {
			result = new CapAjaxFormResult();
			jsonAormActionS = JSONObject.fromObject(formActionS);
			jsonAormActionM = JSONObject.fromObject(formActionM);

			// 由於JPA機制 A查詢完畢後作set在查詢B A的rollback機制就被取消了
			l999m01a = lms9990Service.findL999m01aByMainId(mainId);
			if (l999m01a == null) {
				l999m01a = new L999M01A();
			}

			l999s07a = lms9990Service.findL999s07aByMainId(mainId);
			if (l999s07a == null) {
				l999s07a = new L999S07A();
			}

			// 儲存TITLE
			try {
				DataParse.toBean(jsonAormActionM, l999m01a);
				DataParse.toBean(jsonAormActionM, l999s07a);
				DataParse.toBean(jsonAormActionS, l999s07a);
				l999m01a.setMainId(mainId);
				l999s07a.setMainId(mainId);
			} catch (Exception e) {
				logger.error(
						"[LMS9990M01FormHandler] saveL999m01aM09 Main Page EXCEPTION!!",
						e);
				throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
			}

			switch (page) {
			case 1:
				try {
					// l999s07a.setAccNo(jsonAormActionM.getString("accNo"));
					// l999s07a.setCoverLoanAmt(jsonAormActionM.getString("coverLoanAmt"));
					//
					// checkbox不會解析到該欄位 必須自行處理
					JSONArray dataUseItems = null;
					Integer dataUseItemcount = null;
					boolean itemResult = false;
					l999m01a.setDataUseFlag(jsonAormActionS
							.getString("dataUseFlag"));
					if (jsonAormActionS.containsKey("dataUseItem")) {
						dataUseItems = jsonAormActionS
								.optJSONArray("dataUseItem");
						if (dataUseItems != null) {
							dataUseItemcount = 0;
							dataUseItems = jsonAormActionS
									.getJSONArray("dataUseItem");
							int size = dataUseItems.size();
							for (int i = 0; i < size; i++) {
								String item = dataUseItems.getString(i);
								if ("0".equals(item)) {
									itemResult = true;
								} else {
									dataUseItemcount = dataUseItemcount
											+ dataUseItems.getInt(i);
								}
							}
							if (itemResult) {
								l999m01a.setDataUseItem(0);
							} else {
								l999m01a.setDataUseItem(dataUseItemcount);
							}
						} else if (!"".equals(jsonAormActionS
								.getString("dataUseItem"))) {
							l999m01a.setDataUseItem(jsonAormActionS
									.getInt("dataUseItem"));
						} else {
							l999m01a.setDataUseItem(null);
						}
					}

					l999s07a.setDataUseFlagGua(jsonAormActionS
							.getString("dataUseFlagGua"));
					if (jsonAormActionS.containsKey("dataUseItemGua")) {
						dataUseItems = jsonAormActionS
								.optJSONArray("dataUseItemGua");
						if (dataUseItems != null) {
							dataUseItemcount = 0;
							dataUseItems = jsonAormActionS
									.getJSONArray("dataUseItemGua");
							int size = dataUseItems.size();
							for (int i = 0; i < size; i++) {
								String item = dataUseItems.getString(i);
								if ("0".equals(item)) {
									itemResult = true;
								} else {
									dataUseItemcount = dataUseItemcount
											+ dataUseItems.getInt(i);
								}
							}
							if (itemResult) {
								l999s07a.setDataUseItemGua(0);
							} else {
								l999s07a.setDataUseItemGua(dataUseItemcount);
							}
						} else if (!"".equals(jsonAormActionS
								.getString("dataUseItemGua"))) {
							l999s07a.setDataUseItemGua(jsonAormActionS
									.getInt("dataUseItemGua"));
						} else {
							l999s07a.setDataUseItemGua(null);
						}
					}

					lms9990Service.save(l999m01a, l999s07a);
					result = formatResultShowM9(result, l999m01a,
							l999s07a, page);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM09 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			case 2:
				try {
					l999m01d = lms9990Service.findL999m01dByMainIdItemType(
							mainId, L999M01DItemType.特別條款);
					if (l999m01d == null)
						l999m01d = new L999M01D();

					DataParse.toBean(jsonAormActionS, l999m01d);
					l999m01d.setMainId(mainId);
					l999m01d.setItemType(L999M01DItemType.特別條款);

					lms9990Service.save(l999m01a, l999s07a, l999m01d);
					result = formatResultShowM9(result, l999m01a,
							l999s07a, page);
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM09 page 2 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}
				break;
			case 3:

				break;
			default:

				break;
			}
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}
		} catch (Exception e) {
			logger.error("[LMS9990M01FormHandler] saveL999m01aM09 EXCEPTION!!",
					e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		return result;

	}

	/**
	 * 格式化顯示訊息(07.信用狀開狀約定書)
	 * 
	 * @param parent
	 *            Component
	 * @param result
	 *            CapAjaxFormResult
	 * @param l999m01a
	 *            L999M01A
	 * @param l999s02a
	 *            L999S02A
	 * @param page
	 *            第幾頁
	 * @return
	 * @throws CapException
	 */
	private CapAjaxFormResult formatResultShowM9(CapAjaxFormResult result, L999M01A l999m01a, L999S07A l999s07a,
			Integer page) throws CapException {
		String mainId = l999m01a.getMainId();
		Map<String, String> contractTypeMap = null;
		Map<String, Object> custdata = misCustdataService.findCustdataSelCname(
				l999m01a.getCustId(), l999m01a.getDupNo());
		L999M01D l999m01d = null;

		try {
			contractTypeMap = codeTypeService
					.findByCodeType(CtrConstants.CodeType.企金約據書種類);
			if (contractTypeMap == null) {
				contractTypeMap = new LinkedHashMap<String, String>();
			}

			if (custdata == null) {
				custdata = new LinkedHashMap<String, Object>();
			}

			switch (page) {
			case 1:
				try {

					result.set("useSDateY", Util.trim(l999s07a.getUseSDateY()));
					result.set("useSDateM", Util.trim(l999s07a.getUseSDateM()));
					result.set("useSDateD", Util.trim(l999s07a.getUseSDateD()));
					result.set("useEDateY", Util.trim(l999s07a.getUseEDateY()));
					result.set("useEDateM", Util.trim(l999s07a.getUseEDateM()));
					result.set("useEDateD", Util.trim(l999s07a.getUseEDateD()));
					result.set(
							"totLoanAmt",
							NumConverter.isNumeric(l999s07a.getTotLoanAmt()) ? NumConverter
									.addComma(l999s07a.getTotLoanAmt())
									: l999s07a.getTotLoanAmt());
					result.set(
							"guaPercent",
							NumConverter.isNumeric(l999s07a.getGuaPercent()) ? NumConverter
									.addComma(l999s07a.getGuaPercent())
									: l999s07a.getGuaPercent());
					// 立約人的放主檔
					result.set("dataUseFlag",
							Util.trim(l999m01a.getDataUseFlag()));
					result.set("dataUseItem",
							this.checkBoxOption(l999m01a.getDataUseItem()));
					// 連保人的放L999S07A
					result.set("dataUseFlagGua",
							Util.trim(l999s07a.getDataUseFlagGua()));
					result.set("dataUseItemGua",
							this.checkBoxOption(l999s07a.getDataUseItemGua()));
					result.set("pSignDateY",
							Util.trim(l999s07a.getPSignDateY()));
					result.set("pSignDateM",
							Util.trim(l999s07a.getPSignDateM()));
					result.set("pSignDateD",
							Util.trim(l999s07a.getPSignDateD()));
					result.set("pContractWord",
							Util.trim(l999s07a.getPContractWord()));
					result.set("pContractNo",
							Util.trim(l999s07a.getPContractNo()));

				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM09 page 1 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 2:
				try {
					l999m01d = lms9990Service.findL999m01dByMainIdItemType(
							mainId, CtrConstants.L999M01DItemType.特別條款);
					if (l999m01d == null)
						l999m01d = new L999M01D();
					result.set("itemContent",
							Util.trim(l999m01d.getItemContent()));
				} catch (Exception e) {
					logger.error(
							"[LMS9990M01FormHandler] saveL999m01aM09 page 2 EXCEPTION!!",
							e);
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤),
							getClass());
				}

				break;
			case 3:

				break;
			}

			// L999S07A的資料
			result.set("dueSDateY", l999s07a.getDueSDateY());
			result.set("dueSDateM", l999s07a.getDueSDateM());
			result.set("dueSDateD", l999s07a.getDueSDateD());
			result.set("coverLoanCurr", l999s07a.getCoverLoanCurr());
			result.set(
					"coverLoanAmt",
					NumConverter.isNumeric(l999s07a.getCoverLoanAmt()) ? NumConverter
							.addComma(l999s07a.getCoverLoanAmt()) : l999s07a
							.getCoverLoanAmt());
			result.set("accNo", Util.trim(l999s07a.getAccNo()));

			// L999M01A的資料
			result.set("docStatus",
					getMessage("docStatus." + l999m01a.getDocStatus()));
			result.set("contractType",
					Util.trim(contractTypeMap.get(l999m01a.getContractType())));

			result.set(
					"custData",
					Util.trim(l999m01a.getCustId()) + " "
							+ Util.trim(l999m01a.getDupNo()) + " "
							+ Util.trim(custdata.get("CNAME")));
			result.set("contractWord", Util.trim(l999m01a.getContractWord()));
			result.set("contractNo", Util.trim(l999m01a.getContractNo()));

			result.set(EloanConstants.MAIN_OID, Util.trim(l999m01a.getOid()));
			result.set(EloanConstants.MAIN_ID, Util.trim(l999m01a.getMainId()));
		} catch (Exception e) {
			logger.error(
					"[LMS9990M01FormHandler] formatResultShowM3 EXCEPTION!!", e);
			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		} finally {
			if (contractTypeMap != null) {
				contractTypeMap.clear();
			}
			if (custdata != null) {
				custdata.clear();
			}
		}
		return result;

	}

	/**
	 * <pre>
	 * 刪除(授信簽報書)
	 * 
	 * @param params PageParameters
	 * @param parent Component
	 * @return IResult
	 * @throws CapException
	 * </pre>
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL999M01CList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (Util.notEquals("", Util.trim(params.getString("list")))) {
			String[] Dlist = Util.trim(params.getString("list")).split(","); // 取得list中所有資料組成的字串
			List<L999M01C> listL999m01c = lms9990Service
					.findL999M01CByOids(Dlist);

			if (listL999m01c != null && !listL999m01c.isEmpty()) {
				for (L999M01C l999m01c : listL999m01c) {
					lms9990Service.delete(l999m01c);
				}
			}
		}

		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}// ;

}
