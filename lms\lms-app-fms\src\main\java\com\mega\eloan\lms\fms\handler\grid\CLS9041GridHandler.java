/* 
 * CLS9041GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */package com.mega.eloan.lms.fms.handler.grid;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.fms.service.CLS9041M01Service;
import com.mega.eloan.lms.model.C004M01A;
import com.mega.eloan.lms.model.C004S01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 政策性留學生貸款送保彙報(S1~S3)
 * </pre>
 * 
 * @since 2012/11/05
 * <AUTHOR> Lo
 * @version <ul>
 *          <li>2012/11/01,Vector Lo,new
 *          </ul>
 */
@Scope("request")
@Controller("cls9041gridhandler")
public class CLS9041GridHandler extends AbstractGridHandler {

	@Resource
	CLS9041M01Service service;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codetypeService;

	/**
	 * 依篩選條件 查詢C004M01A資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryC004M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		// 篩選
		if (params.getBoolean("_search")) {
			String[] rptType = params.getStringArray("rptType");
			String creator = params.getString("creator");
			String bgnDate = params.getString("bgnDate");
			String endDate = params.getString("endDate");
			if (!Util.isEmpty(rptType)) {
				pageSetting.addSearchModeParameters(SearchMode.IN, "rptType",
						rptType);
			}
			if (!Util.isEmpty(creator)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"creator", creator);
			}
			if (!Util.isEmpty(bgnDate) && !Util.isEmpty(endDate)) {
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"createTime", bgnDate);
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"createTime", endDate);
			}
		}
		// 開始撈
		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL, "rptType",
				null);
		Page<? extends GenericBean> page = service.findPage(C004M01A.class,
				pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 調閱 上傳的資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {

		Page page = service.findFile(params.getString("mainId"));
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得 Q畫面
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult showQ(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MainId
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addOrderBy("brno");
		Page page = service.findPage(C004S01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new LinkedHashMap<String, IFormatter>();
		dataReformatter.put("amt", new NumericFormatter()); 
		result.setDataReformatter(dataReformatter);
		
		return result;
	}

	/**
	 * 取得政策性留學生貸款送保彙報 的選單
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult showMenu(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<CodeType> data = codetypeService.findByCodeTypeList("cls9041_item");
		
		return new CapGridResult(data, data.size());
	}
}
