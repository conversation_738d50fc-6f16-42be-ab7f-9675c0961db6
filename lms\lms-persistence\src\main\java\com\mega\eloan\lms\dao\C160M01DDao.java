/* 
 * C160M01DDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160M01D;

/** 先行動用呈核及控制表檔 **/
public interface C160M01DDao extends IGenericDao<C160M01D> {

	C160M01D findByOid(String oid);
	
	List<C160M01D> findByMainId(String mainId);
	
	C160M01D findByUniqueKey(String mainId);

	List<C160M01D> findByIndex01(String mainId);
}