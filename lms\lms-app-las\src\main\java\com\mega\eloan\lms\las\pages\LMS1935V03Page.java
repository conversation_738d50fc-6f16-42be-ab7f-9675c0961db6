package com.mega.eloan.lms.las.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;

/**
 * 稽核室 稽核工作底稿 待覆核
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1935v03")
public class LMS1935V03Page extends AbstractEloanInnerView {

	public LMS1935V03Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(LasDocStatusEnum.稽核室_待覆核);
		
		List<EloanPageFragment> list = new ArrayList<>();
		list.add(LmsButtonEnum.PrintAllAudit);
		list.add(LmsButtonEnum.PrintAllBill);
		
		if(this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.ReviewAll);
		}
		
//		UPGRADE：
//		TRANSACTION_CODE在已註解的原LasButtonPanel中，雖引入但並未影響邏輯
//		下列model.put()僅留著以免日後需要TRANSACTION_CODE做邏輯調整用
//		model.put("transactionCode", params.getString(EloanConstants.TRANSACTION_CODE));
		addToButtonPanel(model, list);

		renderJsI18N(LMS1935V01Page.class);
		//前端發動checkPrint後，會使用到1945的內容在前端做訊息組合。
		renderJsI18N(LMS1945V01Page.class);
	}
	
	//UPGRADE
//	@Override
//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/las/LMS1935V03.js",
//				"pagejs/las/LASCOMMON.js" };
//	}

}
