package com.mega.eloan.lms.rpt.report.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生授信 有 無 不適用 銀行法,44,45所稱與本行有利害關係人PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms9515r02rptservice")
public class LMS9515R02RptServiceImpl extends AbstractReportService {

	@Resource
	BranchService branch;

	@Resource
	LMS9515Service service9515;

	@Resource
	LMSService lmsService;
	
	@Override
	public String getReportTemplateFileName() {
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		return "report/rpt/LMS9515R07_" + LMSUtil.getLocale().toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) throws CapException {
		Properties prop = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = null;
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		String mainId = params.getString("mainId","");
		String brNo = params.getString("brNo","");
		String printType = params.getString("printType","");
		String caseDept= params.getString("caseDept","3");
		String apprYY = null;
		String apprMM = null;
		Date dataDate = null;
		
		apprYY = "";
		apprMM = "";
		 
		L784M01A l784m01a = null;
		List<L784S07A> l784s07aList = null;
		try {
			l784m01a = service9515.findL784m01aByMainId(mainId);
			if(l784m01a == null)
				l784m01a = new L784M01A();
			
			if("all".equals(printType)){
				//l784s07aList = service9515.findL784S07AByMainId(mainId);
				l784s07aList = service9515.findL784S07A(mainId, null, null, null, caseDept);
			}else if("allByAppr".equals(printType)){
				
				dataDate = TWNDate.valueOf(params.getString("startDate") + "-01");
				apprYY = TWNDate.toTW(dataDate).split("/")[0];
				apprMM = TWNDate.toTW(dataDate).split("/")[1];
				//System.out.println("mainId="+mainId);
				//System.out.println("apprYY="+apprYY);
				//System.out.println("apprMM="+apprMM);
				l784s07aList = service9515.findL784S07AByMainIdApprYM(mainId,apprYY,apprMM,
						caseDept);
			}else{
				//l784s07aList = service9515.findL784S07AByMainIdBrNo(mainId,brNo);
				l784s07aList = service9515.findL784S07A(mainId, brNo, null, null, caseDept);
			}
			prop = MessageBundleScriptCreator.getComponentResource(LMS9515V01Page.class);

			locale = LMSUtil.getLocale();

			if("allByAppr".equals(printType)){
				titleRows = this.setL9515type7DataTitleRowsByApprYM(titleRows, l784s07aList,prop,printType);
			}else{
				titleRows = this.setL9515type7DataTitleRows(titleRows, l784s07aList,prop,printType);
			}
			
			rptVariableMap = this.setL9515type7DataRptVariableMap(rptVariableMap,
					l784s07aList,l784m01a.getRandomCode(),brNo,prop,printType,apprYY,apprMM,caseDept);
			
			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00", "");
			rptVariableMap.put("LOGOSHOW", logoPath);
			
			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// reportTools.checkVariableExist("C:/test.txt", rptVariableMap);
			// this.reportTools.setTestMethod(true);
		}finally{
			
		}
	}
	
	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException 
	 */
	private List<Map<String, String>> setL9515type7DataTitleRows(
			List<Map<String, String>> titleRows,
			List<L784S07A> list,Properties propV01,String printType) throws CapException {
		try{
			
			Map<String, String> mapInTitleRows = null;
			//月份
			String months[] = { propV01.getProperty("month.01"), propV01.getProperty("month.02"), propV01.getProperty("month.03"), propV01.getProperty("month.04"), propV01.getProperty("month.05"), propV01.getProperty("month.06"), propV01.getProperty("month.07"), propV01.getProperty("month.08"),
					propV01.getProperty("month.09"), propV01.getProperty("month.10"), propV01.getProperty("month.11"), propV01.getProperty("month.12") };
			BigDecimal totalColumn02 = BigDecimal.ZERO;
			BigDecimal totalColumn03 = BigDecimal.ZERO;
			BigDecimal totalColumn04 = BigDecimal.ZERO;
			BigDecimal totalColumn05 = BigDecimal.ZERO;
			BigDecimal totalColumn06 = BigDecimal.ZERO;
			BigDecimal totalColumn07 = BigDecimal.ZERO;
			BigDecimal totalColumn08 = BigDecimal.ZERO;
			BigDecimal totalColumn09 = BigDecimal.ZERO;
			BigDecimal totalColumn10 = BigDecimal.ZERO;
			BigDecimal totalColumn11 = BigDecimal.ZERO;
			BigDecimal totalColumn12 = BigDecimal.ZERO;
			BigDecimal totalColumn13 = BigDecimal.ZERO;
			BigDecimal totalColumn14 = BigDecimal.ZERO;
			BigDecimal totalColumn15 = BigDecimal.ZERO;
			BigDecimal totalColumn16 = BigDecimal.ZERO;
			BigDecimal totalColumn17 = BigDecimal.ZERO;
			BigDecimal totalColumn18 = BigDecimal.ZERO;
			BigDecimal totalColumn19 = BigDecimal.ZERO;
			BigDecimal totalColumn20 = BigDecimal.ZERO;
			BigDecimal totalColumn21 = BigDecimal.ZERO;
			for (int i = 1; i <= 12; i++) {
				BigDecimal column02 = null;
				BigDecimal column03 = null;
				BigDecimal column04 = null;
				BigDecimal column05 = null;
				BigDecimal column06 = null;
				BigDecimal column07 = null;
				BigDecimal column08 = null;
				BigDecimal column09 = null;
				BigDecimal column10 = null;
				BigDecimal column11 = null;
				BigDecimal column12 = null;
				BigDecimal column13 = null;
				BigDecimal column14 = null;
				BigDecimal column15 = null;
				BigDecimal column16 = null;
				BigDecimal column17 = null;
				BigDecimal column18 = null;
				BigDecimal column19 = null;
				BigDecimal column20 = null;
				BigDecimal column21 = null;
				mapInTitleRows = this.setColumnMap2(months[i - 1]);
				for(L784S07A l784s07a : list){
					if (String.format("%02d", i).equals(Util.trim(l784s07a.getApprMM()))) {
						if("all".equals(printType)){
							//新做
							column02 = this.bigAdd(column02, LMSUtil.toBigDecimal(l784s07a.getCItem1Rec()));
							column03 = this.bigAdd(column03, LMSUtil.toBigDecimal(l784s07a.getCItem1Amt()));
							//續約
							column04 = this.bigAdd(column04, LMSUtil.toBigDecimal(l784s07a.getCItem2Rec()));
							column05 = this.bigAdd(column05, LMSUtil.toBigDecimal(l784s07a.getCItem2Amt()));
							//變更條件
							column06 = this.bigAdd(column06, LMSUtil.toBigDecimal(l784s07a.getCItem3Rec()));
							column07 = this.bigAdd(column07, LMSUtil.toBigDecimal(l784s07a.getCItem3Amt()));
							//逾放展期、轉正常
							column08 = this.bigAdd(column08, LMSUtil.toBigDecimal(l784s07a.getCItem12Rec()));
							column09 = this.bigAdd(column09, LMSUtil.toBigDecimal(l784s07a.getCItem12Amt()));
							//無擔保授信
							column12 = this.bigAdd(column12, LMSUtil.toBigDecimal(l784s07a.getCItem4Rec()));
							column13 = this.bigAdd(column13, LMSUtil.toBigDecimal(l784s07a.getCItem4Amt()));
							//擔保授信
							column14 = this.bigAdd(column14, LMSUtil.toBigDecimal(l784s07a.getCItem5Rec()));
							column15 = this.bigAdd(column15, LMSUtil.toBigDecimal(l784s07a.getCItem5Amt()));
							//合計
							column10 = this.bigAdd(column02, column04);
							column10 = this.bigAdd(column10, column06);
							column10 = this.bigAdd(column10, column08);
							
							column11 = this.bigAdd(column03, column05);
							column11 = this.bigAdd(column11, column07);
							column11 = this.bigAdd(column11, column09);

							column16 = this.bigAdd(column16, LMSUtil.toBigDecimal(l784s07a.getCItem6Rec()));
							column17 = this.bigAdd(column17, LMSUtil.toBigDecimal(l784s07a.getCItem7Rec()));
							column18 = this.bigAdd(column18, LMSUtil.toBigDecimal(l784s07a.getCItem8Rec()));
							column19 = this.bigAdd(column19, LMSUtil.toBigDecimal(l784s07a.getCItem9Rec()));
							column20 = this.bigAdd(column20, LMSUtil.toBigDecimal(l784s07a.getCItem10Rec()));
							column21 = this.bigAdd(column21, LMSUtil.toBigDecimal(l784s07a.getCItem11Rec()));
						}else{
							//新做
							column02 = LMSUtil.toBigDecimal(l784s07a.getCItem1Rec());
							column03 = LMSUtil.toBigDecimal(l784s07a.getCItem1Amt());
							//續約
							column04 = LMSUtil.toBigDecimal(l784s07a.getCItem2Rec());
							column05 = LMSUtil.toBigDecimal(l784s07a.getCItem2Amt());
							//變更條件
							column06 = LMSUtil.toBigDecimal(l784s07a.getCItem3Rec());
							column07 = LMSUtil.toBigDecimal(l784s07a.getCItem3Amt());
							//逾放展期、轉正常
							column08 = LMSUtil.toBigDecimal(l784s07a.getCItem12Rec());
							column09 = LMSUtil.toBigDecimal(l784s07a.getCItem12Amt());
							//無擔保授信
							column12 = LMSUtil.toBigDecimal(l784s07a.getCItem4Rec());
							column13 = LMSUtil.toBigDecimal(l784s07a.getCItem4Amt());
							//擔保授信
							column14 = LMSUtil.toBigDecimal(l784s07a.getCItem5Rec());
							column15 = LMSUtil.toBigDecimal(l784s07a.getCItem5Amt());
							//合計
							column10 = this.bigAdd(column02, column04);
							column10 = this.bigAdd(column10, column06);
							column10 = this.bigAdd(column10, column08);
							
							column11 = this.bigAdd(column03, column05);
							column11 = this.bigAdd(column11, column07);
							column11 = this.bigAdd(column11, column09);
							
							column16 = LMSUtil.toBigDecimal(l784s07a.getCItem6Rec());
							column17 = LMSUtil.toBigDecimal(l784s07a.getCItem7Rec());
							column18 = LMSUtil.toBigDecimal(l784s07a.getCItem8Rec());
							column19 = LMSUtil.toBigDecimal(l784s07a.getCItem9Rec());
							column20 = LMSUtil.toBigDecimal(l784s07a.getCItem10Rec());
							column21 = LMSUtil.toBigDecimal(l784s07a.getCItem11Rec());
						}
					}
				}
				totalColumn02 = this.bigAdd(totalColumn02, column02);
				totalColumn03 = this.bigAdd(totalColumn03, column03);
				totalColumn04 = this.bigAdd(totalColumn04, column04);
				totalColumn05 = this.bigAdd(totalColumn05, column05);
				totalColumn06 = this.bigAdd(totalColumn06, column06);
				totalColumn07 = this.bigAdd(totalColumn07, column07);
				totalColumn08 = this.bigAdd(totalColumn08, column08);
				totalColumn09 = this.bigAdd(totalColumn09, column09);
				totalColumn10 = this.bigAdd(totalColumn10, column10);
				totalColumn11 = this.bigAdd(totalColumn11, column11);
				totalColumn12 = this.bigAdd(totalColumn12, column12);
				totalColumn13 = this.bigAdd(totalColumn13, column13);
				totalColumn14 = this.bigAdd(totalColumn14, column14);
				totalColumn15 = this.bigAdd(totalColumn15, column15);
				totalColumn16 = this.bigAdd(totalColumn16, column16);
				totalColumn17 = this.bigAdd(totalColumn17, column17);
				totalColumn18 = this.bigAdd(totalColumn18, column18);
				totalColumn19 = this.bigAdd(totalColumn19, column19);
				totalColumn20 = this.bigAdd(totalColumn20, column20);
				totalColumn21 = this.bigAdd(totalColumn21, column21);
				mapInTitleRows.put("ReportBean.column02", this.formatBigDecimal(column02));
				mapInTitleRows.put("ReportBean.column03", this.formatBigDecimal(column03));
				mapInTitleRows.put("ReportBean.column04", this.formatBigDecimal(column04));
				mapInTitleRows.put("ReportBean.column05", this.formatBigDecimal(column05));
				mapInTitleRows.put("ReportBean.column06", this.formatBigDecimal(column06));
				mapInTitleRows.put("ReportBean.column07", this.formatBigDecimal(column07));
				mapInTitleRows.put("ReportBean.column08", this.formatBigDecimal(column08));
				mapInTitleRows.put("ReportBean.column09", this.formatBigDecimal(column09));
				mapInTitleRows.put("ReportBean.column10", this.formatBigDecimal(column10));
				mapInTitleRows.put("ReportBean.column11", this.formatBigDecimal(column11));
				mapInTitleRows.put("ReportBean.column12", this.formatBigDecimal(column12));
				mapInTitleRows.put("ReportBean.column13", this.formatBigDecimal(column13));
				mapInTitleRows.put("ReportBean.column14", this.formatBigDecimal(column14));
				mapInTitleRows.put("ReportBean.column15", this.formatBigDecimal(column15));
				mapInTitleRows.put("ReportBean.column16", this.formatBigDecimal(column16));
				mapInTitleRows.put("ReportBean.column17", this.formatBigDecimal(column17));
				mapInTitleRows.put("ReportBean.column18", this.formatBigDecimal(column18));
				mapInTitleRows.put("ReportBean.column19", this.formatBigDecimal(column19));
				mapInTitleRows.put("ReportBean.column20", this.formatBigDecimal(column20));
				mapInTitleRows.put("ReportBean.column21", this.formatBigDecimal(column21));
				titleRows.add(mapInTitleRows);
			}
			mapInTitleRows = this.setColumnMap2(propV01.getProperty("month.total"));
			mapInTitleRows.put("ReportBean.column02", this.formatBigDecimal(totalColumn02));
			mapInTitleRows.put("ReportBean.column03", this.formatBigDecimal(totalColumn03));
			mapInTitleRows.put("ReportBean.column04", this.formatBigDecimal(totalColumn04));
			mapInTitleRows.put("ReportBean.column05", this.formatBigDecimal(totalColumn05));
			mapInTitleRows.put("ReportBean.column06", this.formatBigDecimal(totalColumn06));
			mapInTitleRows.put("ReportBean.column07", this.formatBigDecimal(totalColumn07));
			mapInTitleRows.put("ReportBean.column08", this.formatBigDecimal(totalColumn08));
			mapInTitleRows.put("ReportBean.column09", this.formatBigDecimal(totalColumn09));
			mapInTitleRows.put("ReportBean.column10", this.formatBigDecimal(totalColumn10));
			mapInTitleRows.put("ReportBean.column11", this.formatBigDecimal(totalColumn11));
			mapInTitleRows.put("ReportBean.column12", this.formatBigDecimal(totalColumn12));
			mapInTitleRows.put("ReportBean.column13", this.formatBigDecimal(totalColumn13));
			mapInTitleRows.put("ReportBean.column14", this.formatBigDecimal(totalColumn14));
			mapInTitleRows.put("ReportBean.column15", this.formatBigDecimal(totalColumn15));
			mapInTitleRows.put("ReportBean.column16", this.formatBigDecimal(totalColumn16));
			mapInTitleRows.put("ReportBean.column17", this.formatBigDecimal(totalColumn17));
			mapInTitleRows.put("ReportBean.column18", this.formatBigDecimal(totalColumn18));
			mapInTitleRows.put("ReportBean.column19", this.formatBigDecimal(totalColumn19));
			mapInTitleRows.put("ReportBean.column20", this.formatBigDecimal(totalColumn20));
			mapInTitleRows.put("ReportBean.column21", this.formatBigDecimal(totalColumn21));
			titleRows.add(mapInTitleRows);
			

		}catch(Exception e){
			LOGGER.debug("setL9515type7DataTitleRows exception" , e);
			throw new CapException();
		}
		return titleRows;
	}
	
	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException 
	 */
	private List<Map<String, String>> setL9515type7DataTitleRowsByApprYM(
			List<Map<String, String>> titleRows,
			List<L784S07A> list,Properties propV01,String printType) throws CapException {
		try{
			
			Map<String, String> mapInTitleRows = null;
			//月份
			/*
			String months[] = { propV01.getProperty("month.01"), propV01.getProperty("month.02"), propV01.getProperty("month.03"), propV01.getProperty("month.04"), propV01.getProperty("month.05"), propV01.getProperty("month.06"), propV01.getProperty("month.07"), propV01.getProperty("month.08"),
					propV01.getProperty("month.09"), propV01.getProperty("month.10"), propV01.getProperty("month.11"), propV01.getProperty("month.12") };
			*/
			BigDecimal totalColumn02 = BigDecimal.ZERO;
			BigDecimal totalColumn03 = BigDecimal.ZERO;
			BigDecimal totalColumn04 = BigDecimal.ZERO;
			BigDecimal totalColumn05 = BigDecimal.ZERO;
			BigDecimal totalColumn06 = BigDecimal.ZERO;
			BigDecimal totalColumn07 = BigDecimal.ZERO;
			BigDecimal totalColumn08 = BigDecimal.ZERO;
			BigDecimal totalColumn09 = BigDecimal.ZERO;
			BigDecimal totalColumn10 = BigDecimal.ZERO;
			BigDecimal totalColumn11 = BigDecimal.ZERO;
			BigDecimal totalColumn12 = BigDecimal.ZERO;
			BigDecimal totalColumn13 = BigDecimal.ZERO;
			BigDecimal totalColumn14 = BigDecimal.ZERO;
			BigDecimal totalColumn15 = BigDecimal.ZERO;
			BigDecimal totalColumn16 = BigDecimal.ZERO;
			BigDecimal totalColumn17 = BigDecimal.ZERO;
			BigDecimal totalColumn18 = BigDecimal.ZERO;
			BigDecimal totalColumn19 = BigDecimal.ZERO;
			BigDecimal totalColumn20 = BigDecimal.ZERO;
			BigDecimal totalColumn21 = BigDecimal.ZERO;
			
			mapInTitleRows = this.setColumnMap2("");
			
			for(L784S07A l784s07a : list){
			//for (int i = 1; i <= list.size() ; i++) {
				String brno = null;
				String column01 = null;
				BigDecimal column02 = null;
				BigDecimal column03 = null;
				BigDecimal column04 = null;
				BigDecimal column05 = null;
				BigDecimal column06 = null;
				BigDecimal column07 = null;
				BigDecimal column08 = null;
				BigDecimal column09 = null;
				BigDecimal column10 = null;
				BigDecimal column11 = null;
				BigDecimal column12 = null;
				BigDecimal column13 = null;
				BigDecimal column14 = null;
				BigDecimal column15 = null;
				BigDecimal column16 = null;
				BigDecimal column17 = null;
				BigDecimal column18 = null;
				BigDecimal column19 = null;
				BigDecimal column20 = null;
				BigDecimal column21 = null;
				//mapInTitleRows = this.setColumnMap2(months[i - 1]);
				
				//for(L784S07A l784s07a : list){
					//if (String.format("%02d", i).equals(Util.trim(l784s07a.getApprMM()))) {
						//if("allByAppr".equals(printType)){
							//分行別
				brno = Util.nullToSpace(l784s07a.getBrNo());
				column01 =  brno+" "+Util.nullToSpace(branch.getBranchName(Util.trim(brno))).replaceAll("分行", "");
				 
				//新做
				column02 = LMSUtil.toBigDecimal(l784s07a.getCItem1Rec());
				column03 = LMSUtil.toBigDecimal(l784s07a.getCItem1Amt());
				//續約
				column04 = LMSUtil.toBigDecimal(l784s07a.getCItem2Rec());
				column05 = LMSUtil.toBigDecimal(l784s07a.getCItem2Amt());
				//變更條件
				column06 = LMSUtil.toBigDecimal(l784s07a.getCItem3Rec());
				column07 = LMSUtil.toBigDecimal(l784s07a.getCItem3Amt());
				//逾放展期、轉正常
				column08 = LMSUtil.toBigDecimal(l784s07a.getCItem12Rec());
				column09 = LMSUtil.toBigDecimal(l784s07a.getCItem12Amt());
				//無擔保授信
				column12 = LMSUtil.toBigDecimal(l784s07a.getCItem4Rec());
				column13 = LMSUtil.toBigDecimal(l784s07a.getCItem4Amt());
				//擔保授信
				column14 = LMSUtil.toBigDecimal(l784s07a.getCItem5Rec());
				column15 = LMSUtil.toBigDecimal(l784s07a.getCItem5Amt());
				//合計
				column10 = this.bigAdd(column02, column04);
				column10 = this.bigAdd(column10, column06);
				column10 = this.bigAdd(column10, column08);
				
				column11 = this.bigAdd(column03, column05);
				column11 = this.bigAdd(column11, column07);
				column11 = this.bigAdd(column11, column09);
				
				column16 = LMSUtil.toBigDecimal(l784s07a.getCItem6Rec());
				column17 = LMSUtil.toBigDecimal(l784s07a.getCItem7Rec());
				column18 = LMSUtil.toBigDecimal(l784s07a.getCItem8Rec());
				column19 = LMSUtil.toBigDecimal(l784s07a.getCItem9Rec());
				column20 = LMSUtil.toBigDecimal(l784s07a.getCItem10Rec());
				column21 = LMSUtil.toBigDecimal(l784s07a.getCItem11Rec());
						//}
					//}
				//}
				totalColumn02 = this.bigAdd(totalColumn02, column02);
				totalColumn03 = this.bigAdd(totalColumn03, column03);
				totalColumn04 = this.bigAdd(totalColumn04, column04);
				totalColumn05 = this.bigAdd(totalColumn05, column05);
				totalColumn06 = this.bigAdd(totalColumn06, column06);
				totalColumn07 = this.bigAdd(totalColumn07, column07);
				totalColumn08 = this.bigAdd(totalColumn08, column08);
				totalColumn09 = this.bigAdd(totalColumn09, column09);
				totalColumn10 = this.bigAdd(totalColumn10, column10);
				totalColumn11 = this.bigAdd(totalColumn11, column11);
				totalColumn12 = this.bigAdd(totalColumn12, column12);
				totalColumn13 = this.bigAdd(totalColumn13, column13);
				totalColumn14 = this.bigAdd(totalColumn14, column14);
				totalColumn15 = this.bigAdd(totalColumn15, column15);
				totalColumn16 = this.bigAdd(totalColumn16, column16);
				totalColumn17 = this.bigAdd(totalColumn17, column17);
				totalColumn18 = this.bigAdd(totalColumn18, column18);
				totalColumn19 = this.bigAdd(totalColumn19, column19);
				totalColumn20 = this.bigAdd(totalColumn20, column20);
				totalColumn21 = this.bigAdd(totalColumn21, column21);
				
				mapInTitleRows = this.setColumnMap2("");
				mapInTitleRows.put("ReportBean.column01",  column01);
				mapInTitleRows.put("ReportBean.column02", this.formatBigDecimal(column02));
				mapInTitleRows.put("ReportBean.column03", this.formatBigDecimal(column03));
				mapInTitleRows.put("ReportBean.column04", this.formatBigDecimal(column04));
				mapInTitleRows.put("ReportBean.column05", this.formatBigDecimal(column05));
				mapInTitleRows.put("ReportBean.column06", this.formatBigDecimal(column06));
				mapInTitleRows.put("ReportBean.column07", this.formatBigDecimal(column07));
				mapInTitleRows.put("ReportBean.column08", this.formatBigDecimal(column08));
				mapInTitleRows.put("ReportBean.column09", this.formatBigDecimal(column09));
				mapInTitleRows.put("ReportBean.column10", this.formatBigDecimal(column10));
				mapInTitleRows.put("ReportBean.column11", this.formatBigDecimal(column11));
				mapInTitleRows.put("ReportBean.column12", this.formatBigDecimal(column12));
				mapInTitleRows.put("ReportBean.column13", this.formatBigDecimal(column13));
				mapInTitleRows.put("ReportBean.column14", this.formatBigDecimal(column14));
				mapInTitleRows.put("ReportBean.column15", this.formatBigDecimal(column15));
				mapInTitleRows.put("ReportBean.column16", this.formatBigDecimal(column16));
				mapInTitleRows.put("ReportBean.column17", this.formatBigDecimal(column17));
				mapInTitleRows.put("ReportBean.column18", this.formatBigDecimal(column18));
				mapInTitleRows.put("ReportBean.column19", this.formatBigDecimal(column19));
				mapInTitleRows.put("ReportBean.column20", this.formatBigDecimal(column20));
				mapInTitleRows.put("ReportBean.column21", this.formatBigDecimal(column21));
				titleRows.add(mapInTitleRows);
			}
			mapInTitleRows = this.setColumnMap2(propV01.getProperty("month.total"));
			mapInTitleRows.put("ReportBean.column02", this.formatBigDecimal(totalColumn02));
			mapInTitleRows.put("ReportBean.column03", this.formatBigDecimal(totalColumn03));
			mapInTitleRows.put("ReportBean.column04", this.formatBigDecimal(totalColumn04));
			mapInTitleRows.put("ReportBean.column05", this.formatBigDecimal(totalColumn05));
			mapInTitleRows.put("ReportBean.column06", this.formatBigDecimal(totalColumn06));
			mapInTitleRows.put("ReportBean.column07", this.formatBigDecimal(totalColumn07));
			mapInTitleRows.put("ReportBean.column08", this.formatBigDecimal(totalColumn08));
			mapInTitleRows.put("ReportBean.column09", this.formatBigDecimal(totalColumn09));
			mapInTitleRows.put("ReportBean.column10", this.formatBigDecimal(totalColumn10));
			mapInTitleRows.put("ReportBean.column11", this.formatBigDecimal(totalColumn11));
			mapInTitleRows.put("ReportBean.column12", this.formatBigDecimal(totalColumn12));
			mapInTitleRows.put("ReportBean.column13", this.formatBigDecimal(totalColumn13));
			mapInTitleRows.put("ReportBean.column14", this.formatBigDecimal(totalColumn14));
			mapInTitleRows.put("ReportBean.column15", this.formatBigDecimal(totalColumn15));
			mapInTitleRows.put("ReportBean.column16", this.formatBigDecimal(totalColumn16));
			mapInTitleRows.put("ReportBean.column17", this.formatBigDecimal(totalColumn17));
			mapInTitleRows.put("ReportBean.column18", this.formatBigDecimal(totalColumn18));
			mapInTitleRows.put("ReportBean.column19", this.formatBigDecimal(totalColumn19));
			mapInTitleRows.put("ReportBean.column20", this.formatBigDecimal(totalColumn20));
			mapInTitleRows.put("ReportBean.column21", this.formatBigDecimal(totalColumn21));
			titleRows.add(mapInTitleRows);
			

		}catch(Exception e){
			LOGGER.debug("setL9515type7DataTitleRowsByApprYM exception" , e);
			throw new CapException();
		}
		return titleRows;
	}
	
	private BigDecimal bigAdd(BigDecimal number1,BigDecimal number2){
		if(number1 == null){
			number1 = BigDecimal.ZERO;
		}
		if(number2 == null){
			number2 = BigDecimal.ZERO;
		}
		return number1.add(number2);
	}
	
	private String formatBigDecimal(BigDecimal number){
		if(number == null){
			return "";
		}else if("0".equals(String.valueOf(number))){ 
			return "";
		}else{
			return NumConverter.addComma(number);
		}
	}

	/**
	 * 初始化map 資料，將所有的rportBean的資料初使化，避免少了而產生exception
	 * 
	 * @return Map<String, String> 多值MAP
	 */
	private Map<String, String> setColumnMap2(String month) {
		Map<String, String> values = new LinkedHashMap<String, String>();
		values.put("ReportBean.column01", month);
		for (int i = 2; i <= 60; i++) {
			values.put("ReportBean.column" + String.format("%02d", i), "");
		}
		return values;
	}
	
	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param dataCollection2
	 * @return
	 * @throws CapException 
	 */
	private Map<String, String> setL9515type7DataRptVariableMap(
			Map<String, String> rptVariableMap,
			List<L784S07A> l784s07aList,String randomCode,String brno,Properties propV01,String printType,String apprYY,String apprMM,String caseDept) throws CapException {
		try{
			L784S07A l784s07a = null;
			if(l784s07aList.size() > 0){
				l784s07a = l784s07aList.get(0);
			}
			if(l784s07a == null){
				l784s07a = new L784S07A();
			}
			if("all".equals(printType)){
				rptVariableMap.put("L784M01A.BRNO", propV01.getProperty("L784M01a.totalBrNo"));
			}else if("allByAppr".equals(printType)){
				rptVariableMap.put("L784M01A.BRNO", propV01.getProperty("L784M01a.totalBrNo"));
			}
			else{
				rptVariableMap.put("L784M01A.BRNO", Util.nullToSpace(branch.getBranchName(Util.trim(brno))));
			}

			rptVariableMap.put("L784M01A.YEAR",Util.nullToSpace(Util.trim(apprYY)));
			
			if("allByAppr".equals(printType)){
				rptVariableMap.put("L784M01A.MONTH",Util.nullToSpace(Util.trim(apprMM)));
			}else{
				rptVariableMap.put("L784M01A.MONTH","");
			}
			
			rptVariableMap.put("L784M01A.CASEDEPT",propV01.getProperty("L784M01a.docType"+caseDept));
			rptVariableMap.put("L784M01A.RANDOMCODE",randomCode);
		}catch(Exception e){
			throw new CapException();
		}
		return rptVariableMap;
	}
}
