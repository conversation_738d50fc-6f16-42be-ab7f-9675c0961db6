/* 
 * L140S10C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 個金其他敘作條件樣板內容資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S10C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S10C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 業務分類<p/>
	 */
	@Size(max=3)
	@Column(name="BIZCAT", length=3, columnDefinition="CHAR(3)")
	private String bizCat;

	/**
	 * 項目代號<p/>
	 * codeType 規則 => "template_item" +  bizCat第一碼
	 */
	@Size(max=2)
	@Column(name="BIZITEM", length=2, columnDefinition="CHAR(2)")
	private String bizItem;

	/**
	 * 內容代號<p/>
	 * codeType 規則 => "cont" +  bizCat第一碼 + bizItem
	 */
	@Size(max=5)
	@Column(name="CONTNO", length=5, columnDefinition="CHAR(5)")
	private String contNo;

	/** 內容 **/
	@Size(max=2048)
	@Column(name="CONTENT", length=2048, columnDefinition="VARCHAR(2048)")
	private String content;
	
	/** 內容順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="seq", columnDefinition="DECIMAL(3,0)")
	private Integer seq;
	

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L140S09A.oid
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L140S09A.oid
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得業務分類 **/
	public String getBizCat() {
		return this.bizCat;
	}
	/** 設定業務分類 **/
	public void setBizCat(String value) {
		this.bizCat = value;
	}

	/** 取得項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public String getBizItem() {
		return this.bizItem;
	}
	/** 設定項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public void setBizItem(String value) {
		this.bizItem = value;
	}

	/** 取得內容代號<p/>codeType 規則 => "cont" +  bizCat第一碼 + bizItem **/
	public String getContNo() {
		return this.contNo;
	}
	/** 設定內容代號<p/>codeType 規則 => "cont" +  bizCat第一碼 + bizItem **/
	public void setContNo(String value) {
		this.contNo = value;
	}
	
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	
	public Integer getSeq() {
		return seq;
	}
	public void setSeq(Integer seq) {
		this.seq = seq;
	}
}
