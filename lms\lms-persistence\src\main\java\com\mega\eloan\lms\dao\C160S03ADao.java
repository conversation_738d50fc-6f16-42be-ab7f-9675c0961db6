package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S03A;

/** 整批自動開戶分戶檔 **/
public interface C160S03ADao extends IGenericDao<C160S03A> {

	public C160S03A findByOid(String oid);

	public C160S03A findByUk(String cntrNo, int seqNo);
	
	public int findMaxSeqNoByCntrNo(String cntrNo);
	
	public List<C160S03A> findByMainIdOrderBySeqNo(String mainId);
	
	public List<C160S03A> findByCntrNo_hasMemo(String cntrNo);
}