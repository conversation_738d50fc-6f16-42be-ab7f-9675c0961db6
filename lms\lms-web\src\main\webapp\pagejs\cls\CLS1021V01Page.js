pageJsInit(function(){
    $(function(){
        var grid = $("#gridview").iGrid({
            handler: 'cls1021gridhandler',
            height: 350,
            width: 785,
            autowidth: false,
            action: "queryC102m01a",
            postData: {
                docStatus: viewstatus
            },
            rowNum: 15,
            sortname: "custId",
            sortorder: "desc|desc",
            multiselect: true,
            colModel: [{
                colHeader: i18n.cls1021v01['C102M01A.custId'],//"主要借款人統編",
                name: 'custId',
                width: 80,
                align: "left",
                sortable: true,
                formatter: 'click',
                onclick: openDoc
            }, {
                colHeader: i18n.cls1021v01['C102M01A.custName'],//"主要借款人",
                name: 'custName',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1021v01['C102M01A.cntrNo'],//"額度序號",
                name: 'cntrNo',
                width: 150,
                sortable: true
            }, {
                colHeader: i18n.cls1021v01['C102M01A.aLoanAC'],//"放款帳號",
                name: 'aLoanAC',
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1021v01['C102M01A.aLoanDate'],//"首撥日期",
                name: 'aLoanDate',
                width: 70,
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                },
                align: "left",
                sortable: true
            }, {
                colHeader: i18n.cls1021v01['C102M01A.creator'],//"分行經辦",
                name: 'creator',
                width: 80,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'docURL',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridview").getRowData(rowid);
                openDoc(null, null, data);
            }
        });
        function openDoc(cellvalue, options, rowObject){
            $.form.submit({
                url: '..' + rowObject.docURL + '/01',
                data: {
                    formAction: "queryC102m01a",
                    oid: rowObject.oid,
                    mainId: rowObject.mainId,
                    mainOid: rowObject.oid,
                    mainDocStatus: viewstatus,
                    txCode: txCode
                },
                target: rowObject.oid
            });
        }
        $("#buttonPanel").find("#btnDelete").click(function(){
            var rows = $("#gridview").getGridParam('selarrrow');
            var data = [];
            
            if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
                return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            }
            //confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    for (var i in rows) {
                        data.push($("#gridview").getRowData(rows[i]).oid);
                    }
					//debug
                    console.log("deleted data:"+ data);
					$.ajax({
                      handler: "cls1021m01formhandler",
                      data: {
                          formAction: "deleteC102m01a",
                          oids: data
                      },
                  }).done(function(){
                      $("#gridview").trigger("reloadGrid");
                  });
                }
            });
        }).end().find("#btnAdd").click(function(){
			$.ajax({
	               handler: "cls1021m01formhandler",
	               data: {
	                   formAction: "newc102m01a"
	               },
	           }).done(function(obj){
                       $.form.submit({
                           url: '../lms/cls1021m01/01',
                           data: {
                               formAction: "queryC102m01a",
                               oid: obj.oid,
                               mainOid: obj.oid,
                               mainDocStatus: viewstatus,
                               txCode: txCode
                           },
                           target: "_blank"	                       
	                   });
	               });
        }).end().find("#btnView").click(function(){
            var id = $("#gridview").getGridParam('selrow');
            if (!id) {
                // action_004=請先選擇需「調閱」之資料列
                return CommonAPI.showMessage(i18n.def["action_004"]);
            }
            if (id.length > 1) {
                CommonAPI.showMessage(i18n.cls1021m01["C102M01a.error1"]);
            }
            else {
                var result = $("#gridview").getRowData(id);
                openDoc(null, null, result);
            }
        }).end().find("#btnFilter").click(function(){
            openFilterBox();
        }).end().find("#btnUseFirstTable").click(function(){
            openUseFirstTable();
        }).end().find("#btnLogeIN").click(function(){
            openLogeIN();
        }).end().find("#btnDataFix").click(function(){
            var id = $("#gridview").getGridParam('selrow');
            if (!id) {
                // action_004=請先選擇需「調閱」之資料列
                return CommonAPI.showMessage(i18n.def["action_004"]);
            }
            var result = $("#gridview").getRowData(id);
            openCntrCaseBox(result.oid);
        });
    });
});
