pageJsInit(function() {
	$(function() {
		var grid = $("#gridview").iGrid({
			handler: 'lms1025gridhandler',
			height: 350,
			action: 'queryC121M01A',
			sortname: 'ratingDate|caseNo',
			sortorder: 'desc|desc',
			rowNum: 17,
			rownumbers: true,
			shrinkToFit: false,
			postData: {
				docStatus: viewstatus
			},
			colModel: [{ name: 'oid', hidden: true }
				, { name: 'mainId', hidden: true }
				, { name: 'ratingId', hidden: true }
				, { colHeader: i18n.lms1025v01["C121M01A.ratingDate"], width: 70, name: 'ratingDate', sortable: true }
				, { colHeader: i18n.lms1025v01["C121M01A.caseNo"], width: 180, name: 'caseNo', sortable: true, formatter: 'click', onclick: openDoc }
				, { colHeader: i18n.lms1025v01["C121M01A.custId"], width: 90, name: 'custId' }
				, { colHeader: ' ', width: 10, name: 'dupNo', sortable: true }
				, { colHeader: i18n.lms1025v01["C121M01A.custName"], width: 120, name: 'custName', sortable: true }
				, { colHeader: i18n.lms1025v01["label.lnPeriod"], name: 'lnPeriod', align: "left", width: 80, sortable: false }
				, { colHeader: i18n.lms1025v01["C121M01A.varVer"], width: 60, name: 'varVer', sortable: true }
				//澳洲的評等，沒有開放輸入 location ,{colHeader : i18n.lms1025v01["label.cmsLocation"],name : 'cmsLocation', align : "left", width : 200, sortable : false}
				, { colHeader: i18n.def["lastUpdater"], width: 110, name: 'updater', sortable: true }
				, { colHeader: i18n.def["lastUpdateTime"], width: 120, name: 'updateTime', sortable: true }
			]
		});

		$("#buttonPanel").find("#btnFilter").click(function() {
			openFilterBox();
		}).end().find("#btnAdd").click(function() {
			$.ajax({
				handler: "lms1025m01formhandler",
				type: "POST",
				dataType: "json",
				data: {
					formAction: "addRatingDoc"
				},
				}).done(function(obj_param) {
					$.form.submit({
						url: '../lms/lms1025m01/01',
						data: obj_param,
						target: "_blank"
					});
			});
		}).end().find("#btnDelete").click(function() {
			var rows = $("#gridview").getGridParam('selrow');
			var mainOid = "";
			if (rows != 'undefined' && rows != null && rows != 0) {
				var data = $("#gridview").getRowData(rows);
				mainOid = data.oid;
			}
			if (mainOid == "") {
				CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				return;
			}
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
				if (b) {
					$.ajax({
						handler: "lms1025m01formhandler",
						type: "POST",
						dataType: "json",
						data: {
							'formAction': "delRatingDoc",
							'mainOid': mainOid,
							'mainDocStatus': viewstatus
						},
						}).done(function(obj) {
							$("#gridview").trigger("reloadGrid");
					});
				}
			});
		}).end().find("#btnDeliver").click(function() {
			var rows = $("#gridview").getGridParam('selrow');
			var mainOid = "";
			if (rows != 'undefined' && rows != null && rows != 0) {
				var data = $("#gridview").getRowData(rows);
				mainOid = data.oid;
			}
			if (mainOid == "") {
				CommonAPI.showMessage(i18n.def["grid_selector"]);
				return;
			}
			CommonAPI.confirmMessage("是否test上傳DW?", function(b) {
				if (b) {
					$.ajax({
						handler: "lms1015m01formhandler",
						type: "POST",
						dataType: "json",
						data: {
							'formAction': "testRatingDocDW",
							'mainOid': mainOid,
							'mainDocStatus': viewstatus
						},
						}).done(function(obj) {
							API.showMessage("上傳成功");
					});
				}
			});
		});

		function openDoc(cellvalue, options, rowObject) {
			var postData = {
				'mainOid': rowObject.oid,
				'mainId': rowObject.mainId,
				'mainDocStatus': viewstatus
			}
			if (viewstatus == '05O') {
				postData = $.extend(postData, { 'noOpenDoc': true });
			}
			var atPanel = "02";
			if (viewstatus == '02O' || viewstatus == '05O') {
				atPanel = "06";
			}
			$.form.submit({ url: '../lms/lms1025m01/' + atPanel, data: postData, target: rowObject.oid });
		}
	});
});

function openFilterBox(){
	var _id = "_div_filter";
	var _form = _id+"_form";
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		dyna.push("<table class='tb2'>");
		dyna.push("<tr><td class='hd1' nowrap>"+i18n.lms1025v01['C121M01A.custId']+"</td><td><input type='text' name='custId' value='' maxlength='10'></td></tr>");		
		dyna.push("</table>");
		dyna.push("</form>");
		
		dyna.push("</div>");
		
	     $('body').append(dyna.join(""));
	}
	//clear data
	$("#"+_form).reset();
	
	$("#"+_id).thickbox({ title: i18n.def['query'], width: 400, height: 185, modal: true,
        valign: "bottom", align: "center", i18n: i18n.def,
        buttons: {
            "sure": function(){            	
            	if (true){	
					$.thickbox.close();
					
					$("#gridview").jqGrid("setGridParam", {
						postData : $("#"+_form).serializeData(),
						page : 1,
						search : true
					}).trigger("reloadGrid");
				}
            },
            "cancel": function(){
            	 $.thickbox.close();
            }
        }
    });
}