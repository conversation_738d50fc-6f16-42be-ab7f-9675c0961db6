/* 
 * LMS9011V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;


@Controller@RequestMapping(path = "/fms/lms9011v00")
public class LMS9011V00Page extends AbstractEloanInnerView {

	public LMS9011V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		// 加上Button

		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Modify,
				LmsButtonEnum.Delete);
		renderJsI18N(LMS9011V00Page.class);
	}// ;


	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9011V00Page.js" };
	}
}
