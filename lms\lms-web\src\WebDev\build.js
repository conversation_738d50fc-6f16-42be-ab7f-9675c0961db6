/*
根據原架構jawr.properties 所引入的js改寫
jawr.js.bundle.baseJs.id=/bundles/baseJs.jsx
jawr.js.bundle.baseJs.child.names=jquery, jqueryui, jqueryuii18n, jquerywatch, json, validate, qtip, dotimeout, blockui, base64, jqgrid, jqscrollto,corner, uniform,ajaxfileupload,purify

jawr.js.bundle.capcommonJs.id=/bundles/capcommonJs.jsx
jawr.js.bundle.capcommonJs.child.names=capi18n,thickbox,cproperties,citem,common,ilog,ijqgrid,itabs,cvalidate,properties,samplejs

jawr.js.bundle.ckeditor.id=/bundles/ckeditor.jsx
jawr.js.bundle.ckeditor.child.names=ckeditorfilter,ckeditorbase,ckeditoradapters,ckeditorinit

jawr.js.bundle.yuiswfstore.id=/bundles/yuiswfstore.jsx
jawr.js.bundle.yuiswfstore.child.names=yahoodomevent,element,eventdelegate,cookie,swf,swfstore,yuiswfinit

jquery.jsx、jqueryui.jsx 這二個只用在ELoanLoginPage.html，且已經改成獨自import
*/
({
  paths : {
    // main.js
    'libjs' : 'js/libjs',
    'basejs' : 'js/basejs',
    'mega.eloan.properties' : 'js/common/mega.eloan.properties',//(lms update)
	'mega.eloan.lms' : 'js/common/mega.eloan.lms',
	
    // libjs.js (ces版本)
    'jquery' : 'js/lib/jquery/3.6.0/jquery-3.6.0',
    'jqueryui' : 'js/lib/jquery-ui/1.13.2/jquery-ui',
    'qtip' : 'js/lib/jquery.qtip/3.0.3/jquery.qtip-3.0.3',
    'blockui' : 'js/lib/jquery.blockUI/2.70.0/jquery.blockUI-2.70.0',
    'jqgrid' : 'js/lib/jqGrid/5.5.5/jquery.jqGrid-5.5.5',
    'jqgridi18n' : 'js/lib/jqGrid/5.5.5/i18n/grid.locale-en',
    'corner' : 'js/lib/jquery.corner/2.13/jquery.corner-2.13',
    'uniform' : 'js/lib/jquery.uniform/4.3.0/jquery.uniform.standalone',
    'validate' : 'js/lib/jquery-validation/1.19.5/jquery.validate',
    'jqscrollto' : 'js/lib/jquery.scrollTo/2.1.3/jquery.scrollTo',
    'ajaxfileupload' : 'js/lib/ajaxfileupload/ajaxfileupload',
    'stomp' : 'js/lib/websocket/stomp',
	
	// libjs.js (lms版本)
	/*
	baseJs.jsx
	jquery : jawr.js.bundle.jquery.mappings=jquery/development/jquery-1.5.2.js
	jqueryui : jawr.js.bundle.jqueryui.mappings=/jquery/ui/jquery-ui-1.8.4.custom.min.js
	jqueryuii18n : 	jawr.js.bundle.jqueryuii18n.mappings=/jquery/ui/jquery-ui-i18n.js
	jquerywatch : jawr.js.bundle.jquerywatch.mappings=/jquery/plugin/jquery-watch.js
	json : jawr.js.bundle.json.mappings=/js/json/json2.js
	validate : jawr.js.bundle.validate.mappings=/jquery/plugin/validate/jquery.validate.1.7.js
	qtip : jawr.js.bundle.qtip.mappings=/jquery/plugin/qtipv200/jquery.qtip.js
	dotimeout : jawr.js.bundle.dotimeout.mappings=/jquery/plugin/dotimeout/jquery.ba-do-timeout.js
	blockui : jawr.js.bundle.blockui.mappings=/jquery/plugin/blockui/jquery.blockUI.js
	base64 : jawr.js.bundle.base64.mappings=/jquery/plugin/base64/jquery.base64.js
	jqgrid : jawr.js.bundle.ijqgrid.mappings=/js/common/common.jqgrid.js
	jqscrollto : jawr.js.bundle.jqscrollto.mappings=/jquery/plugin/scrollto/jquery.scrollto.js
	corner : jawr.js.bundle.corner.mappings=/jquery/plugin/jquery.corner.js
	uniform : jawr.js.bundle.uniform.mappings=/jquery/plugin/uniform/jquery.uniform.min.js
	ajaxfileupload : jawr.js.bundle.ajaxfileupload.mappings=/jquery/plugin/ajaxfileupload/ajaxfileupload.js
	purify : jawr.js.bundle.purify.mappings=/js/purify/3.0.6/purify.js
	*/
	
    // basejs.js (lms版本)
    'common.i18n' : 'js/commonCes/common.i18n', //(ces版本)
	'thickbox' : 'js/lib/thickbox/thickbox', //(ces版本)
    'common.properties' : 'js/common/common.properties', //(lms update)
    'common.item' : 'js/common/common.item',
    'common' : 'js/common/common',  //(lms update 和ces落差甚大，重點js 測試重點)
    'common.message' : 'js/common/common.message',
    'common.jqgrid' : 'js/commonCes/common.jqgrid', //(ces版本 文件異動紀錄的Grid要用CES版本的)
    'common.tabcrollable' : 'js/commonCes/common.tabcrollable',//(ces版本 應該可以直接拿ces的來用，如果開發tab類js有異常，再來比對lms版本)
    'common.validate' : 'js/common/common.validate', //(lms update)
    'common.ckeditor.init' : 'js/commonCes/common.ckeditor.init', //(ces版本，和lms落差太大...使用lms版本會異常之後再研究...)
    'common.yuiswf.init' : 'js/common/common.yuiswf.init',//(lms update)
	
	// capcommonJs.jsx
	/*
	capi18n : jawr.js.bundle.capi18n.mappings=capi18n:/i18n/tw/com/iisi/cap/i18n/JsI18nAjaxHandler
	thickbox : jawr.js.bundle.thickbox.mappings=/js/thickbox/thickbox.js (和ces重複)
	cproperties : jawr.js.bundle.cproperties.mappings=/js/common/common.properties.js
	citem : jawr.js.bundle.citem.mappings=/js/common/common.item.js
	common : jawr.js.bundle.common.mappings=/js/common/common.js
	ilog : jawr.js.bundle.ilog.mappings=/js/common/common.message.js
	ijqgrid : jawr.js.bundle.ijqgrid.mappings=/js/common/common.jqgrid.js
	itabs : jawr.js.bundle.itabs.mappings=/js/common/common.tabcrollable.js
	cvalidate : jawr.js.bundle.cvalidate.mappings=/js/common/common.validate.js
	properties : jawr.js.bundle.properties.mappings=/js/common/mega.eloan.properties.js
	samplejs : jawr.js.bundle.samplejs.mappings=/js/common/mega.eloan.sample.js
	*/
	'mega.eloan.sample' : 'js/common/mega.eloan.sample',//(lms update  thickbox ckeditor寫在裡面，ces版本移到common.js，同ces移過去，但2邊有落差，用ces版本 )
	
	// ckeditor.jsx
	/*
	ckeditorfilter : jawr.js.bundle.ckeditorfilter.mappings=/js/common/common.ckeditor.filter.js
	ckeditorbase : jawr.js.bundle.ckeditorbase.mappings=/js/ckeditor-3.5.2/ckeditor.js
	ckeditoradapters : jawr.js.bundle.ckeditoradapters.mappings=js/ckeditor-3.5.2/adapters/jquery.js
	ckeditorinit : jawr.js.bundle.ckeditorinit.mappings=/js/common/common.ckeditor.init.js
	*/
	
	// yuiswfstore.jsx
	/*
	yahoodomevent : jawr.js.bundle.yahoodomevent.mappings=/js/yui/yahoo-dom-event/yahoo-dom-event.js
	element : jawr.js.bundle.element.mappings=/js/yui/element/element.js
	eventdelegate : jawr.js.bundle.eventdelegate.mappings=/js/yui/event-delegate/event-delegate.js
	cookie : jawr.js.bundle.cookie.mappings=/js/yui/cookie/cookie.js
	swf : jawr.js.bundle.swfstore.mappings=/js/yui/swfstore/swfstore.js
	swfstore : jawr.js.bundle.swfstore.mappings=/js/yui/swfstore/swfstore.js
	yuiswfinit : jawr.js.bundle.yuiswfinit.mappings=/js/common/common.yuiswf.init.js
	*/
	'yahoo-dom-event' : 'js/yui/yahoo-dom-event/yahoo-dom-event',
	'element' : 'js/yui/element/element',
	'event-delegate' : 'js/yui/event-delegate/event-delegate',
	'cookie' : 'js/yui/cookie/cookie',
	'swfstore' : 'js/yui/swfstore/swfstore'
	
  }
})
