/*_
 * Copyright (c) 2006 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

/**
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.operation.step;

import java.util.Map;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.operation.OperationStep;

/**
 * <pre>
 * AbstractCustomizeOpStep
 * 自定義Operation step
 * </pre>
 * 
 * @since 2010/11/24
 * <AUTHOR>
 * @version $Id$
 * @version
 *          <ul>
 *          <li>2010/11/24,iristu,new
 *          </ul>
 */
public abstract class AbstractCustomizeOpStep implements OperationStep {

    /**
     * 步驟名稱
     */
    String name;

    /**
     * 所有步驟
     */
    Map<String, String> ruleMap;

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#getName()
     */
    @Override
    public String getName() {
        return name;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#getRuleMap()
     */
    @Override
    public Map<String, String> getRuleMap() {
        return ruleMap;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#handleException(java.lang.Exception)
     */
    @Override
    public String handleException(Exception e) throws CapException {
        return ERROR;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#setName(java.lang.String)
     */
    @Override
    public void setName(String name) {
        this.name = name;
    }

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.operation.OperationStep#setRuleMap(java.util.Map)
     */
    @Override
    public void setRuleMap(Map<String, String> ruleMap) {
        this.ruleMap = ruleMap;
    }

}
