package com.mega.eloan.lms.fms.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import javax.annotation.Resource;

import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dao.L820M01WDao;
import com.mega.eloan.lms.model.L820M01W;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.Util;

@Service("lms8200RpaFileService")
public class LMS8200RPAFileServiceImpl implements FileDownloadService {
	
	protected static final Logger LOGGER = LoggerFactory.getLogger(LMS8200RPAFileServiceImpl.class);

	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	DocFileDao docFileDao;
	
	@Resource
	L820M01WDao l820m01wDao;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		
		String oid = Util.trim(params.getString("oid"));
		String dataSource = Util.trim(params.getString("dataSource"));
		String docFileOid = null;
		
		L820M01W l820m01w = l820m01wDao.findByOid(oid);
		docFileOid = l820m01w.getDocfileoid();
		
		ISearch search = docFileDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.OID, docFileOid);
		DocFile docFile = docFileDao.findUniqueOrNone(search);
		
		byte[] bytes = null;
		if (docFile != null && docFile.getSrcFileName().indexOf("jpg") > -1) {
			File file = docFileService.getRealFile(docFile);
			bytes = FileUtil.readAsByteArray(file);
		}
		
		return bytes;
	}
}
