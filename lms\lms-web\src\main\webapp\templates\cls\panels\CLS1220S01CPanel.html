<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S01CPanel')</script>
            <!--======================================================-->
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docinfo'}"></th:block>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                        	<tr>
							 	<td width="15%" class="hd1">
                                    <th:block th:text="#{'doc.branchName'}">
                                        <!--  分行名稱-->
                                    </th:block>&nbsp;&nbsp;
                                </td>
                                <td width="25%">
                                    <span id="ownBrId" ></span><span id="ownBrName" ></span>
                                </td>
                                <td class="hd2 rt">
                                    <th:block th:text="#{'C122M01A.modifyDocStatus'}"></th:block>
                                </td>
                                <td width="35%" >                                    
                                	<span id="docStatusCN" class="rmemo"></span>
								</td>								 	
							</tr>
							<tr>
                                <td class="hd1">
                                    <th:block th:text="#{'C122M01A.custId'}">身分證統編 </th:block>&nbsp;
                                </td>
                                <td>
                                    <span id="custId" name="custId" ></span>&nbsp;&nbsp;<span id="dupNo" name="dupNo" ></span>
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'C122M01A.custName'}">借款人姓名</th:block>&nbsp;
                                </td>
                                <td>
                                    <span id="custName" name="custName" ></span>
                                </td>
                            </tr>			
                            <tr>                               
                                <td class="hd2 rt">                                   
                                    <th:block th:text="#{'C122M01A.applyTS'}">
                                        客戶申請日期
                                    </th:block>
                                </td>
                                <td>
                                    <span id="applyTS" name="applyTS"></span>
                                </td>	
								<td class="hd2 rt" nowrap>
                                	<span class="color-red">＊</span>
                                    <th:block th:text="#{'C122M01A.applyDocId'}">申請文件編號</th:block>
                                </td>
                                <td >
                                    <span id="applyDocId" name="applyDocId" class="required"></span>
                                </td>							
                            </tr>
							<tr>
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.idCardIssueArea'}">身分證發證地</th:block>
                                </td>
                                <td>
                                    <span id="idCardIssueArea" name="idCardIssueArea"></span>&nbsp;&nbsp;
                                </td>
								
                                <td rowspan='6' class="hd2 rt">
                                	<th:block th:text="#{'doc.attchGrid'}">上傳檔案</th:block>
                                </td>
                                <td rowspan='6' >            
			   						<div id='attchGrid' style='margin-left:7px;'>
			   						</div>			
                                </td>
							</tr>
							<tr>
                               <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.idCardIssueDate'}">身分證發證日期</th:block>
                                </td>
                                <td>
                                    <span id="idCardIssueDate" name="idCardIssueDate"></span>&nbsp;&nbsp;
                                </td>
							</tr>
							<tr>
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.idCardChgFlag'}">身分證換補資料</th:block>
                                </td>
                                <td>
                                	<label style="letter-spacing:0px;cursor:pointer;">
                                		<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='0'><th:block th:text="#{'C122M01A.idCardChgFlag.0'}">初發</th:block>
									</label>
									&nbsp;&nbsp;
									<label style="letter-spacing:0px;cursor:pointer;">
										<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='1'><th:block th:text="#{'C122M01A.idCardChgFlag.1'}">補發</th:block>
									</label>
									&nbsp;&nbsp;
									<label style="letter-spacing:0px;cursor:pointer;">
										<input type="radio" id="idCardChgFlag" name="idCardChgFlag" value='2'><th:block th:text="#{'C122M01A.idCardChgFlag.2'}">換發</th:block>
									</label>
									&nbsp;&nbsp;
                                </td>
							</tr>
							<tr>
								<td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.idCardPhoto'}">身分證有無照片</th:block>
                                </td>
                                <td>
                                    <input type="radio" id="idCardPhoto" name="idCardPhoto" codeType="Common_YesNo" itemStyle="sort:desc" />
                                </td>
							</tr>
							
							<tr>
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.applyAmt'}">申請金額</th:block>
                                </td>
                                <td colspan='1'>
                                    <span id="applyCurr" name="applyCurr" class='color-red'></span>&nbsp;&nbsp;
									<span id="applyAmt" name="applyAmt" class='rt color-red'></span><span class='color-red'>萬</span>
                                </td>
							</tr>							
							<tr>
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.maturity'}">借款年限</th:block>
                                </td>
                                <td colspan='1'>
                                    <span id="maturity" name="maturity"  class='color-red'></span><span class='color-red'><th:block th:text="#{'label.year'}">年</th:block></span>
                                </td>								
							</tr>
							<tr>
                                <td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.purpose'}">借款用途</th:block>
                                </td>
                                <td colspan='3'>
                                    <div id="purposeTemp" ></div>
                                </td>
							</tr>
							<tr>	
								<td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.resource'}">還款財源</th:block>
                                </td>
                                <td colspan='3'>
                                    <div id="resourceTemp" ></div>
                                </td>
							</tr>
                            
							<tr>	
								<td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.extYear'}">寬限期</th:block>
                                </td>
                                <td>
                                    <input type='radio' id="nowExtend" name="nowExtend" value='N'><th:block th:text="#{'nohave'}">無</th:block>&nbsp;&nbsp;&nbsp;&nbsp;
									<input type='radio' id="nowExtend" name="nowExtend" value='Y'><th:block th:text="#{'have'}">有</th:block>&nbsp;
									<span id="extYear" name="extYear" ></span><th:block th:text="#{'label.year'}">年</th:block>
                                </td>
								<td class="hd2 rt">
                                	<th:block th:text="#{'C122M01A.marketingStaff'}">行銷人員代碼</th:block>
                                </td>
                                <td>
                                    <span id="marketingStaff" name="marketingStaff" ></span>&nbsp;
                                </td>
                            </tr>
							<tr>                                                        
                                <td class="hd1" nowrap>
                                    <th:block th:text="#{'C122M01A.notifyWay'}">貸款利息通知方式</th:block>
                                </td>
                                <td nowrap >
                                	<label><input type="radio" id="notifyWay" name="notifyWay" value="1" /><th:block th:text="#{'C122M01A.notifyWay.1'}"></th:block></label>&nbsp;&nbsp;
									<label><input type="radio" id="notifyWay" name="notifyWay" value="2" /><th:block th:text="#{'C122M01A.notifyWay.2'}"></th:block></label>&nbsp;&nbsp;	
									<label><input type="radio" id="notifyWay" name="notifyWay" value="3" /><th:block th:text="#{'C122M01A.notifyWay.3'}"></th:block></label>&nbsp;&nbsp;
                                </td>                                                     
                                <td class="hd1" nowrap>
                                    <th:block th:text="#{'C122M01A.contactEmpNo'}">聯絡行員</th:block>
									<div>
										<th:block th:text="#{'C122M01A.contactEmpNo_memo'}">客戶聯絡窗口</th:block>	
									</div>
                                </td>
                                <td>
                                	<input type="text" id="contactEmpNo" name="contactEmpNo" maxlength="6" size="7"> &nbsp; <span id="contactEmpNm" ></span>										
                                </td>
							</tr>							
							<tr >
                                <td class="hd2 rt">
                                    <th:block th:text="#{'C122M01A.applyStatus'}">申貸案件狀態</th:block>
                                </td>
                                <td nowrap>                                    
                                	<span id="applyDocStatusCN" class=" rmemo"></span>
									<th:block th:if="${area_show_n_applyStatus_visible}">變更為
						        		<select id="n_applyStatus" name="n_applyStatus" style="color:#900;">
						        			<option value="0B0"><th:block th:text="#{'C122M01A.applyStatus.0B0'}">審核中</th:block></option>
											<option value="Z01"><th:block th:text="#{'C122M01A.applyStatus.Z01'}">不承做</th:block></option>
											<!-- <option value="Z02"><th:block th:text="#{'C122M01A.applyStatus.Z02'}">轉臨櫃</th:block></option> -->
											<option value="Z03"><th:block th:text="#{'C122M01A.applyStatus.Z03'}">已核貸</th:block></option>
                                            <option value="Z04"><th:block th:text="#{'C122M01A.applyStatus.Z04'}">待補件</th:block></option>
                                            <option value="Z05"><th:block th:text="#{'C122M01A.applyStatus.Z05'}">動審表已覆核</th:block></option>
						        		</select>
						        	</th:block>
									<th:block th:if="${area_hide_n_applyStatus_visible}"><input id="n_applyStatus" name="n_applyStatus" type="hidden">
						        	</th:block>
									<span id='approveAmtInfo' class='onlyZ03 color-red'>
										TWD<input type='text' id='approveAmt' name='approveAmt' size='4' maxlength='7' class="numeric number" style='color: #990000;' fraction="0" integer="6">萬
									</span>
                            	</td>      
                            
                                <td class="hd2 rt " nowrap>
    	                            <th:block th:utext="#{'C122M01A.notifyCust_split'}">本次申貸結果是否 e-mail通知客戶</th:block>
                                </td>
                                <td>
                                    <label><input type="radio" id="notifyCust" name="notifyCust" value="Y" checked="checked"/>是</label>
									<label><input type="radio" name="notifyCust" value="N"/>否</label>
                        		</td>
                            </tr>							
							 <tr>
							 	<td class="hd2 rt">
                                    <span id="prefixMemo" ></span><th:block th:text="#{'label.notifyMemo'}">備註</th:block>
                                </td>
								<td valign="top" colspan='3'>
                            		<textarea name="notifyMemo" id="notifyMemo" cols="72" rows="3" maxlengthC='90' class="txt_mult" style="width:400px;height:60px;"></textarea>
                        		</td>
							 </tr>	
							<tr id='tr_approvedCntrInfo' class='onlyZ03'>
                                <td class="hd2 rt">
									 <th:block th:text="#{'label.approvedCntrInfo'}">核貸資料</th:block>	
                                </td>
								<td colspan='3'>
								<div style='overflow-x:auto;'>

									<div>
										<th:block th:if="${hs_c122s01a_batchNo_1_visible}">											
											<select id="c122s01a_batchNo" name="c122s01a_batchNo">											
											</select>
							        	</th:block>
										
										<th:block th:if="${hs_c122s01a_batchNo_2_visible}">											
											<input type='hidden' value='-1' id="c122s01a_batchNo" name="c122s01a_batchNo">
							        	</th:block>
										&nbsp;&nbsp;&nbsp;&nbsp;
										<button type="button" id="btImpCntrInfo" name="btImpCntrInfo"><span class="text-only"><th:block th:text="#{'label.btImpCntrInfo'}">引進</th:block></span></button>
										<button type="button" id="btDelCntrInfo" name="btDelCntrInfo"><span class="text-only"><th:block th:text="#{'label.btDelCntrInfo'}">刪除</th:block></span></button>
									</div>									
									<div id='gridCntrInfo'></div>
									
								</div>
                                </td>
                            </tr>							
                        </tbody>
                    </table>
                </fieldset>
				
			<!--=======================-->
            
                <fieldset>
                    <legend>
                        <th:block th:text="#{'doc.docUpdateLog'}">
                            文件異動紀錄
                        </th:block>
                    </legend>
                    <div class="funcContainer">
                        <div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.creator'}">
                                        文件建立者
                                    </th:block>
                                </td>
                                <td width="30%">
                                    <span id="creator"></span>&nbsp;(<span id="createTime"></span>)
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.lastUpdater'}">
                                        最後異動者
                                    </th:block>
                                </td>
                                <td>
                                    <span id="updater"></span>&nbsp;(<span id="updateTime"></span>)
                                </td>
                            </tr>
                            <tr>
                                <td class="hd1">
                                    &nbsp;
                                </td>
                                <td>
                                    &nbsp;
                                </td>
                                <td class="hd1">
                                    <th:block th:text="#{'doc.docCode'}">報表亂碼</th:block>
                                </td>
                                <td>
                                    <span id="randomCode"></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
            	<div id='div_gridImpCntrInfo' style='display:none'>
					<div id='gridImpCntrInfo'></div>
				</div>
            </th:block>
    </body>
</html>