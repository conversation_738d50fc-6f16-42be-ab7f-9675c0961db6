/* 
 * MisELF503ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF503Service;

/**
 * <pre>
 * 利率條件MIS.ELF503
 * </pre>
 * 
 * @since 2013/01/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/14,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Service
public class MisELF503ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF503Service {

	@Override
	public void insert(List<Object[]> DeleteList, List<Object[]> dataList) {

		this.getJdbc().batchUpdate("ELF503.delete",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.DATE },
				DeleteList);

		// J-108-0338_05097_B1001 Web e-Loan國內企金額度明細表新增第二組下限利率
		this.getJdbc().batchUpdate(
				"ELF503.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.DATE, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.DECIMAL, Types.DECIMAL,
						Types.DATE, Types.DATE, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.VARCHAR, Types.VARCHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.DATE,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.VARCHAR, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.DECIMAL, Types.DECIMAL,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.VARCHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.CHAR }, dataList);
	}

	/**
	 * 簽約未動用報送，註銷額度時，一併刪除ELF503資料 J-109-0202_05097_B1001 Web
	 * e-Loan利費率資料提前至授信案件經核定後逕行寫入
	 * 
	 * @param DeleteList
	 */
	@Override
	public void deleteByDocumentNo(List<Object[]> DeleteList) {

		this.getJdbc().batchUpdate("ELF503.deleteByDocumentNo",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR },
				DeleteList);

	}

}
