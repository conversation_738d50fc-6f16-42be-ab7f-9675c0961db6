<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
 xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
 xmlns:context="http://www.springframework.org/schema/context"
 xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
 http://www.sprin gframework.org/schema/util http://www.sprin gframework.org/schema/util/sprin g-util-3.0.xsd
 http://www.sprin gframework.org/schema/aop http://www.sprin gframework.org/schema/aop/sprin g-aop-3.0.xsd
 http://www.sprin gframework.org/schema/tx http://www.sprin gframework.org/schema/tx/sprin g-tx-3.0.xsd
 http://www.sprin gframework.org/schema/context http://www.sprin gframework.org/schema/context/sprin g-context-3.0.xsd ">

<bean id="placeholderConfigurer"
 class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    <property name="ignoreUnresolvablePlaceholders" value="false"/>
    <property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE"/>
    <property name="ignoreResourceNotFound" value="false"/>
    <property name="locations">
        <list>
        	<!--************取得DB帳號密碼設定*********************-->
            <value>classpath:testdb/test-database.properties</value>
			<!--************設定Table schema log4j 相關設定*********************-->
            <value>classpath:testdb/test-jpa.properties</value>
        </list>
    </property>
</bean>
<bean id="log4jInitialization"
 class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
    <property name="targetClass" value="org.springframework.util.Log4jConfigurer"/>
    <property name="targetMethod" value="initLogging"/>
    <property name="arguments">
        <list>
        	<!--************log4j 相關設定*********************-->
            <value>classpath:testdb/test-log4j.properties</value>
        </list>
    </property>
</bean>
<!--************設定 DB連結資源*********************-->
<import resource="classpath:testdb/test-datasource.xml"/>
<!--************設定 JPA 連線管理*********************-->
<import resource="classpath:testdb/test-jpa.xml"/>

<bean id="codeTypeDao" class="com.mega.eloan.common.dao.impl.CodeTypeDaoImpl">
	<!--************設定 該程式內使用的變數名稱 並建立關聯*********************-->
    <property name="entityManager" ref="entityManager"/>
</bean>
<bean id="codeTypeService" class="com.mega.eloan.common.service.impl.CodeTypeServiceImpl">
 
</bean>
</beans>