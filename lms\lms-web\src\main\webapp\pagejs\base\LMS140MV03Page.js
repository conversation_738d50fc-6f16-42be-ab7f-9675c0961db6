$(document).ready(function(){
    // 判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(".select").hide()
        //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#queryDataTr" + DOMPurify.sanitize($(this).val())).show();
    });
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        }
        else {
            $("#managerNm").hide();
        }
    });
    //openFilterBox();
    var grid = $("#gridview").iGrid({
        handler: 'lms140mgridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL140mm1a",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
            colHeader: i18n.lms140mv01['L140M01M.custId'],//"借款人統編",
            name: 'custId',
            width: 80,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms140mv01['L140M01M.custName'],//"借款人",
            name: 'custName',
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms140mv01['L140M01M.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 100,
            sortable: true
        }, {
            colHeader: i18n.lms140mv01['L140M01M.creator'],//"分行經辦",
            name: 'creator',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms140mv01["L140M01M.createTime"], // 建立日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
                colHeader: i18n.lms140mv01["L140M01M.approveTime"], // 核准日期
                align: "left",
                width: 60, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'approveTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    // 篩選
    function openFilterBox(){
        $("#filterBox").thickbox({
			title: '',
            width: 400,
            height: 150,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
					
					if($.trim($("#cntrnoFiltered").val()) == "" && $.trim($("#custIdFiltered").val()) == ""){
						return API.showErrorMessage(i18n.lms140mv03['L140MM1A.msg.error.filter']);
					}

					$("#gridview").jqGrid("setGridParam", {
				        postData: $.extend($("#filterForm").serializeData(), {
				            handler: 'lms140mgridhandler',
				            formAction: 'filterL140mm1aByEditCompletedCase',
				            docStatus: viewstatus,
				            mainDocStatus: viewstatus,
							custId: $("#custIdFiltered").val(),
							cntrNo: $("#cntrnoFiltered").val(),
				            rowNum: 15
				        })
				    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
			
		});
    }
    
    // grid資料篩選
    function filterGrid(sendData){
        $("#gridview").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryL140mm1a3",
                docStatus: viewstatus,
                type: $("[name=queryData]:checked").val()
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    }
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',// '../lms/cls1021m01/01',
            data: {
                formAction: "queryL140mm1a",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "lms140mm01formhandler",
                    data: {
                        formAction: "deleteL140mm1a",
                        oids: data
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms140mm01["L140M01M.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
        var id = $("#gridview").getGridParam('selarrrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        
        openCntrCaseBox(result.oid);
        
    });
});
