/* 
 * LMS7820M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.model.L782M01A;

/**
 * <pre>
 * 特殊登錄案件紀錄表
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/8,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7820m01")
public class LMS7820M01Page extends AbstractEloanForm {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(LMS7820M01Page.class);
		new DocLogPanel("_docLog").processPanelData(model, params); // 頁面要作用地方的wicket ID

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L782M01A.class;

	}

}// ~
