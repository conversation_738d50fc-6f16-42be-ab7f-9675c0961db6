/* 
 * CLS1021ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.lms.cls.service.CLS1200Service;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.model.C120M01A;

/**
 * <pre>
 *  線上申貸原始資料
 * </pre>
 * 
 * @since 2015/04/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Service
public class CLS1200ServiceImpl extends AbstractCapService implements
		CLS1200Service {


	@Resource
	C120M01ADao c120m01aDao;
	
	
	@Override
	public Page<C120M01A> getC120V01(ISearch search) {
		return c120m01aDao.findPage(search);
	}


	@Override
	public void save(GenericBean... entity) {
		// TODO Auto-generated method stub
		
	}


	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
		
	}


	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		// TODO Auto-generated method stub
		return null;
	}


	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		// TODO Auto-generated method stub
		return null;
	}


	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	
}
