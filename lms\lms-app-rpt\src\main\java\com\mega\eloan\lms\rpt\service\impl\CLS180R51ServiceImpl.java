package com.mega.eloan.lms.rpt.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.mfaloan.bean.PTEAMAPP;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.rpt.service.CLS180R51Service;
import com.mega.sso.service.BranchService;

/**
 * <pre>
 * 整批房貸統計表
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Service
public class CLS180R51ServiceImpl extends AbstractCapService implements CLS180R51Service {

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	@Override
	public void setBodyContent(WritableSheet sheet, Map<String, Map<String, Object>> data, int colIndex, int rowIndex) throws RowsExceededException, WriteException{
		
		WritableFont font = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}
		
		Map<String, String> propertyMap = codeTypeService.findByCodeType("lms1405s02_proPerty");
		
		for(String grpCntrNo : data.keySet()){
			Map<String, Object> map = data.get(grpCntrNo);
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_AREA_CODE")), cellFormat));//分區代碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_AREA_NAME")), cellFormat));//分區名
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_NO")), cellFormat));//分行代碼
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BRANCH_NAME")), cellFormat));//分行名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("")), cellFormat));//建設公司名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CUSTID")), cellFormat));//統一編號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CNTRNO")), cellFormat));//額度序號
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("BUILDNAME")), cellFormat));//建案名稱
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(String.valueOf(map.get("LANDCITY_NAME")) + map.get("LANDAREA_NAME")), cellFormat));//土地座落
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("REALESTATEAREA")), cellFormat));//不動產分區
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("EXPECTEDFINISHDATE")), cellFormat));//預計完工日
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("TODOREASON_NAME")), cellFormat));//敘作理由
			
			String caseDate = CapDate.formatDateFromF1ToF2(CapString.trimNull(map.get("CASEDATE")), "yyyy-MM-dd", "YYY/MM/DD");
			sheet.addCell(new Label(colIndex++, rowIndex, caseDate.replaceAll("/", "-"), cellFormat));//簽案日期
			
			String approvedDate = CapDate.formatDateFromF1ToF2(CapString.trimNull(map.get("APPROVED_DATE")), "yyyy-MM-dd", "YYY/MM/DD");
			approvedDate = Util.isNotEmpty(approvedDate) ? approvedDate.replaceAll("/", "-") : approvedDate ;
			sheet.addCell(new Label(colIndex++, rowIndex, approvedDate, cellFormat));//核定日期
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "CURRENTAPPLYAMT")/100000000, cellFormat));//現請/核准額度(A)

			String[] arr = String.valueOf(map.get("PROPERTY")).split("\\|");
			String properyStr = "";
			for(String code : arr){
				properyStr += propertyMap.get(code) + "、";
			}
			sheet.addCell(new Label(colIndex++, rowIndex, properyStr.substring(0, properyStr.length()-1), cellFormat));//性質
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "THIS_MONTH_APPR_QUOTA")/100000000, cellFormat));//本月動撥金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "THIS_YEAR_APPR_QUOTA")/100000000, cellFormat));//今年累積動撥金額
			sheet.addCell(new jxl.write.Number(colIndex++, rowIndex, MapUtils.getDoubleValue(map, "ACC_APPR_QUOTA")/100000000, cellFormat));//總累積動撥金額(B)
			
			BigDecimal currentApplyAmt = LMSUtil.nullToZeroBigDecimal(map.get("CURRENTAPPLYAMT"));
			BigDecimal accApprQuota = LMSUtil.nullToZeroBigDecimal(map.get("ACC_APPR_QUOTA"));
			String utilizationRate = currentApplyAmt.compareTo(BigDecimal.ZERO) == 0 
									? "0"
									: accApprQuota.divide(currentApplyAmt, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2).toPlainString();
			
			sheet.addCell(new Label(colIndex++, rowIndex, utilizationRate + " %", cellFormat));//動用率(%) (B/A)
			sheet.addCell(new Label(colIndex++, rowIndex, CapString.trimNull(map.get("CLOSE_CASE")), cellFormat));//結案
			rowIndex++;
			colIndex = 0;
		}
	}

	public Map<String, Integer> getTitleMap(){
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R51.batchMortgageStatisticsReport", 20);//整批房貸統計表
		return titleMap;
	}
	
	@Override
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 20);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String title : titleMap.keySet()){
			this.mergeFieldWithAutoSize(sheet, fromColIndex, toColIndex, fromRowIndex, toRowIndex, prop.getProperty(title), cellFormat);
		}
	}
	
	@Override
	public Map<String, Integer> getHeaderMap(){
		
		Map<String, Integer> titleMap = new LinkedHashMap<String, Integer>();
		titleMap.put("CLS180R51.areaCode", 7);//分區代碼
		titleMap.put("CLS180R51.areaName", 10);//分區名
		titleMap.put("CLS180R51.branchNo", 7);//分行代碼
		titleMap.put("CLS180R51.branchName", 10);//分行名稱
		titleMap.put("CLS180R51.constructionCompanyName", 20);//建設公司名稱
		titleMap.put("CLS180R51.customerId", 15);//統一編號
		titleMap.put("CLS180R51.contractNo", 15);//額度序號
		titleMap.put("CLS180R51.buildCaseName", 15);//建案名稱
		titleMap.put("CLS180R51.landLocated", 15);//土地座落
		titleMap.put("CLS180R51.realEstateGrade", 16);//不動產分區
		titleMap.put("CLS180R51.expectedFinishDate", 16);//預計完工日
		titleMap.put("CLS180R51.toDoReason", 15);//敘作理由
		titleMap.put("CLS180R51.signingDate", 13);//簽案日期
		titleMap.put("CLS180R51.approvedDate", 13);//核定日期
		titleMap.put("CLS180R51.currentApplicationOrApprovedQuota", 15);//現請/核准額度
		titleMap.put("CLS180R51.property", 10);//性質
		titleMap.put("CLS180R51.amountSentThisMonth", 15);//本月動撥金額
		titleMap.put("CLS180R51.cumulativeAmountSentThisYear", 15);//今年累積動撥金額
		titleMap.put("CLS180R51.totalAmountSent", 15);//總累積動撥金額(B)
		titleMap.put("CLS180R51.utilizationRate", 15);//動用率(%) (B/A)
		titleMap.put("CLS180R51.caseClosed", 7);//結案
		return titleMap;
	}
	
	@Override
	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap, Properties prop, int colIndex, int rowIndex) throws WriteException{
		
		WritableFont font_Header = new WritableFont(WritableFont.createFont("標楷體"), 12);
		WritableCellFormat cellFormat = new WritableCellFormat(font_Header);
		{
			cellFormat.setWrap(true);
			cellFormat.setAlignment(Alignment.CENTRE);
			cellFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
			cellFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
		}

		for(String header : headerMap.keySet()){
			this.mergeFieldAndSetWidth(sheet, colIndex, colIndex++, rowIndex, rowIndex+1, prop.getProperty(header), cellFormat, headerMap.get(header));
		}
	}
	
//	private void setCellsFormat(WritableSheet sheet, int width, String content, int colIndex, int rowIndex, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
//		sheet.setColumnView(colIndex, width);//設定欄寬
//		sheet.addCell(new Label(colIndex, rowIndex, content, cellFormat));
//	}
	
	private void mergeFieldAndSetWidth(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat, int width) throws RowsExceededException, WriteException{
		sheet.setColumnView(fromColIndex, width);
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	private void mergeFieldWithAutoSize(WritableSheet sheet, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, 
										String content, WritableCellFormat cellFormat) throws RowsExceededException, WriteException{
		sheet.mergeCells(fromColIndex, fromRowIndex, toColIndex, toRowIndex);
		sheet.addCell(new Label(fromColIndex, fromRowIndex, content, cellFormat));
	}
	
	@Override
	public void processIsCloseCase(Map<String, Map<String, Object>> masterData, String endDate){
		
		Date tmpEndDate = CapDate.getDate(endDate, "yyyy-MM-dd");
		
		HashSet<String> cntrNoSet = new HashSet<String>();
		for(String cntrno : masterData.keySet()){
			cntrNoSet.add(cntrno);
		}							
		
		List<PTEAMAPP> list = this.misPTEAMAPPService.getDataByGrpCntrNo(cntrNoSet);
		for(PTEAMAPP entity : list){
			
			String grpCntrNo = entity.getGrpcntrno();
			
			String closeCaseFlag = UtilConstants.Cntrdoc.Property.取消.equals(masterData.get(grpCntrNo).get("PROPERTY")) 
									|| entity.getEffend().compareTo(tmpEndDate) < 0 
									? "V" : "";
			
			masterData.get(grpCntrNo).put("CLOSE_CASE", closeCaseFlag);
		}
	}

}
