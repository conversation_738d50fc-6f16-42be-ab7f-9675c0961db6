/*
 * CapPluginManager.java 2009/9/9 上午 04:34:00
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System, Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.plugin;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.PlugInNotFoundException;

/**
 * <pre>
 * class CapPluginManager. 
 * 插件管理
 * </pre>
 *
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/16,iristu,modify
 *          </ul>
 */
@Service
public class CapPluginManager implements InitializingBean {

    private final static Logger logger = LoggerFactory.getLogger(CapPluginManager.class);

    @Autowired
    private ApplicationContext context;

    /**
     * User Spring Bean Name to find Bean Class
     * 
     * @param <T>
     *            T
     * @param plugInBeanName
     *            plugInBeanName
     * @param version
     *            version
     * @return T
     * @throws PlugInNotFoundException
     */
    @SuppressWarnings("unchecked")
    public <T> T getPlugin(String plugInBeanName, String version) throws PlugInNotFoundException {
        T plugin = null;

        long start = System.currentTimeMillis();
        plugin = (T) context.getBean(plugInBeanName, ICapPlugin.class);
        if (logger.isTraceEnabled()) {
            logger.trace("Spring Find " + plugInBeanName + " executed time: " + (System.currentTimeMillis() - start));
        }
        if (plugin != null) {
            return plugin;
        } else {
            throw new PlugInNotFoundException(plugInBeanName, plugInBeanName, version);
        }
    }

    /**
     * 取得預設錯誤處理的Bean
     * 
     * @param <R>
     * @return {@code (R) context.getBean("defaultErrorResult")}
     */
    @SuppressWarnings("unchecked")
    public <R> R getDefaultErrorResult() {
        return (R) context.getBean("defaultErrorResult");
    }

    /*
     * 完成屬性設置後, 調用正確的Bean Factory
     * 
     * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet()
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        String[] names = getAllPluginName();

        for (String name : names) {
            if (logger.isInfoEnabled()) {
                logger.info("Found Plugin Name: " + name);
            }
        }
    }

    /**
     * Gets the plugin name.
     * 
     * @return the plugin map
     */
    public String[] getAllPluginName() {
        return context.getBeanNamesForType(ICapPlugin.class);
    }

}
