/* 
 * C101S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.ImportCheck;
import com.mega.eloan.lms.validation.group.SaveCheck;

/** 個金基本資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 出生日期 **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Temporal(TemporalType.DATE)
	@Column(name = "BIRTHDAY", columnDefinition = "DATE")
	private Date birthday;

	/**
	 * 學歷
	 * <p/>
	 * 國中以下 | 01<br/>
	 * 國中 | 02<br/>
	 * 高中職 | 03<br/>
	 * 專科 | 04<br/>
	 * 大學 | 05<br/>
	 * 碩士 | 06<br/>
	 * 博士 | 07
	 */
//	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
//			Check2.class })
//	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
//			Check2.class })
	@Size(max = 2)
	@Column(name = "EDU", length = 2, columnDefinition = "CHAR(2)")
	private String edu;

	/**
	 * 性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 */
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "SEX", length = 1, columnDefinition = "CHAR(1)")
	private String sex;

	/**
	 * 婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 */
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Size(max = 1)
	@Column(name = "MARRY", length = 1, columnDefinition = "CHAR(1)")
	private String marry;

	/** 子女數 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Digits(integer = 2, fraction = 0, groups = SaveCheck.class)
	@Column(name = "CHILD", columnDefinition = "DECIMAL(2,0)")
	private Integer child;

	/** 國別 **/
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 2)
	@Column(name = "NTCODE", length = 2, columnDefinition = "CHAR(2)")
	private String ntCode;

	/**
	 * 外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Size(max = 1)
	@Column(name = "FOREGINAL", length = 1, columnDefinition = "CHAR(1)")
	private String foreginal;

	/** 通訊電話 **/
	@Size(max = 150)
	@Column(name = "COTEL", length = 150, columnDefinition = "VARCHAR(150)")
	private String coTel;

	/** 戶籍電話 **/
	@Size(max = 150)
	@Column(name = "FTEL", length = 150, columnDefinition = "VARCHAR(150)")
	private String fTel;

	/** 行動電話 **/
	@Size(max = 150)
	@Column(name = "MTEL", length = 150, columnDefinition = "VARCHAR(150)")
	private String mTel;

	/** email **/
	@Size(max = 120)
	@Column(name = "EMAIL", length = 120, columnDefinition = "VARCHAR(120)")
	private String email;

	/**
	 * 戶籍地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	@Size(max = 10)
	@Column(name = "FELAND", length = 10, columnDefinition = "VARCHAR(10)")
	private String fEland;

	/** 戶籍地址(縣市) **/
	@Size(max = 2)
	@Column(name = "FCITY", length = 2, columnDefinition = "VARCHAR(2)")
	private String fCity;

	/**
	 * 戶籍地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	@Size(max = 5)
	@Column(name = "FZIP", length = 5, columnDefinition = "VARCHAR(5)")
	private String fZip;

	/**
	 * 戶籍地址
	 * <p/>
	 * 64個全型字
	 */
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 192)
	@Column(name = "FADDR", length = 192, columnDefinition = "VARCHAR(192)")
	private String fAddr;

	/** 戶籍地址(標的) **/
	@Size(max = 300)
	@Column(name = "FTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String fTarget;

	/**
	 * 通訊地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	@Size(max = 10)
	@Column(name = "COELAND", length = 10, columnDefinition = "VARCHAR(10)")
	private String coEland;

	/** 通訊地址(縣市) **/
	@Size(max = 2)
	@Column(name = "COCITY", length = 2, columnDefinition = "VARCHAR(2)")
	private String coCity;

	/**
	 * 通訊地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	@Size(max = 5)
	@Column(name = "COZIP", length = 5, columnDefinition = "VARCHAR(5)")
	private String coZip;

	/**
	 * 通訊地址
	 * <p/>
	 * 300個全型字
	 */
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 900)
	@Column(name = "COADDR", length = 900, columnDefinition = "VARCHAR(900)")
	private String coAddr;

	/** 通訊地址(標的) **/
	@Size(max = 300)
	@Column(name = "COTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String coTarget;

	/**
	 * 現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
     * 
	 * p.s
	 * 於J-102-0196非房貸業務申請評分卡 上線之前：在「放款信用評分表」有使用到
	 */
	@NotEmpty(message = "{required.message}", groups = { ImportCheck.class,
			SaveCheck.class })
	@NotNull(message = "{required.message}", groups = { ImportCheck.class,
			SaveCheck.class })
	@Size(max = 1)
	@Column(name = "HOUSESTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String houseStatus;

	/**
	 * 每月貸款
	 * <p/>
	 * houseStatus=2
	 */
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "HOUSELEND", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal houseLend;

	/**
	 * 每月租金
	 * <p/>
	 * houseStatus=6
	 */
	@Digits(integer = 13, fraction = 0, groups = SaveCheck.class)
	@Column(name = "HOUSERENT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal houseRent;

	/**
	 * 不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 * 
	 * p.s
	 * 於J-102-0196非房貸業務申請評分卡 上線之前：在「放款信用評分表」有使用到
	 * 並上傳至  DW_RKAPPLICANT.CMSSTATUS
	 * 
	 */
//	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
//	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
	@Size(max = 1)
	@Column(name = "CMSSTATUS", length = 1, columnDefinition = "CHAR(1)")
	private String cmsStatus;

	/** 存款往來銀行 **/
	@Size(max = 3)
	@Column(name = "DPBANK", length = 3, columnDefinition = "VARCHAR(3)")
	private String dpBank;

	/** 存款往來分行代碼 **/
	@Size(max = 7)
	@Column(name = "DPBRNO", length = 7, columnDefinition = "VARCHAR(7)")
	private String dpBrno;

	/**
	 * 存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 */
	@Size(max = 60)
	@Column(name = "DPBANKNAME", length = 60, columnDefinition = "VARCHAR(60)")
	private String dpBankName;

	/** 存款帳戶 **/
	// @Pattern(regexp = "^[A-Za-z0-9\\s]+$", message = "只限中英數字", groups =
	// SaveCheck.class)
	// @Pattern(regexp = "^[\u0000-\u007F]+$", message = "只限中英數字", groups =
	// SaveCheck.class)
	@Size(max = 16)
	@Column(name = "DPACCT", length = 16, columnDefinition = "VARCHAR(16)")
	private String dpAcct;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 身分證發證日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="IDCARDISSUEDATE", columnDefinition="DATE")
	private Date idCardIssueDate;
	
	/** 身分證發證地 select * from com.bcodetype where codetype='C101S01A_idCard_siteId'  **/
	@Column(name = "IDCARD_SITEID", length = 8, columnDefinition = "VARCHAR(8)")
	private String idCard_siteId;
	
	/** 身分證換補資料 {0初發 1補發 2換發} select * from com.bcodetype where codetype='C101S01A_idCardChgFlag'  **/
	@Column(name="IDCARDCHGFLAG", length=1, columnDefinition="CHAR(1)")
	private String idCardChgFlag;
	
	/** 身分證有無照片(Y/N) select * from com.bcodetype where codetype='C101S01A_idCardPhoto'  **/
	@Column(name="IDCARDPHOTO", length=1, columnDefinition="CHAR(1)")
	private String idCardPhoto;
	
	/** 現住地址(縣市) **/
	@Size(max=2)
	@Column(name="RESIDENCECITY", length=2, columnDefinition="VARCHAR(2)")
	private String residenceCity;

	/** 現住地址(鄉鎮市區)郵遞區號  **/
	@Size(max=5)
	@Column(name="RESIDENCEZIP", length=5, columnDefinition="VARCHAR(5)")
	private String residenceZip;

	/** 現住地址(64個全型字) **/
	@Size(max=192)
	@Column(name="RESIDENCEADDR", length=192, columnDefinition="VARCHAR(192)")
	private String residenceAddr;

	/** 現住地址(標的) **/
	@Size(max=300)
	@Column(name="RESIDENCETARGET", length=300, columnDefinition="VARCHAR(300)")
	private String residenceTarget;
	
	/**
	 * 申請資料核對表註記
	 * <p/>
	 */
	@Column(name = "CHECKLISTFLAG", length = 3, columnDefinition = "VARCHAR(1)")
	private String checkListFlag;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得出生日期 **/
	public Date getBirthday() {
		return this.birthday;
	}

	/** 設定出生日期 **/
	public void setBirthday(Date value) {
		this.birthday = value;
	}

	/**
	 * 取得學歷
	 * <p/>
	 * 國中以下 | 01<br/>
	 * 國中 | 02<br/>
	 * 高中職 | 03<br/>
	 * 專科 | 04<br/>
	 * 大學 | 05<br/>
	 * 碩士 | 06<br/>
	 * 博士 | 07
	 */
	public String getEdu() {
		return this.edu;
	}

	/**
	 * 設定學歷
	 * <p/>
	 * 國中以下 | 01<br/>
	 * 國中 | 02<br/>
	 * 高中職 | 03<br/>
	 * 專科 | 04<br/>
	 * 大學 | 05<br/>
	 * 碩士 | 06<br/>
	 * 博士 | 07
	 **/
	public void setEdu(String value) {
		this.edu = value;
	}

	/**
	 * 取得性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 */
	public String getSex() {
		return this.sex;
	}

	/**
	 * 設定性別
	 * <p/>
	 * 100/12/06調整<br/>
	 * M男、F女
	 **/
	public void setSex(String value) {
		this.sex = value;
	}

	/**
	 * 取得婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 */
	public String getMarry() {
		return this.marry;
	}

	/**
	 * 設定婚姻狀況
	 * <p/>
	 * 1未婚、2已婚、4離婚、5歿
	 **/
	public void setMarry(String value) {
		this.marry = value;
	}

	/** 取得子女數 **/
	public Integer getChild() {
		return this.child;
	}

	/** 設定子女數 **/
	public void setChild(Integer value) {
		this.child = value;
	}

	/** 取得國別 **/
	public String getNtCode() {
		return this.ntCode;
	}

	/** 設定國別 **/
	public void setNtCode(String value) {
		this.ntCode = value;
	}

	/**
	 * 取得外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getForeginal() {
		return this.foreginal;
	}

	/**
	 * 設定外國人無特定住所
	 * <p/>
	 * 100/12/08新增<br/>
	 * Y/N<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setForeginal(String value) {
		this.foreginal = value;
	}

	/** 取得通訊電話 **/
	public String getCoTel() {
		return this.coTel;
	}

	/** 設定通訊電話 **/
	public void setCoTel(String value) {
		this.coTel = value;
	}

	/** 取得戶籍電話 **/
	public String getFTel() {
		return this.fTel;
	}

	/** 設定戶籍電話 **/
	public void setFTel(String value) {
		this.fTel = value;
	}

	/** 取得行動電話 **/
	public String getMTel() {
		return this.mTel;
	}

	/** 設定行動電話 **/
	public void setMTel(String value) {
		this.mTel = value;
	}

	/** 取得email **/
	public String getEmail() {
		return this.email;
	}

	/** 設定email **/
	public void setEmail(String value) {
		this.email = value;
	}

	/**
	 * 取得戶籍地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	public String getFEland() {
		return this.fEland;
	}

	/**
	 * 設定戶籍地址(eland)
	 * <p/>
	 * Eland代碼
	 **/
	public void setFEland(String value) {
		this.fEland = value;
	}

	/** 取得戶籍地址(縣市) **/
	public String getFCity() {
		return this.fCity;
	}

	/** 設定戶籍地址(縣市) **/
	public void setFCity(String value) {
		this.fCity = value;
	}

	/**
	 * 取得戶籍地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	public String getFZip() {
		return this.fZip;
	}

	/**
	 * 設定戶籍地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 **/
	public void setFZip(String value) {
		this.fZip = value;
	}

	/**
	 * 取得戶籍地址
	 * <p/>
	 * 64個全型字
	 */
	public String getFAddr() {
		return this.fAddr;
	}

	/**
	 * 設定戶籍地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setFAddr(String value) {
		this.fAddr = value;
	}

	/** 取得戶籍地址(標的) **/
	public String getFTarget() {
		return this.fTarget;
	}

	/** 設定戶籍地址(標的) **/
	public void setFTarget(String value) {
		this.fTarget = value;
	}

	/**
	 * 取得通訊地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	public String getCoEland() {
		return this.coEland;
	}

	/**
	 * 設定通訊地址(eland)
	 * <p/>
	 * Eland代碼
	 **/
	public void setCoEland(String value) {
		this.coEland = value;
	}

	/** 取得通訊地址(縣市) **/
	public String getCoCity() {
		return this.coCity;
	}

	/** 設定通訊地址(縣市) **/
	public void setCoCity(String value) {
		this.coCity = value;
	}

	/**
	 * 取得通訊地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	public String getCoZip() {
		return this.coZip;
	}

	/**
	 * 設定通訊地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 **/
	public void setCoZip(String value) {
		this.coZip = value;
	}

	/**
	 * 取得通訊地址
	 * <p/>
	 * 64個全型字
	 */
	public String getCoAddr() {
		return this.coAddr;
	}

	/**
	 * 設定通訊地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setCoAddr(String value) {
		this.coAddr = value;
	}

	/** 取得通訊地址(標的) **/
	public String getCoTarget() {
		return this.coTarget;
	}

	/** 設定通訊地址(標的) **/
	public void setCoTarget(String value) {
		this.coTarget = value;
	}

	/**
	 * 取得現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
	 */
	public String getHouseStatus() {
		return this.houseStatus;
	}

	/**
	 * 設定現住房屋
	 * <p/>
	 * 1自有，無貸款<br/>
	 * 2自有，有貸款<br/>
	 * 3配偶所有<br/>
	 * 4父母親所有<br/>
	 * 5宿舍<br/>
	 * 6租賃
	 **/
	public void setHouseStatus(String value) {
		this.houseStatus = value;
	}

	/**
	 * 取得每月貸款
	 * <p/>
	 * houseStatus=2
	 */
	public BigDecimal getHouseLend() {
		return this.houseLend;
	}

	/**
	 * 設定每月貸款
	 * <p/>
	 * houseStatus=2
	 **/
	public void setHouseLend(BigDecimal value) {
		this.houseLend = value;
	}

	/**
	 * 取得每月租金
	 * <p/>
	 * houseStatus=6
	 */
	public BigDecimal getHouseRent() {
		return this.houseRent;
	}

	/**
	 * 設定每月租金
	 * <p/>
	 * houseStatus=6
	 **/
	public void setHouseRent(BigDecimal value) {
		this.houseRent = value;
	}

	/**
	 * 取得不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 */
	public String getCmsStatus() {
		return this.cmsStatus;
	}

	/**
	 * 設定不動產狀況
	 * <p/>
	 * 1本人或配偶不動產，未設定抵押權<br/>
	 * 2本人或配偶不動產，已設定抵押權<br/>
	 * 3本人或配偶無不動產
	 **/
	public void setCmsStatus(String value) {
		this.cmsStatus = value;
	}

	/** 取得存款往來銀行 **/
	public String getDpBank() {
		return this.dpBank;
	}

	/** 設定存款往來銀行 **/
	public void setDpBank(String value) {
		this.dpBank = value;
	}

	/** 取得存款往來分行代碼 **/
	public String getDpBrno() {
		return this.dpBrno;
	}

	/** 設定存款往來分行代碼 **/
	public void setDpBrno(String value) {
		this.dpBrno = value;
	}

	/**
	 * 取得存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 */
	public String getDpBankName() {
		return this.dpBankName;
	}

	/**
	 * 設定存款往來銀行(海外用)
	 * <p/>
	 * 101/01/18新增
	 **/
	public void setDpBankName(String value) {
		this.dpBankName = value;
	}

	/** 取得存款帳戶 **/
	public String getDpAcct() {
		return this.dpAcct;
	}

	/** 設定存款帳戶 **/
	public void setDpAcct(String value) {
		this.dpAcct = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得身分證發證日期 **/
	public Date getIdCardIssueDate() {
		return idCardIssueDate;
	}
	/** 設定身分證發證日期 **/
	public void setIdCardIssueDate(Date idCardIssueDate) {
		this.idCardIssueDate = idCardIssueDate;
	}
	/** 取得身分證發證地 **/
	public String getIdCard_siteId() {
		return idCard_siteId;
	}
	/** 設定身分證發證地 **/
	public void setIdCard_siteId(String idCard_siteId) {
		this.idCard_siteId = idCard_siteId;
	}
	/** 取得身分證換補資料 {0初發 1補發 2換發} **/
	public String getIdCardChgFlag() {
		return idCardChgFlag;
	}
	/** 設定身分證換補資料 {0初發 1補發 2換發} **/
	public void setIdCardChgFlag(String idCardChgFlag) {
		this.idCardChgFlag = idCardChgFlag;
	}
	/** 取得身分證有無照片 **/
	public String getIdCardPhoto() {
		return idCardPhoto;
	}
	/** 設定身分證有無照片 **/
	public void setIdCardPhoto(String idCardPhoto) {
		this.idCardPhoto = idCardPhoto;
	}
	/** 取得現住地址(縣市) **/
	public String getResidenceCity() {
		return residenceCity;
	}
	/** 設定現住地址(縣市) **/
	public void setResidenceCity(String residenceCity) {
		this.residenceCity = residenceCity;
	}
	/** 取得現住地址(鄉鎮市區)郵遞區號  **/
	public String getResidenceZip() {
		return residenceZip;
	}
	/** 設定現住地址(鄉鎮市區)郵遞區號  **/
	public void setResidenceZip(String residenceZip) {
		this.residenceZip = residenceZip;
	}
	/** 取得現住地址(64個全型字) **/
	public String getResidenceAddr() {
		return residenceAddr;
	}
	/** 設定現住地址(64個全型字) **/
	public void setResidenceAddr(String residenceAddr) {
		this.residenceAddr = residenceAddr;
	}
	/** 取得現住地址(標的) **/
	public String getResidenceTarget() {
		return residenceTarget;
	}
	/** 設定現住地址(標的) **/
	public void setResidenceTarget(String residenceTarget) {
		this.residenceTarget = residenceTarget;
	}

	public void setCheckListFlag(String checkListFlag) {
		this.checkListFlag = checkListFlag;
	}

	public String getCheckListFlag() {
		return checkListFlag;
	}
}
