package com.mega.eloan.lms.base.service.impl;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.context.CapParameter;
import tw.com.iisi.cap.service.ICapService;
import tw.com.jcs.common.PropUtil;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;
import com.mega.eloan.lms.base.service.LMSDataConvertService;
import com.mega.eloan.lms.dc.action.RowData;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.main.OnlineDCHandler;

/**
 * <pre>
 * 單筆資料轉換
 * </pre>
 * 
 * @since 2013/5/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/5/11, 007625 ,new
 *          </ul>
 */

@Service("lmsdataconvertservice")
public class LMSDataConvertServiceImpl implements LMSDataConvertService,
		ICapService {

	private EloanJdbcTemplate jdbc;

	@Resource(name = "eLoanSqlStatement")
	CapParameter lmsSQL;

	@Resource(name = "lms-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_ELDB);
		jdbc.setSqlProvider(lmsSQL);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * get the the jdbc
	 * 
	 * @return the jdbc
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	private static final String PROCESS_NAME = "LMSDataConvertService";

	private static Logger logger = LoggerFactory
			.getLogger(LMSDataConvertServiceImpl.class);

	@Override
	public synchronized String doDataConvert(String nsfName,
			List<String> viewList, String mainId, String ip, String schema,
			String dbType, String brNo, String... l120m01aMainIds) {

		long startTime = System.currentTimeMillis();

		String result = "";

		ConfigData config = null;
		File stateFile = new File("/elnfs/LMS/lmsdc/" + mainId + ".state");

		try {
			if (doLockFile()) {

				FileUtils.writeStringToFile(stateFile, "R");

				OnlineDCHandler onlineDCHandler = new OnlineDCHandler();
				List<RowData> dcSqls = onlineDCHandler.runAll(nsfName,
						viewList, mainId, ip, schema, dbType, brNo);

				config = MainConfig.getInstance().getConfig();

				for (String l120m01aMainId : l120m01aMainIds) {
					this.clearData(l120m01aMainId);
				}

				FileUtils.writeLines(new File(
						"/elnfs/LMS/lmsdc/dataConvertest_" + mainId + ".sql"),
						"BIG5", dcSqls);

				List<String> delSqls = new ArrayList<String>();

				int allSqlCount = dcSqls != null ? dcSqls.size() : 0;
				int count = 0;
				for (RowData rowData : dcSqls) {
					count++;

					String sql = rowData.getSql();

					String deleteSql = this.genDeleteSql(sql);
					delSqls.add(deleteSql);
					try {

						logger.info("SQL筆數處理進度：" + count + "/" + allSqlCount);
						if (rowData.isClobTb()) {
							String pSql = rowData.getSql();
							this.update(pSql, rowData.getClobString());
						} else {
							this.update(sql);
						}
					} catch (Exception e) {
						String exception = e.toString().toLowerCase()
								.replaceAll("\r", "").replaceAll("\n", "");

						if (exception.indexOf("state=23505") >= 0) {
							// String key = "LMS.";
							// String key2 = " VALUES(";
							//
							// String tableName = sql.substring(
							// sql.indexOf(key), sql.indexOf(key2));
							// if
							// (!"LMS.L120M01F".equalsIgnoreCase(tableName)
							// &&
							// !"LMS.L140M01C".equalsIgnoreCase(tableName))
							// {
							// throw e;
							// }
						} else {
							throw e;
						}
					}
				}

				FileUtils.writeLines(new File(
						"/elnfs/LMS/lmsdc/dataConvertestDelete_" + mainId
								+ ".sql"), "BIG5", delSqls);

				result = "Y";
			} else {
				result = "N";
			}

			File sourceDir = new File(config.getLmsloadDB2DirPath(),
					config.getFilesPath());
			// String sourceDir = "/elnfs/LMS/lmsdc/load_db2/" + mainId
			// + "/LMS/FILES/LMS" ;
			File destDir = new File(PropUtil.getProperty("docFile.dir"));
			// String destDir = "/elnfs/LMS";

			// 附件檔案複製
			if (sourceDir.exists()) {
				FileUtils.copyDirectory(sourceDir, destDir);
			}

			// FileUtils.deleteDirectory(sourceDir);

		} catch (DCException e) {
			logger.error("LMSDataConvertServiceImpl Exception : ", e);
			result = e.toString();
		} catch (Exception ioe) {
			logger.error("LMSDataConvertServiceImpl IO Exception : ", ioe);
			result = ioe.toString();
		} finally {
			try {
				if (stateFile.exists()) {
					FileUtils.writeStringToFile(stateFile, result);
				}
			} catch (IOException e) {
				logger.debug("Exception", e);
			}
			doUnLockFile();

		}

		long endTime = System.currentTimeMillis();

		logger.info("---------------------------轉檔作業執行結束，花費時間為："
				+ (endTime - startTime) / 1000 + "秒---------------------------");
		return result;
	}

	private String genDeleteSql(String sql) {

		String text = sql;
		int startPosition = text.indexOf("VALUES('") + "VALUES('".length();

		// System.out.println(text.indexOf("VALUES('")+ "VALUES('".length());

		String oid = text.substring(startPosition, startPosition + 32);
		// System.out.println(text.substring(startPosition , startPosition +
		// 32));

		String key = "LMS.";
		String key2 = " VALUES(";

		String tableName = text
				.substring(text.indexOf(key), text.indexOf(key2));
		// System.out.println(text.substring(text.indexOf(key),
		// text.indexOf(key2)));

		StringBuffer deleteSql = new StringBuffer();

		deleteSql.append("DELETE FROM " + tableName + " WHERE OID = '" + oid
				+ "';");
		return deleteSql.toString();

	}

	@Override
	public boolean checkIsOK() {
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ PROCESS_NAME + ".lock";
		File file = new File(lockFile);
		return !file.exists();
	}

	@Override
	public synchronized boolean doLockFile() {
		return lockSingletonProgramFile(PROCESS_NAME);
	}

	@Override
	public synchronized boolean doUnLockFile() {
		return unLockSingletonProgramFile(PROCESS_NAME);
	}

	private boolean lockSingletonProgramFile(String programName) {

		final String startFailureMessage = "Error:start " + programName
				+ " application";
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ programName + ".lock";

		logger.info("start " + programName + " application with [lockFile] : "
				+ lockFile);

		FileWriter writer = null;
		try {
			File file = new File(lockFile);
			if (!file.exists()) {
				String parent = file.getParent();
				File folder = new File(parent);
				if (!folder.exists() || !folder.isDirectory()) {
					if (!folder.mkdirs()) {
						logger.error(startFailureMessage
								+ " failure: create lock file folder failure:"
								+ parent);
					}
				}
				if (!file.createNewFile()) {
					logger.error(startFailureMessage
							+ " failure: create lock file failure:" + lockFile);
				}

				writer = new FileWriter(file);
				writer.write(programName);
				writer.flush();
				writer.close();
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			logger.error(startFailureMessage + " failure: lock file is ["
					+ lockFile + "]:" + e.getMessage(), e);
			try {

				if (null != writer) {
					writer.close();
				}
			} catch (Exception ex) {
				logger.error(
						"Error: close resource failure:" + ex.getMessage(), ex);
			}
			logger.debug("There is a "
					+ programName
					+ " application process in system processes. Now exit starting!");

			return false;

		}
	}

	private boolean unLockSingletonProgramFile(String programName) {

		final String startFailureMessage = "Error:start " + programName
				+ " application";
		String lockFile = PropUtil.getProperty("docFile.dir") + "/"
				+ programName + ".lock";
		logger.debug("start " + programName
				+ " application with [delete lockFile] : " + lockFile);

		try {
			File file = new File(lockFile);

			if (file.exists()) {
				logger.debug(file.toString() + " delete : " + file.delete()
						+ "");
			}

			return true;

		} catch (Exception e) {
			logger.debug(startFailureMessage + " failure: lock file is ["
					+ lockFile + "]:" + e.getMessage(), e);
			try {

				return false;

			} catch (Exception ex) {
				logger.error(
						"Error: close resource failure:" + ex.getMessage(), ex);
			}
			logger.debug("There is a "
					+ programName
					+ " application process in system processes. Now exit starting!");

			return false;

		}
	}

	private void clearData(String mainId) {
		// delete lms.l140m01a

		// DELETE FROM {0} WHERE MAINID IN (SELECT REFMAINID FROM LMS.L120M01C
		// WHERE MAINID IN (?))
		String sql1 = lmsSQL.getValue("dcSql1");
		// DELETE FROM {0} WHERE SRCMAINID IN (SELECT REFMAINID FROM
		// LMS.L120M01C WHERE MAINID IN (?))
		String sql2 = lmsSQL.getValue("dcSql2");
		// DELETE FROM {0} WHERE MAINID IN (?)
		String sql3 = lmsSQL.getValue("dcSql3");

		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01A" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01A_BF" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01B" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01C" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01C_BF" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01D" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01D_BF" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01E" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01F" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01H" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01I" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01K" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01M" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M01N" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.L140M02A" }),
				mainId);
		this.update(
				MessageFormat.format(sql2, new Object[] { "LMS.L230S01A" }),
				mainId);
		this.update(
				MessageFormat.format(sql1, new Object[] { "LMS.BDOCFILE" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.BDOCFILE" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120M01A" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120M01B" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120M01F" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120M01T" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120S01A" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120S01B" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120S01C" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120S01D" }),
				mainId);
		this.update(
				MessageFormat.format(sql3, new Object[] { "LMS.L120M01C" }),
				mainId);
		//
		// this.update(
		// "DELETE FROM LMS.L140M01A WHERE  MAINID  IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01A_BF WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01B    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01C    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01C_BF WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01D    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01D_BF WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01E    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01F    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01H    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01I    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01K    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01M    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M01N    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L140M02A    WHERE   MAINID IN (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.L230S01A    WHERE SRCMAINID IN (SELECT REFMAINID FROM LMS.L120M01C   WHERE MAINID  IN (?))",
		// mainId);
		//
		// this.update("DELETE FROM LMS.BDOCFILE    WHERE MAINID  IN (?)",
		// mainId);
		// this.update(
		// "DELETE FROM LMS.BDOCFILE    WHERE MAINID IN  (SELECT REFMAINID FROM LMS.L120M01C    WHERE MAINID  IN (?))",
		// mainId);
		// this.update("DELETE FROM LMS.L120M01A    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120M01B    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120M01F    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120M01T    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120S01A    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120S01B    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120S01C    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120S01D    WHERE MAINID  IN (?)",
		// mainId);
		// this.update("DELETE FROM LMS.L120M01C    WHERE MAINID  IN (?)",
		// mainId);

	}

	private int update(String sql, Object... objects) {
		return this.getJdbc().update(sql, objects);
	}

}
