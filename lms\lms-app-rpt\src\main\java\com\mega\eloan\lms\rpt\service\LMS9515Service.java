/* 
 * LMS9515Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L784M01A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.sso.model.IBranch;

public interface LMS9515Service extends AbstractService {
	/**
	 * 報表存實際路徑
	 * 
	 * @param fileName
	 * @param ctype
	 * @param caseDept
	 * @param dataDate
	 * @param i
	 * @param ctype2
	 */
	void saveFlieName(List<?> list, String fileName, int action, String brNo,
			Date dataStartDate, Date dataEndDate, String mainId,
			String caseDept, String randomCode);

	/**
	 * J-108-0192_05097_B1001 Web e-Loan企金授信新增每季海外營業單位授信報案考核彙總表
	 * 
	 * type9-營業單位授信報案考核彙總表 抓全年跟抓指定月份
	 * 
	 * @param TWYM_START
	 *            本月/本季起始年月
	 * @param TWYM_END
	 *            本月/本季結束年月
	 * @param startTWYM
	 *            開始年月
	 * @param endTWYM
	 *            結束年月
	 * @param type
	 *            排序方式不同
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType9BystartYMendYM(String TWYM_START,
			String TWYM_END, String startTWYM, String endTWYM, String type)
			throws CapException;

	/**
	 * 1. 授信契約已逾期控制表
	 * 
	 * @param OvUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType1ByBrNoAndDate(String OvUnitNo,
			Date dateStartDate, Date dateEndDate, String caseDept, String ctype)
			throws CapException;

	/**
	 * 2. 已敘做授信案件清單
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S01A> findType2ByBrNoAndDate(String rptMode, String ovUnitNo,
			Date dateStartDate, Date dateEndDate, String docStatusCode,
			String docKind, String docStatusCode2) throws CapException;

	/**
	 * 2. 已敘做授信案件清單 - 營運中心
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S01A> findType2ByAreaAndDate(String ovUnitNo,
			String docStatusCode, String docKind, String docStatusCode2,
			Date startDate, Date endDate) throws CapException;

	/**
	 * 3. 信保案件未動用屆期清單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param ctype2
	 * @param caseDept
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType3ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate, String caseDept)
			throws CapException;

	/**
	 * 5. 授信案件統計表(授管處、企劃處用)(Excel：無範本檔)
	 * 
	 * @param ovUnitNo
	 * @param benDate
	 * @param endDate
	 * @param otherCondition
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType5ByBrNoAndDate(List<IBranch> ovUnitNo,
			String benDate, String endDate, String otherCondition)
			throws CapException;

	/**
	 * 6. 營運中心每日授權外授信案件清單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType6ByBrNoAndDate(String brNo,
			String dataDate, String tCaseDept, String ctype)
			throws CapException;

	/**
	 * 7. 常董會及申報案件統計表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param tCaseDept
	 * @return
	 */

	List<L784S07A> findType7ByBrNoAndDate(String apprYY, String apprMM,
			String tCaseDept, String mainId);

	/**
	 * L784S07A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S07A> findL784S07AByMainId(String mainId) throws CapException;

	/**
	 * L784S07A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S07A> findL784S07AByMainIdBrNo(String mainId, String brNo)
			throws CapException;

	/**
	 * L784S07A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S07A> findL784S07AByMainIdApprYM(String mainId, String apprYY,
			String apprMM, String caseDept) throws CapException;

	/**
	 * L784S07A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S07A> findL784S07A(String mainId, String brNo, String apprYY,
			String apprMM, String caseDept) throws CapException;

	/**
	 * L784S01A-List
	 * 
	 * @param ovUnitNo
	 * @param dataDate
	 * @param caseDept
	 * @param ctype
	 * @return
	 * @throws CapException
	 */
	List<L784S01A> findL784S01AByMainId(String mainId) throws CapException;

	/**
	 * 儲存L784S07A model list
	 * 
	 * @param list
	 *            List<L784S07A>
	 */
	void savel784s07aList(List<L784S07A> list);

	/**
	 * 利用yyyy rpttype 查詢L784M01A資料
	 * 
	 * @param rpttype
	 * @param year
	 * @return
	 */
	public Map<String, Object> selL784M01A(String rpttype, String year);

	/**
	 * 7. 刪除常董會及申報案件明細檔(L784S07裡全部資料
	 */
	void delete784s07(String year, String month, String mainId);

	/**
	 * 8. 各級授權範圍內承做授信案件統計表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param tCaseDept
	 * @param ctype
	 * @return
	 */
	List<Map<String, Object>> findType8ByBrNoAndDate(String brNo,
			Date dataDate, String tCaseDept, String ctype);

	/**
	 * 找為金控總部銀行代碼
	 * 
	 * @return
	 */
	List<IBranch> findBranch201();

	/**
	 * 下載PDF
	 * 
	 * @param mainId
	 * @param rptType
	 * @return
	 */
	List<DocFile> findDocFile(String mainId, String docURL);

	/**
	 * findByMainId
	 * 
	 * @param mainId
	 * @return
	 */
	L784M01A findL784m01aByMainId(String mainId);

	/**
	 * 5. 授信案件統計表(授管處、企劃處用)(Excel：無範本檔)
	 * 
	 * @param list
	 * @param listName
	 */
	void tranSportExcel(List<Map<String, Object>> list, String listName);

	/**
	 * 9. 營業單位授信報案考核彙總表 產生EXCEL
	 * 
	 * @param list
	 * @param listName9
	 */
	void tranSportExcel9(List<Map<String, Object>> list, String caseDept,
			int action, Date dataStartDate, Date dataEndDate,
			Map<String, Map<String, String>> map01,
			Map<String, Map<String, String>> map02,
			Map<String, Map<String, String>> map01Over,
			Map<String, Map<String, String>> map02Over,
			List<IBranch> branchType0List, List<IBranch> branchType1List,
			List<IBranch> branchType2List, List<IBranch> branchType3List,
			List<IBranch> branchType4List, List<IBranch> branchType5List,
			List<IBranch> branchType6List, List<IBranch> branchType7List,
			List<IBranch> branchType8List, List<IBranch> branchType9SList,
			List<IBranch> branchType9List, String listName);

	/**
	 * 刪除
	 * 
	 * @param oids
	 * @param listName
	 * @return
	 */
	boolean delete(String[] oids, String listName);

	/**
	 * 傳送時，判斷 LMS.L784M01A.sendLastTime 是否有值
	 * 
	 * @param oid
	 * @return
	 */
	boolean cheaksendLastTime(String oid);

	/**
	 * 10. 海外分行過去半年內董事會（或常董會）權限核定之企業戶授信案件名單
	 * 
	 * @param brNo
	 * @param benDate
	 * @param endDate
	 * @return
	 * @throws CapException
	 */
	List<Map<String, Object>> findType10ByBrNoAndDate(String ovUnitNo,
			String benDate, String endDate) throws CapException;

}
