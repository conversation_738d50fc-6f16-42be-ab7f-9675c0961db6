package tw.com.jcs.flow.node;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * <h1>流程節點(狀態)</h1> 紀錄從流程定義檔所讀取的節點資訊
 * </pre>
 * 
 * <AUTHOR> Software Inc.
 */
@SuppressWarnings("rawtypes")
public abstract class FlowNode {

    /**
     * 節點名稱(TAG)
     */
    String tagName;

    /**
     * 節點名稱(狀態)
     */
    String name;

    /**
     * 節點狀態(對應狀態用)
     */
    String status;

    /**
     * 節點LOG flag(記錄LOG用)
     */
    String log;

    /**
     * 節點屬性
     */
    Map<String, String> attr = new HashMap<String, String>();

    /**
     * 節點transition屬性
     */
    Map<String, Map> transitionAttr = new HashMap<String, Map>();

    /**
     * 流程往下的所有分支(分支名稱->節點名稱)
     */
    Map<String, String> transitions = new LinkedHashMap<String, String>();

    /**
     * 節點transition屬性
     */
    Map<String, Map> buttons = new LinkedHashMap<String, Map>();

    public Map<String, Map> getButtons() {
        return buttons;
    }

    public void setButtons(Map<String, Map> buttons) {
        this.buttons = buttons;
    }

    /**
     * 預設的流程分支
     */
    String defaultTransition;

    /**
     * 取得節點名稱(TAG)
     * 
     * @return
     */
    public String getTagName() {
        return tagName;
    }

    /**
     * 設定節點名稱(TAG)
     * 
     * @param tagName
     */
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    /**
     * 取得節點名稱(狀態)
     * 
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * 設定節點名稱(狀態)
     * 
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 取得節點狀態(對應狀態用)
     * 
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * 設定節點狀態(對應狀態用)
     * 
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 取得節點LOG flag(記錄LOG用)
     * 
     * @return
     */
    public String getLog() {
        return log;
    }

    /**
     * 設定節點LOG flag(記錄LOG用)
     * 
     * @param log
     */
    public void setLog(String log) {
        this.log = log;
    }

    /**
     * 取得節點屬性
     * 
     * @return
     */
    public Map<String, String> getAttr() {
        return attr;
    }

    /**
     * 設定節點transition屬性
     * 
     * @param attr
     */
    public void setTransitionAttr(Map<String, Map> attr) {
        this.transitionAttr = attr;
    }

    /**
     * 取得節點transition屬性
     * 
     * @return
     */
    public Map<String, Map> getTransitionAttr() {
        return transitionAttr;
    }

    /**
     * 設定節點屬性
     * 
     * @param attr
     */
    public void setAttr(Map<String, String> attr) {
        this.attr = attr;
    }

    /**
     * 取得流程往下的所有分支(分支名稱->節點名稱)
     * 
     * @return
     */
    public Map<String, String> getTransitions() {
        return transitions;
    }

    /**
     * 設定流程往下的所有分支(分支名稱->節點名稱)
     * 
     * @param transitions
     */
    public void setTransitions(Map<String, String> transitions) {
        this.transitions = transitions;
    }

    /**
     * 取得預設的流程分支
     * 
     * @return
     */
    public String getDefaultTransition() {
        return defaultTransition;
    }

    /**
     * 設定預設的流程分支
     * 
     * @param defaultTransition
     */
    public void setDefaultTransition(String defaultTransition) {
        this.defaultTransition = defaultTransition;
    }

    /**
     * 結束流程實體目前的狀態
     * 
     * @param instance
     *            流程實體
     */
    protected void finishCurrentNode(FlowInstanceImpl instance) {
        // 先加上結束時間，並儲存目前狀態
        instance.setEndTime(new Date());
        FlowNode node = instance.getDefinition().getNodes().get(instance.getState());
        if (node instanceof StateNode || node instanceof StartNode || node instanceof EndNode) {
            instance.saveSequence();
        }
    }

    /**
     * 前往目前節點
     * 
     * @param instance
     *            流程實體
     */
    protected void changeToThisNode(FlowInstanceImpl instance) {
        // 更新狀態
        instance.setState(name);
        instance.setBeginTime(new Date());
        instance.setEndTime(null);

        if (this instanceof StateNode || this instanceof StartNode || this instanceof EndNode) {
            instance.setSeq(instance.getSeq() + 1);
            instance.save();
        }
    }

    /**
     * 結束目前流程實體，取得下一個流程並執行
     * 
     * @param instance
     *            流程實體
     */
    public abstract void next(FlowInstanceImpl instance);

}
