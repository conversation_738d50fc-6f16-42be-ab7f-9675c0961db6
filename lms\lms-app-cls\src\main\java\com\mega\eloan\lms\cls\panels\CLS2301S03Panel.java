/* 
 * LMS2305S03Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 簽約未動用授信案件送作業(個金)
 * </pre>
 * 
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public class CLS2301S03Panel extends Panel {

	public CLS2301S03Panel(String id) {
		super(id);
	}

	public CLS2301S03Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
