package com.mega.eloan.lms.dc.conf;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;

/**
 * <pre>
 * MainConfig
 * </pre>
 * 
 * @since 2013/2/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/21,UFO,new
 *          <li>2013/2/21,UFO,增加getProperties()
 *          <li>2013/2/22,UFO,增加取得DataBean
 *          <li>2013/3/07,UFO,rename:getConfigDataBean -> getConfig
 *          </ul>
 */
public class MainConfig {
	private static Logger logger = LoggerFactory.getLogger(MainConfig.class);
	public static final String CONFIG_FILE = "lmsdc/conf/dc-config.properties";

	private Properties properties = new Properties();
	private ConfigData config = new ConfigData();

	private static MainConfig minConfig = new MainConfig();

	private boolean onlineMode = false;

	public static MainConfig getInstance() {
		return minConfig;
	}

	private MainConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			throw new DCException("讀取Config設定檔錯誤！", ex);
		}
	}

	private void load() throws Exception {
		Configuration conf = new PropertiesConfiguration(CONFIG_FILE);
		properties.clear();

		Map<String, String> map = new HashMap<String, String>();
		Iterator<String> itor = conf.getKeys();
		while (itor.hasNext()) {
			String key = itor.next();
			map.put(key, conf.getString(key));
			properties.put(key, conf.getString(key));
		}
		BeanUtils.populate(this.config, map);
		this.config.init();
		this.setOnlineMode(this.config.isOnlineMode());
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	/**
	 * get the properties
	 * 
	 * @return the properties
	 */
	public Properties getProperties() {
		return properties;
	}

	/**
	 * get the configDataBean
	 * 
	 * @return the configDataBean
	 */
	public ConfigData getConfig() {
		if (logger.isDebugEnabled()) {
			logger.debug("##### [getConfig] CONFIG==>>\n" + this.config);
		}
		return config;
	}

	public ConfigData cloneConfig() {
		try {
			return (ConfigData) BeanUtils.cloneBean(this.config);
		} catch (Exception e) {
			throw new DCException(e);
		}
	}

	/**
	 * get the onlineMode
	 * 
	 * @return the onlineMode
	 */
	public boolean isOnlineMode() {
		return onlineMode;
	}

	/**
	 * set the onlineMode
	 * 
	 * @param onlineMode
	 *            the onlineMode to set
	 */
	public synchronized void setOnlineMode(boolean onlineMode) {
		this.onlineMode = onlineMode;
		if (this.onlineMode) {
			this.config.setOnlineData();
			this.showOnlineModeMsg();
		}
	}

	private void showOnlineModeMsg() {
		for (int i = 0; i < 3; i++) {
			logger.info("ONLINE_MODE !!!");
		}
	}
}
