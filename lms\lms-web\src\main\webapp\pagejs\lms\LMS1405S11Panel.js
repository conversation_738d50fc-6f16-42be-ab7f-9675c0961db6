$(function() {
    initBisGrid();
    initBisItem();

    $("#importCntr_OLD").click(function(){
        $("#bisCntrGridview").jqGrid("setGridParam", {
            postData: {
                init: false,
                mainId: responseJSON.mainid
            },
            search: true
        }).trigger("reloadGrid");

        $("#openBisCntrBox").thickbox({
            title: "",
            width: 800,
            height: 400,
            modal: true,
            i18n: i18n.def,
            align: "center",
            valign: "bottom",
            needPager: false,
            multiselect: true,
            buttons: {
                "sure": function(){
                    var rows = $("#bisCntrGridview").getGridParam('selarrrow');
                    var data = [];

                    if (rows == 'undefined' || rows == null || rows == "") {   // action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def["action_005"]);
                    }

                    for (var i in rows) {
                        data.push($("#bisCntrGridview").getRowData(rows[i]).oid);
                    }

                    $.ajax({
                        handler: "lms1405s11formhandler",
                        data: {
                            formAction: "importL120S25A_OLD",
                            mainId: responseJSON.mainid,
                            oids: data }
                        }).done(function(obj){
                            $("#gridviewBIS").trigger("reloadGrid");
                            $.thickbox.close();
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    });

    $("#importCntr").click(function(){
    	$.ajax({
            handler: "lms1405s11formhandler",
            data: {
                formAction: "importL120S25A",
                mainId: responseJSON.mainid }
            }).done(function(obj){
                $("#gridviewBIS").trigger("reloadGrid");
                $.thickbox.close();
        });
    });

    $("#delL120s25a").click(function(){

        var rows = $("#gridviewBIS").getGridParam('selarrrow');
    	var list = "";
    	var sign = ",";
    	for ( var i = 0; i < rows.length; i++) { // 將所有已選擇的資料存進變數list裡面
    		if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
    			var data = $("#gridviewBIS").getRowData(rows[i]);
    			list += ((list == "") ? "" : sign) + data.oid;
    		}
    	}
    	if (list == "") {
    		CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
    		return;
    	}

        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
            if(b){
                //var data = $("#gridviewEAD").getRowData(select);
                $.ajax({
                    handler: "lms1405s11formhandler",
                    data: {
                        formAction: "deleteL120S25A",
                        mainId: responseJSON.mainid,
                        listOid : list,
            			sign : sign
                        //cntrNoCo_s21a: data.cntrNoCo_s21a 
						}
                    }).done(function(obj){
                        $("#gridviewBIS").trigger("reloadGrid");
                });
            }else{
                return ;
            }
        });

    });

    $("#calcBis").click(function(){
    	 $.ajax({
             handler: "lms1405s11formhandler",
             data: {
                 formAction: "calcAllL120s25a",
                 mainId: responseJSON.mainid }
             }).done(function(obj){
                 $("#gridviewBIS").trigger("reloadGrid");
         });
    });
});

function initBisGrid(){
    $("#gridviewBIS").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: true,
        handler: "lms1205gridhandler",
        postData: {
            formAction: "queryL120s25aList",
            mainId: responseJSON.mainid
        },
        loadComplete: function(){
            $('#gridviewBIS a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s11["L120S25A.bisCustId"],//L120S25A.bisCustId=借款人統編
            name: 'bisCustId_s25a',
            align: 'left',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisCntrNo"],
            name: 'bisCntrNo_s25a',
            align: 'center',
            width: 22,
            sortable: false,
            formatter: 'click',
            onclick: function(cellvalue, options, rowObject){
                openBis(null, null, rowObject);
            }
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisApplyCurr"],
            name: 'bisApplyCurr',
            align: 'center',
            width: 8,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisApplyAmt"],
            name: 'bisApplyAmt',
            align: 'right',
            width: 25,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 2,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisSheetItem"],   //L120S25A.bisSheetItem=表內外
            name: 'bisSheetItem',
            align: 'center',
            width: 10,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisRiskAdjReturn"]+"<br>(%)"+ i18n.lms1405s11["L120S25A.kind1"],
            name: 'bisRiskAdjReturn',
            align: 'right',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisRiskAdjReturn"]+"<br>(%)"+ i18n.lms1405s11["L120S25A.kind3"],
            name: 'bisRiskAdjReturn_1',
            align: 'right',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisRItemD"]+"<br>(%)",
            name: 'bisRItemD',
            align: 'right',
            width: 22,
            sortable: false,
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                removeTrailingZero: true,
                decimalPlaces: 2,    //小數點到第幾位
                defaultValue: ""
            }
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisRorwa"]+"<br>(%)"+ i18n.lms1405s11["L120S25A.kind1"],
            name: 'bisRorwa',
            align: 'right',
            width: 14,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisRorwa"]+"<br>(%)"+ i18n.lms1405s11["L120S25A.kind3"],
            name: 'bisRorwa_1',
            align: 'right',
            width: 14,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisImpactNum"]+"<br>(%)",   //J-111-0443_05097_B1005 Web e-Loan企金授信開發授信BIS評估表
            name: 'bisImpactNum',
            align: 'right',
            width: 16,
            sortable: false
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewBIS").getRowData(rowid);
            openBis(null, null, data);
        }
    });

    $("#bisCntrGridview").iGrid({
        height: "230px",
        width: "100%",
        needPager: false,
        multiselect: true,
        handler: "lms1405gridhandler",
        action: "queryBisL140m01a",
        postData: {
            init: true
        },
        loadComplete: function(){
            $('#bisCntrGridview a').click(function(e){
                // 避免<a href="#"> go to top
                e.preventDefault();
            });
        },
        colModel: [{
            colHeader: i18n.lms1405s11["L120S25A.bisCustId"],//借款人名稱
            name: 'custId',
            align: 'left',
            width: 50,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisCntrNo"],
            name: 'cntrNo',
            align: 'center',
            width: 20,
            sortable: false
        }, {
            colHeader: i18n.lms1405s11["L120S25A.bisProPerty"],
            name: 'proPerty',
            align: 'left',
            width: 20,
            sortable: false
        }, {
        	colHeader: i18n.lms1405s11["L120S25A.bisApplyCurr"],  //L120S25A.bisApplyCurr=幣別
            name: 'bisApplyCurr',
            align: 'center',
            width: 10,
            sortable: false
        }, {
        	colHeader: i18n.lms1405s11["L120S25A.bisApplyAmt"],  //L120S25A.bisApplyAmt=額度
            name: 'bisApplyAmt',
            align: 'right',
            width: 20,
            sortable: false
        }, {
        	colHeader: i18n.lms1405s11["L120S25A.bisRItemD"]+"%",  //L120S25A.bisRItemD=抵減後風險權數
            name: 'bisRItemD',
            align: 'right',
            width: 20,
            sortable: false
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }]
    });

    $("#bisFtpRateGrid").iGrid({
        handler: "lms1201gridhandler",//inits.ghandle,
        needPager: false,
        multiselect: false,
        sortname: 'insCdCnm',
        sortorder: 'asc',
        postData : {
            formAction: "queryFtpRate"
        },
        colModel: [{
            colHeader: "項目",
            name: 'insCdCnm',
            align: "center",
            width: 80,
            sortable: false
        }, {
            colHeader: " ",
            name: 'insRt',
            align: "center",
            width: 20,
            sortable: false
        }]
    });
}

function initBisItem(){
    //select source
    var result_s = CommonAPI.loadCombos(["Common_Currcy", "bisSheetItem", "bisCcf","bisEstimatedType"]);

    //幣別
    $(".money").setItems({
        item: result_s.Common_Currcy,
        format: "{value} - {key}"
    });

    $("#bisSheetItem").setItems({
        item: result_s.bisSheetItem,
        format: "{key}"
    });

    $("#bisCcf").setItems({
        item: result_s.bisCcf,
        format: "{key}"
    });

    $("#bisEstimatedType").setItems({
        item: result_s.bisEstimatedType,
        format: "{key}"
    });
}

function openBis(cellvalue, options, rowObject) {
    var $form = $("#formBisDetail");
    $form.reset();

    var buttons = {
        "saveData": function(){
            if ($form.valid()) {
            	//J-113-0327 授信收益率及新作增額額度檢核
            	$.ajax({
                    handler: "lms1405s11formhandler",
                    data: {
                        formAction : "checkBeforeSaveL120S25A",
                        oid : DOMPurify.sanitize($("#oidL120S25A").val()),
                        bisIncomeRate : DOMPurify.sanitize($("#bisIncomeRate").val()),
                        bisFactAmtIncrease : DOMPurify.sanitize($("#bisFactAmtIncrease").val()),
                        bisFactAmtIncrease_1 : DOMPurify.sanitize($("#bisFactAmtIncrease_1").val()) }
                    }).done(function(obj){
                    	var chkMsg = DOMPurify.sanitize(obj.chkMsg);
                    	if(chkMsg){
                    		CommonAPI.confirmMessage(chkMsg, function(b){
                        		if(b){
                        			saveL120S25A();
                        			API.showMessage("儲存成功");
                        		}
                        	})
                    	} else {
                    		saveL120S25A();
                            $.thickbox.close();
                    	}
                });
                
            }
        },
        "close": function(){
            $.thickbox.close();
        }
    }

    $.ajax({
        handler: "lms1405s11formhandler",
        action: "queryL120S25A",
        data: {
            oid: rowObject.oid }
        }).done(function(obj){
            $form.injectData(obj);
            // $("#bisApplyCurr").attr("disabled", true).find("option[value='" + obj.bisApplyCurr + "']").attr("selected", true);
            $("#bisApplyCurr").prop("disabled", true);
            $("#bisSheetItem").trigger("change");
            
            //J-112-0389_05097_B1001 Web e-Loan調整企金e-Loan之RORWA風險成本計算方式
            if(obj.isShowBisBadDebtExp == "Y"){
            	$("#formBisDetail").find("#showBisBadDebtExp").show();
            	$("#formBisDetail").find('#bisRiskCostStr').val(i18n.lms1405s11["L120S25A.bisRiskCost"]);    //風險成本(預期損失)
            }else{
            	$("#formBisDetail").find("#showBisBadDebtExp").hide();
            	$("#formBisDetail").find('#bisRiskCostStr').val(i18n.lms1405s11["L120S25A.bisRiskCost_1"]);  //風險成本(全行平均)
            }
            
            $("#BisDetailThickbox").thickbox({
                title: "",
                width: 900,
                height: 550,
                modal: true,
                readOnly: _openerLockDoc == "1" || thickboxOptions.readOnly,
                i18n: i18n.def,
                buttons: buttons
            });
    });
}

function openDirBox(boxId, height) {
    var $dirBox = $("#"+boxId);

    $dirBox.thickbox({ // 使用選取的內容進行彈窗
        title: "",
        width: 500,
        height: height,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "close": function(){
                $.thickbox.close();
            }
        }
    });

}

function impBisFtpRate() {
    $("#bisFtpRateCurrBox").thickbox({
        title: i18n.lms1405s11['other.curr'],
        width: 100,
        height: 20,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        open: function(){
            $("input[name='bisFtpRateCurr'][value='TWD']").prop("checked", true);
        },
        buttons: {
            "sure": function(){
                var bisFtpRateCurr = $("[name=bisFtpRateCurr]:checked").val();
                if (bisFtpRateCurr == "") {
                    return CommonAPI.showErrorMessage(i18n.abstracteloan['plsSel']);
                }

                $.thickbox.close();

                $("#bisFtpRateGrid").jqGrid("setGridParam", {
                    postData: {
                        curr: bisFtpRateCurr
                    },
                    search: true
                }).trigger("reloadGrid");

                $("#impBisFtpRateThickBox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.def['grid_selector'],
                    width: 500,
                    height: 250,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                            var rowId = $("#bisFtpRateGrid").getGridParam('selrow');
                            if (rowId) {
                                var data = $("#bisFtpRateGrid").getRowData(rowId);
                                $("#formBisDetail").find("#bisFtpRate").val(data.insRt);
                                $.thickbox.close();
                            } else {
                                CommonAPI.showMessage(i18n.def['grid.selrow']);
                            }
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}


function impBisEstimatedReturn() {
	$.ajax({
		handler : "lms1405s11formhandler",
		type : "POST",
		dataType : "json",
		data : {
			formAction : "impBisEstimatedReturn",
			mainId : responseJSON.mainid,
			queryDateE0 : $("#formBisDetail").find("#bisEstimatedDateYear").val(),
			queryDateE1 : $("#formBisDetail").find("#bisEstimatedDateMonth").val(),
			qCntrNo : $("#formBisDetail").find("#bisCntrNo_s25a").val(),
			qOid:$("#oidL120S25A").val(),
			kind : "A"  //$("#formBisDetail").find("#bisEstimatedType").val() 
		}
		}).done(function(obj120) {
		    $("#formBisDetail").find("#bisEstimatedType").val('A');
			$("#formBisDetail").find("#bisNoneLoanProfit").val(obj120.bisNoneLoanProfit);
			$("#formBisDetail").find("#bisLoanBal").val(obj120.bisLoanBal);
			$("#formBisDetail").find("#bisFactAmtIncrease").val(obj120.bisFactAmtIncrease);
			$("#formBisDetail").find("#bisEstimatedReturn").val(obj120.bisEstimatedReturn);
			$("#formBisDetail").find("#bisNoneLoanProfit_1").val(obj120.bisNoneLoanProfit_1);
            $("#formBisDetail").find("#bisLoanBal_1").val(obj120.bisLoanBal_1);
            $("#formBisDetail").find("#bisFactAmtIncrease_1").val(obj120.bisFactAmtIncrease_1);
            $("#formBisDetail").find("#bisEstimatedReturn_1").val(obj120.bisEstimatedReturn_1);
	});
}

function calcBisEstimatedReturn() {
    $("#formBisDetail").find("#bisEstimatedReturn").val('');
    $("#formBisDetail").find("#bisEstimatedReturn_1").val('');

	$.ajax({
        handler: "lms1405s11formhandler",
        data: $.extend($("#formBisDetail").serializeData(), {
            formAction: "calcBisEstimatedReturn",
            mainId : responseJSON.mainid,
            oid: $("#oidL120S25A").val() })
        }).done(function(obj){
       	    $("#formBisDetail").find("#bisEstimatedReturn").val(obj.bisEstimatedReturn);
       	    $("#formBisDetail").find("#bisEstimatedReturn_1").val(obj.bisEstimatedReturn_1);
    });
}

function checkReadonly(){
    var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
    if (auth.readOnly || _openerLockDoc == "1") {
        return true;
    }
    return false;
}

//J-113-0327 儲存BIS評估表
function saveL120S25A(){
	$.ajax({
        handler: "lms1405s11formhandler",
        data: $.extend($("#formBisDetail").serializeData(), {
            formAction: "saveL120S25A",
            oid: DOMPurify.sanitize($("#oidL120S25A").val())
			})
        }).done(function(obj){
        	$("#formBisDetail").find("#bisEstimatedReturn").val(DOMPurify.sanitize(obj.bisEstimatedReturn));
            $("#gridviewBIS").trigger("reloadGrid");
    });
}

initDfd.done(function(auth) {
    if (checkReadonly() || thickboxOptions.readOnly) {
        $("#LMS1405S11Form01").find("button").hide();
        $("#formBisDetail").find("button").hide();
        $("#formBisDetail").readOnlyChilds(true);
    }

    $("#bisSheetItem").change(function(){
        var value = $(this).val();
        if(value == "Y"){
            $(".bisSheetItemShow").show();
        } else {
            $(".bisSheetItemShow").hide();
            $("#bisCcf").val('');
        }
    });
});