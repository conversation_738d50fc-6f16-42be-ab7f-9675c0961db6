package com.mega.eloan.lms.dc.util;

import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class XMLTool {
	private static Logger logger = LoggerFactory.getLogger(XMLTool.class);
	private static final XPathFactory xpathFactory = XPathFactory.newInstance();

	/**
	 * 讀取Dom物件中符合XPath所指定的第一個節點中的值 .
	 * 
	 * 
	 * Note:呼叫此Method時傳入Document 會比傳入Node時使用較少的計憶體[memory resource].
	 * 
	 * @param document
	 * @param xpath
	 */
	public static String selectSingleNodeValue(Node node, String xpath) {
		Node targetNode = selectSingleNode(node, xpath);
		return (targetNode == null ? ""
				: (targetNode.getFirstChild() == null) ? "" : (targetNode
						.getFirstChild().getNodeValue() == null) ? ""
						: targetNode.getFirstChild().getNodeValue().trim());
	}

	/**
	 * 取回所有符合XPATH所指定的NODE
	 * 
	 * @param xpath
	 * @param node
	 * @return 以NODE ARRAY的方式回傳符合的NODE
	 */
	public static Node[] getNodesArray(String xpath, Node node) {
		NodeList nodeList = getNodes(xpath, node);
		Node[] nodeArray = new Node[nodeList.getLength()];
		for (int i = 0; i < nodeArray.length; ++i) {
			nodeArray[i] = nodeList.item(i);
		}
		return nodeArray;
	}

	/**
	 * 取得XPATH所指定的所有NODE
	 * 
	 * @param xpath
	 * @param node
	 * @return 以XPathResult的方式回傳符合的NODE
	 */
	public static NodeList getNodes(String xpath, Node node) {
		return selectNodes(node, xpath);
	}

	/**
	 * 讀取符合XPath所指定的第一個節點中的值 .
	 * 
	 * 94/04/29 added.
	 * 
	 * @param node
	 * @param xpath
	 */
	public static NodeList selectNodes(Node node, String xpath) {
		XPath xp = xpathFactory.newXPath();
		try {
			return (NodeList) xp.evaluate(xpath, node, XPathConstants.NODESET);
		} catch (XPathExpressionException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 讀取Dom物件中符合XPath所指定的第一個節點中的值 .
	 * 
	 * 
	 * Note:呼叫此Mthod時傳入Document 會比傳入Node時使用較少的計憶體[memory resource].
	 * 
	 * @param node
	 * @param xpath
	 */
	public static Node selectSingleNode(Node node, String xpath) {
		if (node == null) {
			logger.info("Warning->參數document =null。");
			return null;
		}

		XPath xp = xpathFactory.newXPath();
		NodeList list = null;
		try {
			list = (NodeList) xp.evaluate(xpath, node, XPathConstants.NODESET);
			if (list.getLength() > 0) {
				return list.item(0);
			}
		} catch (XPathExpressionException e) {
			logger.error("讀取XML發生錯誤：{} [xpath={}]" + e.getMessage()
					+ xpath);
		}
		return null;
	}
}
