package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

/**
 * <pre>
 * 說明(企金授權外) - 營運概況
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201S05D")
public class LMS1201S05Page04 extends AbstractEloanForm {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.pages.AbstractEloanForm#execute(org.apache.wicket
	 * .PageParameters)
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) {

		renderJsI18N(LMS1201S05Page04.class);
		renderRespMsgJsI18N(new String[] { "EFD2061" }); // 多render一個msgi18n

	}

	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
    /*
     * (non-Javadoc)
     * 
     * @see com.mega.eloan.common.pages.AbstractEloanForm#getViewName()
     */
    @Override
    public String getViewName() {
        // 不要headerarea
        return "common/pages/None";
    }
}
