package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 與額度相關資訊維護檔 **/
public class ELF517 extends GenericBean  {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="ELF517_CONTRACT", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf517_contract;

	/** 建案完工未出售融資Y/N **/
	@Size(max=1)
	@Column(name="ELF517_BUILD_FG", length=1, columnDefinition="CHAR(1)")
	private String elf517_build_fg;

	/** 融資案件分類 **/
	@Size(max=1)
	@Column(name="ELF517_BUILD_ITEM", length=1, columnDefinition="CHAR(1)")
	private String elf517_build_item;

	/** 擔保品座落縣市別 **/
	@Size(max=1)
	@Column(name="ELF517_SITE1NO", length=1, columnDefinition="CHAR(1)")
	private String elf517_site1no;

	/** 擔保品座落鄉鎮市區別 **/
	@Column(name="ELF517_SITE2NO", columnDefinition="DECIMAL(2,0)")
	private Integer elf517_site2no;

	/** 擔保品座落段小段 **/
	@Column(name="ELF517_SITE3NO", columnDefinition="DECIMAL(4,0)")
	private Integer elf517_site3no;

	/** 擔保品座落村里 **/
	@Size(max=11)
	@Column(name="ELF517_SITE4NO", length=11, columnDefinition="CHAR(11)")
	private String elf517_site4no;

	/** 建檔時間 **/
	private String elf517_createtime;

	/** 建檔來源ELOAN/ALOAN **/
	@Size(max=5)
	@Column(name="ELF517_CREATEUNIT", length=5, columnDefinition="CHAR(5)")
	private String elf517_createunit;

	/** 最近異動時間 **/
	private String elf517_modifytime;

	/** 最近異動來源ELOAN/ALOAN **/
	@Size(max=5)
	@Column(name="ELF517_MODIFYUNIT", length=5, columnDefinition="CHAR(5)")
	private String elf517_modifyunit;

	/** 案號 **/
	@Size(max=20)
	@Column(name="ELF517_DOCUMENT_NO", length=20, columnDefinition="CHAR(20)")
	private String elf517_document_no;

	/** 初貸餘屋戶數 **/
	@Size(max=5)
	@Column(name="ELF517_BEG_FOR_SELL", columnDefinition="DECIMAL(5,0)")
	private BigDecimal firstLoanUnsoldHouseQuantity;
	
	/** 目前餘屋戶數 **/
	@Size(max=5)
	@Column(name="ELF517_NEW_FOR_SELL", columnDefinition="DECIMAL(5,0)")
	private BigDecimal currentUnsoldHouseQuantity;

	/** 本案授信期間年限-年 **/
	@Size(max=2)
	@Column(name="ELF517_DURING_YEAR", columnDefinition="DECIMAL(02,0)")
	private BigDecimal yearsOfCreditPeriod;
	
	/** 本案授信期間年限-月 **/
	@Size(max=2)
	@Column(name="ELF517_DURING_MONTH", columnDefinition="DECIMAL(02,0)")
	private String monthOfCreditPeriod;
	
	/** 本案最終授信期限止日(續約後) **/
	@Column(name="ELF517_END_DATE", columnDefinition="DATE")
	private String finalCreditPeriodEndDate;
	
	/** 取得額度序號 **/
	public String getElf517_contract() {
		return this.elf517_contract;
	}
	/** 設定額度序號 **/
	public void setElf517_contract(String value) {
		this.elf517_contract = value;
	}

	/** 取得建案完工未出售融資Y/N **/
	public String getElf517_build_fg() {
		return this.elf517_build_fg;
	}
	/** 設定建案完工未出售融資Y/N **/
	public void setElf517_build_fg(String value) {
		this.elf517_build_fg = value;
	}

	/** 取得融資案件分類 **/
	public String getElf517_build_item() {
		return this.elf517_build_item;
	}
	/** 設定融資案件分類 **/
	public void setElf517_build_item(String value) {
		this.elf517_build_item = value;
	}

	/** 取得擔保品座落縣市別 **/
	public String getElf517_site1no() {
		return this.elf517_site1no;
	}
	/** 設定擔保品座落縣市別 **/
	public void setElf517_site1no(String value) {
		this.elf517_site1no = value;
	}

	/** 取得擔保品座落鄉鎮市區別 **/
	public Integer getElf517_site2no() {
		return this.elf517_site2no;
	}
	/** 設定擔保品座落鄉鎮市區別 **/
	public void setElf517_site2no(Integer value) {
		this.elf517_site2no = value;
	}

	/** 取得擔保品座落段小段 **/
	public Integer getElf517_site3no() {
		return this.elf517_site3no;
	}
	/** 設定擔保品座落段小段 **/
	public void setElf517_site3no(Integer value) {
		this.elf517_site3no = value;
	}

	/** 取得擔保品座落村里 **/
	public String getElf517_site4no() {
		return this.elf517_site4no;
	}
	/** 設定擔保品座落村里 **/
	public void setElf517_site4no(String value) {
		this.elf517_site4no = value;
	}

	/** 取得建檔時間 **/
	public String getElf517_createtime() {
		return this.elf517_createtime;
	}
	/** 設定建檔時間 **/
	public void setElf517_createtime(String value) {
		this.elf517_createtime = value;
	}

	/** 取得建檔來源ELOAN/ALOAN **/
	public String getElf517_createunit() {
		return this.elf517_createunit;
	}
	/** 設定建檔來源ELOAN/ALOAN **/
	public void setElf517_createunit(String value) {
		this.elf517_createunit = value;
	}

	/** 取得最近異動時間 **/
	public String getElf517_modifytime() {
		return this.elf517_modifytime;
	}
	/** 設定最近異動時間 **/
	public void setElf517_modifytime(String value) {
		this.elf517_modifytime = value;
	}

	/** 取得最近異動來源ELOAN/ALOAN **/
	public String getElf517_modifyunit() {
		return this.elf517_modifyunit;
	}
	/** 設定最近異動來源ELOAN/ALOAN **/
	public void setElf517_modifyunit(String value) {
		this.elf517_modifyunit = value;
	}

	/** 取得案號 **/
	public String getElf517_document_no() {
		return this.elf517_document_no;
	}
	/** 設定案號 **/
	public void setElf517_document_no(String value) {
		this.elf517_document_no = value;
	}
	public BigDecimal getFirstLoanUnsoldHouseQuantity() {
		return firstLoanUnsoldHouseQuantity;
	}
	public void setFirstLoanUnsoldHouseQuantity(
			BigDecimal firstLoanUnsoldHouseQuantity) {
		this.firstLoanUnsoldHouseQuantity = firstLoanUnsoldHouseQuantity;
	}
	public BigDecimal getCurrentUnsoldHouseQuantity() {
		return currentUnsoldHouseQuantity;
	}
	public void setCurrentUnsoldHouseQuantity(BigDecimal currentUnsoldHouseQuantity) {
		this.currentUnsoldHouseQuantity = currentUnsoldHouseQuantity;
	}
	public BigDecimal getYearsOfCreditPeriod() {
		return yearsOfCreditPeriod;
	}
	public void setYearsOfCreditPeriod(BigDecimal yearsOfCreditPeriod) {
		this.yearsOfCreditPeriod = yearsOfCreditPeriod;
	}
	public String getMonthOfCreditPeriod() {
		return monthOfCreditPeriod;
	}
	public void setMonthOfCreditPeriod(String monthOfCreditPeriod) {
		this.monthOfCreditPeriod = monthOfCreditPeriod;
	}
	public String getFinalCreditPeriodEndDate() {
		return finalCreditPeriodEndDate;
	}
	public void setFinalCreditPeriodEndDate(String finalCreditPeriodEndDate) {
		this.finalCreditPeriodEndDate = finalCreditPeriodEndDate;
	}
}
