--LBELxx：一般分行
--LJELxx：簡易型分行
--LAELxx：營運中心
--LSELxx：授管處
--LDELxx：債管處
--LGELxx：稽核處 (總行G)
--LHELxx：徵信中心 (總行C)
--LOELxx：海外分行
--LMELxx：大陸分行 (目前Notes無)
--LFELxx：國外部/國金部/金控總部分行(X)
--LKELxx：國外部/國金部/金控總部分行
--LPELxx：海外分行(有海外總行的海外分行)
--LQELxx：海外總行(澳洲/加拿大)
--LRELxx：海外總行(泰國)

--#select * from COM.BELSRLE where TYPE='L' order by ROLCODE;
delete from COM.BELSRLE where TYPE='L';


--################--
--##一般角色設定##--
--################--

--------------------
-- 一般分行(B) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL00', '3', 'L', 'B', '0', '分行查詢        ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL01', '3', 'L', 'B', '0', '分行經辦        ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL02', '3', 'L', 'B', '0', '分行覆核        ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL03', '3', 'L', 'B', '0', '分行收件員      ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL04', '3', 'L', 'B', '0', '分行覆審        ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL05', '3', 'L', 'B', '0', '分行放審委員    ', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL06', '3', 'L', 'B', '0', '分行內部查核人員', '一般分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 簡易型分行(J) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL00', '3', 'L', 'J', '0', '分行查詢        ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL01', '3', 'L', 'J', '0', '分行經辦        ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL02', '3', 'L', 'J', '0', '分行覆核        ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL03', '3', 'L', 'J', '0', '分行收件員      ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL04', '3', 'L', 'J', '0', '分行覆審        ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL05', '3', 'L', 'J', '0', '分行放審委員    ', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LJEL06', '3', 'L', 'J', '0', '分行內部查核人員', '簡易型分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 營運中心(A) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL00', '3', 'L', 'A', '0', '營運中心查詢        ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL01', '3', 'L', 'A', '0', '營運中心經辦        ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL02', '3', 'L', 'A', '0', '營運中心覆核        ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL03', '3', 'L', 'A', '0', '營運中心收件員      ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL04', '3', 'L', 'A', '0', '營運中心覆審        ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL05', '3', 'L', 'A', '0', '營運中心放審委員    ', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LAEL06', '3', 'L', 'A', '0', '營運中心內部查核人員', '營運中心', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 授管處(S) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL00', '3', 'L', 'S', '0', '授管處查詢        ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL01', '3', 'L', 'S', '0', '授管處經辦        ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL02', '3', 'L', 'S', '0', '授管處覆核        ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL03', '3', 'L', 'S', '0', '授管處收件員      ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL04', '3', 'L', 'S', '0', '授管處覆審        ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL05', '3', 'L', 'S', '0', '授管處放審委員    ', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL06', '3', 'L', 'S', '0', '授管處內部查核人員', '授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 稽核處(G) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL00', '3', 'L', 'G', '0', '稽核處查詢        ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL01', '3', 'L', 'G', '0', '稽核處經辦        ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL02', '3', 'L', 'G', '0', '稽核處覆核        ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL03', '3', 'L', 'G', '0', '稽核處收件員      ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL04', '3', 'L', 'G', '0', '稽核處覆審        ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL05', '3', 'L', 'G', '0', '稽核處放審委員    ', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LGEL06', '3', 'L', 'G', '0', '稽核處內部查核人員', '稽核處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 海外分行(O) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL00', '3', 'L', 'O', '0', '分行查詢        ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL01', '3', 'L', 'O', '0', '分行經辦        ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL02', '3', 'L', 'O', '0', '分行覆核        ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL03', '3', 'L', 'O', '0', '分行收件員      ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL04', '3', 'L', 'O', '0', '分行覆審        ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL05', '3', 'L', 'O', '0', '分行放審委員    ', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LOEL06', '3', 'L', 'O', '0', '分行內部查核人員', '海外分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);



--####################--
--##特殊分行角色設定##--
--####################--

--------------------
-- 國外部/國金部/金控總部分行(F) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL00', '3', 'L', 'F', '0', '分行查詢        ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL01', '3', 'L', 'F', '0', '分行經辦        ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL02', '3', 'L', 'F', '0', '分行覆核        ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL03', '3', 'L', 'F', '0', '分行收件員      ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL04', '3', 'L', 'F', '0', '分行覆審        ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL05', '3', 'L', 'F', '0', '分行放審委員    ', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LKEL06', '3', 'L', 'F', '0', '分行內部查核人員', '國外部/國金部/金控總部', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 海外分行(有海外總行)(P) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL00', '3', 'L', 'P', '0', '分行查詢        ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL01', '3', 'L', 'P', '0', '分行經辦        ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL02', '3', 'L', 'P', '0', '分行覆核        ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL03', '3', 'L', 'P', '0', '分行收件員      ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL04', '3', 'L', 'P', '0', '分行覆審        ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL05', '3', 'L', 'P', '0', '分行放審委員    ', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LPEL06', '3', 'L', 'P', '0', '分行內部查核人員', '海外分行(有海外總行)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 海外總行(澳洲/加拿大)(Q) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL00', '3', 'L', 'Q', '0', '分行查詢        ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL01', '3', 'L', 'Q', '0', '分行經辦        ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL02', '3', 'L', 'Q', '0', '分行覆核        ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL03', '3', 'L', 'Q', '0', '分行收件員      ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL04', '3', 'L', 'Q', '0', '分行覆審        ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL05', '3', 'L', 'Q', '0', '分行放審委員    ', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LQEL06', '3', 'L', 'Q', '0', '分行內部查核人員', '海外總行(澳洲/加拿大)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 海外總行(泰國)(R) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL00', '3', 'L', 'R', '0', '分行查詢        ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL01', '3', 'L', 'R', '0', '分行經辦        ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL02', '3', 'L', 'R', '0', '分行覆核        ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL03', '3', 'L', 'R', '0', '分行收件員      ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL04', '3', 'L', 'R', '0', '分行覆審        ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL05', '3', 'L', 'R', '0', '分行放審委員    ', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LREL06', '3', 'L', 'R', '0', '分行內部查核人員', '海外總行(泰國)', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);



--######################--
--##跨單位登入角色設定##--
--######################--

--------------------
-- 稽核處登入分行(BG) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL00G', '3', 'L', 'B', '0', '稽核處查詢        ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL01G', '3', 'L', 'B', '0', '稽核處經辦        ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL02G', '3', 'L', 'B', '0', '稽核處覆核        ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL03G', '3', 'L', 'B', '0', '稽核處收件員      ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL04G', '3', 'L', 'B', '0', '稽核處覆審        ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL05G', '3', 'L', 'B', '0', '稽核處放審委員    ', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL06G', '3', 'L', 'B', '0', '稽核處內部查核人員', '稽核處登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 營運中心登入分行(BA) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL00A', '3', 'L', 'B', '0', '營運中心查詢        ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL01A', '3', 'L', 'B', '0', '營運中心經辦        ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL02A', '3', 'L', 'B', '0', '營運中心覆核        ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL03A', '3', 'L', 'B', '0', '營運中心收件員      ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL04A', '3', 'L', 'B', '0', '營運中心覆審        ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL05A', '3', 'L', 'B', '0', '營運中心放審委員    ', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LBEL06A', '3', 'L', 'B', '0', '營運中心內部查核人員', '營運中心登入分行', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);


--------------------
-- 營運中心登入授管處(SA) --
--------------------
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL00A', '3', 'L', 'S', '0', '營運中心查詢        ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL01A', '3', 'L', 'S', '0', '營運中心經辦        ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
  INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL02A', '3', 'L', 'S', '0', '營運中心覆核        ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL03A', '3', 'L', 'S', '0', '營運中心收件員      ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL04A', '3', 'L', 'S', '0', '營運中心覆審        ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL05A', '3', 'L', 'S', '0', '營運中心放審委員    ', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);
--INSERT INTO COM.BELSRLE(ROLCODE, DATASTU, TYPE, UNIT, STU, ROLNAME, NOTE, UPDATER, UPDTIME, APPRID, APPRTIME) values ('LSEL06A', '3', 'L', 'S', '0', '營運中心內部查核人員', '營運中心登入授管處', 'SYSTEM', current timestamp, 'SYSTEM', current timestamp);

