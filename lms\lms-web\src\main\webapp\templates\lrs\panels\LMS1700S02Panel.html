<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1700S02Panel');
			</script>
			<!-- ====================================================================== -->
            <fieldset>
                <legend>
                    <b>
                        <th:block th:text="#{'subtitle.21'}">一般授信資料</th:block>
                    </b>
                </legend>                
                <div>
                	<table width="860">
                		<tr>
                			<td>&nbsp;</td>
							<td class='noborder rt'><th:block th:text="#{'label.curr_amtUnit'}">金額單位：新台幣仟元</th:block></td>
                		</tr>
                	</table>
					
                    <table width="860" class="tb2" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'L170M01A.totQuota'}">額度合計</th:block>
                            </td>
                            <td width="30%">
                            	<span id="totQuotaCurr"></span>&nbsp;&nbsp;<span id="totQuota"></span>&nbsp;&nbsp;                            	
                            </td>
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'L170M01A.totBal'}">前日結欠餘額合計</th:block>
                            </td>
                            <td width="30%">
                            	<span id="totBalCurr"></span>&nbsp;&nbsp;<span id="totBal"></span>&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="funcContainer">
                    <table width="100%">
                        <tr>
                            <td>
                            	<th:block th:if="${show_lnBtn}">
								
                                <button id="btn_insertPEO" type="button">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.add'}">新增</th:block>
                                    </span>
                                </button>
                                &nbsp;&nbsp;
                                <button id="btn_deletePEO" type="button">
                                    <span class="text-only">
                                        <th:block th:text="#{'btn.deletePEO'}">刪除(經辦自行新增)</th:block>
                                    </span>
                                </button>
                                &nbsp;&nbsp;
                                <button id="btn_importLnData" type="button">
                                    <span class="text-only">
                                        <th:block th:text="#{'btn.importLN'}">產生所有授信資料</th:block>
                                    </span>
                                </button>
								&nbsp;&nbsp;
                                <button id="btn_delteAllLnData" type="button">
                                    <span class="text-only">
                                        <th:block th:text="#{'btn.deleteLN'}">刪除所有授信資料</th:block>
                                    </span>
                                </button>
								</th:block>
							&nbsp;	
                            </td>
                            <td class='rt' nowrap>
                            	<th:block th:text="#{'L170M01A.lnDataDate'}">授信資料引進日期</th:block>：
								<span class="color-blue" id="lnDataDate" ></span>
								<div >
									<button type="button" id="btn_importBefText"><span class="text-only"><th:block th:text="#{'btn.importBefText'}">匯入前次覆審意見資料</th:block></span></button>									
									<button type="button" id="btn_printBefReviewDoc" class='forview'><span class="text-only"><th:block th:text="#{'btn.printBefReviewDoc'}">調閱前次覆審報告</th:block></span></button>									
								</div>
                            </td>
                        </tr>
                    </table>
                </div>
				
                <div id="gridL170M01B" ></div>
				
				<div id="div_gridL170M01B_PEO" style='display:none'>
					<div id="gridL170M01B_PEO" ></div>
				</div>
            </fieldset>
        </th:block>
    </body>
</html>
