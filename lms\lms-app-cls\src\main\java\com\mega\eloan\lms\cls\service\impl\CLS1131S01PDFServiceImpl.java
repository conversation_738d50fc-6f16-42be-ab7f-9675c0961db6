package com.mega.eloan.lms.cls.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import javax.annotation.Resource;

import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.base.common.ClsScoreUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.ScoreService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

@Service("cls1131s01pdfservice")
public class CLS1131S01PDFServiceImpl implements FileDownloadService {
	
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131S01PDFServiceImpl.class);

	@Resource
	ScoreService scoreService;
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException, IOException, URISyntaxException {
		
		
		String markModel = params.getString("markModel", UtilConstants.L140S02AModelKind.房貸);
		String fileSrcName = "HouseLoanNoticeItem.pdf";
		
		if(Util.equals(markModel, UtilConstants.L140S02AModelKind.房貸)){ //值=1
			String varVer = Util.trim(params.getString("varVer"));
			if(Util.isEmpty(varVer)){
				varVer = scoreService.get_Version_HouseLoan();
			}
			if(Util.equals(ClsScoreUtil.V2_0_HOUSE_LOAN, varVer)){
				fileSrcName = "HouseLoanNoticeItem_V2_0.pdf";	
			}else if(Util.equals(ClsScoreUtil.V2_1_HOUSE_LOAN, varVer)){
				fileSrcName = "HouseLoanNoticeItem_V2_1.pdf";	
			}
		}else if(Util.equals(markModel, UtilConstants.L140S02AModelKind.非房貸)){ //值=2
			fileSrcName = "NotHouseLoanNoticeItem.pdf";
			String varVer = Util.trim(params.getString("varVer"));
			if(Util.isEmpty(varVer)){
				varVer = scoreService.get_Version_NotHouseLoan();
			}
			if(Util.equals(ClsScoreUtil.V2_0_NOT_HOUSE_LOAN, varVer)){
				fileSrcName = "NotHouseLoanNoticeItem_V2_0.pdf";	
			}else if(Util.equals(ClsScoreUtil.V2_1_NOT_HOUSE_LOAN, varVer)){
				fileSrcName = "NotHouseLoanNoticeItem_V2_1.pdf";	
			}else if(Util.equals(ClsScoreUtil.V3_0_NOT_HOUSE_LOAN, varVer)){
				fileSrcName = "NotHouseLoanNoticeItem_V3_0.pdf";
			}else if(Util.equals(ClsScoreUtil.V3_1_NOT_HOUSE_LOAN, varVer)){
				fileSrcName = "NotHouseLoanNoticeItem_V3_1.pdf";
			}
		}else if(Util.equals(markModel, UtilConstants.L140S02AModelKind.卡友貸)){ //值=3
			fileSrcName = "CardLoanNoticeItem.pdf";
			String varVer = Util.trim(params.getString("varVer"));
			if(Util.isEmpty(varVer)){
				varVer = scoreService.get_Version_CardLoan();
			}
			if(Util.equals(ClsScoreUtil.V2_1_CARD_LOAN, varVer)){
				fileSrcName = "CardLoanNoticeItem_V2_1.pdf";
			}else if(Util.equals(ClsScoreUtil.V3_0_CARD_LOAN, varVer)){
				fileSrcName = "CardLoanNoticeItem_V3_0.pdf";
			}else if(Util.equals(ClsScoreUtil.V3_1_CARD_LOAN, varVer)){
				fileSrcName = "CardLoanNoticeItem_V3_1.pdf";
			}
		}else if(Util.equals(markModel, "J10_REASON")){
			// 之前在做 日本 的消金申請模型時, 已有J10理由的PDF 
			fileSrcName = "JP_J10_REASON.pdf";
		}else if(Util.equals(markModel, "TW_J10_REASON")){			 
			fileSrcName = "TW_J10_REASON.pdf";
		}else if(Util.equals(markModel, "L")){//借用L 來當地政士填寫說明
			fileSrcName = "LaaNoticeItem.pdf";
		}else if(Util.equals(markModel, "D")){//借用D 來當個人負債比說明
			fileSrcName = "DRateRegulations.pdf";
			// J-113-0267 個金徵信中「負債比規定」檔案抽換為「個人負債比率控管_113.05版」
			fileSrcName = "DRateRegulations_11305.pdf";
			// J-113-0352 配合113年9月3日兆銀消金字第1130000205號函，修訂「消金授信個人負債比率控管規定」修改
			fileSrcName = "DRateRegulations_11309.pdf";
		}else if(Util.equals(markModel, "OneBtnQuery")){
			fileSrcName = "OneBtnQuery_V4_111_4_8.pdf";
		}else if(Util.equals(markModel, "DifficultCharSoln")){
			fileSrcName = "DifficultCharSoln.pdf";
		}else if(Util.equals(markModel, "DRatControlRuleDesc")){
			fileSrcName = "DRatControlRuleDesc_0510_6.pdf";
		}else if(Util.equals(markModel, "HLoanPercentSummaryTable")){//房貸核貸成數彙總表(110年8月版)
			fileSrcName = "HLoanPercentSummaryTable_11008.pdf";
		}else if(Util.equals(markModel, "CreditIncome")){// 消金授信業務收入認列(112.02版)
			// J-113-0273 調整個金徵信檢視收入內容於點選「相關說明」後開啟PDF檔
			fileSrcName = "CreditIncome_v11202.pdf";
		}
		String tempPath = PropUtil.getProperty("loadFile.dir")+ "pdf/"+fileSrcName;
		
		File file = new File(Thread.currentThread().getContextClassLoader()
				.getResource(tempPath).toURI());
		byte[] bytes = FileUtil.readAsByteArray(file);
		return bytes;
	}
}
