/* 
 * L120M01K.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 簽報書逾權記錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120M01K", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120M01K extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 借款人姓名 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 
	 * 逾權類別<p/>
	 * 1: 授信<br/>
	 *  2: 出口押匯業務<br/>
	 *  3: 單獨劃分授信
	 */
	@Size(max=2)
	@Column(name="OVERTYPE", length=2, columnDefinition="VARCHAR(2)")
	private String overType;

	/** 逾權計算說明 **/
	@Size(max=2000)
	@Column(name="DETAIL", length=2000, columnDefinition="VARCHAR(2000)")
	private String detail;

	/** PD分組 **/
	@Size(max=2000)
	@Column(name="PDGROUP", length=10, columnDefinition="VARCHAR(10)")
	private String pdGroup;
	
	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 全案借款人合併資料註記 **/
	@Size(max=2)
	@Column(name="ISFULLCASEFLAG", length=2, columnDefinition="VARCHAR(2)")
	private String isFullCaseFlag;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得借款人姓名 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定借款人姓名 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 
	 * 取得逾權類別<p/>
	 * 1: 授信<br/>
	 *  2: 出口押匯業務<br/>
	 *  3: 單獨劃分授信
	 */
	public String getOverType() {
		return this.overType;
	}
	/**
	 *  設定逾權類別<p/>
	 *  1: 授信<br/>
	 *  2: 出口押匯業務<br/>
	 *  3: 單獨劃分授信
	 **/
	public void setOverType(String value) {
		this.overType = value;
	}

	/** 取得逾權計算說明 **/
	public String getDetail() {
		return this.detail;
	}
	/** 設定逾權計算說明 **/
	public void setDetail(String value) {
		this.detail = value;
	}

	/** 取得PD分組 **/
	public String getPdGroup() {
		return this.pdGroup;
	}
	/** 設定PD分組 **/
	public void setPdGroup(String value) {
		this.pdGroup = value;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	
	/** 取得全案借款人合併資料註記**/
	public String getIsFullCaseFlag() {
		return this.isFullCaseFlag;
	}
	/** 設定全案借款人合併資料註記 **/
	public void setIsFullCaseFlag(String value) {
		this.isFullCaseFlag = value;
	}
}
