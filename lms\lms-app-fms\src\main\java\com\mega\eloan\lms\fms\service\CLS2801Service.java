package com.mega.eloan.lms.fms.service;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140MM2A;
import com.mega.eloan.lms.model.L140S02L;


public interface CLS2801Service extends AbstractService{
	L140S02L findL140S02L_oid(String oid);
	L140S02L findByUniqueKey(String mainId, Integer seq);
	L140MM2A findL140MM2A_oid(String oid);
	L140MM2A findL140MM2A_MainIdSeq(String mainId, Integer seq);
}
