package com.mega.eloan.lms.lns.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**<pre>
 * 綜合評估/往來彙總(企金授權內)
 * </pre>
 * @since  2012/1/19
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMS1101S06Panel extends Panel {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;
	
	public LMS1101S06Panel(String id) {
		super(id);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		
		new LMS1101S06_Panel("lms1105s06_panel").processPanelData(model, params);
	}
}
