#==================================================
# \u8986\u5be9\u5831\u544a\u8868-\u5916Grid
#==================================================
C241M01a.custId=\u7edf\u4e00\u7f16\u53f7
C241M01a.custName=\u5ba2\u6237\u540d\u79f0
C241M01a.dupNo=\u91cd\u590d\u5e8f\u53f7
C241M01a.retrialDate=\u5b9e\u9645\u8986\u5ba1\u65e5\u671f
C241M01a.lastRetrialDate=\u4e0a\u6b21\u8986\u5ba1\u65e5\u671f
C241M01a.docStatus=\u6587\u4ef6\u72b6\u6001
C241M01a.condition=\u8986\u5ba1\u610f\u89c1
C241M01a.branchComm=\u6d3d\u529e\u60c5\u5f62
C241M01a.insertData=\u65b0\u589e\u8986\u5ba1\u62a5\u544a\u8868

chooseNckdflag=\u8bf7\u9009\u62e9\u4e0d\u8986\u5ba1\u539f\u56e0
