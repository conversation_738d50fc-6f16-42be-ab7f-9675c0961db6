/* 
 * compareExpPsr.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.dc.action;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.RptUtil;
import com.mega.eloan.lms.dc.util.TextDefine;

/**
 * <pre>
 * TcompareExpPsr :比對export及parser後的資料筆數差異
 * </pre>
 * 
 * @since 2013/3/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/6,Bang,new
 *          </ul>
 */
public class CompareExpPsr extends BaseAction {

	protected Logger logger = LoggerFactory.getLogger(CompareExpPsr.class);
	private static String schema = "";// 目前執行的系統名稱:LMS或CLS
	private String logsDirPath = "";
	private String dxlDirRootPath = "";
	private static String lmsView = "";
	private static String clsView = "";
	private PrintWriter writeCom = null;

	/**
	 * @param args
	 */
	public static void main(String[] args) {

		try {
			CompareExpPsr cep = new CompareExpPsr();

			if(!validate_args(args)){
				return;
			}
			
			if (args.length == 0) {
				throw new DCException("請輸入程式參數！");
			}
			cep.initParam(args);
			cep.countExpPsr();
		} catch (Exception e) {
			e.printStackTrace();
			throw new DCException(e);
		}
	}

	private static boolean validate_args(String[] args){
		String[] sysArr = new String[]{TextDefine.SCHEMA_LMS, TextDefine.SCHEMA_CLS};
		if(args.length==0 || !ArrayUtils.contains(sysArr, args[0])){
			return false;
		}		
		return true;
	}
	
	/**
	 * 初始化參數資訊
	 * 
	 * @param args
	 */
	private void initParam(String[] args) {
		try {
			schema = args[0];
			if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
				dxlDirRootPath = configData.getLmsDxlDirRootPath();// homePath\today\LMS
				logsDirPath = configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
				lmsView = configData.getLMSViewName();
			} else {
				dxlDirRootPath = configData.getClsDxlDirRootPath();// homePath\today\CLS
				logsDirPath = configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
				clsView = configData.getCLSViewName();
			}
			String dtFile = logsDirPath + File.separator + schema
					+ "_compareExpPsr.log";
			writeCom = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(
							new File(dtFile)), TextDefine.ENCODING_UTF8)), true);
			StringBuffer sb = new StringBuffer()
					.append(RptUtil.fillL("分行 ;", 7))
					.append(RptUtil.fillL("View名稱;", 12))
					.append(RptUtil.fillL("Form名稱;", 12)).append("Export總筆數;")
					.append(RptUtil.fillL("ParserTable名稱;", 30))
					.append(RptUtil.fillL("DB2Table名稱;", 20))
					.append("Parser總筆數;").append("是否吻合");
			writeCom.println(sb.toString());

		} catch (Exception e) {
			String errmsg = new StringBuffer().append(
					"執行CompareExpPsr 之initParam時產生錯誤").toString();
			logger.error(errmsg, e);
			throw new DCException(errmsg + "==>" + e.getLocalizedMessage(), e);
		}

	}

	/**
	 * 比對export與parser之資料筆數
	 */
	private void countExpPsr() {
		@SuppressWarnings("unused")
		List<String> brnoList = BrnoConfig.getInstance().getBrnoList();
		String[] view = null;
		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			view = lmsView.split(TextDefine.SYMBOL_SEMICOLON);
		} else if (TextDefine.SCHEMA_CLS.equalsIgnoreCase(schema)) {
			view = clsView.split(TextDefine.SYMBOL_SEMICOLON);
		}

		File file = new File(this.dxlDirRootPath);
		for (File fe : DXLUtil.list_subdir(file)) {
//		for (File fe : file.listFiles()) {
			// 第一層 分行代號
			if (fe.isDirectory()) {
				for (String viewName : view) {
					getFormCount(fe.getName(), viewName);
				}
			}
		}

		// for (String brno : brnoList) {
		// for (String viewName : view) {
		// getFormCount(brno, viewName);
		// }
		// }
		if (writeCom != null) {
			IOUtils.closeQuietly(writeCom);
		}
	}

	/**
	 * 統計view下所有的Export與parser的筆數
	 * 
	 * @param brno
	 *            String:分行別
	 * @param viewName
	 *            String:viewName
	 */
	private void getFormCount(String brno, String viewName) {
		try {
			// 取得Export後的檔案
			String expRoot = dxlDirRootPath + File.separator + brno
					+ File.separator + viewName;

			Collection<File> expFilesList = getFileList(expRoot, "dxl");
			Map<String, Integer> erpMap = new LinkedHashMap<String, Integer>();
			String tmpForm = "";
			if (null != expFilesList) {
				for (File exp : expFilesList) {
					String expName = exp.getName();
					String formName = expName.split("_")[0];
					if (!tmpForm.equalsIgnoreCase(formName)) {
						String expKey = brno + "_" + viewName + ";" + formName;// Ex:002_VCLS10105;FCLS106M01
						int expCount = this.getSameNameList(expRoot, formName)
								.size();
						erpMap.put(expKey, expCount);
					}
					tmpForm = formName;
				}
			}

			// 取得parser後的檔案
			String psrRoot = dxlDirRootPath + File.separator + brno
					+ File.separator + viewName + this.configData.getTextPath();
			Collection<File> psrFilesList = getFileList(psrRoot, "txt");
			Map<String, Integer> psrMap = new LinkedHashMap<String, Integer>();
			if (null != psrFilesList) {
				for (File psr : psrFilesList) {
					if (psr.getName().equalsIgnoreCase("DXL_Key.txt")
							|| psr.getName().equalsIgnoreCase("L120M01C.txt")) {
						continue;
					}
					String ftName = psr.getName().split(".txt")[0];
					String psrKey = brno + "_" + viewName + ";" + ftName; // Ex:002_VCLS10105;FCLS106M01_L120M01A
					int psrCount = this.getFileLen(psr.getAbsolutePath());
					psrMap.put(psrKey, psrCount);
				}
			}
			// 開始比對
			for (Map.Entry<String, Integer> expEntry : erpMap.entrySet()) {
				String eForm = expEntry.getKey().split(";")[1];// Ex:L120M01A
				for (Map.Entry<String, Integer> psrEntry : psrMap.entrySet()) {
					String pFullName = psrEntry.getKey();// Ex:004_VLMSDB201B;FLMS110M01_L120M01A_BF
					String fTableName = pFullName.split(";")[1];// Ex:L120M01A_BF
					String pForm = fTableName.split("_")[0];// Ex:FLMS110M01

					if (eForm.equalsIgnoreCase(pForm)) {
						String isMatch = "";
						int eCount = expEntry.getValue();
						int pCount = psrEntry.getValue();
						if (eCount == pCount) {
							isMatch = "Y";
						} else {
							isMatch = "N";
						}
						String errstr = "【" + brno + "】;【"
								+ RptUtil.fillL(viewName, 10) + "】;" + "【"
								+ RptUtil.fillL(eForm, 10) + "】;"
								+ RptUtil.fillR(eCount, 10) + ";" + "【"
								+ RptUtil.fillL(fTableName, 28) + "】;" + "【"
								+ RptUtil.fillL(fTableName.substring(pForm.length()+1), 20) + "】;"
								+ RptUtil.fillR(pCount, 10) + ";"
								+ RptUtil.fillL("", 3) + isMatch;
						writeCom.println(errstr);
					}
				}
			}

			// String tmpForm = "";
			// int expCount = 0;// Export同一formName之計數器
			//
			// if (null != expFilesList) {
			// for (File exp : expFilesList) {
			// String expName = exp.getName();
			// String formName = expName.split("_")[0];
			// if (!tmpForm.equalsIgnoreCase(formName)) {
			// expCount = this.getSameNameList(expRoot, formName)
			// .size();
			// }
			// int count = 0;// Parser檔案內容筆數之計數器
			// if (null != psrFilesList
			// && !tmpForm.equalsIgnoreCase(formName)) {
			// for (File psr : psrFilesList) {
			// String tableName = psr.getName();
			// String fName = tableName.split("_")[0];
			// // Export formName與paser的formName相同者再進來
			// if (fName.equalsIgnoreCase(formName)) {
			// count = getFileLen(psrRoot + File.separator
			// + tableName);
			// String isMatch = "";
			// if (expCount == count) {
			// isMatch = "Y";
			// } else {
			// isMatch = "N";
			// }
			// String errstr = "【" + brno + "】;【"+ RptUtil.fillL(viewName,10) +
			// "】;"
			// + "【"+RptUtil.fillL(formName,10)+ "】;" +
			// RptUtil.fillR(expCount,10) + ";"
			// + "【"+ RptUtil.fillL(tableName,28) + "】;" +
			// RptUtil.fillR(count,10) + ";"
			// + RptUtil.fillL("",3)+isMatch;
			// writeCom.println(errstr);
			// }
			// }// end of for (File psr : psrFilesList)
			// tmpForm = formName;
			// }
			// }// end of for (File exp : expFilesList)
			// }

		} catch (Exception e) {
			String errmsg = new StringBuffer().append(
					"執行CompareExpPsr 之getFormCount時產生錯誤").toString();
			logger.error(errmsg, e);
			throw new DCException(e);
		}
	}

	/**
	 * 取得fileRootPath下符合附加檔名之檔案
	 * 
	 * @param fileRootPath
	 *            String :資料夾路徑
	 * @param erp
	 *            String :附加檔名 Ex:java,xml...
	 * @return files Collection<File>
	 */
	private Collection<File> getFileList(String fileRootPath, String erp)
			throws Exception {
		File dir = new File(fileRootPath);
		if (!dir.isDirectory()) {
			return null;
		}
		String[] extensions = { erp };
		Collection<File> files = FileUtils.listFiles(dir, extensions, true);
		return files;
	}

	/**
	 * 取得fileRootPath資料夾下,符合所定義"檔案開頭名稱"的文字檔
	 * 
	 * @param fileRootPath
	 *            String :資料夾路徑
	 * @param formName
	 *            String :檔案開頭名稱
	 * @return files Collection<File>
	 */
	private Collection<File> getSameNameList(String fileRootPath,
			String formName) throws Exception {

		Collection<File> files = FileUtils.listFiles(new File(fileRootPath),
				FileFilterUtils.prefixFileFilter(formName, IOCase.INSENSITIVE),
				FileFilterUtils.suffixFileFilter(TextDefine.ATTACH_DXL,
						IOCase.INSENSITIVE));
		return files;
	}

	/**
	 * 取得指定檔案的行數
	 * 
	 * @param filepath
	 * @return
	 */
	private int getFileLen(String filepath) {
		int count = 0;
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(
					new FileInputStream(filepath)));
			while (br.readLine() != null) {
				count++;
			}
		} catch (Exception e) {
			String errmsg = new StringBuffer().append(
					"執行CompareExpPsr 之getFileLen 取得指定檔案行數時產生錯誤").toString();
			logger.error(errmsg, e);
			throw new DCException(e);
		}
		return count;
	}

}
