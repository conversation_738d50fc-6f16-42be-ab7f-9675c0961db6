
package com.mega.eloan.lms.base.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CentralBankControlService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01MDao;
import com.mega.eloan.lms.dao.L140MM1ADao;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140MM1A;

/**
 * <pre>
 * BY 專案共用Service
 * </pre>
 * 
 * @since 2022/11/18
 * <AUTHOR>
 * @version
 * 
 */
@Service("centralBankControlService")
public class CentralBankControlServiceImpl extends AbstractCapService implements CentralBankControlService {

	@Resource
	L140M01MDao l140m01mDao;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	EloandbcmsBASEService eloandbcmsBaseService;
	
	@Resource
	L140M01ADao l140m01aDao;
	
	@Resource
	L140MM1ADao l140mm1aDao;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Override
	//J-111-0479 引進不動產擔保品至央行房貸註記時價欄位
	public void includeCMStSumAmtAdjToL140m01mTimeVal(String l140m01a_mainId){
		
		BigDecimal totalTimeVal = this.getCMStSumAmtAdj(l140m01a_mainId);
		
		L140M01M l140m01m = this.l140m01mDao.findByMainId(l140m01a_mainId);
		
		if(l140m01m != null && totalTimeVal.compareTo(BigDecimal.ZERO) > 0 &&
				(UtilConstants.L140M01MCbcCase.自然人.equals(l140m01m.getCbcCase()) 
				 || UtilConstants.L140M01MCbcCase.公司法人.equals(l140m01m.getCbcCase()))){
			
			l140m01m.setTimeVal(totalTimeVal);
			l140m01m.setChkYN(UtilConstants.DEFAULT.否);
			this.l140m01mDao.save(l140m01m);
		}
	}
	
	@Override
	//J-111-0479 取得擔保品系統, 不動產本案總計時價
	public BigDecimal getCMStSumAmtAdj(String l140m01a_mainId){

		BigDecimal totalTimeVal = BigDecimal.ZERO;
		List<L140M01O> l140m01os = this.lmsService.findL140m01oByMainId(l140m01a_mainId);
		for(L140M01O l140m01o : l140m01os){
			Map<String, Object> m = this.eloandbcmsBaseService.getTSumAmtAdjInRealEstateTypeByC100m01Oid(l140m01o.getCmsOid());
			totalTimeVal = m == null ? totalTimeVal : totalTimeVal.add(LMSUtil.nullToZeroBigDecimal(m.get("TSUMAMTADJ")));
		}
		
		return totalTimeVal;
	}
	
	@Override
	public void checkIsLandOrConstructionFinancingCase(String landBuildYN, String prodClass, Properties prop, List<String> errList){
		
		if("Y".equals(landBuildYN) && ("33".equals(prodClass) || "34".equals(prodClass))){
			//此為土建融案件，不得修改！
			errList.add(prop.getProperty("L140M01M.msg.error.landOrConstructionFinancingCaseDontModify"));
		}
	}
	
	// J-112-0415 開放維護實際動工日，若擔保品系統該額度序號下所有土地擔保品已無空地時，由實際動工日填入解除控管日
	@Override
	public boolean isRemarkRrmovedt(Date actStartDate, String cntrNo){
		
		if(actStartDate != null){
			
			List<Map<String, Object>> list = this.eloandbcmsBaseService.getSpaceDataOfCollateralByCntrNo(cntrNo);
			
			if(list.isEmpty()){
				return false;
			}
			
			for(Map<String, Object> map : list){
				
				// 03-空地
				if("03".equals(map.get("LANDKIND"))){
					return false;
				}
			}
			
			return true;
		}
		
		return false;
	}
	
	@Override
	public boolean checkContractNoIsExisted(String cntrNo) {
		
		List<L140M01A> cntrNoList = this.l140m01aDao.findL140m01aByCntrNo(cntrNo);
		if(!cntrNoList.isEmpty()){
			return true;
		}
		
		List<L140MM1A> list = this.l140mm1aDao.findByCntrNo(cntrNo);
		if(!list.isEmpty()){
			return true;
		}
		
		Map<String, Object> map = this.misdbBASEService.findElf500_Elf383_Lnf087ByCntrNo(cntrNo);
		int count = (Integer)map.get("CNT");
		if(count > 0){
			return true;
		}
		
		return false;
	}
	
	@Override
	public void checkAllVersionL140m01mData(List<String> errList, Properties prop, String cbcCase, String plusReason, BigDecimal appAmt){
		
		if("4".equals(cbcCase) 
				&& "8".equals(plusReason) 
				&& (appAmt == null || appAmt.compareTo(BigDecimal.ZERO) <= 0)){
			
			//以房養老專案「時價」需大於0"
			errList.add(prop.getProperty("L140M01M.msg.error.currentAmountMustMoreThanZero"));
		}
	}
	
}
