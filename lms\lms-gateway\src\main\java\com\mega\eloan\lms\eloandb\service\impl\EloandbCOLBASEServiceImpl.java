package com.mega.eloan.lms.eloandb.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.eloandb.service.EloandbCOLBASEService;

@Service("eloandbCOLBASEService")
public class EloandbCOLBASEServiceImpl extends AbstractEloandbJdbc implements
		EloandbCOLBASEService {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.eloandb.service.EloandbCOLBASEService#getS104M01AList
	 * (java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> getS104M01AList(String mainId) {
		return this.getJdbc().queryForList("get.COL.CASE_INFO.List",
				new Object[] { mainId });
	}

}
