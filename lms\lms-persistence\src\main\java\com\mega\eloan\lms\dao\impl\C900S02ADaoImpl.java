/* 
 * C900S02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C900S02ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900S02A;

/** 房貸利率永慶信義100億統計 **/
@Repository
public class C900S02ADaoImpl extends LMSJpaDao<C900S02A, String>
	implements C900S02ADao {

	@Override
	public C900S02A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public Date findMaxTxnDate(String companyId5){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "companyId5", companyId5);
		search.addOrderBy("txnDate", true);
		C900S02A model =  findUniqueOrNone(search);
		if(model==null){
			return null;
		}else{
			return model.getTxnDate();
		}
	}

	@Override
	public C900S02A findLNF090_UK(String loanNo, Date txnDate, Date solarDate, String txnTime){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "txnDate", txnDate);
		search.addSearchModeParameters(SearchMode.EQUALS, "txnTime", txnTime);
		search.addSearchModeParameters(SearchMode.EQUALS, "solarDate", solarDate);
		return findUniqueOrNone(search);
	}
}