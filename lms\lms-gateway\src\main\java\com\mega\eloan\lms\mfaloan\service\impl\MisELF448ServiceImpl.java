/* 
 * MisLNF030ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;


import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF448Service;



/**
 * <pre>
 * 上傳ELF448
 * </pre>
 * 
 * @since 2013/01/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Service
public class MisELF448ServiceImpl extends AbstractMFAloanJdbc implements
MisELF448Service {

	@Override
	public List<Map<String, Object>> findBycntrNodupNocustId(String custId,String dupNo,String cnrtNo) {
		
		return this.getJdbc().queryForList("ELF501.findBycntrNodupNocustId", new String[]{cnrtNo,custId,dupNo});
	}
	@Override
	public void updateHOUSE(String custId,String dupNo,String cnrtNo,String selfChk,String rskFlag,String aLoanAC) {
		
		this.getJdbc().update("ELF501.updateHOUSE", new String[]{selfChk,rskFlag,aLoanAC,cnrtNo,custId,dupNo});
	}
	@Override
	public void insertHOUSE(String custId,String dupNo,String cnrtNo,String selfChk,String rskFlag,String aLoanAC) {
		
		this.getJdbc().update("ELF501.insertHOUSE", new String[]{custId,dupNo,cnrtNo,selfChk,rskFlag,aLoanAC});
	}
}
