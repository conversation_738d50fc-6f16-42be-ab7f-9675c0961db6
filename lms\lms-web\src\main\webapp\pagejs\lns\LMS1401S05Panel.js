var initLandAndBuild = {
    fhandle: "lms1201formhandler",
    ghandle: "lms1201gridhandler",
    fhandle140: "lms1401m01formhandler",
    ghandle140: "lms1401gridhandler",
    defButton: {
        "close": function() {
            $.thickbox.close();
        }
    }
};

var initDfd;
pageJsInit(function() {
  $(function() {
initDfd = initDfd || $.Deferred();
initDfd.done(function(auth) {

    $("#lms140s05").click(function() {
        grid.trigger("reloadGrid");
        landBuildGrid.trigger("reloadGrid");
    });

    $("#importLandBuildCredit").click(function() {
        var gridCount = grid.getGridParam("reccount");
        $.ajax({
            handler: "lms1201formhandler",
            action: "addBuildAndLandCredit",
            data: {

            }
		}).done(function(d) {
			API.showMessage(i18n.def.runSuccess);
			grid.trigger("reloadGrid");
			landBuildGrid.trigger("reloadGrid");
			if (d.message) {
			    API.showMessage(d.message);
			}
		}).fail(function() {
			grid.trigger("reloadGrid");
        });
    });

    var openDoc = function(type, docOid, data) {
        if ($('#CLS1131S01ThickBox').length == 0) {
            $('#ClsCustInfo').load(webroot + '/app/cls/cls1131s01', function() {
				setTimeout(function() {
	                CLS1131S01.handler = "cls1141formhandler"; //set handler
	                CLS1131S01.readOnly = true;
	                data.isC120M01A = true;
	                CLS1131S01.open(data);
				}, 500);
            });
        } else {
            data.isC120M01A = true;
            CLS1131S01.open(data);
        }
    }

    var openServCredit = function(type, docOid, data) {

        var cntrNoStr = "";
        $(data).each(function(i, row) {
            cntrNoStr = cntrNoStr + row.cntrNo
            var comma = ((i + 1) < data.length ? "," : "");
            cntrNoStr = cntrNoStr + comma;
        })
        // 顯示那些額度序號被選擇
        $("#cntrNoForLB").text(cntrNoStr);
        var buttons = {};
        //        var custId = datas[0].custId;
        buttons["include"] = function() {
            var type = $("input[name='pCreditType']:checked").val();
            var selrow = setCreditGrid.getGridParam('selrow');
            if(!type){
                API.showErrorMessage("請勾選評等種類");
                return;
            }
            //alert(selrow);
            if (type != "C0") {
                if (!selrow) {
                    API.showErrorMessage(i18n.def["grid.selrow"]);
                    return false;
                }
            }

            var dataPick = setCreditGrid.getRowData(selrow);
            var mainids = [];
            //            for (var i in datas) {
            //                mainids.push(datas[i].mainId)
            //            }

            mainids.push(data.mainId)
            $.ajax({
                handler: "lms1201formhandler",
                action: "addL140s03a",
                data: {
                    mainids: mainids,
                    pCreditOid: dataPick.oid,
                    pCreditType: $("input[name='pCreditType']:checked").val()

                }
			}).done(function(d) {
				landBuildGrid.trigger("reloadGrid");
				$.thickbox.close();
            })
        };

        buttons["cancel"] = function() {
            $.thickbox.close();
        };

        $("#setCreditGrid").jqGrid("setGridParam", { // 重新設定grid需要查到的資料
            postData: {
                // custId: custId,
                tabFormMainId: data.mainId
            },
            loadComplete: function() {
                if ($("#setCreditView").children().length > 0) {
                    $("#setCreditView").thickbox({
                        modal: false,
                        width: 900,
                        height: 350,
                        buttons: buttons
                    });
                }

            }
        }).trigger("reloadGrid");
    }

    // ===================Grid Code=============================
    /** 主表grid */

    var grid = $("#gridLandBuild").iGrid({
        handler: initLandAndBuild.ghandle,
        height: 170,
        needPager: false,
        rownumbers: false,
        sortorder: 'asc',
        shrinkToFit: true,
        postData: {
            formAction: "queryC120m01a",
            mainId: responseJSON.mainId
        },
        autowidth: true,
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'mainId',
            hidden: true //是否隱藏
        }, {
            name: 'custId', //身分證統編
            hidden: true //是否隱藏
        }, {
            name: 'dupNo', //身分證統編重複碼
            hidden: true //是否隱藏
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.001'], //"身分證統編",
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.002'], //"姓名",
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'custName' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.003'], //"評等類型",
            hidden: true,
            name: 'markModel' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.004'], //"房貸最終評等",
            align: "center",
            width: 60, //設定寬度
            sortable: false, //是否允許排序
            name: 'c120s01g.grade3' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.005'], //"非房貸最終評等",
            align: "center",
            width: 60, //設定寬度
            sortable: false, //是否允許排序
            name: 'c120s01q.grade3' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.006'], //備註
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'rmk' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.007'], //"房貸模型版本",
            align: "left",
            width: 20,
            sortable: false, //是否允許排序
            name: 'c120s01g.varVer' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.008'], //"非房貸模型版本",
            align: "left",
            width: 20,
            sortable: false, //是否允許排序
            name: 'c120s01q.varVer' //col.id
        }],
        ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });


    var landBuildGrid = $("#gridLandBuild2").iGrid({
        needPager: false,
        handler: initLandAndBuild.ghandle140,
        height: 150,
        sortname: 'printSeq|custId|cntrNo',
        sortorder: 'asc|asc|asc',
        //multiselect: true,
        postData: {
            formAction: "queryL140m01aForLandBuild",
            itemType: "1"
        },
        colModel: [{
                colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.009'], //"個金評等",
                width: 140,
                name: 'creditDesc',
                sortable: false
            },
            {
                colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.010'], //'土建融案件',
                name: 'isLandAndBuild',
                width: 50,
                sortable: false
            },
            {
                colHeader: i18n.lms1401s02["L140M01a.custName"], //借款人名稱
                width: 140,
                name: 'custName',
                sortable: false,
                onclick: openServCredit
            }, {
                colHeader: i18n.lms1401s02["L140M01a.cntrNo"], //"額度序號",
                name: 'cntrNo',
                width: 80,
                sortable: false
            }, {
                colHeader: i18n.lms1401s02["L140M01a.type"], //"性質"
                name: 'proPerty',
                width: 70,
                sortable: false,
                align: "center",
                formatter: proPertyFormatter
            }, {
                colHeader: i18n.lms1401s02["L140M01a.branchId"], //"分行別",
                name: 'ownBrId',
                width: 80,
                sortable: false,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'printSeq',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                name: 'custId',
                hidden: true
            }
        ],
        ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridLandBuild2").getRowData(rowid);
            openServCredit(null, null, data);
        }
    });


    var setCreditGrid = $("#setCreditGrid").iGrid({
        handler: initLandAndBuild.ghandle140,
        height: 170,
        needPager: false,
        rownumbers: false,
        hideMultiselect: false,
        sortorder: 'asc',
        shrinkToFit: true,
        localFirst: true,
        refresh: false,
        postData: {
            formAction: "queryC120m01aFor140",
            mainId: responseJSON.mainId
            //tabFormMainId:
        },
        autowidth: true,
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'mainId',
            hidden: true //是否隱藏
        }, {
            name: 'custId', //身分證統編
            hidden: true //是否隱藏
        }, {
            name: 'dupNo', //身分證統編重複碼
            hidden: true //是否隱藏
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.001'], //"身分證統編",
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            formatter: 'click',
            name: 'custId' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.002'], //"姓名",
            align: "left",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'custName' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.003'], //"評等類型"
            hidden: true,
            name: 'markModel' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.004'], //"房貸最終評等"
            align: "center",
            width: 60, //設定寬度
            sortable: false, //是否允許排序
            name: 'c120s01g.grade3' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.005'], //"非房貸最終評等"
            align: "center",
            width: 60, //設定寬度
            sortable: false, //是否允許排序
            name: 'c120s01q.grade3' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.006'], //"備註"
            align: "center",
            width: 100, //設定寬度
            sortable: true, //是否允許排序
            name: 'rmk' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.007'], //"房貸模型版本"
            align: "left",
            width: 20,
            sortable: false, //是否允許排序
            name: 'c120s01g.varVer' //col.id
        }, {
            colHeader: i18n.lms1401s05['LMS1401S05Panel.grid.008'], //"非房貸模型版本"
            align: "left",
            width: 20,
            sortable: false, //是否允許排序
            name: 'c120s01q.varVer' //col.id
        }],
        ondblClickRow: function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = setCreditGrid.getRowData(rowid);
            //openDoc(null, null, data);
            //openServCredit(null, null, data);

        }
    });
    $("#setPeopleCredit").click(function() {

        var ids = landBuildGrid.getGridParam('selrow');
        if (!ids) {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }

        //        var chekTheSameId = true;
        //
        //        var baseId = ""
        //        // 全部的資料
        //        var datas = [];
        //        var rowData;
        //        for (var i in ids) {
        //            rowData = landBuildGrid.getRowData(ids[i]);
        //            datas.push(rowData);
        //
        //            if (i == 0) {
        //                baseId = rowData.custId;
        //            } else {
        //                if (rowData.custId != baseId) {
        //                    chekTheSameId = false;
        //                }
        //            }
        //        }
        //        if (!chekTheSameId) {
        //            CommonAPI.showErrorMessage("請選擇同樣id一起設定消金信評");
        //            return;
        //        }
        if (ids) {
            var rowData = landBuildGrid.getRowData(ids);

            openServCredit(null, null, rowData);
        } else {
            CommonAPI.showErrorMessage(i18n.def["grid.selrow"]);
        }

    })
});
	})
});