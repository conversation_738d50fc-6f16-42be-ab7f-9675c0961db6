/* 
 * L120S16BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S06B;
import com.mega.eloan.lms.model.L120S16B;

/** 主要申請敘作內容明細檔 **/
public interface L120S16BDao extends IGenericDao<L120S16B> {

	L120S16B findByOid(String oid);

	List<L120S16B> findByMainId(String mainId);

	L120S16B findByUniqueKey(String mainId, String type, String custId,
			String dupNo, String cntrNo, String itemType);

	List<L120S16B> findByIndex01(String mainId, String custId, String dupNo,
			String cntrNo, String type, String itemType);

	List<L120S16B> findByIndex02(String custId, String dupNo);

	List<L120S16B> findByIndex03(String cntrNo);

	List<L120S16B> findByCntrNo(String CntrNo);

	List<L120S16B> findByCustIdDupId(String custId, String DupNo);

	int delModel(String mainId);

	List<L120S16B> findByMainIdCustIdCntrNo(String mainId, String custId,
			String dupNo, String cntrNo);
}