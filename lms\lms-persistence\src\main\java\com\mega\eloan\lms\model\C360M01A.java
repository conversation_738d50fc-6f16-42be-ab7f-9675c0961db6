package com.mega.eloan.lms.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 資料修正註記檔 */
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C360M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C360M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 修改項目 select * from com.bcodetype where codetype='C360M01A_itemType' */	
	@Column(name = "ITEMTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String itemType; 
	
	/** 額度序號 */	
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;
	
	/** 放款帳號 */	
	@Column(name = "LOANNO", length = 14, columnDefinition = "CHAR(14)")
	private String loanNo;
	
	/** 修改前資料值 */	
	@Column(name = "BEFITEM", length = 100, columnDefinition = "CHAR(100)")
	private String befItem;

	/** 修改後資料值 */	
	@Column(name = "AFTITEM", length = 100, columnDefinition = "CHAR(100)")
	private String aftItem;
	
	/** 調整原因 **/
	@Column(name="CHGREASON", length=900, columnDefinition="VARCHAR(900)")
	private String chgReason;
	
	/** 修改前資料 */	
	@Column(name = "BEFDSCR", length = 500, columnDefinition = "CHAR(500)")
	private String befDscr;

	/** 修改後資料 */	
	@Column(name = "AFTDSCR", length = 500, columnDefinition = "CHAR(500)")
	private String aftDscr;

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getCntrNo() {
		return cntrNo;
	}

	public void setCntrNo(String cntrNo) {
		this.cntrNo = cntrNo;
	}

	public String getLoanNo() {
		return loanNo;
	}

	public void setLoanNo(String loanNo) {
		this.loanNo = loanNo;
	}

	public String getBefItem() {
		return befItem;
	}

	public void setBefItem(String befItem) {
		this.befItem = befItem;
	}

	public String getAftItem() {
		return aftItem;
	}

	public void setAftItem(String aftItem) {
		this.aftItem = aftItem;
	}

	public String getChgReason() {
		return chgReason;
	}

	public void setChgReason(String chgReason) {
		this.chgReason = chgReason;
	}

	public String getBefDscr() {
		return befDscr;
	}

	public void setBefDscr(String befDscr) {
		this.befDscr = befDscr;
	}

	public String getAftDscr() {
		return aftDscr;
	}

	public void setAftDscr(String aftDscr) {
		this.aftDscr = aftDscr;
	}

	@Transient
	private String itemTypeDesc;

	public String getItemTypeDesc() {
		return itemTypeDesc;
	}

	public void setItemTypeDesc(String itemTypeDesc) {
		this.itemTypeDesc = itemTypeDesc;
	}
	
	
}
