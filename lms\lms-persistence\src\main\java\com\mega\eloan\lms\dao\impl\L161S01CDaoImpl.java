/* 
 * L161S01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L161S01CDao;
import com.mega.eloan.lms.model.L161S01C;

/** 額度動用資訊敘述說明檔 **/
@Repository
public class L161S01CDaoImpl extends LMSJpaDao<L161S01C, String>
	implements L161S01CDao {

	@Override
	public L161S01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L161S01C> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L161S01C findByUniqueKey(String mainId, String pid, String itemType){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (pid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L161S01C> findByIndex01(String mainId, String pid, String itemType){
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (pid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01C> findByIndex02(String mainId){
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01C> findByIndex03(String mainId, String pid){
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (pid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01C> findByIndex04(String mainId, String cntrNo){
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L161S01C>  findByMainIdPid(String mainId,String pid) {
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	} 
	
	@Override
	public List<L161S01C> findByMainIdCntrno(String mainId,String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L161S01C> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public L161S01C  findByMainIdPidItemType(String mainId,String pid,String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "pid", pid);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		return findUniqueOrNone(search);
	} 
	
	@Override
	public L161S01C findByMainIdCntrnoItemType(String mainId,String cntrNo,String itemType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		return findUniqueOrNone(search);
	}
	
	
}