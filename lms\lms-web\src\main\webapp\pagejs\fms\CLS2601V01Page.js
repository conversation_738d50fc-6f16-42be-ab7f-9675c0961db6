$(function(){	
	var my_colModel = [{
        name: 'oid', hidden: true
    }, {
    	name: 'mainId', hidden: true
    }, {
        colHeader: i18n.cls2601v01["C900M01H.custName"], //姓名
        align: "left", width: 110, sortable: true, name: 'custName',
        formatter: 'click', onclick: openDoc
    }, {
        colHeader: i18n.cls2601v01["C900M01H.agentCert"], //證書
        align: "left", width: 180, sortable: false, name: 'agent<PERSON><PERSON>'
    }, {
        colHeader: i18n.cls2601v01["C900M01H.ctlFlag"], //控管原因
        align: "left", width: 220, sortable: true, name: 'ctlFlag'
    }];
	
	if(viewstatus == "030|0C0"){
		//全行黑名單查詢 030|0C0 的資料
		my_colModel.push({
			colHeader : i18n.cls2601v01['C900M01H.ownBrId'],
			name : 'ownBrId',
			width : 80,
			sortable : true});
	}
	
	my_colModel.push({
        colHeader: i18n.cls2601v01["C900M01H.updater"], //異動人員
        align: "left", width: 80, sortable: true, name: 'updater'
    });
	my_colModel.push({
        colHeader: i18n.cls2601v01["C900M01H.updateTime"], //異動日期
        align: "left", width: 120, sortable: true, name: 'updateTime'
    });

	
	var grid = $("#gridview").iGrid({
        handler: "cls2601gridhandler",
        height: 350,
        rowNum: 15,
        sortname: 'updateTime',
        sortorder: 'desc',
        shrinkToFit: false,
        postData: {
            formAction: "queryMain",
            docStatus: viewstatus,
            ownBrId: userInfo.unitNo
        },
        colModel: my_colModel,
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus
    		}
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	
		$.form.submit({
			url : '../fms/cls2601m01/01',
			data : postData,
			target : '_blank'
		});
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
    	FilterAction.openBox();
    }).end().find("#btnAdd").click(function(){
		var rowObject = {};
		openDoc(null, null, rowObject);
    }).end().find("#btnDelete").click(function(){
    	var rows = grid.getGridParam('selrow');
        var list = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = grid.getRowData(rows);
            list = data.oid;
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "cls2601formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        list: list,
                        docStatus: viewstatus
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                    	grid.trigger("reloadGrid");
                    }
                });
            }
        });	
    });
});
