/* 
 * L140M01KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01K;

/** 借款收付彙計數明細檔 **/
public interface L140M01KDao extends IGenericDao<L140M01K> {

	L140M01K findByOid(String oid);

	List<L140M01K> findByOids(String[] oids);

	List<L140M01K> findByMainId(String mainId);

	List<L140M01K> findByIndex01(String mainId);
}