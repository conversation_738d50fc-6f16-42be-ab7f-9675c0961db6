package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 企金授信戶覆審檔  **/
public class ELF411 extends GenericBean {
	
	/** 資料日期 **/
	@Column(name="ELF411_DATAYM", length=6, columnDefinition="CHAR(6)")
	private String elf411_dataym;

	/** 分行別 **/
	@Column(name="ELF411_BRNO", length=3, columnDefinition="CHAR(3)")
	private String elf411_brno;

	/** 客戶ID(PV-IDNO) **/
	@Column(name="ELF411_CUSTID", length=10, columnDefinition="CHAR(10)")
	private String elf411_custid;

	/** 重複序號 **/
	@Column(name="ELF411_DUPNO", length=1, columnDefinition="CHAR(1)")
	private String elf411_dupno;

	/** 新作/增額 **/
	@Column(name="ELF411_NEWADD", length=1, columnDefinition="CHAR(1)")
	private String elf411_newadd;

	/** 主要授信戶Y **/
	@Column(name="ELF411_MAINCUST", length=1, columnDefinition="CHAR(1)")
	private String elf411_maincust;

	/** 客戶名稱 **/
	@Column(name="ELF411_CUSTNAME", length=42, columnDefinition="CHAR(42)")
	private String elf411_custname;

	/** 額度序號 **/
	@Column(name="ELF411_CONTRACT", length=12, columnDefinition="CHAR(12)")
	private String elf411_contract;

	/** 授信期間起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_DURBEG", columnDefinition="DATE")
	private Date elf411_durbeg;

	/** 授信期間止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_DUREND", columnDefinition="DATE")
	private Date elf411_durend;

	/** 動用起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_BEG", columnDefinition="DATE")
	private Date elf411_beg;

	/** 動用止日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_END", columnDefinition="DATE")
	private Date elf411_end;

	/** 等值台幣額度金額 **/
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "ELF411_QUOTA", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal elf411_quota;

	/** 新的信用評等
	 * 值：1, 2, 3, ..... 13, DF 
	 */
	@Column(name="ELF411_CDRISKN", length=2, columnDefinition="CHAR(2)")
	private String elf411_cdriskn;

	/** 舊的信用評等 
	 * 值： A、B、C、D、E   
	 */
	@Column(name="ELF411_CDRISKO", length=2, columnDefinition="CHAR(2)")
	private String elf411_cdrisko;

	/** 覆審期限 **/
	@Column(name="ELF411_CKDLINE", length=1, columnDefinition="CHAR(1)")
	private String elf411_ckdline;

	/** 上次覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_LRDATE", columnDefinition="DATE")
	private Date elf411_lrdate;

	/** 下次覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF411_CRDATE", columnDefinition="DATE")
	private Date elf411_crdate;

	/** 負責A/O代號 **/
	@Column(name="ELF411_AOEMPNO", length=6, columnDefinition="CHAR(6)")
	private String elf411_aoempno;

	/** 資料修改人 **/
	@Column(name="ELF411_UPDATER", length=8, columnDefinition="CHAR(8)")
	private String elf411_updater;

	/** 資料修改時間 **/
	@Column(name = "ELF411_TMESTAMP", columnDefinition = "TIMESTAMP")
	private String elf411_tmestamp;

	public String getElf411_dataym() {
		return elf411_dataym;
	}

	public void setElf411_dataym(String elf411_dataym) {
		this.elf411_dataym = elf411_dataym;
	}

	public String getElf411_brno() {
		return elf411_brno;
	}

	public void setElf411_brno(String elf411_brno) {
		this.elf411_brno = elf411_brno;
	}

	public String getElf411_custid() {
		return elf411_custid;
	}

	public void setElf411_custid(String elf411_custid) {
		this.elf411_custid = elf411_custid;
	}

	public String getElf411_dupno() {
		return elf411_dupno;
	}

	public void setElf411_dupno(String elf411_dupno) {
		this.elf411_dupno = elf411_dupno;
	}

	public String getElf411_newadd() {
		return elf411_newadd;
	}

	public void setElf411_newadd(String elf411_newadd) {
		this.elf411_newadd = elf411_newadd;
	}

	public String getElf411_maincust() {
		return elf411_maincust;
	}

	public void setElf411_maincust(String elf411_maincust) {
		this.elf411_maincust = elf411_maincust;
	}

	public String getElf411_custname() {
		return elf411_custname;
	}

	public void setElf411_custname(String elf411_custname) {
		this.elf411_custname = elf411_custname;
	}

	public String getElf411_contract() {
		return elf411_contract;
	}

	public void setElf411_contract(String elf411_contract) {
		this.elf411_contract = elf411_contract;
	}

	public Date getElf411_durbeg() {
		return elf411_durbeg;
	}

	public void setElf411_durbeg(Date elf411_durbeg) {
		this.elf411_durbeg = elf411_durbeg;
	}

	public Date getElf411_durend() {
		return elf411_durend;
	}

	public void setElf411_durend(Date elf411_durend) {
		this.elf411_durend = elf411_durend;
	}

	public Date getElf411_beg() {
		return elf411_beg;
	}

	public void setElf411_beg(Date elf411_beg) {
		this.elf411_beg = elf411_beg;
	}

	public Date getElf411_end() {
		return elf411_end;
	}

	public void setElf411_end(Date elf411_end) {
		this.elf411_end = elf411_end;
	}

	public BigDecimal getElf411_quota() {
		return elf411_quota;
	}

	public void setElf411_quota(BigDecimal elf411_quota) {
		this.elf411_quota = elf411_quota;
	}

	public String getElf411_cdriskn() {
		return elf411_cdriskn;
	}

	public void setElf411_cdriskn(String elf411_cdriskn) {
		this.elf411_cdriskn = elf411_cdriskn;
	}

	public String getElf411_cdrisko() {
		return elf411_cdrisko;
	}

	public void setElf411_cdrisko(String elf411_cdrisko) {
		this.elf411_cdrisko = elf411_cdrisko;
	}

	public String getElf411_ckdline() {
		return elf411_ckdline;
	}

	public void setElf411_ckdline(String elf411_ckdline) {
		this.elf411_ckdline = elf411_ckdline;
	}

	public Date getElf411_lrdate() {
		return elf411_lrdate;
	}

	public void setElf411_lrdate(Date elf411_lrdate) {
		this.elf411_lrdate = elf411_lrdate;
	}

	public Date getElf411_crdate() {
		return elf411_crdate;
	}

	public void setElf411_crdate(Date elf411_crdate) {
		this.elf411_crdate = elf411_crdate;
	}

	public String getElf411_aoempno() {
		return elf411_aoempno;
	}

	public void setElf411_aoempno(String elf411_aoempno) {
		this.elf411_aoempno = elf411_aoempno;
	}

	public String getElf411_updater() {
		return elf411_updater;
	}

	public void setElf411_updater(String elf411_updater) {
		this.elf411_updater = elf411_updater;
	}

	public String getElf411_tmestamp() {
		return elf411_tmestamp;
	}

	public void setElf411_tmestamp(String elf411_tmestamp) {
		this.elf411_tmestamp = elf411_tmestamp;
	}
}
