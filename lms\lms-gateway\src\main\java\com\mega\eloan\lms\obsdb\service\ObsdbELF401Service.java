
package com.mega.eloan.lms.obsdb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 主從債務人檔  ELF401
 * </pre>
 * 
 */
public interface ObsdbELF401Service {
	/**
	 * 動審表 -主從債務人檔 ELLNGTEE(ELF401) 刪除該 分行號碼 、客戶編號、重覆序號、額度序號已存在的
	 * @param BRNID
	 * @param brNo
	 *            分行別
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 */
	public void delEllngteeByUniqueKey(String BRNID, String brNo, String custId,
			String dupNo, String cntrNo);

	/**
	 * 動審表 -主從債務人檔 ELLNGTEE(ELF401) 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *  brNo  分行別
	 *  custid  主債務人統編
	 *  dupNo 主債務人統編重複序號
	 *  cntrNo  額度序號
	 *  lngeFlag 相關身分
	 *  lngeId 從債務人統編
	 *  dupNo1 從債務人統編重複序號
	 *  lngenm 從債務人姓名
	 *  ntCode  國家別
	 *  lngere 與主債務人關係
	 *  upddt 更新日
	 *  updater 資料修改人
	 *  releasedt 董監事任期止日
	 *  grtamt 擔保限額
	 *  localid 當地客戶識別ID
	 * </pre>
	 */
	public void insert(String BRNID, List<Object[]> dataList);
}
