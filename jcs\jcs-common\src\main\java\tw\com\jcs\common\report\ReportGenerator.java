package tw.com.jcs.common.report;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Vector;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.inet.report.Area;
import com.inet.report.Element;
import com.inet.report.Engine;
import com.inet.report.FieldElement;
import com.inet.report.ReportException;
import com.inet.report.Section;
import com.inet.report.TextInterpretationProperties;

import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * 處理RPT檔案變成PDF
 * 
 * <pre>
 * 處理RPT檔案變成PDF
 * </pre>
 * 
 * @since 日期 2011/09/28
 * <AUTHOR> ice
 * @version 版本
 *          <ul>
 *          <li>2011/09/28,ice,new</li>
 *          </ul>
 *          1
 */
// public class ReportTools extends AbstractCapService {
public class ReportGenerator {
    // logger
    private final static Logger LOGGER = LoggerFactory.getLogger(ReportGenerator.class);
    final static String BASEURL = PropUtil.getProperty("baseUrl");
    // 選擇報表型式xls,pdf
    private String exportStyle = null;

    // 存放rpt的檔案名稱
    private String rptFile = null;
    // pdf的檔案路徑 D:\aaa\
    private String pdfPath = null;
    // pdf的檔案位置
    private String pdfName = null;
    // pdf語系選擇
    private Locale lang = null;
    // report參數設定
    // http://www.inetsoftware.de/documentation/clear-reports/online-help/features/report-url-parameters
    private Properties properties = null;

    // 從form傳過來的 其格式為 aaa=111;bbb=222;ccc=333; 用;作切割即可
    // private String param = null;

    /*
     * 從JAVA程式傳過來的,放到報表上的變數 (欄位的值或是想要放置報表上的變數都可以使用) 變數名稱,變數值
     */
    private Map<String, String> rptVariableMap = null;

    private SubReportParam subReportParam = null;

    private boolean testMethod = false;

    /**
     * 控制行距效果
     */
    private double relativeLineSpace = 1;

    /*
     * String[] titles = { "SAP110M01DataBean1.docId","SAP110M01DataBean1.docName" ,"SAP110M01DataBean1.field1".... }; Object[][] rows = { { String.valueOf(System.nanoTime()), "王一", "王一" },{
     * String.valueOf(System.nanoTime()), "王二", "王一" }, { String.valueOf(System.nanoTime()), "王三", "王一" },{ String.valueOf(System.nanoTime()), "王四", "王一" } };
     */
    private String[] titles = null;
    private Object[][] rows = null;

    /**
     * 設定輸出格式
     * 
     * @param exportStyle
     *            輸出格式 xls pdf
     * @throws Exception
     *             Exception
     */
    public ReportGenerator(final String exportStyle, String rptFileName) throws Exception {
        if (Engine.EXPORT_PDF.equals(exportStyle) || Engine.EXPORT_XLS.equals(exportStyle)) {
            this.exportStyle = exportStyle;
        } else {
            LOGGER.error("未給入正確的輸出報表格式!");
            throw new Exception("Exception");
        }

        this.rptFile = rptFileName;
    }

    /**
     * 設定 存放rpt的檔案名稱
     * 
     * @param rptFileName
     *            存放rpt的檔案名稱
     */
    public ReportGenerator(String rptFileName) {
        this.rptFile = rptFileName;
    }

    /**
     * 設定輸出格式(若不指定 則輸出為pdf)
     */
    public ReportGenerator() {
        this.exportStyle = Engine.EXPORT_PDF;
    }

    /**
     * 產生PDF報表
     * 
     * @param testMethod
     *            連線方式
     * @return PDFFn PDF路徑
     * @throws ReportException
     *             ReportException
     * @throws FileNotFoundException
     *             檔案不存在
     * @throws IOException
     *             檔案有問題
     * @throws Exception
     *             Exception
     */
    public String createReport() throws ReportException, FileNotFoundException, IOException, Exception {
        OutputStream out = null;
        Engine engine = null;
        try {
            if (checkVariable()) {

                engine = this.setEngineData();
                engine.execute();
                int pageCount = engine.getPageCount();
                out = new FileOutputStream(this.pdfPath + this.pdfName);
                for (int i = 1; i <= pageCount; i++) {
                    out.write(engine.getPageData(i));
                }
            } else {
                throw new Exception("參數未設定");
            }
        } finally {
            out.close();
        }
        return this.pdfName;
    }

    /**
     * 產生PDF報表
     * 
     * @param testMethod
     *            連線方式
     * @return PDFFn PDF路徑
     * @throws ReportException
     *             ReportException
     * @throws FileNotFoundException
     *             檔案不存在
     * @throws IOException
     *             檔案有問題
     * @throws Exception
     *             Exception
     */
    public OutputStream generateReport() throws ReportException, FileNotFoundException, IOException, Exception {
        return this.generateReport(true);
    }

    /**
     * 取得Exception
     * 
     * @param throwable
     * @return
     */
    public static String getErrorInfoFromException(Throwable throwable) {
        if (throwable == null) {
            return "null";
        }
        // try( ByteArrayOutputStream bos = new ByteArrayOutputStream(); PrintStream pw = new PrintStream(bos) ) {
        //
        // throwable.printStackTrace(pw);
        // return new String( bos.toByteArray() );
        // }
        try {
            return ExceptionUtils.getStackTrace(throwable);
        } catch (Exception e) {
            return "null"; // 不會發生
        } finally {
        }
    }

    /**
     * 產生PDF報表
     * 
     * @param setTop
     *            是否要置頂
     * @param testMethod
     *            連線方式
     * @return PDFFn PDF路徑
     * @throws ReportException
     *             ReportException
     * @throws FileNotFoundException
     *             檔案不存在
     * @throws IOException
     *             檔案有問題
     * @throws Exception
     *             Exception
     */
    public OutputStream generateExceptionReport(Locale locale) throws ReportException, FileNotFoundException, IOException, Exception {

        OutputStream byteOut = null;
        Engine engine = null;
        try {
            LOGGER.debug("i-net generateReport=====>start");
            byteOut = new ByteArrayOutputStream();
            this.rptFile = "report/EXCEPTION_" + locale.toString() + ".rpt";
            engine = this.setEngineData();

            // rpt執行
            LOGGER.debug("i-net engine.execute()=====>start");
            engine.execute();
            LOGGER.debug("i-net engine.execute()=====>final");
            int pageCount = engine.getPageCount();
            for (int i = 1; i <= pageCount; i++) {
                byteOut.write(engine.getPageData(i));
            }
            LOGGER.debug("i-net engine byteOut.write=====>final");

        } finally {
            if (byteOut != null) {
                byteOut.close();
            }
        }
        return byteOut;
    }

    /**
     * 產生PDF報表
     * 
     * @param setTop
     *            是否要置頂
     * @param testMethod
     *            連線方式
     * @return PDFFn PDF路徑
     * @throws ReportException
     *             ReportException
     * @throws FileNotFoundException
     *             檔案不存在
     * @throws IOException
     *             檔案有問題
     * @throws Exception
     *             Exception
     */
    public OutputStream generateReport(boolean setTop) throws ReportException, FileNotFoundException, IOException, Exception {

        OutputStream byteOut = null;
        Engine engine = null;
        try {
            LOGGER.debug("i-net generateReport=====>start");
            byteOut = new ByteArrayOutputStream();
            engine = this.setEngineData(setTop);
            // rpt執行
            LOGGER.debug("i-net engine.execute()=====>start");
            engine.execute();
            LOGGER.debug("i-net engine.execute()=====>final");
            int pageCount = engine.getPageCount();
            for (int i = 1; i <= pageCount; i++) {
                byteOut.write(engine.getPageData(i));
            }
            LOGGER.debug("i-net engine byteOut.write=====>final");
        } finally {
            if (byteOut != null) {
                byteOut.close();
            }
        }
        return byteOut;
    }

    /**
     * 取得頁數
     * 
     * @return
     * @throws ReportException
     */
    public int getReportCount() throws ReportException {

        Engine engine = null;
        int pageCount = 0;
        try {
            engine = this.setEngineData();
            engine.execute();
            pageCount = engine.getPageCount();
        } finally {

        }

        return pageCount;
    }

    /**
     * 設定engine資料
     * 
     * @return
     * @throws ReportException
     */
    private Engine setEngineData() throws ReportException {
        return this.setEngineData(true);
    }

    /**
     * 設定engine資料
     * 
     * @param setTop
     *            是否設置邊界
     * @return
     * @throws ReportException
     */
    private Engine setEngineData(boolean setTop) throws ReportException {

        Engine engine = null;
        URL urlRpt = null;

        LOGGER.debug("i-net Engine.set=====>start");
        Engine.setLogLevel(Engine.LOG_DEBUG);
        if (Engine.EXPORT_XLS.equals(this.exportStyle)) {
            engine = new Engine(Engine.EXPORT_XLS);
        } else {
            engine = new Engine(Engine.EXPORT_PDF);
        }
        if (this.testMethod) {
            engine.setReportFile(this.rptFile);
        } else {
            urlRpt = Thread.currentThread().getContextClassLoader().getResource(this.rptFile);
            engine.setReportFile(urlRpt);
        }
        LOGGER.debug("rptFile path = " + this.rptFile);
        if (setTop) {
            engine.getReportProperties().setMarginBottom(400);
            engine.getReportProperties().setMarginTop(400);
        }
        LOGGER.debug("Version = " + Engine.getVersion());
        // engine.setLocale(this.lang);
        // 塞入變數(由頁面提供的資料)
        // if (!Util.isEmpty(this.param)) {
        // final String[] value = Util.nullToSpace(this.param).split(";");
        // for (int i = 0; i < value.length; i++) {
        // final String[] map = value[i].split("=");
        // if (Util.isEmpty(map.length >= 1)) {
        // // LOGGER.debug("Map參數(由頁面提供的資料)==>" + map[0] + "=" +
        // // map[1]);
        // engine.setPrompt(map[0], map[1]);
        // }
        // }
        // }
        LOGGER.debug("i-net Engine.setrptVariableMap=====>start");
        // 塞入變數(報表變數使用的資料)
        if (this.rptVariableMap != null && this.rptVariableMap.size() > 0) {
            // for (String key : this.rptVariableMap.keySet()) {
            // if (this.rptVariableMap.get(key) == null) {
            // LOGGER.debug("參數為null : " + key);
            // this.rptVariableMap.put(key, "");
            // }
            // }
            for (Entry<String, String> entry : this.rptVariableMap.entrySet()) {
                // LOGGER.debug(entry.getKey() + " = " +
                // this.replaceTag(entry.getValue()));
                engine.setPrompt(entry.getKey(), this.replaceTag(entry.getValue()));
            }
        }

        SubReportParam srp = this.subReportParam;
        if (srp != null) {
            engine = srp.execute(engine);
        }

        // engine.setPrompt("L140M01B.ITEMTYPE10",
        // "※科子目限額：換入(出)換匯換利交易限額TWD 10,000元。※聯行攤貸比例：香港分行：1/4(0C3501250008)，大眾曼谷總行：1/4(Y01501250002)，曼谷春武里分行：1/4(Y02501250016)，挽那分行：1/4(Y03501250001)。");

        LOGGER.debug("i-net Engine.settitles=====>start");
        if (this.titles != null && this.titles.length > 0) {
            engine.setData(this.titles, this.rows);
        } else {
            LOGGER.debug("若是報表內有資料庫檔案多值欄位,而你一列資料都沒塞,會導致報表產生不出來一頁,錯誤產生!!!");
        }
        // 所有的區域(包含報表頁首,頁首,詳細資料,頁尾,報表頁尾
        LOGGER.debug("i-net Engine.setBASEURL=====>start");
        int counti = engine.getAreaCount();
        for (int i = 0; i < counti; i++) {
            // 該區域的所有細分區域,詳細資料a,詳細資料b.......
            Area dArea = engine.getArea(i);
            int countk = dArea.getSectionCount();
            for (int k = 0; k < countk; k++) {
                Section dSec = dArea.getSection(k);
                Vector<?> elemsV = dSec.getElementsV();
                int countj = elemsV.size();
                for (int j = 0; j < countj; j++) {
                    Element elem = (Element) elemsV.elementAt(j);
                    if (elem instanceof FieldElement) {
                        FieldElement fElem = (FieldElement) elem;

                        if (fElem.getTextInterpretation() == TextInterpretationProperties.ADVANCED_HTML_TEXT) {
                            fElem.setBaseUrl(BASEURL);
                        }
                    }
                }
                // LOGGER.debug("BASEURL = " + BASEURL);
            }
        }

        if (this.lang != null)
            engine.setLocale(this.lang);

        if (this.properties != null) {
            engine.setUserProperties(this.properties);
        }

        return engine;

    }

    /**
     * CKEDIT不做置換動作(為了TEXTAREA有一些有按ENTER 但是沒有換行效果)
     * 
     * @param str
     * @return
     */
    private String replaceTag(String str) {
        if (str == null) {
            return "";
        }
        if (str.indexOf("</") == -1) {
            return str;
        } else {
            return str.replace("\r\n", "").replace("\r", "").replace("\n", "").replace("<p>", "").replace("</p>", "<BR/>").replace("<P>", "").replace("</P>", "<BR/>");// .replace(" ",
                                                                                                                                                                       // "").replace("\t",
                                                                                                                                                                       // "");
        }
    }

    /**
     * 取得properties的value
     * 
     * @return boolean 是否傳入參數正常
     */
    private boolean checkVariable() {
        boolean result = true;
        if (Util.isEmpty(this.exportStyle)) {
            this.exportStyle = Engine.EXPORT_PDF;
        }
        if (Util.isEmpty(this.lang)) {
            result = false;
            LOGGER.error("未設定語系");
        }
        if (Util.isEmpty(this.rptFile)) {
            result = false;
            LOGGER.error("未設定RPT檔案");
        }
        if (Util.isEmpty(this.pdfPath)) {
            result = false;
            LOGGER.error("未設定pdf檔案路徑");
        }
        if (Util.isEmpty(this.pdfName)) {
            result = false;
            LOGGER.error("未設定pdf檔案位置");
        }
        return result;
    }

    /**
     * 設定檔案相關參數
     * 
     * @param rptFileName
     *            存放rpt的檔案名稱
     * @param pdfPath
     *            pdf的檔案路徑 D:\aaa\
     * @param pdfName
     *            pdf的檔案位置
     */
    public void setFileConfig(final String rptFile, final String pdfPath, final String pdfName) {
        this.rptFile = rptFile;
        this.pdfPath = pdfPath;
        this.pdfName = pdfName;

    }

    public void setReportFile(final String rptFile) {
        this.rptFile = rptFile;
    }

    /**
     * 設定多筆資料庫欄位資料
     * 
     * @param titleRows
     *            存放欄位資料 map = 欄位名稱1=欄位名稱1第一筆資料,欄位名稱1=欄位名稱1第一筆資料,欄位名稱1=欄位名稱1第一筆資料....
     */
    public void setRowsData(List<Map<String, String>> data) {
        int rowCount = 0;

        if (data == null || data.size() == 0) {
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            list.add(Util.setColumnMap());
            data = list;
        }

        if (data != null && data.size() > 0) {
            Map<String, String> titleMap = data.get(0);
            int columnCount = titleMap.size();
            this.titles = titleMap.keySet().toArray(new String[] {});
            this.rows = new String[data.size()][columnCount];

            int count = 0;
            for (Map<String, String> map : data) {
                count = 0;
                String[] dataRows = new String[columnCount];
                for (String key : this.titles) {
                    dataRows[count++] = this.replaceTag(map.get(key));
                }
                this.rows[rowCount++] = dataRows;
            }
        } else {
            LOGGER.error("Data is no records!");
        }
    }

    /**
     * 確認是否有變數未塞入rpt內 使用方法,建立一個文字檔並且每行放置一個變數名稱 當變數塞入完成後跑這隻即可得到是否有正常塞入變數
     * 
     * @param fileName
     *            變數文字檔
     * @param fmt
     *            編碼
     * @param rptVariableMap
     *            變數MAP
     * @return boolean 是否有正確塞入變數
     * @throws Exception
     */
    public boolean checkVariableExist(String fileName, Map<String, String> rptVariableMap) throws Exception {
        boolean result = true;
        try {
            FileInputStream fileInputStream = new FileInputStream(fileName);
            BufferedReader reader = new BufferedReader(new InputStreamReader(fileInputStream, "big5"));
            String line = "";
            while ((line = reader.readLine()) != null) {
                if (line.length() > 0) {
                    if (rptVariableMap.get(line.trim()) == null) {
                        LOGGER.error("缺少的變數:" + line);
                        result = false;
                    }
                }
            }
            fileInputStream.close();
            reader.close();
        } finally {

        }
        if (!result) {
            throw new Exception("缺少變數!");
        }
        return result;
    }

    public void setSubReport(int subReporIndex, Map<String, String> data) {

    }

    /**
     * 設定變數資料(若欄位為單筆資料指定為變數存入)
     * 
     * @param rptVariableMap
     *            變數存入(變數名稱,變數值)
     */
    public void setVariableData(final Map<String, String> rptVariableMap) {
        this.rptVariableMap = rptVariableMap;
    }

    /**
     * 存入語系 (java.util.Locale.TAIWAN,java.util.Locale.CHINA,java.util.Locale. ENGLISH)
     * 
     * @param lang
     *            語系
     */
    public void setLang(final Locale lang) {
        this.lang = java.util.Locale.TAIWAN;
    }

    /**
     * 設定是否為測試方法
     * 
     * @param testMethod
     *            是否為測試方法
     */
    public void setTestMethod(final boolean testMethod) {
        this.testMethod = testMethod;
    }

    /**
     * @param relativeLineSpace
     *            the relativeLineSpace to set
     */
    public void setRelativeLineSpace(double relativeLineSpace) {
        this.relativeLineSpace = relativeLineSpace;
    }

    /**
     * @return the relativeLineSpace
     */
    public double getRelativeLineSpace() {
        return relativeLineSpace;
    }

    /**
     * 設定report 參數
     * 
     * @param properties
     */
    public void setProperties(Properties properties) {
        this.properties = properties;
    }

    public void setSubReportParam(SubReportParam subReportParam) {
        this.subReportParam = subReportParam;
    }

}
