package com.mega.eloan.lms.dc.action;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FilenameFilter;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * DXLReject依踢退清單(viewName_dxlError.lst),將踢退的dxl檔案移到踢退目錄(REJECT)
 * </pre>
 * 
 * @since 2013/01/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/30,Bang,new
 *          <li>2013/03/07,UFO,取消local_var: dataBean，父類別取得
 *          </ul>
 */
public class DXLReject extends BaseAction {

	private String schema = "";
	private String dxlDirRootPath = "";
	private String logsDirPath = "";
	private PrintWriter logsReject = null;// 輸出log
	private long tt1 = 0;
	private String OldBranch = "";

	/**
	 * 初始化必要資訊及執行Reject動作
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 * @param viewListName
	 *            String
	 */
	public void doReject(String schema) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認";
			this.logger.info(errmsg);
			throw new DCException(errmsg);
		}
		this.schema = schema;
		logger.info("正在初始化 DXLReject 必要資訊...");

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(this.schema)) {
			this.dxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
		} else {
			this.dxlDirRootPath = this.configData.getClsDxlDirRootPath();// homePath\today\CLS
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
		}

		// 取得所有分行
		List<String> brnoList = BrnoConfig.getInstance().getBrnoList();
		for (int i = 0; i < brnoList.size(); i++) {
			this.runReject(brnoList.get(i));

			if (this.logsReject != null) {
				String msg = "【"
						+ this.OldBranch
						+ "】分行 執行 DXLReject 結束...結束時間 :"
						+ Util.getNowTime()
						+ ", TOTAL TIME===>"
						+ Util.millis2minute(System.currentTimeMillis()
								- this.tt1);
				this.logger.info(msg);
				this.logsReject.println(msg);
				IOUtils.closeQuietly(this.logsReject);
			}
		}
	}

	/**
	 * 錯誤檔案搬移至REJECT內
	 * 
	 * @param nsfView
	 *            String :viewList中的資料
	 */
	public void runReject(String strBrn) {
		this.tt1 = System.currentTimeMillis();

		try {
			// 取得錯誤清單的路徑: dxlHome\分行名
			String dxlBrnHomePath = this.dxlDirRootPath + File.separator
					+ strBrn;
			File dxlBrnHome = new File(dxlBrnHomePath);
			if (!dxlBrnHome.exists()) {
				return;
			}
			// 建立各份行logs
			final String LOG_ROOT = this.logsDirPath + File.separator
					+ "REJECT";
			Util.checkDirExist(LOG_ROOT);

			String brnLogPath = LOG_ROOT + File.separator + "reject_" + strBrn
					+ ".log";
			this.logsReject = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							brnLogPath)))), true);
			this.OldBranch = strBrn;
			this.logsReject.println("【" + strBrn + "】分行runReject 起始時間 :"
					+ Util.getNowTime());

			// 取得錯誤清單的檔案名稱
			Collection<File> files = FileUtils.listFiles(dxlBrnHome,
					FileFilterUtils
							.suffixFileFilter(".lst", IOCase.INSENSITIVE),
					FileFilterUtils.trueFileFilter());
			String wkFile = "";
			for (File file : files) {
				String errFilePath = file.getPath();
				String viewName = FilenameUtils.getName(errFilePath).split("_")[0];
				BufferedReader br = new BufferedReader(new FileReader(
						errFilePath));
				String rcd = DXLUtil.proc_readline( br.readLine() );
				while (rcd != null) {
					String[] fnParm = rcd.split(TextDefine.SYMBOL_COLON);
					if (!wkFile.equals(fnParm[0])) {
						wkFile = fnParm[0];// FLMS110S01_C17EBD3F4B1AAF004825726C0021E6E7_40B5D4CC00D54C2B4825726C0021F2F7.dxl
						File myDir = new File(dxlBrnHome + File.separator
								+ viewName);
						// 檢查踢退目錄: dxlHome\viewName\DtlRcdError.lst
						String rjtDir = dxlBrnHome + File.separator + viewName
								+ this.configData.getRejectPath();
						Util.checkDirExist(rjtDir);
						// 取得符合檔名的檔案
//						FilenameFilter select = new FileListFilter(wkFile,
//								"dxl");
//						File[] fnList = myDir.listFiles(select);
						// 搬移
//						if (fnList != null) {
							for (File fn : FileUtils.listFiles(myDir, 
									FileFilterUtils.and(FileFilterUtils.prefixFileFilter(wkFile),
										FileFilterUtils.suffixFileFilter("dxl")), null)) {
								if (!moveFile(fn, rjtDir)) {
									this.logsReject
											.println(" !! DXLReject runReject : There was errors when move ["
													+ fn + "] to " + rjtDir);
								}
							}
//						}
					}
					rcd = DXLUtil.proc_readline(br.readLine());
				}
			}
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行DXLReject 之 runReject步驟 時產生錯誤！";
			this.logger.error(errmsg, e);
			this.logsReject.println(errmsg);
			e.printStackTrace(this.logsReject);
			throw new DCException(errmsg, e);
		}
	}

	/**
	 * 搬移檔案
	 * 
	 * @param srcFile
	 *            File: 來源檔案
	 * @param destPath
	 *            String: 目地的路徑
	 * @return
	 */
	private boolean moveFile(File srcFile, String destPath) {
		// Destination directory
		File dir = new File(destPath);

		// Move file to new directory
		boolean success = srcFile.renameTo(new File(dir, srcFile.getName()));
		return success;
	}

	@SuppressWarnings("unused")
	private boolean moveFile(String srcFile, String destPath) {
		// File (or directory) to be moved
		File file = new File(srcFile);

		// Destination directory
		File dir = new File(destPath);

		// Move file to new directory
		boolean success = file.renameTo(new File(dir, file.getName()));

		return success;
	}

}

// Inner Class FileListFilter: 過濾檔案
class FileListFilter implements FilenameFilter {
	private String name;

	private String extension;

	public FileListFilter(String name, String extension) {
		this.name = name;
		this.extension = extension;
	}

	public boolean accept(File directory, String filename) {
		boolean fileOK = true;

		if (name != null) {
			fileOK &= filename.startsWith(name);
		}

		if (extension != null) {
			fileOK &= filename.endsWith('.' + extension);
		}
		return fileOK;
	}
}
