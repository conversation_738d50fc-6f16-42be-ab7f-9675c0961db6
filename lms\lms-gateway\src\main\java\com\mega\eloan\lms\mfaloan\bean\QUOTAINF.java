/* 
 * QUOTAINF.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.GenericBean;


/** 額度資訊檔 **/
public class QUOTAINF extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 
	 * 借款人統一編號<p/>
	 * 主要借款人
	 */
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 分行代號 **/
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(03)",unique = true)
	private String branch;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 
	 * 申請日<p/>
	 * 民國年YYY/MM/DD<br/>
	 *  消金：申請書/簽報書的申請日<br/>
	 *  企金：簽報書的簽案日
	 */
	@Column(name="APPDATE", length=9, columnDefinition="CHAR(09)")
	private String appdate;

	/** 
	 * 核准日<p/>
	 * 民國年YYY/MM/DD<br/>
	 *  消金：審核書/簽報書核准日<br/>
	 *  企金：簽報書的核准日
	 */
	@Column(name="APRDATE", length=9, columnDefinition="CHAR(09)")
	private String aprdate;

	/** 主要借款人姓名 **/
	@Column(name="CUSTNAME", length=102, columnDefinition="CHAR(102)")
	private String custname;

	/** 
	 * 核准主管代號<p/>
	 * 審核書/簽報書核准主管的代號
	 */
	@Column(name="BOSSID", length=5, columnDefinition="CHAR(05)")
	private String bossid;

	/** 
	 * 核准主管姓名<p/>
	 * 審核書/簽報書核准主管的姓名
	 */
	@Column(name="BOSSNAME", length=102, columnDefinition="CHAR(102)")
	private String bossname;

	/** 
	 * 進帳方式－1.存款帳號<p/>
	 * 若進帳方式為存款帳號則此欄為存款帳號的內容<br/>
	 *  進帳方式1,2可複選
	 */
	@Column(name="ACCNO", length=14, columnDefinition="CHAR(14)")
	private String accno;

	/** 
	 * 進帳方式－1.進帳金額<p/>
	 * 元
	 */
	@Column(name="RCTAMT1", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rctamt1;

	/** 
	 * 進帳方式－2.匯款－解款銀行<p/>
	 * 若進帳方式為匯款則此欄為解款銀行的名稱
	 */
	@Column(name="RMTBH", length=7, columnDefinition="CHAR(07)")
	private String rmtbh;

	/** 
	 * 進帳方式－2.匯款－解款帳號<p/>
	 * 若進帳方式為匯款則此欄為解款帳號
	 */
	@Column(name="RMTNO", length=14, columnDefinition="CHAR(14)")
	private String rmtno;

	/** 
	 * 進帳方式－2.進帳金額<p/>
	 * 元
	 */
	@Column(name="RCTAMT2", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rctamt2;

	/** 
	 * 審核書/簽報書文件ID<p/>
	 * 可以此ID讀取Notes審核書/簽報書內容
	 */
	@Column(name="UNID", length=32, columnDefinition="CHAR(32)")
	private String unid;

	/** 
	 * 新貸<p/>
	 * 若為新貸案件則為Y
	 */
	@Column(name="NEWCASE", length=1, columnDefinition="CHAR(01)")
	private String newcase;

	/** 資料修改人（行員代號） **/
	@Column(name="UPDATER", length=8, columnDefinition="CHAR(08)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 進帳 1. 進帳幣別 */
	@Column(name="RCTSWFT1", length=3, columnDefinition="CHAR(3)")
	private String rctSwft1;
	
	/** 進帳2. 進帳幣別 */
	@Column(name="RCTSWFT2", length=3, columnDefinition="CHAR(3)")
	private String rctSwft2;
	
	/** 
	 * 取得借款人統一編號<p/>
	 * 主要借款人
	 */
	public String getCustid() {
		return this.custid;
	}
	/**
	 *  設定借款人統一編號<p/>
	 *  主要借款人
	 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得分行代號 **/
	public String getBranch() {
		return this.branch;
	}
	/** 設定分行代號 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得申請日<p/>
	 * 民國年YYY/MM/DD<br/>
	 *  消金：申請書/簽報書的申請日<br/>
	 *  企金：簽報書的簽案日
	 */
	public String getAppdate() {
		return this.appdate;
	}
	/**
	 *  設定申請日<p/>
	 *  民國年YYY/MM/DD<br/>
	 *  消金：申請書/簽報書的申請日<br/>
	 *  企金：簽報書的簽案日
	 **/
	public void setAppdate(String value) {
		this.appdate = value;
	}

	/** 
	 * 取得核准日<p/>
	 * 民國年YYY/MM/DD<br/>
	 *  消金：審核書/簽報書核准日<br/>
	 *  企金：簽報書的核准日
	 */
	public String getAprdate() {
		return this.aprdate;
	}
	/**
	 *  設定核准日<p/>
	 *  民國年YYY/MM/DD<br/>
	 *  消金：審核書/簽報書核准日<br/>
	 *  企金：簽報書的核准日
	 **/
	public void setAprdate(String value) {
		this.aprdate = value;
	}

	/** 取得主要借款人姓名 **/
	public String getCustname() {
		return this.custname;
	}
	/** 設定主要借款人姓名 **/
	public void setCustname(String value) {
		this.custname = value;
	}

	/** 
	 * 取得核准主管代號<p/>
	 * 審核書/簽報書核准主管的代號
	 */
	public String getBossid() {
		return this.bossid;
	}
	/**
	 *  設定核准主管代號<p/>
	 *  審核書/簽報書核准主管的代號
	 **/
	public void setBossid(String value) {
		this.bossid = value;
	}

	/** 
	 * 取得核准主管姓名<p/>
	 * 審核書/簽報書核准主管的姓名
	 */
	public String getBossname() {
		return this.bossname;
	}
	/**
	 *  設定核准主管姓名<p/>
	 *  審核書/簽報書核准主管的姓名
	 **/
	public void setBossname(String value) {
		this.bossname = value;
	}

	/** 
	 * 取得進帳方式－1.存款帳號<p/>
	 * 若進帳方式為存款帳號則此欄為存款帳號的內容<br/>
	 *  進帳方式1,2可複選
	 */
	public String getAccno() {
		return this.accno;
	}
	/**
	 *  設定進帳方式－1.存款帳號<p/>
	 *  若進帳方式為存款帳號則此欄為存款帳號的內容<br/>
	 *  進帳方式1,2可複選
	 **/
	public void setAccno(String value) {
		this.accno = value;
	}

	/** 
	 * 取得進帳方式－1.進帳金額<p/>
	 * 元
	 */
	public BigDecimal getRctamt1() {
		return this.rctamt1;
	}
	/**
	 *  設定進帳方式－1.進帳金額<p/>
	 *  元
	 **/
	public void setRctamt1(BigDecimal value) {
		this.rctamt1 = value;
	}

	/** 
	 * 取得進帳方式－2.匯款－解款銀行<p/>
	 * 若進帳方式為匯款則此欄為解款銀行的名稱
	 */
	public String getRmtbh() {
		return this.rmtbh;
	}
	/**
	 *  設定進帳方式－2.匯款－解款銀行<p/>
	 *  若進帳方式為匯款則此欄為解款銀行的名稱
	 **/
	public void setRmtbh(String value) {
		this.rmtbh = value;
	}

	/** 
	 * 取得進帳方式－2.匯款－解款帳號<p/>
	 * 若進帳方式為匯款則此欄為解款帳號
	 */
	public String getRmtno() {
		return this.rmtno;
	}
	/**
	 *  設定進帳方式－2.匯款－解款帳號<p/>
	 *  若進帳方式為匯款則此欄為解款帳號
	 **/
	public void setRmtno(String value) {
		this.rmtno = value;
	}

	/** 
	 * 取得進帳方式－2.進帳金額<p/>
	 * 元
	 */
	public BigDecimal getRctamt2() {
		return this.rctamt2;
	}
	/**
	 *  設定進帳方式－2.進帳金額<p/>
	 *  元
	 **/
	public void setRctamt2(BigDecimal value) {
		this.rctamt2 = value;
	}

	/** 
	 * 取得審核書/簽報書文件ID<p/>
	 * 可以此ID讀取Notes審核書/簽報書內容
	 */
	public String getUnid() {
		return this.unid;
	}
	/**
	 *  設定審核書/簽報書文件ID<p/>
	 *  可以此ID讀取Notes審核書/簽報書內容
	 **/
	public void setUnid(String value) {
		this.unid = value;
	}

	/** 
	 * 取得新貸<p/>
	 * 若為新貸案件則為Y
	 */
	public String getNewcase() {
		return this.newcase;
	}
	/**
	 *  設定新貸<p/>
	 *  若為新貸案件則為Y
	 **/
	public void setNewcase(String value) {
		this.newcase = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}
	
	/** 取得進帳 1. 進帳幣別 **/
	public String getRctSwft1() {
		return rctSwft1;
	}
	/** 設定進帳 1. 進帳幣別 **/
	public void setRctSwft1(String rctSwft1) {
		this.rctSwft1 = rctSwft1;
	}
	
	/** 取得進帳 2. 進帳幣別 **/
	public String getRctSwft2() {
		return rctSwft2;
	}
	/** 設定進帳 2. 進帳幣別 **/
	public void setRctSwft2(String rctSwft2) {
		this.rctSwft2 = rctSwft2;
	}
}
