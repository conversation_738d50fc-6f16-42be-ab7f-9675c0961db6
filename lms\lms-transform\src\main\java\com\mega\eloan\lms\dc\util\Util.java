package com.mega.eloan.lms.dc.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FilenameFilter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.StringTokenizer;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Util {
	private static Logger logger = LoggerFactory.getLogger(Util.class);
	/**
	 * 取得LocalIP
	 * 
	 * @return String :User所在位置ip
	 */
	public static String getLocalIp() {
		try {
			InetAddress ip = InetAddress.getLocalHost();
			return ip.getHostAddress();
		} catch (Exception e) {
			System.out
					.println("GetLocalIp Exception caught =" + e.getMessage());
		}
		return "";
	}

	/**
	 * 取得viewList中所有資料
	 * 
	 * @param userPath
	 *            String : User當前工作目錄
	 * @param viewListName
	 *            :要讀取的viewList
	 * @return myList
	 */
	@SuppressWarnings({ "unused" })
	@Deprecated
	public static List<String> getViewList(String userPath, String viewListName) {
		List<String> myList = new ArrayList<String>();
		File vwFile = new File(userPath + File.separator + "conf"
				+ File.separator + viewListName);
		if (null == vwFile) {
			return null;
		}
		try {
			BufferedReader bufin = new BufferedReader(new FileReader(vwFile));
			String rcd = "";
			while ((rcd = bufin.readLine()) != null) {
				String[] str = rcd.split(TextDefine.SYMBOL_SEMICOLON);
				if (str.length == 2) {
					myList.add(rcd);
				} else {
					logger.error("讀取" + viewListName + "檔案資料錯誤:" + rcd);
					System.exit(10);
					return null;
				}
			}
			bufin.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return myList;
	}

	/**
	 * 亂數取得16進位之 UUID
	 * 
	 * @return a randomly generated UUID.
	 */
	public static String getOID() {
		return UUID.randomUUID().toString().toUpperCase()
				.replaceAll(TextDefine.SYMBOL_DASH, TextDefine.EMPTY_STRING);
	}

	/**
	 * 檢查目錄是否存在:若未存在則建立一個
	 * 
	 * @param dirPath
	 *            : 目錄路徑
	 * @TODO multiThreads下此Method需 Synchronized
	 */
	public synchronized static void checkDirExist(String dirPath) {
		File dir = new File(dirPath);
		if (!dir.exists()) {
			dir.mkdirs();
		}
	}

	/**
	 * 取得同一目錄下所有符合該附加檔類型之檔名
	 * 
	 * @param dxlPath
	 *            String :目錄之路徑
	 * @param fileType
	 *            String :附加檔名型態
	 * @return dxllist:String[]
	 */
	public static String[] getSameAttachFile(String path, final String fileType) {
		File file = new File(path);
		String[] filelist = file.list(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				return name.endsWith(fileType);
			}
		});
		return filelist;
	}

	/**
	 * 取得同一目錄下所有符合該附加檔類型之檔名
	 * 
	 * @param dxlPath
	 *            String :目錄之路徑
	 * @param fileStart
	 *            String:檔案起始FormName
	 * @param fileType
	 *            String :附加檔名型態
	 * @return dxllist:String[]
	 */
	public static String[] getSameAttachFile(String path,
			final String fileStart, final String fileType) {

		File file = new File(path);
		String[] filelist = file.list(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				return name.startsWith(fileStart) && name.endsWith(fileType);
			}
		});
		return filelist;
	}

	/**
	 * 取得今天日期
	 * 
	 * @return String :今天日期yyyyMMdd
	 */
	public static String getToday() {
		Calendar cal = Calendar.getInstance();
		return new SimpleDateFormat("yyyyMMdd").format(cal.getTime());
	}

	/**
	 * 取得目前時間
	 * 
	 * @return String :目前時間HH:mm:ss
	 */
	public static String getNowTime() {
		Calendar cal = Calendar.getInstance();
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
				.format(cal.getTime());
	}

	/**
	 * NULL 轉為空白.
	 * 
	 * @param str
	 *            要執行的字串.
	 */
	public static String nullToSpace(String str) {
		String value = "";
		if (null != str && str.length() > 0) {
			value = str.trim();
		}
		return value;
	}

	/**
	 * 判斷字元是否為英文字
	 * 
	 * @param ch
	 *            char:需被判斷的字元
	 * @return True:英文字
	 */
	public static boolean isEnglish(char ch) {
		return CharUtils.isAsciiAlpha(ch);
	}

	/**
	 * 判斷字串是否含有中文字
	 * 
	 * @param str
	 *            String:需被判斷的字元
	 * @return True:有中文字,false :無中文
	 * @TODO:此方法只判斷是否有中文其它符號一律不辨識
	 */
	public static boolean isChineseChr(String str) {
		boolean flag = false;
		if (StringUtils.isNotBlank(str)) {
			char[] chars = str.toCharArray();
			for (int i = 0; i < chars.length; i++) {
				if (!CharUtils.isAsciiPrintable(chars[i])) {
					flag = true;
					break;
				}
			}
			return flag;
		}
		return flag;
	}

	/**
	 * 判斷字串是否為數字
	 * 
	 * @param str
	 *            String:需被判斷的字串
	 * @return True:數字 , false:字串
	 */
	public static boolean isNumeric(String str) {
		if (StringUtils.isBlank(str)) {
			return false;
		}
		try {
			new BigDecimal(str);
			return true;
		} catch (NumberFormatException nfe) {
			return false;
		}
	}

	/**
	 * 文字轉BigDecimal
	 * 
	 * @param str
	 *            String:被轉換的字串
	 * @return bd BigDecimal
	 */
	public static BigDecimal parseToBigDecimal(String str) {
		BigDecimal bd = BigDecimal.ZERO;
		if (StringUtils.isNotBlank(str)) {
			if (isNumeric(str)) {
				try {
					bd = new BigDecimal(str.trim());
				} catch (Exception e) {
					// nothing to do............
				}
			}
		}
		return bd;
	}

	/**
	 * 文字轉整數
	 * 
	 * @param string
	 * @return int
	 */
	public static int parseInt(String value) {
		int result = 0;
		if (isNumeric(value)) {
			result = Integer.parseInt(value);
		}
		return result;
	}

	/**
	 * 截掉頭部、尾部的全形空白 (因 DB char 會自動補空白 所以要 trimSpace)
	 * 
	 * @param str
	 * @return str
	 */
	public static String trimSpace(String str) {
		if (str == null || str.equals("")) {
			return "";
		}
		// trim 半型空白(頭、尾)
		str = str.trim();
		// trim 全型空白(頭)
		int len = str.length();
		int index = 0;
		while (index < len) {
			if (str.charAt(index) == '　')
				index++;
			else
				break;
		} // end while
		str = str.substring(index, len);
		// trim 全型空白(尾)
		len = str.length();
		index = len;
		while (index > 0) {
			if (str.charAt(index - 1) == '　')
				index--;
			else
				break;
		} // end while
		str = str.substring(0, index);
		return str;

	} // end trimSpace

	/**
	 * 文字長度是否過長
	 * 
	 * @param src
	 *            欲檢核文字
	 * @param lim
	 *            文字長度
	 * @return
	 */
	public static boolean isTooLong(String src, int lim) {
		if (null == src || src.length() == 0 || lim == 0) {
			return false;
		}
		if (lim < 0) {
			throw new RuntimeException("無文字長度");
		}
		int bys = 0, cnt = -1;
		char[] chars = src.toCharArray();
		int len = chars.length;
		int i = 0;
		while (++cnt < len) {
			// Mega DB為utf-8中文字每個是算三
			i = (chars[cnt] < 0x0100) ? 1 : 3; // test char is double byte
			bys += i;
			if (bys > lim) {
				return true;
			}
		}
		return false;
	}

	public static boolean isInteger(String value) {
		try {
			Integer.valueOf(value).intValue();
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	/**
	 * 是否為數值並且符合所定義長度
	 * 
	 * @param value
	 *            資料
	 * @param length
	 *            整數位
	 * @param scale
	 *            小數位
	 * @return
	 */
	public static boolean isNumber(String value, int length, int scale) {
		boolean chk = false;

		if (Util.isNumeric(value)) {
			// 小數
			StringBuffer b = new StringBuffer();
			for (int i = 0; i < scale; i++) {
				b.append("9");
			}
			// 整數
			StringBuffer a = new StringBuffer();
			for (int i = 0; i < (length - scale); i++) {
				a.append("9");
			}
			// 檢查數值是否小於最大值
			BigDecimal max = new BigDecimal(a.append(".").append(b).toString());
			BigDecimal bd = new BigDecimal(value);
			return (bd.compareTo(max) <= 0);
		}
		return chk;
	}

	private static final Pattern datePattern = Pattern
			.compile("(\\d{2,4})[-/]?(\\d{2})[-/]?(\\d{2})");

	/**
	 * 判斷是否為合法的日期字串
	 * 
	 * @param text
	 * @return
	 */
	public static boolean isDate(String text) {
		try {
			if (text != null && text.length() > 0) {
				// 避掉有ERR_的錯誤日期
				if (text.indexOf("ERR_") > -1) {
					return false;
				}
				// 先把日期做的format: 先做成 "四碼-兩碼-兩碼"的樣式不足位數補0
				String[] data = text.split("[-/]");
				// 這個步驟不應該出現民國年 ,"0001-01-01"為日期預設值
				if (!data[0].equalsIgnoreCase("0001")
						&& Integer.parseInt(data[0]) < 1000) {
					return false;
				}
				if (data.length == 3) {
					String year = Util.addZeroWithValue(data[0], 2);
					String month = Util.addZeroWithValue(data[1], 2);
					String day = Util.addZeroWithValue(data[2], 2);
					text = year + "-" + month + "-" + day;
				}
				Matcher matcher = datePattern.matcher(text);
				if (matcher.find()) {
					int year = Integer.parseInt(matcher.group(1), 10);
					int month = Integer.parseInt(matcher.group(2), 10);
					int day = Integer.parseInt(matcher.group(3), 10);
					int dayLimit = 30;

					if (month == 2) {
						dayLimit = (year % 4 == 0 && year % 100 > 0)
								|| (year % 400 == 0) ? 29 : 28;
					} else if (ArrayUtils.contains(new int[] { 1, 3, 5, 7, 8,
							10, 12 }, month)) {
						dayLimit = 31;
					}
					return year > 0 && month > 0 && day > 0 && month <= 12
							&& day <= dayLimit;
				}
			}
			return false;
		} catch (Exception e) {
			logger.error("Util.java判斷是否為合法的日期字串時出現錯誤:" + e);
			return false;
		}
	}

	/**
	 * 字串前補0
	 * 
	 * @param str
	 * @param num
	 *            不足幾位補零 <br/>
	 * @return 補0後之字串 <br/>
	 * <AUTHOR> EX: addZeroWithValue("1",3) -> "001"
	 */
	public static String addZeroWithValue(String str, int num) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < num - str.length(); i++) {
			sb.append("0");
		}
		sb.append(str);
		return sb.toString();
	}

	/**
	 * 將字串截至符合 limit bytes數目的長度
	 * 
	 * @param src
	 *            要截字數的字串
	 * @param lim
	 *            DB2的長度, -1 為 不設限制
	 * @return 傳回截成符合長度的字串
	 */
	public static String truncateString(String src, int lim) {
		if (null == src || src.length() == 0 || lim == 0)
			return "";
		if (lim < 0)
			return src;
		int bys = 0, cnt = -1;
		char[] chars = src.toCharArray();
		int len = chars.length;
		int i = 0;
		while (++cnt < len) {
			// modify by Vance, utf-8中文字每個是算三
			i = (chars[cnt] < 0x0100) ? 1 : 3; // test char is double byte
			bys += i;
			if (bys > lim)
				break;
		}
		return String.valueOf(chars, 0, cnt);
	}

	/**
	 * 取得Timestamp型態之系統時間
	 * 
	 * @return String
	 */
	public static String getCurrentTimestamp() {
		Calendar calendar = Calendar.getInstance();
		java.util.Date now = calendar.getTime();
		java.sql.Timestamp currentTimestamp = new java.sql.Timestamp(
				now.getTime());
		String strDTime = currentTimestamp + "000000";
		strDTime = strDTime.replaceAll(":", ".");
		strDTime = strDTime.replaceAll(" ", "-");
		return strDTime.substring(0, 26);
	}

	public static String millis2minute(long millis) {
		return String.format(
				" %d 分 %d 秒 ( %d ms) ",
				TimeUnit.MILLISECONDS.toMinutes(millis),
				TimeUnit.MILLISECONDS.toSeconds(millis)
						- TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS
								.toMinutes(millis)), millis);
	}

	/**
	 * 是否為空值
	 * 
	 * @param str
	 * @return true:空值 false:不是空值
	 */
	public static boolean isEmpty(String str) {
		return (null == str || str.length() == 0);
	}

	/**
	 * 是否為空值
	 * 
	 * @param str
	 * @return true:空值 false:不是空值
	 */
	public static boolean isEmptyWithTrim(String str) {
		return isEmpty(nullToSpace(str));
	}

	/**
	 * <pre>
	 * 檢核TODAY格式為是否為NMMDDS
	 * N：1碼可變更之英文字，目前請設為N，以免跟現行行員編號重複
	 * MM:兩碼月
	 * DD:兩碼日
	 * S:1碼序號1~9、A~Z
	 * </pre>
	 * 
	 * @param str
	 * @return
	 */
	public static boolean checkToDayFormat(String str) {
		boolean isMatch = true;
		String _value = Util.nullToSpace(str);
		if (Util.isEmpty(str)) {
			isMatch = false;
		}
		Pattern ptn = Pattern.compile("^[A-Z]{1}[0-9]{4}[0-9A-Z]{1}$");
		if (!ptn.matcher(_value).matches()) {
			isMatch = false;
		}
		return isMatch;
	}

	/**
	 * ============================================================
	 * 功能說明：取得split之大小
	 * =============================================================
	 */
	public static int getSplitSize(String source, String delim) {
		if (source.length() <= 0) // 空字串
			return 0;
		else { // 非空字串
			int idx = 0, count = 0;
			while ((idx = source.indexOf(delim, idx)) != -1) {
				count++;
				idx += 1;
			} // end while
			if (count == 0) // 只有一個內容
				return 1;
			else
				// 有兩個以上的內容
				return count += 1;
		} // end else non-empty string
	} // end getSplitSize

	/**
	 * ============================================================
	 * 功能說明：將字串依據關鍵字分割成字串陣列 source: 欲分割字申 delim: 分割所依據的關鍵字
	 * =============================================================
	 */
	public static String[] split(String source, String delim) {
		if (source != null) {
			int size = getSplitSize(source, delim);
			if (size <= 0)
				return new String[0];
			else {
				if (size == 1) {
					String[] strarr = { source };
					return strarr;
				} else {
					String[] strarr = new String[size];
					int idx = 0;
					for (int i = 0; i < strarr.length; i++) {
						idx = source.indexOf(delim);
						if (idx < 0) { // ex: "abc"
							strarr[i] = source;
							break;
						} else if (idx == 0) { // ex: "," or ",," or ",aa,bb" or
												// ",aa,bb,"
							strarr[i] = "";
							if (source.length() == 1) {
								strarr[i + 1] = "";
								break;
							}
						} // idx == 0;
						else if (idx == (source.length() - 1)) { // ex: "abc,"
							strarr[i] = source.substring(0, idx);
							strarr[i + 1] = "";
							break;
						} else { // ex: "abc,cd"
							strarr[i] = source.substring(0, idx);
						}
						source = source.substring(idx + delim.length(),
								source.length());
					} // end for
					return strarr;
				} // end size > 1
			} // end size > 0
		} // if source != null
		else {
			return new String[0];
		}
	}

	/**
	 * 將含有半形的字元轉為全形.
	 * 
	 * @param text
	 *            String 欲轉換的字串
	 * @return String 全形的字串
	 */
	public static String toFullCharString(String text) {
		if (text == null)
			return "";
		StringBuffer sb = new StringBuffer();
		try {
			for (int i = 0; i < text.length(); i++) {
				if (isFullChar(text.substring(i, i + 1))) {
					sb.append(text.substring(i, i + 1));
				} else {
					sb.append(charSemitoFull(text.charAt(i)));
				}
			}

		} catch (Exception e) {
			return null;
		}
		return sb.toString();
	}

	/**
	 * 判斷此字元是否為全型字.
	 * 
	 * @param text
	 *            String
	 * @return boolean
	 */
	private static boolean isFullChar(String text) {
		try {
			String strHex = Integer.toHexString((int) text.charAt(0));
			return (strHex.length() == 4) ? true : false;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 對單一字元進行半形轉全形 .
	 * 
	 * @param ch
	 *            char
	 * @return char
	 */
	private static char charSemitoFull(char ch) {
		char chFull = '0';
		try {
			int iChar = (int) ch;
			// 英文字母(Alphabet) && 數字(Number)
			if ((iChar >= 48 && iChar <= 57) || (iChar >= 65 && iChar <= 90)
					|| (iChar >= 97 && iChar <= 122)) {
				iChar -= 32;
				String after = "ff"
						+ String.valueOf(Integer.toHexString(iChar));
				chFull = (char) Integer.parseInt(after, 16);
			} else {
				// 特殊符號
				switch (iChar) {
				case 33: // 21 ! A149 ！ FF01
					chFull = (char) Integer.parseInt("FF01", 16);
					break;
				case 34: // 22 " A1A8 ” 201D
					chFull = (char) Integer.parseInt("201D", 16);
					break;
				case 35: // 23 # A1AD ＃ FF03
					chFull = (char) Integer.parseInt("FF03", 16);
					break;
				case 36: // 24 $ A243 ＄ FF04
					chFull = (char) Integer.parseInt("FF04", 16);
					break;
				case 37: // 25 % A248 ％ FF05
					chFull = (char) Integer.parseInt("FF05", 16);
					break;
				case 38: // 26 & A1AE ＆ FF06
					chFull = (char) Integer.parseInt("FF06", 16);
					break;
				case 39: // 27 ' A1A6 ’ 2019
					chFull = (char) Integer.parseInt("2019", 16);
					break;
				case 40: // 28 ( A15D （ FF08
					chFull = (char) Integer.parseInt("FF08", 16);
					break;
				case 41: // 29 ) A15E ） FF09
					chFull = (char) Integer.parseInt("FF09", 16);
					break;
				case 42: // 2A * A1AF ＊ FF0A
					chFull = (char) Integer.parseInt("FF0A", 16);
					break;
				case 43: // 2B + A1CF ＋ FF0B
					chFull = (char) Integer.parseInt("FF0B", 16);
					break;
				case 44: // 2C , A141 ， FF0C
					chFull = (char) Integer.parseInt("FF0C", 16);
					break;
				case 45: // 2D - A1D0 － FF0D
					chFull = (char) Integer.parseInt("FF0D", 16);
					break;
				case 46: // 2E . A144 ．FF0E
					chFull = (char) Integer.parseInt("FF0E", 16);
					break;
				case 47: // 2F / A1FE ∕ FF0F
					chFull = (char) Integer.parseInt("FF0F", 16);
					break;
				case 58: // 3A : A147 ： FF1A
					chFull = (char) Integer.parseInt("FF1A", 16);
					break;
				case 59: // 3B ; A146 ； FF1B
					chFull = (char) Integer.parseInt("FF1B", 16);
					break;
				case 60: // 3C < A1D5 ＜ FF1C
					chFull = (char) Integer.parseInt("FF1C", 16);
					break;
				case 61: // 3D = A1D7 ＝ FF1D
					chFull = (char) Integer.parseInt("FF1D", 16);
					break;
				case 62: // 3E > A1D6 ＞ FF1E
					chFull = (char) Integer.parseInt("FF1E", 16);
					break;
				case 63: // 3F ? A148 ？ FF1F
					chFull = (char) Integer.parseInt("FF1F", 16);
					break;
				case 64: // 40 @ A249 ＠ FF20
					chFull = (char) Integer.parseInt("FF20", 16);
					break;
				case 92: // 5C \ A240 ﹨ FF3C
					chFull = (char) Integer.parseInt("FF3C", 16);
					break;
				case 95: // 5F _ A1C4 ＿ FF3F
					chFull = (char) Integer.parseInt("FF3F", 16);
					break;
				case 123: // 7B { A1A1 ﹛ FF5B
					chFull = (char) Integer.parseInt("FF5B", 16);
					break;
				case 124: // 7C | A155 ｜ FF5C
					chFull = (char) Integer.parseInt("FF5C", 16);
					break;
				case 125: // 7D } A1A2 ﹜ FF5D
					chFull = (char) Integer.parseInt("FF5D", 16);
					break;
				case 126: // 7E ~ A1E3 ? FF5E
					chFull = (char) Integer.parseInt("FF5E", 16);
					break;
				case 91: // 5B [ FF3B No corresponding
				case 93: // 5D ] FF3D No corresponding
				case 94: // 5E ^ FF3E No corresponding
				case 96: // 60 ` FF40 No corresponding
				default: // No corresponding 塞 全型空白
					chFull = (char) Integer.parseInt("3000", 16);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return chFull;
	}

	// 標準日期格式
	private static final DateFormat format = new SimpleDateFormat(
			"yyyy-MM-dd hh:mm:ss");

	// 一般西元年日期
	private static final DateFormat format_ad = new SimpleDateFormat(
			"yyyy-MM-dd");

	/**
	 * 轉為日期字串
	 * 
	 * @param date
	 * @return
	 */
	public static String parseDateToString(String date) {
		int iDate = parseDate(date);
		if (isDate(iDate)) {
			// do nothing
		} else if (isTWNDate(iDate)) {
			iDate += 19110000;
		} else {
			return "";
		}
		String value = null;
		try {
			value = format_ad.format(format.parse(normalize(String
					.valueOf(iDate))));
		} catch (Exception e) {
			// 無法轉換回傳原值
			return date;
			// throw new RuntimeException("無法初始化日期！[" + date + "]" + e);
		}
		return value;
	}

	/**
	 * 將字串日期轉為整數(會除去分隔符號)，But不做日期轉換
	 * 
	 * @param date
	 *            傳入值是字串
	 */
	public static int parseDate(String date) {
		if (date == null)
			return 0;
		if (date.trim().length() == 0)
			return 0;
		int iDate;
		date = date.trim();
		try {
			int iPos, iEnd;
			// date=date.replaceAll("-","/"); //V1.4的用法
			int iToken = date.indexOf("-", 0);
			while ((iToken) != -1) {
				date = date.substring(0, iToken) + "/"
						+ date.substring(iToken + 1, date.length());
				iToken++;
				if (iToken > date.length()) {
					break;
				} else {
					iToken = date.indexOf("-", iToken);
				}
			}

			iPos = date.indexOf("/", 0);
			if (iPos == -1) {
				return Integer.parseInt(date);
			}
			iDate = Integer.parseInt(date.substring(0, iPos)) * 10000;
			iEnd = date.indexOf("/", iPos + 1);
			iDate += Integer.parseInt(date.substring(iPos + 1, iEnd)) * 100;
			iDate += Integer.parseInt(date.substring(iEnd + 1, date.length()));
		} catch (Exception e) {
			return 0;
		}
		return iDate;
	}

	/**
	 * 判斷運算結果是否為合法的民國日期 用7位整數判斷
	 * 
	 * @param date
	 *            傳入值是整數
	 */
	public static boolean isTWNDate(int date) {
		if (date < 10000 || date > 9999999) {
			return false;
		} else {
			return isDate(date + 19110000);
		}
	}

	/**
	 * 判斷運算結果是否為合法的日期 用8位整數判斷 Note:為避免與民國年混淆，年度小於999時,return false
	 * 
	 * @param date
	 *            傳入值是整數的西元日期
	 */
	public static boolean isDate(int date) {

		if (date < 10000 || date > 99999999) {
			return false;
		} else {
			int Year = date / 10000;
			//
			if (Year <= 999)
				return false;
			int Month = (date - Year * 10000) / 100;
			int Day = date - Year * 10000 - Month * 100;
			switch (Month) {
			case 1:
			case 3:
			case 5:
			case 7:
			case 8:
			case 10:
			case 12:
				if (Day < 1 || Day > 31) {
					return false;
				}
				break;

			case 4:
			case 6:
			case 9:
			case 11:
				if (Day < 1 || Day > 30) {
					return false;
				}
				break;
			case 2:
				if (Year % 400 == 0) {
					if (Day < 1 || Day > 29) {
						return false;
					}
				} else if (Year % 100 == 0) {
					if (Day < 1 || Day > 28) {
						return false;
					}
				} else if (Year % 4 == 0) {
					if (Day < 1 || Day > 29) {
						return false;
					}
				} else {
					if (Day < 1 || Day > 28) {
						return false;
					}
				}
				break;
			default:
				return false;
			}
			return true;
		}
	}

	/** 民國年判斷條件，低於此值則認定為民國年 */
	private static final int TWN_YEAR_LIMIT = 1000;

	private static final int BUFFER_SIZE = 32;

	/**
	 * 將日期字串標準化為"y-M-d h:m:s"的格式
	 * 
	 * @param date
	 * @return 標準化的格式字串
	 */
	private static String normalize(String date) {
		// 分隔欄位
		int[] tmpDates = new int[10];
		int len = 0;
		StringTokenizer st = new StringTokenizer(date, " :/-");
		while (st.hasMoreElements()) {
			tmpDates[len++] = Integer.parseInt(st.nextElement().toString());
		}

		int[] dates = new int[len];
		System.arraycopy(tmpDates, 0, dates, 0, len);

		// 判斷是否為合法的日期字串
		if (len != 3 && len != 6) {
			// 嘗試沒有分隔字元的組合
			int size = date.length();
			if (size == 7 || size == 8) {
				len = 3;
				dates = new int[len];
				dates[0] = Integer.parseInt(date.substring(0, size - 4), 10);
				dates[1] = Integer.parseInt(date.substring(size - 4, size - 2),
						10);
				dates[2] = Integer.parseInt(date.substring(size - 2, size), 10);
			}
		}
		if ((len != 3 && len != 6) || !checkLimit(dates)) {
			throw new RuntimeException("不是合法的日期字串格式！[" + date + "]");
		}

		// 判斷是否為民國年
		if (dates[0] < TWN_YEAR_LIMIT) {
			dates[0] += 1911;
		}

		// 串接西元完整日期，無提供的欄位皆補0
		StringBuilder buffer = new StringBuilder(BUFFER_SIZE);
		if (len >= 3) {
			buffer.append(dates[0]).append("-").append(dates[1]).append("-")
					.append(dates[2]).append(" ");
		}
		if (len >= 6) {
			buffer.append(dates[3]).append(":").append(dates[4]).append(":")
					.append(dates[5]).append(" ");
		} else {
			buffer.append("0:0:0");
		}
		return buffer.toString();
	}

	/**
	 * 判斷分隔後的日期欄位值，是否在範圍內
	 * 
	 * @param dates
	 *            分隔後的日期欄位值<br/>
	 *            長度為3(y,M,d)或6(y,M,d,h,m,s)
	 * @return 是否在範圍內
	 */
	private static boolean checkLimit(int[] dates) {
		boolean result = true;
		int len = dates.length;
		if (len >= 3) {
			int dayLimit = 30;
			if (dates[1] == 2) {
				int year = dates[0] + (dates[0] < TWN_YEAR_LIMIT ? 1911 : 0);
				dayLimit = ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) ? 29
						: 28;
			} else if (dates[1] == 1 || dates[1] == 3 || dates[1] == 5
					|| dates[1] == 7 || dates[1] == 8 || dates[1] == 10
					|| dates[1] == 12) {
				dayLimit = 31;
			}

			result &= dates[0] > 0 && dates[1] > 0 && dates[2] > 0
					&& dates[0] <= 9999 && dates[1] <= 12
					&& dates[2] <= dayLimit;
		}
		if (len >= 6) {
			result &= dates[3] >= 0 && dates[4] >= 0 && dates[5] >= 0
					&& dates[3] <= 24 && dates[4] <= 60 && dates[5] <= 60;
		}
		return result;
	}

	/**
	 * 去除前後的空白字元
	 * 
	 * @param str
	 */
	public static String trim(Object value) {
		return value == null ? "" : trim(value.toString());
	}

	/**
	 * 去除前後的空白字元
	 * 
	 * @param str
	 */
	public static String trim(String str) {
		if (Util.isEmpty(str)) {
			return "";
		}
		return str.trim();
	}

	/**
	 * <pre>
	 * 檢核是否為英數字
	 * </pre>
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isEnOrNum(String str) {
		boolean isMatch = true;
		String _value = Util.nullToSpace(str);
		if (Util.isEmpty(str)) {
			isMatch = false;
		}
		Pattern ptn = Pattern.compile("^[A-Za-z0-9]{1,}$");
		if (!ptn.matcher(_value).matches()) {
			isMatch = false;
		}
		return isMatch;
	}

	/**
	 * 移除非數字欄位
	 * 
	 * @param value
	 * @return
	 */
	public static String getAllNumString(String value) {
		if (StringUtils.isBlank(value)) {
			return "";
		}
		return value.replaceAll("[^0-9]", "");
	}

	public static void main(String args[]) {
		
		logger.info("[" + Util.isEnOrNum("A2147483646za測試") + "]");
	}

	public static String getHexString(byte[] src, String encode)
			throws UnsupportedEncodingException {
		StringBuffer result = new StringBuffer();
		if (encode != null) {
			result.append("[").append(new String(src, encode)).append("] (")
					.append(encode).append(") ==> ");
		} else {
			result.append("[").append(new String(src)).append("] ==> ");
		}

		for (byte b : src) {
			result.append(String.format("%02X ", b));
		}
		return result.toString();
	}
}
