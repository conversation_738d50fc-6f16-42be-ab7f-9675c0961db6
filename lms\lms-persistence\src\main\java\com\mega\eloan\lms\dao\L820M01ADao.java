/* 
 * L820M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L820M01A;

/** 以房養老貸款博款前查詢檔 **/
public interface L820M01ADao extends IGenericDao<L820M01A> {

	L820M01A findByOid(String oid);
	
	L820M01A findByMainId(String mainId);
	
	L820M01A findByConst(String custId, String dupNo, String createTime);
	
	List<L820M01A> findByMainId01(String mainId);
	
	List<L820M01A> findByDocStatus(String docStatus, String createTime);
	
	L820M01A findByUniqueKey(String mainId, String custId, String dupNo);

	List<L820M01A> findByIndex01(String mainId, String custId, String dupNo);
	
}