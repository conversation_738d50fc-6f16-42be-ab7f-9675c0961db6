var panelAction = {
    handler: 'cls1161formhandler',
    gridhandler: 'cls1161gridhandler',
    L120M01AGrid: null, //簽報書
    PTEAMAPPGrid: null, //團貸年度總額度檔
    L140M01AGrid: null, //額度明細表
    L140M01AGrid2: null, //額度明細表
    caseType: '', //動審表種類
    caseData: null,
    init: function(){
        $('#CaseType3Form').find('.children').hide();
        //僅有電銷權限(EX01、EX02)時隱藏 "登錄"、"先行動用待辦控制表" 按鈕
		//根據權限隱藏特定物件
        $.ajax({
            action: "check_only_expermission",
            handler: panelAction.handler,
            success: function(responseData){
            	if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
            		$(".only-ex-permission").hide();
            	}
            }
        });
    },
    build: function(){
        //var $div = $('#C160M01ADiv');
        
        //選擇額度明細表
        $('body').find('#btCaseType1').click(function(){
            panelAction.caseType = '1';
            panelAction.openCaseType1();
        }) //選擇額度明細表(團貸用)
        .end().find('#btCaseType2').click(function(){
            panelAction.caseType = '2';
            panelAction.openCaseType2();
        }) //匯入EXCEL
        .end().find('#btCaseType3').click(function(){
            panelAction.caseType = '3';
            panelAction.openCaseType3();
        }) //給號
        .end().find('#btPackNo').click(function(){
			if($("#loanMasterNo").val()!=''){
            	panelAction.getNumber();
			}else{
				MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.cls1161s01["error.message1"]);
			}
        }).end().find("[name=GetNumber]").click(function(){
			if ($('input:radio:checked[name="GetNumber"]').val() == 1) {
				$("#SetpackNo").attr("disabled", "disabled").removeClass("required").val('');
			}else{
				$("#SetpackNo").removeAttr("disabled", "disabled").addClass("required","required");
			}
        }).end().find("#btnNote").click(function(){
        	API.confirmMessage("經辦是否已完成照會", function(result){
                if (result) {
                   $.ajax({
                       type: "POST",
                       handler: panelAction.handler,
                       data: {
                           formAction: "noteTheApply",
                           mainId: responseJSON.mainId
                       },
                       success: function(responseData){
                    	   pageAction.init();
                       }
                   });
                }
        	})
        });
        
        //簽報書Grid
        panelAction.L120M01AGrid = $('#L120M01AGrid').iGrid({
            localFirst: true,
            handler: panelAction.gridhandler, //設定handler
            height: 250, //設定高度
            action: 'L120M01AQuery', //執行的Method
            rowNum: 15,
            rownumbers: true,
            colModel: [{
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                name: 'docKind',
                hidden: true //是否隱藏
            }, {
                colHeader: i18n.cls1161s01["L120M01A.custId"], //統一編號
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custId'
            }, {
                colHeader: i18n.cls1161s01["L120M01A.caseDate"], //簽案日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseDate'
            }, {
                colHeader: i18n.cls1161s01["L120M01A.approveTime"], //核准日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'approveTime'
            }, {
                colHeader: i18n.cls1161s01["C160M01A.caseNo"], //案 號
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseNo'
            }]
        });
        
        //簽報書Grid-團貸
        panelAction.PTEAMAPPGrid = $('#PTEAMAPPGrid').iGrid({
            localFirst: true,
            handler: panelAction.gridhandler, //設定handler
            height: 250, //設定高度
            action: 'PTEAMAPPQuery', //執行的Method
            rowNum: 1000,
            rownumbers: true,
            //rowList: [],        // disable page size dropdown
            pgbuttons: false,     // disable page control like next, back button
            pgtext: null,         // disable pager text like 'Page 0 of 10'
            //viewrecords: false    // disable current view record text like 'View 1-10 of 100'
            colModel: [{
                colHeader: i18n.cls1161s01["PTEAMAPP.YEAR"], //年度
                align: "center",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'YEAR' //col.id
            }, {
                colHeader: i18n.cls1161s01["PTEAMAPP.PROJECTNM"], //團貸戶戶名
                align: "left",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'PROJECTNM' //col.id
            }, {
                colHeader: i18n.cls1161s01["PTEAMAPP.GRPCNTRNO"], //團貸編號
                align: "left",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'GRPCNTRNO' //col.id
            }, {
                colHeader: i18n.cls1161s01["PTEAMAPP.SUBCOMPNM"], //子戶戶名
                align: "left",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'SUBCOMPNM' //col.id
            }, {
                colHeader: i18n.cls1161s01["PTEAMAPP.EFFFROM"], //總額度有效起日
                align: "center",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'EFFFROM' //col.id
            }, {
                colHeader: i18n.cls1161s01["PTEAMAPP.EFFEND"], //總額度有效迄日
                align: "center",
                width: 100, //設定寬度
                sortable: false, //是否允許排序
                name: 'EFFEND' //col.id
            },
//			 {
//                colHeader: i18n.cls1161s01["PTEAMAPP.OVERAMT"], //剩餘額度 
//                align: "right",
//                width: 100, //設定寬度
//                sortable: false, //是否允許排序
//                formatter: GridFormatter.number.addComma,
//                name: 'OVERAMT' //col.id
//            }, 
			{
                name: 'CUSTID',
                hidden: true //是否隱藏
            }, {
                name: 'DUPNO',
                hidden: true //是否隱藏
            }, {
                name: 'AMTAPPNO',
                hidden: true //是否隱藏
            }, {
                name: 'SUBCOMPID',
                hidden: true //是否隱藏
            }, {
                name: 'MGRPCNTRNO',
                hidden: true //是否隱藏
            }, {
                name: 'ISSUEBRNO',
                hidden: true //是否隱藏
            }, {
                name: 'BUILDNAME',
                hidden: true //是否隱藏
            }]
        });
        
        var setting = {
            localFirst: true,
            handler: panelAction.gridhandler, //設定handler
            height: 150, //設定高度
            action: 'L140M01AQuery', //執行的Method
            rowNum: 1000,
            pgbuttons: false,     // disable page control like next, back button
            pgtext: null,         // disable pager text like 'Page 0 of 10'
            rownumbers: true,
            multiselect: true, //是否開啟多選
            colModel: [{
                name: 'oid',
                hidden: true //是否隱藏
            }, {
                name: 'mainId',
                hidden: true //是否隱藏
            }, {
                colHeader: i18n.cls1161s01["L140M01A.custId"], //統一編號
                align: "left",
                width: 70, //設定寬度
                sortable: true, //是否允許排序
                name: 'custId'
            }, {
                colHeader: i18n.cls1161s01["L140M01A.custName"], //客戶名稱
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'custName'
            }, {
                colHeader: i18n.cls1161s01["L140M01A.cntrNo"], //額度序號
                align: "left",
                width: 70, //設定寬度
                sortable: true, //是否允許排序
                name: 'cntrNo'
            }, {
                colHeader: i18n.cls1161s01["L140M01A.caseNo"], //案號
                align: "left",
                width: 160, //設定寬度
                sortable: true, //是否允許排序
                name: 'caseNo'
            }, {
                colHeader: i18n.cls1161s01["L140M01A.approveTime"], //核准日期
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                name: 'approveTime'
            }]
        }
        //額度明細表Grid
        panelAction.L140M01AGrid = $('#L140M01AGrid').iGrid($.extend({
        	 loadComplete : function(){
        		  if (panelAction.L140M01AGrid) checkGrid.finish1 = true;
           		checkGrid.check();
           }
        },setting));
        panelAction.L140M01AGrid2 = $('#L140M01AGrid2').iGrid($.extend({
	       	 loadComplete : function(){
	       		 	if (panelAction.L140M01AGrid2) checkGrid.finish2 = true;
	          	checkGrid.check();
	         }
       },setting));
        //build button 
        //子公司重新引進
        $('#CaseType3Form').find('#childrenPullinAgain').click(function(){
            CommonAPI.openQueryBox({
                defaultValue: $('#CaseType3Form').find('#childrenId').val(),//指定時會自動查詢 
                defaultCustType: '1', //2.英文名
                divId: 'CaseType3Form', //在哪個div 底下 
                autoResponse: { // 是否自動回填資訊 
                    id: 'childrenId', // 統一編號欄位ID 
                    //dupno: 'rDupNo', // 重覆編號欄位ID 
                    name: 'childrenName' // 客戶名稱欄位ID 
                }
            });
        })        //是否流用旗下子公司
        .end().find('input[name=isUseChildren]').click(function(){
            var $form = $('#CaseType3Form');
            $form.find('.children').hide();
            if ($(this).val() == 'Y') 
                $form.find('.children').show();
        })        //下載EXCEL
        .end().find('#downloadExcel').click(function(){
            $.capFileDownload({
                data: {
                    fileOid: $('#excelId').val()
                }
            });
        })        //上傳EXCEL
        .end().find('#btUploadExcel').click(function(){
            MegaApi.uploadDialog({
                fieldId: "fileName",
                width: 320,
                height: 100,
                fileCheck: ['.xls'],
                data: {
                    deleteDup: true,
                    uid: responseJSON.oid, //避免此檔案被列為此文件之附加檔案
                    mainId: responseJSON.mainId
                },
                success: function(response){
                    var $form = $('#CaseType3Form');
                    $form.find('#downloadExcel').show();
                    $form.find('#excelId').val(response.fileKey);
                    $form.find('#progress').html('初始化中，請稍後...'); //finish 100%
                    $form.find('#progressTr').show();
                    panelAction.importExcelStep(1);
                }
            });
        })        //
        .end().find('#selectCaseNo').click(function(){
            var sendData = $.extend($('#CaseType3Form').serializeData(), {
                caseType: panelAction.caseType
            });
            panelAction.openPTEAMAPP(sendData);
        });
    },
    /**
     * 選擇額度明細表
     */
    openCaseType1: function(){
        $('#CaseType1ThickBox').thickbox({
            title: i18n.def['query'],
            width: 400,
            height: 200,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var $form = $('#CaseType1Form');
                    if ($form.valid()) {
                        $.thickbox.close();
                        panelAction.openL120M01A($form.serializeData());
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 選擇額度明細表(團貸用)
     */
    openCaseType2: function(){
        $('#CaseType2ThickBox').thickbox({
            title: i18n.def['query'],
            width: 400,
            height: 100,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var $form = $('#CaseType2Form');
                    if ($form.valid()) {
                        $.thickbox.close();
                        panelAction.openPTEAMAPP($form.serializeData());
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 匯入EXCEL
     */
    openCaseType3: function(){
        //下載EXCEL
        $('#progressTr').hide();
        $('#downloadExcel').hide();
        if ($('#excelId').val()) 
            $('#downloadExcel').show();
        
        $('#CaseType3ThickBox').thickbox({
            title: i18n.cls1161s01['button.btCaseType3'],
            width: 600,
            height: 450,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    if ($('#CaseType3Form').valid()) {
                        MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
                            if (action) {
                                panelAction.importExcel({});
                            }
                        });
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 匯入EXCEL step
     */
    importExcelStep: function(step, data){
        $.ajax({
            handler: panelAction.handler,
            action: 'importExcelStep' + step,
            formId: 'CaseType3Form',
            data: data || {},
            success: function(response){
                var $form = $('#CaseType3Form')
                if (response.CaseType3Form) 
                    $form.setValue(response.CaseType3Form);
                
                var progress = 0;
                switch (step) {
                    case 1:
                        progress = 5;
                        panelAction.importExcelStep(2);
                        break;
                    case 2:
                        if (/\d/.test(response.end) && /\d/.test(response.rows)) {
                            var end = parseInt(response.end);
                            var rows = parseInt(response.rows);
                            if (end < rows && end > 0 && rows > 0) {
                                progress = ((end / rows) * 100).toFixed(2);
                                panelAction.importExcelStep(2, {
                                    start: end
                                });
                            } else {
                                progress = 100;
                                panelAction.importExcelStep(3);
                            }
                        }
                        break;
                    case 3:
                        $form.find('#progressTr').hide();
                        pageAction.init(); //reload page info
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                        break;
                }
                $form.find('#progress').injectData({'progress':progress}); //finish progress
                //$form.find('#progressTr').hide();
            }
        });
    },
    
    /**
     * 匯入EXCEL (確定)
     */
    importExcel: function(data){
        $.ajax({
            handler: panelAction.handler,
            action: 'importExcelCallback', //importExcel
            formId: 'CaseType3Form',
            data: data || {},
            success: function(response){
                $.thickbox.close();
                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                pageAction.init(); //reload page info
            }
        });
    },
    /**
     * 開啟簽報書
     */
    openL120M01A: function(data){
        $('#L120M01AThickBox').thickbox({
            title: i18n.cls1161s01['button.btCaseType0'],
            width: 800,
            height: 400,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    var data = panelAction.L120M01AGrid.getSingleData();
                    if (data) {
                        panelAction.openL140M01A(data);
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
        
        panelAction.caseData = $.extend({}, data);
        panelAction.L120M01AGrid.reload(data);
    },
    /**
     * 開啟整批貸款總額度資訊
     */
    openPTEAMAPP: function(data){
        $('#PTEAMAPPThickBox').thickbox({
            title: i18n.cls1161s01['C160M01A.groupLoanInfo'],
            width: 800,
            height: 400,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var data = panelAction.PTEAMAPPGrid.getSingleData();
                    if (data) {
                        //整批匯入
                        if (panelAction.caseType === '3') {
                            $('#CaseType3Form').find('#approvedNo').html(data.GRPCNTRNO);
                            $.thickbox.close();
                        } else {
                            panelAction.openL140M01A(data);
                        }
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
        
        panelAction.caseData = $.extend({}, data);
        panelAction.PTEAMAPPGrid.reload(data);
    },
    /**
     * 開啟額度明細表
     */
    openL140M01A: function(data){
        //set thick default options
        var options = {
            title: i18n.cls1161s01['button.btCaseType1'],
            width: 800,
            height: 300,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var datas = panelAction.L140M01AGrid.getSelectData('oid');
                    if (datas) {
                        panelAction.sure({
                            caseOids: datas
                        });
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        };
        
        //init
        $('.isUse').hide();
        $.extend(panelAction.caseData, {
            caseMainId: data.mainId || ''
        });
        
        //團貸-顯示 已動用,未動用
        if (panelAction.caseType == '2') {
            $('.isUse').show();
            $.extend(panelAction.caseData, data); //set data
            $.extend(options, {
                height: 560
            });
            $.extend(options.buttons, {
                'sure': function(){
                    var cntrNo = '';
                    var args = [], args2 = [];
                    var data = $.extend(panelAction.L140M01AGrid.getSelRowDatas(), panelAction.L140M01AGrid2.getSelRowDatas());
                    if (data) {
                        for (var o in data) {
                            var col = data[o];
                            args.push(col['oid'] || '');
                            var value = col['cntrNo'] || '';
                            if ($.inArray(value, args2) == -1) {
                                args2.push(value);
                            } else {
                                cntrNo = value;
                            }
                        }
                    }
                    if (args.length) {
                        if (cntrNo) {
                            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.cls1161s01['C160S01D.cntrNo'] + ' [' + cntrNo + '] ' + i18n.cls1161s01['C160S01D.checkRepeat']);
                        } else {
                            panelAction.sure({
                                caseOids: args
                            });
                        }
                    } else {
                        MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['action_005']);
                    }
                }
            });
        }
        $('#L140M01AThickBox').thickbox(options);
        
        //grid load data
        panelAction.L140M01AGrid.reload($.extend(data, {
            caseType: panelAction.caseType,
            isUse: 'Y'
        }));
        if (panelAction.caseType == '2') {
            panelAction.L140M01AGrid2.reload($.extend(data, {
                caseType: panelAction.caseType,
                isUse: 'N'
            }));
        }
    },
    /**
     * 額度明細表-確定
     */
    sure: function(data){
        MegaApi.confirmMessage(i18n.def["actoin_001"], function(action){
            if (action) {
                $.ajax({
                    handler: panelAction.handler,
                    action: 'importCase',
                    data: $.extend(panelAction.caseData, data),
                    success: function(response){
                        $.thickbox.close();
                        $.thickbox.close();
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                        pageAction.init();
                    }
                });
            }
        });
    },
    /**
     * 給號
     */
    getNumber: function(){
		 $('#GetNumberBox').thickbox({
            title: i18n.cls1161s01['C160M01A.messege1'],
            width: 350,
            height: 100,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    if($('input:radio:checked[name="GetNumber"]').val()==1){
                        $.ajax({
                            handler: panelAction.handler,
                            action: 'getNumber',
                            formId: 'C160M01AForm',
                            data: {
								 approvedNo: $("#approvedNo").val()
							},
                            success: function(response){
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                                $('#packNo').html(encodeURI(response.number || ''));
                            }
                        });
						$.thickbox.close();
					}else{
                        if ($("#GetNumberForm").valid()) {
                        	//J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
							$('#packNo').html(DOMPurify.sanitize($("#SetpackNo").val()));
                            $.ajax({
                                handler: panelAction.handler,
                                action: 'setNumber',
                                formId: 'C160M01AForm',
                                data: {},
                                success: function(response){
                                    MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def['runSuccess']);
                                }
                            });
                            $.thickbox.close();
                        }
					}
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
      
    }
};

/**
 * 動審表選擇額度明細表時如果皆沒有符合的額度明細表請顯示一下->該簽報書項下並未有可動用之額度明細表
 */
var checkGrid = {
		finish1 : false,
		finish2 : false,
		check : function(){
			try{
				if (panelAction.L140M01AGrid && panelAction.L140M01AGrid2){
					var check = false;
					if (panelAction.caseType == '2'){
						if (checkGrid.finish1 && checkGrid.finish2) check = true;
					}else{
						if (checkGrid.finish1) check = true;
					}
                    // grid.message1=該簽報書項下並未有可動用之額度明細表。<br/>或該所選的簽報書項下的額度明細表為整批團貸分戶案。
					if (check){
						if (panelAction.L140M01AGrid.records() == 0
								&& panelAction.L140M01AGrid2.records() == 0){
								MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.cls1161s01['grid.message1']);
								checkGrid.finish1 = false;
								checkGrid.finish2 = false;
						}
					}
				}
			}catch(e){
				
			}
		}
};

$(function(){
    panelAction.build();
    panelAction.init();
});
