/* 
 * STUDATA.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 政策性留學生資料 **/
public class STUDATA extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 分行代號 **/
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(03)",unique = true)
	private String branch;

	/** 申貸人身分證字號 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String custid;

	/** 申貸人重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupno;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 
	 * 通知事項<p/>
	 * 1.新送保<br/>
	 *  9.更正資料<br/>
	 *  S.已報送中小信保
	 */
	@Column(name="TYPE", length=1, columnDefinition="CHAR(1)")
	private String type;

	/** 學生身分證字號 **/
	@Column(name="STUID", length=10, columnDefinition="CHAR(10)")
	private String stuid;

	/** 學生重複序號 **/
	@Column(name="STUDUPNO", length=1, columnDefinition="CHAR(01)")
	private String studupno;

	/** 家庭所得類別 **/
	@Column(name="ICMTYPE", length=1, columnDefinition="CHAR(01)")
	private String icmtype;

	/** 學生戶籍地址郵遞區號 **/
	@Column(name="ZIP", length=3, columnDefinition="CHAR(03)")
	private String zip;

	/** 學生戶籍地址 **/
	@Column(name="ADDR1", length=80, columnDefinition="CHAR(80)")
	private String addr1;

	/** 國內最高學歷代碼 **/
	@Column(name="GRATSCHL", length=6, columnDefinition="CHAR(06)")
	private String gratschl;

	/** 畢業科系代碼 **/
	@Column(name="GRATDEP", length=6, columnDefinition="CHAR(06) ")
	private String gratdep;

	/** 出國地區 **/
	@Column(name="SCHLLNO", length=2, columnDefinition="CHAR(02) ")
	private String schllno;

	/** 就讀學校名稱 **/
	@Column(name="SCHLNM", length=80, columnDefinition="CHAR(80)")
	private String schlnm;

	/** 就讀科系代碼 **/
	@Column(name="DEPTNO", length=6, columnDefinition="CHAR(06)")
	private String deptno;

	/** 
	 * 教育階段<p/>
	 * 1.博士(碩士無借款) <br/>
	 *  2.博士(碩士有借款) <br/>
	 *  3.碩士 <br/>
	 *  4.學士直攻博士<br/>
	 *  5.碩士升博士
	 */
	@Column(name="EDUCLS", length=1, columnDefinition="CHAR(01)")
	private String educls;

	/** 借款人與學生關係 **/
	@Column(name="STUREL1", length=1, columnDefinition="CHAR(01)")
	private String sturel1;

	/** 保證人1與學生關係 **/
	@Column(name="STUREL2", length=1, columnDefinition="CHAR(01)")
	private String sturel2;

	/** 保證人2與學生關係 **/
	@Column(name="STUREL3", length=1, columnDefinition="CHAR(01)")
	private String sturel3;

	/** 資料修改(行員代號) **/
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 保證人1姓名 **/
	@Column(name="LNGENM1", length=12, columnDefinition="CHAR(12)")
	private String lngenm1;

	/** 保證人1身分證字號 **/
	@Column(name="LNGEID1", length=10, columnDefinition="CHAR(10)")
	private String lngeid1;

	/** 保證人2姓名 **/
	@Column(name="LNGENM2", length=12, columnDefinition="CHAR(12)")
	private String lngenm2;

	/** 保證人2身分證字號 **/
	@Column(name="LNGEID2", length=10, columnDefinition="CHAR(10)")
	private String lngeid2;

	/** 取得分行代號 **/
	public String getBranch() {
		return this.branch;
	}
	/** 設定分行代號 **/
	public void setBranch(String value) {
		this.branch = value;
	}

	/** 取得申貸人身分證字號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定申貸人身分證字號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得申貸人重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定申貸人重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得通知事項<p/>
	 * 1.新送保<br/>
	 *  9.更正資料<br/>
	 *  S.已報送中小信保
	 */
	public String getType() {
		return this.type;
	}
	/**
	 *  設定通知事項<p/>
	 *  1.新送保<br/>
	 *  9.更正資料<br/>
	 *  S.已報送中小信保
	 **/
	public void setType(String value) {
		this.type = value;
	}

	/** 取得學生身分證字號 **/
	public String getStuid() {
		return this.stuid;
	}
	/** 設定學生身分證字號 **/
	public void setStuid(String value) {
		this.stuid = value;
	}

	/** 取得學生重複序號 **/
	public String getStudupno() {
		return this.studupno;
	}
	/** 設定學生重複序號 **/
	public void setStudupno(String value) {
		this.studupno = value;
	}

	/** 取得家庭所得類別 **/
	public String getIcmtype() {
		return this.icmtype;
	}
	/** 設定家庭所得類別 **/
	public void setIcmtype(String value) {
		this.icmtype = value;
	}

	/** 取得學生戶籍地址郵遞區號 **/
	public String getZip() {
		return this.zip;
	}
	/** 設定學生戶籍地址郵遞區號 **/
	public void setZip(String value) {
		this.zip = value;
	}

	/** 取得學生戶籍地址 **/
	public String getAddr1() {
		return this.addr1;
	}
	/** 設定學生戶籍地址 **/
	public void setAddr1(String value) {
		this.addr1 = value;
	}

	/** 取得國內最高學歷代碼 **/
	public String getGratschl() {
		return this.gratschl;
	}
	/** 設定國內最高學歷代碼 **/
	public void setGratschl(String value) {
		this.gratschl = value;
	}

	/** 取得畢業科系代碼 **/
	public String getGratdep() {
		return this.gratdep;
	}
	/** 設定畢業科系代碼 **/
	public void setGratdep(String value) {
		this.gratdep = value;
	}

	/** 取得出國地區 **/
	public String getSchllno() {
		return this.schllno;
	}
	/** 設定出國地區 **/
	public void setSchllno(String value) {
		this.schllno = value;
	}

	/** 取得就讀學校名稱 **/
	public String getSchlnm() {
		return this.schlnm;
	}
	/** 設定就讀學校名稱 **/
	public void setSchlnm(String value) {
		this.schlnm = value;
	}

	/** 取得就讀科系代碼 **/
	public String getDeptno() {
		return this.deptno;
	}
	/** 設定就讀科系代碼 **/
	public void setDeptno(String value) {
		this.deptno = value;
	}

	/** 
	 * 取得教育階段<p/>
	 * 1.博士(碩士無借款) <br/>
	 *  2.博士(碩士有借款) <br/>
	 *  3.碩士 <br/>
	 *  4.學士直攻博士<br/>
	 *  5.碩士升博士
	 */
	public String getEducls() {
		return this.educls;
	}
	/**
	 *  設定教育階段<p/>
	 *  1.博士(碩士無借款) <br/>
	 *  2.博士(碩士有借款) <br/>
	 *  3.碩士 <br/>
	 *  4.學士直攻博士<br/>
	 *  5.碩士升博士
	 **/
	public void setEducls(String value) {
		this.educls = value;
	}

	/** 取得借款人與學生關係 **/
	public String getSturel1() {
		return this.sturel1;
	}
	/** 設定借款人與學生關係 **/
	public void setSturel1(String value) {
		this.sturel1 = value;
	}

	/** 取得保證人1與學生關係 **/
	public String getSturel2() {
		return this.sturel2;
	}
	/** 設定保證人1與學生關係 **/
	public void setSturel2(String value) {
		this.sturel2 = value;
	}

	/** 取得保證人2與學生關係 **/
	public String getSturel3() {
		return this.sturel3;
	}
	/** 設定保證人2與學生關係 **/
	public void setSturel3(String value) {
		this.sturel3 = value;
	}

	/** 取得資料修改(行員代號) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改(行員代號) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}

	/** 取得保證人1姓名 **/
	public String getLngenm1() {
		return this.lngenm1;
	}
	/** 設定保證人1姓名 **/
	public void setLngenm1(String value) {
		this.lngenm1 = value;
	}

	/** 取得保證人1身分證字號 **/
	public String getLngeid1() {
		return this.lngeid1;
	}
	/** 設定保證人1身分證字號 **/
	public void setLngeid1(String value) {
		this.lngeid1 = value;
	}

	/** 取得保證人2姓名 **/
	public String getLngenm2() {
		return this.lngenm2;
	}
	/** 設定保證人2姓名 **/
	public void setLngenm2(String value) {
		this.lngenm2 = value;
	}

	/** 取得保證人2身分證字號 **/
	public String getLngeid2() {
		return this.lngeid2;
	}
	/** 設定保證人2身分證字號 **/
	public void setLngeid2(String value) {
		this.lngeid2 = value;
	}
}
