package com.mega.eloan.lms.base.pages;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.lms.base.service.CLSDataConvertService;

@Controller
@RequestMapping("/clsdataconvertpage")
public class CLSDataConvertPage extends AbstractOutputPage {

	private static Logger logger = LoggerFactory
			.getLogger(CLSDataConvertPage.class);

	@Autowired
	CLSDataConvertService clsDataConvertService;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {
		String action = params.getString("action", "doDC");

		final String[] l120m01aMainIds;

		String notesMainIds = params.getString("notesMainIDs", "");
		
		logger.info("notes傳入mainId ：" + notesMainIds);

		if ("".equals(notesMainIds)) {
			l120m01aMainIds = null;
			// l120m01aMainIds = new String[] {
			// "7CC1369321BF853748257B470022A5C6",
			// "1257C9AE29AF00E5482579990010EF22",
			// "FD7BFBE5B8A2BA3148257B56001E8411",
			// "046CD90F6DE66AE5482575390034D000",
			// "A3DB4C95F09C9CE448257B65001ED383",
			// "D14084B4EF7B475F48257B40002EB726",
			// "F6C9A34DE7B72E4648257B5800388FF9",
			// "30D0D61B61286FF5482579D700203585",
			// "AC7A3C5B00016C3B48257B48003A76EA",
			// "AFD14E75EAECF840482579C200349A69",
			// "098B05C1DDD2CF2848257B64001F32A3" };
		} else {
			List<String> array = new ArrayList<String>();
			String[] splitMainIDs = notesMainIds.split("\\^");
			for (String notesMainId : splitMainIDs) {
				if (notesMainId.length() == 32) {
					array.add(notesMainId);
				}
			}
			l120m01aMainIds = array.toArray(new String[array.size()]);
		}

		final String mainId = params.getString("mainId", "N05229");
		final String ip = params.getString("ip", "***************");
		final String schema = params.getString("schema", "CLS");
		final String dbType = params.getString("dbType", "ELOANDB");
		final String brNo = params.getString("brNo", "998");
		final String nsfName = params.getString("nsfName", "EL1CLSB1.NSF");
		final List<String> viewList = new ArrayList<String>();

		String result = "";
		if (action.equals("doDC")) {
			if (!clsDataConvertService.checkIsOK()) {
				return "N(單筆作業轉檔中，請稍後再試)";
			}
			viewList.add("VCLS10105Z");
			viewList.add("VCLS10130");
			viewList.add("VCLS10111");
			viewList.add("VCLS10112");
			
			viewList.add("VCLS00101");
			viewList.add("VCLS10123");
			viewList.add("VCLS09105");
			viewList.add("VCLS00113");

			new Thread() {
				@Override
				public void run() {
					try {

						clsDataConvertService.doDataConvert(nsfName, viewList,
								mainId, ip, schema, dbType, brNo,
								l120m01aMainIds);
					} catch (Exception e) {
						logger.error("[writeRealFile] IOEXCEPTION!!!", e);
					}
				}
			}.start();

			result = "R";

		} else if ("checkDC".equals(action)) {
			return checkDC(mainId);
		}

		return result;
	}

	private String checkDC(String mainId) {
		
		logger.info("notes 消金單筆轉檔:  批號 :" + mainId + " 檢核ok檔開始");		
		String result = "";
		File stateFile = new File("/elnfs/LMS/lmsdc/" + mainId + ".clsState");

		try {
			result = FileUtils.readFileToString(stateFile);
		} catch (IOException e) {
			logger.error("Exception : ", e);
			result = "E";
		}
		logger.info("notes 消金單筆轉檔:  批號 :" + mainId + " 檢核ok　結束 , result : " + result);
		
		return result;
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}

}
