/* 
 * C900M01M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.lms.validation.group.Check;

/** 分行可分案人員檔 **/
@Entity
@Table(name = "C900M01N", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C900M01N extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 分行代號
	 * <p/>
	 * COM.BELSBRN.brNo
	 */
	@Size(max = 3)
	@Column(name = "BRNO", length = 3, columnDefinition = "VARCHAR(3)")
	private String brNo;

	/** 行員代號 **/
	@Size(max = 6)
	@Column(name = "ASSIGNEMPNO", length = 6, columnDefinition = "VARCHAR(6)")
	private String assignEmpNo;

	/** 派案順序 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "ASSIGNORDER", columnDefinition = "DECIMAL(5,0)")
	private Integer assignOrder;

	/** 最後派案時間 **/
	@Column(name = "LASTASSIGNTIME", columnDefinition = "TIMESTAMP")
	private Timestamp lastAssignTime;

	/** 執行派案甲級代號 **/
	@Size(max = 6)
	@Column(name = "ASSIGNBOSS", length = 6, columnDefinition = "VARCHAR(6)")
	private String assignBoss;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 處理分案用的欄位，本次案件為行銷人員的案件數 **/
	@Transient
	private Integer marketingCount;

	/** 處理分案用的欄位，被分配到的案件數 **/
	@Transient
	private Integer shouldCount;

	/** 處理分案用的欄位，可被分案的最大數量 **/
	@Transient
	private Integer maxCount;

	/** 處理分案用的欄位，已被分案的數量 **/
	@Transient
	private Integer haveCount;

	/** 處理分案用的欄位，剛好分到自己為行銷人員的案件 **/
	@Transient
	private List<C122M01A> marketingC122m01aList;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}

	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得分行代號
	 * <p/>
	 * COM.BELSBRN.brNo
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 設定分行代號
	 * <p/>
	 * COM.BELSBRN.brNo
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/** 取得行員代號 **/
	public String getAssignEmpNo() {
		return this.assignEmpNo;
	}

	/** 設定行員代號 **/
	public void setAssignEmpNo(String value) {
		this.assignEmpNo = value;
	}

	/** 取得派案順序 **/
	public Integer getAssignOrder() {
		return this.assignOrder;
	}

	/** 設定派案順序 **/
	public void setAssignOrder(Integer value) {
		this.assignOrder = value;
	}

	/** 取得最後派案時間 **/
	public Timestamp getLastAssignTime() {
		return this.lastAssignTime;
	}

	/** 設定最後派案時間 **/
	public void setLastAssignTime(Timestamp value) {
		this.lastAssignTime = value;
	}

	/** 取得執行派案甲級代號 **/
	public String getAssignBoss() {
		return this.assignBoss;
	}

	/** 設定執行派案甲級代號 **/
	public void setAssignBoss(String value) {
		this.assignBoss = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public Integer getMarketingCount() {
		return marketingCount;
	}

	public void setMarketingCount(Integer marketingCount) {
		this.marketingCount = marketingCount;
	}

	public Integer getShouldCount() {
		return shouldCount;
	}

	public void setShouldCount(Integer shouldCount) {
		this.shouldCount = shouldCount;
	}

	public Integer getMaxCount() {
		return maxCount;
	}

	public void setMaxCount(Integer maxCount) {
		this.maxCount = maxCount;
	}

	public Integer getHaveCount() {
		return haveCount;
	}

	public void setHaveCount(Integer haveCount) {
		this.haveCount = haveCount;
	}

	public List<C122M01A> getMarketingC122m01aList() {
		return marketingC122m01aList;
	}

	public void setMarketingC122m01aList(List<C122M01A> marketingC122m01aList) {
		this.marketingC122m01aList = marketingC122m01aList;
	}

	
}
