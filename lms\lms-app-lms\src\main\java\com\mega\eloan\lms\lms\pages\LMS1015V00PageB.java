
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.L120S01MPanel;
import com.mega.eloan.lms.lms.panels.LMS1015S02PanelB1;
import com.mega.eloan.lms.lms.panels.LMS1115S02PanelB5;

/**
 * <pre>
 * 消金信用評等模型
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1015v00b")
public class LMS1015V00PageB extends AbstractOverSeaCLSPage {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		renderJsI18N(AbstractOverSeaCLSPage.class);

		model.addAttribute("_PanelB1_visible", true);
		new LMS1015S02PanelB1("PanelB1", true).processPanelData(model, params);

		model.addAttribute("_PanelB5_visible", true);
		new LMS1115S02PanelB5("PanelB5");

		Panel panel = new L120S01MPanel("l120s01mPanel");
		panel.processPanelData(model, params);
	}
	
	@Override
	public String getViewName() {
	    return "common/pages/None";
	}
}
