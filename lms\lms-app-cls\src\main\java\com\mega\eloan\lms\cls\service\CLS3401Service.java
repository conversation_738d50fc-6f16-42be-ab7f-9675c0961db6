package com.mega.eloan.lms.cls.service;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;

public interface CLS3401Service {

	//public void flowAction(String mainOid, C124M01A model, boolean setResult, boolean resultType, boolean upMis)
	// throws Throwable;

	/**
	 * 對個作廢
	 *
	 * @param c340M01A 主檔
	 */
	void inValidOnlineCtr(C340M01A c340M01A) throws CapMessageException;

	/**
	 * 儲存
	 *
	 * @param meta 主檔
	 * @throws CapMessageException
	 */
	void saveMeta(C340M01A meta) throws CapMessageException;

	/**
	 * tempSave 儲存
	 *
	 * @param meta 主檔
	 */
	void saveTemporaryMeta(C340M01A meta);

	/**
	 * 抓取在途未完成的線上對保的文件
	 *
	 * @param cntrNo 額度序號
	 * @return
	 */
	List<C340M01A> findFlowingC340M01A(String cntrNo, String ctrType);

	/**
	 * 產生對保資料
	 *
	 * @param c340m01a   主檔
	 * @param caseMainId 簽報書mainId
	 * @param tabMainIds 額度序號mainId
	 * @throws CapMessageException
	 */
	void init_C340RelateCtrTypeA(C340M01A c340m01a, String caseMainId, String tabMainIds) throws CapMessageException;

	void init_C340RelateCtrTypeB(C340M01A c340m01a, String caseMainId, String tabMainIds) throws CapException;

	void init_C340RelateCtrTypeS(C340M01A c340m01a, String caseMainId, String tabMainIds) throws CapMessageException;
	
	void init_C340RelateCtrTypeL(C340M01A c340m01a, String caseMainId, String tabMainIds) throws CapMessageException;
		
	public C340M01A sys_create_C340RelateCtrTypeL(String tabMainId, String c340m01a_docStatus, String creator, String txCode) throws CapMessageException;
	
	public C340M01A init_deactive_C340_ctrType_C(String c340m01a_mainId, C160S01D c160s01d, C122M01A c122m01a, C122M01E c122m01e, String docUrl);
	
	public C340M01A save_C340_ctrType_C(C340M01A c340m01a, byte[] pdf_contract, byte[] pdf_deductWageAgrmt);
	
	public byte[] get_C340_ctrType_C_merge_Nto1_pdf(C340M01A c340m01a);
	
	public String get_ploanPosSId_name(C340M01A meta);
	
	/**
	 * 案件流程
	 *
	 * @param meta       主檔
	 * @param setResult  是否有決策
	 * @param resultType 決策值
	 * @throws Throwable
	 */
	void flowAction(C340M01A meta, boolean setResult, String resultType) throws Throwable;

	public String getTipsOfComparingLoanAmountAndApprovedQuota(String c340m01a_mainId, String chineseLoanAmount);

	void init_C340M01ARelateCtrTypeA(C340M01A c340m01a,String custId,String dupNo,String ctrType,String caseMainId,String tabMainId,String UnitNo,String UserId,String txCode,String docUrl)throws CapMessageException;
	void init_C340M01ARelateCtrTypeB(C340M01A c340m01a,String custId,String dupNo,String ctrType,String caseMainId,String tabMainId,String UnitNo,String UserId,String txCode,String docUrl)throws CapMessageException;
	public String get_latest_rptId(String ctrType);
	void check_CtrTypeA(C340M01A c340m01a, String userId, String decisionExpr,
			Properties prop_cls3401m02, Properties prop_abstractEloanPage)
			throws Throwable;
	void check_CtrTypeB(C340M01A c340m01a, String userId, String decisionExpr,
			Properties prop_cls3401m02, Properties prop_abstractEloanPage)
			throws Throwable;
	void updateDocLogUser(String oid, String userId);
	public List<C340M01A> findByPloanCtrBegDate_ctrTypeC(String ploanCtrBegDate);
	public List<C340M01A> findFlowingC340M01AComplete(String cntrNo, String ctrType);
	public List<C340M01A> findC340M01A_ploanCtrNo_ctrType(String ploanCtrNo, String ctrType);
	public String[] getHouseLoanParams();
	public List<Map<String, Object>> find_ploanAcct(String custId);
	public Map<String, Object> findODS_CMFAUDAC_ByAcc(String brNo,String acct);
}
