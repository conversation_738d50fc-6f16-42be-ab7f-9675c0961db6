/* 
 * L160M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L160M01C;

/** 動審表查核項目資料 **/
@Repository
public class L160M01CDaoImpl extends LMSJpaDao<L160M01C, String> implements
		L160M01CDao {

	@Override
	public L160M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L160M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		LinkedHashMap<String, Boolean> printSeqMap = new LinkedHashMap<String, Boolean>();
		printSeqMap.put("itemType", false);
		printSeqMap.put("itemSeq", false);
		search.setOrderBy(printSeqMap);
		List<L160M01C> list = createQuery(L160M01C.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L160M01C findByUniqueKey(String mainId, String itemType,
			Integer itemSeq) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq", itemSeq);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L160M01C> findByIndex01(String mainId, String itemType,
			Integer itemSeq) {
		ISearch search = createSearchTemplete();
		List<L160M01C> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		if (itemSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq",
					itemSeq);
		// 檢查是否有查詢參數
		search.addOrderBy("itemSeq");
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L160M01C.class, search).getResultList();
		}
		return list;
	}
}