$(document).ready(function(){
    var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;

    var grid = $("#gridview").iGrid({
        handler: 'lms1805gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        sortname: 'dataDate',
        postData: {
            docStatus: viewstatus,
            formAction: "query"
        },
        multiselect: true,
        colModel: [{
            colHeader: i18n.lms1805v01['L180M01A.dataDate'],//"資料年月",
            name: 'dataDate',
            align: "center",
            width: 75,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
            name: 'branchId',
            width: 100,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1805v01['L180M01A.generateDate'],//"名單產生日期",
            name: 'generateDate',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1805v01['L180M01A.batchNO'],//"批號",
            name: 'batchNO',
            align: "center",
            width: 50,
            formatter: addZero,
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.creator'],//"名單產生人員",
            name: 'creator',
            align: "center",
            width: 125,
            sortable: true
        }, {
            colHeader: i18n.lms1805v01['L180M01A.defaultCTLDate'],//"預計覆審日",
            name: 'defaultCTLDate',
            align: "center",
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms1805v01['L180M01A.createBy'],//"產生方式",
            name: 'createBy',
            align: "center",
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.lastUpdater'],//"最後異動人員",
            name: 'updater',
            align: "center",
            width: 80,
            sortable: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '../lrs/lms1805m01/01',
            data: {
                formAction: "query",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                branchId: rowObject.branchId,
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    };
    
    if (viewstatus == "010") {
        $.ajax({
            type: "POST",
            handler: "lms1805formhandler",
            data: {
                formAction: "queryBranch"
            },
            success: function(obj){
                $("#order").setItems({
                    item: obj.item,
                    space: false,
                    format: "{value} {key}"
                });
                $("#orderBox").setItems({
                    item: obj.item,
                    format: "{value} {key}",
                    size: 1
                });                
                $("[name=branchList]").each(function(k, v){
                    var val = DOMPurify.sanitize($(this).val());                       
                    $(this).parent().parent().after("<td><input type='text' size='7' maxlength='7' id='branch" + val + "' name='branchDate' />(YYYY-MM)</td>");
                });
            }
        });
    }
    // 整批日期覆蓋
    $('#changeDate').click(function coverDate(){
        var date = $('#totalDataDate').val();
        $("input[name='branchDate']").val(date);
    })
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = new Array();
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list[i] = data.oid;
            }
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1805formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteList",
                        //							txCode : responseJSON.txCode,
                        list: list
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        })
    }).end().find("#btnProduceList").click(function(){
        $("#tabform").reset();
        var today = new Date();
        var year = today.getFullYear();
        var month = today.getMonth();
        month = month + 1;
        if (month == 0) {
            month = 12;
            year = year - 1;
        }
        if (month < 10) {
            $("#dataDate").val(year + "-0" + month);
        } else {
            $("#dataDate").val(year + "-" + month);
        }
        $('#showgrid22').show()
        $("#printButtonList2").thickbox({
            //'產生覆審名單'
            title: i18n.lms1805v01['produceList'],
            width: 400,
            height: 230,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    var list = "";
                    var sign = ",";
                    if ($("#tabform").valid()) {
                        //多筆產生
                        if ($("input[name='a1']:checked").val() == "2") {
                            $("#lotBanks").thickbox({
                                //'選擇多分行'
                                title: i18n.lms1805v01['chooseBank'],
                                width: 800,
                                height: 500,
                                modal: true,
                                align: 'center',
                                valign: 'bottom',
                                i18n: i18n.def,
                                buttons: {
                                    "sure": function(){
                                        var date = $("#totalDataDate").val();
                                        var obj = {};
                                        var owin = $("[name=branchList]:checked").map(function(){                                        	
                                            return DOMPurify.sanitize($(this).val());
                                        }).toArray();                                        
                                        if (owin == 'undefined' || owin == null || owin == "") {
                                            CommonAPI.showMessage(i18n.lms1805v01["chooseBank"]);
                                            return;
                                        }
                                        for (var o in owin) {
                                            var date = $("#branch" + owin[o]).val();
                                            if (date ? false : true) {
                                                return CommonAPI.showMessage(i18n.lms1805v01["noDataDate"]);
                                            } else if (!date.match(regYYYYMM)) {
                                                //val.date2=日期格式錯誤(YYYY-MM)
                                                return CommonAPI.showMessage(i18n.def["val.date2"]);
                                            } else {
                                                obj[owin[o]] = date;
                                            }
                                        }
                                        $.ajax({
                                            handler: "lms1805formhandler",
                                            type: "POST",
                                            dataType: "json",
                                            data: {
                                                formAction: "produceMany",
                                                list: JSON.stringify(obj)
                                                //list: JSON.stringify(owin)
                                            },
                                            success: function(obj){
                                                if (!obj.NOTIFY_MESSAGE) {
                                                    $.thickbox.close();
                                                    $.thickbox.close();
                                                    CommonAPI.showMessage(i18n.def["runSuccess"]);
                                                }
                                                $("#gridview").trigger("reloadGrid");
                                            }
                                        });
                                        
                                    },
                                    "cancel": function(){
                                        $.thickbox.close();
                                    }
                                }
                            });
                        }//單筆產生
 else if ($("input[name='a1']:checked").val() == "1") {
                            list = $("#order").find("option:selected").val();
                            $.ajax({
                                handler: "lms1805formhandler",
                                type: "POST",
                                dataType: "json",
                                data: {
                                    formAction: "produce",
                                    basedate: $("#dataDate").val(),
                                    /*txCode : responseJSON.txCode,*/
                                    check: "N",
                                    list: list,
                                    sign: sign
                                },
                                success: function(obj){
                                    if (obj.check == "N") {
                                        if (obj.mag) {
                                            CommonAPI.showMessage(obj.mag);
                                        } else {
                                            CommonAPI.confirmMessage(i18n.lms1805v01["L180M01A.message1"], function(b){
                                                if (b) {
                                                    $.ajax({
                                                        handler: "lms1805formhandler",
                                                        type: "POST",
                                                        dataType: "json",
                                                        data: {
                                                            formAction: "produce",
                                                            basedate: $("#dataDate").val(),
                                                            /*txCode : responseJSON.txCode,*/
                                                            check: "Y",
                                                            list: list,
                                                            sign: sign
                                                        },
                                                        success: function(responseData){
                                                            $("#gridview").trigger("reloadGrid");
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    } else {
                                        $("#gridview").trigger("reloadGrid");
                                    }
                                }
                            });
                            $.thickbox.close();
                        }
                    } else {
                        return;
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnFilter").click(function(){
        thickBoxFilter();
    }).end().find("#btnProduceExcel").click(function(){
        $("input[name=radio]").removeAttr('checked');
        $('#data').reset();
        $('#data').hide();
        $("#openbox2").thickbox({
            //'產生EXCEL'
            title: i18n.abstracteloan['button.ProduceExcel'],
            width: 350,
            height: 150,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var mode = $("input[name=radio]:checked").val();
                    if (mode == 0) {
                        var rows = $("#gridview").getGridParam('selrow');
                        var list = "";
                        if (rows != 'undefined' && rows != null && rows != 0) {
                            var data = $("#gridview").getRowData(rows);
                            list = data.oid;
                        }
                        if (list == "") {
                            $.thickbox.close();
                            return CommonAPI.showMessage(i18n.def["grid_selector"]);
                        }
                        $.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "produceExcel",
                                oid: list
                            },
                            success: function(obj){
                                //alert(obj.xlsOid);
                                var oid = obj.xlsOid
                                if (oid != "") {
                                
                                }
                                $("#gridview").trigger("reloadGrid");
                            }
                        });
                        $.thickbox.close();
                    } else if (mode == 1) {
                        var rows = $("#gridview").getGridParam('selrow');
                        var list = "";
                        if (rows != 'undefined' && rows != null && rows != 0) {
                            var data = $("#gridview").getRowData(rows);
                            list = data.oid;
                        }
                        if (list == "") {
                            return CommonAPI.showMessage(i18n.def["grid_selector"]);
                        }
                        $.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "produceChkExcel",
                                oid: list
                            },
                            success: function(obj){
                                $("#gridview").trigger("reloadGrid");
                            }
                        });
                        $.thickbox.close();
                    } else if (mode == 2) {
                        if (!$("#dataDateExcel").val().match(regYYYYMM)) {
                            //val.date2=日期格式錯誤(YYYY-MM)
                            return CommonAPI.showMessage(i18n.def["val.date2"]);
                        }
                        $.form.submit({
                            url: "../simple/FileProcessingService",
                            target: "_blank",
                            data: {
                                oid: list,
                                dataDate: $("#dataDateExcel").val(),
                                fileDownloadName: "LMS1805PreList.xls",
                                serviceName: "lms1805xlsservice"
                            }
                        });
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnMaintain").click(function(){
        $("#cover").thickbox({
            //'覆審控制檔維護'
            title: i18n.abstracteloan['button.Maintain'],
            width: 450,
            height: 200,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("[name=radioA]:checked").val() == 0) {
                        //檢查是否更新完畢
                        $.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "checkUpdate"
                                //									dataDate : "2010-10"
                            },
                            success: function(obj){
                                //									CommonAPI.showMessage(i18n.lms1805v01["checkFinish"]);
                            }
                        });
                    } else {
                        $.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "updateElf412",
                                updateOrNot: $("[name=radioB]:checked").val()
                            },
                            success: function(obj){
                                $("#gridview").trigger("reloadGrid");
                            }
                        });
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnExceptRetrialDate").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = new Array();
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list[i] = data.oid;
            }
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["grid_selector"]);
            return;
        }
        $("#change").thickbox({
            //'修改預計覆審日'
            title: i18n.abstracteloan['button.ExceptRetrialDate'],
            width: 370,
            height: 130,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var changeDate = $("#defaultCTLDate").val();
                    if ($.trim($("#defaultCTLDate").val()) ? false : true) {
                        return CommonAPI.showMessage(i18n.lms1805v01["noDefaultDate"]);
                    }
                    $.ajax({
                        handler: "lms1805formhandler",
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "changeDefaultDate",
                            defaultCTLDate: changeDate,
                            /*txCode : responseJSON.txCode,*/
                            list: list
                        },
                        success: function(obj){
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnSendRetrialReport").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = new Array();
        var ctlDate = '';
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list[i] = data.oid;
                if (ctlDate == '') {
                    ctlDate = data.defaultCTLDate;
                }
            }
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["action_005"]);
            return;
        }
        $("#ctlDate").val(ctlDate);
        $("#openbox4").thickbox({
            //'傳送分行覆審報告表'
            title: i18n.abstracteloan['button.SendRetrialReport'],
            width: 500,
            height: 200,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.ajax({
                        type: "POST",
                        handler: "lms1805formhandler",
                        data: {
                            formAction: "sendReport",
                            mainId: list,
                            ctlDate: $("#ctlDate").val(),
                            accountYN: $("input[name=radio]:checked").val()
                        },
                        success: function(){
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnAllSend").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var list = new Array();
        for (var i = 0; i < rows.length; i++) {
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridview").getRowData(rows[i]);
                list[i] = data.oid;
            }
        }
        if (list == "") {
            CommonAPI.showMessage(i18n.def["grid_selector"]);
            return;
        }
        $("#flowBox").thickbox({
            //整批覆核
            title: i18n.abstracteloan['button.send'],
            width: 300,
            height: 110,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var apply = $("input[name=flow]:checked").val();
                    if (!apply) {
                        return;
                    }
                    var flow = false;
                    if ("Y" == apply) {
                        flow = true;
                    }
                    $.ajax({
                        handler: "lms1805formhandler",
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "flowCases",
                            list: list,
                            result: flow
                        },
                        success: function(obj){
                            $("#gridview").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
    
    function thickBoxFilter(){
        if ($("#brIdFilter option").length == 0) {
            $("#brIdFilter").setItems({
                item: getBranchList(),
                space: false,
                format: "{value} {key}"
            });
        }
        $("#filterForm").reset();
        var test2 = $("#lms180Filter").thickbox({ // '新增覆審報告表',
            title: i18n.lms1805v01['L180M01a.fileter'],
            width: 450,
            height: 180,
            align: 'center',
            valign: 'bottom',
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("#filterForm").valid()) {
                        if ($("#dataDateFilter").val() == '' && $("#custIdFilter").val() == '') {
                            return CommonAPI.showMessage(i18n.lms1805v01["L180M01a.error1"]);
                        } else if (!$("#dataDateFilter").val().match(regYYYYMM)) {
                            //val.date2=日期格式錯誤(YYYY-MM)
                            return CommonAPI.showMessage(i18n.def["val.date2"]);
                        } else {
                            $("#gridview").jqGrid("setGridParam", {
                                postData: {
                                    dataDate: $("#dataDateFilter").val(),
                                    custId: $("#custIdFilter").val(),
                                    brId: $("#brIdFilter").val()
                                },
                    			page : 1,
                    			//gridPage : 1,
                                search: true
                            }).trigger("reloadGrid");
                            $.thickbox.close();
                        }
                        
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
});
/**
 * 取得分行列表
 */
function getBranchList(){
    var branch = {};
    $.ajax({
        type: "POST",
        async: false,
        handler: "lms1805formhandler",
        data: {
            formAction: "queryBranch"
        },
        success: function(obj){
            branch = obj.item;
        }
    });
    return branch;
}

function addZero(cellvalue, otions, rowObject){
    var itemName = '';
    if (cellvalue) {
        itemName = '000' + cellvalue;
        itemName = itemName.substr(itemName.length - 3, 3);
    }
    return itemName;
}
