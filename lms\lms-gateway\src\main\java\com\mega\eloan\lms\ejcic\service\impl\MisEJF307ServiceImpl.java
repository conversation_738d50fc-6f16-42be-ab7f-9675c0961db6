/* 
 * MisEJF307ServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.ejcic.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.ejcic.service.MisEJF307Service;

/**
 * <pre>
 * MIS.ACM009->MIS.EJV30702->MIS.EJF307個人任職董監事資料
 * </pre>
 * @since 2012/03/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/03/23,TimChiang,new
 *          </ul>
 */
@Service
public class MisEJF307ServiceImpl extends AbstractEjcicJdbc implements MisEJF307Service {

	@Override
	public List<Map<String, Object>> findGuarantorBusiness(String id, String prodId, String statusCode, String codeType) {
		return getJdbc().queryForList("ACM009.findGuarantorBusiness", new String[]{id, prodId, statusCode, codeType});
	}
//
//	@Override
//	public Map<String, Object> findCorpInfoOfDirectorSupervisor(String id) {
//		return getJdbc().queryForMap("ACM009.findCorpInfoOfDirectorSupervisor", new String[]{id});
//	}
//
//	@Override
//	public List<Map<String, Object>> findCorpsInfo(String id) {
//		return getJdbc().queryForList("ACM009.findCorpInfo", new String[]{id});
//	}
}
