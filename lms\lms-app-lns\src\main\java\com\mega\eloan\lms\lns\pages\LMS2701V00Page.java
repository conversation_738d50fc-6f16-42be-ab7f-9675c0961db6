/* 
 * LMS2701V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <pre>
 * 企金信保
 * 信保基金送保查詢
 * </pre>
 * 
 * @since 2021/6/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/1,009301,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms2701v00")
public class LMS2701V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		addToButtonPanel(model, LmsButtonEnum.View);
		renderJsI18N(LMS2701V00Page.class);
		
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS2701V00Page');");
	}

}
