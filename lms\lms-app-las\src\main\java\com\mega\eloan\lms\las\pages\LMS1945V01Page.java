package com.mega.eloan.lms.las.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import com.iisigroup.cap.component.PageParameters;

import tw.com.jcs.auth.AuthType;

import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.LasDocStatusEnum;
import com.mega.eloan.lms.las.panels.LASCOMMON01Panel;

/**
 * 分行 稽核工作底稿 編製中(當月)
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1945v01")
public class LMS1945V01Page extends AbstractEloanInnerView {

	public LMS1945V01Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(LasDocStatusEnum.分行_編製中);
		// 加上Button

		// 假如主管權限的話，如果沒有編輯權限，那麼打開文件就不要lock，這個還要配合lms1945v01.js一起處理
		boolean authModify = this.getAuth(AuthType.Modify);
		setJavaScriptVar("noOpenDoc", authModify ? "N" : "Y");
				
		List<EloanPageFragment> list = new ArrayList<>();
		if(authModify) {
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Delete);
			list.add(LmsButtonEnum.ToReviewAll);
		}
		
		list.add(LmsButtonEnum.Filter);
		list.add(LmsButtonEnum.PrintAllAudit);
		list.add(LmsButtonEnum.PrintAllBill);
		
//		UPGRADE：
//		TRANSACTION_CODE在已註解的原LasButtonPanel中，雖引入但並未影響邏輯
//		下列model.put()僅留著以免日後需要TRANSACTION_CODE做邏輯調整用
//		model.put("transactionCode", params.getString(EloanConstants.TRANSACTION_CODE));
		addToButtonPanel(model, list);

		renderJsI18N(LMS1945V01Page.class);
		renderJsI18N(LMS1935V01Page.class, "lms1935.016");
		//renderJsI18N(AbstractEloanPage.class);
		//EFD訊息移至DB
		renderRespMsgJsI18N("EFD0005");  
		
		setupIPanel(new LASCOMMON01Panel(PANEL_ID), model, params);	
	}
//UPGRADE(Scott)
//	@Override
//	public String[] getJavascriptPath() {
//		return new String[] { "pagejs/las/LMS1945V01.js",
//				"pagejs/las/LASCOMMON.js" };
//	}

}
