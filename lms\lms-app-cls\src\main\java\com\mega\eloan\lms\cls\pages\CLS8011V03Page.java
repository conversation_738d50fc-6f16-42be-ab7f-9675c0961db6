package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

/**
 * <pre>
 * 個金個人資料清冊
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *          
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls8011v03")
public class CLS8011V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.BackDoc,
				LmsButtonEnum.Print, LmsButtonEnum.CreateExcel);
		// build i18n
		renderJsI18N(CLS8011V01Page.class);

		// UPGRADE: 待確認畫面是否正常
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS8011V01Page');");
	}

}
