/* 
 * L784M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金授信管理報表檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L784M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L784M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 L784A01A．關聯檔
	 * 
	 */
	@ToStringExclude
	@OneToMany(mappedBy = "l784m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L784A01A> l784a01a;

	public Set<L784A01A> getL784a01a() {
		return l784a01a;
	}

	public void setL784a01a(Set<L784A01A> l784a01a) {
		this.l784a01a = l784a01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * uid
	 * <p/>
	 * 如同 notes unique id
	 */
	@Column(name = "UID", length = 32, columnDefinition = "CHAR(32)")
	private String uid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 區部別
	 * <p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/**
	 * 辦理單位類別
	 * <p/>
	 * 1.分行/自辦 2.區域營運中心 3.徵信處
	 */
	@Column(name = "UNITTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String unitType;

	/**
	 * 編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 */
	@Column(name = "OWNBRID", length = 3, columnDefinition = "CHAR(3)")
	private String ownBrId;

	/** 目前文件狀態 **/
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "VARCHAR(3)")
	private String docStatus;

	/** 文件亂碼 **/
	@Column(name = "RANDOMCODE", length = 32, columnDefinition = "CHAR(32)")
	private String randomCode;

	/** 文件URL **/
	@Column(name = "DOCURL", length = 40, columnDefinition = "VARCHAR(40)")
	private String docURL;

	/**
	 * 交易代碼
	 * <p/>
	 * 第1碼為系統碼
	 */
	@Column(name = "TXCODE", length = 6, columnDefinition = "CHAR(6)")
	private String txCode;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 核准人員號碼
	 * <p/>
	 * reCheck
	 */
	@Column(name = "APPROVER", length = 6, columnDefinition = "CHAR(6)")
	private String approver;

	/**
	 * 核准日期
	 * <p/>
	 * sendDateTime
	 */
	@Column(name = "APPROVETIME", columnDefinition = "TIMESTAMP")
	private Date approveTime;

	/**
	 * 是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 */
	@Column(name = "ISCLOSED", length = 1, columnDefinition = "CHAR(1)")
	private String isClosed;

	/**
	 * 刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Date deletedTime;

	/**
	 * 報表種類
	 * <p/>
	 * 1. 已敘做授信案件清單<br/>
	 * (代理程式：ALMS18001)<br/>
	 * 2. 授信契約已逾期控制表<br/>
	 * 3. 信保案件未動用屆期清單<br/>
	 * 4. 授信契約產生主辦聯貸案一覽表(國金部用)<br/>
	 * (Word：LMSDoc7.dot)<br/>
	 * 5. 授信案件統計表(授管處、企劃處用)<br/>
	 * (Excel：無範本檔)<br/>
	 * 6. 營運中心每日授權外授信案件清單<br/>
	 * (代理程式：ALMS38002)<br/>
	 * 7. 常董會及申報案件統計表<br/>
	 * 8. 各級授權範圍內承做授信案件統計表<br/>
	 * 9. 營業單位授信報案考核彙總表<br/>
	 * (Excel：CLSDoc20.xls)
	 */
	@Column(name = "RPTTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String rptType;

	/**
	 * 案件隸屬部門
	 * <p/>
	 * 100/12/26新增<br/>
	 * 企金<br/>
	 * 個金<br/>
	 * 企金+個金<br/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 */
	@Column(name = "CASEDEPT", length = 1, columnDefinition = "CHAR(01)")
	private String caseDept;

	/**
	 * 資料產生日期
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CREATDATE", columnDefinition = "DATE")
	private Date creatDate;

	/**
	 * 資料基準日(起)
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/**
	 * 資料基準日(迄日)
	 * <p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "DATAEDATE", columnDefinition = "DATE")
	private Date dataEDate;

	/** 單位別 **/
	@Column(name = "BRANCHID", length = 3, columnDefinition = "CHAR(03)")
	private String branchId;

	/** 資料筆數 **/
	@Column(name = "RECCOUNT", columnDefinition = "DECIMAL(5,0)")
	private Integer recCount;

	/**
	 * 分行首次送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Column(name = "SENDFIRST", length = 6, columnDefinition = "CHAR(6)")
	private String sendFirst;

	/**
	 * 分行首次送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Column(name = "SENDFIRSTTIME", columnDefinition = "TIMESTAMP")
	private Timestamp sendFirstTime;

	/**
	 * 分行最後送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Column(name = "SENDLAST", length = 6, columnDefinition = "CHAR(6)")
	private String sendLast;

	/**
	 * 分行最後送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Column(name = "SENDLASTTIME", columnDefinition = "TIMESTAMP")
	private Timestamp sendLastTime;

	/**
	 * 營運中心負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單<br/>
	 * L700M01A.userNo
	 */
	@Column(name = "AREAUSERNO", length = 6, columnDefinition = "VARCHAR(6)")
	private String areaUserNo;

	/**
	 * 營運中心放行時間
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 */
	@Column(name = "AREASENDINFO", columnDefinition = "TIMESTAMP")
	private Date areaSendInfo;

	/**
	 * 授管處收件日期
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HQRECEIVEDATE", columnDefinition = "DATE")
	private Date hqReceiveDate;

	/**
	 * 授管處負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * L700M01A.userNo
	 */
	@Column(name = "HQUSERNO", length = 6, columnDefinition = "VARCHAR(6)")
	private String hqUserNo;

	/**
	 * 授管處備查日期
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "HQCHECKDATE", columnDefinition = "DATE")
	private Date hqCheckDate;

	/**
	 * 報表檔檔案位置
	 * <p/>
	 * 101/05/02新增(預留)
	 */
	@Column(name = "REPORTFILE", length = 64, columnDefinition = "VARCHAR(64)")
	private String reportFile;

	/**
	 * RPTID
	 * <p/>
	 * 101/05/02新增(預留)<br/>
	 * 電子表單列印套版版本ID
	 */
	@Column(name = "RPTID", length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得uid
	 * <p/>
	 * 如同 notes unique id
	 */
	public String getUid() {
		return this.uid;
	}

	/**
	 * 設定uid
	 * <p/>
	 * 如同 notes unique id
	 **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得區部別
	 * <p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定區部別
	 * <p/>
	 * 0.無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}

	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/**
	 * 取得辦理單位類別
	 * <p/>
	 * 1.分行/自辦 2.區域營運中心 3.徵信處
	 */
	public String getUnitType() {
		return this.unitType;
	}

	/**
	 * 設定辦理單位類別
	 * <p/>
	 * 1.分行/自辦 2.區域營運中心 3.徵信處
	 **/
	public void setUnitType(String value) {
		this.unitType = value;
	}

	/**
	 * 取得編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 */
	public String getOwnBrId() {
		return this.ownBrId;
	}

	/**
	 * 設定編製單位代號
	 * <p/>
	 * 評等單位/編製單位
	 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得目前文件狀態 **/
	public String getDocStatus() {
		return this.docStatus;
	}

	/** 設定目前文件狀態 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得文件亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}

	/** 設定文件亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得文件URL **/
	public String getDocURL() {
		return this.docURL;
	}

	/** 設定文件URL **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/**
	 * 取得交易代碼
	 * <p/>
	 * 第1碼為系統碼
	 */
	public String getTxCode() {
		return this.txCode;
	}

	/**
	 * 設定交易代碼
	 * <p/>
	 * 第1碼為系統碼
	 **/
	public void setTxCode(String value) {
		this.txCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得核准人員號碼
	 * <p/>
	 * reCheck
	 */
	public String getApprover() {
		return this.approver;
	}

	/**
	 * 設定核准人員號碼
	 * <p/>
	 * reCheck
	 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/**
	 * 取得核准日期
	 * <p/>
	 * sendDateTime
	 */
	public Date getApproveTime() {
		return this.approveTime;
	}

	/**
	 * 設定核准日期
	 * <p/>
	 * sendDateTime
	 **/
	public void setApproveTime(Date value) {
		this.approveTime = value;
	}

	/**
	 * 取得是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 */
	public String getIsClosed() {
		return this.isClosed;
	}

	/**
	 * 設定是否結案
	 * <p/>
	 * Y: 已結案 N: 未結案<br/>
	 * 2011/08/10 新增：文件鎖定檢查時使用
	 **/
	public void setIsClosed(String value) {
		this.isClosed = value;
	}

	/**
	 * 取得刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 */
	public Date getDeletedTime() {
		return this.deletedTime;
	}

	/**
	 * 設定刪除註記
	 * <p/>
	 * 2011/11/08 新增：文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Date value) {
		this.deletedTime = value;
	}

	/**
	 * 取得報表種類
	 * <p/>
	 * 1. 已敘做授信案件清單<br/>
	 * (代理程式：ALMS18001)<br/>
	 * 2. 授信契約已逾期控制表<br/>
	 * 3. 信保案件未動用屆期清單<br/>
	 * 4. 授信契約產生主辦聯貸案一覽表(國金部用)<br/>
	 * (Word：LMSDoc7.dot)<br/>
	 * 5. 授信案件統計表(授管處、企劃處用)<br/>
	 * (Excel：無範本檔)<br/>
	 * 6. 營運中心每日授權外授信案件清單<br/>
	 * (代理程式：ALMS38002)<br/>
	 * 7. 常董會及申報案件統計表<br/>
	 * 8. 各級授權範圍內承做授信案件統計表<br/>
	 * 9. 營業單位授信報案考核彙總表<br/>
	 * (Excel：CLSDoc20.xls)
	 */
	public String getRptType() {
		return this.rptType;
	}

	/**
	 * 設定報表種類
	 * <p/>
	 * 1. 已敘做授信案件清單<br/>
	 * (代理程式：ALMS18001)<br/>
	 * 2. 授信契約已逾期控制表<br/>
	 * 3. 信保案件未動用屆期清單<br/>
	 * 4. 授信契約產生主辦聯貸案一覽表(國金部用)<br/>
	 * (Word：LMSDoc7.dot)<br/>
	 * 5. 授信案件統計表(授管處、企劃處用)<br/>
	 * (Excel：無範本檔)<br/>
	 * 6. 營運中心每日授權外授信案件清單<br/>
	 * (代理程式：ALMS38002)<br/>
	 * 7. 常董會及申報案件統計表<br/>
	 * 8. 各級授權範圍內承做授信案件統計表<br/>
	 * 9. 營業單位授信報案考核彙總表<br/>
	 * (Excel：CLSDoc20.xls)
	 **/
	public void setRptType(String value) {
		this.rptType = value;
	}

	/**
	 * 取得案件隸屬部門
	 * <p/>
	 * 100/12/26新增<br/>
	 * 企金<br/>
	 * 個金<br/>
	 * 企金+個金<br/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 */
	public String getCaseDept() {
		return this.caseDept;
	}

	/**
	 * 設定案件隸屬部門
	 * <p/>
	 * 100/12/26新增<br/>
	 * 企金<br/>
	 * 個金<br/>
	 * 企金+個金<br/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 **/
	public void setCaseDept(String value) {
		this.caseDept = value;
	}

	/**
	 * 取得資料產生日期
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getCreatDate() {
		return this.creatDate;
	}

	/**
	 * 設定資料產生日期
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setCreatDate(Date value) {
		this.creatDate = value;
	}

	/**
	 * 取得資料基準日
	 * <p/>
	 * YYYY-MM-DD
	 */
	public Date getDataDate() {
		return this.dataDate;
	}

	/**
	 * 設定資料基準日
	 * <p/>
	 * YYYY-MM-DD
	 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/** 取得單位別 **/
	public String getBranchId() {
		return this.branchId;
	}

	/** 設定單位別 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得資料筆數 **/
	public Integer getRecCount() {
		return this.recCount;
	}

	/** 設定資料筆數 **/
	public void setRecCount(Integer value) {
		this.recCount = value;
	}

	/**
	 * 取得分行首次送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public String getSendFirst() {
		return this.sendFirst;
	}

	/**
	 * 設定分行首次送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setSendFirst(String value) {
		this.sendFirst = value;
	}

	/**
	 * 取得分行首次送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public Timestamp getSendFirstTime() {
		return this.sendFirstTime;
	}

	/**
	 * 設定分行首次送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setSendFirstTime(Timestamp value) {
		this.sendFirstTime = value;
	}

	/**
	 * 取得分行最後送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public String getSendLast() {
		return this.sendLast;
	}

	/**
	 * 設定分行最後送件人員
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setSendLast(String value) {
		this.sendLast = value;
	}

	/**
	 * 取得分行最後送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public Timestamp getSendLastTime() {
		return this.sendLastTime;
	}

	/**
	 * 設定分行最後送件日
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setSendLastTime(Timestamp value) {
		this.sendLastTime = value;
	}

	/**
	 * 取得營運中心負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單<br/>
	 * L700M01A.userNo
	 */
	public String getAreaUserNo() {
		return this.areaUserNo;
	}

	/**
	 * 設定營運中心負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單<br/>
	 * L700M01A.userNo
	 **/
	public void setAreaUserNo(String value) {
		this.areaUserNo = value;
	}

	/**
	 * 取得營運中心放行時間
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 */
	public Date getAreaSendInfo() {
		return this.areaSendInfo;
	}

	/**
	 * 設定營運中心放行時間
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * ※營運中心每日授權外授信案件清單
	 **/
	public void setAreaSendInfo(Date value) {
		this.areaSendInfo = value;
	}

	/**
	 * 取得授管處收件日期
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public Date getHqReceiveDate() {
		return this.hqReceiveDate;
	}

	/**
	 * 設定授管處收件日期
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setHqReceiveDate(Date value) {
		this.hqReceiveDate = value;
	}

	/**
	 * 取得授管處負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * L700M01A.userNo
	 */
	public String getHqUserNo() {
		return this.hqUserNo;
	}

	/**
	 * 設定授管處負責經辦
	 * <p/>
	 * ※已敘做授信案件清單<br/>
	 * L700M01A.userNo
	 **/
	public void setHqUserNo(String value) {
		this.hqUserNo = value;
	}

	/**
	 * 取得授管處備查日期
	 * <p/>
	 * ※已敘做授信案件清單
	 */
	public Date getHqCheckDate() {
		return this.hqCheckDate;
	}

	/**
	 * 設定授管處備查日期
	 * <p/>
	 * ※已敘做授信案件清單
	 **/
	public void setHqCheckDate(Date value) {
		this.hqCheckDate = value;
	}

	/**
	 * 取得報表檔檔案位置
	 * <p/>
	 * 101/05/02新增(預留)
	 */
	public String getReportFile() {
		return this.reportFile;
	}

	/**
	 * 設定報表檔檔案位置
	 * <p/>
	 * 101/05/02新增(預留)
	 **/
	public void setReportFile(String value) {
		this.reportFile = value;
	}

	/**
	 * 取得RPTID
	 * <p/>
	 * 101/05/02新增(預留)<br/>
	 * 電子表單列印套版版本ID
	 */
	public String getRptId() {
		return this.rptId;
	}

	/**
	 * 設定RPTID
	 * <p/>
	 * 101/05/02新增(預留)<br/>
	 * 電子表單列印套版版本ID
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}

	/**
	 * @param dataEDate the dataEDate to set
	 */
	public void setDataEDate(Date dataEDate) {
		this.dataEDate = dataEDate;
	}

	/**
	 * @return the dataEDate
	 */
	public Date getDataEDate() {
		return dataEDate;
	}
}
