/* 
 * L164S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 動審表借款人基本資料檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L164S01A", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class L164S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	@Size(max = 1)
	@Column(name = "CUSTPOS", length = 1, columnDefinition = "CHAR(1)")
	private String custPos;

	/** 負責人統編 **/
	@Size(max = 10)
	@Column(name = "CHAIRMANID", length = 10, columnDefinition = "VARCHAR(10)")
	private String chairmanId;

	/** 負責人統編重複碼 **/
	@Size(max = 1)
	@Column(name = "CHAIRMANDUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String chairmanDupNo;

	/** 負責人姓名 **/
	@Size(max = 120)
	@Column(name = "CHAIRMAN", length = 120, columnDefinition = "VARCHAR(120)")
	private String chairman;

	/** 實質受益人 **/
	@Size(max = 1800)
	@Column(name = "BENEFICIARY", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String beneficiary;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 */
	@Column(name = "SENIORMGR", columnDefinition = "VARCHAR(1800)")
	private String seniorMgr;

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 */
	@Column(name = "CTRLPEO", columnDefinition = "VARCHAR(1800)")
	private String ctrlPeo;

	/** 是否為我國獨資或合夥企業 **/
	@Size(max = 1)
	@Column(name = "ISSOLE", length = 1, columnDefinition = "CHAR(1)")
	private String isSole;

	/** 企業類別 **/
	@Size(max = 2)
	@Column(name = "SOLETYPE", length = 2, columnDefinition = "CHAR(2)")
	private String soleType;

	/** 是否取得商業登記 **/
	@Size(max = 1)
	@Column(name = "HASREGIS", length = 1, columnDefinition = "CHAR(1)")
	private String hasRegis;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 */
	public String getCustPos() {
		return this.custPos;
	}

	/**
	 * 設定相關身份
	 * <p/>
	 * C.共同借款人<br/>
	 * D.共同發票人(企金)<br/>
	 * G.連帶保證人(個金)<br/>
	 * N.ㄧ般保證人(個金)<br/>
	 * S.擔保品提供人(個金)
	 **/
	public void setCustPos(String value) {
		this.custPos = value;
	}

	/** 取得負責人統編 **/
	public String getChairmanId() {
		return this.chairmanId;
	}

	/** 設定負責人統編 **/
	public void setChairmanId(String value) {
		this.chairmanId = value;
	}

	/** 取得負責人統編重複碼 **/
	public String getChairmanDupNo() {
		return this.chairmanDupNo;
	}

	/** 設定負責人統編重複碼 **/
	public void setChairmanDupNo(String value) {
		this.chairmanDupNo = value;
	}

	/** 取得負責人姓名 **/
	public String getChairman() {
		return this.chairman;
	}

	/** 設定負責人姓名 **/
	public void setChairman(String value) {
		this.chairman = value;
	}

	/** 取得實質受益人 **/
	public String getBeneficiary() {
		return this.beneficiary;
	}

	/** 設定實質受益人 **/
	public void setBeneficiary(String value) {
		this.beneficiary = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 設定高階管理人員
	 * 
	 * @return
	 */
	public void setSeniorMgr(String seniorMgr) {
		this.seniorMgr = seniorMgr;
	}

	/**
	 * 取得高階管理人員
	 * 
	 * @return
	 */
	public String getSeniorMgr() {
		return seniorMgr;
	}

	/**
	 * 設定具控制權人
	 * 
	 * @return
	 */
	public void setCtrlPeo(String ctrlPeo) {
		this.ctrlPeo = ctrlPeo;
	}

	/**
	 * 取得具控制權人
	 * 
	 * @return
	 */
	public String getCtrlPeo() {
		return ctrlPeo;
	}

	public void setIsSole(String isSole) {
		this.isSole = isSole;
	}

	public String getIsSole() {
		return isSole;
	}

	public void setSoleType(String soleType) {
		this.soleType = soleType;
	}

	public String getSoleType() {
		return soleType;
	}

	public void setHasRegis(String hasRegis) {
		this.hasRegis = hasRegis;
	}

	public String getHasRegis() {
		return hasRegis;
	}

}
