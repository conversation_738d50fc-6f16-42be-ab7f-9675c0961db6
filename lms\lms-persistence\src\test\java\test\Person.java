package test;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Future;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import com.mega.eloan.lms.validation.group.Check;

public class Person {

	@NotNull(message="{required.message}")
	private String custid;
	
	@NotNull
	@NotEmpty(groups=Check.class)
    private String name;

	@NotEmpty
    @Min(value=3)
    private String min;
    
    @Size(max=4)
    private String max;
    
    @Min(3)
    private int  numberMin;
    
    public BigDecimal getBgMax() {
		return bgMax;
	}

	public void setBgMax(BigDecimal bgMax) {
		this.bgMax = bgMax;
	}

	@Max(4)
    private int  numberMax;
    
    @Max(9)
    private BigDecimal bgMax;
    
    @Past
    @Future
    private Date date;
    
    @DecimalMin(value="0.5")
    @DecimalMax(value="10.5")
    private BigDecimal decimal;
    
    public Person() {
	}

    public Person(String name) {
        super();
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}

	public String getMax() {
		return max;
	}

	public void setMax(String max) {
		this.max = max;
	}

	public void setNumberMax(int numberMax) {
		this.numberMax = numberMax;
	}

	public int getNumberMax() {
		return numberMax;
	}

	public void setNumberMin(int numberMin) {
		this.numberMin = numberMin;
	}

	public int getNumberMin() {
		return numberMin;
	}

	public void setCustid(String custid) {
		this.custid = custid;
	}

	public String getCustid() {
		return custid;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Date getDate() {
		return date;
	}

	public void setDecimal(BigDecimal decimal) {
		this.decimal = decimal;
	}

	public BigDecimal getDecimal() {
		return decimal;
	}
}