/* 
 * NGFlagHelper.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2013 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.base.common;

import org.apache.commons.lang3.StringUtils;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

/**
 * <pre>
 * 三個月以上協議flag處理Helper
 * </pre>
 * 
 * @since 2013/4/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/4/23,UFO,new
 *          <li>2013/06/14,UFO,增加判斷若原單位為資訊處，則不加NGFLAG條件
 *          <li>2013/06/14,UFO,換入至授處理且原分行為債管理，則回傳主管單位為債管處
 *          </ul>
 */
public class NGFlagHelper {
	public static ISearch addSearchParamsAT918(ISearch pageSetting,
			MegaSSOUserDetails user) {

		// 若使用者登入單位為授管處，則需增加NGFLAG查詢判斷條件
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())) {
			// UFO@********:增加判斷原單位代號=債管處則設查詢條件NGFLAG=2，只可查詢三個月以上協議案
			if (!UtilConstants.BankNo.資訊處.equals(user.getOvUnitNo())) {
				pageSetting.addSearchModeParameters(UtilConstants.BankNo.債管處
						.equals(user.getOvUnitNo()) ? SearchMode.EQUALS
						: SearchMode.NOT_EQUALS, "ngFlag", "2");

			}
		}
		return pageSetting;
	}

	/**
	 * 取得主管清單的所在部門(簽報書登錄使用)
	 * 
	 * @param user
	 *            {@link com.mega.sso.userdetails.MegaSSOUserDetails}
	 * @return
	 */
	public static String getBossDept(MegaSSOUserDetails user) {
		// 換入至授處理且原分行為債管理，則回傳單位為債管處
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& UtilConstants.BankNo.債管處.equals(user.getOvUnitNo())) {
			return UtilConstants.BankNo.債管處;

		}
		return user.getUnitNo();
	}

	/**
	 * 取得主管清單的所在部門
	 * 
	 * @param user
	 *            {@link com.mega.sso.userdetails.MegaSSOUserDetails}
	 * @param defaultUnitNo
	 *            指定變更之單位代號
	 * @return
	 */
	public static String getBossDept(MegaSSOUserDetails user,
			String defaultUnitNo) {
		// 換入至授處理且原分行為債管理，則回傳單位為債管處
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& UtilConstants.BankNo.債管處.equals(user.getOvUnitNo())
				&& UtilConstants.BankNo.授管處.equals(defaultUnitNo)) {
			return UtilConstants.BankNo.債管處;

		}
		return defaultUnitNo;
	}

	public static String getDeptName(MegaSSOUserDetails user,
			BranchService branchService) {
		return getDeptName(user, branchService, null);
	}

	/**
	 * 取得單位名稱
	 * 
	 * @param user
	 *            {@link com.mega.sso.userdetails.MegaSSOUserDetails}
	 * @param branchService
	 *            {@link com.mega.sso.service.BranchService}
	 * @return 單位名稱
	 */
	public static String getDeptName(MegaSSOUserDetails user,
			BranchService branchService, String defaultName) {
		// 換入至授處理且原分行為債管理，則回傳單位為債管處
		String unitNo = null;
		String unitName = null;
		String defName = null;
		if (UtilConstants.BankNo.授管處.equals(user.getUnitNo())
				&& UtilConstants.BankNo.債管處.equals(user.getOvUnitNo())) {
			unitNo = UtilConstants.BankNo.債管處;
			defName = "債權管理處";
		} else {
			if (StringUtils.isEmpty(defaultName)) {
				unitNo = UtilConstants.BankNo.授管處;
				defName = "授信管理處";
			} else {
				// 使用傳入預設值的部門名稱
				unitNo = null;
				defName = defaultName;
			}
		}
		if (branchService != null && unitNo != null) {
			try {
				unitName = branchService.getBranchName(unitNo);
			} catch (Exception ex) {
			}
		}
		if (StringUtils.isEmpty(unitName)) {
			unitName = defName;
		}
		return unitName;
	}
}
