/* 
 *LMS9131ServiceImp.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS9131V00Page;
import com.mega.eloan.lms.fms.service.LMS9131Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01E;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 授信簽報書審核層級整批修改
 * </pre>
 * 
 * @since 2013/01/24
 * <AUTHOR> Chang
 * @version <ul>
 *          <li>2013/01/24,Gary Chang,new
 *          </ul>
 */
/* Use MIS-RDB & R6 */
@Service
public class LMS9131ServiceImpl extends AbstractCapService implements
		LMS9131Service {
	@SuppressWarnings("unused")
	private static final Logger logger = LoggerFactory
			.getLogger(LMS9131ServiceImpl.class);
	private static final String 營運中心_海外聯貸案_會簽中 = "LWC";
	private static final String 營運中心_海外聯貸案_待放行 = "LXC";
	private static final String 營運中心_海外聯貸案_已會簽 = "LYC";
	private static final String _國金部 = "025";

	@Resource
	LMSService lmsservice;
	@Resource
	MisELF447Service misELF447Service;

	@Resource
	BranchService branchService;
	@Resource
	EloandbBASEService r6dbService;
	@Resource
	L120M01ADao l120m01aDao;
	@Resource
	L120M01CDao l120m01cDao;
	@Resource
	L120M01EDao l120m01eDao;
	@Resource
	L120M01FDao l120m01fDao;
	@Resource
	L120S01ADao l120s01aDao;
	@Resource
	L120S01DDao l120s01dDao;
	@Resource
	C120S01EDao c120s01eDao;
	@Resource
	L140M01ADao l140m01aDao;

	@Override
	public List<Map<String, Object>> findLNF447ByBranchIdSysType(
			String branchId, String systype) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		return list;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByOid(oid);
		} else if (clazz == L120M01F.class) {
			return (T) l120m01fDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L120M01F.class) {
			return l120m01fDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void deleteL120m01fs(List<L120M01F> l120m01fs, boolean isAll) {
		if (isAll) {
			l120m01fDao.delete(l120m01fs);
		} else {
			List<L120M01F> l120m01fsOld = new ArrayList<L120M01F>();
			for (L120M01F l120m01f : l120m01fs) {
				String staffJob = l120m01f.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					l120m01fsOld.add(l120m01f);
				}
			}
			l120m01fDao.delete(l120m01fsOld);
		}
	}

	
	@Override
	public void saveL120m01fList(List<L120M01F> list) {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L120M01F l120m01f : list) {
			l120m01f.setUpdater(user.getUserId());
			l120m01f.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l120m01fDao.save(list);
	}

	
	
	@Override
	public void saveL120M01A(L120M01A l120m01a) {
		l120m01aDao.save(l120m01a);
	}
	@Override
	public void ClearL120M01A(String[] oidlist,String flag) throws CapMessageException {
		String error="";
		Properties prop = null;
		prop = MessageBundleScriptCreator
		.getComponentResource(LMS9131V00Page.class);
		for (String oid : oidlist) {
			L120M01A l120m01a = this.findModelByOid(L120M01A.class, oid);
			if(!Util.equals(l120m01a.getReEstFlag(),"Y")){
				l120m01a.setReEstFlag(flag);
				saveL120M01A(l120m01a);
			}else{
				error=error+Util.trim(l120m01a.getCaseNo())+"<br/>";
			}
		}
		if(!Util.isEmpty(error)){
			throw new CapMessageException(prop.getProperty("LMS913V00.error4")+error,L120M01A.class);
		}
	}
	@Override
	public void upLoadELF447(String[] oidlist) throws CapMessageException {
		Properties prop = null;
		prop = MessageBundleScriptCreator
		.getComponentResource(LMS9131V00Page.class);
		List<Object[]> dataList = new ArrayList<Object[]>();
		List<Object[]> deleteList = new ArrayList<Object[]>();
		for (String oid : oidlist) {

			L120M01A l120m01a = this.findModelByOid(L120M01A.class, oid);
			// 文件編號
			String mainId = Util.trim(l120m01a.getMainId());
			// 分行別
			String tBrno = Util.trim(l120m01a.getCaseBrId());
			// 覆核分行
			String tBrno2 = "";
			// 核准完整日期
			String approveDate = CapDate.formatDate(l120m01a.getEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
			// 案號
			String tCaseNo = LMSUtil.getUploadCaseNo(l120m01a, false);

			String caseLvl =l120m01a.getCaseLvl();
			
			// 主要借款人
			String custId = l120m01a.getCustId();

			// 重覆序號
			String dupNo = l120m01a.getDupNo();

			// 銀行法,金控法44,45
			String liHaiBank = "";
			String liHai44 = "";
			String liHai45 = "";
			if ("1".equals(Util.trim(l120m01a.getDocType()))) {
				// 企金
				L120S01D l120s01d = l120s01dDao.findByUniqueKey(mainId, custId,
						dupNo);
				if (l120s01d != null) {
					liHaiBank = Util.trim(l120s01d.getMbRlt());
					liHai44 = Util.trim(l120s01d.getMhRlt44());
					liHai45 = Util.trim(l120s01d.getMhRlt45());
				}
			} else {
				// 個金
				C120S01E l120s01e = c120s01eDao.findByUniqueKey(mainId, custId,
						dupNo);
				if (l120s01e != null) {
					liHaiBank = Util.trim(l120s01e.getIsQdata2());
					liHai44 = Util.trim(l120s01e.getIsQdata3());
					liHai45 = Util.trim(l120s01e.getIsQdata16());
				}
			}

			// 現請額度金額 ELF447_CURAMT currentApplyAmt
			double currentApplyAmt = 0.0;
			// 授信額度合計幣別 ELF447_LTCURR LoanTotCurr
			String LoanTotCurr = "";
			// 授信額度合計金額 ELF447_LOANAMT LoanTotAmt
			double LoanTotAmt = 0.0;
			// 其中擔保合計幣別 ELF447_ATCURR assureTotCurr
			String assureTotCurr = "";
			// 其中擔保合計金額 ELF447_ASSAMT assureTotAmt
			double assureTotAmt = 0.0;
			// 衍生性額度合計幣別 ELF447_LTCURRZ LoanTotZCurr
			String LoanTotZCurr = "";
			// 衍生性額度合計金額 ELF447_LOANAMTZ LoanTotZAmt
			double LoanTotZAmt = 0.0;
			// 衍生性相當授信額度合計幣別 ELF447_LTCURRL LoanTotLCurr
			String LoanTotLCurr = "";
			// 衍生性相當授信額度合計金額 ELF447_LOANAMTL LoanTotLAmt
			double LoanTotLAmt = 0.0;
			// 前准額度幣別 ELF447_LVCURR LVCurr
			String LVCurr = "";
			// 前准額度金額 ELF447_OLDAMT LVAmt
			double LVAmt = 0.0;
			// 現請額度幣別 ELF447_APLCURR currentApplyCurr
			String currentApplyCurr = "";

			// 檢查是否有引進徵信報告
			List<L120M01E> listL120m01e = l120m01eDao.findByMainId(mainId);
			// 存放徵信報告文件編號
			List<String> listCesMainId = new ArrayList<String>();
			// 存放資信簡表文件編號
			List<String> listCesMainId2 = new ArrayList<String>();
			if (!listL120m01e.isEmpty()) {
				for (L120M01E l120m01e : listL120m01e) {
					if ("1".equals(Util.trim(l120m01e.getDocType()))) {
						if (custId.equals(Util.trim(l120m01e.getDocCustId()))
								&& dupNo.equals(Util.trim(l120m01e
										.getDocDupNo()))) {
							listCesMainId.add(l120m01e.getDocOid());
						}
					} else if ("2".equals(Util.trim(l120m01e.getDocType()))) {
						if (custId.equals(Util.trim(l120m01e.getDocCustId()))
								&& dupNo.equals(Util.trim(l120m01e
										.getDocDupNo()))) {
							listCesMainId2.add(l120m01e.getDocOid());
						}
					}
				}
			}

			// 開始串徵信文件編號(上傳MISELF447用)
//			StringBuilder cesMainIds = new StringBuilder();
//			for (String cesMainId : listCesMainId) {
//				cesMainIds.append(cesMainIds.length() > 0 ? "," : "");
//				cesMainIds.append("'");
//				cesMainIds.append(cesMainId);
//				cesMainIds.append("'");
//			}
			// 找不到徵信文件時開始串資信簡表文件編號(上傳MISELF447用)
//			StringBuilder cesMainIds2 = new StringBuilder();
//			if (CollectionUtils.isEmpty(listCesMainId)) {
//				for (String cesMainId2 : listCesMainId2) {
//					cesMainIds2.append(cesMainIds2.length() > 0 ? "," : "");
//					cesMainIds2.append("'");
//					cesMainIds2.append(cesMainId2);
//					cesMainIds2.append("'");
//				}
//			}

			// 系統別
			String sysNo = ("1".equals(Util.trim(l120m01a.getDocType()))) ? "LMS"
					: ("2".equals(Util.trim(l120m01a.getDocType()))) ? "CLS"
							: "";
			String cesSysNo = (CollectionUtils.isNotEmpty(listCesMainId)) ? "CES"
					: (CollectionUtils.isNotEmpty(listCesMainId2)) ? "CES" : null;

			// 更新時間
			Timestamp upTime = l120m01a.getUpdateTime();

			// 簽章欄
			List<L120M01F> list = l120m01fDao.findByMainId(mainId);

			// 角色
			String staffJob = "";

			// 員工編號
			String staffNo = "";

			// 額度序號
			String tSno = "0";
			// 額度明細表種類
			String itemType = "";
			// 根據來源不同出現的表不一樣 ，當授權外一定是批覆表，而當授權內為營運中心授權內還是以批覆表為準
			itemType = lmsservice.checkL140M01AItemType(l120m01a);
			List<L140M01A> listL140m01a = l140m01aDao
					.findL140m01aListByL120m01cMainId(mainId, itemType, null);

			HashSet<String> list140Rej = new HashSet<String>();

			// 授信性質別
			String proPerty = "";
			// 額度性質
			String sbjProperty = "";
			// 新作
			Map<String, Integer> tCntitem1 = new HashMap<String, Integer>();
			// 續約
			Map<String, Integer> tCntitem2 = new HashMap<String, Integer>();
			// 變更條件
			Map<String, Integer> tCntitem3 = new HashMap<String, Integer>();
			// 無擔保授信
			Map<String, Integer> tCntitem4 = new HashMap<String, Integer>();
			// 擔保授信
			Map<String, Integer> tCntitem5 = new HashMap<String, Integer>();

			// 是否控管擔保維持率
			Map<String, String> ISCOLLRT = new HashMap<String, String>();
			// 擔保維持率不得低於
			Map<String, Double> COLLRT = new HashMap<String, Double>();

			// 申報案件(含常董會案件)
			// 核准金額
			Map<String, BigDecimal> tApprAmt = new HashMap<String, BigDecimal>();

			Object[] o = new Object[1];
			o[0] = CapDate.getCurrentTimestamp();
			o = LMSUtil.covertAs400Time(o);

			if (l120m01a.getCaseLvl() == null) {
				// 丟出錯誤訊息
				throw new CapMessageException(
						Util.trim(l120m01a.getCaseNo()+prop.getProperty("LMS913V00.error3")),L120M01A.class);
			}
			// 刪除MIS(ELF447)
			deleteList.add(new Object[] { mainId });

			for (L140M01A l140m01a : listL140m01a) {
				if (FlowDocStatusEnum.婉卻.getCode().equals(
						l140m01a.getDocStatus())
						|| LMSUtil.isContainValue(l140m01a.getProPerty(),
								UtilConstants.Cntrdoc.Property.取消)
						|| LMSUtil.isContainValue(l140m01a.getProPerty(),
								UtilConstants.Cntrdoc.Property.不變)) {
					// 額度明細表若屬於婉卻狀態及或性質為不變 or 取消 的不需上傳
					if (FlowDocStatusEnum.婉卻.getCode().equals(
							l140m01a.getDocStatus())) {
						StringBuilder tempSb = new StringBuilder();
						tempSb.setLength(0);
						tempSb.append(Util.trim(l140m01a.getCustId())).append(
								Util.trim(l140m01a.getDupNo()));
						// 將婉卻的額度加入List
						list140Rej.add(tempSb.toString());
					}
					continue;
				} else {
					tSno = Util.trim(l140m01a.getCntrNo());
				}
				proPerty = Util.trim(l140m01a.getProPerty());
				if (LMSUtil.isContainValue(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.新做)) {
					// 新做
					tCntitem1.put(tSno, 1);
				}
				if (LMSUtil.isContainValue(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.續約)) {
					// 續約
					tCntitem2.put(tSno, 1);
				}
				if (LMSUtil.isContainValue(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.變更條件)) {
					// 變更條件
					tCntitem3.put(tSno, 1);
				}

				sbjProperty = Util.trim(l140m01a.getSbjProperty());
				if ("S".equals(sbjProperty)) {
					// 擔保
					tCntitem5.put(tSno, 1);
				} else {
					// 無擔保
					tCntitem4.put(tSno, 1);
				}

				// 核准金額(非台幣之金額需轉換為台幣)
				// 先檢核幣別是否有選擇
				boolean haseCurr = true;

				BranchRate branchRate = lmsservice.getBranchRate(l120m01a
						.getCaseBrId());
				if ("0".equals(l140m01a.getUseDeadline())) {
					if (!Util.isEmpty(l140m01a.getBLAmt())) {
						if (Util.isEmpty(l140m01a.getBLCurr())) {
							haseCurr = false;
						}
						BigDecimal apprAmt = new BigDecimal("0");
						if (!haseCurr) {
							// 幣別沒填
							apprAmt = new BigDecimal("0");
						} else {
							apprAmt = branchRate.toTWDAmt(l140m01a.getBLCurr(),
									l140m01a.getBLAmt()).divide(
									new BigDecimal("1"), 0,
									BigDecimal.ROUND_HALF_UP);
							// double apprAmt = l140m01a.getBLAmt()
							// .multiply(getBraRate(user.getUnitNo()))
							// .doubleValue();
						}
						tApprAmt.put(tSno, apprAmt);
					}
				} else {
					if (!Util.isEmpty(l140m01a.getCurrentApplyAmt())) {
						if (Util.isEmpty(l140m01a.getCurrentApplyCurr())) {
							haseCurr = false;
						}
						BigDecimal apprAmt = new BigDecimal("0");
						if (!haseCurr) {
							// 幣別沒填
							apprAmt = new BigDecimal("0");
						} else {
							apprAmt = branchRate.toTWDAmt(
									l140m01a.getCurrentApplyCurr(),
									l140m01a.getCurrentApplyAmt()).divide(
									new BigDecimal("1"), 0,
									BigDecimal.ROUND_HALF_UP);
							// double apprAmt = l140m01a.getCurrentApplyAmt()
							// .multiply(getBraRate(user.getUnitNo()))
							// .doubleValue();
						}
						tApprAmt.put(tSno, apprAmt);
					}
				}

				if ("Y".equals(Util.trim(l140m01a.getHeadItem5()))) {
					ISCOLLRT.put(tSno, "Y");
					if (UtilConstants.Cntrdoc.l140m01aMRateType.標準.equals(Util
							.trim(l140m01a.getMRateType()))) {
						COLLRT.put(tSno, (double) 0);
					} else if (UtilConstants.Cntrdoc.l140m01aMRateType.自訂
							.equals(Util.trim(l140m01a.getMRateType()))) {
						COLLRT.put(tSno, l140m01a.getMRate().doubleValue());
					} else {
						// 擔保維持率設定錯誤
					}
				} else {
					ISCOLLRT.put(tSno, "N");
					COLLRT.put(tSno, (double) 0);
				}

				// 開始從額度明細表取要上傳的值
				currentApplyAmt = Util.parseDouble(Util.nullToSpace(l140m01a
						.getCurrentApplyAmt()));
				LoanTotCurr = Util.trim(l140m01a.getLoanTotCurr());
				LoanTotAmt = Util.parseDouble(Util.nullToSpace(l140m01a
						.getLoanTotAmt()));
				assureTotCurr = Util.trim(l140m01a.getAssureTotCurr());
				assureTotAmt = Util.parseDouble(Util.nullToSpace(l140m01a
						.getAssureTotAmt()));
				LoanTotZCurr = Util.trim(l140m01a.getLoanTotZCurr());
				LoanTotZAmt = Util.parseDouble(Util.nullToSpace(l140m01a
						.getLoanTotZAmt()));
				LoanTotLCurr = Util.trim(l140m01a.getLoanTotLCurr());
				LoanTotLAmt = Util.parseDouble(Util.nullToSpace(l140m01a
						.getLoanTotLAmt()));
				LVCurr = Util.trim(l140m01a.getLVCurr());
				LVAmt = Util.parseDouble(Util.nullToSpace(l140m01a.getLVAmt()));
				currentApplyCurr = Util.trim(l140m01a.getCurrentApplyCurr());

				if (!list.isEmpty()) {
					for (L120M01F model : list) {
						staffNo = Util.trim(model.getStaffNo());
						if (Util.isEmpty(staffNo)) {
							staffNo = "";
						}
						String branchType = Util.trim(model.getBranchType());
						String areaDocStatus = Util.trim(l120m01a
								.getAreaDocstatus());
						String areaBrid = Util.trim(l120m01a.getAreaBrId());
						staffJob = Util.trim(model.getStaffJob());
						tBrno2 = Util.trim(model.getBranchId());
						Date dApproveDate = null;
						if ("CES".equals(Util.trim(cesSysNo))) {
							String cesStaffNo = null;
							String cesStaffJob = null;
							String cesTbrno2 = null;
							List<Map<String, Object>> rows = null;
							if (CollectionUtils.isNotEmpty(listCesMainId)) {
								rows = r6dbService
										.findC140M01A_selUpload(listCesMainId.toArray(new String[0]));
							} else {
								rows = r6dbService
										.findC120M01A_selUpload(listCesMainId2.toArray(new String[0]));
							}
							for (Map<String, Object> row : rows) {
								cesStaffNo = Util.trim(row.get("SIGNID"));
								Date date = Util.parseDate(Util.trim(Util
										.nullToSpace(row.get("APPROVETIME"))));
								dApproveDate = (Util.isEmpty(date) ? null
										: CapDate.parseSQLDate(date));

								if (dApproveDate == null) {
									// 丟出錯誤訊息
									throw new FlowMessageException(
											"CES APPROVETIME NULL");
								}
								cesStaffJob = getCesTitle(Util
										.trim((String) row.get("TITLE")));
								cesTbrno2 = Util.trim((String) row
										.get("OWNBRID"));
							}
							if (!rows.isEmpty()) {
								// 上傳MIS(ELF447)
								dataList.add(this.upLoad447(
										Util.trim(cesStaffNo), dApproveDate,
										Util.trim(cesStaffJob), tBrno,
										Util.trim(cesTbrno2), custId, dupNo,
										tSno, mainId, caseLvl, proPerty,
										upTime, currentApplyAmt, tCaseNo,
										LoanTotCurr, LoanTotAmt, assureTotCurr,
										assureTotAmt, LoanTotZCurr,
										LoanTotZAmt, LoanTotLCurr, LoanTotLAmt,
										LVCurr, LVAmt, currentApplyCurr,
										liHaiBank, liHai44, liHai45,
										Util.trim(cesSysNo)));
							}

						}

						if (!Util.isEmpty(staffJob)) {
							if (UtilConstants.STAFFJOB.帳戶管理員L2.equals(staffJob)) {
								// 當帳務管理員為空時不上傳
								if (!Util.isEmpty(Util.trim(staffNo))) {
									staffJob = this.getReaStaffJob(
											Util.trim(staffJob),
											Util.trim(branchType),
											Util.trim(areaDocStatus),
											Util.trim(areaBrid));
								}
							} else {
								staffJob = this.getReaStaffJob(
										Util.trim(staffJob),
										Util.trim(branchType),
										Util.trim(areaDocStatus),
										Util.trim(areaBrid));

							}
							if (!Util.isEmpty(approveDate)) {
								dApproveDate = CapDate
										.parseSQLDate(approveDate);
							} else {
								dApproveDate = CapDate
										.parseSQLDate("0001-01-01");
							}
							// 上傳MIS(ELF447)
							dataList.add(this.upLoad447(staffNo, dApproveDate,
									staffJob, tBrno, tBrno2, custId, dupNo,
									tSno, mainId, caseLvl, proPerty, upTime,
									currentApplyAmt, tCaseNo, LoanTotCurr,
									LoanTotAmt, assureTotCurr, assureTotAmt,
									LoanTotZCurr, LoanTotZAmt, LoanTotLCurr,
									LoanTotLAmt, LVCurr, LVAmt,
									currentApplyCurr, liHaiBank, liHai44,
									liHai45, sysNo));

						}
					}
				}
			}
		}
		
		this.misELF447Service.insertELF4472(dataList, deleteList);
		this.ClearL120M01A(oidlist,"");
		
	}

	/**
	 * 依照徵信欄位回傳相對應的值(徵信上傳Mis 447用)
	 * 
	 * @param title
	 *            徵信欄位
	 * @return 相對應的值
	 */
	private String getCesTitle(String title) {
		if ("appraiser".equals(title)) {
			return "1";
		} else if ("reCheck1".equals(title)) {
			return "3";
		} else if ("reCheck2".equals(title)) {
			return "3";
		} else if ("boss1".equals(title)) {
			return "4";
		} else if ("boss2".equals(title)) {
			return "4";
		} else if ("manager".equals(title)) {
			return "5";
		} else {
			return "";
		}
	}

	/**
	 * 取得角色代碼
	 * 
	 * @param staffJob
	 *            人員職稱
	 * @param branchType
	 *            分行類型
	 * @param areaDocStatus
	 *            會簽文件狀態
	 * @param areaBrid
	 *            會審單位代碼
	 * @return 角色代碼
	 */
	private String getReaStaffJob(String staffJob, String branchType,
			String areaDocStatus, String areaBrid) {
		if (!Util.isEmpty(branchType)) {
			if (UtilConstants.BRANCHTYPE.國金部_營運中心.equals(branchType)) {
				if ((營運中心_海外聯貸案_會簽中.equals(areaDocStatus)
						|| 營運中心_海外聯貸案_待放行.equals(areaDocStatus) || 營運中心_海外聯貸案_已會簽
						.equals(areaDocStatus))
						&& !_國金部.equals(Util.trim(areaBrid))) {
					// 營運中心
					return getStaffNo(staffJob, branchType, true);
				} else {
					// 國金部
					return getStaffNo(staffJob, branchType, false);
				}
			} else {
				// 其他單位
				return getStaffNo(staffJob, branchType, false);
			}
		} else {
			return "";
		}
	}

	/**
	 * 依照分行類型與人員職稱取得相對應的角色代碼
	 * 
	 * @param staffJob
	 *            人員職稱
	 * @param type
	 *            分行類型
	 * @param isArea
	 *            是否為會簽營運中心
	 * @return
	 */
	private String getStaffNo(String staffJob, String type, boolean isArea) {
		if (UtilConstants.BRANCHTYPE.分行.equals(type)
				|| UtilConstants.BRANCHTYPE.母行海外總行.equals(type)
				|| (UtilConstants.BRANCHTYPE.國金部_營運中心.equals(type) && !isArea)) {
			// 分行
			return this.getSub(staffJob);
		} else if (UtilConstants.BRANCHTYPE.營運中心.equals(type)
				|| (UtilConstants.BRANCHTYPE.國金部_營運中心.equals(type) && isArea)) {
			// 營運中心
			return this.getArea(staffJob);
		} else if (UtilConstants.BRANCHTYPE.授管處.equals(type)) {
			// 授管處
			return this.getHead(staffJob);
		} else {
			return "";
		}
	}

	/**
	 * 依照人員職稱回傳營運中心角色代碼
	 * 
	 * @param staffJob
	 *            人員職稱
	 * @return 營運中心角色代碼
	 */
	private String getArea(String staffJob) {
		if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
			return "1";
		} else if (UtilConstants.STAFFJOB.帳戶管理員L2.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.執行覆核主管L4.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {
			return "6";
		} else if (UtilConstants.STAFFJOB.分行單位主管L6.equals(staffJob)) {
			return "4";
		} else if (UtilConstants.STAFFJOB.提會登錄經辦L7.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.提會放行主管L8.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.單位主管L9.equals(staffJob)) {
			return "5";
		} else {
			return "";
		}
	}

	/**
	 * 依照人員職稱回傳角色代碼
	 * 
	 * @param staffJob
	 *            人員職稱
	 * @return 角色代碼
	 */
	private String getSub(String staffJob) {
		if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
			return "1";
		} else if (UtilConstants.STAFFJOB.帳戶管理員L2.equals(staffJob)) {
			return "2";
		} else if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
			return "6";
		} else if (UtilConstants.STAFFJOB.執行覆核主管L4.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {
			return "4";
		}
		// else if (UtilConstants.STAFFJOB.分行單位主管L6.equals(staffJob)) {
		// return "5";
		// }
		else if (UtilConstants.STAFFJOB.提會登錄經辦L7.equals(staffJob)) {
			return "1";
		} else if (UtilConstants.STAFFJOB.提會放行主管L8.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.單位主管L9.equals(staffJob)) {
			return "5";
		} else {
			return "";
		}
	}

	/**
	 * 依照人員職稱回傳授管處角色代碼
	 * 
	 * @param staffJob
	 *            人員職稱
	 * @return 授管處角色代碼
	 */
	private String getHead(String staffJob) {
		if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
			return "1";
		} else if (UtilConstants.STAFFJOB.帳戶管理員L2.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.執行覆核主管L4.equals(staffJob)) {
			return "3";
		} else if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {
			return "6";
		} else if (UtilConstants.STAFFJOB.分行單位主管L6.equals(staffJob)) {
			return "4";
		} else if (UtilConstants.STAFFJOB.提會登錄經辦L7.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.提會放行主管L8.equals(staffJob)) {
			return "";
		} else if (UtilConstants.STAFFJOB.單位主管L9.equals(staffJob)) {
			return "5";
		} else {
			return "";
		}
	}

	/**
	 * 上傳ELF447(資料不存在用Insert，存在用Update)
	 * 
	 * @param staffNo
	 *            staffNo
	 * @param approveTime
	 *            approveTime
	 * @param staffJob
	 *            staffJob
	 * @param caseBrid
	 *            caseBrid
	 * @param caseBrid2
	 *            caseBrid2
	 * @param custId
	 *            custId
	 * @param dupNo
	 *            dupNo
	 * @param cntrNo
	 *            cntrNo
	 * @param mainId
	 *            mainId
	 * @param caseLvl
	 *            caseLvl
	 * @param property
	 *            property
	 * @param upTime
	 *            upTime
	 * @param currentApplyAmt
	 *            currentApplyAmt
	 * @param tCaseNo
	 *            tCaseNo
	 * @param LoanTotCurr
	 *            LoanTotCurr
	 * @param LoanTotAmt
	 *            LoanTotAmt
	 * @param assureTotCurr
	 *            assureTotCurr
	 * @param assureTotAmt
	 *            assureTotAmt
	 * @param LoanTotZCurr
	 *            LoanTotZCurr
	 * @param LoanTotZAmt
	 *            LoanTotZAmt
	 * @param LoanTotLCurr
	 *            LoanTotLCurr
	 * @param LoanTotLAmt
	 *            LoanTotLAmt
	 * @param LVCurr
	 *            LVCurr
	 * @param LVAmt
	 *            LVAmt
	 * @param currentApplyCurr
	 *            currentApplyCurr
	 * @param liHaiBank
	 *            liHaiBank
	 * @param liHai44
	 *            liHai44
	 * @param liHai45
	 *            liHai45
	 * @param sysNo
	 *            sysNo
	 */
	private Object[] upLoad447(String staffNo, Date approveTime,
			String staffJob, String caseBrid, String caseBrid2, String custId,
			String dupNo, String cntrNo, String mainId, String caseLvl,
			String property, Timestamp upTime, double currentApplyAmt,
			String tCaseNo, String LoanTotCurr, double LoanTotAmt,
			String assureTotCurr, double assureTotAmt, String LoanTotZCurr,
			double LoanTotZAmt, String LoanTotLCurr, double LoanTotLAmt,
			String LVCurr, double LVAmt, String currentApplyCurr,
			String liHaiBank, String liHai44, String liHai45, String sysNo) {
		// 上傳MIS(ELF447)

		Object[] datalist = new Object[] { staffNo, approveTime, staffJob,
				caseBrid2, custId, dupNo, cntrNo, caseBrid, sysNo, mainId,
				caseLvl, property, upTime, currentApplyAmt, tCaseNo,
				LoanTotCurr, LoanTotAmt, assureTotCurr, assureTotAmt,
				LoanTotZCurr, LoanTotZAmt, LoanTotLCurr, LoanTotLAmt, LVCurr,
				LVAmt, currentApplyCurr, liHaiBank, liHai44, liHai45 };
		// this.misELF447Service.insertElf447();
		return datalist;
	}

}
