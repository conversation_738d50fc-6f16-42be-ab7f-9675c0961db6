package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

import tw.com.iisi.cap.util.CapString;

/**
 * 稽核工作底稿主檔
 * 
 * <AUTHOR>
 * 
 */
@NamedEntityGraph(name = "L192M01A-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l192m01c"),
		@NamedAttributeNode("l192m01d")
		})
@Entity
@EntityListeners({ DocumentModifyListener.class })
public class L192M01A extends Meta {

	private static final long serialVersionUID = 8864606129472730272L;
	
	/**工作底稿種類{1:授信業務, 2:房屋貸款, 3:團體消費性貸款} */
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String shtType;
	
	/**是否顯示於查核對帳單作業
	 * Y: 全顯示
	 * P: 部分顯示(不寄送函證=不顯示)
	 * N: 不列印(全無申請資料、授信餘額均為0、全不寄送函證)
	 * */
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String createBill;

	/**是否為分行內部查核案件*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String innerAudit;
	
	/**檢查基準日*/
	@Temporal(TemporalType.DATE)
	private Date checkBase;

	/**檢查日期*/	
	@Temporal(TemporalType.DATE)
	private Date checkDate;
	
	/**[領隊/覆核]*/
	@Column(length = 38, columnDefinition = "VARCHAR(38)")
	private String leader;

	/**總編號*/
	@Column(length = 13, columnDefinition = "VARCHAR(13)")
	private String tNo;

	/**W/P年度*/
	@Column(precision = 4, columnDefinition = "DECIMAL(4,0)")
	private BigDecimal wpYear;

	/**W/P分行*/
	@Column(length = 3, columnDefinition = "CHAR(3)")
	private String wpBrId;
	
	/**W/P流水號*/
	@Column(columnDefinition = "DECIMAL(5,0)")
	private BigDecimal wpSeq;

	/**W/P編號*/
	@Column(length = 13, columnDefinition = "VARCHAR(14)")
	private String wpNo;

	/**檢查人*/
	@Column(length = 38, columnDefinition = "VARCHAR(38)")
	private String checkMan;

	/**有無授信審查小組會議記錄*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String mtDoc;

	/**主要借款人電話*/
	@Column(length = 16, columnDefinition = "VARCHAR(16)")
	private String tTel;

	/**主要借款人地址*/
	@Column(length = 255, columnDefinition = "VARCHAR(255)")
	private String tAddr;

	/**有無借款人及連保人徵信查詢*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String cdQ1;

	/**有無借款人及連保人票信查詢*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String cdQ2;

	/**有無查身份證遺失*/
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String cdQ3;

	/**鑑價單位*/
	@Column(length = 30, columnDefinition = "VARCHAR(30)")
	private String estUnit;

	/**檢查意見*/
	@Column(length = 3072, columnDefinition = "VARCHAR(3072)")
	private String gist;

	/**處理意見*/
	@Column(length = 384, columnDefinition = "VARCHAR(384)")
	private String processComm;

	/**RPTID*/
	@Column(length = 32, columnDefinition = "VARCHAR(32)")
	private String rptId;

	/**對帳單寄件地址*/
	@Column(length = 255, columnDefinition = "VARCHAR(255)")
	private String statementAddrFrom;

	/**對帳單收件地址*/
	@Column(length = 255, columnDefinition = "VARCHAR(255)")
	private String statementAddrTo;

	@ToStringExclude
	@OneToMany(mappedBy = "l192m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L192M01B> l192m01bs;

	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false) })
	private L192M01C l192m01c;

	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", insertable = false, updatable = false) })
	private L192M01D l192m01d;

	@ToStringExclude
	@OneToMany(mappedBy = "l192m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L192S01A> l192s01as;

	@ToStringExclude
	@OneToMany(mappedBy = "l192m01a", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L192S02A> l192s02as;

	/** 工作底稿種類{1:授信業務, 2:房屋貸款, 3:團體消費性貸款} */
	public String getShtType() {
		return shtType;
	}

	/** 工作底稿種類{1:授信業務, 2:房屋貸款, 3:團體消費性貸款} */
	public void setShtType(String shtType) {
		this.shtType = shtType;
	}

	/** 是否列印對帳單 
	 * Y: 全顯示
	 * P: 部分顯示(不寄送函證=不顯示)
	 * N: 不列印(全無申請資料、授信餘額均為0、全不寄送函證)
	 * */
	public String getCreateBill() {
		return createBill;
	}

	/** 是否列印對帳單
	 * Y: 全顯示
	 * P: 部分顯示(不寄送函證=不顯示)
	 * N: 不列印(全無申請資料、授信餘額均為0、全不寄送函證)
	 *  */
	public void setCreateBill(String createBill) {
		this.createBill = createBill;
	}

	/** 是否為分行內部查核案件 */
	public String getInnerAudit() {
		return innerAudit;
	}

	/** 是否為分行內部查核案件 */
	public void setInnerAudit(String innerAudit) {
		this.innerAudit = innerAudit;
	}

	/** 檢查基準日 */
	public Date getCheckBase() {
		return checkBase;
	}

	/** 檢查基準日 */
	public void setCheckBase(Date checkBase) {
		this.checkBase = checkBase;
	}

	/** 檢查日期 */
	public Date getCheckDate() {
		return checkDate;
	}

	/** 檢查日期 */
	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	/** [領隊/覆核] */
	public String getLeader() {
		return leader;
	}

	/** [領隊/覆核] */
	public void setLeader(String leader) {
		this.leader = leader;
	}

	/** 總編號 */
	public String getTNo() {
		return tNo;
	}

	/** 總編號 */
	public void setTNo(String tNo) {
		this.tNo = tNo;
	}

	/** W/P年度 */
	public BigDecimal getWpYear() {
		return wpYear;
	}

	/** W/P年度 */
	public void setWpYear(BigDecimal wpYear) {
		this.wpYear = wpYear;
	}

	/** W/P分行 */
	public String getWpBrId() {
		return wpBrId;
	}

	/** W/P分行 */
	public void setWpBrId(String wpBrId) {
		this.wpBrId = wpBrId;
	}

	/** W/P流水號 */
	public BigDecimal getWpSeq() {
		return wpSeq;
	}

	/** W/P流水號 */
	public void setWpSeq(BigDecimal wpSeq) {
		this.wpSeq = wpSeq;
	}

	/** W/P編號 */
	public String getWpNo() {
		return wpNo;
	}

	/** W/P編號 */
	public void setWpNo(String wpNo) {
		this.wpNo = wpNo;
	}

	/** 檢查人 */
	public String getCheckMan() {
		return checkMan;
	}

	/** 檢查人 */
	public void setCheckMan(String checkMan) {
		this.checkMan = checkMan;
	}

	/** 有無授信審查小組會議記錄 */
	public String getMtDoc() {
		return mtDoc;
	}

	/** 有無授信審查小組會議記錄 */
	public void setMtDoc(String mtDoc) {
		this.mtDoc = mtDoc;
	}

	/** 主要借款人電話 */
	public String getTTel() {
		return tTel;
	}

	/** 主要借款人電話 */
	public void setTTel(String tTel) {
		this.tTel = tTel;
	}

	/** 主要借款人地址 */
	public String getTAddr() {
		return tAddr;
	}

	/** 主要借款人地址 */
	public void setTAddr(String tAddr) {
		tAddr = CapString.halfWidthToFullWidth(tAddr);
		// StringUtils.left(tAddr, 30);
		this.tAddr = tAddr;
	}

	/** 有無借款人及連保人徵信查詢 */
	public String getCdQ1() {
		return cdQ1;
	}

	/** 有無借款人及連保人徵信查詢 */
	public void setCdQ1(String cdQ1) {
		this.cdQ1 = cdQ1;
	}

	/** 有無借款人及連保人票信查詢 */
	public String getCdQ2() {
		return cdQ2;
	}

	/** 有無借款人及連保人票信查詢 */
	public void setCdQ2(String cdQ2) {
		this.cdQ2 = cdQ2;
	}

	/** 有無查身份證遺失 */
	public String getCdQ3() {
		return cdQ3;
	}

	/** 有無查身份證遺失 */
	public void setCdQ3(String cdQ3) {
		this.cdQ3 = cdQ3;
	}

	/** 鑑價單位 */
	public String getEstUnit() {
		return estUnit;
	}

	/** 鑑價單位 */
	public void setEstUnit(String estUnit) {
		this.estUnit = estUnit;
	}

	/** 檢查意見 */
	public String getGist() {
		return gist;
	}

	/** 檢查意見 */
	public void setGist(String gist) {
		this.gist = gist;
	}

	/** 處理意見 */
	public String getProcessComm() {
		return processComm;
	}

	/** 處理意見 */
	public void setProcessComm(String processComm) {
		this.processComm = processComm;
	}

	/** RPTID */
	public String getRptId() {
		return rptId;
	}

	/** RPTID */
	public void setRptId(String rptId) {
		this.rptId = rptId;
	}

	/** 借款人及連保人基本資料檔 */
	public Set<L192M01B> getL192m01bs() {
		return l192m01bs;
	}

	/** 借款人及連保人基本資料檔 */
	public void setL192m01bs(Set<L192M01B> l192m01bs) {
		this.l192m01bs = l192m01bs;
	}

	public void addL192m01b(L192M01B l192m01b) {
		l192m01bs.add(l192m01b);
	}

	public void removeL192m01b(L192M01B l192m01b) {
		l192m01bs.remove(l192m01b);
	}

	/** 授信業務查核事項檔 */
	public L192M01C getL192m01c() {
		return l192m01c;
	}

	/** 授信業務查核事項檔 */
	public void setL192m01c(L192M01C l192m01c) {
		this.l192m01c = l192m01c;
	}

	/** 房屋貸款查核事項檔 */
	public L192M01D getL192m01d() {
		return l192m01d;
	}

	/** 房屋貸款查核事項檔 */
	public void setL192m01d(L192M01D l192m01d) {
		this.l192m01d = l192m01d;
	}

	/** 申請內容檔 */
	public Set<L192S01A> getL192s01as() {
		return l192s01as;
	}

	/** 申請內容檔 */
	public void setL192s01as(Set<L192S01A> l192s01as) {
		this.l192s01as = l192s01as;
	}

	public void addL192s01a(L192S01A l192s01a) {
		this.l192s01as.add(l192s01a);
	}

	public void removeL192s01a(L192S01A l192s01a) {
		this.l192s01as.remove(l192s01a);
	}

	/** 擔保品資料檔 */
	public Set<L192S02A> getL192s02as() {
		return l192s02as;
	}

	/** 擔保品資料檔 */
	public void setL192s02as(Set<L192S02A> l192s02as) {
		this.l192s02as = l192s02as;
	}

	public void addL192s02a(L192S02A l192s02a) {
		this.l192s02as.add(l192s02a);
	}

	public void removeL192s02a(L192S02A l192s02a) {
		this.l192s02as.remove(l192s02a);
	}

	/** 對帳單寄件地址 */
	public void setStatementAddrFrom(String statementAddrFrom) {
		statementAddrFrom = CapString.halfWidthToFullWidth(statementAddrFrom);
		// statementAddrFrom = StringUtils.left(statementAddrFrom, 30);
		this.statementAddrFrom = statementAddrFrom;
	}

	/** 對帳單寄件地址 */
	public String getStatementAddrFrom() {
		return statementAddrFrom;
	}

	/** 對帳單收件地址 */
	public void setStatementAddrTo(String statementAddrTo) {
		statementAddrTo = CapString.halfWidthToFullWidth(statementAddrTo);
		//statementAddrTo = StringUtils.left(statementAddrTo, 30);
		this.statementAddrTo = statementAddrTo;
	}

	/** 對帳單收件地址 */
	public String getStatementAddrTo() {
		return statementAddrTo;
	}

}
