/* 
 * L140M01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 額度授信科目限額檔 **/
@NamedEntityGraph(name = "L140M01D-entity-graph", attributeNodes = { @NamedAttributeNode("l140m01a") })
@Entity
@Table(name = "L140M01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "lmtType", "lmtSeq" }))
public class L140M01D extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 限額類型
	 * <p/>
	 * 1科子目限額<br/>
	 * 2科子目合併限額<br/>
	 * 3科子目限額_BF
	 */
	@Column(name = "LMTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String lmtType;

	/** 序號 **/
	@Column(name = "LMTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer lmtSeq;

	/**
	 * 授信科目組合
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	@Column(name = "SUBJECT", length = 300, columnDefinition = "VARCHAR(300)")
	private String subject;

	/** 限額－幣別 **/
	@Column(name = "LMTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String lmtCurr;

	/** 限額－金額 **/
	@Column(name = "LMTAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lmtAmt;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;
	
	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得限額類型
	 * <p/>
	 * 1科子目限額<br/>
	 * 2科子目合併限額<br/>
	 * 3科子目限額_BF
	 */
	public String getLmtType() {
		return this.lmtType;
	}

	/**
	 * 設定限額類型
	 * <p/>
	 * 1科子目限額<br/>
	 * 2科子目合併限額<br/>
	 * 3科子目限額_BF
	 **/
	public void setLmtType(String value) {
		this.lmtType = value;
	}

	/** 取得序號 **/
	public Integer getLmtSeq() {
		return this.lmtSeq;
	}

	/** 設定序號 **/
	public void setLmtSeq(Integer value) {
		this.lmtSeq = value;
	}

	/**
	 * 取得授信科目組合
	 * <p/>
	 * xxx|xxx|xxx…
	 */
	public String getSubject() {
		return this.subject;
	}

	/**
	 * 設定授信科目組合
	 * <p/>
	 * xxx|xxx|xxx…
	 **/
	public void setSubject(String value) {
		this.subject = value;
	}

	/** 取得限額－幣別 **/
	public String getLmtCurr() {
		return this.lmtCurr;
	}

	/** 設定限額－幣別 **/
	public void setLmtCurr(String value) {
		this.lmtCurr = value;
	}

	/** 取得限額－金額 **/
	public BigDecimal getLmtAmt() {
		return this.lmtAmt;
	}

	/** 設定限額－金額 **/
	public void setLmtAmt(BigDecimal value) {
		this.lmtAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
