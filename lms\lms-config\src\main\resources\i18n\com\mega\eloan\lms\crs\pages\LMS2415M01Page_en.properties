#==================================================
# \u500b\u91d1-\u8986\u5be9\u5831\u544a\u8868\u4e3b\u6a94-M01
#==================================================
page.title=Lms2415M01 Personal Banking Credit Review Report
page.title2=Personal Banking Credit Review Report

C241M01a.custId=Borrower
C241M01a.ownBrId=Branch name
C241M01a.staff=Branch Staff?
C241M01a.dupNo=Repeat Serial No.

lms2415.tit01=Document Information
lms2415.tit02=Credit Underwriting/Review Information
lms2415.tit03=General Review Items
lms2415.tit04=Credit Review Opinions & The Inspected Unit's Review Progress
lms2415.tit05=File Attachment
lms2415.tit06=After maintain completed; please press[Save and upload the Credit Review Control File once complete]


print.custName=Borrower's Name
print.rptNo=Report Serial Number
print.rptName=Report Name

C241M01a.y=Yes
C241M01a.n=No
C241M01a.an=1
C241M01a.haveNO=None
C241M01a.haved=Exist
C241M01a.cy=Rectified
C241M01a.cn=Not rectified


C241M01a.titleA=Credit Assessment
C241M01a.titleB=Debt Assurance
C241M01a.titleC=Others


lms2415.check=Please Select
lms2415.check1=Whether to return the case to the handling officer for amendments; to return, please press [OK], otherwise press [Cancel]
lms2415.check2=Whether to approve the case; to confirm, press [OK], otherwise press [Cancel] to exit
lms2415.choose1=Return to handling officer for correction
lms2415.choose2=Approval
C241M01b.warmMsg01=The following currency can not exchange rate conversion:

C241M01b.coBorrower01=(grantor)
C241M01b.coBorrower02=(General) 
C241M01b.coBorrower03=(joint debt)
C241M01b.coBorrower04=(notes debt)
C241M01b.coBorrower05=(collateral)

C241M01a.retrialDate=this field can not be empty | (Review Date )
C241M01a.projectNo=This field can not be blank | (Case No.)
C241M01a.branchComm=This field can not be blank | (inspected unit contact the Office of circumstances)
C241M01c.choiceChkResult=All review Items must fill out (yes, no, not applicable)
C241M01b.checkSendData=credit Accounts breakdown \u300cWhether selected as part of the current credit review\u300d \u3001\u300cCredit Limit Type \u300dand \u300c Credit Limit Serial Number \u300d can not be blank
C241M01b.inputOneData=credit billing details at least enter 1 record

C241M01A.check01=has been performed [preparation after upload control file rehear\u3011 whether you want to perform?
C241M01A.check02title=The type for a review of the category 99 to maintain the cycle of Appeal? The default is one month (such as maintenance)
C241M01A.check0200=Whether maintenance of review cycle
C241M01A.check0201=maintenance
C241M01A.check0202=does not maintain a
C241M01A.check0203=Free retrial
C241M01A.check0204=1 months
C241M01A.check0205=3 months
C241M01A.check0206=6 months
C241M01A.check0207=12 months
C241M01A.check0208=Review Cycles
C241M01A.conFlag=Credit Review Opinions
C241M01A.condition=Abnormalities, needed improvements or warnings

C241M01aV2_CA.titleA=Credit Check
C241M01aV2.titleA=Credit Investigation Items
C241M01aV2_CA.titleB=Ensuring Creditor Rights
C241M01aV2.titleB=Creditor Rights Assurance
C241M01aV2.titleC=Other
C241M01a.y_i=includes
C241M01a.n_d=does not include
C241M01a.info01=\uff08give details about the implementation status regardless of the choice\uff09
C241M01a.info02=Attention!! Please verify whether the Usage and Financing type under Article 72-2 of the Banking Act of Taiwan are filed correctly.
lms2415.ChkMsg01=Please select whether Review Item [{0}] has improved and enter a description.
lms2415.ChkMsg02=Review Items: {0} Not entered
lms2415.ChkMsg03=Review Item [{0}] Descriptions Of Credit Review Details can not be empty.
lms2415.ChkMsg04=Review Item [{0}] Attached Table can not be empty.
lms2415.ChkMsg05=Review Item : {0} can not be {1}
lms2415.ChkMsg06=Need link to Credit Review Worksheet

button.AddToC240M01A=Link to Credit Review Worksheet