<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
       <th:block th:fragment="innerPageBody">
       <script type="text/javascript"> 
    		loadScript('pagejs/lms/LMS7830M01Page');
		</script>
       
            <div class="button-menu funcContainer" id="buttonPanel">
                <button id="btnSearch">
                    <span class="ui-icon ui-icon-jcs-102"></span>
                    <th:block th:text="#{'button.search'}">
                        開始查詢
                    </th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">
                        離開
                    </th:block>
                </button>
            </div>
           <div class="tit2 color-black">
				<th:block th:text="#{'L783M01A.title'}">簽報記錄查詢</th:block>：
			</div>
	

	        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	          <tbody>
	              <tr>
	                  <td width="20%" class="hd1"><th:block th:text="#{'L783M01A.queryDate'}">查詢日期</th:block>&nbsp;&nbsp;</td>
	                  <td width="30%"><span id="queryDate"></span></td>
	                  <td width="20%" class="hd1"><th:block th:text="#{'L783M01A.custId'}">客戶名稱</th:block>&nbsp;&nbsp;</td>
	                  <td width="30%"><span id="custId"></span> <th:block th:text="#{'doc.idDup'}">重覆序號</th:block>：<span id="dupNo"></span><br />(<span class="color-red"><span id="typCd"></span></span>)<span id="custName"></span></td>
	              </tr>
	          </tbody>
	        </table>
          <fieldset>
                    <legend><th:block th:text="#{'L783M01A.title'}">簽報案件查詢</th:block></legend>         
					<div id="gridView"></div>
          </fieldset>
		  
		   <div id="queryTable" style="display:none">
		   	<form id="queryForm" name="queryForm">
			  	<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			  		<tr>
			  			<td class="hd1"  width="50%"><th:block th:text="#{'L783M01A.cxtype'}">區部別</th:block>&nbsp;&nbsp;</td>
						<td width="50%">
							<select id="txCdselect" combokey="TypCd" combotype="2"></select>
						</td>
			  		</tr>
					<tr>
			  			<td class="hd1"><th:block th:text="#{'doc.id'}">統一編號</th:block>&nbsp;&nbsp;</td>
						<td><input  id="inputCustId" name="inputCustId" size="10" class="required upText" maxlength="10" /></td>
			  		</tr>
			  	</table>
			</form>
		  </div>
		  
		  
		   <div id="selectPeopleBox" style="display:none">
			  	<select id="custIdSelect" ></select>
		  </div>
        </th:block>
    </body>
</html>
