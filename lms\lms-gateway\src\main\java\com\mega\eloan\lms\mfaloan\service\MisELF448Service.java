/* 
 *MisLNF448Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 放款帳號
 * </pre>
 * 
 * @since 2013/01/02
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/02,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
public interface MisELF448Service {
	public List<Map<String, Object>> findBycntrNodupNocustId(String custId,String dupNo,String cnrtNo);
	
	public void updateHOUSE(String custId,String dupNo,String cnrtNo,String selfChk,String rskFlag,String aLoanAC);

	public void insertHOUSE(String custId,String dupNo,String cnrtNo,String selfChk,String rskFlag,String aLoanAC);
}
