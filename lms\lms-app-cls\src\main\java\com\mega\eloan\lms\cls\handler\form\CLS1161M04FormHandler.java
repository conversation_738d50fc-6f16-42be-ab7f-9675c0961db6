package com.mega.eloan.lms.cls.handler.form;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.pages.CLS1161M04Page;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;
import com.mega.eloan.lms.model.L250S02B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls1161m04formhandler")
@DomainClass(L250M01A.class)
public class CLS1161M04FormHandler extends AbstractFormHandler {

	@Resource
	BranchService branchService;

	@Resource
	LMS2501Service lms2501Service;

	@Resource
	CLS1141Service cls1141Service;

	@Resource
	DocCheckService docCheckService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	LMSService lmsService;

	@Resource
	CLS1161Service cls1161Service;

	@Resource
	CLS1151Service cls1151Service;

	@Resource
	CLSService clsService;
	
	Properties pop = MessageBundleScriptCreator.getComponentResource(CLS1161M04Page.class);

	public IResult saveDoc(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString("page"));
		result.add(saveDocByPage(page, params));

		return result;
	}

	protected IResult saveDocByPage(int page, PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
//		String mainOid = params.getString(EloanConstants.MAIN_OID);

		L250M01A meta = null;
		// 處理 transaction
		meta = prepareData(page, params);

		// 因為畫面不切換，所以要將畫面需要的更新資料回傳。
		switch (page) {
		case 1:
			result.set("docStatus",
					getMessage("docStatus." + meta.getDocStatus()));
			break;
		case 2:
			break;

		default:
			break;

		}

		// common 要求塞值欄位
		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		// result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "Y");
		int page = Util.parseInt(params.getString("page"));
		result.add(tempSaveByPage(page, params));

		return result;
	}

	protected IResult tempSaveByPage(int page, PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L250M01A meta = null;

		// 處理transaction
		meta = prepareData(page, params);

		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	protected L250M01A prepareData(int page, PageParameters params) throws CapException {

		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class,
				params.getString(EloanConstants.MAIN_OID));
		if(meta==null){
			throw new CapMessageException(EloanConstants.MAIN_OID+"="+Util.trim(params.getString(EloanConstants.MAIN_OID))
					+" 查無 模擬動審主檔 ", getClass());
		}
		JSONObject tabFormJson = null;
		switch (page) {
		case 1:
			tabFormJson = JSONObject.fromObject(params.getString("tabForm"));
			DataParse.toBean(tabFormJson, meta);
			meta = lms2501Service.updateMetaInfoTab(meta);
			break;
		case 2:
			String listData = params.getString("listData");
			JSONArray ja = JSONArray.fromObject(listData);
			String project1 = params.getString("project1");
			String project2 = params.getString("project2");
			String project3 = params.getString("project3");
			String project4 = params.getString("project4");
			String project5 = params.getString("project5");
			String project6 = params.getString("project6");
			String project7 = params.getString("project7");

			meta.setProject1(project1);
			meta.setProject2(project2);
			meta.setProject3(project3);
			meta.setProject4(project4);
			meta.setProject5(project5);
			meta.setProject6(project6);
			meta.setProject7(project7);

			meta = lms2501Service.updateClsCheckListTab(meta, ja);
			break;
		default:
			break;
		}

		return meta;
	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult initForm(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);

		L250M01A meta = null;

		if ((mainOid == null || mainOid.length() == 0)
				&& (mainId == null || mainId.length() == 0)) {

			meta = new L250M01A();
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());

			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));

		} else {
			meta = lms2501Service.findModelByOid(L250M01A.class, mainOid);
			int page = Util.parseInt(params.getString("page"));

			switch (page) {
			case 1:
				result = DataParse.toResult(meta);

				result.set("apprId", lmsService.getUserName(meta.getApprId()));
				result.set("managerId",
						lmsService.getUserName(meta.getManagerId()));
				result.set("reCheckId",
						lmsService.getUserName(meta.getReCheckId()));
				result.set("bossId", lmsService.getUserName(meta.getBossId()));

				List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
						.findListByMainId(L250M01B.class, meta.getMainId());

				if (CollectionUtils.isNotEmpty(l250m01bs)) {
					StringBuffer sb = new StringBuffer();
					if ("Y".equals(meta.getAllCanPay())) {

						sb.append("全部動用");

					} else {
						for (L250M01B l250m01b : l250m01bs) {
							String cntrNo = l250m01b.getCntrNo();
							sb.append(cntrNo + ",");
						}
						sb.delete(sb.length() - 1, sb.length());
					}

					result.set("cntrNos", sb.toString());
				}

				result.set(
						"ownBrId",
						StrUtils.concat(user.getUnitNo(), " ",
								branchService.getBranchName(user.getUnitNo())));
				result.set("docStatus",
						getMessage("docStatus." + meta.getDocStatus()));
				result.set("typCd", getMessage("typCd." + meta.getTypCd()));
				break;
			case 2:
				JSONArray listData = lms2501Service.getClsSavedList(meta);
				result.set("listData", listData.toString());
				result.add(new CapAjaxFormResult(meta
						.toJSONObject(new String[] { "project1", "project2",
								"project3", "project4", "project5", "project6",
								"project7" }, null)));
				break;
			default:
				break;
			}

			result.set("showTypCd", getMessage("typCd." + meta.getTypCd()));
			result.set("showCustId", StrUtils.concat(meta.getCustId(), " ",
					meta.getDupNo(), " ", meta.getCustName()));

		}

		// 文件唯讀判斷
		// result.set(EloanConstants.DOC_READONLY, CreditDocStatusEnum.海外_編製中
		// .getCode().equals(meta.getDocStatus()) ? false : true);

		// common 要求塞值欄位
		result.set(EloanConstants.MAIN_OID, meta.getOid());
		result.set(EloanConstants.MAIN_ID, meta.getMainId());
		result.set(EloanConstants.MAIN_UID, meta.getUid());
		// result.set(EloanConstants.DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());

		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult reNewMetaDoc(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = null;

		if (StringUtils.isEmpty(oid)) {
			meta = new L250M01A();
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			meta.setRptId("CLS");
			meta.setOwnBrId(user.getUnitNo());
			meta.setMainId(IDGenerator.getUUID());
			meta.setApprId(user.getUserId());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			meta.setTxCode(txCode);
			// UPGRADE: 待確認，URL是否正確
			meta.setDocURL(params.getString("docUrl"));

		} else {
			meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		}

		String caseMainId = params.getString("caseMainId");
		L120M01A l120m01a = cls1141Service.findL120m01aByMainId(caseMainId);
		meta.setCustId(l120m01a.getCustId());
		meta.setDupNo(l120m01a.getDupNo());
		meta.setCustName(l120m01a.getCustName());
		meta.setTypCd(l120m01a.getTypCd());
		meta.setCaseYear(l120m01a.getCaseYear());
		meta.setCaseDate(l120m01a.getCaseDate());
		meta.setCaseNo(l120m01a.getCaseNo());
		meta.setCaseSeq(l120m01a.getCaseSeq());
		meta.setRandomCode(IDGenerator.getRandomCode());
		meta.setSrcMainId(l120m01a.getMainId());

		String[] selectCntrNoOids = params.getStringArray("caseOids");

		List<L250M01B> newL250m01bs = new ArrayList<L250M01B>();
		// 避免重複的額度序號
		HashMap<String, String> cntrNoMap = new HashMap<String, String>();
		for (String cntrNoOid : selectCntrNoOids) {
			L140M01A l140m01a = cls1161Service.findModelByOid(L140M01A.class,
					cntrNoOid);
			String cntrNo = l140m01a.getCntrNo();
			if (cntrNoMap.containsKey(cntrNo)) {
				continue;
			}

			cntrNoMap.put(cntrNo, "");

			L250M01B l250m01b = new L250M01B();
			l250m01b.setCntrNo(cntrNo);
			l250m01b.setMainId(meta.getMainId());
			l250m01b.setReMainId(l140m01a.getMainId());
			l250m01b.setCreator(user.getUserId());
			l250m01b.setCreateTime(CapDate.getCurrentTimestamp());
			l250m01b.setUpdater(user.getUserId());
			l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			newL250m01bs.add(l250m01b);

		}

		lms2501Service.save(meta, newL250m01bs);
		params.put(EloanConstants.MAIN_OID, meta.getOid());

		return initForm(params);

	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult genList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		lms2501Service.removeLmsCheckList(meta);

		String list = lms2501Service.genCLSCheckList();
		int currentVersion = lms2501Service.getVersion("CLS");
		meta.setVersion(currentVersion);
		lms2501Service.save(meta);
		result.set("checkList", list);

		List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
				.findListByMainId(L250M01B.class, meta.getMainId());

		// 青年安心成家貸款 57, 59, 66
		// 歡喜房貸 31
		// 行家理財貸款 02, 03, 68

		boolean is57 = false;
		boolean is59 = false;
		boolean is66 = false;

		boolean is31 = false;

		boolean isProject3 = false;
		boolean isProject4 = false;
		boolean isProject5 = false;

		// 行家理財
		boolean isProject6 = false;
		// 其它
		boolean isProject7 = false;
		if (CollectionUtils.isNotEmpty(l250m01bs)) {

			for (L250M01B l250m01b : l250m01bs) {
				String cntrNoMainId = l250m01b.getReMainId();

				L140M01A l140m01a = cls1161Service.findModelByMainId(
						L140M01A.class, cntrNoMainId);
				List<L140S02A> l140s02as = clsService.findL140S02A(l140m01a);

				for (L140S02A l140s02a : l140s02as) {
					String prodKind = l140s02a.getProdKind();
					String subjCode = Util.trim(l140s02a.getSubjCode());
					if (ProdService.ProdKindEnum.青年安心成家貸款_57.getCode().equals(
							prodKind)) {
						is57 = true;
					} else if (ProdService.ProdKindEnum.青年安心成家貸款_第二案_59
							.getCode().equals(prodKind)) {
						is59 = true;
					} else if (ProdService.ProdKindEnum.金門青年安心成家_66.getCode()
							.equals(prodKind)) {
						is66 = true;
					} else if (ProdService.ProdKindEnum.房貸專案_B案_100億_31
							.getCode().equals(prodKind)) {
//						is31 = true;
						/*
						403	13500100	一般中期擔保放款　
						603	14501000	一般長期擔保放款　　
						*/
						if(Util.equals(subjCode, "13500100")||Util.equals(subjCode, "14501000")){
							isProject6 = true ; //歡喜房貸內的行家理財
						}else{
						is31 = true;
						}
						
					} else if (ProdService.ProdKindEnum.行家理財貸款_短期_02.getCode()
							.equals(prodKind)
							|| ProdService.ProdKindEnum.行家理財貸款_中長期_03.getCode()
									.equals(prodKind)
								|| ProdService.ProdKindEnum.行家理財中期循環_68.getCode()
									.equals(prodKind)) {
						// 判斷是否為行家理財
						isProject6 = true;
					}

				}

				if (isProject6) {
					// 帶出行家理財所使用的擔保品
					List<L140M01O> l140m01os = (List<L140M01O>) cls1151Service
							.findModelListByMainId(L140M01O.class,
									l140m01a.getMainId());

					if (CollectionUtils.isNotEmpty(l140m01os)) {
						for (L140M01O l140m01o : l140m01os) {

							// l140m01o.collTyp1=01 不動產
							// collTyp1=03 , collTyp2=01 定存單
							// collTyp1=03 , collTyp2=07 股票

							if (UtilConstants.CollTyp1.不動產.equals(l140m01o
									.getCollTyp1())) {
								isProject3 = true;
							} else if (UtilConstants.CollTyp1.權利質權
									.equals(l140m01o.getCollTyp1())) {
								if (UtilConstants.PLEDGEOFRIGHTS.銀行定存單
										.equals(l140m01o.getCollTyp2())) {
									isProject4 = true;
								} else if (UtilConstants.PLEDGEOFRIGHTS.股票
										.equals(l140m01o.getCollTyp2())) {
									isProject5 = true;
								}

							} else {
								isProject7 = true;
							}

						}
					} else {
						isProject7 = true;
					}

				}
			}

		}

		if (is57 || is59 || is66) {
			result.set("project1", "Y");
		}

		if (is31) {
			result.set("project2", "Y");
		}

		if (isProject3) {
			result.set("project3", "Y");
		}

		if (isProject4) {
			result.set("project4", "Y");
		}
		if (isProject5) {
			result.set("project5", "Y");
		}

		if (isProject6) {
			result.set("project6", "Y");
		}

		if (isProject7) {
			result.set("project7", "Y");
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult deleteMeta(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta
				.getMainId());

		if (lockedUser == null) {
			lms2501Service.deleteLmsMeta(meta);
		} else {
			String message = getPopMessage("EFD0055", lockedUser);
			result.set("deleteMessage", message);
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = true)
	public IResult checkBeforSend(PageParameters params) throws CapException {
		Map<String, String> messageSet = new HashMap<String, String>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A meta = lms2501Service.findModelByOid(L250M01A.class, oid);

		int currentVersion = lms2501Service.getVersion("CLS");
		int metaVersion = meta.getVersion();

		if (currentVersion != metaVersion) {
			throw new CapMessageException("檢核表非最新項目，請重新引進", this.getClass());
		}

		JSONArray dataArray = lms2501Service.getClsSavedList(meta);

		if (CollectionUtils.isEmpty(dataArray)) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", "檢核表未填寫"), getClass());
		}
		
		String project1 = meta.getProject1();
		String project2 = meta.getProject2();
		String project3 = meta.getProject3();
		String project4 = meta.getProject4();
		String project5 = meta.getProject5();
		String project6 = meta.getProject6();
		String project7 = meta.getProject7();

		String[] projects = new String[] { project1, project2, project3,
				project4, project5, project6, project7 };

		boolean hasProject = false;
		for (String project : projects) {
			if ("Y".equals(project)) {
				hasProject = true;
				break;
			}
		}

		if (!hasProject) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", "檢核表的產品種類尚未填寫"), getClass());
		}

		for (int k = 0; k < 2; k++) {
			if (k == 0) {

				if ("Y".equals(project1) || "Y".equals(project2)) {

				} else {
					continue;
				}

			} else if (k == 1) {

				if ("Y".equals(project3) || "Y".equals(project4)
						|| "Y".equals(project5) || "Y".equals(project6)
						|| "Y".equals(project7)) {

				} else {
					continue;
				}
			}

			JSONArray typeGroups = dataArray.getJSONObject(k).getJSONArray(
					"groups");

			for (int i = 0; i < typeGroups.size(); i++) {
				JSONArray subItems = typeGroups.getJSONObject(i).getJSONArray(
						"subItems");

				boolean hasY = false;
				String mainTitle = "";
				for (int j = 0; j < subItems.size(); j++) {
					String subValue = subItems.getJSONObject(j).getString(
							"subValue");
					String subTitle = subItems.getJSONObject(j).getString(
							"subTitle");
					if(Util.isEmpty(Util.trim(subTitle)) && subItems.size()==1){
						hasY = true;
					}
					if (j == 0) {
						mainTitle = subTitle;
					}
					if ("Y".equals(subValue)) {
						hasY = true;
					}
				}

				if (!hasY) {
					messageSet.put("colName", "【檢核表】" + mainTitle);
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0005", messageSet), getClass());
				}

			}
		}

		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));

		//J-108-0217_10702_B1001 檢核若為專案為08特定金錢信託受益權自行設質擔保授信時，需選取語音檔
		List<L250M01B> l250m01bs = (List<L250M01B>) lms2501Service
			.findListByMainId(L250M01B.class, meta.getMainId());
		for(L250M01B l250m01b:l250m01bs){
			String reMainId=l250m01b.getReMainId();
			L140M01A l140m01a = cls1161Service.findModelByMainId(L140M01A.class, reMainId);
			String custName = l140m01a.getCustName();
			String projClass = l140m01a.getProjClass();
			
			if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信) && Util.isEmpty(l250m01b.getIVRFlag())){
				throw new CapMessageException(RespMsgHelper.getMessage(
						"EFD0005", custName + pop.getProperty("cls3301v00.error.01")), getClass());
			}
		}
		
		if(Util.equals(project6, "Y")){ //當勾選 模擬動審表 右邊的[V]行家理財
			boolean need_check_l250s02b_30_buyInsurance = false;
			boolean need_check_l250s02b_31_buyInsurance = false;
			String l250s02b_30_itemContent = "";
			String l250s02b_31_itemContent = "";
			for(L250S02B l250s02b : lms2501Service.findL250S02B(meta.getMainId())){
				if(l250s02b.getType()==2 && (l250s02b.getGroup()== UtilConstants.L250S01B_TYPE2.GROUP_30 )){
					if(l250s02b.getSubItem()==1){
						need_check_l250s02b_30_buyInsurance = !Util.equals("Y", l250s02b.getSubValue());
						l250s02b_30_itemContent = l250s02b.getSubTitle();
					}else if(l250s02b.getSubItem()==2){
						//不適用
					}
				}
				if(l250s02b.getType()==2 && (l250s02b.getGroup()== UtilConstants.L250S01B_TYPE2.GROUP_31)){
					if(l250s02b.getSubItem()==1){
						need_check_l250s02b_31_buyInsurance = !Util.equals("Y", l250s02b.getSubValue());
						l250s02b_31_itemContent = l250s02b.getSubTitle();
					}else if(l250s02b.getSubItem()==2){
						//不適用
					}
				}
			}				
			if(need_check_l250s02b_30_buyInsurance && clsService.is_function_on_codetype("J-108-0351_chk_l250type2_group_30")){
				//團貸子戶，如其中有部分是買「類保險商品」
				check_J_108_0351_needChoose(meta, l250m01bs, l250s02b_30_itemContent);
			}
			if(need_check_l250s02b_31_buyInsurance && clsService.is_function_on_codetype("J-108-0351_chk_l250type2_group_31")){
				//團貸子戶，如其中有部分是買「類保險商品」
				check_J_108_0351_needChoose(meta, l250m01bs, l250s02b_31_itemContent);
			}
		}
		return result;
	}

	private boolean has_L140S02A_lnPurs4(L140M01A l140m01a){
		for(L140S02A l140s02a : clsService.findL140S02A(l140m01a)){
			String property = l140s02a.getProperty();
			if (UtilConstants.Cntrdoc.Property.不變.equals(property)
					|| UtilConstants.Cntrdoc.Property.取消.equals(property)) {
				continue;
			}
			if(Util.equals(l140s02a.getLnPurs(), "4")){
				return true;
			}
		}
		return false;
	}
	private void check_J_108_0351_needChoose(L250M01A meta, List<L250M01B> l250m01b_list, String itemContent)
	throws CapMessageException{
		Map<String, TreeSet<String>> caseMainId_cntrNoSet_map = new LinkedHashMap<String, TreeSet<String>>();
		for(L250M01B l250m01b:l250m01b_list){
			String cntrNo = l250m01b.getCntrNo();
			String tabMainId = l250m01b.getReMainId();
			L140M01A l140m01a = clsService.findL140M01A_mainId(tabMainId);
			if(l140m01a!=null){
				if(!has_L140S02A_lnPurs4(l140m01a)){
					continue;
				}
				//~~~~~~~~~~~~~~~~~
				L120M01C l120m01c = l140m01a.getL120m01c();
				if(l120m01c!=null){
					String caseMainId = l120m01c.getMainId();
					if(!caseMainId_cntrNoSet_map.containsKey(caseMainId)){
						caseMainId_cntrNoSet_map.put(caseMainId, new TreeSet<String>());
					}
					caseMainId_cntrNoSet_map.get(caseMainId).add(cntrNo);
				}
			}
		}
		for(String caseMainId : caseMainId_cntrNoSet_map.keySet()){
			String cntrNodesc = StringUtils.join(caseMainId_cntrNoSet_map.get(caseMainId), "、");
			List<L140M04A> l140m04a_list = clsService.findL140M04A(caseMainId);
			boolean buyInsurance = false;
			String l140m04a_checkContent = "";
			for(L140M04A l140m04a: l140m04a_list){
				if(Util.equals(UtilConstants.C900S01A_CHECKCODE.第187項, l140m04a.getCheckCode()) && Util.equals("Y", l140m04a.getCheckYN())){
					buyInsurance = true;
					l140m04a_checkContent = Util.trim(l140m04a.getCheckContent());
					break;
				}	
			}			
			if(buyInsurance){
				L120M01A l120m01a = clsService.findL120M01A_mainId(caseMainId);
				throw new CapMessageException("額度序號"+cntrNodesc+" 所屬簽報書 "+Util.toSemiCharString(l120m01a.getCaseNo())
						+" 之查核事項【"+l140m04a_checkContent+"】為「是」"
						+"<br/>"
						+"模擬動審之檢核項目【"+ itemContent+"】需勾選。", getClass());
			}
		}
	}
	
	@DomainAuth(value = AuthType.Modify + AuthType.Accept, CheckDocStatus = false)
	public IResult flowAction(PageParameters params) throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L250M01A mainModel = lms2501Service.findModelByOid(L250M01A.class, oid);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String account = Util.trim(params.getString("account"));
		if (!Util.isEmpty(account)) {
			//String manager = Util.trim(params.getString("manager"));
			mainModel.setBossId(account);
			//mainModel.setManagerId(manager);
		}

		if (!Util.isEmpty(mainModel)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {

					if (!"back".equals(params.getString("flowAction"))) {
						if (user.getUserId().equals(mainModel.getApprId())) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}
					}
					if ("ok".equals(params.getString("flowAction"))) {
						mainModel.setApproveTime(CapDate.getCurrentTimestamp());
						mainModel.setApprover(user.getUserId());
						mainModel.setReCheckId(user.getUserId());
					}
				}
				lms2501Service.flowAction(mainModel.getOid(), mainModel,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms2501Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
		return new CapAjaxFormResult();
	}
	//J-108-0217_10702_B1001 模擬動審IVR語音系統查詢
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult addIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		List<String> datas=Arrays.asList(params.getStringArray("rows"));
		if(!Util.isEmpty(datas) && datas.size()>0){
			cls1161Service.saveIVRFlag(params.getString(EloanConstants.MAIN_OID),datas);
		}
		return result;
	}
	@SuppressWarnings("unchecked")
	@DomainAuth(AuthType.Modify)
	public IResult deleteIVRList(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid=params.getString(EloanConstants.MAIN_OID);
		String datas=params.getString("record_FileName");
		String deleteCustId=params.getString("deleteCustId");
		
		cls1161Service.deleteIVRFlag(oid , deleteCustId , datas);

		return result;
	}
}
