/* 
 * C004M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C004M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C004M01A;

/** 政策性留學生貸款報送資料 **/
@Repository
public class C004M01ADaoImpl extends LMSJpaDao<C004M01A, String>
	implements C004M01ADao {

	@Override
	public C004M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C004M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C004M01A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public C004M01A findByUniqueKey(String mainId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C004M01A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<C004M01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C004M01A> findByIndex02(String rptType, Date bgnDate){
		ISearch search = createSearchTemplete();
		List<C004M01A> list = null;
		if (rptType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rptType", rptType);
		if (bgnDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "bgnDate", bgnDate);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}