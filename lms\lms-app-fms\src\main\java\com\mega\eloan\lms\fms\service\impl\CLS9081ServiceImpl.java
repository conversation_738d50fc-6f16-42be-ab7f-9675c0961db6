package com.mega.eloan.lms.fms.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.service.CLS9081Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service
public class CLS9081ServiceImpl extends AbstractCapService implements CLS9081Service {
	private static int rowHeightAt14 = 360;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	ICustomerService customerService; 
	
	@Override
	public void genExcel(ByteArrayOutputStream outputStream, String yyyy_MM, String brNo)
			throws IOException, WriteException {
		
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		if (true) {
			// ---
			workbook = Workbook.createWorkbook(outputStream);
			sheet1 = workbook.createSheet("明細", 0);

			// ======
			WritableFont headFont_memo = new WritableFont(
					WritableFont.createFont("標楷體"), 10);
			WritableCellFormat cellFormat_memo = new WritableCellFormat(headFont_memo);
			{
				cellFormat_memo.setAlignment(Alignment.LEFT);
				cellFormat_memo.setWrap(false);
			}
			// ======
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatL.setWrap(true);
			}

			WritableCellFormat cellFormatL_Border = new WritableCellFormat(
					cellFormatL);
			{
				cellFormatL_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}

			WritableCellFormat cellFormatR_Border = new WritableCellFormat(
					cellFormatR);
			{
				cellFormatR_Border.setBorder(Border.ALL, BorderLineStyle.THIN);
			}
			// ======
			WritableFont headFont14 = new WritableFont(
					WritableFont.createFont("標楷體"), 14);
			WritableCellFormat cellFormatC_14 = new WritableCellFormat(
					headFont14);
			{
				cellFormatC_14.setAlignment(Alignment.LEFT);
				cellFormatC_14.setWrap(true);
			}
			//======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("統編", 14);
			headerMap.put("重複碼", 8);
			headerMap.put("姓名", 30);
			headerMap.put("額度序號", 16);
			headerMap.put("帳號", 18);
			headerMap.put("餘額(TWD)", 16);
			headerMap.put("利率", 14);
			
			int totalColSize = headerMap.size();
			
			//======
			List<String[]> rows = new ArrayList<String[]>();
			
			List<String> cntrNoList = new ArrayList<String>();
			for(Map<String, Object> row : eloandbBASEService.findCntrNoByC120S01B_comname("外交部")){
				cntrNoList.add(Util.trim(MapUtils.getString(row, "CNTRNO")));	
			}
			
			//Map<String, String> idDup_name_map = new HashMap<String, String>();
			for(Map<String, Object> map : misdbBASEService.findLNF155_ym_br_cntrNo(yyyy_MM, brNo, cntrNoList)){
				
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				int col = 0;
				
				String id = Util.trim(MapUtils.getString(map, "LNF155_CUST_ID"));
				String dup = Util.trim(MapUtils.getString(map, "LNF155_CUST_DUP"));
				arr[col++] = id;
				arr[col++] = dup;
				//arr[col++] = getCustName(idDup_name_map, id, dup);
				arr[col++] = Util.trim(MapUtils.getString(map, "CNAME"));
				arr[col++] = Util.trim(MapUtils.getString(map, "LNF155_CONTRACT"));
				arr[col++] = Util.trim(MapUtils.getString(map, "LNF155_LOAN_NO"));
				arr[col++] = LMSUtil.pretty_numStr((BigDecimal)MapUtils.getObject(map, "LNF155_LOAN_BAL_TW"));
				arr[col++] = LMSUtil.pretty_numStr((BigDecimal)MapUtils.getObject(map, "LNF155_RATE"));
				
				// ---
				rows.add(arr);	
			}

			// ==============================
			int rowIdx = 0;
			// ==============================
			if (true) {
				sheet1.setRowView(rowIdx, rowHeightAt14, false);
				sheet1.mergeCells(0, rowIdx, totalColSize - 1, rowIdx);
				sheet1.addCell(new Label(0, rowIdx, "分行："+brNo+"  資料日："+yyyy_MM,
						cellFormatC_14));
			}
			// ==============================
			rowIdx++;
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label(colIdx, rowIdx, h, cellFormatL_Border));
				// ---
				colIdx++;
			}
			// ==============================
			if(true){
				for (String[] arr : rows) {
					int colLen = arr.length;
					if(true){
						rowIdx++;	
					}						
					for (int i_col = 0; i_col < colLen; i_col++) {
						sheet1.addCell(new Label(i_col, rowIdx, arr[i_col],
								(i_col == 5||i_col == 6) ? cellFormatR_Border
										: cellFormatL_Border));
					}			
				}	
			}			
			// ---
			workbook.write();
			workbook.close();
		}	
	}
	
	
	@SuppressWarnings("unused")
	private String getCustName(Map<String, String> idDup_name_map, String id, String dup){
		String key = LMSUtil.getCustKey_len10custId(id, dup);
		if(!idDup_name_map.containsKey(key)){
			Map<String, Object> custMap = customerService.findByIdDupNo(id, dup);
			String name = Util.trim(MapUtils.getString(custMap, "CNAME"));
			idDup_name_map.put(key, name);
		}
		return Util.trim(idDup_name_map.get(key));		
	}	
}
