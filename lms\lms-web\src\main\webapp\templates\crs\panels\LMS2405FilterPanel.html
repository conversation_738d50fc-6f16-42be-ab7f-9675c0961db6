<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <div class="" id="">
                        <table class="tb2" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                                <tr>
                                    <td style="text-align: rifht;width: 40%;" class="hd1">
                                        <th:block th:text="#{'LMS2405Filter.Date'}">實際覆審日期</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <input type="text" size="7" maxlength="7" name="checkDateStart" id="checkDateStart">
										(YYYY-MM)
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: rifht;width: 30%;" class="hd1">
                                        <th:block th:text="#{'LMS2405Filter.branch'}">分行名稱</th:block>&nbsp;&nbsp;
                                    </td>
                                    <td>
                                        <select name="brIdFilter" id="brIdFilter" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <script>
                var FilterAction = {
                    formId: "#filterForm",
                    gridId: "#gridview",
                    openBox: function(){
                        var $form = $(this.formId).reset();
                        if ($("#brIdFilter option").length == 0) {
							
	                        $.ajax({
	                            type: "POST",
	                            async: false,
	                            handler: "lms2405m01formhandler",
	                            data: {
	                                formAction: "queryBranch"
	                            },
	                            success: function(obj){
									//addspace
									$("#brIdFilter").setItems({ item: {}, format: "{value} {key}", clear:true, space: true });
									
	                                $.each(obj.itemOrder, function(idx, brNo) {
					            		var currobj = {};
					            		currobj[brNo] = obj.item[brNo] ;
					            		//依 itemOrder, 一個一個append, 把 clear 指為 false
					            		
					            		//select
										$("#brIdFilter").setItems({ item: currobj, format: "{value} {key}", clear:false, space: false });
									});
	                            }
	                        });
                        }
                        $("#filterBox").thickbox({
                            //query=查詢
                            title: i18n.def["query"],
                            width: 400,
                            height: 180,
                            modal: true,
                            i18n: i18n.def,
                            readOnly: false,
                            align: "center",
                            valign: "bottom",
                            buttons: {
                                "sure": function(){
                                    var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
									var v = $("#checkDateStart").val();
                                    if (v!="" && !$("#checkDateStart").val().match(regYYYYMM)) {
                                        //val.date2=日期格式錯誤(YYYY-MM)
                                        return CommonAPI.showMessage(i18n.def["val.date2"]);
                                    }
                                    if (!$form.valid()) {
                                        return false;
                                    }
                                    FilterAction.reloadGrid(JSON.stringify($form.serializeData()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    /**更新grid
                     *
                     * @param {Object} data 查詢條件
                     */
                    reloadGrid: function(data){
                        $(this.gridId).jqGrid("setGridParam", {
                            postData: {
                                formAction: "queryMain",
                                docStatus: viewstatus,
                                filetData: data
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                    }
                };
            </script>
        </th:block>
    </body>
</html>
