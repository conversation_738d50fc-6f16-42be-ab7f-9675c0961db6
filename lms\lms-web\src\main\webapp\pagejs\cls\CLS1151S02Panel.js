String.prototype.startsWith = function(str){
    return (this.match('^' + str) == str)
}

var _PanelAction02 = {
    isInit: false,
    initAction: function(){
        $("#pullinL140S01AForm").find("#custPos").setItems({
            space: true,
            item: API.loadCombos("L140S01A_custPos")["L140S01A_custPos"],
            format: "{value} - {key}",
            sort: "asc"
        });
        
        $("#addL140S01AForm").find("#addL140S01A_custPos").setItems({
            space: true,
            item: API.loadCombos("L140S01A_custPos")["L140S01A_custPos"],
            format: "{value} - {key}",
            sort: "asc"
        });
        
        //讓頁面抓 codetype
        $("#pullinL140S01AForm").buildItem();
        $("#addL140S01AForm").buildItem();
        
        if (!_M.isReadOnly) {
            if (_M.isByHeadAndArea()) {
                $("#addL140S01A").show();
            } else {
                $("#addL140S01A").hide();
            }
        }
        this.initEven();
        this.initGrid();
        //判斷若有舊案借款人才需要show該字樣
        var showPeople = $.trim(_M.AllFormData["02"]["showPeople"]);
        if (showPeople) {
            $("#showPeopleTile").show();
        } else {
            $("#showPeopleTile").hide();
        }
    },
    /**
     *載入頁面後的動作
     * */
    afterAction: function(){
        $("#showPeople").html(_M.AllFormData["02"]["showPeople"]);
        //this.reload_L140S01AGrid();
        if (!_M.isReadOnly) {
            if (_M.isByHeadAndArea()) {
                $("#addL140S01A").show();
                $("#pullinL140S01A").hide();
            } else {
                $("#addL140S01A").hide();
                $("#pullinL140S01A").show();
            }
        }
    },
    _L140S01AGrid: null,
    _C120S01AGrid: null,
    /**
     *更新共同借款人grid
     */
    reload_L140S01AGrid: function(){
        _PanelAction02._L140S01AGrid.jqGrid("setGridParam", {
            postData: {
                formAction: "queryL140S01A",
                tabFormMainId: _M.tabMainId
            },
            search: true
        }).trigger("reloadGrid");
    },
    
    show_area_guaPercent: function(custPos_val){
    	if(_M.AllFormData.cntrNoBelongCLS=="Y"){
			if(custPos_val=="N"){
				 return true;
            } 
		}else{
			if(custPos_val=="N" || custPos_val=="G" ){
				 return true;
            } 
		}
    	return false;
    },
    initEven: function(){
        /**
         *引進共同借款人
         */
        $("#pullinL140S01A").click(function(){
            _PanelAction02.pullinL140S01A();
        });
        
        /**
         *新增借款人
         */
        $("#addL140S01A").click(function(){
            _PanelAction02.addL140S01A();
        });
        /**
         *查詢
         */
        $("#queyBy0024").click(function(){
            _PanelAction02.queyBy0024();
        });
        
        /**
         * 刪除共同借款人
         */
        $("#deleteL140S01A").click(function(){
            _PanelAction02.deleteL140S01A();
        });
        /**
         * 篩選借款人
         */
        $("#findIdBt").click(function(){
            _PanelAction02._C120S01AGrid.reload({
                mainId: _M.CaseMainId,
                tabFormMainId: _M.tabMainId,
                findId: $("#findId").val()
            });
        });
    
        if(true){
        	$('#pullinL140S01AForm').find('#rKindM').change(function(){
                var value = $(this).val() + '';
                var $form = $('#pullinL140S01AForm');
                $form.find('.rKindD').hide().val('');
                switch (value) {
                    case '1':
                    case '2':
                        var $obj = $form.find('#rKindD' + value);
                        $obj.val($form.find('#rKindD').val()).show();
                        break;
                    case '3':
                        var s = ($form.find('#rKindD').val() || '').split('');
                        $form.find('#rKindD31').val(s.length >= 2 ? s[0] : '').show();
                        $form.find('#rKindD32').val(s.length >= 2 ? s[1] : '').show();
                        break;
                }
				
				//當關係類別為2-親屬關係, 非X0-本人時, 顯示與借款人同住選項
				_PanelAction02.isShowLiveWithBorrowerItem(value, $form.find('#rKindD'+ value).val(), $form);
				
            });
        	
        	$('#pullinL140S01AForm').find('#rKindD1,#rKindD2').change(function(){
        		$('#pullinL140S01AForm').find('#rKindD').val($(this).val());
				var formObject = $('#pullinL140S01AForm');
				_PanelAction02.isShowLiveWithBorrowerItem(formObject.find('#rKindM').val(), $(this).val(), formObject);
            });
        	
        	$('#pullinL140S01AForm').find('#rKindD31,#rKindD32').change(function(){
        		$('#pullinL140S01AForm').find('#rKindD').val($('#rKindD31').val() + $('#rKindD32').val());
            });
        	
        	
        	$("#pullinL140S01AForm").find("#reson").change(function(){
        		$('#resonOther').hide();
                if ($(this).val() == '99') 
                    $('#resonOther').show();
        	});
        	
        	 $("#pullinL140S01AForm").find("#custPos").change(function(){
        		 var custPos_val = $(this).val();
        		 
                 if( _PanelAction02.show_area_guaPercent($(this).val()) ){
                	 $("#pullinL140S01AForm").find(".area_guaPercent").show();
                 }else{
                	 $("#pullinL140S01AForm").find(".area_guaPercent").hide();
                 }

                 if(custPos_val=="N"){
                	 $("#pullinL140S01AForm").find(".area_reson").show();
                 }else{
                	 $("#pullinL140S01AForm").find(".area_reson").hide();
                 }
        	 });
        	 //======================
        	$('#addL140S01AForm').find('#addL140S01A_rKindM').change(function(){
                var value = $(this).val() + '';
                var $form = $('#addL140S01AForm');
                $form.find('.rKindD').hide().val('');
                switch (value) {
                    case '1':
                    case '2':
                        var $obj = $form.find('#addL140S01A_rKindD' + value);
                        $obj.val($form.find('#addL140S01A_rKindD').val()).show();
                        break;
                    case '3':
                        var s = ($form.find('#addL140S01A_rKindD').val() || '').split('');
                        $form.find('#addL140S01A_rKindD31').val(s.length >= 2 ? s[0] : '').show();
                        $form.find('#addL140S01A_rKindD32').val(s.length >= 2 ? s[1] : '').show();
                        break;
                }
				//當關係類別為2-親屬關係, 非X0-本人時, 顯示與借款人同住選項
				_PanelAction02.isShowLiveWithBorrowerItem(value, $form.find('#addL140S01A_rKindD' + value).val(), $form);
            });
        	
        	$('#addL140S01AForm').find('#addL140S01A_rKindD1,#addL140S01A_rKindD2').change(function(){
        		$('#addL140S01AForm').find('#addL140S01A_rKindD').val($(this).val());
				var formObject = $('#addL140S01AForm');
				_PanelAction02.isShowLiveWithBorrowerItem(formObject.find('#addL140S01A_rKindM').val(), $(this).val(), formObject);
            });
        	
        	$('#addL140S01AForm').find('#addL140S01A_rKindD31,#addL140S01A_rKindD32').change(function(){
        		$('#addL140S01AForm').find('#addL140S01A_rKindD').val($('#addL140S01A_rKindD31').val() + $('#addL140S01A_rKindD32').val());
            });
        	
        	
        	$("#addL140S01AForm").find("#addL140S01A_reson").change(function(){
        		$('#addL140S01A_resonOther').hide();
                if ($(this).val() == '99') 
                    $('#addL140S01A_resonOther').show();
        	});
        	 $("#addL140S01AForm").find("#addL140S01A_custPos").change(function(){
        		 var custPos_val = $(this).val();
        		 
        		 if( _PanelAction02.show_area_guaPercent($(this).val()) ){
                	 $("#addL140S01AForm").find(".area_guaPercent").show();
                 }else{
                	 $("#addL140S01AForm").find(".area_guaPercent").hide();
                 }
        		 
        		 if(custPos_val=="N"){
                	 $("#addL140S01AForm").find(".area_reson").show();
                 }else{
                	 $("#addL140S01AForm").find(".area_reson").hide();
                 }
        	 });
        };
		
		// 總處才可 疑似人頭戶檢核結果 link
        if (userInfo && (userInfo.ssoUnitNo || '').startsWith('9')) {
            $('#checkResultSpan').show();
        }
		
		$("#checkResult").click(function(){
			CommonAPI.triggerOpener($("#checkIsSuspectedHeadAccountResultGrid"), "reloadGrid");
			$("#checkIsSuspectedHeadAccountResultThickbox").thickbox({
	            title: i18n.cls1151s01["L140MC1A.checkResult.title"],
	            width: 720,
	            height: 730,
	            align: "center",
	            valign: "bottom",
	            i18n: i18n.def,
	            buttons: {
	                "close": function(){
	                    $.thickbox.close();
	                }
	            }
	        });
			
        });
    },
    /**
     * 查詢by0024
     */
    queyBy0024: function(){
    
        var $form = $("#addL140S01AForm");
        var custId = $form.find('#addL140S01A_custId').val()
        if (!custId) {
            var msg = i18n.def['compID'] + ' ' + i18n.def['val.required'];
            API.showErrorMessage(i18n.def["confirmTitle"], msg);
            return;
        }
        //綁入MegaID
        CommonAPI.openQueryBox({
            defaultValue: custId,//指定時會自動查詢 
            defaultCustType: '1', //2.英文名
            divId: 'addL140S01ADiv', //在哪個div 底下 
            doNewUser: true, //是否允許新客戶   (false:不出現新客戶選單)   
            autoResponse: { // 是否自動回填資訊 
                id: 'addL140S01A_custId', // 統一編號欄位ID 
                dupno: 'addL140S01A_dupNo', // 重覆編號欄位ID 
                name: 'addL140S01A_custName' // 客戶名稱欄位ID 
            }
            /*,
             fn : function(response){
             alert(response.custid);
             alert(response.dupno);
             alert(response.name);
             }
             */
        });
    },
    /**
     * 新增借款人
     */
    addL140S01A: function(){
        var $form = $("#addL140S01AForm");
        $form.reset();
        
        $form.find("#addL140S01A_guaPercent").val("100");
        $form.find(".area_guaPercent").hide();
        $form.find(".rKindD").hide();
        $form.find(".area_reson").hide();
        
        $("#addL140S01ABox").thickbox({
            //newData=新增
            title: i18n.def["newData"],
            width: 700,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$form.valid()) {
                        return false;
                    }
                    
                    var reson = "";
                    var resonOther = "";
                    if( $form.find("#addL140S01A_reson").is(":visible")){
                    	reson =  $form.find("#addL140S01A_reson").val();
                        resonOther =  $form.find("#addL140S01A_resonOther").val();
                    }
                    //===============
                    var guaPercent = "";
                    if( $form.find("#addL140S01A_guaPercent").is(":visible")){
                    	guaPercent = $("#addL140S01AForm").find("#addL140S01A_guaPercent").val();
                    }
                    
                    _M.doAjax({
                        action: "addL140S01A",                        
                        data:{
                        	addL140S01AForm: JSON.stringify(
                        						$.extend( $("#addL140S01AForm").serializeData(), 
                        								{'addL140S01A_guaPercent':guaPercent,
                        								 'addL140S01A_reson' : reson,
                        								 'addL140S01A_resonOther' : resonOther
                        								}
                        						)
                        					)
                        } ,
                        success: function(){
                            $.thickbox.close();
                            _PanelAction02.reload_L140S01AGrid();
                            //page2.002=請注意!!由此新增之客戶系統無法取得徵信及信評資訊。
                            API.showMessage(i18n.cls1151s01["page2.002"]);
                            
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     *引進共同借款人
     */
    pullinL140S01A: function(){
    	//J-113-0205 引進保證人檢核
    	_M.doAjax({
            action: "pullInCheck",
            data: {
                mainId : _M.CaseMainId,
                tabFormMainId : _M.tabMainId
            },
            success: function(json){
            	
            	if(json.errMsg){
            		API.showErrorMessage(DOMPurify.sanitize(json.errMsg));
            		return;
            	}
            	if(json.dsbLngeFlagGRsn){
            		$("#custPos").find("option[value=G]").attr("disabled", "disabled");
            		$("#dsbLngeFlagGRsn").html(DOMPurify.sanitize(json.dsbLngeFlagGRsn));
            	}else{
            		$("#custPos").find("option[value=G]").removeAttr("disabled");
            		$("#dsbLngeFlagGRsn").html("");
            	}

                var $form = $("#pullinL140S01AForm");
                $form.reset();
                $("#findId").val("");
                _PanelAction02._C120S01AGrid.reload({
                    mainId: _M.CaseMainId,
                    tabFormMainId: _M.tabMainId,
                    findId: ""
                });
                
                $form.find("#guaPercent").val("100");
                $form.find(".area_guaPercent").hide();
                $form.find(".rKindD").hide();
                $form.find(".area_reson").hide();
                
                $("#C120S01ABox").thickbox({
                    title: i18n.def['import'],
                    width: 600,
                    height: 550,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    buttons: {
                        "sure": function(){
                        
                            if (!$form.valid()) {
                                return false;
                            }
                            var $grid = _PanelAction02._C120S01AGrid;
                            var selectId = $grid.getGridParam('selarrrow');
                            if (!selectId || !selectId.length) {
                                //action_005=請先選取一筆以上之資料列
                                return API.showMessage(i18n.def["action_005"]);
                            }
                            
                            var oids = [];
                            for (var rowId in selectId) {
                                oids.push($grid.getRowData(selectId[rowId]).oid);
                            }
                            
                            var reson = "";
                            var resonOther = "";
                            if( $form.find("#reson").is(":visible")){
                            	reson =  $form.find("#reson").val();
                                resonOther =  $form.find("#resonOther").val();
                            }
                            //===============
                            var guaPercent = "";
                            if( $form.find("#guaPercent").is(":visible")){
                            	guaPercent = $form.find("#guaPercent").val();
                            }
                            _M.doAjax({
                                action: "pullinL140S01A",
                                data: {
                                    oids: oids,
                                    custPos: $form.find("#custPos").val(), 
                                    rKindM: $form.find("#rKindM").val(),
                                    rKindD: $form.find("#rKindD").val(),
                                    reson: reson,
                                    resonOther: resonOther, 
                                    guaPercent : guaPercent,
        							isLiveWithBorrower : $form.find("input[name=isLiveWithBorrower]:checked").val()
                                },
                                success: function(json){
                                    $.thickbox.close();
                                    if ($form.find("#custPos").val() == "G") {
                                        //page2.003=是否符合銀行法12-1規定辦理！！
                                        API.showMessage(i18n.cls1151s01["page2.003"]);
                                    }
                                    if(json.errMsg){
                                    	API.showMessage(json.errMsg);
                                    }
                                    
                                    _M.refresh_M_key(json);
                                    
                                    
                                    _PanelAction02.reload_L140S01AGrid();
                                }
                            });
                            
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    },
    /**
     *刪除共同借款人
     */
    deleteL140S01A: function(){
        var $grid = _PanelAction02._L140S01AGrid;
        var selectId = $grid.getGridParam('selarrrow');
        if (!selectId) {
            //action_005=請先選取一筆以上之資料列
            return API.showMessage(i18n.def["action_005"]);
        }
        
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var oids = [];
                for (var rowId in selectId) {
                    oids.push($grid.getRowData(selectId[rowId]).oid);
                }
                _M.doAjax({
                    action: "deleteL140S01A",
                    data: {
                        oids: oids
                    },
                    success: function(json){

                        _M.refresh_M_key(json);
                        
                    	_PanelAction02.reload_L140S01AGrid();
                    }
                });
            }
        });
    },
    /**
     * 初始化grid
     *
     */
    initGrid: function(){
        /**
         * 額度明細表借款人grid
         */
        this._L140S01AGrid = $("#L140S01AGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            rowNum: 10,
            action: "queryL140S01A",
            postData: {
                tabFormMainId: _M.tabMainId
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140S01A.custPos"],//性質,
                name: 'custPos',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.def["compID"],//統一編號,
                name: 'custId',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140M01A.custName"],//姓名/名稱,
                name: 'custName',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140S01A.type"],//新增
                name: 'typeStr',
                align: "left",
                width: 10,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140S01A.rKindD"],//關係類別細項
                name: 'rKindDStr',
                align: "left",
                width: 60,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140S01A.reson"],//借保原因
                name: 'reson',
                align: "left",
                width: 60,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140S01A.resonOther"],//借保原因其他
                name: 'resonOther',
                align: "left",
                width: 60,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140S01A.guaPercentStr"],//保證人負担保證責任比率
                name: 'guaPercentStr',
                align: "right",
                width: 20,
                sortable: false
            }, {
                colHeader: i18n.cls1151s01["L140S01A.isLiveWithBorrower"],//是否與借款人同住
                name: 'isLiveWithBorrower',
                align: "center",
                width: 20,
                sortable: false
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            
            }
        });
        /**
         * 簽報書借款人 grid
         */
        this._C120S01AGrid = $("#C120S01AGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            rowNum: 10,
            action: "queryC120M01A",
            sortname: "custId",
            sortorder: 'asc',
            postData: {
                mainId: _M.CaseMainId,
                tabFormMainId: _M.tabMainId
            },
            colModel: [{
                colHeader: i18n.def["compID"],//統一編號,
                name: 'custId',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140M01A.custName"],//姓名/名稱,
                name: 'custName',
                align: "left",
                width: 60,
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            
            }
        });
		
		this._L140MC1AGrid = $("#checkIsSuspectedHeadAccountResultGrid").iGrid({
            handler: _M.ghandle,
            height: 600,
			width:600,
            rownumbers: true,
            needPager: false,  //rowNum: 26,  不同的版本，對應的人頭戶態樣的筆數是變動
            action: "queryIsSuspectedHeadAccountCheckResult",
            postData: {
                tabFormMainId: _M.tabMainId
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140MC1A.itemCode"],//項目
                name: 'ITEM_CODE',
                align: "center",
                width: 10,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140MC1A.itemContext"],//項目內容
                name: 'ITEM_CONTEXT',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140MC1A.score"],//分數
                name: 'RESULT',
                align: "center",
                width: 10,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140MC1A.remarks"],//備註
                name: 'REMARKS',
                align: "left",
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140MC1A.version"],//版本
                name: 'VERSION',
                align: "left",
                width: 20,
                sortable: true
            }]
        });
    },
	
	isShowLiveWithBorrowerItem: function(relationType, relationship, formObject){

		if(relationType == '2' && relationship && relationship != 'X0'){
			formObject.find(".isLiveWithBorrowerSpan").each(function(){
				$(this).show();
			})
		}
		else{
			formObject.find(".isLiveWithBorrowerSpan").each(function(){
				$(this).hide();
			})
			formObject.find("input[name=isLiveWithBorrower]").attr('checked', false);
		}
	}
};

_M.pageInitAcion["02"] = _PanelAction02;


