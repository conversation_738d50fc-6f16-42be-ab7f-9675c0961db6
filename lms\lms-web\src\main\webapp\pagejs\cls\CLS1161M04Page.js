var initDfd = initDfd || $.Deferred(), inits = {
    
};
$(document).ready(function(){
    $.form.init({
        formId: "tabForm",
        formHandler: "cls1161m04formhandler",
        formPostData: {
            formAction: "initForm"
        },
        loadSuccess: function(sJson){
            //非編製中設為唯讀且不套用tempSave
            var tabForm = $("#tabForm");
            
            if (tabForm.find("#mainDocStatus").val() != "01O") {
                setIgnoreTempSave(true);
                
                var auth = (responseJSON ? responseJSON.Auth : {}); //權限
                if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
                    $("form").lockDoc();
                }
            }
            if (tabForm.find("#mainDocStatus").val() == "02O") {
                $("#btnCheck, #btnReturn").show();
            }
            initDfd.resolve(sJson);
        }
    });
    function acceptAction(){
        $("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
            //l230m01a.title22=覆核
            title: i18n.cls1161m04['l250m01a.message.07'],
            width: 100,
            height: 150,
            modal: true,
            readOnly: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    var val = $("[name=checkRadio]:checked").val();
                    if (!val) {
                        //l230m01a.title21=請選擇
                        return CommonAPI.showMessage(i18n.cls1161m04['l250m01a.message.04']);
                    }
                    $.thickbox.close();
                    switch (val) {
                        case "1":
                            //一般退回到編製中01O
                            //l230m01a.title23=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
                            CommonAPI.confirmMessage(i18n.cls1161m04['l250m01a.message.05'], function(b){
                                if (b) {
                                
                                
                                    $.ajax({
                                        type: "POST",
                                        handler: "cls1161m04formhandler",
                                        data: $.extend({
                                            formAction: "flowAction",
                                            mainOid: responseJSON.mainOid,
                                            txCode: responseJSON.txCode
                                        }, ({
                                            flowAction: "back"
                                        } || {})),
                                        success: function(){
                                            CommonAPI.triggerOpener("gridview", "reloadGrid");
                                            API.showPopMessage(i18n.def["runSuccess"], window.close);
                                        }
                                    });
                                }
                            });
                            break;
                        case "2":
                            //核定
                            //l230m01a.title24=該案件是否核准？確定請按【確定】，否則請按【取消】離開
                            CommonAPI.confirmMessage(i18n.cls1161m04["l250m01a.message.06"], function(b){
                                if (b) {
                                    //LMS2305Action.checkDate();
                                    $.ajax({
                                        type: "POST",
                                        handler: "cls1161m04formhandler",
                                        data: $.extend({
                                            formAction: "flowAction",
                                            mainOid: responseJSON.mainOid,
                                            txCode: responseJSON.txCode
                                        }, ({
                                            flowAction: "ok"
                                        } || {})),
                                        success: function(){
                                            CommonAPI.triggerOpener("gridview", "reloadGrid");
                                            API.showPopMessage(i18n.def["runSuccess"], window.close);
                                        }
                                    });
                                }
                            });
                            break;
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    };

var saveAction = function (sJson) {

    var sendObj = $.extend(
        {
            tabForm : JSON.stringify($("#tabForm").serializeData()), 
            formAction : "saveDoc",                                  
            page : responseJSON.page || $("#page").val() || "01",
            txCode : responseJSON.txCode,
            mainOid: $("#mainOid").val(),
            docUrl : "/cls/cls1161m04"
        },
        $("#tabForm").serializeData(),
        {
            listData: responseJSON.page == '02'
                    ? JSON.stringify(prasePage2ListData())
                    : []
        }
    );

    API.flowConfirmAction({
        message : false,
        handler : "cls1161m04formhandler",
        action  : "saveDoc",
        data    : sendObj,
        success : sJson.success
    });
};

    
    $("#buttonPanel").find("#btnSave").click(function(){
        var oid = $("#mainOid").val();
        if (oid) {
            saveAction({
                success: function(){
                    API.showMessage(i18n.def.saveSuccess);
                }
            });
        }
        
    }).end().find("#btnCheck").click(function(){
        acceptAction();
    }).end().find("#btnPrint").click(function(){
        var printAction = function(){
            $.form.submit({
                url: "../../simple/FileProcessingService",
                target: "_blank",
                data: {
                    mainId: responseJSON.mainId,
                    mainOid: $("#mainOid").val(),
                    fileDownloadName: "cls1601r04.pdf",
                    serviceName: "cls1161r04rptservice"
                }
            });
        }
        var save = $("#buttonPanel").find("#btnSave:visible");
        if (save.length) {
            saveAction({
                success: function(){
                    API.showMessage(i18n.def.saveSuccess);
                    printAction();
                }
            });
        }
        else {
            printAction();
        }
    }).end().find("#btnExit").click(function(){
        API.triggerOpener();
        window.close();
    }).end().find("#btnSend").click(function(){
        var oid = $("#mainOid").val();
        oid &&
        saveAction({
            success: function(){
                $.ajax({
                    handler: "cls1161m04formhandler",
                    data: {
                        formAction: "checkBeforSend"
                    },
                    success: function(json){
                        $(".boss").setItems({
                            item: json.bossList
                        });
                        //confirmApply=是否呈主管覆核？
                        CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
                            if (b) {
                                $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                                    //l230m01a.title20=呈主管覆核
                                    title: i18n.cls1161m04['l250m01a.message.01'],
                                    width: 500,
                                    height: 180,
                                    modal: true,
                                    valign: "bottom",
                                    align: "center",
                                    readOnly: false,
                                    i18n: i18n.def,
                                    buttons: {
                                        "sure": function(){
                                            var account = $("#accountPerson").val();
                                            //var manager = $("#manager").val();
                                            if (account == "") {
                                                //l230m01a.title11=授信主管
                                                return CommonAPI.showErrorMessage(i18n.cls1161m04['l250m01a.message.02']);
                                            }
                                            //if (manager == "") {
                                                //l230m01a.title10=經副襄理
                                            //    return CommonAPI.showErrorMessage(i18n.cls1161m04['l250m01a.message.03']);
                                            //}
                                            
                                            var sendData = {
                                                page: responseJSON.page,
                                                account: account
												//,manager: manager
                                            };
                                            $.ajax({
                                                type: "POST",
                                                handler: "cls1161m04formhandler",
                                                data: $.extend({
                                                    formAction: "flowAction",
                                                    mainOid: responseJSON.mainOid,
                                                    txCode: responseJSON.txCode
                                                }, (sendData || {})),
                                                success: function(){
                                                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                                                    API.showPopMessage(i18n.def["runSuccess"], window.close);
                                                }
                                            });
                                            
                                            
                                            $.thickbox.close();
                                        },
                                        
                                        "cancel": function(){
                                            $.thickbox.close();
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
        
        
    });
});


/**
 * 將第二2頁的查詢項目格式組成特定的json資料，上傳server
 */
var prasePage2ListData = function(){
    var sendData = [];
    
	sendData.push(getTypeJson("1"));
	sendData.push(getTypeJson("2"));
    
    return sendData;
}

var getTypeJson = function(type){
    var type1 = $("input[name^='subItem_" + type + "']");
    
    var groups = [];
    var baseGroup = -1;
    var subItems = [];
    type1.each(function(i, ob){
        var ob_name = $(ob).attr("name");
        var regex = /[\d]+/g;
        
        var match = ob_name.match(regex);
        var type = match[0];
        var group = match[1];
        var subItem = match[2];
        
        
        if (group != baseGroup) {
            groups.push(group);
        }
        baseGroup = group;
    });
    
    
    var theJson = {};
    var theGroups = [];
    
    for (var i = 0; i < groups.length; i++) {
        var theGroup = {};
        var theSubItems = [];

        $theSubItems = $("input[name^='subItem_" + DOMPurify.sanitize(type) + "_" + DOMPurify.sanitize(groups[i]) + "_']");
		
        $theSubItems.each(function(j, ob){
            var ob_name = $(ob).attr("name");
            var regex = /[\d]+/g;
            var match = ob_name.match(regex);
            var subItem = match[2];
            var subItemJson = {};
            subItemJson.subItem = "";
            subItemJson.subTitle = "";
            subItemJson.subValue = "";
            
            var _subTitle = $("span[name='subItemTitle_" + type + "_" + groups[i] + "_" + subItem + "']").val();
            if(subItem && $.trim(_subTitle)!=''){
            subItemJson.subItem = subItem;
            subItemJson.subTitle = _subTitle;
            subItemJson.subValue = $(this).is(":checked") ? "Y" : "N"
            }else{
            	//default valie
            	subItemJson.subItem = subItem||'';
            }
            theSubItems.push(subItemJson);
        });
        theGroup.group = groups[i];
        theGroup.subItems = theSubItems;
        theGroups.push(theGroup);
    }
    
    theJson.type = type;
    theJson.groups = theGroups;
    
    return theJson;
}

//畫面切換table所需設定之資料如無設定則直接切換
$.extend(window.tempSave, {
    handler: "cls1161m04formhandler",
    action: "tempSave",
    beforeCheck: function(){
        var tabForm = $("#tabForm");
        return true;
    },
	sendData: function () {
	    return $.extend(
	        {
	            formAction : "tempSave",
	            tabForm    : JSON.stringify($("#tabForm").serializeData()),
	            txCode     : responseJSON.txCode,
	            page       : responseJSON.page,
	            mainOid    : $("#mainOid").val(),
	            docUrl     : "/cls/cls1161m04"
	        },
	        $("#tabForm").serializeData(),
	        {
	            listData: responseJSON.page == '02'
	                    ? JSON.stringify(prasePage2ListData())
	                    : []
	        }
	    );
	}

});
