/* 
 * LMS9530GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.grid;

import java.sql.Timestamp;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.collections.map.LinkedMap;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.LPDFM01A;
import com.mega.eloan.lms.model.LPDFS01A;
import com.mega.eloan.lms.rpt.pages.LMS9530V01Page;
import com.mega.eloan.lms.rpt.service.LMS9530Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 舊案轉檔
 * </pre>
 * 
 * @since 2011/11/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/26,Vector,new
 *          </ul>
 */
@Scope("request")
@Controller("lms9530gridhandler")
public class LMS9530GridHandler extends AbstractGridHandler {

	@Resource
	LMS9530Service service;

	@Resource
	BranchService branch;

	final String[] types = { "lms", "cls", "lrs", "crs", "rpt", "log" };
	final String[] rpts = { "案件簽報書", "審核書", "小放會會議記錄", "動用審核表", "政策性留學生貸款報送項目",
			"覆審名單", "覆審報告表", "覆審工作底稿", "消金授信覆審考核表", "已敘做授信案件清單",
			"已敘做授信案件清單(總行批示意見)", "每月常董會報告事項彙總及申報案件數統計表", "各級授權範圍內承做授信案件統計表",
			"授信契約已逾期控制表", "信保案件未動用屆期清單", "分行敘做房屋貸款月報", "敘做無自用住宅購屋放款明細表",
			"已敘做個人消費金融業務月報表", "已敘做消金案件清單", "已敘做授信案件清單(營運中心)", "營運中心每日授權外授信案件清單",
			"疑似代辦案件訊息明細表"};
	final String[][] rptNameFormat = {
			{ "FLMS110M01", "FLMS120M01", "FLMS130M01", "FCLS114M01" },// 案件簽報
			{ "FCLS106M01", "FCLS107M01", "FCLS108M01", "FCLS109M01",
					"FCLS110M01", "FCLS111M01", "FCLS113M01", "FCLS118M01",
					"FCLS119M01" },// 審核書
			{ "FLMS150M01", "FCLS140R01" },// 小放會
			{ "FLMS160M01", "FCLS120R01" },// 動審
			{ "FCLS004M01" },// 政策留貸
			{ "FLMS180M01" },// 覆審名單
			{ "FLMS170M01", "FCLS130R01", "FCLS241R01" },// 覆審報告表
			{ "FLMS180M01" },// 覆審工作底稿
			{ "FCLS240R02" },// 消金授信覆審考核表
			{ "FLMS180M01" },// 已敘做授信案件清單
			{ "FLMS780M02" },// 已敘做授信案件清單(總行批示意見)
			{ "FLMS781M01" },// 每月常董會報告事項彙總及申報案件數統計表
			{ "FLMS781M02" },// 各級授權範圍內承做授信案件統計表
			{ "FLMS180M05" },// 授信契約已逾期控制表
			{ "FLMS180M10" },// 信保案件未動用屆期清單
			{ "FCLS250M01" },// 疑似代辦案件訊息明細表
			{ "FCLS150R01" },// 分行敘做房屋貸款月報
			{ "FCLS151M01" },// 敘做無自用住宅購屋放款明細表
			{ "FCLS152M01" },// 已敘做個人消費金融業務月報表
			{ "FCLS210M01 " },// 已敘做消金案件清單
			{ "FLMS180M02" },// 已敘做授信案件清單(區中心)
			{ "FLMS380M02" } };// 區域中心每日授權外授信案件清單

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */

	@SuppressWarnings({ "rawtypes", "unchecked", "static-access" })
	public CapMapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {
		/* 條件設定 */
		// 類型設定
		String type = params.getString("type");
		if (Util.isNotEmpty(type)) {
			for (int i = 0; i < types.length; i++) {
				if (types[i].equals(type)) {
					type = String.valueOf(i + 1);
					break;
				}
			}
		}
		String rptName = params.getString("rptName");
		int rptType = -1;
		if (Util.isNotEmpty(rptName)) {
			for (int i = 0; i < rpts.length; i++) {
				if (rpts[i].equals(rptName)) {
					rptType = i;
					break;
				}
			}
		}
		String loanType = params.getString("loanType");
		if (Util.isNotEmpty(loanType)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					loanType);
		}
		// 日期區間
		String dateType = params.getString("dateType"), bgnDate = params
				.getString("bgnDate"), endDate = params.getString("endDate");
		if (Util.isNotEmpty(bgnDate) && Util.isNotEmpty(endDate)) {
			pageSetting = dateSetting(rptName, dateType, bgnDate, endDate,
					pageSetting);
		}
		// 分行
		String brno = params.getString("brno");
		if (Util.isNotEmpty(brno)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
					brno);
		}
		// 統一編號
		String id = params.getString("ID");
		if (Util.isNotEmpty(id)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					params.getString("IDType"));
			pageSetting
					.addSearchModeParameters(SearchMode.EQUALS, "custId", id);
			if (Util.isNotEmpty(params.getString("dupNo"))) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
						params.getString("dupNo"));
			}
		}
		pageSetting.addOrderBy("createTime", true);
		// 解決grid顯示異常問題
		int first = pageSetting.getFirstResult();
		int cols = pageSetting.getMaxResults();
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.setFirstResult(0);

		/* 調整Grid資料，案件簽報書撈L120M01A資料 */
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9530V01Page.class);
		List<Map<String, Object>> result = new LinkedList();

		if (rptType == 0) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
					type);
			List<L120M01A> list = service.useL120M01A(pageSetting);
			for (int i = first; i < list.size() && i < first + cols; i++) {
				L120M01A pivot = list.get(i);
				Map<String, Object> record = new LinkedMap();
				// 案件簽報書=>改AuthLvl變中文
				String authLvl = pivot.getAuthLvl(), docCode = pivot
						.getDocCode();
				authLvl = Util
						.nullToSpace(pop.getProperty("authLvl" + authLvl));
				docCode = Util
						.nullToSpace(pop.getProperty("docCode" + docCode));
				pivot.setAuthLvl(authLvl + docCode);
				pivot.setCaseBrId(Util.nullToSpace(pivot.getCaseBrId())
						+ " "
						+ Util.nullToSpace(branch.getBranchName(pivot
								.getCaseBrId())));
				// 將其它欄位塞進Map
				for (int j = 0; j < service.caseRS_cols.length; j++) {
					if (pivot.get(service.caseRS_cols[j]) instanceof Timestamp) {
						record.put(service.caseRS_cols[j], CapDate
								.parseToString((Timestamp) pivot
										.get(service.caseRS_cols[j])));
					} else if (pivot.get(service.caseRS_cols[j]) instanceof Date) {
						record.put(service.caseRS_cols[j], TWNDate
								.toAD((Date) pivot.get(service.caseRS_cols[j])));
					} else {
						record.put(service.caseRS_cols[j], Util
								.nullToSpace(pivot.get(service.caseRS_cols[j])));
					}

				}
				result.add(record);
			}
			return new CapMapGridResult(result, list.size());
		} else {
			if (Util.isNotEmpty(type)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"formType", type);
			}
			pageSetting.addSearchModeParameters(SearchMode.IN, "formName",
					rptNameFormat[rptType]);
			Page page = service.findPage(LPDFM01A.class, pageSetting);
			List<LPDFM01A> list = page.getContent();
			for (int i = first; i < list.size() && i < first + cols; i++) {
				LPDFM01A pivot = list.get(i);
				Map<String, Object> record = new LinkedMap();
				if (Util.isNotEmpty(pivot.getLpdfa01a())) {// 權限內
					//代入jsonData
					String strJson = pivot.getJsonData();
					if (Util.isNotEmpty(strJson)) {
						strJson = strJson.substring(1, strJson.length() - 2);
						String[] json = strJson.split("\",");
						for (int x = 0; x < json.length; x++) {
							String[] data = json[x].split(":\"");
							if (data != null && data.length == 2)
								record.put(data[0], data[1]);
						}
					}
					//欄位處理
					switch (rptType) {
					case 1:// 審核書=>如果uid!=null，加入"連結至簽報書"
						if (pivot.getUid() != null) {
							record.put("linkCaseRS",
									Util.nullToSpace(pop.getProperty("linkCaseRS")));
						} else {
							record.put("linkCaseRS", "");
						}
					case 3:// 動審表=>改成打勾or not
						if ("1".equals(pivot.getDocStatus())) {
							pivot.setDocStatus("V");
						} else {
							pivot.setDocStatus("");
						}
						break;
					case 6:// 覆審報告表=>主要往來戶變為中文
						record.put("mainBorrower", "Y".equals(record.get("mainBorrower"))?pop.getProperty("yes"):pop.getProperty("no"));
						break;
					default:

					}
					pivot.setCaseBrId(Util.nullToSpace(pivot.getCaseBrId())
							+ " "
							+ Util.nullToSpace(branch.getBranchName(pivot
									.getCaseBrId())));
					for (int j = 0; j < service.cols.length; j++) {
						Object cell = pivot.get(service.cols[j]);
						if (cell instanceof Timestamp) {
							record.put(service.cols[j],
									CapDate.parseToString((Timestamp) cell));
						} else if (cell instanceof Date) {
							record.put(service.cols[j],
									TWNDate.toAD((Date) cell));
						} else {
							record.put(service.cols[j], Util.nullToSpace(cell));
						}

					}
					result.add(record);
				}
			}
			return new CapMapGridResult(result, list.size());
		}

	}

	/**
	 * 依照日期種類 代入相關設定
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	private ISearch dateSetting(String rptName, String dateType,
			String bgnDate, String endDate, ISearch pageSetting) {
		String colName = null;// 日期判定的欄位
		int type = 0;
		if (Util.isEmpty(dateType)) {// 小放會or政策留貸
			if (rptName.equals("小放會會議記錄"))
				colName = "caseDate";
			else
				colName = "createTime";
		} else {
			type = Util.parseInt(dateType);
			switch (type) {
			case 0:// 簽案
				colName = "caseDate";
				break;
			case 1:// 收件日期
				colName = "caseDate";
				break;
			case 2:// 核准日期
				if (rptName.equals("案件簽報書")) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"docStatus", "05O");
				} else {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"docStatus", "3");
				}
				colName = "endDate";
				break;
			case 3:// 婉卻
				if (rptName.equals("案件簽報書")) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"docStatus", "06O");
				} else {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"docStatus", "4");
				}
				colName = "endDate";
				break;
			case 4:// 核定
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", "3");
				colName = "endDate";
				break;
			case 5:// 先行動用
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"docStatus", "1");
				colName = "caseDate";
				break;
			case 6:// 辦妥
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"isClosed", "Y");
				colName = "endDate";
				break;
			case 7:// 預計覆審
				colName = "endDate";
				break;
			case 8:// 資料
				colName = "createTime";
				break;
			case 9:// 實際覆審
				colName = "caseDate";
				break;
			case 10:// 上傳控制檔
				colName = "createTime";
				break;
			case 11:// 資料產生日
				colName = "createTime";
				break;
			case 12:// 資料基準日
				colName = "endDate";
			}

		}
		if (type != 10) {// "上傳控制檔日"的時間區間在A01表格上
			pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
					colName, bgnDate);
			pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
					colName, endDate);
		}
		return pageSetting;
	}

	/**
	 * 查詢列印清單
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryPrint(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addOrderBy("rptSeq");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				params.getString("mainId"));
		Page page = service.findPage(LPDFS01A.class, pageSetting);
		List<LPDFS01A> list = page.getContent(), result = new LinkedList();
		for (LPDFS01A pivot : list) {
			if (dispDecoder(user.getUnitNo(), pivot.getRptDisp())) {
				LPDFM01A lpdfm01a = pivot.getLpdfm01a(), record = new LPDFM01A();
				if (lpdfm01a != null) {
					record.setCustName(Util.nullToSpace(lpdfm01a.getCustName())
							+ " " + Util.nullToSpace(lpdfm01a.getCustId())
							+ " " + Util.nullToSpace(lpdfm01a.getDupNo()));
					pivot.setLpdfm01a(record);
					result.add(pivot);
				}
			}
		}
		return new CapGridResult(result, result.size());
	}

	private boolean dispDecoder(String brno, int premition) {
		boolean result = false;
		int brnoVal = Util.parseInt(brno);
		if (UtilConstants.BankNo.授管處.equals(brno)) {
			result = ((premition >> 0) >= 1);
		} else if (brnoVal > 931 && brnoVal < 935) {
			result = ((premition >> 1) >= 1);
		} else {
			result = ((premition >> 2) >= 1);
		}
		return result;
	}
}
