/* 
 * LMS1200DOCServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1205V01Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.model.C140SFFF;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01D;
import com.mega.eloan.lms.model.L120M01H;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01F;
import com.mega.eloan.lms.model.L120S01G;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.XmlTool;

/**
 * <pre>
 * 產Word Service
 * </pre>
 * 
 * @since 2011/12/8
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/12/9,Miller Lin
 *          </ul>
 */
@Service("lms1200docservice")
public class LMS1200DOCServiceImpl extends AbstractFormHandler implements
		FileDownloadService {
	// 復原TFS J-111-0636_05097_B100X
	@Resource
	LMS1205Service service1205;

	@Resource
	LMS1405Service service1405;

	@Resource
	BranchService branch;

	@Resource
	UserInfoService userSrv;

	@Resource
	CodeTypeService codeService;

	@Resource
	MisLNF022Service lnLnf022Service;

	@Resource
	LMSService lmsService;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1200DOCServiceImpl.class);

	DecimalFormat dfMoney = new DecimalFormat("#,###,###,###,##0");
	DecimalFormat dfRate = new DecimalFormat("#,###,###,###,##0.00");
	private final String DATEYYYYMMDD = "yyyy-MM-dd";
	// 開會時間
	private final String caseTime = "13時30分";
	// 開會地點
	private final String casePlace = "本行801會議室";
	// Word XML 換行語法
	private final String strEnter = "</w:t></w:r></w:p>"
			+ "<w:p wsp:rsidR='00D3657A' wsp:rsidRDefault='00D3657A' "
			+ "wsp:rsidP='007E18F7'><w:pPr><w:jc "
			+ "w:val='both'/><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體'/>"
			+ "<wx:font wx:val='標楷體'/></w:rPr></w:pPr>"
			+ "<w:r><w:rPr><w:rFonts w:ascii='標楷體' "
			+ "w:fareast='標楷體' w:h-ansi='標楷體' "
			+ "w:hint='fareast'/><wx:font wx:val='標楷體'/></w:rPr><w:t>";
	// Word XML 表格換行語法
	private final String tblEnter = "<w:p wsp:rsidR='00810A66' wsp:rsidRDefault='00810A66'>"
			+ "<w:pPr><w:rPr><w:rFonts w:fareast='標楷體'/>"
			+ "<w:b/><w:b-cs/><w:spacing w:val='10'/>"
			+ "<w:sz w:val='26'/></w:rPr></w:pPr></w:p>";
	// Word XML 換頁語法
	// private final String tblPage =
	// "<w:p wsp:rsidR='00F229D6' wsp:rsidRDefault='00F50453'>" +
	// "<w:r><w:rPr><w:rFonts w:ascii='標楷體' w:fareast='標楷體' w:h-ansi='標楷體'/>" +
	// "<wx:font wx:val='標楷體'/></w:rPr><w:br w:type='page'/></w:r></w:p>";
	// Word 換頁語法
	private final String wordPage = "<br clear=all style='page-break-before:always'>";
	// // 授審會交易代碼
	// private final String 授審會 = "339062";
	// // 催收會交易代碼
	// private final String 催收會 = "339063";
	// // 常董會交易代碼
	// private final String 常董會 = "339064";

	private final String 國外部 = "007";
	private final String 金控總部 = "201";
	private final String 國金部 = "025";
	private final String 私銀處作業組 = "149";

	// 過濾所有以<開頭以>結尾的標籤
	private final static String regxpForHtml = "<([^>]*)>";
	// 找出IMG標籤
	@SuppressWarnings("unused")
	private final static String regxpForImgTag = "<\\s*img\\s+([^>]*)\\s*>";
	// 找出IMG標籤的SRC屬性
	@SuppressWarnings("unused")
	private final static String regxpForImaTagSrcAttrib = "src=\"([^\"]+)\"";

	@Override
	public byte[] getContent(PageParameters params) throws CapException {
		OutputStream outputStream = null;
		ByteArrayOutputStream baos = null;
		try {
			outputStream = this.creatDoc(params);
			if (outputStream != null) {
				baos = (ByteArrayOutputStream) outputStream;
			}
			return baos.toByteArray();
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return null;
	}

	public OutputStream creatDoc(PageParameters params) {
		OutputStream outputStream = null;
		String docTempType = params.getString("docTempType");
		try {
			if ("LMSDoc1".equals(docTempType)) {
				outputStream = this.getWord1b(params);
			} else if ("LMSDoc2".equals(docTempType)) {
				outputStream = this.getWord2a(params);
			} else if ("LMSDoc4".equals(docTempType)) {
				outputStream = this.getWord3a(params);
			} else if ("LMSDoc5".equals(docTempType)) {
				outputStream = this.getWord3b(params);
			} else if ("LMSDoc6".equals(docTempType)) {
				outputStream = this.getWord3c(params);
			} else if ("LMSDoc7".equals(docTempType)) {
				outputStream = this.getWord4(params);
			} else if ("LMSDoc8".equals(docTempType)) {
				outputStream = this.getWord5(params);
			}
		} catch (Exception e) {

		}
		return outputStream;
	}

	/**
	 * 列印核貸通知書
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord5(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// FileUtils.readFileToString(file, "UTF-8");
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// String str = FileUtils.readFileToString(file, "UTF-8");
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// ==================產XML 程式部份==============//
			// //找尋資料區塊開始字串
			// String bodStr = "<w:body><wx:sect>";
			// //找尋資料區塊結束字串
			// String bodEnd = "<w:sectPr";
			// //資料區塊開始位置
			// int strBod = sbWord.indexOf(bodStr, 0)+bodStr.length();
			// //資料區塊結束位置
			// int endBod = sbWord.indexOf(bodEnd, strBod);
			//
			// // 表頭區塊
			// StringBuilder sbHeader = new StringBuilder();
			// sbHeader.append(sbWord.substring(0, strBod));
			// // 資料區塊
			// StringBuilder sbData = new StringBuilder();
			// sbData.append(sbWord.substring(strBod, endBod));
			// // 表尾區塊
			// StringBuilder sbFooter = new StringBuilder();
			// sbFooter.append(sbWord.substring(endBod));
			// // 稽核資料是否都順利取得
			// if (sbHeader == null || sbData == null || sbFooter == null) {
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205V01Page.class);
			// throw new CapMessageException(RespMsgHelper.getMessage(parent,
			// "EFD0025", pop.getProperty("l120v01.error1")),
			// getClass());
			// }
			//
			// L120M01A model = service1205.findL120m01aByOid(oid);
			// //取得額度批覆表
			// List<L140M01A> list =
			// service1405.findL140m01aListByL120m01cMainId(model.getMainId(),
			// "2");
			// //讀不到批覆表再讀額度明細表
			// if(list.isEmpty()){
			// list =
			// service1405.findL140m01aListByL120m01cMainId(model.getMainId(),
			// "1");
			// }
			//
			// List<String> oldVal = new ArrayList<String>();
			// oldVal.add("%LMSVAR001%");
			// oldVal.add("%LMSVAR002%");
			// oldVal.add("%LMSVAR003%");
			// oldVal.add("%LMSVAR004%");
			// oldVal.add("%LMSVAR005%");
			// oldVal.add("%LMSVAR006%");
			// oldVal.add("%LMSVAR007%");
			// oldVal.add("%LMSVAR008%");
			// oldVal.add("%LMSVAR009%");
			// oldVal.add("%LMSVAR010%");
			// oldVal.add("%LMSVAR011%");
			// oldVal.add("%LMSVAR012%");
			// List<String> newVal = new ArrayList<String>();
			//
			// StringBuilder oldData = new StringBuilder();
			// oldData.append(sbData);
			// // 將資料區塊清空以便開始Append
			// sbData.setLength(0);
			// StringBuilder tempData = new StringBuilder();
			//
			// int count = 0;
			// docName = "LMSDoc15";
			// // 開始替換資料
			// for(L140M01A l140m01a : list){
			// // 將暫存內容初始化
			// tempData.setLength(0);
			// // 將保留區塊移到暫存
			// tempData.append(oldData);
			//
			// //清除要替換的資料內容
			// newVal.clear();
			//
			// //編製單位
			// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName
			// (Util.trim(l140m01a.getOwnBrId())),true));
			// //日期
			// newVal.add(XmlTool.replaceXMLReservedWord(CapDate.getCurrentDate("yyyy/MM/dd"),true));
			//
			// // 授信總額度(單位：仟元)
			// StringBuilder loanTot = new StringBuilder();
			// if(Util.isEmpty(Util.trim(l140m01a.getLoanTotCurr()))){
			// loanTot.append("TWD")
			// .append("0");
			// }else{
			// loanTot.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotCurr()),true))
			// .append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotAmt()
			// .divide(new BigDecimal("1000")).toString()),true));
			// }
			// newVal.add(loanTot.toString());
			//
			// //授信額度種類(授信科目)
			// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
			//
			// //動用方式
			// if("2".equals(Util.trim(l140m01a.getReUse()))){
			// newVal.add("循環動用");
			// }else{
			// newVal.add("不循環動用");
			// }
			//
			// //動用期限
			// StringBuilder sbUseDead = new StringBuilder();
			// if(Util.isEmpty(Util.trim(l140m01a.getUseDeadline()))){
			// newVal.add("");
			// }else{
			// char useDead = Util.trim(l140m01a.getUseDeadline()).charAt(0);
			// String useCon =
			// XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getDesp1()),true);
			// switch(useDead){
			// case '0':
			// sbUseDead.append("不再動用");
			// break;
			// case '1':
			// sbUseDead.append(useCon);
			// break;
			// case '2':
			// sbUseDead.append("自核准日起").append(useCon).append("個月");
			// break;
			// case '3':
			// sbUseDead.append("自簽約日起").append(useCon).append("個月");
			// break;
			// case '4':
			// sbUseDead.append("自首次動用日起").append(useCon).append("個月");
			// break;
			// default :
			// sbUseDead.append(useCon);
			// }
			// newVal.add(sbUseDead.toString());
			// }
			//
			// //利(費)率
			// L140M01B l140m01b = service1405.findL140m01bUniqueKey
			// (Util.trim(l140m01a.getMainId()), "2");
			// if(l140m01b != null){
			// //因為是CKeditor所以要過濾掉不要的標籤
			// newVal.add("<![CDATA["
			// + XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(l140m01b
			// .getItemDscr())) + "]]>",true));
			// }else{
			// //若無資料則設為空
			// newVal.add("");
			// }
			// //償還期限(清償期限)
			// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getPayDeadline()),true));
			//
			// //擔保品
			// L140M01B danbow = service1405.findL140m01bUniqueKey
			// (Util.trim(l140m01a.getMainId()), "3");
			// if(danbow != null){
			// //因為是CKeditor所以要過濾掉不要的標籤
			// newVal.add("<![CDATA["
			// + XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(danbow
			// .getItemDscr())) + "]]>",true));
			// }else{
			// //若無資料則設為空
			// newVal.add("");
			// }
			//
			// //連帶保證人
			// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getGuarantor()),true));
			//
			// //其他條件(其他敘做條件)
			// L140M01B otherIf = service1405.findL140m01bUniqueKey
			// (Util.trim(l140m01a.getMainId()), "4");
			// if(otherIf != null){
			// //因為是CKeditor所以要過濾掉不要的標籤
			// newVal.add("<![CDATA["
			// + XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(otherIf
			// .getItemDscr())) + "]]>",true));
			// }else{
			// //若無資料則設為空
			// newVal.add("");
			// }
			//
			// //主要借款人
			// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
			//
			// if(count < list.size()-1){
			// //如不是最後一筆資料則插入換頁符號
			// tempData.append(tblPage);
			// }
			// // 替換表頭
			// replaceStrB(tempData, newVal, oldVal);
			// // 完成處理表頭資料
			// //將修改過得資料存進去
			// sbData.append(tempData);
			// count++;
			// }
			//
			// // 最後將所有區塊串起來(表頭+資料區塊+表尾)
			// sbWord.setLength(0);
			// sbWord.append(sbHeader).append(sbData).append(sbFooter);

			// ==================產XML 程式部份-結束==============//

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));
			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			L120M01A model = service1205.findL120m01aByOid(oid);
			// 取得額度批覆表
			List<L140M01A> list = service1405.findL140m01aListByL120m01cMainId(
					model.getMainId(), "2");
			// 讀不到批覆表再讀額度明細表
			if (list.isEmpty()) {
				list = service1405.findL140m01aListByL120m01cMainId(
						model.getMainId(), "1");
			}

			List<String> oldVal = new ArrayList<String>();
			oldVal.add("%LMSVAR001%");
			oldVal.add("%LMSVAR002%");
			oldVal.add("%LMSVAR003%");
			oldVal.add("%LMSVAR004%");
			oldVal.add("%LMSVAR005%");
			oldVal.add("%LMSVAR006%");
			oldVal.add("%LMSVAR007%");
			oldVal.add("%LMSVAR008%");
			oldVal.add("%LMSVAR009%");
			oldVal.add("%LMSVAR010%");
			oldVal.add("%LMSVAR011%");
			oldVal.add("%LMSVAR012%");
			List<String> newVal = new ArrayList<String>();

			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();

			int count = 0;
			docName = "LMSDoc15";
			// 開始替換資料
			for (L140M01A l140m01a : list) {
				// 將暫存內容初始化
				tempData.setLength(0);
				// 將保留區塊移到暫存
				tempData.append(oldData);

				// 清除要替換的資料內容
				newVal.clear();

				// 編製單位
				// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName
				// (Util.trim(l140m01a.getOwnBrId())),true));
				newVal.add(branch.getBranchName(Util.trim(l140m01a.getOwnBrId())));
				// 日期
				// newVal.add(XmlTool.replaceXMLReservedWord(CapDate.getCurrentDate("yyyy/MM/dd"),true));
				newVal.add(CapDate.getCurrentDate("yyyy/MM/dd"));

				// 授信總額度(單位：仟元)
				StringBuilder loanTot = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getLoanTotCurr()))) {
					loanTot.append("TWD").append("0");
				} else {
					// loanTot.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotCurr()),true))
					// .append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLoanTotAmt()
					// .divide(new BigDecimal("1000")).toString()),true));
					loanTot.append(Util.trim(l140m01a.getLoanTotCurr()))
							.append(Util.trim(l140m01a
									.getLoanTotAmt()
									.divide(new BigDecimal("1000"),
											BigDecimal.ROUND_HALF_UP)
									.toString())).append("仟元整");
				}
				newVal.add(loanTot.toString());

				// 授信額度種類(授信科目)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
				// newVal.add(Util.trim(l140m01a.getLnSubject()));
				L140M01B l140m01b1 = service1405.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()), "1");
				if (l140m01b1 != null) {
					l140m01b1.getItemDscr();
					newVal.add(Util.trim(l140m01a.getLnSubject()) + "<br/>"
							+ Util.trim(l140m01b1.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add(Util.trim(l140m01a.getLnSubject()));
				}

				// 動用方式
				if ("2".equals(Util.trim(l140m01a.getReUse()))) {
					newVal.add("循環動用");
				} else {
					newVal.add("不循環動用");
				}

				// 動用期限
				StringBuilder sbUseDead = new StringBuilder();
				if (Util.isEmpty(Util.trim(l140m01a.getUseDeadline()))) {
					newVal.add("");
				} else {
					newVal.add(getUseDeadline(
							Util.trim(l140m01a.getUseDeadline()),
							Util.trim(l140m01a.getDesp1())));
				}

				// 利(費)率
				L140M01B l140m01b = service1405.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()), "2");
				if (l140m01b != null) {
					l140m01b.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(l140m01b
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(l140m01b
							.getItemDscr())));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}
				// 償還期限(清償期限)
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getPayDeadline()),true));
				newVal.add(Util.trim(l140m01a.getPayDeadline()));

				// 擔保品
				L140M01B danbow = service1405.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.擔保品);
				if (danbow != null) {
					danbow.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(danbow
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(danbow
							.getItemDscr())));
					// newVal.add(Util.trim(danbow.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 連帶保證人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getGuarantor()),true));
				newVal.add(Util.trim(l140m01a.getGuarantor()));

				// 其他條件(其他敘做條件)
				L140M01B otherIf = service1405.findL140m01bUniqueKey(
						Util.trim(l140m01a.getMainId()),
						UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件);
				if (otherIf != null) {
					otherIf.getItemDscr();
					// 因為是CKeditor所以要過濾掉不要的標籤
					// newVal.add("<![CDATA["
					// +
					// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(otherIf
					// .getItemDscr())) + "]]>",true));
					newVal.add(Util.getDocRealImgPath(Util.trim(otherIf
							.getItemDscr())));
					// newVal.add(Util.trim(otherIf.getItemDscr()));
				} else {
					// 若無資料則設為空
					newVal.add("");
				}

				// 主要借款人
				// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
				newVal.add(Util.trim(l140m01a.getCustName()));

				if (count < list.size() - 1) {
					// 如不是最後一筆資料則插入換頁符號
					// tempData.append(tblPage);
					// tempData.append('<p
					// style="page-break-before:always"></p>');
					tempData.append(wordPage);
				}
				// 替換表頭
				replaceStrB(tempData, newVal, oldVal);
				// 完成處理表頭資料
				// 將修改過得資料存進去
				sbData.append(tempData);
				count++;
			}

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			// OutputStreamWriter outWriter = new OutputStreamWriter(baos,
			// "UTF-8");
			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得授信案件明細Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord4(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		// try {
		// // 開始讀取檔案
		// URL urlRpt = null;
		// urlRpt =
		// Thread.currentThread().getContextClassLoader().getResource(fileName.toString());
		// File file=new File(urlRpt.toURI());
		// fileInputStream = new FileInputStream(file);
		// inputStreamReader = new InputStreamReader(fileInputStream);
		// reader = new BufferedReader(inputStreamReader);
		// // 表頭開始字串
		// String bgnStr = "<w:tr";
		// // 表尾開始字串
		// String endStr = "</w:tr>";
		// // 第一個變數字串
		// String strChk = "%LMSREP001%";
		// String str = FileUtils.readFileToString(file, "UTF-8");
		// sbWord.append(str);
		// if(inputStreamReader!= null){
		// inputStreamReader.close();
		// }
		//
		// // 表頭區塊
		// StringBuilder sbHeader = new StringBuilder();
		// sbHeader.append(getDocXML(sbWord, "H", bgnStr, endStr, strChk));
		//
		// // 資料區塊
		// StringBuilder sbData = new StringBuilder();
		// sbData.append(getDocXML(sbWord, "D", bgnStr, endStr, strChk));
		//
		// // 表尾區塊
		// StringBuilder sbFooter = new StringBuilder();
		// sbFooter.append(getDocXML(sbWord, "F", bgnStr, endStr, strChk));
		//
		// // 稽核資料是否都順利取得
		// if (sbHeader == null || sbData == null || sbFooter == null) {
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMS1205V01Page.class);
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0025", pop.getProperty("l120v01.error1")),
		// getClass());
		// }
		//
		// // 開始替換資料(表頭)
		// //餘額日期
		// StringBuilder var001B = new StringBuilder();
		// //製表日期
		// StringBuilder var002B = new StringBuilder();
		// docName = "LMSDoc71";
		//
		// List<String> oldVal = new ArrayList<String>();
		// List<String> newVal = new ArrayList<String>();
		//
		//
		// L120M01A testModel = service1205.findL120m01aByOid(oidArray[0]);
		// if(testModel != null){
		// var001B.append(XmlTool.replaceXMLReservedWord
		// (CapDate.formatDate(testModel.getCaseDate(), "yyyy/MM/dd"),true));
		// }else{
		// var001B.append("");
		// }
		// var002B.append(CapDate.getCurrentDate("yyyy/MM/dd"));
		// newVal.add(XmlTool.replaceXMLReservedWord(var001B.toString(),true));
		// newVal.add(XmlTool.replaceXMLReservedWord(var002B.toString(),true));
		//
		// oldVal.add("%LMSVAR001%");
		// oldVal.add("%LMSVAR002%");
		//
		// // 替換表頭
		// replaceStrB(sbHeader, newVal, oldVal);
		// // 完成處理表頭資料
		//
		// // 開始處理明細資料區塊
		// StringBuilder oldData = new StringBuilder();
		// oldData.append(sbData);
		// // 將資料區塊清空以便開始Append
		// sbData.setLength(0);
		// StringBuilder tempData = new StringBuilder();
		// oldVal.clear();
		// oldVal.add("%LMSREP001%");
		// oldVal.add("%LMSREP002%");
		// oldVal.add("%LMSREP003%");
		// oldVal.add("%LMSREP004%");
		// oldVal.add("%LMSREP005%");
		// oldVal.add("%LMSREP006%");
		// oldVal.add("%LMSREP007%");
		//
		// for (int i = 0; i < oidArray.length; i++) {
		// // 將暫存內容初始化
		// tempData.setLength(0);
		// newVal.clear();
		// // 承辦單位
		// newVal.add("");
		// // 客戶名稱
		// newVal.add("");
		// // 授信科目
		// newVal.add("");
		// // 額度(單位：仟元)
		// newVal.add("");
		// // 餘額(單位：仟元)
		// newVal.add("");
		// // 擔保品
		// newVal.add("");
		// // 備註
		// newVal.add("");
		// // 將保留區塊移到暫存
		// tempData.append(oldData);
		// L120M01A model = service1205
		// .findL120m01aByOid(oidArray[i]);
		// List<L140M01A> list =
		// service1405.findL140m01aListByL120m01cMainId(model.getMainId(), "2");
		// if(list.isEmpty()){
		// list =
		// service1405.findL140m01aListByL120m01cMainId(model.getMainId(), "1");
		// }
		// // 開始替換資料...
		// for(L140M01A l140m01a : list){
		// if(FlowDocStatusEnum.已核准.toString().equals(l140m01a.getDocStatus())){
		// newVal.clear();
		// // 承辦單位
		// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName(Util.trim(l140m01a
		// .getOwnBrId())),true));
		// // 客戶名稱
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
		// // 授信科目
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
		// // 額度(單位：仟元)
		// StringBuilder sbCur = new StringBuilder();
		// if(Util.isEmpty(Util.trim(l140m01a.getCurrentApplyCurr()))){
		// sbCur.append("TWD").append("0");
		// }else{
		// sbCur.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCurrentApplyCurr()),true))
		// .append(XmlTool.replaceXMLReservedWord(NumConverter.addComma(Util.trim(l140m01a.getCurrentApplyAmt()
		// .divide(new BigDecimal("1000")).toString())),true));
		// }
		// newVal.add(sbCur.toString());
		// // 餘額(單位：仟元)
		// StringBuilder sbBl = new StringBuilder();
		// if(Util.isEmpty(Util.trim(l140m01a.getBLCurr()))){
		// sbBl.append("TWD").append("0");
		// }else{
		// sbBl.append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getBLCurr()),true))
		// .append(XmlTool.replaceXMLReservedWord(NumConverter.addComma(Util.trim(l140m01a.getBLAmt()
		// .divide(new BigDecimal("1000")).toString())),true));
		// }
		// newVal.add(sbBl.toString());
		// // 擔保品
		// L140M01B danbow = service1405.findL140m01bUniqueKey
		// (l140m01a.getMainId(), "3");
		// if(danbow != null){
		// //因為是CKeditor所以要過濾掉不要的標籤
		// newVal.add("<![CDATA["
		// + XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(danbow
		// .getItemDscr())) + "]]>",true));
		// }else{
		// newVal.add("");
		// }
		// // 備註
		// L140M01B psCon = service1405.findL140m01bUniqueKey
		// (l140m01a.getMainId(), "4");
		// if(psCon != null){
		// //因為是CKeditor所以要過濾掉不要的標籤
		// newVal.add("<![CDATA["
		// + XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(psCon
		// .getItemDscr())) + "]]>",true));
		// }else{
		// newVal.add("");
		// }
		// }
		// }
		// replaceStrB(tempData, newVal, oldVal);
		// // 將修改過的資料區塊存進去
		// sbData.append(tempData.toString());
		// }
		//
		// // 最後將所有區塊串起來(表頭+資料區塊+表尾)
		// sbWord.setLength(0);
		// sbWord.append(sbHeader).append(sbData).append(sbFooter);
		//
		// OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		// outWriter.write(sbWord.toString());
		// outWriter.close();
		//
		// } catch (FileNotFoundException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (IOException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (Exception e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// }

		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// 第一個變數字串
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			// String bodStr = "<div class=Section1";
			String bodStr = "%HeadEnd%";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			// 資料區塊開始位置
			int strBod = sbWord.indexOf(bodStr, 0) + bodStr.length();
			;
			// 資料區塊結束位置
			int endBod = sbWord.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord.substring(endBod));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			// 餘額日期
			String var001B = "";
			// 製表日期
			StringBuilder var002B = new StringBuilder();
			docName = "LMSDoc71";

			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			oldVal.add("%LMSREP001%");
			oldVal.add("%LMSREP002%");
			oldVal.add("%LMSREP003%");
			oldVal.add("%LMSREP004%");
			oldVal.add("%LMSREP005%");
			oldVal.add("%LMSREP006%");
			oldVal.add("%LMSREP007%");

			for (int i = 0; i < oidArray.length; i++) {
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 承辦單位
				newVal.add("");
				// 客戶名稱
				newVal.add("");
				// 授信科目
				newVal.add("");
				// 額度(單位：仟元)
				newVal.add("");
				// 餘額(單位：仟元)
				newVal.add("");
				// 擔保品
				newVal.add("");
				// 備註
				newVal.add("");
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A model = service1205.findL120m01aByOid(oidArray[i]);
				List<L140M01A> list = service1405
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								"2");
				if (list.isEmpty()) {
					list = service1405.findL140m01aListByL120m01cMainId(
							model.getMainId(), "1");
				}
				// 開始替換資料...
				for (L140M01A l140m01a : list) {
					if (FlowDocStatusEnum.已核准.toString().equals(
							l140m01a.getDocStatus())) {
						newVal.clear();
						// 2為國外 ，當行別為國外時查DW 其他查 mis
						if (UtilConstants.BrNoType.國外
								.equals(branch.getBranch(
										Util.trim(l140m01a.getCntrNo())
												.substring(0, 3)).getBrNoFlag())) {
							// 抓DW
							var001B = service1205.getLoanDate2(l140m01a,
									var001B);
						} else {
							// 抓MIS
							var001B = service1205
									.getLoanDate(l140m01a, var001B);
						}

						// 承辦單位
						newVal.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 客戶名稱
						newVal.add(Util.trim(l140m01a.getCustName()));
						// 授信科目
						newVal.add(Util.trim(l140m01a.getLnSubject()));
						// 額度(單位：仟元)
						StringBuilder sbCur = new StringBuilder();
						if (Util.isEmpty(Util.trim(l140m01a
								.getCurrentApplyCurr()))) {
							sbCur.append("TWD").append("0");
						} else {
							sbCur.append(
									Util.trim(l140m01a.getCurrentApplyCurr()))
									.append(NumConverter.addComma(Util
											.trim(l140m01a
													.getCurrentApplyAmt()
													.divide(new BigDecimal(
															"1000")).toString())));
						}
						newVal.add(sbCur.toString());
						// 餘額(單位：仟元)
						StringBuilder sbBl = new StringBuilder();
						if (Util.isEmpty(Util.trim(l140m01a.getBLCurr()))) {
							sbBl.append("TWD").append("0");
						} else {
							sbBl.append(Util.trim(l140m01a.getBLCurr()))
									.append(NumConverter.addComma(Util
											.trim(l140m01a
													.getBLAmt()
													.divide(new BigDecimal(
															"1000")).toString())));
						}
						newVal.add(sbBl.toString());
						// 擔保品
						L140M01B danbow = service1405.findL140m01bUniqueKey(
								l140m01a.getMainId(), "3");
						if (danbow != null) {
							// 因為是CKeditor所以要過濾掉不要的標籤
							newVal.add(Util.getDocRealImgPath(Util.trim(danbow
									.getItemDscr())));
							// newVal.add(Util.trim(danbow.getItemDscr()));
						} else {
							newVal.add("");
						}
						// 備註
						L140M01B psCon = service1405.findL140m01bUniqueKey(
								l140m01a.getMainId(), "4");
						if (psCon != null) {
							// 因為是CKeditor所以要過濾掉不要的標籤
							newVal.add(Util.getDocRealImgPath(Util.trim(psCon
									.getItemDscr())));
							// newVal.add(Util.trim(psCon.getItemDscr()));
						} else {
							newVal.add("");
						}
					}
				}
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}

			// 開始替換資料(表頭)
			newVal.clear();
			oldVal.clear();
			var002B.append(CapDate.getCurrentDate("yyyy/MM/dd"));
			newVal.add(var001B);
			newVal.add(var002B.toString());

			oldVal.add("%LMSVAR001%");
			oldVal.add("%LMSVAR002%");
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 替換表頭日期
			replaceStrB(sbData, newVal, oldVal);

			// 最後把表頭關鍵Key清掉避免殘留
			newVal.clear();
			oldVal.clear();
			newVal.add("");
			oldVal.add(bodStr);
			replaceStrB(sbHeader, newVal, oldVal);
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "BIG5");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得議程Word資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord1b(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// 表頭開始字串
			String bgnStr = "<w:tr";
			// 表尾開始字串
			String endStr = "</w:tr>";
			// 第一個變數字串
			String strChk = "%LMSREP001%";
			String str = FileUtils.readFileToString(file, "UTF-8");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(getDocXML(sbWord, "H", bgnStr, endStr, strChk));

			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(getDocXML(sbWord, "D", bgnStr, endStr, strChk));

			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(getDocXML(sbWord, "F", bgnStr, endStr, strChk));

			// 稽核資料是否都順利取得
			if (sbHeader == null || sbData == null || sbFooter == null) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1205V01Page.class);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0025", pop.getProperty("l120v01.error1")),
						getClass());
			}

			// 開始替換資料(表頭)
			L120M01A model = service1205.findL120m01aByOid(oidArray[0]);

			String title = "";
			String hqMeetFlag = Util.trim(model.getHqMeetFlag());
			// 稽核會期資料是否存在
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				title = model.getRptTitle2();
			} else {
				title = model.getRptTitle1();
			}
			if (Util.isEmpty(title)) {
				// Properties pop = MessageBundleScriptCreator
				// .getComponentResource(LMS1205V01Page.class);
				// throw new CapMessageException(RespMsgHelper.getMessage(
				// parent, "EFD0025",
				// pop.getProperty("l120v01.error5")), getClass());
			}
			// YYY年度第99次授信審議小組（委員會）會議議程
			StringBuilder var001B = new StringBuilder();
			String title1 = "";
			String title2 = "";
			if ("1".equals(hqMeetFlag) || "2".equals(hqMeetFlag)
					|| "A".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 授審會
				docName = "LMSDoc11";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 4);
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會
				docName = "LMSDoc1";
				title1 = Util.trim(model.getRptTitle2()).substring(0, 4);
				title2 = Util.trim(model.getRptTitle2()).substring(10);
			}
			var001B.append(XmlTool.replaceXMLReservedWord(title1, true))
					.append("度")
					.append(XmlTool.replaceXMLReservedWord(title2, true))
					.append("會議議程");
			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();

			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				newVal.add(var001B.toString());
				oldVal.add("%LMSVAR001%");
			} else {
				// 開會時間：YYY年MM月DD日XX時XX分　　　 開會地點：ＸＸＸＸＸＸＸ
				StringBuilder var002B = new StringBuilder();
				var002B.append("開會時間：")
						.append(XmlTool.replaceXMLReservedWord(
								Util.trim(model.getRptTitle1()), true)
								.substring(0, 10)).append(caseTime)
						.append("\t\t\t\t").append("開會地點：").append(casePlace);

				newVal.add(var001B.toString());
				newVal.add(var002B.toString());
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
			}
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				oldVal.add("%LMSREP001%");
			} else {
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
				oldVal.add("%LMSREP004%");
			}

			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會第1筆資料固定給授管處用，記錄上次催收會討論事項
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A l120m01a = service1205.findL120m01aByOid(oidArray[0]);
				// 開始替換資料...
				// 提案單位別
				newVal.add("法人金融處/個人金融處");
				// 客戶別/案由
				// 本次會期次數-1
				int indexTime = Util.trim(l120m01a.getRptTitle1()).indexOf("次");
				String times = ("".equals(Util.trim(l120m01a.getRptTitle1())
						.substring(10, indexTime))) ? "0" : String.valueOf(Util
						.parseInt(Util.trim(l120m01a.getRptTitle1()).substring(
								11, indexTime)) - 1);
				StringBuilder sbCase = new StringBuilder();
				sbCase.append("第" + XmlTool.replaceXMLReservedWord(times, true)
						+ "次催收會決議事項報請鑒察案");
				newVal.add(sbCase.toString());
				// 經辦同仁
				newVal.add("");
				// 覆審主管
				newVal.add("");
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}
			for (int i = 0; i < oidArray.length; i++) {
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1205
							.findL120m01aByOid(oidArray[i]);
					// 開始替換資料...
					// 案由(預設簽報書案由)
					StringBuilder sbCase = new StringBuilder();
					sbCase.append(
							XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getGist()), true))
							.append(strEnter);
					// newVal.add(XmlTool.replaceXMLReservedWord(sbCase.toString());
					if (國外部.equals(user.getUnitNo())) {
						// 國外部提
						sbCase.append("(國外部提)");
					} else {
						String gistTitle = "";
						if ("2".equals(Util.trim(l120m01a.getDocType()))) {
							// 個金
							gistTitle = "個人金融處提\\";
						} else {
							// 企金
							gistTitle = "法人金融處提\\";
						}
						sbCase.append(gistTitle)
								.append(XmlTool.replaceXMLReservedWord(branch
										.getBranchName(Util.trim(l120m01a
												.getCaseBrId())), true))
								.append("承做");
					}
					newVal.add(sbCase.toString());
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				} else {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1205
							.findL120m01aByOid(oidArray[i]);
					// 開始替換資料...
					// 提案單位別
					newVal.add(XmlTool.replaceXMLReservedWord(branch
							.getBranchName(Util.trim(l120m01a.getCaseBrId())),
							true));
					// 客戶別/案由
					newVal.add(XmlTool.replaceXMLReservedWord(
							Util.trim(l120m01a.getCustName()), true));
					// 經辦同仁
					newVal.add(XmlTool.replaceXMLReservedWord(
							getPerName(Util.trim(l120m01a.getCreator())), true));
					// 覆審主管
					newVal.add(XmlTool.replaceXMLReservedWord(
							getPerName(Util.trim(l120m01a.getApprover())), true));
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			}

			// 全部完成後塞空白行
			// 將暫存內容初始化
			tempData.setLength(0);
			newVal.clear();
			// 將保留區塊移到暫存
			tempData.append(oldData);
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
			} else {
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
				newVal.add(XmlTool.replaceXMLReservedWord(
						UtilConstants.Mark.SPACE, true));
			}
			replaceStrB(tempData, newVal, oldVal);
			// 將修改過的資料區塊存進去
			sbData.append(tempData.toString());

			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	public String getPerName(String id) {
		return (Util.isNotEmpty(userSrv.getUserName(id)) ? userSrv
				.getUserName(id) : UtilConstants.Mark.SPACE);
	}

	/**
	 * 取得決議錄
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	public OutputStream getWord2a(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();

		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		boolean isArea = params.getBoolean("isArea");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String selOid = params.getString("selOid");
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		String txCode = params.getString("txCode");
		List<String> listSOid = new ArrayList<String>();
		// 決議錄(同會期之全部文件適用)
		if (!Util.isEmpty(selOid)) {
			L120M01A selModel = service1205.findL120m01aByOid(selOid);
			String hqMeetFlag = Util.trim(selModel.getHqMeetFlag());
			for (int i = 0; i < oidArray.length; i++) {
				L120M01A model = service1205.findL120m01aByOid(oidArray[i]);
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					if (Util.trim(selModel.getRptTitle2()).equals(
							Util.trim(model.getRptTitle2()))) {
						listSOid.add(oidArray[i]);
					}
				} else {
					if (isArea) {
						// 營運中心所有提會案件產決議錄
						if (Util.trim(selModel.getRptTitleArea1()).equals(
								Util.trim(model.getRptTitleArea1()))) {
							listSOid.add(oidArray[i]);
						}
					} else if (Util.trim(selModel.getRptTitle1()).equals(
							Util.trim(model.getRptTitle1()))) {
						listSOid.add(oidArray[i]);
					}
				}
			}
		}
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// 表頭開始字串
			String bgnStr = "<w:tr";
			// 表尾開始字串
			String endStr = "</w:tr>";
			// 第一個變數字串
			String strChk = "%LMSREP001%";
			String str = FileUtils.readFileToString(file, "UTF-8");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(getDocXML(sbWord, "H", bgnStr, endStr, strChk));

			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(getDocXML(sbWord, "D", bgnStr, endStr, strChk));

			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(getDocXML(sbWord, "F", bgnStr, endStr, strChk));

			// 開始替換資料(表頭)
			L120M01A model;
			if (!listSOid.isEmpty()) {
				model = service1205.findL120m01aByOid(listSOid.get(0));
			} else {
				model = service1205.findL120m01aByOid(oidArray[0]);
			}
			String hqMeetFlag = Util.trim(model.getHqMeetFlag());
			String title = "";
			// 稽核會期資料是否存在
			if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				title = model.getRptTitle2();
			} else {
				if (isArea) {
					title = model.getRptTitleArea1();
				} else {
					title = model.getRptTitle1();
				}
			}
			// if (Util.isEmpty(title)) {
			// Properties pop = MessageBundleScriptCreator
			// .getComponentResource(LMS1205V01Page.class);
			// throw new CapMessageException(RespMsgHelper.getMessage(
			// parent, "EFD0025",
			// pop.getProperty("l120v01.error5")), getClass());
			// }
			// YYY年度第12屆第2次授審會會議決議錄
			StringBuilder var001B = new StringBuilder();
			String title1 = "";
			String title2 = "";
			if (isArea) {
				docName = "LMSDoc2";
				title1 = Util.trim(model.getRptTitleArea1()).substring(0, 4);
				title2 = Util.trim(model.getRptTitleArea1()).substring(10);
			} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
				// 授審會
				docName = "LMSDoc2";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 10);
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會
				docName = "LMSDoc21";
				title1 = Util.trim(model.getRptTitle1()).substring(0, 4)
						+ ("度");
				title2 = Util.trim(model.getRptTitle1()).substring(10);
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會
				docName = "LMSDoc3";
				title1 = Util.trim(model.getRptTitle2()).substring(0, 4)
						+ ("度");
				title2 = Util.trim(model.getRptTitle2()).substring(10);
			}
			var001B.append(XmlTool.replaceXMLReservedWord(title1, true))
					.append(title2).append("會議決議錄");
			// 開會時間：YYY年MM月DD日XX時XX分　　　 開會地點：ＸＸＸＸＸＸＸ
			StringBuilder var002B = new StringBuilder();
			if (!"3".equals(hqMeetFlag) && !"C".equals(hqMeetFlag)) {
				if (isArea) {
					var002B.append("開會時間：")
							.append(XmlTool.replaceXMLReservedWord(
									Util.trim(model.getRptTitleArea1())
											.substring(0, 10), true))
							.append(caseTime).append("\t\t\t\t")
							.append("開會地點：").append(casePlace);
				} else {
					var002B.append("開會時間：")
							.append(XmlTool.replaceXMLReservedWord(
									Util.trim(model.getRptTitle1()).substring(
											0, 10), true)).append(caseTime)
							.append("\t\t\t\t").append("開會地點：")
							.append(casePlace);
				}
			}
			List<String> oldVal = new ArrayList<String>();
			List<String> newVal = new ArrayList<String>();
			newVal.add(var001B.toString());
			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會範本有兩個表頭變數
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				// 將第二個表頭變數值加入
				newVal.add(var002B.toString());
			} else {
				oldVal.add("%LMSVAR001%");
			}
			// 替換表頭
			replaceStrB(sbHeader, newVal, oldVal);
			// 完成處理表頭資料

			// 開始處理明細資料區塊
			StringBuilder oldData = new StringBuilder();
			oldData.append(sbData);
			// 將資料區塊清空以便開始Append
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			oldVal.clear();
			if ("1".equals(hqMeetFlag) || isArea || "A".equals(hqMeetFlag)) {
				// 授審會範本有四個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
				oldVal.add("%LMSREP004%");
			} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會範本有三個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
				oldVal.add("%LMSREP003%");
			} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
				// 常董會有兩個資料變數
				oldVal.add("%LMSREP001%");
				oldVal.add("%LMSREP002%");
			}
			if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
				// 催收會第1筆資料固定給授管處用，記錄上次催收會討論事項
				// 將暫存內容初始化
				tempData.setLength(0);
				newVal.clear();
				// 將保留區塊移到暫存
				tempData.append(oldData);
				L120M01A l120m01a = service1205.findL120m01aByOid(oidArray[0]);
				// 開始替換資料...
				// 承辦營業單位
				newVal.add("法人金融處/個人金融處");
				// 客戶別/案由
				// 本次會期次數-1
				int indexTime = Util.trim(l120m01a.getRptTitle1()).indexOf("次");
				String times = ("".equals(Util.trim(l120m01a.getRptTitle1())
						.substring(10, indexTime))) ? "0" : String.valueOf(Util
						.parseInt(Util.trim(l120m01a.getRptTitle1()).substring(
								11, indexTime)) - 1);
				StringBuilder sbCase = new StringBuilder();
				sbCase.append("第" + times + "次催收會決議事項報請鑒察案");
				newVal.add(XmlTool.replaceXMLReservedWord(sbCase.toString(),
						true));
				// 結論
				newVal.add("確認。");
				replaceStrB(tempData, newVal, oldVal);
				// 將修改過的資料區塊存進去
				sbData.append(tempData.toString());
			}
			if (!listSOid.isEmpty()) {
				for (int i = 0; i < listSOid.size(); i++) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1205.findL120m01aByOid(listSOid
							.get(i));
					// 開始替換資料...
					if (isArea) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										UtilConstants.Casedoc.MeetingType.營運中心);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getGist()), true));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getGist()), true));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}
					} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getGist()), true));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getGist()), true));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}

					} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 客戶別/案由
							StringBuilder sbCase = new StringBuilder();
							sbCase.append(Util.trim(l120m01a.getCustName())
									+ "/" + Util.trim(l120m01a.getGist()));
							newVal.add(XmlTool.replaceXMLReservedWord(
									sbCase.toString(), true));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 客戶別/案由
							newVal.add("");
							// 決議
							newVal.add("");
						}
					} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
						// 案由(預設簽報書案由)
						StringBuilder sbCase = new StringBuilder();
						sbCase.append(Util.trim(l120m01a.getGist()));
						newVal.add(XmlTool.replaceXMLReservedWord(
								sbCase.toString(), true));
						// 決議
						StringBuilder sbDec = new StringBuilder();
						if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
							// 如果是授管處的常董會->執行此條件
							sbDec.append("(")
									.append(XmlTool.replaceXMLReservedWord(
											branch.getBranchName(Util
													.trim(l120m01a
															.getCaseBrId())),
											true)).append("提)");
							newVal.add(sbDec.toString());
						} else {
							String caseBridTitle = "";
							// 如果是其他分行的常董會->執行此條件
							if ("2".equals(Util.trim(l120m01a.getDocType()))) {
								// 個金
								caseBridTitle = "個人金融處提\\";
							} else {
								// 企金
								caseBridTitle = "法人金融處提\\";
							}
							sbDec.append(caseBridTitle)
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("承做");
						}
					}
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			} else {
				for (int i = 0; i < oidArray.length; i++) {
					// 將暫存內容初始化
					tempData.setLength(0);
					newVal.clear();
					// 將保留區塊移到暫存
					tempData.append(oldData);
					L120M01A l120m01a = service1205
							.findL120m01aByOid(oidArray[i]);
					// 開始替換資料...
					if (isArea) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										UtilConstants.Casedoc.MeetingType.營運中心);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getGist()), true));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getGist()), true));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}
					} else if ("1".equals(hqMeetFlag) || "A".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getGist()), true));
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
							// 核准額度較前准增、減金額
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getQuotaDesrc()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 案由
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getGist()), true));
							// 決議
							newVal.add("");
							// 核准額度較前准增、減金額
							newVal.add("");
						}

					} else if ("2".equals(hqMeetFlag) || "B".equals(hqMeetFlag)) {
						L120M01H l120m01h = service1205
								.findL120m01hByUniqueKey(l120m01a.getMainId(),
										hqMeetFlag);
						if (l120m01h != null) {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 客戶別/案由
							StringBuilder sbCase = new StringBuilder();
							sbCase.append(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01a.getCustName()), true)
									+ "/"
									+ XmlTool.replaceXMLReservedWord(
											Util.trim(l120m01a.getGist()), true));
							newVal.add(sbCase.toString());
							// 決議
							newVal.add(XmlTool.replaceXMLReservedWord(
									Util.trim(l120m01h.getDispWord()), true));
						} else {
							// 承辦營業單位
							newVal.add(XmlTool.replaceXMLReservedWord(branch
									.getBranchName(Util.trim(l120m01a
											.getCaseBrId())), true));
							// 客戶別/案由
							newVal.add("");
							// 決議
							newVal.add("");
						}
					} else if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
						// 案由(預設簽報書案由)
						StringBuilder sbCase = new StringBuilder();
						sbCase.append(Util.trim(l120m01a.getGist()));
						newVal.add(XmlTool.replaceXMLReservedWord(
								sbCase.toString(), true));
						// 決議
						StringBuilder sbDec = new StringBuilder();
						if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
							// 如果是授管處的常董會->執行此條件
							sbDec.append("(")
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("提)");
							newVal.add(XmlTool.replaceXMLReservedWord(
									sbDec.toString(), true));
						} else {
							// 如果是其他分行的常董會->執行此條件
							String caseBridTitle = "";
							if ("2".equals(Util.trim(l120m01a.getDocType()))) {
								// 個金
								caseBridTitle = "個人金融處提\\";
							} else {
								// 企金
								caseBridTitle = "法人金融處提\\";
							}
							sbDec.append(caseBridTitle)
									.append(branch.getBranchName(Util
											.trim(l120m01a.getCaseBrId())))
									.append("承做");
						}
					}
					replaceStrB(tempData, newVal, oldVal);
					// 將修改過的資料區塊存進去
					sbData.append(tempData.toString());
				}
			}
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord.setLength(0);
			sbWord.append(sbHeader).append(sbData).append(sbFooter);

			OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(sbWord.toString());
			outWriter.close();

		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿(個案討論)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3a(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		// try {
		// // 開始讀取第一範本檔案
		// URL urlRpt = null;
		// urlRpt =
		// Thread.currentThread().getContextClassLoader().getResource(fileName.toString());
		// File file=new File(urlRpt.toURI());
		// fileInputStream = new FileInputStream(file);
		// inputStreamReader = new InputStreamReader(fileInputStream);
		// reader = new BufferedReader(inputStreamReader);
		//
		// String bgStr = "客戶資料：";
		// // 插入第二範本資料開始字串
		// String strChk = "<w:p";
		// // 插入第二範本資料結束字串
		// String endChk = "</w:r></w:p>";
		// // 第二範本資料開始字串
		// String bgnData = "<w:tbl>";
		// // 第二範本資料結束字串
		// String endData = "</w:tbl>";
		// // 讀取第一範本資料
		// String str = FileUtils.readFileToString(file, "UTF-8");
		// sbWord.append(str);
		// if(inputStreamReader!= null){
		// inputStreamReader.close();
		// }
		//
		// int bgnTest = sbWord.indexOf(bgStr);
		// // 插入第二範本資料開始字串位置
		// int bgnIndex = sbWord.indexOf(strChk, bgnTest);
		// // 插入第二範本資料結束字串位置
		// int endIndex = sbWord.indexOf(endChk, bgnIndex) + endChk.length();
		//
		// // 存取要被取代成第二範本資料內容的資料內容
		// StringBuilder bgData = new StringBuilder();
		// bgData.append(sbWord.substring(bgnIndex, endIndex));
		//
		// // 開始替換第一範本資料
		// if (!oid.isEmpty()) {
		// L120M01A model = service1205.findL120m01aByOid(oid);
		// List<String> oldVal = new ArrayList<String>();
		// List<String> newVal = new ArrayList<String>();
		// oldVal.add("%LMSVAR001%");
		// oldVal.add("%LMSVAR002%");
		// // 替換表格的關係要連同標籤一起取代
		// oldVal.add(bgData.toString());
		// oldVal.add("%LMSVAR004%");
		// oldVal.add("%LMSVAR005%");
		// oldVal.add("%LMSVAR006%");
		// StringBuilder sbDec = new StringBuilder();
		// docName = "LMSDoc4";
		//
		// // 案由part1
		// L120M01H l120m01h = service1205.findL120m01hByUniqueKey(
		// model.getMainId(), "1");
		// sbDec.setLength(0);
		// if (Util.isEmpty(model.getGist()) || "".equals(model.getGist())) {
		// if (l120m01h != null) {
		// sbDec.append(XmlTool.replaceXMLReservedWord(Util.trim(l120m01h.getDispWord()),true));
		// }
		// } else {
		// if (l120m01h != null) {
		// sbDec.append(XmlTool.replaceXMLReservedWord(model.getGist(),true)).append(strEnter)
		// .append(strEnter)
		// .append(XmlTool.replaceXMLReservedWord(Util.trim(l120m01h.getDispWord()),true));
		// } else {
		// sbDec.append(XmlTool.replaceXMLReservedWord(model.getGist(),true)).append(strEnter)
		// .append(strEnter).append("無");
		// }
		// }
		// newVal.add(sbDec.toString());
		// // 案由part2
		// sbDec.setLength(0);
		// if (常董會.equals(txCode)) {
		// // 如果是授管處的常董會->執行此條件
		// sbDec.append("(")
		// .append(branch.getBranchName(XmlTool.replaceXMLReservedWord(Util.trim(model
		// .getCaseBrId()),true))).append("提)");
		// } else {
		// String title = "";
		// // 若為OBU案件則需再加國金部字眼
		// if ("4".equals(Util.trim(model.getTypCd()))) {
		// // OBU
		// title = "\\國金部承做";
		// } else {
		// title = "承做";
		// }
		// sbDec.append("授信管理處提\\")
		// .append(branch.getBranchName(XmlTool.replaceXMLReservedWord(Util.trim(model
		// .getCaseBrId()),true))).append(title);
		// }
		// newVal.add(sbDec.toString());
		// // 客戶資料(第二範本區塊內容)
		// newVal.add(getWord2(oid, fileName2.toString(), bgnData, endData));
		// // 敘做理由
		// sbDec.setLength(0);
		// // 若為授權外其他則為空白
		// if ("2".equals(model.getDocKind())) {
		// sbDec.append("無");
		// }
		// newVal.add(XmlTool.replaceXMLReservedWord(sbDec.toString(),true));
		// // 分行逾放比
		// sbDec.setLength(0);
		// List<L140M01A> listL140m01a = service1405
		// .findL140m01aListByL120m01cMainId(model.getMainId(),
		// "1");
		// if (!listL140m01a.isEmpty()) {
		// for (L140M01A l140m01a : listL140m01a) {
		// if(!Util.isEmpty(Util.trim(l140m01a.getNpldate()))
		// && !Util.isEmpty(Util.trim(l140m01a.getNpl()))){
		// sbDec.append("資料年月：")
		// .append(XmlTool.replaceXMLReservedWord(CapDate.convertDateToTaiwanYear(TWNDate
		// .toAD(l140m01a.getNpldate())
		// .subSequence(0, 4).toString()),true))
		// .append("/")
		// .append(XmlTool.replaceXMLReservedWord(TWNDate.toAD(l140m01a.getNpldate())
		// .subSequence(5, 7).toString(),true))
		// .append(" ")
		// .append(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getNpl()),true))
		// .append("%").append(strEnter);
		// }
		// }
		// }
		// newVal.add(sbDec.toString());
		//
		// // 總處審查意見
		// L120M01H l120m01h02 = service1205.findL120m01hByUniqueKey(
		// model.getMainId(), "1");
		// sbDec.setLength(0);
		// // 國外部007 金控總部201 國金部025
		// if (國外部.equals(user.getUnitNo())
		// || 金控總部.equals(user.getUnitNo())
		// || 國金部.equals(user.getUnitNo())) {
		// if ("".equals(model.getRptTitle1())
		// || Util.isEmpty(model.getRptTitle1())) {
		// // 不提會, 抓法金處/個金處會簽意見
		// // 授管處會簽意見
		//
		// } else {
		// String dispWord = "";
		// if (l120m01h02 != null) {
		// // 提會, 抓授審會決議
		// dispWord =
		// XmlTool.replaceXMLReservedWord(Util.trim(l120m01h02.getDispWord()),true);
		// } else {
		// dispWord = "無";
		// }
		// sbDec.append("本案經提")
		// .append(XmlTool.replaceXMLReservedWord(Util.trim(model.getRptTitle1()),true))
		// .append("討論，結論：")
		// .append("\t")
		// .append(dispWord);
		// }
		// } else {
		// // 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
		// if ("".equals(model.getRptTitle1())
		// || Util.isEmpty(model.getRptTitle1())) {
		// // 不提會, 抓授管處審查意見
		// L120M01D l120m01d = service1205
		// .findL120m01dByUniqueKey(model.getMainId(), "B");
		// if (l120m01d != null) {
		// sbDec.append(XmlTool.replaceXMLReservedWord(Util.trim(l120m01d.getItemDscr()),true));
		// }
		// } else {
		// String dispWord = "";
		// if (l120m01h02 != null) {
		// // 提會, 抓授審會決議
		// dispWord =
		// XmlTool.replaceXMLReservedWord(Util.trim(l120m01h02.getDispWord()),true);
		// } else {
		// // 提會, 抓授審會決議
		// dispWord = "無";
		// }
		// sbDec.append("本案經提")
		// .append(XmlTool.replaceXMLReservedWord(Util.trim(model.getRptTitle1()),true))
		// .append("討論，結論：").append("\t").append(dispWord);
		// }
		// }
		// newVal.add(sbDec.toString());
		// // 替換第一範本資料
		// replaceStrB(sbWord, newVal, oldVal);
		// // 完成處理
		//
		// OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		// outWriter.write(sbWord.toString());
		// outWriter.close();
		//
		// } else {
		// // 印出找不到資料錯誤
		// //查無資料
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0036"), getClass());
		// }
		// } catch (FileNotFoundException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (IOException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (MissingResourceException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (Exception e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// }

		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 存取要被取代成第二範本資料內容的資料內容
			StringBuilder bgData = new StringBuilder();
			bgData.append(sbWord.toString());

			// 開始替換第一範本資料
			if (!oid.isEmpty()) {
				L120M01A model = service1205.findL120m01aByOid(oid);
				// 取得第二XML範本檔案名稱
				StringBuilder fileName2 = new StringBuilder();
				String fName2 = (UtilConstants.Casedoc.typCd.DBU.equals(Util
						.trim(model.getTypCd()))) ? "LMSDoc42.htm" : params
						.getString("fileName2");
				fileName2
						.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
						.append("word/").append(Util.trim(fName2));
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				oldVal.add("%LMSVAR001%");
				oldVal.add("%LMSVAR002%");
				oldVal.add("%LMSVAR003%");
				oldVal.add("%LMSVAR004%");
				oldVal.add("%LMSVAR005%");
				oldVal.add("%LMSVAR006%");
				StringBuilder sbDec = new StringBuilder();
				docName = "LMSDoc4";
				String hqMeetFlag = Util.trim(model.getHqMeetFlag());
				// 案由part1
				L120M01H l120m01h = service1205.findL120m01hByUniqueKey(
						model.getMainId(), "1");
				sbDec.setLength(0);
				if (Util.isEmpty(model.getGist()) || "".equals(model.getGist())) {
					if (l120m01h != null) {
						sbDec.append(Util.trim(l120m01h.getDispWord()));
					}
				} else {
					if (l120m01h != null) {
						sbDec.append(model.getGist()).append(strEnter)
								.append(strEnter)
								.append(Util.trim(l120m01h.getDispWord()));
					} else {
						sbDec.append(model.getGist()).append(strEnter)
								.append(strEnter).append("無");
					}
				}
				newVal.add(sbDec.toString());
				// 案由part2
				sbDec.setLength(0);
				if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
					// 如果是授管處的常董會->執行此條件
					sbDec.append("(")
							.append(branch.getBranchName(Util.trim(model
									.getCaseBrId()))).append("提)");
				} else {
					String title = "";
					// 若為OBU案件則需再加國金部字眼
					if ("4".equals(Util.trim(model.getTypCd()))) {
						// OBU
						title = "\\國金部承做";
					} else {
						title = "承做";
					}
					sbDec.append("授信管理處提\\")
							.append(branch.getBranchName(Util.trim(model
									.getCaseBrId()))).append(title);
				}
				newVal.add(sbDec.toString());
				// 客戶資料(第二範本區塊內容)
				newVal.add(getWord2(oid, fileName2.toString(), "", ""));
				// 敘做理由
				sbDec.setLength(0);
				// 若為授權外其他，則為空白
				if ("2".equals(Util.trim(model.getDocKind()))
						&& ("2".equals(Util.trim(model.getDocCode())) || "3"
								.equals(Util.trim(model.getDocCode())))) {
					sbDec.append("無");
				} else {
					L120M01D l120m01d = service1205.findL120m01dByUniqueKey(
							model.getMainId(), "4");
					if (l120m01d != null) {
						sbDec.append(Util.trim(l120m01d.getItemDscr()));
					} else {
						sbDec.append("無");
					}
				}
				newVal.add(Util.getDocRealImgPath(sbDec.toString()));
				// newVal.add(sbDec.toString());
				// 分行逾放比
				sbDec.setLength(0);

				List<L140M01A> listL140m01a = service1405
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (!listL140m01a.isEmpty()) {
					Date nplDate = null;
					HashMap<String, String> tempMap = new HashMap<String, String>();
					for (L140M01A l140m01a : listL140m01a) {
						String npl = Util.trim(l140m01a.getNpl());
						nplDate = l140m01a.getNpldate();
						if (Util.isNotEmpty(npl) && Util.isNotEmpty(nplDate)) {
							String[] nplArray = npl.split("、");
							for (String key : nplArray) {
								String tempKey = Util.trim(key);
								if (!tempMap.containsKey(tempKey)) {
									sbDec.append(sbDec.length() > 0 ? "，" : "");
									sbDec.append(tempKey);
									tempMap.put(tempKey, "");
								}
							}

							// sbDec.append("資料年月：")
							// .append(CapDate
							// .convertDateToTaiwanYear(TWNDate
							// .toAD(l140m01a.getNpldate())
							// .subSequence(0, 4)
							// .toString()))
							// .append("/")
							// .append(TWNDate.toAD(l140m01a.getNpldate())
							// .subSequence(5, 7).toString())
							// .append(" ")
							// .append(Util.trim(l140m01a.getNpl()))
							// .append("%").append(strEnter);

						}
					}
					sbDec.append(" ");
					sbDec.append("資料年月：");
					sbDec.append(CapDate.convertDateToTaiwanYear(TWNDate
							.toAD(nplDate).subSequence(0, 4).toString()));
					sbDec.append("/");
					sbDec.append(TWNDate.toAD(nplDate).subSequence(5, 7)
							.toString());
					sbDec.append(strEnter);
				}
				newVal.add(sbDec.toString());

				// 總處審查意見
				L120M01H l120m01h02 = service1205.findL120m01hByUniqueKey(
						model.getMainId(), "1");
				sbDec.setLength(0);
				// 國外部007 金控總部201 國金部025
				if (國外部.equals(user.getUnitNo())
						|| 金控總部.equals(user.getUnitNo())
						|| 國金部.equals(user.getUnitNo())
						|| 私銀處作業組.equals(user.getUnitNo())) {
					if ("".equals(model.getRptTitle1())
							|| Util.isEmpty(model.getRptTitle1())) {
						// 不提會, 抓法金處/個金處會簽意見
						// 授管處會簽意見
					} else {
						String dispWord = "";
						if (l120m01h02 != null) {
							// 提會, 抓授審會決議
							dispWord = Util.trim(l120m01h02.getDispWord());
						} else {
							dispWord = "無";
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t").append(dispWord);
					}
				} else {
					// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
					if ("".equals(model.getRptTitle1())
							|| Util.isEmpty(model.getRptTitle1())) {
						// 不提會, 抓授管處審查意見
						L120M01D l120m01d = service1205
								.findL120m01dByUniqueKey(model.getMainId(), "B");
						if (l120m01d != null) {
							sbDec.append(Util.trim(l120m01d.getItemDscr()));
						}
					} else {
						String dispWord = "";
						if (l120m01h02 != null) {
							// 提會, 抓授審會決議
							dispWord = Util.trim(l120m01h02.getDispWord());
						} else {
							// 提會, 抓授審會決議
							dispWord = "無";
						}
						sbDec.append("本案經提")
								.append(Util.trim(model.getRptTitle1()))
								.append("討論，結論：").append("\t").append(dispWord);
					}
				}
				newVal.add(sbDec.toString());
				// 替換第一範本資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理

				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();

			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿第二範本內容(個案討論)(已替換完成)
	 * 
	 * @param oid
	 *            使用者選擇文件oid
	 * @param fileName
	 *            第二範本路徑
	 * @param bgnData
	 *            第二範本資料開始字串
	 * @param endData
	 *            第二範本資料結束字串
	 * @return 常董稿第二範本內容
	 */
	@SuppressWarnings("unused")
	private String getWord2(String oid, String fileName, String bgnData,
			String endData) throws CapException {
		// 完整第二範本資料內容
		StringBuilder sbDoc = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取第二範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// fileInputStream = new FileInputStream(fileName);
			// inputStreamReader = new InputStreamReader(fileInputStream);
			// reader = new BufferedReader(inputStreamReader);
			// 存取範本內容
			StringBuilder getDoc = new StringBuilder();
			String str = FileUtils.readFileToString(file, "BIG5");
			getDoc.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int bgnIndex = getDoc.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = getDoc.indexOf(bodEnd, bgnIndex);
			StringBuilder dataDoc = new StringBuilder();
			dataDoc.append(getDoc.substring(bgnIndex, endIndex));
			// 存取暫存內容
			StringBuilder tmpDoc = new StringBuilder();
			// 存取被替換的資料
			List<String> listOld = new ArrayList<String>();
			listOld.add("%LMSREP001%");
			listOld.add("%LMSREP002%");
			listOld.add("%LMSREP003%");
			listOld.add("%LMSREP004%");
			listOld.add("%LMSREP005%");
			listOld.add("%LMSREP006%");
			listOld.add("%LMSREP007%");
			listOld.add("%LMSREP008%");
			listOld.add("%LMSREP009%");
			listOld.add("%LMSREP010%");
			listOld.add("%LMSREP011%");
			listOld.add("%LMSREP012%");
			listOld.add("%LMSREP013%");
			listOld.add("%LMSREP014%");
			listOld.add("%LMSREP015%");
			listOld.add("%LMSREP016%");
			listOld.add("%LMSREP017%");
			listOld.add("%LMSREP018%");
			listOld.add("%LMSREP019%");
			listOld.add("%LMSREP020%");
			listOld.add("%LMSREP021%");
			listOld.add("%LMSREP022%");
			listOld.add("%LMSREP023%");
			listOld.add("%LMSREP024%");
			listOld.add("%LMSREP025%");
			listOld.add("%LMSREP026%");
			listOld.add("%LMSREP027%");
			listOld.add("%LMSREP028%");
			listOld.add("%LMSREP028A%");
			listOld.add("%LMSREP028B%");
			listOld.add("%LMSREP028C%");
			listOld.add("%LMSREP028D%");
			listOld.add("%LMSREP028E%");
			listOld.add("%LMSREP029%");
			listOld.add("%LMSREP030%");
			listOld.add("%LMSREP031%");
			listOld.add("%LMSREP032%");
			listOld.add("%LMSREP033%");
			listOld.add("%LMSREP034%");
			// 存取替換後的資料
			List<String> listNew = new ArrayList<String>();
			L120M01A model = service1205.findL120m01aByOid(oid);
			if (model != null) {
				String mainId = Util.trim(model.getMainId());
				List<L120S01A> listL120s01a = service1205
						.findL120s01aByMainId(mainId);
				List<L140M01A> listL140m01a = service1405
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (listL120s01a.isEmpty()) {
					// 錯誤訊息
				} else {
					// 開始替換資料...
					for (L120S01A l120s01a : listL120s01a) {
						// 初始化暫存變數
						tmpDoc.setLength(0);
						// 初始化替換後資料
						listNew.clear();
						// 取得第二範本資料區塊並存到暫存變數中
						tmpDoc.append(dataDoc.toString());
						L120S01B l120s01b = service1205
								.findL120s01bByUniqueKey(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (l120s01b == null) {
							l120s01b = new L120S01B();
						}
						L120S01D l120s01d = service1205
								.findL120s01dByUniqueKey(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (l120s01d == null) {
							l120s01d = new L120S01D();
						}
						// 客戶名稱
						listNew.add(Util.trim(l120s01a.getCustName()));
						// 信用評等
						List<L120S01C> listL120s01c = service1205
								.findL120s01cByCustId(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (listL120s01c.isEmpty()) {
							listNew.add("無");
						} else {
							StringBuilder sbScore = new StringBuilder();
							sbScore.setLength(0);
							// 取得信用評等
							sbScore.append(getL120S01CData(listL120s01c));
							if (Util.isEmpty(sbScore.toString())) {
								listNew.add("無");
							} else {
								listNew.add("<br/>" + sbScore.toString());
							}
						}
						// 利害關係人授信
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt())) ? "有"
								: ("2".equals(Util.trim(l120s01d.getMbRlt())) ? "無"
										: "不適用"));
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt())) ? Util
								.trim(l120s01d.getMbRltDscr()) : "");
						// 負責人
						listNew.add("1".equals(Util.trim(l120s01b.getPosType())) ? "董事長"
								: "2".equals(Util.trim(l120s01b.getPosType())) ? "董事"
										: "3".equals(Util.trim(l120s01b
												.getPosType())) ? "負責人" : "9"
												.equals(Util.trim(l120s01b
														.getPosType())) ? "其他"
												: "無");
						listNew.add("".equals(Util.trim(l120s01b.getChairman())) ? "無"
								: Util.trim(l120s01b.getChairman()));
						// 總經理
						listNew.add("".equals(Util.trim(l120s01b.getGManager())) ? "無"
								: Util.trim(l120s01b.getGManager()));
						// 資本額登記幣別
						listNew.add("".equals(Util.trim(l120s01b.getRgtCurr())) ? "TWD"
								: Util.trim(l120s01b.getRgtCurr()));
						// 實收幣別
						listNew.add("".equals(Util.trim(l120s01b.getCptlCurr())) ? "TWD"
								: Util.trim(l120s01b.getCptlCurr()));
						// 資本額金額
						listNew.add("".equals(Util.trim(Util
								.nullToSpace(l120s01b.getRgtAmt()))) ? "0"
								: NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getRgtAmt()))));
						// 資本額單位
						listNew.add("1".equals(Util.trim(Util
								.nullToSpace(l120s01b.getRgtUnit()))) ? "元"
								: "1000".equals(Util.trim(Util
										.nullToSpace(l120s01b.getRgtUnit()))) ? "千元"
										: "10000".equals(Util.trim(Util
												.nullToSpace(l120s01b
														.getRgtUnit()))) ? "萬元"
												: "1000000".equals(Util.trim(Util
														.nullToSpace(l120s01b
																.getRgtUnit()))) ? "百萬元"
														: "元");
						// 實作金額
						listNew.add("".equals(Util.trim(Util
								.nullToSpace(l120s01b.getCptlAmt()))) ? "0"
								: NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getCptlAmt()))));
						// 實作單位
						listNew.add("1".equals(Util.trim(Util
								.nullToSpace(l120s01b.getCptlUnit()))) ? "元"
								: "1000".equals(Util.trim(Util
										.nullToSpace(l120s01b.getCptlUnit()))) ? "千元"
										: "10000".equals(Util.trim(Util
												.nullToSpace(l120s01b
														.getCptlUnit()))) ? "萬元"
												: "1000000".equals(Util.trim(Util
														.nullToSpace(l120s01b
																.getCptlUnit()))) ? "百萬元"
														: "元");
						// 成立日期
						listNew.add("".equals(Util.trim(CapDate.formatDate(
								l120s01b.getEstDate(), DATEYYYYMMDD))) ? "無"
								: Util.trim(CapDate.formatDate(
										l120s01b.getEstDate(), DATEYYYYMMDD)));
						// 註冊地
						if (Util.isEmpty(Util.trim(l120s01b.getNtCode()))) {
							listNew.add("無");
						} else {
							listNew.add(codeService.findByCodeTypeAndCodeValue(
									"CountryCode",
									Util.trim(l120s01b.getNtCode()))
									.getCodeDesc());
						}
						// 主要營業項目
						if (l120s01a.getCustId().equals(model.getCustId())
								&& l120s01a.getDupNo().equals(model.getDupNo())) {
							if (Util.isEmpty(model.getItemOfBusi())) {
								listNew.add("無");
							} else {
								listNew.add(Util.trim(model.getItemOfBusi()));
							}
						} else {
							listNew.add("無");
						}
						// 股票
						listNew.add("無");
						// 隸屬企業集團
						if (l120s01a.getCustId().equals(model.getCustId())
								&& l120s01a.getDupNo().equals(model.getDupNo())) {
							if (Util.isEmpty(l120s01b.getGroupNo())) {
								listNew.add("");
								listNew.add("無");
							} else {
								listNew.add("");
								listNew.add(Util.trim(l120s01b.getGroupName()));
							}
						} else {
							listNew.add("否");
							listNew.add("無");
						}
						// 大陸投資概況
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? "有"
								: "2".equals(Util.trim(l120s01b.getInvMFlag())) ? "無"
										: "不適用");
						// 赴大陸投資金額
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? Util
								.trim(l120s01b.getInvMCurr())
								+ " "
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getInvMAmt())))
								: "0");
						// 經濟部投審會核准金額
						listNew.add("1".equals(Util.trim(l120s01b.getInvMFlag())) ? Util
								.trim(l120s01b.getAprCurr())
								+ " "
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l120s01b.getAprAmt())))
								: "0");
						// 營運概況簡評 & 財務概況簡評
						// 若為授權外一般才處理
						if ("2".equals(model.getDocKind())) {
							L120S01G l120s01g1 = service1205
									.findL120s01gByUniqueKey(mainId,
											l120s01a.getCustId(),
											l120s01a.getDupNo(), "1");
							L120S01G l120s01g2 = service1205
									.findL120s01gByUniqueKey(mainId,
											l120s01a.getCustId(),
											l120s01a.getDupNo(), "2");
							if (l120s01g1 == null) {
								l120s01g1 = new L120S01G();
							}
							if (l120s01g2 == null) {
								l120s01g2 = new L120S01G();
							}
							listNew.add(Util.trim(l120s01g1.getDataDscr()));
							listNew.add(Util.trim(l120s01g2.getDataDscr()));
						} else {
							listNew.add("無");
							listNew.add("無");
						}
						boolean existData = false;
						if (!"[]".equals(listL140m01a.toString())) {
							for (L140M01A l140m01a : listL140m01a) {
								if (l120s01a.getCustId().equals(
										l140m01a.getCustId())
										&& l120s01a.getDupNo().equals(
												l140m01a.getDupNo())
										&& !existData) {
									// 前准額度幣別(額度明細表)
									listNew.add(Util.trim(l140m01a.getLVCurr()));
									// 前准餘額幣別
									listNew.add(Util.trim(l140m01a.getBLCurr()));
									// 前准額度金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getLVAmt()))));
									// 前准餘額金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getBLAmt()))));
									// 增/減額
									BigDecimal netTamt = (Util.isEmpty(Util
											.trim(Util.nullToSpace(l140m01a
													.getLoanTotAmt())))) ? BigDecimal.ZERO
											: l140m01a
													.getLoanTotAmt()
													.subtract(
															(Util.isEmpty(Util.trim(Util
																	.nullToSpace(l140m01a
																			.getLVTotAmt())))) ? BigDecimal.ZERO
																	: l140m01a
																			.getLVTotAmt());
									StringBuilder plusOrMin = new StringBuilder();
									if (netTamt.compareTo(new BigDecimal("0")) == 1) {
										plusOrMin.append("增額").append(
												l140m01a.getLVTotCurr());
									} else if (l140m01a.getBLAmt().compareTo(
											new BigDecimal("0")) == 0) {
										plusOrMin.append(l140m01a
												.getLVTotCurr());
									} else {
										plusOrMin.append("減額").append(
												l140m01a.getLVTotCurr());
									}
									plusOrMin.append(NumConverter.addComma(Util
											.trim(Util.nullToSpace(netTamt))));
									listNew.add(plusOrMin.toString());

									// 授信總額度申請合計幣別(額度明細表)
									listNew.add(Util.trim(l140m01a
											.getLoanTotCurr()));
									// 授信總額度申請合計金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getLoanTotAmt()))));
									// 授信總額度其中擔保合計幣別(額度明細表)
									listNew.add(Util.trim(l140m01a
											.getAssureTotCurr()));
									// 授信總額度其中擔保合計金額(額度明細表)
									listNew.add(NumConverter.addComma(Util
											.trim(Util.nullToSpace(l140m01a
													.getAssureTotAmt()))));
									existData = true;
								}
							}
							if (!existData) {
								// 授信總額度申請合計幣別(額度明細表)
								listNew.add("TWD");
								// 授信總額度其中擔保合計幣別(額度明細表)
								listNew.add("TWD");
								// 授信總額度申請合計金額(額度明細表)
								listNew.add("0");
								// 授信總額度其中擔保合計金額(額度明細表)
								listNew.add("0");
							}
						} else {
							// 授信總額度申請合計幣別(額度明細表)
							listNew.add("TWD");
							// 授信總額度其中擔保合計幣別(額度明細表)
							listNew.add("TWD");
							// 授信總額度申請合計金額(額度明細表)
							listNew.add("0");
							// 授信總額度其中擔保合計金額(額度明細表)
							listNew.add("0");
						}

						// 前貸期間平均動用率
						L120S01F l120s01f = service1205
								.findL120s01fByUniqueKey(mainId,
										l120s01a.getCustId(),
										l120s01a.getDupNo());
						if (l120s01f == null) {
							listNew.add("無");
						} else {
							listNew.add(Util.trim(Util.nullToSpace(l120s01f
									.getAvgURate())) + "%");
						}
						// 中長期償債能力分析(主要借款人才有)
						if (l120s01a.getCustId().equals(model.getCustId())
								&& l120s01a.getDupNo().equals(model.getDupNo())) {
							C140SFFF c140sfff = service1205.getC140SFFF(mainId,
									mainId, "bfp_note1_1", "A1");
							if (c140sfff == null) {
								listNew.add("無");
							} else {
								listNew.add(Util.getDocRealImgPath(c140sfff
										.getFfbody()));
							}
						} else {
							listNew.add("無");
						}

						// Miller added at 2012/08/31
						// 利害關係人應收帳款承購無追索權-買方
						if ("2".equals(Util.trim(model.getDocType()))) {
							// 個金預設塞空白
							listNew.add("");
							listNew.add("");
						} else {
							// 企金
							if ("1".equals(Util.trim(l120s01d.getFctMbRlt()))
									|| "1".equals(Util.trim(l120s01d
											.getFctMhRlt()))) {
								// 有
								listNew.add("有");
								listNew.add(Util.trim(l120s01d
										.getFctMhRltDscr()));
							} else if ("2".equals(Util.trim(l120s01d
									.getFctMbRlt()))
									|| "2".equals(Util.trim(l120s01d
											.getFctMhRlt()))) {
								// 無
								listNew.add("無");
								listNew.add("");
							} else {
								// 不適用
								listNew.add("不適用");
								listNew.add("");
							}
						}

						// 利害關係人授信
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt33())) ? "有"
								: ("2".equals(Util.trim(l120s01d.getMbRlt33())) ? "無"
										: "不適用"));
						listNew.add("1".equals(Util.trim(l120s01d.getMbRlt33())) ? Util
								.trim(l120s01d.getMbRltDscr33()) : "");

						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tmpDoc, listNew, listOld);
						// 將替換好的資料存到完整資料變數中
						sbDoc.append(tmpDoc.toString());
						// 換行
						sbDoc.append(tblEnter);
					}
				}
			}
		} catch (Exception e) {

		}
		return sbDoc.toString();
	}

	/**
	 * 取得常董稿(彙總討論)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3b(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		// 取得第二XML範本檔案名稱
		StringBuilder fileName2 = new StringBuilder();
		String fName2 = params.getString("fileName2");
		fileName2.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName2));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得使用者所選擇資料之oid
		String oid = params.getString("oid");
		// 取得文件狀態
		String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		// try {
		// // 開始讀取第一範本檔案
		// URL urlRpt = null;
		// urlRpt =
		// Thread.currentThread().getContextClassLoader().getResource(fileName.toString());
		// File file=new File(urlRpt.toURI());
		// fileInputStream = new FileInputStream(file);
		// inputStreamReader = new InputStreamReader(fileInputStream);
		// reader = new BufferedReader(inputStreamReader);
		//
		// String bgStr = "</w:tbl>";
		// // 插入第二範本資料開始字串
		// String strChk = "<w:p";
		// // 插入第二範本資料結束字串
		// String endChk = "</w:r></w:p>";
		// // 第二範本資料開始字串
		// String bgnData = "<w:tbl>";
		// // 第二範本資料結束字串
		// String endData = "</w:tbl>";
		// // 讀取第一範本資料
		// String str = FileUtils.readFileToString(file, "UTF-8");
		// sbWord.append(str);
		// if(inputStreamReader!= null){
		// inputStreamReader.close();
		// }
		//
		// int bgnTest = sbWord.indexOf(bgStr);
		// // 插入第二範本資料開始字串位置
		// int bgnIndex = sbWord.indexOf(strChk, bgnTest);
		// bgnIndex = sbWord.indexOf(strChk, bgnIndex);
		// bgnIndex = sbWord.indexOf(strChk, bgnIndex);
		// // 插入第二範本資料結束字串位置
		// int endIndex = sbWord.indexOf(endChk, bgnIndex) + endChk.length();
		//
		// // 存取要被取代成第二範本資料內容的資料內容
		// StringBuilder bgData = new StringBuilder();
		// bgData.append(sbWord.substring(bgnIndex, endIndex));
		//
		// // 開始替換第一範本資料
		// if (!oid.isEmpty()) {
		// List<String> oldVal = new ArrayList<String>();
		// List<String> newVal = new ArrayList<String>();
		// // 替換表格的關係要連同標籤一起取代
		// oldVal.add(bgData.toString());
		// docName = "LMSDoc51";
		//
		// // 客戶資料(第二範本區塊內容)
		// newVal.add(getWord2b(oid, fileName2.toString(), bgnData, endData,
		// txCode));
		//
		// // 替換第一範本資料
		// replaceStrB(sbWord, newVal, oldVal);
		// // 完成處理
		// OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		// outWriter.write(sbWord.toString());
		// outWriter.close();
		// } else {
		// // 印出找不到資料錯誤
		// //查無資料
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0036"), getClass());
		// }
		// } catch (FileNotFoundException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (IOException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (MissingResourceException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (Exception e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// }
		try {
			// 開始讀取第一範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 開始替換第一範本資料
			if (!oid.isEmpty()) {
				List<String> oldVal = new ArrayList<String>();
				List<String> newVal = new ArrayList<String>();
				// 替換表格的關係要連同標籤一起取代
				oldVal.add("%LMSVAR001%");
				docName = "LMSDoc51";

				// 客戶資料(第二範本區塊內容)
				newVal.add(getWord2b(oid, fileName2.toString(), "", "", txCode));

				// 替換第一範本資料
				replaceStrB(sbWord, newVal, oldVal);
				// 完成處理
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();
			} else {
				// 印出找不到資料錯誤
				// 查無資料
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * 取得常董稿第二範本內容(彙總討論)(已替換完成)
	 * 
	 * @param oid
	 *            使用者選擇文件oid
	 * @param fileName
	 *            第二範本路徑
	 * @param bgnData
	 *            第二範本資料開始字串
	 * @param endData
	 *            第二範本資料結束字串
	 * @param txCode
	 *            文件狀態
	 * @return 常董稿第二範本內容
	 */
	@SuppressWarnings("unused")
	private String getWord2b(String oid, String fileName, String bgnData,
			String endData, String txCode) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 完整第二範本資料內容
		StringBuilder sbDoc = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;
		try {
			// 開始讀取第二範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);
			// fileInputStream = new FileInputStream(fileName);
			// inputStreamReader = new InputStreamReader(fileInputStream);
			// reader = new BufferedReader(inputStreamReader);
			// 存取範本內容
			StringBuilder getDoc = new StringBuilder();
			String str = FileUtils.readFileToString(file, "BIG5");
			getDoc.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}

			// 找尋資料區塊開始字串
			String bodStr = "<div class=Section1";
			// 找尋資料區塊結束字串
			String bodEnd = "</body>";
			// 資料區塊開始位置
			int bgnIndex = getDoc.indexOf(bodStr, 0);
			// 資料區塊結束位置
			int endIndex = getDoc.indexOf(bodEnd, bgnIndex);
			StringBuilder dataDoc = new StringBuilder();
			dataDoc.append(getDoc.substring(bgnIndex, endIndex));
			// 存取暫存內容
			StringBuilder tmpDoc = new StringBuilder();
			// 存取被替換的資料
			List<String> listOld = new ArrayList<String>();
			listOld.add("%LMSREP001%");
			listOld.add("%LMSREP002%");
			listOld.add("%LMSREP003%");
			listOld.add("%LMSREP004%");
			listOld.add("%LMSREP005%");
			listOld.add("%LMSREP006%");
			listOld.add("%LMSREP007%");
			listOld.add("%LMSREP008%");
			listOld.add("%LMSREP009%");
			listOld.add("%LMSREP010%");
			listOld.add("%LMSREP011%");
			listOld.add("%LMSREP012%");
			listOld.add("%LMSREP013%");
			listOld.add("%LMSREP014%");
			listOld.add("%LMSREP015%");
			listOld.add("%LMSREP016%");
			listOld.add("%LMSREP017%");
			listOld.add("%LMSREP018%");
			listOld.add("%LMSREP019%");
			// 存取替換後的資料
			List<String> listNew = new ArrayList<String>();
			L120M01A model = service1205.findL120m01aByOid(oid);
			if (model != null) {
				String hqMeetFlag = Util.trim(model.getHqMeetFlag());
				String mainId = Util.trim(model.getMainId());
				List<L140M01A> listL140m01a = service1405
						.findL140m01aListByL120m01cMainId(mainId,
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (listL140m01a.isEmpty()) {
					// 錯誤訊息
				} else {
					// 換行
					sbDoc.append(tblEnter);
					// 開始替換資料...
					for (L140M01A l140m01a : listL140m01a) {
						String l140MainId = Util.trim(l140m01a.getMainId());
						// 初始化暫存變數
						tmpDoc.setLength(0);
						// 初始化替換後資料
						listNew.clear();
						// 取得第二範本資料區塊並存到暫存變數中
						tmpDoc.append(dataDoc.toString());
						L120S01D l120s01d = service1205
								.findL120s01dByUniqueKey(mainId,
										l140m01a.getCustId(),
										l140m01a.getDupNo());
						if (l120s01d == null) {
							l120s01d = new L120S01D();
						}
						L140M01B l140m01b1 = service1405.findL140m01bUniqueKey(
								l140MainId, "3");
						L140M01B l140m01b2 = service1405.findL140m01bUniqueKey(
								l140MainId, "4");
						L140M01B l140m01b3 = service1405.findL140m01bUniqueKey(
								l140MainId, "5");
						if (l140m01b1 == null) {
							l140m01b1 = new L140M01B();
						}
						if (l140m01b2 == null) {
							l140m01b2 = new L140M01B();
						}
						if (l140m01b3 == null) {
							l140m01b3 = new L140M01B();
						}
						// 營業單位
						listNew.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 客戶名稱
						listNew.add(Util.trim(l140m01a.getCustName()));
						// 銀行法所稱利害關係人
						// 如果為企金則讀取有無利害關係人
						if ("1".equals(model.getDocType())) {
							listNew.add(XmlTool.replaceXMLReservedWord(
									Util.trim("1".equals(l120s01d.getMbRlt()) ? "有"
											: "2".equals(l120s01d.getMbRlt()) ? "無"
													: "不適用"), true));
						} else {
							listNew.add("無");
						}
						// 信用評等
						List<L120S01C> listL120s01c = service1205
								.findL120s01cByCustId(mainId,
										l140m01a.getCustId(),
										l140m01a.getDupNo());
						if (listL120s01c.isEmpty()) {
							listNew.add("無");
						} else {
							StringBuilder sbScore = new StringBuilder();
							sbScore.setLength(0);
							// 取得信用評等
							sbScore.append(getL120S01CData(listL120s01c));
							if (Util.isEmpty(sbScore.toString())) {
								listNew.add("無");
							} else {
								listNew.add(sbScore.toString());
							}
						}
						// 授信科目
						listNew.add(Util.trim(l140m01a.getLnSubject()));
						// 授信額度
						listNew.add(Util.trim(l140m01a.getCurrentApplyCurr()));
						listNew.add(NumConverter.addComma(Util.trim(Util
								.nullToSpace(l140m01a.getCurrentApplyAmt()))));
						listNew.add("元");
						// 擔保品
						listNew.add(Util.trim(l140m01b1.getItemDscr()));
						// 其他敘做條件
						listNew.add(Util.trim(l140m01b2.getItemDscr()));
						// 動用期限
						listNew.add(getUseDeadline(
								Util.trim(l140m01a.getUseDeadline()),
								Util.trim(l140m01a.getDesp1())));
						// 清償期限
						listNew.add(Util.trim(l140m01a.getPayDeadline()));
						// 保證人
						listNew.add(Util.trim(l140m01a.getGuarantor()));
						// 申請續約、敘作條件異動情形
						listNew.add(Util.trim(l140m01b3.getItemDscr()));
						// 授信總額度
						listNew.add(Util.trim(l140m01a.getLoanTotCurr()));
						listNew.add(NumConverter.addComma(Util.trim(Util
								.nullToSpace(l140m01a.getLoanTotAmt()))));
						listNew.add("元");
						// XX審核意見
						if ("3".equals(hqMeetFlag) || "C".equals(hqMeetFlag)) {
							listNew.add("營業單位敘做理由");
						} else {
							listNew.add("總處審核意見");
						}
						// 審核意見內容
						L120M01H l120m01h02 = service1205
								.findL120m01hByUniqueKey(model.getMainId(),
										hqMeetFlag);
						// 國外部007
						if (國外部.equals(user.getUnitNo())) {
							if ("".equals(model.getRptTitle1())
									|| Util.isEmpty(model.getRptTitle1())) {
								// 不提會, 抓法金處/個金處會簽意見
								// 授管處會簽意見
							} else {
								if (l120m01h02 != null) {
									// 提會, 抓授審會決議
									listNew.add(Util.trim(l120m01h02
											.getMeetingNote()));
								} else {
									// 若找不到決議內容則設為無
									listNew.add("無");
								}
							}
						} else {
							// 若在法金處/個金處產生時，則國外部抓會簽, 一般分行抓法金處/個金處審查意見
							if ("".equals(model.getRptTitle1())
									|| Util.isEmpty(model.getRptTitle1())) {
								// 不提會, 抓授管處審查意見
								L120M01D l120m01d = service1205
										.findL120m01dByUniqueKey(
												model.getMainId(), "B");
								if (l120m01d != null) {
									listNew.add(Util.trim(l120m01d
											.getItemDscr()));
								} else {
									// 若找不到決議內容則設為無
									listNew.add("無");
								}
							} else {
								if (l120m01h02 != null) {
									// 提會, 抓授審會決議
									listNew.add(Util.trim(l120m01h02
											.getMeetingNote()));
								} else {
									// 若找不到決議內容則設為無
									listNew.add("無");
								}
							}
						}
						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tmpDoc, listNew, listOld);
						// 將替換好的資料存到完整資料變數中
						sbDoc.append(tmpDoc.toString());
						// 換行
						sbDoc.append(tblEnter);
					}
				}
			}
		} catch (Exception e) {

		}
		return sbDoc.toString();
	}

	/**
	 * 取得常董稿(常董稿常董會授權總經理逕行核定案件)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unused")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public OutputStream getWord3c(PageParameters params)
			throws CapException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		// 文件名稱
		String docName1 = "";
		String docName2 = "";
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 取得XML範本檔案名稱
		StringBuilder fileName = new StringBuilder();
		String fName = params.getString("fileName");
		fileName.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName));
		StringBuilder fileName2 = new StringBuilder();
		String fName2 = params.getString("fileName2");
		fileName2.append(Util.trim(PropUtil.getProperty("loadFile.dir")))
				.append("word/").append(Util.trim(fName2));
		CapAjaxFormResult result = new CapAjaxFormResult();
		// 取得list中所有資料組成的字串
		String listOid = params.getString("listOid");
		// 取得sign的資料
		String strSign = Util.nullToSpace(params.getString("sign"));
		// 將已取得的字串轉換成一陣列，分割辨識為sign內容
		String[] oidArray = listOid.split(strSign);
		// 取得文件狀態
		// String txCode = params.getString("txCode");
		// 儲存完整Word內容
		StringBuilder sbWord = new StringBuilder();
		StringBuilder sbWord2 = new StringBuilder();
		FileInputStream fileInputStream = null;
		InputStreamReader inputStreamReader = null;
		BufferedReader reader = null;

		FileInputStream fileInputStream2 = null;
		InputStreamReader inputStreamReader2 = null;
		BufferedReader reader2 = null;
		// try {
		// // 開始讀取範本檔案
		// URL urlRpt = null;
		// urlRpt =
		// Thread.currentThread().getContextClassLoader().getResource(fileName.toString());
		// URL urlRpt2 = null;
		// urlRpt2 =
		// Thread.currentThread().getContextClassLoader().getResource(fileName2.toString());
		// File file=new File(urlRpt.toURI());
		// fileInputStream = new FileInputStream(file);
		// inputStreamReader = new InputStreamReader(fileInputStream);
		// reader = new BufferedReader(inputStreamReader);
		//
		// File file2=new File(urlRpt2.toURI());
		// fileInputStream2 = new FileInputStream(file2);
		// inputStreamReader2 = new InputStreamReader(fileInputStream2);
		// reader2 = new BufferedReader(inputStreamReader2);
		//
		// // 表頭開始字串
		// String bgnStr = "<w:tr";
		// // 表尾開始字串
		// String endStr = "</w:tr>";
		// // 第一個變數字串
		// String strChk = "%LMSREP001%";
		// // 讀取第一範本資料
		// String str = FileUtils.readFileToString(file, "UTF-8");
		// sbWord.append(str);
		// if(inputStreamReader!= null){
		// inputStreamReader.close();
		// }
		// docName1 = "LMSDoc61";
		// // 讀取第二範本資料
		// String str2 = FileUtils.readFileToString(file2, "UTF-8");
		// sbWord2.append(str2);
		// if(inputStreamReader2!= null){
		// inputStreamReader2.close();
		// }
		// docName2 = "LMSDoc62";
		// // 表頭區塊
		// StringBuilder sbHeader = new StringBuilder();
		// sbHeader.append(getDocXML(sbWord2, "H", bgnStr, endStr, strChk));
		//
		// // 資料區塊
		// StringBuilder sbData = new StringBuilder();
		// sbData.append(getDocXML(sbWord2, "D", bgnStr, endStr, strChk));
		//
		// // 表尾區塊
		// StringBuilder sbFooter = new StringBuilder();
		// sbFooter.append(getDocXML(sbWord2, "F", bgnStr, endStr, strChk));
		//
		// // 開始替換第二範本資料
		// List<String> oldVal = new ArrayList<String>();
		// oldVal.add("%LMSREP001%");
		// oldVal.add("%LMSREP002%");
		// oldVal.add("%LMSREP003%");
		// oldVal.add("%LMSREP004%");
		// oldVal.add("%LMSREP005%");
		// oldVal.add("%LMSREP006%");
		// oldVal.add("%LMSREP007%");
		// oldVal.add("%LMSREP008%");
		// List<String> newVal = new ArrayList<String>();
		// StringBuilder getData = new StringBuilder();
		// getData.append(sbData.toString());
		// sbData.setLength(0);
		// StringBuilder tempData = new StringBuilder();
		// for (int i = 0; i < oidArray.length; i++) {
		// newVal.clear();
		// tempData.setLength(0);
		// tempData.append(getData.toString());
		// L120M01A model = service1205.findL120m01aByOid(oidArray[i]);
		// List<L140M01A> listL140M01A = service1405
		// .findL140m01aListByL120m01cMainId(model.getMainId(),
		// "1");
		// if (listL140M01A == null
		// || listL140M01A.isEmpty()) {
		// // 承辦營業單位
		// newVal.add("無");
		// // 戶名
		// newVal.add("無");
		// // 授信科目
		// newVal.add("無");
		// // 額度(單位：元)
		// newVal.add("無");
		// // 動用期限
		// newVal.add("無");
		// // 擔保品
		// newVal.add("無");
		// // 連保人
		// newVal.add("無");
		// // 續約、敘做條件異動情形
		// newVal.add("無");
		// } else {
		// for (L140M01A l140m01a : listL140M01A) {
		// L140M01B l140m01b1 = service1405.findL140m01bUniqueKey(
		// l140m01a.getMainId(), "3");
		// L140M01B l140m01b2 = service1405.findL140m01bUniqueKey(
		// l140m01a.getMainId(), "5");
		// if (l140m01b1 == null) {
		// l140m01b1 = new L140M01B();
		// } else if (l140m01b2 == null) {
		// l140m01b2 = new L140M01B();
		// }
		// // 承辦營業單位
		// newVal.add(XmlTool.replaceXMLReservedWord(branch.getBranchName(Util.trim(l140m01a
		// .getOwnBrId())),true));
		// // 戶名
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getCustName()),true));
		// // 授信科目
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getLnSubject()),true));
		// // 額度(單位：元)
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(Util.nullToSpace(l140m01a
		// .getCurrentApplyCurr())),true) +
		// XmlTool.replaceXMLReservedWord(NumConverter.addComma(Util.trim(Util.nullToSpace(l140m01a
		// .getCurrentApplyAmt()))),true));
		// // 動用期限
		// newVal.add(XmlTool.replaceXMLReservedWord(getUseDeadline(Util.trim(l140m01a.getUseDeadline()),
		// Util.trim(l140m01a.getDesp1())),true));
		// // 擔保品
		// newVal.add("<![CDATA["
		// +
		// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(l140m01b1.getItemDscr()))
		// + "]]>",true));
		// // 連保人
		// newVal.add(XmlTool.replaceXMLReservedWord(Util.trim(l140m01a.getGuarantor()),true));
		// // 續約、敘做條件異動情形
		// newVal.add("<![CDATA["
		// +
		// XmlTool.replaceXMLReservedWord(filterHtml(Util.trim(l140m01b2.getItemDscr()))
		// + "]]>",true));
		// }
		// }
		// // 將範本資料內容替換成暫存資料內容
		// replaceStrB(tempData, newVal, oldVal);
		// // 將替換好的資料存到完整資料變數中
		// sbData.append(tempData.toString());
		// }
		// // 最後將所有區塊串起來(表頭+資料區塊+表尾)
		// sbWord2.setLength(0);
		// sbWord2.append(sbHeader).append(sbData).append(sbFooter);
		//
		// String docTempFile = params.getString("docTempFile");
		// if("LMSDoc61.doc".equals(docTempFile)){
		// OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		// outWriter.write(sbWord.toString());
		// outWriter.close();
		// }else{
		// OutputStreamWriter outWriter = new OutputStreamWriter(baos, "UTF-8");
		// outWriter.write(sbWord2.toString());
		// outWriter.close();
		// }
		// } catch (FileNotFoundException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (IOException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (MissingResourceException e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// } catch (Exception e) {
		// throw new CapMessageException(getMessage(e.getMessage()),
		// getClass());
		// }
		try {
			// 開始讀取範本檔案
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(fileName.toString());
			URL urlRpt2 = null;
			urlRpt2 = Thread.currentThread().getContextClassLoader()
					.getResource(fileName2.toString());
			File file = new File(urlRpt.toURI());
			fileInputStream = new FileInputStream(file);
			inputStreamReader = new InputStreamReader(fileInputStream);
			reader = new BufferedReader(inputStreamReader);

			File file2 = new File(urlRpt2.toURI());
			fileInputStream2 = new FileInputStream(file2);
			inputStreamReader2 = new InputStreamReader(fileInputStream2);
			reader2 = new BufferedReader(inputStreamReader2);

			// 讀取第一範本資料
			String str = FileUtils.readFileToString(file, "BIG5");
			sbWord.append(str);
			if (inputStreamReader != null) {
				inputStreamReader.close();
			}
			docName1 = "LMSDoc61";
			// 讀取第二範本資料
			String str2 = FileUtils.readFileToString(file2, "BIG5");
			sbWord2.append(str2);
			if (inputStreamReader2 != null) {
				inputStreamReader2.close();
			}
			docName2 = "LMSDoc62";

			// 找尋資料區塊開始字串
			String bodStr = "</thead>";
			// 找尋資料區塊結束字串
			String bodEnd = "</table>";
			// 資料區塊開始位置
			int strBod = sbWord2.indexOf(bodStr, 0) + bodStr.length();
			// 資料區塊結束位置
			int endBod = sbWord2.indexOf(bodEnd, strBod);

			// 表頭區塊
			StringBuilder sbHeader = new StringBuilder();
			sbHeader.append(sbWord2.substring(0, strBod));
			// 資料區塊
			StringBuilder sbData = new StringBuilder();
			sbData.append(sbWord2.substring(strBod, endBod));
			// 表尾區塊
			StringBuilder sbFooter = new StringBuilder();
			sbFooter.append(sbWord2.substring(endBod));

			// 開始替換第二範本資料
			List<String> oldVal = new ArrayList<String>();
			oldVal.add("%LMSREP001%");
			oldVal.add("%LMSREP002%");
			oldVal.add("%LMSREP003%");
			oldVal.add("%LMSREP004%");
			oldVal.add("%LMSREP005%");
			oldVal.add("%LMSREP006%");
			oldVal.add("%LMSREP007%");
			oldVal.add("%LMSREP008%");
			List<String> newVal = new ArrayList<String>();
			StringBuilder getData = new StringBuilder();
			getData.append(sbData.toString());
			sbData.setLength(0);
			StringBuilder tempData = new StringBuilder();
			for (int i = 0; i < oidArray.length; i++) {
				L120M01A model = service1205.findL120m01aByOid(oidArray[i]);
				List<L140M01A> listL140M01A = service1405
						.findL140m01aListByL120m01cMainId(model.getMainId(),
								UtilConstants.Cntrdoc.ItemType.額度明細表);
				if (listL140M01A == null || listL140M01A.isEmpty()) {
					newVal.clear();
					tempData.setLength(0);
					tempData.append(getData.toString());
					// 承辦營業單位
					newVal.add("無");
					// 戶名
					newVal.add("無");
					// 授信科目
					newVal.add("無");
					// 額度(單位：元)
					newVal.add("無");
					// 動用期限
					newVal.add("無");
					// 擔保品
					newVal.add("無");
					// 連保人
					newVal.add("無");
					// 續約、敘做條件異動情形
					newVal.add("無");

					// 將範本資料內容替換成暫存資料內容
					replaceStrB(tempData, newVal, oldVal);
					// 將替換好的資料存到完整資料變數中
					sbData.append(tempData.toString());
				} else {
					for (L140M01A l140m01a : listL140M01A) {
						newVal.clear();
						tempData.setLength(0);
						tempData.append(getData.toString());
						L140M01B l140m01b1 = service1405.findL140m01bUniqueKey(
								l140m01a.getMainId(), "3");
						L140M01B l140m01b2 = service1405.findL140m01bUniqueKey(
								l140m01a.getMainId(), "5");
						if (l140m01b1 == null) {
							l140m01b1 = new L140M01B();
						} else if (l140m01b2 == null) {
							l140m01b2 = new L140M01B();
						}
						// 承辦營業單位
						newVal.add(branch.getBranchName(Util.trim(l140m01a
								.getOwnBrId())));
						// 戶名
						newVal.add(Util.trim(l140m01a.getCustName()));
						// 授信科目
						newVal.add(Util.trim(l140m01a.getLnSubject()));
						// 額度(單位：元)
						newVal.add(Util.trim(Util.nullToSpace(l140m01a
								.getCurrentApplyCurr()))
								+ NumConverter.addComma(Util.trim(Util
										.nullToSpace(l140m01a
												.getCurrentApplyAmt()))));
						// 動用期限
						newVal.add(getUseDeadline(
								Util.trim(l140m01a.getUseDeadline()),
								Util.trim(l140m01a.getDesp1())));
						// 擔保品
						newVal.add(Util.trim(l140m01b1.getItemDscr()));
						// 連保人
						newVal.add(Util.trim(l140m01a.getGuarantor()));
						// 續約、敘做條件異動情形
						newVal.add(Util.trim(l140m01b2.getItemDscr()));

						// 將範本資料內容替換成暫存資料內容
						replaceStrB(tempData, newVal, oldVal);
						// 將替換好的資料存到完整資料變數中
						sbData.append(tempData.toString());
					}
				}
			}
			// 最後將所有區塊串起來(表頭+資料區塊+表尾)
			sbWord2.setLength(0);
			sbWord2.append(sbHeader).append(sbData).append(sbFooter);

			String docTempFile = params.getString("docTempFile");
			if ("LMSDoc61.doc".equals(docTempFile)) {
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord.toString());
				outWriter.close();
			} else {
				OutputStreamWriter outWriter = new OutputStreamWriter(baos,
						"BIG5");
				outWriter.write(sbWord2.toString());
				outWriter.close();
			}
		} catch (FileNotFoundException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (IOException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (MissingResourceException e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		} catch (Exception e) {
			throw new CapMessageException(getMessage(e.getMessage()),
					getClass());
		}
		return baos;
	}

	/**
	 * Common 取得議程文件區塊
	 * 
	 * @param rcd
	 *            整份XML文件
	 * @param strFlag
	 *            欲取得的區塊內容(Header: H, Data: D, Footer: F)
	 * @param bgnStr
	 *            表頭開始字串
	 * @param endStr
	 *            表尾開始字串
	 * @param strChk
	 *            第一變數開始字串
	 * @return 區塊內容
	 */
	public StringBuilder getDocXML(StringBuilder rcd, String strFlag,
			String bgnStr, String endStr, String strChk) {
		// 負責存取資料區塊內容
		StringBuilder sbChk = new StringBuilder();
		// 負責存取結果區塊內容
		StringBuilder sbRtn = new StringBuilder();
		// 初始化表頭位置點
		int bgnPos = 0;
		// 初始化表尾位置點
		int endPos = 0;
		// 初始化檢查第一個變數字串位置點
		int chkPos = 0;
		// 設定檢查點(是否找不到第一個變數字串位置？找不到為真，找到為假)為真
		boolean bChk = true;

		// 增加防錯措施
		chkPos = rcd.indexOf(strChk, 0);
		if (chkPos < 0) {
			bChk = false;
		}

		// 初始化結果區塊內容
		sbRtn.setLength(0);

		// 當檢查點為真(即找不到第一個變數字串位置時)執行此迴圈
		while (bChk) {
			// 初始化資料區塊內容
			sbChk.setLength(0);
			// 依照表頭開始字串找到表頭開始位置
			bgnPos = rcd.indexOf(bgnStr, bgnPos);
			// 依照表尾開始字串找到表尾開始位置
			endPos = rcd.indexOf(endStr, bgnPos) + endStr.length();
			// 表頭位置和表尾位置之間的內容(資料區塊內容)存取下來
			sbChk.append(rcd.substring(bgnPos, endPos));
			// 依照第一變數字串找到其存在位置點
			chkPos = sbChk.indexOf(strChk, 0);
			// 若第一變數字串實際存在則會大於零，否則會小於零
			if (chkPos > 0) {
				// 第一變數字串實際存在設定檢查點為假
				bChk = false;
			}
			// 第一變數字串實際存在時執行此迴圈
			if (!bChk) {
				// 取得表頭區塊資料內容
				if ("H".equals(strFlag)) {
					sbRtn.append(rcd.substring(0, bgnPos));
				}
				// 取得表尾區塊資料內容
				if ("F".equals(strFlag)) {
					sbRtn.append(rcd.substring(endPos));
				}
				// 取得資料區塊內容
				if ("D".equals(strFlag)) {
					sbRtn.append(sbChk.toString());
				}
			}
			// 將表頭位置設成表尾位置(目的在於讓檢查點找不到第一變數位置，因為已經完成任務)
			if (bgnPos > 0) {
				bgnPos = endPos;
			} else {
				bChk = false;
			}
		}
		// 將結果區塊資料內容回傳
		return sbRtn;
	}

	/**
	 * 取代StringBuilder字串內容(不需輸入取代位置且迴圈替換)
	 * 
	 * @param strB
	 *            StringBuilder內容
	 * @param oldVal
	 *            原字串
	 * @param newVal
	 *            取代後的字串
	 */
	private void replaceStrB(StringBuilder strB, List<String> newVal,
			List<String> oldVal) {
		String str = strB.toString();
		for (int i = 0; i < oldVal.size(); i++) {
			str = str.replace(oldVal.get(i), newVal.get(i));
		}
		strB.replace(0, strB.length(), str);
	}

	/**
	 * 
	 * 基本功能：替換標記以正常顯示
	 * <p>
	 * 
	 * @param input
	 *            String
	 * @return String
	 */
	public String replaceTag(String input) {
		if (!hasSpecialChars(input)) {
			return input;
		}
		StringBuffer filtered = new StringBuffer(input.length());
		char c;
		for (int i = 0; i <= input.length() - 1; i++) {
			c = input.charAt(i);
			switch (c) {
			case '<':
				filtered.append("&lt;");
				break;
			case '>':
				filtered.append("&gt;");
				break;
			case '"':
				filtered.append("&quot;");
				break;
			case '&':
				filtered.append("&amp;");
				break;
			default:
				filtered.append(c);
			}

		}
		return (filtered.toString());
	}

	/**
	 * 
	 * 基本功能：判斷標記是否存在
	 * <p>
	 * 
	 * @param input
	 *            String
	 * @return boolean
	 */
	public boolean hasSpecialChars(String input) {
		boolean flag = false;
		if ((input != null) && (input.length() > 0)) {
			char c;
			for (int i = 0; i <= input.length() - 1; i++) {
				c = input.charAt(i);
				switch (c) {
				case '>':
					flag = true;
					break;
				case '<':
					flag = true;
					break;
				case '"':
					flag = true;
					break;
				case '&':
					flag = true;
					break;
				}
			}
		}
		return flag;
	}

	/**
	 * 
	 * 基本功能：過濾所有以"<"開頭以">"結尾的標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @return String
	 */
	public static String filterHtml(String str) {
		Pattern pattern = Pattern.compile(regxpForHtml);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 
	 * 基本功能：過濾指定標籤
	 * <p>
	 * 
	 * @param str
	 *            String
	 * @param tag
	 *            指定標籤
	 * @return String
	 */
	public static String fiterHtmlTag(String str, String tag) {
		String regxp = "<\\s*" + tag + "\\s+([^>]*)\\s*>";
		Pattern pattern = Pattern.compile(regxp);
		Matcher matcher = pattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result1 = matcher.find();
		while (result1) {
			matcher.appendReplacement(sb, "");
			result1 = matcher.find();
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 
	 * 基本功能：替換指定的標籤
	 * <p>
	 * 
	 * @param str
	 * @param beforeTag
	 *            要替換的標籤
	 * @param tagAttrib
	 *            要替換的標籤屬性值
	 * @param startTag
	 *            新標籤開始標記
	 * @param endTag
	 *            新標籤結束標記
	 * @return String
	 * @如：替換img標籤的src屬性值為[img]屬性值[/img]
	 */
	public static String replaceHtmlTag(String str, String beforeTag,
			String tagAttrib, String startTag, String endTag) {
		String regxpForTag = "<\\s*" + beforeTag + "\\s+([^>]*)\\s*>";
		String regxpForTagAttrib = tagAttrib + "=\"([^\"]+)\"";
		Pattern patternForTag = Pattern.compile(regxpForTag);
		Pattern patternForAttrib = Pattern.compile(regxpForTagAttrib);
		Matcher matcherForTag = patternForTag.matcher(str);
		StringBuffer sb = new StringBuffer();
		boolean result = matcherForTag.find();
		while (result) {
			StringBuffer sbreplace = new StringBuffer();
			Matcher matcherForAttrib = patternForAttrib.matcher(matcherForTag
					.group(1));
			if (matcherForAttrib.find()) {
				matcherForAttrib.appendReplacement(sbreplace, startTag
						+ matcherForAttrib.group(1) + endTag);
			}
			matcherForTag.appendReplacement(sb, sbreplace.toString());
			result = matcherForTag.find();
		}
		matcherForTag.appendTail(sb);
		return sb.toString();
	}

	/**
	 * 取得useDeadline對應
	 * 
	 * @param useDeadline
	 *            useDeadline
	 * @param desp1
	 *            desp1
	 * @return 取得useDeadline對應
	 */
	private String getUseDeadline(String useDeadline, String desp1) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		return LMSUtil.getUseDeadline(useDeadline, desp1, prop);
	}

	/**
	 * 取得信評資料
	 * 
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @return 信評資料
	 */
	@SuppressWarnings("unused")
	private String getL120S01CData(List<L120S01C> l120s01cList) {
		String resultData = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		Map<String, String> crdTypeMap = codeService.findByCodeType("CRDType",
				LMSUtil.getLocale().toString());
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			// if (Util.nullToSpace(l120s01a.getCustId()).equals(
			// Util.nullToSpace(l120s01c.getCustId()))
			// && Util.nullToSpace(l120s01a.getDupNo()).equals(
			// Util.nullToSpace(l120s01c.getDupNo()))) {
			String crdType = Util.trim(l120s01c.getCrdType());
			String grade = Util.trim(l120s01c.getGrade());
			tempGrade.setLength(0);
			if ("NA".equals(crdType)) {
				naResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
				// .append(prop.getProperty("L120S05A.GRPGRRDN"))
				// .append("、");
			} else if ("DB".equals(crdType) || "DL".equals(crdType)
					|| "OU".equals(crdType)) {
				if (str3.length() != 0) {
					str3.append("、");
				}
				str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			} else if ("NO".equals(crdType)) {
				noResult = true;
				// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
				// .append(prop.getProperty("L120S01C.NOCRD01"))
				// .append("、");
			} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

				if (Util.isNumeric(grade)) {
					tempGrade.append(grade)
							.append(prop.getProperty("tempGrade")).append(" ");
				}

				// TODO
				// 取得MOW等級之說明
				tempGrade.append(lmsService.getMowGradeName(prop, crdType,
						grade));

				if (str2.length() != 0) {
					str2.append("、");
				}
				str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(tempGrade.toString())
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			} else if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.FitchTW.equals(crdType)
					|| UtilConstants.Casedoc.CrdType.KBRA.equals(crdType)) {
				// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
				if (str1.length() != 0) {
					str1.append("、");
				}
				str1.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(Util.nullToSpace(crdTypeMap.get(l120s01c
								.getCrdType()))).append("】");
			} else if (crdType.startsWith("C") && Util.notEquals(crdType, "CS")) {
				if (str4.length() != 0) {
					str4.append("、");
				}
				str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
						.append(" : ")
						.append(grade)
						.append("【")
						.append(prop.getProperty("L120S01C.CRDTITLE02"))
						.append(Util.nullToSpace(TWNDate.toAD(l120s01c
								.getCrdTYear())))
						.append(" ")
						.append(prop.getProperty("L120S01C.CRDTITLE03"))
						.append(" ")
						.append(l120s01c.getCrdTBR())
						.append(" ")
						.append(Util.nullToSpace(branch.getBranchName(Util
								.nullToSpace(l120s01c.getCrdTBR()))))
						.append("】");
			}
			// }
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部平等一定要串
		boolean result = false;
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "<br/>" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}
		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		resultData = total.toString();
		return resultData;
	}
}
