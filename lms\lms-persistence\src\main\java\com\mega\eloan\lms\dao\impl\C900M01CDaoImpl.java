/* 
 * C900M01CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C900M01CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01C;

/** 產品種類關聯檔 **/
@Repository
public class C900M01CDaoImpl extends LMSJpaDao<C900M01C, String> implements
		C900M01CDao {

	@Override
	public C900M01C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C900M01C findByUniqueKey(String prodKind, String subjCode,
			String type) {
		ISearch search = createSearchTemplete();
		if (prodKind != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodKind",
					prodKind);
		if (subjCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode",
					subjCode);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<C900M01C> findByIndex01(String prodKind, String subjCode,
			String type) {
		ISearch search = createSearchTemplete();
		List<C900M01C> list = null;
		if (prodKind != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "prodKind",
					prodKind);
		if (subjCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode",
					subjCode);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01C> getAll() {
		ISearch search = createSearchTemplete();
		search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}
}