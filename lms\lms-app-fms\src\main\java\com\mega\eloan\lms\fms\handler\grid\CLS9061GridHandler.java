package com.mega.eloan.lms.fms.handler.grid;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.fms.pages.CLS9061V01Page;
import com.mega.eloan.lms.fms.service.CLS9061Service;
import com.mega.eloan.lms.model.C900M01E;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls9061gridhandler")
public class CLS9061GridHandler extends AbstractGridHandler {

	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);

	@Resource
	CLS9061Service service;

	@Resource
	UserInfoService userInfoService;

	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS9061V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String custId = Util.trim(jsoniletData.getString("custId"));
			String custName = Util.trim(jsoniletData.getString("custName"));
			String comId = Util.trim(jsoniletData.getString("comId"));
			String comName = Util.trim(jsoniletData.getString("comName"));
			String comTarget = Util.trim(jsoniletData.getString("comTarget"));
			String sourceNo = Util.trim(jsoniletData.getString("sourceNo"));
			
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"custId", custId);
			}
			if (Util.isNotEmpty(custName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
						"%"+custName+"%");
			}
			if (Util.isNotEmpty(comId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "comId",
						comId);
			}
			if (Util.isNotEmpty(comName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "comName",
						"%"+comName+"%");
			}
			if (Util.isNotEmpty(comTarget)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "comTarget",
						"%"+comTarget+"%");
			}
			if (Util.isNotEmpty(sourceNo)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "sourceNo",
						"%"+sourceNo+"%");
			}
		}
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");

		List<C900M01E> src_list = (List<C900M01E>) service.findPage(
				C900M01E.class, pageSetting).getContent();
		for (C900M01E model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, model, new String[] { "oid", "mainId" });
			row.put(EloanConstants.MAIN_OID, model.getOid());
			row.put(EloanConstants.MAIN_DOC_STATUS, model.getDocStatus());
			row.put("custId", model.getCustId() + " " + model.getDupNo());
			row.put("custName", Util.trim(model.getCustName()));
			row.put("comName", Util.trim(model.getComName()));
			row.put("comTarget", Util.trim(model.getComTarget()));
			row.put("sourceNo", Util.trim(model.getSourceNo()));
			row.put("isClosed",
					model.isClosed() ? prop.get("C900M01E.isClosed.Y") : "");
			row.put("updater", userInfoService.getUserName(model.getUpdater()));
			row.put("updateTime",
					model.getUpdateTime() == null ? "" : S_FORMAT.format(model
							.getUpdateTime()));
			// ---
			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}
