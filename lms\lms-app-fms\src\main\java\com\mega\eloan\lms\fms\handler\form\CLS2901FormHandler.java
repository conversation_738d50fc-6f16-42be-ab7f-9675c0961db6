package com.mega.eloan.lms.fms.handler.form;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.DocOpener;
import com.mega.eloan.common.model.DocOpener.OpenTypeCode;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.eloan.lms.fms.flow.CLS2901Flow;
import com.mega.eloan.lms.fms.pages.CLS2901M01Page;
import com.mega.eloan.lms.model.C900M01J;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("cls2901formhandler")
public class CLS2901FormHandler extends AbstractFormHandler {

	@Resource
	CLSService clsService;
	
	@Resource
	ICustomerService iCustomerService;
	
	@Resource
	UserInfoService userInfoService;

	@Resource 
	FlowSimplifyService flowSimplifyService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	UserInfoService userService;
	
	@Resource
	BranchService branchService;
		
	Properties prop = MessageBundleScriptCreator
		.getComponentResource(CLS2901M01Page.class);

	Properties prop_AbstractEloanPage = MessageBundleScriptCreator
		.getComponentResource(AbstractEloanPage.class);
	
	@DomainAuth(AuthType.Modify)
	public IResult deleteMark(PageParameters params)
			throws CapException {
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String KEY = "saveOkFlag";
		
		String list = params.getString("list");
		String docStatus = params.getString("docStatus");
		
		result.set(KEY, false);
		
		C900M01J meta = null;
		if (Util.isNotEmpty(list)) {
			meta = clsService.findC900M01J_oid(list);	
			
			if(meta!=null){
				
				List<DocOpener> docOpeners = docCheckService.findByMainId(meta.getMainId());
				for(DocOpener docOpener : docOpeners){
					if(OpenTypeCode.Writing.getCode().equals(docOpener.getOpenType())){
						HashMap<String, String> hm = new HashMap<String, String>();
						hm.put("userId", docOpener.getOpener());
						hm.put("userName",
								userInfoService.getUserName(docOpener.getOpener()));
						// 此文件正由 [${userId}-${userName}] 開啟中!<br/>系統將以唯讀狀態開啟此文件。
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0009", hm), getClass());
					}
				}
				
				//判斷編制中刪除，其它 更新DeletedTime
				if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
					flowSimplifyService.flowCancel(meta.getOid());
					clsService.daoDelete(meta);
					// 刪除文件異動記錄
					docLogService.deleteLog(meta.getOid());
				}
				result.set(KEY, true);
			}			
		}
		return defaultResult(params, meta, result);
	}
		
	private CapAjaxFormResult defaultResult(PageParameters params, C900M01J meta,
			CapAjaxFormResult result) throws CapException {		
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, Util.trim(meta.getDocStatus()));
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		UserNameFormatter userNameFormatter;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01J meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900M01J_oid(mainOid);	
			
			LMSUtil.addMetaToResult(result, meta, new String[]{"custId", "dupNo", "custName", "isClosed"
					, "createTime", "updateTime", "category", 
//					"lnflag", 
					"memo", "ownBrId", "dcMemo", "dcUpdateTime", "dcApproveTime", 
					"introduceSrc", "megaEmpNo", "agntNo", "megaCode", "subUnitNo", "subEmpNo", "subEmpNm", "srcMemo", "relPartyId", "relPartyName",
					"introCustId", "introDupNo", "introCustName", "introducerName", "licenseYear", "licenseWord", "licenseNumber"});
			result.set("lnflag", Util.trim(meta.getLnflag())); // C250M01A.lnflag 與 C900M01J.lnflag 在DB是CHAR(2), 但前端的UI只有CHAR(1) => 要 trim , 才能讓 UI 正確呈現
			result.set("creator", Util.trim(userInfoService.getUserName(meta.getCreator())));
			result.set("updater", Util.trim(userInfoService.getUserName(meta.getUpdater())));
			result.set("ownBrIdDesc", meta.getOwnBrId() + branchService.getBranchName(meta.getOwnBrId()));
			result.set("dcUpdater", Util.trim(userInfoService.getUserName(meta.getDcUpdater())));
			result.set("dcApprover", Util.trim(userInfoService.getUserName(meta.getDcApprover())));
			
			String docStatus = meta.getDocStatus();			
			if(Util.equals(FlowDocStatusEnum.編製中.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.010");
			}else if(Util.equals(FlowDocStatusEnum.待覆核.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.020");
			}else if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.030");
			}else if(Util.equals(FlowDocStatusEnum.待解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0C0");
			}else if(Util.equals(FlowDocStatusEnum.已解除.getCode(), docStatus)){
				docStatus = prop_AbstractEloanPage.getProperty("docStatus.0D0");
			}
			result.set("docStatus", docStatus);
			
			
		}else{
			meta = new C900M01J();
			// common 欄位塞值
			meta.setDocStatus(FlowDocStatusEnum.編製中);
			
			//新增,畫面預設值
			result.set("docStatus", prop_AbstractEloanPage.getProperty("docStatus.010"));
			result.set("ownBrId", user.getUnitNo());
			result.set("ownBrIdDesc", user.getUnitNo() + branchService.getBranchName(user.getUnitNo()));
			// 文件異動紀錄區
			userNameFormatter = new UserNameFormatter(userService);
			result.set("creator", userNameFormatter.reformat(user.getUserId()));
			
		}
		
		return defaultResult(params, meta, result);
	}	
	
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		//===
		String KEY = "saveOkFlag";
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C900M01J meta = null;
		
		if(Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900M01J_oid(mainOid);
			String docStatus = Util.trim(meta==null?"":meta.getDocStatus());
			if(Util.equals(FlowDocStatusEnum.已核准.getCode(), docStatus) || Util.equals(FlowDocStatusEnum.待解除.getCode(), docStatus)){
				//not change
			}else{				
				//---
				meta.setUpdater(user.getUserId());
				meta.setUpdateTime(CapDate.getCurrentTimestamp());
				meta.setRandomCode(IDGenerator.getRandomCode());				
			}
		}else{		
			
			Timestamp nowTS = CapDate.getCurrentTimestamp();				
			meta = new C900M01J();			
			//---			
			meta.setMainId(IDGenerator.getUUID());
			meta.setOwnBrId(user.getUnitNo());
			meta.setUnitType(user.getUnitType());		
			meta.setCreator(user.getUserId());
			meta.setCreateTime(nowTS);
			meta.setUpdater(user.getUserId());
			meta.setUpdateTime(nowTS);
			meta.setIsClosed(false);
			meta.setRandomCode(IDGenerator.getRandomCode());		

			if(Util.isEmpty(meta.getDocStatus())){
				meta.setDocStatus(FlowDocStatusEnum.編製中.getCode());
			}
		}		
		//---
		CapBeanUtil.map2Bean(params, meta, new String[] {"custId", "dupNo","custName", "category", "lnflag", "memo", 
				"introduceSrc", "megaEmpNo", "agntNo", "megaCode", "subUnitNo", "subEmpNo", "subEmpNm", "srcMemo"
				, "relPartyId", "relPartyName", "introCustId", "introDupNo", "introCustName", "introducerName", "licenseYear",
				"licenseWord", "licenseNumber"});
		//---
		if(Util.equals("I", meta.getCategory())){
			meta.setIntroduceSrc("");
			//在以下的部分, 去清空欄位
		}
		
		if(Util.equals("1", meta.getIntroduceSrc())){
//			clear_introduceSrc_1(meta);
			clear_introduceSrc_2(meta);
			clear_introduceSrc_3(meta);
			clear_introduceSrc_5(meta);
		}else if(Util.equals("2", meta.getIntroduceSrc())){
			clear_introduceSrc_1(meta);
//			clear_introduceSrc_2(meta);
			clear_introduceSrc_3(meta);
			clear_introduceSrc_5(meta);
		}else if(Util.equals("3", meta.getIntroduceSrc())){
			clear_introduceSrc_1(meta);
			clear_introduceSrc_2(meta);
//			clear_introduceSrc_3(meta);
			clear_introduceSrc_5(meta);
		}else if(Util.equals("5", meta.getIntroduceSrc())){
			clear_introduceSrc_1(meta);
			clear_introduceSrc_2(meta);
			clear_introduceSrc_3(meta);
//			clear_introduceSrc_5(meta);			
		}else{
			clear_introduceSrc_1(meta);
			clear_introduceSrc_2(meta);
			clear_introduceSrc_3(meta);
			clear_introduceSrc_5(meta);
		}
		
		if(true){
			String msg = checkIncompleteMsg(meta);
			if(Util.isNotEmpty(msg)){
				if(allowIncomplete){
					result.set("IncompleteMsg", msg);			
				}else{
					throw new CapMessageException(msg, getClass());	
				}
			}
		}
		clsService.save(meta);
		mainOid = meta.getOid();
		//===========================
		result.set(KEY, true);	
		
		return defaultResult(params, meta, result);
	}
	
	private void clear_introduceSrc_1(C900M01J meta){
		meta.setMegaEmpNo("");
	}
	private void clear_introduceSrc_2(C900M01J meta){
		meta.setAgntNo("");			
	}
	private void clear_introduceSrc_3(C900M01J meta){
		meta.setMegaCode("");
		meta.setSubUnitNo("");
		meta.setSubEmpNo("");
		meta.setSubEmpNm("");
	}
	private void clear_introduceSrc_5(C900M01J meta){ //其它
		meta.setSrcMemo("");
	}
	
	private String checkIncompleteMsg(C900M01J model){
		List<String> list = new ArrayList<String>();
		if(Util.isEmpty(Util.trim(model.getCustId()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.custId")));
		}
		if(Util.isEmpty(Util.trim(model.getCustName()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.custName")));
		}
		if(Util.isEmpty(Util.trim(model.getCategory()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.category")));
		}
		if(Util.isEmpty(Util.trim(model.getIntroduceSrc()))){
			if(Util.equals("P", model.getCategory()) || Util.equals("B", model.getCategory())){ 
				list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.introduceSrc")));
			}
		}
		
		String[] introduceSrc5toA = new String[] {"5", "6", "7", "8"};
		
		/*** 引介來源 ***/
		if(true){
			String introduceSrc = Util.trim(model.getIntroduceSrc());
			if(Util.equals("1", introduceSrc)){
				if(Util.isEmpty(Util.trim(model.getMegaEmpNo()))){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.megaEmpNo")));
				}
			}else if(Util.equals("2", introduceSrc)){
				
				if(Util.isEmpty(Util.trim(model.getAgntNo())) || Util.isEmpty(Util.trim(model.getIntroducerName()))){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.agntNo")));
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.introducerName")));
				}
				
			}else if(Util.equals("3", introduceSrc)){
				
				if(Util.isEmpty(Util.trim(model.getMegaCode()))){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.megaCode")));
				}
				
				if(Util.isEmpty(Util.trim(model.getSubEmpNm()))){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.subEmpNm")));
				}
			}else if(Util.equals("4", introduceSrc)){
				
				if(Util.isEmpty(Util.trim(model.getIntroducerName()))){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.introducerName")));
				}
				
			// 5-往來企金戶所屬員工 or 6-本行客戶引介 or 7-經總處核准之整批貸款 or 8-分行自辦小規模整批貸款
			}else if(Arrays.asList(introduceSrc5toA).contains(introduceSrc)){
				
				if(Util.isEmpty(model.getIntroCustId()) || Util.isEmpty(model.getIntroCustName())){
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.introCustId")));
					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.introCustName")));
				}
				
			}
			
//			else if(Util.equals("6", introduceSrc)){
//				
//			
//			}
//			else if(Util.equals("", introduceSrc)){ //空白
//				
//			}else{ //［5:其它］				
//				if(Util.isEmpty(Util.trim(model.getSrcMemo()))){
//					list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.srcMemo")));
//				}
//			}
		}
		
		if(Util.isEmpty(Util.trim(model.getLnflag()))){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.lnflag")));
		}
		if(Util.isEmpty(Util.trim(model.getMemo())) && Util.equals("D", model.getLnflag())){
			list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.memo")));
		}
		
		if(Util.equals(FlowDocStatusEnum.已核准.getCode(), model.getDocStatus())){
			if(Util.isEmpty(Util.trim(model.getDcMemo()))){
				list.add(MessageFormat.format(prop.getProperty("msg.01"), prop.getProperty("C900M01J.dcMemo")));
			}	
		}
		return StringUtils.join(list, "<br/>"); 
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C900M01J meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900M01J_oid(mainOid);

			
			if(!flowSimplifyService.flowExist(mainOid)){
				flowSimplifyService.flowStart(CLS2901Flow.FLOW_CODE, mainOid, user.getUserId(), user.getUnitNo());
			}		
			
			String errMsg = "";
			if(Util.equals("核定", decisionExpr) ){
				//檢查經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getUpdater())){
					errMsg = RespMsgHelper.getMessage("EFD0053");	
				}			
			}else if(Util.equals("解除核定", decisionExpr)){
				//檢查解除經辦和主管是否為同一人
				if(Util.equals(user.getUserId(), meta.getDcUpdater())){
					errMsg = RespMsgHelper.getMessage("EFD0053");	
				}	
			}
			if(Util.isNotEmpty(errMsg)){
				throw new CapMessageException(errMsg, getClass());
			}
			
			flowSimplifyService.flowNext(meta.getOid(), decisionExpr);
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult inputRemove(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		
		C900M01J meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC900M01J_oid(mainOid);
			if(true){
				CapBeanUtil.map2Bean(params, meta, new String[] {"dcMemo" });	
			}
			clsService.daoSave(meta);
		}
		result.set("dcMemo", meta.getDcMemo());
		return result;
	}
}
