/* 
 * CapByteDownloadResult.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.response;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.reference.DefaultHTTPUtilities;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 資料下載
 * </pre>
 * 
 * @since 2011/11/15
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/11/15,iristu,new
 *          <li>2012/02/20.iristu,add (component,byteArray,contentType)當無outputName時則browser會直接開啟檔案
 *          </ul>
 */
public class CapByteArrayDownloadResult extends CapFileDownloadResult {

    final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 下載檔案的內容
     */
    private byte[] _byteArray = null;

    /**
     * 是否以瀏覽器開啟
     */
    private boolean _isInline = false;

    /**
     * 建構子, 建立下載檔案
     */
    public CapByteArrayDownloadResult() {
    }// ;

    /**
     * <pre>
     * 建構子, 建立下載檔案
     * 若outputName不存在則返回{@code null}
     * </pre>
     * 
     * @param byteArray
     *            下載檔案的內容
     * @param contentType
     *            媒體型式
     * @param outputName
     *            檔案名稱
     */
    public CapByteArrayDownloadResult(byte[] byteArray, String contentType, String outputName) {
        this._byteArray = byteArray;
        this._contentType = contentType;
        this._outputName = CapString.isEmpty(outputName) ? null : outputName;
    }// ;

    /**
     * 建構子, 建立下載檔案
     * 
     * @param byteArray
     *            下載檔案的內容
     * @param contentType
     *            媒體型式
     */
    public CapByteArrayDownloadResult(byte[] byteArray, String contentType) {
        this._byteArray = byteArray;
        this._contentType = contentType;
    }// ;

    public CapByteArrayDownloadResult(byte[] byteArray, String contentType, String outputName, boolean isInline) {
        this._byteArray = byteArray;
        this._contentType = contentType;
        this._outputName = CapString.isEmpty(outputName) ? null : outputName;
        this._isInline = isInline;
    }// ;

    /*
     * 取得檔案下載訊息
     * 
     * @see tw.com.iisi.cap.response.CapFileDownloadResult#getLogMessage()
     */
    @Override
    public String getLogMessage() {
        if (_outputName == null) {
            return _contentType + " byteArrayDownload complete!!";
        } else {
            return new StringBuffer("Download file:").append(_outputName).toString();
        }
    }

    /*
     * 將資訊加入資料內容
     * 
     * @see tw.com.iisi.cap.response.CapFileDownloadResult#add(tw.com.iisi.cap.response.IResult)
     */
    @Override
    public void add(IResult result) {
        if (result instanceof CapByteArrayDownloadResult) {
            CapByteArrayDownloadResult r = (CapByteArrayDownloadResult) result;
            this._contentType = r._contentType;
            this._byteArray = r._byteArray;
            this._outputName = r._outputName;
            this._isInline  = r._isInline;
        }
    }// ;

    /*
     * (non-Javadoc)
     * 
     * @see tw.com.iisi.cap.response.CapFileDownloadResult#respondResult(javax.servlet.ServletResponse)
     */
    @Override
    public void respondResult(ServletResponse response) throws CapException {
        // int length = -1;
        InputStream in = null;
        OutputStream output = null;
        try {
            if (_outputName != null && response instanceof HttpServletResponse) {
                DefaultHTTPUtilities httpUtilities = new DefaultHTTPUtilities();
                HttpServletResponse resp = (HttpServletResponse) response;
                // [refs # 47] HTTP Response Splitting 2200523, 改用ESAPI的 httpUtilities.setHeader
                if (_contentType != null) {
                	httpUtilities.setHeader(resp, "Content-Type", _contentType);
                }
                response.setContentLength(_byteArray.length);
                // [refs # 47] HTTP Response Splitting 2200523, 改用ESAPI.encoder().encodeForURL
                // [refs#171] 由交易決定 CapByteArrayDownloadResult 是否由瀏覽器開啟：attachment代表以下載方式處理、inline代表由瀏覽器開啟
                resp.setHeader("Content-Disposition", (_isInline ? "inline" : "attachment") + ";filename=" + ESAPI.encoder().encodeForURL(_outputName));
                httpUtilities.setHeader(resp, "Cache-Control", "public");
                httpUtilities.setHeader(resp, "Pragma", "public");
            }
            output = response.getOutputStream();
            // Stream to the requester.
            // byte[] bbuf = new byte[1024 * 1024];

            in = new ByteArrayInputStream(_byteArray);
            // while ((in != null) && ((length = in.read(bbuf)) != -1)) {
            // output.write(bbuf, 0, length);
            // }
            IOUtils.copy(in, output);
            // output.flush();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new CapException(e.getMessage(), getClass());
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(output);
        }
    }
}// ~
