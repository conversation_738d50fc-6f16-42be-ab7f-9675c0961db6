s<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S01PanelE');</script>
			<script type="text/javascript">loadScript('pagejs/cls/CLS1220S06Panel01');</script>
            <!--======================================================-->
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.docinfo'}"></th:block>
                </legend>
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr class='align_top'>
                            <td class="hd2 rt" nowrap>
                                <span class="color-red">＊</span>
                                <th:block th:text="#{'C122M01A.ploanCaseNo'}">
                                    案件編號
                                </th:block>
                            </td>
                            <td >
                                <span id="ploanCaseNo" name="ploanCaseNo" ></span>
								<br/>
								<button type="button" id="btnPrintBarcode">
		                            <span class="text-only">產生文件掃描封面條碼</span>
		                        </button>
                            </td>
                            <td class="hd2 rt">
                                <th:block th:text="#{'doc.attchGrid'}">
                                    上傳檔案
                                </th:block>
                                <!--
								<br/>
                                <span class='ploan_attch_downloadZIP' style='color:#5291EF; text-decoration:underline;'>
                                    <th:block th:text="#{'label.ploan_attch_downloadZIP'}">
                                        整批下載
                                    </th:block>
                                </span>
								-->
                                <br/>
                                <button type="button" id="uploadFile">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.uploadFile'}">
                                            <!-- 選擇附加檔案-->
                                        </th:block>
                                    </span>
                                </button>
                                <br/>
                                <button type="button" id="deleteFile">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.deleteFile'}">
                                            <!-- 刪除-->
                                        </th:block>
                                    </span>
                                </button>
								<div id = "uploadFileDiv_temp" class="hide">
									<span class="text-only">文件種類</span><br/>
									<select id="uploadFormId_temp" name="uploadFormId_temp" class="required" codeType="MEGAIMAGE_FormId" ></select><br/>
									<span class="text-only">對應客戶</span><br/>
									<select id="uploadFileRelationship_temp" name="uploadFileRelationship_temp" class="required"></select>
									<input type="hidden" id="formId_temp"></input>
									<input type="hidden" id="stakeholderID_temp"></input>
								</div>
                            </td>
                            <td>
                                <div id='attchGrid' style='margin-left:0px;'>
                                </div>
                            </td>
                        </tr>
						<tr class='align_top'>      	
							<td class="hd2 rt" nowrap>
                            </td>
                            <td >
                            </td>	                         
                            <td class="hd2 rt">                                   
                                <span class="text-only"><th:block th:text="#{'label.megaImage'}">已電子化保存檔案</th:block></span>
		                        <button type="button" id="deleteFileMegaImage">
		                            <span class="text-only"><th:block th:text="#{'button.deleteFile'}"><!-- 刪除--></th:block></span>
		                        </button>
								<br/>
								<!-- 同步檔案清單-->
								<button type="button" id="btnRefreshAttchGrid" class="forview">
		                            <span class="text-only"><th:block th:text="#{'button.btnRefreshAttchGrid'}">同步檔案清單</th:block></span>
		                        </button>
								<!-- 開啟文件數位化系統-->
								<button type="button" id="btnCLSQuery" class="forview">
		                            <span class="text-only"><th:block th:text="#{'button.btnCLSQuery'}">開啟文件數位化系統</th:block></span>
		                        </button>
								<!-- 下載-->
								<div class="hide">
									<button type="button" id="btnRPAQueryCLImage">
			                            <span class="text-only"><th:block th:text="#{'button.btnRPAQueryCLImage'}">下載已電子化保存檔案</th:block></span>
			                        </button>
								</div>
                            </td>
                            <td>
								<div id='mEGAImageGrid' style='margin-left:0px;'>
		   						</div>	
                            </td>						
                        </tr>
						<tr class="ploan_ixml_allowed">
							<td class="hd2 rt">
								<span class="text-only"><th:block th:text="#{'label.iXMLStatus'}">iXML查詢進度</th:block></span>
                       		</td>
                            <td colspan="3">
                            	<div id='iXMLStatusGrid' style='margin-left:0px;'>
			   					</div>
								<th:block th:text="#{'Message.dataNotice'}"><!--財稅資料係為課稅目的的蒐集，僅供金融徵信案件之參考；若有資料內容疑問時，應向資料主管機關洽詢--></th:block>
                        	</td>	
						</tr>	
                        <tr class="ploan_decide_by_main_borrowser">
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'title.caseprocess'}">
                                    <!--案件流程管理-->
                                </th:block>&nbsp;&nbsp;
                                <br/>
                                <button type="button" id="button_caseClosed">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.caseClosed'}">
                                            <!--結案-->
                                        </th:block>
                                    </span>
                                </button>
                            </td>
                            <td width="25%">
                                <span id="docStatusDesc" name="docStatusDesc"></span>
                                <br/>
                                <br/>
                                <span id="message" name="message"></span>
                            </td>
                            <td class="hd2 rt" id="CaseProcess">
                                <button type="button" id="buttonB00">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.stateB00'}">
                                            <!--徵信-->
                                        </th:block>
                                    </span>
                                </button>
                                <button type="button" id="buttonA02">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.stateA02'}">
                                            <!--補件通知-->
                                        </th:block>
                                    </span>
                                </button>
                            </td>
                            <td width="35%">
                                <button type="button" id="deleteDocStatusGrid" style="display:none;">
                                    <span class="text-only">
                                        <th:block th:text="#{'button.delete'}">
                                            <!--刪除-->
                                        </th:block>
                                    </span>
                                </button>
                                <div id='docStatusGrid' style='margin-left:0px;'>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td width="20%" class="hd1">
                                <th:block th:text="#{'doc.branchName'}">
                                    <!--  分行名稱-->
                                </th:block>&nbsp;&nbsp;
                            </td>
                            <td width="25%">
                                <span id="ownBrId" ></span><span id="ownBrName" ></span>
                            </td>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.orgBrId'}"> 原始申貸分行</th:block>
								<span id='changeOrgBrId' class='changeOrgBrId' style='color:#5291EF; text-decoration:underline; display:none;'>
										<th:block th:text="#{'spanbutton.changeOrgBrId'}">變更</th:block>
									</span>
                            </td>
                            <td width="35%">
                                <span id="orgBrId" ></span><span id="orgBrName" ></span>
                            </td>
                        </tr>
                        <tr class='align_top'>
                            <td class="hd1">
                                <th:block th:text="#{'C122M01A.custId'}">
                                    身分證統編 
                                </th:block>&nbsp;
                            </td>
                            <td>
                                <span id="custId" name="custId" ></span>&nbsp;&nbsp;<span id="dupNo" name="dupNo" ></span>
                            </td>
                            <td class="hd1">
                                <span id="custNameLabel" name="custNameLabel"></span>&nbsp;
                            </td>
                            <td>
                                <span id="custName" name="custName"></span>
                                <div class='ploan_relateWithMainBorrower'>
                                    <!--與主借人關係-->
                                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class='hd2'>
                                                <span class="color-red">＊</span>
                                                <th:block th:text="#{'label.ploanRelateCase.custPos'}">
                                                    身分別
                                                </th:block>
                                            </td>
                                            <td class=''>
                                                <select id="ploanCasePos" name="ploanCasePos" class="" codeType="ploan_casePos" ></select>&nbsp;
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class='hd2'>
                                                <th:block th:text="#{'ploanObj.relationWithBorrower'}">
                                                    關係類別
                                                </th:block>
                                            </td>
                                            <td class=''>
                                                <select id="ploan_basicInfo_relationWithBorrower" name="ploan_basicInfo_relationWithBorrower" class="" codeType="Relation_type2" ></select>&nbsp;
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class='hd2' nowrap>
                                                <th:block th:text="#{'ploanObj.liveWithBorrower'}">
                                                    與主借人同住
                                                </th:block>
                                            </td>
                                            <td class=''>
                                                <select id="ploan_basicInfo_liveWithBorrower" name="ploan_basicInfo_liveWithBorrower" class="" codeType="ploan_liveWithBorrower" ></select>&nbsp;
                                            </td>
                                        </tr>
                                        <tr class='align_top'>
                                            <td class='hd2'>
                                                <th:block th:text="#{'ploanObj.guarantyReason'}">
                                                    借保原因 
                                                </th:block>
                                            </td>
                                            <td class=''>
                                                <select id="ploan_basicInfo_guarantyReason" name="ploan_basicInfo_guarantyReason" class="" codeType="cls1161m01_reson" ></select>&nbsp;
                                                <div id='ploan_basicInfo_otherGuarantyReason'>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.applyDateTime'}">
                                    進件日期
                                </th:block>
                            </td>
                            <td>
                                <span id="applyTS" name="applyTS"></span>
                            </td>
                            <td class="hd2 rt" nowrap>
                                <th:block th:text="#{'ploanObj.ipAddr'}">
                                    來源IP
                                </th:block>
                            </td>
                            <td>
                                <span id="applyIPAddr" name="applyIPAddr" ></span>&nbsp;
								<button type="button" id="sameIpImportData">
									<span class="text-only"><th:block th:text="#{'button.sameIpImportData'}"><!--調閱30日內同IP進件資料--></th:block></span>
			                    </button>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.agreeQueryEJTs'}">
                                    同意聯徵時間
                                </th:block>
                            </td>
                            <td>
                                <span id="agreeQueryEJTs" name="agreeQueryEJTs"></span>
                            </td>
                            <td class="hd2 rt" nowrap>
                                <th:block th:text="#{'C122M01A.agreeQueryEJVer'}">
                                    同意事項版本
                                </th:block>
                            </td>
                            <td>
                                <span id="agreeQueryEJVer" name="agreeQueryEJVer"></span>
                            </td>
                        </tr>
                        <tr style='vertical-align:top;'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'ploanObj.avgTransactionAmt'}">
                                    預期月平均交易金額
                                </th:block>
                            </td>
                            <td>
                                <select id="ploan_basicInfo_avgTransactionAmt" name="ploan_basicInfo_avgTransactionAmt" class="" codeType="ploan_avgTransactionAmt" ></select>&nbsp;
                            </td>
                            <td class="hd2 rt" nowrap>
                                <th:block th:text="#{'ploanObj.serviceAssociateData'}">
                                    服務專員
                                </th:block>
                            </td>
                            <td>
                                <span id="ploan_basicInfo_serviceAssociateDeptCode" name="ploan_basicInfo_serviceAssociateDeptCode" ></span>&nbsp; 
                                <br/>
                                <span id="ploan_basicInfo_serviceAssociateCode" name="ploan_basicInfo_serviceAssociateCode" ></span>&nbsp;
                            </td>
                        </tr>
                        <tr style='vertical-align:top;' class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.applyAmt'}">
                                    申請金額
                                </th:block>
                            </td>
                            <td>
                                <span id="applyCurr" name="applyCurr" class='color-red'></span>&nbsp;&nbsp;<span id="applyAmt" name="applyAmt" class='rt color-red'></span>
                                <span class='color-red'>萬</span>
                            </td>
                            <td class="hd2 rt" rowspan='4'>
                                <th:block th:text="#{'label.ploanRelateCase'}">
                                    從債務人
                                </th:block>
								<br/>
								<div id="relManageButtons" class="hide">
									<button type="button" id="addPloanRelateCase">
			                            <span class="text-only"><th:block th:text="#{'button.add'}">新增</th:block></span>
			                        </button>
									<br/>
									<button type="button" id="editPloanRelateCase">
			                            <span class="text-only"><th:block th:text="#{'button.edit'}">修改</th:block></span>
			                        </button>
									<br/>
									<button type="button" id="deletePloanRelateCase">
			                            <span class="text-only"><th:block th:text="#{'button.delete2'}">刪除</th:block></span>
			                        </button>
								</div>
                            </td>
                            <td rowspan='4'>
                                <div id='ploanGridRelateCase' style='margin-left:0px;'>
                                </div>
                            </td>
                        </tr>
                        <tr style='vertical-align:top;' class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.maturity'}">
                                    借款年限
                                </th:block>
                            </td>
                            <td>
                                <span id="maturity" name="maturity" class='color-red'></span><span class='color-red'>
                                    <th:block th:text="#{'label.year'}">
                                        年
                                    </th:block>
                                </span>
                                <span id="maturityM" name="maturityM" class='color-red'></span><span class='color-red'>
                                    <th:block th:text="#{'label.month'}">
                                        月
                                    </th:block>
                                </span>
                            </td>
                        </tr>
                        <tr class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.extYear'}">
                                    寬限期
                                </th:block>
                            </td>
                            <td>
                                <span id="extYear" name="extYear" class='color-red'></span><span class='color-red'>
                                    <th:block th:text="#{'label.year'}">
                                        年
                                    </th:block>
                                </span>
                            </td>
                        </tr>
                        <tr class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'ploanObj.notificationMethod'}">
                                    利息收據通知方式
                                </th:block>
                            </td>
                            <td>
                                <select id="ploan_loanInfo_notificationMethod" name="ploan_loanInfo_notificationMethod" class="" codeType="ploan_notificationMethod" ></select>&nbsp;
                            </td>
                        </tr><!-- 房貸特有欄位_beg -->
                        <tr class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <span class="color-red">＊</span>
                                <th:block th:text="#{'ploanObj.combine_collateralAddrInfo'}"> 擔保品地址</th:block>
                            </td>
                            <td colspan='3'>
                                <span id="ploan_loanInfo_combine_collateralAddrInfo" name="ploan_loanInfo_combine_collateralAddrInfos" ></span>&nbsp;
                            </td>
                        </tr>
                        <tr class='ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <span class="color-red">＊</span>
                                <th:block th:text="#{'ploanObj.mortgageType'}">本次擔保品屬於</th:block>
                            </td>
                            <td>
                                <div>
                                    <input type='radio' id='ploan002_purposeType' name='ploan002_purposeType' value='1' disabled>
                                    <th:block th:text="#{'C122M01A.ploan002_purposeType.1'}">新房貸申請</th:block>
                                    &nbsp;&nbsp;&nbsp;&nbsp;<input type='radio' id='ploan002_purposeType' name='ploan002_purposeType' value='2' disabled>
                                    <th:block th:text="#{'C122M01A.ploan002_purposeType.2'}">既有房貸增貸</th:block>
									&nbsp;&nbsp;&nbsp;&nbsp;<input type='radio' id='ploan002_purposeType' name='ploan002_purposeType' value='3' disabled>
                                    <th:block th:text="#{'C122M01A.ploan002_purposeType.3'}">其他</th:block>
                                </div>
                                <select id="ploan_loanInfo_mortgageType" name="ploan_loanInfo_mortgageType" class="" codeType="ploan_loanInfo_mortgageType" ></select>&nbsp;&nbsp;<span id="ploan_loanInfo_nonPrivateUsageType" name="ploan_loanInfo_nonPrivateUsageType" ></span><span id="ploan_loanInfo_privateUsageType" name="ploan_loanInfo_privateUsageType" ></span>
                            </td>
							 <td class="hd2 rt">
                                <th:block th:text="#{'C122M01A.purchaseHouse'}"><!--是否為購屋--></th:block>
                            </td>
                            <td>
                                <div>
                                    <input type='radio' id='purchaseHouse' name='purchaseHouse' value='N'><th:block th:text="#{'C122M01A.No'}"><!--否--></th:block> 
							<input type='radio' id='purchaseHouse' name='purchaseHouse' value='Y' checked="true"><th:block th:text="#{'C122M01A.Yes'}"><!--是--></th:block>
                                </div>
                            </td>
                        </tr><!-- 房貸特有欄位_end -->
						
						
						
						
						
                        <tr class='align_top ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'label.ploanObj.relate'}">
                                    同一關係人資料
                                </th:block>
                            </td>
                            <td colspan='3'>
                                <div id='ploanGridRelationData' style='margin-left:7px;'>
                                </div>
                                <div id='ploanGridServedData' style='margin:12px 0 0 7px ;'>
                                </div>
                            </td>
                        </tr>
                        <tr class='align_top ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'label.loanBrNo'}">
                                    目前貸款往來分行
                                </th:block>
                            </td>
                            <td>
                                <span id='loanBrNo' ></span>&nbsp;
                            </td>
                            <td class="hd2 rt">
                                <th:block th:text="#{'label.payrollTransfersBrNo'}">
                                    目前薪轉往來分行
                                </th:block>
                            </td>
                            <td>
                                <span id='payrollTransfersBrNo' ></span>&nbsp;
                            </td>
                        </tr>
                        <tr class='align_top ploan_decide_by_main_borrowser'>
                            <td class='hd2' nowrap>
                                <th:block th:text="#{'C122M01A.statFlag'}">
                                    申貸案件狀態
                                </th:block>
                            </td>
                            <td colspan='5'>
                                <table class='tb2'>
                                    <tr class='align_top '>
                                        <td class='noborder'>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='0' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.0'}">
                                                    受理中
                                                </th:block>
                                            </label>
                                            &nbsp;&nbsp;
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='1' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.1'}">
                                                    審核中
                                                </th:block>
                                            </label>
                                            &nbsp;&nbsp;
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='5' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.5'}">
                                                    待補件
                                                </th:block>
                                            </label>
                                            &nbsp;&nbsp;
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='2' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.2'}">
                                                    已核貸
                                                </th:block>
                                            </label>
                                            &nbsp;&nbsp;
                                        </td>
                                        <td class='noborder'>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='A' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.A'}">
                                                    不承做-票債信不良
                                                </th:block>
                                            </label>
                                            <br/>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='E' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.E'}">
                                                    不承做-申請信用評等未達標準
                                                </th:block>
                                            </label>
                                            <br/>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='D' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.D'}">
                                                    不承做-客戶撤件
                                                </th:block>
                                            </label>
                                        </td>
                                        <td class='noborder'>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='X' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.X'}">
                                                    已作廢
                                                </th:block>
                                            </label>
                                        </td>
                                        <td class='noborder'>
                                            <label style="letter-spacing:0px;cursor:pointer;">
                                                <input type="radio" id="statFlag" name="statFlag" value='6' disabled>
                                                <th:block th:text="#{'C122M01A.statFlag.applyKindE.6'}">
                                                    動審表已覆核
                                                </th:block>
                                            </label>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr class='align_top ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'label.notifyMemo'}">
                                    備註
                                </th:block>
                            </td>
                            <td colspan='3'>
                                <textarea name="notifyMemo" id="notifyMemo" maxlengthC='90' class="txt_mult" style="width:760px;height:60px;">
                                </textarea>
                            </td>
                        </tr>
                        <tr class='align_top ploan_decide_by_main_borrowser'>
                            <td class="hd2 rt">
                                <th:block th:text="#{'label.gridCntrInfo'}">
                                    額度資料
                                </th:block>
                            </td>
                            <td colspan='3'>
                                <div id='gridCntrInfo'>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>
            <input type="hidden" class="hidden" id="queryReasonIsRecorded" name="queryReasonIsRecorded"/><!--=======================-->
            <fieldset>
                <legend>
                    <th:block th:text="#{'doc.docUpdateLog'}">
                        文件異動紀錄
                    </th:block>
                </legend>
                <div class="funcContainer">
                    <div id="_docLog" class="forview" th:insert="~{common/panels/DocLogPanel :: DocLogPanel}"></div>
                </div>
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'doc.creator'}">
                                    文件建立者
                                </th:block>
                            </td>
                            <td width="30%">
                                <span id="creator"></span>&nbsp;(<span id="createTime"></span>)
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}">
                                    最後異動者
                                </th:block>
                            </td>
                            <td>
                                <span id="updater"></span>&nbsp;(<span id="updateTime"></span>)
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset><!--=======================-->
            <fieldset class='ploan_decide_by_main_borrowser'>
                <legend>
                    <th:block th:text="#{'label.changeBrNo'}">
                        改分派異動記錄
                    </th:block>
                </legend>
                <div>
                    <div id='gridC122M01C' style='margin-left:0px;'>
                    </div>
                </div>
            </fieldset>
            <!-- 結案、產生個金徵信資料 引入CLS1220S06Panel01.html-->
            <div th:insert="~{cls/panels/CLS1220S06Panel01 :: CLS1220S06Panel01}" class="clear" style="display:none;" id="_iPanelS"></div>
			<!-- 線下從債務人維護 -->
			<div id="ploanRelateCaseDiv" class="content" style="display:none">
				<form id='ploanRelateCaseDialogForm'>
					<span class="color-red" id="mTypeC">＊</span>
					<br/>
					<input type="text" class="hide" id="rel_mainId" name="rel_mainId"/>
					<input type="text" class="hide" id="rel_remainId" name="rel_remainId"/>
					<input type="text" class="hide" id="rel_flowId" name="rel_flowId"/>
					<table id="relDataTable" width="100%" class="tb2">
						<tr>
							<td class="hd2">
		                        <th:block th:text="#{'C122M01A.custId'}">身分證統編 </th:block>&nbsp;
		                    </td>
		                    <td>
								<input type="text" id="rel_custId" name="rel_custId" size="13" maxlength="10" class="required alphanum" readonly="readonly"/>
								<th:block th:text="#{'C122M01A.dupNo'}">重覆序號：</th:block>
		                        <input type="text" id="rel_dupNo" name="rel_dupNo" size="2" maxlength="1" class="alphanum" readonly="readonly"/>
								<br/>
								<button type="button" id="getCustName" >
		                			<span class="text-only"><th:block th:text="#{'button.importCust'}">引進客戶</th:block></span>
		            			</button>
		                    </td>
						</tr>
						<tr>
							<td class="hd2">
		                        <th:block th:text="#{'ploanObj.relationData.relationName'}">姓名 </th:block>&nbsp;
		                    </td>
		                    <td>
		                    	<u style="cursor:pointer;">
									<span id="rel_custName" name="rel_custName" class="field required"></span>
								</u>
		                    </td>
						</tr>
						<tr>
							<td class='hd2'>
								<span class="color-red">＊</span>
								<th:block th:text="#{'label.ploanRelateCase.custPos'}">身分別</th:block>
					 		</td>
		                    <td>
		                        <select id="rel_ploanCasePosCode" name="rel_ploanCasePosCode" class="required" codeType="ploan_casePos" ></select>
		                    </td>
						</tr>
					</table>
					<span class="color-red">＊從債務人存在文件，刪除從債務人檔案會同時刪除</span>
					<div id='relDocFileGrid' style='margin-left:0px;'>
					</div>
				</form>
			</div>
        </th:block>
    </body>
</html>
