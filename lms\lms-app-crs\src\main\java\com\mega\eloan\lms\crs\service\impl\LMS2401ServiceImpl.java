package com.mega.eloan.lms.crs.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.CrsVO;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.common.CrsRuleVO;
import com.mega.eloan.lms.crs.service.LMS2401Service;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C240M01BDao;
import com.mega.eloan.lms.dao.C240M01CDao;
import com.mega.eloan.lms.dao.C240M01ZDao;
import com.mega.eloan.lms.dao.C241A01ADao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.dao.C241M01FDao;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.ELF459;
import com.mega.eloan.lms.mfaloan.bean.ELF486;
import com.mega.eloan.lms.mfaloan.bean.ELF490;
import com.mega.eloan.lms.mfaloan.bean.ELF490B;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.bean.ELF491B;
import com.mega.eloan.lms.mfaloan.bean.ELF491C;
import com.mega.eloan.lms.mfaloan.bean.ELF492;
import com.mega.eloan.lms.mfaloan.bean.ELF498;
import com.mega.eloan.lms.mfaloan.bean.ELF498B;
import com.mega.eloan.lms.mfaloan.bean.LNF07A;
import com.mega.eloan.lms.mfaloan.bean.LNF13E;
import com.mega.eloan.lms.mfaloan.bean.MISLN20;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.LNLNF07AService;
import com.mega.eloan.lms.mfaloan.service.MisDailyctlService;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisELF459Service;
import com.mega.eloan.lms.mfaloan.service.MisELF486Service;
import com.mega.eloan.lms.mfaloan.service.MisELF487Service;
import com.mega.eloan.lms.mfaloan.service.MisELF488Service;
import com.mega.eloan.lms.mfaloan.service.MisELF489Service;
import com.mega.eloan.lms.mfaloan.service.MisELF490BService;
import com.mega.eloan.lms.mfaloan.service.MisELF490Service;
import com.mega.eloan.lms.mfaloan.service.MisELF491BService;
import com.mega.eloan.lms.mfaloan.service.MisELF491CService;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;
import com.mega.eloan.lms.mfaloan.service.MisELF492Service;
import com.mega.eloan.lms.mfaloan.service.MisELF498Service;
import com.mega.eloan.lms.mfaloan.service.MisELF591Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF040Service;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisPTEAMAPPService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240A01A;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01B;
import com.mega.eloan.lms.model.C240M01C;
import com.mega.eloan.lms.model.C240M01Z;
import com.mega.eloan.lms.model.C241A01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.eloan.lms.model.C241M01F;
import com.mega.eloan.lms.model.C241M01Z;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service("LMS2401Service")
public class LMS2401ServiceImpl extends AbstractCapService implements
		LMS2401Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS2401ServiceImpl.class);

	@Resource
	BranchService branchService;

	@Resource
	DocLogService docLogService;

	@Resource
	CLSService clsService;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	RetrialService retrialService;

	@Resource
	C241A01ADao c241a01adao;

	@Resource
	C241M01EDao c241m01eDao;

	@Resource
	C240M01ADao c240m01aDao;

	@Resource
	C240M01BDao c240m01bDao;

	@Resource
	C240M01CDao c240m01cDao;

	@Resource
	C240M01ZDao c240m01zDao;

	@Resource
	C241M01ADao c241m01aDao;

	@Resource
	C241M01BDao c241m01bDao;

	@Resource
	C241M01CDao c241m01cDao;

	@Resource
	C241M01FDao c241m01fDao;

	@Resource
	C241A01ADao c241a01aDao;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	MisLNF030Service misLNF030Service;

	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;

	@Resource
	MisELF447Service misELF447Service;

	@Resource
	MisELF459Service misELF459Service;

	@Resource
	MisELF486Service misELF486Service;

	@Resource
	MisELF487Service misELF487Service;

	@Resource
	MisELF488Service misELF488Service;

	@Resource
	MisELF489Service misELF489Service;

	@Resource
	MisELF490Service misELF490Service;

	@Resource
	MisELF490BService misELF490bService;

	@Resource
	MisELF491Service misELF491Service;

	@Resource
	MisELF491BService misELF491BService;

	@Resource
	MisELF491CService misELF491CService;

	@Resource
	MisELF492Service misELF492Service;

	@Resource
	MisELF498Service misELF498Service;

	@Resource
	MisELF591Service misELF591Service;

	@Resource
	MisPTEAMAPPService misPTEAMAPPService;

	@Resource
	MisdbBASEService misdbBaseService;

	@Resource
	MisMISLN20Service misMISLN20Service;

	@Resource
	MisLNF040Service misLNF040Service;

	@Resource
	MisDailyctlService misDailyctlService;

	@Resource
	MisLnunIdService misLnunIdService;

	@Resource
	LNLNF07AService lnLnf07aService;

	private void _debug(String s) {

	}

	@Override
	public boolean produce(List<String> instIdList, String branch,
			String dateYM, String userId, String unitNo, String unitType,
			String latest__aprdcdate) throws CapException {

		CrsVO crsVO = new CrsVO();
		Date dataEndDate = CapDate.parseDate(CrsUtil.getDataEndDate(dateYM));

		// 過濾不符合 Rule1, Rule2 的資料
		gfnRecheckReViewData_unMatch_LN_R6_2_R1R2(branch, dataEndDate,
				latest__aprdcdate);

		// 99 類別→重歸類到 R1,R2,...R9
		gfnRecheckReViewData99(branch, dataEndDate);

		// 覆審工作底稿主檔
		C240M01A c240m01a = new C240M01A();
		c240m01a.setMainId(IDGenerator.getUUID());
		c240m01a.setTypCd(TypCdEnum.DBU.getCode());
		c240m01a.setUnitType(unitType);
		c240m01a.setOwnBrId(unitNo);
		c240m01a.setCustId("");
		c240m01a.setCustName("");
		c240m01a.setBranchId(branch);
		c240m01a.setDataEndDate(dataEndDate);

		// 若在 2013/12/01 產生 ~2014/03 的覆審名單
		// 抓的 YearOfReview 為 2013-1 = 2012
		// (因為在產生的時間點, 2013 年還沒過完
		// 還不確定 8_1【非團體消貸，授信額度達TWD300萬以下】 在 2013 年一整年的總筆數
		// 所以只能抓 2012 年的資料

		c240m01a.setYearOfReview(String.valueOf(Integer.valueOf(TWNDate.toAD(
				CapDate.getCurrentTimestamp()).substring(0, 4)) - 1));
		c240m01a.setCreator(userId);
		c240m01a.setCreateTime(CapDate.getCurrentTimestamp());
		c240m01a.setUpdater(userId);
		c240m01a.setUpdateTime(c240m01a.getCreateTime());

		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		{// 組合出 c240m01b, c241m01a
			HashMap<String, List<Map<String, Object>>> grpCntrNoMap = new HashMap<String, List<Map<String, Object>>>();
			// ===
			String c240m01a_branchId = c240m01a.getBranchId();
			String c240m01a_stdCurr = this.branchService.getBranch(
					c240m01a_branchId).getUseSWFT();
			String c240m01a_mainId = c240m01a.getMainId();
			// ===
			List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
			produce_docKindN(crsVO, c240m01a_branchId, c240m01a_mainId,
					c241m01a_list, userId, dataEndDate);
			_debug("@produce_step[1], c241m01a_list.size()="
					+ c241m01a_list.size());
			produce_docKindG(c240m01a_branchId, c240m01a_stdCurr,
					c240m01a_mainId, c241m01a_list, dataEndDate, grpCntrNoMap);
			_debug("@produce_step[2], c241m01a_list.size()="
					+ c241m01a_list.size() + ", grpCntrNoMap.keySet()="
					+ grpCntrNoMap.keySet());
			produce_docKindG_dtl(c240m01a_branchId, c240m01a_mainId,
					c241m01a_list, grpCntrNoMap);
			_debug("@produce_step[3], c241m01a_list.size()="
					+ c241m01a_list.size());
			// ===
			produce_commonByC241M01A("@produce_step[4]", crsVO, c241m01a_list,
					c241m01as, c240m01a_branchId, c240m01a_stdCurr,
					c240m01a_mainId, c240m01a.getDocStatus(),
					c240m01a.getExpectedRetrialDate(), c240m01bs, c241a01as,
					c241m01bs, delC241M01BList, c241m01cs, userId, unitNo,
					unitType);
		}

		// 授權檔
		C240A01A c240a01a = new C240A01A();
		c240a01a.setMainId(c240m01a.getMainId());
		c240a01a.setOwnUnit(unitNo);
		c240a01a.setOwner(userId);
		c240a01a.setAuthType("1");
		c240a01a.setAuthUnit(unitNo);

		if (Util.notEquals(branch, unitNo)) {
			C240A01A c240a01a2 = new C240A01A();
			c240a01a2.setMainId(c240m01a.getMainId());
			c240a01a2.setOwnUnit(unitNo);
			c240a01a2.setOwner(userId);
			c240a01a2.setAuthType("4");
			c240a01a2.setAuthUnit(branch);
			retrialService.save(c240a01a2);
		}

		ELF491 elf491CustIdXXXX_8_1 = misELF491Service
				.selWithCustIdXXXX_8_1(branch);
		if (elf491CustIdXXXX_8_1 != null) {
			String remomo = Util.trim(elf491CustIdXXXX_8_1.getElf491_remomo());

			int plotOfReview = CrsUtil.getR8_1_total(remomo);
			int samplingCount = CrsUtil.getR8_1_already(remomo);

			c240m01a.setPlotsOfReview(plotOfReview);
			c240m01a.setSamplingCount(samplingCount);
			if (samplingCount > 0 && plotOfReview > 0) {
				// 先乘100再除分母, 不然會算出0
				String sRate = CapMath.divide(
						CapMath.multiply(String.valueOf(samplingCount), "100"),
						String.valueOf(plotOfReview));
				c240m01a.setSamplingRate(Util.parseInt(sRate));
			} else {
				c240m01a.setSamplingRate(0);
			}
		} else {
			c240m01a.setPlotsOfReview(0);
			c240m01a.setSamplingCount(0);
			c240m01a.setSamplingRate(0);
		}
		c240m01a.setThisSamplingRate(0);// 抽樣比率, default:0

		c240m01a.setEffectiveCount(misELF491Service
				.selC240M01A_EffectiveCount(branch));

		List<C240M01C> c240m01c_list = new ArrayList<C240M01C>();
		if (true) {
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			if (true) { // clsService.is_function_on_codetype("J-109-0372_ON_R95_1")){
				Date elf491c_latestDate_Base95_1 = misELF491CService
						.findLatest_lrDate(CrsUtil.DONE_95_1_BR,
								CrsUtil.DONE_95_1_CUSTID,
								CrsUtil.DONE_95_1_DUPNO,
								CrsUtil.DONE_95_1_RULE_NO);
				if (CrsUtil
						.isNOT_null_and_NOTZeroDate(elf491c_latestDate_Base95_1)) {
					int rule95_allCnt = misELF491CService
							.countRule95_all(branch);
					int rule95_1_baseCnt = misELF491CService
							.countByBrNoRuleNoLrDateBegEnd(branch,
									CrsUtil.LIST_BASE_95_1_RULE_NO,
									TWNDate.toAD(elf491c_latestDate_Base95_1),
									TWNDate.toAD(elf491c_latestDate_Base95_1));
					if (true) {
						C240M01C c240m01c = rule95_1_baseCnt(
								CrsUtil.LIST_BASE_95_1_RULE_NO, branch,
								rule95_allCnt, elf491c_latestDate_Base95_1,
								rule95_1_baseCnt); // 把中心
													// elf491c_rule_no='LS_95'
													// 的筆數加總，做為{基準}
						c240m01c.setMainId(c240m01a.getMainId());
						c240m01c.setCreator(userId);
						c240m01c.setCreateTime(nowTS);
						c240m01c_list.add(c240m01c);
					}
					if (true) {
						String[] donePeriod_arr = CrsUtil
								.get_R95_1_done_period(TWNDate.toAD(nowTS));
						C240M01C c240m01c = rule95_1_summary(CrsUtil.R95_1,
								branch, rule95_allCnt, rule95_1_baseCnt,
								donePeriod_arr);
						c240m01c.setMainId(c240m01a.getMainId());
						c240m01c.setCreator(userId);
						c240m01c.setCreateTime(nowTS);
						// ~~~
						c240m01c_list.add(c240m01c);
					}
				} else {
					// 尚未生效(自2021-07-01 後，才應生效)
				}
			}

			boolean useRuleV20200522 = clsService
					.is_function_on_codetype("J-109-0213_V2020_05_22");
			// 若在 2020-12-31 前產出覆審名單，會缺2019年底的不動產十足擔保總件數 => 指定在 2020-01 後才適用
			if (useRuleV20200522) {
				C240M01C c240m01c = rule_R1R2S(CrsUtil.R1R2S, branch, nowTS);
				c240m01c.setMainId(c240m01a.getMainId());
				c240m01c.setCreator(userId);
				c240m01c.setCreateTime(nowTS);
				// ~~~
				c240m01c_list.add(c240m01c);
			}
			if (useRuleV20200522) {
				C240M01C c240m01c = rule_projectCreditLoan_summary(CrsUtil.R13,
						branch, nowTS);
				c240m01c.setMainId(c240m01a.getMainId());
				c240m01c.setCreator(userId);
				c240m01c.setCreateTime(nowTS);
				// ~~~
				c240m01c_list.add(c240m01c);
			}

			boolean useRuleV20230118 = clsService
					.is_function_on_codetype("J-111-0622_V2023_01_18");
			// 若在 2022-12-31 前產出覆審名單，會缺2022年底的單一授信額度在新臺幣一千萬元以下總件數 => 指定在 2023-01
			// 後才適用
			if (useRuleV20230118) {
				C240M01C c240m01c = rule_R14(CrsUtil.R14, branch, nowTS);
				c240m01c.setMainId(c240m01a.getMainId());
				c240m01c.setCreator(userId);
				c240m01c.setCreateTime(nowTS);
				// ~~~
				c240m01c_list.add(c240m01c);
			}
		}
		// ---
		_debug("@produce_step[5]" + ", c241m01as.size()=" + c241m01as
				+ ", c240m01bs.size()=" + c240m01bs + ", c241a01as.size()="
				+ c241a01as + ", c241m01bs.size()=" + c241m01bs
				+ ", c241m01cs.size()=" + c241m01cs);
		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c240m01cDao.save(c240m01c_list);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		c240m01a_reQuantity_thisSamplingCount(c240m01a);
		retrialService.save(c240m01a, c240a01a);

		// ---
		instIdList.add(c240m01a.getOid());

		return true;
	}

	private void produce_docKindN(CrsVO crsVO, String c240m01a_branchId,
			String c240m01a_mainId, List<C241M01A> c241m01a_list,
			String userId, Date dataEndDate) throws CapException {
		Map<String, String> nckdFlagMap = retrialService.get_crs_NckdFlagMap();
		HashSet<String> existSet = new HashSet<String>();

		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
		String elf491_newflag = "Y";
		if (clsService
				.is_function_on_codetype("c240m01a_fetch_elf491_newflag_Y")) {

		} else {
			elf491_newflag = "Z"; // 故意指定不存在的 elf491_newflag，讓SQL不撈出
									// elf491_newflag='Y'
		}
		retrialService.fetchELF491_LNF020_030_040(crsVO, c240m01a_branchId,
				dataEndDate, elf491_newflag);
		for (Map<String, Object> dataMap : crsVO.listSingle()) {
			String custId = Util.trim(dataMap.get("ELF491_CUSTID"));
			String custName = Util.trim(dataMap.get("CNAME"));
			String dupNo = Util.trim(dataMap.get("ELF491_DUPNO"));

			Date crDate = CapDate.parseDate(Util.trim(dataMap
					.get("ELF491_CRDATE")));
			Date lrDate = CapDate.parseDate(Util.trim(dataMap
					.get("ELF491_LRDATE")));
			Date lastRealDt = CapDate.parseDate(Util.trim(dataMap
					.get("ELF491_LASTREALDT")));
			// 目前只處理 crDate > 0001-01-01
			if (CrsUtil.isNull_or_ZeroDate(crDate)) {
				continue;
			}

			if (CrsUtil.isNckdFlag_EMPTY_A(Util.trim(dataMap
					.get("ELF491_NCKDFLAG")))) {
				// 只需產生「不覆審代碼(NCKDFLAG)」為 null 或空白或'A'(改期覆審)案件，其餘不出現在覆審工作底稿
			} else {
				continue;
			}

			// 如果該戶為聯貸戶母戶或不是該子戶不是本分行則換下一筆
			if (true) {
				List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
						.filter_crsVO_getLNF020(crsVO.getLNF020(custId, dupNo),
								c240m01a_branchId);

				if (CollectionUtils.isEmpty(_filter_crsVO_getLNF020)) {
					continue;
				}
			}

			if (existSet.contains(custId + dupNo)) {
				continue;
			} else {
				existSet.add(custId + dupNo);
			}

			// 明細
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(custName);
			c241m01a.setLastRetrialDate(CrsUtil
					.isNOT_null_and_NOTZeroDate(lrDate) ? lrDate : null);
			c241m01a.setLastRealDt(CrsUtil
					.isNOT_null_and_NOTZeroDate(lastRealDt) ? lastRealDt : null);
			c241m01a.setSpecifyCycle(Util.trim(dataMap.get("ELF491_MAINCUST")));
			c241m01a.setSpecifyCycle(null);// 每次決定

			String remomo = Util.trim(dataMap.get("ELF491_REMOMO"));

			CrsUtil.setR98Data(c241m01a, remomo,
					CapDate.parseDate(Util.trim(dataMap.get("ELF491_UCKDDT"))),
					Util.trim(dataMap.get("ELF491_UCKDLINE")));

			// 計算下次覆審日
			c241m01a.setShouldReviewDate(crDate);

			// 規則為空塞99
			if (Util.isEmpty(remomo)) {
				remomo = CrsUtil.R99;
			}

			if (LMSUtil.cmpDate(crDate, "<=", dataEndDate)) {// 在覆審期間內
				// gfnRecheckReViewData_unMatchR1R2已把本來符合 Rule1,2 但現在不符合的資料
				// CR_DATE 更新成 0001-01-01
				// 所以在此不重複判斷
				c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			} else {
				if (Util.equals(UtilConstants.DEFAULT.是,
						Util.trim(dataMap.get("ELF491_NEWFLAG")))) {
					// 晚於覆審期間且新案.要出現並設成A-改期覆審
					Timestamp nowTimestamp = CapDate.getCurrentTimestamp();

					c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_N);

					// gfnDB2AutoCancelNewData("A-改期覆審");
					c241m01a.setNCkdFlag(CrsUtil.NCKDFLAG_A);
					c241m01a.setNCkdDate(nowTimestamp);
					c241m01a.setNCkdMemo(nckdFlagMap.get(Util.trim(c241m01a
							.getNCkdFlag())));

					// 'gfnDB2AutoCancelNewData
					// '自動將覆審資料日期外之新案資料cancel掉 並註記為- - - - A-改期覆審。
					ELF491 elf491 = misELF491Service.findByPk(
							c240m01a_branchId, c241m01a.getCustId(),
							c241m01a.getDupNo());
					if (elf491 != null) {
						retrialService.up491_at_491toc241m01a_nckdFlagA(
								elf491_list, c241m01z_list, elf491,
								c241m01a.getShouldReviewDate(),
								c241m01a.getNCkdFlag(), c241m01a.getNCkdDate(),
								c241m01a.getNCkdMemo(), userId);
					}
				} else {
					continue;
				}
			}

			// J-110-0272
			if (!Util.equals(remomo, CrsUtil.R99)
					&& is_elf489_contains_8_1(c240m01a_branchId, custId, dupNo)) {
				TreeMap<String, String> retrialKind_map = CrsUtil
						.parseRule(remomo);
				retrialKind_map.put(CrsUtil.R8_1, "");
				remomo = CrsUtil.combineRule(retrialKind_map); // 自動抽樣 8-1
			}
			c241m01a.setRetrialKind(remomo);

			_newCase_491toc241m01a(Util.trim(dataMap.get("ELF491_REPORTKIND")),
					c241m01a);
			c241m01a.setDocKind(CrsUtil.DOCKIND_N);
			c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);
			c241m01a_list.add(c241m01a);
		}

		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);
	}

	private void _newCase_491toc241m01a(String elf491_reportKind,
			C241M01A c241m01a) {

		if (StringUtils.isBlank(elf491_reportKind)) {
			// 當99時,可能reportKind 為 null
			c241m01a.setNewCase("N");// 當成舊案
		} else {
			if ("N".equals(elf491_reportKind)) {
				c241m01a.setNewCase("Y");
			} else if ("O".equals(elf491_reportKind)) {
				c241m01a.setNewCase("N");
			} else {
				c241m01a.setNewCase(Util.trim(elf491_reportKind));
			}
		}
	}

	private void produce_docKindG(String c240m01a_branchId,
			String c240m01a_stdCurr, String c240m01a_mainId,
			List<C241M01A> c241m01a_list, Date dataEndDate,
			HashMap<String, List<Map<String, Object>>> grpCntrNoMap) {
		for (Map<String, Object> map : misPTEAMAPPService.selCrsGroupData(
				c240m01a_branchId, dataEndDate)) {
			String custId = Util.trim(map.get("CUSTID"));
			String grpCntrNo = Util.trim(map.get("GRPCNTRNO"));
			String cName = Util.trim(map.get("CNAME"));
			String dupNo = Util.trim(map.get("DUPNO"));
			logger.trace("produce_docKindG[" + grpCntrNo + "]" + custId + " "
					+ dupNo + " " + cName);
			// gfnDB2GetGrpReviewData
			List<ELF492> elf492List = misELF492Service.selByBrNoCntrNo(
					c240m01a_branchId, grpCntrNo);
			if (elf492List.size() > 0) {
				continue;
			} else {
				grpCntrNoMap.put(grpCntrNo, misPTEAMAPPService
						.selCrsGroupDetail_orderByLoanBalDesc(
								c240m01a_branchId, grpCntrNo));
			}
			String subCompNm = Util.trim(map.get("SUBCOMPNM"));
			String projectNm = Util.trim(map.get("PROJECTNM"));
			Date effEnd = CapDate.parseDate(Util.trim(map.get("EFFEND")));

			// 團貸資料
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);

			if (Util.isNotEmpty(subCompNm)) {
				c241m01a.setCustName(subCompNm);
			} else {
				if (Util.isNotEmpty(projectNm)) {
					c241m01a.setCustName(projectNm);
				} else {
					c241m01a.setCustName(cName);
				}
			}

			c241m01a.setGrpCntrNo(grpCntrNo);
			c241m01a.setGrpCount(grpCntrNoMap.get(grpCntrNo).size());
			c241m01a.setGrpEnd(effEnd);

			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);

			c241m01a.setRetrialKind(CrsUtil.R6_1);
			c241m01a.setNewCase("G");
			c241m01a.setDocKind(CrsUtil.DOCKIND_G);

			c241m01a_list.add(c241m01a);
		}
	}

	private void produce_docKindG_dtl(String c240m01a_branchId,
			String c240m01a_mainId, List<C241M01A> c241m01a_list,
			HashMap<String, List<Map<String, Object>>> grpCntrNoMap)
			throws CapException {
		for (String grpCntrNo : grpCntrNoMap.keySet()) {
			for (Map<String, Object> map : top5_inList(grpCntrNoMap
					.get(grpCntrNo))) {

				String custId = Util.trim(Util.getLeftStr(
						Util.trim(map.get("LNF020_CUST_ID")), 10));
				String dupNo = Util.getRightStr(
						Util.trim(map.get("LNF020_CUST_ID")), 1);
				String custName = Util.trim(map.get("CNAME"));

				// 明細
				C241M01A c241m01a = _build_grpDetail(c240m01a_branchId,
						grpCntrNo, custId, dupNo, custName);

				c241m01a_list.add(c241m01a);
			}
		}
	}

	private C241M01A _build_grpDetail(String c240m01a_branchId,
			String grpCntrNo, String custId, String dupNo, String custName) {
		C241M01A c241m01a = new C241M01A();
		c241m01a.setMainId(IDGenerator.getUUID());
		c241m01a.setCustId(custId);
		c241m01a.setDupNo(dupNo);
		c241m01a.setCustName(custName);
		c241m01a.setGrpCntrNo(grpCntrNo);

		c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);

		ELF491 elf491 = misELF491Service.findByPk(c240m01a_branchId,
				c241m01a.getCustId(), c241m01a.getDupNo());
		if (elf491 != null) {
			Date crDate = elf491.getElf491_crdate();
			Date lrDate = elf491.getElf491_lrdate();
			Date lastRealDt = elf491.getElf491_lastRealDt();
			c241m01a.setLastRetrialDate(CrsUtil
					.isNOT_null_and_NOTZeroDate(lrDate) ? lrDate : null);
			c241m01a.setLastRealDt(CrsUtil
					.isNOT_null_and_NOTZeroDate(lastRealDt) ? lastRealDt : null);
			c241m01a.setSpecifyCycle(Util.trim(elf491.getElf491_maincust()));
			c241m01a.setSpecifyCycle(null);// 每次決定

			String remomo = Util.trim(elf491.getElf491_remomo());

			if (CrsUtil.isNOT_null_and_NOTZeroDate(crDate)) {
				CrsUtil.setR98Data(c241m01a, remomo, elf491.getElf491_uckddt(),
						Util.trim(elf491.getElf491_uckdline()));
				c241m01a.setRetrialKind(remomo);
			}
		}
		// 若無 491 的 remomo 資料,則團貸子戶 default 塞 6-1
		if (Util.isEmpty(c241m01a.getRetrialKind())) {
			c241m01a.setRetrialKind(CrsUtil.R6_1);
		}

		c241m01a.setDocKind(decide_docKindN_or_H___for_grpDetail(
				c240m01a_branchId, custId, dupNo, elf491 == null ? null
						: elf491.getElf491_crdate()));
		c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);
		c241m01a.setNewCase("N");

		return c241m01a;
	}

	private String decide_docKindN_or_H___for_grpDetail(String brNo,
			String custId, String dupNo, Date elf491_crdate) {

		List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(custId,
				dupNo);
		int cnt_match = 0;
		int cnt_otherLoan = 0;
		String[] lnap_07_noGuar = new String[] { "103", "104", "321" }; // 產品07可以做
																		// (A)3個無擔科目
																		// (B)1個有擔科目｛421｝
		for (MISLN20 lnf020 : lnf020_list) {
			String lnf020_bank_code = Util.trim(lnf020.getLnf020_bank_code());
			if (Util.notEquals(UtilConstants.兆豐銀行代碼, lnf020_bank_code)) {
				continue;
			}
			if (!Util.equals(brNo, CrsUtil.getBrNoFromLNF020_CONTRACT(lnf020
					.getLnf020_contract()))) {
				continue;
			}
			if (CrsUtil.isNOT_null_and_NOTZeroDate(lnf020
					.getLnf020_cancel_date())) {
				continue;
			}
			if (CrsUtil.is_69(lnf020.getLnf020_proj_class())) { // Rule12「勞工紓困」為免覆審案件
				continue;
			}

			for (MISLN30 lnf030 : misLNF030Service.selByCntrNo(lnf020
					.getLnf020_contract())) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(lnf030
						.getLnf030_cancel_date())) {
					continue;
				}
				String lnap = Util.trim(CrsUtil
						.getSubjCodeFromLNF030_LOAN_NO(lnf030
								.getLnf030_loan_no()));
				/*
				 * 判斷是否符合 一、 覆審規則 13:專案信貸（包含產品種類 08-卡友信貸, 71-歡喜信貸） 二、產品07 裡的
				 * 無擔科目
				 */
				if (CrsUtil.isRule13_match(lnf030.getLnf030_loan_class(),
						lnf030.getLnf030_loan_date())
						|| (CrsUtil.is_07(lnf030.getLnf030_loan_class()) && CrsUtil
								.inCollection(lnap, lnap_07_noGuar))) {
					++cnt_match;
				} else {
					++cnt_otherLoan;
				}
			}
		}

		if (cnt_match > 0 && cnt_otherLoan == 0) {
			return CrsUtil.DOCKIND_H;
		} else {
			if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491_crdate)) {
				return CrsUtil.DOCKIND_N;
			} else {
				// 可能客戶有房貸、團體信貸
				// 若 elf491_crdate 是 null，表示 {非團體信貸}的其它貸款 (1)已覆審過 或(2)無需覆審
				return CrsUtil.DOCKIND_H;
			}
		}
	}

	@Override
	public boolean produce8_1(C240M01A c240m01a) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		{// 刪除原有的明細(8-1)
			for (C241M01A c241m01a : retrialService
					.findC241M01A_C240M01A(c240m01a.getMainId())) {

				if (CrsUtil.haveBeenUpload(c241m01a)) {
					// 上傳過.不刪
					continue;
				}
				if (CrsUtil.isOnly8_1(c241m01a) == false) {
					// 若為 3;8-1. 則不應刪除
					continue;
				}

				// user 手動加入的,不自動刪除
				// 若有需要,由 UI 執行刪除(註記)不覆審客戶
				if (Util.equals(CrsUtil.C241M01A_NCREATEDATA_USR,
						c241m01a.getNCreatData())) {
					continue;
				}
				deleteC241_classes(c241m01a);
			}
		}

		int max_count = c240m01a.getPlotsOfReview()
				* c240m01a.getThisSamplingRate() / 100;

		/*
		 * misELF491Service.selRuleNo8_1
		 * 
		 * ELF489_CUST_ID ELF489_DUP_NO ELF489_RULE_NO ELF491_LRDATE
		 * ELF491_REPORTKIND ELF491_CRDATE ELF491_REMOMO CNAME --------------
		 * -------------- -------------- ------------- -----------------
		 * ------------- --------------- --------------
		 * 
		 * 
		 * 
		 * lms491Service.LMS491selByBranch
		 * 
		 * CUSTID DUPNO LRDATE REPORTKIND CRDATE REMOMO ---------- -----
		 * ---------- ---------- ---------- ------------------------------
		 */
		List<Map<String, Object>> list = misELF491Service.selRuleNo8_1(c240m01a
				.getBranchId());
		if (CollectionUtils.isNotEmpty(list)) {
			List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
			List<C241M01A> c241m01as = new ArrayList<C241M01A>();
			List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
			List<C241A01A> c241a01as = new ArrayList<C241A01A>();
			List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
			List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
			List<C241M01C> c241m01cs = new ArrayList<C241M01C>();

			Set<String> existCustSet = new HashSet<String>();
			{// 現有的 custId+dupNo
				for (C241M01A row : retrialService
						.findC241M01A_C240M01A(c240m01a.getMainId())) {
					if (CrsUtil.isCaseN(row) || CrsUtil.isCaseG___N_Detail(row)
							|| CrsUtil.isCaseH(row)
							|| CrsUtil.isCaseG___H_Detail(row)) {
						existCustSet.add(LMSUtil.getCustKey_len10custId(
								row.getCustId(), row.getDupNo()));
					}
				}
			}

			for (Map<String, Object> map : list) {
				// 每次把 item 加到 c241m01as
				if (c241m01a_list.size() >= max_count) {
					break;
				}

				if (existCustSet.contains(LMSUtil.getCustKey_len10custId(
						Util.trim(map.get("ELF489_CUST_ID")),
						Util.trim(map.get("ELF489_DUP_NO"))))) {
					continue;
				}
				Date lrDate = CapDate.parseDate(Util.trim(map
						.get("ELF491_LRDATE")));
				Date crDate = CapDate.parseDate(Util.trim(map
						.get("ELF491_CRDATE")));
				Date lastRealDt = CapDate.parseDate(Util.trim(map
						.get("ELF491_LASTREALDT")));
				String remomo = Util.trim(map.get("ELF491_REMOMO"));
				// ---
				C241M01A c241m01a = new C241M01A();
				c241m01a.setMainId(IDGenerator.getUUID());
				c241m01a.setCustId(Util.trim(map.get("ELF489_CUST_ID")));
				c241m01a.setDupNo(Util.trim(map.get("ELF489_DUP_NO")));
				c241m01a.setCustName(Util.trim(map.get("CNAME")));
				c241m01a.setLastRetrialDate(CrsUtil
						.isNOT_null_and_NOTZeroDate(lrDate) ? lrDate : null);
				c241m01a.setLastRealDt(CrsUtil
						.isNOT_null_and_NOTZeroDate(lastRealDt) ? lastRealDt
						: null);
				c241m01a.setShouldReviewDate(CrsUtil
						.isNOT_null_and_NOTZeroDate(crDate) ? crDate : c240m01a
						.getExpectedRetrialDate());

				c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);

				if (CrsUtil.isNull_or_ZeroDate(crDate)) {
					// 若 crDate 為 0001-01-01, 可能 remomo 8-1 ; 9
					c241m01a.setRetrialKind(CrsUtil.R8_1);
				} else {
					TreeMap<String, String> ruleMap = CrsUtil.parseRule(remomo);
					if (!ruleMap.containsKey(CrsUtil.R8_1)) {
						ruleMap.put(CrsUtil.R8_1, "");
						remomo = CrsUtil.combineRule(ruleMap);
					}
					c241m01a.setRetrialKind(remomo);
				}

				_newCase_491toc241m01a(Util.trim(map.get("ELF491_REPORTKIND")),
						c241m01a);
				c241m01a.setDocKind(CrsUtil.DOCKIND_N);
				c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);
				// ===
				c241m01a_list.add(c241m01a);
			}
			// ---
			String c240m01a_stdCurr = this.branchService.getBranch(
					c240m01a.getBranchId()).getUseSWFT();
			produce_commonByC241M01A("@produce8_1", new CrsVO(), c241m01a_list,
					c241m01as, c240m01a.getBranchId(), c240m01a_stdCurr,
					c240m01a.getMainId(), c240m01a.getDocStatus(),
					c240m01a.getExpectedRetrialDate(), c240m01bs, c241a01as,
					c241m01bs, delC241M01BList, c241m01cs, user.getUserId(),
					user.getUnitNo(), user.getUnitType());

			retrialService.save(c241m01as);
			c240m01bDao.save(c240m01bs);
			c241a01aDao.save(c241a01as);
			c241m01bDao.delete(delC241M01BList);
			c241m01bDao.save(c241m01bs);
			c241m01cDao.save(c241m01cs);
			// ------
			c240m01a_reQuantity_thisSamplingCount(c240m01a);
			retrialService.save(c240m01a);
		}
		return true;
	}

	private boolean is_elf489_contains_8_1(String brNo, String custId,
			String dupNo) {
		Map<String, Object> elf489_map = misELF489Service.sel_by_brNo_idDup(
				brNo, custId, dupNo);
		boolean match_8_1 = false;
		if (MapUtils.isNotEmpty(elf489_map)) {
			String elf489_rule_no = Util.trim(MapUtils.getString(elf489_map,
					"ELF489_RULE_NO"));
			if (Util.isNotEmpty(elf489_rule_no)
					&& CrsUtil.parseRule(elf489_rule_no).containsKey(
							CrsUtil.R8_1)) {
				match_8_1 = true;
			}
		}
		return match_8_1;
	}

	@Override
	public boolean produceNew(C240M01A c240m01a, String custId, String dupNo,
			String cName) throws CapException {
		boolean r = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		{
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(cName);
			// 前次覆審日
			c241m01a.setLastRetrialDate(null);
			c241m01a.setLastRealDt(null);
			// 本次覆審日
			c241m01a.setShouldReviewDate(c240m01a.getExpectedRetrialDate());

			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			c241m01a.setRetrialKind(CrsUtil.R99);
			c241m01a.setNewCase("P");// 人工

			c241m01a.setDocKind(CrsUtil.DOCKIND_N);
			c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);
			// ===
			ELF491 elf491 = misELF491Service.findByPk(c240m01a.getBranchId(),
					custId, dupNo);
			if (Util.isNotEmpty(elf491)) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
						.getElf491_crdate())) {
					replace_c241m01a_with_elf491(c241m01a, elf491);
					// J-110-0272
					if (!Util.equals(c241m01a.getRetrialKind(), CrsUtil.R99)
							&& is_elf489_contains_8_1(c240m01a.getBranchId(),
									custId, dupNo)) {
						TreeMap<String, String> retrialKind_map = CrsUtil
								.parseRule(c241m01a.getRetrialKind());
						retrialKind_map.put(CrsUtil.R8_1, "");
						c241m01a.setRetrialKind(CrsUtil
								.combineRule(retrialKind_map));
					}
				} else {
					c241m01a.setLastRetrialDate(CrsUtil
							.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lrdate()) ? elf491
							.getElf491_lrdate() : null);
					c241m01a.setLastRealDt(CrsUtil
							.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lastRealDt()) ? elf491
							.getElf491_lastRealDt() : null);
				}
			} else {
				/*
				 * boolean match_8_1 =
				 * is_elf489_contains_8_1(c240m01a.getBranchId(), custId,
				 * dupNo); if(match_8_1){ //無ELF491,
				 * 可能是既有的額度、放款帳號，但符合免覆審的條件(例如：有效額度低於門檻) //先不要單純的就把它設成 8-1 }
				 */
			}
			// ===
			c241m01a_list.add(c241m01a);
		}
		// ---
		String c240m01a_stdCurr = this.branchService.getBranch(
				c240m01a.getBranchId()).getUseSWFT();
		produce_commonByC241M01A("@produceNew", new CrsVO(), c241m01a_list,
				c241m01as, c240m01a.getBranchId(), c240m01a_stdCurr,
				c240m01a.getMainId(), c240m01a.getDocStatus(),
				c240m01a.getExpectedRetrialDate(), c240m01bs, c241a01as,
				c241m01bs, delC241M01BList, c241m01cs, user.getUserId(),
				user.getUnitNo(), user.getUnitType());
		// ---
		// 在 produce_commonByC241M01A 裡會設定成 SYS, 手動改成 PEO
		for (C241M01A c241m01a : c241m01as) {
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		}

		if (c241m01as.size() > 0) {
			r = true;// 在新增覆審客戶時,若於該分行無帳務,不顯示
		}

		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		// ------
		c240m01a_reQuantity_thisSamplingCount(c240m01a);
		retrialService.save(c240m01a);
		return r;
	}

	@Override
	public boolean produceNewGrpDetail(C240M01A c240m01a,
			C241M01A c241m01a_grpMain, String custId, String dupNo, String cName)
			throws CapException {
		boolean r = false;

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		{
			C241M01A c241m01a = _build_grpDetail(c241m01a_grpMain.getOwnBrId(),
					c241m01a_grpMain.getGrpCntrNo(), custId, dupNo, cName);
			// ===
			c241m01a_list.add(c241m01a);
		}
		// ---
		String c240m01a_stdCurr = this.branchService.getBranch(
				c240m01a.getBranchId()).getUseSWFT();
		produce_commonByC241M01A("@produceNewGrpDetail", new CrsVO(),
				c241m01a_list, c241m01as, c240m01a.getBranchId(),
				c240m01a_stdCurr, c240m01a.getMainId(),
				c240m01a.getDocStatus(), c240m01a.getExpectedRetrialDate(),
				c240m01bs, c241a01as, c241m01bs, delC241M01BList, c241m01cs,
				user.getUserId(), user.getUnitNo(), user.getUnitType());
		// ---
		// 在 produce_commonByC241M01A 裡會設定成 SYS, 手動改成 PEO
		for (C241M01A c241m01a : c241m01as) {
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		}

		if (c241m01as.size() > 0) {
			r = true;// 在新增覆審客戶時,若於該分行無帳務,不顯示
		}

		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		// ------
		// 團貸子戶不列入計算
		// c240m01a_reQuantity_thisSamplingCount(c240m01a);
		retrialService.save(c240m01a);
		return r;
	}

	@Override
	public boolean produce95_1(C240M01A c240m01a, String custId, String dupNo,
			String cName) throws CapException {
		String brNo = c240m01a.getBranchId();
		boolean r = produceNew(c240m01a, custId, dupNo, cName);
		if (r) {
			for (C241M01A c241m01a : retrialService
					.findC241M01A_C240M01A_idDup(c240m01a.getMainId(), custId,
							dupNo)) {
				if (Util.equals(c241m01a.getDocKind(), CrsUtil.DOCKIND_N)
				// && Util.equals(c241m01a.getRetrialKind(), CrsUtil.R99)
				) {
					TreeMap<String, String> ruleMap = new TreeMap<String, String>();
					if (true) {
						Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
						String elf490_data_ym_beg = CrsUtil
								.elf490YM_from_adDate(CapDate.shiftDays(
										sysMonth_1st, -1));
						// ============================
						for (ELF490 elf490 : misELF490Service
								.findDataymBrnoCustIdDupNo(elf490_data_ym_beg,
										brNo, custId, dupNo)) {
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no()));
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no_new()));
						}
					}
					if (true) {
						ruleMap.put(CrsUtil.R95_1, "");
					}
					c241m01a.setRetrialKind(CrsUtil.combineRule(ruleMap));
					// ~~~~~~~
					c241m01aDao.save(c241m01a);
				}
			}
		}
		return r;
	}

	@Override
	public boolean produce96(C240M01A c240m01a, String custId, String dupNo,
			String cName, String pa_ym, String pa_trg) throws CapException {
		boolean r = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		List<C241M01F> c241m01fs = new ArrayList<C241M01F>();
		String c241m01a_mainId = IDGenerator.getUUID();
		{
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(c241m01a_mainId);
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(cName);
			// 前次覆審日
			Date lrDate = null;
			List<ELF491B> elf491b_list = misELF491BService.findByBrNoIdDup(
					c240m01a.getBranchId(), custId, dupNo);
			if (elf491b_list.size() > 0) {
				lrDate = elf491b_list.get(0).getElf491b_lrdate();
			}
			c241m01a.setLastRetrialDate(CrsUtil
					.isNOT_null_and_NOTZeroDate(lrDate) ? lrDate : null); // 這裡的值,
																			// 會影響到
																			// RetrialService
																			// ::
																			// findC241M01A_bef(C241M01A
																			// meta)
			c241m01a.setLastRealDt(null);
			// 本次覆審日
			c241m01a.setShouldReviewDate(c240m01a.getExpectedRetrialDate());

			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			c241m01a.setRetrialKind(CrsUtil.R96);
			c241m01a.setNewCase("");// 人工

			c241m01a.setDocKind(CrsUtil.DOCKIND_S);
			if (CrsUtil.isCaseS(c241m01a)) {
				c241m01a.setPa_ym(pa_ym);
				c241m01a.setPa_trg(pa_trg);
			}
			// ===
			c241m01a_list.add(c241m01a);
		}
		// ---
		String c240m01a_stdCurr = this.branchService.getBranch(
				c240m01a.getBranchId()).getUseSWFT();
		produce_commonByC241M01A("@produce96", new CrsVO(), c241m01a_list,
				c241m01as, c240m01a.getBranchId(), c240m01a_stdCurr,
				c240m01a.getMainId(), c240m01a.getDocStatus(),
				c240m01a.getExpectedRetrialDate(), c240m01bs, c241a01as,
				c241m01bs, delC241M01BList, c241m01cs, user.getUserId(),
				user.getUnitNo(), user.getUnitType());
		// ---
		// 在 produce_commonByC241M01A 裡會設定成 SYS, 手動改成 PEO
		for (C241M01A c241m01a : c241m01as) {
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		}

		if (c241m01as.size() > 0) {
			r = true;// 在新增覆審客戶時,若於該分行無帳務,不顯示
		}

		if (r && c240m01bs.size() > 0) {
			// 以 custId 去找在「覆審基準期間」新作的額度
			String[] period_arr = CrsUtil.get_CLS180R18_period_byEndYM(pa_ym,
					10);
			Date beg_dt = CapDate.parseDate(period_arr[0]);
			Date end_dt = CapDate.parseDate(period_arr[1]);
			Set<String> cntrNo_in_period = new HashSet<String>();
			for (C241M01B c241m01b : c241m01bs) {
				if (Util.trim(c241m01b.getQuotaNo()).startsWith(
						c240m01a.getBranchId())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b
								.getLNF020CrtDate())
						&& LMSUtil.cmpDate(c241m01b.getLNF020CrtDate(), ">=",
								beg_dt)
						&& LMSUtil.cmpDate(c241m01b.getLNF020CrtDate(), "<=",
								end_dt)) {
					cntrNo_in_period.add(c241m01b.getQuotaNo());
				}
			}
			if (cntrNo_in_period.size() == 0) {
				// 若無，挑該分行「最晚建檔的cntrNo」
				Date max_lnf020_create_dt = null;
				for (C241M01B c241m01b : c241m01bs) {
					if (Util.trim(c241m01b.getQuotaNo()).startsWith(
							c240m01a.getBranchId())
							&& CrsUtil.isNOT_null_and_NOTZeroDate(c241m01b
									.getLNF020CrtDate())) {
						if (max_lnf020_create_dt == null) {
							max_lnf020_create_dt = c241m01b.getLNF020CrtDate();
						} else {
							if (LMSUtil.cmpDate(c241m01b.getLNF020CrtDate(),
									">", max_lnf020_create_dt)) {
								max_lnf020_create_dt = c241m01b
										.getLNF020CrtDate();
							}
						}
					}
				}
				if (max_lnf020_create_dt != null) {
					for (C241M01B c241m01b : c241m01bs) {
						if (Util.trim(c241m01b.getQuotaNo()).startsWith(
								c240m01a.getBranchId())
								&& LMSUtil.cmpDate(c241m01b.getLNF020CrtDate(),
										"==", max_lnf020_create_dt)) {
							cntrNo_in_period.add(c241m01b.getQuotaNo());
						}
					}
				}
			}

			// 再用 cntrNo 去找 => 新作的 unid ======> 可能１份簽報書含２個額度
			HashMap<String, Set<String>> case_cntrNo_map = new HashMap<String, Set<String>>();
			for (String cntrNo : cntrNo_in_period) {
				ELF459 elf459 = misELF459Service.findByCntrNo(cntrNo);
				String caseMainId = "";
				if (elf459 != null) {
					caseMainId = elf459.getElf459_rptId();
				} else {
					caseMainId = misELF447Service
							.findProperty1UnidByCntrNo(cntrNo); // 抓
																// ELF447(企金簽報書,
																// 包含個金額度)
				}
				// ========================
				if (Util.isEmpty(Util.trim(caseMainId))) {
					continue;
				}

				if (true) {
					if (!case_cntrNo_map.containsKey(caseMainId)) {
						case_cntrNo_map.put(caseMainId, new HashSet<String>());
					}
					case_cntrNo_map.get(caseMainId).add(cntrNo);
				}
			}
			// ==============================
			if (case_cntrNo_map.size() > 0) {
				Timestamp nowTS = CapDate.getCurrentTimestamp();
				// ~~~~~~
				Set<String> rel_match_empNo_set = new HashSet<String>();
				// 若不加 brNo , 會抓到其它營運中心
				for (ELF490B elf490b : misELF490bService
						.selBy_dataym_flag_brNo(pa_ym, "2",
								c240m01a.getBranchId())) {
					rel_match_empNo_set.add(elf490b.getElf490b_emp_no());
				}

				for (String caseMainId : case_cntrNo_map.keySet()) {
					BigDecimal caseYear = BigDecimal.ZERO;
					BigDecimal caseSeq = BigDecimal.ZERO;
					L120M01A l120m01a = clsService
							.findL120M01A_mainId(caseMainId);
					if (l120m01a != null) {
						caseYear = BigDecimal.valueOf(l120m01a.getCaseYear());
						caseSeq = BigDecimal.valueOf(l120m01a.getCaseSeq());
					}
					TreeSet<String> rel_type_1_set = new TreeSet<String>();
					TreeSet<String> rel_type_2_set = new TreeSet<String>();
					TreeSet<String> rel_type_3_set = new TreeSet<String>();
					int rel_type_1_seq = 0;
					int rel_type_2_seq = 0;
					int rel_type_3_seq = 0;
					// ===========
					// 引介人
					for (String cntrNo : case_cntrNo_map.get(caseMainId)) {
						LNF13E lnf13e = misdbBaseService
								.findLNF13E_contract(cntrNo);
						String lnf13e_mega_empno = Util
								.trim(lnf13e != null ? lnf13e
										.getLnf13e_mega_empno() : "");
						if (Util.isNotEmpty(lnf13e_mega_empno)) {
							rel_type_1_set.add(lnf13e_mega_empno);
						}
					}

					for (String empNo : rel_type_1_set) {
						C241M01F c241m01f = new C241M01F();
						c241m01f.setMainId(c241m01a_mainId);
						c241m01f.setCaseMainId(caseMainId);
						c241m01f.setRel_type("1"); // 引介人
						c241m01f.setStaffNo(empNo);
						c241m01f.setStaffJob("");
						c241m01f.setRel_match(rel_match_empNo_set
								.contains(c241m01f.getStaffNo()) ? "Y" : "N");
						c241m01f.setCaseYear(caseYear);
						c241m01f.setCaseSeq(caseSeq);
						c241m01f.setSeq(BigDecimal.valueOf(++rel_type_1_seq));
						c241m01f.setCreator(user.getUserId());
						c241m01f.setCreateTime(nowTS);
						// ==============
						c241m01fs.add(c241m01f);
					}
					// ===========
					// 經辦人員, 覆核主管
					for (Map<String, Object> elf447_map : misELF447Service
							.findForC241M01A(caseMainId)) {
						String ELF447_ROLE = Util.trim(MapUtils.getString(
								elf447_map, "ELF447_ROLE"));
						String ELF447_EMP_NO = Util.trim(MapUtils.getString(
								elf447_map, "ELF447_EMP_NO"));
						if (Util.equals(ELF447_ROLE, "1")) { // L1
							rel_type_2_set.add(ELF447_EMP_NO);
						} else if (Util.equals(ELF447_ROLE, "3")) { // 執行覆核主管L4
							rel_type_3_set.add(ELF447_EMP_NO);
						} else if (Util.equals(ELF447_ROLE, "6")) { // 授信主管L3
							rel_type_3_set.add(ELF447_EMP_NO);
						}
					}

					for (String empNo : rel_type_2_set) {
						C241M01F c241m01f = new C241M01F();
						c241m01f.setMainId(c241m01a_mainId);
						c241m01f.setCaseMainId(caseMainId);
						c241m01f.setRel_type("2"); // 經辦人員
						c241m01f.setStaffNo(empNo);
						c241m01f.setStaffJob("L1");
						c241m01f.setRel_match(rel_match_empNo_set
								.contains(c241m01f.getStaffNo()) ? "Y" : "N");
						c241m01f.setCaseYear(caseYear);
						c241m01f.setCaseSeq(caseSeq);
						c241m01f.setSeq(BigDecimal.valueOf(++rel_type_2_seq));
						c241m01f.setCreator(user.getUserId());
						c241m01f.setCreateTime(nowTS);
						// ==============
						c241m01fs.add(c241m01f);
					}
					for (String empNo : rel_type_3_set) {
						C241M01F c241m01f = new C241M01F();
						c241m01f.setMainId(c241m01a_mainId);
						c241m01f.setCaseMainId(caseMainId);
						c241m01f.setRel_type("3"); // 覆核主管
						c241m01f.setStaffNo(empNo);
						c241m01f.setStaffJob("L4");
						c241m01f.setRel_match(rel_match_empNo_set
								.contains(c241m01f.getStaffNo()) ? "Y" : "N");
						c241m01f.setCaseYear(caseYear);
						c241m01f.setCaseSeq(caseSeq);
						c241m01f.setSeq(BigDecimal.valueOf(++rel_type_3_seq));
						c241m01f.setCreator(user.getUserId());
						c241m01f.setCreateTime(nowTS);
						// ==============
						c241m01fs.add(c241m01f);
					}
				}
			}
		}

		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		c241m01fDao.save(c241m01fs);
		// ------
		c240m01a_reQuantity_thisSamplingCount(c240m01a);
		retrialService.save(c240m01a);
		return r;
	}

	@Override
	public boolean produce97(C240M01A meta, List<Map<String, Object>> list,
			Map<String, C241M01A> existMap, String lnf660_m_contract,
			String lnf660_loan_class, String comId, String comDupNo,
			String comName) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();

		Map<String, Object> lnf660_map = misMISLN20Service
				.findByCntrNo(lnf660_m_contract);
		String c240m01a_stdCurr = this.branchService.getBranch(
				meta.getBranchId()).getUseSWFT();

		for (String key : existMap.keySet()) {
			deleteC241_classes(existMap.get(key));
		}

		Map<String, Map<String, Object>> lnf040Map = _lnf040Map(misLNF040Service
				.selAll());
		for (Map<String, Object> m : list) {
			String custId = Util.trim(m.get("custId"));
			String dupNo = Util.trim(m.get("dupNo"));
			String cName = Util.trim(m.get("custName"));
			// ---
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(cName);
			// 前次覆審日
			c241m01a.setLastRetrialDate(null); // 這裡的值, 會影響到 RetrialService ::
												// findC241M01A_bef(C241M01A
												// meta)
			c241m01a.setLastRealDt(null);
			// 本次覆審日
			c241m01a.setShouldReviewDate(meta.getExpectedRetrialDate());

			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			c241m01a.setRetrialKind(CrsUtil.R97);
			c241m01a.setNewCase("");

			c241m01a.setDocKind(CrsUtil.DOCKIND_P);
			// ---
			c241m01a.setOverdueP(Util.trim(m.get("Overdue")));
			c241m01a.setComId(comId);
			c241m01a.setComDup(comDupNo);
			c241m01a.setComName(comName);
			c241m01a.setBuyerId("");
			c241m01a.setBuyerDup("");
			c241m01a.setBuyerName("");
			c241m01a.setLnf660_loan_class(lnf660_loan_class);
			c241m01a.setPbAcct(Util.trim(m.get("LNF034_LC_NO")));// default 和
																	// LCNO 一樣
			c241m01a.setLnf660_m_contract(lnf660_m_contract);
			if (MapUtils.isNotEmpty(lnf660_map)) {
				c241m01a.setLnf660_m_curr(Util.trim(lnf660_map
						.get("LNF020_SWFT")));
				c241m01a.setLnf660_m_fact((BigDecimal) lnf660_map
						.get("LNF020_FACT_AMT"));
				c241m01a.setLnf660_m_begDate((Date) lnf660_map
						.get("LNF020_BEG_DATE"));
				c241m01a.setLnf660_m_endDate((Date) lnf660_map
						.get("LNF020_END_DATE"));
			}

			if (existMap.containsKey(CrsUtil.build_P_Key(m))) {
				c241m01a.setProjectNo(existMap.get(CrsUtil.build_P_Key(m))
						.getProjectNo());
			}
			// ===
			c241m01a_list.add(c241m01a);

			// 處理 C241M01B
			proc_E_C241M01B(c241m01bs, c241m01a, m, c240m01a_stdCurr, lnf040Map);
		}
		// ---

		produce_commonByC241M01A("@produce97", new CrsVO(), c241m01a_list,
				c241m01as, meta.getBranchId(), c240m01a_stdCurr,
				meta.getMainId(), meta.getDocStatus(),
				meta.getExpectedRetrialDate(), c240m01bs, c241a01as, c241m01bs,
				delC241M01BList, c241m01cs, user.getUserId(), user.getUnitNo(),
				user.getUnitType());
		// ---
		// 在 produce_commonByC241M01A 裡會設定成 SYS, 手動改成 PEO
		for (C241M01A c241m01a : c241m01as) {
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		}

		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		// ---
		c240m01a_reQuantity_thisSamplingCount(meta);
		retrialService.save(meta);

		return true;
	}

	@Override
	public boolean produce_R1R2S(C240M01A c240m01a, String custId,
			String dupNo, String cName) throws CapException {
		String brNo = c240m01a.getBranchId();
		boolean r = produceNew(c240m01a, custId, dupNo, cName);
		if (r) {
			for (C241M01A c241m01a : retrialService
					.findC241M01A_C240M01A_idDup(c240m01a.getMainId(), custId,
							dupNo)) {
				if (Util.equals(c241m01a.getDocKind(), CrsUtil.DOCKIND_N)
				// && Util.equals(c241m01a.getRetrialKind(), CrsUtil.R99)
				) {
					TreeMap<String, String> ruleMap = new TreeMap<String, String>();
					if (true) {
						Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
						String elf490_data_ym_beg = CrsUtil
								.elf490YM_from_adDate(CapDate.shiftDays(
										sysMonth_1st, -1));
						// ============================
						for (ELF490 elf490 : misELF490Service
								.findDataymBrnoCustIdDupNo(elf490_data_ym_beg,
										brNo, custId, dupNo)) {
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no()));
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no_new()));
						}
						ruleMap.remove(CrsUtil.R12);
					}
					if (true) {
						ruleMap.put(CrsUtil.R1_1___R2_1, "");
					}
					c241m01a.setRetrialKind(CrsUtil.combineRule(ruleMap));
					// ~~~~~~~
					c241m01aDao.save(c241m01a);
				}
			}
		}
		return r;

	}

	@Override
	public boolean produce_R14(C240M01A c240m01a, String custId, String dupNo,
			String cName) throws CapException {
		String brNo = c240m01a.getBranchId();
		boolean r = produceNew(c240m01a, custId, dupNo, cName);
		if (r) {
			for (C241M01A c241m01a : retrialService
					.findC241M01A_C240M01A_idDup(c240m01a.getMainId(), custId,
							dupNo)) {
				if (Util.equals(c241m01a.getDocKind(), CrsUtil.DOCKIND_N)
				// && Util.equals(c241m01a.getRetrialKind(), CrsUtil.R99)
				) {
					TreeMap<String, String> ruleMap = new TreeMap<String, String>();
					if (true) {
						Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
						String elf490_data_ym_beg = CrsUtil
								.elf490YM_from_adDate(CapDate.shiftDays(
										sysMonth_1st, -1));
						// ============================
						for (ELF490 elf490 : misELF490Service
								.findDataymBrnoCustIdDupNo(elf490_data_ym_beg,
										brNo, custId, dupNo)) {
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no()));
							ruleMap.putAll(CrsUtil.parseRule(elf490
									.getElf490_rule_no_new()));
						}
						ruleMap.remove(CrsUtil.R12);
					}
					if (true) {
						ruleMap.put(CrsUtil.R14, "");
					}
					c241m01a.setRetrialKind(CrsUtil.combineRule(ruleMap));
					// ~~~~~~~
					c241m01aDao.save(c241m01a);
				}
			}
		}
		return r;

	}

	@Override
	public boolean is_only_projectCreditLoan(String brNo, String custId,
			String dupNo) {
		/*
		 * 在判斷是否 純專案信貸，不適合用 elf491_crdate 若客戶之前同時有房貸,勞工紓困 => 因 勞工紓困 的覆審規則一直調整,
		 * 可能 elf491_crdate 未被清成 0001-01-01 if(true){ ELF491 elf491 =
		 * misELF491Service.findByPk(brNo, custId, dupNo); if(elf491!=null &&
		 * CrsUtil.isNOT_null_and_NOTZeroDate(elf491.getElf491_crdate()) ){
		 * return false; } }
		 */
		if (true) {
			TreeMap<String, String> ruleMap = new TreeMap<String, String>();
			if (true) {
				Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
				String elf490_data_ym_beg = CrsUtil
						.elf490YM_from_adDate(CapDate.shiftDays(sysMonth_1st,
								-1));
				// ============================
				for (ELF490 elf490 : misELF490Service
						.findDataymBrnoCustIdDupNo(elf490_data_ym_beg, brNo,
								custId, dupNo)) {
					ruleMap.putAll(CrsUtil.parseRule(elf490.getElf490_rule_no()));
					ruleMap.putAll(CrsUtil.parseRule(elf490
							.getElf490_rule_no_new()));
				}
			}

			// 專案信貸, 本身是RULE13
			// 這裡先粗分，若含8-1，在後面的程式再區分
			if (LMSUtil.elm_onlyLeft(ruleMap.keySet(),
					CrsUtil.convert_arr_to_set(CrsUtil.RULE_ARR_R12_R13_R8_1))
					.size() > 0) {
				return false;
			}
		}

		if (true) {
			List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(custId,
					dupNo);
			int cnt_projectCreditLoan = 0;
			int cnt_otherLoan = 0;
			for (MISLN20 lnf020 : lnf020_list) {
				String lnf020_bank_code = Util.trim(lnf020
						.getLnf020_bank_code());
				if (Util.notEquals(UtilConstants.兆豐銀行代碼, lnf020_bank_code)) {
					continue;
				}
				if (!Util.equals(brNo,
						CrsUtil.getBrNoFromLNF020_CONTRACT(lnf020
								.getLnf020_contract()))) {
					continue;
				}
				if (CrsUtil.isNOT_null_and_NOTZeroDate(lnf020
						.getLnf020_cancel_date())) {
					continue;
				}
				if (CrsUtil.is_69(lnf020.getLnf020_proj_class())) { // Rule12「勞工紓困」為免覆審案件
					continue;
				}

				for (MISLN30 lnf030 : misLNF030Service.selByCntrNo(lnf020
						.getLnf020_contract())) {
					if (CrsUtil.isNOT_null_and_NOTZeroDate(lnf030
							.getLnf030_cancel_date())) {
						continue;
					}

					if (CrsUtil.isRule13_match(lnf030.getLnf030_loan_class(),
							lnf030.getLnf030_loan_date())) {
						++cnt_projectCreditLoan;
					} else {
						++cnt_otherLoan;
					}
				}
			}
			if (cnt_projectCreditLoan > 0 && cnt_otherLoan == 0) {
				return true;
			}
		}
		return false;
	}

	private boolean produce_docKindH(C240M01A c240m01a, String custId,
			String dupNo, String cName) throws CapException {
		boolean r = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C241M01A> c241m01a_list = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		List<C241A01A> c241a01as = new ArrayList<C241A01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		List<C241M01C> c241m01cs = new ArrayList<C241M01C>();
		{
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(cName);
			// 前次覆審日
			c241m01a.setLastRetrialDate(null);
			c241m01a.setLastRealDt(null);
			// 本次覆審日
			c241m01a.setShouldReviewDate(c240m01a.getExpectedRetrialDate());

			c241m01a.setRetrialYN(CrsUtil.C241M01A_RETRIALYN_Y);
			c241m01a.setNewCase("P");// 人工

			c241m01a.setDocKind(CrsUtil.DOCKIND_H);
			c241m01a.setDocFmt(CrsUtil.DOCFMT_一般);
			// ===
			ELF491 elf491 = misELF491Service.findByPk(c240m01a.getBranchId(),
					custId, dupNo);
			if (Util.isNotEmpty(elf491)) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
						.getElf491_crdate())) {
					replace_c241m01a_with_elf491(c241m01a, elf491);
				} else {
					c241m01a.setLastRetrialDate(CrsUtil
							.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lrdate()) ? elf491
							.getElf491_lrdate() : null);
					c241m01a.setLastRealDt(CrsUtil
							.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lastRealDt()) ? elf491
							.getElf491_lastRealDt() : null);
				}
			}
			if (true) {
				// setRetrialKind(CrsUtil.R13) 要在
				// replace_c241m01a_with_elf491(c241m01a, elf491) 之後
				// 免得被 elf491_remomo 給覆蓋掉
				c241m01a.setRetrialKind(CrsUtil.R13);
			}
			// ===
			c241m01a_list.add(c241m01a);
		}
		// ---
		String c240m01a_stdCurr = this.branchService.getBranch(
				c240m01a.getBranchId()).getUseSWFT();
		produce_commonByC241M01A("@produce_docKindH", new CrsVO(),
				c241m01a_list, c241m01as, c240m01a.getBranchId(),
				c240m01a_stdCurr, c240m01a.getMainId(),
				c240m01a.getDocStatus(), c240m01a.getExpectedRetrialDate(),
				c240m01bs, c241a01as, c241m01bs, delC241M01BList, c241m01cs,
				user.getUserId(), user.getUnitNo(), user.getUnitType());
		// ---
		// 在 produce_commonByC241M01A 裡會設定成 SYS, 手動改成 PEO
		for (C241M01A c241m01a : c241m01as) {
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_USR);
		}

		if (c241m01as.size() > 0) {
			r = true;// 在新增覆審客戶時,若於該分行無帳務,不顯示
		}

		retrialService.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		c241a01aDao.save(c241a01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		c241m01cDao.save(c241m01cs);
		// ------
		c240m01a_reQuantity_thisSamplingCount(c240m01a);
		retrialService.save(c240m01a);
		return r;
	}

	@Override
	public boolean produce_projectCreditLoan(C240M01A c240m01a, String custId,
			String dupNo, String cName, boolean is_only_projectCreditLoan)
			throws CapException {
		String brNo = c240m01a.getBranchId();

		if (is_only_projectCreditLoan) {
			return produce_docKindH(c240m01a, custId, dupNo, cName);
		} else {
			boolean r = produceNew(c240m01a, custId, dupNo, cName);
			if (r) {
				for (C241M01A c241m01a : retrialService
						.findC241M01A_C240M01A_idDup(c240m01a.getMainId(),
								custId, dupNo)) {
					if (Util.equals(c241m01a.getDocKind(), CrsUtil.DOCKIND_N)) {
						/*
						 * 客戶可能同時有房貸、專案信貸，可能 elf491_remomo 已有存在某些規則 => 若
						 * elf491_remomo=3，產出的 c241m01a.retrialKind 應為 3;13
						 */
						TreeMap<String, String> ruleMap = new TreeMap<String, String>();
						if (true) {
							ruleMap.put(CrsUtil.R13, "");
						}
						if (true) {
							Date sysMonth_1st = CrsUtil.get_sysMonth_1st();
							String elf490_data_ym_beg = CrsUtil
									.elf490YM_from_adDate(CapDate.shiftDays(
											sysMonth_1st, -1));
							// ============================
							for (ELF490 elf490 : misELF490Service
									.findDataymBrnoCustIdDupNo(
											elf490_data_ym_beg, brNo, custId,
											dupNo)) {
								ruleMap.putAll(CrsUtil.parseRule(elf490
										.getElf490_rule_no()));
								ruleMap.putAll(CrsUtil.parseRule(elf490
										.getElf490_rule_no_new()));
							}
						}
						if (true) {
							// ruleMap.remove(CrsUtil.R8_1);
							// //一個人可能同時有［產品08,71］與［行家理財無擔299萬(非小額)］
							ruleMap.remove(CrsUtil.R12); // 勞工紓困免覆審
							// ~~~~~~
							ruleMap.remove(CrsUtil.R99);
						}
						c241m01a.setRetrialKind(CrsUtil.combineRule(ruleMap));
						// ~~~~~~~
						c241m01aDao.save(c241m01a);
					}
				}
			}
			return r;
		}
	}

	private Map<String, Map<String, Object>> _lnf040Map(
			List<Map<String, Object>> lnf040_list) {
		Map<String, Map<String, Object>> r = new HashMap<String, Map<String, Object>>();
		for (Map<String, Object> lnf040 : lnf040_list) {
			r.put(Util.trim(lnf040.get("LNF040_LNAP_CODE")), lnf040);
		}
		return r;
	}

	private void proc_E_C241M01B(List<C241M01B> c241m01bs, C241M01A c241m01a,
			Map<String, Object> m, String c240m01a_stdCurr,
			Map<String, Map<String, Object>> lnf040Map) {
		C241M01B c241m01b = new C241M01B();
		Timestamp now = CapDate.getCurrentTimestamp();
		// ---
		c241m01b.setMainId(c241m01a.getMainId());
		c241m01b.setCustId(c241m01a.getCustId());
		c241m01b.setDupNo(c241m01a.getDupNo());
		c241m01b.setQuotaNo(Util.trim(m.get("LNF020_CONTRACT")));
		c241m01b.setLoanNo(Util.trim(m.get("LNF030_LOAN_NO")));
		c241m01b.setLcNo(Util.trim(m.get("LNF034_LC_NO")));
		c241m01b.setLnDataDate(now);

		// 額度性質 新貸
		// 授信科目 36 一般擔保履約保證(17621802) 案 號
		c241m01b.setQuotaCurr(Util.trim(m.get("LNF020_SWFT")));
		c241m01b.setQuotaAmt((BigDecimal) m.get("LNF020_FACT_AMT"));
		c241m01b.setUseFDate((Date) m.get("LNF020_BEG_DATE"));
		c241m01b.setUseEDate((Date) m.get("LNF020_END_DATE"));
		c241m01b.setBalCurr(Util.trim(m.get("LNF030_SWFT")));
		c241m01b.setBalAmt((BigDecimal) m.get("LNF030_LOAN_BAL"));

		c241m01b.setYnReview("Y");
		c241m01b.setQuotaType("1");
		c241m01b.setSubjectNo(CrsUtil.getSubjCodeFromLNF030_LOAN_NO(c241m01b
				.getLoanNo()));

		String _SubjectName = "";
		String _Actcd = "";
		if (lnf040Map.containsKey(c241m01b.getSubjectNo())) {
			Map<String, Object> lnf040_item = lnf040Map.get(c241m01b
					.getSubjectNo());
			_SubjectName = Util.trim(lnf040_item.get("LNF040_ACT_NAME"));
			_Actcd = Util.trim(lnf040_item.get("LNF040_ACT_CODE"));
		}
		c241m01b.setSubjectName(_SubjectName);
		CrsUtil.processSubjName(c241m01b);
		c241m01b.setActcd(_Actcd);
		if (Util.trim(c241m01a.getLnf660_loan_class()).startsWith("H")) {
			c241m01b.setDocKindP_amt((BigDecimal) m.get("LNF034_CP_SELF_AMT"));
		}
		// ---
		// 將 c241m01b 的值,累計回 c241m01a
		c241m01a.setLnDataDate(now);
		c241m01a.setTotBal(c241m01b.getBalAmt());
		c241m01a.setTotQuota(c241m01b.getQuotaAmt());// 一般是在 produceCommon
														// 指定幣別,再 sum(C241M01B)
		// ---
		c241m01bs.add(c241m01b);
	}

	/**
	 * 把 c241m01a_list 有帳務資料的，才加入 c241m01as
	 */
	private void produce_commonByC241M01A(String debugMsg, CrsVO crsVo,
			List<C241M01A> c241m01a_list, List<C241M01A> c241m01as,
			String c240m01a_branchId, String c240m01a_stdCurr,
			String c240m01a_mainId, String c240m01a_docStatis,
			Date c240m01a_expectedRetrialDate, List<C240M01B> c240m01bs,
			List<C241A01A> c241a01as, List<C241M01B> c241m01bs,
			List<C241M01B> delC241M01BList, List<C241M01C> c241m01cs,
			String userId, String unitNo, String unitType) throws CapException {

		Timestamp nowTimestamp = CapDate.getCurrentTimestamp();
		String latest_aprdcdate = misDailyctlService.getLatest_aprdcdate();
		for (C241M01A c241m01a : c241m01a_list) {
			// ---
			// 先對 C241M01A 共同的部份設值
			c241m01a.setTypCd(TypCdEnum.DBU.getCode());
			c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(unitType));
			c241m01a.setOwnBrId(c240m01a_branchId);
			c241m01a.setDocStatus("");
			c241m01a.setCreator(userId);
			c241m01a.setCreateTime(nowTimestamp);
			c241m01a.setUpdater(userId);
			c241m01a.setUpdateTime(nowTimestamp);
			c241m01a.setTotBalCurr(c240m01a_stdCurr);
			c241m01a.setTotQuotaCurr(c240m01a_stdCurr);
			c241m01a.setNCreatData(CrsUtil.C241M01A_NCREATEDATA_SYS);
			if (CrsUtil
					.isNOT_null_and_NOTZeroDate(c240m01a_expectedRetrialDate)
					&& CrsUtil.isNull_or_ZeroDate(c241m01a.getRetrialDate())) {
				c241m01a.setRetrialDate(c240m01a_expectedRetrialDate);
			}
			boolean run_set_staff = true;
			if (CrsUtil.isCaseN(c241m01a)) {
				// 一般
			} else if (CrsUtil.isCaseG_Parent(c241m01a)) {
				// 團貸(母)
				run_set_staff = false;
			} else if (CrsUtil.isCaseG_Detail(c241m01a)) {
				// 團貸(子)
			} else if (CrsUtil.isCaseS(c241m01a)) {
				// 防杜代辦
			} else if (CrsUtil.isCaseH(c241m01a)) {
				// 專案信貸
			}

			if (run_set_staff) {
				c241m01a.setStaff(retrialService.findstaff(crsVo,
						c241m01a.getCustId(), c241m01a.getDupNo()) ? "Y" : "N");
			} else {
				c241m01a.setStaff("N");
			}

			// 重新引進帳務資料
			// 因為在重引時,會用到一些C241M01A的資料去計算(EX:TotBalCurr)
			// 所以這段程式,要在 C241M01A 填入共用欄位值之後,再執行
			boolean isLnExist = retrialService.importLNtoC241M01B(crsVo,
					c240m01a_branchId, c241m01a, c241m01bs, delC241M01BList,
					userId, latest_aprdcdate);
			if (isLnExist) {
				if (Util.equals("Y", c241m01a.getRealCkFg())) {
					/*
					 * 若該次不為 土建融實地覆審 由 user 自行 convert 成一般格式
					 */
					c241m01a.setDocFmt(CrsUtil.DOCFMT_土建融實地覆審);
				}
				c241m01as.add(c241m01a);
			}
		}
		int c241m01as_size = c241m01as.size();
		int current_idx = 0;
		_debug(debugMsg + ",produce_commonByC241M01A, size from ["
				+ c241m01a_list.size() + "] to [" + c241m01as_size + "]");

		retrialService.importLNtoC241M01B_sumGrpParent(c241m01as);

		for (C241M01A c241m01a : c241m01as) {
			_debug(debugMsg + ",produce_commonByC241M01A[" + (current_idx)
					+ "/" + c241m01as_size + "]-1");
			// 引進覆審項目
			retrialService.importRetrialItemToC241M01C(c241m01a, c241m01cs,
					null);
			_debug(debugMsg + ",produce_commonByC241M01A[" + (current_idx)
					+ "/" + c241m01as_size + "]-2");
			// ---
			// 覆審授權檔-C241A01A
			C241A01A c241a01a = new C241A01A();
			{
				c241a01a.setMainId(c241m01a.getMainId());
				// 1.編製/移送
				c241a01a.setOwnUnit(unitNo);
				c241a01a.setOwner(userId);
				c241a01a.setAuthType("1");
				c241a01a.setAuthUnit(unitNo);
			}

			c241a01as.add(c241a01a);
			// ---
			// 覆審關聯檔-C240M01B
			C240M01B c240m01b = new C240M01B();
			c240m01b.setMainId(c240m01a_mainId);
			c240m01b.setRefMainId(c241m01a.getMainId());
			c240m01bs.add(c240m01b);
			// ---
			current_idx++;
		}

		if (Util.equals(RetrialDocStatusEnum.已產生覆審名單報告檔.getCode(),
				c240m01a_docStatis)) {
			c241a01as.addAll(retrialService.auth_C241A01A_ToBr(unitNo, userId,
					c241m01as));
		}
	}

	private void replace_c241m01a_with_elf491(C241M01A c241m01a, ELF491 elf491) {
		c241m01a.setLastRetrialDate(CrsUtil.isNOT_null_and_NOTZeroDate(elf491
				.getElf491_lrdate()) ? elf491.getElf491_lrdate() : null);
		c241m01a.setLastRealDt(CrsUtil.isNOT_null_and_NOTZeroDate(elf491
				.getElf491_lastRealDt()) ? elf491.getElf491_lastRealDt() : null);
		c241m01a.setShouldReviewDate(elf491.getElf491_crdate());
		c241m01a.setRetrialKind(Util.trim(elf491.getElf491_remomo()));
		c241m01a.setSpecifyCycle(elf491.getElf491_maincust());
		c241m01a.setSpecifyCycle(null);// 每次決定

		CrsUtil.setR98Data(c241m01a, elf491.getElf491_remomo(),
				elf491.getElf491_uckddt(), elf491.getElf491_uckdline());
		_newCase_491toc241m01a(elf491.getElf491_reportkind(), c241m01a);
	}

	private List<Map<String, Object>> top5_inList(List<Map<String, Object>> list) {
		List<Map<String, Object>> r = new ArrayList<Map<String, Object>>();
		int tAcount = Math.max(5, list.size() * 5 / 100);
		for (Map<String, Object> item : list) {
			if (r.size() >= tAcount) {
				break;
			}
			r.add(item);
		}
		return r;
	}

	/**
	 * 在 ELF491 轉 C241M01A 前 先把 ELF491 原本符合條件的資料, 抓出來review, 看是否仍符合 (因 ELF491
	 * 會在1號更新, 可能到25號時, 帳務資料已有異動)
	 * 
	 * 目的：盡可能把「不用覆審」的 ELF491 給排除掉 . 將
	 * 
	 * @param brNo
	 * @param dataEndDate
	 */
	private void gfnRecheckReViewData_unMatch_LN_R6_2_R1R2(String brNo,
			Date dataEndDate, String latest__aprdcdate) {
		Date sysDate = CapDate.parseDate(CapDate
				.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));

		/*
		 * 以 ELF491 中 crDate>'0001-01-01' 的 custId 去重抓 帳務 的資料
		 */
		Set<String> custIdSet_hasLN = misELF491Service
				.sel_custIdSet_hasLN(brNo);

		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
		// select 條件已有 ELF491_CRDATE<>'0001-01-01'
		for (ELF491 elf491 : misELF491Service.selByBranch_activeCrDate(brNo)) {
			if (!custIdSet_hasLN.contains(LMSUtil.getCustKey_len10custId(
					elf491.getElf491_custid(), elf491.getElf491_dupno()))) {
				retrialService.up491_at_bf_491toc241m01a(elf491_list,
						c241m01z_list, elf491, "UnioLn", "no LN data");
				continue;
			}

			// 因載入時過慢, 在本次底稿的資料截止月後, 先略過
			if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491.getElf491_crdate())
					&& LMSUtil.cmpDate(elf491.getElf491_crdate(), ">",
							dataEndDate)) {
				logger.trace("ignor[" + elf491.getElf491_crdate() + "]");
				continue;
			}

			if (CrsUtil.isOnly6_2(elf491.getElf491_remomo())) {
				// 將 R6_2 但逾期天數為 0 的資料，清掉 crDate
				if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_lrdate())) {
					// lrDate未曾覆審過，要覆審
				} else {
					// 若在 3 個月內曾覆審過的, 才考慮把 crDate 清掉
					Date cmpDate = CapDate.addMonth(sysDate, -3);

					if (LMSUtil
							.cmpDate(elf491.getElf491_lrdate(), ">", cmpDate)) {

						Map<String, Integer> _r6_2map = new HashMap<String, Integer>();
						for (Map<String, Object> lnMap : misLNF030Service
								.selNewLnfData(LMSUtil.getCustKey_len10custId(
										elf491.getElf491_custid(),
										elf491.getElf491_dupno()))) {
							String loanNo = Util.trim(lnMap
									.get("LNF030_LOAN_NO"));
							if (Util.isNotEmpty(loanNo)) {
								_r6_2map.put(loanNo, 0);
							}
						}

						if (Util.isNotEmpty(latest__aprdcdate)) {
							for (String loanNo : _r6_2map.keySet()) {

								Map<String, Object> row = misDailyctlService
										.selByBrnoLoanNoPrdcdate(brNo, loanNo,
												latest__aprdcdate);
								String _date = Util.trim(MapUtils.getString(
										row, "AUPAMTDUE"));// '本金應還日

								if (Util.isNotEmpty(_date)
										&& Util.notEquals("000/00/00", _date)) {
									int diffDayes = CapDate.calculateDays(
											sysDate, TWNDate.valueOf(_date));
									_r6_2map.put(loanNo, diffDayes);
								}
							}
						}

						boolean all_zeroDays = true;
						for (String loanNo : _r6_2map.keySet()) {
							if (Util.parseInt(_r6_2map.get(loanNo)) > 0) {
								all_zeroDays = false;
								break;
							}
						}

						if (all_zeroDays) {
							retrialService.up491_at_bf_491toc241m01a(
									elf491_list, c241m01z_list, elf491,
									"no6_2",
									"6_2 days:0,loanNo=" + _r6_2map.keySet());
							continue;
						}
					}
				}
			}

			// TODO 只在 很有把握時,才剔除ELF491
			boolean checkFlag = false;
			if (checkFlag) {
				if (CrsUtil.isInR1R2R4_noOthers(elf491.getElf491_remomo())) {
					// 至少有一個 R1 R2 R4, 且無其他的 Rule, 才跑驗證
					CrsRuleVO crsRuleVO = new CrsRuleVO(sysDate,
							elf491.getElf491_branch(),
							elf491.getElf491_custid(), elf491.getElf491_dupno());
					String _rule_no_new = "";
					String _rule_no = "";
					if (Util.equals(CrsUtil.ELF491_NEWFLAG_Y,
							elf491.getElf491_newflag())) {
						_rule_no_new = elf491.getElf491_remomo();
					} else {
						_rule_no = elf491.getElf491_remomo();
					}

					validate_R1R2R4(crsRuleVO, _rule_no_new, _rule_no);

					if (crsRuleVO.rule_decided() == false) {
						// 之前的額度,餘額符合Rule1, Rule2,但現在不符合, 把 CR_DATE 設成
						// 0001-01-01
						retrialService.up491_at_bf_491toc241m01a(elf491_list,
								c241m01z_list, elf491, "nRView",
								crsRuleVO.getC241M01Z_reasonDesc());
						continue;
					}
				}
			}
		}
		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);
	}

	private boolean _deriveFrom99Skip(C241M01A c241m01a) {
		if (CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet()
				.contains(CrsUtil.R99) == false) {
			// 不包含 99, 略過
			return true;
		}
		if (Util.notEquals("Y", c241m01a.getRetrialYN())) {
			// 不覆審的case先不重整
			return true;
		}
		if (CrsUtil.isCaseG_Parent(c241m01a)
				|| CrsUtil.isCaseG_Detail(c241m01a)
				|| CrsUtil.isCaseP(c241m01a)) {
			// 團貸(母)、(子)、價金履保:略過
			return true;
		}
		// 要重整
		return false;
	}

	@Override
	public void deriveFrom99(C240M01A meta, List<C241M01A> srclist,
			String userId) throws CapException {
		{// 當 ELF491 無資料,先 insert
			List<ELF491> elf491_list = new ArrayList<ELF491>();
			for (C241M01A c241m01a : srclist) {
				if (_deriveFrom99Skip(c241m01a)) {
					continue;
				}
				ELF491 elf491 = misELF491Service.findByPk(meta.getBranchId(),
						c241m01a.getCustId(), c241m01a.getDupNo());
				if (elf491 == null) {
					elf491 = new ELF491();
					_initELF491(elf491, meta.getBranchId(),
							c241m01a.getCustId(), c241m01a.getDupNo());
					// ---
					if (CrsUtil.isNOT_null_and_NOTZeroDate(c241m01a
							.getLastRetrialDate())) {
						elf491.setElf491_lrdate(c241m01a.getLastRetrialDate());
					}
					if (CrsUtil.isNOT_null_and_NOTZeroDate(c241m01a
							.getLastRealDt())) {
						elf491.setElf491_lastRealDt(c241m01a.getLastRealDt());
					}
				}
				retrialService.up491_at_init99(elf491_list, elf491,
						c241m01a.getShouldReviewDate(),
						c241m01a.getRetrialKind());
			}
			retrialService.upELF491_DelThenInsert(elf491_list);
		}
		// 針對ELF491重整
		gfnRecheckReViewData99(meta.getBranchId(), meta.getDataEndDate());

		// 用ELF491的值 replace C241M01A
		List<C241M01A> c241_class_as_del = new ArrayList<C241M01A>();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C241M01B> c241m01bs = new ArrayList<C241M01B>();
		List<C241M01B> delC241M01BList = new ArrayList<C241M01B>();
		for (C241M01A c241m01a : srclist) {
			if (CrsUtil.haveBeenUpload(c241m01a)) {
				// 若 99 的案件上傳過, 不更改 c241m01a
				continue;
			}
			// ---
			if (_deriveFrom99Skip(c241m01a)) {
				continue;
			}

			ELF491 elf491 = misELF491Service.findByPk(meta.getBranchId(),
					c241m01a.getCustId(), c241m01a.getDupNo());
			if (elf491 == null) {
				continue;
			}

			if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())) {
				// 在99重整後,不需覆審
				if (CrsUtil.C241M01A_NCREATEDATA_SYS.equals(c241m01a
						.getNCreatData())) {
					// 系統產生
					if (!CrsUtil.haveBeenUpload(c241m01a)) {
						c241_class_as_del.add(c241m01a);
					}
				} else {
					// user 手動加入的,不自動刪除
					// 若有需要,由 UI 執行刪除(註記)不覆審客戶
				}
			} else {
				replace_c241m01a_with_elf491(c241m01a, elf491);

				String latest_aprdcdate = misDailyctlService
						.getLatest_aprdcdate();
				retrialService.importLNtoC241M01B(new CrsVO(),
						meta.getBranchId(), c241m01a, c241m01bs,
						delC241M01BList, userId, latest_aprdcdate);
				// ---
				c241m01as.add(c241m01a);
			}
		}
		for (C241M01A c241m01a : c241_class_as_del) {
			deleteC241_classes(c241m01a);
		}
		// ---
		retrialService.save(c241m01as);
		c241m01bDao.delete(delC241M01BList);
		c241m01bDao.save(c241m01bs);
		// ------
		c240m01a_reQuantity_thisSamplingCount(meta);
		retrialService.save(meta);
	}

	@Override
	public void c240m01a_reQuantity_thisSamplingCount(C240M01A c240m01a)
			throws CapException {
		int cnt_ReQuantity = 0;
		int cnt_thisSamplingCount = 0;
		try {
			// 考量JPA寫入DB的時點
			for (C241M01A c241m01a : retrialService
					.findC241M01A_C240M01A(c240m01a.getMainId())) {
				if (Util.notEquals(UtilConstants.DEFAULT.是,
						c241m01a.getRetrialYN())) {
					continue;
				}
				// 一般戶 || 團貸(母戶) || 價金履保
				if (CrsUtil.isCaseN(c241m01a)
						|| CrsUtil.isCaseG_Parent(c241m01a)
						|| CrsUtil.isCaseP(c241m01a)
						|| CrsUtil.isCaseS(c241m01a)
						|| CrsUtil.isCaseH(c241m01a)) {
					cnt_ReQuantity++;
				}

				// 包含「單獨的8-1」 及 「3;8-1」
				if (CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet()
						.contains(CrsUtil.R8_1)) {
					cnt_thisSamplingCount++;
				}
			}
			c240m01a.setReQuantity(cnt_ReQuantity);
			c240m01a.setThisSamplingCount(cnt_thisSamplingCount);
		} catch (Exception e) {
			logger.error("mainid[" + c240m01a.getMainId() + "]"
					+ StrUtils.getStackTrace(e));
			throw new CapException(e.getMessage(), getClass());
		}
	}

	/**
	 * 在產生工作底稿時, 針對要覆審的 99 類別→重歸類到 R1,R2,...R9 參考: gfnForKind99Trans
	 */
	private void gfnRecheckReViewData99(String c240m01a_branchId,
			Date dataEndDate) {
		Date sysDate = CapDate.parseDate(CapDate
				.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		Date r1r2CmpEndDate = LMSUtil.cmpDate(sysDate, ">", dataEndDate) ? sysDate
				: dataEndDate;
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();

		for (ELF491 elf491 : misELF491Service
				.selByBranch_activeCrDate(c240m01a_branchId)) {
			if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())) {
				continue;
			}
			if (CrsUtil.parseRule(elf491.getElf491_remomo()).keySet()
					.contains(CrsUtil.R99) == false) {
				continue;
			}
			HashSet<String> deriveUnknownSet = new HashSet<String>();
			TreeMap<String, String> deriveMap = new TreeMap<String, String>();
			Date baseDate = CapDate.parseDate(CapDate.ZERO_DATE);

			CrsVO crsVO = new CrsVO();
			retrialService.fetch_LNF020_LNF030040(crsVO,
					elf491.getElf491_custid(), elf491.getElf491_dupno());
			// 判斷有哪些 LNF020 要排除
			List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
					.filter_crsVO_getLNF020(
							crsVO.getLNF020(elf491.getElf491_custid(),
									elf491.getElf491_dupno()),
							c240m01a_branchId);

			for (Map<String, Object> dataMap020 : _filter_crsVO_getLNF020) {
				String cntrNo = Util.trim(dataMap020.get("LNF020_CONTRACT"));
				Date lnf020_create_dt = (Date) (dataMap020
						.get("LNF020_CREATE_DT"));
				String lnf020_ln_br_no = Util.trim(dataMap020
						.get("LNF020_LN_BR_NO"));
				BigDecimal lnf020_fact_amt = CrsUtil.parseBigDecimal(dataMap020
						.get("LNF020_FACT_AMT"));
				String revolve = Util.trim(dataMap020.get("LNF020_REVOLVE"));

				List<Map<String, Object>> list_030_040 = crsVO
						.getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(
								cntrNo, lnf020_ln_br_no);
				for (Map<String, Object> dataMap030_040 : list_030_040) {
					String lnf030_loan_no = Util.trim(dataMap030_040
							.get("LNF030_LOAN_NO"));
					String lnType = Util.trim(dataMap030_040
							.get("LNF030_LOAN_CLASS"));
					String actcd = Util.trim(dataMap030_040
							.get("LNF040_ACT_CODE"));

					CrsUtil.trans99(deriveMap, deriveUnknownSet,
							lnf030_loan_no, lnType, actcd, revolve,
							lnf020_fact_amt);
				}

				// 應該會是最後一筆的 LN 造成99(之前的 LN 應該都跑過99反推的部分), 用最後1筆 LN 當基準日
				// 把 LNF020_CREATE_DT 當成 baseDate
				if (CrsUtil.isNull_or_ZeroDate(baseDate)) {
					baseDate = lnf020_create_dt;
				} else {
					// baseDate已大於 0001-01-01
					if (LMSUtil.cmpDate(lnf020_create_dt, ">", baseDate)) {
						baseDate = lnf020_create_dt;
					}
				}
			}
			if (deriveUnknownSet.size() > 0) {
				// 若有無法反推的狀況, 仍維持99
				continue;
			}
			if (deriveMap.size() == 0) {
				// 在經過99重新整理後, 仍無法對應到既有的 rule, 仍維持99
				continue;
			}

			if (CollectionUtils.isEmpty(_filter_crsVO_getLNF020)) {
				// 都無 LNF020,LNF030 的資料
				// 應該可把 ELF491_REMOMO 的 99清掉
				// → 先不做這件事
				// 在編製完成上傳時, 若 user 在 specifyCycle 選 0-免再審,
				// elf491_lrdate+elf491_maincust 會有值
			}

			if (CrsUtil.isNull_or_ZeroDate(baseDate)) {
				// 在 baseDate 為空時, 可能只有 LNF020,尚未產生 LNF030
				// ---> 此狀況, 不應把 ELF491 的 99 給去掉, 先保留 ELF491
				continue;
			}

			String _rule_no_new = "";
			String _rule_no = "";
			{// 把導出的 rule 加入, 把本來的 99 移除
				deriveMap.putAll(CrsUtil.parseRule(elf491.getElf491_remomo()));
				deriveMap.remove(CrsUtil.R99);

				if (Util.equals(CrsUtil.ELF491_NEWFLAG_Y,
						elf491.getElf491_newflag())) {
					_rule_no_new = CrsUtil.combineRule(deriveMap);
				} else {
					_rule_no = CrsUtil.combineRule(deriveMap);
				}
			}

			CrsRuleVO crsRuleVO = new CrsRuleVO(r1r2CmpEndDate,
					c240m01a_branchId, elf491.getElf491_custid(),
					elf491.getElf491_dupno());
			// 到這裡時,99已反推出其它的 rule, 所以 ruleSet 已排除 99
			_runRuleLogic("_batch_", crsRuleVO, _rule_no_new, _rule_no,
					CrsUtil.isNOT_null_and_NOTZeroDate(elf491
							.getElf491_lrdate()) ? "Y" : "N");

			boolean is_only_8_1 = false;
			String param_elf491_reportkind = Util.trim(crsRuleVO
					.getElf491_reportkind());
			if (deriveMap.size() == 1 && deriveMap.containsKey(CrsUtil.R8_1)) {
				is_only_8_1 = true;
			}

			if (crsRuleVO.rule_decided() == false
					&& Util.isEmpty(param_elf491_reportkind)) {
				if (is_only_8_1) {
					// 純 8-1 時，因為無 CrsR8_1_O_RuleLogic
					// 推算出的 crsRuleVO.rule_decided() == false
					if (Util.equals(CrsUtil.R8_1, _rule_no)
							&& Util.isEmpty(_rule_no_new)) {
						param_elf491_reportkind = CrsUtil.OLD_RULE;
					} else if (Util.isEmpty(_rule_no)
							&& Util.equals(CrsUtil.R8_1, _rule_no_new)) {
						param_elf491_reportkind = CrsUtil.NEW_RULE;
					}
				}
			}

			if (crsRuleVO.rule_decided() || is_only_8_1) {
				// 因為用 LNF020_CREATE_DT 當成 baseDate, 可能 LNF020_CREATE_DT
				// 是很久之前的日期
				// 用 baseDate+crsRuleVO.getAddedMonth() 也可能<系統日
				Date decideCrDate = CapDate.addMonth(baseDate,
						crsRuleVO.getAddedMonth());

				Date choseDate = elf491.getElf491_crdate();
				if (LMSUtil.cmpDate(decideCrDate, "<",
						CapDate.getCurrentTimestamp())) {
					// 仍用原本 elf491.crDate
				} else {
					if (LMSUtil.cmpDate(decideCrDate, "<=",
							elf491.getElf491_crdate())) {
						choseDate = decideCrDate;
					}
				}
				retrialService.up491_at_deriveFrom99(elf491_list,
						c241m01z_list, elf491, param_elf491_reportkind,
						choseDate, _rule_no, _rule_no_new,
						crsRuleVO.getC241M01Z_reasonDesc());
			} else {
				// 只在 很有把握時,才剔除ELF491
				boolean checkFlag = false;
				if (checkFlag) {
					// 都不符合, 把 CRDATE清成0001-01-01
					retrialService.up491_at_deriveFrom99(elf491_list,
							c241m01z_list, elf491, CrsUtil.OLD_RULE,
							CapDate.parseDate(CapDate.ZERO_DATE), _rule_no,
							_rule_no_new, crsRuleVO.getC241M01Z_reasonDesc());
				}
			}
		}
		// ---
		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);
	}

	@Override
	public void deleteC241_classes(C241M01A c241m01a) {
		if (c241m01a != null) {
			C240M01B c240m01b = c241m01a.getC240m01b();
			if (c240m01b != null) {
				c240m01bDao.delete(c240m01b);
			}
			// ---
			c241a01adao.delete(c241a01adao.findByMainId(c241m01a.getMainId()));
			c241m01bDao.delete(c241m01bDao.findByMainId(c241m01a.getMainId()));
			c241m01cDao.delete(c241m01cDao.findByMainId(c241m01a.getMainId()));
			c241m01eDao.delete(c241m01eDao.findByMainId(c241m01a.getMainId()));
			c241m01aDao.delete(c241m01a);
		}
	}

	@Override
	public void update490(String branch, Date sysMonth_1st) {
		C240M01Z c240m01z = c240m01zDao.findByUniqueKey(sysMonth_1st, branch);
		if (c240m01z != null) {
			// 已跑過 ELF490 轉 ELF491
			return;
		}

		// lastMonth=ALOAN帳務資料日期
		String lastMonth = CapDate.addMonth(
				CapDate.formatDate(sysMonth_1st, CapDate.DEFAULT_DATE_FORMAT),
				-1);

		// elf490_data_ym 的格式: 009912表示0099年12月
		String elf490_data_ym_beg = CrsUtil.elf490YM_from_adDate(lastMonth);
		String elf490_data_ym_end = elf490_data_ym_beg;

		logger.trace("[" + branch + "][" + elf490_data_ym_beg + "~"
				+ elf490_data_ym_end + "]");

		// ============================
		// 執行 elf490_to_elf491
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();

 		for (ELF490 elf490 : misELF490Service.findByDataymBrno(
				elf490_data_ym_beg, elf490_data_ym_end, branch)) {

			// 1.排除免覆審、抽樣覆審
			// 2.排除已覆審過的R1 CASE
			if (Util.isEmpty(Util.trim(elf490.getElf490_rule_no_new()))) {
				TreeMap<String, String> ruleMap = CrsUtil.parseRule(elf490
						.getElf490_rule_no());
				// 1.排除免覆審、抽樣覆審
				for (String ruleNo : CrsUtil.RULE_ARR_R12_R13_R8_1) {
					ruleMap.remove(ruleNo);
				}
				// 若有額度，但無帳號，會出現 Rule 99
				// 純R1案件，覆審過一次，就可免覆審
				if (ruleMap.size() == 1 && ruleMap.containsKey(CrsUtil.R1)) {
					if (misELF487Service.sel_never_retrialData_only_Rule1(
							elf490.getElf490_br_no(),
							elf490.getElf490_cust_id(),
							elf490.getElf490_dup_no()).size() == 0) {
						continue;
					}
				}
			}

			/*
			 * ＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝ 不啟用這段代碼 原因1：沒有去讀擔保品的 table，可能屬擔保科目，但在
			 * 擔保品 未設定完成 原因2：只考慮了［HOUSE_FG , RMBINS_FG , HS_CRE_FG］沒有去判斷 ELF591H
			 * if(Util.equals(CrsUtil.ELF490_AREA_FG_MATCH_SAMPLE,
			 * elf490.getElf490_area_fg())){ TreeMap<String, String> ruleMap =
			 * new TreeMap<String, String>(); if(true){
			 * ruleMap.putAll(CrsUtil.parseRule(elf490.getElf490_rule_no()));
			 * ruleMap
			 * .putAll(CrsUtil.parseRule(elf490.getElf490_rule_no_new())); }
			 * 
			 * if(LMSUtil.elm_onlyLeft(ruleMap.keySet(),
			 * CrsUtil.convert_arr_to_set(CrsUtil.RULE_ARR_R12_R13_R8_1,
			 * CrsUtil.RULE_ARR_R1_R2)).size()>0){ //含其它需覆審的 rule }else{ //純R1,
			 * R2 有X個額度，其中X個已覆審過的案件 => 都覆審過, 不列入{必須覆審}, 由經辦指辦覆審 //純R1, R2
			 * 有X個額度，其中Y個已覆審過的案件 => 有任一未覆審過, 且首撥日自 2020-05-22 後, 且金額低於門檻,
			 * 不列入{必須覆審} //若本來{未達覆審門檻}，因新額度，達到{覆審門檻} => area_fg 應等於Y
			 * if(Util.equals(CrsUtil.ELF490_HOUSE_FG___MATCH,
			 * elf490.getElf490_house_fg()) ||
			 * Util.equals(CrsUtil.ELF490_RMBINS_FG___MATCH,
			 * elf490.getElf490_rmbins_fg()) ||
			 * Util.equals(CrsUtil.ELF490_HS_CRE_FG___MATCH,
			 * elf490.getElf490_hs_cre_fg())){ //因為已有{特殊註記} }else{ continue; } }
			 * }else{ //金額達{已覆審門檻}，需再進一步判斷(可能 有效額度 有變少) }
			 */

			Date elf490Date = CrsUtil.elf490YM_to_adDate_d(elf490
					.getElf490_data_ym());
			Date sysDate = CapDate.parseDate(CapDate
					.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
			Date r1r2CmpEndDate = LMSUtil.cmpDate(sysDate, ">", elf490Date) ? sysDate
					: elf490Date;
			// 可能2月過農曆年. 2/1 未手動執行, 而在 2/6 執行
			CrsRuleVO crsRuleVO = new CrsRuleVO(r1r2CmpEndDate,
					elf490.getElf490_br_no(), elf490.getElf490_cust_id(),
					elf490.getElf490_dup_no());

			elf490_to_elf491(elf490Date, elf490.getElf490_estate_amt(),
					elf490.getElf490_rule_no(), elf490.getElf490_rule_no_new(),
					crsRuleVO, elf491_list, c241m01z_list);
		}
		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);

		// ============================
		// 比照 notes 的程式 fnInsterNewData, 上傳ELF498
		// 當月有新做增額新案CASE寫入ELF498
		fnInsterNewData(branch, elf490_data_ym_beg, elf490_data_ym_end);

		// 更新finishTime[PK 的 key 固定是 yyyy-MM-01 每月1號]

		if (c240m01z == null) {
			c240m01z = new C240M01Z();
			c240m01z.setBranchId(branch);
			c240m01z.setDataDate(sysMonth_1st);
		}
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		c240m01z.setFinishTime(nowTS);
		c240m01zDao.save(c240m01z);

		// 準備批次SLMS-00021資料
		chkR1R2R4(branch, "", TWNDate.toAD(nowTS));
	}

	private boolean isSet_a_all_in_Set_b(Set<String> a, Set<String> b) {
		int cnt = a.size();
		int matchCnt = 0;

		for (String str : a) {
			if (b.contains(str)) {
				matchCnt++;
			}
		}
		return (cnt == matchCnt);
	}

	@Override
	public void up_to_mis(C241M01A c241m01a) {
		if (CrsUtil.isCaseP(c241m01a)) {
			upCase_P_to_mis(c241m01a);
		} else if (CrsUtil.isCaseS(c241m01a)) {
			upCase_S_to_mis(c241m01a);
		} else {
			upCase_N_G_H_to_mis(c241m01a);
		}
	}

	@Override
	public void chkR1R2R4_copy_to_c241m01z(ELF491 elf491, C241M01Z o) {
		o.setBranch(elf491.getElf491_branch());
		o.setCustId(elf491.getElf491_custid());
		o.setDupNo(elf491.getElf491_dupno());
		// ---
		o.setBf_crDate(elf491.getElf491_crdate());
		o.setBf_remomo(elf491.getElf491_remomo());
		o.setBf_updater(elf491.getElf491_updater());
		o.setBf_time(elf491.getElf491_tmestamp());
		o.setBf_reportKind(elf491.getElf491_reportkind());
		o.setBf_newFlag(elf491.getElf491_newflag());
		// ---
		o.setReason("U");
		o.setReasonDesc("待檢核R1");
	}

	/**
	 * 更新C241M01Z
	 * 
	 * @param elf491
	 * @param o
	 */
	private void _to_c241m01z_elf491_bf(ELF491 elf491, C241M01Z o) {
		o.setBf_crDate(elf491.getElf491_crdate());
		o.setBf_remomo(elf491.getElf491_remomo());
		o.setBf_updater(elf491.getElf491_updater());
		o.setBf_time(elf491.getElf491_tmestamp());
		o.setBf_reportKind(elf491.getElf491_reportkind());
		o.setBf_newFlag(elf491.getElf491_newflag());
	}

	private void _to_c241m01z_elf491_af(ELF491 elf491, C241M01Z o,
			String reason, String reasonDesc) {
		o.setBranch(elf491.getElf491_branch());
		o.setCustId(elf491.getElf491_custid());
		o.setDupNo(elf491.getElf491_dupno());
		// ---
		o.setAf_crDate(elf491.getElf491_crdate());
		o.setAf_remomo(elf491.getElf491_remomo());
		o.setAf_updater(elf491.getElf491_updater());
		o.setAf_time(elf491.getElf491_tmestamp());
		o.setAf_reportKind(elf491.getElf491_reportkind());
		o.setAf_newFlag(elf491.getElf491_newflag());
		// ---
		o.setReason(reason);
		o.setReasonDesc(reasonDesc);
	}

	@Override
	/**
	 * 因未讀取擔保品資料無法判斷R1 R2 R4之金額門檻與覆審規則
	 * 將疑似資料佔存至C241M01Z
	 * 做為SLMS-00021 INPUT資料
	 * SLMS-00017 ELF491為應覆審資料=>SLMS-00021判斷門檻與覆審規則
	 * EX:SLMS-00017有效額度3001萬(要覆審)->SLMS-00021有效額度2999萬(免覆審) => 更新ELF491
	 */
	public void chkR1R2R4(String brNo, String custId, String d) {
		List<ELF491> list = misELF491Service.chkR1R2R4(brNo, custId, d);
		if (CollectionUtils.isEmpty(list)) {
			return;
		}

		for (ELF491 elf491 : list) {
			if (LMSUtil.elm_onlyLeft(
					CrsUtil.parseRule(elf491.getElf491_remomo()).keySet(),
					CrsUtil.convert_arr_to_set(CrsUtil.RULE_ARR_R12_R13_R8_1,
							CrsUtil.RULE_ARR_R1_R2_R4)).size() > 0) {
				// 包含非 R1, R2 的 Rule
				continue;
			}
			C241M01Z o = new C241M01Z();
			// SET Reason = "U" 待檢核
			chkR1R2R4_copy_to_c241m01z(elf491, o);
			// ---
			retrialService.save(o);
		}
	}

	@Override
	public void validate_R1R2R4(CrsRuleVO crsRuleVO, String rule_no_new,
			String rule_no) {
		String hasLrDate = "";
		if (true) {
			// 此處是判斷, 是否符合R1,R2, R4的金額規則
			// 不影響要在 [3個月 or 6個月]後覆審
			// 先固定塞Y => 讓程式不必再去連MIS DB, 減少不必要的判斷
			hasLrDate = "Y";
		}
		_runRuleLogic("_produce_", crsRuleVO, rule_no_new, rule_no, hasLrDate);
	}

	@Override
	public void test_calc(String flag, CrsRuleVO crsRuleVO, String rule_no_new,
			String rule_no) {
		String hasLrDate = "";
		_runRuleLogic(flag, crsRuleVO, rule_no_new, rule_no, hasLrDate);
	}

	private C240M01C rule95_1_baseCnt(String ruleNo, String brNo,
			int rule95_allCnt, Date elf491c_latestDate_Base95_1,
			int rule95_1_baseCnt) {
		C240M01C result = new C240M01C();
		result.setRuleNo(ruleNo);
		result.setAllBegDate(elf491c_latestDate_Base95_1);
		result.setAllEndDate(elf491c_latestDate_Base95_1);
		result.setAllCnt(rule95_1_baseCnt);
		if (true) {
			result.setActiveCnt(rule95_1_baseCnt);
		}
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		result.setDoneBegDate(null);
		result.setDoneEndDate(null);
		result.setDoneCnt(null);
		// ~~~~~~~
		result.setDoneRate(null);
		return result;
	}

	@Override
	public C240M01C rule95_1_summary(String ruleNo, String brNo,
			int rule95_allCnt, int rule95_1_baseCnt, String[] donePeriod_arr) {
		C240M01C result = new C240M01C();
		result.setRuleNo(ruleNo);
		result.setAllBegDate(null); // Rule 95 的來源，是授審處發文決定（不是去抓某一段起迄時間）
		result.setAllEndDate(null);
		result.setAllCnt(rule95_allCnt);
		if (true) {
			result.setActiveCnt(rule95_allCnt == 0 ? 0 : misELF491CService
					.countRule95_active(brNo));
		}
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		String doneBegDate = donePeriod_arr[0];
		String doneEndDate = donePeriod_arr[1];

		result.setDoneBegDate(CapDate.parseDate(doneBegDate));
		result.setDoneEndDate(CapDate.parseDate(doneEndDate));
		result.setDoneCnt(rule95_allCnt == 0 ? 0 : misELF491CService
				.countByBrNoRuleNoLrDateBegEnd(brNo, result.getRuleNo(),
						doneBegDate, doneEndDate));
		// ~~~~~~~
		Integer doneRate = CrsUtil.calc_c240m01c_doneRate(rule95_1_baseCnt,
				result.getDoneCnt());
		result.setDoneRate(doneRate == null ? BigDecimal.ZERO : new BigDecimal(
				doneRate));
		return result;
	}

	@Override
	public C240M01C rule_R1R2S(String ruleNo, String brNo, Date retrial_dt) {
		C240M01C result = new C240M01C();
		result.setRuleNo(ruleNo);

		Date nowYear_1st = CapDate.parseDate(Util.getLeftStr(
				Util.trim(TWNDate.toAD(retrial_dt)), 4)
				+ "-01-01");
		String base_year = Util.getLeftStr(
				TWNDate.toAD(CapDate.shiftDays(nowYear_1st, -1)), 4);
		int r1r2S_allCnt = 0;
		if (true) {
			ELF491 elf491CustId_AAAAAAAAAA___dupNo_A = misELF491Service
					.selWithCustId_AAAAAAAAAA___dupNo_A(brNo);
			if (elf491CustId_AAAAAAAAAA___dupNo_A != null) {
				String remomo = Util.trim(elf491CustId_AAAAAAAAAA___dupNo_A
						.getElf491_remomo());

				r1r2S_allCnt = CrsUtil.getR8_1_total(remomo);
			}
		}
		result.setAllBegDate(CapDate.parseDate(base_year + "-12-31"));
		result.setAllEndDate(CapDate.parseDate(base_year + "-12-31"));
		result.setAllCnt(r1r2S_allCnt);

		/*
		 * 不動產十足擔保的抽樣 目標件數，是用「去年底」去計算 但抽樣的群體，卻是用「今年」 => 兩者的起迄不同 => 前1年底的 allCnt
		 * 為0，可能今年仍要覆審
		 */
		result.setActiveCnt(0);
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		result.setDoneBegDate(nowYear_1st);
		result.setDoneEndDate(CapDate.parseDate(Util.getLeftStr(
				Util.trim(TWNDate.toAD(nowYear_1st)), 4)
				+ "-12-31"));
		result.setDoneCnt(misELF491CService.countByBrNoRuleNoLrDateBegEnd(brNo,
				result.getRuleNo(), TWNDate.toAD(result.getDoneBegDate()),
				TWNDate.toAD(result.getDoneEndDate())));
		Integer doneRate = CrsUtil.calc_c240m01c_doneRate(result.getAllCnt(),
				result.getDoneCnt());
		result.setDoneRate(doneRate == null ? BigDecimal.ZERO : new BigDecimal(
				doneRate));
		return result;
	}

	@Override
	public C240M01C rule_R14(String ruleNo, String brNo, Date retrial_dt) {
		C240M01C result = new C240M01C();
		result.setRuleNo(ruleNo);

		Date nowYear_1st = CapDate.parseDate(Util.getLeftStr(
				Util.trim(TWNDate.toAD(retrial_dt)), 4)
				+ "-01-01");
		String base_year = Util.getLeftStr(
				TWNDate.toAD(CapDate.shiftDays(nowYear_1st, -1)), 4);
		int r1r2S_allCnt = 0;
		if (true) {
			ELF491 elf491CustId_DDDDDDDDDD___dupNo_D = misELF491Service
					.selWithCustId_DDDDDDDDDD___dupNo_D(brNo);
			if (elf491CustId_DDDDDDDDDD___dupNo_D != null) {
				String remomo = Util.trim(elf491CustId_DDDDDDDDDD___dupNo_D
						.getElf491_remomo());

				r1r2S_allCnt = CrsUtil.getR8_1_total(remomo);
			}
		}
		result.setAllBegDate(CapDate.parseDate(base_year + "-12-31"));
		result.setAllEndDate(CapDate.parseDate(base_year + "-12-31"));
		result.setAllCnt(r1r2S_allCnt);

		/*
		 * 不動產十足擔保的抽樣 目標件數，是用「去年底」去計算 但抽樣的群體，卻是用「今年」 => 兩者的起迄不同 => 前1年底的 allCnt
		 * 為0，可能今年仍要覆審
		 */
		result.setActiveCnt(0);
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		result.setDoneBegDate(nowYear_1st);
		result.setDoneEndDate(CapDate.parseDate(Util.getLeftStr(
				Util.trim(TWNDate.toAD(nowYear_1st)), 4)
				+ "-12-31"));
		result.setDoneCnt(misELF491CService.countByBrNoRuleNoLrDateBegEnd(brNo,
				result.getRuleNo(), TWNDate.toAD(result.getDoneBegDate()),
				TWNDate.toAD(result.getDoneEndDate())));
		Integer doneRate = CrsUtil.calc_c240m01c_doneRate(result.getAllCnt(),
				result.getDoneCnt());
		result.setDoneRate(doneRate == null ? BigDecimal.ZERO : new BigDecimal(
				doneRate));
		return result;
	}

	@Override
	public C240M01C rule_projectCreditLoan_summary(String ruleNo, String brNo,
			Date retrial_dt) {
		Date loanDate_beg = CrsUtil.get_R13_begDate(retrial_dt);
		Date loanDate_end = retrial_dt;

		C240M01C result = new C240M01C();
		result.setRuleNo(ruleNo);
		result.setAllBegDate(loanDate_beg);
		result.setAllEndDate(loanDate_end);
		int allCnt = misELF491CService.countRule_projectCreditLoan_all(brNo,
				TWNDate.toAD(result.getAllBegDate()),
				TWNDate.toAD(result.getAllEndDate()));
		result.setAllCnt(allCnt);
		if (true) {
			result.setActiveCnt(allCnt == 0 ? 0 : misELF491CService
					.countRule_projectCreditLoan_active(brNo,
							TWNDate.toAD(result.getAllBegDate()),
							TWNDate.toAD(result.getAllEndDate())));
		}
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		result.setDoneBegDate(loanDate_beg);
		result.setDoneEndDate(loanDate_end);
		/*
		 * 依指定的{覆審起日~覆審迄日}查詢{已覆審完成件數} 若上傳的時間=D, 但 c241m01a.retrialDate 是 D +
		 * N【實際覆審日 = 未來日期】 ⇒ 此處的查詢結果，就不會包含到它
		 */
		result.setDoneCnt(misELF491CService.countByBrNoRuleNoLrDateBegEnd(brNo,
				result.getRuleNo(), TWNDate.toAD(result.getDoneBegDate()),
				TWNDate.toAD(result.getDoneEndDate())));
		Integer doneRate = CrsUtil.calc_c240m01c_doneRate(result.getAllCnt(),
				result.getDoneCnt());
		result.setDoneRate(doneRate == null ? BigDecimal.ZERO : new BigDecimal(
				doneRate));
		return result;
	}

	private void upCase_P_to_mis(C241M01A c241m01a) {
		List<ELF486> elf486_list = new ArrayList<ELF486>();

		List<C241M01B> c241m01b_list = retrialService
				.findC241M01B_YnReviewY_c241m01a(c241m01a);

		HashSet<String> up486Set = new HashSet<String>();
		for (C241M01B c241m01b : c241m01b_list) {
			// LNF034_LC_NO 為CHAR(20),但目前實際只有 14 碼
			String elf486_lc_no = Util.trim(c241m01b.getLcNo());

			ELF486 elf486 = misELF486Service
					.findByPk(c241m01a.getOwnBrId(), elf486_lc_no,
							c241m01b.getQuotaNo(), c241m01a.getOverdueP());
			if (elf486 == null) {
				elf486 = new ELF486();
				elf486.setElf486_branch(c241m01a.getOwnBrId());
				elf486.setElf486_lc_no(elf486_lc_no);
				elf486.setElf486_cntrno(c241m01b.getQuotaNo());
				elf486.setElf486_overdue(c241m01a.getOverdueP());
			}
			elf486.setElf486_s_date(c241m01b.getUseFDate());
			elf486.setElf486_e_date(c241m01b.getUseEDate());
			elf486.setElf486_bal_amt(c241m01b.getBalAmt());
			elf486.setElf486_icmtype(c241m01a.getRetrialDate());
			elf486.setElf486_com_id(c241m01a.getComId());
			elf486.setElf486_com_dup(c241m01a.getComDup());
			elf486.setElf486_com_name(Util.trim(c241m01a.getComName()));
			elf486.setElf486_seller_id(c241m01a.getCustId());
			elf486.setElf486_seller_dup(c241m01a.getDupNo());
			elf486.setElf486_seller_name(Util.trim(c241m01a.getCustName()));
			elf486.setElf486_buyer_id(c241m01a.getBuyerId());
			elf486.setElf486_buyer_dup(c241m01a.getBuyerDup());
			elf486.setElf486_buyer_name(Util.trim(c241m01a.getBuyerName()));

			elf486.setElf486_updater(Util.trim(c241m01a.getApprover()));
			elf486.setElf486_tmestamp(CapDate.getCurrentTimestamp());

			// ---
			if (up486Set.contains(_elf486key(elf486))) {
				continue;
			} else {
				up486Set.add(_elf486key(elf486));
			}
			// ---
			elf486_list.add(elf486);
		}

		retrialService.upELF486_DelThenInsert(elf486_list);
	}

	private String _elf486key(ELF486 elf486) {
		ArrayList<String> r = new ArrayList<String>();
		r.add(Util.trim(elf486.getElf486_branch()));
		r.add(Util.trim(elf486.getElf486_lc_no()));
		r.add(Util.trim(elf486.getElf486_cntrno()));
		r.add(Util.trim(elf486.getElf486_overdue()));
		return StringUtils.join(r, "|");
	}

	private void upCase_S_to_mis(C241M01A c241m01a) {
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		List<ELF491B> elf491b_list = new ArrayList<ELF491B>();
		if (true) {
			ELF491B elf491b = new ELF491B();
			elf491b.setElf491b_unid(c241m01a.getMainId());
			elf491b.setElf491b_branch(c241m01a.getOwnBrId());
			elf491b.setElf491b_custid(c241m01a.getCustId());
			elf491b.setElf491b_dupno(c241m01a.getDupNo());
			elf491b.setElf491b_lrdate(c241m01a.getRetrialDate());
			elf491b.setElf491b_pa_ym(c241m01a.getPa_ym());
			elf491b.setElf491b_pa_trg(c241m01a.getPa_trg());
			elf491b.setElf491b_conflag(Util.trim(c241m01a.getConFlag()));
			elf491b.setElf491b_updater(Util.trim(c241m01a.getApprover()));
			elf491b.setElf491b_tmestamp(nowTS);
			// ========
			elf491b_list.add(elf491b);
		}

		List<ELF498B> elf498b_list = new ArrayList<ELF498B>();
		List<C241M01F> c241m01f_list = retrialService
				.findC241M01F_c241m01a(c241m01a);
		for (C241M01F c241m01f : c241m01f_list) {
			ELF498B elf498b = new ELF498B();
			elf498b.setElf498b_unid(c241m01a.getMainId());
			elf498b.setElf498b_rptid(c241m01f.getCaseMainId());
			elf498b.setElf498b_rel_type(c241m01f.getRel_type());
			elf498b.setElf498b_staffno(c241m01f.getStaffNo());
			elf498b.setElf498b_updater(Util.trim(c241m01a.getApprover()));
			elf498b.setElf498b_tmestamp(nowTS);
			// ========
			elf498b_list.add(elf498b);

		}
		retrialService.upELF491B_DelThenInsert(elf491b_list);
		if (true) {
			retrialService.upELF498B_Del_by_unid(c241m01a.getMainId());
			retrialService.upELF498B_DelThenInsert(elf498b_list);
		}
	}

	private void upMis_set_elf491_lastRealDt(ELF491 elf491, C241M01A c241m01a) {
		if (CrsUtil.docKindN_since_R11(c241m01a)) {
			if (Util.equals(CrsUtil.DOCFMT_土建融實地覆審, c241m01a.getDocFmt())) {
				elf491.setElf491_lastRealDt(c241m01a.getRetrialDate());
			} else {
				/*
				 * 若 2月時, 同時產生3月名單(elf491_lastRealDt=2016-08),
				 * 4月名單(elf491_lastRealDt=2016-08) 結果3月選實地覆審(已 update
				 * elf491_lastRealDt=2017-03) 在4月改一般覆審, 在update時，不要抓keep住的
				 * c241m01a.lastRealDt --------------- 當同一份覆審報告表，不斷切換 A→B 或B→A ●
				 * 在 A→B 時，走上面的判斷 ● 在 B→A 時
				 */
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
						.getElf491_lrdate())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_lastRealDt())
						&& LMSUtil.cmpDate(elf491.getElf491_lrdate(), "==",
								elf491.getElf491_lastRealDt())) {
					/*
					 * 或抓 elf492 應該不太好，因為 elf492 已經是之前上傳過的資料(B→A)的B
					 * 若抓倒數第2筆......
					 */
					elf491.setElf491_lastRealDt(c241m01a.getLastRealDt());
				}
			}
		}
	}

	private void upCase_N_G_H_to_mis(C241M01A c241m01a) {
		Date sysDate = CapDate.parseDate(CapDate
				.getCurrentDate(CapDate.DEFAULT_DATE_FORMAT));
		// ---
		// 上傳 MIS
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<ELF491C> elf491c_list = new ArrayList<ELF491C>();
		List<ELF492> elf492_list = new ArrayList<ELF492>();
		List<ELF498> elf498_list = new ArrayList<ELF498>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
		boolean updateELF491_R1R2S = false;
		boolean updateELF491_R14 = false;

		if (CrsUtil.isCaseN(c241m01a) || CrsUtil.isCaseG_Detail(c241m01a)
				|| CrsUtil.isCaseH(c241m01a)) {
			List<C241M01B> c241m01b_list = retrialService
					.findC241M01B_YnReviewY_c241m01a(c241m01a);
			HashSet<String> quotaNoSet = new HashSet<String>();
			{
				for (C241M01B c241m01b : c241m01b_list) {
					quotaNoSet.add(Util.trim(c241m01b.getQuotaNo()));
				}
			}

			ELF491 elf491 = misELF491Service.findByPk(c241m01a.getOwnBrId(),
					c241m01a.getCustId(), c241m01a.getDupNo());
			String memo_elf_uckdline = "";
			ELF491 elf491_8_1 = null;
			{
				boolean overwrite491 = true;

				TreeMap<String, String> localRule = CrsUtil.parseRule(c241m01a
						.getRetrialKind());
				if (elf491 == null) {
					elf491 = new ELF491();
					_initELF491(elf491, c241m01a.getOwnBrId(),
							c241m01a.getCustId(), c241m01a.getDupNo());
				}

				C241M01Z c241m01z = retrialService.copyBf491(elf491);
				CrsRuleVO crsRuleVO = new CrsRuleVO(sysDate,
						elf491.getElf491_branch(), elf491.getElf491_custid(),
						elf491.getElf491_dupno());
				{// lrDate, llrDate
					if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_lrdate())) {
						// 之前都未覆審過,即使只符合抽樣的 6-1, 8-1, 也還是需要寫入 lrDate
						elf491.setElf491_llrdate(CapDate
								.parseDate(CapDate.ZERO_DATE));
						elf491.setElf491_lrdate(c241m01a.getRetrialDate());
						upMis_set_elf491_lastRealDt(elf491, c241m01a);
					} else {
						if (Util.isNotEmpty(Util.trim(elf491.getElf491_unid()))
								&& Util.equals(elf491.getElf491_unid(),
										c241m01a.getMainId())) {
							elf491.setElf491_lrdate(c241m01a.getRetrialDate());
							upMis_set_elf491_lastRealDt(elf491, c241m01a);
						} else {
							if (LMSUtil.cmpDate(elf491.getElf491_lrdate(),
									"==", c241m01a.getRetrialDate())) {
								// 重複上傳 --->
								// elf491.setElf491_lrdate(c241m01a.getRetrialDate());
								// 可能第1次的docFmt=A, 但第2次改成B
								upMis_set_elf491_lastRealDt(elf491, c241m01a);
							} else if (LMSUtil.cmpDate(
									elf491.getElf491_lrdate(), "<",
									c241m01a.getRetrialDate())) {
								// 之前的lrDate=2013年, 現在已是 2014年
								elf491.setElf491_llrdate(elf491
										.getElf491_lrdate());
								elf491.setElf491_lrdate(c241m01a
										.getRetrialDate());
								upMis_set_elf491_lastRealDt(elf491, c241m01a);
							} else if (LMSUtil.cmpDate(
									elf491.getElf491_lrdate(), ">",
									c241m01a.getRetrialDate())) {
								// 不合理
								overwrite491 = false;
							}
						}
					}
				}

				if (Util.isEmpty(elf491.getElf491_remomo())) {
					elf491.setElf491_remomo(c241m01a.getRetrialKind());
				}

				if (CrsUtil.isCaseH(c241m01a)) {
					elf491.setElf491_remomo(c241m01a.getRetrialKind());
				}

				if (CrsUtil.parseRule(elf491.getElf491_remomo()).containsKey(
						CrsUtil.R1_1___R2_1)) {
					TreeMap<String, String> ruleMap = CrsUtil.parseRule(elf491
							.getElf491_remomo());
					if (ruleMap.size() == 1) {
						int lnap_2_cnt = 0;
						int lnap_4_6_cnt = 0;
						for (C241M01B c241m01b : c241m01b_list) {
							if (Util.equals("Y", c241m01b.getYnReview())
									&& Util.trim(c241m01b.getGuaranteeKind())
											.indexOf(
													CrsUtil.C241M01B_GUARANTEEKIND_01_DESC) >= 0) {
								String lnap = CrsUtil
										.getSubjCodeFromLNF030_LOAN_NO(c241m01b
												.getLoanNo());
								if (lnap.startsWith("2")) {
									++lnap_2_cnt;
								} else if (lnap.startsWith("4")
										|| lnap.startsWith("6")) {
									++lnap_4_6_cnt;
								}
							}
						}
						if (lnap_2_cnt == 0 && lnap_4_6_cnt == 0) {
							elf491.setElf491_remomo(CrsUtil.R1); // default 用 R1
						} else {
							TreeMap<String, String> new_ruleMap = new TreeMap<String, String>();
							if (lnap_4_6_cnt > 0) {
								new_ruleMap.put(CrsUtil.R1, "");
							}
							if (lnap_2_cnt > 0) {
								new_ruleMap.put(CrsUtil.R2, "");
							}
							elf491.setElf491_remomo(CrsUtil
									.combineRule(new_ruleMap));
						}
					} else {
						ruleMap.remove(CrsUtil.R1_1___R2_1);
						elf491.setElf491_remomo(CrsUtil.combineRule(ruleMap));
					}
				}

				if (CrsUtil.parseRule(elf491.getElf491_remomo()).containsKey(
						CrsUtil.R14)) {
					TreeMap<String, String> ruleMap = CrsUtil.parseRule(elf491
							.getElf491_remomo());
					if (ruleMap.size() == 1) {
						int lnap_2_cnt = 0;
						int lnap_4_6_cnt = 0;
						for (C241M01B c241m01b : c241m01b_list) {
							if (Util.equals("Y", c241m01b.getYnReview())
									&& Util.trim(c241m01b.getGuaranteeKind())
											.indexOf(
													CrsUtil.C241M01B_GUARANTEEKIND_01_DESC) < 0) {
								String lnap = CrsUtil
										.getSubjCodeFromLNF030_LOAN_NO(c241m01b
												.getLoanNo());
								if (lnap.startsWith("2")) {
									++lnap_2_cnt;
								} else if (lnap.startsWith("4")
										|| lnap.startsWith("6")) {
									++lnap_4_6_cnt;
								}
							}
						}
						if (lnap_2_cnt == 0 && lnap_4_6_cnt == 0) {
							elf491.setElf491_remomo(CrsUtil.R3); // default 用 R1
						} else {
							TreeMap<String, String> new_ruleMap = new TreeMap<String, String>();
							if (lnap_4_6_cnt > 0) {
								new_ruleMap.put(CrsUtil.R3, "");
							}
							if (lnap_2_cnt > 0) {
								new_ruleMap.put(CrsUtil.R4, "");
							}
							elf491.setElf491_remomo(CrsUtil
									.combineRule(new_ruleMap));
						}
					} else {
						ruleMap.remove(CrsUtil.R14);
						elf491.setElf491_remomo(CrsUtil.combineRule(ruleMap));
					}
				}

				{// crDate【R98:uckdline】
					// 參考 gfnUpdateELF491BefCheck
					TreeSet<String> crDateSet = new TreeSet<String>();
					String retrialDate = CapDate.formatDate(
							c241m01a.getRetrialDate(),
							CapDate.DEFAULT_DATE_FORMAT);

					// R98 : abnormal
					// 有一些 R98 的 ELF491_UCKDLINE 為 CG, 所以要加上 remomo 包含 98
					if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
							.getElf491_uckddt())
							&& CrsUtil.parseRule(elf491.getElf491_remomo())
									.keySet().contains(CrsUtil.R98)) {
						/*
						 * int next_uckdline = 2;//由 gfnDB2Abnormal
						 * 塞入時,uckdline是1. 這裡最少由 2 往上起跳 String str_491UckdDt =
						 * CapDate.formatDate(elf491.getElf491_uckddt(),
						 * CapDate.DEFAULT_DATE_FORMAT); while(true){ //長度
						 * CHAR(2) if(next_uckdline>=99){ break; } // unkdDt +
						 * 6*unkdLine String nextCrDt =
						 * CapDate.addMonth(str_491UckdDt ,
						 * CrsUtil.r98AddedMonth_after(next_uckdline) );
						 * if(LMSUtil.cmpDate(CapDate.parseDate(nextCrDt), ">",
						 * c241m01a.getRetrialDate())){ break; }
						 * next_uckdline++; }
						 */
						memo_elf_uckdline = elf491.getElf491_uckdline();
						ELF492 elf492 = misELF492Service.selMaxUckdDt(
								c241m01a.getOwnBrId(), c241m01a.getCustId(),
								c241m01a.getDupNo());
						if (elf492 == null) {
							// .
						} else {
							if (LMSUtil.cmpDate(elf492.getElf492_uckddt(),
									"==", elf491.getElf491_uckddt())) {
								if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
										.getElf491_lrdate())) {
									if (LMSUtil.cmpDate(
											elf492.getElf492_lrdate(), "==",
											elf491.getElf491_lrdate())
											|| (Util.isNotEmpty(Util
													.trim(elf492
															.getElf492_unid())) && Util
													.equals(elf492
															.getElf492_unid(),
															c241m01a.getMainId()))) {
										// 同一份簽報書, 重複上傳MIS
										memo_elf_uckdline = String
												.valueOf(Util
														.parseInt(memo_elf_uckdline) - 1);
									} else if (LMSUtil.cmpDate(
											elf492.getElf492_lrdate(), "<",
											elf491.getElf491_lrdate())) {
										// .
									} else {
										memo_elf_uckdline = String
												.valueOf(Util
														.parseInt(memo_elf_uckdline) - 1);
									}
								}
							} else {
								// .
							}
							// 同一份簽報書, 改覆審日期, 再上傳MIS
							// 因為由{首次3個月內, 後續6個月覆審} → {首次1個月內, 後續6個月覆審}
							// 原先重算 uckdLine 的功能, 變成會不適用
						}
						// 寫入 uckdline
						elf491.setElf491_uckdline(String.valueOf(Util
								.parseInt(memo_elf_uckdline) + 1));

						// 首次異常通報後，之後每6個月覆審一次
						// 用 Min(retrialDate【+6】, uckddt+【(3+ 6*(cnt-1))】)
						crDateSet.add(CapDate.addMonth(retrialDate, 6));
					}

					// R99 : specifyCycle
					if (localRule.containsKey(CrsUtil.R99)) {
						if (Util.equals("0", c241m01a.getSpecifyCycle())) {
							// 免再審
						} else if (Util.equals("A", c241m01a.getSpecifyCycle())) {
							crDateSet.add(CapDate.addMonth(retrialDate, 12));
						} else {
							crDateSet.add(CapDate.addMonth(retrialDate, Util
									.parseInt(Util.trim(c241m01a
											.getSpecifyCycle()))));
						}
					}
					// 無擔逾期戶是否需半年再審, 當 LN 為「無擔」且「逾期」才出現
					if (Util.equals("Y", c241m01a.getOverdueYN())) {
						// 加 6 個月
						crDateSet.add(CapDate.addMonth(retrialDate, 6));
					}

					// R11 : 土建融實地覆審
					if (Util.equals(CrsUtil.DOCFMT_土建融實地覆審,
							c241m01a.getDocFmt())) {
						crDateSet.add(CapDate.addMonth(retrialDate, 12));
					} else {
						if (Util.equals("Y", c241m01a.getRealCkFg())) {
							Date c241m01a_lastRealDt = c241m01a.getLastRealDt();

							if (CrsUtil
									.isNOT_null_and_NOTZeroDate(c241m01a_lastRealDt)
									&& LMSUtil.cmp_yyyyMM(CapDate.addMonth(
											c241m01a_lastRealDt, 12), ">",
											CapDate.getCurrentTimestamp())) {

								String lastRealDt = CapDate.formatDate(
										c241m01a_lastRealDt,
										CapDate.DEFAULT_DATE_FORMAT);
								crDateSet.add(CapDate.addMonth(lastRealDt, 12));
							} else {
								if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
										.getElf491_crdate())) {
									crDateSet.add(CapDate.formatDate(
											elf491.getElf491_crdate(),
											CapDate.DEFAULT_DATE_FORMAT));
								}

								Date lnDate = CrsUtil
										.match_R11_syndType_lnDate(
												elf491.getElf491_branch(),
												c241m01b_list);
								if (lnDate != null) {
									crDateSet
											.add(CapDate.addMonth(
													CapDate.formatDate(
															lnDate,
															CapDate.DEFAULT_DATE_FORMAT),
													6));
								}
							}
						} else if (Util.equals("O", c241m01a.getRealCkFg())) {
							crDateSet.add(CapDate.addMonth(retrialDate, 12));
						}
					}

					// R1~R9
					if (true) {
						String hasLrDate = "Y"; // 本次已執行覆審
						_runRuleLogic("_endnext_", crsRuleVO, "",
								elf491.getElf491_remomo(), hasLrDate);

						if (crsRuleVO.rule_decided()
								&& crsRuleVO.getAddedMonth() > 0) {
							/*
							 * 已在 3/5 覆審完畢且上傳 可能在 3/6 人工重新上傳
							 */
							crDateSet.add(CapDate.addMonth(retrialDate,
									crsRuleVO.getAddedMonth()));
						}
					}

					elf491.setElf491_crdate(CapDate
							.parseDate(getSmallestDate(crDateSet)));
				}

				if (Util.equals("Y", c241m01a.getUp8_1())) {
					// 已累計8_1次數，並搬移8_1_flag、8_1_flag_o
				} else {
					// 先把 491 的 8_1_flag 搬到 8_1_flag_o
					elf491.setElf491_8_1_flag_o(Util.equals("Y",
							elf491.getElf491_8_1_flag()) ? "Y" : "");

					// 本次
					elf491.setElf491_8_1_flag(localRule
							.containsKey(CrsUtil.R8_1) ? "Y" : "");

					// 累加次數
					if (localRule.containsKey(CrsUtil.R8_1)) {
						elf491_8_1 = misELF491Service
								.selWithCustIdXXXX_8_1(c241m01a.getOwnBrId());
						if (true) {
							if (elf491_8_1 == null) {
								elf491_8_1 = new ELF491();

								elf491_8_1.setElf491_branch(c241m01a
										.getOwnBrId());
								elf491_8_1.setElf491_custid("XXXXXXXXXX");
								elf491_8_1.setElf491_dupno("X");
							}
							int exist_total = CrsUtil.getR8_1_total(elf491_8_1
									.getElf491_remomo());
							int exist_already = CrsUtil
									.getR8_1_already(elf491_8_1
											.getElf491_remomo());
							elf491_8_1
									.setElf491_remomo(CrsUtil
											.buildR8_1_total(exist_total)
											+ CrsUtil
													.buildR8_1_already(exist_already + 1));
							elf491_8_1.setElf491_tmestamp(CapDate
									.getCurrentTimestamp());
						}

					}
					// web e-Loan 註記已處理
					c241m01a.setUp8_1("Y");
				}

				if (localRule.containsKey(CrsUtil.R1_1___R2_1)) {
					updateELF491_R1R2S = true;
				}
				if (localRule.containsKey(CrsUtil.R14)) {
					updateELF491_R14 = true;
				}

				// web e-Loan 2碼 → 1碼
				elf491.setElf491_maincust(Util.trim(c241m01a.getSpecifyCycle()));
				elf491.setElf491_nckdflag("");
				elf491.setElf491_nckddate(CapDate.parseDate(CapDate.ZERO_DATE));
				elf491.setElf491_nckdmemo("");

				// 此刻, 都屬舊案
				elf491.setElf491_reportkind(CrsUtil.OLD_RULE);
				elf491.setElf491_newflag(CrsUtil.ELF491_NEWFLAG_N);

				elf491.setElf491_updater(Util.trim(c241m01a.getApprover()));
				elf491.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				elf491.setElf491_unid(c241m01a.getMainId());
				// ---
				if (overwrite491) {
					elf491_list.add(elf491);

					retrialService.copyAf491(c241m01z, elf491, "9",
							crsRuleVO.getC241M01Z_reasonDesc());
					c241m01z_list.add(c241m01z);
					// ---
					if (elf491_8_1 != null) {
						elf491_list.add(elf491_8_1);
					}
				} else {
					retrialService.copyAf491(
							c241m01z,
							"9",
							"not_overwrite491:elf491_lrdate("
									+ TWNDate.toAD(elf491.getElf491_lrdate())
									+ ") > c241m01a.retrialDate("
									+ TWNDate.toAD(c241m01a.getRetrialDate())
									+ ")");
					c241m01z_list.add(c241m01z);
				}
			}

			if (true) {
				Set<String> upElf491c_rule_no_set_simple = CrsUtil
						.get_upElf491c_rule_no_set_simple();
				// ~~~~~~~~~~~
				for (String chose_rule_no : LMSUtil.elm_join(
						CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet(),
						upElf491c_rule_no_set_simple)) {
					ELF491C elf491c = new ELF491C();
					elf491c.setElf491c_unid(c241m01a.getMainId());
					elf491c.setElf491c_rule_no(chose_rule_no);
					elf491c.setElf491c_branch(c241m01a.getOwnBrId());
					elf491c.setElf491c_custid(c241m01a.getCustId());
					elf491c.setElf491c_dupno(c241m01a.getDupNo());
					elf491c.setElf491c_lrdate(c241m01a.getRetrialDate());
					elf491c.setElf491c_updater(Util.trim(c241m01a.getApprover()));
					elf491c.setElf491c_tmestamp(CapDate.getCurrentTimestamp());
					// ~~~
					elf491c_list.add(elf491c);
				}
			}
			if (true) {
				if (LMSUtil.elm_join(
						CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet(),
						CrsUtil.get_upElf491c_rule_no_set_R1R2S()).size() > 0) {
					ELF491C elf491c = new ELF491C();
					elf491c.setElf491c_unid(c241m01a.getMainId());
					elf491c.setElf491c_rule_no(CrsUtil.R1R2S);
					elf491c.setElf491c_branch(c241m01a.getOwnBrId());
					elf491c.setElf491c_custid(c241m01a.getCustId());
					elf491c.setElf491c_dupno(c241m01a.getDupNo());
					elf491c.setElf491c_lrdate(c241m01a.getRetrialDate());
					elf491c.setElf491c_updater(Util.trim(c241m01a.getApprover()));
					elf491c.setElf491c_tmestamp(CapDate.getCurrentTimestamp());
					// ~~~
					elf491c_list.add(elf491c);
				}
			}
			if (true) {
				if (LMSUtil.elm_join(
						CrsUtil.parseRule(c241m01a.getRetrialKind()).keySet(),
						CrsUtil.get_upElf491c_rule_no_set_R14()).size() > 0) {
					ELF491C elf491c = new ELF491C();
					elf491c.setElf491c_unid(c241m01a.getMainId());
					elf491c.setElf491c_rule_no(CrsUtil.R14);
					elf491c.setElf491c_branch(c241m01a.getOwnBrId());
					elf491c.setElf491c_custid(c241m01a.getCustId());
					elf491c.setElf491c_dupno(c241m01a.getDupNo());
					elf491c.setElf491c_lrdate(c241m01a.getRetrialDate());
					elf491c.setElf491c_updater(Util.trim(c241m01a.getApprover()));
					elf491c.setElf491c_tmestamp(CapDate.getCurrentTimestamp());
					// ~~~
					elf491c_list.add(elf491c);
				}
			}

			/*
			 * 有可能 1 個 cntrNo 底下有多個帳號 但 ELF492 只有到 額度 而已, 未記錄到帳號 所以之前 notes
			 * 的程式,只上傳 第 1 個帳號資料而已
			 */
			HashSet<String> up492Set = new HashSet<String>();
			for (C241M01B c241m01b : c241m01b_list) {
				ELF492 elf492 = new ELF492();
				elf492.setElf492_branch(c241m01a.getOwnBrId());
				elf492.setElf492_custid(c241m01b.getCustId());
				elf492.setElf492_dupno(c241m01b.getDupNo());
				elf492.setElf492_cntrno(c241m01b.getQuotaNo());
				elf492.setElf492_lrdate(c241m01a.getRetrialDate());
				elf492.setElf492_maincust(c241m01a.getSpecifyCycle());
				elf492.setElf492_durbeg(c241m01b.getLoanFDate());
				elf492.setElf492_durend(c241m01b.getLoanEDate());
				elf492.setElf492_beg(c241m01b.getUseFDate());
				elf492.setElf492_end(c241m01b.getUseEDate());
				elf492.setElf492_quota(c241m01b.getQuotaAmt());
				elf492.setElf492_lntype(c241m01b.getLnType());
				elf492.setElf492_loantp(c241m01b.getActcd());
				elf492.setElf492_kind(c241m01a.getRetrialKind());
				elf492.setElf492_canceldt(CapDate.parseDate(CapDate.ZERO_DATE));
				elf492.setElf492_updater(Util.trim(c241m01a.getApprover()));

				// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
				elf492.setElf492_apprId(this.getApprIdForElf492(c241m01a));

				elf492.setElf492_tmestamp(CapDate.getCurrentTimestamp());
				elf492.setElf492_uckdline(memo_elf_uckdline);
				elf492.setElf492_uckddt(CrsUtil.isNull_or_ZeroDate(elf491
						.getElf491_uckddt()) ? CapDate
						.parseDate(CapDate.ZERO_DATE) : elf491
						.getElf491_uckddt());
				if (true) {
					Date elf492_lastRealDt = Util.equals(
							CrsUtil.DOCFMT_土建融實地覆審, c241m01a.getDocFmt()) ? c241m01a
							.getRetrialDate() : c241m01a.getLastRealDt();
					if (elf491 != null) {
						elf492_lastRealDt = elf491.getElf491_lastRealDt();
					}
					elf492.setElf492_lastRealDt(elf492_lastRealDt);
				}
				elf492.setElf492_realCkFg(Util.equals(CrsUtil.DOCFMT_土建融實地覆審,
						c241m01a.getDocFmt()) ? "Y" : "N");
				elf492.setElf492_unid(c241m01a.getMainId());
				// ---
				if (up492Set.contains(elf492.getElf492_cntrno())) {
					continue;
				} else {
					up492Set.add(elf492.getElf492_cntrno());
				}
				// ---
				elf492_list.add(elf492);
			}

			for (ELF498 elf498 : misELF498Service.findNotRetrial(
					c241m01a.getOwnBrId(), c241m01a.getCustId(),
					c241m01a.getDupNo())) {

				HashSet<String> s = new HashSet<String>();
				for (String raw_misCntrNo : StringUtils.split(
						Util.trim(elf498.getElf498_totalsno()),
						CrsUtil.ELF498_DELIM)) {
					String misCntrNo = Util.trim(raw_misCntrNo);
					if (Util.isNotEmpty(misCntrNo)) {
						s.add(misCntrNo);
					}
				}

				if (s.size() > 0 && isSet_a_all_in_Set_b(s, quotaNoSet)) {
					elf498.setElf498_actualdate(c241m01a.getRetrialDate());
					elf498.setElf498_updater(Util.trim(c241m01a.getApprover()));
					elf498.setElf498_tmestamp(CapDate.getCurrentTimestamp());
					// ---
					elf498_list.add(elf498);
				}
			}
		}
		if (CrsUtil.isCaseG_Parent(c241m01a)) {

			String elf492_branch = c241m01a.getOwnBrId();
			String elf492_custid = c241m01a.getCustId();
			String elf492_dupno = c241m01a.getDupNo();
			String elf492_cntrno = c241m01a.getGrpCntrNo();
			Date elf492_lrdate = c241m01a.getRetrialDate();

			ELF492 elf492 = misELF492Service.findByPk(elf492_branch,
					elf492_custid, elf492_dupno, elf492_cntrno, elf492_lrdate);
			if (elf492 == null) {
				elf492 = new ELF492();
				elf492.setElf492_branch(elf492_branch);
				elf492.setElf492_custid(elf492_custid);
				elf492.setElf492_dupno(elf492_dupno);
				elf492.setElf492_cntrno(elf492_cntrno);
				elf492.setElf492_lrdate(elf492_lrdate);
				elf492.setElf492_lastRealDt(null);
				elf492.setElf492_realCkFg(Util.equals(CrsUtil.DOCFMT_土建融實地覆審,
						c241m01a.getDocFmt()) ? "Y" : "N");
			}
			elf492.setElf492_updater(Util.trim(c241m01a.getApprover()));

			// J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
			elf492.setElf492_apprId(this.getApprIdForElf492(c241m01a));

			elf492.setElf492_tmestamp(CapDate.getCurrentTimestamp());
			elf492.setElf492_unid(c241m01a.getMainId());

			// ---
			elf492_list.add(elf492);
		}

		retrialService.up491_492_498_at_c241m01aFinish(elf491_list,
				elf492_list, elf498_list, c241m01a);

		retrialService.upELF491_DelThenInsert(elf491_list);

		if (true) {
			retrialService.upELF491C_Del_by_unid(c241m01a.getMainId());
			retrialService.upELF491C_DelThenInsert(elf491c_list);
		}

		if (true) {
			retrialService.upELF492_Del_by_unid(c241m01a.getMainId());
			retrialService.upELF492_DelThenInsert(elf492_list);
		}
		if (updateELF491_R1R2S && c241m01a.getRetrialDate() != null) {
			String current_year = Util.getLeftStr(
					Util.trim(TWNDate.toAD(CapDate.getCurrentTimestamp())), 4);
			String retrial_year = Util.getLeftStr(
					Util.trim(TWNDate.toAD(c241m01a.getRetrialDate())), 4);
			if (Util.equals(current_year, retrial_year)) {
				ELF491 elf491_R1R2S = misELF491Service
						.selWithCustId_AAAAAAAAAA___dupNo_A(c241m01a
								.getOwnBrId());

				if (elf491_R1R2S == null) {
					elf491_R1R2S = new ELF491();

					elf491_R1R2S.setElf491_branch(c241m01a.getOwnBrId());
					elf491_R1R2S.setElf491_custid("AAAAAAAAAA");
					elf491_R1R2S.setElf491_dupno("A");
				}
				int exist_total = CrsUtil.getR8_1_total(elf491_R1R2S
						.getElf491_remomo());
				int done_cnt = misELF491CService.countByBrNoRuleNoLrDateBegEnd(
						elf491_R1R2S.getElf491_branch(), CrsUtil.R1R2S,
						retrial_year + "-01-01", retrial_year + "-12-31");
				elf491_R1R2S.setElf491_remomo(CrsUtil
						.buildR8_1_total(exist_total)
						+ CrsUtil.buildR8_1_already(done_cnt));
				elf491_R1R2S.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				if (true) {
					retrialService.upELF491_DelThenInsert(elf491_R1R2S);
				}
			}
		}
		if (updateELF491_R14 && c241m01a.getRetrialDate() != null) {
			String current_year = Util.getLeftStr(
					Util.trim(TWNDate.toAD(CapDate.getCurrentTimestamp())), 4);
			String retrial_year = Util.getLeftStr(
					Util.trim(TWNDate.toAD(c241m01a.getRetrialDate())), 4);
			if (Util.equals(current_year, retrial_year)) {
				ELF491 elf491_R14 = misELF491Service
						.selWithCustId_DDDDDDDDDD___dupNo_D(c241m01a
								.getOwnBrId());

				if (elf491_R14 == null) {
					elf491_R14 = new ELF491();

					elf491_R14.setElf491_branch(c241m01a.getOwnBrId());
					elf491_R14.setElf491_custid("DDDDDDDDDD");
					elf491_R14.setElf491_dupno("D");
				}
				int exist_total = CrsUtil.getR8_1_total(elf491_R14
						.getElf491_remomo());
				int done_cnt = misELF491CService.countByBrNoRuleNoLrDateBegEnd(
						elf491_R14.getElf491_branch(), CrsUtil.R14,
						retrial_year + "-01-01", retrial_year + "-12-31");
				elf491_R14.setElf491_remomo(CrsUtil
						.buildR8_1_total(exist_total)
						+ CrsUtil.buildR8_1_already(done_cnt));
				elf491_R14.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				if (true) {
					retrialService.upELF491_DelThenInsert(elf491_R14);
				}
			}
		}
		retrialService.upELF498_DelThenInsert(elf498_list);
		retrialService.saveC241M01Z(c241m01z_list);

		// 可能寫入 up8_1
		c241m01aDao.save(c241m01a);
	}

	private String getSmallestDate(TreeSet<String> crDateSet) {
		String choseCrDate = null;

		for (String strCrDate : crDateSet) {
			if (Util.equals(CapDate.ZERO_DATE, strCrDate)) {
				continue;
			}
			if (choseCrDate == null) {
				choseCrDate = strCrDate;
				break;
			}
		}

		if (Util.isEmpty(choseCrDate)) {
			choseCrDate = CapDate.ZERO_DATE;
		}
		return choseCrDate;
	}

	private void fnInsterNewData(String brno, String elf490_data_ym_beg,
			String elf490_data_ym_end) {
		List<Map<String, Object>> list = misELF491Service.selNewCaseForELF498(
				elf490_data_ym_beg, elf490_data_ym_end, brno);
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		Date elf498_newdate = CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_beg);
		Timestamp nowTimeStamp = CapDate.getCurrentTimestamp();

		for (Map<String, Object> item : list) {
			String custId = Util.trim(item.get("ELF490_CUST_ID"));
			String dupNo = Util.trim(item.get("ELF490_DUP_NO"));
			String ruleNoNew = Util.trim(item.get("ELF490_RULE_NO_NEW"));
			String cntrNo = Util.trim(item.get("ELF488_CONTRACT"));
			String elf491_NCKDMEMO = Util.trim(item.get("ELF491_NCKDMEMO"));
			String elf491_crDate = Util.trim(item.get("ELF491_CRDATE"));

			ELF498 elf498 = misELF498Service.findByPk(brno, custId, dupNo,
					elf498_newdate);
			/*
			 * 這裡抓出來的資料, group by [ brno, custId, dupNo, elf498_newdate] 後 可能有多個
			 * cntrNo 所以 每一筆 record 都回寫 ELF498
			 */
			if (elf498 == null) {
				elf498 = new ELF498();
				// --
				elf498.setElf498_branch(brno);
				elf498.setElf498_custid(custId);
				elf498.setElf498_dupno(dupNo);
				elf498.setElf498_newdate(elf498_newdate);
				elf498.setElf498_latestdate(CapDate.parseDate(elf491_crDate));
				if (true) {
					// 可能 3/2 撥款, 剛好中心在 3/10 就一併覆審
					// 在次月寫入 ELF498時, 並非總是寫入 ZERO_DATE, 要抓ELF492
					Date lrDate = misELF492Service
							.selMaxLrDateByBrNoCustIdDupNoCntrNo(brno, custId,
									dupNo, cntrNo);
					elf498.setElf498_actualdate(lrDate);
				}
				elf498.setElf498_kind(ruleNoNew);
				elf498.setElf498_totalsno(cntrNo);
				elf498.setElf498_remark(elf491_NCKDMEMO);
				elf498.setElf498_updater("NewCase");
				elf498.setElf498_tmestamp(nowTimeStamp);
				// ---
				retrialService.upELF498_single_DelThenInsert(elf498);
			} else {
				String existTotalsno = Util.trim(elf498.getElf498_totalsno());

				if (existTotalsno.indexOf(cntrNo) < 0) {
					elf498.setElf498_totalsno(existTotalsno
							+ CrsUtil.ELF498_DELIM + cntrNo);
					elf498.setElf498_tmestamp(CapDate.getCurrentTimestamp());
					// ---
					retrialService.upELF498_single_DelThenInsert(elf498);
				}
			}
		}
	}

	/**
	 * 參考 gfnDB2Abnormal
	 */
	@Override
	public void update491_abnormal() {
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();
		String default_uckdLine = "1";

		/*
		 * 若已銷戶, 在 LN.LNF022 會查不到資料 此處用到的 C241M01Z.reason 共有 {A, B, C}
		 */
		// 抓異常通報案件
		// 比對現有異常通報案，有存在LNFE0854，設定ELF491清空 nckd 註記,
		// C241M01Z資料相同KEY資料的R98為B:異常通報(off)
		List<Map<String, Object>> abnormalList_above_threshold = misdbBaseService
				.getCrsLNFE0854();
		if (true) {
			Set<String> thresholdSet = new HashSet<String>();
			for (Map<String, Object> row : abnormalList_above_threshold) {
				thresholdSet.add(Util.trim(row.get("LNF022_CUST_ID")));
			}

			for (ELF491 elf491 : misELF491Service.selByR98_activeCrDate()) {
				TreeMap<String, String> map = CrsUtil.parseRule(elf491
						.getElf491_remomo());
				if (!map.containsKey(CrsUtil.R98)) {
					continue;
				}

				if (thresholdSet.contains(LMSUtil.getCustKey_len10custId(
						elf491.getElf491_custid(), elf491.getElf491_dupno()))) {
					continue;
				}
				C241M01Z c241m01z = new C241M01Z();
				_to_c241m01z_elf491_bf(elf491, c241m01z);

				map.remove(CrsUtil.R98);

				if (map.size() == 0) {
					elf491.setElf491_crdate(CapDate
							.parseDate(CapDate.ZERO_DATE));
					// 清空 nckd 註記
					elf491.setElf491_nckdflag(""); // 可能因為合計後小於5000萬, 所以
													// misdbBaseService.getCrsLNFE0854()
													// 未抓到 => 不要塞入 "G.已結清或銷戶案件"
					elf491.setElf491_nckddate(CapDate
							.parseDate(CapDate.ZERO_DATE));
					elf491.setElf491_nckdmemo("");
				}
				elf491.setElf491_uckddt(CapDate.parseDate(CapDate.ZERO_DATE));
				elf491.setElf491_uckdline("");
				elf491.setElf491_remomo(CrsUtil.combineRule(map));
				elf491.setElf491_updater("bal98X");
				elf491.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				// ---
				elf491_list.add(elf491);
				if (true) {
					_to_c241m01z_elf491_af(elf491, c241m01z, "B", "");
					c241m01z_list.add(c241m01z);
				}
			}
		}

		// 異常
		// 處理ID下全部放款帳號都是逾催時，設定為轉逾催覆審
		for (Map<String, Object> row : abnormalList_above_threshold) {
			String idDup = Util.trim(row.get("LNF022_CUST_ID"));
			String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
			String dupNo = Util.trim(StringUtils.substring(idDup, 10, 11));

			List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(custId,
					dupNo);
			Set<String> brNoSet = new HashSet<String>();
			for (MISLN20 lnf020 : lnf020_list) {
				String cntrNo = Util.trim(lnf020.getLnf020_contract());
				Date lnf020_cancel_date = lnf020.getLnf020_cancel_date();
				if (CrsUtil.isNull_or_ZeroDate(lnf020_cancel_date)) {
					brNoSet.add(CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo));
				}
			}
			for (String brNo : brNoSet) {
				ELF491 elf491 = misELF491Service.findByPk(brNo, custId, dupNo);
				// 判斷戶況
				boolean inColRetrial = false;
				String cState = "";
				if (true) {
					String[] lnStatusArr = retrialService.gfnCTL_Import_LNF022(
							brNo, custId, dupNo, null);
					cState = lnStatusArr[0];

					if (Util.equals(cState, "2") || Util.equals(cState, "3")
							|| Util.equals(cState, "4")) {
						inColRetrial = true;
					}
				}

				if (inColRetrial) {
					if (elf491 == null) {
						// 逾催覆審
					} else {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_crdate())) {
							C241M01Z c241m01z = new C241M01Z();
							_to_c241m01z_elf491_bf(elf491, c241m01z);

							if (true) { // 參照 RetrialServiceImpl ::
										// up491_at_save_NoCtl_ReCtl(...)
								elf491.setElf491_crdate(CapDate
										.parseDate(CapDate.ZERO_DATE));
								elf491.setElf491_nckdflag("H");// "轉列報逾放、催收、呆帳案件"
								elf491.setElf491_nckddate(CapDate
										.getCurrentTimestamp());
								elf491.setElf491_nckdmemo("cState=" + cState);
							}
							elf491.setElf491_updater("colrev");
							elf491.setElf491_tmestamp(CapDate
									.getCurrentTimestamp());
							// -----
							elf491_list.add(elf491);
							if (true) {
								_to_c241m01z_elf491_af(elf491, c241m01z, "C",
										"");
								c241m01z_list.add(c241m01z);
							}
						} else {
							// 若原本的ELF491的 CR_DATE為 0001-01-01
							// 且為逾催狀態 → 不寫 log
						}
					}
				} else {
					Date lnfe0854_latest_sDate = null;
					String lnfe0854_latest_mdFlag = "";

					Map<String, Object> map = misdbBaseService.getLrsLNFE0854(
							custId, dupNo);
					if (MapUtils.isNotEmpty(map)) {
						String mdFlag = Util.trim(map.get("LNFE0854_MDFLAG"));
						if (Util.isEmpty(mdFlag)) {
							mdFlag = "9";
						}
						lnfe0854_latest_mdFlag = CrsUtil
								.mdFlag_with_leadingZero(mdFlag);
						lnfe0854_latest_sDate = ((Date) map.get("MDDT"));
					}
					// ============================================
					if (elf491 == null) {
						elf491 = new ELF491();

						C241M01Z c241m01z = new C241M01Z();
						_to_c241m01z_elf491_bf(elf491, c241m01z);

						_initELF491(elf491, brNo, custId, dupNo);

						/*
						 * -- update mis.elf491 set elf491_branch='ZZZ' where
						 * elf491_custid='F152XXXXXX' and elf491_branch='210' --
						 * run batch => 跑這一個 block 的程式 -- delete from mis.elf491
						 * where elf491_custid='F152XXXXXX' and
						 * elf491_branch='210' -- update mis.elf491 set
						 * elf491_branch='210' where elf491_custid='F152XXXXXX'
						 * and elf491_branch='ZZZ'
						 */
						elf491.setElf491_crdate(CrsUtil.r98_1st(
								lnfe0854_latest_sDate, brNo)); // 由3個月 → 1個月後
						elf491.setElf491_uckddt(lnfe0854_latest_sDate);
						elf491.setElf491_uckdline(default_uckdLine);
						elf491.setElf491_reportkind(CrsUtil.OLD_RULE);

						TreeMap<String, String> ruleMap = new TreeMap<String, String>();
						ruleMap.put(CrsUtil.R98, lnfe0854_latest_mdFlag);
						elf491.setElf491_remomo(CrsUtil.combineRule(ruleMap));

						elf491.setElf491_updater("insR98");
						// -----
						elf491_list.add(elf491);
						if (true) {
							_to_c241m01z_elf491_af(elf491, c241m01z, "A", "");
							c241m01z_list.add(c241m01z);
						}
					} else {
						Date choose_uckdDt = lnfe0854_latest_sDate;
						String choose_unkdLine = default_uckdLine; // 可由 elf492
																	// 去抓同一
																	// uckdDt
																	// 下最大的
																	// uckdLine
						String choose_mdFlag = lnfe0854_latest_mdFlag;

						Date choose_crDate = elf491.getElf491_crdate();
						// ========================
						C241M01Z c241m01z = new C241M01Z();
						_to_c241m01z_elf491_bf(elf491, c241m01z);

						if (CrsUtil.isNull_or_ZeroDate(elf491
								.getElf491_lrdate())) {
							// 未曾覆審過, 就被異常通報
							choose_crDate = CrsUtil.r98_1st(choose_uckdDt,
									elf491.getElf491_branch());
						} else {
							ELF492 existMaxUckd_elf492 = misELF492Service
									.selMaxUckdDt(brNo, custId, dupNo);
							if (existMaxUckd_elf492 == null) {
								// 只有一般正常覆審的記錄, 從未在異常通報後, 被挑出覆審
								// (1)
								choose_crDate = CrsUtil.r98_1st(choose_uckdDt,
										elf491.getElf491_branch());
							} else {
								/*
								 * 可能狀況 (1)正常戶(房貸首次覆審後,可能 crDate=0001-01-01 vs
								 * crDate有值), 第1次被異常通報 ⇒ 原本沒有 uckddt
								 * 
								 * (2)被異常通報後於1個月內覆審, 之後每隔6個月覆審一次 ⇒
								 * 不需異動既有的ELF491資料
								 * 
								 * (3)被異常通報後, 遭覆審人員, 手動註記不覆審
								 * 
								 * (4)同一個人, 可以在2016被異常通報乙次
								 * "06-負責人、保證人等涉嫌違反法令或與第三人發生訴訟" 然後在
								 * 2017又被異常通報第二次 "72-擔保品遭第三人假扣押（個人戶）" ⇒ 原本已有
								 * uckddt, 要用 latest uckddt 覆蓋掉
								 * 
								 * (5)正常戶 → 異常通報 → 逾催戶, 但後續 "逾催轉正" ⇒ 目前系統上無
								 * "逾催轉正日" , 所以仍要在1個月內去覆審
								 * 
								 * (6)客戶的額度都銷戶, 之後又向本行申貸 ⇒ 在 elf490_rule_no_new
								 * 會有值(新案6個月)
								 */
								Date orgSuggestDate = CrsUtil
										.r98_after1st(existMaxUckd_elf492
												.getElf492_lrdate());
								Date elf491_crDate = elf491.getElf491_crdate();
								Date elf492_uckdDate = existMaxUckd_elf492
										.getElf492_uckddt();
								if (LMSUtil.cmpDate(elf492_uckdDate, "==",
										lnfe0854_latest_sDate)) {
									if (CrsUtil
											.isNOT_null_and_NOTZeroDate(elf491_crDate)) {
										if (LMSUtil.cmpDate(elf491_crDate,
												"<=", orgSuggestDate)) {
											// (2)
											continue;
										} else {
											choose_crDate = orgSuggestDate; // crDate
																			// 被延後,
																			// reset至(lrDate+6個月)
										}
									} else {
										// (3)
										choose_crDate = orgSuggestDate;
									}
								} else if (LMSUtil.cmpDate(elf492_uckdDate,
										">", lnfe0854_latest_sDate)) {
									/*
									 * 在 2016 有一次異常通報 但 2017 又一次異常通報 => 可是, 卻先解除
									 * 2017 的那份(不解除 2016)=> 才可能出現這種狀況
									 */
									choose_uckdDt = elf492_uckdDate;
									choose_unkdLine = Util
											.trim(existMaxUckd_elf492
													.getElf492_uckdline()); // 抓最大的
																			// uckdLine
									TreeMap<String, String> ruleMap_492 = CrsUtil
											.parseRule(existMaxUckd_elf492
													.getElf492_kind());
									choose_mdFlag = Util.trim(ruleMap_492
											.get(CrsUtil.R98));

									if (CrsUtil
											.isNOT_null_and_NOTZeroDate(elf491_crDate)) {
										continue;
									} else {
										choose_crDate = orgSuggestDate; // 修補不合理的資料
									}
								} else if (LMSUtil.cmpDate(elf492_uckdDate,
										"<", lnfe0854_latest_sDate)) {
									// 當之前覆審的異常通報 sDate < latest_sDate, 改成以
									// latest_sDate 為主(才會看到最新的 mdFlag)
									Date newSuggestDate = CrsUtil.r98_1st(
											choose_uckdDt,
											elf491.getElf491_branch());

									if (LMSUtil.cmpDate(
											elf491.getElf491_lrdate(), "<",
											newSuggestDate)) {
										// 挑選 Min(elf491_crDate, newSuggestDate)
										// (4)
										if (CrsUtil
												.isNOT_null_and_NOTZeroDate(elf491_crDate)) {
											choose_crDate = (LMSUtil.cmpDate(
													newSuggestDate, "<",
													elf491_crDate) ? newSuggestDate
													: elf491_crDate);
										} else {
											choose_crDate = newSuggestDate;
										}
									} else {
										// 【previous_sDate < elf492_uckdDate <=
										// elf491_lrdate < lnfe0854_latest_sDate
										// < newSuggestDate】
										// ● 大多情況, 應該 elf492_uckdDate =
										// elf491_lrdate
										// ● 如果 elf492_uckdDate < elf491_lrdate
										// , 表示有在 解除異常通報覆審 後, 曾經有做過一般正常的覆審
										// (不合理的狀況)之前只用 bal, 後來改成 "有效額度" 來判斷
										choose_crDate = CapDate.addMonth(
												elf491.getElf491_lrdate(), 1);
									}
								} else {
									continue;
								}
							}
						}

						boolean chgVal = false;
						if (CrsUtil.isNull_or_ZeroDate(elf491
								.getElf491_crdate())
								|| LMSUtil.cmpDate(choose_crDate, "<",
										elf491.getElf491_crdate())) {
							elf491.setElf491_crdate(choose_crDate);
							chgVal = true;
						}
						if (!CrsUtil.isNckdFlag_EMPTY_A(Util.trim(elf491
								.getElf491_nckdflag()))) {
							// 如之前的 elf491_nckdflag=="H", 要 reset 成 "可覆審" 的狀態
							// 參照 RetrialServiceImpl ::
							// up491_at_save_NoCtl_ReCtl(...)
							elf491.setElf491_nckdflag("");
							elf491.setElf491_nckddate(CapDate
									.parseDate(CapDate.ZERO_DATE));
							elf491.setElf491_nckdmemo("");
							chgVal = true;
						}

						if (CrsUtil.isNull_or_ZeroDate(elf491
								.getElf491_uckddt())
								|| !LMSUtil.cmpDate(choose_uckdDt, "==",
										elf491.getElf491_uckddt())) {
							elf491.setElf491_uckddt(choose_uckdDt);
							elf491.setElf491_uckdline(choose_unkdLine);
							chgVal = true;
						}

						TreeMap<String, String> ruleMap = CrsUtil
								.parseRule(elf491.getElf491_remomo());
						if (!ruleMap.keySet().contains(CrsUtil.R98)) {
							ruleMap.put(CrsUtil.R98, choose_mdFlag);
							elf491.setElf491_remomo(CrsUtil
									.combineRule(ruleMap));
							chgVal = true;
						}

						if (chgVal) {
							elf491.setElf491_updater("chgR98");
							elf491.setElf491_tmestamp(CapDate
									.getCurrentTimestamp());
							// -----
							elf491_list.add(elf491);

							if (true) {
								_to_c241m01z_elf491_af(elf491, c241m01z, "A",
										"");
								c241m01z_list.add(c241m01z);
							}
						}
					}
				}
			}
		}
		retrialService.upELF491_DelThenInsert(elf491_list);
		for (C241M01Z c241m01z : c241m01z_list) {
			retrialService.save(c241m01z);
		}
	}

	@Override
	public void update491_J_109_0372_1st() {
		Date onDate = CapDate.parseDate("2020-08-25");
		Date shouldCrDate = CapDate.parseDate("2020-12-31");
		for (Map<String, Object> row : misdbBaseService
				.crs_chose_J_109_0372_1st()) {
			String custId = Util.trim(MapUtils.getString(row, "CUSTID"));
			String dupNo = Util.trim(MapUtils.getString(row, "DUPNO"));
			String brNo = Util.trim(MapUtils.getString(row, "LNF020_BR"));
			String elf491_branch = Util.trim(MapUtils.getString(row,
					"ELF491_BRANCH"));

			if (Util.isNotEmpty(elf491_branch)) {
				Date lrDate = (Date) MapUtils.getObject(row, "ELF491_LRDATE");
				// Date crDate = (Date)MapUtils.getObject(row, "ELF491_CRDATE");
				// 109.8.25兆銀總授審字第1090045985號函
				if (CrsUtil.isNull_or_ZeroDate(lrDate)) {
					// need
				} else {
					if (LMSUtil.cmpDate(lrDate, "<=", onDate)) {

					} else {
						logger.info("skip_J_109_0372_1st{" + brNo + ","
								+ custId + "," + dupNo + "}lrDate="
								+ TWNDate.toAD(lrDate));
						continue;
					}
				}
			}
			// ~~~~~~~~
			boolean chgVal = false;
			ELF491 elf491 = misELF491Service.findByPk(brNo, custId, dupNo);
			if (elf491 == null) {
				elf491 = new ELF491();
				_initELF491(elf491, brNo, custId, dupNo);
				// ======
				elf491.setElf491_reportkind(CrsUtil.OLD_RULE);
				elf491.setElf491_newflag(CrsUtil.ELF491_NEWFLAG_N);
			}
			C241M01Z c241m01z = new C241M01Z();
			_to_c241m01z_elf491_bf(elf491, c241m01z);

			if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())
					|| LMSUtil.cmpDate(elf491.getElf491_crdate(), ">",
							shouldCrDate)) {
				elf491.setElf491_crdate(shouldCrDate);
				chgVal = true;
			}
			if (true) { // 在 REMOMO 加入 R95
				TreeMap<String, String> remomo_map = CrsUtil.parseRule(elf491
						.getElf491_remomo());
				if (!remomo_map.keySet().contains(CrsUtil.R95)) {
					remomo_map.put(CrsUtil.R95, "");
					elf491.setElf491_remomo(CrsUtil.combineRule(remomo_map));
					chgVal = true;
				}
			}

			if (chgVal) {
				List<ELF491> elf491_list = new ArrayList<ELF491>();
				elf491.setElf491_updater("chgR95");
				elf491.setElf491_tmestamp(CapDate.getCurrentTimestamp());
				// -----
				elf491_list.add(elf491);

				if (true) {
					retrialService.upELF491_DelThenInsert(elf491_list);
					// -----
					_to_c241m01z_elf491_af(elf491, c241m01z, "1", "");
					retrialService.save(c241m01z);
				}
			} else {
				logger.info("skip_J_109_0372_1st{" + brNo + "," + custId + ","
						+ dupNo + "}chgVal=" + chgVal);
			}
		}
	}

	@Override
	public void update491_J_109_0372_byBatchNo(String steps,
			String batchNo_codeType_LNF07A_KEY_1, String postDocNoByBatchNo,
			Date postDateByBatchNo, Date firstCrDateByBatchNo) {
		boolean run_step_1 = false;
		boolean run_step_2 = false;
		boolean run_step_3 = false;

		String[] steps_arr = steps.split("\\|");
		run_step_1 = CrsUtil.inCollection("1", steps_arr);
		run_step_2 = CrsUtil.inCollection("2", steps_arr);
		run_step_3 = CrsUtil.inCollection("3", steps_arr);

		if (run_step_1) { // ref <entry key="job_J_109_0344_step1">
			Map<String, String> batch_data_map = clsService
					.get_codeTypeWithOrder(batchNo_codeType_LNF07A_KEY_1);
			List<LNF07A> lnf07a_list = new ArrayList<LNF07A>();
			for (String lnf020_contract : batch_data_map.keySet()) {
				Map<String, Object> lnf020_map = misMISLN20Service
						.findByCntrNo(lnf020_contract);
				if (MapUtils.isEmpty(lnf020_map)) {
					continue;
				}

				LNF07A lnf07a = new LNF07A();
				String idDup = Util.trim(MapUtils.getString(lnf020_map,
						"LNF020_CUST_ID"));
				String custId = StringUtils.substring(idDup, 0, 10);
				String dupNo = StringUtils.substring(idDup, 10);
				String custName = "";
				if (true) {
					custName = clsService.get0024_custName(custId, dupNo);

					if (Util.isEmpty(custName)) {
						custName = batch_data_map.get(lnf020_contract);
					}
				}
				lnf07a.setLnf07a_key_1(batchNo_codeType_LNF07A_KEY_1);
				lnf07a.setLnf07a_key_2(custId);
				lnf07a.setLnf07a_key_3(dupNo);
				lnf07a.setLnf07a_key_4(lnf020_contract);
				lnf07a.setLnf07a_key_5("");
				lnf07a.setLnf07a_explain(postDocNoByBatchNo);
				lnf07a.setLnf07a_content_1(custName);
				lnf07a.setLnf07a_content_2("");
				lnf07a.setLnf07a_content_3("");
				lnf07a.setLnf07a_content_4("");
				lnf07a.setLnf07a_content_5("");
				lnf07a_list.add(lnf07a);
			}
			if (lnf07a_list.size() > 0) {
				lnLnf07aService.delete_by_key1_key2_key3(lnf07a_list);
				lnLnf07aService.insert(lnf07a_list);
			}
		}

		if (run_step_2) { // <entry key="job_J_109_0344_step2">
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			String regBR = "918";
			String regTeller = "";
			int REFUSECD = 23;
			String rocPostDateByBatchNo = TWNDate.valueOf(postDateByBatchNo)
					.toTW('.');
			String clsCase = "C";
			String statusCD = "1";
			String oid = "";
			for (LNF07A lnf07a : lnLnf07aService
					.sel_by_key1(batchNo_codeType_LNF07A_KEY_1)) {
				String custId = Util.trim(lnf07a.getLnf07a_key_2());
				String dupNo = Util.trim(lnf07a.getLnf07a_key_3());
				String custName = Util.trim(lnf07a.getLnf07a_content_1());
				String REFUSEDS = rocPostDateByBatchNo
						+ Util.trim(lnf07a.getLnf07a_explain())
						+ "。本案屬疑似人頭戶追蹤案件,簽案核決層級應由營運中心(含)以上單位核定";
				misLnunIdService.insertLnunId(custId, dupNo, nowTS, regBR,
						regTeller, REFUSECD, REFUSEDS, "", "system", nowTS,
						clsCase, "", custName, statusCD, oid);
			}
		}

		if (run_step_3) {
			for (Map<String, Object> row : misdbBaseService
					.crs_chose_J_109_0372_byBatchNo(batchNo_codeType_LNF07A_KEY_1)) {
				String custId = Util.trim(MapUtils.getString(row, "CUSTID"));
				String dupNo = Util.trim(MapUtils.getString(row, "DUPNO"));
				String brNo = Util.trim(MapUtils.getString(row, "LNF020_BR"));
				String elf491_branch = Util.trim(MapUtils.getString(row,
						"ELF491_BRANCH"));

				if (Util.isNotEmpty(elf491_branch)) {
					Date lrDate = (Date) MapUtils.getObject(row,
							"ELF491_LRDATE");
					// Date crDate = (Date)MapUtils.getObject(row,
					// "ELF491_CRDATE");
					// 109.8.25兆銀總授審字第1090045985號函
					if (CrsUtil.isNull_or_ZeroDate(lrDate)) {
						// need
					} else {
						if (LMSUtil.cmpDate(lrDate, "<=", postDateByBatchNo)) {

						} else {
							logger.info("skip_J_109_0372_byBatchNo{"
									+ batchNo_codeType_LNF07A_KEY_1 + ","
									+ brNo + "," + custId + "," + dupNo
									+ "}lrDate=" + TWNDate.toAD(lrDate));
							continue;
						}
					}
				}
				// ~~~~~~~~
				boolean chgVal = false;
				ELF491 elf491 = misELF491Service.findByPk(brNo, custId, dupNo);
				if (elf491 == null) {
					elf491 = new ELF491();
					_initELF491(elf491, brNo, custId, dupNo);
					// ======
					elf491.setElf491_reportkind(CrsUtil.OLD_RULE);
					elf491.setElf491_newflag(CrsUtil.ELF491_NEWFLAG_N);
				}
				C241M01Z c241m01z = new C241M01Z();
				_to_c241m01z_elf491_bf(elf491, c241m01z);

				if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())
						|| LMSUtil.cmpDate(elf491.getElf491_crdate(), ">",
								firstCrDateByBatchNo)) {
					elf491.setElf491_crdate(firstCrDateByBatchNo);
					chgVal = true;
				}
				if (true) { // 在 REMOMO 加入 R95
					TreeMap<String, String> remomo_map = CrsUtil
							.parseRule(elf491.getElf491_remomo());
					if (!remomo_map.keySet().contains(CrsUtil.R95)) {
						remomo_map.put(CrsUtil.R95, "");
						elf491.setElf491_remomo(CrsUtil.combineRule(remomo_map));
						chgVal = true;
					}
				}

				if (chgVal) {
					List<ELF491> elf491_list = new ArrayList<ELF491>();
					elf491.setElf491_updater("chgR95");
					elf491.setElf491_tmestamp(CapDate.getCurrentTimestamp());
					// -----
					elf491_list.add(elf491);

					if (true) {
						retrialService.upELF491_DelThenInsert(elf491_list);
						// -----
						_to_c241m01z_elf491_af(elf491, c241m01z, "1", "");
						retrialService.save(c241m01z);
					}
				} else {
					logger.info("skip_J_109_0372_byBatchNo{"
							+ batchNo_codeType_LNF07A_KEY_1 + "," + brNo + ","
							+ custId + "," + dupNo + "}chgVal=" + chgVal);
				}
			}
		}
	}

	private boolean is_crs_newCust_3M_brNo(String brNo) {
		if (Util.equals(brNo, "079")) {
			return false;
		}
		// return true; //J-107-0223 新案由6個月改3個月
		return false; // J-109-0213 新案又由3個月恢復為6個月
	}

	@Override
	/**
	 * 處理跨月撥款狀況
	 * 建帳號LNF030 =>10/31
	 * 撥款			=>11/1
	 * 
	 */
	public void proc_crossMonth_loanData(Date sysMonth_1st) {
		String lastMonth = CapDate.addMonth(
				CapDate.formatDate(sysMonth_1st, CapDate.DEFAULT_DATE_FORMAT),
				-1);

		// elf490_data_ym 的格式: 009912表示0099年12月
		String baseRocDataYM = CrsUtil.elf490YM_from_adDate(lastMonth);

		String prevRocDataYM = CrsUtil.elf490YM_from_adDate(CapDate.addMonth(
				CapDate.formatDate(sysMonth_1st, CapDate.DEFAULT_DATE_FORMAT),
				-2));

		Date cmpCrDate_6m = CapDate.addMonth(sysMonth_1st, 6);
		Date decideCrDate_add_3 = CapDate.shiftDays(
				CapDate.addMonth(sysMonth_1st, 6), -1);
		Date decideCrDate_add_6 = CapDate.shiftDays(
				CapDate.addMonth(sysMonth_1st, 3), -1);

		// 若有N個額度，跨月撥款
		Map<String, Set<String>> proc_brNo_custId_dupNo_map = new HashMap<String, Set<String>>();
		for (Map<String, Object> map : misELF491Service
				.proc_crossMonth_loanData(prevRocDataYM, baseRocDataYM,
						TWNDate.toAD(cmpCrDate_6m))) {
			String brNo = Util.trim(MapUtils.getString(map, "BRNO"));
			String custId = Util.trim(MapUtils
					.getString(map, "ELF488A_CUST_ID"));
			String dupNo = Util.trim(MapUtils.getString(map, "ELF488A_DUP_NO"));
			if (!proc_brNo_custId_dupNo_map.containsKey(brNo)) {
				proc_brNo_custId_dupNo_map.put(brNo, new HashSet<String>());
			}
			proc_brNo_custId_dupNo_map.get(brNo).add(
					LMSUtil.getCustKey_len10custId(custId, dupNo));
		}
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();

		for (String brNo : proc_brNo_custId_dupNo_map.keySet()) {
			boolean is_tp_brNo = CrsUtil.is_tp_brNo(brNo);
			for (String idDup : proc_brNo_custId_dupNo_map.get(brNo)) {
				String custId = Util.trim(StringUtils.substring(idDup, 0, 10));
				String dupNo = Util.trim(StringUtils.substring(idDup, 10, 11));

				ELF491 elf491 = misELF491Service.findByPk(brNo, custId, dupNo);
				String org_elf491_desc = "";
				if (elf491 == null) {
					org_elf491_desc = "ELF491_notExist";
				} else {
					org_elf491_desc = "ELF491_CRDATE="
							+ Util.trim(TWNDate.toAD(elf491.getElf491_crdate()));
				}
				// ============================================================================
				Date decideCrDate = decideCrDate_add_6; // 預設為6個月內
				if (true) {
					String hasLrDate = "";
					if (elf491 != null) {
						hasLrDate = CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_lrdate()) ? "Y" : "N";
					}

					if (is_crs_newCust_3M_brNo(brNo)
							&& !Util.equals(hasLrDate, "Y")) { // J-108-0078
						/*
						 * 已覆審過的案件(代表後續只會新案+6個月, 不會再有+3個月的情況), 已經在
						 * !Util.equals(hasLrDate, "Y") 先排除掉了 剩下的情況 ● 全新客戶 ● 舊客戶
						 * => 跨月撥款時，可能完全沒有ELF490。 => 要另外判斷
						 */
						String elf490_data_ym_S = CrsUtil
								.elf490YM_from_adDate(CapDate.addMonth(CrsUtil
										.elf490YM_to_adDate_d(baseRocDataYM),
										-6));
						Date cmp_date = CrsUtil
								.elf490YM_to_adDate_d(elf490_data_ym_S);
						if (Util.equals("Y",
								decide_crs_NewCust(custId, dupNo, cmp_date))) {
							decideCrDate = decideCrDate_add_3; // 3個月內覆審
						}
					}
				}

				if (elf491 == null) {
					// 仍產生一筆空的 ELF491
					elf491 = new ELF491();
					// set default value
					_initELF491(elf491, brNo, custId, dupNo);
				} else {
					if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())) {

					} else {
						// 當已有 crDate
						if (LMSUtil.cmp_yyyyMM(elf491.getElf491_crdate(), "<=",
								decideCrDate)) {
							continue; // 不需異動
						} else {
							// update ELF491
						}
					}
				}

				String elf490_rule_no_new = CrsUtil.R99; // 預設值,以{產品、科目}去反推符合的
															// Rule
				if (true) {
					HashSet<String> deriveUnknownSet = new HashSet<String>();
					TreeMap<String, String> deriveMap = new TreeMap<String, String>();

					CrsVO crsVO = new CrsVO();
					retrialService
							.fetch_LNF020_LNF030040(crsVO,
									elf491.getElf491_custid(),
									elf491.getElf491_dupno());
					// 判斷有哪些 LNF020 要排除
					List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
							.filter_crsVO_getLNF020(crsVO.getLNF020(
									elf491.getElf491_custid(),
									elf491.getElf491_dupno()), brNo);

					for (Map<String, Object> dataMap020 : _filter_crsVO_getLNF020) {
						String cntrNo = Util.trim(dataMap020
								.get("LNF020_CONTRACT"));
						String lnf020_ln_br_no = Util.trim(dataMap020
								.get("LNF020_LN_BR_NO"));
						BigDecimal lnf020_fact_amt = CrsUtil
								.parseBigDecimal(dataMap020
										.get("LNF020_FACT_AMT"));
						String revolve = Util.trim(dataMap020
								.get("LNF020_REVOLVE"));

						List<Map<String, Object>> list_030_040 = crsVO
								.getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(
										cntrNo, lnf020_ln_br_no);
						for (Map<String, Object> dataMap030_040 : list_030_040) {
							String lnf030_loan_no = Util.trim(dataMap030_040
									.get("LNF030_LOAN_NO"));
							String lnType = Util.trim(dataMap030_040
									.get("LNF030_LOAN_CLASS"));
							String actcd = Util.trim(dataMap030_040
									.get("LNF040_ACT_CODE"));

							CrsUtil.trans99(deriveMap, deriveUnknownSet,
									lnf030_loan_no, lnType, actcd, revolve,
									lnf020_fact_amt);
						}
					}
					if (deriveMap.size() > 0) {
						elf490_rule_no_new = CrsUtil.combineRule(deriveMap);
					}

					if (LMSUtil.elm_onlyLeft(
							CrsUtil.parseRule(elf490_rule_no_new).keySet(),
							CrsUtil.convert_arr_to_set(
									CrsUtil.RULE_ARR_R12_R13_R8_1,
									CrsUtil.RULE_ARR_R1_R2)).size() > 0) {
						// 包含非 R1, R2 的 Rule => 應覆審，並寫入 ELF491
					} else {
						// 純R1,R2 => 判斷 {台北/新北}是否超過金額門檻
						BigDecimal sum = BigDecimal.ZERO;
						for (Map<String, Object> dataMap020 : crsVO.getLNF020(
								custId, dupNo)) {
							BigDecimal lnf020_fact_amt = CrsUtil
									.parseBigDecimal(dataMap020
											.get("LNF020_FACT_AMT"));
							sum = sum.add(lnf020_fact_amt);
						}

						if (CrsUtil.amt_over_threshold_3000WAN_1500WAN_by_brNo(
								is_tp_brNo, sum)) {
							// 需覆審
						} else {
							if (misELF591Service
									.sel_never_retrialData_in_ELF591H(brNo,
											idDup).size() > 0
									|| misELF487Service
											.sel_never_retrialData_match___HOUSE_or_RMBINS_or_HS_CRE_FG(
													brNo, custId, dupNo).size() > 0) {
								// 低於門檻，但因符合{特定態樣}也需覆審
							} else {
								continue;
							}
						}
					}

				}
				String c241m01z_reasonDesc = "from[" + org_elf491_desc
						+ "] to [ELF491_CRDATE="
						+ Util.trim(TWNDate.toAD(decideCrDate)) + "]";
				retrialService.up491_at_proc_crossMonth_loanData(elf491_list,
						c241m01z_list, elf491, CrsUtil.NEW_RULE, decideCrDate,
						"", elf490_rule_no_new, c241m01z_reasonDesc);
			} // end-for idDup
		}
		retrialService.upELF491_DelThenInsert(elf491_list);
		retrialService.saveC241M01Z(c241m01z_list);
	}

	@Override
	/**
	 * 六(二)4.產生應覆審名單時，列於「國內消金警示戶明細表」(SRMLN006)或「國內消金現金存入警示戶明細表」(CRMLN116)者，惟本態樣授信案件辦理覆審時點不受第六條第一項所定覆審期限之限制
	 * 抓取警示戶資料出來，更新ELF491為要覆審
	 */
	public void proc_elf591_custid_not_in_ELF491(String param_brNo,
			String assign_crDate) {
		List<ELF491> elf491_list = new ArrayList<ELF491>();
		List<C241M01Z> c241m01z_list = new ArrayList<C241M01Z>();

		Set<String> proc_brNo_id_dup = new HashSet<String>();
		Date should_crDate = CapDate.parseDate(assign_crDate);
		for (Map<String, Object> dataRow : misELF591Service
				.sel_not_in_ELF491_list(param_brNo, assign_crDate)) {
			String _LNF030_LOAN_NO = Util.trim(MapUtils.getString(dataRow,
					"LNF030_LOAN_NO"));
			String _LNF030_CONTRACT = Util.trim(MapUtils.getString(dataRow,
					"LNF030_CONTRACT"));
			String idDup = Util.trim(MapUtils.getString(dataRow,
					"LNF030_CUST_ID"));

			String brNo = CrsUtil.getBrNoFromLNF020_CONTRACT(_LNF030_CONTRACT);
			String custId = CrsUtil.get_custId_from_custKey(idDup);
			String dupNo = CrsUtil.get_dupNo_from_custKey(idDup);
			Date _ELF591H_DATA_DATE = (Date) MapUtils.getObject(dataRow,
					"ELF591H_DATA_DATE");

			if (true) {
				String build_value = brNo + "-" + custId + "-" + dupNo;
				if (proc_brNo_id_dup.contains(build_value)) {
					continue;
				} else {
					proc_brNo_id_dup.add(build_value);
				}
			}
			ELF491 elf491 = misELF491Service.findByPk(brNo, custId, dupNo);
			if (elf491 == null) {
				elf491 = new ELF491();
				// set default value
				_initELF491(elf491, brNo, custId, dupNo);
			} else {

			}
			/*
			 * 若是短擔科目(2開頭), 回傳 R2 否則(屬於中長期科目), 採用R1
			 */
			String added_rule = CrsUtil.getSubjCodeFromLNF030_LOAN_NO(
					_LNF030_LOAN_NO).startsWith("2") ? CrsUtil.R2 : CrsUtil.R1;
			String c241m01z_reasonDesc = "elf591h_data_date="
					+ Util.trim(TWNDate.toAD(_ELF591H_DATA_DATE));
			retrialService.up491_at_proc_elf591_custid_not_in_ELF491(
					elf491_list, c241m01z_list, elf491, should_crDate,
					added_rule, c241m01z_reasonDesc);
		}
		if (elf491_list.size() > 0) {
			retrialService.upELF491_DelThenInsert(elf491_list);
			retrialService.saveC241M01Z(c241m01z_list);
		}
	}

	private void elf490_to_elf491(Date baseDate, BigDecimal elf490_estate_amt,
			String elf490_rule_no, String elf490_rule_no_new,
			CrsRuleVO crsRuleVO, List<ELF491> elf491_list,
			List<C241M01Z> c241m01z_list) {
		logger.trace("enter elf490_to_elf491:" + crsRuleVO.getInitInfo() + "{"
				+ "ruleNo[" + elf490_rule_no + "]" + "ruleNoNew["
				+ elf490_rule_no_new + "]" + "}");

		String custId = crsRuleVO.getCustId();
		String dupNo = crsRuleVO.getDupNo();
		ELF491 elf491 = misELF491Service.findByPk(crsRuleVO.getBrNo(), custId,
				dupNo);

		// 重新發動SLMS-00017時執行此段
		if (elf491 != null
				&& CrsUtil
						.isNOT_null_and_NOTZeroDate(elf491.getElf491_lrdate())
				&& LMSUtil.cmpDate(elf491.getElf491_lrdate(), ">=", baseDate)) {
			if (Util.isNotEmpty(Util.trim(elf490_rule_no_new))) {
				/*
				 * case A 在11/06做短期額度展延1年,中心在11/15覆審,次月的ELF490會被標註為新案 此時, 雖然寫入
				 * ELF498, 但應該加1年, 而非加6個月
				 * 
				 * case B 若【覆審後1天,又新貸撥款】本來用 `覆審日加1年` → 改成 `新案加6個月`
				 */
				CrsVO crsVO = new CrsVO();
				retrialService.fetch_LNF020_LNF030040(crsVO, custId, dupNo);
				// 判斷有哪些 LNF020 要排除
				List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
						.filter_crsVO_getLNF020(crsVO.getLNF020(custId, dupNo),
								crsRuleVO.getBrNo());

				int lrCnt_N = 0;
				int lrCnt_Y = 0;
				for (Map<String, Object> dataMap020 : _filter_crsVO_getLNF020) {
					String cntrNo = Util
							.trim(dataMap020.get("LNF020_CONTRACT"));
					Date lr_date = misELF492Service
							.selMaxLrDateByBrNoCustIdDupNoCntrNo(
									crsRuleVO.getBrNo(), custId, dupNo, cntrNo);
					if (CrsUtil.isNull_or_ZeroDate(lr_date)) {
						lrCnt_N++;
					} else {
						lrCnt_Y++;
					}
				}

				if (lrCnt_N > 0 || lrCnt_Y == 0) {
					// 跑 Rule 判斷
				} else {
					return; // 額度序號全都覆審過且LrDate在?個月內，不再往下執行
				}
			}
		}

		if (true) {
			// 若 custId 有聯貸的額度序號。在 A 分行有 30,31 。 在 B 分行只有 31。
			// → 在 B 分行不需覆審，產名單時，不會包含該 custId
			// 但在每月的ELF490 轉 491 時，可能出現舊案為 99
			// 此時，要把在 B 分行的 ELF491_CRDATE 清空，不然覆審報表，會出現「逾期未覆審」
			boolean _chkLN = true;
			if (elf491 != null
					&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491
							.getElf491_crdate())) {
				Date _cmp = CapDate.addMonth(new Date(), 1);
				/*
				 * 為了效能考量，不需每一筆都去讀 LN【當 crDate>1個月 後，就不檢查是否無LN資料】
				 * p.s.下個月的1號會再跑一次490轉491
				 */
				if (LMSUtil.cmpDate(elf491.getElf491_crdate(), ">", _cmp)) {
					_chkLN = false;
				}
			}

			if (_chkLN) {
				CrsVO crsVO = new CrsVO();
				retrialService.fetch_LNF020_LNF030040(crsVO, custId, dupNo);
				// 判斷有哪些 LNF020 要排除
				List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
						.filter_crsVO_getLNF020(crsVO.getLNF020(custId, dupNo),
								crsRuleVO.getBrNo());
				if (CollectionUtils.isEmpty(_filter_crsVO_getLNF020)) {
					if (elf491 == null) {
						// 仍產生一筆空的 ELF491
						elf491 = new ELF491();
						// set default value
						_initELF491(elf491, crsRuleVO.getBrNo(), custId, dupNo);
					}
					retrialService.up491_at_490to491_notMatch(elf491_list,
							c241m01z_list, elf491, elf490_rule_no,
							elf490_rule_no_new, CrsUtil.NO_LN_DATA);

					// 若無額度資料，不再往下執行
					return;
				}
			}
		}

		String hasLrDate = "";
		if (elf491 != null) {
			hasLrDate = CrsUtil.isNOT_null_and_NOTZeroDate(elf491
					.getElf491_lrdate()) ? "Y" : "N";
		}
		_runRuleLogic("_batch_", crsRuleVO, elf490_rule_no_new, elf490_rule_no,
				hasLrDate);
		// TODO 在測試環境,發現 ELF490 為 新案
		// 但 cms 因抓 eloan 的資料, 因倒檔的時間點不一致
		// 導致判斷 金額 不符合
		crsRuleVO.forceON490_NEW_R1R2R4(elf490_rule_no_new, elf490_estate_amt);

		if (elf491 == null) {
			elf491 = new ELF491();
			// set default value
			_initELF491(elf491, crsRuleVO.getBrNo(), custId, dupNo);

			if (crsRuleVO.rule_decided()) {
				Date decideCrDate = CapDate.addMonth(baseDate,
						crsRuleVO.getAddedMonth());
				retrialService.up491_at_490to491_match("I", elf491_list,
						c241m01z_list, elf491,
						crsRuleVO.getElf491_reportkind(),
						CrsUtil.get_month_last_date(decideCrDate),
						elf490_rule_no, elf490_rule_no_new,
						crsRuleVO.getC241M01Z_reasonDesc());
			} else {
				retrialService.up491_at_490to491_notMatch(elf491_list,
						c241m01z_list, elf491, elf490_rule_no,
						elf490_rule_no_new, crsRuleVO.getC241M01Z_reasonDesc());
			}
		} else {
			/*
			 * 舊案的處理:
			 * 
			 * ● 之前已設定 nckdFlag=B.(已把ELF491_CRDATE設成0001-01-01) 又有新案 ●
			 * 連續4個月的ELF490都有出現
			 */
			if (crsRuleVO.rule_decided()) {
				// ==========
				// 若新案符合, 用 baseDate 去加
				// 若舊案符合, 用 lrDate~current 的ELF490(若中間因帳務異動變99,可在下一期調回適當的CrDate)
				Date c_baseDate = baseDate;
				if (CrsUtil.OLD_RULE.equals(crsRuleVO.getElf491_reportkind())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_lrdate())) {
					Date cmpDate = CapDate.addMonth(CrsUtil.get_sysMonth_1st(),
							2);
					if (LMSUtil.cmpDate(CapDate.addMonth(
							elf491.getElf491_lrdate(),
							crsRuleVO.getAddedMonth()), ">=", cmpDate)) {						
						c_baseDate = elf491.getElf491_lrdate();
					} else {
						Date _closet_from490 = _get_closet_from490(
								elf491.getElf491_lrdate(), baseDate, crsRuleVO);
						if (CrsUtil.isNOT_null_and_NOTZeroDate(_closet_from490)
								&& LMSUtil.cmpDate(CapDate.addMonth(
										_closet_from490,
										crsRuleVO.getAddedMonth()), ">=",
										cmpDate)) {
							c_baseDate = _closet_from490;
						}
					}			
				}
				Date decideCrDate = CapDate.addMonth(c_baseDate,
						crsRuleVO.getAddedMonth());				
				// ==========
				if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_crdate())
						|| (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_crdate()) && LMSUtil.cmp_yyyyMM(
								decideCrDate, "<", elf491.getElf491_crdate()))) {
					// 清空 nckdFlag
					retrialService.up491_at_490to491_match("U", elf491_list,
							c241m01z_list, elf491,
							crsRuleVO.getElf491_reportkind(),
							CrsUtil.get_month_last_date(decideCrDate),
							elf490_rule_no, elf490_rule_no_new,
							crsRuleVO.getC241M01Z_reasonDesc());
				} else {
					/*
					 * elf491CrDate 已有應覆審日, 而此次算出來的 decideCrDate>=elf491CrDate
					 * => 增加判斷 Rule 是否已 "不適用"
					 * 
					 * 
					 * 客戶原本的覆審日期[2018-06], 下次預計在[2019-06]覆審 但在 2019-05
					 * 把所有的舊額度銷戶, 另建新額度, 此時, 跑出的下次覆審日會是2019-11 因為 "所有的舊額度銷戶" ,
					 * 概念上等同 "先不需覆審,又新建額度" => 此時應寫入 ELF491, 比照新案處理
					 * 
					 * 2018-06 ~~~~~~~~~~~~~~~~ 2019-06 2019-05
					 * ~~~~~~~~~~~2019-11
					 */
					if (Util.equals(CrsUtil.NEW_RULE,
							crsRuleVO.getElf491_reportkind())
							&& clsService
									.is_function_on_codetype("crs_replace_memo")) {
						boolean replace_remomo = isElf491Rule_NotMatch_LNData(
								elf490_rule_no, elf490_rule_no_new, elf491);
						if (replace_remomo) {
							// 因為 elf491_crdate＜decideCrDate，為保險，仍先傳入
							// elf491_crdate
							retrialService.up491_at_490to491_match("U",
									elf491_list, c241m01z_list, elf491,
									crsRuleVO.getElf491_reportkind(), CrsUtil
											.get_month_last_date(elf491
													.getElf491_crdate()),
									elf490_rule_no, elf490_rule_no_new,
									crsRuleVO.getC241M01Z_reasonDesc()
											+ "(replace_remomo)");
						}
					}
				}
				/*
				 * TODO 當月剛撥款, 還沒到下個月的批次作業(未寫入ELF491), 就已先覆審的話 Q:若在2019-04-02撥款,
				 * 立即在2019-04-06覆審, 但要到2019-05的批次, 才會把「下次應覆審日」寫入, 這樣還需再覆審嗎? A:
				 * (1)若 2019-04-02撥款, 但 2019-04-25有異常通報, 若不再去一次, 似乎不合理 (2)在覆審當時,
				 * 帳務都正常, 但是在 [覆審日之後~月底]這段期間, 可能{把某1個帳號銷戶,
				 * 但額度未銷戶}{舊案本來只有1個十足擔保的帳號, 但客戶還款困難, 又另外拆出1個無擔的帳號} 區分不出來
				 * [覆審日之後~月底]這段期間 是否都正常？ 或是有異常？ (3)目前解法: 在2019-05之後, 用「控制檔維護」去變更
				 */
			} else {
				// 沒有 rule 符合,不異動ELF491
			}
		}
	}

	/**
	 * 增加判斷 Rule 是否已 "不適用"
	 */
	private boolean isElf491Rule_NotMatch_LNData(String elf490_rule_no,
			String elf490_rule_no_new, ELF491 elf491) {
		if (Util.isNotEmpty(elf490_rule_no_new) && Util.isEmpty(elf490_rule_no)) {
			String custId = Util.trim(elf491.getElf491_custid());
			String dupNo = Util.trim(elf491.getElf491_dupno());
			String brNo = elf491.getElf491_branch();
			String elf491_remomo = Util.trim(elf491.getElf491_remomo());
			// ================================
			CrsVO crsVO = new CrsVO();
			retrialService.fetch_LNF020_LNF030040(crsVO, custId, dupNo);
			// 判斷有哪些 LNF020 要排除
			List<Map<String, Object>> _filter_crsVO_getLNF020 = retrialService
					.filter_crsVO_getLNF020(crsVO.getLNF020(custId, dupNo),
							brNo);

			int lnap_1_cnt = 0;
			int lnap_2_cnt = 0;
			int lnap_3_cnt = 0;
			int lnap_4_cnt = 0;
			int lnap_5_cnt = 0;
			int lnap_6_cnt = 0;
			int lnap_tot_cnt = 0;

			if (CollectionUtils.isNotEmpty(_filter_crsVO_getLNF020)) {
				for (Map<String, Object> dataMap020 : _filter_crsVO_getLNF020) {
					String cntrNo = Util
							.trim(dataMap020.get("LNF020_CONTRACT"));

					for (Map<String, Object> dataMap030_040 : crsVO
							.getLNF030_040_withLNF020_LN_BR_NO_NoCharcCode30(
									cntrNo, brNo)) {
						String lnf030_loan_no = Util.trim(dataMap030_040
								.get("LNF030_LOAN_NO"));

						lnap_tot_cnt++;
						String lnap = CrsUtil
								.getSubjCodeFromLNF030_LOAN_NO(lnf030_loan_no);

						if (lnap.startsWith("1")) {
							++lnap_1_cnt;
						} else if (lnap.startsWith("2")) {
							++lnap_2_cnt;
						} else if (lnap.startsWith("3")) {
							++lnap_3_cnt;
						} else if (lnap.startsWith("4")) {
							++lnap_4_cnt;
						} else if (lnap.startsWith("5")) {
							++lnap_5_cnt;
						} else if (lnap.startsWith("6")) {
							++lnap_6_cnt;
						}
					}
				}
			}

			if (lnap_tot_cnt > 0) {
				Set<String> elf490ruleSet = CrsUtil.parseRule(
						Util.trim(elf490_rule_no_new)).keySet();
				if (!elf490ruleSet.contains(CrsUtil.R2)
						&& !elf490ruleSet.contains(CrsUtil.R4)
						&& (lnap_2_cnt == 0)) {
					// ELF491 短擔，但帳務檔查無「短擔」
					// 用 SELECT * FROM LN.LNF07A where LNF07A_KEY_1 ='CLS
					// REEXAMINATION' and LNF07A_KEY_2 in ('RULE 2','RULE 4')
					// 去區分出, RULE 2 及 RULE 4 為短期科目

					if (CrsUtil.isOnlyR2R4_noOthers(elf491_remomo)) {
						return true;
					}

					if (CrsUtil.isInR1R2R4_noOthers(elf491_remomo)) {
						return true;
					}
				}
			}
		}
		return false;
	}

	private Date _get_closet_from490(Date elf491_lrDate, Date baseDate,
			CrsRuleVO crsRuleVO) {
		/*
		 * 年月 分行 舊案類別 新案類別 010212 075 9 --- 於12月覆審 010301 075 9 010302 075 9 1
		 * 010303 075 9 1 010304 075 9 010305 075 9 010306 075 9 本來有符合1, 後來又不符合
		 * 在 7/1執行時, ELF490最大的ELF490_DATA_YM 為10306 若直接用 010306 去算, 可能太晚 在此狀況中,
		 * 用 010304 去算
		 */
		List<ELF490> elf490_list = misELF490Service.findCustIdRecord(CrsUtil
				.elf490YM_from_adDate(CapDate.addMonth(elf491_lrDate, 1)),
				CrsUtil.elf490YM_from_adDate(baseDate), crsRuleVO.getCustId(),
				crsRuleVO.getDupNo());
		String maxDataYM_New = "";
		String maxDataYM_All = "";
		for (ELF490 elf490 : elf490_list) {
			if (Util.notEquals(crsRuleVO.getBrNo(), elf490.getElf490_br_no())) {
				continue;
			}
			if (Util.isNotEmpty(Util.trim(elf490.getElf490_rule_no_new()))) {
				Set<String> ruleSet = CrsUtil.parseRule(
						elf490.getElf490_rule_no_new()).keySet();
				ruleSet.remove(CrsUtil.R8_1);// ELF490給的新案，可能有8-1，要排除
				if (ruleSet.size() > 0) {
					maxDataYM_New = elf490.getElf490_data_ym();
				}
			}
			maxDataYM_All = elf490.getElf490_data_ym();
		}

		if (Util.isNotEmpty(maxDataYM_New)
				&& Util.notEquals(maxDataYM_New, maxDataYM_All)) {
			return CapDate.addMonth(
					CrsUtil.elf490YM_to_adDate_d(maxDataYM_New), 1);
		}
		return null;
	}

	private void _initELF491(ELF491 o, String brNo, String custId, String dupNo) {
		o.setElf491_branch(brNo);
		o.setElf491_custid(custId);
		o.setElf491_dupno(dupNo);
		o.setElf491_maincust("");
		o.setElf491_llrdate(CapDate.parseDate(CapDate.ZERO_DATE));
		o.setElf491_lrdate(CapDate.parseDate(CapDate.ZERO_DATE));
		o.setElf491_crdate(CapDate.parseDate(CapDate.ZERO_DATE));// needSet
		o.setElf491_nckdflag("");
		o.setElf491_nckddate(CapDate.parseDate(CapDate.ZERO_DATE));
		o.setElf491_nckdmemo("");
		o.setElf491_canceldt(CapDate.parseDate(CapDate.ZERO_DATE));
		o.setElf491_updater("");// needSet
		o.setElf491_tmestamp(CapDate.getCurrentTimestamp());
		o.setElf491_uckdline("");
		o.setElf491_uckddt(CapDate.parseDate(CapDate.ZERO_DATE));
		o.setElf491_reportkind("");// needSet
		o.setElf491_remomo("");// needSet
		o.setElf491_newflag("");// needSet
		o.setElf491_8_1_flag("");
		o.setElf491_8_1_flag_o("");
		o.setElf491_lastRealDt(CapDate.parseDate(CapDate.ZERO_DATE));
	}

	/**
	 * Rule1, Rule3{05,07,52} 的產品代號 不重疊 但 Rule2{02,04,09,33,34,51}, Rule4{02}
	 * 的產品代號有重疊 所以把 Rule2, Rule4 特別編碼
	 */
	private Set<String> _encodeRule(TreeMap<String, String> map) {
		Set<String> r = new HashSet<String>();
		int cnt = 0;
		for (String ruleStr : map.keySet()) {
			if (ruleStr.equals(CrsUtil.R2) || ruleStr.equals(CrsUtil.R4)) {
				cnt++;
			} else {
				r.add(ruleStr);
			}
		}
		if (cnt > 0) {
			r.add(CrsUtil.R_COMBO_R2R4);
		}
		return r;
	}

	private void _run_Q_R1R2R4(CrsRuleVO crsRuleVO,
			boolean check_elf591_elf487_fg) {
		String brNo = crsRuleVO.getBrNo();
		String custId = crsRuleVO.getCustId();
		String dupNo = crsRuleVO.getDupNo();
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);

		if (CollectionUtils.isNotEmpty(crsRuleVO.getLNF020_CONTRACT())) {
			// 該 crsRuleVO 已執行過 _run_Q_R1R2R4
			return;
		}

		if (true) {
			/*
			 * 這裡只用 custId 及 dupNo 去抓
			 * 
			 * LNF020_LN_BR_NO LNF020_FACT_TYPE LNF020_CONTRACT LNF030_LOAN_NO
			 * LNF030_LOAN_CLASS --------------- ----------------
			 * --------------- -------------- ----------------- 062 51
			 * ************ ************** 10 062 51 ************ **************
			 * 56 062 31 ************ ************** 02 228 31 ************
			 * ************** 02
			 * 
			 * 本例客戶在 062BR(宜蘭),228BR(羅東) 有聯貸關係
			 * 
			 * 不論產生的覆審名單，是 062BR 或 228BR，都會以歸戶的金額去看
			 */
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> dataMap : misLNF030Service
					.selNewLnfData(idDup)) {
				String lnf020_bank_code = Util.trim(dataMap
						.get("LNF020_BANK_CODE"));
				String lnf020_fact_type = Util.trim(dataMap
						.get("LNF020_FACT_TYPE"));

				if (Util.notEquals(UtilConstants.兆豐銀行代碼, lnf020_bank_code)) {
					continue;
				}
				if (Util.equals(UtilConstants.Cntrdoc.snoKind.合作母,
						lnf020_fact_type)) {
					continue;
				}
				list.add(dataMap);
			}
			crsRuleVO.addLNData(list);
		}

		// TODO 以 custId 另抓出 cms 的所有額度, 做為補強
		/*
		 * 若 user 有 3 個額度, 其中 1, 3 已銷戶, 剩2有效。但在 cms 的已設定額度為 3(已銷戶) 會導致判斷異常
		 * 
		 * R1 不動產十足擔保 中長期 R2 不動產十足擔保 短期 R3 非不動產 中長期 R4 非不動產 短期
		 * 
		 * 若不落在 R1, R2. 應落在 R3,R4 只是分類錯誤, 但在覆審名單, 仍應出來
		 */
		for (Map<String, Object> m : eloandbcmsBASEService
				.getCrsCollCntrNoByCustIdDupNo(custId, dupNo)) {
			String cntrNo = Util.trim(MapUtils.getString(m, "CNTRNO"));
			if (!cntrNo.startsWith(crsRuleVO.getBrNo())) {
				continue;
			}
			if (crsRuleVO.getLNF020_CONTRACT().contains(cntrNo)) {
				// 已存在於帳務檔
				continue;
			}
			crsRuleVO.addCntrNo_onlyIn_C100S03A(cntrNo);
		}

		if (true) {
			Map<String, String> pgMap = new HashMap<String, String>();
			for (String cntrNo : crsRuleVO
					.getLNF020_CONTRACT_except_proj_class_69()) {
				List<String> lnap2_loanNoList = new ArrayList<String>();
				List<String> lnap46_loanNoList = new ArrayList<String>();
				List<String> lnap135_loanNoList = new ArrayList<String>();

				retrialService.classifyGuaranteeSubj(brNo,
						crsRuleVO.getLN_VO(), cntrNo, lnap2_loanNoList,
						lnap46_loanNoList, lnap135_loanNoList);
				if (true) {
					/*
					 * 在 for loop 的 cntrNo，包含借款人在[各個分行]的額度序號
					 * 
					 * 而判斷「有擔／無擔」是以 LNF030_LOAN_NO 的 3碼科目 是否為 2XX,4XX,6XX
					 * 
					 * 在 002BR 的覆審名單 ● 額度序號以 002 開頭 ● 額度序號以 005 開頭
					 * ，因聯貸子戶(31)，會在002撥款(LNF030_LOAN_NO以002開頭)
					 */
					if (lnap2_loanNoList.size() == 0
							&& lnap46_loanNoList.size() == 0
							&& lnap135_loanNoList.size() == 0) {
						// 無法區分【擔保(短)、擔保(中長)、非擔保】
						if (Util.equals(
								CrsUtil.getBrNoFromLNF020_CONTRACT(cntrNo),
								brNo)) {
							// 承上，在002BR中，只有LNF020，無LNF030 (ps.會被 decide 為
							// CrsUtil.PG_N 或 CrsUtil.PG_X)
						} else {
							// 承上，以 005 開頭的額度序號，底下沒有以002開頭的帳號
							continue;
						}
					}
				}
				List<Map<String, Object>> coll_list = eloandbcmsBASEService
						.getCrsCollInfoByCntrNo(cntrNo);

				/*
				 * 擔保品編號 <----> 額度 <----> 放款帳號
				 * 
				 * 在檢查資料時, 不應只以 放款帳號 的科目, 來區分「不動產十足擔保」 還要再判斷 cms 內的資料
				 */

				// 可能cms建檔資料錯誤,docStatus已設定,查無擔保品 而 subjectNo包含「擔保放款科目」
				if (CollectionUtils.isEmpty(coll_list)) {
					if (lnap2_loanNoList.size() > 0
							|| lnap46_loanNoList.size() > 0) {
						crsRuleVO
								.addCntrNo_guaranteeLNAP_butLost_CollMstr(cntrNo);
					} else {
						// 帳務都是 無擔科目, 且於CMS查無資料
						continue;
					}
				}

				// 可能擔保品為參貸他行(內容包括不動產...) 而 subjectNo為房屋購置擔保放款
				int not_pure_c01_cnt = 0;
				for (Map<String, Object> coll_item : coll_list) {
					if (Util.trim(coll_item.get("COLLNO")).startsWith("01")) {
						// ...
					} else if (Util.trim(coll_item.get("COLLNO")).startsWith(
							"06")) {

					} else {
						not_pure_c01_cnt++;
					}
				}

				if (not_pure_c01_cnt > 0) {
					pgMap.put(cntrNo, CrsUtil.PG_HAS_COLL___COLLNO_NOT_ONLY_01);
				} else {
					if (lnap135_loanNoList.size() == 0
							&& (lnap2_loanNoList.size() > 0 || lnap46_loanNoList
									.size() > 0)) {
						// 純不動產,全部是「有擔科目」

						pgMap.put(
								cntrNo,
								(lnap46_loanNoList.size() > 0) ? CrsUtil.PG_COLLNO_ONLY_01___LNPA_46_OR_246
										: CrsUtil.PG_COLLNO_ONLY_01___LNAP_ONLY_2);
					} else {
						// 純不動產,包括「無擔科目」
						pgMap.put(cntrNo,
								CrsUtil.PG_COLLNO_ONLY_01___LNAP_IN_135);
					}
				}
			}
			// 判斷R1,R2時, 是用 ELF491_BRANCH 在台北市、新北市 來判斷 1000萬/500萬
			crsRuleVO.set_cntrNo_catByCollAndLNAP_Map(pgMap);
		}

		/*
		 * 當已覆審完畢時，會{計算下次覆審日}、{上傳 ELF492} 當 check_elf591_elf487_fg==true 表示已覆審過,
		 * 只是尚未上傳 ELF492
		 */
		if (check_elf591_elf487_fg) {
			for (Map<String, Object> dataRow : misELF591Service
					.sel_never_retrialData_in_ELF591H(brNo, idDup)) {
				String cntrNo = MapUtils.getString(dataRow, "LNF030_CONTRACT");
				crsRuleVO.addCntrNo_never_retrialData_in_ELF591H(cntrNo);
			}		
			crsRuleVO
					.addCntrNo_never_retrialData_in_ELF487_HOUSE_or_RMBINS_or_HS_CRE_FG(misELF487Service
							.sel_never_retrialData_match___HOUSE_or_RMBINS_or_HS_CRE_FG(
									brNo, custId, dupNo));
		}
	}

	private void _run_Q_R5(CrsRuleVO crsRuleVO) {
		if (crsRuleVO.containsQ_R5()) {
			return;
		}
		List<Map<String, Object>> list = misLNF030Service
				.selByCustid_CrsR5(LMSUtil.getCustKey_len10custId(
						crsRuleVO.getCustId(), crsRuleVO.getDupNo()));
		boolean r5Match = (list.size() > 0);
		crsRuleVO.q_R5_markRun(r5Match);
	}

	private void _run_Q_R9(CrsRuleVO crsRuleVO) {
		if (crsRuleVO.containsQ_R9()) {
			return;
		}

		boolean r9Match = true;
		try {
			BigDecimal amt = misLNF030Service.selCrsR9_amt(LMSUtil
					.getCustKey_len10custId(crsRuleVO.getCustId(),
							crsRuleVO.getDupNo()), crsRuleVO.getBrNo());
			r9Match = (amt.compareTo(CrsUtil.R9_AMT) >= 0);
		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e));
		}
		crsRuleVO.q_R9_markRun(r9Match);
	}

	private String decide_crs_NewCust(String custId, String dupNo, Date cmp_date) {
		/*
		 * 若客戶在10年前已還款塗銷, 現在又新增貸款, 非屬 "首次往來之新貸戶"
		 */
		TreeSet<String> cntrNo_set = new TreeSet<String>();
		if (true) {
			// 同一 cntrNo 可能有30,31
			List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(custId,
					dupNo);
			for (MISLN20 lnf020 : lnf020_list) {
				cntrNo_set.add(lnf020.getLnf020_contract());
			}
		}
		if (true) {
			Set<String> open_ln_set = new TreeSet<String>();
			for (String cntrNo : cntrNo_set) {
				for (MISLN30 misLN30 : misLNF030Service.selByCntrNo(cntrNo)) {
					if (CrsUtil.isNOT_null_and_NOTZeroDate(misLN30
							.getLnf030_loan_date())
							&& LMSUtil.cmpDate(misLN30.getLnf030_loan_date(),
									"<", cmp_date)) {
						return "N";
					}
					if (CrsUtil.isNOT_null_and_NOTZeroDate(misLN30
							.getLnf030_open_date())
							&& LMSUtil.cmpDate(misLN30.getLnf030_open_date(),
									"<", cmp_date)) {
						return "N";
					}
					if (true) {
						open_ln_set.add(StringUtils.substring(Util.trim(TWNDate
								.toAD(misLN30.getLnf030_open_date())), 0, 7));

						if (open_ln_set.size() > 1) { // 可能 4月初建A帳號, 4月底建B帳號,
														// 5月又建C帳號
							return "N";
						}
					}
				}
			}
		}
		return "Y";
	}

	private void _runRuleLogic(String flag, CrsRuleVO crsRuleVO,
			String rule_no_new, String rule_no, String hasLrDate) {
		// 金門分行 因距離太遠, 授審處 在修訂作業辦法時, 有排除
		if (!Util.equals(crsRuleVO.getBrNo(), "079")
				&& !Util.equals(hasLrDate, "Y")) { // J-108-0078
			/*
			 * 已覆審過的案件(代表後續只會新案+6個月, 不會再有+3個月的情況), 已經在 !Util.equals(hasLrDate,
			 * "Y") 先排除掉了 剩下的情況 ● 全新客戶 。首次動撥, 應該只有一筆ELF490 。A額度已動撥過,
			 * B額度又動撥,ELF490 應會有N筆 ● 舊客戶, 但不需覆審[已銷戶 , 未銷戶] => 例如：不動產十足擔保且金額<500萬
			 * ================================================= 可能先建 lnf020 ,
			 * 但延遲X個月才去建 lnf030, 又延遲Y個月才去撥款
			 * ================================================= CrsRuleVO 裡面的
			 * CrsVO , 只會包含未銷戶的 LNF020, LNF030 => 若要抓已銷戶的lnData,另外查
			 */

			if (true) {
				// 2020-12-01 SP 通知
				// select * from mis.elf490 where elf490_data_ym between ? and ?
				// and elf490_cust_id = ? and elf490_dup_no = ? order by
				// elf490_br_no,elf490_data_ym with ur
				// 這一句執行'23238次
				// 自 J-109-0213 之後，消金覆審已取消新案3個月，恢復為新案6個月 => 將這段程式 mark 掉，未來不需再執行
				// String elf490_data_ym_E =
				// CrsUtil.elf490YM_from_adDate(CapDate.shiftDays(CrsUtil.get_sysMonth_1st(),
				// -1));
				// String elf490_data_ym_S =
				// CrsUtil.elf490YM_from_adDate(CapDate.addMonth(CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_E),
				// -6)) ;
				// Date cmp_date =
				// CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_S);
				//
				// List<ELF490> elf490_list =
				// misELF490Service.findCustIdRecord(elf490_data_ym_S,
				// elf490_data_ym_E, crsRuleVO.getCustId(),
				// crsRuleVO.getDupNo());
				// int cnt_elf490 = 0;
				// for(ELF490 elf490 : elf490_list){
				// if(Util.equals(crsRuleVO.getBrNo(),
				// elf490.getElf490_br_no())){
				// cnt_elf490++;
				// }
				// }
				//
				// if(cnt_elf490==1 && Util.equals("Y",
				// decide_crs_NewCust(crsRuleVO.getCustId(),
				// crsRuleVO.getDupNo(), cmp_date))){
				// crsRuleVO.setNewCustIn3M(true);
				// }
			}

			// J-109-0213 因覆審辦法修改，新案又由3個月恢復為6個月
			crsRuleVO.setNewCustIn3M(false);
		}
		
		ArrayList<CrsRuleLogic> ruleList_always = new ArrayList<CrsRuleLogic>();		
		ArrayList<CrsRuleLogic> ruleList_m6 = new ArrayList<CrsRuleLogic>();
		ArrayList<CrsRuleLogic> ruleList_m12 = new ArrayList<CrsRuleLogic>();
		/*
		 * 因部分規則是每1月覆審 ; 部分規則是每6個月覆審
		 * 
		 * 在判斷時 若已符合每1月覆審, 就不必再去跑每6個月覆審
		 */
		if (Util.equals("_batch_", flag)) {
			ruleList_always.add(new CrsR1_N_RuleLogic());
			// 類別1的舊案不覆審
			ruleList_always.add(new CrsR_COMBO_R2R4_N_RuleLogic());
			ruleList_m12.add(new CrsR_COMBO_R2R4_O_RuleLogic());
			ruleList_always.add(new CrsR3_N_RuleLogic());
			ruleList_m12.add(new CrsR3_O_RuleLogic());
			ruleList_always.add(new CrsR5_N_RuleLogic());
			ruleList_m12.add(new CrsR5_O_RuleLogic());
			ruleList_m6.add(new CrsR5_O_RuleLogic());
			// 6-1只有「新案」,而且團體消貸新案，於每案動用期間結束
			ruleList_always.add(new CrsR6_2_RuleLogic());
			ruleList_always.add(new CrsR7_RuleLogic());
			// 8-1用抽樣產生,不在 ELF490 轉 EFL491 產生
			ruleList_always.add(new CrsR8_2_N_RuleLogic());
			ruleList_m12.add(new CrsR8_2_O_RuleLogic());
			ruleList_always.add(new CrsR9_N_RuleLogic());
			ruleList_m12.add(new CrsR9_O2_RuleLogic());
			ruleList_always.add(new CrsR10_N_RuleLogic());
			ruleList_always.add(new CrsR11_N_RuleLogic());
			ruleList_always.add(new CrsR11_O2_RuleLogic());
			
			// J-113-0010 調整個人無擔保授信案件覆審規則
			ruleList_m6.add(new CrsR15_RuleLogic());
			
			ruleList_always.add(new CrsR99_O_RuleLogic());
		} else if (Util.equals("_produce_", flag)) {
			ruleList_always.add(new CrsR1_N_RuleLogic());
			ruleList_always.add(new CrsR_COMBO_R2R4_N_RuleLogic());
			ruleList_m12.add(new CrsR_COMBO_R2R4_O_RuleLogic());
		} else if (Util.equals("_endnext_", flag)) {

			// 類別1的舊案不覆審
			ruleList_m12.add(new CrsR_COMBO_R2R4_O2_RuleLogic());
			ruleList_m12.add(new CrsR3_O_RuleLogic());
			ruleList_m12.add(new CrsR5_O_RuleLogic());
			ruleList_m6.add(new CrsR5_O_RuleLogic());
			// 6-1只有「新案」,而且團體消貸新案，於每案動用期間結束
			ruleList_always.add(new CrsR6_2_Next_RuleLogic());
			ruleList_always.add(new CrsR7_RuleLogic());
			// 8-1用抽樣產生,不在 ELF490 轉 EFL491 產生
			ruleList_m12.add(new CrsR8_2_O_RuleLogic());
			ruleList_m12.add(new CrsR9_O_RuleLogic());
			// R11 另外判斷
			// R99 另外判斷
			
			// J-113-0010 調整個人無擔保授信案件覆審規則
			ruleList_m6.add(new CrsR15_RuleLogic());
		}
		Map<String, Set<String>> ruleMap = new HashMap<String, Set<String>>();
		ruleMap.put(CrsUtil.NEW_RULE,
				_encodeRule(CrsUtil.parseRule(Util.trim(rule_no_new))));
		ruleMap.put(CrsUtil.OLD_RULE,
				_encodeRule(CrsUtil.parseRule(Util.trim(rule_no))));

		for (CrsRuleLogic r : ruleList_always) {
			if (!ruleMap.containsKey(r.getKindNO())) {
				continue;
			}
			if (!ruleMap.get(r.getKindNO()).contains(r.getMatchRule())) {
				continue;
			}
			r.ruleLogic(crsRuleVO);
		}
		if ((crsRuleVO.rule_decided() == false)  
				|| crsRuleVO.getAddedMonth() > 6) {
			for (CrsRuleLogic r : ruleList_m6) {
				if (!ruleMap.containsKey(r.getKindNO())) {
					continue;
				}
				if (!ruleMap.get(r.getKindNO()).contains(r.getMatchRule())) {
					continue;
				}
				r.ruleLogic(crsRuleVO);
			}
		}

		if ((crsRuleVO.rule_decided() == false)
				|| crsRuleVO.getAddedMonth() > 12) {
			for (CrsRuleLogic r : ruleList_m12) {
				if (!ruleMap.containsKey(r.getKindNO())) {
					continue;
				}
				if (!ruleMap.get(r.getKindNO()).contains(r.getMatchRule())) {
					continue;
				}
				r.ruleLogic(crsRuleVO);
			}
		}		
	}

	// =================================================
	interface CrsRuleLogic {
		public boolean ruleLogic(CrsRuleVO crsRuleVO);

		public String getMatchRule();

		public String getKindNO();
	}

	class CrsR_COMBO_R2R4_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			// 拆分 R2, R4
			_run_Q_R1R2R4(crsRuleVO, true);
			if (crsRuleVO.rule2_match__and_adjAmt_over_threshold()) {
				new CrsR2_O_RuleLogic().ruleLogic(crsRuleVO);
			} else if (crsRuleVO.rule2_match__but_adjAmt_below_threshold()) {
				// 自2020-05 後，低於門檻的R2列入捜樣覆審

				/*
				 * Rule 2 案件 ● 金額超過門檻，舊案仍然隔1年覆審 ● 金額低於門檻，舊案隔年抽樣
				 */
			} else {
				// 不符合 Rule 2
			}

			// 當科目{206、207}
			if (crsRuleVO.rule4_match()) {
				new CrsR4_O_RuleLogic().ruleLogic(crsRuleVO);
			}
			return false;
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R_COMBO_R2R4;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR_COMBO_R2R4_O2_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			// 拆分 R2, R4
			_run_Q_R1R2R4(crsRuleVO, false);
			if (crsRuleVO.rule2_match__and_adjAmt_over_threshold()) {
				new CrsR2_O_RuleLogic().ruleLogic(crsRuleVO);
			} else if (crsRuleVO.rule2_match__but_adjAmt_below_threshold()) {
				// 自2020-05 後，低於門檻的R2列入捜樣覆審

				/*
				 * Rule 2 案件 ● 金額超過門檻，舊案仍然隔1年覆審 ● 金額低於門檻，舊案隔年抽樣
				 */
			} else {
				// 不符合 Rule 2
			}

			// 當科目{206、207}
			if (crsRuleVO.rule4_match()) {
				new CrsR4_O_RuleLogic().ruleLogic(crsRuleVO);
			}
			return false;
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R_COMBO_R2R4;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR2_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 12;// 舊案 每年 覆審
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR3_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 12;// 舊案 每年 覆審

			if (check_exclude_R3_R4(crsRuleVO)) {
				added = 0;
			}

			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R3;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR4_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 12;// 舊案 每年 覆審

			if (check_exclude_R3_R4(crsRuleVO)) {
				added = 0;
			}

			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R4;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR5_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R5(crsRuleVO);
			if (crsRuleVO.rule5_match()) {
				// added = 6;//舊案 每6個月 覆審
				added = 12; // J-110-0272 自110/05/03後改為12個月
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R5;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR6_2_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 1;// 團體消貸舊案，逾期戶 第1次出現6-2 => 預設1個月內 覆審

			String elf490_data_ym_E = misELF490Service.findMaxDataYM();
			String elf490_data_ym_S = CrsUtil.elf490YM_from_adDate(CapDate
					.addMonth(CrsUtil.elf490YM_to_adDate_d(elf490_data_ym_E),
							-1));
			List<ELF490> elf490_list = misELF490Service.findCustIdRecord(
					elf490_data_ym_S, elf490_data_ym_E, crsRuleVO.getCustId(),
					crsRuleVO.getDupNo());
			int cnt_6_2 = 0;
			for (ELF490 elf490 : elf490_list) {
				Set<String> ruleSet = CrsUtil.parseRule(
						Util.trim(elf490.getElf490_rule_no())).keySet();
				if (ruleSet.contains(CrsUtil.R6_2)) {
					cnt_6_2++;
				}
			}
			if (cnt_6_2 > 1) {
				added = 6;
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R6_2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;// R6-2 只有舊案才有可能
		}
	}

	class CrsR6_2_Next_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 6;// 覆審後，隔6個月再覆審
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R6_2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;// R6-2 只有舊案才有可能
		}
	}

	class CrsR7_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 1;// 本行員工消貸逾期戶 每1個月 覆審
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R7;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;// R7 只有舊案才有可能
		}
	}

	class CrsR8_2_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 12;// 舊案 每年 覆審
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R8_2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	class CrsR9_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R9(crsRuleVO);
			if (crsRuleVO.rule9_match()) {
				added = 12;// 舊案 每年 覆審
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R9;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	/**
	 * 中心判斷新案、舊案，是用[1]額度是否筆數變多、[2]同一額度序號，核准額度異動 來決定 但若是額度很早就核定，帳號也建了。但直到很晚才撥款
	 * 應以[撥款時點]當新案的時間點
	 * 
	 * 所以在此 method 補充判斷，當有符合 9 的舊案 也有可能是用+6個月
	 */
	class CrsR9_O2_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R9(crsRuleVO);
			if (crsRuleVO.rule9_match()) {
				added = 12;// 舊案 每年 覆審
				// ------------------
				// 當Rule9的額度，未曾被覆審過
				List<MISLN30> list = misLNF030Service.sel_CrsR9(LMSUtil
						.getCustKey_len10custId(crsRuleVO.getCustId(),
								crsRuleVO.getDupNo()), crsRuleVO.getBrNo());
				// J-110-0272 房貸案且申請信用評等模型評等為1級者免辦覆審
				if (is_houseLoan_and_grade_1___R9(list)) {
					added = 0;
				} else {
					Set<String> cntrNoSet = new HashSet<String>();
					Date cmpDate = CapDate.addMonth(CrsUtil.get_sysMonth_1st(),
							-1);
					for (MISLN30 o : list) {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(o
								.getLnf030_loan_date())
								&& LMSUtil.cmpDate(o.getLnf030_loan_date(),
										">=", cmpDate)) {
							cntrNoSet.add(Util.trim(o.getLnf030_contract()));
						}
					}

					boolean neverReview = false;
					for (String cntrNo : cntrNoSet) {
						List<ELF492> elf492_list = misELF492Service
								.selByBrNoCntrNo(crsRuleVO.getBrNo(), cntrNo);
						if (CollectionUtils.isEmpty(elf492_list)) {
							neverReview = true;
							break;
						}
					}
					if (neverReview) {
						added = 6;// TODO 比照新案，可能造成時間提前
					}
				}
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R9;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	/**
	 * 自 2017-07 起，消金需做土建融實地覆審
	 */
	class CrsR11_O2_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 12;
			ELF491 elf491 = misELF491Service.findByPk(crsRuleVO.getBrNo(),
					crsRuleVO.getCustId(), crsRuleVO.getDupNo());

			if (elf491 == null) {
				added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			} else {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
						.getElf491_lastRealDt())
						&& CrsUtil.isNOT_null_and_NOTZeroDate(elf491
								.getElf491_lrdate())
						&& LMSUtil.cmp_yyyyMM(elf491.getElf491_lastRealDt(),
								"==", elf491.getElf491_lrdate())) {
					// 上次覆審日==上次土建融實地覆審日
					added = 12;
				} else {
					// 可能
					// 上上次覆審日 2017-08, 上次覆審日 2017-12, 上次土建融實地覆審日=2017-08

					String realCkFg = _r11_realCkFg(crsRuleVO.getBrNo(),
							crsRuleVO.getCustId(), crsRuleVO.getDupNo());
					if (Util.equals("Y", realCkFg)) {
						// 在 elf490_to_elf491(...) 裡，已加入 "特殊判斷"
						//
						// 若新案符合, 用 baseDate 去加
						// 若舊案符合, 用 lrDate~current
						// 的ELF490(若中間因帳務異動變99,可在下一期調回適當的CrDate)
						//
						// 因為 R11 是 OLD_RULE, 會用 lrDate + added
						// 且 R11 會多用到 lastRealDt => 要多考量 lrDate ~ lastRealDt
						// 之間的時間差

						if (CrsUtil.isNull_or_ZeroDate(elf491
								.getElf491_lastRealDt())) {
							// 新案撥款後, 第2,3...個月的批次, 尚未進行土建融實地覆審
							added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
						} else {
							Date shouleReviewDate = null;						
							if (CrsUtil.isNOT_null_and_NOTZeroDate(elf491
									.getElf491_lastRealDt())
									&& LMSUtil.cmp_yyyyMM(CapDate.addMonth(
											elf491.getElf491_lastRealDt(), 12),
											">", CapDate.getCurrentTimestamp())) {

								shouleReviewDate = CapDate.addMonth(
										elf491.getElf491_lastRealDt(), 12);
							}

							if (CrsUtil
									.isNOT_null_and_NOTZeroDate(shouleReviewDate)) {
								Date crs_baseDate = elf491.getElf491_lrdate();
								if (CrsUtil.isNull_or_ZeroDate(crs_baseDate)) {
									crs_baseDate = CapDate.addMonth(
											CapDate.getCurrentTimestamp(), -1);
								}

								added = _r11_added(crs_baseDate,
										shouleReviewDate);
							}
						}
					} else if (Util.equals("O", realCkFg)) {
						added = 12;
					} else if (Util.equals("N", realCkFg)) {
						// 此狀況表示曾 match R11, 但後來把額度銷戶, 才會變成N
						// 由 覆審組人員 決定是否覆審(先不要由系統把 added 改為0, 仍維持 default 的 added
						// = 12)
					}
				}
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		private int _r11_added(Date crs_baseDate, Date shouleReviewDate) {
			String tmp_beg = StringUtils.substring(TWNDate.toAD(crs_baseDate),
					0, 7) + "-01";
			String tmp_end = StringUtils.substring(
					TWNDate.toAD(shouleReviewDate), 0, 7)
					+ "-01";

			if (LMSUtil.cmp_yyyyMM(CapDate.parseDate(tmp_end), ">",
					CapDate.parseDate(tmp_beg))) {
				int diff = CapDate.calculateMonths(tmp_end, tmp_beg,
						"yyyy-MM-dd", "yyyy-MM-dd");
				return diff;
			} else {
				return 1;
			}
		}

		/**
		 * @param brNo
		 * @param custId
		 * @param dupNo
		 * @return {Y表示土建融同業聯貸主辦, O:只有土建融聯貸參貸(非主辦), N:無土建融額度}
		 */
		private String _r11_realCkFg(String brNo, String custId, String dupNo) {
			List<C241M01B> c241m01b_list = new ArrayList<C241M01B>();
			for (Map<String, Object> lnMap : misLNF030Service
					.selNewLnfData(LMSUtil
							.getCustKey_len10custId(custId, dupNo))) {
				String LNF020_CONTRACT = Util.trim(MapUtils.getString(lnMap,
						"LNF020_CONTRACT"));
				String LNF020_SYND_TYPE = Util.trim(MapUtils.getString(lnMap,
						"LNF020_SYND_TYPE"));
				String LNF030_LOAN_NO = Util.trim(MapUtils.getString(lnMap,
						"LNF030_LOAN_NO"));
				String LNF030_LOAN_CLASS = Util.trim(MapUtils.getString(lnMap,
						"LNF030_LOAN_CLASS"));
				// ~~~
				C241M01B mock_c241m01b = new C241M01B();
				mock_c241m01b.setQuotaNo(LNF020_CONTRACT);
				mock_c241m01b.setSyndType(LNF020_SYND_TYPE);
				mock_c241m01b.setLoanNo(LNF030_LOAN_NO);
				mock_c241m01b.setLnType(LNF030_LOAN_CLASS);
				c241m01b_list.add(mock_c241m01b);
			}
			return CrsUtil.match_R11_syndType(brNo, c241m01b_list);
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R11;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}

	/**
	 * J-113-0010 調整個人無擔保授信案件覆審規則
	 *
	 */
	class CrsR15_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 6;// 每6個月 覆審
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R15;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;// R15 只有舊案
		}
	}
		
	/**
	 * R99 在實際覆審時, 會決定 c241m01a.specifyCycle,上傳至ELF491_MAINCUST
	 * 在覆審完成,上傳ELF491控制檔時
	 * 即用【本次實際覆審日+間隔(elf491_lrdate+elf491_maincust)】去算出elf491_crdate
	 */
	class CrsR99_O_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;
			ELF491 elf491 = misELF491Service.findByPk(crsRuleVO.getBrNo(),
					crsRuleVO.getCustId(), crsRuleVO.getDupNo());
			// oldRule 的不覆審註記 in('', 'A') 才需往下跑
			if (elf491 != null
					&& CrsUtil.isNckdFlag_EMPTY_A(elf491.getElf491_nckdflag())) {
				if (CrsUtil.isNull_or_ZeroDate(elf491.getElf491_lrdate())
						|| Util.isEmpty(elf491.getElf491_maincust())) {
					added = 1;// 無資料
				}
			}
			if (elf491 == null) {
				added = 1;
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R99;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.OLD_RULE;
		}
	}
	
	class CrsR1_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R1R2R4(crsRuleVO, true);
			if (crsRuleVO.rule1_newRule_match__and_adjAmt_over_threshold()) {
				added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			}
			String brNo = crsRuleVO.getBrNo();
			String custId = crsRuleVO.getCustId();
			String dupNo = crsRuleVO.getDupNo();
			// J-110-0272 房貸案且申請信用評等模型評等為1級者免辦覆審
			if (is_houseLoan_and_grade_1___R1(brNo, custId, dupNo)) {
				added = 0;
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R1;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR_COMBO_R2R4_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			// 拆分 R2, R4
			_run_Q_R1R2R4(crsRuleVO, true);

			if (crsRuleVO.rule2_match__and_adjAmt_over_threshold()) {
				new CrsR2_N_RuleLogic().ruleLogic(crsRuleVO);
			}

			// 當科目{206、207}
			if (crsRuleVO.rule4_match()) {
				new CrsR4_N_RuleLogic().ruleLogic(crsRuleVO);
			}
			return false;
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R_COMBO_R2R4;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR2_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR3_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			if (true) { // J-109-0119
						// 配合新冠肺炎紓困調整e-Loan授信覆審，如屬信保八成以上者，由系統判斷免除其首次覆審
				int cnt_69 = 0;
				int cnt_not69 = 0;
				List<MISLN20> lnf020_list = misMISLN20Service.findByCustId(
						crsRuleVO.getCustId(), crsRuleVO.getDupNo());
				for (MISLN20 lnf020 : lnf020_list) {
					String lnf020_bank_code = Util.trim(lnf020
							.getLnf020_bank_code());
					if (Util.notEquals(UtilConstants.兆豐銀行代碼, lnf020_bank_code)) {
						continue;
					}
					if (Util.equals(crsRuleVO.getBrNo(), CrsUtil
							.getBrNoFromLNF020_CONTRACT(lnf020
									.getLnf020_contract()))) {
						if (CrsUtil.isNOT_null_and_NOTZeroDate(lnf020
								.getLnf020_beg_date())
								&& LMSUtil
										.cmp_yyyyMM(
												CapDate.addMonth(lnf020
														.getLnf020_beg_date(),
														1), ">=", CapDate
														.getCurrentTimestamp())) {
							if (CrsUtil.is_69(lnf020.getLnf020_proj_class())) {
								++cnt_69;
							} else {
								++cnt_not69;
							}
						}
					}
				}
				if (cnt_69 > 0 && cnt_not69 == 0) { // 屬純紓困案
					added = 12;
				}
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R3;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR4_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R4;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR5_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R5(crsRuleVO);
			if (crsRuleVO.rule5_match()) {
				added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R5;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR8_2_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			logger.trace("CrsR8_2_N_RuleLogic match");
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R8_2;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR9_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = 0;

			_run_Q_R9(crsRuleVO);
			if (crsRuleVO.rule9_match()) {
				added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			}
			// J-110-0272 房貸案且申請信用評等模型評等為1級者免辦覆審
			List<MISLN30> list = misLNF030Service.sel_CrsR9(LMSUtil
					.getCustKey_len10custId(crsRuleVO.getCustId(),
							crsRuleVO.getDupNo()), crsRuleVO.getBrNo());
			if (is_houseLoan_and_grade_1___R9(list)) {
				added = 0;
			}
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R9;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR10_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R10;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	class CrsR11_N_RuleLogic implements CrsRuleLogic {
		@Override
		public boolean ruleLogic(CrsRuleVO crsRuleVO) {
			int added = crsRuleVO.isNewCustIn3M() ? 3 : 6;
			return crsRuleVO.decideCrDate(added, getMatchRule(), getKindNO());
		}

		@Override
		public String getMatchRule() {
			return CrsUtil.R11;
		}

		@Override
		public String getKindNO() {
			return CrsUtil.NEW_RULE;
		}
	}

	// J-110-0272
	private boolean is_houseLoan_and_grade_1___R1(String brNo, String custId,
			String dupNo) {
		Set<String> cntrNo_withStatusN_A_M = new HashSet<String>();
		if (true) {
			Set<String> elf488_status_arr = new HashSet<String>();
			elf488_status_arr.add("N");
			elf488_status_arr.add("A");
			elf488_status_arr.add("M");
			for (Map<String, Object> elf488_map : misELF488Service
					.sel_by_brNo_idDup(brNo, custId, dupNo, elf488_status_arr)) {
				String cntrNo = Util.trim(MapUtils.getString(elf488_map,
						"ELF488_CONTRACT"));
				// ~~~~~~~~~
				cntrNo_withStatusN_A_M.add(cntrNo);
			}
		}
		if (cntrNo_withStatusN_A_M.size() > 0) {
			/*
			 * 可能當月撥款N個帳號, 1個房貸, 1個非房貸
			 */
			Set<String> cntrNo_lnf154_house = new HashSet<String>();
			String[] house_loan_class = new String[] { "01", "02", "03", "04" };

			List<Map<String, Object>> lnf154_house_loan_list = misdbBaseService
					.lnf154_cls_all_loan(custId, dupNo);
			for (Map<String, Object> lnf154_map : lnf154_house_loan_list) {
				String _cntrNo = Util.trim(MapUtils.getString(lnf154_map,
						"LNF154_CONTRACT"));
				if (!Util.equals(CrsUtil.getBrNoFromLNF020_CONTRACT(_cntrNo),
						brNo)) {
					// 排除其它分行
					continue;
				}
				if (!cntrNo_withStatusN_A_M.contains(_cntrNo)) {
					continue; // 不判斷 舊案(不變) 的額度序號
				}

				String _LNF154_LOAN_CLASS = Util.trim(MapUtils.getString(
						lnf154_map, "LNF154_LOAN_CLASS"));
				if (CrsUtil.inCollection(_LNF154_LOAN_CLASS, house_loan_class)) {
					cntrNo_lnf154_house.add(_cntrNo);
				} else {
					return false; // 當月撥款的帳號，含{房貸}以外
				}
			}

			if (cntrNo_lnf154_house.size() > 0) {
				int cnt_grade1 = 0;
				int cnt_gradeOther = 0;
				for (String cntrNo : cntrNo_lnf154_house) {
					ELF459 elf459 = misELF459Service.findByCntrNo(cntrNo);
					String grade = Util.trim(elf459 == null ? "" : elf459
							.getElf459_grade());
					if (Util.equals(grade, "1")) {
						++cnt_grade1;
					} else {
						++cnt_gradeOther;
					}
				}
				if (cnt_grade1 > 0 && cnt_gradeOther == 0) {
					return true;
				}
			}
			// =====================================================
			/*
			 * Set<String> chose_rule_set = new HashSet<String>();
			 * chose_rule_set.add("RULE 1"); chose_rule_set.add("RULE 9"); //包含
			 * 603 或無擔的 503, 303 for(Map<String, Object> elf487_map :
			 * misELF487Service.sel_by_brNo_idDup(brNo, custId, dupNo)){ String
			 * _cntrNo = Util.trim(MapUtils.getString(elf487_map,
			 * "ELF487_CONTRACT"));
			 * if(!cntrNo_withStatusN_A_M.contains(_cntrNo)){ continue; }
			 * 
			 * String _rule_no = Util.trim(MapUtils.getString(elf487_map,
			 * "ELF487_RULE_NO")); if(chose_rule_set.contains(_rule_no)){
			 * 
			 * }else{ //包含非屬房貸的rule，可能是 {行家理財} return false; } }
			 */
		}

		return false;
	}

	private boolean is_houseLoan_and_grade_1___R9(List<MISLN30> list) {
		Set<String> cntrNoSet = new HashSet<String>();
		for (MISLN30 lnf030 : list) {
			cntrNoSet.add(Util.trim(lnf030.getLnf030_contract()));
		}

		int cnt_grade1 = 0;
		int cnt_gradeOther = 0;
		for (String cntrNo : cntrNoSet) {
			ELF459 elf459 = misELF459Service.findByCntrNo(cntrNo);
			String grade = Util.trim(elf459 == null ? "" : elf459
					.getElf459_grade());
			if (Util.equals(grade, "1")) {
				++cnt_grade1;
			} else {
				++cnt_gradeOther;
			}
		}
		if (cnt_grade1 > 0 && cnt_gradeOther == 0) {
			return true;
		}

		return false;
	}

	// 新臺幣一千萬元以下，且為十足擔保授信或經信用保證基金保證成數七成以上者，除新做、增額案件依第六條第一項規定辦理覆審外，其餘案件依下列規定辦理：(一)
	// 單一額度均為不循環動用額度者，免再辦理覆審。
	private boolean check_exclude_R3_R4(CrsRuleVO crsRuleVO) {
		String brNo = crsRuleVO.getBrNo();
		String custId = crsRuleVO.getCustId();
		String dupNo = crsRuleVO.getDupNo();
		String idDup = LMSUtil.getCustKey_len10custId(custId, dupNo);

		Integer rule3_4Count = 0;
		Integer otherCount = 0;

		boolean neverReview = false;
		List<MISLN20> lnf020_list = misMISLN20Service.findByCustId_R3R4(custId,
				dupNo);

		for (Map<String, Object> elf487_map : misELF487Service
				.sel_by_brNo_idDup(brNo, custId, dupNo)) {
			String lef487_rule_no = Util.trim(elf487_map.get("ELF487_RULE_NO"));
			if (Util.equals(lef487_rule_no, "RULE 3")
					|| Util.equals(lef487_rule_no, "RULE 4")) {
				rule3_4Count++;
			} else if (Util.equals(lef487_rule_no, "RULE 12")) {

			} else if (Util.equals(lef487_rule_no, "RULE 15")) {
				// J-113-0519_07623_B1001 e-Loan授信覆審作業系統之覆審名單篩選條件修正
			} else if (Util.equals(lef487_rule_no, "RULE 8")) {
				String factType = misMISLN20Service.findFactType(custId, dupNo,
						Util.trim(elf487_map.get("ELF487_CONTRACT")));
				if (Util.equals(factType, "50")) {
					// 因信保會有擔保額度、無擔額度，有可能RULE 8 是信保的無擔額度，不計算otherCount
				} else {
					otherCount++;
				}
			} else {
				otherCount++;
			}
		}
		// 判斷只有rule3、4，才檢查是否可免覆審，排除勞工紓困不算複雜rule，若有其他rule則為複雜邏輯不進入此免覆審判斷
		if (rule3_4Count > 0 && otherCount == 0) {
			for (MISLN20 lnf020 : lnf020_list) {
				String lnf020_fact_type = lnf020.getLnf020_fact_type();
				BigDecimal lnf020_ipfd_rate = lnf020.getLnf020_ipfd_rate();
				BigDecimal lnf020_fact_amt = lnf020.getLnf020_fact_amt();
				String lnf020_revolve = lnf020.getLnf020_revolve();

				List<ELF492> elf492List = misELF492Service.selByBrNoCntrNo(
						brNo, lnf020.getLnf020_contract());
				// 判斷覆審過的案件才可進入免覆審判斷
				if (elf492List.size() > 0) {
					// 同一分行、不循環動用額度者
					if (Util.trim(lnf020.getLnf020_ln_br_no()).equals(brNo)) {
						if (lnf020_revolve.equals(UtilConstants.DEFAULT.否)
								&& lnf020_fact_amt.compareTo(new BigDecimal(
										"10000000")) <= 0) {
							// 信保:50、保證成數7成以上、1000萬以下，可免覆審
							if (Util.equals(lnf020_fact_type, "50")
									&& lnf020_ipfd_rate
											.compareTo(new BigDecimal("70")) >= 0) {
								neverReview = true;
							}
							// 當他有多個額度序號，1000萬以上的股票擔保，還是需要覆審
							else {
								return false;
							}
						} else {
							return false;
						}
					}
				}
			}
		}

		return neverReview;
	}

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param c241m01a
	 * @return
	 */
	String getApprIdForElf492(C241M01A c241m01a) {
		String elf492_apprId = "";
		List<C241M01E> c241m01e_list = retrialService
				.findC241M01E_c241m01a(c241m01a);

		// BRANCHTYPE
		// static final String 受檢單位 = "1";
		// static final String 覆審單位 = "2";
		List<C241M01E> c241m01e_L1 = retrialService
				.findC241M01E_byBranchTypeStaffJob(c241m01e_list, "2", "L1");

		if (CollectionUtils.isNotEmpty(c241m01e_L1)) {
			elf492_apprId = c241m01e_L1.get(0).getStaffNo();
		}

		return elf492_apprId;
	}

}
