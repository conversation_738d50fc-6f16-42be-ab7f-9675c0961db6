/* 
 *  LMS1605FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.editDoc;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01E;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.model.L210A01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.eloan.lms.model.VLUSEDOC01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 動用審核表
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          <li>2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase
 *          才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
 *          </ul>
 */
@Scope("request")
@Controller("lms1605m01formhandler")
@DomainClass(L160M01A.class)
public class LMS1605M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	LMS2105Service lms2105Service;

	@Resource
	DocLogService docLogService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	BranchService branchService;

	@Resource
	LMSService lmsService;

	@Resource
	MisdbBASEService misdbBASEService;
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	DwLnquotovService dwLnquotovService;
	
	@Resource
	MisELF442Service misELF442Service;
	
	@Resource
	MisMISLN20Service misMISLN20Service;

	@Resource
	CodeTypeService codeTypeService;

	// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
	@Resource
	MisElCUS25Service misElcus25Service;

	@Resource
	MisElcrcoService misElcrcoService;

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	ICustomerService iCustomerService;

	@Resource
	MisELLNGTEEService misEllngteeService;

	public static final Long ZERO = Long.valueOf(0);

	/**
	 * 是否要出現呈總行的按鈕
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranchData(PageParameters params)
			throws CapException {
		// select PARENTBRNO from COM.BELSBRN where BRNO=? with ur
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A meta = lms1205Service.findL120m01aByMainId(mainId);
		IBranch ibranch = branchService.getBranch(user.getUnitNo());
		// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
		if (Util.isEmpty(Util.trim(ibranch.getParentBrNo()))) {
			result.set("showSendBranch", false);
		} else {
			result.set("showSendBranch", true);
		}

		// 取得目前登錄分行國別
		result.set("country", ibranch.getCountryType());

		// 加會營運中心單位
		result.set(
				"areaBrId",
				(meta == null) ? UtilConstants.Mark.SPACE : Util.trim(meta
						.getAreaBrId()));

		// 呈主管種類(呈主管審核批覆OR呈主管覆核)-- 特殊分行退回會簽意見使用
		result.set("spectialFlag", Util.trim(meta.getPackLoan()));
		return result;
	}

	/**
	 * 查詢所選銀行底下的分行
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBranch(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainBranch = Util.trim(params.getString("mainBranch"));
		Map<String, String> m = new TreeMap<String, String>();
		// 如果是兆豐銀行查詢的位置不一樣
		if (UtilConstants.兆豐銀行代碼.equals(mainBranch)) {
			// 抓需要的銀行代碼
			List<IBranch> bank = branchService.getAllBranch();
			for (IBranch b : bank) {
				String brName = Util.trim(b.getBrName());
				String brCode = b.getBrNo();
				m.put(brCode, brName);
			}
		} else {
			List<Map<String, Object>> rows = misdbBASEService
					.findMISSynBank(Util.trim(mainBranch));
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				m.put(code, name);
			}

		}
		result.set("brankList", new CapAjaxFormResult(m));
		return result;
	}

	/**
	 * 取得聯貸案已編碼國外銀行的清單
	 * 
	 * @param params
	 *            type 12-國外分行 99 -其他(由國外部徵信系統金融機構資料維護)
	 * 
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * 
	 *         foreignBranch 國外銀行清單
	 * 
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryforeignBranch(PageParameters params)
			throws CapException {
		String type = Util.trim(params.getString("type"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		Map<String, String> brank = new TreeMap<String, String>();
		if ("12".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISELFBKSNOBank();

			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("NUMBER")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		} else if ("99".equals(type)) {
			List<Map<String, Object>> rows = this.misdbBASEService
					.findMISSynBankBy99();
			for (Map<String, Object> dataMap : rows) {
				String code = Util.trim(((String) dataMap.get("CODE")));
				String name = Util.trim(((String) dataMap.get("NAME")));
				brank.put(code, name);
			}
		}
		result.set("foreignBranch", new CapAjaxFormResult(brank));
		return result;
	}

	/**
	 * 查詢登錄 -先行動用呈核及控制表
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLogeIN(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
		CapAjaxFormResult result = DataParse.toResult(l160m01a);
		result = formatResultShow(result, l160m01a, 0);
		L163S01A l163s01a = l160m01a.getL163S01A();
		result.set("waitingItem", l163s01a.getWaitingItem());
		result.set("willFinishDate", l163s01a.getWillFinishDate());
		result.set("managerId", l163s01a.getManagerId());
		result.set("finishDate", l163s01a.getFinishDate());
		result.set("itemTrace", l163s01a.getItemTrace());
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 查詢黑名單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBlackInit(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);
		List<L160M01B> l160m01bs = (List<L160M01B>) lms1605Service
				.findListByMainId(L160M01B.class, mainId);

		ArrayList<String> mainIds = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			mainIds.add(l160m01b.getReMainId());
		}

		List<L140M01A> l140m01as = lms1405Service
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));
		JSONObject blackPage;
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		Boolean checkBlack = false;
		StringBuffer temp = new StringBuffer();
		HashMap<String, String> custIdMap = new HashMap<String, String>();
		String key = "";
		for (L140M01A l140m01a : l140m01as) {
			key = l140m01a.getCustId().toUpperCase()
					+ l140m01a.getDupNo().toUpperCase();
			// 判斷是否已經處理過這借款人資料
			if (custIdMap.containsKey(key)) {
				continue;
			}
			custIdMap.put(key, "");
			Map<String, Object> custData = misCustdataService
					.findAllByByCustIdAndDupNo(l140m01a.getCustId()
							.toUpperCase(), l140m01a.getDupNo().toUpperCase());
			if (custData == null || custData.isEmpty()) {
				// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
				HashMap<String, String> param = new HashMap<String, String>();
				param.put("custId", l140m01a.getCustId().toUpperCase() + " "
						+ l140m01a.getDupNo().toUpperCase());
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
			}
			String eName = (String) custData.get("ENAME");
			String cName = (String) custData.get("CNAME");
			if (Util.isEmpty(Util.trim(eName))) {
				result.set("showBox", true);
				return result;
			} else {
				try {
					blackPage = lms1605Service.findBlackPage(eName, "");
				} catch (GWException t1) {
					logger.error(t1.getMessage());
					throw t1;
				} catch (Exception e) {
					logger.error(
							"[queryBlackPage] lms1605Service.findBlackPage EXCEPTION!!",
							e);
					HashMap<String, String> param = new HashMap<String, String>();
					param.put("dsName", "MQ");
					// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
				}

				JSONArray record = blackPage.getJSONArray("RECORD");
				if (record.isEmpty()) {
					// L160M01A.message39=未列於黑名單
					String msg = pop.getProperty("L160M01A.message39");
					temp.append(temp.length() > 0 ? "\r" : "");
					temp.append(l140m01a.getCustId().toUpperCase()).append(" ");
					temp.append(eName);
					if (!Util.isEmpty(Util.trim(cName))) {
						temp.append("【").append(cName).append("】 ");
					}
					temp.append(" ").append(msg);

				} else {

					checkBlack = true;
					// L160M01A.message41=可能是黑名單
					String msg = pop.getProperty("L160M01A.message41");
					temp.append(temp.length() > 0 ? "\r" : "");
					temp.append(l140m01a.getCustId().toUpperCase()).append(" ");
					temp.append(eName);
					if (!Util.isEmpty(Util.trim(cName))) {
						temp.append("【").append(cName).append("】 ");
					}
					temp.append(" ").append(msg);

				}
			}
		}

		if (checkBlack) {
			// L160M01A.message44=有可能是黑名單,請執行BTT-身份異常查詢及維護(0015)交易確認是否有列於黑名單中。
			result.set(
					CapConstants.AJAX_NOTIFY_MESSAGE,
					RespMsgHelper.getMessage("EFD0015", pop.getProperty("L160M01A.message44")));
		}
		if (l160m01a != null) {
			l160m01a.setBlackListTxtOK(temp.toString());
			l160m01a.setBlackDataDate(new Date());
			lms1605Service.save(l160m01a);
		}
		result.set("blackListTxtOK", temp.toString());
		result.set("blackDataDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		return result;
	}

	/**
	 * 查詢黑名單 手動輸入
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryBlackPage(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String name = params.getString("name");
		JSONObject blackPage;
		try {
			blackPage = lms1605Service.findBlackPage(name, "");
		} catch (GWException t1) {
			logger.error(t1.getMessage());
			throw t1;
		} catch (Exception e) {
			logger.error(
					"[queryBlackPage] lms1605Service.findBlackPage EXCEPTION!!",
					e);
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("dsName", "MQ");
			// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		JSONArray record = blackPage.getJSONArray("RECORD");
		if (record.isEmpty()) {
			// L160M01A.message39=未列於黑名單
			String msg = pop.getProperty("L160M01A.message39");
			result.set("blackListTxtOK", name + " " + msg);

		} else {
			// L160M01A.message41=可能是黑名單
			String msg = pop.getProperty("L160M01A.message41");
			result.set("blackListTxtOK", name + " " + msg);
		}

		result.set("blackDataDate",
				CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
		return result;

	}

	/**
	 * 查詢L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL160m01a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class,
					oid);
			result = formatResultShow(result, l160m01a, page);

		} else {

			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
		}

		return result;

	}

	/**
	 * 查詢L161S01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL161s01b(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		L161S01B l161s01b = lms1605Service.findModelByOid(L161S01B.class, oid);
		if (!Util.isEmpty(l161s01b)) {
			result = DataParse.toResult(l161s01b);
		}
		return result;
	}

	/**
	 * 查詢L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL162m01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L162S01A l162m01a = lms1605Service.findModelByOid(L162S01A.class, oid);
		if (!Util.isEmpty(l162m01a)) {
			result = DataParse.toResult(l162m01a);
			result.set("custIdSelect", l162m01a.getCustId().toUpperCase()
					+ l162m01a.getDupNo().toUpperCase());

			// J-107-0070-001 Web e-Loan
			// 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			L164S01A l164s01a = lms1605Service.findL164s01aByMainIdCustId(
					l162m01a.getMainId(), l162m01a.getRId(),
					l162m01a.getRDupNo());
			if (l164s01a != null) {
				result.putAll(DataParse.toResult(l164s01a, DataParse.Need,
						new String[] { "chairmanId", "chairmanDupNo",
								"chairman", "beneficiary", "seniorMgr" }));

			}
			String banktype = "";
			//參數控制是否卡保證金額上限、當地客戶識別ID，海外分行才能填
			String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
			if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
				if(Util.isNotEmpty(l162m01a.getCntrNo())){
					if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(l162m01a.getCntrNo().substring(0, 3))
							.getBrNoFlag())) {
						banktype = TypCdEnum.海外.getCode();
					}
				}
			}
			result.set("banktype", banktype);
		} else {

		}

		return result;
	}

	/**
	 * 引進額度明細表 L140M01A 動用的額度序號 並將額度序號儲存到L160M01B
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryL140m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String oid = params.getString(EloanConstants.OID);
		String type = params.getString("type");
		String dataSrc = params.getString("dataSrc");
		L160M01A l160m01a = null;
		if (Util.isEmpty(oid)) {
			l160m01a = new L160M01A();
			l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l160m01a.setOwnBrId(user.getUnitNo());
			l160m01a.setMainId(IDGenerator.getUUID());
			l160m01a.setApprId(user.getUserId());
			l160m01a.setTType("1");
			l160m01a.setUseSelect("1");
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l160m01a.setTxCode(txCode);
			// UPGRADE: 待確認，URL是否正確
			l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1605M01Page.class));

			// 複製L901M01A審核項目
			List<L160M01C> l160m01cs = copyL901m01aToL160m01c(
					l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
			lms1605Service.saveL160m01cList(l160m01cs);
		} else {
			l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
		}

		// 所選擇的案件簽報書mainId
		String caseMainId = params.getString("caseMainId");
		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(caseMainId);
		if (Util.isEmpty(l120m01a)) {
			logger.debug("\n l120m01a is =====> null");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a.getDocKind())) {
			List<L120M01F> l120m01fs = lms1205Service
					.findL120m01fByMainId(caseMainId);

			List<L160M01D> models = (List<L160M01D>) lms1605Service
					.findListByMainId(L160M01D.class, l160m01a.getMainId());
			if (!models.isEmpty()) {
				lms1605Service.deleteL160m01ds(models, true);
			}
			List<L160M01D> newL1160M01ds = new ArrayList<L160M01D>();
			for (L120M01F l120m01f : l120m01fs) {
				if (UtilConstants.BRANCHTYPE.授管處.equals(l120m01f
						.getBranchType())) {
					if (UtilConstants.STAFFJOB.經辦L1.equals(l120m01f
							.getStaffJob())
							|| UtilConstants.STAFFJOB.授信主管L3.equals(l120m01f
									.getStaffJob())) {
						L160M01D l160m01d = new L160M01D();
						l160m01d.setMainId(l160m01a.getMainId());
						l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
						l160m01d.setCreator(user.getUserId());
						if (UtilConstants.STAFFJOB.經辦L1.equals(l120m01f
								.getStaffJob())) {
							l160m01d.setStaffJob("L6");
						} else {
							l160m01d.setStaffJob("L7");
						}

						l160m01d.setStaffNo(l120m01f.getStaffNo());
						l160m01d.setUpdater(user.getUserId());
						l160m01d.setUpdateTime(CapDate.getCurrentTimestamp());
						newL1160M01ds.add(l160m01d);
					}

				}
			}
			if (!newL1160M01ds.isEmpty()) {
				lms1605Service.saveL160m01dList(newL1160M01ds);
			}

		}
		HashMap<String, String> custContry = lms1605Service
				.getCustCounty(l120m01a);

		l160m01a.setRandomCode(IDGenerator.getRandomCode());

		// 再重新引進時 之前引進的資料
		lms1605Service.deleteListReInclude(l160m01a.getMainId());

		// String useBrId = user.getUnitNo();
		String itemType = lmsService.checkL140M01AItemType(l120m01a);
		List<VLUSEDOC01> vusedoc01s = null;
		// List<L140M01A> l140m01as = null;
		// 如果是全部動用將該簽報書的所有額度序號放進L160M01B

		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		List<String> brnoList = new ArrayList<String>();
		brnoList.add(user.getUnitNo());
		String[] useBrId = brnoList.toArray(new String[brnoList.size()]);

		if ("all".equals(type)) {

			vusedoc01s = lms1605Service.getDoCntrNo(caseMainId, useBrId,
					itemType);

			if (vusedoc01s.isEmpty()) {
				logger.info("not find l140m01as");
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				HashMap<String, String> param = new HashMap<String, String>();
				// L160M01A.message49=查無額度明細表
				param.put("msg", pop.getProperty("L160M01A.message49"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			l160m01a.setAllCanPay(UtilConstants.DEFAULT.是);

		} else {
			String[] selectCntrNo = params.getStringArray("selectCntrNo");
			vusedoc01s = lms1605Service.getDoCntrNo(caseMainId, selectCntrNo,
					useBrId, itemType);
			l160m01a.setAllCanPay(UtilConstants.DEFAULT.否);
		}
		// 檢查要動用的額度明細表是否再簽約未動用被註記為取消
		for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
			String mainId = vusedoc01.getMainId();
			Map<String, Object> rowData = eloandbBASEService
					.findL230S01AMaxDATADATE(mainId,
							CreditDocStatusEnum.海外_已核准.getCode());

			if (rowData != null
					&& UtilConstants.NoUseCase.NuseMemo.不簽約註銷額度.equals(rowData
							.get("NUSEMEMO"))) {
				Date dataDate = (Date) rowData.get("DATADATE");
				String cntrNo = (String) rowData.get("CNTRNO");
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				// L160M01A.message54=額度序號 {0} 已由未簽約/動用報送作業於
				// {1}註記為【不簽約-註銷額度】不得動用，請重新選擇額度明細表後再執行動用作業!!
				String errorMsg = MessageFormat.format(
						pop.getProperty("L160M01A.message54"), cntrNo,
						Util.trim(dataDate));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, errorMsg), getClass());
			}
		}

		// 將引進的案件簽報書資料複製到動審表
		l160m01a = copyL120m01aToL160m01a(l160m01a, l120m01a);
		String mainMainId = l160m01a.getMainId();
		// 存放動用額度序號
		List<L160M01B> newL160m01bs = new ArrayList<L160M01B>();

		// 避免重複的額度序號
		HashMap<String, String> cntrNoMap = new HashMap<String, String>();
		ArrayList<String> selectMainId = new ArrayList<String>();
		// 取得該額度明細表 對應 額度序號
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
			String cntrNo = Util.trim(vusedoc01.getUseCntrNo());
			// 需為該分行額度序號才可動用
			// if (!user.getUnitNo().equals(cntrNo.substring(0, 3))) {

			// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
			// 進出口小行可以幫大行作動審表
			if (!brnoList.contains(cntrNo.substring(0, 3))) {
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				HashMap<String, String> param = new HashMap<String, String>();
				param.put("msg", pop.getProperty("L160M01A.message45"));
				// L160M01A.message45=不得動用額度序號非本行之額度！
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			if (cntrNoMap.containsKey(cntrNo)) {
				continue;
			}
			mainIdmapingCntrno.put(vusedoc01.getMainId(), cntrNo);
			cntrNoMap.put(cntrNo, "");
			selectMainId.add(vusedoc01.getMainId());
			L160M01B l160m01b = new L160M01B();
			l160m01b.setCntrNo(cntrNo);
			l160m01b.setMainId(mainMainId);
			l160m01b.setReMainId(vusedoc01.getMainId());
			l160m01b.setCreator(user.getUserId());
			l160m01b.setCreateTime(CapDate.getCurrentTimestamp());
			newL160m01bs.add(l160m01b);

		}
		List<L140M01A> l140m01as = lms1405Service
				.findL140m01aListByMainIdList(selectMainId
						.toArray(new String[selectMainId.size()]));
		List<L162S01A> newL162m01as = new ArrayList<L162S01A>();

		// 2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase 才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
		boolean haveUnitLoanCase = false;
		// 動審表引進時，動用期限要帶入第一筆額度明細表的動用期限
		Date useFromDate = null;
		Date useEndDate = null;

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> newL164s01as = new ArrayList<L164S01A>();
		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();
		Map<String, String> processedCustId = new HashMap<String, String>();
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
					.findListByMainId(L120S01B.class, l120m01a.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				if (l120s01b != null) {
					// 複製借款人基本資料 L120S01B L120S01A 到 L164S01A
					newL164s01as.add(copyL120s01bToL164s01a(l120s01b,
							mainMainId));

					// 複製借款人實質受益人資料 L120S01P(簽報書MAINID)->L120S01P(動審表MAINID)
					newL120s01ps.addAll(copyL120s01pToNewL120s01p(l120s01b,
							mainMainId));
				}
			}
		} else {
			// 個金
		}

		for (L140M01A l140m01a : l140m01as) {

			String cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());

			// 當本案在額度種類設定為同業聯貸且額度明細表也有選同業聯貸案額為Y才需打同業聯貸
			if ("Y".equals(l160m01a.getUnitLoanCase())
					&& "Y".equals(Util.trim(l140m01a.getUnitCase2()))) {
				haveUnitLoanCase = true;
			}

			// 複製連保人資料到主從債務人
			newL162m01as.addAll(copyL140m01IToL162m01a(l140m01a, mainMainId,
					custContry, cntrNo));

			// 產生L161S01A 額度動用資訊明細 ***************

			L120M01B l120m01b = lms1405Service.findL120m01bByUniqueKey(l120m01a
					.getMainId());

			// 引進聯行攤貸比例
			Set<L140M01E> l140m01es = l140m01a.getL140m01e();

			if (l120m01b == null) {
				l120m01b = new L120M01B();
				l120m01b.setUnitCase("N");
				l120m01b.setUnitMega(l140m01es.isEmpty() ? "N" : "Y");
				l120m01b.setCoKind("0");
			}

			L161S01A l161s01a = lms1605Service.findL161m01aByMainIdCntrno(
					l160m01a.getMainId(), cntrNo);

			if (Util.isEmpty(l161s01a)) {
				l161s01a = new L161S01A();
				l161s01a.setCreator(user.getUserId());
				l161s01a.setCreateTime(l140m01a.getCreateTime());
				l161s01a.setMainId(l160m01a.getMainId());
				l161s01a.setUid(IDGenerator.getUUID());
			}

			l161s01a.setCustId(l140m01a.getCustId());
			l161s01a.setDupNo(l140m01a.getDupNo());
			l161s01a.setProperty(l140m01a.getProPerty());
			l161s01a.setCurrentApplyCurr(l140m01a.getCurrentApplyCurr());
			//G-113-0036 額度序號前三碼與簽案中額度序號前三碼不一致時，則帶入分配參帶金額
			//於處理L140M01E、L140M01E_AF時更新
			l161s01a.setCurrentApplyAmt(l140m01a.getCurrentApplyAmt());
			l161s01a.setCntrMainId(l140m01a.getMainId());
			l161s01a.setSnoKind(l140m01a.getSnoKind());
			l161s01a.setPrintSeq(l140m01a.getPrintSeq());
			l161s01a.setCntrNo(cntrNo);
			l161s01a.setUseSpecialReason("00");

			Map<String, String> caseMap = lmsService.getCaseType("2",
					l120m01a.getMainId(), l120m01b, l140m01a);

			String tmpUnitCase = caseMap.get("tmpUnitCase");
			String tmpMainBranch = caseMap.get("tmpMainBranch");
			String caseType = caseMap.get("caseType");

			l161s01a.setCaseType(caseType);

			l161s01a.setUnitCase(tmpUnitCase);

			if (Util.equals(tmpUnitCase, "Y")) {
				l161s01a.setUCMainBranch(Util.isEmpty(l120m01b) ? "N"
						: l120m01b.getUCMainBranch());
				l161s01a.setUCntBranch(tmpMainBranch);
				l161s01a.setUCMSBranch(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUCMSBranch());
				l161s01a.setUHideName(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUHideName());
				l161s01a.setUArea(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getUArea());
				l161s01a.setURP1(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP1());
				l161s01a.setURP2(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP2());
				l161s01a.setURP3(Util.isEmpty(l120m01b) ? "N" : l120m01b
						.getURP3());
			} else {
				l161s01a.setUCMainBranch("N");
				l161s01a.setUCntBranch("N");
				l161s01a.setUCMSBranch("N");
				l161s01a.setUHideName("N");
				l161s01a.setUArea("N");
				l161s01a.setURP1("N");
				l161s01a.setURP2("N");
				l161s01a.setURP3("N");
			}

			l161s01a.setUnitMega("N");
			if (Util.equals(UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸, caseType)
					|| Util.equals(UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸,
							caseType)
					|| Util.equals(UtilConstants.Usedoc.caseType.自行聯貸, caseType)) {
				l161s01a.setUnitMega("Y");

			}

			l161s01a.setCoKind(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getCoKind());
			l161s01a.setMCntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMCntrt());
			l161s01a.setSCntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getSCntrt());
			l161s01a.setMScntrt(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMScntrt());
			l161s01a.setMSAcc(Util.isEmpty(l120m01b) ? "N" : l120m01b
					.getMSAcc());

			l161s01a.setCaseYear(l120m01a.getCaseYear());
			l161s01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
			l161s01a.setCaseSeq(l120m01a.getCaseSeq());
			l161s01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
			l161s01a.setSignDate(l120m01a.getCaseDate());

			if (Util.equals(tmpUnitCase, "Y")) {
				// 有同業時才要引進案由
				l161s01a.setGist(l120m01a.getGist());
				l161s01a.setQuotaCurr(l140m01a.getCurrentApplyCurr());
			}

			// 引進聯行攤貸比例
			// G-113-0036 動審後有新的攤貸設定則帶最新的攤貸比例
			Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
			if (l140m01e_afs.isEmpty()) {
				if (!l140m01es.isEmpty()) {
					for (L140M01E l140m01e : l140m01es) {
						L161S01B l161s01b = new L161S01B();
						l161s01b.setCreator(user.getUserId());
						l161s01b.setCreateTime(l140m01e.getCreateTime());
						l161s01b.setMainId(l160m01a.getMainId());
						l161s01b.setPid(l161s01a.getUid());
						l161s01b.setSeq(lms1605Service.findL161m01bMaxSeq(
								l160m01a.getMainId(), l161s01a.getUid()));
						l161s01b.setSlBankType("01");
						l161s01b.setSlBank(UtilConstants.兆豐銀行代碼);
						Map<String, Object> map = misdbBASEService.findSYNBANK("");
						l161s01b.setSlBankCN((String) map.get("BRNNAME"));
						l161s01b.setSlBranchCN(branchService.getBranchName(l140m01e
								.getShareBrId()));
						l161s01b.setSlBranch(l140m01e.getShareBrId());
						l161s01b.setSlCurr(l140m01a.getCurrentApplyCurr());
						l161s01b.setSlAmt(l140m01e.getShareAmt());
						//G-113-0036 如非額度管理行, 則現請額度預設帶被分配到的金額
						if (Util.equals(l140m01e.getShareNo(), l161s01a.getCntrNo())) {//140m01e/額度序號 = l161s01a額度序號
							l161s01a.setCurrentApplyAmt(this.checkL161s01aCurrentApplyAmt(l140m01a, l161s01a, l140m01e.getShareAmt()));
						}
						lms1605Service.save(l161s01b);
						//動審攤貸比例init
						L140M01E_AF l140m01e_af = new L140M01E_AF();
						CapBeanUtil.copyBean(l140m01e, l140m01e_af, CapBeanUtil.getFieldName(L140M01E.class, true));// 複製的語法
						l140m01e_af.setOid(null);
						l140m01e_af.setCreator(user.getUserId());
						l140m01e_af.setCreateTime(CapDate.getCurrentTimestamp());
						lms1405Service.save(l140m01e_af);
					}
				}
			} else{//動審後攤貸比例
				for (L140M01E_AF l140m01e_af : l140m01e_afs) {
					L161S01B l161s01b = new L161S01B();
					l161s01b.setCreator(user.getUserId());
					l161s01b.setCreateTime(l140m01e_af.getCreateTime());
					l161s01b.setMainId(l160m01a.getMainId());
					l161s01b.setPid(l161s01a.getUid());
					l161s01b.setSeq(lms1605Service.findL161m01bMaxSeq(
							l160m01a.getMainId(), l161s01a.getUid()));
					l161s01b.setSlBankType("01");
					l161s01b.setSlBank(UtilConstants.兆豐銀行代碼);
					Map<String, Object> map = misdbBASEService.findSYNBANK("");
					l161s01b.setSlBankCN((String) map.get("BRNNAME"));
					l161s01b.setSlBranchCN(branchService.getBranchName(l140m01e_af
							.getShareBrId()));
					l161s01b.setSlBranch(l140m01e_af.getShareBrId());
					l161s01b.setSlCurr(l140m01a.getCurrentApplyCurr());
					l161s01b.setSlAmt(l140m01e_af.getShareAmt());
					//G-113-0036 如非額度管理行, 則現請額度預設帶被分配到的金額
					if (Util.equals(l140m01e_af.getShareNo(), l161s01a.getCntrNo())) {//140m01e/額度序號 = l161s01a額度序號
						l161s01a.setCurrentApplyAmt(this.checkL161s01aCurrentApplyAmt(l140m01a, l161s01a, l140m01e_af.getShareAmt()));
					}
					lms1605Service.save(l161s01b);
				}
			}
			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			l161s01a.setIsDerivatives(UtilConstants.DEFAULT.否);
			ArrayList<String> itemsAll = new ArrayList<String>();
			List<L140M01C> l140m01cs = lms1405Service
					.findL140m01cListByMainId(l140m01a.getMainId());

			if (l140m01cs != null && !l140m01cs.isEmpty()) {
				for (L140M01C l140m01c : l140m01cs) {
					itemsAll.add(l140m01c.getLoanTP());
				}

				Boolean hasDerivateSubjectFlag = false;
				hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
						.toArray(new String[itemsAll.size()]));
				if (hasDerivateSubjectFlag == true) {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.是);
				}
			} else {
				// 找不到額度明細表
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				throw new CapMessageException(
						pop.getProperty("L160M01A.message66"), getClass());
			}

			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);

			if (Util.equals(tmpUnitCase, UtilConstants.DEFAULT.是)
					&& Util.equals(tmpMainBranch, UtilConstants.DEFAULT.是)) {
				l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}

			if (Util.equals(l161s01a.getIsDerivatives(),
					UtilConstants.DEFAULT.是)) {
				l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
			}

			lms1605Service.save(l161s01a);

			// ********************************************

			if (useFromDate == null) {
				if ("1".equals(l140m01a.getUseDeadline())) {
					String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
					if (desp1.length == 2) {

						String date1 = Util.trim(desp1[0]);
						if (Util.isNotEmpty(date1)) {
							useFromDate = CapDate.getDate(date1,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}

						String date2 = Util.trim(desp1[1]);
						if (Util.isNotEmpty(date2)) {
							useEndDate = CapDate.getDate(date2,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}
					}

				} else if ("8".equals(l140m01a.getUseDeadline())) {
					// J-110-0320 為符營業單位陳報團貸實務作業，於ELOAN系統新增團貸動用期限選項
					// 自核准日起~YYYY-MM-DD
					String[] desp1 = Util.trim(l140m01a.getDesp1()).split("~");
					if (desp1.length == 2) {
						String date2 = Util.trim(desp1[1]);
						if (Util.isNotEmpty(date2)) {
							useFromDate = l120m01a.getEndDate();
							useEndDate = CapDate.getDate(date2,
									UtilConstants.DateFormat.YYYY_MM_DD);
						}
					}

				}
			}
		}

		// J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
		// 取得各國別動審表預設免填列註記設定

		String ownBrId = Util.trim(l160m01a.getOwnBrId());
		String countryType = "";
		IBranch ibranch = branchService.getBranch(ownBrId);
		// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
		if (ibranch != null) {
			countryType = Util.trim(ibranch.getCountryType());
		}

		if (Util.notEquals(countryType, "")) {
			CodeType noEdit = codeTypeService
					.findByCodeTypeAndCodeValue("l160m01a.noEditDfCountry",
							Util.trim(countryType), "zh_TW");
			if (noEdit != null) {
				String noEditStr = Util.trim(noEdit.getCodeDesc());
				if (Util.notEquals(noEditStr, "")) {
					String[] noEditArray = noEditStr.split("\\|");
					for (String txt : noEditArray) {
						if (Util.isNotEmpty(Util.trim(txt))) {
							l160m01a.set("noEdit" + Util.trim(txt), "1");
						}
					}
				}

			}

		}

		l160m01a.setUseFromDate(useFromDate);
		l160m01a.setUseEndDate(useEndDate);
		l160m01a.setUnitLoanCase(haveUnitLoanCase ? UtilConstants.DEFAULT.是
				: UtilConstants.DEFAULT.否);
		l160m01a.setSrcMainId(caseMainId);
		if (l160m01a.getDeletedTime() != null) {
			l160m01a.setDeletedTime(null);
		}
		l160m01a.setNewVersion("01");
		lms1605Service.saveMain(newL160m01bs, newL162m01as, l160m01a);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1605Service.saveL164s01aList(newL164s01as);
		amlRelateService.saveL120s01pList(newL120s01ps);

		CapAjaxFormResult result = DataParse.toResult(l160m01a);
		result = formatResultShow(result, l160m01a, 1);

		return result;

	}

	/**
	 * 引進額度明細表 連保人 資料 L140M01I
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult includeL140m01I(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);

		List<L162S01A> l162m01as = (List<L162S01A>) lms1605Service
				.findListByMainId(L162S01A.class, l160m01a.getMainId());

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l162m01as.isEmpty()) {
			lms1605Service.deleteL162m01as(l162m01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> l164s01as = (List<L164S01A>) lms1605Service
				.findListByMainId(L164S01A.class, l160m01a.getMainId());
		if (l164s01as != null && !l164s01as.isEmpty()) {
			lms1605Service.deleteL164s01as(l164s01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L120S01P> l120s01ps = (List<L120S01P>) amlRelateService
				.findListByMainId(L120S01P.class, l160m01a.getMainId());
		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			amlRelateService.deleteListL120s01p(l120s01ps);
		}

		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		List<String> selectMainId = new ArrayList<String>();
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		for (L160M01B l160m01b : l160m01bs) {
			selectMainId.add(l160m01b.getReMainId());
			mainIdmapingCntrno
					.put(l160m01b.getReMainId(), l160m01b.getCntrNo());
		}
		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		HashMap<String, String> custCountry = lms1605Service
				.getCustCounty(l120m01a);
		List<L140M01A> l140m01as = lms1405Service
				.findL140m01aListByMainIdList(selectMainId
						.toArray(new String[selectMainId.size()]));

		List<L162S01A> newL162m01as = new ArrayList<L162S01A>();

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> newL164s01as = new ArrayList<L164S01A>();
		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();
		Map<String, String> processedCustId = new HashMap<String, String>();
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = (List<L120S01B>) amlRelateService
					.findListByMainId(L120S01B.class, l120m01a.getMainId());
			for (L120S01B l120s01b : l120s01bs) {
				if (l120s01b != null) {
					// 複製借款人基本資料 L120S01B L120S01A 到 L164S01A
					newL164s01as.add(copyL120s01bToL164s01a(l120s01b, mainId));

					// 複製借款人實質受益人資料 L120S01P(簽報書MAINID)->L120S01P(動審表MAINID)
					newL120s01ps.addAll(copyL120s01pToNewL120s01p(l120s01b,
							mainId));
				}
			}
		} else {
			// 個金
		}

		for (L140M01A l140m01a : l140m01as) {
			// 複製連保人資料到主從債務人
			String cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());
			newL162m01as.addAll(copyL140m01IToL162m01a(l140m01a, mainId,
					custCountry, cntrNo));
		}

		lms1605Service.saveL162m01aList(newL162m01as);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1605Service.saveL164s01aList(newL164s01as);
		amlRelateService.saveL120s01pList(newL120s01ps);

		return result;

	}

	/**
	 * 儲存L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL160m01a(PageParameters params)
			throws CapException {
		// lmsService.uploadELLNSEEK(new L120M01A());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String formL160m01a = Util.trim(params.getString("L160M01AForm")); // 指定的form
		JSONObject jsonL160m01a = null;
		L160M01A l160m01a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (Util.isEmpty(oid)) {

			l160m01a = new L160M01A();
			l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l160m01a.setTType("1");
			l160m01a.setOwnBrId(user.getUnitNo());
			l160m01a.setMainId(IDGenerator.getUUID());
			l160m01a.setApprId(user.getUserId());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l160m01a.setTxCode(txCode);
			// UPGRADE: 待確認，URL是否正確
			l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1605M01Page.class));

			// 複製L901M01A審核項目
			List<L160M01C> l160m01cs = copyL901m01aToL160m01c(
					l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
			lms1605Service.saveL160m01cList(l160m01cs);
		} else {
			l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
			l160m01a.setRandomCode(IDGenerator.getRandomCode());

			l160m01a.setApprId(user.getUserId());
		}
		String mainId = l160m01a.getMainId();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		if (l160m01a.getDeletedTime() != null) {
			l160m01a.setDeletedTime(null);
		}
		String validate = null;
		// 組待辦事項
		StringBuilder waitingItem = new StringBuilder("");
		switch (page) {
		case 1:
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			lms1605Service.save(l160m01a);
			result.set("randomCode", l160m01a.getRandomCode());
			result.set(
					"showApprId",
					l160m01a.getApprId() + " "
							+ lmsService.getUserName(l160m01a.getApprId()));
			break;
		case 2:
			L163S01A l163s01a = lms1605Service.findL163m01aByMainId(mainId);
			List<L160M01C> l160m01cList = (List<L160M01C>) lms1605Service
					.findListByMainId(L160M01C.class, mainId);
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			// 全行項目
			String allResult = params.getString("allresult");
			JSONObject jsonAllresult = JSONObject.fromObject(allResult);

			// 該分行項目
			String localResult = params.getString("localresult");
			JSONObject jsonLocalresult = JSONObject.fromObject(localResult);

			// 自訂項目
			String selfresult = params.getString("selfresult");
			JSONObject jsonselfresult = JSONObject.fromObject(selfresult);

			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			// 項目輸入欄位
			String allFiledVal = params.getString("allFiledVal");
			JSONObject jsonAllFiledVal = JSONObject.fromObject(allFiledVal);

			for (L160M01C l160m01c : l160m01cList) {
				int type = Util.parseInt(l160m01c.getItemType());

				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				String itemField1Fg1 = Util.trim(lms1605Service
						.isL160m01cHasInputItemField1(l160m01c));
				String itemContent = l160m01c.getItemContent();
				if (Util.notEquals(itemField1Fg1, "")) {
					itemContent = StringUtils.replace(
							l160m01c.getItemContent(), itemField1Fg1,
							l160m01c.getItemField1());
				}

				switch (type) {
				case 1:
					String value1 = (String) jsonAllresult.get(l160m01c
							.getOid());
					l160m01c.setItemCheck(value1);

					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					String itemField1_1 = (String) jsonAllFiledVal.get(l160m01c
							.getOid());
					l160m01c.setItemField1(itemField1_1);

					// 當回傳值為2 表示未收
					if (UtilConstants.Usedoc.checkItem.未收.equals(value1)) {
						waitingItem
								.append((waitingItem.length() > 0 ? "、" : ""));
						waitingItem.append(itemContent);
					}

					break;
				case 2:
					String value2 = (String) jsonLocalresult.get(l160m01c
							.getOid());
					l160m01c.setItemCheck(value2);

					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					String itemField2_1 = (String) jsonAllFiledVal.get(l160m01c
							.getOid());
					l160m01c.setItemField1(itemField2_1);

					// 當回傳值為2 表示未收
					if (UtilConstants.Usedoc.checkItem.未收.equals(value2)) {
						waitingItem
								.append((waitingItem.length() > 0 ? "、" : ""));
						waitingItem.append(itemContent);
					}

					break;
				case 3:
					String seq = String.valueOf(l160m01c.getItemSeq());

					// 當傳回來的json裡面存在該seq的物件在執行儲存
					if (jsonselfresult.containsKey(seq)) {
						JSONObject selfObject = (JSONObject) jsonselfresult
								.get(seq);
						String selectValue = (String) selfObject.get("id");
						String drc = (String) selfObject.get("drc");
						l160m01c.setItemCheck(selectValue);
						l160m01c.setItemContent(drc);
						// 當回傳值為2 表示未收
						if (UtilConstants.Usedoc.checkItem.未收
								.equals(selectValue)) {
							waitingItem.append((waitingItem.length() > 0 ? "、"
									: ""));
							waitingItem.append(l160m01c.getItemContent());
						}

					}
					break;
				}

			}

			if (Util.isEmpty(l163s01a)) {
				l163s01a = new L163S01A();
				l163s01a.setMainId(mainId);
				l163s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l163s01a.setCreator(user.getUserId());
			}

			// 如果有未收項目，新增第四頁籤的model ，若沒有未收清除預定補全日
			if (waitingItem.length() > 0) {
				l160m01a.setUseType("Y");

				// L160M01A.message25=未收到（未辦妥）
				String endMessg = pop.getProperty("L160M01A.message25");
				waitingItem.append(" ").append(endMessg);
				l163s01a.setWaitingItem(waitingItem.toString());
			} else {
				l163s01a.setWillFinishDate(null);
				l163s01a.setWaitingItem("");
				l160m01a.setUseType("N");
				l163s01a.setAppraiserId(null);
			}
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			validate = Util.validateColumnSize(l163s01a, pop, "L163M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("mark", waitingItem.length() > 0 ? true : false);
			try {

				lms1605Service.saveL160m01cs(l160m01cList, l160m01a, l163s01a);
			} catch (Exception e) {

				logger.error(
						"[saveL160m01a] lms1605Service.saveL160m01cs EXCEPTION!!",
						e);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
			}

			break;
		case 3:
			/*
			 * String formL161m01a = params.getString("L161M01AForm");
			 * JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a); //
			 * 先儲存L161M01A 聯貸案參貸比率一覽表主檔 L161S01A l161m01a =
			 * lms1605Service.findL161m01aByMainId(l160m01a .getMainId());
			 * DataParse.toBean(jsonL161m01a, l161m01a);
			 * 
			 * lms1605Service.save(l160m01a, l161m01a);
			 */
			break;
		case 4:
			L163S01A l163m01aPage4 = lms1605Service
					.findL163m01aByMainId(l160m01a.getMainId());
			String formL163m01a = params.getString("L163M01AForm"); // 指定的form
			JSONObject jsonL163m01a = JSONObject.fromObject(formL163m01a);
			DataParse.toBean(jsonL163m01a, l163m01aPage4);

			// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
			// 01O編制中時才需要檢查
			if (Util.equals(l160m01a.getDocStatus(), CreditDocStatusEnum.海外_編製中)
					&& CapDate.getCurrentTimestamp().after(
							l163m01aPage4.getWillFinishDate())) {

				L120M01A l120m01a = lms1205Service
						.findL120m01aByMainId(l160m01a.getSrcMainId());
				// 目前只針對企金做此功能
				if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a
						.getDocType())) {
					// L163M01A.willFinishDateError=預定補全日期需晚於今日日期
					throw new CapMessageException(
							pop.getProperty("L163M01A.willFinishDateError"),
							getClass());
				}
			}

			result.set("appraiserId",
					lmsService.getUserName(l163m01aPage4.getAppraiserId()));

			if (Util.validateColumnSize(l163m01aPage4, pop, "L163M01A") != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName",
						Util.validateColumnSize(l163m01aPage4, pop, "L163M01A"));

				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			lms1605Service.save(l160m01a, l163m01aPage4);
			break;

		}// close switch

		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		if (waitingItem.length() > 0
				&& (l160m01a.getL163S01A() == null || Util.isEmpty(l160m01a
						.getL163S01A().getWillFinishDate()))) {
			// L160M01A.message24 =
			// 尚有待辦事項，在『先行動用呈核及控制表』頁籤中之「預定補全日期」，請至『先行動用呈核及控制表』登錄
			result.set("showPage4msg", "Y");
		}
		result.set(EloanConstants.OID, CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l160m01a.getMainId()));
		// result = formatResultShow(result, l160m01a, page);
		return result;

	}

	/**
	 * 儲存L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL161m01b(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String slCurr = Util.trim(params.getString("slCurr"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		// Long totalAmt = params.getLong("totalAmt");
		String formL161m01a = params.getString("L161M01AForm");
		String formL161m01b = params.getString("L161M01BForm");
		JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a);
		JSONObject jsonL161m01b = JSONObject.fromObject(formL161m01b);

		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);
		L161S01B l161s01b = lms1605Service.findModelByOid(L161S01B.class, oid);
		// 先儲存L161M01A 聯貸案參貸比率一覽表主檔
		L161S01A l161s01a = lms1605Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			throw new CapMessageException(
					prop.getProperty("L160M01A.message79"), getClass());
		}
			
		if (Util.isEmpty(l161s01b)) {
			l161s01b = new L161S01B();
			DataParse.toBean(jsonL161m01b, l161s01b);
			l161s01b.setSeq(lms1605Service.findL161m01bMaxSeq(
					l160m01a.getMainId(), l161s01a.getUid()));
			l161s01b.setCreator(user.getUserId());
			l161s01b.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01b.setMainId(mainId);
		} else {
			DataParse.toBean(jsonL161m01b, l161s01b);
		}

		l161s01b.setSlCurr(slCurr);
		l161s01b.setPid(l161s01a.getUid());

		DataParse.toBean(jsonL161m01a, l161s01a);

		String validate = Util.validateColumnSize(l161s01b, prop, "L160M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		lms1605Service.save(l161s01a, l161s01b);
		return result;

	}

	/**
	 * 儲存L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL162m01a(PageParameters params)
			throws CapException {
		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		Properties lmss20aPop = MessageBundleScriptCreator
				.getComponentResource(LMSS20APanel.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String rKindD = Util.trim(params.getString("rKindD"));
		String formL162m01a = params.getString("L162M01AForm"); // 指定的form
		JSONObject jsonL162m01a = JSONObject.fromObject(formL162m01a);
		L162S01A l162s01a = lms1605Service.findModelByOid(L162S01A.class, oid);
		List<L162S01A> l162m01as = (List<L162S01A>) lms1605Service
				.findListByMainId(L162S01A.class, mainId);

		if (Util.isEmpty(l162s01a)) {
			l162s01a = new L162S01A();
			DataParse.toBean(jsonL162m01a, l162s01a);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setMainId(mainId);
			// 2 是自行新增的選項
			l162s01a.setDataSrc("2");
		} else {
			DataParse.toBean(jsonL162m01a, l162s01a);
		}

		l162s01a.setRKindD(rKindD);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		// 驗證欄位長度
		String validate = Util.validateColumnSize(l162s01a, prop, "L162M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}

		// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
		if (Util.notEquals(l162s01a.getRType(), "C")
				&& Util.notEquals(l162s01a.getRType(), "S")) {
			if (Util.isEmpty(l162s01a.getGuaPercent())) {
				throw new CapMessageException(MessageFormat.format(
						prop.getProperty("L162M01A.error01"), l162s01a.getRId()
								+ " " + Util.trim(l162s01a.getRName())),
						getClass());
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			String guarantorPriorityOn = Util.trim(lmsService
					.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));
			if (Util.equals(guarantorPriorityOn, "Y")) {
				if (Util.isEmpty(l162s01a.getGuaNaExposure())) {
					// L162M01A.error03=「{0}」欄位「{0}」不得空白
					// L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
					throw new CapMessageException(MessageFormat.format(
							prop.getProperty("L162M01A.error03"),
							l162s01a.getRId() + " "
									+ Util.trim(l162s01a.getRName()),
							prop.getProperty("L162M01A.guaNaExposure")),
							getClass());
				}
			}

		} else {
			l162s01a.setGuaPercent(null);
			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			l162s01a.setGuaNaExposure(null);
		}

		// 檢查是否已經有此債務人
		for (L162S01A l162m01aold : l162m01as) {
			// 不檢查自己
			if (l162m01aold.getOid().equals(oid)) {
				continue;
			}
			if (Util.trim(l162m01aold.getCustId()).toUpperCase()
					.equals(Util.trim(l162s01a.getCustId()).toUpperCase())
					&& Util.trim(l162m01aold.getDupNo())
							.toUpperCase()
							.equals(Util.trim(l162s01a.getDupNo())
									.toUpperCase())) {
				if (Util.trim(l162m01aold.getRId()).toUpperCase()
						.equals(Util.trim(l162s01a.getRId()).toUpperCase())
						&& Util.trim(l162m01aold.getRDupNo())
								.toUpperCase()
								.equals(Util.trim(l162s01a.getRDupNo())
										.toUpperCase())
						&& Util.trim(l162m01aold.getRType())
								.toUpperCase()
								.equals(Util.trim(l162s01a.getRType())
										.toUpperCase())) {
					if (Util.trim(l162m01aold.getCntrNo())
							.toUpperCase()
							.equals(Util.trim(l162s01a.getCntrNo())
									.toUpperCase())) {
						Map<String, String> param = new HashMap<String, String>();
						param.put("custId",
								Util.trim(l162s01a.getRId()).toUpperCase()
										+ " "
										+ Util.trim(l162s01a.getRDupNo())
												.toUpperCase());
						// EFD3010=ERROR|$\{custId\}此債務人已經存在 ！！|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD3010", param), getClass());
					}

				}
			}
		}
		// 當主借人ID與從債務人ID相同，其相關身分應該為C 共同借款人
		if (Util.trim(l162s01a.getCustId()).toUpperCase()
				.equals(Util.trim(l162s01a.getRId()).toUpperCase())
				&& Util.trim(l162s01a.getDupNo()).toUpperCase()
						.equals(Util.trim(l162s01a.getRDupNo()).toUpperCase())) {
			if (!UtilConstants.lngeFlag.共同借款人.equals(Util.trim(l162s01a
					.getRType()))) {
				Map<String, String> param = new HashMap<String, String>();
				Properties pop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				param.put("msg", pop.getProperty("L160M01A.message48"));
				// L160M01A.message48=主債務人統編與從債務人統編相同者，其相關身份應為 C、共同借款人
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
		}
		//G-113-0036 主從債務人檢核時 保證金額上限(Guarante Amount) 需<= L140M01A 現請額度
		if (Util.isNotEmpty(l162s01a)) {
			if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(l162s01a.getCntrNo().substring(0, 3))
					.getBrNoFlag())) {
				String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
				if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
					BigDecimal checkamt = new BigDecimal(0);
					L161S01A l161s01a = lms1605Service.findL161m01aByMainIdCntrno(mainId, l162s01a.getCntrNo());
					if (l161s01a != null) {
						L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l161s01a.getCntrMainId());
						if (l140m01a != null && Util.isNotEmpty(l140m01a.getCurrentApplyAmt())) {
							checkamt = l140m01a.getCurrentApplyAmt();
							BigDecimal grtamt = Util.isEmpty(l162s01a.getGrtAmt()) ? new BigDecimal(0) : l162s01a.getGrtAmt();
							if(grtamt.compareTo(checkamt) > 0){
								throw new CapMessageException(MessageFormat.format(
										prop.getProperty("L162M01A.error05"), l162s01a.getRId()
												+ " " + Util.trim(l162s01a.getRName())),
										getClass());
							}
						}
						
					}
				}
			}
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		if (Util.equals(Util.trim(l162s01a.getRType()), "C")) {

			L164S01A l164s01a = lms1605Service.findL164s01aByMainIdCustId(
					mainId, Util.trim(l162s01a.getRId()),
					Util.trim(l162s01a.getRDupNo()));
			if (l164s01a == null) {
				l164s01a = new L164S01A();
				l164s01a.setCreator(user.getUserId());
				l164s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l164s01a.setMainId(mainId);
			}

			DataParse.toBean(jsonL162m01a, l164s01a);

			l164s01a.setCustId(l162s01a.getRId());
			l164s01a.setDupNo(l162s01a.getRDupNo());
			l164s01a.setCustPos(l162s01a.getRType());

			// 取得0024判斷行業對象別
			Map<String, Object> m0024 = iCustomerService.findByIdDupNo(
					l162s01a.getRId(), l162s01a.getRDupNo());
			String busCode = Util.trim(MapUtils.getString(m0024, "BUSCD"));

			// 企業戶才要負責人跟實質受益人
			if (Util.equals(busCode, "")) {
				// AML.error010=主從債務人「{0}」於0024無行業對象別資料。
				throw new CapMessageException(MessageFormat.format(
						lmss20aPop.getProperty("AML.error010"),
						l162s01a.getRId() + l162s01a.getRDupNo()
								+ l162s01a.getRName()), getClass());
			} else {

				if (!LMSUtil.isBusCode_060000_130300(busCode)) {

					String emptyFieldName = "";
					if (Util.equals(l164s01a.getBeneficiary(), "")) {
						// L120S09a.checkbox7=實質受益人
						emptyFieldName = emptyFieldName
								+ (Util.equals(emptyFieldName, "") ? "" : "、")
								+ lmss20aPop.getProperty("L120S09a.checkbox7");
					}

					if (Util.equals(l164s01a.getChairman(), "")) {
						// L120S09a.checkbox3=借戶負責人
						emptyFieldName = emptyFieldName
								+ (Util.equals(emptyFieldName, "") ? "" : "、")
								+ lmss20aPop.getProperty("L120S09a.checkbox3");
					}

					if (Util.notEquals(emptyFieldName, "")) {
						// L120S09a.message07=「{0}」欄位不得為空白
						throw new CapMessageException(MessageFormat.format(
								lmss20aPop.getProperty("L120S09a.message07"),
								emptyFieldName), getClass());
					}
				}
			}

			lms1605Service.save(l164s01a);
		}
		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		lms1605Service.save(l162s01a);

		result.set("formOid", l162s01a.getOid());

		return result;

	}

	/**
	 * 儲存已覆核 登錄辦妥事項
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult saveLogeIn(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
		String formL160m01a = params.getString("L160M01AForm"); // 指定的form
		JSONObject jsonL160m01a = JSONObject.fromObject(formL160m01a);
		L163S01A l163s01a = l160m01a.getL163S01A();
		DataParse.toBean(jsonL160m01a, l163s01a);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		l163s01a.setAppraiserId(user.getUserId());
		try {
			lms1605Service.flowActionGo(oid, l163s01a);
		} catch (Throwable e) {
			logger.error(
					"[saveLogeIn] lms1605Service.flowActionGo EXCEPTION!!", e);
			throw new CapMessageException(e.getMessage(), getClass());
		}

		return result;

	}

	/**
	 * 刪除L160M01A 動用審核表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL160m01a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {

			// 檢查動審表有沒有被已核准退回過
			List<L160M01A> l160m01as = lms1605Service.findL160M01AByOids(oids);

			for (L160M01A l160m01a : l160m01as) {
				List<Map<String, Object>> list = eloandbBASEService
						.findDeletedMetaByMainId(l160m01a.getMainId());
				if (list != null && !list.isEmpty()) {

					StringBuffer msg = new StringBuffer("");
					msg.append("動審表 " + l160m01a.getCustId() + " "
							+ l160m01a.getCustName() + " 有已核准退回紀錄，本筆不得刪除!!<BR>");
					for (Map<String, Object> dataMap : list) {
						msg.append("前次覆核資訊：<BR>");
						msg.append("統編:")
								.append(Util.trim(dataMap.get("CUSTID")))
								.append("<BR>");
						msg.append("戶名:")
								.append(Util.trim(dataMap.get("CUSTNAME")))
								.append("<BR>");
						msg.append("建立人員:")
								.append(Util.trim(dataMap.get("CREATOR")))
								.append("<BR>");
						msg.append("覆核人員:")
								.append(Util.trim(dataMap.get("APPROVER")))
								.append("<BR>");
						msg.append("覆核日期:")
								.append(Util.trim(dataMap.get("APPROVETIME")))
								.append("<BR>");
						msg.append("退回日期:")
								.append(Util.trim(dataMap.get("DELETEDTIME")))
								.append("<BR>");
						break;
					}
					throw new CapMessageException(msg.toString(), getClass());
				}
			}

			if (lms1605Service.deleteL160m01as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE,
						RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}

		return result;

	}

	/**
	 * 新增動審表
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl160m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L160M01A l160m01a = new L160M01A();
		l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l160m01a.setOwnBrId(user.getUnitNo());
		l160m01a.setMainId(IDGenerator.getUUID());
		l160m01a.setApprId(user.getUserId());
		l160m01a.setTType("1");
		l160m01a.setUseSelect("1");
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l160m01a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1605M01Page.class));
		l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		lms1605Service.save(l160m01a);
		// 複製L901M01A審核項目
		List<L160M01C> l160m01cs = this.copyL901m01aToL160m01c(
				l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
		lms1605Service.saveL160m01cList(l160m01cs);
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result.set(EloanConstants.OID, l160m01a.getOid());

	}

	/**
	 * 刪除上傳檔案
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteUploadFile(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1605Service.deleteUploadFile(oids);
		}

		return result;

	}

	/**
	 * 刪除 L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL161M01B(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1605Service.deleteListByOid(L161S01B.class, oids);
		}

		return result;

	}

	/**
	 * 刪除 L162M01A 主從債務人資料表檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL162M01A(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			lms1605Service.deleteListByOid(L162S01A.class, oids);
		}

		return result;

	}

	/**
	 * 檢核是否符合資料修正的資格
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryDateFixBase(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String l140m01aOid = params.getString("l140m01aOid");
		String l160m01aOid = params.getString("l160m01aOid");

		L140M01A l140m01a = lms1405Service.findModelByOid(L140M01A.class,
				l140m01aOid);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class,
				l160m01aOid);
		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		if (l140m01a == null) {
			throw new CapMessageException(RespMsgHelper.getMainMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		// if ("2".equals(l160m01a.getDataSrc())) {
		// Properties prop = MessageBundleScriptCreator
		// .getComponentResource(LMS1605M01Page.class);
		// // EFD0015=WARN|【注意】$\{noticeMsg\}|
		// // L160M01A.message52=額度序號來源為聯行額度明細表，不能執行資料修正
		// HashMap<String, String> msgMap = new HashMap<String, String>();
		// msgMap.put("noticeMsg", prop.getProperty("L160M01A.message52"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0015", msgMap), getClass());
		// }
		L161S01A l161s01a = lms1605Service.findL161m01aByMainIdCntrno(
				l160m01a.getMainId(), l140m01a.getCntrNo());

		Set<L161S01B> l161s01b = l161s01a.getL161s01b();

		// 表示無同業聯貸和同行聯貸不得進行資料修正
		if (l140m01a.getL140m01e().isEmpty() && l161s01b.isEmpty()) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1605M01Page.class);
			// EFD0015=WARN|【注意】$\{noticeMsg\}|
			// L160M01A.message42= 此案件並非聯貸案性質，無法修改參貸比率資料！
			HashMap<String, String> msgMap = new HashMap<String, String>();
			msgMap.put("noticeMsg", prop.getProperty("L160M01A.message42"));
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0015", msgMap), getClass());
		}

		L210M01A l210m01a = new L210M01A();

		// 複製的語法
		CapBeanUtil
				.copyBean(l160m01a, l210m01a, new String[] { "custId", "dupNo",
						"custName", "typCd", "caseNo", "caseDate", "ownBrId" });
		l210m01a.setMainId(IDGenerator.getUUID());
		l210m01a.setRefMainId(l160m01a.getMainId());
		l210m01a.setSrcMainId(l140m01a.getMainId());

		// 如果沒有聯行參貸比率就抓額度明細表上
		if (l161s01b.isEmpty()) {
			l210m01a.setLtAmt(l140m01a.getCurrentApplyAmt());
			l210m01a.setLtCurr(l140m01a.getCurrentApplyCurr());
			l210m01a.setCntrNo(l140m01a.getCntrNo());
		} else {
			l210m01a.setLtAmt(l161s01a.getQuotaAmt());
			l210m01a.setLtCurr(l161s01a.getQuotaCurr());
			l210m01a.setCntrNo(l161s01a.getCntrNo());
		}
		l210m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l210m01a.setCreator(user.getUserId());
		l210m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l210m01a.setUpdater(user.getUserId());
		// UPGRADE: 待確認，URL是否正確
		l210m01a.setDocURL(params.getString("docUrl"));
		l210m01a.setModifyDate(CapDate.getCurrentTimestamp());

		// 授權檔
		L210A01A l210a01a = new L210A01A();
		l210a01a.setAuthTime(CapDate.getCurrentTimestamp());
		l210a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
		l210a01a.setAuthUnit(user.getUnitNo());
		l210a01a.setMainId(l210m01a.getMainId());
		l210a01a.setOwner(user.getUserId());
		l210a01a.setOwnUnit(user.getUnitNo());
		Set<L210A01A> l210a01as = new HashSet<L210A01A>();
		l210a01as.add(l210a01a);
		l210m01a.setL210a01a(l210a01as);

		// 1同行聯貸2同業聯貸3 同業聯貸&同行聯貸
		if (!l140m01a.getL140m01e().isEmpty() && !l161s01b.isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同業聯貸和同行聯貸);
		} else if (!l140m01a.getL140m01e().isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同行聯貸);
		} else if (!l161s01b.isEmpty()) {
			l210m01a.setCaseType(UtilConstants.editDoc.caseType.同業聯貸);
		}
		// 要複製的欄位名稱
		String[] copyColumnForS01A = new String[] { "flag", "shareBrId",
				"shareRate1", "shareRate2", "shareAmt", "totalAmt", "shareNo",
				"shareFlag" };
		// 複製同行聯貸
		ArrayList<L210S01A> l210s01as = new ArrayList<L210S01A>();
		if (!l140m01a.getL140m01e().isEmpty()) {
			for (L140M01E l140m01e : l140m01a.getL140m01e()) {
				L210S01A l210s01a = new L210S01A();
				L210S01A l210s01a2 = new L210S01A();
				CapBeanUtil.copyBean(l140m01e, l210s01a, copyColumnForS01A);
				l210s01a.setMainId(l210m01a.getMainId());
				l210s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01a.setCreator(user.getUserId());
				l210s01a.setUpdater(user.getUserId());
				l210s01a.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01a.setChgFlag(editDoc.chanFlag.變動前);
				l210s01as.add(l210s01a);
				CapBeanUtil.copyBean(l140m01e, l210s01a2, copyColumnForS01A);
				l210s01a2.setMainId(l210m01a.getMainId());
				l210s01a2.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01a2.setCreator(user.getUserId());
				l210s01a2.setUpdater(user.getUserId());
				l210s01a2.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01a2.setChgFlag(editDoc.chanFlag.變動後);
				l210s01as.add(l210s01a2);
			}

		}

		// 要複製的欄位名稱
		String[] copyColumnForS01B = new String[] { "slBankType", "slBank",
				"slBankCN", "slBranch", "slBranchCN", "slMaster", "slAccNo",
				"slCurr", "slAmt", "seq" };
		// 複製同業聯貸
		ArrayList<L210S01B> l210s01bs = new ArrayList<L210S01B>();
		if (!l161s01b.isEmpty()) {

			for (L161S01B l161m01b : l161s01b) {
				L210S01B l210s01b = new L210S01B();
				L210S01B l210s01b2 = new L210S01B();
				CapBeanUtil.copyBean(l161m01b, l210s01b, copyColumnForS01B);
				l210s01b.setMainId(l210m01a.getMainId());
				l210s01b.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01b.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01b.setCreator(user.getUserId());
				l210s01b.setUpdater(user.getUserId());
				l210s01b.setChgFlag(editDoc.chanFlag.變動前);
				l210s01bs.add(l210s01b);
				CapBeanUtil.copyBean(l161m01b, l210s01b2, copyColumnForS01B);
				l210s01b2.setMainId(l210m01a.getMainId());
				l210s01b2.setCreateTime(CapDate.getCurrentTimestamp());
				l210s01b2.setUpdateTime(CapDate.getCurrentTimestamp());
				l210s01b2.setCreator(user.getUserId());
				l210s01b2.setUpdater(user.getUserId());
				l210s01b2.setChgFlag(editDoc.chanFlag.變動後);
				l210s01bs.add(l210s01b2);
			}
		}
		// 當修改分行與目前分行相同 才上這個註記
		if (user.getUnitNo().equals(l120m01a.getCaseBrId())) {
			l120m01a.setReEstFlag("Y");
		}

		lms2105Service
				.saveNewL210m01a(l210s01as, l210s01bs, l210m01a, l120m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String formL160m01a = Util.trim(params.getString("L160M01AForm")); // 指定的form
		JSONObject jsonL160m01a = null;
		L160M01A l160m01a = null;
		if (Util.isEmpty(oid)) {
			l160m01a = new L160M01A();
			l160m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l160m01a.setOwnBrId(user.getUnitNo());
			l160m01a.setMainId(IDGenerator.getUUID());
			l160m01a.setTType("1");
			l160m01a.setApprId(user.getUserId());
			String txCode = Util.trim(params
					.getString(EloanConstants.TRANSACTION_CODE));
			l160m01a.setTxCode(txCode);
			// UPGRADE: 待確認，URL是否正確
			l160m01a.setDocURL(CapWebUtil.getDocUrl(LMS1605M01Page.class));

			// 複製L901M01A審核項目
			List<L160M01C> l160m01cs = copyL901m01aToL160m01c(
					l160m01a.getMainId(), LocaleContextHolder.getLocale().toString());
			lms1605Service.saveL160m01cList(l160m01cs);
		} else {
			l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
		}
		String mainId = l160m01a.getMainId();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		String validate = null;
		// 組待辦事項
		StringBuilder waitingItem = new StringBuilder("");
		switch (page) {
		case 1:
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			lms1605Service.save(l160m01a);
			result.set("randomCode", l160m01a.getRandomCode());
			break;
		case 2:
			L163S01A l163s01a = lms1605Service.findL163m01aByMainId(mainId);
			List<L160M01C> l160m01cList = (List<L160M01C>) lms1605Service
					.findListByMainId(L160M01C.class, mainId);
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l160m01a);
			// 全行項目
			String allResult = params.getString("allresult");
			JSONObject jsonAllresult = JSONObject.fromObject(allResult);

			// 該分行項目
			String localResult = params.getString("localresult");
			JSONObject jsonLocalresult = JSONObject.fromObject(localResult);

			// 自訂項目
			String selfresult = params.getString("selfresult");
			JSONObject jsonselfresult = JSONObject.fromObject(selfresult);

			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			// 項目輸入欄位
			String allFiledVal = params.getString("allFiledVal");
			JSONObject jsonAllFiledVal = JSONObject.fromObject(allFiledVal);

			for (L160M01C l160m01c : l160m01cList) {
				int type = Util.parseInt(l160m01c.getItemType());

				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				String itemField1Fg1 = Util.trim(lms1605Service
						.isL160m01cHasInputItemField1(l160m01c));
				String itemContent = l160m01c.getItemContent();
				if (Util.notEquals(itemField1Fg1, "")) {
					itemContent = StringUtils.replace(
							l160m01c.getItemContent(), itemField1Fg1,
							l160m01c.getItemField1());
				}

				switch (type) {
				case 1:
					String value1 = (String) jsonAllresult.get(l160m01c
							.getOid());
					l160m01c.setItemCheck(value1);

					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					String itemField1_1 = (String) jsonAllFiledVal.get(l160m01c
							.getOid());
					l160m01c.setItemField1(itemField1_1);

					// 當回傳值為2 表示未收
					if (UtilConstants.Usedoc.checkItem.未收.equals(value1)) {
						waitingItem
								.append((waitingItem.length() > 0 ? "、" : ""));
						waitingItem.append(itemContent);
					}
					break;
				case 2:
					String value2 = (String) jsonLocalresult.get(l160m01c
							.getOid());
					l160m01c.setItemCheck(value2);

					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					String itemField2_1 = (String) jsonAllFiledVal.get(l160m01c
							.getOid());
					l160m01c.setItemField1(itemField2_1);

					// 當回傳值為2 表示未收
					if (UtilConstants.Usedoc.checkItem.未收.equals(value2)) {
						waitingItem
								.append((waitingItem.length() > 0 ? "、" : ""));
						waitingItem.append(itemContent);
					}
					break;
				case 3:
					String seq = String.valueOf(l160m01c.getItemSeq());

					// 當傳回來的json裡面存在該seq的物件在執行儲存
					if (jsonselfresult.containsKey(seq)) {
						JSONObject selfObject = (JSONObject) jsonselfresult
								.get(seq);
						String selectValue = (String) selfObject.get("id");
						String drc = (String) selfObject.get("drc");
						l160m01c.setItemCheck(selectValue);
						l160m01c.setItemContent(drc);
						// 當回傳值為2 表示未收
						if (UtilConstants.Usedoc.checkItem.未收
								.equals(selectValue)) {
							waitingItem.append((waitingItem.length() > 0 ? "、"
									: ""));
							waitingItem.append(l160m01c.getItemContent());
						}

					}
					break;
				}

			}

			if (Util.isEmpty(l163s01a)) {
				l163s01a = new L163S01A();
				l163s01a.setMainId(mainId);
				l163s01a.setCreateTime(CapDate.getCurrentTimestamp());
				l163s01a.setCreator(user.getUserId());
			}

			// 如果有未收項目，新增第四頁籤的model ，若沒有未收清除預定補全日
			if (waitingItem.length() > 0) {
				l160m01a.setUseType("Y");

				// L160M01A.message25=未收到（未辦妥）
				String endMessg = pop.getProperty("L160M01A.message25");
				waitingItem.append(" ").append(endMessg);
				l163s01a.setWaitingItem(waitingItem.toString());
			} else {
				l163s01a.setWillFinishDate(null);
				l163s01a.setWaitingItem("");
				l160m01a.setUseType("N");
			}
			validate = Util.validateColumnSize(l160m01a, pop, "L160M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			validate = Util.validateColumnSize(l163s01a, pop, "L163M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			result.set("mark", waitingItem.length() > 0 ? true : false);

			try {

				lms1605Service.saveL160m01cs(l160m01cList, l160m01a, l163s01a);
			} catch (Exception e) {

				logger.error(
						"[saveL160m01a] lms1605Service.saveL160m01cs EXCEPTION!!",
						e);
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
			}

			break;
		case 3:
			/*
			 * String formL161m01a = params.getString("L161M01AForm");
			 * 
			 * JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a); //
			 * 先儲存L161M01A 聯貸案參貸比率一覽表主檔 L161S01A l161m01a =
			 * lms1605Service.findL161m01aByMainId(l160m01a .getMainId());
			 * DataParse.toBean(jsonL161m01a, l161m01a);
			 * 
			 * lms1605Service.save(l161m01a);
			 */
			break;
		case 4:
			L163S01A l163m01aPage4 = lms1605Service
					.findL163m01aByMainId(l160m01a.getMainId());
			String formL163m01a = params.getString("L163M01AForm"); // 指定的form
			JSONObject jsonL163m01a = JSONObject.fromObject(formL163m01a);
			DataParse.toBean(jsonL163m01a, l163m01aPage4);

			result.set("appraiserId",
					lmsService.getUserName(l163m01aPage4.getAppraiserId()));

			if (Util.validateColumnSize(l163m01aPage4, pop, "L163M01A") != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName",
						Util.validateColumnSize(l163m01aPage4, pop, "L163M01A"));

				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			lms1605Service.save(l163m01aPage4);
			break;

		}// close switch

		return result;
	}

	/**
	 * 將引進的案件簽報書資料複製到動審表
	 * 
	 * @param l160m01a
	 *            動審表主檔
	 * @param l120m01a
	 *            案件簽報書主檔
	 * @return 複製完成的動審表主檔
	 * @throws CapException
	 */
	private L160M01A copyL120m01aToL160m01a(L160M01A l160m01a, L120M01A l120m01a)
			throws CapException {

		l160m01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
		l160m01a.setCaseLvl(Util.trim(l120m01a.getCaseLvl()));
		l160m01a.setCaseDate(l120m01a.getCaseDate());
		l160m01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
		l160m01a.setCaseSeq((l120m01a.getCaseSeq()));
		l160m01a.setCaseYear(l120m01a.getCaseYear());
		l160m01a.setCustId(Util.trim(l120m01a.getCustId().toUpperCase()));
		l160m01a.setCustName(Util.trim(l120m01a.getCustName()));
		l160m01a.setDupNo(Util.trim(l120m01a.getDupNo()));
		l160m01a.setTypCd(Util.trim(l120m01a.getTypCd()));
		// L120M01B L120m01b = lms1405Service.findL120m01bByUniqueKey(l120m01a
		// .getMainId());
		// l160m01a.setUCMainBranch(Util.isEmpty(L120m01b) ? "N" : L120m01b
		// .getUCntBranch());
		// l160m01a.setUnitLoanCase(Util.isEmpty(L120m01b) ? "N" : L120m01b
		// .getUnitCase());
		// L161S01A l161s01a = lms1601Service.findL161m01aByMainId(l160m01a
		// .getMainId());
		// if (Util.isEmpty(l161s01a)) {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// l161s01a = new L161S01A();
		// l161s01a.setCreator(user.getUserId());
		// l161s01a.setCreateTime(CapDate.getCurrentTimestamp());
		// l161s01a.setMainId(l160m01a.getMainId());
		// }
		// l161s01a.setCaseBrId(Util.trim(l120m01a.getCaseBrId()));
		//
		// // 簽約日期是否等於簽案日期
		// l161s01a.setSignDate(l120m01a.getCaseDate());
		// l161s01a.setCaseNo(Util.trim(l120m01a.getCaseNo()));
		// l161s01a.setCaseSeq(l120m01a.getCaseSeq());
		// l161s01a.setCaseYear(l120m01a.getCaseYear());
		// l161s01a.setGist(l120m01a.getGist());
		// lms1601Service.save(l161s01a);
		return l160m01a;

	}

	/**
	 * 當如果是新案要將L901M01A的項目寫入到L160M01C
	 * 
	 * @param mainId
	 *            動審表主檔mainId
	 * @return
	 * @throws CapException
	 */
	private List<L160M01C> copyL901m01aToL160m01c(String mainId, String local)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01C> l160m01cs = new ArrayList<L160M01C>();

		// 當如果是新案將全行項目跟該分行項目放入
		// 當是起新案的將目前的項目 918總行
		List<L901M01A> allItem = lms1605Service
				.findL901m01aByBranchIdAndItemType(
						UtilConstants.Usedoc.itemType.全行共同項目,
						UtilConstants.BankNo.授管處, local);

		// J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
		// 取得各國別動審表預設免填列註記設定
		boolean needChkAvoid = false;
		List<String> avoidList = null;
		L160M01A l160m01a = (L160M01A) lms1605Service
				.findL160M01AByMaindId(mainId);
		if (l160m01a != null) {
			String ownBrId = Util.trim(l160m01a.getOwnBrId());
			String countryType = "";
			IBranch ibranch = branchService.getBranch(ownBrId);
			// 當目前登錄 分行有總行時才顯示 呈總行的按鈕
			if (ibranch != null) {
				countryType = Util.trim(ibranch.getCountryType());
			}

			if (Util.notEquals(countryType, "")) {
				CodeType avoid = codeTypeService.findByCodeTypeAndCodeValue(
						"l160m01a.avoidDfCountry", Util.trim(countryType),
						"zh_TW");
				if (avoid != null) {
					String avoidStr = Util.trim(avoid.getCodeDesc());
					if (Util.notEquals(avoidStr, "")) {
						String[] avoidArray = avoidStr.split("\\|");
						avoidList = Arrays.asList(avoidArray);
						needChkAvoid = true;
					}
				}
			}
		}

		for (L901M01A item : allItem) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getItemSeq());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getItemContent());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.全行共同項目);

			// J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
			if (needChkAvoid) {
				if (avoidList.contains(Util.trim(item.getItemSeq()))) {
					l160m01c.setItemCheck("0");
				}
			}

			l160m01cs.add(l160m01c);
		}

		// 本地分行項目
		List<L901M01A> localItem = lms1605Service
				.findL901m01aByBranchIdAndItemType(
						UtilConstants.Usedoc.itemType.當地特殊規定項目,
						user.getUnitNo(), local);
		for (L901M01A item : localItem) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(item.getItemSeq());
			l160m01c.setMainId(mainId);
			l160m01c.setItemContent(item.getItemContent());
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.當地特殊規定項目);
			l160m01cs.add(l160m01c);
		}

		// 自訂項目
		for (int i = 1; i < 7; i++) {
			L160M01C l160m01c = new L160M01C();
			l160m01c.setCreator(user.getUserId());
			l160m01c.setCreateTime(CapDate.getCurrentTimestamp());
			l160m01c.setItemSeq(i);
			l160m01c.setMainId(mainId);
			l160m01c.setItemType(UtilConstants.Usedoc.itemType.自訂項目);
			l160m01cs.add(l160m01c);
		}

		return l160m01cs;

	}

	/**
	 * 將引進的案件簽報書資料複製連保人資料到主從債務人
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param mainId
	 *            要加進去的mainId
	 * @param custContry
	 *            簽報書所有借款人的國別 <custId+dupNo, 國別>
	 * @return 複製完成的主從債務人資料表檔
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private List<L162S01A> copyL140m01IToL162m01a(L140M01A l140m01a,
			String mainId, HashMap<String, String> custContry, String cntrNo)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L140M01J> l140m01js = (List<L140M01J>) lms1405Service
				.findModelListByMainId(L140M01J.class, l140m01a.getMainId());
		List<L162S01A> newL162s01as = new ArrayList<L162S01A>();

		// 聯行額度明細表或攤貸行額度序號會與額度明細表CNTRNO不同，所以應該是用L140M01A 對應的 L160M01B 的額度序號
		// 例如047 動用 0C3的簽報書時，額度明細表0C3500810067，但047的額度應該是047410500007
		// 所以要在外面就決定好用哪個cntrNo
		// String cntrNo = Util.trim(l140m01a.getCntrNo());

		String key = "";
		Set<String> isCreatePerson = new HashSet<String>();
		String custId = l140m01a.getCustId().toUpperCase();
		String dupNo = l140m01a.getDupNo().toUpperCase();
		key = custId + dupNo + custId + dupNo;
		if (!isCreatePerson.contains(key)) {
			isCreatePerson.add(key);
			// 當沒有資料先把主借人資料塞入
			L162S01A l162s01a = new L162S01A();
			// 設定主債務人資料
			l162s01a.setMainId(mainId);
			l162s01a.setCntrNo(cntrNo);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setCustId(custId);
			l162s01a.setDupNo(dupNo);
			l162s01a.setRKindD("");
			l162s01a.setRKindM("");
			String custName = l140m01a.getCustName();
			l162s01a.setRName(custName);
			if (custContry.containsKey(custId + dupNo)) {
				l162s01a.setRCountry(custContry.get(custId + dupNo));
			}
			// 設定主債務人資料
			l162s01a.setRId(custId);
			l162s01a.setRDupNo(dupNo);
			l162s01a.setDataSrc("1");
			l162s01a.setRType(UtilConstants.lngeFlag.共同借款人);
			newL162s01as.add(l162s01a);
		}
		for (L140M01J l140m01j : l140m01js) {
			key = custId + dupNo + l140m01j.getCustId() + l140m01j.getDupNo();
			if (isCreatePerson.contains(key)) {
				continue;
			} else {
				isCreatePerson.add(key);
			}
			L162S01A l162s01a = new L162S01A();
			// 設定主債務人資料
			l162s01a.setMainId(mainId);
			l162s01a.setCntrNo(cntrNo);
			l162s01a.setCreator(user.getUserId());
			l162s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162s01a.setCustId(custId);
			l162s01a.setDupNo(dupNo);

			// 設定從債務人資料
			l162s01a.setRCountry(l140m01j.getNtCode());
			l162s01a.setRId(l140m01j.getCustId());
			l162s01a.setRDupNo(l140m01j.getDupNo());
			l162s01a.setRName(l140m01j.getCustName());
			l162s01a.setDataSrc("1");
			l162s01a.setRType(l140m01j.getCustPos());
			String custRlt = Util.trim(l140m01j.getCustRlt());
			String rKindM = "";
			if (Util.isNotEmpty(custRlt)) {
				int index = custRlt.indexOf("X");
				switch (index) {
				case 0:
					rKindM = "2";
					break;
				case 1:
					rKindM = "1";
					break;
				case -1:
					rKindM = "3";
					break;
				}
			}
			l162s01a.setRType(UtilConstants.lngeFlag.共同借款人);
			l162s01a.setRKindD(custRlt);
			l162s01a.setRKindM(rKindM);
			newL162s01as.add(l162s01a);
		}

		// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
		// 當同時為連保人與擔保品提供人，則RTYPE 寫G就好， S 不需要再寫
		// 因為G 為連帶保證人，擔保品提供人兼連帶保證人
		List<L140M01I> l140m011TypeG = lms1405Service
				.findL140m01iListWithRType(l140m01a.getMainId(),
						UtilConstants.lngeFlag.連帶保證人);
		Set<String> guarantorGSet = new HashSet<String>();
		for (L140M01I l140m01i : l140m011TypeG) {
			key = custId + dupNo + l140m01i.getRId() + l140m01i.getRDupNo();
			guarantorGSet.add(key);
		}
		String guarantType = Util.trim(l140m01a.getGuarantorType());
		for (L140M01I l140m01i : l140m01a.getL140m01i()) {

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			if (Util.equals(l140m01i.getRType(), UtilConstants.lngeFlag.擔保品提供人)) {
				String chkSKey = custId + dupNo + l140m01i.getRId()
						+ l140m01i.getRDupNo();
				if (guarantorGSet.contains(chkSKey)) {
					// 因為G 為連帶保證人，擔保品提供人兼連帶保證人，所以S就不用再寫ㄧ筆了
					continue;
				}
			}

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			key = custId + dupNo + l140m01i.getRId() + l140m01i.getRDupNo()
					+ l140m01i.getRType();
			if (isCreatePerson.contains(key)) {
				continue;
			} else {
				isCreatePerson.add(key);
			}
			L162S01A l162m01a = new L162S01A();
			// 設定主債務人資料
			l162m01a.setMainId(mainId);
			l162m01a.setCntrNo(cntrNo);
			l162m01a.setCreator(user.getUserId());
			l162m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l162m01a.setCustId(custId);
			l162m01a.setDupNo(dupNo);

			// 設定從債務人資料
			l162m01a.setRCountry(l140m01i.getRCountry());
			l162m01a.setRId(l140m01i.getRId());
			l162m01a.setRDupNo(l140m01i.getRDupNo());
			l162m01a.setRKindD(Util.trim(l140m01i.getRKindD()));
			l162m01a.setRKindM(Util.trim(l140m01i.getRKindM()));
			l162m01a.setRName(l140m01i.getRName());
			l162m01a.setDataSrc("1");

			// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
			// 連帶保證人、擔保品提供人
			// if (Util.equals(l140m01i.getRType(),
			// UtilConstants.lngeFlag.連帶保證人)) {
			// l162m01a.setRType(UtilConstants.lngeFlag.連帶保證人);
			// } else {
			// l162m01a.setRType(Util.trim(l140m01i.getRType()));
			// }

			if (Util.equals(guarantType, "3")) {
				if (Util.equals(Util.trim(l140m01i.getGuarantorTypeItem()), "2")) { // 一般保證人
					l162m01a.setRType(UtilConstants.lngeFlag.ㄧ般保證人);
				} else { // 其餘的放連帶保證人
					l162m01a.setRType(UtilConstants.lngeFlag.連帶保證人);
				}
			} else {
				l162m01a.setRType(Util.trim(l140m01i.getRType()));
			}

			// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
			if (Util.equals(l140m01i.getRType(), UtilConstants.lngeFlag.擔保品提供人)) {
				l162m01a.setGuaPercent(null);
				// J-110-0040_05097_B1001 Web
				// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
				l162m01a.setGuaNaExposure(null);
			} else {
				if (Util.equals(l140m01a.getGuaPercentFg(), "Y")) {
					// 有按照比率
					// J-105-0100-001 Web
					// e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
					// if (Util.equals(l140m01i.getType(), "2")) {
					// 企業戶
					if (l140m01i.getGuaPercent() == null) {
						l162m01a.setGuaPercent(BigDecimal.valueOf(100));
					} else {
						l162m01a.setGuaPercent(l140m01i.getGuaPercent());
					}

					// } else {
					// l162m01a.setGuaPercent(BigDecimal.valueOf(100));
					// }
				} else {
					l162m01a.setGuaPercent(BigDecimal.valueOf(100));
				}

				// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
				l162m01a.setPriority(l140m01i.getPriority());

				// J-110-0040_05097_B1001 Web
				// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
				l162m01a.setGuaNaExposure(Util.trim(l140m01a.getGuaNaExposure()));
			}

			newL162s01as.add(l162m01a);
		}
		//G-113-0036 主從債務人
		//取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
		lms1605Service.checkL162s01aLocalIdAndGrtAmt(custId, dupNo, cntrNo, newL162s01as);

		
		return newL162s01as;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            動審表主檔mainId
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L160M01A l160m01a, Integer page) throws CapException {
		String mainId = l160m01a.getMainId();
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		switch (page) {
		case 1:
			result = DataParse.toResult(l160m01a);
			// 簽章欄
			if (!Util.isEmpty(l160m01a.getL160M01D())) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L160M01D l160m01d : l160m01a.getL160M01D()) {
					// 要加上人員代碼
					String type = Util.trim(l160m01d.getStaffJob());
					String userId = Util.trim(l160m01d.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}

				result.set("bossId", bossId.toString());
			}
			// result.set("showApprId",
			// lmsService.getUserName(l160m01a.getApprId()));
			result.set("ownBrName",
					" " + branchService.getBranchName(l160m01a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");
			// 確認全部動用是否有選
			if (UtilConstants.DEFAULT.否.equals(l160m01a.getAllCanPay())) {
				List<L160M01B> l160m01bs = (List<L160M01B>) lms1605Service
						.findListByMainId(L160M01B.class, mainId);
				for (L160M01B l160m01b : l160m01bs) {
					cntrNo.append(cntrNo.length() > 0 ? "," : "");
					cntrNo.append(Util.trim(l160m01b.getCntrNo()));
				}

			} else if (UtilConstants.DEFAULT.是.equals(l160m01a.getAllCanPay())) {

				// 簽報書項下額度明細表全部動用
				cntrNo.append(pop.getProperty("L160M01A.message3"));
			}
			result.set("creator", lmsService.getUserName(l160m01a.getCreator()));
			result.set("updater", lmsService.getUserName(l160m01a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l160m01a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		case 2:
			result = DataParse.toResult(l160m01a);
			List<L160M01C> allItem = (List<L160M01C>) lms1605Service
					.findListByMainId(L160M01C.class, mainId);
			JSONObject jsonAllItem = null;
			JSONObject jsonVal = null;
			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			JSONObject jsonFieldVal = null;
			JSONArray jsonArrayAllItem = new JSONArray();
			JSONArray jsonArrayLocalItem = new JSONArray();
			JSONArray jsonArrayselfItem = new JSONArray();
			JSONArray jsonArrayAllVal = new JSONArray();
			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			JSONArray jsonArrayAllItemFieldVal = new JSONArray();
			for (L160M01C item : allItem) {

				jsonAllItem = new JSONObject();
				jsonVal = new JSONObject();
				jsonVal.put("id", item.getOid());
				jsonVal.put("val", item.getItemCheck());
				jsonAllItem.put("id", item.getOid());

				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				jsonFieldVal = new JSONObject();
				jsonFieldVal.put("id", item.getOid());
				jsonFieldVal.put("val", item.getItemField1());

				if (Util.isEmpty(Util.trim(item.getItemContent()))
						|| UtilConstants.Usedoc.itemType.自訂項目.equals(item
								.getItemType())) {
					jsonAllItem.put("drc", item.getItemContent());
				} else {
					jsonAllItem.put("drc",
							item.getItemSeq() + "." + item.getItemContent());
				}
				if (UtilConstants.Usedoc.itemType.全行共同項目.equals(item
						.getItemType())
						|| UtilConstants.Usedoc.itemType.國內企金.equals(item
								.getItemType())) {

					// 全行項目
					jsonArrayAllItem.add(jsonAllItem);
					jsonArrayAllVal.add(jsonVal);
					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					jsonArrayAllItemFieldVal.add(jsonFieldVal);
				} else if (UtilConstants.Usedoc.itemType.當地特殊規定項目.equals(item
						.getItemType())) {

					// 本地分行項目
					jsonArrayAllVal.add(jsonVal);
					jsonArrayLocalItem.add(jsonAllItem);
					// J-111-0028_05097_B1001 Web
					// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
					jsonArrayAllItemFieldVal.add(jsonFieldVal);
				} else if (UtilConstants.Usedoc.itemType.自訂項目.equals(item
						.getItemType())) {
					jsonAllItem.put("id", item.getItemSeq());
					jsonVal.put("id", String.valueOf(item.getItemSeq()));
					// 自訂項目
					jsonArrayAllVal.add(jsonVal);
					jsonArrayselfItem.add(jsonAllItem);

				}

			}
			result.set("allItem", jsonArrayAllItem);
			result.set("localItem", jsonArrayLocalItem);
			result.set("elfItem", jsonArrayselfItem);
			result.set("value", jsonArrayAllVal);
			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			result.set("fieldValue", jsonArrayAllItemFieldVal);
			break;
		case 3:

			// 存放額度序號 map
			TreeMap<String, JSONArray> cntrNos = new TreeMap<String, JSONArray>();

			// 存放額度序號 map
			TreeMap<String, String> cntrNoMap = new TreeMap<String, String>();

			Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
			ArrayList<String> mainIds = new ArrayList<String>();

			/**
			 * {mainId:[cntrno,...,..]
			 */
			// 先把額度序號跟 mainId關連起來
			for (L160M01B l160m01b : l160m01bs) {
				String tempMainId = l160m01b.getReMainId();
				mainIds.add(tempMainId);
				if (cntrNos.containsKey(tempMainId)) {
					JSONArray value = cntrNos.get(tempMainId);
					value.add(l160m01b.getCntrNo());
				} else {
					JSONArray value = new JSONArray();
					value.add(l160m01b.getCntrNo());
					cntrNos.put(tempMainId, value);
				}
				cntrNoMap.put(l160m01b.getCntrNo(), l160m01b.getCntrNo());
			}
			// 存放客戶統編 map
			TreeMap<String, JSONArray> cusIds = new TreeMap<String, JSONArray>();
			List<L140M01A> l140m01as = lms1405Service
					.findL140m01aListByMainIdList(mainIds
							.toArray(new String[] {}));
			for (L140M01A l140m01a : l140m01as) {
				String key = l140m01a.getCustId().toUpperCase()
						+ l140m01a.getDupNo().toUpperCase();

				if (!cusIds.containsKey(key)) {
					cusIds.put(key, cntrNos.get(l140m01a.getMainId()));
				} else {
					JSONArray jsArr = cusIds.get(key);
					jsArr.addAll(JSONArray.toCollection(cntrNos.get(l140m01a
							.getMainId())));
					cusIds.put(key, jsArr);

				}
			}

			CapAjaxFormResult custIdSelect = new CapAjaxFormResult(cusIds);
			CapAjaxFormResult cntSelect = new CapAjaxFormResult(cntrNoMap);
			result.set("custIdSelect", custIdSelect);
			result.set("cntSelect", cntSelect);

			// 針對舊案調閱顯示原聯貸案參貸比率一覽表用，新動審表因為顯示額度動用資訊GRID，所以不走這邊
			if (Util.notEquals(l160m01a.getDocStatus(),
					CreditDocStatusEnum.海外_編製中)
					&& Util.notEquals(l160m01a.getDocStatus(),
							CreditDocStatusEnum.海外_待覆核)) {

				if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
						|| Util.equals(Util.trim(l160m01a.getNewVersion()),
								"00")) {

					// 找到對應主債務人的額度序號
					// 舊動審表上線時會把MAINID的值塞到UID
					L161S01A l161m01a = lms1605Service.findL161m01aByMainIdUid(
							mainId, mainId);
					if (l161m01a != null) {
						result = DataParse.toResult(l161m01a);

						result.set("gistOld", Util.equals(l161m01a.getGist(),
								"") ? "TWD" : l161m01a.getGist());
						result.set(
								"quotaCurrOld",
								Util.equals(l161m01a.getQuotaCurr(), "") ? "TWD"
										: l161m01a.getQuotaCurr());
						result.set(
								"quotaAmtOld",
								Util.isEmpty(l161m01a.getQuotaAmt()) ? BigDecimal.ZERO
										: l161m01a.getQuotaAmt());
						result.set("signDateOld", Util.isEmpty(l161m01a
								.getSignDate()) ? null : l161m01a.getSignDate());
						// try{
						// result.set("signDateOld",
						// Util.isEmpty(l161m01a.getSignDate()) ? new
						// SimpleDateFormat("yyyy-MM-dd").parse("0001-01-01") :
						// l161m01a.getSignDate());
						// }catch(Exception e){
						// throw new CapMessageException(e.toString(),
						// getClass());
						// }

						result.set("cntrNoOld",
								Util.equals(l161m01a.getCntrNo(), "") ? ""
										: l161m01a.getCntrNo());
						result.set(
								"unitLoanCaseShowOld",
								("Y".equals(l160m01a.getUnitLoanCase()) ? pop
										.getProperty("L160M01A.yes") : pop
										.getProperty("L160M01A.no")));
						result.set(
								"uCMainBranchShowOld",
								("Y".equals(l160m01a.getUCMainBranch()) ? pop
										.getProperty("L160M01A.yes") : pop
										.getProperty("L160M01A.no")));

						result.set("unitLoanCaseOld", ("Y".equals(l160m01a
								.getUnitLoanCase()) ? "Y" : "N"));
						result.set("uCMainBranchOld", ("Y".equals(l160m01a
								.getUCMainBranch()) ? "Y" : "N"));
						result.set("caseNoOld", l160m01a.getCaseNo());
						result.set("uidOld", l161m01a.getUid());
						result.set("showVersion", "0"); // 顯示舊案模式(聯貸參貸比率一覽表)
					} else {
						result.set("showVersion", "0"); // 顯示新按模式(額度動用資訊GRID)
					}

				} else {
					result.set("showVersion", "1"); // 顯示新按模式(額度動用資訊GRID)
				}

			}
			break;
		case 4:
			L163S01A l163s01a = lms1605Service.findL163m01aByMainId(l160m01a
					.getMainId());
			result = DataParse.toResult(l163s01a);
			if (!Util.isEmpty(l163s01a.getAppraiserId())) {
				result.set("appraiserId", l163s01a.getAppraiserId() + " "
						+ lmsService.getUserName(l163s01a.getAppraiserId()));
			}
			if (!Util.isEmpty(l163s01a.getBossId())) {
				result.set(
						"bossId",
						l163s01a.getBossId() + " "
								+ lmsService.getUserName(l163s01a.getBossId()));
			}
			if (!Util.isEmpty(l163s01a.getManagerId())) {
				// 當數字為0表示自行輸入
				if ("0".equals(l163s01a.getManagerId())) {
					result.set("managerId", Util.trim(l163s01a.getManagerNm()));
				} else {
					result.set("managerId", l163s01a.getManagerId() + " "
							+ lmsService.getUserName(l163s01a.getManagerId()));
				}
			}
			result.set("L163M01AForm", result);
			break;
		case 5:
			// J-106-0029-002 洗錢防制-新增洗錢防制頁籤
			// 洗錢防制
			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			CapAjaxFormResult LMS1205S20Form01 = new CapAjaxFormResult();
			L120S09A l120s09a = amlRelateService
					.findL120s09aMaxQDateByMainId(mainId);
			if (l120s09a != null) {
				Date blackListQDate = l120s09a.getQueryDateS();
				if (blackListQDate != null) {
					LMS1205S20Form01.set("blackListQDate", CapDate
							.formatDate(blackListQDate,
									UtilConstants.DateFormat.YYYY_MM_DD));

				}

			}

			// J-106-0238-001
			// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
			L120S09B l120s09b = amlRelateService.findL120s09bByMainId(mainId);
			if (l120s09b != null) {
				LMS1205S20Form01.set("ncResult", l120s09b.getNcResult());
				LMS1205S20Form01.set("uniqueKey", l120s09b.getUniqueKey());
				LMS1205S20Form01.set("refNo", l120s09b.getRefNo());
				LMS1205S20Form01.set("ncCaseId", l120s09b.getNcCaseId());
				if (Util.isNotEmpty(Util.nullToSpace(l120s09b.getQueryDateS()))) {
					LMS1205S20Form01.set("blackListQDate", CapDate.formatDate(
							l120s09b.getQueryDateS(),
							UtilConstants.DateFormat.YYYY_MM_DD));
				}
			}

			result.set("LMS1205S20Form01", LMS1205S20Form01);

			break;
		}// close switch case
		result.set("docStatusVal", l160m01a.getDocStatus());
		result.set("useType", l160m01a.getUseType());
		if (!Util.isEmpty(l160m01a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + l160m01a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + l160m01a.getTypCd()));
			result.set("showCustId", StrUtils.concat(l160m01a.getCustId()
					.toUpperCase(), " ", l160m01a.getDupNo().toUpperCase(),
					" ", l160m01a.getCustName()));
		}

		result.set("randomCode", l160m01a.getRandomCode());
		result.set(EloanConstants.OID, CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l160m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l160m01a.getMainId()));

		return result;

	}

	/**
	 * 呈主管覆核(呈主管 主管覆核 拆2個method)
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {

		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L160M01A l160m01a = (L160M01A) lms1605Service.findModelByOid(
				L160M01A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L160M01D> models = (List<L160M01D>) lms1605Service
					.findListByMainId(L160M01D.class, l160m01a.getMainId());
			if (!models.isEmpty()) {
				lms1605Service.deleteL160m01ds(models, false);
			}
			List<L160M01D> l160m01ds = new ArrayList<L160M01D>();
			for (String people : formSelectBoss) {
				L160M01D l160m01d = new L160M01D();
				l160m01d.setCreator(user.getUserId());
				l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l160m01d.setMainId(l160m01a.getMainId());

				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l160m01d.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l160m01d.setStaffNo(people);
				l160m01ds.add(l160m01d);
			}
			L160M01D managerL160m01d = new L160M01D();
			managerL160m01d.setCreator(user.getUserId());
			managerL160m01d.setCreateTime(CapDate.getCurrentTimestamp());
			managerL160m01d.setMainId(l160m01a.getMainId());
			managerL160m01d.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL160m01d.setStaffNo(manager);
			l160m01ds.add(managerL160m01d);
			L160M01D apprL160m01d = new L160M01D();
			apprL160m01d.setCreator(user.getUserId());
			apprL160m01d.setCreateTime(CapDate.getCurrentTimestamp());
			apprL160m01d.setMainId(l160m01a.getMainId());
			apprL160m01d.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL160m01d.setStaffNo(user.getUserId());
			l160m01ds.add(apprL160m01d);
			lms1605Service.saveL160m01dList(l160m01ds);
		}
		Boolean upMis = false;
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {

			l160m01a.setApprover(user.getUserId());
			l160m01a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			l160m01a.setReCheckId(user.getUserId());
			// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
			// for批次使用，系統實際核准時間，讓晚上批次可以抓資料寫進ELF601
			l160m01a.setSysActApproveTime(CapDate.getCurrentTimestamp());
			upMis = true;
			L160M01D l160m01d = lms1605Service.findL160m01d(
					l160m01a.getMainId(), user.getUserId(),
					UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l160m01d == null) {
				l160m01d = new L160M01D();
				l160m01d.setCreator(user.getUserId());
				l160m01d.setCreateTime(CapDate.getCurrentTimestamp());
				l160m01d.setMainId(l160m01a.getMainId());
				l160m01d.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l160m01d.setStaffNo(user.getUserId());
			}
			lms1605Service.save(l160m01d);

		}

		if (!Util.isEmpty(l160m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L160M01D l160m01d = lms1605Service.findL160m01d(
								l160m01a.getMainId(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);
						if (l160m01d != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						}

						// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
						if (l160m01a.getL163S01A() != null) {
							// L163S01A有可能存在，但沒有WaitingItem，所以要包在判斷是否有WaitingItem裡
							if (!Util.isEmpty(Util.trim(l160m01a.getL163S01A()
									.getWaitingItem()))) {
								// 主管核准時會檢查，預定補全日為今天以前(含今天)，代表他應該應該要收齊資料了，就不該是先行動用
								// 02O待覆核時才需要檢查，避免後面先行動用_待覆核時也走進來判斷
								if (Util.equals(l160m01a.getDocStatus(),
										CreditDocStatusEnum.海外_待覆核)
										&& CapDate.getCurrentTimestamp().after(
												l160m01a.getL163S01A()
														.getWillFinishDate())) {

									L120M01A l120m01a = lms1205Service
											.findL120m01aByMainId(l160m01a
													.getSrcMainId());
									// 目前只針對企金做此功能
									if (UtilConstants.Casedoc.DocType.企金
											.equals(l120m01a.getDocType())) {
										// L163M01A.willFinishDateError=先行動用控制表之預定補全日期需晚於今日日期
										Properties pop = MessageBundleScriptCreator
												.getComponentResource(LMS1605M01Page.class);
										throw new CapMessageException(
												pop.getProperty("L163M01A.willFinishDateError"),
												getClass());
									}
								}
							}
						}
					}
				}
				lms1605Service.flowAction(l160m01a.getOid(), l160m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms1605Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction] lms1605Service.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		Map<String, String> param = new HashMap<String, String>();
		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		if (Util.isEmpty(l160m01a)) {

			// L160M01A.error10=請先儲存
			param.put("msg", pop.getProperty("L160M01A.error10"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		String mainId = l160m01a.getMainId();
		List<L160M01C> l160m01cList = (List<L160M01C>) lms1605Service
				.findListByMainId(L160M01C.class, mainId);
		// 檢核是否引進額度明細表
		if (l160m01a.getL160m01b().isEmpty()) {

			// L160M01A.error3=請先引進額度明細表
			param.put("msg", pop.getProperty("L160M01A.error3"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		if (!Util.isEmpty(l160m01a.getGuFromDate())
				&& !Util.isEmpty(l160m01a.getGuEndDate())) {
			// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
			if (l160m01a.getGuFromDate().after(l160m01a.getGuEndDate())) {
				param.put("colName", pop.getProperty("L160M01A.guCurr") + " "
						+ pop.getProperty("L160M01A.guFromDate"));
				// L160M01A.guCurr"><!--連帶保證書最高本金
				// L160M01A.guFromDate"><!--期間-
				throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
			}
		}
		// 檢核動用期限是否登錄
		if (Util.isEmpty(l160m01a.getTType())) {

			// L160M01A.tType= 授信契約書
			param.put("colName", pop.getProperty("L160M01A.tType"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		} else if ("1".equals(l160m01a.getTType())) {
			param.put("colName", pop.getProperty("L160M01A.useDate"));
			switch (Util.parseInt(l160m01a.getUseSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getUseFromDate())
						|| Util.isEmpty(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getUseFromDate().after(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getUseMonth())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(l160m01a.getUseOther())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
		} else if ("2".equals(l160m01a.getTType())) {
			param.put("colName", pop.getProperty("L160M01A.useDate"));
			switch (Util.parseInt(l160m01a.getUseSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getUseFromDate())
						|| Util.isEmpty(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getUseFromDate().after(l160m01a.getUseEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getUseMonth())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(l160m01a.getUseOther())) {
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
			param.put("colName", pop.getProperty("L160M01A.lnDate"));
			switch (Util.parseInt(l160m01a.getLnSelect())) {
			case 1:
				if (Util.isEmpty(l160m01a.getLnFromDate())
						|| Util.isEmpty(l160m01a.getLnEndDate())) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
				if (l160m01a.getLnFromDate().after(l160m01a.getLnEndDate())) {
					throw new CapMessageException(RespMsgHelper.getMessage("EFD3026", param), getClass());
				}
				break;
			case 2:
				if (Util.isEmpty(l160m01a.getLnMonth())
						|| Util.isEmpty(l160m01a.getLnYear())) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			case 3:
				if (Util.isEmpty(Util.trim(l160m01a.getLnOther()))) {

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}
				break;
			default:
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
			}
		}
		// L160M01A.comm=審核意見
		if (Util.isEmpty(Util.trim(l160m01a.getComm()))) {
			param.put("colName", pop.getProperty("L160M01A.comm"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		}
		if (l160m01a.getL163S01A() != null) {
			// 檢查是否有待辦事項若有要將預定補全日填上日期
			if (!Util.isEmpty(Util
					.trim(l160m01a.getL163S01A().getWaitingItem()))) {
				if (Util.isEmpty(l160m01a.getL163S01A().getWillFinishDate())) {
					// L160M01A.willFinishDate=預定補全日期
					param.put("colName",
							pop.getProperty("L160M01A.willFinishDate"));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.欄位不得為空, param),
							getClass());
				}

				// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
				// 主管核准時會檢查，預定補全日為今天以前(含今天)，代表他應該應該要收齊資料了，就不該是先行動用
				// 01O編制中時才需要檢查
				// L163S01A有可能存在，但沒有WaitingItem，所以要包在判斷是否有WaitingItem裡
				if (Util.equals(l160m01a.getDocStatus(),
						CreditDocStatusEnum.海外_編製中)
						&& CapDate.getCurrentTimestamp().after(
								l160m01a.getL163S01A().getWillFinishDate())) {

					L120M01A l120m01a = lms1205Service
							.findL120m01aByMainId(l160m01a.getSrcMainId());
					// 目前只針對企金做此功能
					if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a
							.getDocType())) {
						// L163M01A.willFinishDateError=先行動用控制表之預定補全日期需晚於今日日期
						throw new CapMessageException(
								pop.getProperty("L163M01A.willFinishDateError"),
								getClass());
					}
				}
			}
		}

		// 當是否為管理行和是否有聯貸案為是檢核是否聯貸案參貸比率一覽表
		// 2013/07/03,Rex,同業聯貸的檢查條件改為UnitLoanCase 才需檢查並登打，且需判斷選入的額度明細表是否有同業聯貸
		// if ("Y".equals(l160m01a.getUnitLoanCase())
		// && "Y".equals(l160m01a.getUCMainBranch())) {

		if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
				|| Util.equals(Util.trim(l160m01a.getNewVersion()), "00")) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		}

		Set<L161S01A> l161s01as = l160m01a.getL161S01A();

		if (l161s01as.isEmpty()) {
			// 動審表請重新引進額度明細表
			throw new CapMessageException(
					pop.getProperty("L160M01A.message73"), getClass());
		} else {
			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			StringBuffer errCntrNo1 = new StringBuffer("");
			for (L161S01A l161s01a : l161s01as) {
				if (Util.notEquals(l161s01a.getChkYN(), "Y")) {
					// 額度動用資訊上未完成檢核，不得呈主管覆核
					param.put("msg", pop.getProperty("L160M01A.message74"));
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, param),
							getClass());
				}
				
				L140M01A l140m01a = lms1405Service
				.findL140m01aByMainId(l161s01a.getCntrMainId());
				
				if (Util.equals(l161s01a.getIsDerivatives(), "")) {

					Boolean hasDerivateSubjectFlag = false;
					if (l140m01a == null) {
						// 找不到額度明細表
						throw new CapMessageException(
								pop.getProperty("L160M01A.message66")
										+ l161s01a.getCntrNo(), getClass());
					}

					ArrayList<String> itemsAll = new ArrayList<String>();
					List<L140M01C> l140m01cs = lms1405Service
							.findL140m01cListByMainId(l140m01a.getMainId());

					if (l140m01cs != null && !l140m01cs.isEmpty()) {
						for (L140M01C l140m01c : l140m01cs) {
							itemsAll.add(l140m01c.getLoanTP());
						}

						hasDerivateSubjectFlag = lmsService
								.hasDerivateSubject(itemsAll
										.toArray(new String[itemsAll.size()]));

						if (hasDerivateSubjectFlag == true) {
							errCntrNo1.append(Util.equals(
									errCntrNo1.toString(), "") ? "" : "、");
							errCntrNo1.append(l161s01a.getCntrNo());

						}

					} else {
						// 找不到額度明細表
						throw new CapMessageException(
								pop.getProperty("L160M01A.message66")
										+ l161s01a.getCntrNo(), getClass());
					}

				} else if (Util.equals(l161s01a.getIsDerivatives(),
						UtilConstants.DEFAULT.是)) {
					if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
						errCntrNo1
								.append(Util.equals(errCntrNo1.toString(), "") ? ""
										: "、");
						errCntrNo1.append(l161s01a.getCntrNo());

					}
				}
				//J-112-0522 依金管會112年度專案檢查報告面請改善事項辦理，因營業單位漏未建檔報送金管會AI370聯合授信案基本資料(國內BTT-L556、海外AS400-3K30)，致本行每月報送報表產生報送錯誤，
				//擬將上述建檔機制移至E-LOAN-授信管理系統-動用審核表內，於動用審核表新增一頁籤(聯貸案基本資料)，營業單位於簽約動審時，須完成建檔作業。
				//相關報表 >> 額度動用資訊一覽表 >> 基本資訊 >> 額度控管種類為"30 - 聯貸"時，需檢核向下的攤貸額度序號都要存在於LNF277
				if(Util.equals(UtilConstants.Cntrdoc.snoKind.聯貸, l161s01a.getSnoKind())){
					String controlflag = Util.trim(lmsService.getSysParamDataValue("J-112-0522_CNTRNO_CHK"));
					if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
						Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
						if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
							for (L140M01E_AF l140m01e_af : l140m01e_afs) {
								String cntrNo = l140m01e_af.getShareNo();
								if(Util.isNotEmpty(cntrNo)){
									int count277 = misdbBASEService.findLNF277ByCntrno(cntrNo);
									if (count277 == 0){
										throw new CapMessageException(
												MessageFormat.format(
														pop.getProperty("L140M01E_AF.message01"),
														new StringBuffer().append(l161s01a.getCntrNo()).toString()
														//new StringBuffer().append(cntrNo).toString()
														),
												this.getClass());
									}
								}
							}
						}
					}
				}
			}

			if (Util.notEquals(errCntrNo1.toString(), "")) {
				throw new CapMessageException(
						pop.getProperty("L160M01A.cntrInfo")
								+ errCntrNo1.toString()
								+ pop.getProperty("L160M01A.message75") + "：「"
								+ pop.getProperty("L160M01A.dervApplyAmtType")
								+ "」", getClass());
			}

		}

		// if ("Y".equals(l160m01a.getUnitLoanCase())) {
		// List<L161S01B> l161m01bs = (List<L161S01B>) lms1605Service
		// .findListByMainId(L161S01B.class, mainId);
		//
		// String curr = l160m01a.getL161S01A().getQuotaCurr();
		//
		// if ("Y".equals(l160m01a.getUCMainBranch())) {
		// if (l161m01bs.isEmpty()) {
		// // L160M01A.message29=「聯貸案參貸比率一覽表」，尚未登錄，不得呈主管覆核
		// param.put("msg", pop.getProperty("L160M01A.message29"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		// }
		//
		// // 檢查聯行攤貸總額幣別
		// if (Util.isEmpty(Util.trim(curr))) {
		// // L160M01A.title10=聯貸案參貸比率一覽表
		// // L160M01A.curr=幣別
		// // EFD0005=ERROR|$\{colName\}此欄位不可空白|
		// param.put("colName", pop.getProperty("L160M01A.title10") + " "
		// + pop.getProperty("L160M01A.curr"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		// }
		//
		// String cntrno = l160m01a.getL161S01A().getCntrNo();
		// // 檢查額度序號是否輸入
		// if (Util.isEmpty(Util.trim(cntrno))) {
		// // L160M01A.title10=聯貸案參貸比率一覽表
		// // L160M01A.cntrNum=額度序號
		// // EFD0005=ERROR|$\{colName\}此欄位不可空白|
		// param.put("colName", pop.getProperty("L160M01A.title10") + " "
		// + pop.getProperty("L160M01A.cntrNum"));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.欄位不得為空, param), getClass());
		// }
		//
		// }
		//
		// String hasKeyL161S01B = "N";
		// Long count = ZERO;
		// for (L161S01B l161m01b : l161m01bs) {
		// if (hasKeyL161S01B.equals("N")) {
		// hasKeyL161S01B = "Y";
		// }
		//
		// count += (l161m01b.getSlAmt().longValue());
		// }
		//
		// // 檢查輸入的總額度與聯行攤貸總額金額是否一樣
		// if (hasKeyL161S01B.equals("Y")) {
		// if (count != l160m01a.getL161S01A().getQuotaAmt().longValue()) {
		// //
		// L160M01A.message30=「聯貸案參貸比率一覽表」，所有參貸金額加總後({0})與總額度({1})不相等，不得呈主管覆核。
		// param.put("noticeMsg", MessageFormat.format(
		// pop.getProperty("L160M01A.message30"),
		// curr + " " + NumConverter.addComma(count),
		// curr
		// + " "
		// + NumConverter.addComma(l160m01a.getL161S01A()
		// .getQuotaAmt().longValue())));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// "EFD0015", param), getClass());
		// }
		// }
		//
		//
		// }
		//
		// // 檢核動審表若有同業聯貸參貸，選取的額度序號必須有勾選有同業參貸額度
		// String unitLoanCase = Util.trim(l160m01a.getUnitLoanCase());
		// String uCMainBranch = Util.trim(l160m01a.getUCMainBranch());
		// if (Util.equals(unitLoanCase, "Y") && Util.equals(uCMainBranch, "Y"))
		// {
		// String selCntrNo = l160m01a.getL161S01A().getCntrNo();
		// for (L160M01B l160m01b : l160m01a.getL160m01b()) {
		// String cntrNo = l160m01b.getCntrNo();
		// if (Util.notEquals(cntrNo, "")) {
		// String reMainId = l160m01b.getReMainId();
		// if (Util.equals(selCntrNo, cntrNo)) {
		// L140M01A l140m01a = lms1405Service
		// .findL140m01aByMainId(reMainId);
		// String unitCase2 = l140m01a.getUnitCase2();
		// if (Util.notEquals(unitCase2, "Y")) {
		// String errorMsg = MessageFormat.format(
		// pop.getProperty("L160M01A.message65"),
		// selCntrNo);
		// throw new CapMessageException(
		// RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤,
		// errorMsg), getClass());
		// }
		// break;
		// }
		// }
		//
		// }
		//
		// }

		// 檢核是否登錄主從債務表
		List<L162S01A> l162m01as = (List<L162S01A>) lms1605Service
				.findListByMainId(L162S01A.class, mainId);
		if (l162m01as.isEmpty()) {
			// L160M01A.message28=「主從債務人資料表」，尚未登錄，不得呈主管覆核
			param.put("msg", pop.getProperty("L160M01A.message28"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}
		// 檢查主從人債務表是否必填欄位都有填寫
		HashMap<String, String> tempMap = new HashMap<String, String>();
		// 檢查債務人是否存在
		HashMap<String, String> getAllCustIdMap = new HashMap<String, String>();
		HashMap<String, String> cntrNoGuaNaExposureMap = new HashMap<String, String>();
		for (L162S01A l162s01a : l162m01as) {
			getAllCustIdMap.put(l162s01a.getCustId() + l162s01a.getDupNo(), "");
			getAllCustIdMap.put(l162s01a.getRId() + l162s01a.getRDupNo(), "");
			if (Util.isEmpty(l162s01a.getRCountry())) {
				tempMap.put(l162s01a.getCustId().toUpperCase() + " "
						+ l162s01a.getDupNo().toUpperCase() + " "
						+ l162s01a.getRId().toUpperCase() + " "
						+ l162s01a.getRDupNo().toUpperCase(), l162s01a
						.getCntrNo().toUpperCase());
			}
			// 主債務人的檢查
			if (l162s01a.getCustId().toUpperCase()
					.equals(l162s01a.getRId().toUpperCase())
					&& l162s01a.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase())) {
			}

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			String guaNaExposure = Util.trim(l162s01a.getGuaNaExposure());
			String rType = Util.trim(l162s01a.getRType());
			if (Util.notEquals(rType, "C") && Util.notEquals(rType, "S")) {

				// 檢查欄位不得空白
				String guarantorPriorityOn = Util.trim(lmsService
						.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));
				if (Util.equals(guarantorPriorityOn, "Y")) {
					if (Util.equals(guaNaExposure, "")) {
						throw new CapMessageException(MessageFormat.format(
								pop.getProperty("L162M01A.error03"),
								l162s01a.getRId() + " "
										+ Util.trim(l162s01a.getRName()) + " "
										+ Util.trim(l162s01a.getCntrNo()),
								pop.getProperty("L162M01A.guaNaExposure")),
								getClass());
					}

					// 檢查同一額度序號選的結果要一樣
					if (cntrNoGuaNaExposureMap.containsKey(Util.trim(l162s01a
							.getCntrNo()))) {
						if (Util.notEquals(
								guaNaExposure,
								MapUtils.getString(cntrNoGuaNaExposureMap,
										Util.trim(l162s01a.getCntrNo())))) {
							// L162M01A.error04=「{0}」欄位「{1}」同一額度序號下所有借款人選擇必須一致
							throw new CapMessageException(MessageFormat.format(
									pop.getProperty("L162M01A.error04"),
									l162s01a.getRId() + " "
											+ Util.trim(l162s01a.getRName())
											+ " "
											+ Util.trim(l162s01a.getCntrNo()),
									pop.getProperty("L162M01A.guaNaExposure")),
									getClass());
						}
					} else {
						cntrNoGuaNaExposureMap.put(
								Util.trim(l162s01a.getCntrNo()), guaNaExposure);
					}
				}

			}

		}

		// BGN J-106-0029-004
		// 洗錢防制-動審表新增洗錢防制頁籤************************************************
		// 檢查實質受益人與負責人

		// 主要借款人
		LinkedHashMap<String, String> cntrNoMainBorrowerMap = new LinkedHashMap<String, String>();

		// 主要借款人+共同借款人
		LinkedHashMap<String, String> allBorrowerIdMap = new LinkedHashMap<String, String>();
		Set<L160M01B> l160m01bs = l160m01a.getL160m01b();
		if (l160m01bs != null && !l160m01bs.isEmpty()) {
			for (L160M01B l160m01b : l160m01bs) {
				L140M01A l140m01a = amlRelateService.findModelByMainId(
						L140M01A.class, l160m01b.getReMainId());
				if (l140m01a != null) {
					String chkId = Util.trim(l140m01a.getCustId());
					String chkDupNo = Util.trim(l140m01a.getDupNo());
					if (Util.notEquals(chkId, "")
							&& Util.notEquals(chkDupNo, "")) {
						if (!cntrNoMainBorrowerMap.containsKey(l160m01b
								.getCntrNo())) {
							cntrNoMainBorrowerMap.put(l160m01b.getCntrNo(),
									chkId + "-" + chkDupNo);
						}

						if (!allBorrowerIdMap.containsKey(chkId + "-"
								+ chkDupNo)) {
							allBorrowerIdMap.put(chkId + "-" + chkDupNo,
									l140m01a.getCustName());
						}

					}
				}
			}
		}
		for (L162S01A l162s01a : l162m01as) {
			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.共同借款人)) {
				String chkId = Util.trim(l162s01a.getRId());
				String chkDupNo = Util.trim(l162s01a.getRDupNo());
				if (Util.notEquals(chkId, "") && Util.notEquals(chkDupNo, "")) {
					if (!allBorrowerIdMap.containsKey(chkId + "-" + chkDupNo)) {
						allBorrowerIdMap.put(chkId + "-" + chkDupNo,
								l162s01a.getRName());
					}
				}
			}
		}

		if (cntrNoMainBorrowerMap != null && !cntrNoMainBorrowerMap.isEmpty()) {
			// 檢查本次動用之額度與主借款人是否未列於相關報表->主從債務人資料表中。
			String errMsg = amlRelateService.chkMainBorrowerInL162S01A(mainId,
					cntrNoMainBorrowerMap);
			if (Util.notEquals(errMsg, "")) {
				// AML.error009=本次動用之額度與主借款人{0}未列於相關報表->主從債務人資料表中。
				throw new CapMessageException(errMsg, getClass());
			}

		}

		if (allBorrowerIdMap != null && !allBorrowerIdMap.isEmpty()) {
			// 檢查實質受益人欄未有無輸入 + 有沒有完成身分確認
			String errMsg = amlRelateService.chkBeneficiaryIsOkForDrawDown(
					mainId, allBorrowerIdMap);
			if (Util.notEquals(errMsg, "")) {
				// AML.error008=借款人/共同借款人{0}於相關報表->主從債務人資料表中尚有洗錢防制所需欄位未完成輸入。
				throw new CapMessageException(errMsg, getClass());
			}
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		// 檢查黑名單
		amlRelateService.chkBlackListFullExitForDrawDown(mainId, true);

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 檢核簽報書調查狀態是否已經完成可以送呈主管
		// lmsL120M01A.error036=簽報書AML/CFT頁籤之「黑名單案件調查結果」尚未完成，請先執行「取得黑名單查詢結果」按鈕。
		amlRelateService.chkNcResultFinishCanSendBoss(mainId);

		// J-106-0238-001
		// 因應於e-Loan授信管理系統企、個金徵、授信業務防制洗錢作業頁籤，對應0015黑名單檢核命中審查之後續作業，增加「黑名單/黑國家/政治敏感人物交易具體檢核機制」
		// 檢核借款人是否為0024拒絕交易名單
		// 檢核0024有拒絕交易且額度明細表有新作時可否送呈
		// lmsL120M01A.error035=請注意! 下列借款人於0024客戶中文檔為拒絕交易狀態!
		amlRelateService.chk0024RejecjTrancCanNotSendBoss(mainId);

		// END J-106-0238-001
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		// 若有借戶身分但沒有「受告誡處分」引入結果要擋
		// 會出現以下訊息
		// L120S09a.cmfwarnpResult.error=AML頁籤尚未執行告誡戶掃描
		amlRelateService.checkCmfwarnpNeed(mainId);
		// ********************************************************************************************************

		// END J-106-0029-004
		// 洗錢防制-動審表新增洗錢防制頁籤************************************************

		Map<String, Object> custData = null;
		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		Map<String, String> custBusCd = new HashMap<String, String>();

		// 檢查債務人是否都有建檔
		StringBuffer temp0024 = new StringBuffer();
		for (String key : getAllCustIdMap.keySet()) {
			String custId = key.substring(0, key.length() - 1);
			String dupNo = key.substring(key.length() - 1, key.length());

			custData = misCustdataService.findAllByByCustIdAndDupNo(custId,
					dupNo);
			if (custData == null || custData.isEmpty()) {
				temp0024.append(temp0024.length() > 0 ? "" : "");
				temp0024.append(custId + " " + dupNo);
			} else {
				// J-103-0299-001
				// Web e-Loan企金額度明細表保證人新增保證比例
				custBusCd.put(Util.trim(custId) + Util.trim(dupNo), custData
						.get("BUSCD") != null ? custData.get("BUSCD")
						.toString() : "999999");
			}
		}
		if (temp0024.length() > 0) {
			// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
			param.put("custId", temp0024.toString());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
		}
		StringBuffer temp = new StringBuffer();
		if (!tempMap.isEmpty()) {
			for (String key : tempMap.keySet()) {
				temp.append("<br/>");
				temp.append(key);
				temp.append(" ");
				temp.append(tempMap.get(key));
			}
			// L160M01A.message51=「主從債務人資料表」以下借款人，資料不完整，不得呈主管覆核{0}
			param.put("msg", MessageFormat.format(
					pop.getProperty("L160M01A.message51"), temp.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		// J-105-0100-001 Web e-Loan授信管理系統企金案件額度明細表之自然人保證人保證比例欄位開放可自行輸入
		// StringBuffer guaPercentErr = new StringBuffer("");
		// for (L162S01A l162s01a : l162m01as) {
		// String rType = l162s01a.getRType();
		// String rCustId = Util.trim(l162s01a.getRId());
		// String rDupNo = Util.trim(l162s01a.getRDupNo());
		// String rName = Util.trim(l162s01a.getRName());
		// String tKey = rCustId + rDupNo;
		// if (Util.notEquals(rType, UtilConstants.lngeFlag.共同借款人)
		// && Util.notEquals(rType, UtilConstants.lngeFlag.擔保品提供人)) {
		// if (!custBusCd.isEmpty()) {
		// if (custBusCd.containsKey(tKey)) {
		// String busCd = custBusCd.get(tKey) != null ? custBusCd
		// .get(tKey).toString() : "999999";
		//
		// if (LMSUtil.isBusCode_060000_130300(busCd)) {
		// BigDecimal guaPercent = l162s01a.getGuaPercent();
		// if (!Util.isEmpty(guaPercent)) {
		// if (guaPercent.compareTo(BigDecimal
		// .valueOf(100)) != 0) {
		// guaPercentErr.append(Util.equals(
		// guaPercentErr.toString(), "") ? ""
		// : "、");
		// guaPercentErr.append(rCustId + "-" + rName
		// + "(" + l162s01a.getCntrNo() + ")");
		// }
		// }
		// }
		// }
		// }
		// }
		//
		// }
		// if (guaPercentErr.length() > 0) {
		// // L162M01A.error02=「{0}」非法人戶之保證人負担保證責任比率欄位必須為100
		// // 為了可以換行呈現 在最前面加上<br> tag
		// guaPercentErr.insert(0, "<br/>");
		// param.put(
		// "msg",
		// " "
		// + MessageFormat.format(
		// pop.getProperty("L162M01A.error02"),
		// guaPercentErr.toString()));
		// throw new CapMessageException(RespMsgHelper.getMessage(parent,
		// UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		//
		// }

		/**
		 * J-101-0104
		 * 
		 * <pre>
		 * 檢查
		 * 動審表增加五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核。
		 * 1.核准敘做文件；
		 * 3.額度本票；
		 * 10.身分證遺失檢查；
		 * 11.查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料	；
		 * 12.恐怖份子黑名單查詢紀錄；
		 * 13.銀行法及金控法利害關係人查詢紀錄(簽約時須再查一次)；
		 * 14.同一關係企業/集團企業建檔維護；
		 * 15.應收帳款承購無追索權—買方；銀行法及金控法利害關係人查詢紀錄；
		 * 17.核貸條件其他應辦事項之土地信託或地上權設定；
		 * 22.擔保品設押(質)。
		 * 
		 * </pre>
		 */
		StringBuffer reguiredItem = new StringBuffer();
		for (L160M01C l160m01c : l160m01cList) {
			int seq = l160m01c.getItemSeq();
			if (UtilConstants.Usedoc.itemType.全行共同項目.equals(l160m01c
					.getItemType())) {
				if (this.isRequireItem(seq)
						&& UtilConstants.Usedoc.checkItem.未收.equals(l160m01c
								.getItemCheck())) {
					reguiredItem.append(reguiredItem.length() > 0 ? "<br/>"
							: "");
					reguiredItem.append(seq + ".");
					reguiredItem.append(l160m01c.getItemContent());
				}
			}
		}
		if (reguiredItem.length() > 0) {
			// L160M01A.message53=五、其他事項之下列項目未收到(未辦妥)，不得呈主管覆核:{0}
			// 為了可以換行呈現 在最前面加上<br> tag
			reguiredItem.insert(0, "<br/>");
			param.put(
					"msg",
					" "
							+ MessageFormat.format(
									pop.getProperty("L160M01A.message53"),
									reguiredItem.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());

		}

		// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
		StringBuffer reguiredItemField1 = new StringBuffer();
		for (L160M01C l160m01c : l160m01cList) {
			int seq = l160m01c.getItemSeq();
			if (UtilConstants.Usedoc.itemType.全行共同項目.equals(l160m01c
					.getItemType())
					|| UtilConstants.Usedoc.itemType.當地特殊規定項目.equals(l160m01c
							.getItemType())) {
				String itemContent = l160m01c.getItemContent();
				String itemField1Fg1 = Util.trim(lms1605Service
						.isL160m01cHasInputItemField1(l160m01c));
				if (Util.notEquals(itemField1Fg1, "")) {
					if (UtilConstants.Usedoc.checkItem.已收.equals(l160m01c
							.getItemCheck())) {
						if (Util.isEmpty(Util.trim(l160m01c.getItemField1()))) {
							reguiredItemField1.append(reguiredItemField1
									.length() > 0 ? "<br/>" : "");
							reguiredItemField1.append(seq + ".");
							reguiredItemField1.append(StringUtils.replace(
									l160m01c.getItemContent(), itemField1Fg1,
									""));
						}
					}

				}
			}
		}
		if (reguiredItemField1.length() > 0) {
			// L160M01A.message108=其他事項之下列項目有欄位輸入未完成，不得呈主管覆核:{0}
			reguiredItemField1.insert(0, "<br/>");
			param.put(
					"msg",
					" "
							+ MessageFormat.format(
									pop.getProperty("L160M01A.message108"),
									reguiredItemField1.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());

		}

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		// 檢核保證人順位是否有缺漏
		// 借款人為企業戶且保證人(一般/連帶)亦為企業戶時，需要填列保證人信用品質順序。

		String guarantorPriorityOn = Util.trim(lmsService
				.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));
		if (Util.equals(guarantorPriorityOn, "Y")) {
			l161s01as = l160m01a.getL161S01A();
			if (!l161s01as.isEmpty()) {
				for (L161S01A l161s01a : l161s01as) {
					String cntrNo = Util.trim(l161s01a.getCntrNo());
					String mainCustId = Util.trim(l161s01a.getCustId());
					String mainDupNo = Util.trim(l161s01a.getDupNo());
					// 借款人是企業戶才要檢核

					// J-110-0007_05097_B1003 Web
					// e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定

					// Map<String, Object> custDataMap = misCustdataService
					// .findAllByByCustIdAndDupNo(mainCustId, mainDupNo);
					//
					// if (custDataMap != null && !custDataMap.isEmpty()) {
					// // 保證人是個人戶就不要
					// if (Util.equals(custDataMap.get("BUSCD"), "130300")
					// || Util.equals(custDataMap.get("BUSCD"),
					// "060000")) {
					// continue;
					// }
					// }

					List<L162S01A> l162s01as = lms1605Service
							.findL162s01aByMainIdCntrno(mainId, cntrNo);
					// 取得必要順序的保證人
					// 再刪選需要的保證人(保證人企業戶且為)
					List<L162S01A> mustL162s01as = lms1605Service
							.findL162s01aNeedPriority(l162s01as, cntrNo);

					if (mustL162s01as != null && !mustL162s01as.isEmpty()) {

						// 檢查保證比例100%前是否都有信用品質順序
						BigDecimal totGuaPercent = BigDecimal.ZERO;
						int setCount = 0;
						for (L162S01A l162s01a : mustL162s01as) {

							if (l162s01a.getPriority() != null
									&& l162s01a.getPriority().compareTo(
											BigDecimal.ZERO) > 0) {

								BigDecimal guaPercent = l162s01a
										.getGuaPercent() == null ? BigDecimal.ZERO
										: l162s01a.getGuaPercent();
								totGuaPercent = totGuaPercent.add(guaPercent);
								setCount = setCount + 1;

							}

						}

						if (mustL162s01as.size() > setCount
								&& totGuaPercent.compareTo(Util
										.parseBigDecimal("100")) < 0) {
							// L140M01a.message262=保證人必需設定信用品質順序，直到該額度之保證人之負?保證責任比率合計達100%。
							Properties prop = MessageBundleScriptCreator
									.getComponentResource(LMS1405S02Panel.class);
							String msg = "「" + cntrNo + "」"
									+ prop.getProperty("L140M01a.message262");
							throw new CapMessageException(
									RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
						}
					}

				}
			}
		}

		// J-111-0406_05097_B1001 Web
		// e-Loan企金授信12305479(興富發建設股份有限公司)限制不得動撥及選擇上述所列二授信業務暨其相對應之會計科目。
		for (L160M01B l160m01b : l160m01a.getL160m01b()) {

			L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l160m01b
					.getReMainId());

			if (l140m01a != null) {
				String noLoanMsg = lmsService.chkNoLoanId(l140m01a, "2");
				if (Util.notEquals(noLoanMsg, "")) {
					throw new CapMessageException(noLoanMsg, getClass());
				}
			}
		}
		
		// J-112-0196_08035_B1001  動審表送呈檢查是否有已覆核登記之保證擔保品
		String j1120194_is_online = Util.trim(lmsService.getSysParamDataValue("J-112-0194_is_online"));
		
		if ("Y".equals(j1120194_is_online)){
			StringBuilder errMsg = new StringBuilder();
			for (L160M01B l160m01b : l160m01a.getL160m01b()) {
				L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l160m01b.getReMainId());

				if (l140m01a != null && "20".equals(l140m01a.getSnoKind())) {
					List<Map<String, Object>> cms05Map = eloandbBASEService.findCmsCollType05ByCntrNo(l140m01a.getCntrNo());
					if (cms05Map == null || cms05Map.isEmpty()) {
						errMsg.append(l140m01a.getCntrNo()).append(" ");
					}
				}
			}
			if (Util.isNotEmpty(errMsg)) {
				errMsg.append(pop.getProperty("L160M01A.message118"));
				throw new CapMessageException(errMsg.toString(), getClass());
			}
		}
		//是否檢核行業別 //只針對企金檢核
		String classCode9_controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0166_controlFlag"));
		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l160m01a.getSrcMainId());
		if(Util.equals(UtilConstants.DEFAULT.是, classCode9_controlFlag) && Util.equals(UtilConstants.Casedoc.DocType.企金, l120m01a.getDocType())){
			//本次動用資訊，檢查所有額度借款人
			for (L160M01B l160m01b : l160m01a.getL160m01b()) {
				L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l160m01b.getReMainId());
				//來源簽報書額度動用的借款人
				L120S01B l120s01b = lms1205Service.findL120s01bByUniqueKey(l160m01a.getSrcMainId(), l140m01a.getCustId(), l140m01a.getDupNo());
				//檢核 MIS.CUSTDATA 與 
				Map<String, Object> custInfo = misCustdataService.findAllByByCustIdAndDupNo(l140m01a.getCustId(), l140m01a.getDupNo());
				if(l120s01b != null && custInfo != null){
					StringBuilder errMsg = new StringBuilder();
					//0024行業別與簽案借款人行業別不同
					if(Util.notEquals(Util.trim(l120s01b.getBusCode()), Util.trim(MapUtils.getString(custInfo, "BUSCD", "")))){
						//J-113-0166_11850_B1001 依客戶取得是否有 ELF442_CLASS_CODE=9(預約額度 09: 行業對象別申請確認暨修改)，如有資料則跳警示
						List<Map<String, Object>> elf442List = misELF442Service.findELF442ByCustIdByClassCode(l140m01a.getCustId(), l140m01a.getDupNo(), "09");
						if(elf442List == null || elf442List.isEmpty()){//沒申請
							errMsg.append(pop.getProperty("L160M01A.message119"));
							throw new CapMessageException(errMsg.toString(), getClass());
						}else{//有申請，需確認申請的行業別是否相同, 不同還是要擋住
							if(elf442List != null && !elf442List.isEmpty()){
								for (Map<String, Object> elf442Map : elf442List) {
									//ELF442_CLASS_CODE =09時   為申請調整的行業對象別
									String elf442Site4No= Util.trim(MapUtils.getString(elf442Map, "ELF442_SITE4NO", ""));
									if(Util.notEquals(elf442Site4No, Util.trim(MapUtils.getString(custInfo, "BUSCD", "")))){
										errMsg.append(pop.getProperty("L160M01A.message119"));
										throw new CapMessageException(errMsg.toString(), getClass());
									}
								}
							}
						}
					}
				}
			}
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;
	}

	/**
	 * 檢查是否為必填項目
	 * 
	 * @param seq
	 *            檢查序號
	 * @return 是 |否
	 */
	private Boolean isRequireItem(int seq) {
		/**
		 * J-101-0104
		 * 
		 * <pre>
		 * 檢查
		 * 動審表增加五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核。
		 * 1.核准敘做文件；
		 * 3.額度本票；
		 * 10.身分證遺失檢查；
		 * 11.查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料	；
		 * 12.恐怖份子黑名單查詢紀錄；
		 * 13.銀行法及金控法利害關係人查詢紀錄(簽約時須再查一次)；
		 * 14.同一關係企業/集團企業建檔維護；
		 * 15.應收帳款承購無追索權—買方；銀行法及金控法利害關係人查詢紀錄；
		 * 17.核貸條件其他應辦事項之土地信託或地上權設定；
		 * 22.擔保品設押(質)。
		 * 
		 * </pre>
		 */

		// for (Integer theSeq : UtilConstants.Usedoc.動審表必填項目) {
		// if (theSeq == seq) {
		// return true;
		// }
		// }

		// COM_J1070152_160M01C_CHK_OV
		// 1,3,10,11,12,13,14,15,17,22
		// 海外-動審表檢查五、其他事項，下列查核項目應辦而未完成，不得呈主管覆核之檢核
		String chkItems = Util.trim(lmsService
				.getSysParamDataValue("COM_J1070152_160M01C_CHK_OV"));
		if (Util.notEquals(chkItems, "")) {
			for (String xx : chkItems.split(",")) {
				if (Util.equals(xx, Util.trim(seq))) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 壓力測試用
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult TETSMIS(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);
		lms1605Service.upLoadMIS(l160m01a);
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		return result;

	}

	/**
	 * 查詢L161S01A 聯貸案參貸比率一覽表主檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL161s01a(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		Properties prop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		L161S01A l161s01a = lms1605Service.findModelByOid(L161S01A.class, oid);

		if (!Util.isEmpty(l161s01a)) {
			if (Util.equals(l161s01a.getIsDerivatives(), "")) {
				Boolean hasDerivateSubjectFlag = false;
				String mainId140 = l161s01a.getCntrMainId();
				L140M01A l140m01a = lms1405Service
						.findL140m01aByMainId(mainId140);
				if (l140m01a == null) {
					// 找不到額度明細表
					throw new CapMessageException(
							prop2.getProperty("L160M01A.message66"), getClass());
				}

				ArrayList<String> itemsAll = new ArrayList<String>();
				List<L140M01C> l140m01cs = lms1405Service
						.findL140m01cListByMainId(l140m01a.getMainId());

				if (l140m01cs != null && !l140m01cs.isEmpty()) {
					for (L140M01C l140m01c : l140m01cs) {
						itemsAll.add(l140m01c.getLoanTP());
					}

					hasDerivateSubjectFlag = lmsService
							.hasDerivateSubject(itemsAll
									.toArray(new String[itemsAll.size()]));

					if (hasDerivateSubjectFlag == true) {
						l161s01a.setIsDerivatives("Y");
					}
				} else {
					// 找不到額度明細表
					throw new CapMessageException(
							prop2.getProperty("L160M01A.message66"), getClass());
				}
			}

		}

		if (!Util.isEmpty(l161s01a)) {
			result = DataParse.toResult(l161s01a);
		}

		result.set("proPertyShowMsg",
				Util.equals(Util.trim(l161s01a.getProperty()), "") ? ""
						: LMSUtil.getProPerty(l161s01a.getProperty(), prop));
		return result;
	}

	/**
	 * 儲存L161M01B 聯貸案參貸比率一覽表明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL161s01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));
		String formL161m01a = params.getString("L161M01AForm");

		JSONObject jsonL161m01a = JSONObject.fromObject(formL161m01a);
		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);
		L161S01A l161s01a = lms1605Service.findL161m01aByMainIdUid(mainId, uid);

		if (Util.isEmpty(l161s01a)) {
			l161s01a = new L161S01A();
			DataParse.toBean(jsonL161m01a, l161s01a);
			l161s01a.setCreator(user.getUserId());
			l161s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l161s01a.setMainId(mainId);
		} else {
			DataParse.toBean(jsonL161m01a, l161s01a);
		}

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		String validate = Util.validateColumnSize(l161s01a, prop, "L160M01A");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}

		int countUnitMega = 0;
		int countUnitBank = 0;
		if (Util.equals(jsonL161m01a.optString("unitCase", ""), "Y")
				|| Util.equals(jsonL161m01a.optString("unitMega", ""), "Y")) {

			Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

			if (l161s01bs != null && !l161s01bs.isEmpty()) {
				for (L161S01B l161s01b : l161s01bs) {

					if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
						// hasUnitMega = true;
						countUnitMega = countUnitMega + 1;
					} else {
						// hasUnitBank = true;
						countUnitBank = countUnitBank + 1;
					}

				}
			}

		}
		if (countUnitBank > 0) {
			l161s01a.setCoBank("Y");
		} else {
			l161s01a.setCoBank("N");
		}

		if (countUnitMega > 0) {
			l161s01a.setCoBranch("Y");
		} else {
			l161s01a.setCoBranch("N");
		}

		if (Util.equals(l161s01a.getIsDerivatives(), "")) {

			L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l161s01a
					.getCntrMainId());

			if (l140m01a == null) {
				// 找不到額度明細表
				throw new CapMessageException(
						prop.getProperty("L160M01A.message66")
								+ l161s01a.getCntrNo(), getClass());
			}

			Boolean hasDerivateSubjectFlag = false;

			ArrayList<String> itemsAll = new ArrayList<String>();
			List<L140M01C> l140m01cs = lms1405Service
					.findL140m01cListByMainId(l140m01a.getMainId());

			if (l140m01cs != null && !l140m01cs.isEmpty()) {
				for (L140M01C l140m01c : l140m01cs) {
					itemsAll.add(l140m01c.getLoanTP());
				}

				hasDerivateSubjectFlag = lmsService.hasDerivateSubject(itemsAll
						.toArray(new String[itemsAll.size()]));

				if (hasDerivateSubjectFlag == true) {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.是);
				} else {
					l161s01a.setIsDerivatives(UtilConstants.DEFAULT.否);
				}
			} else {
				// 找不到額度明細表
				throw new CapMessageException(
						prop.getProperty("L160M01A.message66")
								+ l161s01a.getCntrNo(), getClass());
			}

		}

		// TODO
		String suggestMsg = this.getSuggestData(prop, l161s01a);
		String errorMsg = this.isCheckData(prop, l161s01a, l160m01a, result);

		if (Util.equals(Util.trim(errorMsg), "")) {
			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.已計算);
		} else {
			l161s01a.setChkYN(UtilConstants.Cntrdoc.CHKYN.尚未通過檢核);
		}

		result.set("suggestMsg", suggestMsg);
		result.set("errorMsg", errorMsg);
		lms1605Service.save(l161s01a);
		return result;

	}

	/**
	 * 設定 訊息換列 目前設定五筆 就換列
	 * 
	 * @param temp
	 *            暫存文字
	 * @param countItme
	 *            計算欄位數量
	 * @param showMessage
	 * @return countItme 目前的 計算數量
	 */
	public int setHtmlBr(StringBuffer temp, int countItme, String showMessage) {
		int maxLenth = 5;
		temp.append(temp.length() > 0 ? "、" : "");
		if (countItme > maxLenth) {
			temp.append("<br/>");
			countItme = 1;
		} else {
			countItme++;
		}
		temp.append(showMessage);
		return countItme;
	}

	public String getSuggestData(Properties prop, L161S01A l161s01a)
			throws CapException {
		StringBuffer temp = new StringBuffer("");
		// 取得建議資訊

		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l161s01a
				.getCntrMainId());

		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo(), getClass());
		}

		// String tmpUnitCase = null;
		// String tmpMainBranch = null;
		String caseType = null;

		// String cntrNo = l161s01a.getCntrNo();

		// 檢核案件性質、額度控管種類
		if (!UtilConstants.Cntrdoc.Property.不變.equals(l161s01a.getProperty())) {

			Map<String, String> caseMap = lmsService.getCaseType("2", l161s01a,
					l140m01a);

			// tmpUnitCase = caseMap.get("tmpUnitCase");
			// tmpMainBranch = caseMap.get("tmpMainBranch");
			caseType = caseMap.get("caseType");

			// 多判斷動審表聯行攤貸比率有無017兆豐是主辦行*************
			String hasMainFlag = "N";
			String slBankType = "";
			String slBank = "";
			String slMaster = "";
			// 放017兆豐銀行的l161s01b
			Map<String, L161S01B> checkList = new HashMap<String, L161S01B>();
			
			Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

			if (l161s01bs != null && !l161s01bs.isEmpty()) {
				for (L161S01B l161s01b : l161s01bs) {

					slBankType = l161s01b.getSlBankType();
					slBank = l161s01b.getSlBank();
					slMaster = l161s01b.getSlMaster();
					if (Util.equals(slBankType, "01")
							&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
						if (Util.equals(slMaster, "Y")) {
							hasMainFlag = "Y";
						}
						checkList.put(l161s01b.getSlBranch(), l161s01b);
					}

				}
			}
			//G-113-0036聯貸比率資訊(L161S01B) <> 額度明細表聯行攤貸比例(L140M01E_AF)
			//1. 017兆豐銀行  家數、金額不一致時跳提醒
			//2. 攤貸行現請額度金額>分攤金額時，跳提醒
			Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
			BigDecimal branchShareAmt = l140m01a.getCurrentApplyAmt();
			if(l140m01e_afs != null && !l140m01e_afs.isEmpty()){
				if(l140m01e_afs.size() != checkList.size()){
					if(Util.equals(l161s01a.getUnitMega(), "Y")){
						temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
						temp.append("<br/>");
					}
				}else{
					for (L140M01E_AF l140m01e_af : l140m01e_afs) {
						//攤貸行現請額度金額>分攤金額時，跳提醒
						if(Util.equals(l161s01a.getCntrNo().substring(0, 3), l140m01e_af.getShareNo().substring(0, 3))){
							if (Util.notEquals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {//非額度管理行
								branchShareAmt = l140m01e_af.getShareAmt();
							}
						}
						if(Util.equals(l161s01a.getUnitMega(), "Y")){
							if(checkList.get(l140m01e_af.getShareBrId()) == null){
								//分行不一樣 跳提醒
								temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
								temp.append("<br/>");
								break;
							}else{
								//同分行檢核參貸金額金額，不一致跳提醒
								if(l140m01e_af.getShareAmt().compareTo(checkList.get(l140m01e_af.getShareBrId()).getSlAmt()) != 0){
									temp.append(new StringBuffer(prop.getProperty("L140M01e.message08")));
									temp.append("<br/>");
									break;
								}
							}
						}
					}
				}
			}
			//非額度管理行(攤貸行動審)如動審現請額度 > 攤貸金額，跳提醒訊息 
			if (l161s01a.getCurrentApplyAmt().compareTo(branchShareAmt) > 0) {
				temp.append(new StringBuffer(prop.getProperty("L140M01e.message09")));
				temp.append("<br/>");
			}

			if (Util.equals(hasMainFlag, "Y")) {
				if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
					// 案件性質建議為
					caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
				} else if (Util.equals(caseType,
						UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
					caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
				}
			}

			// *************

			// 1
			if (Util.notEquals(caseType, l161s01a.getCaseType())) {
				CodeType caseTypeCT = codeTypeService
						.findByCodeTypeAndCodeValue("lms1605m01_caseType",
								Util.trim(caseType));

				// 案件性質建議為
				temp.append(new StringBuffer(prop
						.getProperty("L160M01A.message67")).append(caseType)
						.append(".").append(caseTypeCT.getCodeDesc()));
				temp.append("<br/>");

			}

			// 2 lms1405m01_snoKind
			StringBuffer snoKindStr = new StringBuffer("");
			if (Util.notEquals(Util.trim(l161s01a.getSnoKind()), "")) {
				CodeType snoKind = codeTypeService.findByCodeTypeAndCodeValue(
						"lms1405m01_snoKind", Util.trim(l161s01a.getSnoKind()));
				snoKindStr.append(Util.trim(l161s01a.getSnoKind())).append(" ")
						.append(snoKind.getCodeDesc());
			} else {
				snoKindStr.append(Util.trim(l140m01a.getSnoKind()));
			}

			StringBuffer suggestSnoKind = null;
			suggestSnoKind = new StringBuffer("");

			Map<String, String> caseMapSuggest = lmsService.getCaseType("1",
					l161s01a, l140m01a);

			String caseTypeSuggest = caseMapSuggest.get("caseType");

			Map<String, String> snoKindCodeTypeMap = codeTypeService
					.findByCodeType("lms1405m01_snoKind");

			switch (Util.parseInt(caseTypeSuggest)) {
			case 1:
			case 2:
			case 4:
			case 5:
				if (!UtilConstants.Cntrdoc.snoKind.聯貸.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『30.聯貸』|『62.聯貸』
					suggestSnoKind.append("30.")
							.append(snoKindCodeTypeMap.get("30")).append("、");
					suggestSnoKind.append("62.").append(
							snoKindCodeTypeMap.get("62"));
				}
				break;
			case 3:
				if (!UtilConstants.Cntrdoc.snoKind.一般.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『10.一般』
					suggestSnoKind.append("10.").append(
							snoKindCodeTypeMap.get("10"));
				}
				break;
			case 7:
				if (!UtilConstants.Cntrdoc.snoKind.合作母.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『40.合作母』|
					suggestSnoKind.append("40.").append(
							snoKindCodeTypeMap.get("40"));
				}
				break;

			case 8:
				if (!UtilConstants.Cntrdoc.snoKind.合作子.equals(l161s01a
						.getSnoKind())) {
					// 建議為 『41.合作子』|
					suggestSnoKind.append("40.").append(
							snoKindCodeTypeMap.get("41"));
				}
				break;
			case 9:
				if (!UtilConstants.Cntrdoc.snoKind.應收帳款買方.equals(l161s01a
						.getSnoKind())
						&& !UtilConstants.Cntrdoc.snoKind.應收帳款賣方一般戶
								.equals(l161s01a.getSnoKind())) {
					// 『60.應收帳款及供應鏈融資買方』『61.應收帳款及供應鏈融資賣方一般戶』|
					suggestSnoKind.append("60.")
							.append(snoKindCodeTypeMap.get("60")).append("、");
					suggestSnoKind.append("61.").append(
							snoKindCodeTypeMap.get("61"));
				}
				break;
			case 10:
				if (!UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶.equals(l161s01a
						.getSnoKind())) {
					// 『62.應收帳款及供應鏈融資賣方聯貸母戶』|
					suggestSnoKind.append("62.").append(
							snoKindCodeTypeMap.get("62"));
				}
				break;
			default:

				break;

			}

			if (Util.notEquals(Util.trim(suggestSnoKind.toString()), "")) {

				// 額度控管種類 建議
				temp.append(new StringBuffer(prop
						.getProperty("L140M01a.snoKind"))
						.append(prop.getProperty("other.suggest")).append("【")
						.append(suggestSnoKind.toString()).append("】")
						.toString());
				temp.append("<br/>");

			}

		}

		if (temp.length() > 0) {
			// 尚有必填欄位未填
			temp.insert(0, prop.getProperty("L160M01A.message77") + "：<br/>");
		}

		return temp.toString();

	}

	private String isCheckData(Properties prop, L161S01A l161s01a,
			L160M01A l160m01a, CapAjaxFormResult result) throws CapException {

		StringBuffer temp = new StringBuffer("");
		// 統計未填欄位數
		int countItme = 1;

		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l161s01a
				.getCntrMainId());

		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo(), getClass());
		}

		// 檢核欄位有沒有填入
		if (Util.equals(l161s01a.getUnitCase(), "Y")
				&& Util.equals(l161s01a.getUCntBranch(), "Y")) {
			// 幣別
			if (Util.isEmpty(Util.trim(l161s01a.getQuotaCurr()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.curr"));
			}
			// 簽約日期
			if (Util.isEmpty(Util.trim(l161s01a.getSignDate()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.signDate"));
			}
			// 總額度
			if (Util.isEmpty(Util.trim(l161s01a.getQuotaAmt()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.allMoney"));
			}

		} else {
			// 加強檢核案件性質為 1 2 時總額度一定要KEY
			if (Util.equals(l161s01a.getCaseType(),
					UtilConstants.Usedoc.caseType.同業聯貸主辦)
					|| Util.equals(l161s01a.getCaseType(),
							UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
				// 幣別
				if (Util.isEmpty(Util.trim(l161s01a.getQuotaCurr()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.curr"));
				}
				// 簽約日期
				if (Util.isEmpty(Util.trim(l161s01a.getSignDate()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.signDate"));
				}
				// 總額度
				if (Util.isEmpty(Util.trim(l161s01a.getQuotaAmt()))) {
					countItme = this.setHtmlBr(temp, countItme,
							prop.getProperty("L160M01A.allMoney"));
				}
			}
		}

		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			if (Util.isEmpty(Util.trim(l161s01a.getDervApplyAmtType()))) {
				countItme = this.setHtmlBr(temp, countItme,
						prop.getProperty("L160M01A.dervApplyAmtType"));
			}
		}

		if (temp.length() > 0) {
			// 尚有必填欄位未填
			temp.insert(0, prop.getProperty("L160M01A.message75") + "<br/>");
		}

		// ----------以下檢核邏輯-------------

		// 檢核現請額度不得超過額度明細表
		if (l161s01a.getCurrentApplyAmt().compareTo(
				l140m01a.getCurrentApplyAmt()) > 0) {
			temp.append("<br/>");
			// 動審表現請額度金額{0}不得大於額度明細表{1}
			temp.append(MessageFormat.format(
					prop.getProperty("L160M01A.message68"),
					new StringBuffer().append(l161s01a.getCurrentApplyAmt()),
					new StringBuffer().append(l140m01a.getCurrentApplyAmt()))
					.toString());
		}

		// 有同業且為帳務管理行，1.檢核一定要同業參貸比率 2.檢核總額度與明細是否一致

		// boolean hasUnitMega = false;
		// boolean hasUnitBank = false;

		int countUnitMega = 0;
		int countUnitBank = 0;

		String hasKeyL161S01B = "N";
		BigDecimal countAll = BigDecimal.ZERO;
		BigDecimal countMega = BigDecimal.ZERO;
		String hasMainFlag = "N";
		String slBankType = "";
		String slBank = "";
		String slMaster = "";

		Set<L161S01B> l161s01bs = l161s01a.getL161s01b();

		if (l161s01bs != null && !l161s01bs.isEmpty()) {
			for (L161S01B l161s01b : l161s01bs) {

				BigDecimal tSlAmt = l161s01b.getSlAmt() == null ? BigDecimal.ZERO
						: l161s01b.getSlAmt();

				if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
					// hasUnitMega = true;
					countUnitMega = countUnitMega + 1;
				} else {
					// hasUnitBank = true;
					countUnitBank = countUnitBank + 1;
				}

				if (hasKeyL161S01B.equals("N")) {
					hasKeyL161S01B = "Y";
				}

				countAll = countAll.add(tSlAmt);

				if (Util.equals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
					countMega = countMega.add(tSlAmt);
				}

				slBankType = l161s01b.getSlBankType();
				slBank = l161s01b.getSlBank();
				slMaster = l161s01b.getSlMaster();
				if (Util.equals(slBankType, "01")
						&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
					if (Util.equals(slMaster, "Y")) {
						hasMainFlag = "Y";
					}
				}
			}
		}

		if (Util.equals(hasMainFlag, "Y")) {
			if (Util.equals(l161s01a.getCaseType(),
					UtilConstants.Usedoc.caseType.同業聯貸參貸)
					|| Util.equals(l161s01a.getCaseType(),
							UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
				temp.append("<br/>");
				// L160M01A.message91=聯貸案參貸比率一覽表兆豐分行有勾選為共同主辦，基本資訊頁籤-「案件性質」欄位則必須為主辦
				temp.append(prop.getProperty("L160M01A.message91"));

			}
		}

		if (Util.equals(l161s01a.getUnitCase(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			// 檢查輸入的總額度與聯行攤貸總額金額是否一樣
			BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
					: l161s01a.getQuotaAmt();
			if (tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
				// String caseType = l161s01a.getCaseType();
				// if (!(Util.equals(caseType,
				// UtilConstants.Usedoc.caseType.同業聯貸主辦) || Util.equals(
				// caseType, UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸))) {
				if (Util.notEquals(l161s01a.getQuotaCurr(),
						l161s01a.getCurrentApplyCurr())) {
					temp.append("<br/>");
					// 同業聯貸總額度幣別必須與額度明細表現請額度幣別一致
					temp.append(prop.getProperty("L160M01A.message85"));
				}

				// }
			}
		}

		if ((Util.equals(l161s01a.getUnitCase(), "Y") && Util.equals(
				l161s01a.getUCntBranch(), "Y"))
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			if (countUnitBank == 0) {
				// 【本案是否有同業聯貸案額度】及【本行是否為額度管理行】皆為是，則聯貸案參貸比率一覽表中必須要有同業參貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message69"));
			}
		}

		if (Util.equals(l161s01a.getUnitMega(), "Y")) {
			if (countUnitMega <= 1) {
				// 【本案是否有聯行攤貸額度】為是，則聯貸案參貸比率一覽表中必須要有兩筆(含)以上分行的攤貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message70"));
			}

		}

		if (Util.equals(l161s01a.getUnitCase(), "N")
				&& Util.equals(l161s01a.getUnitMega(), "Y")) {
			if (countUnitBank > 0) {
				// 「本案是否有同業聯貸案額度」為否，則聯貸案參貸比率一覽表中不得有同業參貸資料
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message86"));
			}
		}

		if (Util.equals(l161s01a.getUnitCase(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			// 檢查輸入的總額度與聯行攤貸總額金額是否一樣
			BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
					: l161s01a.getQuotaAmt();
			if (countUnitBank > 0 || tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
				if (countAll.compareTo(tQuotaAmt) != 0) {
					// L160M01A.message30=「聯貸案參貸比率一覽表」，所有參貸金額加總後({0})與總額度({1})不相等。
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L160M01A.message71"),
							new StringBuffer().append(countAll).toString(),
							new StringBuffer().append(tQuotaAmt).toString()));
				}
			}

		}

		// 有自行聯貸時，檢核017分行的比率金額要等於動審表之現請額度
		if (Util.equals(l161s01a.getUnitMega(), "Y")
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)
				|| Util.equals(l161s01a.getCaseType(),
						UtilConstants.Usedoc.caseType.自行聯貸)) {
			boolean chgRate = false; // 當現請額度幣別與同業參貸總額度幣別不一致時，以同業參貸總額度幣別為準，此時

			if (Util.equals(l161s01a.getUnitCase(), "Y")) {
				BigDecimal tQuotaAmt = l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
						: l161s01a.getQuotaAmt();

				if (tQuotaAmt.compareTo(BigDecimal.ZERO) != 0) {
					if (Util.notEquals(l161s01a.getQuotaCurr(),
							l161s01a.getCurrentApplyCurr())) {
						chgRate = true;
					}
				}
			}

			BigDecimal tCurrentApplyAmt = l161s01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO
					: l161s01a.getCurrentApplyAmt();

			if (chgRate == true) {
				BranchRate branchRate = lmsService.getBranchRate(l160m01a
						.getCaseBrId());
				BigDecimal chgRateCurrentApplyAmt = branchRate.toOtherAmt(
						l161s01a.getCurrentApplyCurr(),
						l161s01a.getQuotaCurr(), tCurrentApplyAmt).setScale(0,
						BigDecimal.ROUND_UP);
				if (countMega.compareTo(chgRateCurrentApplyAmt) > 0) {
					// L160M01A.message84=「聯貸案參貸比率一覽表」，本行聯行攤貸金額加總後({0})({1})不得大於動審表現請額度換算後({2})({3})
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L160M01A.message84"),
							l161s01a.getQuotaCurr(),
							new StringBuffer().append(countMega),
							l161s01a.getQuotaCurr(),
							new StringBuffer().append(chgRateCurrentApplyAmt))
							.toString());
				}
			} else {
				if (Util.equals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {//額度管理行
					if (countMega.compareTo(tCurrentApplyAmt) != 0) {
						// 「聯貸案參貸比率一覽表」，本行聯貸金額加總後({0})必須等於動審表之現請額度({1})。
						temp.append("<br/>");
						temp.append(MessageFormat.format(
								prop.getProperty("L160M01A.message72"),
								new StringBuffer().append(countMega),
								new StringBuffer().append(tCurrentApplyAmt))).toString();//原本放的是額度明細表現請額度, 改成動審表
								//l140m01a.getCurrentApplyAmt())).toString() 
					}
				}else{//非額度管理行 <=
					if (tCurrentApplyAmt.compareTo(countMega) > 0) {
						// 「聯貸案參貸比率一覽表」，動審表之現請額度({0})必須小於等於本行聯貸金額加總後({1})。
						temp.append("<br/>");
						temp.append(MessageFormat.format(
								prop.getProperty("L160M01A.message120"),
								new StringBuffer().append(tCurrentApplyAmt),
								new StringBuffer().append(countMega))).toString();//原本放的是額度明細表現請額度, 改成動審表
					}
				}
			}
		}

		String snoKind = Util.trim(l161s01a.getSnoKind());
		// 額度明細表為供應鏈融資，則動審表額度控管種類必須為6開頭
		if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")
				&& (Util.equals(
						Util.trim(l140m01a.getSnoKind()).subSequence(0, 1), "4") || Util
						.equals(Util.trim(l140m01a.getSnoKind()).subSequence(0,
								1), "6"))) {

			if (!Util.equals(Util.trim(snoKind).substring(0, 1), "6")) {
				// L140M01a.message165=額度明細表->申請內容->額度控管種類第一碼必須為6!!
				// Properties cntrProp = MessageBundleScriptCreator
				// .getComponentResource(LMS1401S02Page.class);
				// L160M01A.message101=額度明細表->申請內容->額度控管種類->欄位「是否為供應鏈融資」為是，動審表->基本資訊->額度控管種類->第一碼必須為6(EX:60、61、62)
				temp.append("<br/>");
				temp.append(prop.getProperty("L160M01A.message101"));
			}

		}

		// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			if (Util.equals(l161s01a.getSnoKind(),
					UtilConstants.Cntrdoc.snoKind.聯貸)
					|| Util.equals(l161s01a.getSnoKind(),
							UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶)) {

				ArrayList<String> itemsAll = new ArrayList<String>();
				List<L140M01C> l140m01cs = lms1405Service
						.findL140m01cListByMainId(l140m01a.getMainId());

				if (l140m01cs != null && !l140m01cs.isEmpty()) {
					for (L140M01C l140m01c : l140m01cs) {
						itemsAll.add(l140m01c.getLoanTP());
					}

					if (lmsService.hasFxSubject(itemsAll
							.toArray(new String[itemsAll.size()]))) {
						temp.append("<br/>");
						// L160M01A.message90=遠匯、換匯科目額度控管種類不得為聯貸
						temp.append(prop.getProperty("L160M01A.message90")
								+ "－" + l161s01a.getCntrNo());
					}

				} else {
					temp.append("<br/>");
					// 找不到額度明細表
					temp.append(prop.getProperty("L160M01A.message66")
							+ l161s01a.getCntrNo());

				}

			}
		}
		//G-113-0036 額度明細表聯貸攤貸比例金額檢核
		// 暫存合計總額
		BigDecimal count = BigDecimal.ZERO;
		// 分子加總
		BigDecimal shareRateCount = BigDecimal.ZERO;
		// 暫存分母
		BigDecimal shareRate = BigDecimal.ZERO;
		// 檢查現請額度 總金額是否與攤貸金額相等
		BigDecimal nowCurrAMT = l140m01a.getCurrentApplyAmt();
		// 是否有餘額剩下
		BigDecimal tempAmt = BigDecimal.ZERO;
		Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
		if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				count = count.add(Util.parseBigDecimal(l140m01e_af.getShareAmt()));
				// 以比例計算的時候在要判斷
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
					shareRateCount = shareRateCount.add(l140m01e_af.getShareRate1());
					shareRate = l140m01e_af.getShareRate2();
				}
			}
			if (l140m01e_afs.size() > 0) {
				if (shareRateCount.compareTo(shareRate) != 0) {
					temp.append(temp.length() > 0 ? "、" : "");
					// L140M01e.message04=限額條件下聯行攤貸分子加總不等於分母
					temp.append("<br/>");
					temp.append(prop.getProperty("L140M01e.message04"));
				}

			}
			// 檢核分子是否攤貸完
			if (shareRateCount.compareTo(shareRate) == 0) {
				// 當加總額小於現請額度
				if (count.compareTo(nowCurrAMT) == -1) {
					tempAmt = nowCurrAMT.subtract(count);
					temp.append("<br/>");
					temp.append(MessageFormat.format(
							prop.getProperty("L140M01e.message06"),
							tempAmt));
				}
				// 最後剩餘金額
				result.set("l140m01eAmt", NumConverter.addComma(tempAmt));
				
			}
		}
		
		
		if (temp.length() > 0) {
			// 以下訊息為必須更正之錯誤
			temp.insert(0, prop.getProperty("L160M01A.message78") + "：<br/>");
		}

		return temp.toString();

	}

	/**
	 * 複製L161M01B 聯貸案參貸比率明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult copyL161s01b(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String copyFromUid = Util.trim(params.getString("copyFromUid"));
		String copyToUid = Util.trim(params.getString("copyToUid"));

		if (Util.equals(copyFromUid, copyToUid)) {
			// 不得複製同筆額度動用資訊參貸比率
			throw new CapMessageException(
					prop.getProperty("L160M01A.message82"), getClass());
		}

		L161S01A l161s01aFrom = lms1605Service.findL161m01aByMainIdUid(mainId,
				copyFromUid);

		Set<L161S01B> l161s01bsFrom = null;

		if (l161s01aFrom != null) {
			l161s01bsFrom = l161s01aFrom.getL161s01b();
		}

		if (l161s01bsFrom == null) {
			// 選取之額度動用資訊項下無同業參貸比率可複製
			throw new CapMessageException(
					prop.getProperty("L160M01A.message81"), getClass());
		}

		L161S01A l161s01aTo = lms1605Service.findL161m01aByMainIdUid(mainId,
				copyToUid);

		boolean hasUnitBank = false;
		for (L161S01B l161s01b : l161s01bsFrom) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				hasUnitBank = true;
				break;
			}
		}

		if (hasUnitBank == false) {
			// 選取之額度動用資訊項下無同業參貸比率可複製
			throw new CapMessageException(
					prop.getProperty("L160M01A.message81"), getClass());
		}

		// 先刪除
		Set<L161S01B> l161s01bsTo = l161s01aTo.getL161s01b();
		for (L161S01B l161s01b : l161s01bsTo) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				lms1605Service.delete(l161s01b);
			}
		}

		// 再複製
		for (L161S01B l161s01b : l161s01bsFrom) {
			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				L161S01B newL161s01b = new L161S01B();

				newL161s01b.setCreator(user.getUserId());
				newL161s01b.setCreateTime(CapDate.getCurrentTimestamp());
				newL161s01b.setMainId(l161s01aTo.getMainId());
				newL161s01b.setPid(l161s01aTo.getUid());
				newL161s01b.setSeq(lms1605Service.findL161m01bMaxSeq(
						l161s01aTo.getMainId(), l161s01aTo.getUid()));

				newL161s01b.setSlBankType(l161s01b.getSlBankType());
				newL161s01b.setSlBank(l161s01b.getSlBank());
				newL161s01b.setSlBankCN(l161s01b.getSlBankCN());
				newL161s01b.setSlBranch(l161s01b.getSlBranch());
				newL161s01b.setSlBranchCN(l161s01b.getSlBranchCN());

				newL161s01b.setSlMaster(l161s01b.getSlMaster());
				newL161s01b.setSlAccNo(l161s01b.getSlAccNo());

				newL161s01b.setSlCurr(l161s01b.getSlCurr());
				newL161s01b.setSlAmt(l161s01b.getSlAmt());

				lms1605Service.save(newL161s01b);
			}
		}

		return result;

	}

	/**
	 * 複製L161M01B 聯貸案參貸比率明細檔
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyGist(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String uid = Util.trim(params.getString("uid"));

		L161S01A l161s01a = lms1605Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			// 無法取得額度動用資訊L161S01A
			throw new CapMessageException(
					prop.getProperty("L160M01A.message79"), getClass());
		}

		String mainId140 = l161s01a.getCntrMainId();

		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId140);
		if (l140m01a == null) {
			// 找不到額度明細表
			throw new CapMessageException(
					prop.getProperty("L160M01A.message66"), getClass());
		}

		L120M01C l120m01c = l140m01a.getL120m01c();

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l120m01c
				.getMainId());
		if (l120m01a == null) {
			// 無法取得額度動用資訊L161S01A對應的簽報書
			throw new CapMessageException(
					prop.getProperty("L160M01A.message83"), getClass());
		}

		result.set("gist", l120m01a.getGist());
		return result;

	}

	/**
	 * 先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult chkAllPrintHasL161S01B(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		String rptOid = Util.nullToSpace(params.getString("uids"));
		String[] dataSplit = rptOid.split(",");

		boolean printOk = false;

		for (String uid : dataSplit) {
			// L160M01B．動審表額度序號資料
			List<L161S01B> l161s01bList = null;

			L161S01A l161s01a = lms1605Service.findL161m01aByMainIdUid(mainId,
					uid);
			l161s01bList = l161s01a.getL161s01bs();

			if (l161s01bList != null && !l161s01bList.isEmpty()) {
				if (Util.equals(l161s01a.getUnitMega(), "Y")
						|| Util.equals(l161s01a.getUnitCase(), "Y")) {
					printOk = true;
					break;
				}

			}
		}

		if (printOk == false) {
			throw new CapMessageException(
					prop.getProperty("L160M01A.message88"), getClass());
		}
		return result;

	}

	/**
	 * 取得所有需查詢黑名單的客戶ID
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryCust(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONObject json = new JSONObject();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		List<L160M01B> l160m01bs = (List<L160M01B>) lms1605Service
				.findListByMainId(L160M01B.class, mainId);

		ArrayList<String> mainIds = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01bs) {
			mainIds.add(l160m01b.getReMainId());
		}

		List<L140M01A> l140m01as = lms1405Service
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));
		for (L140M01A l140m01a : l140m01as) {
			String custId = Util.trim(l140m01a.getCustId());
			String dupNo = Util.trim(l140m01a.getDupNo());
			String custName = Util.trim(l140m01a.getCustName());
			String key = custId + dupNo;
			if (!json.containsKey(key) && Util.isNotEmpty(key)) {
				JSONObject value = new JSONObject();
				value.put("custId", custId);
				value.put("dupNo", dupNo);
				value.put("custName", custName);
				json.put(key, value);
			}
		}

		result.set("cust", new CapAjaxFormResult(json));
		return result;

	}

	/**
	 * 重新引進動審表稽核項目 J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult reloadL160M01C(PageParameters params)
			throws CapException {
		String mainId = params.getString(EloanConstants.MAIN_ID);
		CapAjaxFormResult result = new CapAjaxFormResult();
		List<L160M01C> l160m01c_Olds = (List<L160M01C>) lms1605Service
				.findListByMainId(L160M01C.class, mainId);
		lms1605Service.deleteL160m01cs(l160m01c_Olds);
		List<L160M01C> l160m01cs = this.copyL901m01aToL160m01c(mainId, LocaleContextHolder.getLocale().toString());

		lms1605Service.saveL160m01cList(l160m01cs);
		this.getL160M01C(result, l160m01cs);

		return result;
	}

	/**
	 * 產生動審表稽核項目 J-105-0079-001 Web e-Loan授信管理系統修改柬埔寨地區分行動審表。
	 * 
	 * @param result
	 * @param allItem
	 */
	@SuppressWarnings("unchecked")
	private void getL160M01C(CapAjaxFormResult result, List<L160M01C> allItem) {
		JSONObject jsonAllItem = null;
		JSONObject jsonVal = null;
		// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
		JSONObject jsonFieldVal = null;
		JSONArray jsonArrayAllItem = new JSONArray();
		JSONArray jsonArrayLocalItem = new JSONArray();
		JSONArray jsonArrayselfItem = new JSONArray();
		JSONArray jsonArrayAllVal = new JSONArray();
		// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
		JSONArray jsonArrayAllItemFieldVal = new JSONArray();
		for (L160M01C item : allItem) {
			jsonAllItem = new JSONObject();
			jsonVal = new JSONObject();
			jsonVal.put("id", item.getOid());
			jsonVal.put("val", item.getItemCheck());
			jsonAllItem.put("id", item.getOid());
			jsonVal.put("id", item.getOid());
			jsonVal.put("val", item.getItemCheck());

			// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
			jsonFieldVal = new JSONObject();
			jsonFieldVal.put("id", item.getOid());
			jsonFieldVal.put("val", item.getItemField1());

			if (Util.isEmpty(Util.trim(item.getItemContent()))
					|| UtilConstants.Usedoc.itemType.自訂項目.equals(item
							.getItemType())) {
				jsonAllItem.put("drc", item.getItemContent());
			} else {
				jsonAllItem.put(
						"drc",
						item.getItemSeq()
								+ "."
								+ Util.trim(item.getItemContent()).replace(
										"\r", "<br/>"));
			}
			if (UtilConstants.Usedoc.itemType.全行共同項目.equals(item.getItemType())) {

				// 全行項目
				jsonArrayAllItem.add(jsonAllItem);
				jsonArrayAllVal.add(jsonVal);
				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				jsonArrayAllItemFieldVal.add(jsonFieldVal);
			} else if (UtilConstants.Usedoc.itemType.當地特殊規定項目.equals(item
					.getItemType())) {

				// 本地分行項目
				jsonArrayAllVal.add(jsonVal);
				jsonArrayLocalItem.add(jsonAllItem);
				// J-111-0028_05097_B1001 Web
				// e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
				jsonArrayAllItemFieldVal.add(jsonFieldVal);
			} else if (UtilConstants.Usedoc.itemType.自訂項目.equals(item
					.getItemType())) {
				jsonAllItem.put("id", item.getItemSeq());
				jsonVal.put("id", String.valueOf(item.getItemSeq()));

				// 自訂項目
				jsonArrayAllVal.add(jsonVal);
				jsonArrayselfItem.add(jsonAllItem);

			}

		}
		result.set("allItem", jsonArrayAllItem);
		result.set("localItem", jsonArrayLocalItem);
		result.set("elfItem", jsonArrayselfItem);
		result.set("value", jsonArrayAllVal);
		// J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
		result.set("fieldValue", jsonArrayAllItemFieldVal);

	}

	/**
	 * 將引進的案件簽報書資料複製連保人資料到主從債務人
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param mainId
	 *            要加進去的mainId
	 * @param custContry
	 *            簽報書所有借款人的國別 <custId+dupNo, 國別>
	 * @return 複製完成的主從債務人資料表檔
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private L164S01A copyL120s01bToL164s01a(L120S01B l120s01b, String mainId)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		L164S01A l164s01a = null;
		L120S01A l120s01a = l120s01b.getL120s01a();

		l164s01a = lms1605Service.findL164s01aByMainIdCustId(mainId,
				l120s01b.getCustId(), l120s01b.getDupNo());
		if (l164s01a == null) {
			l164s01a = new L164S01A();
			l164s01a.setCreator(user.getUserId());
			l164s01a.setCreateTime(CapDate.getCurrentTimestamp());
			l164s01a.setMainId(mainId);
		}

		l164s01a.setCustId(Util.trim(l120s01b.getCustId()));
		l164s01a.setDupNo(Util.trim(l120s01b.getDupNo()));
		l164s01a.setCustPos(Util.trim(l120s01a.getCustPos()));
		l164s01a.setChairmanId(Util.trim(l120s01b.getChairmanId()));
		l164s01a.setChairmanDupNo(Util.trim(l120s01b.getChairmanDupNo()));
		l164s01a.setChairman(Util.toSemiCharString(Util.trim(l120s01b
				.getChairman())));
		l164s01a.setBeneficiary(Util.trim(l120s01b.getBeneficiary()));
		return l164s01a;
	}

	@SuppressWarnings("unchecked")
	private List<L120S01P> copyL120s01pToNewL120s01p(L120S01B l120s01b,
			String mainId) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		List<L120S01P> newL120s01ps = new ArrayList<L120S01P>();

		List<L120S01P> listL120s01p = amlRelateService
				.findL120s01pByMainIdAndCustIdWithRType(l120s01b.getMainId(),
						l120s01b.getCustId(), l120s01b.getDupNo(),
						UtilConstants.Casedoc.L120s09aBlackListCtlTarget.實質受益人);
		if (listL120s01p != null && !listL120s01p.isEmpty()) {
			for (L120S01P oldL120s01p : listL120s01p) {

				L120S01P newL120s01p = new L120S01P();
				CapBeanUtil.copyBean(oldL120s01p, newL120s01p,
						CapBeanUtil.getFieldName(L120S01P.class, true));// 複製的語法
				newL120s01p.setOid(null);
				newL120s01p.setMainId(mainId);
				newL120s01p.setCreator(user.getUserId());
				newL120s01p.setCreateTime(CapDate.getCurrentTimestamp());
				newL120s01ps.add(newL120s01p);
			}
		}

		return newL120s01ps;
	}

	/**
	 * J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getWarnMsg(PageParameters params)
			throws CapException {
		Map<String, String> param = new HashMap<String, String>();
		StringBuffer warnMsg = new StringBuffer("");

		// 儲存and檢核
		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1605Service.findModelByOid(L160M01A.class, oid);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		if (Util.isEmpty(l160m01a)) {

			// L160M01A.error10=請先儲存
			param.put("msg", pop.getProperty("L160M01A.error10"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		// #J-108-0145_05097_B1001 Web e-Loan 國內外企金授信私募基金案件調整實質受益人控管

		L120M01A l120m01a = lms1205Service.findL120m01aByMainId(l160m01a
				.getSrcMainId());
		if (l120m01a != null) {
			String canPassAMLWarnMsg = amlRelateService
					.getPassAmlRelativeAndRiskLvlChkWarnMsg("2",
							l120m01a.getMainId());
			// L120S09a.message27=本案為實質受益人等延後辦理辨識案件，請衡酌加註相關控管條件(如：「首次動撥前務必確實完成實質受益人等辨識相關作業」)
			if (Util.notEquals(canPassAMLWarnMsg, "")) {
				warnMsg.append("").append("●").append(canPassAMLWarnMsg);
			}
		}
		
		// J-113-0082 配合法務部新規，於AML頁籤新增引入「受告誡處分」資訊
		// 提示有受告誡處分
		String checkCmfwarnpResult = 
			amlRelateService.checkCmfwarnpResult(l160m01a.getMainId());
		if(Util.isNotEmpty(checkCmfwarnpResult)){
			warnMsg.append("<br/><br/>")
			.append("●")
			.append(checkCmfwarnpResult.toString());
		}

		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("warnMsg", warnMsg.toString());

		return result;
	}

	/**
	 * 引進額度明細表動撥提醒事項 J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryInitData(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		String guarantorPriorityOn = "";

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		guarantorPriorityOn = Util.trim(lmsService
				.getSysParamDataValue("LMS_GUARANTOR_PRIORITY_ON"));

		result.set("guarantorPriorityOn", guarantorPriorityOn);
		return result;

	}

	/**
	 * 檢查主從債務人都有在0024建檔，否則沒辦法判斷是不是法人
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult chkAllGuarantorHas0024(PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String mainCntrNo = Util.trim(params.getString("cntrNo"));
		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);

		Map<String, String> param = new HashMap<String, String>();

		// 檢核是否登錄主從債務表
		List<L162S01A> l162m01as = lms1605Service.findL162s01aByMainIdCntrno(
				mainId, mainCntrNo);

		// 檢查主從人債務表是否必填欄位都有填寫
		HashMap<String, String> tempMap = new HashMap<String, String>();
		// 檢查債務人是否存在
		HashMap<String, String> getAllCustIdMap = new HashMap<String, String>();
		for (L162S01A l162s01a : l162m01as) {
			String cntrNo = Util.trim(l162s01a.getCntrNo());
			String showKey = StrUtils.concat(
					l162s01a.getCustId().toUpperCase(), " ", l162s01a
							.getDupNo().toUpperCase(), " ", l162s01a.getRId()
							.toUpperCase(), " ", l162s01a.getRDupNo()
							.toUpperCase());
			if (Util.isEmpty(cntrNo)) {
				// L160M01A.message63=「主從債務人資料表」額度序號不得為空!!
				param.put("msg", pop.getProperty("L160M01A.message63"));
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
			}
			getAllCustIdMap.put(l162s01a.getCustId() + l162s01a.getDupNo(), "");
			getAllCustIdMap.put(l162s01a.getRId() + l162s01a.getRDupNo(), "");

			// 主債務人的檢查
			if (l162s01a.getCustId().toUpperCase()
					.equals(l162s01a.getRId().toUpperCase())
					&& l162s01a.getDupNo().toUpperCase()
							.equals(l162s01a.getRDupNo().toUpperCase())) {
			} else {
				if (Util.isEmpty(l162s01a.getRKindD())
						|| Util.isEmpty(l162s01a.getRKindM())) {
					tempMap.put(showKey, l162s01a.getCntrNo().toUpperCase());
				}

			}

		}

		Map<String, Object> custData = null;
		// J-103-0299-001
		// Web e-Loan企金額度明細表保證人新增保證比例
		Map<String, String> custBusCd = new HashMap<String, String>();

		// 檢查債務人是否都有建檔
		StringBuffer temp0024 = new StringBuffer();
		for (String key : getAllCustIdMap.keySet()) {
			String custId = key.substring(0, key.length() - 1);
			String dupNo = key.substring(key.length() - 1, key.length());

			custData = misCustdataService.findAllByByCustIdAndDupNo(custId,
					dupNo);
			if (custData == null || custData.isEmpty()) {
				temp0024.append(temp0024.length() > 0 ? "" : "");
				temp0024.append(custId + " " + dupNo);
			} else {
				// J-103-0299-001
				// Web e-Loan企金額度明細表保證人新增保證比例
				custBusCd.put(Util.trim(custId) + Util.trim(dupNo), custData
						.get("BUSCD") != null ? custData.get("BUSCD")
						.toString() : "999999");
			}
		}
		if (temp0024.length() > 0) {
			// EFD3009=ERROR|$\{custId\}客戶中文檔0024 無此借款人資料 ！！|
			param.put("custId", temp0024.toString());
			throw new CapMessageException(RespMsgHelper.getMessage("EFD3009", param), getClass());
		}

		StringBuffer temp = new StringBuffer("");
		if (!tempMap.isEmpty()) {
			for (String key : tempMap.keySet()) {
				temp.append("<br/>");
				temp.append(key);
				temp.append(" ");
				temp.append(tempMap.get(key));
			}
			// L162M01A.message1=「主從債務人資料表」以下借款人，資料不完整，不得執行本功能{0}
			param.put("msg", MessageFormat.format(
					pop.getProperty("L162M01A.message1"), temp.toString()));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		return result;
	}

	/**
	 * 儲存連保人信用品質順序設定
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult savePriority(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String cntrNo = Util.trim(params.getString("cntrNo"));

		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);

		String data = params.getString("data");
		JSONObject jsonData = JSONObject.fromObject(data);

		List<L162S01A> l162s01as = lms1605Service.findL162s01aByMainIdCntrno(
				mainId, cntrNo);

		// 取得必要順序的保證人
		// 再刪選需要的保證人(保證人企業戶且為)
		List<L162S01A> mustL162s01as = lms1605Service.findL162s01aNeedPriority(
				l162s01as, cntrNo);

		if (mustL162s01as != null && !mustL162s01as.isEmpty()) {

			// 檢查保證比例100%前是否都有信用品質順序
			BigDecimal totGuaPercent = BigDecimal.ZERO;
			int setCount = 0;
			for (L162S01A l162s01a : mustL162s01as) {
				String oid = l162s01a.getOid();
				if (jsonData.has(oid)) {

					if (Util.notEquals(Util.trim(jsonData.getString(oid)), "")) {
						BigDecimal guaPercent = l162s01a.getGuaPercent() == null ? BigDecimal.ZERO
								: l162s01a.getGuaPercent();
						totGuaPercent = totGuaPercent.add(guaPercent);
						setCount = setCount + 1;
					}

				}

			}

			if (mustL162s01as.size() > setCount
					&& totGuaPercent.compareTo(Util.parseBigDecimal("100")) < 0) {
				// L140M01a.message262=保證人必需設定信用品質順序，直到該額度之保證人之負担保證責任比率合計達100%。
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1405S02Panel.class);
				String msg = prop.getProperty("L140M01a.message262");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}

		// 檢查信用品質順序是否有跳號
		int i = 0;
		int maxSeq = 0;
		Iterator<String> keys = jsonData.keys();
		while (keys.hasNext()) {
			String oid = keys.next();
			String id = jsonData.getString(oid);

			if (Util.notEquals(Util.trim(jsonData.getString(oid)), "")) {
				i = i + 1;

				int thisSeq = Util.parseInt(Util.trim(jsonData.getString(oid)));
				if (thisSeq == 0) {
					// L162M01A.message3=保證人信用品質順序必須從1開始依序編排，不得跳號。
					Properties prop = MessageBundleScriptCreator
							.getComponentResource(LMS1605M01Page.class);
					String msg = prop.getProperty("L162M01A.message3");
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}

				if (thisSeq >= maxSeq) {
					maxSeq = Util.parseInt(Util.trim(jsonData.getString(oid)));
				}

			}

		}

		if (i != maxSeq) {
			// L162M01A.message3=保證人信用品質順序必須從1開始依序編排，不得跳號。
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1605M01Page.class);
			String msg = prop.getProperty("L162M01A.message3");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}

		for (L162S01A l162s01a : l162s01as) {
			String oid = l162s01a.getOid();
			if (jsonData.has(oid)) {

				if (Util.equals(Util.trim(jsonData.getString(oid)), "")) {
					l162s01a.setPriority(null);

				} else {
					l162s01a.setPriority(Util.parseBigDecimal(Util
							.trim(jsonData.getString(oid))));
				}
				lms1605Service.save(l162s01a);
			}
		}

		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 取得保證人信評順序
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult getGuarantorCreditPriority(PageParameters params) throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);

		String data = params.getString("data");

		Map<String, String> map = lmsService.getGuarantorPriority("L160M01A",
				data);

		// JSONObject jsonData = JSONObject.fromObject(data);
		// Map<String, String> map = new LinkedHashMap<String, String>();
		// List<L140M01I> l140m01is = lms1401Service.findL140m01iListWithRType(
		// tabFormMainId, UtilConstants.lngeFlag.連帶保證人);
		// int i = 0;
		//
		// Iterator<String> keys = jsonData.keys();
		//
		// while (keys.hasNext()) {
		// String oid = keys.next();
		// String id = jsonData.getString(oid);
		// i = i + 1;
		//
		// map.put(Util.trim(Util.parseBigDecimal(id)),
		// Util.trim(i));
		// }

		result.putAll(map);
		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 引進建檔建檔系統主從債務人順序
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult applyEllngteePriority(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String l162s01aOid = params.getString("l162s01aOid"); // 只會有一筆
		String data = params.getString("data");
		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);
		L162S01A l162s01a = lms1605Service.findModelByOid(L162S01A.class,
				l162s01aOid);

		if (l162s01a == null) {
			l162s01a = new L162S01A();
		}

		String custId = Util.trim(l162s01a.getCustId());
		String dupNo = Util.trim(l162s01a.getDupNo());
		String cntrNo = Util.trim(l162s01a.getCntrNo());

		if (Util.isEmpty(cntrNo)) {
			Map<String, String> param = new HashMap<String, String>();
			// L160M01A.message63=「主從債務人資料表」額度序號不得為空!!
			param.put("msg", prop.getProperty("L160M01A.message63"));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, param), getClass());
		}

		List<Map<String, Object>> ellngteeDatas = misEllngteeService
				.getAllByCustIdCntrNo(custId, dupNo, cntrNo);

		if (ellngteeDatas == null || ellngteeDatas.isEmpty()) {
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		Map<String, String> ellngteeMap = new LinkedHashMap<String, String>();
		boolean hasPriority = false;
		for (Map<String, Object> ellngteeData : ellngteeDatas) {

			String lngeFlag = (String) (ellngteeData.get("LNGEFLAG") == null ? ""
					: ellngteeData.get("LNGEFLAG"));

			// C: 共同借款人
			// D: 共同發票人　
			// E: 票據債務人（指金融交易之擔保背書）
			// G: 連帶保證人，擔保品提供人兼連帶保證人
			// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
			// S: 擔保品提供人
			// N: ㄧ般保證人

			if (Util.equals(lngeFlag, UtilConstants.lngeFlag.連帶保證人)
					|| Util.equals(lngeFlag, UtilConstants.lngeFlag.ㄧ般保證人)) {
				String priority = ((ellngteeData.get("PRIORITY") == null || Util
						.equals(Util.trim(ellngteeData.get("PRIORITY")), "0")) ? " "
						: Util.trim(ellngteeData.get("PRIORITY")));

				if (Util.notEquals(Util.trim(priority), "")
						&& Util.notEquals(Util.trim(priority), "0")) {

					// 990以上是透過批次設定的，分行應該要重新自己設定一次
					// Step1->991.保證人國別都相同或只有一個保證人
					// Step2->992.裡面有非100的，則以最大的當代表............????????.....要不要再調整
					// Step3->993.保證人RATING最高
					// Step4->994.任一保證人國別與額度最終風險國別相同
					// Step5->995.任一保證人國別與借款人相同
					// Step6->996.保證人國家RATING最高
					// Step6->997.無法判斷(非以上STEP)
					if (Util.parseBigDecimal(Util.trim(priority)).compareTo(
							Util.parseBigDecimal("990")) <= 0) {
						hasPriority = true;
					}

				}

				String lngeId = (String) (ellngteeData.get("LNGEID") == null ? ""
						: Util.trim(ellngteeData.get("LNGEID")));
				String dupNo1 = (String) (ellngteeData.get("DUPNO1") == null ? ""
						: Util.trim(ellngteeData.get("DUPNO1")));

				String key = lngeId + "-" + dupNo1;
				ellngteeMap.put(key, priority);
			}

		}

		if (!hasPriority) {
			// ELLNGTEE 資料都沒有一筆有設定PRIORITY
			// EFD0036=INFO|查無資料!|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0036"), getClass());
		}

		JSONObject jsonData = JSONObject.fromObject(data);
		Map<String, String> map = new LinkedHashMap<String, String>();

		List<L162S01A> l162s01as = lms1605Service.findL162s01aByMainIdCntrno(
				mainId, cntrNo);

		Iterator<String> keys = jsonData.keys();

		while (keys.hasNext()) {
			// 設定初始值
			String oid = keys.next();
			String id = jsonData.getString(oid);
			map.put(Util.trim(Util.parseBigDecimal(id)), " ");
		}

		for (L162S01A tl162s01a : l162s01as) {
			String oid = tl162s01a.getOid();
			if (jsonData.has(oid)) {
				String id = jsonData.getString(oid);
				String rId = Util.trim(tl162s01a.getRId());
				String rDupNo = Util.trim(tl162s01a.getRDupNo());
				String key = rId + "-" + rDupNo;
				if (ellngteeMap.containsKey(key)) {

					map.put(id, MapUtils.getString(ellngteeMap, key));
				}

			}
		}

		result.putAll(map);
		return result;// 傳回執行這個動作的AjAX
	}

	/**
	 * 整批引進最新資料
	 * 
	 * J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult entireApplyNew(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		boolean needSave = false;
		String[] oids = params.getStringArray("oids");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		String allCheackedVal = params.getString("allCheackedVal");
		L160M01A l160m01a = lms1605Service.findL160M01AByMaindId(mainId);

		List<L162S01A> l162s01as = lms1605Service.findL162S01AByOids(oids);

		String guaNaExposure = params.getString("guaNaExposure");

		String toDayStr = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		String toDayStr1 = toDayStr.replace("-", "");

		// 上個月
		String newDateStr = CapDate.formatyyyyMMddToDateFormat(
				CapDate.addMonth(toDayStr1, -1),
				UtilConstants.DateFormat.YYYY_MM);

		// 上個月底最後一天
		String preMonLastDate = Util
				.toAD(CapDate.shiftDays(CapDate.addMonth(
						CapDate.parseDate(newDateStr + "-01"), 1), -1));

		// 上個月第一天
		String preMonFirstDate = Util.getLeftStr(preMonLastDate, 7) + "-01";

		HashMap<String, String> doneCntrNoMapA01 = new HashMap<String, String>();

		// 如果文件包含非編製中的不能刪除
		for (L162S01A l162s01a : l162s01as) {

			String cntrNo = Util.trim(l162s01a.getCntrNo());
			String custId = Util.trim(l162s01a.getCustId());
			String dupNo = Util.trim(l162s01a.getDupNo());

			String[] entireApplyCheckList = Util.trim(allCheackedVal).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String checkItem : entireApplyCheckList) {
				if (Util.equals(checkItem, "A01")) {
					// 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別

					if (Util.notEquals(cntrNo, "")) {

						// BY 額度整批設定
						if (!doneCntrNoMapA01.containsKey(cntrNo)) {

							List<L162S01A> tl162s01as = lms1605Service
									.findL162s01aByMainIdCntrno(mainId, cntrNo);
							for (L162S01A tl162s01a : tl162s01as) {
								String rType = Util.trim(tl162s01a.getRType());
								if (Util.notEquals(rType, "C")
										&& Util.notEquals(rType, "S")) {
									tl162s01a.setGuaNaExposure(guaNaExposure);
									lms1605Service.save(tl162s01a);
								}
							}

							doneCntrNoMapA01.put(cntrNo, cntrNo);
						}

					}

				}

			}

		}
		CapAjaxFormResult result = new CapAjaxFormResult();

		return result;
	}

	public String getCntrno(L140M01A l140m01a, String type, String numberType, String selectBrNo, String originalCntrNo)
	throws CapException {
		// 用來放額度序號
		Map<String, Object> cntrNo = null;
		String returnCntrNo = null;
		// 目前的程式規格只for海外
		// 當額度序號等於空 或 額度明細表額度序號 前三碼不等於目前攤貸行，給新號
		if (Util.isEmpty(originalCntrNo) || !selectBrNo.equals(originalCntrNo.substring(0, 3))) {

			if ("1".equals(numberType)) {
				// 是否為遠匯 ，是 =X 、否=0
				String classCd = "0";
				if (LMSUtil.isNeedX(l140m01a)) {
					classCd = "X";
				}
				String ownBrName = branchService.getBranchName(selectBrNo);
				if (ownBrName == null) {
					// EFD0037=WARN|找不到該分行代號之分行名稱，請洽資訊室!!|
					throw new CapMessageException(RespMsgHelper.getMessage("EFD0037"), getClass());
				}
				cntrNo = lms1405Service.queryLnsp0050(
						selectBrNo.toUpperCase(), type, classCd);
				if (!"YES".equals(cntrNo.get("chkFlag"))) {
					HashMap<String, String> msg = new HashMap<String, String>();
					msg.put("msg", Util.trim((String) cntrNo.get("errMsg")));
					// 錯誤訊息
					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
							getClass());
				}
				returnCntrNo = (String) cntrNo.get("cntrNo");
			} else {
				returnCntrNo = originalCntrNo;
			}

		}else{
			returnCntrNo = originalCntrNo;
		}
		return returnCntrNo;
	}
	
	/**
	 * 檢查原案額度序號(從額度明細複製過來，這僅用到 justSave=3聯行攤貸比例, 相關判斷先拿掉)
	 * @param params
	 *            <pre>
	 *            {String}cntrNo 額度序號 
	 *            {String}oid額度明細表文件編號
	 * </pre>
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkCntrno(PageParameters params)
			throws CapException {

		String cntrNo = params.getString("cntrNo", "");
		String snoKind = params.getString("snoKind", "");
		String cntrMainId = params.getString("cntrMainId", "");

		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(cntrMainId);
		if (Util.isEmpty(cntrNo)) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}

		if (l140m01a == null) {
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤), getClass());
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("ownBrName",
				branchService.getBranchName(cntrNo.substring(0, 3)));

		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();

		// 2012_05_22_ 建霖說看已核准簽報書的額度明細表額度序號與攤貸行(l140m01e)的
		List<L140M01A> l140m01as = lms1405Service.findL140m01aBycntrNo(cntrNo,
				custId, dupNo);
		int count140 = 0;
		if (l140m01as != null && !l140m01as.isEmpty()) {
			count140 = l140m01as.size();
		}

		int countE = 0;
		if (count140 == 0) {
			countE = eloandbBASEService.findL140M01EByCustIdAndDupNoAndCntrno(
					custId, dupNo, cntrNo);

		}
		//J-112-0522 依金管會112年度專案檢查報告面請改善事項辦理，因營業單位漏未建檔報送金管會AI370聯合授信案基本資料(國內BTT-L556、海外AS400-3K30)，致本行每月報送報表產生報送錯誤，擬將上述建檔機制移至E-LOAN-授信管理系統-動用審核表內，於動用審核表新增一頁籤(聯貸案基本資料)，營業單位於簽約動審時，須完成建檔作業。
		//舊案額度序號檢核多看LN.LNF277有存在也可以建
		int count277 = 0;
		String controlflag = Util.trim(lmsService.getSysParamDataValue("J-112-0522_CNTRNO_CHK"));
		if(Util.equals(UtilConstants.DEFAULT.是, controlflag)){
			if (count140 == 0 && countE == 0) {
				count277 = misdbBASEService.findLNF277ByCntrno(cntrNo);
			}
		}
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1605M01Page.class);

		// 在額度明細表查詢不到時在到帳務系統內查詢
		int count = 0;
		if (count140 == 0 && countE == 0 && count277 ==0) {
			// 海外查 DW_ASLNQUOT
			if (TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))) {
				count = dwLnquotovService.findByCntrNoAndCustIdAndDupNo(cntrNo,
						custId, dupNo);
				// 以下為測試用資料
				// count = dwASLNQUOTService.findByCntrNoAndCustIdAndDupNo(
				// "238109800150", "A100008276", "0");
			} else {
				custId = Util.addSpaceWithValue(custId, 10);
				// X為 遠匯
				if ("X".equals(cntrNo.substring(7, 8))) {
					count = misdbBASEService.findLNF197BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				} else {
					count = misdbBASEService.findMISLN20BycntrNoAndCustId(
							cntrNo, custId, dupNo);
				}
			}

		}
		// ELOAN 跟帳務系統都沒有時，再檢查ELF442
		int count442 = 0;
		if (count140 == 0 && countE == 0 && count == 0 && count277 ==0) {
			// 檢查預約額度
			count442 = misELF442Service.findELF442ByCntrNoByCheck3(custId,
					dupNo, cntrNo).size();
		}
		// ELOAN 、帳務系統、ELF442預約都沒有時，顯示錯誤
		if (count140 == 0 && countE == 0 && count == 0 && count442 == 0 && count277 == 0) {
			// L140M01a.message69=此舊額度序號：{0} 並未存在於帳務系統, 請確認後再輸入!
			String msg = MessageFormat.format(
					prop.getProperty("L140M01a.message69"), cntrNo);
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		// 非海外且不是遠匯才需要檢查額度種類
		if (!Util.isEmpty(snoKind)) {
			if (!TypCdEnum.海外.getCode().equals(cntrNo.substring(3, 4))
					&& !"X".equals(cntrNo.substring(7, 8))) {

				l140m01a.setSnoKind(snoKind);
				String factType = misMISLN20Service.findFactType(custId, dupNo,
						cntrNo);
				if (!Util.isEmpty(factType)) {
					if ("51".equals(factType)) {
						factType = UtilConstants.Cntrdoc.snoKind.一般;
					} else if ("50".equals(factType)) {
						factType = UtilConstants.Cntrdoc.snoKind.信保;

					}
					// 2013-06-14,Rex, edit 修改當額度控管種類不存在 於020 才需檢查020
					if (Util.isNotEmpty(factType) && !snoKind.equals(factType)) {
						if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
							if (Util.equals(factType.subSequence(0, 1), "4")
									&& Util.equals(snoKind.subSequence(0, 1),
											"6")) {
								// J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
								// 供應鏈融資允許ALOAN 是 40/41 但 ELOAN是 60/61/62

								// OK
							} else {
								Map<String, String> codeMap = codeTypeService
										.findByCodeType("lms1405m01_snoKind");
								// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
								String msg = MessageFormat.format(
										prop.getProperty("L140M01a.message90"),
										codeMap.get(snoKind),
										codeMap.get(factType));
								throw new CapMessageException(msg, getClass());
							}
						} else {

							Map<String, String> codeMap = codeTypeService
									.findByCodeType("lms1405m01_snoKind");
							// L140M01a.message90=額度明細表之額度控管種類『{0}』與a-Loan『{1}』不同!!
							String msg = MessageFormat
									.format(prop
											.getProperty("L140M01a.message90"),

											codeMap.get(snoKind), codeMap
													.get(factType));
							throw new CapMessageException(msg, getClass());
						}
					} else {
						// lms1405Service.save(l140m01a);
					}
				} else {
					// lms1405Service.save(l140m01a);
				}
			}
		}
		return result;// 傳回執行這個動作的AjAX
	}
	
	/**
	 * 查詢分行為海外還是國內
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryShareBrIdType(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("cntrMainId"));
		String shareBrId = params.getString("shareBrId");

		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(shareBrId)
				.getBrNoFlag())) {
			result.set("type", TypCdEnum.海外.getCode());
		} else {
			result.set("type", "");
		}
		// 檢查目前分行是否已經登錄
		L140M01E_AF l140m01e_af = lms1405Service.findL140m01e_afByUniqueKey(mainId, shareBrId);
		result.set("have", l140m01e_af != null ? true : false);
		
		return result;
	}
	
	/**
	 * G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
	 * @param l161s01b_slbank
	 * @param l161s01b_slbranch
	 * @param l161s01a_cntrno
	 * @param l140m01a_cntrno
	 * @return checkflag  true=可、flase=不可新刪
	 * @throws CapException
	 */
	public boolean checkCanModL140M01E_AF(String L161S01A_cntrno, String l140m01a_cntrno)throws CapException {
		boolean checkflag = true;
		//非額度管理行是否能新增/刪除本行攤貸資訊
		String controlflag = Util.trim(lmsService.getSysParamDataValue("G-113-0036-controlFlag"));
		if(Util.equals(UtilConstants.DEFAULT.否, controlflag)){
			if(Util.notEquals(L161S01A_cntrno.substring(0, 3), l140m01a_cntrno.substring(0, 3))){
				//(額度序號前三碼與簽案額度序號相比)
				checkflag = false;
			}
		}
		return checkflag;
	}
	
	public boolean checkCanDelL140M01E_AF(String[] oids, String l140m01a_mainid, String L161S01A_cntrno)throws CapException {
		boolean checkflag = true;
		//非額度管理行是否能新增/刪除本行攤貸資訊
		//G-113-0036 各筆額度主辦行才能新增/刪除聯行額度攤貸資訊(額度序號前三碼與簽案額度序號相比)
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(l140m01a_mainid);
		checkflag = this.checkCanModL140M01E_AF(L161S01A_cntrno, l140m01a.getCntrNo());

		return checkflag;
	}
	
	/**
	 * 查詢 攤貸比例 並告訴使用者目前剩餘可攤貸金額，和刪除以攤貸分行
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140m01e_af(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		CapAjaxFormResult brankList = null;
		String mainId = params.getString("cntrMainId");
		List<L140M01E_AF> modelEs = (List<L140M01E_AF>) lms1405Service
				.findListByMainId(L140M01E_AF.class, mainId);
		//簽案額度明細
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		BigDecimal ShareRate1Count = BigDecimal.ZERO;
		BigDecimal ShareAmtCount = BigDecimal.ZERO;
		Map<String, String> m = new TreeMap<String, String>();

		List<IBranch> bank = branchService.getAllBranch();
		for (IBranch b : bank) {
			String brName = Util.trim(b.getBrName());
			String brCode = b.getBrNo();
			m.put(brCode, brName);
		}

		String oid = params.getString(EloanConstants.OID);
		L140M01E_AF l140m01e_af = null;
		if (!Util.isEmpty(oid)) {
			l140m01e_af = lms1405Service.findModelByOid(L140M01E_AF.class, oid);
			CapAjaxFormResult formData = DataParse.toResult(l140m01e_af,
					DataParse.Delete, new String[] { EloanConstants.MAIN_ID,
							EloanConstants.OID });
			result.set("formData", formData);

		}
		if (!modelEs.isEmpty()) {// 看目前筆數是否為空
			BigDecimal ShareRate2 = modelEs.get(0).getShareRate2();
			BigDecimal TotalAmt = modelEs.get(0).getTotalAmt();
			result.set("role", String.valueOf(ShareRate2));// 取得目前分母
			String shareFlag = "";
			for (L140M01E_AF f : modelEs) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(f
						.getShareFlag())) {
					ShareRate1Count = ShareRate1Count.add(f.getShareRate1());
				}

				ShareAmtCount = ShareAmtCount.add(f.getShareAmt());
				if (m.containsKey(f.getShareBrId())) {
					if (!Util.isEmpty(oid)
							&& f.getShareBrId().equals(f.getShareBrId())) {
						continue;
					}
					m.remove(f.getShareBrId());
				}
				shareFlag = f.getShareFlag();
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(shareFlag)) {
				ShareRate2 = ShareRate2.subtract(ShareRate1Count);
			}

			TotalAmt = TotalAmt.subtract(ShareAmtCount);
			result.set("tips", String.valueOf(ShareRate2));// 增加目前分子剩下提示
			result.set("tips2", String.valueOf(TotalAmt));// 增加目前可攤貸餘額提示
			result.set("shareFlag", shareFlag);
		} else {
			result.set("role", "0");
		}
		brankList = new CapAjaxFormResult(m);
		result.set("item", brankList);
		result.set("totalAmt",
				NumConverter.addComma(l140m01a.getCurrentApplyAmt()));
		return result;

	}
	
	/**
	 * 儲存額度明細表攤貸比率
	 * 
	 * @param params
	 *            <pre>
	 * {
	 *  L140M01E_AFForm :form的資料,
	 *  cntrMainId:額度明細表mainId
	 *  numberType: 給號的種類 1.給新號 2.舊號
	 *  type:額度序號的種類 1.DBU,4.OBU,5.海外
	 *  classCD:是否為遠匯
	 *  selectBrNo:分行號碼
	 *  originalCntrNo:原始額度序號
	 *  }
	 * </pre>
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140m01e_af(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = params.getString("cntrMainId","");
		String cntrno = params.getString("cntrNo", "");

		String formL140m01e_af = params.getString("L140M01E_AFForm"); // 指定的form
		JSONObject jsonL140m01e_af = JSONObject.fromObject(formL140m01e_af);
		String shareBrId = jsonL140m01e_af.getString("shareBrId");
		L140M01E_AF l140m01e_af = lms1405Service.findL140m01e_afByUniqueKey(mainId,
				shareBrId);
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		// 計算分母
		BigDecimal shareRate1Count = BigDecimal.ZERO;

		List<L140M01E_AF> l140m01e_afList = (List<L140M01E_AF>) lms1405Service.findListByMainId(L140M01E_AF.class, mainId);
		// 用來放額度序號
		Map<String, Object> cntrNo = null;
		for (L140M01E_AF modele : l140m01e_afList) {
			// 如果已存在這間分行不將金額納入計算，以免會重複計算到
			if (shareBrId.equals(modele.getShareBrId())) {
				continue;
			}
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(modele
					.getShareFlag())) {
				shareRate1Count = shareRate1Count.add(modele.getShareRate1());
			}
		}
		if (l140m01e_af == null) {
			// 判斷額度序號給號
			String type = params.getString("type");
			l140m01e_af = new L140M01E_AF();
			l140m01e_af.setMainId(mainId);
			DataParse.toBean(jsonL140m01e_af, l140m01e_af);
			String numberType = params.getString("numberType");
			String originalCntrNo = params.getString("originalCntrNo");
			// 2012-04-10_當該分行已有自己的額度序號，就不需重新起號
			String cntrNoTemp = Util.trim(l140m01a.getCntrNo());
			// 目前的程式規格只for海外
			// 當額度序號等於空 或 額度明細表額度序號 前三碼不等於目前攤貸行，給新號
			if (Util.isEmpty(cntrNoTemp) || !shareBrId.equals(cntrNoTemp.substring(0, 3))) {
				if ("1".equals(numberType)) {
					String selectBrNo = params.getString("selectBrNo");
					// 是否為遠匯 ，是 =X 、否=0
					String classCd = "0";
					if (LMSUtil.isNeedX(l140m01a)) {
						classCd = "X";
					}
					String ownBrName = branchService.getBranchName(selectBrNo);
					if (ownBrName == null) {
						// EFD0037=WARN|找不到該分行代號之分行名稱，請洽資訊室!!|
						throw new CapMessageException(RespMsgHelper.getMessage("EFD0037"), getClass());
					}
					cntrNo = lms1405Service.queryLnsp0050(
							selectBrNo.toUpperCase(), type, classCd);
					if (!"YES".equals(cntrNo.get("chkFlag"))) {
						HashMap<String, String> msg = new HashMap<String, String>();
						msg.put("msg", Util.trim(cntrNo.get("errMsg")));
						// 錯誤訊息
						throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.執行有誤, msg),
								getClass());
					}
					cntrNoTemp = (String) cntrNo.get("cntrNo");
				} else {
					cntrNoTemp = originalCntrNo;
				}
			}
			l140m01e_af.setShareNo(cntrNoTemp);
			// 再存的時候把是國內國外的flag 加進去
			l140m01e_af.setFlag(branchService.getBranch(shareBrId).getBrNoFlag());
			l140m01e_af.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01e_af.setCreator(user.getUserId());
			Long shareAmt = Util.parseLong(NumConverter
					.delCommaString(jsonL140m01e_af.getString("shareAmt")));
			l140m01e_af.setShareAmt(new BigDecimal(shareAmt));
		} else {
			DataParse.toBean(jsonL140m01e_af, l140m01e_af);
		}
		if (!this.checkCanModL140M01E_AF(cntrno, l140m01a.getCntrNo())) {
			// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
			// 非主辦行是否能新刪本行攤貸資訊
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1605M01Page.class);
			String msg = prop.getProperty("L140M01e.message07");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		
		// 當如果是以比例計算就判斷比例
		if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
			// 檢核分子總和是否大於分母
			if (shareRate1Count.add(l140m01e_af.getShareRate1()).compareTo(
					l140m01e_af.getShareRate2()) == 1) {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				// L140M01e.lmterror 分子總和大於分母無法儲存
				String msg = prop.getProperty("L140M01e.lmterror");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		} else {
			l140m01e_af.setShareRate1(null);
			l140m01e_af.setShareRate2(null);
		}
		// 專前端的現請額度
		BigDecimal totalAmt = new BigDecimal(NumConverter.delComma(jsonL140m01e_af.getString("totalAmt")));
		l140m01e_af.setTotalAmt(totalAmt);
		lms1405Service.save(l140m01e_af);
		CapAjaxFormResult result = new CapAjaxFormResult();
		return result;// 傳回執行這個動作的AjAX
	}
	
	/**
	 * 刪除(多筆)額度明細表攤貸比率
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140m01e_af(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] Idlist = params.getStringArray("Idlist");
		String mainId = params.getString("cntrMainId", "");
		String cntrno = params.getString("cntrNo", "");
		if (Idlist.length > 0) {
			boolean checkflag = this.checkCanDelL140M01E_AF(Idlist, mainId, cntrno);
			if(checkflag){//可刪
				lms1405Service.deleteL140m01e_afList(Idlist);
			} else {
				// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
				// 非主辦行是否能新刪本行攤貸資訊
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(LMS1605M01Page.class);
				String msg = prop.getProperty("L140M01e.message07");
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
			}
		}
		

		return result;
	}
	
	/**
	 * 查詢變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult queryChangesShareRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("cntrMainId", "");
		HashMap<String, String> msg = new HashMap<String, String>();
		List<L140M01E_AF> L140M01E_AFs = (List<L140M01E_AF>) lms1405Service
				.findModelListByMainId(L140M01E_AF.class, mainId);
		if (L140M01E_AFs.isEmpty()) {
			// L140M01e.message01=請先登錄聯行攤貸比例
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1605M01Page.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01e.message01")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		if (UtilConstants.Cntrdoc.shareType.以金額計算.equals(L140M01E_AFs.get(0)
				.getShareFlag())) {
			// L140M01e.message02=以比例計算時才可使用此功能
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1605M01Page.class);
			msg.put("msg", Util.trim((String) prop.get("L140M01e.message02")));
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		return result;
	}
	
	/**
	 * 儲存變更分母
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveChangesShareRate(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = params.getString("cntrMainId", "");
		String cntrno = params.getString("cntrNo");
		String shareRate = NumConverter.delCommaString(params.getString("shareRate", ""));
		List<L140M01E_AF> l140m01e_afs = (List<L140M01E_AF>) lms1405Service
				.findModelListByMainId(L140M01E_AF.class, mainId);
		//取得額度明細
		L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
		if (!this.checkCanModL140M01E_AF(cntrno, l140m01a.getCntrNo())) {
			// G-113-0036 各筆額度主辦行才能新增聯行額度攤貸資訊
			// 非主辦行是否能新刪本行攤貸資訊
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1605M01Page.class);
			String msg = prop.getProperty("L140M01e.message07");
			throw new CapMessageException(RespMsgHelper.getMessage(
					UtilConstants.AJAX_RSP_MSG.執行有誤, msg), getClass());
		}
		// 原始分母
		BigDecimal orgRate = l140m01e_afs.get(0).getShareRate2();
		// 新分母
		BigDecimal newRate = new BigDecimal(shareRate);
		// 當新分母與舊分母不同 才做更新
		if (orgRate.compareTo(newRate) != 0) {
			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				l140m01e_af.setShareRate2(newRate);
				// 重算攤貸額度金額 攤貸總額 * 分子 /　分母
				BigDecimal shareAmt = Arithmetic.div_floor(l140m01e_af
						.getTotalAmt().multiply(l140m01e_af.getShareRate1()),
						l140m01e_af.getShareRate2(), 0);
				l140m01e_af.setShareAmt(shareAmt);
			}
			lms1405Service.savelistL140M01E_AF(l140m01e_afs);
		}
		return result;
	}
	
	/**
	 * 儲存動審攤貸剩餘餘額
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140m01e_afAmt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String amt = NumConverter.delCommaString(params.getString("amt"));
		L140M01E_AF l140m01e_af = lms1405Service.findModelByOid(L140M01E_AF.class, oid);

		if (l140m01e_af != null) {
			l140m01e_af.setShareAmt(Util.parseBigDecimal(l140m01e_af.getShareAmt()).add(new BigDecimal(amt)));

			lms1405Service.save(l140m01e_af);
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.執行成功));
		}

		return result;// 傳回執行這個動作的AjAX
	}
	
	/**
	 * 檢查攤貸比率是否攤貸完畢並將尚未攤貸餘額加到該額度明細表上的分行， 若沒有則跳出視窗選擇加在哪一筆上
	 * 
	 * @param l140m01a
	 *            額度明細表主檔
	 * @return BigDecimal 回傳剩餘金額
	 */
	public BigDecimal checkL140m01e_af(L140M01A l140m01a) {
		Set<L140M01E_AF> l140m01e_afs = l140m01a.getL140m01e_af();
		BigDecimal nowCurrAMT = l140m01a.getCurrentApplyAmt();
		if (nowCurrAMT == null) {
			nowCurrAMT = BigDecimal.ZERO;
		}
		// 是否有餘額剩下
		BigDecimal tempAmt = BigDecimal.ZERO;
		if (l140m01e_afs != null && !l140m01e_afs.isEmpty()) {
			// 加總分子
			BigDecimal shareRate1Count = BigDecimal.ZERO;
			// 分母
			BigDecimal shareRate2temp = BigDecimal.ZERO;
			// 計算總金額
			BigDecimal amtCount = BigDecimal.ZERO;

			for (L140M01E_AF l140m01e_af : l140m01e_afs) {
				if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
					shareRate1Count = shareRate1Count.add(l140m01e_af.getShareRate1());
					shareRate2temp = l140m01e_af.getShareRate2();
				}
				amtCount = amtCount.add(Util.parseBigDecimal(l140m01e_af.getShareAmt()));
			}

			// 檢核分子是否攤貸完
			if (shareRate1Count.compareTo(shareRate2temp) == 0) {
				// 當加總額小於現請額度
				if (amtCount.compareTo(nowCurrAMT) == -1) {
					tempAmt = nowCurrAMT.subtract(amtCount);
				}
			}
		}
		return tempAmt;
	}
	
	/**
	 * 判斷動審l161s01a 現請額度
	 * @param l140m01a 額度明細
	 * @param l161s01a
	 * @param ShareAmt
	 * @return
	 */
	public BigDecimal checkL161s01aCurrentApplyAmt(L140M01A l140m01a, L161S01A l161s01a, BigDecimal ShareAmt) {
		BigDecimal currentApplyAmt = l140m01a.getCurrentApplyAmt();
		// 非主辦行, 放額度明細表現請額度
		if (Util.notEquals(l161s01a.getCntrNo().substring(0, 3), l140m01a.getCntrNo().substring(0, 3))) {
			currentApplyAmt = ShareAmt;
		}
		return currentApplyAmt;
	}
}
