/* 
 * L120S04ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S04A;

/** 關係戶於本行各項業務往來檔 **/
@Repository
public class L120S04ADaoImpl extends LMSJpaDao<L120S04A, String>
	implements L120S04ADao {

	@Override
	public L120S04A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S04A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04A> list = createQuery(L120S04A.class,search).getResultList();
		
		return list;
	}

	@Override
	public L120S04A findByUniqueKey(String mainId, String custId, String dupNo, String custName){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
//		search.addSearchModeParameters(SearchMode.EQUALS, "custName", custName);
		return findUniqueOrNone(search);
	}	
	
	@Override
	public List<L120S04A> findByMainIdPrtFlag(String mainId,String prtFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "prtFlag", prtFlag);
		search.addOrderBy("custRelation");
		search.addOrderBy("profit",true);
		search.addOrderBy("custId");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04A> list = createQuery(L120S04A.class,search).getResultList();
		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s04a(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"L120S04A.sell120s04a");
		query.setParameter("MAINID", mainId); //設置參數
		query.setMaxResults(Integer.MAX_VALUE);
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s04a_keyCustIdDupNo(String mainId, String keyCustId, String keyDupNo) {
		Query query = getEntityManager().createNamedQuery(
				"L120S04A.sell120s04a_keyCustIdDupNo");
		query.setParameter("MAINID", mainId); //設置參數
		query.setParameter("KEYCUSTID", keyCustId);
		query.setParameter("KEYDUPNO", keyDupNo);
		query.setMaxResults(Integer.MAX_VALUE);
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s04a2(String mainId) {
		Query query = getEntityManager().createNamedQuery(
				"L120S04A.sell120s04a2");
		query.setParameter("MAINID", mainId); //設置參數
		query.setMaxResults(Integer.MAX_VALUE);
		return query.getResultList();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findL120s04a2_keyCustIdDupNo(String mainId, String keyCustId, String keyDupNo) {
		Query query = getEntityManager().createNamedQuery(
				"L120S04A.sell120s04a2_keyCustIdDupNo");
		query.setParameter("MAINID", mainId); //設置參數
		query.setParameter("KEYCUSTID", keyCustId);
		query.setParameter("KEYDUPNO", keyDupNo);
		query.setMaxResults(Integer.MAX_VALUE);
		return query.getResultList();
	}
	
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S04A.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		query.setMaxResults(Integer.MAX_VALUE);
		return query.executeUpdate();
	}
	@Override
	public List<L120S04A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04A> list = createQuery(L120S04A.class,search).getResultList();
		return list;
	}

	@Override
	public List<L120S04A> findByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04A> list = createQuery(L120S04A.class,search).getResultList();
		return list;
	}

	@Override
	public List<L120S04A> findByMainIdKeyCustIdDupNoPrtFlag(String mainId,
			String keyCustId, String keyDupNo, String prtFlag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "prtFlag", prtFlag);
		search.addOrderBy("custRelation");
		search.addOrderBy("profit",true);
		search.addOrderBy("custId");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S04A> list = createQuery(L120S04A.class,search).getResultList();
		return list;
	}
}