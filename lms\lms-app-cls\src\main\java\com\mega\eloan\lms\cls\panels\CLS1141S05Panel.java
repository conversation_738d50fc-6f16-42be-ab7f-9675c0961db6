/* 
 * CLS1141S05Panel.java
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 個金簽報書-說明 *
 * </pre>
 * 
 * @since 2012/12/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/9,REX,new
 *          </ul>
 */
public class CLS1141S05Panel extends Panel {

	public CLS1141S05Panel(String id) {
		super(id);
	}

	public CLS1141S05Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	private static final long serialVersionUID = 1L;

}
