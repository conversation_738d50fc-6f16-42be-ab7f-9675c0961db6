/* 
 * L180M02ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.mega.eloan.lms.dao.L181M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L181M01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

/** 覆審控制維護主檔 **/
@Repository
public class L181M01ADaoImpl extends LMSJpaDao<L181M01A, String> implements
		L181M01ADao {

	@Override
	public L181M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L181M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L181M01A> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<L181M01A> list = createQuery(L181M01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L181M01A findByBranchCustId(String branch, String custId,
			String dupNo, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "elfBranch", branch);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public L181M01A findInProcessData(String branch, String custId,
			String dupNo, String[] docStatusArr, String ctlType, String ownBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "elfBranch", branch);
		if (Util.isNotEmpty(Util.trim(custId))) {
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		}
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IN, "docStatus", docStatusArr);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		if (Util.notEquals(Util.trim(ownBrId), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					ownBrId);
		}
		return findUniqueOrNone(search);
	}
}