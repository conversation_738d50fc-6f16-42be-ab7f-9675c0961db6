/* 
 * L140S02E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 償還方式檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140S02E", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class L140S02E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/**
	 * 償還方式
	 * <p/>
	 * 單選：<br/>
	 * 1.本息平均攤還(按月繳款)<br/>
	 * 2.本息平均攤還(雙週繳款)<br/>
	 * 3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 * 4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 * 5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 * 6.其他（請自行輸入）<br/>
	 * 7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 */
	@Size(max = 1)
	@Column(name = "PAYWAY", length = 1, columnDefinition = "CHAR(1)")
	private String payWay;

	/**
	 * 償還方式(每期攤還本金)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * ※本金平均攤還
	 */
	@Digits(integer = 13, fraction = 0, groups = Check.class)
	@Column(name = "PAYWAYAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal payWayAmt;

	/**
	 * 償還方式(其他)
	 * <p/>
	 * ※其他
	 */
	@Size(max = 1200)
	@Column(name = "PAYWAYOTH", length = 1200, columnDefinition = "VARCHAR(1200)")
	private String payWayOth;

	/**
	 * 前次寬限期起始年月
	 * <p/>
	 * YYYYMM
	 */
	@Size(max = 6)
	@Column(name = "LASTYM", length = 6, columnDefinition = "VARCHAR(6)")
	private String lastYM;

	/** 前次寬限期(起) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "EXTFROM", columnDefinition = "DECIMAL(3,0)")
	private Integer extFrom;

	/** 前次寬限期(迄) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "EXTEND", columnDefinition = "DECIMAL(3,0)")
	private Integer extEnd;

	/** 截至前次剩餘之寬限期 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "OVERTERM", columnDefinition = "DECIMAL(3,0)")
	private Integer overTerm;

	/**
	 * 本次是否有寬限期
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "NOWEXTEND", length = 1, columnDefinition = "CHAR(1)")
	private String nowExtend;

	/** 本次寬限期(起) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "NOWFROM", columnDefinition = "DECIMAL(3,0)")
	private Integer nowFrom;

	/** 本次寬限期(迄) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "NOWEND", columnDefinition = "DECIMAL(3,0)")
	private Integer nowEnd;

	/** 本次寬限期(餘) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "NOWTERM", columnDefinition = "DECIMAL(3,0)")
	private Integer nowTerm;

	/** 本次寬限期(％) **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "NOWTERMRATE", columnDefinition = "DECIMAL(3,0)")
	private Integer nowTermRate;

	/**
	 * 是否展延
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "ADJCHECK", length = 1, columnDefinition = "CHAR(1)")
	private String adjCheck;

	/**
	 * 展延種類（lnf033_def_type）  	select * from com.bcodetype where codetype='L140S02E_adjKind' 
	 * <p/>
	 * 單選：<br/>
	 * 1.921展延<br/>
	 * 2.非志願性勞工展延
	 */
	@Size(max = 1)
	@Column(name = "ADJKIND", length = 1, columnDefinition = "CHAR(1)")
	private String adjKind;

	/**
	 * 展延項目（lnf033_def_item）	 select * from com.bcodetype where codetype='L140S02E_adjItem' 
	 * <p/>
	 * 單選：<br/>
	 * 1.本金展延<br/>
	 * 2.本金及利息展延
	 */
	@Size(max = 1)
	@Column(name = "ADJITEM", length = 1, columnDefinition = "CHAR(1)")
	private String adjItem;

	/**
	 * 本金展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ADJCAPSNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer adjCapSNum;

	/** 本金展延截止期 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ADJCAPENUM", columnDefinition = "DECIMAL(3,0)")
	private Integer adjCapENum;

	/**
	 * 利息展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ADJINTSNUM", columnDefinition = "DECIMAL(3,0)")
	private Integer adjIntSNum;

	/** 利息展延截止期 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "ADJINTENUM", columnDefinition = "DECIMAL(3,0)")
	private Integer adjIntENum;

	/** 應收利息攤還截止期 **/
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "AMORENUM", columnDefinition = "DECIMAL(3,0)")
	private Integer amorENum;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 展延種類細項（lnf033_def_type_s）	 select * from com.bcodetype where codetype='L140S02E_adjKind_s'  */	
	@Column(name = "ADJKIND_S", length = 1, columnDefinition = "CHAR(1)")
	private String adjKind_s;
	
	/** 災害種類（lnf033_def_disastp）	 select * from com.bcodetype where codetype='L140S02E_adjDisasTp'  */
	@Column(name = "ADJDISASTP", length = 2, columnDefinition = "CHAR(2)")
	private String adjDisasTp;
	
	//按 109.4.28兆銀總授審字第1090021405號函，除了 (1)利害關係人、(2)核決層級屬「常董會」或「董事會」、(3)屬授信異常通報或列管案件  其餘案件皆放寬由 營業單位主管 核定
	@Column(name = "NOTBAILOUTTERM1", length = 1, columnDefinition = "CHAR(1)")
	private String notBailoutTerm1;
	
	@Column(name = "NOTBAILOUTTERM2", length = 1, columnDefinition = "CHAR(1)")
	private String notBailoutTerm2;
	
	@Column(name = "NOTBAILOUTTERM3", length = 1, columnDefinition = "CHAR(1)")
	private String notBailoutTerm3;
	
	@Column(name = "NEEDBAILOUTTERM1", length = 1, columnDefinition = "CHAR(1)")
	private String needBailoutTerm1;
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}

	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/**
	 * 取得償還方式
	 * <p/>
	 * 單選：<br/>
	 * 1.本息平均攤還(按月繳款)<br/>
	 * 2.本息平均攤還(雙週繳款)<br/>
	 * 3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 * 4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 * 5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 * 6.其他（請自行輸入）<br/>
	 * 7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 */
	public String getPayWay() {
		return this.payWay;
	}

	/**
	 * 設定償還方式
	 * <p/>
	 * 單選：<br/>
	 * 1.本息平均攤還(按月繳款)<br/>
	 * 2.本息平均攤還(雙週繳款)<br/>
	 * 3.本金平均攤還(按月繳款，每期攤還本金：元)<br/>
	 * 4.本金平均攤還(雙週繳款，每期攤還本金：元)<br/>
	 * 5.貸款本金及貸款期間全部利息自第４年起平均攤還。(921貸款專案適用)<br/>
	 * 6.其他（請自行輸入）<br/>
	 * 7.按月付息，契約到期清償本金(房貸還本週轉適用)
	 **/
	public void setPayWay(String value) {
		this.payWay = value;
	}

	/**
	 * 取得償還方式(每期攤還本金)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * ※本金平均攤還
	 */
	public BigDecimal getPayWayAmt() {
		return this.payWayAmt;
	}

	/**
	 * 設定償還方式(每期攤還本金)
	 * <p/>
	 * 單位：新台幣元<br/>
	 * ※本金平均攤還
	 **/
	public void setPayWayAmt(BigDecimal value) {
		this.payWayAmt = value;
	}

	/**
	 * 取得償還方式(其他)
	 * <p/>
	 * ※其他
	 */
	public String getPayWayOth() {
		return this.payWayOth;
	}

	/**
	 * 設定償還方式(其他)
	 * <p/>
	 * ※其他
	 **/
	public void setPayWayOth(String value) {
		this.payWayOth = value;
	}

	/**
	 * 取得前次寬限期起始年月
	 * <p/>
	 * YYYYMM
	 */
	public String getLastYM() {
		return this.lastYM;
	}

	/**
	 * 設定前次寬限期起始年月
	 * <p/>
	 * YYYYMM
	 **/
	public void setLastYM(String value) {
		this.lastYM = value;
	}

	/** 取得前次寬限期(起) **/
	public Integer getExtFrom() {
		return this.extFrom;
	}

	/** 設定前次寬限期(起) **/
	public void setExtFrom(Integer value) {
		this.extFrom = value;
	}

	/** 取得前次寬限期(迄) **/
	public Integer getExtEnd() {
		return this.extEnd;
	}

	/** 設定前次寬限期(迄) **/
	public void setExtEnd(Integer value) {
		this.extEnd = value;
	}

	/** 取得截至前次剩餘之寬限期 **/
	public Integer getOverTerm() {
		return this.overTerm;
	}

	/** 設定截至前次剩餘之寬限期 **/
	public void setOverTerm(Integer value) {
		this.overTerm = value;
	}

	/**
	 * 取得本次是否有寬限期
	 * <p/>
	 * Y/N
	 */
	public String getNowExtend() {
		return this.nowExtend;
	}

	/**
	 * 設定本次是否有寬限期
	 * <p/>
	 * Y/N
	 **/
	public void setNowExtend(String value) {
		this.nowExtend = value;
	}

	/** 取得本次寬限期(起) **/
	public Integer getNowFrom() {
		return this.nowFrom;
	}

	/** 設定本次寬限期(起) **/
	public void setNowFrom(Integer value) {
		this.nowFrom = value;
	}

	/** 取得本次寬限期(迄) **/
	public Integer getNowEnd() {
		return this.nowEnd;
	}

	/** 設定本次寬限期(迄) **/
	public void setNowEnd(Integer value) {
		this.nowEnd = value;
	}

	/** 取得本次寬限期(餘) **/
	public Integer getNowTerm() {
		return this.nowTerm;
	}

	/** 設定本次寬限期(餘) **/
	public void setNowTerm(Integer value) {
		this.nowTerm = value;
	}

	/** 取得本次寬限期(％) **/
	public Integer getNowTermRate() {
		return this.nowTermRate;
	}

	/** 設定本次寬限期(％) **/
	public void setNowTermRate(Integer value) {
		this.nowTermRate = value;
	}

	/**
	 * 取得是否展延
	 * <p/>
	 * Y/N
	 */
	public String getAdjCheck() {
		return this.adjCheck;
	}

	/**
	 * 設定是否展延
	 * <p/>
	 * Y/N
	 **/
	public void setAdjCheck(String value) {
		this.adjCheck = value;
	}

	/**
	 * 取得展延種類
	 * <p/>
	 * 單選：<br/>
	 * 1.921展延<br/>
	 * 2.非志願性勞工展延
	 */
	public String getAdjKind() {
		return this.adjKind;
	}

	/**
	 * 設定展延種類
	 * <p/>
	 * 單選：<br/>
	 * 1.921展延<br/>
	 * 2.非志願性勞工展延
	 **/
	public void setAdjKind(String value) {
		this.adjKind = value;
	}

	/**
	 * 取得展延項目
	 * <p/>
	 * 單選：<br/>
	 * 1.本金展延<br/>
	 * 2.本金及利息展延
	 */
	public String getAdjItem() {
		return this.adjItem;
	}

	/**
	 * 設定展延項目
	 * <p/>
	 * 單選：<br/>
	 * 1.本金展延<br/>
	 * 2.本金及利息展延
	 **/
	public void setAdjItem(String value) {
		this.adjItem = value;
	}

	/**
	 * 取得本金展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 */
	public Integer getAdjCapSNum() {
		return this.adjCapSNum;
	}

	/**
	 * 設定本金展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 **/
	public void setAdjCapSNum(Integer value) {
		this.adjCapSNum = value;
	}

	/** 取得本金展延截止期 **/
	public Integer getAdjCapENum() {
		return this.adjCapENum;
	}

	/** 設定本金展延截止期 **/
	public void setAdjCapENum(Integer value) {
		this.adjCapENum = value;
	}

	/**
	 * 取得利息展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 */
	public Integer getAdjIntSNum() {
		return this.adjIntSNum;
	}

	/**
	 * 設定利息展延起始期
	 * <p/>
	 * ※(展延期間最長不得超過兩年)
	 **/
	public void setAdjIntSNum(Integer value) {
		this.adjIntSNum = value;
	}

	/** 取得利息展延截止期 **/
	public Integer getAdjIntENum() {
		return this.adjIntENum;
	}

	/** 設定利息展延截止期 **/
	public void setAdjIntENum(Integer value) {
		this.adjIntENum = value;
	}

	/** 取得應收利息攤還截止期 **/
	public Integer getAmorENum() {
		return this.amorENum;
	}

	/** 設定應收利息攤還截止期 **/
	public void setAmorENum(Integer value) {
		this.amorENum = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public String getAdjKind_s() {
		return adjKind_s;
	}

	public void setAdjKind_s(String adjKind_s) {
		this.adjKind_s = adjKind_s;
	}

	public String getAdjDisasTp() {
		return adjDisasTp;
	}

	public void setAdjDisasTp(String adjDisasTp) {
		this.adjDisasTp = adjDisasTp;
	}

	public String getNotBailoutTerm1() {
		return notBailoutTerm1;
	}

	public void setNotBailoutTerm1(String notBailoutTerm1) {
		this.notBailoutTerm1 = notBailoutTerm1;
	}

	public String getNotBailoutTerm2() {
		return notBailoutTerm2;
	}

	public void setNotBailoutTerm2(String notBailoutTerm2) {
		this.notBailoutTerm2 = notBailoutTerm2;
	}

	public String getNotBailoutTerm3() {
		return notBailoutTerm3;
	}

	public void setNotBailoutTerm3(String notBailoutTerm3) {
		this.notBailoutTerm3 = notBailoutTerm3;
	}

	public String getNeedBailoutTerm1() {
		return needBailoutTerm1;
	}

	public void setNeedBailoutTerm1(String needBailoutTerm1) {
		this.needBailoutTerm1 = needBailoutTerm1;
	}	
}
