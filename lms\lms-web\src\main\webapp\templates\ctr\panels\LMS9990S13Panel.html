<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body><!--L999M01A--><!--L999S02A-->
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
				require(['pagejs/ctr/LMS9990M03Page'], function(){
					loadScript('pagejs/ctr/LMS9990S13Panel');
				});
			</script>
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'L999M01AM03.title01'}"><!--一般條款--></th:block></b>
                    </legend>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS13.title01'}"><!--  保證期間--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01AS13.content01'}"><!--  自民國--></th:block>
									<input type="text" name="guaSDateY" id="guaSDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
									<input type="text" name="guaSDateM" id="guaSDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
									<input type="text" name="guaSDateD" id="guaSDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>&nbsp;
                                	<th:block th:text="#{'L999M01AS13.content02'}"><!--  起至民國--></th:block>
									<input type="text" name="guaEDateY" id="guaEDateY" size="3" maxlength="3" integer="3"  class="numeric" onblur="addommon(this,3);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.year'}"><!--  年--></th:block>
									<input type="text" name="guaEDateM" id="guaEDateM" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.month'}"><!--  月--></th:block>
									<input type="text" name="guaEDateD" id="guaEDateD" size="2" maxlength="2" integer="2"  class="numeric" onblur="addommon(this,2);" />
                                	<th:block th:text="#{'L999M01ASCOMMON.date'}"><!--  日--></th:block>
                                	<th:block th:text="#{'L999M01AS13.content03'}"><!--  止--></th:block>
                                </td>
                            </tr>
                            <tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS13.title02'}"><!--  資料保密--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
                                	<th:block th:text="#{'L999M01ASCOMMON.content04'}"><!--  立約人--></th:block>
									<label><input type="radio" id="dataUseFlagY" name="dataUseFlag" value="Y"/>
                                	<th:block th:text="#{'L999M01ASCOMMON.content02'}"><!--  同意--></th:block></label>&nbsp;
									<label><input type="radio" id="dataUseFlagN" name="dataUseFlag" value="N"/>
                                	<th:block th:text="#{'L999M01ASCOMMON.content03'}"><!--  不同意--></th:block></label>&nbsp;
                                	<th:block th:text="#{'L999M01AS13.content04'}"><!--  銀行得將連保人帳務、信用、投資及保險等資料，在合於營業登記項目或基於業務需要並遵守銀行所屬之金融控股公司及其各子公司(同意者請勾選)：--></th:block>
									<br/>
                                	<input type="checkbox" id="dataUseItem" name="dataUseItem"/>
                                </td>
                            </tr>
                            <tr>
                                <td width="35%" class="hd1">
                                  <th:block th:text="#{'L999M01AS13.title03'}"><!-- 管轄法院--></th:block>&nbsp;&nbsp;
                                </td>
                                <td width="65%">
									<select id="courtCode" name="courtCode" comboKey="taiwancourt" combotype="2" space="true"></select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </fieldset>
		</th:block>
    </body>
</html>
