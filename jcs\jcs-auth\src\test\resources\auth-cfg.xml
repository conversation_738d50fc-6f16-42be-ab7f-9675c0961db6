<?xml version="1.0" encoding="UTF-8"?>
<member>
	<system>C</system>
	<table>
		<department>ELSBRN</department>
		<user>ELSUSR</user>
		<role>ELSRLE</role>
		<user-role>ELSUSRR</user-role>
		<role-auth>ELSRLF</role-auth>
		<code>ELSPGM</code>
	</table>
	<query>
		<user>
			select ID, NAME, POSITION AS POS, BRANCH AS DEPT
   			from ${table.user}
   			where DATASTU='3'
   			order by ID
		</user>
		<department>
			select TYPE, <PERSON>ANCH AS DEPT, NAME
   			from ${table.department}
   			order by TYPE, <PERSON><PERSON>CH
		</department>
		<user-role>
			select ID, ROLCODE AS ROLE
			from ${table.user-role}
			where DATASTU='3' and TYPE='${system}'
			  and current date between WORKSDT and WORKEDT
			  and current date not between STOPSDT and STOPEDT
		</user-role>
		<role-auth>
			select
			  rle.ROLCODE AS ROLE,
			  rlf.<PERSON><PERSON>CODE AS AUTHCODE,
			  rlf.<PERSON>GMAUTH AS AUTH
			from ${table.role} rle
			  inner join ${table.role-auth} rlf
			  on rlf.DATASTU='3' and rlf.ROLCODE=rle.ROLCODE
			where rle.DATASTU='3' and rle.TYPE='${system}' and rle.STU='0'
			order by rle.ROLCODE, rlf.PGMCODE
		</role-auth>
		<code>
			select *
			from (
				select
				  TYPE AS STEP,
				  PGMCODE AS CODE,
				  SEQ,
				  PGMTYP AS PARENT
				from ${table.code}
				where SYSTYP='${system}'
			) S
			order by STEP, PARENT, SEQ
		</code>
	</query>
</member>