package com.mega.eloan.lms.las.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;

import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L192M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.las.service.LMS1935Service;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L192M01A;

/**
 * 稽核室 稽核工作底稿 介面 實作
 * 
 * <AUTHOR>
 * 
 */
@Service
public class LMS1935ServiceImpl extends AbstractCapService implements
		LMS1935Service {
	@Resource
	L192M01ADao l192m01aDao;
	
	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	L120S01BDao l120s01bDao;
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.las.service.LMS1935Service#get1935V01(tw.com.iisi.
	 * cap.dao.utils.ISearch)
	 */
	@Override
	public Page<L192M01A> get1935V01(ISearch search) {
		return l192m01aDao.findPage(search);
	}
	
	@Override
	public List<Map<String, Object>> findPrintL140M01AByOidForLMS1935(String oid) {
		// 判斷該OID下的L192S01A是否有額度序號
		List<Map<String, Object>> l129501aList = eloandbBASEService.checkL192S01AHavaCntrNoForLMS1935(oid);
		
		if(l129501aList == null || l129501aList.isEmpty()){
			// L192S01A沒有額度序號，從人出發去串額度明細
			return eloandbBASEService.findPrintL140M01AByOidForLMS1935WithoutCntrNo(oid);
		}else{
			// L192S01A有額度序號，從這些額度序號出發去串額度明細
			return eloandbBASEService.findPrintL140M01AByOidForLMS1935(oid);
		}
	}
	
	@Override
	public List<L120S01B> findL120s01bByMainId(String mainId) {
		// 透過MainId取得多筆資料
		return l120s01bDao.findByMainId(mainId);
	}
}
