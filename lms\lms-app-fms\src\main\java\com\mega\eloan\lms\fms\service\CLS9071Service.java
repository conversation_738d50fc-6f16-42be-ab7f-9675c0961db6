package com.mega.eloan.lms.fms.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import jxl.write.WriteException;
import tw.com.iisi.cap.service.ICapService;

public interface CLS9071Service extends ICapService {
	public void genExcel(ByteArrayOutputStream outputStream, String grpCntrNo)
			throws IOException, WriteException;

	public void genExcel_J_107_0046(ByteArrayOutputStream outputStream, boolean includeOnTheWay, String raw_brNo, String custId, String grpCntrNo, String rptNo,
			String p_dataYM1, String c_dataYM1, String c_dataYM2, boolean isAUDIT)
	throws IOException, WriteException;
	
	
	public List<Map<String, Object>> get_J_107_0046ForAudit(boolean includeOnTheWay, String raw_brNo, String custId, String grpCntrNo, String rptNo,
			String p_dataYM1, String c_dataYM1, String c_dataYM2, boolean isAUDIT);
}
