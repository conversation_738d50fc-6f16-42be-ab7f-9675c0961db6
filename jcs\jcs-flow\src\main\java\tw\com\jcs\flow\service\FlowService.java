package tw.com.jcs.flow.service;

import java.util.Map;

import tw.com.jcs.flow.FlowDefinition;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.node.StateNode;
import tw.com.jcs.flow.query.Query;

/**
 * 流程執行服務
 * 
 * <AUTHOR> Software Inc.
 */
public interface FlowService {

    /**
     * 以指定的ID建立一個新的流程
     * 
     * @param defName
     *            流程定義名稱
     * @param id
     *            流程實體的ID
     * @param userId
     *            指定成員
     * @param deptId
     *            指定部門
     * @return 新的流程實體
     */
    FlowInstance start(String defName, Object id, String userId, String deptId);

    /**
     * 
     * @param defName
     *            流程定義名稱
     * @param userId
     *            指定成員
     * @param deptId
     *            指定部門
     * @return 新的流程實體
     */
    FlowInstance start(String defName, String userId, String deptId);

    /**
     * 建立一個新的流程
     * 
     * @param defName
     *            流程定義名稱
     * @param id
     *            流程實體的ID
     * @param userId
     *            指定成員
     * @param deptId
     *            指定部門
     * @param key
     *            key值
     * @param value
     *            value值
     * @return 新的流程實體
     */
    FlowInstance start(String defName, Object id, String userId, String deptId, String key, Object value);

    /**
     * 建立一個新的流程
     * 
     * @param defName
     *            流程定義名稱
     * @param id
     *            流程實體的ID
     * @param userId
     *            指定成員
     * @param deptId
     *            指定部門
     * @param datas
     *            資料
     * @return
     */
    FlowInstance start(String defName, Object id, String userId, String deptId, Map<String, Object> datas);

    /**
     * 取消一個流程的執行
     * 
     * @param id
     *            流程編號
     */
    void cancel(Object id);

    /**
     * 建立新的流程查詢物件
     * 
     * @return
     */
    Query createQuery();

    /**
     * 取得可執行下一狀態節點<br/>
     * 若下一流程為FORK或END，則回傳null<br/>
     * (END節點不可執行，FORK節點則無法確定下一流程)
     * 
     * @param instance
     *            流程實體
     * @return
     */
    StateNode getNextState(FlowInstance instance);

    /**
     * 判斷目前等待的子流程是否都已結束
     * 
     * @param instance
     *            流程實體
     * @return
     */
    boolean isSubProcessFinished(FlowInstance instance);

    /**
     * 判斷是否已經有對應的流程
     * 
     * @param id
     *            流程編號
     * @return true or false
     */
    boolean existsFlow(Object id);

    /**
     * 取得流程定義檔
     * 
     * @param defName
     *            流程定義檔名稱
     * @return FlowDefinition
     */
    FlowDefinition getDefinition(String defName);

}
