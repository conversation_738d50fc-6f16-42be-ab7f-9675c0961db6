/* 
 *ObsdbELF383Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;

/**
 * <pre>
 * 授信額度共用檔ELF025
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF025Service {

	void insert(String BRNID, String ELF025_SDATE, String ELF025_MAINID,
			String ELF025_BRANCH, String ELF025_CASEDATE,
			String ELF025_DOCUMENT_NO, String ELF025_CONTRACT_CO,
			String ELF025_CONTRACT, String ELF025_SWFT,
			BigDecimal ELF025_FACT_AMT, String ELF025_UPDATER,
			BigDecimal ELF025_TMESTAMP);

	void deleteByMainId(String BRNID, String mainId);

}
