/* 
 * EjcicService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.ejcic.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 聯徵查詢Server
 * </pre>
 * 
 * @since 2012/10/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/23,Fantasy,new
 *          </ul>
 */
public interface EjcicService {

	/**
	 * 判斷要採用 P7 或 P9
	 * 
	 * @param id
	 * @return
	 */
	String get_cls_PRODID(String id);

	List<Map<String, Object>> get_mis_datadate_records(String id, String prodId);

	/**
	 * MIS.DATADATE 資料日期&查詢日期
	 * 
	 * @param id
	 * @param prodId
	 * @return
	 */
	Map<String, Object> getDate(String id, String prodId);

	/**
	 * MIS.DATADATE 資料日期
	 * 
	 * @param id
	 * @return
	 */
	// String getDataDate(String id);

	/**
	 * MIS.AAS003 資料查詢日期
	 * 
	 * @param id
	 * @return
	 */
	String getAAS003QDate(String id, String prodId);

	/**
	 * MIS.BAM095 聯徵查詢月份前一月(或兩個月)之無擔保授信餘額(仟元)
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getD07_G_V_1_3(String id, String prodId, String qDate);

	Map<String, Object> getD07_G_V_2_0(String id, String prodId, String qDate);

	/**
	 * MIS.AAS003 查詢日之相關日期
	 * 
	 * @param id
	 * @return
	 */
	// Map<String, Object> getAAS003Date(String id);

	/**
	 * MIS.KRM040 近6個月平均的月信用卡循環信用
	 * 
	 * @param id
	 * @param prodId
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	Map<String, Object> getKRM040AvgRev_G_V_1_3(String id, String prodId,
			String qDate, String lowDate, String highDate);

	/**
	 * MIS.STM022 12個月新業務申請查詢總家數
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @param tGetQdate
	 * @return
	 */
	List<Map<String, Object>> getN06_G_V_1_3(String id, String prodId,
			String qDate, String tGetQdate, String lowDate, String highDate);

	Map<String, Object> getN06_Q_V_3_0(String id, String prodId, String qDate);

	List<Map<String, Object>> getSTM022_N18_data(String id, String prodId,
			String qDate);

	/**
	 * N18_INQ12_BY30D 因子
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getN18_INQ12_BY30D(String id, String prodId,
			String qDate);
	/**
	 * N22_INQ12_BY30D 因子
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getN22_INQ12_BY30D(String id, String prodId,
			String qDate);
	/**
	 * MIS.STM022 近一年內不含查詢當日非Z類被聯行查詢紀錄明細
	 * 
	 * @param id
	 * @return
	 */
	List<Map<String, Object>> getSTM022Data_isQdata14(String id, String prodId,
			String qDate);

	/**
	 * MIS.KRM040 近12個月信用卡(每筆)循環信用平均使用率
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getR01_G_1_3(String id, String prodId, String qDate);

	/** 非房貸 3.0版 R01 因子 */
	Map<String, Object> getR01_Q_3_0(String id, String prodId, String qDate);

	/**
	 * MIS.KRM040 聯徵查詢月份前一月之信用卡循環信用使用率
	 * 
	 * @param id
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getKRM040RevolRatio_G_V_1_3(String id, String prodId,
			String qDate);

	/**
	 * MIS.KRM040 聯徵查詢月份and前一月之信用卡循環信用(最新一期)
	 * 
	 * @param id
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getKRM040getRevolBalByDebtRate(String id,
			String prodId, String qDate);

	/**
	 * MIS.KRM040 近6個月信用卡繳款狀況出現全額繳清無延遲次數
	 * 
	 * @param id
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	// Map<String, Object> getKRM040SixPcodeAtimes(String id, String lowDate,
	// String highDate);

	/**
	 * MIS.KRM040 近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數
	 * 
	 * @param id
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	// Map<String, Object> getKRM040NotPcodeAtimes(String id, String lowDate,
	// String highDate);

	/**
	 * MIS.KRM040 近12個月信用卡繳款狀況出現全額繳清無延遲次數
	 * 
	 * @param id
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	// Map<String, Object> getKRM040PcodeAtimes(String id, String lowDate,
	// String highDate);

	/**
	 * 近6個月信用卡繳款狀況出現全額繳清無延遲次數
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getP25_V_1_3(String id, String prodId, String qDate);

	Map<String, Object> getP25_V_2_0(String id, String prodId, String qDate);

	/**
	 * MIS.KRM001 強停註記
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getKRM001StopCreditCard(String id, String prodId,
			String qDate);

	/**
	 * MIS.BAM101 授信額度
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getBAM101CollectionLog(String id, String prodId,
			String qDate);

	/**
	 * MIS.BAM101 逾期金額 PASS_DUE_AMT
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getBAM101PassDueAmt(String id, String prodId,
			String qDate);
	
	/**
	 * MIS.VAM106 消債條例信用註記資訊
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getVAM106Data(String id, String prodId, String qDate);

	/**
	 * MIS.VAM107 銀行公會消金案件債務協商補充註記
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getVAM107Data(String id, String prodId, String qDate);

	/**
	 * MIS.VAM108 非屬消債條例及銀行公會債務協商之註記資訊
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getVAM108Data(String id, String prodId, String qDate);

	/**
	 * MIS.BAM087 近12個月授信帳戶有遲延還款紀錄
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getBAM087DelayPayLoan(String id, String prodId,
			String qDate);

	/**
	 * MIS.KRM040 近12個月信用卡繳款狀況出現(循環信用有延遲)2次(含)以上
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	Map<String, Object> getKRM040CardPayCode2(String id, String prodId,
			String qDate, String lowDate, String highDate);

	/**
	 * MIS.KRM040 近12個月信用卡繳款狀況出現(未繳足最低金額)2次(含)以上
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	Map<String, Object> getKRM040CardPayCode3(String id, String prodId,
			String qDate, String lowDate, String highDate);

	/**
	 * MIS.KRM040 近12個月信用卡繳款狀況出現(全額逾期未繳)2次(含)以上
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @param lowDate
	 * @param highDate
	 * @return
	 */
	List<Map<String, Object>> getKRM040CardPayCode4(String id, String prodId,
			String qDate, String lowDate, String highDate);

	/**
	 * MIS.KRM040 近12個月信用卡有預借現金餘額家數2家含
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getKRM040CashAdvance(String id, String prodId,
			String qDate);

	/**
	 * MIS.BAM087 近12個月現金卡有動用紀錄
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getBAM087CashCard(String id, String prodId, String qDate);

	/**
	 * MIS.BAM087 個人主債務資料
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	List<Map<String, Object>> getBAM087Data(String id, String prodId,
			String qDate);

	/**
	 * MIS.BAM087 查詢申貸戶 所有逾期呆帳紀錄筆數
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getBAM087CollectionInfo1(String id, String prodId,
			String qDate);

	/**
	 * MIS.BAM303 查詢所有董監事的從債務 逾期呆帳紀錄筆數
	 * 
	 * @param id
	 * @return
	 */
	// Map<String, Object> getBAM303CollectionInfo1(String id);

	/**
	 * MIS.KRM001 信用卡被強停查詢
	 * 
	 * @param id
	 * @return
	 */
	// Map<String, Object> getKRM001CreditData2(String id);

	/**
	 * MIS.CPXQueryLog
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	List<Map<String, Object>> getCPXQueryLogHtml(String id, String prodId,
			String qDate);

	/**
	 * 取得用途別
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	String getPurposeCode(String id, String prodId, String qDate);

	/**
	 * 近12個月信用卡繳款狀況出現不良繳款紀錄或使用循環信用的次數 近12個月信用卡繳款狀況出現全額繳清無延遲次數
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getP69P19(String id, String prodId, String qdate);

	/**
	 * 聯徵查詢月份當時無擔保授信往來家數(排除學生助學貸款(Z)、本行貸款)
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getD63LnNosBank(String id, String prodId, String qDate);

	/**
	 * 近6個月信用卡使用循環信用月份數(含查詢當月之畸零月)
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getA21Cc6RcUseMonth_Q_V_1_0(String id, String prodId,
			String qDate);

	/**
	 * 近6個月信用卡使用循環信用家數(含查詢當月之畸零月)
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getAllCc6RcUseBank_Q_V_1_0(String id, String prodId,
			String qDate);

	/**
	 * 聯徵查詢月份當月授信繳款記錄小於等於6次旗標(不含本行)
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	Map<String, Object> getD53Ln6TimesFlag(String id, String prodId,
			String qDate);

	Map<String, Object> getAllCc6RcUseBank_V2_0(String id, String prodId,
			String qDate);

	/**
	 * 房貸、非房貸的 P19 在判斷 PAY_CODE 的條件不同。所以P68P19因子拆2個
	 * 
	 * @param id
	 * @param prodId
	 * @param qdate
	 * @return
	 */
	Map<String, Object> getP68P19_G_2_0(String id, String prodId, String qdate);

	Map<String, Object> getP68P19_Q_2_0(String id, String prodId, String qdate);

	Map<String, Object> getC101M01A_JcicFlag(String id, String prodId,
			String qdate);

	Map<String, Object> getKRM040_revol_cnt(String id, String prodId,
			String qdate);

	List<Map<String, Object>> getKRM040_data(String id, String prodId,
			String qdate);

	Map<String, Object> getZ03(String id, String prodId, String qdate);

	/**
	 * 判斷 Q135, Q128 ,Q116 (這個可能沒有) 的RETCODE 若不是 0000，就是失敗的DATA
	 */
	List<Map<String, Object>> getClsRecordLOGFILE(String id, String dupNo,
			String prodId);

	List<Map<String, Object>> getBAM095_data(String id, String prodId,
			String qdate);
	/**
	 * 取得BAM095擔保品類別
	 * 
	 * @param id
	 * @param prodId
	 * @param qdate
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> getBAM095_IS_KIND(String id, String prodId,
			String qdate);

	List<Map<String, Object>> getKCS003_data_ordByQdateDesc(String id);

	// DAM001:B36大額退票 , DAM003:勞工紓困小額退票
	Map<String, String> get_DAM001_DAM003_relateData(String custId);

	Map<String, String> get_DAM001_DAM003_relateData_debug(String custId);

	/**
	 * 取得最新一筆查詢紀錄 (查詢借款人or 保證人授信資訊)
	 * 
	 * @param id
	 * @return PROD_ID, QUERY_PERSON_ID, QUERY_PERSON_BRANCHNO, QUERY_DATE
	 */
	public Map<String, Object> getLatestQueryRecordOfCreditInfoById(String id);

	public Map<String, Object> getLatestQueryRecordOfCreditInfoByIdInP7P9(
			String id);

	public int getHousingLoanCountByBAM095(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate);

	public boolean isEjcicDataQueryTimesMoreThan2ByOtherBank(String id,
			String prodId, String date, int itemCode_P_threshold);

	public List<Map<String, Object>> getBAM095Data(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate);

	public List<Map<String, Object>> getBAM305Data(String id, String prodId,
			String queryPersonId, String queryPersonBranchNo, String queryDate);

	public Map<String, Object> getEarliestIssueDateOfUnsuspensionCreditCard(
			String id);

	public Map<String, Object> getKRM046Data_getNotStopMegaCard_Min_StartDate(
			String id);

	public Map<String, Object> getLabourInsuredCount(String custId,
			String queryBranch);

	public Map<String, Object> getLabourInsuredAmountMoreThan23800Count(
			String custId, String queryBranch);

	public Map<String, Object> getStopCardDataOfCreditCard(String custId,
			String prodId, String queryDate);

	public Map<String, Object> getIdChangedRecord(String custId, String prodId,
			String queryDate);

	public Map<String, Object> getOtherSupplementaryNoteInfo(String custId,
			String prodId, String queryDate);

	public Map<String, Object> getCaseNotifiedRecord(String custId,
			String prodId, String queryDate);

	public List<Map<String, Object>> getForcedStopCardDataByStopCode(
			String custId, String prodId, String queryDate);

	public Map<String, String> getPaymentSituationOfBillInLatestMonthByBankCode(
			String custId, String prodId, String queryDate);

	public Map<String, Object> getLatePaymentRecordOfCreditOver30Days(
			String custId, String queryDate);

	public Map<String, Object> getAnnualIncomeIsLessThan50WanIn108Year(
			String custId);

	public Map<String, Object> getOverdueCollectionAndBadDebtData(
			String custId, String prodId, String queryDate);

	public Map<String, Object> getAbnormalCreditRecord(String custId,
			String prodId, String queryDate);

	public Map<String, Object> getVAM106DataExceptMainCodeD(String id,
			String prodId, String qDate);

	public Map<String, Object> getVAM107DataExceptMainCodeD(String id,
			String prodId, String qDate);

	/**
	 * J-110-0314 利率方案 24-整批房貸400億成長專案中，篩選案件增加檢查 主債務:BAM095
	 * 篩選主債務授信科目別有S且擔保品類別25、27、2A、2B者
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	public Map<String, Object> getBAM095CheckAccountCode2HaveS(String id,
			String prodId, String qDate);

	/**
	 * J-110-0314 利率方案 24-整批房貸400億成長專案中，篩選案件增加檢查 共用債務:BAM305 從債務授信科目別S者
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	public Map<String, Object> getBAM305CheckAccountCode2HaveS(String id,
			String prodId, String qDate);

	/**
	 * J-110-0314 利率方案 24-整批房貸400億成長專案中，篩選案件增加檢查 BAM306 從債務授信科目別S者
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	public Map<String, Object> getBAM306CheckAccountCode2HaveS(String id,
			String prodId, String qDate);

	/**
	 * 查詢最近一次產品組合查詢紀錄且查詢理由是本人同意
	 */
	public List<Map<String, Object>> getZAM003RQueryRecord(String custId,
			String bgnDate, String endDate, String queryItem);

	public Map<String, Object> getBam095_BuyingRealEstateAndCollateralInSpecificTypeData(String id, String prodId, String qDate);

	public Map<String, Object> getKRM040CreditCardCashLent(String id, String qDate, String prodId);

	public Map<String, Object> getBAM087CashCardLoan(String id, String qDate,String prodId);
	
	/**
	 * 退票異常紀錄
	 * @param id
	 * @param prodId
	 * @param qdate
	 * @return
	 */
	List<Map<String, Object>> getDAS001_data(String id, String prodId,
			String qdate);
	
	/**
	 * 拒往紀錄
	 * @param id
	 * @param prodId
	 * @param qdate
	 * @return
	 */
	List<Map<String, Object>> getDAS002_data(String id, String prodId,
			String qdate);
	
	/**
	 * MIS.VAM106 消債條例信用註記資訊多筆
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	List<Map<String, Object>> getVAM106DataList(String id, String prodId, String qDate);

	/**
	 * MIS.VAM107 銀行公會消金案件債務協商補充註記多筆
	 * 
	 * @param id
	 * @param prodId
	 * @param qDate
	 * @return
	 */
	List<Map<String, Object>> getVAM107DataList(String id, String prodId, String qDate);
	
	public List<Map<String, Object>> getKRM046Data(String id);

	public List<Map<String, Object>> getEjcicS11Data(String reqId, String reqBranchNo, String id);

	public Map<String, Object> getLatestLogFileByTxId(String custId, String txId);

	public List<Map<String, Object>> getBam029NewApprovedQuotaByOtherBank(String id, String prodId, String qdate);

	public Map<String, Object> getLatestLogFileByTxIdAndQdate(String custId, String txId, String qDate);

	public Map<String, Object> getLatestLogFileByQKey1_txId_toJcic(String custId, String txId);

	public List<Map<String, Object>> getEjcicQueryRecoredByOtherBank(String custId, String txId, String qDate);

	public List<Map<String, Object>> getNewQuotaOrRepaymentInfo(String custId, String txId, String qDate);

	public Map<String, Object> getLatestLogFileByQkey1AndProdId(String custId, String prodId);
	
	/**
	 * 消金房貸3.0
	 */
	public Map<String, Object> getD42_G_3_0(String id, String prodId, String qdate);
	public Map<String, Object> getN01_G_3_0(String id, String prodId, String qdate);
	public Map<String, Object> getR01_G_3_0(String id, String prodId, String qDate);
	
	public Map<String, Object> getKRM040_MaxQdate(String id);
	/**
	 * 消金非房貸4.0
	 */
	public Map<String, Object> getP01_Q_V_4_0(String id, String prodId, String qDate);
	public Map<String, Object> getR01_Q_V_4_0(String id, String prodId, String qDate);

	/**
	 * 取得DAM001 主體連帶退票摘要紀錄(含外幣資訊)
	 * @param id
	 * @param prodid
	 * @param qdate
	 * @return
	 */
	List<Map<String, Object>> _get_B36_etch_detailData(String id, String prodid, String qdate);
	
	public Map<String, Object> getKCS003DataBy(String id, String prodId, String qDate);

	public List<Map<String, Object>> getP7P9HtmlData(String custId, String prodId);

	public Map<String, Object> getVAM106DataExceptMainCode29DF(String id, String prodId, String qDate);

	public Map<String, Object> getVAM107DataExceptMainCode29DF(String id, String prodId, String qDate);

	public Map<String, Object> getVAM108DataExceptMainCode29DF(String id, String prodId, String qDate);
	
	public List<Map<String, Object>> getKRM040CardPayDelayCountsByBank(String id, String prodId,
			String qDate, String lowDate, String highDate);
	
	public List<Map<String, Object>> getKRM040CardPayDelayDataByBank(String id, String prodId,
			String qDate, String lowDate, String highDate,String issue);
	
	public List<Map<String, Object>> getKRM040CardPayLatestDateByBank(String id, String prodId,	String qDate);
	
	public Map<String, Object> getKRM040CardPayLatestDataUseRevolByBank(String id, String prodId, String qDate,
			String billDate, String issue);

	public Map<String, Object> getBAM095AllBankAmtData(String id, String prodId, String qDate);
	
	public Map<String, Object> getBAM095Bank017AmtData(String id, String prodId, String qDate);
	
	public List<Map<String, Object>> getBAM305LoanAmtPassDueAmtByBank(String id, String prodId,	String qDate);
	
	public List<Map<String, Object>> getBAM306LoanAmtPassDueAmtByBank(String id, String prodId,	String qDate);
		
	public Map<String, Object> getKRM040CardPayAbnormalCount(String id, String prodId, String qDate);

	public Map<String, Object> getBAM305PassDueAmtOverZeroCount(String id, String prodId, String qDate);
	
	/**
	 * <p>
	 * 金融機構透過財團法人金融聯合徵信中心介接公務機關資料平台
	 * </p>
	 * <ul>
	 * <li>T50財政部個人最新年度所得資料</li>
	 * <li>T51財政部個人財產資料</li>
	 * <li>T52勞動部職業投保資料</li>
	 * <li>T53交通部駕籍資料</li>
	 * <li>T54交通部車籍資料</li>
	 * </ul>
	 * 
	 * @param id
	 * @param queryItem
	 * @param qDate
	 * @param qBranch
	 * @return
	 */
	public Map<String, Object> findTAS500(String id, String queryItem, String qDate, String qBranch);
	
	/**
	 * <p>
	 * 金融機構透過財團法人金融聯合徵信中心介接公務機關資料平台 HTML
	 * </p>
	 * <ul>
	 * <li>T50財政部個人最新年度所得資料</li>
	 * <li>T51財政部個人財產資料</li>
	 * <li>T52勞動部職業投保資料</li>
	 * <li>T53交通部駕籍資料</li>
	 * <li>T54交通部車籍資料</li>
	 * </ul>
	 * 
	 * @param txId
	 * @param id
	 * @param qDate
	 * @param qBranch
	 * @return
	 */
	public Map<String, Object> findBT3FILEByIdQDate(String txId, String id, String qDate, String qBranch);
	
	public Map<String, Object> findTAS700ById(String id);
	
	public Map<String, Object> findBT2FILEByTxIdIdQDate(String txId, String id, String qDate);
	
	/**
	 * JCIC總行代碼對應JCIC聯徵代碼表
	 * EJF369_DEPTID 總行代碼3碼     
	 * EJF369_VDEPTID 聯徵代碼4碼
	 * @return
	 */
	public List<Map<String, Object>> findEJF369();

	public String findEJF369VDEPTID(String brId);
	
	public List<Map<String, Object>> getKRM040getRevolBalByDebtRateWithoutSum(String id,
																			  String prodId, String qDate);

	public Map<String, Object> getKrm040PermanentQuotaAndRevolvingCreditData(String id);

	public Map<String, Object> getTAS700DataBy(String id, String prodId);

	public Map<String, Object> getBT2FileDataBy(String custId, String txId, String qDate, String qempCode, String qBranch);
}
