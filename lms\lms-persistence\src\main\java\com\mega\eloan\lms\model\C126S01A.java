package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** RPA發查房仲證書字號 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C126S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C126S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	public C126S01A(){
	}
	
	public C126S01A(String mainId, String realtorName){
		this.mainId = mainId;
		this.realtorName = realtorName;
	}

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 結果回傳URL **/
	@Size(max=200)
	@Column(name="RESPONSEURL", length=200, columnDefinition="VARCHAR(200)")
	private String responseURL;
	
	/** 系統別 **/
	@Size(max=10)
	@Column(name="SYSTEM", length=10, columnDefinition="VARCHAR(10)")
	private String system;
	
	/** 識別碼 **/
	@Size(max=300)
	@Column(name="UNIQUEID", length=300, columnDefinition="VARCHAR(300)")
	private String uniqueID;
	
	/** 發查分行別 **/
	@Size(max=3)
	@Column(name="BRANCHNO", length=3, columnDefinition="CHAR(3)")
	private String branchNo;
	
	/** 發查員工編號 **/
	@Size(max=6)
	@Column(name="EMPNO", length=6, columnDefinition="CHAR(6)")
	private String empNo;
	
	/** 客戶ID **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;
	
	/** 引介房仲姓名 **/
	@Size(max=120)
	@Column(name="REALTORNAME", length=120, columnDefinition="VARCHAR(120)")
	private String realtorName;
	
	/** 流程執行結果代碼 
	 *  0:查詢成功
	 *  1:查詢失敗
	 * **/
	@Size(max=1)
	@Column(name="RESPONSECODE", length=1, columnDefinition="CHAR(1)")
	private String responseCode;
	
	/** 
	 *  N:測試環境
	 *  Y:正式環境
	 * **/
	@Size(max=1)
	@Column(name="VAILDIP", length=1, columnDefinition="CHAR(1)")
	private String vaildIp;
	
	/** 結果訊息 **/
	@Size(max=300)
	@Column(name="RESPONSEMSG", length=300, columnDefinition="VARCHAR(300)")
	private String responseMsg;
	
	/** 
	 * 回傳結果內容<p/>
	 * Json格式
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="REALTORINFO", columnDefinition="CLOB")
	private String realtorInfo;

	/** 查詢時間 **/
	@Column(name="QUERYTIME", columnDefinition="TIMESTAMP")
	private Timestamp queryTime;

	/** 文件建立者 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(06)")
	private String creator;

	/** 文件建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 資料修改人(行編) **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(06)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;
	
	/** 
	 * 回傳結果內容<p/>
	 * Json格式
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="RETURNDATA", columnDefinition="CLOB")
	private String returnData;
	
	/** 
	 * 處理狀態<p/>
	 * A01:查詢中<br/>
	 * A02:查詢完成<br/>
	 * A03:查詢失敗
	 */
	@Size(max=3)
	@Column(name="STATUS", length=3, columnDefinition="CHAR(3)")
	private String status;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getResponseURL() {
		return responseURL;
	}

	public void setResponseURL(String responseURL) {
		this.responseURL = responseURL;
	}

	public String getSystem() {
		return system;
	}

	public void setSystem(String system) {
		this.system = system;
	}

	public String getUniqueID() {
		return uniqueID;
	}

	public void setUniqueID(String uniqueID) {
		this.uniqueID = uniqueID;
	}

	public String getBranchNo() {
		return branchNo;
	}

	public void setBranchNo(String branchNo) {
		this.branchNo = branchNo;
	}

	public String getEmpNo() {
		return empNo;
	}

	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getResponseMsg() {
		return responseMsg;
	}

	public void setResponseMsg(String responseMsg) {
		this.responseMsg = responseMsg;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Timestamp getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(Timestamp queryTime) {
		this.queryTime = queryTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public String getReturnData() {
		return returnData;
	}

	public void setReturnData(String returnData) {
		this.returnData = returnData;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getRealtorName() {
		return realtorName;
	}

	public void setRealtorName(String realtorName) {
		this.realtorName = realtorName;
	}

	public String getVaildIp() {
		return vaildIp;
	}

	public void setVaildIp(String vaildIp) {
		this.vaildIp = vaildIp;
	}

	public String getRealtorInfo() {
		return realtorInfo;
	}

	public void setRealtorInfo(String realtorInfo) {
		this.realtorInfo = realtorInfo;
	}

}
