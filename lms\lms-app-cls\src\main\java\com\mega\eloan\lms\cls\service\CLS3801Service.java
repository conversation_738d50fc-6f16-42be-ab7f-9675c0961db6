package com.mega.eloan.lms.cls.service;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.lms.model.C103M01A;
import com.mega.eloan.lms.model.C103M01E;
import com.mega.eloan.common.service.AbstractService;

public interface CLS3801Service extends AbstractService {

	public void daoSave(GenericBean... entity);

	public List<C103M01A> findRptCls180r58(String ownBrId, Date rptStartDate,
			Date rptEndDate);

	public List<C103M01E> findC103M01E(String mainId);

}
