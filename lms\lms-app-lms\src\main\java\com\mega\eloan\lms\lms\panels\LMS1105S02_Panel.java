package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * 借款人基本資料(企金授權內)
 * </pre>
 * 
 * @since 2012/1/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/19,<PERSON>,new
 *          </ul>
 */
public class LMS1105S02_Panel extends Panel {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4024257163623646201L;

	public LMS1105S02_Panel(String id) {
		super(id);
		// add(new LMS1105S02Panel01("lms1105s02panel01"));
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMS1105S02Panel01("lms1105s02panel01").processPanelData(model, params);
	}
}
