package tw.com.jcs.common.report;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfCopy;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.RandomAccessFileOrArray;

import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.WriteFile;

/**
 * <pre>
 * 處理PDF合併分頁
 * </pre>
 * 
 * @since 2011/10/11
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/10/11,ice,new
 *          </ul>
 */
public class PdfTools {
    static String pdfFilePath = null;
    static String tiffFilePath = null;
    public final static String BYTEARRAY = "bytearray";
    public final static String PAGE_INDEX = "pageIndex";
    public final static String TOTAL_PAGE = "totalPage";
    // logger
    private final static Logger LOGGER = LoggerFactory.getLogger(PdfTools.class);

    /**
     * 取得ImageReader(tiff)
     * 
     * @return {@link javax.imageio.ImageReader}
     */
    private static ImageReader getTiffImageReader() {
        Iterator<ImageReader> imageReaders = ImageIO.getImageReadersByFormatName("TIFF");
        if (!imageReaders.hasNext()) {
            throw new UnsupportedOperationException("No TIFF Reader found!");
        }
        return imageReaders.next();
    }

    /**
     * 依照所有頁數分割tif檔。
     * 
     * @param inputStream
     *            檔案來源
     * @return Set<ByteArrayInputStream> set of tiffs
     * @throws Exception
     *             Exception
     * @throws CapException
     *             CapException
     */
    @SuppressWarnings("null")
    public static Set<Map<String, Object>> spiltTIFF(final InputStream inputStream) throws Exception {
        Set<Map<String, Object>> tiffs;
        RandomAccessFileOrArray rafa;
        int pages;
        Document document;
        ByteArrayOutputStream singleTiff;
        Map<String, Object> map;
        Image img;
        float width;
        float height;
        PdfWriter writer;
        PdfContentByte pcb;

        tiffs = new HashSet<Map<String, Object>>();
        rafa = new RandomAccessFileOrArray(inputStream);
        ImageReader reader = getTiffImageReader();
        try (ImageInputStream iis = ImageIO.createImageInputStream(inputStream)) {
            reader.setInput(iis);
            pages = reader.getNumImages(true);
        } catch (Exception e) {
            pages = 0;
        }
        for (int i = 1; i <= pages; i++) {
            document = new Document(PageSize.A4);
            singleTiff = new ByteArrayOutputStream();
            map = new HashMap<String, Object>();
            try {
                // Image waterImage = Image.getInstance("./yyyyy.png");
                // waterImage.setAbsolutePosition(200, 400);
                BufferedImage bufferedImage = reader.read(i);
                img = Image.getInstance(bufferedImage, null, false);
                if (img.getWidth() > img.getHeight()) {
                    document.setPageSize(PageSize.A4.rotate());
                }
                width = document.getPageSize().getWidth();
                height = document.getPageSize().getHeight();
                if (img.getDpiX() > 0 && img.getDpiY() > 0) {
                    img.scalePercent(7200f / img.getDpiX(), 7200f / img.getDpiY());
                }
                if (img.getScaledWidth() > width || img.getScaledHeight() > height) {
                    if (img.getDpiX() > 0 && img.getDpiY() > 0) {
                        final float adjx = width / img.getScaledWidth();
                        final float adjy = height / img.getScaledHeight();
                        final float adj = Math.min(adjx, adjy);
                        img.scalePercent(7200f / img.getDpiX() * adj, 7200f / img.getDpiY() * adj);
                    } else {
                        img.scaleToFit(width, height);
                    }
                }
                img.setAbsolutePosition(0, 0);
                writer = PdfWriter.getInstance(document, singleTiff);
                // writer.setPageEvent(new MyPageEventHelper(waterImage));

                document.open();
                pcb = writer.getDirectContent();
                pcb.addImage(img);
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
                throw new Exception();
            } finally {
                if (document.isOpen()) {
                    document.close();
                    document = null;
                }
                if (rafa != null) {
                    rafa.close();
                    rafa = null;
                }
                if (map != null) {
                    map.clear();
                    map = null;
                }
            }
            map.put(BYTEARRAY, singleTiff.toByteArray());
            map.put(PAGE_INDEX, i);
            tiffs.add(map);
        }
        return tiffs;
    }

    /**
     * 預設不分割pdf檔案。
     * 
     * @param inputStream
     *            input PDF file
     * @param rotateFlag
     *            rotate flag
     * @return Set<ByteArrayInputStream> set of Pdfs
     * @throws CapException
     * @throws IOException
     */
    public static Set<Map<String, Object>> splitPDF(final InputStream inputStream, final boolean rotateFlag) throws Exception, IOException {
        return splitPDF(inputStream, rotateFlag, false);
    }

    /**
     * 依照所有頁數分割pdf檔。
     * 
     * @param inputStream
     *            input PDF file
     * @param rotateFlag
     *            rotate flag
     * @param needSegment
     *            need or not segment
     * @return Set<ByteArrayInputStream> set of Pdfs
     * @throws CapException
     * @throws IOException
     */
    public static Set<Map<String, Object>> splitPDF(final InputStream inputStream, final boolean rotateFlag, final boolean needSegment) throws IOException, Exception {
        Set<Map<String, Object>> pdfs = new HashSet<Map<String, Object>>();
        try {
            PdfReader inputPDF = new PdfReader(inputStream);
            int totals = inputPDF.getNumberOfPages();
            if (needSegment) {
                int i = 1;
                while (i <= totals) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    ByteArrayOutputStream singlePdf = new ByteArrayOutputStream();
                    PdfTools.splitPDF(inputPDF, singlePdf, i, i, rotateFlag, needSegment);
                    map.put(BYTEARRAY, singlePdf.toByteArray());
                    map.put(PAGE_INDEX, i);
                    pdfs.add(map);
                    i++;
                }
            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                ByteArrayOutputStream singlePdf = new ByteArrayOutputStream();
                PdfTools.splitPDF(inputPDF, singlePdf, 1, totals, rotateFlag, needSegment);
                map.put(BYTEARRAY, singlePdf.toByteArray());
                map.put(PAGE_INDEX, totals);
                pdfs.add(map);
            }
        } finally {
            if (inputStream != null)
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage());
                }
        }
        return pdfs;
    }

    /**
     * 分割pdf檔。
     * 
     * @param inputPDF
     *            Input PDF file
     * @param outputStream
     *            Output PDF file
     * @param fromPage
     *            start page from input PDF file
     * @param toPage
     *            end page from input PDF file
     */
    public static void splitPDF(final PdfReader inputPDF, final OutputStream outputStream, final int fromPage, final int toPage) throws Exception {
        splitPDF(inputPDF, outputStream, fromPage, toPage, true, true);
    }

    /**
     * 分割pdf檔。
     * 
     * @param inputPDF
     *            Input PDF file
     * @param outputStream
     *            Output PDF file
     * @param fromPage
     *            start page from input PDF file
     * @param toPage
     *            end page from input PDF file
     * @param rotateFlag
     *            rotate flag
     * @param segmentFlag
     *            segment flag
     * @throws Exception
     */
    public static void splitPDF(final PdfReader inputPDF, final OutputStream outputStream, final int fromPage, final int toPage, final boolean rotateFlag, final boolean segmentFlag) throws Exception {
        Document document = new Document(inputPDF.getPageSizeWithRotation(fromPage), 0, 0, 0, 0);
        int fromPageCh;
        int toPageCh;
        try {
            int totalPages = inputPDF.getNumberOfPages();

            // make fromPage equals to toPage if it is greater
            if (fromPage > toPage) {
                fromPageCh = toPage;
            } else {
                fromPageCh = fromPage;
            }
            if (toPage > totalPages) {
                toPageCh = totalPages;
            } else {
                toPageCh = toPage;
            }

            // Create a writer for the outputstream
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);

            document.open();
            PdfContentByte pcb = writer.getDirectContent(); // Holds the PDF
                                                            // data
            PdfImportedPage page;

            while (fromPageCh <= toPageCh) {
                if (!segmentFlag) {
                    document.newPage();
                } else {
                    document.open();
                }
                page = writer.getImportedPage(inputPDF, fromPageCh);

                int rotation = inputPDF.getPageRotation(fromPageCh);
                if (rotation == 90 && rotateFlag) {
                    pcb.addTemplate(page, 0f, -1f, 1f, 0, 0, inputPDF.getPageSizeWithRotation(fromPageCh).getHeight());
                } else if (rotation == 270 && rotateFlag) {
                    pcb.addTemplate(page, 0, 1.0F, -1.0F, 0, inputPDF.getPageSizeWithRotation(fromPageCh).getWidth(), 0);
                } else {
                    pcb.addTemplate(page, 1.0F, 0, 0, 1.0F, 0, 0);
                }
                fromPageCh++;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen()) {
                document.close();
                document = null;
            }
        }
    }

    /**
     * 將傳進來的streamOfPDFFiles串接再一起
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginate
     *            分頁
     * @throws IOException
     * @throws Exception
     */
    public static void concatPDFs(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, final boolean paginate) throws IOException, Exception {
        PdfWriter writer;
        int totalPages;
        BaseFont bfont;
        PdfContentByte pcb;
        PdfImportedPage page;
        int currentPageNumber;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        Document document = new Document(PageSize.A4);
        try {
            List<InputStream> pdfs = streamOfPDFFiles;
            List<PdfReader> readers = new ArrayList<PdfReader>();
            totalPages = 0;
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
                totalPages += pdfReader.getNumberOfPages();
            }
            // Create a writer for the outputstream
            writer = PdfWriter.getInstance(document, outputStream);

            document.open();
            bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            currentPageNumber = 0;
            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();

                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    currentPageNumber++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);

                    // Code for pagination.

                    if (paginate) {
                        pcb.beginText();

                        pcb.setFontAndSize(bfont, 9);

                        pcb.showTextAligned(PdfContentByte.ALIGN_CENTER, "" + currentPageNumber + " of " + totalPages, 520, 5, 0);
                        pcb.endText();
                    }
                }
                pageOfCurrentReaderPDF = 0;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }

    /**
     * 將傳入的files合併
     * 
     * @param files
     *            文件內容的陣列
     * @param savepath
     *            要儲存的路徑
     * @throws IOException
     * @throws DocumentException
     */
    public static void mergePdfFiles(final String[] files, final String savepath) throws IOException, DocumentException {
        Document document = null;
        try {
            if (WriteFile.exists(savepath)) {
                WriteFile.delete(savepath);
            }
            PdfReader readerForPageSize = new PdfReader(files[0]);
            document = new Document(readerForPageSize.getPageSize(1));
            readerForPageSize.close();
            PdfCopy copy = new PdfCopy(document, new FileOutputStream(savepath));
            document.open();

            for (int i = 0; i < files.length; i++) {
                PdfReader reader = new PdfReader(files[i]);
                int numberPages = reader.getNumberOfPages();
                for (int j = 1; j <= numberPages; j++) {
                    document.newPage();
                    PdfImportedPage page = copy.getImportedPage(reader, j);
                    copy.addPage(page);
                }
            }

            document.close();
        } catch (IOException e) {
            LOGGER.error(e.getMessage());
            throw new IOException();
        } catch (DocumentException e) {
            LOGGER.error(e.getMessage());
            throw new DocumentException();
        } finally {
        }
    }

    /**
     * 合併檔案
     * 
     * @param fileList
     *            要整併的檔案
     * @param savepath
     *            若未提供則由系統提供亂數路徑
     * @return String savepath
     * @throws IOException
     *             IOException
     * @throws DocumentException
     *             DocumentException
     */
    @SuppressWarnings("unused")
    public static String mergePdfFilesFiles(final List<String> fileList, final String savepath) throws IOException, DocumentException {
        Document document = null;
        File file = null;
        String[] files = new String[fileList.size()];
        String filePath = null;
        if ("".equals(Util.nullToSpace(savepath))) {
            filePath = UUID.randomUUID().toString().replace("-", "") + ".pdf";
        } else {
            filePath = savepath;
        }
        try {
            if (fileList != null) {
                for (int i = 0; i < fileList.size(); i++) {
                    files[i] = fileList.get(i);
                }
                if (WriteFile.exists(savepath)) {
                    WriteFile.newFileInstance(savepath).deleteOnExit();
                }
                PdfReader readerForPageSize = new PdfReader(files[0]);
                document = new Document(readerForPageSize.getPageSize(1));
                readerForPageSize.close();
                PdfCopy copy = new PdfCopy(document, new FileOutputStream(savepath));
                document.open();

                for (int i = 0; i < files.length; i++) {
                    PdfReader reader = new PdfReader(files[i]);
                    int numberPages = reader.getNumberOfPages();
                    for (int j = 1; j <= numberPages; j++) {
                        document.newPage();
                        PdfImportedPage page = copy.getImportedPage(reader, j);
                        copy.addPage(page);
                    }
                }

                document.close();
            } else {
                LOGGER.error("未提供檔案");
            }

        } catch (IOException e) {
            LOGGER.error(e.getMessage());
            throw new IOException();
        } catch (DocumentException e) {
            LOGGER.error(e.getMessage());
            throw new DocumentException();
        } finally {
            if (file != null) {
                file = null;
            }
        }
        return filePath;
    }

    /**
     * 合併檔案
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream) throws IOException, Exception {
        PdfWriter writer;
        PdfContentByte pcb;
        PdfImportedPage page;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        Document document = new Document(PageSize.A4);
        try {
            List<InputStream> pdfs = streamOfPDFFiles;
            List<PdfReader> readers = new ArrayList<PdfReader>();
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
            }
            // Create a writer for the outputstream
            writer = PdfWriter.getInstance(document, outputStream);
            document.open();

            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();

                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);
                    pcb.beginText();
                    pcb.endText();
                }
                pageOfCurrentReaderPDF = 0;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }
    }

    /**
     * 合併檔案
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale)
            throws IOException, Exception {
        PdfTools.mergeReWritePagePdf(streamOfPDFFiles, outputStream, paginationText, paginate, locale, 8);
    }

    /**
     * 合併檔案
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale, int subLine)
            throws IOException, Exception {
        PdfTools.mergeReWritePagePdf(streamOfPDFFiles, outputStream, paginationText, paginate, locale, subLine, true);
    }

    /**
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @param vaPrintResult
     *            橫向縱向列印 true 縱向 false 橫向
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale, int subLine,
            boolean vaPrintResult) throws IOException, Exception {
        PdfWriter writer;
        int totalPages;
        BaseFont bfont;
        PdfContentByte pcb;
        PdfImportedPage page;
        int currentPageNumber;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        // Rectangle pSize=new Rectangle(600,790);
        // Document document = new Document(pSize);
        Document document = null;
        if (vaPrintResult) {
            document = new Document(PageSize.A4);
        } else {
            document = new Document(PageSize.A4.rotate());
        }
        String ttcFile = null;
        StringBuffer text = new StringBuffer();
        // Properties prop = MessageBundleScriptCreator
        // .getComponentResource(AbstractEloanPage.class);
        try {
            ttcFile = PropUtil.getProperty("ttcFile.dir") + PropUtil.getProperty("itext.mingliuFile") + ",0";
            // ttcFile = "D:/eloan/fonts/mingliu.ttc,0";
            List<InputStream> pdfs = streamOfPDFFiles;
            List<PdfReader> readers = new ArrayList<PdfReader>();
            totalPages = 0;
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
                totalPages += pdfReader.getNumberOfPages();
            }
            // Create a writer for the outputstream
            writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            // bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252,
            // BaseFont.NOT_EMBEDDED);
            // bfont = BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H",
            // BaseFont.NOT_EMBEDDED);
            bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            currentPageNumber = 0;
            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();
                // 20200325 Update by Johnny lin 修正錯誤 java.lang.reflect.Field f = pdfReader.getClass().getDeclaredField(
                java.lang.reflect.Field f = pdfReader.getClass().getDeclaredField("encrypted");
                f.setAccessible(true);
                f.set(pdfReader, false);

                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    text.setLength(0);
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    currentPageNumber++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);
                    pcb.beginText();
                    // 制式的JCS頁次使用subLine=8
                    // 縱向的設定印頁次地方
                    if (vaPrintResult) {
                        if (subLine == 8) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 755);
                        } else if (subLine == 1) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(450, 785);
                        } else if (subLine == 3) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 797);
                        } else if (subLine == 9) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 813);
                        } else if (subLine == 7) {// 使用的有cms1010r01
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(418, 770);
                        } else {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 705);
                        }
                    } else {
                        if (subLine == 8) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(647, 513);
                        } else if (subLine == 1) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(647, 513);
                        }
                    }

                    Object[] object = { currentPageNumber, totalPages };
                    text.append(new MessageFormat(Util.nullToSpace(paginationText)).format(object));
                    // text.append(MessageFormat.format(Util.nullToSpace(paginationText), object));//not thread-safe

                    pcb.showText(text.toString());
                    pcb.endText();
                }
                pageOfCurrentReaderPDF = 0;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }

    /**
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @param vaPrintResult
     *            橫向縱向列印 true 縱向 false 橫向
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale, int subLine,
            List<Boolean> vaPrintResults) throws IOException, Exception {
        // 自訂橫向縱向列印 By johnny Lin
        if (vaPrintResults == null || vaPrintResults.isEmpty() || vaPrintResults.size() != streamOfPDFFiles.size()) {
            mergeReWritePagePdf(streamOfPDFFiles, outputStream, paginationText, paginate, locale, subLine, true);
        } else {
            PdfWriter writer;
            int totalPages;
            BaseFont bfont;
            PdfContentByte pcb;
            PdfImportedPage page;
            int currentPageNumber;
            int pageOfCurrentReaderPDF;
            int seqCurrentReaderPDF;
            Iterator<PdfReader> iteratorPDFReader;
            // Rectangle pSize=new Rectangle(600,790);
            // Document document = new Document(pSize);
            Document document = null;
            document = new Document();
            // if (vaPrintResult) {
            // document = new Document(PageSize.A4);
            // } else {
            // document = new Document(PageSize.A4.rotate());
            // }
            String ttcFile = null;
            StringBuffer text = new StringBuffer();
            // Properties prop = MessageBundleScriptCreator
            // .getComponentResource(AbstractEloanPage.class);
            try {
                ttcFile = PropUtil.getProperty("ttcFile.dir") + PropUtil.getProperty("itext.mingliuFile") + ",0";
                // ttcFile = "D:/eloan/fonts/mingliu.ttc,0";
                List<InputStream> pdfs = streamOfPDFFiles;
                List<PdfReader> readers = new ArrayList<PdfReader>();
                totalPages = 0;
                Iterator<InputStream> iteratorPDFs = pdfs.iterator();

                // Create Readers for the pdfs.
                while (iteratorPDFs.hasNext()) {
                    InputStream pdf = iteratorPDFs.next();
                    PdfReader pdfReader = new PdfReader(pdf);
                    readers.add(pdfReader);
                    totalPages += pdfReader.getNumberOfPages();
                }
                // Create a writer for the outputstream
                writer = PdfWriter.getInstance(document, outputStream);
                document.open();
                // bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252,
                // BaseFont.NOT_EMBEDDED);
                // bfont = BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H",
                // BaseFont.NOT_EMBEDDED);
                bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

                pcb = writer.getDirectContent(); // Holds the PDF
                // data

                currentPageNumber = 0;
                pageOfCurrentReaderPDF = 0;
                seqCurrentReaderPDF = 0;
                iteratorPDFReader = readers.iterator();

                // Loop through the PDF files and add to the output.
                while (iteratorPDFReader.hasNext()) {
                    PdfReader pdfReader = iteratorPDFReader.next();
                    // 20200325 Update by Johnny lin 修正錯誤 java.lang.reflect.Field f = pdfReader.getClass().getDeclaredField(
                    java.lang.reflect.Field f = pdfReader.getClass().getDeclaredField("encrypted");
                    f.setAccessible(true);
                    f.set(pdfReader, false);

                    if (vaPrintResults.get(seqCurrentReaderPDF)) {
                        document.setPageSize(PageSize.A4);
                    } else {
                        document.setPageSize(PageSize.A4.rotate());
                    }

                    // Create a new page in the target for each source page.
                    while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                        text.setLength(0);
                        document.newPage();
                        pageOfCurrentReaderPDF++;
                        currentPageNumber++;
                        page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                        pcb.addTemplate(page, 0, 0);
                        pcb.beginText();
                        // 制式的JCS頁次使用subLine=8
                        // 縱向的設定印頁次地方
                        if (vaPrintResults.get(seqCurrentReaderPDF)) {
                            if (subLine == 8) {
                                pcb.setFontAndSize(bfont, 10);
                                pcb.setTextMatrix(445, 755);
                            } else if (subLine == 1) {
                                pcb.setFontAndSize(bfont, 11);
                                pcb.setTextMatrix(450, 785);
                            } else if (subLine == 3) {
                                pcb.setFontAndSize(bfont, 11);
                                pcb.setTextMatrix(445, 797);
                            } else if (subLine == 9) {
                                pcb.setFontAndSize(bfont, 10);
                                pcb.setTextMatrix(445, 813);
                            } else if (subLine == 7) {// 使用的有cms1010r01
                                pcb.setFontAndSize(bfont, 10);
                                pcb.setTextMatrix(418, 770);
                            } else {
                                pcb.setFontAndSize(bfont, 10);
                                pcb.setTextMatrix(445, 705);
                            }
                        } else {
                            if (subLine == 8) {
                                pcb.setFontAndSize(bfont, 10);
                                pcb.setTextMatrix(647, 513);
                            } else if (subLine == 1) {
                                pcb.setFontAndSize(bfont, 11);
                                pcb.setTextMatrix(647, 513);
                            }
                        }

                        Object[] object = { currentPageNumber, totalPages };
                        text.append(new MessageFormat(Util.nullToSpace(paginationText)).format(object));
                        // text.append(MessageFormat.format(Util.nullToSpace(paginationText), object));//not thread-safe

                        pcb.showText(text.toString());
                        pcb.endText();
                    }
                    pageOfCurrentReaderPDF = 0;

                    seqCurrentReaderPDF++;
                }
                outputStream.flush();
                document.close();
                outputStream.close();
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
                throw new Exception();
            } finally {
                if (document.isOpen())
                    document.close();
                try {
                    if (outputStream != null)
                        outputStream.close();
                } catch (IOException ioe) {
                    LOGGER.error(ioe.getMessage());
                    throw new IOException();
                }
            }
        }
    }

    /**
     * 為了有N份報表合併 但每份的頁次都不同位置使用的
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final Map<InputStream, Integer> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale, int subLine)
            throws IOException, Exception {
        PdfTools.mergeReWritePagePdf(streamOfPDFFiles, outputStream, paginationText, paginate, locale, subLine, true);
    }

    /**
     * 為了有N份報表合併 但每份的頁次都不同位置使用的
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @param vaPrintResult
     *            橫向縱向列印 true 縱向 false 橫向
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdf(final Map<InputStream, Integer> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale, int subLine,
            boolean vaPrintResult) throws IOException, Exception {
        PdfWriter writer;
        int totalPages;
        BaseFont bfont;
        PdfContentByte pcb;
        PdfImportedPage page;
        int currentPageNumber;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        // Rectangle pSize=new Rectangle(600,790);
        // Document document = new Document(pSize);
        Document document = null;
        if (vaPrintResult) {
            document = new Document(PageSize.A4);
        } else {
            document = new Document(PageSize.A4.rotate());
        }
        String ttcFile = null;
        StringBuffer text = new StringBuffer();
        // Properties prop = MessageBundleScriptCreator
        // .getComponentResource(AbstractEloanPage.class);
        Map<Integer, Integer> map = new LinkedHashMap<Integer, Integer>();
        List<Integer> list = new LinkedList<Integer>();
        LOGGER.info("into mergeReWritePagePdf");
        try {
            ttcFile = PropUtil.getProperty("ttcFile.dir") + PropUtil.getProperty("itext.mingliuFile") + ",0";

            LOGGER.info("get ttcFile = " + ttcFile);
            // ttcFile = "D:/eloan/fonts/mingliu.ttc,0";
            List<InputStream> pdfs = new LinkedList<InputStream>();
            for (InputStream key : streamOfPDFFiles.keySet()) {
                pdfs.add(key);
                map.put(streamOfPDFFiles.get(key), 0);
            }
            List<PdfReader> readers = new ArrayList<PdfReader>();
            totalPages = 0;
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            LOGGER.info("into iteratorPDFs.hasNext()");
            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
                totalPages += pdfReader.getNumberOfPages();
                list.add(totalPages);
            }
            int count = 0;
            for (Integer key : map.keySet()) {
                map.put(key, list.get(count));
                count++;
            }

            writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            // bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252,
            // BaseFont.NOT_EMBEDDED);
            // bfont = BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H",
            // BaseFont.NOT_EMBEDDED);
            bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            currentPageNumber = 0;
            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            LOGGER.info("into iteratorPDFReader.hasNext()");
            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();
                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    text.setLength(0);
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    currentPageNumber++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);
                    pcb.beginText();
                    // 取得頁次位置
                    boolean result = false;
                    for (Integer key : map.keySet()) {
                        if (map.get(key) >= currentPageNumber && !result) {
                            result = true;
                            subLine = key;
                        }
                    }
                    // 縱向的設定印頁次地方
                    if (vaPrintResult) {
                        // 制式的JCS頁次使用subLine=8
                        if (subLine == 8) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 757);
                        } else if (subLine == 12) {
                            // 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 769);
                        } else if (subLine == 9) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 813);
                        } else if (subLine == 7) {// 使用的有lms1705r01,lms1705r02
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(425, 770);
                        } else if (subLine == 6) {// 使用的有lms1705r01,lms1705r02
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(425, 774);
                        } else if (subLine == 1) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(450, 785);
                        } else if (subLine == 3) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 797);
                        } else if (subLine == 10) {
                            // cls1141r01rptservice r99 使用
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 797);
                        } else if (subLine == 13) {
                            // cls1141r01rptservice r99 使用
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 830);
                        } else {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(325, 770);
                        }
                    } else {
                        if (subLine == 7) {// 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(630, 528);
                        } else if (subLine == 8) {// 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(630, 515);
                        } else {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(630, 528);
                        }
                    }

                    Object[] object = { currentPageNumber, totalPages };
                    text.append(new MessageFormat(Util.nullToSpace(paginationText)).format(object));
                    // text.append(MessageFormat.format(Util.nullToSpace(paginationText), object));//not thread-safe

                    pcb.showText(text.toString());
                    pcb.endText();

                }
                pageOfCurrentReaderPDF = 0;
            }
            LOGGER.info("into outputStream.flush()");
            outputStream.flush();
            document.close();
            LOGGER.info("into outputStream.close()");
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }

    /**
     * 為了有N份報表合併 但每份的頁次都不同位置使用的
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param paginationText
     *            頁次：第 {0} 頁/共 {1} 頁
     * @param paginate
     *            分頁
     * @param locale
     *            zh_TW: 正體中文,zh_CN: 簡體中文,en_US: 英文
     * @param subLine
     * @param vaPrintResult
     *            橫向縱向列印 true 縱向 false 橫向
     * @param printPageNum
     *            要不要印頁碼
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdfWithPageNum(final Map<InputStream, Integer> streamOfPDFFiles, final OutputStream outputStream, String paginationText, final boolean paginate, Locale locale,
            int subLine, boolean vaPrintResult, boolean printPageNum) throws IOException, Exception {
        PdfWriter writer;
        int totalPages;
        BaseFont bfont;
        PdfContentByte pcb;
        PdfImportedPage page;
        int currentPageNumber;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        // Rectangle pSize=new Rectangle(600,790);
        // Document document = new Document(pSize);
        Document document = null;
        if (vaPrintResult) {
            document = new Document(PageSize.A4);
        } else {
            document = new Document(PageSize.A4.rotate());
        }
        String ttcFile = null;
        StringBuffer text = new StringBuffer();
        // Properties prop = MessageBundleScriptCreator
        // .getComponentResource(AbstractEloanPage.class);
        Map<Integer, Integer> map = new LinkedHashMap<Integer, Integer>();
        List<Integer> list = new LinkedList<Integer>();
        LOGGER.info("into mergeReWritePagePdf");
        try {
            ttcFile = PropUtil.getProperty("ttcFile.dir") + PropUtil.getProperty("itext.mingliuFile") + ",0";

            LOGGER.info("get ttcFile = " + ttcFile);
            // ttcFile = "D:/eloan/fonts/mingliu.ttc,0";
            List<InputStream> pdfs = new LinkedList<InputStream>();
            for (InputStream key : streamOfPDFFiles.keySet()) {
                pdfs.add(key);
                map.put(streamOfPDFFiles.get(key), 0);
            }
            List<PdfReader> readers = new ArrayList<PdfReader>();
            totalPages = 0;
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            LOGGER.info("into iteratorPDFs.hasNext()");
            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
                totalPages += pdfReader.getNumberOfPages();
                list.add(totalPages);
            }
            int count = 0;
            for (Integer key : map.keySet()) {
                map.put(key, list.get(count));
                count++;
            }

            writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            // bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252,
            // BaseFont.NOT_EMBEDDED);
            // bfont = BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H",
            // BaseFont.NOT_EMBEDDED);
            bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            currentPageNumber = 0;
            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            LOGGER.info("into iteratorPDFReader.hasNext()");
            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();
                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    text.setLength(0);
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    currentPageNumber++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);
                    pcb.beginText();
                    // 取得頁次位置
                    boolean result = false;
                    for (Integer key : map.keySet()) {
                        if (map.get(key) >= currentPageNumber && !result) {
                            result = true;
                            subLine = key;
                        }
                    }
                    // 縱向的設定印頁次地方
                    if (vaPrintResult) {
                        // 制式的JCS頁次使用subLine=8
                        if (subLine == 8) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 757);
                        } else if (subLine == 12) {
                            // 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 769);
                        } else if (subLine == 9) {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(445, 813);
                        } else if (subLine == 7) {// 使用的有lms1705r01,lms1705r02
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(425, 770);
                        } else if (subLine == 6) {// 使用的有lms1705r01,lms1705r02
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(425, 774);
                        } else if (subLine == 1) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(450, 785);
                        } else if (subLine == 3) {
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 797);
                        } else if (subLine == 10) {
                            // cls1141r01rptservice r99 使用
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 797);
                        } else if (subLine == 13) {
                            // cls1141r01rptservice r99 使用
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(445, 830);
                        } else {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(325, 770);
                        }
                    } else {
                        if (subLine == 7) {// 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(630, 528);
                        } else if (subLine == 8) {// 使用的有lms1205r01,lms1205r02.....
                            pcb.setFontAndSize(bfont, 11);
                            pcb.setTextMatrix(630, 515);
                        } else {
                            pcb.setFontAndSize(bfont, 10);
                            pcb.setTextMatrix(630, 528);
                        }
                    }

                    Object[] object = { currentPageNumber, totalPages };
                    text.append(new MessageFormat(Util.nullToSpace(paginationText)).format(object));
                    // text.append(MessageFormat.format(Util.nullToSpace(paginationText), object));//not thread-safe
                    // J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
                    if (printPageNum) {
                        pcb.showText(text.toString());
                    }
                    pcb.endText();

                }
                pageOfCurrentReaderPDF = 0;
            }
            LOGGER.info("into outputStream.flush()");
            outputStream.flush();
            document.close();
            LOGGER.info("into outputStream.close()");
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }

    /**
     * 合併檔案
     * 
     * @param streamOfPDFFiles
     *            多個pdf的InputStream
     * @param outputStream
     * @param vaPrintResult
     *            橫向縱向列印 true 縱向 false 橫向
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWriteA3PagePdf(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, boolean vaPrintResult) throws IOException, Exception {
        PdfWriter writer;
        // int totalPages;
        // BaseFont bfont;
        PdfContentByte pcb;
        PdfImportedPage page;
        // int currentPageNumber;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        Document document = null;
        if (vaPrintResult) {
            document = new Document(PageSize.A3);
        } else {
            document = new Document(PageSize.A3.rotate());
        }
        try {
            List<InputStream> pdfs = streamOfPDFFiles;
            List<PdfReader> readers = new ArrayList<PdfReader>();
            // totalPages = 0;
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
                // totalPages += pdfReader.getNumberOfPages();
            }
            // Create a writer for the outputstream
            writer = PdfWriter.getInstance(document, outputStream);

            document.open();
            // bfont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            // currentPageNumber = 0;
            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();

                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    // currentPageNumber++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    pcb.addTemplate(page, 0, 0);
                }
                pageOfCurrentReaderPDF = 0;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }

    /**
     * 任意尺寸TO A4
     * 
     * @param streamOfPDFFiles
     * @param outputStream
     * @param vaPrintResult
     * @throws IOException
     * @throws Exception
     */
    public static void mergeReWritePagePdfToA4(final List<InputStream> streamOfPDFFiles, final OutputStream outputStream, boolean vaPrintResult) throws IOException, Exception {
        PdfWriter writer;
        PdfContentByte pcb;
        PdfImportedPage page;
        int pageOfCurrentReaderPDF;
        Iterator<PdfReader> iteratorPDFReader;
        Document document = null;
        if (vaPrintResult) {
            document = new Document(PageSize.A4);
        } else {
            document = new Document(PageSize.A4.rotate());
        }
        try {
            List<InputStream> pdfs = streamOfPDFFiles;
            List<PdfReader> readers = new ArrayList<PdfReader>();
            Iterator<InputStream> iteratorPDFs = pdfs.iterator();

            // Create Readers for the pdfs.
            while (iteratorPDFs.hasNext()) {
                InputStream pdf = iteratorPDFs.next();
                PdfReader pdfReader = new PdfReader(pdf);
                readers.add(pdfReader);
            }
            // Create a writer for the outputstream
            writer = PdfWriter.getInstance(document, outputStream);

            document.open();
            pcb = writer.getDirectContent(); // Holds the PDF
            // data

            pageOfCurrentReaderPDF = 0;
            iteratorPDFReader = readers.iterator();

            // Loop through the PDF files and add to the output.
            while (iteratorPDFReader.hasNext()) {
                PdfReader pdfReader = iteratorPDFReader.next();

                // Create a new page in the target for each source page.
                while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {
                    document.newPage();
                    pageOfCurrentReaderPDF++;
                    page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
                    float width = page.getWidth();
                    float height = page.getHeight();
                    if (height > width) {
                        // 橫向
                        float widthScale = PageSize.A4.getWidth() / width;
                        float heightScale = PageSize.A4.getHeight() / height;
                        pcb.addTemplate(page, widthScale, 0, 0, heightScale, 0, 0);
                    } else {
                        // 縱向
                        float widthScale = PageSize.A4.getWidth() / height;
                        float heightScale = PageSize.A4.getHeight() / width;
                        pcb.addTemplate(page, widthScale, 0, 0, heightScale, 0, 0);
                    }
                }
                pageOfCurrentReaderPDF = 0;
            }
            outputStream.flush();
            document.close();
            outputStream.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception();
        } finally {
            if (document.isOpen())
                document.close();
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
                throw new IOException();
            }
        }

    }
}
