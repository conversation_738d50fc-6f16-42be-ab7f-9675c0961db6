/* 
 * L130S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.kordamp.json.JSONObject;
import org.springframework.stereotype.Repository;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.dao.L130S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L130S01A;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/** 異常通報表明細檔 **/
@Repository
public class L130S01ADaoImpl extends LMSJpaDao<L130S01A, String> implements
		L130S01ADao {

	@Override
	public L130S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L130S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		// 依照事項顯示順序昇冪排序
		// search.addOrderBy("seqKind");
		// search.addOrderBy("seqNo");
		// search.addOrderBy("seqShow");
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L130S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L130S01A> findInsertData(String mainId) {
		// J-GGG-XXXX
		// 授管處核定與分行已辦事項才要上傳
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "headDecide", "Y");
		ISearch search2 = createSearchTemplete();
		search2.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search2.addSearchModeParameters(SearchMode.EQUALS, "seqKind", "1");
		search2.addSearchModeParameters(SearchMode.NOT_EQUALS, "headDecide",
				"Y");
		List<L130S01A> listAll = new ArrayList<L130S01A>();
		List<L130S01A> list = createQuery(search).getResultList();
		List<L130S01A> list2 = createQuery(search2).getResultList();
		listAll.addAll(list);
		listAll.addAll(list2);
		return listAll;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<L130S01A> findByMainIdAndBranchKind(String mainId,
			String branchKind) {
		List<L130S01A> list = new ArrayList<L130S01A>();
		String[] sqlKeys = new String[] { "oid", "Creator", "areaDecide",
				"areaMonth", "bigKind", "branchKind", "createBY", "createTime",
				"docDscr", "headDecide", "headMonth", "isStop", "isUnNormal",
				"mainId", "runDate", "seqAppend", "bigKind", "seqName",
				"seqNo", "seqShow", "setAmt", "setCurr", "setKind",
				"updateTime", "updater", "seqKind", "ctlDscr" };
		Query query = null;
		// 依照不同單位提供不同條件排序
		if ("1".equals(branchKind)) {
			/*
			 * 分行: 已辦|1;擬辦|2;其他|3 ASC mainKind(通報事項大類
			 * A、B、C…LNF07A_KEY_4)+E_SEQ(順序 LNF07A_KEY_3) ASC 項目序號 LNF07A_KEY_2
			 * ASC
			 */
			// 分行
			query = getEntityManager().createNamedQuery(
					"L130S01A.selSubL130s01a");
			query.setParameter("mainId", mainId); // 設置參數
			query.setParameter("branchKind", branchKind); // 設置參數
		} else if ("2".equals(branchKind)) {
			/*
			 * 營運中心: 營運中心有勾核定 @If(E_Item3 != "";"1";"9") ASC 擬辦(=2)在前
			 * 
			 * @If(E_Item2 = "2";"1";"9") ASC mainKind(通報事項大類
			 * A、B、C…LNF07A_KEY_4)+E_SEQ(順序 LNF07A_KEY_3) ASC 項目序號 LNF07A_KEY_2
			 * ASC
			 */
			// 營運中心
			// query =
			// getEntityManager().createNamedQuery("L130S01A.selAreaL130s01a");
			query = getEntityManager().createNamedQuery(
					"L130S01A.selAreaL130s01a2");
			// query.setParameter("areaDecide", "Y"); // 設置參數
			// query.setParameter("seqKind", "2"); // 設置參數
			query.setParameter("mainId", mainId); // 設置參數
			// query.setParameter("branchKind", branchKind); // 設置參數
		} else if ("3".equals(branchKind)) {
			/*
			 * 授管處: 授管處有勾核定 @If(E_Item3 != "";"1";"9") ASC 擬辦(=2)在前 @If(E_Item2
			 * = "2";"1";"9") ASC mainKind(通報事項大類 A、B、C…LNF07A_KEY_4)+E_SEQ(順序
			 * LNF07A_KEY_3) ASC 項目序號 LNF07A_KEY_2 ASC
			 */
			// 授管處
			// query =
			// getEntityManager().createNamedQuery("L130S01A.selHeadL130s01a");
			query = getEntityManager().createNamedQuery(
					"L130S01A.selHeadL130s01a2");
			// query.setParameter("headDecide", "Y"); // 設置參數
			// query.setParameter("seqKind", "2"); // 設置參數
			query.setParameter("mainId", mainId); // 設置參數
			// query.setParameter("branchKind", branchKind); // 設置參數
		}
		if (query != null) {
			List<Object[]> l = query.getResultList();
			for (Object[] objs : l) {
				/*
				 * TODO 不好的寫法 在 orm-lms.xml 裡，指定取出的 column 名稱及順序 最後 2 個欄位是
				 * DEFINESTR1, DEFINESTR2
				 * 
				 * 所以下面寫 for(int i=0; i<objs.length-2; i++){
				 * 
				 * 要新增欄位時，要小心 orm-lms.xml 的 column 順序
				 */
				Object[] subObjs = new Object[objs.length - 2];
				for (int i = 0; i < objs.length - 2; i++) {
					subObjs[i] = objs[i];
				}
				try {
					L130S01A m = new L130S01A();
					JSONObject json = DataParse.toJSON(m, true);
					int index = 0;
					for (String key : sqlKeys) {
						if ("runDate".equals(Util.trim(key))) {
							json.put(key, CapDate.formatDate(
									(Date) subObjs[index], "yyyy-MM-dd"));
						} else {
							json.put(key,
									changeVal(branchKind, key, subObjs[index]));
						}
						index++;
					}
					DataParse.toBean(json, m);
					list.add(m);
				} catch (CapException e) {
					logger.error(e.getMessage());
				}
			}
		}
		return list;
	}

	/**
	 * 依照條件取代特定欄位數值
	 * 
	 * @param branchKind
	 *            單位類別
	 * @param key
	 * @param val
	 * @return 取代後數值
	 */
	private String changeVal(String branchKind, Object key, Object val) {
		if ("2".equals(branchKind)) {
			// 營運中心
			if ("areaDecide".equals(Util.trim(key))) {
				if ("Y".equals(Util.trim(val))) {
					return "1";
				} else {
					return "9";
				}
			} else if ("seqKind".equals(Util.trim(key))) {
				if ("2".equals(Util.trim(val))) {
					return "1";
				} else {
					return "9";
				}
			}
		} else if ("3".equals(branchKind)) {
			// 授管處
			if ("headDecide".equals(Util.trim(key))) {
				if ("Y".equals(Util.trim(val))) {
					return "1";
				} else {
					return "9";
				}
			} else if ("seqKind".equals(Util.trim(key))) {
				if ("2".equals(Util.trim(val))) {
					return "1";
				} else {
					return "9";
				}
			}
		}
		return Util.trim(val);
	}

	@Override
	public L130S01A findByUniqueKey(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L130S01A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L130S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	/**
	 * J-GGG-XXXX
	 * 簡化小規模營業人(央行C方案)異常通報簽報流程，授管中心轄下分行由各授管中心核批，其餘國外部、金控總部分行、國際金融業務分行，由授信審查核處核批
	 */
	@Override
	public List<L130S01A> findInsertDataByCaseType(String mainId,
			String whoDecide) {
		// J-GGG-XXXX
		// 授管處核定與分行已辦事項才要上傳
		String decideFieldName = "headDecide";
		if (Util.equals(whoDecide, "A")) {
			decideFieldName = "areaDecide";
		}

		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, decideFieldName, "Y");
		ISearch search2 = createSearchTemplete();
		search2.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search2.addSearchModeParameters(SearchMode.EQUALS, "seqKind", "1");
		search2.addSearchModeParameters(SearchMode.NOT_EQUALS, decideFieldName,
				"Y");
		List<L130S01A> listAll = new ArrayList<L130S01A>();
		List<L130S01A> list = createQuery(search).getResultList();
		List<L130S01A> list2 = createQuery(search2).getResultList();
		listAll.addAll(list);
		listAll.addAll(list2);
		return listAll;
	}
}