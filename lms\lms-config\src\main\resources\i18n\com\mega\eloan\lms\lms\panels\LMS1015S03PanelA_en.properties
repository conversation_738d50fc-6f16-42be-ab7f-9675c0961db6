tab.03=Collateral Info.
#==================
tab03.desc01=Note\uff1aIf there are more or multiple collateral, please fill based on principal collateral.
tab03.desc02=\u203bPlease decide if the collateral is "Self-Occupied" or "Not Self-Occupied" when Type of Collateral is Real Estate. "Self-occupied" refers to situation that the real estate is fully self-occupied and otherwise is "Not Self-Occupied."
tab03.houseAreaUnit=square meter

tab03.factor_chooseOne=(please select the one with the best approximation.)
tab03.factor_li=\u25ce
tab03.factor1=Location and type of the collateral 
tab03.factor1_note=Note\uff1aIf the collateral is not a real estate, please select by guarantee ratio or collateral type.
tab03.factor1.6.a=Fully secured; and the collateral is Mega bank\u2019s deposit or bond funds approved by headquarter.
tab03.factor1.5.a=\u8ddd\u5730\u9435\u7ad9/\u706b\u8eca\u7ad9\u6b65\u884c5\u5206\u9418\u5167\uff0c\u751f\u6d3b\u6a5f\u80fd\u5341\u5206\u826f\u597d\uff0c\u9130\u8fd1\u5e02\u5340/\u6587\u6559\u5340\uff0c\u6216\u4f4d\u65bc\u5546\u696d\u6838\u5fc3\u5730\u6bb5\uff0c\u4e00\u7d1a\u5546\u696d\u5927\u6a13\u53ca\u5730\u6a19\u5f0f\u5efa\u7bc9\u6797\u7acb\uff0cMercantile activities in the neighborhood are prosperous.
tab03.factor1.5.b=Fully secured; and the collateral is other collateral also approved by headquarter.
tab03.factor1.4.a=\u8ddd\u5730\u9435\u7ad9/\u706b\u8eca\u7ad9\u6b65\u884c6~10\u5206\u9418\uff0c\u751f\u6d3b\u6a5f\u80fd\u4f73\uff0c\u793e\u5340\u74b0\u5883\u5b9c\u4eba\uff0c\u6216\u4f4d\u8655\u5546\u696d\u6838\u5fc3\u5340\u5468\u570d\uff0c\u6216\u70ba\u91cd\u8981\u885b\u661f\u90fd\u5e02\uff0c\u4e2d\u5c0f\u578b\u5546\u696d\u5927\u6a13\u6797\u7acb\uff0c\u4e2d\u5c0f\u578b\u5546\u696d\u532f\u96c6\uff0c\u5546\u696d\u6d3b\u52d5\u4e2d\u5ea6\u3002
tab03.factor1.3.a=\u8ddd\u5730\u9435\u7ad9/\u706b\u8eca\u7ad9\u6b65\u884c11~15\u5206\u9418\uff0cCollateral is located in an ordinary community with some planning, or in business park with mainly small-to-medium companies or light industrials. Mercantile activities in the neighborhood are moderate.
tab03.factor1.3.b=Partially secured; or collateral is exclueded in the pledged value.
tab03.factor1.2.a=\u8ddd\u5730\u9435\u7ad9/\u706b\u8eca\u7ad9\u6b65\u884c16~20\u5206\u9418\uff0cCollateral is located in a poor community, or in a neighborhood scattered with small-to-medium or micro companies. Level of mercantile activities is low.
tab03.factor1.1.a=\u8ddd\u5730\u9435\u7ad9/\u706b\u8eca\u7ad9\u6b65\u884c21\u5206\u9418\u4ee5\u4e0a\uff0cCollateral is located in a poor community, or in a neighborhood scattered with small-to-medium or micro companies. Level of mercantile activities is low.
tab03.factor1.1.b=No collateral

tab03.factor2=Market Condition and Liquidity
tab03.factor2_note=Note\uff1aIf the collateral is not a real estate, please select by liquidity.
tab03.factor2.4.a=The supply and demand for the property type and location are currently in equilibrium. The number of competitive properties coming to the market is lower than the forecasted demand.
tab03.factor2.4.b=Non-real estate collateral with strong liquity.
tab03.factor2.3.a=The supply and demand for the property type and location are roughly in equilibrium. The number of competitive properties coming to the market is approximately equal to the forecasted demand.
tab03.factor2.3.b=Non-real estate collateral with good liquity.
tab03.factor2.2.a=The supply and demand for the property type and location are sort of in disequilibrium. A large number of competitive properties are coming on the market or are in the planning stages.
tab03.factor2.2.b=Non-real estate collateral with average liquity.
tab03.factor2.1.a=Market conditions are weak. It is uncertain when conditions will improve and return to equilibrium.
tab03.factor2.1.b=No collateral
#==================
#C121S01A
C121S01A.cmsType=Type of Collateral
C121S01A.location=Location
C121S01A.region=\u5ea7\u843d\u5730\u5340
C121S01A.houseAge=Age of House
C121S01A.houseArea=Construction Area
C121S01A.collUsage=Collateral Usage
C121S01A.collUsage.label=(Real Estate)
C121S01A.locationType=Type of Location