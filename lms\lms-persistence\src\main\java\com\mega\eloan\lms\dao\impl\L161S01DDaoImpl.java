/* 
 * L161S01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.model.L161S01D;

/** RPA自動發查明細檔 **/
@Repository
public class L161S01DDaoImpl extends LMSJpaDao<L161S01D, String>
	implements L161S01DDao {

	@Override
	public L161S01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L161S01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L161S01D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L161S01D> findByIndex01(String mainId, String type, String status){
		ISearch search = createSearchTemplete();
		List<L161S01D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (status != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "status", status);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L161S01D> findByIndex02(String mainId, String type, String status, String rpaQueryReason1){
		ISearch search = createSearchTemplete();
		List<L161S01D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (status != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "status", status);
		if (rpaQueryReason1 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "rpaQueryReason1", rpaQueryReason1);
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L161S01D> findByIndex03(String mainId, String docfileoid, String docfileoid2){
		ISearch search = createSearchTemplete();
		List<L161S01D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (docfileoid != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docfileoid", docfileoid);
		if (docfileoid2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docfileoid2", docfileoid2);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}