/* 
 * L170M01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L170M01F;

/** 覆審意見檔 **/
public interface L170M01FDao extends IGenericDao<L170M01F> {

	L170M01F findByOid(String oid);
	
	L170M01F findByMainId(String mainId);
	
	L170M01F findByUniqueKey(String mainId, String custId, String dupNo);

	List<L170M01F> findByIndex01(String mainId, String custId, String dupNo);
	
	List<L170M01F> findByCustIdDupId(String custId,String DupNo);
}