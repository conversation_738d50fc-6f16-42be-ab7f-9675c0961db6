<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.0" xmlns="http://java.sun.com/xml/ns/persistence"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd">
	<persistence-unit name="pu-com">
		<class>com.mega.eloan.common.model.CodeType</class>
		<class>com.mega.eloan.common.model.SysParameter</class>
		<class>com.mega.eloan.common.model.ElsUser</class>
		<class>com.mega.eloan.common.model.Branch</class>
		<class>com.mega.eloan.common.model.Bstbl</class>
		<class>com.mega.eloan.common.model.AssignAgent</class>
		<class>com.mega.eloan.common.model.AssignGroup</class>
		<class>com.mega.eloan.common.model.AssignRel</class>
		<class>com.mega.eloan.common.model.AuditConfig</class>
		<class>com.mega.eloan.common.model.GWLog</class>
		<class>com.mega.eloan.common.model.GWData</class>		
		<class>com.mega.eloan.common.model.ErrorCode</class>
		<class>com.mega.eloan.common.model.Bulletin</class>
		<class>com.mega.eloan.common.model.Meta</class>
		<class>com.mega.eloan.common.model.SmsProfile</class>
		<class>com.mega.eloan.common.model.SmsContent</class>
		<class>com.mega.eloan.common.batch.model.SchMain</class>
		<class>com.mega.eloan.common.batch.model.SchJob</class>
		<class>com.mega.eloan.common.batch.model.SchRel</class>
		<class>com.mega.eloan.common.batch.model.SchLog</class>
		<class>com.mega.eloan.common.batch.model.SchQueue</class>
		<class>com.mega.eloan.common.model.DeletedMeta</class>
		<class>com.mega.eloan.common.model.EjcicLog</class>
		<class>com.mega.eloan.common.model.ELEjcicGwReqMessage</class>
		<class>com.mega.eloan.common.model.ElAml</class>
		<class>com.mega.eloan.common.model.ElAmlItem</class>
		<class>com.mega.eloan.common.model.BELDFM01</class>
		<class>com.mega.eloan.common.model.BELDFM02</class>
		<exclude-unlisted-classes>true</exclude-unlisted-classes>
	</persistence-unit>
</persistence>
