package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金分析與評估檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name="L120S01G", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","dataType"}))
public class L120S01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 類別<p/>
	 * 1營運概況分析與評估<br/>
	 *  2財務狀況分析與評估
	 */
	@Column(name="DATATYPE", length=1, columnDefinition="CHAR(1)")
	private String dataType;

	/** 分析與評估 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="DATADSCR", columnDefinition="CLOB")
	private String dataDscr;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1營運概況分析與評估<br/>
	 *  2財務狀況分析與評估
	 */
	public String getDataType() {
		return this.dataType;
	}
	/**
	 *  設定類別<p/>
	 *  1營運概況分析與評估<br/>
	 *  2財務狀況分析與評估
	 **/
	public void setDataType(String value) {
		this.dataType = value;
	}

	/** 取得分析與評估 **/
	public String getDataDscr() {
		return this.dataDscr;
	}
	/** 設定分析與評估 **/
	public void setDataDscr(String value) {
		this.dataDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 建構子 **/
	public L120S01G() {}

	/** 建構子
	* @param mainId (文件編號)
	* @param custId (身分證統編)
	* @param dupNo (身分證統編重複碼)
	* @param dataType (類別)
	**/
	public L120S01G (String mainId,String custId,String dupNo,String dataType){
		this.mainId = mainId;
		this.custId = custId;
		this.dupNo = dupNo;
		this.dataType = dataType;
	}
}
