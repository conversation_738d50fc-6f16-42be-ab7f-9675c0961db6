/* 
 * LMS1705S02Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.panels;

import org.springframework.ui.ModelMap;
import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;

/**
 * <pre>
 * [企金]-覆審報告表  一般授信資料
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,jessica,new
 *          </ul>
 */
public class LMS1705S02Panel extends Panel {
	
	public LMS1705S02Panel(String id) {
		super(id);
	}

	public LMS1705S02Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new LMS1705S02Panel01("_lms1705s02").processPanelData(model, params);
	}

	/**/
	private static final long serialVersionUID = 1L;

}
