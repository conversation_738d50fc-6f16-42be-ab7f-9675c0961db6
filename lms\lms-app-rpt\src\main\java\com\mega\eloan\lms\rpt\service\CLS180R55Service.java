package com.mega.eloan.lms.rpt.service;

import java.util.List;
import java.util.Map;
import java.util.Properties;

import jxl.write.WritableSheet;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;




/**
 * <pre>
 * 待售房屋(餘屋)去化落後追蹤表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface CLS180R55Service {
	
	public Map<String, Integer> getTitleMap();
	
	public void setTitleContent(WritableSheet sheet, Map<String, Integer> titleMap, Properties prop, int fromColIndex, int toColIndex, int fromRowIndex, int toRowIndex, String date) throws WriteException;

	public void setHeaderContent(WritableSheet sheet, Map<String, Integer> headerMap1, Properties prop, int colIndex, int rowIndex) throws WriteException;
	
	public void processData(List<Map<String, Object>> elf517List, Map<String, Map<String, Object>> eloanData);

	public Map<String, Integer> getHeaderMapFor918();

	public void setBodyContent(WritableSheet sheet, List<Map<String, Object>> data, int colIndex, int rowIndex, boolean isSecondList) throws RowsExceededException, WriteException;

	public Map<String, Integer> getHeaderMapForSecondList();
}
