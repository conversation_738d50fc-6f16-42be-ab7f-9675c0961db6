package com.mega.eloan.lms.lrs.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFFooter;
import org.apache.poi.hssf.usermodel.HSSFHeader;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.usermodel.HeaderFooter;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.common.RO412;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.constants.lrsConstants;
import com.mega.eloan.lms.lrs.service.LMS1801Service;
import com.mega.eloan.lms.lrs.service.LMS1810Service;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.bean.ELF412B;
import com.mega.eloan.lms.mfaloan.bean.ELF412C;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.service.MisELF412BService;
import com.mega.eloan.lms.mfaloan.service.MisELF412CService;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElCUS25Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.eloan.lms.model.L180M01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1801ServiceImpl extends AbstractCapService implements
		LMS1801Service {
	private static final int MAXLEN_ELF493_PROCESS = StrUtils
			.getEntityFileldLegth(ELF493.class, "elf493_process", 202);
	private static final int MAXLEN_ELF493_MEMO = StrUtils
			.getEntityFileldLegth(ELF493.class, "elf493_memo", 202);
	private static final int MAXLEN_ELF493_DBUCOID = StrUtils
			.getEntityFileldLegth(ELF493.class, "elf493_dbuCoid", 255);
	private static final int MAXLEN_ELF493_OBUCOID = StrUtils
			.getEntityFileldLegth(ELF493.class, "elf493_obuCoid", 255);

	protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Resource
	LMS1810Service lms1810Service;

	@Resource
	RetrialService retrialService;

	@Resource
	BranchService branchService;

	@Autowired
	DocFileService fileService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisLNF022Service misLNF022Service;

	@Resource
	MisElCUS25Service misElCUS25Service;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisELF412Service misELF412Service;

	@Resource
	MisELF412BService misELF412BService;
	
	@Resource
	MisELF412CService misELF412CService;

	@Resource
	CodeTypeService codeTypeService;

	private static int rowHeightAt14 = 360;

	@Override
	public boolean gfnGenCTLListExcel(L180M01A meta) {
		boolean r = false;

		String fieldId = LrsUtil.ATTCH_L180M01A_0;

		String fileName = LrsUtil.getExcelName(meta, fieldId);
		try {
			List<DocFile> docFiles = fileService.findByIDAndName(
					meta.getMainId(), fieldId, "");

			if (docFiles != null && !docFiles.isEmpty()) {
				for (DocFile file : docFiles) {
					fileService.clean(file.getOid());
				}
			}

			DocFile docFile = new DocFile();
			docFile.setBranchId(meta.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(meta.getMainId());
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setSrcFileName(fileName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			docFile.setDeletedTime(null);
			fileService.save(docFile, false);

			create_listExcel(meta, docFile);
			// ---
			r = true;
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		}
		return r;
	}

	private void create_listExcel(L180M01A meta, DocFile docFile)
			throws IOException {

		Map<String, String> map_mowDesc = retrialService.get_lrs_MowType_1();
		
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;
		// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
		HSSFSheet sheet2 = null;
		HSSFSheet sheet3 = null;

		File file = null;
		String filename = LMSUtil.getUploadFilePath(meta.getOwnBrId(),
				meta.getMainId(), docFile.getFieldId());

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_主辦覆審);

		boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_自辦覆審);

		boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_價金履約);

		if (true) {
			File directory = new File(filename);
			directory.mkdirs(); 
			file = new File(filename + "/" + docFile.getOid() + ".xls");
			// ---
			workbook = new HSSFWorkbook();
			sheet1 = workbook.createSheet("覆審名單");
			
			PrintSetup ps1 = sheet1.getPrintSetup();
			ps1.setPaperSize(PrintSetup.A4_PAPERSIZE);
			ps1.setLandscape(false); // 直向列印
			ps1.setFitWidth((short) 1); 

			sheet2 = workbook.createSheet("不覆審名單");
			PrintSetup ps2 = sheet1.getPrintSetup();
			ps2.setPaperSize(PrintSetup.A4_PAPERSIZE);
			ps2.setLandscape(false);
			ps2.setFitWidth((short) 1); // 設定預覽列印與列印成為一頁, 寬度
			
			HSSFFont headFont10 = workbook.createFont();
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);
			
			HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
		
			// ======
			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);
			
			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			cellFormatL.setFont(headFont12);
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);
			
			HSSFCellStyle cellFormatR = workbook.createCellStyle();
			cellFormatR.setFont(headFont12);
			cellFormatR.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);
			
			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
			cellFormatL_Border.cloneStyleFrom(cellFormatL); 
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);
			
			HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
			cellFormatR_Border.cloneStyleFrom(cellFormatR);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
			
			// ======
			HSSFFont headFont14 = workbook.createFont();
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);
			
			HSSFCellStyle cellFormatL_14 = workbook.createCellStyle();
			cellFormatL_14.setFont(headFont14); 
			cellFormatL_14.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_14.setWrapText(true);
			
			// ======
			boolean statusIn1_2 = (Util.equals(
					RetrialDocStatusEnum.編製中.getCode(), meta.getDocStatus()) || Util
					.equals(RetrialDocStatusEnum.待覆核.getCode(),
							meta.getDocStatus()));

			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("序號", 6);
			headerMap.put("統一編號", 13);
			headerMap.put("信用評等", 15);
			headerMap.put("符合授信額度標準", 6);
			headerMap.put("客戶名稱", 18);
			headerMap.put("前次覆審日期", 15);
			if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
				headerMap.put("覆審名單種類", 6);
			}
			if (ctlTypeA) {
				headerMap.put("DBU/OBU額度共管", 18);
			}
			headerMap.put("覆審週期", 6);
			headerMap.put("不覆審註記", 6);
			headerMap.put("備註", 15);
			if (statusIn1_2) {
				headerMap.put("最遲應覆審年月", 10);
			}
			// J-109-0313 小規模覆審
			headerMap.put("純小規模註記", 6);
			headerMap.put("小規模評分", 6);
			// J-110-0272 抽樣覆審
			headerMap.put("抽樣", 6);

			Map<String, String> eachRrowMap = null;

			int totalColSize = headerMap.size();

			List<Map<String, String>> rows = new ArrayList<Map<String, String>>();
			List<Map<String, String>> rows2 = new ArrayList<Map<String, String>>();

			Map<String, List<L180M01B>> l180m01bAllList =  this.getAllFilterXls(statusIn1_2,
					retrialService.findL180M01BDefaultOrder(meta.getMainId()));
			List<L180M01B> l180m01b_list_Y = l180m01bAllList.get("Y");
			List<L180M01B> l180m01b_list_N = l180m01bAllList.get("N");
			for (L180M01B model : l180m01b_list_Y) {
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}

				eachRrowMap = new LinkedHashMap<String, String>();
				for (String h : headerMap.keySet()) {
					eachRrowMap.put(h, "");
				}

				// arr[0] = "";// 之後顯示 count
				eachRrowMap.put("序號", "");

				// arr[1] = Util.trim(model.getCustId());
				eachRrowMap.put("統一編號", Util.trim(model.getCustId()));

				List<String> mowStr = new ArrayList<String>();
				if (true) {
					// 處理信評
					String crdtTbl = Util.trim(model.getElfCrdTTbl());
					if (Util.isNotEmpty(crdtTbl)) {
						mowStr.add("舊信評：" + crdtTbl);
					}
					String mowTbl1 = Util.trim(model.getElfMowTbl1());
					if (Util.isNotEmpty(mowTbl1)) {
						String mowType = Util.trim(model.getElfMowType());
						String label_mowType = map_mowDesc.containsKey(mowType) ? map_mowDesc
								.get(mowType) : mowType;
						mowStr.add(label_mowType + "：" + mowTbl1);
					}
					String fcrdGrad = Util.trim(model.getElfFcrdGrad());
					if (Util.isNotEmpty(fcrdGrad)) {
						mowStr.add(retrialService.gfnCTLCaculateFCRDTYPENM(
								Util.trim(model.getElfFcrdType()),
								Util.trim(model.getElfFcrdArea()),
								Util.trim(model.getElfFcrdPred()), fcrdGrad));
					}
				}
				// arr[2] = Util.trim(StringUtils.join(mowStr,
				// EloanConstants.LINE_BREAK));
				eachRrowMap.put("信用評等", Util.trim(StringUtils.join(mowStr,
						EloanConstants.LINE_BREAK)));

				// arr[3] = Util.equals("Y", model.getElfMainCust()) ? "Y" : "";
				eachRrowMap.put("符合授信額度標準",
						Util.equals("Y", model.getElfMainCust()) ? "Y" : "");

				// arr[4] = Util.trim(model.getElfCName());
				eachRrowMap.put("客戶名稱", Util.trim(model.getElfCName()));

				// arr[5] = Util.trim(TWNDate.toAD(model.getElfLRDate()));
				eachRrowMap.put("前次覆審日期",
						Util.trim(TWNDate.toAD(model.getElfLRDate())));

				if (ctlTypeA) {
					String dbuObuDStr = "";
					if (true) {
						Set<L180M01D> l180m01ds = model.getL180m01ds();
						if (l180m01ds != null && !l180m01ds.isEmpty()) {
							List<String> d_list = new ArrayList<String>();
							for (L180M01D l180m01d : l180m01ds) {

								String id = Util.trim(l180m01d.getDbuObuId());
								String name = Util.trim(l180m01d
										.getDbuObuName());
								d_list.add(id + " " + name);
							}
							dbuObuDStr = StringUtils.join(d_list, ",");
						}
					}
					// arr[6] = Util.trim(dbuObuDStr);
					eachRrowMap.put("DBU/OBU額度共管", Util.trim(dbuObuDStr));
				}

				if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
					eachRrowMap.put("覆審名單種類", Util.trim(model.getCtlType()));
				}

				// arr[7] = Util.trim(model.getElfRCkdLine());
				eachRrowMap.put("覆審週期", Util.trim(model.getElfRCkdLine()));

				// arr[8] = Util.trim(model.getNewNCkdFlag());
				eachRrowMap.put("不覆審註記", Util.trim(model.getNewNCkdFlag()));

				List<String> memoList = new ArrayList<String>();
				if (true) {
					String elfnewAdd = Util.trim(model.getElfNewAdd());
					String newAddDesc = Util.trim(retrialService
							.get_lrs_NewAdd().get(elfnewAdd));
					String elfCntrNo = Util.trim(retrialService
							.findL180M01C_cntrNo(model));

					if (Util.isNotEmpty(newAddDesc)) {
						// 010306
						String raw_dateDesc = LrsUtil
								.model_elfNewDate_to_elf412_rocDateStr(model
										.getElfNewDate());
						String dateDesc = "";
						if (Util.isNotEmpty(raw_dateDesc)) {
							// 轉換成 103.06
							dateDesc = LrsUtil.toStr_NewDate(raw_dateDesc, ".");
						}

						String addMemo = dateDesc + " " + newAddDesc;
						if (Util.equals("C", elfnewAdd)
								|| Util.equals("N", elfnewAdd)) {
							if (Util.isNotEmpty(elfCntrNo)) {
								addMemo = addMemo + "：" + elfCntrNo;
							}
						} else if (Util.equals("R", elfnewAdd)) {
						}
						memoList.add(addMemo);
					}

					if (Util.isNotEmpty(Util.trim(model.getElfMDFlag()))
							&& Util.notEquals("0", model.getElfMDFlag())) {
						memoList.add(Util.trim(TWNDate.toTW(model.getElfMDDt()))
								+ "通報異常");
					}

					if (Util.isNotEmpty(Util.trim(model.getNewNCkdMemo()))) {
						memoList.add(Util.trim(model.getNewNCkdMemo()));
					} else {
						if (Util.isNotEmpty(Util.trim(model.getElfNCkdMemo()))
								&& Util.equals(
										RetrialDocStatusEnum.編製中.getCode(),
										meta.getDocStatus())) {
							memoList.add(Util.trim(model.getElfNCkdMemo()));
						}
					}

					if (ctlTypeA) {
						// BGN J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						if (Util.equals(Util.trim(model.getElfRealCkFg()), "Y")) {
							memoList.add("實地覆審註記："
									+ Util.trim(model.getElfRealCkFg()));
						}

						if (Util.equals(Util.trim(model.getElfRealCkFg()), "Y")) {

							if (CrsUtil.isNOT_null_and_NOTZeroDate(model
									.getElfRealDt())) {

								memoList.add("實地覆審基準日:"
										+ TWNDate.toAD(model.getElfRealDt()));
							}
						}
						// END J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					}

					if (Util.isNotEmpty(Util.trim(model.getElfMemo()))) {
						memoList.add(Util.trim(model.getElfMemo()));
					}
				}

				// arr[9] = Util.trim(StringUtils.join(memoList,
				// EloanConstants.LINE_BREAK));
				eachRrowMap.put("備註", Util.trim(StringUtils.join(memoList,
						EloanConstants.LINE_BREAK)));

				if (statusIn1_2) {
					String calcDate = "";
					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審,
							model.getNewNCkdFlag())
							&& CrsUtil.isNOT_null_and_NOTZeroDate(model
									.getNewNextNwDt())) {

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						Date dueDate = null;
						if (Util.equals(model.getCtlType(),
								LrsUtil.CTLTYPE_自辦覆審)) {
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(), null, new RO412(
											meta.getBranchId(), model), null);
						} else if (Util.equals(model.getCtlType(),
								LrsUtil.CTLTYPE_價金履約)) {
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(), null, null,
									new RO412(meta.getBranchId(), model));
						} else {
							// LrsUtil.CTLTYPE_主辦覆審
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(),
									new RO412(meta.getBranchId(), model), null,
									null);
						}

						if (CrsUtil.isNOT_null_and_NOTZeroDate(dueDate)) {
							calcDate = LrsUtil.toStrYM(dueDate);
						}
					}
					// arr[10] = Util.trim(calcDate);
					eachRrowMap.put("最遲應覆審年月", Util.trim(calcDate));

				} else {
					// '不印最遲應覆審年月
				}
				// ---

				// J-109-0313 小規模覆審
				// arr[11]
				eachRrowMap.put("純小規模註記", Util.equals("Y", model.getIsSmallBuss()) ? "Y" : "");
				// arr[12]
				eachRrowMap.put("小規模評分", model.getSbScore() == null ? "" : model.getSbScore().toString());
				// J-110-0272 抽樣覆審
				// arr[13]
				String randomType = (Util.equals("Y", Util.trim(model.getIsSmallBuss())) ?
						LrsUtil.RANDOMTYPE_Z_純小規模營業人 : Util.trim(model.getElfRandomType()));
				eachRrowMap.put("抽樣", randomType);

				rows.add(eachRrowMap);
			}

			// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
			for (L180M01B model : l180m01b_list_N) {
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}

				eachRrowMap = new LinkedHashMap<String, String>();
				for (String h : headerMap.keySet()) {
					eachRrowMap.put(h, "");
				}

				// arr[0] = "";// 之後顯示 count
				eachRrowMap.put("序號", "");

				// arr[1] = Util.trim(model.getCustId());
				eachRrowMap.put("統一編號", Util.trim(model.getCustId()));

				List<String> mowStr = new ArrayList<String>();
				if (true) {
					// 處理信評
					String crdtTbl = Util.trim(model.getElfCrdTTbl());
					if (Util.isNotEmpty(crdtTbl)) {
						mowStr.add("舊信評：" + crdtTbl);
					}
					String mowTbl1 = Util.trim(model.getElfMowTbl1());
					if (Util.isNotEmpty(mowTbl1)) {
						String mowType = Util.trim(model.getElfMowType());
						String label_mowType = map_mowDesc.containsKey(mowType) ? map_mowDesc
								.get(mowType) : mowType;
						mowStr.add(label_mowType + "：" + mowTbl1);
					}
					String fcrdGrad = Util.trim(model.getElfFcrdGrad());
					if (Util.isNotEmpty(fcrdGrad)) {
						mowStr.add(retrialService.gfnCTLCaculateFCRDTYPENM(
								Util.trim(model.getElfFcrdType()),
								Util.trim(model.getElfFcrdArea()),
								Util.trim(model.getElfFcrdPred()), fcrdGrad));
					}
				}
				// arr[2] = Util.trim(StringUtils.join(mowStr,
				// EloanConstants.LINE_BREAK));
				eachRrowMap.put("信用評等", Util.trim(StringUtils.join(mowStr,
						EloanConstants.LINE_BREAK)));

				// arr[3] = Util.equals("Y", model.getElfMainCust()) ? "Y" : "";
				eachRrowMap.put("符合授信額度標準",
						Util.equals("Y", model.getElfMainCust()) ? "Y" : "");

				// arr[4] = Util.trim(model.getElfCName());
				eachRrowMap.put("客戶名稱", Util.trim(model.getElfCName()));

				// arr[5] = Util.trim(TWNDate.toAD(model.getElfLRDate()));
				eachRrowMap.put("前次覆審日期",
						Util.trim(TWNDate.toAD(model.getElfLRDate())));

				if (ctlTypeA) {
					String dbuObuDStr = "";
					if (true) {
						Set<L180M01D> l180m01ds = model.getL180m01ds();
						if (l180m01ds != null && !l180m01ds.isEmpty()) {
							List<String> d_list = new ArrayList<String>();
							for (L180M01D l180m01d : l180m01ds) {

								String id = Util.trim(l180m01d.getDbuObuId());
								String name = Util.trim(l180m01d
										.getDbuObuName());
								d_list.add(id + " " + name);
							}
							dbuObuDStr = StringUtils.join(d_list, ",");
						}
					}
					// arr[6] = Util.trim(dbuObuDStr);
					eachRrowMap.put("DBU/OBU額度共管", Util.trim(dbuObuDStr));
				}

				if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
					eachRrowMap.put("覆審名單種類", Util.trim(model.getCtlType()));
				}

				// arr[7] = Util.trim(model.getElfRCkdLine());
				eachRrowMap.put("覆審週期", Util.trim(model.getElfRCkdLine()));

				// arr[8] = Util.trim(model.getNewNCkdFlag());
				eachRrowMap.put("不覆審註記", Util.trim(model.getNewNCkdFlag()));

				List<String> memoList = new ArrayList<String>();
				if (true) {
					String elfnewAdd = Util.trim(model.getElfNewAdd());
					String newAddDesc = Util.trim(retrialService
							.get_lrs_NewAdd().get(elfnewAdd));
					String elfCntrNo = Util.trim(retrialService
							.findL180M01C_cntrNo(model));

					if (Util.isNotEmpty(newAddDesc)) {
						// 010306
						String raw_dateDesc = LrsUtil
								.model_elfNewDate_to_elf412_rocDateStr(model
										.getElfNewDate());
						String dateDesc = "";
						if (Util.isNotEmpty(raw_dateDesc)) {
							// 轉換成 103.06
							dateDesc = LrsUtil.toStr_NewDate(raw_dateDesc, ".");
						}

						String addMemo = dateDesc + " " + newAddDesc;
						if (Util.equals("C", elfnewAdd)
								|| Util.equals("N", elfnewAdd)) {
							if (Util.isNotEmpty(elfCntrNo)) {
								addMemo = addMemo + "：" + elfCntrNo;
							}
						} else if (Util.equals("R", elfnewAdd)) {
						}
						memoList.add(addMemo);
					}

					if (Util.isNotEmpty(Util.trim(model.getElfMDFlag()))
							&& Util.notEquals("0", model.getElfMDFlag())) {
						memoList.add(Util.trim(TWNDate.toTW(model.getElfMDDt()))
								+ "通報異常");
					}

					if (Util.isNotEmpty(Util.trim(model.getNewNCkdMemo()))) {
						memoList.add(Util.trim(model.getNewNCkdMemo()));
					} else {
						if (Util.isNotEmpty(Util.trim(model.getElfNCkdMemo()))
								&& Util.equals(
								RetrialDocStatusEnum.編製中.getCode(),
								meta.getDocStatus())) {
							memoList.add(Util.trim(model.getElfNCkdMemo()));
						}
					}

					if (ctlTypeA) {
						// BGN J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
						if (Util.equals(Util.trim(model.getElfRealCkFg()), "Y")) {
							memoList.add("實地覆審註記："
									+ Util.trim(model.getElfRealCkFg()));
						}

						if (Util.equals(Util.trim(model.getElfRealCkFg()), "Y")) {

							if (CrsUtil.isNOT_null_and_NOTZeroDate(model
									.getElfRealDt())) {

								memoList.add("實地覆審基準日:"
										+ TWNDate.toAD(model.getElfRealDt()));
							}
						}
						// END J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
					}

					if (Util.isNotEmpty(Util.trim(model.getElfMemo()))) {
						memoList.add(Util.trim(model.getElfMemo()));
					}
				}

				// arr[9] = Util.trim(StringUtils.join(memoList,
				// EloanConstants.LINE_BREAK));
				eachRrowMap.put("備註", Util.trim(StringUtils.join(memoList,
						EloanConstants.LINE_BREAK)));

				if (statusIn1_2) {
					String calcDate = "";
					if (Util.equals(LrsUtil.NCKD_8_本次暫不覆審,
							model.getNewNCkdFlag())
							&& CrsUtil.isNOT_null_and_NOTZeroDate(model
							.getNewNextNwDt())) {

						// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
						Date dueDate = null;
						if (Util.equals(model.getCtlType(),
								LrsUtil.CTLTYPE_自辦覆審)) {
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(), null, new RO412(
											meta.getBranchId(), model), null);
						} else if (Util.equals(model.getCtlType(),
								LrsUtil.CTLTYPE_價金履約)) {
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(), null, null,
									new RO412(meta.getBranchId(), model));
						} else {
							// LrsUtil.CTLTYPE_主辦覆審
							dueDate = retrialService.gfnCTL_Caculate_DueDate(
									"3", meta.getDataDate(),
									new RO412(meta.getBranchId(), model), null,
									null);
						}

						if (CrsUtil.isNOT_null_and_NOTZeroDate(dueDate)) {
							calcDate = LrsUtil.toStrYM(dueDate);
						}
					}
					// arr[10] = Util.trim(calcDate);
					eachRrowMap.put("最遲應覆審年月", Util.trim(calcDate));

				} else {
					// '不印最遲應覆審年月
				}
				// ---

				// J-109-0313 小規模覆審
				// arr[11]
				eachRrowMap.put("純小規模註記", Util.equals("Y", model.getIsSmallBuss()) ? "Y" : "");
				// arr[12]
				eachRrowMap.put("小規模評分", model.getSbScore() == null ? "" : model.getSbScore().toString());
				// J-110-0272 抽樣覆審
				// arr[13]
				String randomType = (Util.equals("Y", Util.trim(model.getIsSmallBuss())) ?
						LrsUtil.RANDOMTYPE_Z_純小規模營業人 : Util.trim(model.getElfRandomType()));
				eachRrowMap.put("抽樣", randomType);

				rows2.add(eachRrowMap);
			}

			if (true) {
				int i = 1;
				for (Map<String, String> tEachRrowMap : rows) {
					// arr[0] = String.valueOf(i) + " ";
					tEachRrowMap.put("序號", String.valueOf(i) + " ");
					i++;
				}

				int y = 1;
				for (Map<String, String> tEachRrowMap : rows2) {
					tEachRrowMap.put("序號", String.valueOf(y) + " ");
					y++;
				}
			}

			// ==============================
			int rowIdx = 0;
			// ==============================
			String headerMain = "";
			String headerLine1 = "";
			String headerLine2A = "";
			String headerLine2B = "";
			
			HSSFRow row1 = (HSSFRow) sheet1.getRow(rowIdx);
			if (row1 == null) {
			    row1 = (HSSFRow) sheet1.createRow(rowIdx);
			}
			row1.setHeight((short) (rowHeightAt14));
			
			HSSFRow row2 = (HSSFRow) sheet2.getRow(rowIdx);
			if (row2 == null) {
			    row2 = (HSSFRow) sheet2.createRow(rowIdx);
			}
			row2.setHeight((short) (rowHeightAt14));
			
			
			if (true) {
				int colIdx_row0 = 4;
				headerLine1 = "分行代碼：" + meta.getBranchId();
				
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row0 - 1));
				HSSFCell cell1 = (HSSFCell) row1.createCell(0);
				cell1.setCellValue(headerLine1);
				cell1.setCellStyle(cellFormatL_14);
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row0, totalColSize - 1));
				
				sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row0 - 1));
				HSSFCell cell2 = (HSSFCell) row2.createCell(0);
				cell2.setCellValue(headerLine1);
				cell2.setCellStyle(cellFormatL_14);
				sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row0, totalColSize - 1));

				String title = "";
				if (Util.equals(RetrialDocStatusEnum.編製中.getCode(),
						meta.getDocStatus())) {
					title = "經辦查詢名單檔";
				} else if (Util.equals(RetrialDocStatusEnum.待覆核.getCode(),
						meta.getDocStatus())) {
					title = "待主管覆核名單檔";
				} else {
					title = "覆審名單";
				}
				headerMain = Util.trim(branchService.getBranchName(meta
						.getBranchId())) + "【" + title + "】";

				HSSFCell cellMain1 = (HSSFCell) row1.createCell(colIdx_row0);
				cellMain1.setCellValue(headerMain);
				cellMain1.setCellStyle(cellFormatL_14);
				
				HSSFCell cellMain2 = (HSSFCell) row2.createCell(colIdx_row0);
				cellMain2.setCellValue(headerMain);
				cellMain2.setCellStyle(cellFormatL_14);
				
			}
			// ==============================
			if (true) {
				rowIdx = 1;
				HSSFRow row1_sheet1 = (HSSFRow) sheet1.getRow(rowIdx);
				if (row1_sheet1 == null) {
				    row1_sheet1 = (HSSFRow) sheet1.createRow(rowIdx);
				}
				row1_sheet1.setHeight((short) (rowHeightAt14));

				HSSFRow row1_sheet2 = (HSSFRow) sheet2.getRow(rowIdx);
				if (row1_sheet2 == null) {
				    row1_sheet2 = (HSSFRow) sheet2.createRow(rowIdx);
				}
				row1_sheet2.setHeight((short) (rowHeightAt14));
				
				int colIdx_row1 = 7;
				String label_defaultCTLDate = statusIn1_2 ? "預計覆審日期" : "覆審日期";
				headerLine2A = label_defaultCTLDate + "：" + Util.trim(TWNDate.toAD(meta.getDefaultCTLDate()));
				headerLine2B = "指定年月：" + LrsUtil.toStrYM(meta.getDataDate());
				
				// Sheet1
				// 第一個合併儲存格 (0 到 colIdx_row1-1)
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
				HSSFCell cell1_2A = (HSSFCell) row1_sheet1.createCell(0);
				cell1_2A.setCellValue(headerLine2A);
				cell1_2A.setCellStyle(cellFormatL_14);

				// 第二個合併儲存格 (colIdx_row1 到 totalColSize-1)
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
				HSSFCell cell1_2B = (HSSFCell) row1_sheet1.createCell(colIdx_row1);
				cell1_2B.setCellValue(headerLine2B);
				cell1_2B.setCellStyle(cellFormatL);

				// Sheet2
				// 第一個合併儲存格 (0 到 colIdx_row1-1)
				sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
				HSSFCell cell2_2A = (HSSFCell) row1_sheet2.createCell(0);
				cell2_2A.setCellValue(headerLine2A);
				cell2_2A.setCellStyle(cellFormatL_14);

				// 第二個合併儲存格 (colIdx_row1 到 totalColSize-1)
				sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
				HSSFCell cell2_2B = (HSSFCell) row1_sheet2.createCell(colIdx_row1);
				cell2_2B.setCellValue(headerLine2B);
				cell2_2B.setCellStyle(cellFormatL);
			}
			// ==============================
			rowIdx = 2;
			HSSFRow headerRow_sheet1 = (HSSFRow) sheet1.getRow(rowIdx);
			if (headerRow_sheet1 == null) {
			    headerRow_sheet1 = (HSSFRow) sheet1.createRow(rowIdx);
			}

			HSSFRow headerRow_sheet2 = (HSSFRow) sheet2.getRow(rowIdx);
			if (headerRow_sheet2 == null) {
			    headerRow_sheet2 = (HSSFRow) sheet2.createRow(rowIdx);
			}
			
			int colIdx = 0;
			for (String h : headerMap.keySet()) {
			    int colWidth = headerMap.get(h);
			    
			    sheet1.setColumnWidth(colIdx, colWidth * 256);
			    sheet2.setColumnWidth(colIdx, colWidth * 256);
			    
			    // Sheet1 - 建立標題儲存格
			    HSSFCell headerCell1 = (HSSFCell) headerRow_sheet1.createCell(colIdx);
			    headerCell1.setCellValue(h);
			    headerCell1.setCellStyle(cellFormatL_Border);
			    
			    // Sheet2 - 建立標題儲存格
			    HSSFCell headerCell2 = (HSSFCell) headerRow_sheet2.createCell(colIdx);
			    headerCell2.setCellValue(h);
			    headerCell2.setCellStyle(cellFormatL_Border);
			    
			    colIdx++;
			}
			// ==============================

			rowIdx = 3;
			int i = 0;
			for (Map<String, String> tEachRrowMap : rows) {
			    int currentRowIdx = rowIdx + i;
			    
			    // 建立當前資料行
			    HSSFRow dataRow = (HSSFRow) sheet1.getRow(currentRowIdx);
			    if (dataRow == null) {
			        dataRow = (HSSFRow) sheet1.createRow(currentRowIdx);
			    }
			    
			    int i_col = 0;
			    for (String h : headerMap.keySet()) {
			        // 取得資料值
			        String cellValue = MapUtils.getString(tEachRrowMap, h, "");
			        
			        // 建立儲存格
			        HSSFCell dataCell = (HSSFCell) dataRow.createCell(i_col);
			        dataCell.setCellValue(cellValue);
			        
			        // 設定格式 (第一欄用右對齊，其他欄用左對齊)
			        dataCell.setCellStyle(i_col == 0 ? cellFormatR_Border : cellFormatL_Border);
			        
			        i_col++;
			    }
			    
			    i++;
			}
			// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
			int rowIdx2 = rowIdx;
			int y = 0;
			for (Map<String, String> tEachRrowMap : rows2) {
			    HSSFRow row = sheet2.getRow(rowIdx2 + y);
			    if (row == null) {
			        row = sheet2.createRow(rowIdx2 + y);
			    }

			    int i_col = 0;
			    for (String h : headerMap.keySet()) {
			        // Create the cell, set its value and apply the appropriate style
			        HSSFCell cell = row.createCell(i_col);
			        cell.setCellValue(MapUtils.getString(tEachRrowMap, h, ""));
			        cell.setCellStyle(i_col == 0 ? cellFormatR_Border : cellFormatL_Border);
			        i_col++;
			    }
			    y++;
			}
			// ==============================
			// 因有些 L180M01B 會被略過，以 rows.size() 為主
			if (rows.size() == 0) {
				rowIdx += 2;
			} else {
				rowIdx += (rows.size());
			}
			if (rows2.size() == 0) {
				rowIdx2 += 2;
			} else {
				rowIdx2 += (rows2.size());
			}

			if (true) {
			    // 處理 sheet1
			    HSSFRow tempRow1 = sheet1.getRow(rowIdx);
			    if (tempRow1 == null) {
			    	tempRow1 = sheet1.createRow(rowIdx);
			    }
			    tempRow1.setHeight((short) rowHeightAt14);

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));

			    String strGenDate_random = "";
			    if (statusIn1_2) {
			        strGenDate_random += ("名單產生日期："
			                + Util.trim(TWNDate.toAD(meta.getGenerateDate())) + " / ");
			    }
			    strGenDate_random += ("報表亂碼：" + Util.trim(meta.getRandomCode()));

			    HSSFCell cell1 = tempRow1.createCell(0);
			    cell1.setCellValue(strGenDate_random);
			    cell1.setCellStyle(cellFormatR);

			    // 處理 sheet2
			    HSSFRow tempRow2 = sheet2.getRow(rowIdx2);
			    if (tempRow2 == null) {
			    	tempRow2 = sheet2.createRow(rowIdx2);
			    }
			    tempRow2.setHeight((short) rowHeightAt14);

			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));

			    HSSFCell cell2 = tempRow2.createCell(0);
			    cell2.setCellValue(strGenDate_random);
			    cell2.setCellStyle(cellFormatR);
			}

			if (statusIn1_2) {

				String[] notesArr = {
						"不覆審代碼說明：",
						"1.本行或同業主辦之聯貸案件，非擔任管理行。",
						"2.以國庫券、政府公債、央行儲蓄券、金融債券、定存單（含存款設質）、經同業保證之債券或票券等為十足擔保之授信案件。",
						"3.純進出押戶。4.對政府或政府所屬機關、學校之授信案件。",
						"5.拆放同業或對同業之融通。6.已列報為逾期放款或轉列催收款項之案件。7.銷戶。8.本次暫不覆審。",
						"9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。",
						"10.外勞保證中長期授信案件，已於新作後辦理一次覆審，且無增額、減額、變更條件或續約",
						"11.小規模營業人（央行C方案）：已抽樣覆審，於次年起免辦覆審；或未列於抽樣需覆審名單內",
                        "12.有效額度新臺幣一千萬元(含)以下且經信用保證基金保證成數七成(含)以上或十足擔保之不循環動用案件，免再辦理覆審。",
						"13.有效循環額度新臺幣一千萬元(含)以下且經信用保證基金保證成數七成(含)以上或十足擔保之需抽樣案件，本年度未列於抽樣需覆審名單內。",
						"A.非董事會(或常董會)權限案件", "B.參貸同業主辦之聯合授信案件",
						"C.國內營業單位辦理之境外公司授信案件（含對大陸地區授信）",
						"D.國外營業單位單獨承做之跨國(非當地國)授信案件", "E.非實地覆審主辦分行" };
				for (String s : notesArr) {
				    ++rowIdx;
				    // 確保 sheet1 的當前行存在
				    HSSFRow noteRow1 = sheet1.getRow(rowIdx);
				    if (noteRow1 == null) {
				    	noteRow1 = sheet1.createRow(rowIdx);
				    }
				    // 合併 sheet1 上的儲存格
				    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				    // 在 sheet1 上新增儲存格並設定內容與樣式
				    HSSFCell cell1 = noteRow1.createCell(0);
				    cell1.setCellValue(s);
				    cell1.setCellStyle(cellFormatL_10);

				    ++rowIdx2;
				    // 確保 sheet2 的當前行存在
				    HSSFRow noteRow2 = sheet2.getRow(rowIdx2);
				    if (noteRow2 == null) {
				    	noteRow2 = sheet2.createRow(rowIdx2);
				    }
				    // 合併 sheet2 上的儲存格
				    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				    // 在 sheet2 上新增儲存格並設定內容與樣式
				    HSSFCell cell2 = noteRow2.createCell(0);
				    cell2.setCellValue(s);
				    cell2.setCellStyle(cellFormatL_10);
				}

				// J-110-0272 抽樣覆審
				// 抽樣類別說明串在報表下方
				Locale locale = LMSUtil.getLocale();
				Map<String, String> randomTypeMap = codeTypeService.findByCodeType(
						"lms1815m01_elfRandomType", locale.toString());
				if (randomTypeMap.size() > 0) {
				    ++rowIdx;
				    ++rowIdx2;

				    // 處理 sheet1 的「抽樣類別代碼說明：」
				    HSSFRow row1_header = sheet1.getRow(rowIdx);
				    if (row1_header == null) {
				        row1_header = sheet1.createRow(rowIdx);
				    }
				    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				    HSSFCell cell1_header = row1_header.createCell(0);
				    cell1_header.setCellValue("抽樣類別代碼說明：");
				    cell1_header.setCellStyle(cellFormatL_10);

				    // 處理 sheet2 的「抽樣類別代碼說明：」
				    // 注意：原始碼 sheet1.addCell(new Label(0, rowIdx2, ...)) 應為 sheet2.addCell(new Label(0, rowIdx2, ...))
				    HSSFRow row2_header = sheet2.getRow(rowIdx2);
				    if (row2_header == null) {
				        row2_header = sheet2.createRow(rowIdx2);
				    }
				    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				    HSSFCell cell2_header = row2_header.createCell(0); // 這裡修正為 cell2_header
				    cell2_header.setCellValue("抽樣類別代碼說明：");
				    cell2_header.setCellStyle(cellFormatL_10);


				    for (String key : randomTypeMap.keySet()) {
				        String randomTypeStr = key + "." + randomTypeMap.get(key);
				        ++rowIdx;
				        ++rowIdx2;

				        // 處理 sheet1 的代碼說明
				        HSSFRow row1_detail = sheet1.getRow(rowIdx);
				        if (row1_detail == null) {
				            row1_detail = sheet1.createRow(rowIdx);
				        }
				        sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				        HSSFCell cell1_detail = row1_detail.createCell(0);
				        cell1_detail.setCellValue(randomTypeStr);
				        cell1_detail.setCellStyle(cellFormatL_10);

				        // 處理 sheet2 的代碼說明
				        HSSFRow row2_detail = sheet2.getRow(rowIdx2);
				        if (row2_detail == null) {
				            row2_detail = sheet2.createRow(rowIdx2);
				        }
				        sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				        HSSFCell cell2_detail = row2_detail.createCell(0);
				        cell2_detail.setCellValue(randomTypeStr);
				        cell2_detail.setCellStyle(cellFormatL_10);
				    }
				}
			}

			if (true) {// XXX				
				workbook.setPrintArea(workbook.getSheetIndex(sheet1), 
						0, totalColSize - 1, 2, ++rowIdx);
				
				
				workbook.setPrintArea(workbook.getSheetIndex(sheet2),
					    0, totalColSize - 1, 2, ++rowIdx2);

				if (true) {
					// --- 設定 sheet1 的頁首 ---
					HSSFHeader header = sheet1.getHeader();
					if (true) {
					    // 原始 JXL: _set_headerContents(contentsC, headerMain);
					    // 轉換為 POI: 直接在字串中嵌入字體控制碼
					    header.setCenter("&\"標楷體\"&14" + headerMain);
					}
					if (true) {
					    header.setLeft("&\"標楷體\"&14" + headerLine1 + "\n" + headerLine2A);
					}
					if (true) {
					    header.setRight("&\"標楷體\"&14" + "\n" + headerLine2B);
					}

					// --- 設定 sheet2 的頁首 ---
					HSSFHeader header2 = sheet2.getHeader();

					if (true) {
					    header2.setCenter("&\"標楷體\"&14" + headerMain);
					}
					if (true) {
					    header2.setLeft("&\"標楷體\"&14" + headerLine1 + "\n" + headerLine2A);
					}
					if (true) {
					    header2.setRight("&\"標楷體\"&14" + "\n" + headerLine2B);
					}
				}

				if (true) {
					// --- 設定 sheet1 的頁尾 ---
					HSSFFooter footer = sheet1.getFooter();
					// 企金戶第 &[頁碼] 頁，共 &[總頁數] 頁
					footer.setCenter("企金戶第 &P 頁 ，共 &N 頁");


					// --- 設定 sheet2 的頁尾 ---
					HSSFFooter footer2 = sheet2.getFooter();
					// 企金戶第 &[頁碼] 頁，共 &[總頁數] 頁
					footer2.setCenter("企金戶第 &P 頁 ，共 &N 頁");
				}
			}

			if (statusIn1_2) {

			} else {
				// '寫WorkSheet(2) EJCIC查詢ID 借款人、負責人、保證人 BGN
				gfnCTLGetRelateData(workbook, meta, l180m01b_list_Y);
			}

			try (FileOutputStream fileOut = new FileOutputStream(file)) {
			    workbook.write(fileOut);
			} catch (IOException e) {
				LOGGER.error(e.getMessage());
			} finally {
			    try {
			        if (workbook != null) {
			            workbook.close();
			        }
			    } catch (IOException e) {
			    	LOGGER.error(e.getMessage());
			    }
			}
		}
	}

	private Map<String, List<L180M01B>> getAllFilterXls(
			boolean statusIn1_2, List<L180M01B> src_list) {
		Map<String, List<L180M01B>> resultMap = new HashMap<String, List<L180M01B>>();
		List<L180M01B> listY = new ArrayList<L180M01B>();
		List<L180M01B> listN = new ArrayList<L180M01B>();
		List<L180M01B> l180m01b_list = _filter_xls(statusIn1_2, src_list);
		for (L180M01B model : l180m01b_list) {
			String nckdFlag = Util.nullToSpace(model.getNewNCkdFlag());
			if(Util.isNotEmpty(nckdFlag)){
				listN.add(model);
			} else {
				listY.add(model);
			}
		}
		resultMap.put("Y", listY);	//覆審名單
		resultMap.put("N", listN);	//不覆審名單
		return resultMap;
	}

	private List<L180M01B> _filter_xls(boolean statusIn1_2,
			List<L180M01B> src_list) {
		List<L180M01B> r = new ArrayList<L180M01B>();
		for (L180M01B model : src_list) {
			if (Util.notEquals(lrsConstants.docStatus1.不覆審,
					model.getDocStatus1())
					&& Util.equals(LrsUtil.NCKD_8_本次暫不覆審,
							model.getNewNCkdFlag())) {
				continue;
			}

			if (statusIn1_2) {

			} else {
				// 'J-099-0403 產生已覆核名單Exl 不用列印不覆審註記 8 本次暫不覆審
				if (Util.equals(lrsConstants.docStatus1.不覆審,
						model.getDocStatus1())
						&& Util.equals(LrsUtil.NCKD_8_本次暫不覆審,
								model.getNewNCkdFlag())) {
					continue;
				}
			}
			// ---
			r.add(model);
		}
		return r;
	}

	private void gfnCTLGetRelateData(HSSFWorkbook workbook, L180M01A meta,
			List<L180M01B> l180m01b_list){
		
		HSSFSheet sheet = workbook.createSheet("聯徵查詢名單-分行用");
		workbook.setSheetOrder("聯徵查詢名單-分行用", 2);

		HSSFFont headFont10 = workbook.createFont();
		headFont10.setFontName("標楷體");
		headFont10.setFontHeightInPoints((short) 10);

		HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
		cellFormatL_10.setFont(headFont10);
		cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL_10.setWrapText(true);
		// ======
		HSSFFont headFont12 = workbook.createFont();
		headFont12.setFontName("標楷體");
		headFont12.setFontHeightInPoints((short) 12);

		HSSFCellStyle cellFormatL = workbook.createCellStyle();
		cellFormatL.setFont(headFont12);
		cellFormatL.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL.setWrapText(true);

		HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
		cellFormatL_Border.setFont(headFont12);
		cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL_Border.setWrapText(true);
		cellFormatL_Border.setBorderTop(BorderStyle.THIN);
		cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
		cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
		cellFormatL_Border.setBorderRight(BorderStyle.THIN);

		// ======
		HSSFFont headFont14 = workbook.createFont();
		headFont14.setFontName("標楷體");
		headFont14.setFontHeightInPoints((short) 14);

		HSSFCellStyle cellFormatL_14 = workbook.createCellStyle();
		cellFormatL_14.setFont(headFont14);
		cellFormatL_14.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL_14.setWrapText(true);

		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("名單序號", 8);
		headerMap.put("借款人統編", 13);
		headerMap.put("關係" + EloanConstants.LINE_BREAK + "(不含擔保品提供人)", 15);
		headerMap.put("MEGA統編", 13);
		headerMap.put("重覆序號", 4);
		headerMap.put("聯徵查詢企業戶" + EloanConstants.LINE_BREAK + "(OBU為虛擬統編)", 18);
		headerMap.put("聯徵查詢個人戶", 18);
		headerMap.put("戶名", 28);
		headerMap.put("額度序號", 15);

		int totalColSize = headerMap.size();
		// ==============================
		int rowIdx = 0;
		// ==============================
		if (true) {
			HSSFRow row = sheet.getRow(rowIdx);
			if (row == null) {
			    row = sheet.createRow(rowIdx);
			}
			row.setHeight((short) (rowHeightAt14));

			int colIdx_row0 = 4;

			sheet.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row0 - 1));
			HSSFCell cell1 = row.createCell(0);
			cell1.setCellValue("分行代碼：" + meta.getBranchId());
			cell1.setCellStyle(cellFormatL_14);

			sheet.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row0, totalColSize - 1));

			String title = "聯徵查詢名單";
			HSSFCell cell2 = row.createCell(colIdx_row0);
			cell2.setCellValue(Util.trim(branchService.getBranchName(meta.getBranchId())) + "【" + title + "】");
			cell2.setCellStyle(cellFormatL_14);
		}
		// ==============================
		if (true) {
			rowIdx = 1;
			HSSFRow row = sheet.getRow(rowIdx);
			if (row == null) {
			    row = sheet.createRow(rowIdx);
			}
			row.setHeight((short) (rowHeightAt14));

			int colIdx_row1 = 7;

			sheet.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
			String label_defaultCTLDate = "覆審日期";
			HSSFCell cell1 = row.createCell(0);
			cell1.setCellValue(label_defaultCTLDate + "：" + Util.trim(TWNDate.toAD(meta.getDefaultCTLDate())));
			cell1.setCellStyle(cellFormatL_14);

			sheet.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
			HSSFCell cell2 = row.createCell(colIdx_row1);
			cell2.setCellValue("指定年月：" + LrsUtil.toStrYM(meta.getDataDate()));
			cell2.setCellStyle(cellFormatL_10);
		}
		// ==============================
		rowIdx = 2;
		int colIdx = 0;
		HSSFRow row = sheet.getRow(rowIdx);
		if (row == null) {
		    row = sheet.createRow(rowIdx);
		}

		for (String h : headerMap.keySet()) {
		    int colWidth = headerMap.get(h);
		    sheet.setColumnWidth(colIdx, colWidth * 256);

		    HSSFCell cell = row.createCell(colIdx);
		    cell.setCellValue(h);
		    cell.setCellStyle(cellFormatL_Border);

		    colIdx++;
		}
		// ==============================
		List<String[]> rows = new ArrayList<String[]>();
		int seq = 1;
		for (L180M01B model : l180m01b_list) {
			// 處理借款人自己
			if (true) {
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}

				String megaId = Util.trim(model.getCustId());
				String megaDupno = Util.trim(model.getDupNo());

				arr[0] = String.valueOf(seq);
				arr[1] = Util.trim(model.getCustId());
				_set_megaid(arr, megaId, megaDupno,
						Util.trim(model.getElfCName()));

				rows.add(arr);// 同1個seq會有N筆
			}
			// 處理負責人
			if (true) {

				Map<String, Object> m_CMFCUS25 = misElCUS25Service.findByPk(
						model.getCustId(), model.getDupNo());
				String chairManID = Util.trim(MapUtils.getString(m_CMFCUS25,
						"SUP1ID"));
				String chairManDupcode = "";
				String chairManName = "";
				if (Util.isNotEmpty(chairManID)) {
					chairManDupcode = Util.trim(MapUtils.getString(m_CMFCUS25,
							"SUP1DUPNO"));
					chairManName = LMSUtil.getNotEmptyVal_str(m_CMFCUS25,
							"SUP1CNM", "SUP3CNM");
				}

				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				arr[0] = String.valueOf(seq);
				arr[1] = Util.trim(model.getCustId());
				arr[2] = "負責人";
				_set_megaid(arr, chairManID, chairManDupcode, chairManName);

				rows.add(arr);
			}
			// 處理連保人
			if (true) {
				Set<String> cntrNoList = new HashSet<String>();
				for (Map<String, Object> map : misLNF022Service
						.getLNF022_forCtl(model.getCustId(), model.getDupNo(),
								meta.getBranchId())) {
					cntrNoList.add(Util.trim(map.get("QUOTANO")));
				}
				for (String cntrNo : cntrNoList) {
					for (Map<String, Object> map : misELLNGTEEService
							.findByLrs(model.getCustId(), model.getDupNo(),
									cntrNo)) {
						String megaId = Util.trim(map.get("LNGEID"));
						String megaDupno = Util.trim(map.get("DUPNO1"));
						String LNGENM = Util.trim(map.get("LNGENM"));
						String LNGEFLAG = Util.trim(map.get("LNGEFLAG"));

						String[] arr = new String[totalColSize];
						for (int i_col = 0; i_col < totalColSize; i_col++) {
							arr[i_col] = "";
						}

						arr[0] = String.valueOf(seq);
						arr[1] = Util.trim(model.getCustId());

						String lngeFlagDesc = LNGEFLAG;
						if (Util.equals(UtilConstants.lngeFlag.共同借款人, LNGEFLAG)) {
							lngeFlagDesc = "共同借款人";
						} else if (Util.equals(UtilConstants.lngeFlag.共同發票人,
								LNGEFLAG)) {
							lngeFlagDesc = "共同發票人";
						} else if (Util.equals(UtilConstants.lngeFlag.票據債務人,
								LNGEFLAG)) {
							lngeFlagDesc = "票據債務人";
						} else if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
								LNGEFLAG)) {
							lngeFlagDesc = "連帶保證人";
						} else if (Util.equals(UtilConstants.lngeFlag.連帶借款人,
								LNGEFLAG)) {
							lngeFlagDesc = "連帶借款人，連帶債務人，擔保品提供人兼連帶債務人";
						} else if (Util.equals(UtilConstants.lngeFlag.ㄧ般保證人,
								LNGEFLAG)) {
							lngeFlagDesc = "一般保證人";
						} else {
							lngeFlagDesc = "保證人";
						}
						arr[2] = lngeFlagDesc;
						_set_megaid(arr, megaId, megaDupno, LNGENM);
						arr[8] = cntrNo;

						rows.add(arr);// 同1個seq會有N筆
					}
				}
			}
			// ---
			seq++;
		}

		rowIdx = 3;
		int i = 0;
		for (String[] arr : rows) {
		    HSSFRow tempRow = sheet.getRow(rowIdx + i);
		    if (tempRow == null) {
		    	tempRow = sheet.createRow(rowIdx + i);
		    }

		    int colLen = arr.length;
		    for (int i_col = 0; i_col < colLen; i_col++) {
		        HSSFCell cell = tempRow.createCell(i_col);
		        cell.setCellValue(arr[i_col]);
		        cell.setCellStyle(cellFormatL_Border);
		    }
		    i++;
		}
	}

	private void _set_megaid(String[] arr, String megaId, String megaDupno,
			String name) {

		arr[3] = megaId;
		arr[4] = megaDupno;

		// '聯徵查詢ID
		if (Util.trim(megaId).length() == 10
				&& !Util.isNumeric(StringUtils.substring(megaId, 0, 1))
				&& !LrsUtil.isCustId_Z(megaId)) {
			// '個人戶
			arr[6] = megaId;
		} else {
			// '企業戶
			String JCICTAXNO = "";
			if (Util.trim(megaId).length() == 10
					&& ((!Util.isNumeric(StringUtils.substring(megaId, 0, 1)) && !Util
							.isNumeric(StringUtils.substring(megaId, 1, 2))) && LrsUtil
							.isCustId_Z(megaId))) {
				for (Map<String, Object> map : misElCUS25Service
						.findById(megaId)) {
					JCICTAXNO = Util.trim(map.get("CM25_JCIC_TAXNO"));
				}
				arr[5] = JCICTAXNO; // '聯徵查詢-企業戶-OBU戶為虛擬統編
			} else {
				arr[5] = megaId;
			}
		}
		arr[7] = name;
	}

	@Override
	public boolean gfnGenCTLListChkExcel(L180M01A meta) {
		boolean r = false;

		String fieldId = LrsUtil.ATTCH_L180M01A_1;

		String fileName = LrsUtil.getExcelName(meta, fieldId);
		try {
			List<DocFile> docFiles = fileService.findByIDAndName(
					meta.getMainId(), fieldId, "");

			if (docFiles != null && !docFiles.isEmpty()) {
				for (DocFile file : docFiles) {
					fileService.clean(file.getOid());
				}
			}

			DocFile docFile = new DocFile();
			docFile.setBranchId(meta.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(meta.getMainId());
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(fieldId);
			docFile.setSrcFileName(fileName);
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			docFile.setDeletedTime(null);
			fileService.save(docFile, false);

			create_listChk(meta, docFile);
			// ---
			r = true;
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		}
		return r;
	}

	private void create_listChk(L180M01A meta, DocFile docFile)
			throws IOException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> map_mowDesc = retrialService.get_lrs_MowType_1();

		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;
		// J-110-0396 配合授審處，E-Loan企金授信覆審系統修改每月需覆審名單報表，優化由系統排除免覆審名單等事項。
		HSSFSheet sheet2 = null;
		File file = null;
		String filename = LMSUtil.getUploadFilePath(meta.getOwnBrId(),
				meta.getMainId(), docFile.getFieldId());

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_主辦覆審);

		boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_自辦覆審);

		boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(meta.getOwnBrId(),
				LrsUtil.CTLTYPE_價金履約);

		if (true) {
			File directory = new File(filename);
			directory.mkdirs(); 
			file = new File(filename + "/" + docFile.getOid() + ".xls");
			// ---
			workbook = new HSSFWorkbook(); 
			sheet1 = workbook.createSheet("Sheet1");
			sheet2 = workbook.createSheet("不覆審名單");
			workbook.setSheetOrder("Sheet1", 0);
			workbook.setSheetOrder("不覆審名單", 1);
			
			PrintSetup ps1 = sheet1.getPrintSetup();
			ps1.setPaperSize(PrintSetup.A4_PAPERSIZE);
			ps1.setFitWidth((short) 1);
			ps1.setLandscape(false); // false 代表縱向

			PrintSetup ps2 = sheet2.getPrintSetup();
			ps2.setPaperSize(PrintSetup.A4_PAPERSIZE);
			ps2.setFitWidth((short) 1);
			ps2.setLandscape(false); // false 代表縱向

			HSSFFont headFont10 = workbook.createFont();
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);

			HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
			// ======
			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);

			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			cellFormatL.setFont(headFont12);
			cellFormatL.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);

			HSSFCellStyle cellFormatR = workbook.createCellStyle();
			cellFormatR.setFont(headFont12);
			cellFormatR.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);

			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);

			HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setVerticalAlignment(VerticalAlignment.TOP);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);

			// ======
			HSSFFont headFont14 = workbook.createFont();
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);

			// 儲存格樣式 cellFormatL_14 (左對齊、自動換行)
			HSSFCellStyle cellFormatL_14 = workbook.createCellStyle();
			cellFormatL_14.setFont(headFont14);
			cellFormatL_14.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_14.setWrapText(true);

			// 儲存格樣式 cellFormatC_14 (置中對齊、自動換行)
			HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER); // 注意這裡從 Alignment.CENTRE 變為 HorizontalAlignment.CENTER
			cellFormatC_14.setWrapText(true);
			// ======
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("序號", 6);
			headerMap.put("覆審序號", 6);
			headerMap.put("統一編號", 13);
			if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
				headerMap.put("覆審名單種類", 6);
			}
			headerMap.put("信用評等", 15);
			headerMap.put("符合授信額度標準", 6);
			headerMap.put("客戶名稱", 25);
			headerMap.put("上傳控制檔或更新前次覆審日", 9);
			headerMap.put("覆審週期", 6);
			headerMap.put("覆審人員", 12);
			headerMap.put("備註", 15);
			headerMap.put("不覆審註記", 6);
			headerMap.put("覆審異常註記", 9);

			if (ctlTypeA) {
				headerMap.put("實地覆審報告表", 6); // J-105-0287-001 修改Web
			} // e-Loan國內企金授信覆審系統

			// J-109-0313 小規模覆審
			headerMap.put("純小規模註記", 6);
			headerMap.put("小規模評分", 6);
			headerMap.put("小規模變更註記", 6);
			// J-110-0272 抽樣覆審
			headerMap.put("抽樣", 6);

			int totalColSize = headerMap.size();

			// List<String[]> rows = new ArrayList<String[]>();
			List<Map<String, String>> rows = new ArrayList<Map<String, String>>();
			List<Map<String, String>> rows2 = new ArrayList<Map<String, String>>();
			List<L180M01B> l180m01b_list = retrialService
					.findL180M01BDefaultOrder(meta.getMainId());
			List<RO412> _NCKDList = new ArrayList<RO412>();
			List<L180M01B> _NCKDList2 = new ArrayList<L180M01B>();
			List<String> totCTL = new ArrayList<String>();
			for (L180M01B model : l180m01b_list) {
				if (Util.notEquals(model.getNewNCkdFlag(),
						LrsUtil.NCKD_8_本次暫不覆審)) {
					RO412 ro412 = null;
					ELF412 elf412 = null;
					ELF412B elf412b = null;
					ELF412C elf412c = null;

					String org_Nckd_Flag = "";
					if (Util.equals(Util.trim(model.getCtlType()),
							LrsUtil.CTLTYPE_自辦覆審)) {
						elf412b = misELF412BService.findByPk(
								meta.getBranchId(), model.getCustId(),
								model.getDupNo());
						if (elf412b == null) {
							continue;
						}

						// 因為需要重算覆審週期，所以會呼叫gfnCTL_Caculate_ELF412，但是
						// gfnCTL_Caculate_ELF412 處理nckdFlag
						// 為6、7時，會多判斷ELF412的ELF412_CSTAT或ELF412_CANCELDT欄位，
						// 如果nckdFlag=6，但是ELF412_CSTAT=0，則會把nckdFlag清掉，掉
						// 但是ELF412_CSTAT與ELF412_CANCELDT無法從覆審控制擋維護調整，
						// 所以人工維護nckdFlag時，CSTAT 與
						// CANCELDT會有不一致的情況，導致報表顯示不覆審註記為空白。
						// 所以產生報表時，還是以目前DB2中ELF412的nckdFlag為主，不要用
						// gfnCTL_Caculate_ELF412調整過的nckdFlag。
						// PS.nckdflag=8(本次暫不覆審)， gfnCTL_Caculate_ELF412
						// 會把nckdflag清成空白，所以可以用調整後的
						org_Nckd_Flag = elf412b.getElf412b_nckdFlag();
						retrialService.gfnCTL_Caculate_ELF412B(elf412b);
						if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
								elf412b.getElf412b_nckdFlag())) {
							elf412b.setElf412b_nckdFlag(org_Nckd_Flag);
						}

						ro412 = new RO412(elf412b);

					} else if(Util.equals(Util.trim(model.getCtlType()), LrsUtil.CTLTYPE_價金履約)){
						elf412c = misELF412CService.findByPk(
								meta.getBranchId(), model.getCustId(),
								model.getDupNo());
						if (elf412c == null) {
							continue;
						}

						org_Nckd_Flag = elf412c.getElf412c_nckdFlag();
						retrialService.gfnCTL_Caculate_ELF412C(elf412c);
						if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
								elf412c.getElf412c_nckdFlag())) {
							elf412c.setElf412c_nckdFlag(org_Nckd_Flag);
						}

						ro412 = new RO412(elf412c);
					} else {
						elf412 = misELF412Service.findByPk(meta.getBranchId(),
								model.getCustId(), model.getDupNo());
						if (elf412 == null) {
							continue;
						}

						// 因為需要重算覆審週期，所以會呼叫gfnCTL_Caculate_ELF412，但是
						// gfnCTL_Caculate_ELF412 處理nckdFlag
						// 為6、7時，會多判斷ELF412的ELF412_CSTAT或ELF412_CANCELDT欄位，
						// 如果nckdFlag=6，但是ELF412_CSTAT=0，則會把nckdFlag清掉，掉
						// 但是ELF412_CSTAT與ELF412_CANCELDT無法從覆審控制擋維護調整，
						// 所以人工維護nckdFlag時，CSTAT 與
						// CANCELDT會有不一致的情況，導致報表顯示不覆審註記為空白。
						// 所以產生報表時，還是以目前DB2中ELF412的nckdFlag為主，不要用
						// gfnCTL_Caculate_ELF412調整過的nckdFlag。
						// PS.nckdflag=8(本次暫不覆審)， gfnCTL_Caculate_ELF412
						// 會把nckdflag清成空白，所以可以用調整後的
						org_Nckd_Flag = elf412.getElf412_nckdFlag();
						retrialService.gfnCTL_Caculate_ELF412(elf412, null);
						if (Util.notEquals(LrsUtil.NCKD_8_本次暫不覆審,
								elf412.getElf412_nckdFlag())) {
							elf412.setElf412_nckdFlag(org_Nckd_Flag);
						}

						ro412 = new RO412(elf412);

					}

					/* 2021/12/08 授審連喬凱說  實際有覆審的都往前搬到有覆審名單
					String nckdFlag = Util.trim(ro412.get_nckdFlag());
					if (Util.isNotEmpty(nckdFlag)
							&& Util.notEquals(nckdFlag, "0")) {
						_NCKDList.add(ro412);
						_NCKDList2.add(model);
						continue;
					}
					*/
					if(!this.isCtlFound(model)){
						_NCKDList.add(ro412);
						_NCKDList2.add(model);
						continue;
					}
					gfnGenCTLListChkExcel_writeExcel(rows, totalColSize, meta,
							model, ro412, map_mowDesc, totCTL, headerMap,
							ctlTypeA, ctlTypeB, ctlTypeC);
				}
			}
			// '不覆審的排在最後面
			if (_NCKDList.size() > 0) {
				int cnt = _NCKDList.size();
				for (int i = 0; i < cnt; i++) {
					gfnGenCTLListChkExcel_writeExcel(rows2, totalColSize, meta,
							_NCKDList2.get(i), _NCKDList.get(i), map_mowDesc,
							totCTL, headerMap, ctlTypeA, ctlTypeB, ctlTypeC);
				}
			}

			if (true) {
				int i = 1;
				for (Map<String, String> tEachRrowMap : rows) {
					// arr[0] = String.valueOf(i);
					tEachRrowMap.put("序號", String.valueOf(i));
					i++;
				}

				int y = 1;
				for (Map<String, String> tEachRrowMap : rows2) {
					tEachRrowMap.put("序號", String.valueOf(y));
					y++;
				}
			}
			// ==============================
			int rowIdx = 0;
			// ==============================
			String headerMain = "";
			String headerLine1A = "";
			String headerLine1B = "";
			if (true) {
				HSSFRow row1 = sheet1.getRow(rowIdx);
				if (row1 == null) {
				    row1 = sheet1.createRow(rowIdx);
				}
				row1.setHeight((short) (rowHeightAt14));

				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				headerMain = Util.trim(meta.getBranchId()
				        + " "
				        + Util.trim(branchService.getBranchName(meta.getBranchId())) + "驗證名單");
				HSSFCell cell1 = row1.createCell(0);
				cell1.setCellValue(headerMain);
				cell1.setCellStyle(cellFormatC_14);

				HSSFRow row2 = sheet2.getRow(rowIdx);
				if (row2 == null) {
				    row2 = sheet2.createRow(rowIdx);
				}
				row2.setHeight((short) (rowHeightAt14));

				sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				HSSFCell cell2 = row2.createCell(0);
				cell2.setCellValue(headerMain);
				cell2.setCellStyle(cellFormatC_14);
			}
			// ==============================
			if (true) {
			    rowIdx = 1;

			    HSSFRow row1 = sheet1.getRow(rowIdx);
			    if (row1 == null) {
			        row1 = sheet1.createRow(rowIdx);
			    }
			    row1.setHeight((short) (rowHeightAt14));

			    int colIdx_row1 = 8;
			    headerLine1A = "覆審日期" + "：" + Util.trim(TWNDate.toAD(meta.getDefaultCTLDate()));
			    headerLine1B = "驗證名單產生人員：" + Util.trim(userInfoService.getUserName(user.getUserId()));

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
			    HSSFCell cell1_1 = row1.createCell(0);
			    cell1_1.setCellValue(headerLine1A);
			    cell1_1.setCellStyle(cellFormatL_14);

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
			    HSSFCell cell1_2 = row1.createCell(colIdx_row1);
			    cell1_2.setCellValue(headerLine1B);
			    cell1_2.setCellStyle(cellFormatL);

			    HSSFRow row2 = sheet2.getRow(rowIdx);
			    if (row2 == null) {
			        row2 = sheet2.createRow(rowIdx);
			    }
			    row2.setHeight((short) (rowHeightAt14));

			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
			    HSSFCell cell2_1 = row2.createCell(0);
			    cell2_1.setCellValue(headerLine1A);
			    cell2_1.setCellStyle(cellFormatL_14);

			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
			    HSSFCell cell2_2 = row2.createCell(colIdx_row1);
			    cell2_2.setCellValue(headerLine1B);
			    cell2_2.setCellStyle(cellFormatL);
			}
			// ==============================
			rowIdx = 2;
			int colIdx = 0;

			HSSFRow row1Header = sheet1.getRow(rowIdx);
			if (row1Header == null) {
		        row1Header = sheet1.createRow(rowIdx);
		    }
			HSSFRow row2Header = sheet2.getRow(rowIdx);
			if (row2Header == null) {
		        row2Header = sheet2.createRow(rowIdx);
		    }

			for (String h : headerMap.keySet()) {
			    int colWidth = headerMap.get(h);
			    sheet1.setColumnWidth(colIdx, colWidth * 256);
			    sheet2.setColumnWidth(colIdx, colWidth * 256);

			    HSSFCell cell1 = row1Header.createCell(colIdx);
			    cell1.setCellValue(h);
			    cell1.setCellStyle(cellFormatL_Border);

			    HSSFCell cell2 = row2Header.createCell(colIdx);
			    cell2.setCellValue(h);
			    cell2.setCellStyle(cellFormatL_Border);

			    colIdx++;
			}
			// ==============================
			rowIdx = 3;
			int i = 0;

			for (Map<String, String> tEachRrowMap : rows) {
			    HSSFRow dataRow1 = sheet1.getRow(rowIdx + i);
			    dataRow1 = sheet1.createRow(rowIdx + i);

			    int i_col = 0;
			    for (String h : headerMap.keySet()) {
			        HSSFCell cell = dataRow1.createCell(i_col);
			        cell.setCellValue(MapUtils.getString(tEachRrowMap, h, ""));
			        cell.setCellStyle(cellFormatL_Border);
			        i_col++;
			    }
			    i++;
			}
			
			int rowIdx2 = rowIdx;
			int y = 0;
			for (Map<String, String> tEachRrowMap : rows2) {
			    HSSFRow dataRow2 = sheet2.getRow(rowIdx2 + y);
			    dataRow2 = sheet2.createRow(rowIdx2 + y);

			    int i_col = 0;
			    for (String h : headerMap.keySet()) {
			        HSSFCell cell = dataRow2.createCell(i_col);
			        cell.setCellValue(MapUtils.getString(tEachRrowMap, h, ""));
			        cell.setCellStyle(cellFormatL_Border);
			        i_col++;
			    }
			    y++;
			}
			// ==============================
			rowIdx += (rows.size());
			rowIdx2 += (rows2.size());
			if (true) {
			    int colIdx_row1_bottom = 6;

			    HSSFRow row1_bottom = sheet1.getRow(rowIdx);
			    if (row1_bottom == null) {
			        row1_bottom = sheet1.createRow(rowIdx);
			    }
			    row1_bottom.setHeight((short) (rowHeightAt14));

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1_bottom - 1));
			    HSSFCell cell1_bottom_1 = row1_bottom.createCell(0);
			    cell1_bottom_1.setCellValue("實際覆審 " + totCTL.size() + " 件數");
			    cell1_bottom_1.setCellStyle(cellFormatL);

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1_bottom, totalColSize - 1));
			    HSSFCell cell1_bottom_2 = row1_bottom.createCell(colIdx_row1_bottom);
			    cell1_bottom_2.setCellValue("報表亂碼：" + Util.trim(meta.getRandomCode()));
			    cell1_bottom_2.setCellStyle(cellFormatL);

			    HSSFRow row2_bottom = sheet2.getRow(rowIdx2);
			    if (row2_bottom == null) {
			        row2_bottom = sheet2.createRow(rowIdx2);
			    }
			    row2_bottom.setHeight((short) (rowHeightAt14));

			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, colIdx_row1_bottom - 1));
			    HSSFCell cell2_bottom_1 = row2_bottom.createCell(0);
			    cell2_bottom_1.setCellValue("");
			    cell2_bottom_1.setCellStyle(cellFormatL);

			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, colIdx_row1_bottom, totalColSize - 1));
			    HSSFCell cell2_bottom_2 = row2_bottom.createCell(colIdx_row1_bottom);
			    cell2_bottom_2.setCellValue("報表亂碼：" + Util.trim(meta.getRandomCode()));
			    cell2_bottom_2.setCellStyle(cellFormatL);

			    ++rowIdx;
			    ++rowIdx2;

			    HSSFRow row1_sampling = sheet1.getRow(rowIdx);
			    if (row1_sampling == null) {
			        row1_sampling = sheet1.createRow(rowIdx);
			    }
			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
			    BigDecimal samplingRate = retrialService.cauculateSamplingRate(meta.getMainId(), "XLS");
			    String samplingRateStr = Util.isEmpty(Util.trim(samplingRate)) ? "" :
			            samplingRate.setScale(2, BigDecimal.ROUND_DOWN).toString();
			    HSSFCell cell1_sampling = row1_sampling.createCell(0);
			    cell1_sampling.setCellValue("小規模營業人覆審抽樣率：" + samplingRateStr + "％");
			    cell1_sampling.setCellStyle(cellFormatL_10);

//			    HSSFRow row2_sampling = sheet2.getRow(rowIdx2);
//			    if (row2_sampling == null) {
//			        row2_sampling = sheet2.createRow(rowIdx2);
//			    }
//			    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
//			    HSSFCell cell2_sampling = row2_sampling.createCell(0); // 假設 sheet2 也顯示相同內容
//			    cell2_sampling.setCellValue("小規模營業人覆審抽樣率：" + samplingRateStr + "％");
//			    cell2_sampling.setCellStyle(cellFormatL_10);

			    // J-110-0272 抽樣覆審
			    ++rowIdx;
			    HSSFRow row1_random = sheet1.getRow(rowIdx);
			    if (row1_random == null) {
			        row1_random = sheet1.createRow(rowIdx);
			    }
			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
			    BigDecimal randomSamplingRate = retrialService.cauculateRandomSamplingRate(meta.getMainId(), "XLS");
			    String randomSamplingRateStr = Util.isEmpty(Util.trim(randomSamplingRate)) ? "" :
			            randomSamplingRate.setScale(2, BigDecimal.ROUND_DOWN).toString();
			    HSSFCell cell1_random = row1_random.createCell(0);
			    cell1_random.setCellValue("覆審隨機抽樣率：" + randomSamplingRateStr + "％");
			    cell1_random.setCellStyle(cellFormatL_10);
			}
			
			if (true) {
				String[] notesArr = {
						"不覆審代碼說明：",
						"1.本行或同業主辦之聯貸案件，非擔任管理行。",
						"2.以國庫券、政府公債、央行儲蓄券、金融債券、定存單（含存款設質）、經同業保證之債券或票券等為十足擔保之授信案件。",
						"3.純進出押戶。4.對政府或政府所屬機關、學校之授信案件。",
						"5.拆放同業或對同業之融通。6.已列報為逾期放款或轉列催收款項之案件。7.銷戶。8.本次暫不覆審。",
						"9.已專案核准免辦理覆審之房屋仲介價金履約保證案件。",
						"10.外勞保證中長期授信案件，已於新作後辦理一次覆審，且無增額、減額、變更條件或續約",
                        "11.小規模營業人（央行C方案）：已抽樣覆審，於次年起免辦覆審；或未列於抽樣需覆審名單內",
						"12.有效額度新臺幣一千萬元(含)以下且經信用保證基金保證成數七成(含)以上或十足擔保之不循環動用案件，免再辦理覆審。",
						"13.有效循環額度新臺幣一千萬元(含)以下且經信用保證基金保證成數七成(含)以上或十足擔保之需抽樣案件，本年度未列於抽樣需覆審名單內。",
						"A.非董事會(或常董會)權限案件", "B.參貸同業主辦之聯合授信案件",
						"C.國內營業單位辦理之境外公司授信案件（含對大陸地區授信）",
						"D.國外營業單位單獨承做之跨國(非當地國)授信案件", "E.非實地覆審主辦分行" };
				
				for (String s : notesArr) {
				    ++rowIdx;
				    // 取得或創建 sheet1 上的當前列
				    HSSFRow row1 = sheet1.getRow(rowIdx);
				    if (row1 == null) {
				        row1 = sheet1.createRow(rowIdx);
				    }
				    // 合併 sheet1 上的儲存格範圍
				    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				    // 在 sheet1 上創建儲存格，設定其值和樣式
				    HSSFCell cell1 = row1.createCell(0);
				    cell1.setCellValue(s);
				    cell1.setCellStyle(cellFormatL_10);

				    ++rowIdx2;
				    // 取得或創建 sheet2 上的當前列
				    HSSFRow row2 = sheet2.getRow(rowIdx2);
				    if (row2 == null) {
				        row2 = sheet2.createRow(rowIdx2);
				    }
				    // 合併 sheet2 上的儲存格範圍
				    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				    // 在 sheet2 上創建儲存格，設定其值和樣式
				    HSSFCell cell2 = row2.createCell(0);
				    cell2.setCellValue(s);
				    cell2.setCellStyle(cellFormatL_10);
				}
			}

			// J-110-0272 抽樣覆審
			// 抽樣類別說明串在報表下方
			Locale locale = LMSUtil.getLocale();
			Map<String, String> randomTypeMap = codeTypeService.findByCodeType(
					"lms1815m01_elfRandomType", locale.toString());
			if(randomTypeMap.size() > 0) {
				++rowIdx;
				HSSFRow row1 = sheet1.getRow(rowIdx);
				if (row1 == null) {
				    row1 = sheet1.createRow(rowIdx);
				}
				sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				HSSFCell cell1 = row1.createCell(0);
				cell1.setCellValue("抽樣類別代碼說明：");
				cell1.setCellStyle(cellFormatL_10);

				++rowIdx2;
				HSSFRow row2 = sheet2.getRow(rowIdx2);
				if (row2 == null) {
				    row2 = sheet2.createRow(rowIdx2);
				}
				sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				HSSFCell cell2 = row2.createCell(0);
				cell2.setCellValue("抽樣類別代碼說明：");
				cell2.setCellStyle(cellFormatL_10);
				
				for (String key : randomTypeMap.keySet()) {
				    String randomTypeStr = key + "." + randomTypeMap.get(key);

				    ++rowIdx;
				    HSSFRow randomRow1 = sheet1.getRow(rowIdx);
				    if (randomRow1 == null) {
				    	randomRow1 = sheet1.createRow(rowIdx);
				    }
				    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
				    HSSFCell randomCell1 = randomRow1.createCell(0);
				    randomCell1.setCellValue(randomTypeStr);
				    randomCell1.setCellStyle(cellFormatL_10);

				    ++rowIdx2;
				    HSSFRow randomRow2 = sheet2.getRow(rowIdx2);
				    if (randomRow2 == null) {
				    	randomRow2 = sheet2.createRow(rowIdx2);
				    }
				    sheet2.addMergedRegion(new CellRangeAddress(rowIdx2, rowIdx2, 0, totalColSize - 1));
				    HSSFCell randomCell2 = randomRow2.createCell(0);
				    randomCell2.setCellValue(randomTypeStr);
				    randomCell2.setCellStyle(cellFormatL_10);
				}
			}

			// 設定 Excel 的頁面縮放比
			ps1.setScale((short) 95); // 95% 縮放
			ps2.setScale((short) 95); // 95% 縮放

			if (true) {// XXX 覆審名單驗證檔
				workbook.setPrintArea(workbook.getSheetIndex(sheet1), 
						0, totalColSize - 1, 2, ++rowIdx);
			    workbook.setPrintArea(workbook.getSheetIndex(sheet2), 
			    		0, totalColSize - 1, 2, ++rowIdx2);

				if (true) {
					HSSFHeader header = sheet1.getHeader();

					if (true) {
					    header.setCenter(headerMain);
					}
					if (true) {
					    header.setLeft("\n" + headerLine1A + Util.addSpaceWithValueFixCht("", 52) + headerLine1B);
					}
					
					
					HSSFHeader header2 = sheet2.getHeader();

					if (true) {
					    header2.setCenter(headerMain);
					}
					if (true) {
					    header2.setLeft("\n" + headerLine1A + Util.addSpaceWithValueFixCht("", 52) + headerLine1B);
					}
				}

				if (true) {
					HSSFFooter footer = sheet1.getFooter();
					footer.setCenter("企金戶第 " + HeaderFooter.page() + " 頁 ，共 " + HeaderFooter.numPages() + " 頁");

					HSSFFooter footer2 = sheet2.getFooter();
					footer2.setCenter("企金戶第 " + HeaderFooter.page() + " 頁 ，共 " + HeaderFooter.numPages() + " 頁");
				}
			}

			try (FileOutputStream fileOut = new FileOutputStream(file)) {
			    workbook.write(fileOut);
			} catch (IOException e) {
				LOGGER.error(e.getMessage());
			} finally {
			    try {
			        if (workbook != null) {
			            workbook.close();
			        }
			    } catch (IOException e) {
			    	LOGGER.error(e.getMessage());
			    }
			}
		}
	}

	/**
	 * 避免出現奇怪的字
	 */
//	private void _set_headerContents(Contents contents, String s) {
//
//		contents.clear();
//		// 先 setFontSize 再 setFontName，呈現才正常
//		contents.setFontSize(14);
//		contents.setFontName("標楷體");
//
//		contents.append(s);
//	}

	private void gfnGenCTLListChkExcel_writeExcel(
			List<Map<String, String>> rows, int totalColSize,
			L180M01A l180m01a, L180M01B l180m01b, RO412 ro412,
			Map<String, String> map_mowDesc, List<String> totCTL,
			Map<String, Integer> headerMap, boolean ctlTypeA, boolean ctlTypeB,
			boolean ctlTypeC) {
		// ===
		L170M01A l170m01a = retrialService.findL170M01A(l180m01b);
		// String[] arr = new String[totalColSize];
		Map<String, String> eachRrowMap = null;

		// for (int i_col = 0; i_col < totalColSize; i_col++) {
		// arr[i_col] = "";
		// }

		eachRrowMap = new LinkedHashMap<String, String>();
		for (String h : headerMap.keySet()) {
			eachRrowMap.put(h, "");
		}

		// arr[0] = "";// 之後顯示 count
		eachRrowMap.put("序號", "");

		// arr[1] = CrsUtil.seqPart2nd_FromProjectNo(l180m01b.getProjectNo());
		eachRrowMap.put("覆審序號",
				CrsUtil.seqPart2nd_FromProjectNo(l180m01b.getProjectNo()));

		// arr[2] = Util.trim(l180m01b.getCustId());
		eachRrowMap.put("統一編號", Util.trim(l180m01b.getCustId()));

		if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
			eachRrowMap.put("覆審名單種類", Util.trim(l180m01b.getCtlType()));
		}

		List<String> mowStr = new ArrayList<String>();
		if (true) {
			// 處理信評
			String crdtTbl = Util.trim(ro412.get_crdtTbl());
			if (Util.isNotEmpty(crdtTbl)) {
				mowStr.add("舊信評：" + crdtTbl);
			}
			String mowTbl1 = Util.trim(ro412.get_mowTbl1());
			if (Util.isNotEmpty(mowTbl1)) {
				String mowType = Util.trim(ro412.get_mowType());
				String label_mowType = map_mowDesc.containsKey(mowType) ? map_mowDesc
						.get(mowType) : mowType;
				mowStr.add(label_mowType + "：" + mowTbl1);
			}
			String fcrdGrad = Util.trim(ro412.get_fcrdGrad());
			if (Util.isNotEmpty(fcrdGrad)) {
				mowStr.add(retrialService.gfnCTLCaculateFCRDTYPENM(
						Util.trim(ro412.get_fcrdType()),
						Util.trim(ro412.get_fcrdArea()),
						Util.trim(ro412.get_fcrdPred()), fcrdGrad));
			}
		}

		// arr[3] = Util.trim(StringUtils.join(mowStr,
		// EloanConstants.LINE_BREAK));
		eachRrowMap.put("信用評等",
				Util.trim(StringUtils.join(mowStr, EloanConstants.LINE_BREAK)));

		// arr[4] = Util.equals("Y", elf412.getElf412_mainCust()) ? "Y" : "";
		eachRrowMap.put("符合授信額度標準",
				Util.equals("Y", ro412.get_mainCust()) ? "Y" : "");

		// arr[5] = Util.trim(l180m01b.getElfCName());
		eachRrowMap.put("客戶名稱", Util.trim(l180m01b.getElfCName()));

		// ctlFound 判斷方式變更的話 isCtlFound()也要記的改
		boolean ctlFound = false;
		L170M01F l170m01f = retrialService.findL170M01F(l170m01a);
		if (l170m01f == null) {
			l170m01f = new L170M01F();// 之後會由 l170m01f 取值
		}
		// ---
		if (l170m01f.getUpDate() != null) {
			ctlFound = true;
		}

		if (ctlFound) {
			// arr[6] = "Y";
			eachRrowMap.put("上傳控制檔或更新前次覆審日", "Y");
			if (Util.equals("2", l170m01f.getConFlag())) {
				// arr[11] = "Y";
				eachRrowMap.put("覆審異常註記", "Y");
			}
		} else {
			if (CrsUtil.isNull_or_ZeroDate(ro412.get_lrDate())) {
				// arr[6] = "N";
				eachRrowMap.put("上傳控制檔或更新前次覆審日", "N");
			} else {
				// L170M01A 未上傳過ELF412,但ELF412_LRDATE 有值
				if (LMSUtil.cmp_yyyyMM(ro412.get_lrDate(), ">=",
						l180m01a.getDataDate())) {
					// arr[6] = "N";
					eachRrowMap.put("上傳控制檔或更新前次覆審日", "N");
					// arr[11] = "";
					eachRrowMap.put("覆審異常註記", "");

				} else {
					// arr[6] = "N";
					eachRrowMap.put("上傳控制檔或更新前次覆審日", "N");
				}
			}
		}

		// '正常戶才要重新計算

		// if (Util.equals(ro412.get_nckdFlag(), "")
		// || Util.equals(ro412.get_nckdFlag(), "0")) {
		// //retrialService.gfnCTL_Caculate_ELF412(elf412, null);
		// }
		// arr[7] = Util.trim(elf412.getElf412_rckdLine());
		eachRrowMap.put("覆審週期", Util.trim(ro412.get_rckdLine()));

		String retrial_L1_Staff = "";
		L170M01G l170m01g = retrialService
				.findL170M01G_first_L1_retrialTeam(l170m01a);
		if (l170m01g != null) {
			retrial_L1_Staff = l170m01g.getStaffName();
		}

		// arr[8] = Util.trim(retrial_L1_Staff);
		eachRrowMap.put("覆審人員", Util.trim(retrial_L1_Staff));

		if (ctlFound
				&& (LrsUtil.isNckdFlag_EMPTY_8(ro412.get_nckdFlag()) || Util
						.equals("0", ro412.get_nckdFlag()))) {
			// '會寫ELF412_NCKDFLAG = "8" 是因為中南區覆審 11月產生002名單後
			// 12月還會產生002名單，並註明12月內已出現在11月之覆審名單為 已列為１００／１１／２８應覆審案件
			// '若11月覆審報告表上傳後，再產生12月名單，則覆審驗證檔會把此戶當作本次不覆審(但實際上已經覆審)
			totCTL.add("");
		} else if(ctlFound && Util.equals(
				LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內,
				ro412.get_nckdFlag())) {
			// J-109-0313 小規模覆審 - 因為覆審完會把ELF412 壓成 11，所以要另外再計入
			totCTL.add("");
        } else if(ctlFound && Util.equals(
                LrsUtil.NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審,
                ro412.get_nckdFlag())) {
            // J-109-0456 覆審500w&70% 新做增額審完後 不需再覆審 - 因為覆審完會把ELF412 壓成 12，所以要另外再計入
            // J-110-0272 不覆審代碼12條件放寬
            //  新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上，均為不循環動用額度者，免再辦理覆審。倘含循環動用者，抽樣覆審。
            totCTL.add("");
        }
		List<String> memoList = new ArrayList<String>();
		String elfnewAdd = Util.trim(l180m01b.getElfNewAdd());
		String newAddDesc = Util.trim(retrialService.get_lrs_NewAdd().get(
				elfnewAdd));

		if (Util.isNotEmpty(newAddDesc)) {
			// 010306
			String raw_dateDesc = LrsUtil
					.model_elfNewDate_to_elf412_rocDateStr(l180m01b
							.getElfNewDate());
			String dateDesc = "";
			if (Util.isNotEmpty(raw_dateDesc)) {
				// 轉換成 103.06
				dateDesc = LrsUtil.toStr_NewDate(raw_dateDesc, ".");
			}

			String addMemo = dateDesc + " " + newAddDesc;
			memoList.add(addMemo);
		}

		if (Util.equals(l180m01b.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {

		} else if (Util.equals(l180m01b.getCtlType(), LrsUtil.CTLTYPE_價金履約)){
			
		} else {
			if (true) {

				if (Util.isNotEmpty(Util.trim(l180m01b.getElfMDFlag()))
						&& Util.notEquals("0", l180m01b.getElfMDFlag())) {
					memoList.add(Util.trim(TWNDate.toTW(l180m01b.getElfMDDt()))
							+ "通報異常");
				}
			}

		}
		if (Util.isNotEmpty(Util.trim(ro412.get_memo()))) {
			memoList.add(Util.trim(ro412.get_memo()));
		}

		String addMemo = Util.trim(StringUtils.join(memoList,
				EloanConstants.LINE_BREAK));
		if (Util.isNotEmpty(addMemo)) {
			if (Util.isEmpty(Util.trim(l180m01b.getNewNCkdMemo()))) {
				// arr[9] = addMemo;
				eachRrowMap.put("備註", addMemo);
			} else {
				if (!(ctlFound && Util.equals(ro412.get_nckdFlag(),
						LrsUtil.NCKD_8_本次暫不覆審))) {
					// arr[9] = addMemo + EloanConstants.LINE_BREAK
					// + Util.trim(elf412.getElf412_nckdMemo());
					eachRrowMap.put("備註", addMemo + EloanConstants.LINE_BREAK
							+ Util.trim(ro412.get_nckdMemo()));
				}
			}
		} else {
			if (!(ctlFound && Util.equals(ro412.get_nckdFlag(),
					LrsUtil.NCKD_8_本次暫不覆審))) {
				// arr[9] = Util.trim(elf412.getElf412_nckdMemo());
				eachRrowMap.put("備註", Util.trim(ro412.get_nckdMemo()));
			}
		}

		if (!(ctlFound && Util.equals(ro412.get_nckdFlag(),
				LrsUtil.NCKD_8_本次暫不覆審))) {
			// arr[10] = Util.trim(elf412.getElf412_nckdFlag());
			if(Util.equals(ro412.get_nckdFlag(),
					LrsUtil.NCKD_11_小規模營業人_央行C方案_已抽樣覆審於次年起免辦覆審或未列於抽樣需覆審名單內)
					&& Util.equals(l180m01b.getIsSmallBuss(), "Y")){
				// J-109-0313 小規模覆審
				eachRrowMap.put("不覆審註記", Util.trim(l180m01b.getNewNCkdFlag()));
			} else if(Util.equals(ro412.get_nckdFlag(),
                    LrsUtil.NCKD_12_有效額度NTD1000w以下信保七成以上或十足擔保之不循環案件_已於新作增貸後辦理一次覆審)){
                // J-110-0272 不覆審代碼12條件放寬
                //  新臺幣一千萬元以下且為十足擔保授信或經信用保證基金保證成數七成以上，均為不循環動用額度者，免再辦理覆審。倘含循環動用者，抽樣覆審。
                eachRrowMap.put("不覆審註記", Util.trim(l180m01b.getNewNCkdFlag()));
            } else {
				eachRrowMap.put("不覆審註記", Util.trim(ro412.get_nckdFlag()));
			}
		}

		// if (Util.equals(Util.trim(l170m01f.getConFlag()), "2")) {
		// arr[11] = "Y";
		// }

		// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
		eachRrowMap.put("實地覆審報告表", "");
		if (l170m01a != null) {
			if (ctlTypeA) {
				// arr[12] = Util.trim(l170m01a.getRealRpFg());
				eachRrowMap.put("實地覆審報告表", Util.trim(l170m01a.getRealRpFg()));
			}

		}

		// J-109-0313 小規模覆審
		// arr[13]
		eachRrowMap.put("純小規模註記", Util.equals("Y", l180m01b.getIsSmallBuss()) ? "Y" : "");
		// arr[14]
		eachRrowMap.put("小規模評分", l180m01b.getSbScore() == null ? "" : l180m01b.getSbScore().toString());
		// arr[15]
		if(l170m01a != null){
		    String l170Flag = Util.trim(l170m01a.getIsSmallBuss());
		    if(Util.isNotEmpty(l170Flag)) {
                boolean diff = Util.notEquals(Util.trim(l180m01b.getIsSmallBuss()), Util.trim(l170m01a.getIsSmallBuss()));
                eachRrowMap.put("小規模變更註記", diff ? "Ｖ" : "");
            } else {
                eachRrowMap.put("小規模變更註記", "");
            }
		} else {
			eachRrowMap.put("小規模變更註記", "");
		}
		// J-110-0272 抽樣覆審
		// arr[16]
		String randomType = (Util.equals("Y", Util.trim(l180m01b.getIsSmallBuss())) ?
				LrsUtil.RANDOMTYPE_Z_純小規模營業人 : Util.trim(l180m01b.getElfRandomType()));
		eachRrowMap.put("抽樣", randomType);

		// ---
		rows.add(eachRrowMap);
	}

	// 上傳控制檔或更新前次覆審日
	private boolean isCtlFound(L180M01B l180m01b) {
		boolean ctlFound = false;
		if(l180m01b != null) {
			L170M01A l170m01a = retrialService.findL170M01A(l180m01b);
			if(l170m01a != null) {
				L170M01F l170m01f = retrialService.findL170M01F(l170m01a);
				if (l170m01f == null) {
					l170m01f = new L170M01F();// 之後會由 l170m01f 取值
				}
				// ---
				if (l170m01f.getUpDate() != null) {
					ctlFound = true;
				}
			}
		}
		return ctlFound;
	}

	@Override
	public void gfnGenCTLListExcel_GroupByBranch(
			ByteArrayOutputStream outputStream, String dataDate,
			List<L180M01A> meta_list) throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;
		if (true) {
			// ---
			workbook = new HSSFWorkbook();
			sheet1 = workbook.createSheet("Sheet1");
			
			HSSFFont headFont10 = workbook.createFont();
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);

			HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);
			// ======
			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);

			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);

			HSSFCellStyle cellFormatR = workbook.createCellStyle();
			cellFormatR.setFont(headFont12);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatL.setWrapText(true); 

			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT); 
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);

			HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
			// ======
			HSSFFont headFont14 = workbook.createFont();
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);

			HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
			// ======

			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("", 6);
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 25);
			headerMap.put("預計覆審件數", 18);
			headerMap.put("不覆審件數", 18);

			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			for (L180M01A meta : meta_list) {
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				arr[0] = "";// 之後顯示 count
				arr[1] = Util.trim(meta.getBranchId());
				arr[2] = Util.trim(branchService.getBranchName(meta
						.getBranchId()));
				int chk = 0;
				int notChk = 0;

				List<L180M01B> l180m01b_list = retrialService
						.findL180M01BDefaultOrder(meta.getMainId());
				for (L180M01B model : l180m01b_list) {
					if (Util.equals(lrsConstants.docStatus1.不覆審,
							model.getDocStatus1())) {
						notChk++;
					} else {
						chk++;
					}
				}
				arr[3] = String.valueOf(chk);
				arr[4] = String.valueOf(notChk);
				// ---
				rows.add(arr);
			}

			if (true) {
				int i = 1;
				for (String[] arr : rows) {
					arr[0] = String.valueOf(i);
					i++;
				}
			}
			// ==============================
			int rowIdx = 0;
			// ==============================
			if (true) {
				IBranch brn = branchService.getBranch(user.getUnitNo());
				HSSFRow row = sheet1.getRow(rowIdx);
				if (row == null) {
			        row = sheet1.createRow(rowIdx);
			    }
			    row.setHeight((short) (rowHeightAt14));

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
			    HSSFCell cell = row.createCell(0);
			    cell.setCellValue(Util.trim(brn.getNameABBR()) + "分行企金戶預計覆審件數查詢表");
			    cell.setCellStyle(cellFormatC_14);
			}
			// ==============================
			if (true) {
				rowIdx = 2;
				HSSFRow row = sheet1.getRow(rowIdx);
				if (row == null) {
			        row = sheet1.createRow(rowIdx);
			    }
			    row.setHeight((short) (rowHeightAt14));

			    int colIdx_row1 = 3;
			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
			    HSSFCell cell1 = row.createCell(0);
			    cell1.setCellValue("指定年月：" + dataDate);
			    cell1.setCellStyle(cellFormatL);

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
			    HSSFCell cell2 = row.createCell(colIdx_row1);
			    cell2.setCellValue("查詢日期：" + TWNDate.toAD(new Date()));
			    cell2.setCellStyle(cellFormatL);
			}
			// ==============================
			rowIdx = 3;
			int colIdx = 0;
			HSSFRow headerRow = sheet1.getRow(rowIdx);
			if (headerRow == null) {
			    headerRow = sheet1.createRow(rowIdx);
			}

			for (String h : headerMap.keySet()) {
			    int colWidth = headerMap.get(h);
			    sheet1.setColumnWidth(colIdx, colWidth * 256);

			    HSSFCell cell = headerRow.createCell(colIdx);
			    cell.setCellValue(h);
			    cell.setCellStyle(cellFormatL_Border);
			    colIdx++;
			}
			// ==============================

			rowIdx = 4;
			int i = 0;
			for (String[] arr : rows) {
			    HSSFRow dataRow = sheet1.getRow(rowIdx + i);
			    if (dataRow == null) {
			        dataRow = sheet1.createRow(rowIdx + i);
			    }

			    int colLen = arr.length;
			    for (int i_col = 0; i_col < colLen; i_col++) {
			        HSSFCell cell = dataRow.createCell(i_col);
			        cell.setCellValue(arr[i_col]);
			        cell.setCellStyle(i_col >= 3 ? cellFormatR_Border : cellFormatL_Border);
			    }
			    i++;
			}

			try {
				workbook.write(outputStream);
			} finally {
				workbook.close();
			}
		}
	}

	@Override
	public void gfnGenerateCTL_FLMS180R13(ByteArrayOutputStream outputStream,
			String dataDate, List<String> brNo_list) throws IOException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;

		if (true) {
			// ---
			workbook = new HSSFWorkbook();
			sheet1 = workbook.createSheet("合計");
			
			HSSFFont headFont10 = workbook.createFont();
			headFont10.setFontName("標楷體");
			headFont10.setFontHeightInPoints((short) 10);

			HSSFCellStyle cellFormatL_10 = workbook.createCellStyle();
			cellFormatL_10.setFont(headFont10);
			cellFormatL_10.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_10.setWrapText(true);

			// ======
			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);

			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true);

			HSSFCellStyle cellFormatR = workbook.createCellStyle();
			cellFormatR.setFont(headFont12);
			cellFormatR.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR.setWrapText(true);

			HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
			cellFormatL_Border.setFont(headFont12);
			cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL_Border.setWrapText(true);
			cellFormatL_Border.setBorderTop(BorderStyle.THIN);
			cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatL_Border.setBorderRight(BorderStyle.THIN);

			HSSFCellStyle cellFormatR_Border = workbook.createCellStyle();
			cellFormatR_Border.setFont(headFont12);
			cellFormatR_Border.setAlignment(HorizontalAlignment.RIGHT);
			cellFormatR_Border.setWrapText(true);
			cellFormatR_Border.setBorderTop(BorderStyle.THIN);
			cellFormatR_Border.setBorderBottom(BorderStyle.THIN);
			cellFormatR_Border.setBorderLeft(BorderStyle.THIN);
			cellFormatR_Border.setBorderRight(BorderStyle.THIN);
			// ======
			HSSFFont headFont14 = workbook.createFont();
			headFont14.setFontName("標楷體");
			headFont14.setFontHeightInPoints((short) 14);

			HSSFCellStyle cellFormatC_14 = workbook.createCellStyle();
			cellFormatC_14.setFont(headFont14);
			cellFormatC_14.setAlignment(HorizontalAlignment.CENTER);
			cellFormatC_14.setWrapText(true);
			// ======

			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("", 6);
			headerMap.put("分行代碼", 12);
			headerMap.put("分行別", 25);
			headerMap.put("無不覆審註記件數", 20);
			headerMap.put("有不覆審註記件數", 20);
			headerMap.put("預計覆審總件數", 18);
			headerMap.put("備註", 18);

			int totalColSize = headerMap.size();

			List<String[]> rows = new ArrayList<String[]>();
			LinkedHashMap<String, List<L180M01B>> map = new LinkedHashMap<String, List<L180M01B>>();
			Date d_dataDate = CapDate.parseDate(dataDate + "-01");
			String createBy = "2";// 1:預約單, 2:手動
			String userId = user.getUserId();
			String unitNo = user.getUnitNo();
			String unitType = user.getUnitType();
			boolean is_exMode_A = true;

			for (String brNo : brNo_list) {
				List<String> instIdList = new ArrayList<String>();
				List<L180M01B> l180m01b_list = new ArrayList<L180M01B>();
				boolean exeR = lms1810Service.gfnGenCTLList_ByBrno(is_exMode_A,
						instIdList, brNo, d_dataDate, userId, unitNo, unitType,
						createBy, l180m01b_list);
				if (exeR == false) {
					continue;
				}
				map.put(brNo, l180m01b_list);
			}

			String[] noCrlArr = { LrsUtil.NCKD_1_本行或同業主辦之聯貸案件_非擔任管理行,
					LrsUtil.NCKD_2_十成定存, LrsUtil.NCKD_3_純進出押戶,
					LrsUtil.NCKD_4_對政府或政府所屬機關_學校之授信案件,
					LrsUtil.NCKD_5_拆放同業或對同業之融通,
					LrsUtil.NCKD_9_已專案核准免辦理覆審之房屋仲介價金履約保證案件,
					LrsUtil.NCKD_10_外勞保證中長期授信案件_已於新作後辦理一次覆審_且無增額_減額_變更條件或續約,
					LrsUtil.NCKD_A_非董事會或常董會權限案件, LrsUtil.NCKD_B_參貸同業主辦之聯合授信案件,
					LrsUtil.NCKD_C_國內營業單位辦理之境外公司授信案件含對大陸地區授信,
					LrsUtil.NCKD_D_國外營業單位單獨承做之跨國非當地國授信案件,
					LrsUtil.NCKD_E_非實地覆審主辦分行 };

			for (String brNo : map.keySet()) {
				List<L180M01B> l180m01b_list = map.get(brNo);
				int count = l180m01b_list.size();
				int noCtlCount = 0;
				for (L180M01B model : l180m01b_list) {
					if (CrsUtil.inCollection(model.getElfNCkdFlag(), noCrlArr)) {
						noCtlCount++;
					}
				}
				// ---
				String[] arr = new String[totalColSize];
				for (int i_col = 0; i_col < totalColSize; i_col++) {
					arr[i_col] = "";
				}
				arr[0] = "";// 之後顯示 count
				arr[1] = Util.trim(brNo);
				arr[2] = Util.trim(branchService.getBranchName(brNo));
				if (count == 0) {
					arr[3] = "0";
					arr[4] = "0";
					arr[5] = "無資料";
				} else {
					arr[3] = String.valueOf(count - noCtlCount);
					arr[4] = String.valueOf(noCtlCount);
					arr[5] = String.valueOf(count);
				}
				arr[6] = "";
				// ---
				rows.add(arr);
			}

			if (true) {
				int i = 1;
				for (String[] arr : rows) {
					arr[0] = String.valueOf(i);
					i++;
				}
			}
			// ==============================
			int rowIdx = 0;
			// ==============================
			if (true) {
			    IBranch brn = branchService.getBranch(user.getUnitNo());

			    HSSFRow row = sheet1.getRow(rowIdx);
			    if (row == null) {
			        row = sheet1.createRow(rowIdx);
			    }
			   
			    row.setHeight((short) (rowHeightAt14));

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, totalColSize - 1));
			    HSSFCell cell = row.createCell(0);
			    cell.setCellValue(Util.trim(brn.getNameABBR()) + "分行企金戶『必須』覆審件數查詢表");
			    cell.setCellStyle(cellFormatC_14);
			}
			// ==============================
			if (true) {
			    rowIdx = 2; 
			    
			    HSSFRow row = sheet1.getRow(rowIdx);
			    if (row == null) {
			        row = sheet1.createRow(rowIdx);
			    }
			    row.setHeight((short) (rowHeightAt14));

			    int colIdx_row1 = 5;

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, 0, colIdx_row1 - 1));
			    HSSFCell cell1 = row.createCell(0);
			    cell1.setCellValue("指定年月：" + dataDate);
			    cell1.setCellStyle(cellFormatL);

			    sheet1.addMergedRegion(new CellRangeAddress(rowIdx, rowIdx, colIdx_row1, totalColSize - 1));
			    HSSFCell cell2 = row.createCell(colIdx_row1);
			    cell2.setCellValue("查詢日期：" + TWNDate.toAD(new Date()));
			    cell2.setCellStyle(cellFormatL);
			}
			// ==============================
			rowIdx = 3;
			int colIdx = 0;
			HSSFRow headerRow = sheet1.getRow(rowIdx);
			if (headerRow == null) {
			    headerRow = sheet1.createRow(rowIdx);
			}

			for (String h : headerMap.keySet()) {
			    int colWidth = headerMap.get(h);
			    sheet1.setColumnWidth(colIdx, colWidth * 256);

			    HSSFCell cell = headerRow.createCell(colIdx);
			    cell.setCellValue(h);
			    cell.setCellStyle(cellFormatL_Border);
			    colIdx++;
			}
			// ==============================
			rowIdx = 4;
			int i = 0;
			for (String[] arr : rows) {
			    HSSFRow dataRow = sheet1.getRow(rowIdx + i);
			    if (dataRow == null) {
			        dataRow = sheet1.createRow(rowIdx + i);
			    }

			    int colLen = arr.length;
			    for (int i_col = 0; i_col < colLen; i_col++) {
			        // 建立儲存格並設定值與樣式
			        HSSFCell cell = dataRow.createCell(i_col);
			        cell.setCellValue(arr[i_col]);
			        // 根據欄位索引判斷套用哪種邊框樣式
			        cell.setCellStyle((i_col >= 3 && i_col <= 5) ? cellFormatR_Border : cellFormatL_Border);
			    }
			    // ---
			    i++;
			}
			
			// ---
			// worksheet 2

			gfnGenerateCTL_FLMS180R13_writeExlList(workbook, map);
			// ---
			try {
				workbook.write(outputStream);
			} finally {
				workbook.close();
			}
		}
	}

	@Override
	public void genExcelWithMsg(ByteArrayOutputStream outputStream, String msg)
			throws IOException{
		HSSFWorkbook workbook = null;
		HSSFSheet sheet1 = null;
		if (true) {
			workbook = new HSSFWorkbook(); 
			sheet1 = workbook.createSheet("Sheet1");

			HSSFFont headFont12 = workbook.createFont();
			headFont12.setFontName("標楷體");
			headFont12.setFontHeightInPoints((short) 12);

			HSSFCellStyle cellFormatL = workbook.createCellStyle();
			cellFormatL.setFont(headFont12);
			cellFormatL.setAlignment(HorizontalAlignment.LEFT);
			cellFormatL.setWrapText(true); 

			sheet1.setColumnWidth(0, 100 * 256);

			HSSFRow row0 = sheet1.createRow(0);
			HSSFCell cell0_0 = row0.createCell(0);
			cell0_0.setCellValue(msg);
			cell0_0.setCellStyle(cellFormatL);

			try {
				workbook.write(outputStream);
			} finally {
				workbook.close();
			}
		}
	}

	private void gfnGenerateCTL_FLMS180R13_writeExlList(
			HSSFWorkbook workbook, LinkedHashMap<String, List<L180M01B>> map){

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String unitNo = user.getUnitNo();

		HSSFSheet sheet = workbook.createSheet("明細");

		// ======
		HSSFFont headFont12 = workbook.createFont();
		headFont12.setFontName("標楷體");
		headFont12.setFontHeightInPoints((short) 12);

		HSSFCellStyle cellFormatL = workbook.createCellStyle();
		cellFormatL.setFont(headFont12);
		cellFormatL.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL.setWrapText(true);

		HSSFCellStyle cellFormatL_Border = workbook.createCellStyle();
		cellFormatL_Border.setFont(headFont12);
		cellFormatL_Border.setAlignment(HorizontalAlignment.LEFT);
		cellFormatL_Border.setWrapText(true);
		cellFormatL_Border.setBorderTop(BorderStyle.THIN);
		cellFormatL_Border.setBorderBottom(BorderStyle.THIN);
		cellFormatL_Border.setBorderLeft(BorderStyle.THIN);
		cellFormatL_Border.setBorderRight(BorderStyle.THIN);

		// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(unitNo,
				LrsUtil.CTLTYPE_主辦覆審);

		boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(unitNo,
				LrsUtil.CTLTYPE_自辦覆審);

		boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(unitNo,
				LrsUtil.CTLTYPE_價金履約);

		Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
		headerMap.put("分行別", 8);
		headerMap.put("分行名稱", 16);
		headerMap.put("客戶統編", 13);
		headerMap.put("重覆序號", 4);
		headerMap.put("客戶名稱", 30);
		if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
			headerMap.put("覆審種類", 4);
		}
		headerMap.put("覆審週期", 5);
		headerMap.put("上次覆審日", 15);
		headerMap.put("符合授信額度標準", 8);
		headerMap.put("評等", 25);
		if (ctlTypeA) {
			headerMap.put("實地覆審" + EloanConstants.LINE_BREAK + "註記/基準日", 17);
		}

		int totalColSize = headerMap.size();
		// ==============================
		Map<String, String> brDescMap = new HashMap<String, String>();
		for (String brNo : map.keySet()) {
			brDescMap.put(brNo, Util.trim(branchService.getBranchName(brNo)));
		}
		Map<String, String> mowDescMap = retrialService.get_lrs_MowType_1();
		// List<String[]> rows = new ArrayList<String[]>();
		List<Map<String, String>> rows = new ArrayList<Map<String, String>>();
		Map<String, String> eachRrowMap = null;

		for (String brNo : map.keySet()) {
			for (L180M01B l180m01b : map.get(brNo)) {
				// String[] arr = new String[totalColSize];
				// for (int i_col = 0; i_col < totalColSize; i_col++) {
				// arr[i_col] = "";
				// }

				eachRrowMap = new LinkedHashMap<String, String>();
				for (String h : headerMap.keySet()) {
					eachRrowMap.put(h, "");
				}

				List<String> grade = new ArrayList<String>();
				if (true) {
					if (Util.isNotEmpty(Util.trim(l180m01b.getElfCrdTTbl()))) {
						String desc = "";
						if (Util.equals("B", l180m01b.getElfCrdType())) {
							desc = "大型企業評等";
						} else if (Util.equals("L", l180m01b.getElfCrdType())) {
							desc = "中小型企業評等";
						} else if (Util.equals("O", l180m01b.getElfCrdType())) {
							desc = "ＯＢＵ評等";
						} else {
							desc = "舊評等";
						}
						grade.add(desc + "： "
								+ Util.trim(l180m01b.getElfCrdTTbl()) + " 級");

					}
					if (Util.isNotEmpty(Util.trim(l180m01b.getElfMowTbl1()))) {
						String desc = "";
						String mowType = Util.trim(l180m01b.getElfMowType());
						if (mowDescMap.containsKey(mowType)) {
							desc = mowDescMap.get(mowType);
						} else {
							desc = "信用模型評等";
						}
						grade.add(desc + " "
								+ Util.trim(l180m01b.getElfMowTbl1()) + " 分");
					}
					if (Util.isNotEmpty(Util.trim(l180m01b.getElfFcrdGrad()))) {
						grade.add(retrialService.gfnCTLCaculateFCRDTYPENM(
								Util.trim(l180m01b.getElfFcrdType()),
								Util.trim(l180m01b.getElfFcrdArea()),
								Util.trim(l180m01b.getElfFcrdPred()),
								Util.trim(l180m01b.getElfFcrdGrad())));
					}
				}

				// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				StringBuffer realCkData = new StringBuffer("");
				if (Util.notEquals(Util.trim(l180m01b.getElfRealCkFg()), "")) {
					// realCkData.append("註記:");
					realCkData.append(l180m01b.getElfRealCkFg());
				}

				if (Util.equals(Util.trim(l180m01b.getElfRealCkFg()), "Y")) {

					if (CrsUtil.isNOT_null_and_NOTZeroDate(l180m01b
							.getElfRealDt())) {
						realCkData.append("/");
						realCkData
								.append(TWNDate.toAD(l180m01b.getElfRealDt()));
					}

				}

				// ---

				eachRrowMap.put("分行別", brNo);
				eachRrowMap.put("分行名稱", brDescMap.get(brNo));
				eachRrowMap.put("客戶統編", l180m01b.getCustId());
				eachRrowMap.put("重覆序號", l180m01b.getDupNo());
				eachRrowMap.put("客戶名稱", l180m01b.getElfCName());
				if (ctlTypeA || ctlTypeC || (ctlTypeA && ctlTypeB)) {
					eachRrowMap.put("覆審種類", Util.trim(l180m01b.getCtlType()));
				}
				eachRrowMap.put("覆審週期", l180m01b.getElfRCkdLine());
				eachRrowMap.put(
						"上次覆審日",
						CrsUtil.isNOT_null_and_NOTZeroDate(l180m01b
								.getElfLRDate()) ? TWNDate.toAD(l180m01b
								.getElfLRDate()) : "");
				eachRrowMap.put("符合授信額度標準",
						Util.trim(l180m01b.getElfMainCust()));
				eachRrowMap.put("評等",
						StringUtils.join(grade, EloanConstants.LINE_BREAK));
				if (ctlTypeA) {
					eachRrowMap.put("實地覆審" + EloanConstants.LINE_BREAK
							+ "註記/基準日", realCkData.toString());
				}

				// arr[0] = brNo;
				// arr[1] = brDescMap.get(brNo);
				// arr[2] = l180m01b.getCustId();
				// arr[3] = l180m01b.getDupNo();
				// arr[4] = l180m01b.getElfCName();
				// arr[5] = l180m01b.getElfRCkdLine();
				// arr[6] = CrsUtil.isNOT_null_and_NOTZeroDate(l180m01b
				// .getElfLRDate()) ? TWNDate
				// .toAD(l180m01b.getElfLRDate()) : "";
				// arr[7] = Util.trim(l180m01b.getElfMainCust());
				// arr[8] = StringUtils.join(grade, EloanConstants.LINE_BREAK);
				// arr[9] = realCkData.toString();
				// ---
				rows.add(eachRrowMap);
			}
		}
		// ==============================
		int rowIdx = 0;
		// ==============================
		int colIdx = 0;
		HSSFRow headerRow = sheet.getRow(rowIdx);
		if (headerRow == null) {
		    headerRow = sheet.createRow(rowIdx);
		}

		for (String h : headerMap.keySet()) {
		    int colWidth = headerMap.get(h);
		    sheet.setColumnWidth(colIdx, colWidth * 256);

		    HSSFCell cell = headerRow.createCell(colIdx);
		    cell.setCellValue(h);
		    cell.setCellStyle(cellFormatL_Border);
		    colIdx++;
		}
		
		rowIdx = 1;
		int i = 0;
		
		for (Map<String, String> tEachRrowMap : rows) {
		    HSSFRow dataRow = sheet.getRow(rowIdx + i);
		    if (dataRow == null) {
		        dataRow = sheet.createRow(rowIdx + i);
		    }

		    int i_col = 0;
		    for (String h : headerMap.keySet()) {
		        HSSFCell cell = dataRow.createCell(i_col);
		        cell.setCellValue(MapUtils.getString(tEachRrowMap, h, ""));
		        cell.setCellStyle(cellFormatL_Border);
		        i_col++;
		    }
		    i++;
		}
	}

	@Override
	public List<ELF493> gfnDB2UpELF493(L180M01A l180m01a,
			List<L180M01B> l180m01b_list, String elf493_updater) {
		List<ELF493> elf493_list = new ArrayList<ELF493>();

		String elf493_branch = l180m01a.getBranchId();
		String elf493_rptDocId = LrsUtil.elf493_rptDocId(l180m01a);
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		for (L180M01B l180m01b : l180m01b_list) {
			ELF493 elf493 = new ELF493();
			// ---
			elf493.setElf493_branch(elf493_branch);
			elf493.setElf493_custId(l180m01b.getCustId());
			elf493.setElf493_dupNo(l180m01b.getDupNo());
			elf493.setElf493_mainCust(l180m01b.getElfMainCust());
			elf493.setElf493_crdType(l180m01b.getElfCrdType());
			elf493.setElf493_crdtTbl(l180m01b.getElfCrdTTbl());
			elf493.setElf493_mowType(l180m01b.getElfMowType());
			elf493.setElf493_mowTbl1(l180m01b.getElfMowTbl1());
			elf493.setElf493_llrDate(l180m01b.getElfLLRDate());
			elf493.setElf493_lrDate(l180m01b.getElfLRDate());
			elf493.setElf493_rckdLine(l180m01b.getElfRCkdLine());
			elf493.setElf493_ockdLine(l180m01b.getElfOCkdLine());
			elf493.setElf493_cState(l180m01b.getElfCState());
			elf493.setElf493_mdFlag(CrsUtil.mdFlag_trim_leadingZero(l180m01b
					.getElfMDFlag()));
			elf493.setElf493_mdDt(l180m01b.getElfMDDt());
			elf493.setElf493_process(Util.truncateString(
					Util.trim(l180m01b.getElfProcess()), MAXLEN_ELF493_PROCESS));
			elf493.setElf493_newAdd(l180m01b.getElfNewAdd());
			elf493.setElf493_newDate(LrsUtil
					.model_elfNewDate_to_elf412_rocDateStr(l180m01b
							.getElfNewDate()));
			elf493.setElf493_nckdFlag(l180m01b.getElfNCkdFlag());
			elf493.setElf493_nckdDate(l180m01b.getElfNCkdDate());
			elf493.setElf493_nckdMemo(l180m01b.getElfNCkdMemo());
			elf493.setElf493_cancelDt(l180m01b.getElfCancelDt());
			elf493.setElf493_dbuObu(l180m01b.getElfDBUOBU());
            // 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
            elf493.setElf493_isRescue(l180m01b.getElfIsRescue());
			elf493.setElf493_guarFlag(l180m01b.getElfGuarFlag());
			elf493.setElf493_newRescue(l180m01b.getElfNewRescue());
            elf493.setElf493_newRescueYM(LrsUtil
                    .model_elfNewDate_to_elf412_rocDateStr(l180m01b.getElfNewRescueYM()));

			List<String> elf412_DBUCOID_list = new ArrayList<String>();
			List<String> elf412_OBUCOID_list = new ArrayList<String>();
			Set<L180M01D> l180m01ds = l180m01b.getL180m01ds();
			if (l180m01ds != null && !l180m01ds.isEmpty()) {
				for (L180M01D l180m01d : l180m01ds) {
					String str = ";"
							+ LMSUtil.getCustKey_len10custId(
									l180m01d.getDbuObuId(),
									l180m01d.getDbuObuDupNo());
					if (Util.equals("0", l180m01d.getDbuObuType())) {
						elf412_DBUCOID_list.add(str);
					} else {
						elf412_OBUCOID_list.add(str);
					}
				}
			}
			elf493.setElf493_dbuCoid(Util.truncateString(
					Util.trim(StringUtils.join(elf412_DBUCOID_list, "")),
					MAXLEN_ELF493_DBUCOID));
			elf493.setElf493_obuCoid(Util.truncateString(
					Util.trim(StringUtils.join(elf412_OBUCOID_list, "")),
					MAXLEN_ELF493_OBUCOID));
			elf493.setElf493_updDate(nowTS);
			elf493.setElf493_updater(elf493_updater);
			elf493.setElf493_memo(Util.truncateString(
					Util.trim(l180m01b.getElfMemo()), MAXLEN_ELF493_MEMO));
			elf493.setElf493_tmestamp(nowTS);
			elf493.setElf493_uckdLine(l180m01b.getElfUCkdLINE());
			elf493.setElf493_uckdDt(l180m01b.getElfUCkdDt());
			elf493.setElf493_dataDt(l180m01b.getElfDataDt());
			elf493.setElf493_nextNwDt(l180m01b.getElfNextNwDt());
			elf493.setElf493_nextLtDt(CapDate.parseDate(CapDate.ZERO_DATE));
			elf493.setElf493_sno(Util.addZeroWithValue(
					l180m01b.getProjectSeq(), 3));
			elf493.setElf493_projNo(l180m01b.getProjectNo());
			elf493.setElf493_n_nckdFlag(l180m01b.getNewNCkdFlag());
			elf493.setElf493_n_nckdMemo(l180m01b.getNewNCkdMemo());
			elf493.setElf493_n_nextNwDt(l180m01b.getNewNextNwDt());
			elf493.setElf493_n_lrDate(l180m01b.getNewLRDate());
			elf493.setElf493_batchNo(Util.addZeroWithValue(
					l180m01a.getBatchNO(), 3));
			elf493.setElf493_docStus1(l180m01b.getDocStatus1());
			elf493.setElf493_rptDocId(elf493_rptDocId);
			String roc_dataDt = LrsUtil.elf412_rocDateStr_from_Date(l180m01a
					.getDataDate());
			elf493.setElf493_dataDtY(StringUtils.substring(roc_dataDt, 1, 4));
			elf493.setElf493_dataDtM(StringUtils.substring(roc_dataDt, 4, 6));
			elf493.setElf493_ctlgDate(l180m01a.getGenerateDate());
			elf493.setElf493_dfctlDt(l180m01a.getDefaultCTLDate());
			elf493.setElf493_apprId(l180m01a.getApprId());
			elf493.setElf493_bossId(l180m01a.getBossId());
			elf493.setElf493_fcrdType(l180m01b.getElfFcrdType());
			elf493.setElf493_fcrdArea(l180m01b.getElfFcrdArea());
			elf493.setElf493_fcrdPred(l180m01b.getElfFcrdPred());
			elf493.setElf493_fcrdGrad(l180m01b.getElfFcrdGrad());

			// J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			elf493.setElf493_realCkFg(Util.trim(l180m01b.getElfRealCkFg()));
			elf493.setElf493_realDt(l180m01b.getElfRealDt());

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			elf493.setElf493_ctlType(Util.trim(l180m01b.getCtlType()));
			if (Util.equals(l180m01b.getCtlType(), LrsUtil.CTLTYPE_自辦覆審)) {

				elf493.setElf493_oldRptId(Util.trim(l180m01b.getOldRptId()));
				elf493.setElf493_oldRptDt(l180m01b.getOldRptDt());
				elf493.setElf493_newRptId(Util.trim(l180m01b.getNewRptId()));
				elf493.setElf493_newRptDt(l180m01b.getNewRptDt());
			}

			// ---
			LrsUtil.elf493_null_to_zeroDate(elf493);
			elf493_list.add(elf493);
		}
		return elf493_list;
	}
}
