package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/** 線上貸款ixml記錄檔 **/
@NamedEntityGraph(name = "C122M01G-entity-graph", attributeNodes = { @NamedAttributeNode("c122m01a") })
@Entity
@Table(name = "C122M01G", uniqueConstraints = @UniqueConstraint(columnNames = {
		"oid"}))
public class C122M01G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * JOIN條件 關聯黨
	 *
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable=false, updatable=false)
	private C122M01A c122m01a;

	public C122M01A getC122m01a() {
		return c122m01a;
	}

	public void setC122m01a(C122M01A c122m01a) {
		this.c122m01a = c122m01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 項目類別
	 **/
	@Column(name = "ITEMTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String itemType;

	/** 內容 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "JSONDATA", columnDefinition = "CLOB")
	private String jsonData;
	
	/** 內容VO類別 **/
	@Column(name = "JSONVOCLASS", length = 20, columnDefinition = "VARCHAR(20)")
	private String jsonVoClass;

	/** 序號 **/
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** JCIC 發送驗章時間 **/
	@Column(name="SENDFTPTIME", columnDefinition = "TIMESTAMP")
	private Date sendFTPTime;

	/**
	 * JCIC 發送驗章狀態
	 * 0000 進檔成功
	 * 9999 系統錯誤
	 * 1001 同一序號已成功進檔，序號重覆
	 * 1002 序號格式不符
	 * 1003 同意書起訖時間格式不符
	 * 1005 header 內容格式錯誤
	 * 1007 idnBan 格式錯誤
	 * 1009 查詢理由代碼格式錯誤
	 * 1011 同意書版本編號不存在
	 * 1013 同意書內容本文檢核不符
	 * 2001 context 簽章不符
	 * 2003 context 解密失敗
	 * 2005 驗 HMAC 錯誤
	 **/
	@Column(name = "SENDFTPSTATUS", length = 6, columnDefinition = "CHAR(6)")
	private String sendFTPStatus;

	/** JCIC 驗章完成時間 **/
	@Column(name="RECEIVEDFTPTIME", columnDefinition = "TIMESTAMP")
	private Date receivedFTPTime;

	/** 內容 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "RECEIVEDFTPJSONDATA", columnDefinition = "CLOB")
	private String receivedFTPJsonData;

	/** JCIC 發查時間 **/
	@Column(name="QUERYJCICTIME", columnDefinition = "TIMESTAMP")
	private Date queryJCICTime;

	/** JCIC 發查失敗次數 **/
	@Column(name = "RETRY", columnDefinition = "DECIMAL(5,0)")
	private Integer retry;

	/** JCIC 資料回傳時間 **/
	@Column(name="RECEIVEDJCICTIME", columnDefinition = "TIMESTAMP")
	private Date receivedJCICTime;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getJsonData() {
		return jsonData;
	}

	public void setJsonData(String jsonData) {
		this.jsonData = jsonData;
	}

	public String getJsonVoClass() {
		return jsonVoClass;
	}

	public void setJsonVoClass(String jsonVoClass) {
		this.jsonVoClass = jsonVoClass;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public Date getSendFTPTime() {
		return sendFTPTime;
	}

	public void setSendFTPTime(Date sendFTPTime) {
		this.sendFTPTime = sendFTPTime;
	}

	public String getSendFTPStatus() {
		return sendFTPStatus;
	}

	public void setSendFTPStatus(String sendFTPStatus) {
		this.sendFTPStatus = sendFTPStatus;
	}

	public Integer getRetry() {
		return retry;
	}

	public void setRetry(Integer retry) {
		this.retry = retry;
	}

	public Date getReceivedFTPTime() {
		return receivedFTPTime;
	}

	public void setReceivedFTPTime(Date receivedFTPTime) {
		this.receivedFTPTime = receivedFTPTime;
	}

	public String getReceivedFTPJsonData() {
		return receivedFTPJsonData;
	}

	public void setReceivedFTPJsonData(String receivedFTPJsonData) {
		this.receivedFTPJsonData = receivedFTPJsonData;
	}

	public Date getQueryJCICTime() {
		return queryJCICTime;
	}

	public void setQueryJCICTime(Date queryJCICTime) {
		this.queryJCICTime = queryJCICTime;
	}

	public Date getReceivedJCICTime() {
		return receivedJCICTime;
	}

	public void setReceivedJCICTime(Date receivedJCICTime) {
		this.receivedJCICTime = receivedJCICTime;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdater() {
		return updater;
	}

	public void setUpdater(String updater) {
		this.updater = updater;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}
