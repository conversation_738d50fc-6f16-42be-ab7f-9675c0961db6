$(function(){
    setCloseConfirm(true);/*設定關閉此畫面時要詢問*/
    //權限
    var auth = (responseJSON ? responseJSON.Auth : {});
    if (responseJSON.mainDocStatus != "010" || !auth.Modify) {
    	if(responseJSON.page == "02"){
            $("#C240M01AForm").lockDoc();
        	$("#retrialDate").readOnly(true);
    	}else{
            $("#C240M01AForm").lockDoc();
    	}
    }
    
    $.form.init({
        formHandler: "lms2405m01formhandler",
        formPostData: {
            formAction: "queryMain"
        },
        loadSuccess: function(json){
            if (json.C240M01AForm) {
                $('#C240M01AForm').injectData(json.C240M01AForm);
            }
            //經辦
            $('#apprId').val(json.apprId);
            $('#creator').val(json.creator);
            $('#updater').val(json.updater);
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        if ($("#C240M01AForm").valid()) {
            $.ajax({
                type: "POST",
                handler: "lms2405m01formhandler",
                data: {
                    formAction: "saveMain",
                    page: responseJSON.page,
                    oid: responseJSON.mainOid,
                    thisSamplingRate: $("#thisSamplingRate").val()
                }
                }).done(function(responseData){
                    $('body').injectData(responseData);
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
            });
        }
    }).end().find("#btnModifyDate").click(function(){
        $("#defaultForm").reset();
        $("#change").thickbox({
            //修改預計覆審日
            title: i18n.lms2405m01['title.modifyDate'],
            width: 380,
            height: 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#defaultForm").valid()) {
                        return;
                    }
                    $.ajax({
                        type: "POST",
                        handler: "lms2405m01formhandler",
                        data: {
                            formAction: "saveDefault",
                            mainOid: responseJSON.mainOid,
                            date: $("#defaultCTLDate").val()
                        }
                        }).done(function(responseData){
                            $('#C240M01AForm').injectData(responseData.C240M01AForm);
                            CommonAPI.triggerOpener("gridview", "reloadGrid");
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnSend").click(function(){
        sendBoss();
    }).end().find("#btnAccept").click(function(){
    	$("#acceptBack").thickbox({
            //修改預計覆審日
            title: i18n.lms2405m01['button.acceptBack'],
            width: 380,
            height: 100,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.thickbox.close();
                    if($("input[name='apply']:checked").val()){
                    	if($("input[name='apply']:checked").val() == 1){
                    		flowAction({
                                flowAction: true
                            });
                    	}else if($("input[name='apply']:checked").val() == 2){
                    		flowAction({
                                flowAction: false
                            });
                    	}else{
                    		CommonAPI.showMessage(i18n.lms2405m01["err.chooseOne"]);
                    	}
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnReturn").click(function(){
    	CommonAPI.confirmMessage(i18n.def["confirmReturn"],function(b){
            flowAction({
                flowAction: false
            });
    	})
    }).end().find("#btnSendRetrialReport").click(function(){
        flowAction({
            saveData: true
        });
    }).end().find("#btnExit").click(function(){
    	$("#mainId").val(responseJSON.mainId);
    });
    
    //呈主管 -  編製中
    function sendBoss(){
        //confirmApply=是否呈主管覆核？
        CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b){
            if (b) {
                flowAction({
                    flowAction: false
                });
            }
        });
    }
    
    function flowAction(sendData){
        $.ajax({
            type: "POST",
            handler: "lms2405m01formhandler",
            data: $.extend({
                formAction: "flowAction",
                mainOId: responseJSON.mainOid,
                thisSamplingRate: $("#thisSamplingRate").val()
            }, (sendData || {}))
            }).done(function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#mainId").val(responseJSON.mainId);
                setCloseConfirm(false);
                window.close();
        });
    }
});


$.extend(window.tempSave, {
    handler: "lms2405m01formhandler",
    action: "tempMain",
    sendData: function(){
        return $("#C240M01AForm").serializeData();
    }
});
