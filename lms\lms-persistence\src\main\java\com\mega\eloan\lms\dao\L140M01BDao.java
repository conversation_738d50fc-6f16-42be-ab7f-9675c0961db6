/* 
 * L140M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01B;

/** 額度敘述說明檔 **/
public interface L140M01BDao extends IGenericDao<L140M01B> {

	L140M01B findByOid(String oid);
	
	List<L140M01B> findByMainId(String mainId);
	
	L140M01B findByUniqueKey(String mainId,String itemType);
}