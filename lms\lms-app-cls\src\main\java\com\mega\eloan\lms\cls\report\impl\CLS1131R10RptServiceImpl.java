package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.report.CLS1131R10RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.dao.C101S01EDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01E;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 列印負面新聞
 * </pre>
 *
 * <AUTHOR>
 * @version <ul>
 * <li>2023/12/21, EL10702
 * </ul>
 * @since 2023/12/21
 */
@Service("cls1131r10rptservice")
public class CLS1131R10RptServiceImpl extends AbstractIISIReportService implements CLS1131R10RptService {

	protected static final Logger LOGGER = LoggerFactory.getLogger(CLS1131R10RptServiceImpl.class);


	@Resource
	CLS1131Service cls1131Service;

	@Resource
	CLSService clsService;

	@Resource
	C101S01EDao c101s01eDao;

	@Resource
	C120S01EDao c120s01eDao;

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 *
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData, Engine engine) {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String oid = Util.trim(params.getString("oid"));
		String dataSource = Util.trim(params.getString("dataSource"));
		String isChinaSteel = Util.trim(params.getString("isChinaSteel"));
		String json = "";
		String custId="";
		String custName="";
		C101S01E c101s01e = c101s01eDao.findByOid(oid);
		if (Util.isNotEmpty(c101s01e)) {
			C101M01A c101m01a = clsService.findC101M01A_mainId(c101s01e.getMainId());
			json = c101s01e.getWiseNews();
			custId = c101s01e.getCustId();
			custName = c101m01a.getCustName();
		} else {
			C120S01E c120s01e = c120s01eDao.findByOid(oid);
			if (Util.isNotEmpty(c120s01e)){
				C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c120s01e.getMainId(),c120s01e.getCustId(),c120s01e.getDupNo());
				json = c120s01e.getWiseNews();
				custId = c120s01e.getCustId();
				custName = c120m01a.getCustName();
			}
		}
		
		// J-113-0083 中鋼線上進件案件自動查詢負面新聞
		if(Util.equals("Y", isChinaSteel)){
			json = Util.trim(params.getString("json"));
			custId = Util.trim(params.getString("custId"));
			custName = Util.trim(params.getString("custName"));
		}

		if (Util.isEmpty(json)) {
			return  reportData;
		}

		List<List<String>> details = new ArrayList<List<String>>();
		JSONObject queryDataJSON = JSONObject.fromObject(json);
		JSONArray datas = queryDataJSON.optJSONArray("data");
		if (datas.isEmpty()) {
			List<String> detail = new ArrayList<String>();
			detail.add("查無資料");
			details.add(detail);
		}
		for (int i = 0; i < datas.size(); i++) {
			JSONObject exitData = datas.optJSONObject(i);
			JSONArray negative_entity_info = exitData.optJSONArray("negative_entity_info");
			String bank_url = exitData.optString("bank_url");
			String headline = exitData.optString("headline");
			String href = "<a href=\"" + bank_url + "\" target = \"_blank\">" + headline + "</a>";
			List<String> entity = new ArrayList<String>();
			StringBuilder infoHTML = new StringBuilder();
			String[] infoCols = {"entity", "entity_id", "birthday", "description", "hit_customer_value"};
			if (negative_entity_info != null && !negative_entity_info.isEmpty()) {
				infoHTML.append("<style> table,td {border-collapse: collapse;} td {border: 1px solid black;text-align: center;}</style>");
				infoHTML.append("<table width=\"100%\">");
				infoHTML.append("<thead>");
				infoHTML.append("<tr>");
				infoHTML.append("<th>涉案名稱</th>");
				infoHTML.append("<th>ID</th>");
				infoHTML.append("<th>生日</th>");
				infoHTML.append("<th>其他資訊</th>");
				infoHTML.append("<th>命中客戶</th>");
				infoHTML.append("</tr>");
				infoHTML.append("</thead>");
				for (int j = 0; j < negative_entity_info.size(); j++) {
					infoHTML.append("<tr>");
					JSONObject info = negative_entity_info.optJSONObject(j);
					entity.add(info.optString("entity").replace("null", ""));
					// 涉案人資料
					for (String col : infoCols) {
						infoHTML.append("<td>").append(info.getString(col).replace("null", "")).append("</td>");
					}
					infoHTML.append("</tr>");
				}
				infoHTML.append("</table>");
			}
			List<String> detail = new ArrayList<String>();
			detail.add(String.valueOf(i + 1));
			detail.add(exitData.optString("publish_at"));
			detail.add(href);
			detail.add(exitData.optString("mega_importance_value"));
			detail.add(exitData.optString("hit_customer_value"));
			detail.add(StringUtils.join(entity, "</br>"));
			detail.add(exitData.optString("person").replace(";", "</br>"));
			detail.add(exitData.optString("company").replace(";", "</br>"));
			detail.add(infoHTML.toString());
			details.add(detail);
		}
		reportData.addDetail(details);
		reportData.setField("custId", custId);
		reportData.setField("custName", custName);
		reportData.setField("dataDate", "查詢日期：" + CapDate.getCurrentDate("yyyy/MM/dd"));
		return reportData;
	}

	@Override
	public String getReportDefinition() {
		return "report/cls/CLS1131R10";
	}
}
