/* 
 * L918M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L712M01A;

/** 授管處停權主檔 **/
public interface L712M01ADao extends IGenericDao<L712M01A> {

	L712M01A findByOid(String oid);
	
	L712M01A findByMainId(String mainId);
	
	List<L712M01A> findByDocStatus(String docStatus);
}