/* 
 *  LMS1835ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service.impl;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L184A01ADao;
import com.mega.eloan.lms.dao.L184M01ADao;
import com.mega.eloan.lms.eloandb.service.Dw_elf411ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms412Service;
import com.mega.eloan.lms.lrs.service.LMS1835Service;
import com.mega.eloan.lms.mfaloan.bean.ELF411;
import com.mega.eloan.lms.mfaloan.service.MisELF411Service;
import com.mega.eloan.lms.model.L184A01A;
import com.mega.eloan.lms.model.L184M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS1835ServiceImpl extends AbstractCapService implements
		LMS1835Service {
	private static Logger logger = LoggerFactory.getLogger(LMS1835ServiceImpl.class);
	@Resource
	BranchService branch;

	@Resource
	DocFileService docFileService;

	@Resource
	DocFileDao docFileDao;

	@Resource
	L184M01ADao l184m01aDao;

	@Resource
	L184A01ADao l184a01aDao;

	@Resource
	DocFileDao l184m01a;

	@Autowired
	DocFileService fileService;

	@Resource
	BranchService branchService;

	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	TempDataService tempDataService;

	@Resource
	Lms412Service lms412Service;

	@Resource
	CodeTypeService codetypeService;

	@Resource
	Dw_elf411ovsService dwElf411ovsService;

	@Resource
	LMSService lmsService;
	
	@Resource
	MisELF411Service misELF411Service;

	@Override
	public L184M01A fL184m01aByoid(String oid) {

		return l184m01aDao.findByOid(oid);
	}

	// ==========日期格式化===========================================

	@Override
	public boolean transportExcel(boolean overSea, String brNo, Date date, String listName)
			throws Exception {
		String ranMainId = IDGenerator.getRandomCode();
		DocFile docFile = new DocFile();
		L184M01A l184m01a = new L184M01A();
		L184A01A l184a01a = new L184A01A();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<Map<String, Object>> rows = null;
		List<Map<String, Object>> list = null;

		logger.info("findELF411ByMaxDate final");
		StringBuffer custIdsDupNosBranchs = new StringBuffer();
		boolean addResult = false;

		try {
			if(overSea){
				docFile.setBranchId(brNo);	
			}else{
				docFile.setBranchId(user.getUnitNo());
			}			
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(listName + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			fileService.save(docFile, false);
			// 新增L184M01A,L184A01A
			l184m01a.setMainId(ranMainId);
			l184m01a.setTypCd(overSea?TypCdEnum.海外.getCode():TypCdEnum.DBU.getCode());
			l184m01a.setDataDate(date);
			if(overSea){
				l184m01a.setOwnBrId(brNo);	
			}else{
				l184m01a.setOwnBrId(user.getUnitNo());
			}
			l184m01a.setBranchId(brNo);
			l184m01a.setCreator(user.getUserId());
			l184m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l184m01a.setExcelFile(docFile.getOid());

			l184a01a.setMainId(l184m01a.getMainId());			
			if(overSea){
				l184a01a.setAuthType("4");
				l184a01a.setAuthUnit(brNo);
				l184a01a.setOwnUnit(brNo);	
			}else{
				l184a01a.setAuthType("1");
				l184a01a.setAuthUnit(l184m01a.getOwnBrId());
				l184a01a.setOwnUnit(l184m01a.getOwnBrId());
			}
			
			this.save(l184m01a, l184a01a);

			if(overSea){
				rows = dwElf411ovsService.findELF411ByMaxDate(brNo, date);
				List<String> custIdlist = new ArrayList<String>();
				List<String> custNmlist = new ArrayList<String>();
				List<String> pkCustlist = new ArrayList<String>();
				List<String> cdrisknlist = new ArrayList<String>();
				List<String> cdrisknGradelist = new ArrayList<String>();
				List<String> cdriskolist = new ArrayList<String>();
				List<String> cdriskoGradelist = new ArrayList<String>();
				List<String> cntrnolist = new ArrayList<String>();
				List<Long> factAmtlist = new ArrayList<Long>();
				List<String> factCurrlist = new ArrayList<String>();
				List<Date> useInitDtlist = new ArrayList<Date>();
				List<Date> useDueDtlist = new ArrayList<Date>();
				List<Date> lnInitDtlist = new ArrayList<Date>();
				List<Date> lnDueDtist = new ArrayList<Date>();
				List<Date> lastDtlist = new ArrayList<Date>();
	
				if (rows.size() > 0) {
					List<String> custIds = new ArrayList<String>();
					List<String> dupNos = new ArrayList<String>();
					List<String> brNos = new ArrayList<String>();
//					StringBuffer temp = new StringBuffer();
					Map<String, String> temp2 = new HashMap<String, String>();
					for (Map<String, Object> dataMap411 : rows) {

						String key = Util.trim(dataMap411.get("CUST_ID")) +
								Util.trim(dataMap411.get("DUPNO")) + Util.trim(dataMap411.get("BR_NO"));
						if (temp2.containsKey(key)) {

						} else {
							temp2.put(key, "");
							custIds.add(Util.trim(dataMap411.get("CUST_ID")));
							dupNos.add(Util.trim(dataMap411.get("DUPNO")));
							brNos.add(Util.trim(dataMap411.get("BR_NO")));
						}

//						temp.setLength(0);
//						temp.append("('")
//								.append(Util.trim(dataMap411.get("CUST_ID")))
//								.append("','")
//								.append(Util.trim(dataMap411.get("DUPNO")))
//								.append("','")
//								.append(Util.trim(dataMap411.get("BR_NO")))
//								.append("')");
//						if (custIdsDupNosBranchs.toString()
//								.indexOf(temp.toString()) != -1) {
//
//						} else {
//							if (custIdsDupNosBranchs.length() > 0) {
//								custIdsDupNosBranchs.append(",");
//							}
//							custIdsDupNosBranchs.append(temp);
//						}
					}
					list = lms412Service
							.findELF412ByCustIdDupNoBranchs(custIds.toArray(new String[0]),
									dupNos.toArray(new String[0]), brNos.toArray(new String[0]));
					for (Map<String, Object> dataMap411 : rows) {
						boolean result = false;
						brNo = Util.trim(dataMap411.get("BR_NO"));
						String custId = Util.trim(dataMap411.get("CUST_ID"));
						custIdlist.add(custId);
						String dupNo = Util.trim(dataMap411.get("DUPNO"));
						String custNm = Util.trim(dataMap411.get("CUST_NM"));
						custNmlist.add(custNm);
						// 主要授信戶
						String pkCust = Util.trim(dataMap411.get("PK_CUST"));
						pkCustlist.add(pkCust);
						for (Map<String, Object> dataMap412 : list) {
							if (result) {
	
							} else if (custId.equals(Util.trim(dataMap412
									.get("CUSTID")))
									&& dupNo.equals(Util.trim(dataMap412
											.get("DUPNO")))
									&& brNo.equals(Util.trim(dataMap412
											.get("BRANCH")))) {
								// 新的信用評等
								String cdriskn = Util.trim(dataMap412
										.get("CRDTYPE"));
								cdrisknlist.add(cdriskn);
								cdrisknGradelist.add(Util.trim(dataMap412
										.get("CRDTTBL")));
								// 舊的信用評等
								String cdrisko = Util.trim(dataMap412
										.get("MOWTYPE"));
								cdriskolist.add(cdrisko);
								cdriskoGradelist.add(Util.trim(dataMap412
										.get("MOWTBL1")));
								// 上次覆審日
								Date lastDt = (Date) dataMap412.get("LRDATE");
								lastDtlist.add(lastDt);
								result = true;
							}
						}
						if (!result) {
							cdrisknlist.add("");
							cdriskolist.add("");
							lastDtlist.add(null);
						}
						// 額度序號
						String cntrno = Util.trim(dataMap411.get("CNTRNO"));
						cntrnolist.add(cntrno);
	
						String factCurr = Util.trim(dataMap411.get("FACT_CURR"));
						factCurrlist.add(factCurr);
	
						Long factAmt = LMSUtil.toBigDecimal(
								dataMap411.get("FACT_AMT")).longValue();
	
						// 轉台幣
						// factAmt = Arithmetic
						// .round(rate.toTWDAmt(factCurr,
						// BigDecimal.valueOf(factAmt)), 2).longValue();
	
						factAmtlist.add(factAmt);
	
						// 動用起日
						Date useInitDt = (Date) dataMap411.get("USE_INIT_DT");
						useInitDtlist.add(useInitDt);
						// 動用止日
						Date useDueDt = (Date) dataMap411.get("USE_DUE_DT");
						useDueDtlist.add(useDueDt);
						// 授信期間起日
						Date lnInitDt = (Date) dataMap411.get("LN_INIT_DT");
						lnInitDtlist.add(lnInitDt);
						// 授信期間迄日
						Date lnDueDt = (Date) dataMap411.get("LN_DUE_DT");
						lnDueDtist.add(lnDueDt);
	
					}
				}
				logger.info("into produceExcel");
				// 轉成EXCEL
				this.createExcel(rows.size(), brNo, ranMainId, date, listName,
						custIdlist, custNmlist, pkCustlist, cdrisknlist,
						cdrisknGradelist, cdriskolist, cdriskoGradelist,
						cntrnolist, factAmtlist, factCurrlist, useInitDtlist,
						useDueDtlist, lnInitDtlist, lnDueDtist, lastDtlist, docFile);
			}else{
				create_dbu_xls(l184m01a, docFile);
			}
			addResult = true;
		} finally {
			if (custIdsDupNosBranchs != null)
				custIdsDupNosBranchs.setLength(0);
			if (rows != null)
				rows.clear();
			if (list != null)
				list.clear();
		}
		return addResult;

	}
	
	private String _procDate(Date d){
		if(d==null){
			return "0000-00-00";
		}else{
			return Util.trim(TWNDate.toAD(d));
		}
	}
	
	private void create_dbu_xls(L184M01A meta,
			 DocFile docFile) throws Exception{
		WritableWorkbook workbook = null;
		WritableSheet sheet1 = null;
		File file = null;
		String filename = LMSUtil.getUploadFilePath(meta.getOwnBrId(), meta.getMainId(), docFile.getFieldId());
		try {
			file = new File(filename);
			file.mkdirs();	
			file = new File(filename + "/" + docFile.getOid() + ".xls");
			//---
			workbook = Workbook.createWorkbook(file);
			sheet1 = workbook.createSheet("Sheet1", 0);
			
			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			{
				cellFormatL.setAlignment(Alignment.LEFT);
				cellFormatL.setWrap(true);
				cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			{
				cellFormatR.setAlignment(Alignment.RIGHT);
				cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);	
			}
			
			WritableCellFormat cellNFormatL = new WritableCellFormat(headFont12);
			{
				cellNFormatL.setWrap(true);
				cellNFormatL.setAlignment(Alignment.LEFT);	
			}
			
			WritableCellFormat cellNFormatR = new WritableCellFormat(headFont12);
			{
				cellNFormatR.setWrap(true);
				cellNFormatR.setAlignment(Alignment.RIGHT);	
			}
			
			List<String[]> rows = new ArrayList<String[]>();		
			
			List<ELF411> elf411_list = misELF411Service.getData(meta.getBranchId(), CrsUtil.elf490YM_from_adDate(meta.getDataDate()));
			Map<String, Integer> headerMap = new LinkedHashMap<String, Integer>();
			headerMap.put("分行代號", 10);
			headerMap.put("客戶統編", 13);
			headerMap.put("客戶名稱", 30);
			headerMap.put("是否為主要戶", 8);	
			headerMap.put("信用風險內部評等", 10);	
			headerMap.put("信用評等", 10);	
			headerMap.put("額度序號", 17);	
			headerMap.put("額度(等值台幣)", 19);
			headerMap.put("授信期間起日", 15);	
			headerMap.put("授信期間止日", 15);
			headerMap.put("動用起日", 15);
			headerMap.put("動用止日", 15);
			headerMap.put("上次複審日期", 15);
			
			int colIdx = 0;
			for(String h: headerMap.keySet()){
				int colWidth = headerMap.get(h);
				sheet1.setColumnView(colIdx, colWidth);
				sheet1.addCell(new Label( colIdx, 0, h, cellFormatL));
				//---
				colIdx++;
			}
			//==============================
			if(CollectionUtils.isNotEmpty(elf411_list)){
				int totalColSize = headerMap.size();
				for(ELF411 elf411:elf411_list){
					String[] arr = new String[totalColSize];
					for(int i_col = 0; i_col <totalColSize ; i_col++){
						arr[i_col] = "";
					}
					arr[0] = Util.trim(elf411.getElf411_brno());//分行代號
					arr[1] = Util.trim(elf411.getElf411_custid());//客戶統編
					arr[2] = Util.trim(elf411.getElf411_custname());//客戶名稱
					arr[3] = Util.trim(elf411.getElf411_maincust());//是否為主要戶	
					arr[4] = Util.trim(elf411.getElf411_cdriskn());//信用風險內部評等	
					arr[5] = Util.trim(elf411.getElf411_cdrisko());//信用評等	
					arr[6] = Util.trim(elf411.getElf411_contract());//額度序號	
					arr[7] = NumConverter.addComma(elf411.getElf411_quota());//額度(等值台幣)
					arr[8] = _procDate(elf411.getElf411_durbeg());//授信期間起日	
					arr[9] = _procDate(elf411.getElf411_durend());//授信期間止日
					arr[10] = _procDate(elf411.getElf411_beg());//動用起日
					arr[11] = _procDate(elf411.getElf411_end());//動用止日
					arr[12] = _procDate(elf411.getElf411_lrdate());//上次複審日期				
					//---
					rows.add(arr);
				}
			}			
			//==============================			
			int i = 0;
			int shiftRows = 1;
			for(String[] arr: rows){	
				int colLen = arr.length;
				for(int i_col = 0; i_col <colLen ; i_col++){
					//數值欄位(向右對齊)
					boolean alignRight = (i_col==7);
					sheet1.addCell(new Label( i_col, shiftRows + i, arr[i_col], alignRight?cellFormatR:cellFormatL));	
										
				}
				//---	
				i++;
			}

			workbook.write();
			workbook.close();

		} catch (Exception e) {
			logger.error(StrUtils.getStackTrace(e) );
			throw e;
		} 
	}
	
	private void createExcel(int size, String brNo, String ranMainId,
			Date dataDateMax, String listName, List<String> custIdlist,
			List<String> custNmlist, List<String> pkCustlist,
			List<String> cdrisknlist, List<String> cdrisknGradelist,
			List<String> cdriskolist, List<String> cdriskoGradelist,
			List<String> cntrnolist, List<Long> factAmtlist,
			List<String> factCurrlist, List<Date> useInitDtlist,
			List<Date> useDueDtlist, List<Date> lnInitDtlist,
			List<Date> lnDueDtlist, List<Date> lastDtlist, DocFile docFile)
			throws Exception {
		Label label = null;
		String filename = null;
		String xlsOid = docFile.getOid();
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(brNo, ranMainId, listName);
		file = new File(filename);
		file.mkdirs();
		Map<String, String> crdTypeMap = null;
		Map<String, String> mowTypeMap = null;
		try {
			crdTypeMap = codetypeService.findByCodeType("lms1705s01_crdType");
			if (crdTypeMap == null) {
				crdTypeMap = new LinkedHashMap<String, String>();
			}
			mowTypeMap = codetypeService.findByCodeType("lms1705s01_crdType2");
			if (mowTypeMap == null) {
				mowTypeMap = new LinkedHashMap<String, String>();
			}
			String path = PropUtil.getProperty("loadFile.dir")
					+ "excel/L184M01A.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("新細明體"), 12);
			// 宣告文字格式
			WritableCellFormat NcellFormatC = new WritableCellFormat(headFont12);
			// 靠右
			NcellFormatC.setAlignment(Alignment.CENTRE);
			// 自動換行
			NcellFormatC.setWrap(true);
			// 宣告文字格式
			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont12);
			// 靠右
			NcellFormatL.setAlignment(Alignment.LEFT);
			// 自動換行
			NcellFormatL.setWrap(true);
			// 宣告文字格式
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont12);
			// 靠右
			NcellFormatR.setAlignment(Alignment.RIGHT);
			// 自動換行
			NcellFormatR.setWrap(true);
			for (int i = 0; i < size; i++) {

				// 分行代碼
				label = new Label(0, i + 1, brNo, NcellFormatL);
				sheet.addCell(label);

				// 客戶統編
				if (custIdlist.get(i) != null || custIdlist.get(i).isEmpty()) {
					label = new Label(1, i + 1, custIdlist.get(i), NcellFormatL);
				} else {
					label = new Label(1, i + 1, "");
				}
				sheet.addCell(label);
				// 客戶名稱
				if (custNmlist.get(i) != null || custNmlist.get(i).isEmpty()) {
					label = new Label(2, i + 1, custNmlist.get(i), NcellFormatL);
				} else {
					label = new Label(2, i + 1, "");
				}
				sheet.addCell(label);
				// 是否為主要授信戶
				if (pkCustlist.get(i) != null || pkCustlist.get(i).isEmpty()) {
					label = new Label(3, i + 1, pkCustlist.get(i), NcellFormatC);
				} else {
					label = new Label(3, i + 1, "");
				}
				sheet.addCell(label);

				// 信用評等內部風險 (新)
				if (cdriskolist.get(i) != null || cdriskolist.get(i).isEmpty()) {
					label = new Label(4, i + 1,
							Util.trim(mowTypeMap.get(cdriskolist.get(i)))
									+ cdriskoGradelist.get(i), NcellFormatC);
				} else {
					label = new Label(4, i + 1, "");
				}
				sheet.addCell(label);

				// 信用評等
				String crdType = Util.trim(cdrisknlist.get(i));
				if (Util.isNotEmpty(crdType)) {
					if (crdType.equals(UtilConstants.Casedoc.CrdTypeM.DBU大型企業)) {
						crdType = UtilConstants.Casedoc.CrdType.DBU大型企業;
					} else if (crdType
							.equals(UtilConstants.Casedoc.CrdTypeM.DBU中小型企業)) {
						crdType = UtilConstants.Casedoc.CrdType.DBU中小型企業;
					} else if (crdType
							.equals(UtilConstants.Casedoc.CrdTypeM.OBU)) {
						crdType = UtilConstants.Casedoc.CrdType.海外;
					} else if (crdType
							.equals(UtilConstants.Casedoc.CrdTypeM.未評等)) {
						crdType = "";
					}

					label = new Label(5, i + 1,
							Util.trim(crdTypeMap.get(crdType))
									+ Util.trim(cdrisknGradelist.get(i)),
							NcellFormatC);
				} else {
					label = new Label(5, i + 1, "");
				}
				sheet.addCell(label);

				// 額度序號
				if (cntrnolist.get(i) != null || cntrnolist.get(i).isEmpty()) {
					label = new Label(6, i + 1, cntrnolist.get(i), NcellFormatL);
				} else {
					label = new Label(6, i + 1, "");
				}
				sheet.addCell(label);
				if (factCurrlist.get(i) != null
						|| factCurrlist.get(i).isEmpty()) {
					label = new Label(7, i + 1, factCurrlist.get(i),
							NcellFormatC);
				} else {
					label = new Label(7, i + 1, "");
				}
				sheet.addCell(label);
				// 額度(等值台幣)
				if (factAmtlist.get(i) != null) {
					label = new Label(8, i + 1,
							NumConverter.addComma(factAmtlist.get(i)),
							NcellFormatR);
				} else {
					label = new Label(8, i + 1, "");
				}
				sheet.addCell(label);

				// 授信期間起日
				if (lnInitDtlist.get(i) != null) {
					label = new Label(9, i + 1,
							(lnInitDtlist.get(i)).toString(), NcellFormatC);
				} else {
					label = new Label(9, i + 1, "");
				}
				sheet.addCell(label);

				// 授信期間止日
				if (lnDueDtlist.get(i) != null) {
					label = new Label(10, i + 1,
							(lnDueDtlist.get(i)).toString(), NcellFormatC);
				} else {
					label = new Label(10, i + 1, "");
				}
				sheet.addCell(label);
				// 動用起日
				if (useInitDtlist.get(i) != null) {
					label = new Label(11, i + 1,
							(useInitDtlist.get(i)).toString(), NcellFormatC);
				} else {
					label = new Label(11, i + 1, "");
				}
				sheet.addCell(label);
				// 動用止日
				if (useDueDtlist.get(i) != null) {
					label = new Label(12, i + 1,
							(useDueDtlist.get(i)).toString(), NcellFormatC);
				} else {
					label = new Label(12, i + 1, "");
				}
				sheet.addCell(label);
				// 上次覆審日
				if (lastDtlist.get(i) != null) {
					label = new Label(13, i + 1,
							TWNDate.toAD(lastDtlist.get(i)), NcellFormatC);
				} else {
					label = new Label(13, i + 1, "");
				}
				sheet.addCell(label);

			}
			test.write();
			test.close();

		} catch (Exception e) {
			logger.error("LMS1835ServiceImpl createExcel EXCEPTION!!", e);
			throw new Exception();
		} finally {
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
		}

	}

	@Override
	public List<DocFile> findDocFile(String mainId, String FieldId) {
		List<DocFile> docFile = docFileDao.findByMainIdAndFieldId(mainId,
				FieldId);
		return docFile;

	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L184M01A) {
					// ((L170M01A) model).setDeletedTime(null);
					((L184M01A) model).setUpdater(user.getUserId());
					((L184M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l184m01aDao.save((L184M01A) model);
				} else if (model instanceof L184A01A) {
					l184a01aDao.save((L184A01A) model);

				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		if (clazz == L184A01A.class) {
			return l184a01aDao.findPage(search);
		} else if (clazz == L184M01A.class) {
			return l184m01aDao.findPage(search);
		}
		return null;
	}

	@Override
	public L184M01A fL184m01aByDataDateAndBrNO(Date date, String branchId) {
		return l184m01aDao.findByUniqueKey(date, branchId);
	}

	@Override
	public void delete(String[] mainIds) {
		for (String mainId : mainIds) {
			L184M01A l184m01a = l184m01aDao.findByMainId(mainId);
			if (l184m01a != null) {
				l184m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				this.save(l184m01a);
			}
		}
	}

}
