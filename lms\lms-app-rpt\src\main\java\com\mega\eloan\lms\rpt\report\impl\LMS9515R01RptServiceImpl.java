/* 
 *  LMS9515R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

import com.inet.report.ReportException;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L784S01A;
import com.mega.eloan.lms.model.L784S07A;
import com.mega.eloan.lms.rpt.pages.LMS9515V01Page;
import com.mega.eloan.lms.rpt.service.LMS9515R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9515Service;
import com.mega.sso.service.BranchService;

@Service
public class LMS9515R01RptServiceImpl extends AbstractCapService implements
		LMS9515R01RptService {
	@Resource
	UserInfoService userInfoService;

	@Resource
	LMS9515Service service9515;

	@Resource
	BranchService branch;

	@Autowired
	DocFileService fileService;

	@Resource
	DocFileDao docFileDao;

	@Resource
	LMSService lmsService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	LMS1401Service service1401;

	@Resource
	LMS1201Service service1201;

	@Resource
	LMS1405Service service1405;

	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	L140S01ADao l140s01aDao;

	@Resource
	LMS1205Service service1205;

	public final static String YYYY_MM_DD2 = "yyyyMMdd";

	/**
	 * 取得日期(XXXX-XX-XX)
	 * 
	 * @param date
	 *            日期
	 * @return 日期
	 */
	private String getDate(Date date) {
		String str = null;
		if (date == null) {
			str = "";
		} else {
			str = TWNDate.toAD(date);
		}
		return str;
	}

	/*
	 * (non-Javadoc)產生PDF(LMS9515R01_zh_TW.rpt)
	 * 
	 * @see
	 * com.mega.eloan.lms.rpt.service.LMS9515R01RptService#createReportType1
	 * (java.util.Map, int)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public OutputStream generateReport(List<?> list, int action,
			String listName, String ovUnitNo, Date dataStartDate,
			Date dataEndDate, String caseDept) throws CapException, IOException {
		OutputStream outputStream = null;
		OutputStream outputStreamToFile = null;
		Locale locale = null;
		String ranMainId = IDGenerator.getRandomCode();
		String randomCode = IDGenerator.getRandomCode();
		// list i=2的時候傳的是List<L784S01A> 其他的是傳List<Map<String,Object>>
		if (action == 2 && !list.isEmpty()) {
			// 已敘做授信案件明細 LMS.L784S01A.mainId=LMS.L784M01A.mainId
			L784S01A l784s01a = (L784S01A) list.get(0);
			ranMainId = l784s01a.getMainId();
		} else if (action == 2 && !list.isEmpty()) {
			// 已敘做授信案件明細 LMS.L784S01A.mainId=LMS.L784M01A.mainId
			L784S07A l784s07a = (L784S07A) list.get(0);
			ranMainId = l784s07a.getMainId();
		}
		DocFile docFile = new DocFile();
		docFile.setBranchId(ovUnitNo);
		docFile.setContentType("application/mspdf");
		docFile.setMainId(ranMainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setFieldId(listName);
		docFile.setSrcFileName(listName + ".pdf");
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setSysId(fileService.getSysId());
		docFile.setData(new byte[] {});
		fileService.save(docFile, false);

		String filename = null;
		String xlsOid = null;
		List<DocFile> docFileList = this.findDocFile(ranMainId, listName);
		filename = LMSUtil.getUploadFilePath(ovUnitNo, ranMainId, listName);
		for (DocFile temp : docFileList) {
			xlsOid = temp.getOid();
			File file = new File(filename);
			file.mkdirs();
		}
		File file2 = new File(filename + "/" + xlsOid + ".pdf");
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		int subLine = 7;
		Properties propEloanPage = null;
		boolean vaPrintResult = true;
		FileOutputStream fileOutputStream = null;
		try {
			propEloanPage = MessageBundleScriptCreator
					.getComponentResource(AbstractEloanPage.class);
			// zh_TW: 正體中文
			// zh_CN: 簡體中文
			// en_US: 英文
			locale = LMSUtil.getLocale();
			outputStream = null;
			switch (action) {
			case 1:
				outputStream = this.genLMS9515R01(
						(List<Map<String, Object>>) list, locale, action,
						randomCode, TWNDate.toAD(dataStartDate),
						TWNDate.toAD(dataEndDate));
				break;
			case 2:
				vaPrintResult = false;
				outputStream = this.genLMS9515R01((List<L784S01A>) list,
						locale, action, randomCode,
						TWNDate.toAD(dataStartDate), TWNDate.toAD(dataEndDate),
						ovUnitNo);
				break;
			case 3:
				outputStream = this.genLMS9515R01(
						(List<Map<String, Object>>) list, locale, action,
						randomCode, TWNDate.toAD(dataStartDate),
						TWNDate.toAD(dataEndDate));
				break;
			case 6:
				outputStream = this.genLMS9515R01(
						(List<Map<String, Object>>) list, locale, action,
						randomCode, TWNDate.toAD(dataStartDate),
						TWNDate.toAD(dataEndDate));
				break;
			case 8:
				outputStream = this.genLMS9515R01(
						(List<Map<String, Object>>) list, locale, action,
						randomCode, TWNDate.toAD(dataStartDate),
						TWNDate.toAD(dataEndDate));
				break;
			case 10:
				vaPrintResult = false;
				outputStream = this.genLMS9515R01(
						(List<Map<String, Object>>) list, locale, action,
						randomCode, TWNDate.toAD(dataStartDate),
						TWNDate.toAD(dataEndDate));
				break;
			}
			service9515
					.saveFlieName(list, xlsOid, action, ovUnitNo,
							dataStartDate, dataEndDate, ranMainId, caseDept,
							randomCode);

			if (outputStream != null) {
				pdfNameMap.put(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()),
						subLine);
			} else {
				pdfNameMap.put(null, subLine);
			}
			if (pdfNameMap != null && pdfNameMap.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
						propEloanPage.getProperty("PaginationText"), true,
						locale, subLine, vaPrintResult);
			}
			if (file2 != null) {
				fileOutputStream = new FileOutputStream(file2);
			}
			if (fileOutputStream != null) {
				outputStreamToFile = new DataOutputStream(fileOutputStream);
				if (outputStreamToFile != null) {
					outputStreamToFile
							.write(((ByteArrayOutputStream) outputStream)
									.toByteArray());
					outputStreamToFile.flush();
					outputStreamToFile.close();
				}
			}
		} catch (Exception e) {
			throw new CapException();
		} finally {
			if (file2 != null) {
				file2 = null;
			}
			if (fileOutputStream != null) {
				fileOutputStream.close();
				fileOutputStream = null;
			}
			if (outputStreamToFile != null) {
				outputStreamToFile.close();
				outputStreamToFile = null;
			}
		}
		return outputStream;
	}

	public OutputStream genLMS9515R01(List<?> list, Locale locale, int action,
			String randomCode, String startDate, String endDate)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		return this.genLMS9515R01(list, locale, action, randomCode, startDate,
				endDate, null);
	}

	@SuppressWarnings("unchecked")
	public OutputStream genLMS9515R01(List<?> list, Locale locale, int action,
			String randomCode, String startDate, String endDate, String brno)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		OutputStream outputStream = null;
		ReportGenerator generator = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);

		try {
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();

			generator = new ReportGenerator("report/rpt/LMS9515R"
					+ String.format("%02d", action) + "_" + locale.toString()
					+ ".rpt");

			String logoPath = lmsService.getLogoShowPath(
					UtilConstants.RPTPicType.兆豐LOGO, "00", "");
			rptVariableMap.put("LOGOSHOW", logoPath);

			switch (action) {
			case 1:
				titleRows = this.setL9515type1DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setL9515type1DataRptVariableMap(
						rptVariableMap, randomCode);
				break;
			case 2:
				//取得登入分行別
				String countryType = Util.trim(branch.getBranch(Util.nullToSpace(brno)).getCountryType());
				titleRows = this.setL9515type2DataTitleRows(titleRows,
						(List<L784S01A>) list, prop, locale, countryType);
				rptVariableMap = this.setL9515type2DataRptVariableMap(
						rptVariableMap, randomCode, TWNDate.valueOf(endDate),
						brno, countryType, prop);
				break;

			case 3:
				titleRows = this.setL9515type3DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setL9515type3DataRptVariableMap(
						rptVariableMap, (List<Map<String, Object>>) list,
						randomCode, startDate, endDate);
				break;
			case 6:
				titleRows = this.setL9515type6DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setL9515type6DataRptVariableMap(
						rptVariableMap, startDate, randomCode);
				break;
			case 8:
				titleRows = this.setL9515type8DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setTitleRowsDataRptVariableMap(
						rptVariableMap, (List<Map<String, Object>>) list);
				rptVariableMap = this.setL9515type8DataRptVariableMap(
						rptVariableMap, startDate, randomCode);
				break;
			case 10:
				titleRows = this.setL9515type10DataTitleRows(titleRows,
						(List<Map<String, Object>>) list);
				rptVariableMap = this.setL9515type10DataRptVariableMap(
						rptVariableMap, startDate, endDate, randomCode);
				break;
			}
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();

		} finally {

		}
		return outputStream;
	}

	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL9515type1DataRptVariableMap(
			Map<String, String> rptVariableMap, String randomCode)
			throws CapException {
		try {
			rptVariableMap.put("LMS9515R01.dataDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			rptVariableMap.put("LMS9515R01.printDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL9515type1DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		Map<String, String> mapInTitleRows = null;
		try {
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();
						// 客戶統一編號
						String column01 = Util.trim(dataCollection
								.get("custId"));
						// 名稱
						String column02 = Util.trim(dataCollection
								.get("custName"));

						// 額度序號
						String column03 = Util.trim(dataCollection
								.get("contract"));
						// 幣別
						String column04 = Util.trim(dataCollection
								.get("factSwft"));
						// 核准額度
						String column05 = NumConverter.addComma(LMSUtil
								.toBigDecimal(dataCollection.get("factAmt")));
						// 動用起日
						String column06 = TWNDate.toAD(TWNDate.valueOf(Util
								.trim(dataCollection.get("begDate"))));
						// 動用止日
						String column07 = TWNDate.toAD(TWNDate.valueOf(Util
								.trim(dataCollection.get("endDate"))));

						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", column06);
						mapInTitleRows.put("ReportBean.column07", column07);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL9515type2DataRptVariableMap(
			Map<String, String> rptVariableMap, String randomCode,
			Date dataEndDate, String brno, String countryType, Properties prop) throws CapException {
		try {
			rptVariableMap.put("LMS9515R02.year", TWNDate.toAD(dataEndDate)
					.split("-")[0]);
			rptVariableMap.put("LMS9515R02.month", TWNDate.toAD(dataEndDate)
					.split("-")[1]);
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
			// J-111-0039_11557_B1001
			// eloan授信管理系統,針對海外分行管理報表所產製之「已敘做授信案件清單」表頭比照國內增加分行名稱之列印
			rptVariableMap.put("L784M01A.BRNAME",
					Util.trim(branch.getBranchName(brno)));
			// J-113-0477 請協助修改eloan系統已敘做授信案件清單內容及資信簡表部分欄位
			// 泰國行替換表頭, 其餘維持不變
			if(Util.equals(UtilConstants.Country.泰國, countryType)){//國別為泰國
				Properties prop9515 = MessageBundleScriptCreator.getComponentResource(LMS9515R01RptServiceImpl.class);
				rptVariableMap.put("LMS9515R02.header", prop9515.getProperty("LMS9515R02.header"));
			}else{
				rptVariableMap.put("LMS9515R02.header", prop.getProperty("LMS9515R02.header"));
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 取得property對應
	 * 
	 * @param proPerty
	 *            property
	 * @return property對應
	 */
	private String getProPerty(String proPerty, Properties prop) {
		StringBuffer str = new StringBuffer();
		String[] temp = proPerty.split("\\|");
		for (String perty : temp) {
			if ("1".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type1")).append("、");
			} else if ("2".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type2")).append("、");
			} else if ("3".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type3")).append("、");
			} else if ("4".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type4")).append("、");
			} else if ("5".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type5")).append("、");
			} else if ("6".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type6")).append("、");
			} else if ("7".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type7")).append("、");
			} else if ("8".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type8")).append("、");
			} else if ("9".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type9")).append("、");
			} else if ("10".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type10")).append("、");
			} else if ("11".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type11")).append("、");
			} else if ("12".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type12")).append("、");
			} else if ("13".equals(perty)) {
				str.append(prop.getProperty("L140M01a.type13")).append("、");
			}
		}
		if (str.length() == 0) {
			str.append("、");
		}

		return str.toString().substring(0, str.length() - 1);
	}

	/**
	 * 取得useDeadline對應
	 * 
	 * @param useDeadline
	 *            useDeadline
	 * @param desp1
	 *            desp1
	 * @return 取得useDeadline對應
	 */
	private String getUseDeadline(String useDeadline, String desp1) {
		String str = null;
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		if ("0".equals(useDeadline) || "1".equals(useDeadline)
				|| "5".equals(useDeadline)) {
			str = desp1;
		} else if ("2".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.001");
			} else {
				str = prop.getProperty("L140M01as02.001") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}

		} else if ("3".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.003");
			} else {
				str = prop.getProperty("L140M01as02.003") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}

		} else if ("4".equals(useDeadline)) {
			if (Util.equals(LMSUtil.getLocale(), "en")) {
				str = desp1 + " " + prop.getProperty("L140M01as02.002") + " "
						+ prop.getProperty("L140M01as02.004");
			} else {
				str = prop.getProperty("L140M01as02.004") + " " + desp1 + " "
						+ prop.getProperty("L140M01as02.002");
			}

		} else {
			str = "";
		}
		return str;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL9515type2DataTitleRows(
			List<Map<String, String>> titleRows, List<L784S01A> list,
			Properties prop, Locale locale, String countryType) throws CapException {
		Map<String, String> mapInTitleRows = null;
		try {
			mapInTitleRows = Util.setColumnMap();
			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				// 為了column11信評資料要接露PD+LGD，這個map幫忙存PD可以減少查過多重複資料
				Map<String, String> pdGroupMap = new HashMap<String,String>();
				for (int j = 0; j < k; j++) {
					L784S01A l784s01a = list.get(j);
					mapInTitleRows = Util.setColumnMap();
					if (l784s01a != null) {
						// 核定日
						Date column01 = l784s01a.getEndDate();
						// 客戶統一編號
						String column02 = l784s01a.getCustId();
						// 戶名
						String column03 = l784s01a.getCustName();
						// 額度序號
						String column04 = l784s01a.getCntrNo();
						// 授信科目
						String column05 = l784s01a.getLnSubject();
						String curr = l784s01a.getCurrentApplyCurr();
						// 額度(元)
						BigDecimal column06 = LMSUtil.toBigDecimal(l784s01a
								.getCurrentApplyAmt());
						// 期間
						String column07 = l784s01a.getDesp1();
						// 備註_敘作續約或變更條件 L140M01A.property
						String column08 = this.getProPerty(
								l784s01a.getProperty(), prop);
						// 核准人 STAFFNO
						String column09 = Util.trim(userInfoService
								.getUserName(l784s01a.getStaffNo()));
						//J-113-0477 請協助調整eloan系統已敘做案件清單
						if(Util.equals(UtilConstants.Country.泰國, countryType) && Util.equals("en", locale)){
							//泰國行切英文版，需將核准人切換成英文名 如SSO未提供英文名一樣先放中文
							ElsUser elsuser = userInfoService.getUser(l784s01a.getStaffNo());
							if(elsuser != null && Util.isNotEmpty(elsuser.getEnUserName())){
								column09 = Util.trim(elsuser.getEnUserName());
							}	
						}
						// 報表亂碼 randomCode
						String column10 = l784s01a.getRandomCode();

						// J-109-0085_10702_B1001 Web
						// e-Loan已敘做授信案件清單及區域授信管理中心授權內外已核准/已婉卻授信案件報表，增加企、消金信用評等資料修改
						String L120M01A_mainId = l784s01a.getL120M01A_MainId();
						String L140M01A_mainId = l784s01a.getL140M01A_MainId();
						String column11 = null;
						if (Util.isNotEmpty(L120M01A_mainId)
								&& Util.isNotEmpty(L140M01A_mainId)) {
							L120M01A l120m01a = service1201
									.findL120m01aByMainId(L120M01A_mainId);
							L140M01A l140m01a = service1405
									.findL140m01aByMainId(L140M01A_mainId);
							String docType = l120m01a.getDocType();
							Map<String, String> crdTypeMap = codetypeservice
									.findByCodeType("CRDType",
											locale.toString());
							// 企金
							if (Util.nullToSpace(docType).equals("1")) {
								// L120S01A．借款人主檔
								List<L120S01A> l120s01aList = service1201
										.findL120s01aByMainIdForOrder(L120M01A_mainId);

								if (l140m01a == null) {
									l140m01a = new L140M01A();
								}
								List<L120S01C> l120s01cList = l120s01cList = service1201
										.findL120s01cByMainId(L120M01A_mainId);
								L120S01A l120s01aFor140 = null;
								for (L120S01A l120s01a : l120s01aList) {
									if (Util.nullToSpace(l120s01a.getCustId())
											.equals(Util.nullToSpace(l140m01a
													.getCustId()))
											&& Util.nullToSpace(
													l120s01a.getCustId())
													.equals(Util
															.nullToSpace(l140m01a
																	.getCustId()))) {
										l120s01aFor140 = l120s01a;
									}
								}
								if (l120s01aFor140 == null)
									l120s01aFor140 = new L120S01A();
								column11 = this.setL120S01CData(l120s01aFor140,
										l120s01cList, crdTypeMap, prop,
										l140m01a, countryType, locale);
								
								// J-113-0233 配合PD/LGD授權架構之應用
								// 只針對企金簽報書，補上PD LGD資訊
								String LMS_L180R01_SHOW_PDLGD = Util
										.trim(lmsService
												.getSysParamDataValue("LMS_L180R01_SHOW_PDLGD"));
								if ("Y".equals(LMS_L180R01_SHOW_PDLGD)) {
									String pdLgdInfo = lmsService
											.getPdGroupAndLgd(l120m01a,
													l140m01a, pdGroupMap);
									column11 = column11 + "<BR/>" + pdLgdInfo;
								}
							}
							// 消金
							else {
								// 判斷是否為海外
								if (LMSUtil.isOverSea_CLS(l120m01a)) {
									List<L120S01A> l120s01aList = service1205
											.findL120s01aByMainIdForOrder(L120M01A_mainId);
									L120S01A l120s01aFor140 = null;
									for (L120S01A l120s01a : l120s01aList) {
										if (Util.nullToSpace(
												l120s01a.getCustId()).equals(
												Util.nullToSpace(l140m01a
														.getCustId()))
												&& Util.nullToSpace(
														l120s01a.getDupNo())
														.equals(Util
																.nullToSpace(l140m01a
																		.getDupNo()))) {
											l120s01aFor140 = l120s01a;
										}
									}
									if (l120s01aFor140 == null)
										l120s01aFor140 = new L120S01A();
									column11 = this.set_cls_gradeData(l120m01a,
											l140m01a, prop);
								} else {
									Map<String, String> c120m01aMap = new HashMap<String, String>();
									Map<String, String> l140s01aMap = new HashMap<String, String>();
									List<L140S02A> l140s02as = l140s02aDao
											.findByMainId(L140M01A_mainId);
									this._c102m01aAndl140s01aMap(c120m01aMap,
											l140s01aMap, l120m01a, l140m01a);
									if (l140s02as.size() > 1) {
										column11 = Util.trim(shortModelKind(
												prop, l120m01a, l140m01a,
												l140s02as, c120m01aMap,
												l140s01aMap));
									} else {
										// 當額度明細表,只有1個產品時,執行此段程式
										L140S02A l140s02a = l140s02as.get(0);
										column11 = showModelKind(prop,
												l120m01a, l140m01a, l140s02a,
												c120m01aMap, l140s01aMap);
									}
								}
							}
						}

						mapInTitleRows.put("ReportBean.column01",
								this.getDate(column01));
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", curr + " "
								+ NumConverter.addComma(column06));
						String useDeadline = l784s01a.getUseDeadline();
						mapInTitleRows.put("ReportBean.column07",
								this.getUseDeadline(useDeadline, column07));
						mapInTitleRows.put("ReportBean.column08", column08);
						mapInTitleRows.put("ReportBean.column09", column09);
						mapInTitleRows.put("ReportBean.column10", column10);
						mapInTitleRows.put("ReportBean.column11", column11);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL9515type3DataRptVariableMap(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list,
			String randomCode, String startDate, String endDate)
			throws CapException {
		try {
			// 資料基準日
			rptVariableMap.put("LMS9515R03.STARTDATE", startDate);
			rptVariableMap.put("LMS9515R03.ENDDATE", endDate);
			// 資料產生日期
			rptVariableMap.put("LMS9515R03.createDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			// 印表日
			rptVariableMap.put("LMS9515R03.printDate",
					TWNDate.toAD(CapDate.getCurrentTimestamp()));
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param list
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL9515type3DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS9515V01Page.class);
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();
						// 客戶統一編號
						String column01 = (String) dataCollection.get("custId");
						// 客戶名稱
						String column02 = (String) dataCollection.get("cname");
						// 額度序號
						String column03 = (String) dataCollection.get("cntrno");
						// 性質
						String column04 = (String) dataCollection
								.get("property");
						if (!column04.isEmpty()) {
							column04 = pop.getProperty("L784M01a." + column04);
						}
						// 動用止日
						Date column05 = (Date) dataCollection.get("gutcdate");
						// 案號
						String column06 = (String) dataCollection.get("projno");
						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05",
								this.getDate(column05));
						mapInTitleRows.put("ReportBean.column06", column06);
					}
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	/**
	 * 塞值 RptVariableMap
	 * 
	 * @param rptVariableMap
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private Map<String, String> setL9515type6DataRptVariableMap(
			Map<String, String> rptVariableMap, String startDate,
			String randomCode) throws CapException {
		try {
			rptVariableMap.put("LMS9515R06.year", startDate.split("-")[0]);
			rptVariableMap.put("LMS9515R06.month", startDate.split("-")[1]);
			rptVariableMap.put("LMS9515R06.day", startDate.split("-")[2]);
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 塞值 TitleRows
	 * 
	 * @param titleRows
	 * @param dataCollection
	 * @return
	 * @throws CapException
	 */
	private List<Map<String, String>> setL9515type6DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		Map<String, String> mapInTitleRows = null;
		try {
			mapInTitleRows = Util.setColumnMap();

			if (!list.isEmpty()) {
				// 總共有K筆資料
				int k = list.size();
				for (int j = 0; j < k; j++) {
					Map<String, Object> dataCollection = list.get(j);
					if (dataCollection != null) {
						mapInTitleRows = Util.setColumnMap();

						String column01 = Util.trim((String) dataCollection
								.get("caseBrId"));
						// 客戶統一編號
						String column02 = Util.trim((String) dataCollection
								.get("custId"));
						// 戶名
						String column03 = Util.trim((String) dataCollection
								.get("custName"));
						// 案號
						String column04 = Util.trim((String) dataCollection
								.get("caseNo"));
						// 分屬
						String column05 = Util.trim((String) dataCollection
								.get("docType"));
						// 營運中心負責經辦
						String column06 = Util.trim((String) dataCollection
								.get("areaAppraiser"));
						// 分行最後送件日
						Date column07 = ((Date) dataCollection
								.get("areaSendInfo "));
						// 報表亂碼 randomCode
						String column08 = Util.trim((String) dataCollection
								.get("randomCode"));
						mapInTitleRows.put("ReportBean.column01", column01);
						mapInTitleRows.put("ReportBean.column02", column02);
						mapInTitleRows.put("ReportBean.column03", column03);
						mapInTitleRows.put("ReportBean.column04", column04);
						mapInTitleRows.put("ReportBean.column05", column05);
						mapInTitleRows.put("ReportBean.column06", column06);
						mapInTitleRows.put("ReportBean.column07",
								this.getDate(column07));
						mapInTitleRows.put("ReportBean.column08", column08);
					}
					titleRows.add(mapInTitleRows);
				}

			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}
		return titleRows;
	}

	private String formatBigDecimal(BigDecimal number) {
		if (number == null) {
			return "";
		} else if ("0".equals(String.valueOf(number))) {
			return "";
		} else {
			return NumConverter.addComma(number);
		}
	}

	private Map<String, String> setL9515type8DataRptVariableMap(
			Map<String, String> rptVariableMap, String startDate,
			String randomCode) throws CapException {
		try {
			rptVariableMap.put("LMS9515R08.year", startDate.split("-")[0]);
			rptVariableMap.put("LMS9515R08.month", startDate.split("-")[1]);
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	@SuppressWarnings("unused")
	private List<Map<String, String>> setL9515type8DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();
			if (!list.isEmpty()) {
				for (Map<String, Object> dataCollection : list) {
					mapInTitleRows = Util.setColumnMap();

					String column01 = Util.trim((String) dataCollection
							.get("brNo"));
					// 新作
					BigDecimal column02 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM1"));
					BigDecimal column03 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM1AMT")),
							new BigDecimal(1000), 0);

					// 續約
					BigDecimal column04 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM2"));
					BigDecimal column05 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM2AMT")),
							new BigDecimal(1000), 0);

					// 變更條件
					BigDecimal column06 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM3"));
					BigDecimal column07 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM3AMT")),
							new BigDecimal(1000), 0);

					// 逾放展期轉正常
					BigDecimal column08 = null;
					BigDecimal column09 = null;

					// 合計
					BigDecimal column10 = column02.add(column04).add(column06);
					BigDecimal column11 = column03.add(column05).add(column07);
					// 無擔保授信
					BigDecimal column12 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM4"));
					BigDecimal column13 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM4AMT")),
							new BigDecimal(1000), 0);

					// 擔保授信
					BigDecimal column14 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM5"));
					BigDecimal column15 = Arithmetic.div(LMSUtil
							.toBigDecimal(dataCollection.get("tCITEM5AMT")),
							new BigDecimal(1000), 0);

					BigDecimal column16 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM6"));
					BigDecimal column17 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM7"));
					BigDecimal column18 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM8"));
					BigDecimal column19 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM9"));
					BigDecimal column20 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM10"));
					BigDecimal column21 = LMSUtil.toBigDecimal(dataCollection
							.get("tCITEM11"));

					mapInTitleRows.put("ReportBean.column01",
							branch.getBranchName(column01));
					mapInTitleRows.put("ReportBean.column02",
							this.formatBigDecimal(column02));
					mapInTitleRows.put("ReportBean.column03",
							this.formatBigDecimal(column03));
					mapInTitleRows.put("ReportBean.column04",
							this.formatBigDecimal(column04));
					mapInTitleRows.put("ReportBean.column05",
							this.formatBigDecimal(column05));
					mapInTitleRows.put("ReportBean.column06",
							this.formatBigDecimal(column06));
					mapInTitleRows.put("ReportBean.column07",
							this.formatBigDecimal(column07));
					mapInTitleRows.put("ReportBean.column08", "");
					mapInTitleRows.put("ReportBean.column09", "");
					mapInTitleRows.put("ReportBean.column10",
							this.formatBigDecimal(column10));
					mapInTitleRows.put("ReportBean.column11",
							this.formatBigDecimal(column11));
					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}

		return titleRows;
	}

	/**
	 * 過去半年內董事會（或常董會）權限核定之企業戶授信案件名單 LMS9515R10
	 * 
	 * 
	 */
	private Map<String, String> setL9515type10DataRptVariableMap(
			Map<String, String> rptVariableMap, String startDate,
			String endDate, String randomCode) throws CapException {
		try {
			rptVariableMap.put("LMS9515R10.startDate", startDate);
			rptVariableMap.put("LMS9515R10.endDate", endDate);
			rptVariableMap.put("L784M01A.RANDOMCODE", randomCode);
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	/**
	 * 過去半年內董事會（或常董會）權限核定之企業戶授信案件名單 LMS9515R10
	 * 
	 * 
	 */
	@SuppressWarnings("unused")
	private List<Map<String, String>> setL9515type10DataTitleRows(
			List<Map<String, String>> titleRows, List<Map<String, Object>> list)
			throws CapException {
		try {
			Map<String, String> mapInTitleRows = null;
			mapInTitleRows = Util.setColumnMap();
			if (!list.isEmpty()) {
				for (Map<String, Object> dataCollection : list) {
					mapInTitleRows = Util.setColumnMap();

					String column01 = Util.trim((String) dataCollection
							.get("CASEBRID")); // 分行代號
					String column02 = Util.trim((String) dataCollection
							.get("BRNAME")); // 分行名稱
					String column03 = Util.trim((String) dataCollection
							.get("CUSTID")); // ID
					String column04 = Util.trim((String) dataCollection
							.get("CUSTNAME")); // 授信戶名稱
					String column05 = Util.trim((String) dataCollection
							.get("ENDDATE")); // 核准日期
					String column06 = Util.trim((String) dataCollection
							.get("CASENO")); // 簽報書案號
					String column07 = Util.trim((String) dataCollection
							.get("CNTRNO")); // 額度序號
					BigDecimal column08 = LMSUtil.toBigDecimal(dataCollection
							.get("AMT")); // 核准額度
					// String column07 = Util.trim((String)
					// dataCollection.get("ITEMDSCR")); //授信期間
					String column09 = Util.trim((String) dataCollection
							.get("STATUS")); // 案件狀態
					String column10 = Util.trim((String) dataCollection
							.get("CASELVL")); // 授權等級

					mapInTitleRows.put("ReportBean.column01", column01);
					mapInTitleRows.put("ReportBean.column02", column02);
					mapInTitleRows.put("ReportBean.column03", column03);
					mapInTitleRows.put("ReportBean.column04", column04);
					mapInTitleRows.put("ReportBean.column05", column05);
					mapInTitleRows.put("ReportBean.column06", column06);
					mapInTitleRows.put("ReportBean.column07", column07);
					mapInTitleRows.put("ReportBean.column08", (Util.notEquals(
							"0", column08) ? this.formatBigDecimal(column08)
							: "0"));
					mapInTitleRows.put("ReportBean.column09", column09);
					mapInTitleRows.put("ReportBean.column10", column10);

					titleRows.add(mapInTitleRows);
				}
			} else {
				mapInTitleRows = Util.setColumnMap();
				titleRows.add(mapInTitleRows);
			}
		} catch (Exception e) {
			throw new CapException();
		}

		return titleRows;
	}

	private Map<String, String> setTitleRowsDataRptVariableMap(
			Map<String, String> rptVariableMap, List<Map<String, Object>> list)
			throws CapException {
		try {
			BigDecimal CITEM1Count = new BigDecimal(0);
			BigDecimal CITEM2Count = new BigDecimal(0);
			BigDecimal CITEM3Count = new BigDecimal(0);
			BigDecimal CITEM4Count = new BigDecimal(0);// 無擔保授信
			BigDecimal CITEM5Count = new BigDecimal(0);// 擔保授信
			BigDecimal totalCount = new BigDecimal(0);
			BigDecimal CITEM1Amt = new BigDecimal(0);
			BigDecimal CITEM2Amt = new BigDecimal(0);
			BigDecimal CITEM3Amt = new BigDecimal(0);
			BigDecimal CITEM4Amt = new BigDecimal(0);
			BigDecimal CITEM5Amt = new BigDecimal(0);
			BigDecimal totalAmt = new BigDecimal(0);
			for (Map<String, Object> dataCollection : list) {
				// // 新作
				// BigDecimal column02 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM1"));
				// BigDecimal column03 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM1AMT"));
				//
				// // 續約
				// BigDecimal column04 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM2"));
				// BigDecimal column05 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM2AMT"));
				//
				// // 變更條件
				// BigDecimal column06 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM3"));
				// BigDecimal column07 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM3AMT"));
				//
				// // 逾放展期轉正常
				// BigDecimal column08 = null;
				// BigDecimal column09 = null;
				//
				// // 合計
				// BigDecimal column10 = column02.add(column04).add(column06);
				// BigDecimal column11 = column03.add(column05).add(column07);
				// // 無擔保授信
				// BigDecimal column12 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM4"));
				// BigDecimal column13 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM4AMT"));
				//
				// // 擔保授信
				// BigDecimal column14 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM5"));
				// BigDecimal column15 =
				// LMSUtil.toBigDecimal(dataCollection.get("tCITEM5AMT"));
				//
				// CITEM1Count = CITEM1Count.add(column02);
				// CITEM2Count = CITEM2Count.add(column04);
				// CITEM3Count = CITEM3Count.add(column06);
				// totalCount = totalCount.add(column10);
				// CITEM4Count = CITEM4Count.add(column12);
				// CITEM5Count = CITEM5Count.add(column14);
				// CITEM1Amt = CITEM1Amt.add(column03);
				// CITEM2Amt = CITEM2Amt.add(column05);
				// CITEM3Amt = CITEM3Amt.add(column07);
				// totalAmt = totalAmt.add(column11);
				// CITEM4Amt = CITEM4Amt.add(column13);
				// CITEM5Amt = CITEM5Amt.add(column15);

				// 新作
				BigDecimal column02 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM1"));
				BigDecimal column03 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM1AMT")),
						new BigDecimal(1000), 0);

				// 續約
				BigDecimal column04 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM2"));
				BigDecimal column05 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM2AMT")),
						new BigDecimal(1000), 0);

				// 變更條件
				BigDecimal column06 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM3"));
				BigDecimal column07 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM3AMT")),
						new BigDecimal(1000), 0);

				// 逾放展期轉正常
				// BigDecimal column08 = null;
				// BigDecimal column09 = null;

				// 合計
				BigDecimal column10 = column02.add(column04).add(column06);
				BigDecimal column11 = column03.add(column05).add(column07);
				// 無擔保授信
				BigDecimal column12 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM4"));
				BigDecimal column13 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM4AMT")),
						new BigDecimal(1000), 0);

				// 擔保授信
				BigDecimal column14 = LMSUtil.toBigDecimal(dataCollection
						.get("tCITEM5"));
				BigDecimal column15 = Arithmetic.div(
						LMSUtil.toBigDecimal(dataCollection.get("tCITEM5AMT")),
						new BigDecimal(1000), 0);

				CITEM1Count = CITEM1Count.add(column02);
				CITEM2Count = CITEM2Count.add(column04);
				CITEM3Count = CITEM3Count.add(column06);
				totalCount = totalCount.add(column10);
				CITEM4Count = CITEM4Count.add(column12);
				CITEM5Count = CITEM5Count.add(column14);
				CITEM1Amt = CITEM1Amt.add(column03);
				CITEM2Amt = CITEM2Amt.add(column05);
				CITEM3Amt = CITEM3Amt.add(column07);
				totalAmt = totalAmt.add(column11);
				CITEM4Amt = CITEM4Amt.add(column13);
				CITEM5Amt = CITEM5Amt.add(column15);
			}
			rptVariableMap.put("SUMCOLUMN02",
					NumConverter.addComma(CITEM1Count));
			rptVariableMap.put("SUMCOLUMN04",
					NumConverter.addComma(CITEM2Count));
			rptVariableMap.put("SUMCOLUMN06",
					NumConverter.addComma(CITEM3Count));
			rptVariableMap.put("SUMCOLUMN08", "0");
			rptVariableMap
					.put("SUMCOLUMN10", NumConverter.addComma(totalCount));
			rptVariableMap.put("SUMCOLUMN12",
					NumConverter.addComma(CITEM4Count));
			rptVariableMap.put("SUMCOLUMN14",
					NumConverter.addComma(CITEM5Count));
			// rptVariableMap.put("SUMCOLUMN03",NumConverter.addComma(
			// Arithmetic.div(CITEM1Amt,new BigDecimal(1000),0)) );
			// rptVariableMap.put("SUMCOLUMN05",NumConverter.addComma(
			// Arithmetic.div(CITEM2Amt,new BigDecimal(1000),0)) );
			// rptVariableMap.put("SUMCOLUMN07",NumConverter.addComma(
			// Arithmetic.div(CITEM3Amt,new BigDecimal(1000),0)) );
			// rptVariableMap.put("SUMCOLUMN09","0");
			// rptVariableMap.put("SUMCOLUMN11",NumConverter.addComma(Arithmetic.div(totalAmt,new
			// BigDecimal(1000),0)));
			// rptVariableMap.put("SUMCOLUMN13",NumConverter.addComma(Arithmetic.div(CITEM4Amt,new
			// BigDecimal(1000),0)));
			// rptVariableMap.put("SUMCOLUMN15",NumConverter.addComma(Arithmetic.div(CITEM5Amt,new
			// BigDecimal(1000),0)));
			rptVariableMap.put("SUMCOLUMN03", NumConverter.addComma(CITEM1Amt));
			rptVariableMap.put("SUMCOLUMN05", NumConverter.addComma(CITEM2Amt));
			rptVariableMap.put("SUMCOLUMN07", NumConverter.addComma(CITEM3Amt));
			rptVariableMap.put("SUMCOLUMN09", "0");
			rptVariableMap.put("SUMCOLUMN11", NumConverter.addComma(totalAmt));
			rptVariableMap.put("SUMCOLUMN13", NumConverter.addComma(CITEM4Amt));
			rptVariableMap.put("SUMCOLUMN15", NumConverter.addComma(CITEM5Amt));
		} catch (Exception e) {
			throw new CapException();
		}
		return rptVariableMap;
	}

	@Override
	public void save(GenericBean... entity) {

	}

	@Override
	public void delete(GenericBean... entity) {

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {

		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {

		return null;
	}

	@Override
	public List<DocFile> findDocFile(String mainId, String FieldId) {
		List<DocFile> docFile = docFileDao.findByMainIdAndFieldId(mainId,
				FieldId);
		return docFile;

	}

	/**
	 * 企金信用評等(L120S01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @param crdTypeMap
	 *            bcodetype的crdType
	 * @return Map<String,String> rptVariableMap
	 */
	@SuppressWarnings("unused")
	private String setL120S01CData(L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop, L140M01A l140m01a, String countryType, Locale locale) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		StringBuffer str5 = new StringBuffer();// 個人信用評等 J-105-0156-001 Web
												// e-Loan企金額度明細表增加得引入消金個人信用評等

		//J-113-0477 請協助調整eloan系統已敘做案件清單
		//海業要求 信用評等僅有泰國要翻成英文, 其他國家暫時維持(多語言檔en評等都是中文)
		//原本源自於LMS1405S02Panel_en，copy一份改成泰行要的多語言
		Properties prop9515 = MessageBundleScriptCreator.getComponentResource(LMS9515R01RptServiceImpl.class);
		if(Util.equals(UtilConstants.Country.泰國, countryType)){
			prop = prop9515;
		}
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				//泰國行多語言選英文時，分行名也要顯示英文 其餘不變
				String branchName = Util.nullToSpace(branch.getBranchName(Util.nullToSpace(l120s01c.getCrdTBR())));
				if(Util.equals(UtilConstants.Country.泰國, countryType) && Util.equals("en", locale)){
					//顯示英文行名時，欄位過長會導致擠到下面的】，所以】欄位要多兩個空白
					branchName = Util.nullToSpace(Util.trim(branch.getBranch(Util.nullToSpace(l120s01c.getCrdTBR())).getEngName()));
				}
				
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType) || "OB".equals(crdType)
						|| "A0".equals(crdType) || "A1".equals(crdType)
						|| "A2".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}

					if ("A0".equals(crdType) || "A1".equals(crdType)
							|| "A2".equals(crdType)) {

						if (Util.isNumeric(grade)) {
							tempGrade.append(grade)
									.append(prop.getProperty("tempGrade"))
									.append(" ");
						}

						// 取得MOW等級之說明
						tempGrade.append(lmsService.getMowGradeName(prop,
								crdType, grade));

						str3.append(Util.nullToSpace(crdTypeMap.get(crdType)))
								.append(" : ")
								.append(tempGrade.toString())
								.append("【")
								.append(prop.getProperty("L120S01C.CRDTITLE02"))
								.append(Util.nullToSpace(TWNDate.toAD(l120s01c
										.getCrdTYear())))
								.append(" ")
								.append(prop.getProperty("L120S01C.CRDTITLE03"))
								.append(" ")
								.append(l120s01c.getCrdTBR())
								.append(" ")
								.append(branchName)
								.append("】");

					} else {
						str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
								.append(grade)
								.append("【")
								.append(prop.getProperty("L120S01C.CRDTITLE02"))
								.append(Util.nullToSpace(TWNDate.toAD(l120s01c
										.getCrdTYear())))
								.append(" ")
								.append(prop.getProperty("L120S01C.CRDTITLE03"))
								.append(" ")
								.append(l120s01c.getCrdTBR())
								.append(" ")
								.append(branchName)
								.append("】");
					}

				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(branchName)
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(branchName)
							.append("】");
				}
			}
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		String buscd = Util.trim(l120s01a.getBusCode());
		if (Util.equals(buscd, "130300") || Util.equals(buscd, "060000")) {
			// 個人戶
			str5.append(service1401.buildL140S03AStr(l140m01a.getMainId()));
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;
		// rptVariableMap.put("L120S01C.CRD", "");
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}

		// L120S01C.CRDTITLE06=當地評等 :
		if (str4.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE06")
					+ str4.toString());
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		// L120S01C.CRDTITLE07=個金評等 :
		if (str5.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE07")
					+ str5.toString());
		}

		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}

		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());
		// rptVariableMap.put("L120S01C.CRD", total.toString());
		return total.toString();
	}

	@SuppressWarnings("unused")
	private String setL120S01CData(L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType) || "OB".equals(crdType)
						|| "A0".equals(crdType) || "A1".equals(crdType)
						|| "A2".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}

					if ("A0".equals(crdType) || "A1".equals(crdType)
							|| "A2".equals(crdType)) {

						if (Util.isNumeric(grade)) {
							tempGrade.append(grade)
									.append(prop.getProperty("tempGrade"))
									.append(" ");
						}

						// 取得MOW等級之說明
						tempGrade.append(lmsService.getMowGradeName(prop,
								crdType, grade));

						str3.append(Util.nullToSpace(crdTypeMap.get(crdType)))
								.append(" : ")
								.append(tempGrade.toString())
								.append("【")
								.append(prop.getProperty("L120S01C.CRDTITLE02"))
								.append(Util.nullToSpace(TWNDate.toAD(l120s01c
										.getCrdTYear())))
								.append(" ")
								.append(prop.getProperty("L120S01C.CRDTITLE03"))
								.append(" ")
								.append(l120s01c.getCrdTBR())
								.append(" ")
								.append(Util.nullToSpace(branch
										.getBranchName(Util
												.nullToSpace(l120s01c
														.getCrdTBR()))))
								.append("】");

					} else {
						str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
								.append(grade)
								.append("【")
								.append(prop.getProperty("L120S01C.CRDTITLE02"))
								.append(Util.nullToSpace(TWNDate.toAD(l120s01c
										.getCrdTYear())))
								.append(" ")
								.append(prop.getProperty("L120S01C.CRDTITLE03"))
								.append(" ")
								.append(l120s01c.getCrdTBR())
								.append(" ")
								.append(Util.nullToSpace(branch
										.getBranchName(Util
												.nullToSpace(l120s01c
														.getCrdTBR()))))
								.append("】");
					}

				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {

					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			}
		}

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;
		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}

		// L120S01C.CRDTITLE06=當地評等 :
		if (str4.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE06")
					+ str4.toString());
		}

		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}
		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());

		return total.toString();
	}

	private String set_cls_gradeData(L120M01A l120m01a, L140M01A l140m01a,
			Properties prop) {
		// L120S01H.clsnagrade=未評等
		// L120S01H.clsnograde=免辦
		String Result = null;
		C120S01A c120s01a = service1205
				.findC120S01AByUniqueKey(l120m01a.getMainId(),
						l140m01a.getCustId(), l140m01a.getDupNo());
		if (true) {
			/*
			 * 有 rptVariableMap.put("L120S01C.CRD", ""); 在rpt隱藏公式才會落入
			 * L120S01C.CRD='' 無 rptVariableMap.put("L120S01C.CRD", "");
			 * 在rpt隱藏公式，L120S01C.CRD 可能是 null
			 */

			// 影響 rpt 的row【未選擇模型評等】隱藏與否
			// rptVariableMap.put("L120S01C.CRD", "");
		}

		if (c120s01a != null) {
			if ("CK".equals(c120s01a.getO_crdType())) {
				Result = c120s01a.getO_grade();
			} else if ("NA".equals(c120s01a.getO_crdType())) {
				Result = prop.getProperty("L120S01H.clsnagrade");
			} else if ("NO".equals(c120s01a.getO_crdType())) {
				Result = prop.getProperty("L120S01H.clsnograde");
			}
		}
		if (true) {
			String cntrNoAdoptGrade = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getRatingFlag()))
					&& OverSeaUtil.isCaseDoc_CLS_RatingFlag_ON(l120m01a)) {

				cntrNoAdoptGrade = service1405.build_l140m01aAdoptGrade(
						l140m01a, "<br/>");
				// ● 第一類：無消金內部評等(非日本、澳洲、泰國的海外分行)
				// ● 第二類：有消金內部評等(日本、澳洲、泰國的海外分行)
				// 此區塊為第二類，第一類則維持使用Result即可
				Result = cntrNoAdoptGrade;
			}
			// rptVariableMap.put("cntrNoAdoptGrade", cntrNoAdoptGrade);
		}
		return Result;
	}

	private void _c102m01aAndl140s01aMap(Map<String, String> c120m01aMap,
			Map<String, String> l140s01aMap, L120M01A l120m01a,
			L140M01A l140m01a) {

		List<C120M01A> c120m01alist = c120m01aDao.findByMainId(l120m01a
				.getMainId());
		List<L140S01A> l140s01as = l140s01aDao.findByMainId(l140m01a
				.getMainId());

		for (C120M01A c120m01a : c120m01alist) {
			String custPos = "";
			String tCustid = "";
			String tDupNo = "";
			String gCustid = "";
			String gDupNo = "";

			tCustid = Util.trim(c120m01a.getCustId());
			tDupNo = Util.trim(c120m01a.getDupNo());

			for (L140S01A l140s01a : l140s01as) {
				gCustid = Util.trim(l140s01a.getCustId());
				gDupNo = Util.trim(l140s01a.getDupNo());

				if ((gCustid + gDupNo).equals(tCustid + tDupNo)) {
					custPos = Util.trim(l140s01a.getCustPos());
					break;
				}
			}

			c120m01aMap
					.put(tCustid + tDupNo, Util.trim(c120m01a.getCustName()));
			l140s01aMap.put(tCustid + tDupNo, custPos);
		}
	}

	/**
	 * 單筆版信評組字內容
	 */
	private String showModelKind(Properties prop_CLS1141R01RptServiceImpl,
			L120M01A l120m01a, L140M01A l140m01a, L140S02A l140s02a,
			Map<String, String> c120m01aMap, Map<String, String> l140s01aMap) {
		String custPos = "";
		String tCustid = "";
		String tDupNo = "";
		String gCustid = "";
		String gDupNo = "";

		if (LMSUtil.isParentCase(l120m01a)) {
			return "";
		}

		String grade1 = Util.trim(l140s02a.getGrade1());

		tCustid = Util.trim(l140s02a.getCustId());
		tDupNo = Util.trim(l140s02a.getDupNo());
		gCustid = Util.trim(l140m01a.getCustId());
		gDupNo = Util.trim(l140m01a.getDupNo());
		custPos = Util.trim(l140s01aMap.get(tCustid + tDupNo));
		if (("C").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosC");
		} else if (("N").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosN");
		} else if (("G").equals(custPos)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosG");
		} else if ((tCustid + tDupNo).equals(gCustid + gDupNo)) {
			custPos = prop_CLS1141R01RptServiceImpl
					.getProperty("L140S01A.custPosK");
		}

		String modelKindDesc = MessageFormat.format(
				prop_CLS1141R01RptServiceImpl
						.getProperty("CLS1151R03.custId02"), custPos)
				+ "："
				+ Util.trim(l140s02a.getCustId())
				+ " "
				+ Util.trim(l140s02a.getDupNo())
				+ " "
				+ Util.trim(c120m01aMap.get(Util.trim(l140s02a.getCustId())
						+ Util.trim(l140s02a.getDupNo()))) + "，";

		if (Util.isNotEmpty(l140s02a)) {
			String modelKind = Util.trim(l140s02a.getModelKind());
			String property = Util.trim(l140s02a.getProperty());

			HashSet<String> prop7_8 = new HashSet<String>();
			{
				prop7_8.add(UtilConstants.Cntrdoc.Property.不變);
				prop7_8.add(UtilConstants.Cntrdoc.Property.取消);
			}
			if (prop7_8.contains(property)) {
				return "";// 以前印出prop_CLS1141R01RptServiceImpl.getProperty("CLS1151R03.grade3");本案無評等
			} else {
				Properties prop = MessageBundleScriptCreator
						.getComponentResource(CLS1151S01Page.class);
				if (Util.equals(UtilConstants.L140S02AModelKind.免辦, modelKind)) {
					return prop.getProperty("L140S02A.modelKind.0");
				} else if (Util.equals(UtilConstants.L140S02AModelKind.房貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.1")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.2")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸,
						modelKind)) {
					return modelKindDesc
							+ prop.getProperty("L140S02A.modelKind.3")
							+ "："
							+ LMSUtil.getFinalGrade(l140s02a.getModelKind(),
									grade1);
				}
			}
		}
		return modelKindDesc
				+ prop_CLS1141R01RptServiceImpl
						.getProperty("CLS1151R03.grade2")
				+ "："
				+ LMSUtil.getFinalGrade(UtilConstants.L140S02AModelKind.房貸,
						grade1);
	}

	/**
	 * 多筆版信評組字內容
	 */
	private String shortModelKind(Properties prop_CLS1141R01RptServiceImpl,
			L120M01A l120m01a, L140M01A l140m01a, List<L140S02A> l140s02as,
			Map<String, String> c120m01aMap, Map<String, String> l140s01aMap) {
		LinkedHashMap<Integer, String> map = new LinkedHashMap<Integer, String>();
		for (L140S02A l140s02a : l140s02as) {
			if (l140s02a.getSeq() == null) {
				continue;
			}
			String desc = showModelKind(prop_CLS1141R01RptServiceImpl,
					l120m01a, l140m01a, l140s02a, c120m01aMap, l140s01aMap);
			map.put(l140s02a.getSeq(), Util.trim(desc));
		}
		if (Util.isEmpty(Util.trim(StringUtils.join(map.values(), "")))) {
			// 當 N 個產品都是「不需評等」
			return "";
		}

		/*
		 * 加工，讓資料呈現 A. B.
		 */
		Map<Integer, String> seq_printStrMap = LMSUtil
				.getPrintStrForProdSeqNo(l140s02as
						.toArray(new L140S02A[l140s02as.size()]));
		List<String> r = new ArrayList<String>();
		for (Integer seq : map.keySet()) {
			String desc = map.get(seq);

			String seqStr = Util.trim(seq);
			if (seq_printStrMap.containsKey(seq)) {
				seqStr = seq_printStrMap.get(seq);
			}
			if (true) {
				/*
				 * 若有A, B個產品［A:續約、B:不變］ 為免只印出B. 加工處理為印出B.免辦評等
				 */

				// CLS1151R03.noGrade00=免辦評等
				// CLS1151R03.noGrade01=尚未勾選評等
				if (Util.isEmpty(Util.trim(desc))) {
					desc = prop_CLS1141R01RptServiceImpl
							.getProperty("CLS1151R03.noGrade00");
				}
			}
			r.add(seqStr + "." + desc);
		}
		return StringUtils.join(r, "<br/>");
	}
}