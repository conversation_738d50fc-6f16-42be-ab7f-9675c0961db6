/* 
 * LNF030.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.GenericBean;

/** 帳務檔 **/

public class MISLN30 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/**
	 * 放款帳號
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 14)
	@Column(name = "LNF030_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf030_loan_no;

	/**
	 * 客戶編號
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 11)
	@Column(name = "LNF030_CUST_ID", length = 11, columnDefinition = "CHAR(11)")
	private String lnf030_cust_id;

	/**
	 * 額度序號
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 12)
	@Column(name = "LNF030_CONTRACT", length = 12, columnDefinition = "CHAR(12)")
	private String lnf030_contract;

	/**
	 * 部門別
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_DEP_NO", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_dep_no;

	/**
	 * 幣別
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 3)
	@Column(name = "LNF030_SWFT", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_swft;

	/**
	 * 放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_LOAN_BAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_loan_bal;

	/**
	 * 性質別
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 2)
	@Column(name = "LNF030_CHARC_CODE", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_charc_code;

	/**
	 * 戶況
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_STATUS", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_status;

	/**
	 * 逾期代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT'9'
	 */
	@Size(max = 1)
	@Column(name = "LNF030_OVER_CODE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_over_code;

	/**
	 * 利率代碼
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 2)
	@Column(name = "LNF030_INT_CODE", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_int_code;

	/**
	 * 利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF030_INT_SPREAD", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf030_int_spread;

	/**
	 * 利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 4, groups = Check.class)
	@Column(name = "LNF030_INT_RATE", columnDefinition = "DECIMAL(06,4)")
	private BigDecimal lnf030_int_rate;

	/**
	 * 採用競爭利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_COMPETE_INT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_compete_int;

	/**
	 * 收息方式
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTRT_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intrt_type;

	/**
	 * 計息方式
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTCAL_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intcal_type;

	/**
	 * 利率方式
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INT_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_int_type;

	/**
	 * 利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTCHG_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intchg_type;

	/**
	 * 利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTCHG_CYCL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intchg_cycl;

	/** 上次利率變動日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_INTLCH_DATE", columnDefinition = "DATE")
	private Date lnf030_intlch_date;

	/** 下次利率變動日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_INTNCH_DATE", columnDefinition = "DATE")
	private Date lnf030_intnch_date;

	/**
	 * 利率變動基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF030_INTCHG_SDAY", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_intchg_sday;

	/**
	 * 利息出帳幣別
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTCUR_BASE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intcur_base;

	/**
	 * 計息天數基礎
	 * <p/>
	 * NOTNULL
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTDAY_BASE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intday_base;

	/** 開戶日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_OPEN_DATE", columnDefinition = "DATE")
	private Date lnf030_open_date;

	/** 首次撥款日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_LOAN_DATE", columnDefinition = "DATE")
	private Date lnf030_loan_date;

	/** 上次交易日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_LST_TXN_DT", columnDefinition = "DATE")
	private Date lnf030_lst_txn_dt;

	/** 上次計息日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_LST_INT_DT", columnDefinition = "DATE")
	private Date lnf030_lst_int_dt;

	/** 銷戶日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_CANCEL_DATE", columnDefinition = "DATE")
	private Date lnf030_cancel_date;

	/**
	 * 首撥金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_1ST_LN_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_1st_ln_amt;

	/**
	 * 進帳帳號一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF030_DP_ACT_1", length = 11, columnDefinition = "CHAR(11)")
	private String lnf030_dp_act_1;

	/**
	 * 進帳帳號二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF030_DP_ACT_2", length = 11, columnDefinition = "CHAR(11)")
	private String lnf030_dp_act_2;

	/**
	 * 扣帳帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 11)
	@Column(name = "LNF030_DP_ACT_3", length = 11, columnDefinition = "CHAR(11)")
	private String lnf030_dp_act_3;

	/**
	 * 用途別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_USE_TYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_use_type;

	/**
	 * 資金來源大類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_FUND_TYPE1", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_fund_type1;

	/**
	 * 資金來源小類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_FUND_TYPE2", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_fund_type2;

	/**
	 * 消費財公司週轉金貸款註記'
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INSTALMENT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_instalment;

	/**
	 * 適用計算股票擔保維持率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_STOCK_RATE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_stock_rate;

	/**
	 * 開戶行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_OPEN_BR", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_open_br;

	/**
	 * 開戶櫃員代號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 5)
	@Column(name = "LNF030_TELLER_NO", length = 5, columnDefinition = "CHAR(05)")
	private String lnf030_teller_no;

	/**
	 * 遠期出口押匯註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_LONG_EXPO", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_long_expo;

	/**
	 * 備註
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 60)
	@Column(name = "LNF030_MEMO", length = 60, columnDefinition = "CHAR(60)")
	private String lnf030_memo;

	/**
	 * 信保基金搭配帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF030_IPFD_LN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf030_ipfd_ln_no;

	/**
	 * 是否電催設定
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_ECOLLECTION", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_ecollection;

	/**
	 * 代墊款金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_RPLACE_BAL", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_rplace_bal;

	/**
	 * 自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INTRT_ACT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_intrt_act;

	/** 加速到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_SPEED_DATE", columnDefinition = "DATE")
	private Date lnf030_speed_date;

	/**
	 * 產品種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 對應LNF110
	 */
	@Size(max = 2)
	@Column(name = "LNF030_LOAN_CLASS", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_loan_class;

	/**
	 * 放款分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF030_LOAN_KIND", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_loan_kind;

	/**
	 * 資產評估第一類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_EVALUATE_1", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_evaluate_1;

	/**
	 * 資產評估第二類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_EVALUATE_2", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_evaluate_2;

	/**
	 * 資產評估第三類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_EVALUATE_3", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_evaluate_3;

	/**
	 * 資產評估第四類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_EVALUATE_4", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_evaluate_4;

	/**
	 * 資產評估第五類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_EVALUATE_5", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_evaluate_5;

	/**
	 * 資產評估情形及執行計畫
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 25)
	@Column(name = "LNF030_DESCRIBE", length = 25, columnDefinition = "GRAPHIC(25)")
	private String lnf030_describe;

	/** 產品種類轉換日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_LNCLS_CHGDT", columnDefinition = "DATE")
	private Date lnf030_lncls_chgdt;

	/**
	 * 放款分類二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF030_LOAN_KIND_2", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_loan_kind_2;

	/**
	 * 應予注意評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_ALLRCV_RATE", columnDefinition = "DECIMAL(05,2)")
	private BigDecimal lnf030_allrcv_rate;

	/**
	 * 可望收回評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_PARRCV_RATE", columnDefinition = "DECIMAL(05,2)")
	private BigDecimal lnf030_parrcv_rate;

	/**
	 * 收回困難評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_NILRCV_RATE", columnDefinition = "DECIMAL(05,2)")
	private BigDecimal lnf030_nilrcv_rate;

	/**
	 * 其他有欠正常放款原因代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 4)
	@Column(name = "LNF030_ABNORMAL_CD", length = 4, columnDefinition = "CHAR(04)")
	private String lnf030_abnormal_cd;

	/**
	 * 融資業務分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_LN_PURPOSE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_ln_purpose;

	/**
	 * 債權結案註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_CLOSE_FLAG", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_close_flag;

	/**
	 * 帳號動用限額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_LIMIT_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_limit_amt;

	/**
	 * 帳號動用共管註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_UNION_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_union_flag;

	/**
	 * 帳號動用共管序號計數器
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 0, groups = Check.class)
	@Column(name = "LNF030_COCNTRL_CNT", columnDefinition = "DECIMAL(02,0)")
	private Integer lnf030_cocntrl_cnt;

	/**
	 * 信保基金比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_IPFD_RATE", columnDefinition = "DECIMAL(05,2)")
	private BigDecimal lnf030_ipfd_rate;

	/**
	 * 新資產評估無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_NILRCV_RT_N", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal lnf030_nilrcv_rt_n;

	/**
	 * 代墊款無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_NILRCV_RT_R", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal lnf030_nilrcv_rt_r;

	/**
	 * 不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_NOASET_FG_N", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_noaset_fg_n;

	/**
	 * 代墊款不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_NOASET_FG_R", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_noaset_fg_r;

	/**
	 * 賦予資產擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_OBLIG_FG_N", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_oblig_fg_n;

	/**
	 * 賦予資產擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_OBLIG_SWFTN", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_oblig_swftn;

	/**
	 * 賦予資產擔保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_OBLIG_AMT_N", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_oblig_amt_n;

	/**
	 * 賦予代墊款擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_OBLIG_FG_R", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_oblig_fg_r;

	/**
	 * 賦予代墊款擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_OBLIG_SWFTR", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_oblig_swftr;

	/**
	 * 賦予代墊款擔保保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_OBLIG_AMT_R", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_oblig_amt_r;

	/**
	 * 賦予不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_SET_FLAG_N", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_set_flag_n;

	/**
	 * 賦予代墊款不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_SET_FLAG_R", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_set_flag_r;

	/**
	 * 資產轉換方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_SWITCH_KIND", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_switch_kind;

	/**
	 * 資產轉換對象ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 10)
	@Column(name = "LNF030_SWITCH_CUST", length = 10, columnDefinition = "CHAR(10)")
	private String lnf030_switch_cust;

	/**
	 * 資產轉換案號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 40)
	@Column(name = "LNF030_SWITCH_CASE", length = 40, columnDefinition = "CHAR(40)")
	private String lnf030_switch_case;

	/** 本期繳息日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_TURNINDT", columnDefinition = "DATE")
	private Date lnf030_turnindt;

	/** 下期繳息日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_NXINDT", columnDefinition = "DATE")
	private Date lnf030_nxindt;

	/** 第一次起息日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_FSINDT", columnDefinition = "DATE")
	private Date lnf030_fsindt;

	/**
	 * 計息週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF030_INCYCLE", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_incycle;

	/**
	 * 授信性質
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 4)
	@Column(name = "LNF030_CRDTCHAR", length = 4, columnDefinition = "CHAR(04)")
	private String lnf030_crdtchar;

	/**
	 * 扣稅負擔碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_TAXCD", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_taxcd;

	/**
	 * 利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF030_INT_RATE_6", columnDefinition = "DECIMAL(08,6)")
	private BigDecimal lnf030_int_rate_6;

	/**
	 * 利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF030_INT_SPRD_6", columnDefinition = "DECIMAL(08,6)")
	private BigDecimal lnf030_int_sprd_6;

	/**
	 * 24小時放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_LOAN_BAL_24", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_loan_bal_24;

	/**
	 * 計息小數位特殊註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_POINT_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_point_flag;

	/**
	 * 線上融資註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_ONLOAN_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_onloan_flag;

	/**
	 * 自動調整還本計劃
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_AUTO_PLAN", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_auto_plan;

	/**
	 * 承接之前放款帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 14)
	@Column(name = "LNF030_ORG_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String lnf030_org_loan_no;

	/**
	 * 本金自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_CAPRT_ACT", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_caprt_act;

	/**
	 * CUTTIME後累計借方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_ACH24_DRAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_ach24_dramt;

	/**
	 * CUTTIME後累計貸方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_ACH24_CRAMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_ach24_cramt;

	/**
	 * 遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF030_DEC_SPRD", columnDefinition = "DECIMAL(08,6)")
	private BigDecimal lnf030_dec_sprd;

	/**
	 * 遞減次數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 2, fraction = 6, groups = Check.class)
	@Column(name = "LNF030_DEC_CNT", columnDefinition = "DECIMAL(08,6)")
	private BigDecimal lnf030_dec_cnt;

	/**
	 * 下限利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_DOWNINT_FG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_downint_fg;

	/**
	 * 下限利率代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 2)
	@Column(name = "LNF030_DOWNINT_CD", length = 2, columnDefinition = "CHAR(02)")
	private String lnf030_downint_cd;

	/**
	 * 資產減損幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 3)
	@Column(name = "LNF030_LOSS_SWFT", length = 3, columnDefinition = "CHAR(03)")
	private String lnf030_loss_swft;

	/**
	 * 資產減損價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Digits(integer = 13, fraction = 2, groups = Check.class)
	@Column(name = "LNF030_LOSS_VALUE", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_loss_value;

	/** 資產減損評估日期 **/
	private String lnf030_evaluate_dt;

	/**
	 * 興建住宅註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_RESIDENTIAL", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_residential;

	/**
	 * 承接之前放款帳號原因
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_TRANS_CODE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_trans_code;

	/**
	 * 莫拉克颱風受災註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_MORAKOT_FG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_morakot_fg;

	/**
	 * 投資戶註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	@Size(max = 1)
	@Column(name = "LNF030_INVEST_FLAG", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_invest_flag;


	/** 天然及重大災害種類 **/
	@Size(max = 2)
	@Column(name = "LNF030_DISAS_TYPE", length = 2, columnDefinition = "VARCHAR(02)")
	private String lnf030_disas_type;
	
	/** 首次進帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_1ST_DATE", columnDefinition = "DATE")
	private Date lnf030_1st_date;
	
	/** 進帳基準日 **/
	@Size(max = 2)
	@Column(name = "LNF030_DP_SDAY", length = 2, columnDefinition = "VARCHAR(02)")
	private String lnf030_dp_sday;
	
	/** 下次進帳日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "LNF030_NXTDP_DATE", columnDefinition = "DATE")
	private Date lnf030_nxtdp_date;
	
	/** 每期進帳金額 **/
	@Digits(integer = 13, fraction = 2)
	@Column(name = "LNF030_PER_AMT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_per_amt;
	
	/** 每期扣息上限金額 **/
	@Digits(integer = 13, fraction = 2)
	@Column(name = "LNF030_INT_LIMIT", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal lnf030_int_limit;
	
	/** 應撥款總期數 **/
	@Digits(integer = 3, fraction = 0)
	@Column(name = "LNF030_LNTOT_TERM", columnDefinition = "DECIMAL(03,0)")
	private Integer lnf030_lntot_term;
	
	/** 房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
		, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	@Size(max = 5)
	@Column(name = "LNF030_MEGA_CODE", length = 5, columnDefinition = "CHAR(05)")
	private String lnf030_mega_code;
	
	/** 綠色支出類型 <br/>
	 * + LNF020_ESGSTYPE ,LNF020_ESGSUNRE是{只限} 企金戶用   <br/>
	 * + LNF030_ESGTYPE是企、消金戶皆可用 
	 */ 
	@Size(max = 1)
	@Column(name = "LNF030_ESGGTYPE", length = 1, columnDefinition = "CHAR(01)")
	private String lnf030_esggtype;
	
	/**
	 * 取得放款帳號
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_loan_no() {
		return this.lnf030_loan_no;
	}

	/**
	 * 設定放款帳號
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_loan_no(String value) {
		this.lnf030_loan_no = value;
	}

	/**
	 * 取得客戶編號
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_cust_id() {
		return this.lnf030_cust_id;
	}

	/**
	 * 設定客戶編號
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_cust_id(String value) {
		this.lnf030_cust_id = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_contract() {
		return this.lnf030_contract;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_contract(String value) {
		this.lnf030_contract = value;
	}

	/**
	 * 取得部門別
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_dep_no() {
		return this.lnf030_dep_no;
	}

	/**
	 * 設定部門別
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_dep_no(String value) {
		this.lnf030_dep_no = value;
	}

	/**
	 * 取得幣別
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_swft() {
		return this.lnf030_swft;
	}

	/**
	 * 設定幣別
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_swft(String value) {
		this.lnf030_swft = value;
	}

	/**
	 * 取得放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_loan_bal() {
		return this.lnf030_loan_bal;
	}

	/**
	 * 設定放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loan_bal(BigDecimal value) {
		this.lnf030_loan_bal = value;
	}

	/**
	 * 取得性質別
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_charc_code() {
		return this.lnf030_charc_code;
	}

	/**
	 * 設定性質別
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_charc_code(String value) {
		this.lnf030_charc_code = value;
	}

	/**
	 * 取得戶況
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_status() {
		return this.lnf030_status;
	}

	/**
	 * 設定戶況
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_status(String value) {
		this.lnf030_status = value;
	}

	/**
	 * 取得逾期代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT'9'
	 */
	public String getLnf030_over_code() {
		return this.lnf030_over_code;
	}

	/**
	 * 設定逾期代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT'9'
	 **/
	public void setLnf030_over_code(String value) {
		this.lnf030_over_code = value;
	}

	/**
	 * 取得利率代碼
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_int_code() {
		return this.lnf030_int_code;
	}

	/**
	 * 設定利率代碼
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_int_code(String value) {
		this.lnf030_int_code = value;
	}

	/**
	 * 取得利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_int_spread() {
		return this.lnf030_int_spread;
	}

	/**
	 * 設定利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_int_spread(BigDecimal value) {
		this.lnf030_int_spread = value;
	}

	/**
	 * 取得利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_int_rate() {
		return this.lnf030_int_rate;
	}

	/**
	 * 設定利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_int_rate(BigDecimal value) {
		this.lnf030_int_rate = value;
	}

	/**
	 * 取得採用競爭利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_compete_int() {
		return this.lnf030_compete_int;
	}

	/**
	 * 設定採用競爭利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_compete_int(String value) {
		this.lnf030_compete_int = value;
	}

	/**
	 * 取得收息方式
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_intrt_type() {
		return this.lnf030_intrt_type;
	}

	/**
	 * 設定收息方式
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_intrt_type(String value) {
		this.lnf030_intrt_type = value;
	}

	/**
	 * 取得計息方式
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_intcal_type() {
		return this.lnf030_intcal_type;
	}

	/**
	 * 設定計息方式
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_intcal_type(String value) {
		this.lnf030_intcal_type = value;
	}

	/**
	 * 取得利率方式
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_int_type() {
		return this.lnf030_int_type;
	}

	/**
	 * 設定利率方式
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_int_type(String value) {
		this.lnf030_int_type = value;
	}

	/**
	 * 取得利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_intchg_type() {
		return this.lnf030_intchg_type;
	}

	/**
	 * 設定利率變動方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_intchg_type(String value) {
		this.lnf030_intchg_type = value;
	}

	/**
	 * 取得利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_intchg_cycl() {
		return this.lnf030_intchg_cycl;
	}

	/**
	 * 設定利率變動週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_intchg_cycl(String value) {
		this.lnf030_intchg_cycl = value;
	}

	/** 取得上次利率變動日 **/
	public Date getLnf030_intlch_date() {
		return this.lnf030_intlch_date;
	}

	/** 設定上次利率變動日 **/
	public void setLnf030_intlch_date(Date value) {
		this.lnf030_intlch_date = value;
	}

	/** 取得下次利率變動日 **/
	public Date getLnf030_intnch_date() {
		return this.lnf030_intnch_date;
	}

	/** 設定下次利率變動日 **/
	public void setLnf030_intnch_date(Date value) {
		this.lnf030_intnch_date = value;
	}

	/**
	 * 取得利率變動基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_intchg_sday() {
		return this.lnf030_intchg_sday;
	}

	/**
	 * 設定利率變動基準日
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_intchg_sday(String value) {
		this.lnf030_intchg_sday = value;
	}

	/**
	 * 取得利息出帳幣別
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_intcur_base() {
		return this.lnf030_intcur_base;
	}

	/**
	 * 設定利息出帳幣別
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_intcur_base(String value) {
		this.lnf030_intcur_base = value;
	}

	/**
	 * 取得計息天數基礎
	 * <p/>
	 * NOTNULL
	 */
	public String getLnf030_intday_base() {
		return this.lnf030_intday_base;
	}

	/**
	 * 設定計息天數基礎
	 * <p/>
	 * NOTNULL
	 **/
	public void setLnf030_intday_base(String value) {
		this.lnf030_intday_base = value;
	}

	/** 取得開戶日 **/
	public Date getLnf030_open_date() {
		return this.lnf030_open_date;
	}

	/** 設定開戶日 **/
	public void setLnf030_open_date(Date value) {
		this.lnf030_open_date = value;
	}

	/** 取得首次撥款日 **/
	public Date getLnf030_loan_date() {
		return this.lnf030_loan_date;
	}

	/** 設定首次撥款日 **/
	public void setLnf030_loan_date(Date value) {
		this.lnf030_loan_date = value;
	}

	/** 取得上次交易日 **/
	public Date getLnf030_lst_txn_dt() {
		return this.lnf030_lst_txn_dt;
	}

	/** 設定上次交易日 **/
	public void setLnf030_lst_txn_dt(Date value) {
		this.lnf030_lst_txn_dt = value;
	}

	/** 取得上次計息日 **/
	public Date getLnf030_lst_int_dt() {
		return this.lnf030_lst_int_dt;
	}

	/** 設定上次計息日 **/
	public void setLnf030_lst_int_dt(Date value) {
		this.lnf030_lst_int_dt = value;
	}

	/** 取得銷戶日 **/
	public Date getLnf030_cancel_date() {
		return this.lnf030_cancel_date;
	}

	/** 設定銷戶日 **/
	public void setLnf030_cancel_date(Date value) {
		this.lnf030_cancel_date = value;
	}

	/**
	 * 取得首撥金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_1st_ln_amt() {
		return this.lnf030_1st_ln_amt;
	}

	/**
	 * 設定首撥金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_1st_ln_amt(BigDecimal value) {
		this.lnf030_1st_ln_amt = value;
	}

	/**
	 * 取得進帳帳號一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_dp_act_1() {
		return this.lnf030_dp_act_1;
	}

	/**
	 * 設定進帳帳號一
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_dp_act_1(String value) {
		this.lnf030_dp_act_1 = value;
	}

	/**
	 * 取得進帳帳號二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_dp_act_2() {
		return this.lnf030_dp_act_2;
	}

	/**
	 * 設定進帳帳號二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_dp_act_2(String value) {
		this.lnf030_dp_act_2 = value;
	}

	/**
	 * 取得扣帳帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_dp_act_3() {
		return this.lnf030_dp_act_3;
	}

	/**
	 * 設定扣帳帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_dp_act_3(String value) {
		this.lnf030_dp_act_3 = value;
	}

	/**
	 * 取得用途別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_use_type() {
		return this.lnf030_use_type;
	}

	/**
	 * 設定用途別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_use_type(String value) {
		this.lnf030_use_type = value;
	}

	/**
	 * 取得資金來源大類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_fund_type1() {
		return this.lnf030_fund_type1;
	}

	/**
	 * 設定資金來源大類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_fund_type1(String value) {
		this.lnf030_fund_type1 = value;
	}

	/**
	 * 取得資金來源小類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_fund_type2() {
		return this.lnf030_fund_type2;
	}

	/**
	 * 設定資金來源小類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_fund_type2(String value) {
		this.lnf030_fund_type2 = value;
	}

	/**
	 * 取得消費財公司週轉金貸款註記'
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_instalment() {
		return this.lnf030_instalment;
	}

	/**
	 * 設定消費財公司週轉金貸款註記'
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_instalment(String value) {
		this.lnf030_instalment = value;
	}

	/**
	 * 取得適用計算股票擔保維持率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_stock_rate() {
		return this.lnf030_stock_rate;
	}

	/**
	 * 設定適用計算股票擔保維持率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_stock_rate(String value) {
		this.lnf030_stock_rate = value;
	}

	/**
	 * 取得開戶行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_open_br() {
		return this.lnf030_open_br;
	}

	/**
	 * 設定開戶行
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_open_br(String value) {
		this.lnf030_open_br = value;
	}

	/**
	 * 取得開戶櫃員代號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_teller_no() {
		return this.lnf030_teller_no;
	}

	/**
	 * 設定開戶櫃員代號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_teller_no(String value) {
		this.lnf030_teller_no = value;
	}

	/**
	 * 取得遠期出口押匯註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_long_expo() {
		return this.lnf030_long_expo;
	}

	/**
	 * 設定遠期出口押匯註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_long_expo(String value) {
		this.lnf030_long_expo = value;
	}

	/**
	 * 取得備註
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_memo() {
		return this.lnf030_memo;
	}

	/**
	 * 設定備註
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_memo(String value) {
		this.lnf030_memo = value;
	}

	/**
	 * 取得信保基金搭配帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_ipfd_ln_no() {
		return this.lnf030_ipfd_ln_no;
	}

	/**
	 * 設定信保基金搭配帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ipfd_ln_no(String value) {
		this.lnf030_ipfd_ln_no = value;
	}

	/**
	 * 取得是否電催設定
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_ecollection() {
		return this.lnf030_ecollection;
	}

	/**
	 * 設定是否電催設定
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ecollection(String value) {
		this.lnf030_ecollection = value;
	}

	/**
	 * 取得代墊款金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_rplace_bal() {
		return this.lnf030_rplace_bal;
	}

	/**
	 * 設定代墊款金額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_rplace_bal(BigDecimal value) {
		this.lnf030_rplace_bal = value;
	}

	/**
	 * 取得自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_intrt_act() {
		return this.lnf030_intrt_act;
	}

	/**
	 * 設定自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_intrt_act(String value) {
		this.lnf030_intrt_act = value;
	}

	/** 取得加速到期日 **/
	public Date getLnf030_speed_date() {
		return this.lnf030_speed_date;
	}

	/** 設定加速到期日 **/
	public void setLnf030_speed_date(Date value) {
		this.lnf030_speed_date = value;
	}

	/**
	 * 取得產品種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 對應LNF110
	 */
	public String getLnf030_loan_class() {
		return this.lnf030_loan_class;
	}

	/**
	 * 設定產品種類
	 * <p/>
	 * NOTNULLWITHDEFAULT<br/>
	 * 對應LNF110
	 **/
	public void setLnf030_loan_class(String value) {
		this.lnf030_loan_class = value;
	}

	/**
	 * 取得放款分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_loan_kind() {
		return this.lnf030_loan_kind;
	}

	/**
	 * 設定放款分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loan_kind(String value) {
		this.lnf030_loan_kind = value;
	}

	/**
	 * 取得資產評估第一類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_evaluate_1() {
		return this.lnf030_evaluate_1;
	}

	/**
	 * 設定資產評估第一類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_evaluate_1(Integer value) {
		this.lnf030_evaluate_1 = value;
	}

	/**
	 * 取得資產評估第二類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_evaluate_2() {
		return this.lnf030_evaluate_2;
	}

	/**
	 * 設定資產評估第二類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_evaluate_2(Integer value) {
		this.lnf030_evaluate_2 = value;
	}

	/**
	 * 取得資產評估第三類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_evaluate_3() {
		return this.lnf030_evaluate_3;
	}

	/**
	 * 設定資產評估第三類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_evaluate_3(Integer value) {
		this.lnf030_evaluate_3 = value;
	}

	/**
	 * 取得資產評估第四類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_evaluate_4() {
		return this.lnf030_evaluate_4;
	}

	/**
	 * 設定資產評估第四類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_evaluate_4(Integer value) {
		this.lnf030_evaluate_4 = value;
	}

	/**
	 * 取得資產評估第五類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_evaluate_5() {
		return this.lnf030_evaluate_5;
	}

	/**
	 * 設定資產評估第五類比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_evaluate_5(Integer value) {
		this.lnf030_evaluate_5 = value;
	}

	/**
	 * 取得資產評估情形及執行計畫
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_describe() {
		return this.lnf030_describe;
	}

	/**
	 * 設定資產評估情形及執行計畫
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_describe(String value) {
		this.lnf030_describe = value;
	}

	/** 取得產品種類轉換日 **/
	public Date getLnf030_lncls_chgdt() {
		return this.lnf030_lncls_chgdt;
	}

	/** 設定產品種類轉換日 **/
	public void setLnf030_lncls_chgdt(Date value) {
		this.lnf030_lncls_chgdt = value;
	}

	/**
	 * 取得放款分類二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_loan_kind_2() {
		return this.lnf030_loan_kind_2;
	}

	/**
	 * 設定放款分類二
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loan_kind_2(String value) {
		this.lnf030_loan_kind_2 = value;
	}

	/**
	 * 取得應予注意評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_allrcv_rate() {
		return this.lnf030_allrcv_rate;
	}

	/**
	 * 設定應予注意評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_allrcv_rate(BigDecimal value) {
		this.lnf030_allrcv_rate = value;
	}

	/**
	 * 取得可望收回評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_parrcv_rate() {
		return this.lnf030_parrcv_rate;
	}

	/**
	 * 設定可望收回評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_parrcv_rate(BigDecimal value) {
		this.lnf030_parrcv_rate = value;
	}

	/**
	 * 取得收回困難評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_nilrcv_rate() {
		return this.lnf030_nilrcv_rate;
	}

	/**
	 * 設定收回困難評估比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_nilrcv_rate(BigDecimal value) {
		this.lnf030_nilrcv_rate = value;
	}

	/**
	 * 取得其他有欠正常放款原因代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_abnormal_cd() {
		return this.lnf030_abnormal_cd;
	}

	/**
	 * 設定其他有欠正常放款原因代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_abnormal_cd(String value) {
		this.lnf030_abnormal_cd = value;
	}

	/**
	 * 取得融資業務分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_ln_purpose() {
		return this.lnf030_ln_purpose;
	}

	/**
	 * 設定融資業務分類
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ln_purpose(String value) {
		this.lnf030_ln_purpose = value;
	}

	/**
	 * 取得債權結案註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_close_flag() {
		return this.lnf030_close_flag;
	}

	/**
	 * 設定債權結案註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_close_flag(String value) {
		this.lnf030_close_flag = value;
	}

	/**
	 * 取得帳號動用限額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_limit_amt() {
		return this.lnf030_limit_amt;
	}

	/**
	 * 設定帳號動用限額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_limit_amt(BigDecimal value) {
		this.lnf030_limit_amt = value;
	}

	/**
	 * 取得帳號動用共管註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_union_flag() {
		return this.lnf030_union_flag;
	}

	/**
	 * 設定帳號動用共管註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_union_flag(String value) {
		this.lnf030_union_flag = value;
	}

	/**
	 * 取得帳號動用共管序號計數器
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public Integer getLnf030_cocntrl_cnt() {
		return this.lnf030_cocntrl_cnt;
	}

	/**
	 * 設定帳號動用共管序號計數器
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_cocntrl_cnt(Integer value) {
		this.lnf030_cocntrl_cnt = value;
	}

	/**
	 * 取得信保基金比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_ipfd_rate() {
		return this.lnf030_ipfd_rate;
	}

	/**
	 * 設定信保基金比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ipfd_rate(BigDecimal value) {
		this.lnf030_ipfd_rate = value;
	}

	/**
	 * 取得新資產評估無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_nilrcv_rt_n() {
		return this.lnf030_nilrcv_rt_n;
	}

	/**
	 * 設定新資產評估無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_nilrcv_rt_n(BigDecimal value) {
		this.lnf030_nilrcv_rt_n = value;
	}

	/**
	 * 取得代墊款無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_nilrcv_rt_r() {
		return this.lnf030_nilrcv_rt_r;
	}

	/**
	 * 設定代墊款無法收回比例
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_nilrcv_rt_r(BigDecimal value) {
		this.lnf030_nilrcv_rt_r = value;
	}

	/**
	 * 取得不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_noaset_fg_n() {
		return this.lnf030_noaset_fg_n;
	}

	/**
	 * 設定不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_noaset_fg_n(String value) {
		this.lnf030_noaset_fg_n = value;
	}

	/**
	 * 取得代墊款不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_noaset_fg_r() {
		return this.lnf030_noaset_fg_r;
	}

	/**
	 * 設定代墊款不資產評估註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_noaset_fg_r(String value) {
		this.lnf030_noaset_fg_r = value;
	}

	/**
	 * 取得賦予資產擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_oblig_fg_n() {
		return this.lnf030_oblig_fg_n;
	}

	/**
	 * 設定賦予資產擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_fg_n(String value) {
		this.lnf030_oblig_fg_n = value;
	}

	/**
	 * 取得賦予資產擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_oblig_swftn() {
		return this.lnf030_oblig_swftn;
	}

	/**
	 * 設定賦予資產擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_swftn(String value) {
		this.lnf030_oblig_swftn = value;
	}

	/**
	 * 取得賦予資產擔保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_oblig_amt_n() {
		return this.lnf030_oblig_amt_n;
	}

	/**
	 * 設定賦予資產擔保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_amt_n(BigDecimal value) {
		this.lnf030_oblig_amt_n = value;
	}

	/**
	 * 取得賦予代墊款擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_oblig_fg_r() {
		return this.lnf030_oblig_fg_r;
	}

	/**
	 * 設定賦予代墊款擔保品註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_fg_r(String value) {
		this.lnf030_oblig_fg_r = value;
	}

	/**
	 * 取得賦予代墊款擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_oblig_swftr() {
		return this.lnf030_oblig_swftr;
	}

	/**
	 * 設定賦予代墊款擔保品幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_swftr(String value) {
		this.lnf030_oblig_swftr = value;
	}

	/**
	 * 取得賦予代墊款擔保保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_oblig_amt_r() {
		return this.lnf030_oblig_amt_r;
	}

	/**
	 * 設定賦予代墊款擔保保品價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_oblig_amt_r(BigDecimal value) {
		this.lnf030_oblig_amt_r = value;
	}

	/**
	 * 取得賦予不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_set_flag_n() {
		return this.lnf030_set_flag_n;
	}

	/**
	 * 設定賦予不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_set_flag_n(String value) {
		this.lnf030_set_flag_n = value;
	}

	/**
	 * 取得賦予代墊款不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_set_flag_r() {
		return this.lnf030_set_flag_r;
	}

	/**
	 * 設定賦予代墊款不良債信註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_set_flag_r(String value) {
		this.lnf030_set_flag_r = value;
	}

	/**
	 * 取得資產轉換方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_switch_kind() {
		return this.lnf030_switch_kind;
	}

	/**
	 * 設定資產轉換方式
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_switch_kind(String value) {
		this.lnf030_switch_kind = value;
	}

	/**
	 * 取得資產轉換對象ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_switch_cust() {
		return this.lnf030_switch_cust;
	}

	/**
	 * 設定資產轉換對象ID
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_switch_cust(String value) {
		this.lnf030_switch_cust = value;
	}

	/**
	 * 取得資產轉換案號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_switch_case() {
		return this.lnf030_switch_case;
	}

	/**
	 * 設定資產轉換案號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_switch_case(String value) {
		this.lnf030_switch_case = value;
	}

	/** 取得本期繳息日 **/
	public Date getLnf030_turnindt() {
		return this.lnf030_turnindt;
	}

	/** 設定本期繳息日 **/
	public void setLnf030_turnindt(Date value) {
		this.lnf030_turnindt = value;
	}

	/** 取得下期繳息日 **/
	public Date getLnf030_nxindt() {
		return this.lnf030_nxindt;
	}

	/** 設定下期繳息日 **/
	public void setLnf030_nxindt(Date value) {
		this.lnf030_nxindt = value;
	}

	/** 取得第一次起息日 **/
	public Date getLnf030_fsindt() {
		return this.lnf030_fsindt;
	}

	/** 設定第一次起息日 **/
	public void setLnf030_fsindt(Date value) {
		this.lnf030_fsindt = value;
	}

	/**
	 * 取得計息週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_incycle() {
		return this.lnf030_incycle;
	}

	/**
	 * 設定計息週期
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_incycle(String value) {
		this.lnf030_incycle = value;
	}

	/**
	 * 取得授信性質
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_crdtchar() {
		return this.lnf030_crdtchar;
	}

	/**
	 * 設定授信性質
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_crdtchar(String value) {
		this.lnf030_crdtchar = value;
	}

	/**
	 * 取得扣稅負擔碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_taxcd() {
		return this.lnf030_taxcd;
	}

	/**
	 * 設定扣稅負擔碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_taxcd(String value) {
		this.lnf030_taxcd = value;
	}

	/**
	 * 取得利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_int_rate_6() {
		return this.lnf030_int_rate_6;
	}

	/**
	 * 設定利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_int_rate_6(BigDecimal value) {
		this.lnf030_int_rate_6 = value;
	}

	/**
	 * 取得利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_int_sprd_6() {
		return this.lnf030_int_sprd_6;
	}

	/**
	 * 設定利率加減碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_int_sprd_6(BigDecimal value) {
		this.lnf030_int_sprd_6 = value;
	}

	/**
	 * 取得24小時放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_loan_bal_24() {
		return this.lnf030_loan_bal_24;
	}

	/**
	 * 設定24小時放款餘額
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loan_bal_24(BigDecimal value) {
		this.lnf030_loan_bal_24 = value;
	}

	/**
	 * 取得計息小數位特殊註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_point_flag() {
		return this.lnf030_point_flag;
	}

	/**
	 * 設定計息小數位特殊註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_point_flag(String value) {
		this.lnf030_point_flag = value;
	}

	/**
	 * 取得線上融資註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_onloan_flag() {
		return this.lnf030_onloan_flag;
	}

	/**
	 * 設定線上融資註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_onloan_flag(String value) {
		this.lnf030_onloan_flag = value;
	}

	/**
	 * 取得自動調整還本計劃
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_auto_plan() {
		return this.lnf030_auto_plan;
	}

	/**
	 * 設定自動調整還本計劃
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_auto_plan(String value) {
		this.lnf030_auto_plan = value;
	}

	/**
	 * 取得承接之前放款帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_org_loan_no() {
		return this.lnf030_org_loan_no;
	}

	/**
	 * 設定承接之前放款帳號
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_org_loan_no(String value) {
		this.lnf030_org_loan_no = value;
	}

	/**
	 * 取得本金自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_caprt_act() {
		return this.lnf030_caprt_act;
	}

	/**
	 * 設定本金自動扣帳註記(Y/N)
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_caprt_act(String value) {
		this.lnf030_caprt_act = value;
	}

	/**
	 * 取得CUTTIME後累計借方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_ach24_dramt() {
		return this.lnf030_ach24_dramt;
	}

	/**
	 * 設定CUTTIME後累計借方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ach24_dramt(BigDecimal value) {
		this.lnf030_ach24_dramt = value;
	}

	/**
	 * 取得CUTTIME後累計貸方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_ach24_cramt() {
		return this.lnf030_ach24_cramt;
	}

	/**
	 * 設定CUTTIME後累計貸方發生數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_ach24_cramt(BigDecimal value) {
		this.lnf030_ach24_cramt = value;
	}

	/**
	 * 取得遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_dec_sprd() {
		return this.lnf030_dec_sprd;
	}

	/**
	 * 設定遞減利率
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_dec_sprd(BigDecimal value) {
		this.lnf030_dec_sprd = value;
	}

	/**
	 * 取得遞減次數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_dec_cnt() {
		return this.lnf030_dec_cnt;
	}

	/**
	 * 設定遞減次數
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_dec_cnt(BigDecimal value) {
		this.lnf030_dec_cnt = value;
	}

	/**
	 * 取得下限利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_downint_fg() {
		return this.lnf030_downint_fg;
	}

	/**
	 * 設定下限利率註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_downint_fg(String value) {
		this.lnf030_downint_fg = value;
	}

	/**
	 * 取得下限利率代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_downint_cd() {
		return this.lnf030_downint_cd;
	}

	/**
	 * 設定下限利率代碼
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_downint_cd(String value) {
		this.lnf030_downint_cd = value;
	}

	/**
	 * 取得資產減損幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_loss_swft() {
		return this.lnf030_loss_swft;
	}

	/**
	 * 設定資產減損幣別
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loss_swft(String value) {
		this.lnf030_loss_swft = value;
	}

	/**
	 * 取得資產減損價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public BigDecimal getLnf030_loss_value() {
		return this.lnf030_loss_value;
	}

	/**
	 * 設定資產減損價值
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_loss_value(BigDecimal value) {
		this.lnf030_loss_value = value;
	}

	/** 取得資產減損評估日期 **/
	public String getLnf030_evaluate_dt() {
		return this.lnf030_evaluate_dt;
	}

	/** 設定資產減損評估日期 **/
	public void setLnf030_evaluate_dt(String value) {
		this.lnf030_evaluate_dt = value;
	}

	/**
	 * 取得興建住宅註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_residential() {
		return this.lnf030_residential;
	}

	/**
	 * 設定興建住宅註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_residential(String value) {
		this.lnf030_residential = value;
	}

	/**
	 * 取得承接之前放款帳號原因
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_trans_code() {
		return this.lnf030_trans_code;
	}

	/**
	 * 設定承接之前放款帳號原因
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_trans_code(String value) {
		this.lnf030_trans_code = value;
	}

	/**
	 * 取得莫拉克颱風受災註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_morakot_fg() {
		return this.lnf030_morakot_fg;
	}

	/**
	 * 設定莫拉克颱風受災註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_morakot_fg(String value) {
		this.lnf030_morakot_fg = value;
	}

	/**
	 * 取得投資戶註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 */
	public String getLnf030_invest_flag() {
		return this.lnf030_invest_flag;
	}

	/**
	 * 設定投資戶註記
	 * <p/>
	 * NOTNULLWITHDEFAULT
	 **/
	public void setLnf030_invest_flag(String value) {
		this.lnf030_invest_flag = value;
	}
	

	/** 取得天然及重大災害種類 **/
	public String getLnf030_disas_type() {
		return this.lnf030_disas_type;
	}
	/** 設定天然及重大災害種類 **/
	public void setLnf030_disas_type(String value) {
		this.lnf030_disas_type = value;
	}

	/** 取得首次進帳日 **/
	public Date getLnf030_1st_date() {
		return this.lnf030_1st_date;
	}
	/** 設定首次進帳日 **/
	public void setLnf030_1st_date(Date value) {
		this.lnf030_1st_date = value;
	}

	/** 取得進帳基準日 **/
	public String getLnf030_dp_sday() {
		return this.lnf030_dp_sday;
	}
	/** 設定進帳基準日 **/
	public void setLnf030_dp_sday(String value) {
		this.lnf030_dp_sday = value;
	}

	/** 取得下次進帳日 **/
	public Date getLnf030_nxtdp_date() {
		return this.lnf030_nxtdp_date;
	}
	/** 設定下次進帳日 **/
	public void setLnf030_nxtdp_date(Date value) {
		this.lnf030_nxtdp_date = value;
	}

	/** 取得每期進帳金額 **/
	public BigDecimal getLnf030_per_amt() {
		return this.lnf030_per_amt;
	}
	/** 設定每期進帳金額 **/
	public void setLnf030_per_amt(BigDecimal value) {
		this.lnf030_per_amt = value;
	}

	/** 取得每期扣息上限金額 **/
	public BigDecimal getLnf030_int_limit() {
		return this.lnf030_int_limit;
	}
	/** 設定每期扣息上限金額 **/
	public void setLnf030_int_limit(BigDecimal value) {
		this.lnf030_int_limit = value;
	}

	/** 取得應撥款總期數 **/
	public Integer getLnf030_lntot_term() {
		return this.lnf030_lntot_term;
	}
	/** 設定應撥款總期數 **/
	public void setLnf030_lntot_term(Integer value) {
		this.lnf030_lntot_term = value;
	}

	/** 取得房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public String getLnf030_mega_code() {
		return lnf030_mega_code;
	}
	/** 設定房貸引介子公司代號{90001:兆豐金控, 90002:	兆豐產險, 90003:	兆豐證券, 90004:	兆豐投信
	, 90005:兆豐資產管理, 90006:兆豐人身保代, 90007:兆豐創投, 90008:兆豐票券} **/
	public void setLnf030_mega_code(String lnf030_mega_code) {
		this.lnf030_mega_code = lnf030_mega_code;
	}

	/** 取得綠色支出類型 */
	public String getLnf030_esggtype() {
		return lnf030_esggtype;
	}
	/** 設定綠色支出類型 */
	public void setLnf030_esggtype(String lnf030_esggtype) {
		this.lnf030_esggtype = lnf030_esggtype;
	}
}
