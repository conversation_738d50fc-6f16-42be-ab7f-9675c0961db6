/* 
 * C241M01ZDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241M01Z;

/** 覆審異動歷程檔 **/
public interface C241M01ZDao extends IGenericDao<C241M01Z> {

	C241M01Z findByOid(String oid);
	
	List<C241M01Z> findByIndex1(String branch, String custId, String dupNo);
	
	List<C241M01Z> findProc(String reasonFlag, int batch_size);
	
}