/* 
 * C900S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900S01A;

/** 查核事項檔 **/
public interface C900S01ADao extends IGenericDao<C900S01A> {

	C900S01A findByOid(String oid);
	List<C900S01A> getAll();

	List<C900S01A> findByMainId(String mainId);

	C900S01A findByUniqueKey(String checkCode);

	List<C900S01A> findByIndex01(String checkCode);
}