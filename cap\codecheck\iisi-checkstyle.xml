<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN" "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: iisi-checkstyle
    Description: none
-->
<module name="Checker">
  <property name="severity" value="warning"/>
  <module name="TreeWalker">
    <module name="JavadocMethod">
      <property name="scope" value="public"/>
      <property name="allowUndeclaredRTE" value="true"/>
      <property name="allowMissingThrowsTags" value="true"/>
      <property name="allowMissingPropertyJavadoc" value="true"/>
      <property name="allowMissingJavadoc " value="true"/>
    </module>
    <module name="JavadocType">
      <property name="authorFormat" value="\S"/>
    </module>
    <module name="JavadocStyle">
      <property name="checkEmptyJavadoc" value="true"/>
      <property name="checkFirstSentence" value="false"/>
      <property name="checkHtml" value="false"/>
    </module>
    <module name="AvoidNestedBlocks"/>
    <module name="EmptyBlock"/>
    <module name="DoubleCheckedLocking"/>
    <module name="EmptyStatement"/>
    <module name="EqualsHashCode"/>
    <module name="IllegalInstantiation"/>
    <module name="InnerAssignment"/>
    <module name="SimplifyBooleanExpression"/>
    <module name="SimplifyBooleanReturn"/>
    <module name="RedundantThrows"/>
    <module name="TodoComment"/>
    <module name="UpperEll"/>
  </module>
  <module name="Translation"/>
</module>
