/* 
 * CLS1220V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;

/**
 * <pre>
 * 線上申貸原始資料(個金)
 * </pre>
 * 
 * @since 2015/4/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/4/18,003738,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1220v01")
public class CLS1220V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.Modify,
				LmsButtonEnum.View);
		// build i18n
		renderJsI18N(CLS1220M01Page.class);
		renderJsI18N(CLS1220V01Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1220V01Page');");
	}

}
