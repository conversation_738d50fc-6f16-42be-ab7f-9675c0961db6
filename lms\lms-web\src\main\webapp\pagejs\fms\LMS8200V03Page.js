$(document).ready(function(){
    // 判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(".select").hide()
        //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#queryDataTr" + DOMPurify.sanitize($(this).val())).show();
    });
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        }
        else {
            $("#managerNm").hide();
        }
    });
	
    var grid = $("#gridview").iGrid({
        handler: 'lms8200gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryL820m01A",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "approveTime",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{
	        colHeader: i18n.lms8200v01["L820M01A.custId"], //借款戶統一編號
	        align: "left", width: 100, sortable: true, name: 'custId',
	        formatter: 'click', 
	        onclick: openDoc
	    }, {
			colHeader: i18n.lms8200v01['L820M01A.dupNo'],//"重複序號",
		    name: 'dupNo',
		    width: 30,
		    sortable: true
		}, {
	        colHeader: i18n.lms8200v01["L820M01A.custName"], //借款戶名稱
	        align: "left", width: 100, sortable: true, name: 'custName'
	    }, {
            colHeader: i18n.lms8200v01['L820M01A.creator'],//"分行經辦",
            name: 'updater',
            width: 80,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms8200v01['L820M01A.approver'],//"覆核",
            name: 'approver',
            width: 80,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.lms8200v01["L820M01A.approveTime"], // 核准日期
                align: "left",
                width: 80, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'approveTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',
            data: {
                formAction: "queryL820m01A",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // L140M01M.error1=此功能不能多選
            CommonAPI.showMessage(i18n.lms8200m01["L820M01A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
		
    });
});
