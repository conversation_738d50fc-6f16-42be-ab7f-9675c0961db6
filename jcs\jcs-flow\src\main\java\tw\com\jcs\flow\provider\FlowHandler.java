package tw.com.jcs.flow.provider;

import tw.com.jcs.flow.FlowInstance;

/**
 * <h1>流程處理器</h1> 可在流程執行時，提供額外的處理
 * 
 * <AUTHOR> Software Inc.
 */
public interface FlowHandler {

    /**
     * 進行處理
     * 
     * @param instance
     *            流程實體
     * @param nodeName
     *            節點名稱
     * @param transition
     *            流程路徑名稱
     */
    void handle(FlowInstance instance, String nodeName, String transition);

}
