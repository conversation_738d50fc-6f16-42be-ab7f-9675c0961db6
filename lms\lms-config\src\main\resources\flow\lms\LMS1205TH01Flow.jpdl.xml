<?xml version="1.0" encoding="UTF-8"?>

<process handler="com.mega.eloan.lms.lms.flow.LMS1205TH01Flow" name="LMS1205TH01Flow" xmlns="http://jbpm.org/4.4/jpdl">
	<start g="298,-1,58,43" name="泰國子流程起案">
		<transition g="19,-8" name="to編製中" to="exclusive1"/>
	</start>
	<decision expr="#result=='waitCase'?'to待補件':'to編製中'" g="296,66,48,48" name="exclusive1">
		<transition g="-183,-48" name="to編製中" to="編製中"/>
		<transition g="-21,-23" name="to待補件" to="待補件"/>
	</decision>
	<state g="6,61,92,52" name="編製中" status="海外_編製中">
		<transition g="10,-8" name="to判斷行別" to="判斷行別"/>
	</state>
	<decision expr="#result" g="306,158,48,48" name="判斷行別">
		<transition g="155,178:-81,18" log="FORWARD" name="to總行待覆核" to="總行待覆核"/>
		<transition g="636,186:-7,-24" log="FORWARD" name="to分行待覆核" to="分行待覆核"/>
	</decision>
	<state auth="Accept" g="584,255,110,52" name="分行待覆核" status="海外_待覆核">
		<transition g="38,-24" name="to決策" to="決策"/>
	</state>
	<state auth="Accept" g="108,256,92,52" name="總行待覆核" status="海外_總行待覆核">
		<transition g="15,4" name="to呈核" to="呈核"/>
	</state>
	<decision expr="#result" g="336,259,30,35" name="決策">
		<transition g="-35,-36" log="FORWARD" name="to總行待覆核" to="總行待覆核"/>
		<transition g="70,16" log="BACK" name="to退回編製中" to="編製中"/>
		<transition g="34,-15" log="COMPLETE" name="to核定" to="授權內核定"/>
      <transition g="-49,-5" log="REJECT" name="to已婉卻" to="授權內婉卻"/>
	</decision>
	<decision expr="#result" g="134,372,48,48" name="呈核">
		<transition g="-54,-31" log="MOVE" name="to呈授管處" to="呈授管處"/>
		<transition g="-23,7" log="MOVE" name="to呈區域中心" to="呈區域中心"/>
		<transition g="16,-5" log="COMPLETE" name="to核定" to="結案"/>
		<transition g="9,3" log="REJECT" name="to已婉卻" to="已婉卻"/>
		<transition g="51,396:-37,-130" log="BACK" name="to退回編製中" to="編製中"/>
        <transition g="-80,-21" log="BACK" name="to退回" to="分行待覆核"/>
	</decision>
	<end g="-5,536,48,48" name="呈授管處"/>
	<end g="123,538,48,48" name="呈區域中心"/>
	<end g="351,521,48,48" name="結案" status="海外_已核准"/>
	<end g="242,513,48,48" name="已婉卻" status="海外_婉卻"/>
	<state g="681,60,92,52" name="待補件" status="海外_待補件">
		<transition g="-16,-31" name="to判斷行別" to="判斷行別"/>
	</state>
   <end g="466,198,48,48" name="授權內核定" status="海外_已核准"/>
   <end g="392,192,48,48" name="授權內婉卻" status="海外_婉卻"/>
</process>