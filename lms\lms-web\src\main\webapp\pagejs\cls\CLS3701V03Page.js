var _handler = "cls3701m01formhandler";
$(function(){	
	var hidden05O = true;
    if(viewstatus == "05O"){
        hidden05O = false;
    }
	var grid = $("#gridview").iGrid({
        handler: 'cls3701gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        postData: {
            formAction: "queryView3",
            docStatus : viewstatus
        },
        rowNum: 15,
        sortname: "createTime|custId",
        sortorder: "desc|asc|desc",//desc
        multiselect: true,
        colModel: [{
        	colHeader: i18n.cls3701m01["C126M01A.ownBrId"],
            align: "left",
            width: 100, // 設定寬
            name: 'ownBrId'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.custId"],
            align: "left", width: 90, sortable: true, name: 'custId',
            formatter: 'click', onclick: openDoc
        }, {
            colHeader: i18n.cls3701m01["C126M01A.dupNo"],
            align: "left", width: 10, sortable: true, name: 'dupNo'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.custName"],
            align: "left", width: 120, sortable: true, name: 'custName'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.agntNo"], //房仲代號
            align: "left",
            width: 100, // 設定寬
            name: 'agntNo'
        }, {
            colHeader: i18n.cls3701m01["C126M01A.statFlag"], //承作情形
            align: "left",
            width: 100, // 設定寬
            name: 'statFlag'
        }, {
            colHeader: i18n.cls3701m01['C126M01A.updater'],//經辦
            name: 'updater',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01["C126M01A.createTime"], //建立日期
            align: "center",
            width: 80, // 設定寬
            name: 'createTime',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d H:i:s',
                newformat: 'Y-m-d H:i'
            }
        }, {
            colHeader: i18n.cls3701m01['C126M01A.approver'],//覆核人員
            name: 'approver',
            width: 80,
            align: "center"
        }, {
            colHeader: i18n.cls3701m01['C126M01A.approveTime'],//覆核日期
            name: 'approveTime',
            width: 80,
            align: "center"
        },{
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docStatus',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        },
        codetypeItem: {}
    });
	
	function openDoc(cellvalue, options, rowObject) {
		$.form.submit({
			url : '../cls/cls3701m01/01',
			data : {
				'oid' : rowObject.oid,
				'mainOid' : rowObject.oid,
				'mainId' : rowObject.mainId,
				'mainDocStatus' : viewstatus
			},
			target : rowObject.oid
		});					
	};
	
	
    $("#buttonPanel").find("#btnView").click(function(){
    	var id = $("#gridview").getGridParam('selarrrow');
        if (id.length==0) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
        	// includeId.selData=請選擇一筆資料!!
        	return CommonAPI.showMessage(i18n.def["includeId.selData"]);
        } else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
    	openFilterBox();
    }).end().find("#btnProduceExcel").click(function(){
    	if(true){
    		var $div = $("#exportExcelForm").find("[itemType]");
    		var allKey = [];
    		$div.each(function(){
    			allKey.push($(this).attr("itemType"));
    		});
    		grid.codetypeItem = API.loadCombos(allKey);
    		$div.each(function(){
    			var $obj = $(this);
    			var itemType = $obj.attr("itemType");
    			if (itemType) {
    				var format = $obj.attr("itemFormat") || "{value} - {key}";
    				$obj.setItems({
    					space: $obj.attr("space") || true,
    					item: grid.codetypeItem[itemType],
    					format: format,
    					sort: $obj.attr("itemSort") || "asc",
    					size: $obj.attr("itemSize")
    				});
    			}
    		});
    		
    		$.ajax({
        		type : "POST",
        		handler : "codetypehandler",
        		data : {
        			formAction : "allBranchByUnitType"
        		},
        		success : function(responseData) {
        			var json = {
        				format : "{value} - {key}",
        				item : responseData
        			};
        			$("#xlsCaseBrId").setItems(json);

        		}
        	});
    	}
    	
    	$("#exportExcelForm").find("[name=applyTS_beg]").val(getmonthFirstDate());
		$("#exportExcelForm").find("[name=applyTS_end]").val(CommonAPI.getToday());
    	
		$("#exportExcelBox").thickbox({ // 使用選取的內容進行彈窗
	       title: i18n.def.query,
	       width: 450,
           height: 210,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	   if ($("#exportExcelForm").find("[name=agntNo]").val() == "") { // action_005=請先選取一筆以上之資料列
                       return CommonAPI.showMessage(i18n.cls3701m01["cls3701.err3"]);
                   }
            	   $.form.submit({
                       url: "../simple/FileProcessingService",
                       target: "_blank",
                       data: { 
                    	   caseBrId:$("#exportExcelForm").find("[name=xlsCaseBrId]").val(),
                    	   agntNo:$("#exportExcelForm").find("[name=agntNo]").val(),
                    	   applyTS_beg:$("#exportExcelForm").find("[name=applyTS_beg]").val(),
                           applyTS_end:$("#exportExcelForm").find("[name=applyTS_end]").val()+" 23:59:59",
                           fileDownloadName: "CLS3701R01.xls",
                           serviceName: "cls3701xlsservice"
                       }
                   });
            	   
            	   $.thickbox.close();
            	   grid.trigger("reloadGrid");
               },
               "cancel": function(){
            	   $.thickbox.close();            	  
               }
           }
		});	
    });
    
    // 篩選
    function openFilterBox(){
        var $filterForm = $("#filterForm");
        $filterForm.reset();            // 初始化
        
        $.ajax({
    		type : "POST",
    		handler : "cls3701m01formhandler",
    		data : {
    			formAction : "getSpecialBank"
    		},
    		success : function(responseData) {
    			if (responseData.isSpecialBank) {
    	            $filterForm.find("#isSpecialBank").show();
    	        }
    	        else {
    	            $filterForm.find("#isSpecialBank").hide();
    	        }
    		}
    	});
        
        $.ajax({
    		type : "POST",
    		handler : "codetypehandler",
    		data : {
    			formAction : "allBranchByUnitType"
    		},
    		success : function(responseData) {
    			var json = {
    				format : "{value} - {key}",
    				item : responseData
    			};
    			$("#caseBrId").setItems(json);

    		}
    	});
        if(true){
        	//第一次開啟box
            //產生下拉選單
            var $div = $("#filterForm").find("[itemType]");
            var allKey = [];
            $div.each(function(){
                allKey.push($(this).attr("itemType"));
            });
            grid.codetypeItem = API.loadCombos(allKey);
            $div.each(function(){
                var $obj = $(this);
                var itemType = $obj.attr("itemType");
                if (itemType) {
                    var format = $obj.attr("itemFormat") || "{value} - {key}";
                    $obj.setItems({
                        space: $obj.attr("space") || true,
                        item: grid.codetypeItem[itemType],
                        format: format,
                        sort: $obj.attr("itemSort") || "asc",
                        size: $obj.attr("itemSize")
                    });
                }
            });
        }
        
        $("#filterForm").find("#filterApplyTS_beg").val(getmonthFirstDate());
		$("#filterForm").find("#filterApplyTS_end").val(CommonAPI.getToday());
		
        $("#filterBox").thickbox({
            // filter=請輸入欲查詢項目：
            title: i18n.cls3701m01["filter"],
            width: 450,
            height: 240,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#filterForm").valid()) {
                        return;
                    }
                    $("#filterForm").find("#filterApplyTS_end").val($("#filterForm").find("#filterApplyTS_end").val()+" 23:59:59")
                    grid.jqGrid("setGridParam", {
                        postData: $.extend({ docStatus: viewstatus,filter: 'Y'}, $filterForm.serializeData() ),
                        search: true
                    }).trigger("reloadGrid");

                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }

    function chose_custId(){
        var my_dfd = $.Deferred();
        AddCustAction.open({
                handler: _handler,
                action : 'echo_custId',
                data : {
                },
                callback : function(json){
                    // 關掉 AddCustAction 的
                    $.thickbox.close();
                    my_dfd.resolve( json );
                }
            });
        return my_dfd.promise();
	}

	function build_submenu(dyna, rdoName, submenu){
    	$.each(submenu, function(k, v) {
    		dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>");
        });
    }
	
	function getmonthFirstDate(){
        var tDate = new Date();
        return tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-01";
    }
});
