<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
			<form id="L170M01JForm">
			<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1700S06Panel');
			</script>
			<!-- ====================================================================== -->
            <table width="100%" class="tb2">
				<tr>
					<td>
						<th:block th:text="#{'L170M01A.needPa'}">是否有須扣分情事</th:block>：
						<input name="needPa" type="radio" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
						<input name="needPa" type="radio" value="N"/><th:block th:text="#{'no'}">否</th:block>
						<fieldset id="paFieldset">
							<legend>
								<th:block th:text="#{'subtitle.61'}">考評項目</th:block>
							</legend>
							<p id="paForm"></p>
							<!--
							<table width='100%' id="paTable">
								<tr>
									<td width='55%'><th:block th:text="#{'L170M01J.paTitle01'}">考評項目</th:block></td>
									<td width='15%'><th:block th:text="#{'L170M01J.paTitle02'}">項次</th:block></td>
									<td width='30%'><th:block th:text="#{'L170M01J.paTitle03'}">備註</th:block></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem01'}"></th:block></td>
									<td>
										<input type="text" id="paItem01_itemName" name="paItem01_itemName" value="paItem01" style="display:none"/>
										<input type="text" id="paItem01_itemType" name="paItem01_itemType" value="YN" style="display:none"/>
										<input id="paItem01_itemYn" name="paItem01_itemYn" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
									</td>
									<td><textarea id="paItem01_dscr" name="paItem01_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem02'}"></th:block></td>
									<td>
										<input type="text" id="paItem02_itemType" name="paItem02_itemType" value="YN" style="display:none"/>
										<input id="paItem02_itemYn" name="paItem02_itemYn" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
									</td>
									<td><textarea id="paItem02_dscr" name="paItem02_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem03'}"></th:block></td>
									<td>
										<input type="text" id="paItem03_itemType" name="paItem03_itemType" value="CNT" style="display:none"/>
										<input type="text" name="paItem03_itemCnt" id="paItem03_itemCnt" class="numeric" positiveonly="false" integer="2" fraction="0" maxlength="2" size="12"/>項
									</td>
									<td><textarea id="paItem03_dscr" name="paItem03_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem04'}"></th:block></td>
									<td>
										<input type="text" id="paItem04_itemType" name="paItem04_itemType" value="CNT" style="display:none"/>
										<input type="text" name="paItem04_itemCnt" id="paItem04_itemCnt" class="numeric" positiveonly="false" integer="2" fraction="0" maxlength="2" size="12"/>項
									</td>
									<td><textarea id="paItem04_dscr" name="paItem04_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem05'}"></th:block></td>
									<td>
										<input type="text" id="paItem05_itemType" name="paItem05_itemType" value="CNT" style="display:none"/>
										<input type="text" name="paItem05_itemCnt" id="paItem05_itemCnt" class="numeric" positiveonly="false" integer="2" fraction="0" maxlength="2" size="12"/>項
									</td>
									<td><textarea id="paItem05_dscr" name="paItem05_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem06'}"></th:block></td>
									<td>
										<input type="text" id="paItem06_itemType" name="paItem06_itemType" value="YN" style="display:none"/>
										<input id="paItem06_itemYn" name="paItem06_itemYn" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
									</td>
									<td><textarea id="paItem06_dscr" name="paItem06_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem07'}"></th:block></td>
									<td>
										<input type="text" id="paItem07_itemType" name="paItem07_itemType" value="YN" style="display:none"/>
										<input id="paItem07_itemYn" name="paItem07_itemYn" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
									</td>
									<td><textarea id="paItem07_dscr" name="paItem07_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
								<tr>
									<td><th:block th:text="#{'L170M01J.paItem08'}"></th:block></td>
									<td>
										<input type="text" id="paItem08_itemType" name="paItem08_itemType" value="YN" style="display:none"/>
										<input id="paItem08_itemYn" name="paItem08_itemYn" type="checkbox" value="Y"/><th:block th:text="#{'yes'}">是</th:block>
									</td>
									<td><textarea id="paItem08_dscr" name="paItem08_dscr" cols="25" rows="2" class="max txt_mult" maxlengthC="100"></textarea></td>
								</tr>
							</table>
							-->
						</fieldset>
					</td>
				</tr>
			</table>
			</form>
        </th:block>
    </body>
</html>
