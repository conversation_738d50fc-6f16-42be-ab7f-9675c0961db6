package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.exception.RateException;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.RateTypeEnum;

/**
 * <pre>
 * 海外分行利率資料
 * </pre>
 * 
 * @since 2012/5/9
 * <AUTHOR>
 * @version <ul>
 *          2012/5/9,<PERSON>,new
 *          </ul>
 */
public class BranchRate {

	private static Logger logger = LoggerFactory.getLogger(BranchRate.class);

	// 默認除法運算精度
	private static final int DEF_DIV_SCALE = 10;

	/** 分行代號 */
	private String brNo = null;
	/** 本位幣幣別 */
	private String mCurr = null;
	/** 分行轉換利率表 */
	private HashMap<String, Rate> rateMap = new HashMap<String, Rate>();

	/** 國內轉換利率表 */
	private HashMap<String, Rate> misRateMap = new HashMap<String, Rate>();

	/**
	 * 分行利率
	 * 
	 * @param brNo
	 *            分行代號
	 */
	public BranchRate(String brNo) {
		this.brNo = brNo;
	}

	/**
	 * 取得本位幣幣別
	 * 
	 * @return
	 */
	public String getMCurr() {
		if (Util.isEmpty(mCurr)) {
			return "TWD";
		}
		return mCurr;
	}

	/**
	 * 設定本位幣幣別
	 * 
	 * @param mCurr
	 *            本位幣幣別
	 */
	public void setMCurr(String mCurr) {
		this.mCurr = mCurr;
	}

	/**
	 * 取得分行代號
	 * 
	 * @return
	 */
	public String getBrNo() {
		return this.brNo;
	}

	/**
	 * 取得分行轉換利率表
	 * 
	 * @return
	 */
	public HashMap<String, Rate> getRateMap() {
		return rateMap;
	}

	/**
	 * 取得分行轉換利率表
	 * 
	 * @return
	 */
	public HashMap<String, Rate> getMisRateMap() {
		return misRateMap;
	}

	/**
	 * 新增利率資料
	 * 
	 * @param rate
	 *            利率
	 */
	public void addRate(Rate rate) {
		rateMap.put(rate.getCurr(), rate);
	}

	/**
	 * 新增利率資料
	 * 
	 * @param rate
	 *            利率
	 */
	public void addMisRate(Rate rate) {
		misRateMap.put(rate.getCurr(), rate);
	}

	/**
	 * 其他幣別金額轉換台幣
	 * 
	 * @param frmCurr
	 *            其他幣別
	 * @param amt
	 *            其他幣別金額
	 * @return 折台幣金額
	 */
	public BigDecimal toTWDAmt(String frmCurr, BigDecimal amt) {
		return toOtherAmt(frmCurr, UtilConstants.CURR.TWD, amt);
	}

	/**
	 * 其他幣別金額轉換為美金
	 * 
	 * @param frmCurr
	 *            其他幣別
	 * @param amt
	 *            其他幣別金額
	 * @return 折美金金額
	 */
	public BigDecimal toUSDAmt(String frmCurr, BigDecimal amt) {
		return toOtherAmt(frmCurr, UtilConstants.CURR.USD, amt);
	}

	/**
	 * 其他幣別 金額轉換為本位幣金額
	 * 
	 * @param curr
	 * @param amt
	 * @return
	 */
	public BigDecimal toLocalAmt(String curr, String amt) {
		if (Util.isNotEmpty(curr)) {
			String temp = Util.trim(amt).replaceAll(",", "");
			if (Util.isNotEmpty(temp))
				return toLocalAmt(curr, new BigDecimal(temp));
		}
		return BigDecimal.ZERO;
	}

	/**
	 * 其他幣別 金額轉換為本位幣金額
	 * 
	 * @param frmCurr
	 *            其他幣別
	 * @param amt
	 *            其他幣別金額
	 * @return 折為本位幣金額
	 */
	public BigDecimal toLocalAmt(String frmCurr, BigDecimal amt) {
		return toOtherAmt(frmCurr, this.getMCurr(), amt);
	}

	/**
	 * 其他幣別轉換為其他幣別金額
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @param dstCurr
	 *            目標幣別
	 * @param frmAmt
	 *            來源金額
	 * @return 目標金額
	 */
	public BigDecimal toOtherAmt(String frmCurr, String dstCurr, String frmAmt) {
		if (!NumConverter.isNumeric(frmAmt)) {
			return BigDecimal.ZERO;
		} else {
			return toOtherAmt(frmCurr, dstCurr, new BigDecimal(frmAmt));
		}
	}

	/**
	 * 其他幣別轉換為其他幣別金額
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @param dstCurr
	 *            目標幣別
	 * @param frmAmt
	 *            來源金額
	 * @return 目標金額
	 */
	public BigDecimal toOtherAmt(String frmCurr, String dstCurr,
			BigDecimal frmAmt) {
		if (Util.isEmpty(frmCurr)) {
			return BigDecimal.ZERO;
		}
		if (Util.isEmpty(dstCurr)) {
			return BigDecimal.ZERO;
		}
		if (frmAmt == null) {
			return BigDecimal.ZERO;
		}
		BigDecimal frmRate = toOtherRate(frmCurr, dstCurr);
		return frmAmt.multiply(frmRate);
	}

	/**
	 * 轉換台幣匯率
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @return
	 */
	public BigDecimal toTWDRate(String frmCurr) {
		return toOtherRate(frmCurr, UtilConstants.CURR.TWD);
	}

	/**
	 * 轉換美金匯率
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @return
	 */
	public BigDecimal toUSDRate(String frmCurr) {
		return toOtherRate(frmCurr, UtilConstants.CURR.USD);
	}

	/**
	 * 轉換本位幣匯率
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @return
	 */
	public BigDecimal toLocalRate(String frmCurr) {
		return toOtherRate(frmCurr, this.getMCurr());
	}

	/**
	 * 取得轉換為其他幣別匯率
	 * 
	 * @param frmCurr
	 *            來源幣別
	 * @param dstCurr
	 *            目標幣別
	 * @return 來源幣別轉換為目標幣別匯率
	 */
	public BigDecimal toOtherRate(String frmCurr, String dstCurr) {

		/*
		 * type 當為本位幣 轉 其他幣別時 M 為除、D 為乘， 其他幣別 轉 本位幣別時 M 為乘、D 為除
		 */

		logger.debug("[匯率轉換開始]================================");
		logger.debug("1. 本位幣:{}", this.mCurr);
		logger.debug("2. 來源幣別:{} , 目標幣別:{}", frmCurr, dstCurr);

		BigDecimal rate = BigDecimal.ONE;
		if (rateMap.containsKey(frmCurr) && rateMap.containsKey(dstCurr)) {
			// 如果幣別相同利率為1
			if (frmCurr.equals(dstCurr)) {
				return rate;
			}
			// Step 1. 轉本位幣
			Rate mRate = rateMap.get(frmCurr);

			logger.debug("3. 來源幣匯率轉本位幣匯率:[{}]", mRate.toString());
			if (RateTypeEnum.M.isEquals(mRate.getType())) {
				rate = rate.multiply(mRate.getRate());
				logger.debug(" (rate) * {} = {}",
						new Object[] { mRate.getRate(), rate });
			} else {
				rate = rate.divide(mRate.getRate(), DEF_DIV_SCALE,
						BigDecimal.ROUND_HALF_UP);
				logger.debug(" (rate) / {} = {}",
						new Object[] { mRate.getRate(), rate });
			}

			// Step 2. 本位幣轉其他幣別
			Rate otRate = rateMap.get(dstCurr);

			logger.debug("4. 本位幣匯率轉目標幣匯率:[{}]", otRate.toString());
			if (RateTypeEnum.M.isEquals(otRate.getType())) {
				rate = rate.divide(otRate.getRate(), DEF_DIV_SCALE,
						BigDecimal.ROUND_HALF_UP);
				logger.debug(" (rate) / {} = {}",
						new Object[] { otRate.getRate(), rate });
			} else {
				rate = rate.multiply(otRate.getRate());
				logger.debug(" rate * {} = {}", new Object[] {
						otRate.getRate(), rate });
			}
		} else {
			// 當海外分行無利率時抓國內分行利率檔
			if (misRateMap.containsKey(frmCurr)
					&& misRateMap.containsKey(dstCurr)) {
				Rate mRate = misRateMap.get(frmCurr);
				// 如果幣別相同利率為1
				if (frmCurr.equals(dstCurr)) {
					return rate;
				}
				// Step 1. 其他幣別轉TWD
				rate = mRate.getRate();
				logger.debug(" (rate) * {} = {}",
						new Object[] { mRate.getRate(), rate });

				// Step 2. TWD轉其他幣別
				Rate otRate = misRateMap.get(dstCurr);
				rate = rate.divide(otRate.getRate(), DEF_DIV_SCALE,
						BigDecimal.ROUND_HALF_UP);
				logger.debug(" (rate) / {} = {}",
						new Object[] { otRate.getRate(), rate });
			} else {
				logger.error(
						"幣別轉換發生錯誤-無該幣別轉換匯率: {}[{}], {}[{}]",
						new Object[] { Util.truncateString(frmCurr, 3),
								misRateMap.get(frmCurr),
								Util.truncateString(dstCurr, 3),
								misRateMap.get(dstCurr) });
				// Exception 處理
				StringBuffer sb = new StringBuffer();
				if (!misRateMap.containsKey(frmCurr)) {
					sb.append(frmCurr).append(",");
				}
				if (!misRateMap.containsKey(dstCurr)) {
					sb.append(dstCurr).append(",");
				}
				throw new RateException("幣別轉換發生錯誤-無該幣別轉換匯率", sb.toString()
						.replaceAll(",$", ""));
			}

		}

		logger.debug(" **** 結果匯率[{}]", rate);
		logger.debug("[匯率轉換結束]================================");
		return rate;
	}
}
