var init120s11a = {
	fhandle : "lms1205formhandler",
	ghandle : "lms1205gridhandler",
	fhandle140 : "lms1405m01formhandler",
	ghandle140 : "lms1405gridhandler",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var initDfd = initDfd || $.Deferred();
initDfd.done(function(auth) {

	// ===================Grid Code=============================
	/** 主表,與當地往來grid 共用欄位設定 */
	var gridJson = {
			handler : init120s11a.ghandle,
	        height: 170,
	        needPager: false,
	        rownumbers: false,
	        multiselect: true,
	        hideMultiselect: false,
	        sortname: 'printSeq',
	        sortorder: 'asc',
	        shrinkToFit : true,
	        postData: {
				formAction: "queryL120s11aMainCust",
	            mainId : responseJSON.mainId
	        },
	        autowidth: false,
	        colModel : [{
				colHeader : i18n.lms1405s06["L120S11A.custId"], //申貸戶統一編號
				align : "left",
				width : 80, //設定寬度
				sortable : true, //是否允許排序
				formatter : 'click',
				onclick : L120s11aAPI.opendocBox,
				name : 'custId' //col.id
			},{
				colHeader : i18n.lms1405s06["L120S11A.dupNo"], //申貸戶重覆序號
				align : "center",
				width : 40, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dupNo' //col.id
			},{
				colHeader : i18n.lms1405s06["L120S11A.custName"], //申貸戶客戶名稱
				align : "left",
				width : 170, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName' //col.id
			},{
				colHeader : i18n.lms1405s06["L120S11A.factAmt2"]+"<br>(TWD)", //授信
				width : 90, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
				formatter: 'currency',
	            formatoptions: {
	                thousandsSeparator: ",",
					removeTrailingZero: true,
	                decimalPlaces: 0//小數點到第幾位
	            },
				name : 'factAmt' //col.id		
			},{
				colHeader : i18n.lms1405s06["L120S11A.imAmt2"]+"<br>(TWD)", //進口
				width : 90, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
				formatter: 'currency',
	            formatoptions: {
	                thousandsSeparator: ",",
					removeTrailingZero: true,
	                decimalPlaces: 0//小數點到第幾位
	            },
				name : 'imAmt' //col.id			
			},{
				colHeader : i18n.lms1405s06["L120S11A.exAmt2"]+"<br>(USD)", //出口
				width : 90, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
				formatter: 'currency',
	            formatoptions: {
	                thousandsSeparator: ",",
					removeTrailingZero: true,
	                decimalPlaces: 0//小數點到第幾位
	            },
				name : 'exAmt' //col.id			
			},{
				colHeader : i18n.lms1405s06["L120S11A.arSellerAmt2"]+"<br>(TWD)", //應收帳款
				width : 90, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
				formatter: 'currency',
	            formatoptions: {
	                thousandsSeparator: ",",
					removeTrailingZero: true,
	                decimalPlaces: 0//小數點到第幾位
	            },
				name : 'arSellerAmt' //col.id	
			},{
				colHeader : i18n.lms1405s06["L120S11A.submitAmt2"]+"<br>(USD)", //衍生性
				width : 90, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
				formatter: 'currency',
	            formatoptions: {
	                thousandsSeparator: ",",
					removeTrailingZero: true,
	                decimalPlaces: 0//小數點到第幾位
	            },
				name : 'submitAmt' //col.id			
			},{
				colHeader : i18n.lms1405s06["L120S11A.dataDate"], //資料日期
				align : "left",
				width : 80, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				name : 'dataDate' //col.id		
			},{
				colHeader : "printSeq", //列印順序
				align : "right",
				width : 5, //設定寬度
				hidden : true, //是否隱藏
				name : 'printSeq'  	
			}],
	        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
	        	var data = $(L120s11aAPI.mainGridId).getRowData(rowid);
	        	L120s11aAPI.opendocBox(null, null, data);
	        }
		}
	
	/** 主表grid */
	$("#gridviewRelatedCompany").iGrid(gridJson);

	/** J-110-0325_11557_B1001 增加合併關係企業與當地有往來資料(L120S11A_LOC) */
	/** 共用的json設定要做微調 */
	gridJson.postData.formAction = "queryL120s11aLocMainCust";
	gridJson.colModel[0].onclick = L120s11aAPI.opendocLocBox;
	gridJson.ondblClickRow = function(rowid){
		var data = $(L120s11aAPI.mainGridId).getRowData(rowid);
		L120s11aAPI.opendocLocBox(null, null, data);
	}
	
	/** 與當地往來grid */
	$("#gridviewRelatedCompanyLoc").iGrid(gridJson);
	
	L120s11aAPI.gridviewSubRelatedCompany();
	L120s11aAPI.gridviewSubRelatedCompanyLoc();

	// button
	$("#applyRelatedCompany").click(function() {
		L120s11aAPI.applyRelatedCompany();
	});
	 

});

// 額度明細表內程式
var L120s11aAPI = {

	mainGridId : "#gridviewRelatedCompany",
	locGridId : "#gridviewRelatedCompanyLoc",
	/**
	 * 觸發主檔Grid更新
	 */
	_triggerMainGrid : function() {
		$(L120s11aAPI.mainGridId).trigger('reloadGrid');
		$(L120s11aAPI.locGridId).trigger('reloadGrid');
	},
	/**
	 * 引進
	 */
	applyRelatedCompany : function() {
		
		var count=$(L120s11aAPI.mainGridId).jqGrid('getGridParam','records');
    	if(count > 0){
    		//L120S11A.message001=執行引進後會刪除已存在之名單，是否確定執行？
    		CommonAPI.confirmMessage(i18n.lms1405s06["L120S11A.message001"], function(b){
				if (b) {					
					//是的function
					$.ajax({
		    			handler : init120s11a.fhandle,
		    			type : "POST",
		    			dataType : "json",
		    			action : "applyRelatedCompany",
		    			data : {
		    				mainId : responseJSON.mainId }
		    			}).done(function(obj) {
		    				
		    				$(L120s11aAPI.mainGridId).trigger("reloadGrid");
		    				$(L120s11aAPI.locGridId).trigger('reloadGrid');
		    				 
		    		});	
				}				
			});		
    	}else{
    		$.ajax({
    			handler : init120s11a.fhandle,
    			type : "POST",
    			dataType : "json",
    			action : "applyRelatedCompany",
    			data : {
    				mainId : responseJSON.mainId }
    			}).done(function(obj) {
    				
    				$(L120s11aAPI.mainGridId).trigger("reloadGrid");
    				$(L120s11aAPI.locGridId).trigger('reloadGrid');
    				 
    		});	
    	}
		
	},
	/** 子表共用的json定義 */
	subGridJson : {
    	handler: init120s11a.ghandle,
        height: 300,
        needPager: false,
        rownumbers: false,
        multiselect: true,
        hideMultiselect: false,
        sortname: 'itemSeq',
        sortorder: 'asc',
        shrinkToFit : false,
        localFirst: true,
        autowidth: true,
        colModel : [{
			colHeader : i18n.lms1405s06["L120S11A.itemSeq"], //序號
			align : "center",
			width : 50, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'itemSeq' //col.id
		},{
			colHeader : i18n.lms1405s06["L120S11A.custId22"], //關係企業統一編號
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'custId2' //col.id
		},{
			colHeader : i18n.lms1405s06["L120S11A.dupNo22"], //重覆序號
			align : "center",
			width : 40, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'dupNo2' //col.id
		},{
			colHeader : i18n.lms1405s06["L120S11A.custName22"], //關係企業名稱
			align : "left",
			width : 180, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName2' //col.id
		},{
			colHeader : i18n.lms1405s06["L120S11A.relType2"], //關係
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'relTypeStr' //col.id		
		},{
			colHeader : i18n.lms1405s06["L120S11A.factAmt"]+"<br>(TWD)", //授信
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'factAmt' //col.id
		},{
			colHeader : i18n.lms1405s06["L120S11A.factAmtS"]+"<br>(TWD)", //其中擔保
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
				defaultValue: "",
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'factAmtS' //col.id		
		},{
			colHeader : i18n.lms1405s06["L120S11A.imAmt"]+"<br>(TWD)", //進口
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'imAmt' //col.id			
		},{
			colHeader : i18n.lms1405s06["L120S11A.exExperfYAmt"]+"<br>(USD)", //出口符合
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'exExperfYAmt' //col.id			
		},{
			colHeader : i18n.lms1405s06["L120S11A.exExperfNAmt"]+"<br>(USD)", //出口不符合
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'exExperfNAmt' //col.id			
		},{
			colHeader : i18n.lms1405s06["L120S11A.exFlawAmt"]+"<br>(USD)", //出口瑕疵
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'exFlawAmt' //col.id					
		},{
			colHeader : i18n.lms1405s06["L120S11A.arSellerAmt"]+"<br>(TWD)", //應收帳款
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'arSellerAmt' //col.id	
		},{
			colHeader : i18n.lms1405s06["L120S11A.submitAmt"]+"<br>(USD)", //衍生性
			width : 90, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
			formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
				removeTrailingZero: true,
                decimalPlaces: 0//小數點到第幾位
            },
			name : 'submitAmt' //col.id			
		},{
			colHeader : i18n.lms1405s06["L120S11A.dataDate"], //資料日期
			align : "left",
			width : 10, //設定寬度
			//sortable : true, //是否允許排序
			//formatter : 'click',
			hidden : true, //是否隱藏
			name : 'dataDate' //col.id			
		}],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridviewSubRelatedCompany").getRowData(rowid);
            opendocBox(null, null, data);
        },
        loadComplete: function(){
        		        	  	
        }    
    },
	/**
	 * 引進
	 */
	gridviewSubRelatedCompany : function() {
		$("#gridviewSubRelatedCompany").iGrid($.extend({
			postData: {
				formAction: "queryL120s11aMainCust",
	            mainId : responseJSON.mainId
	        	}
			}, L120s11aAPI.subGridJson));
	},
	/**
	 * 與當地往來子表
	 */
	gridviewSubRelatedCompanyLoc : function() {
		$("#gridviewSubRelatedCompanyLoc").iGrid($.extend({
			postData: {
				formAction: "queryL120s11aLocMainCust",
	            mainId : responseJSON.mainId
	        	}
			}, L120s11aAPI.subGridJson));
	},
	/** 主檔 */
	opendocBox : function(type, docOid, data) {
		var buttons = {};
		
		var custId = data.custId;
		var dupNo = data.dupNo;
		var custName = data.custName;
		
		
		$("#gridviewSubRelatedCompany").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
			postData : {
				formAction: "queryL120s11aByCustId",
                mainId : responseJSON.mainId,
                custId : custId,
                dupNo : dupNo
			},
			search : true
		}).trigger("reloadGrid");

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		
		$("#detailL120s11aBox").thickbox({
			// L120S11A.title01=借款人合併關係企業額度彙總表
			title : custId+dupNo+" "+custName,
			width : 990,
			height : 450,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
		

	},
	/** 主檔(與當地有往來) */
	opendocLocBox : function(type, docOid, data) {

		var buttons = {};
		
		var custId = data.custId;
		var dupNo = data.dupNo;
		var custName = data.custName;
		
		
		$("#gridviewSubRelatedCompanyLoc").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
			postData : {
				formAction: "queryL120s11aLocByCustId",
                mainId : responseJSON.mainId,
                custId : custId,
                dupNo : dupNo
			},
			search : true
		}).trigger("reloadGrid");

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

		
		$("#detailL120s11aLocBox").thickbox({
			// L120S11A.title01=借款人合併關係企業額度彙總表
			title : custId+dupNo+" "+custName,
			width : 990,
			height : 450,
			modal : true,
			readOnly : thickboxOptions.readOnly,
			align : "center",
			i18n : i18n.def,
			valign : "bottom",
			buttons : buttons
		});
		

	}

};