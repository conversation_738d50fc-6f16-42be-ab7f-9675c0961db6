package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * 消金模擬動審已覆核
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/cls/cls1161v10")
public class CLS1161V10Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters parameters) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		renderJsI18N(CLS1161V08Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1161V10Page');");
	}

}
