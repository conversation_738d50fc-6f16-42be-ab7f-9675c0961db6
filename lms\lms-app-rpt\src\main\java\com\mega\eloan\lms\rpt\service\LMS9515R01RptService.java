package com.mega.eloan.lms.rpt.service;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.AbstractService;

public interface LMS9515R01RptService extends AbstractService {

	/**
	 * <pre>
	 * 產生PDF檔
	 * 1. 授信契約已逾期控制表
	 * 
	 * @param dataCollection
	 * @param i
	 * @param listName 
	 * @param ovUnitNo 
	 * @param ctype 
	 * @param caseDept 
	 * @param dataDate 
	 * @return
	 * </pre>
	 * @throws CapException 
	 * @throws IOException 
	 */
	OutputStream generateReport(List<?> list, int action, String listName,
			String ovUnitNo, Date dateStartDate,Date dateEndDate, String caseDept) throws CapException, IOException;

	/**
	 * 查詢LMS.BDOC是否有此筆資料
	 * 
	 * @param mainId
	 * @param FieldId
	 * @return
	 */
	List<DocFile> findDocFile(String mainId, String FieldId);

}
