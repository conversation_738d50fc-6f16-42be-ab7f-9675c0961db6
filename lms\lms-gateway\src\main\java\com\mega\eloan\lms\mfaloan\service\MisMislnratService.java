/* 
 *MisMislnratService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

/**
 * <pre>
 * MIS.MISLNRAT(基準利率檔)
 * </pre>
 * 
 * @since 2012/10/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/26,REX,new
 *          </ul>
 */
public interface MisMislnratService {
	/**
	 * 查詢利率基礎 代碼所屬利率
	 * 
	 * @param type
	 *            利率代碼
	 * @param curr
	 *            幣別
	 * @return
	 */
	public List<Map<String, Object>> findMislnratByLRRate(String type,
			String curr);

	/**
	 * 抓取利率基礎 選單
	 * 
	 * @param currs
	 *            需要抓取的幣別陣列
	 * @return 利率基礎選項 <br/>
	 *         <幣別,<代碼,顯示名稱>>
	 */
	public HashMap<String, LinkedHashMap<String, String>> findBaseRateByCurrs(
			String[] currs);

	/**
	 * 查詢利率基礎 代碼所屬利率 多筆
	 * 
	 * @param curr
	 *            幣別
	 * @param codes
	 *            利率代碼 M1,M2,.........
	 * @return <pre>
	 * { 
	 * 	 "M1":0.3313, 
	 *   "M2":0.44443, .........
	 *  }
	 * </pre>
	 * 
	 */
	public List<Map<String, Object>> findByCurrAndCodes(String curr,
			String... codes);

	/**
	 * 查詢利率基礎 代碼所屬利率 全部
	 * 
	 * @param curr
	 *            幣別
	 * @return
	 */
	public List<Map<String, Object>> findByCurr(String curr);

	/**
	 * 額度明細表 [結構化利率]依照幣別查詢基準利率
	 * 
	 * @param curr
	 * @return
	 */
	List<Map<String, Object>> findAllBaseRateByCurr(String curr);

	public List<Map<String, Object>> findMislnratAllRateCode();

	public Map<String, Object> findLrCodeByCurr(String curr, String rateCode);

	public List<Map<String, Object>> findLrCodeBySpecificCurrency(String curr,
			Map<String, String> currencyMap);

	public JSONObject getSysParamJsonObject(String param);

	public List<Map<String, Object>> findAllLrCodeBySpecificCurrencies(Map<String, String> currencyMap);
}
