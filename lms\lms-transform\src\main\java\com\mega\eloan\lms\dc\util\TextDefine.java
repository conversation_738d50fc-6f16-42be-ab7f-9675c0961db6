package com.mega.eloan.lms.dc.util;

/**
 * 常用字串及符號定義
 * 
 * <AUTHOR>
 */
public class TextDefine {

	// ------------------------------------------------------------------------------------------
	// 符號相關
	// ------------------------------------------------------------------------------------------
	/** 空字串 */
	public static final String EMPTY_STRING = "";
	/** 空一格(半形) */
	public static final String SPACE = " ";
	/** 間隔符號--分號 ";" (半形) */
	public static final String SYMBOL_SEMICOLON = ";";
	/** 間隔符號--冒號 ":" (半形) */
	public static final String SYMBOL_COLON = ":";
	/** 間隔符號--破折號 "-" (半形) */
	public static final String SYMBOL_DASH = "-";

	// ------------------------------------------------------------------------------------------
	// 編碼相關
	// ------------------------------------------------------------------------------------------
	/** UTF8編碼 @TODO: UTF-8國際標準寫法,只有在MySQL中才需要變更為utf8 */
	public static final String ENCODING_UTF8 = "UTF-8";
	/** MS950編碼 */
	public static final String ENCODING_MS950 = "MS950";

	// ------------------------------------------------------------------------------------------
	// 附加檔名相關
	// ------------------------------------------------------------------------------------------
	/** 附加檔名為 ".dxl" */
	public static final String ATTACH_DXL = ".dxl";
	/** 附加檔名為 ".txt" */
	public static final String ATTACH_TXT = ".txt";
	/** 附加檔名為 ".log" */
	public static final String ATTACH_LOG = ".log";
	/** 檔案類型為 "gif" */
	public static final String ATTACH_GIF = "gif";

	// ------------------------------------------------------------------------------------------
	// Main Function 參數設定相關
	// ------------------------------------------------------------------------------------------
	/** 檢核的文字檔路徑辨識碼:來源資料夾為load_db2 */
	public static final String MAIN_PATH_DB2 = "db2";

	// ------------------------------------------------------------------------------------------
	// 資料庫相關
	// ------------------------------------------------------------------------------------------
	/** 企金系統名稱 */
	public static final String SCHEMA_LMS = "LMS";
	/** 個金系統名稱 */
	public static final String SCHEMA_CLS = "CLS";

	/** SQL檔的分隔符號 */
	public static final String FILE_DELIM = ";";

	public static final String RICHTEXT = "richtext";

	// ------------------------------------------------------------------------------------------
	// 每一步驟log命名開頭
	// ------------------------------------------------------------------------------------------
	/** EXPORT */
	public static final String LOG_EXPORT = "dxlexport_";
	/** PARSER */
	public static final String LOG_PARSER = "parser_";

	// ------------------------------------------------------------------------------------------
	// 企金獨立轉檔相關
	// ------------------------------------------------------------------------------------------
	/** 企金獨立轉檔的ViewName: VLMS14020 */
	public static final String LMS_PARSER14020 = "VLMS14020";
	// modified by Sandra註解掉，以config檔或單筆傳入的為主
	// public static final String LMS_PARSERDB201B = "VLMSDB201B";

	// ------------------------------------------------------------------------------------------
	// 企金Table獨立轉檔對應的Form相關
	// ------------------------------------------------------------------------------------------
	/** L140M01B相關的form */
	public static final String L140M01B_FORM = "FLMS140M01";
	/** L140M01C相關的form */
	public static final String L140M01C_FORM = "FLMS140M01";
	/** L140M01C_BF相關的form */
	public static final String L140M01C_BF_FORM = "FLMS140M01";
	/** L140M01D相關的form */
	public static final String L140M01D_FORM = "FLMS140M01";
	/** L140M01D_BF相關的form */
	public static final String L140M01D_BF_FORM = "FLMS140M01";
	/** L140M01F相關的form */
	public static final String L140M01F_FORM = "FLMS140M01";
	/** L140M01I相關的form */
	public static final String L140M01I_FORM = "FLMS140M01";
	/** L120M01F相關的form */
	public static final String L120M01F_FORM = "FLMS110M01";
	/** L140M01K相關的form */
	public static final String L140M01K_FORM = "FLMS140M01";

	// ------------------------------------------------------------------------------------------

	// 個金獨立轉檔相關
	// ------------------------------------------------------------------------------------------
	/** 個金獨立轉檔的ViewName: VCLS10130 */
	public static final String CLS_PARSER10130 = "VCLS10130";
	/** 個金獨立轉檔的ViewName: VCLS10130 */
	public static final String CLS_PARSER10105Z = "VCLS10105";

	// ------------------------------------------------------------------------------------------
	// 個金Table獨立轉檔對應的Form相關
	// ------------------------------------------------------------------------------------------
	/** L140M01B相關的form */
	public static final String CLS_L140M01B_FORM_11501 = "FCLS115M01";
	/** L140M01B相關的form */
	public static final String CLS_L140M01B_FORM_71501 = "FCLS715M01";

	/** L140S02B相關的form */
	public static final String CLS_L140S02B_FORM_109M01 = "FCLS109M01";
	/** L140S02B相關的form */
	public static final String CLS_L140S02B_FORM_109M02 = "FCLS109M02";
	/** L140S02B相關的form */
	public static final String CLS_L140S02B_FORM_109M03 = "FCLS109M03";

	/** L120M01F相關的form */
	public static final String CLS_L120M01F_FORM = "FCLS106M01;FCLS106M02;FCLS107M01;FCLS107M02;FCLS108M01;FCLS108M02;FCLS109M01;FCLS109M02;FCLS109M03;FCLS110M01;FCLS110M02;FCLS111M01;FCLS111M02;FCLS113M01;FCLS113M02;FCLS118M01;FCLS118M02;FCLS119M01;FCLS119M02;FCLS114M01;FCLS114M02";

}
