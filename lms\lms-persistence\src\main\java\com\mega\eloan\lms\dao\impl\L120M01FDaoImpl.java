/* 
 * L120M01FDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120M01F;

/** 案件簽章欄檔 **/
@Repository
public class L120M01FDaoImpl extends LMSJpaDao<L120M01F, String> implements
		L120M01FDao {

	@Override
	public L120M01F findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01F> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq");
		search.addOrderBy("oid");
		List<L120M01F> list = null;
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01F.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01F> findToSaveHq(String mainId, String branchType,
			String branchId, String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		List<L120M01F> list = null;
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01F.class, search).getResultList();
		}
		return list;
	}

	@Override
	public L120M01F findByUniqueKey(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01F> findByIndex01(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		ISearch search = createSearchTemplete();
		List<L120M01F> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		if (branchId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		}

		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo",
					staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob",
					staffJob);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01F.class, search).getResultList();
		}
		return list;
	}

	@Override
	public L120M01F findByMainIdAndKey(String mainId, String branchType,
			String branchId, String staffJob) {
		ISearch search = createSearchTemplete();
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}

		if (branchType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		}

		if (branchId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		}

		if (staffJob != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob",
					staffJob);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01F> findByMainIdAndKey(String mainId, String branchType,
			String branchId, String[] staffJob) {
		ISearch search = createSearchTemplete();
		List<L120M01F> list = null;
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}

		if (branchType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		}

		if (branchId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		}

		if (staffJob != null) {
			search.addSearchModeParameters(SearchMode.IN, "staffJob", staffJob);
		}
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01F.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01F> findByMainIdAndBranchType(String mainId,
			String branchType) {
		ISearch search = createSearchTemplete();
		List<L120M01F> list = null;
		if (mainId != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		}

		if (branchType != null) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		}
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L120M01F.class, search).getResultList();
		}
		return list;
	}
}