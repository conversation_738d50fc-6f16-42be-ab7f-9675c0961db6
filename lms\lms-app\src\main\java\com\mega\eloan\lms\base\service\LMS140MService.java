package com.mega.eloan.lms.base.service;

/* 
 * LMS140MService.java
 * 
 * Copyright (c) 2011-2013 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140MM1A;
import com.mega.eloan.lms.model.L140MM1B;

/**
 * <pre>
 * 央行註記異動作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
public interface LMS140MService extends AbstractService {

	/**
	 * 刪除動審表主檔資料 根據所選的oid
	 * 
	 * @param oids
	 *            文件編號陣列
	 * @return boolean
	 */
	boolean deleteL140mm1as(String[] oids);


	/**
	 * 刪除上傳檔案
	 * 
	 * @param oids
	 *            文件編號
	 */
	//void deleteUploadFile(String[] oids);

	/**
	 * 儲存C102M01B．案件簽章欄檔
	 * 
	 * @param list
	 *            List<L160M01D>
	 */
	public void saveL140mm1bList(List<L140MM1B> list);

	/**
	 * 查詢 L160M01D．案件簽章欄檔
	 * 
	 * @param mainId
	 *            案件編號
	 * 
	 * @param staffNo
	 *            員編
	 * @param staffjob
	 *            人員職稱
	 */
	public L140MM1B findL140mm1b(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	/**
	 * 其它到結案所用的flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            資料表
	 * @param setResult
	 *            boolean
	 * @param resultType
	 *            boolean
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, L140MM1A model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable;

	public void deleteL140mm1bs(List<L140MM1B> l140mm1bs, boolean isAll);

	/**
	 * 產生央行房貸註記維護
	 * @param params
	 * @throws CapException
	 */
	public void addL140M01M(L140MM1A l140mm1a, String selectVersion) throws CapException;


	public String[] l140m01m_location(String str_city, String str_area,
			BigDecimal str_site3, String str_site4);
	
	public void setUnsoldHouseFinancingDataForCentralBankMarkMaintenance(Map<String, String> elf517Map, L140M01M l140m01m);
	
	public String checkUnsoldHouseFinancingDataForCentralBankMarkMaintenance(L140M01M l140m01m);

	public void addL140M01MForBrandNewCntrNo(L140MM1A l140mm1a, String selectVersion) throws CapException;

	public String checkContractNoIsCorrect(String cntrNo);
}