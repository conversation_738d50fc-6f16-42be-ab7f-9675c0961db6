<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="innerPageBody">
    		<script type="text/javascript">
				var defaultValue = null;
				require(['[[@{/pagejs/lns/LMS1401S02Panel.js}]]'], function() {
	    			loadScript('[[@{/pagejs/lns/LMS1401M01Page.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel02.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel03.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel13.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel04.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel05.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel06.js}]]');
					loadScript('[[@{/pagejs/lns/LMS1401S02Panel07.js}]]');
				});
    		</script>
			<div id="buttonPanel" class="button-menu funcContainer"></div>
			<div id="opendocBox">
				<div class="tit2 color-black">
					<th:block th:text="#{'L140M01a.title'}"><!--額度明細表--></th:block>：
					<span id="showCustId" class="color-blue"></span>
				</div>
				<div id="loadPanel"></div>
			</div>
			<input type="hidden" id="mainId" name="mainId"></input>
			<input type="hidden" id="srcMainId" name="srcMainId"></input>
			<div id="tab-1b" th:insert="~{base/panels/LMSL140M01MPanel :: panelFragmentBody}"></div>
			<div id="l140m01eAmtBox" style="display:none;">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td><span id="l140m01eAmtMsg"></span><br></td>
					</tr>
					<tr>
						<td><div id="l140m01eAmtGrid"></div></td>
					</tr>
				</table>
			</div><!-- 特殊登錄案件 -->
			<div id="special" class="content" style="display:none;">
				<form id="L782M01AForm" name="L782M01AForm">
					<fieldset>
						<legend>
							<strong><th:block th:text="#{'doc.baseInfo'}"><!--基本資訊--></th:block></strong>
						</legend>
						<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td width="20%" class="hd1">
										<th:block th:text="#{'doc.branchName'}"><!--分行名稱--></th:block>&nbsp;&nbsp;
									</td>
									<td width="30%">
										<input id="branchId" name="branchId" style="display:none"></input>
										<span id="branchName"></span>
									</td>
									<td width="20%" class="hd1">
										<th:block th:text="#{'L782M01A.dispatchDate'}"><!--發文日期--></th:block>&nbsp;&nbsp;
									</td>
									<td width="30%">
										<input type='text' id='dispatchDate' name='dispatchDate' class='date required' size='8'></input>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.custName'}"><!--客戶名稱--></th:block>&nbsp;&nbsp;
									</td>
									<td>
										<span id="specialCustId"></span>
										<th:block th:text="#{'L782M01A.dupNo'}"><!--重覆序號--></th:block>：
										<span id="specialDupNo"></span><br>(<span class="color-red">DBU</span>)<span id="specialCustName"></span>
									</td>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.loanTP'}"><!--科目--></th:block>&nbsp;&nbsp;
									</td>
									<td valign="middle">
										<select id="SplieloanTP" name="SplieloanTP" class="required"></select>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.applyAmt'}"><!--額度--></th:block>&nbsp;&nbsp;
									</td>
									<td width="40%">
										<select id="applyCurr" name="applyCurr" class="money required"></select>
										<!--幣別-->
										<input type='text' id='applyAmt' name='applyAmt' size="18" maxlength="22" integer="13" fraction="2" class="numeric required"></input>
									</td>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.caseType'}"><!--歸類--></th:block>&nbsp;&nbsp;
									</td>
									<td>
										<!--<select id="caseType" name="caseType" combokey="lms1405m01_SpecialCaseType" />-->
										<select id="caseType" name="caseType" class="required"></select>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.inteRate'}"><!--利費率/其他--></th:block>&nbsp;&nbsp;
									</td>
									<td colspan="3">
										<textarea id="inteRate" name="inteRate" cols="60" maxlength="900" maxlengthC="300"></textarea>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L782M01A.disp1'}"><!--備註說明--></th:block>&nbsp;&nbsp;
									</td>
									<td colspan="3">
										<textarea id="disp1" name="disp1" cols="60" maxlength="900" maxlengthC="300"></textarea>
									</td>
								</tr>
								<tr>
									<td class="hd1">
										<th:block th:text="#{'L140M01a.creator'}"><!--建立人員--></th:block>&nbsp;&nbsp;
									</td>
									<td>
										<span id="SplieCreator"></span>
									</td>
									<td class="hd1">
										<th:block th:text="#{'L140M01a.updater'}"><!--最後異動者--></th:block>&nbsp;&nbsp;
									</td>
									<td>
										<span id="SplieUpdater"></span>
									</td>
								</tr>
							</tbody>
						</table>
					</fieldset>
				</form>
			</div>
    	</th:block>
    </body>
</html>