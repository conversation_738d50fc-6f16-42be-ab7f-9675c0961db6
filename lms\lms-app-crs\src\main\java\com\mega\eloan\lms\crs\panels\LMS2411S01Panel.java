package com.mega.eloan.lms.crs.panels;

import java.util.HashSet;

import org.springframework.ui.ModelMap;

import com.mega.eloan.common.panels.Panel;

import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.model.C241M01A;

public class LMS2411S01Panel extends Panel {
	
	private static final long serialVersionUID = 1L;
	
	private C241M01A meta;

	public LMS2411S01Panel(String id, C241M01A meta) {
		super(id);
		this.meta = meta;
	}
	
	public LMS2411S01Panel(String id, C241M01A meta, boolean updatePanelName) {
		super(id, updatePanelName);
		this.meta = meta;
	}
	
	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		new DocLogPanel("_docLog").processPanelData(model, params);
		HashSet<String> allowSet = new HashSet<String>();
		allowSet.add("");
		allowSet.add(RetrialDocStatusEnum.區中心_編製中.getCode());
		model.addAttribute("show_reQueryCustName", allowSet.contains( Util.trim(meta.getDocStatus())));		
	}
}
