$(document).ready(function() {

	var grid = $("#result").iGrid({
		height : 380,
		width : "100%",
		autowidth : true,
		handler : "lms1925gridhandler",
		action : "queryL192S01A",
		rownumbers : true,
		colModel : [ {
			colHeader : i18n.lms1925m01['lms1925s03.003'],// "科目",
			name : "subject",
			align : "center",
			formatter : 'click',
			onclick : openDoc
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.004'],// "帳號",
			name : "accNo",
			align : "center"
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.020'],// "額度序號",
			name : "quotaNo",
			align : "center"
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.005'],// "幣別",
			name : "quotaCurr",
			align : "center"
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.006'],// "額度",
			name : "quotaAmt",
			align : "center",
			formatter:'currency',
			formatoptions:{decimalSeparator:".", thousandsSeparator: ",", decimalPlaces: 2}
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.007'],// "幣別",
			name : "balCurr",
			align : "center"
		}, {
			colHeader : i18n.lms1925m01['lms1925s03.008'],// "餘額",
			name : "balAmt",
			align : "center",
			formatter:'currency',
			formatoptions:{decimalSeparator:".", thousandsSeparator: ",", decimalPlaces: 2}
		}, {
			name : "balDate",
			hidden : "true"
		}, {
			name : "oid",
			hidden : "true"
		}, {
			name : "mainId",
			hidden : "true"
		} ],		
        ondblClickRow: function(rowid){
            var data = grid.getRowData(rowid);
            openDoc(null, null, data);
        },
		gridComplete : function() {
			var ret = grid.getRowData(1);
			$("#bal_date").text(ret.balDate);
		}
	});

	function openDoc(cellvalue, options, rowObject) {
		//alert(rowObject.oid);
		
		$("#loanDatas").thickbox({
			title : i18n.lms1925m01['lms1925s03.009'],//申請內容
			width : 640,
			height : 500,
			align : 'left',
			valign : 'top',
			open : function() {				
				$.ajax({
					handler : "lms1925m01formhandler",
					data : {
						l192s01aoid : rowObject.oid,
						formAction : "getL192S01A"
					},
					success : function(responseData) {
						$('#loanData').injectData(responseData);
					}
				});
			},
			close : function(){
				$('#loanData .text-only').val("");
			},
			buttons : API.createJSON([ {
				key : i18n.def['sure'],
				value : function() {
					$.thickbox.close();
				}
			} ])
		});
	}

});