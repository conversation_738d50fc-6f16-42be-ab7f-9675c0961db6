package com.mega.eloan.lms.dc.action;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * CopyAllTextFile : Copy & Merge All Text Files to a File
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013/03/07,UFO,取消local_var: dataBean，父類別取得
 *          </ul>
 */
public class CopyAllTextFile extends BaseAction {
	private String schema = "";
	private String dxlDirRootPath = "";
	private String logsDirPath = "";
	private String loadDB2DataPath = "";// load_db2 下data目錄所在位置路徑
	private String loadDB2ClobPath = "";// load_db2 下clob 目錄所在位置路徑
	private String loadDB2FilesPath = "";// load_db2 下FILES 目錄所在位置路徑
	private PrintWriter logsCw = null;// 輸出log

	/**
	 * 初始化必要資訊及執行集中檔案,移動clob檔,處理附加檔案,圖檔等動作
	 * 
	 * @param schema
	 *            String:目前執行的系統名稱
	 */
	public void doCopyAllText(String schema) {
		if (StringUtils.isBlank(schema)) {
			String errmsg = "讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認!";
			this.logger.error(errmsg);
			throw new DCException(errmsg);
		}
		this.schema = schema;
		this.logger.info("正在初始化 CopyAllTextFile 必要資訊...");

		if (TextDefine.SCHEMA_LMS.equalsIgnoreCase(schema)) {
			this.logsDirPath = this.configData.getLmsLogsDirPath();// User當前工作目錄\log\logs\執行日期\LMS
			this.dxlDirRootPath = this.configData.getLmsDxlDirRootPath();// homePath\today\LMS
			// User當前工作目錄\load_db2\執行日期\LMS\data
			this.loadDB2DataPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getDataPath();
			// User當前工作目錄\load_db2\執行日期\LMS\clob
			this.loadDB2ClobPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getClobPath();
			// User當前工作目錄\load_db2\執行日期\LMS\FILES
			this.loadDB2FilesPath = this.configData.getLmsloadDB2DirPath()
					+ this.configData.getFilesPath();
		} else {
			this.logsDirPath = this.configData.getClsLogsDirPath();// User當前工作目錄\log\logs\執行日期\CLS
			this.dxlDirRootPath = this.configData.getClsDxlDirRootPath();// homePath\today\CLS
			// User當前工作目錄\load_db2\執行日期\CLS\data
			this.loadDB2DataPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getDataPath();
			// User當前工作目錄\load_db2\執行日期\CLS\clob
			this.loadDB2ClobPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getClobPath();
			// User當前工作目錄\load_db2\執行日期\CLS\FILES
			this.loadDB2FilesPath = this.configData.getClsloadDB2DirPath()
					+ this.configData.getFilesPath();
		}
		Util.checkDirExist(this.loadDB2DataPath);
		Util.checkDirExist(this.loadDB2ClobPath);

		// 輸出log
		final String LOG_ROOT = this.logsDirPath + File.separator + "COPY_TEXT";
		Util.checkDirExist(LOG_ROOT);

		String copyLogPath = LOG_ROOT + File.separator + "copyAllText.log";
		try {
			this.logsCw = new PrintWriter(new BufferedWriter(
					new OutputStreamWriter(new FileOutputStream(new File(
							copyLogPath)))), true);
		} catch (Exception e) {
			this.logger.error("CopyAllTextFile 時產生錯誤! Exception: ", e);
			throw new DCException("CopyAllTextFile 時產生錯誤", e);
		}
		this.run();
		this.logger.info("CopyAllTextFile 執行結束");
	}

	/**
	 * 主要執行程式
	 */
	public void run() {
		this.logsCw.println(" CopyAllTextFile 起始時間 :" + Util.getNowTime());
		long tt1 = System.currentTimeMillis();
		try {
			File file = new File(this.dxlDirRootPath);
			this.logger.info("正在集中檔案及移動clob檔...");
			for (File fe : file.listFiles()) {
				// 第一層 分行代號
				if (fe.isDirectory()) {
					String brno = fe.getName();
					@SuppressWarnings("unused")
					File destClobDir = new File(this.loadDB2ClobPath
							+ File.separator + brno);
					for (File subDir : DXLUtil.list_subdir(fe)) {
//					for (File subDir : fe.listFiles()) {
						// 第二層 notes view name
						if (subDir.isDirectory()) {
							this.logsCw.println("\n---分行代號-:" + fe.getName()
									+ ":---notes view name---:"
									+ subDir.getName());
							long readViewTime = System.currentTimeMillis();

							String txtPath = subDir.getAbsolutePath()
									+ this.configData.getTextPath();
							File txtFile = new File(txtPath);
							String[] fnlist = DXLUtil.list_suffix(txtFile, TextDefine.ATTACH_TXT);
//							String[] fnlist = txtFile
//									.list(new FilenameFilter() {
//										@Override
//										public boolean accept(File dir,
//												String name) {
//											return name
//													.endsWith(TextDefine.ATTACH_TXT); // 讀取所有指定檔案
//										}
//									});
							if(fnlist == null){
								this.logger.error(fe.getName()+"下"+subDir.getName()+"無資料");
								continue; 
							}else{
								for (String fn : fnlist) {
									int idx = fn.indexOf("_");// fnEx:FLMS110S01_L120S01A.txt
									String srFile = "", dtFile = "";
	
									if (TextDefine.SCHEMA_LMS
											.equalsIgnoreCase(schema)) {
										// export已合併,這裡不需再合一次
										if (fn.equalsIgnoreCase("L120M01C.txt")) {
											continue;
										}
										String dtf = fn.substring(idx + 1);
										srFile = txtPath + File.separator + fn;
										dtFile = this.loadDB2DataPath
												+ File.separator + dtf;
									} else {
										// 此檔是export時為了之後比對key值所產生的檔案
										if (fn.equalsIgnoreCase("DXL_Key.txt")) {
											continue;
										}
										String dtf = "";
										// 只要是FCLS115M01產生的Table都需單獨合併
										String formName = fn.split("_")[0];
										if (formName
												.equalsIgnoreCase(TextDefine.CLS_L140M01B_FORM_11501)
												||formName.equalsIgnoreCase("FCLS715M01")//2013-06-07 modified by Sandra與明澤討論後加入
												||formName.equalsIgnoreCase("FCLS114M01")
												||formName.equalsIgnoreCase("FCLS114M02")) {
											dtf = fn;
										} else {
											dtf = fn.substring(idx + 1);
										}
										srFile = txtPath + File.separator + fn;
										dtFile = this.loadDB2DataPath
												+ File.separator + dtf;
									}
									copyAppendFiles(srFile, dtFile);
								}
							}
							// 移動clob檔案 03-26 改為Parser時直接寫入loadDB2
							// String htmlDir = subDir.getAbsolutePath()
							// + this.configData.getHtmlPath();
							// File htmlHome = new File(htmlDir);
							// if (htmlHome.isDirectory()) {
							// for (File h : htmlHome.listFiles()) {
							// // 移動檔案
							// move(h, destClobDir);
							// }
							// }
							long cost2 = System.currentTimeMillis()
									- readViewTime;
							this.logsCw.print(" , 結束時間 :" + Util.getNowTime()
									+ " ,TOTAL TIME :"
									+ Util.millis2minute(cost2));
						}
					}
				}
			}
			// 處理附加檔案,圖檔
			this.logger.info("正在處理附加檔案及圖檔...");
			DocFile fileDf = new DocFile();
			fileDf.init(this.schema, this.dxlDirRootPath, this.loadDB2DataPath,
					this.loadDB2FilesPath, this.logsCw);
			fileDf.create();

			long cost = System.currentTimeMillis() - tt1;
			this.logsCw.println("\nCopyAllTextFile 結束時間 :" + Util.getNowTime()
					+ " ,TOTAL TIME :" + Util.millis2minute(cost));
		} catch (Exception e) {
			String errmsg = "匯集各分行的TEXT檔至load_db2目錄下時出現錯誤 !";
			this.logger.error(errmsg, e);
			this.logsCw.println(errmsg);
			e.printStackTrace(this.logsCw);
			throw new DCException(errmsg, e);
		} finally {
			IOUtils.closeQuietly(this.logsCw);
		}
	}

	/**
	 * 寫入檔案
	 * 
	 * @param srFile
	 *            來源檔案
	 * @param dtFile
	 *            目標檔案
	 * @return true:寫入成功 , false:寫入失敗
	 */
	private boolean copyAppendFiles(String srFile, String dtFile) {
		try {
			File f1 = new File(srFile);
			File f2 = new File(dtFile);
			InputStream in = new FileInputStream(f1);

			OutputStream out;
			if (f2.exists()) {
				// For Append the file.
				out = new FileOutputStream(f2, true);
			} else {
				// For Create or Overwrite the file.
				out = new FileOutputStream(f2);
			}
			byte[] buf = new byte[4096];
			int len;
			while ((len = in.read(buf)) > 0) {
				out.write(buf, 0, len);
			}
			in.close();
			out.close();
		} catch (Exception ex) {
			String errmsg = "製檔案至load_db2 : data目錄下時出現錯誤 ,來源檔案 :" + srFile
					+ " , 目的地 :" + dtFile;
			this.logger.error(errmsg, ex);
			this.logsCw.println(errmsg);
			ex.printStackTrace(this.logsCw);
			throw new DCException(errmsg, ex);
		}
		return true;
	}

	/**
	 * 移動檔案
	 * 
	 * @param srcFile
	 *            來源檔案
	 * @param destDir
	 *            目標路徑
	 */
	public void move(File srcFile, File destDir) {
		try {
			FileUtils.moveFileToDirectory(srcFile, destDir, true);
		} catch (Exception ex) {
			String errmsg = "複製檔案至load_db2 : clob目錄下時出現錯誤 ,來源檔案 :" + srcFile
					+ " , 目的地 :" + destDir;
			this.logger.error(errmsg, ex);
			this.logsCw.println(errmsg);
			ex.printStackTrace(this.logsCw);
			throw new DCException(errmsg, ex);
		}
	}

}
