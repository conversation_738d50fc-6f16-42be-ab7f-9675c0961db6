package com.mega.eloan.lms.base.common;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.utils.CustomerIdCheckUtil;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01B;
import com.mega.eloan.lms.model.C121M01C;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01F;
import com.mega.eloan.lms.model.C121M01G;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01I;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.sso.model.IBranch;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <AUTHOR>
 * 
 */
public class OverSeaUtil {
	/**
	 * C121M01A被引入L120M01A後，其docStatus為ZZZ 避免在 copy 資料後，menu 已覆核多出 1 筆
	 */
	public static final String C121M01A_IMPDOCSTATUS = "ZZZ";
	/** 1.從評等copy */
	public static final String C120M01A_RATINGKIND_1 = "1";
	/** 2:簽報書新增 */
	public static final String C120M01A_RATINGKIND_2 = "2";
	/** 3:由評等合併 */
	public static final String C120M01A_RATINGKIND_3 = "3";
	//--------------共用----------------------------
	//日本房貸=C121M01B、非房貸=C121M01F
	public static final String 海外評等_非房貸 = "N"; 
	public static final String 海外評等_房貸 = "M";
	//--------------日本----------------------------
	//目前日本模型，房貸非房貸的版號是統一的，且兩者的因子都一樣。統一使用同一個代號做控管。
	public static final String V1_0_LOAN_JP = "1.0";
	public static final String V2_0_LOAN_JP = "2.0";
//	public static final String V2_0_JP_ACTIVEDATE = "9999-01-01";
	
	// =====================
	public static final String OVERSEA_JP_CLSMODEL_ONDATE = "2015-10-30";
	public static final String OVERSEA_AU_CLSMODEL_ONDATE = "2015-10-02";
	public static final String OVERSEA_TH_CLSMODEL_ONDATE = "2017-03-31";

	public static final String CMP_ADJUSTREASON_ONDATE = "2016-11-11";

	// --------------採用模型註記----------------------------
	public static final String L120M01A_RatingFlag_CLS = "00";
	public static final String L120M01A_RatingFlag_JP = "JP"; //日本
	public static final String L120M01A_RatingFlag_AU = "AU"; //澳洲
	public static final String L120M01A_RatingFlag_TH = "TH"; //泰國
	
	// --------------評等代碼----------------------------
	public static final String C121M01A_MOW_TYPE_日本 = "J";
	public static final String C121M01A_MOW_TYPE_澳洲 = "A";
	public static final String C121M01A_MOW_TYPE_泰國 = "T";
	public static final String C121M01A_MOW_TYPE_柬埔寨 = "K";
	
	// --------------國別瑪----------------------------
	public static final String C121M01A_MOW_TYPE_COUNTRY_日本 = "JP"; 
	public static final String C121M01A_MOW_TYPE_COUNTRY_澳洲 = "AU"; 
	public static final String C121M01A_MOW_TYPE_COUNTRY_加拿大 = "CA"; 
	public static final String C121M01A_MOW_TYPE_COUNTRY_法國 = "FR"; 
	public static final String C121M01A_MOW_TYPE_COUNTRY_泰國 = "TH"; 
	public static final String C121M01A_MOW_TYPE_COUNTRY_越南 = "VN";
	public static final String C121M01A_MOW_TYPE_COUNTRY_柬埔寨 = "KH";
		
	public static final String R32 = "R32";
	
	//--------------亞洲其他地區模型(原澳洲模型)----------------------------
	public static final String V1_0_LOAN_AU = "1.0";
	public static final String V2_0_LOAN_AU = "2.0";
	public static final String V3_0_LOAN_AU = "3.0";
	public static final String V2_0_AU_ACTIVEDATE = "2016-03-18";
//	public static final String V3_0_AU_ACTIVEDATE = "9999-01-01";
//	public static final String V3_0_AU_ACTIVEDATE = "2024-01-01";
	
	//--------------亞洲地區模型(原泰國模型)----------------------------
	public static final String V1_0_LOAN_TH = "1.0";
	public static final String V2_0_LOAN_TH = "2.0";
//	public static final String V2_0_TH_ACTIVEDATE = "2024-07-01";

	public static final int TYPE_RAW = -1;
	public static final int TYPE_UI = 0;
	public static final int TYPE_RPT = 1;

	public static final String JP_NEGATIVE_ONLY_FATAL = "1to5Or9to10";
	public static final String JP_NAGATIVE_ALL = "1to10";

	/*
	 * 
	 * FORMATT_國家_TXFLAG_XXXXXXXXX 用來做 ● 金額的轉換(仟元←→元) ● 必填欄位的檢核(基本資料，可不必填
	 * keyMan, custPos)
	 */
	// =====================
	// 日本模型
	public static final String FORMATT_JP_TXFLAG_LMS1015V00 = "lms1015v00cb";
	public static final String FORMATT_JP_TXFLAG_LMS1015S02 = "lms1015s02cb";

	public static Set<String> getRating_FORMATT_JP_TXFLAG() {
		Set<String> r = new HashSet<String>();
		r.add(FORMATT_JP_TXFLAG_LMS1015V00);
		r.add(FORMATT_JP_TXFLAG_LMS1015S02);
		return r;
	}

	// =====================
	// 澳洲模型
	public static final String FORMATT_AU_TXFLAG_LMS1025V00 = "lms1025v00cb";
	public static final String FORMATT_AU_TXFLAG_LMS1025S02 = "lms1025s02cb";

	public static Set<String> getRating_FORMATT_AU_TXFLAG() {
		Set<String> r = new HashSet<String>();
		r.add(FORMATT_AU_TXFLAG_LMS1025V00);
		r.add(FORMATT_AU_TXFLAG_LMS1025S02);
		return r;
	}

	// =====================
	// 泰國模型
	public static final String FORMATT_TH_TXFLAG_LMS1035V00 = "lms1035v00cb";
	public static final String FORMATT_TH_TXFLAG_LMS1035S02 = "lms1035s02cb";

	public static Set<String> getRating_FORMATT_TH_TXFLAG() {
		Set<String> r = new HashSet<String>();
		r.add(FORMATT_TH_TXFLAG_LMS1035V00);
		r.add(FORMATT_TH_TXFLAG_LMS1035S02);
		return r;
	}

	// =====================
	// 　適用所有海外消金模型【日本／澳洲／泰國】
	public static Set<String> getRating_FORMATT_AllCountry_TXFLAG() {
		Set<String> r = new HashSet<String>();
		// 日本
		r.add(FORMATT_JP_TXFLAG_LMS1015V00);
		r.add(FORMATT_JP_TXFLAG_LMS1015S02);
		// 澳洲
		r.add(FORMATT_AU_TXFLAG_LMS1025V00);
		r.add(FORMATT_AU_TXFLAG_LMS1025S02);
		// 泰國
		r.add(FORMATT_TH_TXFLAG_LMS1035V00);
		r.add(FORMATT_TH_TXFLAG_LMS1035S02);
		// ~~~~~~~~
		return r;
	}

	// =====================
	// 　基本資料【日本／澳洲／泰國】
	public static Set<String> getRating_FORMATT_BasicData_TXFLAG() {
		Set<String> r = new HashSet<String>();
		r.add(FORMATT_JP_TXFLAG_LMS1015V00);
		r.add(FORMATT_AU_TXFLAG_LMS1025V00);
		r.add(FORMATT_TH_TXFLAG_LMS1035V00);
		return r;
	}

	// =====================
	// 一般簽報書
	public static final String FORMATT_TXFLAG_LMS1115S02 = "lms1115s02cb";

	public static final String M = "M";
	public static final String DF = "DF";
	public static final String L140M01A_CLS_GRADE = "l140m01a_cls_grade";
	public static final String L140M01C_CLS_GRADE = "l140m01c_cls_grade";

	public static boolean isCaseDoc_CLS_JP_ON(L120M01A model) {
		return model == null ? false : Util.equals(model.getDocType(),
				UtilConstants.Casedoc.DocType.個金)
				&& Util.equals(model.getRatingFlag(), L120M01A_RatingFlag_JP);
	}

	public static boolean isCaseDoc_CLS_AU_ON(L120M01A model) {
		return model == null ? false : Util.equals(model.getDocType(),
				UtilConstants.Casedoc.DocType.個金)
				&& Util.equals(model.getRatingFlag(), L120M01A_RatingFlag_AU);
	}

	public static boolean isCaseDoc_CLS_TH_ON(L120M01A model) {
		return model == null ? false : Util.equals(model.getDocType(),
				UtilConstants.Casedoc.DocType.個金)
				&& Util.equals(model.getRatingFlag(), L120M01A_RatingFlag_TH);
	}

	/**
	 * 是否為海外消金評等模型
	 */
	public static boolean isCaseDoc_CLS_RatingFlag_ON(L120M01A model) {
		if (isCaseDoc_CLS_JP_ON(model)) {
			return true;
		}
		if (isCaseDoc_CLS_AU_ON(model)) {
			return true;
		}
		if (isCaseDoc_CLS_TH_ON(model)) {
			return true;
		}
		return false;
	}

	/**
	 * 使用時，應被包在 LMSUtil.isOverSea_CLS(...) 裡 不然 else 可能會是企金簽報書 <br/>
	 * 案件別【陳復(述)、異常通報=true】【一般、其他(含額度序號)=true|false】
	 */
	public static boolean isCaseDoc_CLS_rawBorrowerPanel(L120M01A l120m01a) {
		if (isCaseDoc_CLS_RatingFlag_ON(l120m01a)) {
			if (isDocCode_hasCntrNo(l120m01a.getDocCode())) {
				// 有額度明細表的類型，出現 package
				return false;
			}
		}
		return true;// default
	}

	/**
	 * 陳復(述)、異常通報，沒有引入C121M01A
	 */
	public static boolean isDocCode_hasCntrNo(String docCode) {
		if (Util.equals(UtilConstants.Casedoc.DocCode.一般, docCode)
				|| Util.equals(UtilConstants.Casedoc.DocCode.其他, docCode)) {
			// 有額度明細表的類型，出現 package
			return true;
		}
		return false;
	}

	public static String getFormAttrTxFlag(PageParameters params) {
		return Util.trim(params.getString("formAttrTxFlag"));
	}

	public static void getCustData3(CapAjaxFormResult result,
			CapAjaxFormResult map1, CapAjaxFormResult map2,
			CapAjaxFormResult map3, CapAjaxFormResult map4) {
		if ("{}".equals(Util.trim(map1))) {
			result.set("empty1", true);
		}
		if ("{}".equals(Util.trim(map2))) {
			result.set("empty2", true);
		}
		if ("{}".equals(Util.trim(map3))) {
			result.set("empty3", true);
		}
		if ("{}".equals(Util.trim(map4))) {
			result.set("empty4", true);
		}
		result.set("sCoAddr", map1);
		result.set("sMComTel", map2);
		result.set("sMTel", map3);
		result.set("sEmail", map4);
	}

	public static void setC121M01A(C121M01A meta, String uuid) {
		meta.setMainId(uuid);
		meta.setRatingId(uuid);
	}

	public static void copyC120_ref(String mainId, String custId, String dupNo,
			String creator, String createTime, C120M01A c120m01a,
			C120S01A c120s01a, C120S01B c120s01b, C120S01C c120s01c,
			C120S01D c120s01d, C120S01E c120s01e) {

		JSONObject addborrowJson = new JSONObject();
		addborrowJson.put("mainId", mainId);
		addborrowJson.put("custId", custId);
		addborrowJson.put("dupNo", dupNo);
		addborrowJson.put("creator", creator);
		addborrowJson.put("createTime", creator);

		DataParse.toBean(addborrowJson, c120m01a);
		DataParse.toBean(addborrowJson, c120s01a);
		DataParse.toBean(addborrowJson, c120s01b);
		DataParse.toBean(addborrowJson, c120s01c);
		DataParse.toBean(addborrowJson, c120s01d);
		DataParse.toBean(addborrowJson, c120s01e);
	}

	public static void copyC121M01B(C121M01B bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}
	
	public static void copyC121M01F(C121M01F bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}

	public static void copyC121M01C(C121M01C bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}
	
	public static void copyC121M01G(C121M01G bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}

	public static void copyC121M01D(C121M01D bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}
	
	public static void copyC121M01H(C121M01H bean, C121M01A meta,
			String custId, String dupNo) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
		bean.setOwnBrId(meta.getOwnBrId());
		bean.setCustId(custId);
		bean.setDupNo(dupNo);
	}
	
	

	public static void copyC121S01A(C121S01A bean, C121M01A meta) {
		bean.setMainId(meta.getMainId());
		bean.setRatingId(meta.getRatingId());
	}

	public static List<String> bean_to_ui_CheckBoxStr_PerChar(String val) {
		String str = Util.trim(val);
		int size = str.length();
		List<String> r = new ArrayList<String>();
		for (int i = 0; i < size; i++) {
			r.add(str.substring(i, i + 1));
		}
		return r;
	}

	public static String[] bean_to_ui_CheckBoxStr(String val, String sep) {
		return StringUtils.split(val, sep);
	}

	public static String ui_to_bean_CheckBoxStr(PageParameters params,
			String key, String sep) {
		String[] v = params.getStringArray(key);
		return StringUtils.join(v, sep);
	}

	public static Integer ui_to_bean_Integer(PageParameters params, String key) {
		String v = Util.trim(params.getString(key));
		return (Util.isEmpty(Util.trim(v)) ? null : Util.parseInt(String
				.valueOf(v)));
	}

	public static BigDecimal ui_to_bean_BigDecimal(PageParameters params,
			String key) {
		String v = Util.trim(params.getString(key));
		BigDecimal r = null;
		if (Util.isEmpty(Util.trim(v))) {

		} else {
			r = CapMath.getBigDecimal(v);
		}
		return r;
	}

	/**
	 * DB中的layout是 Dec(5,2) 最大只能輸入999.99
	 * 
	 * 當輸入2000，又要存到DB，會拋出JPA Exception
	 */
	public static BigDecimal ui_to_bean_BigDecimal(PageParameters params,
			String key, BigDecimal maxVal) {
		String v = Util.trim(params.getString(key));
		if (Util.isEmpty(Util.trim(v))) {
			return null;
		}

		BigDecimal r = CapMath.getBigDecimal(v);

		if (r.compareTo(maxVal) > 0) {
			return maxVal;
		} else {
			return r;
		}

	}

	public static void add_empty_to_list(List<String> r, Object val,
			Properties propCol, String keyCol) {
		add_empty_to_list(r, val, Util.trim(propCol.getProperty(keyCol)));
	}

	public static void add_empty_to_list(List<String> r, Object val,
			String colDesc) {
		Properties prop_abstractOverSeaCLSPage = MessageBundleScriptCreator
				.getComponentResource(AbstractOverSeaCLSPage.class);
		if (val == null || Util.isEmpty(Util.trim(val))) {
			r.add(MessageFormat.format(prop_abstractOverSeaCLSPage
					.getProperty("message.emptyField"), colDesc));
		}
	}

	/**
	 * ref LMSM01FormHandler:: verifyEntity
	 */
	public static void verifyEntity(List<String> panel1, List<String> panel2,
			List<String> panel3, List<String> panel4, List<String> panel5,
			Properties prop, String formAttrTxFlag,
			boolean noReqFieldForRating, C120M01A c120m01a, C120S01A c120s01a,
			C120S01B c120s01b, C120S01C c120s01c, C120S01D c120s01d,
			C120S01E c120s01e, L120S01M l120s01m,
			boolean has_snrY_snrM, String snrY, String snrM) {
		boolean isCls = LMSUtil.isBusCode_060000_130300(c120s01a.getBusCode());
		if (true) { // 基本資料
			if (getRating_FORMATT_BasicData_TXFLAG().contains(formAttrTxFlag)) {

			} else {
				if ("Y".equals(c120m01a.getKeyMan())) {

				} else {
					// 如果不是主要借款人則檢查相關身份與主要借款人關係是否不為空
					// 驗證個金資料必填欄位
					add_empty_to_list(panel1, c120m01a.getO_custRlt(), prop,
							"L120S01A.custRlt");
					add_empty_to_list(panel1, c120m01a.getCustPos(), prop,
							"L120S01A.custPos");
				}
			}

			if (true) {
				String birthday = TWNDate.toAD(c120s01a.getBirthday());
				if (isCls && Util.equals(birthday, "0001-01-01")) {
					birthday = "";
				}
				add_empty_to_list(panel1, birthday, prop, "L120S01H.birthday");
			}
			add_empty_to_list(panel1, c120s01a.getO_custClass(), prop,
					"l120s01b.custClass");

			if (isCls) {
				add_empty_to_list(panel1, c120s01a.getEdu(), prop,
						"L120S01H.edu");
				add_empty_to_list(panel1, c120s01a.getSex(), prop,
						"L120S01H.sex");
				add_empty_to_list(panel1, c120s01a.getMarry(), prop,
						"L120S01H.marry");
				add_empty_to_list(panel1, c120s01a.getChild(), prop,
						"L120S01H.child");
			}
			add_empty_to_list(panel1, c120s01a.getNtCode(), prop,
					"L120S01H.ntCode");
			if (isCls) {
				add_empty_to_list(panel1, c120s01a.getFAddr(), prop,
						"L120S01H.fAddr");
				add_empty_to_list(panel1, c120s01a.getCoAddr(), prop,
						"L120S01H.coAddr");
				add_empty_to_list(panel1, c120s01a.getCmsStatus(), prop,
						"L120S01H.cmsStatus");
			}
		}
		// ====== panel2 ====================================
		if (isCls) { // 服務單位
			add_empty_to_list(panel2, c120s01b.getComName(), prop,
					"L120S01I.comName");
			add_empty_to_list(panel2, c120s01b.getJobType1(), prop,
					"L120S01I.jobType1");
			add_empty_to_list(panel2, c120s01b.getJobType2(), prop,
					"L120S01I.jobType2");
			add_empty_to_list(panel2, c120s01b.getJobTitle(), prop,
					"L120S01I.jobTitle");

			if (getRating_FORMATT_AllCountry_TXFLAG().contains(formAttrTxFlag)) {
				// 日本、澳洲模型，風控都提出，年薪為必填
				// check 年薪必填
				add_empty_to_list(panel2, Util.trim(c120s01b.getPayCurr()),
						prop, "L120S01I.payCurr");
				add_empty_to_list(panel2, Util.trim(c120s01b.getPayAmt()),
						prop, "L120S01I.payAmt");

			} else {
				if (c120s01b.getPayAmt() != null
						&& Util.isEmpty(c120s01b.getPayCurr())) {
					// [年薪]有金額時，幣別為必填
					add_empty_to_list(panel2, Util.trim(c120s01b.getPayCurr()),
							prop, "L120S01I.payCurr");
				}
			}

			add_empty_to_list(panel2, LMSUtil.pretty_numStr(c120s01b.getSeniority()), prop,
					"L120S01I.seniority");
			if(has_snrY_snrM){
				//判斷 snrY , snrM
				add_empty_to_list(panel2, snrY, prop, "L120S01I.seniority_year");
				add_empty_to_list(panel2, snrM, prop, "L120S01I.seniority_month");
			}
			add_empty_to_list(panel2, c120s01b.getInDoc(), prop,
					"L120S01I.inDoc");

			if (c120s01b.getOthAmt() != null
					&& Util.isEmpty(c120s01b.getOthCurr())) {
				// [其他收入]有金額時，幣別為必填
				add_empty_to_list(panel2, Util.trim(c120s01b.getOthCurr()),
						prop, "L120S01J.oMoneyCurr");
			}

			if (true) {
				if (getRating_FORMATT_AllCountry_TXFLAG().contains(
						formAttrTxFlag)) {

					if (Util.isNotEmpty(Util.trim(c120s01c.getOIncome()))) {
						// 當有勾選 其它收入 的item時，其他收入幣別、金額會變成必需填的欄位
						add_empty_to_list(panel2, c120s01c.getOMoneyAmt(),
								prop, "L120S01J.oMoneyAmt");
					}

					if (c120s01c.getOMoneyAmt() != null
							&& Util.isEmpty(c120s01c.getOMoneyCurr())) {
						// [其他收入]有金額時，幣別為必填
						add_empty_to_list(panel2,
								Util.trim(c120s01c.getOMoneyCurr()), prop,
								"L120S01J.oMoneyCurr");
					}
				} else if (Util.equals(formAttrTxFlag,
						FORMATT_TXFLAG_LMS1115S02)) {

				}
			}
		}
		// ====== panel3 ====================================
		if (isCls) { // 償債能力
			if (true) {
				if (getRating_FORMATT_AllCountry_TXFLAG().contains(
						formAttrTxFlag)) {

				} else if (Util.equals(formAttrTxFlag,
						FORMATT_TXFLAG_LMS1115S02)) {
					if (c120s01c.getOMoneyAmt() != null
							&& Util.isEmpty(c120s01c.getOMoneyCurr())) {
						// [其他收入]有金額時，幣別為必填
						add_empty_to_list(panel3,
								Util.trim(c120s01c.getOMoneyCurr()), prop,
								"L120S01J.oMoneyCurr");
					}
				}
			}

			if (getRating_FORMATT_JP_TXFLAG().contains(formAttrTxFlag)) {
				if (c120s01c.getRealEstateRentIncomeAmt() != null
						&& Util.isEmpty(c120s01c.getRealEstateRentIncomeCurr())) {
					// [本次新做案下不動產租金收入]有金額時，幣別為必填
					add_empty_to_list(panel3,
							Util.trim(c120s01c.getRealEstateRentIncomeCurr()),
							prop, "C120S01C.realEstateRentIncomeCurr");
				}
			}

			if (getRating_FORMATT_TH_TXFLAG().contains(formAttrTxFlag)) {
				if (c120s01c.getTotMmInAmt() != null
						&& Util.isEmpty(c120s01c.getTotMmInCurr())) {
					// [每月個人收入金額]有金額時，幣別為必填
					add_empty_to_list(panel3,
							Util.trim(c120s01c.getTotMmInCurr()), prop,
							"C120S01C.totMmInCurr");
				}
				if (c120s01c.getTotMmExpAmt() != null
						&& Util.isEmpty(c120s01c.getTotMmExpCurr())) {
					// [每月個人支出金額]有金額時，幣別為必填
					add_empty_to_list(panel3,
							Util.trim(c120s01c.getTotMmExpCurr()), prop,
							"C120S01C.totMmExpCurr");
				}
			}

			if (noReqFieldForRating) {

			} else {
				if (getRating_FORMATT_JP_TXFLAG().contains(formAttrTxFlag)) {

				} else if (getRating_FORMATT_AU_TXFLAG().contains(
						formAttrTxFlag)) {
					add_empty_to_list(panel3, c120s01c.getIssueLC(), prop,
							"C120S01C.issueLC");
				}
			}
			if (Util.equals("Y", c120s01c.getIssueLC())) {

			} else if (Util.equals("N", c120s01c.getIssueLC())) {
				add_empty_to_list(panel3, c120s01c.getICRFlag(), prop,
						"C120S01C.ICRFlag");

				if (Util.equals("Y", c120s01c.getICRFlag())) {
					add_empty_to_list(panel3, c120s01c.getICR(), prop,
							"C120S01C.ICR");
				}
			}

			add_empty_to_list(panel3, c120s01c.getYFamCurr(), prop,
					"L120S01J.yFamCurr");
			add_empty_to_list(panel3, c120s01c.getYFamAmt(), prop,
					"L120S01J.yFamAmt");
			add_empty_to_list(panel3, c120s01c.getYIncomeCert(), prop,
					"L120S01J.yIncomeCert");
			// ~~~
			add_empty_to_list(panel3, c120s01c.getDRate(), prop,
					"L120S01J.dRate");
			add_empty_to_list(panel3, c120s01c.getYRate(), prop,
					"L120S01J.yRate");
			add_empty_to_list(panel3, c120s01c.getIsPeriodFund(), prop,
					"L120S01J.isPeriodFund");
			add_empty_to_list(panel3, c120s01c.getBusi(), prop, "L120S01J.busi");
			// ~~~
			add_empty_to_list(panel3, c120s01c.getInvMBalCurr(), prop,
					"L120S01J.invMBalCurr");
			add_empty_to_list(panel3, c120s01c.getInvMBalAmt(), prop,
					"L120S01J.invMBalAmt");
			// ~~~
			add_empty_to_list(panel3, c120s01c.getInvOBalCurr(), prop,
					"L120S01J.invOBalCurr");
			add_empty_to_list(panel3, c120s01c.getInvMBalAmt(), prop,
					"L120S01J.invOBalAmt");
			// ~~~
			add_empty_to_list(panel3, c120s01c.getBranCurr(), prop,
					"L120S01J.branCurr");
			add_empty_to_list(panel3, c120s01c.getBranAmt(), prop,
					"L120S01J.branAmt");

			if (noReqFieldForRating) {

			} else {
				if (c120s01c.getTotMmInAmt() != null
						|| c120s01c.getTotMmExpAmt() != null) {
					// 是否已輸入了每月收入合計、支出合計值(非0)，但沒有附加檔案?
					// 若有此情形，顯示訊息:「請附加個人資料表檔案作為每月收支資料佐證(Please attach
					// “Individual Information” file in order to prove monthly
					// total income and expense.)」
					add_empty_to_list(panel3, c120m01a.getAttchFileOid(), prop,
							"tab03.TH.desc01");
				}
			}
		}
		if (isCls) { // 配偶資料
			if ("B".equals(Util.trim(c120s01d.getMateFlag()))
					|| "C".equals(Util.trim(c120s01d.getMateFlag()))) {// B列於本欄
																		// C同本案借款人
				add_empty_to_list(panel4, c120s01d.getMCustId(), prop,
						"L120S01K.mCustId");
				add_empty_to_list(panel4, c120s01d.getMDupNo(), prop,
						"L120S01K.mDupNo");
				add_empty_to_list(panel4, c120s01d.getMName(), prop,
						"L120S01K.mName");

			}

			if ("B".equals(Util.trim(c120s01d.getMateFlag()))) {

				if (c120s01d.getMPayAmt() != null
						&& Util.isEmpty(c120s01d.getMPayCurr())) {
					// [配偶年薪]有金額時，幣別為必填
					add_empty_to_list(panel4,
							Util.trim(c120s01d.getMPayCurr()), prop,
							"L120S01K.mPayCurr");
				}
			}
		}

		if (true) { // 相關查詢資料
			// 票信
			if (!"3".equals(c120s01e.getEChkFlag())) {// 1.有、2.無、3.N.A
				add_empty_to_list(panel5,
						TWNDate.toAD(c120s01e.getEChkDDate()), prop,
						"L120S01L.eChkDDate");
				add_empty_to_list(panel5,
						TWNDate.toAD(c120s01e.getEChkQDate()), prop,
						"L120S01L.eChkQDate");

			}
			// 聯徵
			if (!"3".equals(c120s01e.getEJcicFlag())) {// 1.有、2.無、3.N.A
				add_empty_to_list(panel5,
						TWNDate.toAD(c120s01e.getEJcicDDate()), prop,
						"L120S01L.eJcicDDate");
				add_empty_to_list(panel5,
						TWNDate.toAD(c120s01e.getEJcicQDate()), prop,
						"L120S01L.eJcicQDate");
			}
			if (isCls) {
				if (noReqFieldForRating) {

				} else {
					if (getRating_FORMATT_JP_TXFLAG().contains(formAttrTxFlag)) {
						// 檢核是否有 EJcicFlag
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getEJcicFlag()), prop,
								"l120s02.index60");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getEJcicFlag())
								|| Util.equals(UtilConstants.haveNo.無,
										c120s01e.getEJcicFlag())) {
							// 檢查附加檔案
							add_empty_to_list(panel5,
									Util.trim(c120m01a.getAttchFileOid()),
									prop, "tab05.JP.desc03");
						}
						// 日本模型的 chkItem1 由以下4個因子組成
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata9()), prop,
								"l120s02.index81");
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata10()), prop,
								"l120s02.index82");
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata11()), prop,
								"l120s02.index83");
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata13()), prop,
								"l120s02.index84");

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata19()), prop,
								"C120S01E.isQdata19");
						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata20()), prop,
								"C120S01E.isQdata20");

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata25()), prop,
								"C120S01E.isQdata25");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata25())) {
							add_empty_to_list(panel5, c120s01e.getBalQdata25(),
									prop, "C120S01E.isQdata25");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata26()), prop,
								"C120S01E.isQdata26");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata26())) {
							add_empty_to_list(panel5, c120s01e.getBalQdata26(),
									prop, "C120S01E.isQdata26");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata28()), prop,
								"C120S01E.isQdata28");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata28())) {
							add_empty_to_list(panel5, c120s01e.getBalQdata28(),
									prop, "C120S01E.isQdata28");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata21()), prop,
								"C120S01E.isQdata21");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata21())) {
							add_empty_to_list(panel5, c120s01e.getCntQdata21(),
									prop, "C120S01E.isQdata21");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata22()), prop,
								"C120S01E.isQdata22");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata22())) {
							add_empty_to_list(panel5, c120s01e.getCntQdata22(),
									prop, "C120S01E.isQdata22");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata23()), prop,
								"C120S01E.isQdata23");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata23())) {
							add_empty_to_list(panel5, c120s01e.getCntQdata23(),
									prop, "C120S01E.isQdata23");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata24()), prop,
								"C120S01E.isQdata24");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata24())) {
							add_empty_to_list(panel5, c120s01e.getCntQdata24(),
									prop, "C120S01E.isQdata24");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getIsQdata27()), prop,
								"C120S01E.isQdata27");
						if (Util.equals(UtilConstants.haveNo.有,
								c120s01e.getIsQdata27())) {
							add_empty_to_list(panel5, c120s01e.getCntQdata27(),
									prop, "C120S01E.isQdata27");
						}

						add_empty_to_list(panel5,
								Util.trim(c120s01e.getJ10_score_flag()), prop,
								"C120S01E._j10");
						if (Util.equals("A", c120s01e.getJ10_score_flag())) {
							add_empty_to_list(panel5, c120s01e.getJ10_score(),
									prop, "C120S01E._j10");
						}

					} else if (getRating_FORMATT_AU_TXFLAG().contains(
							formAttrTxFlag)) {
						if(LMSUtil.get_AU_BRNO_SET().contains(c120m01a.getOwnBrId())){ //澳洲才有vedaReport，僅檢核澳洲地區
							if (Util.equals(UtilConstants.haveNo.無,
									c120s01e.getVedaRecord())
									|| Util.equals(UtilConstants.haveNo.NA,
											c120s01e.getVedaRecord())) {
								// 無資料，不check
							} else {
								// 檢核是否有 VedaRecord
								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaRecord()), prop,
										"C120S01E.vedaRecord");
								if (Util.equals(UtilConstants.haveNo.有,
										c120s01e.getVedaRecord())) {
									// 檢查附加檔案
									add_empty_to_list(panel5,
											Util.trim(c120m01a.getAttchFileOid()),
											prop, "tab05.AU.desc02");
								}

								add_empty_to_list(panel5,
										TWNDate.toAD(c120s01e.getVedaQDate()),
										prop, "C120S01E.vedaQDate");
								// ~~~
								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaScoreFlag()),
										prop, "C120S01E.vedaScore");
								if (Util.equals(UtilConstants.haveNo.有,
										c120s01e.getVedaScoreFlag())) {
									add_empty_to_list(panel5,
											c120s01e.getVedaScore(), prop,
											"C120S01E.vedaScore");
								}

								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaAdverseFile()),
										prop, "C120S01E.vedaAdverseFile");
								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaDefaultAmt()),
										prop, "C120S01E.vedaDefaultAmt");
								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaJudgement()),
										prop, "C120S01E.vedaJudgement");

								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaEnquiriesFlag()),
										prop, "C120S01E.vedaEnquiriesFlag");
								if (Util.equals(UtilConstants.haveNo.有,
										c120s01e.getVedaEnquiriesFlag())) {
									add_empty_to_list(panel5,
											c120s01e.getVedaEnquiriesTimes(), prop,
											"C120S01E.vedaEnquiriesFlag");
								}

								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaProvider()),
										prop, "C120S01E.vedaProvider");

								add_empty_to_list(panel5,
										Util.trim(c120s01e.getVedaFileAgeFlag()),
										prop, "C120S01E.vedaFileAgeFlag");
								if (Util.equals(UtilConstants.haveNo.有,
										c120s01e.getVedaFileAgeFlag())) {
									add_empty_to_list(panel5,
											Util.trim(c120s01e.getVedaFileAge()),
											prop, "C120S01E.vedaFileAgeFlag");
								}
							}
						}
					} else if (getRating_FORMATT_TH_TXFLAG().contains(
							formAttrTxFlag)) {
						if(LMSUtil.get_TH_BRNO_SET().contains(c120m01a.getOwnBrId())){ //NCB僅適用泰國，僅檢核泰國地區
						if (Util.equals(UtilConstants.haveNo.無,
								c120s01e.getNcbRecord())
								|| Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbRecord())) {
							// 無資料
							if (true) {
								// 無NCB報告時，以下3項只能選NA,不可選[有,無]
								String p0_rpt = prop
										.getProperty("C120S01E.ncbReport");
								String p2_na = prop
										.getProperty("l120s01a.radio21");
								if (!Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbOver90DaysFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.noReport_columnMustBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbOver90Days"),
											p2_na));
								}
								if (!Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbMaximumDpdFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.noReport_columnMustBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbMaximumDpd"),
											p2_na));
								}
								if (!Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbEnqRecent6MFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.noReport_columnMustBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbEnqRecent6M"),
											p2_na));
								}
							}
						} else {
							// 有 NcbRecord，檢核欄位
							add_empty_to_list(panel5,
									Util.trim(c120s01e.getNcbRecord()), prop,
									"C120S01E.ncbRecord");

							add_empty_to_list(panel5,
									TWNDate.toAD(c120s01e.getNcbQDate()), prop,
									"C120S01E.ncbQDate");
							if (true) {
								if (c120s01e.getNcbQDate() != null
										&& LMSUtil.cmpDate(c120s01e
												.getNcbQDate(), ">=", CapDate
												.addYears(CapDate
														.getCurrentTimestamp(),
														500))) {
									// 泰國官方之佛曆年 = 西曆年 + 543
									String p0 = prop
											.getProperty("C120S01E.ncbQDate")
											+ ":"
											+ Util.trim(TWNDate.toAD(c120s01e
													.getNcbQDate())) + " ";
									panel5.add(MessageFormat.format(
											prop.getProperty("message.date_no_buddha"),
											p0));
								}
							}
							// ~~~
							add_empty_to_list(panel5,
									Util.trim(c120s01e.getNcbOver90DaysFlag()),
									prop, "C120S01E.ncbOver90Days");
							if (Util.equals(UtilConstants.haveNo.有,
									c120s01e.getNcbOver90DaysFlag())) {
								add_empty_to_list(panel5,
										c120s01e.getNcbOver90Days(), prop,
										"C120S01E.ncbOver90Days");
							}

							add_empty_to_list(panel5,
									Util.trim(c120s01e.getNcbMaximumDpdFlag()),
									prop, "C120S01E.ncbMaximumDpd");
							if (Util.equals(UtilConstants.haveNo.有,
									c120s01e.getNcbMaximumDpdFlag())) {
								add_empty_to_list(panel5,
										c120s01e.getNcbMaximumDpd(), prop,
										"C120S01E.ncbMaximumDpd");
							}

							add_empty_to_list(
									panel5,
									Util.trim(c120s01e.getNcbEnqRecent6MFlag()),
									prop, "C120S01E.ncbEnqRecent6M");
							if (Util.equals(UtilConstants.haveNo.有,
									c120s01e.getNcbEnqRecent6MFlag())) {
								add_empty_to_list(
										panel5,
										Util.trim(c120s01e.getNcbEnqRecent6M()),
										prop, "C120S01E.ncbEnqRecent6M");
							}
							// ~~~
							if (true) {
								// 有NCB報告時，以下3項不可選NA，只能選[有,無]
								String p0_rpt = prop
										.getProperty("C120S01E.ncbReport");
								String p2_na = prop
										.getProperty("l120s01a.radio21");
								if (Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbOver90DaysFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.hasReport_columnCanNotBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbOver90Days"),
											p2_na));
								}
								if (Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbMaximumDpdFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.hasReport_columnCanNotBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbMaximumDpd"),
											p2_na));
								}
								if (Util.equals(UtilConstants.haveNo.NA,
										c120s01e.getNcbEnqRecent6MFlag())) {
									panel5.add(MessageFormat.format(
											prop.getProperty("message.hasReport_columnCanNotBeX"),
											p0_rpt,
											prop.getProperty("C120S01E.ncbEnqRecent6M"),
											p2_na));
								}
							}
							// ~~~
							// 檢查互斥狀況
							if (Util.equals(UtilConstants.haveNo.有,
									c120s01e.getNcbOver90DaysFlag())
									&& (!Util.equals(UtilConstants.haveNo.有,
											c120s01e.getNcbMaximumDpdFlag()) || !Util
											.equals("3",
													c120s01e.getNcbMaximumDpd()))) {
								panel5.add(prop
										.getProperty("message.ncb_over90_maxDpd_NE_3"));
							}
							if (!Util.equals(UtilConstants.haveNo.有,
									c120s01e.getNcbOver90DaysFlag())
									&& (Util.equals(UtilConstants.haveNo.有,
											c120s01e.getNcbMaximumDpdFlag()) && Util
											.equals("3",
													c120s01e.getNcbMaximumDpd()))) {
								panel5.add(prop
										.getProperty("message.ncb_notOver90_maxDpd_EQ_3"));
							}
						}
					}
				}
				}
			} else {
				// 企金戶的畫面，沒提到要改
			}

			if (l120s01m == null) {
				// 尚未引進「授信信用風險管理」遵循檢核！
				panel5.add(prop.getProperty("l120s02.alert30"));
			} else {
				if (Util.notEquals(Util.trim(c120s01e.getIsQdata2()),
						Util.trim(l120s01m.getMbRlt()))
						|| Util.notEquals(Util.trim(c120s01e.getIsQdata3()),
								Util.trim(l120s01m.getMhRlt44()))
						|| Util.notEquals(Util.trim(c120s01a.getBusCode()),
								Util.trim(l120s01m.getBusCode()))) {
					// 借款人基本資料之所屬集團、利害關係人、行業對項別與「授信信用風險管理」遵循檢核引進時狀態有不一致，請重新引進！
					panel5.add(prop.getProperty("l120s02.alert31"));
				}

				if (getRating_FORMATT_BasicData_TXFLAG().contains(
						formAttrTxFlag)) {
					Date cmpDate = CapDate.shiftDays(new Date(), -60);
					if (l120s01m.getQueryDate() != null
							&& LMSUtil.cmpDate(l120s01m.getQueryDate(), "<",
									cmpDate)) {
						panel5.add(prop.getProperty("error.02")
								+ TWNDate.toAD(l120s01m.getQueryDate()));
					}
				}
			}

			if (Util.isEmpty(Util.trim(c120s01e.getMbRlt33()))) {
				panel5.add(prop.getProperty("l120s02.alert32"));// 尚未輸入「銀行法第33條之2、銀行法第33條之4」之情形！
			}
		}
	}

	public static int rating_min1_max10(int src) {
		return min_rating_1(max_rating_10(src));
	}
	
	public static int rating_min1_max15(int src) {
		return min_rating_1(max_rating_15(src));
	}

	public static int rating_min9_max10(int src) {
		return min_rating_9(max_rating_10(src));
	}
	
	public static int rating_min14_max15(int src) {
		return min_rating_14(max_rating_15(src));
	}

	private static int min_rating_1(int src) {
		int min_rating = 1;
		if (src < min_rating) {
			// 目前最多升到1等
			return min_rating;
		}
		return src;
	}

	private static int min_rating_9(int src) {
		int min_rating = 9;
		if (src < min_rating) {
			return min_rating;
		}
		return src;
	}
	
	private static int min_rating_14(int src) {
		int min_rating = 14;
		if (src < min_rating) {
			return min_rating;
		}
		return src;
	}

	private static int max_rating_10(int src) {
		int max_rating = 10;
		if (src > max_rating) {
			// 目前最多降到10等
			return max_rating;
		}
		return src;
	}
	
	//J-111-0373
	private static int max_rating_15(int src) {
		int max_rating = 15;
		if (src > max_rating) {
			// 目前最多降到15等
			return max_rating;
		}
		return src;
	}

	public static boolean eqBigDecimal(BigDecimal b1, BigDecimal b2) {
		if (b1 == null && b2 == null) {
			return true;
		} else if (b1 == null && b2 != null) {
			return false;
		} else if (b1 != null && b2 == null) {
			return false;
		} else {
			return (b1.compareTo(b2) == 0);
		}
	}

	public static boolean eqDate(Date b1, Date b2) {
		String d1 = Util.trim(TWNDate.toAD(b1));
		String d2 = Util.trim(TWNDate.toAD(b2));

		return Util.equals(d1, d2);
	}

	public static String pretty_numStr(String r) {
		int idx = r.lastIndexOf(".");
		if (idx > 0) {
			String bf = r.substring(0, idx);
			String point = r.substring(idx, idx + 1);
			String af = r.substring(idx + 1);
			for (int i = 0; i < 10; i++) {
				if (af.endsWith("0")) {
					af = af.substring(0, af.length() - 1);
				}
			}
			return bf + (Util.isNotEmpty(af) ? (point + af) : "");
		} else {
			return r;
		}
	}

	public static Map<String, Boolean> set_L120M01A_ratingFlag(L120M01A l120m01a) {

		String ratingFlag = Util.trim(l120m01a.getRatingFlag());
		boolean r = false;
		boolean r00 = false;
		boolean rJP = false;
		boolean rAU = false;
		boolean rTH = false;
		if (Util.equals(OverSeaUtil.L120M01A_RatingFlag_CLS, ratingFlag)) {
			r00 = true;
		} else if (Util.equals(OverSeaUtil.L120M01A_RatingFlag_JP, ratingFlag)) {
			rJP = true;
		} else if (Util.equals(OverSeaUtil.L120M01A_RatingFlag_AU, ratingFlag)) {
			rAU = true;
		} else if (Util.equals(OverSeaUtil.L120M01A_RatingFlag_TH, ratingFlag)) {
			rTH = true;
		} else {
			r = true;
		}
		Map<String, Boolean> map = new HashMap<String, Boolean>();
		map.put("hs_L120M01A_ratingFlag_", r);
		map.put("hs_L120M01A_ratingFlag_00", r00);
		map.put("hs_L120M01A_ratingFlag_JP", rJP);
		map.put("hs_L120M01A_ratingFlag_AU", rAU);
		map.put("hs_L120M01A_ratingFlag_TH", rTH);
		return map;
	}

	public static void clearL140M01C_rating(L140M01C l140m01c) {
		l140m01c.setC121MainId("");
		l140m01c.setC121RatingId("");
		l140m01c.setC121CustId("");
		l140m01c.setC121DupNo("");
		l140m01c.setGrade("");
	}

	public static Integer getAge(Date dateOfBirth) {
		if (dateOfBirth == null) {
			return null;
		}

		Calendar dob = Calendar.getInstance();
		dob.setTime(dateOfBirth);
		Calendar today = Calendar.getInstance();
		int age = today.get(Calendar.YEAR) - dob.get(Calendar.YEAR);
		if (today.get(Calendar.MONTH) < dob.get(Calendar.MONTH)) {
			age--;
		} else if (today.get(Calendar.MONTH) == dob.get(Calendar.MONTH)
				&& today.get(Calendar.DAY_OF_MONTH) < dob
						.get(Calendar.DAY_OF_MONTH)) {
			age--;
		}

		return age;
	}

	public static Integer getAge(Date dateOfBirth, Date caseDate) {
		if (dateOfBirth == null) {
			return null;
		}
		if (caseDate == null) {
			return null;
		}
		String str_birth = TWNDate.toAD(dateOfBirth);
		String str_case = TWNDate.toAD(caseDate);
		int diff_year = Util.parseInt(StringUtils.substring(str_case, 0, 4)) - Util.parseInt(StringUtils.substring(str_birth, 0, 4));
		
		int birth_md = Util.parseInt(StringUtils.substring(str_birth, 5, 7)+StringUtils.substring(str_birth, 8, 10));
		int case_md = Util.parseInt(StringUtils.substring(str_case, 5, 7)+StringUtils.substring(str_case, 8, 10));
				
		int result = diff_year+(case_md<birth_md?-1:0);
		if(result<0){
			return 0;
		}
		return result;
	}
	
	public static BigDecimal getLnAge(int year, int month) {
		return yearmonth_toYear(get_total_month(year, month));
	}
	
	public static Date get_raw_m1(C120S01A c120s01a) {
		Date r = c120s01a.getBirthday();
		if (CrsUtil.isNull_or_ZeroDate(r)) {
			return null;
		}
		return r;
	}

	private static Integer get_total_month(Integer y, Integer m) {
		Integer raw_a5 = null;
		if (y != null) {
			if (raw_a5 == null) {
				raw_a5 = 0;
			}
			raw_a5 += (y * 12);
		}
		if (m != null) {
			if (raw_a5 == null) {
				raw_a5 = 0;
			}

			raw_a5 += m;
		}
		return raw_a5;
	}

	public static Integer get_raw_a5(C121M01A meta) {
		return get_total_month(meta.getLnYear(), meta.getLnMonth());
	}

	public static Integer get_total_month(L140M01C meta) {
		return get_total_month(meta.getLnYear(), meta.getLnMonth());
	}

	public static BigDecimal yearmonth_toYear(Integer cntMonth) {
		if (cntMonth == null) {
			return null;
		}
		int scale = 2;
		return Arithmetic.div(new BigDecimal(cntMonth), new BigDecimal(12),
				scale);
	}

	/**
	 * 檢核D1_ICR 若填寫"利息保障倍數ICR"=0 勾選"借款目的為開立保證函"=1 未進行ICR分析=2
	 */
	public static String get_item_icr_na(C120S01C c120s01c) {
		if (Util.equals("Y", c120s01c.getIssueLC())) {
			return "1";
		} else {
			if (Util.equals("Y", c120s01c.getICRFlag())) {
				if (c120s01c.getICR() != null) {
					return "0";
				} else {
					return "2";
				}
			} else {
				return "2";
			}
		}
	}

	public static String getBrNameByLocale(IBranch branch, Locale locale) {
		String branchName = "";
		if (branch != null) {
			if (locale != null
					&& Util.equals(Locale.ENGLISH.toString(), locale.toString())) {
				branchName = branch.getEngName();
			} else {
				branchName = branch.getBrName();
			}
		}
		return Util.trim(branchName);
	}

	public static String trimRightPaddingFullEmpty(String custName) {
		String r = Util.trim(custName);
		int size = r.length();
		for (int i = 0; i < size; i++) {
			if (r.endsWith("　")) {
				r = r.substring(0, r.length() - 1);
			}
		}
		return r;
	}

	public static boolean isBranchEditing(String docStatus) {
		Set<String> r = new HashSet<String>();
		r.add(CreditDocStatusEnum.海外_編製中.getCode());
		r.add(CreditDocStatusEnum.海外_待補件.getCode());
		return r.contains(Util.trim(docStatus));
	}

	public static void setRatingKind2(C120M01A c120m01a, String caseId) {
		c120m01a.setCaseId(caseId);
		c120m01a.setRatingDesc("");
		c120m01a.setRatingKind(OverSeaUtil.C120M01A_RATINGKIND_2);
	}

	public static void clearOldColumn(L140M01A l140m01a) {
		l140m01a.setFacilityRating(null);
	}

	/**
	 * 從 0024 抓到的 CNAME 會是全形在 在 CustomerFormHandler 的寫法(非service) 另加工成「半形」
	 * 
	 * @param custId
	 * @param dupNo
	 * @param latestData
	 * @return
	 */
	public static String getCNameFrom0024Map(String custId, String dupNo,
			Map<String, Object> latestData) {
		String cname = (String) latestData.get("CNAME");
		cname = Util.toSemiCharString(cname);
		String ename = (String) latestData.get("ENAME");
		return CustomerIdCheckUtil.getName(custId, cname, ename);
	}

	public static String adjustReason_html(Map<String, String> map) {
		if (map.size() == 0) {
			return "";
		}
		StringBuffer buff = new StringBuffer();
		buff.append("<dl>");
		for (String k : map.keySet()) {
			buff.append("<dt>").append(k).append("</dt>");
			buff.append("<dd>").append(map.get(k)).append("<br/>&nbsp;")
					.append("</dd>");
		}
		buff.append("</dl>");
		return buff.toString();
	}

	public static void set_adjustReason_fmt_result(CapAjaxFormResult result,
			String reasonCnt, String cfmStr,
			Properties prop_abstractOverSeaCLSPage) {
		result.set("adjRsnFmt_cnt", reasonCnt);

		if (LMSUtil.cmpDate(new Date(), "<",
				CapDate.parseDate(OverSeaUtil.CMP_ADJUSTREASON_ONDATE))) {

			result.set("adjRsnFmt_cnt", "0");
		}

		// 升降等提示訊息
		if (true) {
			CapAjaxFormResult cfmObj = new CapAjaxFormResult();
			if (true) {
				cfmObj.set("cfmStr", cfmStr);
				cfmObj.set("cfmY", prop_abstractOverSeaCLSPage
						.getProperty("message.adjustReason_cfm.Y"));
				cfmObj.set("cfmN", prop_abstractOverSeaCLSPage
						.getProperty("message.adjustReason_cfm.N"));
			}
			result.set("adjRsnFmt_cfmObj", cfmObj);
		}

		if (true) {
			CapAjaxFormResult alwaysCfmObj = new CapAjaxFormResult();
			if (true) {
				alwaysCfmObj.set("alwaysCfmStr", prop_abstractOverSeaCLSPage
						.getProperty("message.adjustReason_always"));
				alwaysCfmObj.set("alwaysCfmY", prop_abstractOverSeaCLSPage
						.getProperty("message.adjustReason_always.Y"));
				alwaysCfmObj.set("alwaysCfmN", prop_abstractOverSeaCLSPage
						.getProperty("message.adjustReason_always.N"));
			}
			result.set("adjRsnFmt_alwaysCfmObj", alwaysCfmObj);
		}
	}

	public static String get_jp_j10Desc(Properties prop_abstractOverSeaCLSPage,
			Properties prop_lms1015m01Page, C121M01B c121m01b) {
		String j10Desc = "";
		if (Util.equals("A", c121m01b.getJ10_score_flag())) {
			j10Desc = Util.trim(c121m01b.getJ10_score())
					+ prop_abstractOverSeaCLSPage
							.getProperty("C120S01E._j10_score_flag.A_unit");

		} else if (Util.equals("B", c121m01b.getJ10_score_flag())) {
			j10Desc = prop_lms1015m01Page
					.getProperty("C121M01B.j10_score_flag.B");
		} else if (Util.equals("C", c121m01b.getJ10_score_flag())) {
			j10Desc = prop_lms1015m01Page
					.getProperty("C121M01B.j10_score_flag.C");
		} else if (Util.equals("D", c121m01b.getJ10_score_flag())) {
			j10Desc = prop_lms1015m01Page
					.getProperty("C121M01B.j10_score_flag.D");
		} else if (Util.equals("N", c121m01b.getJ10_score_flag())) {
			j10Desc = prop_lms1015m01Page
					.getProperty("C121M01B.j10_score_flag.N");
		} else if (Util.equals("NA", c121m01b.getJ10_score_flag())) {
			j10Desc = prop_lms1015m01Page
					.getProperty("C121M01B.j10_score_flag.NA");
		}
		return j10Desc;
	}

	public static LinkedHashSet<String> get_jp_nega_serious(
			Properties prop_lms1015m01, C121M01B model) {
		LinkedHashSet<String> propKey_1to5Or9to10 = new LinkedHashSet<String>();
		if (UtilConstants.haveNo.有.equals(model.getChkItem1())) {
			// C121M01B.chkItem1=退票、拒往、信用卡強停及催收呆帳紀錄
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem1"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem2())) {
			// C121M01B.chkItem2=消債條例信用註記、銀行公會債務協商註記及其他補充註記
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem2"));
		}
		
		//J-111-0529 開放3~5項目，若為十足擔保，可於授權內辦理
		/*if (UtilConstants.haveNo.有.equals(model.getChkItem9())) {
			// C121M01B.chkItem9=近12個月授信帳戶出現遲延還款
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem9"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem4())) {
			// C121M01B.chkItem4=近12個月信用卡繳款狀況出現循環信用有延遲2次(含)以上
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem4"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem5())) {
			// C121M01B.chkItem5=近12個月信用卡繳款狀況出現未繳足最低金額2次(含)以上
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem5"));
		}*/
		
		//J-111-0529 開放9~10項目，不檢核
		/*if (UtilConstants.haveNo.有.equals(model.getChkItem12())) {
			// C121M01B.chkItem12=申貸查詢時無擔保授信(扣除學生助學貸款)餘額超過新台幣1,000仟元
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem12"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem13())) {
			// C121M01B.chkItem13=近3個月新業務申請查詢總家數超過3次(含)以上
			propKey_1to5Or9to10.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem13"));
		}*/
		return propKey_1to5Or9to10;
	}

	public static LinkedHashSet<String> get_jp_nega_slight(
			Properties prop_lms1015m01, C121M01B model) {
		LinkedHashSet<String> propKey_6to8 = new LinkedHashSet<String>();
		//J-111-0529 3~5項目，變更於[提醒]若為十足擔保，可於授權內辦理
		if (UtilConstants.haveNo.有.equals(model.getChkItem9())) {
			// C121M01B.chkItem9=近12個月授信帳戶出現遲延還款
			propKey_6to8.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem9"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem4())) {
			// C121M01B.chkItem4=近12個月信用卡繳款狀況出現循環信用有延遲2次(含)以上
			propKey_6to8.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem4"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem5())) {
			// C121M01B.chkItem5=近12個月信用卡繳款狀況出現未繳足最低金額2次(含)以上
			propKey_6to8.add(prop_lms1015m01
					.getProperty("C121M01B.chkItem5"));
		}
		
		if (UtilConstants.haveNo.有.equals(model.getChkItem10())) {
			// C121M01B.chkItem10=近12個月信用卡繳款狀況出現全額逾期未繳2次(含)以上
			propKey_6to8.add(prop_lms1015m01.getProperty("C121M01B.chkItem10"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem7())) {
			// C121M01B.chkItem7=近12個月信用卡出現預借現金餘額2次(含)以上
			propKey_6to8.add(prop_lms1015m01.getProperty("C121M01B.chkItem7"));
		}
		if (UtilConstants.haveNo.有.equals(model.getChkItem11())) {
			// C121M01B.chkItem11=申貸查詢時現金卡有借款餘額(新台幣仟元)
			propKey_6to8.add(prop_lms1015m01.getProperty("C121M01B.chkItem11"));
		}

		return propKey_6to8;
	}

	/**
	 * String[]第1個, 回傳的key String[]第2個, 回傳的col(同一個key有多筆) String[]第3個, 用來
	 * compare 的值
	 * 
	 * @param list
	 * @return
	 */
	public static Map<String, String> get_max_colData(List<String[]> list) {
		Map<String, String> r = new HashMap<String, String>();
		Map<String, TreeMap<String, String>> tmp = new HashMap<String, TreeMap<String, String>>();
		for (String[] row : list) {
			String key = row[0];
			String col = row[1];
			String cmpVal = row[2];
			// ===
			if (!tmp.containsKey(key)) {
				tmp.put(key, new TreeMap<String, String>());
			}
			tmp.get(key).put(cmpVal, col);
		}
		for (String key : tmp.keySet()) {
			TreeMap<String, String> sortData = tmp.get(key);
			String max_cmpVal = sortData.lastKey();
			r.put(key, sortData.get(max_cmpVal));
		}
		return r;
	}

	/**
	 * 在UI、報表上呈現，依順序顯示
	 * 
	 * @param prop_lms1015m01Page
	 * @param type
	 * @param c121m01b
	 * @return
	 */
	public static String get_jp_negaInfoStr(Properties prop_lms1015m01Page,
			int type, C121M01B c121m01b) {

		List<String> negaInfoList = new ArrayList<String>();
		if (true) {
			boolean match1to2 = false;
			boolean match3to8 = false;
			// 第1~5項
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem1())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem1"));
				match1to2 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem2())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem2"));
				match1to2 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem9())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem9"));
				match3to8 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem4())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem4"));
				match3to8 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem5())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem5"));
				match3to8 = true;
			}
			// 第6~8項
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem10())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem10"));
				match3to8 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem7())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem7"));
				match3to8 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem11())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem11"));
				match3to8 = true;
			}
			// 第9~10項
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem12())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem12"));
				//J-111-0529 放寬日本消金檢核，9~10不顯示呈總處相關訊息
//				match1to5_9to10 = true;
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01b.getChkItem13())) {
				negaInfoList.add(prop_lms1015m01Page
						.getProperty("C121M01B.chkItem13"));
				//J-111-0529 放寬日本消金檢核，9~10不顯示呈總處相關訊息
//				match1to5_9to10 = true;
			}
			// =====
			proc_nega_msg(negaInfoList, match1to2, match3to8, type, "");
		}

		return StringUtils.join(negaInfoList, "<br/>");
	}

	public static String color_red(String s) {
		return "<font style='color:#990000;'>" + s + "</font>";
	}

	public static String color_blue(String s) {
		return "<font style='color:#000066;'>" + s + "</font>";
	}

	public static String build_l120m01i_bailout_flag(L120M01I l120m01i){
		if(l120m01i!=null){
			return Util.trim(l120m01i.getBailout_flag1())+Util.trim(l120m01i.getBailout_flag2());
		}
		return "";
	}
	public static void proc_nega_msg(List<String> list, boolean matchMajor,
				boolean matchMinor, int type, String bailout_flag) {	
		/*
		 * 在 GridHandler 會有 @Transient alertMsg 抓到的值,不需再加工
		 */
		if (type == TYPE_RAW) {
			return;
		}

		boolean is_bailout = Util.isNotEmpty(Util.trim(bailout_flag));
		if (matchMajor) {
			String msg = "※應呈報總處核准。";
			if(is_bailout){
				msg+= get_bailout_msg();
			}
			if (type == TYPE_UI) {
				msg = color_red(msg);
			}
			list.add(msg);
		} else {
			if (matchMinor) {
				// 只符合6~8項
				String msg = "※若敘做「<u>非十足擔保</u>」且非「<u>專項貸款案件</u>」時授信案件時，應呈報總處核准。";
				if(is_bailout){
					msg+= get_bailout_msg();
				}
				if (type == TYPE_UI) {
					msg = color_red(msg);
				}
				list.add(msg);
			} else {
				// 都不符合

			}
		}
	}
	
	public static String get_bailout_msg(){
		return "舊貸展延還款或勞工紓困貸款案件，於紓困方案施行期間放寬營業單位主管可在授權內逕行核定。";
	}

	public static String jp_negaInfoEmpty(String msg) {
		if (Util.isEmpty(msg)) {
			return "無聯徵特殊負面資訊";
		}
		return msg;
	}

	public static String prompt_jcic_msg_serious(String custId,
			String custName, Set<String> msgSet) {
		return "<br/>" + "本案關係人" + custId + " " + Util.trim(custName)
				+ "有聯徵特殊負面資訊<br/>" + "[" + StringUtils.join(msgSet, ",")
				+ "]<br/>" + "依本行消金信用評等作業要點第六條應呈報總處核准。";

	}

	public static String prompt_jcic_msg_slight_default(String custId,
			String custName, Set<String> msgSet) {
		return "<br/>" + "本案關係人" + custId + " " + Util.trim(custName)
				+ "有聯徵特殊負面資訊<br/>" + "[" + StringUtils.join(msgSet, ",")
				+ "]<br/>" + "依本行消金信用評等作業要點第六條應呈報總處核准；惟敘做十足擔保授信案件，若經查明且有<br/>"
				+ "文件證明該借款人當時存款帳戶或經常性收入足以償還者，分行得在授權內辦理。";

	}

	public static String prompt_jcic_msg_slight_noGuarantee(String custId,
			String custName, Set<String> msgSet) {
		return "<br/>" + "本案關係人" + custId + " " + Util.trim(custName)
				+ "有聯徵特殊負面資訊<br/>" + "[" + StringUtils.join(msgSet, ",")
				+ "]<br/>" + "且本案非屬十足擔保授信案件，依本行消金信用評等作業要點第六條，應呈報總處核准。";
	}

	public static Set<String> fetch_idDup11(C120M01A c120m01a) {
		Set<String> idDup11Set = new HashSet<String>();
		idDup11Set.add(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(),
				c120m01a.getDupNo()));
		return idDup11Set;
	}

	public static Set<String> fetch_idDup11(List<C120M01A> list) {
		Set<String> idDup11Set = new HashSet<String>();
		for (C120M01A c120m01a : list) {
			idDup11Set.add(LMSUtil.getCustKey_len10custId(c120m01a.getCustId(),
					c120m01a.getDupNo()));
		}
		return idDup11Set;
	}

	public static boolean valid_RepaymentSchDays_LoanTenor(C121M01A meta){
		if(Util.equals("2", meta.getRepaymentSchFmt()) && 
				Util.isNotEmpty(Util.trim(meta.getRepaymentSchDays())) &&
				(meta.getRepaymentSchDays()> Util.parseInt(meta.getLnYear())*366+Util.parseInt(meta.getLnMonth())*31)){
			return false;
		}
		return true;
	}
	
	public static List<String> build_chkItemInfoNcb(
			Properties prop_lms1035m01Page, C121M01D c121m01d) {
		List<String> r = new ArrayList<String>();
		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHG1())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descG")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHG1"));
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHG2())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descG")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHG2"));
			}
		}

		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHS1())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descS")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHS1"));
			}
		}

		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHO3())) {
				r.add(prop_lms1035m01Page
						.getProperty("tab07.note.chkItemInfoNcb.O.Y"));
			}
		}
		if (r.size() == 0) {
			r.add(prop_lms1035m01Page
					.getProperty("tab07.note.chkItemInfoNcb.noWarnMsg"));
		}
		return r;
	}
	
	public static List<String> build_chkItemInfoNcb(
			Properties prop_lms1035m01Page, C121M01H c121m01h) {
		List<String> r = new ArrayList<String>();
		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01h.getChkItemTHG1())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descG")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHG1"));
			}
			if (Util.equals(UtilConstants.haveNo.有, c121m01h.getChkItemTHG2())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descG")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHG2"));
			}
		}

		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01h.getChkItemTHS1())) {
				r.add(prop_lms1035m01Page.getProperty("tab05.descS")
						+ "："
						+ prop_lms1035m01Page
								.getProperty("C121M01D.chkItemTHS1"));
			}
		}

		if (true) {
			if (Util.equals(UtilConstants.haveNo.有, c121m01h.getChkItemTHO3())) {
				r.add(prop_lms1035m01Page
						.getProperty("tab07.note.chkItemInfoNcb.O.Y"));
			}
		}
		if (r.size() == 0) {
			r.add(prop_lms1035m01Page
					.getProperty("tab07.note.chkItemInfoNcb.noWarnMsg"));
		}
		return r;
	}
	
	public static String getMowTypeCountry(String BrId){
		String mowTypeCountry = "";
		if(LMSUtil.get_JP_BRNO_SET().contains(BrId)){
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_日本;
		}else if(LMSUtil.get_AU_BRNO_SET().contains(BrId)){ 
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_澳洲;
		}else if(LMSUtil.get_FR_BRNO_SET().contains(BrId)){
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_法國;
		}else if(LMSUtil.get_CA_BRNO_SET().contains(BrId)){
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_加拿大;
		}else if(LMSUtil.get_TH_BRNO_SET().contains(BrId)){
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_泰國;
		}else if(LMSUtil.get_VN_BRNO_SET().contains(BrId)){
			mowTypeCountry = OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_越南;
		}
		
		return mowTypeCountry;
	}
	
	public static String getMowType(String BrId){
		String mowType = "";
		if(LMSUtil.get_JP_BRNO_SET().contains(BrId)){
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_日本;
		}else if(LMSUtil.get_AU_BRNO_SET().contains(BrId) 
				|| LMSUtil.get_FR_BRNO_SET().contains(BrId)
				|| LMSUtil.get_CA_BRNO_SET().contains(BrId)){ //澳洲、法國、加拿大
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_澳洲;
		}else if(LMSUtil.get_TH_BRNO_SET().contains(BrId)
				|| LMSUtil.get_VN_BRNO_SET().contains(BrId)){
			mowType = OverSeaUtil.C121M01A_MOW_TYPE_泰國;
		}
		
		return mowType;
	}
	

}