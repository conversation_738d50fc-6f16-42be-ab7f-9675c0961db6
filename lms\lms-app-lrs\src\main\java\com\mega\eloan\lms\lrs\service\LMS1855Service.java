/* 
 * LMS1855Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.service;

import java.io.IOException;

import jxl.write.WriteException;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import com.mega.eloan.common.service.AbstractService;

public interface LMS1855Service extends AbstractService {

	/**
	 * 查GRID資料
	 * 
	 * @param class1
	 * @param pageSetting
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	/**
	 * "1"= 逾期未覆審名單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @return
	 * @throws IOException 
	 * @throws WriteException 
	 */
	String findType1ByBrNoAndDate(String brNo, String dataDate,String listName) throws WriteException, IOException;

	/**
	 * "2"= 企金戶未出現於覆審名單
	 * 
	 * @param brNo
	 * @param dataDate
	 * @throws IOException 
	 * @throws WriteException 
	 */
	String findType2ByBrNoAndDate(String brNo, String dataDate,String listName) throws WriteException, IOException;

	/**
	 * "3"= 撥貸後半年內辦理覆審檢核表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @param endDate
	 */
	String findType3ByBrNoAndDate(String brNo, String starDate, String endDate,String listName);

	/**
	 * "4"= 授信覆審明細檢核表
	 * 
	 * @param brNo
	 * @param dataDate
	 * @return
	 * @throws IOException 
	 * @throws WriteException 
	 */
	String findType4ByBrNoAndDate(String brNo, String dataDate,String listName) throws WriteException, IOException;
	
	/**
	 * 刪除一筆資料
	 * 
	 * @param mainId
	 * @param listName
	 */
	void deleteL185m01a(String[] mainIdList, String listName);

}
