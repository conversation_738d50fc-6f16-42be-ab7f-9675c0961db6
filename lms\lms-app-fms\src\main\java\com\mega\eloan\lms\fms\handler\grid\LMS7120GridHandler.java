package com.mega.eloan.lms.fms.handler.grid;

import java.util.List;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.fms.constants.fmsConstants;
import com.mega.eloan.lms.fms.pages.LMS7120M01Page;
import com.mega.eloan.lms.fms.service.LMS7120Service;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.model.L918S01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護Grid Handler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7120gridhandler")
public class LMS7120GridHandler extends AbstractGridHandler {

	@Resource
	LMS7120Service service7120;

	@Resource
	UserInfoService userservice;

	@Resource
	BranchService branchService;

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL712m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addOrderBy("caseDate");
		// 取得文件狀態代碼(交易代碼)
		String docStatus = params.getString("mainDocStatus");
		CreditDocStatusEnum docStatusEnum = CreditDocStatusEnum
				.getEnum(docStatus);
		if (docStatusEnum == null) {
			docStatusEnum = CreditDocStatusEnum.DOC_EDITING;
		}

		switch (docStatusEnum) {
		case 授管處_停權編製中:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權編製中.getCode());
			break;
		case 授管處_停權待覆核:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權待覆核.getCode());
			break;
		case 授管處_停權已覆核:
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					CreditDocStatusEnum.授管處_停權已覆核.getCode());
			break;
		default:
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
			break;
		}

		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = service7120.findPage(L712M01A.class,
				pageSetting);
		List<L712M01A> l712m01as = (List<L712M01A>) page.getContent();
		for (L712M01A model : l712m01as) {
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId((Util.isEmpty(model.getCustId())) ? "N.A."
					: allCust.toString());
			model.setUpdater(!Util.isEmpty(userservice.getUserName(model
					.getUpdater())) ? userservice.getUserName(model
					.getUpdater()) : model.getUpdater());
			// 停權經辦顯示
			if (!Util.isEmpty(model.getStopUpdater())) {
				model.setStopUpdater(!Util.isEmpty(userservice
						.getUserName(model.getStopUpdater())) ? userservice
						.getUserName(model.getStopUpdater()) : Util.trim(model
						.getStopUpdater()));
			} else {
				model.setStopUpdater(getPerName(Util.trim(model
						.getStopUpdater())));
			}
			
			// 停權覆核主管顯示
			if (!Util.isEmpty(model.getStopApprover())) {
				model.setStopApprover(!Util.isEmpty(userservice
						.getUserName(model.getStopApprover())) ? userservice
						.getUserName(model.getStopApprover()) : Util.trim(model
						.getStopApprover()));
			} else {
				model.setStopApprover(getPerName(Util.trim(model
						.getStopApprover())));
			}
			// 分行代碼顯示
			if (Util.isNotEmpty(model.getCaseBrId())) {
				model.setCaseBrId((Util.nullToSpace(model.getCaseBrId()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getCaseBrId())));
			} else {
				model.setCaseBrId("N.A.");
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者名稱
	 */
	private String getPerName(String id) {
		return (!Util.isEmpty(userservice.getUserName(id)) ? userservice
				.getUserName(id) : id);
	}

	/**
	 * 查詢停權明細Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL918s01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS7120M01Page.class);
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("mainId"));
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		// 第三個參數為formatting
		Page<? extends GenericBean> page = service7120.findPage(L918S01A.class,
				pageSetting);

		List<L918S01A> l918s01as = (List<L918S01A>) page.getContent();
		for (L918S01A model : l918s01as) {
			// 統編顯示設定
			StringBuilder allCust = new StringBuilder();
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo());
			model.setCustId((Util.isEmpty(model.getCustId())) ? "N.A."
					: allCust.toString());
			// 分行代碼顯示設定
			if (Util.isNotEmpty(model.getBranchNo())) {
				model.setBranchNo((Util.nullToSpace(model.getBranchNo()))
						+ " "
						+ branchService.getBranchName(Util.nullToSpace(model
								.getBranchNo())));
			} else {
				model.setBranchNo("N.A.");
			}
			// 停權代碼顯示設定
			if (Util.isNotEmpty(model.getSuspendCode())) {
				if (fmsConstants.stopRelease.suspendCode.逾期.equals(Util
						.trim(model.getSuspendCode()))) {
					// subGrid.show5=逾期
					model.setSuspendCode(pop.getProperty("subGrid.show5"));
				} else if (fmsConstants.stopRelease.suspendCode.拒往.equals(Util
						.trim(model.getSuspendCode()))) {
					// subGrid.show6=拒往
					model.setSuspendCode(pop.getProperty("subGrid.show6"));
				} else if (fmsConstants.stopRelease.suspendCode.財異.equals(Util
						.trim(model.getSuspendCode()))) {
					// subGrid.show7=財異
					model.setSuspendCode(pop.getProperty("subGrid.show7"));
				} else if (fmsConstants.stopRelease.suspendCode.退票.equals(Util
						.trim(model.getSuspendCode()))) {
					// subGrid.show8=退票
					model.setSuspendCode(pop.getProperty("subGrid.show8"));
				}
			}

			// 狀態Flag顯示設定
			if (Util.isNotEmpty(model.getStatusFlag())) {
				if (fmsConstants.stopRelease.statusFlag.新增.equals(Util
						.trim(model.getStatusFlag()))) {
					// subGrid.show1=新增
					model.setStatusFlag(pop.getProperty("subGrid.show1"));
				} else if (fmsConstants.stopRelease.statusFlag.修改.equals(Util
						.trim(model.getStatusFlag()))) {
					// subGrid.show2=修改
					model.setStatusFlag(pop.getProperty("subGrid.show2"));
				} else if (fmsConstants.stopRelease.statusFlag.刪除.equals(Util
						.trim(model.getStatusFlag()))) {
					// subGrid.show3=刪除
					model.setStatusFlag(pop.getProperty("subGrid.show3"));
				} else if (fmsConstants.stopRelease.statusFlag.新增後刪除
						.equals(Util.trim(model.getStatusFlag()))) {
					// subGrid.show4=新增後刪除
					model.setStatusFlag(pop.getProperty("subGrid.show4"));
				}
			}

			// 案號顯示設定
			if (Util.isNotEmpty(model.getSetDocNo())) {
				if (Util.trim(model.getSetDocNo()).toUpperCase()
						.contains("LMS")
						|| Util.trim(model.getSetDocNo()).toUpperCase()
								.contains("CLS")) {
					StringBuilder sb = new StringBuilder();
					String setDocNo = Util.trim(model.getSetDocNo());
					int year = LMSUtil.checkSubStr(setDocNo, 0, 3) ? Util
							.parseInt(setDocNo.substring(0, 3)) : 0;
					String brno = LMSUtil.checkSubStr(setDocNo, 3, 6) ? setDocNo
							.substring(3, 6) : null;
					String seqNo = getSeqNo(setDocNo);
					if (year != 0) {
						year += 1911;
					}
					sb.append(year)
							.append(Util.trim(branchService.getBranchName(brno)))
							.append(UtilConstants.Field.兆)
							.append(UtilConstants.Field.授字第)
							.append(Util.toFullCharString(Util
									.addZeroWithValue(Util.trim(seqNo), 5)))
							.append(UtilConstants.Field.號);
					model.setSetDocNo(Util.toSemiCharString(sb.toString()));
				}
			}
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得流水序號
	 * 
	 * @param setDocNo
	 *            完整案號
	 * @return 流水序號
	 */
	private String getSeqNo(String setDocNo) {
		int lmsIndex = setDocNo.toUpperCase().indexOf("LMS");
		int clsIndex = setDocNo.toUpperCase().indexOf("CLS");
		if (lmsIndex != -1) {
			return LMSUtil.checkSubStr(setDocNo, lmsIndex + 3) ? setDocNo
					.substring(lmsIndex + 3) : null;
		} else if (clsIndex != -1) {
			return LMSUtil.checkSubStr(setDocNo, clsIndex + 3) ? setDocNo
					.substring(clsIndex + 3) : null;
		} else {
			return null;
		}
	}
}
