/* 
 *ObsdbELF164ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.sql.Types;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF164Service;

/**
 * <pre>
 * 授信消金利率檔 ELF164
 * </pre>
 * 
 * @since 2012/1/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/3,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF164ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF164Service {

	@Override
	public List<Map<String, Object>> findByBrNo(String BRNID, String brNo) {
		return this.getJdbc(BRNID).queryForList("ELF164.findByBR_NO",
				new Object[] { brNo });
	}

	@Override
	public void insert(String BRNID, List<Object[]> dataList) {
		this.getJdbc(BRNID).batchUpdate(
				"ELF164.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.CHAR }, dataList);
	}

	@Override
	public int insert(String BRNID, String BR_NO, String CONTRACT,
			String CUST_ID, String KIND, String SWFT, String LNAP_CODE,
			String INTRT_TYPE, String INT_KIND, String INT_BASE,
			Double INT_SPREAD, String INT_TYPE, String INTCHG_TYPE,
			String INT_MEMO) {
		return this.getJdbc(BRNID).update(
				"ELF164.insert",
				new Object[] { BR_NO, CONTRACT, CUST_ID, KIND, SWFT, LNAP_CODE,
						INTRT_TYPE, INT_KIND, INT_BASE, INT_SPREAD, INT_TYPE,
						INTCHG_TYPE, INT_MEMO });

	}

}
