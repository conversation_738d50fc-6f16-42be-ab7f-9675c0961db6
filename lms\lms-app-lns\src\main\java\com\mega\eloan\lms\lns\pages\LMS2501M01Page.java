package com.mega.eloan.lms.lns.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.LMSS20APanel;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.lns.panels.LMS2501S01Panel;
import com.mega.eloan.lms.lns.panels.LMS2501S02Panel;
import com.mega.eloan.lms.lns.panels.LMS2501S03Panel;
import com.mega.eloan.lms.model.L250M01A;

/**
 * 企金模擬動審
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/lms/lms2501m01/{page}")
public class LMS2501M01Page extends AbstractEloanForm {

	@Autowired
	LMS2501Service lms2501Service;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));

		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept, CreditDocStatusEnum.海外_待覆核));
		
		renderJsI18N(LMS2501M01Page.class);
		renderJsI18N(LMSS20APanel.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
		model.addAttribute("show_ivr_panel_visible", lms2501Service.getProjClassFromL250M01A(params.getString(EloanConstants.MAIN_OID)));
	}// ;

	// 頁籤
	@SuppressWarnings("unused")
	public Panel getPanel(int index) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS2501S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS2501S02Panel(TAB_CTX, true);
			break;
//		case 3:
//			panel = new LMSS20APanel(TAB_CTX);
//			break;
		case 3:
			panel = new LMS2501S03Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS2501S01Panel(TAB_CTX, true);
			break;
		}
		if (panel == null) {
			panel = new Panel(TAB_CTX);
		}

		return panel;
	}

	public Class<? extends Meta> getDomainClass() {
		return L250M01A.class;
	}

}
