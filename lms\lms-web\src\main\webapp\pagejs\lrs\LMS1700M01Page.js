var initDfd = initDfd || $.Deferred();
var initAll = initAll || $.Deferred();

var _handler = "lms1700m01formhandler";
//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
var grid_p = "grid_printL140M01A";
var grid_c = "grid_printCollSet";
var print_url = "../../simple/FileProcessingService";

pageJsInit(function() {
	$(function(){
		
		var tabForm = $("#tabForm");
		var btnPanel = $("#buttonPanel");
		var initControl_lockDoc = false;
		$.form.init({
			formHandler:_handler, 
			formAction:'query', 
			loadSuccess:function(json){			
				
				// 控制頁面 Read/Write
				if(!$("#buttonPanel").find("#btnSave").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
					json['initControl_lockDoc'] = initControl_lockDoc;
				}			
				
				if(json.page=="01"){
					build_selItem(json.selItem, ".");
					
					//J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
					// J-107-0254_09301_B1001  配合授審處增加「對合作房仲業價金履約保證額度覆審報告表」
					if (json.ctlType == "B" || json.ctlType == "C" ) {
					   $(".showRealRpFg").hide();
					}else{
					   $(".showRealRpFg").show();
					}	
				}
				
				//覆審組不應 click 受檢單位的 btn【EX:輸入洽辦情形、呈主管、覆核】
				var _fromUnitNo = userInfo.ssoUnitNo; 
				if(_fromUnitNo.substring(0,1)=="9" && _fromUnitNo!="900"){
					var disableArr = ["btnSend", "btnBranchAccept"];
					if(json.processingBranchComm=="Y"){
						disableArr.push("btnBranchComm");
					}
					$.each( disableArr, function(idx,btnId){					
						if(btnPanel.find("#"+btnId).is("button")){
							btnPanel.find("#"+btnId).addClass(" ui-state-disabled ").prop("disabled", "true");
						}
	            	});
				}
				if(json.processingBranchComm=="Y"){
					btnPanel.find("#btnOnlyUp412").addClass(" ui-state-disabled ").prop("disabled", "true");
					btnPanel.find("#btnUpdate412").addClass(" ui-state-disabled ").prop("disabled", "true");
				}
				
				if(json.isFromNotes=="Y"){
					btnPanel.find("#btnEnd_to_01A").addClass(" ui-state-disabled ").prop("disabled", "true");				
				}
	
				if(json.hidePaFormPanel=="Y"){
	                $("#book06").hide();
	            }
	
				tabForm.injectData(json);
				initDfd.resolve(json);	
		}});
		
		var $gridAddToL180M01A = $("#gridAddToL180M01A").iGrid({
	        handler: 'lms1700gridhandler',        
	        height: 200,
	        sortname: 'defaultCTLDate|dataDate|',
	        sortorder: "desc|desc",
	        postData: {
	        	mainOid: $("#mainOid").val()
	        	, formAction: "query_L180M01A"
	        },
	        needPager: false,
	        shrinkToFit: true,       
	        colModel: [
	          {
	        	colHeader: i18n.lms1700m01['L180M01A.defaultCTLDate'],//"預計覆審日",
	              name: 'defaultCTLDate',
	              align: "center",
	              width: 80,
	              sortable: true      
	          }, {
	              colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
	              name: 'branchId',
	              width: 100,
	              sortable: true,
	              formatter: 'click'
	          }, {
	            colHeader: i18n.lms1700m01['L180M01A.dataDate'],//"資料年月",
	            name: 'dataDate',
	            align: "center",
	            width: 75,
	            formatter: 'date',
	            formatoptions: {
	                srcformat: 'Y-m-d',
	                newformat: 'Y-m'
	            },
	            sortable: true        
	        }, {
	            colHeader: i18n.lms1700m01['L180M01A.generateDate'],//"名單產生日期",
	            name: 'generateDate',
	            width: 90,
	            sortable: true,
	            align: "center"       
	        }, {
	            colHeader: "mainId", name: 'mainId', hidden: true
	        } ]        
	    });
		
		//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
		$("#"+grid_p).iGrid({
	        handler: 'lms1700gridhandler',        
	        height: 350,
	        postData: {
	        	parStrArr: '',
	            formAction: "queryL140M01A"
	        },
	        needPager: false,        
	        shrinkToFit: false,
	        multiselect: true,        
	        colModel: [
	          {
	            // "借款人統編",
	            colHeader: i18n.lms1700m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
	          }, {
	            // "借款人姓名",
	            colHeader: i18n.lms1700m01['L180M01B.elfCName'],name: 'cName',align: "left",width: 130,sortable: false
	          },{//"類別"
	            colHeader: i18n.lms1700m01['grid.cntrNoType'], name:'rptNoDesc',align: "left",width: 85,sortable: false
	          }, {//"額度序號",
	            colHeader: i18n.lms1700m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 90,sortable: false
	          },{//"案號"
	              colHeader: i18n.lms1700m01['grid.caseNo'], name:'caseNo',align: "left",width: 300,sortable: false  
	          }, { name: 'rptNo', hidden: true}
	          , { name: 'oid', hidden: true}  
	          , { name: 'cntrCustid', hidden: true }
	          , { name: 'cntrDupno', hidden: true }
	        ] 
	    });
		
		//J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
		$("#"+grid_c).iGrid({
	        handler: 'lms1700gridhandler',        
	        height: 350,
	        postData: {
	        	parStrArr: '',
	            formAction: "queryC100m01ByMainid"
	        },
	        needPager: false,     
	        multiselect: true,        
	        colModel: [
			  { // "分行別",
	            colHeader: '分行別',name:'branch',align: "left",width: 50,sortable: false
	          }, {// "借款人統編",
	            colHeader: i18n.lms1700m01['grid.custId'],name:'custId',align: "left",width: 85,sortable: false
	          }, {// "借款人姓名",
	            colHeader: i18n.lms1700m01['L180M01B.elfCName'],name: 'custName',align: "left",width: 130,sortable: false
	          }, {//"擔保品編號"
	            colHeader: '擔保品編號', name:'collNo',align: "left",width: 80,sortable: false
	          }, {//"類別"
	            colHeader: '類別', name:'collTyp1',align: "left",width: 60,sortable: false
	          }, {//"額度序號",
	            colHeader: i18n.lms1700m01['grid.cntrNo'], name:'cntrNo',align: "left",width: 230,sortable: false
	          }, { name: 'mainId', hidden: true}
	        ] 
	    });
		
		$(".phrase_branchComm").click(function(){
			$("#reg_branchComm").val( $(this).text() );
		});
		
		btnPanel.find("#btnSave").click(function(){		
		    //J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			$.ajax({ handler: _handler, type: "POST", dataType: "json",
		        data: {
		            formAction: "getL170mWarnMsg",
					tabForm : $("#tabForm").serializeData(),
		            mainOid: $("#mainOid").val()
		        },
		        success: function(warnJson){
					if(warnJson.warnMsg != ""){
						API.confirmMessage(warnJson.warnMsg , function(result){ 
	                         if (result) {
							    saveAction({'allowIncomplete':'Y'}).done(function(json){
									
									if(json.saveOkFlag){
										
										if(json.IncompleteMsg){
											
											API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
										}else{
											
											API.showMessage(i18n.def.saveSuccess);	
										}	
									}
						        });
							 }	
	    	        	});	            
						
					}else{
						saveAction({'allowIncomplete':'Y'}).done(function(json){
							
							if(json.saveOkFlag){
								
								if(json.IncompleteMsg){
									
									API.showMessage(i18n.def.saveSuccess+"<br/>-------------------<br/>"+json.IncompleteMsg);
								}else{
									
									API.showMessage(i18n.def.saveSuccess);	
								}	
							}
				        });
					}
	            		        	
		        }
		    });
			
		}).end().find("#btnPrint").click(function(){
			if(initControl_lockDoc) {
				printL170M01A( $("#mainOid").val() +"^"+ $("#mainId").val() );
			}else if(responseJSON.mainDocStatus =="060" || responseJSON.mainDocStatus =="050"){
				//已覆核未核定(050)、已覆核已核定(060)，不要儲存，直接列印
		        printL170M01A( $("#mainOid").val() +"^"+ $("#mainId").val() );
			}else{
				
				//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
				$.ajax({ handler: _handler, type: "POST", dataType: "json",
			        data: {
			            formAction: "getL170mWarnMsg",
						tabForm : $("#tabForm").serializeData(),
			            mainOid: $("#mainOid").val()
			        },
			        success: function(warnJson){
						if(warnJson.warnMsg != ""){
							API.confirmMessage(warnJson.warnMsg , function(result){ 
		                         if (result) {
								    saveAction({'allowIncomplete':'Y'}).done(function(json){
										if(json.saveOkFlag){					
											printL170M01A( json.mainOid+"^"+json.mainId );
										}
							        });
								 }	
		    	        	});	            
							
						}else{
							saveAction({'allowIncomplete':'Y'}).done(function(json){
								if(json.saveOkFlag){					
									printL170M01A( json.mainOid+"^"+json.mainId );
								}
					        });
						}
		            		        	
			        }
			    });
				
			}
		}).end().find("#btnMoveToBranch").click(function(){
			saveAction().done(function(json){
				if(json.saveOkFlag){
					//ui_lms1700.msg06=是否移「受檢單位」編製中
					API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg06"], function(result){
			            if (result) {
			            	signContentL170M01G('R').done(function(){
			            		flowAction({'decisionExpr':'to_待覆核_覆審組'});
			            	});
			        	}
			    	});
				}
	        });
		}).end().find("#btnAreaAccept").click(function(){
			//[覆審組]主管-覆核
			var _id = "_div_btnAreaAccept";
			var _form = _id+"_form";
			if ($("#"+_id).length == 0){
				var dyna = [];
				dyna.push("<div id='"+_id+"' style='display:none;' >");
				dyna.push("<form id='"+_form+"'>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms1700m01["ui_lms1700.msg10"]+"</label></p>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms1700m01["ui_lms1700.msg11"]+"</label></p>");
				dyna.push("</form>");
				
				dyna.push("</div>");
				
			     $('body').append(dyna.join(""));
			}
			//clear data
			$("#"+_form).reset();
			
			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
		        title: i18n.def["confirmApprove"],
		        width: 380, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if (!$("#"+_form).valid()) {
	                        return;
	                    }
	                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
	                    if(val=="1"){
	                    	flowAction({'decisionExpr':'to_編製中_分行端'});
	                    }else if(val=="3"){
	                    	flowAction({'decisionExpr':'backto_編製中_覆審組'});
	                    }
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
		    });
		}).end().find("#btnBranchComm").click(function(){
			//受檢單位洽辦情形
			var param = {'mainOid':$("#mainOid").val() };
			
			var $div = $("#divReg_branchComm");		
			//============
			//先清空值, 再填入 抓回的結果		
			$div.find("#reg_branchComm").val("");
			//============
			$.ajax({ type: "POST", handler: _handler
				, data: $.extend( {formAction: "getBranchComm"}, param)
				, success: function(json_branchComm){
					//textarea 設值
					$div.find("#reg_branchComm").val(json_branchComm.reg_branchComm);
					
					$div.thickbox({
	        	        title: $("#btnBranchComm").text(),
	        	        width: 850, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	                    buttons: {
	                        "sure": function(){
	                        	if (!$("#reg_branchComm_form").valid()) {
	                                return;
	                            }                		
	                        	$.ajax({ type: "POST", handler: _handler
	                    			, data:$.extend( {
	                    					formAction: "saveBranchComm",
	                    					'branchComm': $("#reg_branchComm").val()
	                    			  }, param)
	                    			, success: function(json_BranchComm){
	                    				                					
	                    				var tabForm = $("#tabForm");
	                        			tabForm.injectData(json_BranchComm);
	                        	
	                        			$.thickbox.close();
	                    				
	                    			}
	                        	});
	                        	
	                        },
	                        "cancel": function(){
	                        	$.thickbox.close();
	                        }
	                    }
	        	    });    	
	            }
			});
		}).end().find("#btnSend").click(function(){
			//分行[受檢單位]經辦-呈主管
	
			//因分行經辦只能填入 受檢單位洽辦情形
			//沒有 save 的 button
			//所以這裡不 call saveAction
			API.confirmMessage(i18n.def.confirmApply, function(result){
	            if (result) {
	            	check_bef_btnSend().done(function(){            		
	            		signContentL170M01G('E').done(function(){            		
	            			flowAction({'decisionExpr':'呈主管'}); 
	            		}); 
	        		});    	
	        	}
	    	});
		}).end().find("#btnBranchAccept").click(function(){
			//分行[受檢單位]主管-覆核
			var _id = "_div_btnBranchAccept";
			var _form = _id+"_form";
			if ($("#"+_id).length == 0){
				var dyna = [];
				dyna.push("<div id='"+_id+"' style='display:none;' >");
				dyna.push("<form id='"+_form+"'>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms1700m01["ui_lms1700.msg13"]+"</label></p>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms1700m01["ui_lms1700.msg12"]+"</label></p>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms1700m01["ui_lms1700.msg11"]+"</label></p>");
				dyna.push("</form>");
				
				dyna.push("</div>");
				
			     $('body').append(dyna.join(""));
			}
			//clear data
			$("#"+_form).reset();
			
			$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
		        title: i18n.def["confirmApprove"],
		        width: 380,
	            height: 180,
	            align: "center",
	            valign: "bottom",
	            modal: false,
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if (!$("#"+_form).valid()) {
	                        return;
	                    }
	                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
	                    if(val=="1"){
	                    	flowAction({'decisionExpr':'to_已覆核未核定'});
	                    }else if(val=="2"){
	                    	flowAction({'decisionExpr':'backto_編製中_分行端'});
	                    }else if(val=="3"){
	                    	flowAction({'decisionExpr':'backto_編製中_覆審組'});
	                    }
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
		    });
		}).end().find("#btnBack_R_L1").click(function(){
			API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg07"], function(result){
	            if (result) {
	            	flowAction({'decisionExpr':'backto_編製中_覆審組'});    	
	        	}
	    	});	
		}).end().find("#btnBack_E_L1").click(function(){
			API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg06"], function(result){
	            if (result) {
	            	flowAction({'decisionExpr':'backto_編製中_分行端'});    	
	        	}
	    	});
		}).end().find("#btnOnlyUp412").click(function(){
			//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			$.ajax({ handler: _handler, type: "POST", dataType: "json",
		        data: {
		            formAction: "getL170mWarnMsg",
					tabForm : $("#tabForm").serializeData(),
		            mainOid: $("#mainOid").val()
		        },
		        success: function(warnJson){
					if(warnJson.warnMsg != ""){
						API.confirmMessage(warnJson.warnMsg , function(result){ 
	                         if (result) {
							    saveAction().done(function(json){
									if(json.saveOkFlag){
										//ui_lms1700.msg01=是否上傳至覆審控制檔？
										API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg01"], function(result){
								            if(result){
								            	$.ajax({ handler: _handler, type: "POST", dataType: "json",
							            	        data: {
							            	            formAction: "onlyUp412",
							            	            mainOid: $("#mainOid").val()
							            	        },
							            	        success: function(json){
							                        	tabForm.injectData(json);
							                        	//更新 opener 的 Grid
							                            CommonAPI.triggerOpener("gridview", "reloadGrid");
						
							            	        	API.showMessage(i18n.def.runSuccess);		            	        	
							            	        }
							            	    });
								        	}
								    	});
									}
						        });
							 }	
	    	        	});	            
						
					}else{
						saveAction().done(function(json){
							if(json.saveOkFlag){
								//ui_lms1700.msg01=是否上傳至覆審控制檔？
								API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg01"], function(result){
						            if(result){
						            	$.ajax({ handler: _handler, type: "POST", dataType: "json",
					            	        data: {
					            	            formAction: "onlyUp412",
					            	            mainOid: $("#mainOid").val()
					            	        },
					            	        success: function(json){
					                        	tabForm.injectData(json);
					                        	//更新 opener 的 Grid
					                            CommonAPI.triggerOpener("gridview", "reloadGrid");
				
					            	        	API.showMessage(i18n.def.runSuccess);		            	        	
					            	        }
					            	    });
						        	}
						    	});
							}
				        });
					}
	            		        	
		        }
		    });
			
		}).end().find("#btnUpdate412").click(function(){
			//J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
			$.ajax({ handler: _handler, type: "POST", dataType: "json",
		        data: {
		            formAction: "getL170mWarnMsg",
					tabForm : $("#tabForm").serializeData(),
		            mainOid: $("#mainOid").val()
		        },
		        success: function(warnJson){
					if(warnJson.warnMsg != ""){
						API.confirmMessage(warnJson.warnMsg , function(result){ 
	                         if (result) {
							     saveAction().done(function(json){
									if(json.saveOkFlag){
										//ui_lms1700.msg01=是否上傳至覆審控制檔？
										API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg01"], function(result){
								            if(result){
								            	signContentL170M01G('R').done(function(){
									            	flowAction({'decisionExpr':'to_已覆核已核定'});
									            });
								        	}
								    	});
									}
						        });
							 }	
	    	        	});	            
						
					}else{
						saveAction().done(function(json){
							if(json.saveOkFlag){
								//ui_lms1700.msg01=是否上傳至覆審控制檔？
								API.confirmMessage(i18n.lms1700m01["ui_lms1700.msg01"], function(result){
						            if(result){
						            	signContentL170M01G('R').done(function(){
							            	flowAction({'decisionExpr':'to_已覆核已核定'});
							            });
						        	}
						    	});
							}
				        });
					}
	            		        	
		        }
		    });
			
			
		}).end().find("#btnEnd_to_01A").click(function(){
			choose_end_to_01A( $("#is_flowClass_throughBr").val()=="Y" ).done(function(json_choose_end_to_01A){
				//forceFlag
	        	$.ajax({ handler: _handler, data: $.extend({formAction: "end_to_01A"
	        		, 'mainOid': $("#mainOid").val()
	        		, 'forceFlag': 'Y'}
	        		, json_choose_end_to_01A
	        	),success: function(json_end_to_01A){
	    			if(json_end_to_01A.passedFlag=="Y"){
	    				CommonAPI.triggerOpener("gridview", "reloadGrid");
	    				window.close();        				
	    			}           
	            }});   
			});
		}).end().find("#btnAddToL180M01A").click(function(){
			
			$gridAddToL180M01A.trigger("reloadGrid");
			
			$("#div_AddToL180M01A").thickbox({ // 使用選取的內容進行彈窗
		        title: '', width: 410, height: 340, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                	var data = $gridAddToL180M01A.getSingleData();
	                    if (data) {
	                    	API.confirmMessage("是否指定為【"+i18n.lms1700m01['L180M01A.defaultCTLDate']
	                    		+"："+data.defaultCTLDate+"】之覆審名單？", function(result){
	        		            if (result) {
	        		            	$.ajax({ handler: _handler, data: {
	                            		formAction: "save_pid"
	                           			, 'mainOid':$("#mainOid").val()
	                           			, 'pid': data.mainId}
	                            	,success: function(json_save_pid){
	                            		if(json_save_pid.pidOkFlag=="Y"){
	                            			$("#btnAddToL180M01A").hide();
	                            			$.thickbox.close();	
	                            		}                            		           
	                                }});
	        		        	}
	        		    	});
	                    }
	                },
	                "cancel": function(){
	                    $.thickbox.close();
	                }
	            }
		    });
		}).end().find("#btnExit").click(function(){
			$(".msgContent").closest(".TB_window").find("button").first().focus();
		}).end().find("#btnLoadLms8000v04").click(function(){
		    getLastRetrialDate().done(function(result){
		    	$.ajax({
	                type: "POST",
	                handler: _handler,
	                data:{
	                    formAction: "queryPostLoanList",
	                    mainId: responseJSON.mainId
	                },
	                success: function(json){
	        	        $("#div_LoadLms8000v04").load("../../fms/lms8000v04", function () {
							setTimeout(function() {
		                        $("#openFrom").val("lrs");
		                        $("#filterForm").find(".lrsHide").hide();
		                        $("#filterForm").find(".lrsShow").show();
		                        $("#filterForm").find("#custId").val($("#custId").val()).attr("readonly", true);
		                        $("#filterForm").find("#dupNo").val($("#dupNo").val()).attr("readonly", true);
		                        if(result.lastRetrialDate != "" && result.lastRetrialDate!= "0001-01-01"){
		                            $("#filterForm").find("#startDate").val(result.lastRetrialDate);
		                        }
								var jcntrnoList = $("#filterForm").find("#cntrnoList");
		                        jcntrnoList.setItems({
		        					item: json.cntrnoList,
		        					space: false
		        				});
							}, 500);
	                    });
	                }
	            });
		    });
		}).end().find("#btnPrintLatestL140M01A").click(function(){
			//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
			printLatestL140M01A($("#mainOid").val() );
	    }).end().find("#btnPrintCollSet").click(function(){
			//J-111-0554 配合授審處增進管理效益，修改相關功能程式
			printCollSet($("#mainOid").val() );
	    });
	
	    function getLastRetrialDate(){
			var my_dfd = $.Deferred();
			var lastRetrialDate = "";
	        if(responseJSON.page == "01"){
	            lastRetrialDate = $("#lastRetrialDate").val();
	            my_dfd.resolve( {'lastRetrialDate':lastRetrialDate} );
	        } else {
	            $.ajax({
	                type: "POST",
	                handler: _handler,
	                data:{
	                    formAction: "queryLastRetrialDate",
	                    mainId: responseJSON.mainId
	                },
	                success: function(json){
	                    lastRetrialDate = json.lastRetrialDate;
	                    my_dfd.resolve( {'lastRetrialDate':lastRetrialDate} );
	                }
	            });
	        }
			return my_dfd.promise();
		}
		$("#sign_cntE").change(function(){
	        var $target = $("#new_sign_E_L4Span");
	        //清空原本的
	        $target.empty();
	        
	        var newdiv = "";
	        var val = parseInt($(this).val(), 10);
	        if (val > 1) {
	            for (var i = 2, count = val + 1; i < count; i++) {
	            	var idName = "sign_E_L4_"+i;
	            	
	                newdiv+=  
	                ("<div>" 
	                + i18n.lms1700m01['label.signSeqDescPrefix'] + i +i18n.lms1700m01['label.signSeqDescPostfix']
	                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_E_L4'/>&nbsp;"
	                +"</div>");
	            }
	            $target.append(newdiv);
	            
	            var copyFrom = $("#sign_E_L4_1").html();//get srcElm html content
	            $(".selectSign_E_L4").html(copyFrom);
	        }
	    });
		$("#sign_cntR").change(function(){
	        var $target = $("#new_sign_R_L4Span");
	        //清空原本的
	        $target.empty();
	        
	        var newdiv = "";
	        var val = parseInt($(this).val(), 10);
	        if (val > 1) {
	            for (var i = 2, count = val + 1; i < count; i++) {
	            	var idName = "sign_R_L4_"+i;
	            	
	                newdiv+=  
	                ("<div>" 
	                + i18n.lms1700m01['label.signSeqDescPrefix'] + i +i18n.lms1700m01['label.signSeqDescPostfix']
	                +"&nbsp;&nbsp;&nbsp;<select id='"+idName+"' name='"+idName+"' class='selectSign_R_L4'/>&nbsp;"
	                +"</div>");
	            }
	            $target.append(newdiv);
	            
	            var copyFrom = $("#sign_R_L4_1").html();//get srcElm html content
	            $(".selectSign_R_L4").html(copyFrom);
	        }
	    });
		
		var flowAction = function(opts){
			return $.ajax({
	            type: "POST",
	            handler: _handler, action: "flowAction",
	            data:$.extend( {
	            	mainOid: $("#mainOid").val(), 
	            	mainDocStatus: $("#mainDocStatus").val() 
	                }
	                , ( opts||{} )
	            ),                
	            success: function(json){            	
	            	API.triggerOpener();//gridview.reloadGrid 
	            	window.close();            	
	            }
	        });
		}
		
		var choose_end_to_01A = function (isThrougrBr){
			var my_dfd = $.Deferred();
			//ui_lms1700.msg11=退回「覆審單位」覆審人員 
			//ui_lms1700.msg12=退回「受檢單位」經辦
			var _id = "_div_choose_end_to_01A";
			var _form = _id+"_form";
					
			if ($("#"+_id).length == 0){
				var dyna = [];
				dyna.push("<div id='"+_id+"' style='display:none;' >");
				dyna.push("<form id='"+_form+"'>");
				dyna.push("<p><label><input type='radio' name='decisionExpr' value='2' class='required' />"+i18n.lms1700m01['ui_lms1700.msg11']+"</label></p>");
				if(isThrougrBr){
					dyna.push("<p><label><input type='radio' name='decisionExpr' value='1' class='required' />"+i18n.lms1700m01['ui_lms1700.msg12']+"</label></p>");
					/*
	                // 2022/04/18 授審處連喬凱來電  分處對覆審報告表有回頭打考評表之需求
	                // 1. 「已覆核已核定」之覆審報告表可以退為「已覆核未核定」修改_限制為有考評表且有傳送至分行的覆審報告表
	                // 2. 不限上傳者本人，任何人都可以修改
	                */
	                dyna.push("<p><label><input type='radio' name='decisionExpr' value='3' class='required' />"+i18n.lms1700m01['ui_lms1700.msg39']+"</label></p>");
				}
				dyna.push("</form>");				
				dyna.push("</div>");
				
			     $('body').append(dyna.join(""));
			}
			//clear data
			$("#"+_form).reset();
			
			$("#"+_id).thickbox({
		       title: '', width: 280, height: 180, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	           buttons: {
	               "sure": function(){
	            	   
	                   if (!$("#"+_form).valid()) {
	                       return;
	                   }
	                   var decisionExpr = $("#"+_form).find("[name='decisionExpr']:checked").val();
	                   if(decisionExpr=='2'){
	                	   my_dfd.resolve({});
	                   }else if(decisionExpr=='1'){
	                	   my_dfd.resolve({'targetDocStatus':'010'});
	                   }else if(decisionExpr=='3'){
	                       my_dfd.resolve({'targetDocStatus':'050'});
	                   }else{
	                	   my_dfd.reject();   
	                   }	
	                   $.thickbox.close();
	               },
	               "cancel": function(){
	            	   my_dfd.reject();
	            	   $.thickbox.close();
	               }
	           }   
	        });		
			return my_dfd.promise();
		}
		
		var check_bef_btnSend = function (){
			return $.ajax({
	            type: "POST",
	            handler: _handler,
	            data:{
	            	formAction: "check_bef_btnSend",
	            	'mainOid':$("#mainOid").val()
	            },                
	            success: function(){
	            }
	        });
		}
		var signContentL170M01G = function (signRole){
			var param = {'mainOid':$("#mainOid").val(), 'signRole': signRole};
			var my_dfd = $.Deferred();
			
			$.ajax({ type: "POST", handler: _handler
				, data: $.extend( {formAction: "getSignList"}, param)
				, success: function(json_signContent){
					if(json_signContent.canSkip && json_signContent.canSkip=="Y"){
						my_dfd.resolve();
					}else{
						//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
						var condL2 = null;
						var condL4 = null;
						var condL5 = null;
						var $div = null;
						var tb_title = "";
						if(signRole=="E"){
							//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
							condL2 = ".selectSign_E_L2";  //帳戶管理員
							condL4 = ".selectSign_E_L4"; 
							condL5 = ".selectSign_E_L5"; 
							$div = $("#divSignContent_E"); 
							tb_title = i18n.lms1700m01["reviewBrn.1"];//受檢單位
						}else if(signRole=="R"){
							condL4 = ".selectSign_R_L4"; 
							condL5 = ".selectSign_R_L5"; 
							$div = $("#divSignContent_R"); 
							tb_title = i18n.lms1700m01["reviewBrn.2"];//覆審單位
						}
						//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
						if(signRole=="E"){
							var jcondL2 = $(condL2);
							jcondL2.setItems({ item: json_signContent.l2_list, space: true });
						}
						
						var jcondL4 = $(condL4);
						jcondL4.setItems({ item: json_signContent.l4_list, space: true });
						var jcondL5 = $(condL5);
						jcondL5.setItems({ item: json_signContent.l5_list, space: true });
		            	//=========
						$div.thickbox({
		        	        title: i18n.lms1700m01["label.signature"]+"("+tb_title+")",
		        	        width: 580, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
		                    buttons: {
		                        "sure": function(){
		                        	var l4Arr = [];
		                        	var l5Arr = [];
		                        	//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		                        	var l2Arr = [];   
		                        	
		                        	$.each( $(condL4), function(idx,obj){
		                        		var val = $(obj).val();                        	
		                        		l4Arr.push( val );
		                        	});
		                        	$.each( $(condL5), function(idx,obj){
		                        		var val = $(obj).val();                        		
		                        		l5Arr.push( val );
		                        	});
		                        	
		                        	//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		                        	if(signRole=="E"){
		                        		$.each( $(condL2), function(idx,obj){
			                        		var val = $(obj).val();                        		
			                        		l2Arr.push( val );
			                        	});
		                        	}
		                        	
		                        	                  
		                        	//J-111-0033_05097_B1001 Web e-Loan 企金覆審報告表(一般、含土建融、實地，分行自辦用及授管中心用)皆新曾帳戶管理員用印欄位
		                        	$.ajax({ type: "POST", handler: _handler
		                    			, data:$.extend( {
		                    					formAction: "saveSignList"
		                    					,'l4Arr':l4Arr.join("|") 
		                    					,'l5Arr':l5Arr.join("|")
		                    					,'l2Arr': ( signRole=="E" ? l2Arr.join("|") : "" )
		                    			  }, param)
		                    			, success: function(json_saveSignList){
		                    				if(json_saveSignList.saveSignFlag=="Y"){                    					
		                    					var tabForm = $("#tabForm");
		                        				tabForm.injectData(json_saveSignList);
		                        				
		                        				my_dfd.resolve();
		                        				$.thickbox.close();
		                    				}
		                    			}
		                        	});
		                        	
		                        },
		                        "cancel": function(){
		                        	my_dfd.reject();
		                        	$.thickbox.close();
		                        }
		                    }
		        	    });   
					}
	            }
			});
			return my_dfd.promise();
		}
		
	})
});

function build_selItem(json_selItem, sep){
	$.each(json_selItem, function(itemName, kvMap) {
		var chooseItem = $("#"+itemName);
		var _addSpace = false;
		if(chooseItem.prop("space")=="true"){
			_addSpace = true;
		}
		
		var _fmt = "{key}";
		if(chooseItem.prop("myShowKey")==="Y"){
			_fmt = "{value}"+sep+"{key}";
		}
		chooseItem.setItems({ item: json_selItem[itemName], format: _fmt, clear:false, space: true, disabled: chooseItem.prop("disabled")});

//		$.each(json_selItemOrder[itemName], function(idx, kVal) {
//			var currobj = {};
//    		currobj[kVal] = kvMap[kVal];
//
//    		chooseItem.setItems({ item: currobj, format: _fmt, clear:false, space: (_addSpace?(idx==0):false) });
//		});
	});
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){ // return false or true		
		return $("#tabForm").valid();
	},sendData:function(){ // 需上送之資料集合(Map<String,String>)
		return $("#tabForm").serializeData();
	}
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                ( opts||{} )
            ),                
            success: function(json){
            	tabForm.injectData(json);
            	if(json.page=="01"){
            		hs_s01_CreditMowFcrdType();
            		hs_s01_freeG_freeC();
            	} else if(json.page=="06"){
                    var paForm = $('#L170M01JForm').find("#paForm");
                    paForm.injectData(json);
            	}

            	getL170mTipsMsg();

            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	}else{
		return $.Deferred();
	}
}

function getL170mTipsMsg(){
    $.ajax({
        type: "POST",
        handler: _handler,
        data:{
            formAction: "getL170mTipsMsg",
            mainOid: responseJSON.mainOid
        },
        success: function(json){
            if(json.tipsMsg != ""){
                API.showMessage(json.tipsMsg);
            }
        }
    });
}

/**
 * 同時印多份: 
 * oid_arr.push(data.oid+"^"+data.mainId);
 * rptOid: oid_arr.join("|")
 */
function printL170M01A(rptOid){
	$.form.submit({
        url: "../../simple/FileProcessingService",
        target: "_blank",
        data: {
            'rptOid': rptOid,
            'fileDownloadName': "lms1700r01.pdf",
            serviceName: "lms1700r01rptservice"            
        }
    });
}
function hs_s01_freeG_freeC(){
	hs_freeG();
	hs_freeC()
}
function hs_freeG(){
	var val = $("[name='freeG']:checked").val();
	if(val=="Y"){
		$("#div_M01H_G_FREE").show();
		$("#div_M01H_G_SYS").hide();
	}else{
		$("#div_M01H_G_FREE").hide();
		$("#div_M01H_G_SYS").show();
	}
}
function hs_freeC(){
	var val = $("[name='freeC']:checked").val();
	if(val=="Y"){
		$("#div_M01H_C_FREE").show();
		$("#div_M01H_C_SYS").hide();
	}else{
		$("#div_M01H_C_FREE").hide();
		$("#div_M01H_C_SYS").show();
	}
}
function clear_FcrdGrad(){
	$("#FcrdGrad").val("");
}

function hs_s01_CreditMowFcrdType(){
	hs_CreditType();
	hs_MowType();
	hs_FcrdType();
	hs_s01_Type($("#exCreditType"), $("#exCreditGrade"));
	hs_s01_Type($("#exMowType"), $("#exMowGrade"));
	hs_s01_Type("", "");
}
function hs_CreditType(){
	var val = $("#CreditType").val();
	var $target = $("#CreditGrade");
	if(val=="" || val=="NA"){
		$target.val("");
		$target.hide();
	}else{
		$target.show();
	}
}

function hs_MowType(){
	var val = $("#MowType").val();
	var $target = $("#MowGrade");
	if(val=="" || val=="NO"){
		$target.val("");
		$target.hide();
	}else{
		$target.show();
	}
}

function hs_FcrdType(){
	var val = $("#FcrdType").val();
	var $target = $(".tr_fcrd");
	if(val==""){
		$target.find("#FcrdArea").val("");
		$target.find("#FcrdPred").val("");
		clear_FcrdGrad();
		
		$target.hide();
	}else{
		$target.show();
	}
}

function hs_s01_Type(v,t){
	if(v==""){
		v=$("#exFcrdType");
		t=$(".tr_exfcrd");
	}
	var val = v.val();
	var $target = t;

	if (v == "") {
		if(val==""){
			$target.find("#exFcrdArea").val("");
			$target.find("#exFcrdPred").val("");
			$("#exFcrdGrad").val("");
			
			$target.hide();
		}else{
			$target.show();
		}
	} else {
		if (val == "" || val == "XC" || val == "XM" 
				|| val == "NA" || val == "NO") {
			$target.val("");
			$target.hide();
		}
		else {
			$target.show();
		}
	}
}

//J-110-0505_05097_B1001 Web e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
function printLatestL140M01A(oids){
	$.ajax({ type: "POST", handler: _handler, data:{ formAction: "getPrintL140M01AParam", 'oids': oids},
        success: function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_p).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printL140M01A").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 700, height: 500, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
         	           buttons: {
         	               "列印": function(){
         	            	  var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_p).getRowData(rowId_arr[i]);
         	         			oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo );    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'rptOid': oid_arr.join("|"),
         	                         'fileDownloadName': "cntrNo.pdf",
         	                         serviceName: "lms1201r01rptservice"            
         	                     }
         	            	  });  	                  	
         	               },
         	               "列印並加入附加檔案": function(){
         	            	  var rowId_arr = $("#"+grid_p).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_p).getRowData(rowId_arr[i]);
         	         			var saveFile = "mainId:"+responseJSON.mainId+";"+"fileId:lrs"+";"+"class:L170M01A"+";"+"brNo:" + userInfo.unitNo+";"+"rpa:N";
         	         			oid_arr.push(data.rptNo+"^"+data.oid+"^"+data.cntrCustid+"^"+data.cntrDupno+"^"+data.cntrNo);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'rptOid': oid_arr.join("|"),
         	                         'fileDownloadName': "cntrNo.pdf",
         	                         createFile:true,
         	                         saveFile: saveFile,
         	                         serviceName: "lms1201r01rptservice"            
         	                     }
         	            	  });  	           
         	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
        }
    });
}

//J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
function printCollSet(oids){
	$.ajax({ 
		type: "POST", 
		handler: _handler, 
		data:{ 
				formAction: "getPrintCollSet"
		        , 'oids': oids
		     },
        success: function(json){
        	var my_dfd = $.Deferred();    	
        	if(json.notProc){
        		API.showPopMessage("", json.notProc,function(){
        			my_dfd.resolve();
  				});
        	}else{
        		my_dfd.resolve();
        	}

        	my_dfd.done(function(){
        		if(json.parStr){
            		$("#"+grid_c).jqGrid("setGridParam", {
                        postData: {
                        	parStrArr:json.parStr
                        },
                        search: true
                    }).trigger("reloadGrid");
            		
            		$("#div_printCollSet").thickbox({ // 使用選取的內容進行彈窗
         		       title: "", width: 700, height: 500, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
         	           buttons: {
         	               "列印": function(){
         	            	  var rowId_arr = $("#"+grid_c).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_c).getRowData(rowId_arr[i]);
         	         			oid_arr.push(data.mainId);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'mainIds': oid_arr.join("|"),
         	                         'fileDownloadName': "collSet.pdf",
         	                         serviceName: "lmscallcmsr01rptservice"            
         	                     }
         	            	  });  	                  	
         	               },
         	               "列印並加入附加檔案": function(){
         	            	  var rowId_arr = $("#"+grid_c).getGridParam('selarrrow');
         	            	  var oid_arr = [];
         	            	 	for (var i = 0; i < rowId_arr.length; i++) {
         	         			var data = $("#"+grid_c).getRowData(rowId_arr[i]);
         	         			var saveFile = "mainId:"+responseJSON.mainId+";"+"fileId:lrs"+";"+"class:L170M01A"+";"+"brNo:" + userInfo.unitNo+";"+"rpa:N";
         	         			oid_arr.push(data.mainId);    			
         	            	  }
         	            	  if(oid_arr.length==0){   	 		
    	                	 	API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
    	             	 		return;
    	             	 	  }	
         	            	 
         	            	  $.form.submit({
         	                     url: print_url,
         	                     target: "_blank",
         	                     data: {
         	                         'mainIds': oid_arr.join("|"),
         	                         'fileDownloadName': "collSet.pdf",
         	                         createFile:true,
         	                         saveFile: saveFile,
         	                         serviceName: "lmscallcmsr01rptservice"            
         	                     }
         	            	  });  	           
         	               },
         	               "close": function(){
         	            	   $.thickbox.close();
         	               }
         	           }
            		});        		
            	}
        	});
    	 }
    });
}
