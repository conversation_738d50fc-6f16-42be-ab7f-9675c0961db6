#---------------------------------------------#
# javascript commom.js use
#---------------------------------------------#
comboSpace=--\u8acb\u9078\u64c7--
yes=\u662f
no=\u5426
all=\u5168\u90e8
close=\u95dc\u9589
cancel=\u53d6\u6d88
include=\u5f15\u7528
noData=\u67e5\u7121\u8cc7\u6599\uff0c\u8acb\u91cd\u65b0\u67e5\u8a62\u3002
timeout=\u4f3a\u670d\u5668\u9023\u7dda\u903e\u6642\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66\u3002
connectError=\u4f3a\u670d\u5668\u9023\u7dda\u5931\u6557\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66\uff0c\u6216\u6aa2\u67e5\u7db2\u8def\u662f\u5426\u6709\u554f\u984c\u3002
sessionTimeout=\u60a8\u7684\u767b\u5165\u5df1\u7d93\u904e\u671f\n\u8acb\u7531\u54e1\u5de5\u5c08\u7528\u7db2\u767b\u5165
loading=\u9060\u7aef\u7cfb\u7d71\u300a\u9023\u7dda\u4e2d\u300b\uff0c\u8acb\u7a0d\u5f85
lastDBMonidyTime=\u4e3b\u6a5f\u8cc7\u6599\u5eab\u6700\u5f8c\u7570\u52d5\u4eba\u54e1
sure=\u78ba\u5b9a
enter=\u8f38\u5165
compID=\u7d71\u4e00\u7de8\u865f
megaID=\u7d71\u4e00\u7de8\u865f
dupNo=\u91cd\u8986\u5e8f\u865f
name=\u59d3\u540d
compName=\u59d3\u540d/\u540d\u7a31
query=\u67e5\u8a62
look=\u8abf\u95b1
reQuery=\u91cd\u65b0\u5f15\u9032
reModify=\u91cd\u65b0\u7de8\u8f2f
import=\u5f15\u9032
calculate=\u8a08\u7b97
del=\u522a\u9664
print=\u5217\u5370
deleted=\u6b64\u6587\u4ef6\u5df2\u8a2d\u5b9a\u70ba\u522a\u9664\u4e26\u4e14\u4e3b\u7ba1\u9000\u56de\u4e0d\u53ef\u57f7\u884c\u4efb\u4f55\u52d5\u4f5c\uff0c\u8acb\u56de\u4e3b\u756b\u9762\u91cd\u65b0\u67e5\u8a62\u6216\u65b0\u589e\u6848\u4ef6\u3002
newData=\u65b0\u589e
newCustomer=\u65b0\u5ba2\u6236
selectOption=\u9078\u9805
saveData=\u5132\u5b58
accept=\u6838\u5b9a
return=\u9000\u56de
saveSuccess=\u5132\u5b58\u6210\u529f
txnSuccess=\u4ea4\u6613\u6210\u529f!
runSuccess=\u57f7\u884c\u6210\u529f
addSuccess=\u65b0\u589e\u6210\u529f
saveDataClose=\u5132\u5b58\u5f8c\u96e2\u958b
CloseWithoutSave=\u4e0d\u5132\u5b58\u96e2\u958b
confirmSaveLeave=\u662f\u5426\u78ba\u5b9a\u5132\u5b58\u5f8c\u96e2\u958b?
closewindows=\u300c\u95dc\u9589\u300d \u6216 \u300c\u91cd\u6574\u300d \u5c07\u6703\u907a\u5931\u76ee\u524d\u7de8\u8f2f\u4e2d\u7684\u8cc7\u6599
count=\u5e8f\u865f
action=\u7570\u52d5\u6a21\u5f0f
actoin_001=\u662f\u5426\u57f7\u884c\u6b64\u52d5\u4f5c?
action_002=\u8acb\u5148\u9078\u64c7\u9700\u300c\u4fee\u6539/\u522a\u9664\u300d\u4e4b\u8cc7\u6599\u5217\u3002
action_003=\u662f\u5426\u78ba\u5b9a\u300c\u522a\u9664\u300d\u6b64\u7b46\u8cc7\u6599?
action_004=\u8acb\u5148\u9078\u64c7\u9700\u300c\u8abf\u95b1\u300d\u4e4b\u8cc7\u6599\u5217
action_005=\u8acb\u5148\u9078\u53d6\u4e00\u7b46\u4ee5\u4e0a\u4e4b\u8cc7\u6599\u5217
action_006=\u8acb\u5148\u9078\u64c7\u9700\u300c\u5217\u5370\u300d\u4e4b\u8cc7\u6599\u5217
confirmApply=\u662f\u5426\u78ba\u5b9a\u5448\u4e3b\u7ba1\u8986\u6838?
confirmApply2=\u662f\u5426\u78ba\u5b9a\u5448\u4e3b\u7ba1\u8986\u6838?
confirmApprove=\u662f\u5426\u78ba\u5b9a\u8986\u6838?
confirmApprove2=\u662f\u5426\u8981\u5448\u9001?
confirmApprove3=\u662f\u5426\u78ba\u5b9a\u6838\u51c6?
confirmReturn=\u662f\u5426\u78ba\u5b9a\u9000\u56de?
confirmReject=\u662f\u5426\u78ba\u5b9a\u62d2\u7d55?
confirmDelete=\u662f\u5426\u78ba\u5b9a\u522a\u9664?
confirmCopy=\u662f\u5426\u78ba\u5b9a\u8907\u88fd?
confirmSend=\u662f\u5426\u78ba\u8a8d\u9001\u51fa?
confirmRun=\u662f\u5426\u78ba\u5b9a\u57f7\u884c\u6b64\u529f\u80fd?
confirmCk=\u5167\u5bb9\u5df2\u7570\u52d5\uff0c\u662f\u5426\u9700\u8981\u5132\u5b58?
confirmApplySuccess=\u8986\u6838\u6210\u529f
confirmApplySuccess1=\u6838\u51c6\u6210\u529f
confirmApplySuccess2=\u5448\u9001\u6210\u529f
confirmReturnSuccess=\u9000\u56de\u6210\u529f
confirmDeleteSuccess=\u522a\u9664\u6210\u529f
confirmCopySuccess=\u8907\u88fd\u6210\u529f
confirmApplySuccess3=\u5448\u4e3b\u7ba1\u8986\u6838\u6210\u529f
confirmRejectSuccess=\u5df2\u62d2\u7d55
confirmDeliverSuccess=\u50b3\u9001\u6210\u529f
confirmContinueRun=\u8cc7\u6599\u5df2\u7570\u52d5\uff0c\u5c1a\u672a\u5132\u5b58\uff0c\u662f\u5426\u7e7c\u7e8c\u57f7\u884c?
confirmTitle=\u63d0\u793a
id_reason=\u67e5\u8a62\u7406\u7531
grid_selector=\u8acb\u9078\u64c7\u8cc7\u6599
#J-106-0029-003  \u6d17\u9322\u9632\u5236-\u65b0\u589e\u5be6\u8cea\u53d7\u76ca\u4eba
confirmBeforeDeleteAll=\u57f7\u884c\u6642\u6703\u522a\u9664\u5df2\u5b58\u5728\u4e4b\u8cc7\u6599\uff0c\u662f\u5426\u78ba\u5b9a\u57f7\u884c\uff1f
#J-107-0390_05097_B1001 \u5206\u884c\u6b0a\u9650\u4e4b\u6388\u4fe1\u6848\u4ef6\u82e5\u65bc\u8986\u6838\u5f8c\u6b32\u4fee\u6539,\u5f97\u6388\u6b0a\u4e3b\u7ba1\u5f97\u9000\u56de\u81f3\u7de8\u88fd\u4e2d
confirmBackApprove=\u9000\u56de\u5f8c\u6848\u4ef6\u5fc5\u9808\u91cd\u65b0\u8986\u6838\uff0c\u7121\u6cd5\u76f4\u63a5\u5fa9\u539f\u70ba\u5df2\u6838\u51c6\u72c0\u614b\uff0c\u662f\u5426\u78ba\u5b9a\u9000\u56de\u5df2\u6838\u51c6\u6848\u4ef6?

#(\u96d9\u64ca\u6ed1\u9f20\u5de6\u9375\u5f15\u5165)
TMInsert=\u662f\u5426\u65b0\u589e\u6b64\u7b46\u8cc7\u6599?
TMModify=\u662f\u5426\u78ba\u5b9a\u4fee\u6539\u6b64\u7b46\u8cc7\u6599?
TMDelete=\u662f\u5426\u78ba\u5b9a\u522a\u9664\u6b64\u7b46\u8cc7\u6599?
TMMDeleteError=\u8acb\u5148\u9078\u64c7\u9700\u4fee\u6539(\u522a\u9664)\u4e4b\u8cc7\u6599\u5217
TMNoChangeModify=\u4e26\u672a\u66f4\u52d5\u4efb\u4f55\u8cc7\u6599\uff0c\u7121\u9700\u4fee\u6539\u8cc7\u6599!
fileSelect=\u8acb\u5148\u9078\u64c7\u6a94\u6848
fileSelError=\u8acb\u4f7f\u7528\u6b63\u78ba\u6a94\u6848,\u526f\u6a94\u540d\u70ba
#fileXlsError=\u8acb\u4f7f\u7528*.xls\u6a94
fileUploadError=\u6a94\u6848\u8b80\u53d6\u5931\u6557\uff0c\u8acb\u91cd\u65b0\u4e0a\u50b3
fileUploading=\u6a94\u6848\u8b80\u53d6\u4e2d\u8acb\u8010\u5fc3\u7b49\u5019\u2026
fileUploadSuccess=\u6a94\u6848\u4e0a\u50b3\u5b8c\u6210!
attachfile=\u9644\u52a0\u6a94\u6848
insertfile=\u8acb\u9078\u64c7\u9644\u52a0\u6a94\u6848
insertfileSize=\u76ee\u524d\u53ef\u4e0a\u50b3\u6a94\u6848\u5927\u5c0f\u70ba$\{fileSize\}M
#id_dcTitle=(\u8acb\u96d9\u64ca\u6240\u9700\u4e4b\u9078\u9805)
#id_error=\u7d71\u4e00\u7de8\u865f\u6aa2\u6838\u932f\u8aa4
lastModifyBy=\u7de8\u88fd\u4eba\u54e1
lastModifyRole=\u7de8\u88fd\u4eba\u54e1\u7fa4\u7d44
lastModifyName=\u7de8\u88fd\u4eba\u54e1
lastModifyTime=\u6700\u5f8c\u66f4\u65b0\u65e5\u671f
lastUpdater=\u6700\u5f8c\u7570\u52d5\u4eba\u54e1
lastUpdateTime=\u6700\u5f8c\u7570\u52d5\u6642\u9593
actionType=\u6848\u4ef6\u72c0\u614b
createTime=\u5efa\u7acb\u65e5\u671f
tabchange=\u9801\u9762\u8cc7\u6599\u8655\u7406\u4e2d\u2026
localtempResolve=\u662f\u5426\u91cd\u65b0\u57f7\u884c\u5148\u524d\u672a\u6b63\u5e38\u5132\u5b58\u52d5\u4f5c?
requireSave=\u7169\u8acb\u5148\u5132\u5b58\u5f8c\uff0c\u518d\u57f7\u884c\u5176\u52d5\u4f5c\uff0c\u8b1d\u8b1d\u3002
saveBeforePrint=\u57f7\u884c\u5217\u5370\u5c07\u81ea\u52d5\u5132\u5b58\u8cc7\u6599\uff0c\u662f\u5426\u7e7c\u7e8c\u6b64\u52d5\u4f5c? 
saveBeforeSend=\u57f7\u884c\u5c07\u81ea\u52d5\u5132\u5b58\u8cc7\u6599\uff0c\u662f\u5426\u7e7c\u7e8c\u6b64\u52d5\u4f5c? 
saveBeforeAction=\u5c07\u81ea\u52d5\u5132\u5b58\u8cc7\u6599\uff0c$\{btnAction\}
lognView=\u767b\u9304/\u8abf\u95b1\u6838\u5099\u8a3b\u8a18
#---------------------------------------------#
# javascript commom.js use(validation)
#---------------------------------------------#
val.required=\u6b64\u70ba\u5fc5\u586b\u6b04\u4f4d.
#
val.remote=Please fix this field.
#Please fix this field.
val.email=E-Mail\u683c\u5f0f\u932f\u8aa4
#Please enter a valid email address.
val.url=\u7db2\u5740\u683c\u5f0f\u932f\u8aa4.
#Please enter a valid URL.
val.date=\u65e5\u671f\u683c\u5f0f\u932f\u8aa4.(YYYYMMDD)
val.date2=\u65e5\u671f\u683c\u5f0f\u932f\u8aa4.(YYYY-MM)
val.date3=\u65e5\u671f\u683c\u5f0f\u932f\u8aa4.(YYYYMM)
val.date4=\u65e5\u671f\u683c\u5f0f\u932f\u8aa4.(YYYY/MM)
#Please enter a valid date(YYYYMMDD).
val.dateISO=Please enter a valid date (ISO).
#Please enter a valid date (ISO).
#val.dateD=Bitte geben Sie ein g\u00fcltiges Datum ein.
#Bitte geben Sie ein g\u00fcltiges Datum ein.
val.number=\u8acb\u8f38\u5165\u6578\u5b57.
#Please enter a valid number.
#val.numberDE=Bitte geben Sie eine Nummer ein.
#Bitte geben Sie eine Nummer ein.
val.digits=\u8acb\u8f38\u5165\u6578\u5b57(\u7121\u6b63\u8ca0\u865f)
#Please enter only digits
val.creditcard=Please enter a valid credit card number.
#Please enter a valid credit card number.
val.equalTo=Please enter the same value again.
#Please enter the same value again.
val.accept=Please enter a value with a valid extension.
#Please enter a value with a valid extension.
val.maxlength=\u6700\u591a\u8f38\u5165{0}\u500b\u5b57\u5143.
#$.validator.format("Please enter no more than {0} characters.
val.minlength=\u6700\u5c11\u8f38\u5165{0}\u500b\u5b57\u5143
#Please enter at least {0} characters.
val.rangelength=\u8acb\u8f38\u5165 {0} \u5230 {1} \u4f4d\u5b57\u5143
#Please enter a value between {0} and {1} characters long.
val.range=Please enter a value between {0} and {1}.
#Please enter a value between {0} and {1}.
val.max=\u5fc5\u9700\u5c0f\u65bc\u6216\u7b49\u65bc {0} 
#Please enter a value less than or equal to {0}.
val.min=\u5fc5\u9700\u5927\u65bc\u6216\u7b49\u65bc {0}
#Please enter a value greater than or equal to {0}.
val.twid=\u8eab\u4efd\u8a3c\u8f38\u5165\u932f\u8aa4.
val.compNo=\u7d71\u4e00\u7de8\u865f\u8f38\u5165\u932f\u8aa4.
val.foreign=\u5916\u570b\u81ea\u7136\u4eba\u7d71\u7de8\u8f38\u5165\u932f\u8aa4.
val.requiredLength=\u8acb\u8f38\u5165 {0} \u500b\u5b57\u5143
val.checkID=\u300c\u8eab\u4efd\u8a3c\u300d\u6216\u300c\u7d71\u4e00\u7de8\u865f\u300d\u8f38\u5165\u932f\u8aa4.
val.tooLong=\u6587\u5b57\u9577\u5ea6\u904e\u9577
#\u65e5\u671f
val.ineldate=\u8acb\u8f38\u5165\u5e74\u6708
val.inelbranch=\u8acb\u9078\u5206\u884c
val.ip=\u8acb\u8f38\u5165\u5408\u6cd5IP
val.time=\u8acb\u8f38\u5165\u5408\u6cd5\u6642\u9593 00:00~ 23:59
val.noSignNumber=\u8acb\u8f38\u5165\u6578\u5b57(\u7121\u6b63\u8ca0\u865f)
val.checkmaxlength=\u4e0d\u53ef\u8d85\u904e {0} \u500b\u5b57\u5143
val.numeric=\u8acb\u8f38\u5165\u6578\u5b57\uff0c\u6574\u6578 ${0} \u4f4d
val.numericFraction=,\u5c0f\u6578 ${0} \u4f4d
val.phone=\u96fb\u8a71\u683c\u5f0f\u932f\u8aa4
val.alphanum=\u53ea\u80fd\u8f38\u5165\u82f1\u6587\u53ca\u6578\u5b57
val.obuText=\u8acb\u8f38\u5165\u82f1\u6578\u5b57
val.numText=\u8acb\u8f38\u5165\u6578\u5b57
val.enText=\u8acb\u8f38\u5165\u82f1\u6587\u5b57
val.halfword=\u53ea\u80fd\u8f38\u5165\u534a\u578b\u5b57
#\u5171\u7528Grid i18n
grid.pgtext=\u7b2c{0}\u9801,\u5171{1}\u9801
grid.emptyrecords=\u67e5\u7121\u8cc7\u6599
grid.recordtext={0}~{1}/\u5171{2}\u7b46
grid.loadtext=\u67e5\u8a62\u4e2d\uff0c\u8acb\u7a0d\u5f8c\uff01
grid.refresh=\u91cd\u65b0\u6574\u7406
grid.up=\u4e0a\u79fb
grid.down=\u4e0b\u79fb
grid.selrow=\u8acb\u5148\u9078\u64c7\u4e00\u7b46\u8cc7\u6599\u3002
grid.check=\u78ba\u8a8d\u4fee\u6539
grid.showAllBranch=\u5206\u884c\u6e05\u55ae\u67e5\u8a62
grid.branchNo=\u5206\u884c\u4ee3\u865f
grid.branchName=\u5206\u884c\u540d\u7a31
grid.branchGroup=\u6240\u5c6c\u71df\u904b\u4e2d\u5fc3\u540d\u7a31
grid.datePeriodCheck=\u8acb\u8f38\u5165\u65e5\u671f\u5340\u9593\u4e4b\u8d77\u8a16\u65e5\u671f!
localSave.quotaExceededError=\u8acb\u5141\u8a31\u5728\u60a8\u96fb\u8166\u4e0a\u52a0\u5927\u672c\u6a5f\u5132\u5b58\u5340\u5bb9\u91cf\u7684\u6b0a\u9650\u3002
grid.docName=\u6a94\u6848\u540d\u7a31
grid.maxSelrow=${0} \u6700\u591a\u53ea\u53ef\u9078\u64c7${1}\u7b46\u8cc7\u6599\u3002
#\u6587\u4ef6\u7570\u52d5\u8a18\u9304
docLog.logNum=\u8a18\u9304\u5e8f\u865f
docLog.logTime=\u8a18\u9304\u65e5\u671f\u6642\u9593
docLog.unitId=\u57f7\u884c\u4eba\u6240\u5c6c\u55ae\u4f4d
docLog.userId=\u57f7\u884c\u4eba\u54e1\u4ee3\u865f
docLog.userName=\u57f7\u884c\u4eba\u54e1\u59d3\u540d
docLog.userPos=\u57f7\u884c\u4eba\u54e1\u8077\u52d9
docLog.actCode=\u57f7\u884c\u9805\u76ee
docLog.title=\u6587\u4ef6\u7570\u52d5\u8a18\u9304
#\u6d41\u7a0b\u985e
flow.confirmSend=\u662f\u5426\u78ba\u5b9a\u8981\u50b3\u9001?
flow.sent=\u50b3\u9001\u6210\u529f!!
flow.exit=\u662f\u5426\u78ba\u5b9a\u96e2\u958b?
flow.confirmSend2=\u662f\u5426\u78ba\u5b9a\u5448\u4e3b\u7ba1\u8986\u6838?
flow.sent2=\u5448\u9001\u6210\u529f!!
flow.confirmReturn=\u662f\u5426\u78ba\u5b9a\u9000\u56de?
flow.returned=\u9000\u56de\u6210\u529f!!
flow.needCreateContract=\u662f\u5426\u81ea\u52d5\u7522\u751f\u4e26\u8986\u6838\u7dda\u4e0a\u5c0d\u4fdd\u5951\u7d04\u66f8?
#\u5f15\u5165\u5ba2\u6236ID
includeId.title=\u5ba2\u6236\u8cc7\u6599\u67e5\u8a62
includeId.subTitle=\u7d71\u4e00\u7de8\u865f
includeId.newCustName=(\u65b0\u5ba2\u6236)
includeId.noData=\u5ba2\u6236\u57fa\u672c\u8cc7\u6599\u6a94\u67e5\u7121\u8a72\u7d71\u4e00\u7de8\u865f
includeId.selData=\u8acb\u9078\u64c7\u4e00\u7b46\u8cc7\u6599!!
#0024\u5efa\u6a94
creatCust.queryType1=\u4f9d\u7d71\u7de8\u67e5\u8a62
creatCust.queryType2=\u4f9d\u5ba2\u6236\u82f1\u6587\u540d\u67e5\u8a62(\u516c\u53f8\u6236)
creatCust.error=\u8acb\u8f38\u5165\u67e5\u8a62\u5167\u5bb9
creatCust.custType=\u5ba2\u6236\u985e\u5225
creatCust.custType1=\u500b\u4eba\u6236
creatCust.custType2=\u516c\u53f8\u6236
creatCust.headNation=\u516c\u53f8\u8a3b\u518a\u5730\u570b\u5225(\u500b\u4eba\u570b\u7c4d)
creatCust.regNation=\u6240\u5728\u5730\u570b\u5225(\u500b\u4eba\u51fa\u751f\u5730)
creatCust.licenseType=\u8b49\u4ef6\u985e\u578b
creatCust.licenseNO=\u8b49\u4ef6\u865f\u78bc
creatCust.birthday=\u516c\u53f8\u8a2d\u7acb\u65e5 ( \u500b\u4eba\u6236\u51fa\u751f\u5e74\u6708\u65e5 )
creatCust.CNAME=\u7e41\u9ad4\u6236\u540d ( \u5168\u5f62 )
creatCust.LNAME=\u7576\u5730\u6236\u540d ( \u5168\u5f62 )
creatCust.ENAME=\u82f1\u6587\u6236\u540d ( \u534a\u5f62 )
creatCust.custName=\u6236\u540d\u8acb\u64c7\u4efb\u4e00\u8a9e\u7cfb\u8f38\u5165
creatCust.ENAMEerror=\u82f1\u6587\u6236\u540d\u683c\u5f0f\u932f\u8aa4
creatCust.bstbl=\u4e3b\u8a08\u8655\u884c\u696d\u5c0d\u8c61\u5225\u5927\u985e
creatCust.bstb2=\u4e3b\u8a08\u8655\u884c\u696d\u5c0d\u8c61\u5225\u4e2d\u985e
creatCust.bstb3=\u4e3b\u8a08\u8655\u884c\u696d\u5c0d\u8c61\u5225\u7d30\u985e
creatCust.bstb4=\u884c\u696d\u5c0d\u8c61\u5225\u7d30\u5206\u985e
creatCust.bstbError=\u8acb\u9078\u64c7\u4e00\u7a2e\u884c\u696d\u5225
creatCust.localId=\u6d77\u5916\u5206\u884c\u7576\u5730 AS400 \u7de8\u78bc ID
creatCust1.060000=\u79c1\u4eba
creatCust1.130300=\u5728\u53f0\u7121\u4f4f\u6240\u5916\u570b\u4eba
creatCust.companyDate=\u540c\u5ba2\u6236ID\u516c\u53f8\u6236\u8a2d\u7acb\u65e5
creatCust.reqID=\u8ca0\u8cac\u4ebaID
creatCust.swiftID=\u540c\u696d\u4ee3\u78bc
creatCust.buscd=\u884c\u696d\u5225
creatCust.newUserDisabled=\u7121\u65b0\u589e\u4f7f\u7528\u8005\u6b0a\u9650
creatCust.failAndConfirmExit=\u5efa\u7acb\u65b0\u5ba2\u6236\u767c\u751f\u932f\u8aa4\uff0c\u662f\u5426\u7d50\u675f\u65b0\u5ba2\u6236\u5efa\u6a94\u4f5c\u696d\uff1f
creatCust.MEMO=\u6d77\u5916\u5206\u884c\u500b\u4eba\u6236\u6642\uff0c\u4e3b\u8a08\u8655\u884c\u696d\u5c0d\u8c61\u5225\u5927\u985e\u8b2e\u9078\u64c7[\u79c1\u4eba]
#\u6a94\u6848\u4e0a\u50b3
uploadFile.button=\u4e0a\u50b3\u8cc7\u6599\u81f3\u4e3b\u6a5f
uploadFile.uploadTime=\u4e0a\u50b3\u6642\u9593
uploadFile.srcFileName=\u6a94\u6848\u540d\u7a31
uploadFile.srcFileDesc=\u6a94\u6848\u8aaa\u660e
#\u5831\u8868\u985e
printError=\u7522\u751f\u5831\u8868\u932f\u8aa4!!
printPrcess=\u5831\u8868\u7e6a\u88fd\u4e2d \uff0c\u8acb\u7a0d\u5019
openTckeditBoxmsg=\u958b\u555f{0}\u767b\u9304\u756b\u9762
backdoc.msg1=\u78ba\u8a8d\u57f7\u884c\u53d6\u6d88\u8986\u6838?
#\u756b\u9762\u8a0a\u606f
err.chooseBoss=\u8acb\u9078\u64c7\u4e3b\u7ba1
common.L140M01M=\u592e\u884c\u8cfc\u4f4f/\u7a7a\u5730/\u8208\u5efa\u623f\u5c4b\u7d71\u8a08\u5831\u8868\u7528\u76f8\u95dc\u8cc7\u8a0a
common.L140M01Q=\u5927\u9678\u5730\u5340\u6388\u4fe1\u696d\u52d9\u63a7\u7ba1\u8a3b\u8a18
common.L140S05A=\u8b8a\u66f4\u689d\u4ef6\u9805\u76ee
common.001=\u6b04\u4f4d\u6aa2\u6838\u672a\u5b8c\u6210\uff0c\u8acb\u586b\u59a5\u5f8c\u518d\u9001\u51fa
common.002=\u8acb\u9078\u64c7\u6bcd\u884c\u55ae\u4f4d/\u6388\u6b0a\u4e3b\u7ba1
common.003=\u7d93\u8fa6
common.004=\u5e38\u7528\u4e3b\u7ba1
defaultFontSize=\u5b57\u578b\u5927\u5c0f\u5efa\u8b70\u4f7f\u752816
#\u9650\u5b9aCKEDIT\u4e00\u884c\u5b57\u6578\u6709\u591a\u9577\u7684\u5099\u8a3b
lms.ckeditRemark1=\u8a3b1:\u5efa\u8b70\u5b57\u578b16
lms.ckeditRemark2=\u8a3b2:|\u2190\u5b57\u578b16\u6642\u5efa\u8b70\u63db\u884c
lms.ckeditRemark3=\u8a3b1:|\u2190\u5efa\u8b70\u63db\u884c
queryByEnglish=\u4f9d\u5ba2\u6236\u82f1\u6587\u540d\u67e5\u8a62







