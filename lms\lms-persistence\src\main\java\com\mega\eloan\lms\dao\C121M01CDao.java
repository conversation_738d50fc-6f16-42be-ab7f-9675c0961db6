package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01C;

/** 澳洲消金評等表 **/
public interface C121M01CDao extends IGenericDao<C121M01C> {

	public C121M01C findByOid(String oid);
	public List<C121M01C> findByMainId(String mainId);	
	public List<C121M01C> findByC121M01A(C121M01A meta);
	public C121M01C findByUk(String mainId, String custId, String dupNo);
	public C121M01C findByC120M01A(C120M01A c120m01a);
}