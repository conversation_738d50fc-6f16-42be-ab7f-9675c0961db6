/* 
 * LMS9541V04Formhandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.handler.form;

import java.util.Map;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.rpt.service.LMS9541V04Service;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 查詢優惠額度資訊
 * </pre>
 * 
 * @since 2012/12/06
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/06,Vector,new
 *          </ul>
 */

@Scope("request")
@Controller("lms9541v04formhandler")
public class LMS9541V04Formhandler extends AbstractFormHandler {

	@Resource
	LMS9541V04Service service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	BranchService branchService;

	/**
	 * 依照custId+報表種類獲得該紀錄。
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("static-access")
	public IResult search(PageParameters params)
			throws CapException {
		String form = params.getString("initForm");
		JSONObject jsonData = DataParse.toJSON(form);
		Map<String, Object> data = service.findMisData(
				jsonData.getString("custId"), jsonData.getString("kindNo"));

		if (Util.isNotEmpty(data)) {
			for (int i = 0; i < service.COL.length; i++) {
				Object value = data.get(service.COL[i]);
				if (Util.isNotEmpty(value)) {
					if (i == 7 || i == 8 || i == 11 || i == 12)
						jsonData.put(service.COL[i],
								NumConverter.addComma(value));
					else
						jsonData.put(service.COL[i], value.toString());
				}
			}
			jsonData.put("isEmpty", false);
		} else {
			jsonData.put("isEmpty", true);
		}
		return new CapAjaxFormResult(jsonData);
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("static-access")
	public IResult save(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String form = params.getString("detailForm");
		JSONObject jsonData = DataParse.toJSON(form);

		
		String[] data = new String[service.COL.length];
		for (int i = 0; i < service.COL.length; i++) {
			data[i] = jsonData.getString(service.COL[i]);
		}
		result.set("success", service.save(data, jsonData.getString("custId"),
				jsonData.getString("kindNo")));
		return result;
	}
}
