/* 
 * C101S01Z.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 信貸集中徵信 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C101S01Z", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo"}))
public class C101S01Z extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 案件價值NPV **/
	@Digits(integer=10, fraction=0, groups = Check.class)
	@Column(name="KYCNPV", columnDefinition="DECIMAL(15,2)")
	private BigDecimal kycNpv;

	/** KYC經辦人員 **/
	@Column(name="KYCUPDATER", columnDefinition="VARCHAR(10)")
	private String kycUpdater;
	
	/** KYC經辦人員資料時間 **/
	@Column(name="KYCUPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp kycUpdateTime;
	
	/** KYC經辦人員備註 **/
	@Column(name="KYCUPDATERMEMO", columnDefinition="VARCHAR(1000)")
	private String kycUpdaterMemo;
	
	/** KYC主管人員 **/
	@Column(name="KYCAPPROVER", columnDefinition="VARCHAR(10)")
	private String kycApprover;
	
	/** KYC主管人員資料時間 **/
	@Column(name="KYCAPPRTIME", columnDefinition="TIMESTAMP")
	private Timestamp kycApprTime;
	
	/** KYC主管人員備註 **/
	@Column(name="KYCAPPROVERMEMO", columnDefinition="VARCHAR(1000)")
	private String kycApproverMemo;
/**/
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}
	
	/** 取得案件價值NPV **/
	public BigDecimal getKycNpv() {
		return kycNpv;
	}
	/** 設定案件價值NPV **/
	public void setKycNpv(BigDecimal kycNpv) {
		this.kycNpv = kycNpv;
	}
	
	/** 取得KYC經辦人員 **/
	public String getKycUpdater() {
		return kycUpdater;
	}
	/** 設定KYC經辦人員 **/
	public void setKycUpdater(String kycUpdater) {
		this.kycUpdater = kycUpdater;
	}
	
	/** 取得KYC經辦人員資料時間 **/
	public Timestamp getKycUpdateTime() {
		return kycUpdateTime;
	}
	/** 設定KYC經辦人員資料時間 **/
	public void setKycUpdateTime(Timestamp kycUpdateTime) {
		this.kycUpdateTime = kycUpdateTime;
	}
	
	/** 取得KYC經辦人員備註 **/
	public String getKycUpdaterMemo() {
		return kycUpdaterMemo;
	}
	/** 設定KYC經辦人員備註 **/
	public void setKycUpdaterMemo(String kycUpdaterMemo) {
		this.kycUpdaterMemo = kycUpdaterMemo;
	}
	
	/** 取得KYC主管人員 **/
	public String getKycApprover() {
		return kycApprover;
	}
	/** 設定KYC主管人員 **/
	public void setKycApprover(String kycApprover) {
		this.kycApprover = kycApprover;
	}
	
	/** 取得KYC主管人員資料時間 **/
	public Timestamp getKycApprTime() {
		return kycApprTime;
	}
	/** 設定KYC主管人員資料時間 **/
	public void setKycApprTime(Timestamp kycApprTime) {
		this.kycApprTime = kycApprTime;
	}
	
	/** 取得KYC主管人員備註 **/
	public String getKycApproverMemo() {
		return kycApproverMemo;
	}
	/** 設定KYC主管人員備註 **/
	public void setKycApproverMemo(String kycApproverMemo) {
		this.kycApproverMemo = kycApproverMemo;
	}

	
}
