var pageAction = {
    handler: 'lms7110formhandler',
    grid: null,
    build: function(){
        pageAction.grid = $("#gridStopDetail").iGrid({
            // localFirst: true,
            handler: 'lms7110gridhandler',
            height: 400,
            action: "queryL918s01a",
            sortname: 'branchNo|custId|setDocNo|suspendCode',
            sortorder: 'asc|asc|asc|asc',
            postData: {
                mainId: responseJSON.mainId
            },
            rowNum: 15,
            rownumbers: true,
            multiselect: true,
            colModel: [{
                colHeader: "oid",
                name: 'oid',
                hidden: true
                // 是否隱藏
            }, {
                colHeader: "mainId", // mainId
                hidden: true,// 是否隱藏
                name: 'mainId' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index1"], // 分行代碼
                align: "left",
                width: 100, //設定寬度
                sortable: true, //是否允許排序
                //formatter : 'click',
                //onclick : function,
                name: 'branchNo' // col.id
            }, {
                colHeader: i18n.lms7110m01["L918S01A.custId"], // 客戶統編
                align: "left",
                width: 60,
                sortable: true,
                name: 'custId' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index3"], // 停權案件號碼
                align: "left",
                width: 150,
                sortable: true,
                name: 'setDocNo' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index6"], // 停權代碼
                align: "center",
                width: 50,
                sortable: true,
                name: 'suspendCode' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index4a"], // 停權月份(原始)
                align: "center",
                width: 60,
                sortable: true,
                formatter: function(data){
                    if (data == null) {
                        return "";
                    }
                    else {
                        // data + "月"
                        // html.index9=月
                        return data + i18n.lms7110m01["html.index9"];
                    }
                },
                name: 'suspendMons' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index4b"], // 停權月份(修改)
                align: "center",
                width: 60,
                sortable: true,
                formatter: function(data){
                    if (data == null) {
                        return "";
                    }
                    else {
                        // data + "月"
                        // html.index9=月
                        return data + i18n.lms7110m01["html.index9"];
                    }
                },
                name: 'modlifyMons' // col.id
            }, {
                colHeader: i18n.lms7110m01["subGrid.index5"], // 狀態註記
                align: "center",
                width: 50,
                sortable: true,
                name: 'statusFlag' // col.id
            }],
            ondblClickRow: function(rowid){
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openDetail(data);
            }
        });
        //build buttons
        //呈主管覆核
        $("#buttonPanel").find("#btnSend").click(function(){
            // confirmRun=是否確定執行此功能?
            CommonAPI.confirmMessage(i18n.def["confirmRun"], function(b){
                if (b) {
                    // 停權待覆核					
                    pageAction.flowAction({
                        flowAction: "sendStop"
                    });
                }
            });
        });
        
        //查詢目前停權起迄
        $("#buttonPanel").find("#btnQueryStop").click(function(){
            $.ajax({
                handler: pageAction.handler,
                action: 'queryStopELF510',
                data: {
                    mainId: responseJSON.mainId
                },
                success: function(result){
                    CommonAPI.showMessage(result.stopMsg);
                }
            });
        });
        
    },
    /**
     * 流程
     */
    flowAction: function(sendData){
        $.ajax({
            handler: pageAction.handler,
            data: $.extend({
                formAction: "flowAction",
                mainOid: responseJSON.mainOid
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                API.showPopMessage(i18n.def["runSuccess"], window.close);
            }
        });
    },
    /**
     * 查詢detail
     */
    init: function(){
        $.form.init({
            formHandler: pageAction.handler,
            formPostData: {
                formAction: "queryL918m01a",
                mainId: responseJSON.mainId
            },
            loadSuccess: function(respons){
                $("#formStopDetail1").setData(respons.formStopDetail1);
            }
        });
    },
    /**
     * 開啟detail
     */
    openDetail: function(data){
        var $formStopDetail2 = $("#formStopDetail2");
        // 初始化原始停權月
        $formStopDetail2.find("#suspendMons").val("");
        if (data) {
            // btn.index1=新增
            if (data.statusFlag != i18n.lms7110m01["btn.index1"]) {
                $formStopDetail2.find(".addNeed").attr("disabled", true);
            }
        }
        else {
            $formStopDetail2.find(".addNeed").attr("disabled", false);
            $formStopDetail2.find("#modlifyMons").attr("disabled", false);
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'setAllBranch',
            success: function(response){
                // 設定所有分行代碼Map
                $formStopDetail2.find("#branchNo").setItems({
                    clear: false,
                    item: response.caseBrId,
                    format: "{value} - {key}",
                    space: true
                });
                // 開始進行查詢明細
                $.ajax({
                    handler: pageAction.handler,
                    action: 'queryL918s01a',
                    data: {
                        oid: (data) ? data.oid : ""
                    },
                    success: function(result){
                        $formStopDetail2.setData(result.formStopDetail2, true);
                        if ($formStopDetail2.find("#modlifyMons").val() == "0") {
                            // 若為刪除則停權月設成唯讀
                            $formStopDetail2.find("#modlifyMons").attr("disabled", true);
                        }
                        else {
                            $formStopDetail2.find("#modlifyMons").attr("disabled", false);
                        }
                        // html.index15=停權明細
                        //開視窗
                        $("#stopDetail2").thickbox({
                            title: i18n.lms7110m01["html.index15"],
                            width: 800,
                            height: 200,
                            modal: true,
                            //align : 'center',
                            //valign: 'bottom',
                            i18n: i18n.def,
                            buttons: {
                                'saveData': function(){
                                    if ($formStopDetail2.valid()) {
                                        var modlifyMons = $formStopDetail2.find("#modlifyMons").val();
                                        if ($formStopDetail2.find("#modlifyMons").attr("disabled") == false) {
                                            if (modlifyMons < 1 || modlifyMons > 12) {
                                                // msg.alert2=輸入不合法的月份!
                                                CommonAPI.showMessage(i18n.lms7110m01["msg.alert2"]);
                                                return;
                                            }
                                        }
                                        pageAction.saveDetail(data);
                                    }
                                },
                                'del': function(){
                                    if (data) {
                                        pageAction.delDetail(data.oid);
                                    }
                                    else {
                                        // msg.alert3=資料尚未儲存，請執行「儲存」後再執行本功能。
                                        CommonAPI.showMessage(i18n.lms7110m01["msg.alert3"]);
                                        return;
                                    }
                                },
                                'close': function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
                });
            }
        });
    },
    /**
     * 儲存detail
     */
    saveDetail: function(data){
        $.ajax({
            handler: pageAction.handler,
            action: 'saveL918s01a',
            data: {
                oid: (data) ? data.oid : "",
                mainId: responseJSON.mainId,
                formStopDetail2: JSON.stringify($("#formStopDetail2").serializeData())
            },
            success: function(result){
                pageAction.reloadGrid();
                $.thickbox.close();
                $.thickbox.close();
                CommonAPI.showMessage(result.NOTIFY_MESSAGE);
            }
        });
    },
    /**
     * 新增detail
     */
    addDetail: function(){
        pageAction.openDetail();
    },
    /**
     * 刪除detail(從Grid刪除)
     */
    delGridDetail: function(){
        var list = pageAction.getRowData();
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms7110m01["msg.alert1"]);
            return;
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'delL918s01a',
            data: {
                list: list
            },
            success: function(result){
                pageAction.reloadGrid();
            }
        });
    },
    /**
     * 刪除detail
     */
    delDetail: function(oid){
        var list = oid;
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms7110m01["msg.alert1"]);
            return;
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'delL918s01a',
            data: {
                list: list
            },
            success: function(result){
                pageAction.reloadGrid();
                $.thickbox.close();
                $.thickbox.close();
                CommonAPI.showMessage(result.NOTIFY_MESSAGE);
            }
        });
    },
    /**
     * 取消刪除detail
     */
    undoGridDetail: function(){
        var list = pageAction.getRowData();
        if (list == "") {
            // msg.alert1=尚未選取資料!
            CommonAPI.showMessage(i18n.lms7110m01["msg.alert1"]);
            return;
        }
        $.ajax({
            handler: pageAction.handler,
            action: 'undoDelL918s01a',
            data: {
                list: list
            },
            success: function(result){
                pageAction.reloadGrid();
            }
        });
    },
    //依照不同文件狀態控制唯讀
    setReadOnly: function(auth){
        //停權編製中可編輯，其他都不可編輯
        if (responseJSON.mainDocStatus == "LEH") {
            //編製中且沒被鎖定
            if (auth.Modify && !thickboxOptions.readOnly) {
                responseJSON["readOnly"] = false;
                $(this).find("button").show();
            }
            else {
                responseJSON["readOnly"] = true;
                var $formStopDetail2 = $("#formStopDetail2");
                $formStopDetail2.readOnlyChilds(true);
                $("#formStopBtn").find("button").hide();
                thickboxOptions.readOnly = true;
                //顯示上方主要標題按鈕
                $("#buttonPanel :button").show();
                $("#buttonPanel").find("#btnSend").hide();
            }
        }
        else {
            //非編製中
            responseJSON["readOnly"] = true;
            var $formStopDetail2 = $("#formStopDetail2");
            $formStopDetail2.readOnlyChilds(true);
            $("#formStopBtn").find("button").hide();
            thickboxOptions.readOnly = true;
            //顯示上方主要標題按鈕
            $("#buttonPanel :button").show();
            $("#buttonPanel").find("#btnSend").hide();
        }
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var rows = pageAction.grid.getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) {
            //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = pageAction.grid.getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        return list;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    }
}
$(document).ready(function(){
    var auth = (responseJSON ? responseJSON.Auth : {}); //權限
    pageAction.build();
    pageAction.init();
    pageAction.setReadOnly(auth);
});
