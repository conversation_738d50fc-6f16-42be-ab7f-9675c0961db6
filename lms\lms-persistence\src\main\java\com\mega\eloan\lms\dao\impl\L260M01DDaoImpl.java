/* 
 * L260M01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L260M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01D;

/** 貸後管理紀錄檔 **/
//
@Repository
public class L260M01DDaoImpl extends LMSJpaDao<L260M01D, String>
	implements L260M01DDao {

	@Override
	public L260M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
//		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		return findUniqueOrNone(search);
	}

	@Override
	public List<L260M01D> findByMainId(String mainId, boolean notIncDel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("followDate", false);
		map.put("unid", false);
		map.put("oid", false);
		search.setOrderBy(map);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260M01D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L260M01D> findByDataSrc(String dataSrc) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "dataSrc", dataSrc);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("followDate", false);
		map.put("unid", false);
		map.put("oid", false);
		search.setOrderBy(map);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260M01D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L260M01D> findByMainIdAndNos(String mainId, String cntrNo,
											 String loanNo, boolean notIncDel, boolean incEmptyLoanNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if(Util.isNotEmpty(loanNo) || incEmptyLoanNo){
			search.addSearchModeParameters(SearchMode.EQUALS, "loanNo", loanNo);
		}
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("followDate", false);
		map.put("unid", false);
		map.put("oid", false);
		search.setOrderBy(map);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L260M01D> list = createQuery(search).getResultList();
		return list;
	}

    @Override
    public L260M01D findByMainIdAndUnid(String mainId, String unid) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        search.addSearchModeParameters(SearchMode.EQUALS, "unid", unid);
        Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("followDate", false);
        map.put("unid", false);
        map.put("oid", false);
        search.setOrderBy(map);
        search.setMaxResults(Integer.MAX_VALUE);
        return findUniqueOrNone(search);
    }

    @Override
    public List<L260M01A> findPageByFilter(StringBuilder condition, List<Object> paramValues,int firstRow,int maxRow) {

		String sql = "SELECT a.* FROM LMS.L260M01A a LEFT JOIN LMS.L260M01D c ON a.MAINID =c.MAINID WHERE c.DELETEDTIME IS NULL AND " + condition;
		Query query = entityManager.createNativeQuery(sql,L260M01A.class);
		for(int i=0;i<paramValues.size();i++){
			query.setParameter(i+1, paramValues.get(i));
		}
		query.setFirstResult(firstRow);
		query.setMaxResults(maxRow);
		return (List<L260M01A>) query.getResultList();
    }

	@Override
	public List<Object[]> findByFilter(StringBuilder condition, List<Object> paramValues) {

		String sql = "SELECT a.*,c.* FROM LMS.L260M01A a LEFT JOIN LMS.L260M01D c ON a.MAINID =c.MAINID WHERE c.DELETEDTIME IS NULL AND " + condition;
		Query query = entityManager.createNativeQuery(sql);
		for(int i=0;i<paramValues.size();i++){
			query.setParameter(i+1, paramValues.get(i));
		}
		return query.getResultList();
	}
}