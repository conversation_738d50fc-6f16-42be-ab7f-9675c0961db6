/* 
 * L140M01PEnum.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.enums;

/**
 * <pre>
 * L140M01P 敘做條件異動比較表ENUM
 * </pre>
 * 
 * @since 2013/3/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/13,REX,new
 *          </ul>
 */
public enum L140M01PEnum {
	借保人(1), 申貸金額(2),動撥金額(3),授信期間(4), 動用期間(5), 評等(6), 產品(7), 科目(8), 利率(9), 償還方式(10), 管制條件(11), 擔保品(
			12), 房貸利率方案(13), 房貸產品方案(14), 股票基金受益憑證維持率(15), 風險權數抵減後(16), 切結同意書(17), 代償轉貸(
			18), 融資業務分類(19), 用途別(20), 是否屬興建房屋(21), 央行註記(22), 額度本票(23), 政策性貸款內容(
			24), 行家理財續約次數(25), 搭配循環性貸款內容(26), 本額度應計入DBR22倍金額(27), 其他敘做條件(28);

	private int code;

	private L140M01PEnum(int code) {
		this.code = code;
	}

	public int getCode() {
		return this.code;
	}

	public boolean isEquals(int other) {
		return code == other;
	}

	public static L140M01PEnum getEnum(int code) {
		for (L140M01PEnum enums : values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}
}
