package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.OverSeaCLSOuterPanel;

/**
 * <pre>
 * 泰國地區消金偣款人基本資料
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035v00")
public class LMS1035V00Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 主管跟經辦都會出現的按鈕
		if (true) {
			addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.Add, LmsButtonEnum.Delete,
					LmsButtonEnum.Send);
		}

		renderJsI18N(LMS1035V01Page.class);
		renderJsI18N(LMS1035M01Page.class);
		
		renderJsI18N(AbstractOverSeaCLSPage.class);
		Panel panel = new OverSeaCLSOuterPanel("divOverSeaCLSPanel");
		panel.processPanelData(model, params);
		model.addAttribute("loadScript", "loadScript('pagejs/lms/LMS1035V00Page');");
	}

}