#==================================================
# \u52d5\u7528\u5be9\u6838\u8868\u5916\u90e8\u6309\u9215
button.UseFirstTable=Advance Drawdown Control Sheet
button.LogeIN=Register
button.DataFix=Edit Data
# \u52d5\u7528\u5be9\u6838\u8868\u4e3b\u6a94
L160M01A.caseNo=Case No.
L160M01A.mainCustId=Principal Borrower's UBN
L160M01A.mainCust=Principal Borrower
L163M01A.itemTrace=Follow-up Progress
L163M01A.finishDate=Completion Date
L160M01A.willFinishDate=Scheduled Full Documentation Date
L163M01A.waitingItem=Pending Actions
L160M01A.custId=Unified Business Number
L160M01A.dupNo=Repeat Serial No.
L160M01A.managerId=Class A Supervisor
# \u52d5\u7528\u5be9\u6838\u8868\u8a0a\u606f
L160M01A.message01=Please input records to inquire
L160M01A.message02=Approved Drawdown Period
L160M01A.message03=This case has not ben drawn in advance, therefore dows not need to input a completion date
L160M01A.message04=This advance drawdown has been completed and submitted for supervisor's approval; no further changes are allowed
L160M01A.message05=Multiple selections are not allowed under this function
L160M01A.message06=The start date can not be later than the end date
L160M01A.message07=Borrower's UBN not entered
L160M01A.message08=Manual Input
L160M01A.message09=Please input a date
L160M01A.message10=Please input the name of the Class A Supervisor
