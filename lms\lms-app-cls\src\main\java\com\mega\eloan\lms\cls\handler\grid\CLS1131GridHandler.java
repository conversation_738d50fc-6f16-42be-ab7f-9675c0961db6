/* 
 * CLS1131GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1131V01Page;
import com.mega.eloan.lms.cls.panels.CLS1131S01Panel;
import com.mega.eloan.lms.cls.report.CLS1131R03RptService;
import com.mega.eloan.lms.cls.report.CLS1131R04RptService;
import com.mega.eloan.lms.cls.report.CLS1131R05RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C101S01G;
import com.mega.eloan.lms.model.C101S01H;
import com.mega.eloan.lms.model.C101S01I;
import com.mega.eloan.lms.model.C101S01Q;
import com.mega.eloan.lms.model.C101S01R;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C101S01U;
import com.mega.eloan.lms.model.C101S02S;
import com.mega.eloan.lms.model.C101S04W;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C120S01G;
import com.mega.eloan.lms.model.C120S01H;
import com.mega.eloan.lms.model.C120S01I;
import com.mega.eloan.lms.model.C120S01Q;
import com.mega.eloan.lms.model.C120S01R;
import com.mega.eloan.lms.model.C120S01S;
import com.mega.eloan.lms.model.C120S01T;
import com.mega.eloan.lms.model.C120S01U;
import com.mega.eloan.lms.model.C120S02S;
import com.mega.eloan.lms.model.C120S04W;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          <li>2013/07/04,Rex,加上內容聯徵查詢日期不得小於今天一個月
 *          </ul>
 */
@Scope("request")
@Controller("cls1131gridhandler")
public class CLS1131GridHandler extends AbstractGridHandler {

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	CLS1131Service service;

	@Resource
	CLS1131R03RptService cls1131R03RptService;
	
	@Resource
	EloandbBASEService eloandbService;

	@Resource
	ScoreService scoreService;
	
	@Resource
	CLS1131R04RptService cls1131R04RptService;
	
	@Resource
	CLS1131R05RptService cls1131R05RptService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	CLS1141Service cls1141Service;
	
	Properties prop;
	
	@Resource
	RPAProcessService rpaProcessService;

	/**
	 * 個金徵信作業>查詢基本資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryBaseData(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String custId = Util.trim(params.getString("search_custId"));
		if (Util.isNotEmpty(custId)){
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		String custName = Util.trim(params.getString("search_custName"));
		if(Util.isNotEmpty(custName)){
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					"%"+custName+"%");
		}		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());

		Page<? extends GenericBean> page = service.findPage(C101M01A.class,
				pageSetting);
		List<C101M01A> list = (List<C101M01A>) page.getContent();
		for (C101M01A model : list) {
			// 統編+重覆序號
			model.setCustNumber(Util.trim(model.getCustId()) + " "
					+ Util.trim(model.getDupNo()));
			// 評等模型
			model.setMarkModel(Util.trim(getI18nMsg("C101M01A.markModel."
					+ Util.trim(model.getMarkModel()))));
			
			if(true){//房貸
				C101S01G c101s01g = model.getC101s01g();
				if (c101s01g != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01g.getQuote())){
						c101s01g.setGrade2(ClsUtil.getAdjustStatus(c101s01g));	
					}else{
						c101s01g.setGrade1("");
						c101s01g.setGrade2("");
						c101s01g.setGrade3("");
					}
					
					// 警示訊息
					c101s01g.setAlertMsg(Util.trim(LMSUtil.getGradeMessage(c101s01g, UtilConstants.L140S02AModelKind.房貸, 
							OverSeaUtil.TYPE_RAW, "",UtilConstants.CheckItemRange.defult)
							).replaceAll(
							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
			
			// C101S01E c101s01e = model.getC101s01e();
			
			if(true){//非房貸
				C101S01Q c101s01q = model.getC101s01q();
				if (c101s01q != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01q.getQuote())){
						c101s01q.setGrade2(ClsUtil.getAdjustStatus(c101s01q));	
					}else{
						c101s01q.setGrade1("");
						c101s01q.setGrade2("");
						c101s01q.setGrade3("");
					}					
					// 警示訊息 => 前端js只用到 c101s01g.alertMsg
//					c101s01q.setAlertMsg(Util.trim(
//							LMSUtil.getGradeMessage(c101s01q, UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
			
			if(true){//卡友貸
				C101S01R c101s01r = model.getC101s01r();
				if (c101s01r != null) {
					// 調整評等
					if(ClsUtil.showMarkModelInfoByQuote(c101s01r.getQuote())){
						c101s01r.setGrade2(ClsUtil.getAdjustStatus(c101s01r));	
					}else{
						c101s01r.setGrade1("");
						c101s01r.setGrade2("");
						c101s01r.setGrade3("");
					}					
					// 警示訊息 => 前端js只用到 c101s01g.alertMsg
//					c101s01r.setAlertMsg(Util.trim(
//							LMSUtil.getGradeMessage(c101s01r, UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//							EloanConstants.HTML_NEWLINE, " "));
				}	
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 個金徵信作業>查詢相關評分模型
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryScoreModel(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String custId = Util.trim(params.getString("search_custId"));
		String search_custId = "";
		String search_dupNo = ""; 
		String search_ownBrId = "";
		if (Util.isNotEmpty(custId)) {
			search_custId = custId;
			// 登入單位開頭不是9的,只能查自己的分行
			// String unitNo = Util.trim(user.getUnitNo());
			String unitNo = Util.trim(user.getSsoUnitNo());
			if (!unitNo.startsWith("9")) {
				search_ownBrId = unitNo;
			}
		} else {
			search_ownBrId = user.getUnitNo();
		}
		List<Map<String, Object>> r = eloandbService.findC101M01A_queryScoreModel(search_custId, search_dupNo, search_ownBrId, pageSetting);
		for(Map<String, Object> row:r){
			row.put("rawMarkModel", row.get("markModel"));
			row.put("markModel", Util.trim(getI18nMsg("C101M01A.markModel."+ Util.trim(row.get("markModel")))));
		}
		
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(r, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 個金簽報書>編製中> click button 引進借保人
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult importList(ISearch pageSetting, PageParameters params) throws CapException {
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				Util.trim(MegaSSOSecurityContext.getUnitNo()));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "importFlag",
				UtilConstants.DEFAULT.是);
		String findId = Util.trim(params.getString("findId"));

		if (Util.isNotEmpty(findId)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
					findId + "%");
		}
		
		// 2013/07/04,Rex,加上內容聯徵查詢日期不得小於今天一個月
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"c101s01e.eJcicQDate", ClsUtil.getDateOfMonthsAgo(1));
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"c101s01e.eChkQDate", ClsUtil.getDateOfMonthsAgo(1));
		if(true){
			/*
			 * 風控處比對 模型 的A、B卡資料
			 * 發現已覆核的cntrNo 新作 201110300046，有C120S01E 但無C120S01G,C120S01Q
			 * 為免日後再出現異常：額外加上 c101s01g.varVer>'' 的條件
			 */
			SearchModeParameter naturalFlagY = new SearchModeParameter(SearchMode.EQUALS,"naturalFlag", "Y");
			SearchModeParameter paramVarVer = new SearchModeParameter(SearchMode.GREATER_THAN,"c101s01g.varVer", "");
			
			pageSetting.addSearchModeParameters(SearchMode.OR, 
					new SearchModeParameter(SearchMode.EQUALS, "naturalFlag", "N"), 
					new SearchModeParameter(SearchMode.AND, naturalFlagY, paramVarVer));
		}

		if(fetch_latestVarVer()){
			SearchModeParameter naturalFlagY = new SearchModeParameter(SearchMode.EQUALS,"naturalFlag", "Y");
			SearchModeParameter paramVarVer = new SearchModeParameter(SearchMode.EQUALS,"c101s01q.varVer", scoreService.get_Version_NotHouseLoan());
			
			pageSetting.addSearchModeParameters(SearchMode.OR, 
					new SearchModeParameter(SearchMode.EQUALS, "naturalFlag", "N"), 
					new SearchModeParameter(SearchMode.AND, naturalFlagY, paramVarVer));
		}
		
		Map<String, Boolean> orderByMap = pageSetting.getOrderBy();
		if (orderByMap != null && orderByMap.containsKey("custNumber")) {
			orderByMap.put("custId", orderByMap.get("custNumber"));
			orderByMap.remove("custNumber");
		}
		
		//TODO：若測試模型, 導致該分行有50萬筆資料, findPage(...)會跑很久
		Page<? extends GenericBean> page = service.findPage(C101M01A.class,
				pageSetting);
		List<C101M01A> list = (List<C101M01A>) page.getContent();
		for (C101M01A model : list) {
			// 統編+重覆序號
			model.setCustNumber(Util.trim(model.getCustId()) + " "
					+ Util.trim(model.getDupNo()));
			// 評等模型
			model.setMarkModel(Util.trim(getI18nMsg("C101M01A.markModel."
					+ Util.trim(model.getMarkModel()))));
			
			if(true){//房貸
				C101S01G c101s01g = model.getC101s01g();
				if (c101s01g != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c101s01g.getQuote())){
//						c101s01g.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c101s01g,UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c101s01g.setGrade3("");
					}
					
				}	
			}
			if(true){//非房貸
				C101S01Q c101s01q = model.getC101s01q();
				if (c101s01q != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c101s01q.getQuote())){
//						c101s01q.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c101s01q,UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c101s01q.setGrade3("");	
					}					
				}	
			}
			if(true){//卡友貸
				C101S01R c101s01r = model.getC101s01r();
				if (c101s01r != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c101s01r.getQuote())){
//						c101s01r.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c101s01r,UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c101s01r.setGrade3("");	
					}					
				}	
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 因為有緩衝期、在緩衝期內可以勾選
	 */
	private boolean fetch_latestVarVer(){
		return false;
	}
	
	/**
	 * 個金簽報書>編製中> 引入借保人的UI【click button 引進借保人,帶入該grid】
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC120m01a(ISearch pageSetting,
			PageParameters params) throws CapException {
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				Util.trim(params.getString(EloanConstants.MAIN_ID)));
		// 2013-01-25_Rex註解不需加入分行資料
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
		// MegaSSOSecurityContext.getUnitNo());
		Map<String, Boolean> orderByMap = pageSetting.getOrderBy();
		if (orderByMap != null && orderByMap.containsKey("custNumber")) {
			orderByMap.put("custId", orderByMap.get("custNumber"));
			orderByMap.remove("custNumber");
		}
		
		Page<? extends GenericBean> page = service.findPage(C120M01A.class,
				pageSetting);
		List<C120M01A> list = (List<C120M01A>) page.getContent();
		for (C120M01A model : list) {
			if (UtilConstants.DEFAULT.是.equals(Util.trim(model.getKeyMan()))) {
				model.setKeyMan("*");
			} else {
				model.setKeyMan("");
			}
			// 統編+重覆序號
			model.setCustNumber(Util.trim(model.getCustId()) + " "
					+ Util.trim(model.getDupNo()));
			if(true){//房貸
				C120S01G c120s01g = model.getC120s01g();
				if (c120s01g != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c120s01g.getQuote())){
//						c120s01g.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c120s01g, UtilConstants.L140S02AModelKind.房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c120s01g.setGrade3("");
					}
				}	
			}
			if(true){//非房貸
				C120S01Q c120s01q = model.getC120s01q();
				if (c120s01q != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c120s01q.getQuote())){
//						c120s01q.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c120s01q, UtilConstants.L140S02AModelKind.非房貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c120s01q.setGrade3("");
					}
					
				}	
			}
			if(true){//卡友貸
				C120S01R c120s01r = model.getC120s01r();
				if (c120s01r != null) {
					if(ClsUtil.showMarkModelInfoByQuote(c120s01r.getQuote())){
//						c120s01r.setAlertMsg(Util.trim(
//								LMSUtil.getGradeMessage(c120s01r, UtilConstants.L140S02AModelKind.卡友貸, OverSeaUtil.TYPE_RAW)).replaceAll(
//								EloanConstants.HTML_NEWLINE, " "));
					}else{
						c120s01r.setGrade3("");
					}					
				}	
			}
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簡化簽報書借款人明細
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC120s01t(ISearch pageSetting,
			PageParameters params) throws CapException {
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				Util.trim(params.getString(EloanConstants.MAIN_ID)));
		
		Page<? extends GenericBean> page = service.findPage(C120S01T.class,
				pageSetting);

		List<C120S01T> listC120S01T = (List<C120S01T>) page.getContent();
		
		for (C120S01T modelC120S01T : listC120S01T) {
			C120M01A l120s01a = cls1141Service
								.findC120M01AByfindByUniqueKey(Util.trim(params.getString(EloanConstants.MAIN_ID)),
										 modelC120S01T.getCustId(), modelC120S01T.getDupNo());
			modelC120S01T.setCustName(l120s01a.getCustName());
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * @param key
	 * @return
	 */
	private String getI18nMsg(String key) {
		String result = null;
		if (prop == null){
			prop = MessageBundleScriptCreator
					.getComponentResource(CLS1131V01Page.class);
		}
		if (prop != null) {
			result = prop.getProperty(Util.trim(key));
		}
		return Util.trim(result);
	}
	

	private String c101s01h_dataTypeDesc(){
		return "聯徵EJCIC查詢資料";
	}
	private String c101s01i_dataTypeDesc(){
		return "票信ETCH查詢資料";
	}
	private String c101s01e_Z13_Z21_dataTypeDesc(String txId){
		if(Util.equals(txId, CrsUtil.EJ_TXID_Z13)){
			return CrsUtil.EJ_TXID_PREFIX+(txId)+CrsUtil.EJ_TXID_Z13_DESC;
		}else if(Util.equals(txId, CrsUtil.EJ_TXID_Z21)){
			return CrsUtil.EJ_TXID_PREFIX+(txId)+CrsUtil.EJ_TXID_Z21_DESC;
		}else if(Util.equals(txId, CrsUtil.EJ_TXID_B36)){
			return CrsUtil.EJ_TXID_PREFIX+(txId)+CrsUtil.EJ_TXID_B36_DESC;
		}else if(Util.equals(txId, CrsUtil.EJ_TXID_D10)){
			return CrsUtil.EJ_TXID_PREFIX+(txId)+CrsUtil.EJ_TXID_D10_DESC;
		}else if(Util.equals(txId, CrsUtil.EJ_TXID_R20)){
			return CrsUtil.EJ_TXID_PREFIX+(txId)+CrsUtil.EJ_TXID_R20_DESC;		
		}else{
			return txId;
		}
	}
	private String c101s01s_dataTypeDesc(Properties prop_cls1131, String dataType){
		if(Util.equals("1", dataType)){
			//C101S01S.customerCreditAnomalyData=往來客戶信用異常資料
			return prop_cls1131.getProperty("C101S01S.customerCreditAnomalyData");
		}else if(Util.equals("2", dataType)){
			//C101S01S.isCustomerStakeholderData=客戶是否為利害關係人資料
			return prop_cls1131.getProperty("C101S01S.isCustomerStakeholderData");
		}else if(Util.equals("3", dataType)){
			//C101S01S.refusedRecordData=婉卻紀錄資料
			return prop_cls1131.getProperty("C101S01S.refusedRecordData");
		}else if(Util.equals("4", dataType)){
			//C101S01S.eaiCURIQ01=證券違約交割資料
			return prop_cls1131.getProperty("C101S01S.eaiCURIQ01");
		}else if(Util.equals("5", dataType)){
			//C101S01S.api.idCardCheckData=內政部國民身分證領換補資料
			return prop_cls1131.getProperty("C101S01S.api.idCardCheckData");
		}
		return dataType;		
	}
	
	/** dataStatus 內 ，0代表正常。把 0去掉後，若有非0的字元，代表需出現「警示」  */
	private String c101s01s_dataStatus(String icon_ok, String icon_warn, String raw_dataStatus){
		String dataStatus = Util.trim(raw_dataStatus);
		
		if(Util.isEmpty(dataStatus)){
			return "";
		}else{
			if(dataStatus.replaceAll("0", "").length()>0){
				return icon_warn;
			}else{
				return icon_ok;
			}
		}		
	}
	private Map<String, Object> build_queryDataArchivalRecordData_row(String oid, String mainId,
			String dataType, String dataTypeDesc, String fileSeq, String queryTime, String link, String dataStatus, String remark, String dataSrcMemo, String dataStatusCode){
		Map<String, Object> row = new HashMap<String, Object>();
		row.put("oid", oid);
		row.put("mainId", mainId);
		row.put("dataType", dataType);
		row.put("dataTypeDesc", dataTypeDesc);
		row.put("fileSeq", fileSeq);
		row.put("queryDate", queryTime);
		row.put("link", link);
		row.put("dataStatus", dataStatus);
		row.put("remark", remark);
		row.put("dataSrcMemo", dataSrcMemo);
		row.put("dataStatusCode", dataStatusCode);
		return row;
	}
	
	public CapMapGridResult queryDataArchivalRecordData(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String branchNo = Util.trim(params.getString("ownBrId"));
		boolean isC120M01A = Util.equals("Y", Util.trim(params.getString("isC120M01A")));
		String icon_ok = "○";
		String icon_warn = "▲";
		
		Properties prop_cls1131 = MessageBundleScriptCreator.getComponentResource(CLS1131S01Panel.class);
		if(Util.isNotEmpty(custId)){
			if(isC120M01A){
				
				C120S01E c120s01e = this.clsService.findC120S01E(mainId, custId, dupNo);
				if(true){ // 資料建檔相關data
					List<C120S01S> s01s_list = new ArrayList<C120S01S>();
					s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(mainId, custId, dupNo, "1"));
					s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(mainId, custId, dupNo, "2"));
					s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(mainId, custId, dupNo, "3"));

					if(null != c120s01e 
							&& ("1.0".equals(c120s01e.getVersion()) || c120s01e.getVersion() == null)){
						
						s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(mainId, custId, dupNo, "4")); // 證券違約交割資料
					}
					
					s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(mainId, custId, dupNo, "5"));
					for(C120S01S c101s01s : s01s_list){
						String oid = c101s01s.getOid();						
						String dataType = c101s01s.getDataType();
						String dataTypeDesc = c101s01s_dataTypeDesc(prop_cls1131, c101s01s.getDataType());
						String fileSeq = c101s01s.getFileSeq();
						String queryTime = CapDate.getDateTimeFormat(c101s01s.getDataCreateTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = c101s01s_dataStatus(icon_ok, icon_warn, c101s01s.getDataStatus());
						String remark =  "";
						String dataSrcMemo = "C120S01S";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");
						row.put("reportFileType", c101s01s.getReportFileType());
						//-----------
						list.add(row);				
					}
				}
				
				C120S01E c101s01e = clsService.findC120S01E(mainId, custId, dupNo);
				if(c101s01e!=null){ //聯徵、票信相關data
					List<C120S01H> c101s01h_list = clsService.findC120S01H(mainId, custId, dupNo);
					List<C120S01I> c101s01i_list = clsService.findC120S01I(mainId, custId, dupNo);
					if(c101s01e.getOneBtnQ_qTime()!=null && c101s01h_list.size()>0){
						String oid = "c101s01h"+"_"+mainId;
						String dataType = "c101s01h";
						String dataTypeDesc = c101s01h_dataTypeDesc();
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c101s01e.getOneBtnQ_qTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = icon_ok;
						String remark =  "";
						String dataSrcMemo = "C120S01H";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
						//-----------
						list.add(row);
					}
					if(true){
						if(c101s01e.getZ13_qTime()!=null){
							String txId = CrsUtil.EJ_TXID_Z13;
							String oid = c101s01e.getOid()+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getZ13_qTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C120S01E";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}
						if(c101s01e.getZ21_qTime()!=null){
							String txId = CrsUtil.EJ_TXID_Z21;
							String oid = c101s01e.getOid()+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getZ21_qTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C120S01E";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}
					}
					if(c101s01e.getOneBtnQ_qTime()!=null && c101s01i_list.size()>0){
						String oid= "c101s01i"+"_"+mainId;
						String dataType = "c101s01i";
						String dataTypeDesc = c101s01i_dataTypeDesc();
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c101s01e.getOneBtnQ_qTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = icon_ok;
						String remark =  "";
						String dataSrcMemo = "C120S01I";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
						//-----------
						list.add(row);
					}
				}				
				
				if(true){
					String[] txid_arr = new String[]{CrsUtil.EJ_TXID_B36, CrsUtil.EJ_TXID_D10, CrsUtil.EJ_TXID_R20};
					for(String txId :txid_arr){
						List<C120S01U> s01u_list = clsService.findC120S01U_txid(mainId, custId, dupNo, txId);
						if(s01u_list.size()>0){
							String oid = mainId+custId+dupNo+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(s01u_list.get(0).getSendTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C120S01U";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}						
					}						
				}
				
				if(true){
					// RPA 司法院受監護/輔助宣告資料				
					C120S04W c120S04w = this.rpaProcessService.getC120S04WBy(mainId, custId);
					if(null != c120S04w){
						String oid = c120S04w.getOid();			
						String dataType = CrsUtil.RPA_TXID_FA;
						String dataTypeDesc = CrsUtil.RPA_TXID_FA_DESC;
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c120S04w.getQueryTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = LMSUtil.getRpaStatusMap().get(c120S04w.getStatus());
						String remark = c120S04w.getMemo();
						String dataSrcMemo = "C120S04W";
						String dataStatusCode = c120S04w.getStatus();
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, dataStatusCode);						
						//-----------
						list.add(row);	
					}
				}
				
				if(c120s01e != null && "2.0".equals(c120s01e.getVersion())){
					// 聯徵T70證券暨期貨違約交割記錄資訊
					C120S02S c120s02s = this.service.getC120s02sByUniqueKey(mainId, custId, dupNo);
					if(null != c120s02s){
						String oid = c120s02s.getOid();			
						String dataType = CrsUtil.EJ_TXID_T70;
						String dataTypeDesc = CrsUtil.EJ_TXID_T70_DESC;
						String fileSeq = "0";
						String queryTime = c120s02s.getQueryTime() == null ? "" : CapDate.formatDate(c120s02s.getQueryTime(), "yyyy-MM-dd");
						String link = prop_cls1131.getProperty("C101S01S.open");
						
						String negFlag = c120s02s.getNegFlag();
						String dataStatus = "";
						if(negFlag != null){
							dataStatus = "N0".equals(negFlag) || "N1".equals(negFlag) ? icon_ok : icon_warn ;
						}
						
						String remark = "A3".equals(c120s02s.getDataStatus()) ? prop_cls1131.getProperty("C101S02S.ProcessedSuccessfully") : prop_cls1131.getProperty("C101S02S.Processing");
						String dataSrcMemo = "C120S02S";
						String dataStatusCode = c120s02s.getDataStatus();
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, dataStatusCode);						
						//-----------
						list.add(row);	
					}
				}

				if(true){
					// WiseNews 負面新聞查詢
					if(null != c101s01e){
						if (Util.isNotEmpty(c101s01e.getWiseNews())) {
							String oid = c101s01e.getOid();
							String dataType = CrsUtil.WiseNews;
							String dataTypeDesc = CrsUtil.WiseNews_DESC;
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getUpdateTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark = "";
							String dataSrcMemo = "WISENEWS";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo,"");
							//-----------
							list.add(row);
						}
					}
				}
				
			}else{
				if(true){ // 資料建檔相關data
					List<C101S01S> s01s_list = new ArrayList<C101S01S>();
					s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, "1"));
					s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, "2"));
					s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, "3"));
					s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, "4")); //證券違約交割資料
					s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(mainId, custId, dupNo, "5"));
					for(C101S01S c101s01s : s01s_list){
						String oid = c101s01s.getOid();						
						String dataType = c101s01s.getDataType();
						String dataTypeDesc = c101s01s_dataTypeDesc(prop_cls1131, c101s01s.getDataType());
						String fileSeq = c101s01s.getFileSeq();
						String queryTime = CapDate.getDateTimeFormat(c101s01s.getDataCreateTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = c101s01s_dataStatus(icon_ok, icon_warn, c101s01s.getDataStatus());
						String remark =  "";
						String dataSrcMemo = "C101S01S";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
						row.put("reportFileType", c101s01s.getReportFileType());
						//-----------
						list.add(row);				
					}				
				}
				
				C101M01A c101m01a = clsService.findC101M01A_mainId(mainId);
				C101S01E c101s01e = clsService.findC101S01E(c101m01a);
				if(c101s01e!=null){ //聯徵、票信相關data					
					List<C101S01H> c101s01h_list = clsService.findC101S01H(c101m01a);
					List<C101S01I> c101s01i_list = clsService.findC101S01I(c101m01a);
					if(c101s01e.getOneBtnQ_qTime()!=null && c101s01h_list.size()>0){
						String oid = "c101s01h"+"_"+mainId;
						String dataType = "c101s01h";
						String dataTypeDesc = c101s01h_dataTypeDesc();
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c101s01e.getOneBtnQ_qTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = icon_ok;
						String remark =  "";
						String dataSrcMemo = "C101S01H";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
						//-----------
						list.add(row);
					}
					if(true){
						if(c101s01e.getZ13_qTime()!=null){
							String txId = CrsUtil.EJ_TXID_Z13;
							String oid = c101s01e.getOid()+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getZ13_qTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C101S01E";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}
						if(c101s01e.getZ21_qTime()!=null){
							String txId = CrsUtil.EJ_TXID_Z21;
							String oid = c101s01e.getOid()+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getZ21_qTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C101S01E";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}
					}
					if(c101s01e.getOneBtnQ_qTime()!=null && c101s01i_list.size()>0){
						String oid= "c101s01i"+"_"+mainId;
						String dataType = "c101s01i";
						String dataTypeDesc = c101s01i_dataTypeDesc();
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c101s01e.getOneBtnQ_qTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = icon_ok;
						String remark =  "";
						String dataSrcMemo = "C101S01I";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
						//-----------
						list.add(row);
					}
				}				
				if(true){
					String[] txid_arr = new String[]{CrsUtil.EJ_TXID_B36, CrsUtil.EJ_TXID_D10, CrsUtil.EJ_TXID_R20};
					for(String txId :txid_arr){
						List<C101S01U> s01u_list = clsService.findC101S01U_txid(mainId, custId, dupNo, txId);
						if(s01u_list.size()>0){
							String oid = mainId+custId+dupNo+"_"+txId;						
							String dataType = txId;
							String dataTypeDesc = c101s01e_Z13_Z21_dataTypeDesc(txId);
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(s01u_list.get(0).getSendTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark =  "";
							String dataSrcMemo = "C101S01U";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");						
							//-----------
							list.add(row);	
						}						
					}						
				}
				if(true){
					// RPA 司法院受監護/輔助宣告資料				
					C101S04W c101S04w = this.rpaProcessService.getC101S04WBy(mainId, custId);
					if(null != c101S04w){
						String oid = c101S04w.getOid();			
						String dataType = CrsUtil.RPA_TXID_FA;
						String dataTypeDesc = CrsUtil.RPA_TXID_FA_DESC;
						String fileSeq = "0";
						String queryTime = CapDate.getDateTimeFormat(c101S04w.getQueryTime());
						String link = prop_cls1131.getProperty("C101S01S.open");
						String dataStatus = LMSUtil.getRpaStatusMap().get(c101S04w.getStatus());
						String remark = c101S04w.getMemo();
						String dataSrcMemo = "C101S04W";
						String dataStatusCode = c101S04w.getStatus();
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, dataStatusCode);						
						//-----------
						list.add(row);	
					}
				}
				if(true){
					// 聯徵T70證券暨期貨違約交割記錄資訊
					C101S02S c101s02s = this.clsService.findLatestC101S02S(custId, dupNo, branchNo);
					if(null != c101s02s){
						
						String originalMainId = c101s02s.getMainId();
						if(!mainId.equals(originalMainId)){
							c101s02s = this.service.copyNewC101S02SAndC101S01U(mainId, c101s02s);
						}
						
						String oid = c101s02s.getOid();			
						String dataType = CrsUtil.EJ_TXID_T70;
						String dataTypeDesc = CrsUtil.EJ_TXID_T70_DESC;
						String fileSeq = "0";
						String queryTime = c101s02s.getQueryTime() == null ? "" : CapDate.formatDate(c101s02s.getQueryTime(), "yyyy-MM-dd");
						String link = prop_cls1131.getProperty("C101S01S.open");
						
						String negFlag = c101s02s.getNegFlag();
						String dataStatus = "";
						if(negFlag != null && StringUtils.isNotBlank(negFlag)){
							dataStatus = "N0".equals(negFlag) || "N1".equals(negFlag) ? icon_ok : icon_warn ;
						}
						
						String remark = "A3".equals(c101s02s.getDataStatus()) ? prop_cls1131.getProperty("C101S02S.ProcessedSuccessfully") : prop_cls1131.getProperty("C101S02S.Processing");
						String dataSrcMemo = "C101S02S";
						String dataStatusCode = c101s02s.getDataStatus();
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, dataStatusCode);						
						//-----------
						list.add(row);	
					}
				}

				if(true){
					// WiseNews 負面新聞查詢
					if(null != c101s01e){
						if (Util.isNotEmpty(c101s01e.getWiseNews())) {
							String oid = c101s01e.getOid();
							String dataType = CrsUtil.WiseNews;
							String dataTypeDesc = CrsUtil.WiseNews_DESC;
							String fileSeq = "0";
							String queryTime = CapDate.getDateTimeFormat(c101s01e.getUpdateTime());
							String link = prop_cls1131.getProperty("C101S01S.open");
							String dataStatus = icon_ok;
							String remark = "";
							String dataSrcMemo = "WISENEWS";
							Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo,"");
							//-----------
							list.add(row);
						}
					}
				}
			}	
		}
		return new CapMapGridResult(list, list.size());
	}
	
	/**
	 * 查詢RPA地政士結果
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult queryRpaResultDetail(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(search);
		Page page = service.findPage(L161S01D.class, pageSetting);

		Map<String, String> codeMap = codeTypeService.findByCodeType("l161s01d_status");
		
		List<L161S01D> list = page.getContent();
		for (L161S01D l161s01d : list) {
			l161s01d.setStatusDesc(codeMap.get(l161s01d.getStatus()));
		}
		
		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();

		fmtMap.put("type", new CodeTypeFormatter(codeTypeService,
				"l161s01d_type", CodeTypeFormatter.ShowTypeEnum.Desc));
		
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), fmtMap);
		return capGridResult;
	}

	public CapGridResult queryC101S01W(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		search.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "incomeItem", "");
		pageSetting.addSearchModeParameters(search);
		pageSetting.addOrderBy("incomeType");
		pageSetting.addOrderBy("incomeItem");
		Page page = null;
		if (isC120M01A) {
			page = service.getC120S01WPage(pageSetting);
		} else {
			page = service.getC101S01WPage(pageSetting);
		}

		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();
		fmtMap.put("incomeItem", new CodeTypeFormatter(codeTypeService,
				"c101s01w_incomeItem", CodeTypeFormatter.ShowTypeEnum.Val_Desc));
		fmtMap.put("incomeType", new IFormatter() {
			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				return "A".equals(in) ? "經常性收入" : "非經常性收入";
			}
		});

		// 第三個參數為formatting
		CapGridResult capGridResult = new CapGridResult(page.getContent(), page.getTotalRow(), fmtMap);
		return capGridResult;
	}
	
	/**
	 * 地政士資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryC101S01Y(ISearch pageSetting,
			PageParameters params) throws CapException {
		ISearch search = createSearchTemplete();
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String custId = Util.nullToSpace(params
				.getString("custId"));
		String dupNo = Util.nullToSpace(params
				.getString("dupNo"));
		Boolean isC120M01A = params.getBoolean("isC120M01A");
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				"custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				"dupNo", dupNo);
		pageSetting.addSearchModeParameters(search);
		pageSetting.addOrderBy("laaYear");
		pageSetting.addOrderBy("laaWord");
		pageSetting.addOrderBy("laaNo");
		pageSetting.addOrderBy("laaName");
		Page page = null;
		if (isC120M01A) {
			page = service.getC120S01YPage(pageSetting);
		} else {
			page = service.getC101S01YPage(pageSetting);
		}
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("laaCtlFlagType", new IBeanFormatter() {
			private static final long serialVersionUID = 1L;

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				GenericBean s01y = (GenericBean) in;
				String laaCtlFlag;
				try {
					laaCtlFlag = CapString.trimNull(s01y.get("laaCtlFlag"));
				} catch (CapException e) {
					laaCtlFlag = "";
				}
				String ctlFlagType = clsService.getCtlFlagType(Util.trim(laaCtlFlag));
				String laaCtlFlagType = "";
				if (Util.equals(ctlFlagType, LMSUtil.地政士黑名單拒絕名單)) {
					laaCtlFlagType = "╳";
				} else if (Util.equals(ctlFlagType, LMSUtil.地政士黑名單警示名單)) {
					laaCtlFlagType = "△";
				}
				return laaCtlFlagType;
			}
		});

		// 第三個參數為formatting
		CapGridResult capGridResult = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return capGridResult;
	}
}
