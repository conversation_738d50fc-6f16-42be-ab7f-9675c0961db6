/* 
 *MisQuotunioService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 聯貸案參貸比率-自行參貸 QUOTUNIO(MIS.ELV38501)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisQuotunioService {
	/**
	 * 刪除
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param unlnType
	 *            1.同業聯貸 2.自行聯貸
	 */
	void delByCntrNo(String cntrNo, String unlnType);

	/**
	 * 刪除
	 * 
	 * @param cntrNo
	 *            額度序號
	 */
	void delByCntrNo(String cntrNo);

	/**
	 * 新增
	 * 
	 * @param dataList
	 *            <pre>
	 *            object[] content
	 *  UNLNTYPE 1.同業聯貸
	 *  		 2.自行聯貸
	 * 
	 *  cntrNo 額度序號
	 *  unitNo 參貸行庫
	 *  mainBH 主辦行 則給Y 其他為 空
	 *  lnAmt 參貸金額
	 *  updater 資料修改人
	 * </pre>
	 */
	public void insert(List<Object[]> dataList);
}
