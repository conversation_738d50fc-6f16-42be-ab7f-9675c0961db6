/* 
 * MisQuotsubServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisQuotsubService;

/**
 * <pre>
 *  科(子)目及其限額檔QUOTSUB (MIS.ELV38401)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
@Service
public class MisQuotsubServiceImpl extends AbstractMFAloanJdbc implements
		MisQuotsubService {

	@Override
	public void insert(String custId, String dupNo, String cntrNo,
			String sDate, String loantp, String chgflag, String oldcurr,
			Double oldquota, String newcurr, Double newquota, String lngu,
			String updater,BigDecimal lmtDays, String lnapFlag) {
		this.getJdbc().update(
				"QUOTSUB.insert",
				new Object[] { custId, dupNo, cntrNo, sDate, loantp, chgflag,
						oldcurr, oldquota, newcurr, newquota, lngu, updater,
						null, null,lmtDays, lnapFlag });

	}

	@Override
	public List<Map<String, Object>> selByUniqueKey(String custId,
			String dupNo, String cntrNo, String sDate, String loanTp) {
		return this.getJdbc().queryForList("QUOTSUB.selByUniqueKey",
				new Object[] { custId, dupNo, cntrNo, sDate, loanTp });

	}

	@Override
	public void update(String chgFlag, String oldCurr, Double oldQuota,
			String newCurr, Double newQuota, String lngu, String updater,
			String custId, String dupNo, String cntrNo, String sDate,
			String loanTp,BigDecimal lmtDays, String lnapFlag) {
		
		String otherConditons = null;
		if(lmtDays != null){
			//UPDATE 來源為科目時，要一併更新清償期限
			otherConditons = " ,DURATION = ? ";
			this.getJdbc().updateByCustParam(
					"QUOTSUB.update",
					new Object[] { otherConditons },
					new Object[] { chgFlag, oldCurr, oldQuota, newCurr, newQuota,
							lngu, updater, lnapFlag,lmtDays, custId, dupNo, cntrNo, sDate, loanTp });
		}else{
			//UPDATE 來源為科子目限額/合併限額時，因為沒有清償期限，為免蓋掉原值，所以不要更新清償期限
			otherConditons = " ";
			this.getJdbc().updateByCustParam(
					"QUOTSUB.update",
					new Object[] { otherConditons },
					new Object[] { chgFlag, oldCurr, oldQuota, newCurr, newQuota,
							lngu, updater, lnapFlag, custId, dupNo, cntrNo, sDate, loanTp });
		}
		

	}

	@Override
	public Map<String, Object> selByUniqueKeyMaxSdate(String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForMap("QUOTSUB.selByMaxSdate",
				new Object[] { custId, dupNo, cntrNo });
	}
	@Override
	public Map<String, Object> selByUniqueKeyMaxSdate2(String custId,
			String dupNo, String cntrNo,String loanTp) {
		return this.getJdbc().queryForMap("QUOTSUB.selByMaxSdate2",
				new Object[] { custId, dupNo, cntrNo, loanTp });
	}
	@Override
	public Map<String, Object> selByUniqueKey2(String custId,
			String dupNo, String cntrNo,String loanTp,String sDate) {
		return this.getJdbc().queryForMap("QUOTSUB.selByUniqueKey2",
				new Object[] { custId, dupNo, cntrNo, loanTp,sDate });
	}
	
	@Override
	public Map<String, Object> selBySdate(String custId, String dupNo,
			String cntrNo, String sDate) {
		return this.getJdbc().queryForMap("QUOTSUB.selBySdate",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public int delBySdate(String custId, String dupNo, String cntrNo,
			String sDate) {
		return this.getJdbc().update("QUOTSUB.delBySdate",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public int updateChgflagBySdate(String custId, String dupNo, String cntrNo,
			String sDate) {
		return this.getJdbc().update("QUOTSUB.updateChgflagBySdate",
				new Object[] { custId, dupNo, cntrNo, sDate });
	}

	@Override
	public int insetSelectOldData(String custId, String dupNo, String cntrNo,
			String sDate, String OLDSDATE, String updater) {
		sDate = StringEscapeUtils.escapeSql(sDate);
		updater = StringEscapeUtils.escapeSql(updater);
		return this.getJdbc().updateByCustParam(
				"QUOTSUB.insetSelChgflagBySdate",
				new Object[] { "'" + sDate + "'", "'" + updater + "'" },
				new Object[] { custId, dupNo, cntrNo, OLDSDATE });
	}

}
