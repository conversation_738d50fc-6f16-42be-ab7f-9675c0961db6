package com.mega.eloan.lms.batch.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01E;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

/**
 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
 */
@Service("lmsbatch0012serviceimpl")
public class LmsBatch0012ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	SysParameterService sysParamService;

	@Resource
	C241M01ADao c241m01aDao;

	@Resource
	C241M01EDao c241m01eDao;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	RetrialService retrialService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0009ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0012ServiceImpl執行成功！");
		}

		return result;
	}

	/**
	 * J-111-0515_05097_B1001 Web
	 * e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
	 */
	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		StringBuffer errMsg = new StringBuffer("");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			errMsg.append("無法取得完整資料期間 " + dataStartDate + "~" + dataEndDate
					+ " ");
			System.out.println(errMsg.toString());
			return errMsg.toString();

		}

		// ELF492
		List<Map<String, Object>> listMap = misDBService.get_ELF492_By_LrDate(
				dataStartDate, dataEndDate);
		if (listMap != null && !listMap.isEmpty()) {
			for (Map<String, Object> elf492Map : listMap) {
				String ELF492_UNID = Util.trim(MapUtils.getString(elf492Map,
						"ELF492_UNID"));
				if (Util.notEquals(ELF492_UNID, "")) {
					C241M01A c241m01a = c241m01aDao.findByMainId(ELF492_UNID);
					if (c241m01a != null) {
						String ELF492_APPRID = Util.trim(this
								.getApprIdForElf492(c241m01a));
						if (Util.notEquals(ELF492_APPRID, "")) {
							// 更新ELF492

							logger.info("[LmsBatch0012ServiceImpl]====> ELF492_UNID =["
									+ ELF492_UNID
									+ "]，ELF492_APPRID =["
									+ ELF492_APPRID + "]");

							misDBService.updateELF942_ApprId_By_Unid(
									ELF492_UNID, ELF492_APPRID);
						}
					}
				}

			}
		}

		return errMsg.toString();
	}

	String getApprIdForElf492(C241M01A c241m01a) {
		String elf492_apprId = "";
		List<C241M01E> c241m01e_list = retrialService
				.findC241M01E_c241m01a(c241m01a);

		// BRANCHTYPE
		// static final String 受檢單位 = "1";
		// static final String 覆審單位 = "2";
		List<C241M01E> c241m01e_L1 = retrialService
				.findC241M01E_byBranchTypeStaffJob(c241m01e_list, "2", "L1");

		if (CollectionUtils.isNotEmpty(c241m01e_L1)) {
			elf492_apprId = c241m01e_L1.get(0).getStaffNo();
		}

		logger.info("[LmsBatch0012ServiceImpl]====> ELOAN覆審報告表=["
				+ c241m01a.getMainId() + "]，覆審經辦 =[" + elf492_apprId + "]");

		return elf492_apprId;
	}

}
