<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Configure PUBLIC "-//Jetty//Configure//EN" "https://jetty.org/configure_10_0.dtd">


<!-- Jndi -->
<Configure class="org.eclipse.jetty.webapp.WebAppContext">
 <New id="ELOANDB_COM" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ELOANDB_COM</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <!--**************  *************-->
    <Set name="jdbcUrl">jdbc:db2://**************:50000/ELOANDB:currentSchema=COM;useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">eldbusr</Set>
    <Set name="password">eldbusr</Set>
   </New>
  </Arg>
 </New>

  <New id="ELOANDB_LMS" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ELOANDB_LMS</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://**************:50000/ELOANDB:currentSchema=LMS;useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">eldbusr</Set>
    <Set name="password">eldbusr</Set>
   </New>
  </Arg>
 </New>
 
 <New id="ELOANDB_LMS4COM" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ELOANDB_LMS4COM</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://**************:50000/ELOANDB:currentSchema=LMS;useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">eldbusr</Set>
    <Set name="password">eldbusr</Set>
   </New>
  </Arg>
 </New>

 <New id="icbcrdb" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ICBCRDB</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <!-- **************:50000/icbcrdb **************:50001/icbcadb   **************:50000/icbcrdb -->
    <!-- 透過主機HTTPS 取得借款人基本資料， 要改85的系統設定維護   MFHTTP_GW_URL   http://*************:9084/gw-web/app/，另外86後台管理的系統設定維護SYS_GW_ALLOW_IP_LIST 也要確認有白IP-->
    <Set name="jdbcUrl">jdbc:db2://**************:50000/icbcrdb:useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">eloanweb</Set>
    <Set name="password">eloan123</Set>
    <!--<Set name="user">ejcic</Set>
    <Set name="password">ejcic123</Set>-->
   </New>
  </Arg>
 </New>
 <New id="DWOTS" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/DWOTS</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://***************:50002/DWOTS:useJDBC4ColumnNameAndLabelSemantics=2;</Set>  
    <!-- <Set name="jdbcUrl">jdbc:db2://**************:50002/DWOTS:useJDBC4ColumnNameAndLabelSemantics=2;</Set>-->
    <Set name="user">eluser1</Set>
    <Set name="password">Eler1234!@#$4321</Set>
   </New>
  </Arg>
 </New>
  <New id="ODS" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ODS</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://**************:50000/ODS:useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <!-- <Set name="user">db2batch</Set>
    <Set name="password">db2batch</Set>-->
    <Set name="user">ods_eloanweb</Set>
    <Set name="password">Ods_eloanweb1234</Set>
   </New>
  </Arg>
 </New>
 <New id="ETCHDB" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/ETCHDB</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://***************:60000/ETCHDB:useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">etchdb</Set>
    <Set name="password">password</Set>
   </New>
  </Arg>
 </New>
  <New id="EJCICT" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/EJCICDB</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.db2.jcc.DB2Driver</Set>
    <Set name="jdbcUrl">jdbc:db2://***************:60000/EJCICT:useJDBC4ColumnNameAndLabelSemantics=2;</Set>
    <Set name="user">ejcic</Set>
    <Set name="password">abcde</Set>
   </New>
  </Arg>
 </New>
 
 <New id="OBSDB_COM" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_COM</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_TPE" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_TPE</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_0C3" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_0C3</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_0E3" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_0E3</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_0A2" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_0A2</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_0B0" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_0B0</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 <New id="OBSDB_0B9" class="org.eclipse.jetty.plus.jndi.Resource">
  <Arg>jdbc/OBSDB_0B9</Arg>
  <Arg>
   <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
    <Set name="driverClass">com.ibm.as400.access.AS400JDBCDriver</Set>
    <Set name="jdbcUrl">jdbc:as400://**************</Set>
    <Set name="user">ELUSER</Set>
    <Set name="password">passw0rd</Set>
   </New>
  </Arg>
 </New>
 
 
 <New id="TEJDB" class="org.eclipse.jetty.plus.jndi.Resource">
    <Arg>jdbc/TEJDB</Arg>
    <Arg>
       <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
          <Set name="driverClass">com.microsoft.sqlserver.jdbc.SQLServerDriver</Set>
          <Set name="jdbcUrl">jdbc:sqlserver://**************:1433;DatabaseName=tejmondb</Set>
          <Set name="user">tmicbuser</Set>
          <Set name="password">tmicbpasswd</Set>
       </New>
    </Arg>
 </New>
 
 <New id="MEGAIMAGEDB" class="org.eclipse.jetty.plus.jndi.Resource">
    <Arg>jdbc/MEGAIMAGEDB</Arg>
    <Arg>
       <New class="com.mchange.v2.c3p0.ComboPooledDataSource">
          <Set name="driverClass">com.microsoft.sqlserver.jdbc.SQLServerDriver</Set>
          <Set name="jdbcUrl">***********************************************************</Set>
          <Set name="user">eloanSys</Set>
          <Set name="password">eloansys123</Set>
       </New>
    </Arg>
 </New>
 
</Configure>