var _handler = "cls2901formhandler";

$(document).ready(function(){
	
	IntroductionSource.initEvent();
	
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	load_item().done(function(){
		$("#tabForm").find("[name=lnflag][value=E]").closest("tr").remove();
		
		$.form.init({
			formHandler:_handler, 
			formAction:'query', 
			loadSuccess:function(json){
				
				// 控制頁面 Read/Write
				if(!$("#buttonPanel").find("#btnSave").is("button")) {
					tabForm.lockDoc();				
				}
				
				tabForm.injectData(json);
				
				if(json.introduceSrc == '3'){
					tabForm.find("select#megaCode").trigger("change");
					tabForm.find("select#subUnitNo").val(json.subUnitNo);
				}
	
				IntroductionSource.show(json.introduceSrc);
			}
		});
	});
	
	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction({'allowIncomplete':'Y'}).done(function(json){
			if(json.saveOkFlag){
				var dyna = [];
				if(true){
					dyna.push(i18n.def.saveSuccess);
				}	
				
				if(json.IncompleteMsg){
					dyna.push(json.IncompleteMsg);
				}
				if(json.promptMsg){
					dyna.push(json.promptMsg);
				}
				
				API.showMessage(dyna.join("<br/>-------------------<br/>"));
			}
        });
	}).end().find("#btnSend,#btnRemove").click(function(){ 
		//借用 btnRemove 當作「解除」 
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	flowAction({'decisionExpr':'呈主管'});    	
    	        	}
    	    	});
    		}
    	});
	}).end().find("#btnInputRemove").click(function(){ 
		var param = {'mainOid':$("#mainOid").val() };
		
		var $div = $("#divReg_dcMemo");		
		//============
		//先清空值, 再填入 抓回的結果		
		$div.find("#reg_dcMemo").val("");
		
		$div.thickbox({
	        title: $("#btnInputRemove").text(),
	        width: 850, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                	if (!$("#reg_dcMemo_form").valid()) {
                        return;
                    }	
                	$.ajax({ type: "POST", handler: _handler
            			, data:$.extend( {
            					formAction: "inputRemove",
            					'dcMemo': $("#reg_dcMemo").val()
            			  }, param)
            			, success: function(json_inputRemove){
            				                					
            				var tabForm = $("#tabForm");
                			tabForm.injectData(json_inputRemove);
                	
                			$.thickbox.close();
            				
            			}
                	});
                	
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
	    });    	
		
	}).end().find("#btnAccept,#btnAcceptRemove").click(function(){	
		//分行[受檢單位]主管-覆核
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		var btnid = this.id;
		var decisionExprExtend = "";
		if(btnid == "btnAcceptRemove"){
			decisionExprExtend = "解除";
    	}
    	
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			//dyna.push("	<table><tr><td>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label></p>");
			//dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	
                    	flowAction({'decisionExpr': decisionExprExtend +'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr': decisionExprExtend + '退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});

	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                		formAction: "saveMain"
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                ),                
                success: function(json){
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            }
        });
	}
	
	var clear_introduceSrc_box = function(opts){
		tabForm.find('[name=introduceSrc]').removeAttr('checked');
	}
	var clear_introduceSrc_1 = function(opts){
		tabForm.injectData({ 'megaEmpNo':''
			});
	}
	var clear_introduceSrc_2 = function(opts){
		tabForm.injectData({ 'agntNo':''
			});
	}
	var clear_introduceSrc_3 = function(opts){
		tabForm.injectData({ 'megaCode':''
				, 'subUnitNo':''
 				, 'subEmpNo':''
 				, 'subEmpNm':''
			});	
	}
	var clear_introduceSrc_5 = function(opts){
		tabForm.injectData({ 'srcMemo':''
			});
	}
	 if(true){ // J-107-0136
		 tabForm.find("select#megaCode").change(function(){
     		var val_mega_code = $(this).val();
     		if(val_mega_code==''){
//     			ilog.debug("call[megaCode='']change");
     			tabForm.find("select#subUnitNo").setItems({});
     			tabForm.find("#subEmpNo").val("");
     			tabForm.find("#subEmpNm").val("");
     		}else{
//     			ilog.debug("call[megaCode="+val_mega_code+"]change");
	        		var key_sub_unitNo = ('LNF13E_SUB_UNITNO_' + val_mega_code);
	        		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	        		var item = CommonAPI.loadCombos(key_sub_unitNo);
	        		tabForm.find("select#subUnitNo").setItems({ item: item[key_sub_unitNo] , format: "{value} - {key}" });
     		}
     	});	
     	
     	tabForm.find("input#category").change(function(){
     		var val_category = $(this).val();
     		if(val_category=='I'){
     			clear_introduceSrc_box();
     			clear_introduceSrc_1();
     			clear_introduceSrc_2();
     			clear_introduceSrc_3();
     			clear_introduceSrc_5();
     		}else{
     		}
     	});	
     	
     	tabForm.find("input#introduceSrc").change(function(){
     		var val_introduceSrc = $(this).val();
     		if(val_introduceSrc=='1'){
//     			clear_introduceSrc_1();
     			clear_introduceSrc_2();
     			clear_introduceSrc_3();
     			clear_introduceSrc_5();
     		}else if(val_introduceSrc=='2'){
     			clear_introduceSrc_1();
//     			clear_introduceSrc_2();
     			clear_introduceSrc_3();
     			clear_introduceSrc_5();
     		}else if(val_introduceSrc=='3'){
     			clear_introduceSrc_1();
     			clear_introduceSrc_2();
//     			clear_introduceSrc_3();
     			clear_introduceSrc_5();
     		}else if(val_introduceSrc=='5'){
     			clear_introduceSrc_1();
     			clear_introduceSrc_2();
     			clear_introduceSrc_3();
//     			clear_introduceSrc_5();
     		}else{
     		}
     	});	
     }
});

/* build */
function load_item(){
	var dfd = $.Deferred();
	var $formObject = $("#tabForm");
	
	var $div = $formObject.find("[itemType]");
    var allKey = [];
    $div.each(function(){
        allKey.push($(this).attr("itemType"));
    });
    var item = API.loadCombos(allKey);
	var len = $div.length;
    $div.each(function(index, element){
		if (index == len - 1) {
            dfd.resolve();
        }
		
        var $obj = $(this);
        var itemType = $obj.attr("itemType");
        if (itemType) {
            var format = "{value} - {key}";
            $obj.setItems({
                space: $obj.attr("space") || true,
                item: item[itemType],
                format: format,
                size: $obj.attr("itemSize")
            });
        }
    });
	
	return dfd.promise();	
}

var IntroductionSource = {
	
	initEvent: function(){
		
		$("#introduceSrc").change(function(){
			IntroductionSource.change();
		});
		
		IntroductionSource.loadGridInfo();
		
		$("#importRealEstateAgentInfo").click(function(){
			
			var custId = $("#custId").val();
			var dupNo = $("#dupNo").val();
			if(custId == '' || dupNo == ''){
				CommonAPI.showErrorMessage('請輸入被通報統一編號及報姓名/名稱');
				return;
			}
			
			$("#realEstateGrid").jqGrid("setGridParam", {
				postData: {
					tabFormMainId: $("#mainOid").val(),
					flag: "N",
					custId: custId,
					dupNo: dupNo
				},
				search: true
			}).trigger("reloadGrid");

			IntroductionSource.openBox();
		})
		
		$("#importCustOrComButton").click(function(){
			IntroductionSource.importCustomerOrCompanyInfo();
		})
		
		$("input[name=packLoan]").change(function(){
			IntroductionSource.processBatchHouseholdLoan($(this).val());
		});
		
		IntroductionSource.initIntroductionSource();
	},
	
	initIntroductionSource: function(){
		

	},
	
	show: function(introduceSrc){

		switch (introduceSrc) {
		    case '1':
		        $("#employeeDiv").show();
		        break;
			case '2':
		        $("#realEstateAgentDiv").show();
				$("#introducerNameDiv").show();
		        break;
		    case '3':
		        $("#megaSubCompanyDiv").show();
		        break;
			case '4':
				$("#introducerNameDiv").show();
				//代書(地政士)引介注意事項
				$("#landsmenNoticeItemDiv").show();
				break;
		    case '5':
				$("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		    case '6':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case '7':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
				break;
			case '8':
		        $("#customerOrCompanyDiv").show();
				$("#importCustOrComSpan").show();
		        break;
		}
	},
	
	change: function(){
		
		$("#employeeDiv").hide();
		$("#realEstateAgentDiv").hide();
		$("#megaSubCompanyDiv").hide();
		$("#customerOrCompanyDiv").hide();
		$("#importCustOrComSpan").hide();
		$("#introducerNameDiv").hide();
		//代書(地政士)引介注意事項
		$("#landsmenNoticeItemDiv").hide();
		
		//行員引介
		$("#employeeDiv").find("input:text").val("");
		//房仲業者引介
		$("#realEstateAgentDiv").find("input:text").val("");
		//金控子公司員工引介
		$("#megaSubCompanyDiv").find("select").val("");
		$("#megaSubCompanyDiv").find("input:text").val("");
		//往來企金戶所屬員工, 本行客戶引介
		$("#customerOrCompanyDiv").find("input:text").val("");
		//引介人姓名
		$("#introducerNameDiv").find("input:text").val("");
		
		IntroductionSource.show($("#introduceSrc").val());
	},
	
	loadGridInfo: function(){
	
		$("#realEstateGrid").iGrid({
			height: 100,
			handler: "cls2901gridhandler",
			sortname: 'estateType|estateSubType',
			sortorder: 'asc|asc',
			action: "importRealEstateAgentInfo",
			postData: {
				tabFormMainId: $("#mainOid").val(),
				flag: "N",
				custId: $("#custId").val(),
				dupNo: $("#dupNo").val()
			},
			loadComplete: function(){
				$('#realEstateGrid tr').click(function(e){
					//click 帶入資料至畫面後 go to top
					e.preventDefault();
				});
			},
			colModel: [{
				colHeader: i18n.cls2901m01["C900M01J.agntNo"],//引介房仲代號
				width: 20,
				name: 'AGNTNO',
				sortable: true,
				formatter: 'click',
				onclick: function(cellValue, options, rowObject){
					$("#agntNo").val(rowObject.AGNTNO);//引介房仲代號
					$("#introducerName").val(rowObject.AGNTNAME);//引介房仲姓名
					$("#licenseYear").val(rowObject.LICENSEYEAR);//房仲證書(明)字號-年
					$("#licenseWord").val(rowObject.LICENSEWORD);//房仲證書(明)字號-年登字
					$("#licenseNumber").val(rowObject.LICENSENUMBER);//房仲證書(明)字號-編號
					$.thickbox.close();
				}
			}, {
				colHeader: i18n.cls2901m01["C900M01J.introAgntName"],//引介房仲姓名
				name: 'AGNTNAME',
				width: 30
			}, {
				colHeader: i18n.cls2901m01["C900M01J.agentCertNo"],//引介房仲證書(明)字號
				name: 'AGENTCERTNO',
				width: 40
			}, {
				name: 'LICENSEYEAR',
				hidden: true
			}, {
				name: 'LICENSEWORD',
				hidden: true
			}, {
				name: 'LICENSENUMBER',
				hidden: true
			}]
		});
	},

	openBox: function(){

		var buttons = {};
		buttons[i18n.def.close] = function(){				
			$.thickbox.close();
        };
		
       	$("#openBox_realEstateIntroduction").thickbox({
            title: i18n.cls2901m01['page01.title.introductionRealEstateAgentInfo'],
            width: 600,
            height: 250,
            modal: true,
			align: "center",
			valign: 'bottom',
            i18n: i18n.def,
            buttons: buttons
        });
	},
	
	importCustomerOrCompanyInfo: function(){

		AddCustAction.open({
    		handler: 'cls1151m01formhandler',
			action : 'importCustomerOrCompanyInfo',
			data : {
            },
			callback : function(json){					
            	// 關掉 AddCustAction 的 
            	$.thickbox.close();					
				$("#introCustId").val(json.introCustId);
				$("#introDupNo").val(json.introDupNo);
				$("#introCustName").val(json.introCustName);
				var introduceSrc = $("#introduceSrc").val();
				if(introduceSrc == '9' || introduceSrc == 'A'){
					$("#introducerName").val(json.introCustName)
				}
			}
		});
	},
	
	processBatchHouseholdLoan: function(isBatchLoan){

//		$("#introduceSrc").attr("disabled", false);
		if(isBatchLoan == "Y"){
			$("#introduceSrc").val("7").trigger('change');
//			$("#introduceSrc").attr("disabled", true);
		}
	}
}
