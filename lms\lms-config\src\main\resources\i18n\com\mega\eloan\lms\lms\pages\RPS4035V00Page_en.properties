#==================================================
# \u672c\u884c\u5a49\u537b\u8cc7\u6599\u6a94 grid
lnunid.cUSTID=Customer's UBN
lnunid.dUPNO=Repeated Serial No.
lnunid.rEGDT=Date & Time Of Input
lnunid.rEGBR=Rejecting Branch
lnunid.rEGTELLER=Originating Staff's ID
lnunid.rEFUSECD=Rejected Business
lnunid.rEFUSEDS=Reject Descriptions
lnunid.rFSAUTH=Approval Remark
lnunid.uPDATER=Modifier (Staff ID)
lnunid.tMESTAMP=Date of Amendment
lnunid.cLSCASE=Case Remark
lnunid.cUSTNM=Customer Name
lnunid.title01=Please input the UBN of the customer you wish to inquire rejection history
lnunid.title02=[Attention] Error in the length of customer's UBN
lnunid.title03=You have not entered an UBN for the rejection history search; do you wish to cancel your login?
lnunid.title04=Reject History Inquiry
lnunid.title05=Inquiry date
lnunid.title06=Inquirer
lnunid.title07=The Bank's Rejection History
lnunid.title08=The Financial Holding Group's Rejection History
lnunid.title09=Select Borrower of entered UBN
lnunid.title10=Rejected Business
lnunid.title11=Card Debt Rejection
lnunid.clscaseK=Card Debt
lnunid.clscaseC=Personal Banking
lnunid.clscaseL=Corporate Banking
lnunid.clscaseF=Foreign Currency
lnunid.noCase=No Rejection Record
lnunid.bt01=Data Inquiry
lnunid.statuscd=\u5a49\u537b\u72c0\u614b
lnunid.memo=\u203b\u5a49\u537b\u72c0\u614b\u5206\u70ba1(\u63a7\u7ba1) ;2(\u8b66\u793a);D(\u522a\u9664)\u3002\u5982\u6b32\u4fee\u6539\u9700\u81f3E-LOAN\u7cfb\u7d71\u7c3d\u6848\u4e26\u7d93\u5340\u57df\u71df\u904b\u4e2d\u5fc3\u6216\u6388\u7ba1\u8655\u6838\u51c6\u5f8c\u624d\u80fd\u7570\u52d5\u3002
# MIS\u91d1\u63a7\u50b3\u56de\u5a49\u537b\u8cc7\u6599\u6a94
lnunid02.cUSTID=Customer's UBN
lnunid02.dUPNO=Repeated Serial No.
lnunid02.rEGDT=Date & Time Of Input
lnunid02.rEGBR=Originating Branch
lnunid02.rEGTELLER=Originating Staff's ID
lnunid02.rEFUSECD=Reject Code
lnunid02.rEFUSEDS=Reject Descriptions
lnunid02.rFSAUTH=Approval Remark
lnunid02.uPDATER=Modifier (Staff ID)
lnunid02.tMESTAMP=Date of Amendment
lnunid02.cLSCASE=Personal Banking Case Remark
lnunid02.cARDREJ=Card Holder's Rejection Remark
lnunid02.cUSTNM=Customer Name
