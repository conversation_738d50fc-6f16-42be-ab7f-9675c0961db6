/* 
 * C101S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check2;
import com.mega.eloan.lms.validation.group.ImportCheck;
import com.mega.eloan.lms.validation.group.SaveCheck;

/** 個金服務單位檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01B", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 服務單位名稱 **/
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 250)
	@Column(name = "COMNAME", length = 250, columnDefinition = "VARCHAR(250)")
	private String comName;

	/**
	 * 服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	@Size(max = 10)
	@Column(name = "COMELAND", length = 10, columnDefinition = "VARCHAR(10)")
	private String comEland;

	/** 服務單位地址(縣市) **/
	@Size(max = 2)
	@Column(name = "COMCITY", length = 2, columnDefinition = "VARCHAR(2)")
	private String comCity;

	/**
	 * 服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	@Size(max = 5)
	@Column(name = "COMZIP", length = 5, columnDefinition = "VARCHAR(5)")
	private String comZip;

	/**
	 * 服務單位地址
	 * <p/>
	 * 64個全型字
	 */
	@Size(max = 192)
	@Column(name = "COMADDR", length = 192, columnDefinition = "VARCHAR(192)")
	private String comAddr;

	/** 服務單位地址(標的) **/
	@Size(max = 300)
	@Column(name = "COMTARGET", length = 300, columnDefinition = "VARCHAR(300)")
	private String comTarget;

	/** 服務單位電話 **/
	@Size(max = 150)
	@Column(name = "COMTEL", length = 150, columnDefinition = "VARCHAR(150)")
	private String comTel;

	/** 到職日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "WORKDATE", columnDefinition = "DATE")
	private Date workDate;

	/**
	 * 職業別大類
	 * <p/>
	 * 詳註一
	 */
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Size(max = 2)
	@Column(name = "JOBTYPE1", length = 2, columnDefinition = "CHAR(2)")
	private String jobType1;

	/**
	 * 職業別細項
	 * <p/>
	 * 詳註一
	 */
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@Size(max = 1)
	@Column(name = "JOBTYPE2", length = 1, columnDefinition = "CHAR(1)")
	private String jobType2;

	/**
	 * 職稱
	 * <p/>
	 * (單選)<br/>
	 * 上市或上櫃公司之負責人|g<br/>
	 * 上市或上櫃公司之總經理、副總或執行長|h<br/>
	 * 上市或上櫃公司之董監事及主管及人員|a<br/>
	 * 非上市或上櫃公司之負責人|i<br/>
	 * 非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 * 非上市或上櫃公司之董監事及主管及人員|b<br/>
	 * 其他機構主管級人員|c<br/>
	 * 一般職員|d<br/>
	 * 服務人員|e<br/>
	 * 其他|f
	 */
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class, Check2.class })
	@Size(max = 1)
	@Column(name = "JOBTITLE", length = 1, columnDefinition = "CHAR(1)")
	private String jobTitle;

	/** 年資 **/
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			ImportCheck.class })
	@Digits(integer = 2, fraction = 2, groups = SaveCheck.class)
	@Column(name = "SENIORITY", columnDefinition = "DECIMAL(4,2)")
	private BigDecimal seniority;

	/** 年薪(幣別) **/
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "PAYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String payCurr;

	/**
	 * 年薪(金額)
	 * <p/>
	 * (提供報送聯徵DBR22倍用)
	 */
	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
			Check2.class })
	@Digits(integer = 13, fraction = 0, groups = { SaveCheck.class,
			Check2.class })
	@Column(name = "PAYAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal payAmt;

	/**
	 * 其他收入項目 select * from com.bcodetype where codetype='cls1131m01_othType' <p/>
	 * 100/12/08新增<br/>
	 * (複選) 01|02|…<br/>
	 * 01.薪資所得<br/>
	 * 02.利息所得<br/>
	 * 03.營利所得<br/>
	 * 04.租賃所得<br/>
	 * 05.權利金所得<br/>
	 * 06.自力耕作、漁、牧、林、礦所得<br/>
	 * 07.執行業務所得<br/>
	 * 08.著作人、稿費、版稅、鐘點費等<br/>
	 * 09.財產交易所得<br/>
	 * 10.競技、競賽及機會中獎獎金<br/>
	 * 11.退職所得<br/>
	 * 12.其他所得<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@Size(max = 36)
	@Column(name = "OTHTYPE", length = 36, columnDefinition = "VARCHAR(36)")
	private String othType;

	/**
	 * 其他收入(幣別)
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 3)
	@Column(name = "OTHCURR", length = 3, columnDefinition = "CHAR(3)")
	private String othCurr;

	/**
	 * 其他收入(金額)
	 * <p/>
	 * 100/12/08新增<br/>
	 * (提供報送聯徵DBR22倍用)<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	@NotNull(message = "{required.message}", groups = {SaveCheck.class,Check2.class})
	@NotEmpty(message = "{required.message}", groups = {SaveCheck.class,Check2.class})
	@Digits(integer = 13, fraction = 0, groups = { SaveCheck.class,
			Check2.class })
	@Column(name = "OTHAMT", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal othAmt;

	/** 經歷 **/
	@Size(max = 250, groups = SaveCheck.class)
	@Column(name = "EXPERIENCE", length = 250, columnDefinition = "VARCHAR(250)")
	private String experience;

	/**
	 * 個人所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 */
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "INDOC", length = 1, columnDefinition = "CHAR(1)")
	private String inDoc;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 服務單位統一編號 **/
	@Size(max = 11)
	@Column(name = "JUID", length = 11, columnDefinition = "CHAR(11)")
	private String juId;
	
	/** 服務單位資本總額(新台幣元) **/
	@Column(name = "JUTOTALCAPITAL", columnDefinition = "DECIMAL(19,0)")
	private BigDecimal juTotalCapital;
	
	/** 服務單位實收資本額(新台幣元) **/
	@Column(name = "JUPAIDUPCAPITAL", columnDefinition = "DECIMAL(19,0)")
	private BigDecimal juPaidUpCapital;
	
	/** 服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 6)
	@Column(name = "JUTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String juType;
	
	/** 有無_服務單位統一編號 **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "YNJUID", length = 1, columnDefinition = "CHAR(1)")
	private String ynJuId;
	
	/** 有無_服務單位資本總額 **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "YNJUTOTALCAPITAL", length = 1, columnDefinition = "CHAR(1)")	
	private String ynJuTotalCapital;
	
	
	
	/**
	 * 歡喜信貸職業別大類
	 * <p/>
	 * 詳註一
	 */
	@Size(max = 1)
	@Column(name = "CLSJOBTYPE1", length = 1, columnDefinition = "CHAR(1)")
	private String clsJobType1;

	/**
	 * 歡喜信貸職業別細項
	 * <p/>
	 * 詳註一
	 */
	@Size(max = 3)
	@Column(name = "CLSJOBTYPE2", length = 3, columnDefinition = "CHAR(3)")
	private String clsJobType2;

	/**
	 * 職稱
	 * <p/>
	 * 
	 */
	@Size(max = 4)
	@Column(name = "CLSJOBTITLE", length = 4, columnDefinition = "CHAR(4)")
	private String clsJobTitle;
	
	/**
	 * 歡喜信貸客群(發查決策後寫回)
	 * select * from COM.BCODETYPE where CODETYPE='L140S02A_termGroup';
	 */
	@Size(max = 2)
	@Column(name = "TERMGROUP", length = 2, columnDefinition = "VARCHAR(2)")
	private String termGroup;
	
	/**
	 * 客群子類別(發查決策後寫回)
	 * "A" or ""
	 * A:DBR上限15倍
	 */
	@Size(max = 2)
	@Column(name = "APPLYDBRTYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String applyDBRType;
	
	@Column(name = "ISNPO", length = 1, columnDefinition = "CHAR(1)")
	private String isNPO;

	
	/** 有無_服務單位實收資本額 **/
//	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
//	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "YNJUPAIDUPCAPITAL", length = 1, columnDefinition = "CHAR(1)")
	private String ynJuPaidUpCapital;
	
	/** 薪轉戶註記{Y,N} **/
	@Size(max = 1)
	@Column(name = "PTAFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String ptaFlag;
	
	/** 薪轉戶企業統編 **/
	@Size(max = 10)
	@Column(name = "PTATAXNO", length = 10, columnDefinition = "CHAR(10)")
	private String ptaTaxNo;
	
	/** 薪轉戶企業評等 **/
	@Size(max = 1)
	@Column(name = "PTAGRADE", length = 1, columnDefinition = "CHAR(1)")
	private String ptaGrade;
	
	/** 引入薪轉戶資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "PTADATADT", columnDefinition = "DATE")
	private Date ptaDataDt;
	
	/** 引入0024服務單位  **/
	@Column(name = "CM1_SERVE_COMPANY", length = 45, columnDefinition = "CHAR(45)")
	private String cm1_serve_company;
	
	/** 引入0024服務單位行業對象 **/
	@Column(name = "CM1_JOB_BUSINESS_CODE", length = 6, columnDefinition = "CHAR(6)")
	private String cm1_job_business_code;
	
	/** 引入0024職稱代碼 **/
	@Column(name = "CM1_TITLE_CODE", length = 2, columnDefinition = "CHAR(2)")
	private String cm1_title_code;
		
	/** 引入0024職稱說明 **/
	@Column(name = "CM1_JOB_TITLE", length = 2, columnDefinition = "CHAR(2)")
	private String cm1_job_title;
	
	/** 引入0024資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CM1_DATADT", columnDefinition = "DATE")
	private Date cm1_dataDt;
	
	/** 前職與現職同屬性 (限前一份工作) **/
	@NotNull(message = "{required.message}", groups = SaveCheck.class)
	@NotEmpty(message = "{required.message}", groups = SaveCheck.class)
	@Size(max = 1)
	@Column(name = "ISSAMEWORKATTRIBUTES", length = 1, columnDefinition = "CHAR(1)")
	private String isSameWorkAttributes;

	/**職位別
	 * 1 非藍領
	 * 2 藍領
	 * 3 業務職(底薪+獎金者)
	 */
	@Column(name = "POSITIONTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String positionType;

	/**
	 * A 所得清單/扣繳憑單
	 * B1 薪轉存摺/薪資單
	 * B2 勞保投保明細/個人投保紀錄
	 * B3 營利事業所得申報書/
	 * 銷售額與稅額申報書(401/403/405)
	 * 限負責人申辦
	 * B4 現金收入
	 */
	@Column(name = "MAININCOMETYPE", length = 2, columnDefinition = "VARCHAR(2)")
	private String mainIncomeType;


	/**
	 *  所得清單/扣繳憑單 金額
	 */
	@Column(name = "ITEMAVALUE", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemAvalue;

	/**
	 * 所得清單/扣繳憑單 金額 年化
	 */
	@Column(name = "ITEMAVALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemAvalueYear;

	/**
	 * 薪轉存摺/薪資單 項目1 金額
	 */
	@Column(name = "ITEMB1VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value1;

	/**
	 * 薪轉存摺/薪資單 項目2 金額
	 */
	@Column(name = "ITEMB1VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value2;

	/**
	 * 薪轉存摺/薪資單 項目3 金額
	 */
	@Column(name = "ITEMB1VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value3;

	/**
	 * 薪轉存摺/薪資單 項目4 金額
	 */
	@Column(name = "ITEMB1VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value4;

	/**
	 * 薪轉存摺/薪資單 項目5 金額
	 */
	@Column(name = "ITEMB1VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value5;

	/**
	 * 薪轉存摺/薪資單 項目6 金額
	 */
	@Column(name = "ITEMB1VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1value6;

	/**
	 * 薪轉存摺/薪資單 三節獎金
	 */
	@Column(name = "ITEMB1HOLIDAYBONUS", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1HolidayBonus;

	/**
	 * 薪轉存摺/薪資單 年終獎金
	 */
	@Column(name = "ITEMB1YEARENDBONUS", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1YearEndBonus;

	/**
	 * 薪轉存摺/薪資單 金額 年化
	 */
	@Column(name = "ITEMB1VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB1valueYear;

	/**
	 * 勞保投保明細/個人投保紀錄 金額
	 */
	@Column(name = "ITEMB2VALUE", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB2value;

	/**
	 * 勞保投保明細/個人投保紀錄 金額 年化
	 */
	@Column(name = "ITEMB2VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB2valueYear;


	/**
	 * 報表種類：
	 * A:401　
	 * B:403/405
	 */
	@Column(name = "ITEMB3REPORTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String  itemB3ReportType;


	/**
	 * B3 項目1 金額
	 */
	@Column(name = "ITEMB3VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB3value1;

	/**
	 * B3 項目2 金額
	 */
	@Column(name = "ITEMB3VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB3value2;

	/**
	 * B3 項目3 金額
	 */
	@Column(name = "ITEMB3VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB3value3;

	///**
	// * B3 項目4 金額
	// */
	//@Column(name = "ITEMB3VALUE4", columnDefinition = "DECIMAL(13,0)")
	//private BigDecimal itemB3value4;
	//
	///**
	// * B3 項目5 金額
	// */
	//@Column(name = "ITEMB3VALUE5", columnDefinition = "DECIMAL(13,0)")
	//private BigDecimal itemB3value5;
	//
	///**
	// * B3 項目6 金額
	// */
	//@Column(name = "ITEMB3VALUE6", columnDefinition = "DECIMAL(13,0)")
	//private BigDecimal itemB3value6;

	/**
	 * B3 產業利潤率
	 */
	@Column(name = "ITEMB3INPROFIT", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal itemB3InProfit;

	/**
	 * B3 持股
	 */
	@Column(name = "ITEMB3HOLDING", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal itemB3Holding;

	/**
	 * B3  金額 年化
	 */
	@Column(name = "ITEMB3VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB3valueYear;

	/**
	 * 現金收入 項目1
	 */
	@Column(name = "ITEMB4VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value1;

	/**
	 * 現金收入 項目2
	 */
	@Column(name = "ITEMB4VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value2;

	/**
	 * 現金收入 項目3
	 */
	@Column(name = "ITEMB4VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value3;

	/**
	 * 現金收入 項目4
	 */
	@Column(name = "ITEMB4VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value4;

	/**
	 * 現金收入 項目5
	 */
	@Column(name = "ITEMB4VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value5;

	/**
	 * 現金收入 項目6
	 */
	@Column(name = "ITEMB4VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4value6;

	/**
	 * 現金收入 年化
	 */
	@Column(name = "ITEMB4VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemB4valueYear;

	/**
	 * 勾選 定存利息
	 */
	@Column(name = "OTHERC1", length = 1, columnDefinition = "CHAR(1)")
	private String otherC1;

	/**
	 * 定存利息 項目1
	 */
	@Column(name = "ITEMC1VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value1;

	/**
	 * 定存利息 項目
	 */
	@Column(name = "ITEMC1VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value2;

	/**
	 * 定存利息 項目3
	 */
	@Column(name = "ITEMC1VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value3;

	/**
	 * 定存利息 項目4
	 */
	@Column(name = "ITEMC1VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value4;

	/**
	 * 定存利息 項目5
	 */
	@Column(name = "ITEMC1VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value5;

	/**
	 * 定存利息 項目6
	 */
	@Column(name = "ITEMC1VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1value6;

	/**
	 * 定存利息 年化
	 */
	@Column(name = "ITEMC1VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC1valueYear;

	/**
	 * 勾選 租金收入
	 */
	@Column(name = "OTHERC2", length = 1, columnDefinition = "CHAR(1)")
	private String otherC2;

	/**
	 * 租金收入 項目1
	 */
	@Column(name = "ITEMC2VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value1;

	/**
	 * 租金收入 項目2
	 */
	@Column(name = "ITEMC2VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value2;

	/**
	 * 租金收入 項目3
	 */
	@Column(name = "ITEMC2VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value3;

	/**
	 * 租金收入 項目4
	 */
	@Column(name = "ITEMC2VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value4;

	/**
	 * 租金收入 項目5
	 */
	@Column(name = "ITEMC2VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value5;

	/**
	 * 租金收入 項目6
	 */
	@Column(name = "ITEMC2VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2value6;

	/**
	 * 定存利息 年化
	 */
	@Column(name = "ITEMC2VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC2valueYear;

	/**
	 * 勾選 退休/退撫/退役收入
	 */
	@Column(name = "OTHERC3", length = 1, columnDefinition = "CHAR(1)")
	private String otherC3;

	/**
	 * 退休/退撫/退役收入 項目1
	 */
	@Column(name = "ITEMC3VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value1;

	/**
	 * 退休/退撫/退役收入 項目2
	 */
	@Column(name = "ITEMC3VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value2;

	/**
	 * 退休/退撫/退役收入 項目3
	 */
	@Column(name = "ITEMC3VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value3;

	/**
	 * 退休/退撫/退役收入 項目4
	 */
	@Column(name = "ITEMC3VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value4;

	/**
	 * 退休/退撫/退役收入 項目5
	 */
	@Column(name = "ITEMC3VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value5;

	/**
	 * 退休/退撫/退役收入 項目6
	 */
	@Column(name = "ITEMC3VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3value6;

	/**
	 * 退休/退撫/退役收入 年化
	 */
	@Column(name = "ITEMC3VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemC3valueYear;

	/**
	 * 勾選 活存/定存
	 */
	@Column(name = "OTHERD4", length = 1, columnDefinition = "CHAR(1)")
	private String otherD4;

	/**
	 * 活存/定存 項目1
	 */
	@Column(name = "ITEMD4VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value1;

	/**
	 * 活存/定存 項目2
	 */
	@Column(name = "ITEMD4VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value2;

	/**
	 * 活存/定存 項目3
	 */
	@Column(name = "ITEMD4VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value3;

	/**
	 * 活存/定存 項目4
	 */
	@Column(name = "ITEMD4VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value4;

	/**
	 * 活存/定存 項目5
	 */
	@Column(name = "ITEMD4VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value5;

	/**
	 * 活存/定存 項目6
	 */
	@Column(name = "ITEMD4VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4value6;

	/**
	 * 活存/定存 年化
	 */
	@Column(name = "ITEMD4VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD4valueYear;

	/**
	 * 勾選 基金
	 */
	@Column(name = "OTHERD5", length = 1, columnDefinition = "CHAR(1)")
	private String otherD5;

	/**
	 * 基金 項目1
	 */
	@Column(name = "ITEMD5VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value1;

	/**
	 * 基金 項目2
	 */
	@Column(name = "ITEMD5VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value2;

	/**
	 * 基金 項目3
	 */
	@Column(name = "ITEMD5VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value3;

	/**
	 * 基金 項目4
	 */
	@Column(name = "ITEMD5VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value4;

	/**
	 * 基金 項目5
	 */
	@Column(name = "ITEMD5VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value5;

	/**
	 * 基金 項目6
	 */
	@Column(name = "ITEMD5VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5value6;

	/**
	 * 基金 年化
	 */
	@Column(name = "ITEMD5VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD5valueYear;

	/**
	 * 勾選 公開發行債券
	 */
	@Column(name = "OTHERD6", length = 1, columnDefinition = "CHAR(1)")
	private String otherD6;

	/**
	 * 公開發行債券 項目1
	 */
	@Column(name = "ITEMD6VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value1;

	/**
	 * 公開發行債券 項目2
	 */
	@Column(name = "ITEMD6VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value2;

	/**
	 * 公開發行債券 項目3
	 */
	@Column(name = "ITEMD6VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value3;

	/**
	 * 公開發行債券 項目4
	 */
	@Column(name = "ITEMD6VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value4;

	/**
	 * 公開發行債券 項目5
	 */
	@Column(name = "ITEMD6VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value5;

	/**
	 * 公開發行債券 項目6
	 */
	@Column(name = "ITEMD6VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6value6;

	/**
	 * 公開發行債券 年化
	 */
	@Column(name = "ITEMD6VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD6valueYear;


	/**
	 * 勾選 人身保險之保單價值準備
	 */
	@Column(name = "OTHERD7", length = 1, columnDefinition = "CHAR(1)")
	private String otherD7;

	/**
	 * 人身保險之保單價值準備 項目1
	 */
	@Column(name = "ITEMD7VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value1;

	/**
	 * 人身保險之保單價值準備 項目2
	 */
	@Column(name = "ITEMD7VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value2;

	/**
	 * 人身保險之保單價值準備 項目3
	 */
	@Column(name = "ITEMD7VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value3;

	/**
	 * 人身保險之保單價值準備 項目4
	 */
	@Column(name = "ITEMD7VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value4;

	/**
	 * 人身保險之保單價值準備 項目5
	 */
	@Column(name = "ITEMD7VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value5;

	/**
	 * 人身保險之保單價值準備 項目6
	 */
	@Column(name = "ITEMD7VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7value6;

	/**
	 * 人身保險之保單價值準備 年化
	 */
	@Column(name = "ITEMD7VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD7valueYear;

	/**
	 * 勾選 上市(櫃)公司股票
	 */
	@Column(name = "OTHERD8", length = 1, columnDefinition = "CHAR(1)")
	private String otherD8;

	/**
	 * 上市(櫃)公司股票 項目1
	 */
	@Column(name = "ITEMD8VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value1;

	/**
	 * 上市(櫃)公司股票 項目2
	 */
	@Column(name = "ITEMD8VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value2;

	/**
	 * 上市(櫃)公司股票 項目3
	 */
	@Column(name = "ITEMD8VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value3;

	/**
	 * 上市(櫃)公司股票 項目4
	 */
	@Column(name = "ITEMD8VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value4;

	/**
	 * 上市(櫃)公司股票 項目5
	 */
	@Column(name = "ITEMD8VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value5;

	/**
	 * 上市(櫃)公司股票 項目6
	 */
	@Column(name = "ITEMD8VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8value6;

	/**
	 * 上市(櫃)公司股票 年化
	 */
	@Column(name = "ITEMD8VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD8valueYear;

	/**
	 * 勾選 其他
	 */
	@Column(name = "OTHERD9", length = 1, columnDefinition = "CHAR(1)")
	private String otherD9;

	/**
	 * 其他 項目1
	 */
	@Column(name = "ITEMD9VALUE1", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value1;

	/**
	 * 其他 項目2
	 */
	@Column(name = "ITEMD9VALUE2", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value2;

	/**
	 * 其他 項目3
	 */
	@Column(name = "ITEMD9VALUE3", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value3;

	/**
	 * 其他 項目4
	 */
	@Column(name = "ITEMD9VALUE4", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value4;

	/**
	 * 其他 項目5
	 */
	@Column(name = "ITEMD9VALUE5", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value5;

	/**
	 * 其他 項目6
	 */
	@Column(name = "ITEMD9VALUE6", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9value6;

	/**
	 * 其他 年化
	 */
	@Column(name = "ITEMD9VALUEYEAR", columnDefinition = "DECIMAL(13,0)")
	private BigDecimal itemD9valueYear;

	/**
	 * 現金收入 折算率
	 */
	@Column(name = "ITEMB4DISRATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal itemB4DisRate;

	/**
	 * 其他收入  折算率
	 */
	@Column(name = "ITEMD9DISRATE", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal itemD9DisRate;

	/** 薪轉戶往來分行 **/
	@Column(name = "PTABRNO", length = 3, columnDefinition = "CHAR(3)")
	private String ptaBrNo;
	
	/** 薪轉戶帳號 **/
	@Column(name = "PTAACTNO", length = 1, columnDefinition = "CHAR(14)")
	private String ptaActNo;
	
	/** 高風險職業註記 **/
//	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
//	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
	@Column(name = "HIGHRISKJOBFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String highRiskJobFlag;
	
	/** 是否有ESG分數 **/
//	@NotEmpty(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
//	@NotNull(message = "{required.message}", groups = { SaveCheck.class,
//			ImportCheck.class })
	@Column(name = "ISHASESGSCORE", length = 1, columnDefinition = "CHAR(1)")
	private String isHasEsgScore;
	
	/** ESG分數 **/
	@Column(name = "ESGSCORE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal esgScore;
	
	/** 年資_月份數 **/
	@Column(name = "SNRM", columnDefinition = "DECIMAL(2,0)")
	private BigDecimal snrM;

	/**
	 * 個人收入明細版本
	 */
	@Column(name = "INCOMEDETAILVER", columnDefinition = "DECIMAL(2,0)")
	private BigDecimal incomeDetailVer;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得服務單位名稱 **/
	public String getComName() {
		return this.comName;
	}

	/** 設定服務單位名稱 **/
	public void setComName(String value) {
		this.comName = value;
	}

	/**
	 * 取得服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 */
	public String getComEland() {
		return this.comEland;
	}

	/**
	 * 設定服務單位地址(eland)
	 * <p/>
	 * Eland代碼
	 **/
	public void setComEland(String value) {
		this.comEland = value;
	}

	/** 取得服務單位地址(縣市) **/
	public String getComCity() {
		return this.comCity;
	}

	/** 設定服務單位地址(縣市) **/
	public void setComCity(String value) {
		this.comCity = value;
	}

	/**
	 * 取得服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 */
	public String getComZip() {
		return this.comZip;
	}

	/**
	 * 設定服務單位地址(鄉鎮市區)
	 * <p/>
	 * 郵地區號
	 **/
	public void setComZip(String value) {
		this.comZip = value;
	}

	/**
	 * 取得服務單位地址
	 * <p/>
	 * 64個全型字
	 */
	public String getComAddr() {
		return this.comAddr;
	}

	/**
	 * 設定服務單位地址
	 * <p/>
	 * 64個全型字
	 **/
	public void setComAddr(String value) {
		this.comAddr = value;
	}

	/** 取得服務單位地址(標的) **/
	public String getComTarget() {
		return this.comTarget;
	}

	/** 設定服務單位地址(標的) **/
	public void setComTarget(String value) {
		this.comTarget = value;
	}

	/** 取得服務單位電話 **/
	public String getComTel() {
		return this.comTel;
	}

	/** 設定服務單位電話 **/
	public void setComTel(String value) {
		this.comTel = value;
	}

	/** 取得到職日期 **/
	public Date getWorkDate() {
		return this.workDate;
	}

	/** 設定到職日期 **/
	public void setWorkDate(Date value) {
		this.workDate = value;
	}

	/**
	 * 取得職業別大類
	 * <p/>
	 * 詳註一
	 */
	public String getJobType1() {
		return this.jobType1;
	}

	/**
	 * 設定職業別大類
	 * <p/>
	 * 詳註一
	 **/
	public void setJobType1(String value) {
		this.jobType1 = value;
	}

	/**
	 * 取得職業別細項
	 * <p/>
	 * 詳註一
	 */
	public String getJobType2() {
		return this.jobType2;
	}

	/**
	 * 設定職業別細項
	 * <p/>
	 * 詳註一
	 **/
	public void setJobType2(String value) {
		this.jobType2 = value;
	}

	/**
	 * 取得職稱
	 * <p/>
	 * (單選)<br/>
	 * 上市或上櫃公司之負責人|g<br/>
	 * 上市或上櫃公司之總經理、副總或執行長|h<br/>
	 * 上市或上櫃公司之董監事及主管及人員|a<br/>
	 * 非上市或上櫃公司之負責人|i<br/>
	 * 非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 * 非上市或上櫃公司之董監事及主管及人員|b<br/>
	 * 其他機構主管級人員|c<br/>
	 * 一般職員|d<br/>
	 * 服務人員|e<br/>
	 * 其他|f
	 */
	public String getJobTitle() {
		return this.jobTitle;
	}

	/**
	 * 設定職稱
	 * <p/>
	 * (單選)<br/>
	 * 上市或上櫃公司之負責人|g<br/>
	 * 上市或上櫃公司之總經理、副總或執行長|h<br/>
	 * 上市或上櫃公司之董監事及主管及人員|a<br/>
	 * 非上市或上櫃公司之負責人|i<br/>
	 * 非上市或上櫃公司之總經理、副總或執行長|j<br/>
	 * 非上市或上櫃公司之董監事及主管及人員|b<br/>
	 * 其他機構主管級人員|c<br/>
	 * 一般職員|d<br/>
	 * 服務人員|e<br/>
	 * 其他|f
	 **/
	public void setJobTitle(String value) {
		this.jobTitle = value;
	}

	/** 取得年資 **/
	public BigDecimal getSeniority() {
		return this.seniority;
	}

	/** 設定年資 **/
	public void setSeniority(BigDecimal value) {
		this.seniority = value;
	}

	/** 取得年薪(幣別) **/
	public String getPayCurr() {
		return this.payCurr;
	}

	/** 設定年薪(幣別) **/
	public void setPayCurr(String value) {
		this.payCurr = value;
	}

	/**
	 * 取得年薪(金額)
	 * <p/>
	 * (提供報送聯徵DBR22倍用)
	 */
	public BigDecimal getPayAmt() {
		return this.payAmt;
	}

	/**
	 * 設定年薪(金額)
	 * <p/>
	 * (提供報送聯徵DBR22倍用)
	 **/
	public void setPayAmt(BigDecimal value) {
		this.payAmt = value;
	}

	/**
	 * 取得其他收入項目
	 * <p/>
	 * 100/12/08新增<br/>
	 * (複選) 01|02|…<br/>
	 * 01.薪資所得<br/>
	 * 02.利息所得<br/>
	 * 03.營利所得<br/>
	 * 04.租賃所得<br/>
	 * 05.權利金所得<br/>
	 * 06.自力耕作、漁、牧、林、礦所得<br/>
	 * 07.執行業務所得<br/>
	 * 08.著作人、稿費、版稅、鐘點費等<br/>
	 * 09.財產交易所得<br/>
	 * 10.競技、競賽及機會中獎獎金<br/>
	 * 11.退職所得<br/>
	 * 12.其他所得<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getOthType() {
		return this.othType;
	}

	/**
	 * 設定其他收入項目
	 * <p/>
	 * 100/12/08新增<br/>
	 * (複選) 01|02|…<br/>
	 * 01.薪資所得<br/>
	 * 02.利息所得<br/>
	 * 03.營利所得<br/>
	 * 04.租賃所得<br/>
	 * 05.權利金所得<br/>
	 * 06.自力耕作、漁、牧、林、礦所得<br/>
	 * 07.執行業務所得<br/>
	 * 08.著作人、稿費、版稅、鐘點費等<br/>
	 * 09.財產交易所得<br/>
	 * 10.競技、競賽及機會中獎獎金<br/>
	 * 11.退職所得<br/>
	 * 12.其他所得<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setOthType(String value) {
		this.othType = value;
	}

	/**
	 * 取得其他收入(幣別)
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public String getOthCurr() {
		return this.othCurr;
	}

	/**
	 * 設定其他收入(幣別)
	 * <p/>
	 * 100/12/08新增<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setOthCurr(String value) {
		this.othCurr = value;
	}

	/**
	 * 取得其他收入(金額)
	 * <p/>
	 * 100/12/08新增<br/>
	 * (提供報送聯徵DBR22倍用)<br/>
	 * ※國內DBU/OBU才需填寫
	 */
	public BigDecimal getOthAmt() {
		return this.othAmt;
	}

	/**
	 * 設定其他收入(金額)
	 * <p/>
	 * 100/12/08新增<br/>
	 * (提供報送聯徵DBR22倍用)<br/>
	 * ※國內DBU/OBU才需填寫
	 **/
	public void setOthAmt(BigDecimal value) {
		this.othAmt = value;
	}

	/** 取得經歷 **/
	public String getExperience() {
		return this.experience;
	}

	/** 設定經歷 **/
	public void setExperience(String value) {
		this.experience = value;
	}

	/**
	 * 取得個人所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 */
	public String getInDoc() {
		return this.inDoc;
	}

	/**
	 * 設定個人所得證明文件
	 * <p/>
	 * (單選)<br/>
	 * 個人綜合所得申報資料|1<br/>
	 * 扣繳憑單|2<br/>
	 * 薪資轉帳存摺|3<br/>
	 * 勞保薪資|4<br/>
	 * 租賃契約|5<br/>
	 * 其他收入證明|6
	 **/
	public void setInDoc(String value) {
		this.inDoc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
	/** 取得服務單位統一編號 **/
	public String getJuId() {
		return juId;
	}
	/** 設定服務單位統一編號 **/
	public void setJuId(String juId) {
		this.juId = juId;
	}
	/** 取得服務單位資本總額(新台幣元) **/
	public BigDecimal getJuTotalCapital() {
		return juTotalCapital;
	}
	/** 設定服務單位資本總額(新台幣元) **/
	public void setJuTotalCapital(BigDecimal juTotalCapital) {
		this.juTotalCapital = juTotalCapital;
	}
	/** 取得服務單位實收資本額(新台幣元) **/
	public BigDecimal getJuPaidUpCapital() {
		return juPaidUpCapital;
	}
	/** 設定服務單位實收資本額(新台幣元) **/
	public void setJuPaidUpCapital(BigDecimal juPaidUpCapital) {
		this.juPaidUpCapital = juPaidUpCapital;
	}
	/** 取得服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
	public String getJuType() {
		return juType;
	}
	/** 設定服務單位組織類型{1:獨資,2:合夥,3:無限公司,4:有限公司,5:兩合公司,6:股份有限公司,7:其他,0:N/A} **/
	public void setJuType(String juType) {
		this.juType = juType;
	}
	
	/** 取得有無_服務單位統一編號 **/
	public String getYnJuId() {
		return ynJuId;
	}
	/** 設定有無_服務單位統一編號 **/
	public void setYnJuId(String ynJuId) {
		this.ynJuId = ynJuId;
	}
	
	/** 取得有無_服務單位資本總額 **/
	public String getYnJuTotalCapital() {
		return ynJuTotalCapital;
	}
	/** 設定有無_服務單位資本總額 **/
	public void setYnJuTotalCapital(String ynJuTotalCapital) {
		this.ynJuTotalCapital = ynJuTotalCapital;
	}
	
	/** 取得有無_服務單位實收資本額 **/
	public String getYnJuPaidUpCapital() {
		return ynJuPaidUpCapital;
	}
	/** 設定有無_服務單位實收資本額 **/
	public void setYnJuPaidUpCapital(String ynJuPaidUpCapital) {
		this.ynJuPaidUpCapital = ynJuPaidUpCapital;
	}

	/** 取得薪轉戶註記{Y,N} **/
	public String getPtaFlag() {
		return ptaFlag;
	}
	/** 設定薪轉戶註記{Y,N} **/
	public void setPtaFlag(String ptaFlag) {
		this.ptaFlag = ptaFlag;
	}
	/** 取得薪轉戶企業統編 **/
	public String getPtaTaxNo() {
		return ptaTaxNo;
	}
	/** 設定薪轉戶企業統編 **/
	public void setPtaTaxNo(String ptaTaxNo) {
		this.ptaTaxNo = ptaTaxNo;
	}
	
	/** 取得薪轉戶企業評等 **/
	public String getPtaGrade() {
		return ptaGrade;
	}
	/** 設定薪轉戶企業評等 **/
	public void setPtaGrade(String ptaGrade) {
		this.ptaGrade = ptaGrade;
	}
	
	/** 取得引入薪轉戶資料日期 **/
	public Date getPtaDataDt() {
		return ptaDataDt;
	}
	/** 設定引入薪轉戶資料日期 **/
	public void setPtaDataDt(Date ptaDataDt) {
		this.ptaDataDt = ptaDataDt;
	}

	/** 取得引入0024服務單位  **/
	public String getCm1_serve_company() {
		return cm1_serve_company;
	}
	/** 設定引入0024服務單位  **/
	public void setCm1_serve_company(String cm1_serve_company) {
		this.cm1_serve_company = cm1_serve_company;
	}

	/** 取得引入0024服務單位行業對象 **/
	public String getCm1_job_business_code() {
		return cm1_job_business_code;
	}
	/** 設定引入0024服務單位行業對象 **/
	public void setCm1_job_business_code(String cm1_job_business_code) {
		this.cm1_job_business_code = cm1_job_business_code;
	}

	/** 取得引入0024職稱代碼 **/
	public String getCm1_title_code() {
		return cm1_title_code;
	}
	/** 設定引入0024職稱代碼 **/
	public void setCm1_title_code(String cm1_title_code) {
		this.cm1_title_code = cm1_title_code;
	}

	/** 取得引入0024職稱說明 **/
	public String getCm1_job_title() {
		return cm1_job_title;
	}
	/** 設定引入0024職稱說明 **/
	public void setCm1_job_title(String cm1_job_title) {
		this.cm1_job_title = cm1_job_title;
	}

	/** 取得引入0024資料日期 **/
	public Date getCm1_dataDt() {
		return cm1_dataDt;
	}
	/** 設定引入0024資料日期 **/
	public void setCm1_dataDt(Date cm1_dataDt) {
		this.cm1_dataDt = cm1_dataDt;
	}

	public String getIsSameWorkAttributes() {
		return isSameWorkAttributes;
	}

	public void setIsSameWorkAttributes(String isSameWorkAttributes) {
		this.isSameWorkAttributes = isSameWorkAttributes;
	}

	public String getPositionType() {
		return positionType;
	}

	public void setPositionType(String positionType) {
		this.positionType = positionType;
	}

	public String getMainIncomeType() {
		return mainIncomeType;
	}

	public void setMainIncomeType(String mainIncomeType) {
		this.mainIncomeType = mainIncomeType;
	}

	public BigDecimal getItemAvalue() {
		return itemAvalue;
	}

	public void setItemAvalue(BigDecimal itemAvalue) {
		this.itemAvalue = itemAvalue;
	}

	public BigDecimal getItemAvalueYear() {
		return itemAvalueYear;
	}

	public void setItemAvalueYear(BigDecimal itemAvalueYear) {
		this.itemAvalueYear = itemAvalueYear;
	}

	public BigDecimal getItemB1value1() {
		return itemB1value1;
	}

	public void setItemB1value1(BigDecimal itemB1value1) {
		this.itemB1value1 = itemB1value1;
	}

	public BigDecimal getItemB1value2() {
		return itemB1value2;
	}

	public void setItemB1value2(BigDecimal itemB1value2) {
		this.itemB1value2 = itemB1value2;
	}

	public BigDecimal getItemB1value3() {
		return itemB1value3;
	}

	public void setItemB1value3(BigDecimal itemB1value3) {
		this.itemB1value3 = itemB1value3;
	}

	public BigDecimal getItemB1value4() {
		return itemB1value4;
	}

	public void setItemB1value4(BigDecimal itemB1value4) {
		this.itemB1value4 = itemB1value4;
	}

	public BigDecimal getItemB1value5() {
		return itemB1value5;
	}

	public void setItemB1value5(BigDecimal itemB1value5) {
		this.itemB1value5 = itemB1value5;
	}

	public BigDecimal getItemB1value6() {
		return itemB1value6;
	}

	public void setItemB1value6(BigDecimal itemB1value6) {
		this.itemB1value6 = itemB1value6;
	}

	public BigDecimal getItemB1HolidayBonus() {
		return itemB1HolidayBonus;
	}

	public void setItemB1HolidayBonus(BigDecimal itemB1HolidayBonus) {
		this.itemB1HolidayBonus = itemB1HolidayBonus;
	}

	public BigDecimal getItemB1YearEndBonus() {
		return itemB1YearEndBonus;
	}

	public void setItemB1YearEndBonus(BigDecimal itemB1YearEndBonus) {
		this.itemB1YearEndBonus = itemB1YearEndBonus;
	}

	public BigDecimal getItemB1valueYear() {
		return itemB1valueYear;
	}

	public void setItemB1valueYear(BigDecimal itemB1valueYear) {
		this.itemB1valueYear = itemB1valueYear;
	}

	public BigDecimal getItemB2value() {
		return itemB2value;
	}

	public void setItemB2value(BigDecimal itemB2value) {
		this.itemB2value = itemB2value;
	}

	public BigDecimal getItemB2valueYear() {
		return itemB2valueYear;
	}

	public void setItemB2valueYear(BigDecimal itemB2valueYear) {
		this.itemB2valueYear = itemB2valueYear;
	}

	public String getItemB3ReportType() {
		return itemB3ReportType;
	}

	public void setItemB3ReportType(String itemB3ReportType) {
		this.itemB3ReportType = itemB3ReportType;
	}

	public BigDecimal getItemB3value1() {
		return itemB3value1;
	}

	public void setItemB3value1(BigDecimal itemB3value1) {
		this.itemB3value1 = itemB3value1;
	}

	public BigDecimal getItemB3value2() {
		return itemB3value2;
	}

	public void setItemB3value2(BigDecimal itemB3value2) {
		this.itemB3value2 = itemB3value2;
	}

	public BigDecimal getItemB3value3() {
		return itemB3value3;
	}

	public void setItemB3value3(BigDecimal itemB3value3) {
		this.itemB3value3 = itemB3value3;
	}

	//public BigDecimal getItemB3value4() {
	//	return itemB3value4;
	//}
	//
	//public void setItemB3value4(BigDecimal itemB3value4) {
	//	this.itemB3value4 = itemB3value4;
	//}
	//
	//public BigDecimal getItemB3value5() {
	//	return itemB3value5;
	//}
	//
	//public void setItemB3value5(BigDecimal itemB3value5) {
	//	this.itemB3value5 = itemB3value5;
	//}
	//
	//public BigDecimal getItemB3value6() {
	//	return itemB3value6;
	//}
	//
	//public void setItemB3value6(BigDecimal itemB3value6) {
	//	this.itemB3value6 = itemB3value6;
	//}

	public BigDecimal getItemB3InProfit() {
		return itemB3InProfit;
	}

	public void setItemB3InProfit(BigDecimal itemB3InProfit) {
		this.itemB3InProfit = itemB3InProfit;
	}

	public BigDecimal getItemB3Holding() {
		return itemB3Holding;
	}

	public void setItemB3Holding(BigDecimal itemB3Holding) {
		this.itemB3Holding = itemB3Holding;
	}

	public BigDecimal getItemB3valueYear() {
		return itemB3valueYear;
	}

	public void setItemB3valueYear(BigDecimal itemB3valueYear) {
		this.itemB3valueYear = itemB3valueYear;
	}

	public BigDecimal getItemB4value1() {
		return itemB4value1;
	}

	public void setItemB4value1(BigDecimal itemB4value1) {
		this.itemB4value1 = itemB4value1;
	}

	public BigDecimal getItemB4value2() {
		return itemB4value2;
	}

	public void setItemB4value2(BigDecimal itemB4value2) {
		this.itemB4value2 = itemB4value2;
	}

	public BigDecimal getItemB4value3() {
		return itemB4value3;
	}

	public void setItemB4value3(BigDecimal itemB4value3) {
		this.itemB4value3 = itemB4value3;
	}

	public BigDecimal getItemB4value4() {
		return itemB4value4;
	}

	public void setItemB4value4(BigDecimal itemB4value4) {
		this.itemB4value4 = itemB4value4;
	}

	public BigDecimal getItemB4value5() {
		return itemB4value5;
	}

	public void setItemB4value5(BigDecimal itemB4value5) {
		this.itemB4value5 = itemB4value5;
	}

	public BigDecimal getItemB4value6() {
		return itemB4value6;
	}

	public void setItemB4value6(BigDecimal itemB4value6) {
		this.itemB4value6 = itemB4value6;
	}

	public BigDecimal getItemB4valueYear() {
		return itemB4valueYear;
	}

	public void setItemB4valueYear(BigDecimal itemB4valueYear) {
		this.itemB4valueYear = itemB4valueYear;
	}

	public String getOtherC1() {
		return otherC1;
	}

	public void setOtherC1(String otherC1) {
		this.otherC1 = otherC1;
	}

	public BigDecimal getItemC1value1() {
		return itemC1value1;
	}

	public void setItemC1value1(BigDecimal itemC1value1) {
		this.itemC1value1 = itemC1value1;
	}

	public BigDecimal getItemC1value2() {
		return itemC1value2;
	}

	public void setItemC1value2(BigDecimal itemC1value2) {
		this.itemC1value2 = itemC1value2;
	}

	public BigDecimal getItemC1value3() {
		return itemC1value3;
	}

	public void setItemC1value3(BigDecimal itemC1value3) {
		this.itemC1value3 = itemC1value3;
	}

	public BigDecimal getItemC1value4() {
		return itemC1value4;
	}

	public void setItemC1value4(BigDecimal itemC1value4) {
		this.itemC1value4 = itemC1value4;
	}

	public BigDecimal getItemC1value5() {
		return itemC1value5;
	}

	public void setItemC1value5(BigDecimal itemC1value5) {
		this.itemC1value5 = itemC1value5;
	}

	public BigDecimal getItemC1value6() {
		return itemC1value6;
	}

	public void setItemC1value6(BigDecimal itemC1value6) {
		this.itemC1value6 = itemC1value6;
	}

	public BigDecimal getItemC1valueYear() {
		return itemC1valueYear;
	}

	public void setItemC1valueYear(BigDecimal itemC1valueYear) {
		this.itemC1valueYear = itemC1valueYear;
	}

	public String getOtherC2() {
		return otherC2;
	}

	public void setOtherC2(String otherC2) {
		this.otherC2 = otherC2;
	}

	public BigDecimal getItemC2value1() {
		return itemC2value1;
	}

	public void setItemC2value1(BigDecimal itemC2value1) {
		this.itemC2value1 = itemC2value1;
	}

	public BigDecimal getItemC2value2() {
		return itemC2value2;
	}

	public void setItemC2value2(BigDecimal itemC2value2) {
		this.itemC2value2 = itemC2value2;
	}

	public BigDecimal getItemC2value3() {
		return itemC2value3;
	}

	public void setItemC2value3(BigDecimal itemC2value3) {
		this.itemC2value3 = itemC2value3;
	}

	public BigDecimal getItemC2value4() {
		return itemC2value4;
	}

	public void setItemC2value4(BigDecimal itemC2value4) {
		this.itemC2value4 = itemC2value4;
	}

	public BigDecimal getItemC2value5() {
		return itemC2value5;
	}

	public void setItemC2value5(BigDecimal itemC2value5) {
		this.itemC2value5 = itemC2value5;
	}

	public BigDecimal getItemC2value6() {
		return itemC2value6;
	}

	public void setItemC2value6(BigDecimal itemC2value6) {
		this.itemC2value6 = itemC2value6;
	}

	public BigDecimal getItemC2valueYear() {
		return itemC2valueYear;
	}

	public void setItemC2valueYear(BigDecimal itemC2valueYear) {
		this.itemC2valueYear = itemC2valueYear;
	}

	public String getOtherC3() {
		return otherC3;
	}

	public void setOtherC3(String otherC3) {
		this.otherC3 = otherC3;
	}

	public BigDecimal getItemC3value1() {
		return itemC3value1;
	}

	public void setItemC3value1(BigDecimal itemC3value1) {
		this.itemC3value1 = itemC3value1;
	}

	public BigDecimal getItemC3value2() {
		return itemC3value2;
	}

	public void setItemC3value2(BigDecimal itemC3value2) {
		this.itemC3value2 = itemC3value2;
	}

	public BigDecimal getItemC3value3() {
		return itemC3value3;
	}

	public void setItemC3value3(BigDecimal itemC3value3) {
		this.itemC3value3 = itemC3value3;
	}

	public BigDecimal getItemC3value4() {
		return itemC3value4;
	}

	public void setItemC3value4(BigDecimal itemC3value4) {
		this.itemC3value4 = itemC3value4;
	}

	public BigDecimal getItemC3value5() {
		return itemC3value5;
	}

	public void setItemC3value5(BigDecimal itemC3value5) {
		this.itemC3value5 = itemC3value5;
	}

	public BigDecimal getItemC3value6() {
		return itemC3value6;
	}

	public void setItemC3value6(BigDecimal itemC3value6) {
		this.itemC3value6 = itemC3value6;
	}

	public BigDecimal getItemC3valueYear() {
		return itemC3valueYear;
	}

	public void setItemC3valueYear(BigDecimal itemC3valueYear) {
		this.itemC3valueYear = itemC3valueYear;
	}

	public String getOtherD4() {
		return otherD4;
	}

	public void setOtherD4(String otherD4) {
		this.otherD4 = otherD4;
	}

	public BigDecimal getItemD4value1() {
		return itemD4value1;
	}

	public void setItemD4value1(BigDecimal itemD4value1) {
		this.itemD4value1 = itemD4value1;
	}

	public BigDecimal getItemD4value2() {
		return itemD4value2;
	}

	public void setItemD4value2(BigDecimal itemD4value2) {
		this.itemD4value2 = itemD4value2;
	}

	public BigDecimal getItemD4value3() {
		return itemD4value3;
	}

	public void setItemD4value3(BigDecimal itemD4value3) {
		this.itemD4value3 = itemD4value3;
	}

	public BigDecimal getItemD4value4() {
		return itemD4value4;
	}

	public void setItemD4value4(BigDecimal itemD4value4) {
		this.itemD4value4 = itemD4value4;
	}

	public BigDecimal getItemD4value5() {
		return itemD4value5;
	}

	public void setItemD4value5(BigDecimal itemD4value5) {
		this.itemD4value5 = itemD4value5;
	}

	public BigDecimal getItemD4value6() {
		return itemD4value6;
	}

	public void setItemD4value6(BigDecimal itemD4value6) {
		this.itemD4value6 = itemD4value6;
	}

	public BigDecimal getItemD4valueYear() {
		return itemD4valueYear;
	}

	public void setItemD4valueYear(BigDecimal itemD4valueYear) {
		this.itemD4valueYear = itemD4valueYear;
	}

	public String getOtherD5() {
		return otherD5;
	}

	public void setOtherD5(String otherD5) {
		this.otherD5 = otherD5;
	}

	public BigDecimal getItemD5value1() {
		return itemD5value1;
	}

	public void setItemD5value1(BigDecimal itemD5value1) {
		this.itemD5value1 = itemD5value1;
	}

	public BigDecimal getItemD5value2() {
		return itemD5value2;
	}

	public void setItemD5value2(BigDecimal itemD5value2) {
		this.itemD5value2 = itemD5value2;
	}

	public BigDecimal getItemD5value3() {
		return itemD5value3;
	}

	public void setItemD5value3(BigDecimal itemD5value3) {
		this.itemD5value3 = itemD5value3;
	}

	public BigDecimal getItemD5value4() {
		return itemD5value4;
	}

	public void setItemD5value4(BigDecimal itemD5value4) {
		this.itemD5value4 = itemD5value4;
	}

	public BigDecimal getItemD5value5() {
		return itemD5value5;
	}

	public void setItemD5value5(BigDecimal itemD5value5) {
		this.itemD5value5 = itemD5value5;
	}

	public BigDecimal getItemD5value6() {
		return itemD5value6;
	}

	public void setItemD5value6(BigDecimal itemD5value6) {
		this.itemD5value6 = itemD5value6;
	}

	public BigDecimal getItemD5valueYear() {
		return itemD5valueYear;
	}

	public void setItemD5valueYear(BigDecimal itemD5valueYear) {
		this.itemD5valueYear = itemD5valueYear;
	}

	public String getOtherD6() {
		return otherD6;
	}

	public void setOtherD6(String otherD6) {
		this.otherD6 = otherD6;
	}

	public BigDecimal getItemD6value1() {
		return itemD6value1;
	}

	public void setItemD6value1(BigDecimal itemD6value1) {
		this.itemD6value1 = itemD6value1;
	}

	public BigDecimal getItemD6value2() {
		return itemD6value2;
	}

	public void setItemD6value2(BigDecimal itemD6value2) {
		this.itemD6value2 = itemD6value2;
	}

	public BigDecimal getItemD6value3() {
		return itemD6value3;
	}

	public void setItemD6value3(BigDecimal itemD6value3) {
		this.itemD6value3 = itemD6value3;
	}

	public BigDecimal getItemD6value4() {
		return itemD6value4;
	}

	public void setItemD6value4(BigDecimal itemD6value4) {
		this.itemD6value4 = itemD6value4;
	}

	public BigDecimal getItemD6value5() {
		return itemD6value5;
	}

	public void setItemD6value5(BigDecimal itemD6value5) {
		this.itemD6value5 = itemD6value5;
	}

	public BigDecimal getItemD6value6() {
		return itemD6value6;
	}

	public void setItemD6value6(BigDecimal itemD6value6) {
		this.itemD6value6 = itemD6value6;
	}

	public BigDecimal getItemD6valueYear() {
		return itemD6valueYear;
	}

	public void setItemD6valueYear(BigDecimal itemD6valueYear) {
		this.itemD6valueYear = itemD6valueYear;
	}

	public String getOtherD7() {
		return otherD7;
	}

	public void setOtherD7(String otherD7) {
		this.otherD7 = otherD7;
	}

	public BigDecimal getItemD7value1() {
		return itemD7value1;
	}

	public void setItemD7value1(BigDecimal itemD7value1) {
		this.itemD7value1 = itemD7value1;
	}

	public BigDecimal getItemD7value2() {
		return itemD7value2;
	}

	public void setItemD7value2(BigDecimal itemD7value2) {
		this.itemD7value2 = itemD7value2;
	}

	public BigDecimal getItemD7value3() {
		return itemD7value3;
	}

	public void setItemD7value3(BigDecimal itemD7value3) {
		this.itemD7value3 = itemD7value3;
	}

	public BigDecimal getItemD7value4() {
		return itemD7value4;
	}

	public void setItemD7value4(BigDecimal itemD7value4) {
		this.itemD7value4 = itemD7value4;
	}

	public BigDecimal getItemD7value5() {
		return itemD7value5;
	}

	public void setItemD7value5(BigDecimal itemD7value5) {
		this.itemD7value5 = itemD7value5;
	}

	public BigDecimal getItemD7value6() {
		return itemD7value6;
	}

	public void setItemD7value6(BigDecimal itemD7value6) {
		this.itemD7value6 = itemD7value6;
	}

	public BigDecimal getItemD7valueYear() {
		return itemD7valueYear;
	}

	public void setItemD7valueYear(BigDecimal itemD7valueYear) {
		this.itemD7valueYear = itemD7valueYear;
	}

	public String getOtherD8() {
		return otherD8;
	}

	public void setOtherD8(String otherD8) {
		this.otherD8 = otherD8;
	}

	public BigDecimal getItemD8value1() {
		return itemD8value1;
	}

	public void setItemD8value1(BigDecimal itemD8value1) {
		this.itemD8value1 = itemD8value1;
	}

	public BigDecimal getItemD8value2() {
		return itemD8value2;
	}

	public void setItemD8value2(BigDecimal itemD8value2) {
		this.itemD8value2 = itemD8value2;
	}

	public BigDecimal getItemD8value3() {
		return itemD8value3;
	}

	public void setItemD8value3(BigDecimal itemD8value3) {
		this.itemD8value3 = itemD8value3;
	}

	public BigDecimal getItemD8value4() {
		return itemD8value4;
	}

	public void setItemD8value4(BigDecimal itemD8value4) {
		this.itemD8value4 = itemD8value4;
	}

	public BigDecimal getItemD8value5() {
		return itemD8value5;
	}

	public void setItemD8value5(BigDecimal itemD8value5) {
		this.itemD8value5 = itemD8value5;
	}

	public BigDecimal getItemD8value6() {
		return itemD8value6;
	}

	public void setItemD8value6(BigDecimal itemD8value6) {
		this.itemD8value6 = itemD8value6;
	}

	public BigDecimal getItemD8valueYear() {
		return itemD8valueYear;
	}

	public void setItemD8valueYear(BigDecimal itemD8valueYear) {
		this.itemD8valueYear = itemD8valueYear;
	}

	public String getOtherD9() {
		return otherD9;
	}

	public void setOtherD9(String otherD9) {
		this.otherD9 = otherD9;
	}

	public BigDecimal getItemD9value1() {
		return itemD9value1;
	}

	public void setItemD9value1(BigDecimal itemD9value1) {
		this.itemD9value1 = itemD9value1;
	}

	public BigDecimal getItemD9value2() {
		return itemD9value2;
	}

	public void setItemD9value2(BigDecimal itemD9value2) {
		this.itemD9value2 = itemD9value2;
	}

	public BigDecimal getItemD9value3() {
		return itemD9value3;
	}

	public void setItemD9value3(BigDecimal itemD9value3) {
		this.itemD9value3 = itemD9value3;
	}

	public BigDecimal getItemD9value4() {
		return itemD9value4;
	}

	public void setItemD9value4(BigDecimal itemD9value4) {
		this.itemD9value4 = itemD9value4;
	}

	public BigDecimal getItemD9value5() {
		return itemD9value5;
	}

	public void setItemD9value5(BigDecimal itemD9value5) {
		this.itemD9value5 = itemD9value5;
	}

	public BigDecimal getItemD9value6() {
		return itemD9value6;
	}

	public void setItemD9value6(BigDecimal itemD9value6) {
		this.itemD9value6 = itemD9value6;
	}

	public BigDecimal getItemD9valueYear() {
		return itemD9valueYear;
	}

	public void setItemD9valueYear(BigDecimal itemD9valueYear) {
		this.itemD9valueYear = itemD9valueYear;
	}

	public BigDecimal getItemB4DisRate() {
		return itemB4DisRate;
	}

	public void setItemB4DisRate(BigDecimal itemB4DisRate) {
		this.itemB4DisRate = itemB4DisRate;
	}

	public BigDecimal getItemD9DisRate() {
		return itemD9DisRate;
	}

	public void setItemD9DisRate(BigDecimal itemD9DisRate) {
		this.itemD9DisRate = itemD9DisRate;
	}

	public String getPtaBrNo() {
		return ptaBrNo;
	}
	public void setPtaBrNo(String ptaBrNo) {
		this.ptaBrNo = ptaBrNo;
	}

	public String getPtaActNo() {
		return ptaActNo;
	}
	public void setPtaActNo(String ptaActNo) {
		this.ptaActNo = ptaActNo;
	}

	public String getHighRiskJobFlag() {
		return highRiskJobFlag;
	}

	public void setHighRiskJobFlag(String highRiskJobFlag) {
		this.highRiskJobFlag = highRiskJobFlag;
	}

	public String getIsHasEsgScore() {
		return isHasEsgScore;
	}

	public void setIsHasEsgScore(String isHasEsgScore) {
		this.isHasEsgScore = isHasEsgScore;
	}

	public BigDecimal getEsgScore() {
		return esgScore;
	}

	public void setEsgScore(BigDecimal esgScore) {
		this.esgScore = esgScore;
	}

	public BigDecimal getSnrM() {
		return snrM;
	}
	public void setSnrM(BigDecimal snrM) {
		this.snrM = snrM;
	}

	public BigDecimal getIncomeDetailVer() {
		return incomeDetailVer;
	}

	public void setIncomeDetailVer(BigDecimal incomeDetailVer) {
		this.incomeDetailVer = incomeDetailVer;
	}

	public String getClsJobType1() {
		return clsJobType1;
	}

	public void setClsJobType1(String clsJobType1) {
		this.clsJobType1 = clsJobType1;
	}

	public String getClsJobType2() {
		return clsJobType2;
	}

	public void setClsJobType2(String clsJobType2) {
		this.clsJobType2 = clsJobType2;
	}

	public String getClsJobTitle() {
		return clsJobTitle;
	}

	public void setClsJobTitle(String clsJobTitle) {
		this.clsJobTitle = clsJobTitle;
	}

	public String getIsNPO() {
		return isNPO;
	}

	public void setIsNPO(String isNPO) {
		this.isNPO = isNPO;
	}
	
	public String getTermGroup() {
		return termGroup;
	}

	public void setTermGroup(String termGroup) {
		this.termGroup = termGroup;
	}
	
	public String getApplyDBRType() {
		return applyDBRType;
	}
	public void setApplyDBRType(String applyDBRType) {
		this.applyDBRType = applyDBRType;
	}
}
