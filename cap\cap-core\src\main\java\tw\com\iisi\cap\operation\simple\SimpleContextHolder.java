/*
 * SimpleContextHolder.java
 *
 * Copyright (c) 2009-2012 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.operation.simple;

import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * SimpleContextHolder. 
 * 從ThreadLocal 中取得對應資訊
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/22,iristu,new
 *          </ul>
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
public class SimpleContextHolder extends InheritableThreadLocal {

    /**
     * {@value #FORM_RESULT}
     */
    public static final String FORM_RESULT = "formResult";

    /**
     * {@value #DATA_BEAN}
     */
    public static final String DATA_BEAN = "docData";

    /**
     * 保存 Context
     */
    protected static ThreadLocal<Map<String, Object>> keepInfoStore = new InheritableThreadLocal<Map<String, Object>>();

    /**
     * Get a map containing all the objects held by the current thread.
     */
    private static Map getInfoStore() {
        Map<String, Object> m = keepInfoStore.get();
        if (m == null) {
            m = new HashMap<String, Object>();
            keepInfoStore.set(m);
        }
        return m;
    }

    /**
     * Get the context identified by the key parameter.
     * 
     * @param <T>
     *            the model
     * @param key
     *            the key
     * @return Object
     */
    public static <T> T get(String key) {
        return (T) getInfoStore().get(key);
    }

    /**
     * Put a context value (the o parameter) as identified with the key parameter into the current thread's context map.
     * 
     * @param key
     *            the Key
     * @param o
     *            Object
     */
    public static void put(String key, Object o) {
        getInfoStore().put(key, o);
    }

    /**
     * 檢核Context中是否存在該鍵值
     * 
     * @param key
     *            鍵值
     * @return
     */
    public static boolean containKey(String key) {
        return getInfoStore().containsKey(key);
    }

    /**
     * Remove the the context identified by the key parameter.
     * 
     * @param key
     *            the Key
     */
    public static void remove(String key) {
        getInfoStore().remove(key);
    }

    /**
     * Remove all the object put in this thread context.
     */
    public static void resetInfoStore() {
        if (keepInfoStore.get() != null) {
            keepInfoStore.get().clear();
            keepInfoStore.remove();
        }
    }

}// ~
