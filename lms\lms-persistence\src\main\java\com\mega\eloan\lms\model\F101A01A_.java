package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the F101A01A database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sunkist Wang,new</li>
 *          <li>2011/7/28,Sunkis<PERSON> Wang,update letter case.</li>
 *          <li>2011/8/11,<PERSON><PERSON><PERSON> Wang,add column.</li>
 *          </ul>
 */
@Generated(value = "Dali", date = "2011-07-26T16:25:22.390+0800")
@StaticMetamodel(F101A01A.class)
public class F101A01A_ extends RelativeMeta_ {
	public static volatile SingularAttribute<F101A01A, Timestamp> authTime;
	public static volatile SingularAttribute<F101A01A, String> authType;
	public static volatile SingularAttribute<F101A01A, String> authUnit;
	public static volatile SingularAttribute<F101A01A, String> owner;
	public static volatile SingularAttribute<F101A01A, String> ownUnit;
	public static volatile SingularAttribute<F101A01A, F101M01A> f101m01a;
}
