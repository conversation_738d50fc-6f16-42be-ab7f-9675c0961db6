---------------------------------------------------------
-- LMS.L918S01A 授管處停權明細檔
---------------------------------------------------------
--DROP TABLE LMS.L918S01A;
CREATE TABLE LMS.L918S01A (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)     ,
	SETDOC<PERSON><PERSON>      CHAR(50)     ,
	<PERSON>ANCHN<PERSON>      CHAR(3)      ,
	CUSTID        VARCHAR(10)  ,
	<PERSON>UPN<PERSON>         CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   CHAR(1)      ,
	SUSPENDMONS   DECIMAL(2, 0),
	<PERSON><PERSON><PERSON><PERSON>YMONS   DECIMAL(2, 0),
	CHANGEMONS    DECIMAL(2, 0),
	STOPSTATUS    CHAR(1)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>      DATE         ,
	<PERSON><PERSON><PERSON><PERSON>        DATE         ,
	SET<PERSON>PART     CHAR(3)      ,
	<PERSON>TE<PERSON><PERSON><PERSON>      CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON>       DATE         ,
	<PERSON><PERSON><PERSON><PERSON><PERSON>     CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR(6)      ,
	<PERSON><PERSON><PERSON><PERSON>       DATE         ,
	<PERSON><PERSON>DOCNO      CHAR(50)     ,
	SETMAINID     CHAR(32)     ,
	MODMAINID     CHAR(32)     ,
	CLASSNO       CHAR(3)      ,
	STATUSFLAG    CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L918S01A PRIMARY KEY(OID)
) in EL_DATA_4KTS index in EL_INDEX_4KTS;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L918S01A IS '授管處停權明細檔';
COMMENT ON LMS.L918S01A (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	SETDOCNO      IS '設定案件編號', 
	BRANCHNO      IS '分行別', 
	CUSTID        IS '客戶統編', 
	DUPNO         IS '重覆序號', 
	SUSPENDCODE   IS '停權代碼', 
	SUSPENDMONS   IS '停權月數', 
	MODLIFYMONS   IS '修改停權月數', 
	CHANGEMONS    IS '本次停權月數', 
	STOPSTATUS    IS '停權狀態碼', 
	LOANDATE      IS '最近簽案後首撥日', 
	OVDATE        IS '逾期設定日', 
	SETDEPART     IS '設定單位', 
	SETEMPNO      IS '設定覆核人員', 
	SETDATE       IS '設定日期', 
	MODDEPART     IS '修改單位', 
	MODEMPNO      IS '修改覆核人員', 
	MODDATE       IS '修改日期', 
	MODDOCNO      IS '修改案件編號', 
	SETMAINID     IS '設定文件編號', 
	MODMAINID     IS '修改文件編號', 
	CLASSNO       IS '系統代碼', 
	STATUSFLAG    IS '狀態Flag', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
