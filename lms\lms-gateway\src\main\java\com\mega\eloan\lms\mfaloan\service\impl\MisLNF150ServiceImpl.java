package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;

@Service
public class MisLNF150ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF150Service {

	@Override
	public List<Map<String, Object>> findByCustIdDup(String custId,
			String dupNo, String brNo) {

		return getJdbc().queryForList("LNF150.findByIdDupBrNo",
				new String[] { custId, dupNo, brNo, brNo });

	}

	@Override
	public List<Map<String, Object>> findByCustIdDup2(String custId,
			String dupNo, String brNo) {
		return getJdbc().queryForList("LNF150.findByIdDupBrNo2",
				new String[] { custId, dupNo, brNo, brNo, brNo });
	}

	@Override
	public List<Map<String, Object>> findByCustIdDupOnlyHouse(String custId,
			String dupNo, String brNo) {
		return getJdbc().queryForList("LNF150.findByIdDupBrNoOnlyHouse",
				new String[] { custId, dupNo, brNo, brNo });
	}

	@Override
	public List<Map<String, Object>> findByCustIdDupOnlyHouse2(String custId,
			String dupNo, String brNo) {
		return getJdbc().queryForList("LNF150.findByIdDupBrNoOnlyHouse2",
				new String[] { custId, dupNo, brNo, brNo, brNo });
	}

	@Override
	public List<Map<String, Object>> findByContractAndCustId(String custId,
			String dupNo, String cntrNo) {
		return getJdbc().queryForList("LNF150.selByContractAndCustId",
				new String[] { custId, dupNo, cntrNo });
	}

	@Override
	public List<Map<String, Object>> gfnDB2GetELLNF(String custId) {
		return getJdbc().queryForList("LN.LNF150.gfnDB2GetELLNF",
				new String[] { custId });
	}
	
	@Override
	public Map<String, Object> selFactAmt3(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt3",
				new String[] { brno, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> selFactAmt4(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt4",
				new String[] { brno, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> selFactAmt5(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt5",
				new String[] { brno, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> selFactAmt6(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt6",
				new String[] { brno, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> selFactAmt7(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt7",
				new String[] { brno, custId, dupNo });
	}
	
	@Override
	public Map<String, Object> selFactAmt8(String brno, String custId, String dupNo) {
		return getJdbc().queryForMap("LNF150.selFactAmt8",
				new String[] { brno, custId, dupNo });
	}
}
