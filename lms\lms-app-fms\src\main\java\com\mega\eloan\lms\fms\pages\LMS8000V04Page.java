package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**
 * <pre>
 * 貸後管理作業 - 查詢
 *
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/fms/lms8000v04")
public class LMS8000V04Page extends AbstractEloanInnerView {

	public LMS8000V04Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_呈總行);
		
//		// 加上Button
//		List<EloanPageFragment> btns = new ArrayList<>();
//		// 主管跟經辦都會出現的按鈕
//		btns.add(CreditButtonEnum.Filter);
//		btns.add(CreditButtonEnum.View);
//
//		addToButtonPanel(model,
//				btns.toArray(new CreditButtonEnum[] {})));
		// 加上Button
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));
		renderJsI18N(LMS8000V01Page.class);
		renderJsI18N(LMS8000V04Page.class);
	}
}