/* 
 *  LMS1601GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.pages.LMS1601M01Page;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1411Service;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.eloan.lms.model.L161S01E;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 動用審核表
 * </pre>
 * 
 * @since 2011/10/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/5,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1601gridhandler")
public class LMS1601GridHandler extends AbstractGridHandler {

	@Resource
	LMS1601Service lms1601Service;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LMS1411Service lms1411Service;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;
	@Resource
	LMSService lmsService;

	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	MisIcbcBrService misIcbcBrService;

	/**
	 * 查詢動用審核表外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL160m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l160a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L160M01A.class, pageSetting);

		List<L160M01A> l160m01as = (List<L160M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (L160M01A l160m01a : l160m01as) {
			l160m01a.setApprId(this.getUserName(l160m01a.getApprId()));
			// 設定顯示名稱 使用者id+重複序號
			l160m01a.setCustId(l160m01a.getCustId() + " " + l160m01a.getDupNo());
			cntrNo.setLength(0);
			// 確認全部動用是否有選
			if ("N".equals(l160m01a.getAllCanPay())) {
				Set<L160M01B> setL160m01b = l160m01a.getL160m01b();
				Iterator<L160M01B> it = setL160m01b.iterator();
				while (it.hasNext()) {

					cntrNo.append(cntrNo.length() > 0 ? "<br/>" : "");
					cntrNo.append(Util.trim(it.next().getCntrNo()));
				}

			} else {

				// L160M01A.allUse=全部動用
				cntrNo.append(pop.getProperty("L160M01A.allUse"));
			}
			l160m01a.setAllCanPay(cntrNo.toString());

			if ("Y".equals(Util.trim(l160m01a.getUseType()))) {
				l160m01a.setUseType("V");
			} else {
				l160m01a.setUseType("");
			}
			if (!Util.isEmpty(l160m01a.getL163S01A())) {
				// 當文件為 編製中要顯示-預定補全日 、待覆核-要新增顯示辦妥日期
				l160m01a.setWillFinishDate(l160m01a.getL163S01A()
						.getWillFinishDate());
				String nowDocstatus = l160m01a.getDocStatus();
				if (CreditDocStatusEnum.海外_編製中.getCode().equals(nowDocstatus)) {

				} else if (CreditDocStatusEnum.海外_待覆核.getCode().equals(
						nowDocstatus)
						|| CreditDocStatusEnum.先行動用_待覆核.getCode().equals(
								nowDocstatus)) {
					l160m01a.setBlackDataDate(l160m01a.getL163S01A()
							.getFinishDate());
				}
			}
			l160m01a.setCaseNo(Util.toSemiCharString(l160m01a.getCaseNo()));
		}

		return new CapGridResult(l160m01as, page.getTotalRow());

	}

	/**
	 * 查詢動用審核表外部的grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL160m01a3(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] docStatusArray = new String[] {
				CreditDocStatusEnum.海外_已核准.getCode(),
				CreditDocStatusEnum.先行動用_已覆核.getCode() };
		String type = Util.nullToSpace(params.getString("type"));
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l160a01a.authUnit", user.getUnitNo());
		// 狀態1 是用客戶ID去查
		switch (Util.parseInt(type)) {
		case 1:
			String custId = Util.nullToSpace(params.getString("custId"));
			String dupNo = Util.nullToSpace(params.getString("dupNo"));
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
			break;
		case 2:
			Date fromDate = Util.parseDate(Util.nullToSpace(params
					.getString("fromDate")));
			Date endDate = Util.parseDate(Util.nullToSpace(params
					.getString("endDate") + " 23:59:59"));
			Object[] reason = { fromDate, endDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", reason);
			break;
		default:
			break;
		}

		Page<? extends GenericBean> page = lms1601Service.findPage(
				L160M01A.class, pageSetting);
		List<L160M01A> l160m01as = (List<L160M01A>) page.getContent();
		StringBuilder cntrNo = new StringBuilder("");
		for (L160M01A l160m01a : l160m01as) {
			// 設定顯示名稱 使用者id+重複序號
			l160m01a.setCustId(l160m01a.getCustId() + " " + l160m01a.getDupNo());
			cntrNo.setLength(0);
			// 確認全部動用是否有選
			if ("N".equals(l160m01a.getAllCanPay())) {
				Set<L160M01B> setL160m01b = l160m01a.getL160m01b();
				Iterator<L160M01B> it = setL160m01b.iterator();
				while (it.hasNext()) {

					cntrNo.append(cntrNo.length() > 0 ? "<br/>" : "");
					cntrNo.append(Util.trim(it.next().getCntrNo()));
				}
			} else {
				// L160M01A.allUse=全部動用
				cntrNo.append(pop.getProperty("L160M01A.allUse"));
			}
			l160m01a.setAllCanPay(cntrNo.toString());

			if ("Y".equals(Util.trim(l160m01a.getUseType()))) {
				l160m01a.setUseType("V");
			} else {
				l160m01a.setUseType("");
			}

			if (!Util.isEmpty(l160m01a.getL163S01A())) {
				L163S01A l163m01a = l160m01a.getL163S01A();
				// 顯示辦妥日期
				l160m01a.setBlackDataDate(l163m01a.getFinishDate());

				// 顯示辦妥覆核
				l160m01a.setBlackListTxtErr(Util.isEmpty(l163m01a
						.getBfReCheckDate()) ? "" : "V");

			}
			// 顯示先行動用覆核和預定補全日期
			l160m01a.setWillFinishDate(l160m01a.getL163S01A()
					.getWillFinishDate());
			l160m01a.setApprId(this.getUserName(l160m01a.getApprId()));
			l160m01a.setCaseNo(Util.toSemiCharString(l160m01a.getCaseNo()));
		}

		return new CapGridResult(l160m01as, page.getTotalRow());

	}

	/**
	 * 查詢已核准案件簽報書
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,	PageParameters params) throws CapException {

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
		}
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "docCode",
				UtilConstants.Casedoc.DocCode.陳復陳述案);
		// 如果登錄的是總行要多看到 泰國的提會待登錄、和泰國提會待覆核的案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.海外_已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseBrId",
				user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		Page<? extends GenericBean> page = lms1201Service.findPage(
				L120M01A.class, pageSetting);
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A l120m01a : l120m01as) {

			// 設定顯示名稱 使用者id+重複序號+名稱
			l120m01a.setCustId(l120m01a.getCustId() + " " + l120m01a.getDupNo()
					+ " " + l120m01a.getCustName());
			l120m01a.setUpdater(this.getUserName(l120m01a.getUpdater()));
			l120m01a.setCaseNo(Util.toSemiCharString(l120m01a.getCaseNo()));
		}

		return new CapGridResult(l120m01as, page.getTotalRow());

	}

	/**
	 * 
	 * 查詢先行動用待辦事項控制表登錄介面
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryUseFirst(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String[] docStatusArray = new String[] {
				CreditDocStatusEnum.海外_已核准.getCode(),
				CreditDocStatusEnum.先行動用_已覆核.getCode() };
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l160a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				"l163s01a.finishDate", "");

		pageSetting.addSearchModeParameters(SearchMode.IS_NOT_NULL,
				"l163s01a.waitingItem", "");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "useType",
				UtilConstants.DEFAULT.是);
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L160M01A.class, pageSetting);

		List<L160M01A> l160m01as = (List<L160M01A>) page.getContent();
		for (L160M01A l160m01a : l160m01as) {

			// 設定顯示名稱 使用者id+重複序號
			l160m01a.setCustId(l160m01a.getCustId() + " " + l160m01a.getDupNo());

			// 顯示先行動用覆核和預定補全日期
			l160m01a.setCaseDate(l160m01a.getL163S01A().getWillFinishDate());

			// 顯示待辦事項
			l160m01a.setCaseNo(l160m01a.getL163S01A().getWaitingItem());
		}

		return new CapGridResult(l160m01as, page.getTotalRow());

	}

	/**
	 * 查詢已核准聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL141m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		if (Util.isNotEmpty(dupNo)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
		}
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.海外_已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms1411Service.findPage(
				L141M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name)); // 分行名稱格式化
		dataReformatter.put("userid", new UserNameFormatter(userInfoService)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);
		List<L141M01A> l141m01as = (List<L141M01A>) page.getContent();
		for (L141M01A l141m01a : l141m01as) {
			l141m01a.setCaseNo(Util.toSemiCharString(l141m01a.getCaseNo()));
		}

		return result;
	}

	/**
	 * 查詢該案件簽報書mainId額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	// @SuppressWarnings("unchecked")
	// public CapGridResult queryL140m01a(ISearch pageSetting,
	// PageParameters params) throws CapException {
	// // 查這份文件的MinId
	// String mainId = Util.nullToSpace(params
	// .getString(EloanConstants.MAIN_ID));
	// L120M01A l120m01a = lms1201Service.findL120m01aByMainId(mainId);
	// String itemType = lmsService.checkL140M01AItemType(l120m01a);
	// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "caseMainId",
	// mainId);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "useBrId",
	// user.getUnitNo());
	// // // 先註解因為會沒有資料
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "itemType",
	// itemType);
	//
	// Page<? extends GenericBean> page = lms1601Service.findPage(
	// VLUSEDOC01.class, pageSetting);
	//
	// List<VLUSEDOC01> vusedoc01s = (List<VLUSEDOC01>) page.getContent();
	// ArrayList<VLUSEDOC01> showvusedoc01s = new ArrayList<VLUSEDOC01>();
	// HashMap<String, String> cntrNoMap = new HashMap<String, String>();
	// for (VLUSEDOC01 vusedoc01 : vusedoc01s) {
	// System.out.println(vusedoc01.getShareAmt());
	// if (cntrNoMap.containsKey(vusedoc01.getUseCntrNo())) {
	// continue;
	// }
	// // 設定顯示名稱 使用者id+重複序號
	// vusedoc01.setCustId(vusedoc01.getCustId() + " "
	// + vusedoc01.getDupNo());
	// // 分行別
	// vusedoc01.setUseBrId(vusedoc01.getUseBrId() + " "
	// + branchService.getBranchName(vusedoc01.getUseBrId()));
	//
	// showvusedoc01s.add(vusedoc01);
	// cntrNoMap.put(vusedoc01.getUseCntrNo(), "");
	// }
	// return new CapGridResult(showvusedoc01s, page.getTotalRow());
	//
	// }

	/**
	 * 查詢該案件簽報書mainId額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL140m01a(ISearch pageSetting, PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(mainId);
		String itemType = lmsService.checkL140M01AItemType(l120m01a);
		String caseMainId = mainId;
		// String useBrId = user.getUnitNo();

		// List<Map<String, Object>> beanList = eloandbBASEService
		// .get_cntrDoc_from_VLUSEDOC01(caseMainId, useBrId, itemType);
		// I-110-0028_05097_B1002 Web e-Loan企金授信額度明細表配合進出口業務集中化修改小行可以敘作大行動審表
		List<String> brnoList = new ArrayList<String>();
		brnoList.add(user.getUnitNo());

		Map<String, Object> bankInfo = misIcbcBrService.getBankInfo(user
				.getUnitNo());
		if (bankInfo != null && !bankInfo.isEmpty()) {
			String IEX_BRM = Util.trim(MapUtils.getString(bankInfo, "IEX_BRM"));
			if (Util.notEquals(IEX_BRM, "")) {
				brnoList.add(IEX_BRM);
			}
		}

		String[] useBrId = brnoList.toArray(new String[brnoList.size()]);

		List<Map<String, Object>> beanList = eloandbBASEService
				.get_cntrDoc_from_VLUSEDOC01_multiBr(caseMainId, useBrId,
						itemType);

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > beanList.size() ? start
				+ (beanList.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				beanListnew, beanList.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String fieldId = Util.nullToSpace(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 資料修正查詢額度明細表的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryCntrCase(ISearch pageSetting, PageParameters params) throws CapException {

		String oid = params.getString(EloanConstants.OID);
		L160M01A l160m01a = lms1601Service.findModelByOid(L160M01A.class, oid);
		List<String> mainIds = new ArrayList<String>();

		for (L160M01B l160m01b : l160m01a.getL160m01b()) {
			mainIds.add(l160m01b.getReMainId());
		}
		pageSetting.addSearchModeParameters(SearchMode.IN,
				EloanConstants.MAIN_ID, mainIds.toArray(new String[0]));
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢聯貸案參貸比率一覽表的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryBranch(ISearch pageSetting, PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String cntrNo = Util.nullToSpace(params.getString("cntrNo"));
		String uid = Util.nullToSpace(params.getString("uid"));

		if (Util.equals(Util.trim(uid), "")) {
			uid = mainId;
		}
		L161S01A l161s01a = lms1601Service.findL161m01aByMainIdUid(mainId, uid);

		if (l161s01a == null) {
			throw new CapMessageException(
					pop.getProperty("L160M01A.message79"), getClass());
		}

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid", uid);
		pageSetting.addOrderBy("createTime");
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01B.class, pageSetting);

		// 要顯示的銀行名稱
		StringBuilder showBranch = new StringBuilder("");

		List<L161S01B> l161m01bs = (List<L161S01B>) page.getContent();
		for (L161S01B l161m01b : l161m01bs) {
			l161m01b.setSlMaster("Y".equals(l161m01b.getSlMaster()) ? pop
					.getProperty("L160M01A.yes") : pop
					.getProperty("L160M01A.no"));
			showBranch.setLength(0);

			showBranch.append(l161m01b.getSlBank()).append(" ")
					.append(l161m01b.getSlBankCN());
			if (!Util.isEmpty(Util.trim(l161m01b.getSlBranch()))) {
				showBranch.append("<br/>").append(l161m01b.getSlBranch())
						.append(" ").append(l161m01b.getSlBranchCN());
			}

			l161m01b.setSlBankCN(showBranch.toString());
		}
		return new CapGridResult(l161m01bs, page.getTotalRow());

	}

	/**
	 * 查詢主從債務人資料表的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryPeople(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		String custId = l160m01a.getCustId();
		String dupNo = l160m01a.getDupNo();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		LinkedList<L162S01A> keyManList = new LinkedList<L162S01A>();
		LinkedList<L162S01A> otherList = new LinkedList<L162S01A>();
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L162S01A.class, pageSetting);
		List<L162S01A> l162m01as = (List<L162S01A>) page.getContent();
		for (L162S01A l162m01a : l162m01as) {
			// 當為主借款人排序要在前面
			if (l162m01a.getCustId().equals(custId)
					&& l162m01a.getDupNo().equals(dupNo)) {
				keyManList.add(l162m01a);
			} else {
				otherList.add(l162m01a);
			}
			StringBuilder show = new StringBuilder("");
			String rkindD = Util.trim(l162m01a.getRKindD());
			int rkindm = Util.parseInt(l162m01a.getRKindM());
			switch (rkindm) {
			case 1:
				l162m01a.setRKindD(rkindD
						+ Util.trim(codeMap.get("Relation_type1").get(rkindD)));
				break;
			case 2:
				l162m01a.setRKindD(rkindD
						+ Util.trim(codeMap.get("Relation_type2").get(rkindD)));
				break;
			case 3:
				char[] kind = rkindD.toCharArray();
				if (kind.length == 2) {
					String kind1 = (String) codeMap.get("Relation_type31").get(
							String.valueOf(kind[0]));
					String kind2 = (String) codeMap.get("Relation_type32").get(
							String.valueOf(kind[1]));
					show.append(rkindD).append(" ");
					show.append(kind1).append(" - ").append(kind2);
				}

				l162m01a.setRKindD(show.toString());
				break;

			}
			// 主要借款人統編
			l162m01a.setCustId(l162m01a.getCustId() + " " + l162m01a.getDupNo());
			// 主要借款人統編
			l162m01a.setRId(l162m01a.getRId() + " " + l162m01a.getRDupNo());
			String rType = l162m01a.getRType();
			// 相關身分
			l162m01a.setRType(rType
					+ Util.trim(codeMap.get("lms1605s03_rType").get(rType)));

			// 國別
			l162m01a.setRCountry((String) codeMap.get(
					CodeTypeEnum.國家代碼.getCode()).get(l162m01a.getRCountry()));
		}
		keyManList.addAll(otherList);
		return new CapGridResult(keyManList, page.getTotalRow());
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (userId == null) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢額度動用資訊一覽表的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryCntrnoInfo(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] codeType = { "Relation_type1", "lms1405m01_snoKind",
				"lms1605m01_caseType", "lms1605m01_useSpecialReason",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);
		String custId = l160m01a.getCustId();
		String dupNo = l160m01a.getDupNo();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S02Panel.class);
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01A.class, pageSetting);
		List<L161S01A> l161s01as = (List<L161S01A>) page.getContent();
		for (L161S01A l161s01a : l161s01as) {
			l161s01a.setSnoKindDscr(l161s01a.getSnoKind()
					+ "."
					+ Util.trim(codeMap.get("lms1405m01_snoKind").get(
							l161s01a.getSnoKind())));
			l161s01a.setCaseTypeDscr(l161s01a.getCaseType()
					+ "."
					+ Util.trim(codeMap.get("lms1605m01_caseType").get(
							l161s01a.getCaseType())));
			// lms1605m01_useSpecialReason
			if (Util.equals(l161s01a.getUseSpecialReason(), "00")
					|| Util.equals(Util.trim(l161s01a.getUseSpecialReason()),
							"")) {
				l161s01a.setPropertyDscr(Util.equals(
						Util.trim(l161s01a.getProperty()), "") ? "" : LMSUtil
						.getProPerty(l161s01a.getProperty(), prop));
			} else {
				l161s01a.setPropertyDscr(l161s01a.getUseSpecialReason()
						+ "."
						+ Util.trim(codeMap.get("lms1605m01_useSpecialReason")
								.get(l161s01a.getUseSpecialReason())));
			}

			if (Util.equals(l161s01a.getUnitCase(), "Y")) {

				if (Util.equals(l161s01a.getQuotaCurr(), "")
						&& BigDecimal.ZERO
								.compareTo(l161s01a.getQuotaAmt() == null ? BigDecimal.ZERO
										: l161s01a.getQuotaAmt()) == 0) {
					l161s01a.setQuotaCurr("");
					l161s01a.setQuotaAmtDscr("");
				} else {
					l161s01a.setQuotaCurr(l161s01a.getQuotaCurr() == null ? ""
							: l161s01a.getQuotaCurr());

					String formatAmt = new NumericFormatter(
							"###,###,###,###,###,###,###.##").reformat(l161s01a
							.getQuotaAmt());
					l161s01a.setQuotaAmtDscr(l161s01a.getQuotaAmt() == null ? ""
							: formatAmt);

				}

			} else {
				l161s01a.setQuotaCurr("");
				l161s01a.setQuotaAmtDscr("");
			}

			if (UtilConstants.DEFAULT.是.equals(l161s01a.getChkYN())) {
				l161s01a.setChkYN("V");
			} else {
				l161s01a.setChkYN("X");
			}

		}

		return new CapGridResult(l161s01as, page.getTotalRow());
	}

	/**
	 * RPA查詢名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL161S01E(ISearch pageSetting,	PageParameters params) throws CapException {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);

		// 查這份文件的MainId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01E.class, pageSetting);

		// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
		Map<String, String> RpaCustListRelation = codeTypeService
				.findByCodeType("RpaCustListRelation", LMSUtil.getLocale()
						.toString());

		List<L161S01E> l161s01es = (List<L161S01E>) page.getContent();
		for (L161S01E l161s01e : l161s01es) {
			StringBuilder sb = new StringBuilder();

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			// 不能CALL AMLRelateService 因為會觸發交易，導致model 會把修改後的值真的存起來
			String[] strs = this.sortCustRelation(Util.trim(
					l161s01e.getCustRelation()).split(","));

			String custRelationIndex = "";
			for (String s : strs) {
				if (Util.equals(Util.trim(custRelationIndex), "")) {
					custRelationIndex = s;
				}
				if (sb.length() > 0)
					sb.append("/");
				// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
				sb.append(Util.trim(RpaCustListRelation.get(s)));

			}
			l161s01e.setCustRelationIndex(sb.toString());
			l161s01e.setCustRelation(custRelationIndex);
		}

		return new CapGridResult(l161s01es, page.getTotalRow(), null);
	}

	/**
	 * RPA查詢結果
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryRpaInfo(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] codeType = { "l161s01d_type" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1601M01Page.class);
		Page<? extends GenericBean> page = lms1601Service.findPage(
				L161S01D.class, pageSetting);

		Map<String, IFormatter> fmtMap = new HashMap<String, IFormatter>();

		fmtMap.put("type", new CodeTypeFormatter(codeTypeService,
				"l161s01d_type", CodeTypeFormatter.ShowTypeEnum.Desc));
		fmtMap.put("status", new CodeTypeFormatter(codeTypeService,
				"l161s01d_status", CodeTypeFormatter.ShowTypeEnum.Desc));

		List<L161S01D> l161s01ds = (List<L161S01D>) page.getContent();
		for (L161S01D l161s01d : l161s01ds) {

		}

		return new CapGridResult(l161s01ds, page.getTotalRow(), fmtMap);
	}

	// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	public String[] sortCustRelation(String[] strs) {

		// 對陣列進行排序
		// 轉成數字後再SORT
		List<String> asList = Arrays.asList(strs);
		List<Integer> newList = new ArrayList();

		for (String str : asList) {
			if (Util.notEquals(str, "")) {
				newList.add(new BigDecimal(str).intValue());
			}
		}

		int index = 0;
		int[] newIntArr = new int[newList.toArray().length];
		for (Integer xInt : newList) {
			newIntArr[index] = xInt;
			index++;
		}

		// 對陣列進行排序
		Arrays.sort(newIntArr);

		// 回復原來的陣列
		index = 0;
		String[] rtnList = new String[newList.toArray().length];
		for (Integer intX : newIntArr) {

			String newStr = new BigDecimal(intX).toPlainString();
			rtnList[index] = newStr;
			index++;

		}

		return rtnList;
	}

	// J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
	public CapMapGridResult queryIVRFiltergird(ISearch pageSetting,	PageParameters params) throws CapException {

		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = lms1601Service.getIVRFiltergrid(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	public CapMapGridResult queryIVRgrid(ISearch pageSetting, PageParameters params) throws CapException {

		String oid = params.getString(EloanConstants.MAIN_OID);
		List<Map<String, Object>> list = lms1601Service.getIVRgrid(oid);

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				list, list.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	/**
	 * 查詢主從債務人資料表的grid
	 * 
	 * 
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryPeopleForSetPriority(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String[] oids = params.getStringArray("oids"); // 只會有一筆

		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		L160M01A l160m01a = lms1601Service.findL160M01AByMaindId(mainId);

		L162S01A l162s01a = lms1601Service.findModelByOid(L162S01A.class,
				oids[0]);

		String cntrNo = l162s01a.getCntrNo();
		String custId = l162s01a.getCustId();
		String dupNo = l162s01a.getDupNo();
		String rId = l162s01a.getRId();
		String rDupNo = l162s01a.getRDupNo();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		LinkedList<L162S01A> keyManList = new LinkedList<L162S01A>();
		LinkedList<L162S01A> otherList = new LinkedList<L162S01A>();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId",
				l160m01a.getMainId());
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		pageSetting.addSearchModeParameters(SearchMode.IN, "rType",
				new String[] { UtilConstants.lngeFlag.連帶保證人,
						UtilConstants.lngeFlag.ㄧ般保證人 });

		Page<? extends GenericBean> page = lms1601Service.findPage(
				L162S01A.class, pageSetting);

		List<L162S01A> list = (List<L162S01A>) page.getContent();

		// 再刪選需要的保證人(保證人企業戶且為)
		List<L162S01A> newList = lms1601Service.findL162s01aNeedPriority(list,
				cntrNo);

		List<Map<String, Object>> m01List = new ArrayList<Map<String, Object>>();

		if (newList != null && !newList.isEmpty()) {

			for (L162S01A l162m01a : newList) {

				Map<String, Object> m01Map = CapBeanUtil.bean2Map(l162m01a,
						new String[] { "oid", "mainId", "custId", "dupNo",
								"cntrNo", "rId", "rDupNo", "rName", "rKindM",
								"rKindD", "rCountry", "rType", "dueDate",
								"dataSrc", "guaPercent", "priority" });

				StringBuilder show = new StringBuilder("");
				String rkindD = Util.trim(l162m01a.getRKindD());
				int rkindm = Util.parseInt(l162m01a.getRKindM());
				switch (rkindm) {
				case 1:
					m01Map.put(
							"rKindD",
							rkindD
									+ Util.trim(codeMap.get("Relation_type1")
											.get(rkindD)));

					break;
				case 2:
					m01Map.put(
							"rKindD",
							rkindD
									+ Util.trim(codeMap.get("Relation_type2")
											.get(rkindD)));

					break;
				case 3:
					char[] kind = rkindD.toCharArray();
					if (kind.length == 2) {
						String kind1 = (String) codeMap.get("Relation_type31")
								.get(String.valueOf(kind[0]));
						String kind2 = (String) codeMap.get("Relation_type32")
								.get(String.valueOf(kind[1]));
						show.append(rkindD).append(" ");
						show.append(kind1).append(" - ").append(kind2);
					}

					m01Map.put("rKindD", show.toString());

					break;

				}
				// 主要借款人統編
				m01Map.put("custId",
						l162m01a.getCustId() + " " + l162m01a.getDupNo());

				// 主要借款人統編
				m01Map.put("rId",
						l162m01a.getCustId() + " " + l162m01a.getDupNo());

				// 相關身分
				String rType = l162m01a.getRType();
				m01Map.put(
						"rType",
						rType
								+ Util.trim(codeMap.get("lms1605s03_rType")
										.get(rType)));

				// 國別
				m01Map.put(
						"rCountry",
						(String) codeMap.get(CodeTypeEnum.國家代碼.getCode()).get(
								l162m01a.getRCountry()));

				m01Map.putAll(m01Map);
				m01List.add(m01Map);

			}
		}

		CapMapGridResult m01Result = new CapMapGridResult(m01List,
				m01List.size());

		return m01Result;
	}
	
	/**
	 * G-113-0036 查詢動審後攤貸比率Grid 資料
	 * 
	 * @param pageSetting ISearch
	 * @param paramsPageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01e_af(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params.getString("cntrMainId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1601Service.findPage(L140M01E_AF.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("shareBrId", new BranchNameFormatter(branchService, ShowTypeEnum.ID_Name)); //
		result.setDataReformatter(dataReformatter);
		List<L140M01E_AF> l140m01e_afs = (List<L140M01E_AF>) page.getContent();
		for (L140M01E_AF l140m01e_af : l140m01e_afs) {
			if (UtilConstants.Cntrdoc.shareType.以比例計算.equals(l140m01e_af.getShareFlag())) {
				l140m01e_af.setShowRate(l140m01e_af.getShareRate1() + "/"
						+ l140m01e_af.getShareRate2());
			} else {
				l140m01e_af.setShowRate("");
			}
		}
		return result;
	}
}
