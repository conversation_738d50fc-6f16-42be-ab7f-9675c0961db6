package com.mega.eloan.lms.las.report;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedList;
import java.util.List;

import com.iisigroup.cap.component.PageParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.model.L192M01A;

import tw.com.iisi.cap.exception.CapException;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 稽核工作底稿共用的合併列印
 * <AUTHOR>
 *
 */
public abstract class AbstractLasReportMergeService implements
		FileDownloadService, LasReportMergeService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(AbstractLasReportMergeService.class);

	/**
	 * 合併列印，是否列印頁碼
	 * 
	 * @return 是否顯示頁碼
	 */
	public abstract boolean showPaginate();

	public List<InputStream> generateLasMergeReport(PageParameters params)
			throws CapException {

		String mainOids = params.getString("mainOids");
		String[] _mainOid = mainOids.split(UtilConstants.Mark.SPILT_MARK);
		List<InputStream> pdfNameList = new LinkedList<InputStream>();

		for (String mainOid : _mainOid) {
			ReportGenerator rptGenerator = new ReportGenerator();

			L192M01A meta = getLasData(mainOid);

			if (meta == null) {
				continue;
			}
			setLasReportData(rptGenerator, mainOid, meta.getShtType());

			OutputStream out = null;
			InputStream input = null;
			try {
				out = rptGenerator.generateReport();
				input = new ByteArrayInputStream(
						((ByteArrayOutputStream) out).toByteArray());
				pdfNameList.add(input);
			} catch (Exception ex) {
				throw new CapException(ex.getCause(), ex.getClass());
			} finally {
				if (out != null) {
					try {
						out.close();
					} catch (IOException ex) {
						LOGGER.error("[generateReport]close() Exception!!", ex);
					}
				}
				if (input != null) {
					try {
						input.close();
					} catch (IOException ex) {
						LOGGER.error("[generateReport]close() Exception!!", ex);
					}
				}
			}
		}
		return pdfNameList;
	}

	/**
	 * 設定工作底稿要列印的內容
	 * 
	 * @param rptGenerator reportTool
	 * @param mainOid key
	 * @param shtType 工作底稿種類
	 */
	public abstract void setLasReportData(ReportGenerator rptGenerator,
			String mainOid, String shtType);

	/**
	 * 取得稽核工作底稿主文件資訊
	 * 
	 * @param oid key
	 * @return 稽核工作底稿主文件
	 */
	public abstract L192M01A getLasData(String oid);

	@Override
	public byte[] getContent(PageParameters params) throws CapException {

		List<InputStream> pdfNameList = this.generateLasMergeReport(params);

		OutputStream out = null;
		try {
			if (pdfNameList != null && !pdfNameList.isEmpty()) {
				out = new ByteArrayOutputStream();
				PdfTools.concatPDFs(pdfNameList, out, showPaginate());
				return ((ByteArrayOutputStream) out).toByteArray();
			}
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException ex) {
					LOGGER.error("[generateMergeReport]close() Exception!!", ex);
				}
			}
		}
		return null;
	}
}
