<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<script type="text/javascript" src="pagejs/fms/CLS2901M01Page.js?r=20230728"></script>
        <div class="button-menu funcContainer" id="buttonPanel">
        	<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnSave" />
				
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>				
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_APPROVE" />
				
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_REMOVE" />
				
				<button type="button" id="btnAcceptRemove">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">解除覆核</wicket:message>
				</button>
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnRemove" />				
				<button type="button" id="btnRemove">
					<!-- 解除 -->
					<span class="ui-icon ui-icon-unlocked" />
					<wicket:message key="button.remove"/>
				</button>	
				
				<button type="button" id="btnInputRemove">
					<!-- 解除 -->
					<wicket:message key="button.inputRemove"/>
				</button>	
			</wicket:enclosure>        	
			
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>	
			
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                    	<wicket:message key="doc.title">人頭戶或代辦案件黑名單建檔作業</wicket:message>
					</td>
                </tr>
            </table>
        </div>
		
		<div style='padding:0 1em;'>
        <form id="tabForm">
        	<input type="hidden" id="licenseWord" name="licenseWord" value="年登字">
        	<fieldset>
                <legend>
                    <b><wicket:message key="label.basicData">基本資料</wicket:message></b>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<tr style='vertical-align:top;'>
							<td width="20%" class="hd1" nowrap>
								<wicket:message key="C900M01J.custId">被通報統一編號</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="40%">
	                        	<input type="text" id="custId" name="custId" class="required" maxlength="10" size="12" />
								- <input type="text" id="dupNo" name="dupNo" class="required" maxlength="1" size="1" value='0' />
								<div>
									<span class='color-red'><wicket:message key="label.memo_LNUNID">待主管覆核後，將上傳中心。</wicket:message></span>
								</div>
								<div>
									<span class='color-red'><wicket:message key="label.memo_forBAD">調查結果為「異常」的客戶，才需在此建檔。</wicket:message></span>
								</div>
	                        </td>
							<td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01J.ownBrId">分行別</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="20%">
								<input type="text" id="ownBrId" name="ownBrId"  class="hide required" readonly  />
	                        	<input type="text" id="ownBrIdDesc" name="ownBrIdDesc" class="" readonly />
	                        </td>
	                    </tr>
						<tr>
							<td width="20%" class="hd1" nowrap>
								<input type="text" class="hide" id="mainOid" name="mainOid" />
	                            <wicket:message key="C900M01J.custName">被通報姓名/名稱</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
	                        	<input type="text" id="custName" name="custName" class="required" maxlengthC="40" size="40" />
	                        </td>
	                        <td class="hd1" nowrap>
	                            <wicket:message key="doc.docStatus">文件狀態</wicket:message>&nbsp;&nbsp;								
	                        </td>
	                        <td >
								<b><span id="docStatus" class="color-red" /></b>
	                        </td>
	                    </tr>						
						<tr style='vertical-align:top;'>
							<td class="hd1" nowrap>
	                            <wicket:message key="C900M01J.category">被通報類別</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<table class='td2'>
	                        		<tr>
	                        			<td class='noborder'><label><input type="radio" id="category" name="category" value="P" class="" /><wicket:message key="C900M01J.category.P">疑似代辦案件</wicket:message></label>
										<span style='padding-left:36px;' /><label><input type="radio" id="category" name="category" value="B" class="" /><wicket:message key="C900M01J.category.B">疑似人頭戶案件</wicket:message></label>
											<div style='margin-left:36px;'>
											
												<table class='td2' width='100%'>	
													<td class="hd1">
                                	<span class="text-red">＊</span>
									<wicket:message key="C900M01J.introduceSrc">案件來源</wicket:message>
									<br/>
                                </td>
                                <td>
                                	 <select name="introduceSrc" id="introduceSrc" itemtype="L140M01A_introductionSource"/>
									 <div id="employeeDiv" style="display:none;">
                	 					<table>
	                	 					<tr>
				                	 			<td class="hd1">
				                                	<wicket:message key="C900M01J.megaEmpno">引介行員代號</wicket:message>
				                                </td>
				                                <td width="60%">
				                                	<input type="text" id="megaEmpNo" name="megaEmpNo" maxlength="6" size="6" class='numText '
																onblur="if($.trim($(this).val()).length > 0){var var_megaEmpNo = '000000'+$(this).val();$(this).val(var_megaEmpNo.substr(var_megaEmpNo.length-6, 6));}" />
												</td>
											</tr>
	                	 				</table>
                	 				</div>
									<!-- 引介來源 選擇分行 -->
									<div id="selectBranchDiv" class="content" style="display:none">
										<table width="100%" class="tb2">
											<tr>
												<td class="hd2" nowrap><wicket:message key="branchType"><!--分行別--></wicket:message> 
												</td>
												<td colspan="3">
													<select id="selectBranch" name="selectBranch" class="boss"/> 
												</td>
											</tr>
										</table>
									</div>
									<div id="realEstateAgentDiv" style="display:none;">
                	 					<button type="button" id="importRealEstateAgentInfo"><wicket:message key="button.importRealEstateAgentInfo">引進房仲資訊</wicket:message></button>
                	 					<table>
	                	 					<tr>
				                	 			<td class="hd1" nowrap><wicket:message key="C900M01J.agntNo">引介房仲代號</wicket:message></td>
			                	 				<td>
			                	 					<select id="agntNo" itemtype="L140M01A_agntNo" name="agntNo" />
												</td>
											</tr>
											<tr>
				                	 			<td class="hd1" ><wicket:message key="C900M01J.agentCertNo">引介房仲證書(明)字號</wicket:message>				                	 			</td>
			                	 				<td>
			                	 					<input type="text" id="licenseYear" name="licenseYear" size="3" maxlength="3" class="required"/>
													<wicket:message key="C900M01J.licenseWord">年登字</wicket:message>
													<input type="text" id="licenseNumber" name="licenseNumber" size="6" maxlength="6" class="required"/>
													<wicket:message key="C900M01J.licenseNumber">號</wicket:message>
													<br/>
													<span class="text-red"><wicket:message key="C900M01J.realEstateAgentNoticeItem">如無法得知年份及證號，年份請輸入：000，證號請輸入：000000</wicket:message></span>
												</td>
											</tr>
	                	 				</table>
                	 				</div>
									<div id="megaSubCompanyDiv" style="display:none;">
										<table>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1" nowrap><wicket:message key="C900M01J.megaCode">引介子公司代號</wicket:message>
				                	 			</td>
				                	 			<td><select id="megaCode" name="megaCode" itemType="L140S02A_megaCode" />
				                	 			</td>
											</tr>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"><wicket:message key="C900M01J.subUnitNo">引介子公司分支代號</wicket:message>
				                	 			</td>
				                	 			<td>
				                	 				<select id="subUnitNo" name="subUnitNo"  /> <!-- J-107-0136 -->
				                	 			</td>
				                	 		</tr>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"  ><wicket:message key="C900M01J.subEmpNo">引介子公司員工編號</wicket:message>
				                	 			</td>
				                	 			<td><input type="text" id="subEmpNo" name="subEmpNo" maxlength="6" size="6" class=' '
													onblur="if($.trim($(this).val()).length > 0 && (new RegExp('[a-zA-Z]').test($(this).val())==false)){var var_subEmpNo = '000000'+$(this).val();$(this).val(var_subEmpNo.substr(var_subEmpNo.length-6, 6));}" />
				                	 			</td>
											</tr>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"  ><wicket:message key="C900M01J.subEmpNm">引介子公司員工姓名</wicket:message>
				                	 			</td>
				                	 			<td><input type="text" id="subEmpNm" name="subEmpNm"  maxlength="10" maxlengthC="10" size="18" />
				                	 			</td>
				                	 		</tr>
	                	 				</table>
									</div>
									<div id="customerOrCompanyDiv" style="display:none;">
                	 					<button type="button" id="importCustOrComButton"><wicket:message key="button.importCustomerOrCompanyInfo">引進客戶/企業資訊</wicket:message></button>
                	 					<table>
	                	 					<tr>
				                	 			<td class="hd1" nowrap><wicket:message key="C900M01J.introCustId">客戶ID / 總戶(建商)統編</wicket:message></td>
			                	 				<td>
			                	 					<input type="text" id="introCustId" name="introCustId" readonly size="10"/>
													<input type="text" id="introDupNo" name="introDupNo" readonly size="2"/>
												</td>
											</tr>
											<tr>
				                	 			<td class="hd1" ><wicket:message key="C900M01J.introCustName">客戶名稱 / 總戶(建商)名稱</wicket:message></td>
			                	 				<td><input type="text" id="introCustName" name="introCustName" readonly/></td>
											</tr>
	                	 				</table>
                	 				</div>
									<div id="buildCaseDiv" style="display:none;">
										<table>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"><wicket:message key="C900M01J.builderName">建商名稱</wicket:message></td>
				                	 			<td>
				                	 				<input type="text" id="introBuilderName" name="introBuilderName" maxlength="90" size="30" maxlengthC="30"/>
				                	 			</td>
											</tr>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"  ><wicket:message key="C900M01J.biuldCaseName">建案名稱</wicket:message></td>
				                	 			<td>
				                	 				<input type="text" id="introBiuldCaseName" name="introBiuldCaseName" maxlength="90" maxlengthC="30" size="30" />
				                	 			</td>
				                	 		</tr>
	                	 				</table>
									</div>
									<div id="introducerNameDiv" style="display:none;margin-top:5px;" >
										<table>
				                	 		<tr class="docCode5Hide" >
				                	 			<td class="hd1"><wicket:message key="C900M01J.introducerName">引介人姓名</wicket:message></td>
				                	 			<td width="50%">
				                	 				<input type="text" id="introducerName" name="introducerName" maxlength="90" maxlengthC="30" size="20"/>
				                	 			</td>
				                	 		</tr>
	                	 				</table>
									</div>
									<div id="landsmenNoticeItemDiv" style="display:none;margin-top:15px;" >
										<span class="text-red">
											<wicket:message key="C900M01J.landsmenIntroductionNoticeItem">請在本建檔作業建置後，另依地政士黑名單建置原則至地政士黑名單維護系統註記</wicket:message>
										</span>
									</div>
									<div id="openBox_realEstateIntroduction" style="display:none;" >
										<div id="realEstateGrid" />
									</div>
								</td>
							</table>
												
							<table class='td2' width='100%' style='margin-top:12px;'>	
								<tr style='vertical-align:top;'>
									<td  class="hd1" nowrap>
			                            <wicket:message key="C900M01J.relPartyId">案件關係人統編</wicket:message>&nbsp;&nbsp;
			                        </td>
			                        <td>
										<input type="text" id="relPartyId" name="relPartyId" class=" " maxlength="10" size="10" />	                        	
			                        </td>
								</tr>
								<tr>
									<td  class="hd1" nowrap>
			                            <wicket:message key="C900M01J.relPartyName">案件關係人名稱</wicket:message>&nbsp;&nbsp;
			                        </td>
			                        <td>
			                        	<input type="text" id="relPartyName" name="relPartyName" class=" " maxlengthC="50" size="40" />
			                        </td>	
			                    </tr>		
							</table>
											</div>
	                        			</td>
	                        		<tr>
	                        		</tr>
	                        			<td class='noborder'><label><input type="radio" id="category" name="category" value="I" class="" /><wicket:message key="C900M01J.category.I">疑似代辦案件引介人</wicket:message></label>
	                        			</td>
	                        		</tr>								
								</table>
	                        </td>
	                    </tr>						
						<tr style='vertical-align:top;'>
							<td  class="hd1" nowrap>
	                            <wicket:message key="C900M01J.lnflag">案件訊息態樣</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<div wicket:id="C900M01J_lnflag_Label"></div>
	                        </td>
	                    </tr>					
						<tr style='vertical-align:top;'>
							<td  class="hd1" nowrap>
	                            <wicket:message key="C900M01J.memo">備註</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<textarea name="memo" id="memo" cols="87" rows="3" class="txt_mult" maxlength="300" maxlengthC="100" ></textarea>
	                        </td>
	                    </tr>
					</tbody>
	            </table>
			</fieldset>
			
			<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog">文件異動紀錄</wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog">
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.creator">文件建立者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" name="creator"></span>
                                    (<span id="createTime" name="createTime"></span>)
                                </td>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.lastUpdater">最後異動者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="updater" name="updater"></span>
                                    (<span id="updateTime" name="updateTime"></span>)
                                </td>
                            </tr>                            
                        </tbody>
                    </table>
                </fieldset>
				
				<fieldset>
                <legend>
                    <b><wicket:message key="label.dcData">解除資料</wicket:message></b>
                </legend>
				<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	                <tbody>
	                	<tr>
							<td width="20%" class="hd1" nowrap>
								<wicket:message key="C900M01J.dcUpdater">申請解除人員</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
                                    <span id="dcUpdater" name="dcUpdater"></span>
                                    (<span id="dcUpdateTime" name="dcUpdateTime"></span>)
	                        </td>
							<td width="20%" class="hd1" nowrap>
	                            <wicket:message key="C900M01J.dcApprover">核准解除人員</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td width="30%">
                                    <span id="dcApprover" name="dcApprover"></span>
                                    (<span id="dcApproveTime" name="dcApproveTime"></span>)
	                        </td>
	                    </tr>
						<tr style='vertical-align:top;'>
							<td  class="hd1" nowrap>
	                            <wicket:message key="C900M01J.dcMemo">解除說明</wicket:message>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
	                        	<textarea id="dcMemo" name="dcMemo" style="width:800px;height:100px" disabled>
								</textarea>
					
	                        </td>
	                    </tr>
					</tbody>
	            </table>
			</fieldset>
        </form>			
		</div>
		
		<!-- ===================================== -->
		<!-- 受檢單位洽辦情形 -->
		<div class="tb2" id="divReg_dcMemo" style="display:none;">
			<form id='reg_dcMemo_form'>
			<textarea id="reg_dcMemo" name="reg_dcMemo" style="width:800px;height:80px" maxlength='300' maxlengthC='100'>
			</textarea>
				
			</form>
		</div>		
	</wicket:extend>
</body>
</html>
