/* 
 * L120S21C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** LGD額度擔保品檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S21C", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L120S21C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * 文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 已分配額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO_S21C", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo_s21c;

	/**
	 * 建檔種類
	 * <p/>
	 * 擔保品<br/>
	 * 未建擔保品
	 */
	@Size(max = 1)
	@Column(name = "COLLTYPE_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String collType_s21c;

	/**
	 * 未建檔擔保品種類
	 * <p/>
	 * 可編輯<br/>
	 * 020100機器設備<br/>
	 * 030100 定存單<br/>
	 * 030700 股票
	 */
	@Size(max = 6)
	@Column(name = "COLKIND_S21C", length = 6, columnDefinition = "CHAR(6)")
	private String colKind_s21c;

	/**
	 * 擔保品幣別
	 * <p/>
	 * 可編輯
	 */
	@Size(max = 3)
	@Column(name = "COLCURR_S21C", length = 3, columnDefinition = "CHAR(3)")
	private String colCurr_s21c;

	/**
	 * 購置時價
	 * <p/>
	 * 可編輯
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLTIMEVALUE_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colTimeValue_s21c;

	/**
	 * 前順位設定金額
	 * <p/>
	 * 可編輯
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLPRERGSTAMT_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colPreRgstAmt_s21c;

	/**
	 * 擔保品設定金額
	 * <p/>
	 * 可編輯
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLRGSTAMT_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colRgstAmt_s21c;

	/**
	 * 是否與其他額度共用
	 * <p/>
	 * 可編輯<br/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "COLCOUSEFLAG_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String colCoUseFlag_s21c;

	/**
	 * 分配比率
	 * <p/>
	 * 可編輯
	 */
	@Digits(integer = 7, fraction = 4, groups = Check.class)
	@Column(name = "COLSHARERATE_S21C", columnDefinition = "DECIMAL(7,4)")
	private BigDecimal colShareRate_s21c;

	/**
	 * 擔保品回收率
	 * <p/>
	 * E4～E33
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "COLRATE_S21C", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal colRate_s21c;

	/** 預估擔保品回收 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLESTRECOVERY_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colEstRecovery_s21c;

	/** 順位預估擔保品回收 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLRGSTRECOVERY_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colRgstRecovery_s21c;

	/**
	 * 分配後擔保品回收
	 * <p/>
	 * TWD
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLRECOVERY_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colRecovery_s21c;

	/** 分配後擔保品回收Twd **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "COLRECOVERYTWD_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal colRecoveryTwd_s21c;

	/** 估價報告書分行 **/
	@Size(max = 3)
	@Column(name = "CMSBRANCH_S21C", length = 3, columnDefinition = "CHAR(3)")
	private String cmsBranch_s21c;

	/** 估價報告書區部別 **/
	@Size(max = 1)
	@Column(name = "CMSTYPECD_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String cmsTypeCd_s21c;

	/** 估價報告書統編 **/
	@Size(max = 10)
	@Column(name = "CMSCUSTID_S21C", length = 10, columnDefinition = "CHAR(10)")
	private String cmsCustId_s21c;

	/** 估價報告書重複序號 **/
	@Size(max = 1)
	@Column(name = "CMSDUPNO_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String cmsDupNo_s21c;

	/** 估價報告書擔保品編號 **/
	@Size(max = 9)
	@Column(name = "CMSCOLLNO_S21C", length = 9, columnDefinition = "VARCHAR(9)")
	private String cmsCollNo_s21c;

	/** 估價報告書OID **/
	@Size(max = 32)
	@Column(name = "CMSOID_S21C", length = 32, columnDefinition = "VARCHAR(32)")
	private String cmsOid_s21c;

	/** 估價報告書CollKey **/
	@Size(max = 32)
	@Column(name = "CMSCOLLKEY_S21C", length = 32, columnDefinition = "VARCHAR(32)")
	private String cmsCollKey_s21c;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 輸入資料檢誤完成(Y/N) **/
	@Size(max = 1)
	@Column(name = "CHKYN_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN_s21c;

	/**
	 * 信保保證成數
	 * <p/>
	 * 可編輯
	 */
	@Digits(integer = 5, fraction = 2, groups = Check.class)
	@Column(name = "CMSGRTRT_S21C", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal cmsGrtrt_s21c;

	/** 主副擔保 **/
	@Size(max = 1)
	@Column(name = "COLMAJORTYPE_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String colMajorType_s21c;

	/** 發行公司是否為借款人之同一關係企業 **/
	@Size(max = 1)
	@Column(name = "ISELCRECOM_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String isElcreCom_s21c;

	/**
	 * 擔保品是否為多筆聯貸案額度共用
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "UNIONFLAG_S21C", length = 1, columnDefinition = "CHAR(1)")
	private String unionFlag_s21c;

	/** 聯貸幣別 **/
	@Size(max = 3)
	@Column(name = "UNIONCURR_S21C", length = 3, columnDefinition = "CHAR(3)")
	private String unionCurr_s21c;

	/** 本行參貸額度合計 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "SYNDAMT_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal syndAmt_s21c;

	/** 聯合授信案總金額合計 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "UNIONAMT_S21C", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal unionAmt_s21c;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得文件編號
	 * <p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定文件編號
	 * <p/>
	 * 簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得已分配額度序號 **/
	public String getCntrNo_s21c() {
		return this.cntrNo_s21c;
	}

	/** 設定已分配額度序號 **/
	public void setCntrNo_s21c(String value) {
		this.cntrNo_s21c = value;
	}

	/**
	 * 取得建檔種類
	 * <p/>
	 * 擔保品<br/>
	 * 未建擔保品
	 */
	public String getCollType_s21c() {
		return this.collType_s21c;
	}

	/**
	 * 設定建檔種類
	 * <p/>
	 * 擔保品<br/>
	 * 未建擔保品
	 **/
	public void setCollType_s21c(String value) {
		this.collType_s21c = value;
	}

	/**
	 * 取得未建檔擔保品種類
	 * <p/>
	 * 可編輯<br/>
	 * 020100機器設備<br/>
	 * 030100 定存單<br/>
	 * 030700 股票
	 */
	public String getColKind_s21c() {
		return this.colKind_s21c;
	}

	/**
	 * 設定未建檔擔保品種類
	 * <p/>
	 * 可編輯<br/>
	 * 020100機器設備<br/>
	 * 030100 定存單<br/>
	 * 030700 股票
	 **/
	public void setColKind_s21c(String value) {
		this.colKind_s21c = value;
	}

	/**
	 * 取得擔保品幣別
	 * <p/>
	 * 可編輯
	 */
	public String getColCurr_s21c() {
		return this.colCurr_s21c;
	}

	/**
	 * 設定擔保品幣別
	 * <p/>
	 * 可編輯
	 **/
	public void setColCurr_s21c(String value) {
		this.colCurr_s21c = value;
	}

	/**
	 * 取得購置時價
	 * <p/>
	 * 可編輯
	 */
	public BigDecimal getColTimeValue_s21c() {
		return this.colTimeValue_s21c;
	}

	/**
	 * 設定購置時價
	 * <p/>
	 * 可編輯
	 **/
	public void setColTimeValue_s21c(BigDecimal value) {
		this.colTimeValue_s21c = value;
	}

	/**
	 * 取得前順位設定金額
	 * <p/>
	 * 可編輯
	 */
	public BigDecimal getColPreRgstAmt_s21c() {
		return this.colPreRgstAmt_s21c;
	}

	/**
	 * 設定前順位設定金額
	 * <p/>
	 * 可編輯
	 **/
	public void setColPreRgstAmt_s21c(BigDecimal value) {
		this.colPreRgstAmt_s21c = value;
	}

	/**
	 * 取得擔保品設定金額
	 * <p/>
	 * 可編輯
	 */
	public BigDecimal getColRgstAmt_s21c() {
		return this.colRgstAmt_s21c;
	}

	/**
	 * 設定擔保品設定金額
	 * <p/>
	 * 可編輯
	 **/
	public void setColRgstAmt_s21c(BigDecimal value) {
		this.colRgstAmt_s21c = value;
	}

	/**
	 * 取得是否與其他額度共用
	 * <p/>
	 * 可編輯<br/>
	 * Y/N
	 */
	public String getColCoUseFlag_s21c() {
		return this.colCoUseFlag_s21c;
	}

	/**
	 * 設定是否與其他額度共用
	 * <p/>
	 * 可編輯<br/>
	 * Y/N
	 **/
	public void setColCoUseFlag_s21c(String value) {
		this.colCoUseFlag_s21c = value;
	}

	/**
	 * 取得分配比率
	 * <p/>
	 * 可編輯
	 */
	public BigDecimal getColShareRate_s21c() {
		return this.colShareRate_s21c;
	}

	/**
	 * 設定分配比率
	 * <p/>
	 * 可編輯
	 **/
	public void setColShareRate_s21c(BigDecimal value) {
		this.colShareRate_s21c = value;
	}

	/**
	 * 取得擔保品回收率
	 * <p/>
	 * E4～E33
	 */
	public BigDecimal getColRate_s21c() {
		return this.colRate_s21c;
	}

	/**
	 * 設定擔保品回收率
	 * <p/>
	 * E4～E33
	 **/
	public void setColRate_s21c(BigDecimal value) {
		this.colRate_s21c = value;
	}

	/** 取得預估擔保品回收 **/
	public BigDecimal getColEstRecovery_s21c() {
		return this.colEstRecovery_s21c;
	}

	/** 設定預估擔保品回收 **/
	public void setColEstRecovery_s21c(BigDecimal value) {
		this.colEstRecovery_s21c = value;
	}

	/** 取得順位預估擔保品回收 **/
	public BigDecimal getColRgstRecovery_s21c() {
		return this.colRgstRecovery_s21c;
	}

	/** 設定順位預估擔保品回收 **/
	public void setColRgstRecovery_s21c(BigDecimal value) {
		this.colRgstRecovery_s21c = value;
	}

	/**
	 * 取得分配後擔保品回收
	 * <p/>
	 * TWD
	 */
	public BigDecimal getColRecovery_s21c() {
		return this.colRecovery_s21c;
	}

	/**
	 * 設定分配後擔保品回收
	 * <p/>
	 * TWD
	 **/
	public void setColRecovery_s21c(BigDecimal value) {
		this.colRecovery_s21c = value;
	}

	/** 取得分配後擔保品回收Twd **/
	public BigDecimal getColRecoveryTwd_s21c() {
		return this.colRecoveryTwd_s21c;
	}

	/** 設定分配後擔保品回收Twd **/
	public void setColRecoveryTwd_s21c(BigDecimal value) {
		this.colRecoveryTwd_s21c = value;
	}

	/** 取得估價報告書分行 **/
	public String getCmsBranch_s21c() {
		return this.cmsBranch_s21c;
	}

	/** 設定估價報告書分行 **/
	public void setCmsBranch_s21c(String value) {
		this.cmsBranch_s21c = value;
	}

	/** 取得估價報告書區部別 **/
	public String getCmsTypeCd_s21c() {
		return this.cmsTypeCd_s21c;
	}

	/** 設定估價報告書區部別 **/
	public void setCmsTypeCd_s21c(String value) {
		this.cmsTypeCd_s21c = value;
	}

	/** 取得估價報告書統編 **/
	public String getCmsCustId_s21c() {
		return this.cmsCustId_s21c;
	}

	/** 設定估價報告書統編 **/
	public void setCmsCustId_s21c(String value) {
		this.cmsCustId_s21c = value;
	}

	/** 取得估價報告書重複序號 **/
	public String getCmsDupNo_s21c() {
		return this.cmsDupNo_s21c;
	}

	/** 設定估價報告書重複序號 **/
	public void setCmsDupNo_s21c(String value) {
		this.cmsDupNo_s21c = value;
	}

	/** 取得估價報告書擔保品編號 **/
	public String getCmsCollNo_s21c() {
		return this.cmsCollNo_s21c;
	}

	/** 設定估價報告書擔保品編號 **/
	public void setCmsCollNo_s21c(String value) {
		this.cmsCollNo_s21c = value;
	}

	/** 取得估價報告書OID **/
	public String getCmsOid_s21c() {
		return this.cmsOid_s21c;
	}

	/** 設定估價報告書OID **/
	public void setCmsOid_s21c(String value) {
		this.cmsOid_s21c = value;
	}

	/** 取得估價報告書CollKey **/
	public String getCmsCollKey_s21c() {
		return this.cmsCollKey_s21c;
	}

	/** 設定估價報告書CollKey **/
	public void setCmsCollKey_s21c(String value) {
		this.cmsCollKey_s21c = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得輸入資料檢誤完成(Y/N) **/
	public String getChkYN_s21c() {
		return this.chkYN_s21c;
	}

	/** 設定輸入資料檢誤完成(Y/N) **/
	public void setChkYN_s21c(String value) {
		this.chkYN_s21c = value;
	}

	/** 設定信保保證成數 **/
	public void setCmsGrtrt_s21c(BigDecimal cmsGrtrt_s21c) {
		this.cmsGrtrt_s21c = cmsGrtrt_s21c;
	}

	/** 取得信保保證成數 **/
	public BigDecimal getCmsGrtrt_s21c() {
		return cmsGrtrt_s21c;
	}

	/** 設定主副擔保 **/
	public void setColMajorType_s21c(String colMajorType_s21c) {
		this.colMajorType_s21c = colMajorType_s21c;
	}

	/** 取得主副擔保 **/
	public String getColMajorType_s21c() {
		return colMajorType_s21c;
	}

	/** 設定發行公司是否為借款人之同一關係企業 **/
	public void setIsElcreCom_s21c(String isElcreCom_s21c) {
		this.isElcreCom_s21c = isElcreCom_s21c;
	}

	/** 取得發行公司是否為借款人之同一關係企業 **/
	public String getIsElcreCom_s21c() {
		return isElcreCom_s21c;
	}

	/**
	 * 取得擔保品是否為多筆聯貸案額度共用
	 * <p/>
	 * Y/N
	 */
	public String getUnionFlag_s21c() {
		return this.unionFlag_s21c;
	}

	/**
	 * 設定擔保品是否為多筆聯貸案額度共用
	 * <p/>
	 * Y/N
	 **/
	public void setUnionFlag_s21c(String value) {
		this.unionFlag_s21c = value;
	}

	/** 取得聯貸幣別 **/
	public String getUnionCurr_s21c() {
		return this.unionCurr_s21c;
	}

	/** 設定聯貸幣別 **/
	public void setUnionCurr_s21c(String value) {
		this.unionCurr_s21c = value;
	}

	/** 取得本行參貸額度合計 **/
	public BigDecimal getSyndAmt_s21c() {
		return this.syndAmt_s21c;
	}

	/** 設定本行參貸額度合計 **/
	public void setSyndAmt_s21c(BigDecimal value) {
		this.syndAmt_s21c = value;
	}

	/** 取得聯合授信案總金額合計 **/
	public BigDecimal getUnionAmt_s21c() {
		return this.unionAmt_s21c;
	}

	/** 設定聯合授信案總金額合計 **/
	public void setUnionAmt_s21c(BigDecimal value) {
		this.unionAmt_s21c = value;
	}
}
