<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
<body>
	<wicket:extend>
		<script type="text/javascript" src="pagejs/fms/CLS2801M01Page.js"></script>
        <div class="button-menu funcContainer" id="buttonPanel">
        	<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnSave" />
				
				<button type="button" id="btnSave">
					<span class="ui-icon ui-icon-jcs-04" />
					<wicket:message key="button.save">儲存</wicket:message>
				</button>
				<button type="button" id="btnSend">
					<span class="ui-icon ui-icon-jcs-02" />
					<wicket:message key="button.send">呈主管覆核</wicket:message>
				</button>				
			</wicket:enclosure>
			<!-- ===================================== -->
			<wicket:enclosure>
				<span wicket:id="_btnWAIT_APPROVE" />
				
				<button type="button" id="btnAccept">
					<span class="ui-icon ui-icon-check" />
					<wicket:message key="button.check">覆核</wicket:message>
				</button>
			</wicket:enclosure>     	
			<!-- ===================================== -->
			<button type="button" id="btnExit" class="forview">
				<span class="ui-icon ui-icon-jcs-01"></span>
				<wicket:message key="button.exit">離開</wicket:message>
			</button>
		</div>	
			
		<div class="tit2 color-black">
            <table width="100%">
                <tr>
                    <td width="100%">
                    	<wicket:message key="doc.title">天然及重大災害受災戶住宅補貼建檔作業</wicket:message>
					</td>
                </tr>
            </table>
        </div>
		
		<!--天然及重大災害受災戶住宅補貼-->
		<div style='padding:1em;'>
			<form id="tabForm">
				<fieldset>
					<legend>
	                    <strong><wicket:message key="label.basicData">基本資料</wicket:message>：</strong>
	                </legend>
					<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
						<tbody>
							<tr>
								<td width="18%" class="hd1">
									<wicket:message key="L140S02L.hold_no">毀損住宅所有權人戶籍號碼</wicket:message>
								</td>
								<td>
									<span id="hold_no" name="hold_no"></span>
	                            </td>
							</tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.owner_id">毀損住宅所有權人身分證字號</wicket:message>
	                            </td>
	                            <td>
	                            	<span id="owner_id" name="owner_id"></span>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.app_date">申請日期</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="app_date" id="app_date" class="date required" _requiredLength="10"  />
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.house_adr">毀損住宅所在地址</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="house_adr" id="house_adr" maxlength="102" maxlengthC="34" size="85" class="required"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.owner_nm">毀損住宅所有權人姓名</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="owner_nm" id="owner_nm" maxlength="30" maxlengthC="10" class="required"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.ownsp_id">毀損住宅所有權人配偶身分證字號</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="ownsp_id" id="ownsp_id" class="checkID max" maxlength="11" size="11"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.ownsp_nm">毀損住宅所有權人配偶姓名</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="ownsp_nm" id="ownsp_nm" maxlength="30" maxlengthC="10"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.spouse_id">借款人配偶身分證字號</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="spouse_id" id="spouse_id" class="checkID max" maxlength="11" size="11"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.spouse_nm">借款人配偶姓名</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="spouse_nm" id="spouse_nm" maxlength="30" maxlengthC="10"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.coll_ln">重建(購)、修繕貸款擔保品地號</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="coll_ln" id="coll_ln" maxlength="8" class="required"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.coll_bn">重建(購)、修繕貸款擔保品建號</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="coll_bn" id="coll_bn" maxlength="8" class="required"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.coll_addr">重建(購)、修繕貸款擔保品地址</wicket:message>
	                            </td>
	                            <td>
	                                <input type="text" name="coll_addr" id="coll_addr" maxlength="102" maxlengthC="34" size="85" class="required"/>
	                            </td>
	                        </tr>
							<tr>
	                            <td class="hd1">
	                                <wicket:message key="L140S02L.set_hold">建物所有權人是否設籍於受毀損住宅</wicket:message>
	                            </td>
	                            <td>
	                                <label>
	                                    <input type="radio" id="set_hold" name="set_hold" value="Y" class="required"/>
	                                    <wicket:message key="yes">是</wicket:message>
	                                </label>
	                                <label>
	                                    <input type="radio" id="set_hold" name="set_hold" value="N" class="required"/>
	                                    <wicket:message key="no">否</wicket:message>
	                                </label>
	                            </td>
	                        </tr>
						</tbody>
					</table>
				</fieldset>
			</form>
		</div>
		
		<div style='padding:1em;'>
        <form id="tabForm">
        			
			<fieldset>
                    <legend>
                        <b><wicket:message key="doc.docUpdateLog">文件異動紀錄</wicket:message></b>
                    </legend>
                    <div class="funcContainer">
                        <div wicket:id="_docLog">
                        </div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                            <tr>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.creator">文件建立者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="creator" name="creator"></span>
                                    (<span id="createTime" name="createTime"></span>)
                                </td>
                                <td width="20%" class="hd1">
                                    <wicket:message key="doc.lastUpdater">最後異動者</wicket:message>&nbsp;&nbsp;
                                </td>
                                <td width="30%">
                                    <span id="updater" name="updater"></span>
                                    (<span id="updateTime" name="updateTime"></span>)
                                </td>
                            </tr>                            
                        </tbody>
                    </table>
                </fieldset>
        </form>			
		</div>	
	</wicket:extend>
</body>
</html>
