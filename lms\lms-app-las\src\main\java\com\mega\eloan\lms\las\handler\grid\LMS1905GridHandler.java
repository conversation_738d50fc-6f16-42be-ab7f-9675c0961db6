package com.mega.eloan.lms.las.handler.grid;

import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.las.service.LMS1905Service;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.service.BranchService;

/**
 * 稽核工作底稿，此gridhandler專門處理房貸業務工作底稿之資料
 * 
 * <AUTHOR>
 * 
 */
@Scope("request")
@Controller("lms1905gridhandler")
public class LMS1905GridHandler extends AbstractGridHandler {

	@Resource
	LMS1905Service lms1905Service;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	/**
	 * 取得歷年來借款人資料，供挑選
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult getBorrows(ISearch pageSetting,
			PageParameters params) throws CapException {

		String branch = params.getString("brId");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				CreditDocStatusEnum.海外_已核准.getCode());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				branch);
		// List<SearchModeParameter> test =
		// pageSetting.getSearchModeParameters();
		// for(SearchModeParameter a: test){
		// System.out.println(a.getKey());
		// }
		pageSetting.setDistinct(true);
		// Page<L160M01A> page = lms1925Service.get1925V02(pageSetting);
		Page<Map<String, Object>> page = lms1905Service.getBorrows(branch,
				CreditDocStatusEnum.海外_已核准.getCode(), pageSetting);
		// page.getContent();
		// System.out.println(page);

		// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		// map.put("custId", new CustIdFormatter());
		// map.put("ownBrId", new BranchNameFormatter(branchService,
		// ShowTypeEnum.ID_Name));
		// map.put("updater", new UserNameFormatter(userInfoService));

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	// /**
	// * 房貸工作底稿 - 取得編製中當月資料
	// *
	// * @param pageSetting ISearch
	// * @param params PageParameters
	// * @param parent Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocEditing(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	//
	// String branch = MegaSSOSecurityContext.getUnitNo();
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "N");
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "shtType", "2");
	// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	// Calendar beginDate = Calendar.getInstance();
	// beginDate.setTime(new Date());
	// beginDate.set(Calendar.DAY_OF_MONTH, 1);
	//
	// Calendar endDate = (Calendar) beginDate.clone();
	// int month = endDate.get(Calendar.MONTH) + 1;
	// endDate.set(Calendar.MONTH, month);
	//
	// // 取得檢查日當月資料
	// pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
	// "checkDate", sdf.format(beginDate.getTime()));
	// pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "checkDate",
	// sdf.format(endDate.getTime()));
	//
	// pageSetting.addOrderBy("checkDate", true);
	// Page<L192M01A> page = lms1905Service.get1905V01(pageSetting);
	//
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	// /**
	// * 房貸工作底稿 - 取得編製中歷史資料
	// *
	// * @param pageSetting ISearch
	// * @param params PageParameters
	// * @param parent Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocEditingThisMonthBefore(
	// ISearch pageSetting, PageParameters params, Component parent)
	// throws CapException {
	//
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "N");
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "shtType", "2");
	//
	// SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	// Calendar beginDate = Calendar.getInstance();
	// beginDate.setTime(new Date());
	// beginDate.set(Calendar.DAY_OF_MONTH, 1);
	//
	// pageSetting.addSearchModeParameters(SearchMode.LESS_THAN, "checkDate",
	// sdf.format(beginDate.getTime()));
	//
	// pageSetting.addOrderBy("checkDate", true);
	//
	// Page<L192M01A> page = lms1905Service.get1905V01(pageSetting);
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	// public CapGridResult queryViewDocEditingByCheckDate(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	//
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	// String docStatus = Util.nullToSpace(params
	// .getString(EloanConstants.DOC_STATUS));
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, docStatus);
	//
	// String innerAudit = params.getString("innerAudit");
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// innerAudit);
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "shtType", "2");
	//
	// String checkDateStart = params.getString("checkDateStart");
	// String checkDataEnd = params.getString("checkDateEnd");
	//
	// pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
	// "checkDate", checkDateStart);
	// pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
	// "checkDate", checkDataEnd);
	//
	// pageSetting.addOrderBy("checkDate", true);
	// Page<L192M01A> page = lms1905Service.get1905V01(pageSetting);
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	// /**
	// * 已傳送稽核處資料
	// *
	// * @param pageSetting ISearch
	// * @param params PageParameters
	// * @param parent Component
	// * @return CapGridResult
	// * @throws CapException
	// */
	// public CapGridResult queryViewDocTransmission(ISearch pageSetting,
	// PageParameters params, Component parent) throws CapException {
	//
	// // 增加search 條件
	// String branch = MegaSSOSecurityContext.getUnitNo();
	// // String docStatus =
	// // Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
	//
	// pageSetting.addSearchModeParameters(
	// SearchMode.OR,
	// new SearchModeParameter(SearchMode.OR, new SearchModeParameter(
	// SearchMode.EQUALS, EloanConstants.DOC_STATUS,
	// LasDocStatusEnum.稽核室_編製中.getCode()),
	// new SearchModeParameter(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS,
	// LasDocStatusEnum.稽核室_待覆核.getCode())),
	// new SearchModeParameter(SearchMode.EQUALS,
	// EloanConstants.DOC_STATUS, LasDocStatusEnum.稽核室_已核准
	// .getCode()));
	//
	// // 非自行查核，為稽核查核
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "innerAudit",
	// "N");
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
	// branch);
	//
	// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "shtType", "2");
	// pageSetting.addOrderBy("checkDate", true);
	// Page<L192M01A> page = lms1905Service.get1905V01(pageSetting);
	// // formatter
	// Map<String, IFormatter> map = new HashMap<String, IFormatter>();
	// map.put("ownBrId", new BranchNameFormatter(branchService,
	// ShowTypeEnum.ID_Name));
	// map.put("updater", new UserNameFormatter(userInfoService));
	//
	// return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	// }

	/**
	 * 取得房貸工作底稿的 保證人和借款人資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192M01B(ISearch pageSetting,
			PageParameters params) throws CapException {

		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		String custType = params.getString("custType");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custType",
				custType);

		Page<L192M01B> page = lms1905Service.getL192M01B(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得房貸工作底稿 - 申請內容(帳務資料)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192S01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		L192M01A l192m01a = lms1905Service.getL192M01A(params
				.getString(EloanConstants.MAIN_OID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l192m01b.custId", l192m01a.getCustId());

		Page<L192S01A> page = lms1905Service.getL192S01A(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得房貸工作底稿 - 擔保品資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL192S02A(ISearch pageSetting,
			PageParameters params) throws CapException {

		logger.debug("mainId : " + params.getString(EloanConstants.MAIN_ID));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID,
				params.getString(EloanConstants.MAIN_ID));

		Page<L192S02A> page = lms1905Service.getL192S02A(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

}
