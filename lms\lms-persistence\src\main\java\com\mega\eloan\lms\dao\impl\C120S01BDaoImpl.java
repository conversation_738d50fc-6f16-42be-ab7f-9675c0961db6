/* 
 * C120S01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01B;

/** 個金服務單位檔 **/
@Repository
public class C120S01BDaoImpl extends LMSJpaDao<C120S01B, String>
	implements C120S01BDao {

	@Override
	public C120S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01B> list = createQuery(search).getResultList();
		return list;
	}
	@Override
	public List<C120S01B> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C120S01B> list = createQuery(C120S01B.class,search).getResultList();
		return list;
	}
	@Override
	public C120S01B findByUniqueKey(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01B> findByIndex01(String mainId, String custId, String dupNo){
		ISearch search = createSearchTemplete();
		List<C120S01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01B.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}

	@Override
	public int deleteByMainId(String mainId) {
		Query query = entityManager.createNamedQuery("c120s01b.deleteByMainId");
		query.setParameter(1, mainId);
		return query.executeUpdate();
	}
}