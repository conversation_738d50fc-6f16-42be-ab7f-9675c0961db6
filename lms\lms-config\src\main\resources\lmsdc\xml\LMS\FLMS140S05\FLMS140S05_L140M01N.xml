﻿<?xml version="1.0"?>
<MsgRq>
    <Header>
        <Title>額度利率結構利率化明細檔</Title>
        <Form>FLMS140S05</Form>
        <OutFile>FLMS140S05_L140M01N.txt</OutFile>
    </Header>
    <ItemName>
        <oid          name="unid"       null="N" type=""  default="" num="" desc="*oid"/>
        <mainId       name="WEBELOANMAINID"  null="N" type=""  default="" num="" desc="文件編號"/>
        <rateSeq      name="SubjectNo"       null="N" type=""  default="" num="" desc="序號"/>
        <rateType     name="APLCurr"    null="N" type="rateType"  default="" num="" desc="類別"/>
        <secNo        name="SecNo"      null="" type=""  default="" num="" desc="段數"/>
        <secNoOp      name="SecNo_1"    null="" type=""  default="" num="" desc="適用期間選項"/>
        <secBegMon    name="s_year_1"   null="" type=""  default="" num="" desc="適用期間起月"/>
        <secEndMon    name="f_year_1"   null="" type=""  default="" num="" desc="適用期間迄月"/>
        <secBegDate   name="s_year_2"   null="" type="cdate"  default="" num="" desc="適用期間起日"/>
        <secEndDate   name="f_year_2"   null="" type="cdate"  default="" num="" desc="適用期間迄日"/>
        <!--<rateBaseC    name="Rate1"      null="" type=""  default="" num="" desc="利率基礎代碼"/>0104討論廢除-->
        <rateBase     name="Rate1"      null="" type="multi-val"  default="" num="|" desc="利率基礎"/><!--原指Rate11，0104討論改為Rate1-->
        <rateSetAll   name="rateSetAll" null="" type="yn"  default="" num="" desc="利率基礎-是否以借款同天期顯示文字"/>
        <groupName    name="GroupName"  null="" type=""  default="" num="" desc="利率基礎-貨幣市場利率群組"/>
        <tRateMin     name="trate_1"    null="" type=""  default="" num="" desc="利率基礎-牌告利率-最小值"/>
        <tRateMax     name="trate_2"    null="" type=""  default="" num="" desc="利率基礎-牌告利率-最大值"/>
        <ctRateMin    name="ctrate_1"   null="" type=""  default="" num="" desc="利率基礎-牌告利率-最小值(修改值)"/>
        <ctRateMax    name="ctrate_2"   null="" type=""  default="" num="" desc="利率基礎-牌告利率-最大值(修改值)"/>
        <prRate       name="PrRate1"    null="" type="multi-val"  default="" num="|" desc="利率基礎-自訂利率參考指標-指標名稱"/>
        <ratePeriod   name="rate01Period_1"  null="" type="multi-val"  default="" num="|"  desc="利率基礎-自訂利率天期"/>
        <disYearOp    name="add_dis_1"       null="" type="disYearOp"  default="" num="" desc="利率基礎-加減年利率選項"/>
        <disYearRate  name="y_rate_1"        null="" type=""  default="" num="" desc="利率基礎-加減年利率(%)"/>
        <reRateMin    name="cur_rate_1"      null="" type="reRate"  default="" num="0" desc="利率基礎-敘做利率最小值(%)"/>
        <reRateMax    name="cur_rate_1"      null="" type="reRate"  default="" num="1" desc="利率基礎-敘做利率最大值(%)"/>
        <reRateSelAll  name="cur_rate_show"  null="" type=""  default="" num="" desc="利率基礎-敘做利率(%) 組合文字時顯示此項目"/>
        <attRate       name="cur_rate_2"     null="" type=""  default="" num="" desc="利率基礎-固定利率(%)"/>
        <otherRateDrc  name="OTH_Rate"       null="" type=""  default="" num="" desc="利率基礎-其他(自行輸入)"/>
        <usdMarket     name="rateOther6"     null="" type=""  default="" num="" desc="自訂利率參考指標U01、U02專用-與借款人敘做之市場利率代碼"/>
        <usdSetAll     name="rateOther7"     null="" type=""  default="" num="" desc="自訂利率參考指標U01、U02專用-是否以借款同天期顯示"/>
        <usdMarketRate   name="rateOther1"   null="" type="multi-val"  default="" num="|"  desc="自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率代碼"/>
<!--<usdMarketRate   name="rateOther1_1" null="" type=""  default="" num="" desc="自訂利率參考指標U01、U02專用-(A)與借款人敘作之市場利率"/> 0118:重複欄位，討論後移除-->
        <usdRateTax      name="rateOther4"   null="" type="usdRateTax"  default="" num="" desc="自訂利率參考指標U01、U02專用-(B)稅賦負擔"/>
        <usdInsideRate   name="rateOther5"   null="" type=""  default="" num="" desc="自訂利率參考指標U01、U02專用-(C)內含"/>
        <usdDesRate      name="rateOther8"   null="" type=""  default="" num="" desc="自訂利率參考指標U01、U02專用-(D)S/LBOR與TAIFX差額逾"/>
        <rateMemo        name="rateMemo"     null="" type=""  default="" num="" desc="利率補充說明"/>
        <rateKind        name="rateType_1"   null="" type=""  default="2" num="" desc="利率方式"/><!--0425建霖mail加上預設值-->
        <rateGetInt      name="getIntWay_1"  null="" type=""  default="1" num="" desc="收息方式"/><!--0425建霖mail加上預設值-->
        <rateChgKind     name="rateChange1_1"        null="" type=""  default="" num="" desc="利率變動方式"/>
        <rateChg1        name="rateChange11_1"       null="" type=""  default="" num="" desc="變動週期"/>
        <rateChgDate     name="rateChange21_1"       null="" type=""  default="" num="" desc="指定下次變動日期"/>
        <uionMemo        name="Limit_Oth"            null="" type=""  default="" num="" desc="聯貸案專用說明"/>
        <rateTax         name="TaxGetWay_1"          null="" type=""  default="" num="" desc="稅負洽收"/>
        <rateTaxCode     name="TaxBurdenCode_1"      null="" type=""  default="" num="" desc="稅負洽收-扣稅負擔"/>
        <rateLimitType   name="Limit_1"              null="" type=""  default="" num="" desc="限制條件/說明"/>
        <rateLimitRate   name=""                     null="" type="multi-val"  default="" num="|"  desc="限制條件利率"/>
        <rateLimit       name="lowStrKind"           null="" type=""  default="" num="" desc="下限利率"/>
        <rateLimitCode   name="lowRate1"             null="" type=""  default="" num="" desc="下限利率-利率代碼"/><!--原指Rate11，0104討論改為Rate1-->
        <reteLimitSetAll      name="lowRateAll"              null="" type=""  default="" num="" desc="下限利率-是否以借款同天期顯示文字"/>
        <reteLimitMarket      name="lowRateGroupName"        null="" type=""  default="" num="" desc="下限利率-貨幣市場利率群組"/>
        <rateLimitCountType   name="lowRate_add_dis_1"       null="" type="disYearOp"  default="" num="" desc="下限利率-加減年利率選項"/>
        <rateLimitCountRate   name="lowRate_y_rate_1"        null="" type=""  default="" num="" desc="下限利率-加減年利率(%)"/>
        <rateLimitCountPr     name="lowRate_y_rate_2"        null="" type=""  default="" num="" desc="下限利率-自訂利率"/>
        <rateLimitTax         name="lowRate_TaxGetWay_1"     null="" type=""  default="2" num="" desc="下限利率-稅負負擔選項"/><!--0425建霖mail加上預設值-->
        <rateLimitTaxRate     name="lowRate_TaxBurdenCode_1" null="" type=""  default="" num="" desc="下限利率-稅負負擔年率(%)"/>
        <rateDscr    name="RateTxt"     null="" type="memo"  default="" num="" desc="組成說明字串"/>
        <upRateDscr  name="RateTxt_DB2" null="" type="memo"  default="" num="" desc="上傳用文字"/>
        <creator     name="" null=""  type="today"    default="" num="" desc="建立人員號碼"/>
        <createTime  name="" null=""  type="sysdate"  default="" num="" desc="建立日期"/>
        <updater     name="" null=""  type="today"    default="" num="" desc="異動人員號碼"/>
        <updateTime  name="" null=""  type="sysdate"  default="" num="" desc="異動日期"/>
    </ItemName>  
	<Criteria> 
		<SelectKeys>RateTxt</SelectKeys><!--0425建霖mail加上條件-->
		<SelectValues>notSpace</SelectValues>
	</Criteria>
	
</MsgRq>
