
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF459;
import com.mega.eloan.lms.mfaloan.service.MisELF459Service;

/**
 * <pre>
 * 消金額度新做時徵信情形
 * </pre>
 * 
 * @since 2019/4/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4/17,EL08034,new
 *          </ul>
 */
@Service
public class MisELF459ServiceImpl extends AbstractMFAloanJdbc implements
MisELF459Service {

	@Override
	public ELF459 findByCntrNo(String cntrNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF459.query_br_cntrNo", new String[]{cntrNo});
		List<ELF459> list = toELF459(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}		
	}
	
	private List<ELF459> toELF459(List<Map<String, Object>> rowData){
		List<ELF459> list = new ArrayList<ELF459>();
		for (Map<String, Object> row : rowData) {
			ELF459 model = new ELF459();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
