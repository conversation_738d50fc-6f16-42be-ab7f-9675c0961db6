package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;


import com.mega.eloan.lms.model.L120S01A;

/** 借款人主檔 **/
public interface L120S01ADao extends IGenericDao<L120S01A> {

	L120S01A findByOid(String oid);

	List<L120S01A> findByOids(String[] oids);

	List<L120S01A> findByMainId(String mainId);

	List<L120S01A> findByMainIdForOrder(String mainId);

	L120S01A findByUniqueKey(String mainId, String custId, String dupNo);

	int delModel(String mainId);
	
	List<L120S01A> findByCustIdDupId(String custId,String DupNo);
}