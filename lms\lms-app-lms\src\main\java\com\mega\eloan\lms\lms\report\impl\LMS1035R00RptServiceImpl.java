package com.mega.eloan.lms.lms.report.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;
import com.mega.eloan.lms.lms.report.LMS1035R00RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.L120S01M;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

@Service("lms1035r00rptservice")
public class LMS1035R00RptServiceImpl extends AbstractIISIReportService
		implements LMS1035R00RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1035R00RptServiceImpl.class);
	private Map<String, CapAjaxFormResult> container = new LinkedHashMap<String, CapAjaxFormResult>();

	private Map<String, String> keyMap = new LinkedHashMap<String, String>();

	@Resource
	LMSService lmsService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	BranchService branchService;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {
		initCodeType();

		Properties prop_lms1035m01Page = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);

		String oid = params.getString("oid");
		C120M01A c120m01a = clsService.findC120M01A_oid(oid);
		if (c120m01a != null) {

			try {
				C120S01A c120s01a = clsService.findC120S01A(c120m01a);
				C120S01B c120s01b = clsService.findC120S01B(c120m01a);
				C120S01C c120s01c = clsService.findC120S01C(c120m01a);
				C120S01D c120s01d = clsService.findC120S01D(c120m01a);
				C120S01E c120s01e = clsService.findC120S01E(c120m01a);
				L120S01M l120s01m = clsService.findL120S01M(c120s01e);

				// 若用 JSONObject.fromObject(...)，遇到有關連的 entity 時，會出錯
				reportData.setAll(DataParse.toJSON(c120m01a));

				Map<String, Object> m = new HashMap<String, Object>();

				IBranch branch = branchService.getBranch(c120m01a.getOwnBrId());
				m.put("BRANCHNAME", OverSeaUtil.getBrNameByLocale(branch, LMSUtil.getLocale()));
				m.put("prtDate", CapDate.getCurrentDate("yyyy-MM-dd"));
				
				//
				String randomCode = "";
			    C121M01A c121m01a = clsService.findC121M01A(c120m01a);
			    if(c121m01a!=null){
			     randomCode = Util.trim(c121m01a.getRandomCode());
			    }
			    m.put("randomCode", randomCode);
				
				if (l120s01m != null) {					
					m.put("l120s01m_queryDate", CapDate.formatDate(l120s01m.getQueryDate(), "yyyy-MM-dd"));					
				}
				//
				if (c120s01a != null) {
					reportData.setAll(DataParse.toJSON(c120s01a));
					m.put("edu",
							Util.trim(getValue("edu",
									Util.trim(c120s01a.getEdu()))));
					m.put("marry",
							Util.trim(getValue("marry",
									Util.trim(c120s01a.getMarry()))));
				}

				if (c120s01b != null) {
					reportData.setAll(DataParse.toJSON(c120s01b));
					String value = null;
					String code2 = getJobType2CodeValue(CapString.trimNull(c120s01b
							.getJobType1()));
					
					Map<String, String> map = codeTypeService
							.findByCodeType(code2);

					if (map != null)
						value = map.get(Util.trim(c120s01b.getJobType2()));

					Map<String, String> cls1131m01_othTypemap = codeTypeService
							.findByCodeType("cls1131m01_othType");
					int count = 0;
					String othTypes = "";
					if (cls1131m01_othTypemap != null) {						
						for (String othtype : Util.trim(c120s01c.getOIncome())
								.split(UtilConstants.Mark.SPILT_MARK)) {
							if (count == 0) {
								othTypes = cls1131m01_othTypemap.get(othtype);
								count++;
							} else {
								othTypes = othTypes + "、"
										+ cls1131m01_othTypemap.get(othtype);
							}
						}
					}

					m.put("jobType2", Util.trim(value));
					m.put("oIncome", CapString.trimNull(othTypes)); // 其他收入
					m.put("jobType1",
							Util.trim(getValue("jobType1",
									Util.trim(c120s01b.getJobType1()))));
					m.put("jobTitle",
							Util.trim(getValue("jobTitle",
									Util.trim(c120s01b.getJobTitle()))));
					m.put("inDoc",
							Util.trim(getValue("inDoc",
									Util.trim(c120s01b.getInDoc()))));// yIncomeCert
					// m.put("inDoc",
					// Util.trim(getValue("inDoc",
					// Util.trim(c120s01b.getInDoc()))));//yIncomeCert

					if(true){
						String seniority_desc = MessageFormat.format(prop_lms1035m01Page.getProperty("report.snrDesc_year"), LMSUtil.pretty_numStr(c120s01b.getSeniority())); //default{若空白}
						if(c120s01b.getSeniority()!=null){
							Integer[] seniorityYM_arr = ClsUtility.seniorityYM_decode(c120s01b.getSeniority());
							if(c120s01b.getSnrM()==null){
								seniority_desc = MessageFormat.format(prop_lms1035m01Page.getProperty("report.snrDesc_year"), String.valueOf(seniorityYM_arr[0]));
							}else{
								seniority_desc = MessageFormat.format(prop_lms1035m01Page.getProperty("report.snrDesc_year_month"), String.valueOf(seniorityYM_arr[0]), String.valueOf(seniorityYM_arr[1]));
							}
						}
						m.put(ClsUtility.SNRDESC, seniority_desc);	//泰國報表, 有分「繁中」 en
					}
				}

				if (c120s01c != null) {
					reportData.setAll(DataParse.toJSON(c120s01c));
					m.put("yIncomeCert",
							Util.trim(getValue("inDoc",
									Util.trim(c120s01c.getYIncomeCert()))));
					if(true){
						String credit = Util.trim(c120s01c.getCredit());
						String creditA = "N";
						String creditB = "N";
						if(Util.isNotEmpty(credit)){
							
							if(Util.equals("A", credit)){
								creditA = "Y";
							}else if(Util.equals("B", credit)){
								creditB = "Y";
							}else if(Util.equals("AB", credit)||Util.equals("A|B", credit)){
								creditA = "Y";
								creditB = "Y";
							}
						}
						m.put("creditA", creditA);
						m.put("creditB", creditB);
					}
				}

				if (c120s01d != null) {
					// mName'mName
					m.put("mCustId", Util.trim(c120s01d.getMCustId()));
					m.put("mName", Util.trim(c120s01d.getMName()));
				}
				if (c120s01e != null) {
					reportData.setAll(DataParse.toJSON(c120s01e));
					m.put("ncbMaximumDpd",
							Util.trim(getValue("ncbMaximumDpd",
									Util.trim(c120s01e.getNcbMaximumDpd()))));
				}

				reportData.setAll(m);

			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}

		}
		return reportData;
	}
	
	@Override
	public Engine getSubReportData(PageParameters params, Engine engine) {
		String oid = params.getString("oid");
		C120M01A choose_c120m01a = clsService.findC120M01A_oid(oid);
		if (choose_c120m01a != null) {
			C121M01A meta = clsService.findC121M01A(choose_c120m01a);
			try {
				Engine srp = engine.getSubReport(0);
				if (true) {
					
					List<String[]> detailList1 = new ArrayList<String[]>();
					//===
					BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
					String localCurr = branchRate.getMCurr();
					BigDecimal item_p3 = null;
					
					if(true){
						
						
						List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
						Map<String, String> custPosMap = clsService.get_codeTypeWithOrder("lms1015_custPos");
						for(C120M01A c120m01a: list ){
							if(Util.equals(UtilConstants.lngeFlag.共同借款人, c120m01a.getCustPos())){
								/*
								 在 assign 從債務人身分 時, 可選擇 C-共借人
								 但只採 「借款人與連保人年收入合計」 
								*/
								continue;
							}
							C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
							String col_01 = _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos()));
							String col_02 = Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo())
										+" "+Util.trim(c120m01a.getCustName());
							String val_p4 = Util.trim(LMSUtil.pretty_numStr(c121m01d.getItem_p4()));
							String col_03 = NumConverter.addComma(val_p4)+(Util.isEmpty(val_p4)?"":"%");
							
							String col_04 = Util.trim(c121m01d.getRaw_payCurr());
							String col_05 = NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_payAmt()));
							
							String col_06 = Util.trim(c121m01d.getRaw_otherCurr());
							String col_07 = NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_otherAmt()));
							
							String col_08 = localCurr;
							String col_09 = NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_p3_idv()));
							//=========
							detailList1.add(new String[] { col_01, col_02, col_03,
									col_04, col_05, col_06, col_07, col_08, col_09 });
							
							//挑任一項的 p3
							item_p3 = c121m01d.getItem_p3();							
						}
					}
					String[] columns1 = { "CommonBean1.field01",
							"CommonBean1.field02", "CommonBean1.field03",
							"CommonBean1.field04", "CommonBean1.field05",
							"CommonBean1.field06", "CommonBean1.field07",
							"CommonBean1.field08", "CommonBean1.field09" };
					int col_cnt = columns1.length;

					// 建立所有欄位的資料，不足筆數放空白
					String[][] data1 = new String[detailList1.size()][col_cnt];
					for (int i = 0; i < detailList1.size(); i++) {
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = "";
						}
					}
					for (int i = 0; i < detailList1.size(); i++) {
						String[] cb1List = detailList1.get(i);
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = cb1List[j];
						}
					}
					srp.setData(columns1, data1);
					srp.setPrompt("sumCurr", localCurr);
					srp.setPrompt("sumAmt", NumConverter.addComma(LMSUtil.pretty_numStr(item_p3)));
				}

			} catch (ReportException e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		return engine;
	}

	private String _custPosDesc(Map<String, String> custPosMap, String custPos){
		
		String custPosDesc = ""; 
		if(Util.isNotEmpty(custPos)){
			custPosDesc = LMSUtil.getDesc(custPosMap, custPos);
			if(Util.notEquals(custPos, custPosDesc)){
				custPosDesc = (custPos+"."+custPosDesc);	
			}	
		}
		return custPosDesc;
	}
	
	@Override
	public String getReportDefinition() {
		return "report/lms/LMS1035R00";
	}

	private void initCodeType() {
		if (keyMap == null)
			keyMap = new LinkedHashMap<String, String>();
		keyMap.clear();
		// set key
		keyMap.put("marry", "marry"); // 婚姻狀況
		keyMap.put("edu", "lms1205s01_edu"); // 學歷

		keyMap.put("jobType1", "lms1205s01_jobType1"); // 職業別
		// keyMap.put("jobType2", "jobType"); // 職業別 jobType
		keyMap.put("jobTitle", "lms1205s01_jobTitle"); // 職稱
		keyMap.put("inDoc", "lms1205s01_inDoc"); // 個人所得證明文件
		keyMap.put("oIncome", "cls1131m01_othType"); // 其他收入
		keyMap.put("yIncomeCert", "lms1205s01_inDoc"); // 家庭所得證明文件

		keyMap.put("ncbMaximumDpd", "lms1035s02_ncbMaximumDpd");
		
		keyMap.put("curr", "Common_Currcy");
		if (container == null)
			container = new LinkedHashMap<String, CapAjaxFormResult>();
		container.clear();

		Collection<String> collection = keyMap.values();
		if (collection != null) {
			String[] keys = collection.toArray(new String[] {});
			container = codeTypeService.findByCodeType(keys);
		}
	}

	private String getJobType2CodeValue(String code) {
		// code = jobType1;
		if (("01").equals(code)){		
			code = "lms1205s01_jobType2a";
		} else if (("02").equals(code)) {
			code = "lms1205s01_jobType2b";
		} else if (("03").equals(code)) {
			code = "lms1205s01_jobType2c";
		} else if (("04").equals(code)) {
			code = "lms1205s01_jobType2d";
		} else if (("05").equals(code)) {
			code = "lms1205s01_jobType2e";
		} else if (("06").equals(code)) {
			code = "lms1205s01_jobType2f";
		} else if (("07").equals(code)) {
			code = "lms1205s01_jobType2g";
		} else if (("08").equals(code)) {
			code = "lms1205s01_jobType2h";
		} else if (("09").equals(code)) {
			code = "lms1205s01_jobType2i";
		} else if (("10").equals(code)) {
			code = "lms1205s01_jobType2j";
		} else if (("11").equals(code)) {
			code = "lms1205s01_jobType2k";
		} else if (("12").equals(code)) {
			code = "lms1205s01_jobType2l";
		} else if (("13").equals(code)) {
			code = "lms1205s01_jobType2m";
		} else if (("14").equals(code)) {
			code = "lms1205s01_jobType2n";
		} else {
			code = "";
		}
		return code;
	}

	private String getValue(String key, String value) {
		Object result = null;
		if (key != null && keyMap != null) {
			String codeTypeKey = keyMap.get(key);
			if (codeTypeKey != null) {
				CapAjaxFormResult cafr = container.get(codeTypeKey);
				if (cafr != null)
					result = cafr.get(value);
			}
		}
		return Util.trim(result != null ? result : value);
	}

	@Override
	public Vector<String> getReportDetailColumns(PageParameters params,
			Engine engine) {
		// 應該搭配 reportData.addDetail
		Vector<String> result = new Vector<String>();
		// ReportBean.column01...........
		for (String s : Util.setColumnMap(30).keySet()) {
			result.add(s);
		}
		return result;
	}
}
