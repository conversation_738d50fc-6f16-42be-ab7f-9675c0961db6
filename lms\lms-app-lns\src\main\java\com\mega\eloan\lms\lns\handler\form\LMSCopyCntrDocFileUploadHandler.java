package com.mega.eloan.lms.lns.handler.form;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.response.MegaErrorResult;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.Rate;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.handler.FileUploadHandler;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapMath;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 評等對照 上傳
 * </pre>
 * 
 * @since 2016/4/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2016/4/12,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lmscopycntrdocfileuploadhandler")
public class LMSCopyCntrDocFileUploadHandler extends FileUploadHandler {

	@Autowired
	DocFileService fileService;

	@Resource
	LMSService lmsService;
	@Resource
	BranchService branchSrv;
	// @Resource
	// LMS1401Service lms1401Service;
	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMS1401Service lms1401Service;
	@Resource
	LMS1201Service lms1201Service;
	@Resource
	EloandbBASEService eloandbService;
	@Resource
	DwLnquotovService dwLnquotovService;
	@Resource
	MisdbBASEService misdbBASEService;

	@Override
	public IResult afterUploaded(PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMS1401S02Page.class);

		MultipartFile uFile = params.getFile(params.getString("fieldId"));
		String sysId = params.getString("sysId", fileService.getSysId());

		// 案件簽報書主檔MainId
		String mainId = params.getString(EloanConstants.MAIN_ID);

		// 要設定的借款人名稱和統一編號
		String[] mainName = params.getStringArray("main");
		// copyType 1-複製額度明細表 2-轉入聯行額度明細表
		String copyType = Util.trim(params.getString("copyType"));
		String[] oidList = params.getStringArray("list");

		// 設定上傳檔案資訊
		String fileName = uFile.getName();
		String fieldId = Util.trim(params.getString("fieldId"));

		if (params.containsKey("fileSize")) {
			if (uFile.getSize() > params.getLong("fileSize", 1048576)) {
				// EFD0063=ERROR|上送的檔案已超過$\{fileSize\}M的限制大小，無法執行上傳動作。|
				Map<String, String> msg = new HashMap<String, String>();
				msg.put("fileSize",
						CapMath.divide(params.getString("fileSize"), "1048576")); // 1M*1024*1024
				MegaErrorResult result = new MegaErrorResult();
				result.putError(params, new CapMessageException(RespMsgHelper.getMessage("EFD0063", msg), getClass()));
				return result;
			}
		}

		// 開始匯入EXCEL****************************************************************************

		// 一律以國內MIS TABLE為主
		// 一律以國內MIS TABLE為主
		BranchRate branchRate = lmsService.getBranchRate("005");
		HashMap<String, Rate> rateMap = branchRate.getMisRateMap();

		Workbook workbook = null;
		String errMsg = "";
		InputStream is = null;
		String fileKey = "";
		boolean findError = false;

		int[] dimension = { -1, -1 };
		try {

			is = uFile.getInputStream();
			workbook = Workbook.getWorkbook(is);
			Sheet sheet = workbook.getSheet(0);
			int totalCol = sheet.getColumns();
			if (totalCol == 0) {
				// L120S09a.message14=匯入之黑名單EXCEL格式錯誤。
				throw new CapMessageException("匯入之EXCEL格式錯誤。", getClass());
			}

			List<Map<String, Object>> xlsList = new ArrayList<Map<String, Object>>();

			HashMap<String, String> importCustId = new HashMap<String, String>();
			int maxItemSeq = 0;
			for (int row = 1; row < sheet.getRows(); row++) {
				int column = 0;

				String custId = "";
				String dupNo = "";
				String custKey = "";
				String cntrNo = "";
				String typCd = "";
				String curr = "";
				String factAmt = "";
				String itemDscr4 = ""; // 其他敘作條件
				String itemDscr3 = ""; // 擔保品

				if (++column <= totalCol) {
					custId = StringUtils.upperCase(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (Util.equals(custId, "")) {
					continue;
				}

				if (++column <= totalCol) {
					dupNo = Util.trim(getContents(sheet
							.getCell(column - 1, row)));
					if (Util.equals(dupNo, "")) {
						dupNo = "0";
					}
				}

				custKey = custId + "-" + dupNo;

				if (++column <= totalCol) {
					cntrNo = StringUtils.upperCase(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					typCd = StringUtils.upperCase(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					curr = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));
				}

				if (++column <= totalCol) {
					factAmt = Util.toSemiCharString(Util.trim(getContents(sheet
							.getCell(column - 1, row))));

				}

				if (++column <= totalCol) {
					itemDscr4 = Util.trim(getContents(sheet.getCell(column - 1,
							row)));
				}

				if (++column <= totalCol) {
					itemDscr3 = Util.trim(getContents(sheet.getCell(column - 1,
							row)));
				}

				// 檢核EXCEL內容**********************************************************************************
				StringBuffer custErrMsg = new StringBuffer("");

				if (Util.notEquals(custId, "")) {
					if (StringUtils.length(custId) > 10) {
						custErrMsg.append("借款人統一編號長度錯誤");
						custErrMsg.append(";");

					}
				} else {
					custErrMsg.append("借款人統一編號欄位空白");
					custErrMsg.append(";");
				}

				// 重覆序號
				if (Util.notEquals(dupNo, "")) {
					if (StringUtils.length(dupNo) != 1) {
						custErrMsg.append("借款人重覆序號長度錯誤");
						custErrMsg.append(";");
					}
				}

				// 檢查要出現在借款人基本資料

				L120S01A l120s01a = lms1201Service.findL120s01aByUniqueKey(
						mainId, custId, dupNo);
				if (l120s01a == null) {
					custErrMsg.append("借款人" + custId + "尚未於簽報書借款人基本資料建檔");
					custErrMsg.append(";");
				}

				// 額度序號
				if (Util.notEquals(cntrNo, "")) {
					if (StringUtils.length(cntrNo) != 12) {
						custErrMsg.append("額度序號" + cntrNo + "長度錯誤");
						custErrMsg.append(";");

					}

					// 檢查要有這個額度序號
					List<L140M01A> l140m01as = lms1401Service
							.findL140m01aBycntrNo(cntrNo, custId, dupNo);
					int countE = eloandbService
							.findL140M01EByCustIdAndDupNoAndCntrno(custId,
									dupNo, cntrNo);
					if (l140m01as.isEmpty() && countE == 0) {
						int count = 0;
						// 海外查 DW_ASLNQUOT
						if (TypCdEnum.海外.getCode().equals(
								cntrNo.substring(3, 4))) {
							count = dwLnquotovService
									.findByCntrNoAndCustIdAndDupNo(cntrNo,
											custId, dupNo);

						} else {
							custId = Util.addSpaceWithValue(custId, 10);
							// X為 遠匯
							if ("X".equals(cntrNo.substring(7, 8))) {
								count = misdbBASEService
										.findLNF197BycntrNoAndCustId(cntrNo,
												custId, dupNo);
							} else {
								count = misdbBASEService
										.findMISLN20BycntrNoAndCustId(cntrNo,
												custId, dupNo);

							}
						}

						if (count == 0) {
							custErrMsg.append("額度序號" + cntrNo
									+ "不存在於e-Loan與帳務系統系統");
							custErrMsg.append(";");
						}
					}

				} else {
					// 未填列額度序號時，由系統自動給號，要由經辦指定區部別
					if (Util.equals(typCd, "")) {
						custErrMsg.append("額度序號區部別不得空白");
						custErrMsg.append(";");
					} else {
						if (Util.notEquals(typCd, "1")
								&& Util.notEquals(typCd, "4")
								&& Util.notEquals(typCd, "5")) {
							custErrMsg.append("額度序號區部別值域錯誤");
							custErrMsg.append(";");
						}
					}
				}

				// 幣別
				if (Util.notEquals(curr, "")) {
					if (StringUtils.length(curr) != 3) {
						custErrMsg.append("現請額度幣別長度錯誤");
						custErrMsg.append(";");
					}

					if (Util.notEquals(curr, "TWD")
							&& !rateMap.containsKey(curr)) {
						custErrMsg.append("匯率檔無" + curr + "幣別資料");
						custErrMsg.append(";");
					}

				} else {
					// custErrMsg.append("現請額度幣別欄位空白");
					// custErrMsg.append(";");
				}

				// 額度金額
				// if (Util.equals(factAmt, "")) {
				// custErrMsg.append("現請額度金額欄位空白");
				// custErrMsg.append(";");
				// }

				if (Util.notEquals(factAmt, "")) {
					if (!Util.isNumeric(getBigDecimalString(factAmt))) {
						custErrMsg.append("現請額度金額必須為數字");
						custErrMsg.append(";");
					} else {
						// 要去掉逗號
						factAmt = Util.trim(NumConverter.delComma(factAmt));
					}
				}

				if (Util.notEquals(custErrMsg.toString(), "")) {
					findError = true;
					importCustId.put(custKey, custErrMsg.toString());
				}

				// 儲存到List
				// Map**********************************************************************************

				if (Util.equals(custErrMsg.toString(), "")) {

					Map<String, Object> excelMap = new HashMap<String, Object>();
					excelMap.put("CUSTID", custId);
					excelMap.put("DUPNO", dupNo);
					excelMap.put("CNTRNO", cntrNo);
					excelMap.put("TYPCD", typCd);
					excelMap.put("CURR", curr);
					excelMap.put("FACTAMT", factAmt);
					excelMap.put("ITEMDSCR4", itemDscr4);
					excelMap.put("ITEMDSCR3", itemDscr3);

					xlsList.add(excelMap);

				}
			}

			// 引進名單結束************************************************************************************************************************************

			if (findError) {
				// L140M01a.message205=資料匯入失敗，錯誤如下:<br>{0}
				StringBuffer allError = new StringBuffer("");

				for (String errCustKey : importCustId.keySet()) {
					String errCustId = errCustKey.split("-")[0];
					String errDupNo = errCustKey.split("-")[1];
					String errCustMsg = importCustId.get(errCustKey);
					if (Util.notEquals(errCustMsg, "")) {
						allError.append(errCustId).append(errDupNo).append("：")
								.append(errCustMsg).append("<br>");
					}

				}

				workbook.close();

				// throw new CapMessageException(MessageFormat.format(
				// pop.getProperty("L140M01a.message205"),
				// allError.toString()), getClass());

				throw new CapMessageException(MessageFormat.format(
						"資料匯入失敗，錯誤如下:<br>{0}", allError.toString()), getClass());

			} else {
				// 沒問題才要產生資料
				if ("1".equals(copyType)) {
					// 額度明細表要複製的oids

					int count = oidList[0].split(",").length;
					int i = 0;
					for (Map<String, Object> excelMap : xlsList) {
						i = i + 1;

						if (i > count) {
							i = 1;
						}
						// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
						String caseType = params.getString("CaseType");
						String[] copyOid = new String[1];
						copyOid[0] = oidList[0].split(",")[i - 1];
						mainName = new String[1];
						mainName[0] = Util.trim(excelMap.get("CUSTID"))
								+ Util.trim(excelMap.get("DUPNO"));

						CapAjaxFormResult result = lms1401Service.copyCntrdoc(
								mainId, caseType, mainName, copyOid, excelMap);

						// J-110-0195_05097_B1001 Web e-Loan
						// 企金授信額度明細表新增以整批匯入方式填列
						// 呼叫檢查額度明細表
						String CNTRMAINID = Util.trim(result.get("CNTRMAINID"));
						if (Util.notEquals(CNTRMAINID, "")) {

							Properties prop = this
									.setProp(MessageBundleScriptCreator
											.getComponentResource(LMS1401S02Panel.class));

							L120M01A l120m01a = lms1201Service
									.findL120m01aByMainId(mainId);
							L140M01A newLm140m01a = lms1401Service
									.findL140m01aByMainId(CNTRMAINID);

							// 因為從DB取得數字欄位會有小數點，所以透過DataParse.toResult
							// 處理小數點後，再轉回L140M01A

							CapAjaxFormResult result02 = DataParse
									.toResult(newLm140m01a);

							DataParse
									.toBean(result02.getResult(), newLm140m01a);

							if (newLm140m01a != null) {

								L120M01B l120m01b = lms1401Service
										.findL120m01bByUniqueKey(l120m01a
												.getMainId());

								List<L140M01C> l140m01cs = lms1401Service
										.findL140m01cListByMainId(newLm140m01a
												.getMainId());
								// 用來暫存已登錄授信科目
								HashMap<String, String> itemMap = new HashMap<String, String>();
								for (L140M01C l140m01c : l140m01cs) {
									itemMap.put(l140m01c.getLoanTP(), "");
								}
								List<L140M01J> l140m01js = (List<L140M01J>) lms1401Service
										.findModelListByMainId(L140M01J.class,
												newLm140m01a.getMainId());

								// 取得所有借款人的Id
								List<L120S01A> l120s01as = (List<L120S01A>) lms1401Service
										.findModelListByMainId(L120S01A.class,
												l120m01a.getMainId());
								HashSet<String> custIdSet = new HashSet<String>();
								for (L120S01A tl120s01a : l120s01as) {
									custIdSet.add(tl120s01a.getCustId()
											+ tl120s01a.getDupNo());
								}
								L120S01D l120s01d = lms1401Service
										.findL120S01DByKey(
												l120m01a.getMainId(),
												newLm140m01a.getCustId(),
												newLm140m01a.getDupNo());
								List<L140M01N> l140m01ns = (List<L140M01N>) lms1401Service
										.findModelListByMainId(L140M01N.class,
												newLm140m01a.getMainId());

								L140M01M l140m01m = lmsService
										.findModelByMainId(L140M01M.class,
												newLm140m01a.getMainId());
								L140M01Q l140m01q = lmsService
										.findModelByMainId(L140M01Q.class,
												newLm140m01a.getMainId());

								String isCheckData = lms1401Service
										.isCheckData(newLm140m01a, l120m01b,
												prop, itemMap, l140m01js,
												custIdSet, l120s01d, l140m01m,
												l140m01ns, l140m01q, l120m01a);
								if (isCheckData.length() == 0) {
									// 否為單檔基本資料都以儲存完畢
									newLm140m01a
											.setChkYN(UtilConstants.DEFAULT.否);
								} else {
									newLm140m01a.setChkYN(null);
								}
								lms1401Service.save(newLm140m01a);
							}
						}

					}

				}

			}

			workbook.close();

		} catch (IOException e) {
			logger.error(e.getMessage(), e);
			throw new CapMessageException("file IO ERROR", getClass());
		} catch (BiffException be) {
			logger.error(be.getMessage(), be);
			throw new CapMessageException("file IO ERROR", getClass());
		} finally {
			if (is != null) {
				try {
					is.close();
					if (workbook != null) {
						workbook.close();
						workbook = null;
					}
				} catch (IOException e) {
					logger.debug("inputStream close Error", getClass());
				}
			}

		}

		if (Util.isNotEmpty(errMsg)) {
			throw new CapMessageException(errMsg, getClass());
		}

		return new CapAjaxFormResult().set("url", "file?id=" + fileKey)
				.set("fileKey", fileKey).set("imgWidth", dimension[0])
				.set("imgHeight", dimension[1]);

	}

	private String getContents(Cell cell) {
		DateCell dCell = null;
		if (cell.getType() == CellType.DATE) {
			dCell = (DateCell) cell;
			// System.out.println("Value of Date Cell is: " + dCell.getDate());
			// ==> Value of Date Cell is: Thu Apr 22 02:00:00 CEST 2088
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			// System.out.println(sdf.format(dCell.getDate()));
			// ==> 2088-04-22
			return sdf.format(dCell.getDate());
		}
		// possibly manage other types of cell in here if needed for your goals
		// read more:
		// http://www.quicklyjava.com/reading-excel-file-in-java-datatypes/#ixzz2fYIkHdZP
		return cell.getContents();
	}

	/**
	 * 將字串轉為BigDecimal格式
	 * 
	 * @param in
	 *            the input
	 * @return BigDecimal
	 */
	public String getBigDecimalString(String in) {

		char[] ca = in.toCharArray();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < ca.length; i++) {
			switch (ca[i]) {
			case '-':
			case '+':
			case '0':
			case '1':
			case '2':
			case '3':
			case '4':
			case '5':
			case '6':
			case '7':
			case '8':
			case '9':
			case '.':
				sb.append(ca[i]);
			}
		}
		return sb.toString();
	}

	/**
	 * 設定Properties檔驗證時要show的名稱
	 * 
	 * @param prop
	 *            語系檔
	 * @return 處理完的prop
	 */
	private Properties setProp(Properties prop) {
		// title.14=本　　票
		prop.put("L140M01a.checkNote", prop.getProperty("title.14"));
		// L140M01a.guarantor = 連保人
		prop.put("L140M01a.guarantor",
				prop.getProperty("L140M01a.conPersonNew"));

		// L140M01a.conPersonNew=本案未送保原因
		prop.put("L140M01a.noInsuReasonOther",
				prop.getProperty("L140M01a.noInsuReason"));
		// L140M01a.guarantor = 連保人
		// L782M01A.disp1=備註說明
		prop.put(
				"L140M01a.guarantorMemo",
				StrUtils.concat(prop.getProperty("L140M01a.conPersonNew"),
						prop.getProperty("L782M01A.disp1")));
		prop.put("L140M01a.guarantor",
				prop.getProperty("L140M01a.conPersonNew"));

		// L140M01a.type=性質
		prop.put("L140M01a.proPerty", prop.getProperty("L140M01a.type"));
		// L140M01c.item= 授信科目
		prop.put("L140M01a.lnSubject", prop.getProperty("L140M01c.item"));

		// L140M01c.lmtDays= 清償期限
		prop.put("L140M01a.payDeadline", prop.getProperty("L140M01c.lmtDays"));

		// L140M01a.useDeadline==動用期限
		prop.put("L140M01a.desp1", prop.getProperty("L140M01a.useDeadline"));
		// L140M01a.risknt
		// L140M01a.noInsuReason=本案未送保原因
		prop.put("L140M01a.noInsuReasonOther",
				prop.getProperty("L140M01a.noInsuReason"));
		// L140M01a.gratio=保證成數
		prop.put("L140M01A.gutPercent", prop.getProperty("L140M01a.gratio"));
		return prop;
	}

}
