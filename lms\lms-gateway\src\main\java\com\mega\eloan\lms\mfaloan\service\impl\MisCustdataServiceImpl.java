package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisCustdataService;

@Service
public class MisCustdataServiceImpl extends AbstractMFAloanJdbc implements
		MisCustdataService {
	public Map<String, Object> findCustdataMapByMainId(String id, String dupno) {
		return this.getJdbc().queryForMap("CUSTDATA.selByMainId",
				new Object[] { id, dupno });
	}

	public List<Map<String, Object>> findCustdataForList(String id, String dupno) {
		return this.getJdbc().queryForList("CUSTDATA.selSup1cnm",
				new Object[] { id, dupno });
	}

	public List<?> findCustdataByMainId(String mainId, String dupNo) {
		return this.getJdbc().queryForList("CUSTDATA.selByMainId",
				new Object[] { mainId, dupNo });
	}

	public Map<String, Object> findCustdataSelCname(String custId, String dupNo) {
		return this.getJdbc().queryForMap("CUSTDATA.selCname",
				new Object[] { custId, dupNo });
	}

	/**
	 * 輸入客戶統一編號，系統取得其姓名及重複序號。
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @return List<客戶資料>
	 */
	@Override
	public List<Map<String, Object>> findById(String custId) {
		if (CapString.isEmpty(custId)) {
			return null;
		}
		return getJdbc().queryForList("CUSTDATA.findById",
				new Object[] { custId, custId + "%" });
	}

	/**
	 * 取得客戶基本資料
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, String>
	 */
	@Override
	public Map<String, Object> findByIdDupNo(String custId, String dupNo) {
		return getJdbc().queryForMap("CUSTDATA.findByIdDupNo",
				new Object[] { custId, dupNo });
	}// ;

	@Override
	public List<Map<String, Object>> findCustDataCname(String custId,
			String dupNo) {
		return this.getJdbc().queryForList("CUSTDATA.FindCnamedata",
				new Object[] { custId, dupNo });
	}

	/**
	 * 取得客戶行業別
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, String>
	 */
	public Map<String, Object> findBussTypeByIdDupNo(String custId, String dupNo) {
		return getJdbc().queryForMap("CUSTDATA.findBussTypeByIdDupNo",
				new Object[] { custId, dupNo });
	}

	/**
	 * 輸入客戶統一編號，系統取得其姓名及重複序號及電話。
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return List<客戶資料>
	 */
	public Map<String, Object> findByIdDupNoWithTel(String custId, String dupNo) {
		custId = custId.toUpperCase();
		return getJdbc().queryForMap("CUSTDATA.findByIdWithTel",
				new Object[] { custId, dupNo });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisCustDataService#findCNameByIdDupNo
	 * (java.lang.String, java.lang.String)
	 */
	public Map<String, Object> findCNameByIdDupNo(String custId, String dupNo) {
		return getJdbc().queryForMap("CUSTDATA.findCNameByIdDupNo",
				new Object[] { custId, dupNo });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.lms.mfaloan.service.MisCustdataService#
	 * findBUSCDByCustIdANdDupNo(java.lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findBUSCDByCustIdANdDupNo(String custId,
			String dupNo) {
		return getJdbc().queryForMap("MISCUSTDATA.selByCustIdANdDupNo",
				new Object[] { custId, dupNo });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.mega.eloan.lms.mfaloan.service.MisCustdataService#
	 * findAllByByCustIdAndDupNo(java.lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findAllByByCustIdAndDupNo(String custId,
			String dupNo) {
		return getJdbc().queryForMap("MISCUSTDATA.selAllByCustIdAndDupNo",
				new Object[] { custId, dupNo });
	}

	@Override
	public String findCltypeById(String custId, String dupNo) {
		Map<String, Object> data = getJdbc().queryForMap(
				"MIS.CUSTDATAByIdGetCLTYPE", new Object[] { custId, dupNo });
		if (data != null) {
			return Util.trim(data.get("CLTYPE"));
		}
		return null;
	}

	@Override
	public Map<String, Object> findCMFCUS1ByIdAndDup(String custId, String dupNo) {
		return getJdbc().queryForMap("MIS.CMFCUS1.findByIdAndDup",
				new Object[] { custId, dupNo });
	}

	/**
	 * 取得客戶性質
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, String>
	 */
	@Override
	public Map<String, Object> findCharCdByIdDupNo(String custId, String dupNo) {
		return getJdbc().queryForMap("MISCUSTDATA.selCharCdByIdAndDup",
				new Object[] { custId, dupNo });
	}

	/**
	 * 取得註冊地址 J-105-0233-001 Web
	 * e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
	 * 
	 * @param id
	 * @param dupno
	 * @return
	 */
	@Override
	public List<Map<String, Object>> findCustRegisteredAddressForList(
			String id, String dupno) {
		return this.getJdbc().queryForList("CUSTDATA.selRegisteredAddress",
				new Object[] { id, dupno });
	}

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findCltypeAndBusCdById(String custId,
			String dupNo) {
		return getJdbc().queryForMap("MIS.CUSTDATAByIdGetCtlTypeAndBusCd",
				new Object[] { custId, dupNo });

	}

	/**
	 * I-108-0027_05097_B1001 Web
	 * e-Loan國內授信系統配合0024客戶中文檔取消AML-STATUS禁止交易之限制，若為制裁名單將採關戶或凍結
	 * 
	 * 原本AML判斷拒絕交易，是抓MIS.CMFCUS1 的CM1_AML_STATUS欄位 = '3'，現在要改成讀取 MIS.CMFDNGER ，當
	 * CMDNG_REASON_CODE = '3' 且 CMDNG_TF_FLAG = 'Y' 為拒絕交易
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findCMFDNGERByIdAndDup(String custId,
			String dupNo) {
		return getJdbc().queryForMap("MIS.CMFDNGER.findByIdAndDup",
				new Object[] { custId, dupNo });
	}

	/**
	 * J-109-0394_05097_B1001 e-Loan國內企金授信額度明細表新增DBU戶使用OBU額度等相關檢核
	 * 
	 * OBU ID 檢查-mis.cmfcus25 該ID CM25_OSU_FLAG = '3' 且 CM25_DOMEST_TAXNO 有 DBU
	 * ID ，則本案需要改用DBU ID來簽案
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findCmfcus25ByCustId(String custId, String dupNo) {
		return getJdbc().queryForMap("CMFCUS25.selByCustId",
				new Object[] { custId, dupNo });

	}

	/**
	 * J-109-0394_05097_B1001 e-Loan國內企金授信額度明細表新增DBU戶使用OBU額度等相關檢核
	 * 
	 * DBU ID 檢查-mis.cmfcus25 中要有一筆OBU ID，他的CM25_OSU_FLAG = '3'，且
	 * CM25_DOMEST_TAXNO 為此DBU ID，此時DBU才可以簽OBU額度序號
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findCmfcus25ByDomestIdAndOsuFlag(String custId) {
		return getJdbc().queryForMap("CMFCUS25.selByDomestIdAndOsuFlag3",
				new Object[] { custId });

	}
	
	/**
	 * J-111-0207 eloan國內企金管理系統，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 用負責人查詢是否有配偶資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findPrincipalMate(String custId, String dupNo){
		return this.getJdbc().queryForMap("MISCUSTDATA.findPrincipalMate",
				new Object[] { custId, dupNo });
	}

}
