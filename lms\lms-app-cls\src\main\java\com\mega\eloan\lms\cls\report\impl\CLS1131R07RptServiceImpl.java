package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.cls.report.CLS1131R07RptService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.model.C101S01S;
import com.mega.eloan.lms.model.C120S01S;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;

/**
 * <pre>
 * 一件列印PDF
 * </pre>
 * 
 * @since 2020/05/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/21, EL09763
 *          </ul>
 */
@Service("cls1131r07rptservice")
public class CLS1131R07RptServiceImpl implements FileDownloadService,
		CLS1131R07RptService {

	@Resource
	CLSService clsService;

	@Resource
	CLS1131Service cms1131Service;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1131R07RptServiceImpl.class);

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		OutputStream outputStream = null;
		Map<InputStream, Integer> pdfNameMap = new HashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		Locale locale = LMSUtil.getLocale();
		int subLine = 8;// 此數值對應的(x,y).要查 PdfTools.並不一定是愈小,愈上面
		try {

			String mainId = params.getString(EloanConstants.MAIN_ID);
			String custId = params.getString("custId");
			String dupNo = params.getString("dupNo");
			boolean isC120M01A = Util.equals("Y",
					Util.trim(params.getString("isC120M01A")));

			if (isC120M01A) {
				// 簽報書
				List<C120S01S> s01s_list = new ArrayList<C120S01S>();
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "1"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "2"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "3"));
				s01s_list.addAll(clsService.findC120S01S_byIdDupDataType(
						mainId, custId, dupNo, "4"));
				for (C120S01S c101s01s : s01s_list) {
					// 抓PDF檔byte[]
					if (c101s01s.getReportFile() != null
							&& !"J".equals(c101s01s.getReportFileType())) {
						// 需要橫印
						// 2:客戶是否為利害關係人資料
						// 3:婉卻紀錄資料
						// 4:證券違約交割資料
						if ("2".equals(c101s01s.getDataType())
								|| "3".equals(c101s01s.getDataType())
								|| "4".equals(c101s01s.getDataType())) {
							outputStream = new ByteArrayOutputStream();
							List<InputStream> rotateList = new LinkedList<InputStream>();
							rotateList.add(new ByteArrayInputStream((c101s01s
									.getReportFile())));
							PdfTools.mergeReWritePagePdf(rotateList,
									outputStream, "", true, locale, subLine,
									false);
							list.add(new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()));
						} else {
							list.add(new ByteArrayInputStream((c101s01s
									.getReportFile())));
						}
					}
				}
			} else {
				// 個金徵信
				List<C101S01S> s01s_list = new ArrayList<C101S01S>();
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "1"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "2"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "3"));
				s01s_list.addAll(clsService.findC101S01S_byIdDupDataType(
						mainId, custId, dupNo, "4"));
				for (int i = 0; i < s01s_list.size(); i++) {
					// 抓PDF檔byte[]
					C101S01S c101s01s = s01s_list.get(i);
					if (c101s01s.getReportFile() != null
							&& !"J".equals(c101s01s.getReportFileType())) {
						// 需要橫印
						// 2:客戶是否為利害關係人資料
						// 3:婉卻紀錄資料
						// 4:證券違約交割資料
						if ("2".equals(c101s01s.getDataType())
								|| "3".equals(c101s01s.getDataType())
								|| "4".equals(c101s01s.getDataType())) {
							outputStream = new ByteArrayOutputStream();
							List<InputStream> rotateList = new LinkedList<InputStream>();
							rotateList.add(new ByteArrayInputStream((c101s01s
									.getReportFile())));
							PdfTools.mergeReWritePagePdf(rotateList,
									outputStream, "", true, locale, subLine,
									false);
							list.add(new ByteArrayInputStream(
									((ByteArrayOutputStream) outputStream)
											.toByteArray()));
						} else {
							list.add(new ByteArrayInputStream((c101s01s
									.getReportFile())));
						}
					}
				}
			}

			if (list != null && list.size() > 0) {
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);
			} else {
				throw new CapMessageException("無PDF檔案資料，請改用一鍵列印HTML",
						this.getClass());
			}

		} finally {
			if (pdfNameMap != null) {
				pdfNameMap.clear();
			}
		}

		return outputStream;
	}

}
