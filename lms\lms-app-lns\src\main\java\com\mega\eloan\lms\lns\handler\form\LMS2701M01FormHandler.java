/* 
 * LMS2701M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.form;

import com.iisigroup.cap.component.PageParameters;
import com.inet.html.utils.Logger;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.lns.service.LMS2701Service;
import com.mega.eloan.lms.model.L270M01A;
import org.springframework.context.annotation.Scope;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <pre>
 * 企金信保
 * 信保基金送保查詢
 * </pre>
 *
 * @since 2021/6/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/1,009301,new
 *          </ul>
 */
@Scope("request")
@Controller("lms2701m01formhandler")
public class LMS2701M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS2701Service lms2701Service;

	@Resource
	CodeTypeService codeTypeService;

	@DomainAuth(value = AuthType.Modify)
	public IResult getL270M01A(PageParameters params) throws CapException {
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L270M01A l270m01a = lms2701Service.findModelByOid(L270M01A.class, mainOid);
		if(l270m01a == null){

		} else {
			result = DataParse.toResult(l270m01a);
			Map<String, CapAjaxFormResult> codeTypes = codeTypeService
					.findByCodeType(new String[] { "HaveNo", "YesNo", "lms2701_organ", "lms2701_qualiCode" });
			CodeType counties = codeTypeService.findByCodeTypeAndCodeValue(
					"counties", Util.trim(l270m01a.getCity()));
			CodeType counties2 = codeTypeService.findByCodeTypeAndCodeValue(
					"counties" + Util.trim(l270m01a.getCity()),
					Util.trim(l270m01a.getDist()));

			result.set("organ", this.showPic(Util.trim(l270m01a.getOrgan()), "7", codeTypes, locale));
			result.set("qualiCode", this.showPic(Util.trim(l270m01a.getQualiCode()), "8", codeTypes, locale));
			result.set("isEmail", this.showPic(Util.trim(l270m01a.getIsEmail()), "2", codeTypes, locale));
			result.set("isCreditCheck", this.showPic(Util.trim(l270m01a.getIsCreditCheck()), "1", codeTypes, locale));
			result.set("isnCovProject", this.showPic(Util.trim(l270m01a.getIsnCovProject()), "1", codeTypes, locale));
			result.set("isnCovEffect", this.showPic(Util.trim(l270m01a.getIsnCovEffect()), "1", codeTypes, locale));
			result.set("isTaxBan", this.showPic(Util.trim(l270m01a.getIsTaxBan()), "1", codeTypes, locale));
			result.set("isCreditAbnormal", this.showPic(Util.trim(l270m01a.getIsCreditAbnormal()), "1", codeTypes, locale));
			result.set("isOverSimple70", this.showPic(Util.trim(l270m01a.getIsOverSimple70()), "1", codeTypes, locale));
			result.set("isOperating", this.showPic(Util.trim(l270m01a.getIsOperating()), "1", codeTypes, locale));
			result.set("isRealOwner", this.showPic(Util.trim(l270m01a.getIsRealOwner()), "2", codeTypes, locale));
			result.set("isOwnIdle", this.showPic(Util.trim(l270m01a.getIsOwnIdle()), "1", codeTypes, locale));
			result.set("agreement", this.showPic(Util.trim(l270m01a.getAgreement()), "1", codeTypes, locale));
			result.set("isCheckAddress", this.showPic(Util.trim(l270m01a.getIsCheckAddress()), "1", codeTypes, locale));
			result.set("isUnregisteredFactory", this.showPic(Util.trim(l270m01a.getIsUnregisteredFactory()), "1", codeTypes, locale));
			result.set("isEligible", this.showPic(Util.trim(l270m01a.getIsEligible()), "1", codeTypes, locale));

			// DB 開 DECIMAL 會有千分位
			result.set("trad6", ((l270m01a.getTrad6() != null && l270m01a.getTrad6() != 0) ?
					String.valueOf(l270m01a.getTrad6()) : ""));
			result.set("neighborhood", ((l270m01a.getNeighborhood() != null && l270m01a.getNeighborhood() != 0) ?
					String.valueOf(l270m01a.getNeighborhood()) : ""));
			result.set("grntPaper", ((l270m01a.getGrntPaper() != null && l270m01a.getGrntPaper() != 0) ?
					String.valueOf(l270m01a.getGrntPaper()) : ""));

//			DecimalFormat dfMoney = new DecimalFormat("###,###,###,###,##0");
//			result.set("applyLoan", this.showPic(Util.trim(l270m01a.getOrgan()), "7", codeTypes, locale));

			result.set("addr", Util.trim(l270m01a.getZip())
					+ (counties == null ? "" : counties.getCodeDesc())
					+ (counties2 == null ? "" : counties2.getCodeDesc())
					+ Util.trim(l270m01a.getVillageName())
					+ (Util.isEmpty(Util.trim(l270m01a.getVillageName())) ? "" :
							this.showPic(Util.trim(l270m01a.getVillage()), "5", codeTypes, locale))
					+ Util.trim(l270m01a.getNeighborhood())
					+ (Util.isEmpty(Util.trim(l270m01a.getNeighborhood())) ? "" : "鄰")
						+ Util.trim(l270m01a.getRoadName()) + this.showPic(Util.trim(l270m01a.getRoad()), "6", codeTypes, locale)
						+ Util.trim(l270m01a.getSec()) + (Util.isEmpty(Util.trim(l270m01a.getSec())) ? "" : "段")
						+ Util.trim(l270m01a.getLane()) + (Util.isEmpty(Util.trim(l270m01a.getLane())) ? "" : "巷")
						+ Util.trim(l270m01a.getAlley()) + (Util.isEmpty(Util.trim(l270m01a.getAlley())) ? "" : "弄")
						+ Util.trim(l270m01a.getNo1())
						+ ((Util.isEmpty(Util.trim(l270m01a.getNo1())) && Util.isEmpty(Util.trim(l270m01a.getNo2()))) ?
							"" : (Util.isEmpty(Util.trim(l270m01a.getNo2())) ? "號" : "之"))
						+ Util.trim(l270m01a.getNo2()) + (Util.isEmpty(Util.trim(l270m01a.getNo2())) ? "" : "號")
						+ Util.trim(l270m01a.getFloor1()) + (Util.isEmpty(Util.trim(l270m01a.getFloor1())) ? "" : "樓")
						+ (Util.isEmpty(Util.trim(l270m01a.getFloor2())) ? "" : "之") + Util.trim(l270m01a.getFloor2())
						+ (Util.isEmpty(Util.trim(l270m01a.getRoom())) ? "" : "(") + Util.trim(l270m01a.getRoom()) + (Util.isEmpty(Util.trim(l270m01a.getRoom())) ? "" : "室)"));
		}

		return result;
	}

	/**
	 * kind=1, 寫死 "是 or 否"
	 * kind=2, 寫死 "有 or 無"
	 * kind=3, 1:是. 2:否
	 * kind=4, 1:有. 2:無
	 * kind=5, 1:里. 2:村
	 * kind=6, 1:"". 2:路. 3:街
	 * kind=7, 組織代號 organ
	 * kind=8, 適用之對象 qualiCode
	 **/
	private String showPic(String value, String kind,
						   Map<String, CapAjaxFormResult> codeTypes, Locale locale) {
		Map<String, String> yesNoMap = null;
		StringBuffer str = new StringBuffer();
		try {
			String code = "";
			if(Util.equals(kind, "1") || Util.equals(kind, "3")){
				code = "YesNo";
			} else if(Util.equals(kind, "2") || Util.equals(kind, "4")){
				code = "HaveNo";
			} else if(Util.equals(kind, "7")){
				code = "lms2701_organ";
			} else if(Util.equals(kind, "8")){
				code = "lms2701_qualiCode";
			}

			if(Util.equals(kind, "1") || Util.equals(kind, "2")
					|| Util.equals(kind, "7") || Util.equals(kind, "8")){
				str.append(codeTypes.get(code).get(value));
			} else if(Util.equals(kind, "3") || Util.equals(kind, "4")){
				str.append((Util.equals(value, "1") ? "■" : "□"));
				str.append(codeTypes.get(code).get("1"));
				str.append((Util.equals(value, "2") ? "■" : "□"));
				str.append(codeTypes.get(code).get("2"));
			} else if(Util.equals(kind, "5")){
				Map<String, String> k4Map = new HashMap<String, String>();
				k4Map.put("1", "里");
				k4Map.put("2", "村");
				str.append(k4Map.get(value));
			} else if(Util.equals(kind, "6")){
				Map<String, String> k5Map = new HashMap<String, String>();
				k5Map.put("1", "");
				k5Map.put("2", "路");
				k5Map.put("3", "街");
				str.append(k5Map.get(value));
			}
		} catch (Exception e) {
			Logger.error(StrUtils.getStackTrace(e));
//			throw new CapException(e, getClass());
		} finally {
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
		}
		return str.toString();
	}
}
