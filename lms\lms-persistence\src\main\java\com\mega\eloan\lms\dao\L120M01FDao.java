/* 
 * L120M01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120M01F;

/** 案件簽章欄檔 **/
public interface L120M01FDao extends IGenericDao<L120M01F> {

	L120M01F findByOid(String oid);

	List<L120M01F> findByMainId(String mainId);

	List<L120M01F> findToSaveHq(String mainId, String branchType,
			String branchId, String staffJob);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * @param branchId
	 *            單位代碼
	 * @param staffNo
	 *            人員編號
	 * @param staffJob
	 *            人員職稱
	 * @return
	 */
	L120M01F findByUniqueKey(String mainId, String branchType, String branchId,
			String staffNo, String staffJob);

	List<L120M01F> findByIndex01(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * @param branchId
	 *            單位代碼
	 * @param staffJob
	 *            人員職稱
	 * @return 簽章欄
	 */
	L120M01F findByMainIdAndKey(String mainId, String branchType,
			String branchId, String staffJob);

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * @param branchId
	 *            單位代碼
	 * @param staffJob
	 *            人員職稱
	 * @return 簽章欄list
	 */
	List<L120M01F> findByMainIdAndKey(String mainId, String branchType,
			String branchId, String[] staffJob);
	
	

	/**
	 * 查詢簽章欄
	 * 
	 * @param mainId
	 *            文件編號
	 * @param branchType
	 *            單位類型 1. 分行<br/>
	 *            2. 母行/海外總行<br/>
	 *            3. 營運中心<br/>
	 *            4. 授管處<br/>
	 *            5. 徵信承作分行
	 * @return 簽章欄list
	 */
	List<L120M01F> findByMainIdAndBranchType(String mainId, String branchType);
}