package com.mega.eloan.lms.fms.pages;

import java.util.ArrayList;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * 		覆審考核表作業 - 排名表
 * </pre>
 * 
 * @since 2022
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller@RequestMapping(path = "/fms/lms8100v04")
public class LMS8100V04Page extends AbstractEloanInnerView {

	public LMS8100V04Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		// 加上Button
		ArrayList<EloanPageFragment> btns = new ArrayList<>();
		
		if(Util.notEquals(user.getUnitNo(), UtilConstants.BankNo.授管處)){
			btns.add(LmsButtonEnum.Delete);
			btns.add(LmsButtonEnum.SendToCtrlDept);
		} else {
			btns.add(LmsButtonEnum.Return);
		}
		addToButtonPanel(model, btns);

		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS8100V01Page.class);
	}
	
	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS8100V04Page.js" };
	}
}