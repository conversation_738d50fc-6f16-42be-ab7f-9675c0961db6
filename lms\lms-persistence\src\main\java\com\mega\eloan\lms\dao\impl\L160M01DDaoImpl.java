/* 
 * L160M01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L160M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L160M01D;

/** 案件簽章欄檔 **/
@Repository
public class L160M01DDaoImpl extends LMSJpaDao<L160M01D, String> implements
		L160M01DDao {

	@Override
	public L160M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L160M01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L160M01D> list = createQuery(L160M01D.class,search).getResultList();
		return list;
	}

	@Override
	public L160M01D findByUniqueKey(String mainId, String staffNo,
			String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L160M01D> findByIndex01(String mainId, String staffNo,
			String staffJob) {
		ISearch search = createSearchTemplete();
		List<L160M01D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo",
					staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob",
					staffJob);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L160M01D.class,search).getResultList();
		}
		return list;
	}
}