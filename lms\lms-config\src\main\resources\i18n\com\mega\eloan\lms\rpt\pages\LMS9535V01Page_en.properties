#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u932f\u8aa4\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.error1 =Inquiry Start Year/Month
L1205S07.error2 =Inquiry End Year/Month
L1205S07.error3 =Earlier than the starting date of DW data range (currently no data)
L1205S07.error4 =Earlier than the starting date of DW data range
L1205S07.error5 =Later than the latest date of DW data range (currently no data)
L1205S07.error6 =Later than the latest date of DW data range
L1205S07.error7 =\u203bThe data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L1205S07.error8 =Later than the starting date of DW data range
L1205S07.error9 =Later than the starting date of DW data range (currently no data)
L1205S07.error10 =Earlier than the latest date of DW data range
L1205S07.error11 =The group's/company's latest credit relationship is blank; please import using the "Related Documents" function

l120v01.error3=You have entered an invalid month
l120v01.error8=You have entered an invalid year
l120v01.error9=You have entered an invalid year range
#==================================================
# \u5f80\u4f86\u5f59\u7e3dGrid\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.grida=Borrower's Name
L1205S07.gridb=Borrower
L1205S07.grid3=Credit Line
L1205S07.grid4=Credit Limit Serial Number
L1205S07.grid5=Reference Borrower
L1205S07.grid6=Reference Credit Line
L1205S07.grid7=Reference Credit Limit Serial Number
L1205S07.grid8=Print Mode
L1205S07.grid9=Applicant
L1205S07.grid10=Approval Date
L1205S07.grid11=Document Status
L1205S07.grid12=Credit Report No.
L1205S07.grid13=Handling officer
L1205S07.grid14=Nature
L1205S07.grid15=Printing Required
L1205S07.grid16=Related Party's UBN
L1205S07.grid17=Related Party's Name
L1205S07.grid18=Relationship With Borrower
L1205S07.grid19=Contribution
L1205S07.grid20=Credit Limit
L1205S07.grid21=Outstanding Balance
L1205S07.grid22=Current Deposit
L1205S07.grid23=Applicant
L1205S07.grid24=Approval Date
L1205S07.grid25=Document Status
L1205S07.grid26=Credit Report No.
L1205S07.grid27=Handling officer
L1205S07.grid28=Nature
L1205S07.grid29=Unified Business Number
L1205S07.grid30=Customer Name
L1205S07.grid31=Signature Date
L1205S07.grid32=Case No.
L1205S07.grid33=Credit Limit Serial Number
L1205S07.grid34=Document Status
L1205S07.grid35=Applied Credit Limit
L1205S07.grid36=Credit Line
L1205S07.grid1=Standalone
L1205S07.grid2=Consolidated
L1205S07.grid37=Principal Borrower
L1205S07.grid38=Date Created
L1205S07.grid39=Document Status
L1205S07.grid40=Approval date
L1205S07.grid41=Document Creator
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8b66\u544a\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.alert1=No data selected
L1205S07.alert2=You have not selected any data; please select at least one record...
L1205S07.alert3=Document "Not" canceled
L1205S07.alert4=Document "Not" recovered
L1205S07.alert5=Document "Not" deleted
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u78ba\u8a8d\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L1205S07.confirm1=Are you sure to cancel the list?
L1205S07.confirm2=Are you sure to Resume Printing Related Account List?
L1205S07.confirm3=Dada can not be recovered once deleted; are you sure to delete "All" documents pertaining to related parties?
L1205S07.confirm4=\u95dc\u9589\u524d\u6703\u5c07\u5df2\u5f15\u9032\u7684\u5f80\u4f86\u5f59\u7e3d\u8868\u522a\u9664\uff0c\u662f\u5426\u78ba\u5b9a\u8981\u95dc\u9589\uff1f
#==================================================
# \u5f80\u4f86\u5f59\u7e3dThickBox\u6309\u9215\u8207\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.thickbox1=Confirm
L1205S07.thickbox2=Cancel
L1205S07.thickbox3=Save
L1205S07.thickbox4=Delete
L1205S07.thickbox5=Close
L1205S07.thickbox6=Borrower Selection
L1205S07.thickbox7=Exit
L1205S07.thickbox8=Please input the starting/ending year & month for your inquiry (YYYYMM)
L1205S07.thickbox9=Register the related account to the bank's banking transactions
L1205S07.thickbox10=Re-import
L1205S07.thickbox11=Add Printed Related Account List
L1205S07.thickbox12=Capital adequacy ratio
L1205S07.thickbox13=Calculations
L1205S07.thickbox14=Stakeholders Credit Term Reference
L1205S07.thickbox15=Credit Facility Report Selection
L1205S07.thickbox16=Credit Assessment Report Selection
L1205S07.thickbox17=Summary Information Sheet Selection
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8cc7\u6599\u57fa\u671f\u7d30\u90e8\u540d\u7a31
#==================================================
L1205S07.form1=Average Outstanding Balance
L1205S07.form2=Available Credit Line
L1205S07.form3=Transaction Volume
L1205S07.form4=Entrusted Asset Balance
L1205S07.form5=Fee Income
L1205S07.form6=Number of accounts
L1205S07.form7=Amount OF Credit Card Purchase
L1205S07.form8=Whether co-brand cards exist
L1205S07.form9=(including EDI)
#==================================================
# \u5f80\u4f86\u5f59\u7e3dLegend\u540d\u7a31
#==================================================
L1205S07.subindex1=Overall Evaluation & Reason To Continue Relationship
L1205S07.subindex2=Risk Weight Calculated Detail
L1205S07.subindex3=Related Account's Banking Transactions With the Bank
L1205S07.subindex4=Related Account
L1205S07.subindex5=Register the related account to the bank's banking transactions
L1205S07.subindex6=Credit Term Reference
L1205S07.subindex7=Borrowers and related the bank dealings performance summary table
L1205S07.subindex8=\u95dc\u4fc2\u6236\u5f80\u4f86\u5f59\u7e3d\u67e5\u8a62
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6309\u9215\u540d\u7a31
#==================================================
L1205S07.btn1=Import Overall Credit Assessment
L1205S07.btn2=Print Preview Overall Evaluation & Reason To Continue Relationship
L1205S07.btn3=Import Credit Facility Report
L1205S07.btn4=Write Back Credit Facility Report
L1205S07.btn5=Print This Page
L1205S07.btn6=Add Printed Related Account List
L1205S07.btn7=Cancel Printing Related Account List
L1205S07.btn8=Resume Printing Related Account List
L1205S07.btn9=Import Related Account Banking Summary
L1205S07.btn10=Calculate Group/Related Party Total
L1205S07.btn11=Print
L1205S07.btn12=Delete All Related Account Banking Data
L1205S07.btn13=Inquiry
L1205S07.btn14=Re-import
L1205S07.btn15=Import Reference Customer's Credit Limit Data
L1205S07.btn16=Clear All
L1205S07.btn17=Generate List
L1205S07.btn18=Delete List
L1205S07.btn19=Borrowers and related the bank dealings performance summary table
L1205S07.btn20=\u7522\u751f\u4e3b\u8981\u95dc\u4fc2\u6236\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u6bd4\u8f03\u8868
L1205S07.btn21=\u4e0a\u50b3\u4e3b\u8981\u95dc\u4fc2\u6236\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u6bd4\u8f03\u8868
L1205S07.btn22=Delete data
#J-107-0225_05097_B1001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u7c3d\u5831\u66f8\u65b0\u589e\u96c6\u5718\u95dc\u4fc2\u4f01\u696d\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u689d\u4ef6\u6bd4\u8f03\u8868
L1205S07.btn23=Produce the Group/Affiliated company loan terms comparison table
L1205S07.btn24=\u5f15\u9032\u5f80\u4f86\u5f59\u7e3d\u8aaa\u660e
#J-113-0183  e-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u65b0\u589eRORWA\u8a08\u7b97
RORWACAL=Calculate RORWA
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u7d30\u90e8\u8cc7\u6599\u5176\u4ed6\u540d\u7a31
#==================================================
L1205S07.other1=\u203bPlease input the reason for change of lending term, industry prospect, overall business and financial evaluation, banking transactions, feasibility and rationality for continuing banking relationship...etc.
L1205S07.other2=Unit: thousand dollars
L1205S07.other3=Description: 1. Non-credit Guaranteed Cases: for every $1 billion of exposure, capital adequacy is calculated at 0.7/10000. (includes accounts receivable financing; the risk weight can be based on buyer's credit rating subject to case-by-case approval, and can be used to calculate exposure)
L1205S07.other4=Field Description: A. Credit Limit: multiple 20% to import negotiation limits. Derivatives need to be multiplied with proper risk conversion ratios.
L1205S07.other5=Description: 1. The data covers all bank customers. 2. If certain groups/companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly by inputting a Modification Notice. 3. If certain related companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly. 4. The source of related company data is the same as Item 9 under the Case Report's Description tab; it only covers controlling and cross-investment relationships. 5. Salary account data was maintained in the data warehouse system since 2011/02. 6. The data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L1205S07.other6=Please input information on the borrower, representative, group, and related companies into Related Account's Banking Transactions With the Bank.
L1205S07.other7=The credit limit serial number is used as mean of control, and is not shown in printed reports.
L1205S07.other8=Importing is allowed only for within-authority Case Reports; for other types of Case Reports, please input manually
L1205S07.other9=If the Credit Facility Report specifies the repayment period under Other Terms & Conditions, the system will consolidate Other Terms & Conditions into the Tenor field
L1205S07.other10=Please select the Credit Facility Report from which to generate the List
L1205S07.other11=Please select 1 Credit Facility Report
L1205S07.other12=&nbsp;&nbsp;&nbsp;\u8aaa\u660e\uff1a<br /> &nbsp;&nbsp;&nbsp;1.\u300c\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u5831\u916c\u7387\u300d\u516c\u5f0f\u70ba\u6309\u4f01\u696d\u4e4b\u8ca2\u737b\u5ea6/\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u8a08\u7b97\u3002<br />&nbsp;&nbsp;&nbsp;2.\u55ae\u4e00\u5ba2\u6236\u300c\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u300d\u8acb\u65bc\u8cc7\u6599\u5009\u5132\u7cfb\u7d71\u2192\u55ae\u4e00\u5ba2\u6236\u67e5\u8a62\u2192C350\u4e3b\u8981\u696d\u52d9\u67e5\u8a62\u2192\u55ae\u4e00\u5ba2\u6236\u9280\u884c\u5f80\u4f86\u6982\u6cc1\u5f59\u7e3d\u8868\u67e5\u8a62\u3002<br />&nbsp;&nbsp;&nbsp;3.\u300c\u98a8\u96aa\u6027\u8cc7\u7522\u5e73\u5747\u9918\u984d\u300d\u6700\u65b0\u8cc7\u6599\u5e74\u6708\u8207\u67e5\u8a62\u7576\u65e5\u6703\u6709\u5169\u500b\u6708\u7684\u843d\u5dee\uff0c\u6545\u6700\u8fd1\u4e00\u5e74\u671f\u9593\u4e4b\u5224\u65b7\u4ee5\u6700\u65b0\u8cc7\u6599\u5e74\u6708\u5f80\u524d\u63a8\u7b97\u4e00\u5e74\u3002<br />
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6a19\u984c\u540d\u7a31
#==================================================
L1205S07.index1=Credit Guarantee
L1205S07.index2=Not Credit Guaranteed
L1205S07.index3=(C denotes credit guarantee)
L1205S07.index4=Related Companies Total
L1205S07.index5=Group Total
L1205S07.index6=Current Deposit
L1205S07.index7=Outstanding Balance
L1205S07.index8=Credit Limit
L1205S07.index9=Contribution
L1205S07.index10=Item
L1205S07.index11=Principal Borrower
L1205S07.index12=Unit: TWD1,000
L1205S07.index13=Data Inquiry Period
L1205S07.index14=Please select a customer
L1205S07.index15=UBN (excluding repeated serial number)
L1205S07.index16=Customer Status
L1205S07.index17=No. Of Records
L1205S07.index18=Amount
L1205S07.index19=Compiling In Progress
L1205S07.index20=Document Status
L1205S07.index21=Starting Year/Month
L1205S07.index22=Ending Year/Month
L1205S07.index23=Document Status
L1205S07.index24=Compiling In Progress
L1205S07.index25=Borrower
L1205S07.index26=Reference Borrower
L1205S07.index27=Main Collateral
L1205S07.index28=Interest Rate
L1205S07.index29=Tenor
L1205S07.index30=Year
L1205S07.index31=Months
L1205S07.index32=\u4e3b\u8981\u95dc\u4fc2\u6236\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u6bd4\u8f03\u8868
L1205S07.index33=\u5217\u5370\u7c3d\u5831\u66f8\u6642\uff0c\u7cfb\u7d71\u4e0d\u6703\u4e00\u4f75\u5217\u5370\u4e3b\u8981\u95dc\u4fc2\u6236\u8207\u672c\u884c\u6388\u4fe1\u5f80\u4f86\u6bd4\u8f03\u8868\uff0c\u8acb\u518d\u81ea\u884c\u958b\u555fEXCEL\u5217\u5370\u3002
L1205S07.createBY1=System Generated
L1205S07.createBY2=Manually Generated
L1205S07.prtFlag1=Print
L1205S07.prtFlag2=Do Not Print
#==================================================
# \u5f80\u4f86\u5f59\u7e3dradio\u540d\u7a31
#==================================================
L1205S07.radio1=DBU Customer
L1205S07.radio2=OBU Customer
L1205S07.radio3=Overseas Peer
L1205S07.radio4=Overseas Customer
L1205S07.radio5=Yes
L1205S07.radio6=No
L1205S07.radio7=Separate Pages
L1205S07.radio8=Combine Into Same Page
#==================================================
# \u5f80\u4f86\u5f59\u7e3dcheckbox\u540d\u7a31
#==================================================
L1205S07.checkbox1=Borrower
L1205S07.checkbox2=Borrower's Representative
L1205S07.checkbox3=Group Total
L1205S07.checkbox4=Related Companies Total
L1205S07.checkbox5=Group company
L1205S07.checkbox6=Related Company
#==================================================
# \u8cc7\u672c\u9069\u8db3\u7387\u5f71\u97ff\u6578\u8cc7\u6599\u6a94
#==================================================
L120S03A.oid=oid
L120S03A.mainId=Document Number (Case Report)
L120S03A.cntrMainId=Document Number (Credit Facility Report)
L120S03A.cntrNo=Credit Limit Serial Number
L120S03A.crdFlag=Credit Guaranteed/Not Credit Guaranteed
L120S03A.applyAmt=Original Applied Credit Limit
L120S03A.fcltAmt=Credit Limit
L120S03A.collAmt=Eligible Collateral Deduction
L120S03A.rskRatio=Risk Weight (apply remark for guarantor)
L120S03A.rskAmt1=Exposure Net Of Risk Mitigant
L120S03A.rskr1=Risk Weight Net Of Risk Mitigant
L120S03A.camt1=Utilized Capital (not required)
L120S03A.bisr1=% To Capital Adequacy Ratio
L120S03A.costr1=Funding Cost %
L120S03A.crdRatio=Credit Guarantee Percentage
L120S03A.rskMega=Bank's exposure A*(1-B) * B'
L120S03A.rskCrd=Credit Guaranteed Exposure (20% risk weight)
L120S03A.rskAmt2=Total Exposure Net Of Risk Mitigant
L120S03A.rskr2=Risk Weight Net Of Risk Mitigant
L120S03A.camt2=Utilized Capital (not required)
L120S03A.bisr2=% To Capital Adequacy Ratio
L120S03A.costr2=Funding cost for the credit limit (H)
L120S03A.creator=Originator's ID
L120S03A.createTime=Date Created
L120S03A.updater=Modifier's ID
L120S03A.updateTime=Date Of Change
L120S03A.crdRskRatio=Risk Weight(Not Credit Guaranteed)

#==================================================
# \u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86\u6a94
#==================================================
L120S04A.oid=oid
L120S04A.mainId=Document Number
L120S04A.createBY=Document Generation Method
L120S04A.custId=Unified Business Number
L120S04A.dupNo=Repeat Serial No.
L120S04A.custName=Account name please select any Language input
L120S04A.typCd=Region/Department
L120S04A.custRelation=Relationship With The Borrower
L120S04A.prtFlag=Case Report Printing Remarks
L120S04A.queryDateS=Data Inquiry Start Date
L120S04A.queryDateE=Data Inquiry End Date
L120S04A.itemName=Business Transaction
L120S04A.memo=Base Period
L120S04A.dep=Deposit
L120S04A.depMemo=Deposits - Base Period
L120S04A.depTime=Current Deposit
L120S04A.depFixed=Fixed deposits
L120S04A.loan=Loan
L120S04A.loanQMemo=Loans - Credit Limit - Base Period
L120S04A.loanQuota=Balance
L120S04A.loanABMemo=Loans - Average Balance - Base Period
L120S04A.loanAvgBal=Average Outstanding Balance
L120S04A.loanAvgRate=Average Utilization
L120S04A.exchg=Foreign Currency
L120S04A.exchgMemo=Foreign Currency - Base Period
L120S04A.exchgImp=Import (transactions/amount)
L120S04A.exchgImpRec=Foreign Currency - Import (transactions)
L120S04A.exchgImpAmt=Foreign Currency - Import (amount)
L120S04A.exchgExp=Export (transactions/amount)
L120S04A.exchgExpRec=Foreign Currency - Export (transactions)
L120S04A.exchgExpAmt=Foreign Currency - Export (amount)
L120S04A.exchgOut=Outward Remittance (transactions/amount)
L120S04A.exchgOutRec=Foreign Currency - Outward Remittance (transactions)
L120S04A.exchgOutAmt=Foreign Currency - Outward Remittance (amount)
L120S04A.exchgIn=Inward Remittance (transactions/amount)
L120S04A.exchgInRec=Foreign Currency - Inward Remittance (transactions)
L120S04A.exchgInAmt=Foreign Currency - Inward Remittance (amount)
L120S04A.der=Derivative
L120S04A.derMemo=Derivative - Base Period
L120S04A.derOption=Options
L120S04A.derRateExchg=Interest Rate Swap
L120S04A.derCCS=Cross Currency Swap
L120S04A.derDraft=Derivative - Foreard Exchange
L120S04A.derSWAP=Forward Exchange (includes SWAP)
L120S04A.trust=Trust
L120S04A.trustMemo=Trust - Base Period
L120S04A.trustBond=ETF\u3001Foreign Bonds
L120S04A.trustFund=Fund
L120S04A.trustOther=Trust accounts
L120S04A.trustSetAcct=Central Depository
L120S04A.trustSecurities=Trust - Securities Trust
L120S04A.trustREITs=Trust - Real Estate Trust
L120S04A.trustWelDep=Trust - Welfare & Savings Trust
L120S04A.wealth=Wealth Management
L120S04A.wealthMemo=Wealth Management - Base Period
L120S04A.wealthTrust=Trust
L120S04A.wealthInsCom=Insurance Commission
L120S04A.wealthInvest=Dual Currency Investment
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMemo=Salary Account - Base Period
L120S04A.salaryRec=Number Of Salary Accounts
L120S04A.salaryFixed=Number Of Fixed Deposit Accounts
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMortgage=Mortgage
L120S04A.salaryConsumption=Consumer Loan
L120S04A.salaryCard=Number Of Credit Card Holders
L120S04A.salaryNetwork=Number Of Personal Internet Banking Users
L120S04A.cardComMemo=Cards - Corporate Card - Base Period
L120S04A.card=Cards
L120S04A.cardCommercial=Corporate Card
L120S04A.cardNoneCommercial=Non-business cards
L120S04A.cardNoneCommercialMemo=Cards - Non-business cards - Base Period
L120S04A.cardBrnMemo=Cards - Co-brand Card - Base Period
L120S04A.cardCoBranded=Co-brand Card
L120S04A.GEB=GEB
L120S04A.GEBMemo=GEB - NTD/Foreign Currency - Base Period
L120S04A.GEBTWDRec=Number Of NTD Transactions
L120S04A.GEBOTHRec=Number Of Foreign Currency Transactions
L120S04A.GEBLCMemo=GEB - LC - Base Period
L120S04A.GEBLCRec=Number Of LC Transactions
L120S04A.profitMemo=Profit Contribution - Base Period
L120S04A.profit=Profit Contribution
L120S04A.creator=Originator's ID
L120S04A.createTime=Date Created
L120S04A.updater=Modifier's ID
L120S04A.updateTime=Date Of Change
L120S04A.profitSalaryAmt=Contain Salary Account
L120S04A.profitTrustFdtaAmt=Contain Trust Department accounts spreads
L120S04A.rcvBuyFactAmt=AR buyer credit
L120S04A.rcvBuyAvgBal=AR buyer Avg. Bal.

#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u4e3b\u6a94
#==================================================
L120S06A.oid=oid
L120S06A.mainId=Document Number
L120S06A.custId=Borrower's UBN
L120S06A.dupNo=Borrower's Repeated Serial Number
L120S06A.cntrNo=Borrower's Credit Limit Serial Number
L120S06A.custName=Borrower
L120S06A.custId2=Reference Borrower's UBN
L120S06A.dupNo2=Reference Borrower's Repeated Serial Number
L120S06A.cntrNo2=Reference Borrower's Credit Limit Serial Number
L120S06A.custName2=Reference Borrower
L120S06A.proPerty=Nature
L120S06A.currentApplyCurr=Applied Credit Limit - Currency
L120S06A.currentApplyAmt=Applied Credit Limit - Amount
L120S06A.lnSubject=Credit Category
L120S06A.purpose=Use Of Fund
L120S06A.gutPercent=Guarantee Percentage
L120S06A.payDeadline=Tenor
L120S06A.guarantor=Guarantor
L120S06A.guarantorMemo=Guarantor Remarks
L120S06A.proPerty2=Nature
L120S06A.currentApplyCurr2=Applied Credit Limit - Currency
L120S06A.currentApplyAmt2=Applied Credit Limit - Amount
L120S06A.lnSubject2=Credit Category
L120S06A.purpose2=Use Of Fund
L120S06A.gutPercent2=Guarantee Percentage
L120S06A.payDeadline2=Tenor
L120S06A.guarantor2=Guarantor
L120S06A.guarantorMemo2=Guarantor Remarks
L120S06A.printMode=Print Mode
L120S06A.creator=Originator's ID
L120S06A.createTime=Date Created
L120S06A.updater=Modifier's ID
L120S06A.updateTime=Date Of Change

#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u660e\u7d30\u6a94
#==================================================
L120S06B.oid=oid
L120S06B.mainId=Document Number
L120S06B.custId=Borrower's UBN
L120S06B.dupNo=Borrower's Repeated Serial Number
L120S06B.cntrNo=Borrower's Credit Limit Serial Number
L120S06B.type=Category
L120S06B.itemType=Item Category
L120S06B.itemDscr=Item Description
L120S06B.creator=Originator's ID
L120S06B.createTime=Date Created
L120S06B.updater=Modifier's ID
L120S06B.updateTime=Date Of Change

#==================================================
# \u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5f80\u4f86\u5be6\u7e3e\u5f59\u7e3d\u8868\u4e3b\u6a94
#==================================================
L120S04B.oid=oid
L120S04B.mainId=Document Number
L120S04B.grpNo=Group ID
L120S04B.grpName=Group Name
L120S04B.grpYear=Group ratings year
L120S04B.grpGrrd=Group rating
L120S04B.grpGrrd1=Level 1
L120S04B.grpGrrd2=Level 2
L120S04B.grpGrrd3=Level 3
L120S04B.grpGrrd4=Level 4
L120S04B.grpGrrd5=Level 5
L120S04B.grpGrrd6=Not rated
L120S04B.grpGrrd7=Problems Group
L120S04B.grpGrrdMain=\u203bGroup level 1-5(level 1-7 after 2017 rating year) for the major group
L120S04B.title1=1.Borrowers the bank dealings performance summary table\uff1a
L120S04B.title2=2.Borrowers and related the bank dealings performance summary table\uff1a
L120S04B.dollar=(Unit: thousand)
L120S04B.mainGrpDateS=Major group From the date of the most recent year begin date
L120S04B.mainGrpDateE=Major group From the date of the most recent year end date
L120S04B.mainGrpAvgRate=Major group - The rate of return of the average balance
L120S04B.depositDateS=Borrowers and the relationship  nearly six months begin date
L120S04B.depositDateE=Borrowers and the relationship  nearly six months end date
L120S04B.megaAvgAmt=Total average deposits of the Bank\uff0dAmount
L120S04B.demandAmt=Demand deposits - amount
L120S04B.demandAvgRate=Demand deposit percentage
L120S04B.creator=Originator's ID
L120S04B.createTime=Date Created
L120S04B.updater=Modifier's ID
L120S04B.updateTime=Date Of Change
L120S04B.other=Explain:<br/>1.Field\u300cThe case of the major group, the most recent year, the Group risk assets average balance rate of return\u300dAccording to the Credit Management Division\u300cThe the major group contribution and using the rate of return on capital employed (By  group)\u300dSheet\u3002<br/>2.Annualized rate of return calculation\uff1a(D/(A-B+C))/months *12<br/>3.Performance summary table of Outward Remittance = Outward Remittance<br/>Performance summary table of Inward Remittance = Inward Remittance +clean bill+Buy foreign currency cash
L120S04B.other1=\u203bThe case of major group, the most recent year
L120S04B.other2=Group's risk assets and return on average balances\uff1a
L120S04B.other3=\u203bBorrowers and the relationship nearly six months
L120S04B.other4=Average deposit in the Bank's total TWD
L120S04B.other5=The thousand (Demand TWD
L120S04B.other6=Thousand, accounting for
L120S04B.other7=\u203bThe most recent year
L120S04B.other8=Primary borrower's risk assets and return on average balances\uff1a
L120S04B.other9=Borrowers and the relationship's the average rate of return on the balance of risk assets\uff1a
#J-111-0052 \u4fee\u6539\u501f\u6236\u66a8\u95dc\u4fc2\u6236\u8207\u672c\u884c\u5f80\u4f86\u5be6\u7e3e\u5f59\u7e3d\u8868
L120S04B.other10=\u203b\u501f\u6236\u66a8\u95dc\u4fc2\u6236\u65bc105.8.1\u524d\u65bc\u672c\u884c\u81ea\u52d5\u5316\u901a\u8def\u4e4b\u62c6\u55ae\u5b9a\u5b58\uff0c\u622a\u81f3
L120S04B.other11=\u6b62\u4ecd\u81ea\u52d5\u5c55\u671f\u4e4b\u91d1\u984d\u5171\u8a08
L120S04B.other12=\u5343\u5143(\u5b9a\u5b58\u5e73\u5747\u5229\u7387\u70ba
L120S04B.other13=%)\u3002\u8a72\u62c6\u55ae\u5b9a\u5b58\u4e4b\u8fd1\u534a\u5e74\u5e73\u5747\u91d1\u984d\u70ba
L120S04B.other14=\u5343\u5143\uff0c\u5e73\u5747\u5229\u7387\u70ba
L120S04B.other15=%\u3002
L120S04B.year=\u5e74
L120S04B.month=\u6708
L120S04B.yearMontInput=\u8acb\u8f38\u5165\u8fc4\u5e74\u7684\u5e74-\u6708\u8cc7\u6599
#==================================================
# \u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5f80\u4f86\u5be6\u7e3e\u5f59\u7e3d\u8868\u660e\u7d30\u6a94
#==================================================
L120S04C.oid=oid
L120S04C.mainId=Document Number
L120S04C.docKind=kind
L120S04C.docDate=Data years
L120S04C.docDateE=Data years(Ended)
L120S04C.avgDepositAmt=Average deposit<br/>(TWD)
L120S04C.avgLoanAmt=A.Average Loans<br/>(TWD)
L120S04C.rcvBuyAvgAmt=B.Accounts receivable without recourse buyer factoring average balance<br/>(TWD)
L120S04C.rcvSellAvgAmt=C.Accounts receivable without recourse seller financing average balance<br/>(TWD)
L120S04C.exportAmt=Import LC and Outward Remittance(USD)
L120S04C.importAmt=Export Negotiation and Inward Remittance(USD)
L120S04C.profitAmt=D.Profit contribution
L120S04C.profitSalaryAmt=Salary Account
L120S04C.profitContain=--Contain--
L120S04C.profitTrustFdtaAmt=Trust Department accounts spreads
L120S04C.profitRate=Rate of return\uff05<br/>D/(A-B+C)<br/>(*:Annualized \uff0cOppression 2)
L120S04C.creator=Originator's ID
L120S04C.createTime=Date Created
L120S04C.updater=Modifier's ID
L120S04C.updateTime=Date Of Change
L120S04C.withRelation=With the relationship 

#==================================================
#J-113-0183 e-Loan\u6388\u4fe1\u7ba1\u7406\u7cfb\u7d71\u65b0\u589eRORWA\u8a08\u7b97
#==================================================
RORWA.title1=\u4f7f\u7528\u8005\u8f38\u5165
RORWA.applyAmt=\u73fe\u8acb\u984d\u5ea6
RORWA.newCreditLimitAmt=\u8003\u616e\u8868\u5916\u4fe1\u7528\u8f49\u63db\u4fc2\u6578\u4e4b\u65b0\u4f5c\u589e\u984d\u984d\u5ea6(\u8868\u5167+\u8868\u5916X\u4fe1\u7528\u8f49\u63db\u4fc2\u6578)
RORWA.qualCollDiscVal=\u6a19\u6e96\u6cd5\u4e0b\u5408\u683c\u91d1\u878d\u64d4\u4fdd\u54c1
RORWA.riskOffInsAmt=\u98a8\u96aa\u62b5\u6e1b\u5f8c\u66b4\u96aa\u984d
RORWA.riskWeight=\u98a8\u96aa\u6b0a\u6578
RORWA.onlyGuar_s25a=\u672c\u984d\u5ea6\u50c5\u6709\u4fdd\u8b49\u79d1\u76ee\u3001\u61c9\u6536\u4fe1\u7528\u72c0\u6b3e\u9805\u79d1\u76ee
RORWA.yes=\u662f
RORWA.no=\u5426
RORWA.bisIncomeRate=\u6388\u4fe1\u6536\u76ca\u7387
RORWA.bisEstimatedType=\u975e\u6388\u4fe1\u6536\u76ca\u9810\u4f30\u5c0d\u8c61
RORWA.bisEstimatedDate=\u8cc7\u6599\u57fa\u6e96\u5e74\u6708
RORWA.kind1=\u500b\u9ad4
RORWA.kind2=\u96c6\u5718
RORWA.bisNoneLoanProfit=\u8fd1\u4e00\u5e74\u975e\u6388\u4fe1\u8ca2\u737b\u5ea6
RORWA.bisLoanBal=\u8fd1\u4e00\u5e74\u6388\u4fe1\u9918\u984d
RORWA.bisFactAmtIncrease=\u65b0\u4f5c\u589e\u984d\u984d\u5ea6
RORWA.bisEstimatedReturn=\u9810\u4f30\u975e\u6388\u4fe1\u6536\u76ca\u7387
RORWA.title2=\u8a08\u7b97\u7d50\u679c
RORWA.bisBankWorkCost=\u71df\u904b\u6210\u672c\u7387
RORWA.bisRiskCost=\u98a8\u96aa\u6210\u672c(\u5168\u884c\u5e73\u5747)
RORWA.bisTwdRate=\u984d\u5ea6\u5e63\u5225\u6298\u53f0\u5e63\u532f\u7387
RORWA.rateDate=\u532f\u7387\u8cc7\u6599\u65e5\u671f
RORWA.bisRiskAdjReturn=\u98a8\u96aa\u8abf\u6574\u5f8c\u6536\u76ca
RORWA.bisRItemD=\u62b5\u6e1b\u5f8c\u98a8\u96aa\u6b0a\u6578
RORWA.bisRorwa=RORWA
RORWA.StandardAppr=\u6a19\u6e96\u6cd5
RORWA.IRBAppr=\u5167\u8a55\u6cd5
other.money=\u5143
other.thousandMoney=\u4edf\u5143
other.explain=\u8aaa\u660e
other.year=\u5e74
other.month=\u6708
other.curr=\u5e63\u5225
button.import=\u5f15\u9032
button.calc=\u8a08\u7b97
bisIncomeRateDir.title1=\u6388\u4fe1\u6536\u76ca\u7387\u8aaa\u660e
bisIncomeRateDir.content1=\u6388\u4fe1\u6536\u76ca\u7387\u4ee5\u5229\u606f\u6536\u5165\u3001\u624b\u7e8c\u8cbb\u53ca\u5e33\u7ba1\u8cbb\u7b49\u6b78\u5c6c\u4e8b\u696d\u7fa4\u6536\u5165\uff0c\u52a0\u7e3d\u4f9d\u984d\u5ea6\u63db\u7b97\u70ba\u5e74\u5316\u4e4b\u6536\u76ca\u7387\uff0c\u53e6\u6216\u6709\u6027\u6536\u5165\u4e0d\u8a08(\u61f2\u7f70\u6027\u6536\u76ca\u3001\u9055\u7d04\u91d1\u3001\u672a\u9054\u627f\u8afe\u4e8b\u9805\u4e4b\u5229\u7387\u7b49)\u3002
bisCostRateTypeDirBox.title1=FTP\u8aaa\u660e
bisCostRateTypeDirBox.content1=1.\u6388\u4fe1\u5929\u671f\u5c0d\u61c9\u4e4bFTP\u53f0\u5e63\u53ca\u7f8e\u91d1\u53ef\u5f15\u9032\u67e5\u8a62\uff0c\u96dc\u5e63\u8acb\u4ee5\u672c\u884c\u501f\u5165\u8cc7\u91d1\u6210\u672c\u4f30\u7b97\uff0c\u4e26\u4ee5\u6388\u4fe1\u689d\u4ef6\u4e2d\u6700\u9577\u6388\u4fe1\u5929\u671f\u6240\u5c0d\u61c9\u4e4bFTP\u586b\u5beb\u3002<br>2.\u82e5\u8a72\u984d\u5ea6\u201d\u5168\u6578\u201d\u7686\u70ba\u4fdd\u8b49\u984d\u5ea6\uff0c\u5c0d\u61c9\u4e4bFTP\u8acb\u586b\u5beb0\u3002
bisCostRateTypeDirBox.title2=\u8cc7\u91d1\u6210\u672c\u8aaa\u660e
bisCostRateTypeDirBox.content2=1.\u65b0\u53f0\u5e63\u3001\u7f8e\u91d1\u8cc7\u91d1\u6210\u672c\uff1a<br>\u4f9d\u6388\u4fe1\u653e\u6b3e\u5340\u9593\u5206\u70ba\u77ed\u3001\u4e2d\u9577\u671f\uff0c\u5206\u5225\u7531\u7cfb\u7d71\u81ea\u52d5\u8a08\u7b97\uff0c\u516c\u5f0f\u5982\u4e0b\uff1a<br><br>\u77ed\u671f\u8cc7\u91d1\u6210\u672c =\u300c\u5be6\u8cea\u5b58\u6b3e\u5e73\u5747\u5229\u7387\u300d<br>\u4e2d\u9577\u671f\u8cc7\u91d1\u6210\u672c =\u300c\u5be6\u8cea\u5b58\u6b3e\u5e73\u5747\u5229\u7387\u300d+\u4e2d\u9577\u671fterm spread*<br>*\u4e2d\u9577\u671fterm spread =  \u6388\u4fe1\u9069\u7528\u5229\u7387(1~3\u5e74) \u2013 \u6388\u4fe1\u9069\u7528\u5229\u7387(9\u500b\u6708~1\u5e74)<br>(\u5229\u7387\u5f9eCRAAL001\u5831\u8868\u53d6\u5f97\uff0c\u60df\u82e5\u5229\u7387\u5012\u639b\u81f4Spread\u70ba\u8ca0\u6642\u4e0d\u8003\u616e)<br><br>2.\u975e\u65b0\u53f0\u5e63\u3001\u7f8e\u91d1\uff1a\u8acb\u4ee5\u672c\u884c\u501f\u5165\u8cc7\u91d1\u6210\u672c\u4f30\u7b97\u3002<br><br>3.\u82e5\u8a72\u984d\u5ea6\u50c5\u6709\u4fdd\u8b49\u79d1\u76ee\u3001\u61c9\u6536\u4fe1\u7528\u72c0\u6b3e\u9805\u79d1\u76ee\uff0c\u5c0d\u61c9\u4e4b\u8cc7\u91d1\u6210\u672c\u61c9\u70ba0\u3002
bisNoneLoanProfitDirBox.title1=\u8fd1\u4e00\u5e74\u975e\u6388\u4fe1\u8ca2\u737b\u5ea6\u8aaa\u660e
bisNoneLoanProfitDirBox.content1=1.\u975e\u6388\u4fe1\u8ca2\u737b\u5ea6\uff1a\u501f\u6236\u6700\u8fd1\u4e00\u5e74\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86\u5f59\u7e3d\uff0c\u6263\u9664\u6388\u4fe1\u696d\u52d9\u4e4b\u8ca2\u737b\u5ea6\uff0c\u5305\u62ec\u5b58\u6b3e\u3001\u5916\u532f\u3001\u884d\u751f\u6027\u5546\u54c1\u3001\u4fe1\u8a17\u3001\u8ca1\u52d9\u7ba1\u7406\u3001\u85aa\u8f49\u6236\u3001\u7968\u50b5\u5238\u3001\u4fe1\u7528\u5361\u53ca\u85aa\u8f49\u6236\u4fe1\u7528\u5361\u3002<br>2.\u82e5\u501f\u6236\u5f80\u4f86\u672a\u6eff\u4e00\u5e74\uff0c\u5c07\u5e74\u5316\u5f8c\u8a08\u7b97\u3002