/* 
 * L260M01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import org.apache.wicket.markup.html.form.Check;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 貸後管理紀錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260M01D", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L260M01D extends GenericBean implements IDataObject, IDocObject {

	//
	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 新產生時：getUUID()
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 序號(系統UNID) **/
	@Size(max=60)
	@Column(name="UNID", length=60, columnDefinition="CHAR(60)")
	private String unid;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max=20)
	@Column(name="LOANNO", length=20, columnDefinition="CHAR(20)")
	private String loanNo;

	/** 業務別 **/
	@Size(max=2)
	@Column(name="LOANKIND", length=2, columnDefinition="CHAR(2)")
	private String loanKind;

	/** 
	 * 追蹤事項通知日期<p/>
	 * YYYY-MM-dd
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="FOLLOWDATE", columnDefinition="DATE")
	private Date followDate;

	/** 
	 * 類別<p/>
	 * 複選 以｜分隔
	 */
	@Size(max=20)
	@Column(name="FOLLOWKIND", length=20, columnDefinition="VARCHAR(20)")
	private String followKind;

	/** 追蹤事項通知內容 **/
	@Size(max=800)
	@Column(name = "FOLLOWCONTENT", length = 800, columnDefinition = "VARCHAR(800)")
	private String followContent;

	/**
	 * 應辦理追蹤對象<p/>
	 * 01-帳務人員,02-AO人員
	 */
	@Size(max=2)
	@Column(name="FOLLOWSTAFF", length=2, columnDefinition="CHAR(2)")
	private String followStaff;

	/** 檢核日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CHKDATE", columnDefinition="DATE")
	private Date chkDate;

	/** 
	 * 符合註記<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CONFORMFG", length=1, columnDefinition="CHAR(1)")
	private String conformFg;

	/** 追蹤說明 **/
//	@Lob
//	@Basic(fetch = FetchType.LAZY)
//	@Column(name="FOLLOWMEMO", columnDefinition="CLOB")
//	private String followMemo;
	@Size(max=506)
	@Column(name = "FOLLOWMEMO", length = 506, columnDefinition = "VARCHAR(506)")
	private String followMemo;

	/** 
	 * 辦理狀況<p/>
	 * 1-未辦理2-辦理中3-已完成
	 */
	@Size(max=1)
	@Column(name="HANDLINGSTATUS", length=1, columnDefinition="CHAR(1)")
	private String handlingStatus;

	/** 
	 * 資料寫入來源MEMO<p/>
	 * BTT交易TCS編號/待追蹤設定檔之序號
	 */
	@Size(max=60)
	@Column(name="DATASRC", length=60, columnDefinition="VARCHAR(60)")
	private String dataSrc;

	/** 
	 * 還款來源異常註記<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="REPAYUNUSUALFG", length=1, columnDefinition="CHAR(1)")
	private String repayUnusualFg;

	/** 證明文件說明 **/
	@Size(max=800)
	@Column(name = "FILEDESC", length = 800, columnDefinition = "VARCHAR(800)")
	private String fileDesc;

	/** 理由敘述 **/
	@Size(max=800)
	@Column(name = "UNUSUALDESC", length = 800, columnDefinition = "VARCHAR(800)")
	private String unusualDesc;

	/**
	 * 是否承做<p/>
	 * Y承做/N婉卻
	 */
	@Size(max=1)
	@Column(name="ISNOTIONAL", length=1, columnDefinition="CHAR(1)")
	private String isNotional;

	/**
	 * 是否申報疑似洗錢<p/>
	 * Y申報/N不申報
	 */
	@Size(max=1)
	@Column(name="ISAML", length=1, columnDefinition="CHAR(1)")
	private String isAML;

	/**
	 * 資料來源<p/>
	 * P:人工 B:中心Batch
	 */
	@Size(max=1)
	@Column(name="DATAFROM", length=1, columnDefinition="CHAR(1)")
	private String dataFrom;

	/**
	 * 是否通過檢核<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHECKYN", length=1, columnDefinition="CHAR(1)")
	private String checkYN;

    /**
     * (前次)附加文件mainId
     */
    @Size(max = 32)
    @Column(name = "FIELDMAINID", length = 32, columnDefinition = "VARCHAR(32)")
    private String fieldMainId;
    
	/** 
	 * 是否申購理財商品<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="FINPRODFG", length=1, columnDefinition="CHAR(1)")
	private String finProdFg;

    /**
     * 案件註記<p/>
     * 01:謄本
     * 02:實價登錄
	 * 03:餘屋貸款
	 * 04:央行不動產管控
     */
    @Size(max=2)
    @Column(name="CASEMARK", length=2, columnDefinition="VARCHAR(2)")
    private String caseMark;

	/**
	 * 實價登錄查詢狀況<p/>
	 * 更新點：新增L260M01D紀錄 / 上傳實登附件 / 刪除實登附件
	 */
	@Size(max=1)
	@Column(name="RASPSTATUS", length=1, columnDefinition="VARCHAR(1)")
	private String raspStatus;

	/**
	 * 是否已動工興建<p/>
	 * begin construction：Y/N
	 */
	@Size(max=1)
	@Column(name="BEGCONSTR", length=1, columnDefinition="VARCHAR(1)")
	private String begConstr;

	/**
	 * 實際動工日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ACTSTDATE", columnDefinition = "DATE")
	private Date actStDate;

	/** 
	 *  分項UID 
	 */
	@Size(max = 32)
	@Column(name = "FROM602SUID", length = 32, columnDefinition = "CHAR(32)")
	private String from602SUid;

	/** 
	 * 分項核准時間 
	 */
	@Column(name = "FROM602SAPPTIME", columnDefinition = "TIMESTAMP")
	private Date from602SApptime;

	/** 
	 * 分項序號 
	 */
	@Digits(integer = 3, fraction = 0, groups = Check.class)
	@Column(name = "FROM602SSEQNO", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal from602SSeqno;
	
	/** 
	 *  分項ESG 約定條件全部未達成 
	 */
	@Size(max = 1)
	@Column(name = "FROM602SESGSUNRE", length = 1, columnDefinition = "CHAR(1)")
	private String from602SESGsunre;

   /** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 新產生時：getUUID()
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  新產生時：getUUID()
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件亂碼<p/>
	 * 每次儲存：getRandomCode()
	 */
	public String getRandomCode() {
		return this.randomCode;
	}
	/**
	 *  設定文件亂碼<p/>
	 *  每次儲存：getRandomCode()
	 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLoanNo() {
		return this.loanNo;
	}
	/** 設定放款帳號 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 取得序號(系統UNID) **/
	public String getUnid() {
		return this.unid;
	}
	/** 設定序號(系統UNID) **/
	public void setUnid(String value) {
		this.unid = value;
	}

	/** 取得業務別 **/
	public String getLoanKind() {
		return this.loanKind;
	}
	/** 設定業務別 **/
	public void setLoanKind(String value) {
		this.loanKind = value;
	}

	/** 
	 * 取得追蹤事項通知日期<p/>
	 * YYYY-MM-dd
	 */
	public Date getFollowDate() {
		return this.followDate;
	}
	/**
	 *  設定追蹤事項通知日期<p/>
	 *  YYYY-MM-dd
	 **/
	public void setFollowDate(Date value) {
		this.followDate = value;
	}

	/** 
	 * 取得類別<p/>
	 * 複選 以｜分隔
	 */
	public String getFollowKind() {
		return this.followKind;
	}
	/**
	 *  設定類別<p/>
	 *  複選 以｜分隔
	 **/
	public void setFollowKind(String value) {
		this.followKind = value;
	}

	/** 取得追蹤事項通知內容 **/
	public String getFollowContent() {
		return this.followContent;
	}
	/** 設定追蹤事項通知內容 **/
	public void setFollowContent(String value) {
		this.followContent = value;
	}

	/**
	 * 取得應辦理追蹤對象<p/>
	 * 01-帳務人員,02-AO人員
	 */
	public String getFollowStaff() {
		return this.followStaff;
	}
	/**
	 *  設定應辦理追蹤對象<p/>
	 *  01-帳務人員,02-AO人員
	 **/
	public void setFollowStaff(String value) {
		this.followStaff = value;
	}

	/** 取得檢核日期 **/
	public Date getChkDate() {
		return this.chkDate;
	}
	/** 設定檢核日期 **/
	public void setChkDate(Date value) {
		this.chkDate = value;
	}

	/** 
	 * 取得符合註記<p/>
	 * Y/N
	 */
	public String getConformFg() {
		return this.conformFg;
	}
	/**
	 *  設定符合註記<p/>
	 *  Y/N
	 **/
	public void setConformFg(String value) {
		this.conformFg = value;
	}

	/** 取得追蹤說明 **/
	public String getFollowMemo() {
		return this.followMemo;
	}
	/** 設定追蹤說明 **/
	public void setFollowMemo(String value) {
		this.followMemo = value;
	}

	/** 
	 * 取得辦理狀況<p/>
	 * 1-未辦理2-辦理中3-已完成
	 */
	public String getHandlingStatus() {
		return this.handlingStatus;
	}
	/**
	 *  設定辦理狀況<p/>
	 *  1-未辦理2-辦理中3-已完成
	 **/
	public void setHandlingStatus(String value) {
		this.handlingStatus = value;
	}

	/** 
	 * 取得資料寫入來源MEMO<p/>
	 * BTT交易TCS編號/待追蹤設定檔之序號
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}
	/**
	 *  設定資料寫入來源MEMO<p/>
	 *  BTT交易TCS編號/待追蹤設定檔之序號
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/** 
	 * 取得還款來源異常註記<p/>
	 * Y/N
	 */
	public String getRepayUnusualFg() {
		return this.repayUnusualFg;
	}
	/**
	 *  設定還款來源異常註記<p/>
	 *  Y/N
	 **/
	public void setRepayUnusualFg(String value) {
		this.repayUnusualFg = value;
	}

	/** 取得證明文件說明 **/
	public String getFileDesc() {
		return this.fileDesc;
	}
	/** 設定證明文件說明 **/
	public void setFileDesc(String value) {
		this.fileDesc = value;
	}

	/** 取得理由敘述 **/
	public String getUnusualDesc() {
		return this.unusualDesc;
	}
	/** 設定理由敘述 **/
	public void setUnusualDesc(String value) {
		this.unusualDesc = value;
	}

	/**
	 * 取得是否承做<p/>
	 * Y承做/N婉卻
	 */
	public String getIsNotional() {
		return this.isNotional;
	}
	/**
	 *  設定是否承做<p/>
	 *  Y承做/N婉卻
	 **/
	public void setIsNotional(String value) {
		this.isNotional = value;
	}

	/**
	 * 取得是否申報疑似洗錢<p/>
	 * Y申報/N不申報
	 */
	public String getIsAML() {
		return this.isAML;
	}
	/**
	 *  設定是否申報疑似洗錢<p/>
	 *  Y申報/N不申報
	 **/
	public void setIsAML(String value) {
		this.isAML = value;
	}

	/** 取得資料來源 **/
	public String getDataFrom() {
		return this.dataFrom;
	}
	/** 設定資料來源 **/
	public void setDataFrom(String value) {
		this.dataFrom = value;
	}

	/**
	 * 取得是否通過檢核<p/>
	 * Y/N
	 */
	public String getCheckYN() {
		return this.checkYN;
	}
	/**
	 *  設定是否通過檢核<p/>
	 *  Y/N
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}

    /**
     * 取得(前次)附加文件mainId
     */
    public String getFieldMainId() {
        return this.fieldMainId;
    }

    /**
     * 設定(前次)附加文件mainId
     */
    public void setFieldMainId(String value) {
        this.fieldMainId = value;
    }
    
    /** 
	 * 取得是否申購理財商品<p/>
	 * Y/N
	 */
	public String getFinProdFg() {
		return this.finProdFg;
	}
	/**
	 *  設定是否申購理財商品<p/>
	 *  Y/N
	 **/
	public void setFinProdFg(String value) {
		this.finProdFg = value;
	}

    /**
     * 取得案件註記<p/>
     * 01:謄本
     * 02:實價登錄
	 * 03:餘屋貸款
	 * 04:央行不動產管控
     */
    public String getCaseMark() {
        return this.caseMark;
    }
    /**
     *  設定案件註記<p/>
     * 01:謄本
     * 02:實價登錄
	 * 03:餘屋貸款
	 * 04:央行不動產管控
     **/
    public void setCaseMark(String value) {
        this.caseMark = value;
    }

	/**
	 * 取得實價登錄查詢狀況<p/>
	 * 更新點：新增L260M01D紀錄 / 上傳實登附件 / 刪除實登附件
	 */
	public String getRaspStatus() {
		return this.raspStatus;
	}
	/**
	 * 設定實價登錄查詢狀況<p/>
	 * 更新點：新增L260M01D紀錄 / 上傳實登附件 / 刪除實登附件
	 **/
	public void setRaspStatus(String value) {
		this.raspStatus = value;
	}

	/**
	 * 取得是否已動工興建<p/>
	 * begin construction：Y/N
	 */
	public String getBegConstr() {
		return this.begConstr;
	}
	/**
	 * 設定是否已動工興建<p/>
	 * begin construction：Y/N
	 **/
	public void setBegConstr(String value) {
		this.begConstr = value;
	}

	/** 取得實際動工日 **/
	public Date getActStDate() {
		return this.actStDate;
	}
	/** 設定實際動工日 **/
	public void setActStDate(Date value) {
		this.actStDate = value;
	}

	/**
	 * 取得分項UID
	 * 
	 * @return
	 */
	public String getFrom602SUid() {
		return from602SUid;
	}

	/**
	 * 設定分項UID
	 * 
	 * @param from602sUid
	 */
	public void setFrom602SUid(String from602sUid) {
		from602SUid = from602sUid;
	}

	/**
	 * 取得分項核准時間
	 * 
	 * @return
	 */
	public Date getFrom602SApptime() {
		return from602SApptime;
	}

	/**
	 * 設定分項核准時間
	 * 
	 * @param from602sApptime
	 */
	public void setFrom602SApptime(Date from602sApptime) {
		from602SApptime = from602sApptime;
	}

	/**
	 * 取得分項序號
	 * 
	 * @return
	 */
	public BigDecimal getFrom602SSeqno() {
		return from602SSeqno;
	}

	/**
	 * 設定分項序號
	 * 
	 * @param from602sSeqno
	 */
	public void setFrom602SSeqno(BigDecimal from602sSeqno) {
		from602SSeqno = from602sSeqno;
	}

	/**
	 * 取得分項ESG 約定條件全部未達成
	 * @return
	 */
	public String getFrom602SESGsunre() {
		return from602SESGsunre;
	}

	/**
	 * 設定分項ESG 約定條件全部未達成
	 * @param from602sEsgsunre
	 */
	public void setFrom602SESGsunre(String from602sesGsunre) {
		from602SESGsunre = from602sesGsunre;
	}

}
