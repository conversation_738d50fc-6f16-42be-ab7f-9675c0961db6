/* 
 * L184M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L184M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L184M01A;


/** 企金戶新增/增額/逾放轉正名單檔 **/
@Repository
public class L184M01ADaoImpl extends LMSJpaDao<L184M01A, String> implements
		L184M01ADao {

	@Override
	public L184M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L184M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public L184M01A findByUniqueKey(Date dataDate, String branchId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L184M01A> findByIndex01(Date dataDate, String branchId) {
		ISearch search = createSearchTemplete();
		List<L184M01A> list = null;
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					dataDate);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L184M01A.class,search).getResultList();
		}
		return list;
	}

	@Override
	public L184M01A findByIndex02(String brNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", brNo);
		return findUniqueOrNone(search);
	
	}
	@Override
	public List<L184M01A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L184M01A> list = createQuery(L184M01A.class,search).getResultList();
		return list;
	}

}