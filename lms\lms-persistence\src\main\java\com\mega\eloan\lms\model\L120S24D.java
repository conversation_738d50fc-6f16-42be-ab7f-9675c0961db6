/* 
 * L120S24D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** LTV風險權數計算對照表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S24D", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S24D extends GenericBean implements IDataObject{

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 央行管制暴險 **/
	@Size(max=2)
	@Column(name="ISCBCONTROL", length=2, columnDefinition="VARCHAR(2)")
	private String isCBControl;

	/** 
	 * 分類<p/>
	 * CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N
	 */
	@Size(max=2)
	@Column(name="LTVCLASS", length=2, columnDefinition="VARCHAR(2)")
	private String LTVClass;

	/** 
	 * 類別<p/>
	 * 1=一般、2=收益<br/>
	 *  Y=符合標準、N=不符合標準
	 */
	@Size(max=2)
	@Column(name="LTVTYPE", length=2, columnDefinition="VARCHAR(2)")
	private String LTVType;

	/** 
	 * 是否合格<p/>
	 * Y=是，合格<br/>
	 *  N=否，非合格
	 */
	@Size(max=2)
	@Column(name="ISGOOD", length=2, columnDefinition="VARCHAR(2)")
	private String isGood;

	/** LTV下限 **/
	@Digits(integer=7, fraction=2, groups = Check.class)
	@Column(name="LTVLOWER", columnDefinition="DECIMAL(7,2)")
	private BigDecimal LTVLower;

	/** LTV上限 **/
	@Digits(integer=7, fraction=2, groups = Check.class)
	@Column(name="LTVUPPER", columnDefinition="DECIMAL(7,2)")
	private BigDecimal LTVUpper;

	/** RW **/
	@Digits(integer=7, fraction=2, groups = Check.class)
	@Column(name="RW", columnDefinition="DECIMAL(7,2)")
	private BigDecimal rw;

	/** 
	 * 有額外計算公式<p/>
	 * 如果有額外計算公式就不可取RW欄位值<br/>
	 *  N代表沒公式、其他不同數字則對應程式裡不同的公式RW取法
	 */
	@Size(max=2)
	@Column(name="HASFORMULA", length=2, columnDefinition="VARCHAR(2)")
	private String hasFormula;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得央行管制暴險 **/
	public String getIsCBControl() {
		return this.isCBControl;
	}
	/** 設定央行管制暴險 **/
	public void setIsCBControl(String value) {
		this.isCBControl = value;
	}

	/** 
	 * 取得分類<p/>
	 * CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N
	 */
	public String getLTVClass() {
		return this.LTVClass;
	}
	/**
	 *  設定分類<p/>
	 *  CODETYPE= LTVClass_s24a_Y,LTVClass_s24a_N
	 **/
	public void setLTVClass(String value) {
		this.LTVClass = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1=一般、2=收益<br/>
	 *  Y=符合標準、N=不符合標準
	 */
	public String getLTVType() {
		return this.LTVType;
	}
	/**
	 *  設定類別<p/>
	 *  1=一般、2=收益<br/>
	 *  Y=符合標準、N=不符合標準
	 **/
	public void setLTVType(String value) {
		this.LTVType = value;
	}

	/** 
	 * 取得是否合格<p/>
	 * Y=是，合格<br/>
	 *  N=否，非合格
	 */
	public String getIsGood() {
		return this.isGood;
	}
	/**
	 *  設定是否合格<p/>
	 *  Y=是，合格<br/>
	 *  N=否，非合格
	 **/
	public void setIsGood(String value) {
		this.isGood = value;
	}

	/** 取得LTV下限 **/
	public BigDecimal getLTVLower() {
		return this.LTVLower;
	}
	/** 設定LTV下限 **/
	public void setLTVLower(BigDecimal value) {
		this.LTVLower = value;
	}

	/** 取得LTV上限 **/
	public BigDecimal getLTVUpper() {
		return this.LTVUpper;
	}
	/** 設定LTV上限 **/
	public void setLTVUpper(BigDecimal value) {
		this.LTVUpper = value;
	}

	/** 取得RW **/
	public BigDecimal getRw() {
		return this.rw;
	}
	/** 設定RW **/
	public void setRw(BigDecimal value) {
		this.rw = value;
	}

	/** 
	 * 取得有額外計算公式<p/>
	 * 如果有額外計算公式就不可取RW欄位值<br/>
	 *  N代表沒公式、其他不同數字則對應程式裡不同的公式RW取法
	 */
	public String getHasFormula() {
		return this.hasFormula;
	}
	/**
	 *  設定有額外計算公式<p/>
	 *  如果有額外計算公式就不可取RW欄位值<br/>
	 *  N代表沒公式、其他不同數字則對應程式裡不同的公式RW取法
	 **/
	public void setHasFormula(String value) {
		this.hasFormula = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
