package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 期付金扣帳收據檔 **/
public class LNF916S extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 資料產生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF916S_PROC_DATE", columnDefinition="DATE", unique = true)
	private Date lnf916s_proc_date;

	/** 收據FORM ID **/
	@Size(max=8)
	@Column(name="LNF916S_FORMID", length=8, columnDefinition="CHAR(8)", unique = true)
	private String lnf916s_formid;

	/** 放款帳號 **/
	@Size(max=14)
	@Column(name="LNF916S_LOAN_NO", length=14, columnDefinition="CHAR(14)", unique = true)
	private String lnf916s_loan_no;

	/** 戶別 **/
	@Size(max=10)
	@Column(name="LNF916S_LOAN_SEQ", length=10, columnDefinition="CHAR(10)")
	private String lnf916s_loan_seq;

	/** 客戶ID **/
	@Size(max=11)
	@Column(name="LNF916S_CUST_ID", length=11, columnDefinition="CHAR(11)")
	private String lnf916s_cust_id;

	/** 客戶姓名 **/
	@Size(max=12)
	@Column(name="LNF916S_CUST_NAME", length=12, columnDefinition="CHAR(12)")
	private String lnf916s_cust_name;

	/** 收件人姓名 **/
	@Size(max=50)
	@Column(name="LNF916S_MAIL_NAME", length=50, columnDefinition="CHAR(50)")
	private String lnf916s_mail_name;

	/** 通訊地址1 **/
	@Size(max=40)
	@Column(name="LNF916S_ADDR1", length=40, columnDefinition="CHAR(40)")
	private String lnf916s_addr1;

	/** 通訊地址2 **/
	@Size(max=62)
	@Column(name="LNF916S_ADDR2", length=62, columnDefinition="CHAR(62)")
	private String lnf916s_addr2;

	/** 還款日期 **/
	@Size(max=7)
	@Column(name="LNF916S_RT_DATE", length=7, columnDefinition="CHAR(7)")
	private String lnf916s_rt_date;

	/** 計息起訖 **/
	@Size(max=19)
	@Column(name="LNF916S_DT_TM", length=19, columnDefinition="CHAR(19)")
	private String lnf916s_dt_tm;

	/** 償還期數 **/
	@Size(max=7)
	@Column(name="LNF916S_RT_TERM", length=7, columnDefinition="CHAR(7)", unique = true)
	private String lnf916s_rt_term;

	/** 利率 **/
	@Column(name="LNF916S_INT_RATE", columnDefinition="DECIMAL(8,6)")
	private BigDecimal lnf916s_int_rate;

	/** 期付金利息 **/
	@Column(name="LNF916S_RT_INT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_rt_int;

	/** 逾期日數 **/
	@Column(name="LNF916S_OV_DAYS", columnDefinition="DECIMAL(3,0)")
	private Integer lnf916s_ov_days;

	/** 累計應收利息 **/
	@Column(name="LNF916S_ACCU_INT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_accu_int;

	/** 利息合計 **/
	@Column(name="LNF916S_INT_TOT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_int_tot;

	/** 上期本金餘額 **/
	@Column(name="LNF916S_LST_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_lst_bal;

	/** 期付金本金 **/
	@Column(name="LNF916S_RT_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_rt_bal;

	/** 本期償還後本金餘額 **/
	@Column(name="LNF916S_CUR_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_cur_bal;

	/** 總計需繳金額 **/
	@Column(name="LNF916S_TOT_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_tot_amt;

	/** 存款帳號 **/
	@Size(max=11)
	@Column(name="LNF916S_DP_ACT_NO", length=11, columnDefinition="CHAR(11)")
	private String lnf916s_dp_act_no;

	/** 初貸金額 **/
	@Column(name="LNF916S_1ST_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf916s_1st_bal;

	/** 建商名稱 **/
	@Size(max=42)
	@Column(name="LNF916S_GRP_NAME", length=42, columnDefinition="CHAR(42)")
	private String lnf916s_grp_name;

	/** 取得資料產生日期 **/
	public Date getLnf916s_proc_date() {
		return this.lnf916s_proc_date;
	}
	/** 設定資料產生日期 **/
	public void setLnf916s_proc_date(Date value) {
		this.lnf916s_proc_date = value;
	}

	/** 取得收據FORM ID **/
	public String getLnf916s_formid() {
		return this.lnf916s_formid;
	}
	/** 設定收據FORM ID **/
	public void setLnf916s_formid(String value) {
		this.lnf916s_formid = value;
	}

	/** 取得放款帳號 **/
	public String getLnf916s_loan_no() {
		return this.lnf916s_loan_no;
	}
	/** 設定放款帳號 **/
	public void setLnf916s_loan_no(String value) {
		this.lnf916s_loan_no = value;
	}

	/** 取得戶別 **/
	public String getLnf916s_loan_seq() {
		return this.lnf916s_loan_seq;
	}
	/** 設定戶別 **/
	public void setLnf916s_loan_seq(String value) {
		this.lnf916s_loan_seq = value;
	}

	/** 取得客戶ID **/
	public String getLnf916s_cust_id() {
		return this.lnf916s_cust_id;
	}
	/** 設定客戶ID **/
	public void setLnf916s_cust_id(String value) {
		this.lnf916s_cust_id = value;
	}

	/** 取得客戶姓名 **/
	public String getLnf916s_cust_name() {
		return this.lnf916s_cust_name;
	}
	/** 設定客戶姓名 **/
	public void setLnf916s_cust_name(String value) {
		this.lnf916s_cust_name = value;
	}

	/** 取得收件人姓名 **/
	public String getLnf916s_mail_name() {
		return this.lnf916s_mail_name;
	}
	/** 設定收件人姓名 **/
	public void setLnf916s_mail_name(String value) {
		this.lnf916s_mail_name = value;
	}

	/** 取得通訊地址1 **/
	public String getLnf916s_addr1() {
		return this.lnf916s_addr1;
	}
	/** 設定通訊地址1 **/
	public void setLnf916s_addr1(String value) {
		this.lnf916s_addr1 = value;
	}

	/** 取得通訊地址2 **/
	public String getLnf916s_addr2() {
		return this.lnf916s_addr2;
	}
	/** 設定通訊地址2 **/
	public void setLnf916s_addr2(String value) {
		this.lnf916s_addr2 = value;
	}

	/** 取得還款日期 **/
	public String getLnf916s_rt_date() {
		return this.lnf916s_rt_date;
	}
	/** 設定還款日期 **/
	public void setLnf916s_rt_date(String value) {
		this.lnf916s_rt_date = value;
	}

	/** 取得計息起訖 **/
	public String getLnf916s_dt_tm() {
		return this.lnf916s_dt_tm;
	}
	/** 設定計息起訖 **/
	public void setLnf916s_dt_tm(String value) {
		this.lnf916s_dt_tm = value;
	}

	/** 取得償還期數 **/
	public String getLnf916s_rt_term() {
		return this.lnf916s_rt_term;
	}
	/** 設定償還期數 **/
	public void setLnf916s_rt_term(String value) {
		this.lnf916s_rt_term = value;
	}

	/** 取得利率 **/
	public BigDecimal getLnf916s_int_rate() {
		return this.lnf916s_int_rate;
	}
	/** 設定利率 **/
	public void setLnf916s_int_rate(BigDecimal value) {
		this.lnf916s_int_rate = value;
	}

	/** 取得期付金利息 **/
	public BigDecimal getLnf916s_rt_int() {
		return this.lnf916s_rt_int;
	}
	/** 設定期付金利息 **/
	public void setLnf916s_rt_int(BigDecimal value) {
		this.lnf916s_rt_int = value;
	}

	/** 取得逾期日數 **/
	public Integer getLnf916s_ov_days() {
		return this.lnf916s_ov_days;
	}
	/** 設定逾期日數 **/
	public void setLnf916s_ov_days(Integer value) {
		this.lnf916s_ov_days = value;
	}

	/** 取得累計應收利息 **/
	public BigDecimal getLnf916s_accu_int() {
		return this.lnf916s_accu_int;
	}
	/** 設定累計應收利息 **/
	public void setLnf916s_accu_int(BigDecimal value) {
		this.lnf916s_accu_int = value;
	}

	/** 取得利息合計 **/
	public BigDecimal getLnf916s_int_tot() {
		return this.lnf916s_int_tot;
	}
	/** 設定利息合計 **/
	public void setLnf916s_int_tot(BigDecimal value) {
		this.lnf916s_int_tot = value;
	}

	/** 取得上期本金餘額 **/
	public BigDecimal getLnf916s_lst_bal() {
		return this.lnf916s_lst_bal;
	}
	/** 設定上期本金餘額 **/
	public void setLnf916s_lst_bal(BigDecimal value) {
		this.lnf916s_lst_bal = value;
	}

	/** 取得期付金本金 **/
	public BigDecimal getLnf916s_rt_bal() {
		return this.lnf916s_rt_bal;
	}
	/** 設定期付金本金 **/
	public void setLnf916s_rt_bal(BigDecimal value) {
		this.lnf916s_rt_bal = value;
	}

	/** 取得本期償還後本金餘額 **/
	public BigDecimal getLnf916s_cur_bal() {
		return this.lnf916s_cur_bal;
	}
	/** 設定本期償還後本金餘額 **/
	public void setLnf916s_cur_bal(BigDecimal value) {
		this.lnf916s_cur_bal = value;
	}

	/** 取得總計需繳金額 **/
	public BigDecimal getLnf916s_tot_amt() {
		return this.lnf916s_tot_amt;
	}
	/** 設定總計需繳金額 **/
	public void setLnf916s_tot_amt(BigDecimal value) {
		this.lnf916s_tot_amt = value;
	}

	/** 取得存款帳號 **/
	public String getLnf916s_dp_act_no() {
		return this.lnf916s_dp_act_no;
	}
	/** 設定存款帳號 **/
	public void setLnf916s_dp_act_no(String value) {
		this.lnf916s_dp_act_no = value;
	}

	/** 取得初貸金額 **/
	public BigDecimal getLnf916s_1st_bal() {
		return this.lnf916s_1st_bal;
	}
	/** 設定初貸金額 **/
	public void setLnf916s_1st_bal(BigDecimal value) {
		this.lnf916s_1st_bal = value;
	}

	/** 取得建商名稱 **/
	public String getLnf916s_grp_name() {
		return this.lnf916s_grp_name;
	}
	/** 設定建商名稱 **/
	public void setLnf916s_grp_name(String value) {
		this.lnf916s_grp_name = value;
	}
}
