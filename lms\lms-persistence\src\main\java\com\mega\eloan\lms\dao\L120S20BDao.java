/* 
 * L120S20BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S20B;

/** 主要申請敘作內容明細檔 **/
public interface L120S20BDao extends IGenericDao<L120S20B> {

	L120S20B findByOid(String oid);

	List<L120S20B> findByMainId(String mainId);

	List<L120S20B> findByIndex01(String mainId);

	L120S20B findByIndex02(String mainId, String cntrNo);
}