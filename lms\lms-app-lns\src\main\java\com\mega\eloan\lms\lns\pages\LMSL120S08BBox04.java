package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

/**
 * <pre>
 * 利率定價合理性分析表v20220222
 * </pre>
 */
@Controller
@RequestMapping("/lms/lmsL120S08BBox04")
public class LMSL120S08BBox04 extends AbstractEloanInnerView {

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(model, parameters);
//		remove("_buttonPanel");
		// UPGRADETODO 待確認
		//add(new Label("_buttonPanel"));
	}

	private static final long serialVersionUID = 1L;

	@Override
	public void execute(ModelMap model, PageParameters params) {
//		renderJsI18N(LMS1401S03Panel.class);
		renderJsI18N(this.getClass());
		
	}
	
}
