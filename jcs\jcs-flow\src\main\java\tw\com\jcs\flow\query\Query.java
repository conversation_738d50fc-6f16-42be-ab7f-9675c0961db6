package tw.com.jcs.flow.query;

import java.util.List;

import tw.com.jcs.flow.FlowInstance;

/**
 * <pre>
 * Query
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public interface Query {

    /**
     * 依所屬使用者
     * 
     * @param id
     * @return
     */
    Query user(String id);

    /**
     * 依所屬角色
     * 
     * @param id
     * @return
     */
    Query role(String id);

    /**
     * 依所屬部門
     * 
     * @param id
     * @return
     */
    Query dept(String id);

    /**
     * 依流程定義
     * 
     * @param id
     * @return
     */
    Query definition(String id);

    /**
     * 依流程狀態
     * 
     * @param name
     * @return
     */
    Query state(String name);

    /**
     * 依流程ID
     * 
     * @param id
     * @return
     */
    Query id(Object id);

    /**
     * 包含歷程資訊
     * 
     * @return
     */
    Query all();

    /**
     * 流程為已結束流程
     * 
     * @return
     */
    Query history();

    /**
     * 進行查詢，回傳符合的所有流程實體
     * 
     * @return
     */
    List<FlowInstance> queryForList();

    /**
     * 進行查詢，回傳單一流程實體
     * 
     * @return
     */
    FlowInstance query();

}
