function hyperLink(rowName, format, rowData){
    pageAction.openPrint(rowData, $("select#rptName").val());
}

function linkToCaseRS(rowName, format, rowData){
    rowData.mainId = rowData.uid;
    pageAction.openPrint(rowData, i18n.lms9530v01["rpt.caseRS"]);
}

var selfBuild = {
    idMixName: {
        colHeader: i18n.lms9530v01["LPDFM01A.custName"], //主要借款人
        align: "center",
        width: 200, //設定寬度
        sortable: false, //是否允許排序
        name: 'lpdfm01a.custName' //col.id
    },
    authLvlMixDocCode: {
        colHeader: i18n.lms9530v01["docName"], //授權類別
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'authLvl' //col.id
    },
    reson: {
        colHeader: i18n.lms9530v01["reson"], //案由
        align: "left",
        width: 100, //設定寬度
        sortable: false, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'reson' //col.id
    },
    useBeforeConfirm: {
        colHeader: i18n.lms9530v01["isAdvance"], //先行動用
        align: "center",
        width: 100, //設定寬度
        sortable: false, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'useBeforeConfirm' //col.id
    },
    confirmFlag: {
        colHeader: i18n.lms9530v01["finishRehear"], //辦妥覆核
        align: "center",
        width: 100, //設定寬度
        sortable: false, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'confirmFlag' //col.id
    },
    mainBorrower: {
        colHeader: i18n.lms9530v01["mainBorrower"], //主要授信戶
        align: "center",
        width: 75, //設定寬度
        sortable: false, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'mainBorrower' //col.id
    },
    preRehearDate: {
        colHeader: i18n.lms9530v01["preRehearDate"], //上次覆審日期
        align: "center",
        width: 100, //設定寬度
        sortable: false, //是否允許排序
        //formatter : 'click',
        //onclick : function,
        name: 'preRehearDate' //col.id
    },
    linkCaseRS: {
        colHeader: i18n.lms9530v01["linkCaseRS"], //連結簽報書
        align: "center",
        width: 100, //設定寬度
        sortable: false, //是否允許排序
        formatter: 'click',
        onclick: linkToCaseRS,
        name: 'linkCaseRS' //col.id
    }
}
var rpt = {
    oid: {
        colHeader: "oid",
        name: 'oid',
        hidden: true //是否隱藏
    },
    mainId: {
        colHeader: i18n.lms9530v01["LPDFS01A.mainId"], //文件編號
        hidden: true,
        name: 'mainId' //col.id
    },
    rptUnid: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptUNID"], //報表文件ID
        hidden: true,
        name: 'rptUNID' //col.id
    },
    rptSeq: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptSeq"], //顯示順序
        hidden: true,
        name: 'rptSeq' //col.id
    },
    rptDisp: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptDisp"], //顯示註記
        hidden: true,
        name: 'rptDisp' //col.id
    },
    rptType: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptType"], //報表編號
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'rptType' //col.id
    },
    rptName: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptName"], //報表名稱(描述)
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'rptName' //col.id
    },
    cntrNo: {
        colHeader: i18n.lms9530v01["LPDFS01A.cntrNo"], //額度序號
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'cntrNo' //col.id
    },
    rptFile: {
        colHeader: i18n.lms9530v01["LPDFS01A.rptFile"], //報表檔檔案位置
        hidden: true,
        name: 'rptFile' //col.id
    },
    randomCode: {
        colHeader: i18n.lms9530v01["LPDFS01A.randomCode"], //報表亂碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'randomCode' //col.id
    },
    creator: {
        colHeader: i18n.lms9530v01["LPDFS01A.creator"], //建立人員號碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'creator' //col.id
    },
    createTime: {
        colHeader: i18n.lms9530v01["LPDFS01A.createTime"], //建立日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'createTime' //col.id
    },
    updater: {
        colHeader: i18n.lms9530v01["LPDFS01A.updater"], //異動人員號碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'updater' //col.id
    },
    updateTime: {
        colHeader: i18n.lms9530v01["LPDFS01A.updateTime"], //異動日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'updateTime' //col.id
    }
}

var columns = {
    oid: {
        colHeader: "oid",
        name: 'oid',
        hidden: true //是否隱藏
    },
    uid: {
        colHeader: i18n.lms9530v01["LPDFM01A.uid"], //uid
        hidden: true,
        name: 'uid' //col.id
    },
    mainId: {
        colHeader: i18n.lms9530v01["LPDFM01A.mainId"], //文件編號
        hidden: true,
        name: 'mainId' //col.id
    },
    typCd: {
        colHeader: i18n.lms9530v01["LPDFM01A.typCd"], //區部別
        hidden: true,
        name: 'typCd' //col.id
    },
    custId: {
        colHeader: i18n.lms9530v01["LPDFM01A.custId"], //統一編號
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        formatter: 'click',
        onclick: hyperLink,
        name: 'custId' //col.id
    },
    dupNo: {
        colHeader: i18n.lms9530v01["LPDFM01A.dupNo"], //重覆序號
        hidden: true,
        name: 'dupNo' //col.id
    },
    custName: {
        colHeader: i18n.lms9530v01["LPDFM01A.custName"], //客戶名稱
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        formatter: null,
        onclick: null,
        name: 'custName' //col.id
    },
    unitType: {
        colHeader: i18n.lms9530v01["LPDFM01A.unitType"], //辦理單位類別
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'unitType' //col.id
    },
    ownBrId: {
        colHeader: i18n.lms9530v01["LPDFM01A.ownBrId"], //編製單位代號
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'ownBrId' //col.id
    },
    docStatus: {
        colHeader: i18n.lms9530v01["LPDFM01A.docStatus"], //目前文件狀態
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'docStatus' //col.id
    },
    randdomCode: {
        colHeader: i18n.lms9530v01["LPDFM01A.randomCode"], //文件亂碼
        hidden: true,
        name: 'randomCode' //col.id
    },
    docUrl: {
        colHeader: i18n.lms9530v01["LPDFM01A.docURL"], //文件URL
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'docURL' //col.id
    },
    txCode: {
        colHeader: i18n.lms9530v01["LPDFM01A.txCode"], //交易代碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'txCode' //col.id
    },
    creator: {
        colHeader: i18n.lms9530v01["LPDFM01A.creator"], //建立人員號碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'creator' //col.id
    },
    createTime: {
        colHeader: i18n.lms9530v01["LPDFM01A.createTime"], //建立日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'createTime' //col.id
    },
    updater: {
        colHeader: i18n.lms9530v01["LPDFM01A.updater"], //異動人員號碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'updater' //col.id
    },
    updaterNM: {
        colHeader: i18n.lms9530v01["LPDFM01A.updaterNM"], //異動人員名稱
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'updaterNM' //col.id
    },
    updateTime: {
        colHeader: i18n.lms9530v01["LPDFM01A.updateTime"], //異動日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'updateTime' //col.id
    },
    approver: {
        colHeader: i18n.lms9530v01["LPDFM01A.approver"], //核准人員號碼
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'approver' //col.id
    },
    approverNM: {
        colHeader: i18n.lms9530v01["LPDFM01A.approverNM"], //核准人員名稱
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'approverNM' //col.id
    },
    approveTime: {
        colHeader: i18n.lms9530v01["LPDFM01A.approveTime"], //核准日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        formatter: null,
        //onclick : function,
        name: 'approveTime' //col.id
    },
    isClosed: {
        colHeader: i18n.lms9530v01["LPDFM01A.isClosed"], //是否結案
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'isClosed' //col.id
    },
    deletedTime: {
        colHeader: i18n.lms9530v01["LPDFM01A.deletedTime"], //刪除註記
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'deletedTime' //col.id
    },
    formType: {
        colHeader: i18n.lms9530v01["LPDFM01A.formType"], //報表類型
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'formType' //col.id
    },
    formName: {
        colHeader: i18n.lms9530v01["LPDFM01A.formName"], //報表名稱
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'formName' //col.id
    },
    formText: {
        colHeader: i18n.lms9530v01["LPDFM01A.formText"], //報表描述
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'formText' //col.id
    },
    caseDate: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseDate"], //日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        formatter: null,
        onclick: null,
        name: 'caseDate' //col.id
    },
    endDate: {
        colHeader: i18n.lms9530v01["LPDFM01A.endDate"], //核准(婉卻)日期
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'endDate' //col.id
    },
    docType: {
        colHeader: i18n.lms9530v01["LPDFM01A.docType"], //企/個金案件
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'docType' //col.id
    },
    docKind: {
        colHeader: i18n.lms9530v01["LPDFM01A.docKind"], //授權別
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'docKind' //col.id
    },
    docCode: {
        colHeader: i18n.lms9530v01["LPDFM01A.docCode"], //案件別
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'docCode' //col.id
    },
    caseYear: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseYear"], //案件號碼-年度
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'caseYear' //col.id
    },
    caseBrId: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseBrId"], //案件號碼-分行
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        formatter: null,
        onclick: null,
        name: 'caseBrId' //col.id
    },
    caseSeq: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseSeq"], //案件號碼-流水號
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'caseSeq' //col.id
    },
    caseNo: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseNo"], //案件號碼
        align: "center",
        width: 200, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'caseNo' //col.id
    },
    authLvl: {
        colHeader: i18n.lms9530v01["LPDFM01A.authLvl"], //授權等級
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'authLvl' //col.id
    },
    caseLvl: {
        colHeader: i18n.lms9530v01["LPDFM01A.caseLvl"], //案件審核層級
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'caseLvl' //col.id
    },
    jsonData: {
        colHeader: i18n.lms9530v01["LPDFM01A.jsonData"], //內容描述
        align: "center",
        width: 100, //設定寬度
        sortable: true, //是否允許排序
        hidden: false,
        //formatter : 'click',
        //onclick : function,
        name: 'jsonData' //col.id
    }
}
var startIndex = 7//column initial length
var pageAction = {
    handler: 'lms9530formhandler',
    grid: null,
    printGrid: null,
    column: null,
    build: function(){
        $("#buttonPanel").before('<div class=" tit2 color-black" id="searchActionName" name="searchActionName">' + DOMPurify.sanitize($("select#type").find("option:selected").text()) + ' - ' + DOMPurify.sanitize($("select#rptName").val()) + '</div>');
        pageAction.grid = $("#gridview").iGrid({
            handler: 'lms9530gridhandler',
            height: 360,
            action: "query",
            postData: {
                type: $("select#type").val(),
                rptName: $("select#rptName").val(),
                loanType: $("input[id='lonType']:checked").val(),
                dateType: $("input[id='dateRange']:checked").val(),
                bgnDate: $("input#bgnDate").val(),
                endDate: $("input#endDate").val(),
                brno: $("select#brno").val(),
                IDType: $("input[id='IDType']:checked").val(),
                ID: $("input#ID").val(),
                dupNo: $("input#dupNo").val()
            },
            search: false,
            autowidth: ($("select#rptName").val() != "消金授信覆審考核表" && $("select#rptName").val() != "覆審報告表"),
            rowNum: 15,
            rownumbers: false,
            colModel: pageAction.column,
            ondblClickRow: function(rowid){//同列印
                var data = pageAction.grid.getRowData(rowid);
                pageAction.openPrint(data, $("select#rptName").val());
            }
        });
        //build button 
        //列印
        $("#buttonPanel").find("#btnPrint").click(function(){
            var data = pageAction.grid.getSingleData();
            if (data) 
                //開窗~~
                pageAction.openPrint(data, $("select#rptName").val());
        }).end().find("#btnFilter").click(function(){
            //隱藏報表選擇
            $("#typeSelect").hide();
            //開啟畫面
            $("#searchThickBox").thickbox({
                title: i18n.lms9530v01["lms9530v01.searchTitle"],
                width: 500,
                height: 250,
                modal: true,
                align: 'center',
                valign: 'bottom',
                i18n: i18n.def,
                buttons: {
                    'sure': function(){
                        var bgnDate = $("input#bgnDate").val(), endDate = $("input#endDate").val();
                        if (bgnDate > endDate || (endDate.length != 0 && bgnDate.length == 0)) {//排除日期不符
                            CommonAPI.showErrorMessage(i18n.lms9530v01["dateError"]);//日期格式錯誤
                        }
                        else {
                            $.thickbox.close();
                            pageAction.grid.reload({
                                type: $("select#type").val(),
                                rptName: $("select#rptName").val(),
                                loanType: $("input[id='lonType']:checked").val(),
                                dateType: $("input[id='dateRange']:checked").val(),
                                bgnDate: $("input#bgnDate").val(),
                                endDate: $("input#endDate").val(),
                                brno: $("select#brno").val(),
                                IDType: $("input[id='IDType']:checked").val(),
                                ID: $("input#ID").val(),
                                dupNo: $("input#dupNo").val()
                            });
                        }
                    },
                    'close': function(){
                        $.thickbox.close();
                    }
                }
            });
        }).end().find("#btnChange").click(function(){
			changeCase2();
		}).end().find("#btnTableSend").click(function(){
        // 已核准 - 額度明細表傳送聯行
        	btnTableSend();
    	})
    },
    openPrint: function(data, rptName){
        //grid設定~~~
        columns.formType.hidden = false;
        columns.formName.hidden = false;
        columns.formText.hidden = false;
        if (pageAction.printGrid == null) {
            columns.custName.width = 300;
            pageAction.printGrid = $("#printGrid").iGrid({
                handler: 'lms9530gridhandler',
                height: 250,
                action: "queryPrint",
                postData: {
                    mainId: data.mainId
                },
                rowNum: 10,
                rownumbers: true,
                multiselect: true,
                colModel: [rpt.oid, rpt.mainId, rpt.rptUnid, rpt.rptSeq, rpt.rptFile, selfBuild.idMixName, rpt.rptType, rpt.rptName, rpt.cntrNo]
            });
        }
        else {
            pageAction.printGrid.reload({
                mainId: data.mainId,
                search: true
            });
        }
        //開啟畫面
        $("div#printThickBox").thickbox({
            title: rptName,
            width: 700,
            height: 400,
            modal: true,
            align: 'left',
            valign: 'top',
            i18n: i18n.lms9530v01,
            buttons: {
                'print': function(){//列印
                    var rptFile = pageAction.printGrid.getSelectData("rptFile");
                    if (rptFile) {
                        var rptNames = pageAction.printGrid.getSelectData("rptName");
                        var pdfName, fileNum = 0, selPDF = false, selElse = false;
                        for (var i = 0; i < rptNames.length; i++) {
                            if (rptNames[i].lastIndexOf(".") == -1) {//系統附加無副檔名=PDF
                                pdfName = rptName + ".pdf";
                                selPDF = true;
                            }
                            else 
                                if (rptNames[i].lastIndexOf("pdf") == -1) {//重新上傳&附加檔案都會有副檔名
                                    selElse = true;
                                    fileNum++;
                                }
								else{
									pdfName = rptName + ".pdf";
									selPDF=true;
								}
                            
                        }
                        if (selPDF && selElse) {//同時勾PDF&其它
                            MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.lms9530v01['differentType']);
                        }
                        else 
                            if (fileNum > 1) {//目前未提供非PDF合併功能
                                MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.lms9530v01['cantMerge']);
                            }
                            else {
                                if (selPDF) {
                                    $.capFileDownload({
                                        handler: "lmsdownloadformhandler",//  "/"會被換成url編碼.....
                                        data: {
                                            rptFile: rptFile,
                                            fileDownloadName: pdfName,
                                            serviceName: "lms9530filedownloadhandler"
                                        }
                                    });
                                }
								else{
									$.capFileDownload({
                                        handler: "lmsdownloadformhandler",//  "/"會被換成url編碼.....
                                        data: {
                                            rptFile: rptFile,
                                            fileDownloadName: rptNames[0],
                                            serviceName: "lms9530filedownloadhandler"
                                        }
                                    });
								}
                            }
                        
                    }
                },
                'chooseFile': function(){
                    var rows = pageAction.printGrid.getSelectData("oid");
                    if (rows.length == 1) {
                        MegaApi.uploadDialog({
                            handler: "lms9530fileuploadhandler",
                            width: 320,
                            height: 100,
                            fieldId: "test",
                            data: {
                                oid: rows
                            },
                            success: function(){
                                pageAction.printGrid.reload();
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
                            }
                        });
                    }
                    else {
                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.lms9530v01["pickone"]);
                    }
                },
                'delete': function(){//刪除
                    var rows = pageAction.printGrid.getGridParam('selarrrow');
                    if (rows.length) {
                        MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                            if (action) {
                                for (var i = 0; i < rows.length; i++) {
                                    var rowData = pageAction.printGrid.getRowData(rows[i]);
                                    $.ajax({
                                        handler: pageAction.handler,
                                        action: 'delete',
                                        data: rowData,
                                        success: function(responseData){
                                            if (i >= rows.length - 1) {
                                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                                                pageAction.printGrid.trigger("reloadGrid");
                                            }
                                        }
                                    });
                                }
                            }
                        });
                    }
                    else {
                        MegaApi.showErrorMessage(i18n.def['confirmTitle'], i18n.def['grid.selrow']);
                    }
                },
                'close': function(){//關閉
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 開啟視窗
     */
    openInitBox: function(data){
        $("#btnPrint").hide();
        $("#btnFilter").hide();
		$("#btnChange").hide();
        //建立select
        $("select#type").change(function(){
            $("div#subCondition").find("div").hide();
            $.ajax({
                handler: pageAction.handler,
                action: 'getItem',
                data: {
                    type: $("select#type").val()
                },
                success: function(response){
                    pageAction.refreshDiv(response);
                }
            });
        });
        $("select#rptName").change(function(){
            pageAction.refreshDiv();//切換div
        });
        $("input#loanType").change(function(){
            $.ajax({
                handler: pageAction.handler,
                action: 'getItem',
                data: {
                    type: $("select#type").val(),
                    loanType: $("#loanType:checked").val()
                },
                success: function(response){
                    pageAction.refreshDiv(response);
                }
            });
        });
        
        //開啟畫面
        $("#searchThickBox").thickbox({
            title: i18n.lms9530v01["lms9530v01.searchTitle"],
            width: 500,
            height: 350,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                'sure': function(){
                    checkID = /^[A-Z]\d{9}$/;
                    if ($('select#type').val() == 0) {//無法用vaild()只好一一判斷
                        CommonAPI.showErrorMessage(i18n.lms9530v01["typeNeedSelect"]);
                    }
                    else 
                        if ($('select#rptName').val() == 0) {
                            CommonAPI.showErrorMessage(i18n.lms9530v01["rptNameNeedSelect"]);
                        }
                        else {//兩者皆selected=>開始搜~
                            var bgnDate = $("input#bgnDate").val(), endDate = $("input#endDate").val();
                            if (bgnDate > endDate || (endDate.length != 0 && bgnDate.length == 0)) {//排除日期不符
                                CommonAPI.showErrorMessage(i18n.lms9530v01["dateError"]);//日期格式錯誤
                            }
                            else 
                                
                                    $.thickbox.close();
                                    $("#btnPrint").show();
                                    $("#btnFilter").show();
									if($("select#rptName").val() == "案件簽報書" && $("select#type").val()=="lms"){
										$("#btnChange").show();
									}
                                    pageAction.build();
                                
                        }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    },
    refreshDiv: function(data){//更新顯示的div順便控制顯示的column
        var subCondition = $("div#subCondition");
        subCondition.show();
        //reset
        subCondition.find("label").hide();
        subCondition.find("tr").hide();
        subCondition.find("#ln").show();
        pageAction.column = [columns.oid, columns.mainId, columns.uid, columns.formType, columns.formName, columns.formText, columns.randdomCode];
        columns.formType.hidden = true;
        columns.formName.hidden = true;
        columns.formText.hidden = true;
        columns.caseDate.formatoptions = null;
        columns.caseDate.formatter = null;
        
        var type = $("select#type").val();
        if (data != null) {//查詢種類變動=>reload
            $("#loan").hide();
            var rptName = {
                format: "{key}",
                item: data.rptName
            };
            var brno = {
                format: "{value} {key}",
                item: data.brno
            };
            $("select#rptName").setItems(rptName);
            subCondition.find("select#brno").setItems(brno);
            //special case
            switch (type) {
                case "cls":
                    subCondition.find("#cls").show();
                    break;
                case "rpt":
                    if (data.isBranch == "true") {
                        $("div#loan").show();
                    }
            }
        }
        else {//報表類型變動
            var rptType = $("select#rptName").val();
            if (rptType != "") {
                //special case
                //rpt、log下方div不變
                if (type == "rpt") {
                    subCondition.find("tr#brnoRow").show();
                    
                    pageAction.column[startIndex + 0] = columns.caseBrId;//分行名稱
                    columns.caseDate.colHeader = i18n.lms9530v01["data"];
                    columns.caseDate.formatoptions = {
                        srcformat: 'Y-m-d H:i:s',
                        newformat: 'Y-m'
                    };
                    columns.caseDate.formatter = 'date';
                    pageAction.column[startIndex + 1] = columns.caseDate;//資料年月
                    columns.createTime.colHeader = i18n.lms9530v01["createDate"];
                    pageAction.column[startIndex + 2] = columns.createTime;//資料產生日期
                    columns.endDate.hidden = true;
                    pageAction.column[startIndex + 3] = columns.endDate;//基準日
                }
                else 
                    if (type == "log") {
                        subCondition.find("tr#brnoRow").show();
                        subCondition.find("tr#rangeRow").show();
                        subCondition.find("label#createDate").show();
                        subCondition.find("label#standard").show();
                        subCondition.find("#dateRange[value='11']").attr("checked", true);
                        
                        pageAction.column[startIndex + 0] = columns.caseBrId;//分行名稱
                        columns.caseDate.colHeader = i18n.lms9530v01["data"];
                        columns.caseDate.formatoptions = {
                            srcformat: 'Y-m-d H:i:s',
                            newformat: 'Y-m'
                        };
                        columns.caseDate.formatter = 'date';
                        pageAction.column[startIndex + 1] = columns.caseDate;//資料年月
                        columns.createTime.colHeader = i18n.lms9530v01["createDate"];
                        pageAction.column[startIndex + 2] = columns.createTime;//資料產生日期
                        columns.endDate.hidden = true;
                        pageAction.column[startIndex + 3] = columns.endDate;//基準日
                    }
                    else {//normal
                        //依照報表類別顯示選項
                        switch (rptType) {
                            case "案件簽報書":
                                subCondition.find("tr#rangeRow").show();
                                subCondition.find("tr#IDRow").show();
                                
                                subCondition.find("label#sign").show();
                                subCondition.find("label#permit").show();
                                subCondition.find("label#refuse").show();
                                
                                subCondition.find("#dateRange[value='0']").attr("checked", true);
                                
                                columns.endDate.formatoptions = {
                                    srcformat: 'Y-m-d H:i:s',
                                    newformat: 'Y-m-d'
                                };
                                columns.endDate.formatter = 'date';
                                pageAction.column[startIndex + 0] = columns.endDate;//核准日期
                                columns.caseDate.colHeader = i18n.lms9530v01["sign"];
                                pageAction.column[startIndex + 1] = columns.caseDate;//簽案日期
                                pageAction.column[startIndex + 2] = columns.custId;//主要借款人統一編號
                                pageAction.column[startIndex + 3] = columns.custName;//主要借款人
                                columns.caseNo.colHeader = i18n.lms9530v01["caseNum"];
                                pageAction.column[startIndex + 4] = columns.caseNo;//案號
                                pageAction.column[startIndex + 5] = selfBuild.authLvlMixDocCode;//授權類別
                                if (type == "fms") {
                                    pageAction.column[startIndex + 6] = columns.typCd;//區部別
                                    pageAction.column[startIndex + 7] = columns.dupNo;//重覆序號
                                    pageAction.column[startIndex + 8] = columns.formName;//報表種類
                                    columns.jsonData.hidden = true;
                                    pageAction.column[startIndex + 9] = columns.docType;//核准(婉卻)
                                }
                                break;
                            case "審核書":
                                subCondition.find("tr#rangeRow").show();
                                subCondition.find("tr#IDRow").show();
                                
                                subCondition.find("label#get").show();
                                subCondition.find("label#permit").show();
                                subCondition.find("label#refuse").show();
                                
                                subCondition.find("#dateRange[value='1']").attr("checked", true);
                                
                                columns.endDate.colHeader = i18n.lms9530v01["permit"];
                                pageAction.column[startIndex + 0] = columns.endDate;//核准日期
                                columns.caseDate.colHeader = i18n.lms9530v01["sign"];
                                columns.caseDate.formatoptions = {
                                    srcformat: 'Y-m-d H:i:s',
                                    newformat: 'Y-m-d'
                                };
                                columns.caseDate.formatter = 'date';
                                pageAction.column[startIndex + 1] = columns.caseDate;//簽案日期
                                pageAction.column[startIndex + 2] = columns.custId;//主要借款人統一編號
                                pageAction.column[startIndex + 3] = columns.custName;//主要借款人
                                columns.caseNo.colHeader = i18n.lms9530v01["caseNum"];
                                pageAction.column[startIndex + 4] = columns.caseNo;//案號
                                pageAction.column[startIndex + 5] = selfBuild.linkCaseRS;//連結至簽報書
                                //收件=endDate
                                break;
                            case "小放會會議記錄":
                                subCondition.find("tr#rangeRow").show();
                                subCondition.find("#ln").hide();
                                
                                columns.caseDate.colHeader = i18n.lms9530v01["metting"];
                                columns.caseDate.formatter = 'click';
                                columns.caseDate.onclick = hyperLink;
                                columns.caseDate.width = 20;
                                pageAction.column[startIndex + 0] = columns.caseDate;//會議日期
                                pageAction.column[startIndex + 1] = selfBuild.reson;//案由
                                break;
                            case "政策性留學生貸款報送項目":
                                subCondition.find("tr#rangeRow").show();
                                subCondition.find("#ln").hide();
                                
                                pageAction.column[startIndex + 0] = columns.createTime;//建立日期
                                columns.caseBrId.colHeader = i18n.lms9530v01["LPDFM01A.caseBrId"];
                                pageAction.column[startIndex + 1] = columns.caseBrId;//分行代號
                                columns.caseDate.colHeader = i18n.lms9530v01["beginDate"];
                                pageAction.column[startIndex + 2] = columns.caseDate;//資料起日
                                columns.endDate.colHeader = i18n.lms9530v01["endDate"];
                                pageAction.column[startIndex + 3] = columns.endDate;//資料迄日
                                break;
                            case "動用審核表":
                                subCondition.find("tr#rangeRow").show();
                                subCondition.find("tr#IDRow").show();
                                
                                subCondition.find("label#approved").show();
                                subCondition.find("label#finish").show();
                                
                                subCondition.find("#dateRange[value='4']").attr("checked", true);
                                
                                columns.caseDate.colHeader = i18n.lms9530v01["approved"];
                                pageAction.column[startIndex + 0] = columns.caseDate;//核定日期
                                pageAction.column[startIndex + 1] = columns.custId;//主要借款人統一編號
                                pageAction.column[startIndex + 2] = columns.custName;//主要借款人
                                columns.caseNo.colHeader = i18n.lms9530v01["caseNum"];
                                pageAction.column[startIndex + 3] = columns.caseNo;//案號
                                //辦妥覆核+先行動用在jsonData
                                pageAction.column[startIndex + 4] = selfBuild.useBeforeConfirm;//先行動用
                                columns.endDate.colHeader = i18n.lms9530v01["permit"];
                                pageAction.column[startIndex + 5] = columns.endDate;//辦妥日期
                                pageAction.column[startIndex + 6] = selfBuild.confirmFlag;//辦妥覆核
                                pageAction.column[startIndex + 7] = columns.typCd;//區部別
                                break;
                            case "覆審工作底稿":
                            case "覆審名單":
                                subCondition.find("tr#brnoRow").show();
                                subCondition.find("tr#rangeRow").show();
                                
                                subCondition.find("label#data").show();
                                subCondition.find("label#estimate").show();
                                
                                subCondition.find("#dateRange[value='7']").attr("checked", true);
                                
                                columns.caseDate.formatoptions = {
                                    srcformat: 'Y-m-d H:i:s',
                                    newformat: 'Y-m'
                                };
                                columns.caseDate.formatter = 'date';
                                pageAction.column[startIndex + 0] = columns.caseDate;//資料年月
                                columns.caseBrId.formatter = 'click';
                                columns.caseBrId.onclick = hyperLink;
                                pageAction.column[startIndex + 1] = columns.caseBrId;//分行名稱
                                pageAction.column[startIndex + 2] = columns.createTime;//名單產生日期
                                columns.caseNo.colHeader = i18n.lms9530v01["batchNum"];
                                pageAction.column[startIndex + 3] = columns.caseNo;//覆審批號
                                columns.endDate.colHeader = i18n.lms9530v01["estimate"];
                                pageAction.column[startIndex + 4] = columns.endDate;//預計覆審日
                                break;
                            case "消金授信覆審考核表":
                            case "覆審報告表":
                                subCondition.find("tr").show();//全顯示
                                subCondition.find("label#fact").show();
                                subCondition.find("label#upload").show();
                                
                                subCondition.find("#dateRange[value='9']").attr("checked", true);
                                
                                columns.caseDate.colHeader = i18n.lms9530v01["rehearDate"];
                                pageAction.column[startIndex + 0] = columns.caseDate;//覆審日期
                                columns.caseNo.colHeader = i18n.lms9530v01["rehearNum"];
                                columns.caseNo.width = 230;
                                pageAction.column[startIndex + 1] = columns.caseNo;//覆審案號
                                columns.custId.width = 130;
                                pageAction.column[startIndex + 2] = columns.custId;//統一編號
                                pageAction.column[startIndex + 3] = columns.custName;//主要借款人
                                pageAction.column[startIndex + 4] = selfBuild.mainBorrower;//主要授信戶
                                pageAction.column[startIndex + 5] = selfBuild.preRehearDate;//上次複審日期
                                columns.approver.hidden = true;
                                pageAction.column[startIndex + 6] = columns.approver;//覆審人員
                                columns.approverNM.hidden = true;
                                pageAction.column[startIndex + 7] = columns.approverNM;//覆審人員姓名
                        }
                        if (type == "log") {//只重新設定篩選畫面
                            subCondition.find("label").hide();
                            subCondition.find("tr").hide();
                            subCondition.find("#ln").show();
                            
                            subCondition.find("tr#brnoRow").show();
                            subCondition.find("tr#rangeRow").show();
                            
                            subCondition.find("label#createDate").show();
                            subCondition.find("label#standard").show();
                            
                            subCondition.find("#dateRange[value='11']").attr("checked", true);
                            
                        }
                        subCondition.find("select#brno").setItems(brno);
                    }
            }
            
        }
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        }
        else {
            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    },
    /**
     * 檔案下載
     */
    download: function(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }
}

//條件變更/續約
function changeCase2(){
	showType();
	var $LMS1205V01Form = $("#LMS1205V01Form");
    var row = $("#gridview").getGridParam('selrow');
    var list = "";
    var data = $("#gridview").getRowData(row);
    list = data.oid;
    list = (list == undefined ? "" : list);
    if (list != "") {
        $.ajax({
            type: "POST",
            handler: "lms1201formhandler",
            data: {
                formAction: "getCaseFormate",
                oid: list
            },
            success: function(responseData){
                //alert(JSON.stringify(responseData));
                $("#LMS1205V01Form").reset();
                $("#LMS1205V01Form").find("[name='docType']").each(function(i){
                    if ($(this).val() == responseData.LMS1205V01Form.docType) {
                        $(this).attr("checked", true);
                    }
                });
                $("#LMS1205V01Form").find("[name='docKind']").each(function(j){
                    if ($(this).val() == responseData.LMS1205V01Form.docKind) {
                        $(this).attr("checked", true);
                    }
                });
                $("#LMS1205V01Form").find("[name='docCode']").each(function(k){
                    if ($(this).val() == responseData.LMS1205V01Form.docCode) {
                        $(this).attr("checked", true);
                    }
                });
                
                $LMS1205V01Form.find("[name='ngFlag']").each(function(k){
					
                    if ($(this).val() == responseData.LMS1205V01Form.ngFlag) {
                        $(this).attr("checked", true);
                    }
                    //$(this).attr("disabled", true);
                });
				
				if(!responseData.LMS1205V01Form.ngFlag){
					 $LMS1205V01Form.find("[name='ngFlag'][value='0']").attr("checked", true);
				}
                
                var openThickbox = $("#LMS1205V01Thickbox").thickbox({ // 使用選取的內容進行彈窗
                    title: i18n.lms1201v01["l120v01.thickbox13"],
                    width: 700,
                    height: 340,
                    align: 'center',
                    valign: 'bottom',
                    modal: true,
                    i18n: i18n.lms1201v01,
                    buttons: {
                        "l120v01.thickbox1": function(showMsg){
                            if ($("#LMS1205V01Form").find("[name='docType']:checked").val() != undefined &&
                            $("#LMS1205V01Form").find("[name='docKind']:checked").val() != undefined &&
                            $("#LMS1205V01Form").find("[name='docCode']:checked").val() != undefined &&
                            $LMS1205V01Form.find("[name='ngFlag']:checked").val() != undefined) {
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1201formhandler",
                                    data: {
                                        formAction: "changeIf",
                                        oid: list
                                    },
                                    success: function(responseData){
                                        // alert(JSON.stringify(responseData));
                                        $("#gridview").trigger("reloadGrid"); // 更新Grid內容
                                        $.thickbox.close();
                                        CommonAPI.showMessage(responseData.runSuccess);
                                    }
                                });
                            } else {
                                CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert2"]);
                            }
                        },
                        "l120v01.thickbox2": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
            }
        });
    } else {
        CommonAPI.showMessage(i18n.lms1201v01["l120v01.alert1"]);
    }
}

function showType(){
    $.ajax({
        type: "POST",
		async: false,
        handler: "lms1201formhandler",
        data: {
            formAction: "getType"
        },
        success: function(responseData){
			$("#LMS1205V01Form").find("#"+responseData.needToShow).show();
        }			
    });	
}

// 額度明細表傳送聯行
function btnTableSend(){
    var id = $("#gridview").getGridParam('selrow');
    if (!id) {
        // action_005=請先選取一筆以上之資料列
        return CommonAPI.showMessage(i18n.def["action_005"]);
    }
    
    $.ajax({
        handler: "lms1411m01formhandler",
        data: {
            formAction: "queryBrankList"
        },
        success: function(obj){
            $("#brankSelect").setItems({
                item: obj,
                format: "{value} {key}",
                space: false
            });
        }
    });
    
    var data = $("#gridview").getRowData(id);
    $("#tableSendBox").thickbox({
        // l120v01.thickbox11=額度明細表傳送聯行
        title: i18n.lms1201v01["l120v01.thickbox11"],
        width: 460,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $.ajax({
                    handler: "lms1411m01formhandler",
                    data: {
                        formAction: "copyCase",
                        brank: $("#brankSelect").val(),
                        oid: data.oid
                    },
                    success: function(obj){
                        $("#gridview").trigger("reloadGrid");// 更新Grid內容
                    }
                });
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}


$(function(){

    pageAction.openInitBox();
});
