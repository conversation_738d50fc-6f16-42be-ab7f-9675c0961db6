package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS2701V01Page;
import com.mega.eloan.lms.fms.service.CLS2701Service;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C900M01I;
import com.mega.eloan.lms.model.C900S01I;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls2701gridhandler")
public class CLS2701GridHandler extends AbstractGridHandler {

	@Resource
	CLS2701Service cls2701Service;

	@Resource
	CLSService clsService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	BranchService branchService;
	
	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS2701V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryC900M01I(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String docStatus = Util.trim(params.getString("docStatus"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		
		if(Util.equals(user.getUnitNo(), "900")){
			
		}else{
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					user.getUnitNo());	
		}
		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);

		if(true){
			String custId = Util.trim(params.getString("custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
						custId);
			}	
		}
		
		if(true){
			String orgCustId = Util.trim(params.getString("orgCustId"));
			if (Util.isNotEmpty(orgCustId)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "orgCustId",
						orgCustId);
			}	
		}		

		Page<? extends GenericBean> page = cls2701Service.findPage(C900M01I.class,
				pageSetting);
//		List<C900M01I> list = (List<C900M01I>) page.getContent();
//		for (C900M01I model : list) {
//			
//		}
		
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}
	
	public CapGridResult queryC900S01I(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String docStatus = Util.trim(params.getString("docStatus"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
					user.getUnitNo());	
		
		if(true){
			String custId = Util.trim(params.getString("search_custId"));
			if (Util.isNotEmpty(custId)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custId",
						custId+"%");
			}	
		}
		
		if(true){
			String orgCustId = Util.trim(params.getString("search_orgCustId"));
			if (Util.isNotEmpty(orgCustId)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "orgCustId",
						orgCustId+"%");
			}	
		}		

		Page<? extends GenericBean> page = cls2701Service.findPage(C900S01I.class,
				pageSetting);
//		List<C900M01I> list = (List<C900M01I>) page.getContent();
//		for (C900M01I model : list) {
//			
//		}
		
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));
		map.put("approver", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}
	
	/**
	 * 參照  CLS1141GridHandler :: queryL120m01a(...)
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String c900m01i_mainId = Util.trim(params.getString("c900m01i_mainId"));
		C900M01I c900m01i = cls2701Service.findC900M01I_mainId(c900m01i_mainId);
		List<String> c120_mainId_list = new ArrayList<String>();
		if(c900m01i!=null){
			String orgCustId = Util.trim(c900m01i.getOrgCustId());
			String orgDupNo = Util.trim(c900m01i.getOrgDupNo());
			
			if(true){
				ISearch subSearch = clsService.getMetaSearch();
				subSearch.addSearchModeParameters(SearchMode.EQUALS, "custId", orgCustId);
				subSearch.addSearchModeParameters(SearchMode.EQUALS, "dupNo", orgDupNo);
				subSearch.setMaxResults(Integer.MAX_VALUE);
				Page<? extends GenericBean> subPage = clsService.findPage(C120M01A.class,
						subSearch);
				List<C120M01A> list = (List<C120M01A>) subPage.getContent();
				for (C120M01A model : list) {
					c120_mainId_list.add(model.getMainId());
				}
			}
		}		
		
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS,
				"docCode", UtilConstants.Casedoc.DocCode.陳復陳述案);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
					"docStatus", CreditDocStatusEnum.海外_已核准.getCode());		
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
//		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
//			UtilConstants.Casedoc.DocType.個金);
		pageSetting.addSearchModeParameters(SearchMode.IN,
				"mainId", c120_mainId_list);
		

		Page<? extends GenericBean> page = cls2701Service.findPage(L120M01A.class,
				pageSetting);
		List<L120M01A> list = (List<L120M01A>) page.getContent();
		for (L120M01A model : list) {
			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));	
		}
		
		Map<String, IFormatter> map = new HashMap<String, IFormatter>();
		map.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.ID_Name));
		map.put("updater", new UserNameFormatter(userInfoService));
		map.put("approver", new UserNameFormatter(userInfoService));
		return new CapGridResult(page.getContent(), page.getTotalRow(), map);
	}
}
