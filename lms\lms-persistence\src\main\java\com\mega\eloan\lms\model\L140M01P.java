/* 
 * L140M01P.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 敘做條件異動比較表 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140M01P", uniqueConstraints = @UniqueConstraint(columnNames = { "mainId" }))
public class L140M01P extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/**
	 * mainId
	 * <p/>
	 * 額度明細表mainId
	 */
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 序號
	 * <p/>
	 * 排序用
	 */
	@Digits(integer = 5, fraction = 0, groups = Check.class)
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 變更前額度序號 **/
	@Column(name = "BFCNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String bfcntrNo;

	/** 變更後額度序號 **/
	@Column(name = "AFCNTRNO", length = 12, columnDefinition = "VARCHAR(12)")
	private String afcntrNo;

	/** 項目 **/
	@Column(name = "ITEMNAME", length = 100, columnDefinition = "VARCHAR(100)")
	private String itemName;

	/**
	 * 前准敘做條件
	 * <p/>
	 * CLOB
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "BFITEMDRC", columnDefinition = "CLOB")
	private String bfItemDrc;

	/**
	 * 本次申請敘做條件
	 * <p/>
	 * CLOB
	 */
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "AFITEMDRC", columnDefinition = "CLOB")
	private String afItemDrc;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 取得mainId
	 * <p/>
	 * 額度明細表mainId
	 */
	public String getMainId() {
		return this.mainId;
	}

	/**
	 * 設定mainId
	 * <p/>
	 * 額度明細表mainId
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得序號
	 * <p/>
	 * 排序用
	 */
	public Integer getSeq() {
		return this.seq;
	}

	/**
	 * 設定序號
	 * <p/>
	 * 排序用
	 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 取得變更前額度序號 **/
	public String getBfcntrNo() {
		return this.bfcntrNo;
	}

	/** 設定變更前額度序號 **/
	public void setBfcntrNo(String value) {
		this.bfcntrNo = value;
	}

	/** 取得變更後額度序號 **/
	public String getAfcntrNo() {
		return this.afcntrNo;
	}

	/** 設定變更後額度序號 **/
	public void setAfcntrNo(String value) {
		this.afcntrNo = value;
	}

	/** 取得項目 **/
	public String getItemName() {
		return this.itemName;
	}

	/** 設定項目 **/
	public void setItemName(String value) {
		this.itemName = value;
	}

	/**
	 * 取得前准敘做條件
	 * <p/>
	 * CLOB
	 */
	public String getBfItemDrc() {
		return this.bfItemDrc;
	}

	/**
	 * 設定前准敘做條件
	 * <p/>
	 * CLOB
	 **/
	public void setBfItemDrc(String value) {
		this.bfItemDrc = value;
	}

	/**
	 * 取得本次申請敘做條件
	 * <p/>
	 * CLOB
	 */
	public String getAfItemDrc() {
		return this.afItemDrc;
	}

	/**
	 * 設定本次申請敘做條件
	 * <p/>
	 * CLOB
	 **/
	public void setAfItemDrc(String value) {
		this.afItemDrc = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
