/* 
 * LMS1605ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.OBSMqGwClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.ICustomerService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01BDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L120S01DDao;
import com.mega.eloan.lms.dao.L120S21BDao;
import com.mega.eloan.lms.dao.L120S21CDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01E_AFDao;
import com.mega.eloan.lms.dao.L140M01JDao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L160A01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L160M01BDao;
import com.mega.eloan.lms.dao.L160M01CDao;
import com.mega.eloan.lms.dao.L160M01DDao;
import com.mega.eloan.lms.dao.L161S01ADao;
import com.mega.eloan.lms.dao.L161S01BDao;
import com.mega.eloan.lms.dao.L161S01CDao;
import com.mega.eloan.lms.dao.L162S01ADao;
import com.mega.eloan.lms.dao.L163S01ADao;
import com.mega.eloan.lms.dao.L164S01ADao;
import com.mega.eloan.lms.dao.L901M01ADao;
import com.mega.eloan.lms.dao.VLUSEDOC01Dao;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.service.MISSEFService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELCRTBLService;
import com.mega.eloan.lms.mfaloan.service.MisELF603Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisIquotappService;
import com.mega.eloan.lms.mfaloan.service.MisIquotgurService;
import com.mega.eloan.lms.mfaloan.service.MisIquotjonService;
import com.mega.eloan.lms.mfaloan.service.MisLNF164Service;
import com.mega.eloan.lms.mfaloan.service.MisQuotainfService;
import com.mega.eloan.lms.mfaloan.service.MisQuotapprService;
import com.mega.eloan.lms.mfaloan.service.MisQuotsubService;
import com.mega.eloan.lms.mfaloan.service.MisQuotunioService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01B;
import com.mega.eloan.lms.model.L120M01F;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L120S01D;
import com.mega.eloan.lms.model.L120S01P;
import com.mega.eloan.lms.model.L120S21B;
import com.mega.eloan.lms.model.L120S21C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140M01C;
import com.mega.eloan.lms.model.L140M01D;
import com.mega.eloan.lms.model.L140M01E_AF;
import com.mega.eloan.lms.model.L140M01F;
import com.mega.eloan.lms.model.L140M01G;
import com.mega.eloan.lms.model.L140M01H;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L160A01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L160M01B;
import com.mega.eloan.lms.model.L160M01C;
import com.mega.eloan.lms.model.L160M01D;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.eloan.lms.model.L161S01C;
import com.mega.eloan.lms.model.L162S01A;
import com.mega.eloan.lms.model.L163S01A;
import com.mega.eloan.lms.model.L164S01A;
import com.mega.eloan.lms.model.L901M01A;
import com.mega.eloan.lms.model.VLUSEDOC01;
import com.mega.eloan.lms.obsdb.service.ObsdbELF164Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF383Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF384Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF385Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF388Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF401Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF422Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF476Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF601Service;
import com.mega.eloan.lms.obsdb.service.ObsdbELF603Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 動用審核表 LMS1605ServiceImpl
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          <li>2013/07/03,Rex,同業聯貸為Y才需上傳同業聯貸
 *          </ul>
 */
@Service
public class LMS1605ServiceImpl extends AbstractCapService implements
		LMS1605Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS1605ServiceImpl.class);
	@Resource
	FlowNameService flowNameService;

	@Resource
	TempDataService tempDataService;
	@Resource
	OBSMqGwClient obsMqGwClient;

	@Resource
	FlowService flowService;
	
	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01BDao l120m01bDao;
	
	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	L120S01ADao l120s01aDao;

	@Resource
	L120S01BDao l120s01bDao;

	@Resource
	L120S01CDao l120s01cDao;

	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	L120S01DDao l120s01dDao;
	@Resource
	C120S01EDao c120s01eDao;

	@Resource
	L140M01ADao l140m01aDao;
	
	@Resource
	L140M01E_AFDao l140m01e_efDao;
	
	@Resource
	L160A01ADao l160A01aDao;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L160M01BDao l160m01bDao;

	@Resource
	L160M01CDao l160m01cDao;

	@Resource
	L160M01DDao l160m01dDao;

	@Resource
	L161S01ADao l161m01aDao;

	@Resource
	L161S01BDao l161m01bDao;

	@Resource
	L161S01CDao l161s01cDao;

	@Resource
	L162S01ADao l162s01aDao;

	@Resource
	L163S01ADao l163s01aDao;

	@Resource
	L901M01ADao l901m01aDao;

	@Resource
	VLUSEDOC01Dao vusedoc01Dao;

	@Resource
	DocFileDao docFileDao;

	@Resource
	DocLogService docLogService;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisIquotjonService misIquotjonService;

	@Resource
	MisIquotgurService misIquotgurService;

	@Resource
	MisIquotappService misIquotappService;

	@Resource
	MisQuotainfService misQuotainfService;

	@Resource
	MisQuotunioService misQuotunioService;

	@Resource
	MisQuotapprService misQuotapprService;

	@Resource
	MisQuotsubService misQuotsubService;

	@Resource
	MisLNF164Service misLNF164Service;
	@Resource
	MisELCRTBLService misELCRTBLService;
	@Resource
	ObsdbELF164Service obsdbELF164Service;

	@Resource
	ObsdbELF383Service obsdbELF383Service;
	@Resource
	ObsdbELF384Service obsdbELF384Service;
	@Resource
	ObsdbELF385Service obsdbELF385Service;
	@Resource
	ObsdbELF388Service obsdbELF388Service;
	@Resource
	ObsdbELF401Service obsdbELF401Service;
	@Resource
	ObsdbELF422Service obsdbELF422Service;
	@Resource
	ObsdbELF476Service obsdbELF476Service;
	@Resource
	ObsdbELF601Service obsdbELF601Service;
	@Resource
	ObsdbELF603Service obsdbELF603Service;
	@Resource
	MISSEFService missefService;
	@Resource
	LMSService lmsService;
	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	L140M01JDao l140m01jDao;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	CLSService clsService;

	// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	@Resource
	L164S01ADao l164s01aDao;

	@Resource
	AMLRelateService amlRelateService;

	// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
	@Resource
	ICustomerService customerSrv;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisStoredProcService msps;
	
	@Resource
	L120S21BDao l120s21bDao;

	@Resource
	L120S21CDao l120s21cDao;

	@Resource
	L140M01ODao l140m01oDao;
	
	@Resource
	MisELF603Service misELF603Service;

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L160M01A.class) {
			return l160m01aDao.findByMainIds(mainId);
		} else if (clazz == L160M01B.class) {
			return l160m01bDao.findByMainId(mainId);
		} else if (clazz == L160M01C.class) {
			return l160m01cDao.findByMainId(mainId);
		} else if (clazz == L160M01D.class) {
			return l160m01dDao.findByMainId(mainId);
		} else if (clazz == L161S01A.class) {
			return l161m01aDao.findByMainId(mainId);
		} else if (clazz == L161S01B.class) {
			return l161m01bDao.findByMainId(mainId);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findByMainId(mainId);
		} else if (clazz == L163S01A.class) {
			return l163s01aDao.findByMainId(mainId);
		} else if (clazz == L164S01A.class) {
			return l164s01aDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L160M01A) {
					if (Util.isEmpty(((L160M01A) model).getOid())) {
						((L160M01A) model).setCreator(user.getUserId());
						((L160M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
						l160m01aDao.save((L160M01A) model);
						flowService.start("LMS1605Flow",
								((L160M01A) model).getOid(), user.getUserId(),
								user.getUnitNo());
						// 新增授權檔
						L160A01A l160a01a = new L160A01A();
						l160a01a.setAuthTime(CapDate.getCurrentTimestamp());
						l160a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
						l160a01a.setAuthUnit(user.getUnitNo());
						l160a01a.setMainId(((L160M01A) model).getMainId());
						l160a01a.setOwner(user.getUserId());
						l160a01a.setOwnUnit(user.getUnitNo());
						l160A01aDao.save(l160a01a);

					} else {
						// 當文件狀態為編製中時文件亂碼才變更

						((L160M01A) model).setUpdater(user.getUserId());
						((L160M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						l160m01aDao.save((L160M01A) model);
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L160M01A) model)
									.getMainId());
							docLogService.record(((L160M01A) model).getOid(),
									DocLogEnum.SAVE);
						}

					}

				} else if (model instanceof L160M01B) {
					((L160M01B) model).setUpdater(user.getUserId());
					((L160M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l160m01bDao.save((L160M01B) model);
				} else if (model instanceof L161S01A) {
					((L161S01A) model).setUpdater(user.getUserId());
					((L161S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l161m01aDao.save((L161S01A) model);

				} else if (model instanceof L161S01B) {
					((L161S01B) model).setUpdater(user.getUserId());
					((L161S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l161m01bDao.save((L161S01B) model);
				} else if (model instanceof L162S01A) {
					((L162S01A) model).setUpdater(user.getUserId());
					((L162S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l162s01aDao.save((L162S01A) model);
				} else if (model instanceof L163S01A) {
					((L163S01A) model).setUpdater(user.getUserId());
					((L163S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l163s01aDao.save((L163S01A) model);
				} else if (model instanceof L160M01D) {
					((L160M01D) model).setUpdater(user.getUserId());
					((L160M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l160m01dDao.save((L160M01D) model);
				} else if (model instanceof L164S01A) {
					// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
					l164s01aDao.save((L164S01A) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public boolean deleteListByOid(Class clazz, String[] oids) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		boolean flag = false;
		if (clazz == L160M01A.class) {
			// 不做直接刪除
			List<L160M01A> l160m01as = l160m01aDao.findByOids(oids);
			for (L160M01A l160m01a : l160m01as) {
				l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				l160m01a.setUpdater(user.getUserId());
				docLogService.record(l160m01a.getOid(), DocLogEnum.DELETE);
			}
			l160m01aDao.save(l160m01as);
			return true;
		} else if (clazz == L161S01B.class) {
			List<L161S01B> l161m01bs = l161m01bDao.findByOid(oids);
			l161m01bDao.delete(l161m01bs);
			return true;
		} else if (clazz == L162S01A.class) {
			List<L162S01A> l162m01as = l162s01aDao.findByOid(oids);
			l162s01aDao.delete(l162m01as);
			return true;
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			List<L164S01A> l164s01as = l164s01aDao.findByOid(oids);
			l164s01aDao.delete(l164s01as);
			return true;
		}
		return flag;
	}

	@Override
	public void delete(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L160M01A) {
					((L160M01A) model).setDeletedTime(CapDate
							.getCurrentTimestamp());
					((L160M01A) model).setUpdater(user.getUserId());
					docLogService.record(((L160M01A) model).getOid(),
							DocLogEnum.DELETE);
					l160m01aDao.save((L160M01A) model);
				} else if (model instanceof L160M01B) {
					l160m01bDao.delete((L160M01B) model);
				} else if (model instanceof L161S01A) {
					l161m01aDao.delete((L161S01A) model);
				} else if (model instanceof L161S01B) {
					l161m01bDao.delete((L161S01B) model);
				} else if (model instanceof L162S01A) {
					l162s01aDao.delete((L162S01A) model);
				} else if (model instanceof L163S01A) {
					l163s01aDao.delete((L163S01A) model);
				} else if (model instanceof L160M01D) {
					l160m01dDao.delete((L160M01D) model);
				} else if (model instanceof L164S01A) {
					// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
					l164s01aDao.delete((L164S01A) model);
				}
			}

		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L160M01A.class) {
			return l160m01aDao.findPage(search);
		} else if (clazz == L160M01B.class) {
			return l160m01bDao.findPage(search);
		} else if (clazz == L160M01C.class) {
			return l160m01cDao.findPage(search);
		} else if (clazz == L161S01A.class) {
			return l161m01aDao.findPage(search);
		} else if (clazz == L161S01B.class) {
			return l161m01bDao.findPage(search);
		} else if (clazz == L162S01A.class) {
			return l162s01aDao.findPage(search);
		} else if (clazz == VLUSEDOC01.class) {
			return vusedoc01Dao.findPage(search);
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			return l164s01aDao.findPage(search);
		} else if (clazz == L140M01E_AF.class) {
			return l140m01e_efDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L160M01A.class) {
			return (T) l160m01aDao.findByOid(oid);
		} else if (clazz == L160M01B.class) {
			return (T) l160m01bDao.findByOid(oid);
		} else if (clazz == L161S01A.class) {
			return (T) l161m01aDao.findByOid(oid);
		} else if (clazz == L161S01B.class) {
			return (T) l161m01bDao.findByOid(oid);
		} else if (clazz == L162S01A.class) {
			return (T) l162s01aDao.findByOid(oid);
		} else if (clazz == L163S01A.class) {
			return (T) l163s01aDao.findByOid(oid);
		} else if (clazz == L164S01A.class) {
			// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
			return (T) l164s01aDao.findByOid(oid);
		}
		return null;
	}

	@Override
	public boolean deleteL160m01as(String[] oids) {
		boolean flag = false;
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<L160M01A> l160m01as = new ArrayList<L160M01A>();
		for (int i = 0, size = oids.length; i < size; i++) {
			L160M01A l160m01a = (L160M01A) findModelByOid(L160M01A.class,
					oids[i]);
			// 設定刪除並非直接刪除 ，只是標記刪除時間
			l160m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			l160m01a.setUpdater(user.getUserId());
			l160m01as.add(l160m01a);
			docLogService.record(l160m01a.getOid(), DocLogEnum.DELETE);
		}

		if (!l160m01as.isEmpty()) {
			l160m01aDao.save(l160m01as);
			flag = true;
		}

		return flag;
	}

	@Override
	public void deleteL160m01bs(List<L160M01B> l160m01bs) {
		l160m01bDao.delete(l160m01bs);
	}

	@Override
	public void deleteL160m01ds(List<L160M01D> l160m01ds, boolean isAll) {
		if (isAll) {
			l160m01dDao.delete(l160m01ds);
		} else {
			List<L160M01D> L160M01DsOld = new ArrayList<L160M01D>();
			for (L160M01D l160m01d : l160m01ds) {
				String staffJob = l160m01d.getStaffJob();
				if (!("L6".equals(staffJob) || "L7".equals(staffJob))) {
					L160M01DsOld.add(l160m01d);
				}
			}
			l160m01dDao.delete(L160M01DsOld);
		}

	}

	@Override
	public void saveL160m01bList(List<L160M01B> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01B l160m01b : list) {
			l160m01b.setUpdater(user.getUserId());
			l160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01bDao.save(list);
	}

	@Override
	public void saveL162m01aList(List<L162S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L162S01A l162m01a : list) {
			l162m01a.setUpdater(user.getUserId());
			l162m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l162s01aDao.save(list);
	}

	@Override
	public void saveL160m01dList(List<L160M01D> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01D l160m01d : list) {
			l160m01d.setUpdater(user.getUserId());
			l160m01d.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01dDao.save(list);
	}

	@Override
	public void saveL160m01cList(List<L160M01C> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L160M01C l160m01c : list) {
			l160m01c.setUpdater(user.getUserId());
			l160m01c.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l160m01cDao.save(list);
	}

	@Override
	public List<L901M01A> findL901m01aByBranchIdAndItemType(String itemType,
			String branchId, String locale) {
		return l901m01aDao
				.findByItemTypeAndbranchId(itemType, branchId, locale);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteL160m01cs(java.util
	 * .List)
	 */
	@Override
	public void deleteL160m01cs(List<L160M01C> l160m01cs) {
		l160m01cDao.delete(l160m01cs);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteUploadFile(java.lang
	 * .String[])
	 */
	@Override
	public void deleteUploadFile(String[] oids) {
		List<DocFile> docfiles = docFileDao.findAllByOid(oids);
		for (DocFile docfile : docfiles) {
			docfile.setDeletedTime(CapDate.getCurrentTimestamp());
		}
		docFileDao.save(docfiles);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL163m01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L163S01A findL163m01aByMainId(String mainId) {
		return l163s01aDao.findByUniqueKey(mainId);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainId(java
	 * .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainId(String mainId, String cntrNo) {
		return l161m01aDao.findByUniqueKey(mainId, cntrNo);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#deleteL162m01as(java.util
	 * .List)
	 */
	@Override
	public void deleteL162m01as(List<L162S01A> l162m01as) {
		l162s01aDao.delete(l162m01as);

	}

	@Override
	public int findL161m01bMaxSeqOLD(String mainId) {
		List<L161S01B> l161m01bs = l161m01bDao.findByMainId(mainId);
		int count = 0;
		int seqval = 0;
		for (L161S01B l161m01b : l161m01bs) {// 取出這個mainID底下的最大Seq
			seqval = l161m01b.getSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@Override
	public int findL161m01bMaxSeq(String mainId, String pid) {
		List<L161S01B> l161m01bs = l161m01bDao.findByMainIdUid(mainId, pid);
		int count = 0;
		int seqval = 0;
		for (L161S01B l161m01b : l161m01bs) {// 取出這個mainID底下的最大Seq
			seqval = l161m01b.getSeq();
			if (seqval > count) {
				count = seqval;
			}
		}
		return ++count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public void deleteListReInclude(String mainId) {
		List<L160M01B> l160m01bs = (List<L160M01B>) findListByMainId(
				L160M01B.class, mainId);

		List<L161S01A> l161s01as = (List<L161S01A>) findListByMainId(
				L161S01A.class, mainId);

		List<L161S01B> l161s01bs = (List<L161S01B>) findListByMainId(
				L161S01B.class, mainId);

		List<L162S01A> l162m01as = (List<L162S01A>) findListByMainId(
				L162S01A.class, mainId);

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		List<L164S01A> l164s01as = (List<L164S01A>) this.findListByMainId(
				L164S01A.class, mainId);

		List<L120S01P> l120s01ps = (List<L120S01P>) amlRelateService
				.findListByMainId(L120S01P.class, mainId);

		// 當該mainId已經存在的額度序號，要先刪除再引進新的。
		if (!l160m01bs.isEmpty()) {
			l160m01bDao.delete(l160m01bs);
		}

		// 當該mainId已經存在的額度序號，要先刪除再引進新的。
		if (!l161s01as.isEmpty()) {
			l161m01aDao.delete(l161s01as);
		}

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l161s01bs.isEmpty()) {
			l161m01bDao.delete(l161s01bs);
		}

		// 當該mainId已經存在的主從債務人資料，要先刪除再引進新的。
		if (!l162m01as.isEmpty()) {
			l162s01aDao.delete(l162m01as);
		}

		// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
		if (l164s01as != null && !l164s01as.isEmpty()) {
			this.deleteL164s01as(l164s01as);
		}

		if (l120s01ps != null && !l120s01ps.isEmpty()) {
			amlRelateService.deleteListL120s01p(l120s01ps);
		}

	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType, boolean upMis)
			throws Throwable {
		if (upMis) {
			Properties prop = MessageBundleScriptCreator
					.getComponentResource(LMS1605M01Page.class);

			List<L161S01A> l161s01as = (List<L161S01A>) findListByMainId(
					L161S01A.class, ((L160M01A) model).getMainId());

			if (l161s01as == null || l161s01as.isEmpty()) {
				// 無法取得額度動用資訊主檔(L161S01A)
				throw new CapMessageException(
						prop.getProperty("L160M01A.message79"), getClass());
			}

			// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
			StringBuffer errCntrNo1 = new StringBuffer("");
			for (L161S01A l161s01a : l161s01as) {
				// J-103-0202-005 Web e-Loan授信簽案衍生性金融商品遠匯與換匯科目，改以交易額度來簽案。
				// 非不變或01.不動用，僅修改聯貸參貸比率時，不用檢核(不用上傳名目額度)
				L140M01A l140m01a = lms1405Service
						.findL140m01aByMainId(l161s01a.getCntrMainId());

				if (l140m01a == null) {
					// 找不到額度明細表
					throw new CapMessageException(
							prop.getProperty("L160M01A.message66")
									+ l161s01a.getCntrNo(), getClass());
				}

				if (Util.equals(l161s01a.getIsDerivatives(), "")) {

					Boolean hasDerivateSubjectFlag = false;

					ArrayList<String> itemsAll = new ArrayList<String>();
					List<L140M01C> l140m01cs = lms1405Service
							.findL140m01cListByMainId(l140m01a.getMainId());

					if (l140m01cs != null && !l140m01cs.isEmpty()) {
						for (L140M01C l140m01c : l140m01cs) {
							itemsAll.add(l140m01c.getLoanTP());
						}

						hasDerivateSubjectFlag = lmsService
								.hasDerivateSubject(itemsAll
										.toArray(new String[itemsAll.size()]));

						if (hasDerivateSubjectFlag == true) {
							errCntrNo1.append(Util.equals(
									errCntrNo1.toString(), "") ? "" : "、");
							errCntrNo1.append(l140m01a.getCntrNo());
						}

					} else {
						// 找不到額度明細表
						throw new CapMessageException(
								prop.getProperty("L160M01A.message66")
										+ l161s01a.getCntrNo(), getClass());
					}
				} else if (Util.equals(l161s01a.getIsDerivatives(),
						UtilConstants.DEFAULT.是)) {
					if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
						errCntrNo1
								.append(Util.equals(errCntrNo1.toString(), "") ? ""
										: "、");
						errCntrNo1.append(l140m01a.getCntrNo());

					}
				}

			}

			if (Util.notEquals(errCntrNo1.toString(), "")) {
				throw new CapMessageException(
						prop.getProperty("L160M01A.cntrInfo")
								+ errCntrNo1.toString()
								+ prop.getProperty("L160M01A.message75") + "：「"
								+ prop.getProperty("L160M01A.dervApplyAmtType")
								+ "」", getClass());
			}

			this.upLoadMIS((L160M01A) model);
		}

		if (model instanceof L160M01A) {
			save((L160M01A) model);
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS1605Flow",
						((L160M01A) model).getOid(), user.getUserId(),
						user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				// inst.setNextDept("200");
				// inst.setNextUser("nextTest");

				// resultType 控制前進還是後退
				// 當有先行動用的狀態 是到03O 非先行動用表示已完成 到05O

				L163S01A l163m01a = ((L160M01A) model).getL163S01A();
				// 先行動用_待覆核04O
				if (CreditDocStatusEnum.先行動用_待覆核.getCode().equals(
						((L160M01A) model).getDocStatus())) {
					inst.setAttribute("result", resultType ? "結案" : "退回");

					// 當在待覆核執行先行動用已辦妥要填入覆核日期 ，如果是退回要清空乙級主管
					if (resultType) {
						l163m01a.setBfReCheckDate(CapDate.getCurrentTimestamp());
						l163m01a.setBossId(user.getUserId());

						// J-110-0547 為控管先行動用之授信案件，增加先行動用呈核及控制表預定補全日期之通知功能。
						// for批次使用，系統實際核准時間(先行動用覆核)，讓晚上批次可以抓資料寫進ELF601
						if (model instanceof L160M01A) {
							L160M01A l160m01a = (L160M01A) model;
							l160m01a.setSysActApproveTime(CapDate
									.getCurrentTimestamp());
							save(l160m01a);
						}
					} else {
						l163m01a.setBossId(null);
					}
					save(l163m01a);
				} else {
					// 當核定動撥有兩種情形，先行動用的核定跟非先行動用
					String resultNext = (UtilConstants.DEFAULT.是
							.equals(((L160M01A) model).getUseType())) ? "先行動用"
							: "結案";
					inst.setAttribute("result", resultType ? resultNext : "退回");
				}

			}
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	@Override
	public void flowActionGo(String mainOid, L163S01A l163s01a)
			throws Throwable {

		try {
			save(l163s01a);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			inst.setDeptId(user.getUnitNo());
			inst.setUserId(user.getUserId());
			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	// J-106-0029-003 Web e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組
	@Override
	public JSONObject findBlackPage(String name, String mainId)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 測試資料
		// obsmqReq.setDefaultRequestHeader("0A7");
		// obsmqReq.addRequest("ENG-NAME", "BIN LADEN");

		// BGN J-106-0029-003 Web
		// e-Loan授信簽報書借款人基本資料與動審表黑名單查詢調整使用共用模組****************
		// OBSMqGwReqMessage obsmqReq = new OBSMqGwReqMessage("004", mainId);
		// obsmqReq.setDefaultRequestHeader(user.getUnitNo());
		// obsmqReq.addRequest("ENG-NAME", name);
		// JSONObject resp = null;
		// resp = obsMqGwClient.send(obsmqReq);
		// logger.info("004==>respon" + resp.toString());
		// END
		// ************************************************************************************

		// 成功會收到回應訊息
		// {"HSEQNO":"A5097674-5243-4C0C-8B91-8C33CAB5D85F","HTXNID":"004","HBRNO":"0A7","HRET-CODE":"MCP0000","HTXN-PAGE":"01","HTXN-CNT":"000","HTXN-TOT":"0000","HTXN-TYPE":"S","HTXN-END":"Y","HRET-MSG":"","HFILLER":"","NEXT-KEY":"OFAC0000011378OBIN LADEN","RECORD":[]}
		// 失敗會收到GWException

		String unitNo = user.getUnitNo();
		JSONObject resp = new JSONObject();
		try {
			List<String> blackList = customerSrv.findBlackList(unitNo, name,
					mainId);
			String blackListCode = blackList
					.get(ICustomerService.BlackList_ReturnCode);
			String memo = blackList.get(ICustomerService.BlackList_OFACName);

			JSONArray value = new JSONArray();
			if (Util.equals(blackListCode,
					UtilConstants.Casedoc.L120s09aBlackListCode.是黑名單)
					|| Util.equals(blackListCode,
							UtilConstants.Casedoc.L120s09aBlackListCode.可能是黑名單)) {
				value.add(blackListCode);
				value.add(memo);
			} else if (Util.equals(blackListCode,
					UtilConstants.Casedoc.L120s09aBlackListCode.未列於黑名單)) {
			} else {
				// ????
				value.add(blackListCode);
				value.add(memo);
			}
			resp.put("RECORD", value);

		} catch (CapException e) {
			throw e;
		}

		return resp;
	}

	@Override
	public void upLoadMIS(L160M01A l160m01a) throws CapException {
		String brnId = l160m01a.getOwnBrId();
		long t1 = System.currentTimeMillis();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		if (Util.equals(Util.trim(l160m01a.getNewVersion()), "")
				|| Util.equals(Util.trim(l160m01a.getNewVersion()), "00")) {
			// 請至文件資訊頁籤重新執行「選擇額度明細表」
			throw new CapMessageException(
					prop.getProperty("L160M01A.message73"), getClass());
		}

		L120M01A l120m01a = l120m01aDao.findByMainId(l160m01a.getSrcMainId());
		List<L120S01C> l120s01cs = l120s01cDao.findByMainId(l120m01a
				.getMainId());
		L120M01B l120m01b = l120m01bDao.findByUniqueKey(l120m01a.getMainId());
		// 取得該額度明細表 對應 額度序號
		HashMap<String, String> mainIdmapingCntrno = new HashMap<String, String>();
		List<String> mainIds = new ArrayList<String>();
		// 將查詢結果放到List裡面
		List<String> cntrNosList = new ArrayList<String>();
		for (L160M01B l160m01b : l160m01a.getL160m01b()) {
			mainIds.add(l160m01b.getReMainId());
			mainIdmapingCntrno
					.put(l160m01b.getReMainId(), l160m01b.getCntrNo());
			// cntrNosTemp.append(cntrNosTemp.length() > 0 ? "," : "");
			// cntrNosTemp.append("'");
			// cntrNosTemp.append(l160m01b.getCntrNo());
			// cntrNosTemp.append("'");
			cntrNosList.add(l160m01b.getCntrNo());
		}

		List<L120M01F> l120m01fs = l120m01fDao.findByMainId(l160m01a
				.getSrcMainId());
		// 取出所有的額度序號一次查詢

		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByMainIdList(mainIds
						.toArray(new String[mainIds.size()]));

		List<Map<String, Object>> cntrNoData = misIquotappService
				.findByCntrNo(cntrNosList.toArray(new String[0]));

		ArrayList<String> cntrNoList = new ArrayList<String>();
		for (Map<String, Object> map : cntrNoData) {
			cntrNoList.add((String) map.get("QUOTANO"));
		}

		// 目前系統時間
		String sDate = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		// 目前系統時間(時分秒)
		String sDateTime = CapDate.convertTimestampToString(CapDate.getCurrentTimestamp(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
		// 借款人資料檔
		HashMap<String, HashMap<String, String>> custDatas = new HashMap<String, HashMap<String, String>>();
		HashMap<String, String> custIdDup10_name = new HashMap<String, String>();
		if (LMSUtil.isOverSea_CLS(l120m01a)) {
			List<C120M01A> c120m01as = c120m01aDao.findByMainId(l120m01a
					.getMainId());
			for (C120M01A c120m01a : c120m01as) {
				HashMap<String, String> custData = new HashMap<String, String>();
				// OBU公司
				custData.put("existDate", "");
				custData.put("feeDate", "");
				custData.put("countryDate", "");
				custDatas.put(
						StrUtils.concat(c120m01a.getCustId(),
								c120m01a.getDupNo()), custData);
				// ------
				custIdDup10_name.put(LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo()), Util
						.trim(c120m01a.getCustName()));
			}
		} else {
			List<L120S01A> l120s01as = l120s01aDao.findByMainId(l120m01a
					.getMainId());
			for (L120S01A l120s01a : l120s01as) {
				HashMap<String, String> custData = new HashMap<String, String>();
				custData.put("existDate", CapDate.formatDate(
						l120s01a.getExistDate(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				custData.put("feeDate", CapDate.formatDate(
						l120s01a.getFeeDate(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				custData.put("countryDate", CapDate.formatDate(
						l120s01a.getCountryDate(),
						UtilConstants.DateFormat.YYYY_MM_DD));
				custDatas.put(
						StrUtils.concat(l120s01a.getCustId(),
								l120s01a.getDupNo()), custData);
				// ------
				custIdDup10_name.put(LMSUtil.getCustKey_len10custId(
						l120s01a.getCustId(), l120s01a.getDupNo()), Util
						.trim(l120s01a.getCustName()));
			}
		}

		Map<String, String> codeTypeMap = codeTypeService
				.findByCodeType(UtilConstants.CodeTypeItem.授信科目轉會計科目);
		// 資料修改人
		String updater = MegaSSOSecurityContext.getUserId();

		// 存放處理過的客戶
		List<String> tProcessCustId = new ArrayList<String>();
		// 存放已處理過的額度序號
		List<String> tProcessSno = new ArrayList<String>();

		// MIS 用
		List<Object[]> iquotgurList = new ArrayList<Object[]>();
		List<Object[]> iquotjonList = new ArrayList<Object[]>();
		List<Object[]> quotainfList = new ArrayList<Object[]>();
		List<Object[]> quotunioList = new ArrayList<Object[]>();
		List<Object[]> lnf164List = new ArrayList<Object[]>();

		// AS400 用
		List<Object[]> elf164List = new ArrayList<Object[]>();
		// List<Object[]> elf383List = new ArrayList<Object[]>();
		// List<Object[]> elf384List = new ArrayList<Object[]>();
		List<Object[]> elf385List = new ArrayList<Object[]>();

		List<Object[]> elf422List = new ArrayList<Object[]>();
		Boolean upAs400 = false;
		if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(brnId)
				.getBrNoFlag())) {
			upAs400 = true;
		}
		HashMap<String, String> checkIquotjon = new HashMap<String, String>();
		String cntrNo = "";
		for (L140M01A l140m01a : l140m01as) {
			cntrNo = mainIdmapingCntrno.get(l140m01a.getMainId());
			if (Util.isEmpty(cntrNo)) {
				cntrNo = l140m01a.getCntrNo();
			}
			// 若該額度序號已經處理過則直接跳過此份額度明細表
			if (Util.isEmpty(Util.trim(cntrNo)) || tProcessSno.contains(cntrNo)) {
				continue;
			}

			// 將處理完的額度序號存到到 tProcessSno 中
			tProcessSno.add(cntrNo);

			L161S01A l161s01a = this.findL161m01aByMainIdCntrno(
					l160m01a.getMainId(), cntrNo);

			if (l161s01a == null) {
				// 無法取得額度動用資訊主檔(L161S01A)
				throw new CapMessageException(
						prop.getProperty("L160M01A.message79"), getClass());
			}

			logger.info("{}=======>{}", "Start", "sendFtpToArServer|cntrNo==>"
					+ cntrNo);

			String snoKind = l161s01a.getSnoKind();
			if (FlowDocStatusEnum.已核准.getCode().equals(l140m01a.getDocStatus())) {
				// 測試FTP檔案到AR
				if (Util.equals(snoKind,
						UtilConstants.Cntrdoc.snoKind.應收帳款賣方一般戶)
						|| Util.equals(snoKind,
								UtilConstants.Cntrdoc.snoKind.應收帳款賣方聯貸母戶)) {

					lmsService.sendFtpToArServer(l140m01a.getMainId(),
							l140m01a.getCntrNo());
				}

			}

			logger.info("{}=======>{}", "Start", "uploadIquotapp|cntrNo==>"
					+ cntrNo);
			// 1.上傳 核准額度資料檔 IQUOTAPP
			this.uploadIquotapp(brnId, l120m01a, l140m01a, l120m01fs,
					cntrNoList, updater, upAs400, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadIquotgur|cntrNo==>"
					+ cntrNo);
			// 2.上傳 保證人檔IQUOTGUR
			this.uploadIquotgur(l140m01a, updater, iquotgurList, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadIquotjon|cntrNo==>"
					+ cntrNo);
			// 3.上傳 共同借款人檔Iquotjon
			this.uploadIquotjon(l140m01a, custIdDup10_name, updater,
					iquotjonList, cntrNo, checkIquotjon, l120m01a);
			logger.info("{}=======>{}", "Start", "uploadQuotainf|cntrNo==>"
					+ cntrNo);
			// 6.上傳 額度資訊檔 QUOTAINF
			this.uploadQuotainf(brnId, l160m01a, l140m01a, l120m01a, updater,
					quotainfList, elf422List, upAs400, cntrNo, l120m01fs);

			// 非不變或01.不動用，僅修改聯貸參貸比率時要上傳QUOTUNIO
			if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
					.getProPerty())
					|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
				logger.info("{}=======>{}", "Start", "uploadQuotunio|cntrNo==>"
						+ cntrNo);
				// 7.上傳 聯貸案參貸比率-自行參貸 QUOTUNIO
				this.uploadQuotunio(brnId, l160m01a, l140m01a, l120m01a,
						updater, quotunioList, elf385List, upAs400, cntrNo,
						l161s01a);

			}
			logger.info("{}=======>{}", "Start", "uploadQuotappr|cntrNo==>"
					+ cntrNo);
			// 8.上傳 授信額度檔 QUOTAPPR
			this.uploadQuotappr(brnId, l160m01a, l140m01a, l120m01a, l120m01b,
					l120s01cs, custDatas, updater, sDate, upAs400, cntrNo,
					l161s01a);

			// 此額度性質為「取消」, 不上傳科(子)目及其限額
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.取消)) {
				continue;
			}
			logger.info("{}=======>{}", "Start", "uploadQuotsub|cntrNo==>"
					+ cntrNo);
			// 9.上傳科(子)目及其限額檔Quotsub
			this.uploadQuotsub(brnId, l160m01a, l140m01a, l120m01a, updater,
					sDate, upAs400, codeTypeMap, cntrNo);
			logger.info("{}=======>{}", "Start", "uploadLNF164|cntrNo==>"
					+ cntrNo);
			// 10.-利率條件LN.LNF164
			this.uploadLNF164(brnId, l160m01a, l140m01a, l120m01a, updater,
					sDate, lnf164List, elf164List, upAs400, cntrNo);

			// J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
			this.updateELF500(l160m01a, l140m01a, l120m01a, cntrNo);

			// J-113-0168 E-LOAN完成動審上送額度介面資料至A-LOAN時，針對屬授信額度者(排除衍生性金融商品業務外)，立即通知0024為本行授信戶
			if (!tProcessCustId.contains(l140m01a.getCustId() + l140m01a.getDupNo())) {
				// (排除)衍生性金融商品科目961 962 963 964 、 純Z類的虛科目、應收帳款買方 fact_type = 60的不用通知
				// 已處理過的額度序號上面跳過了，by客戶傳處理過的客戶也要跳過
				this.callLNSP0130ForUpdateLnFlag(l140m01a, l160m01a, brnId);
				tProcessCustId.add(l140m01a.getCustId() + l140m01a.getDupNo());	
			}
		}
		// G-113-0036 異動聯貸案參貸比率時(uploadQuotappr)，如有新增額度序號一併發聯行額度明細
		lmsService.L1601M01AsendToL141M01A(l120m01a, l140m01as, l160m01a);
		
		
		// 4.上傳主從債務人檔 保證人 ELLNGTEE
		// 5.上傳主從債務人檔 共同借款人 ELLNGTEE
		logger.info("{}=======>{}", "Start", "uploadEllngtee|");
		this.uploadEllngtee(l160m01a, updater, upAs400);
		logger.info("{}=======>{}", "Start", "uploadQuotunio2|");
		// 11.上傳 聯貸案參貸比率-同業聯貸參貸 QUOTUNIO
		this.uploadQuotunio2(brnId, l160m01a, l120m01a, updater, quotunioList,
				elf385List, upAs400);
		logger.info("{}=======>{}", "Start", "misIquotgurService.insert|");
		misIquotgurService.insert(iquotgurList);
		logger.info("{}=======>{}", "Start", "misIquotjonService.insert|");
		misIquotjonService.insert(iquotjonList);
		logger.info("{}=======>{}", "Start", "misQuotainfService.insert|");
		misQuotainfService.insert(quotainfList);
		logger.info("{}=======>{}", "Start", "misQuotunioService.insert|");
		misQuotunioService.insert(quotunioList);
		logger.info("{}=======>{}", "Start", "misLNF164Service.insert|");
		misLNF164Service.insert(lnf164List);

		// J-113-0035 貸後追蹤分項紀錄檔
		logger.info("{}=======>{}", "Start", "misELF603Service.insert|");
		String as400_controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0035_AS400_ON"));
		if(Util.equals(UtilConstants.DEFAULT.是, as400_controlFlag)){//海外是否啟用
			this.updateELF603(l160m01a, l120m01a, l140m01as, updater, sDateTime);
		}
		
		logger.info(StrUtils.concat("\n mis upLoad total Time ===>",
				(System.currentTimeMillis() - t1), " ms"));

		// 以下為AS400 測試
		long t2 = System.currentTimeMillis();
		// obsdbELF164Service.insert(brnId,
		// LMSUtil.covertAs400Time(elf164List));
		if (upAs400) {
			logger.info("{}=======>{}", "Start", "obsdbELF385Service.insert|");
			obsdbELF385Service.insert(brnId,
					LMSUtil.covertAs400Time(elf385List));
			logger.info("{}=======>{}", "Start", "obsdbELF422Service.insert|");
			obsdbELF422Service.insert(brnId,
					LMSUtil.covertAs400Time(elf422List));
			logger.info(StrUtils.concat("\n as400 upLoad total Time ===>",
					(System.currentTimeMillis() - t2), " ms"));
		}

		// 以下為測試rollback 用
		// Object ddd = null;
		//
		// logger.info(ddd.toString());

	}

	/**
	 * 上傳核准額度資料檔 IQUOTAPP(AS400-ELF388)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l120m01a
	 *            案件簽報書
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01f
	 *            借款人
	 * @param cntrNoList
	 *            額度序號list
	 * @param updater
	 *            更新者
	 * @param upAS400
	 *            是否上傳AS400
	 */
	private void uploadIquotapp(String BRNID, L120M01A l120m01a,
			L140M01A l140m01a, List<L120M01F> l120m01fs,
			ArrayList<String> cntrNoList, String updater, boolean upAS400,
			String cntrNo) {

		// 身分證/統編 (額度明細表)
		String custId = l140m01a.getCustId();

		// 重複序號 額度明細表
		String dupNo = Util.isEmpty(l140m01a.getDupNo()) ? "0" : l140m01a
				.getDupNo();

		// 授信經辦行員編號
		String icbcNo = "";
		// 初放主管姓名
		String omgrName = Util.isEmpty(userInfoService.getUserName(l120m01a
				.getApprover())) ? "" : userInfoService.getUserName(l120m01a
				.getApprover());
		// 敘作主管姓名
		String fmgrName = Util.isEmpty(userInfoService.getUserName(l120m01a
				.getApprover())) ? "" : userInfoService.getUserName(l120m01a
				.getApprover());
		for (L120M01F l120m01f : l120m01fs) {
			String staffJob = l120m01f.getStaffJob();
			String staffNo = l120m01f.getStaffNo();
			if (UtilConstants.BRANCHTYPE.分行.equals(l120m01f.getBranchType())) {
				if (UtilConstants.STAFFJOB.經辦L1.equals(staffJob)) {
					icbcNo = staffNo;
				}
				if (UtilConstants.STAFFJOB.單位授權主管L5.equals(staffJob)) {

					omgrName = Util.trimSizeInOS390(this.getUserName(staffNo),
							20);
					fmgrName = Util.trimSizeInOS390(this.getUserName(staffNo),
							20);
				}
			}
		}
		// 授信經辦姓名
		String cName = Util.isEmpty(userInfoService.getUserName(icbcNo)) ? ""
				: userInfoService.getUserName(icbcNo);
		cName = Util.trimSizeInOS390(cName, 20);
		// 授權等級
		String approLvl = "";

		if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a.getDocKind())) {
			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(l120m01a
					.getAuthLvl())) {
				approLvl = "B";
			} else {
				approLvl = "9";
			}

		} else if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
				.getDocKind())) {
			approLvl = Util.trim(l120m01a.getCaseLvl());
		} else {
			logger.info(StrUtils.concat("\n DocKind is NULL",
					l120m01a.getDocKind()));
		}
		if (Util.isEmpty(approLvl)) {
			approLvl = "";
		}
		// 當有此額度序號表示非初貸
		if (cntrNoList.contains(cntrNo)) {
			// ---- 非初次貸放時只傳敘作主管, 原初放主管不上傳 ---
			misIquotappService.update(custId, dupNo,
					LMSUtil.forFiveBossId(icbcNo), cName, fmgrName, approLvl,
					LMSUtil.forFiveBossId(updater), cntrNo);

		} else {
			// ---- 初次貸放時初放與敘作主管為同一位 ---
			misIquotappService.insert(cntrNo, custId, dupNo,
					LMSUtil.forFiveBossId(icbcNo), cName, omgrName, fmgrName,
					approLvl, LMSUtil.forFiveBossId(updater));

		}
		if (upAS400) {
			// 以下為as400上傳
			if (obsdbELF388Service.selByQuotano(BRNID, new Object[] { cntrNo })
					.isEmpty()) {
				obsdbELF388Service.insert(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] { cntrNo, custId,
								dupNo, LMSUtil.forFiveBossId(icbcNo), cName,
								omgrName, fmgrName, approLvl, updater,
								CapDate.getCurrentTimestamp() }));
			} else {
				obsdbELF388Service.update(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] { custId, dupNo,
								LMSUtil.forFiveBossId(icbcNo), cName, fmgrName,
								Util.trim(approLvl), updater,
								CapDate.getCurrentTimestamp(), cntrNo }));
			}
		}

	}

	/**
	 * 上傳 保證人檔 Iquotgur
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 */
	private void uploadIquotgur(L140M01A l140m01a, String updater,
			List<Object[]> dataList, String cntrNo) {
		// 刪除該額度序號存在的 保證人檔IQUOTGUR
		misIquotgurService.delByCntrNo(cntrNo);
		// 姓名長度限制在70
		for (L140M01I l140m01i : l140m01a.getL140m01i()) {
			if (Util.equals(Util.trim(l140m01i.getRType()),
					UtilConstants.lngeFlag.連帶保證人)) {
				// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式

				dataList.add(new Object[] { cntrNo, l140m01i.getRId(),
						l140m01i.getRDupNo(),
						Util.trimSizeInOS390(l140m01i.getRName(), 70), updater });
			}
		}

		// misIquotgurService.insert(dataList);

	}

	/**
	 * 上傳 共同借款人檔 Iquotjon
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param l120s01as
	 *            借款人資料檔
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param dataList
	 *            batch 上傳
	 * @param cntrNo
	 *            額度序號
	 * @param checkIquotjon
	 *            排除重覆的借款人
	 */
	private void uploadIquotjon(L140M01A l140m01a,
			Map<String, String> custIdDup10_name, String updater,
			List<Object[]> dataList, String cntrNo,
			HashMap<String, String> checkIquotjon, L120M01A l120m01a) {
		// 刪除該額度序號存在的 共同借款人檔Iquotjon
		misIquotjonService.delByCntrNo(cntrNo);
		// 姓名長度限制在70
		String key = "";
		for (String custIdDup10 : custIdDup10_name.keySet()) {
			String custName = custIdDup10_name.get(custIdDup10);
			// 排除主借款人
			if (Util.equals(custIdDup10, LMSUtil.getCustKey_len10custId(
					l120m01a.getCustId(), l120m01a.getDupNo()))) {

			} else {

				key = cntrNo + custIdDup10;
				if (!checkIquotjon.containsKey(key)) {
					String custId = StringUtils.substring(custIdDup10, 0, 10);
					String dupNo = StringUtils.substring(custIdDup10, 10);
					dataList.add(new Object[] { cntrNo, custId, dupNo,
							Util.trimSizeInOS390(custName, 42), updater });
					checkIquotjon.put(key, "");
				}
			}

		}
		// misIquotjonService.insert(dataList);

	}

	/**
	 * 主從債務人檔 ELLNGTEE
	 * 
	 * @param l160m01a
	 *            額度明細表
	 * @param l120s01as
	 *            借款人資料檔
	 * @param updater
	 *            資料更新者
	 * @param upAs400    
	 * 
	 */
	private void uploadEllngtee(L160M01A l160m01a, String updater, boolean upAs400) {
		List<String> cntrNoList = new ArrayList<String>();
		List<Object[]> dataList = new ArrayList<Object[]>();
		// 董監事任期止日
		String releaseDT = "9999-12-31";
		String brNo = "";
		String custId = "";
		String dupNo = "";
		String cntrNo = "";
		String lngeFlag = "";
		// 從債務人統編
		String lngeId = "";
		// 從債務人統編重複序號
		String dupNo1 = "";
		// 從債務人姓名
		String lngeNm = "";
		// 國家別
		String ntCode = "";
		// 與主債務人關係
		String lnGere = "";
		// 更新日
		String upddt = CapDate.formatDate(new Date(),
				UtilConstants.DateFormat.YYYY_MM_DD);
		// 保證人負担保證責任比率
		BigDecimal guaPercent = BigDecimal.ZERO;

		// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
		BigDecimal priority = null;

		// J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
		String guaNaExposure = "";
		
		//G-113-0036 主從債務人新增 擔保限額、當地客戶識別ID
		String localId = "";
		BigDecimal grtAmt = null; 
		String brnId = l160m01a.getOwnBrId();
		for (L162S01A l162m01a : l160m01a.getL162S01A()) {
			cntrNo = l162m01a.getCntrNo();
			brNo = l162m01a.getCntrNo().substring(0, 3);
			custId = l162m01a.getCustId();
			dupNo = l162m01a.getDupNo();
			lngeFlag = l162m01a.getRType();
			lngeId = l162m01a.getRId();
			dupNo1 = l162m01a.getRDupNo();
			ntCode = Util.trim(l162m01a.getRCountry());
			lngeNm = Util.trimSizeInOS390(l162m01a.getRName(), 40);
			if (l162m01a.getCustId().equals(l162m01a.getRId())
					&& l162m01a.getDupNo().equals(l162m01a.getRDupNo())) {
				// 判斷當其關係為空白是要帶X0本人
				lnGere = "X0";
			} else {
				lnGere = l162m01a.getRKindD();
			}
			// 不論共同借款人或是從債務人, 在此統編及額度序號下之所有資料皆刪除
			if (!cntrNoList.contains(cntrNo)) {
				// 先刪除此筆資料後再新增
				misELLNGTEEService.delEllngteeByUniqueKey(brNo, custId, dupNo,
						cntrNo);
				String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
				if (upAs400 && Util.equals(UtilConstants.DEFAULT.是, controlflag)) {
					obsdbELF401Service.delEllngteeByUniqueKey(brnId, brNo, custId, dupNo,
							cntrNo);
				}
				cntrNoList.add(cntrNo);
			}

			if (!Util.isEmpty(l162m01a.getDueDate())) {
				releaseDT = CapDate.formatDate(l162m01a.getDueDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			} else {
				releaseDT = "9999-12-31";
			}

			if (Util.isEmpty(l162m01a.getGuaPercent())) {
				if (Util.equals(l162m01a.getRType(), "S")) {
					guaPercent = BigDecimal.ZERO;
				} else {
					guaPercent = BigDecimal.valueOf(100);
				}

			} else {
				guaPercent = l162m01a.getGuaPercent();
			}

			// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
			priority = l162m01a.getPriority() == null ? BigDecimal.ZERO
					: l162m01a.getPriority();

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			if (Util.notEquals(l162m01a.getRType(), "C")
					&& Util.notEquals(l162m01a.getRType(), "S")) {
				guaNaExposure = Util.trim(l162m01a.getGuaNaExposure());
			} else {
				guaNaExposure = "";
			}
			//G-113-0036 主從債務人新增 擔保限額、當地客戶識別ID
			grtAmt = l162m01a.getGrtAmt() == null ? BigDecimal.ZERO
					: l162m01a.getGrtAmt();
			localId = Util.isEmpty(l162m01a.getLocalId()) ? "" : l162m01a.getLocalId();
			
			dataList.add(new Object[] { brNo, custId, dupNo, cntrNo, lngeFlag,
					lngeId, dupNo1, lngeNm, ntCode, lnGere, upddt, updater,
					releaseDT, guaPercent, priority, guaNaExposure, grtAmt, localId });
			
		}
		misELLNGTEEService.insert(dataList);
		String controlflag = Util.trim(lmsService.getSysParamDataValue("RPS_GTE1000"));
		if (upAs400 && Util.equals(UtilConstants.DEFAULT.是, controlflag)) {
			obsdbELF401Service.insert(brnId, LMSUtil.covertAs400Time(dataList));
		}
	}

	/**
	 * 額度資訊檔 QUOTAINF(AS400 ELF422)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            as400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrNo
	 *            額度序號
	 * 
	 */
	private void uploadQuotainf(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrNo, List<L120M01F> l120m01fs) {
		// 先刪除此筆資料後再新增
		misQuotainfService.delByKey(l160m01a.getCustId(), l160m01a.getDupNo(),
				l160m01a.getOwnBrId(), cntrNo);
		if (upAS400) {
			obsdbELF422Service.delByKey(BRNID, l160m01a.getCustId(),
				l160m01a.getDupNo(), l160m01a.getOwnBrId(), cntrNo);
		}

		String bossId = "";
		for (L120M01F l120m01f : l120m01fs) {
			String staffJob = l120m01f.getStaffJob();
			String staffNo = l120m01f.getStaffNo();
			if (UtilConstants.STAFFJOB.授信主管L3.equals(staffJob)) {
				if (UtilConstants.BRANCHTYPE.分行
						.equals(l120m01f.getBranchType())) {
					bossId = staffNo;
				}
			}

		}
		String newCase = LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.新做) ? UtilConstants.DEFAULT.是
				: "";

		dataList.add(new Object[] { l160m01a.getCustId(), l160m01a.getDupNo(),
				l160m01a.getOwnBrId(), cntrNo,
				TWNDate.toTW(l120m01a.getCaseDate()),
				TWNDate.toTW(l120m01a.getEndDate()),
				Util.trimSizeInOS390(l120m01a.getCustName(), 40),
				LMSUtil.forFiveBossId(bossId),
				Util.trimSizeInOS390(this.getUserName(bossId), 20),
				l120m01a.getMainId(), newCase, updater });
		if (upAS400) {
			as400List.add(new Object[] { l160m01a.getCustId(),
					l160m01a.getDupNo(), l160m01a.getOwnBrId(), cntrNo,
					CapDate.formatDate(l120m01a.getCaseDate(), "yyyyMMdd"),
					CapDate.formatDate(l120m01a.getEndDate(), "yyyyMMdd"),

					Util.trimSizeInOS390(l120m01a.getCustName(), 40),
					LMSUtil.forFiveBossId(bossId),
					Util.trimSizeInOS390(this.getUserName(bossId), 20),
					// 以後用上面那段取核准者姓名
					// Util.trimSizeInOS390("bossName", 20),
					l120m01a.getMainId(), newCase, updater,
					CapDate.getCurrentTimestamp() });
		}

	}

	/**
	 * 聯貸案參貸比率-自行參貸 QUOTUNIO(AS400-ELF385)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            AS400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrno
	 *            額度序號
	 */
	private void uploadQuotunio(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrno, L161S01A l161s01a) {
		// 先刪除該額度序號的檔案
		misQuotunioService.delByCntrNo(cntrno);
		if (upAS400) {
			obsdbELF385Service.delByCntrNoOnly(BRNID, cntrno);
		}

		StringBuffer branchCode = new StringBuffer();
		// List<Object[]> dataList = new ArrayList<Object[]>();
		for (L161S01B l161s01b : l161s01a.getL161s01b()) {

			if (Util.notEquals(l161s01b.getSlBank(), UtilConstants.兆豐銀行代碼)) {
				continue;
			}

			IBranch branchData = branchService
					.getBranch(l161s01b.getSlBranch());
			if (branchData == null) {
				continue;
			}
			// 當為海外分行其前三碼為999
			if (UtilConstants.BrNoType.國外.equals(branchData.getBrNoFlag())) {
				branchCode.append("999").append(
						misdbBASEService.findICBCBRByBrId(l161s01b
								.getSlBranch()));
			} else {
				branchCode.append(UtilConstants.兆豐銀行代碼).append(
						l161s01b.getSlBranch());
			}
			// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
			// 2013/07/19,Rex,修改判斷為動審表的分行
			if (l160m01a.getOwnBrId().equals(cntrno.substring(0, 3))) {
				dataList.add(new Object[] {
						UtilConstants.Usedoc.unioType.自行聯貸,
						cntrno,
						branchCode.toString(),
						l140m01a.getOwnBrId().equals(l161s01b.getSlBranch()) ? UtilConstants.DEFAULT.是
								: "",
						Util.parseBigDecimal(l161s01b.getSlAmt()), updater });
			}

			if (upAS400) {
				as400List
						.add(new Object[] {
								UtilConstants.Usedoc.unioType.自行聯貸,
								cntrno,
								branchCode.toString(),
								l140m01a.getOwnBrId().equals(
										l161s01b.getSlBranch()) ? UtilConstants.DEFAULT.是
										: "",
								Util.parseBigDecimal(l161s01b.getSlAmt()),
								updater, CapDate.getCurrentTimestamp() });
			}

			branchCode.setLength(0);
		}
		// misQuotunioService.insert(dataList);

		// // 更新上傳主辦行的聯貸額度上 db2
		// if (total != l140m01a.getCurrentApplyAmt().longValue()) {
		// // Long d = l140m01a.getCurrentApplyAmt().longValue() - total;
		// }
	}

	/**
	 * 聯貸案參貸比率-同業聯貸參貸 QUOTUNIO2(AS400-ELF385)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            資料更新者
	 * @param dataList
	 *            Sql資料儲存
	 * @param as400List
	 *            AS400 Sql資料儲存
	 * @param upAS400
	 *            是否上傳as400
	 * 
	 */
	private void uploadQuotunio2(String BRNID, L160M01A l160m01a,
			L120M01A l120m01a, String updater, List<Object[]> dataList,
			List<Object[]> as400List, boolean upAS400) {
		// 2013/07/03,Rex,同業聯貸為Y才需上傳同業聯貸

		Set<L161S01A> l161m01as = l160m01a.getL161S01A();
		for (L161S01A l161m01a : l161m01as) {

			if ("Y".equals(l161m01a.getUnitCase())) {
				String cntrno = Util.trim(l161m01a.getCntrNo());
				if (Util.isNotEmpty(cntrno)) {
					// 先刪除該額度序號的檔案
					misQuotunioService.delByCntrNo(l161m01a.getCntrNo());
					if (upAS400) {
						obsdbELF385Service.delByCntrNoOnly(BRNID,
								l161m01a.getCntrNo());
					}
					String branchCode = "";
					// List<Object[]> dataList = new ArrayList<Object[]>();
					// Long total = Long.valueOf(0);

					Set<L161S01B> l161m01bs = l161m01a.getL161s01b();
					if (l161m01bs != null && !l161m01bs.isEmpty()) {
						for (L161S01B l161m01b : l161m01a.getL161s01b()) {

							if (Util.equals(l161m01b.getSlBank(),
									UtilConstants.兆豐銀行代碼)) {
								continue;
							}

							if (Util.isEmpty(cntrno)) {
								continue;
							}
							branchCode = "";
							if (UtilConstants.兆豐銀行代碼.equals(l161m01b
									.getSlBank())) {
								IBranch branchData = branchService
										.getBranch(l161m01b.getSlBranch());
								// 當為海外分行其前三碼為999
								if (UtilConstants.BrNoType.國外.equals(branchData
										.getBrNoFlag())) {

									branchCode = StrUtils.concat("999",
											misdbBASEService
													.findICBCBRByBrId(l161m01b
															.getSlBranch()));

								} else {
									branchCode = StrUtils.concat(
											UtilConstants.兆豐銀行代碼,
											l161m01b.getSlBranch());

								}
							} else {
								if (Util.isNotEmpty(Util.trim(l161m01b
										.getSlBranch()))) {
									branchCode = StrUtils.concat(l161m01b
											.getSlBank(), l161m01b
											.getSlBranch().substring(3, 6));
								} else {
									branchCode = l161m01b.getSlBank();
								}

							}

							// 12 -國外銀行
							if ("12".equals(l161m01b.getSlBankType())
									|| "99".equals(l161m01b.getSlBankType())) {
								branchCode = l161m01b.getSlBank();

							}

							if (!Util.isEmpty(branchCode)) {
								if (Util.trim(branchCode).length() == 3) {
									branchCode = StrUtils.concat(
											Util.trim(branchCode), "000");
								}
							}

							// 2012_07_19_MIS 只有主辦行 可以上,簽報書簽案行 與額度序號前三碼相同時 才可以上傳
							// 2013/07/19,Rex,修改判斷為動審表的分行
							if (l160m01a.getOwnBrId().equals(
									cntrno.substring(0, 3))) {
								dataList.add(new Object[] {
										UtilConstants.Usedoc.unioType.同業聯貸,
										cntrno,
										branchCode,
										l161m01b.getSlMaster(),
										this.gfnConvertZero(l161m01b.getSlAmt()),
										updater });
							}

							if (upAS400) {
								as400List
										.add(new Object[] {
												UtilConstants.Usedoc.unioType.同業聯貸,
												cntrno,
												branchCode,
												l161m01b.getSlMaster(),
												this.gfnConvertZero(l161m01b
														.getSlAmt()), updater,
												CapDate.getCurrentTimestamp() });
							}

						}
					}

					// misQuotunioService.insert(dataList);

					// // 更新上傳主辦行的聯貸額度上 db2
					// if (total != l140m01a.getCurrentApplyAmt().longValue()) {
					// // Long d = l140m01a.getCurrentApplyAmt().longValue() -
					// total;
					// }

				}
			}
		}

	}

	/**
	 * 授信額度檔 QUOTAPPR(AS400-ELF383)
	 * 
	 * @param BRNID
	 *            AS400上傳分行代碼
	 * @param l160m01a
	 *            動審表
	 * 
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param l120m01b
	 *            額度種類檔
	 * @param l120s01as
	 *            借款人主檔
	 * @param l120s01cs
	 *            企金信用評等資料檔
	 * @param custDatas
	 *            暫存借款人國別資料
	 * @param updater
	 *            資料更新者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳AS400
	 * @param cntrNo
	 *            額度序號
	 */
	@SuppressWarnings("unused")
	private void uploadQuotappr(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, L120M01B l120m01b,
			List<L120S01C> l120s01cs,
			HashMap<String, HashMap<String, String>> custDatas, String updater,
			String sDate, Boolean upAS400, String cntrNo, L161S01A l161s01a)
			throws CapException {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);

		// J-112-0129_05097_B1001 Web
		// e-Loan系統對於符合單純僅有「交換票據抵用」會計科目之額度序號，不要帶入額度介面檔
		boolean isOnlyNoUploadItem = true;
		for (L140M01C l140m01c : l140m01a.getL140m01c()) {
			String item = l140m01c.getLoanTP();
			if (!this.isNoUploadQuotapprItem(item)) {
				isOnlyNoUploadItem = false;
				break;
			}
		}
		if (isOnlyNoUploadItem) {
			return;
		}

		String mainId = l140m01a.getMainId();
		String custId = l140m01a.getCustId();
		String dupNo = l140m01a.getDupNo();
		String custKey = StrUtils.concat(custId, dupNo);
		L140M01A l140m01aOld = null;
		if (UtilConstants.Cntrdoc.DataSrc.條件續約變更產生
				.equals(l140m01a.getDataSrc())) {
			l140m01aOld = l140m01aDao.findByMainId(l140m01a.getMainIdSrc());

		}
		if (l140m01aOld == null) {
			l140m01aOld = new L140M01A();
		}
		// 性質是否變動
		String lnFlag = "N";
		String isSpCase = ""; // 若為續約、展期、提前續約者，此值為Y
		String isSQCase = ""; // 若為紓困者，此值為Y
		HashMap<String, String> tempMap = new HashMap<String, String>();
		tempMap.put(UtilConstants.Cntrdoc.Property.新做,
				UtilConstants.Usedoc.upType.新作);
		tempMap.put(UtilConstants.Cntrdoc.Property.增額,
				UtilConstants.Usedoc.upType.增額);
		tempMap.put(UtilConstants.Cntrdoc.Property.減額,
				UtilConstants.Usedoc.upType.減額);
		tempMap.put(UtilConstants.Cntrdoc.Property.取消,
				UtilConstants.Usedoc.upType.取消);
		String tProperty = "";
		int property_count = 0;

		for (String f : l140m01a.getProPerty().split(
				UtilConstants.Mark.SPILT_MARK)) {
			// 之後在判斷是否為純續約, 展期, 提前續約用
			property_count += 1;
			if (tempMap.containsKey(f)) {
				tProperty = tempMap.get(f);
				break;
			}
			// 2.續約 9.展期(不良授信案) 11.提前續約
			if (UtilConstants.Cntrdoc.Property.續約.equals(f)
					|| UtilConstants.Cntrdoc.Property.展期.equals(f)
					|| UtilConstants.Cntrdoc.Property.提前續約者.equals(f)) {
				isSpCase = UtilConstants.DEFAULT.是;
			}

			if (UtilConstants.Cntrdoc.Property.紓困.equals(f)) {
				isSQCase = UtilConstants.DEFAULT.是;
			}
		}
		// 若為紓困則要檢核其為增額或減額
		if (UtilConstants.DEFAULT.是.equals(isSQCase)) {
			// 幣別一樣才做比較
			if (Util.trim(l140m01aOld.getCurrentApplyCurr()).equals(
					Util.trim(l161s01a.getCurrentApplyCurr()))) {

				if (this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
						.compareTo(
								this.gfnConvertZero(l140m01aOld
										.getCurrentApplyAmt())) == 1) {

					tProperty = UtilConstants.Usedoc.upType.增額;
				} else if (this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
						.compareTo(
								this.gfnConvertZero(l140m01aOld
										.getCurrentApplyAmt())) == -1) {
					tProperty = UtilConstants.Usedoc.upType.減額;
				}
			}

		}

		lnFlag = tProperty;
		if (LMSUtil.isContainValue(l140m01a.getProPerty(),
				UtilConstants.Cntrdoc.Property.不變)) {
			lnFlag = "X";
		}

		if (Util.equals(l161s01a.getUseSpecialReason(), "01")) {
			// 特殊修改-01.不動用，僅修改聯貸比率
			lnFlag = "U";
		}

		// 是否為純續約、展期、提前續約, 是者為Y
		String ChangeFlag1 = UtilConstants.DEFAULT.否;
		// 送保/不送保是否有異動
		String ChangeFlag2 = UtilConstants.DEFAULT.否;
		// 循環/不循環是否有異動
		String ChangeFlag3 = UtilConstants.DEFAULT.否;
		// 是否總額度不變但限額改變
		String ChangeFlag4 = UtilConstants.DEFAULT.否;
		// 保證成數是否變更
		String ChangeFlag5 = UtilConstants.DEFAULT.否;
		// 授信期間終止日是否變更
		String ChangeFlag6 = UtilConstants.DEFAULT.否;
		// 動用期間終止日是否變更
		String ChangeFlag7 = UtilConstants.DEFAULT.否;
		int ItemChange = 0;
		// --- (1) 檢查是否為 純續約、展期、提前續約 案件, 若是需則上傳到期日 ---
		if (UtilConstants.DEFAULT.是.equals(isSpCase) && property_count == 1) {
			ChangeFlag1 = UtilConstants.DEFAULT.是;
		} else {
			ItemChange += 1;
		}
		// --- (2) 檢查送保/不送保是否有異動 , 若是需上傳 ---
		if (Util.isNotEmpty(l140m01aOld.getHeadItem1())) {
			if (!l140m01a.getHeadItem1().equals(l140m01aOld.getHeadItem1())) {
				ChangeFlag2 = UtilConstants.DEFAULT.是;
			} else {
				ItemChange += 1;
			}
		}
		// ' --- (3) 檢查循環/不循環是否有異動 , 若是需上傳 ---
		if (Util.isNotEmpty(l140m01aOld.getReUse())) {
			if (!l140m01a.getReUse().equals(l140m01aOld.getReUse())) {
				ChangeFlag3 = UtilConstants.DEFAULT.是;
			} else {
				ItemChange += 1;
			}
		}
		// --- (4) 檢查是否總額度不變但限額改變 , 若是需上傳
		if (Util.isEmpty(l140m01aOld.getLnSubject())) {
			if (!l140m01a.getReUse().equals(l140m01aOld.getReUse())) {
				ChangeFlag4 = "";
				ItemChange += 1;
			}
		} else {
			ChangeFlag4 = UtilConstants.DEFAULT.是;
			ItemChange += 1;
		}
		// --- (5) 檢查保證成數是否變更 , 若是需上傳 ---
		if (Util.isNotEmpty(l140m01aOld.getHeadItem1())
				|| Util.isNotEmpty(l140m01aOld.getGutPercent())) {
			if (!l140m01a.getHeadItem1().equals(l140m01aOld.getHeadItem1())
					|| this.gfnConvertZero(l140m01a.getGutPercent()).compareTo(
							this.gfnConvertZero(l140m01aOld.getGutPercent())) != 0) {
				ChangeFlag5 = UtilConstants.DEFAULT.是;
				ItemChange += 1;
			}
		}

		// 循環/不循環是否變更
		String reclChg = "N"; // 新案2015.02改為 循環/不循環 ，A. 循環、B. 不循環
		// 送保註記是否變更
		String sguChg = "N";
		// 信保保證成數是否變更
		String gutFlag = "N";

		// OBU公司存續證明到期日
		String existdate = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? custDatas
				.get(custKey).get("existDate") : "0001-01-01";
		if (Util.equals(existdate, "")) {
			existdate = "0001-01-01";
		}

		// OBU公司繳交年費證明到期日
		String feedate = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? custDatas
				.get(custKey).get("feeDate") : "0001-01-01";
		if (Util.equals(feedate, "")) {
			feedate = "0001-01-01";
		}

		// OBU公司註冊國有效期
		String countrydt = l140m01a.getTypCd().equals(TypCdEnum.OBU.getCode()) ? custDatas
				.get(custKey).get("countryDate") : "0001-01-01";
		if (Util.equals(countrydt, "")) {
			countrydt = "0001-01-01";
		}

		// 本案是否有同業聯貸案額度
		String tmpUnitCase = "";
		String tmpMainBranch = "";
		String caseType = "";

		// Map<String, String> caseMap = lmsService.getCaseType("2",
		// l120m01b.getMainId(), l120m01b, l140m01a);
		// tmpUnitCase = caseMap.get("tmpUnitCase");
		// tmpMainBranch = caseMap.get("tmpMainBranch");
		// caseType = caseMap.get("caseType");

		Map<String, String> caseMap = lmsService.getCaseType("2", l161s01a,
				l140m01a);

		tmpUnitCase = l161s01a.getUnitCase();
		tmpMainBranch = l161s01a.getUCntBranch();
		caseType = l161s01a.getCaseType();

		// 加強檢核動審表同業參貸比例有沒有勾兆豐為主辦行，如果有且簽報書勾非主辦行，則改上傳QUOTAPPR時由參貸改為主辦
		// List<L161S01B> l161m01bs = l161m01bDao.findByMainId(l160m01a
		// .getMainId());

		Set<L161S01B> l161m01bs = l161s01a.getL161s01b();

		String hasMainFlag = "N";
		String slBankType = "";
		String slBank = "";
		String slMaster = "";
		for (L161S01B l161m01b : l161m01bs) {
			slBankType = l161m01b.getSlBankType();
			slBank = l161m01b.getSlBank();
			slMaster = l161m01b.getSlMaster();
			if (Util.equals(slBankType, "01")
					&& Util.equals(slBank, UtilConstants.兆豐銀行代碼)) {
				if (Util.equals(slMaster, "Y")) {
					hasMainFlag = "Y";
					break;
				}
			}
		}
		if (Util.equals(hasMainFlag, "Y")) {
			if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸參貸)) {
				caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦;
			} else if (Util.equals(caseType,
					UtilConstants.Usedoc.caseType.同業聯貸參貸含自行聯貸)) {
				caseType = UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸;
			}
		}

		// 合作業務種類
		String coKind = "";
		// 合作業務母戶額度序號
		String cntrnom = "";
		// 合作業務子戶代收帳號
		String rclno = "";
		if (l161s01a != null && !"0".equals(l161s01a.getCoKind())
				&& !Util.isEmpty(l161s01a.getCoKind())) {
			if (UtilConstants.DEFAULT.是.equals(l161s01a.getMCntrt())) {

				// caseType = UtilConstants.Usedoc.caseType.合作業務母戶;
			} else if (UtilConstants.DEFAULT.是.equals(l161s01a.getSCntrt())) {
				// caseType = UtilConstants.Usedoc.caseType.合作業務子戶;
				cntrnom = l161s01a.getMScntrt();
				rclno = Util.trimSizeInOS390(l161s01a.getMSAcc(), 14);
			} else {
				cntrnom = "";
				rclno = "";
			}
			coKind = l161s01a.getCoKind();
		}

		// 原請額度
		BigDecimal oldAmt = this.gfnConvertZero(l140m01aOld
				.getCurrentApplyAmt());
		// 現請額度
		BigDecimal curAmt = this.gfnConvertZero(l161s01a.getCurrentApplyAmt());
		// 現請額度幣別
		String curCurr = l161s01a.getCurrentApplyCurr();
		String oldCurr = Util.trim(l140m01aOld.getCurrentApplyCurr());

		/** ---------判斷科(子)目限額是否變更 --------------------------- */
		// 科(子)目限額是否變更
		String lnqtFlag = ChangeFlag4;
		/** ---判斷循環/不循環是否有異動 ------- */
		String reclFlag = "";
		String sguFlag = "";

		if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
			if (UtilConstants.Cntrdoc.ReUse.循環使用.equals(l140m01a.getReUse())) {
				// 新作循環額度
				reclFlag = "R";
			} else {
				// 新作不循環額度
				reclFlag = "N";
			}

		} else {
			reclChg = ChangeFlag3;
			if (UtilConstants.DEFAULT.是.equals(reclChg)) {
				String reuse = Util.trim(l140m01a.getReUse());
				String reuseBF = Util.trim(l140m01aOld.getReUse());
				if (UtilConstants.Cntrdoc.ReUse.循環使用.equals(reuse)
						&& UtilConstants.Cntrdoc.ReUse.不循環使用.equals(reuseBF)) {
					reclFlag = "C";
				}
				if (UtilConstants.Cntrdoc.ReUse.不循環使用.equals(reuse)
						&& UtilConstants.Cntrdoc.ReUse.循環使用.equals(reuseBF)) {
					reclFlag = "F";
				}
			}

		}

		// 2015-02-10 與曉曉討論循環/不循環是否變更改為直接帶循環/不循環就好
		if (Util.equals(Util.trim(l140m01a.getReUse()), "1")) {
			// 不循環
			reclChg = "B";
		} else if (Util.equals(Util.trim(l140m01a.getReUse()), "2")) {
			// 循環
			reclChg = "A";
		} else {
			// 維持舊案Y/N不變
		}

		/** -----判斷送保註記 --------------- */
		String headItem1 = Util.trim(l140m01a.getHeadItem1());
		String headItem1BF = Util.trim(l140m01aOld.getHeadItem1());

		// J-105-0203-002 配合a-Loan調整信保資料上傳判斷
		// 32 送保註記是否變更 SGUCHG CHAR(01) Y.是、N.否
		// =>改A.送保　B.不送保
		// 33 信保保證成數是否變更 GUTFLAG CHAR(01) Y.是、N.否
		// =>主機取消判斷
		// 34 信保保證成數 GUTPER DECIMAL(03) 一律寫入，不判斷是否有更改

		BigDecimal gutper = BigDecimal.ZERO;
		if (Util.equals(lmsService.getSysParamDataValue("LMS_J1050203002_ON"),
				"Y")) {
			if (UtilConstants.DEFAULT.是.equals(headItem1)) {
				// 送保
				sguChg = "A";
				gutFlag = "Y";
				gutper = this.gfnConvertZero(l140m01a.getGutPercent());
				if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
					sguFlag = "G";
				} else {
					sguFlag = "";
				}
			} else {
				// 不送保
				sguChg = "B";
				gutFlag = "";
				gutper = BigDecimal.ZERO;
				if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
					sguFlag = "N";
				} else {
					sguFlag = "R";
				}
			}
		} else {
			// 信保成數
			gutper = this.gfnConvertZero(l140m01a.getGutPercent());
			if (UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
				if (UtilConstants.DEFAULT.是.equals(headItem1)) {
					sguFlag = "G";
				} else {
					sguFlag = "N";
				}
			} else {
				sguChg = ChangeFlag2;
				if (UtilConstants.DEFAULT.是.equals(sguChg)) {
					if (UtilConstants.DEFAULT.否.equals(headItem1)
							&& UtilConstants.DEFAULT.是.equals(headItem1BF)) {
						sguFlag = "R";
					}
				}
			}

			/** -------判斷信保成數是否變更 ------- */
			if (!UtilConstants.Usedoc.upType.新作.equals(tProperty)) {
				if (UtilConstants.DEFAULT.是.equals(headItem1)) {
					gutFlag = ChangeFlag5;
				}
			}
		}

		/** ------------------契約書型態------------------ */
		String lrpType = Util.trim(l160m01a.getTType());
		/** ---------授信期間 ------------ */
		String llnNo = "";
		String llnfDate = "0001-01-01";
		String llneDate = "0001-01-01";
		int llnmon = 0;

		if (UtilConstants.Usedoc.TType.中長期.equals(lrpType)) {
			llnNo = Util.trim(l160m01a.getLnSelect());
			if ("1".equals(llnNo)) {// 授信期間為起迄日期
				llnfDate = CapDate.formatDate(l160m01a.getLnFromDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
				llneDate = CapDate.formatDate(l160m01a.getLnEndDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			} else if ("2".equals(llnNo)) {// 授信期間」自首動日起XX年XX月
				llnmon = l160m01a.getLnYear() * 12 + l160m01a.getLnMonth();
			} else {
				// 「授信期間」的其他 都帶預設值就好所以不做處理

			}
		} else {
			llnNo = "";
		}

		/** -------------動用期限------------- */
		String lnuseNo = Util.trim(l160m01a.getUseSelect());
		String useFmDt = "0001-01-01";
		String useEnDt = "0001-01-01";
		int useFtMn = 0;
		if ("1".equals(lnuseNo)) {
			if (Util.isNotEmpty((l160m01a.getUseFromDate()))) {
				useFmDt = CapDate.formatDate(l160m01a.getUseFromDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			}
			if (Util.isNotEmpty((l160m01a.getUseEndDate()))) {
				useEnDt = CapDate.formatDate(l160m01a.getUseEndDate(),
						UtilConstants.DateFormat.YYYY_MM_DD);
			}
		} else if ("2".equals(lnuseNo)) {
			useFtMn = l160m01a.getUseMonth();
		}
		// 若為取消則將動用終止日設為0001-01-01
		if (UtilConstants.Usedoc.upType.取消.equals(tProperty)) {
			useEnDt = "0001-01-01";
		}

		// --- (6) 檢查授信期間終止日是否變更 , 若是需上傳 ---
		// 授信期間－終止日期是否變更
		String llneFlag = "N";
		// 動用期限－終止日期是否變更
		String useeFlag = "N";
		// Map<String, Object> quotapp = misQuotapprService.findByKey(custId,
		// dupNo, cntrNo, sDate);
		// 2013-05-24_多加查詢條件 onlntime is not null or bthtime is not null
		Map<String, Object> quotapp = misQuotapprService.findByKey2(custId,
				dupNo, cntrNo);
		// ' 授信契約書型態
		String BF_LRPTYPE = "";
		// ' 中長期授信期間代碼
		String BF_LLNNO = "";
		// 中長期授信期間（授信期間－終止日期）
		String BF_LLNEDATE = "";
		// ' 授信期間－月
		int BF_LLNMON = 0;
		// ' 動用期限代碼
		String BF_LNUSENO = "";
		// ' 動用期限（動用期限－終止日期）
		String BF_USEENDT = "";
		// 動用期限－自首次動用日起Ｘ個月
		int BF_USEFTMN = 0;
		if (quotapp != null) {
			BF_LRPTYPE = Util.trim(quotapp.get("LRPTYPE"));
			BF_LLNNO = Util.trim(quotapp.get("LLNNO"));
			BF_LLNEDATE = Util.trim(quotapp.get("LLNEDATE"));

			BF_LLNMON = Util.parseInt(Util.trim(quotapp.get("LLNMON")));
			BF_LNUSENO = Util.trim(quotapp.get("LNUSENO"));
			BF_USEENDT = Util.trim(quotapp.get("USEENDT"));

			BF_USEFTMN = Util.parseInt(Util.trim(quotapp.get("USEFTMN")));
			// 判斷授信期間是否變動
			if ("2".equals(lrpType)) {
				// 授信契約書型態不符
				if (Util.notEquals(BF_LRPTYPE, lrpType)) {
					// 型態不符當為中長期時才要寫這個Flag
					llneFlag = "Y";
				} else {
					// 中長期要判斷授信期間
					if (Util.notEquals(BF_LLNNO, llnNo)) {
						llneFlag = "Y";
					} else {
						// 授信期間代碼相符時，判斷終止日期 OR 月份是否相符
						if ("1".equals(llnNo)) {
							if (Util.notEquals(useEnDt, BF_USEENDT)) {
								llneFlag = "Y";
							}
						} else if ("2".equals(llnNo)) {
							if (llnmon != BF_LLNMON) {
								llneFlag = "Y";
							}
						}

					}
				}
			}

			// 判斷動用期間是否變動
			if (Util.notEquals(BF_LNUSENO, lnuseNo)) {
				useeFlag = "Y";
			} else {
				if ("1".equals(lnuseNo)) {
					if (Util.notEquals(BF_USEENDT, useEnDt)) {
						useeFlag = "Y";
					}
				} else if ("2".equals(lnuseNo)) {
					if (BF_USEFTMN != useFtMn) {
						useeFlag = "Y";
					}
				}
			}

		}

		// 其他敘作條件動撥提示用語
		String memo = "";
		// 承諾事項
		String promise = "";
		// J-113-0035 承諾事項區分新舊版(以簽案createdate比較)
		String as400_chkDate = Util.trim(lmsService.getSysParamDataValue("J-113-0035_AS400CHKDATE"));
		Date createdate = l140m01a.getCreateTime();
		Date checkdate = CapDate.getDate(as400_chkDate, UtilConstants.DateFormat.YYYY_MM_DD);
		String as400_controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0035_AS400_ON"));
		boolean isnewpromise = false;
		if(createdate.after(checkdate) && Util.equals(UtilConstants.DEFAULT.是, as400_controlFlag)){
			isnewpromise = true;
		}

		String reViewDateKind = null;
		String reViewDate = null;
		String reViewChgKind = null;
		BigDecimal reViewChg1 = null;

		reViewDateKind = "";
		reViewDate = "0001-01-01";
		reViewChgKind = "";
		reViewChg1 = BigDecimal.ZERO;
		//J-113-0035 承諾事項區分新舊版
		if(isnewpromise){//新
			List<L140S12A> l140s12as = (List<L140S12A>) findListByMainId(L140S12A.class, mainId);
			if(l140s12as != null && !l140s12as.isEmpty()){
				Map<String, String> l140s12a_esgType = codeTypeService.findByCodeType(
						"l140s12a_esgType", LMSUtil.getLocale().toString());
				StringBuilder newmemo = new StringBuilder();//Util.trimSizeInOS390(l140m01b.getToALoan(), 60);
				StringBuilder newpromise = new StringBuilder();
				for (L140S12A l140s12a : l140s12as) {
					newpromise.append(l140s12a.getContentText());
					
					if(Util.isNotEmpty(l140s12a.getEsgType())){
						String[] esgtype = l140s12a.getEsgType().split(UtilConstants.Mark.SPILT_MARK);// |
						for(String detal : esgtype){
							String detailmsg = MapUtils.getString(l140s12a_esgType, Util.trim(detal), "");
							if(newmemo.length() > 0){
								newmemo.append("、");
							}
							newmemo.append(detailmsg);
						}
					}
					
				}
				if(newmemo.length() > 60){
					//L140S12A.message02=有關注意事項內容因字數限制，請至貸後管理平台查明。
					memo = Util.trimSizeInOS390(prop.getProperty("L140S12A.message02"), 60);
				}else{
					memo = Util.trimSizeInOS390(newmemo.toString().replaceAll("\r\n", " ").replaceAll("\r", " ").replaceAll("\n", " "), 60);
				}
				if(newpromise.length() > 300){
					//L140S12A.message01=有關承諾事項內容因字數限制，請至貸後管理平台查明。
					promise = Util.trimSizeInOS390(prop.getProperty("L140S12A.message01"), 750);
				}else{
					//換行符號會讓DW那邊吃有問題，需replace
					promise = Util.trimSizeInOS390(newpromise.toString().replaceAll("\r\n", " ").replaceAll("\r", " ").replaceAll("\n", " "), 750);
				}
				
			}
		}else{//舊
			L161S01C l161s01c = l161s01cDao.findByMainIdPidItemType(
					l161s01a.getMainId(), l161s01a.getUid(),
					UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語);
	
			if (l161s01c == null) {
				for (L140M01B l140m01b : l140m01a.getL140m01b()) {
					if (UtilConstants.Cntrdoc.l140m01bItemType.其他敘做條件_動撥提示用語
							.equals(l140m01b.getItemType())) {
						memo = Util.trimSizeInOS390(l140m01b.getToALoan(), 60);
						promise = Util.trimSizeInOS390(l140m01b.getItemDscr(), 750);
						break;
					}
				}
			} else {
	
				reViewDateKind = l161s01a.getReViewDateKind();
				if (Util.equals(reViewDateKind, "01")) {
					reViewDate = CapDate.formatDate(l161s01a.getReViewDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
					reViewChgKind = l161s01a.getReViewChgKind();
					if (Util.equals(reViewChgKind, "01")) {
						reViewChg1 = l161s01a.getReViewChg1();
					}
				}
	
				memo = Util.trimSizeInOS390(l161s01c.getToALoan(), 60);
				promise = Util.trimSizeInOS390(l161s01c.getItemDscr(), 750);
			}
		}
		/** ------授權等級 ------------ */
		String grantNo = "";

		if (UtilConstants.Casedoc.DocKind.授權內.equals(l120m01a.getDocKind())) {
			String authlvl = Util.trim(l120m01a.getAuthLvl());
			if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authlvl)
					|| UtilConstants.Casedoc.AuthLvl.總行授權內.equals(authlvl)) {
				grantNo = "9";
			} else {
				grantNo = "B";

			}

		} else if (UtilConstants.Casedoc.DocKind.授權外.equals(l120m01a
				.getDocKind())) {
			grantNo = Util.trim(l120m01a.getCaseLvl());
		} else {
			logger.info("缺少DocKind 欄位值目前值為======>" + l120m01a.getDocKind());
		}

		// String commborw = "";
		// if (l120s01as.size() > 1) {
		// commborw = UtilConstants.DEFAULT.是;
		// }
		//
		String commborw = "";
		for (L162S01A l162s01a : l160m01a.getL162S01A()) {
			// 檢查動審表主從債務人資料表，若有共同借款人、相同額度序號、不同關係人ID，則代表有共同借款人
			if (Util.equals(l162s01a.getRType(), UtilConstants.lngeFlag.共同借款人)
					&& Util.equals(cntrNo, l162s01a.getCntrNo())
					&& !(l162s01a.getCustId().equals(l162s01a.getRId()) && l162s01a
							.getDupNo().equals(l162s01a.getRDupNo()))

			) {
				commborw = UtilConstants.DEFAULT.是;
				break;
			}
		}

		String unichgFlag = "";
		/**
		 * <pre>
		 * static final String 一般貸款 = 6
		 * static final String 合作業務母戶 = 7
		 * static final String 合作業務子戶 =8
		 * </pre>
		 */
		if (!UtilConstants.Usedoc.caseType.一般貸款.equals(caseType)
				&& !UtilConstants.Usedoc.caseType.合作業務母戶.equals(caseType)
				&& !UtilConstants.Usedoc.caseType.合作業務子戶.equals(caseType)) {
			unichgFlag = UtilConstants.DEFAULT.是;
		} else {
			unichgFlag = UtilConstants.DEFAULT.否;
		}

		// 聯貸額度(包含同業)
		BigDecimal unionAmt = BigDecimal.ZERO;
		// if (cntrNo.equals(l160m01a.getL161S01A().getCntrNo())) {
		/*
		 * [下午 01:54:06] 曉曉: * 若 CASETYPE='2'( 同業主辦含聯行參貸 ) 若 UNLNTYPE='1'
		 * 同業參貸明細中有本行參貸明細 2 筆以上時，則 下傳全部的同業參貸明細，若否，則排除同業參貸明細本行部份，
		 * 其他同業皆下傳，並下傳聯行參貸明細
		 */
		if (Util.equals(caseType, UtilConstants.Usedoc.caseType.同業聯貸主辦)
				|| Util.equals(caseType,
						UtilConstants.Usedoc.caseType.同業聯貸主辦含自行聯貸)) {
			unionAmt = this.gfnConvertZero(l161s01a.getQuotaAmt()).setScale(0,
					BigDecimal.ROUND_HALF_UP);
			curAmt = this.gfnConvertZero(l161s01a.getQuotaAmt());
			curCurr = l161s01a.getQuotaCurr();
		} else {
			unionAmt = this.gfnConvertZero(l161s01a.getCurrentApplyAmt())
					.setScale(0, BigDecimal.ROUND_HALF_UP);
		}
		// }

		// 2012_05_23_建霖說 CURAMT 欄位上傳為以下規則
		// If DBCURAMT < DBUNIONAMT Then
		// DBCURAMT=gfnConvertZero(tdoc1.LoanTotamt(0))
		// End If

		// L161S01A l161s01a = l160m01a.getL161S01A();

		// if (l161s01a != null) {
		// // 當現請額度小於聯貸額度 ，且 額度序號 相同
		// if (curAmt.compareTo(unionAmt) == -1
		// && l161s01a.getCntrNo().equals(cntrNo)) {
		// curAmt = unionAmt;
		// curCurr = l161s01a.getQuotaCurr();
		// }
		// }

		// 本行額度=現請額度
		BigDecimal shareAmt = this
				.gfnConvertZero(l161s01a.getCurrentApplyAmt()).setScale(0,
						BigDecimal.ROUND_HALF_UP);

		// 報核方式
		String permitType = "";

		if (UtilConstants.BankNo.資訊處.equals(MegaSSOSecurityContext.getUnitNo())
				|| UtilConstants.BankNo.國金部.equals(MegaSSOSecurityContext
						.getUnitNo())) {
			if (l161s01a != null) {
				permitType = StrUtils.concat(Util.trim(l161s01a.getURP1()),
						Util.trim(l161s01a.getURP2()),
						Util.trim(l161s01a.getURP3()));
				if ("AAA".equals(permitType)) {
					permitType = "1";
				} else if ("AAB".equals(permitType)) {
					permitType = "2";
				} else if ("ABA".equals(permitType)) {
					permitType = "3";
				} else if ("ABB".equals(permitType)) {
					permitType = "4";
				} else if ("BAA".equals(permitType)) {
					permitType = "5";
				} else if ("BAB".equals(permitType)) {
					permitType = "6";
				} else if ("BBA".equals(permitType)) {
					permitType = "7";
				} else if ("BBB".equals(permitType)) {
					permitType = "8";
				} else {
					permitType = "";
				}

			}

		}

		String hideunion = "";
		String UArea = "";
		String unionRole = "";

		if (Util.equals(l161s01a.getUnitCase(), "Y")) {
			if (l161s01a != null) {
				hideunion = Util.trim(l161s01a.getUHideName());
				UArea = Util.trim(l161s01a.getUArea());
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCMainBranch())) {
					unionRole = "Y";
				} else {
					unionRole = " ";
				}
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCntBranch())) {
					unionRole += "L";
				} else {
					unionRole += " ";
				}
				if (UtilConstants.DEFAULT.是.equals(l161s01a.getUCMSBranch())) {
					unionRole += "C";
				} else {
					unionRole += " ";
				}
			}
		}

		String riskArea = Util.trim(l140m01a.getRiskArea());
		String setDate = null;
		if (Util.equals(l161s01a.getUnitCase(), "Y")) {
			setDate = CapDate.formatDate(l161s01a.getSignDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		if (Util.isEmpty(setDate)) {
			setDate = CapDate.formatDate(l120m01a.getEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		if (Util.isEmpty(setDate)) {
			setDate = CapDate.formatDate(l120m01a.getApproveTime(),
					UtilConstants.DateFormat.YYYY_MM_DD);
		}

		// 信用評等等級CHAR(2)
		String crdttbl = "";
		// 信用風險模型評等類別 CHAR(1)
		String mowType = "";
		// 信用風險模型評等等級 CHAR(2)
		String mowtbl1 = "";

		// 舊評等日期CHAR(07)
		String crdtYmd = "";
		// 舊評等日期 AS400 DECIMAL(8)
		BigDecimal forAS400crdtYmd = BigDecimal.ZERO;
		// 舊評等分行 CHAR(3)
		String crdtBr = "";
		// MOW 評等日期CHAR(07)
		String mowYmd = "";
		// MOW 評等日期AS400 DECIMAL(8)
		BigDecimal forAS400MowYmd = BigDecimal.ZERO;
		// MOW 評等分行
		String mowbr = "";
		// MOODY評等日期 DECIMAL(8)
		BigDecimal modyDate = BigDecimal.ZERO;
		// MOODY評等 CHAR(10)
		String moodyGrd = "";
		// SP評等日期 DECIMAL(8)
		BigDecimal spDate = BigDecimal.ZERO;
		// SP評等 CHAR(10)
		String spGrd = "";
		// FITCH評等日期 DECIMAL(8)
		BigDecimal fitchDate = BigDecimal.ZERO;
		// FITCH評等 CHAR(10)
		String fitchGrd = "";
		String crdType = "";
		String grade = "";

		String l120s01cCustKey = "";
		for (L120S01C l120s01c : l120s01cs) {
			l120s01cCustKey = l120s01c.getCustId() + l120s01c.getDupNo();
			if (custKey.equals(l120s01cCustKey)) {
				crdType = Util.trim(l120s01c.getCrdType());
				grade = Util.trimSizeInOS390(Util.trim(l120s01c.getGrade()), 2);
				if (crdType.startsWith("M")) {
					// 信用風險模型評等類別
					mowType = crdType.substring(1, 2);
					mowYmd = this.paresTOTWDate(l120s01c.getCrdTYear());
					// 信用風險模型評等等級
					mowtbl1 = grade;
					mowbr = Util.trimSizeInOS390(l120s01c.getCrdTBR(), 3);

				} else if (UtilConstants.Casedoc.CrdType.DBU大型企業
						.equals(crdType)
						|| UtilConstants.Casedoc.CrdType.DBU中小型企業
								.equals(crdType)
						|| UtilConstants.Casedoc.CrdType.海外.equals(crdType)) {

					if (UtilConstants.Casedoc.CrdType.DBU大型企業.equals(crdType)
							&& "NA".equals(grade)) {
						// 當為DB且為 分數為NA 此值上傳空白
						crdType = "";
						crdttbl = "";
						crdtBr = "";
						crdtYmd = "";
					} else {
						crdttbl = grade;
						crdtBr = Util.trimSizeInOS390(l120s01c.getCrdTBR(), 3);
						crdtYmd = this.paresTOTWDate(l120s01c.getCrdTYear());
					}
				}
				if (UtilConstants.Casedoc.CrdType.MOODY.equals(crdType)) {
					moodyGrd = grade;
					modyDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
				}
				if (UtilConstants.Casedoc.CrdType.SAndP.equals(crdType)) {
					spGrd = grade;
					spDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
				}
				if (UtilConstants.Casedoc.CrdType.Fitch.equals(crdType)) {
					fitchDate = new BigDecimal(CapDate.formatDate(
							l120s01c.getCrdTYear(), "yyyyMMdd"));
					fitchGrd = grade;
				}
			}
		}
		// 聯貸信保註記
		String syndipfd = Util.trim(l140m01a.getSyndIPFD());
		// 簽報書案號-> 民國年 + 分行別+LMS+末五碼流水號
		String documentNo = LMSUtil.getUploadCaseNo(l120m01a);

		// 額度控管種類
		String factType = Util.trim(l161s01a.getSnoKind());
		// 是否有共用額度序號
		String commsno = Util.trim(l140m01a.getCommSno());
		if (Util.isNotEmpty(commsno)) {
			commsno = UtilConstants.DEFAULT.是;
		} else {
			commsno = "";
		}

		// 銀行法,金控法44,45
		String liHaiBank = "";
		String liHai44 = "";
		String liHai45 = "";
		String caseMainId = l120m01a.getMainId();
		String docType = l120m01a.getDocType();
		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
			// 企金
			L120S01D l120s01d = l120s01dDao.findByUniqueKey(caseMainId, custId,
					dupNo);
			if (l120s01d != null) {
				liHaiBank = Util.trim(l120s01d.getMbRlt());
				liHai44 = Util.trim(l120s01d.getMhRlt44());
				liHai45 = Util.trim(l120s01d.getMhRlt45());
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(docType)) {
			// 個金
			C120S01E c120s01e = c120s01eDao.findByUniqueKey(caseMainId, custId,
					dupNo);
			if (c120s01e != null) {
				liHaiBank = Util.trim(c120s01e.getIsQdata2());
				liHai44 = Util.trim(c120s01e.getIsQdata3());
				liHai45 = Util.trim(c120s01e.getIsQdata16());
			}
		}

		// 是否為銀行法或金控法利害關係人
		// String reFlag = UtilConstants.DEFAULT.否;
		// // J-104-0219-001
		// // 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		// String unsecureFlag = "";
		// if ("1".equals(liHaiBank) || "1".equals(liHai44) ||
		// "1".equals(liHai45)) {
		// reFlag = UtilConstants.DEFAULT.是;
		//
		// // J-104-0219-001
		// // 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		// unsecureFlag = Util.trim(l140m01a.getUnsecureFlag());
		//
		// } else {
		// reFlag = UtilConstants.DEFAULT.否;
		// }

		// J-105-0250-001 Web e-Loan 新增利害關係人檢核
		/*
		 * 補充說明如下 ELF500 新增２個欄位 ● elf500_unsecurefla => 比照 elf383 的欄位 ●
		 * elf500_notvalid => 當企金簽報書含個人戶的額度，為免 ELF383, ELF500 存在相同的額度, a-loan
		 * 不曉得哪一筆是最新的資料 故增加此欄位來判斷
		 * 
		 * 
		 * 銀行法 金控44 金控45 －－ －－－ －－－ －－－ 主借人 甲 乙 丙 共借人 X Y
		 * 
		 * elf383_reflag ,elf500_relate原本的值, 只有{Y,N} {Y,N}代表［主借人］，是否是［銀行法 or
		 * 金控44 or 金控45］關係人，判斷的範圍是上圖的［甲、乙、丙］
		 * 
		 * 此案上線後, 企金／消金動審表上傳的值, 將更改為{A, B} {A, B}代表［主借人、共借人］，是否是［銀行法 or 金控44
		 * ］關係人，判斷的範圍是上圖的［甲、乙、X、Y］ 之後中心引進「簽案時是否為利害關係人」時，會把”A”改”Y”,”B”改”N”
		 */

		String reFlag = "B"; // ELF383 REFLAG
		String unsecureFlag = ""; // ELF383 UNSECUREFLAG
		if (LMSUtil.isOverSea_CLS(l120m01a)) {

			boolean cls_is_relate = clsService
					.cls_is_relate(l120m01a, l140m01a);
			if (cls_is_relate) {
				reFlag = "A";
			}
			unsecureFlag = Util.trim(l140m01a.getUnsecureFlag());

		} else {

			List<L120S01D> l120s01ds = lms1405Service
					.getCntrDocAllBorrowerL120S01D(l140m01a);

			for (L120S01D tl120s01d : l120s01ds) {
				if (LMSUtil.isUnsecureFlag(tl120s01d)) {

					reFlag = "A";

					if (lms1405Service.chkCntrDocHasUnSecureSubject(l140m01a,
							l140m01a.getSbjProperty(), new String[] { "321" })) {
						unsecureFlag = Util.trim(l140m01a.getUnsecureFlag());
					}

					break;

				}
			}
		}

		// 不計入授信項目代號
		String lnnoflag = "";
		// J-104-0219-001
		// 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
		lnnoflag = Util.trim(l140m01a.getNoLoan());

		// 不是同業聯貸案，所以同業聯貸相關欄位都不應該有值
		// (UNIONAMT,SHAREAMT,UNICHGFLAG ,HIDEUNION ,SETDATE ,UNIONAREA
		// ,UNIONROLE)
		if (UtilConstants.Usedoc.caseType.一般貸款.equals(caseType)) {
			unionAmt = BigDecimal.ZERO;
			shareAmt = BigDecimal.ZERO;
			unichgFlag = "";
			hideunion = "";
			setDate = "0001-01-01";
			UArea = "";
			unionRole = "";
		}

		// 國內新增上傳欄位

		// 借款人有無赴大陸投資 CHAR(01) Y/N
		String chinaivt = "";
		// 赴大陸投資金額幣別 CHAR(03)
		String chinacur = "";
		// DECIMAL(15,2) 仟元
		BigDecimal chinaamt = BigDecimal.ZERO;
		// 經濟部投審會核准金額(TWD) DECIMAL(15,2) TWD 仟元
		BigDecimal signamt = BigDecimal.ZERO;

		String noisurea = Util.trim(l140m01a.getNoInsuReason());
		// 未送信保-擔保率超過% DECIMAL(5,2) %
		BigDecimal noisuort = BigDecimal.ZERO;
		// 未送信保-其他 VCHAR(1024) 經辦輸入描述 國內新增上傳欄位
		String noisudesp = "";
		if ("4".equals(noisurea)) {
			noisuort = Util.parseBigDecimal(l140m01a.getNoInsuReasonOther());
		} else if ("7".equals(noisurea)) {
			noisudesp = l140m01a.getNoInsuReasonOther();
		}

		// 央行購住/空地/建屋貸款註記
		String controlcd = "";
		// 是否所有權取得日期在99/6/25以後或99/6/25後曾受央行管制客戶之增貸或轉貸案
		String duringFlag = "";
		// 貸款成數
		BigDecimal ltvRate = BigDecimal.ZERO;
		// 擔保品座落地區別
		String locationcd = "";
		// 本次聯徵查詢名下有其他房貸資料
		String jcicMark = "";
		// 屬央行控管對象者需要選擇一個理由
		String plusreason = "";

		// 按轉讓發票金額 % 動用
		BigDecimal loanPer = BigDecimal.ZERO;

		// 額度控管種類為6開頭的(應收帳款相關)才需上傳 loanPer
		if (Util.equals(Util.getLeftStr(l161s01a.getSnoKind(), 1), "6")) {
			loanPer = Util.parseBigDecimal(l140m01a.getLoanPer());
		}

		// 先刪除此筆資料後再新增
		// misQuotapprService.delByUniqueKey(custId, dupNo, cntrNo, sDate);

		if (l160m01a.getDocStatus()
				.equals(CreditDocStatusEnum.海外_已核准.getCode())) {
			// 重新上傳MIS
			misQuotapprService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
		} else {
			// misQuotapprService.delByUniqueKey(custId, dupNo, cntrNo, sDate);
			misQuotapprService.delByUniqueKeyWithoutONLNTIME(custId, dupNo,
					cntrNo, sDate);
		}

		// 調整 USEEFLAG LLNEFLAG，因為不論與ELOAN比或是與ALOAN比都無法正確比出有無變更，故與小小討論後改以下列方式判斷
		if (!Util.equals(lnFlag, "N") && !Util.equals(lnFlag, "U")) { // N= 新作
			if ((!Util.equals(useFmDt, "0001-01-01") && !Util.equals(useEnDt,
					"0001-01-01")) || useFtMn > 0) {
				useeFlag = "Y";
			}
			if ((!Util.equals(llnfDate, "0001-01-01") && !Util.equals(llneDate,
					"0001-01-01")) || llnmon > 0) {
				llneFlag = "Y";
			}
		}

		// J-103-0202-005 遠匯換匯比照選擇權，以交易額度(名目本金*風險係數)來簽案
		// 風險係數 RISKFACTORS
		// 名目額度 RISKFACTAMT
		// BigDecimal shareAmt =
		// this.gfnConvertZero(l161s01a.getCurrentApplyAmt()).setScale(0,
		// BigDecimal.ROUND_HALF_UP);
		Boolean hasDerivateSubjectFlag = false;
		BigDecimal riskFactors = BigDecimal.ZERO; // 風險係數
		BigDecimal riskActAmt = BigDecimal.ZERO; // 名目額度
		String derivativesNumDscr = Util.trim(l140m01a.getDerivativesNumDscr());

		if (Util.equals(l161s01a.getIsDerivatives(), UtilConstants.DEFAULT.是)) {
			boolean fxTradeEffectFlag = lmsService.chkFxTradeEffect();

			BigDecimal maxFactors = BigDecimal.ZERO;
			BigDecimal tmpFactors = BigDecimal.ZERO;

			if (Util.notEquals(derivativesNumDscr, "")) {
				// 取得最大的風險係數
				String[] dervArr = derivativesNumDscr
						.split(UtilConstants.Mark.SPILT_MARK);
				for (String dervItem : dervArr) {
					tmpFactors = Util.parseBigDecimal(dervItem);
					if (maxFactors.compareTo(tmpFactors) < 0) {
						maxFactors = tmpFactors;
					}
				}

				if (Util.equals(l161s01a.getDervApplyAmtType(), "")) {
					// L160M01A.cntrInfo=額度動用資訊
					// L160M01A.message75=尚有必填欄位未填
					// L160M01A.dervApplyAmtType=衍生性商品現請額度種類

					throw new CapMessageException(
							prop.getProperty("L160M01A.cntrInfo")
									+ l161s01a.getCntrNo()
									+ prop.getProperty("L160M01A.message75")
									+ "：「"
									+ prop.getProperty("L160M01A.dervApplyAmtType")
									+ "」", getClass());
				}

				if (maxFactors.compareTo(BigDecimal.ZERO) > 0) {
					riskFactors = maxFactors;
					BigDecimal divdFactors = maxFactors.divide(
							BigDecimal.valueOf(100), 10,
							BigDecimal.ROUND_HALF_UP);
					if (Util.equals(l161s01a.getDervApplyAmtType(), "1")) {
						// 現請額度為
						// 授權額度/交易額度(已乘上信用轉換係數/風險係數)**************************
						if (fxTradeEffectFlag == true) {
							// 生效後 curAmt = 交易額度 riskActAmt = 名目額度
							// 名目額度 =現請額度(交易額度) / 風險係數
							riskActAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).divide(
									divdFactors, 2, BigDecimal.ROUND_HALF_UP);
						} else {
							// 生效前 curAmt = 名目額度 riskActAmt = 交易額度
							curAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).divide(
									divdFactors, 2, BigDecimal.ROUND_HALF_UP);
							riskFactors = BigDecimal.ZERO;
							riskActAmt = BigDecimal.ZERO;
						}
					} else {
						// 現請額度為名目額度*************************************************************
						if (fxTradeEffectFlag == true) {
							// 生效後 curAmt = 交易額度 riskActAmt = 名目額度
							// 現請額度改為交易額度 =>名目額度 * 風險係數
							// 遠匯沒有聯貸，所以現請額度不用考量可能為聯貸總額度之情形
							curAmt = this
									.gfnConvertZero(
											l161s01a.getCurrentApplyAmt())
									.multiply(divdFactors)
									.setScale(2, BigDecimal.ROUND_HALF_UP);
							// 名目額度 =現請額度
							riskActAmt = this.gfnConvertZero(
									l161s01a.getCurrentApplyAmt()).setScale(2,
									BigDecimal.ROUND_HALF_UP);
						} else {
							// 生效前 curAmt = 名目額度 riskActAmt = 交易額度
							riskFactors = BigDecimal.ZERO;
							riskActAmt = BigDecimal.ZERO;
						}

					}

				} else {
					// L160M01A.message89=額度明細表【選擇權、換匯換利交易、換利交易、遠匯、換匯】科目無對應之風險係數，不得動用!!
					if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
							.getProPerty())
							|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
						throw new CapMessageException(
								prop.getProperty("L160M01A.message89"),
								getClass());
					}

				}
			} else {
				// 沒有風險係數
				boolean isProPerty7 = LMSUtil.isContainValue(
						Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.不變);
				if (!UtilConstants.Cntrdoc.Property.不變.equals(l140m01a
						.getProPerty())
						|| Util.equals(l161s01a.getUseSpecialReason(), "01")) {
					throw new CapMessageException(
							prop.getProperty("L160M01A.message89"), getClass());
				}
			}

		}

		// J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
		String isEfin = "";
		if (Util.notEquals(factType, "")) {
			if (Util.equals(Util.trim(l140m01a.getIsEfin()), "Y")) {
				if (Util.equals(factType.substring(0, 1), "6")) {
					isEfin = Util.trim(l140m01a.getIsEfin());
				}
			}
		}

		String prodKind = Util.trim(l140m01a.getLnType());
		if (Util.equals(prodKind, "00")) {
			prodKind = "";
		}

		// J-107-0357_05097_B1001 Web
		// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
		String projClass = Util.trim(l140m01a.getProjClass());
		if (Util.equals(projClass, "00")) {
			projClass = "";
		}

		// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
		// 海外目前沒有這個欄位
		String isNonSMEProjLoan = "";
		BigDecimal nonSMEProjLoanAmt = BigDecimal.ZERO;

		// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
		// 海外目前沒有這個欄位
		String inSmeFg = "";
		BigDecimal inSmeToAmt = BigDecimal.ZERO;
		BigDecimal inSmeCaAmt = BigDecimal.ZERO;

		// J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
		// _ 為N.A. 上傳時改為""
		String exceptFlag = Util.trim(l140m01a.getExceptFlag())
				.replace("_", "");

		// J-107-0357_05097_B1001 Web
		// e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
		// J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
		// J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
		// 新創重點產業
		String itwCode = "";
		if (Util.equals(Util.trim(l140m01a.getIsStartUp()), "N")) {
			if (Util.equals(Util.trim(projClass), "05")) {
				itwCode = Util.trim(l140m01a.getItwCode());
			}
		} else {
			if (Util.equals(Util.trim(projClass), "05")) {
				projClass = "";
			}
		}

		// J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
		// 六大核心戰略產業
		String itwCodeCoreBuss = "";
		if (Util.equals(Util.trim(l140m01a.getIsCoreBuss()), "Y")) {
			itwCodeCoreBuss = "";
		} else if (Util.equals(Util.trim(l140m01a.getIsCoreBuss()), "N")) {
			itwCodeCoreBuss = Util.trim(l140m01a.getItwCodeCoreBuss());
			if (Util.equals(Util.trim(itwCodeCoreBuss), "00")) {
				itwCodeCoreBuss = "";
			}
		}

		// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
		String isHedge = "";
		BigDecimal enhanceAmt = BigDecimal.ZERO;
		String netSwft = "";
		BigDecimal netAmt = BigDecimal.ZERO;
		BigDecimal netAmtUnit = BigDecimal.ZERO;

		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {
			// 企金
			isHedge = Util.trim(lmsService
					.getDerivateSubjectHedgeKinde(l140m01a));
			if (Util.equals(isHedge, "2")) {
				enhanceAmt = l140m01a.getEnhanceAmt() == null ? BigDecimal.ZERO
						: l140m01a.getEnhanceAmt();

				L120S01B l120s01b = l120s01bDao.findByUniqueKey(caseMainId,
						custId, dupNo);
				if (l120s01b != null) {
					netSwft = Util.trim(l120s01b.getNetSwft());
					netAmt = l120s01b.getNetAmt() == null ? BigDecimal.ZERO
							: l120s01b.getNetAmt();
					netAmtUnit = l120s01b.getNetAmtUnit() == null ? BigDecimal.ZERO
							: l120s01b.getNetAmtUnit();
				}
			}
		}

		// J-107-0137_05097_B1001 Web
		// e-Loan企金授信額度明細表，新增「信保基金保證書發文日期」與「信保基金核准之保證手續費率」
		BigDecimal cgfRate = BigDecimal.ZERO;
		String cgfDate = null;
		if (UtilConstants.Casedoc.DocType.企金.equals(docType)) {

			String newHeadItem = Util.trim(l140m01a.getHeadItem1());
			if (UtilConstants.DEFAULT.是.equals(newHeadItem)) {
				cgfRate = l140m01a.getCgfRate() == null ? BigDecimal.ZERO
						: l140m01a.getCgfRate();

				if (l140m01a.getCgfDate() != null) {
					cgfDate = CapDate.formatDate(l140m01a.getCgfDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
				}
			}

		}

		// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);
		// J-108-0302 是否符合出口實績規範 942
		String experf_fg = "";
		String flaw_fg = ""; // 瑕疵額度控管方式
		BigDecimal flaw_amt = BigDecimal.ZERO; // 瑕疵額度限額
		String[] sysExperf = StringUtils.split(
				Util.trim(lmsService.getSysParamDataValue("EXPERF")), ",");
		for (String key : newList.keySet()) {
			if (Arrays.asList(sysExperf).contains(key)) {
				experf_fg = Util.trim(l140m01a.getExperf_fg());
				flaw_fg = Util.trim(l140m01a.getFlaw_fg());
				flaw_amt = Util.parseBigDecimal(l140m01a.getFlaw_amt());
				break;
			}
		}

		// J-109-0235_05097_B1001 Web e-loan國內企金授信新增兆元振興融資方案
		String isRevive = "";
		String revTarget = "";
		String revSubItem = "";
		String revPurpose = "";

		// J-110-0281_05097_B1001 Web
		// e-Loan授信配合「公股攜手，兆元振興」融資方案已結案，謹申請取消e-LOAN簽報書相關欄位之顯示
		if (lmsService.needShowIsRevive(l120m01a)) {
			isRevive = Util.trim(l140m01a.getIsRevive());
			if (Util.equals(isRevive, "Y")) {
				revTarget = Util.trim(l140m01a.getReviveTarget());
				if (Util.equals(revTarget, "01")) {
					revSubItem = Util.trim(l140m01a.getReviveCoreIndustry());
				} else if (Util.equals(revTarget, "02")) {
					revSubItem = Util.trim(l140m01a.getReviveChain());
				}

				revPurpose = Util.trim(l140m01a.getReviveLoanPurpose());
			}
		}

		// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
		String esggFg = "";
		String esggType = "";
		String esgsFg = "";
		String esgsType = "";
		String esgsUnre = "";

		esggFg = Util.trim(l140m01a.getIsEsgGreenLoan());
		if (Util.equals(esggFg, "Y")) {
			esggType = Util.trim(l140m01a.getEsgGreenSpendType());
		}

		esgsFg = Util.trim(l140m01a.getEsgSustainLoan());
		if (Util.equals(esgsFg, "Y")) {
			esgsType = Util.trim(l140m01a.getEsgSustainLoanType());
			if (Util.notEquals(esgsType, "")) {
				esgsType = lmsService
						.convertEsgSustainLoanTypeToAloan(esgsType);
			}
			esgsUnre = Util.trim(l140m01a.getEsgSustainLoanUnReach());
		}

		// G-111-0168_05097_B1001 新增海外分(子)行「綠色授信」及「永續績效連結授信」等註記
		// 海外ESG要多傳
		BigDecimal esgGreenSpendType1 = BigDecimal.ZERO;
		BigDecimal esgGreenSpendType2 = BigDecimal.ZERO;
		BigDecimal esgGreenSpendType3 = BigDecimal.ZERO;

		if ((LMSUtil.isContainValue(Util.trim(l140m01a.getEsgGreenSpendType()),
				"A"))) {
			// L140M01a.esgGreenSpendType1=發電站
			esgGreenSpendType1 = l140m01a.getEsgGreenSpendType1() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType1();
			// L140M01a.esgGreenSpendType2=裝置容量
			esgGreenSpendType2 = l140m01a.getEsgGreenSpendType2() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType2();
			// L140M01a.esgGreenSpendType3=裝置發電量
			esgGreenSpendType3 = l140m01a.getEsgGreenSpendType3() == null ? BigDecimal.ZERO
					: l140m01a.getEsgGreenSpendType3();
		}
		
		//J-113-0377 海外分(子)行企金授信新增社會責任授信
		String socialFlag = "";
		String socialKind = "";
		String socialTa = "";
		String socialResp = "";
		
		socialFlag = Util.trim(l140m01a.getSocialLoanFlag());
		if("Y".equals(socialFlag)){
			socialKind = Util.trim(l140m01a.getSocialKind());
			socialTa = Util.trim(l140m01a.getSocialTa());
			socialResp = Util.trim(l140m01a.getSocialResp());
		}

		// J-111-0197_05097_B1001 Web e-Loan國內企金系統於計算LGD時，若於擔保品系統建有存款或自建擔保品有存款者,
		// 增加上送徵提存款設質擔保註記供ALOAN L550交易引進
		// J-111-0208_05097_B1001 Web
		// e-Loan國內外企金授信簽報，各額度LGD隨核定內容上送ALOAN、AS400(ELF383)
		boolean showLgdEffect = lmsService.showPanelLms140s08(l120m01a,
				l120m01a.getCaseBrId());
		BigDecimal cntrLgd = null;
		String stdAuth = "";
		String depositFg = "";
		if (showLgdEffect) {
			L120S21B l120s21b = l120s21bDao.findByIndex02(l120m01a.getMainId(),
					cntrNo);
			if (l120s21b != null) {
				if (l120s21b.getExpectLgd() != null) {
					// J-111-0400_05097_B1001 Web e-Loan企金授信增修LGD及額度暴險估算規則
					if (l120s21b.getLgdVer1() != null
							&& l120s21b.getLgdVer1() > 0) {
						// 大版是0為LGD試算，不要上傳
						cntrLgd = l120s21b.getExpectLgd().min(
								new BigDecimal(100));
					}
				}
			}

			stdAuth = Util.trim(l140m01a.getIsStandAloneAuth());

			List<L120S21C> l120s21cs = l120s21cDao.findByIndex03(
					l120m01a.getMainId(), cntrNo, null);
			if (l120s21cs != null && !l120s21cs.isEmpty()) {
				depositFg = "N";
				for (L120S21C l120s21c : l120s21cs) {
					if (Util.equals(Util.trim(l120s21c.getColKind_s21c()),
							"030100")) {
						depositFg = "Y";
						break;
					}
				}
			}

			if (Util.notEquals(depositFg, "Y")) {
				// 額度明細表擔保品
				List<L140M01O> l140m01os = l140m01oDao.findByMainId(l140m01a
						.getMainId());
				if (l140m01os != null && !l140m01os.isEmpty()) {

					for (L140M01O l140m01o : l140m01os) {
						if (Util.equals(Util.getLeftStr(
								Util.trim(l140m01o.getCollNo()), 5), "03-01")) {
							depositFg = "Y";
							break;
						}
					}

				}

			}

		}

		// J-111-0461_05097_B1002 授信額度合計新增瑕疵押匯額度、與合計總授信額度(授信額度+出口瑕疵額度)
		// J-113-0377 海外分(子)行企金授信新增社會責任授信
		misQuotapprService.insert(custId, dupNo, cntrNo, sDate, caseType,
				lnFlag, oldAmt, curAmt, oldCurr, curCurr, lnqtFlag, reclFlag,
				sguFlag, lrpType, llnNo,

				llnfDate, llneDate, llnmon, lnuseNo, useFmDt, useEnDt, useFtMn,
				memo, grantNo, commborw, updater, reclChg, sguChg, gutFlag,
				gutper, llneFlag, useeFlag, lnnoflag, unichgFlag, reFlag,
				unionAmt, shareAmt, permitType, hideunion, setDate, UArea,
				unionRole, riskArea, existdate, feedate, countrydt, crdttbl,
				mowType, mowtbl1, syndipfd, coKind, cntrnom, rclno, documentNo,
				crdtYmd, crdtBr, mowYmd, mowbr, controlcd, duringFlag, ltvRate,
				locationcd, jcicMark, promise, factType, commsno, riskFactors,
				riskActAmt, reViewDate, reViewChg1, unsecureFlag, isEfin,
				prodKind, isNonSMEProjLoan, nonSMEProjLoanAmt, exceptFlag,
				itwCode, inSmeFg, inSmeToAmt, inSmeCaAmt, isHedge, enhanceAmt,
				netSwft, netAmt, netAmtUnit, cgfRate, cgfDate, projClass,
				isRevive, revTarget, revSubItem, revPurpose, itwCodeCoreBuss,
				esggFg, esggType, esgsFg, esgsType, esgsUnre, cntrLgd, stdAuth,
				depositFg, experf_fg, flaw_fg, flaw_amt,
				socialFlag, socialKind, socialTa, socialResp);

		if (upAS400) {
			// AS400無下列三欄 promise factType commsno
			obsdbELF383Service.deleteByKey(BRNID, custId, dupNo, cntrNo,
					sDate.replaceAll("\\D", ""));
			obsdbELF383Service.insert(
					BRNID,
					LMSUtil.covertAs400Time(new Object[] { custId, dupNo,
							cntrNo, sDate, caseType, lnFlag, oldAmt, curAmt,
							oldCurr, curCurr, lnqtFlag, reclFlag, sguFlag,
							lrpType,

							llnNo, llnfDate, llneDate, llnmon, lnuseNo,
							useFmDt, useEnDt, useFtMn,
							Util.trimSizeInOS390(memo, 40), grantNo, commborw,
							updater, CapDate.getCurrentTimestamp(), reclChg,
							sguChg, gutFlag, gutper,

							llneFlag, useeFlag, lnnoflag, unichgFlag, reFlag,
							unionAmt, shareAmt, permitType, hideunion, setDate,
							UArea, unionRole, riskArea, existdate, feedate,

							countrydt, crdttbl, mowType, mowtbl1, syndipfd,
							coKind, cntrnom, rclno, documentNo,
							forAS400crdtYmd, crdtBr, forAS400MowYmd, mowbr,
							modyDate, moodyGrd, spDate, spGrd, fitchDate,
							fitchGrd, controlcd, duringFlag, ltvRate,
							locationcd, jcicMark, riskFactors, riskActAmt,
							unsecureFlag, exceptFlag, isHedge, enhanceAmt,
							netSwft, netAmt, netAmtUnit, isRevive, revTarget,
							revSubItem, revPurpose, esggFg, esggType, esgsFg,
							esgsType, esgsUnre, esgGreenSpendType1,
							esgGreenSpendType2, esgGreenSpendType3,
							(cntrLgd == null ? -999.99 : cntrLgd), stdAuth,
							experf_fg, flaw_fg, flaw_amt,
							socialFlag, socialKind, socialTa, socialResp}));
		}

	}

	/**
	 * 上傳科(子)目及其限額檔QUOTSUB(AS400-ELF384)
	 * 
	 * @param BRNID
	 *            目前上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            案件簽報書
	 * @param updater
	 *            上傳者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳AS400
	 * @param codeTypeMap
	 *            授信科目代碼轉會計科目
	 * @param cntrNo
	 *            額度序號
	 */

	private void uploadQuotsub(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater, String sDate,
			Boolean upAS400, Map<String, String> codeTypeMap, String cntrNo) {
		lmsService.gfnQUOTSUBProcess(l140m01a, sDate, cntrNo, upAS400, updater,
				BRNID);
		L140M01A l140m01aOld = null;
		if (UtilConstants.Cntrdoc.DataSrc.條件續約變更產生
				.equals(l140m01a.getDataSrc())) {
			l140m01aOld = l140m01aDao.findByMainId(l140m01a.getMainIdSrc());

		}
		HashMap<String, String> newList = LMSUtil.getItemList(l140m01a);
		// 變更前項目
		HashMap<String, String> oldList = LMSUtil.getItemList(l140m01aOld);

		// (1)上傳授信科目
		for (L140M01C l140m01c : l140m01a.getL140m01c()) {
			String item = l140m01c.getLoanTP();
			String as400item = l140m01c.getLoanTP();

			BigDecimal lmtDays = Util.parseBigDecimal(l140m01c.getLmtDays());

			String lmtOther = Util.trim(l140m01c.getLmtOther());

			if (Util.equals(lmtOther, "")) {
				if (lmtDays.compareTo(BigDecimal.ZERO) == 0) {
					lmtDays = BigDecimal.valueOf(999);
				}

				if (lmtDays.compareTo(BigDecimal.valueOf(999)) > 0) {
					lmtDays = BigDecimal.valueOf(999);
				}

			} else {
				lmtDays = BigDecimal.valueOf(999);
			}

			if (this.isNoUploadItem(item)) {
				continue;
			}
			item = item.substring(0, 3);
			// AS400上傳授信科目的部分要轉為會計科目 沒有科目的要帶8個0
			if (codeTypeMap.containsKey(item)) {
				as400item = codeTypeMap.get(item);
			} else {
				as400item = "00000000";
			}

			/**
			 * <pre>
			 *                               'Msgbox "(1) 申請科目 =" & Condition9
			 *                         T_OLDCURR =""     ' 原限額幣別
			 *                         T_OLDQUOTA = 0   ' 原限額
			 *                         T_NEWCURR = ""   ' 新限額幣別
			 *                         T_NEWQUOTA = 0  ' 新限額
			 *                         
			 *                               '--- 讀取新、舊限額幣別及金額 ---
			 *                         If Iselement(BFSnoList(X))  Then
			 *                               If Trim(BFSnoList(X))<>"" Then
			 *                                     T_OLDCURR = Left(BFSnoList(X),3)
			 *                                     T_OLDQUOTA= Cdbl(Mid(BFSnoList(X),4,Len(BFSnoList(X))))
			 *                               End If
			 *                         End If
			 *                         If Iselement(NewSnoList(X))  Then
			 *                               If Trim(NewSnoList(X))<>"" Then
			 *                                     T_NEWCURR = Left(NewSnoList(X),3)
			 *                                     T_NEWQUOTA= Cdbl(Mid(NewSnoList(X),4,Len(NewSnoList(X))))
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			// 原限額幣別
			String T_OLDCURR = "";
			// 原限額
			BigDecimal T_OLDQUOTA = BigDecimal.ZERO;

			if (oldList.containsKey(item)) {
				String value = Util.trim(oldList.get(item));
				T_OLDCURR = Util.getLeftStr(value, 3);
				T_OLDQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}
			String T_NEWCURR = "";
			// 新限額
			BigDecimal T_NEWQUOTA = BigDecimal.ZERO;
			if (newList.containsKey(item)) {
				String value = Util.trim(newList.get(item));
				T_NEWCURR = Util.getLeftStr(value, 3);
				T_NEWQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}

			/**
			 * <pre>
			 *                                 ' --- 判斷科(子)目變更註記 ---
			 *                         If tRdoc.Property(0) = "1" Or (Not Iselement(BFSnoList(X))) Then
			 *                               tProperty = "1"        '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
			 *                         Else
			 *                               If  T_NEWQUOTA > T_OLDQUOTA Then
			 *                                     tProperty = "3"   '--- 增額---
			 *                               Else
			 *                                     If  T_NEWQUOTA < T_OLDQUOTA Then
			 *                                           tProperty = "4"   '--- 減額---
			 *                                     Else
			 *                                           tProperty = "0"         '---  0.額度不變 ---
			 *                                     End If
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			String tProperty = "0";
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.新做)
					|| !oldList.containsKey(item)) {
				// '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
				tProperty = "1";
			} else {
				switch (T_NEWQUOTA.compareTo(T_OLDQUOTA)) {
				case 1:
					// '--- 增額---
					tProperty = "3";
					break;
				case -1:
					// '--- 減額---
					tProperty = "4";
					break;
				case 0:
					// '--- 0.額度不變 ---
					tProperty = "0";
					break;
				}
			}

			if ("941".equals(item)) {
				item = "944";
			} else if ("912".equals(item)) {
				item = "947";
			} else if ("913".equals(item)) {
				item = "949";
			}

			if (misQuotsubService.selByUniqueKey(l140m01a.getCustId(),
					l140m01a.getDupNo(), cntrNo, sDate, item).isEmpty()) {
				misQuotsubService.insert(l140m01a.getCustId(),
						l140m01a.getDupNo(), cntrNo, sDate, item, tProperty,
						T_OLDCURR, T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, lmtDays, "N");
				if (upAS400) {
					obsdbELF384Service.insert(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] {
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item, tProperty, T_OLDCURR,
									T_OLDQUOTA.doubleValue(), T_NEWCURR,
									T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp() }));
				}
			} else {
				misQuotsubService.update(tProperty, T_OLDCURR,
						T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, l140m01a.getCustId(), l140m01a.getDupNo(),
						cntrNo, sDate, item, lmtDays, "N");
				if (upAS400) {
					obsdbELF384Service.update(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] { tProperty,
									T_OLDCURR, T_OLDQUOTA.doubleValue(),
									T_NEWCURR, T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp(),
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item }));
				}
			}

		}

		// (2)上傳科目限額

		for (L140M01D l140m01d : l140m01a.getL140m01d()) {
			// 科子目合併限額不上傳

			BigDecimal lmtDays = BigDecimal.valueOf(999);

			if (!"1".equals(l140m01d.getLmtType())) {
				continue;
			}

			String item = l140m01d.getSubject();
			String as400item = l140m01d.getSubject();
			// StringBuilder loanTp = new StringBuilder(0);
			StringBuilder loanTpFor400 = new StringBuilder(0);

			if (this.isNoUploadItem(item)) {
				continue;
			}
			// AS400上傳授信科目的部分要轉為會計科目 沒有科目的要帶8個0
			if (codeTypeMap.containsKey(as400item)) {
				as400item = codeTypeMap.get(item);
			} else {
				as400item = "00000000";
			}

			item = item.substring(0, 3);

			/**
			 * <pre>
			 *                               'Msgbox "(1) 申請科目 =" & Condition9
			 *                         T_OLDCURR =""     ' 原限額幣別
			 *                         T_OLDQUOTA = 0   ' 原限額
			 *                         T_NEWCURR = ""   ' 新限額幣別
			 *                         T_NEWQUOTA = 0  ' 新限額
			 *                         
			 *                               '--- 讀取新、舊限額幣別及金額 ---
			 *                         If Iselement(BFSnoList(X))  Then
			 *                               If Trim(BFSnoList(X))<>"" Then
			 *                                     T_OLDCURR = Left(BFSnoList(X),3)
			 *                                     T_OLDQUOTA= Cdbl(Mid(BFSnoList(X),4,Len(BFSnoList(X))))
			 *                               End If
			 *                         End If
			 *                         If Iselement(NewSnoList(X))  Then
			 *                               If Trim(NewSnoList(X))<>"" Then
			 *                                     T_NEWCURR = Left(NewSnoList(X),3)
			 *                                     T_NEWQUOTA= Cdbl(Mid(NewSnoList(X),4,Len(NewSnoList(X))))
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			// 原限額幣別
			String T_OLDCURR = "";
			// 原限額
			BigDecimal T_OLDQUOTA = BigDecimal.ZERO;

			if (oldList.containsKey(item)) {
				String value = Util.trim(oldList.get(item));
				T_OLDCURR = Util.getLeftStr(value, 3);
				T_OLDQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}
			String T_NEWCURR = "";
			// 新限額
			BigDecimal T_NEWQUOTA = BigDecimal.ZERO;
			if (newList.containsKey(item)) {
				String value = Util.trim(newList.get(item));
				T_NEWCURR = Util.getLeftStr(value, 3);
				T_NEWQUOTA = Util.parseBigDecimal(value.substring(3,
						value.length()));
			}

			/**
			 * <pre>
			 *                                 ' --- 判斷科(子)目變更註記 ---
			 *                         If tRdoc.Property(0) = "1" Or (Not Iselement(BFSnoList(X))) Then
			 *                               tProperty = "1"        '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
			 *                         Else
			 *                               If  T_NEWQUOTA > T_OLDQUOTA Then
			 *                                     tProperty = "3"   '--- 增額---
			 *                               Else
			 *                                     If  T_NEWQUOTA < T_OLDQUOTA Then
			 *                                           tProperty = "4"   '--- 減額---
			 *                                     Else
			 *                                           tProperty = "0"         '---  0.額度不變 ---
			 *                                     End If
			 *                               End If
			 *                         End If
			 * </pre>
			 */
			String tProperty = "0";
			if (LMSUtil.isContainValue(l140m01a.getProPerty(),
					UtilConstants.Cntrdoc.Property.新做)
					|| !oldList.containsKey(item)) {
				// '--- 新增 : 性質為新增以及無變更前資料者都視為新增---
				tProperty = "1";
			} else {
				switch (T_NEWQUOTA.compareTo(T_OLDQUOTA)) {
				case 1:
					// '--- 增額---
					tProperty = "3";
					break;
				case -1:
					// '--- 減額---
					tProperty = "4";
					break;
				case 0:
					// '--- 0.額度不變 ---
					tProperty = "0";
					break;
				}
			}

			// If Y = "941" Or Y = "944" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '941' OR LOANTP = '944'  ) "
			// Elseif Y = "912" Or Y = "947" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '912' OR LOANTP = '947'  ) "
			// Elseif Y = "913" Or Y = "949" Then
			// Condition9 = Condition9 +
			// " (LOANTP = '913' OR LOANTP = '949'  ) "
			// Else
			// Condition9 = Condition9 + " LOANTP = '" + Y + "'"
			// End If

			if ("941".equals(item)) {
				item = "944";
			} else if ("912".equals(item)) {
				item = "947";
			} else if ("913".equals(item)) {
				item = "949";
			}

			if (Util.notEquals(item, "")) {
				// AS400上傳要轉會計科目
				if (codeTypeMap.containsKey(as400item)) {
					as400item = codeTypeMap.get(item);
				}
				// loanTp.append("'").append(item).append("'");
				// loanTpFor400.append("'").append(as400item).append("'");
			}

			if (misQuotsubService.selByUniqueKey(l140m01a.getCustId(),
					l140m01a.getDupNo(), cntrNo, sDate, item).isEmpty()) {
				misQuotsubService.insert(l140m01a.getCustId(),
						l140m01a.getDupNo(), cntrNo, sDate, item, tProperty,
						T_OLDCURR, T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, lmtDays, "N");

			} else {
				misQuotsubService.update(tProperty, T_OLDCURR,
						T_OLDQUOTA.doubleValue(), T_NEWCURR,
						T_NEWQUOTA.doubleValue(), l140m01a.getSbjProperty(),
						updater, l140m01a.getCustId(), l140m01a.getDupNo(),
						cntrNo, sDate, item, null, "N");

			}
			if (upAS400) {
				if (obsdbELF384Service.selByUniqueKey(
						BRNID,
						LMSUtil.covertAs400Time(new Object[] {
								l140m01a.getCustId(), l140m01a.getDupNo(),
								cntrNo, sDate, as400item })).isEmpty()) {

					obsdbELF384Service.insert(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] {
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item, tProperty, T_OLDCURR,
									T_OLDQUOTA.doubleValue(), T_NEWCURR,
									T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp() }));

				} else {
					obsdbELF384Service.update(
							BRNID,
							LMSUtil.covertAs400Time(new Object[] { tProperty,
									T_OLDCURR, T_OLDQUOTA.doubleValue(),
									T_NEWCURR, T_NEWQUOTA.doubleValue(),
									l140m01a.getSbjProperty(), updater,
									CapDate.getCurrentTimestamp(),
									l140m01a.getCustId(), l140m01a.getDupNo(),
									cntrNo, sDate.replaceAll("\\D", ""),
									as400item }));
				}
			}

		}

	}

	/**
	 * 利率條件LN.LNF164
	 * 
	 * @param BRNID
	 *            AS400上傳分行
	 * @param l160m01a
	 *            動審表
	 * @param l140m01a
	 *            額度明細表
	 * @param l120m01a
	 *            簽報書
	 * @param updater
	 *            上傳者
	 * @param sDate
	 *            目前上傳時前
	 * @param dataList
	 *            MIS OBJECT[]
	 * @param as400List
	 *            as400 OBJECT[]
	 * @param upAS400
	 *            是否上傳as400
	 * @param cntrNo
	 *            額度序號
	 */
	private void uploadLNF164(String BRNID, L160M01A l160m01a,
			L140M01A l140m01a, L120M01A l120m01a, String updater, String sDate,
			List<Object[]> dataList, List<Object[]> as400List, boolean upAS400,
			String cntrNo) {
		HashMap<String, String> moneyMap = new HashMap<String, String>();
		moneyMap.put("1", "TWD");
		moneyMap.put("2", "USD");
		moneyMap.put("3", "JPY");
		moneyMap.put("4", "EUR");
		moneyMap.put("5", "");
		moneyMap.put("7", "THB");
		StringBuilder custid = new StringBuilder(0);
		custid.append(Util.trim(l140m01a.getCustId()));
		String intchgType = "";
		String intchgCycl = "";
		if (custid.length() == 8) {
			custid.append("  ").append(l140m01a.getDupNo());
		} else {
			custid.append(l140m01a.getDupNo());
		}

		for (L140M01F l140m01f : l140m01a.getL140m01f()) {

			if (Util.isEmpty(l140m01f.getLoanTPList())) {
				continue;
			}
			for (String item : l140m01f.getLoanTPList().split(
					UtilConstants.Mark.SPILT_MARK)) {
				for (L140M01G l140m01g : l140m01f.getL140m01g()) {
					switch (Util.parseInt(l140m01g.getRateChg1())) {
					case 1:
						intchgType = "W";
						intchgCycl = "1";
						break;
					case 2:
						intchgType = "M";
						intchgCycl = "1";
						break;

					case 3:
						intchgType = "M";
						intchgCycl = "3";
						break;
					case 4:
						intchgType = "M";
						intchgCycl = "6";
						break;
					case 5:
						intchgType = "M";
						intchgCycl = "9";
						break;
					case 6:
						intchgType = "Y";
						intchgCycl = "1";
						break;
					}
					dataList.add(new Object[] {
							l140m01a.getOwnBrId(),
							cntrNo,
							custid.toString(),
							l160m01a.getTType(),
							moneyMap.get(l140m01g.getRateType()),
							Util.trimSizeInOS390(item, 3),
							l140m01g.getRateGetInt(),
							"N",
							Util.trimSizeInOS390(Util.toFullCharString(l140m01g
									.getRateInput()), 100),
							this.coverNumber(l140m01g.getRateValue()),
							l140m01g.getRateKind(),
							intchgType,
							intchgCycl,
							Util.trimSizeInOS390(Util.toFullCharString(l140m01g
									.getRateDscr()), 200) });

					System.out.println("l140m01g.mainid()="
							+ l140m01g.getMainId());
					System.out.println("l140m01g.getRateType()="
							+ l140m01g.getRateType());
					if (upAS400) {
						obsdbELF164Service.insert(BRNID, l140m01a.getOwnBrId(),
								cntrNo, custid.toString(), l160m01a.getTType(),
								moneyMap.get(l140m01g.getRateType()), Util
										.trimSizeInOS390(item, 3), l140m01g
										.getRateGetInt(), "N", Util
										.trimSizeInOS390(Util
												.toFullCharString(l140m01g
														.getRateInput()), 70),
								this.coverNumber(l140m01g.getRateValue()),
								l140m01g.getRateKind(), intchgType, Util
										.trimSizeInOS390(
												l140m01g.getRateDscr(), 140));
					}

				}
				if (!Util.isEmpty(l140m01f.getL140m01h())) {
					// 11.-企金簽案費率檔 ELCRTBL
					uploadElcrtbl(BRNID, l140m01a, l140m01f.getL140m01h(),
							Util.trimSizeInOS390(item, 3), updater, sDate,
							upAS400, cntrNo);
				}

			}

		}
		// misLNF164Service.insert(dataList);
		custid.setLength(0);
	}

	/**
	 * 企金簽案費率檔 ELCRTBL(AS400-ELF476)
	 * 
	 * @param BRNID
	 *            as400上傳分行
	 * @param l140m01a
	 *            額度明細表主檔
	 * @param l140m01h
	 *            額度費率明細檔
	 * @param item
	 *            授信科目
	 * @param updater
	 *            更新者
	 * @param sDate
	 *            系統時間
	 * @param upAS400
	 *            是否上傳 as400
	 * @param cntrno
	 *            　額度序號 　　　　　　　
	 */
	private void uploadElcrtbl(String BRNID, L140M01A l140m01a,
			L140M01H l140m01h, String item, String updater, String sDate,
			boolean upAS400, String cntrno) {

		List<Object[]> elf476List = new ArrayList<Object[]>();
		List<Object[]> dataList = new ArrayList<Object[]>();

		if (!Util.isEmpty(l140m01h.getCpType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "1");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"1",
					Util.trim(l140m01h.getCpType()),
					this.coverNumber(l140m01h.getCp1Rate()),
					this.coverNumber(l140m01h.getCp1Fee()),
					this.coverNumber(l140m01h.getCp2Rate1()),
					this.coverNumber(l140m01h.getCp2Rate2()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getCpDes()), 80),
					"", 0.0, 0, "", 0, 0.0, "", 0, "", "", 0.0, 0, "", 0, 0.0,
					"", 0, "", "", 0.0, "", 0, 0.0, "", 0,

					"", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "1");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "1",
						Util.trim(l140m01h.getCpType()),
						this.coverNumber(l140m01h.getCp1Rate()),
						this.coverNumber(l140m01h.getCp1Fee()),
						this.coverNumber(l140m01h.getCp2Rate1()),
						this.coverNumber(l140m01h.getCp2Rate2()),
						Util.trimSizeInOS390(l140m01h.getCpDes(), 60), "", 0,
						0, "", 0, 0, "", 0, "", "", 0, 0, "", 0, 0, "", 0, "",
						"", 0, "", 0, 0, "", 0, "", updater,
						CapDate.getCurrentTimestamp() });
			}

		}

		if (!Util.isEmpty(l140m01h.getCfType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "2");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"2",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					Util.trim(l140m01h.getCfType()),
					this.coverNumber(l140m01h.getCf1Rate()),
					this.coverNumber(l140m01h.getCf1Mon1()),
					Util.trim(l140m01h.getCf1MD()),
					this.coverNumber(l140m01h.getCf1Mon2()),
					this.coverNumber(l140m01h.getCf2Rate()),
					Util.trim(l140m01h.getCf2MD()),
					this.coverNumber(l140m01h.getCf2Mon()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getCfDes()), 80),
					"", 0.0, 0, "", 0, 0.0, "", 0, "", "", 0.0, "", 0, 0.0, "",
					0, "", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "2");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "2", "",
						0, 0, 0, 0, "", Util.trim(l140m01h.getCfType()),
						coverNumber(l140m01h.getCf1Rate()),
						this.coverNumber(l140m01h.getCf1Mon1()),
						Util.trim(l140m01h.getCf1MD()),
						this.coverNumber(l140m01h.getCf1Mon2()),
						this.coverNumber(l140m01h.getCf2Rate()),
						Util.trim(l140m01h.getCf2MD()),
						this.coverNumber(l140m01h.getCf2Mon()),
						Util.trimSizeInOS390(l140m01h.getCfDes(), 60), "", 0,
						0, "", 0, 0, "", 0, "", "", 0, "", 0, 0, "", 0, "",
						updater, CapDate.getCurrentTimestamp() });

			}
		}

		if (!Util.isEmpty(l140m01h.getCpyType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "3");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"3",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					Util.trim(l140m01h.getCpyType()),
					this.coverNumber(l140m01h.getCpy1Rate()),
					this.coverNumber(l140m01h.getCpy1Mon1()),
					Util.trim(l140m01h.getCpy1MD()),
					this.coverNumber(l140m01h.getCpy1Mon2()),
					this.coverNumber(l140m01h.getCpy2Rate()),
					Util.trim(l140m01h.getCpy2MD()),
					this.coverNumber(l140m01h.getCpy2Mon()),
					Util.trimSizeInOS390(Util.toFullCharString(Util
							.trim(l140m01h.getCpyDes())), 80), "", 0.0, "", 0,
					0.0, "", 0, "", updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "3");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "3", "",
						0, 0, 0, 0, "", "", 0, 0, "", 0, 0, "", 0, "",
						Util.trim(l140m01h.getCpyType()),
						coverNumber(l140m01h.getCpy1Rate()),
						this.coverNumber(l140m01h.getCpy1Mon1()),
						Util.trim(l140m01h.getCpy1MD()),
						this.coverNumber(l140m01h.getCpy1Mon2()),
						this.coverNumber(l140m01h.getCpy2Rate()),
						Util.trim(l140m01h.getCpy2MD()),
						this.coverNumber(l140m01h.getCpy2Mon()),
						Util.trimSizeInOS390(l140m01h.getCpyDes(), 60), "", 0,
						"", 0, 0, "", 0, "", updater,
						CapDate.getCurrentTimestamp() });
			}

		}
		if (!Util.isEmpty(l140m01h.getPaType())) {
			misELCRTBLService.delByKey(cntrno, item, sDate, "4");
			dataList.add(new Object[] {
					cntrno,
					item,
					sDate,
					"4",
					"",
					0.0,
					0,
					0.0,
					0.0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					"",
					0.0,
					0,
					"",
					0,
					0.0,
					"",
					0,
					"",
					Util.trim(l140m01h.getPaType()),
					this.coverNumber(l140m01h.getPa1Rate()),

					Util.trim(l140m01h.getPa1MD()),
					this.coverNumber(l140m01h.getPa1Mon()),
					this.coverNumber(l140m01h.getPa2Rate()),
					Util.trim(l140m01h.getPa2MD()),
					this.coverNumber(l140m01h.getPa2Mon()),
					Util.trimSizeInOS390(
							Util.toFullCharString(l140m01h.getPaDes()), 80),
					updater });
			if (upAS400) {
				obsdbELF476Service.delByKey(BRNID, cntrno, item, CapDate
						.formatDate(CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD),
								"yyyyMMdd"), "4");
				elf476List.add(new Object[] {
						cntrno,
						item,
						CapDate.getDate(sDate,
								UtilConstants.DateFormat.YYYY_MM_DD), "4", "",
						0, 0, 0, 0, "", "", 0, 0, "", 0, 0, "", 0, "", "", 0,
						0, "", 0, 0, "", 0, "",
						Util.trim(l140m01h.getPaType()),
						coverNumber(l140m01h.getPa1Rate()),
						Util.trim(l140m01h.getPa1MD()),
						this.coverNumber(l140m01h.getPa1Mon()),
						this.coverNumber(l140m01h.getPa2Rate()),
						Util.trim(l140m01h.getPa2MD()),
						this.coverNumber(l140m01h.getPa2Mon()),
						Util.trimSizeInOS390(l140m01h.getPaDes(), 60), updater,
						CapDate.getCurrentTimestamp() });

			}
		}
		misELCRTBLService.insert(dataList);
		if (upAS400) {
			obsdbELF476Service.insert(BRNID,
					LMSUtil.covertAs400Time(elf476List));
		}
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Double
	 * @return Double
	 */
	private Double coverNumber(BigDecimal value) {
		if (value == null) {
			return BigDecimal.ZERO.doubleValue();
		}
		return value.doubleValue();
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Double
	 * @return Double
	 */
	private Double coverNumber(Double value) {

		return Util.isEmpty(value) ? 0.0 : value;
	}

	/**
	 * 轉換數值
	 * 
	 * @param value
	 *            Integer
	 * @return Integer
	 */
	private Integer coverNumber(Integer value) {

		return Util.isEmpty(value) ? 0 : value;
	}

	/**
	 * 檢查是否為需要上傳科目
	 * 
	 * @param item
	 *            授信科目
	 * @return boolean
	 */
	private boolean isNoUploadItem(String item) {
		// (1)額度明細表申請科目 ####
		// 科目代號只取前3碼，(第4碼為性質相同但名稱不同的科目)，
		// 第1碼為Z開頭的及代碼為7031者皆為虛擬科目，不上傳。

		// J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan

		if (Util.equals(lmsService.getSysParamDataValue("LMS_J1050340001_ON"),
				"Y")) {
			if (Util.isEmpty(Util.trim(item))
					|| ("Z".equals(item.substring(0, 1)) && !"Z15".equals(item))
					|| "7031".equals(item)) {
				return true;
			}
		} else {
			if (Util.isEmpty(Util.trim(item))
					|| "Z".equals(item.substring(0, 1)) || "7031".equals(item)) {
				return true;
			}
		}

		return false;
	}

	// /**
	// * 檢核 是否為續約、展期、提前續約者
	// *
	// * @param value
	// * 性質
	// * @return boolean
	// */
	// private boolean isSQCase(String value) {
	//
	// if (UtilConstants.Cntrdoc.Property.續約.equals(value)
	// || UtilConstants.Cntrdoc.Property.展期.equals(value)
	// || UtilConstants.Cntrdoc.Property.提前續約者.equals(value)) {
	// return true;
	// }
	// return false;
	// }

	@Override
	public List<L160M01A> findFirstUse(String[] docStatus, String ownBrId) {
		return l160m01aDao.findByFitstUse(docStatus, ownBrId);
	}

	@Override
	public void saveMain(List<L160M01B> l160m01bs, List<L162S01A> l162s01as,
			GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		save(entity);
		for (L160M01B l160m01b : l160m01bs) {
			l160m01b.setUpdateTime(CapDate.getCurrentTimestamp());
			l160m01b.setUpdater(user.getUserId());
		}
		for (L162S01A l162s01a : l162s01as) {
			l162s01a.setUpdateTime(CapDate.getCurrentTimestamp());
			l162s01a.setUpdater(user.getUserId());
		}
		l160m01bDao.save(l160m01bs);
		l162s01aDao.save(l162s01as);

	}

	@Override
	public void saveL160m01cs(List<L160M01C> l160m01cs, GenericBean... entity) {
		save(entity);
		l160m01cDao.save(l160m01cs);
	}

	@Override
	public HashMap<String, String> getCustCounty(L120M01A l120m01a) {

		StringBuilder custIdKey = new StringBuilder("");
		HashMap<String, String> custCounty = new HashMap<String, String>();
		// 1企金 2個金 、取得借款人國別
		if (UtilConstants.Casedoc.DocType.企金.equals(l120m01a.getDocType())) {
			List<L120S01B> l120s01bs = l120s01bDao.findByMainId(l120m01a
					.getMainId());

			for (L120S01B l120s01b : l120s01bs) {
				custIdKey.append(l120s01b.getCustId()).append(
						l120s01b.getDupNo());
				custCounty.put(custIdKey.toString(), l120s01b.getNtCode());
				custIdKey.setLength(0);
			}
		} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
				.getDocType())) {
			List<C120S01A> c120s01as = c120s01aDao.findByMainId(l120m01a
					.getMainId());
			for (C120S01A c120s01a : c120s01as) {
				custIdKey.append(c120s01a.getCustId()).append(
						c120s01a.getDupNo());
				custCounty.put(custIdKey.toString(), c120s01a.getNtCode());
				custIdKey.setLength(0);
			}

		}
		return custCounty;

	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (userId == null) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	@Override
	public L160M01A findL160M01AByMaindId(String mainId) {
		return l160m01aDao.findByMainId(mainId);
	}

	@Override
	public List<L160M01A> findL160M01AByOids(String[] oids) {
		return l160m01aDao.findByOids(oids);
	}

	@Override
	public L160M01D findL160m01d(String mainId, String staffNo, String staffjob) {
		return l160m01dDao.findByUniqueKey(mainId, staffNo, staffjob);
	}

	@Override
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId, String[] useBrId,
			String itemType) {
		return vusedoc01Dao.findUseCntrNo(caseMainId, useBrId, itemType);
	}

	@Override
	public List<VLUSEDOC01> getDoCntrNo(String caseMainId,
			String[] selectCntrNos, String[] useBrId, String itemType) {
		return vusedoc01Dao.findUseCntrNo(caseMainId, selectCntrNos, useBrId,
				itemType);
	}

	private BigDecimal gfnConvertZero(BigDecimal num) {
		if (num == null) {
			return BigDecimal.ZERO;
		}
		return num;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainIdUid(String mainId, String uid) {
		return l161m01aDao.findByMainIdUid(mainId, uid);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.lms.service.LMS1605Service#findL161m01aByMainIdUid
	 * (java .lang.String)
	 */
	@Override
	public L161S01A findL161m01aByMainIdCntrno(String mainId, String cntrNo) {
		return l161m01aDao.findByMainIdCntrno(mainId, cntrNo);
	}

	/**
	 * 將西元改民國年
	 * 
	 * @param _obj
	 * @return
	 */
	private String paresTOTWDate(Date _obj) {
		String result = "";
		if (_obj != null) {
			TWNDate d = TWNDate.valueOf((Date) _obj);

			// 上傳mis如日期大於最大2910/12/31 (999/12/31) 一律上傳 2910/12/31
			if (d.compareTo(TWNDate.valueOf("2910-12-31")) > 0) {
				d = TWNDate.valueOf("2910-12-31");
			}
			result = d.toTW();

			// 日期如果小於 1911-01-01 則轉空白 (因為西元扣掉1911後會變成負的)
			if (d.compareTo(TWNDate.valueOf("1911-01-01")) < 0) {
				result = "";
			}

		} else {
			result = "";
		}

		return result;
	}

	/**
	 * J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
	 * 
	 * @param l160m01a
	 * @param l140m01a
	 * @param l120m01a
	 * @param cntrNo
	 * @throws CapException
	 */
	private void updateELF500(L160M01A l160m01a, L140M01A l140m01a,
			L120M01A l120m01a, String cntrNo) throws CapException {

		misdbBASEService.updateELf500_NOTVALID_ByCntrNo(cntrNo,
				l140m01a.getCustId(), l140m01a.getDupNo());

	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public L164S01A findL164s01aByMainIdCustId(String mainId, String custId,
			String dupNo) {
		return l164s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 */
	@Override
	public void deleteL164s01as(List<L164S01A> l164s01as) {
		l164s01aDao.delete(l164s01as);
	}

	/**
	 * J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
	 * 
	 * @param list
	 */
	@Override
	public void saveL164s01aList(List<L164S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (L164S01A l164s01a : list) {
			l164s01a.setUpdater(user.getUserId());
			l164s01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l164s01aDao.save(list);
	}

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 * 
	 * @param list
	 * @return
	 */
	public List<L162S01A> findL162s01aNeedPriority(List<L162S01A> list,
			String cntrNo) {

		List<L162S01A> newList = new ArrayList<L162S01A>();

		for (L162S01A l162m01a : list) {

			String tCustId = Util.trim(l162m01a.getRId());
			String tDupNo = Util.trim(l162m01a.getRDupNo());
			String tCntrNo = Util.trim(l162m01a.getCntrNo());

			// 要相同額度序號
			if (Util.notEquals(tCntrNo, cntrNo)) {
				continue;
			}

			Map<String, Object> custData = misCustdataService
					.findAllByByCustIdAndDupNo(tCustId, tDupNo);

			if (custData != null && !custData.isEmpty()) {
				// 保證人是個人戶就不要
				if (Util.equals(custData.get("BUSCD"), "130300")
						|| Util.equals(custData.get("BUSCD"), "060000")) {
					continue;
				}
			}

			String rType = Util.trim(l162m01a.getRType());
			if (Util.equals(rType, UtilConstants.lngeFlag.連帶保證人)
					|| Util.equals(rType, UtilConstants.lngeFlag.ㄧ般保證人)) {
				newList.add(l162m01a);
			}

		}

		return newList;
	}

	/**
	 * J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
	 */
	@Override
	public List<L162S01A> findL162s01aByMainIdCntrno(String mainId,
			String cntrNo) {
		return l162s01aDao.findByMainIdCntrNo(mainId, cntrNo);
	}

	/**
	 * J-110-0040_05097_B1001 Web e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
	 * 
	 * @param oids
	 * @return
	 */
	@Override
	public List<L162S01A> findL162S01AByOids(String[] oids) {
		return l162s01aDao.findByOids(oids);
	}

	/**
	 * J-111-0028_05097_B1001 Web e-Loan海外企金授信動用審核表其它事項增加「土建融案件維護表」檢核項目
	 * 
	 * @param l160m01c
	 * @return
	 */
	@Override
	public String isL160m01cHasInputItemField1(L160M01C l160m01c) {
		String itemField = "";

		String itemField1Fg1 = "%T1%";
		String itemField1Fg2 = "%D1%";

		String itemContent = l160m01c.getItemContent();
		if (StringUtils.indexOf(itemContent, itemField1Fg1) >= 0) {

			itemField = itemField1Fg1;
		} else if (StringUtils.indexOf(itemContent, itemField1Fg2) >= 0) {
			itemField = itemField1Fg2;
		}

		return itemField;
	}

	/**
	 * 額度向下僅有該科目時不用上傳QUOTAPPR
	 * 
	 * 檢查是否為需要上傳科目 J-112-0129_05097_B1001 Web
	 * e-Loan系統對於符合單純僅有「交換票據抵用」會計科目之額度序號，不要帶入額度介面檔
	 * 
	 * @param item
	 *            授信科目
	 * @return boolean
	 */
	@Override
	public boolean isNoUploadQuotapprItem(String item) {
		boolean isNoNeed = false;
		String LMS_NO_UPLOAD_QUOTAPPR_ITEM = Util.trim(lmsService
				.getSysParamDataValue("LMS_NO_UPLOAD_QUOTAPPR_ITEM"));

		if (Util.notEquals(LMS_NO_UPLOAD_QUOTAPPR_ITEM, "")) {
			for (String xx : LMS_NO_UPLOAD_QUOTAPPR_ITEM.split(",")) {
				if (Util.equals(xx, item)) {
					isNoNeed = true;
					break;
				}
			}
		}

		return isNoNeed;
	}

	@Override
	public List<L162S01A> checkL162s01aLocalIdAndGrtAmt(String custId, String dupNo, String cntrNo, List<L162S01A> newL162s01as) {
		//G-113-0036 主從債務人
		//取得 MIS.ELLNGTEE(ELF401)，如額度明細引入之主從債務人 擔保限額Guarante Amount(GRTAMT)、當地客戶識別ID Local Id(LOCALID)未填，則帶MIS.ELLNGTEE為預設值
		List<Map<String, Object>> ellngteeDatas = misELLNGTEEService.getAllDataByCustIdCntrNo(custId, dupNo, cntrNo);
		//
		for (L162S01A finall162s01a : newL162s01as) {
			for (Map<String, Object> ellngteeData : ellngteeDatas) {
				String cntrno = (String) (ellngteeData.get("CNTRNO") == null ? "" : ellngteeData.get("CNTRNO"));
				String custid = (String) (ellngteeData.get("CUSTID") == null ? "" : ellngteeData.get("CUSTID"));
				String dupno = (String) (ellngteeData.get("DUPNO") == null ? "" : ellngteeData.get("DUPNO"));
				String rtype = (String) (ellngteeData.get("LNGEFLAG") == null ? "" : ellngteeData.get("LNGEFLAG"));
				String rid = (String) (ellngteeData.get("LNGEID") == null ? "" : ellngteeData.get("LNGEID"));
				String rdupno = (String) (ellngteeData.get("DUPNO1") == null ? "" : ellngteeData.get("DUPNO1"));
				BigDecimal grtamt = Util.parseBigDecimal(ellngteeData.get("GRTAMT"));
				String localid = (String) (ellngteeData.get("LOCALID") == null ? "" : ellngteeData.get("LOCALID"));

				if(Util.isEmpty(finall162s01a.getGrtAmt()) || Util.isEmpty(finall162s01a.getLocalId())){
					//PK相符則帶入預設資訊
					if (Util.equals(cntrno, finall162s01a.getCntrNo()) && Util.equals(custid, finall162s01a.getCustId())
							&& Util.equals(dupno, finall162s01a.getDupNo()) && Util.equals(rtype, finall162s01a.getRType())
							&& Util.equals(rid, finall162s01a.getRId()) && Util.equals(rdupno, finall162s01a.getRDupNo())) {
						if(Util.isEmpty(finall162s01a.getGrtAmt())){
							finall162s01a.setGrtAmt(grtamt);
						}
						if(Util.isEmpty(finall162s01a.getLocalId())){
							finall162s01a.setLocalId(localid);
						}
					}
				}
			}
		}		
		
		return newL162s01as;
	}
	
	/**
	 * J-113-0035 貸後追蹤分項紀錄檔
	 * @param l160m01a
	 * @param l120m01a
	 * @param l140m01as
	 * @param l140m01a
	 * @param updater
	 * @param sDate
	 * @throws CapException
	 */
	private void updateELF603(L160M01A l160m01a, L120M01A l120m01a, List<L140M01A> l140m01as, String updater, String sDateTime)
			throws CapException {
		String casemainid = l120m01a.getMainId();
		//String mainid = l140m01a.getMainId();
		//List<L140S12A> l140s12as = lmsService.findL140s12aListByCaseMainId(casemainid);
		boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(l160m01a.getOwnBrId()).getBrNoFlag());
		if(!l140m01as.isEmpty()){
			String apptime = CapDate.convertTimestampToString(l120m01a.getApproveTime(), UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
			//BigDecimal as400appTime = LMSUtil.covertAs400Time(l120m01a.getApproveTime());
			BigDecimal as400appTime = LMSUtil.covertAs400Time(CapDate.convertStringToTimestamp(apptime));		
			String format_as400appTime = Util.trim(as400appTime);
			BigDecimal as400sDateTime = LMSUtil.covertAs400Time(CapDate.getCurrentTimestamp());	
			for (L140M01A l140m01a : l140m01as) {
				//603刪除
				if(isOverSea){
					obsdbELF603Service.delByUidIdAppDateCntrNo(l160m01a.getOwnBrId(),casemainid, format_as400appTime, l140m01a.getCntrNo());
				}else{
					misELF603Service.delByUidIdAppDateCntrNo(casemainid, apptime, l140m01a.getCntrNo());
				}
				//額度明細如為不變、取消 則不送
				if ((LMSUtil.isContainValue(Util.trim(l140m01a.getProPerty()),
						UtilConstants.Cntrdoc.Property.不變))
						|| LMSUtil.isContainValue(
								Util.trim(l140m01a.getProPerty()),
								UtilConstants.Cntrdoc.Property.取消)) {
					continue;
				}
				String mainid = l140m01a.getMainId();
				List<L140S12A> l140s12as = lmsService.findL140s12aListByMainId(mainid);
				if(!l140s12as.isEmpty()){
					for (L140S12A l140s12a : l140s12as) {
						String uid = casemainid;//簽案文件編號//l140s12a.getMainId();
						int seqno = l140s12a.getSeqNum();
						String cntrNo = l140s12a.getCntrNo();
						String esgtype = l140s12a.getEsgType();
						String esgmodel = l140s12a.getEsgModel();
						String tracond = l140s12a.getTraceCondition();
						String traprofik = l140s12a.getTraceProfiling();
						int tramonth = l140s12a.getTraceProfilingMonth(); 
						String content = l140s12a.getContentText();
						
						//ELF603上傳
						if(isOverSea){
							obsdbELF603Service.insertForInside(l160m01a.getOwnBrId(), uid, format_as400appTime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, as400sDateTime);
						}else{
							misELF603Service.insertForInside(uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, sDateTime);
						}
						//追蹤項目>追蹤週期為其他時，要多寫601
						if(Util.equals("3", l140s12a.getTraceCondition())){
							this.updateELF601(l160m01a, l140s12a, updater, sDateTime, l160m01a.getOwnBrId(), uid, l120m01a.getApproveTime(), seqno);	
						}

					}
				}else{//不追蹤 寫一筆空的讓ALOAN判斷此額度無資料
					String uid = casemainid;//簽案文件編號//l140s12a.getMainId();
					int seqno = 1;
					String cntrNo = l140m01a.getCntrNo();
					String esgtype = "X";
					String esgmodel = "";
					String tracond = "";
					String traprofik = "";
					int tramonth = 0; 
					String content = "";
					//ELF603上傳
					//misELF603Service.insertForInside(uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, sDateTime);
					if(isOverSea){
						obsdbELF603Service.insertForInside(l160m01a.getOwnBrId(), uid, format_as400appTime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, as400sDateTime);
					}else{
						misELF603Service.insertForInside(uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik, tramonth, content, updater, sDateTime);
					}
				}
				
			}
		}

	}
	
	/**
	 * J-113-0035 其他續做條件追蹤分項，起始追蹤日為其他時寫入ELF601
	 * @param l140s12a
	 * sDateTime
	 */
	private void updateELF601(L160M01A l160m01a, L140S12A l140s12a, String updater, String sDateTime, String brNo, String elf603_uid, Timestamp elf603_apptime, int elf603_seqno){
    	// -------------L140S12A 起始追蹤日(traceCondition)=3.其他，寫ELF601 start-------------
		if(l140s12a != null) {
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1605M01Page.class);
			String oid = l140s12a.getOid();
			//	==> L160M01A.OID + _BATCH_   + Timestamp(到秒) 塞 ELF601_UNID ( KEY )
			String actApproveTimeString = sDateTime.replace("-", "").replace(" ", "").replace(":", "").replace(" ", "");
			String unid = oid + "_L140S12A_"+ actApproveTimeString;// UNID有_L140S12A_則一定是這支產生的ELF601，以及相關的ELF602
			String unidForLike = oid + "_L140S12A_" + actApproveTimeString;// OID+_L140S12A_則一定是這支產生的ELF601，以及相關的ELF602
			boolean isOverSea = UtilConstants.BrNoType.國外.equals(branchService.getBranch(brNo).getBrNoFlag());
			
			// 寫入ELF601的資料 start
			ELF601 elf601Insert = new ELF601();
			elf601Insert.setElf601_unid(unid);// 序號(UNID)
			
			String cntrNo = Util.trim(l140s12a.getCntrNo());
			String cntrnoStringForContent = Util.trim(cntrNo + prop.getProperty("L140S12A.message03"));// L140S12A.message03=額度明細表：參閱 應注意/承諾/待追蹤/ESG連結條款明細 內容。

			elf601Insert.setElf601_cntrno(cntrNo);
			elf601Insert.setElf601_loan_no("");// 放款帳號，放空白
			elf601Insert.setElf601_loan_kind("LN");// 業務別

			String esgtype = Util.trim(l140s12a.getEsgType());
			elf601Insert.setElf601_fo_kind(esgtype);// 類別
			elf601Insert.setElf601_fo_content(cntrnoStringForContent);// 追蹤事項通知內容
			//elf601Insert.setElf601_fo_way("1");// 追蹤方式
			//取得追蹤週期<p/>
			//1.一次  2.週期月  3.核准日後6個月內  4.核准日後6個月第一次，其後每12個月一次
			//基準日先以動審核准隔天算 
			String traceProfiling = Util.trim(l140s12a.getTraceProfiling());
			if(Util.equals("2", traceProfiling)){//2.週期月
				elf601Insert.setElf601_fo_way("2");// 追蹤方式: 2-循環週期
				elf601Insert.setElf601_fo_cycle(BigDecimal.valueOf(l140s12a.getTraceProfilingMonth()));// 循環追蹤週期（月）
			}else {
				elf601Insert.setElf601_fo_way("1");// 追蹤方式: 1-特定日期
				elf601Insert.setElf601_fo_cycle(BigDecimal.valueOf(0));// 循環追蹤週期（月）
			}
			// 下次追蹤日: 基準日先以動審核准隔天算 ，待金襄確認
			Date baseDate = CapDate.getDate(sDateTime, UtilConstants.DateFormat.YYYY_MM_DD);
			Date nextDate = CapDate.shiftDays(baseDate, 1);

			elf601Insert.setElf601_fo_next_date(new java.sql.Date(nextDate.getTime()));// 下次追蹤日
			elf601Insert.setElf601_staff("02");// 應辦理追蹤對象  02-OA人員
			
			String apprId = Util.trim(l160m01a.getApprId());
			String reCheckId = Util.trim(l160m01a.getReCheckId());
			elf601Insert.setElf601_fo_staffNo("");// 應辦理追蹤對象行編-授信人員
			elf601Insert.setElf601_ao_staffNo(apprId);// 應辦理追蹤對象行編-AO人員
			elf601Insert.setElf601_status("N");// 狀態  N-新增
			elf601Insert.setElf601_cre_date(CapDate.getCurrentTimestamp());// 建檔日期
			elf601Insert.setElf601_cre_teller(apprId);// 建檔經辦  from動審表
			elf601Insert.setElf601_cre_supvno(reCheckId);// 建檔主管  from動審表
			elf601Insert.setElf601_upd_date(CapDate.getCurrentTimestamp());// 建檔日期
			elf601Insert.setElf601_upd_teller(apprId);// 建檔經辦  from動審表
			elf601Insert.setElf601_upd_supvno(reCheckId);// 建檔主管  from動審表
			elf601Insert.setElf601_full_content(Util.trimSizeInOS390(
					Util.toFullCharString(elf601Insert.getElf601_fo_content()), 402));// 追蹤事項通知內容（全形）
			String custId = Util.trim(l160m01a.getCustId());
			elf601Insert.setElf601_custid(custId);
			String dupNo = Util.trim(l160m01a.getDupNo());
			elf601Insert.setElf601_dupno(dupNo);
			elf601Insert.setElf601_br_no(brNo);
			elf601Insert.setElf601_case_mark("");// 設定案件註記，要給空字串，不然海外DB會ERROR
			
			elf601Insert.setElf601_suid(elf603_uid);
			elf601Insert.setElf601_sapptime(elf603_apptime);
			elf601Insert.setElf601_sseqno(new BigDecimal(elf603_seqno));
			// 此筆先行動用核准，要寫入ELF601的資料 end
			
			// 刪除同動審表oid相關的ELF601、ELF602，並新增ELF601 start
			if(isOverSea){
				// 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
				ELF601 elf601Delete = obsdbELF601Service.getElf601ByUnid(brNo, unid);
				if (elf601Delete != null && Util.isNotEmpty(elf601Delete)) {
					logger.info("updateELF601 deleteELF601 ByUnid，brNo: " + brNo + ", unid: " + unid);
					obsdbELF601Service.deleteELF601(brNo, unid);
				}
				// 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
				List<ELF602> elf602DeleteList = obsdbELF601Service.getElf602ByDatasrc(brNo, unid);
				for(ELF602 elf602Delete : elf602DeleteList){
					if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
						logger.info("updateELF601 deleteELF602 ByDatasrc，brNo: " + brNo + ", datasrc: " + unid);
						obsdbELF601Service.deleteELF602(brNo, elf602Delete.getElf602_unid());
					}
				}
				
				// 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF601> elf601LikeDeleteList = obsdbELF601Service.getElf601ByUnidLike(brNo, unidForLike + "%");
				for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
					if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF601 ByUnidLike，brNo: " + brNo + ", unidForLike: " + unidForLike);
						obsdbELF601Service.deleteELF601(brNo, elf601LikeDelete.getElf601_unid());
					}
				}
				// 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF602> elf602LikeDeleteList = obsdbELF601Service.getElf602ByDatasrcLike(brNo, unidForLike + "%");
				for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
					if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF602 ByDatasrcLike，brNo: " + brNo + ", datasrcLike: " + unidForLike);
						obsdbELF601Service.deleteELF602(brNo, elf602LikeDelete.getElf602_unid());
					}
				}
				
				logger.info("updateELF601 insertELF601，brNo: " + brNo + ", unid: " + elf601Insert.getElf601_unid());
				obsdbELF601Service.insertELF601(brNo, elf601Insert);
			} else {
				// 同一筆動審表且同一核准時間產生的ELF601，照理講不會有才對
				ELF601 elf601Delete = misdbBASEService.getElf601ByUnid(unid);
				if(elf601Delete != null && Util.isNotEmpty(elf601Delete)){
					logger.info("updateELF601 deleteELF601 ByUnid， unid: " + unid);
					misdbBASEService.deleteELF601(unid);
				}
				
				// 同一筆動審表且同一核准時間產生的ELF602，照理講不會有才對
				List<ELF602> elf602DeleteList = misdbBASEService.getElf602ByDatasrc(unid);
				for(ELF602 elf602Delete : elf602DeleteList){
					if (elf602Delete != null && Util.isNotEmpty(elf602Delete)) {
						logger.info("updateELF601 deleteELF602 ByDatasrc，datasrc: " + unid);
						misdbBASEService.deleteELF602(elf602Delete.getElf602_unid());
					}
				}
				
				// 同一筆動審表且不同核准時間產生的ELF601，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF601> elf601LikeDeleteList = misdbBASEService.getElf601ByUnidLike(unidForLike + "%");
				for(ELF601 elf601LikeDelete : elf601LikeDeleteList){
					if (elf601LikeDelete != null && Util.isNotEmpty(elf601LikeDelete) && elf601LikeDelete.getElf601_unid().contains(unidForLike)) {
						logger.info("updateELF601 deleteELF601 ByUnidLike，unidForLike: " + unidForLike);
						misdbBASEService.deleteELF601(elf601LikeDelete.getElf601_unid());
					}
				}
				// 同一筆動審表且不同核准時間產生的ELF602，可能是動審表重複核准(來來回回)產生的舊資料，應當刪除避免虛增
				List<ELF602> elf602LikeDeleteList = misdbBASEService.getElf602ByDatasrcLike(unidForLike + "%");
				for(ELF602 elf602LikeDelete : elf602LikeDeleteList){
					if (elf602LikeDelete != null && Util.isNotEmpty(elf602LikeDelete) && elf602LikeDelete.getElf602_datasrc().contains(unidForLike)) {
						logger.info("doLmsBatch0051 deleteELF602 ByDatasrcLike，datasrcLike: " + unidForLike);
						misdbBASEService.deleteELF602(elf602LikeDelete.getElf602_unid());
					}
				}
				
				logger.info("updateELF601 insertELF601，unid: " + elf601Insert.getElf601_unid());
				misdbBASEService.insertELF601(elf601Insert);
			}
			// 刪除同本次動審表oid L140S12A相關的ELF601、ELF602，並新增ELF601 end
		}
	}
	/**
	 * J-113-0168 E-LOAN完成動審上送額度介面資料至A-LOAN時，針對屬授信額度者(排除衍生性金融商品業務外)，立即通知0024為本行授信戶
	 * @param l140m01a
	 * @param l160m01a
	 * @param brNo
	 */
	private void callLNSP0130ForUpdateLnFlag(L140M01A l140m01a, L160M01A l160m01a, String brNo){
		String controlFlag = Util.trim(lmsService.getSysParamDataValue("J-113-0168_FLAG"));
		if(Util.equals(UtilConstants.DEFAULT.是, controlFlag)){
			boolean uploadFlag = false;
			// 以下條件不送
			//1. 衍生性金融商品科目961 962 963 964 、 純Z類的虛科目
			//2. 應收帳款買方 fact_type = 60的不用通知
			String ruleOutSubItemString = Util.trim(lmsService.getSysParamDataValue("J-113-0168_RULEOUTSUBITEM"));
			String[] subItems = ruleOutSubItemString.split(UtilConstants.Mark.SPILT_MARK);
			List<String> subItemsList = Arrays.asList(subItems);
			//抓動審當下L161S01A設定的
			L161S01A ll61s01a = this.findL161m01aByMainIdCntrno(l160m01a.getMainId(), l140m01a.getCntrNo());
			List<L140M01C> l140m01cs = lms1405Service.findL140m01cListByMainId(l140m01a.getMainId());
			String snoKind = "";
			if(ll61s01a != null){
				snoKind = Util.trim(ll61s01a.getSnoKind());
			}
			for(L140M01C l140m01c : l140m01cs){
				if(Util.isNotEmpty(l140m01c.getLoanTP()) 
						&& !subItemsList.contains(l140m01c.getLoanTP())
						&& Util.notEquals(snoKind, UtilConstants.Cntrdoc.snoKind.應收帳款買方)
				){
					uploadFlag = true;//只要有一個科目不是就送
					break;
				}
			}
			//通知0024為本行授信戶
			if(uploadFlag){
				String updater = MegaSSOSecurityContext.getUserId();
				Map<String, Object> lnsp0130Map = msps.callLNSP0130ForUpLnFlag(l160m01a.getCustId(), l160m01a.getDupNo(), updater, brNo);
			}
		}

	}
}
