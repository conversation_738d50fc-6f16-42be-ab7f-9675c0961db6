package com.mega.eloan.lms.base.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.node.DecisionNode;
import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.FlowConstants;
import com.mega.eloan.lms.base.service.FlowSimplifyService;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service("FlowSimplifyService")
public class FlowSimplifyServiceImpl  extends AbstractCapService implements FlowSimplifyService{
	
	@Resource 
	FlowService flowService;
	
	@Override
	public FlowInstance flowStart(String flowName, String mainOid, String userId, String unitNo){
		return flowStart(flowName, mainOid, userId, unitNo, null);
	}
	
	@Override
	public FlowInstance flowStart(String flowName, String mainOid, String userId, String unitNo
			, Map<String, Object> m){
		FlowInstance inst = flowService.createQuery().id(mainOid).query();
		if (inst == null) {
			if(MapUtils.isEmpty(m)){
				inst = flowService.start(flowName, mainOid, userId, unitNo);	
			}else{
				inst = flowService.start(flowName, mainOid, userId, unitNo, m);
			}			
		}
		return inst;
	}
	
	@Override
	public void flowNext(String mainOid, String decisionExpr) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		flowNext(mainOid, decisionExpr, user.getUserId(), user.getUnitNo());
	}
	
	@Override
	public void flowNext(String mainOid, String decisionExpr, Map<String, String> m) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		FlowInstance flowInst = flowService.createQuery().id(mainOid).query();
		
		Map<String, Object> map = new HashMap<String, Object>();
		if(Util.isNotEmpty(decisionExpr)){
			FlowNode nextNode = flowInst.getNextNode();
			if (nextNode != null) {
				if (nextNode instanceof DecisionNode) {
					String expr = Util.trim(
							nextNode.getAttr().get(FlowConstants.Attr.Expr))
							.replace("#", "");
					map.put(expr, decisionExpr);
				}			
			}	
		}		
		flowInst.setUserId( user.getUserId());
		flowInst.setDeptId( user.getUnitNo());
		if(m.size()>0){
			map.putAll(m);	
		}
		
		for(String key : map.keySet()){
			flowInst.setAttribute(key, map.get(key));
		}
		flowInst.next();
	}

	@Override
	public void flowNext(String mainOid, String decisionExpr, String userId, String deptId) {
		FlowInstance flowInst = flowService.createQuery().id(mainOid).query();
		
		Map<String, Object> map = new HashMap<String, Object>();
		if(Util.isNotEmpty(decisionExpr)){
			FlowNode nextNode = flowInst.getNextNode();
			if (nextNode != null) {
				if (nextNode instanceof DecisionNode) {
					String expr = Util.trim(
							nextNode.getAttr().get(FlowConstants.Attr.Expr))
							.replace("#", "");
					map.put(expr, decisionExpr);
				}			
			}	
		}		
		
		flowInst.setUserId(userId);
		flowInst.setDeptId(deptId);
		map.put("assgn_userId", userId);
		map.put("assgn_deptId", deptId);
		
		for(String key : map.keySet()){
			flowInst.setAttribute(key, map.get(key));
		}
		flowInst.next();	
	}
	
	@Override
	public boolean flowExist(String mainOid){
		return flowService.existsFlow(mainOid);
	}
	
	@Override
	public void flowCancel(String mainOid){
		flowService.cancel(mainOid);
	}
}
