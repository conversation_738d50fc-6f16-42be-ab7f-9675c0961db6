package test;


@SuppressWarnings("unused")
public class CheckI18N {
	private static final String xlsFile = "C:\\I18NExportProperties.xls";
	private static final String xlsFile2 = "C:\\I18NCheckProperties.xls";
	private static final String xlsFile3 = "C:\\I18NTotalProperties.xls";

	/**
	 * @param args
	 */
	public static void main(String[] args) {
//		Date date1 = TWNDate.valueOf("2012/07/01");
//		Date date = new Date (TWNDate.toAD(date1).substring(0,4) + "/" + TWNDate.toAD(date1).substring(5,7) + "/01");
//		Calendar rightNow = Calendar.getInstance();
//		rightNow.setTime(date);
//		rightNow.add(Calendar.MONTH, 1);
//		date = rightNow.getTime()	;
//		System.out.println(TWNDate.toAD(date));
		/**
		 * 這隻的源頭是jcs-common裡面的CheckI18NProperties 執行步驟 1.先將絕對路徑換成你放eclipse的路徑
		 * directoryPath是到i18n的系統曾(下一層為子系統名稱) directoryPath2是為了要讀js18n
		 * 所以路徑寫到tw的i18n 2.將systems更新成你們使用的子系統名稱 3.將//
		 * System.out.println置換成System.out.println 4.按右鍵執行 5.產生的檔案是產生到C:\\
		 * 請去看I18NCheckProperties.xls 還有多少沒有翻譯的內容
		 */
		// System.out.println("分析Properties開始!!!");
		// D:\\MegaProject2\\lms\\lms-web\\src\\main\\webapp\\i18n\\com\\mega\\eloan\\lms\\crs
		// i18n的絕對路徑(只到cms 沒有到子系統目錄喔)
//		CheckI18NProperties action = new CheckI18NProperties();
//		String directoryPath = "D:\\MegaProject2\\lms\\lms-web\\src\\main\\webapp\\i18n\\com\\mega\\eloan\\lms\\";
//		String directoryPath2 = "D:\\MegaProject2\\lms\\lms-web\\src\\main\\webapp\\i18n\\tw\\com\\iisi\\cap\\i18n\\";
//		String fileName = "JsI18nAjaxHandler_zh_TW.properties";
//		String[] systems = { "crs", "ctr", "fms", "las", "lms", "lrs", "rpt" };
//		// 先跑複製檔案 COPY沒有的空格 檔案格式一致 將檔案匯出致C:\xls
//		action.checkI18NContent2(directoryPath, systems, directoryPath2,
//				fileName);
//		// 把xls寫回到原始檔案內 讓格式統一
//		action.returnI18NContent();
//		// 掃出所有的內容 en或是CN有用中文字或是內容有問題的
//		action.checkI18NContent1(directoryPath, systems, directoryPath2,
//				fileName);
//		// 全部內容對照
//		action.checkI18NContent3(directoryPath, systems, directoryPath2,
//				fileName);
		// System.out.println("分析Properties結束!!!");
	}

}
