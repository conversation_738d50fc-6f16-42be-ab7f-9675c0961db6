var _handler = "cls9061formhandler";

$(document).ready(function(){
	var tabForm = $("#tabForm");
	var btnPanel = $("#buttonPanel");
	
	$.form.init({
		formHandler:_handler, 
		formAction:'query', 
		loadSuccess:function(json){
			
			// 控制頁面 Read/Write
			if(!$("#buttonPanel").find("#btnSave").is("button")) {
				tabForm.lockDoc();				
			}else{
				// 服務單位地址
			    $('#comTargetLink').click(function(){
			        AddrAction.open({
			            formId: 'tabForm',
			            signify: 'com'
			        });
			    });
			}
			
			tabForm.injectData(json);
			
			
			if(json.comTarget==""){
				if(!$("#buttonPanel").find("#btnSave").is("button")) {
					
				}else{
					tabForm.find("#comTarget").after("<span id='addrNote'>"+(i18n.def['comboSpace'] || '--請選擇--')+"</span>");
				}
			}
		}
	});

	btnPanel.find("#btnSave").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json){
			if(json.saveOkFlag){
				API.showMessage(i18n.def.saveSuccess);
			}
        });
	}).end().find("#btnSend").click(function(){
		if(!tabForm.valid()){
			return;
		}
		
		saveAction().done(function(json_saveAction){
    		if(json_saveAction.saveOkFlag){
    			API.confirmMessage(i18n.def.confirmApply, function(result){
    	            if (result) {
    	            	flowAction({'decisionExpr':'呈主管'});    	
    	        	}
    	    	});
    		}
    	});	
	}).end().find("#btnAccept").click(function(){	
		//分行[受檢單位]主管-覆核
		var _id = "_div_btnAccept";
		var _form = _id+"_form";
		if ($("#"+_id).size() == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			//dyna.push("	<table><tr><td>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />核定</label></p>");
			dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />退回</label></p>");
			//dyna.push(" </td></tr></table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: i18n.def["confirmApprove"],
	        width: 380,
            height: 180,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decisionExpr']:checked").val();
                    if(val=="1"){
                    	flowAction({'decisionExpr':'核定'});
                    }else if(val=="2"){
                    	flowAction({'decisionExpr':'退回'});
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	}).end().find("#btnReturn").click(function(){
		$.ajax({
            type: "POST",
            handler: _handler, action: "flowRestart",
            data:{
            	mainOid: $("#mainOid").val() 
            },                
            success: function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();
            }
        });
	});	
	
	$("#btn_imp_custId").click(function(){
		$.ajax({
            type: "POST",
            handler: _handler, action: "impCustName",
            data:{
            	mainOid: $("#mainOid").val() 
            },                
            success: function(json){
            	$("#custName").val(json.custName);
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	});
	
	$("#btn_imp_comId").click(function(){
		$.ajax({
            type: "POST",
            handler: _handler, action: "impComName",
            data:{
            	comId: $("#comId").val() 
            },                
            success: function(json){            	
            	$("#comName").val(json.comName);
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	});	
	
	var saveAction = function(opts){		
		if(tabForm.valid()){			
			return $.ajax({
                type: "POST",
                handler: _handler,
                data:$.extend( {
                	formAction: "saveMain",
                    mainOid: responseJSON.mainOid
                    }, 
                    tabForm.serializeData(),
                    ( opts||{} )
                ),                
                success: function(json){
                	tabForm.injectData(json);
                	//更新 opener 的 Grid
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
		}else{
			return $.Deferred();
		}
	}
	
	var flowAction = function(opts){
		return $.ajax({
            type: "POST",
            handler: _handler, action: "flowAction",
            data:$.extend( {
            	mainOid: $("#mainOid").val(), 
            	mainDocStatus: $("#mainDocStatus").val() 
                }
                , ( opts||{} )
            ),                
            success: function(json){            	
            	API.triggerOpener();//gridview.reloadGrid 
            	window.close();            	
            }
        });
	}
	
});


/**
 * 地址
 */
var AddrAction = {
    formId: '',
    signify: '',
    open: function(options){
        AddrAction.formId = options.formId;
        AddrAction.signify = options.signify;
        
        var $form = $('#' + AddrAction.formId);
        var cityCode = $form.find('#' + AddrAction.signify + 'City').val();
        var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
        
        var $addrForm = $('#AddrForm');
        $addrForm.setValue(); // $addrForm reset
        // 縣市
        $addrForm.find('#AddrCity').setItems({
            item: combos['counties'],
            format: '{key}',
            value: cityCode,
            fn: function(){
                var $addrForm = $('#AddrForm');
                var cityCode = $addrForm.find('#AddrCity').val();
                var combos = CommonAPI.loadCombos('counties' + cityCode);
                $addrForm.find('#AddrZip').setItems({
                    item: combos['counties' + cityCode],
                    format: '{key}'
                });
            }
        });
        // 鄉鎮市區
        $addrForm.find('#AddrZip').setItems({
            item: combos['counties' + cityCode],
            format: '{key}',
            value: $form.find('#' + AddrAction.signify + 'Zip').val()
        });
        // 地址
        $addrForm.find('#AddrAddr').val($form.find('#' + AddrAction.signify + 'Addr').val() || '');
        
        $('#AddrThickBox').thickbox({
            title: '', // i18n.cms1400v01["title"],
            width: 500,
            height: 200,
            align: 'center',
            valign: 'bottom',
            buttons: {
                'sure': function(){
                    var $addrForm = $('#AddrForm');
                    if ($addrForm.valid()) {
                        var $form = $('#' + AddrAction.formId);
                        $form.find('#' + AddrAction.signify + 'City').val($addrForm.find('#AddrCity').val());
                        $form.find('#' + AddrAction.signify + 'Zip').val($addrForm.find('#AddrZip').val());
                        $form.find('#' + AddrAction.signify + 'Addr').val($addrForm.find('#AddrAddr').val());
                        $form.find('#' + AddrAction.signify + 'Target').html($addrForm.find('#AddrCity :selected').text() +
                        $addrForm.find('#AddrZip :selected').text() +
                        $addrForm.find('#AddrAddr').val());
                        $("#addrNote").hide();
                        $.thickbox.close();
                    }
                },
                'close': function(){
                    $.thickbox.close();
                }
            }
        });
    }
};