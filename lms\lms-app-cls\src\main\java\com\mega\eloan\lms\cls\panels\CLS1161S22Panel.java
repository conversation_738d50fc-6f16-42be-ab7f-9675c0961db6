/* 
 * CLS1161S01Panel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 中鋼整批評等檔 - 詳細資料
 * </pre>
 * 
 * @since 2015/07/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/07/17,EL07623,new
 *          </ul>
 */
public class CLS1161S22Panel extends Panel {

	public CLS1161S22Panel(String id) {
		super(id);
	}

	public CLS1161S22Panel(String id, boolean updatePanelName) {
		super(id, updatePanelName);
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		MegaSSOUserDetails users = MegaSSOSecurityContext.getUserDetails();

		// hide tab
		// 912風控處資訊處可查看詳細資料
		boolean dtl_view = users.getSsoUnitNo().matches("900|912");
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// add(new Label("bt_for912").setVisible(dtl_view));
		model.addAttribute("bt_for912_visible", dtl_view);
	}

	/**/
	private static final long serialVersionUID = 1L;
}
