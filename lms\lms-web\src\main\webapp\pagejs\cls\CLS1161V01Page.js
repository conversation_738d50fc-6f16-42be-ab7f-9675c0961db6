$(document).ready(function(){
    var pageAction = {
        handler: 'cls1161formhandler',
        grid: null,
        oid: null,
        /**
         * 是否為動審表編製中
         */
        isGrid1: function(){
            return ((viewstatus || '') == "01O");
        },
        /**
         * 是否為動審表待覆核
         */
        isGrid2: function(){
            return (viewstatus || '').match(/02O|05O/);
        },
        /**
         *是否為動審表已核准覆核
         */
        isGrid3: function(){
            return (viewstatus || '').match(/03O|04O/);
        },
        build: function(){
            pageAction.grid = $("#gridview").iGrid({
                // localFirst: true,
                handler: 'cls1161gridhandler',
                height: 400,
                postData: {
                    formAction: 'queryViewData',
                    docStatus: viewstatus
                },
                rowNum: 17,
                rownumbers: true,
                sortname: 'custId',
                multiselect: viewstatus.indexOf("02O") > -1 || viewstatus.indexOf("05O") > -1  ? true : false,
                colModel: [{
                    name: 'oid',
                    hidden: true
                }, {
                    name: 'mainId',
                    hidden: true
                }, {
                    name: 'srcMainId',
                    hidden: true
                }, {
                    name: 'dupNo',
                    hidden: true
                }, {
                    name: 'docURL',
                    hidden: true
                }, {
                    name: 'docStatus',
                    hidden: true
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.approveTime"], // 核准日期
                    align: "left",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'approveTime',
                    formatter: 'date',
                    formatoptions: {
                        srcformat: 'Y-m-d H:i:s',
                        newformat: 'Y-m-d'
                    },
                    hidden: (!pageAction.isGrid3())
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.custId"], // 身分證統編重複碼
                    align: "left",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'custId',
                    formatter: 'click',
                    onclick: function(cellvalue, options, rowObject){
                        pageAction.openDoc(rowObject);
                    }
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.custName"], // 借款人姓名
                    align: "left",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'custName'
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.caseType"], // 種類
                    align: "left",
                    width: 30, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'caseType'
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.caseNo"]+"/"+i18n.cls1161v01["C160M01A.caseNo2"], // 案號
                    align: "center",
                    width: 120, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'caseNo'
                },            /*
                 * { colHeader : i18n.cls1161v01["C160M01A.caseSeq"], // 動用額度序號
                 * align : "center", width : 60, // 設定寬度 sortable : false, //
                 * 是否允許排序 name : 'caseSeq' },
                 */
                {
                    colHeader: i18n.cls1161v01["C160M01A.apprId"], // 經辦
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'apprId'
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.useType"], // 是否先行動用
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'useType'
                }, {
                    colHeader: i18n.cls1161v01["C160M01A.caseDate"], // 簽案日期
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'caseDate',
                    hidden: !pageAction.isGrid1()
                }, {
                    colHeader: i18n.cls1161v01["C160M01D.willFinishDate"], // 預定補全日期
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'c160m01d.willFinishDate',
                    hidden: pageAction.isGrid1()
                }, {
                    colHeader: i18n.cls1161v01["C160M01D.finishDate"], // 辦妥日期
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'c160m01d.finishDate',
                    hidden: !pageAction.isGrid3()
                }, {
                    colHeader: i18n.cls1161v01["C160M01D.bfReCheckDate"], // 辦妥覆核
                    align: "center",
                    width: 60, // 設定寬度
                    sortable: true, // 是否允許排序
                    name: 'c160m01d.bfReCheckDate',
                    formatter: pageAction.bfReCheckDateFormatter,
                    hidden: !pageAction.isGrid3()
                }           /*
                 * , { colHeader : i18n.cls1161v01["C160M01A.docStatus"], //
                 * 文件狀態 align : "center", width : 60, // 設定寬度 sortable : true, //
                 * 是否允許排序 name : 'docStatus' }
                 */
                ],
                ondblClickRow: function(rowid){
                    var data = pageAction.grid.getRowData(rowid);
                    pageAction.openDoc(data);
                }
            });
            //僅有電銷權限(EX01、EX02)時隱藏 "登錄"、"先行動用待辦控制表" 按鈕
            //根據權限隱藏特定物件
            $.ajax({
                handler: pageAction.handler,
                data: {
                    formAction: "check_only_expermission"
                }
            }).done(function(responseData){
                if(responseData.only_ex_permission){//僅有電銷權限, 無其他EL相關權限 true=是, false=否
                    $(".only-ex-permission").hide();
                }
            });
            
            // build button
            // 新增
            $("#buttonPanel").find("#btnAdd").on('click', function(){
                get_caseType().done(function(){        	    		
                $('#addThickBox').thickbox({
                    title: (i18n.def['newData'] || '新增') +
                    i18n.cls1161v01['add.title'],
                    width: 350,
                    height: 180,
                    modal: true,
                    align: 'center',
                    valign: 'bottom',
                    i18n: i18n.def,
                    buttons: {
                        'sure': function(){
                            if ($('#addForm').valid()) {
                                $.ajax({
                                    handler: pageAction.handler,
                                    data: {
                                        formAction: 'addCase',
                                        caseType: $('#addForm').find('#caseType:checked').val()
                                    }
                                }).done(function(response){
                                    $.thickbox.close();
                                    pageAction.openDoc(response.data);
                                    // pageAction.grid.reload();
                                });
                            }
                        },
                        'close': function(){
                            $.thickbox.close();
                        }
                    }
                });
            });        
        }) // 編輯 & 調閱
        .end().find("#btnModify,#btnView").on('click', function(){
                var data = pageAction.grid.getSingleData();
                if (data) {
                    pageAction.openDoc(data);
                }
            }) // 刪除
        .end().find("#btnDelete").on('click', function(){
                var data = pageAction.grid.getSingleData();
                if (data) {
                    MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                        if (action) {
                            $.ajax({
                                handler: pageAction.handler,
                                data: $.extend(data, {
                                    formAction: 'deleteCase'
                                })
                            }).done(function(response){
                                pageAction.grid.reload();
                                MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                            });
                        }
                    });
                }
            }) // 登錄
        .end().find('#btnLogeIN').on('click', function(){
                var data = pageAction.grid.getSingleData();
                // 040 先行動用_已覆核
                if (data) {
                    if (data.docStatus === '04O') {
                        pageAction.openC160M01D(data);
                    } else if (data.useType != '') {
                        CommonAPI.showMessage(i18n.cls1161v01['C160M01A.message01']);
                    } else {
                        CommonAPI.showMessage(i18n.cls1161v01['C160M01A.message03']);
                    }
                }
            }) // 篩選
        .end().find('#btnFilter').on('click', function(){
                pageAction.openFilter();
            }) // 先行動用控制表
        .end().find('#btnUseFirstTable').on('click', function(){
                $.form.submit({
                    url: '../simple/FileProcessingService',
                    target: '_blank',
                    data: {
                        fileDownloadName: 'lms1601r04.pdf',
                        serviceName: 'lms1601r01rptservice',
                        type: 'R04',
                        kind: 'CLS'
                    }
                });
            }).end().find("#btnBatchSelectAprvProd69").on('click', function(){
            //2020/05/20 add 勞工紓困案整批勾選
            
            //判斷是否為prod69
            var rowData = $("#gridview").getRowData();
            var resMaindId=[];
            for (var i = 0; i < rowData.length; i++) {
                resMaindId.push(rowData[i].srcMainId);
            }
            
            $.ajax({
                    handler: "cls1141m01formhandler",
                    data: {
                        formAction: "checkIsProd69",
                        mainIds: resMaindId
                    }
                }).done(function(result){
                    //alert(result.mainIdByIsProd69);
                    for (var i = 0; i < rowData.length; i++) {
                        if (result.mainIdByIsProd69.indexOf(rowData[i].srcMainId) > -1) {
                            if ($("#gridview #" + (i + 1) + " input[type=checkbox]:checked").length == 0) {
                                //排除已經選取
                                //改用click方法
                                //$("#jqg_gridview_" + (i + 1) ).attr('checked', 'checked');
                                $("#gridview #" + (i + 1) ).click();
                            }
                        }
                    }
                });
        }).end().find("#btnBatchAprvProd69").on('click', function(){
            //2020/05/20 add 勞工紓困案整批覆核
            var ret = $("#gridview").getGridParam('selarrrow');
            ret && ret.length != 0  ? API.confirmMessage(i18n.def["confirmApprove"], function(result){
                if (result) {
                    var resOid=[] , resAuthLvl=[];
                    for( var i=0; i < ret.length; i++ ){
                        resOid.push($("#gridview").getRowData(ret[i]).oid);
                    }
                    $("#aprv_dialog").thickbox({
                        open: function(){
                            //增加覆核預設值
                            $('input[name="queryButtonType"][value="8"]').prop("checked", true);
                        },
                        modal: false,
                        height: 100,
                        width: 300,
                        valign: 'bottom',
                        align: 'center',
                        buttons: {
                            "sure": function(){
                                var value = $("[name=queryButtonType]:checked").val();
                                
                                var flowChkDateDfd = $.Deferred();
                                
                                //分行內-輸入核准日 checkDate
                                var checkDate;
                                
                                if (value != "1") {
                                    //checkDate()
                                    $("#forCheckDate").val(CommonAPI.getToday());
                                    $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
                                        title: i18n.cls1161v01["l120v01.chkDate"],
                                        width: 100,
                                        height: 100,
                                        modal: true,
                                        valign: "bottom",
                                        align: "center",
                                        readOnly: false,
                                        i18n: i18n.def,
                                        buttons: {
                                            "sure": function(){
                                                var forCheckDate = $("#forCheckDate").val();
                                                if ($.trim(forCheckDate) == "") {
                                                    return CommonAPI.showErrorMessage(i18n.cls1161v01["l120v01.chkDate"]);
                                                }
                                                checkDate = forCheckDate;
                                                flowChkDateDfd && flowChkDateDfd.resolve();
                                                
        //							                flowAction({
        //							                            flowAction: "check",
        //							                            checkDate: forCheckDate
        //					                        });
                                                $.thickbox.close();
                                            },
                                            
                                            "cancel": function(){
                                                $.thickbox.close();
                                            }
                                        }
                                    });
                                } else {
                                    flowChkDateDfd && flowChkDateDfd.resolve();
                                }
                                
                                flowChkDateDfd.done(function(){
                                    $.ajax({
                                            handler: "cls1161formhandler",
                                            data: {
                                                formAction: "flowActionBatch",
                                                activity: value == "1" ? "back" : "accept",
                                                oids: resOid,
                                                approveDate: checkDate
                                            }
                                        }).done(function(){
                                            $.thickbox.close();
                                            $("#gridview").trigger("reloadGrid");
                                            API.showPopMessage(i18n.def["runSuccess"], $.thickbox.close());
                                        });
                                    });
                                },
                                "cancel": function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                    });
                }
            }) : API.showErrorMessage(i18n.def.action_005);
        });
            
            // 篩選form
            $("#filterForm").find('input[name=queryData]').on('click', function(){
                var $fm = $("#filterForm");
                $fm.find('.queryData').hide();
                $fm.find('.queryData input').val('');
                if ($(this).val() == '1') {
                    $fm.find('#queryDataTr1').show();
                } else {
                    $fm.find('#queryDataTr2').show();
                    $fm.find('#fromDate').val(CommonAPI.getToday());
                    $fm.find('#endDate').val(CommonAPI.getToday());
                }
            });
            
            // 主管
            var $form = $('#C160M01DForm');
            if ($form.length > 0) {
                $.ajax({
                    handler: 'flowactionformhandler',
                    data: {
                        formAction: 'manager',
                        formId: 'empty',
                        sign: ['甲級主管']
                        // 首長,單位主管,甲級主管,乙級主管,經辦人員
                    }
                }).done(function(response){
                    $form.find('#managerId').setItems({
                        item: $.extend({}, response.manager, {
                            '99': (i18n.cls1161v01['C160M01A.other'] || '其他')
                        }),
                        format: '{key}',
                        fn: function(){
                            $('#managerNm').hide();
                            if ($('#managerId').val() == '99') {
                                $('#managerNm').show();
                            }
                        }
                    });
                });
            }
        },
        /**
         * 開啟文件
         */
        openDoc: function(data){
            $.form.submit({
                url: data.docURL, // "../cls/cls1161m01/02",
                data: {
                    oid: data.oid || '',
                    mainId: data.mainId || '',
                    mainOid: data.oid || '',
                    mainDocStatus: viewstatus,
                    txCode: window.txCode || responseJSON.txCode || ''
                },
                target: data.oid
            });
        },
        load: function(data){
            $.ajax({
                handler: pageAction.handler,
                data: $.extend(data, {
                    formAction: 'loadDetial',
                    formName: 'C160M01AForm',
                    noOpenDoc: true
                })
            }).done(function(response){
                $('#C160M01DForm').setValue(response.C160M01AForm);
                $('#C160M01DForm').setValue(response.C160M01DForm, false);
            });
        },
        openC160M01D: function(data){
            $('#C160M01DThickBox').thickbox({
                title: '登錄',
                width: 700,
                height: 400,
                modal: true,
                align: 'center',
                valign: 'bottom',
                i18n: i18n.def,
                buttons: {
                    'sure': function(){
                        if ($('#C160M01DForm').valid()) {
                            MegaApi.confirmMessage(i18n.def['actoin_001'], function(action){
                                if (action) {
                                    $.ajax({
                                        handler: pageAction.handler,
                                        formId: 'C160M01DForm',
                                        data: $.extend(pageAction.data, {
                                            formAction: 'srue',
                                            activity: 'send',
                                            noOpenDoc: true
                                        })
                                    }).done(function(response){
                                        $.thickbox.close();
                                        pageAction.grid.reload();
                                        MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["runSuccess"]);
                                    });
                                }
                            });
                        }
                    },
                    'close': function(){
                        $.thickbox.close();
                    }
                }
            });
            // load data
            pageAction.data = $.extend({}, data);
            pageAction.load(data);
        },
        /**
         * 篩選
         */
        openFilter: function(){
            var $form = $("#filterForm");
            // 初始化
            $("#filterForm").setValue({
                queryData: '1'
            }, true);
            // $form.find('[name=queryData][value=1]').attr("checked",
            // true).trigger('click');
            
            $('#filterBox').thickbox({
                title: i18n.cls1161v01['add.title'] || '',
                width: 500,
                height: 150,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    'sure': function(){
                        var $form = $('#filterForm');
                        if ($form.valid()) {
                            var endData = $form.find('#endDate').val() ||
                            '';
                            var fromData = $form.find('#fromDate').val() ||
                            '';
                            // 起始日期不能大於結束日期
                            if (fromData || endData) {
                                if (fromData > endData) {
                                    CommonAPI.showErrorMessage(i18n.cls1161v01['C160M01A.message02']);
                                    return;
                                }
                            }
                            pageAction.grid.reload($form.serializeData());
                            $.thickbox.close();
                        }
                    },
                    'cancel': function(){
                        $.thickbox.close();
                    }
                }
            });
        },
        bfReCheckDateFormatter: function(cellvalue, otions, rowObject){
            var value = "";
            if ($.trim(cellvalue)) {
                value = "Ｖ"
            }
            return value
        }
    }

    function get_caseType(){
        var my_dfd = $.Deferred();

        if($('#addForm').find('[name=caseType]').length <=1){
            var combos = CommonAPI.loadCombos(["cls1161m01_caseType"]);
            $('#addForm').find('#caseType').setItems({
                item: combos['cls1161m01_caseType'],
                format: '{key}',
                size: 1
            });
            my_dfd.resolve();
        }else{
            my_dfd.resolve();
        }		
        return my_dfd.promise();
    }

    pageAction.build();
    if ((viewstatus || '').match(/03O|04O/)) {
        // pageAction.openFilter();
    }
});
