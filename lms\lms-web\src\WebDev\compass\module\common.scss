/* Header Footer----------------------------- */
.body {
	width: 980px; /*width: 964 px;*/
	margin: 0 auto;
	background: #ffffff;
	border-left: 1px solid #CCC;
	border-right: 1px solid #CCC;
	border-bottom: 1px solid #CCC;
	display: block;
}

body {
	margin: 0 auto;
	padding: 0;
	background: #ffffff;
	height: 100%
}

.block {
	margin: 0;
	padding: 0;
	display: block;
}

/*news*/
.mainCtxArea {
	vertical-align: top;
	padding: 0 0 0 0 0;
	margin: 0;
	/*background-image: url('../img/work_bg.gif');
	background-repeat: repeat;*/
	width: 100%;
}

.news2 { /*padding: 15px 60px 0 60px;*/
	padding: 5px 10px 0 10px;
	margin: 0;
	height: auto;
}

.news {
	padding: 0px;
	margin: 0px;
}

.news ul,.news2 ul {
	list-style-type: none;
	padding-left: 5px;
	margin: 0;
	width: 95%;
	font-size: 13px;
}

.news li,.news2 li {
	font-size: 15px;
	padding: 0;
	margin-bottom: 5px;
	background-image: url('../img/xxexpand2.gif'); /**/
	background-repeat: no-repeat;
	background-position: 0px 5px; /*left top*/
	padding-left: 18px;
	border: 1px;
	border-color: #CCC;
	border-bottom-style: dashed;
	line-height: 20px;
}

.news fieldset {
	border: 2px solid #CCC;
	margin: 0;
	padding: 0;
	width: 95%;
}

.news2 fieldset {
	border: 1px solid #CCC;
	margin: 20;
	padding: 20;
	width: 95%;
	height: auto;
}

.news2 legend {
	color: #3B7092;
	font-size: 14px;
	font-weight: bold;
	line-height: 35px;
}

legend {
	color: #3B7092;
	/*font-size: 13px;*/
	font-weight: bold;
	line-height: 26px;
}

.footer {
	background: #d5deeb;
	color: #808080;
	font-size: 12px;
	width: 980px;
	*margin: 0px auto;
	background: url(../img/footer_bg.jpg) no-repeat;
}

/* mLeft Div styles -----------*/
#mLeft {
	width: 231px;
	height: 450px;
	margin: 0px 5px 0px 5px;
	overflow: auto;
}

/* Clear Fix------------------------------------ */
.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.clearfix {
	display: inline-block;
	*display: block;
}

/* grid ---------------------------------*/
.ui-jqgrid-view { 
	FONT: 13px ;
}

.ui-jqgrid-view th,.ui-jqgrid-view td {
	vertical-align: middle;
}

.capgrid {
	margin-top: 5px;
}

table.ui-jqgrid-btable tr.altClass {
	background: #f0f6f8;
}

table.ui-jqgrid-btable tr.altClass:hover {
	background: #88B1D2; /*88B1D2--fef6da*/
}

/*
 table.ui-jqgrid-btable tr{background: #D8EDCA;}
 table.ui-jqgrid-btable tr.altClass{background: #FFFFFF;}
 table.ui-jqgrid-btable tr.altClass:hover{background: #CCC;}
 table.ui-jqgrid-btable tr.jqgroup{background:#D8E5EE;}
 */
/* ckeditor new item */
/* teset Dialog */
.ckeditor-insert-img input {
	font-size: 13px !important;
}

/*.ckeditor-insert-img .ui-dialog-titlebar,.ckeditor-insert-img .ui-dialog-content .ui-resizable-handle{display:none;}*/
/** project **/
span.rmemo {
	color: #900;
}

.placeholder {
	color: #C0C0C0;
}
.doc-tabs ,.funcContainer{
	display:none;
}
#uploadFileDialog div{
	margin:5px 0;
}
#uploadFileDialog .tit{
	font-weight:bold;
}
#uploadFileDialog #subTitle{
	color:#900;
}
// override jqgrid default css
.ui-jqgrid-table-striped > tbody > tr:nth-of-type(odd) {
	background: #f0f6f8;
}
.ui-jqgrid-table-striped > tbody > tr:nth-of-type(odd):hover {
	background: #88B1D2;
}
// override ckeditor default css
.editor__editable,
/* Classic build. */
main .ck-editor[role='application'] .ck.ck-content,
/* Decoupled document build. */
.ck.editor__editable[role='textbox'],
.ck.ck-editor__editable[role='textbox'],
/* Inline & Balloon build. */
.ck.editor[role='textbox'] {
	width: calc(100% - 18px); /* width 100% - padding (8px * 2) - border (1px * 2) */
	background: #fff;
	font-size: 1em;
	line-height: 1.6em;
	min-height: var(--ck-sample-editor-min-height);
	padding: 8px 8px; /* simulate for 3.x margin 8px */
}
.ck.ck-dropdown__panel { /* toolbar dropdown */
	max-height: 400px;
	overflow-y: auto;
	overflow-x: hidden;
}
.ck.ck-content li { /* for list icon */
	list-style: inherit;
}
.ck.ck-content ol, .ck.ck-content ul { /* for list indent */
	padding: revert;
}
figure {
    display: block;
}
figure.table table {
    width: 100%;
}
