/* 
 * L784M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L784M01A;


/** 企金授信管理報表檔 **/
public interface L784M01ADao extends IGenericDao<L784M01A> {

	L784M01A findByOid(String oid);
	
	L784M01A findByMainIdUn(String mainId);

	List<L784M01A> findByMainId(String mainId);

	List<L784M01A> findByIndex01(Date dataDate, String branchId);

	L784M01A findByBrNoAndRptTypeAndBaseDate(String brNo, int i,
			String baseDate);
	List<L784M01A> findByCustIdDupId(String custId,String DupNo);
}