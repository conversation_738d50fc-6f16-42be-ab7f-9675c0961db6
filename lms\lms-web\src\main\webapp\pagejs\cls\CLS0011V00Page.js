$(document).ready(function(){
    var pelopleDef = $.Deferred();
    var grirDef = $.Deferred();
    pelopleDef.done(setPelopleList);
    grirDef.done(gridview);
    $.ajax({
        handler: "cls0011m01formhandler",
        data: {
            formAction: "queryFilter"
        },
        success: function(obj){
        
            if (obj.show) {
                //狀況1 當查詢到儲存篩選條件 並無勾選下次不顯示視窗，跳出"篩選視窗"並把篩選的條件帶到視窗上
                //狀況3 無查詢篩選條件 直接出現"篩選視窗"
                grirDef.resolve();
                openFilterBox(obj);
            } else {
                //狀況3  當查詢到儲存篩選條件 並勾選下次不顯示視窗，直接帶入所有的篩選條件並執行grid
                obj.formAction = "queryFilter"
                gridview(obj);
            }
            
        }//close success function
    }); //close ajax
    function gridview(options){
        $("#gridview").iGrid({
            handler: 'cls0011gridhandler',
            height: 350,
            width: 785,
            autowidth: false,
            postData: $.extend({
                formAction: ""
            }, options),
            rowNum: 15,
            colModel: [{
                colHeader: i18n.cls0011v00['v0015.date'],//日期,
                name: 'caseDate',
                width: 50,
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                },
                align: "left",
                sortable: true
            }, {
                colHeader: i18n.cls0011v00['v0015.mainCustId'],//主要借款人統編,
                name: 'custId',
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls0011v00['v0015.mainCust'],//主要借款人,
                name: 'custName',
                width: 100,
                sortable: true,
                formatter: 'click',
                onclick: openDoc
            }, {
                colHeader: i18n.cls0011v00['v0015.caseNo'],//案號,
                name: 'caseNo',
                width: 150,
                sortable: true
            }, {
                colHeader: i18n.cls0011v00['v0015.caseName'],//文件名稱,
                name: 'typeShow',
                width: 60,
                align: "left",
                sortable: true
            }, {
                colHeader: i18n.cls0011v00['v0015.state'],//狀態,
                name: 'docStatus',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                colHeader: i18n.cls0011v00['v0015.boss'],//經辦/主管
                name: 'updater',
                width: 60,
                sortable: true,
                align: "center"
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }, {
                name: 'docURL',
                hidden: true
            }, {
                name: 'type',
                hidden: true
            }, {
                name: 'docType',
                hidden: true
            }, {
                name: 'docCode',
                hidden: true
            }, {
                name: 'docKind',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridview").getRowData(rowid);
                openDoc(null, null, data);
            }
        });
    }
    
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',//'../lms/xxx/01'
            data: {
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                //type先暫放文件狀態
                mainDocStatus: rowObject.type,
                docType: rowObject.docType,
                docCode: rowObject.docCode,
                docKind: rowObject.docKind,
                docURL: rowObject.docURL
            },
            target: rowObject.oid
        });
    }
    
    //篩選
    function openFilterBox(obj){
    
        pelopleDef.resolve();
        var $filterForm = $("#filterForm");
        $filterForm.reset();
        $("#startDate").val(CommonAPI.getToday());
        $("#endDate").val(CommonAPI.getToday());
        if (obj && obj.type) {
            // 設定相關資料到對應欄位裡
            $("[name=selectRadio][value=" + obj.type + "]").attr("checked", true);
            $("[name=sortType][value=" + obj.sort + "]").attr("checked", true);
            $("#selectCase").val(obj.selectCase);
            $("#selectDoc").val(obj.selectDoc);
            switch (obj.type) {
                case "1":
                    $("#startDate").val(obj.startDate);
                    $("#endDate").val(obj.endDate);
                    break;
                case "2":
                    $("#custId").val(obj.custId);
                    break;
                case "3":
                    $("#mangerId").val(obj.mangerId);
                    break;
            }
            
        }
        $("#filterDialog").thickbox({
            // v0015.title01=篩選條件設定
            title: i18n.cls0011v00['v0015.title01'],
            width: 550,
            height: 480,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                
                    if ($("#selectDoc").val() == "" || $("#selectCase").val() == "") {
                        //v0015.title03=請選文件名稱
                        //v0015.title04=與&nbsp;文件狀態
                        return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.title03"] + i18n.cls0011v00["v0015.title04"]);
                    }
                    
                    if (!$("input[name=sortType]:checked").val()) {
                        //v0015.title12=請設定篩選結果採
                        return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.title12"]);
                    }
                    if ($("input[name=sortType]:checked").val() == "desc") {
                    
                    }
                    switch ($("input[name=selectRadio]:checked").val()) {
                        case "1":
                            if (!$("#filterForm").valid()) {
                                return false;
                            }
                            if ($.trim($("#endDate").val()) == "" || $.trim($("#startDate").val()) == "") {
                                //v0015.error02=請輸入日期
                                return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.error02"]);
                            }
                            
                            var start = $("#startDate").val().split("-");
                            var end = $("#endDate").val().split("-");
                            var startDate = new Date(start[0], start[1], start[2]);
                            var endData = new Date(end[0], end[1], end[2]);
                            if (startDate > endData) {
                            
                                //v0015.error01=起始日期不能大於結束日期
                                return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.error01"]);
                            }
                            
                            filterGrid({
                                startDate: $("#startDate").val(),
                                endDate: $("#endDate").val()
                            });
                            break;
                        case "2":
                            var custId = $filterForm.find("#custId").val();
                            if ($.trim(custId) == "") {
                                //v0015.error03=借款人統編尚未輸入
                                return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.error03"]);
                            }
                            
                            filterGrid({
                                custId: custId
                            });
                            break;
                        case "3":
                            var mangerId = $("#selectManger").val();
                            if ("0" == mangerId) {
                                //v0015.title11=起始日期不能大於結束日期
                                return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.title11"]);
                            }
                            
                            filterGrid({
                                mangerId: mangerId
                            });
                            break;
                        default:
                            //v0015.error04=請選擇篩選條件
                            return CommonAPI.showErrorMessage(i18n.cls0011v00["v0015.error04"]);
                            break;
                    }
                    
                    $.thickbox.close();
                },
                "cancel": function(){
                    filterGrid({
                        type: "0"
                    });
                    $.thickbox.close();
                }
            }
        });
    }
    
    //取得人員清單
    function setPelopleList(){
        $.ajax({
            handler: "lms0015m01formhandler",
            data: {
                formAction: "queryPeople"
            },
            success: function(obj){
                $("#selectManger,#changePeople").setItems({//塞複製的select
                    item: obj.peopleList,
                    format: "{value} - {key}"
                });
            }//close success function
        }); //close ajax
    }
    
    
    
    //grid資料篩選
    function filterGrid(sendData){
        if (sendData && sendData.type != 0) {
            //儲存篩選條件
            $.ajax({
                handler: "lms0015m01formhandler",
                data: $.extend({
                    formAction: "saveFilter",
                    type: $("input[name=selectRadio]:checked").val(),
                    sort: $("input[name=sortType]:checked").val(),
                    selectDoc: $("#selectDoc").val(),
                    selectCase: $("#selectCase").val(),
                    checkShow: $("input[id=checkShow]:checked").val()
                }, sendData || {}),
                success: function(obj){
                    $("#gridview").jqGrid("setGridParam", {
                        postData: $.extend({
                            formAction: "queryFilter",
                            type: $("input[name=selectRadio]:checked").val(),
                            sort: $("input[name=sortType]:checked").val(),
                            selectDoc: $("#selectDoc").val(),
                            selectCase: $("#selectCase").val()
                        }, sendData || {}),
                        search: true
                    }).trigger("reloadGrid");
                }//close success function
            }); //close ajax
        } else {
            $("#gridview").jqGrid("setGridParam", {
                postData: $.extend({
                    formAction: "queryFilter"
                }, sendData || {}),
                search: true
            }).trigger("reloadGrid");
        }
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
        $.ajax({
            handler: "lms0015m01formhandler",
            data: {
                formAction: "queryFilter"
            },
            success: function(obj){
            
                openFilterBox(obj);
            }//close success function
        }); //close ajax
        //  openFilterBox();
    
    }).end().find("#btnCaseToChange").click(function(){
        pelopleDef.resolve();
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        $("#changeBox").thickbox({
            // v0015.title18=選擇案件改分派人員
            title: i18n.cls0011v00['v0015.title18'],
            width: 300,
            height: 100,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    $.ajax({
                        handler: "lms0015m01formhandler",
                        data: {
                            formAction: "changePeople",
                            selectPeople: $("#changePeople").val(),
                            selectCaseOid: result.oid,
                            selectCaseType: result.type
                        }
                    }); //close ajax
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    });
    
});
