/* 
* jqGrid  4.1.2 - j<PERSON><PERSON><PERSON> 
* Copyright (c) 2008, <PERSON>, <EMAIL> 
* Dual licensed under the MIT and GPL licenses 
* http://www.opensource.org/licenses/mit-license.php 
* http://www.gnu.org/licenses/gpl-2.0.html 
* Date:2011-07-20 
* Modules: grid.base.js; jquery.fmatter.js; grid.custom.js; grid.common.js; grid.formedit.js; grid.filter.js; grid.inlinedit.js; grid.celledit.js; jqModal.js; jqDnR.js; grid.subgrid.js; grid.grouping.js; grid.treegrid.js; grid.import.js; JsonXml.js; grid.tbltogrid.js; grid.jqueryui.js; 
*/
(function(b){b.jgrid=b.jgrid||{};b.extend(b.jgrid,{htmlDecode:function(f){if(f&&(f=="&nbsp;"||f=="&#160;"||f.length==1&&f.charCodeAt(0)==160))return"";return!f?f:String(f).replace(/&amp;/g,"&").replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&quot;/g,'"')},htmlEncode:function(f){return!f?f:String(f).replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;").replace(/\"/g,"&quot;")},format:function(f){var j=b.makeArray(arguments).slice(1);if(f===undefined)f="";return f.replace(/\{(\d+)\}/g,
function(h,c){return j[c]})},getCellIndex:function(f){f=b(f);if(f.is("tr"))return-1;f=(!f.is("td")&&!f.is("th")?f.closest("td,th"):f)[0];if(b.browser.msie)return b.inArray(f,f.parentNode.cells);return f.cellIndex},stripHtml:function(f){f+="";var j=/<("[^"]*"|'[^']*'|[^'">])*>/gi;if(f)return(f=f.replace(j,""))&&f!=="&nbsp;"&&f!=="&#160;"?f.replace(/\"/g,"'"):"";else return f},stringToDoc:function(f){var j;if(typeof f!=="string")return f;try{j=(new DOMParser).parseFromString(f,"text/xml")}catch(h){j=
new ActiveXObject("Microsoft.XMLDOM");j.async=false;j.loadXML(f)}return j&&j.documentElement&&j.documentElement.tagName!="parsererror"?j:null},parse:function(f){if(f.substr(0,9)=="while(1);")f=f.substr(9);if(f.substr(0,2)=="/*")f=f.substr(2,f.length-4);f||(f="{}");return b.jgrid.useJSON===true&&typeof JSON==="object"&&typeof JSON.parse==="function"?JSON.parse(f):eval("("+f+")")},parseDate:function(f,j){var h={m:1,d:1,y:1970,h:0,i:0,s:0},c,g,k;c=/[\\\/:_;.,\t\T\s-]/;if(j&&j!==null&&j!==undefined){j=
b.trim(j);j=j.split(c);f=f.split(c);var l=b.jgrid.formatter.date.monthNames,a=b.jgrid.formatter.date.AmPm,q=function(x,y){if(x===0){if(y==12)y=0}else if(y!=12)y+=12;return y};c=0;for(g=f.length;c<g;c++){if(f[c]=="M"){k=b.inArray(j[c],l);if(k!==-1&&k<12)j[c]=k+1}if(f[c]=="F"){k=b.inArray(j[c],l);if(k!==-1&&k>11)j[c]=k*****}if(f[c]=="a"){k=b.inArray(j[c],a);if(k!==-1&&k<2&&j[c]==a[k]){j[c]=k;h.h=q(j[c],h.h)}}if(f[c]=="A"){k=b.inArray(j[c],a);if(k!==-1&&k>1&&j[c]==a[k]){j[c]=k-2;h.h=q(j[c],h.h)}}if(j[c]!==
undefined)h[f[c].toLowerCase()]=parseInt(j[c],10)}h.m=parseInt(h.m,10)-1;c=h.y;if(c>=70&&c<=99)h.y=1900+h.y;else if(c>=0&&c<=69)h.y=2E3+h.y}return new Date(h.y,h.m,h.d,h.h,h.i,h.s,0)},jqID:function(f){return String(f).replace(/[!"#$%&'()*+,.\/:;<=>?@\[\\\]\^`{|}~]/g,"\\$&")},guid:1,uidPref:"jqg",randId:function(f){return(f?f:b.jgrid.uidPref)+b.jgrid.guid++},getAccessor:function(f,j){var h,c,g=[],k;if(typeof j==="function")return j(f);h=f[j];if(h===undefined)try{if(typeof j==="string")g=j.split(".");
if(k=g.length)for(h=f;h&&k--;){c=g.shift();h=h[c]}}catch(l){}return h},ajaxOptions:{},from:function(f){return new function(j,h){if(typeof j=="string")j=b.data(j);var c=this,g=j,k=true,l=false,a=h,q=/[\$,%]/g,x=null,y=null,H=0,L=false,M="",P=[],U=true;if(typeof j=="object"&&j.push){if(j.length>0)U=typeof j[0]!="object"?false:true}else throw"data provides is not an array";this._hasData=function(){return g===null?false:g.length===0?false:true};this._getStr=function(o){var m=[];l&&m.push("jQuery.trim(");
m.push("String("+o+")");l&&m.push(")");k||m.push(".toLowerCase()");return m.join("")};this._strComp=function(o){return typeof o=="string"?".toString()":""};this._group=function(o,m){return{field:o.toString(),unique:m,items:[]}};this._toStr=function(o){if(l)o=b.trim(o);k||(o=o.toLowerCase());return o=o.toString().replace(/\\/g,"\\\\").replace(/\"/g,'\\"')};this._funcLoop=function(o){var m=[];b.each(g,function(r,D){m.push(o(D))});return m};this._append=function(o){var m;if(a===null)a="";else a+=M===
""?" && ":M;for(m=0;m<H;m++)a+="(";if(L)a+="!";a+="("+o+")";L=false;M="";H=0};this._setCommand=function(o,m){x=o;y=m};this._resetNegate=function(){L=false};this._repeatCommand=function(o,m){if(x===null)return c;if(o!==null&&m!==null)return x(o,m);if(y===null)return x(o);if(!U)return x(o);return x(y,o)};this._equals=function(o,m){return c._compare(o,m,1)===0};this._compare=function(o,m,r){if(r===undefined)r=1;if(o===undefined)o=null;if(m===undefined)m=null;if(o===null&&m===null)return 0;if(o===null&&
m!==null)return 1;if(o!==null&&m===null)return-1;if(!k&&typeof o!=="number"&&typeof m!=="number"){o=String(o).toLowerCase();m=String(m).toLowerCase()}if(o<m)return-r;if(o>m)return r;return 0};this._performSort=function(){if(P.length!==0)g=c._doSort(g,0)};this._doSort=function(o,m){var r=P[m].by,D=P[m].dir,T=P[m].type,I=P[m].datefmt;if(m==P.length-1)return c._getOrder(o,r,D,T,I);m++;r=c._getGroup(o,r,D,T,I);D=[];for(T=0;T<r.length;T++){I=c._doSort(r[T].items,m);for(var C=0;C<I.length;C++)D.push(I[C])}return D};
this._getOrder=function(o,m,r,D,T){var I=[],C=[],ca=r=="a"?1:-1,V,fa;if(D===undefined)D="text";fa=D=="float"||D=="number"||D=="currency"||D=="numeric"?function(R){R=parseFloat(String(R).replace(q,""));return isNaN(R)?0:R}:D=="int"||D=="integer"?function(R){return R?parseFloat(String(R).replace(q,"")):0}:D=="date"||D=="datetime"?function(R){return b.jgrid.parseDate(T,R).getTime()}:b.isFunction(D)?D:function(R){R||(R="");return b.trim(String(R).toUpperCase())};b.each(o,function(R,$){V=m!==""?b.jgrid.getAccessor($,
m):$;if(V===undefined)V="";V=fa(V,$);C.push({vSort:V,index:R})});C.sort(function(R,$){R=R.vSort;$=$.vSort;return c._compare(R,$,ca)});D=0;for(var oa=o.length;D<oa;){r=C[D].index;I.push(o[r]);D++}return I};this._getGroup=function(o,m,r,D,T){var I=[],C=null,ca=null,V;b.each(c._getOrder(o,m,r,D,T),function(fa,oa){V=b.jgrid.getAccessor(oa,m);if(V===undefined)V="";if(!c._equals(ca,V)){ca=V;C!==null&&I.push(C);C=c._group(m,V)}C.items.push(oa)});C!==null&&I.push(C);return I};this.ignoreCase=function(){k=
false;return c};this.useCase=function(){k=true;return c};this.trim=function(){l=true;return c};this.noTrim=function(){l=false;return c};this.execute=function(){var o=a,m=[];if(o===null)return c;b.each(g,function(){eval(o)&&m.push(this)});g=m;return c};this.data=function(){return g};this.select=function(o){c._performSort();if(!c._hasData())return[];c.execute();if(b.isFunction(o)){var m=[];b.each(g,function(r,D){m.push(o(D))});return m}return g};this.hasMatch=function(){if(!c._hasData())return false;
c.execute();return g.length>0};this.andNot=function(o,m,r){L=!L;return c.and(o,m,r)};this.orNot=function(o,m,r){L=!L;return c.or(o,m,r)};this.not=function(o,m,r){return c.andNot(o,m,r)};this.and=function(o,m,r){M=" && ";if(o===undefined)return c;return c._repeatCommand(o,m,r)};this.or=function(o,m,r){M=" || ";if(o===undefined)return c;return c._repeatCommand(o,m,r)};this.orBegin=function(){H++;return c};this.orEnd=function(){if(a!==null)a+=")";return c};this.isNot=function(o){L=!L;return c.is(o)};
this.is=function(o){c._append("this."+o);c._resetNegate();return c};this._compareValues=function(o,m,r,D,T){var I;I=U?"jQuery.jgrid.getAccessor(this,'"+m+"')":"this";if(r===undefined)r=null;var C=r,ca=T.stype===undefined?"text":T.stype;if(r!==null)switch(ca){case "int":case "integer":C=isNaN(Number(C))||C===""?"0":C;I="parseInt("+I+",10)";C="parseInt("+C+",10)";break;case "float":case "number":case "numeric":C=String(C).replace(q,"");C=isNaN(Number(C))||C===""?"0":C;I="parseFloat("+I+")";C="parseFloat("+
C+")";break;case "date":case "datetime":C=String(b.jgrid.parseDate(T.newfmt||"Y-m-d",C).getTime());I='jQuery.jgrid.parseDate("'+T.srcfmt+'",'+I+").getTime()";break;default:I=c._getStr(I);C=c._getStr('"'+c._toStr(C)+'"')}c._append(I+" "+D+" "+C);c._setCommand(o,m);c._resetNegate();return c};this.equals=function(o,m,r){return c._compareValues(c.equals,o,m,"==",r)};this.notEquals=function(o,m,r){return c._compareValues(c.equals,o,m,"!==",r)};this.isNull=function(o,m,r){return c._compareValues(c.equals,
o,null,"===",r)};this.greater=function(o,m,r){return c._compareValues(c.greater,o,m,">",r)};this.less=function(o,m,r){return c._compareValues(c.less,o,m,"<",r)};this.greaterOrEquals=function(o,m,r){return c._compareValues(c.greaterOrEquals,o,m,">=",r)};this.lessOrEquals=function(o,m,r){return c._compareValues(c.lessOrEquals,o,m,"<=",r)};this.startsWith=function(o,m){var r=m===undefined||m===null?o:m;r=l?b.trim(r.toString()).length:r.toString().length;if(U)c._append(c._getStr("jQuery.jgrid.getAccessor(this,'"+
o+"')")+".substr(0,"+r+") == "+c._getStr('"'+c._toStr(m)+'"'));else{r=l?b.trim(m.toString()).length:m.toString().length;c._append(c._getStr("this")+".substr(0,"+r+") == "+c._getStr('"'+c._toStr(o)+'"'))}c._setCommand(c.startsWith,o);c._resetNegate();return c};this.endsWith=function(o,m){var r=m===undefined||m===null?o:m;r=l?b.trim(r.toString()).length:r.toString().length;U?c._append(c._getStr("jQuery.jgrid.getAccessor(this,'"+o+"')")+".substr("+c._getStr("jQuery.jgrid.getAccessor(this,'"+o+"')")+
".length-"+r+","+r+') == "'+c._toStr(m)+'"'):c._append(c._getStr("this")+".substr("+c._getStr("this")+'.length-"'+c._toStr(o)+'".length,"'+c._toStr(o)+'".length) == "'+c._toStr(o)+'"');c._setCommand(c.endsWith,o);c._resetNegate();return c};this.contains=function(o,m){U?c._append(c._getStr("jQuery.jgrid.getAccessor(this,'"+o+"')")+'.indexOf("'+c._toStr(m)+'",0) > -1'):c._append(c._getStr("this")+'.indexOf("'+c._toStr(o)+'",0) > -1');c._setCommand(c.contains,o);c._resetNegate();return c};this.groupBy=
function(o,m,r,D){if(!c._hasData())return null;return c._getGroup(g,o,m,r,D)};this.orderBy=function(o,m,r,D){m=m===undefined||m===null?"a":b.trim(m.toString().toLowerCase());if(r===null||r===undefined)r="text";if(D===null||D===undefined)D="Y-m-d";if(m=="desc"||m=="descending")m="d";if(m=="asc"||m=="ascending")m="a";P.push({by:o,dir:m,type:r,datefmt:D});return c};return c}(f,null)},extend:function(f){b.extend(b.fn.jqGrid,f);this.no_legacy_api||b.fn.extend(f)}});b.fn.jqGrid=function(f){if(typeof f==
"string"){var j=b.jgrid.getAccessor(b.fn.jqGrid,f);if(!j)throw"jqGrid - No such method: "+f;var h=b.makeArray(arguments).slice(1);return j.apply(this,h)}return this.each(function(){if(!this.grid){var c=b.extend(true,{url:"",height:150,page:1,rowNum:20,rowTotal:null,records:0,pager:"",pgbuttons:true,pginput:true,colModel:[],rowList:[],colNames:[],sortorder:"asc",sortname:"",datatype:"xml",mtype:"GET",altRows:false,selarrrow:[],savedRow:[],shrinkToFit:true,xmlReader:{},jsonReader:{},subGrid:false,subGridModel:[],
reccount:0,lastpage:0,lastsort:0,selrow:null,beforeSelectRow:null,onSelectRow:null,onSortCol:null,ondblClickRow:null,onRightClickRow:null,onPaging:null,onSelectAll:null,loadComplete:null,gridComplete:null,loadError:null,loadBeforeSend:null,afterInsertRow:null,beforeRequest:null,onHeaderClick:null,viewrecords:false,loadonce:false,multiselect:false,multikey:false,editurl:null,search:false,caption:"",hidegrid:true,hiddengrid:false,postData:{},userData:{},treeGrid:false,treeGridModel:"nested",treeReader:{},
treeANode:-1,ExpandColumn:null,tree_root_level:0,prmNames:{page:"page",rows:"rows",sort:"sidx",order:"sord",search:"_search",nd:"nd",id:"id",oper:"oper",editoper:"edit",addoper:"add",deloper:"del",subgridid:"id",npage:null,totalrows:"totalrows"},forceFit:false,gridstate:"visible",cellEdit:false,cellsubmit:"remote",nv:0,loadui:"enable",toolbar:[false,""],scroll:false,multiboxonly:false,deselectAfterSort:true,scrollrows:false,autowidth:false,scrollOffset:18,cellLayout:5,subGridWidth:20,multiselectWidth:20,
gridview:false,rownumWidth:25,rownumbers:false,pagerpos:"center",recordpos:"right",footerrow:false,userDataOnFooter:false,hoverrows:true,altclass:"ui-priority-secondary",viewsortcols:[false,"vertical",true],resizeclass:"",autoencode:false,remapColumns:[],ajaxGridOptions:{},direction:"ltr",toppager:false,headertitles:false,scrollTimeout:40,data:[],_index:{},grouping:false,groupingView:{groupField:[],groupOrder:[],groupText:[],groupColumnShow:[],groupSummary:[],showSummaryOnHide:false,sortitems:[],
sortnames:[],groupDataSorted:false,summary:[],summaryval:[],plusicon:"ui-icon-circlesmall-plus",minusicon:"ui-icon-circlesmall-minus"},ignoreCase:false,cmTemplate:{}},b.jgrid.defaults,f||{}),g={headers:[],cols:[],footers:[],dragStart:function(e,d,i){this.resizing={idx:e,startX:d.clientX,sOL:i[0]};this.hDiv.style.cursor="col-resize";this.curGbox=b("#rs_m"+b.jgrid.jqID(c.id),"#gbox_"+b.jgrid.jqID(c.id));this.curGbox.css({display:"block",left:i[0],top:i[1],height:i[2]});b.isFunction(c.resizeStart)&&
c.resizeStart.call(this,d,e);document.onselectstart=function(){return false}},dragMove:function(e){if(this.resizing){var d=e.clientX-this.resizing.startX;e=this.headers[this.resizing.idx];var i=c.direction==="ltr"?e.width+d:e.width-d,n;if(i>33){this.curGbox.css({left:this.resizing.sOL+d});if(c.forceFit===true){n=this.headers[this.resizing.idx+c.nv];d=c.direction==="ltr"?n.width-d:n.width+d;if(d>33){e.newWidth=i;n.newWidth=d}}else{this.newWidth=c.direction==="ltr"?c.tblwidth+d:c.tblwidth-d;e.newWidth=
i}}}},dragEnd:function(){this.hDiv.style.cursor="default";if(this.resizing){var e=this.resizing.idx,d=this.headers[e].newWidth||this.headers[e].width;d=parseInt(d,10);this.resizing=false;b("#rs_m"+b.jgrid.jqID(c.id)).css("display","none");c.colModel[e].width=d;this.headers[e].width=d;this.headers[e].el.style.width=d+"px";this.cols[e].style.width=d+"px";if(this.footers.length>0)this.footers[e].style.width=d+"px";if(c.forceFit===true){d=this.headers[e+c.nv].newWidth||this.headers[e+c.nv].width;this.headers[e+
c.nv].width=d;this.headers[e+c.nv].el.style.width=d+"px";this.cols[e+c.nv].style.width=d+"px";if(this.footers.length>0)this.footers[e+c.nv].style.width=d+"px";c.colModel[e+c.nv].width=d}else{c.tblwidth=this.newWidth||c.tblwidth;b("table:first",this.bDiv).css("width",c.tblwidth+"px");b("table:first",this.hDiv).css("width",c.tblwidth+"px");this.hDiv.scrollLeft=this.bDiv.scrollLeft;if(c.footerrow){b("table:first",this.sDiv).css("width",c.tblwidth+"px");this.sDiv.scrollLeft=this.bDiv.scrollLeft}}b.isFunction(c.resizeStop)&&
c.resizeStop.call(this,d,e)}this.curGbox=null;document.onselectstart=function(){return true}},populateVisible:function(){g.timer&&clearTimeout(g.timer);g.timer=null;var e=b(g.bDiv).height();if(e){var d=b("table:first",g.bDiv),i,n;if(d[0].rows.length)try{n=(i=d[0].rows[1])?b(i).outerHeight()||g.prevRowHeight:g.prevRowHeight}catch(p){n=g.prevRowHeight}if(n){g.prevRowHeight=n;var A=c.rowNum;i=g.scrollTop=g.bDiv.scrollTop;var s=Math.round(d.position().top)-i,E=s+d.height();n*=A;var u,z,w;if(E<e&&s<=0&&
(c.lastpage===undefined||parseInt((E+i+n-1)/n,10)<=c.lastpage)){z=parseInt((e-E+n-1)/n,10);if(E>=0||z<2||c.scroll===true){u=Math.round((E+i)/n)+1;s=-1}else s=1}if(s>0){u=parseInt(i/n,10)+1;z=parseInt((i+e)/n,10)+2-u;w=true}if(z)if(!(c.lastpage&&u>c.lastpage||c.lastpage==1||u===c.page&&u===c.lastpage))if(g.hDiv.loading)g.timer=setTimeout(g.populateVisible,c.scrollTimeout);else{c.page=u;if(w){g.selectionPreserver(d[0]);g.emptyRows(g.bDiv,false,false)}g.populate(z)}}}},scrollGrid:function(e){if(c.scroll){var d=
g.bDiv.scrollTop;if(g.scrollTop===undefined)g.scrollTop=0;if(d!=g.scrollTop){g.scrollTop=d;g.timer&&clearTimeout(g.timer);g.timer=setTimeout(g.populateVisible,c.scrollTimeout)}}g.hDiv.scrollLeft=g.bDiv.scrollLeft;if(c.footerrow)g.sDiv.scrollLeft=g.bDiv.scrollLeft;e&&e.stopPropagation()},selectionPreserver:function(e){var d=e.p,i=d.selrow,n=d.selarrrow?b.makeArray(d.selarrrow):null,p=e.grid.bDiv.scrollLeft,A=d.gridComplete;d.gridComplete=function(){d.selrow=null;d.selarrrow=[];if(d.multiselect&&n&&
n.length>0)for(var s=0;s<n.length;s++)n[s]!=i&&b(e).jqGrid("setSelection",n[s],false);i&&b(e).jqGrid("setSelection",i,false);e.grid.bDiv.scrollLeft=p;d.gridComplete=A;d.gridComplete&&A()}}};if(this.tagName.toUpperCase()!="TABLE")alert("Element is not a table");else{b(this).empty().attr("tabindex","1");this.p=c;var k,l,a;if(this.p.colNames.length===0)for(k=0;k<this.p.colModel.length;k++)this.p.colNames[k]=this.p.colModel[k].label||this.p.colModel[k].name;if(this.p.colNames.length!==this.p.colModel.length)alert(b.jgrid.errors.model);
else{var q=b("<div class='ui-jqgrid-view'></div>"),x,y=b.browser.msie?true:false,H=b.browser.webkit||b.browser.safari?true:false;a=this;a.p.direction=b.trim(a.p.direction.toLowerCase());if(b.inArray(a.p.direction,["ltr","rtl"])==-1)a.p.direction="ltr";l=a.p.direction;b(q).insertBefore(this);b(this).appendTo(q).removeClass("scroll");var L=b("<div class='ui-jqgrid ui-widget ui-widget-content ui-corner-all'></div>");b(L).insertBefore(q).attr({id:"gbox_"+this.id,dir:l});b(q).appendTo(L).attr("id","gview_"+
this.id);x=y&&b.browser.version<=6?'<iframe style="display:block;position:absolute;z-index:-1;filter:Alpha(Opacity=\'0\');" src="javascript:false;"></iframe>':"";b("<div class='ui-widget-overlay jqgrid-overlay' id='lui_"+this.id+"'></div>").append(x).insertBefore(q);b("<div class='loading ui-state-default ui-state-active' id='load_"+this.id+"'>"+this.p.loadtext+"</div>").insertBefore(q);b(this).attr({cellspacing:"0",cellpadding:"0",border:"0",role:"grid","aria-multiselectable":!!this.p.multiselect,
"aria-labelledby":"gbox_"+this.id});var M=function(e,d){e=parseInt(e,10);return isNaN(e)?d?d:0:e},P=function(e,d,i,n,p,A){var s=a.p.colModel[e],E=s.align,u='style="',z=s.classes,w=s.name,t=[];if(E)u+="text-align:"+E+";";if(s.hidden===true)u+="display:none;";if(d===0)u+="width: "+g.headers[e].width+"px;";else if(s.cellattr&&b.isFunction(s.cellattr))if((e=s.cellattr.call(a,p,i,n,s,A))&&typeof e==="string"){e=e.replace(/style/i,"style").replace(/title/i,"title");if(e.indexOf("title")>-1)s.title=false;
if(e.indexOf("class")>-1)z=undefined;t=e.split("style");if(t.length===2){t[1]=b.trim(t[1].replace("=",""));if(t[1].indexOf("'")===0||t[1].indexOf('"')===0)t[1]=t[1].substring(1);u+=t[1].replace(/'/gi,'"')}else u+='"'}if(!t.length){t[0]="";u+='"'}u+=(z!==undefined?' class="'+z+'"':"")+(s.title&&i?' title="'+b.jgrid.stripHtml(i)+'"':"");u+=' aria-describedby="'+a.p.id+"_"+w+'"';return u+t[0]},U=function(e){return e===undefined||e===null||e===""?"&#160;":a.p.autoencode?b.jgrid.htmlEncode(e):e+""},o=
function(e,d,i,n,p){var A=a.p.colModel[i];if(typeof A.formatter!=="undefined"){e={rowId:e,colModel:A,gid:a.p.id,pos:i};d=b.isFunction(A.formatter)?A.formatter.call(a,d,e,n,p):b.fmatter?b.fn.fmatter(A.formatter,d,e,n,p):U(d)}else d=U(d);return d},m=function(e,d,i,n,p){d=o(e,d,i,p,"add");return'<td role="gridcell" '+P(i,n,d,p,e,true)+">"+d+"</td>"},r=function(e,d,i){var n='<input role="checkbox" type="checkbox" id="jqg_'+a.p.id+"_"+e+'" class="cbox" name="jqg_'+a.p.id+"_"+e+'"/>';return'<td role="gridcell" '+
P(d,i,"",null,e,true)+">"+n+"</td>"},D=function(e,d,i,n){i=(parseInt(i,10)-1)*parseInt(n,10)+1+d;return'<td role="gridcell" class="ui-state-default jqgrid-rownum" '+P(e,d,i,null,d,true)+">"+i+"</td>"},T=function(e){var d,i=[],n=0,p;for(p=0;p<a.p.colModel.length;p++){d=a.p.colModel[p];if(d.name!=="cb"&&d.name!=="subgrid"&&d.name!=="rn"){i[n]=e=="local"?d.name:e=="xml"?d.xmlmap||d.name:d.jsonmap||d.name;n++}}return i},I=function(e){var d=a.p.remapColumns;if(!d||!d.length)d=b.map(a.p.colModel,function(i,
n){return n});if(e)d=b.map(d,function(i){return i<e?null:i-e});return d},C=function(e,d,i){if(a.p.deepempty)b("#"+b.jgrid.jqID(a.p.id)+" tbody:first tr:gt(0)").remove();else{var n=b("#"+b.jgrid.jqID(a.p.id)+" tbody:first tr:first")[0];b("#"+b.jgrid.jqID(a.p.id)+" tbody:first").empty().append(n)}if(d&&a.p.scroll){b(">div:first",e).css({height:"auto"}).children("div:first").css({height:0,display:"none"});e.scrollTop=0}if(i===true)if(a.p.treeGrid===true){a.p.data=[];a.p._index={}}},ca=function(){var e=
a.p.data.length,d,i,n;d=a.p.rownumbers===true?1:0;i=a.p.multiselect===true?1:0;n=a.p.subGrid===true?1:0;d=a.p.keyIndex===false||a.p.loadonce===true?a.p.localReader.id:a.p.colModel[a.p.keyIndex+i+n+d].name;for(i=0;i<e;i++){n=b.jgrid.getAccessor(a.p.data[i],d);a.p._index[n]=i}},V=function(e,d,i,n,p){var A=new Date,s=a.p.datatype!="local"&&a.p.loadonce||a.p.datatype=="xmlstring",E=a.p.datatype=="local"?"local":"xml";if(s){a.p.data=[];a.p._index={};a.p.localReader.id="_id_"}a.p.reccount=0;if(b.isXMLDoc(e)){if(a.p.treeANode===
-1&&!a.p.scroll){C(d,false,true);i=1}else i=i>1?i:1;var u,z,w=0,t,F=0,S=0,N=0,K,O=[],Y,J={},v,B,G=[],ia=a.p.altRows===true?" "+a.p.altclass:"";a.p.xmlReader.repeatitems||(O=T(E));K=a.p.keyIndex===false?a.p.xmlReader.id:a.p.keyIndex;if(O.length>0&&!isNaN(K)){if(a.p.remapColumns&&a.p.remapColumns.length)K=b.inArray(K,a.p.remapColumns);K=O[K]}E=(K+"").indexOf("[")===-1?O.length?function(ga,aa){return b(K,ga).text()||aa}:function(ga,aa){return b(a.p.xmlReader.cell,ga).eq(K).text()||aa}:function(ga,aa){return ga.getAttribute(K.replace(/[\[\]]/g,
""))||aa};a.p.userData={};b(a.p.xmlReader.page,e).each(function(){a.p.page=this.textContent||this.text||0});b(a.p.xmlReader.total,e).each(function(){a.p.lastpage=this.textContent||this.text;if(a.p.lastpage===undefined)a.p.lastpage=1});b(a.p.xmlReader.records,e).each(function(){a.p.records=this.textContent||this.text||0});b(a.p.xmlReader.userdata,e).each(function(){a.p.userData[this.getAttribute("name")]=this.textContent||this.text});(e=b(a.p.xmlReader.root+" "+a.p.xmlReader.row,e))||(e=[]);var ba=
e.length,W=0,Z={},ha;if(e&&ba){ha=parseInt(a.p.rowNum,10);var pa=a.p.scroll?b.jgrid.randId():1;if(p)ha*=p+1;p=b.isFunction(a.p.afterInsertRow);var qa="";if(a.p.grouping&&a.p.groupingView.groupCollapse===true)qa=' style="display:none;"';for(;W<ba;){v=e[W];B=E(v,pa+W);u=i===0?0:i+1;u=(u+W)%2==1?ia:"";G.push("<tr"+qa+' id="'+B+'" tabindex="-1" role="row" class ="ui-widget-content jqgrow ui-row-'+a.p.direction+""+u+'">');if(a.p.rownumbers===true){G.push(D(0,W,a.p.page,a.p.rowNum));N=1}if(a.p.multiselect===
true){G.push(r(B,N,W));F=1}if(a.p.subGrid===true){G.push(b(a).jqGrid("addSubGridCell",F+N,W+i));S=1}if(a.p.xmlReader.repeatitems){Y||(Y=I(F+S+N));var Ba=b(a.p.xmlReader.cell,v);b.each(Y,function(ga){var aa=Ba[this];if(!aa)return false;t=aa.textContent||aa.text;J[a.p.colModel[ga+F+S+N].name]=t;G.push(m(B,t,ga+F+S+N,W+i,v))})}else for(u=0;u<O.length;u++){t=b(O[u],v).text();J[a.p.colModel[u+F+S+N].name]=t;G.push(m(B,t,u+F+S+N,W+i,v))}G.push("</tr>");if(a.p.grouping){u=a.p.groupingView.groupField.length;
for(var xa=[],ya=0;ya<u;ya++)xa.push(J[a.p.groupingView.groupField[ya]]);Z=b(a).jqGrid("groupingPrepare",G,xa,Z,J);G=[]}if(s||a.p.treeGrid===true){J._id_=B;a.p.data.push(J);a.p._index[B]=a.p.data.length-1}if(a.p.gridview===false){b("tbody:first",d).append(G.join(""));p&&a.p.afterInsertRow.call(a,B,J,v);G=[]}J={};w++;W++;if(w==ha)break}}if(a.p.gridview===true){z=a.p.treeANode>-1?a.p.treeANode:0;if(a.p.grouping){b(a).jqGrid("groupingRender",Z,a.p.colModel.length);Z=null}else a.p.treeGrid===true&&z>
0?b(a.rows[z]).after(G.join("")):b("tbody:first",d).append(G.join(""))}if(a.p.subGrid===true)try{b(a).jqGrid("addSubGrid",F+N)}catch(Ha){}a.p.totaltime=new Date-A;if(w>0)if(a.p.records===0)a.p.records=ba;G=null;if(a.p.treeGrid===true)try{b(a).jqGrid("setTreeNode",z+1,w+z+1)}catch(Ia){}if(!a.p.treeGrid&&!a.p.scroll)a.grid.bDiv.scrollTop=0;a.p.reccount=w;a.p.treeANode=-1;a.p.userDataOnFooter&&b(a).jqGrid("footerData","set",a.p.userData,true);if(s){a.p.records=ba;a.p.lastpage=Math.ceil(ba/ha)}n||a.updatepager(false,
true);if(s)for(;w<ba;){v=e[w];B=E(v,w);if(a.p.xmlReader.repeatitems){Y||(Y=I(F+S+N));var Ea=b(a.p.xmlReader.cell,v);b.each(Y,function(ga){var aa=Ea[this];if(!aa)return false;t=aa.textContent||aa.text;J[a.p.colModel[ga+F+S+N].name]=t})}else for(u=0;u<O.length;u++){t=b(O[u],v).text();J[a.p.colModel[u+F+S+N].name]=t}J._id_=B;a.p.data.push(J);a.p._index[B]=a.p.data.length-1;J={};w++}}},fa=function(e,d,i,n,p){var A=new Date;if(e){if(a.p.treeANode===-1&&!a.p.scroll){C(d,false,true);i=1}else i=i>1?i:1;var s,
E=a.p.datatype!="local"&&a.p.loadonce||a.p.datatype=="jsonstring";if(E){a.p.data=[];a.p._index={};a.p.localReader.id="_id_"}a.p.reccount=0;if(a.p.datatype=="local"){d=a.p.localReader;s="local"}else{d=a.p.jsonReader;s="json"}var u=0,z,w,t=[],F,S=0,N=0,K=0,O,Y,J={},v,B,G=[],ia=a.p.altRows===true?" "+a.p.altclass:"";a.p.page=b.jgrid.getAccessor(e,d.page)||0;O=b.jgrid.getAccessor(e,d.total);a.p.lastpage=O===undefined?1:O;a.p.records=b.jgrid.getAccessor(e,d.records)||0;a.p.userData=b.jgrid.getAccessor(e,
d.userdata)||{};d.repeatitems||(F=t=T(s));s=a.p.keyIndex===false?d.id:a.p.keyIndex;if(t.length>0&&!isNaN(s)){if(a.p.remapColumns&&a.p.remapColumns.length)s=b.inArray(s,a.p.remapColumns);s=t[s]}(Y=b.jgrid.getAccessor(e,d.root))||(Y=[]);O=Y.length;e=0;var ba=parseInt(a.p.rowNum,10),W=a.p.scroll?b.jgrid.randId():1;if(p)ba*=p+1;var Z=b.isFunction(a.p.afterInsertRow),ha={},pa="";if(a.p.grouping&&a.p.groupingView.groupCollapse===true)pa=' style="display:none;"';for(;e<O;){p=Y[e];B=b.jgrid.getAccessor(p,
s);if(B===undefined){B=W+e;if(t.length===0)if(d.cell)B=b.jgrid.getAccessor(p,d.cell)[s]||B}z=i===1?0:i;z=(z+e)%2==1?ia:"";G.push("<tr"+pa+' id="'+B+'" tabindex="-1" role="row" class= "ui-widget-content jqgrow ui-row-'+a.p.direction+""+z+'">');if(a.p.rownumbers===true){G.push(D(0,e,a.p.page,a.p.rowNum));K=1}if(a.p.multiselect){G.push(r(B,K,e));S=1}if(a.p.subGrid){G.push(b(a).jqGrid("addSubGridCell",S+K,e+i));N=1}if(d.repeatitems){if(d.cell)p=b.jgrid.getAccessor(p,d.cell);F||(F=I(S+N+K))}for(w=0;w<
F.length;w++){z=b.jgrid.getAccessor(p,F[w]);G.push(m(B,z,w+S+N+K,e+i,p));J[a.p.colModel[w+S+N+K].name]=z}G.push("</tr>");if(a.p.grouping){z=a.p.groupingView.groupField.length;w=[];for(var qa=0;qa<z;qa++)w.push(J[a.p.groupingView.groupField[qa]]);ha=b(a).jqGrid("groupingPrepare",G,w,ha,J);G=[]}if(E||a.p.treeGrid===true){J._id_=B;a.p.data.push(J);a.p._index[B]=a.p.data.length-1}if(a.p.gridview===false){b("#"+b.jgrid.jqID(a.p.id)+" tbody:first").append(G.join(""));Z&&a.p.afterInsertRow.call(a,B,J,p);
G=[]}J={};u++;e++;if(u==ba)break}if(a.p.gridview===true){v=a.p.treeANode>-1?a.p.treeANode:0;if(a.p.grouping)b(a).jqGrid("groupingRender",ha,a.p.colModel.length);else a.p.treeGrid===true&&v>0?b(a.rows[v]).after(G.join("")):b("#"+b.jgrid.jqID(a.p.id)+" tbody:first").append(G.join(""))}if(a.p.subGrid===true)try{b(a).jqGrid("addSubGrid",S+K)}catch(Ba){}a.p.totaltime=new Date-A;if(u>0)if(a.p.records===0)a.p.records=O;if(a.p.treeGrid===true)try{b(a).jqGrid("setTreeNode",v+1,u+v+1)}catch(xa){}if(!a.p.treeGrid&&
!a.p.scroll)a.grid.bDiv.scrollTop=0;a.p.reccount=u;a.p.treeANode=-1;a.p.userDataOnFooter&&b(a).jqGrid("footerData","set",a.p.userData,true);if(E){a.p.records=O;a.p.lastpage=Math.ceil(O/ba)}n||a.updatepager(false,true);if(E)for(;u<O&&Y[u];){p=Y[u];B=b.jgrid.getAccessor(p,s);if(B===undefined){B=W+u;if(t.length===0)if(d.cell)B=b.jgrid.getAccessor(p,d.cell)[s]||B}if(p){if(d.repeatitems){if(d.cell)p=b.jgrid.getAccessor(p,d.cell);F||(F=I(S+N+K))}for(w=0;w<F.length;w++){z=b.jgrid.getAccessor(p,F[w]);J[a.p.colModel[w+
S+N+K].name]=z}J._id_=B;a.p.data.push(J);a.p._index[B]=a.p.data.length-1;J={}}u++}}},oa=function(){function e(v){var B=0,G,ia,ba,W,Z;if(v.groups!==undefined){(ia=v.groups.length&&v.groupOp.toString().toUpperCase()==="OR")&&t.orBegin();for(G=0;G<v.groups.length;G++){B>0&&ia&&t.or();try{e(v.groups[G])}catch(ha){alert(ha)}B++}ia&&t.orEnd()}if(v.rules!==undefined){if(B>0){ia=t.select();t=b.jgrid.from(ia)}try{(ba=v.rules.length&&v.groupOp.toString().toUpperCase()==="OR")&&t.orBegin();for(G=0;G<v.rules.length;G++){Z=
v.rules[G];W=v.groupOp.toString().toUpperCase();if(w[Z.op]&&Z.field){if(B>0&&W&&W==="OR")t=t.or();t=w[Z.op](t,W)(Z.field,Z.data,n[Z.field])}B++}ba&&t.orEnd()}catch(pa){alert(pa)}}}var d,i=false,n={},p=[],A=[],s,E,u;if(b.isArray(a.p.data)){var z=a.p.grouping?a.p.groupingView:false;b.each(a.p.colModel,function(){E=this.sorttype||"text";if(E=="date"||E=="datetime"){if(this.formatter&&typeof this.formatter==="string"&&this.formatter=="date"){s=this.formatoptions&&this.formatoptions.srcformat?this.formatoptions.srcformat:
b.jgrid.formatter.date.srcformat;u=this.formatoptions&&this.formatoptions.newformat?this.formatoptions.newformat:b.jgrid.formatter.date.newformat}else s=u=this.datefmt||"Y-m-d";n[this.name]={stype:E,srcfmt:s,newfmt:u}}else n[this.name]={stype:E,srcfmt:"",newfmt:""};if(a.p.grouping&&this.name==z.groupField[0]){var v=this.name;if(typeof this.index!="undefined")v=this.index;p[0]=n[v];A.push(v)}if(!i&&(this.index==a.p.sortname||this.name==a.p.sortname)){d=this.name;i=true}});if(a.p.treeGrid)b(a).jqGrid("SortTree",
d,a.p.sortorder,n[d].stype,n[d].srcfmt);else{var w={eq:function(v){return v.equals},ne:function(v){return v.notEquals},lt:function(v){return v.less},le:function(v){return v.lessOrEquals},gt:function(v){return v.greater},ge:function(v){return v.greaterOrEquals},cn:function(v){return v.contains},nc:function(v,B){return B==="OR"?v.orNot().contains:v.andNot().contains},bw:function(v){return v.startsWith},bn:function(v,B){return B==="OR"?v.orNot().startsWith:v.andNot().startsWith},en:function(v,B){return B===
"OR"?v.orNot().endsWith:v.andNot().endsWith},ew:function(v){return v.endsWith},ni:function(v,B){return B==="OR"?v.orNot().equals:v.andNot().equals},"in":function(v){return v.equals},nu:function(v){return v.isNull},nn:function(v,B){return B==="OR"?v.orNot().isNull:v.andNot().isNull}},t=b.jgrid.from(a.p.data);if(a.p.ignoreCase)t=t.ignoreCase();if(a.p.search===true){var F=a.p.postData.filters;if(F){if(typeof F=="string")F=b.jgrid.parse(F);e(F)}else try{t=w[a.p.postData.searchOper](t)(a.p.postData.searchField,
a.p.postData.searchString,n[a.p.postData.searchField])}catch(S){}}if(a.p.grouping){t.orderBy(A,z.groupOrder[0],p[0].stype,p[0].srcfmt);z.groupDataSorted=true}if(d&&a.p.sortorder&&i)a.p.sortorder.toUpperCase()=="DESC"?t.orderBy(a.p.sortname,"d",n[d].stype,n[d].srcfmt):t.orderBy(a.p.sortname,"a",n[d].stype,n[d].srcfmt);F=t.select();var N=parseInt(a.p.rowNum,10),K=F.length,O=parseInt(a.p.page,10),Y=Math.ceil(K/N),J={};F=F.slice((O-1)*N,O*N);n=t=null;J[a.p.localReader.total]=Y;J[a.p.localReader.page]=
O;J[a.p.localReader.records]=K;J[a.p.localReader.root]=F;F=null;return J}}},R=function(){a.grid.hDiv.loading=true;if(!a.p.hiddengrid)switch(a.p.loadui){case "enable":b("#load_"+b.jgrid.jqID(a.p.id)).show();break;case "block":b("#lui_"+b.jgrid.jqID(a.p.id)).show();b("#load_"+b.jgrid.jqID(a.p.id)).show()}},$=function(){a.grid.hDiv.loading=false;switch(a.p.loadui){case "enable":b("#load_"+b.jgrid.jqID(a.p.id)).hide();break;case "block":b("#lui_"+b.jgrid.jqID(a.p.id)).hide();b("#load_"+b.jgrid.jqID(a.p.id)).hide()}},
ja=function(e){if(!a.grid.hDiv.loading){var d=a.p.scroll&&e===false,i={},n,p=a.p.prmNames;if(a.p.page<=0)a.p.page=1;if(p.search!==null)i[p.search]=a.p.search;if(p.nd!==null)i[p.nd]=(new Date).getTime();if(p.rows!==null)i[p.rows]=a.p.rowNum;if(p.page!==null)i[p.page]=a.p.page;if(p.sort!==null)i[p.sort]=a.p.sortname;if(p.order!==null)i[p.order]=a.p.sortorder;if(a.p.rowTotal!==null&&p.totalrows!==null)i[p.totalrows]=a.p.rowTotal;var A=a.p.loadComplete,s=b.isFunction(A);s||(A=null);var E=0;e=e||1;if(e>
1)if(p.npage!==null){i[p.npage]=e;E=e-1;e=1}else A=function(z){a.p.page++;a.grid.hDiv.loading=false;s&&a.p.loadComplete.call(a,z);ja(e-1)};else p.npage!==null&&delete a.p.postData[p.npage];if(a.p.grouping){b(a).jqGrid("groupingSetup");if(a.p.groupingView.groupDataSorted===true)i[p.sort]=a.p.groupingView.groupField[0]+" "+a.p.groupingView.groupOrder[0]+", "+i[p.sort]}b.extend(a.p.postData,i);var u=!a.p.scroll?1:a.rows.length-1;if(b.isFunction(a.p.datatype))a.p.datatype.call(a,a.p.postData,"load_"+
a.p.id);else{b.isFunction(a.p.beforeRequest)&&a.p.beforeRequest.call(a);n=a.p.datatype.toLowerCase();switch(n){case "json":case "jsonp":case "xml":case "script":b.ajax(b.extend({url:a.p.url,type:a.p.mtype,dataType:n,data:b.isFunction(a.p.serializeGridData)?a.p.serializeGridData.call(a,a.p.postData):a.p.postData,success:function(z){n==="xml"?V(z,a.grid.bDiv,u,e>1,E):fa(z,a.grid.bDiv,u,e>1,E);A&&A.call(a,z);d&&a.grid.populateVisible();if(a.p.loadonce||a.p.treeGrid)a.p.datatype="local";$()},error:function(z,
w,t){b.isFunction(a.p.loadError)&&a.p.loadError.call(a,z,w,t);$()},beforeSend:function(z){R();b.isFunction(a.p.loadBeforeSend)&&a.p.loadBeforeSend.call(a,z)}},b.jgrid.ajaxOptions,a.p.ajaxGridOptions));break;case "xmlstring":R();i=b.jgrid.stringToDoc(a.p.datastr);V(i,a.grid.bDiv);s&&a.p.loadComplete.call(a,i);a.p.datatype="local";a.p.datastr=null;$();break;case "jsonstring":R();i=typeof a.p.datastr=="string"?b.jgrid.parse(a.p.datastr):a.p.datastr;fa(i,a.grid.bDiv);s&&a.p.loadComplete.call(a,i);a.p.datatype=
"local";a.p.datastr=null;$();break;case "local":case "clientside":R();a.p.datatype="local";i=oa();fa(i,a.grid.bDiv,u,e>1,E);A&&A.call(a,i);d&&a.grid.populateVisible();$()}}}};x=function(e,d){var i="",n="<table cellspacing='0' cellpadding='0' border='0' style='table-layout:auto;' class='ui-pg-table'><tbody><tr>",p="",A,s,E,u,z=function(w){var t;if(b.isFunction(a.p.onPaging))t=a.p.onPaging.call(a,w);a.p.selrow=null;if(a.p.multiselect){a.p.selarrrow=[];b("#cb_"+b.jgrid.jqID(a.p.id),a.grid.hDiv).attr("checked",
false)}a.p.savedRow=[];if(t=="stop")return false;return true};e=e.substr(1);d+="_"+e;A="pg_"+e;s=e+"_left";E=e+"_center";u=e+"_right";b("#"+b.jgrid.jqID(e)).append("<div id='"+A+"' class='ui-pager-control' role='group'><table cellspacing='0' cellpadding='0' border='0' class='ui-pg-table' style='width:100%;table-layout:fixed;height:100%;' role='row'><tbody><tr><td id='"+s+"' align='left'></td><td id='"+E+"' align='center' style='white-space:pre;'></td><td id='"+u+"' align='right'></td></tr></tbody></table></div>").attr("dir",
"ltr");if(a.p.rowList.length>0){p="<td dir='"+l+"'>";p+="<select class='ui-pg-selbox' role='listbox'>";for(s=0;s<a.p.rowList.length;s++)p+='<option role="option" value="'+a.p.rowList[s]+'"'+(a.p.rowNum==a.p.rowList[s]?' selected="selected"':"")+">"+a.p.rowList[s]+"</option>";p+="</select></td>"}if(l=="rtl")n+=p;if(a.p.pginput===true)i="<td dir='"+l+"'>"+b.jgrid.format(a.p.pgtext||"","<input class='ui-pg-input' type='text' size='2' maxlength='7' value='0' role='textbox'/>","<span id='sp_1_"+b.jgrid.jqID(e)+
"'></span>")+"</td>";if(a.p.pgbuttons===true){s=["first"+d,"prev"+d,"next"+d,"last"+d];l=="rtl"&&s.reverse();n+="<td id='"+s[0]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-first'></span></td>";n+="<td id='"+s[1]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-prev'></span></td>";n+=i!==""?"<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='ui-separator'></span></td>"+i+"<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='ui-separator'></span></td>":
"";n+="<td id='"+s[2]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-next'></span></td>";n+="<td id='"+s[3]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-end'></span></td>"}else if(i!=="")n+=i;if(l=="ltr")n+=p;n+="</tr></tbody></table>";a.p.viewrecords===true&&b("td#"+e+"_"+a.p.recordpos,"#"+A).append("<div dir='"+l+"' style='text-align:"+a.p.recordpos+"' class='ui-paging-info'></div>");b("td#"+e+"_"+a.p.pagerpos,"#"+A).append(n);p=b(".ui-jqgrid").css("font-size")||
"11px";b(document.body).append("<div id='testpg' class='ui-jqgrid ui-widget ui-widget-content' style='font-size:"+p+";visibility:hidden;' ></div>");n=b(n).clone().appendTo("#testpg").width();b("#testpg").remove();if(n>0){if(i!=="")n+=50;b("td#"+e+"_"+a.p.pagerpos,"#"+A).width(n)}a.p._nvtd=[];a.p._nvtd[0]=n?Math.floor((a.p.width-n)/2):Math.floor(a.p.width/3);a.p._nvtd[1]=0;n=null;b(".ui-pg-selbox","#"+A).bind("change",function(){a.p.page=Math.round(a.p.rowNum*(a.p.page-1)/this.value-0.5)+1;a.p.rowNum=
this.value;if(d)b(".ui-pg-selbox",a.p.pager).val(this.value);else a.p.toppager&&b(".ui-pg-selbox",a.p.toppager).val(this.value);if(!z("records"))return false;ja();return false});if(a.p.pgbuttons===true){b(".ui-pg-button","#"+A).hover(function(){if(b(this).hasClass("ui-state-disabled"))this.style.cursor="default";else{b(this).addClass("ui-state-hover");this.style.cursor="pointer"}},function(){if(!b(this).hasClass("ui-state-disabled")){b(this).removeClass("ui-state-hover");this.style.cursor="default"}});
b("#first"+b.jgrid.jqID(d)+", #prev"+b.jgrid.jqID(d)+", #next"+b.jgrid.jqID(d)+", #last"+b.jgrid.jqID(d)).click(function(){var w=M(a.p.page,1),t=M(a.p.lastpage,1),F=false,S=true,N=true,K=true,O=true;if(t===0||t===1)O=K=N=S=false;else if(t>1&&w>=1)if(w===1)N=S=false;else{if(!(w>1&&w<t))if(w===t)O=K=false}else if(t>1&&w===0){O=K=false;w=t-1}if(this.id==="first"+d&&S){a.p.page=1;F=true}if(this.id==="prev"+d&&N){a.p.page=w-1;F=true}if(this.id==="next"+d&&K){a.p.page=w+1;F=true}if(this.id==="last"+d&&
O){a.p.page=t;F=true}if(F){if(!z(this.id))return false;ja()}return false})}a.p.pginput===true&&b("input.ui-pg-input","#"+A).keypress(function(w){if((w.charCode?w.charCode:w.keyCode?w.keyCode:0)==13){a.p.page=b(this).val()>0?b(this).val():a.p.page;if(!z("user"))return false;ja();return false}return this})};var Ca=function(e,d,i,n){if(a.p.colModel[d].sortable)if(!(a.p.savedRow.length>0)){if(!i){if(a.p.lastsort==d)if(a.p.sortorder=="asc")a.p.sortorder="desc";else{if(a.p.sortorder=="desc")a.p.sortorder=
"asc"}else a.p.sortorder=a.p.colModel[d].firstsortorder||"asc";a.p.page=1}if(n)if(a.p.lastsort==d&&a.p.sortorder==n&&!i)return;else a.p.sortorder=n;i=b("thead:first",a.grid.hDiv).get(0);b("tr th:eq("+a.p.lastsort+") span.ui-grid-ico-sort",i).addClass("ui-state-disabled");b("tr th:eq("+a.p.lastsort+")",i).attr("aria-selected","false");b("tr th:eq("+d+") span.ui-icon-"+a.p.sortorder,i).removeClass("ui-state-disabled");b("tr th:eq("+d+")",i).attr("aria-selected","true");if(!a.p.viewsortcols[0])if(a.p.lastsort!=
d){b("tr th:eq("+a.p.lastsort+") span.s-ico",i).hide();b("tr th:eq("+d+") span.s-ico",i).show()}e=e.substring(5+a.p.id.length+1);a.p.sortname=a.p.colModel[d].index||e;i=a.p.sortorder;if(b.isFunction(a.p.onSortCol))if(a.p.onSortCol.call(a,e,d,i)=="stop"){a.p.lastsort=d;return}if(a.p.datatype=="local")a.p.deselectAfterSort&&b(a).jqGrid("resetSelection");else{a.p.selrow=null;a.p.multiselect&&b("#cb_"+b.jgrid.jqID(a.p.id),a.grid.hDiv).attr("checked",false);a.p.selarrrow=[];a.p.savedRow=[]}if(a.p.scroll){i=
a.grid.bDiv.scrollLeft;C(a.grid.bDiv,true,false);a.grid.hDiv.scrollLeft=i}a.p.subGrid&&a.p.datatype=="local"&&b("td.sgexpanded","#"+b.jgrid.jqID(a.p.id)).each(function(){b(this).trigger("click")});ja();a.p.lastsort=d;if(a.p.sortname!=e&&d)a.p.lastsort=d}},Fa=function(e){var d,i={},n=H?0:a.p.cellLayout;for(d=i[0]=i[1]=i[2]=0;d<=e;d++)if(a.p.colModel[d].hidden===false)i[0]+=a.p.colModel[d].width+n;if(a.p.direction=="rtl")i[0]=a.p.width-i[0];i[0]-=a.grid.bDiv.scrollLeft;if(b(a.grid.cDiv).is(":visible"))i[1]+=
b(a.grid.cDiv).height()+parseInt(b(a.grid.cDiv).css("padding-top"),10)+parseInt(b(a.grid.cDiv).css("padding-bottom"),10);if(a.p.toolbar[0]===true&&(a.p.toolbar[1]=="top"||a.p.toolbar[1]=="both"))i[1]+=b(a.grid.uDiv).height()+parseInt(b(a.grid.uDiv).css("border-top-width"),10)+parseInt(b(a.grid.uDiv).css("border-bottom-width"),10);if(a.p.toppager)i[1]+=b(a.grid.topDiv).height()+parseInt(b(a.grid.topDiv).css("border-bottom-width"),10);i[2]+=b(a.grid.bDiv).height()+b(a.grid.hDiv).height();return i};
this.p.id=this.id;if(b.inArray(a.p.multikey,["shiftKey","altKey","ctrlKey"])==-1)a.p.multikey=false;a.p.keyIndex=false;for(k=0;k<a.p.colModel.length;k++){a.p.colModel[k]=b.extend(true,{},a.p.cmTemplate,a.p.colModel[k].template||{},a.p.colModel[k]);if(a.p.keyIndex===false&&a.p.colModel[k].key===true)a.p.keyIndex=k}a.p.sortorder=a.p.sortorder.toLowerCase();if(a.p.grouping===true){a.p.scroll=false;a.p.rownumbers=false;a.p.subGrid=false;a.p.treeGrid=false;a.p.gridview=true}if(this.p.treeGrid===true){try{b(this).jqGrid("setTreeGrid")}catch(Ja){}if(a.p.datatype!=
"local")a.p.localReader={id:"_id_"}}if(this.p.subGrid)try{b(a).jqGrid("setSubGrid")}catch(Ka){}if(this.p.multiselect){this.p.colNames.unshift("<input role='checkbox' id='cb_"+this.p.id+"' class='cbox' type='checkbox'/>");this.p.colModel.unshift({name:"cb",width:H?a.p.multiselectWidth+a.p.cellLayout:a.p.multiselectWidth,sortable:false,resizable:false,hidedlg:true,search:false,align:"center",fixed:true})}if(this.p.rownumbers){this.p.colNames.unshift("");this.p.colModel.unshift({name:"rn",width:a.p.rownumWidth,
sortable:false,resizable:false,hidedlg:true,search:false,align:"center",fixed:true})}a.p.xmlReader=b.extend(true,{root:"rows",row:"row",page:"rows>page",total:"rows>total",records:"rows>records",repeatitems:true,cell:"cell",id:"[id]",userdata:"userdata",subgrid:{root:"rows",row:"row",repeatitems:true,cell:"cell"}},a.p.xmlReader);a.p.jsonReader=b.extend(true,{root:"rows",page:"page",total:"total",records:"records",repeatitems:true,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:true,
cell:"cell"}},a.p.jsonReader);a.p.localReader=b.extend(true,{root:"rows",page:"page",total:"total",records:"records",repeatitems:false,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:true,cell:"cell"}},a.p.localReader);if(a.p.scroll){a.p.pgbuttons=false;a.p.pginput=false;a.p.rowList=[]}a.p.data.length&&ca();var da="<thead><tr class='ui-jqgrid-labels' role='rowheader'>",Da,ma,sa,ra,ta,X,Q,na;ma=na="";if(a.p.shrinkToFit===true&&a.p.forceFit===true)for(k=a.p.colModel.length-
1;k>=0;k--)if(!a.p.colModel[k].hidden){a.p.colModel[k].resizable=false;break}if(a.p.viewsortcols[1]=="horizontal"){na=" ui-i-asc";ma=" ui-i-desc"}Da=y?"class='ui-th-div-ie'":"";na="<span class='s-ico' style='display:none'><span sort='asc' class='ui-grid-ico-sort ui-icon-asc"+na+" ui-state-disabled ui-icon ui-icon-triangle-1-n ui-sort-"+l+"'></span>";na+="<span sort='desc' class='ui-grid-ico-sort ui-icon-desc"+ma+" ui-state-disabled ui-icon ui-icon-triangle-1-s ui-sort-"+l+"'></span></span>";for(k=
0;k<this.p.colNames.length;k++){ma=a.p.headertitles?' title="'+b.jgrid.stripHtml(a.p.colNames[k])+'"':"";da+="<th id='"+a.p.id+"_"+a.p.colModel[k].name+"' role='columnheader' class='ui-state-default ui-th-column ui-th-"+l+"'"+ma+">";ma=a.p.colModel[k].index||a.p.colModel[k].name;da+="<div id='jqgh_"+a.p.id+"_"+a.p.colModel[k].name+"' "+Da+">"+a.p.colNames[k];a.p.colModel[k].width=a.p.colModel[k].width?parseInt(a.p.colModel[k].width,10):150;if(typeof a.p.colModel[k].title!=="boolean")a.p.colModel[k].title=
true;if(ma==a.p.sortname)a.p.lastsort=k;da+=na+"</div></th>"}da+="</tr></thead>";na=null;b(this).append(da);b("thead tr:first th",this).hover(function(){b(this).addClass("ui-state-hover")},function(){b(this).removeClass("ui-state-hover")});if(this.p.multiselect){var za=[],ua;b("#cb_"+b.jgrid.jqID(a.p.id),this).bind("click",function(){a.p.selarrrow=[];if(this.checked){b(a.rows).each(function(e){if(e>0)if(!b(this).hasClass("subgrid")&&!b(this).hasClass("jqgroup")&&!b(this).hasClass("ui-state-disabled")){b("#jqg_"+
b.jgrid.jqID(a.p.id)+"_"+b.jgrid.jqID(this.id)).attr("checked","checked");b(this).addClass("ui-state-highlight").attr("aria-selected","true");a.p.selarrrow.push(this.id);a.p.selrow=this.id}});ua=true;za=[]}else{b(a.rows).each(function(e){if(e>0)if(!b(this).hasClass("subgrid")&&!b(this).hasClass("ui-state-disabled")){b("#jqg_"+b.jgrid.jqID(a.p.id)+"_"+b.jgrid.jqID(this.id)).removeAttr("checked");b(this).removeClass("ui-state-highlight").attr("aria-selected","false");za.push(this.id)}});a.p.selrow=
null;ua=false}if(b.isFunction(a.p.onSelectAll))a.p.onSelectAll.call(a,ua?a.p.selarrrow:za,ua)})}if(a.p.autowidth===true){da=b(L).innerWidth();a.p.width=da>0?da:"nw"}(function(){var e=0,d=H?0:a.p.cellLayout,i=0,n,p=a.p.scrollOffset,A,s=false,E,u=0,z=0,w;b.each(a.p.colModel,function(){if(typeof this.hidden==="undefined")this.hidden=false;this.widthOrg=A=M(this.width,0);if(this.hidden===false){e+=A+d;if(this.fixed)u+=A+d;else i++;z++}});if(isNaN(a.p.width))a.p.width=g.width=e;else g.width=a.p.width;
a.p.tblwidth=e;if(a.p.shrinkToFit===false&&a.p.forceFit===true)a.p.forceFit=false;if(a.p.shrinkToFit===true&&i>0){E=g.width-d*i-u;if(!isNaN(a.p.height)){E-=p;s=true}e=0;b.each(a.p.colModel,function(t){if(this.hidden===false&&!this.fixed){this.width=A=Math.round(E*this.width/(a.p.tblwidth-d*i-u));e+=A;n=t}});w=0;if(s){if(g.width-u-(e+d*i)!==p)w=g.width-u-(e+d*i)-p}else if(!s&&Math.abs(g.width-u-(e+d*i))!==1)w=g.width-u-(e+d*i);a.p.colModel[n].width+=w;a.p.tblwidth=e+w+d*i+u;if(a.p.tblwidth>a.p.width){a.p.colModel[n].width-=
a.p.tblwidth-parseInt(a.p.width,10);a.p.tblwidth=a.p.width}}})();b(L).css("width",g.width+"px").append("<div class='ui-jqgrid-resize-mark' id='rs_m"+a.p.id+"'>&#160;</div>");b(q).css("width",g.width+"px");da=b("thead:first",a).get(0);var va="";if(a.p.footerrow)va+="<table role='grid' style='width:"+a.p.tblwidth+"px' class='ui-jqgrid-ftable' cellspacing='0' cellpadding='0' border='0'><tbody><tr role='row' class='ui-widget-content footrow footrow-"+l+"'>";q=b("tr:first",da);var wa="<tr class='jqgfirstrow' role='row' style='height:auto'>";
a.p.disableClick=false;b("th",q).each(function(e){sa=a.p.colModel[e].width;if(typeof a.p.colModel[e].resizable==="undefined")a.p.colModel[e].resizable=true;if(a.p.colModel[e].resizable){ra=document.createElement("span");b(ra).html("&#160;").addClass("ui-jqgrid-resize ui-jqgrid-resize-"+l);b.browser.opera||b(ra).css("cursor","col-resize");b(this).addClass(a.p.resizeclass)}else ra="";b(this).css("width",sa+"px").prepend(ra);var d="";if(a.p.colModel[e].hidden){b(this).css("display","none");d="display:none;"}wa+=
"<td role='gridcell' style='height:0px;width:"+sa+"px;"+d+"'></td>";g.headers[e]={width:sa,el:this};ta=a.p.colModel[e].sortable;if(typeof ta!=="boolean")ta=a.p.colModel[e].sortable=true;d=a.p.colModel[e].name;d=="cb"||d=="subgrid"||d=="rn"||a.p.viewsortcols[2]&&b("div",this).addClass("ui-jqgrid-sortable");if(ta)if(a.p.viewsortcols[0]){b("div span.s-ico",this).show();e==a.p.lastsort&&b("div span.ui-icon-"+a.p.sortorder,this).removeClass("ui-state-disabled")}else if(e==a.p.lastsort){b("div span.s-ico",
this).show();b("div span.ui-icon-"+a.p.sortorder,this).removeClass("ui-state-disabled")}if(a.p.footerrow)va+="<td role='gridcell' "+P(e,0,"",null,"",false)+">&#160;</td>"}).mousedown(function(e){if(b(e.target).closest("th>span.ui-jqgrid-resize").length==1){var d=b.jgrid.getCellIndex(this);if(a.p.forceFit===true){var i=a.p,n=d,p;for(p=d+1;p<a.p.colModel.length;p++)if(a.p.colModel[p].hidden!==true){n=p;break}i.nv=n-d}g.dragStart(d,e,Fa(d));return false}}).click(function(e){if(a.p.disableClick)return a.p.disableClick=
false;var d="th>div.ui-jqgrid-sortable",i,n;a.p.viewsortcols[2]||(d="th>div>span>span.ui-grid-ico-sort");e=b(e.target).closest(d);if(e.length==1){d=b.jgrid.getCellIndex(this);if(!a.p.viewsortcols[2]){i=true;n=e.attr("sort")}Ca(b("div",this)[0].id,d,i,n);return false}});if(a.p.sortable&&b.fn.sortable)try{b(a).jqGrid("sortableColumns",q)}catch(La){}if(a.p.footerrow)va+="</tr></tbody></table>";wa+="</tr>";this.appendChild(document.createElement("tbody"));b(this).addClass("ui-jqgrid-btable").append(wa);
wa=null;q=b("<table class='ui-jqgrid-htable' style='width:"+a.p.tblwidth+"px' role='grid' aria-labelledby='gbox_"+this.id+"' cellspacing='0' cellpadding='0' border='0'></table>").append(da);var ea=a.p.caption&&a.p.hiddengrid===true?true:false;k=b("<div class='ui-jqgrid-hbox"+(l=="rtl"?"-rtl":"")+"'></div>");da=null;g.hDiv=document.createElement("div");b(g.hDiv).css({width:g.width+"px"}).addClass("ui-state-default ui-jqgrid-hdiv").append(k);b(k).append(q);q=null;ea&&b(g.hDiv).hide();if(a.p.pager){if(typeof a.p.pager==
"string"){if(a.p.pager.substr(0,1)!="#")a.p.pager="#"+a.p.pager}else a.p.pager="#"+b(a.p.pager).attr("id");b(a.p.pager).css({width:g.width+"px"}).appendTo(L).addClass("ui-state-default ui-jqgrid-pager ui-corner-bottom");ea&&b(a.p.pager).hide();x(a.p.pager,"")}a.p.cellEdit===false&&a.p.hoverrows===true&&b(a).bind("mouseover",function(e){Q=b(e.target).closest("tr.jqgrow");b(Q).attr("class")!=="subgrid"&&b(Q).addClass("ui-state-hover")}).bind("mouseout",function(e){Q=b(e.target).closest("tr.jqgrow");
b(Q).removeClass("ui-state-hover")});var ka,la;b(a).before(g.hDiv).click(function(e){X=e.target;Q=b(X,a.rows).closest("tr.jqgrow");if(b(Q).length===0||Q[0].className.indexOf("ui-state-disabled")>-1)return this;var d=b(X).hasClass("cbox"),i=true;if(b.isFunction(a.p.beforeSelectRow))i=a.p.beforeSelectRow.call(a,Q[0].id,e);if(X.tagName=="A"||(X.tagName=="INPUT"||X.tagName=="TEXTAREA"||X.tagName=="OPTION"||X.tagName=="SELECT")&&!d)return this;if(i===true){if(a.p.cellEdit===true)if(a.p.multiselect&&d)b(a).jqGrid("setSelection",
Q[0].id,true);else{ka=Q[0].rowIndex;la=b.jgrid.getCellIndex(X);try{b(a).jqGrid("editCell",ka,la,true)}catch(n){}}else if(a.p.multikey)if(e[a.p.multikey])b(a).jqGrid("setSelection",Q[0].id,true);else{if(a.p.multiselect&&d){d=b("#jqg_"+b.jgrid.jqID(a.p.id)+"_"+Q[0].id).attr("checked");b("#jqg_"+b.jgrid.jqID(a.p.id)+"_"+Q[0].id).attr("checked",!d)}}else{if(a.p.multiselect&&a.p.multiboxonly)if(!d){b(a.p.selarrrow).each(function(p,A){var s=a.rows.namedItem(A);b(s).removeClass("ui-state-highlight");b("#jqg_"+
b.jgrid.jqID(a.p.id)+"_"+b.jgrid.jqID(A)).attr("checked",false)});a.p.selarrrow=[];b("#cb_"+b.jgrid.jqID(a.p.id),a.grid.hDiv).attr("checked",false)}b(a).jqGrid("setSelection",Q[0].id,true)}if(b.isFunction(a.p.onCellSelect)){ka=Q[0].id;la=b.jgrid.getCellIndex(X);a.p.onCellSelect.call(a,ka,la,b(X).html(),e)}}return this}).bind("reloadGrid",function(e,d){if(a.p.treeGrid===true)a.p.datatype=a.p.treedatatype;d&&d.current&&a.grid.selectionPreserver(a);if(a.p.datatype=="local"){b(a).jqGrid("resetSelection");
a.p.data.length&&ca()}else if(!a.p.treeGrid){a.p.selrow=null;if(a.p.multiselect){a.p.selarrrow=[];b("#cb_"+b.jgrid.jqID(a.p.id),a.grid.hDiv).attr("checked",false)}a.p.savedRow=[]}a.p.scroll&&C(a.grid.bDiv,true,false);if(d&&d.page){var i=d.page;if(i>a.p.lastpage)i=a.p.lastpage;if(i<1)i=1;a.p.page=i;a.grid.bDiv.scrollTop=a.grid.prevRowHeight?(i-1)*a.grid.prevRowHeight*a.p.rowNum:0}if(a.grid.prevRowHeight&&a.p.scroll){delete a.p.lastpage;a.grid.populateVisible()}else a.grid.populate();return false});
b.isFunction(this.p.ondblClickRow)&&b(this).dblclick(function(e){X=e.target;Q=b(X,a.rows).closest("tr.jqgrow");if(b(Q).length===0)return false;ka=Q[0].rowIndex;la=b.jgrid.getCellIndex(X);a.p.ondblClickRow.call(a,b(Q).attr("id"),ka,la,e);return false});b.isFunction(this.p.onRightClickRow)&&b(this).bind("contextmenu",function(e){X=e.target;Q=b(X,a.rows).closest("tr.jqgrow");if(b(Q).length===0)return false;a.p.multiselect||b(a).jqGrid("setSelection",Q[0].id,true);ka=Q[0].rowIndex;la=b.jgrid.getCellIndex(X);
a.p.onRightClickRow.call(a,b(Q).attr("id"),ka,la,e);return false});g.bDiv=document.createElement("div");if(y)if(String(a.p.height).toLowerCase()==="auto")a.p.height="100%";b(g.bDiv).append(b('<div style="position:relative;'+(y&&b.browser.version<8?"height:0.01%;":"")+'"></div>').append("<div></div>").append(this)).addClass("ui-jqgrid-bdiv").css({height:a.p.height+(isNaN(a.p.height)?"":"px"),width:g.width+"px"}).scroll(g.scrollGrid);b("table:first",g.bDiv).css({width:a.p.tblwidth+"px"});if(y){b("tbody",
this).size()==2&&b("tbody:gt(0)",this).remove();a.p.multikey&&b(g.bDiv).bind("selectstart",function(){return false})}else a.p.multikey&&b(g.bDiv).bind("mousedown",function(){return false});ea&&b(g.bDiv).hide();g.cDiv=document.createElement("div");var Aa=a.p.hidegrid===true?b("<a role='link' href='javascript:void(0)'/>").addClass("ui-jqgrid-titlebar-close HeaderButton").hover(function(){Aa.addClass("ui-state-hover")},function(){Aa.removeClass("ui-state-hover")}).append("<span class='ui-icon ui-icon-circle-triangle-n'></span>").css(l==
"rtl"?"left":"right","0px"):"";b(g.cDiv).append(Aa).append("<span class='ui-jqgrid-title"+(l=="rtl"?"-rtl":"")+"'>"+a.p.caption+"</span>").addClass("ui-jqgrid-titlebar ui-widget-header ui-corner-top ui-helper-clearfix");b(g.cDiv).insertBefore(g.hDiv);if(a.p.toolbar[0]){g.uDiv=document.createElement("div");if(a.p.toolbar[1]=="top")b(g.uDiv).insertBefore(g.hDiv);else a.p.toolbar[1]=="bottom"&&b(g.uDiv).insertAfter(g.hDiv);if(a.p.toolbar[1]=="both"){g.ubDiv=document.createElement("div");b(g.uDiv).insertBefore(g.hDiv).addClass("ui-userdata ui-state-default").attr("id",
"t_"+this.id);b(g.ubDiv).insertAfter(g.hDiv).addClass("ui-userdata ui-state-default").attr("id","tb_"+this.id);ea&&b(g.ubDiv).hide()}else b(g.uDiv).width(g.width).addClass("ui-userdata ui-state-default").attr("id","t_"+this.id);ea&&b(g.uDiv).hide()}if(a.p.toppager){a.p.toppager=b.jgrid.jqID(a.p.id)+"_toppager";g.topDiv=b("<div id='"+a.p.toppager+"'></div>")[0];a.p.toppager="#"+a.p.toppager;b(g.topDiv).insertBefore(g.hDiv).addClass("ui-state-default ui-jqgrid-toppager").width(g.width);x(a.p.toppager,
"_t")}if(a.p.footerrow){g.sDiv=b("<div class='ui-jqgrid-sdiv'></div>")[0];k=b("<div class='ui-jqgrid-hbox"+(l=="rtl"?"-rtl":"")+"'></div>");b(g.sDiv).append(k).insertAfter(g.hDiv).width(g.width);b(k).append(va);g.footers=b(".ui-jqgrid-ftable",g.sDiv)[0].rows[0].cells;if(a.p.rownumbers)g.footers[0].className="ui-state-default jqgrid-rownum";ea&&b(g.sDiv).hide()}k=null;if(a.p.caption){var Ga=a.p.datatype;if(a.p.hidegrid===true){b(".ui-jqgrid-titlebar-close",g.cDiv).click(function(e){var d=b.isFunction(a.p.onHeaderClick),
i=".ui-jqgrid-bdiv, .ui-jqgrid-hdiv, .ui-jqgrid-pager, .ui-jqgrid-sdiv",n,p=this;if(a.p.toolbar[0]===true){if(a.p.toolbar[1]=="both")i+=", #"+b(g.ubDiv).attr("id");i+=", #"+b(g.uDiv).attr("id")}n=b(i,"#gview_"+b.jgrid.jqID(a.p.id)).length;if(a.p.gridstate=="visible")b(i,"#gbox_"+b.jgrid.jqID(a.p.id)).slideUp("fast",function(){n--;if(n===0){b("span",p).removeClass("ui-icon-circle-triangle-n").addClass("ui-icon-circle-triangle-s");a.p.gridstate="hidden";b("#gbox_"+b.jgrid.jqID(a.p.id)).hasClass("ui-resizable")&&
b(".ui-resizable-handle","#gbox_"+b.jgrid.jqID(a.p.id)).hide();if(d)ea||a.p.onHeaderClick.call(a,a.p.gridstate,e)}});else a.p.gridstate=="hidden"&&b(i,"#gbox_"+b.jgrid.jqID(a.p.id)).slideDown("fast",function(){n--;if(n===0){b("span",p).removeClass("ui-icon-circle-triangle-s").addClass("ui-icon-circle-triangle-n");if(ea){a.p.datatype=Ga;ja();ea=false}a.p.gridstate="visible";b("#gbox_"+b.jgrid.jqID(a.p.id)).hasClass("ui-resizable")&&b(".ui-resizable-handle","#gbox_"+b.jgrid.jqID(a.p.id)).show();if(d)ea||
a.p.onHeaderClick.call(a,a.p.gridstate,e)}});return false});if(ea){a.p.datatype="local";b(".ui-jqgrid-titlebar-close",g.cDiv).trigger("click")}}}else b(g.cDiv).hide();b(g.hDiv).after(g.bDiv).mousemove(function(e){if(g.resizing){g.dragMove(e);return false}});b(".ui-jqgrid-labels",g.hDiv).bind("selectstart",function(){return false});b(document).mouseup(function(){if(g.resizing){g.dragEnd();return false}return true});a.formatCol=P;a.sortData=Ca;a.updatepager=function(e,d){var i,n,p,A,s,E,u,z="",w=a.p.pager?
"_"+b.jgrid.jqID(a.p.pager.substr(1)):"",t=a.p.toppager?"_"+a.p.toppager.substr(1):"";p=parseInt(a.p.page,10)-1;if(p<0)p=0;p*=parseInt(a.p.rowNum,10);s=p+a.p.reccount;if(a.p.scroll){i=b("tbody:first > tr:gt(0)",a.grid.bDiv);p=s-i.length;a.p.reccount=i.length;if(n=i.outerHeight()||a.grid.prevRowHeight){i=p*n;n*=parseInt(a.p.records,10);b(">div:first",a.grid.bDiv).css({height:n}).children("div:first").css({height:i,display:i?"":"none"})}a.grid.bDiv.scrollLeft=a.grid.hDiv.scrollLeft}z=a.p.pager?a.p.pager:
"";z+=a.p.toppager?z?","+a.p.toppager:a.p.toppager:"";if(z){u=b.jgrid.formatter.integer||{};i=M(a.p.page);n=M(a.p.lastpage);b(".selbox",z).attr("disabled",false);if(a.p.pginput===true){b(".ui-pg-input",z).val(a.p.page);A=a.p.toppager?"#sp_1"+w+",#sp_1"+t:"#sp_1"+w;b(A).html(b.fmatter?b.fmatter.util.NumberFormat(a.p.lastpage,u):a.p.lastpage)}if(a.p.viewrecords)if(a.p.reccount===0)b(".ui-paging-info",z).html(a.p.emptyrecords);else{A=p+1;E=a.p.records;if(b.fmatter){A=b.fmatter.util.NumberFormat(A,u);
s=b.fmatter.util.NumberFormat(s,u);E=b.fmatter.util.NumberFormat(E,u)}b(".ui-paging-info",z).html(b.jgrid.format(a.p.recordtext,A,s,E))}if(a.p.pgbuttons===true){if(i<=0)i=n=0;if(i==1||i===0){b("#first"+w+", #prev"+w).addClass("ui-state-disabled").removeClass("ui-state-hover");a.p.toppager&&b("#first_t"+t+", #prev_t"+t).addClass("ui-state-disabled").removeClass("ui-state-hover")}else{b("#first"+w+", #prev"+w).removeClass("ui-state-disabled");a.p.toppager&&b("#first_t"+t+", #prev_t"+t).removeClass("ui-state-disabled")}if(i==
n||i===0){b("#next"+w+", #last"+w).addClass("ui-state-disabled").removeClass("ui-state-hover");a.p.toppager&&b("#next_t"+t+", #last_t"+t).addClass("ui-state-disabled").removeClass("ui-state-hover")}else{b("#next"+w+", #last"+w).removeClass("ui-state-disabled");a.p.toppager&&b("#next_t"+t+", #last_t"+t).removeClass("ui-state-disabled")}}}e===true&&a.p.rownumbers===true&&b("td.jqgrid-rownum",a.rows).each(function(F){b(this).html(p+1+F)});d&&a.p.jqgdnd&&b(a).jqGrid("gridDnD","updateDnD");b.isFunction(a.p.gridComplete)&&
a.p.gridComplete.call(a)};a.refreshIndex=ca;a.formatter=function(e,d,i,n,p){return o(e,d,i,n,p)};b.extend(g,{populate:ja,emptyRows:C});this.grid=g;a.addXmlData=function(e){V(e,a.grid.bDiv)};a.addJSONData=function(e){fa(e,a.grid.bDiv)};this.grid.cols=this.rows[0].cells;ja();a.p.hiddengrid=false;b(window).unload(function(){a=null})}}}})};b.jgrid.extend({getGridParam:function(f){var j=this[0];if(j&&j.grid)return f?typeof j.p[f]!="undefined"?j.p[f]:null:j.p},setGridParam:function(f){return this.each(function(){this.grid&&
typeof f==="object"&&b.extend(true,this.p,f)})},getDataIDs:function(){var f=[],j=0,h,c=0;this.each(function(){if((h=this.rows.length)&&h>0)for(;j<h;){if(b(this.rows[j]).hasClass("jqgrow")){f[c]=this.rows[j].id;c++}j++}});return f},setSelection:function(f,j){return this.each(function(){function h(a){var q=b(c.grid.bDiv)[0].clientHeight,x=b(c.grid.bDiv)[0].scrollTop,y=c.rows[a].offsetTop;a=c.rows[a].clientHeight;if(y+a>=q+x)b(c.grid.bDiv)[0].scrollTop=y-(q+x)+a+x;else if(y<q+x)if(y<x)b(c.grid.bDiv)[0].scrollTop=
y}var c=this,g,k,l;if(f!==undefined){j=j===false?false:true;k=c.rows.namedItem(f+"");if(!(!k||k.className.indexOf("ui-state-disabled")>-1)){if(c.p.scrollrows===true){g=c.rows.namedItem(f).rowIndex;g>=0&&h(g)}if(c.p.multiselect){c.p.selrow=k.id;l=b.inArray(c.p.selrow,c.p.selarrrow);if(l===-1){k.className!=="ui-subgrid"&&b(k).addClass("ui-state-highlight").attr("aria-selected","true");g=true;b("#jqg_"+b.jgrid.jqID(c.p.id)+"_"+b.jgrid.jqID(c.p.selrow)).attr("checked",g);c.p.selarrrow.push(c.p.selrow)}else{k.className!==
"ui-subgrid"&&b(k).removeClass("ui-state-highlight").attr("aria-selected","false");g=false;b("#jqg_"+b.jgrid.jqID(c.p.id)+"_"+b.jgrid.jqID(c.p.selrow)).attr("checked",g);c.p.selarrrow.splice(l,1);l=c.p.selarrrow[0];c.p.selrow=l===undefined?null:l}c.p.onSelectRow&&j&&c.p.onSelectRow.call(c,k.id,g)}else if(k.className!=="ui-subgrid"){if(c.p.selrow!=k.id){b(c.rows.namedItem(c.p.selrow)).removeClass("ui-state-highlight").attr({"aria-selected":"false",tabindex:"-1"});b(k).addClass("ui-state-highlight").attr({"aria-selected":true,
tabindex:"0"});g=true}else g=false;c.p.selrow=k.id;c.p.onSelectRow&&j&&c.p.onSelectRow.call(c,k.id,g)}}}})},resetSelection:function(f){return this.each(function(){var j=this,h,c;if(typeof f!=="undefined"){c=f===j.p.selrow?j.p.selrow:f;b("#"+b.jgrid.jqID(j.p.id)+" tbody:first tr#"+b.jgrid.jqID(c)).removeClass("ui-state-highlight").attr("aria-selected","false");if(j.p.multiselect){b("#jqg_"+b.jgrid.jqID(j.p.id)+"_"+b.jgrid.jqID(c)).attr("checked",false);b("#cb_"+b.jgrid.jqID(j.p.id)).attr("checked",
false)}c=null}else if(j.p.multiselect){b(j.p.selarrrow).each(function(g,k){h=j.rows.namedItem(k);b(h).removeClass("ui-state-highlight").attr("aria-selected","false");b("#jqg_"+b.jgrid.jqID(j.p.id)+"_"+b.jgrid.jqID(k)).attr("checked",false)});b("#cb_"+b.jgrid.jqID(j.p.id)).attr("checked",false);j.p.selarrrow=[]}else if(j.p.selrow){b("#"+b.jgrid.jqID(j.p.id)+" tbody:first tr#"+b.jgrid.jqID(j.p.selrow)).removeClass("ui-state-highlight").attr("aria-selected","false");j.p.selrow=null}j.p.savedRow=[]})},
getRowData:function(f){var j={},h,c=false,g,k=0;this.each(function(){var l=this,a,q;if(typeof f=="undefined"){c=true;h=[];g=l.rows.length}else{q=l.rows.namedItem(f);if(!q)return j;g=2}for(;k<g;){if(c)q=l.rows[k];if(b(q).hasClass("jqgrow")){b("td",q).each(function(x){a=l.p.colModel[x].name;if(a!=="cb"&&a!=="subgrid"&&a!=="rn")if(l.p.treeGrid===true&&a==l.p.ExpandColumn)j[a]=b.jgrid.htmlDecode(b("span:first",this).html());else try{j[a]=b.unformat(this,{rowId:q.id,colModel:l.p.colModel[x]},x)}catch(y){j[a]=
b.jgrid.htmlDecode(b(this).html())}});if(c){h.push(j);j={}}}k++}});return h?h:j},delRowData:function(f){var j=false,h,c;this.each(function(){if(h=this.rows.namedItem(f)){b(h).remove();this.p.records--;this.p.reccount--;this.updatepager(true,false);j=true;if(this.p.multiselect){c=b.inArray(f,this.p.selarrrow);c!=-1&&this.p.selarrrow.splice(c,1)}if(f==this.p.selrow)this.p.selrow=null}else return false;if(this.p.datatype=="local"){var g=this.p._index[f];if(typeof g!="undefined"){this.p.data.splice(g,
1);this.refreshIndex()}}if(this.p.altRows===true&&j){var k=this.p.altclass;b(this.rows).each(function(l){l%2==1?b(this).addClass(k):b(this).removeClass(k)})}});return j},setRowData:function(f,j,h){var c,g=true,k;this.each(function(){if(!this.grid)return false;var l=this,a,q,x=typeof h,y={};q=l.rows.namedItem(f);if(!q)return false;if(j)try{b(this.p.colModel).each(function(P){c=this.name;if(j[c]!==undefined){y[c]=this.formatter&&typeof this.formatter==="string"&&this.formatter=="date"?b.unformat.date(j[c],
this):j[c];a=l.formatter(f,j[c],P,j,"edit");k=this.title?{title:b.jgrid.stripHtml(a)}:{};l.p.treeGrid===true&&c==l.p.ExpandColumn?b("td:eq("+P+") > span:first",q).html(a).attr(k):b("td:eq("+P+")",q).html(a).attr(k)}});if(l.p.datatype=="local"){var H=l.p._index[f];if(l.p.treeGrid)for(var L in l.p.treeReader)y.hasOwnProperty(l.p.treeReader[L])&&delete y[l.p.treeReader[L]];if(typeof H!="undefined")l.p.data[H]=b.extend(true,l.p.data[H],y);y=null}}catch(M){g=false}if(g)if(x==="string")b(q).addClass(h);
else x==="object"&&b(q).css(h)});return g},addRowData:function(f,j,h,c){h||(h="last");var g=false,k,l,a,q,x,y,H,L,M="",P,U,o,m,r;if(j){if(b.isArray(j)){P=true;h="last";U=f}else{j=[j];P=false}this.each(function(){var D=j.length;x=this.p.rownumbers===true?1:0;a=this.p.multiselect===true?1:0;q=this.p.subGrid===true?1:0;if(!P)if(typeof f!="undefined")f+="";else{f=b.jgrid.randId();if(this.p.keyIndex!==false){U=this.p.colModel[this.p.keyIndex+a+q+x].name;if(typeof j[0][U]!="undefined")f=j[0][U]}}o=this.p.altclass;
for(var T=0,I="",C={},ca=b.isFunction(this.p.afterInsertRow)?true:false;T<D;){m=j[T];l="";if(P){try{f=m[U]}catch(V){f=b.jgrid.randId()}I=this.p.altRows===true?(this.rows.length-1)%2===0?o:"":""}if(x){M=this.formatCol(0,1,"",null,f,true);l+='<td role="gridcell" aria-describedby="'+this.p.id+'_rn" class="ui-state-default jqgrid-rownum" '+M+">0</td>"}if(a){L='<input role="checkbox" type="checkbox" id="jqg_'+this.p.id+"_"+f+'" class="cbox"/>';M=this.formatCol(x,1,"",null,f,true);l+='<td role="gridcell" aria-describedby="'+
this.p.id+'_cb" '+M+">"+L+"</td>"}if(q)l+=b(this).jqGrid("addSubGridCell",a+x,1);for(H=a+q+x;H<this.p.colModel.length;H++){r=this.p.colModel[H];k=r.name;C[k]=r.formatter&&typeof r.formatter==="string"&&r.formatter=="date"?b.unformat.date(m[k],r):m[k];L=this.formatter(f,b.jgrid.getAccessor(m,k),H,m,"edit");M=this.formatCol(H,1,L,f,m,true);l+='<td role="gridcell" aria-describedby="'+this.p.id+"_"+k+'" '+M+">"+L+"</td>"}l='<tr id="'+f+'" role="row" tabindex="-1" class="ui-widget-content jqgrow ui-row-'+
this.p.direction+" "+I+'">'+l+"</tr>";if(this.rows.length===0)b("table:first",this.grid.bDiv).append(l);else switch(h){case "last":b(this.rows[this.rows.length-1]).after(l);y=this.rows.length-1;break;case "first":b(this.rows[0]).after(l);y=1;break;case "after":if(y=this.rows.namedItem(c))b(this.rows[y.rowIndex+1]).hasClass("ui-subgrid")?b(this.rows[y.rowIndex+1]).after(l):b(y).after(l);y++;break;case "before":if(y=this.rows.namedItem(c)){b(y).before(l);y=y.rowIndex}y--}this.p.subGrid===true&&b(this).jqGrid("addSubGrid",
a+x,y);this.p.records++;this.p.reccount++;ca&&this.p.afterInsertRow.call(this,f,m,m);T++;if(this.p.datatype=="local"){C[this.p.localReader.id]=f;this.p._index[f]=this.p.data.length;this.p.data.push(C);C={}}}if(this.p.altRows===true&&!P)if(h=="last")(this.rows.length-1)%2==1&&b(this.rows[this.rows.length-1]).addClass(o);else b(this.rows).each(function(fa){fa%2==1?b(this).addClass(o):b(this).removeClass(o)});this.updatepager(true,true);g=true})}return g},footerData:function(f,j,h){function c(q){for(var x in q)if(q.hasOwnProperty(x))return false;
return true}var g,k=false,l={},a;if(typeof f=="undefined")f="get";if(typeof h!="boolean")h=true;f=f.toLowerCase();this.each(function(){var q=this,x;if(!q.grid||!q.p.footerrow)return false;if(f=="set")if(c(j))return false;k=true;b(this.p.colModel).each(function(y){g=this.name;if(f=="set"){if(j[g]!==undefined){x=h?q.formatter("",j[g],y,j,"edit"):j[g];a=this.title?{title:b.jgrid.stripHtml(x)}:{};b("tr.footrow td:eq("+y+")",q.grid.sDiv).html(x).attr(a);k=true}}else if(f=="get")l[g]=b("tr.footrow td:eq("+
y+")",q.grid.sDiv).html()})});return f=="get"?l:k},showHideCol:function(f,j){return this.each(function(){var h=this,c=false,g=b.browser.webkit||b.browser.safari?0:h.p.cellLayout,k;if(h.grid){if(typeof f==="string")f=[f];j=j!="none"?"":"none";var l=j===""?true:false;b(this.p.colModel).each(function(a){if(b.inArray(this.name,f)!==-1&&this.hidden===l){b("tr",h.grid.hDiv).each(function(){b(this).children("th:eq("+a+")").css("display",j)});b(h.rows).each(function(){b(this).children("td:eq("+a+")").css("display",
j)});h.p.footerrow&&b("tr.footrow td:eq("+a+")",h.grid.sDiv).css("display",j);k=this.widthOrg?this.widthOrg:parseInt(this.width,10);if(j==="none")h.p.tblwidth-=k+g;else h.p.tblwidth+=k+g;this.hidden=!l;c=true}});if(c===true)if(h.p.shrinkToFit===false)b(h).jqGrid("setGridWidth",h.grid.width);else h.grid.width!==h.p.tblwidth&&b(h).jqGrid("setGridWidth",h.p.tblwidth)}})},hideCol:function(f){return this.each(function(){b(this).jqGrid("showHideCol",f,"none")})},showCol:function(f){return this.each(function(){b(this).jqGrid("showHideCol",
f,"")})},remapColumns:function(f,j,h){function c(l){var a;a=l.length?b.makeArray(l):b.extend({},l);b.each(f,function(q){l[q]=a[this]})}function g(l,a){b(">tr"+(a||""),l).each(function(){var q=this,x=b.makeArray(q.cells);b.each(f,function(){var y=x[this];y&&q.appendChild(y)})})}var k=this.get(0);c(k.p.colModel);c(k.p.colNames);c(k.grid.headers);g(b("thead:first",k.grid.hDiv),h&&":not(.ui-jqgrid-labels)");j&&g(b("#"+b.jgrid.jqID(k.p.id)+" tbody:first"),".jqgfirstrow, tr.jqgrow, tr.jqfoot");k.p.footerrow&&
g(b("tbody:first",k.grid.sDiv));if(k.p.remapColumns)if(k.p.remapColumns.length)c(k.p.remapColumns);else k.p.remapColumns=b.makeArray(f);k.p.lastsort=b.inArray(k.p.lastsort,f);if(k.p.treeGrid)k.p.expColInd=b.inArray(k.p.expColInd,f)},setGridWidth:function(f,j){return this.each(function(){if(this.grid){var h=this,c,g=0,k=b.browser.webkit||b.browser.safari?0:h.p.cellLayout,l,a=0,q=false,x=h.p.scrollOffset,y,H=0,L=0,M;if(typeof j!="boolean")j=h.p.shrinkToFit;if(!isNaN(f)){f=parseInt(f,10);h.grid.width=
h.p.width=f;b("#gbox_"+b.jgrid.jqID(h.p.id)).css("width",f+"px");b("#gview_"+b.jgrid.jqID(h.p.id)).css("width",f+"px");b(h.grid.bDiv).css("width",f+"px");b(h.grid.hDiv).css("width",f+"px");h.p.pager&&b(h.p.pager).css("width",f+"px");h.p.toppager&&b(h.p.toppager).css("width",f+"px");if(h.p.toolbar[0]===true){b(h.grid.uDiv).css("width",f+"px");h.p.toolbar[1]=="both"&&b(h.grid.ubDiv).css("width",f+"px")}h.p.footerrow&&b(h.grid.sDiv).css("width",f+"px");if(j===false&&h.p.forceFit===true)h.p.forceFit=
false;if(j===true){b.each(h.p.colModel,function(){if(this.hidden===false){c=this.widthOrg?this.widthOrg:parseInt(this.width,10);g+=c+k;if(this.fixed)H+=c+k;else a++;L++}});if(a===0)return;h.p.tblwidth=g;y=f-k*a-H;if(!isNaN(h.p.height))if(b(h.grid.bDiv)[0].clientHeight<b(h.grid.bDiv)[0].scrollHeight||h.rows.length===1){q=true;y-=x}g=0;var P=h.grid.cols.length>0;b.each(h.p.colModel,function(U){if(this.hidden===false&&!this.fixed){c=this.widthOrg?this.widthOrg:parseInt(this.width,10);c=Math.round(y*
c/(h.p.tblwidth-k*a-H));if(!(c<0)){this.width=c;g+=c;h.grid.headers[U].width=c;h.grid.headers[U].el.style.width=c+"px";if(h.p.footerrow)h.grid.footers[U].style.width=c+"px";if(P)h.grid.cols[U].style.width=c+"px";l=U}}});M=0;if(q){if(f-H-(g+k*a)!==x)M=f-H-(g+k*a)-x}else if(Math.abs(f-H-(g+k*a))!==1)M=f-H-(g+k*a);h.p.colModel[l].width+=M;h.p.tblwidth=g+M+k*a+H;if(h.p.tblwidth>f){q=h.p.tblwidth-parseInt(f,10);h.p.tblwidth=f;c=h.p.colModel[l].width-=q}else c=h.p.colModel[l].width;h.grid.headers[l].width=
c;h.grid.headers[l].el.style.width=c+"px";if(P)h.grid.cols[l].style.width=c+"px";if(h.p.footerrow)h.grid.footers[l].style.width=c+"px"}if(h.p.tblwidth){b("table:first",h.grid.bDiv).css("width",h.p.tblwidth+"px");b("table:first",h.grid.hDiv).css("width",h.p.tblwidth+"px");h.grid.hDiv.scrollLeft=h.grid.bDiv.scrollLeft;h.p.footerrow&&b("table:first",h.grid.sDiv).css("width",h.p.tblwidth+"px")}}}})},setGridHeight:function(f){return this.each(function(){if(this.grid){b(this.grid.bDiv).css({height:f+(isNaN(f)?
"":"px")});this.p.height=f;this.p.scroll&&this.grid.populateVisible()}})},setCaption:function(f){return this.each(function(){this.p.caption=f;b("span.ui-jqgrid-title",this.grid.cDiv).html(f);b(this.grid.cDiv).show()})},setLabel:function(f,j,h,c){return this.each(function(){var g=-1;if(this.grid)if(typeof f!="undefined"){b(this.p.colModel).each(function(a){if(this.name==f){g=a;return false}});if(g>=0){var k=b("tr.ui-jqgrid-labels th:eq("+g+")",this.grid.hDiv);if(j){var l=b(".s-ico",k);b("[id^=jqgh_]",
k).empty().html(j).append(l);this.p.colNames[g]=j}if(h)typeof h==="string"?b(k).addClass(h):b(k).css(h);typeof c==="object"&&b(k).attr(c)}}})},setCell:function(f,j,h,c,g,k){return this.each(function(){var l=-1,a,q;if(this.grid){if(isNaN(j))b(this.p.colModel).each(function(y){if(this.name==j){l=y;return false}});else l=parseInt(j,10);if(l>=0)if(a=this.rows.namedItem(f)){var x=b("td:eq("+l+")",a);if(h!==""||k===true){a=this.formatter(f,h,l,a,"edit");q=this.p.colModel[l].title?{title:b.jgrid.stripHtml(a)}:
{};this.p.treeGrid&&b(".tree-wrap",b(x)).length>0?b("span",b(x)).html(a).attr(q):b(x).html(a).attr(q);if(this.p.datatype=="local"){a=this.p.colModel[l];h=a.formatter&&typeof a.formatter==="string"&&a.formatter=="date"?b.unformat.date(h,a):h;q=this.p._index[f];if(typeof q!="undefined")this.p.data[q][a.name]=h}}if(typeof c==="string")b(x).addClass(c);else c&&b(x).css(c);typeof g==="object"&&b(x).attr(g)}}})},getCell:function(f,j){var h=false;this.each(function(){var c=-1;if(this.grid){if(isNaN(j))b(this.p.colModel).each(function(l){if(this.name===
j){c=l;return false}});else c=parseInt(j,10);if(c>=0){var g=this.rows.namedItem(f);if(g)try{h=b.unformat(b("td:eq("+c+")",g),{rowId:g.id,colModel:this.p.colModel[c]},c)}catch(k){h=b.jgrid.htmlDecode(b("td:eq("+c+")",g).html())}}}});return h},getCol:function(f,j,h){var c=[],g,k=0,l=0,a=0,q;j=typeof j!="boolean"?false:j;if(typeof h=="undefined")h=false;this.each(function(){var x=-1;if(this.grid){if(isNaN(f))b(this.p.colModel).each(function(M){if(this.name===f){x=M;return false}});else x=parseInt(f,
10);if(x>=0){var y=this.rows.length,H=0;if(y&&y>0){for(;H<y;){if(b(this.rows[H]).hasClass("jqgrow")){try{g=b.unformat(b(this.rows[H].cells[x]),{rowId:this.rows[H].id,colModel:this.p.colModel[x]},x)}catch(L){g=b.jgrid.htmlDecode(this.rows[H].cells[x].innerHTML)}if(h){q=parseFloat(g);k+=q;l=Math.min(l,q);a=Math.max(l,q)}else j?c.push({id:this.rows[H].id,value:g}):c.push(g)}H++}if(h)switch(h.toLowerCase()){case "sum":c=k;break;case "avg":c=k/y;break;case "count":c=y;break;case "min":c=l;break;case "max":c=
a}}}}});return c},clearGridData:function(f){return this.each(function(){if(this.grid){if(typeof f!="boolean")f=false;if(this.p.deepempty)b("#"+b.jgrid.jqID(this.p.id)+" tbody:first tr:gt(0)").remove();else{var j=b("#"+b.jgrid.jqID(this.p.id)+" tbody:first tr:first")[0];b("#"+b.jgrid.jqID(this.p.id)+" tbody:first").empty().append(j)}this.p.footerrow&&f&&b(".ui-jqgrid-ftable td",this.grid.sDiv).html("&#160;");this.p.selrow=null;this.p.selarrrow=[];this.p.savedRow=[];this.p.records=0;this.p.page=1;this.p.lastpage=
0;this.p.reccount=0;this.p.data=[];this.p._index={};this.updatepager(true,false)}})},getInd:function(f,j){var h=false,c;this.each(function(){if(c=this.rows.namedItem(f))h=j===true?c:c.rowIndex});return h},bindKeys:function(f){var j=b.extend({onEnter:null,onSpace:null,onLeftKey:null,onRightKey:null,scrollingRows:true},f||{});return this.each(function(){var h=this;b("body").is("[role]")||b("body").attr("role","application");h.p.scrollrows=j.scrollingRows;b(h).keydown(function(c){var g=b(h).find("tr[tabindex=0]")[0],
k,l,a,q=h.p.treeReader.expanded_field;if(g){a=h.p._index[g.id];if(c.keyCode===37||c.keyCode===38||c.keyCode===39||c.keyCode===40){if(c.keyCode===38){l=g.previousSibling;k="";if(l)if(b(l).is(":hidden"))for(;l;){l=l.previousSibling;if(!b(l).is(":hidden")&&b(l).hasClass("jqgrow")){k=l.id;break}}else k=l.id;b(h).jqGrid("setSelection",k)}if(c.keyCode===40){l=g.nextSibling;k="";if(l)if(b(l).is(":hidden"))for(;l;){l=l.nextSibling;if(!b(l).is(":hidden")&&b(l).hasClass("jqgrow")){k=l.id;break}}else k=l.id;
b(h).jqGrid("setSelection",k)}if(c.keyCode===37){h.p.treeGrid&&h.p.data[a][q]&&b(g).find("div.treeclick").trigger("click");b.isFunction(j.onLeftKey)&&j.onLeftKey.call(h,h.p.selrow)}if(c.keyCode===39){h.p.treeGrid&&!h.p.data[a][q]&&b(g).find("div.treeclick").trigger("click");b.isFunction(j.onRightKey)&&j.onRightKey.call(h,h.p.selrow)}}else if(c.keyCode===13)b.isFunction(j.onEnter)&&j.onEnter.call(h,h.p.selrow);else c.keyCode===32&&b.isFunction(j.onSpace)&&j.onSpace.call(h,h.p.selrow)}})})},unbindKeys:function(){return this.each(function(){b(this).unbind("keydown")})},
getLocalRow:function(f){var j=false,h;this.each(function(){if(typeof f!=="undefined"){h=this.p._index[f];if(h>=0)j=this.p.data[h]}});return j}})})(jQuery);
(function(b){b.fmatter={};b.extend(b.fmatter,{isBoolean:function(a){return typeof a==="boolean"},isObject:function(a){return a&&(typeof a==="object"||b.isFunction(a))||false},isString:function(a){return typeof a==="string"},isNumber:function(a){return typeof a==="number"&&isFinite(a)},isNull:function(a){return a===null},isUndefined:function(a){return typeof a==="undefined"},isValue:function(a){return this.isObject(a)||this.isString(a)||this.isNumber(a)||this.isBoolean(a)},isEmpty:function(a){if(!this.isString(a)&&
this.isValue(a))return false;else if(!this.isValue(a))return true;a=b.trim(a).replace(/\&nbsp\;/ig,"").replace(/\&#160\;/ig,"");return a===""}});b.fn.fmatter=function(a,c,d,e,f){var g=c;d=b.extend({},b.jgrid.formatter,d);if(b.fn.fmatter[a])g=b.fn.fmatter[a](c,d,e,f);return g};b.fmatter.util={NumberFormat:function(a,c){b.fmatter.isNumber(a)||(a*=1);if(b.fmatter.isNumber(a)){var d=a<0,e=a+"",f=c.decimalSeparator?c.decimalSeparator:".",g;if(b.fmatter.isNumber(c.decimalPlaces)){var h=c.decimalPlaces;
e=Math.pow(10,h);e=Math.round(a*e)/e+"";g=e.lastIndexOf(".");if(h>0){if(g<0){e+=f;g=e.length-1}else if(f!==".")e=e.replace(".",f);for(;e.length-1-g<h;)e+="0"}}if(c.thousandsSeparator){h=c.thousandsSeparator;g=e.lastIndexOf(f);g=g>-1?g:e.length;f=e.substring(g);for(var i=-1,j=g;j>0;j--){i++;if(i%3===0&&j!==g&&(!d||j>1))f=h+f;f=e.charAt(j-1)+f}e=f}e=c.prefix?c.prefix+e:e;return e=c.suffix?e+c.suffix:e}else return a},DateFormat:function(a,c,d,e){var f=/^\/Date\((([-+])?[0-9]+)(([-+])([0-9]{2})([0-9]{2}))?\)\/$/,
g=typeof c==="string"?c.match(f):null;f=function(m,r){m=String(m);for(r=parseInt(r,10)||2;m.length<r;)m="0"+m;return m};var h={m:1,d:1,y:1970,h:0,i:0,s:0,u:0},i=0,j,k=["i18n"];k.i18n={dayNames:e.dayNames,monthNames:e.monthNames};if(a in e.masks)a=e.masks[a];if(c.constructor===Number){if(String(a).toLowerCase()=="u")c*=1E3;i=new Date(c)}else if(c.constructor===Date)i=c;else if(g!==null){i=new Date(parseInt(g[1],10));if(g[3]){a=Number(g[5])*60+Number(g[6]);a*=g[4]=="-"?1:-1;a-=i.getTimezoneOffset();
i.setTime(Number(Number(i)+a*6E4))}}else{c=String(c).split(/[\\\/:_;.,\t\T\s-]/);a=a.split(/[\\\/:_;.,\t\T\s-]/);g=0;for(j=a.length;g<j;g++){if(a[g]=="M"){i=b.inArray(c[g],k.i18n.monthNames);if(i!==-1&&i<12)c[g]=i+1}if(a[g]=="F"){i=b.inArray(c[g],k.i18n.monthNames);if(i!==-1&&i>11)c[g]=i*****}if(c[g])h[a[g].toLowerCase()]=parseInt(c[g],10)}if(h.f)h.m=h.f;if(h.m===0&&h.y===0&&h.d===0)return"&#160;";h.m=parseInt(h.m,10)-1;i=h.y;if(i>=70&&i<=99)h.y=1900+h.y;else if(i>=0&&i<=69)h.y=2E3+h.y;i=new Date(h.y,
h.m,h.d,h.h,h.i,h.s,h.u)}if(d in e.masks)d=e.masks[d];else d||(d="Y-m-d");a=i.getHours();c=i.getMinutes();h=i.getDate();g=i.getMonth()+1;j=i.getTimezoneOffset();var l=i.getSeconds(),o=i.getMilliseconds(),n=i.getDay(),p=i.getFullYear(),q=(n+6)%7+1,s=(new Date(p,g-1,h)-new Date(p,0,1))/864E5,t={d:f(h),D:k.i18n.dayNames[n],j:h,l:k.i18n.dayNames[n+7],N:q,S:e.S(h),w:n,z:s,W:q<5?Math.floor((s+q-1)/7)+1:Math.floor((s+q-1)/7)||(((new Date(p-1,0,1)).getDay()+6)%7<4?53:52),F:k.i18n.monthNames[g-1+12],m:f(g),
M:k.i18n.monthNames[g-1],n:g,t:"?",L:"?",o:"?",Y:p,y:String(p).substring(2),a:a<12?e.AmPm[0]:e.AmPm[1],A:a<12?e.AmPm[2]:e.AmPm[3],B:"?",g:a%12||12,G:a,h:f(a%12||12),H:f(a),i:f(c),s:f(l),u:o,e:"?",I:"?",O:(j>0?"-":"+")+f(Math.floor(Math.abs(j)/60)*100+Math.abs(j)%60,4),P:"?",T:(String(i).match(/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g)||[""]).pop().replace(/[^-+\dA-Z]/g,""),Z:"?",c:"?",r:"?",U:Math.floor(i/
1E3)};return d.replace(/\\.|[dDjlNSwzWFmMntLoYyaABgGhHisueIOPTZcrU]/g,function(m){return m in t?t[m]:m.substring(1)})}};b.fn.fmatter.defaultFormat=function(a,c){return b.fmatter.isValue(a)&&a!==""?a:c.defaultValue?c.defaultValue:"&#160;"};b.fn.fmatter.email=function(a,c){return b.fmatter.isEmpty(a)?b.fn.fmatter.defaultFormat(a,c):'<a href="mailto:'+a+'">'+a+"</a>"};b.fn.fmatter.checkbox=function(a,c){var d=b.extend({},c.checkbox),e;b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,
c.colModel.formatoptions));e=d.disabled===true?'disabled="disabled"':"";if(b.fmatter.isEmpty(a)||b.fmatter.isUndefined(a))a=b.fn.fmatter.defaultFormat(a,d);a+="";a=a.toLowerCase();return'<input type="checkbox" '+(a.search(/(false|0|no|off)/i)<0?" checked='checked' ":"")+' value="'+a+'" offval="no" '+e+"/>"};b.fn.fmatter.link=function(a,c){var d={target:c.target},e="";b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));if(d.target)e="target="+d.target;return b.fmatter.isEmpty(a)?
b.fn.fmatter.defaultFormat(a,c):"<a "+e+' href="'+a+'">'+a+"</a>"};b.fn.fmatter.showlink=function(a,c){var d={baseLinkUrl:c.baseLinkUrl,showAction:c.showAction,addParam:c.addParam||"",target:c.target,idName:c.idName},e="";b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));if(d.target)e="target="+d.target;d=d.baseLinkUrl+d.showAction+"?"+d.idName+"="+c.rowId+d.addParam;return b.fmatter.isString(a)||b.fmatter.isNumber(a)?"<a "+e+' href="'+d+'">'+a+"</a>":b.fn.fmatter.defaultFormat(a,
c)};b.fn.fmatter.integer=function(a,c){var d=b.extend({},c.integer);b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));if(b.fmatter.isEmpty(a))return d.defaultValue;return b.fmatter.util.NumberFormat(a,d)};b.fn.fmatter.number=function(a,c){var d=b.extend({},c.number);b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));if(b.fmatter.isEmpty(a))return d.defaultValue;return b.fmatter.util.NumberFormat(a,d)};b.fn.fmatter.currency=
function(a,c){var d=b.extend({},c.currency);b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));if(b.fmatter.isEmpty(a))return d.defaultValue;return b.fmatter.util.NumberFormat(a,d)};b.fn.fmatter.date=function(a,c,d,e){d=b.extend({},c.date);b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend({},d,c.colModel.formatoptions));return!d.reformatAfterEdit&&e=="edit"?b.fn.fmatter.defaultFormat(a,c):b.fmatter.isEmpty(a)?b.fn.fmatter.defaultFormat(a,c):b.fmatter.util.DateFormat(d.srcformat,
a,d.newformat,d)};b.fn.fmatter.select=function(a,c){a+="";var d=false,e=[];if(b.fmatter.isUndefined(c.colModel.formatoptions)){if(!b.fmatter.isUndefined(c.colModel.editoptions))d=c.colModel.editoptions.value}else d=c.colModel.formatoptions.value;if(d){var f=c.colModel.editoptions.multiple===true?true:false,g=[],h;if(f){g=a.split(",");g=b.map(g,function(l){return b.trim(l)})}if(b.fmatter.isString(d))for(var i=d.split(";"),j=0,k=0;k<i.length;k++){h=i[k].split(":");if(h.length>2)h[1]=jQuery.map(h,function(l,
o){if(o>0)return l}).join(":");if(f){if(jQuery.inArray(h[0],g)>-1){e[j]=h[1];j++}}else if(b.trim(h[0])==b.trim(a)){e[0]=h[1];break}}else if(b.fmatter.isObject(d))if(f)e=jQuery.map(g,function(l){return d[l]});else e[0]=d[a]||""}a=e.join(", ");return a===""?b.fn.fmatter.defaultFormat(a,c):a};b.fn.fmatter.rowactions=function(a,c,d,e){var f={keys:false,onEdit:null,onSuccess:null,afterSave:null,onError:null,afterRestore:null,extraparam:{oper:"edit"},url:null,delOptions:{},editOptions:{}};a=b.jgrid.jqID(a);
c=b.jgrid.jqID(c);e=b("#"+c)[0].p.colModel[e];b.fmatter.isUndefined(e.formatoptions)||(f=b.extend(f,e.formatoptions));if(!b.fmatter.isUndefined(b("#"+c)[0].p.editOptions))f.editOptions=b("#"+c)[0].p.editOptions;if(!b.fmatter.isUndefined(b("#"+c)[0].p.delOptions))f.delOptions=b("#"+c)[0].p.delOptions;e=function(h){f.afterSave&&f.afterSave(h);b("tr#"+a+" div.ui-inline-edit, tr#"+a+" div.ui-inline-del","#"+c+".ui-jqgrid-btable:first").show();b("tr#"+a+" div.ui-inline-save, tr#"+a+" div.ui-inline-cancel",
"#"+c+".ui-jqgrid-btable:first").hide()};var g=function(h){f.afterRestore&&f.afterRestore(h);b("tr#"+a+" div.ui-inline-edit, tr#"+a+" div.ui-inline-del","#"+c+".ui-jqgrid-btable:first").show();b("tr#"+a+" div.ui-inline-save, tr#"+a+" div.ui-inline-cancel","#"+c+".ui-jqgrid-btable:first").hide()};switch(d){case "edit":b("#"+c).jqGrid("editRow",a,f.keys,f.onEdit,f.onSuccess,f.url,f.extraparam,e,f.onError,g);b("tr#"+a+" div.ui-inline-edit, tr#"+a+" div.ui-inline-del","#"+c+".ui-jqgrid-btable:first").hide();
b("tr#"+a+" div.ui-inline-save, tr#"+a+" div.ui-inline-cancel","#"+c+".ui-jqgrid-btable:first").show();break;case "save":if(b("#"+c).jqGrid("saveRow",a,f.onSuccess,f.url,f.extraparam,e,f.onError,g)){b("tr#"+a+" div.ui-inline-edit, tr#"+a+" div.ui-inline-del","#"+c+".ui-jqgrid-btable:first").show();b("tr#"+a+" div.ui-inline-save, tr#"+a+" div.ui-inline-cancel","#"+c+".ui-jqgrid-btable:first").hide()}break;case "cancel":b("#"+c).jqGrid("restoreRow",a,g);b("tr#"+a+" div.ui-inline-edit, tr#"+a+" div.ui-inline-del",
"#"+c+".ui-jqgrid-btable:first").show();b("tr#"+a+" div.ui-inline-save, tr#"+a+" div.ui-inline-cancel","#"+c+".ui-jqgrid-btable:first").hide();break;case "del":b("#"+c).jqGrid("delGridRow",a,f.delOptions);break;case "formedit":b("#"+c).jqGrid("setSelection",a);b("#"+c).jqGrid("editGridRow",a,f.editOptions)}};b.fn.fmatter.actions=function(a,c){var d={keys:false,editbutton:true,delbutton:true,editformbutton:false};b.fmatter.isUndefined(c.colModel.formatoptions)||(d=b.extend(d,c.colModel.formatoptions));
var e=c.rowId,f="",g;if(typeof e=="undefined"||b.fmatter.isEmpty(e))return"";if(d.editformbutton){g="onclick=$.fn.fmatter.rowactions('"+e+"','"+c.gid+"','formedit',"+c.pos+"); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";f=f+"<div title='"+b.jgrid.nav.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+g+"><span class='ui-icon ui-icon-pencil'></span></div>"}else if(d.editbutton){g="onclick=$.fn.fmatter.rowactions('"+
e+"','"+c.gid+"','edit',"+c.pos+"); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover') ";f=f+"<div title='"+b.jgrid.nav.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+g+"><span class='ui-icon ui-icon-pencil'></span></div>"}if(d.delbutton){g="onclick=$.fn.fmatter.rowactions('"+e+"','"+c.gid+"','del',"+c.pos+"); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";
f=f+"<div title='"+b.jgrid.nav.deltitle+"' style='float:left;margin-left:5px;' class='ui-pg-div ui-inline-del' "+g+"><span class='ui-icon ui-icon-trash'></span></div>"}g="onclick=$.fn.fmatter.rowactions('"+e+"','"+c.gid+"','save',"+c.pos+"); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";f=f+"<div title='"+b.jgrid.edit.bSubmit+"' style='float:left;display:none' class='ui-pg-div ui-inline-save' "+g+"><span class='ui-icon ui-icon-disk'></span></div>";
g="onclick=$.fn.fmatter.rowactions('"+e+"','"+c.gid+"','cancel',"+c.pos+"); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";f=f+"<div title='"+b.jgrid.edit.bCancel+"' style='float:left;display:none;margin-left:5px;' class='ui-pg-div ui-inline-cancel' "+g+"><span class='ui-icon ui-icon-cancel'></span></div>";return"<div style='margin-left:8px;'>"+f+"</div>"};b.unformat=function(a,c,d,e){var f,g=c.colModel.formatter,h=c.colModel.formatoptions||
{},i=/([\.\*\_\'\(\)\{\}\+\?\\])/g,j=c.colModel.unformat||b.fn.fmatter[g]&&b.fn.fmatter[g].unformat;if(typeof j!=="undefined"&&b.isFunction(j))f=j(b(a).text(),c,a);else if(!b.fmatter.isUndefined(g)&&b.fmatter.isString(g)){f=b.jgrid.formatter||{};switch(g){case "integer":h=b.extend({},f.integer,h);c=h.thousandsSeparator.replace(i,"\\$1");f=b(a).text().replace(RegExp(c,"g"),"");break;case "number":h=b.extend({},f.number,h);c=h.thousandsSeparator.replace(i,"\\$1");f=b(a).text().replace(RegExp(c,"g"),
"").replace(h.decimalSeparator,".");break;case "currency":h=b.extend({},f.currency,h);c=h.thousandsSeparator.replace(i,"\\$1");f=b(a).text().replace(RegExp(c,"g"),"").replace(h.decimalSeparator,".").replace(h.prefix,"").replace(h.suffix,"");break;case "checkbox":h=c.colModel.editoptions?c.colModel.editoptions.value.split(":"):["Yes","No"];f=b("input",a).attr("checked")?h[0]:h[1];break;case "select":f=b.unformat.select(a,c,d,e);break;case "actions":return"";default:f=b(a).text()}}return f!==undefined?
f:e===true?b(a).text():b.jgrid.htmlDecode(b(a).html())};b.unformat.select=function(a,c,d,e){d=[];a=b(a).text();if(e===true)return a;c=b.extend({},c.colModel.editoptions);if(c.value){var f=c.value;c=c.multiple===true?true:false;e=[];var g;if(c){e=a.split(",");e=b.map(e,function(k){return b.trim(k)})}if(b.fmatter.isString(f))for(var h=f.split(";"),i=0,j=0;j<h.length;j++){g=h[j].split(":");if(g.length>2)g[1]=jQuery.map(g,function(k,l){if(l>0)return k}).join(":");if(c){if(jQuery.inArray(g[1],e)>-1){d[i]=
g[0];i++}}else if(b.trim(g[1])==b.trim(a)){d[0]=g[0];break}}else if(b.fmatter.isObject(f)||b.isArray(f)){c||(e[0]=a);d=jQuery.map(e,function(k){var l;b.each(f,function(o,n){if(n==k){l=o;return false}});if(typeof l!="undefined")return l})}return d.join(", ")}else return a||""};b.unformat.date=function(a,c){var d=b.jgrid.formatter.date||{};b.fmatter.isUndefined(c.formatoptions)||(d=b.extend({},d,c.formatoptions));return b.fmatter.isEmpty(a)?b.fn.fmatter.defaultFormat(a,c):b.fmatter.util.DateFormat(d.newformat,
a,d.srcformat,d)}})(jQuery);
(function(a){a.jgrid.extend({getColProp:function(c){var h={},b=this[0];if(!b.grid)return false;b=b.p.colModel;for(var i=0;i<b.length;i++)if(b[i].name==c){h=b[i];break}return h},setColProp:function(c,h){return this.each(function(){if(this.grid)if(h)for(var b=this.p.colModel,i=0;i<b.length;i++)if(b[i].name==c){a.extend(this.p.colModel[i],h);break}})},sortGrid:function(c,h,b){return this.each(function(){var i=-1;if(this.grid){if(!c)c=this.p.sortname;for(var o=0;o<this.p.colModel.length;o++)if(this.p.colModel[o].index==
c||this.p.colModel[o].name==c){i=o;break}if(i!=-1){o=this.p.colModel[i].sortable;if(typeof o!=="boolean")o=true;if(typeof h!=="boolean")h=false;o&&this.sortData("jqgh_"+this.p.id+"_"+c,i,h,b)}}})},GridDestroy:function(){return this.each(function(){if(this.grid){this.p.pager&&a(this.p.pager).remove();var c=this.id;try{a("#gbox_"+c).remove()}catch(h){}}})},GridUnload:function(){return this.each(function(){if(this.grid){var c={id:a(this).attr("id"),cl:a(this).attr("class")};this.p.pager&&a(this.p.pager).empty().removeClass("ui-state-default ui-jqgrid-pager corner-bottom");
var h=document.createElement("table");a(h).attr({id:c.id});h.className=c.cl;c=this.id;a(h).removeClass("ui-jqgrid-btable");if(a(this.p.pager).parents("#gbox_"+c).length===1){a(h).insertBefore("#gbox_"+c).show();a(this.p.pager).insertBefore("#gbox_"+c)}else a(h).insertBefore("#gbox_"+c).show();a("#gbox_"+c).remove()}})},setGridState:function(c){return this.each(function(){if(this.grid)if(c=="hidden"){a(".ui-jqgrid-bdiv, .ui-jqgrid-hdiv","#gview_"+this.p.id).slideUp("fast");this.p.pager&&a(this.p.pager).slideUp("fast");
this.p.toppager&&a(this.p.toppager).slideUp("fast");if(this.p.toolbar[0]===true){this.p.toolbar[1]=="both"&&a(this.grid.ubDiv).slideUp("fast");a(this.grid.uDiv).slideUp("fast")}this.p.footerrow&&a(".ui-jqgrid-sdiv","#gbox_"+this.p.id).slideUp("fast");a(".ui-jqgrid-titlebar-close span",this.grid.cDiv).removeClass("ui-icon-circle-triangle-n").addClass("ui-icon-circle-triangle-s");this.p.gridstate="hidden"}else if(c=="visible"){a(".ui-jqgrid-hdiv, .ui-jqgrid-bdiv","#gview_"+this.p.id).slideDown("fast");
this.p.pager&&a(this.p.pager).slideDown("fast");this.p.toppager&&a(this.p.toppager).slideDown("fast");if(this.p.toolbar[0]===true){this.p.toolbar[1]=="both"&&a(this.grid.ubDiv).slideDown("fast");a(this.grid.uDiv).slideDown("fast")}this.p.footerrow&&a(".ui-jqgrid-sdiv","#gbox_"+this.p.id).slideDown("fast");a(".ui-jqgrid-titlebar-close span",this.grid.cDiv).removeClass("ui-icon-circle-triangle-s").addClass("ui-icon-circle-triangle-n");this.p.gridstate="visible"}})},filterToolbar:function(c){c=a.extend({autosearch:true,
searchOnEnter:true,beforeSearch:null,afterSearch:null,beforeClear:null,afterClear:null,searchurl:"",stringResult:false,groupOp:"AND",defaultSearch:"bw"},c||{});return this.each(function(){function h(e,f){var j=a(e);j[0]&&jQuery.each(f,function(){this.data!==undefined?j.bind(this.type,this.data,this.fn):j.bind(this.type,this.fn)})}var b=this;if(!this.ftoolbar){var i=function(){var e={},f=0,j,d,g={},k;a.each(b.p.colModel,function(){d=this.index||this.name;switch(this.stype){case "select":k=this.searchoptions&&
this.searchoptions.sopt?this.searchoptions.sopt[0]:"eq";if(j=a("#gs_"+a.jgrid.jqID(this.name),b.grid.hDiv).val()){e[d]=j;g[d]=k;f++}else try{delete b.p.postData[d]}catch(p){}break;case "text":k=this.searchoptions&&this.searchoptions.sopt?this.searchoptions.sopt[0]:c.defaultSearch;if(j=a("#gs_"+a.jgrid.jqID(this.name),b.grid.hDiv).val()){e[d]=j;g[d]=k;f++}else try{delete b.p.postData[d]}catch(s){}}});var n=f>0?true:false;if(c.stringResult===true||b.p.datatype=="local"){var m='{"groupOp":"'+c.groupOp+
'","rules":[',q=0;a.each(e,function(p,s){if(q>0)m+=",";m+='{"field":"'+p+'",';m+='"op":"'+g[p]+'",';s+="";m+='"data":"'+s.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}';q++});m+="]}";a.extend(b.p.postData,{filters:m});a.each(["searchField","searchString","searchOper"],function(p,s){b.p.postData.hasOwnProperty(s)&&delete b.p.postData[s]})}else a.extend(b.p.postData,e);var l;if(b.p.searchurl){l=b.p.url;a(b).jqGrid("setGridParam",{url:b.p.searchurl})}var r=false;if(a.isFunction(c.beforeSearch))r=c.beforeSearch.call(b);
r||a(b).jqGrid("setGridParam",{search:n}).trigger("reloadGrid",[{page:1}]);l&&a(b).jqGrid("setGridParam",{url:l});a.isFunction(c.afterSearch)&&c.afterSearch()},o=a("<tr class='ui-search-toolbar' role='rowheader'></tr>"),t;a.each(b.p.colModel,function(){var e=this,f,j,d,g;j=a("<th role='columnheader' class='ui-state-default ui-th-column ui-th-"+b.p.direction+"'></th>");f=a("<div style='width:100%;position:relative;height:100%;padding-right:0.3em;'></div>");this.hidden===true&&a(j).css("display","none");
this.search=this.search===false?false:true;if(typeof this.stype=="undefined")this.stype="text";d=a.extend({},this.searchoptions||{});if(this.search)switch(this.stype){case "select":if(g=this.surl||d.dataUrl)a.ajax(a.extend({url:g,dataType:"html",complete:function(l){if(d.buildSelect!==undefined)(l=d.buildSelect(l))&&a(f).append(l);else a(f).append(l.responseText);d.defaultValue&&a("select",f).val(d.defaultValue);a("select",f).attr({name:e.index||e.name,id:"gs_"+e.name});d.attr&&a("select",f).attr(d.attr);
a("select",f).css({width:"100%"});d.dataInit!==undefined&&d.dataInit(a("select",f)[0]);d.dataEvents!==undefined&&h(a("select",f)[0],d.dataEvents);c.autosearch===true&&a("select",f).change(function(){i();return false});l=null}},a.jgrid.ajaxOptions,b.p.ajaxSelectOptions||{}));else{var k;if(e.searchoptions&&e.searchoptions.value)k=e.searchoptions.value;else if(e.editoptions&&e.editoptions.value)k=e.editoptions.value;if(k){g=document.createElement("select");g.style.width="100%";a(g).attr({name:e.index||
e.name,id:"gs_"+e.name});var n,m;if(typeof k==="string"){k=k.split(";");for(var q=0;q<k.length;q++){n=k[q].split(":");m=document.createElement("option");m.value=n[0];m.innerHTML=n[1];g.appendChild(m)}}else if(typeof k==="object")for(n in k)if(k.hasOwnProperty(n)){m=document.createElement("option");m.value=n;m.innerHTML=k[n];g.appendChild(m)}d.defaultValue&&a(g).val(d.defaultValue);d.attr&&a(g).attr(d.attr);d.dataInit!==undefined&&d.dataInit(g);d.dataEvents!==undefined&&h(g,d.dataEvents);a(f).append(g);
c.autosearch===true&&a(g).change(function(){i();return false})}}break;case "text":g=d.defaultValue?d.defaultValue:"";a(f).append("<input type='text' style='width:95%;padding:0px;' name='"+(e.index||e.name)+"' id='gs_"+e.name+"' value='"+g+"'/>");d.attr&&a("input",f).attr(d.attr);d.dataInit!==undefined&&d.dataInit(a("input",f)[0]);d.dataEvents!==undefined&&h(a("input",f)[0],d.dataEvents);if(c.autosearch===true)c.searchOnEnter?a("input",f).keypress(function(l){if((l.charCode?l.charCode:l.keyCode?l.keyCode:
0)==13){i();return false}return this}):a("input",f).keydown(function(l){switch(l.which){case 13:return false;case 9:case 16:case 37:case 38:case 39:case 40:case 27:break;default:t&&clearTimeout(t);t=setTimeout(function(){i()},500)}})}a(j).append(f);a(o).append(j)});a("table thead",b.grid.hDiv).append(o);this.ftoolbar=true;this.triggerToolbar=i;this.clearToolbar=function(e){var f={},j,d=0,g;e=typeof e!="boolean"?true:e;a.each(b.p.colModel,function(){j=this.searchoptions&&this.searchoptions.defaultValue?
this.searchoptions.defaultValue:"";g=this.index||this.name;switch(this.stype){case "select":var r;a("#gs_"+a.jgrid.jqID(g)+" option",b.grid.hDiv).each(function(u){if(u===0)this.selected=true;if(a(this).text()==j){this.selected=true;r=a(this).val();return false}});if(r){f[g]=r;d++}else try{delete b.p.postData[g]}catch(p){}break;case "text":a("#gs_"+a.jgrid.jqID(g),b.grid.hDiv).val(j);if(j){f[g]=j;d++}else try{delete b.p.postData[g]}catch(s){}}});var k=d>0?true:false;if(c.stringResult===true||b.p.datatype==
"local"){var n='{"groupOp":"'+c.groupOp+'","rules":[',m=0;a.each(f,function(r,p){if(m>0)n+=",";n+='{"field":"'+r+'",';n+='"op":"eq",';p+="";n+='"data":"'+p.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}';m++});n+="]}";a.extend(b.p.postData,{filters:n});a.each(["searchField","searchString","searchOper"],function(r,p){b.p.postData.hasOwnProperty(p)&&delete b.p.postData[p]})}else a.extend(b.p.postData,f);var q;if(b.p.searchurl){q=b.p.url;a(b).jqGrid("setGridParam",{url:b.p.searchurl})}var l=false;if(a.isFunction(c.beforeClear))l=
c.beforeClear.call(b);l||e&&a(b).jqGrid("setGridParam",{search:k}).trigger("reloadGrid",[{page:1}]);q&&a(b).jqGrid("setGridParam",{url:q});a.isFunction(c.afterClear)&&c.afterClear()};this.toggleToolbar=function(){var e=a("tr.ui-search-toolbar",b.grid.hDiv);e.css("display")=="none"?e.show():e.hide()}}})}})})(jQuery);
(function(a){a.extend(a.jgrid,{showModal:function(b){b.w.show()},closeModal:function(b){b.w.hide().attr("aria-hidden","true");b.o&&b.o.remove()},hideModal:function(b,c){c=a.extend({jqm:true,gb:""},c||{});if(c.onClose){var d=c.onClose(b);if(typeof d=="boolean"&&!d)return}if(a.fn.jqm&&c.jqm===true)a(b).attr("aria-hidden","true").jqmHide();else{if(c.gb!=="")try{a(".jqgrid-overlay:first",c.gb).hide()}catch(f){}a(b).hide().attr("aria-hidden","true")}},findPos:function(b){var c=0,d=0;if(b.offsetParent){do{c+=
b.offsetLeft;d+=b.offsetTop}while(b=b.offsetParent)}return[c,d]},createModal:function(b,c,d,f,g,h,j){var e=document.createElement("div"),k,m=this;j=a.extend({},j||{});k=a(d.gbox).attr("dir")=="rtl"?true:false;e.className="ui-widget ui-widget-content ui-corner-all ui-jqdialog";e.id=b.themodal;var i=document.createElement("div");i.className="ui-jqdialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix";i.id=b.modalhead;a(i).append("<span class='ui-jqdialog-title'>"+d.caption+"</span>");var q=
a("<a href='javascript:void(0)' class='ui-jqdialog-titlebar-close ui-corner-all'></a>").hover(function(){q.addClass("ui-state-hover")},function(){q.removeClass("ui-state-hover")}).append("<span class='ui-icon ui-icon-closethick'></span>");a(i).append(q);if(k){e.dir="rtl";a(".ui-jqdialog-title",i).css("float","right");a(".ui-jqdialog-titlebar-close",i).css("left","0.3em")}else{e.dir="ltr";a(".ui-jqdialog-title",i).css("float","left");a(".ui-jqdialog-titlebar-close",i).css("right","0.3em")}var l=document.createElement("div");
a(l).addClass("ui-jqdialog-content ui-widget-content").attr("id",b.modalcontent);a(l).append(c);e.appendChild(l);a(e).prepend(i);if(h===true)a("body").append(e);else typeof h=="string"?a(h).append(e):a(e).insertBefore(f);a(e).css(j);if(typeof d.jqModal==="undefined")d.jqModal=true;c={};if(a.fn.jqm&&d.jqModal===true){if(d.left===0&&d.top===0&&d.overlay){j=[];j=this.findPos(g);d.left=j[0]+4;d.top=j[1]+4}c.top=d.top+"px";c.left=d.left}else if(d.left!==0||d.top!==0){c.left=d.left;c.top=d.top+"px"}a("a.ui-jqdialog-titlebar-close",
i).click(function(){var p=a("#"+b.themodal).data("onClose")||d.onClose,o=a("#"+b.themodal).data("gbox")||d.gbox;m.hideModal("#"+b.themodal,{gb:o,jqm:d.jqModal,onClose:p});return false});if(d.width===0||!d.width)d.width=300;if(d.height===0||!d.height)d.height=200;if(!d.zIndex){f=a(f).parents("*[role=dialog]").filter(":first").css("z-index");d.zIndex=f?parseInt(f,10)+1:950}f=0;if(k&&c.left&&!h){f=a(d.gbox).width()-(!isNaN(d.width)?parseInt(d.width,10):0)-8;c.left=parseInt(c.left,10)+parseInt(f,10)}if(c.left)c.left+=
"px";a(e).css(a.extend({width:isNaN(d.width)?"auto":d.width+"px",height:isNaN(d.height)?"auto":d.height+"px",zIndex:d.zIndex,overflow:"hidden"},c)).attr({tabIndex:"-1",role:"dialog","aria-labelledby":b.modalhead,"aria-hidden":"true"});if(typeof d.drag=="undefined")d.drag=true;if(typeof d.resize=="undefined")d.resize=true;if(d.drag){a(i).css("cursor","move");if(a.fn.jqDrag)a(e).jqDrag(i);else try{a(e).draggable({handle:a("#"+i.id)})}catch(n){}}if(d.resize)if(a.fn.jqResize){a(e).append("<div class='jqResize ui-resizable-handle ui-resizable-se ui-icon ui-icon-gripsmall-diagonal-se ui-icon-grip-diagonal-se'></div>");
a("#"+b.themodal).jqResize(".jqResize",b.scrollelm?"#"+b.scrollelm:false)}else try{a(e).resizable({handles:"se, sw",alsoResize:b.scrollelm?"#"+b.scrollelm:false})}catch(r){}d.closeOnEscape===true&&a(e).keydown(function(p){if(p.which==27){p=a("#"+b.themodal).data("onClose")||d.onClose;m.hideModal(this,{gb:d.gbox,jqm:d.jqModal,onClose:p})}})},viewModal:function(b,c){c=a.extend({toTop:true,overlay:10,modal:false,overlayClass:"ui-widget-overlay",onShow:this.showModal,onHide:this.closeModal,gbox:"",jqm:true,
jqM:true},c||{});if(a.fn.jqm&&c.jqm===true)c.jqM?a(b).attr("aria-hidden","false").jqm(c).jqmShow():a(b).attr("aria-hidden","false").jqmShow();else{if(c.gbox!==""){a(".jqgrid-overlay:first",c.gbox).show();a(b).data("gbox",c.gbox)}a(b).show().attr("aria-hidden","false");try{a(":input:visible",b)[0].focus()}catch(d){}}},info_dialog:function(b,c,d,f){var g={width:290,height:"auto",dataheight:"auto",drag:true,resize:false,caption:"<b>"+b+"</b>",left:250,top:170,zIndex:1E3,jqModal:true,modal:false,closeOnEscape:true,
align:"center",buttonalign:"center",buttons:[]};a.extend(g,f||{});var h=g.jqModal,j=this;if(a.fn.jqm&&!h)h=false;b="";if(g.buttons.length>0)for(f=0;f<g.buttons.length;f++){if(typeof g.buttons[f].id=="undefined")g.buttons[f].id="info_button_"+f;b+="<a href='javascript:void(0)' id='"+g.buttons[f].id+"' class='fm-button ui-state-default ui-corner-all'>"+g.buttons[f].text+"</a>"}f=isNaN(g.dataheight)?g.dataheight:g.dataheight+"px";var e="<div id='info_id'>";e+="<div id='infocnt' style='margin:0px;padding-bottom:1em;width:100%;overflow:auto;position:relative;height:"+
f+";"+("text-align:"+g.align+";")+"'>"+c+"</div>";e+=d?"<div class='ui-widget-content ui-helper-clearfix' style='text-align:"+g.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'><a href='javascript:void(0)' id='closedialog' class='fm-button ui-state-default ui-corner-all'>"+d+"</a>"+b+"</div>":b!==""?"<div class='ui-widget-content ui-helper-clearfix' style='text-align:"+g.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'>"+
b+"</div>":"";e+="</div>";try{a("#info_dialog").attr("aria-hidden")=="false"&&this.hideModal("#info_dialog",{jqm:h});a("#info_dialog").remove()}catch(k){}this.createModal({themodal:"info_dialog",modalhead:"info_head",modalcontent:"info_content",scrollelm:"infocnt"},e,g,"","",true);b&&a.each(g.buttons,function(i){a("#"+this.id,"#info_id").bind("click",function(){g.buttons[i].onClick.call(a("#info_dialog"));return false})});a("#closedialog","#info_id").click(function(){j.hideModal("#info_dialog",{jqm:h});
return false});a(".fm-button","#info_dialog").hover(function(){a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});a.isFunction(g.beforeOpen)&&g.beforeOpen();this.viewModal("#info_dialog",{onHide:function(i){i.w.hide().remove();i.o&&i.o.remove()},modal:g.modal,jqm:h});a.isFunction(g.afterOpen)&&g.afterOpen();try{a("#info_dialog").focus()}catch(m){}},createEl:function(b,c,d,f,g){function h(l,n){a.isFunction(n.dataInit)&&n.dataInit(l);n.dataEvents&&a.each(n.dataEvents,
function(){this.data!==undefined?a(l).bind(this.type,this.data,this.fn):a(l).bind(this.type,this.fn)});return n}function j(l,n,r){var p=["dataInit","dataEvents","dataUrl","buildSelect","sopt","searchhidden","defaultValue","attr"];if(typeof r!="undefined"&&a.isArray(r))p=a.extend(p,r);a.each(n,function(o,s){a.inArray(o,p)===-1&&a(l).attr(o,s)});n.hasOwnProperty("id")||a(l).attr("id",a.jgrid.randId())}var e="";switch(b){case "textarea":e=document.createElement("textarea");if(f)c.cols||a(e).css({width:"98%"});
else if(!c.cols)c.cols=20;if(!c.rows)c.rows=2;if(d=="&nbsp;"||d=="&#160;"||d.length==1&&d.charCodeAt(0)==160)d="";e.value=d;j(e,c);c=h(e,c);a(e).attr({role:"textbox",multiline:"true"});break;case "checkbox":e=document.createElement("input");e.type="checkbox";if(c.value){b=c.value.split(":");if(d===b[0]){e.checked=true;e.defaultChecked=true}e.value=b[0];a(e).attr("offval",b[1])}else{b=d.toLowerCase();if(b.search(/(false|0|no|off|undefined)/i)<0&&b!==""){e.checked=true;e.defaultChecked=true;e.value=
d}else e.value="on";a(e).attr("offval","off")}j(e,c,["value"]);c=h(e,c);a(e).attr("role","checkbox");break;case "select":e=document.createElement("select");e.setAttribute("role","select");f=[];if(c.multiple===true){b=true;e.multiple="multiple";a(e).attr("aria-multiselectable","true")}else b=false;if(typeof c.dataUrl!="undefined")a.ajax(a.extend({url:c.dataUrl,type:"GET",dataType:"html",context:{elem:e,options:c,vl:d},success:function(l){var n=[],r=this.elem,p=this.vl,o=a.extend({},this.options),s=
o.multiple===true;if(typeof o.buildSelect!="undefined")l=o.buildSelect(l);if(l=a(l).html()){a(r).append(l);j(r,o);o=h(r,o);if(typeof o.size==="undefined")o.size=s?3:1;if(s){n=p.split(",");n=a.map(n,function(t){return a.trim(t)})}else n[0]=a.trim(p);setTimeout(function(){a("option",r).each(function(){a(this).attr("role","option");if(a.inArray(a.trim(a(this).text()),n)>-1||a.inArray(a.trim(a(this).val()),n)>-1)this.selected="selected"})},0)}}},g||{}));else if(c.value){var k;if(b){f=d.split(",");f=a.map(f,
function(l){return a.trim(l)});if(typeof c.size==="undefined")c.size=3}else c.size=1;if(typeof c.value==="function")c.value=c.value();var m,i;if(typeof c.value==="string"){m=c.value.split(";");for(k=0;k<m.length;k++){i=m[k].split(":");if(i.length>2)i[1]=a.map(i,function(l,n){if(n>0)return l}).join(":");g=document.createElement("option");g.setAttribute("role","option");g.value=i[0];g.innerHTML=i[1];if(!b&&(a.trim(i[0])==a.trim(d)||a.trim(i[1])==a.trim(d)))g.selected="selected";if(b&&(a.inArray(a.trim(i[1]),
f)>-1||a.inArray(a.trim(i[0]),f)>-1))g.selected="selected";e.appendChild(g)}}else if(typeof c.value==="object"){m=c.value;for(k in m)if(m.hasOwnProperty(k)){g=document.createElement("option");g.setAttribute("role","option");g.value=k;g.innerHTML=m[k];if(!b&&(a.trim(k)==a.trim(d)||a.trim(m[k])==a.trim(d)))g.selected="selected";if(b&&(a.inArray(a.trim(m[k]),f)>-1||a.inArray(a.trim(k),f)>-1))g.selected="selected";e.appendChild(g)}}j(e,c,["value"]);c=h(e,c)}break;case "text":case "password":case "button":k=
b=="button"?"button":"textbox";e=document.createElement("input");e.type=b;e.value=d;j(e,c);c=h(e,c);if(b!="button")if(f)c.size||a(e).css({width:"98%"});else if(!c.size)c.size=20;a(e).attr("role",k);break;case "image":case "file":e=document.createElement("input");e.type=b;j(e,c);c=h(e,c);break;case "custom":e=document.createElement("span");try{if(a.isFunction(c.custom_element))if(m=c.custom_element.call(this,d,c)){m=a(m).addClass("customelement").attr({id:c.id,name:c.name});a(e).empty().append(m)}else throw"e2";
else throw"e1";}catch(q){q=="e1"&&this.info_dialog(a.jgrid.errors.errcap,"function 'custom_element' "+a.jgrid.edit.msg.nodefined,a.jgrid.edit.bClose);q=="e2"?this.info_dialog(a.jgrid.errors.errcap,"function 'custom_element' "+a.jgrid.edit.msg.novalue,a.jgrid.edit.bClose):this.info_dialog(a.jgrid.errors.errcap,typeof q==="string"?q:q.message,a.jgrid.edit.bClose)}}return e},checkDate:function(b,c){var d={},f;b=b.toLowerCase();f=b.indexOf("/")!=-1?"/":b.indexOf("-")!=-1?"-":b.indexOf(".")!=-1?".":"/";
b=b.split(f);c=c.split(f);if(c.length!=3)return false;f=-1;for(var g,h=-1,j=-1,e=0;e<b.length;e++){g=isNaN(c[e])?0:parseInt(c[e],10);d[b[e]]=g;g=b[e];if(g.indexOf("y")!=-1)f=e;if(g.indexOf("m")!=-1)j=e;if(g.indexOf("d")!=-1)h=e}g=b[f]=="y"||b[f]=="yyyy"?4:b[f]=="yy"?2:-1;e=function(m){for(var i=1;i<=m;i++){this[i]=31;if(i==4||i==6||i==9||i==11)this[i]=30;if(i==2)this[i]=29}return this}(12);var k;if(f===-1)return false;else{k=d[b[f]].toString();if(g==2&&k.length==1)g=1;if(k.length!=g||d[b[f]]===0&&
c[f]!="00")return false}if(j===-1)return false;else{k=d[b[j]].toString();if(k.length<1||d[b[j]]<1||d[b[j]]>12)return false}if(h===-1)return false;else{k=d[b[h]].toString();if(k.length<1||d[b[h]]<1||d[b[h]]>31||d[b[j]]==2&&d[b[h]]>(d[b[f]]%4===0&&(d[b[f]]%100!==0||d[b[f]]%400===0)?29:28)||d[b[h]]>e[d[b[j]]])return false}return true},isEmpty:function(b){return b.match(/^\s+$/)||b===""?true:false},checkTime:function(b){var c=/^(\d{1,2}):(\d{2})([ap]m)?$/;if(!this.isEmpty(b))if(b=b.match(c)){if(b[3]){if(b[1]<
1||b[1]>12)return false}else if(b[1]>23)return false;if(b[2]>59)return false}else return false;return true},checkValues:function(b,c,d,f,g){var h,j;if(typeof f==="undefined")if(typeof c=="string"){f=0;for(g=d.p.colModel.length;f<g;f++)if(d.p.colModel[f].name==c){h=d.p.colModel[f].editrules;c=f;try{j=d.p.colModel[f].formoptions.label}catch(e){}break}}else{if(c>=0)h=d.p.colModel[c].editrules}else{h=f;j=g===undefined?"_":g}if(h){j||(j=d.p.colNames[c]);if(h.required===true)if(this.isEmpty(b))return[false,
j+": "+a.jgrid.edit.msg.required,""];f=h.required===false?false:true;if(h.number===true)if(!(f===false&&this.isEmpty(b)))if(isNaN(b))return[false,j+": "+a.jgrid.edit.msg.number,""];if(typeof h.minValue!="undefined"&&!isNaN(h.minValue))if(parseFloat(b)<parseFloat(h.minValue))return[false,j+": "+a.jgrid.edit.msg.minValue+" "+h.minValue,""];if(typeof h.maxValue!="undefined"&&!isNaN(h.maxValue))if(parseFloat(b)>parseFloat(h.maxValue))return[false,j+": "+a.jgrid.edit.msg.maxValue+" "+h.maxValue,""];if(h.email===
true)if(!(f===false&&this.isEmpty(b))){g=/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$/i;
if(!g.test(b))return[false,j+": "+a.jgrid.edit.msg.email,""]}if(h.integer===true)if(!(f===false&&this.isEmpty(b))){if(isNaN(b))return[false,j+": "+a.jgrid.edit.msg.integer,""];if(b%1!==0||b.indexOf(".")!=-1)return[false,j+": "+a.jgrid.edit.msg.integer,""]}if(h.date===true)if(!(f===false&&this.isEmpty(b))){c=d.p.colModel[c].formatoptions&&d.p.colModel[c].formatoptions.newformat?d.p.colModel[c].formatoptions.newformat:d.p.colModel[c].datefmt||"Y-m-d";if(!this.checkDate(c,b))return[false,j+": "+a.jgrid.edit.msg.date+
" - "+c,""]}if(h.time===true)if(!(f===false&&this.isEmpty(b)))if(!this.checkTime(b))return[false,j+": "+a.jgrid.edit.msg.date+" - hh:mm (am/pm)",""];if(h.url===true)if(!(f===false&&this.isEmpty(b))){g=/^(((https?)|(ftp)):\/\/([\-\w]+\.)+\w{2,3}(\/[%\-\w]+(\.\w{2,})?)*(([\w\-\.\?\\\/+@&#;`~=%!]*)(\.\w{2,})?)*\/?)/i;if(!g.test(b))return[false,j+": "+a.jgrid.edit.msg.url,""]}if(h.custom===true)if(!(f===false&&this.isEmpty(b)))if(a.isFunction(h.custom_func)){b=h.custom_func.call(d,b,j);return a.isArray(b)?
b:[false,a.jgrid.edit.msg.customarray,""]}else return[false,a.jgrid.edit.msg.customfcheck,""]}return[true,"",""]}})})(jQuery);
(function(a){var d={};a.jgrid.extend({searchGrid:function(f){f=a.extend({recreateFilter:false,drag:true,sField:"searchField",sValue:"searchString",sOper:"searchOper",sFilter:"filters",loadDefaults:true,beforeShowSearch:null,afterShowSearch:null,onInitializeSearch:null,afterRedraw:null,closeAfterSearch:false,closeAfterReset:false,closeOnEscape:false,multipleSearch:false,multipleGroup:false,top:0,left:0,jqModal:true,modal:false,resize:true,width:450,height:"auto",dataheight:"auto",showQuery:false,errorcheck:true,
sopt:null,stringResult:undefined,onClose:null,onSearch:null,onReset:null,toTop:true,overlay:30,columns:[],tmplNames:null,tmplFilters:null,tmplLabel:" Template: ",showOnLoad:false,layer:null},a.jgrid.search,f||{});return this.each(function(){function b(){if(a.isFunction(f.beforeShowSearch)){E=f.beforeShowSearch(a("#"+r));if(typeof E==="undefined")E=true}if(E){a.jgrid.viewModal("#"+C.themodal,{gbox:"#gbox_"+r,jqm:f.jqModal,modal:f.modal,overlay:f.overlay,toTop:f.toTop});a.isFunction(f.afterShowSearch)&&
f.afterShowSearch(a("#"+r))}}var e=this;if(e.grid){var r="fbox_"+e.p.id,E=true,C={themodal:"searchmod"+r,modalhead:"searchhd"+r,modalcontent:"searchcnt"+r,scrollelm:r},F=e.p.postData[f.sFilter];if(typeof F==="string")F=a.jgrid.parse(F);f.recreateFilter===true&&a("#"+C.themodal).remove();if(a("#"+C.themodal).html()!==null)b();else{var y=a("<span><div id='"+r+"' class='searchFilter' style='overflow:auto'></div></span>").insertBefore("#gview_"+e.p.id);if(a.isFunction(f.onInitializeSearch))f.onInitializeSearch(a("#"+
r));var m=a.extend([],e.p.colModel),s="<a href='javascript:void(0)' id='"+r+"_search' class='fm-button ui-state-default ui-corner-all fm-button-icon-right ui-reset'><span class='ui-icon ui-icon-search'></span>"+f.Find+"</a>",t="<a href='javascript:void(0)' id='"+r+"_reset' class='fm-button ui-state-default ui-corner-all fm-button-icon-left ui-search'><span class='ui-icon ui-icon-arrowreturnthick-1-w'></span>"+f.Reset+"</a>",o="",c="",u,n=false,h=-1;if(f.showQuery)o="<a href='javascript:void(0)' id='"+
r+"_query' class='fm-button ui-state-default ui-corner-all fm-button-icon-left'><span class='ui-icon ui-icon-comment'></span>Query</a>";if(f.columns.length)m=f.columns;else a.each(m,function(x,A){if(!A.label)A.label=e.p.colNames[x];if(!n){var G=typeof A.search==="undefined"?true:A.search,N=A.hidden===true;if(A.searchoptions&&A.searchoptions.searchhidden===true&&G||G&&!N){n=true;u=A.index||A.name;h=x}}});if(!F&&u||f.multipleSearch===false){var p="eq";if(h>=0&&m[h].searchoptions&&m[h].searchoptions.sopt)p=
m[h].searchoptions.sopt[0];else if(f.sopt&&f.sopt.length)p=f.sopt[0];F={groupOp:"AND",rules:[{field:u,op:p,data:""}]}}n=false;if(f.tmplNames&&f.tmplNames.length){n=true;c=f.tmplLabel;c+="<select class='ui-template'>";c+="<option value='default'>Default</option>";a.each(f.tmplNames,function(x,A){c+="<option value='"+x+"'>"+A+"</option>"});c+="</select>"}s="<table class='EditTable' style='border:0px none;margin-top:5px' id='"+r+"_2'><tbody><tr><td colspan='2'><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr><td class='EditButton' style='text-align:left'>"+
t+c+"</td><td class='EditButton'>"+o+s+"</td></tr></tbody></table>";a("#"+r).jqFilter({columns:m,filter:f.loadDefaults?F:null,showQuery:f.showQuery,errorcheck:f.errorcheck,sopt:f.sopt,groupButton:f.multipleGroup,ruleButtons:f.multipleSearch,afterRedraw:f.afterRedraw,_gridsopt:a.jgrid.search.odata,onChange:function(){this.p.showQuery&&a(".query",this).html(this.toUserFriendlyString())}});y.append(s);n&&f.tmplFilters&&f.tmplFilters.length&&a(".ui-template",y).bind("change",function(){var x=a(this).val();
x=="default"?a("#"+r).jqFilter("addFilter",F):a("#"+r).jqFilter("addFilter",f.tmplFilters[parseInt(x,10)]);return false});if(f.multipleGroup===true)f.multipleSearch=true;if(a.isFunction(f.onInitializeSearch))f.onInitializeSearch(a("#"+r));f.layer?a.jgrid.createModal(C,y,f,"#gview_"+e.p.id,a("#gbox_"+e.p.id)[0],"#"+f.layer,{position:"relative"}):a.jgrid.createModal(C,y,f,"#gview_"+e.p.id,a("#gbox_"+e.p.id)[0]);o&&a("#"+r+"_query").bind("click",function(){a(".queryresult",y).toggle();return false});
if(f.stringResult===undefined)f.stringResult=f.multipleSearch;a("#"+r+"_search").bind("click",function(){var x=a("#"+r),A={},G,N=x.jqFilter("filterData");if(f.errorcheck){x[0].hideError();f.showQuery||x.jqFilter("toSQLString");if(x[0].p.error){x[0].showError();return false}}if(f.stringResult){try{G=xmlJsonClass.toJson(N,"","",false)}catch(I){try{G=JSON.stringify(N)}catch(l){}}if(typeof G==="string"){A[f.sFilter]=G;a.each([f.sField,f.sValue,f.sOper],function(){A[this]=""})}}else if(f.multipleSearch){A[f.sFilter]=
N;a.each([f.sField,f.sValue,f.sOper],function(){A[this]=""})}else{A[f.sField]=N.rules[0].field;A[f.sValue]=N.rules[0].data;A[f.sOper]=N.rules[0].op;A[f.sFilter]=""}e.p.search=true;a.extend(e.p.postData,A);if(a.isFunction(f.onSearch))f.onSearch();a(e).trigger("reloadGrid",[{page:1}]);f.closeAfterSearch&&a.jgrid.hideModal("#"+C.themodal,{gb:"#gbox_"+e.p.id,jqm:f.jqModal,onClose:f.onClose});return false});a("#"+r+"_reset").bind("click",function(){var x={},A=a("#"+r);e.p.search=false;if(f.multipleSearch===
false)x[f.sField]=x[f.sValue]=x[f.sOper]="";else x[f.sFilter]="";A[0].resetFilter();n&&a(".ui-template",y).val("default");a.extend(e.p.postData,x);if(a.isFunction(f.onReset))f.onReset();a(e).trigger("reloadGrid",[{page:1}]);return false});b();a(".fm-button:not(.ui-state-disabled)",y).hover(function(){a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")})}}})},editGridRow:function(f,b){b=a.extend({top:0,left:0,width:300,height:"auto",dataheight:"auto",modal:false,overlay:30,
drag:true,resize:true,url:null,mtype:"POST",clearAfterAdd:true,closeAfterEdit:false,reloadAfterSubmit:true,onInitializeForm:null,beforeInitData:null,beforeShowForm:null,afterShowForm:null,beforeSubmit:null,afterSubmit:null,onclickSubmit:null,afterComplete:null,onclickPgButtons:null,afterclickPgButtons:null,editData:{},recreateForm:false,jqModal:true,closeOnEscape:false,addedrow:"first",topinfo:"",bottominfo:"",saveicon:[],closeicon:[],savekey:[false,13],navkeys:[false,38,40],checkOnSubmit:false,checkOnUpdate:false,
_savedData:{},processing:false,onClose:null,ajaxEditOptions:{},serializeEditData:null,viewPagerButtons:true},a.jgrid.edit,b||{});d[a(this)[0].p.id]=b;return this.each(function(){function e(){a("#"+h+" > tbody > tr > td > .FormElement").each(function(){var g=a(".customelement",this);if(g.length){var k=a(g[0]).attr("name");a.each(c.p.colModel,function(){if(this.name===k&&this.editoptions&&a.isFunction(this.editoptions.custom_value)){try{j[k]=this.editoptions.custom_value(a("#"+a.jgrid.jqID(k),"#"+h),
"get");if(j[k]===undefined)throw"e1";}catch(q){q==="e1"?a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+a.jgrid.edit.msg.novalue,jQuery.jgrid.edit.bClose):a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,q.message,jQuery.jgrid.edit.bClose)}return true}})}else{switch(a(this).get(0).type){case "checkbox":if(a(this).attr("checked"))j[this.name]=a(this).val();else{g=a(this).attr("offval");j[this.name]=g}break;case "select-one":j[this.name]=a("option:selected",this).val();Q[this.name]=
a("option:selected",this).text();break;case "select-multiple":j[this.name]=a(this).val();j[this.name]=j[this.name]?j[this.name].join(","):"";var v=[];a("option:selected",this).each(function(q,J){v[q]=a(J).text()});Q[this.name]=v.join(",");break;case "password":case "text":case "textarea":case "button":j[this.name]=a(this).val()}if(c.p.autoencode)j[this.name]=a.jgrid.htmlEncode(j[this.name])}});return true}function r(g,k,v,q){var J,D,B,L=0,w,O,H,T=[],M=false,aa="",S;for(S=1;S<=q;S++)aa+="<td class='CaptionTD'>&#160;</td><td class='DataTD'>&#160;</td>";
if(g!="_empty")M=a(k).jqGrid("getInd",g);a(k.p.colModel).each(function(U){J=this.name;O=(D=this.editrules&&this.editrules.edithidden===true?false:this.hidden===true?true:false)?"style='display:none'":"";if(J!=="cb"&&J!=="subgrid"&&this.editable===true&&J!=="rn"){if(M===false)w="";else if(J==k.p.ExpandColumn&&k.p.treeGrid===true)w=a("td:eq("+U+")",k.rows[M]).text();else{try{w=a.unformat(a("td:eq("+U+")",k.rows[M]),{rowId:g,colModel:this},U)}catch(fa){w=this.edittype&&this.edittype=="textarea"?a("td:eq("+
U+")",k.rows[M]).text():a("td:eq("+U+")",k.rows[M]).html()}if(!w||w=="&nbsp;"||w=="&#160;"||w.length==1&&w.charCodeAt(0)==160)w=""}var Y=a.extend({},this.editoptions||{},{id:J,name:J}),Z=a.extend({},{elmprefix:"",elmsuffix:"",rowabove:false,rowcontent:""},this.formoptions||{}),ea=parseInt(Z.rowpos,10)||L+1,ga=parseInt((parseInt(Z.colpos,10)||1)*2,10);if(g=="_empty"&&Y.defaultValue)w=a.isFunction(Y.defaultValue)?Y.defaultValue():Y.defaultValue;if(!this.edittype)this.edittype="text";if(c.p.autoencode)w=
a.jgrid.htmlDecode(w);H=a.jgrid.createEl(this.edittype,Y,w,false,a.extend({},a.jgrid.ajaxOptions,k.p.ajaxSelectOptions||{}));if(w===""&&this.edittype=="checkbox")w=a(H).attr("offval");if(w===""&&this.edittype=="select")w=a("option:eq(0)",H).text();if(d[c.p.id].checkOnSubmit||d[c.p.id].checkOnUpdate)d[c.p.id]._savedData[J]=w;a(H).addClass("FormElement");if(this.edittype=="text"||this.edittype=="textarea")a(H).addClass("ui-widget-content ui-corner-all");B=a(v).find("tr[rowpos="+ea+"]");if(Z.rowabove){Y=
a("<tr><td class='contentinfo' colspan='"+q*2+"'>"+Z.rowcontent+"</td></tr>");a(v).append(Y);Y[0].rp=ea}if(B.length===0){B=a("<tr "+O+" rowpos='"+ea+"'></tr>").addClass("FormData").attr("id","tr_"+J);a(B).append(aa);a(v).append(B);B[0].rp=ea}a("td:eq("+(ga-2)+")",B[0]).html(typeof Z.label==="undefined"?k.p.colNames[U]:Z.label);a("td:eq("+(ga-1)+")",B[0]).append(Z.elmprefix).append(H).append(Z.elmsuffix);T[L]=U;L++}});if(L>0){S=a("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+
(q*2-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='"+k.p.id+"_id' value='"+g+"'/></td></tr>");S[0].rp=L+999;a(v).append(S);if(d[c.p.id].checkOnSubmit||d[c.p.id].checkOnUpdate)d[c.p.id]._savedData[k.p.id+"_id"]=g}return T}function E(g,k,v){var q,J=0,D,B,L,w,O;if(d[c.p.id].checkOnSubmit||d[c.p.id].checkOnUpdate){d[c.p.id]._savedData={};d[c.p.id]._savedData[k.p.id+"_id"]=g}var H=k.p.colModel;if(g=="_empty"){a(H).each(function(){q=this.name;L=a.extend({},this.editoptions||
{});if((B=a("#"+a.jgrid.jqID(q),"#"+v))&&B.length&&B[0]!==null){w="";if(L.defaultValue){w=a.isFunction(L.defaultValue)?L.defaultValue():L.defaultValue;if(B[0].type=="checkbox"){O=w.toLowerCase();if(O.search(/(false|0|no|off|undefined)/i)<0&&O!==""){B[0].checked=true;B[0].defaultChecked=true;B[0].value=w}else B.attr({checked:"",defaultChecked:""})}else B.val(w)}else if(B[0].type=="checkbox"){B[0].checked=false;B[0].defaultChecked=false;w=a(B).attr("offval")}else if(B[0].type&&B[0].type.substr(0,6)==
"select")B[0].selectedIndex=0;else B.val(w);if(d[c.p.id].checkOnSubmit===true||d[c.p.id].checkOnUpdate)d[c.p.id]._savedData[q]=w}});a("#id_g","#"+v).val(g)}else{var T=a(k).jqGrid("getInd",g,true);if(T){a("td",T).each(function(M){q=H[M].name;if(q!=="cb"&&q!=="subgrid"&&q!=="rn"&&H[M].editable===true){if(q==k.p.ExpandColumn&&k.p.treeGrid===true)D=a(this).text();else try{D=a.unformat(a(this),{rowId:g,colModel:H[M]},M)}catch(aa){D=H[M].edittype=="textarea"?a(this).text():a(this).html()}if(c.p.autoencode)D=
a.jgrid.htmlDecode(D);if(d[c.p.id].checkOnSubmit===true||d[c.p.id].checkOnUpdate)d[c.p.id]._savedData[q]=D;q=a.jgrid.jqID(q);switch(H[M].edittype){case "password":case "text":case "button":case "image":case "textarea":if(D=="&nbsp;"||D=="&#160;"||D.length==1&&D.charCodeAt(0)==160)D="";a("#"+q,"#"+v).val(D);break;case "select":var S=D.split(",");S=a.map(S,function(fa){return a.trim(fa)});a("#"+q+" option","#"+v).each(function(){this.selected=!H[M].editoptions.multiple&&(S[0]==a.trim(a(this).text())||
S[0]==a.trim(a(this).val()))?true:H[M].editoptions.multiple?a.inArray(a.trim(a(this).text()),S)>-1||a.inArray(a.trim(a(this).val()),S)>-1?true:false:false});break;case "checkbox":D+="";if(H[M].editoptions&&H[M].editoptions.value)if(H[M].editoptions.value.split(":")[0]==D){a("#"+q,"#"+v).attr("checked",true);a("#"+q,"#"+v).attr("defaultChecked",true)}else{a("#"+q,"#"+v).attr("checked",false);a("#"+q,"#"+v).attr("defaultChecked","")}else{D=D.toLowerCase();if(D.search(/(false|0|no|off|undefined)/i)<
0&&D!==""){a("#"+q,"#"+v).attr("checked",true);a("#"+q,"#"+v).attr("defaultChecked",true)}else{a("#"+q,"#"+v).attr("checked",false);a("#"+q,"#"+v).attr("defaultChecked","")}}break;case "custom":try{if(H[M].editoptions&&a.isFunction(H[M].editoptions.custom_value))H[M].editoptions.custom_value(a("#"+q,"#"+v),"set",D);else throw"e1";}catch(U){U=="e1"?a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+a.jgrid.edit.msg.nodefined,jQuery.jgrid.edit.bClose):a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,
U.message,jQuery.jgrid.edit.bClose)}}J++}});J>0&&a("#id_g","#"+h).val(g)}}}function C(){var g,k=[true,"",""],v={},q=c.p.prmNames,J,D,B,L;if(a.isFunction(d[c.p.id].beforeCheckValues)){var w=d[c.p.id].beforeCheckValues(j,a("#"+n),j[c.p.id+"_id"]=="_empty"?q.addoper:q.editoper);if(w&&typeof w==="object")j=w}for(B in j)if(j.hasOwnProperty(B)){k=a.jgrid.checkValues(j[B],B,c);if(k[0]===false)break}y();if(k[0]){if(a.isFunction(d[c.p.id].onclickSubmit))v=d[c.p.id].onclickSubmit(d[c.p.id],j)||{};if(a.isFunction(d[c.p.id].beforeSubmit))k=
d[c.p.id].beforeSubmit(j,a("#"+n))}if(k[0]&&!d[c.p.id].processing){d[c.p.id].processing=true;a("#sData","#"+h+"_2").addClass("ui-state-active");D=q.oper;J=q.id;j[D]=a.trim(j[c.p.id+"_id"])=="_empty"?q.addoper:q.editoper;if(j[D]!=q.addoper)j[J]=j[c.p.id+"_id"];else if(j[J]===undefined)j[J]=j[c.p.id+"_id"];delete j[c.p.id+"_id"];j=a.extend(j,d.editData,v);if(c.p.treeGrid===true){if(j[D]==q.addoper){L=a(c).jqGrid("getGridParam","selrow");j[c.p.treeGridModel=="adjacency"?c.p.treeReader.parent_id_field:
"parent_id"]=L}for(i in c.p.treeReader){v=c.p.treeReader[i];if(j.hasOwnProperty(v))j[D]==q.addoper&&i==="parent_id_field"||delete j[v]}}v=a.extend({url:d[c.p.id].url?d[c.p.id].url:a(c).jqGrid("getGridParam","editurl"),type:d[c.p.id].mtype,data:a.isFunction(d[c.p.id].serializeEditData)?d[c.p.id].serializeEditData(j):j,complete:function(O,H){if(H!="success"){k[0]=false;k[1]=a.isFunction(d[c.p.id].errorTextFormat)?d[c.p.id].errorTextFormat(O):H+" Status: '"+O.statusText+"'. Error code: "+O.status}else if(a.isFunction(d[c.p.id].afterSubmit))k=
d[c.p.id].afterSubmit(O,j);if(k[0]===false){a("#FormError>td","#"+h).html(k[1]);a("#FormError","#"+h).show()}else{a.each(c.p.colModel,function(){if(Q[this.name]&&this.formatter&&this.formatter=="select")try{delete Q[this.name]}catch(aa){}});j=a.extend(j,Q);c.p.autoencode&&a.each(j,function(aa,S){j[aa]=a.jgrid.htmlDecode(S)});if(j[D]==q.addoper){k[2]||(k[2]=a.jgrid.randId());j[J]=k[2];if(d[c.p.id].closeAfterAdd){if(d[c.p.id].reloadAfterSubmit)a(c).trigger("reloadGrid");else if(c.p.treeGrid===true)a(c).jqGrid("addChildNode",
k[2],L,j);else{a(c).jqGrid("addRowData",k[2],j,b.addedrow);a(c).jqGrid("setSelection",k[2])}a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose})}else if(d[c.p.id].clearAfterAdd){if(d[c.p.id].reloadAfterSubmit)a(c).trigger("reloadGrid");else c.p.treeGrid===true?a(c).jqGrid("addChildNode",k[2],L,j):a(c).jqGrid("addRowData",k[2],j,b.addedrow);E("_empty",c,n)}else if(d[c.p.id].reloadAfterSubmit)a(c).trigger("reloadGrid");else c.p.treeGrid===true?a(c).jqGrid("addChildNode",
k[2],L,j):a(c).jqGrid("addRowData",k[2],j,b.addedrow)}else{if(d[c.p.id].reloadAfterSubmit){a(c).trigger("reloadGrid");d[c.p.id].closeAfterEdit||setTimeout(function(){a(c).jqGrid("setSelection",j[J])},1E3)}else c.p.treeGrid===true?a(c).jqGrid("setTreeRow",j[J],j):a(c).jqGrid("setRowData",j[J],j);d[c.p.id].closeAfterEdit&&a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose})}if(a.isFunction(d[c.p.id].afterComplete)){g=O;setTimeout(function(){d[c.p.id].afterComplete(g,
j,a("#"+n));g=null},500)}if(d[c.p.id].checkOnSubmit||d[c.p.id].checkOnUpdate){a("#"+n).data("disabled",false);if(d[c.p.id]._savedData[c.p.id+"_id"]!="_empty")for(var T in d[c.p.id]._savedData)if(j[T])d[c.p.id]._savedData[T]=j[T]}}d[c.p.id].processing=false;a("#sData","#"+h+"_2").removeClass("ui-state-active");try{a(":input:visible","#"+n)[0].focus()}catch(M){}}},a.jgrid.ajaxOptions,d[c.p.id].ajaxEditOptions);if(!v.url&&!d[c.p.id].useDataProxy)if(a.isFunction(c.p.dataProxy))d[c.p.id].useDataProxy=
true;else{k[0]=false;k[1]+=" "+a.jgrid.errors.nourl}if(k[0])d[c.p.id].useDataProxy?c.p.dataProxy.call(c,v,"set_"+c.p.id):a.ajax(v)}if(k[0]===false){a("#FormError>td","#"+h).html(k[1]);a("#FormError","#"+h).show()}}function F(g,k){var v=false,q;for(q in g)if(g[q]!=k[q]){v=true;break}return v}function y(){a.each(c.p.colModel,function(g,k){if(k.editoptions&&k.editoptions.NullIfEmpty===true)if(j.hasOwnProperty(k.name)&&j[k.name]=="")j[k.name]="null"})}function m(){var g=true;a("#FormError","#"+h).hide();
if(d[c.p.id].checkOnUpdate){j={};Q={};e();P=a.extend({},j,Q);if(V=F(P,d[c.p.id]._savedData)){a("#"+n).data("disabled",true);a(".confirm","#"+p.themodal).show();g=false}}return g}function s(){if(f!=="_empty"&&typeof c.p.savedRow!=="undefined"&&c.p.savedRow.length>0&&a.isFunction(a.fn.jqGrid.restoreRow))for(var g=0;g<c.p.savedRow.length;g++)if(c.p.savedRow[g].id==f){a(c).jqGrid("restoreRow",f);break}}function t(g,k){g===0?a("#pData","#"+h+"_2").addClass("ui-state-disabled"):a("#pData","#"+h+"_2").removeClass("ui-state-disabled");
g==k?a("#nData","#"+h+"_2").addClass("ui-state-disabled"):a("#nData","#"+h+"_2").removeClass("ui-state-disabled")}function o(){var g=a(c).jqGrid("getDataIDs"),k=a("#id_g","#"+h).val();return[a.inArray(k,g),g]}var c=this;if(c.grid&&f){var u=c.p.id,n="FrmGrid_"+u,h="TblGrid_"+u,p={themodal:"editmod"+u,modalhead:"edithd"+u,modalcontent:"editcnt"+u,scrollelm:n},x=a.isFunction(d[c.p.id].beforeShowForm)?d[c.p.id].beforeShowForm:false,A=a.isFunction(d[c.p.id].afterShowForm)?d[c.p.id].afterShowForm:false,
G=a.isFunction(d[c.p.id].beforeInitData)?d[c.p.id].beforeInitData:false,N=a.isFunction(d[c.p.id].onInitializeForm)?d[c.p.id].onInitializeForm:false,I=true,l=1,z=0,j,Q,P,V;if(f==="new"){f="_empty";b.caption=d[c.p.id].addCaption}else b.caption=d[c.p.id].editCaption;b.recreateForm===true&&a("#"+p.themodal).html()!==null&&a("#"+p.themodal).remove();var R=true;if(b.checkOnUpdate&&b.jqModal&&!b.modal)R=false;if(a("#"+p.themodal).html()!==null){if(G){I=G(a("#"+n));if(typeof I=="undefined")I=true}if(I===
false)return;s();a(".ui-jqdialog-title","#"+p.modalhead).html(b.caption);a("#FormError","#"+h).hide();if(d[c.p.id].topinfo){a(".topinfo","#"+h+"_2").html(d[c.p.id].topinfo);a(".tinfo","#"+h+"_2").show()}else a(".tinfo","#"+h+"_2").hide();if(d[c.p.id].bottominfo){a(".bottominfo","#"+h+"_2").html(d[c.p.id].bottominfo);a(".binfo","#"+h+"_2").show()}else a(".binfo","#"+h+"_2").hide();E(f,c,n);f=="_empty"||!d[c.p.id].viewPagerButtons?a("#pData, #nData","#"+h+"_2").hide():a("#pData, #nData","#"+h+"_2").show();
if(d[c.p.id].processing===true){d[c.p.id].processing=false;a("#sData","#"+h+"_2").removeClass("ui-state-active")}if(a("#"+n).data("disabled")===true){a(".confirm","#"+p.themodal).hide();a("#"+n).data("disabled",false)}x&&x(a("#"+n));a("#"+p.themodal).data("onClose",d[c.p.id].onClose);a.jgrid.viewModal("#"+p.themodal,{gbox:"#gbox_"+u,jqm:b.jqModal,jqM:false,overlay:b.overlay,modal:b.modal});R||a(".jqmOverlay").click(function(){if(!m())return false;a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,
onClose:d[c.p.id].onClose});return false});A&&A(a("#"+n))}else{var K=isNaN(b.dataheight)?b.dataheight:b.dataheight+"px";K=a("<form name='FormPost' id='"+n+"' class='FormGrid' onSubmit='return false;' style='width:100%;overflow:auto;position:relative;height:"+K+";'></form>").data("disabled",false);var W=a("<table id='"+h+"' class='EditTable' cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>");if(G){I=G(a("#"+n));if(typeof I=="undefined")I=true}if(I===false)return;s();a(c.p.colModel).each(function(){var g=
this.formoptions;l=Math.max(l,g?g.colpos||0:0);z=Math.max(z,g?g.rowpos||0:0)});a(K).append(W);G=a("<tr id='FormError' style='display:none'><td class='ui-state-error' colspan='"+l*2+"'></td></tr>");G[0].rp=0;a(W).append(G);G=a("<tr style='display:none' class='tinfo'><td class='topinfo' colspan='"+l*2+"'>"+d[c.p.id].topinfo+"</td></tr>");G[0].rp=0;a(W).append(G);I=(G=c.p.direction=="rtl"?true:false)?"nData":"pData";var X=G?"pData":"nData";r(f,c,W,l);I="<a href='javascript:void(0)' id='"+I+"' class='fm-button ui-state-default ui-corner-left'><span class='ui-icon ui-icon-triangle-1-w'></span></a>";
X="<a href='javascript:void(0)' id='"+X+"' class='fm-button ui-state-default ui-corner-right'><span class='ui-icon ui-icon-triangle-1-e'></span></a>";var ba="<a href='javascript:void(0)' id='sData' class='fm-button ui-state-default ui-corner-all'>"+b.bSubmit+"</a>",$="<a href='javascript:void(0)' id='cData' class='fm-button ui-state-default ui-corner-all'>"+b.bCancel+"</a>";I="<table border='0' cellspacing='0' cellpadding='0' class='EditTable' id='"+h+"_2'><tbody><tr><td colspan='2'><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr id='Act_Buttons'><td class='navButton'>"+
(G?X+I:I+X)+"</td><td class='EditButton'>"+ba+$+"</td></tr>";I+="<tr style='display:none' class='binfo'><td class='bottominfo' colspan='2'>"+d[c.p.id].bottominfo+"</td></tr>";I+="</tbody></table>";if(z>0){var ca=[];a.each(a(W)[0].rows,function(g,k){ca[g]=k});ca.sort(function(g,k){if(g.rp>k.rp)return 1;if(g.rp<k.rp)return-1;return 0});a.each(ca,function(g,k){a("tbody",W).append(k)})}b.gbox="#gbox_"+u;var da=false;if(b.closeOnEscape===true){b.closeOnEscape=false;da=true}K=a("<span></span>").append(K).append(I);
a.jgrid.createModal(p,K,b,"#gview_"+c.p.id,a("#gbox_"+c.p.id)[0]);if(G){a("#pData, #nData","#"+h+"_2").css("float","right");a(".EditButton","#"+h+"_2").css("text-align","left")}d[c.p.id].topinfo&&a(".tinfo","#"+h+"_2").show();d[c.p.id].bottominfo&&a(".binfo","#"+h+"_2").show();I=K=null;a("#"+p.themodal).keydown(function(g){var k=g.target;if(a("#"+n).data("disabled")===true)return false;if(d[c.p.id].savekey[0]===true&&g.which==d[c.p.id].savekey[1])if(k.tagName!="TEXTAREA"){a("#sData","#"+h+"_2").trigger("click");
return false}if(g.which===27){if(!m())return false;da&&a.jgrid.hideModal(this,{gb:b.gbox,jqm:b.jqModal,onClose:d[c.p.id].onClose});return false}if(d[c.p.id].navkeys[0]===true){if(a("#id_g","#"+h).val()=="_empty")return true;if(g.which==d[c.p.id].navkeys[1]){a("#pData","#"+h+"_2").trigger("click");return false}if(g.which==d[c.p.id].navkeys[2]){a("#nData","#"+h+"_2").trigger("click");return false}}});if(b.checkOnUpdate){a("a.ui-jqdialog-titlebar-close span","#"+p.themodal).removeClass("jqmClose");a("a.ui-jqdialog-titlebar-close",
"#"+p.themodal).unbind("click").click(function(){if(!m())return false;a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose});return false})}b.saveicon=a.extend([true,"left","ui-icon-disk"],b.saveicon);b.closeicon=a.extend([true,"left","ui-icon-close"],b.closeicon);if(b.saveicon[0]===true)a("#sData","#"+h+"_2").addClass(b.saveicon[1]=="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+b.saveicon[2]+"'></span>");if(b.closeicon[0]===
true)a("#cData","#"+h+"_2").addClass(b.closeicon[1]=="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+b.closeicon[2]+"'></span>");if(d[c.p.id].checkOnSubmit||d[c.p.id].checkOnUpdate){ba="<a href='javascript:void(0)' id='sNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+b.bYes+"</a>";X="<a href='javascript:void(0)' id='nNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+b.bNo+"</a>";$="<a href='javascript:void(0)' id='cNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+
b.bExit+"</a>";K=b.zIndex||999;K++;a("<div class='ui-widget-overlay jqgrid-overlay confirm' style='z-index:"+K+";display:none;'>&#160;"+(a.browser.msie&&a.browser.version==6?'<iframe style="display:block;position:absolute;z-index:-1;filter:Alpha(Opacity=\'0\');" src="javascript:false;"></iframe>':"")+"</div><div class='confirm ui-widget-content ui-jqconfirm' style='z-index:"+(K+1)+"'>"+b.saveData+"<br/><br/>"+ba+X+$+"</div>").insertAfter("#"+n);a("#sNew","#"+p.themodal).click(function(){C();a("#"+
n).data("disabled",false);a(".confirm","#"+p.themodal).hide();return false});a("#nNew","#"+p.themodal).click(function(){a(".confirm","#"+p.themodal).hide();a("#"+n).data("disabled",false);setTimeout(function(){a(":input","#"+n)[0].focus()},0);return false});a("#cNew","#"+p.themodal).click(function(){a(".confirm","#"+p.themodal).hide();a("#"+n).data("disabled",false);a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose});return false})}N&&N(a("#"+n));f=="_empty"||
!d[c.p.id].viewPagerButtons?a("#pData,#nData","#"+h+"_2").hide():a("#pData,#nData","#"+h+"_2").show();x&&x(a("#"+n));a("#"+p.themodal).data("onClose",d[c.p.id].onClose);a.jgrid.viewModal("#"+p.themodal,{gbox:"#gbox_"+u,jqm:b.jqModal,overlay:b.overlay,modal:b.modal});R||a(".jqmOverlay").click(function(){if(!m())return false;a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose});return false});A&&A(a("#"+n));a(".fm-button","#"+p.themodal).hover(function(){a(this).addClass("ui-state-hover")},
function(){a(this).removeClass("ui-state-hover")});a("#sData","#"+h+"_2").click(function(){j={};Q={};a("#FormError","#"+h).hide();e();if(j[c.p.id+"_id"]=="_empty")C();else if(b.checkOnSubmit===true){P=a.extend({},j,Q);if(V=F(P,d[c.p.id]._savedData)){a("#"+n).data("disabled",true);a(".confirm","#"+p.themodal).show()}else C()}else C();return false});a("#cData","#"+h+"_2").click(function(){if(!m())return false;a.jgrid.hideModal("#"+p.themodal,{gb:"#gbox_"+u,jqm:b.jqModal,onClose:d[c.p.id].onClose});
return false});a("#nData","#"+h+"_2").click(function(){if(!m())return false;a("#FormError","#"+h).hide();var g=o();g[0]=parseInt(g[0],10);if(g[0]!=-1&&g[1][g[0]+1]){if(a.isFunction(b.onclickPgButtons))b.onclickPgButtons("next",a("#"+n),g[1][g[0]]);E(g[1][g[0]+1],c,n);a(c).jqGrid("setSelection",g[1][g[0]+1]);a.isFunction(b.afterclickPgButtons)&&b.afterclickPgButtons("next",a("#"+n),g[1][g[0]+1]);t(g[0]+1,g[1].length-1)}return false});a("#pData","#"+h+"_2").click(function(){if(!m())return false;a("#FormError",
"#"+h).hide();var g=o();if(g[0]!=-1&&g[1][g[0]-1]){if(a.isFunction(b.onclickPgButtons))b.onclickPgButtons("prev",a("#"+n),g[1][g[0]]);E(g[1][g[0]-1],c,n);a(c).jqGrid("setSelection",g[1][g[0]-1]);a.isFunction(b.afterclickPgButtons)&&b.afterclickPgButtons("prev",a("#"+n),g[1][g[0]-1]);t(g[0]-1,g[1].length-1)}return false})}x=o();t(x[0],x[1].length-1)}})},viewGridRow:function(f,b){b=a.extend({top:0,left:0,width:0,height:"auto",dataheight:"auto",modal:false,overlay:30,drag:true,resize:true,jqModal:true,
closeOnEscape:false,labelswidth:"30%",closeicon:[],navkeys:[false,38,40],onClose:null,beforeShowForm:null,beforeInitData:null,viewPagerButtons:true},a.jgrid.view,b||{});return this.each(function(){function e(){if(b.closeOnEscape===true||b.navkeys[0]===true)setTimeout(function(){a(".ui-jqdialog-titlebar-close","#"+o.modalhead).focus()},0)}function r(l,z,j,Q){for(var P,V,R,K=0,W,X,ba=[],$=false,ca="<td class='CaptionTD form-view-label ui-widget-content' width='"+b.labelswidth+"'>&#160;</td><td class='DataTD form-view-data ui-helper-reset ui-widget-content'>&#160;</td>",
da="",g=["integer","number","currency"],k=0,v=0,q,J,D,B=1;B<=Q;B++)da+=B==1?ca:"<td class='CaptionTD form-view-label ui-widget-content'>&#160;</td><td class='DataTD form-view-data ui-widget-content'>&#160;</td>";a(z.p.colModel).each(function(){V=this.editrules&&this.editrules.edithidden===true?false:this.hidden===true?true:false;if(!V&&this.align==="right")if(this.formatter&&a.inArray(this.formatter,g)!==-1)k=Math.max(k,parseInt(this.width,10));else v=Math.max(v,parseInt(this.width,10))});q=k!==0?
k:v!==0?v:0;$=a(z).jqGrid("getInd",l);a(z.p.colModel).each(function(L){P=this.name;J=false;X=(V=this.editrules&&this.editrules.edithidden===true?false:this.hidden===true?true:false)?"style='display:none'":"";D=typeof this.viewable!="boolean"?true:this.viewable;if(P!=="cb"&&P!=="subgrid"&&P!=="rn"&&D){W=$===false?"":P==z.p.ExpandColumn&&z.p.treeGrid===true?a("td:eq("+L+")",z.rows[$]).text():a("td:eq("+L+")",z.rows[$]).html();J=this.align==="right"&&q!==0?true:false;a.extend({},this.editoptions||{},
{id:P,name:P});var w=a.extend({},{rowabove:false,rowcontent:""},this.formoptions||{}),O=parseInt(w.rowpos,10)||K+1,H=parseInt((parseInt(w.colpos,10)||1)*2,10);if(w.rowabove){var T=a("<tr><td class='contentinfo' colspan='"+Q*2+"'>"+w.rowcontent+"</td></tr>");a(j).append(T);T[0].rp=O}R=a(j).find("tr[rowpos="+O+"]");if(R.length===0){R=a("<tr "+X+" rowpos='"+O+"'></tr>").addClass("FormData").attr("id","trv_"+P);a(R).append(da);a(j).append(R);R[0].rp=O}a("td:eq("+(H-2)+")",R[0]).html("<b>"+(typeof w.label===
"undefined"?z.p.colNames[L]:w.label)+"</b>");a("td:eq("+(H-1)+")",R[0]).append("<span>"+W+"</span>").attr("id","v_"+P);J&&a("td:eq("+(H-1)+") span",R[0]).css({"text-align":"right",width:q+"px"});ba[K]=L;K++}});if(K>0){l=a("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+(Q*2-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='id' value='"+l+"'/></td></tr>");l[0].rp=K+99;a(j).append(l)}return ba}function E(l,z){var j,Q,P=0,V,R;if(R=a(z).jqGrid("getInd",
l,true)){a("td",R).each(function(K){j=z.p.colModel[K].name;Q=z.p.colModel[K].editrules&&z.p.colModel[K].editrules.edithidden===true?false:z.p.colModel[K].hidden===true?true:false;if(j!=="cb"&&j!=="subgrid"&&j!=="rn"){V=j==z.p.ExpandColumn&&z.p.treeGrid===true?a(this).text():a(this).html();a.extend({},z.p.colModel[K].editoptions||{});j=a.jgrid.jqID("v_"+j);a("#"+j+" span","#"+t).html(V);Q&&a("#"+j,"#"+t).parents("tr:first").hide();P++}});P>0&&a("#id_g","#"+t).val(l)}}function C(l,z){l===0?a("#pData",
"#"+t+"_2").addClass("ui-state-disabled"):a("#pData","#"+t+"_2").removeClass("ui-state-disabled");l==z?a("#nData","#"+t+"_2").addClass("ui-state-disabled"):a("#nData","#"+t+"_2").removeClass("ui-state-disabled")}function F(){var l=a(y).jqGrid("getDataIDs"),z=a("#id_g","#"+t).val();return[a.inArray(z,l),l]}var y=this;if(y.grid&&f){if(!b.imgpath)b.imgpath=y.p.imgpath;var m=y.p.id,s="ViewGrid_"+m,t="ViewTbl_"+m,o={themodal:"viewmod"+m,modalhead:"viewhd"+m,modalcontent:"viewcnt"+m,scrollelm:s},c=a.isFunction(b.beforeInitData)?
b.beforeInitData:false,u=true,n=1,h=0;if(a("#"+o.themodal).html()!==null){if(c){u=c(a("#"+s));if(typeof u=="undefined")u=true}if(u===false)return;a(".ui-jqdialog-title","#"+o.modalhead).html(b.caption);a("#FormError","#"+t).hide();E(f,y);a.isFunction(b.beforeShowForm)&&b.beforeShowForm(a("#"+s));a.jgrid.viewModal("#"+o.themodal,{gbox:"#gbox_"+m,jqm:b.jqModal,jqM:false,overlay:b.overlay,modal:b.modal});e()}else{var p=isNaN(b.dataheight)?b.dataheight:b.dataheight+"px";p=a("<form name='FormPost' id='"+
s+"' class='FormGrid' style='width:100%;overflow:auto;position:relative;height:"+p+";'></form>");var x=a("<table id='"+t+"' class='EditTable' cellspacing='1' cellpadding='2' border='0' style='table-layout:fixed'><tbody></tbody></table>");if(c){u=c(a("#"+s));if(typeof u=="undefined")u=true}if(u===false)return;a(y.p.colModel).each(function(){var l=this.formoptions;n=Math.max(n,l?l.colpos||0:0);h=Math.max(h,l?l.rowpos||0:0)});a(p).append(x);r(f,y,x,n);c=y.p.direction=="rtl"?true:false;u="<a href='javascript:void(0)' id='"+
(c?"nData":"pData")+"' class='fm-button ui-state-default ui-corner-left'><span class='ui-icon ui-icon-triangle-1-w'></span></a>";var A="<a href='javascript:void(0)' id='"+(c?"pData":"nData")+"' class='fm-button ui-state-default ui-corner-right'><span class='ui-icon ui-icon-triangle-1-e'></span></a>",G="<a href='javascript:void(0)' id='cData' class='fm-button ui-state-default ui-corner-all'>"+b.bClose+"</a>";if(h>0){var N=[];a.each(a(x)[0].rows,function(l,z){N[l]=z});N.sort(function(l,z){if(l.rp>z.rp)return 1;
if(l.rp<z.rp)return-1;return 0});a.each(N,function(l,z){a("tbody",x).append(z)})}b.gbox="#gbox_"+m;var I=false;if(b.closeOnEscape===true){b.closeOnEscape=false;I=true}p=a("<span></span>").append(p).append("<table border='0' class='EditTable' id='"+t+"_2'><tbody><tr id='Act_Buttons'><td class='navButton' width='"+b.labelswidth+"'>"+(c?A+u:u+A)+"</td><td class='EditButton'>"+G+"</td></tr></tbody></table>");a.jgrid.createModal(o,p,b,"#gview_"+y.p.id,a("#gview_"+y.p.id)[0]);if(c){a("#pData, #nData","#"+
t+"_2").css("float","right");a(".EditButton","#"+t+"_2").css("text-align","left")}b.viewPagerButtons||a("#pData, #nData","#"+t+"_2").hide();p=null;a("#"+o.themodal).keydown(function(l){if(l.which===27){I&&a.jgrid.hideModal(this,{gb:b.gbox,jqm:b.jqModal,onClose:b.onClose});return false}if(b.navkeys[0]===true){if(l.which===b.navkeys[1]){a("#pData","#"+t+"_2").trigger("click");return false}if(l.which===b.navkeys[2]){a("#nData","#"+t+"_2").trigger("click");return false}}});b.closeicon=a.extend([true,
"left","ui-icon-close"],b.closeicon);if(b.closeicon[0]===true)a("#cData","#"+t+"_2").addClass(b.closeicon[1]=="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+b.closeicon[2]+"'></span>");a.isFunction(b.beforeShowForm)&&b.beforeShowForm(a("#"+s));a.jgrid.viewModal("#"+o.themodal,{gbox:"#gbox_"+m,jqm:b.jqModal,modal:b.modal});a(".fm-button:not(.ui-state-disabled)","#"+t+"_2").hover(function(){a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});
e();a("#cData","#"+t+"_2").click(function(){a.jgrid.hideModal("#"+o.themodal,{gb:"#gbox_"+m,jqm:b.jqModal,onClose:b.onClose});return false});a("#nData","#"+t+"_2").click(function(){a("#FormError","#"+t).hide();var l=F();l[0]=parseInt(l[0],10);if(l[0]!=-1&&l[1][l[0]+1]){if(a.isFunction(b.onclickPgButtons))b.onclickPgButtons("next",a("#"+s),l[1][l[0]]);E(l[1][l[0]+1],y);a(y).jqGrid("setSelection",l[1][l[0]+1]);a.isFunction(b.afterclickPgButtons)&&b.afterclickPgButtons("next",a("#"+s),l[1][l[0]+1]);
C(l[0]+1,l[1].length-1)}e();return false});a("#pData","#"+t+"_2").click(function(){a("#FormError","#"+t).hide();var l=F();if(l[0]!=-1&&l[1][l[0]-1]){if(a.isFunction(b.onclickPgButtons))b.onclickPgButtons("prev",a("#"+s),l[1][l[0]]);E(l[1][l[0]-1],y);a(y).jqGrid("setSelection",l[1][l[0]-1]);a.isFunction(b.afterclickPgButtons)&&b.afterclickPgButtons("prev",a("#"+s),l[1][l[0]-1]);C(l[0]-1,l[1].length-1)}e();return false})}p=F();C(p[0],p[1].length-1)}})},delGridRow:function(f,b){b=a.extend({top:0,left:0,
width:240,height:"auto",dataheight:"auto",modal:false,overlay:30,drag:true,resize:true,url:"",mtype:"POST",reloadAfterSubmit:true,beforeShowForm:null,beforeInitData:null,afterShowForm:null,beforeSubmit:null,onclickSubmit:null,afterSubmit:null,jqModal:true,closeOnEscape:false,delData:{},delicon:[],cancelicon:[],onClose:null,ajaxDelOptions:{},processing:false,serializeDelData:null,useDataProxy:false},a.jgrid.del,b||{});d[a(this)[0].p.id]=b;return this.each(function(){var e=this;if(e.grid)if(f){var r=
a.isFunction(d[e.p.id].beforeShowForm),E=a.isFunction(d[e.p.id].afterShowForm),C=a.isFunction(d[e.p.id].beforeInitData)?d[e.p.id].beforeInitData:false,F=e.p.id,y={},m=true,s="DelTbl_"+F,t,o,c,u,n={themodal:"delmod"+F,modalhead:"delhd"+F,modalcontent:"delcnt"+F,scrollelm:s};if(jQuery.isArray(f))f=f.join();if(a("#"+n.themodal).html()!==null){if(C){m=C(a("#"+s));if(typeof m=="undefined")m=true}if(m===false)return;a("#DelData>td","#"+s).text(f);a("#DelError","#"+s).hide();if(d[e.p.id].processing===true){d[e.p.id].processing=
false;a("#dData","#"+s).removeClass("ui-state-active")}r&&d[e.p.id].beforeShowForm(a("#"+s));a.jgrid.viewModal("#"+n.themodal,{gbox:"#gbox_"+F,jqm:d[e.p.id].jqModal,jqM:false,overlay:d[e.p.id].overlay,modal:d[e.p.id].modal})}else{var h=isNaN(d[e.p.id].dataheight)?d[e.p.id].dataheight:d[e.p.id].dataheight+"px";h="<div id='"+s+"' class='formdata' style='width:100%;overflow:auto;position:relative;height:"+h+";'>";h+="<table class='DelTable'><tbody>";h+="<tr id='DelError' style='display:none'><td class='ui-state-error'></td></tr>";
h+="<tr id='DelData' style='display:none'><td >"+f+"</td></tr>";h+='<tr><td class="delmsg" style="white-space:pre;">'+d[e.p.id].msg+"</td></tr><tr><td >&#160;</td></tr>";h+="</tbody></table></div>";h+="<table cellspacing='0' cellpadding='0' border='0' class='EditTable' id='"+s+"_2'><tbody><tr><td><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr><td class='DelButton EditButton'>"+("<a href='javascript:void(0)' id='dData' class='fm-button ui-state-default ui-corner-all'>"+b.bSubmit+"</a>")+
"&#160;"+("<a href='javascript:void(0)' id='eData' class='fm-button ui-state-default ui-corner-all'>"+b.bCancel+"</a>")+"</td></tr></tbody></table>";b.gbox="#gbox_"+F;a.jgrid.createModal(n,h,b,"#gview_"+e.p.id,a("#gview_"+e.p.id)[0]);if(C){m=C(a("#"+s));if(typeof m=="undefined")m=true}if(m===false)return;a(".fm-button","#"+s+"_2").hover(function(){a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});b.delicon=a.extend([true,"left","ui-icon-scissors"],d[e.p.id].delicon);
b.cancelicon=a.extend([true,"left","ui-icon-cancel"],d[e.p.id].cancelicon);if(b.delicon[0]===true)a("#dData","#"+s+"_2").addClass(b.delicon[1]=="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+b.delicon[2]+"'></span>");if(b.cancelicon[0]===true)a("#eData","#"+s+"_2").addClass(b.cancelicon[1]=="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+b.cancelicon[2]+"'></span>");a("#dData","#"+s+"_2").click(function(){var p=[true,""];y=
{};var x=a("#DelData>td","#"+s).text();if(a.isFunction(d[e.p.id].onclickSubmit))y=d[e.p.id].onclickSubmit(d[e.p.id],x)||{};if(a.isFunction(d[e.p.id].beforeSubmit))p=d[e.p.id].beforeSubmit(x);if(p[0]&&!d[e.p.id].processing){d[e.p.id].processing=true;a(this).addClass("ui-state-active");c=e.p.prmNames;t=a.extend({},d[e.p.id].delData,y);u=c.oper;t[u]=c.deloper;o=c.id;t[o]=x;var A=a.extend({url:d[e.p.id].url?d[e.p.id].url:a(e).jqGrid("getGridParam","editurl"),type:d[e.p.id].mtype,data:a.isFunction(d[e.p.id].serializeDelData)?
d[e.p.id].serializeDelData(t):t,complete:function(G,N){if(N!="success"){p[0]=false;p[1]=a.isFunction(d[e.p.id].errorTextFormat)?d[e.p.id].errorTextFormat(G):N+" Status: '"+G.statusText+"'. Error code: "+G.status}else if(a.isFunction(d[e.p.id].afterSubmit))p=d[e.p.id].afterSubmit(G,t);if(p[0]===false){a("#DelError>td","#"+s).html(p[1]);a("#DelError","#"+s).show()}else{if(d[e.p.id].reloadAfterSubmit&&e.p.datatype!="local")a(e).trigger("reloadGrid");else{var I=[];I=x.split(",");if(e.p.treeGrid===true)try{a(e).jqGrid("delTreeNode",
I[0])}catch(l){}else for(var z=0;z<I.length;z++)a(e).jqGrid("delRowData",I[z]);e.p.selrow=null;e.p.selarrrow=[]}a.isFunction(d[e.p.id].afterComplete)&&setTimeout(function(){d[e.p.id].afterComplete(G,x)},500)}d[e.p.id].processing=false;a("#dData","#"+s+"_2").removeClass("ui-state-active");p[0]&&a.jgrid.hideModal("#"+n.themodal,{gb:"#gbox_"+F,jqm:b.jqModal,onClose:d[e.p.id].onClose})}},a.jgrid.ajaxOptions,d[e.p.id].ajaxDelOptions);if(!A.url&&!d[e.p.id].useDataProxy)if(a.isFunction(e.p.dataProxy))d[e.p.id].useDataProxy=
true;else{p[0]=false;p[1]+=" "+a.jgrid.errors.nourl}if(p[0])d[e.p.id].useDataProxy?e.p.dataProxy.call(e,A,"del_"+e.p.id):a.ajax(A)}if(p[0]===false){a("#DelError>td","#"+s).html(p[1]);a("#DelError","#"+s).show()}return false});a("#eData","#"+s+"_2").click(function(){a.jgrid.hideModal("#"+n.themodal,{gb:"#gbox_"+F,jqm:d[e.p.id].jqModal,onClose:d[e.p.id].onClose});return false});r&&d[e.p.id].beforeShowForm(a("#"+s));a.jgrid.viewModal("#"+n.themodal,{gbox:"#gbox_"+F,jqm:d[e.p.id].jqModal,overlay:d[e.p.id].overlay,
modal:d[e.p.id].modal})}E&&d[e.p.id].afterShowForm(a("#"+s));d[e.p.id].closeOnEscape===true&&setTimeout(function(){a(".ui-jqdialog-titlebar-close","#"+n.modalhead).focus()},0)}})},navGrid:function(f,b,e,r,E,C,F){b=a.extend({edit:true,editicon:"ui-icon-pencil",add:true,addicon:"ui-icon-plus",del:true,delicon:"ui-icon-trash",search:true,searchicon:"ui-icon-search",refresh:true,refreshicon:"ui-icon-refresh",refreshstate:"firstpage",view:false,viewicon:"ui-icon-document",position:"left",closeOnEscape:true,
beforeRefresh:null,afterRefresh:null,cloneToTop:false},a.jgrid.nav,b||{});return this.each(function(){if(!this.nav){var y={themodal:"alertmod",modalhead:"alerthd",modalcontent:"alertcnt"},m=this,s,t,o;if(!(!m.grid||typeof f!="string")){if(a("#"+y.themodal).html()===null){if(typeof window.innerWidth!="undefined"){s=window.innerWidth;t=window.innerHeight}else if(typeof document.documentElement!="undefined"&&typeof document.documentElement.clientWidth!="undefined"&&document.documentElement.clientWidth!==
0){s=document.documentElement.clientWidth;t=document.documentElement.clientHeight}else{s=1024;t=768}a.jgrid.createModal(y,"<div>"+b.alerttext+"</div><span tabindex='0'><span tabindex='-1' id='jqg_alrt'></span></span>",{gbox:"#gbox_"+m.p.id,jqModal:true,drag:true,resize:true,caption:b.alertcap,top:t/2-25,left:s/2-100,width:200,height:"auto",closeOnEscape:b.closeOnEscape},"","",true)}s=1;if(b.cloneToTop&&m.p.toppager)s=2;for(t=0;t<s;t++){var c=a("<table cellspacing='0' cellpadding='0' border='0' class='ui-pg-table navtable' style='float:left;table-layout:auto;'><tbody><tr></tr></tbody></table>"),
u,n;if(t===0){u=f;n=m.p.id;if(u==m.p.toppager){n+="_top";s=1}}else{u=m.p.toppager;n=m.p.id+"_top"}m.p.direction=="rtl"&&a(c).attr("dir","rtl").css("float","right");if(b.add){r=r||{};o=a("<td class='ui-pg-button ui-corner-all'></td>");a(o).append("<div class='ui-pg-div'><span class='ui-icon "+b.addicon+"'></span>"+b.addtext+"</div>");a("tr",c).append(o);a(o,c).attr({title:b.addtitle||"",id:r.id||"add_"+n}).click(function(){a(this).hasClass("ui-state-disabled")||(a.isFunction(b.addfunc)?b.addfunc():
a(m).jqGrid("editGridRow","new",r));return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});o=null}if(b.edit){o=a("<td class='ui-pg-button ui-corner-all'></td>");e=e||{};a(o).append("<div class='ui-pg-div'><span class='ui-icon "+b.editicon+"'></span>"+b.edittext+"</div>");a("tr",c).append(o);a(o,c).attr({title:b.edittitle||"",id:e.id||"edit_"+n}).click(function(){if(!a(this).hasClass("ui-state-disabled")){var h=
m.p.selrow;if(h)a.isFunction(b.editfunc)?b.editfunc(h):a(m).jqGrid("editGridRow",h,e);else{a.jgrid.viewModal("#"+y.themodal,{gbox:"#gbox_"+m.p.id,jqm:true});a("#jqg_alrt").focus()}}return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});o=null}if(b.view){o=a("<td class='ui-pg-button ui-corner-all'></td>");F=F||{};a(o).append("<div class='ui-pg-div'><span class='ui-icon "+b.viewicon+"'></span>"+b.viewtext+
"</div>");a("tr",c).append(o);a(o,c).attr({title:b.viewtitle||"",id:F.id||"view_"+n}).click(function(){if(!a(this).hasClass("ui-state-disabled")){var h=m.p.selrow;if(h)a.isFunction(b.viewfunc)?b.viewfunc(h):a(m).jqGrid("viewGridRow",h,F);else{a.jgrid.viewModal("#"+y.themodal,{gbox:"#gbox_"+m.p.id,jqm:true});a("#jqg_alrt").focus()}}return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});o=null}if(b.del){o=
a("<td class='ui-pg-button ui-corner-all'></td>");E=E||{};a(o).append("<div class='ui-pg-div'><span class='ui-icon "+b.delicon+"'></span>"+b.deltext+"</div>");a("tr",c).append(o);a(o,c).attr({title:b.deltitle||"",id:E.id||"del_"+n}).click(function(){if(!a(this).hasClass("ui-state-disabled")){var h;if(m.p.multiselect){h=m.p.selarrrow;if(h.length===0)h=null}else h=m.p.selrow;if(h)"function"==typeof b.delfunc?b.delfunc(h):a(m).jqGrid("delGridRow",h,E);else{a.jgrid.viewModal("#"+y.themodal,{gbox:"#gbox_"+
m.p.id,jqm:true});a("#jqg_alrt").focus()}}return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});o=null}if(b.add||b.edit||b.del||b.view)a("tr",c).append("<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='ui-separator'></span></td>");if(b.search){o=a("<td class='ui-pg-button ui-corner-all'></td>");C=C||{};a(o).append("<div class='ui-pg-div'><span class='ui-icon "+b.searchicon+
"'></span>"+b.searchtext+"</div>");a("tr",c).append(o);a(o,c).attr({title:b.searchtitle||"",id:C.id||"search_"+n}).click(function(){a(this).hasClass("ui-state-disabled")||a(m).jqGrid("searchGrid",C);return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});C.showOnLoad&&C.showOnLoad===true&&a(o,c).click();o=null}if(b.refresh){o=a("<td class='ui-pg-button ui-corner-all'></td>");a(o).append("<div class='ui-pg-div'><span class='ui-icon "+
b.refreshicon+"'></span>"+b.refreshtext+"</div>");a("tr",c).append(o);a(o,c).attr({title:b.refreshtitle||"",id:"refresh_"+n}).click(function(){if(!a(this).hasClass("ui-state-disabled")){a.isFunction(b.beforeRefresh)&&b.beforeRefresh();m.p.search=false;try{var h=m.p.id;m.p.postData.filters="";a("#fbox_"+h).jqFilter("resetFilter");a.isFunction(m.clearToolbar)&&m.clearToolbar(false)}catch(p){}switch(b.refreshstate){case "firstpage":a(m).trigger("reloadGrid",[{page:1}]);break;case "current":a(m).trigger("reloadGrid",
[{current:true}])}a.isFunction(b.afterRefresh)&&b.afterRefresh()}return false}).hover(function(){a(this).hasClass("ui-state-disabled")||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")});o=null}o=a(".ui-jqgrid").css("font-size")||"11px";a("body").append("<div id='testpg2' class='ui-jqgrid ui-widget ui-widget-content' style='font-size:"+o+";visibility:hidden;' ></div>");o=a(c).clone().appendTo("#testpg2").width();a("#testpg2").remove();a(u+"_"+b.position,u).append(c);
if(m.p._nvtd){if(o>m.p._nvtd[0]){a(u+"_"+b.position,u).width(o);m.p._nvtd[0]=o}m.p._nvtd[1]=o}c=o=o=null;this.nav=true}}}})},navButtonAdd:function(f,b){b=a.extend({caption:"newButton",title:"",buttonicon:"ui-icon-newwin",onClickButton:null,position:"last",cursor:"pointer"},b||{});return this.each(function(){if(this.grid){if(f.indexOf("#")!==0)f="#"+f;var e=a(".navtable",f)[0],r=this;if(e)if(!(b.id&&a("#"+b.id,e).html()!==null)){var E=a("<td></td>");b.buttonicon.toString().toUpperCase()=="NONE"?a(E).addClass("ui-pg-button ui-corner-all").append("<div class='ui-pg-div'>"+
b.caption+"</div>"):a(E).addClass("ui-pg-button ui-corner-all").append("<div class='ui-pg-div'><span class='ui-icon "+b.buttonicon+"'></span>"+b.caption+"</div>");b.id&&a(E).attr("id",b.id);if(b.position=="first")e.rows[0].cells.length===0?a("tr",e).append(E):a("tr td:eq(0)",e).before(E);else a("tr",e).append(E);a(E,e).attr("title",b.title||"").click(function(C){a(this).hasClass("ui-state-disabled")||a.isFunction(b.onClickButton)&&b.onClickButton.call(r,C);return false}).hover(function(){a(this).hasClass("ui-state-disabled")||
a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")})}}})},navSeparatorAdd:function(f,b){b=a.extend({sepclass:"ui-separator",sepcontent:""},b||{});return this.each(function(){if(this.grid){if(f.indexOf("#")!==0)f="#"+f;var e=a(".navtable",f)[0];if(e){var r="<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='"+b.sepclass+"'></span>"+b.sepcontent+"</td>";a("tr",e).append(r)}}})},GridToForm:function(f,b){return this.each(function(){if(this.grid){var e=
a(this).jqGrid("getRowData",f);if(e)for(var r in e)a("[name="+a.jgrid.jqID(r)+"]",b).is("input:radio")||a("[name="+a.jgrid.jqID(r)+"]",b).is("input:checkbox")?a("[name="+a.jgrid.jqID(r)+"]",b).each(function(){a(this).val()==e[r]?a(this).attr("checked","checked"):a(this).attr("checked","")}):a("[name="+a.jgrid.jqID(r)+"]",b).val(e[r])}})},FormToGrid:function(f,b,e,r){return this.each(function(){if(this.grid){e||(e="set");r||(r="first");var E=a(b).serializeArray(),C={};a.each(E,function(F,y){C[y.name]=
y.value});if(e=="add")a(this).jqGrid("addRowData",f,C,r);else e=="set"&&a(this).jqGrid("setRowData",f,C)}})}})})(jQuery);
(function(c){c.fn.jqFilter=function(k){if(typeof k==="string"){var w=c.fn.jqFilter[k];if(!w)throw"jqFilter - No such method: "+k;var B=c.makeArray(arguments).slice(1);return w.apply(this,B)}var o=c.extend(true,{filter:null,columns:[],onChange:null,afterRedraw:null,checkValues:null,error:false,errmsg:"",errorcheck:true,showQuery:true,sopt:null,ops:[{name:"eq",description:"equal",operator:"="},{name:"ne",description:"not equal",operator:"<>"},{name:"lt",description:"less",operator:"<"},{name:"le",description:"less or equal",
operator:"<="},{name:"gt",description:"greater",operator:">"},{name:"ge",description:"greater or equal",operator:">="},{name:"bw",description:"begins with",operator:"LIKE"},{name:"bn",description:"does not begin with",operator:"NOT LIKE"},{name:"in",description:"in",operator:"IN"},{name:"ni",description:"not in",operator:"NOT IN"},{name:"ew",description:"ends with",operator:"LIKE"},{name:"en",description:"does not end with",operator:"NOT LIKE"},{name:"cn",description:"contains",operator:"LIKE"},{name:"nc",
description:"does not contain",operator:"NOT LIKE"},{name:"nu",description:"is null",operator:"IS NULL"},{name:"nn",description:"is not null",operator:"IS NOT NULL"}],numopts:["eq","ne","lt","le","gt","ge","nu","nn","in","ni"],stropts:["eq","ne","bw","bn","ew","en","cn","nc","nu","nn","in","ni"],_gridsopt:[],groupOps:["AND","OR"],groupButton:true,ruleButtons:true},k||{});return this.each(function(){if(!this.filter){this.p=o;if(this.p.filter===null||this.p.filter===undefined)this.p.filter={groupOp:this.p.groupOps[0],
rules:[],groups:[]};var q,x=this.p.columns.length,i,y=/msie/i.test(navigator.userAgent)&&!window.opera;if(this.p._gridsopt.length)for(q=0;q<this.p._gridsopt.length;q++)this.p.ops[q].description=this.p._gridsopt[q];this.p.initFilter=c.extend(true,{},this.p.filter);if(x){for(q=0;q<x;q++){i=this.p.columns[q];if(i.stype)i.inputtype=i.stype;else if(!i.inputtype)i.inputtype="text";if(i.sorttype)i.searchtype=i.sorttype;else if(!i.searchtype)i.searchtype="string";if(i.hidden===undefined)i.hidden=false;if(!i.label)i.label=
i.name;if(i.index)i.name=i.index;if(!i.hasOwnProperty("searchoptions"))i.searchoptions={};if(!i.hasOwnProperty("searchrules"))i.searchrules={}}this.p.showQuery&&c(this).append("<table class='queryresult ui-widget ui-widget-content' style='display:block;max-width:440px;border:0px none;'><tbody><tr><td class='query'></td></tr></tbody></table>");var z=function(d,f){var a=[true,""];if(c.isFunction(f.searchrules))a=f.searchrules(d,f);else if(c.jgrid&&c.jgrid.checkValues)try{a=c.jgrid.checkValues(d,-1,
null,f.searchrules,f.label)}catch(b){}if(a&&a.length&&a[0]===false){o.error=!a[0];o.errmsg=a[1]}};this.onchange=function(){this.p.error=false;this.p.errmsg="";return c.isFunction(this.p.onChange)?this.p.onChange.call(this,this.p):false};this.reDraw=function(){c("table.group:first",this).remove();var d=this.createTableForGroup(o.filter,null);c(this).append(d);c.isFunction(this.p.afterRedraw)&&this.p.afterRedraw.call(this,this.p)};this.createTableForGroup=function(d,f){var a=this,b,e=c("<table class='group ui-widget ui-widget-content' style='border:0px none;'><tbody></tbody></table>");
f===null&&c(e).append("<tr class='error' style='display:none;'><th colspan='5' class='ui-state-error' align='left'></th></tr>");var g=c("<tr></tr>");c(e).append(g);var j=c("<th colspan='5' align='left'></th>");g.append(j);if(this.p.ruleButtons===true){var h=c("<select class='opsel'></select>");j.append(h);g="";var l;for(b=0;b<o.groupOps.length;b++){l=d.groupOp===a.p.groupOps[b]?" selected='selected'":"";g+="<option value='"+a.p.groupOps[b]+"'"+l+">"+a.p.groupOps[b]+"</option>"}h.append(g).bind("change",
function(){d.groupOp=c(h).val();a.onchange()})}g="<span></span>";if(this.p.groupButton){g=c("<input type='button' value='+ {}' title='Add subgroup' class='add-group'/>");g.bind("click",function(){if(d.groups===undefined)d.groups=[];d.groups.push({groupOp:o.groupOps[0],rules:[],groups:[]});a.reDraw();a.onchange();return false})}j.append(g);if(this.p.ruleButtons===true){g=c("<input type='button' value='+' title='Add rule' class='add-rule ui-add'/>");var m;g.bind("click",function(){if(d.rules===undefined)d.rules=
[];for(b=0;b<a.p.columns.length;b++){var n=typeof a.p.columns[b].search==="undefined"?true:a.p.columns[b].search,s=a.p.columns[b].hidden===true;if(a.p.columns[b].searchoptions.searchhidden===true&&n||n&&!s){m=a.p.columns[b];break}}d.rules.push({field:m.name,op:(m.searchoptions.sopt?m.searchoptions.sopt:a.p.sopt?a.p.sopt:m.searchtype==="string"?a.p.stropts:a.p.numopts)[0],data:""});a.reDraw();return false});j.append(g)}if(f!==null){g=c("<input type='button' value='-' title='Delete group' class='delete-group'/>");
j.append(g);g.bind("click",function(){for(b=0;b<f.groups.length;b++)if(f.groups[b]===d){f.groups.splice(b,1);break}a.reDraw();a.onchange();return false})}if(d.groups!==undefined)for(b=0;b<d.groups.length;b++){j=c("<tr></tr>");e.append(j);g=c("<td class='first'></td>");j.append(g);g=c("<td colspan='4'></td>");g.append(this.createTableForGroup(d.groups[b],d));j.append(g)}if(d.groupOp===undefined)d.groupOp=a.p.groupOps[0];if(d.rules!==undefined)for(b=0;b<d.rules.length;b++)e.append(this.createTableRowForRule(d.rules[b],
d));return e};this.createTableRowForRule=function(d,f){var a=this,b=c("<tr></tr>"),e,g,j,h,l="",m;b.append("<td class='first'></td>");var n=c("<td class='columns'></td>");b.append(n);var s=c("<select></select>"),p,t=[];n.append(s);s.bind("change",function(){d.field=c(s).val();j=c(this).parents("tr:first");for(e=0;e<a.p.columns.length;e++)if(a.p.columns[e].name===d.field){h=a.p.columns[e];break}if(h){h.searchoptions.id=c.jgrid.randId();if(y&&h.inputtype==="text")if(!h.searchoptions.size)h.searchoptions.size=
10;var r=c.jgrid.createEl(h.inputtype,h.searchoptions,"",true,a.p.ajaxSelectOptions,true);c(r).addClass("input-elm");g=h.searchoptions.sopt?h.searchoptions.sopt:a.p.sopt?a.p.sopt:h.searchtype==="string"?a.p.stropts:a.p.numopts;var A="",v="";t=[];c.each(a.p.ops,function(){t.push(this.name)});for(e=0;e<g.length;e++){p=c.inArray(g[e],t);if(p!==-1){v="";if(e===0){d.op=a.p.ops[p].name;v=" selected='selected'"}A+="<option value='"+a.p.ops[p].name+"'"+v+">"+a.p.ops[p].description+"</option>"}}c(".selectopts",
j).empty().append(A);c(".data",j).empty().append(r);c(".input-elm",j).bind("change",function(){d.data=c(this).val();a.onchange()});setTimeout(function(){d.data=c(r).val();a.onchange()},0)}});for(e=n=0;e<a.p.columns.length;e++){m=typeof a.p.columns[e].search==="undefined"?true:a.p.columns[e].search;var C=a.p.columns[e].hidden===true;if(a.p.columns[e].searchoptions.searchhidden===true&&m||m&&!C){m="";if(d.field===a.p.columns[e].name){m=" selected='selected'";n=e}l+="<option value='"+a.p.columns[e].name+
"'"+m+">"+a.p.columns[e].label+"</option>"}}s.append(l);l=c("<td class='operators'></td>");b.append(l);h=o.columns[n];h.searchoptions.id=c.jgrid.randId();if(y&&h.inputtype==="text")if(!h.searchoptions.size)h.searchoptions.size=10;n=c.jgrid.createEl(h.inputtype,h.searchoptions,d.data,true,a.p.ajaxSelectOptions,true);var u=c("<select class='selectopts'></select>");l.append(u);u.bind("change",function(){d.op=c(u).val();j=c(this).parents("tr:first");var r=c(".input-elm",j)[0];if(d.op==="nu"||d.op==="nn"){d.data=
"";r.value="";r.setAttribute("readonly","true");r.setAttribute("disabled","true")}else{r.removeAttribute("readonly");r.removeAttribute("disabled")}a.onchange()});g=h.searchoptions.sopt?h.searchoptions.sopt:a.p.sopt?a.p.sopt:h.searchtype==="string"?o.stropts:a.p.numopts;l="";c.each(a.p.ops,function(){t.push(this.name)});for(e=0;e<g.length;e++){p=c.inArray(g[e],t);if(p!==-1){m=d.op===a.p.ops[p].name?" selected='selected'":"";l+="<option value='"+a.p.ops[p].name+"'"+m+">"+a.p.ops[p].description+"</option>"}}u.append(l);
l=c("<td class='data'></td>");b.append(l);l.append(n);c(n).addClass("input-elm").bind("change",function(){d.data=c(this).val();a.onchange()});l=c("<td></td>");b.append(l);if(this.p.ruleButtons===true){n=c("<input type='button' value='-' title='Delete rule' class='delete-rule ui-del'/>");l.append(n);n.bind("click",function(){for(e=0;e<f.rules.length;e++)if(f.rules[e]===d){f.rules.splice(e,1);break}a.reDraw();a.onchange();return false})}return b};this.getStringForGroup=function(d){var f="(",a;if(d.groups!==
undefined)for(a=0;a<d.groups.length;a++){if(f.length>1)f+=" "+d.groupOp+" ";try{f+=this.getStringForGroup(d.groups[a])}catch(b){alert(b)}}if(d.rules!==undefined)try{for(a=0;a<d.rules.length;a++){if(f.length>1)f+=" "+d.groupOp+" ";f+=this.getStringForRule(d.rules[a])}}catch(e){alert(e)}f+=")";return f==="()"?"":f};this.getStringForRule=function(d){var f="",a="",b,e;for(b=0;b<this.p.ops.length;b++)if(this.p.ops[b].name===d.op){f=this.p.ops[b].operator;a=this.p.ops[b].name;break}for(b=0;b<this.p.columns.length;b++)if(this.p.columns[b].name===
d.field){e=this.p.columns[b];break}b=d.data;if(a==="bw"||a==="bn")b+="%";if(a==="ew"||a==="en")b="%"+b;if(a==="cn"||a==="nc")b="%"+b+"%";if(a==="in"||a==="ni")b=" ("+b+")";o.errorcheck&&z(d.data,e);return c.inArray(e.searchtype,["int","integer","float","number","currency"])!==-1||a==="nn"||a==="nu"?d.field+" "+f+" "+b:d.field+" "+f+' "'+b+'"'};this.resetFilter=function(){this.p.filter=c.extend(true,{},this.p.initFilter);this.reDraw();this.onchange()};this.hideError=function(){c("th.ui-state-error",
this).html("");c("tr.error",this).hide()};this.showError=function(){c("th.ui-state-error",this).html(this.p.errmsg);c("tr.error",this).show()};this.toUserFriendlyString=function(){return this.getStringForGroup(o.filter)};this.toString=function(){function d(a){var b="(",e;if(a.groups!==undefined)for(e=0;e<a.groups.length;e++){if(b.length>1)b+=a.groupOp==="OR"?" || ":" && ";b+=d(a.groups[e])}if(a.rules!==undefined)for(e=0;e<a.rules.length;e++){if(b.length>1)b+=a.groupOp==="OR"?" || ":" && ";var g=a.rules[e];
if(f.p.errorcheck){var j=void 0,h=void 0;for(j=0;j<f.p.columns.length;j++)if(f.p.columns[j].name===g.field){h=f.p.columns[j];break}h&&z(g.data,h)}b+=g.op+"(item."+g.field+",'"+g.data+"')"}b+=")";return b==="()"?"":b}var f=this;return d(this.p.filter)};this.reDraw();if(this.p.showQuery)this.onchange();this.filter=true}}})};c.extend(c.fn.jqFilter,{toSQLString:function(){var k="";this.each(function(){k=this.toUserFriendlyString()});return k},filterData:function(){var k;this.each(function(){k=this.p.filter});
return k},getParameter:function(k){if(k!==undefined)if(this.p.hasOwnProperty(k))return this.p[k];return this.p},resetFilter:function(){return this.each(function(){this.resetFilter()})},addFilter:function(k){if(typeof k==="string")k=jQuery.jgrid.parse(k);this.each(function(){this.p.filter=k;this.reDraw();this.onchange()})}})})(jQuery);
(function(a){a.jgrid.extend({editRow:function(f,x,m,r,t,y,u,l,s){var g={keys:x||false,oneditfunc:m||null,successfunc:r||null,url:t||null,extraparam:y||{},aftersavefunc:u||null,errorfunc:l||null,afterrestorefunc:s||null,restoreAfterErorr:true},o=a.makeArray(arguments).slice(1),b;b=o[0]&&typeof o[0]=="object"&&!a.isFunction(o[0])?a.extend(g,o[0]):g;return this.each(function(){var d=this,c,j,v=0,q=null,p={},k,e;if(d.grid){k=a(d).jqGrid("getInd",f,true);if(k!==false)if((a(k).attr("editable")||"0")=="0"&&
!a(k).hasClass("not-editable-row")){e=d.p.colModel;a("td",k).each(function(h){c=e[h].name;var A=d.p.treeGrid===true&&c==d.p.ExpandColumn;if(A)j=a("span:first",this).html();else try{j=a.unformat(this,{rowId:f,colModel:e[h]},h)}catch(n){j=e[h].edittype&&e[h].edittype=="textarea"?a(this).text():a(this).html()}if(c!="cb"&&c!="subgrid"&&c!="rn"){if(d.p.autoencode)j=a.jgrid.htmlDecode(j);p[c]=j;if(e[h].editable===true){if(q===null)q=h;A?a("span:first",this).html(""):a(this).html("");var i=a.extend({},e[h].editoptions||
{},{id:f+"_"+c,name:c});if(!e[h].edittype)e[h].edittype="text";if(j=="&nbsp;"||j=="&#160;"||j.length==1&&j.charCodeAt(0)==160)j="";i=a.jgrid.createEl(e[h].edittype,i,j,true,a.extend({},a.jgrid.ajaxOptions,d.p.ajaxSelectOptions||{}));a(i).addClass("editable");A?a("span:first",this).append(i):a(this).append(i);e[h].edittype=="select"&&e[h].editoptions.multiple===true&&a.browser.msie&&a(i).width(a(i).width());v++}}});if(v>0){p.id=f;d.p.savedRow.push(p);a(k).attr("editable","1");a("td:eq("+q+") input",
k).focus();b.keys===true&&a(k).bind("keydown",function(h){h.keyCode===27&&a(d).jqGrid("restoreRow",f,s);if(h.keyCode===13){if(h.target.tagName=="TEXTAREA")return true;a(d).jqGrid("saveRow",f,b);return false}h.stopPropagation()});a.isFunction(b.oneditfunc)&&b.oneditfunc.call(d,f)}}}})},saveRow:function(f,x,m,r,t,y,u){var l={successfunc:x||null,url:m||null,extraparam:r||{},aftersavefunc:t||null,errorfunc:y||null,afterrestorefunc:u||null,restoreAfterErorr:true},s=a.makeArray(arguments).slice(1),g;g=
s[0]&&typeof s[0]=="object"&&!a.isFunction(s[0])?a.extend(l,s[0]):l;var o=false,b=this[0],d,c={},j={},v={},q,p,k;if(!b.grid)return o;k=a(b).jqGrid("getInd",f,true);if(k===false)return o;l=a(k).attr("editable");g.url=g.url?g.url:b.p.editurl;if(l==="1"){var e;a("td",k).each(function(n){e=b.p.colModel[n];d=e.name;if(d!="cb"&&d!="subgrid"&&e.editable===true&&d!="rn"&&!a(this).hasClass("not-editable-cell")){switch(e.edittype){case "checkbox":var i=["Yes","No"];if(e.editoptions)i=e.editoptions.value.split(":");
c[d]=a("input",this).attr("checked")?i[0]:i[1];break;case "text":case "password":case "textarea":case "button":c[d]=a("input, textarea",this).val();break;case "select":if(e.editoptions.multiple){i=a("select",this);var w=[];c[d]=a(i).val();c[d]=c[d]?c[d].join(","):"";a("select > option:selected",this).each(function(B,C){w[B]=a(C).text()});j[d]=w.join(",")}else{c[d]=a("select>option:selected",this).val();j[d]=a("select>option:selected",this).text()}if(e.formatter&&e.formatter=="select")j={};break;case "custom":try{if(e.editoptions&&
a.isFunction(e.editoptions.custom_value)){c[d]=e.editoptions.custom_value.call(b,a(".customelement",this),"get");if(c[d]===undefined)throw"e2";}else throw"e1";}catch(z){z=="e1"&&a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+a.jgrid.edit.msg.nodefined,jQuery.jgrid.edit.bClose);z=="e2"?a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+a.jgrid.edit.msg.novalue,jQuery.jgrid.edit.bClose):a.jgrid.info_dialog(jQuery.jgrid.errors.errcap,z.message,jQuery.jgrid.edit.bClose)}}p=
a.jgrid.checkValues(c[d],n,b);if(p[0]===false){p[1]=c[d]+" "+p[1];return false}if(b.p.autoencode)c[d]=a.jgrid.htmlEncode(c[d]);if(g.url!=="clientArray"&&e.editoptions&&e.editoptions.NullIfEmpty===true)if(c[d]=="")v[d]="null"}});if(p[0]===false){try{var h=a.jgrid.findPos(a("#"+a.jgrid.jqID(f),b.grid.bDiv)[0]);a.jgrid.info_dialog(a.jgrid.errors.errcap,p[1],a.jgrid.edit.bClose,{left:h[0],top:h[1]})}catch(A){alert(p[1])}return o}if(c){l=b.p.prmNames;s=l.oper;h=l.id;c[s]=l.editoper;c[h]=f;if(typeof b.p.inlineData==
"undefined")b.p.inlineData={};c=a.extend({},c,b.p.inlineData,g.extraparam)}if(g.url=="clientArray"){c=a.extend({},c,j);b.p.autoencode&&a.each(c,function(n,i){c[n]=a.jgrid.htmlDecode(i)});h=a(b).jqGrid("setRowData",f,c);a(k).attr("editable","0");for(l=0;l<b.p.savedRow.length;l++)if(b.p.savedRow[l].id==f){q=l;break}q>=0&&b.p.savedRow.splice(q,1);a.isFunction(g.aftersavefunc)&&g.aftersavefunc.call(b,f,h);o=true;a(k).unbind("keydown")}else{a("#lui_"+b.p.id).show();v=a.extend({},c,v);a.ajax(a.extend({url:g.url,
data:a.isFunction(b.p.serializeRowData)?b.p.serializeRowData.call(b,v):v,type:"POST",async:false,complete:function(n,i){a("#lui_"+b.p.id).hide();if(i==="success")if((a.isFunction(g.successfunc)?g.successfunc.call(b,n):true)===true){b.p.autoencode&&a.each(c,function(z,B){c[z]=a.jgrid.htmlDecode(B)});c=a.extend({},c,j);a(b).jqGrid("setRowData",f,c);a(k).attr("editable","0");for(var w=0;w<b.p.savedRow.length;w++)if(b.p.savedRow[w].id==f){q=w;break}q>=0&&b.p.savedRow.splice(q,1);a.isFunction(g.aftersavefunc)&&
g.aftersavefunc.call(b,f,n);o=true;a(k).unbind("keydown")}else{a.isFunction(g.errorfunc)&&g.errorfunc.call(b,f,n,i);g.restoreAfterError===true&&a(b).jqGrid("restoreRow",f,g.afterrestorefunc)}},error:function(n,i){a("#lui_"+b.p.id).hide();if(a.isFunction(g.errorfunc))g.errorfunc.call(b,f,n,i);else try{jQuery.jgrid.info_dialog(jQuery.jgrid.errors.errcap,'<div class="ui-state-error">'+n.responseText+"</div>",jQuery.jgrid.edit.bClose,{buttonalign:"right"})}catch(w){alert(n.responseText)}g.restoreAfterError===
true&&a(b).jqGrid("restoreRow",f,g.afterrestorefunc)}},a.jgrid.ajaxOptions,b.p.ajaxRowOptions||{}))}}return o},restoreRow:function(f,x){return this.each(function(){var m=this,r,t,y={};if(m.grid){t=a(m).jqGrid("getInd",f,true);if(t!==false){for(var u=0;u<m.p.savedRow.length;u++)if(m.p.savedRow[u].id==f){r=u;break}if(r>=0){if(a.isFunction(a.fn.datepicker))try{a("input.hasDatepicker","#"+a.jgrid.jqID(t.id)).datepicker("hide")}catch(l){}a.each(m.p.colModel,function(){if(this.editable===true&&this.name in
m.p.savedRow[r]&&!a(this).hasClass("not-editable-cell"))y[this.name]=m.p.savedRow[r][this.name]});a(m).jqGrid("setRowData",f,y);a(t).attr("editable","0").unbind("keydown");m.p.savedRow.splice(r,1)}a.isFunction(x)&&x.call(m,f)}}})}})})(jQuery);
(function(b){b.jgrid.extend({editCell:function(d,f,a){return this.each(function(){var c=this,h,e,g,i;if(!(!c.grid||c.p.cellEdit!==true)){f=parseInt(f,10);c.p.selrow=c.rows[d].id;c.p.knv||b(c).jqGrid("GridNav");if(c.p.savedRow.length>0){if(a===true)if(d==c.p.iRow&&f==c.p.iCol)return;b(c).jqGrid("saveCell",c.p.savedRow[0].id,c.p.savedRow[0].ic)}else window.setTimeout(function(){b("#"+c.p.knv).attr("tabindex","-1").focus()},0);i=c.p.colModel[f];h=i.name;if(!(h=="subgrid"||h=="cb"||h=="rn")){g=b("td:eq("+
f+")",c.rows[d]);if(i.editable===true&&a===true&&!g.hasClass("not-editable-cell")){if(parseInt(c.p.iCol,10)>=0&&parseInt(c.p.iRow,10)>=0){b("td:eq("+c.p.iCol+")",c.rows[c.p.iRow]).removeClass("edit-cell ui-state-highlight");b(c.rows[c.p.iRow]).removeClass("selected-row ui-state-hover")}b(g).addClass("edit-cell ui-state-highlight");b(c.rows[d]).addClass("selected-row ui-state-hover");try{e=b.unformat(g,{rowId:c.rows[d].id,colModel:i},f)}catch(k){e=i.edittype&&i.edittype=="textarea"?b(g).text():b(g).html()}if(c.p.autoencode)e=
b.jgrid.htmlDecode(e);if(!i.edittype)i.edittype="text";c.p.savedRow.push({id:d,ic:f,name:h,v:e});if(e=="&nbsp;"||e=="&#160;"||e.length==1&&e.charCodeAt(0)==160)e="";if(b.isFunction(c.p.formatCell)){var j=c.p.formatCell.call(c,c.rows[d].id,h,e,d,f);if(j!==undefined)e=j}j=b.extend({},i.editoptions||{},{id:d+"_"+h,name:h});var m=b.jgrid.createEl(i.edittype,j,e,true,b.extend({},b.jgrid.ajaxOptions,c.p.ajaxSelectOptions||{}));b.isFunction(c.p.beforeEditCell)&&c.p.beforeEditCell.call(c,c.rows[d].id,h,e,
d,f);b(g).html("").append(m).attr("tabindex","0");window.setTimeout(function(){b(m).focus()},0);b("input, select, textarea",g).bind("keydown",function(l){if(l.keyCode===27)if(b("input.hasDatepicker",g).length>0)b(".ui-datepicker").is(":hidden")?b(c).jqGrid("restoreCell",d,f):b("input.hasDatepicker",g).datepicker("hide");else b(c).jqGrid("restoreCell",d,f);l.keyCode===13&&b(c).jqGrid("saveCell",d,f);if(l.keyCode==9)if(c.grid.hDiv.loading)return false;else l.shiftKey?b(c).jqGrid("prevCell",d,f):b(c).jqGrid("nextCell",
d,f);l.stopPropagation()});b.isFunction(c.p.afterEditCell)&&c.p.afterEditCell.call(c,c.rows[d].id,h,e,d,f)}else{if(parseInt(c.p.iCol,10)>=0&&parseInt(c.p.iRow,10)>=0){b("td:eq("+c.p.iCol+")",c.rows[c.p.iRow]).removeClass("edit-cell ui-state-highlight");b(c.rows[c.p.iRow]).removeClass("selected-row ui-state-hover")}g.addClass("edit-cell ui-state-highlight");b(c.rows[d]).addClass("selected-row ui-state-hover");if(b.isFunction(c.p.onSelectCell)){e=g.html().replace(/\&#160\;/ig,"");c.p.onSelectCell.call(c,
c.rows[d].id,h,e,d,f)}}c.p.iCol=f;c.p.iRow=d}}})},saveCell:function(d,f){return this.each(function(){var a=this,c;if(!(!a.grid||a.p.cellEdit!==true)){c=a.p.savedRow.length>=1?0:null;if(c!==null){var h=b("td:eq("+f+")",a.rows[d]),e,g,i=a.p.colModel[f],k=i.name,j=b.jgrid.jqID(k);switch(i.edittype){case "select":if(i.editoptions.multiple){j=b("#"+d+"_"+j,a.rows[d]);var m=[];if(e=b(j).val())e.join(",");else e="";b("option:selected",j).each(function(o,p){m[o]=b(p).text()});g=m.join(",")}else{e=b("#"+d+
"_"+j+">option:selected",a.rows[d]).val();g=b("#"+d+"_"+j+">option:selected",a.rows[d]).text()}if(i.formatter)g=e;break;case "checkbox":var l=["Yes","No"];if(i.editoptions)l=i.editoptions.value.split(":");g=e=b("#"+d+"_"+j,a.rows[d]).attr("checked")?l[0]:l[1];break;case "password":case "text":case "textarea":case "button":g=e=b("#"+d+"_"+j,a.rows[d]).val();break;case "custom":try{if(i.editoptions&&b.isFunction(i.editoptions.custom_value)){e=i.editoptions.custom_value.call(a,b(".customelement",h),
"get");if(e===undefined)throw"e2";else g=e}else throw"e1";}catch(q){q=="e1"&&b.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+b.jgrid.edit.msg.nodefined,jQuery.jgrid.edit.bClose);q=="e2"?b.jgrid.info_dialog(jQuery.jgrid.errors.errcap,"function 'custom_value' "+b.jgrid.edit.msg.novalue,jQuery.jgrid.edit.bClose):b.jgrid.info_dialog(jQuery.jgrid.errors.errcap,q.message,jQuery.jgrid.edit.bClose)}}if(g!==a.p.savedRow[c].v){if(b.isFunction(a.p.beforeSaveCell))if(c=a.p.beforeSaveCell.call(a,
a.rows[d].id,k,e,d,f))g=e=c;var r=b.jgrid.checkValues(e,f,a);if(r[0]===true){c={};if(b.isFunction(a.p.beforeSubmitCell))(c=a.p.beforeSubmitCell.call(a,a.rows[d].id,k,e,d,f))||(c={});b("input.hasDatepicker",h).length>0&&b("input.hasDatepicker",h).datepicker("hide");if(a.p.cellsubmit=="remote")if(a.p.cellurl){var n={};if(a.p.autoencode)e=b.jgrid.htmlEncode(e);n[k]=e;l=a.p.prmNames;i=l.id;j=l.oper;n[i]=a.rows[d].id;n[j]=l.editoper;n=b.extend(c,n);b("#lui_"+a.p.id).show();a.grid.hDiv.loading=true;b.ajax(b.extend({url:a.p.cellurl,
data:b.isFunction(a.p.serializeCellData)?a.p.serializeCellData.call(a,n):n,type:"POST",complete:function(o,p){b("#lui_"+a.p.id).hide();a.grid.hDiv.loading=false;if(p=="success")if(b.isFunction(a.p.afterSubmitCell)){var s=a.p.afterSubmitCell.call(a,o,n.id,k,e,d,f);if(s[0]===true){b(h).empty();b(a).jqGrid("setCell",a.rows[d].id,f,g,false,false,true);b(h).addClass("dirty-cell");b(a.rows[d]).addClass("edited");b.isFunction(a.p.afterSaveCell)&&a.p.afterSaveCell.call(a,a.rows[d].id,k,e,d,f);a.p.savedRow.splice(0,
1)}else{b.jgrid.info_dialog(b.jgrid.errors.errcap,s[1],b.jgrid.edit.bClose);b(a).jqGrid("restoreCell",d,f)}}else{b(h).empty();b(a).jqGrid("setCell",a.rows[d].id,f,g,false,false,true);b(h).addClass("dirty-cell");b(a.rows[d]).addClass("edited");b.isFunction(a.p.afterSaveCell)&&a.p.afterSaveCell.call(a,a.rows[d].id,k,e,d,f);a.p.savedRow.splice(0,1)}},error:function(o,p){b("#lui_"+a.p.id).hide();a.grid.hDiv.loading=false;b.isFunction(a.p.errorCell)?a.p.errorCell.call(a,o,p):b.jgrid.info_dialog(b.jgrid.errors.errcap,
o.status+" : "+o.statusText+"<br/>"+p,b.jgrid.edit.bClose);b(a).jqGrid("restoreCell",d,f)}},b.jgrid.ajaxOptions,a.p.ajaxCellOptions||{}))}else try{b.jgrid.info_dialog(b.jgrid.errors.errcap,b.jgrid.errors.nourl,b.jgrid.edit.bClose);b(a).jqGrid("restoreCell",d,f)}catch(t){}if(a.p.cellsubmit=="clientArray"){b(h).empty();b(a).jqGrid("setCell",a.rows[d].id,f,g,false,false,true);b(h).addClass("dirty-cell");b(a.rows[d]).addClass("edited");b.isFunction(a.p.afterSaveCell)&&a.p.afterSaveCell.call(a,a.rows[d].id,
k,e,d,f);a.p.savedRow.splice(0,1)}}else try{window.setTimeout(function(){b.jgrid.info_dialog(b.jgrid.errors.errcap,e+" "+r[1],b.jgrid.edit.bClose)},100);b(a).jqGrid("restoreCell",d,f)}catch(u){}}else b(a).jqGrid("restoreCell",d,f)}b.browser.opera?b("#"+a.p.knv).attr("tabindex","-1").focus():window.setTimeout(function(){b("#"+a.p.knv).attr("tabindex","-1").focus()},0)}})},restoreCell:function(d,f){return this.each(function(){var a=this,c;if(!(!a.grid||a.p.cellEdit!==true)){c=a.p.savedRow.length>=1?
0:null;if(c!==null){var h=b("td:eq("+f+")",a.rows[d]);if(b.isFunction(b.fn.datepicker))try{b("input.hasDatepicker",h).datepicker("hide")}catch(e){}b(h).empty().attr("tabindex","-1");b(a).jqGrid("setCell",a.rows[d].id,f,a.p.savedRow[c].v,false,false,true);b.isFunction(a.p.afterRestoreCell)&&a.p.afterRestoreCell.call(a,a.rows[d].id,a.p.savedRow[c].v,d,f);a.p.savedRow.splice(0,1)}window.setTimeout(function(){b("#"+a.p.knv).attr("tabindex","-1").focus()},0)}})},nextCell:function(d,f){return this.each(function(){var a=
false;if(!(!this.grid||this.p.cellEdit!==true)){for(var c=f+1;c<this.p.colModel.length;c++)if(this.p.colModel[c].editable===true){a=c;break}if(a!==false)b(this).jqGrid("editCell",d,a,true);else this.p.savedRow.length>0&&b(this).jqGrid("saveCell",d,f)}})},prevCell:function(d,f){return this.each(function(){var a=false;if(!(!this.grid||this.p.cellEdit!==true)){for(var c=f-1;c>=0;c--)if(this.p.colModel[c].editable===true){a=c;break}if(a!==false)b(this).jqGrid("editCell",d,a,true);else this.p.savedRow.length>
0&&b(this).jqGrid("saveCell",d,f)}})},GridNav:function(){return this.each(function(){function d(g,i,k){if(k.substr(0,1)=="v"){var j=b(a.grid.bDiv)[0].clientHeight,m=b(a.grid.bDiv)[0].scrollTop,l=a.rows[g].offsetTop+a.rows[g].clientHeight,q=a.rows[g].offsetTop;if(k=="vd")if(l>=j)b(a.grid.bDiv)[0].scrollTop=b(a.grid.bDiv)[0].scrollTop+a.rows[g].clientHeight;if(k=="vu")if(q<m)b(a.grid.bDiv)[0].scrollTop=b(a.grid.bDiv)[0].scrollTop-a.rows[g].clientHeight}if(k=="h"){k=b(a.grid.bDiv)[0].clientWidth;j=b(a.grid.bDiv)[0].scrollLeft;
m=a.rows[g].cells[i].offsetLeft;if(a.rows[g].cells[i].offsetLeft+a.rows[g].cells[i].clientWidth>=k+parseInt(j,10))b(a.grid.bDiv)[0].scrollLeft=b(a.grid.bDiv)[0].scrollLeft+a.rows[g].cells[i].clientWidth;else if(m<j)b(a.grid.bDiv)[0].scrollLeft=b(a.grid.bDiv)[0].scrollLeft-a.rows[g].cells[i].clientWidth}}function f(g,i){var k,j;if(i=="lft"){k=g+1;for(j=g;j>=0;j--)if(a.p.colModel[j].hidden!==true){k=j;break}}if(i=="rgt"){k=g-1;for(j=g;j<a.p.colModel.length;j++)if(a.p.colModel[j].hidden!==true){k=j;
break}}return k}var a=this;if(!(!a.grid||a.p.cellEdit!==true)){a.p.knv=a.p.id+"_kn";var c=b("<span style='width:0px;height:0px;background-color:black;' tabindex='0'><span tabindex='-1' style='width:0px;height:0px;background-color:grey' id='"+a.p.knv+"'></span></span>"),h,e;b(c).insertBefore(a.grid.cDiv);b("#"+a.p.knv).focus().keydown(function(g){e=g.keyCode;if(a.p.direction=="rtl")if(e==37)e=39;else if(e==39)e=37;switch(e){case 38:if(a.p.iRow-1>0){d(a.p.iRow-1,a.p.iCol,"vu");b(a).jqGrid("editCell",
a.p.iRow-1,a.p.iCol,false)}break;case 40:if(a.p.iRow+1<=a.rows.length-1){d(a.p.iRow+1,a.p.iCol,"vd");b(a).jqGrid("editCell",a.p.iRow+1,a.p.iCol,false)}break;case 37:if(a.p.iCol-1>=0){h=f(a.p.iCol-1,"lft");d(a.p.iRow,h,"h");b(a).jqGrid("editCell",a.p.iRow,h,false)}break;case 39:if(a.p.iCol+1<=a.p.colModel.length-1){h=f(a.p.iCol+1,"rgt");d(a.p.iRow,h,"h");b(a).jqGrid("editCell",a.p.iRow,h,false)}break;case 13:parseInt(a.p.iCol,10)>=0&&parseInt(a.p.iRow,10)>=0&&b(a).jqGrid("editCell",a.p.iRow,a.p.iCol,
true)}return false})}})},getChangedCells:function(d){var f=[];d||(d="all");this.each(function(){var a=this,c;!a.grid||a.p.cellEdit!==true||b(a.rows).each(function(h){var e={};if(b(this).hasClass("edited")){b("td",this).each(function(g){c=a.p.colModel[g].name;if(c!=="cb"&&c!=="subgrid")if(d=="dirty"){if(b(this).hasClass("dirty-cell"))try{e[c]=b.unformat(this,{rowId:a.rows[h].id,colModel:a.p.colModel[g]},g)}catch(i){e[c]=b.jgrid.htmlDecode(b(this).html())}}else try{e[c]=b.unformat(this,{rowId:a.rows[h].id,
colModel:a.p.colModel[g]},g)}catch(k){e[c]=b.jgrid.htmlDecode(b(this).html())}});e.id=this.id;f.push(e)}})});return f}})})(jQuery);
(function(b){b.fn.jqm=function(a){var f={overlay:50,closeoverlay:true,overlayClass:"jqmOverlay",closeClass:"jqmClose",trigger:".jqModal",ajax:e,ajaxText:"",target:e,modal:e,toTop:e,onShow:e,onHide:e,onLoad:e};return this.each(function(){if(this._jqm)return j[this._jqm].c=b.extend({},j[this._jqm].c,a);l++;this._jqm=l;j[l]={c:b.extend(f,b.jqm.params,a),a:e,w:b(this).addClass("jqmID"+l),s:l};f.trigger&&b(this).jqmAddTrigger(f.trigger)})};b.fn.jqmAddClose=function(a){return o(this,a,"jqmHide")};b.fn.jqmAddTrigger=
function(a){return o(this,a,"jqmShow")};b.fn.jqmShow=function(a){return this.each(function(){b.jqm.open(this._jqm,a)})};b.fn.jqmHide=function(a){return this.each(function(){b.jqm.close(this._jqm,a)})};b.jqm={hash:{},open:function(a,f){var c=j[a],d=c.c,i="."+d.closeClass,g=parseInt(c.w.css("z-index"));g=g>0?g:3E3;var h=b("<div></div>").css({height:"100%",width:"100%",position:"fixed",left:0,top:0,"z-index":g-1,opacity:d.overlay/100});if(c.a)return e;c.t=f;c.a=true;c.w.css("z-index",g);if(d.modal){k[0]||
setTimeout(function(){p("bind")},1);k.push(a)}else if(d.overlay>0)d.closeoverlay&&c.w.jqmAddClose(h);else h=e;c.o=h?h.addClass(d.overlayClass).prependTo("body"):e;if(q){b("html,body").css({height:"100%",width:"100%"});if(h){h=h.css({position:"absolute"})[0];for(var m in{Top:1,Left:1})h.style.setExpression(m.toLowerCase(),"(_=(document.documentElement.scroll"+m+" || document.body.scroll"+m+"))+'px'")}}if(d.ajax){g=d.target||c.w;h=d.ajax;g=typeof g=="string"?b(g,c.w):b(g);h=h.substr(0,1)=="@"?b(f).attr(h.substring(1)):
h;g.html(d.ajaxText).load(h,function(){d.onLoad&&d.onLoad.call(this,c);i&&c.w.jqmAddClose(b(i,c.w));r(c)})}else i&&c.w.jqmAddClose(b(i,c.w));d.toTop&&c.o&&c.w.before('<span id="jqmP'+c.w[0]._jqm+'"></span>').insertAfter(c.o);d.onShow?d.onShow(c):c.w.show();r(c);return e},close:function(a){a=j[a];if(!a.a)return e;a.a=e;if(k[0]){k.pop();k[0]||p("unbind")}a.c.toTop&&a.o&&b("#jqmP"+a.w[0]._jqm).after(a.w).remove();if(a.c.onHide)a.c.onHide(a);else{a.w.hide();a.o&&a.o.remove()}return e},params:{}};var l=
0,j=b.jqm.hash,k=[],q=b.browser.msie&&b.browser.version=="6.0",e=false,r=function(a){var f=b('<iframe src="javascript:false;document.write(\'\');" class="jqm"></iframe>').css({opacity:0});if(q)if(a.o)a.o.html('<p style="width:100%;height:100%"/>').prepend(f);else b("iframe.jqm",a.w)[0]||a.w.prepend(f);s(a)},s=function(a){try{b(":input:visible",a.w)[0].focus()}catch(f){}},p=function(a){b(document)[a]("keypress",n)[a]("keydown",n)[a]("mousedown",n)},n=function(a){var f=j[k[k.length-1]];(a=!b(a.target).parents(".jqmID"+
f.s)[0])&&s(f);return!a},o=function(a,f,c){return a.each(function(){var d=this._jqm;b(f).each(function(){if(!this[c]){this[c]=[];b(this).click(function(){for(var i in{jqmShow:1,jqmHide:1})for(var g in this[i])if(j[this[i][g]])j[this[i][g]].w[i](this);return e})}this[c].push(d)})})}})(jQuery);
(function(b){b.fn.jqDrag=function(a){return l(this,a,"d")};b.fn.jqResize=function(a,e){return l(this,a,"r",e)};b.jqDnR={dnr:{},e:0,drag:function(a){if(c.k=="d")d.css({left:c.X+a.pageX-c.pX,top:c.Y+a.pageY-c.pY});else{d.css({width:Math.max(a.pageX-c.pX+c.W,0),height:Math.max(a.pageY-c.pY+c.H,0)});M1&&f.css({width:Math.max(a.pageX-M1.pX+M1.W,0),height:Math.max(a.pageY-M1.pY+M1.H,0)})}return false},stop:function(){b(document).unbind("mousemove",i.drag).unbind("mouseup",i.stop)}};var i=b.jqDnR,c=i.dnr,
d=i.e,f,l=function(a,e,n,m){return a.each(function(){e=e?b(e,a):a;e.bind("mousedown",{e:a,k:n},function(g){var j=g.data,h={};d=j.e;f=m?b(m):false;if(d.css("position")!="relative")try{d.position(h)}catch(o){}c={X:h.left||k("left")||0,Y:h.top||k("top")||0,W:k("width")||d[0].scrollWidth||0,H:k("height")||d[0].scrollHeight||0,pX:g.pageX,pY:g.pageY,k:j.k};M1=f&&j.k!="d"?{X:h.left||f1("left")||0,Y:h.top||f1("top")||0,W:f[0].offsetWidth||f1("width")||0,H:f[0].offsetHeight||f1("height")||0,pX:g.pageX,pY:g.pageY,
k:j.k}:false;if(b("input.hasDatepicker",d[0])[0])try{b("input.hasDatepicker",d[0]).datepicker("hide")}catch(p){}b(document).mousemove(b.jqDnR.drag).mouseup(b.jqDnR.stop);return false})})},k=function(a){return parseInt(d.css(a))||false};f1=function(a){return parseInt(f.css(a))||false}})(jQuery);
(function(b){b.jgrid.extend({setSubGrid:function(){return this.each(function(){var f;this.p.subGridOptions=b.extend({plusicon:"ui-icon-plus",minusicon:"ui-icon-minus",openicon:"ui-icon-carat-1-sw",expandOnLoad:false,delayOnLoad:50,selectOnExpand:false,reloadOnExpand:true},this.p.subGridOptions||{});this.p.colNames.unshift("");this.p.colModel.unshift({name:"subgrid",width:b.browser.safari?this.p.subGridWidth+this.p.cellLayout:this.p.subGridWidth,sortable:false,resizable:false,hidedlg:true,search:false,
fixed:true});f=this.p.subGridModel;if(f[0]){f[0].align=b.extend([],f[0].align||[]);for(var d=0;d<f[0].name.length;d++)f[0].align[d]=f[0].align[d]||"left"}})},addSubGridCell:function(f,d){var a="",n,s;this.each(function(){a=this.formatCol(f,d);s=this.p.id;n=this.p.subGridOptions.plusicon});return'<td role="grid" aria-describedby="'+s+'_subgrid" class="ui-sgcollapsed sgcollapsed" '+a+"><a href='javascript:void(0);'><span class='ui-icon "+n+"'></span></a></td>"},addSubGrid:function(f,d){return this.each(function(){var a=
this;if(a.grid){var n=function(g,j,e){j=b("<td align='"+a.p.subGridModel[0].align[e]+"'></td>").html(j);b(g).append(j)},s=function(g,j){var e,c,h,k=b("<table cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>"),i=b("<tr></tr>");for(c=0;c<a.p.subGridModel[0].name.length;c++){e=b("<th class='ui-state-default ui-th-subgrid ui-th-column ui-th-"+a.p.direction+"'></th>");b(e).html(a.p.subGridModel[0].name[c]);b(e).width(a.p.subGridModel[0].width[c]);b(i).append(e)}b(k).append(i);if(g){h=
a.p.xmlReader.subgrid;b(h.root+" "+h.row,g).each(function(){i=b("<tr class='ui-widget-content ui-subtblcell'></tr>");if(h.repeatitems===true)b(h.cell,this).each(function(m){n(i,b(this).text()||"&#160;",m)});else{var o=a.p.subGridModel[0].mapping||a.p.subGridModel[0].name;if(o)for(c=0;c<o.length;c++)n(i,b(o[c],this).text()||"&#160;",c)}b(k).append(i)})}e=b("table:first",a.grid.bDiv).attr("id")+"_";b("#"+e+j).append(k);a.grid.hDiv.loading=false;b("#load_"+a.p.id).hide();return false},v=function(g,j){var e,
c,h,k,i,o=b("<table cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>"),m=b("<tr></tr>");for(c=0;c<a.p.subGridModel[0].name.length;c++){e=b("<th class='ui-state-default ui-th-subgrid ui-th-column ui-th-"+a.p.direction+"'></th>");b(e).html(a.p.subGridModel[0].name[c]);b(e).width(a.p.subGridModel[0].width[c]);b(m).append(e)}b(o).append(m);if(g){k=a.p.jsonReader.subgrid;e=g[k.root];if(typeof e!=="undefined")for(c=0;c<e.length;c++){h=e[c];m=b("<tr class='ui-widget-content ui-subtblcell'></tr>");
if(k.repeatitems===true){if(k.cell)h=h[k.cell];for(i=0;i<h.length;i++)n(m,h[i]||"&#160;",i)}else{var u=a.p.subGridModel[0].mapping||a.p.subGridModel[0].name;if(u.length)for(i=0;i<u.length;i++)n(m,h[u[i]]||"&#160;",i)}b(o).append(m)}}c=b("table:first",a.grid.bDiv).attr("id")+"_";b("#"+c+j).append(o);a.grid.hDiv.loading=false;b("#load_"+a.p.id).hide();return false},z=function(g){var j,e,c,h;j=b(g).attr("id");e={nd_:(new Date).getTime()};e[a.p.prmNames.subgridid]=j;if(!a.p.subGridModel[0])return false;
if(a.p.subGridModel[0].params)for(h=0;h<a.p.subGridModel[0].params.length;h++)for(c=0;c<a.p.colModel.length;c++)if(a.p.colModel[c].name==a.p.subGridModel[0].params[h])e[a.p.colModel[c].name]=b("td:eq("+c+")",g).text().replace(/\&#160\;/ig,"");if(!a.grid.hDiv.loading){a.grid.hDiv.loading=true;b("#load_"+a.p.id).show();if(!a.p.subgridtype)a.p.subgridtype=a.p.datatype;if(b.isFunction(a.p.subgridtype))a.p.subgridtype.call(a,e);else a.p.subgridtype=a.p.subgridtype.toLowerCase();switch(a.p.subgridtype){case "xml":case "json":b.ajax(b.extend({type:a.p.mtype,
url:a.p.subGridUrl,dataType:a.p.subgridtype,data:b.isFunction(a.p.serializeSubGridData)?a.p.serializeSubGridData.call(a,e):e,complete:function(k){a.p.subgridtype=="xml"?s(k.responseXML,j):v(b.jgrid.parse(k.responseText),j)}},b.jgrid.ajaxOptions,a.p.ajaxSubgridOptions||{}))}}return false},l,t,w,x=0,p,q;b.each(a.p.colModel,function(){if(this.hidden===true||this.name=="rn"||this.name=="cb")x++});var y=a.rows.length,r=1;if(d!==undefined&&d>0){r=d;y=d+1}for(;r<y;){b(a.rows[r]).hasClass("jqgrow")&&b(a.rows[r].cells[f]).bind("click",
function(){var g=b(this).parent("tr")[0];q=g.nextSibling;if(b(this).hasClass("sgcollapsed")){t=a.p.id;l=g.id;if(a.p.subGridOptions.reloadOnExpand===true||a.p.subGridOptions.reloadOnExpand===false&&!b(q).hasClass("ui-subgrid")){w=f>=1?"<td colspan='"+f+"'>&#160;</td>":"";p=true;if(b.isFunction(a.p.subGridBeforeExpand))p=a.p.subGridBeforeExpand.call(a,t+"_"+l,l);if(p===false)return false;b(g).after("<tr role='row' class='ui-subgrid'>"+w+"<td class='ui-widget-content subgrid-cell'><span class='ui-icon "+
a.p.subGridOptions.openicon+"'></span></td><td colspan='"+parseInt(a.p.colNames.length-1-x,10)+"' class='ui-widget-content subgrid-data'><div id="+t+"_"+l+" class='tablediv'></div></td></tr>");b.isFunction(a.p.subGridRowExpanded)?a.p.subGridRowExpanded.call(a,t+"_"+l,l):z(g)}else b(q).show();b(this).html("<a href='javascript:void(0);'><span class='ui-icon "+a.p.subGridOptions.minusicon+"'></span></a>").removeClass("sgcollapsed").addClass("sgexpanded");a.p.subGridOptions.selectOnExpand&&b(a).jqGrid("setSelection",
l)}else if(b(this).hasClass("sgexpanded")){p=true;if(b.isFunction(a.p.subGridRowColapsed)){l=g.id;p=a.p.subGridRowColapsed.call(a,t+"_"+l,l)}if(p===false)return false;if(a.p.subGridOptions.reloadOnExpand===true)b(q).remove(".ui-subgrid");else b(q).hasClass("ui-subgrid")&&b(q).hide();b(this).html("<a href='javascript:void(0);'><span class='ui-icon "+a.p.subGridOptions.plusicon+"'></span></a>").removeClass("sgexpanded").addClass("sgcollapsed")}return false});a.p.subGridOptions.expandOnLoad===true&&
b(a.rows[r].cells[f]).trigger("click");r++}a.subGridXml=function(g,j){s(g,j)};a.subGridJson=function(g,j){v(g,j)}}})},expandSubGridRow:function(f){return this.each(function(){if(this.grid||f)if(this.p.subGrid===true){var d=b(this).jqGrid("getInd",f,true);if(d)(d=b("td.sgcollapsed",d)[0])&&b(d).trigger("click")}})},collapseSubGridRow:function(f){return this.each(function(){if(this.grid||f)if(this.p.subGrid===true){var d=b(this).jqGrid("getInd",f,true);if(d)(d=b("td.sgexpanded",d)[0])&&b(d).trigger("click")}})},
toggleSubGridRow:function(f){return this.each(function(){if(this.grid||f)if(this.p.subGrid===true){var d=b(this).jqGrid("getInd",f,true);if(d){var a=b("td.sgcollapsed",d)[0];if(a)b(a).trigger("click");else(a=b("td.sgexpanded",d)[0])&&b(a).trigger("click")}}})}})})(jQuery);
(function(e){e.jgrid.extend({groupingSetup:function(){return this.each(function(){var a=this.p.groupingView;if(a!==null&&(typeof a==="object"||e.isFunction(a)))if(a.groupField.length){if(typeof a.visibiltyOnNextGrouping=="undefined")a.visibiltyOnNextGrouping=[];for(var b=0;b<a.groupField.length;b++){a.groupOrder[b]||(a.groupOrder[b]="asc");a.groupText[b]||(a.groupText[b]="{0}");if(typeof a.groupColumnShow[b]!="boolean")a.groupColumnShow[b]=true;if(typeof a.groupSummary[b]!="boolean")a.groupSummary[b]=
false;if(a.groupColumnShow[b]===true){a.visibiltyOnNextGrouping[b]=true;e(this).jqGrid("showCol",a.groupField[b])}else{a.visibiltyOnNextGrouping[b]=e("#"+this.p.id+"_"+a.groupField[b]).is(":visible");e(this).jqGrid("hideCol",a.groupField[b])}a.sortitems[b]=[];a.sortnames[b]=[];a.summaryval[b]=[];if(a.groupSummary[b]){a.summary[b]=[];for(var c=this.p.colModel,d=0,g=c.length;d<g;d++)c[d].summaryType&&a.summary[b].push({nm:c[d].name,st:c[d].summaryType,v:""})}}this.p.scroll=false;this.p.rownumbers=false;
this.p.subGrid=false;this.p.treeGrid=false;this.p.gridview=true}else this.p.grouping=false;else this.p.grouping=false})},groupingPrepare:function(a,b,c,d){this.each(function(){b[0]+="";var g=b[0].toString().split(" ").join(""),h=this.p.groupingView,f=this;if(c.hasOwnProperty(g))c[g].push(a);else{c[g]=[];c[g].push(a);h.sortitems[0].push(g);h.sortnames[0].push(e.trim(b[0].toString()));h.summaryval[0][g]=e.extend(true,[],h.summary[0])}h.groupSummary[0]&&e.each(h.summaryval[0][g],function(){this.v=e.isFunction(this.st)?
this.st.call(f,this.v,this.nm,d):e(f).jqGrid("groupingCalculations."+this.st,this.v,this.nm,d)})});return c},groupingToggle:function(a){this.each(function(){var b=this.p.groupingView,c=a.lastIndexOf("_"),d=a.substring(0,c+1);c=parseInt(a.substring(c+1),10)+1;var g=b.minusicon,h=b.plusicon,f=e("#"+a);f=f.length?f[0].nextSibling:null;var k=e("#"+a+" span.tree-wrap-"+this.p.direction),l=false;if(k.hasClass(g)){if(b.showSummaryOnHide&&b.groupSummary[0]){if(f)for(;f;){if(e(f).hasClass("jqfoot"))break;
e(f).hide();f=f.nextSibling}}else if(f)for(;f;){if(e(f).attr("id")==d+String(c))break;e(f).hide();f=f.nextSibling}k.removeClass(g).addClass(h);l=true}else{if(f)for(;f;){if(e(f).attr("id")==d+String(c))break;e(f).show();f=f.nextSibling}k.removeClass(h).addClass(g);l=false}e.isFunction(this.p.onClickGroup)&&this.p.onClickGroup.call(this,a,l)});return false},groupingRender:function(a,b){return this.each(function(){var c=this,d=c.p.groupingView,g="",h="",f,k="",l,r,m;if(!d.groupDataSorted){d.sortitems[0].sort();
d.sortnames[0].sort();if(d.groupOrder[0].toLowerCase()=="desc"){d.sortitems[0].reverse();d.sortnames[0].reverse()}}k=d.groupCollapse?d.plusicon:d.minusicon;k+=" tree-wrap-"+c.p.direction;for(m=0;m<b;){if(c.p.colModel[m].name==d.groupField[0]){r=m;break}m++}e.each(d.sortitems[0],function(o,n){f=c.p.id+"ghead_"+o;h="<span style='cursor:pointer;' class='ui-icon "+k+"' onclick=\"jQuery('#"+c.p.id+"').jqGrid('groupingToggle','"+f+"');return false;\"></span>";try{l=c.formatter(f,d.sortnames[0][o],r,d.sortitems[0])}catch(v){l=
d.sortnames[0][o]}g+='<tr id="'+f+'" role="row" class= "ui-widget-content jqgroup ui-row-'+c.p.direction+'"><td colspan="'+b+'">'+h+e.jgrid.format(d.groupText[0],l,a[n].length)+"</td></tr>";for(var i=0;i<a[n].length;i++)g+=a[n][i].join("");if(d.groupSummary[0]){i="";if(d.groupCollapse&&!d.showSummaryOnHide)i=' style="display:none;"';g+="<tr"+i+' role="row" class="ui-widget-content jqfoot ui-row-'+c.p.direction+'">';i=d.summaryval[0][n];for(var p=c.p.colModel,q,s=a[n].length,j=0;j<b;j++){var t="<td "+
c.formatCol(j,1,"")+">&#160;</td>",u="{0}";e.each(i,function(){if(this.nm==p[j].name){if(p[j].summaryTpl)u=p[j].summaryTpl;if(this.st=="avg")if(this.v&&s>0)this.v/=s;try{q=c.formatter("",this.v,j,this)}catch(w){q=this.v}t="<td "+c.formatCol(j,1,"")+">"+e.jgrid.format(u,q)+"</td>";return false}});g+=t}g+="</tr>"}});e("#"+c.p.id+" tbody:first").append(g);g=null})},groupingGroupBy:function(a,b){return this.each(function(){if(typeof a=="string")a=[a];var c=this.p.groupingView;this.p.grouping=true;for(var d=
0;d<c.groupField.length;d++)!c.groupColumnShow[d]&&c.visibiltyOnNextGrouping[d]&&e(this).jqGrid("showCol",c.groupField[d]);for(d=0;d<a.length;d++)c.visibiltyOnNextGrouping[d]=e("#"+this.p.id+"_"+a[d]).is(":visible");this.p.groupingView=e.extend(this.p.groupingView,b||{});c.groupField=a;e(this).trigger("reloadGrid")})},groupingRemove:function(a){return this.each(function(){if(typeof a=="undefined")a=true;this.p.grouping=false;if(a===true){for(var b=this.p.groupingView,c=0;c<b.groupField.length;c++)!b.groupColumnShow[c]&&
b.visibiltyOnNextGrouping[c]&&e(this).jqGrid("showCol",b.groupField);e("tr.jqgroup, tr.jqfoot","#"+this.p.id+" tbody:first").remove();e("tr.jqgrow:hidden","#"+this.p.id+" tbody:first").show()}else e(this).trigger("reloadGrid")})},groupingCalculations:{sum:function(a,b,c){return parseFloat(a||0)+parseFloat(c[b]||0)},min:function(a,b,c){if(a==="")return parseFloat(c[b]||0);return Math.min(parseFloat(a),parseFloat(c[b]||0))},max:function(a,b,c){if(a==="")return parseFloat(c[b]||0);return Math.max(parseFloat(a),
parseFloat(c[b]||0))},count:function(a,b,c){if(a==="")a=0;return c.hasOwnProperty(b)?a+1:0},avg:function(a,b,c){return parseFloat(a||0)+parseFloat(c[b]||0)}}})})(jQuery);
(function(d){d.jgrid.extend({setTreeNode:function(a,c){return this.each(function(){var b=this;if(b.grid&&b.p.treeGrid)for(var e=b.p.expColInd,g=b.p.treeReader.expanded_field,h=b.p.treeReader.leaf_field,f=b.p.treeReader.level_field,l=b.p.treeReader.icon_field,i=b.p.treeReader.loaded,j,o,n,k;a<c;){k=b.p.data[b.p._index[b.rows[a].id]];if(b.p.treeGridModel=="nested")if(!k[h]){j=parseInt(k[b.p.treeReader.left_field],10);o=parseInt(k[b.p.treeReader.right_field],10);k[h]=o===j+1?"true":"false";b.rows[a].cells[b.p._treeleafpos].innerHTML=
k[h]}j=parseInt(k[f],10);if(b.p.tree_root_level===0){n=j+1;o=j}else{n=j;o=j-1}n="<div class='tree-wrap tree-wrap-"+b.p.direction+"' style='width:"+n*18+"px;'>";n+="<div style='"+(b.p.direction=="rtl"?"right:":"left:")+o*18+"px;' class='ui-icon ";if(k[i]!==undefined)k[i]=k[i]=="true"||k[i]===true?true:false;if(k[h]=="true"||k[h]===true){n+=(k[l]!==undefined&&k[l]!==""?k[l]:b.p.treeIcons.leaf)+" tree-leaf treeclick'";k[h]=true;o="leaf"}else{k[h]=false;o=""}k[g]=(k[g]=="true"||k[g]===true?true:false)&&
k[i];n+=k[g]===false?k[h]===true?"'":b.p.treeIcons.plus+" tree-plus treeclick'":k[h]===true?"'":b.p.treeIcons.minus+" tree-minus treeclick'";n+="></div></div>";d(b.rows[a].cells[e]).wrapInner("<span class='cell-wrapper"+o+"'></span>").prepend(n);if(j!==parseInt(b.p.tree_root_level,10))(k=(k=d(b).jqGrid("getNodeParent",k))&&k.hasOwnProperty(g)?k[g]:true)||d(b.rows[a]).css("display","none");d(b.rows[a].cells[e]).find("div.treeclick").bind("click",function(m){m=d(m.target||m.srcElement,b.rows).closest("tr.jqgrow")[0].id;
m=b.p._index[m];if(!b.p.data[m][h])if(b.p.data[m][g]){d(b).jqGrid("collapseRow",b.p.data[m]);d(b).jqGrid("collapseNode",b.p.data[m])}else{d(b).jqGrid("expandRow",b.p.data[m]);d(b).jqGrid("expandNode",b.p.data[m])}return false});b.p.ExpandColClick===true&&d(b.rows[a].cells[e]).find("span.cell-wrapper").css("cursor","pointer").bind("click",function(m){m=d(m.target||m.srcElement,b.rows).closest("tr.jqgrow")[0].id;var r=b.p._index[m];if(!b.p.data[r][h])if(b.p.data[r][g]){d(b).jqGrid("collapseRow",b.p.data[r]);
d(b).jqGrid("collapseNode",b.p.data[r])}else{d(b).jqGrid("expandRow",b.p.data[r]);d(b).jqGrid("expandNode",b.p.data[r])}d(b).jqGrid("setSelection",m);return false});a++}})},setTreeGrid:function(){return this.each(function(){var a=this,c=0,b=false,e,g,h=[];if(a.p.treeGrid){a.p.treedatatype||d.extend(a.p,{treedatatype:a.p.datatype});a.p.subGrid=false;a.p.altRows=false;a.p.pgbuttons=false;a.p.pginput=false;a.p.gridview=true;if(a.p.rowTotal===null)a.p.rowNum=1E4;a.p.multiselect=false;a.p.rowList=[];a.p.expColInd=
0;a.p.treeIcons=d.extend({plus:"ui-icon-triangle-1-"+(a.p.direction=="rtl"?"w":"e"),minus:"ui-icon-triangle-1-s",leaf:"ui-icon-radio-off"},a.p.treeIcons||{});if(a.p.treeGridModel=="nested")a.p.treeReader=d.extend({level_field:"level",left_field:"lft",right_field:"rgt",leaf_field:"isLeaf",expanded_field:"expanded",loaded:"loaded",icon_field:"icon"},a.p.treeReader);else if(a.p.treeGridModel=="adjacency")a.p.treeReader=d.extend({level_field:"level",parent_id_field:"parent",leaf_field:"isLeaf",expanded_field:"expanded",
loaded:"loaded",icon_field:"icon"},a.p.treeReader);for(g in a.p.colModel)if(a.p.colModel.hasOwnProperty(g)){e=a.p.colModel[g].name;if(e==a.p.ExpandColumn&&!b){b=true;a.p.expColInd=c}c++;for(var f in a.p.treeReader)a.p.treeReader[f]==e&&h.push(e)}d.each(a.p.treeReader,function(l,i){if(i&&d.inArray(i,h)===-1){if(l==="leaf_field")a.p._treeleafpos=c;c++;a.p.colNames.push(i);a.p.colModel.push({name:i,width:1,hidden:true,sortable:false,resizable:false,hidedlg:true,editable:true,search:false})}})}})},expandRow:function(a){this.each(function(){var c=
this;if(c.grid&&c.p.treeGrid){var b=d(c).jqGrid("getNodeChildren",a),e=c.p.treeReader.expanded_field;d(b).each(function(){var g=d.jgrid.getAccessor(this,c.p.localReader.id);d("#"+g,c.grid.bDiv).css("display","");this[e]&&d(c).jqGrid("expandRow",this)})}})},collapseRow:function(a){this.each(function(){var c=this;if(c.grid&&c.p.treeGrid){var b=d(c).jqGrid("getNodeChildren",a),e=c.p.treeReader.expanded_field;d(b).each(function(){var g=d.jgrid.getAccessor(this,c.p.localReader.id);d("#"+g,c.grid.bDiv).css("display",
"none");this[e]&&d(c).jqGrid("collapseRow",this)})}})},getRootNodes:function(){var a=[];this.each(function(){var c=this;if(c.grid&&c.p.treeGrid)switch(c.p.treeGridModel){case "nested":var b=c.p.treeReader.level_field;d(c.p.data).each(function(){parseInt(this[b],10)===parseInt(c.p.tree_root_level,10)&&a.push(this)});break;case "adjacency":var e=c.p.treeReader.parent_id_field;d(c.p.data).each(function(){if(this[e]===null||String(this[e]).toLowerCase()=="null")a.push(this)})}});return a},getNodeDepth:function(a){var c=
null;this.each(function(){if(this.grid&&this.p.treeGrid)switch(this.p.treeGridModel){case "nested":c=parseInt(a[this.p.treeReader.level_field],10)-parseInt(this.p.tree_root_level,10);break;case "adjacency":c=d(this).jqGrid("getNodeAncestors",a).length}});return c},getNodeParent:function(a){var c=null;this.each(function(){if(this.grid&&this.p.treeGrid)switch(this.p.treeGridModel){case "nested":var b=this.p.treeReader.left_field,e=this.p.treeReader.right_field,g=this.p.treeReader.level_field,h=parseInt(a[b],
10),f=parseInt(a[e],10),l=parseInt(a[g],10);d(this.p.data).each(function(){if(parseInt(this[g],10)===l-1&&parseInt(this[b],10)<h&&parseInt(this[e],10)>f){c=this;return false}});break;case "adjacency":var i=this.p.treeReader.parent_id_field,j=this.p.localReader.id;d(this.p.data).each(function(){if(this[j]==a[i]){c=this;return false}})}});return c},getNodeChildren:function(a){var c=[];this.each(function(){if(this.grid&&this.p.treeGrid)switch(this.p.treeGridModel){case "nested":var b=this.p.treeReader.left_field,
e=this.p.treeReader.right_field,g=this.p.treeReader.level_field,h=parseInt(a[b],10),f=parseInt(a[e],10),l=parseInt(a[g],10);d(this.p.data).each(function(){parseInt(this[g],10)===l+1&&parseInt(this[b],10)>h&&parseInt(this[e],10)<f&&c.push(this)});break;case "adjacency":var i=this.p.treeReader.parent_id_field,j=this.p.localReader.id;d(this.p.data).each(function(){this[i]==a[j]&&c.push(this)})}});return c},getFullTreeNode:function(a){var c=[];this.each(function(){var b;if(this.grid&&this.p.treeGrid)switch(this.p.treeGridModel){case "nested":var e=
this.p.treeReader.left_field,g=this.p.treeReader.right_field,h=this.p.treeReader.level_field,f=parseInt(a[e],10),l=parseInt(a[g],10),i=parseInt(a[h],10);d(this.p.data).each(function(){parseInt(this[h],10)>=i&&parseInt(this[e],10)>=f&&parseInt(this[e],10)<=l&&c.push(this)});break;case "adjacency":if(a){c.push(a);var j=this.p.treeReader.parent_id_field,o=this.p.localReader.id;d(this.p.data).each(function(n){b=c.length;for(n=0;n<b;n++)if(c[n][o]==this[j]){c.push(this);break}})}}});return c},getNodeAncestors:function(a){var c=
[];this.each(function(){if(this.grid&&this.p.treeGrid)for(var b=d(this).jqGrid("getNodeParent",a);b;){c.push(b);b=d(this).jqGrid("getNodeParent",b)}});return c},isVisibleNode:function(a){var c=true;this.each(function(){if(this.grid&&this.p.treeGrid){var b=d(this).jqGrid("getNodeAncestors",a),e=this.p.treeReader.expanded_field;d(b).each(function(){c=c&&this[e];if(!c)return false})}});return c},isNodeLoaded:function(a){var c;this.each(function(){if(this.grid&&this.p.treeGrid){var b=this.p.treeReader.leaf_field;
c=a!==undefined?a.loaded!==undefined?a.loaded:a[b]||d(this).jqGrid("getNodeChildren",a).length>0?true:false:false}});return c},expandNode:function(a){return this.each(function(){if(this.grid&&this.p.treeGrid){var c=this.p.treeReader.expanded_field,b=this.p.treeReader.parent_id_field,e=this.p.treeReader.loaded,g=this.p.treeReader.level_field,h=this.p.treeReader.left_field,f=this.p.treeReader.right_field;if(!a[c]){var l=d.jgrid.getAccessor(a,this.p.localReader.id),i=d("#"+l,this.grid.bDiv)[0],j=this.p._index[l];
if(d(this).jqGrid("isNodeLoaded",this.p.data[j])){a[c]=true;d("div.treeclick",i).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus")}else{a[c]=true;d("div.treeclick",i).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus");this.p.treeANode=i.rowIndex;this.p.datatype=this.p.treedatatype;this.p.treeGridModel=="nested"?d(this).jqGrid("setGridParam",{postData:{nodeid:l,n_left:a[h],n_right:a[f],n_level:a[g]}}):d(this).jqGrid("setGridParam",
{postData:{nodeid:l,parentid:a[b],n_level:a[g]}});d(this).trigger("reloadGrid");a[e]=true;this.p.treeGridModel=="nested"?d(this).jqGrid("setGridParam",{postData:{nodeid:"",n_left:"",n_right:"",n_level:""}}):d(this).jqGrid("setGridParam",{postData:{nodeid:"",parentid:"",n_level:""}})}}}})},collapseNode:function(a){return this.each(function(){if(this.grid&&this.p.treeGrid)if(a.expanded){a.expanded=false;var c=d.jgrid.getAccessor(a,this.p.localReader.id);c=d("#"+c,this.grid.bDiv)[0];d("div.treeclick",
c).removeClass(this.p.treeIcons.minus+" tree-minus").addClass(this.p.treeIcons.plus+" tree-plus")}})},SortTree:function(a,c,b,e){return this.each(function(){if(this.grid&&this.p.treeGrid){var g,h,f,l=[],i=this,j;g=d(this).jqGrid("getRootNodes");g=d.jgrid.from(g);g.orderBy(a,c,b,e);j=g.select();g=0;for(h=j.length;g<h;g++){f=j[g];l.push(f);d(this).jqGrid("collectChildrenSortTree",l,f,a,c,b,e)}d.each(l,function(o){var n=d.jgrid.getAccessor(this,i.p.localReader.id);d("#"+i.p.id+" tbody tr:eq("+o+")").after(d("tr#"+
n,i.grid.bDiv))});l=j=g=null}})},collectChildrenSortTree:function(a,c,b,e,g,h){return this.each(function(){if(this.grid&&this.p.treeGrid){var f,l,i,j;f=d(this).jqGrid("getNodeChildren",c);f=d.jgrid.from(f);f.orderBy(b,e,g,h);j=f.select();f=0;for(l=j.length;f<l;f++){i=j[f];a.push(i);d(this).jqGrid("collectChildrenSortTree",a,i,b,e,g,h)}}})},setTreeRow:function(a,c){var b=false;this.each(function(){if(this.grid&&this.p.treeGrid)b=d(this).jqGrid("setRowData",a,c)});return b},delTreeNode:function(a){return this.each(function(){var c=
this.p.localReader.id,b=this.p.treeReader.left_field,e=this.p.treeReader.right_field,g,h,f;if(this.grid&&this.p.treeGrid){var l=this.p._index[a];if(l!==undefined){g=parseInt(this.p.data[l][e],10);h=g-parseInt(this.p.data[l][b],10)+1;l=d(this).jqGrid("getFullTreeNode",this.p.data[l]);if(l.length>0)for(var i=0;i<l.length;i++)d(this).jqGrid("delRowData",l[i][c]);if(this.p.treeGridModel==="nested"){c=d.jgrid.from(this.p.data).greater(b,g,{stype:"integer"}).select();if(c.length)for(f in c)c[f][b]=parseInt(c[f][b],
10)-h;c=d.jgrid.from(this.p.data).greater(e,g,{stype:"integer"}).select();if(c.length)for(f in c)c[f][e]=parseInt(c[f][e],10)-h}}}})},addChildNode:function(a,c,b){var e=this[0];if(b){var g=e.p.treeReader.expanded_field,h=e.p.treeReader.leaf_field,f=e.p.treeReader.level_field,l=e.p.treeReader.parent_id_field,i=e.p.treeReader.left_field,j=e.p.treeReader.right_field,o=e.p.treeReader.loaded,n,k,m,r,p;n=0;var s=c,t;if(!a){p=e.p.data.length-1;if(p>=0)for(;p>=0;){n=Math.max(n,parseInt(e.p.data[p][e.p.localReader.id],
10));p--}a=n+1}var u=d(e).jqGrid("getInd",c);t=false;if(c===undefined||c===null||c===""){s=c=null;n="last";r=e.p.tree_root_level;p=e.p.data.length+1}else{n="after";k=e.p._index[c];m=e.p.data[k];c=m[e.p.localReader.id];r=parseInt(m[f],10)+1;p=d(e).jqGrid("getFullTreeNode",m);if(p.length){s=p=p[p.length-1][e.p.localReader.id];p=d(e).jqGrid("getInd",s)+1}else p=d(e).jqGrid("getInd",c)+1;if(m[h]){t=true;m[g]=true;d(e.rows[u]).find("span.cell-wrapperleaf").removeClass("cell-wrapperleaf").addClass("cell-wrapper").end().find("div.tree-leaf").removeClass(e.p.treeIcons.leaf+
" tree-leaf").addClass(e.p.treeIcons.minus+" tree-minus");e.p.data[k][h]=false;m[o]=true}}k=p+1;b[g]=false;b[o]=true;b[f]=r;b[h]=true;if(e.p.treeGridModel==="adjacency")b[l]=c;if(e.p.treeGridModel==="nested"){var q;if(c!==null){h=parseInt(m[j],10);f=d.jgrid.from(e.p.data);f=f.greaterOrEquals(j,h,{stype:"integer"});f=f.select();if(f.length)for(q in f){f[q][i]=f[q][i]>h?parseInt(f[q][i],10)+2:f[q][i];f[q][j]=f[q][j]>=h?parseInt(f[q][j],10)+2:f[q][j]}b[i]=h;b[j]=h+1}else{h=parseInt(d(e).jqGrid("getCol",
j,false,"max"),10);f=d.jgrid.from(e.p.data).greater(i,h,{stype:"integer"}).select();if(f.length)for(q in f)f[q][i]=parseInt(f[q][i],10)+2;f=d.jgrid.from(e.p.data).greater(j,h,{stype:"integer"}).select();if(f.length)for(q in f)f[q][j]=parseInt(f[q][j],10)+2;b[i]=h+1;b[j]=h+2}}if(c===null||d(e).jqGrid("isNodeLoaded",m)||t){d(e).jqGrid("addRowData",a,b,n,s);d(e).jqGrid("setTreeNode",p,k)}m&&!m[g]&&d(e.rows[u]).find("div.treeclick").click()}}})})(jQuery);
(function(b){b.jgrid.extend({jqGridImport:function(a){a=b.extend({imptype:"xml",impstring:"",impurl:"",mtype:"GET",impData:{},xmlGrid:{config:"roots>grid",data:"roots>rows"},jsonGrid:{config:"grid",data:"data"},ajaxOptions:{}},a||{});return this.each(function(){var d=this,c=function(f,g){var e=b(g.xmlGrid.config,f)[0],h=b(g.xmlGrid.data,f)[0],i;if(xmlJsonClass.xml2json&&b.jgrid.parse){e=xmlJsonClass.xml2json(e," ");e=b.jgrid.parse(e);for(var l in e)if(e.hasOwnProperty(l))i=e[l];if(h){h=e.grid.datatype;
e.grid.datatype="xmlstring";e.grid.datastr=f;b(d).jqGrid(i).jqGrid("setGridParam",{datatype:h})}else b(d).jqGrid(i)}else alert("xml2json or parse are not present")},j=function(f,g){if(f&&typeof f=="string"){var e=b.jgrid.parse(f),h=e[g.jsonGrid.config];if(e=e[g.jsonGrid.data]){var i=h.datatype;h.datatype="jsonstring";h.datastr=e;b(d).jqGrid(h).jqGrid("setGridParam",{datatype:i})}else b(d).jqGrid(h)}};switch(a.imptype){case "xml":b.ajax(b.extend({url:a.impurl,type:a.mtype,data:a.impData,dataType:"xml",
complete:function(f,g){if(g=="success"){c(f.responseXML,a);b.isFunction(a.importComplete)&&a.importComplete(f)}}},a.ajaxOptions));break;case "xmlstring":if(a.impstring&&typeof a.impstring=="string"){var k=b.jgrid.stringToDoc(a.impstring);if(k){c(k,a);b.isFunction(a.importComplete)&&a.importComplete(k);a.impstring=null}k=null}break;case "json":b.ajax(b.extend({url:a.impurl,type:a.mtype,data:a.impData,dataType:"json",complete:function(f,g){if(g=="success"){j(f.responseText,a);b.isFunction(a.importComplete)&&
a.importComplete(f)}}},a.ajaxOptions));break;case "jsonstring":if(a.impstring&&typeof a.impstring=="string"){j(a.impstring,a);b.isFunction(a.importComplete)&&a.importComplete(a.impstring);a.impstring=null}}})},jqGridExport:function(a){a=b.extend({exptype:"xmlstring",root:"grid",ident:"\t"},a||{});var d=null;this.each(function(){if(this.grid){var c=b.extend({},b(this).jqGrid("getGridParam"));if(c.rownumbers){c.colNames.splice(0,1);c.colModel.splice(0,1)}if(c.multiselect){c.colNames.splice(0,1);c.colModel.splice(0,
1)}if(c.subGrid){c.colNames.splice(0,1);c.colModel.splice(0,1)}c.knv=null;if(c.treeGrid)for(var j in c.treeReader)if(c.treeReader.hasOwnProperty(j)){c.colNames.splice(c.colNames.length-1);c.colModel.splice(c.colModel.length-1)}switch(a.exptype){case "xmlstring":d="<"+a.root+">"+xmlJsonClass.json2xml(c,a.ident)+"</"+a.root+">";break;case "jsonstring":d="{"+xmlJsonClass.toJson(c,a.root,a.ident,false)+"}";if(c.postData.filters!==undefined){d=d.replace(/filters":"/,'filters":');d=d.replace(/}]}"/,"}]}")}}}});
return d},excelExport:function(a){a=b.extend({exptype:"remote",url:null,oper:"oper",tag:"excel",exportOptions:{}},a||{});return this.each(function(){if(this.grid){var d;if(a.exptype=="remote"){d=b.extend({},this.p.postData);d[a.oper]=a.tag;d=jQuery.param(d);d=a.url.indexOf("?")!=-1?a.url+"&"+d:a.url+"?"+d;window.location=d}}})}})})(jQuery);
var xmlJsonClass={xml2json:function(a,b){if(a.nodeType===9)a=a.documentElement;var g=this.toJson(this.toObj(this.removeWhite(a)),a.nodeName,"\t");return"{\n"+b+(b?g.replace(/\t/g,b):g.replace(/\t|\n/g,""))+"\n}"},json2xml:function(a,b){var g=function(d,c,i){var h="",k,j;if(d instanceof Array)if(d.length===0)h+=i+"<"+c+">__EMPTY_ARRAY_</"+c+">\n";else{k=0;for(j=d.length;k<j;k+=1){var l=i+g(d[k],c,i+"\t")+"\n";h+=l}}else if(typeof d==="object"){k=false;h+=i+"<"+c;for(j in d)if(d.hasOwnProperty(j))if(j.charAt(0)===
"@")h+=" "+j.substr(1)+'="'+d[j].toString()+'"';else k=true;h+=k?">":"/>";if(k){for(j in d)if(d.hasOwnProperty(j))if(j==="#text")h+=d[j];else if(j==="#cdata")h+="<![CDATA["+d[j]+"]]\>";else if(j.charAt(0)!=="@")h+=g(d[j],j,i+"\t");h+=(h.charAt(h.length-1)==="\n"?i:"")+"</"+c+">"}}else h+=typeof d==="function"?i+"<"+c+"><![CDATA["+d+"]]\></"+c+">":d.toString()==='""'||d.toString().length===0?i+"<"+c+">__EMPTY_STRING_</"+c+">":i+"<"+c+">"+d.toString()+"</"+c+">";return h},e="",f;for(f in a)if(a.hasOwnProperty(f))e+=
g(a[f],f,"");return b?e.replace(/\t/g,b):e.replace(/\t|\n/g,"")},toObj:function(a){var b={},g=/function/i;if(a.nodeType===1){if(a.attributes.length){var e;for(e=0;e<a.attributes.length;e+=1)b["@"+a.attributes[e].nodeName]=(a.attributes[e].nodeValue||"").toString()}if(a.firstChild){var f=e=0,d=false,c;for(c=a.firstChild;c;c=c.nextSibling)if(c.nodeType===1)d=true;else if(c.nodeType===3&&c.nodeValue.match(/[^ \f\n\r\t\v]/))e+=1;else if(c.nodeType===4)f+=1;if(d)if(e<2&&f<2){this.removeWhite(a);for(c=
a.firstChild;c;c=c.nextSibling)if(c.nodeType===3)b["#text"]=this.escape(c.nodeValue);else if(c.nodeType===4)if(g.test(c.nodeValue))b[c.nodeName]=[b[c.nodeName],c.nodeValue];else b["#cdata"]=this.escape(c.nodeValue);else if(b[c.nodeName])if(b[c.nodeName]instanceof Array)b[c.nodeName][b[c.nodeName].length]=this.toObj(c);else b[c.nodeName]=[b[c.nodeName],this.toObj(c)];else b[c.nodeName]=this.toObj(c)}else if(a.attributes.length)b["#text"]=this.escape(this.innerXml(a));else b=this.escape(this.innerXml(a));
else if(e)if(a.attributes.length)b["#text"]=this.escape(this.innerXml(a));else{b=this.escape(this.innerXml(a));if(b==="__EMPTY_ARRAY_")b="[]";else if(b==="__EMPTY_STRING_")b=""}else if(f)if(f>1)b=this.escape(this.innerXml(a));else for(c=a.firstChild;c;c=c.nextSibling)if(g.test(a.firstChild.nodeValue)){b=a.firstChild.nodeValue;break}else b["#cdata"]=this.escape(c.nodeValue)}if(!a.attributes.length&&!a.firstChild)b=null}else if(a.nodeType===9)b=this.toObj(a.documentElement);else alert("unhandled node type: "+
a.nodeType);return b},toJson:function(a,b,g,e){if(e===undefined)e=true;var f=b?'"'+b+'"':"",d="\t",c="\n";if(!e)c=d="";if(a==="[]")f+=b?":[]":"[]";else if(a instanceof Array){var i,h,k=[];h=0;for(i=a.length;h<i;h+=1)k[h]=this.toJson(a[h],"",g+d,e);f+=(b?":[":"[")+(k.length>1?c+g+d+k.join(","+c+g+d)+c+g:k.join(""))+"]"}else if(a===null)f+=(b&&":")+"null";else if(typeof a==="object"){i=[];for(h in a)if(a.hasOwnProperty(h))i[i.length]=this.toJson(a[h],h,g+d,e);f+=(b?":{":"{")+(i.length>1?c+g+d+i.join(","+
c+g+d)+c+g:i.join(""))+"}"}else f+=typeof a==="string"?(b&&":")+'"'+a.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"':(b&&":")+'"'+a.toString()+'"';return f},innerXml:function(a){var b="";if("innerHTML"in a)b=a.innerHTML;else{var g=function(e){var f="",d;if(e.nodeType===1){f+="<"+e.nodeName;for(d=0;d<e.attributes.length;d+=1)f+=" "+e.attributes[d].nodeName+'="'+(e.attributes[d].nodeValue||"").toString()+'"';if(e.firstChild){f+=">";for(d=e.firstChild;d;d=d.nextSibling)f+=g(d);f+="</"+e.nodeName+">"}else f+=
"/>"}else if(e.nodeType===3)f+=e.nodeValue;else if(e.nodeType===4)f+="<![CDATA["+e.nodeValue+"]]\>";return f};for(a=a.firstChild;a;a=a.nextSibling)b+=g(a)}return b},escape:function(a){return a.replace(/[\\]/g,"\\\\").replace(/[\"]/g,'\\"').replace(/[\n]/g,"\\n").replace(/[\r]/g,"\\r")},removeWhite:function(a){a.normalize();var b;for(b=a.firstChild;b;)if(b.nodeType===3)if(b.nodeValue.match(/[^ \f\n\r\t\v]/))b=b.nextSibling;else{var g=b.nextSibling;a.removeChild(b);b=g}else{b.nodeType===1&&this.removeWhite(b);
b=b.nextSibling}return a}};
function tableToGrid(n,o){jQuery(n).each(function(){if(!this.grid){jQuery(this).width("99%");var a=jQuery(this).width(),d=jQuery("tr td:first-child input[type=checkbox]:first",jQuery(this)),b=jQuery("tr td:first-child input[type=radio]:first",jQuery(this));d=d.length>0;b=!d&&b.length>0;var l=d||b,c=[],g=[];jQuery("th",jQuery(this)).each(function(){if(c.length===0&&l){c.push({name:"__selection__",index:"__selection__",width:0,hidden:true});g.push("__selection__")}else{c.push({name:jQuery(this).attr("id")||
jQuery.trim(jQuery.jgrid.stripHtml(jQuery(this).html())).split(" ").join("_"),index:jQuery(this).attr("id")||jQuery.trim(jQuery.jgrid.stripHtml(jQuery(this).html())).split(" ").join("_"),width:jQuery(this).width()||150});g.push(jQuery(this).html())}});var f=[],h=[],i=[];jQuery("tbody > tr",jQuery(this)).each(function(){var j={},e=0;jQuery("td",jQuery(this)).each(function(){if(e===0&&l){var k=jQuery("input",jQuery(this)),m=k.attr("value");h.push(m||f.length);k.attr("checked")&&i.push(m);j[c[e].name]=
k.attr("value")}else j[c[e].name]=jQuery(this).html();e++});e>0&&f.push(j)});jQuery(this).empty();jQuery(this).addClass("scroll");jQuery(this).jqGrid(jQuery.extend({datatype:"local",width:a,colNames:g,colModel:c,multiselect:d},o||{}));for(a=0;a<f.length;a++){b=null;if(h.length>0)if((b=h[a])&&b.replace)b=encodeURIComponent(b).replace(/[.\-%]/g,"_");if(b===null)b=a+1;jQuery(this).jqGrid("addRowData",b,f[a])}for(a=0;a<i.length;a++)jQuery(this).jqGrid("setSelection",i[a])}})};
(function(a){if(a.browser.msie&&a.browser.version==8)a.expr[":"].hidden=function(b){return b.offsetWidth===0||b.offsetHeight===0||b.style.display=="none"};a.jgrid._multiselect=false;if(a.ui)if(a.ui.multiselect){if(a.ui.multiselect.prototype._setSelected){var q=a.ui.multiselect.prototype._setSelected;a.ui.multiselect.prototype._setSelected=function(b,g){var c=q.call(this,b,g);if(g&&this.selectedList){var f=this.element;this.selectedList.find("li").each(function(){a(this).data("optionLink")&&a(this).data("optionLink").remove().appendTo(f)})}return c}}if(a.ui.multiselect.prototype.destroy)a.ui.multiselect.prototype.destroy=
function(){this.element.show();this.container.remove();a.Widget===undefined?a.widget.prototype.destroy.apply(this,arguments):a.Widget.prototype.destroy.apply(this,arguments)};a.jgrid._multiselect=true}a.jgrid.extend({sortableColumns:function(b){return this.each(function(){function g(){c.p.disableClick=true}var c=this,f=c.p.id;f={tolerance:"pointer",axis:"x",scrollSensitivity:"1",items:">th:not(:has(#jqgh_"+f+"_cb,#jqgh_"+f+"_rn,#jqgh_"+f+"_subgrid),:hidden)",placeholder:{element:function(h){return a(document.createElement(h[0].nodeName)).addClass(h[0].className+
" ui-sortable-placeholder ui-state-highlight").removeClass("ui-sortable-helper")[0]},update:function(h,j){j.height(h.currentItem.innerHeight()-parseInt(h.currentItem.css("paddingTop")||0,10)-parseInt(h.currentItem.css("paddingBottom")||0,10));j.width(h.currentItem.innerWidth()-parseInt(h.currentItem.css("paddingLeft")||0,10)-parseInt(h.currentItem.css("paddingRight")||0,10))}},update:function(h,j){var i=a(j.item).parent();i=a(">th",i);var l={},m=c.p.id+"_";a.each(c.p.colModel,function(k){l[this.name]=
k});var d=[];i.each(function(){var k=a(">div",this).get(0).id.replace(/^jqgh_/,"").replace(m,"");k in l&&d.push(l[k])});a(c).jqGrid("remapColumns",d,true,true);a.isFunction(c.p.sortable.update)&&c.p.sortable.update(d);setTimeout(function(){c.p.disableClick=false},50)}};if(c.p.sortable.options)a.extend(f,c.p.sortable.options);else if(a.isFunction(c.p.sortable))c.p.sortable={update:c.p.sortable};if(f.start){var e=f.start;f.start=function(h,j){g();e.call(this,h,j)}}else f.start=g;if(c.p.sortable.exclude)f.items+=
":not("+c.p.sortable.exclude+")";b.sortable(f).data("sortable").floating=true})},columnChooser:function(b){function g(d,k){if(d)if(typeof d=="string")a.fn[d]&&a.fn[d].apply(k,a.makeArray(arguments).slice(2));else a.isFunction(d)&&d.apply(k,a.makeArray(arguments).slice(2))}var c=this;if(!a("#colchooser_"+c[0].p.id).length){var f=a('<div id="colchooser_'+c[0].p.id+'" style="position:relative;overflow:hidden"><div><select multiple="multiple"></select></div></div>'),e=a("select",f);b=a.extend({width:420,
height:240,classname:null,done:function(d){d&&c.jqGrid("remapColumns",d,true)},msel:"multiselect",dlog:"dialog",dlog_opts:function(d){var k={};k[d.bSubmit]=function(){d.apply_perm();d.cleanup(false)};k[d.bCancel]=function(){d.cleanup(true)};return{buttons:k,close:function(){d.cleanup(true)},modal:d.modal?d.modal:false,resizable:d.resizable?d.resizable:true,width:d.width+20}},apply_perm:function(){a("option",e).each(function(){this.selected?c.jqGrid("showCol",h[this.value].name):c.jqGrid("hideCol",
h[this.value].name)});var d=[];a("option[selected]",e).each(function(){d.push(parseInt(this.value,10))});a.each(d,function(){delete i[h[parseInt(this,10)].name]});a.each(i,function(){var k=parseInt(this,10);var p=d,o=k;if(o>=0){var n=p.slice(),r=n.splice(o,Math.max(p.length-o,o));if(o>p.length)o=p.length;n[o]=k;d=n.concat(r)}else d=void 0});b.done&&b.done.call(c,d)},cleanup:function(d){g(b.dlog,f,"destroy");g(b.msel,e,"destroy");f.remove();d&&b.done&&b.done.call(c)},msel_opts:{}},a.jgrid.col,b||{});
if(a.ui)if(a.ui.multiselect)if(b.msel=="multiselect"){if(!a.jgrid._multiselect){alert("Multiselect plugin loaded after jqGrid. Please load the plugin before the jqGrid!");return}b.msel_opts=a.extend(a.ui.multiselect.defaults,b.msel_opts)}b.caption&&f.attr("title",b.caption);if(b.classname){f.addClass(b.classname);e.addClass(b.classname)}if(b.width){a(">div",f).css({width:b.width,margin:"0 auto"});e.css("width",b.width)}if(b.height){a(">div",f).css("height",b.height);e.css("height",b.height-10)}var h=
c.jqGrid("getGridParam","colModel"),j=c.jqGrid("getGridParam","colNames"),i={},l=[];e.empty();a.each(h,function(d){i[this.name]=d;if(this.hidedlg)this.hidden||l.push(d);else e.append("<option value='"+d+"' "+(this.hidden?"":"selected='selected'")+">"+j[d]+"</option>")});var m=a.isFunction(b.dlog_opts)?b.dlog_opts.call(c,b):b.dlog_opts;g(b.dlog,f,m);m=a.isFunction(b.msel_opts)?b.msel_opts.call(c,b):b.msel_opts;g(b.msel,e,m)}},sortableRows:function(b){return this.each(function(){var g=this;if(g.grid)if(!g.p.treeGrid)if(a.fn.sortable){b=
a.extend({cursor:"move",axis:"y",items:".jqgrow"},b||{});if(b.start&&a.isFunction(b.start)){b._start_=b.start;delete b.start}else b._start_=false;if(b.update&&a.isFunction(b.update)){b._update_=b.update;delete b.update}else b._update_=false;b.start=function(c,f){a(f.item).css("border-width","0px");a("td",f.item).each(function(j){this.style.width=g.grid.cols[j].style.width});if(g.p.subGrid){var e=a(f.item).attr("id");try{a(g).jqGrid("collapseSubGridRow",e)}catch(h){}}b._start_&&b._start_.apply(this,
[c,f])};b.update=function(c,f){a(f.item).css("border-width","");g.p.rownumbers===true&&a("td.jqgrid-rownum",g.rows).each(function(e){a(this).html(e+1)});b._update_&&b._update_.apply(this,[c,f])};a("tbody:first",g).sortable(b);a("tbody:first",g).disableSelection()}})},gridDnD:function(b){return this.each(function(){function g(){var e=a.data(c,"dnd");a("tr.jqgrow:not(.ui-draggable)",c).draggable(a.isFunction(e.drag)?e.drag.call(a(c),e):e.drag)}var c=this;if(c.grid)if(!c.p.treeGrid)if(a.fn.draggable&&
a.fn.droppable){a("#jqgrid_dnd").html()===null&&a("body").append("<table id='jqgrid_dnd' class='ui-jqgrid-dnd'></table>");if(typeof b=="string"&&b=="updateDnD"&&c.p.jqgdnd===true)g();else{b=a.extend({drag:function(e){return a.extend({start:function(h,j){if(c.p.subGrid){var i=a(j.helper).attr("id");try{a(c).jqGrid("collapseSubGridRow",i)}catch(l){}}for(i=0;i<a.data(c,"dnd").connectWith.length;i++)a(a.data(c,"dnd").connectWith[i]).jqGrid("getGridParam","reccount")=="0"&&a(a.data(c,"dnd").connectWith[i]).jqGrid("addRowData",
"jqg_empty_row",{});j.helper.addClass("ui-state-highlight");a("td",j.helper).each(function(m){this.style.width=c.grid.headers[m].width+"px"});e.onstart&&a.isFunction(e.onstart)&&e.onstart.call(a(c),h,j)},stop:function(h,j){if(j.helper.dropped){var i=a(j.helper).attr("id");a(c).jqGrid("delRowData",i)}for(i=0;i<a.data(c,"dnd").connectWith.length;i++)a(a.data(c,"dnd").connectWith[i]).jqGrid("delRowData","jqg_empty_row");e.onstop&&a.isFunction(e.onstop)&&e.onstop.call(a(c),h,j)}},e.drag_opts||{})},drop:function(e){return a.extend({accept:function(h){if(!a(h).hasClass("jqgrow"))return h;
var j=a(h).closest("table.ui-jqgrid-btable");if(j.length>0&&a.data(j[0],"dnd")!==undefined){h=a.data(j[0],"dnd").connectWith;return a.inArray("#"+this.id,h)!=-1?true:false}return h},drop:function(h,j){if(a(j.draggable).hasClass("jqgrow")){var i=a(j.draggable).attr("id");i=j.draggable.parent().parent().jqGrid("getRowData",i);if(!e.dropbyname){var l=0,m={},d,k=a("#"+this.id).jqGrid("getGridParam","colModel");try{for(var p in i){if(i.hasOwnProperty(p)&&k[l]){d=k[l].name;m[d]=i[p]}l++}i=m}catch(o){}}j.helper.dropped=
true;if(e.beforedrop&&a.isFunction(e.beforedrop)){d=e.beforedrop.call(this,h,j,i,a("#"+c.id),a(this));if(typeof d!="undefined"&&d!==null&&typeof d=="object")i=d}if(j.helper.dropped){var n;if(e.autoid)if(a.isFunction(e.autoid))n=e.autoid.call(this,i);else{n=Math.ceil(Math.random()*1E3);n=e.autoidprefix+n}a("#"+this.id).jqGrid("addRowData",n,i,e.droppos)}e.ondrop&&a.isFunction(e.ondrop)&&e.ondrop.call(this,h,j,i)}}},e.drop_opts||{})},onstart:null,onstop:null,beforedrop:null,ondrop:null,drop_opts:{activeClass:"ui-state-active",
hoverClass:"ui-state-hover"},drag_opts:{revert:"invalid",helper:"clone",cursor:"move",appendTo:"#jqgrid_dnd",zIndex:5E3},dropbyname:false,droppos:"first",autoid:true,autoidprefix:"dnd_"},b||{});if(b.connectWith){b.connectWith=b.connectWith.split(",");b.connectWith=a.map(b.connectWith,function(e){return a.trim(e)});a.data(c,"dnd",b);c.p.reccount!="0"&&!c.p.jqgdnd&&g();c.p.jqgdnd=true;for(var f=0;f<b.connectWith.length;f++)a(b.connectWith[f]).droppable(a.isFunction(b.drop)?b.drop.call(a(c),b):b.drop)}}}})},
gridResize:function(b){return this.each(function(){var g=this;if(g.grid&&a.fn.resizable){b=a.extend({},b||{});if(b.alsoResize){b._alsoResize_=b.alsoResize;delete b.alsoResize}else b._alsoResize_=false;if(b.stop&&a.isFunction(b.stop)){b._stop_=b.stop;delete b.stop}else b._stop_=false;b.stop=function(c,f){a(g).jqGrid("setGridParam",{height:a("#gview_"+g.p.id+" .ui-jqgrid-bdiv").height()});a(g).jqGrid("setGridWidth",f.size.width,b.shrinkToFit);b._stop_&&b._stop_.call(g,c,f)};b.alsoResize=b._alsoResize_?
eval("("+("{'#gview_"+g.p.id+" .ui-jqgrid-bdiv':true,'"+b._alsoResize_+"':true}")+")"):a(".ui-jqgrid-bdiv","#gview_"+g.p.id);delete b._alsoResize_;a("#gbox_"+g.p.id).resizable(b)}})}})})(jQuery);
