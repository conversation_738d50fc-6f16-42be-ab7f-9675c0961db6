package com.mega.eloan.lms.lrs.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.lrs.report.LMS1800R02RptService;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.eloan.lms.model.L180M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.report.ReportGenerator;

@Service("lms1800r02rptservcie")
public class LMS1800R02RptServiceImpl extends AbstractReportService implements
		LMS1800R02RptService {
	@Resource
	RetrialService retrialService;
	@Resource
	BranchService branchService;
	@Resource
	L180M01ADao l180m01aDao;
	
	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();

		return "report/lrs/LMS1800R02_" + locale.toString();
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		
		
		Properties properties = new Properties();

		properties.setProperty("celldistribution", "linebreak");
		rptGenerator.setProperties(properties);

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String dataDate = params.getString("dataDate") + "-01";

		ISearch isearch = l180m01aDao.createSearchTemplete();
		isearch.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
				CapDate.parseDate(dataDate));
		isearch.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				RetrialDocStatusEnum.編製中.getCode());
		isearch.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		isearch.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				user.getUnitNo());
		isearch.setMaxResults(Integer.MAX_VALUE);
		isearch.addOrderBy("branchId");
		List<L180M01A> l180m01as = l180m01aDao.find(isearch);

		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		int count = 0;
		for (L180M01A meta : l180m01as) {
			count++;
			Map<String, String> values = new HashMap<String, String>();
			int need = 0;
			int noNeed = 0;
			List<L180M01B> l180m01bs = retrialService.findL180M01BDefaultOrder(meta.getMainId());

			// 計算要覆審 不覆審件數
			for (L180M01B l180m01b : l180m01bs) {
				String docStataus1 = CapString.trimNull(l180m01b
						.getDocStatus1());
				if ("2".equals(docStataus1)) {
					noNeed++;
				} else {
					need++;
				}
			}
			values.put("ReportBean.column01", String.valueOf(count));
			values.put("ReportBean.column02", meta.getBranchId());
			values.put("ReportBean.column03",
					branchService.getBranchName(meta.getBranchId()));
			values.put("ReportBean.column04", String.valueOf(need));
			values.put("ReportBean.column05", String.valueOf(noNeed));
			list.add(values);
		}

		Map<String, String> prompts = new HashMap<String, String>();
		prompts.put("dataDate", params.getString("dataDate"));
		prompts.put("queryDate", CapDate.formatDate(new Date(), "yyyy-MM-dd"));
		prompts.put("branchName", branchService.getBranchName(user.getUnitNo()));
		rptGenerator.setRowsData(list);
		rptGenerator.setVariableData(prompts);

	}
	
	@Override
	public byte[] getContent(PageParameters params)
			throws FileNotFoundException, ReportException, IOException,
			Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			
			
		} catch (Exception ex) {
			LOGGER.error("[getContent] Exception!!", ex);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(ex));
			rptGenerator.setVariableData(rptVariableMap);
			baos = (ByteArrayOutputStream) rptGenerator
					.generateExceptionReport(LMSUtil.getLocale());
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException ex) {
					LOGGER.error("[getContent] Exception!!", ex.getMessage());
				}
			}

		}
		return baos.toByteArray();
	}
}
