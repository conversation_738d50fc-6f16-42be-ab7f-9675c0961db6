var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
		
	let listData = Array.isArray(json?.listData) ? json.listData : [];
	if (!listData.length) {
	    const dataStr = $("#listData").val();
	    if (dataStr) {
	        try { listData = JSON.parse(dataStr); } catch (e) { listData = []; }
	    }
	}
	ilog.debug("the Data : " + listData.length);
	
    if (listData.length) {
        ilog.debug(json.listData)
        var test = listData;
        var type1Length = test[0].groups.length;
        var type2Length = test[1].groups.length;
        
        var type1hint = [i18n.cls1161m04['type1.hint1'], i18n.cls1161m04['type1.hint2']];
        var type2hint = [i18n.cls1161m04['type2.hint1'], i18n.cls1161m04['type2.hint2']];
        
        var tmp1hint = 0;
        var tmp2hint = 0;
        
        
        var maxLength = Math.max(type1Length + type1hint.length, type2Length + type2hint.length);
        
        var type1 = test[0].type;
        var type2 = test[1].type;
        
        var groups1 = test[0].groups;
        var groups2 = test[1].groups;
                        
        var html = "";
        for (var i = 0; i < maxLength; i++) {
            html = html + "<tr style='vertical-align:top;'>" ;
            
            var subItems1 = null;
            if (i < type1Length) {
                subItems1 = groups1[i].subItems;
            }
            var subItems2 = null;
            if (i < type2Length) {
                subItems2 = groups2[i].subItems;
            }
            
            if (subItems1 != null) {
                //				alert("type1 : " + subItems1[0].subTitle);
                
                var sub = "";
                for (var b = 0; b < subItems1.length; b++) {
                	if($.trim(subItems1[b].subTitle)!=''){
                    sub = sub + "<label><input " + (subItems1[b].subValue == "Y" ? "checked" : "") + " type='checkbox' name='subItem_" + DOMPurify.sanitize(type1) + "_" + DOMPurify.sanitize(groups1[i].group) + "_" + DOMPurify.sanitize(subItems1[b].subItem) + "' id='subItem_" + DOMPurify.sanitize(type1) + "_" + DOMPurify.sanitize(groups1[i].group) + "_" + DOMPurify.sanitize(subItems1[b].subItem) + "' /><span class='field' name='subItemTitle_" + DOMPurify.sanitize(type1) + "_" + DOMPurify.sanitize(groups1[i].group) + "_" + DOMPurify.sanitize(subItems1[b].subItem) + "' id='subItemTitle_" + DOMPurify.sanitize(type1) + "_" + DOMPurify.sanitize(groups1[i].group) + "_" + DOMPurify.sanitize(subItems1[b].subItem) + "'>" + DOMPurify.sanitize(subItems1[b].subTitle) + "</span></label><br>";
                	}else{
                		//
                		sub = "<input type='hidden' name='subItem_" + DOMPurify.sanitize(type1) + "_" + DOMPurify.sanitize(groups1[i].group) + "_" + "' value=''>&nbsp; ";
                	}
                }
                
                html = html + "<td>" + sub + "</td>"
            }
            else 
                if (i < (type1Length + type1hint.length)) {
                    html = html + "<td><label>" + DOMPurify.sanitize(type1hint[tmp1hint++]) + "</label></td>"
                }
                else {
                    html = html + "<td>" + "</td>"
                }
            if (subItems2 != null) {
                //				alert("type2 : " + subItems2[0].subTitle);
                var sub = "";
                for (var b = 0; b < subItems2.length; b++) {
                	if($.trim(subItems2[b].subTitle)!=''){
                	sub = sub + "<label><input " + (subItems2[b].subValue == "Y" ? "checked" : "") + " type='checkbox' name='subItem_" + DOMPurify.sanitize(type2) + "_" + DOMPurify.sanitize(groups2[i].group) + "_" + DOMPurify.sanitize(subItems2[b].subItem) + "' id='subItem_" + DOMPurify.sanitize(type2) + "_" + DOMPurify.sanitize(groups2[i].group) + "_" + DOMPurify.sanitize(subItems2[b].subItem) + "' /><span class='field' name='subItemTitle_" + DOMPurify.sanitize(type2) + "_" + DOMPurify.sanitize(groups2[i].group) + "_" + DOMPurify.sanitize(subItems2[b].subItem) + "' id='subItemTitle_" + DOMPurify.sanitize(type2) + "_" + DOMPurify.sanitize(groups2[i].group) + "_" + DOMPurify.sanitize(subItems2[b].subItem) + "'>" + DOMPurify.sanitize(subItems2[b].subTitle) + "</span></label><br>";
                	}else{
                		//
                		sub = "<input type='hidden' name='subItem_" + DOMPurify.sanitize(type2) + "_" + DOMPurify.sanitize(groups2[i].group) + "_" + "' value=''>&nbsp; ";
                	}
                }
                html = html + "<td>" + sub + "</td>"
            }
            else 
                if (i < (type2Length + type2hint.length)) {
                    html = html + "<td><label>" + DOMPurify.sanitize(type2hint[tmp2hint++]) + "</label></td>"
                }
                else {
                    html = html + "<td>" + "</td>"
                }
            html = html + "</tr>"
            
        }
		/* 連同多餘格子一起隱藏
		$("#checkListTable").append(html);
		var auth = (json?.Auth) || (typeof responseJSON !== "undefined" ? responseJSON.Auth : {}) || {}; //權限
		var docStatus = json?.mainDocStatus || (typeof responseJSON !== "undefined" ? responseJSON.mainDocStatus : "");
		var $btnRow = $("#checkListTable tr.checkListHead").first();
		if (auth.readOnly || docStatus !== "01O") {
		           // 1) 非編制中 → a. checkbox 鎖唯讀　b. 按鈕列整行隱藏 
		           $("input[name^='subItem_'][type='checkbox']").prop("disabled", true);
		           $btnRow.hide();          // 或用 .remove() 完全拿掉都可以
		       } else {
		           // 2) 編制中 → a. checkbox 可編輯　b. 顯示四顆按鈕
		           $btnRow.find(".fg-buttonset").show()
		                  .find("button").show();     // 清掉行內的 display:none
		       }
		*/
        $("#checkListTable").append(html);
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
            $("input[name^='subItem_'][type='checkbox']").prop("disabled", true)
        }
        
    }
	
	//UPGRADE
	function buildSubCheckboxes(subItems, type, group) {
	    return subItems.map(function (item) {
	        if ($.trim(item.subTitle) === "") {
	            return "<input type='hidden' name='subItem_" + DOMPurify.sanitize(type) + "_" +
	                   DOMPurify.sanitize(group) + "_' value=''>&nbsp; ";
	        }
	        return "<label><input " + (item.subValue === "Y" ? "checked" : "") +
	               " type='checkbox' name='subItem_" + DOMPurify.sanitize(type) + "_" +
	               DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(item.subItem) +
	               "' id='subItem_" + DOMPurify.sanitize(type) + "_" + DOMPurify.sanitize(group) +
	               "_" + DOMPurify.sanitize(item.subItem) +
	               "' /><span class='field' name='subItemTitle_" + DOMPurify.sanitize(type) + "_" +
	               DOMPurify.sanitize(group) + "_" + DOMPurify.sanitize(item.subItem) +
	               "' id='subItemTitle_" + DOMPurify.sanitize(type) + "_" + DOMPurify.sanitize(group) +
	               "_" + DOMPurify.sanitize(item.subItem) + "'>" +
	               DOMPurify.sanitize(item.subTitle) + "</span></label><br>";
	    }).join("");
	}

    
    
    $("#importNewList").click(function(){
    
        var type1hint = [i18n.cls1161m04['type1.hint1'], i18n.cls1161m04['type1.hint2']];
        var type2hint = [i18n.cls1161m04['type2.hint1'], i18n.cls1161m04['type2.hint2']];
        
        var tmp1hint = 0;
        var tmp2hint = 0;
        
        
        promptUI(i18n.cls1161m04['l250m01a.message.08']).done(function(){
            $("#checkListTable tr").filter(':not(".checkListHead")').remove()
            $.ajax({
				url  : "../../cls/cls1161m04/02",      // 依專案路徑自行調整
				type : "POST",
				data : {
				    _pa         : "cls1161m04formhandler",   // ★最重要
				    formAction  : "genList",
				    page        : 2,
				    mainOid     : $("#mainOid").val(),
				    txCode      : responseJSON.txCode,
				    docUrl      : "/cls/cls1161m04"
				}
			}).done(function(obj){
				/* ---------- ① 先把資料轉成安全陣列 ---------- */
				    let list = obj?.checkList;
				    if (typeof list === "string") {
				        try { list = JSON.parse(list); } catch (e) { list = []; }
				    }
				    if (!Array.isArray(list)) { list = []; }

				    /* ---------- ② 最少要有兩筆 (type1 & type2) ---------- */
				    if (list.length < 2 ||
				        !Array.isArray(list[0]?.groups) ||
				        !Array.isArray(list[1]?.groups)) {

				        API.showErrorMessage("後端未傳回完整的檢核資料，無法載入最新清單！");
				        return;          // ← 終止後續組表，避免再丟 JS error
				    }

					/* ---------- ③ 進入原本的繪表流程 ---------- */
					const type1Info   = list[0];
					const type2Info   = list[1];
					const type1Groups = type1Info.groups || [];
					const type2Groups = type2Info.groups || [];

					const type1Length = type1Groups.length;
					const type2Length = type2Groups.length;
					const maxLength   = Math.max(
					    type1Length + type1hint.length,
					    type2Length + type2hint.length
					);

					let html = "";
					for (let i = 0; i < maxLength; i++) {

					    const subItems1 = i < type1Length ? type1Groups[i].subItems : null;
					    const subItems2 = i < type2Length ? type2Groups[i].subItems : null;

					    html += "<tr style='vertical-align:top;'>";

					    /* ---------- 左欄（type1） ---------- */
					    if (subItems1) {
					        html += "<td>" + buildSubCheckboxes(subItems1, type1Info.type, type1Groups[i].group) + "</td>";
					    } else if (i < type1Length + type1hint.length) {
					        html += "<td><label>" + DOMPurify.sanitize(type1hint[tmp1hint++]) + "</label></td>";
					    } else {
					        html += "<td></td>";
					    }

					    /* ---------- 右欄（type2） ---------- */
					    if (subItems2) {
					        html += "<td>" + buildSubCheckboxes(subItems2, type2Info.type, type2Groups[i].group) + "</td>";
					    } else if (i < type2Length + type2hint.length) {
					        html += "<td><label>" + DOMPurify.sanitize(type2hint[tmp2hint++]) + "</label></td>";
					    } else {
					        html += "<td></td>";
					    }

					    html += "</tr>";
					}

					$("#checkListTable").append(html);

                    delete obj.checkList;
                    $("input[name^='project']").prop("checked", false);
                    $("#tabForm").injectData(obj);
            });
        });
        
        
    });
    
    var promptUI = function(msg){
        var deferred = $.Deferred();
        if ($("#checkListTable").find("tr").length > 0) {
            API.confirmMessage(msg, function(result){
                if (result) {
                    deferred.resolve();
                }
            });
        }
        else {
            deferred.resolve();
        }
        return deferred.promise();
    }
    
    $("#checkAll_left").click(function(){
        $("#checkListTable tr").filter(':not(".checkListHead")').find('td:even').find('input:eq(0)').prop("checked", true);
    })
    
    $("#cancelAll_left").click(function(){
        $("#checkListTable tr").filter(':not(".checkListHead")').find('td:even').find('input').prop("checked", false);
    })
    
    $("#checkAll_right").click(function(){
        $("#checkListTable tr").filter(':not(".checkListHead")').find('td:odd').find('input:eq(0)').prop("checked", true);
    })
    
    $("#cancelAll_right").click(function(){
        $("#checkListTable tr").filter(':not(".checkListHead")').find('td:odd').find('input').prop("checked", false);
    })
    
});
