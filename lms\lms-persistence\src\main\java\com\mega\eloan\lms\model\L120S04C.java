/* 
 * L120S04C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 關係戶於本行往來實績彙總表明細檔 **/
@Entity
//@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S04C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S04C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 種類<p/>
	 * 借款人與本行往來實績彙總表 | 1<br/>
	 *  借款人暨關係戶與本行往來實績彙總表 | 2
	 */
	@Column(name="DOCKIND", length=1, columnDefinition="CHAR(1)")
	private String docKind;

	/** 
	 * 資料年月<p/>
	 * 1~2.YYYY年<br/>
	 *  3.YYYY/MM~MM月<br/>
	 *  Miller edited at <br/>
	 *  2012/11/1
	 */
	@Column(name="DOCDATE", length=20, columnDefinition="VARCHAR(20)")
	private String docDate;

	/** 
	 * 資料年月(迄)<p/>
	 * (年月)YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DOCDATEE", columnDefinition="DATE")
	private Date docDateE;

	/** 
	 * 平均存款－金額<p/>
	 * TWD仟元
	 */
	@Column(name="AVGDEPOSITAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal avgDepositAmt;

	/** 
	 * A平均授信－金額<p/>
	 * TWD仟元
	 */
	@Column(name="AVGLOANAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal avgLoanAmt;

	/** 
	 * Ｂ應收帳款無追索買方承購平均餘額－金額<p/>
	 * TWD仟元
	 */
	@Column(name="RCVBUYAVGAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rcvBuyAvgAmt;

	/** 
	 * Ｃ應收帳款無追索權賣方融資平均餘額－金額<p/>
	 * TWD仟元
	 */
	@Column(name="RCVSELLAVGAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal rcvSellAvgAmt;

	/** 
	 * 進押及匯出－金額<p/>
	 * USD仟元
	 */
	@Column(name="EXPORTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal exportAmt;

	/** 
	 * 出押及匯入－金額<p/>
	 * USD仟元
	 */
	@Column(name="IMPORTAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal importAmt;

	/** 
	 * Ｄ利潤貢獻－金額<p/>
	 * TWD仟元
	 */
	@Column(name="PROFITAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal profitAmt;

	/** 
	 * 報酬率<p/>
	 * D/(A-B+C) %
	 */
	@Column(name="PROFITRATE", columnDefinition="DECIMAL(15,2)")
	private BigDecimal profitRate;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 分項統一編號 **/
	@Column(name="KEYCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String keyCustId;
	
	/** 分項重覆序號 **/
	@Column(name="KEYDUPNO", length=1, columnDefinition="CHAR(1)")
	private String keyDupNo;
	
	/** 關係戶統編 **/
	@Column(name="RCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String rCustId;
	
	/** 關係戶重複序號 **/
	@Column(name="RDUPNO", length=1, columnDefinition="CHAR(1)")
	private String rDupNo;
	
	/** 年度排序 By RCUSTID **/
	@Column(name="PRINTSEQ1", columnDefinition="INTEGER")
	private Integer printSeq1;
	
	/** 
	 * 企業戶員工薪轉貢獻度－金額<p/>
	 * TWD仟元
	 */
	@Column(name="PROFITSALARYAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal profitSalaryAmt;
	
	/** 信託專戶利差 **/
	@Column(name="PROFITTRUSTFDTAAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal profitTrustFdtaAmt;
	
	/** 財富管理 **/
	@Column(name="WMAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal wmAmt;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得種類<p/>
	 * 借款人與本行往來實績彙總表 | 1<br/>
	 *  借款人暨關係戶與本行往來實績彙總表 | 2
	 */
	public String getDocKind() {
		return this.docKind;
	}
	/**
	 *  設定種類<p/>
	 *  借款人與本行往來實績彙總表 | 1<br/>
	 *  借款人暨關係戶與本行往來實績彙總表 | 2
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/** 
	 * 取得資料年月<p/>
	 * 1~2.YYYY年<br/>
	 *  3.YYYY/MM~MM月<br/>
	 *  Miller edited at <br/>
	 *  2012/11/1
	 */
	public String getDocDate() {
		return this.docDate;
	}
	/**
	 *  設定資料年月<p/>
	 *  1~2.YYYY年<br/>
	 *  3.YYYY/MM~MM月<br/>
	 *  Miller edited at <br/>
	 *  2012/11/1
	 **/
	public void setDocDate(String value) {
		this.docDate = value;
	}

	/** 
	 * 取得資料年月(迄)<p/>
	 * (年月)YYYY-MM-01
	 */
	public Date getDocDateE() {
		return this.docDateE;
	}
	/**
	 *  設定資料年月(迄)<p/>
	 *  (年月)YYYY-MM-01
	 **/
	public void setDocDateE(Date value) {
		this.docDateE = value;
	}

	/** 
	 * 取得平均存款－金額<p/>
	 * TWD仟元
	 */
	public BigDecimal getAvgDepositAmt() {
		return this.avgDepositAmt;
	}
	/**
	 *  設定平均存款－金額<p/>
	 *  TWD仟元
	 **/
	public void setAvgDepositAmt(BigDecimal value) {
		this.avgDepositAmt = value;
	}

	/** 
	 * 取得A平均授信－金額<p/>
	 * TWD仟元
	 */
	public BigDecimal getAvgLoanAmt() {
		return this.avgLoanAmt;
	}
	/**
	 *  設定A平均授信－金額<p/>
	 *  TWD仟元
	 **/
	public void setAvgLoanAmt(BigDecimal value) {
		this.avgLoanAmt = value;
	}

	/** 
	 * 取得Ｂ應收帳款無追索買方承購平均餘額－金額<p/>
	 * TWD仟元
	 */
	public BigDecimal getRcvBuyAvgAmt() {
		return this.rcvBuyAvgAmt;
	}
	/**
	 *  設定Ｂ應收帳款無追索買方承購平均餘額－金額<p/>
	 *  TWD仟元
	 **/
	public void setRcvBuyAvgAmt(BigDecimal value) {
		this.rcvBuyAvgAmt = value;
	}

	/** 
	 * 取得Ｃ應收帳款無追索權賣方融資平均餘額－金額<p/>
	 * TWD仟元
	 */
	public BigDecimal getRcvSellAvgAmt() {
		return this.rcvSellAvgAmt;
	}
	/**
	 *  設定Ｃ應收帳款無追索權賣方融資平均餘額－金額<p/>
	 *  TWD仟元
	 **/
	public void setRcvSellAvgAmt(BigDecimal value) {
		this.rcvSellAvgAmt = value;
	}

	/** 
	 * 取得進押及匯出－金額<p/>
	 * USD仟元
	 */
	public BigDecimal getExportAmt() {
		return this.exportAmt;
	}
	/**
	 *  設定進押及匯出－金額<p/>
	 *  USD仟元
	 **/
	public void setExportAmt(BigDecimal value) {
		this.exportAmt = value;
	}

	/** 
	 * 取得出押及匯入－金額<p/>
	 * USD仟元
	 */
	public BigDecimal getImportAmt() {
		return this.importAmt;
	}
	/**
	 *  設定出押及匯入－金額<p/>
	 *  USD仟元
	 **/
	public void setImportAmt(BigDecimal value) {
		this.importAmt = value;
	}

	/** 
	 * 取得Ｄ利潤貢獻－金額<p/>
	 * TWD仟元
	 */
	public BigDecimal getProfitAmt() {
		return this.profitAmt;
	}
	/**
	 *  設定Ｄ利潤貢獻－金額<p/>
	 *  TWD仟元
	 **/
	public void setProfitAmt(BigDecimal value) {
		this.profitAmt = value;
	}

	/** 
	 * 取得報酬率<p/>
	 * D/(A-B+C) %
	 */
	public BigDecimal getProfitRate() {
		return this.profitRate;
	}
	/**
	 *  設定報酬率<p/>
	 *  D/(A-B+C) %
	 **/
	public void setProfitRate(BigDecimal value) {
		this.profitRate = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 取得分項統一編號 **/
	public String getKeyCustId() {
		return keyCustId;
	}
	/** 設定分項統一編號 **/
	public void setKeyCustId(String keyCustId) {
		this.keyCustId = keyCustId;
	}
	
	/** 取得分項重覆序號 **/
	public String getKeyDupNo() {
		return keyDupNo;
	}
	/** 設定分項重覆序號 **/
	public void setKeyDupNo(String keyDupNo) {
		this.keyDupNo = keyDupNo;
	}
	
	/** 設定關係戶統一編號 **/
	public void setRCustId(String rCustId) {
		this.rCustId = rCustId;
	}
	
	/** 取得關係戶統一編號 **/
	public String getRCustId() {
		return rCustId;
	}
	
	/** 設定關係戶重覆序號 **/
	public void setRDupNo(String rDupNo) {
		this.rDupNo = rDupNo;
	}
	
	/** 取得關係戶重覆序號 **/
	public String getRDupNo() {
		return rDupNo;
	}
	public void setPrintSeq1(Integer printSeq1) {
		this.printSeq1 = printSeq1;
	}
	public Integer getPrintSeq1() {
		return printSeq1;
	}
	
	/**
	 * 設定企業戶員工薪轉貢獻度－金額
	 * @param profitSalaryAmt
	 */
	public void setProfitSalaryAmt(BigDecimal profitSalaryAmt) {
		this.profitSalaryAmt = profitSalaryAmt;
	}
	
	/**
	 * 取得企業戶員工薪轉貢獻度－金額
	 * @return
	 */
	public BigDecimal getProfitSalaryAmt() {
		return profitSalaryAmt;
	}
	
	/**
	 *  設定信託專戶利差
	 * @param profitTrustFdta
	 */
	public void setProfitTrustFdtaAmt(BigDecimal profitTrustFdtaAmt) {
		this.profitTrustFdtaAmt = profitTrustFdtaAmt;
	}
	
	/**
	 *  取得信託專戶利差
	 * @param profitTrustFdta
	 */
	public BigDecimal getProfitTrustFdtaAmt() {
		return profitTrustFdtaAmt;
	}
		
	public BigDecimal getWmAmt() {
		return wmAmt;
	}
	public void setWmAmt(BigDecimal wmAmt) {
		this.wmAmt = wmAmt;
	}	
}
