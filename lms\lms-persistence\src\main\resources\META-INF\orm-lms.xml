<?xml version="1.0" encoding="UTF-8"?>
<entity-mappings version="2.0"
 xmlns="http://java.sun.com/xml/ns/persistence/orm" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:schemaLocation="http://java.sun.com/xml/ns/persistence/orm http://java.sun.com/xml/ns/persistence/orm_2_0.xsd">
    
    <!-- COM -->
    <named-query name="Ddocfile.deleteByMainId">
        <query>
            delete from DocFile t where t.mainId = :mainId
        </query>
    </named-query>
    
    <named-query name="Ddocfile.cleanByDate">
        <query>
            delete from DocFile t where t.deletedTime = :deletedTime
            <!-- <![CDATA[delete from BDocFile t where t.deletedTime <= :enddate]]> -->
        </query>
    </named-query>
    <named-query name="TempData.deleteByMainId">
        <query>
            delete from TempData t where t.mainId = :mainId
        </query>
    </named-query>
    <!-- <named-query name="TempData.deleteByDocNo">
        <query>
            delete from TempData t where t.docNo = :docNo
        </query>
    </named-query> -->
    <named-query name="TempData.findEntityOidByMainId">
        <query>
            select t.entityOid from TempData t where t.mainId = :mainId
        </query>
    </named-query>
    <named-query name="DocLog.deleteByMetaOid">
        <query>
            delete from DocLog t where t.metaOid = :metaOid
        </query>
    </named-query>
    <named-query name="DocOpener.clearAll">
        <query>
            delete from DocOpener
        </query>
    </named-query>
    
    <!-- LMS -->
    <!--named-query name="L170M01B.deleteByMainId">
        <query>
            DELETE FROM L170M01B T WHERE T.mainId = :MAINID
        </query>
    </named-query-->
	<!-- 分行查詢異常通報明細資料 -->
	<named-query name="L130S01A.selSubL130s01a">
        <query>
			SELECT T0.oid, T0.Creator, T0.areaDecide, T0.areaMonth, T0.bigKind, 
			        T0.branchKind, T0.createBY, T0.createTime, T0.docDscr, T0.headDecide, 
			        T0.headMonth, T0.isStop, T0.isUnNormal, T0.mainId, T0.runDate, 
			        T0.seqAppend, T0.bigKind, T0.seqName, T0.seqNo, T0.seqShow, T0.setAmt, 
			        T0.setCurr, T0.setKind, T0.updateTime, T0.updater, T0.seqKind, T0.ctlDscr,
					CONCAT(TRIM(T0.bigKind),TRIM(T0.seqShow)) AS DEFINESTR1, 
					SUBSTRING(T0.seqNo,2) AS DEFINESTR2
			    FROM L130S01A T0 
			    WHERE (T0.mainId = :mainId AND T0.branchKind = :branchKind) 
			    ORDER BY T0.seqKind ASC, DEFINESTR1 ASC,
			        DEFINESTR2 ASC
        </query>
    </named-query>
	<!-- 營運中心查詢異常通報明細資料 -->
	<named-query name="L130S01A.selAreaL130s01a">
        <query>
			SELECT T0.oid, T0.Creator, 
					CASE WHEN T0.areaDecide='Y' THEN '1' ELSE '9' END AS AREADECIDE,
					T0.areaMonth, T0.bigKind, 
			        T0.branchKind, T0.createBY, T0.createTime, T0.docDscr, T0.headDecide, 
			        T0.headMonth, T0.isStop, T0.isUnNormal, T0.mainId, T0.runDate, 
			        T0.seqAppend, T0.bigKind, T0.seqName, T0.seqNo, T0.seqShow, T0.setAmt, 
			        T0.setCurr, T0.setKind, T0.updateTime, T0.updater,
					CASE WHEN T0.seqKind='2' THEN '1' ELSE '9' END AS SEQKIND, T0.ctlDscr,
					CONCAT(TRIM(T0.bigKind),TRIM(T0.seqShow)) AS DEFINESTR1, 
					SUBSTRING(T0.seqNo,2) AS DEFINESTR2
			    FROM L130S01A T0 
			    WHERE (T0.mainId = :mainId AND T0.branchKind IN ('1','2')) 
			    ORDER BY AREADECIDE ASC, SEQKIND ASC, DEFINESTR1 ASC,
			        DEFINESTR2 ASC
        </query>
    </named-query>
	<named-query name="L130S01A.selAreaL130s01a2">
        <query>
			SELECT T0.oid, T0.Creator, T0.areaDecide AS AREADECIDE,
					T0.areaMonth, T0.bigKind, 
			        T0.branchKind, T0.createBY, T0.createTime, T0.docDscr, T0.headDecide, 
			        T0.headMonth, T0.isStop, T0.isUnNormal, T0.mainId, T0.runDate, 
			        T0.seqAppend, T0.bigKind, T0.seqName, T0.seqNo, T0.seqShow, T0.setAmt, 
			        T0.setCurr, T0.setKind, T0.updateTime, T0.updater,
					T0.seqKind AS SEQKIND, T0.ctlDscr,
					CONCAT(TRIM(T0.bigKind),TRIM(T0.seqShow)) AS DEFINESTR1, 
					SUBSTRING(T0.seqNo,2) AS DEFINESTR2
			    FROM L130S01A T0 
			    WHERE (T0.mainId = :mainId AND T0.branchKind IN ('1','2')) 
			    ORDER BY AREADECIDE ASC, SEQKIND ASC, DEFINESTR1 ASC,
			        DEFINESTR2 ASC
        </query>
    </named-query>		
	<!-- 授管處查詢異常通報明細資料 -->
	<named-query name="L130S01A.selHeadL130s01a">
        <query>
			SELECT T0.oid, T0.Creator, T0.areaDecide, T0.areaMonth, T0.bigKind, 
			        T0.branchKind, T0.createBY, T0.createTime, T0.docDscr, 
					CASE WHEN T0.headDecide='Y' THEN '1' ELSE '9' END AS HEADDECIDE, 
			        T0.headMonth, T0.isStop, T0.isUnNormal, T0.mainId, T0.runDate, 
			        T0.seqAppend, T0.bigKind, T0.seqName, T0.seqNo, T0.seqShow, T0.setAmt, 
			        T0.setCurr, T0.setKind, T0.updateTime, T0.updater, 
					CASE WHEN T0.seqKind='2' THEN '1' ELSE '9' END AS SEQKIND, T0.ctlDscr,
					CONCAT(TRIM(T0.bigKind),TRIM(T0.seqShow)) AS DEFINESTR1, 
					SUBSTRING(T0.seqNo,2) AS DEFINESTR2					
			    FROM L130S01A T0 
			    WHERE (T0.mainId = :mainId AND T0.branchKind IN ('1','2','3')) 
			    ORDER BY HEADDECIDE ASC, SEQKIND ASC, DEFINESTR1 ASC,
			        DEFINESTR2 ASC
        </query>
    </named-query>
	<named-query name="L130S01A.selHeadL130s01a2">
        <query>
			SELECT T0.oid, T0.Creator, T0.areaDecide, T0.areaMonth, T0.bigKind, 
			        T0.branchKind, T0.createBY, T0.createTime, T0.docDscr, 
					T0.headDecide AS HEADDECIDE, 
			        T0.headMonth, T0.isStop, T0.isUnNormal, T0.mainId, T0.runDate, 
			        T0.seqAppend, T0.bigKind, T0.seqName, T0.seqNo, T0.seqShow, T0.setAmt, 
			        T0.setCurr, T0.setKind, T0.updateTime, T0.updater, 
					T0.seqKind AS SEQKIND, T0.ctlDscr,
					CONCAT(TRIM(T0.bigKind),TRIM(T0.seqShow)) AS DEFINESTR1, 
					SUBSTRING(T0.seqNo,2) AS DEFINESTR2					
			    FROM L130S01A T0 
			    WHERE (T0.mainId = :mainId AND T0.branchKind IN ('1','2','3')) 
			    ORDER BY HEADDECIDE ASC, SEQKIND ASC, DEFINESTR1 ASC,
			        DEFINESTR2 ASC
        </query>
    </named-query>		
	 <!-- 產生核准文號-->
    <named-query  name="L120M01A.findByEndDate">
      <query>
          SELECT  A.endDate ,MAX(A.signSeq) AS SEQ FROM L120M01A A  WHERE
				  A.endDate IS NOT NULL  AND A.deletedTime IS NULL AND A.docKind =:docKind GROUP BY A.endDate
        </query>
    </named-query>
    <named-query name="L170M01B.deleteByMainIdNotLnDataDate">
        <query>
            DELETE FROM L170M01B T WHERE T.mainId = :MAINID and T.lnDataDate is not null
        </query>
    </named-query>
    <named-query name="L170M01E.deleteByMainId">
        <query>
            DELETE FROM L170M01E T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L180M01B.deleteByMainId">
        <query>
            DELETE FROM L180M01B T WHERE T.mainId = :mainId AND  T.custId = :custId AND  T.dupNo = :dupNo AND  T.ctlType = :ctlType
        </query>
    </named-query>
	
    <named-query name="L180M01C.deleteByMainId">
        <query>
            DELETE FROM L180M01C T WHERE T.mainId = :mainId AND  T.custId = :custId AND  T.dupNo = :dupNo AND  T.ctlType = :ctlType
        </query>
    </named-query>
    <named-query name="L180A01A.delByMainId">
        <query>
            DELETE FROM L180A01A T WHERE T.mainId = :mainId
        </query>
    </named-query>
    <named-query name="L170M01A.selDocStatus">
        <query>
            select t.lastRetrialDate  from L170M01A t where t.custId = :custId And t.dupNo = :dupNo And t.ownBrId = :ownBrId And t.docStatus = :docStatus
            And createTime IN (select Max(t1.createTime) As time From L170M01A t1)
            GROUP BY t.custId
        </query>
    </named-query>
    <named-query name="L120M01A.getMaxCaseSeq">
        <query>
            select t.caseBrId, MAX(t.caseSeq) from L120M01A t where t.caseYear = :caseYear GROUP BY t.caseBrId
        </query>
    </named-query>
    <named-query name="L120M01A.getMaxCaseSeq2">
        <query>
            select new L120M01A(t.caseBrId, MAX(t.caseSeq)) from L120M01A t where t.caseYear = :caseYear GROUP BY t.caseBrId
        </query>
    </named-query>
    <named-query name="L180M01A.selMaxbatchNO">
        <query>
            SELECT t.branchId, MAX(t.batchNO), t.dataDate FROM L180M01A t WHERE t.dataDate BETWEEN :dataDateMin AND :dataDateMax GROUP BY t.branchId,t.dataDate
        </query>
    </named-query>
    <named-query name="L170M01E.selCrdType">
        <query>
            SELECT t.crdType ,t.grade  FROM L170M01E t WHERE t.mainId = :MAINID  AND t.custId =:CUSTID  AND t.dupNo = :DUPNO AND  t.crdType IN ('DB','DL','OU') AND t.timeFlag = :TIMEFLAG 
        </query>
    </named-query>
    	
    <named-query name="C240M01A.selMaxbatchNO">
        <query>
            SELECT t.branchId, MAX(t.batchNO), t.dataEndDate FROM C240M01A t WHERE t.dataEndDate BETWEEN :dataDateMin AND :dataDateMax GROUP BY t.branchId,t.dataEndDate
        </query>
    </named-query>
    <!-- 找出此案件簽報書下 同一借款人有幾種幣別 -->
    <named-query name="L140M01A.selCurr">
        <query>
            SELECT A.custId , A.dupNo,COUNT(DISTINCT  A.currentApplyCurr) as CURR  FROM L140M01A A,L120M01C C WHERE A.mainId =  C.refMainId AND C.mainId= :MAINID AND C.itemType= :ITEMTYPE AND A.deletedTime IS NULL GROUP BY A.custId , A.dupNo
        </query>
    </named-query>
    
    
    <named-query name="L120S05B.getGpcomamt">
        <query>
            SELECT SUM(t.gpComAmt+0.00)/1000 AS GPCOMAMT,
            SUM(t.gpRiskAmt+0.00)/1000 AS GPRISKAMT
            FROM L120S05B t WHERE t.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05B.getTotAmtB">
        <query>
            SELECT SUM(t.totAmtB+0.00)/1000 AS TOTAMTB,
            SUM(t.crdAmtB+0.00)/1000 AS CRDAMTB
            FROM L120S05B t WHERE t.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05D.getTotAmtB">
        <query>
            SELECT SUM(t.totAmtB+0.00)/1000 AS TOTAMTB,
            SUM(t.crdAmtB+0.00)/1000 AS CRDAMTB
            FROM L120S05D t WHERE t.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L170M01B.selCntrNo">
        <query>
            SELECT B.cntrNo,MAX(A.retrialDate) as retrialDate,A.lastRetrialDate
            FROM L170M01A A,L170M01B B
            WHERE A.mainId = B.mainId and B.cntrNo = :CNTRNO GROUP BY B.cntrNo,A.lastRetrialDate
        </query>
    </named-query>
    
    <named-query name="l192m01b.deleteByMainId">
        <query>
            DELETE FROM L192M01B T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="l192m01c.deleteByMainId">
        <query>
            DELETE FROM L192M01C T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="l192m01d.deleteByMainId">
        <query>
            DELETE FROM L192M01D T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="l192s01a.deleteByMainId">
        <query>
            DELETE FROM L192S01A T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="l192s02a.deleteByMainId">
        <query>
            DELETE FROM L192S02A T WHERE T.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S04A.sell120s04a">
        <query>
            SELECT SUM(t1.depTime) AS DEPTIME, SUM(t1.depFixed) AS DEPFIXED,SUM(t1.loanQuota) AS LOANQUOTA,SUM(t1.loanAvgBal) AS LOANAVGBAL,
			SUM(t1.exchgImpRec) AS EXCHGIMPREC,SUM(t1.exchgImpAmt) AS EXCHGIMPAMT,
			SUM(t1.exchgExpRec) AS EXCHGEXPREC,SUM(t1.exchgExpAmt) AS EXCHGEXPAMT,SUM(t1.exchgOutRec) AS EXCHGOUTREC,SUM(t1.exchgOutAmt) AS EXCHGOUTAMT,
			SUM(t1.exchgInRec) AS EXCHGINREC,SUM(t1.exchgInAmt) AS EXCHGINAMT,SUM(t1.derOption) AS DEROPTION,SUM(t1.derRateExchg) AS DERRATEEXCHG,
			SUM(t1.derCCS) AS DERCCS, SUM(t1.derSWAP) AS DERSWAP,
			SUM(t1.trustBond) AS TRUSTBOND,SUM(t1.trustFund) AS TRUSTFUND,SUM(t1.trustSetAcct) AS TRUSTSETACCT,
			SUM(t1.trustOther) AS TRUSTOTHER,SUM(t1.wealthTrust) AS WEALTHTRUST,SUM(t1.wealthInsCom) AS WEALTHINSCOM,SUM(t1.wealthInvest) AS WEALTHINVEST,
			SUM(t1.salaryRec) AS SALARYREC,SUM(t1.salaryFixed) AS SALARYFIXED,SUM(t1.salaryMortgage) AS SALARYMORTGAGE,
			SUM(t1.salaryConsumption) AS SALARYCONSUMPTION ,SUM(t1.salaryCard) AS SALARYCARD,SUM(t1.salaryNetwork) AS SALARYNETWORK,
			SUM(t1.cardCommercial) AS CARDCOMMERCIAL,MAX(t1.cardCoBranded) AS CARDCOBRANDED,SUM(t1.GEBTWDRec) AS GEBTWDREC,
			SUM(t1.GEBOTHRec) AS GEBOTHREC,SUM(t1.GEBLCRec) AS GEBLCREC,SUM(t1.profit) AS PROFIT,SUM(t1.cardNoneCommercial) AS CARDNONECOMMERCIAL,
			SUM(t1.profitSalary) AS PROFITSALARY,SUM(t1.profitTrustFdta) AS PROFITTRUSTFDTA,
			SUM(t1.rcvBuyFactAmt) AS RCVBUYFACTAMT,SUM(t1.rcvBuyAvgBal) AS RCVBUYAVGBAL
            FROM L120S04A t1
            WHERE t1.custRelation LIKE '%5%' AND t1.mainId = :MAINID
        </query>
    </named-query>
	<named-query name="L120S04A.sell120s04a_keyCustIdDupNo">
        <query>
            SELECT SUM(t1.depTime) AS DEPTIME, SUM(t1.depFixed) AS DEPFIXED,SUM(t1.loanQuota) AS LOANQUOTA,SUM(t1.loanAvgBal) AS LOANAVGBAL,
			SUM(t1.exchgImpRec) AS EXCHGIMPREC,SUM(t1.exchgImpAmt) AS EXCHGIMPAMT,
			SUM(t1.exchgExpRec) AS EXCHGEXPREC,SUM(t1.exchgExpAmt) AS EXCHGEXPAMT,SUM(t1.exchgOutRec) AS EXCHGOUTREC,SUM(t1.exchgOutAmt) AS EXCHGOUTAMT,
			SUM(t1.exchgInRec) AS EXCHGINREC,SUM(t1.exchgInAmt) AS EXCHGINAMT,SUM(t1.derOption) AS DEROPTION,SUM(t1.derRateExchg) AS DERRATEEXCHG,
			SUM(t1.derCCS) AS DERCCS, SUM(t1.derSWAP) AS DERSWAP,
			SUM(t1.trustBond) AS TRUSTBOND,SUM(t1.trustFund) AS TRUSTFUND,SUM(t1.trustSetAcct) AS TRUSTSETACCT,
			SUM(t1.trustOther) AS TRUSTOTHER,SUM(t1.wealthTrust) AS WEALTHTRUST,SUM(t1.wealthInsCom) AS WEALTHINSCOM,SUM(t1.wealthInvest) AS WEALTHINVEST,
			SUM(t1.salaryRec) AS SALARYREC,SUM(t1.salaryFixed) AS SALARYFIXED,SUM(t1.salaryMortgage) AS SALARYMORTGAGE,
			SUM(t1.salaryConsumption) AS SALARYCONSUMPTION ,SUM(t1.salaryCard) AS SALARYCARD,SUM(t1.salaryNetwork) AS SALARYNETWORK,
			SUM(t1.cardCommercial) AS CARDCOMMERCIAL,MAX(t1.cardCoBranded) AS CARDCOBRANDED,SUM(t1.GEBTWDRec) AS GEBTWDREC,
			SUM(t1.GEBOTHRec) AS GEBOTHREC,SUM(t1.GEBLCRec) AS GEBLCREC,SUM(t1.profit) AS PROFIT,SUM(t1.cardNoneCommercial) AS CARDNONECOMMERCIAL,
			SUM(t1.profitSalary) AS PROFITSALARY,SUM(t1.profitTrustFdta) AS PROFITTRUSTFDTA,
			SUM(t1.rcvBuyFactAmt) AS RCVBUYFACTAMT,SUM(t1.rcvBuyAvgBal) AS RCVBUYAVGBAL   
            FROM L120S04A t1
            WHERE t1.custRelation LIKE '%5%' AND t1.mainId = :MAINID
			and t1.keyCustId=:KEYCUSTID and t1.keyDupNo=:KEYDUPNO
        </query>
    </named-query>
    <named-query name="L120S04A.sell120s04a2">
        <query>
            SELECT SUM(t2.depTime) AS DEPTIME, SUM(t2.depFixed) AS DEPFIXED,SUM(t2.loanQuota) AS LOANQUOTA,SUM(t2.loanAvgBal) AS LOANAVGBAL,
			SUM(t2.exchgImpRec) AS EXCHGIMPREC,SUM(t2.exchgImpAmt) AS EXCHGIMPAMT,
			SUM(t2.exchgExpRec) AS EXCHGEXPREC,SUM(t2.exchgExpAmt) AS EXCHGEXPAMT,SUM(t2.exchgOutRec) AS EXCHGOUTREC,SUM(t2.exchgOutAmt) AS EXCHGOUTAMT,
			SUM(t2.exchgInRec) AS EXCHGINREC,SUM(t2.exchgInAmt) AS EXCHGINAMT,SUM(t2.derOption) AS DEROPTION,SUM(t2.derRateExchg) AS DERRATEEXCHG,
			SUM(t2.derCCS) AS DERCCS, SUM(t2.derSWAP) AS DERSWAP,
			SUM(t2.trustBond) AS TRUSTBOND,SUM(t2.trustFund) AS TRUSTFUND,SUM(t2.trustSetAcct) AS TRUSTSETACCT,
			SUM(t2.trustOther) AS TRUSTOTHER,SUM(t2.wealthTrust) AS WEALTHTRUST,SUM(t2.wealthInsCom) AS WEALTHINSCOM,SUM(t2.wealthInvest) AS WEALTHINVEST,
			SUM(t2.salaryRec) AS SALARYREC,SUM(t2.salaryFixed) AS SALARYFIXED,SUM(t2.salaryMortgage) AS SALARYMORTGAGE,
			SUM(t2.salaryConsumption) AS SALARYCONSUMPTION ,SUM(t2.salaryCard) AS SALARYCARD,SUM(t2.salaryNetwork) AS SALARYNETWORK,
			SUM(t2.cardCommercial) AS CARDCOMMERCIAL,MAX(t2.cardCoBranded) AS CARDCOBRANDED,SUM(t2.GEBTWDRec) AS GEBTWDREC,
			SUM(t2.GEBOTHRec) AS GEBOTHREC,SUM(t2.GEBLCRec) AS GEBLCREC,SUM(t2.profit) AS PROFIT,SUM(t2.cardNoneCommercial) AS CARDNONECOMMERCIAL,
			SUM(t2.profitSalary) AS PROFITSALARY,SUM(t2.profitTrustFdta) AS PROFITTRUSTFDTA,
			SUM(t2.rcvBuyFactAmt) AS RCVBUYFACTAMT,SUM(t2.rcvBuyAvgBal) AS RCVBUYAVGBAL  
			FROM L120S04A t2
            WHERE t2.custRelation LIKE '%6%' AND t2.mainId = :MAINID
        </query>
    </named-query>
	<named-query name="L120S04A.sell120s04a2_keyCustIdDupNo">
        <query>
            SELECT SUM(t2.depTime) AS DEPTIME, SUM(t2.depFixed) AS DEPFIXED,SUM(t2.loanQuota) AS LOANQUOTA,SUM(t2.loanAvgBal) AS LOANAVGBAL,
			SUM(t2.exchgImpRec) AS EXCHGIMPREC,SUM(t2.exchgImpAmt) AS EXCHGIMPAMT,
			SUM(t2.exchgExpRec) AS EXCHGEXPREC,SUM(t2.exchgExpAmt) AS EXCHGEXPAMT,SUM(t2.exchgOutRec) AS EXCHGOUTREC,SUM(t2.exchgOutAmt) AS EXCHGOUTAMT,
			SUM(t2.exchgInRec) AS EXCHGINREC,SUM(t2.exchgInAmt) AS EXCHGINAMT,SUM(t2.derOption) AS DEROPTION,SUM(t2.derRateExchg) AS DERRATEEXCHG,
			SUM(t2.derCCS) AS DERCCS, SUM(t2.derSWAP) AS DERSWAP,
			SUM(t2.trustBond) AS TRUSTBOND,SUM(t2.trustFund) AS TRUSTFUND,SUM(t2.trustSetAcct) AS TRUSTSETACCT,
			SUM(t2.trustOther) AS TRUSTOTHER,SUM(t2.wealthTrust) AS WEALTHTRUST,SUM(t2.wealthInsCom) AS WEALTHINSCOM,SUM(t2.wealthInvest) AS WEALTHINVEST,
			SUM(t2.salaryRec) AS SALARYREC,SUM(t2.salaryFixed) AS SALARYFIXED,SUM(t2.salaryMortgage) AS SALARYMORTGAGE,
			SUM(t2.salaryConsumption) AS SALARYCONSUMPTION ,SUM(t2.salaryCard) AS SALARYCARD,SUM(t2.salaryNetwork) AS SALARYNETWORK,
			SUM(t2.cardCommercial) AS CARDCOMMERCIAL,MAX(t2.cardCoBranded) AS CARDCOBRANDED,SUM(t2.GEBTWDRec) AS GEBTWDREC,
			SUM(t2.GEBOTHRec) AS GEBOTHREC,SUM(t2.GEBLCRec) AS GEBLCREC,SUM(t2.profit) AS PROFIT,SUM(t2.cardNoneCommercial) AS CARDNONECOMMERCIAL,
			SUM(t2.profitSalary) AS PROFITSALARY,SUM(t2.profitTrustFdta) AS PROFITTRUSTFDTA,
			SUM(t2.rcvBuyFactAmt) AS RCVBUYFACTAMT,SUM(t2.rcvBuyAvgBal) AS RCVBUYAVGBAL  
			FROM L120S04A t2
            WHERE t2.custRelation LIKE '%6%' AND t2.mainId = :MAINID 
			and t2.keyCustId=:KEYCUSTID and t2.keyDupNo=:KEYDUPNO
        </query>
    </named-query>
    <named-query name="L120S01E.selL120s01e">
        <query>
            SELECT t1.finYear,t1.finItem,t1.finAmt,t1.finRatio
            FROM L120S01E t1
            WHERE t1.mainId = :MAINID AND t1.custId = :CUSTID
            AND t1.dupNo = :DUPNO AND t1.finKind = :FINKIND
            ORDER BY t1.finYear DESC
        </query>
    </named-query>
    <named-query name="L120S01E.selFinYear">
        <query>
            SELECT DISTINCT t1.finYear
            FROM L120S01E t1
            WHERE t1.mainId = :MAINID AND t1.custId = :CUSTID
            AND t1.dupNo = :DUPNO AND t1.finKind = :FINKIND
            ORDER BY t1.finYear DESC
        </query>
    </named-query>
    <named-query name="L120M01D.delModel">
        <query>
            DELETE FROM L120M01D t1 WHERE t1.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01A.delModel">
        <query>
            DELETE FROM L120S01A t2 WHERE t2.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01B.delModel">
        <query>
            DELETE FROM L120S01B t3 WHERE t3.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01C.delModel">
        <query>
            DELETE FROM L120S01C t4 WHERE t4.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01D.delModel">
        <query>
            DELETE FROM L120S01D t5 WHERE t5.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01E.delModel">
        <query>
            DELETE FROM L120S01E t6 WHERE t6.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01F.delModel">
        <query>
            DELETE FROM L120S01F t7 WHERE t7.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S01G.delModel">
        <query>
            DELETE FROM L120S01G t8 WHERE t8.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S03A.delModel">
        <query>
            DELETE FROM L120S03A t9 WHERE t9.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S04A.delModel">
        <query>
            DELETE FROM L120S04A ta WHERE ta.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05A.delModel">
        <query>
            DELETE FROM L120S05A tb WHERE tb.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05B.delModel">
        <query>
            DELETE FROM L120S05B tc WHERE tc.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05C.delModel">
        <query>
            DELETE FROM L120S05C td WHERE td.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S05D.delModel">
        <query>
            DELETE FROM L120S05D te WHERE te.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S06A.delModel">
        <query>
            DELETE FROM L120S06A tf WHERE tf.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S06B.delModel">
        <query>
            DELETE FROM L120S06B tg WHERE tg.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L160M01A.selDistinctBorrow">
        <query>
            select distinct t.custId,t.dupNo,t.custName
            from L160M01A t
            where t.ownBrId = :ownBrId and t.docStatus = :docStatus
        </query>
    </named-query>
    
    <named-query name="L192M01A.getWpNo">
        <query>
            select  max(t.wpSeq) from L192M01A t where t.ownBrId = :ownBrId and t.wpYear = :wpYear
        </query>
    </named-query>
    
    <named-query name="C240M01B.delByC240m01aMainid">
        <query>
            DELETE FROM C240M01B T WHERE T.mainId = :mainId
        </query>
    </named-query>
    <named-query name="C241A01A.delByC240m01aMainid">
        <query>
            DELETE FROM C241A01A A WHERE A.mainId IN (
            SELECT B.refMainId FROM C240M01B B WHERE B.mainId = :mainId
            )
        </query>
    </named-query>
    <named-query name="C241M01A.delByC240m01aMainid">
        <query>
            DELETE FROM C241M01A A WHERE A.mainId IN (
            SELECT B.refMainId FROM C240M01B B WHERE B.mainId = :mainId
            )
        </query>
    </named-query>
    <named-query name="C241M01B.delByC240m01aMainid">
        <query>
            DELETE FROM C241M01B A WHERE A.mainId IN (
            SELECT B.mainId FROM C241M01A B WHERE B.mainId IN (
            SELECT C.refMainId FROM C240M01B C WHERE C.mainId = :mainId
            ))
        </query>
    </named-query>
    
	<named-query name="ces140s07a.deleteByMainIdAndPid">
        <query>delete from C140S07A t where t.mainId = :mainId and t.pid = :pid</query>
    </named-query>    
	
	<named-query name="C120S01T.deleteByMainId">
        <query>
            DELETE FROM C120S01T T WHERE T.mainId = :mainId
        </query>
    </named-query>
	
	<named-query name="C101M01A.deleteOid">
        <query>
            DELETE FROM C101M01A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01A.deleteOid">
        <query>
            DELETE FROM C101S01A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01B.deleteOid">
        <query>
            DELETE FROM C101S01B T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01C.deleteOid">
        <query>
            DELETE FROM C101S01C T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01D.deleteOid">
        <query>
            DELETE FROM C101S01D T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01E.deleteOid">
        <query>
            DELETE FROM C101S01E T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01F.deleteOid">
        <query>
            DELETE FROM C101S01F T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01G.deleteOid">
        <query>
            DELETE FROM C101S01G T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01H.deleteOid">
        <query>
            DELETE FROM C101S01H T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01I.deleteOid">
        <query>
            DELETE FROM C101S01I T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01J.deleteOid">
        <query>
            DELETE FROM C101S01J T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01K.deleteOid">
        <query>
            DELETE FROM C101S01K T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01L.deleteOid">
        <query>
            DELETE FROM C101S01L T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01M.deleteOid">
        <query>
            DELETE FROM C101S01M T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01N.deleteOid">
        <query>
            DELETE FROM C101S01N T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01O.deleteOid">
        <query>
            DELETE FROM C101S01O T WHERE T.oid = :OID
        </query>
    </named-query>
    
	<named-query name="C101S01P.deleteOid">
        <query>
            DELETE FROM C101S01P T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01Q.deleteOid">
        <query>
            DELETE FROM C101S01Q T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01R.deleteOid">
        <query>
            DELETE FROM C101S01R T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01S.deleteOid">
        <query>
            DELETE FROM C101S01S T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01Y.deleteOid">
        <query>
            DELETE FROM C101S01Y T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01Y.deleteOid">
        <query>
            DELETE FROM C120S01Y T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S02A.deleteOid">
        <query>
            DELETE FROM C101S02A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S02A.deleteOid">
        <query>
            DELETE FROM C120S02A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S02B.deleteOid">
        <query>
            DELETE FROM C101S02B T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S02B.deleteOid">
        <query>
            DELETE FROM C120S02B T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C160S02A.deleteOid">
        <query>
            DELETE FROM C160S02A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C101S01U.deleteOid">
        <query>
            DELETE FROM C101S01U T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C101S01V.deleteOid">
        <query>
            DELETE FROM C101S01V T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C101S01X.deleteOid">
        <query>
            DELETE FROM C101S01X T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C101S01Z.deleteOid">
        <query>
            DELETE FROM C101S01Z T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120M01A.deleteOid">
        <query>
            DELETE FROM C120M01A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01A.deleteOid">
        <query>
            DELETE FROM C120S01A T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01B.deleteOid">
        <query>
            DELETE FROM C120S01B T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01C.deleteOid">
        <query>
            DELETE FROM C120S01C T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01D.deleteOid">
        <query>
            DELETE FROM C120S01D T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01E.deleteOid">
        <query>
            DELETE FROM C120S01E T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01F.deleteOid">
        <query>
            DELETE FROM C120S01F T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01G.deleteOid">
        <query>
            DELETE FROM C120S01G T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01H.deleteOid">
        <query>
            DELETE FROM C120S01H T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01I.deleteOid">
        <query>
            DELETE FROM C120S01I T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01J.deleteOid">
        <query>
            DELETE FROM C120S01J T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01K.deleteOid">
        <query>
            DELETE FROM C120S01K T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01L.deleteOid">
        <query>
            DELETE FROM C120S01L T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01M.deleteOid">
        <query>
            DELETE FROM C120S01M T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01N.deleteOid">
        <query>
            DELETE FROM C120S01N T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01O.deleteOid">
        <query>
            DELETE FROM C120S01O T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01P.deleteOid">
        <query>
            DELETE FROM C120S01P T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01Q.deleteOid">
        <query>
            DELETE FROM C120S01Q T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C120S01R.deleteOid">
        <query>
            DELETE FROM C120S01R T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120S01S.deleteOid">
        <query>
            DELETE FROM C120S01S T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120S01U.deleteOid">
        <query>
            DELETE FROM C120S01U T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120S01V.deleteOid">
        <query>
            DELETE FROM C120S01V T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120S01X.deleteOid">
        <query>
            DELETE FROM C120S01X T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C120S01Z.deleteOid">
        <query>
            DELETE FROM C120S01Z T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C122S01G.deleteOid">
        <query>
            DELETE FROM C122S01G T WHERE T.oid = :OID
        </query>
    </named-query>
	<named-query name="C122S01H.deleteOid">
        <query>
            DELETE FROM C122S01H T WHERE T.oid = :OID
        </query>
    </named-query>
	
	<named-query name="C801M01B.deleteMainId">
        <query>
            DELETE FROM C801M01B T WHERE T.mainId = :MAINID
        </query>
    </named-query>
	
	<named-query name="L120M01ATMP1.deleteByUserId">
        <query>
            DELETE from L120M01ATMP1 T where T.notesUp = :NOTESUP
        </query>
    </named-query>
	<named-query name="L140M01ATMP1.deleteByUserId">
        <query>
            DELETE from L140M01ATMP1 T where T.notesUp = :NOTESUP
        </query>
    </named-query>
	<named-query name="L140M01ATMP1.deleteByUid">
        <query>
            DELETE from L140M01ATMP1 T where T.uid = :UID
        </query>
    </named-query>
	<named-query name="L120S01M.deleteByKey">
        <query>
            DELETE from L120S01M T where T.mainId = :MAINID and T.custId = :CUSTID and T.dupNo = :DUPNO
        </query>
    </named-query>
	<named-query name="L120S01N.deleteByKey">
        <query>
            DELETE from L120S01N T where T.mainId = :MAINID and T.custId = :CUSTID and T.dupNo = :DUPNO
        </query>
    </named-query>
	<named-query name="L120S01O.deleteByKey">
        <query>
            DELETE from L120S01O T where T.mainId = :MAINID and T.custId = :CUSTID and T.dupNo = :DUPNO
        </query>
    </named-query>
	<named-query name="L130S02A.deleteByKeyDeletedTime">
        <query>
            DELETE from L130S02A T 
			where T.mainId = :MAINID and T.seqNo = :SEQNO and T.ctlType = :CTLTYPE and T.ctlItem = :CTLITEM 
				AND T.deletedTime is not null
        </query>
    </named-query>
	
	<named-query name="L120S05F.getGpcomamt">
        <query>
            SELECT SUM(t.gpComAmt+0.00)/1000 AS GPCOMAMT,
            SUM(t.gpRiskAmt+0.00)/1000 AS GPRISKAMT
            FROM L120S05F t WHERE t.mainId = :MAINID AND t.custId = :CUSTID AND t.dupNo = :DUPNO
        </query>
    </named-query>
    <named-query name="L120S05F.getTotAmtB">
        <query>
            SELECT SUM(t.totAmtB+0.00)/1000 AS TOTAMTB,
            SUM(t.crdAmtB+0.00)/1000 AS CRDAMTB
            FROM L120S05F t WHERE t.mainId = :MAINID AND t.custId = :CUSTID AND t.dupNo = :DUPNO
        </query>
    </named-query>
	<named-query name="L120S05E.delModel">
        <query>
            DELETE FROM L120S05E tb WHERE tb.mainId = :MAINID AND tb.custId = :CUSTID AND tb.dupNo = :DUPNO
        </query>
    </named-query>
    <named-query name="L120S05F.delModel">
        <query>
            DELETE FROM L120S05F tc WHERE tc.mainId = :MAINID AND tc.custId = :CUSTID AND tc.dupNo = :DUPNO
        </query>
    </named-query>
	<named-query name="L120S16A.delModel">
        <query>
            DELETE FROM L120S16A tf WHERE tf.mainId = :MAINID
        </query>
    </named-query>
    <named-query name="L120S16B.delModel">
        <query>
            DELETE FROM L120S16B tg WHERE tg.mainId = :MAINID
        </query>
    </named-query>
	<named-query name="L120S16C.delModel">
        <query>
            DELETE FROM L120S16C tg WHERE tg.mainId = :MAINID
        </query>
    </named-query>
    <!-- named-query 和 named-native-query 的tag不要混在一起，會出錯，所以放在最後面 -->
    
    
    <named-native-query name="L160M01A.selDistinctBorrowCount">
        <query>
            select count(*)
            from
            (
            select distinct a.custId,a.dupNo,a.custName
            from lms.L160M01A a where a.ownBrId = ?1 and a.docStatus = ?2
            )
        </query>
    </named-native-query>
    <named-native-query name="L160M01A.selDistinctBorrow2">
        <query>
            SELECT DISTINCT A.CUSTID,A.DUPNO,A.CUSTNAME
            FROM LMS.L160M01A A
            INNER JOIN LMS.L160M01B B ON A.MAINID = B.MAINID
            INNER JOIN LMS.L140M01A LA ON B.REMAINID = LA.MAINID
            INNER JOIN LMS.L140M01C LC ON LA.MAINID = LC.MAINID
            WHERE LC.LOANTP IN ('273','473','474','673','674') AND A.OWNBRID = ?1 AND A.DOCSTATUS = ?2
        </query>
    </named-native-query>
    <named-native-query name="L160M01A.selDistinctBorrowCount2">
        <query>
            select count(*)
            from
            (
            SELECT DISTINCT A.CUSTID,A.DUPNO,A.CUSTNAME
            FROM LMS.L160M01A A
            INNER JOIN LMS.L160M01B B ON A.MAINID = B.MAINID
            INNER JOIN LMS.L140M01A LA ON B.REMAINID = LA.MAINID
            INNER JOIN LMS.L140M01C LC ON LA.MAINID = LC.MAINID
            WHERE LC.LOANTP IN ('273','473','474','673','674') AND A.OWNBRID = ?1 AND A.DOCSTATUS = ?2
            )
        </query>
    </named-native-query>
    <named-native-query name="f101m01a.getSortMainId">
        <query><![CDATA[SELECT MAINID FROM F101M01A WHERE MAINID IN (?1, ?2, ?3, ?4) ORDER BY EDATE DESC]]></query>
    </named-native-query> 
	<!-- 中鋼整批轉檔用,找出jcic及etch近一個月查詢，且GrdCDate最大的C120S01Q-->
	<named-native-query name="c120s01q.max_GrdCDate_jcic_etch">
		<query><![CDATA[WITH TMP1 AS(SELECT q.* FROM LMS.C160M02A m LEFT JOIN LMS.C120S01Q q ON m.MAINID=q.MAINID where m.DELETEDTIME is null AND m.OWNBRID=?1 AND (ETCHQDATE > CURRENT DATE - 1 MONTH AND JCICQDATE > CURRENT DATE - 1 MONTH)),
    TMP2 AS(SELECT CUSTID,DUPNO,MAX(CREATETIME) CREATETIME FROM TMP1 GROUP BY CUSTID,DUPNO)
        SELECT TMP1.* FROM TMP2 LEFT JOIN TMP1 ON TMP2.CUSTID=TMP1.CUSTID AND TMP2.DUPNO = TMP1.DUPNO AND TMP2.CREATETIME = TMP1.CREATETIME]]></query>
	</named-native-query> 
	<named-native-query name="c120s01q.max_GrdCDate_jcic_etch_2m">
		<query><![CDATA[WITH TMP1 AS(SELECT q.* FROM LMS.C160M02A m LEFT JOIN LMS.C120S01Q q ON m.MAINID=q.MAINID where m.DELETEDTIME is null AND m.OWNBRID=?1 AND (ETCHQDATE > CURRENT DATE - 2 MONTH AND JCICQDATE > CURRENT DATE - 2 MONTH)),
    TMP2 AS(SELECT CUSTID,DUPNO,MAX(CREATETIME) CREATETIME FROM TMP1 GROUP BY CUSTID,DUPNO)
        SELECT TMP1.* FROM TMP2 LEFT JOIN TMP1 ON TMP2.CUSTID=TMP1.CUSTID AND TMP2.DUPNO = TMP1.DUPNO AND TMP2.CREATETIME = TMP1.CREATETIME]]></query>
	</named-native-query>
	<named-native-query name="c120s01q.findBySrcMainId">
		<query>
	SELECT q.* FROM LMS.C120S01Q q LEFT JOIN LMS.C160M01A a ON q.MAINID=a.MAINID WHERE a.DELETEDTIME IS NULL AND q.SRCMAINID = ?1
		</query>
	</named-native-query> 
	<named-native-query name="c120m01a.deleteByMainId">
        <query><![CDATA[DELETE FROM LMS.C120M01A WHERE mainId = ?1]]></query>
    </named-native-query>
	<named-native-query name="c120s01b.deleteByMainId">
        <query><![CDATA[DELETE FROM LMS.C120S01B WHERE mainId = ?1]]></query>
    </named-native-query> 
	<named-native-query name="c120s01e.deleteByMainId">
        <query><![CDATA[DELETE FROM LMS.C120S01E WHERE mainId = ?1]]></query>
    </named-native-query> 
	<named-native-query name="c120s01q.deleteByMainId">
        <query><![CDATA[DELETE FROM LMS.C120S01Q WHERE mainId = ?1]]></query>
    </named-native-query>  
	<named-native-query name="C900M01F.delByMainId">
        <query><![CDATA[DELETE FROM LMS.C900M01F WHERE mainId = ?1]]></query>
    </named-native-query>
	<named-native-query name="C900S02C.deleteByDataYM">
        <query><![CDATA[DELETE FROM LMS.C900S02C WHERE data_ym <= ?1]]></query>
    </named-native-query>
	<named-native-query name="C122M01A.getAllOnLineLoan">
        <query><![CDATA[
		 select count(*) as cnt, sum(APPLYAMT) as amt from lms.c122M01a
		 where substr(docStatus,1,1) != 'A' AND applyKind=?1 AND deletedTime is null
		 ]]></query>
    </named-native-query>
	<named-native-query name="C122M01A.getOnLineLoanByBranch">
        <query><![CDATA[
		select applystatus , count(*) as cnt, sum(APPLYAMT) as amt from lms.c122M01a 
		where applyKind=?1 AND substr(docStatus,1,1) != 'A'  AND OWNBRID = ?2  
		AND deletedTime is null
		group by applystatus]]></query>
    </named-native-query>	
	<named-native-query name="C122M01A.getCloseCaseByBrAndMonth">
        <query><![CDATA[
		select applystatus , count(*) as cnt, sum(APPLYAMT) as amt from lms.c122M01a 
		where applyKind=?1 AND substr(docStatus,1,1) != 'A'  AND OWNBRID = ?2 and substr(applystatus,1,1) = 'Z' 
		  and substr(char(applyTS),1,7)=?3 AND deletedTime is null 
		group by applystatus]]></query>
    </named-native-query>
    <named-native-query name="C122M01A.getSMEAList"> <!--J-113-0237 青創追踨報表-->
        <query><![CDATA[
            select m01a.CUSTID ,m01a.DUPNO ,m01a.CUSTNAME ,s01c.CNAME ,s01c.N_CNUMBER , DATE(m01a.APPLYTS) APPLYTS, m01a.OWNBRID ,b.BRNAME , m01a.INCOMTYPE, m01a.APPLYKIND, m01a.applyAmt*10000 APPLYAMT , l140m01a.CURRENTAPPLYAMT "approveAmt"
            from LMS.C122M01A m01a
              LEFT JOIN LMS.C122S01C s01c on m01a.MAINID = s01c.MAINID
              LEFT JOIN LMS.L140M01Y m01y on m01a.MAINID = m01y.REFMAINID -- 綁定資料
              LEFT JOIN LMS.L140M01A l140m01a on m01y.MAINID= l140m01a.MAINID -- 用綁定資料去串額度明細
              LEFT JOIN LMS.L120M01C l120m01c on l140m01a.MAINID = l120m01c.REFMAINID --簽報書跟額度明細表關聯
              LEFT JOIN LMS.L120M01A l120m01a on l120m01c.MAINID = l120m01a.MAINID -- 簽報書
              LEFT JOIN COM.BELSBRN b ON m01a.OWNBRID=b.BRNO
            where m01a.APPLYKIND in ('I','J') --I:青創100萬以下 , J:青創100萬以上 (進件管理)
              and l140m01a.DOCSTATUS ='030' and l140m01a.DELETEDTIME is null --額度明細表已核准
              and l120m01a.DOCSTATUS='05O' and l120m01a.DELETEDTIME is NULL --簽報書已核准
              and m01a.DELETEDTIME is NULL
              AND m01a.APPLYTS BETWEEN ?1 AND ?2
            ORDER BY m01a.APPLYTS ]]></query>
    </named-native-query>
    <named-native-query name="CES.C270M01A.getSMEAList"> <!--J-113-0237 青創追踨報表-->
        <query><![CDATA[
            SELECT X.IDNUMBER ,X.NAME ,X.CUSTNAME,X.custid,X.dupno, X.applyTS, X.CASEBRID,X.BRNAME ,
                   X.INCOMTYPE, X.apply_type, X.applyAMT,X.CURRENTAPPLYAMT, X.PROPERTY,
                   X.lnType,X.applyDate,X.subSidyut,X.yoPurpose
            FROM
                (
                    SELECT
                        m.IDNUMBER ,m.NAME ,t3.CUSTNAME,t3.custid,t3.dupno,Date(m.TRANSTIME) applyTS, LMS.L120M01A.CASEBRID,b.BRNAME ,
                CASE WHEN m.smeaId IS NULL THEN '線下' WHEN m.SMEAID ='0' THEN '小微EASY貸' ELSE '青年創業貸款' END INCOMTYPE,
                m.apply_type,VALUE(m.a3_1,VALUE(m.a3_4,m.a3_9)) applyAMT,t3.CURRENTAPPLYAMT,'|'||T3.PROPERTY||'|' AS PROPERTY,
		        t3.lnType,t3.applyDate,t3.subSidyut,t3.yoPurpose
            FROM
                CES.C270M01A m LEFT JOIN lms.l140m01a AS t3
            ON m.CUSTID =t3.CUSTID AND m.DUPNO =t3.DUPNO
                LEFT JOIN LMS.L120M01C ON LMS.L120M01C.REFMAINID = t3.MAINID
                LEFT JOIN LMS.L120M01A ON LMS.L120M01C.MAINID = LMS.L120M01A.MAINID
                LEFT JOIN COM.BELSBRN b ON LMS.L120M01A.CASEBRID=b.BRNO
            WHERE
                LMS.l120m01a.DOCSTATUS = '05O' AND t3.DOCSTATUS ='030' AND
                lms.l120m01a.DELETEDTIME IS NULL AND
                t3.DELETEDTIME IS NULL AND
                t3.lnType IN ('61') AND
                t3.applyDate >= Date(m.TRANSTIME) AND
                m.TRANSTIME BETWEEN ?1 AND ?2
                ) AS X WHERE PROPERTY LIKE '%|1|%']]></query>
    </named-native-query>
	<named-native-query name="C900S02E.countByCYC_MN">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S02E T WHERE T.CYC_MN = ?1 AND deletedtime is null
        </query>
    </named-native-query>
	<named-native-query name="C900S02E.countByCYC_MN_no_chk_result">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S02E T WHERE T.CYC_MN = ?1 AND deletedtime is null and (chk_result is null or chk_result='')
        </query>
    </named-native-query>
	<named-native-query name="C900S02E.setDeletedtimeByCYC_MN_no_chk_result">
        <query>
        	update lms.C900S02E T set deletedtime = current timestamp  WHERE T.CYC_MN = ?1 AND deletedtime is null and (chk_result is null or chk_result='')
        </query>
    </named-native-query>
	<named-native-query name="C900S03A.countByFnGenDateGenTime">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S03A T WHERE T.fn = ?1 and T.genDate= ?2 and T.genTime= ?3
        </query>
    </named-native-query>
	<named-native-query name="C900S03B.countByFnGenDateGenTime">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S03B T WHERE T.fn = ?1 and T.genDate= ?2 and T.genTime= ?3
        </query>
    </named-native-query>
	<named-native-query name="C900S03C.countByCYC_MN">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S03C T WHERE T.CYC_MN = ?1
        </query>
    </named-native-query>
	<named-native-query name="C900S03D.countByCYC_MN">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S03D T WHERE T.CYC_MN = ?1
        </query>
    </named-native-query>
	<named-native-query name="C900S03E.countByCYC_MN">
        <query>
            SELECT COUNT(*) as CNT FROM lms.C900S03E T WHERE T.CYC_MN = ?1
        </query>
    </named-native-query>
	 
</entity-mappings>
      