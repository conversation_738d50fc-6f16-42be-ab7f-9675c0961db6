/* 
 * L120S04CDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S04C;

/** 關係戶於本行往來實績彙總表明細檔 **/
@Repository
public class L120S04CDaoImpl extends LMSJpaDao<L120S04C, String> implements
		L120S04CDao {

	@Override
	public L120S04C findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S04C> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S04C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S04C> findByMainIdKeyCustIdDupNo(String mainId,
			String keyCustId, String keyDupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId",
				keyCustId);
		search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
		List<L120S04C> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S04C> findByMainIdDocKind(String mainId, String[] docKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// search.addSearchModeParameters(SearchMode.EQUALS, "docKind",
		// docKind);
		search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
		search.addOrderBy("rCustId", false);
		search.addOrderBy("rDupNo", false);
		search.addOrderBy("printSeq1", false);
		List<L120S04C> list = createQuery(search).getResultList();
		return list;
	}

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * 只會有一個年度的資料
	 */
	@Override
	public List<L120S04C> findByMainIdDocKindForClearLand(String mainId,
			String[] docKind) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "docDate", "");
		search.addOrderBy("docDate", true);
		List<L120S04C> list = createQuery(search).getResultList();
		return list;
	}


    @Override
    public List<L120S04C> findByMainIdKeyCustIdDupNoDocKind(
            String mainId, String keyCustId, String keyDupNo, String [] docKind) {
        ISearch search = createSearchTemplete();
        search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        search.addSearchModeParameters(SearchMode.EQUALS, "keyCustId", keyCustId);
        search.addSearchModeParameters(SearchMode.EQUALS, "keyDupNo", keyDupNo);
        search.addSearchModeParameters(SearchMode.IN, "docKind", docKind);
        List<L120S04C> list = createQuery(search).getResultList();
        return list;
    }
}