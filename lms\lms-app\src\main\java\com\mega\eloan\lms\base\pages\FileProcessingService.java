package com.mega.eloan.lms.base.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;

@Controller
@RequestMapping("/simple/FileProcessingService")
public class FileProcessingService extends AbstractEloanForm {
	
	private static final long serialVersionUID = 1L;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}
	
	@Override
	protected String getContentPageName() {
		return "base/pages/FileProcessingService";
	}
}

