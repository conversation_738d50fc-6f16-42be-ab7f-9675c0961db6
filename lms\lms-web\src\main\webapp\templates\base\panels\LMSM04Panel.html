<!-- 營運中心(授管處)會議決議共用元件 -->
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
    		    <!-- thickbox -->
				<div id="signContent0" style="display:none;" >
			       <form id="formL120m01h">
					   <!-- J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版
						<button type="button" onclick="printR20();">
							<span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block></span>
						</button>
						<button type="button" id="copyPatternNM" onclick="thickLms7205Grid();">
							<span class="text-only"><th:block th:text="#{'l120m01a.bt05">複製表格範本</th:block></span>
						</button>
						-->
					   <!-- J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版 -->
					   <button type="button" onclick="printLMSDoc9('Z');">
						   <span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block><th:block th:text="#{'L120M01H.dispWord'}"></th:block></span>
					   </button>
					   <button type="button" onclick="printLMSDoc9('A');">
						   <span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block>A</span>
					   </button>
					   <button type="button" onclick="printLMSDoc9('B');">
						   <span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block>B</span>
					   </button>
					   <button type="button" onclick="printLMSDoc9('C');">
						   <span class="text-only"><th:block th:text="#{'button.print'}">列印</th:block>C</span>
					   </button>
						<span id="btnLmsDoc80" class="hide">
							<button type="button" onclick="printLmsDoc80();">
								<span class="text-only"><th:block th:text="#{'l120m01a.title28'}">列印決議事項</th:block></span>
							</button>							
						</span>			  
					  <center>						 
					   <table class="tb2" width="95%">
				       		<tr class="commonDisp">
				       			<td class="hd1" width="35%"><th:block th:text="#{'L120M01H.gist'}"><!--案由--></th:block>&nbsp;&nbsp;
									<br/>
									<button type="button" onclick="importGist();">
										<span class="text-only"><th:block th:text="#{'button.importGist'}">引進案由</th:block></span>
									</button>
								</td>
								<td width="65%" align="left">
									<textarea id="l120m01hGist" name="l120m01hGist" class="max txt_mult" maxlengthC="1365" cols="80" rows="5" style="line-height: 20px;" ></textarea>
								</td>
							</tr>
						   <tr class="commonDisp">
							   <td class="hd1"><th:block th:text="#{'L120M01H.negOpinion'}"><!--負面意見--></th:block>&nbsp;&nbsp;</td>
							   <td align="left">
								   <textarea id="negOpinion" name="negOpinion" class="max txt_mult" maxlengthC="800" cols="80" rows="5" style="line-height: 20px;" ></textarea>
							   </td>
						   </tr>
						   <tr class="commonDisp">
							   <td class="hd1"><th:block th:text="#{'L120M01H.consent'}"><!--同意理由--></th:block>&nbsp;&nbsp;</td>
							   <td align="left">
								   <!--
								   <div class="phraseList">
									   <span><th:block th:text="#{'phrase.index1'}">[片語]</th:block></span>
									   <a onclick="$('#consent').val($(this).html());" class="link1" style="color:blue;text-decoration:underline;cursor:hand;"><th:block th:text="#{'phrase.index3'}"></th:block>：</a>　
								   </div>
								   -->
								   <textarea id="consent" name="consent" class="max txt_mult" maxlengthC="1365" cols="80" rows="5" style="line-height: 20px;" ></textarea>
							   </td>
						   </tr>
							<tr class="commonDisp">
								<td class="hd1"><th:block th:text="#{'L120M01H.approved'}"><!--擬辦--></th:block>&nbsp;&nbsp;</td>
								<td align="left">
									<!--
									<div class="phraseList">
					                  <span><th:block th:text="#{'phrase.index1'}">[片語]</th:block></span> 
					                  <a onclick="$('#dispWord').val($(this).html());" class="link1" style="color:blue;text-decoration:underline;cursor:hand;"><th:block th:text="#{'phrase.index2'}">本案擬</th:block>：</a>　
					                </div>
					                -->
									<textarea id="dispWord" name="dispWord" class="max txt_mult" maxlengthC="800" cols="80" rows="5" style="line-height: 20px;" ></textarea>
								</td>
							</tr>
						   <tr class="commonDisp">
							   <td class="hd1"><th:block th:text="#{'L120M01H.another'}"><!--另囑--></th:block>&nbsp;&nbsp;</td>
							   <td align="left">
								   <!--
								   <div class="phraseList">
									   <span><th:block th:text="#{'phrase.index1'}">[片語]</th:block></span>
									   <a onclick="$('#another').val($(this).html());" class="link1" style="color:blue;text-decoration:underline;cursor:hand;"><th:block th:text="#{'phrase.index4'}"></th:block>：</a>　
								   </div>
								   編排請用全形空白調整　　　　　　　　　　　　　　　　　　　　　　|←<br/>
								   　 →|　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　 |←
								   -->
								   <textarea id="another" name="another" class="max txt_mult" maxlengthC="1365" cols="80" rows="5" style="line-height: 20px;" ></textarea>
							   </td>
						   </tr>
							<tr class="commonDisp">
								<td class="hd1"><th:block th:text="#{'L120M01H.quotaDesrc'}"><!--核准額度較前准增、減金額 --></th:block>&nbsp;&nbsp;</td>
								<td align="left">
									<!--
									<div class="phraseList">
										<span><th:block th:text="#{'phrase.index1'}">[片語]</th:block></span>
										<a onclick="$('#quotaDesrc').val($(this).html());" class="link1" style="color:blue;text-decoration:underline;cursor:hand;"><th:block th:text="#{'phrase.index5'}"></th:block>：</a>　
									</div>
									-->
									<textarea id="quotaDesrc" name="quotaDesrc" class="max txt_mult" maxlengthC="256" cols="80" rows="5" style="line-height: 20px;" ></textarea>
								</td>
							</tr>
						   <tr class="commonDisp">
							   <td class="hd1"><th:block th:text="#{'L120M01H.authLvlStr'}"><!--授權層級--></th:block>&nbsp;&nbsp;
								   <br/>
								   <button type="button" onclick="importAuthLvlStr();">
									   <span class="text-only"><th:block th:text="#{'button.importAuthLvlStr'}">引進授權層級</th:block></span>
								   </button>
							   </td>
							   <td align="left">
								   <textarea id="authLvlStr" name="authLvlStr" class="max txt_mult" maxlengthC="256" cols="80" rows="5" style="line-height: 20px;" ></textarea>
							   </td>
						   </tr>
							<tr id="commonSign" class="hide">
								<td class="hd1"><th:block th:text="#{'l120m01a.decide1'}"><!--會簽 --></th:block>&nbsp;&nbsp;</td>
								<td>
									<textarea class="ickeditor" id="itemDscrC" name="itemDscrC" ></textarea>
								</td>
							</tr>
						   <!-- J-110-0499 配合作業合理化提案，調整會議決議及相關A、B、C版 -->
							<tr style="display:none">
								<td class="hd1"><th:block th:text="#{'L120M01H.meetingNote'}">會議決議</th:block>&nbsp;&nbsp;</td>
								<td>
									<div align="left" style="width:100%">
							         <textarea class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="49" th:attr="displayMessage=#{'L120M01H.meetingNote'}" id="meetingNote" name="meetingNote" cols="115" rows="10" preview="heigth:900;width:480"></textarea>
							       </div>
								</td>
							</tr>
				       </table>
					  </center>
				   </form>
				</div>
				<div id="authLvlStrThickbox" style="display:none">
					<form id="authLvlStrForm">
						<table width="100%" class="tb2">
							<tbody>
							<tr>
								<td>
									<select id="authLvlStrList" name="authLvlStrList">
										<option value="1" selected="selected">本案擬以個案討論事項提常董會核議，常董會提案稿併   呈。</option>
										<option value="2">本案擬以個案討論事項提董事會核議，董事會提案稿併   呈。</option>
										<option value="3">本案擬以彙總討論事項提常董會核議，常董會提案稿併   呈。</option>
										<option value="4">本案擬以彙總討論事項提董事會核議，董事會提案稿併   呈。</option>
									</select>
								</td>
							</tr>
							</tbody>
						</table>
					</form>
				</div>
				<div id="caseLvlStrThickbox" style="display:none">
					<form id="caseLvlStrForm">
						<table width="100%" class="tb2">
							<tbody>
							<tr>
								<td>
									<select id="caseLvlStrList" name="caseLvlStrList">
										<option value="1" selected="selected">個案討論</option>
										<option value="2">彙總討論</option>
									</select>
								</td>
							</tr>
							</tbody>
						</table>
					</form>
				</div>
	            <script type="text/javascript">
					//upgradetodo test
					/*
	                $(function(){
	                    $.getScript(webroot + "/pagejs/base/LMSM04Page.js");
	                });*/
	                loadScript('pagejs/base/LMSM04Page');
	            </script>
        </th:block>
    </body>
</html>
