/* 
 *MisELF442ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF442Service;

;

@Service
public class MisELF442ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF442Service {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.mfaloan.service.MisELF442Service#findELF442ByCntrNo
	 * (java.lang.String, java.lang.String, java.lang.String)
	 */
	public List<Map<String, Object>> findELF442ByCntrNo(String custId,
			String dupNo, String toDay) {
		return this.getJdbc().queryForList("ELF442.selCntrNo",
				new Object[] { custId, dupNo, toDay });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.mfaloan.service.MisELF442Service#findELF442ByCntrNoByKey
	 * (java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByKey(String branchId,
			String custId, String dupNo, String toDay) {

		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByKey",
				new Object[] { newBranchId, custId, dupNo, toDay });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.mfaloan.service.MisELF442Service#updateELF442ByforToBoss
	 * (java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public void updateByforToBoss(String STATUS, String custId, String dupNo,
			String cntrNo, String mainId) {
		this.getJdbc().update("ELF442.updateForToBoss",
				new Object[] { STATUS, mainId, custId, dupNo, cntrNo });

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.mfaloan.service.MisELF442Service#updateForToCheck(
	 * java.lang.String, java.lang.String, java.lang.String, java.lang.String,
	 * java.lang.String)
	 */
	@Override
	public void updateForToCheck(String status, String agree_Flag,
			String custId, String dupNo, String cntrNo, String mainId) {
		this.getJdbc().update(
				"ELF442.updateForToCheck",
				new Object[] { status, agree_Flag, mainId, custId, dupNo,
						cntrNo });

	}

	@Override
	public List<Map<String, Object>> selByCount(String ELF442_BRANCH,
			String ELF442_CUSTID, String ELF442_DUPNO, String ELF442_CNTRNO) {

		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = ELF442_BRANCH + "%";
		if (Util.equals(ELF442_BRANCH, "940")) {
			newBranchId = "%";
		}

		return this.getJdbc().queryForList(
				"ELF442.selByCount",
				new Object[] { newBranchId, ELF442_CUSTID, ELF442_DUPNO,
						ELF442_CNTRNO });
	}

	@Override
	public void updateByother(String ELF442_RISK_CNTRY,
			String ELF442_RISK_AREA, String ELF442_BUS_CD,
			String ELF442_BUS_SUB_CD, String ELF442_BRANCH,
			String ELF442_CUSTID, String ELF442_DUPNO, String ELF442_CNTRNO,
			String mainId) {

		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = ELF442_BRANCH + "%";
		if (Util.equals(ELF442_BRANCH, "940")) {
			newBranchId = "%";
		}

		this.getJdbc().update(
				"ELF442.updateByOther",
				new Object[] { ELF442_RISK_CNTRY, ELF442_RISK_AREA,
						ELF442_BUS_CD, ELF442_BUS_SUB_CD, mainId, newBranchId,
						ELF442_CUSTID, ELF442_DUPNO, ELF442_CNTRNO });
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.lms.mfaloan.service.MisELF442Service#findELF442ByCntrNoByCheck
	 * (java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	public List<Map<String, Object>> findELF442ByCntrNoByCheck(String branchId,
			String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheck",
				new Object[] { newBranchId, custId, cntrNo });
	}

	public List<Map<String, Object>> findELF442ByCntrNoByCheck2(
			String branchId, String custId, String dupNo, String cntrNo) {

		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheck2",
				new Object[] { newBranchId, custId, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheck3(String custId,
			String dupNo, String cntrNo) {
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheck3",
				new Object[] { custId, cntrNo });
	}

	@Override
	public Map<String, Object> findELF442ByKey(String custId, String dupNo,
			String cntrNo, String CLASS_CODE) {
		return this.getJdbc().queryForMap("ELF442.selbyKey",
				new Object[] { custId, dupNo, cntrNo, CLASS_CODE });
	}

	@Override
	public void updateForToReject(String status, String agree_Flag,
			String custId, String dupNo, String cntrNo, String mainId) {
		this.getJdbc().update(
				"ELF442.updateForToReject",
				new Object[] { status, agree_Flag, mainId, custId, dupNo,
						cntrNo });

	}

	public List<Map<String, Object>> findELF442ByLandBuild(String custId,
			String dupNo, String toDay) {
		return this.getJdbc().queryForList("ELF442.selbyLandBuild",
				new Object[] { custId, dupNo, toDay });
	}

	/**
	 * J-107-0035-001 Web e-Loan授信系統配合集團企業之「預約額度情形彙整表」，修改預約額度相關功能。
	 */
	@Override
	public void updateOnlyMainId(String custId, String dupNo, String cntrNo,
			String mainId) {
		this.getJdbc().update("ELF442.updateOnlyMainId",
				new Object[] { mainId, custId, dupNo, cntrNo });

	}

	/**
	 * J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管 檢核72-2
	 */
	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheckFor722(
			String branchId, String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheckFor722",
				new Object[] { newBranchId, custId, cntrNo });
	}

	/**
	 * J-111-0163_05097_B1001 Web e-Loan企金國內、海外簽報書新增中租集團(代號1208)預約額度檢核作業
	 */
	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForGrp07(
			String branchId, String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheckForGrp07",
				new Object[] { newBranchId, custId, cntrNo });
	}

	/**
	 * J-111-0397_05097_B1001 Web e-Loan國內企金授信新增權加權風險性資產(RWA)相關計算與預約機制
	 */
	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForRwa(
			String branchId, String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheckForRwa",
				new Object[] { newBranchId, custId, cntrNo });
	}

	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForRwaCase2(
			String branchId, String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheckForRwaCase2",
				new Object[] { newBranchId, custId, cntrNo });
	}

	@Override
	public void backELF442ByRptMainId(String elf442RptMainId) {
		this.getJdbc().update("ELF442.backByRptMainId",
				new Object[] { elf442RptMainId });
	}

	/**
	 * J-110-0455_05097_B1001 企金授信簽報書新增72-2簽案預約控管 檢核非72-2
	 */
	@Override
	public List<Map<String, Object>> findELF442ByCntrNoByCheckForGeneral(
			String branchId, String custId, String dupNo, String cntrNo) {
		// J-106-0159-001 Web e-Loan 授信管理系統開放營業單位與授信行銷處之間可互相協助預約額度申請
		String newBranchId = branchId + "%";
		if (Util.equals(branchId, "940")) {
			newBranchId = "%";
		}
		return this.getJdbc().queryForList("ELF442.selCntrNoByCheckForGeneral",
				new Object[] { newBranchId, custId, cntrNo });
	}
	
	/**
	 * 依客戶取得特定ELF442_CLASS_CODE(預約額度 09: 行業對象別申請確認暨修改)
	 */
	@Override
	public List<Map<String, Object>> findELF442ByCustIdByClassCode(String custId,
			String dupNo, String classCode) {
		return this.getJdbc().queryForList("ELF442.selCntrNoByClassCode",
				new Object[] { custId, dupNo, classCode });
	}

}
