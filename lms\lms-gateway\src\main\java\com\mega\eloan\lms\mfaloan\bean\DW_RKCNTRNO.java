/* 
 * DW_RKCNTRNO.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Digits;

import tw.com.iisi.cap.model.GenericBean;


/** 房貸額度序號檔 **/
public class DW_RKCNTRNO extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 分行別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="BR_CD", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String br_cd;

	/** NOTES文件編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String noteid;

	/** 客戶統一編號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String custid;

	/** 重複序號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String dupno;

	/** 評等模型類別 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWTYPE", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String mowtype;

	/** 模型版本-大版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver1;

	/** 模型版本-小版 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer mowver2;

	/** JCIC查詢日期 YYYY-MM-DD **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Temporal(TemporalType.DATE)
	@Column(name="JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date jcic_date;

	/** 序號/帳號 **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="ACCT_KEY", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String acct_key;

	
	/** 主借款人統一編號(CUSTKEY) **/
	@Column(name="CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String cust_key;

	/** 
	 * 相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	@Column(name="LNGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String lngeflag;

	/** 本件額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrno;

	/** 
	 * 文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	@Column(name="DOCSTATUS", length=2, columnDefinition="CHAR(2)")
	private String docstatus;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DATA_SRC_DT", columnDefinition="DATE")
	private Date data_src_dt;	
	
	/** 申請額度幣別 **/	
	@Column(name="LOANCURR", length=3, columnDefinition="CHAR(3)")
	private String loancurr;
	
	/** 申請額度金額 **/
	@Column(name="LOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal loanamt;
	
	/** 承作性質代碼 **/
	@Column(name="PROPERTY", length=30, columnDefinition="VARCHAR(30)")
	private String property;
	
	/** 授信期間-年數 **/
	@Column(name="LNYEAR", columnDefinition="DECIMAL(2,0)")
	private Integer lnyear;
	
	/** 授信期間-月數 **/
	@Column(name="LNMONTH", columnDefinition="DECIMAL(2,0)")
	private Integer lnmonth;
	
	/** 產品種類 **/
	@Column(name="PRODKIND", length=2, columnDefinition="CHAR(2)")
	private String prodkind;
	
	/** 科目 **/
	@Column(name="SUBJCODE", length=8, columnDefinition="VARCHAR(8)")
	private String subjcode;
	
	/** 是否屬興建住宅 **/
	@Column(name="RESIDENTIAL", length=1, columnDefinition="CHAR(1)")
	private String residential;

	/** 卡友貸旗標 */
	@Column(name="C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String c_flag;
	
	/** 擔保品是否為房屋 */
	@Column(name="COLL_HOUSE", length=1, columnDefinition="CHAR(1)")
	private String coll_house;
	
	/** 核貸成數 **/
	@Digits(integer = 3, fraction = 2)
	@Column(name = "APPROVEDPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal approvedPercent;
	
	/** 取得分行別 **/
	public String getBr_cd() {
		return this.br_cd;
	}
	/** 設定分行別 **/
	public void setBr_cd(String value) {
		this.br_cd = value;
	}

	/** 取得NOTES文件編號 **/
	public String getNoteid() {
		return this.noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setNoteid(String value) {
		this.noteid = value;
	}

	/** 取得客戶統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定客戶統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getMowtype() {
		return this.mowtype;
	}
	/** 設定評等模型類別 **/
	public void setMowtype(String value) {
		this.mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getMowver1() {
		return this.mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setMowver1(Integer value) {
		this.mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getMowver2() {
		return this.mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setMowver2(Integer value) {
		this.mowver2 = value;
	}

	/** 取得JCIC查詢日期 YYYY-MM-DD **/
	public Date getJcic_date() {
		return this.jcic_date;
	}
	/** 設定JCIC查詢日期 YYYY-MM-DD **/
	public void setJcic_date(Date value) {
		this.jcic_date = value;
	}

	/** 取得主借款人統一編號(CUSTKEY) **/
	public String getCust_key() {
		return this.cust_key;
	}
	/** 設定主借款人統一編號(CUSTKEY) **/
	public void setCust_key(String value) {
		this.cust_key = value;
	}

	/** 
	 * 取得相關身分 ( LNGEFLAG)<p/>
	 * M: 主借款人  C: 共同借款人  G: 連帶保證人
	 */
	public String getLngeflag() {
		return this.lngeflag;
	}
	/**
	 *  設定相關身分 ( LNGEFLAG)<p/>
	 *  M: 主借款人  C: 共同借款人  G: 連帶保證人
	 **/
	public void setLngeflag(String value) {
		this.lngeflag = value;
	}

	/** 取得本件額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定本件額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得文件狀態<p/>
	 * 編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 */
	public String getDocstatus() {
		return this.docstatus;
	}
	/**
	 *  設定文件狀態<p/>
	 *  編製中|1待覆核|2待母行覆核 | 2C核准|3婉卻|4呈區域授信中心|5呈總行法金處/授管處|6待補件 | 7提放審會|H1提常董會|H2審核中|A已會簽|B會簽中|C會簽待覆核|2A
	 **/
	public void setDocstatus(String value) {
		this.docstatus = value;
	}

	/** 取得上傳資料日期 **/
	public Date getData_src_dt() {
		return this.data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setData_src_dt(Date value) {
		this.data_src_dt = value;
	}
	/** 取得序號/帳號 **/
	public String getAcct_key() {
		return this.acct_key;
	}

	/** 設定序號/帳號 **/
	public void setAcct_key(String value) {
		this.acct_key = value;
	}
	
	/** 設定申請額度幣別 **/
	public void setLoancurr(String loancurr) {
		this.loancurr = loancurr;
	}	
	/** 取得申請額度幣別 **/
	public String getLoancurr() {
		return loancurr;
	}
	
	/** 設定申請額度金額 **/
	public void setLoanamt(BigDecimal loanamt) {
		this.loanamt = loanamt;
	}
	/** 取得申請額度金額 **/
	public BigDecimal getLoanamt() {
		return loanamt;
	}	
	/** 設定承作性質代碼 **/
	public void setProperty(String property) {
		this.property = property;
	}
	/** 取得承作性質代碼 **/
	public String getProperty() {
		return property;
	}	
	/** 設定授信期間-年數 **/
	public void setLnyear(Integer lnyear) {
		this.lnyear = lnyear;
	}
	/** 取得授信期間-年數 **/
	public Integer getLnyear() {
		return lnyear;
	}	
	/** 設定授信期間-月數 **/
	public void setLnmonth(Integer lnmonth) {
		this.lnmonth = lnmonth;
	}
	/** 取得授信期間-月數 **/
	public Integer getLnmonth() {
		return lnmonth;
	}	
	/** 設定產品種類 **/
	public void setProdkind(String prodkind) {
		this.prodkind = prodkind;
	}
	/** 取得產品種類 **/
	public String getProdkind() {
		return prodkind;
	}	
	/** 設定科目 **/
	public void setSubjcode(String subjcode) {
		this.subjcode = subjcode;
	}
	/** 取得科目 **/
	public String getSubjcode() {
		return subjcode;
	}	
	/** 設定是否屬興建住宅 **/
	public void setResidential(String residential) {
		this.residential = residential;
	}
	/** 取得是否屬興建住宅 **/
	public String getResidential() {
		return residential;
	}

	/** 取得卡友貸旗標 */
	public String getC_flag() {
		return c_flag;
	}
	/** 設定卡友貸旗標 */
	public void setC_flag(String c_flag) {
		this.c_flag = c_flag;
	}
	
	/** 取得擔保品是否為房屋 */
	public String getColl_house() {
		return coll_house;
	}
	/** 設定擔保品是否為房屋 */
	public void setColl_house(String coll_house) {
		this.coll_house = coll_house;
	}
	
	/** 取得核貸成數 */
	public BigDecimal getApprovedPercent() {
		return approvedPercent;
	}

	/** 設定核貸成數 */
	public void setApprovedPercent(BigDecimal approvedPercent) {
		this.approvedPercent = approvedPercent;
	}
}
