/* 
 * MisIquotgurService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.util.List;

/**
 * <pre>
 * 保證人檔 IQUOTGUR(MIS.ELV42101)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisIquotgurService {

	/**
	 * @param DateList
	 *            <pre>
	 *            Object[] content 
	 *            
	 *            quotaNo 額度序號 , 
	 *            CUSTID 借款人名稱,
	 *            dupNo 重複序號,
	 *            cName]保證人姓名 ,
	 *            updater 資料修改人
	 * </pre>
	 */
	public void insert(List<Object[]> DateList);

	/**
	 * 動審表 -根據額度序號查詢刪除該額度序號 保證人檔 Iquotgur
	 * 
	 * @param args
	 *            所有的額度序號
	 */
	void delByCntrNo(String args);

	/**
	 * @param DateList
	 * @param DateList2
	 */
//	public void insertAllTest(String delCntrNo, List<Object[]> DateList,
//			List<Object[]> DateList2);

}
