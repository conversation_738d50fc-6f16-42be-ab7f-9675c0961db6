package com.mega.eloan.lms.batch.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.aml.AmlStrategy;
import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

@Service("lmsbatch0006serviceimpl")
public class LmsBatch0006ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	SysParameterService sysParamService;

	@Resource
	AMLRelateService amlRelateService;

	@Resource
	EloandbBASEService eloandbService;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		// @NonTransactional
		JSONObject result = new JSONObject();
		JSONObject request = json.getJSONObject("request");

		String errMsg = this.doLmsBatch0001(request);
		if (Util.notEquals(errMsg, "")) {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0006ServiceImpl-doLmsBatch0001執行失敗！==>" + errMsg);
			return result;
		} else {
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE,
					"LmsBatch0006ServiceImpl執行成功！");
		}

		return result;
	}

	@NonTransactional
	public String doLmsBatch0001(JSONObject request) {

		// http://127.0.0.1:9081/lms-web/app/scheduler?input={"TIMEOUT":"9999","serviceId":"lmsbatch0006serviceimpl",request:{"dataStartDate":"2021-05-01","dataEndDate":"2020-05-31"}}

		StringBuffer errMsg = new StringBuffer("");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataStartDate, "")) {
			errMsg.append("資料起迄日不得空白(doLmsBatch0001)");
			return errMsg.toString();
		}

		AmlStrategy as = amlRelateService.getAmlStrategy("0A2");
		int count = 0;

		List<String> uniKeys = new ArrayList<String>();
		List<Map<String, Object>> elList = new ArrayList<Map<String, Object>>();
		elList = eloandbService.doLmsBatch0047(dataStartDate, dataEndDate);
		if (elList != null && !elList.isEmpty()) {
			for (Map<String, Object> elMap : elList) {
				String UNIQUEKEY = MapUtils.getString(elMap, "UNIQUEKEY");
				if (Util.notEquals(UNIQUEKEY, "")) {
					// 等贊介
					uniKeys.add(UNIQUEKEY);
				}
			}
		}

		if (uniKeys != null && !uniKeys.isEmpty()) {
			as.reSend(uniKeys);
			count = uniKeys.size();
		}

		logger.info("doLmsBatch0047 執行結果 : " + count + "筆。");

		return errMsg.toString();

	}

}
