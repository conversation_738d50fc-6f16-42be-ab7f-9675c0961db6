package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C160M03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C160M03A;

/**
 * C160M03A 整批自動開戶
 * 
 * <AUTHOR>
 * 
 */
@Repository
public class C160M03ADaoImpl extends LMSJpaDao<C160M03A, String> implements
		C160M03ADao {

	@Override
	public C160M03A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C160M03A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int findMaxPackNoByCntrNo(String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addOrderBy("packNo", true);
		C160M03A bean = findUniqueOrNone(search);
		if(bean==null){
			return 0;
		}
		return Util.parseInt(bean.getPackNo());
	}

	@Override
	public List<C160M03A> findByCntrNo(String cntrNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		
		search.setMaxResults(Integer.MAX_VALUE);
		List<C160M03A> list = createQuery(C160M03A.class,search).getResultList();
		return list;
	}	
}