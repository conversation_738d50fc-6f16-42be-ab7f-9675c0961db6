/* 
 * L140MM3A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 都更危老註記維護主檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140MM3A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L140MM3A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 本案是否屬銀行法72-2條控管對象
	 */
	@Column(name = "IS722FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722Flag;

	/**
	 * 前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	@Column(name = "IS722ONFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722OnFlag;

	/**
	 * 查詢銀行法72-2條控管對象日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "IS722QDATE", columnDefinition = "DATE")
	private Date is722QDate;

	/**
	 * 額度序號檢核是否存在帳務擋註記
	 */
	@Column(name = "CNTRNOCHKEXISTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cntrNoChkExistFlag;

	/**
	 * 額度序號檢核是否存在帳務擋日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CNTRNOCHKEXISTDATE", columnDefinition = "DATE")
	private Date cntrNoChkExistDate;

	/** 本次是否為購置不動產 **/
	@Column(name = "ISBUY", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuy;

	/** 排除原因 **/
	@Column(name = "EXITEM", length = 3, columnDefinition = "VARCHAR(3)")
	private String exItem;

	/** 前次是否為購置不動產 **/
	@Column(name = "ISBUYON", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuyOn;

	/** 排除原因 **/
	@Column(name = "EXITEMON", length = 60, columnDefinition = "VARCHAR(60)")
	private String exItemOn;

	/**
	 * 是否為重建
	 */
	@Column(name = "REBUILD", length = 1, columnDefinition = "CHAR(1)")
	private String rebuild;

	/**
	 * 是否純營建工程業之營運週轉金
	 */
	@Column(name = "ISINSTALMENT", length = 1, columnDefinition = "CHAR(1)")
	private String isInstalment;

	/**
	 * 前次是否純營建工程業之營運週轉金
	 */
	@Column(name = "ISINSTALMENTON", length = 1, columnDefinition = "CHAR(1)")
	private String isInstalmentOn;

	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 產品種類 ELF506_PROD_KIND ELF447N_PROD_CLASS
	 */
	@Column(name = "LNTYPE", columnDefinition = "CHAR(2)")
	private String lnType;

	/**
	 * ADC案件編號
	 * <p/>
	 * ADC + 2021(西元年) + 007 (分行代號)+ 00001(流水號)
	 */
	@Column(name = "ADCCASENO", length = 15, columnDefinition = "CHAR(15)")
	private String adcCaseNo;

	/**
	 * 修改項目不動產暨72-2相關資訊註記
	 */
	@Column(name = "UPDATEITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String updateItem2;

	/**
	 * 修改項目暴險註記
	 */
	@Column(name = "UPDATEITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String updateItem3;

	/**
	 * 本案是否屬特殊融資暴險
	 */
	@Column(name = "ISSPECIALFINRISK", length = 1, columnDefinition = "CHAR(1)")
	private String isSpecialFinRisk;

	/**
	 * 特殊融資暴險類別
	 */
	@Column(name = "SPECIALFINRISKTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String specialFinRiskType;

	/**
	 * 本案是否屬不動產ADC暴險
	 */
	@Column(name = "ISCMSADCRISK", length = 1, columnDefinition = "CHAR(1)")
	private String isCmsAdcRisk;

	/**
	 * 專案融資是否屬營運階段
	 */
	@Column(name = "ISPROJECTFINOPERATESTAG", length = 1, columnDefinition = "CHAR(1)")
	private String isProjectFinOperateStag;

	/**
	 * 本案是否屬特殊融資暴險(前案)
	 */
	@Column(name = "ISSPECIALFINRISKON", length = 1, columnDefinition = "CHAR(1)")
	private String isSpecialFinRiskOn;

	/**
	 * 特殊融資暴險類別(前案)
	 */
	@Column(name = "SPECIALFINRISKTYPEON", length = 1, columnDefinition = "CHAR(1)")
	private String specialFinRiskTypeOn;

	/**
	 * 本案是否屬不動產ADC暴險(前案)
	 */
	@Column(name = "ISCMSADCRISKON", length = 1, columnDefinition = "CHAR(1)")
	private String isCmsAdcRiskOn;

	/**
	 * 專案融資是否屬營運階段(前案)
	 */
	@Column(name = "ISPROJECTFINOPERATESTAGON", length = 1, columnDefinition = "CHAR(1)")
	private String isProjectFinOperateStagOn;

	/**
	 * ADC號碼補建或變更
	 */
	@Column(name = "UPDATEITEM4", length = 1, columnDefinition = "CHAR(1)")
	private String updateItem4;

	/**
	 * 產品種類(前案) ELF506_PROD_KIND ELF447N_PROD_CLASS
	 */
	@Column(name = "LNTYPEON", columnDefinition = "CHAR(2)")
	private String lnTypeOn;

	/**
	 * ADC案件編號(前案)
	 * <p/>
	 * ADC + 2021(西元年) + 007 (分行代號)+ 00001(流水號)
	 */
	@Column(name = "ADCCASENOON", length = 15, columnDefinition = "CHAR(15)")
	private String adcCaseNoOn;
	
	/**
	 * 高品質專案融資_選項1
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_1", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_1;
	
	/**
	 * 高品質專案融資_選項2
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_2", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_2;
	
	/**
	 * 高品質專案融資_選項3
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_3", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_3;
	
	/**
	 * 高品質專案融資_選項4
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_4", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_4;
	
	/**
	 * 高品質專案融資_選項5
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_5", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_5;
	
	/**
	 * 高品質專案融資_最終結果
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJRESULT", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjResult;
	
	/**
	 * 高品質專案融資_選項1(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_1ON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_1On;
	
	/**
	 * 高品質專案融資_選項2(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_2ON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_2On;
	
	/**
	 * 高品質專案融資_選項3(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_3ON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_3On;
	
	/**
	 * 高品質專案融資_選項4(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_4ON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_4On;
	
	/**
	 * 高品質專案融資_選項5(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJOPT_5ON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjOpt_5On;
	
	/**
	 * 高品質專案融資_最終結果(前案)
	 * 
	 * J-112-0417 Web e-Loan修改企金額度明細表高品質專案融資
	 */
	@Column(name = "ISHIGHQUALITYPROJRESULTON", length = 1, columnDefinition = "CHAR(1)")
	private String isHighQualityProjResultOn;
	
	/**
	 * 維護項目約定融資額度註記
	 */
	@Column(name = "UPDATEITEM5", length = 1, columnDefinition = "CHAR(1)")
	private String updateItem5;
	
	/**
	 * 約定融資額度註記
	 */
	@Column(name = "EXCEPTFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlag;
	
	/**
	 * 約定融資額度註記(前案)
	 */
	@Column(name = "EXCEPTFLAGON", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlagOn;
	
	/**
	 * 約定融資額度註記問答項目選擇[是]的項目
	 */
	@Column(name = "EXCEPTFLAGQAISY", length = 2, columnDefinition = "VARCHAR(2)")
	private String exceptFlagQAisY;
	
	/**
	 * 約定融資額度註記問答項目選擇[是]的項目(前案)
	 */
	@Column(name = "EXCEPTFLAGQAISYON", length = 2, columnDefinition = "VARCHAR(2)")
	private String exceptFlagQAisYOn;
	
	/**
	 * 約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消)
	 */
	@Column(name = "EXCEPTFLAGQAPLUS", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlagQAPlus;
	
	/**
	 * 約定融資額度註記資本計提延伸問答(僅有條件及無條件可取消)(前案)
	 */
	@Column(name = "EXCEPTFLAGQAPLUSON", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlagQAPlusOn;
	

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 設定本案是否屬銀行法72-2條控管對象
	 */
	public void setIs722Flag(String is722Flag) {
		this.is722Flag = is722Flag;
	}

	/**
	 * 取得本案是否屬銀行法72-2條控管對象
	 */
	public String getIs722Flag() {
		return is722Flag;
	}

	/**
	 * 設定 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public void setIs722OnFlag(String is722OnFlag) {
		this.is722OnFlag = is722OnFlag;
	}

	/**
	 * 取得 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public String getIs722OnFlag() {
		return is722OnFlag;
	}

	/**
	 * 設定查詢銀行法72-2條控管對象日期
	 */
	public void setIs722QDate(Date is722QDate) {
		this.is722QDate = is722QDate;
	}

	/**
	 * 取得查詢銀行法72-2條控管對象日期
	 */
	public Date getIs722QDate() {
		return is722QDate;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋註記
	 */
	public void setCntrNoChkExistFlag(String cntrNoChkExistFlag) {
		this.cntrNoChkExistFlag = cntrNoChkExistFlag;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋註記
	 */
	public String getCntrNoChkExistFlag() {
		return cntrNoChkExistFlag;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋日期
	 */
	public void setCntrNoChkExistDate(Date cntrNoChkExistDate) {
		this.cntrNoChkExistDate = cntrNoChkExistDate;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋日期
	 */
	public Date getCntrNoChkExistDate() {
		return cntrNoChkExistDate;
	}

	/**
	 * 取得是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuy() {
		return isBuy;
	}

	/**
	 * 設定是否為購置不動產
	 * 
	 * @param isBuy
	 */
	public void setIsBuy(String isBuy) {
		this.isBuy = isBuy;
	}

	/**
	 * 取得排除條件
	 * 
	 * @return
	 */
	public String getExItem() {
		return exItem;
	}

	/**
	 * 設定排除條件
	 * 
	 * @param exItem
	 */
	public void setExItem(String exItem) {
		this.exItem = exItem;
	}

	/**
	 * 取得前案是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuyOn() {
		return isBuyOn;
	}

	/**
	 * 設定前案是否為購置不動產
	 * 
	 * @param isBuyOn
	 */
	public void setIsBuyOn(String isBuyOn) {
		this.isBuyOn = isBuyOn;
	}

	/**
	 * 取得前案排除條件
	 * 
	 * @return
	 */
	public String getExItemOn() {
		return exItemOn;
	}

	/**
	 * 設定前案排除條件
	 * 
	 * @param exItemOn
	 */
	public void setExItemOn(String exItemOn) {
		this.exItemOn = exItemOn;
	}

	/** 是否為重建 **/
	public void setRebuild(String rebuild) {
		this.rebuild = rebuild;
	}

	/** 是否為重建 **/
	public String getRebuild() {
		return rebuild;
	}

	public void setIsInstalment(String isInstalment) {
		this.isInstalment = isInstalment;
	}

	public String getIsInstalment() {
		return isInstalment;
	}

	public void setIsInstalmentOn(String isInstalmentOn) {
		this.isInstalmentOn = isInstalmentOn;
	}

	public String getIsInstalmentOn() {
		return isInstalmentOn;
	}

	public String getChkYN() {
		return this.chkYN;
	}

	public void setChkYN(String value) {
		this.chkYN = value;
	}

	/* 設定產品種類 */
	public void setLnType(String lnType) {
		this.lnType = lnType;
	}

	/* 取得產品種類 */
	public String getLnType() {
		return lnType;
	}

	/* 取得ADC案件號碼 */
	public String getAdcCaseNo() {
		return this.adcCaseNo;
	}

	/* 設定ADC案件號碼 */
	public void setAdcCaseNo(String value) {
		this.adcCaseNo = value;
	}

	public void setIsSpecialFinRisk(String isSpecialFinRisk) {
		this.isSpecialFinRisk = isSpecialFinRisk;
	}

	public String getIsSpecialFinRisk() {
		return isSpecialFinRisk;
	}

	public void setSpecialFinRiskType(String specialFinRiskType) {
		this.specialFinRiskType = specialFinRiskType;
	}

	public String getSpecialFinRiskType() {
		return specialFinRiskType;
	}

	public void setIsCmsAdcRisk(String isCmsAdcRisk) {
		this.isCmsAdcRisk = isCmsAdcRisk;
	}

	public String getIsCmsAdcRisk() {
		return isCmsAdcRisk;
	}

	/**
	 * 設定專案融資是否屬營運階段
	 * 
	 * @param isProjectFinOperateStag
	 */
	public void setIsProjectFinOperateStag(String isProjectFinOperateStag) {
		this.isProjectFinOperateStag = isProjectFinOperateStag;
	}

	/**
	 * 取得專案融資是否屬營運階段
	 * 
	 * @return
	 */
	public String getIsProjectFinOperateStag() {
		return isProjectFinOperateStag;
	}

	public void setIsSpecialFinRiskOn(String isSpecialFinRiskOn) {
		this.isSpecialFinRiskOn = isSpecialFinRiskOn;
	}

	public String getIsSpecialFinRiskOn() {
		return isSpecialFinRiskOn;
	}

	public void setSpecialFinRiskTypeOn(String specialFinRiskTypeOn) {
		this.specialFinRiskTypeOn = specialFinRiskTypeOn;
	}

	public String getSpecialFinRiskTypeOn() {
		return specialFinRiskTypeOn;
	}

	public void setIsCmsAdcRiskOn(String isCmsAdcRiskOn) {
		this.isCmsAdcRiskOn = isCmsAdcRiskOn;
	}

	public String getIsCmsAdcRiskOn() {
		return isCmsAdcRiskOn;
	}

	public void setIsProjectFinOperateStagOn(String isProjectFinOperateStagOn) {
		this.isProjectFinOperateStagOn = isProjectFinOperateStagOn;
	}

	public String getIsProjectFinOperateStagOn() {
		return isProjectFinOperateStagOn;
	}

	public void setUpdateItem2(String updateItem2) {
		this.updateItem2 = updateItem2;
	}

	public String getUpdateItem2() {
		return updateItem2;
	}

	public void setUpdateItem3(String updateItem3) {
		this.updateItem3 = updateItem3;
	}

	public String getUpdateItem3() {
		return updateItem3;
	}

	/**
	 * 設定ADC號碼補建或變更
	 * 
	 * @param updateItem4
	 */
	public void setUpdateItem4(String updateItem4) {
		this.updateItem4 = updateItem4;
	}

	/**
	 * 取得ADC號碼補建或變更
	 * 
	 * @return updateItem4
	 */
	public String getUpdateItem4() {
		return updateItem4;
	}

	/**
	 * 設定產品種類(前案)
	 * 
	 * @param lnTypeOn
	 */
	public void setLnTypeOn(String lnTypeOn) {
		this.lnTypeOn = lnTypeOn;
	}

	/**
	 * 取得產品種類(前案)
	 * 
	 * @return lnTypeOn
	 */
	public String getLnTypeOn() {
		return lnTypeOn;
	}

	/**
	 * 設定ADC案件編號(前案)
	 * 
	 * @param adcCaseNoOn
	 */
	public void setAdcCaseNoOn(String adcCaseNoOn) {
		this.adcCaseNoOn = adcCaseNoOn;
	}

	/**
	 * 取得ADC案件編號(前案)
	 * 
	 * @return adcCaseNoOn
	 */
	public String getAdcCaseNoOn() {
		return adcCaseNoOn;
	}
	
	/** 高品質專案融資_選項1 **/
	public void setIsHighQualityProjOpt_1(String isHighQualityProjOpt_1) {
		this.isHighQualityProjOpt_1 = isHighQualityProjOpt_1;
	}

	public String getIsHighQualityProjOpt_1() {
		return isHighQualityProjOpt_1;
	}

	/** 高品質專案融資_選項2 **/
	public void setIsHighQualityProjOpt_2(String isHighQualityProjOpt_2) {
		this.isHighQualityProjOpt_2 = isHighQualityProjOpt_2;
	}

	public String getIsHighQualityProjOpt_2() {
		return isHighQualityProjOpt_2;
	}

	/** 高品質專案融資_選項3 **/
	public void setIsHighQualityProjOpt_3(String isHighQualityProjOpt_3) {
		this.isHighQualityProjOpt_3 = isHighQualityProjOpt_3;
	}

	public String getIsHighQualityProjOpt_3() {
		return isHighQualityProjOpt_3;
	}

	/** 高品質專案融資_選項4 **/
	public void setIsHighQualityProjOpt_4(String isHighQualityProjOpt_4) {
		this.isHighQualityProjOpt_4 = isHighQualityProjOpt_4;
	}

	public String getIsHighQualityProjOpt_4() {
		return isHighQualityProjOpt_4;
	}

	/** 高品質專案融資_選項5 **/
	public void setIsHighQualityProjOpt_5(String isHighQualityProjOpt_5) {
		this.isHighQualityProjOpt_5 = isHighQualityProjOpt_5;
	}

	public String getIsHighQualityProjOpt_5() {
		return isHighQualityProjOpt_5;
	}

	/** 高品質專案融資_最終結果 **/
	public void setIsHighQualityProjResult(String isHighQualityProjResult) {
		this.isHighQualityProjResult = isHighQualityProjResult;
	}

	public String getIsHighQualityProjResult() {
		return isHighQualityProjResult;
	}
	
	/** 高品質專案融資_選項1(前案) **/
	public void setIsHighQualityProjOpt_1On(String isHighQualityProjOpt_1On) {
		this.isHighQualityProjOpt_1On = isHighQualityProjOpt_1On;
	}

	public String getIsHighQualityProjOpt_1On() {
		return isHighQualityProjOpt_1On;
	}

	/** 高品質專案融資_選項2(前案) **/
	public void setIsHighQualityProjOpt_2On(String isHighQualityProjOpt_2On) {
		this.isHighQualityProjOpt_2On = isHighQualityProjOpt_2On;
	}

	public String getIsHighQualityProjOpt_2On() {
		return isHighQualityProjOpt_2On;
	}

	/** 高品質專案融資_選項3(前案) **/
	public void setIsHighQualityProjOpt_3On(String isHighQualityProjOpt_3On) {
		this.isHighQualityProjOpt_3On = isHighQualityProjOpt_3On;
	}

	public String getIsHighQualityProjOpt_3On() {
		return isHighQualityProjOpt_3On;
	}

	/** 高品質專案融資_選項4(前案) **/
	public void setIsHighQualityProjOpt_4On(String isHighQualityProjOpt_4On) {
		this.isHighQualityProjOpt_4On = isHighQualityProjOpt_4On;
	}

	public String getIsHighQualityProjOpt_4On() {
		return isHighQualityProjOpt_4On;
	}

	/** 高品質專案融資_選項5(前案) **/
	public void setIsHighQualityProjOpt_5On(String isHighQualityProjOpt_5On) {
		this.isHighQualityProjOpt_5On = isHighQualityProjOpt_5On;
	}

	public String getIsHighQualityProjOpt_5On() {
		return isHighQualityProjOpt_5On;
	}

	/** 高品質專案融資_最終結果(前案) **/
	public void setIsHighQualityProjResultOn(String isHighQualityProjResultOn) {
		this.isHighQualityProjResultOn = isHighQualityProjResultOn;
	}

	public String getIsHighQualityProjResultOn() {
		return isHighQualityProjResultOn;
	}

	public String getUpdateItem5() {
		return updateItem5;
	}

	public void setUpdateItem5(String updateItem5) {
		this.updateItem5 = updateItem5;
	}

	public String getExceptFlag() {
		return exceptFlag;
	}

	public void setExceptFlag(String exceptFlag) {
		this.exceptFlag = exceptFlag;
	}

	public String getExceptFlagOn() {
		return exceptFlagOn;
	}

	public void setExceptFlagOn(String exceptFlagOn) {
		this.exceptFlagOn = exceptFlagOn;
	}

	public String getExceptFlagQAisY() {
		return exceptFlagQAisY;
	}

	public void setExceptFlagQAisY(String exceptFlagQAisY) {
		this.exceptFlagQAisY = exceptFlagQAisY;
	}

	public String getExceptFlagQAisYOn() {
		return exceptFlagQAisYOn;
	}

	public void setExceptFlagQAisYOn(String exceptFlagQAisYOn) {
		this.exceptFlagQAisYOn = exceptFlagQAisYOn;
	}

	public String getExceptFlagQAPlus() {
		return exceptFlagQAPlus;
	}

	public void setExceptFlagQAPlus(String exceptFlagQAPlus) {
		this.exceptFlagQAPlus = exceptFlagQAPlus;
	}

	public String getExceptFlagQAPlusOn() {
		return exceptFlagQAPlusOn;
	}

	public void setExceptFlagQAPlusOn(String exceptFlagQAPlusOn) {
		this.exceptFlagQAPlusOn = exceptFlagQAPlusOn;
	}
}
