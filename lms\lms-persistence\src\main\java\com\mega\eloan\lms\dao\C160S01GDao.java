/* 
 * C160S01GDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01G;

/** 分段利率明細檔 **/
public interface C160S01GDao extends IGenericDao<C160S01G> {

	C160S01G findByOid(String oid);
	
	List<C160S01G> findByMainId(String mainId);
	
	C160S01G findByUniqueKey(String mainId, Integer seq);

	List<C160S01G> findByIndex01(String mainId, Integer seq);

}