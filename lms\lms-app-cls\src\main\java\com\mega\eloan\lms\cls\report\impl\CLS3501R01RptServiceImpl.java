package com.mega.eloan.lms.cls.report.impl;

import java.text.ParseException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS3501M01Page;
import com.mega.eloan.lms.cls.report.CLS3101R01RptService;
import com.mega.eloan.lms.cls.service.CLS3501Service;
import com.mega.eloan.lms.model.C124M01A;
import com.mega.eloan.lms.model.C124M01B;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

@Service("cls3501r01rptservice")
public class CLS3501R01RptServiceImpl extends AbstractReportService implements CLS3101R01RptService  {

	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CodeTypeService codetypeservice;

	@Resource
	CLS3501Service cls3501Service;

	@Override
	public String getReportTemplateFileName() {
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		return "report/cls/CLS3501R01_" + locale.toString() + ".rpt";
	}

	@Override
	public void setReportData(ReportGenerator rptGenerator,
			PageParameters params) throws CapException, ParseException {
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS3501M01Page.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		C124M01A c124m01a = null;
		List<C124M01B> c124m01blist = null;
		String branchName = null;
		Locale locale = null;

		try {
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c124m01a = cls3501Service.findModelByOid(C124M01A.class, mainOid);
			if (c124m01a == null) {
				c124m01a = new C124M01A();
			}
			
			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			c124m01blist = (List<C124M01B>)cls3501Service.findListByMainId(C124M01B.class, mainId);
			if (!Util.isEmpty(c124m01blist) &&
					Util.notEquals(c124m01a.getDocStatus(), CreditDocStatusEnum.海外_編製中)) {
				StringBuilder bossId = new StringBuilder("");
				for (C124M01B c124m01b : c124m01blist) {
					String type = Util.trim(c124m01b.getStaffJob());
					String userId = Util.trim(c124m01b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {	//L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {	//L3. 分行授信主管
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {	//L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {	//L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}
			String updater = Util.trim(c124m01a.getUpdater()) + " " +
					Util.trim(lmsService.getUserName(c124m01a.getUpdater()));
			rptVariableMap.put("C124M01A.UPDATER", updater);
			rptVariableMap.put("C124M01B.RECHECKID", recheckid);
			rptVariableMap.put("C124M01B.BOSSID", bossid);
			rptVariableMap.put("C124M01B.MANAGERID", managerid);
			rptVariableMap.put("C124M01B.APPRID", apprid);

			branchName = Util.nullToSpace(branchService.getBranchName(
					Util.nullToSpace(c124m01a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);

			Map<String, CapAjaxFormResult> codeTypes = codetypeservice
					.findByCodeType(new String[] {"HaveNo","YesNo"});

			rptVariableMap.put("C124M01A.CUST", Util.trim(c124m01a.getCustId()) + " " +
					Util.trim(c124m01a.getDupNo()) + " " + Util.trim(c124m01a.getCustName()));

			rptVariableMap.put("C124M01A.APPROVENO", Util.trim(c124m01a.getApproveNo()));
			rptVariableMap.put("C124M01A.GRNTPAPER", Util.trim(c124m01a.getGrntPaper()));
			rptVariableMap.put("C124M01A.AGREEMENT", this.showPic(
					Util.trim(c124m01a.getAgreement()), "1", codeTypes, locale));
			rptVariableMap.put("C124M01A.ISCREDITCHECK", this.showPic(
					Util.trim(c124m01a.getIsCreditCheck()), "1", codeTypes, locale));
			rptVariableMap.put("C124M01A.QUALICODE", this.showPic(
					Util.trim(c124m01a.getQualiCode()), "1", codeTypes, locale));
			rptVariableMap.put("C124M01A.ISCREDITABNORMAL", this.showPic(
					Util.trim(c124m01a.getIsCreditAbnormal()), "2", codeTypes, locale));
			rptVariableMap.put("C124M01A.BORROWERBIRTHDAY", CapDate.formatDate(
					c124m01a.getBorrowerBirthDay(), "yyyy-MM-dd"));
			rptVariableMap.put("C124M01A.APPLYLOANDAY", CapDate.formatDate(
					c124m01a.getApplyLoanDay(), "yyyy-MM-dd"));
			rptVariableMap.put("C124M01A.APPLYLOAN", NumConverter.addComma(
					Util.trim(c124m01a.getApplyLoan())) + "元");
			rptVariableMap.put("C124M01A.TEL", Util.trim(c124m01a.getTel1()) +
					(Util.isEmpty(Util.trim(c124m01a.getTel2())) ? "" : "-") +
					Util.trim(c124m01a.getTel2()));
			rptVariableMap.put("C124M01A.OWNERCELLPHONE", Util.trim(c124m01a.getOwnerCellphone1()) +
					(Util.isEmpty(Util.trim(c124m01a.getOwnerCellphone2())) ? "" : "-") +
					Util.trim(c124m01a.getOwnerCellphone2()));
			rptVariableMap.put("C124M01A.ISEMAIL", this.showPic(
					Util.trim(c124m01a.getIsEmail()), "4", codeTypes, locale));
			rptVariableMap.put("C124M01A.EMAIL", Util.trim(c124m01a.getEmail()));
			CodeType counties = codetypeservice.findByCodeTypeAndCodeValue(
					"counties", Util.trim(c124m01a.getCity()));
			CodeType counties2 = codetypeservice.findByCodeTypeAndCodeValue(
					"counties" + Util.trim(c124m01a.getCity()),
					Util.trim(c124m01a.getDist()));
			rptVariableMap.put("C124M01A.ADDR", Util.trim(c124m01a.getZip()) +
					(counties == null ? "" : counties.getCodeDesc()) +
					(counties2 == null ? "" : counties2.getCodeDesc()) +
					Util.trim(c124m01a.getVillageName()) +
					(Util.isEmpty(Util.trim(c124m01a.getVillageName())) ? "" :
							this.showPic(Util.trim(c124m01a.getVillage()), "5", codeTypes, locale)) +
					Util.trim(c124m01a.getNeighborhood()) +
					(Util.isEmpty(Util.trim(c124m01a.getNeighborhood())) ? "" : "鄰") +
					Util.trim(c124m01a.getRoadName()) + this.showPic(
							Util.trim(c124m01a.getRoad()), "6", codeTypes, locale) +
					Util.trim(c124m01a.getSec()) +
					(Util.isEmpty(Util.trim(c124m01a.getSec())) ? "" : "段") +
					Util.trim(c124m01a.getLane()) +
					(Util.isEmpty(Util.trim(c124m01a.getLane())) ? "" : "巷") +
					Util.trim(c124m01a.getAlley()) +
					(Util.isEmpty(Util.trim(c124m01a.getAlley())) ? "" : "弄") +
					Util.trim(c124m01a.getNo1()) +
					((Util.isEmpty(Util.trim(c124m01a.getNo1())) &&
							Util.isEmpty(Util.trim(c124m01a.getNo2()))) ? "" :
							(Util.isEmpty(Util.trim(c124m01a.getNo2())) ? "號" : "之")) +
                    Util.trim(c124m01a.getNo2()) +
					(Util.isEmpty(Util.trim(c124m01a.getNo2())) ? "" : "號") +
					Util.trim(c124m01a.getFloor1()) +
					(Util.isEmpty(Util.trim(c124m01a.getFloor1())) ? "" : "樓") +
					(Util.isEmpty(Util.trim(c124m01a.getFloor2())) ? "" : "之") +
					Util.trim(c124m01a.getFloor2()) +
					(Util.isEmpty(Util.trim(c124m01a.getRoom())) ? "" : "(") +
					Util.trim(c124m01a.getRoom()) +
					(Util.isEmpty(Util.trim(c124m01a.getRoom())) ? "" : "室)"));
			rptVariableMap.put("C124M01A.IMPORTER", Util.trim(c124m01a.getImporterNo()) +
					"　" + Util.trim(c124m01a.getImporterName()));
			rptVariableMap.put("C124M01A.IMPORTERCELLPHONE",
					Util.trim(c124m01a.getImporterCellphone1()) +
					(Util.isEmpty(Util.trim(c124m01a.getImporterCellphone2())) ? "" : "-") +
					Util.trim(c124m01a.getImporterCellphone2()));
			rptVariableMap.put("C124M01A.IMPORTERTEL", Util.trim(c124m01a.getImporterTel1()) +
					(Util.isEmpty(Util.trim(c124m01a.getImporterTel2())) ? "" : "-") +
					Util.trim(c124m01a.getImporterTel2()) + "　分機 " +
					Util.trim(c124m01a.getImporterTelExt()));
			rptVariableMap.put("C124M01A.IMPORTEREMAIL", Util.trim(c124m01a.getImporterEmail()));

			rptGenerator.setLang(locale);
			rptGenerator.setVariableData(rptVariableMap);
		} finally {

		}
		
	}

	/**
	 * kind=1, 寫死 "是 or 否"
	 * kind=2, 寫死 "有 or 無"
	 * kind=3, 1:是. 2:否
	 * kind=4, 1:有. 2:無
	 * kind=5, 1:里. 2:村
	 * kind=6, 1:"". 2:路. 3:街
	 **/
	private String showPic(String value, String kind,
						   Map<String, CapAjaxFormResult> codeTypes, Locale locale) {
		Map<String, String> yesNoMap = null;
		StringBuffer str = new StringBuffer();
		try {
			String code = "";
			if(Util.equals(kind, "1") || Util.equals(kind, "3")){
				code = "YesNo";
			} else if(Util.equals(kind, "2") || Util.equals(kind, "4")){
				code = "HaveNo";
			}

			if(Util.equals(kind, "1") || Util.equals(kind, "2")){
				str.append(codeTypes.get(code).get(value));
			} else if(Util.equals(kind, "3") || Util.equals(kind, "4")){
				str.append((Util.equals(value, "1") ? "■" : "□"));
				str.append(codeTypes.get(code).get("1"));
				str.append((Util.equals(value, "2") ? "■" : "□"));
				str.append(codeTypes.get(code).get("2"));
			} else if(Util.equals(kind, "5")){
				Map<String, String> k4Map = new HashMap<String, String>();
				k4Map.put("1", "里");
				k4Map.put("2", "村");
				str.append(k4Map.get(value));
			} else if(Util.equals(kind, "6")){
				Map<String, String> k5Map = new HashMap<String, String>();
				k5Map.put("1", "");
				k5Map.put("2", "路");
				k5Map.put("3", "街");
				str.append(k5Map.get(value));
			}
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		} finally {
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
		}
		return str.toString();
	}

}
