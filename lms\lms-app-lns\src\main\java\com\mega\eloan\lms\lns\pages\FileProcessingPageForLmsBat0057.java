package com.mega.eloan.lms.lns.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.iisi.cap.util.CapString;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;

/**
 * http://192.168.59.141/lms-web/app/simple/FileProcessingPageForLmsBat0057?fileOid=483DD160B93911EF93CD2E4CC0A83B8D
 * 
 * security.xml要增加設定
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/simple/FileProcessingPageForLmsBat0057")
public class FileProcessingPageForLmsBat0057 extends
		AbstractFileDownloadPage {

	@Autowired
	DocFileService fileSrv;

	public FileProcessingPageForLmsBat0057(PageParameters parameters) {

		super();

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * tw.com.iisi.cap.base.pages.AbstractCapPage#execute(org.apache.wicket.
	 * PageParameters)
	 */
	@Override
	public void execute(ModelMap model, PageParameters params) {
		this.fileDownloadName = params.getString("fileDownloadName");
		this.serviceName = params.getString("serviceName");
		
		//UPGRADETODO 下面寫法要改寫，之後研究
		//getRequestCycle().setRequestTarget(
				//new ResourceStreamRequestTarget(new FileDownloadStreamWriter(
						//params)));
	}

	@Override
	public String getDownloadFileName() {
		return this.fileDownloadName;
	}

	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	/**
	 * 取得下載檔案內容
	 *
	 * @param params PageParameters object
	 * @return 檔案內容
	 * @throws Exception
	 */
	@Override
	protected byte[] getContent(PageParameters params) throws Exception {

		String fileOid = params.getString("fileOid");
		if (CapString.isEmpty(fileOid)) {
			return ("<h1>找不到此檔案，請洽資訊處</h1>").getBytes();
		} else {
			DocFile docFile = fileSrv.read(fileOid);
			if (docFile != null) {
				String fileName = params.getString("dwnfileName");
				if (CapString.isEmpty(fileName)) {
					fileName = docFile.getSrcFileName();
					if(CapString.isEmpty(fileName)){
						fileName = "企金處公司訪談紀錄表.zip";
					}
				}
				return docFile.getData();
			} else {
				return ("<h1>無法下載此文件，請洽資訊處</h1>").getBytes();
			}
		}
	}
	
	@Override
	//UPGRADE
	protected String getViewName() {
		return null;
	}
	
	//UPGRADETODO 下面寫法要改寫，之後研究
//	final class FileDownloadStreamWriter extends AbstractResourceStreamWriter {
//
//		private static final long serialVersionUID = 1L;
//		private PageParameters params;
//
//		/**
//		 * constructor
//		 *
//		 * @param params
//		 *            PageParameters
//		 */
//		public FileDownloadStreamWriter(PageParameters params) {
//			super();
//			this.params = params;
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see
//		 * org.apache.wicket.util.resource.IResourceStreamWriter#write(java.
//		 * io.OutputStream)
//		 */
//		public void write(OutputStream output) {
//			try {
//				output.write(getContent(params));
//				output.flush();
//			} catch (Exception ex) {
//				LOGGER.error(ex.getMessage(), ex);
//				throw new RuntimeException(ex);
//			}
//		}
//
//		/*
//		 * (non-Javadoc)
//		 *
//		 * @see org.apache.wicket.util.resource.IResourceStream#getContentType()
//		 */
//		public String getContentType() {
//			return "application/zip";
//		}
//
//	}
}
