/* 
 * CLS1131V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金徵信作業 - 基本資料
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131v01")
public class CLS1131V01Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		
		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<>();
		list.add(LmsButtonEnum.Filter);
		if(_Accept||_Modify){
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Modify);
			list.add(LmsButtonEnum.Delete);
			list.add(LmsButtonEnum.Send);
		}
		list.add(LmsButtonEnum.View);
		list.add(LmsButtonEnum.Copy);
		
		if(_Accept||_Modify){
			if(UtilConstants.BankNo.資訊處.equals(user.getUnitNo())){
				list.add(LmsButtonEnum.CallCenterSendCreditCase);
			}else if(UtilConstants.BankNo.消金業務處.equals(user.getUnitNo())){
				//btns.add(SimpleButtonEnum.CallCenterSendCreditCase);				
			}
		}
		addToButtonPanel(model, list);

		// build i18n
		renderJsI18N(CLS1131V01Page.class);
		
		// add panel
		// add(new CLS1131S01Panel("CLS1131S01"));

		model.addAttribute("loadScript","loadScript('pagejs/cls/CLS1131V01Page');");
	}// ;

}
