var results;
var TermGroupAction = {
	clsJobType1: "",//歡喜信貸行業別
	clsJobType2: "",
	build: function(){
		ilog.debug("@CLS3201V00Page.js > build");
		TermGroupAction.initObj();
		TermGroupAction.initObjEvent();
	},
	openTermGroupRuleQuery: function(){
		//開啟條件框
		$("#termGroupRuleThickBox").thickbox({ // 使用選取的內容進行彈窗
			//termGroupRule=歡喜信貸客群查詢 
		    title: i18n.cls3201v00["termGroupRule.title01"],
		    width: 600,
		    height: 500,
		    valign: "bottom",
		    align: "center",
		    i18n: i18n.def,
		    buttons: {
		    	"計算": function(){
		    		var $TermGroupForm = $("form#termGroupRuleForm");
		    		//非營利事業未勾選、 服務單位實收資本總額(新台幣元) 未勾無 時，檢核"服務單位實收資本額" 需>0
		    		if(!$TermGroupForm.find("#isNPO").is(":checked") && $TermGroupForm.find("#juPaidUpCapital").val() == "0" && !$("#hasJuTotalCapital").is(":checked")){
		    			$("#span_termGroupRuleResultText").text(i18n.cls3201v00["termGroupRule.juTotalCapitalNeedMoreThenZero"]);
		    			return;
		    		}
		    		var hasJuTotalCapital = "Y";
		    		if($TermGroupForm.find("input[name='hasJuTotalCapital']:checked").length > 0){
		    			hasJuTotalCapital = "N";
		    		}
                    //call 決策api /api/eloan/termGroupRule
                    $.ajax({
                        handler: "cls1131formhandler",
                        action: "brmp_termGroupRule",
                        data: { "mainId":""
                        	, "custId": "termGroup"//試算功能不放ID
                        	, "lnClass": "71"//預設歡喜信貸
                        	, "clsJobType2": $TermGroupForm.find("#clsJobType2").val()//行業代碼
                        	, "positionCode": $TermGroupForm.find("#clsJobTitle").val()//職位代碼
                        	, "juPaidUpCapital": $TermGroupForm.find("#juPaidUpCapital").val()//資本額 (若非公司請帶0)
                        	, "hasJuTotalCapital": hasJuTotalCapital
                        	, "payAmt": $TermGroupForm.find("#payAmt").val()//年收入
                        },
                    }).done(function(jsonparm){
						//results=jsonparm;
                        if(jsonparm.brmp004data.result.errorMsg != null && jsonparm.brmp004data.result.errorMsg != ""){//sow錯誤訊息
                        	$("#span_termGroupRuleResultText").text(jsonparm.brmp004data.result.errorMsg);
                        }
                        if(jsonparm.brmp004data.result.resultMsg != null && jsonparm.brmp004data.result.resultMsg != ""){//
                        	if(jsonparm.brmp004data.result.termGroup != null){//最終客群分類 "X":不承作 , "S":小額 , "N":普惠 , "G":優質, "E":行員
                        		var resultmsg = "";
                        		$("select#termGroup").val(jsonparm.brmp004data.result.termGroup);
                        		resultmsg += $("select#termGroup option:selected").text();
                        		if(jsonparm.brmp004data.result.applyDBRType == "A"){
                        			resultmsg += i18n.cls3201v00["C101S01B.DBR15"];
                        		}
                        		$("#span_termGroupRuleResultText").text(resultmsg);
                        	}else{
                        		$("select#termGroup").val("");
                        		$("#span_termGroupRuleResultText").text("");
                        	}
                        }
					});
                },
		        "關閉": function(){
		        	$.thickbox.close();
			    }
		    }
		});
	},
	initObj: function(){
		//初始化歡喜信貸行業別
		var juType1_v = $("select#clsJobType1").attr("data-codetype") || "";
		if (juType1_v) {
			$.ajax({
				type : "POST",
				handler : "lms1015m01formhandler",
				async : false,// 用「同步」的方式
				data : {
					formAction : "codeTypeWithOrder",
					key : juType1_v
				},
			}).done(function(obj){
				var chooseItem1 = $("select#clsJobType1");
				var _addSpace = false;
				if (chooseItem1.attr("space") == "true") {
					_addSpace = true;
				}

				$.each(obj.itemOrder, function(idx, c_val) {
					var currobj = {};
					var c_desc = obj.item[c_val];
					currobj[c_val] = c_desc;

					chooseItem1.setItems({
						item : currobj,
						format : "{key}",
						clear : false,
						space : (_addSpace ? (idx == 0) : false)
					});
				});
			});
		}
		//初始化歡喜信貸職業別
		var juType_v = $("select#clsJobTitle").attr("data-codetype") || '';
		if (juType_v) {
			$.ajax({
				type : "POST",
				handler : "lms1015m01formhandler",
				async : false,// 用「同步」的方式
				data : {
					formAction : "codeTypeWithOrder",
					key : juType_v
				},
			}).done(function(obj){
				var chooseItem1 = $("select#clsJobTitle");
				var _addSpace = false;
				if (chooseItem1.attr("space") == "true") {
					_addSpace = true;
				}
				$.each(obj.itemOrder, function(idx, c_val) {
					var currobj = {};
					var c_desc = obj.item[c_val];
					currobj[c_val] = c_desc;

					// select
					chooseItem1.setItems({
						item : currobj,
						format : "{key}",
						clear : false,
						space : (_addSpace ? (idx == 0) : false)
					});
				});
			});
		}
		
		//客群
		//$("#termGroup option[value='GA']").remove();//移除優質A類
		//$("#termGroup option[value='GB']").remove();//移除優質B類
		//$("#termGroup").css("background-color", "#FFF");
		//$("#termGroup").attr("disabled","disabled");
		//$("#termGroup").hide();
	},
	initObjEvent: function(){
		//初始化事件
		ilog.debug("@CLS3201V00Page.js > initObjEvent");
		var $TermGroupForm = $("form#termGroupRuleForm");
    	//歡喜信貸行業別變動
		$TermGroupForm.find("#clsJobType1").change(function(){
            var code = $TermGroupForm.find("#clsJobType1").val();
            if (code) {
                var item = CommonAPI.loadCombos("clsJobType" + code);
                $TermGroupForm.find("#clsJobType2").setItems({
                    item: item["clsJobType" + code],
                    format: "{key}"
                });
            }
            //職業mapping
            clsJobType1 = code;
    		var capital = TermGroupAction.checkStringIsNotEmpty($TermGroupForm.find("#juPaidUpCapital").val()) ? $TermGroupForm.find("#juPaidUpCapital").val() : "0";//服務單位實收資本額(新台幣元) 
    		var isNPO = $TermGroupForm.find("#isNPO").is(":checked");//非營利事業
            TermGroupAction.mappingClsJob(clsJobType1, clsJobType2, clsJobTitle, capital, isNPO);
        });
		//非營利事業
		$TermGroupForm.find("#isNPO").change(function(){
			TermGroupAction.controlIJuPaidUpCapital($TermGroupForm);
		});
		// 服務單位實收資本總額(新台幣元)   無
		$TermGroupForm.find("#hasJuTotalCapital").change(function(){
			TermGroupAction.controlIJuPaidUpCapital($TermGroupForm);
		});
	},
	mappingClsJob: function(clsJobType1, clsJobType2, clsJobTitle, capital, isNPOflag){
		//mapping 行業別
    	if(!(TermGroupAction.checkStringIsNotEmpty(clsJobType1) && TermGroupAction.checkStringIsNotEmpty(clsJobType2) 
    			&& TermGroupAction.checkStringIsNotEmpty(clsJobTitle) && TermGroupAction.checkStringIsNotEmpty(capital))){
    		return false;
    	}
    	//不為空時，把資料丟到後端。
     	$.ajax({
            handler: "cls1131formhandler",
            action: "mappingClsJob",
            data: {
            	"clsJobType1": clsJobType1,
            	"clsJobType2": clsJobType2,
            	"clsJobTitle": clsJobTitle,
            	"capital": capital,
            	"isNPO": isNPOflag 
            },
        }).done(function(json){
			$("select#jobType1").val(json.jobType1);
    		$("select#jobType1").trigger("change");
        	$("select#jobType2").val(json.jobType2);
        	$("select#jobType2").trigger("change");
        	$("select#jobTitle").val(json.jobTitle);
			$("select#jobTitle").trigger("change");
		});
    },
    //檢核項目
    checkStringIsNotEmpty: function(para){
    	//驗證欄位
    	if(typeof (para) != "string"){
    		return false;
    	}
    	if(para === null || para === ""){
    	    return false;
    	}else{
    		return true;
    	}
    },
    controlIJuPaidUpCapital: function($TermGroupForm) {
    	var isNPOflag = $TermGroupForm.find("#isNPO").is(":checked");//非營利事業
		var hasJuTotalCapitallag = $TermGroupForm.find("#hasJuTotalCapital").is(":checked");//服務單位實收資本總額(新台幣元)   無
    	if(isNPOflag || hasJuTotalCapitallag){//有勾非營利 或   服務單位實收資本總額(新台幣元) 無，數字反灰鎖住寫0
    		$("#juPaidUpCapital").val('0');
    		$("#juPaidUpCapital").trigger('change');
    		$("#juPaidUpCapital").prop("disabled", "disabled");
    		$("#juPaidUpCapital").css("background-color", "#ccc");
    	}
    	else{
    		$('#juPaidUpCapital').trigger('change');
    		$("#juPaidUpCapital").removeAttr("disabled");
    		$("#juPaidUpCapital").css("background-color", "#FFF");
    	}
    }
}
TermGroupAction.build();
TermGroupAction.openTermGroupRuleQuery();
