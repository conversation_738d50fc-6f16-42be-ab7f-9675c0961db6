package tw.com.jcs.flow.provider;

import tw.com.jcs.flow.FlowInstance;

/**
 * <h1>流程編號提供者</h1> 實作此介面，以自訂流程取號規則
 * 
 * <AUTHOR> Software Inc.
 */
public interface IdProvider {

    /**
     * 取得下一個流程編號
     */
    Object getNextId();

    /**
     * 取得下一個流程編號
     * 
     * @param parameter
     *            參數列
     */
    Object getNextId(Object[] parameter);

    /**
     * 取得下一個子流程編號
     * 
     * @param parent
     *            父流程
     */
    Object getNextId(FlowInstance parent);

}
