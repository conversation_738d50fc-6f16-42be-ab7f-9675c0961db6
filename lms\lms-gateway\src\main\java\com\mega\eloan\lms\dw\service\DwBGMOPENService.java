package com.mega.eloan.lms.dw.service;

import java.util.Map;

/**
 * 全國營業(稅籍)登記資料 DWADM.OTS_DW_BGMOPEN
 */
public interface DwBGMOPENService {
	/**
	 * 取得全國營業(稅籍)登記資料
	 * 
	 * @param custId
	 * @return
	 */
	Map<String, Object> getDataFromTaxation(String custId);

	/**
	 * 綜合營業、停業、停業以外之非營業中資料
	 * 
	 * @param custId
	 * @return
	 */
	Map<String, Object> getDataFromTaxationAllOpenStatus(String custId);
}
