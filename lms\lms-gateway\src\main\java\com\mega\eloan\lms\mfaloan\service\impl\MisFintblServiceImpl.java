/* 
 * MisFintblServiceImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapString;

import com.mega.eloan.lms.mfaloan.service.MisFintblService;

/**
 * <pre>
 * 財務資料檔
 * </pre>
 * 
 * @since 2011/8/31
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/8/31,iristu,new
 *          <li>2011/9/01,iristu,update
 *          <li>2011/1/31,yung<PERSON><PERSON><PERSON>,update 取得財務資料檔(G112S01A)
 *          <li>2013/05/08,EL07623,getFinancialData2增加合併報表別條件
 *          <li>J-103-0137 EL07623修正徵信報告第玖章節之集團財務資訊引進來源
 *          </ul>
 */
@Service
public class MisFintblServiceImpl extends AbstractMFAloanJdbc implements
		MisFintblService {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisFintblService#addFintbl(java.util
	 * .Map)
	 */
	@Override
	public int addFintbl(Map<String, Object> inputData) {
		Object[] delCols = { inputData.get(finCols[0]),
				inputData.get(finCols[1]), inputData.get(finCols[2]),
				inputData.get(finCols[3]), inputData.get(finCols[4]) };
		// 先刪除
		getJdbc().update("FINTBL.deleteByPK", delCols);
		// 再新增
		ArrayList<Object> insCols = new ArrayList<Object>(inputData.size());
		for (String key : finCols) {
			insCols.add(inputData.get(key));
		}
		int rtn = getJdbc().update("FINTBL.insert", insCols.toArray());
		return rtn;
	}

	// @Override
	// public List<Map<String, Object>> getFinancialData(int year, int season,
	// String custId, String dupNo, String conso) {
	// return getFinancialData2(year, season, custId, dupNo, conso);
	// }

	@Override
	public List<Map<String, Object>> getFinancialData(int year, int season,

	String custId, String dupNo) {

		List<String> params = new ArrayList<String>();

		String dataYear = StringUtils.leftPad(String.valueOf(year), 3, '0');

		String tBegdt2 = StringUtils.leftPad(String.valueOf(year - 1), 3, '0');

		String tBegdt = dataYear + "0101", tEnddt = "";

		switch (season) {

		case 1:

			tEnddt = dataYear + "0331";

			break;

		case 2:

			tEnddt = dataYear + "0630";

			break;

		case 3:

			tEnddt = dataYear + "0930";

			break;

		default:

			tEnddt = dataYear + "1231";

		}

		params.add(custId);

		params.add(dupNo);

		params.add(tBegdt);

		params.add(tEnddt);

		params.add(custId);

		params.add(dupNo);

		params.add(dataYear);

		params.add(tBegdt2);

		// J-103-0137,EL07623,修改為以IFRSs個別/個體第一順位、GAAP單一第二順位、IFRSs合併第三順位、GAAP合併最後

		return getJdbc().queryForList("FINTBL.getFinancialData",

		params.toArray());

	}

	public List<Map<String, Object>> getFinancialData1(int year, int season,
			String custId, String dupNo) {
		List<String> params = new ArrayList<String>();
		String sql = getSqlBySqlId("FINTBL.getFinancialData");
		params.add(custId);
		String inCause = "", appedSql = "";
		params.add(dupNo);
		if (year >= 0) {
			appedSql += " AND LEFT(BEGDT,3) = ?";
			String dataYear = StringUtils.leftPad(String.valueOf(year), 3, '0');
			params.add(dataYear);
		}
		if (season >= 0) {
			appedSql += " AND PERIODTYPE IN {0}";
			switch (season) {
			case 1:
				inCause = "( ? )";
				params.add("3");
				break;
			case 2:
				inCause = "( ?, ? )";
				params.add("2");
				params.add("4");
				break;
			case 3:
				inCause = "( ? )";
				params.add("5");
				break;
			default:
				inCause = "( ?, ? )";
				params.add("1");
				params.add("6");
			}
		}
		sql = StringUtils.replace(sql, "{0}", appedSql);
		sql = MessageFormat.format(sql, new Object[] { inCause });
		return getJdbc().queryForList(sql, params.toArray());
	}

	public List<Map<String, Object>> getFinancialData2(int year, int season,
			String custId, String dupNo, String conso) {
		List<String> params = new ArrayList<String>();
		String sql = getSqlBySqlId("FINTBL.getFinancialData2");
		String appendSql1 = "", appendSql2 = "";
		params.add(custId);
		params.add(dupNo);

		if (year >= 0 && season >= 0) {
			String dataYear = StringUtils.leftPad(String.valueOf(year), 3, '0');
			String tBegdt2 = StringUtils.leftPad(String.valueOf(year - 1), 3,
					'0');
			String tBegdt = dataYear + "0101", tEnddt = "";
			switch (season) {
			case 1:
				tEnddt = dataYear + "0331";
				break;
			case 2:
				tEnddt = dataYear + "0630";
				break;
			case 3:
				tEnddt = dataYear + "0930";
				break;
			default:
				tEnddt = dataYear + "1231";
			}
			if (!CapString.isEmpty(conso)) {
				appendSql1 = " AND BEGDT = ? AND ENDDT = ? AND FINFLAG = ?";
				params.add(tBegdt);
				params.add(tEnddt);
				params.add(conso);
				appendSql2 = " OR ((CUSTID= ? AND DUPNO = ? AND FINFLAG = ? AND RIGHT(BEGDT,4) <> '0101' AND (left(BEGDT,3) = ? OR left(BEGDT,3)=?) AND integer(left(BEGDT,3))-integer(left(ENDDT,3))=-1) AND(integer(right(left(BEGDT,5),2))-integer(right(left(ENDDT,5),2))=1))";
				params.add(custId);
				params.add(dupNo);
				params.add(conso);
				params.add(dataYear);
				params.add(tBegdt2);
			} else {
				appendSql1 = " AND BEGDT = ? AND ENDDT = ?";
				params.add(tBegdt);
				params.add(tEnddt);
				appendSql2 = " OR ((CUSTID= ? AND DUPNO = ? AND RIGHT(BEGDT,4) <> '0101' AND (left(BEGDT,3) = ? OR left(BEGDT,3)=?) AND integer(left(BEGDT,3))-integer(left(ENDDT,3))=-1) AND(integer(right(left(BEGDT,5),2))-integer(right(left(ENDDT,5),2))=1))";
				params.add(custId);
				params.add(dupNo);
				params.add(dataYear);
				params.add(tBegdt2);
			}

		}
		sql = MessageFormat
				.format(sql, new Object[] { appendSql1, appendSql2 });
		return getJdbc().queryForList(sql, params.toArray());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.MisFintblService#findByGrpId(java.
	 * lang.String)
	 */
	@Override
	public List<Map<String, Object>> findByGrpId(String grpId) {
		return getJdbc().queryForList("FINTBL.findByGrpId",
				new String[] { grpId });
	}

	public List<Map<String, Object>> findByGrpIdAndFingFlagBCN(String grpId) {
		return getJdbc().queryForList("FINTBL.findByGrpIdAndFingFlagBCN",
				new String[] { grpId });
	}

}
