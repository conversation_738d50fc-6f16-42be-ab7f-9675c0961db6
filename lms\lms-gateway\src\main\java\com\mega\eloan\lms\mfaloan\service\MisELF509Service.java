/* 
 *MisELF509Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import com.mega.eloan.lms.mfaloan.bean.ELF509;


/**
 * <pre>
 * 消金各項費用介面檔 MIS.ELF509
 */
public interface MisELF509Service {
	public ELF509 findByUniqueKey1(String ELF509_CASE_NO, String ELF509_FEE_ITEM, Integer ELF509_SEQ_NO);
}
