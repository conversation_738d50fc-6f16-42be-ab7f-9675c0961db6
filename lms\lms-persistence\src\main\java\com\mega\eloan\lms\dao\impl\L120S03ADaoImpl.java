/* 
 * L120S03ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S03ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S03A;

/** 資本適足率影響數資料檔 **/
@Repository
public class L120S03ADaoImpl extends LMSJpaDao<L120S03A, String> implements
		L120S03ADao {

	@Override
	public L120S03A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S03A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S03A> list = createQuery(L120S03A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120S03A findByUniqueKey(String mainId, String cntrMainId,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
				cntrMainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S03A> findByIndex01(String mainId, String cntrMainId,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L120S03A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrMainId",
					cntrMainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			search.setMaxResults(Integer.MAX_VALUE);
			list = createQuery(L120S03A.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120S03A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S03A> list = createQuery(L120S03A.class, search)
				.getResultList();

		return list;
	}

	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120S03A.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}

	@Override
	public List<L120S03A> findByCntrMainId(String[] cntrMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "cntrMainId", cntrMainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S03A> list = createQuery(L120S03A.class, search)
				.getResultList();
		return list;
	}
}