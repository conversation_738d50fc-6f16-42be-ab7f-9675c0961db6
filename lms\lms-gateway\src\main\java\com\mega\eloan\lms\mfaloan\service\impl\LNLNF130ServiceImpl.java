/* 
 *LNLNF130ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.LNF130;
import com.mega.eloan.lms.mfaloan.service.LNLNF130Service;

/**
 * <pre>
 * LN.LNF130
 * </pre>
 * 
 * @since 2012/11/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/11/7,REX,new
 *          </ul>
 */
@Service
public class LNLNF130ServiceImpl extends AbstractMFAloanJdbc implements
		LNLNF130Service {

	@Override
	public List<LNF130> findByKey(String custId, String dupNo, String cntrNo,
			String loanNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LN.LNF130_findBykey",
				new Object[] { Util.getLeftStr(cntrNo, 3),
						Util.addSpaceWithValue(custId, 10) + dupNo, cntrNo,
						loanNo });

		List<LNF130> list = new ArrayList<LNF130>();
		for (Map<String, Object> row : rowData) {
			LNF130 model = new LNF130();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<LNF130> findByCntrNo(String cntrNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"LN.LNF130.findByCntrNo", new Object[] { cntrNo });

		List<LNF130> list = new ArrayList<LNF130>();
		for (Map<String, Object> row : rowData) {
			LNF130 model = new LNF130();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
