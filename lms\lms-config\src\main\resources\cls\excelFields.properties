#=====================================================
# C160M01F\uff0e\u500b\u91d1\u52d5\u5be9\u8868\u532f\u5165\u4e3b\u6a94
#=====================================================
C160M01F.01=custId
#C160M01F.02=dupNo
C160M01F.02=custName
C160M01F.03=fFund
C160M01F.04=lnPurs
C160M01F.05=prodKind
C160M01F.06=subjCode
C160M01F.13=intWay
C160M01F.14=rIntWay
C160M01F.15=reUse
C160M01F.16=payWay
C160M01F.17=dRateAdd
C160M01F.18=dRate1
C160M01F.19=commsCust
C160M01F.20=cName
C160M01F.21=omgrNo
C160M01F.22=omgrName
C160M01F.23=frgrName
C160M01F.24=firstDate
C160M01F.25=approveTime

#=====================================================
# C160S01G\uff0e\u500b\u91d1\u52d5\u5be9\u8868\u532f\u5165\u5229\u7387\u6a94
#=====================================================
C160S01G.01=bgnNum
C160S01G.02=endNum
C160S01G.03=rateType
C160S01G.04=pmRate
C160S01G.05=rateFlag
C160S01G.06=rateChgWay
C160S01G.07=rateChgWay2

#=====================================================
# C160S01D\uff0e\u500b\u91d1\u52d5\u5be9\u8868\u532f\u5165\u660e\u7d30\u6a94
#=====================================================
C160S01D.00=staffNo
C160S01D.01=custId
C160S01D.02=custName
C160S01D.03=ntCode
C160S01D.04=LoanTotAmt
C160S01D.05=Month
C160S01D.06=lnFromDate
C160S01D.07=lnEndDate
C160S01D.08=payWay
C160S01D.09=payWayAmt
C160S01D.10=autoRct
C160S01D.11=rctDate
C160S01D.12=accNo
C160S01D.13=autoPay
C160S01D.14=atpayNo
C160S01D.15=cntrNo
C160S01D.16=rId1
C160S01D.17=rName1
C160S01D.18=rNtCode1
C160S01D.19=rKindD1
C160S01D.20=rType1
C160S01D.21=rId2
C160S01D.22=rName2
C160S01D.23=rNtCode2
C160S01D.24=rKindD2
C160S01D.25=rType2
C160S01D.26=useFromDate
C160S01D.27=useEndDate
C160S01D.28=result
#\u57282014\u5e74\u6642, XLS \u7684[\u5e74\u85aa, \u5176\u4ed6\u6240\u5f97]\u662f\u7531\u7d93\u8fa6\u8f38\u5165, \u5f8c\u4f86\u6539\u6293 C120S01B \u88e1\u7684 data
C160S01D.29=annuity
#\u8981\u4e00\u4f75\u66f4\u6539 CLS1161FormHandler
C160S01D.30=othincome