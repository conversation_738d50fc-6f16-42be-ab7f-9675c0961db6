/* 

 * L140M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;

import tw.com.iisi.cap.model.IDataObject;

/** 額度明細表主檔 **/
@NamedEntityGraph(name = "L140M01ATMP1-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l140m01a"),
		@NamedAttributeNode("l140m03a")
		})
@Entity
@Table(name = "L140M01ATMP1", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L140M01ATMP1 extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	/**
	 * JOIN條件與關聯檔By聯行額度明細表
	 *  
	 */
	/**
	 * JOIN條件 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L140M01A l140m01a;

	public L140M01A getL140m01a() {
		return l140m01a;
	}

	public void setL140m01a(L140M01A l140m01a) {
		this.l140m01a = l140m01a;
	}

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	@Column(name = "INQJCIC", length = 1, columnDefinition = "CHAR(1)")
	private String inqJcic;

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	public String getInqJcic() {
		return inqJcic;
	}

	/**
	 * 是否已查詢聯徵中心不動產鑑價資訊 <br/>
	 * 101/10/25新增For個金 Y/N
	 */
	public void setInqJcic(String inqJcic) {
		this.inqJcic = inqJcic;
	}

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "USEPAR", length = 1, columnDefinition = "DECIMAL(5,2)")
	private BigDecimal usePar;

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	public BigDecimal getUsePar() {
		return usePar;
	}

	/**
	 * 平均動用率<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setUsePar(BigDecimal usePar) {
		this.usePar = usePar;
	}

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "USEPARDATE", columnDefinition = "DATE")
	private Date useParDate;

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	public Date getUseParDate() {
		return useParDate;
	}

	/**
	 * 平均動用率資料日期<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setUseParDate(Date useParDate) {
		this.useParDate = useParDate;
	}

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	@Column(name = "HEADITEM6", length = 1, columnDefinition = "CHAR(1)")
	private String headItem6;

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	public String getHeadItem6() {
		return headItem6;
	}

	/**
	 * 是否屬於總處300億專款專用短期不循環週轉放款<br/>
	 * 101/11/23新增for國內企金
	 */
	public void setHeadItem6(String headItem6) {
		this.headItem6 = headItem6;
	}

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	@Column(name = "RESIDENTIAL", length = 1, columnDefinition = "CHAR(1)")
	private String residential;

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public String getResidential() {
		return residential;
	}

	/**
	 * 是否屬興建房屋<br/>
	 * 是-興建房屋自用 |1<br/>
	 * 是-興建房屋非自用 |2<br/>
	 * 是-暫無興建計畫之購地貸款 |3<br/>
	 * 否-非興建房屋 |N<br/>
	 * Codetype='lms140_residential'
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public void setResidential(String residential) {
		this.residential = residential;
	}

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan <br/>
	 * 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	@Column(name = "NOLOAN", length = 1, columnDefinition = "CHAR(1)")
	private String noLoan;

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan <br/>
	 * 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public String getNoLoan() {
		return noLoan;
	}

	/**
	 * 同一人/同一關係企業受限額註計項目<br/>
	 * codetype = lms140_noLoan 101/11/23新增for國內企金<br/>
	 * 非授信科目 | 0<br/>
	 * 配合政府政策，經財政部專案核准之專案授信或經中央銀行專案轉融通之授信 | 1<br/>
	 * 對政府機關之授信 | 2<br/>
	 * 以公債，國庫券，中央銀行可轉讓定期存單或本行金融債券為擔保品授信 | 3<br/>
	 * 依加強推動銀行辦理小額放款業務要點辦理之新台幣一百萬元以下授信 | 4<br/>
	 * 配合政府專案不需列入同一人限額計算，但需列入同一關係企業限額中 | 5<br/>
	 * 
	 * 
	 * ※需檢查額度性質為無擔時要維護此欄位，且借款人銀行法或利害人關係為Y。再動審表時要上傳。此欄位位於「額度種類欄」
	 */
	public void setNoLoan(String noLoan) {
		this.noLoan = noLoan;
	}

	/**
	 * 來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生<br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)
	 */
	@Column(name = "DATASRC", length = 1, columnDefinition = "CHAR(1)")
	private String dataSrc;

	/**
	 * 來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 */
	@Column(name = "MAINIDSRC", length = 32, columnDefinition = "CHAR(32)")
	private String mainIdSrc;

	/** 列印順序 **/
	@Column(name = "PRINTSEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer printSeq;

	/**
	 * 案件號碼
	 * <p/>
	 * 100/11/24調整
	 */
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/** 簽案日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "CASEDATE", columnDefinition = "DATE")
	private Date caseDate;

	/**
	 * 案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 */
	@Column(name = "GRANT", length = 1, columnDefinition = "CHAR(1)")
	private String grant;

	/** 分行放行時間 **/
	@Column(name = "BRDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp brdTime;

	/** 營運中心放行時間 **/
	@Column(name = "AREASENDINFO", columnDefinition = "TIMESTAMP")
	private Timestamp areaSendInfo;

	/**
	 * 婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 */
	@Column(name = "CESRJTCAUSE", length = 2, columnDefinition = "VARCHAR(2)")
	private String cesRjtCause;

	/** 婉卻原因 **/
	@Column(name = "CESRJTREASON", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String cesRjtReason;

	/** 本額度風險歸屬國別 **/
	@Column(name = "RISKAREA", length = 2, columnDefinition = "CHAR(2)")
	private String riskArea;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Column(name = "HEADITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1;

	/** 保證成數 **/
	@Digits(integer = 3, fraction = 2, groups = Check.class)
	@Column(name = "GUTPERCENT", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal gutPercent;

	/** 信保首次動用有效期限 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "GUTCUTDATE", columnDefinition = "DATE")
	private Date gutCutDate;

	/**
	 * 借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 */
	@Column(name = "HEADITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String headItem2;

	/**
	 * 本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "SYNDIPFD", length = 1, columnDefinition = "CHAR(1)")
	private String syndIPFD;

	/**
	 * 是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	@Column(name = "HEADITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3;

	/**
	 * 送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 */
	@Column(name = "HEADITEM3TTL", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3Ttl;

	/**
	 * 擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 */
	@Column(name = "HEADITEM5", length = 1, columnDefinition = "CHAR(1)")
	private String headItem5;

	/**
	 * 擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 */
	@Column(name = "MRATETYPE", length = 1, columnDefinition = "CHAR(1)")
	private String mRateType;

	/**
	 * 擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 */
	@Column(name = "MRATE", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal mRate;

	/**
	 * 性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String proPerty;

	/**
	 * 額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	@Column(name = "SNOKIND", length = 2, columnDefinition = "CHAR(2)")
	private String snoKind;

	/** 共用額度序號 **/
	@Column(name = "COMMSNO", length = 12, columnDefinition = "CHAR(12)")
	private String commSno;

	/** 額度序號 **/
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	@Column(name = "SBJPROPERTY", length = 1, columnDefinition = "CHAR(1)")
	private String sbjProperty;

	/** 原始額度序號 **/
	@Column(name = "SNOOLD", length = 12, columnDefinition = "CHAR(12)")
	private String snoOld;

	/** 原始控管種類 **/
	@Column(name = "SNOKINDOLD", length = 2, columnDefinition = "CHAR(2)")
	private String snoKindOld;

	/**
	 * 本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 */
	@Column(name = "UNITCASE2", length = 1, columnDefinition = "CHAR(1)")
	private String unitCase2;

	/**
	 * 加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 */
	@Column(name = "HEADITEM4", length = 1, columnDefinition = "CHAR(1)")
	private String headItem4;

	/**
	 * 授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 */
	@Column(name = "GIST", length = 300, columnDefinition = "VARCHAR(300)")
	private String gist;

	/**
	 * 授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字) <br/>
	 * 102.02.18 欄位擴大 300 -> 1536
	 */
	@Column(name = "LNSUBJECT", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String lnSubject;

	/**
	 * 清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字) <br/>
	 * 102.02.18 欄位擴大 200 -> 600
	 */
	@Column(name = "PAYDEADLINE", length = 600, columnDefinition = "VARCHAR(600)")
	private String payDeadline;

	/**
	 * 衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 */
	@Column(name = "DERIVATIVES", length = 100, columnDefinition = "VARCHAR(100)")
	private String derivatives;

	/**
	 * 是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	@Column(name = "ISDERIVATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String isDerivatives;

	/**
	 * 風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 */
	@Digits(integer = 5, fraction = 4, groups = Check.class)
	@Column(name = "DERIVATIVESNUM", columnDefinition = "DECIMAL(9,4)")
	private BigDecimal derivativesNum;

	/** 前准額度－同前頁 **/
	@Column(name = "LASTVALUEREFP1", length = 1, columnDefinition = "CHAR(1)")
	private String lastValueRefP1;

	/** 前准額度－幣別 **/
	@Column(name = "LVCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVCurr;

	/**
	 * 前准額度－金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LVAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAmt;

	/**
	 * 前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LV2CURR", length = 3, columnDefinition = "CHAR(3)")
	private String LV2Curr;

	/**
	 * 前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LV2AMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LV2Amt;

	/**
	 * 前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	@Column(name = "LVASSURECURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVAssureCurr;

	/**
	 * 前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVASSUREAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAssureAmt;

	/** 餘額－幣別 **/
	@Column(name = "BLCURR", length = 3, columnDefinition = "CHAR(3)")
	private String BLCurr;

	/**
	 * 餘額－金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "BLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal BLAmt;

	/**
	 * 餘額－備註
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 60 -> 600
	 */
	@Column(name = "BLMEMO", length = 600, columnDefinition = "VARCHAR(600)")
	private String BLMemo;

	/** 購料放款案下已開狀未到單金額－幣別 **/
	@Column(name = "LCCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LCCurr;

	/** 購料放款案下已開狀未到單金額－金額 **/
	@Column(name = "LCAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LCAmt;

	/** 現請額度－同前頁 **/
	@Column(name = "CURRENTAPPREF", length = 1, columnDefinition = "CHAR(1)")
	private String CurrentAppRef;

	/** 現請額度－幣別 **/
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/**
	 * 現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/**
	 * 現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	@Column(name = "REUSE", length = 1, columnDefinition = "CHAR(1)")
	private String reUse;

	/**
	 * 現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 */
	@Column(name = "OTHERCURR", length = 1, columnDefinition = "CHAR(1)")
	private String otherCurr;

	/**
	 * 聯貸說明
	 * <p/>
	 * 101/03/17調整
	 */
	@Column(name = "COUNTSAY", length = 1024, columnDefinition = "VARCHAR(1024)")
	private String countSay;

	/** 借款收付彙計數－同前頁 **/
	@Column(name = "COLLECTPAYREF", length = 1, columnDefinition = "CHAR(1)")
	private String collectPayRef;

	/** 借款收付彙計數（付）－幣別 **/
	@Column(name = "CPCURR", length = 3, columnDefinition = "CHAR(3)")
	private String CPCurr;

	/**
	 * 動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	@Column(name = "USEDEADLINE", length = 1, columnDefinition = "CHAR(1)")
	private String useDeadline;

	/**
	 * 動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整 <br/>
	 * 102.02.18 欄位擴大 128-> 900
	 */
	@Column(name = "DESP1", length = 900, columnDefinition = "VARCHAR(900)")
	private String desp1;

	/** 風險權數－借款戶 **/
	@Column(name = "ITEMC", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal itemC;

	/** 風險權數－抵減後 **/
	@Column(name = "RITEMD", columnDefinition = "DECIMAL(15,2)")
	private BigDecimal rItemD;

	/** 資金成本率 **/
	@Column(name = "ITEMG", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal itemG;

	/** 占資本適足率 **/
	@Column(name = "ITEMF", columnDefinition = "DECIMAL(8,5)")
	private BigDecimal itemF;

	/**
	 * 分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 */
	@Column(name = "NPL", length = 300, columnDefinition = "VARCHAR(300)")
	private String npl;

	/** 分行逾放比更新日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "NPLDATE", columnDefinition = "DATE")
	private Date npldate;

	/** 本票－同前頁 **/
	@Column(name = "CHECKNOTEREF", length = 1, columnDefinition = "CHAR(1)")
	private String checkNoteRef;

	/**
	 * 本票 <br/>
	 * 102.02.18 欄位擴大 100 -> 1050
	 * **/
	@Column(name = "CHECKNOTE", length = 1050, columnDefinition = "VARCHAR(1050)")
	private String checkNote;

	/**
	 * 額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 */
	@Column(name = "CHECKQNOTE", length = 5, columnDefinition = "VARCHAR(5)")
	private String checkQNote;

	/**
	 * 連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 */
	@Column(name = "GUARANTORTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorType;

	/**
	 * 連保人
	 * <p/>
	 * 128個全型字 <br/>
	 * 102.02.18 欄位擴大 384 -> 1800 個金:用來儲存舊案連保人訊息
	 */
	@Column(name = "GUARANTOR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String guarantor;

	/**
	 * 連保人備註
	 * <p/>
	 * 64個全型字 <br/>
	 * 102.02.18 欄位擴大 192 -> 900
	 */
	@Column(name = "GUARANTORMEMO", length = 900, columnDefinition = "VARCHAR(900)")
	private String guarantorMemo;

	/** 連保人－同前頁 **/
	@Column(name = "GUARANTORREF", length = 1, columnDefinition = "CHAR(1)")
	private String guarantorRef;

	/**
	 * 物上保證人 <br/>
	 * 102.02.18 欄位擴大 100 -> 900
	 * **/
	@Column(name = "GUARANTOR1", length = 900, columnDefinition = "VARCHAR(900)")
	private String guarantor1;

	/**
	 * 本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 */
	@Column(name = "NOINSUREASON", length = 1, columnDefinition = "CHAR(1)")
	private String noInsuReason;

	/**
	 * 本案未送保原因(其他) <br/>
	 * 102.02.18 欄位擴大 100 -> 900
	 **/
	@Column(name = "NOINSUREASONOTHER", length = 900, columnDefinition = "VARCHAR(900)")
	private String noInsuReasonOther;

	/** 擔保授信額度調整幣別 **/
	@Column(name = "ASSURETOTECURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotECurr;

	/**
	 * 擔保授信額度調整金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "ASSURETOTEAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotEAmt;

	/**
	 * 合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	@Column(name = "VALUETUNE", length = 1, columnDefinition = "CHAR(1)")
	private String valueTune;

	/**
	 * 前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LVTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVTotCurr;

	/**
	 * 前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVTotAmt;

	/**
	 * 前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "LVASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LVAssTotCurr;

	/**
	 * 前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "LVASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LVAssTotAmt;

	/**
	 * 新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "INCAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incApplyTotCurr;

	/**
	 * 新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "INCAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incApplyTotAmt;

	/**
	 * 新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "INCASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incAssTotCurr;

	/**
	 * 新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "INCASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incAssTotAmt;

	/**
	 * 取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String decApplyTotCurr;

	/**
	 * 取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal decApplyTotAmt;

	/**
	 * 取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "DECASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String decAssTotCurr;

	/**
	 * 取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "DECASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal decAssTotAmt;

	/** 授信額度合計幣別 **/
	@Column(name = "LOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotCurr;

	/**
	 * 授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotAmt;

	/** 擔保授信額度合計幣別 **/
	@Column(name = "ASSURETOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotCurr;

	/**
	 * 擔保授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "ASSURETOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotAmt;

	/**
	 * 擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	@Column(name = "ESTTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String estTotCurr;

	/**
	 * 擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 */
	@Column(name = "ESTTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal estTotAmt;

	/** 衍生性金融商品合計幣別 **/
	@Column(name = "LOANTOTZCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotZCurr;

	/**
	 * 衍生性金融商品合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 **/
	@Column(name = "LOANTOTZAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotZAmt;

	/** 衍生性金融商品相當授信額度合計幣別 **/
	@Column(name = "LOANTOTLCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotLCurr;

	/**
	 * 衍生性金融商品相當授信額度合計金額 <br/>
	 * 102.02.18 欄位擴大 DECIMAL(15,0) -> DECIMAL(17,2)
	 * **/
	@Column(name = "LOANTOTLAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotLAmt;

	/**
	 * 匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "RATEDT", columnDefinition = "DATE")
	private Date rateDT;

	/**
	 * 結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Column(name = "RATELOANTOLOC", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateLoanToLoc;

	/**
	 * 結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	@Column(name = "RATELOCTOSUM", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateLocToSum;

	/**
	 * 結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 */
	@Column(name = "RATECROSSOVER", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateCrossover;

	/**
	 * 折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 */
	@Column(name = "RATECONVERT", columnDefinition = "DECIMAL(12,8)")
	private BigDecimal rateConvert;

	/**
	 * 備註 <br/>
	 * 102.02.18 欄位擴大 300 -> 1536
	 **/
	@Column(name = "RMK", length = 1536, columnDefinition = "VARCHAR(1536)")
	private String Rmk;

	/**
	 * 各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	@Column(name = "MULTIAMTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String multiAmtFlag;

	/** 各幣別授信額度合計 **/
	@Column(name = "MULTIAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String multiAmt;

	/**
	 * 各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 */
	@Column(name = "MULTIASSUREAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String multiAssureAmt;

	/**
	 * 輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "CHKYN", length = 1, columnDefinition = "CHAR(1)")
	private String chkYN;

	/**
	 * 是否有衍生性商品科目(Y/N)
	 * <p/>
	 * 101/09/13調整<br/>
	 * Y：有(要顯示衍生性金融商品合計與約當授信合計，列印亦須顯示)<br/>
	 * N：無(若isDerivatives = "Y"而hasDerivatives = "N"
	 * 則為保證或進出口科目，只要顯示風險係數而不顯示衍生性合計 )<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	@Column(name = "HASDERIVATIVES", length = 1, columnDefinition = "CHAR(1)")
	private String hasDerivatives;

	/**
	 * 共同借款人組成文字 <BR/>
	 * 消金團貸額度明細表借用此欄位，來存放 '另案產品科目' <BR/>
	 * 因團貸母戶限輸入1個產品, 當需要有2個科目來描述時, 即可用此欄位<BR/>
	 * 例如：成數75%以內要建有擔科目, 超過75%的要建無擔科目, 即可在 '另案產品科目' 來描述
	 * **/
	@Column(name = "L140M01JSTR", length = 1800, columnDefinition = "VARCHAR(1800)")
	private String l140m01jStr;

	/**
	 * NOTES 轉檔版本
	 */
	@Column(name = "NOTESUP", columnDefinition = "VARCHAR(6)")
	private String notesUp;

	/**
	 * 按轉讓發票金額%動用
	 * <p/>
	 * 102/08/17新增<br/>
	 */
	@Column(name = "LOANPER", columnDefinition = "DECIMAL(3,0)")
	private BigDecimal loanPer;

	/**
	 * 可否借新還舊
	 */
	@Column(name = "BYNEWOLD", columnDefinition = "CHAR(1)")
	private String byNewOld;

	/**
	 * 本案是否為信保續約案
	 */
	@Column(name = "ISGUAOLDCASE", columnDefinition = "CHAR(1)")
	private String isGuaOldCase;

	/**
	 * 產品種類
	 */
	@Column(name = "LNTYPE", columnDefinition = "CHAR(2)")
	private String lnType;

	/**
	 * 本行帳務是否有聯貸拆帳作業
	 */
	@Column(name = "HASLOANASSIGN", columnDefinition = "CHAR(1)")
	private String hasLoanAssign;

	/**
	 * 青年創業及啟動金貸款 申請日期
	 */
	@Column(name = "APPLYDATE", columnDefinition = "DATE")
	private Date applyDate;

	/** 各幣別授信額度合計 **/
	@Column(name = "DERVMULTIAMT", length = 300, columnDefinition = "VARCHAR(300)")
	private String dervMultiAmt;

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * <p/>
	 * 103/03/24新增<br/>
	 * Y/N
	 */
	@Column(name = "NEWDERVMULTIAMTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String newDervMultiAmtFlag;

	/**
	 * 衍生性係數複選組合字串
	 */
	@Column(name = "DERIVATIVESNUMDSCR", columnDefinition = "VARCHAR(300)")
	private String derivativesNumDscr;

	/**
	 * 額度評等
	 * <p/>
	 * 103/06/09調整<br/>
	 * 300
	 */
	@Column(name = "FACILITYRATING", length = 300, columnDefinition = "VARCHAR(300)")
	private String facilityRating;

	/** 現請擔保額度－幣別 **/
	@Column(name = "ASSUREAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureApplyCurr;

	/** 現請擔保額度－金額 **/
	@Column(name = "ASSUREAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureApplyAmt;

	/**
	 * 列印明細 1:現請金額/2:信評
	 * **/
	@Column(name = "PRINTDETAILS", length = 20, columnDefinition = "VARCHAR(20)")
	private String printDetails;

	/**
	 * 保證人是否按一定比率負担保證責任 Y/N
	 */
	@Column(name = "GUAPERCENTFG", length = 1, columnDefinition = "CHAR(1)")
	private String guaPercentFg;

	/**
	 * 簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	@Column(name = "CNTRCHGOID", columnDefinition = "VARCHAR(32)")
	private String cntrChgOid;

	/**
	 * 擔保品是否為房屋
	 */
	@Column(name = "HEADITEM7", length = 1, columnDefinition = "CHAR(1)")
	private String headItem7;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 本案是否屬銀行法72-2條控管對象
	 */
	@Column(name = "IS722FLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722Flag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y ,
	 * N , A:兩種都有}
	 */
	@Column(name = "IS722ONFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String is722OnFlag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 查詢銀行法72-2條控管對象額度序號
	 */
	@Column(name = "IS722CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String is722CntrNo;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 查詢銀行法72-2條控管對象日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "IS722QDATE", columnDefinition = "DATE")
	private Date is722QDate;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 額度序號檢核是否存在帳務擋註記
	 */
	@Column(name = "CNTRNOCHKEXISTFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String cntrNoChkExistFlag;

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 額度序號檢核是否存在帳務擋日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CNTRNOCHKEXISTDATE", columnDefinition = "DATE")
	private Date cntrNoChkExistDate;

	@Column(name = "ISBUY", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuy;
	@Column(name = "EXITEM", length = 3, columnDefinition = "VARCHAR(3)")
	private String exItem;
	@Column(name = "ISBUYON", length = 1, columnDefinition = "VARCHAR(1)")
	private String isBuyOn;
	@Column(name = "EXITEMON", length = 60, columnDefinition = "VARCHAR(60)")
	private String exItemOn;

	/**
	 * 利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	@Column(name = "UNSECUREFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String unsecureFlag;

	/**
	 * 備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	@Column(name = "NOLOANBK", length = 1, columnDefinition = "CHAR(1)")
	private String noLoanBk;

	/**
	 * 額度明細表檢核供應鏈融資賣放限週轉科目 J-104-0284-001 額度明細表新增是否收取帳務管理費
	 */
	@Column(name = "ISEFIN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isEfin;

	/**
	 * 是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	@Column(name = "HASAMFEE", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasAmFee;

	/**
	 * 是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	@Column(name = "ISNONSMEPROJLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String isNonSMEProjLoan;

	/**
	 * J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 * 
	 * **/
	@Column(name = "NONSMEPROJLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal nonSMEProjLoanAmt;

	/**
	 * 本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	@Column(name = "HASRATELIMIT", length = 1, columnDefinition = "VARCHAR(1)")
	private String hasRateLimit;

	/**
	 * 是否為振興經濟非中小企業專案貸款(動審表變更後) J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	@Column(name = "CISNONSMEPROJLOAN", length = 1, columnDefinition = "VARCHAR(1)")
	private String cIsNonSMEProjLoan;

	/**
	 * 振興經濟非中小企業專案貸款金額(動審表變更後) J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 * 
	 * **/
	@Column(name = "CNONSMEPROJLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal cNonSMEProjLoanAmt;

	/**
	 * 振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 * 
	 * **/
	@Column(name = "CNONSMEPROJMAINID", columnDefinition = "VARCHAR(32)")
	private String cNonSMEProjMainId;

	/**
	 * 衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 * 
	 * **/
	@Column(name = "DERIVATIVEVERSION", columnDefinition = "VARCHAR(8)")
	private String derivativeVersion;

	/**
	 * 約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	@Column(name = "EXCEPTFLAG", length = 1, columnDefinition = "VARCHAR(1)")
	private String exceptFlag;

	/**
	 * 產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	@Column(name = "ITWCODE", length = 2, columnDefinition = "VARCHAR(2)")
	private String itwCode;

	/**
	 * 借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	@Column(name = "ISSTARTUP", length = 1, columnDefinition = "VARCHAR(1)")
	private String isStartUp;

	/** 物上保證人－同前頁 **/
	@Column(name = "GUARANTOR1REF", length = 1, columnDefinition = "CHAR(1)")
	private String guarantor1Ref;
	
	
	//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 是否屬未核配額度國家 **/
	@Column(name="ISNOFACTCOUNTRY", length=1, columnDefinition="VARCHAR(1)")
	private String isNoFactCountry;
	
	//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 未核配額度國家 **/
	@Column(name="NOFACTCOUNTRY", length=60, columnDefinition="VARCHAR(60)")
	private String noFactCountry;
	
	//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 是否屬凍結額度國家 **/
	@Column(name="ISFREEZEFACTCOUNTRY", length=1, columnDefinition="VARCHAR(1)")
	private String isFreezeFactCountry;
	
	//J-106-0085-001  Web e-Loan企金授信新增主要還款來源國等相關欄位
	/** 凍結額度國家 **/
	@Column(name="FREEZEFACTCOUNTRY", length=60, columnDefinition="VARCHAR(60)")
	private String freezeFactCountry;
	
	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 */
	@Column(name = "INSMEFG", length = 1, columnDefinition = "VARCHAR(1)")
	private String inSmeFg;

	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMETOAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeToAmt;
	
	/**
	 * J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
	 * 
	 * **/
	@Column(name = "INSMECAAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal inSmeCaAmt;
	

	/**
	 * 取得共同借款人組成文字
	 * 
	 * @return
	 */
	public String getL140m01jStr() {
		return l140m01jStr;
	}

	/**
	 * 設定共同借款人組成文字
	 * 
	 * @return
	 */
	public void setL140m01jStr(String l140m01jStr) {
		this.l140m01jStr = l140m01jStr;
	}

	/**
	 * 取得來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生 <br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)
	 */
	public String getDataSrc() {
		return this.dataSrc;
	}

	/**
	 * 設定來源註記
	 * <p/>
	 * 100/12/06新增<br/>
	 * 0.案件產生(for國內個金舊案)<br/>
	 * 1.新增額度明細表(空白)<br/>
	 * 2.複製額度明細表<br/>
	 * 3.轉入額度明細表<br/>
	 * 4.續約/條件變更產生 <br/>
	 * 5.帳務 (由帳務系統資訊轉入之資料LN.LNF) <br/>
	 * 6.核准 (由R6轉入之資料) <br/>
	 * 7.動用 (由mis.Elf500轉入之資料)
	 **/
	public void setDataSrc(String value) {
		this.dataSrc = value;
	}

	/**
	 * 取得來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 */
	public String getMainIdSrc() {
		return this.mainIdSrc;
	}

	/**
	 * 設定來源文件編號
	 * <p/>
	 * 101/07/17新增<br/>
	 * for「續約/條件變更」<br/>
	 * (為取得變更前資料用)
	 **/
	public void setMainIdSrc(String value) {
		this.mainIdSrc = value;
	}

	/** 取得列印順序 **/
	public Integer getPrintSeq() {
		return this.printSeq;
	}

	/** 設定列印順序 **/
	public void setPrintSeq(Integer value) {
		this.printSeq = value;
	}

	/**
	 * 取得案件號碼
	 * <p/>
	 * 100/11/24調整
	 */
	public String getCaseNo() {
		return this.caseNo;
	}

	/**
	 * 設定案件號碼
	 * <p/>
	 * 100/11/24調整
	 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/** 取得簽案日期 **/
	public Date getCaseDate() {
		return this.caseDate;
	}

	/** 設定簽案日期 **/
	public void setCaseDate(Date value) {
		this.caseDate = value;
	}

	/**
	 * 取得案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 */
	public String getGrant() {
		return this.grant;
	}

	/**
	 * 設定案件授權等級
	 * <p/>
	 * 1.分行授權內<br/>
	 * 2.營運中心授權內<br/>
	 * 3.分行授權外<br/>
	 * 4.營運中心授權外
	 **/
	public void setGrant(String value) {
		this.grant = value;
	}

	/** 取得分行放行時間 **/
	public Timestamp getBrdTime() {
		return this.brdTime;
	}

	/** 設定分行放行時間 **/
	public void setBrdTime(Timestamp value) {
		this.brdTime = value;
	}

	/** 取得營運中心放行時間 **/
	public Timestamp getAreaSendInfo() {
		return this.areaSendInfo;
	}

	/** 設定營運中心放行時間 **/
	public void setAreaSendInfo(Timestamp value) {
		this.areaSendInfo = value;
	}

	/**
	 * 取得婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 */
	public String getCesRjtCause() {
		return this.cesRjtCause;
	}

	/**
	 * 設定婉卻代碼
	 * <p/>
	 * 101/02/22調整
	 **/
	public void setCesRjtCause(String value) {
		this.cesRjtCause = value;
	}

	/** 取得婉卻原因 **/
	public String getCesRjtReason() {
		return this.cesRjtReason;
	}

	/** 設定婉卻原因 **/
	public void setCesRjtReason(String value) {
		this.cesRjtReason = value;
	}

	/** 取得本額度風險歸屬國別 **/
	public String getRiskArea() {
		return this.riskArea;
	}

	/** 設定本額度風險歸屬國別 **/
	public void setRiskArea(String value) {
		this.riskArea = value;
	}

	/**
	 * 取得本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	public String getHeadItem1() {
		return this.headItem1;
	}

	/**
	 * 設定本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 **/
	public void setHeadItem1(String value) {
		this.headItem1 = value;
	}

	/** 取得保證成數 **/
	public BigDecimal getGutPercent() {
		return this.gutPercent;
	}

	/** 設定保證成數 **/
	public void setGutPercent(BigDecimal value) {
		this.gutPercent = value;
	}

	/** 取得信保首次動用有效期限 **/
	public Date getGutCutDate() {
		return this.gutCutDate;
	}

	/** 設定信保首次動用有效期限 **/
	public void setGutCutDate(Date value) {
		this.gutCutDate = value;
	}

	/**
	 * 取得借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 */
	public String getHeadItem2() {
		return this.headItem2;
	}

	/**
	 * 設定借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）<br/>
	 * [個金]利率列印位置<br/>
	 * 0|印於主表<br/>
	 * 1|印於附表(一)<br/>
	 **/
	public void setHeadItem2(String value) {
		this.headItem2 = value;
	}

	/**
	 * 取得本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getSyndIPFD() {
		return this.syndIPFD;
	}

	/**
	 * 設定本額度是否為聯貸信保
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setSyndIPFD(String value) {
		this.syndIPFD = value;
	}

	/**
	 * 取得是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getHeadItem3() {
		return this.headItem3;
	}

	/**
	 * 設定是否符合送中小信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setHeadItem3(String value) {
		this.headItem3 = value;
	}

	/**
	 * 取得送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 */
	public String getHeadItem3Ttl() {
		return this.headItem3Ttl;
	}

	/**
	 * 設定送保種類
	 * <p/>
	 * 101/01/13因應泰行需求新增<br/>
	 * 1.中小信保基金保證<br/>
	 * 2.海外信保基金保證<br/>
	 * ※國內交易預設1，海外預設2
	 **/
	public void setHeadItem3Ttl(String value) {
		this.headItem3Ttl = value;
	}

	/**
	 * 取得擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 */
	public String getHeadItem5() {
		return this.headItem5;
	}

	/**
	 * 設定擔保品是否為股票/開放型基金/受益憑證
	 * <p/>
	 * 101/02/23新增<br/>
	 * Y/N
	 **/
	public void setHeadItem5(String value) {
		this.headItem5 = value;
	}

	/**
	 * 取得擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 */
	public String getMRateType() {
		return this.mRateType;
	}

	/**
	 * 設定擔保維持率採用類型
	 * <p/>
	 * 101/02/23新增<br/>
	 * 0.標準<br/>
	 * 1.自訂
	 **/
	public void setMRateType(String value) {
		this.mRateType = value;
	}

	/**
	 * 取得擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 */
	public BigDecimal getMRate() {
		return this.mRate;
	}

	/**
	 * 設定擔保維持率
	 * <p/>
	 * 101/02/23新增<br/>
	 * ※(標準擔保維持率為140%)
	 **/
	public void setMRate(BigDecimal value) {
		this.mRate = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 */
	public String getProPerty() {
		return this.proPerty;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 100/10/03 新增11、12、13<br/>
	 * 100/12/05調整成notes代碼<br/>
	 * 100/12/20調整欄位長度150(30<br/>
	 * 複選：<br/>
	 * 1報價(報價|13<br/>
	 * 2新作(新做|1<br/>
	 * 3增額(增額|5<br/>
	 * 4紓困(紓困|10<br/>
	 * 5協議清償(協議清償|12<br/>
	 * 6減額(減額|6<br/>
	 * 7變更條件(變更條件|3<br/>
	 * 8續約(續約|2<br/>
	 * 9提前續約(提前續約|11<br/>
	 * 10展期（不良授信案）(展期(不良授信案)|9<br/>
	 * 11流用(流用|4<br/>
	 * 12取消(取消|8<br/>
	 * 13不變(不變|7
	 **/
	public void setProPerty(String value) {
		this.proPerty = value;
	}

	/**
	 * 取得額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 */
	public String getSnoKind() {
		return this.snoKind;
	}

	/**
	 * 設定額度控管種類
	 * <p/>
	 * 10.一般<br/>
	 * 20.信保<br/>
	 * 30.聯貸<br/>
	 * 40.合作母<br/>
	 * 41.合作子<br/>
	 * 51.個人戶一般
	 **/
	public void setSnoKind(String value) {
		this.snoKind = value;
	}

	/** 取得共用額度序號 **/
	public String getCommSno() {
		return this.commSno;
	}

	/** 設定共用額度序號 **/
	public void setCommSno(String value) {
		this.commSno = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}

	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		if (value != null) {
			// 一率轉大寫額度序號
			value = value.toUpperCase();
		}
		this.cntrNo = value;
	}

	/**
	 * 取得額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	public String getSbjProperty() {
		return this.sbjProperty;
	}

	/**
	 * 設定額度性質
	 * <p/>
	 * 1.擔保<br/>
	 * 2.無擔保/信保<br/>
	 * 100/12/27 修改為代碼 上傳時要用<br/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 **/
	public void setSbjProperty(String value) {
		this.sbjProperty = value;
	}

	/** 取得原始額度序號 **/
	public String getSnoOld() {
		return this.snoOld;
	}

	/** 設定原始額度序號 **/
	public void setSnoOld(String value) {
		this.snoOld = value;
	}

	/** 取得原始控管種類 **/
	public String getSnoKindOld() {
		return this.snoKindOld;
	}

	/** 設定原始控管種類 **/
	public void setSnoKindOld(String value) {
		this.snoKindOld = value;
	}

	/**
	 * 取得本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 */
	public String getUnitCase2() {
		return this.unitCase2;
	}

	/**
	 * 設定本案是否有同業聯貸案額度
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/03 額度種類選擇本案是否有同業聯貸案額度為是則每份額度明細表都要提供此選項
	 **/
	public void setUnitCase2(String value) {
		this.unitCase2 = value;
	}

	/**
	 * 取得加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 */
	public String getHeadItem4() {
		return this.headItem4;
	}

	/**
	 * 設定加計該額度是否逾越限額
	 * <p/>
	 * Y/N（有/無）<br/>
	 * 100/10/13 新增
	 **/
	public void setHeadItem4(String value) {
		this.headItem4 = value;
	}

	/**
	 * 取得授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 */
	public String getGist() {
		return this.gist;
	}

	/**
	 * 設定授信科目性質與額度
	 * <p/>
	 * 100個全型字<br/>
	 * (組合文字)供簽報書案由引進用
	 **/
	public void setGist(String value) {
		this.gist = value;
	}

	/**
	 * 取得授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字)
	 */
	public String getLnSubject() {
		return this.lnSubject;
	}

	/**
	 * 設定授信科目
	 * <p/>
	 * 101/04/12調整<br/>
	 * 200 ( 300<br/>
	 * 100個全型字(組合文字)
	 **/
	public void setLnSubject(String value) {
		this.lnSubject = value;
	}

	/**
	 * 取得清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字)
	 */
	public String getPayDeadline() {
		return this.payDeadline;
	}

	/**
	 * 設定清償期限
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100 ( 200<br/>
	 * (組合文字)
	 **/
	public void setPayDeadline(String value) {
		this.payDeadline = value;
	}

	/**
	 * 取得衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 */
	public String getDerivatives() {
		return this.derivatives;
	}

	/**
	 * 設定衍生性金融商品期數
	 * <p/>
	 * 1.六個月期（含）以內<br/>
	 * 2.六至十二個月期（含）以內<br/>
	 * 3.二年期（含）以內<br/>
	 * 4.三年期（含）以內<br/>
	 * 5.四年期（含）以內<br/>
	 * 6.五年期（含）以內<br/>
	 * 7.五年期以上
	 **/
	public void setDerivatives(String value) {
		this.derivatives = value;
	}

	/**
	 * 取得是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 */
	public String getIsDerivatives() {
		return this.isDerivatives;
	}

	/**
	 * 設定是否為衍生性金融商品
	 * <p/>
	 * Y/N
	 **/
	public void setIsDerivatives(String value) {
		this.isDerivatives = value;
	}

	/**
	 * 取得風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 */
	public BigDecimal getDerivativesNum() {
		return this.derivativesNum;
	}

	/**
	 * 設定風險係數（%）
	 * <p/>
	 * 101/02/23調整
	 **/
	public void setDerivativesNum(BigDecimal value) {
		this.derivativesNum = value;
	}

	/** 取得前准額度－同前頁 **/
	public String getLastValueRefP1() {
		return this.lastValueRefP1;
	}

	/** 設定前准額度－同前頁 **/
	public void setLastValueRefP1(String value) {
		this.lastValueRefP1 = value;
	}

	/** 取得前准額度－幣別 **/
	public String getLVCurr() {
		return this.LVCurr;
	}

	/** 設定前准額度－幣別 **/
	public void setLVCurr(String value) {
		this.LVCurr = value;
	}

	/** 取得前准額度－金額 **/
	public BigDecimal getLVAmt() {
		return this.LVAmt;
	}

	/** 設定前准額度－金額 **/
	public void setLVAmt(BigDecimal value) {
		this.LVAmt = value;
	}

	/**
	 * 取得前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLV2Curr() {
		return this.LV2Curr;
	}

	/**
	 * 設定前准批覆授信額度－幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLV2Curr(String value) {
		this.LV2Curr = value;
	}

	/**
	 * 取得前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getLV2Amt() {
		return this.LV2Amt;
	}

	/**
	 * 設定前准批覆授信額度－金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLV2Amt(BigDecimal value) {
		this.LV2Amt = value;
	}

	/**
	 * 取得前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	public String getLVAssureCurr() {
		return this.LVAssureCurr;
	}

	/**
	 * 設定前准批覆擔保授信額度－幣別
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 **/
	public void setLVAssureCurr(String value) {
		this.LVAssureCurr = value;
	}

	/**
	 * 取得前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 */
	public BigDecimal getLVAssureAmt() {
		return this.LVAssureAmt;
	}

	/**
	 * 設定前准批覆擔保授信額度－金額
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * ※自行輸入
	 **/
	public void setLVAssureAmt(BigDecimal value) {
		this.LVAssureAmt = value;
	}

	/** 取得餘額－幣別 **/
	public String getBLCurr() {
		return this.BLCurr;
	}

	/** 設定餘額－幣別 **/
	public void setBLCurr(String value) {
		this.BLCurr = value;
	}

	/** 取得餘額－金額 **/
	public BigDecimal getBLAmt() {
		return this.BLAmt;
	}

	/** 設定餘額－金額 **/
	public void setBLAmt(BigDecimal value) {
		this.BLAmt = value;
	}

	/**
	 * 取得餘額－備註
	 * <p/>
	 * 101/04/12新增
	 */
	public String getBLMemo() {
		return this.BLMemo;
	}

	/**
	 * 設定餘額－備註
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setBLMemo(String value) {
		this.BLMemo = value;
	}

	/** 取得購料放款案下已開狀未到單金額－幣別 **/
	public String getLCCurr() {
		return this.LCCurr;
	}

	/** 設定購料放款案下已開狀未到單金額－幣別 **/
	public void setLCCurr(String value) {
		this.LCCurr = value;
	}

	/** 取得購料放款案下已開狀未到單金額－金額 **/
	public BigDecimal getLCAmt() {
		return this.LCAmt;
	}

	/** 設定購料放款案下已開狀未到單金額－金額 **/
	public void setLCAmt(BigDecimal value) {
		this.LCAmt = value;
	}

	/** 取得現請額度－同前頁 **/
	public String getCurrentAppRef() {
		return this.CurrentAppRef;
	}

	/** 設定現請額度－同前頁 **/
	public void setCurrentAppRef(String value) {
		this.CurrentAppRef = value;
	}

	/** 取得現請額度－幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/** 設定現請額度－幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/**
	 * 取得現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整
	 */
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/**
	 * 設定現請額度－金額
	 * <p/>
	 * 100/12/05配合上傳DB調整
	 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/**
	 * 取得現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 */
	public String getReUse() {
		return this.reUse;
	}

	/**
	 * 設定現請額度－是否循環使用
	 * <p/>
	 * 1.不循環使用<br/>
	 * 2.循環使用
	 **/
	public void setReUse(String value) {
		this.reUse = value;
	}

	/**
	 * 取得現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 */
	public String getOtherCurr() {
		return this.otherCurr;
	}

	/**
	 * 設定現請額度－動用幣別
	 * <p/>
	 * 1.或等值其他貨幣<br/>
	 * 2.或等值其他外幣<br/>
	 * 3.之等值其他外幣
	 **/
	public void setOtherCurr(String value) {
		this.otherCurr = value;
	}

	/**
	 * 取得聯貸說明
	 * <p/>
	 * 101/03/17調整
	 */
	public String getCountSay() {
		return this.countSay;
	}

	/**
	 * 設定聯貸說明
	 * <p/>
	 * 101/03/17調整
	 **/
	public void setCountSay(String value) {
		this.countSay = value;
	}

	/** 取得借款收付彙計數－同前頁 **/
	public String getCollectPayRef() {
		return this.collectPayRef;
	}

	/** 設定借款收付彙計數－同前頁 **/
	public void setCollectPayRef(String value) {
		this.collectPayRef = value;
	}

	/** 取得借款收付彙計數（付）－幣別 **/
	public String getCPCurr() {
		return this.CPCurr;
	}

	/** 設定借款收付彙計數（付）－幣別 **/
	public void setCPCurr(String value) {
		this.CPCurr = value;
	}

	/**
	 * 取得動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 */
	public String getUseDeadline() {
		return this.useDeadline;
	}

	/**
	 * 設定動用期限(ELLNSEEK.LNUSENO 或 ELF461_LNUSENO)
	 * <p/>
	 * 0.不再動用<br/>
	 * 1.YYYY-MM-DD～YYYY-MM-DD<br/>
	 * 2.自核准日起MM個月<br/>
	 * 3.自簽約日起MM個月<br/>
	 * 4.自首次動用日起MM個月<br/>
	 * 5.其他
	 **/
	public void setUseDeadline(String value) {
		this.useDeadline = value;
	}

	/**
	 * 取得動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整
	 */
	public String getDesp1() {
		return this.desp1;
	}

	/**
	 * 設定動用期限－其他(ELLNSEEK.USEFTMN 或ELF461_USEFTMN)
	 * <p/>
	 * 101/03/29調整
	 **/
	public void setDesp1(String value) {
		this.desp1 = value;
	}

	/** 取得風險權數－借款戶 **/
	public BigDecimal getItemC() {
		return this.itemC;
	}

	/** 設定風險權數－借款戶 **/
	public void setItemC(BigDecimal value) {
		this.itemC = value;
	}

	/** 取得風險權數－抵減後 **/
	public BigDecimal getRItemD() {
		return this.rItemD;
	}

	/** 設定風險權數－抵減後 **/
	public void setRItemD(BigDecimal value) {
		this.rItemD = value;
	}

	/** 取得資金成本率 **/
	public BigDecimal getItemG() {
		return this.itemG;
	}

	/** 設定資金成本率 **/
	public void setItemG(BigDecimal value) {
		this.itemG = value;
	}

	/** 取得占資本適足率 **/
	public BigDecimal getItemF() {
		return this.itemF;
	}

	/** 設定占資本適足率 **/
	public void setItemF(BigDecimal value) {
		this.itemF = value;
	}

	/**
	 * 取得分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 */
	public String getNpl() {
		return this.npl;
	}

	/**
	 * 設定分行逾放比
	 * <p/>
	 * 101/04/12調整<br/>
	 * 100(300
	 **/
	public void setNpl(String value) {
		this.npl = value;
	}

	/** 取得分行逾放比更新日期 **/
	public Date getNpldate() {
		return this.npldate;
	}

	/** 設定分行逾放比更新日期 **/
	public void setNpldate(Date value) {
		this.npldate = value;
	}

	/** 取得本票－同前頁 **/
	public String getCheckNoteRef() {
		return this.checkNoteRef;
	}

	/** 設定本票－同前頁 **/
	public void setCheckNoteRef(String value) {
		this.checkNoteRef = value;
	}

	/** 取得本票 **/
	public String getCheckNote() {
		return this.checkNote;
	}

	/** 設定本票 **/
	public void setCheckNote(String value) {
		this.checkNote = value;
	}

	/**
	 * 取得額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 */
	public String getCheckQNote() {
		return this.checkQNote;
	}

	/**
	 * 設定額度本票
	 * <p/>
	 * 101/02/16新增<br/>
	 * ※國內個金才需填寫<br/>
	 * 1.由借款人及連帶保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 2.由借款人及一般保證人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 3.由借款人及共同借款人共同出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 4.由借款人出具並簽立委由本行填寫到期日之授權書交本行存執<br/>
	 * 5.無此項<br/>
	 * ※除第1, 2, 3項可複選並存外，第4項或第5項皆不可與其他項目並存。<br/>
	 * eg.1|2|3 或 1|3 或 4 或 5
	 **/
	public void setCheckQNote(String value) {
		this.checkQNote = value;
	}

	/**
	 * 取得連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 */
	public String getGuarantorType() {
		return this.guarantorType;
	}

	/**
	 * 設定連保人/保證人
	 * <p/>
	 * 100/12/02因應授管處需求新增<br/>
	 * 1.連保人(預設)<br/>
	 * 2.保證人
	 **/
	public void setGuarantorType(String value) {
		this.guarantorType = value;
	}

	/**
	 * 取得連保人
	 * <p/>
	 * 128個全型字<br/>
	 * 102.02.18 欄位擴大 384 -> 1800 <br/>
	 * 個金:用來儲存舊案連保人訊息
	 */
	public String getGuarantor() {
		return this.guarantor;
	}

	/**
	 * 設定連保人
	 * <p/>
	 * 128個全型字<br/>
	 * 102.02.18 欄位擴大 384 -> 1800 <br/>
	 * 個金:用來儲存舊案連保人訊息
	 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/**
	 * 取得連保人備註
	 * <p/>
	 * 64個全型字
	 */
	public String getGuarantorMemo() {
		return this.guarantorMemo;
	}

	/**
	 * 設定連保人備註
	 * <p/>
	 * 64個全型字
	 **/
	public void setGuarantorMemo(String value) {
		this.guarantorMemo = value;
	}

	/** 取得連保人－同前頁 **/
	public String getGuarantorRef() {
		return this.guarantorRef;
	}

	/** 設定連保人－同前頁 **/
	public void setGuarantorRef(String value) {
		this.guarantorRef = value;
	}

	/** 取得物上保證人 **/
	public String getGuarantor1() {
		return this.guarantor1;
	}

	/** 設定物上保證人 **/
	public void setGuarantor1(String value) {
		this.guarantor1 = value;
	}

	/**
	 * 取得本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 */
	public String getNoInsuReason() {
		return this.noInsuReason;
	}

	/**
	 * 設定本案未送保原因
	 * <p/>
	 * 100/12/05增列<br/>
	 * 1.非中小企業<br/>
	 * 2.本行績優客戶<br/>
	 * 3.十足擔保<br/>
	 * 4.擔保率超過50%<br/>
	 * 5.營業未滿1年<br/>
	 * 6.外國人資本超過50%<br/>
	 * 7.單一法人所佔資本額超過50%<br/>
	 * 8.其他(請自行輸入)
	 **/
	public void setNoInsuReason(String value) {
		this.noInsuReason = value;
	}

	/** 取得本案未送保原因(其他) **/
	public String getNoInsuReasonOther() {
		return this.noInsuReasonOther;
	}

	/** 設定本案未送保原因(其他) **/
	public void setNoInsuReasonOther(String value) {
		this.noInsuReasonOther = value;
	}

	/** 取得擔保授信額度調整幣別 **/
	public String getAssureTotECurr() {
		return this.assureTotECurr;
	}

	/** 設定擔保授信額度調整幣別 **/
	public void setAssureTotECurr(String value) {
		this.assureTotECurr = value;
	}

	/** 取得擔保授信額度調整金額 **/
	public BigDecimal getAssureTotEAmt() {
		return this.assureTotEAmt;
	}

	/** 設定擔保授信額度調整金額 **/
	public void setAssureTotEAmt(BigDecimal value) {
		this.assureTotEAmt = value;
	}

	/**
	 * 取得合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	public String getValueTune() {
		return this.valueTune;
	}

	/**
	 * 設定合計金額調整註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 **/
	public void setValueTune(String value) {
		this.valueTune = value;
	}

	/**
	 * 取得前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLVTotCurr() {
		return this.LVTotCurr;
	}

	/**
	 * 設定前准(批覆)額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLVTotCurr(String value) {
		this.LVTotCurr = value;
	}

	/**
	 * 取得前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計
	 */
	public BigDecimal getLVTotAmt() {
		return this.LVTotAmt;
	}

	/**
	 * 設定前准(批覆)額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆授信額度】合計
	 **/
	public void setLVTotAmt(BigDecimal value) {
		this.LVTotAmt = value;
	}

	/**
	 * 取得前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getLVAssTotCurr() {
		return this.LVAssTotCurr;
	}

	/**
	 * 設定前准擔保授信額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setLVAssTotCurr(String value) {
		this.LVAssTotCurr = value;
	}

	/**
	 * 取得前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計
	 */
	public BigDecimal getLVAssTotAmt() {
		return this.LVAssTotAmt;
	}

	/**
	 * 設定前准擔保授信額度合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 由【前准批覆擔保授信額度】合計
	 **/
	public void setLVAssTotAmt(BigDecimal value) {
		this.LVAssTotAmt = value;
	}

	/**
	 * 取得新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getIncApplyTotCurr() {
		return this.incApplyTotCurr;
	}

	/**
	 * 設定新作、增額合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncApplyTotCurr(String value) {
		this.incApplyTotCurr = value;
	}

	/**
	 * 取得新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計
	 */
	public BigDecimal getIncApplyTotAmt() {
		return this.incApplyTotAmt;
	}

	/**
	 * 設定新作、增額合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * 新作、增額合計 = 授信額度合計- 前准額度 + 取消、減額合計
	 **/
	public void setIncApplyTotAmt(BigDecimal value) {
		this.incApplyTotAmt = value;
	}

	/**
	 * 取得新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getIncAssTotCurr() {
		return this.incAssTotCurr;
	}

	/**
	 * 設定新作、增額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncAssTotCurr(String value) {
		this.incAssTotCurr = value;
	}

	/**
	 * 取得新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getIncAssTotAmt() {
		return this.incAssTotAmt;
	}

	/**
	 * 設定新作、增額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setIncAssTotAmt(BigDecimal value) {
		this.incAssTotAmt = value;
	}

	/**
	 * 取得取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getDecApplyTotCurr() {
		return this.decApplyTotCurr;
	}

	/**
	 * 設定取消、減額合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecApplyTotCurr(String value) {
		this.decApplyTotCurr = value;
	}

	/**
	 * 取得取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getDecApplyTotAmt() {
		return this.decApplyTotAmt;
	}

	/**
	 * 設定取消、減額合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecApplyTotAmt(BigDecimal value) {
		this.decApplyTotAmt = value;
	}

	/**
	 * 取得取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 */
	public String getDecAssTotCurr() {
		return this.decAssTotCurr;
	}

	/**
	 * 設定取消、減額擔保額度合計幣別
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecAssTotCurr(String value) {
		this.decAssTotCurr = value;
	}

	/**
	 * 取得取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 */
	public BigDecimal getDecAssTotAmt() {
		return this.decAssTotAmt;
	}

	/**
	 * 設定取消、減額擔保額度合計金額
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setDecAssTotAmt(BigDecimal value) {
		this.decAssTotAmt = value;
	}

	/** 取得授信額度合計幣別 **/
	public String getLoanTotCurr() {
		return this.LoanTotCurr;
	}

	/** 設定授信額度合計幣別 **/
	public void setLoanTotCurr(String value) {
		this.LoanTotCurr = value;
	}

	/** 取得授信額度合計金額 **/
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/** 設定授信額度合計金額 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得擔保授信額度合計幣別 **/
	public String getAssureTotCurr() {
		return this.assureTotCurr;
	}

	/** 設定擔保授信額度合計幣別 **/
	public void setAssureTotCurr(String value) {
		this.assureTotCurr = value;
	}

	/** 取得擔保授信額度合計金額 **/
	public BigDecimal getAssureTotAmt() {
		return this.assureTotAmt;
	}

	/** 設定擔保授信額度合計金額 **/
	public void setAssureTotAmt(BigDecimal value) {
		this.assureTotAmt = value;
	}

	/**
	 * 取得擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	public String getEstTotCurr() {
		return this.estTotCurr;
	}

	/**
	 * 設定擔保品押值合計幣別
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 **/
	public void setEstTotCurr(String value) {
		this.estTotCurr = value;
	}

	/**
	 * 取得擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 */
	public BigDecimal getEstTotAmt() {
		return this.estTotAmt;
	}

	/**
	 * 設定擔保品押值合計金額
	 * <p/>
	 * 101/04/12新增<br/>
	 * ※自行輸入
	 **/
	public void setEstTotAmt(BigDecimal value) {
		this.estTotAmt = value;
	}

	/** 取得衍生性金融商品合計幣別 **/
	public String getLoanTotZCurr() {
		return this.LoanTotZCurr;
	}

	/** 設定衍生性金融商品合計幣別 **/
	public void setLoanTotZCurr(String value) {
		this.LoanTotZCurr = value;
	}

	/** 取得衍生性金融商品合計金額 **/
	public BigDecimal getLoanTotZAmt() {
		return this.LoanTotZAmt;
	}

	/** 設定衍生性金融商品合計金額 **/
	public void setLoanTotZAmt(BigDecimal value) {
		this.LoanTotZAmt = value;
	}

	/** 取得衍生性金融商品相當授信額度合計幣別 **/
	public String getLoanTotLCurr() {
		return this.LoanTotLCurr;
	}

	/** 設定衍生性金融商品相當授信額度合計幣別 **/
	public void setLoanTotLCurr(String value) {
		this.LoanTotLCurr = value;
	}

	/** 取得衍生性金融商品相當授信額度合計金額 **/
	public BigDecimal getLoanTotLAmt() {
		return this.LoanTotLAmt;
	}

	/** 設定衍生性金融商品相當授信額度合計金額 **/
	public void setLoanTotLAmt(BigDecimal value) {
		this.LoanTotLAmt = value;
	}

	/**
	 * 取得匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public Date getRateDT() {
		return this.rateDT;
	}

	/**
	 * 設定匯率資料日期
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateDT(Date value) {
		this.rateDT = value;
	}

	/**
	 * 取得結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public BigDecimal getRateLoanToLoc() {
		return this.rateLoanToLoc;
	}

	/**
	 * 設定結算匯率(放款幣折本位幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateLoanToLoc(BigDecimal value) {
		this.rateLoanToLoc = value;
	}

	/**
	 * 取得結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 */
	public BigDecimal getRateLocToSum() {
		return this.rateLocToSum;
	}

	/**
	 * 設定結算匯率(本位幣折合計幣)
	 * <p/>
	 * 100/11/10新增(保留未用)
	 **/
	public void setRateLocToSum(BigDecimal value) {
		this.rateLocToSum = value;
	}

	/**
	 * 取得結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 */
	public BigDecimal getRateCrossover() {
		return this.rateCrossover;
	}

	/**
	 * 設定結算匯率(交叉匯率)
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * (保留未用)<br/>
	 * 放款幣先折算本位幣，本位幣再折算合計幣。
	 **/
	public void setRateCrossover(BigDecimal value) {
		this.rateCrossover = value;
	}

	/**
	 * 取得折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 */
	public BigDecimal getRateConvert() {
		return this.rateConvert;
	}

	/**
	 * 設定折算匯率
	 * <p/>
	 * 100/11/10新增(保留未用)<br/>
	 * 預設值同「交叉匯率」欄。<br/>
	 * 本欄開放user自行調整，如有異動，則清空「授信額度合計金額」欄。<br/>
	 * 執行【授信額度合計】時，依此匯率計算合計。<br/>
	 * 若執行【授信額度合計】所選取的「合計幣別」與「授信額度合計幣別」欄不同時，則讀取匯率檔重新計算。
	 **/
	public void setRateConvert(BigDecimal value) {
		this.rateConvert = value;
	}

	/** 取得備註 **/
	public String getRmk() {
		return this.Rmk;
	}

	/** 設定備註 **/
	public void setRmk(String value) {
		this.Rmk = value;
	}

	/**
	 * 取得各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 */
	public String getMultiAmtFlag() {
		return this.multiAmtFlag;
	}

	/**
	 * 設定各幣別授信額度合計註記
	 * <p/>
	 * 101/04/12新增<br/>
	 * Y/N
	 **/
	public void setMultiAmtFlag(String value) {
		this.multiAmtFlag = value;
	}

	/** 取得各幣別授信額度合計 **/
	public String getMultiAmt() {
		return this.multiAmt;
	}

	/** 設定各幣別授信額度合計 **/
	public void setMultiAmt(String value) {
		this.multiAmt = value;
	}

	/**
	 * 取得各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 */
	public String getMultiAssureAmt() {
		return this.multiAssureAmt;
	}

	/**
	 * 設定各幣別其中擔保合計
	 * <p/>
	 * 101/04/12新增
	 **/
	public void setMultiAssureAmt(String value) {
		this.multiAssureAmt = value;
	}

	/**
	 * 取得輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 */
	public String getChkYN() {
		return this.chkYN;
	}

	/**
	 * 設定輸入資料檢誤完成(Y/N)
	 * <p/>
	 * 100/12/16調整<br/>
	 * Y：檢誤完成(包含計算合計)<br/>
	 * N：檢誤完成(尚未計算合計)<br/>
	 * 空值：檢誤未完成<br/>
	 * 儲存時檢核所有必要欄位是否已完備，完成則為N。(若chkYN原為Y，亦修改為N)<br/>
	 * 計算合計時檢核所有額度明細表chkYN若尚有空值，則不能執行；計算合計完成後則修改chkYN為Y。<br/>
	 * 預先檢核資料是否已登錄完整，供執行【呈主管覆核】時，可快速檢核資料是否完備用。
	 **/
	public void setChkYN(String value) {
		this.chkYN = value;
	}

	public String getHasDerivatives() {
		return hasDerivatives;
	}

	public void setHasDerivatives(String hasDerivatives) {
		this.hasDerivatives = hasDerivatives;
	}

	/**
	 * JOIN 個金額度明細表主檔補充資料檔
	 * 
	 */
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "mainId", referencedColumnName = "mainId", nullable = true, insertable = false, updatable = false) })
	private L140M03A l140m03a;

	public L140M03A getL140M03A() {
		return l140m03a;
	}

	public void setL140M03A(L140M03A l140m03a) {
		this.l140m03a = l140m03a;
	}

	public void setNotesUp(String notesUp) {
		this.notesUp = notesUp;
	}

	public String getNotesUp() {
		return notesUp;
	}

	/**
	 * 設定按轉讓發票金額%動用
	 * 
	 * @param loanPer
	 */
	public void setLoanPer(BigDecimal loanPer) {
		this.loanPer = loanPer;
	}

	/**
	 * 取得按轉讓發票金額%動用
	 * 
	 * @return
	 */
	public BigDecimal getLoanPer() {
		return loanPer;
	}

	/**
	 * 設定可否借新還舊
	 * 
	 * @param byNewOld
	 */
	public void setByNewOld(String byNewOld) {
		this.byNewOld = byNewOld;
	}

	/**
	 * 取得可否借新還舊
	 * 
	 * @return
	 */
	public String getByNewOld() {
		return byNewOld;
	}

	/**
	 * 設定本案是否為信保續約案
	 * 
	 * @param isGuaOldCase
	 */
	public void setIsGuaOldCase(String isGuaOldCase) {
		this.isGuaOldCase = isGuaOldCase;
	}

	/**
	 * 取得本案是否為信保續約案
	 * 
	 * @return
	 */
	public String getIsGuaOldCase() {
		return isGuaOldCase;
	}

	/**
	 * 設定產品種類
	 * 
	 * @param byNewOld
	 */
	public void setLnType(String lnType) {
		this.lnType = lnType;
	}

	/**
	 * 取得產品種類
	 * 
	 * @return
	 */
	public String getLnType() {
		return lnType;
	}

	/**
	 * 設定本行帳務是否有聯貸拆帳作業
	 * 
	 * @param byNewOld
	 */
	public void setHasLoanAssign(String hasLoanAssign) {
		this.hasLoanAssign = hasLoanAssign;
	}

	/**
	 * 取得本行帳務是否有聯貸拆帳作業
	 * 
	 * @return
	 */
	public String getHasLoanAssign() {
		return hasLoanAssign;
	}

	/**
	 * 設定青年創業及啟動金貸款 請日期
	 * 
	 * @param applyDate
	 */
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	/**
	 * 取得青年創業及啟動金貸款 申請日期
	 * 
	 * @return
	 */
	public Date getApplyDate() {
		return applyDate;
	}

	/** 取得各幣別衍生性授信額度合計 **/
	public String getDervMultiAmt() {
		return this.dervMultiAmt;
	}

	/** 設定各幣別衍生性授信額度合計 **/
	public void setDervMultiAmt(String value) {
		this.dervMultiAmt = value;
	}

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * 
	 * @param newDervMultiAmtFlag
	 */
	public void setNewDervMultiAmtFlag(String newDervMultiAmtFlag) {
		this.newDervMultiAmtFlag = newDervMultiAmtFlag;
	}

	/**
	 * 衍生性授信額度合計多幣別說明註記
	 * 
	 * @param newDervMultiAmtFlag
	 */
	public String getNewDervMultiAmtFlag() {
		return newDervMultiAmtFlag;
	}

	/**
	 * 設定衍生性係數複選組合字串
	 * 
	 * @param derivativesNumDscr
	 */
	public void setDerivativesNumDscr(String derivativesNumDscr) {
		this.derivativesNumDscr = derivativesNumDscr;
	}

	/**
	 * 取得衍生性係數複選組合字串
	 * 
	 * @param derivativesNumDscr
	 */
	public String getDerivativesNumDscr() {
		return derivativesNumDscr;
	}

	/**
	 * 設定額度評等
	 * 
	 * @param derivativesNumDscr
	 */
	public void setFacilityRating(String facilityRating) {
		this.facilityRating = facilityRating;
	}

	/**
	 * 取得額度評等
	 * 
	 * @param derivativesNumDscr
	 */
	public String getFacilityRating() {
		return facilityRating;
	}

	/** 設定現請擔保額度－幣別 **/
	public void setAssureApplyCurr(String value) {
		this.assureApplyCurr = value;
	}

	/** 取得現請擔保額度－幣別 **/
	public String getAssureApplyCurr() {
		return this.assureApplyCurr;
	}

	/** 設定現請擔保額度－金額 **/
	public void setAssureApplyAmt(BigDecimal value) {
		this.assureApplyAmt = value;
	}

	/** 取得現請擔保額度－金額 **/
	public BigDecimal getAssureApplyAmt() {
		return this.assureApplyAmt;
	}

	/**
	 * 設定列印明細 1:現請金額/2:信評
	 * **/
	public void setPrintDetails(String value) {
		this.printDetails = value;
	}

	/**
	 * 取得列印明細 1:現請金額/2:信評
	 * **/
	public String getPrintDetails() {
		return this.printDetails;
	}

	/**
	 * 設定保證人是否按一定比率負担保證責任
	 * **/
	public void setGuaPercentFg(String guaPercentFg) {
		this.guaPercentFg = guaPercentFg;
	}

	/**
	 * 取得保證人是否按一定比率負担保證責任
	 * **/
	public String getGuaPercentFg() {
		return guaPercentFg;
	}

	/**
	 * 設定簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	public void setCntrChgOid(String cntrChgOid) {
		this.cntrChgOid = cntrChgOid;
	}

	/**
	 * 取得簽報書OID(計算額度合計後塞值，用來判斷是否需要重新計算授信額度合計)
	 */
	public String getCntrChgOid() {
		return cntrChgOid;
	}

	/**
	 * 取得擔保品是否為房屋
	 */
	public String getHeadItem7() {
		return headItem7;
	}

	/**
	 * 設定擔保品是否為房屋
	 */
	public void setHeadItem7(String headItem7) {
		this.headItem7 = headItem7;
	}

	/**
	 * 設定本案是否屬銀行法72-2條控管對象
	 */
	public void setIs722Flag(String is722Flag) {
		this.is722Flag = is722Flag;
	}

	/**
	 * 取得本案是否屬銀行法72-2條控管對象
	 */
	public String getIs722Flag() {
		return is722Flag;
	}

	/**
	 * 設定 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public void setIs722OnFlag(String is722OnFlag) {
		this.is722OnFlag = is722OnFlag;
	}

	/**
	 * 取得 現行帳務系統或e-Loan前次報案(帳務系統無資料時)是否屬銀行法72-2條控管對象{Y , N , A:兩種都有}
	 */
	public String getIs722OnFlag() {
		return is722OnFlag;
	}

	/**
	 * 設定查詢銀行法72-2條控管對象額度序號
	 */
	public void setIs722CntrNo(String is722CntrNo) {
		this.is722CntrNo = is722CntrNo;
	}

	/**
	 * 取得查詢銀行法72-2條控管對象額度序號
	 */
	public String getIs722CntrNo() {
		return is722CntrNo;
	}

	/**
	 * 設定查詢銀行法72-2條控管對象日期
	 */
	public void setIs722QDate(Date is722QDate) {
		this.is722QDate = is722QDate;
	}

	/**
	 * 取得查詢銀行法72-2條控管對象日期
	 */
	public Date getIs722QDate() {
		return is722QDate;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋註記
	 */
	public void setCntrNoChkExistFlag(String cntrNoChkExistFlag) {
		this.cntrNoChkExistFlag = cntrNoChkExistFlag;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋註記
	 */
	public String getCntrNoChkExistFlag() {
		return cntrNoChkExistFlag;
	}

	/**
	 * 設定額度序號檢核是否存在帳務擋日期
	 */
	public void setCntrNoChkExistDate(Date cntrNoChkExistDate) {
		this.cntrNoChkExistDate = cntrNoChkExistDate;
	}

	/**
	 * 取得額度序號檢核是否存在帳務擋日期
	 */
	public Date getCntrNoChkExistDate() {
		return cntrNoChkExistDate;
	}

	/**
	 * 取得是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuy() {
		return isBuy;
	}

	/**
	 * 設定是否為購置不動產
	 * 
	 * @param isBuy
	 */
	public void setIsBuy(String isBuy) {
		this.isBuy = isBuy;
	}

	/**
	 * 取得排除條件
	 * 
	 * @return
	 */
	public String getExItem() {
		return exItem;
	}

	/**
	 * 設定排除條件
	 * 
	 * @param exItem
	 */
	public void setExItem(String exItem) {
		this.exItem = exItem;
	}

	/**
	 * 取得前案是否為購置不動產
	 * 
	 * @return
	 */
	public String getIsBuyOn() {
		return isBuyOn;
	}

	/**
	 * 設定前案是否為購置不動產
	 * 
	 * @param isBuyOn
	 */
	public void setIsBuyOn(String isBuyOn) {
		this.isBuyOn = isBuyOn;
	}

	/**
	 * 取得前案排除條件
	 * 
	 * @return
	 */
	public String getExItemOn() {
		return exItemOn;
	}

	/**
	 * 設定前案排除條件
	 * 
	 * @param exItemOn
	 */
	public void setExItemOn(String exItemOn) {
		this.exItemOn = exItemOn;
	}

	/**
	 * 設定利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public void setUnsecureFlag(String unsecureFlag) {
		this.unsecureFlag = unsecureFlag;
	}

	/**
	 * 取得利害關係人敘作無擔保授信註記 J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public String getUnsecureFlag() {
		return unsecureFlag;
	}

	/**
	 * 設定備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public void setNoLoanBk(String noLoanBk) {
		this.noLoanBk = noLoanBk;
	}

	/**
	 * 取得備份原NOLOAN J-104-0219-001
	 * 修改e-Loan授信管理系統企金額度明細表申請內容之「額度性質」欄，有關為無擔保授信不計入限額註記項目之「說明」及其下拉選項。
	 */
	public String getNoLoanBk() {
		return noLoanBk;
	}

	/**
	 * 設定是否為供應鏈融資 J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
	 */
	public void setIsEfin(String isEfin) {
		this.isEfin = isEfin;
	}

	/**
	 * 取得是否為供應鏈融資 J-104-0284-001 額度明細表檢核供應鏈融資賣放限週轉科目
	 */
	public String getIsEfin() {
		return isEfin;
	}

	/**
	 * 設定是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	public void setHasAmFee(String hasAmFee) {
		this.hasAmFee = hasAmFee;
	}

	/**
	 * 取得是否收取帳務管理費 J-104-0264-001 額度明細表新增是否收取帳務管理費
	 */
	public String getHasAmFee() {
		return hasAmFee;
	}

	/**
	 * 設定是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public void setIsNonSMEProjLoan(String isNonSMEProjLoan) {
		this.isNonSMEProjLoan = isNonSMEProjLoan;
	}

	/**
	 * 取得是否為振興經濟非中小企業專案貸款 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public String getIsNonSMEProjLoan() {
		return isNonSMEProjLoan;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public void setNonSMEProjLoanAmt(BigDecimal nonSMEProjLoanAmt) {
		this.nonSMEProjLoanAmt = nonSMEProjLoanAmt;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額 J-104-0339-001 增加振興經濟非中小企業專案貸款案件及金額欄位
	 */
	public BigDecimal getNonSMEProjLoanAmt() {
		return nonSMEProjLoanAmt;
	}

	/**
	 * 設定本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	public void setHasRateLimit(String hasRateLimit) {
		this.hasRateLimit = hasRateLimit;
	}

	/**
	 * 取得本額度是否有下限利率 J-105-0088-001 Web e-Loan
	 * 企金授信系統利率條件無下限利率時，新增a-Loan是否需建置下限利率欄位並上傳a-Loan檢核。
	 */
	public String getHasRateLimit() {
		return hasRateLimit;
	}

	/**
	 * 設定是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCIsNonSMEProjLoan(String cIsNonSMEProjLoan) {
		this.cIsNonSMEProjLoan = cIsNonSMEProjLoan;
	}

	/**
	 * 取得是否為振興經濟非中小企業專案貸款 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public String getCIsNonSMEProjLoan() {
		return cIsNonSMEProjLoan;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCNonSMEProjLoanAmt(BigDecimal cNonSMEProjLoanAmt) {
		this.cNonSMEProjLoanAmt = cNonSMEProjLoanAmt;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額 J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public BigDecimal getCNonSMEProjLoanAmt() {
		return cNonSMEProjLoanAmt;
	}

	/**
	 * 設定振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public void setCNonSMEProjMainId(String cNonSMEProjMainId) {
		this.cNonSMEProjMainId = cNonSMEProjMainId;
	}

	/**
	 * 取得振興經濟非中小企業專案貸款金額變更動審表MAINID J-105-0135-001 Web
	 * e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
	 */
	public String getCNonSMEProjMainId() {
		return cNonSMEProjMainId;
	}

	/**
	 * 設定衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public void setDerivativeVersion(String derivativeVersion) {
		this.derivativeVersion = derivativeVersion;
	}

	/**
	 * 取得衍生性金融商品期數版本 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public String getDerivativeVersion() {
		return derivativeVersion;
	}

	/**
	 * 設定約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	public void setExceptFlag(String exceptFlag) {
		this.exceptFlag = exceptFlag;
	}

	/**
	 * 取得約定融資額度註記 J-105-0155-001 Web e-Loan國內、海外企金額度明細表增加『約定融資額度註記』欄位與上傳a-Loan功能
	 */
	public String getExceptFlag() {
		return exceptFlag;
	}

	/**
	 * 設定產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param itwCode
	 */
	public void setItwCode(String itwCode) {
		this.itwCode = itwCode;
	}

	/**
	 * 取得產品種類新創產業細目 J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @return
	 */
	public String getItwCode() {
		return itwCode;
	}

	/**
	 * 設定借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @param itwCode
	 */
	public void setIsStartUp(String isStartUp) {
		this.isStartUp = isStartUp;
	}

	/**
	 * 取得借款人行業別是否列於新創重點產業 J-105-0308-001 Web
	 * e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 * 
	 * @return
	 */
	public String getIsStartUp() {
		return isStartUp;
	}

	/**
	 * 設定物上保證人 J-106-0029-001
	 * 
	 * @param itwCode
	 */
	public void setGuarantor1Ref(String guarantor1Ref) {
		this.guarantor1Ref = guarantor1Ref;
	}

	/**
	 * 取得物上保證人 J-106-0029-001
	 * 
	 * @return
	 */
	public String getGuarantor1Ref() {
		return guarantor1Ref;
	}
	
	
	/** 設定是否屬未核配額度國家**/
	public void setIsNoFactCountry(String isNoFactCountry) {
		this.isNoFactCountry = isNoFactCountry;
	}
	
	/** 取得是否屬未核配額度國家**/
	public String getIsNoFactCountry() {
		return isNoFactCountry;
	}
	
	/** 設定未核配額度國家**/
	public void setNoFactCountry(String noFactCountry) {
		this.noFactCountry = noFactCountry;
	}
	
	/** 取得未核配額度國家**/
	public String getNoFactCountry() {
		return noFactCountry;
	}
	
	/** 設定是否屬凍結額度國家**/
	public void setIsFreezeFactCountry(String isFreezeFactCountry) {
		this.isFreezeFactCountry = isFreezeFactCountry;
	}
	
	/** 取得是否屬凍結額度國家**/
	public String getIsFreezeFactCountry() {
		return isFreezeFactCountry;
	}
	
	/** 設定凍結額度國家**/
	public void setFreezeFactCountry(String freezeFactCountry) {
		this.freezeFactCountry = freezeFactCountry;
	}
	
	/** 取得凍結額度國家**/
	public String getFreezeFactCountry() {
		return freezeFactCountry;
	}

	/** 設定是否屬中小企業創新發展專案貸款**/
	public void setInSmeFg(String inSmeFg) {
		this.inSmeFg = inSmeFg;
	}

	/** 取得是否屬中小企業創新發展專案貸款**/
	public String getInSmeFg() {
		return inSmeFg;
	}

	/** 設定週轉性支出**/
	public void setInSmeToAmt(BigDecimal inSmeToAmt) {
		this.inSmeToAmt = inSmeToAmt;
	}

	/** 取得週轉性支出**/
	public BigDecimal getInSmeToAmt() {
		return inSmeToAmt;
	}

	/** 設定資本性支出**/
	public void setInSmeCaAmt(BigDecimal inSmeCaAmt) {
		this.inSmeCaAmt = inSmeCaAmt;
	}

	/** 取得資本性支出**/
	public BigDecimal getInSmeCaAmt() {
		return inSmeCaAmt;
	}	

}
