package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF412;
import com.mega.eloan.lms.mfaloan.service.MisELF412Service;

@Service
public class MisELF412ServiceImpl extends AbstractMFAloanJdbc implements
		MisELF412Service {

	@Override
	public List<Map<String, Object>> getByKeyWithBasicData(String branch) {

		return getJdbc().queryForListWithMax(
				"MIS.ELF412.GetByKeyWithBasicData",
				new String[] { branch, "%", "%", "SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getByKeyWithBasicData(String branch,
			String custId, String dupNo) {

		return getJdbc()
				.queryForMap(
						"MIS.ELF412.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"SYS", "SYS" });

	}

	@Override
	public Map<String, Object> getDataWithPEO(String branch, String custId,
			String dupNo) {
		return getJdbc()
				.queryForMap(
						"MIS.ELF412.GetByKeyWithBasicData",
						new Object[] { branch, custId + "%", dupNo + "%",
								"PEO", "PEO" });
	}

	private List<ELF412> toELF412(List<Map<String, Object>> rowData) {
		List<ELF412> list = new ArrayList<ELF412>();
		for (Map<String, Object> row : rowData) {
			ELF412 model = new ELF412();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public ELF412 findByPk(String branch, String custId, String dupNo) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList(
				"MIS.ELF412.GetByKey",
				new String[] { branch, custId + "%", dupNo + "%" });
		List<ELF412> list = toELF412(rowData);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public List<ELF412> findByBranch(String branch) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax(
				"MIS.ELF412.GetByKey", new String[] { branch, "%", "%" });
		return toELF412(rowData);
	}

	@Override
	public List<Map<String, Object>> sel_gfnGenerateCTL_FLMS180R14(
			String date_s, String date_e) {
		return this.getJdbc().queryForListWithMax(
				"MIS.ELF412.gfnGenerateCTL_FLMS180R14",
				new String[] { date_s, date_e, date_s, date_e });
	}

	@Override
	public int updateELF412NckdFlag(Date ELF412_LRDATE, String ELF412_NEWADD,
			String ELF412_NEWDATE, String ELF412_NCKDFLAG,
			Date ELF412_NCKDDATE, String ELF412_NCKDMEMO, Date ELF412_NEXTNWDT,
			Date ELF412_NEXTLTDT, Timestamp ELF412_TMESTAMP,
			Date ELF412_UPDDATE, String ELF412_UPDATER, String ELF412_BRANCH,
			String ELF412_CUSTID, String ELF412_DUPNO, String ELF412_ISRESCUE,
			String ELF412_GUARFLAG, String ELF412_NEWRESCUE, String ELF412_NEWRESCUEYM,
            String ELF412_RANDOMTYPE) {
		int count = this.getJdbc().update(
				"lms1800flow.update.elf412.nckdflag",
				new Object[] { ELF412_LRDATE, ELF412_NEWADD, ELF412_NEWDATE,
						ELF412_NCKDFLAG, ELF412_NCKDDATE,
						Util.trimSizeInOS390(ELF412_NCKDMEMO, 200),
						ELF412_NEXTNWDT, ELF412_NEXTLTDT, ELF412_TMESTAMP,
						ELF412_UPDDATE, ELF412_UPDATER, ELF412_ISRESCUE,
						ELF412_GUARFLAG, ELF412_NEWRESCUE, ELF412_NEWRESCUEYM,
                        ELF412_RANDOMTYPE,
						ELF412_BRANCH, ELF412_CUSTID, ELF412_DUPNO });
		return count;
	}

}
