package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;

@Service
public class MisElcrcoServiceImpl extends AbstractMF<PERSON>loanJdbc implements
		MisElcrcoService {
	@Override
	public List<?> findElcrecomByIdDupno(String id, String dupno) {
		return this.getJdbc().queryForListWithMax("MISELCRECOM.selLNFE0811Rel",
				new String[] { id, dupno });
	}
	@Override
	public List<?> findElcrecomByIdDupno2(String id, String dupno) {
		return this.getJdbc().queryForListWithMax("MISELCRECOM.selCname",
				new String[] { id, dupno });
	}
	@Override
	public List<?> findElcrecomByIdDupnoForBlackList(boolean isObs, String id, String dupno) {
		if (isObs) {
			return this.findElcrecomByIdDupno2(id, dupno);
		} else {
			return this.getJdbc().queryForListWithMax("MISELCRECOM.selCnameForTWBlackList",
					new String[] { id, dupno });
		}

	}
	@Override
	public List<?> findElcrecomByCustIdAndRCustId(String custId, String dupNo,
			String rCustId, String dupNo1) {
		return this.getJdbc().queryForListWithMax("MISELCRECOM.selByCustIdAndRCustId",
				new String[] { custId, dupNo, rCustId, dupNo1 });
	}
	@Override
	public List<?> findElcrecomByIdDupnoWithR01R02ExceptSelf(String id, String dupno) {
		return this.getJdbc().queryForListWithMax("MISELCRECOM.selR01R02ExceptSelf",
				new String[] { id, dupno });
	}
	@Override
	public List<Map<String, Object>> findElcrecomByIdDupnoWithR01R02R03ExceptSelf(String id, String dupno) {
		return this.getJdbc().queryForListWithMax("MISELCRECOM.selR01R02R03ExceptSelf",
				new String[] { id, dupno });
	}

}
