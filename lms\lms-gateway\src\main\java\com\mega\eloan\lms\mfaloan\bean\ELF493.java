package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import tw.com.iisi.cap.model.GenericBean;

/** 企金覆審名單檔  **/
public class ELF493 extends GenericBean {
	
	/** 分行代號 **/
	@Column(name="ELF493_BRANCH", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String elf493_branch ;

	/** 借款人統一編號(PV-IDNO) **/
	@Column(name="ELF493_CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String elf493_custId ;

	/** 重複序號 **/
	@Column(name="ELF493_DUPNO", length=1, columnDefinition="CHAR(1)", nullable=false,unique = true)
	private String elf493_dupNo ;

	/** 主要授信戶 **/
	@Column(name="ELF493_MAINCUST", length=1, columnDefinition="CHAR(1)")
	private String elf493_mainCust;

	/** 資信評等類別 **/
	@Column(name="ELF493_CRDTYPE", length=1, columnDefinition="CHAR(1)")
	private String elf493_crdType ;

	/** 資信評等 **/
	@Column(name="ELF493_CRDTTBL", length=2, columnDefinition="CHAR(2)")
	private String elf493_crdtTbl ;

	/** 信用模型評等類別 **/
	@Column(name="ELF493_MOWTYPE", length=1, columnDefinition="CHAR(1)")
	private String elf493_mowType ;

	/** 信用模型評等 **/
	@Column(name="ELF493_MOWTBL1", length=2, columnDefinition="CHAR(2)")
	private String elf493_mowTbl1 ;

	/** 上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_LLRDATE", columnDefinition="DATE")
	private Date elf493_llrDate ;

	/** 本次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_LRDATE", columnDefinition="DATE")
	private Date elf493_lrDate ;

	/** 覆審週期 **/
	@Column(name="ELF493_RCKDLINE", length=2, columnDefinition="CHAR(2)")
	private String elf493_rckdLine;

	/** 原始週期 **/
	@Column(name="ELF493_OCKDLINE", length=2, columnDefinition="CHAR(2)")
	private String elf493_ockdLine;

	/** 戶況 **/
	@Column(name="ELF493_CSTATE", length=1, columnDefinition="CHAR(1)")
	private String elf493_cState ;

	/** 異常通報代碼 **/
	@Column(name="ELF493_MDFLAG", length=2, columnDefinition="CHAR(2)")
	private String elf493_mdFlag ;

	/** 異常通報日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_MDDT", columnDefinition="DATE")
	private Date elf493_mdDt ;

	/** 異常通報情形 **/
	@Column(name="ELF493_PROCESS", length=202, columnDefinition="CHAR(202)")
	private String elf493_process ;

	/** 新作/增額 **/
	@Column(name="ELF493_NEWADD", length=1, columnDefinition="CHAR(1)")
	private String elf493_newAdd ;

	/** 新作/增額資料日期 **/
	@Column(name="ELF493_NEWDATE", length=6, columnDefinition="CHAR(6)")
	private String elf493_newDate ;

	/** 不覆審代碼 **/
	@Column(name="ELF493_NCKDFLAG", length=1, columnDefinition="CHAR(1)")
	private String elf493_nckdFlag;

	/** 不覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_NCKDDATE", columnDefinition="DATE")
	private Date elf493_nckdDate;

	/** 不覆審備註 **/
	@Column(name="ELF493_NCKDMEMO", length=202, columnDefinition="CHAR(202)")
	private String elf493_nckdMemo;

	/** 銷戶日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_CANCELDT", columnDefinition="DATE")
	private Date elf493_cancelDt;

	/** DBUOBU 是否有共管 **/
	@Column(name="ELF493_DBUOBU", length=1, columnDefinition="CHAR(1)")
	private String elf493_dbuObu ;

	/** DBU共管客戶統編 **/
	@Column(name="ELF493_DBUCOID", length=255, columnDefinition="CHAR(255)")
	private String elf493_dbuCoid ;

	/** OBU共管客戶統編 **/
	@Column(name="ELF493_OBUCOID", length=255, columnDefinition="CHAR(255)")
	private String elf493_obuCoid ;

	/** 人工維護日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_UPDDATE", columnDefinition="DATE")
	private Date elf493_updDate ;

	/** 人工調整ID **/
	@Column(name="ELF493_UPDATER", length=6, columnDefinition="CHAR(6)")
	private String elf493_updater ;

	/** 其他備註 **/
	@Column(name="ELF493_MEMO", length=202, columnDefinition="CHAR(202)")
	private String elf493_memo ;

	/** 資料更新日 **/
	@Column(name="ELF493_TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp elf493_tmestamp;

	/** 主管機關指定覆審案件 **/
	@Column(name="ELF493_UCKDLINE", length=2, columnDefinition="CHAR(2)")
	private String elf493_uckdLine;

	/** 主管機關通知日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_UCKDDT", columnDefinition="DATE")
	private Date elf493_uckdDt ;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_DATADT", columnDefinition="DATE")
	private Date elf493_dataDt ;

	/** 最新一次下次恢復覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_NEXTNWDT", columnDefinition="DATE")
	private Date elf493_nextNwDt;

	/** 上次設定之下次恢復覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_NEXTLTDT", columnDefinition="DATE")
	private Date elf493_nextLtDt;

	/** 覆審序號 **/
	@Column(name="ELF493_SNO", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String elf493_sno ;

	/** 覆審報告表案號 **/
	@Column(name="ELF493_PROJNO", length=100, columnDefinition="CHAR(100)")
	private String elf493_projNo ;

	/** 本次不覆審代碼 **/
	@Column(name="ELF493_N_NCKDFLAG", length=2, columnDefinition="CHAR(2)")
	private String elf493_n_nckdFlag;

	/** 本次不覆審備註 **/
	@Column(name="ELF493_N_NCKDMEMO", length=202, columnDefinition="CHAR(202)")
	private String elf493_n_nckdMemo;

	/** 下次恢復覆審日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_N_NEXTNWDT", columnDefinition="DATE")
	private Date elf493_n_nextNwDt;

	/** 調整後上次覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_N_LRDATE", columnDefinition="DATE")
	private Date elf493_n_lrDate;

	/** 覆審批號 **/
	@Column(name="ELF493_BATCHNO", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String elf493_batchNo ;

	/** 是否覆審 **/
	@Column(name="ELF493_DOCSTUS1", length=2, columnDefinition="CHAR(2)")
	private String elf493_docStus1;

	/** 覆審名單UNID **/
	@Column(name="ELF493_RPTDOCID", length=32, columnDefinition="CHAR(32)")
	private String elf493_rptDocId;

	/** 資料年 **/
	@Column(name="ELF493_DATADTY", length=3, columnDefinition="CHAR(3)", nullable=false,unique = true)
	private String elf493_dataDtY ;

	/** 資料月 **/
	@Column(name="ELF493_DATADTM", length=2, columnDefinition="CHAR(2)")
	private String elf493_dataDtM ;

	/** 名單產生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_CTLGDATE", columnDefinition="DATE")
	private Date elf493_ctlgDate;

	/** 預計覆審日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_DFCTLDT", columnDefinition="DATE")
	private Date elf493_dfctlDt ;

	/** X覆審經辦行員代號 **/
	@Column(name="ELF493_APPRID", length=8, columnDefinition="CHAR(8)")
	private String elf493_apprId ;

	/** X覆審主管行員代號 **/
	@Column(name="ELF493_BOSSID", length=8, columnDefinition="CHAR(8)")
	private String elf493_bossId ;

	/**
	 * 外部評等類別 
	 * <br/>標準普爾 | 1
	 * <br/>穆迪信評 | 2
	 * <br/>惠譽信評 | 3
	 * <br/>中華信評 | 4
	 */
	@Column(name="ELF493_FCRDTYPE", length=1, columnDefinition="CHAR(1)")
	private String elf493_fcrdType;

	/**
	 * 外部評等地區別
	 * <br/>國際 | 1
	 * <br/>本國 | 2 
	 */
	@Column(name="ELF493_FCRDAREA", length=1, columnDefinition="CHAR(1)")
	private String elf493_fcrdArea;

	/**
	 * 外部評等期間別 
	 * <br/>長期 | 1
	 * <br/>短期 | 2
	 */
	@Column(name="ELF493_FCRDPRED", length=1, columnDefinition="CHAR(1)")
	private String elf493_fcrdPred;

	/** 外部評等等級 **/
	@Column(name="ELF493_FCRDGRAD", length=30, columnDefinition="CHAR(30)")
	private String elf493_fcrdGrad;

	/**
	 * 實地覆審註記
	 */
	@Column(name="ELF493_REALCKFG", length=1, columnDefinition="CHAR(1)")
	private String elf493_realCkFg;
	
	/** 實地覆審基準日**/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF493_REALDT", columnDefinition="DATE")
	private Date elf493_realDt;
	
	
	/**
	 * 覆審名單類別 J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 */
	@Column(name = "ELF493_CTLTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String elf493_ctlType;

	/**
	 * 舊簽報書MAINID
	 */
	@Column(name = "ELF493_OLDRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elf493_oldRptId;

	/**
	 * 舊簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF493_OLDRPTDT", columnDefinition = "DATE")
	private Date elf493_oldRptDt;

	/**
	 * 新簽報書MAINID
	 */
	@Column(name = "ELF493_NEWRPTID", length = 32, columnDefinition = "CHAR(32)")
	private String elf493_newRptId;

	/**
	 * 新簽報書核准日期
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ELF493_NEWRPTDT", columnDefinition = "DATE")
	private Date elf493_newRptDt;

    /** 純紓困戶 **/
    @Column(name="ELF493_ISRESCUE", length=1, columnDefinition="CHAR(1)")
    private String elf493_isRescue;

	/** 信保擔保註記
	 * 2020/04 設定為8成 **/
	@Column(name = "ELF493_GUARFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String elf493_guarFlag;

	/** 新作紓困註記 **/
	@Column(name = "ELF493_NEWRESCUE", length = 1, columnDefinition = "CHAR(1)")
	private String elf493_newRescue;

    /** 新作紓困資料年月 **/
    @Column(name="ELF493_NEWRESCUEYM", length=6, columnDefinition="CHAR(6)")
    private String elf493_newRescueYM;
	
	
	public String getElf493_branch() {
		return elf493_branch;
	}

	public void setElf493_branch(String elf493_branch) {
		this.elf493_branch = elf493_branch;
	}

	public String getElf493_custId() {
		return elf493_custId;
	}

	public void setElf493_custId(String elf493_custId) {
		this.elf493_custId = elf493_custId;
	}

	public String getElf493_dupNo() {
		return elf493_dupNo;
	}

	public void setElf493_dupNo(String elf493_dupNo) {
		this.elf493_dupNo = elf493_dupNo;
	}

	public String getElf493_mainCust() {
		return elf493_mainCust;
	}

	public void setElf493_mainCust(String elf493_mainCust) {
		this.elf493_mainCust = elf493_mainCust;
	}

	public String getElf493_crdType() {
		return elf493_crdType;
	}

	public void setElf493_crdType(String elf493_crdType) {
		this.elf493_crdType = elf493_crdType;
	}

	public String getElf493_crdtTbl() {
		return elf493_crdtTbl;
	}

	public void setElf493_crdtTbl(String elf493_crdtTbl) {
		this.elf493_crdtTbl = elf493_crdtTbl;
	}

	public String getElf493_mowType() {
		return elf493_mowType;
	}

	public void setElf493_mowType(String elf493_mowType) {
		this.elf493_mowType = elf493_mowType;
	}

	public String getElf493_mowTbl1() {
		return elf493_mowTbl1;
	}

	public void setElf493_mowTbl1(String elf493_mowTbl1) {
		this.elf493_mowTbl1 = elf493_mowTbl1;
	}

	public Date getElf493_llrDate() {
		return elf493_llrDate;
	}

	public void setElf493_llrDate(Date elf493_llrDate) {
		this.elf493_llrDate = elf493_llrDate;
	}

	public Date getElf493_lrDate() {
		return elf493_lrDate;
	}

	public void setElf493_lrDate(Date elf493_lrDate) {
		this.elf493_lrDate = elf493_lrDate;
	}

	public String getElf493_rckdLine() {
		return elf493_rckdLine;
	}

	public void setElf493_rckdLine(String elf493_rckdLine) {
		this.elf493_rckdLine = elf493_rckdLine;
	}

	public String getElf493_ockdLine() {
		return elf493_ockdLine;
	}

	public void setElf493_ockdLine(String elf493_ockdLine) {
		this.elf493_ockdLine = elf493_ockdLine;
	}

	public String getElf493_cState() {
		return elf493_cState;
	}

	public void setElf493_cState(String elf493_cState) {
		this.elf493_cState = elf493_cState;
	}

	public String getElf493_mdFlag() {
		return elf493_mdFlag;
	}

	public void setElf493_mdFlag(String elf493_mdFlag) {
		this.elf493_mdFlag = elf493_mdFlag;
	}

	public Date getElf493_mdDt() {
		return elf493_mdDt;
	}

	public void setElf493_mdDt(Date elf493_mdDt) {
		this.elf493_mdDt = elf493_mdDt;
	}

	public String getElf493_process() {
		return elf493_process;
	}

	public void setElf493_process(String elf493_process) {
		this.elf493_process = elf493_process;
	}

	public String getElf493_newAdd() {
		return elf493_newAdd;
	}

	public void setElf493_newAdd(String elf493_newAdd) {
		this.elf493_newAdd = elf493_newAdd;
	}

	public String getElf493_newDate() {
		return elf493_newDate;
	}

	public void setElf493_newDate(String elf493_newDate) {
		this.elf493_newDate = elf493_newDate;
	}

	public String getElf493_nckdFlag() {
		return elf493_nckdFlag;
	}

	public void setElf493_nckdFlag(String elf493_nckdFlag) {
		this.elf493_nckdFlag = elf493_nckdFlag;
	}

	public Date getElf493_nckdDate() {
		return elf493_nckdDate;
	}

	public void setElf493_nckdDate(Date elf493_nckdDate) {
		this.elf493_nckdDate = elf493_nckdDate;
	}

	public String getElf493_nckdMemo() {
		return elf493_nckdMemo;
	}

	public void setElf493_nckdMemo(String elf493_nckdMemo) {
		this.elf493_nckdMemo = elf493_nckdMemo;
	}

	public Date getElf493_cancelDt() {
		return elf493_cancelDt;
	}

	public void setElf493_cancelDt(Date elf493_cancelDt) {
		this.elf493_cancelDt = elf493_cancelDt;
	}

	public String getElf493_dbuObu() {
		return elf493_dbuObu;
	}

	public void setElf493_dbuObu(String elf493_dbuObu) {
		this.elf493_dbuObu = elf493_dbuObu;
	}

	public String getElf493_dbuCoid() {
		return elf493_dbuCoid;
	}

	public void setElf493_dbuCoid(String elf493_dbuCoid) {
		this.elf493_dbuCoid = elf493_dbuCoid;
	}

	public String getElf493_obuCoid() {
		return elf493_obuCoid;
	}

	public void setElf493_obuCoid(String elf493_obuCoid) {
		this.elf493_obuCoid = elf493_obuCoid;
	}

	public Date getElf493_updDate() {
		return elf493_updDate;
	}

	public void setElf493_updDate(Date elf493_updDate) {
		this.elf493_updDate = elf493_updDate;
	}

	public String getElf493_updater() {
		return elf493_updater;
	}

	public void setElf493_updater(String elf493_updater) {
		this.elf493_updater = elf493_updater;
	}

	public String getElf493_memo() {
		return elf493_memo;
	}

	public void setElf493_memo(String elf493_memo) {
		this.elf493_memo = elf493_memo;
	}

	public Timestamp getElf493_tmestamp() {
		return elf493_tmestamp;
	}

	public void setElf493_tmestamp(Timestamp elf493_tmestamp) {
		this.elf493_tmestamp = elf493_tmestamp;
	}

	public String getElf493_uckdLine() {
		return elf493_uckdLine;
	}

	public void setElf493_uckdLine(String elf493_uckdLine) {
		this.elf493_uckdLine = elf493_uckdLine;
	}

	public Date getElf493_uckdDt() {
		return elf493_uckdDt;
	}

	public void setElf493_uckdDt(Date elf493_uckdDt) {
		this.elf493_uckdDt = elf493_uckdDt;
	}

	public Date getElf493_dataDt() {
		return elf493_dataDt;
	}

	public void setElf493_dataDt(Date elf493_dataDt) {
		this.elf493_dataDt = elf493_dataDt;
	}

	public Date getElf493_nextNwDt() {
		return elf493_nextNwDt;
	}

	public void setElf493_nextNwDt(Date elf493_nextNwDt) {
		this.elf493_nextNwDt = elf493_nextNwDt;
	}

	public Date getElf493_nextLtDt() {
		return elf493_nextLtDt;
	}

	public void setElf493_nextLtDt(Date elf493_nextLtDt) {
		this.elf493_nextLtDt = elf493_nextLtDt;
	}

	public String getElf493_sno() {
		return elf493_sno;
	}

	public void setElf493_sno(String elf493_sno) {
		this.elf493_sno = elf493_sno;
	}

	public String getElf493_projNo() {
		return elf493_projNo;
	}

	public void setElf493_projNo(String elf493_projNo) {
		this.elf493_projNo = elf493_projNo;
	}

	public String getElf493_n_nckdFlag() {
		return elf493_n_nckdFlag;
	}

	public void setElf493_n_nckdFlag(String elf493_n_nckdFlag) {
		this.elf493_n_nckdFlag = elf493_n_nckdFlag;
	}

	public String getElf493_n_nckdMemo() {
		return elf493_n_nckdMemo;
	}

	public void setElf493_n_nckdMemo(String elf493_n_nckdMemo) {
		this.elf493_n_nckdMemo = elf493_n_nckdMemo;
	}

	public Date getElf493_n_nextNwDt() {
		return elf493_n_nextNwDt;
	}

	public void setElf493_n_nextNwDt(Date elf493_n_nextNwDt) {
		this.elf493_n_nextNwDt = elf493_n_nextNwDt;
	}

	public Date getElf493_n_lrDate() {
		return elf493_n_lrDate;
	}

	public void setElf493_n_lrDate(Date elf493_n_lrDate) {
		this.elf493_n_lrDate = elf493_n_lrDate;
	}

	public String getElf493_batchNo() {
		return elf493_batchNo;
	}

	public void setElf493_batchNo(String elf493_batchNo) {
		this.elf493_batchNo = elf493_batchNo;
	}

	public String getElf493_docStus1() {
		return elf493_docStus1;
	}

	public void setElf493_docStus1(String elf493_docStus1) {
		this.elf493_docStus1 = elf493_docStus1;
	}

	public String getElf493_rptDocId() {
		return elf493_rptDocId;
	}

	public void setElf493_rptDocId(String elf493_rptDocId) {
		this.elf493_rptDocId = elf493_rptDocId;
	}

	public String getElf493_dataDtY() {
		return elf493_dataDtY;
	}

	public void setElf493_dataDtY(String elf493_dataDtY) {
		this.elf493_dataDtY = elf493_dataDtY;
	}

	public String getElf493_dataDtM() {
		return elf493_dataDtM;
	}

	public void setElf493_dataDtM(String elf493_dataDtM) {
		this.elf493_dataDtM = elf493_dataDtM;
	}

	public Date getElf493_ctlgDate() {
		return elf493_ctlgDate;
	}

	public void setElf493_ctlgDate(Date elf493_ctlgDate) {
		this.elf493_ctlgDate = elf493_ctlgDate;
	}

	public Date getElf493_dfctlDt() {
		return elf493_dfctlDt;
	}

	public void setElf493_dfctlDt(Date elf493_dfctlDt) {
		this.elf493_dfctlDt = elf493_dfctlDt;
	}

	public String getElf493_apprId() {
		return elf493_apprId;
	}

	public void setElf493_apprId(String elf493_apprId) {
		this.elf493_apprId = elf493_apprId;
	}

	public String getElf493_bossId() {
		return elf493_bossId;
	}

	public void setElf493_bossId(String elf493_bossId) {
		this.elf493_bossId = elf493_bossId;
	}

	public String getElf493_fcrdType() {
		return elf493_fcrdType;
	}

	public void setElf493_fcrdType(String elf493_fcrdType) {
		this.elf493_fcrdType = elf493_fcrdType;
	}

	public String getElf493_fcrdArea() {
		return elf493_fcrdArea;
	}

	public void setElf493_fcrdArea(String elf493_fcrdArea) {
		this.elf493_fcrdArea = elf493_fcrdArea;
	}

	public String getElf493_fcrdPred() {
		return elf493_fcrdPred;
	}

	public void setElf493_fcrdPred(String elf493_fcrdPred) {
		this.elf493_fcrdPred = elf493_fcrdPred;
	}

	public String getElf493_fcrdGrad() {
		return elf493_fcrdGrad;
	}

	public void setElf493_fcrdGrad(String elf493_fcrdGrad) {
		this.elf493_fcrdGrad = elf493_fcrdGrad;
	}

	public void setElf493_realCkFg(String elf493_realCkFg) {
		this.elf493_realCkFg = elf493_realCkFg;
	}

	public String getElf493_realCkFg() {
		return elf493_realCkFg;
	}

	public void setElf493_realDt(Date elf493_realDt) {
		this.elf493_realDt = elf493_realDt;
	}

	public Date getElf493_realDt() {
		return elf493_realDt;
	}

	public void setElf493_ctlType(String elf493_ctlType) {
		this.elf493_ctlType = elf493_ctlType;
	}

	public String getElf493_ctlType() {
		return elf493_ctlType;
	}

	public void setElf493_oldRptId(String elf493_oldRptId) {
		this.elf493_oldRptId = elf493_oldRptId;
	}

	public String getElf493_oldRptId() {
		return elf493_oldRptId;
	}

	public void setElf493_oldRptDt(Date elf493_oldRptDt) {
		this.elf493_oldRptDt = elf493_oldRptDt;
	}

	public Date getElf493_oldRptDt() {
		return elf493_oldRptDt;
	}

	public void setElf493_newRptId(String elf493_newRptId) {
		this.elf493_newRptId = elf493_newRptId;
	}

	public String getElf493_newRptId() {
		return elf493_newRptId;
	}

	public void setElf493_newRptDt(Date elf493_newRptDt) {
		this.elf493_newRptDt = elf493_newRptDt;
	}

	public Date getElf493_newRptDt() {
		return elf493_newRptDt;
	}

    public String getElf493_isRescue() {
        return elf493_isRescue;
    }

    public void setElf493_isRescue(String elf493_isRescue) {
        this.elf493_isRescue = elf493_isRescue;
    }

	public String getElf493_guarFlag() {
		return elf493_guarFlag;
	}

	public void setElf493_guarFlag(String elf493_guarFlag) {
		this.elf493_guarFlag = elf493_guarFlag;
	}

	public String getElf493_newRescue() {
		return elf493_newRescue;
	}

    public void setElf493_newRescue(String elf493_newRescue) {
		this.elf493_newRescue = elf493_newRescue;
	}

    public String getElf493_newRescueYM() {
        return elf493_newRescueYM;
    }

    public void setElf493_newRescueYM(String elf493_newRescueYM) {
        this.elf493_newRescueYM = elf493_newRescueYM;
    }
}
