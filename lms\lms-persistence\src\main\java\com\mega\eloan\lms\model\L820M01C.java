/* 
 * L820M01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 以房養老貸款撥款前查詢明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L820M01C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L820M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** uid **/
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Size(max=14)
	@Column(name="LNF242_LOAN_NO", length=14, columnDefinition="CHAR(14)")
	private String lnf242_loan_no;

	/** 預計撥款日 **/
	@Column(name="ESTIMATETIME", columnDefinition="TIMESTAMP")
	private Timestamp estimateTime;

	/** 實際撥款日 **/
	@Column(name="ACTUALTIME", columnDefinition="TIMESTAMP")
	private Timestamp actualTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得uid **/
	public String getUid() {
		return this.uid;
	}
	/** 設定uid **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getLnf242_loan_no() {
		return this.lnf242_loan_no;
	}
	/** 設定放款帳號 **/
	public void setLnf242_loan_no(String value) {
		this.lnf242_loan_no = value;
	}

	/** 取得預計撥款日 **/
	public Timestamp getEstimateTime() {
		return this.estimateTime;
	}
	/** 設定預計撥款日 **/
	public void setEstimateTime(Timestamp value) {
		this.estimateTime = value;
	}

	/** 取得實際撥款日 **/
	public Timestamp getActualTime() {
		return this.actualTime;
	}
	/** 設定實際撥款日 **/
	public void setActualTime(Timestamp value) {
		this.actualTime = value;
	}
}
