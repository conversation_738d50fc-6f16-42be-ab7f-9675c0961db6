/* 
 *  LMS1605Flow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.flow;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import tw.com.jcs.flow.FlowInstance;

import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L160M01A;

/**
 * <pre>
 * 動審表流程
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
@Component
public class LMS1605Flow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;

	// node 決策點的name value 線的name
	@Transition(node = "海外_待覆核", value = "確認")
	public void check(FlowInstance instance) {
		// 寫入文件核定者
		// String instanceId = instance.getId().toString();
		// L160M01A meta = (L160M01A)metaDao.findByOid(getDomainClass(),
		// instanceId);
		// 判斷是否先行動用
		// String resultNext = ("Y".equals(((L160M01A) meta).getUseType())) ?
		// "先行動用" : "結案";
		// instance.setAttribute("result", resultType ? resultNext : "退回");
	}

	@Transition(node = "海外_編製中")
	public void test2(FlowInstance instance) {
		// 寫入文件核定者
		// String instanceId = instance.getId().toString();
		// Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
	}

	@Transition(node = "海外_待覆核")
	public void test3(FlowInstance instance) {
		// 寫入文件核定者
		// String instanceId = instance.getId().toString();
		// String ss = instance.getId().toString();
		// Meta meta = metaDao.findByOid(getDomainClass(), instanceId);
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L160M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}