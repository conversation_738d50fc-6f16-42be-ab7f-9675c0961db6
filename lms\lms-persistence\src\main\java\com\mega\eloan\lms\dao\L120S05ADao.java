/* 
 * L120S05ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S05A;

/** 借款人集團相關資料檔 **/
public interface L120S05ADao extends IGenericDao<L120S05A> {

	L120S05A findByOid(String oid);
	
	List<L120S05A> findByMainId(String mainId);
	
	L120S05A findByUniqueKey(String mainId);

	List<L120S05A> findByIndex01(String mainId);
	
	List<Object[]> findMaxCaseSeq(String mainId);
	
	List<Object[]> findTotAmtBSeq(String mainId);

	int delModel(String mainId);
}