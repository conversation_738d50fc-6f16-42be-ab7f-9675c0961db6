package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.gwclient.GWLogger;
import com.mega.eloan.common.gwclient.GWType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.GWLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L160M01ADao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1601Service;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L161S01D;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * RPA 信保保證書查詢
 * </pre>
 * 
 * LOCAL Test URL example ： http://localhost:9081/lms-web/app/schedulerRPA
 * 
 * Post Request :
 * {"serviceId":"CorporateLoanGuarService","vaildIP":"N","request"
 * :{"responseCode":"0","responseMsg":"查詢成功","uniqueID":
 * "7580953a5aa341a391194d39004b91d3|31691021"
 * ,"data_searchResult":"0","data_GuarCaseNo"
 * :"9107198","data_GuarApproveNo":"1100033986"
 * ,"data_GuarStutas":"基金已回覆","data_QueryResultFile":
 * "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
 * g I C A g I C A g } }
 * 
 * (data_QueryResultFile 檔案太大無法貼進來)
 * 
 * SIT http://192.168.53.85:9081/lms-web/app/schedulerRPA
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */

@Service("CorporateLoanGuarService")
public class RPACorporateLoanGuarServiceImpl extends AbstractCapService
		implements WebBatchService {
	private static Logger logger = LoggerFactory
			.getLogger(RPACorporateLoanGuarServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService r6dbService;

	@Resource
	EloandbBASEService eloanService;

	@Resource
	DocFileService docFileService;

	@Resource
	DocLogService docLogService;

	@Resource
	LMS1601Service lms1601Service;

	@Resource
	L160M01ADao l160m01aDao;

	@Resource
	L161S01DDao l161s01dDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	SysParameterService sysParameterService; // GWLOG

	@Resource
	GWLogService gwLogService; // GWLOG

	@Value("${systemId}")
	private String sysId; // GWLOG

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag = new JSONObject();
		logger.info("CorporateLoanGuarService 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		// GWLOG
		GWLogger gwlogger = new GWLogger(GWType.GWTYPE_RPA, gwLogService,
				sysParameterService);

		JSONObject req = json.getJSONObject("request");

		String errorMsg = "";
		// J-109-0208_05097_B1002 Web e-Loan企金動審表一鍵查詢與列印
		String uniqueID = req.optString("uniqueID", ""); // Reference
		String mainId = uniqueID.split("\\|")[0]; // L160M01A.mainid OR
													// L120M01A.mainId
		String queryCustId = uniqueID.split("\\|")[1]; // L161S01D.rpaQueryReason1

		// GWLOG
		gwlogger.logBegin(sysId, queryCustId, "CorporateLoanGuarService",
				req.toString(), System.currentTimeMillis());

		String ownBrId = "";
		String uid = "";
		L160M01A l160m01a = l160m01aDao.findByMainId(mainId);
		if (l160m01a != null) {
			ownBrId = l160m01a.getOwnBrId();
			uid = l160m01a.getUid();
		} else {
			L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
			if (l120m01a != null) {
				ownBrId = l120m01a.getCaseBrId();
				uid = l120m01a.getUid();
			}
		}

		List<L161S01D> l161s01ds = l161s01dDao.findByIndex02(mainId,
				UtilConstants.RPA.TYPE.信保保證書, UtilConstants.RPA.STATUS.查詢中,
				queryCustId);

		for (L161S01D l161s01d : l161s01ds) {
			try {
				byte[] bytes1 = LMSUtil.base64StringToImage(req
						.getString("data_QueryResultFile"));
				String responseCode = req.getString("responseCode");
				String responseMsg = req.getString("responseMsg");

				// TODO TEST
				// ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
				// BufferedImage bi1 = ImageIO.read(bais);
				// File f1 = new File("d://out.jpg");
				// ImageIO.write(bi1, "jpg", f1);

				// 設定上傳檔案資訊
				DocFile docFile = new DocFile();
				docFile.setBranchId(ownBrId);
				docFile.setContentType("application/pdf");
				docFile.setMainId(mainId);
				docFile.setPid(uid);
				docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
				docFile.setFieldId("rpa_lms");
				docFile.setDeletedTime(null);
				docFile.setSrcFileName(l161s01d.getRpaQueryReason1() + ".pdf");
				docFile.setUploadTime(CapDate.getCurrentTimestamp());
				docFile.setSysId(docFileService.getSysId());
				docFile.setFileSize(bytes1.length);
				docFile.setFileDesc(responseCode.equals("0") ? "查詢成功"
						: responseMsg);

				InputStream is = new ByteArrayInputStream(bytes1);
				;
				String fileKey = "";
				int[] dimension = { -1, -1 };
				try {
					// 排除附檔欄位，因某些特殊字串造成CLOB存檔失敗
					JSONObject reqSave = json.getJSONObject("request");
					reqSave.remove("data_QueryResultFile");

					if (responseCode.equals("0")) {
						// 設定上傳檔案處理物件
						docFile.setData(IOUtils.toByteArray(is));

						// 儲存上傳檔案
						fileKey = docFileService.save(docFile);

						l161s01d.setStatus("A02"); // 查詢完成
						l161s01d.setReason(responseMsg);
						l161s01d.setData(reqSave.toString());
						l161s01d.setDocfileoid(fileKey);

						String searchResult = req
								.getString("data_searchResult");

						String memo = "";

						String guarCaseNo = ""; // 發文字號
						String guarApproveNo = ""; // 保證案號
						String guarStutas = ""; // 處理狀態

						if (searchResult.equals("0")) {

							guarCaseNo = req.getString("data_GuarCaseNo"); // 發文字號
							guarApproveNo = req.getString("data_GuarApproveNo"); // 保證案號
							guarStutas = req.getString("data_GuarStutas"); // 處理狀態

							memo = "統編" + l161s01d.getRpaQueryReason1()
									+ "查詢成功，：" + "發文字號「" + guarCaseNo
									+ "」，保證案號「" + guarApproveNo + "」，處理狀態「"
									+ guarStutas + "」。";
						} else {
							memo = "統編" + l161s01d.getRpaQueryReason1()
									+ "查無資料";
						}

						l161s01d.setMemo(memo);
						l161s01d.setRpaReturnData1(Util.trim(guarCaseNo)); // 發文字號
						l161s01d.setRpaReturnData2(Util.trim(guarApproveNo)); // 保證案號
						l161s01d.setRpaReturnData3(Util.truncateString(
								Util.trim(guarStutas), 100)); // 處理狀態

					} else {
						l161s01d.setStatus("A03"); // 查詢失敗
						l161s01d.setReason(responseMsg);
						l161s01d.setData(reqSave.toString());
						l161s01d.setRpaReturnData1(""); // 發文字號
						l161s01d.setRpaReturnData2(""); // 保證案號
						l161s01d.setRpaReturnData3(""); // 處理狀態
					}

					// 若是圖檔取得其尺寸
					// dimension = docFileService.getImageDimension(docFile);
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
					throw new CapMessageException("file IO ERROR", getClass());
				} finally {
					if (is != null) {
						try {
							is.close();
						} catch (IOException e) {
							logger.debug("inputStream close Error", getClass());
						}
					}
				}

				lms1601Service.save(l161s01d);

			} catch (Exception e) {
				errorMsg = e.getMessage();
				logger.error(e.getMessage(), getClass());
				e.printStackTrace();
			}
		}
		logger.info("CorporateLoanGuarService 結束========================");

		// GWLOG
		GWException gwException = null;
		if (!CapString.isEmpty(errorMsg)) {
			logger.info(errorMsg);
			gwException = new GWException(errorMsg, getClass(),
					GWException.GWTYPE_RPA);
		}

		if (!CapString.isEmpty(errorMsg)) {
			mag = JSONObject
					.fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
							+ errorMsg + "\"}");
		} else {
			mag = JSONObject
					.fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\"}");
		}

		// GWLOG
		gwlogger.logEnd(mag.toString(), gwException, "0");

		return mag;
	}

}
