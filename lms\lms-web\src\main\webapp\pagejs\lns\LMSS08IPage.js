var dfd1 = new $.Deferred();
var initS08iJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定附加檔案內容
	fileSet : function(upFileId, delFileId, fieldId, fileGridId){
		// 上傳檔案按鈕
		$("#" + upFileId).click(function(){
			
			var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
			if (count > 0) {
				//L120S08I.confirm2=請先刪除資料後再重新上傳
				CommonAPI.showErrorMessage(i18n.lmss08a["L120S08I.confirm2"]);
				return false;
			}
			
			var limitFileSize=9437103;
			MegaApi.uploadDialog({
				fieldId:fieldId,
	            fieldIdHtml:"size='30'",
	            fileDescId:"fileDesc",
	            fileDescHtml:"size='30' maxlength='30'",
	            fileCheck: ['doc'],
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				limitSize:limitFileSize,
	            width:320,
	            height:190,			
				data:{
					mainId:$("#mainId").val()				
				},
				success : function(obj) {
					$("#" + fileGridId).trigger("reloadGrid");
				}
		   });
		});
		
		// 刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
					var data = $("#" + fileGridId).getRowData(select);
					if(data.oid == "" || data.oid == undefined || data.oid == null){		
						// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
					}				
					$.ajax({
						handler : (this.handlerName == null) ? "lms1201formhandler" : this.handlerName,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							fileOid:data.oid
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		// 檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 50,
			sortname : 'srcFileName',
			postData : {
				formAction : "queryfile",
				fieldId:fieldId,
				mainId:responseJSON.mainId
			},
			rowNum : 15,
			caption: "&nbsp;",
			hiddengrid : false,
			// expandOnLoad : true, //只對subgrid有用
			// multiselect : true,
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08I.srcFileName'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 120,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :  i18n.lmss08a['L120S08I.fileDesc'],// 檔案說明
				name : 'fileDesc',
				width : 140,
				sortable : false
			}, {
				colHeader : i18n.lmss08a['L120S08I.uploadTime'],// 上傳時間
				name : 'uploadTime',
				width : 140,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}]
		});		
	},
	copySrcByL120M01AGrid: null,
    
    /**
     *引進簽報書
     * @param {Object} brNo 分行代號
     * @param {Object} id 客戶統編
     */
    copySrcByL120M01A: function(brNo, id){
        if (!this.copySrcByL120M01AGrid) {
            this.copySrcByL120M01AGrid = $("#lmss08i_choiceL120M0AGrid").iGrid({
                handler: "lms1401gridhandler",
                height: 230,
                rownumbers: true,
                multiselect: false,
                hideMultiselect: true,
                sortname: 'caseDate|caseNo',
                sortorder: 'desc|desc',
                rowNum: 10,
                postData: {
                    formAction: "querySrcByL120M01A",
                    custId: id,
                    brNo: brNo
                },
                colModel: [{
                    colHeader: i18n.lmss08a["L120S08I.caseDate"],//簽案日期,
                    name: 'caseDate',
                    align: "center",
                    width: 80,
                    sortable: false
                }, {
                    colHeader: i18n.lmss08a["L120S08I.mainPerson"],//"主要借款人",
                    name: 'custName',
                    width: 120,
                    sortable: false
                }, {
                    colHeader: i18n.lmss08a["L120S08I.caseNum"],//"案號",
                    name: 'caseNo',
                    width: 160,
                    sortable: false
                }, {
                    colHeader: i18n.lmss08a["L120S08I.manger"],//"經辦",
                    name: 'updater',
                    width: 80,
                    sortable: false,
                    align: "center"
                }, {
                    colHeader: "oid",
                    name: 'oid',
                    hidden: true
                }, {
                    colHeader: "mainId",
                    name: 'mainId',
                    hidden: true
                }]
            });
        }
        else {
            this.copySrcByL120M01AGrid.reload({
                custId: id,
                brNo: brNo
            });
        }
        $("#lmss08i_choiceCaseReportBox").thickbox({
            //L120S08I.title.02=選擇簽報書
            title: i18n.lmss08a["L120S08I.title.02"],
            width: 680,
            height: 440,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = initS08iJson.copySrcByL120M01AGrid;
                    //單筆
                    var rowData = $grid.getSingleData();
                    if (rowData) {
                        $.thickbox.close();
                        
                        //取得FILE的OID
                        $.ajax({
                            handler: "lmscommonformhandler",
                            action: "getFileOid",
                            data: {
                            	mainId: rowData.mainId, //responseJSON.mainId, //rowData.mainId,
                                fieldId: "mainCreditSumReport"
                            },
                            success: function(obj){
                            	if(obj.fileOid == "" || obj.fileOid == undefined || obj.fileOid == null){
                            		//L120S08I.error04=該簽報書無可調閱資料
                            		CommonAPI.showErrorMessage(i18n.lmss08a["L120S08I.error04"]);
                    				return false;
                            	}else{
                            		//下載檔案
                                    $.capFileDownload({
                                        handler:"simplefiledwnhandler",
                                        data : {
                                            fileOid:obj.fileOid
                                        }
                                    });
                            	}
                            	
                            }
                        });		
  
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
        
    }	
};


 

$(document).ready(function() {
	
	setCloseConfirm(true);
	// 設定handler名稱
	initS08iJson.setHandler();
	
    var fileGridId = "lmss08i_gridfile";
    
	initS08iJson.fileSet("lmss08i_uploadFile", "lmss08i_deleteFile", "mainCreditSumReport", fileGridId);
	 
	// 設定附加檔案Grid
	initS08iJson.fileGrid(fileGridId, "mainCreditSumReport");
	
	//產生報表
	$("#lmss08i_generate").click(function(){
		lmss08i_generateReport(fileGridId);
	});
	
	//調閱舊案
	$("#lmss08i_viewOldCase").click(function(){
		lmss08i_viewOldCase();
	});
	
	/**
	登錄分行代號
	*/
	$("#lmss08i_selectCopyBranchBt").click(function(){
	   CommonAPI.showAllBranch({
	       btnAction: function(a, b){
	           $("#lmss08i_selectFilterBrno").val(b.brNo);
	           $.thickbox.close();
	       }
	   });
	});
	
	  
});



function lmss08i_generateReport(fileGridId){

	//		$.capFileDownload({
	//            handler: "lmsdownloadformhandler",
	//            data: {
	//            	mainId: responseJSON.mainId,
	//                fileName: "LMSDoc22.htm",
	//                docTempType: "LMSDoc22",
	//                fileDownloadName: "LMSDoc22.doc",
	//                serviceName: "lms1201docservice"
	//            }
	//        });

	var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
	if (count > 0) {
		//L120S08I.confirm1=執行前會刪除已存在之資料，是否確定執行？
		CommonAPI.confirmMessage(i18n.lmss08a["L120S08I.confirm1"], function(b){
            if (b) {
                //是的function
            	var select = $("#"+fileGridId).jqGrid('getRowData');
            	var data = [];
                for (var i in select) {
                    data.push( select[i].oid);
                }
                
                $.ajax({
                	handler : "lmscommonformhandler",
                    data: {
                        formAction: "deleteUploadFile",
                        realTime : "Y",
                        oids: data
                    },
                    success: function(obj){
                    	$("#"+fileGridId).trigger("reloadGrid");
                        $.ajax({
                            handler: "lms1201docservice",
                            action: "saveCreatDoc",
                            data: {
                            	mainId: responseJSON.mainId,
                                fileName: "LMSDoc23.htm",
                                docTempType: "LMSDoc23",
                                fileDownloadName: "LMSDoc23.doc",
                                fieldId: "mainCreditSumReport"
                            },
                            success: function(obj){
                            	$("#"+fileGridId).trigger("reloadGrid");
                            }
                        });		
                    }
                });
                
                
            }
        });
	} else{
		$("#"+fileGridId).trigger("reloadGrid");
        $.ajax({
            handler: "lms1201docservice",
            action: "saveCreatDoc",
            data: {
            	mainId: responseJSON.mainId,
                fileName: "LMSDoc23.htm",
                docTempType: "LMSDoc23",
                fileDownloadName: "LMSDoc23.doc",
                fieldId: "mainCreditSumReport"
            },
            success: function(obj){
            	$("#"+fileGridId).trigger("reloadGrid");
            }
        });		
	}

}

//調閱舊案
function lmss08i_viewOldCase(){
    $.ajax({
        handler: "lms1401m01formhandler",
        action: "queryL120s01a",
        data: {},
        success: function(obj){
            if ($.isEmptyObject(obj.item)) {
                //L120S08I.error01=請先登錄借款人資料
                return CommonAPI.showMessage(i18n.lmss08a['L120S08I.error01']);
            }
            $("#lmss08i_nowSelectPerson").setItems({//塞複製的select
                item: obj.item,
                format: "{value} {key}"
            });
            
            //新增額度明細表grid
            dfd1.resolve();
             
            
            
            $("[name=lmss08i_copyValueRadio][value=1]").click();
            $("#lmss08i_otherTextid,#lmss08i_selectFilterBrno").val("");
            //帶入預設分行
            $("#lmss08i_selectFilterBrno").val(userInfo ? userInfo.unitNo : "");
            
            
            $("#lmss08i_viewOldCaseBox").thickbox({
                title: i18n.lmss08a['title.04'],
                width: 430,
                height: 250,
                modal: true,
                readOnly: false,
                align: "center",
                i18n: i18n.def,
                valign: "bottom",
                buttons: {
                    "sure": function(){
                        var brNo = $("#lmss08i_selectFilterBrno").val();
                        var id = "";
                        var get = $("[name=lmss08i_copyValueRadio]:checked").val();
                        switch (get) {
	                        case "1":
	                            id = $("#lmss08i_nowSelectPerson").val();
	                            if (!$.trim(id)) {
	                                //L120S08I.error03=請選擇本案借款人
	                                return CommonAPI.showMessage(i18n.lmss08a["L120S08I.error03"]);
	                            }
	                            break;
	                        case "2":
	                            id = $("#lmss08i_otherTextid").val();
	                            if (!$.trim(id)) {
	                                //L120S08I.error02=請輸入複製來源之借款人統一編號(加上重覆序號)
	                                return CommonAPI.showMessage(i18n.lmss08a["L120S08I.error02"]);
	                            }
	                            break;
	                        default:
	                            //action_004=請先選擇需「調閱」之資料列
	                            return CommonAPI.showMessage(i18n.def["action_004"]);
	                            break;
                         }
                        
                        
                        $.thickbox.close();
                         
                        initS08iJson.copySrcByL120M01A(brNo, id);
                        
                        
                        
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }//close success function
    }); //close ajax

}




function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}