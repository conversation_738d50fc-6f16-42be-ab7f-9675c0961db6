package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractFileDownloadPage;

@Controller
@RequestMapping(path = "/fms/cls9041r05.xls")
public class CLS9041M05Report extends AbstractFileDownloadPage {

	public CLS9041M05Report() {
		super();
	}

	@Override
	public String getDownloadFileName() {
		return "CLS9041PreList.xls";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "cls9041xlsservice";
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
