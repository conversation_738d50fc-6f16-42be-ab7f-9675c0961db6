package tw.com.jcs.common;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.StringTokenizer;

/**
 * 民國日期類別<br/>
 * 可直接以Date格式操作，可直接儲存到DB (自動轉為西元年) 相關操作同Date類別，新增部分針對民國年處理的Method
 * 
 * <AUTHOR> Software Inc.
 */
public class TWNDate extends Date {

    private static final long serialVersionUID = 1L;

    private final Calendar calendar = Calendar.getInstance();

    /** 民國年判斷條件，低於此值則認定為民國年 */
    private static final int TWN_YEAR_LIMIT = 1000;

    private static final int BUFFER_SIZE = 32;

    public TWNDate() {
        this(System.currentTimeMillis());
    }

    /**
     * 建構子
     * 
     * @param date
     *            此long為new Date().getTime()請勿誤用
     */
    public TWNDate(long date) {
        super(date);
        calendar.setTime(this);
    }

    /**
     * 建構子
     * 
     * @param date
     *            (Date)
     */
    private TWNDate(Date date) {
        super(date.getTime());
        calendar.setTime(this);
    }

    /**
     * 建構子
     * 
     * @param date
     *            (String)
     */
    private TWNDate(String date) {
        this(parseDate(date));
    }

    /**
     * 傳入Date 返回建構子
     * 
     * @param date
     *            (Date)
     * @return
     */
    public static TWNDate valueOf(Date date) {
        if (date == null)
            return null;
        if (date instanceof TWNDate)
            return (TWNDate) date;
        return new TWNDate(date);
    }

    /**
     * 傳入Date 返回建構子
     * 
     * @param date
     *            (String)
     * @return
     */
    public static TWNDate valueOf(String date) {
        if (date == null || "".equals(date))
            return null;
        return new TWNDate(date);
    }

    /**
     * @return 民國日期,格式為"yyyMMdd"
     */
    public String toTW() {
        return toTW((char) 0);
    }

    /**
     * @return 民國日期,type為分格符號,type='/',格式為"yyy/MM/dd"
     */
    public String toTW(char type) {
        boolean hasDel = (int) type > 0;
        int y = get(Calendar.YEAR);
        int m = get(Calendar.MONTH);
        int d = get(Calendar.DAY_OF_MONTH);

        StringBuilder buffer = new StringBuilder();
        if (y < 10)
            buffer.append('0').append('0');
        else if (y < 100)
            buffer.append('0');
        buffer.append(y);
        if (hasDel)
            buffer.append(type);
        if (m < 10)
            buffer.append('0');
        buffer.append(m);
        if (hasDel)
            buffer.append(type);
        if (d < 10)
            buffer.append('0');
        buffer.append(d);
        return buffer.toString();
    }

    /**
     * @return 西元日期,格式為"yyyyMMdd"
     */
    public String toAD() {
        return toAD((char) 0);
    }

    /**
     * @return 西元日期,type為分格符號,type='-',格式為"yyyy-MM-dd"
     */
    public String toAD(char type) {
        boolean hasDel = (int) type > 0;
        int y = calendar.get(Calendar.YEAR);
        int m = get(Calendar.MONTH);
        int d = get(Calendar.DAY_OF_MONTH);

        StringBuilder buffer = new StringBuilder();
        buffer.append(Util.addZeroWithValue(y, 4));
        if (hasDel) {
            buffer.append(type);
        }
        buffer.append(Util.addZeroWithValue(m, 2));
        if (hasDel) {
            buffer.append(type);
        }
        buffer.append(Util.addZeroWithValue(d, 2));
        return buffer.toString();
    }

    /**
     * 日期時間,格式為"yyyy-MM-dd hh:mm:ss"
     * 
     * @param date
     * @return
     */
    private static Date parseDate(String date) {
        String dt = normalize(date);
        try {
            // 2015.08.11 Mike 修正DateFormat的Thread-Safe問題，調整為local variable
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            return format.parse(dt);
        } catch (ParseException e) {
            throw new RuntimeException("無法初始化日期！" + e);
        }
    }

    /**
     * 設定日期
     * 
     * @param date
     *            日期字串，可接受的格式如下：<br/>
     *            "y/M/d", "y-M-d h:m:s", "yyyyMMdd", "yyyMMdd"<br/>
     *            分各欄位字元定義，可參考DateFormat，分隔字元可為'/'或'-'或' '或':'
     * 
     * @see DateFormat
     */
    public void setTime(String date) {
        calendar.setTime(parseDate(date));
    }

    /**
     * 增加或減少日期<br/>
     * Example :<br/>
     * TWNDate date = new TWNDate();<br/>
     * date.add(Calendar.YEAR, 1) // 加一年<br/>
     * date.add(Calendar.MONTH, -1) // 減一個月<br/>
     * 
     * @param field
     *            為Calendar定義的日期欄位
     * @param amount
     *            增加(+)或減少(-)的數值
     * @see Calendar
     */
    public TWNDate add(int field, int amount) {
        calendar.add(field, amount);
        setTime(calendar.getTimeInMillis());
        return this;
    }

    /**
     * 日期相減<br/>
     * 
     * @param TWNDate
     *            date
     */
    public BigDecimal sub(TWNDate date) {
        return new BigDecimal((this.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 取得某個日期欄位值
     * 
     * @param field
     *            為Calendar定義的日期欄位
     * @return 該欄位值，定義可見Calendar
     * @see Calendar
     */
    public int get(int field) {
        if (field == Calendar.YEAR) {
            return calendar.get(field) - 1911;
        } else if (field == Calendar.MONTH) {
            return calendar.get(field) + 1;
        } else {
            return calendar.get(field);
        }
    }

    /**
     * 將參數的Date類型，以民國日期完整格式輸出
     * 
     * @param date
     * @return 民國日期格式字串"yyy/MM/dd"
     */
    public static String toTW(Date date) {
        return date == null ? null : new TWNDate(date.getTime()).toTW('/');
    }

    /**
     * 將參數的Date類型，以西元日期完整格式輸出
     * 
     * @param date
     * @return 西元日期格式字串"yyyy-MM-dd"
     */
    public static String toAD(Date date) {
        return date == null ? null : new TWNDate(date.getTime()).toAD('-');
    }

    /**
     * DB完整西元年格式
     */
    @Override
    public String toString() {
        return toFullAD('-');
    }

    /**
     * 取得原始Date型別
     * 
     * @return
     */
    public Date toDate() {
        return calendar.getTime();
    }

    /**
     * 以民國日期,時間完整格式輸出
     * 
     * @return 民國日期時間格式字串"yyy/MM/dd hh:mm:ss"
     */
    public static String toFullTW(Date date) {
        return date == null ? null : new TWNDate(date.getTime()).toTW('/') + " " + new TWNDate(date.getTime()).getTimeInHHMMSS();
    }

    /**
     * 以民國日期,時間完整格式輸出,type為分格符號,type='/',回傳yyy/MM/dd hh:mm:ss
     * 
     * @return 民國日期時間格式字串"yyy/MM/dd hh:mm:ss"
     */
    public String toFullTW(char type) {
        return this.toTW(type) + " " + this.getTimeInHHMMSS();
    }

    /**
     * 以西元日期,時間完整格式輸出
     * 
     * @return 西元日期時間格式字串"yyyy-MM-dd hh:mm:ss"
     */
    public static String toFullAD(Date date) {
        return date == null ? null : new TWNDate(date.getTime()).toAD('-') + " " + new TWNDate(date.getTime()).getTimeInHHMMSS();
    }

    /**
     * 以西元日期,時間完整格式輸出,type為分格符號,type='-',回傳yyyy-MM-dd hh:mm:ss
     * 
     * @return 西元日期時間格式字串"yyyy-MM-dd hh:mm:ss"
     */
    public String toFullAD(char type) {
        return this.toAD(type) + " " + this.getTimeInHHMMSS();
    }

    /**
     * 時間格式輸出
     * 
     * @return 時間格式 "hh:mm:ss"
     */
    public String getTimeInHHMMSS() {
        return getTimeInHHMMSS(':');
    }

    /**
     * 時間格式輸出,type為分格符號,type=':',回傳hh:mm:ss
     * 
     * @return 時間格式 "hh:mm:ss"
     */
    public String getTimeInHHMMSS(char type) {
        boolean hasDel = (int) type > 0;
        int h = get(Calendar.HOUR_OF_DAY);
        int m = get(Calendar.MINUTE);
        int s = get(Calendar.SECOND);

        StringBuilder buffer = new StringBuilder();
        if (h < 10)
            buffer.append(0);
        buffer.append(h);
        if (hasDel)
            buffer.append(type);
        if (m < 10)
            buffer.append(0);
        buffer.append(m);
        if (hasDel)
            buffer.append(type);
        if (s < 10)
            buffer.append(0);
        buffer.append(s);
        return buffer.toString();
    }

    /**
     * 將日期字串標準化為"y-M-d h:m:s"的格式
     * 
     * @param date
     * @return 標準化的格式字串
     */
    private static String normalize(String date) {
        // 分隔欄位
        int[] tmpDates = new int[10];
        int len = 0;
        StringTokenizer st = new StringTokenizer(date, " :/-");

        for (int i = 0; i < 500000; i++) {
            if (st.hasMoreElements()) {
                tmpDates[len++] = Integer.parseInt(st.nextElement().toString());
            } else {
                break;
            }
        }

        int[] dates = new int[len];
        System.arraycopy(tmpDates, 0, dates, 0, len);

        // 判斷是否為合法的日期字串
        if (len != 3 && len != 6) {
            // 嘗試沒有分隔字元的組合
            int size = date.length();
            if (size == 7 || size == 8) {
                len = 3;
                dates = new int[len];
                dates[0] = Integer.parseInt(date.substring(0, size - 4), 10);
                dates[1] = Integer.parseInt(date.substring(size - 4, size - 2), 10);
                dates[2] = Integer.parseInt(date.substring(size - 2, size), 10);
            }
        }
        if ((len != 3 && len != 6) || !checkLimit(dates)) {
            throw new RuntimeException("不是合法的日期字串格式！");
        }

        // 判斷是否為民國年
        if (dates[0] < TWN_YEAR_LIMIT) {
            dates[0] += 1911;
        }

        // 串接西元完整日期，無提供的欄位皆補0
        StringBuilder buffer = new StringBuilder(BUFFER_SIZE);
        if (len >= 3) {
            buffer.append(dates[0]).append("-").append(dates[1]).append("-").append(dates[2]).append(" ");
        }
        if (len >= 6) {
            buffer.append(dates[3]).append(":").append(dates[4]).append(":").append(dates[5]).append(" ");
        } else {
            buffer.append("0:0:0");
        }
        return buffer.toString();
    }

    /**
     * 將日期轉換為中文民國年字串(ex: 民國九十九年八月二十五日)
     */
    public String toChineseDate() {
        final String s = "零一二三四五六七八九";
        int y = get(Calendar.YEAR);
        int m = get(Calendar.MONTH);
        int d = get(Calendar.DAY_OF_MONTH);

        StringBuilder buffer = new StringBuilder(BUFFER_SIZE);
        buffer.append("民國");
        if (y >= 100) {
            buffer.append(s.charAt(y / 100)).append("百");
            y %= 100;
        }
        if (y >= 10) {
            buffer.append(s.charAt(y / 10)).append("十");
            y %= 10;
        } else if (y > 0) {
            buffer.append(s.charAt(0));
        }
        if (y > 0) {
            buffer.append(s.charAt(y));
        }
        buffer.append("年");

        if (m >= 10) {
            buffer.append("十");
            m %= 10;
        }
        if (m > 0) {
            buffer.append(s.charAt(m));
        }
        buffer.append("月");

        if (d >= 20) {
            buffer.append(s.charAt(d / 10));
        }
        if (d >= 10) {
            buffer.append("十");
            d %= 10;
        }
        if (d > 0) {
            buffer.append(s.charAt(d));
        }
        buffer.append("日");

        return buffer.toString();
    }

    /**
     * 判斷分隔後的日期欄位值，是否在範圍內
     * 
     * @param dates
     *            分隔後的日期欄位值<br/>
     *            長度為3(y,M,d)或6(y,M,d,h,m,s)
     * @return 是否在範圍內
     */
    private static boolean checkLimit(int[] dates) {
        boolean result = true;
        int len = dates.length;
        if (len >= 3) {
            int dayLimit = 30;
            if (dates[1] == 2) {
                int year = dates[0] + (dates[0] < TWN_YEAR_LIMIT ? 1911 : 0);
                dayLimit = ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) ? 29 : 28;
            } else if (dates[1] == 1 || dates[1] == 3 || dates[1] == 5 || dates[1] == 7 || dates[1] == 8 || dates[1] == 10 || dates[1] == 12) {
                dayLimit = 31;
            }

            result &= dates[0] > 0 && dates[1] > 0 && dates[2] > 0 && dates[0] <= 9999 && dates[1] <= 12 && dates[2] <= dayLimit;
        }
        if (len >= 6) {
            result &= dates[3] >= 0 && dates[4] >= 0 && dates[5] >= 0 && dates[3] <= 24 && dates[4] <= 60 && dates[5] <= 60;
        }
        return result;
    }

    /**
     * getLastDay:取得該年月份最後一天的日期
     * 
     * @param type
     *            分格符號
     * @return YYYY-MM-DD
     */
    public String getLastDay() {
        String day = "";
        int year = get(Calendar.YEAR);
        int month = get(Calendar.MONTH);
        year = year + 1911;
        if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
            day = "31";
        } else if (month == 2) {
            if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) { // 只要400倍數就是閏年
                day = "29";
            } else {
                day = "28";
            }
        } else {
            day = "30";
        }

        return year + "-" + month + "-" + day;
    }

    /**
     * 返回建構子
     */
    @Override
    public Object clone() {
        return new TWNDate(this);
    }

    /**
     * 讀取日期欄位,格式西元日期 yyyymmddhhmmss
     */
    public String getAS400Timestamp() {
        return this.toString().replaceAll("\\D", "");
    }

    /**
     * 讀取日期欄位,可指定輸出格式
     * 
     * @param outFormat
     * @return
     */
    public String getDate(String outFormat) {
        if ("YYYY".equalsIgnoreCase(outFormat)) {
            return toAD().substring(0, 4);
        } else if ("MM".equalsIgnoreCase(outFormat)) {
            return toAD().substring(4, 6);
        } else if ("DD".equalsIgnoreCase(outFormat)) {
            String ad = toAD();
            return ad.substring(6, 8);
        } else {
            return toAD();
        }
    }
}