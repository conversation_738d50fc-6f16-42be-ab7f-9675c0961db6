
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisELF488Service;

/**
 * <pre>
 * 覆審(額度層)
 * </pre>
 * 
 * @since 2021/12/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/12/22,EL08034,new
 *          </li>
 *          </ul>
 */
@Service
public class MisELF488ServiceImpl extends AbstractMFAloanJdbc implements
MisELF488Service {
	@Override
	public List<Map<String, Object>> sel_by_brNo_idDup(String elf488_br_no, String elf488_cust_id, String elf488_dup_no){
		return this.getJdbc().queryForListWithMax("ELF488.sel_by_brNo_idDup", new String[]{elf488_br_no+"%", elf488_cust_id, elf488_dup_no});
	}
	
	@Override
	public List<Map<String, Object>> sel_by_brNo_idDup(String elf488_br_no, String elf488_cust_id, String elf488_dup_no, Set<String> elf488_status_arr){
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		for(Map<String, Object> elf488_map : sel_by_brNo_idDup(elf488_br_no, elf488_cust_id, elf488_dup_no)){
			String elf488_status = Util.trim(MapUtils.getString(elf488_map, "ELF488_STATUS"));
			if(elf488_status_arr.contains(elf488_status)){
				result.add(elf488_map);
			}
		}
		return result;
	}	
}
