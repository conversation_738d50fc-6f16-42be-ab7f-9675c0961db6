/* 
 * L141M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L141M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L141M01B;

/** 聯行額度明細關聯檔 **/
@Repository
public class L141M01BDaoImpl extends LMSJpaDao<L141M01B, String> implements
		L141M01BDao {

	@Override
	public L141M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L141M01B> list = createQuery(L141M01B.class,search).getResultList();
		return list;
	}

	@Override
	public L141M01B findByUniqueKey(String mainId, String itemType,
			String refMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId",
				refMainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01B> findByIndex01(String mainId, String itemType,
			String refMainId) {
		ISearch search = createSearchTemplete();
		List<L141M01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemType",
					itemType);
		if (refMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "refMainId",
					refMainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L141M01B.class,search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L141M01B> findByRefMainId(String refMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		List<L141M01B> list = createQuery(L141M01B.class,search).getResultList();
		return list;
	}
	

}