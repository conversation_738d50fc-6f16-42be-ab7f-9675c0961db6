var pageAction = {	
	grid_height: 200,
	build : function(){

        $.ajax({
            handler: "cls9071formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "loadV02"
            },
            success: function(json){
            	var _addSpace = json.br_addSpace=="Y"?true:false;
            	$.each(json.br_itemOrder, function(idx, brNo) {
            		var currobj = {};
            		var brName = json.br_item[brNo];
            		currobj[brNo] = brName;
            		
            		//select
            		$("#brNo").setItems({ item: currobj, format: "{value} {key}", clear:false, space: (_addSpace?(idx==0):false) });
				});
            	
            	if(json.dataYM){
            		$("#p_dataYM1").val(json.dataYM);	
            		$("#c_dataYM1").val(json.dataYM);
            		$("#c_dataYM2").val(json.dataYM);
            	}        
            	$("input[name=rptNo][type=radio][value=1]").attr("checked", true);
            }
        });
        
		$("#inputThickBox").thickbox({
			title : '',
			width : 780,
			height : pageAction.grid_height + 180,
			modal : true,
			align : 'center',
			valign: 'bottom',
			i18n: i18n.def,
			buttons : {
				'sure' : function(){
					 $.ajax({
			            handler: "cls9071formhandler",
			            type: "POST",
			            dataType: "json",
			            data: $.extend($("#inputForm").serializeData(),{
			                formAction: "checkPteamappForm"
			            }),
			            success: function(json){
							 //不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
							 $.form.submit({
								url: __ajaxHandler,
				        		target : "_blank",
				        		data : $.extend($("#inputForm").serializeData(), {
				        			_pa : 'lmsdownloadformhandler',		        			
				        			'fileDownloadName' : "data.xls",
				        			'serviceName' : "cls9071r02rptservice"
				        		})
				        	 });			            	
			            }
			        });			        
				},
				'close' : function(){	
					$.thickbox.close();
				}
			}
		});
		
	}
}

$(function() {
	pageAction.build();
});