/* 
 * C124M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C124M01A;

/** 勞工紓困信保整批貸款申請書 **/
public interface C124M01ADao extends IGenericDao<C124M01A> {

	C124M01A findByOid(String oid);
	
	C124M01A findByMainId(String mainId);
	
	List<C124M01A> findByDocStatus(String docStatus);
	
	C124M01A findByUniqueKey(String mainId);

	List<C124M01A> findByIndex01(String mainId);

	List<C124M01A> findByIndex02(String ownBrId, String custId, String dupNo);

	/**
	 * 依勞工紓困版本取得資料
	 *
	 * @param custId
	 * @param dupNo
	 * @param verNo
	 * @return
	 */
	List<C124M01A> findByCustIdAndDupNo(String custId, String dupNo, Integer verNo);
}