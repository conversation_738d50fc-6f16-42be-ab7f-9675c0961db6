package com.mega.eloan.lms.batch.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.batch.report.LMSWEL003RptService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;

@Service("lmswel003rptservice")
public class LMSWEL003RptServiceImpl extends AbstractIISIReportService
		implements LMSWEL003RptService {

	@Resource
	BranchService branchService;
	@Resource
	C122M01ADao c122m01aDao;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		boolean skipNoData = true;
		NumericFormatter nf = new NumericFormatter("#,###.##");

		Integer totalCNT = 0;
		BigDecimal totalAMT = BigDecimal.ZERO;

		String applyKind = UtilConstants.C122_ApplyKind.H;
		List<Object[]> loanList = c122m01aDao.getAllOnLineLoan(applyKind);
		
		for (Object[] oArray : loanList) {
			totalCNT = ((Integer) oArray[0]);
			totalAMT = ((BigDecimal) oArray[1]);
			break;
		}
		//=================================
		Integer[] sumCNT = new Integer[7];
		Arrays.fill(sumCNT, 0);
		BigDecimal[] sumAMT = new BigDecimal[7];
		Arrays.fill(sumAMT, BigDecimal.ZERO);
		
		if (CapMath.compare(totalAMT, BigDecimal.ZERO) > 0) {
			TreeSet<String> brNoSet = c122m01aDao.queryOwnBranchListC122M01A(applyKind);
			List<List<String>> details = new ArrayList<List<String>>();

			String[] applyStatus = { UtilConstants.C122_ApplyStatus.受理中,
					UtilConstants.C122_ApplyStatus.審核中,
					UtilConstants.C122_ApplyStatus.不承做,
					UtilConstants.C122_ApplyStatus.轉臨櫃,
					UtilConstants.C122_ApplyStatus.已核准 };
			try {
				for (String brNo : brNoSet) {
					List<Object[]> summaryData = c122m01aDao.getOnLineLoanByBranch(applyKind, brNo);
					if (skipNoData && summaryData.isEmpty()) {
						continue;
					}
					//brZxxCNT 只加總「已結案狀態」
					Integer brZxxCNT = 0;
					BigDecimal brZxxAMT = BigDecimal.ZERO;
					Integer brTotCNT = 0;
					BigDecimal brTotAMT = BigDecimal.ZERO;
					
					List<String> detail = new ArrayList<String>();
					if(true){
						detail.add(brNo+ " "+ branchService.getBranchName(brNo)); //第1欄 - 分行名稱
					}
					for (String as : applyStatus) {
						boolean statusFlag = false;

						for (Object[] oArray : summaryData) {
							String statusResult = CapString.trimNull(oArray[0]);

							Integer CNT = ((Integer) oArray[1]);
							BigDecimal AMT = ((BigDecimal) oArray[2]);

							if (as.equals(statusResult)) {
								if (as.equals(UtilConstants.C122_ApplyStatus.受理中)) {
									sumCNT[0] += CNT;
									sumAMT[0] = sumAMT[0].add(AMT);
								} else if (as
										.equals(UtilConstants.C122_ApplyStatus.審核中)) {
									sumCNT[1] += CNT;
									sumAMT[1] = sumAMT[1].add(AMT);
								} else if (as.startsWith("Z")) {

									if (as.equals(UtilConstants.C122_ApplyStatus.不承做)) {
										sumCNT[2] += CNT;
										sumAMT[2] = sumAMT[2].add(AMT);
									} else if (as.equals(UtilConstants.C122_ApplyStatus.轉臨櫃)) {
										sumCNT[3] += CNT;
										sumAMT[3] = sumAMT[3].add(AMT);
									} else if (as.equals(UtilConstants.C122_ApplyStatus.已核准)) {
										sumCNT[4] += CNT;
										sumAMT[4] = sumAMT[4].add(AMT);
									}
									if(true){
										sumCNT[5] += CNT;// 小計=不承做+轉臨櫃+已核貸
										sumAMT[5] = sumAMT[5].add(AMT);
										//~~~
										brZxxCNT += CNT;
										brZxxAMT = brZxxAMT.add(AMT);
									}
								}
								//~~~~~~~~~~~~~~~~~~~~
								if(true){
									sumCNT[6] += CNT; // 合計=受理中+審核中+不承做+轉臨櫃+已核貸
									sumAMT[6] = sumAMT[6].add(AMT);
									//~~~
									brTotCNT += CNT;
									brTotAMT = brTotAMT.add(AMT);
								}
								//============================================================
								// 當 if (as.equals(statusResult)) { 時, 指定 statusFlag = true;
								detail.add(String.valueOf(CNT));
								detail.add(nf.reformat(CapMath.setScale(String.valueOf(AMT), 0)));
								
								statusFlag = true;
								break;
							}

						}
						//~~~~~~~~~~~~~~~~~~~~
						//第2,3欄 - 受理中 , sumCNT[0]
						//第4,5欄 - 審核中 , sumCNT[1]
						//第6,7欄 - 不承做 , sumCNT[2]
						//第8,9欄 - 轉臨櫃 , sumCNT[3]
						//第10,11欄 - 已核貸 , sumCNT[4]
						
						if (!statusFlag) { // 沒資料補0
							detail.add("0");
							detail.add("0");
						}
					} //end-loop [受理中,審核中,不承做,轉臨櫃, 已核貸]

					if(true){ //第12,13欄   分行小計「件數、金額」
						detail.add(String.valueOf(brZxxCNT)); 
						detail.add(nf.reformat(CapMath.setScale(String.valueOf(brZxxAMT), 0)));
					}
					if(true){ //第14,15欄   分行合計「件數、%」
						detail.add(String.valueOf(brTotCNT)); 
						detail.add(CapMath.multiply(CapMath.divide(String.valueOf(brTotCNT),totalCNT.toString(), 4), "100", 2));
					}
					if(true){ //第16,17欄   分行合計「申請金額、%」
						detail.add(nf.reformat(CapMath.setScale(String.valueOf(brTotAMT), 0))); 
						detail.add(CapMath.multiply(CapMath.divide(String.valueOf(brTotAMT),CapMath.bigDecimalToString(totalAMT), 4), "100", 2));
					}
					
					details.add(detail);
				}

				// 全行合計}
				for (int i = 0; i < 7; i++) {
					if(true){
						//在「本行合計」的資料列，塞入合計 {cnt1 , amt1}, {cnt2 , amt2}......{cnt6 , amt6} 的數值
						reportData.setField("cnt" + i, sumCNT[i]);
						reportData.setField("amt" + i, nf.reformat(CapMath.setScale(CapMath.bigDecimalToString(sumAMT[i]), 0)));
					}
					if (i < 6) {
						if ((i > 1) && (i < 5)) {
							//在「佔已結案比重%」的資料列，塞入數值
							reportData
									.setField("zc" + i, CapMath.multiply(
											CapMath.divide(
													String.valueOf(sumCNT[i]),
													sumCNT[5].toString(), 4),
											"100", 2));
							reportData.setField("za" + i,
									CapMath.multiply(CapMath.divide(String
											.valueOf(sumAMT[i]), CapMath
											.bigDecimalToString(sumAMT[5]), 4),
											"100", 2));

						}
						//在「佔全部案件比重%」的資料列，塞入數值
						reportData.setField(
								"rc" + i,
								CapMath.multiply(CapMath.divide(
										String.valueOf(sumCNT[i]),
										totalCNT.toString(), 4), "100", 2));
						reportData.setField("ra" + i,
								CapMath.multiply(
										CapMath.divide(String
												.valueOf(sumAMT[i]), CapMath
												.bigDecimalToString(totalAMT),
												4), "100", 2));
					}
				}

				reportData.addDetail(details);
			} catch (CapFormatException e) {
				e.printStackTrace();
			}

		} else {
		}
		reportData.setField("printDate", CapDate.getCurrentDate("yyyy-MM-dd"));
		return reportData;

	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LLMEL003"; // 授管要求改月報(rpt 上的 FORM:LLMEL019) SLMS-00033
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

}
