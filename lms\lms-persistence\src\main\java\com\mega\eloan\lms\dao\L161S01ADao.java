/* 
 * L161S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L161S01A;

/** 聯貸案參貸比率一覽表主檔 **/
public interface L161S01ADao extends IGenericDao<L161S01A> {

	L161S01A findByOid(String oid);

	List<L161S01A> findByMainId(String mainId);

	L161S01A findByUniqueKey(String mainId, String cntrNo);

	List<L161S01A> findByIndex01(String mainId);

	List<L161S01A> findByCntrNo(String CntrNo);

	L161S01A findByMainIdUid(String mainId, String uid);

	L161S01A findByMainIdCntrno(String mainId, String cntrNo);

	/**
	 * J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	 * 
	 * @param mainId
	 * @return
	 */
	public List<L161S01A> findByMainIdWithOrder(String mainId);
}