var dfd1 = new $.Deferred();
var initS08pJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	},
	// 設定附加檔案內容
	fileSet : function(upFileId, delFileId, fieldId, fileGridId){
		// 上傳檔案按鈕
		$("#" + upFileId).click(function(){
			
			var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
			if (count > 0) {
				//L120S08P.confirm2=請先刪除資料後再重新上傳
//				CommonAPI.showErrorMessage(i18n.lmss08a["L120S08P.confirm2"]);
//				return false;
			}
			
			var limitFileSize=9437103;
			MegaApi.uploadDialog({
				handler:"lmsfileuploadhandler",
				fieldId:fieldId,
	            fieldIdHtml:"size='30'",
	            fileDescId:"fileDesc",
	            fileDescHtml:"size='30' maxlength='30'",
	            //fileCheck: ['doc'],
				subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
				limitSize:limitFileSize,
				multiple : true,
	            width:320,
	            height:190,			
				data:{
					mainId:$("#mainId").val()				
				},
				success : function(obj) {
					$("#" + fileGridId).trigger("reloadGrid");
				}
		   });
		});
		
		// 刪除檔案按鈕
		$("#" + delFileId).click(function(){
			var select  = $("#" + fileGridId).getGridParam('selrow');		
			// confirmDelete=是否確定刪除?
			CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
				if(b){				
				 
					 
					var select = $("#"+fileGridId).getGridParam('selarrrow');
			        if (select == "") {
			        	// TMMDeleteError=請先選擇需修改(刪除)之資料列
						CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
						return;
			        }
				        
					
			        var data = [];
	                for (var i in select) {
	                    data.push($("#"+fileGridId).getRowData(select[i]).oid);
	                }
				 
					$.ajax({
						handler : "lmscommonformhandler",
						type : "POST",
						dataType : "json",
						data : {
							formAction : "deleteUploadFile",
							realTime : "Y",
	                        oids: data
						},
						success : function(obj) {
							$("#" + fileGridId).trigger("reloadGrid");
						}
					});
				}else{
					return ;
				}
			});
		});		
	},
	// 設定附加檔案Grid
	fileGrid : function(fileGridId, fieldId){
		// 檔案上傳grid
		$("#" + fileGridId).iGrid({
			handler : 'lms1201gridhandler',
			height : 100,
			sortname : 'srcFileName',
			postData : {
				formAction : "queryfile",
				fieldId:fieldId,
				mainId:responseJSON.mainId
			},
			rowNum : 15,
			caption: "&nbsp;",
			hiddengrid : false,
			// expandOnLoad : true, //只對subgrid有用
			multiselect : true,
			colModel : [ {
				colHeader : i18n.lmss08a['L120S08P.srcFileName'],// 原始檔案名稱,
				name : 'srcFileName',
				width : 120,
				align: "left",
				sortable : false,
				formatter : 'click',
				onclick : openDoc
			}, {
				colHeader :  i18n.lmss08a['L120S08P.fileDesc'],// 檔案說明
				name : 'fileDesc',
				width : 140,
				sortable : false
			}, {
				colHeader : i18n.lmss08a['L120S08P.uploadTime'],// 上傳時間
				name : 'uploadTime',
				width : 140,
				sortable : false
			}, {
				name : 'oid',
				hidden : true
			}],
			gridComplete : function () {
				initS08oJson.updateCombinePrintInfo(); 
			}
		});		
	} 
};



$(document).ready(function() {
	
	setCloseConfirm(true);
	// 設定handler名稱
	initS08pJson.setHandler();
	
    var fileGridId = "lmss08p_gridfile";
    
	initS08pJson.fileSet("lmss08p_uploadFile", "lmss08p_deleteFile", "recvCombinePrint", fileGridId);
	 
	// 設定附加檔案Grid
	initS08pJson.fileGrid(fileGridId, "recvCombinePrint");
	
	//產生報表
	$("#lmss08p_refresh").click(function(){
		lmss08p_refresh(fileGridId);
	});

	  
});
 
//重新整理
function lmss08p_refresh(fileGridId){
 
	var count = $("#"+fileGridId).jqGrid('getGridParam', 'records');
	$("#"+fileGridId).trigger("reloadGrid");
	

}


function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}