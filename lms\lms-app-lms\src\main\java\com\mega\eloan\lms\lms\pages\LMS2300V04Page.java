/* 
 * LMS2300V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

/**
 * <pre>
 * 簽約未動用授信案件送作業 --營運單位傳送(授管處)
 * </pre>
 * 
 * @since 2012/02/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/02/22,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms2300v04")
public class LMS2300V04Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters parameters) {
		setGridViewStatus(CreditDocStatusEnum.海外_已核准);
		// 加上Button
		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		renderJsI18N(LMS2305V01Page.class);
		renderJsI18N(LMS2305V03Page.class);
	}


}
