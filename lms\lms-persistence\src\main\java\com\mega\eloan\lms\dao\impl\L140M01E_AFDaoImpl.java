/* 
 * L140M01E_AFDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140M01E_AFDao;
import com.mega.eloan.lms.model.L140M01E_AF;

/** 動審後額度聯行攤貸比例檔 **/
@Repository
public class L140M01E_AFDaoImpl extends LMSJpaDao<L140M01E_AF, String>
	implements L140M01E_AFDao {

	@Override
	public L140M01E_AF findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140M01E_AF> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<L140M01E_AF> list = createQuery(L140M01E_AF.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L140M01E_AF> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140M01E_AF> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L140M01E_AF> findByIndex01(String mainId, String flag, String shareBrId){
		ISearch search = createSearchTemplete();
		List<L140M01E_AF> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (flag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "flag", flag);
		if (shareBrId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId", shareBrId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public L140M01E_AF findByUniqueKey(String mainId, String shareBrId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "shareBrId",
				shareBrId);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140M01E_AF> findByMainIdAndFlag(String mainId, String[] Flag) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.IN, "flag", Flag);
		return find(search);
	}
	
	@Override
	public List<L140M01E_AF> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "shareNo", CntrNo);
		search.addOrderBy("shareNo");
		List<L140M01E_AF> list = createQuery(L140M01E_AF.class,search).getResultList();
		
		return list;
	}
	
	@Override
	public List<L140M01E_AF> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L140M01E_AF> list = createQuery(L140M01E_AF.class,search).getResultList();
		return list;
	}
}