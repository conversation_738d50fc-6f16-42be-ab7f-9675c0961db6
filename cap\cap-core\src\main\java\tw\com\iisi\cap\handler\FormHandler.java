/*
 * FormHandler.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.action.IAction;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.operation.Operation;
import tw.com.iisi.cap.plugin.AjaxHandlerPlugin;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * FormHandler
 * Form資料處理
 * </pre>
 * 
 * @since 2010/07/20
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>iristu,2010/07/20,new
 *          </ul>
 */
public abstract class FormHandler extends AjaxHandlerPlugin {

    /**
     * {@value #FORM_ACTION}
     */
    public static final String FORM_ACTION = "formAction";

    /**
     * {@value #SIMPLE_OPERATION}
     */
    public static final String SIMPLE_OPERATION = "simpleOperation";

    /**
     * 執行對應Operation
     * 
     * @return {@code IResult result}
     * @see {@link AjaxHandlerPlugin#execute(PageParameters) execute}
     */
    @Override
    public IResult execute(PageParameters params) throws CapException {
        setParameters(params);
        IResult result = null;
        Operation oper = getOperation();
        if (oper != null) {
            result = oper.execute(params, this);
        }
        return result;
    }

    /**
     * 取得動作
     * 
     * @param formAction
     * @return
     */
    public abstract IAction getAction(String formAction);

    /**
     * Get Operation Name
     */
    public abstract String getOperationName();

    @Autowired
    private ApplicationContext context;

    /**
     * get Operation
     * 
     * @return
     */
    public Operation getOperation() {
        return (Operation) context.getBean(getOperationName());
    }

    /**
     * 取得版本號
     * 
     * @return {@code "1.0.0"}
     */
    public String version() {
        return "1.0.0";
    }

    /**
     * 目前是空方法 <br>
     * Properties Set
     */
    public void afterPropertiesSet() throws Exception {
        // do nothing
    }

}// ~
