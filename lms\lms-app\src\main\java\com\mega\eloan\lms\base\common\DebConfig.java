/* 
 * DebConfig.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.common;

import java.io.File;
import java.io.IOException;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.InitializingBean;

/**
 * <pre>
 * DEB 相關Config
 * </pre>
 * 
 * @since 2012/6/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/13,iristu,new
 *          </ul>
 */
public class DebConfig implements InitializingBean {

	private Properties properties;

	public DebConfig() {
	}

	public DebConfig(Properties properties) throws IOException {
		this.properties = properties;
	}

	public String getDownloadFilePath() {
		return properties.getProperty("downloadFilePath");
	}

	public String getUploadFilePath() {
		return properties.getProperty("uploadFilePath");
	}

	public String getTempFilePath() {
		return properties.getProperty("tempFilePath");
	}

	public String getUCCFTPDownloadPath() {
		return properties.getProperty("UCCFTP_DOWNLOAD_PATH");
	}

	public String getUCCFTPUploadPath() {
		return properties.getProperty("UCCFTP_UPLOAD_PATH");
	}

	public String getGDBFTPDownloadPath() {
		return properties.getProperty("GDBFTP_DOWNLOAD_PATH");
	}

	public String getGDBFTPUploadPath() {
		return properties.getProperty("GDBFTP_UPLOAD_PATH");
	}

	public String getProperty(String key) {
		if (properties.containsKey(key)) {
			return properties.getProperty(key);
		}
		return null;
	}// ;

	public String getProperty(String key, String defValue) {
		return properties.getProperty(key, defValue);
	}// ;

	public boolean containsKey(String key) {
		return properties.containsKey(key);
	}// ;

	@Override
	public void afterPropertiesSet() throws Exception {
		FileUtils.forceMkdir(new File(getUploadFilePath()));
		FileUtils.forceMkdir(new File(getTempFilePath()));
	}

}
