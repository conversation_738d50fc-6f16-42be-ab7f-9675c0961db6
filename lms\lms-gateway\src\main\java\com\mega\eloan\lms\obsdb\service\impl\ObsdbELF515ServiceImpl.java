package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.mfaloan.bean.ELF515;
import com.mega.eloan.lms.obsdb.service.ObsdbELF515Service;

@Service
public class ObsdbELF515ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF515Service {
	@Override
	public void delete(String brNo, String cntrNo) {

		// delete from mis.elf515 where ELF515_CNTRNO = ?
		this.getJdbc(brNo).update("ELF515.delete", new String[] { cntrNo });
	}

	@Override
	public void insert(String brNo, ELF515 elf515) {
		
		Date elf515_build_date = elf515.getElf515_build_date();
		int elf515_build_date_i = 0;

		if (elf515_build_date != null) {
			elf515_build_date_i = Integer.parseInt(CapDate.formatDate(
					elf515_build_date, "yyyyMMdd"));
		}

		this.getJdbc(brNo).update(
				"ELF515.insert",
				new Object[] { Util.trim(elf515.getElf515_cntrno()),
						Util.trim(elf515.getElf515_type()),
						Util.trim(elf515.getElf515_instalment()),
						Util.trim(elf515.getElf515_cntrno_m()),
						Util.trim(elf515.getElf515_inter_outer()),
						Util.trim(elf515.getElf515_site1()),
						Util.trim(elf515.getElf515_site2()),
						Util.trim(elf515.getElf515_site3()),
						Util.trim(elf515.getElf515_site4()),
						Util.trimSizeInOS390(elf515.getElf515_address(), 100),
						Util.trim(elf515.getElf515_build_state()),
						elf515_build_date_i,
						Util.trim(elf515.getElf515_sub_type1()),
						Util.trim(elf515.getElf515_sub_type2()),
						Util.trim(elf515.getElf515_sub_type3()),
						Util.trim(elf515.getElf515_sub_type4()),
						Util.trim(elf515.getElf515_sub_type5()),
						Util.trim(elf515.getElf515_sub_type6()),
						Util.trim(elf515.getElf515_sub_type7()),
						Util.trimSizeInOS390(elf515.getElf515_data1(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data2(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data3(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data4(), 100),
						Util.trimSizeInOS390(elf515.getElf515_data5(), 100),
						Util.trim(elf515.getElf515_empl_no()),
						Util.trim(elf515.getElf515_supv_no()),
						Util.trim(elf515.getElf515_createUnit()),
						Util.trim(elf515.getElf515_modifyUnit()),
						new BigDecimal(StringUtils.left(StringUtils.rightPad(
								new Timestamp(System.currentTimeMillis())
										.toString().replaceAll("\\D", ""), 17,
								"0"), 17)),
						new BigDecimal(StringUtils.left(StringUtils.rightPad(
								new Timestamp(System.currentTimeMillis())
										.toString().replaceAll("\\D", ""), 17,
								"0"), 17)) });
	}


    @Override
    public void do72_2BatchFrom515(String brNo){
//		INSERT INTO #SCHEMA#.ELF515
//				(ELF515_CNTRNO, ELF515_TYPE, ELF515_INSTALMENT, ELF515_CNTRNO_M, ELF515_INTER_OUTER, ELF515_SITE1, ELF515_SITE2, ELF515_SITE3, ELF515_SITE4,
//						ELF515_ADDRESS, ELF515_BUILD_STATE, ELF515_SUB_TYPE1, ELF515_SUB_TYPE2, ELF515_SUB_TYPE3, ELF515_SUB_TYPE4, ELF515_SUB_TYPE5,
//						ELF515_SUB_TYPE6, ELF515_SUB_TYPE7, ELF515_DATA1, ELF515_DATA2, ELF515_DATA3, ELF515_DATA4, ELF515_DATA5, ELF515_EMPL_NO, ELF515_SUPV_NO,
//						ELF515_CREATEUNIT, ELF515_MODIFYUNIT, ELF515_CREATETIME, ELF515_MODIFYTIME,ELF515_BUILD_DATE)
//		select
//		ELF506_CNTRNO, ELF506_722_EX_ITEM,'N','','','','','','',
//				'','','','','','','',
//				'','','','','','','','SYSTEM','SYSTEM',
//				'900','900',BIGINT(left(REPLACE(REPLACE(char(current TIMESTAMP),'-',''),'.',''),17)) ,BIGINT(left(REPLACE(REPLACE(char(current TIMESTAMP),'-',''),'.',''),17)),0
//		from #SCHEMA#.elf506
//		where ELF506_722_EX_ITEM != ''
        this.getJdbc(brNo).update("ELF515.batch001", new Object[]{});

    }

	@Override
	public void deleteAll(String brNo){
		this.getJdbc(brNo).update("ELF515.deleteAll",new Object[]{});
	}
}
