package com.mega.eloan.lms.lrs.pages;

import java.io.ByteArrayOutputStream;
import java.util.LinkedHashMap;
import java.util.Map;
import tw.com.jcs.common.Util;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapByteArrayDownloadResult;
import tw.com.iisi.cap.response.IResult;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.mega.eloan.common.utils.SpringContextHelper;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.pages.AbstractSimplePdfPage;
import com.mega.eloan.lms.base.service.FileDownloadService;

import tw.com.jcs.common.report.ReportGenerator;

/**
 * 
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/lrs/lms1800r02")
public class LMS1800R02Report extends AbstractSimplePdfPage {

	@Override
	public void execute(ModelMap model, PageParameters params) throws CapException {
		params.add("exportType", Engine.EXPORT_XLS);
		this.serviceName = Util.trim(params.getString("serviceName", ""));
		super.execute(model, params);

		byte[] bytes = null;
		ByteArrayOutputStream baos = null;

		try {
			bytes = ((FileDownloadService) SpringContextHelper.getBean(this
					.getFileDownloadServiceName())).getContent(params);
		} catch (Exception ex) {
			logger.error("[getContent] Exception!!", ex);
			Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
			ReportGenerator rptGenerator = new ReportGenerator();
			rptVariableMap.put("ERRORMSG",
					"EFD0066:" + ReportGenerator.getErrorInfoFromException(ex));
			rptGenerator.setVariableData(rptVariableMap);
			try {
				baos = (ByteArrayOutputStream) rptGenerator
						.generateExceptionReport(LMSUtil.getLocale());
				bytes = baos.toByteArray();
			} catch (Exception ex2) {
				logger.debug("LMS1800R02Report Exception : ", ex2);
			}

		}
		
		IResult result = new CapByteArrayDownloadResult(bytes,
				"application/ms-excel", getFileName());
		
		result.respondResult(response);

	}
	
	@Override
	public String getFileDownloadServiceName() {
		return this.serviceName;
	}

	@Override
	protected String getReportCreatorName() {
		return "lms1800r02rptservcie";
	}

	@Override
	protected String getFileName() {
		return "lms1800r02.xls";
	}

}
