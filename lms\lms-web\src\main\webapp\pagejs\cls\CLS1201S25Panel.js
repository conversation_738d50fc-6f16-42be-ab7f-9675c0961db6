initDfd.done(function(json){
	ilog.debug("<EMAIL>"); 
	
	if(json.CLS1201S25Form){
		var l120s15a_rptId = (json.CLS1201S25Form.rptId || "V2020");
		
		ilog.debug("l120s15a_rptId="+ (l120s15a_rptId||''));		
		if(l120s15a_rptId=="V202101"){
			$("#item170_desc2B").val("");
			$("#item170_descY").val(i18n.cls1201s25["item.matchCreditLoanCriterion"]); //item.matchCreditLoanCriterion=符合本行信貸規範
		}else if(l120s15a_rptId=="V2020"){
			$("#item170_desc2B").val(i18n.cls1201s25["item170.desc2B"]);
			$("#item170_descY").val(i18n.cls1201s25["item.match"]);
		}
	}
	
	$("#cls1201s25_btn_import").click(function(){
		
		var reminder = i18n.cls1201s25["message.001"];
			API.confirmMessage(reminder , function(result){
				if (result) {
					var dowork = function(){
						$.ajax({
							handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
							data : {
								formAction : "chg_rptid",
								mainId : responseJSON.mainid
							},
							success : function(json) {
									$.form.submit({
							            url: location.href,
							            target: $("#mainOid").val(),
							            data: {
							                txCode: responseJSON.txCode,
							                oid: responseJSON.oid,
							                mainId: responseJSON.mainId,
							                mainOid: responseJSON.oid,
							                mainDocStatus: responseJSON.docStatus,
							                docType: responseJSON.docType,
							                docCode: responseJSON.docCode,
							                docKind: responseJSON.docKind,
							                docURL: responseJSON.docURL,
							                docStatus: responseJSON.docStatus,
							                brnGroup: responseJSON.brnGroup,
							                ownBrId: responseJSON.ownBrId,
							                caseBrId: responseJSON.caseBrId,
							                chgrptid: "1"
							            }
							        });
							}
						});		
					};
					var save = $("#buttonPanel").find("#btnSave:visible");
					save.size() &&
					save.trigger('click', [{
						success: dowork
					}]);
				}
			});
		
		
		
		
		
//		$.ajax({
//			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
//			data : {
//				formAction : "cls1201s25_btn_import",
//				mainId : responseJSON.mainid
//			},
//			success : function(json) {
//				if(json.fetchForm){
//					$("#CLS1201S25Form").injectData(json.fetchForm);
//				}
//			}
//		});		
	});
	
	$("#cls1201s25_btn_del").click(function(){
		//action_003=是否確定「刪除」此筆資料?
		CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
            if (b) {
            	$.ajax({
        			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
        			data : {
        				formAction : "cls1201s25_btn_del",
        				mainId : responseJSON.mainid
        			},
        			success : function(json) {
        				if(json.fetchForm){
        					$("#CLS1201S25Form").injectData(json.fetchForm);
        					$("#CLS1201S25Form").reset();
        				}
        			}
        		});		
            }
        });
	});
});

