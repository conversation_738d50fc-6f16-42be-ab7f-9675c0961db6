/* 
 * L140M01S.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 購置不動動銀行法72-2控制資訊檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L140MM3C")
public class L140MM3C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 本次(Y)，前次(N)flag
	 */
	@Column(name = "flag", length = 1, columnDefinition = "CHAR(1)")
	private String flag;

	@Column(name = "ISTURNOVER", length = 1, columnDefinition = "CHAR(1)")
	private String isTurnOver;

	/**
	 * 類別
	 */
	@Column(name = "ESTATETYPE")
	private String estateType;

	/**
	 * 重建類別
	 */
	@Column(name = "ESTATESUBTYPE")
	private String estateSubType;

	/**
	 * 機構名稱,政府機關名稱,案件名稱
	 */
	@Column(name = "SUBJECT")
	private String subject;

	/**
	 * 機構代碼
	 */
	@Column(name = "SUBJECTCODE")
	private String subjectCode;

	/**
	 * 類別
	 */
	@Column(name = "SUBJECTKIND")
	private String subjectKind;

	/**
	 * 計畫狀態
	 */
	@Column(name = "ESTATESTATUS")
	private String estateStatus;

	/**
	 * 縣市代碼
	 */
	@Column(name = "ESTATECITYID")
	private String estateCityId;

	/**
	 * 鄉鎮市代碼
	 */
	@Column(name = "ESTATEAREAID")
	private String estateAreaId;

	/**
	 * 段
	 */
	@Column(name = "ESTATESIT3NO")
	private String estateSit3No;

	/**
	 * 小段
	 */
	@Column(name = "ESTATESIT4NO")
	private String estateSit4No;

	/**
	 * 座落說明
	 */
	@Column(name = "SITENOTE")
	private String siteNote;

	/**
	 * 重建方式
	 */
	@Column(name = "BUILDWAY")
	private String buildWay;

	/**
	 * 母戶額度序號
	 */
	@Column(name = "MCNTRNO")
	private String mCntrNo;

	/**
	 * 地主人數
	 */
	@Column(name = "LANDLORDNUM")
	private BigDecimal landlordNum;

	/**
	 * 其它都更危老說明
	 */
	@Column(name = "SUBTYPENOTE")
	private String subTypeNote;

	/**
	 * 說明
	 */
	@Column(name = "ESTATENOTE")
	private String estateNote;

	/**
	 * 國內/海外
	 */
	@Column(name = "POSITION")
	private String position;

	/**
	 * 預計完工/年月
	 */
	@Column(name = "OVERDATE")
	private Date overDate;

	/**
	 * 土區分區類別
	 */
	@Column(name = "SECTKIND")
	private String sectKind;

	/**
	 * 使用分區
	 */
	@Column(name = "USESECT")
	private String useSect;

	/**
	 * 用地類別
	 */
	@Column(name = "USEKIND")
	private String useKind;

	/**
	 * 檢核是否完畢
	 */
	@Column(name = "CHECKYN")
	private String checkYN;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;
	
	/** 所有權人註記 **/
	@Column(name = "ESTATEOWNER")
	private String estateOwner;
	
	/** J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容  **/
	/** 其他內容 **/
	@Column(name = "OTHERDESC", length = 100, columnDefinition = "VARCHAR(100)")
	private String otherDesc;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	public void setEstateType(String estateType) {
		this.estateType = estateType;
	}

	public String getEstateType() {
		return estateType;
	}

	public void setEstateSubType(String estateSubType) {
		this.estateSubType = estateSubType;
	}

	public String getEstateSubType() {
		return estateSubType;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getFlag() {
		return flag;
	}

	public void setEstateNote(String estateNote) {
		this.estateNote = estateNote;
	}

	public String getEstateNote() {
		return estateNote;
	}

	public String getEstateStatus() {
		return estateStatus;
	}

	public void setEstateStatus(String estateStatus) {
		this.estateStatus = estateStatus;
	}

	public String getEstateCityId() {
		return estateCityId;
	}

	public void setEstateCityId(String estateCityId) {
		this.estateCityId = estateCityId;
	}

	public String getEstateAreaId() {
		return estateAreaId;
	}

	public void setEstateAreaId(String estateAreaId) {
		this.estateAreaId = estateAreaId;
	}

	public String getEstateSit3No() {
		return estateSit3No;
	}

	public void setEstateSit3No(String estateSit3No) {
		this.estateSit3No = estateSit3No;
	}

	public String getEstateSit4No() {
		return estateSit4No;
	}

	public void setEstateSit4No(String estateSit4No) {
		this.estateSit4No = estateSit4No;
	}

	public String getBuildWay() {
		return buildWay;
	}

	public void setBuildWay(String buildWay) {
		this.buildWay = buildWay;
	}

	public String getmCntrNo() {
		return mCntrNo;
	}

	public void setmCntrNo(String mCntrNo) {
		this.mCntrNo = mCntrNo;
	}

	public BigDecimal getLandlordNum() {
		return landlordNum;
	}

	public void setLandlordNum(BigDecimal landlordNum) {
		this.landlordNum = landlordNum;
	}

	public String getSubTypeNote() {
		return subTypeNote;
	}

	public void setSubTypeNote(String subTypeNote) {
		this.subTypeNote = subTypeNote;
	}

	public void setCheckYN(String checkYN) {
		this.checkYN = checkYN;
	}

	public String getCheckYN() {
		return checkYN;
	}

	public void setIsTurnOver(String isTurnOver) {
		this.isTurnOver = isTurnOver;
	}

	public String getIsTurnOver() {
		return isTurnOver;
	}

	public void setOverDate(Date overDate) {
		this.overDate = overDate;
	}

	public Date getOverDate() {
		return overDate;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getPosition() {
		return position;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubjectCode(String subjectCode) {
		this.subjectCode = subjectCode;
	}

	public String getSubjectCode() {
		return subjectCode;
	}

	public void setSubjectKind(String subjectKind) {
		this.subjectKind = subjectKind;
	}

	public String getSubjectKind() {
		return subjectKind;
	}

	public void setSiteNote(String siteNote) {
		this.siteNote = siteNote;
	}

	public String getSiteNote() {
		return siteNote;
	}

	public void setSectKind(String sectKind) {
		this.sectKind = sectKind;
	}

	public String getSectKind() {
		return sectKind;
	}

	public void setUseSect(String useSect) {
		this.useSect = useSect;
	}

	public String getUseSect() {
		return useSect;
	}

	public void setUseKind(String useKind) {
		this.useKind = useKind;
	}

	public String getUseKind() {
		return useKind;
	}
	
	public void setEstateOwner(String estateOwner) {
		this.estateOwner = estateOwner;
	}

	public String getEstateOwner() {
		return estateOwner;
	}

	public String getOtherDesc() {
		return otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}
	
}
