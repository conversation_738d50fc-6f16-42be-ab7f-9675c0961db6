/* 
 * L120S10ADaoImpl
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S10ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L120S10A;

/** 微型企業明細檔 **/
@Repository
public class L120S10ADaoImpl extends LMSJpaDao<L120S10A, String> 
	implements L120S10ADao {

	@Override
	public L120S10A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S10A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("custRelation", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S10A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S10A> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S10A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			search.setMaxResults(Integer.MAX_VALUE);
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L120S10A> findByMainIdAndCustIdDupNo(String mainId,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S10A> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L120S10A> findByMainIdAndCustName(String mainId, 
			String custName) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custName", custName);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S10A> list = createQuery(search).getResultList();
		return list;
	}
}