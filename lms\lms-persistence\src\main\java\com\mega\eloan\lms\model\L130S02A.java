/* 
 * L130S02A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 異常通報表額度控管設定 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L130S02A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L130S02A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** **/
	@Column(name="SEQNO", length=3, columnDefinition="CHAR(3)")
	private String seqNo;
	
	/** 
	 * 控管種類<p/>
	 * 1-依 額度 控管<br/>
	 * 2-依 custIdDupNo 控管<br/>
	 * 3-依 集團 控管
	 */
	@Size(max=1)
	@Column(name="CTLTYPE", length=1, columnDefinition="CHAR(1)")
	private String ctlType;

	/** 
	 * 控管資料<p/>
	 * 1-額度:12碼<br/>
	 * 2-統編:11碼<br/>
	 * 3-集團: 4碼(上傳改成3碼)
	 */
	@Size(max=12)
	@Column(name="CTLITEM", length=12, columnDefinition="VARCHAR(12)")
	private String ctlItem;
	
	/** 
	 * 描述
	 */
	@Size(max=120)
	@Column(name="CTLNAME", length=120, columnDefinition="VARCHAR(120)")
	private String ctlName;

	/** 控管迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ENDDATE", columnDefinition="DATE")
	private Date endDate;
	
	/**
	 * 刪除註記日期
	 */
//	@Temporal(TemporalType.TIMESTAMP)
	@Column(columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;
	
	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得seqNo **/
	public String getSeqNo() {
		return this.seqNo;
	}
	/** 設定seqNo **/
	public void setSeqNo(String value) {
		this.seqNo = value;
	}
	
	/** 取得控管迄日 **/
	public Date getEndDate() {
		return this.endDate;
	}
	/** 設定控管迄日 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 
	 * 取得控管種類<p/>
	 * 依 額度 控管<br/>
	 *  依 custIdDupNo控管<br/>
	 *  依 集團 控管
	 */
	public String getCtlType() {
		return this.ctlType;
	}
	/**
	 *  設定控管種類<p/>
	 *  依 額度 控管<br/>
	 *  依 custIdDupNo控管<br/>
	 *  依 集團 控管
	 **/
	public void setCtlType(String value) {
		this.ctlType = value;
	}

	/** 
	 * 取得控管資料<p/>
	 * 額度:12碼<br/>
	 *  統編:11碼<br/>
	 *  集團: 4碼(上傳改成3碼)
	 */
	public String getCtlItem() {
		return this.ctlItem;
	}
	/**
	 *  設定控管資料<p/>
	 *  額度:12碼<br/>
	 *  統編:11碼<br/>
	 *  集團: 4碼(上傳改成3碼)
	 **/
	public void setCtlItem(String value) {
		this.ctlItem = value;
	}
	
	/** 
	 * 取得描述
	 */
	public String getCtlName() {
		return this.ctlName;
	}
	/**
	 *  設定描述
	 **/
	public void setCtlName(String value) {
		this.ctlName = value;
	}

	/** 取得刪除註記日期 **/
	public Timestamp getDeletedTime() {
		return deletedTime;
	}

	/** 設定刪除註記日期 **/
	public void setDeletedTime(Timestamp deletedTime) {
		this.deletedTime = deletedTime;
	}
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
