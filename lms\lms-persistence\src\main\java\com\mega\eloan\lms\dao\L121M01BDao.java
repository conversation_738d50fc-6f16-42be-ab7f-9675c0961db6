/* 
 * L121M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L121M01B;

/** 海外聯貸案會簽意見檔 **/
public interface L121M01BDao extends IGenericDao<L121M01B> {

	L121M01B findByOid(String oid);
	
	List<L121M01B> findByMainId(String mainId);
	
	L121M01B findByUniqueKey(String mainId, String itemType);

	List<L121M01B> findByIndex01(String mainId, String itemType);
}