<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
				<script type="text/javascript">
				loadScript('pagejs/cls/CLS8011M01Page');
				</script>
			<div class="button-menu funcContainer" id="buttonPanel">
				
			<!--編製中 -->
			<th:block th:if="${_btnDOC_EDITING_visible}">
        		<button id="btnSave"> 
        			<span class="ui-icon ui-icon-jcs-04" ></span>
        			<th:block th:text="#{'button.save'}">儲存</th:block>
        		</button>
				<button id="btnImpLatestItem">
        			<th:block th:text="#{'button.impLatestItem'}">引入最新項目</th:block>
        		</button>
				<button id="btnEditOK" >
        			<span class="ui-icon ui-icon-jcs-106" ></span>
        			<th:block th:text="#{'button.editOK'}">編製完成</th:block>
        		</button>
	        </th:block>
			<!--編製完成 -->
			<th:block th:if="${_btnDOC_APPROVE_visible}">
				<button id="btnToEdit" >
        			<th:block th:text="#{'button.toEdit'}">轉編製中</th:block>
        		</button>				
			</th:block>
			
			<!--共用部分 -->	
            <button id="btnPrint" class="forview">
            	<span class="ui-icon ui-icon-jcs-03"></span>
				<th:block th:text="#{'button.print'}">列印</th:block>
			</button>
            <button id="btnExit"  class="forview">
            	<span class="ui-icon ui-icon-jcs-01"></span>
				<th:block th:text="#{'button.exit'}">離開</th:block>
			</button>
				
            </div>
			<div class="tit2 color-black">
				<th:block th:text="#{'C801M01A.title01'}">個金個人資料清冊作業</th:block>：<span id="custInfo" class="color-blue" ></span>
			</div>
			
			<div class="tabs doc-tabs">
                <ul>
                	<li id="tabs_1" > <a href="#tab-01" goto="01"><b><th:block th:text="#{'doc.docinfo'}">文件資訊</th:block></b></a></li>
					<li id="tabs_2" > <a href="#tab-02" goto="02"><b><th:block th:text="#{'C801M01A.title04'}">申貸文件</th:block></b></a></li>
					<li id="tabs_3" > <a href="#tab-03" goto="03"><b><th:block th:text="#{'C801M01A.title05'}">徵信文件</th:block></b></a></li>
					<li id="tabs_4" > <a href="#tab-04" goto="04"><b><th:block th:text="#{'C801M01A.title06'}">授信文件</th:block></b></a></li>
				 </ul>
                <div class="tabCtx-warp">
                	<form id="tabForm">
                		<div th:id="${tabIdx}" th:insert="~{${panelName} :: ${panelFragmentName}}"></div>
					</form>	
				</div>
			</div>
		</th:block>
    </body>
</html>
