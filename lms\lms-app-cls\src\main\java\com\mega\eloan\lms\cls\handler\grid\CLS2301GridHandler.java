package com.mega.eloan.lms.cls.handler.grid;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS2301M01Page;
import com.mega.eloan.lms.cls.service.CLS2301Service;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L230S01A;
import com.mega.eloan.lms.model.VL230M01;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 簽約未動用授信案件送作業
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("cls2301gridhandler")
public class CLS2301GridHandler extends AbstractGridHandler {

	@Resource
	CLS2301Service cls2301Service;

	@Resource
	LMSService lmsService;
	@Resource
	UserInfoService userInfoService;

	@Resource
	CodeTypeService codeTypeService;

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL230M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l230a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, null);
		// 建立主要Search 條件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);

		/**
		 * CLS 個金<br/>
		 * LMS企金<br/>
		 * 空白 海外
		 */
		String rptId = UtilConstants.CaseSchema.個金;
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptId", rptId);
		// 排除掉海外授信案件
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		// 限定只顯示個金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docURL",
				CapWebUtil.getDocUrl(CLS2301M01Page.class));

		// 第三個參數為formatting
		Page<? extends GenericBean> page = cls2301Service.findPage(
				L230M01A.class, pageSetting);
		List<L230M01A> l230m01as = (List<L230M01A>) page.getContent();
		for (L230M01A l230m01a : l230m01as) {
			l230m01a.setCustId(StrUtils.concat(l230m01a.getCustId(), " ",
					l230m01a.getDupNo()));
			l230m01a.setApprId(this.getUserName(l230m01a.getApprId()));
			l230m01a.setCaseNo(Util.toSemiCharString(l230m01a.getCaseNo()));

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 按日期查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryByDateString(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 建立主要Search 條件
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String type = Util.nullToSpace(params.getString("type"));

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, docStatus);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l230a01a.authUnit", user.getUnitNo());
		/**
		 * CLS 個金<br/>
		 * LMS企金<br/>
		 * 空白 海外
		 */
		String rptId = UtilConstants.CaseSchema.個金;
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rptId", rptId);

		// 2012-09-06 黃建霖 begin
		String custId = Util.trim(params.getString("custId"));
		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		// 2012-09-06 黃建霖 end

		if (Util.isNotEmpty(params.getString("fromDate"))
				&& Util.isNotEmpty(params.getString("endDate"))) {

			// 狀態1 是用核准日期去查
			if ("1".equals(type)) {
				Date fromDate = Util.parseDate(Util.nullToSpace(params
						.getString("fromDate")));
				Date endDate = Util.parseDate(Util.nullToSpace(params
						.getString("endDate") + " 23:59:59"));
				Object[] reason = { fromDate, endDate };
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"approveTime", reason);
			} else if ("2".equals(type)) {

				Date fromDate = Util.parseDate(Util.nullToSpace(params
						.getString("fromDate")));
				Date endDate = Util.parseDate(Util.nullToSpace(params
						.getString("endDate") + " 23:59:59"));
				Object[] reason = { fromDate, endDate };
				pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
						"caseDate", reason);
			}
		}
		// 加入換頁條件
		Page<? extends GenericBean> page = cls2301Service.findPage(
				L230M01A.class, pageSetting);

		List<L230M01A> l230m01as = (List<L230M01A>) page.getContent();
		for (L230M01A l230m01a : l230m01as) {
			l230m01a.setCustId(StrUtils.concat(l230m01a.getCustId(), " ",
					l230m01a.getDupNo()));
			l230m01a.setApprId(this.getUserName(l230m01a.getApprId()));
			l230m01a.setCaseNo(Util.toSemiCharString(l230m01a.getCaseNo()));

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢已核准案件簽報書Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		// TODO 要修改只顯示國內個金
		String custId = Util.nullToSpace(params.getString("custId"))
				.toUpperCase();
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrid",
				user.getUnitNo());
		Page<? extends GenericBean> page = cls2301Service.findPage(
				VL230M01.class, pageSetting);
		List<VL230M01> vl230m01s = (List<VL230M01>) page.getContent();
		for (VL230M01 VL230M01 : vl230m01s) {
			// 設定顯示名稱 使用者id+重複序號+名稱
			VL230M01.setCustId(StrUtils.concat(VL230M01.getCustId(),
					VL230M01.getDupNo(), " ", VL230M01.getCustName()));
			VL230M01.setCaseNo(Util.toSemiCharString(VL230M01.getCaseNo()));
		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢L230S01A 額度資訊檔資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked" })
	public CapGridResult queryL230S01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = cls2301Service.findPage(
				L230S01A.class, pageSetting);
		List<L230S01A> l230s01as = (List<L230S01A>) page.getContent();

		Map<String, CapAjaxFormResult> ALLCodeMap = codeTypeService
				.findByCodeType(new String[] { "lms1405s02_proPerty",
						"lms2305s01_reason" });
		CapAjaxFormResult proPertyCode = ALLCodeMap.get("lms1405s02_proPerty");
		CapAjaxFormResult reasonCode = ALLCodeMap.get("lms2305s01_reason");
		String[] proPerty = null;
		StringBuffer temp = new StringBuffer();
		for (L230S01A l230s01a : l230s01as) {
			proPerty = Util.trim(l230s01a.getProPerty()).split(
					UtilConstants.Mark.SPILT_MARK);
			for (String value : proPerty) {
				temp.append(temp.length() > 0 ? UtilConstants.Mark.MARKDAN
						: UtilConstants.Mark.SPACE);
				temp.append(Util.trim(proPertyCode.get(value)));
			}
			l230s01a.setProPerty(temp.toString());
			temp.setLength(0);
			this.setReason(l230s01a, reasonCode);

		}
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		result.setDataReformatter(dataReformatter);
		dataReformatter.put("nuseMemo", new CodeTypeFormatter(codeTypeService,
				"lms2305m01_status")); // codeType格式化
		return result;
	}

	/**
	 * 設定不簽約原因
	 * 
	 * @param l230s01a
	 *            額度資訊檔
	 * @param reasonCode
	 *            不簽約原因 codeType
	 */
	private void setReason(L230S01A l230s01a, CapAjaxFormResult reasonCode) {
		String reason = Util.trim(l230s01a.getReason());
		String[] reasons = reason.split(UtilConstants.Mark.SPILT_MARK);
		StringBuffer temp = new StringBuffer();
		for (String key : reasons) {
			temp.append(temp.length() > 0 ? "<br/>" : "");
			temp.append(reasonCode.get(key));
		}
		if (reason.indexOf("99") > -1) {
			temp.append("<br/>");
			temp.append(l230s01a.getReasonDrc());
		}
		l230s01a.setReason(temp.toString());
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}
}
