var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();
initDfd.done(function(json){

	var grid_height = 270;
	var $grid_fcrdGrad = $("#grid_fcrdGrad").iGrid({
        handler: 'lms1810gridhandler',        
        height: grid_height,
        postData: {
        	fcrdType: trans_FcrdType_2_to_1($("#FcrdType").val()),
        	fcrdArea: $("#FcrdArea").val(),
        	fcrdPred: $("#FcrdPred").val(),
            formAction: "query_elfFcrdGrad"
        },
        needPager: false,        
        shrinkToFit: true,       
        colModel: [
          {//分行名稱
        	  colHeader: '評等等級', name: 'ratingGrad', sortable: false, align: "left" 
          }
        ],        
        ondblClickRow: function(rowid){
        	
        }        
    });
	
	//==============	
	hs_s01_CreditMowFcrdType();
	hs_s01_freeG_freeC();
	//==============
	
	//引進資料
	$("#btn_impData").click(function(){
		impData("01");
	});
	//引 負責人
	$("#btn_impChairman").click(function(){
		impData("02");
	});
	//引 0024行業別
	$("#btn_impTradeType").click(function(){
		impData("03");
	});
	//引 資信簡表 行業別
	$("#btn_impCesBizMode").click(function(){
		impData("04");
	});
	//引 符合授信額度標準
	$("#btn_impmLoanPerson").click(function(){
		impData("05");
	});
	//引 主要授信戶
	$("#btn_impmLoanPersonA").click(function(){
		impData("06");
	});
	//引 保證人
	$("#btn_impRltGuarantor").click(function(){
		impData("07");
	});
	//引 共同借款人
	$("#btn_impRltBorrower").click(function(){
		impData("08");
	});	
	//引 實地覆審註記 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	$("#btn_impRealCkData").click(function(){
		impData("10");
	});	
	//引 信評資料
	$("#btn_impCreditGrade").click(function(){
		
		var _id = "_div_choseGradeType";
		var _form = _id+"_form";
		var decisionElm = 'decision_choseGradeType';
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<div>請選擇信用評等種類</div>");
			dyna.push("<div>( 因 99.1.1 開始信用評等單軌作業，最多只能選一種評等 ) </div>");
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {
				  '1':'信用評等'
				, '2':'信用風險內部評等'
				, '3':'全部免辦'
				
				};
			build_submenu(dyna, decisionElm, submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({
	        title: '', width: 380, height: 250, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='"+decisionElm+"']:checked").val();
                    $.thickbox.close();
                    
                    if(val=="1"){//信用評等
                    	impData("09A");                    	
                    }else if(val=="2"){//信用風險內部評等
                    	impData("09B");
                    }else if(val=="3"){//全部免辦
                    	impData("09C");
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });	
	});	
	//引 外部評等
	$("#btn_fcrdGrad").click(function(){
		
		var fcrdType = trans_FcrdType_2_to_1($("#FcrdType").val());
		var fcrdArea = $("#FcrdArea").val()
		var fcrdPred = $("#FcrdPred").val();	
		if(fcrdType==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdType']+"不可空白");
			return;
		}
		if(fcrdArea==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdArea']+"不可空白");
			return;
		}
		if(fcrdPred==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdPred']+"不可空白");
			return;
		}
		if(fcrdType=="4" && fcrdArea=="1"){
			CommonAPI.showErrorMessage("中華信評-外部評等地區別錯誤(必須為本國)");
			return;
		}
		
		$grid_fcrdGrad.jqGrid("setGridParam", {
	    	postData : {
	    		'fcrdType': fcrdType,
	        	'fcrdArea': fcrdArea,
	        	'fcrdPred': fcrdPred,
	            formAction: "query_elfFcrdGrad"
	    	},
			search: true			
		}).trigger("reloadGrid");
		
		$("#_div_fcrdGrad").thickbox({
	        title: "",
	        width: 400,
            height: grid_height+140,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	 var data = $grid_fcrdGrad.getSingleData();
                     if (data) {
                    	 $("#FcrdGrad").val( data.ratingGrad ); 
    	                 $.thickbox.close();
                     }      
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	//引前次 外部評等
	$("#btn_exfcrdGrad").click(function(){
		
		var fcrdType = trans_FcrdType_2_to_1($("#exFcrdType").val());
		var fcrdArea = $("#exFcrdArea").val()
		var fcrdPred = $("#exFcrdPred").val();	
		if(fcrdType==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdType']+"不可空白");
			return;
		}
		if(fcrdArea==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdArea']+"不可空白");
			return;
		}
		if(fcrdPred==""){
			CommonAPI.showErrorMessage(i18n.lms1700m01['label.FcrdPred']+"不可空白");
			return;
		}
		if(fcrdType=="4" && fcrdArea=="1"){
			CommonAPI.showErrorMessage("中華信評-外部評等地區別錯誤(必須為本國)");
			return;
		}
		
		$grid_fcrdGrad.jqGrid("setGridParam", {
	    	postData : {
	    		'fcrdType': fcrdType,
	        	'fcrdArea': fcrdArea,
	        	'fcrdPred': fcrdPred,
	            formAction: "query_elfFcrdGrad"
	    	},
			search: true			
		}).trigger("reloadGrid");
		
		$("#_div_fcrdGrad").thickbox({
	        title: "",
	        width: 400,
            height: grid_height+140,
            align: "center",
            valign: "bottom",
            modal: false,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	 var data = $grid_fcrdGrad.getSingleData();
                     if (data) {
                    	 $("#exFcrdGrad").val( data.ratingGrad ); 
    	                 $.thickbox.close();
                     }      
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
	    });
	});
	
	$("#CreditType").change(function(){
		hs_CreditType();
	});
	$("#exCreditType").change(function(){
		hs_s01_Type($("#exCreditType"), $("#exCreditGrade"));
	});
	
	$("#MowType").change(function(){
		hs_MowType();
	});
	$("#exMowType").change(function(){
		hs_s01_Type($("#exMowType"), $("#exMowGrade"));
	});
	
	$("#FcrdType").change(function(){
		clear_FcrdGrad();
		
		hs_FcrdType();
	});
	$("#FcrdArea").change(function(){
		clear_FcrdGrad();
	});
	$("#FcrdPred").change(function(){
		clear_FcrdGrad();
	});
	$("#exFcrdType").change(function(){
		$("#exFcrdGrad").val("");
		
		hs_s01_Type("", "");
	});
	$("#exFcrdArea").change(function(){
		$("#exFcrdGrad").val("");
	});
	$("#exFcrdPred").change(function(){
		$("#exFcrdGrad").val("");
	});
	
	$("input[name=freeG]").change(function(){
		hs_freeG();
	});
	$("input[name=freeC]").change(function(){
		hs_freeC();
	});
	function impData(flag){
		var param = {
			 'flag':flag
			,'mainOid': $("#mainOid").val()
		};
		
		$.ajax({
			type: "POST",
			handler: _handler,
			data: $.extend( {formAction: "impData"}, param),
		}).done(function(json_impData){
			$("#tabForm").injectData(json_impData);

			if(flag=="01" || (flag=="09A"||flag=="09B"||flag=="09C") ){
				hs_s01_CreditMowFcrdType();
			}

			//更新 opener 的 Grid
			CommonAPI.triggerOpener("gridview", "reloadGrid");
		});
	}
	
	function build_submenu(dyna, rdoName, submenu){
		$.each(submenu, function(k, v) { 
			dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
        });		
	}
	
});

function trans_FcrdType_2_to_1(cardType){
	if(cardType=="NS"){
		return "1";
	}else if(cardType=="NM"){
		return "2";
	}else if(cardType=="NF"){
		return "3";
	}else if(cardType=="NC"){
		return "4";
	}else if(cardType=="NT"){
		//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		//FitchTW
		return "5";
	}else if(cardType=="NK"){
		//J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
		//KBRA
		return "6";	
	}else{
		return cardType;
	}
}