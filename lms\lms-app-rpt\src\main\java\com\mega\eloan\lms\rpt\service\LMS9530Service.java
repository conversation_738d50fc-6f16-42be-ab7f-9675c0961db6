/* 
 * LMS9515Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.service;

import java.util.List;

import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;

public interface LMS9530Service extends AbstractService {
	final String[] cols = { "oid", "uid", "mainId", "typCd", "custId", "dupNo",
			"custName", "unitType", "ownBrId", "docStatus", "randomCode",
			"docURL", "txCode", "creator", "createTime", "updater",
			"updaterNM", "updateTime", "approver", "approverNM", "approveTime",
			"isClosed", "deletedTime", "formType", "formName", "formText",
			"caseDate", "endDate", "docType", "docKind", "docCode", "caseYear",
			"caseBrId", "caseSeq", "caseNo", "authLvl", "caseLvl" };
	
	final String[] caseRS_cols = { "oid", "uid", "mainId", "typCd", "custId", "dupNo",
			"custName", "unitType", "ownBrId", "docStatus", "randomCode",
			"docURL", "txCode", "creator", "createTime", "updater",
			"updateTime", "approver",  "approveTime",
			"isClosed", "deletedTime", "caseDate", "endDate", "docType", "docKind", "docCode", "caseYear",
			"caseBrId", "caseSeq", "caseNo", "authLvl", "caseLvl" };
	/**
	 * 判斷該筆紀錄是否屬於該分行，且在授權內
	 * 
	 * @param String
	 * @return boolean
	 */
	public boolean inPremition(String mainId);

	public List<L120M01A> useL120M01A(ISearch search);
}
