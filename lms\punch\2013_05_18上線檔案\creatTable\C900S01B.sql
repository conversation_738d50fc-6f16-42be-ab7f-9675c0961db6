---------------------------------------------------------
-- LMS.C900S01B 檢附資訊檔
---------------------------------------------------------
---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.C900S01B;
CREATE TABLE LMS.C900S01B (
	OID           CHAR(32)      not null,
	ITEMCODE      VARCHAR(10)  ,
	ITEMSEQ       DECIMAL(5,0) ,
	ITEMTY<PERSON><PERSON>      CHAR(1)      ,
	ITEMCONTENT   VARCHAR(300) ,
	ITEMFORMAT    VARCHAR(300) ,
	CREATOR       CHAR(6)      ,
	CREATE<PERSON><PERSON>    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_C900S01B PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XC900S01B01;
--CREATE UNIQUE INDEX LMS.XC900S01B01 ON LMS.C900S01B   (OID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.C900S01B IS '檢附資訊檔';
COMMENT ON LMS.C900S01B (
	OID           IS 'oid', 
	ITEMCODE      IS '檢附資訊編號', 
	ITEMSEQ       IS '顯示順序', 
	ITEMTYPE      IS '查核項目類別', 
	ITEMCONTENT   IS '訊息內容', 
	ITEMFORMAT    IS '欄位顯示格式', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
