/* 
 * C101S01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01I;


/** 個金票信訊息紀錄表 **/
public interface C101S01IDao extends IGenericDao<C101S01I> {

	C101S01I findByOid(String oid);

	List<C101S01I> findByMainId(String mainId);

	/** 1個人可能會有2筆 C101S01I */
	@Deprecated
	C101S01I findByUniqueKey(String mainId, String custId, String dupNo);

	List<C101S01I> findByIndex01(String mainId, String custId, String dupNo);

	List<C101S01I> findByCustIdDupId(String custId,String DupNo);

	int deleteByOid(String oid);
}