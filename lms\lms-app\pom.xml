<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>lms</artifactId>
		<groupId>com.mega.eloan</groupId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>lms-app</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-persistence</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-config</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-gateway</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>
<!-- 		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-flow</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency> -->
<!-- 		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-service</artifactId>
			<version>1.0.0</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency> -->
		<dependency>
			<groupId>com.mega.eloan</groupId>
			<artifactId>lms-transform</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		
		<!-- web service使用 -->
		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
			<version>1.4</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis-jaxrpc</artifactId>
			<version>1.4</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
	      <groupId>wsdl4j</groupId>
	      <artifactId>wsdl4j</artifactId>
	      <version>1.6.2</version>
	      <scope>compile</scope>
	    </dependency>
	    <dependency>
	      <groupId>org.apache.axis</groupId>
	      <artifactId>axis-saaj</artifactId>
	      <version>1.4</version>
	      <scope>compile</scope>
	    </dependency>
		<dependency>
	      <groupId>commons-discovery</groupId>
	      <artifactId>commons-discovery</artifactId>
	      <version>0.2</version>
		  <scope>compile</scope>
	    </dependency>
        <!-- zip使用  解決ZIP檔內中文亂碼問題-->
		<dependency>
			<groupId>org.apache.ant</groupId>
			<artifactId>ant</artifactId>
			<version>1.8.2</version>
			<scope>compile</scope>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
			<version>5.3.39</version>
            <scope>test</scope>
        </dependency>
        <dependency>
			<groupId>net.sourceforge.jexcelapi</groupId>
			<artifactId>jxl</artifactId>
			<version>2.6.12</version>
        </dependency>
	</dependencies>
</project>