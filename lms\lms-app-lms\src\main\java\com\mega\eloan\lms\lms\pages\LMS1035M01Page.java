
package com.mega.eloan.lms.lms.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.OverSeaCLSOuterPanel;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ScoreServiceTH;
import com.mega.eloan.lms.lms.panels.LMS1035S01Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S02Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S03Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S04Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S05Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S06Panel;
import com.mega.eloan.lms.lms.panels.LMS1035S07Panel;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;


/**
 * <pre>
 * 泰國消金信用評等模型
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035m01/{page}")
public class LMS1035M01Page extends AbstractEloanForm {

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Autowired
	CLSService clsService;
	@Autowired
	ScoreServiceTH scoreServiceTH;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		// 依權限設定button
		addAclLabel(model,
				new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		addAclLabel(model, new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(), AuthType.Accept,
				CreditDocStatusEnum.海外_待覆核));
		
		C121M01A c121m01a = clsService.findC121M01A_oid(Util.trim(params.getString(EloanConstants.MAIN_OID)));
		
		if(c121m01a!=null && !Util.equals(c121m01a.getOwnBrId(), user.getUnitNo())){
			model.addAttribute("_btn_APPROVED", false);
		}else{
			addAclLabel(model, new AclLabel("_btn_APPROVED", params, getDomainClass(), AuthType.Modify,
					CreditDocStatusEnum.海外_已核准));
		}
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);	
		String mowTypeCountry = Util.trim(meta.getMowTypeCountry());
		String varVer = Util.trim(meta.getVarVer());
		if(Util.isEmpty(varVer)){ //無版本資訊，抓現行的版本資訊
			varVer = scoreServiceTH.get_Version_TH();
		}
		boolean showTab05 = true;
		boolean showTab06 = true;
		boolean title_1_0 = true;
		boolean title_2_0 = false;
		
		if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
			showTab06 = false;
			title_1_0 = false;
			title_2_0 = true;
		}
		if(Util.equals(mowTypeCountry, OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_越南) ){
			showTab05 = false;
		}
		
		
		//確認NBC Credit Report >> 是不是只有泰國有，越南待確認，若只有泰國有，要一併修改[基本資料]部分
		model.addAttribute("title_1_0", title_1_0);
		model.addAttribute("title_2_0", title_2_0);
		model.addAttribute("tab_NCBReport", showTab05);
		model.addAttribute("tab_Adjustment", showTab06);
		
		
		
		renderJsI18N(LMS1035M01Page.class);
		renderJsI18N(LMS1035V01Page.class);
		
		renderJsI18N(AbstractOverSeaCLSPage.class);
		model.addAttribute("_divOverSeaCLSPanel_visible", true);
		new OverSeaCLSOuterPanel("divOverSeaCLSPanel").processPanelData(model, params);
		
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		Panel panel = getPanel(page, varVer);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}

	// 頁籤
	public Panel getPanel(int index, String varVer) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new LMS1035S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new LMS1035S02Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new LMS1035S03Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new LMS1035S04Panel(TAB_CTX, true);
			break;
		case 5:
			panel = new LMS1035S05Panel(TAB_CTX, true, varVer);
			break;
		case 6:
			panel = new LMS1035S06Panel(TAB_CTX, true);
			break;
		case 7:
			panel = new LMS1035S07Panel(TAB_CTX, true);
			break;
		default:
			panel = new LMS1035S01Panel(TAB_CTX, true);
			break;
		}

		return panel;
	}

	public Class<? extends Meta> getDomainClass() {
		return C121M01A.class;
	}

}
