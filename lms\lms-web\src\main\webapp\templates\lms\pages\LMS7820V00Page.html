<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="innerPageBody">
       		<script type="text/javascript"> 
     			loadScript('pagejs/lms/LMS7820V00Page');
 			</script>
        	
            <div id="filterBox" style="display:none">
                <form id="filterForm">
                    <table class="tb2">
                        <tbody>
                            
                            <tr>
                                <td class="hd1">
                                    <th:block th:text="#{'l782m01a.releaseDate'}">
                                    	發文日期
                                    </th:block>：&nbsp;&nbsp;
                                </td>
                                <td>
                                    <input id="releaseDateS" name="releaseDateS" class="date required" type="text" size="8" maxlength="10" /><span class="text-red">～</span>
                                    <input id="releaseDateE" name="releaseDateE" class="date required" type="text" size="8" maxlength="10" /><span class="text-red">ex:YYYY-MM-DD</span>
                                </td>
                            </tr>
                            
                        </tbody>
                    </table>
                </form>
             </div> 
        </th:block>
    </body>
</html>