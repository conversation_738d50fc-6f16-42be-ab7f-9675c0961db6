/* 
 * PTEAMDTL.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.GenericBean;

import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 團貸額度明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="PTEAMDTL", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class PTEAMDTL extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 公司統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)" ,unique = true)
	private String custid;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 年度 **/
	@Size(max=3)
	@Column(name="YEAR", length=3, columnDefinition="CHAR(03)",unique = true)
	private String year;

	/** 額度申請序號 **/
	@Size(max=2)
	@Column(name="AMTAPPNO", length=2, columnDefinition="CHAR(02)",unique = true)
	private String amtappno;

	/** 申請批號 **/
	@Size(max=4)
	@Column(name="LOTNO", length=4, columnDefinition="CHAR(04)",unique = true)
	private String lotno;

	/** 申請額度 **/

	@Column(name="APPAMT", columnDefinition="DECIMAL(12)")
	private BigDecimal appamt;

	/** 核准額度 **/

	@Column(name="SECAMT", columnDefinition="DECIMAL(12)")
	private BigDecimal secamt;

	/** 核准動用日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="APRVDATE", columnDefinition="DATE")
	private Date aprvdate;

	/** 資料修改人 **/
	@Size(max=5)
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp tmestamp;

	/** 團貸編號 **/
	@Size(max=12)
	@Column(name="GRPCNTRNO", length=12, columnDefinition="CHAR(12)")
	private String grpcntrno;
	
	/** 取得公司統一編號 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定公司統一編號 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重覆序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重覆序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 取得年度 **/
	public String getYear() {
		return this.year;
	}
	/** 設定年度 **/
	public void setYear(String value) {
		this.year = value;
	}

	/** 取得額度申請序號 **/
	public String getAmtappno() {
		return this.amtappno;
	}
	/** 設定額度申請序號 **/
	public void setAmtappno(String value) {
		this.amtappno = value;
	}

	/** 取得申請批號 **/
	public String getLotno() {
		return this.lotno;
	}
	/** 設定申請批號 **/
	public void setLotno(String value) {
		this.lotno = value;
	}

	/** 取得申請額度 **/
	public BigDecimal getAppamt() {
		return this.appamt;
	}
	/** 設定申請額度 **/
	public void setAppamt(BigDecimal value) {
		this.appamt = value;
	}

	/** 取得核准額度 **/
	public BigDecimal getSecamt() {
		return this.secamt;
	}
	/** 設定核准額度 **/
	public void setSecamt(BigDecimal value) {
		this.secamt = value;
	}

	/** 取得核准動用日 **/
	public Date getAprvdate() {
		return this.aprvdate;
	}
	/** 設定核准動用日 **/
	public void setAprvdate(Date value) {
		this.aprvdate = value;
	}

	/** 取得資料修改人 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Timestamp getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Timestamp value) {
		this.tmestamp = value;
	}
	
	/** 取得團貸編號 **/
	public String getGrpcntrno() {
		return this.grpcntrno;
	}
	/** 設定團貸編號 **/
	public void setGrpcntrno(String value) {
		this.grpcntrno = value;
	}
}
