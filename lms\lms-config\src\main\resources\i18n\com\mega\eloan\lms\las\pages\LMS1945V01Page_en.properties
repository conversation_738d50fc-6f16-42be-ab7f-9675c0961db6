#==================================================
# \u7a3d\u6838\u5de5\u4f5c\u5e95\u7a3f
#==================================================
auditSheet=Worksheet
queryDialog.001=Please input search criteria
shttype.name1=Credit Process Audit Worksheet
shttype.name2=Mortgage Audit Worksheet
shttype.name3=Mass Market Audit Worksheet



lms1945.001=Inspected Unit
lms1945.002=Audit Date:
lms1945.003=Principal Borrower's UBN
lms1945.004=Customer Name
lms1945.005=Inspector
lms1945.006=Type
lms1945.007=Bank Statement
lms1945.008=Import Borrower
lms1945.009=Repeat Serial No.
lms1945.010=The customer has not made any applications or the outstanding balance is 0, hence no bank statements are needed
lms1945.011=Add Borrower
lms1945.012=Unified Serial Number (including repeated serial No.)
lms1945.013=Name
lms1945.014=Borrower's data not found
lms1945.015=Row {0}
lms1945.017=\u532f\u5165\u5718\u8cb8\u8cc7\u6599\u6a94
lms1945.018=\u8acb\u8f38\u5165\u5718\u8cb8\u7e3d\u6236ID
lms1945.019=\u5718\u8cb8\u7e3d\u6236\u5e8f\u865f
lms1945.020=\u540d\u7a31
lms1945.021=\u7d71\u7de8
lms1945.022=\u540d\u7a31
lms1945.023=\u9918\u984d
lms1945.024=\u8acb\u9078\u64c7\u4e00\u7b46\u5718\u8cb8\u7e3d\u6236\u5e8f\u865f
lms1945.025=\u7d71\u7de8
lms1945.026=\u540d\u7a31
lms1945.027=\u9918\u984d

lms1945.checkBase=Audit Record Date
lms1945.checkMan=Inspector
lms1945.checkDate=Audit Date:
lms1945.leader=Verify Name
lms1945.userItem=Add Audit Item
lms1945.userItem1=Add Audit Item (1)
lms1945.userItem2=Add Audit Item (2)
lms1945.userItem3=Add Audit Item (3)



lms1945.oid=oid
lms1945.uid=uid
lms1945.mainid=Document Number
lms1945.typcd=Region/Department
lms1945.custid=Unified Business Number
lms1945.dupno=Repeat Serial No.
lms1945.custname=Customer Name
lms1945.unittype=Handling Unit's Category
lms1945.ownbrid=Preparing Unit's ID
lms1945.docstatus=Document Status
lms1945.randomcode=Document Decode Error
lms1945.docurl=DocUrl
lms1945.docname=Document Name
lms1945.txcode=Transaction Code
lms1945.creator=Originator's ID
lms1945.createtime=Date Created
lms1945.updater=Modifier's ID
lms1945.updatetime=Date Of Change
lms1945.approver=Approver's ID
lms1945.approvetime=Approval date
lms1945.doctype=Corporate/Personal Banking Case
lms1945.dockind=Authority Type
lms1945.doccode=Case Type
lms1945.caseyear=Case No. - Year
lms1945.casebrid=Case No. - Branch
lms1945.caseseq=Case No. - Serial No.
lms1945.caseno=Case No.
lms1945.casedate=Signature Date
lms1945.authlvl=Authority Level
lms1945.areachk=Whether to circulate to joint reviewers
lms1945.areabrid=Joint Reviewer's ID
lms1945.caselvl=Case Approval Level
lms1945.docrslt=Final Approval Decision
lms1945.enddate=Date Of Final Approval Decision
lms1945.gist=Agenda
lms1945.itemofbusi=Main Business Activities
lms1945.cescustid=Business Overview - Borrower's UBN
lms1945.cesdupno=Business Overview - Borrower's Repeated Number
lms1945.longcaseflag=Whether a medium/long term case
lms1945.longcasedscr=Description Of Medium/Long Term Case
lms1945.cescase=This case does not require credit assessment
lms1945.grpid=Group ID
lms1945.meetingtype=Credit Review Committee/Collection Committee
lms1945.rpttitle1=Credit Review Committee/Collection Committee Meeting Session
lms1945.rpttitle2=Board Of Managing Directors Meeting Session
lms1945.areasendinfo=Regional Headquarter 's Release Time
lms1945.rpttitlearea1=Regional Headquarter's Credit Review Committee Meeting Session
lms1945.signno=Approval Reference No.
lms1945.sendfirst=Branch's First Submission
lms1945.sendfirsttime=Branch's First Submission Date
lms1945.sendlast=Branch's Last Submission
lms1945.sendlasttime=Branch's Last Submission Date
lms1945.rptid=rptid

lms1945.status=Document Information
lms1945.borrowdata=Borrower's Profile
lms1945.l140m01a=Credit Facility Report
lms1945.casebecause=Agenda
lms1945.say=Description
lms1945.other=Others
lms1945.lookall=Overall Evaluation/Banking Relationship
lms1945.relationshipdoc=Related Documents
lms1945.moresay=Supplementary Descriptions
lms1945.addfile=File Attachment
