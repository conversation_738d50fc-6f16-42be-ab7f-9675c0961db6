/* 
 * L120S04ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S04A;


/** 關係戶於本行各項業務往來檔 **/
public interface L120S04ADao extends IGenericDao<L120S04A> {

	L120S04A findByOid(String oid);
	
	List<L120S04A> findByMainId(String mainId);
	
	List<L120S04A> findByMainIdPrtFlag(String mainId,String prtFlag);
	
	List<Object[]> findL120s04a(String mainId);
	
	List<Object[]> findL120s04a2(String mainId);

	int delModel(String mainId);

	L120S04A findByUniqueKey(String mainId, String custId, String dupNo,
			String custName);
	
	List<L120S04A> findByCustIdDupId(String custId,String DupNo);
	
	List<L120S04A> findByMainIdKeyCustIdDupNo(String mainId, String keyCustId, String keyDupNo);
	List<L120S04A> findByMainIdKeyCustIdDupNoPrtFlag(String mainId, String keyCustId, String keyDupNo, String prtFlag);
	
	List<Object[]> findL120s04a_keyCustIdDupNo(String mainId, String keyCustId, String keyDupNo);
	
	List<Object[]> findL120s04a2_keyCustIdDupNo(String mainId, String keyCustId, String keyDupNo);
}