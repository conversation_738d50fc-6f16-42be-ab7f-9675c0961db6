/* 
 * L130M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 異常通報表事項檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L130M01B", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","branchKind"}))
public class L130M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 單位種類<p/>
	 * 1:分行(合併字串)<br/>
	 *  2:營運中心(合併字串)<br/>
	 *  3:授管處(合併字串)<br/>
	 *  4:授管處批覆意見(給分行看的)
	 */
	@Column(name="BRANCHKIND", length=1, columnDefinition="CHAR(1)")
	private String branchKind;

	/** 
	 * 事項內容<p/>
	 * 紀錄合併後的字串<br/>
	 *  約1024個中文字
	 */
	@Column(name="SEQDSCR", length=3072, columnDefinition="VARCHAR(3072)")
	private String seqDscr;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得單位種類<p/>
	 * 1:分行(合併字串)<br/>
	 *  2:營運中心(合併字串)<br/>
	 *  3:授管處(合併字串)<br/>
	 *  4:授管處批覆意見(給分行看的)
	 */
	public String getBranchKind() {
		return this.branchKind;
	}
	/**
	 *  設定單位種類<p/>
	 *  1:分行(合併字串)<br/>
	 *  2:營運中心(合併字串)<br/>
	 *  3:授管處(合併字串)<br/>
	 *  4:授管處批覆意見(給分行看的)
	 **/
	public void setBranchKind(String value) {
		this.branchKind = value;
	}

	/** 
	 * 取得事項內容<p/>
	 * 紀錄合併後的字串<br/>
	 *  約1024個中文字
	 */
	public String getSeqDscr() {
		return this.seqDscr;
	}
	/**
	 *  設定事項內容<p/>
	 *  紀錄合併後的字串<br/>
	 *  約1024個中文字
	 **/
	public void setSeqDscr(String value) {
		this.seqDscr = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
