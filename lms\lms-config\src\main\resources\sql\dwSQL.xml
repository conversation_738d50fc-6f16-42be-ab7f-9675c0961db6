<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
 xsi:schemaLocation="
 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd ">

<util:map id="dwSql" map-class="java.util.HashMap" key-type="java.lang.String">
	
	
    <!-- ########## -->
    <!-- 授信共用 -->
    <!-- ########## -->
 
 	<!-- 取得最新結帳匯率 -->
	<entry key="DW_FXRTHOVS.selRate">
        <value>
			SELECT AGNT_TWD_RT FROM DWADM.DW_FXRTHOVS 
			WHERE BR_CD=? AND HOME_FG='Y' ORDER BY DT DESC
        </value>
    </entry>    
	
	<entry key="DW_FXRTH.selLatestRate">
        <value>
			SELECT * FROM DWADM.DW_FXRTH WHERE DATA_DT in (select MAX(DATA_DT) from DWADM.DW_FXRTH) 
        </value>
    </entry>

    <!-- 海外客戶查詢(一年內) -->
	<!-- J-111-0252 多join 額度匯集檔(lncntrovs)，以避免在沒有帳務時撈不到額度序號 -->
    <entry key="DWADM.OTS_ASLNDNEWOVS.selectByBrNoCustId">
        <value>
			SELECT * FROM (
				SELECT FACT_KEY_ALN AS CONTRACT, REF_NO AS LOAN_NO
				FROM DWADM.OTS_ASLNDNEWOVS
				WHERE CYC_DT = (SELECT MAX(CYC_DT) FROM DWADM.OTS_ASLNDNEWOVS)
					AND BR_CD = ? AND CUST_KEY = ?
				UNION
				SELECT LINE_NO AS CONTRACT, '''' AS LOAN_NO
				FROM DWADM.DW_LNCNTROVS
				WHERE BR_CD = ? AND CUST_KEY = ?
			) {0}
        </value>
    </entry>
    
    <!-- ########## -->
    <!-- 徵信相關(from CES.xxxx) -->
    <!-- ########## -->
    <entry key="DW_LNMRATEOVS.findPrevDayCrdDetailStep2">
        <value><![CDATA[SELECT  USED_RATEY FROM  dwadm.DW_LNMRATEOVS WHERE CUST_KEY =? AND LINE_NO =?  AND (CYC_MN >= ?  and  CYC_MN <= ?) ]]></value>
    </entry>    
    <entry key="ASLNAVGOVS_SS.findPrevDayCrdDetailStep1">
        <value><![CDATA[SELECT LND.FACT_CONTR, LND.FACT_CUR_CD, (LND.FACT_AMT)/1000 as FACT_AMT, AC.CNM_SHORT, SUM(LND.LN_BAL_NT)/1000 as BAL
		FROM DWADM.DW_ASLNDAVGOVS LND 
		  LEFT OUTER JOIN DWADM.DW_CNGLACNO AC ON LND.GL_AC_KEY = AC.GL_AC_KEY
		WHERE LND.CUST_KEY = ? AND LND.CANCEL_DT ='0001-01-01' GROUP BY LND.FACT_CONTR, AC.CNM_SHORT, LND.FACT_CUR_CD, LND.FACT_AMT  ]]></value>
    </entry> 
    <entry key="DW_LNMRATEOVS.findPrevDayCrdDetailStep2">
        <value><![CDATA[SELECT  USED_RATEY FROM  dwadm.DW_LNMRATEOVS WHERE CUST_KEY =? AND LINE_NO =?  AND (CYC_MN >= ?  and  CYC_MN <= ?) ]]></value>
    </entry>
    <!-- ########## -->
    <!-- 擔保品相關(from CMS.xxxx) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(簽報書) -->
    <!-- 個金授信(簽報書) -->
    <!-- ########## -->
	<!-- 查詢國外餘額日期  -->
    <entry key="DWLNQUOTOV.selLoan_date">
        <value>
        	<!--
            SELECT CONTRACT, FACT_DATE, FACT_SWFT, FACT_AMT, FACT_AMT_T, LOAN_DATE, LOAN_BAL_S, LOAN_BAL_N
 					FROM DWADM.DW_LNQUOTOV WHERE CUST_KEY=? AND CONTRACT=?
			-->	
			SELECT LINE_NO AS CONTRACT,FACT_DT AS FACT_DATE,CUR_CD AS FACT_SWFT,ADJ_FAMT AS FACT_AMT,
			ADJ_FAMT_T AS FACT_AMT_T, LOAN_DT AS LOAN_DATE, LOAN_BAL_TS AS LOAN_BAL_S, LOAN_BAL_TN AS LOAN_BAL_N
			FROM DWADM.DW_LNCNTROVS WHERE  CUST_KEY = ?	AND LINE_NO = ? 
        </value>
    </entry>	
     <!-- 刪除上傳授信報案考核表標頭檔  -->
    <entry key="DWADM.DW_ELINSPSC.delete">
        <value>
            DELETE FROM DWADM.DW_ELINSPSC WHERE UNID=? 
        </value>
    </entry>
	<!--上傳授信報案考核表標頭檔-->
    <entry key="DWADM.DW_ELINSPSC.insert">
        <value>
			INSERT INTO DWADM.DW_ELINSPSC
			(TYPCD, BRANCHID, CHKYM, UNID, APPRID, CHKID, 
			CHKUNIT, CUSTID, DUPNO, PROJNO, CURR, LOANAMT, 
			CASEAMT, TMGRADE, TMESTAMP, SYSTYPE) 
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT TIMESTAMP, ?)
        </value>
    </entry>	 		
     <!-- 刪除上傳授信報案考核表子檔  -->
    <entry key="DWADM.DW_ELINSPDT.delete">
        <value>
            DELETE FROM DWADM.DW_ELINSPDT WHERE UNID=?
        </value>
    </entry>
	<!--上傳授信報案考核表子檔-->
    <entry key="DWADM.DW_ELINSPDT.insert">
        <value>
			INSERT INTO DWADM.DW_ELINSPDT
			(CHKYM, UNID, ITEMNO, ITEMSCOR, ITEMCNT, 
			ITEMALL, ITEMMEMO, SYSTYPE, TMESTAMP) 
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT TIMESTAMP)
        </value>
    </entry>	
     <!-- 查詢DW有效資料範圍日期  -->
    <entry key="DWADM_MD_CUPFM_OTS.selDate">
        <value>
        	<!-- 
            SELECT CHAR(MAX(MIN_CYC_MN)) AS MIN_CYC_MN,CHAR(MAX(MAX_CYC_MN)) AS MAX_CYC_MN
			FROM
			(
			  (SELECT MIN(CYC_MN) AS MIN_CYC_MN,'0001-01-01' AS MAX_CYC_MN  FROM DWADM.MD_CUPFM_OTS)
			  UNION ALL
			  (SELECT  '0001-01-01' AS MIN_CYC_MN,MAX(CYC_MN) AS MAX_CYC_MN FROM DWADM.MD_CUPFM_OTS
			  where  CYC_MN > current date - 3 months )
			) AS T1
			-->
			
			SELECT CHAR(MAX(MIN_CYC_MN)) AS MIN_CYC_MN,CHAR(MAX(MAX_CYC_MN)) AS MAX_CYC_MN
			FROM
			(
			  (SELECT MIN(CYC_MN) AS MIN_CYC_MN,'0001-01-01' AS MAX_CYC_MN  FROM DWADM.MD_CUPFM_OTS)
			  UNION ALL
			  (SELECT  '0001-01-01' AS MIN_CYC_MN,MAX(CYC_MN) AS MAX_CYC_MN FROM DWADM.MD_CUPFM_OTS
			  WHERE CUST_KEY = '9999999999' and BR_CD = '999' )
			) AS T1
			
        </value>
    </entry>
	<!-- 查詢DW最近半年之資料日期  -->
    <entry key="DWADM_MD_CUPFM_OTS.selDate2">
        <value>
			SELECT CHAR( CYC_MN ) AS MAX_CYC_MN FROM DWADM.MD_CUPFM_OTS WHERE CYC_MN BETWEEN ? AND ? ORDER BY CYC_MN DESC
        </value>
    </entry>	
    <!-- 	查詢同一關係企業/集團企業之金融商品金額與曝險總額(延續查詢) -->
    <entry key="DWADMDW_ROCLIST_ECUS.selDW_ROCLIST_ECUS_Main3">
        <value>
            SELECT CUST_ID,CUST_ID_DUP, CHAR(CYC_MN_ROC) AS CYC_MN_ROC, LN_BAL ,
            LONG_BAL,CHAR(CYC_MN_FIN) AS CYC_MN_FIN ,FIN_NS,FIN_MM,FIN_ST,FIN_AR,FIN_DAMT,
            (SELECT (COALESCE(FIN_NS,0) + COALESCE(FIN_MM,0) + COALESCE(FIN_ST,0) + COALESCE(FIN_AR,0) + COALESCE(FIN_DAMT,0)) FROM DWADM.DW_ROCLIST_ECUS WHERE CUST_ID = ? AND CUST_ID_DUP = ? ) gpComAmt,
            (SELECT (COALESCE(FIN_NS,0) + COALESCE(FIN_MM,0) + COALESCE(FIN_ST,0) + COALESCE(FIN_AR,0) + COALESCE(FIN_DAMT,0) + COALESCE(LN_BAL,0) + COALESCE(LONG_BAL,0)) FROM DWADM.DW_ROCLIST_ECUS WHERE CUST_ID = ? AND CUST_ID_DUP = ? ) gpRiskAmt
            FROM DWADM.DW_ROCLIST_ECUS WHERE CUST_ID = ?
            AND CUST_ID_DUP = ?
        </value>
    </entry>
    <!-- 讀取外匯相關資料  -->
    <entry key="MD_CUPFM_OTS.selCYC_MN">
        <value>
        	<!--
            SELECT  * FROM DWADM.MD_CUPFM_OTS WHERE CUST_KEY= ? AND  CYC_MN BETWEEN ? AND ?  
			UNION
			SELECT  * FROM DWADM.MD_CUPFM_OVS WHERE CUST_KEY= ? AND CYC_MN BETWEEN ? AND ? 
			ORDER BY CYC_MN DESC
			-->
			WITH v1 as
			(
				SELECT
					 src.CYC_MN                                                                as CYC_MN
				    ,case when substr(src.CUST_KEY, 1,1) between '0' and '9' and
					           substr(src.CUST_KEY, 2,1) between '0' and '9' and
					           substr(src.CUST_KEY, 3,1) between '0' and '9' and
					           substr(src.CUST_KEY, 4,1) between '0' and '9' and
					           substr(src.CUST_KEY, 5,1) between '0' and '9' and
					           substr(src.CUST_KEY, 6,1) between '0' and '9' and
					           substr(src.CUST_KEY, 7,1) between '0' and '9' and
					           substr(src.CUST_KEY, 8,1) between '0' and '9' and
					           substr(src.CUST_KEY, 9,1) = ' ' and
					           substr(src.CUST_KEY,10,1) = ' ' then substr(src.CUST_KEY,1,10)
					      else substr(src.CUST_KEY,1,10)
					 end                                                                       as CUST_KEY
					,src.BR_CD                                                                 as BR_CD
					,src.IN_DP_1 + src.IN_DP_2 + src.IN_CK                                     as IN_DP
					,src.IN_DP_3                                                               as IN_DP_G
					,src.IN_CT_1 + src.IN_CT_2                                                 as IN_CT
					,src.IN_LN_USE                                                             as IN_LN_USE
					,src.IN_LN_AVGRT                                                           as IN_LN_AVGRT
					,src.IN_LN_FACT_AMT                                                        as IN_LN_FACT_AMT
					,src.IN_LN_FA_S                                                            as IN_LN_FA_S
					,src.IN_LN_FA_B                                                            as IN_LN_FA_B					
					,src.IN_LN_FACT_AMT_FA_S                                                   as IN_LN_FACT_AMT_FA_S
					,src.IN_LN_FACT_AMT_FA_B                                                   as IN_LN_FACT_AMT_FA_B
					,src.IN_LN_AVGRT_FA_S                                                      as IN_LN_AVGRT_FA_S
					,src.IN_LN_AVGRT_FA_B                                                      as IN_LN_AVGRT_FA_B					
					,src.IN_IM_LC + src.IN_IM_IC + src.IN_IM_FS + src.IN_IM_TT                 as IN_IM
					,src.IN_EX_BP + src.IN_EX_EC + src.IN_EX_FB + src.IN_EX_BB  + src.IN_EX_AP as IN_EX_BP
					,src.IN_IM_LC_TXN + src.IN_IM_IC_TXN + src.IN_IM_FS_TXN + src.IN_IM_TT_TXN as IN_IM_TXN
					,src.IN_EX_BP_TXN + src.IN_EX_EC_TXN + src.IN_EX_FB_TXN + src.IN_EX_BB_TXN + src.IN_EX_AP_TXN as IN_EX_TXN
					,src.IN_OR_FX                                                              as IN_OR
					,src.IN_IR_IN + src.IN_IR_CB + src.IN_IR_CH                                as IN_IR
					,src.IN_OR_FX_TXN                                                          as IN_OR_TXN
					,src.IN_IR_IN_TXN +src.IN_IR_CB_TXN + src.IN_IR_CH_TXN                     as IN_IR_TXN
					,src.IN_DV_OP                                                              as IN_DV_OP
					,src.IN_DV_RE                                                              as IN_DV_RE
					,src.IN_DV_ER                                                              as IN_DV_ER
					,src.IN_DV_FR                                                              as IN_DV_FR
					,src.IN_WM_F_FEE                                                           as IN_WM_F_FEE
					,src.IN_WM_I_FEE                                                           as IN_WM_I_FEE
					,src.IN_WM_S_FEE                                                           as IN_WM_S_FEE
					,src.IN_TR_FU                                                              as IN_TR_FU
					,src.IN_TR_CF                                                              as IN_TR_CF
					,src.IN_TR_SC_E + src.IN_TR_SC_D1 + src.IN_TR_SC_D2                        as IN_TR_SC
					,src.IN_TR_TA                                                              as IN_TR_OTS
					<!--,src.IN_TR_OTS                                                         as IN_TR_OTS 2015-02-11 其他信託改以信託專戶取代M-104-00028-->
					,src.IN_CC_CC                                                              as IN_CC_CC
					,src.IN_CC_IV                                                              as IN_CC_IV
					,value(src.IN_CC_CC_CN,0)                                                  as IN_CC_CC_CN
					,value(src.IN_CC_JC_CN,0)                                                  as IN_CC_JC_CN
					,src.IN_ST                                                                 as IN_ST
					,src.IN_ST_FD                                                              as IN_ST_FD
					,src.IN_ST_LN_1                                                            as IN_ST_LN_1
					,src.IN_ST_LN_2                                                            as IN_ST_LN_2
					,src.IN_ST_CC                                                              as IN_ST_CC
					,src.IN_ST_NB                                                              as IN_ST_NB
					,src.IN_GEB_NTD_TXN                                                        as IN_GEB_NTD_TXN
					,src.IN_GEB_NTD_N_TXN                                                      as IN_GEB_NTD_N_TXN
					,src.IN_GEB_LC_TXN                                                         as IN_GEB_LC_TXN
					,src.IN_WM_FD                                                              as IN_WM_FD
                    ,src.IN_WM_IA                                                              as IN_WM_IA
                    ,src.IN_WM_STD                                                             as IN_WM_STD
                    ,src.IN_WM_BAL                                                             as IN_WM_BAL
                    ,src.IN_WM_FEE                                                             as IN_WM_FEE
                    ,src.IN_TR_SC_E                                                            as IN_TR_SC_E
				FROM DWADM.MD_CUPFM_OTS as src
				WHERE 
				( (substr(src.BR_CD,1,1) between '0' and '2') or src.BR_CD = '999' )
				AND not ( (substr(src.BR_CD,2,1) between 'A' and 'Z') or (substr(src.BR_CD,1,1) in ('Y','Z')) )
				AND CUST_KEY like ?
			  AND CYC_MN BETWEEN ? AND ?
			)
			,v2 as
			(
				SELECT
					 src.CYC_MN                                                                as CYC_MN
				    ,case when substr(src.CUST_KEY, 1,1) between '0' and '9' and
					           substr(src.CUST_KEY, 2,1) between '0' and '9' and
					           substr(src.CUST_KEY, 3,1) between '0' and '9' and
					           substr(src.CUST_KEY, 4,1) between '0' and '9' and
					           substr(src.CUST_KEY, 5,1) between '0' and '9' and
					           substr(src.CUST_KEY, 6,1) between '0' and '9' and
					           substr(src.CUST_KEY, 7,1) between '0' and '9' and
					           substr(src.CUST_KEY, 8,1) between '0' and '9' and
					           substr(src.CUST_KEY, 9,1) = ' ' and
					           substr(src.CUST_KEY,10,1) = ' ' then substr(src.CUST_KEY,1,10)
					      else substr(src.CUST_KEY,1,10)
					 end                                                                       as CUST_KEY
					,src.BR_CD                                                                 as BR_CD
					,src.IN_DP_1 + src.IN_DP_2 + src.IN_CK                                     as IN_DP
					,src.IN_DP_3                                                               as IN_DP_G
					,src.IN_CT_1 + src.IN_CT_2                                                 as IN_CT
					,src.IN_LN_USE                                                             as IN_LN_USE
					,src.IN_LN_AVGRT                                                           as IN_LN_AVGRT
					,src.IN_LN_FACT_AMT                                                        as IN_LN_FACT_AMT
					,src.IN_LN_FA_S                                                            as IN_LN_FA_S
					,src.IN_LN_FA_B                                                            as IN_LN_FA_B					
					,src.IN_LN_FACT_AMT_FA_S                                                   as IN_LN_FACT_AMT_FA_S
					,src.IN_LN_FACT_AMT_FA_B                                                   as IN_LN_FACT_AMT_FA_B
					,src.IN_LN_AVGRT_FA_S                                                      as IN_LN_AVGRT_FA_S
					,src.IN_LN_AVGRT_FA_B                                                      as IN_LN_AVGRT_FA_B				
					,src.IN_IM_LC + src.IN_IM_IC + src.IN_IM_FS + src.IN_IM_TT                 as IN_IM
					,src.IN_EX_BP + src.IN_EX_EC + src.IN_EX_FB + src.IN_EX_BB + src.IN_EX_AP  as IN_EX_BP
					,src.IN_IM_LC_TXN + src.IN_IM_IC_TXN + src.IN_IM_FS_TXN + src.IN_IM_TT_TXN as IN_IM_TXN
					,src.IN_EX_BP_TXN + src.IN_EX_EC_TXN + src.IN_EX_FB_TXN + src.IN_EX_BB_TXN + src.IN_EX_AP_TXN as IN_EX_TXN
					,src.IN_OR_FX                                                              as IN_OR
					,src.IN_IR_IN + src.IN_IR_CB + src.IN_IR_CH                                as IN_IR
					,src.IN_OR_FX_TXN                                                          as IN_OR_TXN
					,src.IN_IR_IN_TXN +src.IN_IR_CB_TXN + src.IN_IR_CH_TXN                     as IN_IR_TXN
					,src.IN_DV_OP                                                              as IN_DV_OP
					,src.IN_DV_RE                                                              as IN_DV_RE
					,src.IN_DV_ER                                                              as IN_DV_ER
					,src.IN_DV_FR                                                              as IN_DV_FR
					,src.IN_WM_F_FEE                                                           as IN_WM_F_FEE
					,src.IN_WM_I_FEE                                                           as IN_WM_I_FEE
					,src.IN_WM_S_FEE                                                           as IN_WM_S_FEE
					,src.IN_TR_FU                                                              as IN_TR_FU
					,src.IN_TR_CF                                                              as IN_TR_CF
					,src.IN_TR_SC_E + src.IN_TR_SC_D1 + src.IN_TR_SC_D2                        as IN_TR_SC
					,src.IN_TR_TA                                                              as IN_TR_OTS
					<!--,src.IN_TR_OTS                                                         as IN_TR_OTS 2015-02-11 其他信託改以信託專戶取代M-104-00028-->
					,src.IN_CC_CC                                                              as IN_CC_CC
					,src.IN_CC_IV                                                              as IN_CC_IV
					,value(src.IN_CC_CC_CN,0)                                                  as IN_CC_CC_CN
					,value(src.IN_CC_JC_CN,0)                                                  as IN_CC_JC_CN
					,src.IN_ST                                                                 as IN_ST
					,src.IN_ST_FD                                                              as IN_ST_FD
					,src.IN_ST_LN_1                                                            as IN_ST_LN_1
					,src.IN_ST_LN_2                                                            as IN_ST_LN_2
					,src.IN_ST_CC                                                              as IN_ST_CC
					,src.IN_ST_NB                                                              as IN_ST_NB
					,src.IN_GEB_NTD_TXN                                                        as IN_GEB_NTD_TXN
					,src.IN_GEB_NTD_N_TXN                                                      as IN_GEB_NTD_N_TXN
					,src.IN_GEB_LC_TXN                                                         as IN_GEB_LC_TXN
					,src.IN_WM_FD                                                              as IN_WM_FD
                    ,src.IN_WM_IA                                                              as IN_WM_IA
                    ,src.IN_WM_STD                                                             as IN_WM_STD
                    ,src.IN_WM_BAL                                                             as IN_WM_BAL
                    ,src.IN_WM_FEE                                                             as IN_WM_FEE
                    ,src.IN_TR_SC_E                                                            as IN_TR_SC_E
				FROM DWADM.MD_CUPFM_OVS as src
				WHERE
				(
					(substr(src.BR_CD,1,1) = '0' and substr(src.BR_CD,2,1) between 'A' and 'Z')
					or
					(substr(src.BR_CD,1,1) in ('Y','Z'))
				)
				AND CUST_KEY like ?
			    AND CYC_MN BETWEEN ? AND ?
			)
			
			SELECT
					 src.CYC_MN                                           as CYC_MN
					,src.CUST_KEY                                         as CUST_KEY
					,src.BR_CD                                            as BR_CD
					,sum(src.IN_DP         )                              as IN_DP
					,sum(src.IN_DP_G       )                              as IN_DP_G
					,sum(src.IN_CT         )                              as IN_CT
					,sum(src.IN_LN_USE     )                              as IN_LN_USE
					,sum(src.IN_LN_AVGRT   )                              as IN_LN_AVGRT
					,sum(src.IN_LN_FACT_AMT)                              as IN_LN_FACT_AMT
					,sum(src.IN_LN_FA_S    )                              as IN_LN_FA_S
					,sum(src.IN_LN_FA_B    )                              as IN_LN_FA_B					
					,sum(src.IN_LN_FACT_AMT_FA_S )                        as IN_LN_FACT_AMT_FA_S
					,sum(src.IN_LN_FACT_AMT_FA_B )                        as IN_LN_FACT_AMT_FA_B
					,sum(src.IN_LN_AVGRT_FA_S    )                        as IN_LN_AVGRT_FA_S
					,sum(src.IN_LN_AVGRT_FA_B    )                        as IN_LN_AVGRT_FA_B				
					,sum(src.IN_IM         )                              as IN_IM
					,sum(src.IN_EX_BP      )                              as IN_EX_BP
					,sum(src.IN_IM_TXN     )                              as IN_IM_TXN
					,sum(src.IN_EX_TXN     )                              as IN_EX_TXN
					,sum(src.IN_OR         )                              as IN_OR
					,sum(src.IN_IR         )                              as IN_IR
					,sum(src.IN_OR_TXN     )                              as IN_OR_TXN
					,sum(src.IN_IR_TXN     )                              as IN_IR_TXN
					,sum(src.IN_DV_OP      )                              as IN_DV_OP
					,sum(src.IN_DV_RE      )                              as IN_DV_RE
					,sum(src.IN_DV_ER      )                              as IN_DV_ER
					,sum(src.IN_DV_FR      )                              as IN_DV_FR
					,sum(src.IN_WM_F_FEE   )                              as IN_WM_F_FEE
					,sum(src.IN_WM_I_FEE   )                              as IN_WM_I_FEE
					,sum(src.IN_WM_S_FEE   )                              as IN_WM_S_FEE
					,sum(src.IN_TR_FU      )                              as IN_TR_FU
					,sum(src.IN_TR_CF      )                              as IN_TR_CF
					,sum(src.IN_TR_SC      )                              as IN_TR_SC
					,sum(src.IN_TR_OTS     )                              as IN_TR_OTS
					<!--,sum(src.IN_TR_OTS     )                          as IN_TR_OTS 2015-02-11 其他信託改以信託專戶取代M-104-00028-->
					,sum(src.IN_CC_CC      )                              as IN_CC_CC
					,sum(src.IN_CC_IV      )                              as IN_CC_IV
					,case when sum(IN_CC_CC_CN) > 0 then 'Y' else 'N' end as IN_CC_CC_ACT
					,case when sum(IN_CC_JC_CN) > 0 then 'Y' else 'N' end as IN_CC_JC_ACT
					,sum(src.IN_ST         )                              as IN_ST
					,sum(src.IN_ST_FD      )                              as IN_ST_FD
					,sum(src.IN_ST_LN_1    )                              as IN_ST_LN_1
					,sum(src.IN_ST_LN_2    )                              as IN_ST_LN_2
					,sum(src.IN_ST_CC      )                              as IN_ST_CC
					,sum(src.IN_ST_NB      )                              as IN_ST_NB
					,sum(src.IN_GEB_NTD_TXN    )                          as IN_GEB_NTD_TXN
					,sum(src.IN_GEB_NTD_N_TXN  )                          as IN_GEB_NTD_N_TXN
					,sum(src.IN_GEB_LC_TXN     )                          as IN_GEB_LC_TXN
					,sum(src.IN_WM_FD          )                          as IN_WM_FD
                    ,sum(src.IN_WM_IA          )                          as IN_WM_IA
                    ,sum(src.IN_WM_STD         )                          as IN_WM_STD
                    ,sum(src.IN_WM_BAL         )                          as IN_WM_BAL
                    ,sum(src.IN_WM_FEE         )                          as IN_WM_FEE
                    ,sum(src.IN_TR_SC_E        )                          as IN_TR_SC_E
				FROM v1 as src
				WHERE CUST_KEY like ?
				GROUP BY src.CYC_MN, src.CUST_KEY, src.BR_CD				
				UNION ALL
				SELECT
					 src.CYC_MN                                           as CYC_MN
					,src.CUST_KEY                                         as CUST_KEY
					,src.BR_CD                                            as BR_CD
					,sum(src.IN_DP         )                              as IN_DP
					,sum(src.IN_DP_G       )                              as IN_DP_G
					,sum(src.IN_CT         )                              as IN_CT
					,sum(src.IN_LN_USE     )                              as IN_LN_USE
					,sum(src.IN_LN_AVGRT   )                              as IN_LN_AVGRT
					,sum(src.IN_LN_FACT_AMT)                              as IN_LN_FACT_AMT
					,sum(src.IN_LN_FA_S    )                              as IN_LN_FA_S
					,sum(src.IN_LN_FA_B    )                              as IN_LN_FA_B
					,sum(src.IN_LN_FACT_AMT_FA_S )                        as IN_LN_FACT_AMT_FA_S
					,sum(src.IN_LN_FACT_AMT_FA_B )                        as IN_LN_FACT_AMT_FA_B
					,sum(src.IN_LN_AVGRT_FA_S    )                        as IN_LN_AVGRT_FA_S
					,sum(src.IN_LN_AVGRT_FA_B    )                        as IN_LN_AVGRT_FA_B
					,sum(src.IN_IM         )                              as IN_IM
					,sum(src.IN_EX_BP      )                              as IN_EX_BP
					,sum(src.IN_IM_TXN     )                              as IN_IM_TXN
					,sum(src.IN_EX_TXN     )                              as IN_EX_TXN
					,sum(src.IN_OR         )                              as IN_OR
					,sum(src.IN_IR         )                              as IN_IR
					,sum(src.IN_OR_TXN     )                              as IN_OR_TXN
					,sum(src.IN_IR_TXN     )                              as IN_IR_TXN
					,sum(src.IN_DV_OP      )                              as IN_DV_OP
					,sum(src.IN_DV_RE      )                              as IN_DV_RE
					,sum(src.IN_DV_ER      )                              as IN_DV_ER
					,sum(src.IN_DV_FR      )                              as IN_DV_FR
					,sum(src.IN_WM_F_FEE   )                              as IN_WM_F_FEE
					,sum(src.IN_WM_I_FEE   )                              as IN_WM_I_FEE
					,sum(src.IN_WM_S_FEE   )                              as IN_WM_S_FEE
					,sum(src.IN_TR_FU      )                              as IN_TR_FU
					,sum(src.IN_TR_CF      )                              as IN_TR_CF
					,sum(src.IN_TR_SC      )                              as IN_TR_SC
					,sum(src.IN_TR_OTS     )                              as IN_TR_OTS
					<!--,sum(src.IN_TR_OTS     )                          as IN_TR_OTS 2015-02-11 其他信託改以信託專戶取代M-104-00028-->	
					,sum(src.IN_CC_CC      )                              as IN_CC_CC
					,sum(src.IN_CC_IV      )                              as IN_CC_IV
					,case when sum(IN_CC_CC_CN) > 0 then 'Y' else 'N' end as IN_CC_CC_ACT
					,case when sum(IN_CC_JC_CN) > 0 then 'Y' else 'N' end as IN_CC_JC_ACT
					,sum(src.IN_ST         )                              as IN_ST
					,sum(src.IN_ST_FD      )                              as IN_ST_FD
					,sum(src.IN_ST_LN_1    )                              as IN_ST_LN_1
					,sum(src.IN_ST_LN_2    )                              as IN_ST_LN_2
					,sum(src.IN_ST_CC      )                              as IN_ST_CC
					,sum(src.IN_ST_NB      )                              as IN_ST_NB
					,sum(src.IN_GEB_NTD_TXN    )                          as IN_GEB_NTD_TXN
					,sum(src.IN_GEB_NTD_N_TXN  )                          as IN_GEB_NTD_N_TXN
					,sum(src.IN_GEB_LC_TXN     )                          as IN_GEB_LC_TXN
					,sum(src.IN_WM_FD          )                          as IN_WM_FD
                    ,sum(src.IN_WM_IA          )                          as IN_WM_IA
                    ,sum(src.IN_WM_STD         )                          as IN_WM_STD
                    ,sum(src.IN_WM_BAL         )                          as IN_WM_BAL
                    ,sum(src.IN_WM_FEE         )                          as IN_WM_FEE
                    ,sum(src.IN_TR_SC_E        )                          as IN_TR_SC_E
				FROM v2 as src
				WHERE CUST_KEY like ?
				GROUP BY src.CYC_MN, src.CUST_KEY, src.BR_CD				
				ORDER BY CYC_MN DESC
        </value>
    </entry>
    <!-- 讀取國內貢獻度(存款,非存款)  -->
    <entry key="DM_CUBCPCM.selTOTAL_ATTRIBUTE">
        <value>
            SELECT SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE,SUM((CASE WHEN BC3_CD = 'SLDP' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS SLDP_TOTAL,
			SUM((CASE WHEN BC3_CD = 'FDTA_T' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS FDTA_T_TOTAL
			FROM DWADM.OTS_CUBCPCM WHERE HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?
        </value>
    </entry>
	
	
    <!-- 讀取海外貢獻度(非存款部份)  -->
	<!--
			因為AS400銷戶後該ID就不會出現DWADM.DW_CSLNOVS.CUST_KEY，所以要多判斷 DWADM.OTS_CUBCPCMOVS.HCUST_KEY
			WHERE (DWADM.OTS_CUBCPCMOVS.HCUST_KEY like ? OR DWADM.DW_CSLNOVS.CUST_KEY like ? )
			-->
			<!--
			串DWADM.DW_CSLNOVS會有相同客戶但不同客戶編號的問題，所以改成DW把海外銀行原SWIFTCODE全部轉成MEGA ID，就不用多串DWADM.DW_CSLNOVS了
			select CUST_KEY,BR_CD,CUST_NO,CUST_ID from DWADM.DW_CSLNOVS where DWADM.DW_CSLNOVS.CUST_KEY like 'USZ0167752%'
			CUST_KEY BR_CD CUST_NO CUST_ID 
			USZ0167752  0A3 0000140862 000140862 
			USZ0167752  0A3 0000140871 000140862 

			SELECT CURR,SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE,SUM((CASE WHEN BC3_CD = 'SLDP' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS SLDP_TOTAL,
			SUM((CASE WHEN BC3_CD = 'FDTA_T' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS FDTA_T_TOTAL   
			FROM DWADM.OTS_CUBCPCMOVS 
			LEFT OUTER JOIN 
			DWADM.DW_CSLNOVS 
			ON 
			DWADM.DW_CSLNOVS.CUST_ID = DWADM.OTS_CUBCPCMOVS.CUST_ID   AND 
			DWADM.DW_CSLNOVS.BR_CD = DWADM.OTS_CUBCPCMOVS.BRN_NO
			WHERE (DWADM.OTS_CUBCPCMOVS.HCUST_KEY like ? OR DWADM.DW_CSLNOVS.CUST_KEY like ? )
            AND  CYC_MN BETWEEN ? AND ? GROUP BY CURR
	-->
    <entry key="DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE">
        <value>       	
            SELECT 'TWD' AS CURR,SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE,SUM((CASE WHEN BC3_CD = 'SLDP' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS SLDP_TOTAL,
			SUM((CASE WHEN BC3_CD = 'FDTA_T' THEN TOTAL_ATTRIBUTE ELSE 0  END)) AS FDTA_T_TOTAL   
			FROM DWADM.OTS_CUBCPCMOVS WHERE HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  					
        </value>
    </entry>	
    <!-- 	取得集團明細(海外)、關係明細相關欄位(海外) -->
    <entry key="DWADM.DW_LNQUOTOV.selL120s05b1">
    	<value>
    		<!--
            SELECT CUST_ID,CUST_DUP_NO,FACT_AMT_T,FACT_AMT_N,LOAN_BAL_S,LOAN_BAL_N,AVL_FAMT_TN,AVL_FAMT_TS
            FROM DWADM.DW_LNQUOTOV WHERE CUST_KEY = ?
			-->
			SELECT LEFT(CUST_KEY,10) as CUST_ID,RIGHT(CUST_KEY,1) as CUST_DUP_NO,
			ADJ_FAMT_T AS FACT_AMT_T,ADJ_FAMT_TN AS FACT_AMT_N,
			LOAN_BAL_TS AS LOAN_BAL_S,LOAN_BAL_TN AS LOAN_BAL_N,
			ADJ_FAMT_TN AS AVL_FAMT_TN,ADJ_FAMT_TS AS AVL_FAMT_TS 
            FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ?
        </value>
    </entry>   
	
	<!-- 取得客戶之海外額度序號(含已銷戶) -->
	<entry key="DWADM.DW_LNQUOTOV.DW_LNQUOTOV_C.selByCustId">
		<value>
			SELECT DISTINCT LINE_NO FROM (
				SELECT LINE_NO FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ?
				UNION ALL
				SELECT EL_LINE_NO AS LINE_NO FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ?
				UNION ALL
				SELECT LINE_NO FROM DWADM.DW_LNCNTROVS_C WHERE CUST_KEY = ?
				UNION ALL
				SELECT EL_LINE_NO AS LINE_NO FROM DWADM.DW_LNCNTROVS_C WHERE CUST_KEY = ?
			)
		</value>
	</entry>

	<!-- 確認額度序號不存在於海外額度檔 -->
	<entry key="DWADM.DW_LNQUOTOV.DW_LNQUOTOV_C.withoutCntrNo">
		<value>
			WITH TMP(CNTRNO) AS
			(
				?
			)
			SELECT T.CNTRNO FROM TMP T
			LEFT JOIN DWADM.DW_LNCNTROVS LL ON T.CNTRNO = LL.LINE_NO
			LEFT JOIN DWADM.DW_LNCNTROVS LE ON T.CNTRNO = LE.EL_LINE_NO
			LEFT JOIN DWADM.DW_LNCNTROVS_C CL ON T.CNTRNO = CL.LINE_NO
			LEFT JOIN DWADM.DW_LNCNTROVS_C CE ON T.CNTRNO = CE.EL_LINE_NO
			WHERE LL.LINE_NO IS NULL AND LE.EL_LINE_NO IS NULL AND CL.LINE_NO IS NULL AND CE.EL_LINE_NO IS NULL
		</value>
	</entry>
	
	<!-- 	取得客戶之海外額度序號-->
    <entry key="DWADM.LNCNTROVS.selByCustId">
    	<value>
    		SELECT LINE_NO AS CONTRACT FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY like ?
        </value>
    </entry>

    <!--J-111-0052 高利拆單定存  取得資料月份數及最新年月-->
    <entry key="DWADM.OTS_HINS_ASCT.selMaxMN">
        <value>
			SELECT COUNT(DISTINCT CYC_MN) AS MNCNT, MAX(CYC_MN) AS MAXMN, DATE(MAX(CYC_MN))-5 MONTH AS BEGMN FROM DWADM.OTS_HINS_ASCT WHERE BR_CD = '999' AND CUST_KEY = '9999999999'
        </value>
    </entry>

    <!--J-111-0052 高利拆單定存  取得統計資料-->
    <entry key="DWADM.OTS_HINS_ASCT.selByCustId">
        <value>
            SELECT CYC_MN, SUM(SUM_FACE_VAL_CT) AS SUMCT, ROUND(AVG(INS_RT), 6) AS AVGRT
            FROM DWADM.OTS_HINS_ASCT
            WHERE CUST_KEY = ? AND CYC_MN = ?
            GROUP BY CYC_MN
        </value>
    </entry>

    <!--J-111-0052 高利拆單定存  取得近半年總數  -->
    <entry key="DWADM.OTS_HINS_ASCT.selSumByCustId">
        <value>
            WITH TEMP1 AS (
                SELECT CYC_MN, SUM(SUM_FACE_VAL_CT) AS SUMCT,ROUND(AVG(INS_RT), 6) AS AVGRT
                FROM DWADM.OTS_HINS_ASCT
                WHERE CUST_KEY = ? AND CYC_MN BETWEEN ? AND ?
                GROUP BY CYC_MN
            )
            SELECT SUM(SUMCT) AS TOTALCT, SUM(AVGRT) AS TOTALRT FROM TEMP1
        </value>
    </entry>
	
	<!-- 	G-104-0286 加強銀行法72-2條之相關控管 取得客戶之海外額度序號資訊BY額度序號-->
    <entry key="DDWADM.LNCNTROVS.selByCustIdAndCntrNo">
    	<value>
    		SELECT * FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ? AND LINE_NO = ?
        </value>
    </entry>   
	
    
    <!-- ########## -->
    <!-- 沿用徵信交易 -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(額度明細表) -->
    <!-- 個金授信(額度明細表) -->
	<!-- ########## -->
	
	
	
	<!-- 額度明細表內 查詢國家別所對應區域別代碼(DWCNTRY_AREA) -->
    <entry key="DWCNTRYAREA.selByCountryCode">
        <value>
           SELECT AREA_NO FROM DWADM.DW_CNTRY_AREA  WHERE CNTRY_CD = ?
        </value>
    </entry>
	
	
	<!-- 額度明細表內 查詢原額度序號DW_LNQUOTOV  -->
    <entry key="DWLNQUOTOV.selByContract">
        <value>
        	<!--
            SELECT COUNT(*) FROM DWADM.DW_LNQUOTOV  WHERE CONTRACT=?  AND CUST_KEY=?  
			-->
			SELECT COUNT(*) FROM DWADM.DW_LNCNTROVS  WHERE LINE_NO =?  AND CUST_KEY=?  
        </value>
    </entry>
	
	   <!-- 額度明細表內 引進帳務資料  購料放款案下已開狀未到單金額  -->
    <entry key="DWLNQUOTOV.selLcamt">
        <value>
        	
            SELECT T1.OPEN_FLAG AS FLAG,T1.OPEN_BAL AS LCAMT,T2.CUR_CD_3C AS LCCURR
 					FROM DWADM.DW_LNQUOTOV T1 ,DWADM.DW_CNCUR2N T2
 					WHERE T1.FACT_SWFT = T2.CUR_CD AND T1.CUST_KEY=? AND T1.CONTRACT=?
			<!--
			SELECT T1.OPEN_FLAG AS FLAG,T1.OPEN_BAL_T AS LCAMT,T1.CUR_CD_3C AS LCCURR
 					FROM DWADM.DW_LNCNTROVS T1 
 					WHERE T1.CUST_KEY=? AND T1.LINE_NO=?	
			-->			
        </value>
    </entry>
    <!-- 額度明細表內 引進帳務資料 (借方彙計數,貸方彙計數) -->
    <entry key="DWLNCNTROVS.seltot">
        <value>
            SELECT CUR_CD_3C AS CURR,SUM(DR_TOT) AS DRTOT,SUM(CR_TOT) AS CRTOT FROM DWADM.DW_LNCNTROVS
            WHERE CUST_KEY=? AND LINE_NO=? GROUP BY CUR_CD_3C
        </value>
    </entry>
	
		   <!-- 額度明細表內 引進帳務資料 (餘額) -->
    <entry key="DW_LNQUOTOV.selBLAmt">
        <value>
            SELECT   RATE.CUR_CD_3C AS CURR,(LNQ.LOAN_BAL_S + LNQ.LOAN_BAL_N) / RATE.AGNT_TWD_RT AS COUNT
            FROM DWADM.DW_LNQUOTOV LNQ ,DWADM.DW_FXRTHOVS RATE
            WHERE  LNQ.BR_NO=RATE.BR_CD
            AND LNQ.CUST_KEY=?
            AND LNQ.BR_NO=?
            AND LNQ.CONTRACT=?
            AND HOME_FG='Y' AND  RATE.DW_LST_MNT_DT=(SELECT MAX(DW_LST_MNT_DT) FROM DWADM.DW_FXRTHOVS)
        </value>
    </entry>
    <!-- 額度明細表內 計算合計取得該分行所有幣別匯率 -->
    <entry key="DW_FXRTHOVS.selByBrNoRate">
        <value>
            SELECT  CUR_CD_3C AS CURR , HOME_FG AS HOME,AGNT_TWD_RT AS TWD,MUL_DIV_L AS TYPE,AGNT_LOC_RT  AS RATE ,MUL_DIV_T TTYPE
            FROM DWADM.DW_FXRTHOVS WHERE
            BR_CD=? AND  DW_LST_MNT_DT=(SELECT MAX(DW_LST_MNT_DT) FROM DWADM.DW_FXRTHOVS)
        </value>
    </entry>
    <!-- 額度明細表內 計算合計取得該分行本位幣別 -->
    <entry key="DW_FXRTHOVS.selByBrNoMainCurr">
        <value>
            SELECT  CUR_CD_3C AS CURR FROM DWADM.DW_FXRTHOVS
            WHERE BR_CD=? AND  DW_LST_MNT_DT=(SELECT MAX(DW_LST_MNT_DT) FROM DWADM.DW_FXRTHOVS) AND HOME_FG='Y'
        </value>
    </entry>
	
	 <!-- 取得分行逾期比率  -->
    <entry key="DW_ASLNOVEROVS.selRate">
        <value>
        	 SELECT * FROM( SELECT  BR_NO AS BRNO,CYC_MN AS DATEYM , OV_AMT AS AMT,LN_BAL BAL FROM DWADM.DW_ASLNOVEROVS WHERE BR_NO IN ({0})
            AND CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ASLNOVEROVS  WHERE BR_NO  IN ({1}))  )T
        </value>
    </entry>
	


    
    
    <!-- ########## -->
    <!-- 企金授信(動審表) -->
    <!-- 個金授信(動審表) -->
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金授信(其他) -->
    <!-- 個金授信(其他) -->
	
    <!-- 	取得放款餘額檔 -->
    <entry key="DWADM.DW_ASLNDAVGOVS.selDistinctByCust_Key">
        <value>
            SELECT distinct BR_CD,CANCEL_DT,FACT_CONTR
            FROM DWADM.DW_ASLNDAVGOVS WHERE CUST_KEY = ?
        </value>
    </entry>   
    <entry key="DWADM.DW_ASLNDAVGOVS.selByCust_Key">
        <value>
            SELECT BR_CD,CANCEL_DT,FACT_CONTR ,DW_ASLNDAVGOVS 
            FROM DWADM.DW_ASLNDAVGOVS WHERE CUST_KEY = ?
        </value>
    </entry>   
    <!-- ########## -->
    
    
    <!-- ########## -->
    <!-- 企金覆審(覆審名單) -->
    <!-- ########## -->
    
	<!-- 維護控制檔-搜尋戶況 -->
	<entry key="DW_LNQUOTOV.selCstate">
        <value>
			SELECT COUNT(*) AS TCOUNT, 
			MAX(NORMAL_FG) AS NORMAL_FG, 
			MIN(OV_FG) AS OV_FG, 
			MIN(OB_FG) AS OB_FG, 
			MIN(CD_FG) AS CD_FG 
			FROM ( 
				SELECT T2.CONTRACT,A.CONTRACT, 
				CASE WHEN A.CONTRACT IS NOT NULL THEN A.NORMAL_FG ELSE 'Y' END AS NORMAL_FG, 
				CASE WHEN A.CONTRACT IS NOT NULL THEN A.OV_FG ELSE 'N' END AS OV_FG, 
				CASE WHEN A.CONTRACT IS NOT NULL THEN A.OB_FG ELSE 'N' END AS OB_FG, 
				CASE WHEN A.CONTRACT IS NOT NULL THEN A.CD_FG ELSE 'N' END AS CD_FG 
				FROM ( 
				  SELECT DISTINCT CONTRACT AS CONTRACT FROM ( 
						(SELECT CONTRACT FROM DWADM.DW_LNQUOTOV WHERE CUST_KEY = ? AND BR_NO = ? ) 
						UNION ALL  
						(SELECT DISTINCT FACT_CONTR AS CONTRACT FROM DWADM.DW_ASLNDAVGOVS WHERE CUST_KEY = ? AND BR_CD = ? AND CANCEL_DT IS NULL ) 
					) AS T1
				) AS T2 
				LEFT OUTER JOIN 
				DWADM.DW_LNQUOTOV A
				ON  
				T2.CONTRACT = A.CONTRACT	 
			) AS T3
        </value>
    </entry>
    
    <!-- ########## -->
    <!-- 企金覆審(覆審報告表) -->
    <!-- ########## -->
    
    <!-- ########## -->
    <!-- 個金覆審(覆審名單) -->
    <!-- ########## -->
	  <!--查詢DW授信資料 -->
    <entry key="DW_ASLNDAVGOVSJOIN.selectByBrNoCustId">
        <value>
            SELECT
            T1.CUST_KEY, T1.BR_NO , T1.CONTRACT , T1.FACT_SWFT ,
            T1.FACT_AMT,t1.DURING_BG,t1.DURING_ED ,'' as GL_AC_KEY ,0 as LN_BAL,0
            as LN_BAL_NT,FACT_SWFT,'' as ACCT_KEY,0 as FACT_AMT_NT
            
            FROM DWADM.DW_LNQUOTOV T1
            WHERE BR_NO = ?
            AND CUST_KEY = ?
            AND CONTRACT NOT IN
            (SELECT FACT_CONTR FROM DWADM.DW_ASLNDAVGOVS where BR_CD=T1.BR_NO AND CUST_KEY
            = T1.CUST_KEY AND FACT_CONTR = T1.CONTRACT)
            
            UNION
            
            SELECT T1.CUST_KEY,T1.BR_NO , T1.CONTRACT , T1.FACT_SWFT ,
            T1.FACT_AMT,t1.DURING_BG,t1.DURING_ED,
            t2.GL_AC_KEY,t2.LN_BAL,t2.LN_BAL_NT,t1.FACT_SWFT ,T2. ACCT_KEY,t2.FACT_AMT_NT
            
            FROM DWADM.DW_LNQUOTOV T1,DWADM.DW_ASLNDAVGOVS T2
            WHERE T1.BR_NO = ?
            AND T1.CUST_KEY = ?
            AND T1.BR_NO = T2.BR_CD
            AND T1.CUST_KEY = T2.CUST_KEY
            AND T1.CONTRACT = T2.FACT_CONTR
        </value>
    </entry>
	
    
	<!-- 個金覆審更新-搜尋來源檔-新案 -->
    <entry key="DW_ELF490OVS.selNewRule">
        <value>
            <![CDATA[
					SELECT * FROM DWADM.DW_ELF490OVS 
					WHERE LEFT(CHAR(CYC_MN),7) = ? AND RULE_NO_NW <> '' AND BR_NO = ?
				]]>
        </value>
    </entry>
    <!-- 個金覆審更新-搜尋來源檔-舊案 -->
    <entry key="DW_ELF490OVS.selOldRule">
        <value>
            SELECT * FROM DWADM.DW_ELF490OVS
            WHERE LEFT(CHAR(CYC_MN),7) = ? AND RULE_NO_NW =''  AND BR_NO= ?
        </value>
    </entry>
    <!-- 個金覆審更新-查詢是否循環 -->
    <entry key="DW_ASLNDAVGOVS.joinDW_LNQUOTOV">
        <value>
            SELECT A.GL_AC_KEY,A.ACCT_KEY,A.FACT_CONTR,X.REVOVLE AS REVOLVE FROM (
            SELECT GL_AC_KEY,ACCT_KEY,FACT_CONTR
            FROM DWADM.DW_ASLNDAVGOVS
            WHERE CUST_KEY = ?  AND (CHAR(CANCEL_DT) IS NULL OR CHAR(CANCEL_DT) = '0001-01-01')
            ) AS A
            LEFT JOIN DWADM.DW_LNQUOTOV X ON A.FACT_CONTR=X.CONTRACT
            WHERE A.GL_AC_KEY IN ('12400000','12100100','12100200')
        </value>
    </entry>
    <!-- 個金覆審更新-判斷是否為不動產 -->
    <entry key="DW_ASLNDAVGOVS.selQuotano">
        <value>
            SELECT
            CUST_KEY,MAX(GL_AC_KEY) as LOANCODE,STATUS
            ,FACT_CONTR as QUOTANO,ACCT_KEY AS LOAN_NO,FACT_AMT AS QUOTAPRV
            ,CONTR_START_DT AS APRVDT,CONTR_DUE_DT AS DUEDT,SUM(LN_BAL) as LOANBAL
            ,SUM(LN_BAL_NT) AS NTDLBAL,FACT_CUR_CD AS QUOTACURR
            FROM DWADM.DW_ASLNDAVGOVS
            WHERE CUST_KEY = ? AND
            Not (LN_KIND='IM' OR LN_KIND='IO' OR LN_KIND='EX' or
            LN_KIND='EO') AND (CANCEL_DT IS null OR CANCEL_DT = '0001-01-01')
            GROUP BY
            CUST_KEY,FACT_CONTR,ACCT_KEY,STATUS
            ,FACT_AMT ,CONTR_START_DT, CONTR_DUE_DT
            ,FACT_CUR_CD
            ORDER BY QUOTANO ASC
        </value>
    </entry>
    <!-- 個金覆審更新-判斷是否大於500萬 -->
    <entry key="DW_LNQUOTOV.joinDW_ASLNDAVGOVS">
        <value>
            SELECT
            A.CUST_KEY AS CUST_ID,A.CONTRACT,A.REVOVLE,A.FACT_SWFT,A.FACT_AMT AS FACT_AMT,B.ACCT_KEY AS LOAN_NO,B.GL_AC_KEY,B.LN_BAL AS LOAN_BAL
            FROM
            DWADM.DW_LNQUOTOV A LEFT OUTER JOIN DWADM.DW_ASLNDAVGOVS B
            ON A.CUST_KEY=B.CUST_KEY
            WHERE
            A.BR_NO= ? AND
            A.CUST_KEY = ? AND
            (B.CANCEL_DT IS NULL OR B.CANCEL_DT = '0001-01-01') AND
            A.CONTRACT=B.FACT_CONTR
        </value>
    </entry>
    <!-- 個金覆審更新-取出匯率計算500萬 -->
    <entry key="DW_FXRTHOVS.selAll">
        <value>
            SELECT *
            FROM DWADM.DW_FXRTHOVS
            WHERE
            BR_CD = ? AND
            DW_DATA_SRC_DT =
            (SELECT MAX(DW_DATA_SRC_DT)
            FROM (SELECT * FROM DWADM.DW_FXRTHOVS WHERE LEFT(CHAR(DW_DATA_SRC_DT),7) = ?) T )
        </value>
    </entry>
    <entry key="DW_FXRTHOVS.selAllMaxDate">
        <value>
            SELECT *
            FROM DWADM.DW_FXRTHOVS
            WHERE
            BR_CD = ? AND
            DW_DATA_SRC_DT =
            (SELECT MAX(DW_DATA_SRC_DT)
            FROM DWADM.DW_FXRTHOVS )
        </value>
    </entry>
	<entry key="LNQUOTOV.joinASLNDAVGOVS">
		<value>
			SELECT 
				A.BR_NO, A.CUST_KEY, A.FACT_AMT_T, A.FACT_SWFT, 
				B.GL_AC_KEY, B.LN_BAL_NT AS LOAN_BAL, B.ACCT_KEY AS LOAN_NO, B.STATUS, B.FACT_CONTR AS QUOTANO, B.CUR_CD
			FROM
				DWADM.DW_LNQUOTOV A LEFT OUTER JOIN DWADM.DW_ASLNDAVGOVS B
			ON A.CUST_KEY=B.CUST_KEY AND A.CONTRACT=B.FACT_CONTR
			WHERE
				A.BR_NO= ? AND A.CUST_KEY = ? AND
				(B.CANCEL_DT IS NULL OR B.CANCEL_DT = '0001-01-01')
		</value>
	</entry>
	
    <!-- 個金覆審更新-排除逾催呆戶 -->
    <entry key="DW_ASLNDAVGOVS.selStatus">
        <value>
            SELECT STATUS FROM DWADM.DW_ASLNDAVGOVS WHERE ACCT_KEY= ? AND CANCEL_DT IS null
        </value>
    </entry>
    <!-- 個金產生名單 -->
    <entry key="DW_LNQUOTOV.selRevovle">
        <value>
            <![CDATA[
				SELECT  
				  D.BR_NO, D.FACT_TYPE , D.USED_AMT , D.CREATE_DT,  
				  D.CONTRACT,D.REVOVLE,D.FACT_SWFT,D.FACT_AMT,D.BEG_DATE, 
				  D.END_DATE,D.DURING_BG,D.DURING_ED,  
				  E.ACCT_KEY, E.LN_BAL, E.STATUS,E.CONTR_START_DT AS START_DT
				FROM 
				  DWADM.DW_LNQUOTOV D,DWADM.DW_ASLNDAVGOVS E  
				WHERE
				  D.CUST_KEY = ? AND     
				  (E.CANCEL_DT IS null OR E.CANCEL_DT = '0001-01-01' ) AND 
				  E.FACT_CONTR=D.CONTRACT AND  
				  D.FACT_TYPE<>'30'	
				]]>
        </value>
    </entry>
    <!-- 個金產生覆審名單-搜尋帳務資料 -->
    <entry key="DW_LNQUOTOV.selFactAmt">
        <value>
            SELECT
            P.BR_NO, P.FACT_SWFT, P.FACT_AMT, P.USED_AMT,
            D.FACT_CONTR, D.FACT_CUR_CD, D.LN_BAL AS MBAL
            FROM
            DWADM.DW_LNQUOTOV P, DWADM.DW_ASLNDAVGOVS D
            WHERE
            P.CUST_KEY= ? AND
            P.CONTRACT= ? AND
            P.FACT_TYPE='30' AND
            P.CONTRACT=D.FACT_CONTR
        </value>
    </entry>
    <!-- 個金產生名單-搜尋會計科目 -->
    <entry key="ASLNDAVGOVS.selACTCD">
        <value>
            SELECT
            GL_AC_KEY as ACTCD
            FROM
            DWADM.DW_ASLNDAVGOVS
            WHERE
            ACCT_KEY = ?
        </value>
    </entry>
    <!-- 個金產生名單-搜尋總額度 -->
    <entry key="DW_LNQUOTOV.selSumFact">
        <value>
            SELECT SUM(FACT_AMT) AS SUMFACT FROM DWADM.DW_LNQUOTOV WHERE CUST_KEY= ?
        </value>
    </entry>
    <!-- 個金產生資料-搜尋放款餘額 -->
    <entry key="DW_LNQUOTOV.selLnbal">
        <value>
            SELECT
            CHAR(T.END_DATE) AS END_DATE,D.LN_BAL
            FROM
            DWADM.DW_LNQUOTOV T JOIN DWADM.DW_ASLNDAVGOVS D
            ON
            T.CONTRACT=D.FACT_CONTR
            WHERE
            D.LN_BAL>0 AND T.CUST_KEY = ?
        </value>
    </entry>
	
    
    <!-- ########## -->
    <!-- 個金覆審(覆審報告表) -->
    <!-- ########## -->
	<!--查詢DW授信資料 -->
    <entry key="DW_ASLNDAVGOVSJOIN.selectByBrNoCustId2">
        <value>
        	select * from (
	            --有額度有餘額
				SELECT
				T1.CUST_KEY ,	T1.BR_NO ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	VALUE(T2.DW_DATA_SRC_DT,T1.DW_LST_MNT_DT) as DW_DATA_SRC_DT,
				t2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T1.AVL_FAMT AS FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, T2.CONTR_START_DT as DURING_BG ,	T2.CONTR_DUE_DT as DURING_ED 
				FROM DWADM.DW_LNQUOTOV T1 , DWADM.DW_ASLNDAVGOVS T2 
				where T1.BR_NO = ?
				AND	T1.CUST_KEY =  ?
				AND T1.BR_NO = T2.BR_CD AND
				T1.CUST_KEY = T2.CUST_KEY AND
				T1.CONTRACT = T2.FACT_CONTR 
						union
				--沒額度有餘額
				SELECT
				T1.CUST_KEY ,	T1.BR_NO ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	VALUE(T2.DW_DATA_SRC_DT,T1.DW_LST_MNT_DT) as DW_DATA_SRC_DT,
				t2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T1.AVL_FAMT AS FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, T2.CONTR_START_DT as DURING_BG ,	T2.CONTR_DUE_DT as DURING_ED 
				FROM DWADM.DW_LNQUOTOV  T1 
				right JOIN 
				(	SELECT	* FROM	DWADM.DW_ASLNDAVGOVS	WHERE	BR_CD = ?AND	CUST_ID =  ?) T2 
				ON T1.BR_NO = T2.BR_CD AND
				T1.CUST_KEY = T2.CUST_KEY AND
				T1.CONTRACT = T2.FACT_CONTR 
						union
				--有額度沒餘額
				SELECT
				T1.CUST_KEY ,	T1.BR_NO ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	VALUE(T2.DW_DATA_SRC_DT,T1.DW_LST_MNT_DT) as DW_DATA_SRC_DT,
				t2.GL_AC_KEY,	T3.CUR_CD_3C as FACT_SWFT ,	T1.AVL_FAMT AS FACT_AMT,	T3.CUR_CD_3C as CUR_CD,	0 as LN_BAL,value(T1.DURING_BG,T1.BEG_DATE) as DURING_BG ,	value(T1.DURING_ED,T1.END_DATE) as DURING_ED 
				FROM
				(	SELECT	* FROM	DWADM.DW_LNQUOTOV 	WHERE	BR_NO = ?AND	CUST_KEY =  ?) T1 
				join DWADM.DW_CNCUR2N T3 
				on T1.FACT_SWFT = T3.CUR_CD
				LEFT JOIN DWADM.DW_ASLNDAVGOVS T2 
				ON T1.BR_NO = T2.BR_CD AND
				T1.CUST_KEY = T2.CUST_KEY AND
				T1.CONTRACT = T2.FACT_CONTR  
				where value(T2.FACT_CONTR,'') = ''
			)  as a
			order by a.CONTRACT
        </value>
    </entry>
 <!--查詢DW授信資料 -->
    <entry key="DW_ASLNDAVGOVSJOIN.selectByBrNoCustId">
        <value>
            SELECT T1.CUST_KEY AS CUST_ID ,T1.BR_NO , T1.CONTRACT , T1.FACT_SWFT ,
            T1.FACT_AMT,t1.DURING_BG,t1.DURING_ED,
            t2.GL_AC_KEY,t2.LN_BAL,t2.LN_BAL_NT,t1.FACT_SWFT ,T2. ACCT_KEY,t2.FACT_AMT_NT
   
            FROM (select * from DWADM.DW_LNQUOTOV where BR_NO = ? and CUST_KEY = ? ) T1
			left join DWADM.DW_ASLNDAVGOVS T2
            on T1.BR_NO = T2.BR_CD
            AND T1.CUST_KEY = T2.CUST_KEY
            AND T1.CONTRACT = T2.FACT_CONTR
        </value>
    </entry>
     <!--個金-重新引進帳務資料 Part1-->
    <entry key="DW_ASLNOVEROVS.selCustIdData">
        <value>
        	 <![CDATA[
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T2.FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, value(T1.BEG_DATE,T2.CONTR_START_DT) as BEG_DATE ,	value(T1.END_DATE,T2.CONTR_DUE_DT) as END_DATE  ,T1.DURING_BG as DURING_BG , T1.DURING_ED as DURING_ED 
			FROM DWADM.DW_LNQUOTOV T1 , DWADM.DW_ASLNDAVGOVS T2 
			where T1.BR_NO = ?
			AND	T1.CUST_KEY =  ?
			AND T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR  AND
			T1.FACT_TYPE != '30' 
			AND ( CANCEL_DT IS NULL OR CANCEL_DT='0001-01-01')
			        union
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T2.FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, value(T1.BEG_DATE,T2.CONTR_START_DT) as BEG_DATE ,	value(T1.END_DATE,T2.CONTR_DUE_DT) as END_DATE  ,T1.DURING_BG as DURING_BG , T1.DURING_ED as DURING_ED 
			FROM (select * from DWADM.DW_LNQUOTOV where FACT_TYPE != '30' )  T1 
			right JOIN 
			(	SELECT	* FROM	DWADM.DW_ASLNDAVGOVS	WHERE	BR_CD = ? AND	CUST_ID =  ?  AND( CANCEL_DT IS NULL OR CANCEL_DT='0001-01-01') ) T2 
			ON T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR
			        union
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T3.CUR_CD_3C as FACT_SWFT ,	T1.FACT_AMT,	T3.CUR_CD_3C as CUR_CD,	0 as LN_BAL,value(T1.BEG_DATE,T2.CONTR_START_DT) as BEG_DATE ,	value(T1.END_DATE,T2.CONTR_DUE_DT) as END_DATE ,T1.DURING_BG as DURING_BG , T1.DURING_ED as DURING_ED 
			FROM
			(	SELECT	* FROM	DWADM.DW_LNQUOTOV 	WHERE	BR_NO = ? AND CUST_KEY =  ? AND FACT_TYPE != '30' ) T1 
			join DWADM.DW_CNCUR2N T3 
			on T1.FACT_SWFT = T3.CUR_CD
			LEFT JOIN (select * from DWADM.DW_ASLNDAVGOVS where CANCEL_DT IS NULL OR CANCEL_DT='0001-01-01') T2 
			ON T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR  
			where value(T2.FACT_CONTR,'') = ''
			]]>
        </value>
    </entry>
    
    <!--個金-重新引進帳務資料 Part2-->
	<!--
	            SELECT P.BR_NO, P.FACT_SWFT, P.FACT_AMT, P.USED_AMT , D.FACT_CONTR, D.FACT_CUR_CD, D.LN_BAL
            FROM
            DWADM.DW_LNQUOTOV P, DWADM.DW_ASLNDAVGOVS D
            WHERE
            P.CUST_KEY=? AND P.CONTRACT=? AND
            P.FACT_TYPE='30'  AND
            P.CONTRACT=D.FACT_CONTR
	-->
    <entry key="DW_ASLNOVEROVS.selContractData">
        <value>
			<![CDATA[
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T2.FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, T2.CONTR_START_DT as DURING_BG ,	T2.CONTR_DUE_DT as DURING_ED 
			FROM DWADM.DW_LNQUOTOV T1 , DWADM.DW_ASLNDAVGOVS T2 
			where T1.BR_NO = ?
			AND	T1.CUST_KEY =  ?
			AND T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR  AND
			T1.FACT_TYPE = '30' 
			        union
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T2.FACT_CUR_CD as FACT_SWFT ,	T2.FACT_AMT,	T2.CUR_CD,	T2.LN_BAL, T2.CONTR_START_DT as DURING_BG ,	T2.CONTR_DUE_DT as DURING_ED 
			FROM (select * from DWADM.DW_LNQUOTOV where FACT_TYPE = '30' )  T1 
			right JOIN 
			(	SELECT	* FROM	DWADM.DW_ASLNDAVGOVS	WHERE	BR_CD = ? AND	CUST_ID =  ? ) T2 
			ON T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR
			        union
			SELECT
			T1.CUST_KEY ,	T1.BR_NO , T1.FACT_TYPE ,T1.CREATE_DT ,	T1.CONTRACT ,	VALUE(T2. ACCT_KEY,'') AS ACCT_KEY,	T2.CONTR_START_DT,
			T2.STATUS , T1.REVOVLE , T2.GL_AC_KEY,	T3.CUR_CD_3C as FACT_SWFT ,	T1.FACT_AMT,	T3.CUR_CD_3C as CUR_CD,	0 as LN_BAL,value(T1.DURING_BG,T1.BEG_DATE) as DURING_BG ,	value(T1.DURING_ED,T1.END_DATE) as DURING_ED 
			FROM
			(	SELECT	* FROM	DWADM.DW_LNQUOTOV 	WHERE	BR_NO = ? AND CUST_KEY =  ? AND FACT_TYPE = '30' ) T1 
			join DWADM.DW_CNCUR2N T3 
			on T1.FACT_SWFT = T3.CUR_CD
			LEFT JOIN DWADM.DW_ASLNDAVGOVS T2 
			ON T1.BR_NO = T2.BR_CD AND
			T1.CUST_KEY = T2.CUST_KEY AND
			T1.CONTRACT = T2.FACT_CONTR  
			where value(T2.FACT_CONTR,'') = ''
			]]>
        </value>
    </entry>
    
    <!-- ########## -->
    <!-- 稽核工作底稿 -->
    <!-- ########## -->
    
     <!--取得海外的帳戶資料-->
    <entry key="DW_ASLNDAVGOVS.selectByCustIdBrNo">
        <value>
            SELECT BR_CD, ACCT_KEY, LN_DEP, LN_KIND, CUR_CD, GL_AC_KEY, CUST_NO, STATUS, CUST_KEY, OV_CUST_KEY, CUST_ID, DATA_DT, CONTR_START_DT, CONTR_DUE_DT, CANCEL_DT, LN_BAL, LN_BAL_NT, FACT_BR_CD, FACT_CONTR, FACT_CUR_CD, FACT_AMT, FACT_AMT_NT, DW_LST_DATA_SRC, DW_DATA_SRC_DT, DW_LST_MNT_DT
            FROM DWADM.DW_ASLNDAVGOVS
            WHERE CUST_KEY = ?  AND (BR_CD = ? or substr(FACT_CONTR,1,3) = ?)
        </value>
    </entry>
	
	<!-- ########## -->
    <!-- 覆審管理報表 -->
    <!-- ########## -->
	
	 <!-- *[覆審管理報表 查詢DW_LNQUOTOV]***產[EXCEL]*** "2" = 企金戶未出現於覆審名單  -->
    <entry key="DWLNQUOTOV.selBybrNo">
        <value>
            <![CDATA[
           SELECT T1.BR_NO,T1.CUST_KEY AS CUST_ID,T1.CONTRACT,
           T1.NORMAL_FG,T1.OV_FG,T1.OB_FG,T1.CD_FG, 
           value(T2.CUR_CD_3C,T1.FACT_SWFT) as FACT_SWFT,T1.FACT_AMT,T1.DURING_BG,T1.DURING_ED,T1.CREATE_DT   
           FROM   DWADM.DW_LNQUOTOV  T1 , DWADM.DW_CNCUR2N T2
		   WHERE T1.FACT_SWFT = T2.CUR_CD
           and SUBSTR(T1.CUST_KEY,1,1) <> '#' and  
	       ((T1.OV_FG <>'Y'and T1.OB_FG <>'Y'  AND T1.CD_FG <>'Y' ) or T1.OV_FG IS NULL ) 
		  -- and (left(T1.CONTRACT,1) BETWEEN '0' AND '9' ) 
		   AND   SUBSTR(T1.CONTRACT,8,1) <> 'Z' AND T1.BR_NO = ?
           ]]>
        </value>
    </entry>
    	
    <!-- ########## -->
    <!-- 管理報表 -->
    <!-- ########## -->
	
	 <!-- *[管理報表 查詢DW_LNQUOTOV]***產[報表]*** "1" = 授信契約已逾期控制表  step1  -->
    <entry key="DW_LNQUOTOV.selForNewReportType1ByBrNo">
        <value>
            <![CDATA[
	           SELECT T1.CUST_KEY AS CUSTID,
	           T1.CONTRACT AS CNTRNO,value(T2.CUR_CD_3C,T1.FACT_SWFT) AS CURR,
	           T1.FACT_AMT AS FACTAMT,T1.FACT_AMT_T  AS FACTNT,T1.BEG_DATE AS BDATE,T1.END_DATE AS EDATE
	           FROM DWADM.DW_LNQUOTOV T1 join DWADM.DW_CNCUR2N T2 
				on T1.FACT_SWFT = T2.CUR_CD
	           WHERE  T1.REVOVLE='Y' AND 
	           T1.BR_NO =? AND T1.FACT_TYPE in('10','31','41') 
			   AND CHAR(T1.END_DATE) between ? and ?
           ]]>
        </value>
    </entry>
	
	<!-- type9-營業單位授信報案考核彙總表  抓全年抓指定月份 -->
    <entry key="DW_ELINSPDT.sel9ByBRANCHIDCHKYMTYPCDYM">
        <value>
            <![CDATA[
			select M1.BRANCHID,value(M1.TCOUNT,0) as TCOUNT , value(M1.TITEMALL,0) as TITEMALL , value(M2.FCOUNT,0) as FCOUNT , value(M2.FITEMALL,0) as FITEMALL , value(M1.AVGT,0) as AVGT , value(M2.AVGF,0) as AVGF from 
			(
			--全年
				SELECT BRANCHID , SUM(TCOUNT) AS TCOUNT, SUM(TITEMALL) AS TITEMALL  ,CASE SUM(TCOUNT) WHEN 0 THEN 0 ELSE ROUND(DOUBLE(SUM(TITEMALL))/SUM(TCOUNT),2) END AS AVGT FROM(
				    SELECT BRANCHID,COUNT(*) AS TCOUNT ,SUM(TITEMALL) AS TITEMALL FROM   
				    (  
				          SELECT BRANCHID,DWADM.DW_ELINSPSC.UNID,SUM(ITEMALL)AS TITEMALL
				          FROM DWADM.DW_ELINSPDT
				          LEFT OUTER JOIN   
				          DWADM.DW_ELINSPSC  
				          ON  
				          DWADM.DW_ELINSPDT.UNID = DWADM.DW_ELINSPSC.UNID AND  
				          DWADM.DW_ELINSPDT.CHKYM  = DWADM.DW_ELINSPSC.CHKYM  
				          WHERE   
	          			  DWADM.DW_ELINSPSC.CHKYM between ? and ? AND DWADM.DW_ELINSPSC.TYPCD = ''1''
				          AND (DWADM.DW_ELINSPSC.SYSTYPE IS NULL OR DWADM.DW_ELINSPSC.SYSTYPE <> ''AAA'') 
				          AND NOT (DWADM.DW_ELINSPSC.SYSTYPE = ''CLS'' AND ( ITEMNO >= ''030'' AND ITEMNO <= ''041''  ) ) 
				          GROUP BY BRANCHID,DWADM.DW_ELINSPSC.UNID  
				    ) AS T1  
				    GROUP BY BRANCHID  
					UNION ALL
				    SELECT BRANCHID,SUM(CASEAMT) AS TCOUNT ,SUM(TMGRADE) AS TITEMALL FROM   
				    (  
				          SELECT DISTINCT BRANCHID,DWADM.DW_ELINSPSC.UNID,CASEAMT,TMGRADE
				          FROM DWADM.DW_ELINSPDT
				          LEFT OUTER JOIN   
				          DWADM.DW_ELINSPSC  
				          ON  
				          DWADM.DW_ELINSPDT.UNID = DWADM.DW_ELINSPSC.UNID AND  
				          DWADM.DW_ELINSPDT.CHKYM  = DWADM.DW_ELINSPSC.CHKYM  
				          WHERE   
	          			  DWADM.DW_ELINSPSC.CHKYM between ? and ?  AND DWADM.DW_ELINSPSC.TYPCD = ''2''
				          AND (DWADM.DW_ELINSPSC.SYSTYPE IS NULL OR DWADM.DW_ELINSPSC.SYSTYPE <> ''AAA'') 
				          AND NOT (DWADM.DW_ELINSPSC.SYSTYPE = ''CLS'' AND ( ITEMNO >= ''030'' AND ITEMNO <= ''041''  ) ) 
				    ) AS T1  
				    GROUP BY BRANCHID  
				) as A1
 				GROUP BY BRANCHID  
			)  M1 LEFT JOIN
			(
			--指定月份
				SELECT BRANCHID , SUM(FCOUNT) AS FCOUNT, SUM(FITEMALL) AS FITEMALL  ,CASE SUM(FCOUNT) WHEN 0 THEN 0 ELSE ROUND(DOUBLE(SUM(FITEMALL))/SUM(FCOUNT),2) END AS AVGF FROM(
				    SELECT BRANCHID,COUNT(*) AS FCOUNT ,SUM(TITEMALL) AS FITEMALL FROM   
				    (  
				          SELECT BRANCHID,DWADM.DW_ELINSPSC.UNID,SUM(ITEMALL)AS TITEMALL
				          FROM DWADM.DW_ELINSPDT
				          LEFT OUTER JOIN   
				          DWADM.DW_ELINSPSC  
				          ON  
				          DWADM.DW_ELINSPDT.UNID = DWADM.DW_ELINSPSC.UNID AND  
				          DWADM.DW_ELINSPDT.CHKYM  = DWADM.DW_ELINSPSC.CHKYM  
				          WHERE   
				          DWADM.DW_ELINSPSC.CHKYM between ? and ? AND DWADM.DW_ELINSPSC.TYPCD = ''1''
				          AND (DWADM.DW_ELINSPSC.SYSTYPE IS NULL OR DWADM.DW_ELINSPSC.SYSTYPE <> ''AAA'') 
				          AND NOT (DWADM.DW_ELINSPSC.SYSTYPE = ''CLS'' AND ( ITEMNO >= ''030'' AND ITEMNO <= ''041''  ) ) 
				          GROUP BY BRANCHID,DWADM.DW_ELINSPSC.UNID  
				    ) AS T1  
				    GROUP BY BRANCHID  
				    UNION ALL
				    SELECT BRANCHID,SUM(CASEAMT) AS FCOUNT ,SUM(TMGRADE) AS FITEMALL FROM   
				    (  
				          SELECT DISTINCT BRANCHID,DWADM.DW_ELINSPSC.UNID,CASEAMT,TMGRADE
				          FROM DWADM.DW_ELINSPDT
				          LEFT OUTER JOIN   
				          DWADM.DW_ELINSPSC  
				          ON  
				          DWADM.DW_ELINSPDT.UNID = DWADM.DW_ELINSPSC.UNID AND  
				          DWADM.DW_ELINSPDT.CHKYM  = DWADM.DW_ELINSPSC.CHKYM  
				          WHERE   
				          DWADM.DW_ELINSPSC.CHKYM between ? and ? AND DWADM.DW_ELINSPSC.TYPCD = ''2''
				          AND (DWADM.DW_ELINSPSC.SYSTYPE IS NULL OR DWADM.DW_ELINSPSC.SYSTYPE <> ''AAA'') 
				          AND NOT (DWADM.DW_ELINSPSC.SYSTYPE = ''CLS'' AND ( ITEMNO >= ''030'' AND ITEMNO <= ''041''  ) ) 
				    ) AS T1  
				    GROUP BY BRANCHID  
				) as A2
 				GROUP BY BRANCHID  
			) M2 on M1.BRANCHID = M2.BRANCHID
			{0}
          ]]>
        </value>
    </entry>
     <!-- 管理報表-個金  查詢  報案考核表被扣分清單 -->
    <entry key="DW_ELINSPDT.queryDeductPoint">
        <value>
            <![CDATA[
	           	SELECT A.*,B.CUSTID,B.DUPNO
				FROM 
					(SELECT DWADM.DW_ELINSPDT.UNID,SUM(ITEMALL) AS AFCOUNT 
					FROM DWADM.DW_ELINSPDT    
					INNER JOIN DWADM.DW_ELINSPSC 
					ON DWADM.DW_ELINSPDT.UNID = DWADM.DW_ELINSPSC.UNID   
					WHERE DWADM.DW_ELINSPSC.CHKYM= ?
					  AND DWADM.DW_ELINSPSC.BRANCHID= ?
					  AND DWADM.DW_ELINSPSC.SYSTYPE ='CLS'  
					GROUP BY DWADM.DW_ELINSPDT.UNID) AS A
				LEFT JOIN DWADM.DW_ELINSPSC  AS B ON A.UNID = B.UNID
           ]]>
        </value>
    </entry>
	
	<entry key="FXRTHOVS_S.TWDAndCur2Rate">
			<value><![CDATA[WITH FXRTH AS (SELECT F.BR_CD,F.CUR_CD_3C,F.HOME_FG,F.AGNT_LOC_RT,F.AGNT_TWD_RT,F.MUL_DIV_L,F.MUL_DIV_T FROM {0} F WHERE F.{1} = ? AND F.BR_CD = ? AND (F.HOME_FG = '''Y''' OR F.CUR_CD_3C = ?))
				SELECT H.BR_CD,H.CUR_CD_3C AS CURRH,CRT.CURRT AS CURRT,CR2.CURR2 AS CURR2,
  					CASE WHEN CR2.MDL2 = '''M''' THEN DOUBLE(CRT.CURRTH) / DOUBLE(CR2.RT2) ELSE DOUBLE(CRT.CURRTH) * DOUBLE(CR2.RT2) END AS CURRT2, 
  					CASE WHEN CR2.MDL2 = '''M''' THEN DOUBLE(CRT.CURRHT) * DOUBLE(CR2.RT2) ELSE DOUBLE(CRT.CURRHT) / DOUBLE(CR2.RT2) END AS CURR2T  
				FROM FXRTH H INNER JOIN (SELECT BR_CD BRT,CHAR('''TWD''') AS CURRT,CASE WHEN MUL_DIV_T = '''M''' THEN 1 * AGNT_TWD_RT ELSE 1 / AGNT_TWD_RT END AS CURRHT,CASE WHEN MUL_DIV_T = '''M''' THEN 1 / AGNT_TWD_RT ELSE 1 * AGNT_TWD_RT END AS CURRTH FROM FXRTH WHERE HOME_FG = '''Y''') CRT ON H.BR_CD = CRT.BRT
  							 INNER JOIN (SELECT BR_CD AS BR2,CUR_CD_3C AS CURR2,AGNT_LOC_RT AS RT2,MUL_DIV_L AS MDL2 FROM FXRTH WHERE CUR_CD_3C = ?) CR2 ON H.BR_CD = CR2.BR2
				WHERE H.HOME_FG = '''Y''']]></value>
	</entry>
	<entry key="FXRTHOVS_S.Cur1ToCur2Rate">
			<value><![CDATA[WITH FXRTH AS (SELECT F.BR_CD,F.CUR_CD_3C,F.HOME_FG,F.AGNT_LOC_RT,F.MUL_DIV_L FROM {0} F WHERE F.{1} = ? AND F.BR_CD = ? AND (F.HOME_FG = \Y\ OR F.CUR_CD_3C in (?,?)))
				SELECT H.BR_CD,H.CUR_CD_3C AS CURRH,CR1.CURR1 AS CURR1,CR2.CURR2 AS CURR2, 
  					CASE WHEN CR1.MDL1 = \M\ AND CR2.MDL2 = \M\ THEN 1 * DOUBLE(CR1.RT1) / DOUBLE(CR2.RT2)
       					WHEN CR1.MDL1 = \D\ AND CR2.MDL2 = \M\ THEN 1 / DOUBLE(CR1.RT1) / DOUBLE(CR2.RT2)
       					WHEN CR1.MDL1 = \D\ AND CR2.MDL2 = \D\ THEN 1 / DOUBLE(CR1.RT1) * DOUBLE(CR2.RT2)
       					WHEN CR1.MDL1 = \M\ AND CR2.MDL2 = \D\ THEN 1 * DOUBLE(CR1.RT1) * DOUBLE(CR2.RT2) END AS CURR12 
				FROM FXRTH H INNER JOIN (SELECT BR_CD AS BR1,CUR_CD_3C AS CURR1,AGNT_LOC_RT AS RT1,MUL_DIV_L AS MDL1 FROM FXRTH WHERE CUR_CD_3C = ?) CR1 ON H.BR_CD = CR1.BR1
  							 INNER JOIN (SELECT BR_CD AS BR2,CUR_CD_3C AS CURR2,AGNT_LOC_RT AS RT2,MUL_DIV_L AS MDL2 FROM FXRTH WHERE CUR_CD_3C = ?) CR2 ON H.BR_CD = CR2.BR2
				WHERE H.HOME_FG = \Y\]]></value>
	</entry>
	<entry key="DW_FXRTHOVS.getMaxDT">
			<value><![CDATA[select max(H.DT) as DT from DWADM.DW_FXRTHOVS H ]]></value>
	</entry>
		
	 <!-- ########## -->
    <!-- 建檔維護 -->
    <!-- ########## -->
	<entry key="DWADM.DW_RKCNTRNO.selectByCntrNo">
        <value>
            Select count(CntrNo)
			From DWADM.DW_RKCNTRNO
			Where CntrNo=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCNTRNO.Update">
        <value>
			Update DWADM.DW_RKCNTRNO
			Set CntrNo=?
			Where CntrNo=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCNTRNO.selBycustIddupNo">
        <value>
            Select *
            From
            (Select COUNT( CUSTID ) AS DW_RKADJUST From DWADM.DW_RKADJUST Where CUSTID = ? AND DUPNO = ? ) AS T1,
            (Select COUNT( CUSTID ) AS DW_RKAPPLICANT From DWADM.DW_RKAPPLICANT Where CUSTID = ? AND DUPNO = ? ) AS T2,
            (Select COUNT( CUSTID ) AS DW_RKCNTRNO From DWADM.DW_RKCNTRNO Where CUSTID = ? AND DUPNO = ? ) AS T3,
            (Select COUNT( CUSTID ) AS DW_RKCOLL From DWADM.DW_RKCOLL Where CUSTID = ? AND DUPNO = ? ) AS T4,
            (Select COUNT( CUSTID ) AS DW_RKCREDIT From DWADM.DW_RKCREDIT Where CUSTID = ? AND DUPNO = ? ) AS T5,
            (Select COUNT( CUSTID ) AS DW_RKJCIC From DWADM.DW_RKJCIC Where CUSTID = ? AND DUPNO = ? ) AS T6,
            (Select COUNT( CUSTID ) AS DW_RKPROJECT From DWADM.DW_RKPROJECT Where CUSTID = ? AND DUPNO = ? ) AS T7,
            (Select COUNT( CUSTID ) AS DW_RKSCORE From DWADM.DW_RKSCORE Where CUSTID = ? AND DUPNO = ? ) AS T8,
            (Select COUNT( CUSTID ) AS DW_ELINSPSC From DWADM.DW_ELINSPSC Where CUSTID = ? AND DUPNO = ? ) AS T9
        </value>
    </entry>
    <entry key="DWADM.DW_RKCNTRNO.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKCNTRNO set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>
    <entry key="DWADM.DW_RKADJUST.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKADJUST set acct_key=?	where acct_key=? and acct_key!=''
        </value>
    </entry>	
	<entry key="DWADM.DW_RKADJUST.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKADJUST Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKAPPLICANT.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKAPPLICANT Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKAPPLICANT.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKAPPLICANT set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>
	<entry key="DWADM.DW_RKAPPLICANT.SelectByNoteIdCustIdDupNo">
        <value>
            SELECT * FROM DWADM.DW_RKAPPLICANT WHERE NOTEID in (?, ?) AND CUSTID = ? AND DUPNO = ? 
        </value>
    </entry>
	<entry key="DWADM.DW_RKCUSTID.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKCUSTID Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKCOLL.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKCOLL Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKCREDIT.select_by_acct_key">
        <value>
            select * from DWADM.DW_RKCREDIT where acct_key=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCREDIT.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKCREDIT set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>	
	<entry key="DWADM.DW_RKCREDIT.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKCREDIT Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKJCIC.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKJCIC Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
	<entry key="DWADM.DW_RKJCIC.SelectByNoteIdCustIdDupNo">
        <value>
            SELECT * FROM DWADM.DW_RKJCIC WHERE NOTEID in (?, ?) AND CUSTID = ? AND DUPNO = ? 
        </value>
    </entry>
    <entry key="DWADM.DW_RKJCIC.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKJCIC set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>	
	<entry key="DWADM.DW_RKPROJECT.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKPROJECT set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>
	<entry key="DWADM.DW_RKPROJECT.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKPROJECT Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_RKSCORE.convert_acct_key_old_to_new">
        <value>
            update DWADM.DW_RKSCORE set acct_key=? where acct_key=? and acct_key!=''
        </value>
    </entry>
	<entry key="DWADM.DW_RKSCORE.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_RKSCORE Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
    <entry key="DWADM.DW_ELINSPSC.UpdateByCustIdDupNo">
        <value>
            Update DWADM.DW_ELINSPSC Set CUSTID = ? , DUPNO = ? Where CUSTID = ? AND DUPNO = ?
        </value>
    </entry>
	
	<!-- batch pdate & insert DWADM.DW_RKCREDIT add by Fantasy 2013/03/06 -->
	<entry key="DWADM.DW_RKCREDIT.update">
        <value>
            update DWADM.DW_RKCREDIT set CUST_KEY=?,LNGEFLAG=?,
				BASE_A=?,BASE_B=?,BASE_S=?,BASE_SCORE=?,TOTAL_SCORE=?,
				INITIAL_SCORE=?,PREDICT_BAD_RATE=?,INITIAL_RATING=?,
				ADJ_RATING=?,FINAL_RATING=?,JCIC_WARNING_FLAG=?,FINAL_RATING_FLAG=?,
				DELETE_REASON=?,REJECT_OTHEREASON_TEXT=?,DOCSTATUS=?,DATA_SRC_DT=?
			where NOTEID=? and CUSTID=? and DUPNO=? and MOWTYPE=? and MOWVER1=? and MOWVER2=? and JCIC_DATE=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCREDIT.insert">
        <value>
            insert into DWADM.DW_RKCREDIT(BR_CD, NOTEID, CUSTID, DUPNO, MOWTYPE, MOWVER1, MOWVER2, JCIC_DATE, CUST_KEY, LNGEFLAG, BASE_A, BASE_B, BASE_S, BASE_SCORE, TOTAL_SCORE, INITIAL_SCORE, PREDICT_BAD_RATE, INITIAL_RATING, ADJ_RATING, FINAL_RATING, JCIC_WARNING_FLAG, FINAL_RATING_FLAG, DELETE_REASON, REJECT_OTHEREASON_TEXT, DOCSTATUS, DATA_SRC_DT) 
			values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        </value>
    </entry>
	<entry key="delete">
		<value>
        	DELETE FROM {0} WHERE {1}
        </value>
	</entry>
	<entry key="insert">
		<value>
        	INSERT INTO {0}(
					{1}
				)
				VALUES (
					{2}
				)
        </value>
	</entry>
	<entry key="update">
		<value>
			UPDATE {0}
			SET {1}
			WHERE {2}
        </value>
	</entry>
	
	<entry key="DWADM_MD_CUPFM_OTS.getMaxCycMn">
        <value>
			select max(cyc_mn) as cyc_mn from DWADM.MD_CUPFM_OTS where CUST_KEY = '9999999999' and BR_CD = '999'
        </value>
    </entry>
	
	<entry key="DWADM_OTS_BSL2CSNET_AVG.getMaxCycMn">
        <value>
			select max(cyc_mn) as cyc_mn from DWADM.OTS_BSL2CSNET_AVG where CUST_KEY = '9999999999'  
        </value>
    </entry>
	
	<!-- 	取得集團明細(海外)、關係明細相關欄位(海外) -->
    <entry key="DWADM.DW_LNCNTROVS.selL120s05b1">
		<value>
            <![CDATA[	
			SELECT T1.BAN,T1.DUPNO,T1.CNAME,AMT_T AS FACT_AMT_T,AMT_N AS FACT_AMT_N,
			LOANBAL_S AS LOAN_BAL_S,LOANBAL_N AS LOAN_BAL_N, (
			SELECT SUM(ROUND(ADJ_FAMT_T/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE DEP_CODE <> ''09'' AND DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL1, (
			SELECT SUM(ROUND(ADJ_FAMT_TN/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE DEP_CODE <> ''09'' AND DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL2, (
			SELECT SUM(ROUND(LOAN_BAL_TS/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE DEP_CODE <> ''09'' AND (DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO OR DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT ''  '' CONCAT T1.DUPNO) AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL3, (
			SELECT SUM(ROUND(LOAN_BAL_TN/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE DEP_CODE <> ''09'' AND (DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO OR DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT ''  '' CONCAT T1.DUPNO) AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL4, 
			CASE WHEN LEFT(T1.BAN,1) BETWEEN ''0'' AND ''9'' THEN ''1'' ELSE ''4'' END AS TYPCD
			FROM (
			SELECT GRPCMP.BAN, GRPCMP.DUPNO,CNAME,SUM(ADJ_FAMT_T) AS AMT_T, SUM(ADJ_FAMT_TN) AS AMT_N, SUM(LOAN_BAL_TS) AS LOANBAL_S, SUM(LOAN_BAL_TN) AS LOANBAL_N
			FROM (
			SELECT DISTINCT {0} AS CUST_KEY, {1} AS BAN,{2} AS DUPNO, {3} AS CNAME
			FROM SYSIBM.SYSDUMMY1 ) AS GRPCMP LEFT OUTER JOIN DWADM.DW_LNCNTROVS
			ON DWADM.DW_LNCNTROVS.CUST_KEY = GRPCMP.CUST_KEY AND DEP_CODE <> ''09''
			GROUP BY GRPCMP.BAN, GRPCMP.DUPNO,CNAME ) AS T1 
			ORDER BY TYPCD,T1.BAN,T1.DUPNO
		]]>
        </value>
    </entry>   
	
	<!-- 	取得集團明細(海外)、關係明細相關欄位(海外) -->
    <entry key="DWADM.DW_LNCNTROVS.selL120s05b1ForLocal">
		<value>
            <![CDATA[	
			SELECT T1.BAN,T1.DUPNO,T1.CNAME,AMT_T AS FACT_AMT_T,AMT_N AS FACT_AMT_N,
			LOANBAL_S AS LOAN_BAL_S,LOANBAL_N AS LOAN_BAL_N, (
			SELECT SUM(ROUND(ADJ_FAMT_T/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE BR_CD IN ({4}) AND DEP_CODE <> ''09'' AND DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL1, (
			SELECT SUM(ROUND(ADJ_FAMT_TN/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE BR_CD IN ({5}) AND DEP_CODE <> ''09'' AND DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL2, (
			SELECT SUM(ROUND(LOAN_BAL_TS/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE BR_CD IN ({6}) AND DEP_CODE <> ''09'' AND (DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO OR DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT ''  '' CONCAT T1.DUPNO) AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL3, (
			SELECT SUM(ROUND(LOAN_BAL_TN/1000,0))*1000 AS TOTBAL
			FROM DWADM.DW_LNCNTROVS
			WHERE BR_CD IN ({7}) AND DEP_CODE <> ''09'' AND (DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT T1.DUPNO OR DWADM.DW_LNCNTROVS.CUST_KEY = T1.BAN CONCAT ''  '' CONCAT T1.DUPNO) AND( LN_NO_FLAG='''' OR LN_NO_FLAG=''5'' OR LN_NO_FLAG=''7'' ) ) TOTBAL4, 
			CASE WHEN LEFT(T1.BAN,1) BETWEEN ''0'' AND ''9'' THEN ''1'' ELSE ''4'' END AS TYPCD
			FROM (
			SELECT GRPCMP.BAN, GRPCMP.DUPNO,CNAME,SUM(ADJ_FAMT_T) AS AMT_T, SUM(ADJ_FAMT_TN) AS AMT_N, SUM(LOAN_BAL_TS) AS LOANBAL_S, SUM(LOAN_BAL_TN) AS LOANBAL_N
			FROM (
			SELECT DISTINCT {0} AS CUST_KEY, {1} AS BAN,{2} AS DUPNO, {3} AS CNAME
			FROM SYSIBM.SYSDUMMY1 ) AS GRPCMP LEFT OUTER JOIN DWADM.DW_LNCNTROVS
			ON DWADM.DW_LNCNTROVS.CUST_KEY = GRPCMP.CUST_KEY AND DEP_CODE <> ''09'' AND BR_CD IN ({8})
			GROUP BY GRPCMP.BAN, GRPCMP.DUPNO,CNAME ) AS T1 
			ORDER BY TYPCD,T1.BAN,T1.DUPNO
		]]>
        </value>
    </entry>   
	
	
	<entry key="DW_LNMRATEOVS.find_USED_RATEY_By_LINENO">
        <value><![CDATA[SELECT  USED_RATEY AS MRA_USED_RTEY FROM  dwadm.DW_LNMRATEOVS WHERE LINE_NO =?  AND (CYC_MN >= ?  and  CYC_MN <= ?) ORDER BY CYC_MN DESC ]]></value>
    </entry>  

	<!-- 信用風險集中 -->
	<entry key="DWADM.OTS_ROCLIST_ECUSD.selByCustId">
        <value>
            SELECT * FROM DWADM.OTS_ROCLIST_ECUSD WHERE CUST_ID = ? AND CUST_ID_DUP = ? 
        </value>
    </entry>
	
	<!-- 信用風險集中度，集團抓的規定限額比率 -->
	<entry key="DWADM.findDW_GRP_LIMIT_ByGrade">
        <value>
            SELECT * FROM DWADM.MD_CLXCL WHERE SBJ_CD_CLS_ID = 'GRP_LIMIT' AND OBJ_CD_CLS_ID = ?
        </value>
    </entry>
	
	<!-- 取得前一日客戶存款資料 原 MISDPF.findByCustId(MisEldpfServiceImpl.java\findByCustId) -->
	<entry key="DWADM.IDDP.findDpfByCustId">
        <value>
			WITH DEPOIST_TABLE AS
			(SELECT CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_NO
			             ELSE MR_PB_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_DUP_NO
			             ELSE MR_PB_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_PB_ACT_STATUS AS DP_STATUS,
			        MR_PB_CURR_CODE AS DP_CURR,
			        MR_PB_AVAIL_BAL AS DP_BAL,
			        MR_PB_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_PB_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_PB_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_PB_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0001
			  WHERE MR_PB_ID_NO = ? OR MR_PB_TAX_NO = ?  			  
			  UNION 
			 SELECT CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_NO
			             ELSE MR_CT_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_DUP_NO
			             ELSE MR_CT_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CT_ACT_STATUS AS DP_STATUS,
			        MR_CT_CURR_CODE AS DP_CURR,
			        MR_CT_SUM_FACE_VALUE_CT AS DP_BAL,
			        MR_CT_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CT_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CT_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CT_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0002
			  WHERE MR_CT_ID_NO = ? OR MR_CT_TAX_NO = ?
			  UNION        
			 SELECT CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_NO
			             ELSE MR_CK_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_DUP_NO
			             ELSE MR_CK_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CK_ACT_STATUS AS DP_STATUS,
			        MR_CK_CURR_CODE AS DP_CURR,
			        MR_CK_AVAIL_BAL AS DP_BAL,
			        MR_CK_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CK_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CK_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CK_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0003
			  WHERE MR_CK_ID_NO = ? OR MR_CK_TAX_NO = ? ) 
			 SELECT DP_CUST_ID AS DP_CUST_ID, DP_APCD AS DP_AP_CODE, SUM(DP_BAL * FR_AC_RATE)/1000 AS TWDBAL
			   FROM DEPOIST_TABLE, DWADM.IDOT0004
			  WHERE DP_CUST_ID = ?
			    AND DP_CURR = FR_CURR
			  GROUP BY DP_CUST_ID, DP_APCD
			  ORDER BY SUM(DP_BAL * FR_AC_RATE)/1000 DESC
        </value>
    </entry>  
	
	<!-- 查詢本行帳戶 By brno,custid,dupNo 原MIS.ELDPF.findByCustid(MisdbBASEServiceImpl.java\findMisEldpfByCustid) -->
	<entry key="DWADM.IDDP.findDpfSeqnoByCustidBrno">
        <value>
            WITH DEPOIST_TABLE AS
			(SELECT CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_NO
			             ELSE MR_PB_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_DUP_NO
			             ELSE MR_PB_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_PB_ACT_STATUS AS DP_STATUS,
			        MR_PB_CURR_CODE AS DP_CURR,
			        MR_PB_AVAIL_BAL AS DP_BAL,
			        MR_PB_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_PB_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_PB_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_PB_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0001
			  WHERE MR_PB_ID_NO = ? OR MR_PB_TAX_NO = ?
			  UNION 
			 SELECT CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_NO
			             ELSE MR_CT_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_DUP_NO
			             ELSE MR_CT_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CT_ACT_STATUS AS DP_STATUS,
			        MR_CT_CURR_CODE AS DP_CURR,
			        MR_CT_SUM_FACE_VALUE_CT AS DP_BAL,
			        MR_CT_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CT_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CT_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CT_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0002
			  WHERE MR_CT_ID_NO = ?  OR MR_CT_TAX_NO = ? 
			  UNION        
			 SELECT CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_NO
			             ELSE MR_CK_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_DUP_NO
			             ELSE MR_CK_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CK_ACT_STATUS AS DP_STATUS,
			        MR_CK_CURR_CODE AS DP_CURR,
			        MR_CK_AVAIL_BAL AS DP_BAL,
			        MR_CK_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CK_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CK_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CK_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0003
			  WHERE MR_CK_ID_NO = ? OR MR_CK_TAX_NO = ? ) 
			 SELECT DP_BRNO AS BRNO, DP_APCD AS APCD, DP_SEQNO AS SEQNO
			   FROM DEPOIST_TABLE
			  WHERE DP_CUST_ID = ?
			    AND DP_CUST_DUP = ?
			    AND DP_STATUS NOT IN('02','03')
			    AND DP_CURR = '00'
			    AND DP_BRNO = ?
        </value>
    </entry>
	  
	<!-- 查詢本行帳戶 By custid,dupNo 原MIS.ELDPF.findByCustid2(MisdbBASEServiceImpl.java\findMisEldpfByCustid) -->
	<entry key="DWADM.IDDP.findDpfSeqnoByCustid">
        <value>
            WITH DEPOIST_TABLE AS
			(SELECT CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22','76','77') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_NO
			             ELSE MR_PB_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22','76','77') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_DUP_NO
			             ELSE MR_PB_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_PB_ACT_STATUS AS DP_STATUS,
			        MR_PB_CURR_CODE AS DP_CURR,
			        MR_PB_AVAIL_BAL AS DP_BAL,
			        MR_PB_ACT_NO AS DP_ACT_NO,
			        CAST(CASE WHEN MR_PB_AIO_FLAG = 'Y' then SUBSTR(MR_PB_ORI_ACT_NO, 6, 3) else LEFT(MR_PB_ACT_NO,3) end AS CHAR(03)) AS DP_BRNO,
			        CAST(CASE WHEN MR_PB_AIO_FLAG = 'Y' then SUBSTR(MR_PB_ORI_ACT_NO, 9, 2) else SUBSTR(MR_PB_ACT_NO,4,2) end AS CHAR(02)) AS DP_APCD,
			        CAST(CASE WHEN MR_PB_AIO_FLAG = 'Y' then RIGHT(MR_PB_ORI_ACT_NO, 6) else RIGHT(MR_PB_ACT_NO,6) end AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0001
			  WHERE MR_PB_ID_NO = ? OR MR_PB_TAX_NO = ?   
			  UNION 
			 SELECT CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_NO
			             ELSE MR_CT_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_DUP_NO
			             ELSE MR_CT_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CT_ACT_STATUS AS DP_STATUS,
			        MR_CT_CURR_CODE AS DP_CURR,
			        MR_CT_SUM_FACE_VALUE_CT AS DP_BAL,
			        MR_CT_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CT_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CT_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CT_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0002
			  WHERE MR_CT_ID_NO = ? OR MR_CT_TAX_NO = ?  
			  UNION        
			 SELECT CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_NO
			             ELSE MR_CK_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_DUP_NO
			             ELSE MR_CK_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CK_ACT_STATUS AS DP_STATUS,
			        MR_CK_CURR_CODE AS DP_CURR,
			        MR_CK_AVAIL_BAL AS DP_BAL,
			        MR_CK_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CK_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CK_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CK_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0003
			  WHERE MR_CK_ID_NO = ? OR MR_CK_TAX_NO = ? ) 
			 SELECT DP_BRNO AS BRNO, DP_APCD AS APCD, DP_SEQNO AS SEQNO
			   FROM DEPOIST_TABLE
			  WHERE DP_CUST_ID = ?
			    AND DP_CUST_DUP = ?
			    AND DP_STATUS NOT IN('02','03')
			    AND DP_CURR = '00'
			  ORDER BY DP_CUST_DUP DESC
        </value>
    </entry>
	
	<!-- 查詢本行帳戶 By brno,apcd,seqno 原MIS.ELDPF.findByAccount(MisdbBASEServiceImpl.java\findMisEldpfByAccount)  -->
	<entry key="DWADM.IDDP.findDpfCustByAccount">
        <value>
            WITH DEPOIST_TABLE AS
			(SELECT CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_NO
			             ELSE MR_PB_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_PB_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_PB_ID_DUP_NO
			             ELSE MR_PB_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_PB_ACT_STATUS AS DP_STATUS,
			        MR_PB_CURR_CODE AS DP_CURR,
			        MR_PB_AVAIL_BAL AS DP_BAL,
			        MR_PB_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_PB_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_PB_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_PB_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0001
			  WHERE MR_PB_ACT_NO = ?  
			  UNION 
			 SELECT CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_NO
			             ELSE MR_CT_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CT_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CT_ID_DUP_NO
			             ELSE MR_CT_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CT_ACT_STATUS AS DP_STATUS,
			        MR_CT_CURR_CODE AS DP_CURR,
			        MR_CT_SUM_FACE_VALUE_CT AS DP_BAL,
			        MR_CT_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CT_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CT_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CT_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0002
			  WHERE MR_CT_ACT_NO = ? 
			  UNION        
			 SELECT CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_NO
			             ELSE MR_CK_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN('03','17','18','57','58','47','48','49','22') OR MR_CK_BUSINESS_CODE6 IN('060000','130300') 
			             THEN MR_CK_ID_DUP_NO
			             ELSE MR_CK_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CK_ACT_STATUS AS DP_STATUS,
			        MR_CK_CURR_CODE AS DP_CURR,
			        MR_CK_AVAIL_BAL AS DP_BAL,
			        MR_CK_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CK_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CK_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CK_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0003
			  WHERE MR_CK_ACT_NO = ?)
			 SELECT DP_CUST_ID AS CUSTID, DP_CUST_DUP AS CUSTID_DUP
			   FROM DEPOIST_TABLE
        </value>
    </entry>
	
	<!-- 查詢本行帳戶 By brno,apcd,seqno,custid 原MIS.ELDPF.findByAccount2(MisdbBASEServiceImpl.java\findMisEldpfByAccount)  -->
	<entry key="DWADM.IDDP.findDpfCustByCustIdAccount">
        <value>
            WITH DEPOIST_TABLE AS
			(SELECT CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_PB_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_PB_ID_NO
			             ELSE MR_PB_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_PB_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_PB_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_PB_ID_DUP_NO
			             ELSE MR_PB_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_PB_ACT_STATUS AS DP_STATUS,
			        MR_PB_CURR_CODE AS DP_CURR,
			        MR_PB_AVAIL_BAL AS DP_BAL,
			        MR_PB_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_PB_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_PB_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_PB_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0001
			  WHERE MR_PB_ACT_NO = ?
			  UNION 
			 SELECT CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_CT_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_CT_ID_NO
			             ELSE MR_CT_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CT_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_CT_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_CT_ID_DUP_NO
			             ELSE MR_CT_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CT_ACT_STATUS AS DP_STATUS,
			        MR_CT_CURR_CODE AS DP_CURR,
			        MR_CT_SUM_FACE_VALUE_CT AS DP_BAL,
			        MR_CT_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CT_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CT_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CT_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0002
			  WHERE MR_CT_ACT_NO = ? 
			  UNION        
			 SELECT CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_CK_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_CK_ID_NO
			             ELSE MR_CK_TAX_NO
			             END AS DP_CUST_ID,
			        CASE WHEN SUBSTR(MR_CK_ACT_NO,4,2) IN(''03'',''17'',''18'',''57'',''58'',''47'',''48'',''49'',''22'') OR MR_CK_BUSINESS_CODE6 IN(''060000'',''130300'') 
			             THEN MR_CK_ID_DUP_NO
			             ELSE MR_CK_TAX_DUP_NO
			             END AS DP_CUST_DUP,
			        MR_CK_ACT_STATUS AS DP_STATUS,
			        MR_CK_CURR_CODE AS DP_CURR,
			        MR_CK_AVAIL_BAL AS DP_BAL,
			        MR_CK_ACT_NO AS DP_ACT_NO,
			        CAST(LEFT(MR_CK_ACT_NO,3) AS CHAR(03)) AS DP_BRNO,
			        CAST(SUBSTR(MR_CK_ACT_NO,4,2) AS CHAR(02)) AS DP_APCD,
			        CAST(RIGHT(MR_CK_ACT_NO,6) AS CHAR(06)) AS DP_SEQNO
			   FROM DWADM.IDDP0003
			  WHERE MR_CK_ACT_NO = ?)
			 SELECT DP_CUST_ID AS CUSTID, DP_CUST_DUP AS CUSTID_DUP
			   FROM DEPOIST_TABLE
			  WHERE DP_CUST_ID IN ({0})
        </value>
    </entry>
	
	<entry key="OTS_BSL2CSNET_AVG.selCYC_MN">
        <value>
            SELECT  SUM(RA_AMT) AS TOTAL_RA_AMT FROM DWADM.OTS_BSL2CSNET_AVG WHERE CUST_KEY like ? AND  CYC_MN BETWEEN ? AND ?  
        </value>
    </entry>

	<!--
	房貸模型 [1.0 及 1.1] 
	原本 DR 的8等為                       0.2970     9等為     0.5590
	但後來發現有誤，8等應為  0.0297     9等應為0.0559
	-->
	<entry key="DWADM.DW_RKCREDIT.updDR__DR_1YR">
        <value>
            update dwadm.DW_RKCREDIT set dr=?,dr_1yr=?
			where MOWTYPE=? and MOWVER1=? and MOWVER2=? and FINAL_RATING=? 
			and (DR is null or DR=0 or DR=0.2970 or DR=0.5590)
        </value>
    </entry>	
	
	<entry key="DWADM.DW_RKSCORE.delL140M01A">
        <value>
            update dwadm.DW_RKSCORE set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCREDIT.delL140M01A">
        <value>
            update dwadm.DW_RKCREDIT set DOCSTATUS=?,DELETE_REASON=?,REJECT_OTHEREASON_TEXT=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKJCIC.delL140M01A">
        <value>
            update dwadm.DW_RKJCIC set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKPROJECT.delL140M01A">
        <value>
            update dwadm.DW_RKPROJECT set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKADJUST.delL140M01A">
        <value>
            update dwadm.DW_RKADJUST set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKCNTRNO.delL140M01A">
        <value>
            update dwadm.DW_RKCNTRNO set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	<entry key="DWADM.DW_RKAPPLICANT.delL140M01A">
        <value>
            update dwadm.DW_RKAPPLICANT set DOCSTATUS=?
			where BR_CD=? AND NOTEID=? AND CUSTID=? AND DUPNO=? AND MOWTYPE=? AND MOWVER1=?
			AND MOWVER2=? AND JCIC_DATE=? AND ACCT_KEY=?
        </value>
    </entry>
	
	<!--J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍-->
	<entry key="DW_ROCGRPBLD1_E.getDBUEstAmtAndBal">
   <value><![CDATA[SELECT GRPID,SUM(ADJ_FAMT_TS)AS DBU_IsEstAmtAdj,SUM(ADJ_FAMT_TN) AS DBU_NotEstAmtAdj,SUM(AVL_FAMT_TS) AS DBU_IsEstAmt,SUM(AVL_FAMT_TN) AS DBU_NotEstAmt,SUM(LN_BAL_S) AS DBU_IsEstBal,SUM(LN_BAL_N) AS DBU_NotEstBal,SUM(BD_FAMT_TN) AS DBU_BD_FAMT_TN,
   SUM(BD_BAL_N) AS DBU_BD_BAL_N,SUM(BD_BAL_S) AS DBU_BD_BAL_S,SUM(BD_BAL_S+BD_BAL_N) AS DBU_BD_BAL_T,SUM(BD_BAL_N_G) AS DBU_BD_BAL_N_G,SUM(BD_BAL_N_OG) AS DBU_BD_BAL_N_OG
   FROM (SELECT * FROM DWADM.DW_ROCGRPBLD1_E WHERE GRPID = ? AND GRP_CMP_ID IN ({0}) AND DW_LST_DATA_SRC='''LN'''
   AND CYC_DT=(SELECT MAX(CYC_DT) FROM DWADM.DW_ROCGRPBLD1_E WHERE DW_LST_DATA_SRC='''LN''')) A
   GROUP BY GRPID]]></value>
  </entry>

  <!--J-107-0395_05097_B1001 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍-->
  <entry key="DW_ROCGRPBLD1_E.getOCEEstAmtAndBal">
   <value><![CDATA[SELECT GRPID,SUM(ADJ_FAMT_TS) AS OCE_IsEstAmtAdj,SUM(ADJ_FAMT_TN) AS OCE_NotEstAmtAdj,SUM(AVL_FAMT_TS) AS OCE_IsEstAmt,SUM(AVL_FAMT_TN) AS OCE_NotEstAmt,SUM(LN_BAL_S) AS OCE_IsEstBal,SUM(LN_BAL_N) AS OCE_NotEstBal,SUM(BD_FAMT_TN) AS OCE_BD_FAMT_TN,
   SUM(BD_BAL_N) AS OCE_BD_BAL_N,SUM(BD_BAL_S) AS OCE_BD_BAL_S,SUM(BD_BAL_S+BD_BAL_N) AS OCE_BD_BAL_T,SUM(BD_BAL_N_G) AS OCE_BD_BAL_N_G,SUM(BD_BAL_N_OG) AS OCE_BD_BAL_N_OG
   FROM (SELECT * FROM DWADM.DW_ROCGRPBLD1_E WHERE GRPID = ? AND GRP_CMP_ID IN ({0}) AND DW_LST_DATA_SRC='''OV'''
   AND CYC_DT=(SELECT MAX(CYC_DT) FROM DWADM.DW_ROCGRPBLD1_E WHERE DW_LST_DATA_SRC='''OV''')) A
   GROUP BY GRPID]]></value>
  </entry>

   <!--J-107-0395_05097_B1002 Web e-Loan企金授信簽報書修改第八章本行買入集團企業無擔保債券額度及餘額及計算之種類範圍-->
   <!--泰國地區當地集團成員要以集團代號來串所有Y開頭分行的客戶檔-->
   <entry key="DW_CSLNOVS.selectByBrNoCustId">
        <value>
        	<![CDATA[
            SELECT * FROM
			( 
				SELECT RTRIM(LEFT(T0.CUST_KEY,10)) AS BAN,
				(CASE WHEN RTRIM(RIGHT(T0.CUST_KEY,1)) = '' THEN '0' ELSE RTRIM(RIGHT(T0.CUST_KEY,1)) END) AS DUPNO,
				'' AS CNAME,0 AS AMT_T,0 AS AMT_N,0 AS LOANBAL_S,0 AS LOANBAL_N, 0 AS TOTBAL,T0.GRPID AS GRPID
				FROM 
				(SELECT * FROM DWADM.DW_CSLNOVS WHERE BR_CD = ? AND CUST_KEY = ? AND GRPID <> '000000000' ) AS T0 
				UNION 
				SELECT RTRIM(LEFT(T2.CUST_KEY,10)) AS BAN,
				(CASE WHEN RTRIM(RIGHT(T2.CUST_KEY,1)) = '' THEN '0' ELSE RTRIM(RIGHT(T2.CUST_KEY,1)) END) AS DUPNO,
				'' AS CNAME,0 AS AMT_T,0 AS AMT_N,0 AS LOANBAL_S,0 AS LOANBAL_N, 0 AS TOTBAL,T2.GRPID AS GRPID
				FROM 
				(SELECT  BR_CD,CUST_KEY,GRPID FROM DWADM.DW_CSLNOVS WHERE BR_CD = ? AND CUST_KEY = ? AND GRPID <> '000000000' ) AS T1 
				LEFT OUTER JOIN
				DWADM.DW_CSLNOVS AS T2
				ON 
				T1.GRPID = T2.GRPID  
				WHERE LEFT(T2.BR_CD,1) = 'Y'
			)
			]]>
        </value>
    </entry>
	
	<entry key="J1050202.update_DW_ELINSPSC_01">
        <value>
            update dwadm.DW_ELINSPSC set CUSTID = ?,DUPNO = ? where CUSTID = ? AND DUPNO = ? AND UNID = ?
        </value>
    </entry>
	
	<entry key="DWLNCNTROVS.selByCustId">
        <value>
			SELECT BR_CD  AS LNF022_BR_NO,LINE_NO AS LNF022_CONTRACT,CUST_KEY AS LNF022_CUST_ID,'' AS LNF022_BEG_DATE,EXPIRE_DT AS LNF022_END_DATE,
			LOAN_BAL_TS AS LNF022_LOAN_BAL_S,LOAN_BAL_TN AS LNF022_LOAN_BAL_N,CUR_CD_3C AS LNF022_FACT_SWFT,ADJ_FAMT AS LNF022_ADJ_FAMT,ADJ_FAMT_T AS LNF022_ADJ_FAMT_T 
			from  DWADM.DW_LNCNTROVS
			WHERE 
			CUST_KEY = ?
        </value>
    </entry>	
	
	<entry key="DWLNCNTROVS.selByCntrNo">
        <value>
			SELECT BR_CD  AS LNF022_BR_NO,LINE_NO AS LNF022_CONTRACT,CUST_KEY AS LNF022_CUST_ID,'' AS LNF022_BEG_DATE,EXPIRE_DT AS LNF022_END_DATE,
			LOAN_BAL_TS AS LNF022_LOAN_BAL_S,LOAN_BAL_TN AS LNF022_LOAN_BAL_N,CUR_CD_3C AS LNF022_FACT_SWFT,ADJ_FAMT AS LNF022_ADJ_FAMT,ADJ_FAMT_T AS LNF022_ADJ_FAMT_T,
			LOAN_BAL_OS,LOAN_BAL_ON,REVOVLE
			from  DWADM.DW_LNCNTROVS
			WHERE 
			LINE_NO = ?
        </value>
    </entry>	
	
	<entry key="DWLNCNTROVS.selByCustIdAndCntrNo">
        <value>
			SELECT BR_CD  AS LNF022_BR_NO,LINE_NO AS LNF022_CONTRACT,CUST_KEY AS LNF022_CUST_ID,'' AS LNF022_BEG_DATE,EXPIRE_DT AS LNF022_END_DATE,
			LOAN_BAL_TS AS LNF022_LOAN_BAL_S,LOAN_BAL_TN AS LNF022_LOAN_BAL_N,CUR_CD_3C AS LNF022_FACT_SWFT,ADJ_FAMT AS LNF022_ADJ_FAMT,ADJ_FAMT_T AS LNF022_ADJ_FAMT_T 
			from  DWADM.DW_LNCNTROVS
			WHERE 
			CUST_KEY = ? AND LINE_NO = ?
        </value>
    </entry>	
	
	<entry key="DWLNCNTROVS.selByCustIdAndCntrNo_withOriginal">
        <value>
			SELECT BR_CD  AS LNF022_BR_NO,LINE_NO AS LNF022_CONTRACT,CUST_KEY AS LNF022_CUST_ID,'' AS LNF022_BEG_DATE,EXPIRE_DT AS LNF022_END_DATE,
			LOAN_BAL_OS AS LNF022_LOAN_BAL_OS,LOAN_BAL_ON AS LNF022_LOAN_BAL_ON,LOAN_BAL_TS AS LNF022_LOAN_BAL_TS,LOAN_BAL_TN AS LNF022_LOAN_BAL_TN,
			CUR_CD_3C AS LNF022_FACT_SWFT,ADJ_FAMT AS LNF022_ADJ_FAMT,ADJ_FAMT_T AS LNF022_ADJ_FAMT_T 
			from  DWADM.DW_LNCNTROVS
			WHERE 
			CUST_KEY = ? AND LINE_NO = ?
        </value>
    </entry>	

	<entry key="OTS_EFFECTIVE_OVS.selByCustIdAndBrNo">
        <value>
			SELECT * FROM  DWADM.OTS_EFFECTIVE_OVS
			WHERE 
			BR_CD = ? AND (CUST_KEY = ?  ) AND BENE_EFFECTIVE = 'Y' AND CYC_DT = (SELECT MAX(CYC_DT) FROM DWADM.OTS_EFFECTIVE_OVS WHERE BR_CD = '999')
        </value>
    </entry>	
	
	<!--優先抓未關戶之海外分行客戶，再依UPDT_DT抓最新一筆 STUS = 'C' 代表關戶-->
	<!--
			SELECT * FROM  DWADM.OTS_CSOVS
			WHERE 
			BR_CD = ? AND CUST_KEY = ?  ORDER BY UPDT_DT DESC
	-->
	<!--
	A：ID ACTIVE　活戶。
	C：CLOSE : MM/DD/YY（2010年後才有CLOSE DATE）關戶。
	S：SYS DORMANT　靜止戶。
	D：DISABLED : MM/DD/YY　禁用存款交易。
	M：Manual Suspend手動暫停使用。（仍計息，香港使用）
	I：INACTIVE
	T：GUAR/AUTH/WALKIN：保證人、過路客、被授權人..（Non-Txn-id = Y,A,V,F時，此處即顯示T）
	-->
	<entry key="OTS_CSOVS.selByCustIdAndBrNo">
        <value>
			WITH CSOVS AS
			(			
			SELECT * FROM  DWADM.OTS_CSOVS
			WHERE 
			BR_CD = ? AND (CUST_KEY = ? )
			),
			TALIVE AS
			(
			  SELECT '1' AS TSNO, CSOVS.* FROM CSOVS WHERE CSOVS.STUS != 'C'
			),
			TCLOSE AS
			(
			  SELECT '2' AS TSNO, CSOVS.* FROM CSOVS WHERE CSOVS.STUS = 'C'
			)
			SELECT * FROM TALIVE 
			UNION ALL
			SELECT * FROM TCLOSE 
			ORDER BY TSNO ASC,UPDT_DT DESC
        </value>
    </entry>	
	
	<!--J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊-->
	<!--引進用，LMS1201ServiceImpl.java,LMS1205ServiceImpl.java，授權外簽報書第八章引進集團用，儲存引進時最新利率的資料與CYC_MN，供後續列印用-->
	<entry key="DW_ROCGRPRT.selByBadFgAndGrpGrade">
        <value>
			SELECT * FROM 
			(
				SELECT * FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = ? AND GRPGRADE = ?  AND CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ROCGRPRT)
			) AS T1
			LEFT OUTER JOIN
			(
			  	SELECT CYC_MN,GRPYY as MAX_GRPYY FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = '0' AND GRPGRADE = '1'  AND CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ROCGRPRT)
			) AS T2
			ON T1.CYC_MN = T2.CYC_MN
        </value>
    </entry>	
	
	<!--J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊-->
	<!--評等0要放在最後面(2017後的評等0為新戶及未評等)J-107-0087-001 Web e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。-->
	<!--列印用，LMS1201R01RptServiceImpl.java,LMS1201R05RptServiceImpl.java  列印時以L120S05A集團資訊引進時間為主-->
	<entry key="DW_ROCGRPRT.selByBadFgAndGrpYy">
        <value>
			SELECT T1.*,T2.MAX_GRPYY,(CASE WHEN GRPGRADE = '0' THEN '9' ELSE GRPGRADE END) AS PRINTORDER
			FROM 
			(
				SELECT * FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = ?  AND CYC_MN = ?
			) AS T1
			LEFT OUTER JOIN
			(
			  	SELECT CYC_MN,GRPYY as MAX_GRPYY FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = '0' AND GRPGRADE = '1' AND CYC_MN = ?
			) AS T2
			ON T1.CYC_MN = T2.CYC_MN
			ORDER BY PRINTORDER ASC,CYC_MN DESC
        </value>
    </entry>	
	
	<!--J-107-0007-001 Web e-Loan國內、海外授信簽報書第八章新增相同集團企業評等等級之新臺幣及美元放款利率資訊-->
	<!--列印用，CLS1141R01RptServiceImpl.java,LMS9535R01RptServiceImpl.java  因為沒有L120S05A集團資訊，所以列印時以最新當時的利率呈現-->
	<entry key="DW_ROCGRPRT.selByBadFgWithMaxCycMn">
        <value>
			SELECT T1.*,T2.MAX_GRPYY,(CASE WHEN GRPGRADE = '0' THEN '9' ELSE GRPGRADE END) AS PRINTORDER FROM 
			(
				SELECT * FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = ?  AND CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ROCGRPRT)
			) AS T1
			LEFT OUTER JOIN
			(
			  	SELECT CYC_MN,GRPYY as MAX_GRPYY FROM  DWADM.DW_ROCGRPRT
				WHERE 
				BAD_FG = '0' AND GRPGRADE = '1' AND CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ROCGRPRT)
			) AS T2
			ON T1.CYC_MN = T2.CYC_MN
			ORDER BY PRINTORDER ASC,CYC_MN DESC
        </value>
    </entry>	
	
	<!-- J-107-0073 Web e-Loan 引入是否有定時定額扣款 -->
	<entry key="DW_OTS_ASFDAC2.sel_J_107_0073">
        <value>
			select * from  DWadm.OTS_ASFDAC2 where CUST_KEY=?
			AND NOT (AC230='Y' OR AC223 >= 3 OR substr(AC299,9,1) ='Y'  )
			AND ((AC210='0001-01-01' and AC211='0001-01-01')
			OR NOT(? between AC210 AND AC211 ) 
			)                     

        </value>
    </entry>
	
	<!-- J-107-0073 Web e-Loan 引入 INV_AMT_WMS 理財AUM(不含存款) -->
	<entry key="DW_OTS_WM_CUST_SUMMARY.sel_J_107_0073">
        <value>
			SELECT * FROM DWADM.OTS_WM_CUST_SUMMARY where  CUS_CODE=? AND CYC_MN >= ?
			ORDER BY CYC_MN
        </value>
    </entry>	
	
	<!--J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊現-->
	<entry key="DW_LNCNTROVS.sumFamtByCustKey">
        <value>
			SELECT  SUM(ADJ_FAMT_T) AS ADJ_FAMT_T,SUM(ADJ_FAMT_TN) AS ADJ_FAMT_TN,SUM(ADJ_FAMT_T-ADJ_FAMT_TN) AS ADJ_FAMT_TS,
			SUM(LOAN_BAL_TN) AS LOAN_BAL_TN,SUM(LOAN_BAL_TS) AS LOAN_BAL_TS,SUM(LOAN_BAL_TN + LOAN_BAL_TS) AS LOAN_BAL_T  
			FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ?
        </value>
    </entry>	
	
	<!--J-110-0325_11557_B1001 增加合併關係企業與當地有往來資料(L120S11A_LOC)-->
	<entry key="DW_LNCNTROVS.sumFamtByCustKeyBrIds">
        <value>
			SELECT LINE_NO FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY = ? AND LEFT(LINE_NO,3) IN ({0})
        </value>
    </entry>	
	
	<!-- J-107-0073 Web e-Loan 引入是否有定時定額扣款 -->
	<entry key="DW_OTS_ASFDAC2.sel_J_107_0073">
        <value>
			select * from  DWadm.OTS_ASFDAC2 where CUST_KEY=?
			AND NOT (AC230='Y' OR AC223 >= 3 OR substr(AC299,9,1) ='Y'  )
			AND ((AC210='0001-01-01' and AC211='0001-01-01')
			OR NOT(? between AC210 AND AC211 ) 
			)                     

        </value>
    </entry>
		

    <!--J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。-->
	<!--不含利息-->
	<!---2018/09 排除'NTLN','FXLN','NTFA','FXFA','FXFB','NTFI','FXFJ'-->
	<!--
	   	 海外貢獻度(彙總檔):IMOV0074
		海外貢獻度(明細檔):IMOV0001
		海外匯兌(FXRT)-秀卿說貢獻度計算沒有含利息收入，所以不用排除資金運用部分
		海外出口(FXEX)-界寬說，有含利息收入，但是IMOV0001(明細)未提供單獨的利息收入可供排除，所以只能不排除資金運用部分
		海外進口(FXIX)-界寬說，沒有含利息收入，所以不用排除資金運用部分
		海外授信(FXLN)-維斌說，有含利息收入，且IMOV0001(明細)有提供單獨的利息收入LOAN_INT，所以DW會把利息收入放到資金運用收入，e-Loan可以排除資金運用部分
	-->
	<entry key="DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_exclude_interest">
        <value>       	
            SELECT 'TWD' AS CURR,
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
			SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN ('FXLN') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE 
			FROM DWADM.OTS_CUBCPCMOVS WHERE HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  					
        </value>
    </entry>
	
	<!--J-107-0233_05097_B1001 Web e-Loan企金授信修訂「放款定價合理性分析表」。-->
	<!--不含利息-->
	<!-- 讀取國內貢獻度(存款,非存款)  -->
	<!---2018/09 排除'NTLN','FXLN','NTFA','FXFA','FXFB','NTFI','FXFJ'-->
	<!--
		國內排除
		NTLN  	台幣放款        
		FXLN  	外幣放款        
		NTFA  	台幣應收帳款    
		NTFB  	台幣應收帳款    
		FXFA  	外幣應收帳款    
		FXFB  	外幣應收帳款    
		NTFI  	台幣供應鏈融資  
		NTFJ  	台幣供應鏈融資  
		FXFI  	外幣供應鏈融資  
		FXFJ  	外幣供應鏈融資  
		FXRT  	國外匯兌        
		FXIX  	進口            
		FXEX  	出口 
	-->
    <entry key="DM_CUBCPCM.selTOTAL_ATTRIBUTE_exclude_interest">
        <value>
            SELECT 
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
			SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN ('NTLN','FXLN','NTFA','NTFB','FXFA','FXFB','NTFI','NTFJ','FXFI','FXFJ','FXRT','FXIX','FXEX') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE
			FROM DWADM.OTS_CUBCPCM WHERE HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  
        </value>
    </entry>
	
	<!--J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」-->
	<!--參考DM_CUBCPCM.selTOTAL_ATTRIBUTE_exclude_interest-->
	<entry key="DM_CUBCPCM.selTOTAL_ATTRIBUTE_ByBC3_CD">
        <value>
            SELECT 
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
			SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN (''NTLN'',''FXLN'',''NTFA'',''NTFB'',''FXFA'',''FXFB'',''NTFI'',''NTFJ'',''FXFI'',''FXFJ'',''FXRT'',''FXIX'',''FXEX'') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE
			FROM DWADM.OTS_CUBCPCM 
			WHERE {0} HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  
        </value>
    </entry>
	
	<!--J-110-0155 修改e-loan授信管理系統簽報書之「利率定價合理性分析表」為「新臺幣、美元利率定價合理性及收益率分析表」-->
	<!--參考DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_exclude_interest-->
	<entry key="DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_ByBC3_CD">
        <value>       	
            SELECT ''TWD'' AS CURR,
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
			SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN (''FXLN'') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE 
			FROM DWADM.OTS_CUBCPCMOVS 
			WHERE {0} HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  					
        </value>
    </entry>
	
	
	 
	
	<!--J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表--> 
	<entry key="DM_CUBCPCM.selTOTAL_ATTRIBUTE_None_Loan">
        <value>
            SELECT 
			CYC_MN,
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE
			FROM DWADM.OTS_CUBCPCM 
			WHERE BC3_CD NOT IN ({0}) AND HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  
			GROUP BY CYC_MN		
        </value>
    </entry>
	
	<!--J-111-0443_05097_B1002 Web e-Loan企金授信開發授信BIS評估表--> 
	<entry key="DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_None_Loan">
        <value>       	
            SELECT 
			CYC_MN,
			''TWD'' AS CURR,
			SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE
			FROM DWADM.OTS_CUBCPCMOVS 
			WHERE BC3_CD NOT IN ({0}) AND HCUST_KEY like ?
            AND  CYC_MN BETWEEN ? AND ?  
			GROUP BY CYC_MN					
        </value>
    </entry>

    <!--J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。-->
	<!--參考DM_CUBCPCM.selTOTAL_ATTRIBUTE_ByBC3_CD-->
    <entry key="DM_CUBCPCM.selTOTAL_ATTRIBUTE_GroupByBC3_CD">
        <value>
            SELECT BC3_CD,
            SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
            SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN ('NTLN','FXLN','NTFA','NTFB','FXFA','FXFB','NTFI','NTFJ','FXFI','FXFJ','FXRT','FXIX','FXEX') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE
            FROM DWADM.OTS_CUBCPCM
            WHERE HCUST_KEY like ? AND CYC_MN BETWEEN ? AND ?
            GROUP BY BC3_CD
        </value>
    </entry>

	<!--J-112-0078 配合企金處，修改「借戶暨關係戶與本行往來實績彙總表」中，增列各業務別利潤貢獻度欄位等。-->
    <!--參考DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_ByBC3_CD-->
    <entry key="DM_CUBCPCMOVS.selTOTAL_ATTRIBUTE_GroupByBC3_CD">
        <value>
            SELECT BC3_CD, 'TWD' AS CURR,
            SUM(TOTAL_ATTRIBUTE) AS TOTAL_ATTRIBUTE_INCLUDE_INTEREST ,
            SUM(TOTAL_ATTRIBUTE - (CASE WHEN BC3_CD IN ('FXLN') THEN (FD_INCM- FD_CST) ELSE 0 END ) ) AS TOTAL_ATTRIBUTE
            FROM DWADM.OTS_CUBCPCMOVS
            WHERE HCUST_KEY like ? AND CYC_MN BETWEEN ? AND ?
            GROUP BY BC3_CD
        </value>
    </entry>
	
	 
	
	
	<!--J-107-0224_05097_B1001  Web e-Loan企金處新增企金授信案件敘做情形及比較表-->
    <entry key="OTS_RPC1A4.selbyRPT_DT">
        <value>
            select * from dwadm.OTS_RPC1A4 where RPT_DT between ? and ? order by BR_CD asc
        </value>
    </entry>
	
	
	<!--J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表-->
	<entry key="DWLNCNTROVS.selByCustIdForCreExcel2">
        <value>
			SELECT CUST_KEY AS CUST_ID,'' AS CNAME,LINE_NO AS LNF022_CONTRACT,CUR_CD_3C AS LNF022_FACT_SWFT,MIN(APRV_DT) AS BGN_DATE,MAX(EXPIRE_DT) AS END_DATE,
			SUM(ADJ_FAMT) AS AMT,
			SUM(ADJ_FAMT_T) AS AMT_T, 
			SUM(ADJ_FAMT_TN) AS AMT_TN, 
			SUM(ADJ_FAMT_TS) AS AMT_TS, 
			SUM(LOAN_BAL_TS) AS LOANBAL_TS, 
			SUM(LOAN_BAL_TN) AS LOANBAL_TN,
			SUM(LOAN_BAL_TS+LOAN_BAL_TN) AS LOANBAL_T,
			SUM(LOAN_BAL_OS+LOAN_BAL_ON) AS LOANBAL_O
			FROM DWADM.DW_LNCNTROVS
			WHERE 
			CUST_KEY = ?
			GROUP BY CUST_KEY,LINE_NO,CUR_CD_3C
        </value>
    </entry>
		
	<entry key="DW_RKCNTRNO.fixLnmonth">
        <value>
			SELECT * FROM DWADM.DW_RKCNTRNO where (br_cd like ?) AND (noteId like ?) AND (cntrNo like ?) AND lnmonth>12  
        </value>
    </entry>
	
	<entry key="DW.find_clsScoreData_by_noteId">
        <value>
			SELECT * FROM {0} WHERE noteid=?  
        </value>
    </entry>

	<entry key="DWLNQUOTOV.selDistinctCntrnoByCustidDupno">
        <value>
			SELECT DISTINCT LINE_NO AS CNTRNO FROM DWADM.DW_LNCNTROVS WHERE CUST_KEY=?  
        </value>
    </entry>
	
	<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	<entry key="DW_LNCNTROVS.updateLineNoForBt15">
        <value>
			UPDATE DWADM.DW_LNCNTROVS SET LINE_NO =  ?||RIGHT(LINE_NO,9) where LEFT(LINE_NO,3) = ?  AND CUST_KEY = ?
        </value>
    </entry>	
	
	<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	<entry key="DW_LNQUOTOV.updateLineNoForBt15">
        <value>
			UPDATE DWADM.DW_LNQUOTOV SET CONTRACT =  ?||RIGHT(CONTRACT,9) where LEFT(CONTRACT,3) = ?  AND CUST_KEY = ?
        </value>
    </entry>
	
	<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	<entry key="OTS_EFFECTIVE_OVS.updateBrCdForBt15">
        <value>
			UPDATE DWADM.OTS_EFFECTIVE_OVS SET BR_CD =  ?  where BR_CD = ?  AND CUST_KEY = ?
        </value>
    </entry>
	
	<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	<entry key="OTS_CSOVS.updateBrCdForBt15">
        <value>
			UPDATE DWADM.OTS_CSOVS SET BR_CD =  ?  where BR_CD = ?  AND CUST_KEY = ?
        </value>
    </entry>	
	
	<!--G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權-->
	<entry key="DW_CSLNOVS.updateBrCdForBt15">
        <value>
			UPDATE DWADM.DW_CSLNOVS SET BR_CD =  ?  where BR_CD = ?  AND CUST_KEY = ?
        </value>
    </entry>	
	<entry key="DW_LNCNTROVS.findAll">
        <value>
			select * from DWADM.DW_LNCNTROVS
        </value>
    </entry>	
	
	<entry key="DW_LNCUSTREL.list_by_cust_key">
        <value>
			select * from DWADM.DW_LNCUSTREL where CUST_KEY=? ORDER BY CYC_MN DESC
        </value>
    </entry>
	<entry key="DW_LNCUSTREL.find_by_cyc_mn_cust_key">
        <value>
			select * from DWADM.DW_LNCUSTREL where CYC_MN=? AND CUST_KEY=?
        </value>
    </entry>
	<entry key="DW_LNCUSTREL.find">
        <value>
			select * from DWADM.DW_LNCUSTREL where CYC_MN=? AND CUST_KEY like ? AND REL_FLAG like ?
        </value>
    </entry>	
	<entry key="DW_LNCUSTREL.count">
        <value>
			select count(*) as CNT from DWADM.DW_LNCUSTREL where CYC_MN=? AND CUST_KEY !=? AND REL_FLAG like ?
        </value>
    </entry>
	<entry key="DW_LNCUSTREL.top_dup_cnt">
        <value>
			select distinct text, dup_cnt from DWADM.DW_LNCUSTREL where cyc_mn=? and rel_flag=? and dup_cnt>? order by dup_cnt desc
        </value>
    </entry>
	
	<entry key="DW_LNCUSTBR.find">
        <value>
			select distinct CYC_MN, CUST_KEY, BR_NO
 			from DWADM.DW_LNCUSTBR 
			where CYC_MN=? 
        </value>
    </entry>
	
	<entry key="DW_LNCUSTBR.count">
        <value>
			select count(*) as CNT from (
			select distinct CYC_MN, CUST_KEY, BR_NO
 			from DWADM.DW_LNCUSTBR 
			where CYC_MN=? 
			) t1
        </value>
    </entry>
	<entry key="DW_BAM095_CC.getD63LnNosBank">
		<value>
			<![CDATA[
				WITH TMP_01(S_ID) AS (Select DISTINCT IDN_BAN FROM  DWADM.DW_KRM040_CC WHERE IDN_BAN = ?), bam095 AS 
				(
				SELECT BANID,BANK_CODE,ACCOUNT_CODE2,ACCOUNT_CODE,CONTRACT_AMT,LOAN_AMT,PASS_DUE_AMT,
				(INTEGER(SUBSTR(RESP_DATE,1,4)) ) ||'-'|| SUBSTR(RESP_DATE ,5,2) ||'-'|| SUBSTR(RESP_DATE ,7,2) AS QDate
				  FROM DWADM.DW_bam095_CC
				),TMP_02 AS
				(
				  SELECT A.S_ID,COUNT(DISTINCT(BANK)) AS BANK_COUNT,SUM(BANK_1) AS BANK_2
				  FROM TMP_01 A 
				  LEFT JOIN 
				      (SELECT DISTINCT S_ID,SUBSTR(BANK_CODE,1,3)AS BANK
				         FROM TMP_01 A LEFT JOIN  BAM095 B ON A.S_ID=B.banID AND SUBSTR(BANK_CODE,1,3) <> '017' AND ACCOUNT_CODE2 <> 'S' AND ACCOUNT_CODE <> 'Z' AND CONTRACT_AMT >=0 AND (LOAN_AMT+PASS_DUE_AMT) >0) B ON A.S_ID =B.S_ID 
				  LEFT JOIN 
				      (SELECT S_ID,(CASE WHEN BANK_CODE IS NULL THEN NULL ELSE 0 END) AS BANK_1
				        FROM TMP_01 A LEFT JOIN   BAM095 B ON A.S_ID =B.banID
				       ) C ON A.S_ID =C.S_ID
				  GROUP BY A.S_ID
				 ), TMP_03 AS 
				(
				  SELECT S_ID,
				      (CASE WHEN BANK_2 IS NULL THEN NULL
				       ELSE BANK_COUNT END )AS D63
				  FROM TMP_02 
				)
				SELECT S_ID,D63,
				       (CASE WHEN D63 =0 OR D63 IS NULL  THEN  26.9028
				             WHEN D63 =1 THEN -10.4257
				             WHEN D63 =2 THEN -33.2014
				             WHEN D63 =3 THEN -55.1463
				             WHEN D63 >3 THEN -85.8243
				         ELSE 99999 END) AS D63_SCORE 
				FROM TMP_03]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getAllCc6RcUseBank">
		<value>
			<![CDATA[
			WITH TMP_01(S_ID) AS 
				(Select DISTINCT IDN_BAN FROM  DWADM.DW_KRM040_CC WHERE IDN_BAN = ?)
				, KRM040 AS 
				(
				SELECT DWADM.DW_KRM040_CC.* ,
				(INTEGER(SUBSTR(RESP_DATE,1,4)) ) ||'-'|| SUBSTR(RESP_DATE ,5,2) ||'-'|| SUBSTR(RESP_DATE ,7,2) AS QDate
				  FROM DWADM.DW_KRM040_CC
				)
				,TMP_02 AS 
				(
				   SELECT A.S_ID,SUM(A11_COUNT) AS A11
				     FROM TMP_01 A LEFT JOIN 
				     (  
				        SELECT DISTINCT A.S_ID,ISSUE ,
				           (CASE WHEN SUBSTR(CHAR(DATE(B.QDATE)-6 MONTH),1,7)> 
				                      SUBSTR(CHAR(INT(SUBSTR(B.BILL_DATE,1,3))+1911),1,4) || '-' ||
				                      SUBSTR(B.BILL_DATE,4,2) THEN 0 
				                 WHEN REVOL_BAL=0 THEN  0 
				                 WHEN REVOL_BAL >20000 THEN 1
				                 WHEN REVOL_BAL >0 AND PERM_LIMIT >0 AND (REVOL_BAL/(PERM_LIMIT*1000) >0.1) THEN 1
				                 WHEN REVOL_BAL >0 AND REVOL_BAL <=20000 AND PERM_LIMIT >0 AND (REVOL_BAL/(PERM_LIMIT*1000) <= 0.1)  THEN 0
				                 WHEN REVOL_BAL >0 AND PERM_LIMIT =0 THEN 1         
				            ELSE NULL END )AS A11_COUNT
				           FROM TMP_01 A  LEFT JOIN KRM040  B ON A.S_ID =B.IDN_BAN
				           WHERE ISSUE <> 'TOT'
				     )B ON A.S_ID = B.S_ID 
				   GROUP BY A.S_ID
				)        
				SELECT S_ID,A11,
				       (CASE WHEN A11=0 THEN 15.5670 
				             WHEN A11=1 THEN -10.2280
				             WHEN A11=2 THEN -19.8304
				             WHEN A11>2 THEN -38.1277
				             WHEN A11 IS NULL THEN -4.8257
				        ELSE 99999 END ) AS A11_SCORE
				FROM TMP_02
			]]>
		</value>
	</entry>
	<entry key="DW_BAM095_CC.getD07_G_V_1_3">
		<value>
			<![CDATA[
			WITH TMP_01(S_ID) AS 
			(Select DISTINCT IDN_BAN FROM  DWADM.DW_KRM040_CC WHERE IDN_BAN = ?)
			 , bam095 AS 
			(
			SELECT DWADM.DW_bam095_CC.* ,
			(INTEGER(SUBSTR(RESP_DATE,1,4)) ) ||'-'|| SUBSTR(RESP_DATE ,5,2) ||'-'|| SUBSTR(RESP_DATE ,7,2) AS QDate
			  FROM DWADM.DW_bam095_CC
			)
			 ,TMP_02 AS 
			(
			   SELECT A.S_ID,D07
			   FROM TMP_01 A
			     LEFT JOIN 
			          (SELECT S_ID,
			                  SUM(CASE WHEN ACCOUNT_CODE2 <> 'S' AND ACCOUNT_CODE <> 'Z'  
			                           THEN (LOAN_AMT+PASS_DUE_AMT) ELSE NULL END) AS D07
			             FROM TMP_01 A  LEFT JOIN bam095 B ON  A.S_ID = B.banID
			          
			           GROUP BY S_ID
			           )B ON A.S_ID = B.S_ID
			)
			SELECT S_ID,
			       D07,
			       (CASE WHEN D07 <= 50  OR  D07 IS NULL   THEN 19.9653
			             WHEN D07 >  50  AND D07  <= 900   THEN -14.7824
			             WHEN D07 >  900 AND D07  <= 2200  THEN -25.5142
			             WHEN D07 >  2200  THEN -27.7653
			        ELSE 9999999
			        END) AS D07_SCORE
			FROM TMP_02]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getP68P19_Q">
		<value>
			<![CDATA[
				WITH TMP_01(S_ID) AS 
					(Select DISTINCT IDN_BAN FROM  DWADM.DW_KRM040_CC WHERE IDN_BAN = ? ) 
					, KRM040 AS 
					(
					SELECT DWADM.DW_KRM040_CC.* ,
					(INTEGER(SUBSTR(RESP_DATE,1,4)) ) ||'-'|| SUBSTR(RESP_DATE ,5,2) ||'-'|| SUBSTR(RESP_DATE ,7,2) AS QDate
					  FROM DWADM.DW_KRM040_CC
					)
					,TMP_02 AS 
					(
					   SELECT A.IDN_BAN,
					          A.QDATE,
					          (SUM(CASE WHEN SUBSTR(CHAR(DATE(A.QDATE)-6 MONTH),1,7)> 
					                         SUBSTR(CHAR(INT(SUBSTR(A.BILL_DATE,1,3))+1911),1,4) || '-' || SUBSTR(A.BILL_DATE,4,2) THEN 0
					                    WHEN PAY_STAT ='2' THEN 1 
					                    WHEN PAY_STAT ='3' THEN 1
					                    WHEN PAY_STAT ='4' THEN 1
					                    ELSE 0 END))     AS P68
					   FROM   KRM040   AS A,
					          TMP_01         AS B
					   WHERE  A.IDN_BAN = B.S_ID
					     AND  ISSUE <> 'TOT'
					    GROUP BY 
					          A.IDN_BAN,
					          A.QDATE
					),
					TMP_03 AS 
					(
					  SELECT A.IDN_BAN,
					          A.QDATE,
					          (SUM(CASE WHEN PAY_STAT ='1' AND PAY_CODE ='N' THEN 1 
					                    WHEN PAY_STAT ='1' AND PAY_CODE ='0' THEN 1
					                    ELSE 0 END)) AS P19
					   FROM   KRM040   AS A,
					          TMP_01         AS B
					   WHERE  A.IDN_BAN = B.S_ID
					   AND    ISSUE <> 'TOT'
					   GROUP BY 
					          A.IDN_BAN,
					          A.QDATE
					)
					SELECT S_ID, P68, P19,
					       (CASE WHEN P68 <=3.5 AND P19 <= 6.5 THEN 8.4214
					             WHEN P68 <=3.5 AND P19 > 6.5  THEN 26.6097
					             WHEN P68 >3.5  AND P19 <=6.5  THEN -46.5567
					             WHEN P68 >3.5  AND P19 >6.5   THEN -46.5567
					             WHEN P68 IS NULL AND P19 IS NULL    THEN -7.8973
					        ELSE 0 END) AS P68_P19_SCORE
					     
					FROM   TMP_01 A LEFT JOIN TMP_02 B ON S_ID = B.IDN_BAN
					                LEFT JOIN TMP_03 C ON S_ID = C.IDN_BAN
			]]>
		</value>
	</entry> 
	<entry key="DW_VAM106_CC.getVAM106Data">
		<value>
			<![CDATA[select * from dwadm.DW_VAM106_CC where BANID = ? and MAINCODE IN ('A','B','C','7','8')]]>
		</value>
	</entry>
	<entry key="DW_VAM107_CC.getVAM107Data">
		<value>
			<![CDATA[select * from dwadm.DW_VAM107_CC where BANID = ? and MAINCODE IN ('A','B','C','7','8')]]>
		</value>
	</entry>
	<entry key="DW_VAM108_CC.getVAM108Data">
		<value>
			<![CDATA[select * from dwadm.DW_VAM108_CC where BANID = ? and MAINCODE IN ('A','B','C','7','8')]]>
		</value>
	</entry>
	<entry key="DW_BAM095_CC.getBAM087DelayPayLoan">
		<value>
			<![CDATA[select PAY_CODE_12 from dwadm.DW_BAM095_CC where BANID = ?]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getKRM040CardPayCode2">
		<value>
			<![CDATA[select IDN_BAN from dwadm.DW_KRM040_CC where IDN_BAN = ?  
			  			and PAY_STAT = '2' and PAY_CODE >= '1' and PAY_CODE != 'N' and PAY_CODE != 'X' 
						group by IDN_BAN HAVING COUNT(*) > 1]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getKRM040CardPayCode3">
		<value>
			<![CDATA[select IDN_BAN from dwadm.DW_KRM040_CC where IDN_BAN = ?  
						  and PAY_STAT = '3' 
						  group by IDN_BAN HAVING COUNT(*) > 1]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getKRM040CardPayCode4">
		<value>
			<![CDATA[select DISTINCT substr(bill_date,1,5)|| '01' AS BILL_DATE1 from dwadm.DW_KRM040_CC
						where  IDN_BAN = ? and PAY_STAT = '4' 
						order by BILL_DATE1]]>
		</value>
	</entry>
	<entry key="DW_KRM040_CC.getKRM040CashAdvance">
		<value>
			<![CDATA[select count(issue) as Counts from dwadm.DW_KRM040_CC where IDN_BAN = ? and CASH_LENT > 0 and ISSUE != 'TOT']]>
		</value>
	</entry>
	<entry key="DW_BAM095_CC.getBAM087CashCard">
		<value>
			<![CDATA[select PAY_CODE_12 from dwadm.DW_BAM095_CC where BANID = ? and account_code = 'Y']]>
		</value>
	</entry>
	
	<entry key="OTS_CSOVS.getElDeleteFileList">
        <value>
        	<![CDATA[
			SELECT * FROM ( 
			    SELECT ROW_NUMBER() OVER (PARTITION BY CUST_KEY ORDER BY CMST_CLOSE_DATE ASC) AS SEQ,DWADM.OTS_CSOVS.* 
			    FROM DWADM.OTS_CSOVS
			) AS T1
			WHERE SEQ = 1 AND CMST_CLOSE_DATE <> ''0001-01-01'' AND STUS=''C'' {0}
			]]>   
        </value>
    </entry>
	
	<!--J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)-->
	<entry key="OTS_CSFACT_LIST.getMaxCynMn">
        <value>
			select max(CYC_MN) AS CYC_MN from dwadm.OTS_CSFACT_LIST where CUST_KEY = '999999'
        </value>
    </entry>
	
	<!--J-108-0143_05097_B1001 Web e-Loan國內外企金額度明細表簽報性質新作時加註(新客戶往來原有客戶往來)-->
	<entry key="OTS_CSFACT_LIST.getByCustKeyAndCycMn">
        <value>
			select * from dwadm.OTS_CSFACT_LIST where CUST_KEY = ? AND CYC_MN = ?
        </value>
    </entry>
	
	<!--J-108-0268 覆審案件  客戶逾期情形_本金-->
	<entry key="DW_LNF253_DFTOTS.getCapOvDaysByDatadate">
        <value>
        	<![CDATA[
			SELECT (DAYS(A.DATA_DATE) - DAYS(CAP_OV_DATE)) AS CAP_OV_DAYS,CAP_OV_DATE,DATA_DATE
			FROM DWADM.DW_LNF253_DFTOTS A
			WHERE A.DATA_DATE BETWEEN ? AND ? 
					AND A.CAP_OV_DATE IS NOT NULL AND A.CAP_OV_DATE <> '0001-01-01' 
			AND A.CUST_ID= ? AND A.CUST_DUP_NO= ?
			ORDER BY CAP_OV_DAYS DESC,CAP_OV_DATE DESC
			]]> 
        </value>
    </entry>
	<!--J-108-0268 覆審案件  客戶逾期情形_利息-->
	<entry key="DW_LNF253_DFTOTS.getIntOvDaysByDatadate">
        <value>
			<![CDATA[
			SELECT (DAYS(A.DATA_DATE) - DAYS(INT_OV_DATE)) AS INT_OV_DAYS,INT_OV_DATE,DATA_DATE
			FROM DWADM.DW_LNF253_DFTOTS A
			WHERE A.DATA_DATE BETWEEN ? AND ? 
					AND A.INT_OV_DATE IS NOT NULL AND A.INT_OV_DATE <> '0001-01-01'
			AND A.CUST_ID= ? AND A.CUST_DUP_NO= ?
			ORDER BY INT_OV_DAYS DESC,INT_OV_DATE DESC
			]]>
        </value>
    </entry>
	
	
	<!--M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料-->
	<entry key="DW_COSTSHARE_LMS01.deleteByMainId">
        <value>
			DELETE FROM DWADM.DW_COSTSHARE_LMS01 WHERE MAINID = ?
        </value>
    </entry>
	
	<!--M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料-->
	<entry key="DW_COSTSHARE_LMS02.deleteByMainId">
        <value>
			DELETE FROM DWADM.DW_COSTSHARE_LMS02 WHERE MAINID = ?
        </value>
    </entry>
	
	<!--M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料-->
	<entry key="DW_COSTSHARE_LMS03.deleteByMainId">
        <value>
			DELETE FROM DWADM.DW_COSTSHARE_LMS03 WHERE MAINID = ?
        </value>
    </entry>
	
	<!--M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料-->
	<entry key="DW_COSTSHARE_LMS04.deleteByMainId">
        <value>
			DELETE FROM DWADM.DW_COSTSHARE_LMS04 WHERE CNTRMAINID = ?
        </value>
    </entry>
	
	<!--M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料-->
	<!--上傳授信審查(企、消金)-->
    <entry key="DW_COSTSHARE_LMS01.insert">
        <value>
			INSERT INTO DWADM.DW_COSTSHARE_LMS01
			(
				CNTRNO,CASEBRID,CASETYPE,CASELVL,
				COLLTYPE,CASENO,ENDDATE,MAINID,
				CNTRMAINID,AREABRID,APPROVEDATE,UPDATER,
				UPDATETIME,DELETETIME
            ) 
			VALUES 
			(
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				?, ?, ?, ?,
				CURRENT TIMESTAMP, ?
			)
        </value>
    </entry>	
	
	<!--M-108-0296_05097_B1001 Web e-Loan配合總處經費分攤提供所需資料-->
	<!--上傳授信覆審(企、消金)-->
    <entry key="DW_COSTSHARE_LMS02.insert">
        <value>
			INSERT INTO DWADM.DW_COSTSHARE_LMS02
			(
				CNTRNO,OWNBRID,CASETYPE,CASELVL,
				COLLTYPE,RETRIALDATE,PROJECTNO,ENDDATE,
				CASENO,MAINID,RPTMAINID,UPDATER,
				UPDATETIME,DELETETIME
            ) 
			VALUES 
			(
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				CURRENT TIMESTAMP, ?
			)
        </value>
    </entry>
	
	
	<!--M-108-0296_05097_B1003 Web e-Loan配合總處經費分攤提供所需資料-->
	<!--上傳授信審查(企、消金)-->
    <entry key="DW_COSTSHARE_LMS03.insert">
        <value>
			INSERT INTO DWADM.DW_COSTSHARE_LMS03
			(
				CASENO,CASEBRID,CASETYPE,CASELVL,
				DOCCODE,ENDDATE,MAINID,AREABRID,
				APPROVEDATE,UPDATER,UPDATETIME,DELETETIME
            ) 
			VALUES 
			(
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				?, ?,CURRENT TIMESTAMP, ?
			)
        </value>
    </entry>	
	
	
	<!--J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表-->
	<!--衍生性商品交易-->
	<!--
	    DW資料來源:
		IDTR0024規則
		PROD_LINE  IN ('FXOPT','FXOPT_OLD','IRS','CCS','IRO','IRS_NOM','CCS_NOM','NDF_HEDGE','NDF_NONHG','NDF_OLD','NDF_CRED')	
		額度:LMT_AMT
		餘額:EXPOSURE
	-->
    <entry key="OTS_SMTLMT.selectByCustKey">
        <value>
			SELECT CUST_KEY,CURR,SUM(LMT_AMT) AS FACTAMT,SUM(EXPOSURE) AS BAL FROM dwadm.OTS_SMTLMT WHERE 
			CUST_KEY = ? AND 
			PROD_LINE  IN ('FXOPT','FXOPT_OLD','IRS','CCS','IRO','IRS_NOM','CCS_NOM','NDF_HEDGE','NDF_NONHG','NDF_OLD','NDF_CRED') AND
			CYC_DT = (SELECT MAX(CYC_DT) FROM  dwadm.OTS_SMTLMT WHERE CUST_KEY = '99999999999' )
			GROUP BY CUST_KEY,CURR
        </value>
    </entry>
		
	<entry key="OTS_RKCREDITOVS.fixCHKDATE_CHKEMPNO">
        <value>
			SELECT * FROM DWADM.OTS_RKCREDITOVS where (br_cd like ?) AND (noteId like ?) AND (cntrNo like ?) AND chkdate is null  
        </value>
    </entry>	
	
	
	
	<!--M-108-0296_05097_B1004 Web e-Loan配合總處經費分攤提供所需資料-->
	<!--上傳授信審查聯貸比例(企、消金)-->
    <entry key="DW_COSTSHARE_LMS04.insert">
        <value>
			INSERT INTO DWADM.DW_COSTSHARE_LMS04
			(
				CNTRMAINID,CNTRNO,SHAREBRID,SHAREFLAG,
				SHARERATE1,SHARERATE2,SHAREAMT,TOTALAMT,
				SHARENO,SNOKIND,CURRENTAPPLYCURR,CURRENTAPPLYAMT,
				UPDATER,UPDATETIME,DELETETIME
            ) 
			VALUES 
			(
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				?, ?, ?, ?, 
				?, CURRENT TIMESTAMP, ?
			)
        </value>
    </entry>	
	
	
	<!--J-108-0288_05097_B1003 Web e-Loan授信系統新增合併關係企業額度彙總表-->
	<!--其他具相同地址、電話等原因之關係企業-->
	<!--
	  DWADM.DW_LNCUSTREL_O.REL_FLAG
	  1.地址
      2.電話
	-->
	<entry key="findRelatedCompanyWithSameTELAndAddr">
        <value>
        	WITH tmp 
			     AS (
				SELECT * 
				FROM   DWADM.DW_LNCUSTREL_O 
				WHERE  TEXT IN (SELECT TEXT 
				                FROM   DWADM.DW_LNCUSTREL_O 
				                WHERE  CUST_ID = ? 
				                       AND CUST_DUP_NO = ?
				                       AND CYC_MN = (SELECT MAX(CYC_MN) 
				                                     FROM   DWADM.DW_LNCUSTREL_O)) 
									   AND LENGTH(REPLACE(TRIM(TEXT), '0', '' )) > 0
                                       AND LENGTH(TRIM(TEXT)) >= 7			 
				       AND NOT( CUST_ID = ? 
				                AND CUST_DUP_NO = ? ) 
					   AND CYC_MN=(SELECT MAX(CYC_MN) FROM   DWADM.DW_LNCUSTREL_O) 			
				UNION ALL 
				SELECT NULL       AS CYC_MN, 
				       NULL       AS CUST_KEY, 
				       REL_ID    AS CUST_ID, 
				       REL_DUPNO AS CUST_DUP_NO, 
				       REL_FLAG, 
				       TEXT, 
				       NULL       AS REL_UPD, 
				       NULL       AS DUP_CNT, 
				       NULL       AS DW_LST_DATA_SRC, 
				       NULL       AS DW_DATA_SRC_DT, 
				       NULL       AS DW_LST_MNT_DT 
				FROM   DWADM.DW_OTHER_REL 
				WHERE  REL_FLAG = '3' 
				       AND  MAIN_ID = ? 
				       AND MAIN_DUPNO = ? 
				       AND DELETETIME IS NULL
				UNION ALL 
				SELECT NULL       AS CYC_MN, 
				       NULL       AS CUST_KEY, 
				       MAIN_ID    AS CUST_ID, 
				       MAIN_DUPNO AS CUST_DUP_NO, 
				       REL_FLAG, 
				       TEXT, 
				       NULL       AS REL_UPD, 
				       NULL       AS DUP_CNT, 
				       NULL       AS DW_LST_DATA_SRC, 
				       NULL       AS DW_DATA_SRC_DT, 
				       NULL       AS DW_LST_MNT_DT 
				FROM   DWADM.DW_OTHER_REL 
				WHERE  REL_FLAG = '3' 
				       AND REL_ID = ? 
				       AND REL_DUPNO = ? 
				       AND DELETETIME IS NULL
			) 
			SELECT t0.CUST_ID AS TCUSTID,t0.CUST_DUP_NO AS TDUPNO,'' AS TCNAME,'15' AS SEQ,'' AS PCUSTID ,'' AS PDUPNO,t0.TEXT AS PCNAME,t1.REMARK,t1.CREATETIME,t1.APPROVETIME
			FROM tmp t0 left join DWADM.DW_OTHER_REL t1
			ON 
			(t0.CUST_ID = t1.MAIN_ID AND t0.CUST_DUP_NO=t1.MAIN_DUPNO
			AND t1.REL_ID= ? AND t1.REL_DUPNO=?
			AND t1.DELETETIME IS NULL
			) 
			or (t0.CUST_ID=t1.REL_ID AND t0.CUST_DUP_NO=t1.REL_DUPNO 
			AND t1.MAIN_ID= ? AND t1.MAIN_DUPNO=?
			AND t1.DELETETIME IS NULL
			)
			WHERE t1.REMARK IS NULL
			ORDER  BY t0.REL_FLAG, 
			          t0.TEXT, 
			          t0.CUST_ID
        </value>
    </entry>
	
	<!--J-109-0115_10702_B1001 Web e-Loan管理報表新增分行「授信業務異常通報月報表」-->
	<!--抓未關戶之海外分行客戶， STUS != 'C' 代表未關戶-->
	<entry key="OTS_CSOVS.selByCustIdAndBrNoWithoutClose">
        <value>
			SELECT *
			FROM  DWADM.OTS_CSOVS
			WHERE  STUS != 'C' AND BR_CD = ? AND CUST_KEY=?
        </value>
    </entry>

	<entry key="IDDP0001.getAccount">
		<value>
			SELECT MR_PB_ACT_NO, MR_PB_TAX_NO,MR_PB_ACT_STATUS
			FROM DWADM.IDDP0001
			WHERE MR_PB_ID_NO = ? AND MR_PB_CURR_CODE='00' AND MR_PB_MSAC_FLAG NOT IN ('1', '2', '3') and MR_PB_ACT_STATUS NOT IN ('02','03')
		</value>
	</entry>
	
	<entry key="IDDP0001.getAccount_laborContract">
		<value>
			SELECT MR_PB_ACT_NO , MR_PB_MSAC_FLAG
			FROM DWADM.IDDP0001
			WHERE MR_PB_ID_NO = ? AND MR_PB_CURR_CODE='00' 
		</value>
	</entry>
	
	<entry key="IDDP0001.getAccount_laborContract_2">
		<value>
			SELECT MR_PB_ACT_NO , MR_PB_MSAC_FLAG
			FROM DWADM.IDDP0001
			WHERE MR_PB_ID_NO = ? AND MR_PB_CURR_CODE='00' AND MR_PB_ID_DUP_NO = ? 
		</value>
	</entry>

	<!--抓是否屬凍結額度國家-->
	<entry key="DW_CTYRKANY.selLastType2">
        <value>
			 SELECT CTYCODE,CTYNAME FROM DWADM.DW_CTYRKANY WHERE TYPE = '2' AND DATADATE = ( SELECT max(DATADATE) FROM DWADM.DW_CTYRKANY WHERE TYPE = '2' ) order by CTYCODE ASC
        </value>
    </entry>
	
	
	<!--抓全國營業(稅籍)登記資料-->
	<entry key="OTS_DW_BGMOPEN.selByIdNo">
        <value>
			 SELECT BUS_ADDRS, CYC_DT, ID_NO, HEAD_ID_NO, ENTITY_NAME, CAPITAL, CREATE_DT, ORG_TYPE_NAME, USE_INVOICE, INDUS_CD, INDUS_NM FROM DWADM.OTS_DW_BGMOPEN WHERE CYC_DT =	(SELECT MAX(CYC_DT) FROM DWADM.OTS_DW_BGMOPEN WHERE ID_NO = '999' AND CYC_DT IS NOT NULL ) AND ID_NO = ? 
        </value>
    </entry>
	
	
	<!--抓國家限額資料-->
	<entry key="OTS_DW_CNTRY_C1.selByCntryCd">
        <value>
			 SELECT * FROM dwadm.OTS_DW_CNTRY_C1 WHERE CYC_MN IN (SELECT max(CYC_MN) AS MAX_CYC_MN FROM dwadm.OTS_DW_CNTRY_C1 WHERE LVL = 999) AND CNTRY_CD IN ({0}) ORDER BY LVL ASC, RISK_ORDER ASC  
        </value>
    </entry>
	
	<!--M-110-0227 抓FTP利率-->
	<entry key="OTS_FTPINSRT.selByCurr">
		<value>
			SELECT * FROM DWADM.OTS_FTPINSRT WHERE INS_DT = (SELECT MAX(INS_DT) FROM DWADM.OTS_FTPINSRT WHERE AP_TYPE = 'LN' AND CUR_CD IN (?)) AND AP_TYPE = 'LN' AND CUR_CD IN (?) ORDER BY CUR_CD, INS_CD
		</value>
	</entry>
	
	<!--J-112-0225_05097_B1001 Web e-Loan企金授信調整BIS評估表FTP引用資料-->
	<entry key="OTS_FTPINSRT.selByCurr.lastMonth">
		<value>
			<![CDATA[
			SELECT * FROM DWADM.OTS_FTPINSRT WHERE INS_DT = (SELECT MAX(INS_DT) FROM DWADM.OTS_FTPINSRT WHERE AP_TYPE = 'LN' AND CUR_CD IN (?) AND INS_DT <=  (date(LEFT(CURRENT DATE,8 ) || '01')  - 1)    ) AND AP_TYPE = 'LN' AND CUR_CD IN (?) ORDER BY CUR_CD, INS_CD
			]]>
		</value>
	</entry>
	
	
	<!--抓外部信評最新資料日期-->
	<entry key="DW_BSL2ECRSCR.selMaxCycMn">
        <value>
			 SELECT MAX(CYC_MN) AS MAXCYC_MN FROM dwadm.DW_BSL2ECRSCR   
        </value>
    </entry>
	
	
	<!--抓外部信評資料-->
	<entry key="DW_BSL2ECRSCR.selByCustKeyAndCycMn">
        <value>
			 SELECT * FROM dwadm.DW_BSL2ECRSCR where CUST_KEY = ? and CYC_MN = ?  and SCORE != 90 
        </value>
    </entry>
	
	<!--J-109-0496 貸後管理 理財商品贖回追蹤-->
    <entry key="DW_LNWM_CFM.selCFM">
        <value>
            <![CDATA[
		   		SELECT  CFM.CNTRNO, CFM.LOANNO, MNT.CUST_ID, MNT.CUST_DUP_NO, MNT.PRO_TYPE, MNT.BANK_PRO_CODE, MNT.ACC_NO, MNT.LST_SELL_DT, MNT.LST_SELL_AMT, MNT.TRAN_TYPE, MNT.DATA_DT, COUNT(*) COUNT
				FROM dwadm.OTS_DW_LNWM_CFM CFM
				LEFT JOIN dwadm.OTS_DW_LNWM_MNT MNT ON CFM.CUST_ID = MNT.CUST_ID and CFM.CUST_DUP_NO = MNT.CUST_DUP_NO and CFM.PRO_TYPE = MNT.PRO_TYPE and CFM.BANK_PRO_CODE = MNT.BANK_PRO_CODE and CFM.ACC_NO = MNT.ACC_NO
				WHERE  (MNT.LST_SELL_DT <> '0001-01-01' OR (CFM.PRO_TYPE='INS' and CFM.TRAN_TYPE NOT IN ('C1','CG','CC','C2','C3','C4','CD','CF') and CFM.TRAN_TYPE NOT LIKE 'A%')) and CFM.BATCH_DT is null and MNT.LST_SELL_DT <> CFM.LST_SELL_DT 
				group by CFM.CNTRNO, CFM.LOANNO,MNT.CUST_ID,MNT.CUST_DUP_NO, MNT.PRO_TYPE, MNT.BANK_PRO_CODE, MNT.ACC_NO, MNT.LST_SELL_DT, MNT.LST_SELL_AMT, MNT.TRAN_TYPE, MNT.DATA_DT 
			]]>
        </value>
    </entry>
	<entry key="DW_LNWM_CFM.selUnid">
        <value>
            <![CDATA[
		   		SELECT unid FROM dwadm.OTS_DW_LNWM_CFM WHERE  CUST_ID = ? and CUST_DUP_NO = ? and PRO_TYPE = ? and BANK_PRO_CODE = ? and ACC_NO = ?    
			]]>
        </value>
    </entry>
	<entry key="DW_LNWM_CFM.Update">
        <value>
			UPDATE dwadm.OTS_DW_LNWM_CFM 
			SET LST_SELL_DT = ?, LST_SELL_CUR_CD = ?, LST_SELL_AMT = ?, LST_SELL_BR_CD = ?, TRAN_TYPE = ?, DATA_DT = ?, BATCH_DT = ?
			WHERE UNID = ? AND CUST_ID = ? AND CUST_DUP_NO = ? AND PRO_TYPE = ? AND BANK_PRO_CODE = ? AND ACC_NO = ? 
        </value>
    </entry>
	<!--抓理財商品資料-->
	<entry key="OTS_DW_LNWM_MNT.selById">
        <value>
			 SELECT * FROM DWADM.OTS_DW_LNWM_MNT WHERE CUST_ID = ? AND CUST_DUP_NO = ? AND LST_BUY_DT > ?
        </value>
    </entry>
	<entry key="OTS_DW_LNWM_MNT.selByKey">
        <value>
			 SELECT * FROM DWADM.OTS_DW_LNWM_MNT WHERE CUST_ID = ? AND CUST_DUP_NO = ? AND PRO_TYPE = ? AND BANK_PRO_CODE = ? AND ACC_NO = ?
        </value>
    </entry>
	
	<!--抓已確認的理財商品資料-->
	<entry key="OTS_DW_LNWM_CFM.selByUnid">
        <value>
			 SELECT * FROM DWADM.OTS_DW_LNWM_CFM WHERE UNID = ?
        </value>
    </entry>
	<entry key="OTS_DW_LNWM_CFM.selByKey">
        <value>
			 SELECT * FROM DWADM.OTS_DW_LNWM_CFM WHERE UNID = ? AND CUST_ID = ? AND CUST_DUP_NO = ? AND PRO_TYPE = ? AND BANK_PRO_CODE = ? AND ACC_NO = ?
        </value>
    </entry>
	
	<!-- J-110-0104 分行敘做永慶信義住商仲介房貸引介統計表  替換 932 為 931 -->
	<entry key="OTS_CRDLN031.change_AREA_CD">
        <value>
			 update DWADM.OTS_CRDLN031 set AREA_CD=? where AREA_CD=? AND BR_CD=?
        </value>
    </entry>
	
	<!--刪除已確認的理財商品資料-->
	<entry key="DWADM.OTS_DW_LNWM_CFM.delete">
        <value>
            DELETE FROM DWADM.OTS_DW_LNWM_CFM WHERE UNID = ? AND CUST_ID = ? AND CUST_DUP_NO = ? AND PRO_TYPE = ? AND BANK_PRO_CODE = ? AND ACC_NO = ?
        </value>
    </entry>
	
	
	<!--取得國內海外聯貸額度資訊-->
	<!--J-113-0033_05097_B1001 Web e-Loan配合DW ETL資料品質優化，修改DW_ASLNQUOT改參照為DW_LNF022-->
	<entry key="DWADM.DW_LNF273.selectByCntrNo">
        <value>
            select DISTINCT a.doc_no,  b.contract AS  contract, (SYND_OV_NT+SYND_AMT_NT) as LOAN_PART, (UNION_OV_NT+UNION_AMT_NT) as LOAN_TOTAL ,
				(SYND_OV_NT+SYND_AMT_NT) / (UNION_OV_NT+UNION_AMT_NT) as UNION_RATE
			from dwadm.DW_LNF273 a
			inner join dwadm.DW_LNF022 b on a.DOC_NO = b.DOC_NO  
			where b.contract = ? AND a.CYC_MN IN (SELECT max(CYC_MN) FROM dwadm.DW_LNF273)
			UNION
			select DISTINCT a.doc_no,  b.LINE_NO AS contract, (SYND_OV_NT+SYND_AMT_NT) as LOAN_PART, (UNION_OV_NT+UNION_AMT_NT) as LOAN_TOTAL ,
				(SYND_OV_NT+SYND_AMT_NT) / (UNION_OV_NT+UNION_AMT_NT) as UNION_RATE
			from dwadm.DW_LNF273 a
			inner join dwadm.DW_LNCNTROVS b on a.DOC_NO = b.DOCUMENT_NO  
			where b.LINE_NO = ? AND a.CYC_MN IN (SELECT max(CYC_MN) FROM dwadm.DW_LNF273)
        </value>
    </entry>
	
	<!--上傳已確認的理財商品資料-->
    <entry key="DWADM.OTS_DW_LNWM_CFM.insert">
        <value>
			INSERT INTO DWADM.OTS_DW_LNWM_CFM
			(UNID, CNTRNO, LOANNO, UPD_DATE, CUST_ID, CUST_DUP_NO, CUST_CNM, 
			PRO_TYPE, BANK_PRO_CODE, BANK_PRO_NAME, ACC_NO, 
			LST_BUY_DT, LST_BUY_CUR_CD, LST_BUY_AMT, LST_BUY_BR_CD, 
			LST_SELL_DT, LST_SELL_CUR_CD, LST_SELL_AMT, LST_SELL_BR_CD, 
			TRAN_TYPE, INV_AMT, DATA_DT, BATCH_DT) 
			VALUES (?, ?, ?, ?, ?, ?, ?, 
					?, ?, ?, ?, 
					?, ?, ?, ?, 
					?, ?, ?, ?, 
					?, ?, ?, ?)
        </value>
    </entry>
	
	
	<!--刪除授信EAD共用額度分配檔-->
	<entry key="DWADM.DW_L120S21A.delete">
        <value>
            DELETE FROM DWADM.DW_L120S21A WHERE UNID = ?  
        </value>
    </entry>
	
	<!--上傳授信EAD共用額度分配檔-->
    <entry key="DWADM.DW_L120S21A.insert">
        <value>
			INSERT INTO DWADM.DW_L120S21A 
			(
				UNID,PROJECT_NO,DATA_DATE,STATUS,STATUS_DT,END_DT,
				BR_NO,CONTRACT_CO,SWFT,FACT_AMT_CO,FACT_AMT_CO_NT,
				CONTRACT,FACT_AMT_T,BAL_T,INT_AMT_T,FINAL_EAD,
				REUSE
			) 
			VALUES 
			(
				?,?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?
			)
        </value>
    </entry>
	
	
	<!--刪除授信EAD及LGD模型-主檔-->
	<entry key="DWADM.DW_L120S21B.delete">
        <value>
            DELETE FROM DWADM.DW_L120S21B WHERE UNID = ?  
        </value>
    </entry>
	
	<!--上傳授信EAD及LGD模型-主檔-->
	<!--J-111-0083_05097_B1002 Web e-Loan企金授信額度明細表新增「屬本行授信業務授權準則得單獨劃分之業務」之LGD業務分類-->
	<!--J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則-->
    <entry key="DWADM.DW_L120S21B.insert">
        <value>
			INSERT INTO DWADM.DW_L120S21B 
			(
				UNID,PROJECT_NO,CUSTID,DATA_DATE,STATUS,
				STATUS_DT,END_DT,LGD_MOWVER1,LGD_MOWVER2,EAD_MOWVER1,
				EAD_MOWVER2,BR_NO,CONTRACT,CREDIT_FLAG,GUR_FLAG,
				UNION_FLAG,SYND_AMT_NT,UNION_AMT_NT,FACT_CURR,FACT_AMT,
				FACT_AMT_T,BAL_T,INT_AMT_T,FINAL_EAD,PRO_R,
				COLL_R,CREDIT_R,UNSEC_R,EXP_R,UNDIR_COST,
				WORKOUT_LGD,WORKOUT,RESTRUCTURE_LGD,RESTRUCTURE,CURE_LGD,
				CURE,FACT_LGD,CUST_LGD,CREDIT_PT,
				BUSS_TYPE, BUSS_LGD,AUTH_TYPE,ISGUARANTOREFFECT_S21B,
				GUARANTORID_S21B,GUARANTORDUPNO_S21B,GUARANTORRNAME_S21B,GUARANTORCRDTYPE_S21B,GUARANTORCRDTYEAR_S21B,GUARANTORGRADEORG_S21B,
				GUARANTORGRADENEW_S21B,GUARANTORCPTLCURR_S21B,GUARANTORCPTLAMT_S21B,GUARANTORCPTLUNIT_S21B,GUARANTORNTCODE_S21B,
				GUARANTORSTOCKSTATUS_S21B,GUARANTORSTOCKNUM_S21B,GUARANTORCRDTYPE2_S21B,GUARANTORCRDTYEAR2_S21B,GUARANTORGRADEORG2_S21B,
				GUARANTORGRADENEW2_S21B,GUARANTORSTKCATNM_S21B,GUARANTORCRDTYPE3_S21B,GUARANTORCRDTYEAR3_S21B,GUARANTORGRADEORG3_S21B,
				GUARANTORGRADENEW3_S21B,GUARANTORSTKCATNM3_S21B
			) 
			VALUES 
			(
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,
				?,?,?,?,
				?,?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,? 
			)
        </value>
    </entry>
	
	
	<!--刪除授信EAD及LGD模型-主檔-->
	<entry key="DWADM.DW_L120S21C.delete">
        <value>
            DELETE FROM DWADM.DW_L120S21C WHERE UNID = ?  
        </value>
    </entry>
	
	<!--上傳授信EAD及LGD模型-主檔-->
    <entry key="DWADM.DW_L120S21C.insert">
        <value>
			INSERT INTO DWADM.DW_L120S21C (
				UNID,PROJECT_NO,DATA_DATE,STATUS,STATUS_DT,
				END_DT,BR_NO,CONTRACT,COLKIND,COLCURR,
				COLTIMEVALUE,COLPRERGSTAMT,COLRGSTAMT,COLCOUSEFLAG,COLSHARERATE,
				COLRATE,COLESTRECOVERY,COLRGSTRECOVERY,COLRECOVERY,COLRECOVERYTWD,
				CMSBRANCH,CMSTYPECD,CMSCUSTID,CMSDUPNO,CMSCOLLNO,
				CMSOID,CMSCOLLKEY,COLLTYPE,CMSGRTRT,
				UNIONFLAG_S21C,UNIONCURR_S21C, SYNDAMT_S21C, UNIONAMT_S21C
			) 
			VALUES
			(
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,?,
				?,?,?,?,
				?,?,?,?
			)
        </value>
    </entry>
	
	<!--J-110-0485_05097_B1009_B Web e-Loan國內企金授信簽報書新增LGD試算等相關欄位與功能-->
	<!--
	WITH TBL1 AS 
			(
			  SELECT *
				FROM dwadm.DW_LNF025OVS WHERE  CONTRACT_CO IN 
				(  
					select DISTINCT CONTRACT_CO  from dwadm.DW_LNF025OVS where CONTRACT in 
					(
					  {0}
					) 
				)  
				AND  CONTRACT != '''' 
			)
			SELECT 
			t1.CONTRACT_CO, 
			t4.SWFT AS LIMIT_SWIFT, t4.FACT_AMT AS LIMIT_FACT_AMT,
			t1.CONTRACT, t1.SWFT, t1.FACT_AMT, t1.USED_AMT, t1.LNAP_FLAG
			FROM 
			TBL1 AS t1 
			LEFT OUTER JOIN 
			dwadm.DW_LNF025OVS AS t4  
			ON 
			t1.CONTRACT_CO = t4.CONTRACT_CO
			WHERE t4.CONTRACT = ''''
			ORDER BY CONTRACT_CO ASC,CONTRACT ASC
	-->		
	<entry key="DW_LNF025OVS.selCoByCntrNoForLgd">
        <value>  
			WITH TBL1 AS 
			(
			  SELECT *
				FROM dwadm.DW_LNF025OVS WHERE  CONTRACT_CO IN 
				(  
					select DISTINCT CONTRACT_CO  from dwadm.DW_LNF025OVS where CONTRACT in 
					(
					    	SELECT DISTINCT CONTRACT
								FROM dwadm.DW_LNF025OVS WHERE  CONTRACT_CO IN 
								(  
									select DISTINCT CONTRACT_CO  from dwadm.DW_LNF025OVS where CONTRACT in 
									(
									  {0}
									) 
								) 
								AND
								CONTRACT != '''' 
					) 
				)  
				AND  CONTRACT != '''' 
			)
			SELECT 
			t1.CONTRACT_CO, 
			t4.SWFT AS LIMIT_SWIFT, t4.FACT_AMT AS LIMIT_FACT_AMT,
			t1.CONTRACT, t1.SWFT, t1.FACT_AMT, t1.USED_AMT, t1.LNAP_FLAG
			FROM 
			TBL1 AS t1 
			LEFT OUTER JOIN 
			dwadm.DW_LNF025OVS AS t4  
			ON 
			t1.CONTRACT_CO = t4.CONTRACT_CO
			WHERE t4.CONTRACT = ''''
			ORDER BY CONTRACT_CO ASC,CONTRACT ASC
        </value>
    </entry>
	 
	<entry key="DW_ASLNDAVGOVS.SUM.INT_AMT_T">
        <value>
            SELECT INT_CURR,SUM(INT_AMT_T) AS INT_AMT_T  FROM 
			( 
			  SELECT CUST_KEY,FACT_CONTR,INT_CURR,INT_AMT,INT_AMT_T FROM DWADM.DW_ASLNDAVGOVS	WHERE FACT_CONTR = ? AND (CHAR(CANCEL_DT) IS NULL OR CHAR(CANCEL_DT) = '0001-01-01')		
			) GROUP BY 	INT_CURR,FACT_CONTR	
        </value>
    </entry>
	
	<entry key="DW_OTS_TRPAYLG.QUERY">
        <value>
            SELECT * FROM DWADM.OTS_TRPAYLG 
			where ID_NO like ? and (TX_ACCT_DT between ? and ?) 
			ORDER BY TX_ACCT_DT, BR_CD, TAX_NO, ACCT_KEY, TRM_ID, TX_AMT DESC	
        </value>
    </entry>
	
	 
	<!-- LGD -->
    <entry key="DW_LNF025OVS.selByCntrNoCo">
        <value>
            SELECT T1.*,T4.SWFT AS LIMIT_SWIFT, T4.FACT_AMT AS LIMIT_FACT_AMT
			FROM DWADM.DW_LNF025OVS T1
			LEFT OUTER JOIN
			DWADM.DW_LNF025OVS AS T4
			ON T1.CONTRACT_CO = T4.CONTRACT_CO 
			WHERE 
			T1.CONTRACT_CO = ? AND 
			T4.CONTRACT = ''
			ORDER BY CONTRACT_CO ASC, CONTRACT ASC
        </value>
    </entry> 
			
	<!--J-112-0170 配合常董會決議, eloan國內授信管理系統, 調整授信成本收益概算表的顯示內容-->
	<entry key="OTS_ACINSRT.findOTS_ACINSRTByColumn">
        <value>
            SELECT * FROM DWADM.OTS_ACINSRT 
			WHERE 
			CYC_DT = ? AND
			BR_CD = ? AND
			DATA_DEF = ? AND
			CUR_CD = ? AND
			DW_DEP_CD = ? AND
			DATA_TYP = ? 			
			ORDER BY BR_CD, CYC_DT asc, CUR_CD, DATA_DEF,DW_DEP_CD, DATA_TYP
        </value>
    </entry> 
	
	<!--J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度-->
	<entry key="DW_LNCNTROVS.selByCntrNoAndElLineNo">
        <value>
            WITH tmp(cntrno) AS (
			         {0}
			)
			SELECT T.cntrno
			FROM   tmp T
			       LEFT JOIN dwadm.dw_lncntrovs LL
			              ON T.cntrno = LL.line_no
			       LEFT JOIN dwadm.dw_lncntrovs LE
			              ON T.cntrno = LE.el_line_no
			       LEFT JOIN dwadm.dw_lncntrovs_c CL
			              ON T.cntrno = CL.line_no
			       LEFT JOIN dwadm.dw_lncntrovs_c CE
			              ON T.cntrno = CE.el_line_no
			WHERE  LL.line_no IS NULL
			       AND LE.el_line_no IS NULL
			       AND CL.line_no IS NULL
			       AND CE.el_line_no IS NULL	
        </value>
    </entry>
	
	<!--取得表外科目CCF (廢除LMS.L120S25B改抓DW)-->
	<entry key="OTS_BSL2ACRULE.selByAcKey">
        <value>
            select * from DWADM.OTS_BSL2ACRULE WHERE AC_KEY = ?
        </value>
    </entry>
	 
	
	<entry key="OTS_DW_BGMOPENByCustId">
        <value><![CDATA[SELECT * FROM DWADM.OTS_DW_BGMOPEN B WHERE CYC_DT = (SELECT MAX(CYC_DT) FROM DWADM.OTS_DW_BGMOPEN WHERE ID_NO='999') AND B.ID_NO =?]]></value>
    </entry>
	<entry key="OTS_DW_BGMOPENXByCustId">
        <value><![CDATA[SELECT * FROM DWADM.OTS_DW_BGMOPENX B WHERE CYC_DT = (SELECT MAX(CYC_DT) FROM DWADM.OTS_DW_BGMOPENX WHERE ID_NO='999') AND B.ID_NO =?]]></value>
    </entry>
	<entry key="OTS_DW_BGMOPEN1YByCustId">
        <value><![CDATA[SELECT * FROM DWADM.OTS_DW_BGMOPEN1Y B WHERE CYC_DT = (SELECT MAX(CYC_DT) FROM DWADM.OTS_DW_BGMOPEN1Y WHERE ID_NO='999') AND B.ID_NO =?]]></value>
    </entry>
	
	<entry key="DW_OTS_CUST_INFO.selectByDataTypeAndContent">
		<value>
			<![CDATA[
				select * from  DWADM.OTS_CUST_INFO where CONTENT is not null and CONTENT != '''' and CONTENT in ({0}) and concat(id, dup) != ?
			]]>
		</value>
	</entry>
	
	<!--J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW-->
	<entry key="DWADM.DW_ELOAN_APPLY.delete">
		<value>
			<![CDATA[
				DELETE FROM DWADM.DW_ELOAN_APPLY WHERE C122M01A_OID IN ({0})
			]]>
		</value>
	</entry>
	
	<!--J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW-->
	<entry key="DWADM.DW_ELOAN_APPLY.insert">
		<value>
			<![CDATA[
				INSERT INTO DWADM.DW_ELOAN_APPLY (
					OID, C122M01A_OID
					, PLOANCASENO, CNTRNO, DOCSTATUS, STATFLAG, OWNBRID, BRNAME, ORGBRID, ORGBRNAME
					, CUSTID, DUPNO, CUSTNAME, APPLYTS, APPLYAMT, MATURITY, PLOANPLAN, PLOANPLANDESC, REJECTCODE
					, INCOMTYPE, MARKETINGUNIT, MARKETINGUNITID, INTRODUCESRC, BIRTHDAY, EDU, MARRY, CHILD, JUID
					, COMNAME, JOBTYPE1, JOBTYPE2, JOBTITLE, CLSJOBTYPE1, CLSJOBTYPE2, CLSJOBTITLE
					, SENIORITY, PAYAMT, MARKETING_MARK, USEPLAN, USEPLANDESC, SOURCE_SYSTEM
					, CREATOR, CREATETIME, UPDATER, UPDATETIME
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			]]>
		</value>
	</entry>
	
	<!--J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW-->
	<entry key="DWADM.DW_ELOAN_REQUEST.delete">
		<value>
			<![CDATA[
				DELETE FROM DWADM.DW_ELOAN_REQUEST WHERE L120M01A_OID IN ({0})
			]]>
		</value>
	</entry>
	
	<!--J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW-->
	<entry key="DWADM.DW_ELOAN_REQUEST.insert">
		<value>
			<![CDATA[
				INSERT INTO DWADM.DW_ELOAN_REQUEST (
					OID, L120M01A_OID, PLOANCASENO, CUSTID, DUPNO, CNTRNO, CASEDATE, APPROVETIME, TERMGROUP, J10_SCORE, JUID, COMNAME
					, CLSJOBTYPE1, CLSJOBTYPE2, CLSJOBTITLE, JOBTYPE1, JOBTYPE2, JOBTITLE, SENIORITY, PAYAMT, KYCNPV, KYCUPDATER, KYCAPPROVER
					, PURPOSE, DRATE, CURRENTAPPLYAMT, LNYEAR, LNMONTH, RATE1, RATE2, BGNNUM2, FEEAMT, PCONEND2
					, BIRTHDAY, EDU, MARRY, CHILD, PRODKIND, REJECTCODE, RANK, SIMPLIFYFLAG, ITEMTYPE, SOURCE_SYSTEM, CREATOR, CREATETIME, UPDATER, UPDATETIME
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			]]>
		</value>
	</entry>
	
	<entry key="DWADM.DW_LNCNTROVS.selectByCntrNo">
		<value>
			<![CDATA[
				select * from DWADM.DW_LNCNTROVS where line_no = ?
			]]>
		</value>
	</entry>

    <!--J-113-0143 配合SBTi貸後名單通知營業單位更新，條件1.SBT議合法產業別(註1)&大型企業&本行尚有額度-->
    <entry key="DW_ESGLOANLIST_SS_1">
        <value>
            <![CDATA[
				SELECT *
FROM DWADM.DW_ESGLOANLIST_SS
WHERE CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ESGLOANLIST_SS) AND LN_TYP='1' AND AVL_FAMT_T >0 AND (BIZNAME LIKE '%0500'
   OR BIZNAME LIKE '%0600'
   OR BIZNAME LIKE '%1700'
   OR BIZNAME LIKE '%2641'
   OR BIZNAME LIKE '%2642'
   OR BIZNAME LIKE '%2643'
   OR BIZNAME LIKE '%2649'
   OR BIZNAME LIKE '%2691'
   OR BIZNAME LIKE '%2699'
   OR BIZNAME LIKE '%2711'
   OR BIZNAME LIKE '%2712'
   OR BIZNAME LIKE '%2719'
   OR BIZNAME LIKE '%2721'
   OR BIZNAME LIKE '%2729'
   OR BIZNAME LIKE '%2820'
   OR BIZNAME LIKE '%2890'
   OR BIZNAME LIKE '%3010'
   OR BIZNAME LIKE '%3030'
   OR BIZNAME LIKE '%3520'
   OR BIZNAME LIKE '%4910'
   OR BIZNAME LIKE '%4931'
   OR BIZNAME LIKE '%4940'
   OR BIZNAME LIKE '%5010'
   OR BIZNAME LIKE '%5100'
   OR BIZNAME LIKE '%5231'
   OR BIZNAME LIKE '%5232'
   OR BIZNAME LIKE '%5251'
   OR BIZNAME LIKE '%5260'
   OR BIZNAME LIKE '%5301'
   OR BIZNAME LIKE '%5302'
   OR BIZNAME LIKE '%4932'
   OR BIZNAME LIKE '%4939'
   OR BIZNAME LIKE '%5233'
   OR BIZNAME LIKE '%5259')
			]]>
        </value>
    </entry>

	<!--J-113-0143 配合SBTi貸後名單通知營業單位更新，條件2.3510電力供應業&本行尚有額度-->
	<entry key="DW_ESGLOANLIST_SS_2">
		<value>
			<![CDATA[
	SELECT *
		FROM DWADM.DW_ESGLOANLIST_SS
	WHERE CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ESGLOANLIST_SS) AND AVL_FAMT_T >0 AND BIZNAME LIKE '%3510'
		]]>
		</value>
	</entry>

	<!--J-113-0143 配合SBTi貸後名單通知營業單位更新，條件3.服務商業建築產業別(註1)&大型企業&非短期額度&有餘額-->
	<entry key="DW_ESGLOANLIST_SS_3">
		<value>
			<![CDATA[
		SELECT *
			FROM DWADM.DW_ESGLOANLIST_SS
		WHERE CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ESGLOANLIST_SS) AND LN_TYP='1' AND TIMESTAMPDIFF(256,CHAR(CAST(DURING_ED AS TIMESTAMP) - CAST(DURING_BG AS TIMESTAMP))) > 1
		AND LOAN_BAL_T >0 AND SUBSTR(BIZNAME,3,3) IN('160','200','276','332','340','353','360','370','381','382','383','390','451','452','453','454','455','456','457','458','461','462','463','464','465','469','471','472','473','474','475','476','481','482','483','484','485','486','487','542','551','559','561','562','581','582','591','592','601','602','610','620','631','639','641','642','643','649','651','652','653','654','655','661','662','664','669','670','681','689','691','692','701','702','711','712','721','722','723','731',
		'732','740','750','760','771','772','773','774','781','782','790','800','811','812','813','820','831','840','851','852','853','854','855','856','858','859','861','862','869','871','879','881',
		'889','901','902','903','920','931','932','942','949','951','959','961','962','963','964','969')
		]]>
		</value>
	</entry>
	<!--J-113-0143 配合SBTi貸後名單通知營業單位更新，條件4.國內營業單位公司&僅中長期額度&非上市櫃公司者(再抓MIS判斷),MKT上市別:OTC-包括上櫃公司和興櫃公司,ROTC-專指興櫃公司,PUB-指所有公開發行公司,TSE-指上市公司-->
	<entry key="DW_ESGLOANLIST_SS_4">
		<value>
			<![CDATA[
		SELECT e.*
			FROM DWADM.DW_ESGLOANLIST_SS e LEFT JOIN DWADM.DW_TCRI t ON e.CUSTID =t.CUSTID
		WHERE e.CYC_MN = (SELECT MAX(CYC_MN) FROM DWADM.DW_ESGLOANLIST_SS) AND t.CYC_MN =(SELECT MAX(CYC_MN) FROM DWADM.DW_TCRI)
		AND TIMESTAMPDIFF(256,CHAR(CAST(DURING_ED AS TIMESTAMP) - CAST(DURING_BG AS TIMESTAMP))) > 1
			AND NOT(LEFT(BR_NO,1) BETWEEN 'A' AND 'Z' OR SUBSTR(BR_NO,2,1) BETWEEN 'A' AND 'Z') AND t.MKT IN('PUB','ROTC','OTC','TSE')
		]]>
		</value>
	</entry>
    <!--J-113-0143 針對ESG風險評級即將屆期之案件進行通知(中、低ESG風險至少每五年辦理一次、高ESG風險至少每年辦理一次-->
        <entry key="DW_ESGLOANLIST_SSByRiskRating">
            <value>
                <![CDATA[
                WITH tmp AS (SELECT CUSTID ,max(UPDATETIME) approveTime FROM DWADM.DW_ESGDETAIL de WHERE UPDATESRC ='C290M01A' GROUP BY CUSTID),
                     tmp2 AS (SELECT DISTINCT d.approveTime, m.CUSTID ,m.FINALASSESSMENT,
                     CASE WHEN m.FINALASSESSMENT IN('高風險','HIGH') AND DAYS(CAST(? AS Date) + 1 MONTHS)  - DAYS(d.approveTime) > (1 * 365) THEN '1'
                          WHEN m.FINALASSESSMENT IN('中風險','低風險','MEDIUM','LOW') AND DAYS(CAST(? AS Date) + 1 MONTHS) - DAYS(d.approveTime) > (5 * 365) THEN '5'
                            ELSE 'N' END as Result
                     FROM DWADM.DW_ESGMASTER m LEFT JOIN tmp d ON m.CUSTID =d.CUSTID WHERE m.FINALASSESSMENT IN('高風險','中風險','低風險','HIGH','LOW','MEDIUM'))
                SELECT t.*,S.BR_NO FROM tmp2 t LEFT JOIN DWADM.DW_ESGLOANLIST_SS S ON t.CUSTID =s.CUSTID WHERE S.CYC_MN =(SELECT max(CYC_MN) FROM DWADM.DW_ESGLOANLIST_SS) AND t.RESULT<>'N'
                ]]>
                </value>
	    </entry>
    </util:map>
    </beans>
