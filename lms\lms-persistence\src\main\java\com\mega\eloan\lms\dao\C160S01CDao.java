/* 
 * C160S01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160S01C;

/** 個金產品種類檔 **/
public interface C160S01CDao extends IGenericDao<C160S01C> {

	C160S01C findByOid(String oid);
	
	List<C160S01C> findByMainId(String mainId);
	
	C160S01C findByUniqueKey(String mainId, Integer seq, String refmainId);

	List<C160S01C> findByIndex01(String mainId, Integer seq, String refmainId);

	List<C160S01C> findByMainIdRefMainid(String mainId, String refmainId);

	List<C160S01C> findByMainIdRefMainidOrderBySeq(String mainId,
			String refmainId);
	
	List<C160S01C> findByMainIdRefMainidOrderByUiSeq(String mainId,
			String refmainId);
}