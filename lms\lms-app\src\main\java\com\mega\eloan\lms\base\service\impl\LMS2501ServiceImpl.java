package com.mega.eloan.lms.base.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.DocAuthTypeEnum;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.gwclient.IVRGwClient;
import com.mega.eloan.common.gwclient.IVRGwReqMessage;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.service.LMS2501Service;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L250A01ADao;
import com.mega.eloan.lms.dao.L250M01ADao;
import com.mega.eloan.lms.dao.L250M01BDao;
import com.mega.eloan.lms.dao.L250M01CDao;
import com.mega.eloan.lms.dao.L250S01ADao;
import com.mega.eloan.lms.dao.L250S01BDao;
import com.mega.eloan.lms.dao.L250S02ADao;
import com.mega.eloan.lms.dao.L250S02BDao;
import com.mega.eloan.lms.dao.L250S03ADao;
import com.mega.eloan.lms.dao.L250S04ADao;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L250A01A;
import com.mega.eloan.lms.model.L250M01A;
import com.mega.eloan.lms.model.L250M01B;
import com.mega.eloan.lms.model.L250M01C;
import com.mega.eloan.lms.model.L250S01A;
import com.mega.eloan.lms.model.L250S01B;
import com.mega.eloan.lms.model.L250S02A;
import com.mega.eloan.lms.model.L250S02B;
import com.mega.eloan.lms.model.L250S03A;
import com.mega.eloan.lms.model.L250S04A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * 
 * <AUTHOR>
 * 
 */
@Service
public class LMS2501ServiceImpl extends AbstractCapService implements
		LMS2501Service {
	private static Logger logger = LoggerFactory
			.getLogger(LMS2501ServiceImpl.class);

	@Resource
	L250M01ADao l250m01aDao;
	@Resource
	L250M01BDao l250m01bDao;
	@Resource
	L250M01CDao l250m01cDao;
	@Resource
	L250A01ADao l250a01aDao;
	@Resource
	L250S01ADao l250s01aDao;
	@Resource
	L250S02ADao l250s02aDao;
	@Resource
	L250S03ADao l250s03aDao;
	@Resource
	L250S04ADao l250s04aDao;

	@Resource
	L250S01BDao l250s01bDao;

	@Resource
	L250S02BDao l250s02bDao;

	@Resource
	TempDataService tempDataService;
	@Resource
	DocLogService docLogService;
	@Resource
	FlowService flowService;
	
	@Resource
	SysParameterService sysParameterService;

	@Resource
	private IVRGwClient ivrGwClient;
	
	@Resource
	L140M01ADao l140m01aDao;

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String action) throws Throwable {

		if (model instanceof L250M01A) {
			save((L250M01A) model);
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS2501Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}

			if (setResult) {
				inst.setAttribute("result", action);
			}

			inst.next();

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}

	}

	@Override
	public int getVersion(String rptId) {
		L250M01C l250m01c = l250m01cDao.findLatestVersion(rptId);
		return l250m01c.getVersion();
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (GenericBean model : entity) {
			if (model instanceof L250M01A) {
				if (Util.isEmpty(((L250M01A) model).getOid())) {
					l250m01aDao.save((L250M01A) model);
					flowService.start("LMS2501Flow",
							((L250M01A) model).getOid(), user.getUserId(),
							user.getUnitNo());
					// 新增授權檔
					L250A01A l250a01a = new L250A01A();
					l250a01a.setAuthTime(CapDate.getCurrentTimestamp());
					l250a01a.setAuthType(DocAuthTypeEnum.MODIFY.getCode());
					l250a01a.setAuthUnit(user.getUnitNo());
					l250a01a.setMainId(((L250M01A) model).getMainId());
					l250a01a.setOwner(user.getUserId());
					l250a01a.setOwnUnit(user.getUnitNo());
					l250a01aDao.save(l250a01a);

				} else {
					// 當文件狀態為編製中時文件亂碼才變更
					l250m01aDao.save((L250M01A) model);

					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						try {
							if ("01O".equals(model.get("docStatus"))) {
								model.set("apprId", user.getUserId());
								model.set("updater", user.getUserId());
								model.set("updateTime",
										CapDate.getCurrentTimestamp());
								model.set("randomCode",
										IDGenerator.getRandomCode());
							}
						} catch (CapException e) {

						}
						tempDataService.deleteByMainId(((L250M01A) model)
								.getMainId());
						docLogService.record(((L250M01A) model).getOid(),
								DocLogEnum.SAVE);
					}

				}
			}

		}

	}

	@Override
	public void save(L250M01A meta, List<L250M01B> l250m01bs) {
		this.save(meta);
		List<L250M01B> oldL250m01bs = l250m01bDao
				.findByMainId(meta.getMainId());
		if (CollectionUtils.isNotEmpty(oldL250m01bs)) {
			l250m01bDao.delete(oldL250m01bs);
			l250m01bDao.flush();
		}
		l250m01bDao.save(l250m01bs);

	}

	@Override
	public L250M01A updateMetaInfoTab(L250M01A meta) throws CapException {
		save(meta);
		return meta;
	}

	@Override
	public L250M01A updateCheckListTab(L250M01A meta, JSONArray listData)
			throws CapException {

		List<L250S03A> oldL250s03as = l250s03aDao
				.findByMainId(meta.getMainId());
		List<L250S04A> oldL250s04as = l250s04aDao
				.findByMainId(meta.getMainId());

		if (CollectionUtils.isNotEmpty(oldL250s03as)
				&& CollectionUtils.isNotEmpty(oldL250s04as)) {

			for (int i = 0; i < listData.size(); i++) {
				JSONObject groupJson = listData.getJSONObject(i);
				JSONArray ja = groupJson.getJSONArray("subItems");
				for (int j = 0; j < ja.size(); j++) {
					JSONObject subItem = ja.getJSONObject(j);
					L250S04A l250s04a = l250s04aDao.findByKey(meta.getMainId(),
							Integer.parseInt(groupJson.getString("group")),
							Integer.parseInt(subItem.getString("subItem")));
					if (l250s04a != null) {
						l250s04a.setValue(subItem.optString("checkValue"));
					}
				}

			}

		} else {
			List<L250S03A> l250s03as = new ArrayList<L250S03A>();
			List<L250S04A> l250s04as = new ArrayList<L250S04A>();
			for (int i = 0; i < listData.size(); i++) {

				// System.out.println("i+++++++++++++++++++++++:" + i);
				JSONObject groupJson = listData.getJSONObject(i);

				L250S03A l250s03a = new L250S03A();
				l250s03a.setGroup(Integer.parseInt(groupJson.getString("group")));
				l250s03a.setGroupOrder(i + 1);
				l250s03a.setGroupTitle(groupJson.getString("groupTitle"));
				l250s03a.setShowHead(groupJson.getString("showHead"));
				l250s03a.setYesTitle(groupJson.getString("yesTitle"));
				l250s03a.setNoTitle(groupJson.getString("noTitle"));
				l250s03a.setNaTitle(groupJson.getString("naTitle"));
				l250s03a.setMainId(meta.getMainId());
				JSONArray ja = groupJson.getJSONArray("subItems");

				for (int j = 0; j < ja.size(); j++) {
					JSONObject subItem = ja.getJSONObject(j);
					L250S04A l250s04a = new L250S04A();
					l250s04a.setMainId(meta.getMainId());
					l250s04a.setGroup(Integer.parseInt(groupJson
							.getString("group")));
					l250s04a.setYes(subItem.getString("yes"));
					l250s04a.setNo(subItem.getString("no"));
					// l250s04a.setTbd(subItem.getString("tbd"));
					l250s04a.setNa(subItem.getString("na"));
					l250s04a.setSub1Item(Integer.parseInt(subItem
							.getString("subItem")));
					l250s04a.setSub1Order(j + 1);
					l250s04a.setSub1Title(subItem.getString("subTitle"));
					l250s04a.setSub1RejectVal(subItem.getString("subRejectVal"));
					l250s04a.setValue(subItem.optString("checkValue"));
					l250s04as.add(l250s04a);
				}

				l250s03as.add(l250s03a);

			}
			l250s03aDao.save(l250s03as);
			l250s04aDao.save(l250s04as);
		}

		save(meta);

		return meta;
	}

	@Override
	public L250M01A updateClsCheckListTab(L250M01A meta, JSONArray listData)
			throws CapException {

		List<L250S02B> oldL250s02bs = l250s02bDao
				.findByMainId(meta.getMainId());

		l250s02bDao.delete(oldL250s02bs);
		List<L250S02B> l250s02bs = new ArrayList<L250S02B>();

		for (int k = 0; k < listData.size(); k++) {
			JSONObject typeJson = listData.getJSONObject(k);

			String type = typeJson.getString("type");
			JSONArray groups = typeJson.getJSONArray("groups");

			for (int i = 0; i < groups.size(); i++) {
				JSONObject groupJson = groups.getJSONObject(i);
				String group = groupJson.getString("group");
				JSONArray subItems = groupJson.getJSONArray("subItems");
				for (int j = 0; j < subItems.size(); j++) {
					L250S02B l250s02b = new L250S02B();
					l250s02b.setMainId(meta.getMainId());
					JSONObject subItemJson = subItems.getJSONObject(j);

					String subItem = subItemJson.optString("subItem", "");
					String subTitle = subItemJson.optString("subTitle", "");
					String subValue = subItemJson.optString("subValue", "");
					l250s02b.setType(Integer.parseInt(type));
					l250s02b.setGroup(Integer.parseInt(group));
					l250s02b.setGroupOrder(i + 1);
					if(Util.isNotEmpty(Util.trim(subItem))){
						l250s02b.setSubItem(Integer.parseInt(subItem));
					}
					l250s02b.setSubOrder(j + 1);
					l250s02b.setSubTitle(subTitle);
					l250s02b.setSubValue(subValue);
					l250s02bs.add(l250s02b);
				}

			}
		}

		l250s02bDao.save(l250s02bs);
		return meta;
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub

	}

	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		return l250m01aDao.findPage(search);
	}

	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L250M01A.class) {
			return (T) l250m01aDao.findByOid(oid);
		}

		return null;
	}

	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == L250M01A.class) {

		} else if (clazz == L250M01B.class) {
			return l250m01bDao.findByMainId(mainId);
		}
		return null;
	}

	@Override
	public String genLMSCheckList() {

		JSONArray ja = new JSONArray();

		List<L250S01A> l250s01as = l250s01aDao.getAll();

		for (L250S01A l250s01a : l250s01as) {
			JSONObject jb = new JSONObject();
			Integer group = l250s01a.getGroup();
			String groupTitle = Util.trim(l250s01a.getGroupTitle());
			String showHead = Util.trim(l250s01a.getShowHead());
			String yesTitle = Util.trim(l250s01a.getYesTitle());
			String noTitle = Util.trim(l250s01a.getNoTitle());
			String naTitle = Util.trim(l250s01a.getNaTitle());
			jb.put("group", group);

			jb.put("groupTitle", groupTitle);
			jb.put("showHead", showHead);
			jb.put("yesTitle", yesTitle);
			jb.put("noTitle", noTitle);
			jb.put("naTitle", naTitle);

			List<L250S02A> l250s02as = l250s02aDao.getByGroup(group);

			JSONArray ja2 = new JSONArray();
			for (L250S02A l250s02a : l250s02as) {
				JSONObject jb2 = new JSONObject();
				jb2.put("subTitle", l250s02a.getSub1Title());
				jb2.put("yes", l250s02a.getYes());
				jb2.put("no", l250s02a.getNo());
				jb2.put("na", l250s02a.getNa());
				jb2.put("subItem", l250s02a.getSub1Item());
				jb2.put("subRejectVal", Util.trim(l250s02a.getSub1RejectVal()));

				ja2.add(jb2);
			}

			jb.put("subItems", ja2);
			ja.add(jb);
		}
		return ja.toString();
	}

	@Override
	public String genCLSCheckList() {
		return  genCLSCheckList_V1();
	}
	
	private String genCLSCheckList_V2() {		
		JSONArray ja = new JSONArray();
		
		List<L250S01B> l250s01bs = l250s01bDao.getAll();
		if (CollectionUtils.isNotEmpty(l250s01bs)) {
			LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, String>>> map = new LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, String>>>();
			for(L250S01B l250s01b: l250s01bs ){
				String type = Util.trim(l250s01b.getType());
				String group = Util.trim(l250s01b.getGroup());
				String subItem = Util.trim(l250s01b.getSubItem());
				String subTitle = Util.trim(l250s01b.getSubTitle());
				
				if(!map.containsKey(type)){
					map.put(type, new LinkedHashMap<String, LinkedHashMap<String, String>>());
				}
				if(!map.get(type).containsKey(group)){
					map.get(type).put(group, new LinkedHashMap<String, String>());
				}
				if(!map.get(type).get(group).containsKey(subItem)){
					map.get(type).get(group).put(subItem, subTitle);
				}
			}
			
			for(String type : map.keySet()){
				JSONObject json_type = new JSONObject();
				JSONArray groups = new JSONArray();
				LinkedHashMap<String, LinkedHashMap<String, String>> groups_map = map.get(type);
				for(String group : groups_map.keySet()){
					JSONObject json_group = new JSONObject();
					JSONArray subItems = new JSONArray();
					
					LinkedHashMap<String, String> subItem_map = groups_map.get(group);
					for(String subItem : subItem_map.keySet() ){
						JSONObject json_subItem = new JSONObject();
						json_subItem.put("subItem", subItem);
						json_subItem.put("subTitle", subItem_map.get(subItem));
						//=================
						subItems.add(json_subItem);
					}
					json_group.put("group", group);
					json_group.put("subItems", subItems);
					//=================
					groups.add(json_group);
				}
				json_type.put("type", type);
				json_type.put("groups", groups);
				ja.add(json_type);
			}
			
		}	
		return ja.toString();		
	}
	
	private String genCLSCheckList_V1() {		
		JSONArray ja = new JSONArray();
		JSONObject type = new JSONObject();
		JSONObject group = null;
		JSONArray subItems = new JSONArray();
		JSONArray groups = new JSONArray();
		List<L250S01B> l250s01bs = l250s01bDao.getAll();

		if (CollectionUtils.isNotEmpty(l250s01bs)) {
			// get first
			L250S01B first = l250s01bs.get(0);

			int baseType = first.getType();
			int baseGroup = first.getGroup();

			JSONObject subItem = new JSONObject();
			subItem.put("subItem", first.getSubItem());
			subItem.put("subTitle", first.getSubTitle());
			subItems.add(subItem);

			for (int i = 1; i < l250s01bs.size(); i++) {
				L250S01B l250s01b = l250s01bs.get(i);
				int groupN = l250s01b.getGroup();
				int typeN = l250s01b.getType();

				if (groupN != baseGroup) {

					group = new JSONObject();
					group.put("group", baseGroup);
					group.put("subItems", subItems);

					subItem = new JSONObject();
					subItem.put("subItem", l250s01b.getSubItem());
					subItem.put("subTitle", l250s01b.getSubTitle());

					subItems = new JSONArray();
					subItems.add(subItem);

					groups.add(group);
				} else {
					subItem = new JSONObject();
					subItem.put("subItem", l250s01b.getSubItem());
					subItem.put("subTitle", l250s01b.getSubTitle());
					subItems.add(subItem);
				}

				if (typeN != baseType) {
					type.put("type", baseType);
					type.put("groups", groups);
					ja.add(type);

					groups = new JSONArray();
				} else {

				}

				baseGroup = groupN;
				baseType = typeN;
			}

			group = new JSONObject();
			group.put("group", baseGroup);
			group.put("subItems", subItems);

			groups.add(group);
			type.put("type", baseType);
			type.put("groups", groups);
			ja.add(type);
		}

		return ja.toString();
	}

	@Override
	public void deleteLmsMeta(L250M01A meta) {
		flowService.cancel(meta.getOid());
		removeLmsCheckList(meta);
		l250m01aDao.delete(meta);
		docLogService.record(meta.getOid(), DocLogEnum.DELETE);
	}

	@Override
	public void deleteClsMeta(L250M01A meta) {
		flowService.cancel(meta.getOid());
		removeClsCheckList(meta);
		l250m01aDao.delete(meta);
		docLogService.record(meta.getOid(), DocLogEnum.DELETE);
	}

	@Override
	public void removeLmsCheckList(L250M01A meta) {
		List<L250S03A> oldL250s03as = l250s03aDao
				.findByMainId(meta.getMainId());
		List<L250S04A> oldL250s04as = l250s04aDao
				.findByMainId(meta.getMainId());
		if (CollectionUtils.isNotEmpty(oldL250s03as)) {
			l250s03aDao.delete(oldL250s03as);
		}
		if (CollectionUtils.isNotEmpty(oldL250s04as)) {
			l250s04aDao.delete(oldL250s04as);
		}
	}

	@Override
	public void removeClsCheckList(L250M01A meta) {
		List<L250S02B> oldL250s02bs = l250s02bDao
				.findByMainId(meta.getMainId());

		if (CollectionUtils.isNotEmpty(oldL250s02bs)) {
			l250s02bDao.delete(oldL250s02bs);
		}
	}

	@Override
	public JSONArray getSavedList(L250M01A meta) {
		List<L250S03A> l250s03as = l250s03aDao.findByMainId(meta.getMainId());
		JSONArray ja = new JSONArray();

		if (CollectionUtils.isNotEmpty(l250s03as)) {
			for (L250S03A l250s03a : l250s03as) {
				JSONObject jb = new JSONObject();
				Integer group = l250s03a.getGroup();
				String groupTitle = l250s03a.getGroupTitle();
				jb.put("group", group);
				jb.put("groupTitle", groupTitle);
				jb.put("showHead", l250s03a.getShowHead());
				jb.put("yesTitle", l250s03a.getYesTitle());
				jb.put("noTitle", l250s03a.getNoTitle());
				jb.put("naTitle", l250s03a.getNaTitle());
				List<L250S04A> l250s04as = l250s04aDao.findByMainIdAndGroup(
						meta.getMainId(), group);

				JSONArray ja2 = new JSONArray();
				for (L250S04A l250s04a : l250s04as) {
					JSONObject jb2 = new JSONObject();
					jb2.put("subTitle", l250s04a.getSub1Title());
					jb2.put("yes", l250s04a.getYes());
					jb2.put("no", l250s04a.getNo());
					// jb2.put("tbd", l250s04a.getTbd());
					jb2.put("na", l250s04a.getNa());
					jb2.put("subItem", l250s04a.getSub1Item());
					jb2.put("checkValue", l250s04a.getValue());
					jb2.put("subRejectVal",
							Util.trim(l250s04a.getSub1RejectVal()));

					ja2.add(jb2);
				}

				jb.put("subItems", ja2);
				ja.add(jb);
			}

		}

		return ja;

	}

	@Override
	public JSONArray getClsSavedList(L250M01A meta) {

		JSONArray ja = new JSONArray();
		JSONObject type = new JSONObject();
		JSONObject group = null;
		JSONArray subItems = new JSONArray();
		JSONArray groups = new JSONArray();
		List<L250S02B> l250s02bs = l250s02bDao.findByMainId(meta.getMainId());

		if (CollectionUtils.isNotEmpty(l250s02bs)) {
			// get first
			L250S02B first = l250s02bs.get(0);

			int baseType = first.getType();
			int baseGroup = first.getGroup();

			JSONObject subItem = new JSONObject();
			subItem.put("subItem", first.getSubItem());
			subItem.put("subTitle", first.getSubTitle());
			subItem.put("subValue", first.getSubValue());
			subItems.add(subItem);

			for (int i = 1; i < l250s02bs.size(); i++) {
				L250S02B l250s02b = l250s02bs.get(i);
				int groupN = l250s02b.getGroup();
				int typeN = l250s02b.getType();

				if (groupN != baseGroup) {

					group = new JSONObject();
					group.put("group", baseGroup);
					group.put("subItems", subItems);

					subItem = new JSONObject();
					subItem.put("subItem", l250s02b.getSubItem());
					subItem.put("subTitle", l250s02b.getSubTitle());
					subItem.put("subValue", l250s02b.getSubValue());

					subItems = new JSONArray();
					subItems.add(subItem);

					groups.add(group);
				} else {
					subItem = new JSONObject();
					subItem.put("subItem", l250s02b.getSubItem());
					subItem.put("subTitle", l250s02b.getSubTitle());
					subItem.put("subValue", l250s02b.getSubValue());
					subItems.add(subItem);
				}

				if (typeN != baseType) {
					type.put("type", baseType);
					type.put("groups", groups);
					ja.add(type);

					groups = new JSONArray();
				} else {

				}

				baseGroup = groupN;
				baseType = typeN;
			}

			group = new JSONObject();
			group.put("group", baseGroup);
			group.put("subItems", subItems);

			groups.add(group);
			type.put("type", baseType);
			type.put("groups", groups);
			ja.add(type);
		}

		return ja;
	}

	@Override
	public List<L250S04A> getL250s04as(L250M01A meta) {
		List<L250S04A> l250s04as = l250s04aDao.findByMainId(meta.getMainId());
		return l250s04as;
	}
	
	//J-108-0217_10702_B1002 新增特定金錢信託受益權自行設質擔保授信 檢核
	public boolean getProjClassFromL250M01A(String oid){
		boolean checkprojClass=false;
		
		if(!Util.isEmpty(oid)){
			L250M01A meta = findModelByOid(L250M01A.class,oid);
			List<L250M01B> l250m01bs = (List<L250M01B>) findListByMainId(L250M01B.class, meta.getMainId());
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				String projClass = l140m01a.getProjClass();
				if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					checkprojClass=true;
				}
			}
		}
		return checkprojClass;
	}
	
	public void saveIVRFlag(String oid, List<String> addIVRList) throws CapException{
		try{
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			for(String data:addIVRList)
			{
				String[] eachdata=data.split(";");
				int cnt = (eachdata==null?0:eachdata.length);
				if(cnt==2){
					String newCustId = cnt > 0 ? eachdata[0] : "";
					String newIVRFlag =  cnt > 1 ? eachdata[1] : "";
					L250M01A meta = findModelByOid(L250M01A.class,oid);
					List<L250M01B> l250m01bs = (List<L250M01B>) findListByMainId(L250M01B.class, meta.getMainId());
					for(L250M01B l250m01b:l250m01bs){
						String reMainId=l250m01b.getReMainId();
						L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
						String custId = l140m01a.getCustId();
						String projClass = l140m01a.getProjClass();
						if(newCustId.equals(custId) && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信))
						{
							String ivrFlag=l250m01b.getIVRFlag();
							if(ivrFlag==null){
								l250m01b.setIVRFlag(newIVRFlag);
							}
							else if (ivrFlag !=null && !ivrFlag.contains(newIVRFlag)){
								l250m01b.setIVRFlag(Util.trim(ivrFlag)+ "," +newIVRFlag);
							}
							l250m01b.setUpdater( user.getUserId());
							l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
							this.save(l250m01b);
						}
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public void deleteIVRFlag(String oid, String deleteCustId, String fileName) throws CapException{
		try{
			if(!Util.isEmpty(fileName)){
				MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
				L250M01A meta = findModelByOid(L250M01A.class,oid);
				List<L250M01B> l250m01bs = (List<L250M01B>) findListByMainId(L250M01B.class, meta.getMainId());
				for(L250M01B l250m01b:l250m01bs){
					String reMainId=l250m01b.getReMainId();
					L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
					String projClass = l140m01a.getProjClass();
					String custId=l140m01a.getCustId();
					String ivrFlag=l250m01b.getIVRFlag();
					if(Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信) && custId.equals(deleteCustId) && ivrFlag !=null && ivrFlag.contains(fileName))
					{
						if(ivrFlag.contains(","))
						{
							l250m01b.setIVRFlag(ivrFlag.replace(fileName + ",", "").replace("," + fileName, ""));
						}
						else
						{
							l250m01b.setIVRFlag(null);
						}
						l250m01b.setUpdater( user.getUserId());
						l250m01b.setUpdateTime(CapDate.getCurrentTimestamp());
						this.save(l250m01b);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
	}
	
	public List<Map<String, Object>> getIVRgrid(String oid) throws CapException{
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			L250M01A meta = findModelByOid(L250M01A.class,oid);
			List<L250M01B> l250m01bs = (List<L250M01B>) findListByMainId(L250M01B.class, meta.getMainId());
			
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			String lightId =user.getLightId();
			String userId =user.getUserId();
			String url = sysParameterService.getParamValue(SysParamConstants.IVR_REC_GW_URL);
			
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				if(!Util.isEmpty(l250m01b.getIVRFlag())){
					String[] IVRFlag=l250m01b.getIVRFlag()!=null ? l250m01b.getIVRFlag().split(",") : null;
					for(String ivrFlag:IVRFlag){
						Map<String, Object> row = new HashMap<String, Object>();
						row.put("custId", Util.trim(l140m01a.getCustId()));
						row.put("custName", Util.trim(l140m01a.getCustName()));
						row.put("record_FileName", Util.trim(ivrFlag));
						row.put("record_LightId", lightId);
						row.put("record_UserID", userId);
						row.put("record_Url", url);
						row.put("record_FileName2", Util.trim(ivrFlag).replace(".wav", ""));
						list.add(row);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		
		return list;
	}
	
	public List<Map<String, Object>> getIVRFiltergrid(String oid) throws CapException{

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try{
			L250M01A meta = findModelByOid(L250M01A.class,oid);
			String SRCMAINID =meta.getSrcMainId();
	
			List<L250M01B> l250m01bs = (List<L250M01B>) findListByMainId(L250M01B.class, meta.getMainId());
			
			String branCode = user.getUnitNo();
			Date sDate = new Date();
	        Calendar rightNow = Calendar.getInstance();  
	        rightNow.setTime(sDate);  
	        rightNow.add(Calendar.MONTH, NumberUtils.toInt(sysParameterService.getParamValue(SysParamConstants.IVR_Query_StartMonth)));
	        sDate = rightNow.getTime();  
			Date dDate = new Date();
			SimpleDateFormat ft = new SimpleDateFormat ("yyyyMMdd");
			String startDate = ft.format(sDate);
			String endDate = ft.format(dDate);
			String lightId =user.getLightId();
			String userId =user.getUserId();
			//branCode="";
			
			for(L250M01B l250m01b:l250m01bs){
				String reMainId=l250m01b.getReMainId();
				L140M01A l140m01a = l140m01aDao.findByMainId(reMainId);
				String custId = l140m01a.getCustId();
				String projClass = l140m01a.getProjClass();
				IVRGwReqMessage req = new IVRGwReqMessage(custId,startDate, endDate, lightId, userId , branCode);
				if(Util.notEquals(custId, "") && Util.notEquals(startDate, "") && Util.notEquals(endDate, "") && Util.equals(projClass, LMSUtil.特定金錢信託受益權自行設質擔保授信)){
					List<Map<String, Object>> lists = new ArrayList<Map<String, Object>>();
					lists=this.find(req);
					for(Map<String, Object> each:lists){
						list.add(each);
					}
				}
			}
		} catch (Throwable t1) {
			throw new CapMessageException(t1.getMessage(), getClass());
		}
		return list;
	}
	
	public List<Map<String, Object>> find(IVRGwReqMessage req) {
		/*
			http://192.168.211.127/megabank/web/admin/midi_rec_edit.php?lightID=ISEhfDAwODAzNHxFTHwwODZBOTQzNDRBRjk2QTY2NTA2NzJFQThCMTJBODdBQXwxNTIwNDE5ODAwMDR3&&UserID=008034&RECORD_FILENAME=201906050090039684320
		 */

		//ivrGwClient.init();
		List<Map<String, Object>> list = ivrGwClient.send(req);
		
		return list;
	}
	
	public L140M01A findByMainId(String reMainId){
		return l140m01aDao.findByMainId(reMainId);
	}

	@Override
	public List<L250S02B> findL250S02B(String mainId){
		return l250s02bDao.findByMainId(mainId);
	}
}
