package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.component.impl.CapMvcParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AMLRelateService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.report.CLS1161RptService;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.L120S09A;
import com.mega.eloan.lms.model.L120S09B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("cls1161rptservice")
public class CLS1161RptServiceImpl implements FileDownloadService, CLS1161RptService {
	protected static final Logger LOGGER = LoggerFactory
		.getLogger(CLS1161RptServiceImpl.class);
	
	@Resource
	AMLRelateService amlRelateService;

	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	CLS1161Service cls1161Service;
	
	@Resource
	CLS1161R01RptServiceImpl cls1161R01RptService;
	
	@Resource
	CLS1161R01CRptServiceImpl cls1161R01CRptService;
	
	@Resource
	C160M01ADao c160m01aDao;

	@Resource
	C160M01BDao c160m01bDao;
	
	@Resource
	L140M01ADao l140m01aDao;
	
	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();
	
	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}
	
	private OutputStream generateReport(PageParameters params) throws IOException, Exception {
		
		List<InputStream> list = new LinkedList<InputStream>();
		
		String mainId = Util.trim(params.getString("mainId"));
		int subLine = 1;
		OutputStream outputStream = null;				
		try {
			Locale locale = LMSUtil.getLocale();
			Properties propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			
			String[] rptNoArr = {"R01", "R33", "LMS1201R34"};
			for (String rptNo : rptNoArr) {
				outputStream = null;
				
				if(Util.equals("R01", rptNo)){		
					
					outputStream = genR01(mainId);
					
					if(outputStream != null){
						list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
					}
					
				}else if(Util.equals("R33", rptNo)){
					
					List<L120S09A> listL120s09a = amlRelateService.findL120s09aByMainIdWithOrder(mainId);
					if(listL120s09a.size()>0){
						outputStream = genLMS1201R33(mainId, locale,
								"report/lns/LMS1201R33_" + locale.toString()
								+ ".rpt", listL120s09a);
						
						// 列印頁次位置
						subLine = 8;
						
						Map<InputStream, Integer> tmp_map = new LinkedHashMap<InputStream, Integer>();
						tmp_map.put(new ByteArrayInputStream(
								((ByteArrayOutputStream) outputStream)
								.toByteArray()), subLine);
						//==========
						outputStream = new ByteArrayOutputStream();
						PdfTools.mergeReWritePagePdf(tmp_map, outputStream,
								propEloanPage.getProperty("PaginationText"), true,
								locale, subLine);						
					}
					
					if(outputStream != null){
						list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
					}
					
				}
				// J-108-0083  國內個金金新增撥貸逾一年以上未動工興建之空地貸款控管機制
				else if(Util.equals("LMS1201R34", rptNo)){
					
					Map<InputStream, Integer> pdfNameMap6 = new LinkedHashMap<InputStream, Integer>();
					// 加印額度動用資訊一覽表
					outputStream = this.genVacantLandReport(mainId, locale, "report/lns/LMS1201R34_" + locale.toString() + ".rpt");
					// 列印頁次位置
					subLine = 10;
					if (outputStream != null) {
						pdfNameMap6.put(new ByteArrayInputStream( ((ByteArrayOutputStream) outputStream).toByteArray()), subLine);
					}
					
					if (pdfNameMap6 != null && pdfNameMap6.size() > 0) {
						outputStream = new ByteArrayOutputStream();
						PdfTools.mergeReWritePagePdf(pdfNameMap6, outputStream, propEloanPage.getProperty("PaginationText"), true, locale, subLine);
						list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
					}
				}
			}
			
			if(list.size()>0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(list, outputStream);	
			}			
		}finally{
			
		}
		return outputStream;
	}
	
	/**
	 * 先行動用待辦事項控制表
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genVacantLandReport(String mainId, Locale locale, String path) throws Exception {
		
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(path);

		Properties properties = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
		C160M01A mainC160m01a = c160m01aDao.findByMainId(mainId);
		
		try {
			List<C160M01B> c160m01bList = c160m01bDao.findByMainId(mainC160m01a.getMainId());
			if (c160m01bList == null || c160m01bList.isEmpty()) {
				return null;
			}
			
			dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));
			int caseType = Integer.parseInt(mainC160m01a.getCaseType());//案號 : 一般案號、團貸案號
			
			//*** set title content
			rptVariableMap.put("C160M01A.CASETYPE", mainC160m01a.getCaseType());
			//分行名稱
			String branchName = null;
			branchName = Util.nullToSpace(branchService.getBranchName(Util.nullToSpace(mainC160m01a.getOwnBrId())));
			rptVariableMap.put("BRANCHNAME", branchName);
			//文件亂碼
			rptVariableMap.put("L160M01A.RANDOMCODE", Util.nullToSpace(mainC160m01a.getRandomCode()));
			//案件號碼
			rptVariableMap.put("L160M01A.CASENO", this.getCaseNo(caseType, mainC160m01a));
			//借款人
			rptVariableMap.put("L160M01A.CUSTNAME", this.getDebtorName(c160m01bList, caseType, mainC160m01a.getCustName(), properties));
			//額度序號
			rptVariableMap.put("L160M01B.CNTRNO", this.getQuotaNoString(caseType, mainC160m01a.getAllCanPay(), c160m01bList, properties));

			//*** get sub report content
			List<Map<String, String>> subReportRows = this.getVacantLandMortgageReportContent(locale, c160m01bList, properties);
			if (subReportRows == null) {
				// 如果舊案一比空地貸款註記都沒有，就不要印LMS1601R05
				return null;
			}
				
			Map<String, String> subVariableData = new HashMap<String, String>();
			SubReportParam subReportParam = new SubReportParam();
			subReportParam.setData(0, subVariableData, subReportRows);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			generator.setSubReportParam(subReportParam);

			outputStream = generator.generateReport();
			
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		
		return outputStream;
	}
	
	private String getCaseNo(int caseType, C160M01A c160m01a){
		
		if(caseType == 2){
			return Util.nullToSpace(c160m01a.getLoanMasterNo());
		}
		
		return Util.nullToSpace(c160m01a.getCaseNo());
	}
	
	/**
	 * 設定國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> getVacantLandMortgageReportContent(Locale locale, List<C160M01B> c160m01bList, Properties prop) {
		
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		Map<String, String> newMapInTitleRows = null;
		boolean hasClearLand = false;
		List<Map<String, String>> returnRows = new LinkedList<Map<String, String>>();
		
		for (C160M01B c160m01b : c160m01bList) {

			mapInTitleRows = Util.setColumnMap();
			newMapInTitleRows = Util.setColumnMap(); //欄位名稱轉 ReportBean Column

			String isClearLand = Util.trim(c160m01b.getIsClearLand());
			
			if (Util.notEquals(isClearLand, "")) {
				hasClearLand = true;
				mapInTitleRows.put("L140M01M.CNTRNO", Util.trim(c160m01b.getCntrNo()));//額度序號
				//本案是否為撥貸逾一年以上未動工興建之空地貸款控管案件
				mapInTitleRows.put("L140M01M.ISCLEARLAND", isClearLand);
				mapInTitleRows.put("L140M01M.ISCLEARLANDDSCR", this.showYNPic4(isClearLand, prop));
			}
			
			//本案是否為撥貸逾一年以上未動工興建之空地貸款控管案件 -> 是
			if (Util.notEquals(isClearLand, "" ) && Util.equals(isClearLand, "Y")) {
				DecimalFormat clearDf = new DecimalFormat("###,###,###,###,###,###,###,##0.#####");
				// 控管類別
				Map<String, String> ctlTypeMap = codeTypeService.findByCodeType("lms7600_ctlType");
				mapInTitleRows.put("L140M01M.CTLTYPE", MapUtils.getString(ctlTypeMap, Util.trim(c160m01b.getCtlType()), ""));
				//初次核定預計動工日
				mapInTitleRows.put("L140M01M.FSTDATE", 
										c160m01b.getFstDate() == null 
										? "" 
										: Util.trim(CapDate.formatDate(c160m01b.getFstDate(), UtilConstants.DateFormat.YYYY_MM_DD)));
				//最新核定預計動工日
				mapInTitleRows.put("L140M01M.LSTDATE",
										c160m01b.getLstDate() == null ? ""
										: Util.trim(CapDate.formatDate(c160m01b.getLstDate(), UtilConstants.DateFormat.YYYY_MM_DD)));
				//是否變更預計動工日
				String isChgStDate = Util.trim(c160m01b.getIsChgStDate());
				if (Util.notEquals(isChgStDate, "")) {
					mapInTitleRows.put("L140M01M.ISCHGSTDATE", isChgStDate);
					mapInTitleRows.put("L140M01M.ISCHGSTDATEDSCR", this.showYNPic4(isChgStDate, prop));

				} else {
					mapInTitleRows.put("L140M01M.ISCHGSTDATE", "");
				}

				if (Util.equals(isChgStDate, "Y")) {
					//變更預計動工日
					mapInTitleRows.put("L140M01M.CSTDATE",
									c160m01b.getCstDate() == null 
									? ""
									: Util.trim(CapDate.formatDate(c160m01b.getCstDate(), UtilConstants.DateFormat.YYYY_MM_DD)));
					// 變更預計動工日原因
					Map<String, String> cstReasonMap = codeTypeService.findByCodeType("lms7600_cstReason");
					mapInTitleRows.put("L140M01M.CSTREASON", MapUtils.getString(cstReasonMap, Util.trim(c160m01b.getCstReason()), ""));
					// 輸入本次採行措施
					Map<String, String> adoptFgMap = codeTypeService.findByCodeType("lms7600_adoptFg");
					StringBuffer adoptStr = new StringBuffer("");
					for (String adoptKey : adoptFgMap.keySet()) {
						
						adoptStr.append(Util.trim(c160m01b.getAdoptFg()).contains(adoptKey)
										? showYNPic7(Util.nullToSpace("Y"), prop)
										: showYNPic7(Util.nullToSpace("N"), prop));
						adoptStr.append(adoptKey + ".");
						adoptStr.append(MapUtils.getString(adoptFgMap, adoptKey, ""));
						adoptStr.append("　");
					}

					mapInTitleRows.put("L140M01M.ADOPTFG", adoptStr.toString());
					
				} else {
					mapInTitleRows.put("L140M01M.CSTDATE", "");
					mapInTitleRows.put("L140M01M.CSTREASON", "");
					mapInTitleRows.put("L140M01M.ADOPTFG", "");
				}

				String isChgRate = Util.trim(c160m01b.getIsChgRate());
				//是否調整利率
				mapInTitleRows.put("L140M01M.ISCHGRATE", Util.trim(isChgRate));
				mapInTitleRows.put("L140M01M.ISCHGRATEDSCR", Util.notEquals(isChgRate, "") 
																? this.showYNPic4(Util.trim(c160m01b.getIsChgRate()), prop)
																: "");

				if (Util.trim(c160m01b.getAdoptFg()).contains("3") || Util.equals(Util.trim(c160m01b.getIsChgRate()), "Y")) {
					//再加減碼幅度
					mapInTitleRows.put("L140M01M.SHOWCHGRATE", "Y");
					mapInTitleRows.put("L140M01M.RATEADD", c160m01b.getRateAdd() == null 
															? "" 
															: clearDf.format(Util.parseDouble(Util.trim(c160m01b.getRateAdd()))));
					//借款人ROA
					mapInTitleRows.put("L140M01M.CUSTROA", c160m01b.getCustRoa() == null 
															? "" 
															: clearDf.format(Util.parseDouble(Util.trim(c160m01b.getCustRoa()))));
					//關係人ROA
					mapInTitleRows.put("L140M01M.RELROA", c160m01b.getRelRoa() == null 
															? "" 
															: clearDf.format(Util.parseDouble(Util.trim(c160m01b.getRelRoa()))));
					//ROA查詢期間
					mapInTitleRows.put("L140M01M.ROABGNDATE", c160m01b.getRoaBgnDate() == null 
															? ""
															: Util.trim(CapDate.formatDate(c160m01b.getRoaBgnDate(), UtilConstants.DateFormat.YYYY_MM_DD)));
					mapInTitleRows.put("L140M01M.ROAENDDATE", c160m01b.getRoaEndDate() == null 
															? ""
															: Util.trim(CapDate.formatDate(c160m01b.getRoaEndDate(), UtilConstants.DateFormat.YYYY_MM_DD)));
				} else {
					mapInTitleRows.put("L140M01M.SHOWCHGRATE", "N");
					mapInTitleRows.put("L140M01M.RATEADD", "");
					mapInTitleRows.put("L140M01M.CUSTROA", "");
					mapInTitleRows.put("L140M01M.RELROA", "");
					mapInTitleRows.put("L140M01M.ROABGNDATE", "");
					mapInTitleRows.put("L140M01M.ROAENDDATE", "");
				}
				//是否符合本行規定
				mapInTitleRows.put("L140M01M.ISLEGAL", Util.equals(Util.trim(c160m01b.getIsChgStDate()), "Y") || Util.equals(Util.trim(c160m01b.getIsChgRate()), "Y")
									? this.showYNPic4(Util.trim(c160m01b.getIsLegal()), prop)
									: "");
			} 
			
			//本案是否為撥貸逾一年以上未動工興建之空地貸款控管案件 -> 否
			if (Util.notEquals(isClearLand, "" ) && Util.equals(isClearLand, "N")) {
				mapInTitleRows.put("L140M01M.CTLTYPE", "");
				mapInTitleRows.put("L140M01M.FSTDATE", "");
				mapInTitleRows.put("L140M01M.LSTDATE", "");
				mapInTitleRows.put("L140M01M.ISCHGSTDATE", "");
				mapInTitleRows.put("L140M01M.CSTDATE", "");
				mapInTitleRows.put("L140M01M.CSTREASON", "");
				mapInTitleRows.put("L140M01M.ISCHGRATE", "");
				mapInTitleRows.put("L140M01M.ADOPTFG", "");
				mapInTitleRows.put("L140M01M.RATEADD", "");
				mapInTitleRows.put("L140M01M.CUSTROA", "");
				mapInTitleRows.put("L140M01M.RELROA", "");
				mapInTitleRows.put("L140M01M.ROABGNDATE", "");
				mapInTitleRows.put("L140M01M.ROAENDDATE", "");
				mapInTitleRows.put("L140M01M.ISLEGAL", "");
				mapInTitleRows.put("L140M01M.SHOWCHGRATE", "N");
				mapInTitleRows.put("L140M01M.ISCHGSTDATEDSCR", "");
				mapInTitleRows.put("L140M01M.ISCHGRATEDSCR", "");
			}

			newMapInTitleRows = columnToReportBeanNameFor1601R05(mapInTitleRows);
			
			if (Util.notEquals(isClearLand, "")) {
				// 舊案沒有空地貸款註記 不用印
				returnRows.add(newMapInTitleRows);
			}
		}

		if (hasClearLand) {
			return returnRows;
		} else {
			return null;
		}

	}
	
	private Map<String, String> columnToReportBeanNameFor1601R05(Map<String, String> mapInTitleRows) {

		if (mapInTitleRows == null) {
			return null;
		}

		Map<String, String> newMapInTitleRows = Util.setColumnMap(); // 轉換後欄位名稱
		newMapInTitleRows.put("ReportBean.column01",
				MapUtils.getString(mapInTitleRows, "L140M01M.CNTRNO", "")); // L140M01M.CNTRNO
																			// 額度序號
		newMapInTitleRows.put("ReportBean.column02", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCLEARLANDDSCR", "")); // L140M01M.ISCLEARLANDDSCR
		newMapInTitleRows.put("ReportBean.column03",
				MapUtils.getString(mapInTitleRows, "L140M01M.FSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column04",
				MapUtils.getString(mapInTitleRows, "L140M01M.LSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column05", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCHGSTDATEDSCR", ""));
		newMapInTitleRows.put("ReportBean.column06",
				MapUtils.getString(mapInTitleRows, "L140M01M.CSTDATE", ""));
		newMapInTitleRows.put("ReportBean.column07",
				MapUtils.getString(mapInTitleRows, "L140M01M.CSTREASON", ""));
		newMapInTitleRows.put("ReportBean.column08",
				MapUtils.getString(mapInTitleRows, "L140M01M.ADOPTFG", ""));
		newMapInTitleRows.put("ReportBean.column09", MapUtils.getString(
				mapInTitleRows, "L140M01M.ISCHGRATEDSCR", ""));
		newMapInTitleRows.put("ReportBean.column10",
				MapUtils.getString(mapInTitleRows, "L140M01M.RATEADD", ""));
		newMapInTitleRows.put("ReportBean.column11",
				MapUtils.getString(mapInTitleRows, "L140M01M.CUSTROA", ""));
		newMapInTitleRows.put("ReportBean.column12",
				MapUtils.getString(mapInTitleRows, "L140M01M.RELROA", ""));
		newMapInTitleRows.put("ReportBean.column13",
				MapUtils.getString(mapInTitleRows, "L140M01M.ROABGNDATE", ""));
		newMapInTitleRows.put("ReportBean.column14",
				MapUtils.getString(mapInTitleRows, "L140M01M.ROAENDDATE", ""));
		newMapInTitleRows.put("ReportBean.column15",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISLEGAL", ""));
		newMapInTitleRows.put("ReportBean.column16",
				MapUtils.getString(mapInTitleRows, "L140M01M.CTLTYPE", "")); // L140M01M.CTLTYPE

		if (mapInTitleRows.containsKey("L140M01M.ISCLEARLAND")) {
			newMapInTitleRows.put("ReportBean.column17", MapUtils.getString(
					mapInTitleRows, "L140M01M.ISCLEARLAND", "")); // L140M01M.ISCLEARLAND
		}

		newMapInTitleRows.put("ReportBean.column18",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISCHGSTDATE", "")); // L140M01M.ISCHGSTDATE
		newMapInTitleRows.put("ReportBean.column19",
				MapUtils.getString(mapInTitleRows, "L140M01M.ISCHGRATE", "")); // L140M01M.ISCHGRATE
		newMapInTitleRows.put("ReportBean.column20",
				MapUtils.getString(mapInTitleRows, "L140M01M.SHOWCHGRATE", "")); // L140M01M.SHOWCHGRATE

		return newMapInTitleRows;
	}
	
	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得欄位文字□是□否
	 * 
	 * @param type
	 *            Y=是 N=否
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic4(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否

		return str.toString();
	}
	
	// J-106-0195-001 Web e-Loan授信管理系統異常通報簽報書新增本行重大偶發事件通報作業要點相關欄位
	// 取得欄位文字□ ■
	private String showYNPic7(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}

		return str.toString();
	}
	
	/**
	 * 設定L160M01B資料
	 * 
	 * @param rptVariableMap
	 *            javabeanMap
	 * @param list
	 *            L160M01B List
	 * @return rptVariableMap javabeanMap
	 */
	private String getQuotaNoString(int caseType, String allCanPay, List<C160M01B> c160m01bList, Properties properties) {
		
		String quotaNo = "";
		//如為團貸案號, 額度序號會簡略印出共x筆
		if(caseType == 2){
			
			if(null != c160m01bList && !c160m01bList.isEmpty()){
							
				quotaNo = Util.nullToSpace(c160m01bList.get(0).getCntrNo()).replace("　", " ").trim();
				quotaNo = c160m01bList.size() > 1 
							? quotaNo + properties.getProperty("comma") + properties.getProperty("total") + c160m01bList.size() + properties.getProperty("record")
							: quotaNo;
			}
			
			return quotaNo;
		}
		
		//caseType == 1
		if (UtilConstants.DEFAULT.是.equals(allCanPay)) {
			// L160M01A.message3=簽報書項下額度明細表全部動用
			quotaNo = properties.getProperty("L160M01A.message3");
		} 
		else {
			
			for (C160M01B c160m011b : c160m01bList) {
				quotaNo += Util.nullToSpace(c160m011b.getCntrNo()).replace("　", " ").trim() + "、";
			}
			
			quotaNo = quotaNo.substring(0, quotaNo.length()-1);
		}
		
		return quotaNo;
	}

	private String getDebtorName( List<C160M01B> c160m01bList,
								  int caseType,
								  String c160m01aCustName,
								  Properties properties){
		String debtorName = "";
		//如為團貸案號, 借款人姓名會簡略印出共x人
		if(caseType == 2 && null != c160m01bList && !c160m01bList.isEmpty()){
			String c160m01bName = c160m01bList.get(0).getCustId() + " " + c160m01bList.get(0).getCustName();
			debtorName = c160m01bName.concat(
								c160m01bList.size() > 1
								? properties.getProperty("comma") + properties.getProperty("total") + c160m01bList.size() + properties.getProperty("people")
								: c160m01bName);
		}
		
		if(caseType == 1){
			List<L140M01A> l140m01aList = new ArrayList<L140M01A>();
			for(C160M01B c160m01b : c160m01bList){
				l140m01aList.add(l140m01aDao.findByUniqueKey(c160m01b.getRefmainId()));
			}
			
			StringBuffer nameStr = new StringBuffer();
			Set<String> tempSet = new HashSet<String>();
			String custName = null;
			String mainCustName = "";
			
			for (L140M01A l140m01a : l140m01aList) {
				
				custName = l140m01a.getCustName();
				if (tempSet.contains(custName)) {
					continue;
				}
				
				if (custName.equals(Util.trim(c160m01aCustName))) {
					mainCustName = custName;
				} 
				else {
					nameStr.append(nameStr.length() > 0 ? "、" : "").append(custName);
				}
				
				tempSet.add(custName);
			}
			
			debtorName = mainCustName;
			if(nameStr.length() > 0){
				debtorName += Util.equals(mainCustName, "") ? "" : "、" + nameStr;
			}
		}
		
		return debtorName;
	}
	
	private OutputStream genR01(String mainId)
	throws FileNotFoundException, IOException, Exception {
		PageParameters params = new CapMvcParameters();
		params.put(EloanConstants.MAIN_ID, mainId); 
		if(Util.equals("Y", cls1161Service.useProd69Fmt(mainId))){
			return cls1161R01CRptService.generateReport(params);	
		}
		
		return cls1161R01RptService.generateReport(params);
	}
	

	private OutputStream genLMS1201R33(String mainId, Locale locale,
			String path, List<L120S09A> listL120s09a)
	throws FileNotFoundException, IOException, Exception {		
		
		
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(path);
		OutputStream outputStream = null;

		String branchName = null;

		C160M01A c160m01a = null;

		// L120S09A．洗錢防制明細檔		
//		Map<String, String> caseLvlMap = null;
		Map<String, String> typCdMap = null;
		Map<String, String> blackListCodeMap = null;
		Map<String, String> custRelationMap = null;

		try {		
			c160m01a = clsService.findC160M01A_mainId(mainId);
			
			

			branchName = Util.nullToSpace(branchService.getBranchName(Util
					.nullToSpace(c160m01a.getOwnBrId())));

			typCdMap = codeTypeService.findByCodeType("TypCd",
					locale.toString());
			blackListCodeMap = codeTypeService.findByCodeType("BlackListCode",
					locale.toString());
			custRelationMap = codeTypeService.findByCodeType(
					"BlackListRelation", locale.toString());

			if (typCdMap == null)
				typCdMap = new LinkedHashMap<String, String>();
			if (blackListCodeMap == null)
				blackListCodeMap = new LinkedHashMap<String, String>();
			if (custRelationMap == null)
				custRelationMap = new LinkedHashMap<String, String>();

			Properties prop_CLS1141R01RptServiceImpl = MessageBundleScriptCreator.getComponentResource(CLS1141R01RptServiceImpl.class);
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L160M01B.CNTRNO", "");

			//同 cls1161gridhandler :: queryViewData
			String caseNo = "";
			if(true){
				if (Util.equals(c160m01a.getCaseType(), "1")
						|| Util.equals(c160m01a.getCaseType(), "3")) {
					caseNo = Util.toSemiCharString(c160m01a.getCaseNo());
				} else if (Util.equals(c160m01a.getCaseType(), "2")) {
					String seq = Util.trim(c160m01a.getPackNo());
					if (!Util.isEmpty(seq)) {
						seq = "(" + seq + ")";
					}
					caseNo = Util.toSemiCharString(c160m01a.getLoanMasterNo()+ seq);
				}
			}
			rptVariableMap.put("L120M01A.CASEDATE", Util.trim(TWNDate.toAD(c160m01a.getCaseDate())));
			rptVariableMap.put("L120M01A.CASENO", caseNo);
			rptVariableMap.put("L120M01A.RANDOMCODE", Util.trim(c160m01a.getRandomCode()));

			// 黑名單查詢日期			
			Date blackListQDate = null;
			String ncResult = "";
			String uniqueKey = "";
			String refNo = "";
			String ncCaseId = "";
			if(clsService.active_SAS_AML(c160m01a)){
				
				L120S09B l120s09b = clsService.findL120S09B_mainId_latestOne(mainId);
				if(l120s09b !=null ){
					blackListQDate = l120s09b.getQueryDateS();	
					//========
					ncResult = Util.trim(l120s09b.getNcResult());
					uniqueKey = Util.trim(l120s09b.getUniqueKey());
					refNo = Util.trim(l120s09b.getRefNo());
					ncCaseId = Util.trim(l120s09b.getNcCaseId());
				}else{
					blackListQDate = LMSUtil.maxQueryDateS(listL120s09a);	
				}
				
				if(Util.isNotEmpty(ncResult)){
					Map<String, String> descMap = clsService.get_codeTypeWithOrder("SAS_NC_Result");
					if(descMap.containsKey(ncResult)){
						ncResult = (ncResult+" - "+Util.trim(descMap.get(ncResult))); 
					}
				}
			}else{
				blackListQDate = LMSUtil.maxQueryDateS(listL120s09a);	
			}			
			rptVariableMap.put("BLACKLISTQDATE", Util.trim(TWNDate.toAD(blackListQDate)));				
			rptVariableMap.put("L120S09B.NCRESULT", ncResult);
			rptVariableMap.put("L120S09B.REFNO", refNo);
			rptVariableMap.put("L120S09B.UNIQUEKEY", uniqueKey);
			rptVariableMap.put("L120S09B.NCCASEID", ncCaseId);
			

			String printFactoringTimeStr = Util.trim(lmsService
					.getSysParamDataValue("LMS_J1060057001_ON"));
			titleRows = LMSUtil.setL120S09aData(titleRows, listL120s09a,
					blackListCodeMap, custRelationMap, prop_CLS1141R01RptServiceImpl, 
					rptVariableMap, printFactoringTimeStr);


			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (titleRows != null) {
				titleRows.clear();
			}

			if (typCdMap != null) {
				typCdMap.clear();
			}
			if (blackListCodeMap != null) {
				blackListCodeMap.clear();
			}
			if (custRelationMap != null) {
				custRelationMap.clear();
			}

		}
		return outputStream;
	}
}
