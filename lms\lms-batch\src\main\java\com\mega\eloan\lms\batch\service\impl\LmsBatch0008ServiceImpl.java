/* 
 * ADEB001101ServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.batch.service.impl;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.gwclient.DWUCB1FTPClient;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.mfaloan.bean.ELF606;
import com.mega.eloan.lms.model.L290M01A;
import com.mega.eloan.lms.rpt.report.LMS9511R01RptService;
import com.mega.eloan.lms.rpt.service.LMS9551R01RptService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 接收DW SRMRP220聯貸變更授信條件案件應檢視核定條件控管表
 * </pre>
 * 
 * J-110-0532_05097_B1001 Web e-Loan授信系統法令遵循自評授信案件明細報表新增項目「聯貸變更案額度序號」
 * 
 * @since 2012/7/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/17,TammyChen,new
 *          <li>2012/11/29,Sunkist Wang,add interface for spring trasaction aop
 *          config
 *          <li>2012/11/30,Sunkist Wang,but!在LNSPServiceImpl.callSP() throw
 *          Exception 時將會導致這隻批次執行失敗，所以在還沒方案前改回來WebBatchService
 *          <li>2013/1/15,Tammy Chen,#1377 傳送卡務所有檔案內日期，均以YYYY/MM/DD格式顯示
 *          </ul>
 */
@Service("lmsbatch0008serviceimpl")
public class LmsBatch0008ServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	DebConfig debConfig;
	@Resource
	LMS9551R01RptService lms9551r01rptservice; // deb1010GService;

	@Resource
	DWUCB1FTPClient ftpClient;
	@Resource
	EloandbBASEService eloandbBaseService;

	@Resource
	LMS1201Service service1201;
	@Resource
	LMS1401Service service1401;

	@Resource
	UserInfoService userInfoService;
	@Resource
	ProdService prodService;
	@Resource
	LMS9511R01RptService lms9511r01rptservice;

	@Resource
	CodeTypeService codetypeService;

	final String UTF8 = "UTF-8";
	final String ContentType = "application/octet-stream";
	final String success = "Y";
	final String fail = "N";
	final String TW_DATE_FORMAT_STR = "YYYMMDD";
	final String TW_DATE_FORMAT_STR2 = "YYY/MM/DD";
	final String DATE_FORMAT_STR = "yyyy-MM-dd";
	final String DATE_FORMAT_STR_2 = "yyyy/MM/dd";
	final String DATE_FORMAT_STR_3 = "yyyy.MM.dd";
	final String ADD_DATA = "addList";
	final String SYSTEM = "system";
	File fileDir = null;
	final String getBrNoCol = "ICBCBR_BRNO_BRNM";
	boolean isSuccess = true;

	// 可接受的檔案名稱
	private String[] acceptFileNames = new String[] { "RPM220" };

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;
		result = exeMode(json);
		return result;
	}

	/**
	 * 整批第一次執行
	 * 
	 * @param json
	 * @return
	 */
	public JSONObject exeMode(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;

		// 產生主檔
		StringBuffer xx = new StringBuffer("");
		JSONObject request = json.getJSONObject("request");

		String dataStartDate = Util.trim(request.getString("dataStartDate"));
		String dataEndDate = Util.trim(request.getString("dataEndDate"));

		if (Util.equals(dataStartDate, "") || Util.equals(dataEndDate, "")) {
			final String DATE_FORMAT_STR = "yyyy-MM-dd";
			String currentDate = Util.getLeftStr(
					CapDate.getCurrentDate(DATE_FORMAT_STR), 7)
					+ "-01";
			dataStartDate = Util.getLeftStr(
					CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR, -1),
					7) + "-01";
			dataEndDate = CapDate.shiftDaysString(currentDate, DATE_FORMAT_STR,
					-1);

		}

		String localFilePath = PropUtil.getProperty("docFile.dir")
				+ File.separator + PropUtil.getProperty(

				"systemId") + File.separator + "900" + File.separator
				+ "LMS180R53A" + File.separator;

		File bkFolder = new File(localFilePath);

		try {
			if (!bkFolder.exists()) {
				FileUtils.forceMkdir(bkFolder);
			}

			// 將執行完成的檔案備份到bk
			// FileUtils.copyFile(inFile, new File(bkFolder.toString(), file));
			// FileUtils.forceDelete(inFile);
		} catch (IOException ex) {
			result = JSONObject.fromObject(WebBatchCode.RC_ERROR);
			result.element(WebBatchCode.P_RESPONSE, this.getClass().getName()
					+ "執行失敗！==>" + ex.getLocalizedMessage());
			logger.error(ex.getMessage(), ex);
			return result;
		}

		List<Map<String, String>> doMonthList = new ArrayList<Map<String, String>>();

		int i = 0;
		int colCount = 0;
		int intExeMonth = 1;

		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();

		while (CapDate.addMonth(Util.parseDate(dataStartDate), i).compareTo(
				Util.parseDate(dataEndDate)) <= 0) {
			colCount = colCount + 1;

			String colDate = TWNDate.toAD(CapDate.addMonth(
					Util.parseDate(dataStartDate), i));

			final String DATE_FORMAT_STR = "yyyy-MM-dd";

			String tDataStartDate = Util.getLeftStr(colDate, 7) + "-01";

			String xDataEndDate = TWNDate.toAD(CapDate.addMonth(
					Util.parseDate(dataStartDate), i + intExeMonth));

			String tDataEndDate = CapDate.shiftDaysString(xDataEndDate,
					DATE_FORMAT_STR, -1);

			Map<String, String> txMap = new HashMap<String, String>();

			txMap.put("dataStartDate", tDataStartDate);
			txMap.put("dataEndDate", tDataEndDate);

			doMonthList.add(txMap);

			i = i + intExeMonth;

		}

		String errorMsg = "";
		for (Map<String, String> doMonthMap : doMonthList) {

			String tDataStartDate = MapUtils.getString(doMonthMap,
					"dataStartDate");

			String tDataEndDate = MapUtils.getString(doMonthMap, "dataEndDate");

			try {
				errorMsg = this.downloadL180R53A(localFilePath, tDataStartDate);
				if (Util.notEquals(errorMsg, "")) {
					isSuccess = false;
				}
			} catch (Exception e) {
				isSuccess = false;
				logger.error("downloadL180R53A EXCEPTION!!", e);
				errorMsg = "downloadL180R53A EXCEPTION!!" + e.toString();
			}

			if (!isSuccess) {
				break;
			}

		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗=>" + errorMsg);
		}

		return result;
	}

	private String downloadL180R53A(String localFilePath, String downloadYYYYMM)
			throws Exception {
		String errorMsg = "";
		// String downloadYYYYMM/
		final int dataStartColumn = 0;// 使用者開始輸入欄位
		String msgId = IDGenerator.getUUID();
		Calendar today = Calendar.getInstance();
		int year = today.get(Calendar.YEAR);
		Date strDate1 = today.getTime();
		DateFormat df1 = new SimpleDateFormat("yyyyMM");
		String thisMonthFile = Util.trim(df1.format(strDate1)) + "01";
		if (Util.isNotEmpty(downloadYYYYMM)) {
			thisMonthFile = downloadYYYYMM.replace("-", "");
		}

		logger.info("ftpClient.getServerDir()=" + ftpClient.getServerDir());

		String ServerDir = "SRMRP220";
		String ftpFileName = "RPM220" + "_" + thisMonthFile + ".del";

		// 自RPA FTP下載ESG檔案
		String[] ftpFiles = ftpClient.list(msgId, ftpClient.getServerDir()
				+ ServerDir);
		for (String ftpFile : ftpFiles) {
			boolean match = false;
			if (ftpFile.toUpperCase().endsWith(".DEL")) {
				if (ftpFile.contains(thisMonthFile)) {

					for (String acceptName : acceptFileNames) {
						if (ftpFile.contains(acceptName)) {
							match = true;
							break;
						}
					}
					if (match) {
						ftpClient.download(msgId, ftpFile, new File(
								localFilePath, ftpFile).toString(), true,
								ftpClient.getServerDir() + ServerDir);

						// ftpClient.delete(msgId, ftpFile,
						// ftpClient.getServerDir());
					}

					break;
				}
			}
		}

		// 將excel儲存至DB
		File esgFile = new File(localFilePath + ftpFileName);
		List<Object[]> batchValues = new ArrayList<Object[]>();
		List<ELF606> elf606_list = new ArrayList<ELF606>();
		if (esgFile.exists()) {
			String errMsg = "";
			InputStreamReader isr = null;
			BufferedReader reader = null;
			String mainId = IDGenerator.getUUID();
			int idx = 0;
			try {
				BufferedReader bufferedReader = new BufferedReader(
						new FileReader(esgFile));
				String input;
				int count = 0;
				while ((input = bufferedReader.readLine()) != null) {
					count++;
				}

				// 檔案讀取路徑
				isr = new InputStreamReader(new FileInputStream(esgFile));
				reader = new BufferedReader(isr);
				String line;
				while ((line = reader.readLine()) != null) {
					++idx;
					if (line.startsWith("\uFEFF")) {
						line = line.replace("\uFEFF", "");
					}

					int column = dataStartColumn;
					String item[] = line.split("\t");

					L290M01A bean = new L290M01A();
					Object[] o = new Object[17];
					String[] stkObject = Util.trim(item[column++]).split(" ");
					String stkName = "";
					String stkNo = "";
					if (stkObject.length == 2) {
						stkNo = Util.trim(Util.getLeftStr(stkObject[0], 4));
						stkName = Util.trim(stkObject[1]);
					}
					String esgScore = Util.trim(item[column++]);
					String msciLevel = Util.trim(item[column++]);
					String esgLevel = Util.trim(item[column++]);
					String issLevel1 = Util.trim(item[column++]);
					String issLevel2 = Util.trim(item[column++]);

				}
				reader.close();
				isr.close();

			} catch (Exception e) {
				logger.error("parseExcel EXCEPTION!!", e);
				errorMsg = "parseExcel EXCEPTION!!" + e.toString();
			} finally {
				if (reader != null) {
					reader.close();
				}
				if (isr != null) {
					isr.close();
				}
			}
		} else {
			logger.error("parseExcel EXCEPTION!!", "該月份檔案不存在");
			errorMsg = "該月份檔案不存在";
		}

		return errorMsg;
	}

}
