package com.mega.eloan.lms.mfaloan.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;

@Service
public class MisLNF022ServiceImpl extends AbstractMFAloanJdbc implements
		MisLNF022Service {
	public List<?> findLNLNF022ForNetLoanNBal(String condition) {
		return this.getJdbc().queryForList("LNLNF022.selNetLoanNBal",
				new String[] { condition });
	}

	public List<?> findLNLNF022ForNetLoanQta(String condition) {
		return this.getJdbc().queryForList("LNLNF022.selNetLoanQta",
				new String[] { condition });
	}

	public List<?> findLNLNF022ForNetLoanNQta(String condition) {
		return this.getJdbc().queryForList("LNLNF022.selNetLoanNQta",
				new String[] { condition });
	}

	public List<?> findLNLNF022ForNetLoanSBal(String condition) {
		return this.getJdbc().queryForList("LNLNF022.selNetLoanSBal",
				new String[] { condition });
	}

	/**
	 * 以集團代碼查詢集團代號(總)授信明細
	 * 
	 * @param grpId
	 *            集團代碼
	 * @return 集團代號(總)授信明細
	 */
	@Override
	public Map<String, Object> findByGrpId(String grpId) {
		return getJdbc().queryForMap("LNF022.findByGrpId",
				new String[] { grpId });
	}// ;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.LnLnf022Service#findByRelCompDeposit
	 * (java.lang.String)
	 */
	@Override
	public Map<String, Object> findByRelCompDeposit(String custId, String dupNo) {
		Map<String, Object> result = getJdbc().queryForMap(
				"LNF022.findRelCompDepositByGrpId",
				new String[] { CapString.fillBlankTail(custId, 10) + dupNo,
						CapString.fillBlankTail(custId, 10) + dupNo });
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1);
		result.put("DATE", CapDate.formatDate(cal.getTime(), "yyyy-MM-dd"));
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.LnLnf022Service#findLoanMRate(java
	 * .lang.String, java.lang.String)
	 */
	@Override
	public List<Map<String, Object>> findLoanMRate(String custId, String dupNo) {
		List<Map<String, Object>> query1 = getJdbc().queryForList(
				"LNF150.findContractByCustidAndDupNo",
				new String[] { custId, dupNo });
		if (!query1.isEmpty()) {
			Map<String, Object> query2 = null;
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DATE, -1);
			String date = CapDate.formatDate(cal.getTime(), "yyyy-MM");
			for (Map<String, Object> data : query1) {
				query2 = getJdbc().queryForMap("LNF022.findFactAVLByContract",
						new Object[] { data.get("CONT") }, "LNF022_");
				if (query2 != null) {
					data.putAll(query2);
				} else {
					data.put("FACT_SWFT", null);
					data.put("QTA", null);
				}
				query2 = getJdbc().queryForMap(
						"MRATE.findByCustIdAndDupNoAndContractNoAndDate",
						new Object[] { custId, dupNo, data.get("CONT"), date },
						"MRA_");
				if (query2 != null) {
					data.put("USED_RTEY", query2.get("USED_RTEY"));
				} else {
					data.put("USED_RTEY", null);
				}
			}
			return query1;
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.ces.mfaloan.service.LnLnf022Service#findCustLoanStatus
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public Map<String, Object> findCustLoanStatus(String custId, String dupNo) {
		return getJdbc().queryForMap("LN022.findCustLoanStatus",
				new String[] { custId + dupNo, custId + dupNo });
	}

	@Override
	public List<Map<String, Object>> findMainLoanBalance(String custId,
			String dupNo) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		String query = "LNF022.findMainLoanBalance";
		// for test 72065570 0
		result = getJdbc().queryForList(query,
				new String[] { CapString.fillBlankTail(custId, 10) + dupNo });
		if (!CollectionUtils.isEmpty(result)) {
			Map<String, Object> quMap = result.get(0);
			if (quMap != null) {
				// String brNo = MapUtils.getString(quMap, "LNF022_BR_NO");
				// Object totalBal = MapUtils.getObject(quMap, "TOTAL_BAL");
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findCommonLoanBalance(String custId,
			String dupNo, String brNo) {
		String query1 = "LNF022.findCommonLoanBalance";
		String query2 = "LNF022.findRelationshipPeopleName";
		custId = CapString.fillString(custId, 10, false, ' ');
		String lngeFlag[] = new String[] { "'C'", "'D','E','G','L','S','N'" };
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		String atBrNo = "";
		for (String stg : lngeFlag) {
			String totalBal = "0";
			Map<String, Object> quMap = new HashMap<String, Object>();
			String sql = getSqlBySqlId(query1);
			sql = MessageFormat.format(sql, new Object[] { atBrNo, brNo, stg });
			sql = StringUtils.replace(sql, "\\", "'");
			List<Map<String, Object>> list1 = getJdbc().queryForList(sql,
					new Object[] { custId, dupNo }, 0, 500);
			for (Map<String, Object> qu : list1) {
				quMap.put("FG", qu.get("FG"));
				totalBal = CapMath.add(totalBal,
						MapUtils.getString(qu, "TOTAL_BAL", "0"));
			}
			String sql2 = MessageFormat.format(getSqlBySqlId(query2),
					new Object[] { atBrNo, brNo, stg });
			sql2 = StringUtils.replace(sql2, "\\", "'");
			List<Map<String, Object>> list = getJdbc().queryForList(sql2,
					new Object[] { custId, dupNo }, 0, 500);
			int ii = 1;
			String cn = "";
			for (Map<String, Object> quMap2 : list) {
				if (!CollectionUtils.isEmpty(quMap)
						&& !CollectionUtils.isEmpty(quMap2)) {
					quMap.putAll(quMap2);
					Object oo = quMap2.get("CNAME");
					if (ii > 1) {
						cn += "等";
						break;
					} else
						cn = CapString.trimNull(oo);
					ii++;
				}
			}
			if (!CollectionUtils.isEmpty(quMap)) {
				quMap.put("CNAME2", cn);
				quMap.put("TOTAL_BAL", totalBal);
				result.add(quMap);
			} else {
				result.add(null);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findCommonLoanBalanceNotInBrNo(
			String custId, String dupNo, String brNo) {
		String query1 = "LNF022.findCommonLoanBalance";
		String query2 = "LNF022.findRelationshipPeopleName";
		custId = CapString.fillString(custId, 10, false, ' ');
		String lngeFlag[] = new String[] { "'C'", "'D','E','G','L','S','N'" };
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		int countC = 0, countOther = 0;
		for (String stg : lngeFlag) {
			String totalBal = "0";
			String sql = getSqlBySqlId(query1);
			sql = MessageFormat.format(sql, new Object[] { "NOT", brNo, stg });
			sql = StringUtils.replace(sql, "\\", "'");
			List<Map<String, Object>> list1 = getJdbc().queryForList(sql,
					new Object[] { custId, dupNo }, 0, 500);
			for (Map<String, Object> qu : list1) {
				if ("'C'".equals(stg))
					countC++;
				else
					countOther++;
				Map<String, Object> quMap = new HashMap<String, Object>();
				quMap.put("FLAG", stg);
				quMap.put("FG", qu.get("FG"));
				String subBrNo = MapUtils.getString(qu, "LNF022_BR_NO");
				totalBal = CapMath.add(totalBal,
						MapUtils.getString(qu, "TOTAL_BAL", "0"));
				String sql2 = MessageFormat.format(getSqlBySqlId(query2),
						new Object[] { "", "'" + subBrNo + "'", stg });
				sql2 = StringUtils.replace(sql2, "\\", "'");
				List<Map<String, Object>> list = getJdbc().queryForList(sql2,
						new Object[] { custId, dupNo }, 0, 500);
				int ii = 1;
				String cn = "";
				for (Map<String, Object> quMap2 : list) {
					if (!CollectionUtils.isEmpty(quMap)
							&& !CollectionUtils.isEmpty(quMap2)) {
						quMap.putAll(quMap2);
						Object oo = quMap2.get("CNAME");
						if (ii > 1) {
							cn += "等";
							break;
						} else
							cn = CapString.trimNull(oo);
						ii++;
					}
				}
				if (!CollectionUtils.isEmpty(quMap)) {
					quMap.put("CNAME2", cn);
					quMap.put("TOTAL_BAL", totalBal);
				}
				result.add(quMap);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> findByContractAndCustId(String custId,
			String dupNo, String cntrNo) {
		return getJdbc().queryForList(
				"LNF022.selByContractAndCustId",
				new String[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
	}

	@Override
	public List<?> findLnf022_loan_date(String allCust, String cntrNo) {
		return this.getJdbc().queryForList("LN.LNF022.selLnf022_loan_date",
				new String[] { allCust, cntrNo });
	}

	@Override
	public String findDB2ChkALoanUse(String custId, String dupNo, String cntrNo) {
		String result = "N";
		Map<String, Object> row = getJdbc().queryForMap(
				"LNF022.MaxOpenAcFlag",
				new String[] { Util.addSpaceWithValue(custId, 10) + dupNo,
						cntrNo });
		if (row != null && "Y".equals(row.get("FLAG"))) {
			result = "Y";
		}
		return result;
	}

	@Override
	public Map<String, Object> selFactAmt1(String brno, String allCustId) {
		return getJdbc().queryForMap("LNF022.selFactAmt1",
				new String[] { brno, allCustId });
	}

	@Override
	public Map<String, Object> selFactAmt2(String brno, String allCustId) {
		return getJdbc().queryForMap("LNF022.selFactAmt2",
				new String[] { brno, allCustId });
	}

	@Override
	public List<Map<String, Object>> getLNF022_CtlData(String custId,
			String dupNo, String brNo) {

		String custIdDupNo = CapString.fillBlankTail(custId, 10) + dupNo;

		return getJdbc()
				.queryForList(
						"LN.LNF022_ctlData",
						new String[] { brNo, custIdDupNo, brNo, custIdDupNo,
								brNo, custIdDupNo, custIdDupNo, brNo, brNo,
								custIdDupNo, brNo, custIdDupNo, brNo,
								custIdDupNo, custIdDupNo, brNo });

	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	// J-108-0028_05097_B1001 Web
	// e-Loan國內企金授信覆審，價金履約保證額度序號為918起頭者，其覆審名單由敘做分行或管理行之所屬營運中心或自辦覆審分行辦理覆審。
	@Override
	public List<Map<String, Object>> getLNF022_forCtl(String custId,
			String dupNo, String brNo) {

		String idDupNo = CapString.fillBlankTail(custId, 10) + dupNo;
		// 參考 gfnGetLNF_MSTR_SQL
		return getJdbc().queryForListWithMax(
				"LN.LNF022_ctlLoanData",
				new String[] { brNo, idDupNo, brNo, idDupNo, brNo, idDupNo,
						idDupNo, brNo, custId, brNo, brNo, idDupNo, custId,
						brNo, brNo, idDupNo, brNo, idDupNo, brNo, idDupNo,
						idDupNo, brNo, custId, dupNo, idDupNo });
	}

	// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	// J-108-0028_05097_B1001 Web
	// e-Loan國內企金授信覆審，價金履約保證額度序號為918起頭者，其覆審名單由敘做分行或管理行之所屬營運中心或自辦覆審分行辦理覆審。
	@Override
	public Map<String, Object> gfnCTL_Import_LNF022(String branch,
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForMap(
				"LN022.gfnCTL_Import_LNF022",
				new String[] { branch, custIdDupNo, branch, custIdDupNo,
						branch, custIdDupNo, branch, custId, dupNo, branch,
						custIdDupNo, branch, custIdDupNo, custIdDupNo, branch,
						branch, custIdDupNo });
	}

	// 2020/04 配合新冠肺炎紓困貸款專案，新增 J.純紓困貸款戶之首次覆審。
	@Override
	public List<Map<String, Object>> gfnCTL_Import_LNF022_selContract(
			String branch, String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LN022.gfnCTL_Import_LNF022_selContract",
				new String[] { branch, custIdDupNo, branch, custIdDupNo,
						branch, custIdDupNo, branch, custId, dupNo, branch,
						custIdDupNo, branch, custIdDupNo, custIdDupNo, branch,
						branch, custIdDupNo });
	}

	@Override
	public Map<String, Object> gfnCTL_Get_Cust_Worst_Status(String branch,
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForMap(
				"LN022.gfnCTL_Get_Cust_Worst_Status",
				new String[] { branch, custIdDupNo, branch, custIdDupNo,
						branch, custIdDupNo, branch, custId, dupNo, branch,
						custIdDupNo, branch, custIdDupNo });
	}

	@Override
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R12(String brNo) {
		return getJdbc().queryForListWithMax("LN022.gfnGenerateCTL_FLMS180R12",
				new String[] { brNo, brNo, brNo });
	}

	/**
	 * 依額度序號找出動用率
	 */
	@Override
	public List<Map<String, Object>> getMrateByCntrno(String cntrNo, String date) {
		return getJdbc().queryForListWithMax("MRATE.findByCntrnoAndDate",
				new String[] { cntrNo, date });
	}// ;

	@Override
	public List<Map<String, Object>> findSumBycntrNo(String cntrNo) {
		return getJdbc().queryForListWithMax("LN.LNF022SumByCntrNo",
				new String[] { cntrNo });
	}

	/**
	 * 依借款人統編查詢資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	@Override
	public List<Map<String, Object>> getByCustId(String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForListWithMax("lnf022.findByCustId",
				new String[] { fullCustId, fullCustId });
	}// ;

	/**
	 * 依借款人統編查詢資料 J-105-0228-001 Web e-Loan企金授信簽報書新增私募基金相關建檔與報表。
	 */
	@Override
	public List<Map<String, Object>> getByCntrNo(String cntrNo) {

		return getJdbc().queryForListWithMax("lnf022.findByCntrNo",
				new String[] { cntrNo, cntrNo });
	}

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	@Override
	public List<Map<String, Object>> getByCntrNoAndCustId(String cntrNo,
			String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForListWithMax("lnf022.findByCntrNoAndCustId",
				new String[] { cntrNo, fullCustId, cntrNo, fullCustId });
	}

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findSumByCustId(String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForMap("LNF022.sumFactByCustId",
				new String[] { fullCustId });
	}

	@Override
	public List<Map<String, Object>> SumAvlFamtByCntrNoAndSwft(String cntrNo) {
		return getJdbc().queryForListWithMax(
				"LNF022.SumAvlFamtByCntrNoAndSwft", new String[] { cntrNo });
	}

	/**
	 * J-108-0107_05097_B1001 國內分行新核准往來企金客戶數統計表(按分行列表)
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findSumBalGroupByCntrNo(String cntrNo) {
		return getJdbc().queryForMap("LN.LNF022SumBalGroupByCntrNo",
				new String[] { cntrNo });

	}

	/**
	 * J-108-0107_05097_B1002 Web e-Loan企金授信新核准往來企金客戶數統計表新增動用資訊與調整效能
	 * 
	 * @param cntrNo
	 * @return
	 */
	@Override
	public Map<String, Object> findByCntrNoFetchOne(String cntrNo) {
		return getJdbc().queryForMap("LNF022.selByCntrNoFetchOne",
				new String[] { cntrNo });

	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findPureLoanByCustIdForRelatedCompany(
			String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForMap("MIS.LNF022.pureLoan",
				new String[] { fullCustId });

	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findImportLoanByCustIdForRelatedCompany(
			String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForMap("MIS.LNF022.importLoan",
				new String[] { fullCustId });

	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findExportLoanByCustIdForRelatedCompany(
			String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForMap("MIS.LNF022.exportLoan",
				new String[] { fullCustId });

	}

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public Map<String, Object> findArSellerLoanByCustIdForRelatedCompany(
			String custId, String dupNo) {
		String fullCustId = Util.getLeftStr(custId + "          ", 10) + dupNo;
		return getJdbc().queryForMap("MIS.LNF02P.arSellerLoan",
				new String[] { fullCustId });

	}

	/**
	 * J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程。
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Override
	public List<Map<String, Object>> gfnCTL_Import_LNF022_selContract_without_branch(
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LN022.gfnCTL_Import_LNF022_selContract_without_branch",
				new String[] { custIdDupNo, custIdDupNo, custIdDupNo, custId,
						dupNo, custIdDupNo, custIdDupNo, custIdDupNo,
						custIdDupNo });
	}

	/**
	 * J-109-0456
	 * 授信有效額度新臺幣五百萬元(含)以下且經信保七成(含)以上之不循環動用案件，除新做、增貸案件應於撥貸後之半年內辦理覆審外，免再辦理覆審。
	 */
	@Override
	public List<Map<String, Object>> findSumByCustIdAndGetIpfdRate(
			String custId, String dupNo) {
		String custIdDupNo = Util.addSpaceWithValue(custId, 10) + dupNo;
		return getJdbc().queryForListWithMax(
				"LNF022.sumFactByCustIdAndGetIpfdRate",
				new String[] { custIdDupNo });
	}

	@Override
	public List<String> getContractNoWithinValidCreditPeriod(
			String cntrNoString, String toDate) {

		// 修正checkmarx 安全性問題
		cntrNoString = cntrNoString.replaceAll("'", "");

		String[] cntrArray = cntrNoString.split(",");
		// 在測試環境有遇到2萬多筆的資料，這樣preparedStatement會當掉，所以分段
		int baseCount = 10000;

		int cntrlength = cntrArray.length;
		int times = cntrlength / baseCount;

		List<String> rtnList = new ArrayList<String>();

		for (int i = 1; i <= times; i++) {
			String paramSymbol = StringUtils.repeat("?,", baseCount);
			paramSymbol = paramSymbol
					.substring(0, paramSymbol.lastIndexOf(','));
			Object[] params = new Object[baseCount + 2];
			for (int j = 1; j <= baseCount; j++) {
				params[j - 1] = cntrArray[(i - 1) * baseCount + j - 1];
			}
			params[params.length - 1] = toDate;
			params[params.length - 2] = toDate;

			List<Map<String, Object>> list = this.getJdbc()
					.queryForAllListByCustParam(
							"LNF022.getContractNoWithinValidCreditPeriod",
							new Object[] { paramSymbol }, params);

			for (Map<String, Object> map : list) {
				rtnList.add(String.valueOf(map.get("LNF022_CONTRACT")));
			}
		}

		int remainCount = cntrlength - (times * baseCount);
		if (remainCount > 0) {
			Object[] params2 = new Object[remainCount + 2];

			String paramSymbol = StringUtils.repeat("?,", remainCount);
			paramSymbol = paramSymbol
					.substring(0, paramSymbol.lastIndexOf(','));

			for (int k = 1; k <= remainCount; k++) {
				params2[k - 1] = cntrArray[times * baseCount + k - 1];
			}

			params2[remainCount] = toDate;
			params2[remainCount + 1] = toDate;
			List<Map<String, Object>> list = this.getJdbc()
					.queryForAllListByCustParam(
							"LNF022.getContractNoWithinValidCreditPeriod",
							new Object[] { paramSymbol }, params2);

			for (Map<String, Object> map : list) {
				rtnList.add(String.valueOf(map.get("LNF022_CONTRACT")));
			}
		}

		return rtnList;
	}

	/**
	 * J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
	 */
	@Override
	public List<Map<String, Object>> findSmallBussCByCustId(String custId) {

		return getJdbc().queryForListWithMax("LNF022.selSmallBussCByCustId",
				new String[] { custId });
	}

	/**
	 * J-110-0234_05097_B1002 Web e-Loan國內企金簽報書小規模RPA修改
	 */
	@Override
	public List<Map<String, Object>> findSmallBussCByCustIdHasCancel(
			String custId) {

		return getJdbc().queryForListWithMax(
				"LNF020.selSmallBussCByCustIdHasCancel",
				new String[] { custId });
	}

	@Override
	public List<Map<String, Object>> findStartUpReliefByCustid(String custId,
			String[] rescueItem, String[] rescueItemSub) {

		String conditionStr = "";
		List<Object> params = new ArrayList<Object>();

		StringBuffer rescueItemBuf = new StringBuffer();
		if (rescueItem != null && rescueItem.length > 0) {

			for (String key : rescueItem) {
				rescueItemBuf
						.append(rescueItemBuf.toString().length() > 0 ? ","
								: "");
//				rescueItemBuf.append("'");
//				rescueItemBuf.append(key);
//				rescueItemBuf.append("'");

				rescueItemBuf.append("?");

				params.add(key);
			}
			rescueItemBuf.insert(0, " RESCUEITEM IN (");
			rescueItemBuf.append(")");
		}

		StringBuffer rescueItemSubBuf = new StringBuffer();
		if (rescueItemSub != null && rescueItemSub.length > 0) {

			for (String key : rescueItemSub) {
				rescueItemSubBuf
						.append(rescueItemSubBuf.toString().length() > 0 ? ","
								: "");
//				rescueItemSubBuf.append("'");
//				rescueItemSubBuf.append(key);
//				rescueItemSubBuf.append("'");

				rescueItemSubBuf.append("?");
				params.add(key);
			}
			rescueItemSubBuf.insert(0, " RESCUEITEM_SUB IN (");
			rescueItemSubBuf.append(")");
		}

		StringBuffer whereString = new StringBuffer("");
		if (Util.notEquals(rescueItemBuf.toString(), "")
				|| Util.notEquals(rescueItemSubBuf.toString(), "")) {

			if (Util.notEquals(rescueItemBuf.toString(), "")) {
				whereString.append(rescueItemBuf.toString());
			}

			if (Util.notEquals(rescueItemSubBuf.toString(), "")) {
				if (Util.notEquals(whereString.toString(), "")) {
					whereString.append(" OR ").append(
							rescueItemSubBuf.toString());
				} else {
					whereString.append(rescueItemSubBuf.toString());
				}
			}
		}

		if (Util.notEquals(whereString.toString(), "")) {
			whereString.insert(0, "AND ( ");
			whereString.append(") ");
		}

		params.add(custId);

		return this.getJdbc().queryForListByCustParam(
				"LNF022.selStartUpReliefByCustId",
				new Object[] { whereString.toString() },
				params.toArray(new Object[0]));

	}

	/**
	 * 經濟部協助中小型事業疫後振興專案貸款 & 經濟部協助中小企業轉型發展專案貸款
	 */
	@Override
	public List<Map<String, Object>> findByCustIdRescueItem(String custId,
			String rescueItem) {
		return getJdbc().queryForListWithMax("LNF022.selByCustIdRescueItem",
				new String[] { custId, rescueItem });
	}

}
