<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1835V00Page');
			</script>
            <div id="lms1835new" style="display:none;">
                <form id="lms1835v00From">
                    <table class='tb2' width="100%" cellspacing="0" cellspadding="0">
                        <tr>
                            <td width="40%">
                                <B>
                                    <th:block th:text="#{'L1835M01a.date'}">
                                        資料年月
                                    </th:block>
                                </B>:
                            </td>
                            <td>
                                <input id="dataDate" size="10" maxlength=7/><span class="text-red">(YYYY-MM)
                                    <BR>
                                    可不輸入，預設值為主機資料庫中最新月份之資料
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <B>
                                    <th:block th:text="#{'L1835M01a.brNo'}">
                                        請選擇欲產生名單之分行代號 
                                    </th:block>
                                </B>
                                : 
                            </td>
                            <td>
                                <select id="order" name="order">
                                </select>
                                <!--  <div id="gridviewBranch" name="gridviewBranch"/>-->
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div id="lms1835new2" style="display:none;">
                <form id="lms1835v00From2">
                    <table class='tb2' width="100%" cellspacing="0" cellspadding="0">
                        <tr>
                            <td>
                                <b>
                                    <label>
                                        <input type="radio" name="search" value="new" checked/>
                                        <th:block th:text="#{'L1835M01a.searchNew'}">
                                            最新資料
                                        </th:block>
                                    </label>
                                </b>
                            </td>
                            <td>
                                <b>
                                    <label>
                                        <input type="radio" name="search" value="old"/>
                                        <th:block th:text="#{'L1835M01a.searchOld'}">
                                            歷史資料
                                        </th:block>
                                    </label>
                                </b>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <div id="show1" style="display:none">
                                    <b>
                                        <th:block th:text="#{'L1835M01a.dataDateForFilter'}">
                                            資料日期
                                        </th:block>
                                    </b>&nbsp;&nbsp; <span id="searchDate" />
                                </div>
                                <div id="show2" style="display:none">
                                    <b>
                                        <th:block th:text="#{'L1835M01a.dataDateForFilter2'}">
                                            資料日期區間
                                        </th:block>:
                                    </b>
                                    <input id="starDate" type="text" size="8" maxlength=7 />~ <input id="endDate" type="text" size="8" maxlength=7 /><span class="text-red">(YYYY-MM)</span>
                                </div>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </th:block>
    </body>
</html>
