package com.mega.eloan.lms.las.pages;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.mega.eloan.lms.base.pages.AbstractPdfReportPage;

/**
 * 分行 稽核工作底稿 合併列印基本內容
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/las/lms1945r01")	
public class LMS1945R01Report extends AbstractPdfReportPage {

	public LMS1945R01Report() {
		super();
	}
	
	@Override
	public String getDownloadFileName() {
		return "lms1945r01.pdf";
	}

	@Override
	public String getFileDownloadServiceName() {
		return "lms1945r01rptservice";
	}
	
	//UPGRADE：或可參照LMS1200P01Page.java的方式return
	@Override
	protected String getViewName() {
		return null;
	}

}
