var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();

$(function(){
    //權限
    var auth = (responseJSON ? responseJSON.Auth : {});
    if (auth.readOnly || responseJSON.mainDocStatus != "010") {
        $("form").lockDoc();
    }
    $.extend(window.tempSave, {
	    handler: "lms1805formhandler",
	    action: "tempSave",
	    sendData: function(){
	    		if (responseJSON.page == "01") {
	                return $("#L180M01AForm").serializeData();
	            }
	    }
	});
    setCloseConfirm(true);/*設定關閉此畫面時要詢問*/
    $.form.init({
        formHandler: "lms1805formhandler",
        formPostData: {
            formAction: "query"
        },
        loadSuccess: function(json){
            $('body').injectData(json);
            if (json.page == "02") {
                gridview22();
            }
            if (responseJSON.mainDocStatus != "010") {
                $(".date").next("img").remove();
                $("input[type='text']").attr("disabled", true);
            }
        }
    });
    var btn = $("#buttonPanel");
    btn.find("#btnSave").click(function(showMsg){
        if ($("#L180M01AForm").valid()) {
            $.ajax({
                type: "POST",
                handler: "lms1805formhandler",
                data: {
                    formAction: "save",
                    page: responseJSON.page,
                    mainOid: responseJSON.mainOid
                },
                success: function(responseData){
                    $('#creator').val(responseData.Creator);
                    $('#updater').val(responseData.Updater);
                    $('#appraiser').val(responseData.apprId);   
					$('#randomCode').html(DOMPurify.sanitize(String(responseData.randomCode)));
                    $('#L180M01AForm').injectData(responseData);
             
                    CommonAPI.triggerOpener("gridview", "reloadGrid");
                }
            });
        }
    }).end().find("#btnSend").click(function(){
    	CommonAPI.confirmMessage(i18n.def["confirmApply"],function(b){
    		if(b){
    	        flowAction($.extend($("#tabForm").serializeData(), {
    	            page: responseJSON.page,
    	            saveData: true
    	        }));
    		}
    	});
    }).end().find("#btnAccept").click(function(){
    	CommonAPI.confirmMessage(i18n.def["confirmApprove"],function(b){
            flowAction({
                flowAction: true
            });
    	})
    }).end().find("#btnReturn").click(function(){
    	CommonAPI.confirmMessage(i18n.def["confirmReturn"],function(b){
            flowAction({
                flowAction: false
            });
    	})
    });
    
    function flowAction(sendData){
        $.ajax({
            type: "POST",
            handler: "lms1805formhandler",
            data: $.extend({
                formAction: "flowAction",
                mainId: responseJSON.mainId
            }, (sendData || {})),
            success: function(){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                setCloseConfirm(false);
                window.close();
            }
        });
    }
});