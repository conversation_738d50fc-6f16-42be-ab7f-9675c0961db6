package com.mega.eloan.cls.dc.action;

import org.apache.commons.lang.StringUtils;

import com.mega.eloan.cls.dc.util.ClsDXLUtil;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.util.DXLUtil;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ClsChkType
 * </pre>
 * 
 * @since 2013/1/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/30,Bang,new
 *          </ul>
 */
public class ClsChkType {

	/**
	 * 檢查目前執行的type類型
	 * 
	 * @param itemType
	 *            String: db2Xml中的type屬性欄位
	 * @param itemValue
	 *            String: 從.dxl中對應到db2XML後取得的值
	 * @param itemNum
	 *            int: db2Xml中的Num屬性欄位
	 * @param dxlXml
	 *            String: 已轉換為String型態之dxl檔
	 */
	public String chkAllType(String itemType, String itemValue, int itemNum,
			String dxlXml) throws Exception {

		String typeValue = itemValue;

		// 目前文件狀態
		if ("docStatus".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}// 目前文件狀態
		else if ("docStatus102".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		//dockind, FCLS114則用grant來判斷
		else if ("dockind4".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getDockind4(itemValue);
		}
		//dockind, 審核書用CaseLevel_No來判斷
		else if ("dockind".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getDockind(itemValue);
		}
		// 轉換中文數字為數值
		else if ("numC2E".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// 現行額度-是否循環使用
		else if ("reUse".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		/*
		 * // 區部別 else if ("typCd".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.codeConvert(itemType, itemValue); }
		 */
		// typer
		else if ("typer".equalsIgnoreCase(itemType)) {
			switch (itemNum) {
			case 0:
				typeValue = ClsDXLUtil.getEmpName(itemValue);
				typeValue = ClsDXLUtil.searchId(dxlXml, typeValue) == "" ? typeValue
						: ClsDXLUtil.searchId(dxlXml, typeValue);
				break;
			case 1:
				typeValue = ClsDXLUtil.getDateTime(itemValue);
				break;
			}
		}
		// 合計金額調整註記
		/*
		 * else if ("valueTune".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.codeConvert(itemType, itemValue); }
		 */
		/*
		 * // 有無xxxxx與本行有利害關係人(者) else if ("Yn3".equalsIgnoreCase(itemType)) {
		 * typeValue = ClsDXLUtil.codeConvert(itemType, itemValue); }
		 */
		// grant簽報書.authLvl
		else if ("authLvl".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getAuthLvl(itemValue);
		}
		// 簽報書grant.authLvl
		else if ("authLvl4".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getAuthLvl114(itemValue);
		}
		// grant 額度明細表
		else if ("grant".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getAuthLvl(itemValue);
		}
		// cdate
		else if ("cdate".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.parseRocDate(itemValue);
		}
		// charFull
		else if ("charFull".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getFullChar(itemValue);
		}
		// fillLeftZero
		else if ("fillLeftZero".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getFillLeftZero(itemValue, itemNum);
		}
		/*
		 * // grade else if ("grade".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.getGrade(itemValue); }
		 */
		/*
		 * // 連保人/保證人 else if ("guarantorType".equalsIgnoreCase(itemType)) {
		 * typeValue = ClsDXLUtil.codeConvert(itemType, itemValue); }
		 */
		// idn11
		/*
		 * else if ("idn11".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.getIdn11(itemValue, itemNum); }
		 */
		// money
		else if ("money".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getMoney(itemValue);
		}
		// projectNo
		else if ("projectNo".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getProjectNo(itemValue, itemNum);
		}
		/*
		 * // reRate else if ("reRate".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.getReRate(itemValue, itemNum); }
		 */
		// substr
		else if ("substr".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getSubstr(itemValue, itemNum);
		}
		// 是否轉換YN
		else if ("yn".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getYesNo(itemValue);
		}
		/*
		 * // Yn1 else if ("Yn1".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.getYn1(itemValue); }
		 */
		/*
		 * // 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人 else if
		 * ("Yn5".equalsIgnoreCase(itemType)) { typeValue =
		 * ClsDXLUtil.codeConvert(itemType, itemValue); }
		 */
		// -------------------------------------------------------------------------------------
		// CLS新增
		// -------------------------------------------------------------------------------------

		// 現住房屋 house
		else if ("house".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// jobkind
		else if ("jobkind".equalsIgnoreCase(itemType)) {
			if (StringUtils.isBlank(itemValue)) {
				typeValue = itemValue;
			} else {
				typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
			}
		}
		// stdCountry
		else if ("stdCountry".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// cityId
		else if ("cityId".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// areaId
		else if ("areaId".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// edu
		else if ("edu".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// oIncome
		else if ("oIncome".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// marry
		else if ("marry".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.codeConvert(itemType, itemValue);
		}
		// sexy
		else if ("sexy".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getSexy(itemValue);
		}
		// sysdate
		else if ("sysdate".equalsIgnoreCase(itemType)) {
			typeValue = Util.getCurrentTimestamp();
		}
		// chgOther
		else if ("chgOther".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getChgOther(itemValue);
		}
		// naturalFlag
		else if ("naturalFlag".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getNaturalFlag(itemValue);
		}
		// mateFlag
		else if ("mateFlag".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getMateFlag(itemValue);
		}
		// pmFlag
		else if ("pmFlag".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getPmFlag(itemValue);
		}
		// chgALoan
		else if ("chgALoan".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getChgALoan(itemValue);
		}
		// chargeFlag
		else if ("chargeFlag".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getChargeFlag(itemValue);
		}
		// setIns
		else if ("setIns".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getSetIns(itemValue);
		}
		// 是否轉換YN2:L140S02G-chgCase
		else if ("yn2".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getYesNo2(itemValue);
		}
		// agency:L140S02J
		else if ("agency".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getAgency(itemValue);
		}
		// yyyymm:L140S02E
		else if ("yyyymm".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getYyyymm(itemValue);
		}
		// yn2Num:C120S01E
		else if ("yn2Num".equalsIgnoreCase(itemType)) {
			typeValue = ClsDXLUtil.getYn2Num(itemValue);
		}
		// TODAY
		// 格式為NMMDDS
		// N：1碼可變更之英文字，目前請設為N，以免跟現行行員編號重複
		// MM:兩碼月
		// DD:兩碼日
		// S:1碼序號1~9、A~Z
		else if ("today".equalsIgnoreCase(itemType)) {
			typeValue = MainConfig.getInstance().getConfig().getTODAY();
		}
		// multiple: 數值欄位 x (num) 回傳
		else if ("multiple".equalsIgnoreCase(itemType)) {
			typeValue = DXLUtil.getMultiple(itemValue, itemNum);
		}

		return typeValue;
	}

}
