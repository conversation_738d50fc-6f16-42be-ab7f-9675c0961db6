/* 
 * L186M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L186M01ADao;
import com.mega.eloan.lms.model.L186M01A;

/** 覆審抽樣資料記錄檔 **/
@Repository
public class L186M01ADaoImpl extends LMSJpaDao<L186M01A, String>
	implements L186M01ADao {

	@Override
	public L186M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L186M01A findByUniqueKey(Date dataDate, String branchId, String custId, String dupNo, String randomType){
		ISearch search = createSearchTemplete();
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (randomType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "randomType", randomType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L186M01A> findByBrAndDateAndType(Date dataDate, String branchId, String randomType) {
		ISearch search = createSearchTemplete();
		if (dataDate != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		if (randomType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "randomType", randomType);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L186M01A> list = createQuery(search).getResultList();
		return list;
	}
}