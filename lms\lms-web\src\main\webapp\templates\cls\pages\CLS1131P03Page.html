<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:wicket="http://wicket.apache.org/">
	<body>
		<style>
			@media print {
				#func_div {
					display: none;
				}
				.pageBreak {
					page-break-after: always;
				}
			}

			p.watermark {
				font-size:38;
				color=#FCFCFC;
				z-index:-1;
				position:absolute;
				/*filter:Alpha(Opacity=20, FinishOpacity=20, Style=2);*/
				filter: Alpha(Opacity=15);/* for IE */
				Opacity:0.15;/* for Firefox */
			}
			.verticalPage table {
			  border-collapse: collapse;
			  width: 100%;
			}
			
			.verticalPage table, .verticalPage td, .verticalPage tr {
			  border: 1px solid black;
			}
			
			.verticalPage {
				width: 100%;
			}
			.img {
				text-align: center;
			}
		</style>
		<script>
			function watermark(msg){
				var lastone = document.getElementById('wmitest1');
				var str="";
				var tH=lastone.offsetTop;   
			
				var iT=Math.round(tH/1056);
				if(iT<1) iT=1;
			 
			    var kk=200;
			    var to=0;
			    var iO=0;
			    
			    while (to < tH){
					to=kk*iO;
					if (to < tH){
			           str=str+"<P class='watermark' style='top:"+
					       (kk*iO)+"px;left:"+(iO%2==0?160:610)+"px\'>"+msg+"</P>";
			           iO++;
				    }
				}
				document.getElementById('wmitest1').innerHTML=str;
			}
			function doPrint(){
				window.print();
			}
		</script>
		<div id="func_div" style="position:absolute; top:30px; left:12px; ">
			<input type="button" name="B0" value="列印" onclick="doPrint()">
		</div>
		<div id="wmitest1"></div>
		<div id="wmitest2"></div>
		<div id="wmitest3"></div>
		<div id="wmitest4"></div>
		
		<div wicket:id="CLS101S01S1" />
		<div wicket:id="CLS101S01S2" />
		<div wicket:id="CLS101S01S3" />
		<div wicket:id="CLS101S01S4" />
		<div wicket:id="CLS101S01S5" />
		<div wicket:id="CLS101S01S6" />
		
		<!-- 隱藏 -->
		<div style="display:none;">
			<input wicket:id="mainId" id="mainId" name="mainId" class="hidden" />
			<input wicket:id="custId" id="custId" name="custId" class="hidden" />
			<input wicket:id="dupNo" id="dupNo" name="dupNo" class="hidden" />
			<input wicket:id="queryType" id="queryType" name="queryType" class="hidden" />
			<input wicket:id="dataType" id="dataType" name="dataType" class="hidden" />
			<input wicket:id="isC120M01A" id="isC120M01A" name="isC120M01A" class="hidden" />
		</div>
		
	</body>
</html>
