package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;


@Controller@RequestMapping(path = "/fms/lms9015v00")
public class LMS9015V00Page extends AbstractEloanInnerView {

	public LMS9015V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.DOC_EDITING);
		// 加上Button

		addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Modify,
				LmsButtonEnum.Delete);
		renderJsI18N(LMS9015V00Page.class);
	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/LMS9015V00Page.js" };
	}
}
