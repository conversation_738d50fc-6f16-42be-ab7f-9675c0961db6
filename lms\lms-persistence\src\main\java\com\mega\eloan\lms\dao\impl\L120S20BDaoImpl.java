/* 
 * L120S20BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S20BDao;
import com.mega.eloan.lms.model.L120S20B;

/** 主要申請敘作內容明細檔 **/
@Repository
public class L120S20BDaoImpl extends LMSJpaDao<L120S20B, String> implements
		L120S20BDao {

	@Override
	public L120S20B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S20B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S20B> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120S20B> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120S20B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120S20B findByIndex02(String mainId, String cntrNo) {
		ISearch search = createSearchTemplete();

		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		return findUniqueOrNone(search);
	}
}