/* 
 * CLS1021ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.dao.ElsUserDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.MEGAImageApiEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.gwclient.EmailClient;
import com.mega.eloan.common.gwclient.PLOAN009;
import com.mega.eloan.common.gwclient.PLOAN012;
import com.mega.eloan.common.gwclient.PLOANGwClient;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.ElsUser;
import com.mega.eloan.common.model.SysParameter;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.MEGAImageService;
import com.mega.eloan.common.service.MEGAImageService.取得影像清單_ScanType;
import com.mega.eloan.common.service.RPAService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.cls.pages.CLS1220M01Page;
import com.mega.eloan.lms.cls.pages.CLS1220M03Page;
import com.mega.eloan.lms.cls.pages.CLS1220M04Page;
import com.mega.eloan.lms.cls.pages.CLS1220M05Page;
import com.mega.eloan.lms.cls.pages.CLS1220V10Page;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S02CDao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01DDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C122M01BDao;
import com.mega.eloan.lms.dao.C122M01CDao;
import com.mega.eloan.lms.dao.C122M01DDao;
import com.mega.eloan.lms.dao.C122M01EDao;
import com.mega.eloan.lms.dao.C122M01FDao;
import com.mega.eloan.lms.dao.C122M01GDao;
import com.mega.eloan.lms.dao.C122S01ADao;
import com.mega.eloan.lms.dao.C122S01BDao;
import com.mega.eloan.lms.dao.C122S01CDao;
import com.mega.eloan.lms.dao.C122S01EDao;
import com.mega.eloan.lms.dao.C122S01FDao;
import com.mega.eloan.lms.dao.C122S01GDao;
import com.mega.eloan.lms.dao.C122S01HDao;
import com.mega.eloan.lms.dao.C122S01YDao;
import com.mega.eloan.lms.dao.C900M01MDao;
import com.mega.eloan.lms.dao.C900M01NDao;
import com.mega.eloan.lms.dao.C900M01ODao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.megaimage.service.MEGAImageDBService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S02C;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122M01B;
import com.mega.eloan.lms.model.C122M01C;
import com.mega.eloan.lms.model.C122M01D;
import com.mega.eloan.lms.model.C122M01E;
import com.mega.eloan.lms.model.C122M01F;
import com.mega.eloan.lms.model.C122M01G;
import com.mega.eloan.lms.model.C122S01A;
import com.mega.eloan.lms.model.C122S01B;
import com.mega.eloan.lms.model.C122S01C;
import com.mega.eloan.lms.model.C122S01E;
import com.mega.eloan.lms.model.C122S01F;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.C122S01H;
import com.mega.eloan.lms.model.C122S01Y;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.C900M01N;
import com.mega.eloan.lms.model.C900M01O;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L161S01D;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapCommonUtil;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  線上申貸原始資料
 * </pre>
 * 
 * @since 2015/04/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/07,GaryChang,new
 *          </ul>
 */
@Service
public class CLS1220ServiceImpl extends AbstractCapService implements
		CLS1220Service {
	protected static final Logger LOGGER = LoggerFactory
							.getLogger(CLS1220ServiceImpl.class);
	
	@Resource
	BranchService branchService;
	
	@Resource
	CodeTypeService codetypeService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	C120S01ADao c120s01aDao;
	
	@Resource
	C120S01BDao c120s01bDao;
	
	@Resource
	C120S01CDao c120s01cDao;
	
	@Resource
	C122M01ADao c122m01aDao;
	
	@Resource
	C122M01BDao c122m01bDao;
	
	@Resource
	C122M01CDao c122m01cDao;
	
	@Resource
	C122M01DDao c122m01dDao;
	
	@Resource
	C122M01EDao c122m01eDao;
	
	@Resource
	C122S01ADao c122s01aDao;
	
	@Resource
	C122S01BDao c122s01bDao;
	
	@Resource
	C122S01CDao c122s01cDao;
	
	@Resource
	C122S01EDao c122s01eDao;
	
	@Resource
	L140M01ADao l140m01aDao;
	
	@Resource
	L140S02ADao l140s02aDao;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	EmailClient emailClient;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	private PLOANGwClient pLoanGwClient;
	
	@Resource
	EloandbBASEService eloandbService;
	
	@Resource
	RPAService rpaservice;
	
	@Resource
	SysParameterService sysparamService;
	
	@Resource
	MEGAImageService mEGAImageService;
	
	@Resource
	L161S01DDao l161s01dDao;
	
	@Resource
	C122M01FDao c122m01fDao;

	@Resource
	C120M01ADao c120m01aDao;
	
	@Resource
	C120S01DDao c120s01dDao;
	
	@Resource
	C101M01ADao c101m01aDao;
	
	@Resource
	C122S01FDao c122s01fDao;
	
	@Resource
	C122S01GDao c122s01gDao;
	
	@Resource
	C122S01HDao c122s01hDao;
	
	@Resource
	C122S01YDao c122s01yDao;
	
	@Resource
	C900M01MDao c900m01mDao;
	
	@Resource
	DocFileDao docFileDao;
	
	@Resource
	RelatedAccountService relatedAccountService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	MEGAImageDBService mEGAImageDBService;

	@Resource
	C900M01NDao c900m01nDao;
	
	@Resource
	C900M01ODao c900m01oDao;
	
	@Resource
	ElsUserDao elsUserDao;
	
	@Resource
	C122M01GDao c122m01gDao;

	@Resource
	C101S02CDao c101s02cDao;
	
	@Resource
	LMSService lmsService;
	
	@Override
	public Page<C122M01A> getC122V01(ISearch search) {
		return c122m01aDao.findPage(search);
	}
	
	@Override
	public List<C122M01A> getC122M01AList(ISearch search){
		return c122m01aDao.findC122M01AList(search);
	}
	
	@Override
	public Page<C122M01A> getC122M01A(ISearch search){
		return c122m01aDao.findPage(search);
	}
	
	@Override
	public Page<C122M01C> getC122M01C(ISearch search){
		return c122m01cDao.findPage(search);
	}
	
	@Override
	public Page<C122S01A> getC122S01A(ISearch search){
		return c122s01aDao.findPage(search);
	}
	
	@Override
	public Page<C122S01B> getC122S01B(ISearch search){
		return c122s01bDao.findPage(search);
	}
	
	@Override
	public C122M01A getC122M01A_byOid(String oid) {
		return c122m01aDao.findByOid(oid);
	}
	
	@Override
	public C122M01A getC122M01A_byMainId(String mainId) {
		return c122m01aDao.findByMainId(mainId);
	}
	
	@Override
	public C122M01B getC122M01B_byMainIdItemType(String mainId, String itemType){
		return c122m01bDao.findByMainIdItemType(mainId, itemType);
	}
	
	@Override
	public List<C122M01B> getC122M01B_byMainId(String mainId) {
		return c122m01bDao.findByMainId(mainId);
	}
	
	@Override 
	public C122M01C getC122M01C_byMainIdSeq(String mainId, int seq){
		return c122m01cDao.findByMainIdSeq(mainId, seq);
	}
	
	@Override
	public List<C122M01C> getC122M01C_byMainIdOrderBySeqAsc(String mainId){
		return c122m01cDao.findByMainIdOrderBySeqAsc(mainId);
	}
	
	@Override
	public List<C122M01C> getC122M01C_byMainIdOrderBySeqDesc(String mainId){
		return c122m01cDao.findByMainIdOrderBySeqDesc(mainId);
	}	
	
	@Override
	public List<C122M01D> getC122M01D_byMainIdOrderBySeqAsc(String mainId){
		return c122m01dDao.findByMainIdOrderBySeqAsc(mainId);
	}
	
	@Override
	public Page<L161S01D> getL161S01D(ISearch search){
		return l161s01dDao.findPage(search);
	}
	
	@Override
	public C122M01C changeOwnBrId(C122M01A parent_meta, String newBrNo, String memo) throws CapMessageException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ploanCaseId = Util.trim(parent_meta.getPloanCaseId());
		if(Util.isEmpty(ploanCaseId)){
			return null;
		}
		
		List<C122M01C> c122m01c_list = getC122M01C_byMainIdOrderBySeqDesc(parent_meta.getMainId());
		int c122m01c_list_size = c122m01c_list.size();
		C122M01C c122m01c = new C122M01C();
		c122m01c.setMainId(parent_meta.getMainId());
		c122m01c.setSeq(c122m01c_list_size==0?1:c122m01c_list.get(0).getSeq()+1);
		c122m01c.setBrNoBef(parent_meta.getOwnBrId());
		c122m01c.setBrNoAft(newBrNo);
		c122m01c.setMemo(memo);
		c122m01c.setCreator(user.getUserId());
		c122m01c.setCreateTime(CapDate.getCurrentTimestamp());
		c122m01cDao.save(c122m01c);
		//============================
		/* 檢視是否有寫入 c122m01c，但 c122m01a裡仍是舊的分行
		 
			select tb.*, tc.ownbrid, tc.applyts, tc.ploanCaseNo, tc.custid, tc.custname from
			(select mainid, max(seq) as seq from lms.c122m01c where 1=1 group by mainid)  ta 
			inner join lms.c122m01c tb on ta.mainid=tb.mainid and ta.seq=tb.seq
			left outer join lms.c122m01a tc on tb.mainid=tc.mainid 
			where BRNOAFT != ownbrid
		
		*/
		for(C122M01A model : c122m01aDao.findBy_ploanCaseId(ploanCaseId)){
			model.setOwnBrId(newBrNo);		
			clsService.daoSave(model);
			//============================
			C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(model.getMainId(), model.getCustId(), model.getDupNo());
			if(c120m01a!=null){
				c120m01a.setOwnBrId(newBrNo);
				clsService.daoSave(c120m01a);
			}
		}	
		
		if(clsService.is_function_on_codetype("c122_ploan_update_brNo") && 
				Util.notEquals(parent_meta.getApplyKind(), UtilConstants.C122_ApplyKind.I) && 
				Util.notEquals(parent_meta.getApplyKind(), UtilConstants.C122_ApplyKind.J)){
			JSONObject rtnJSON = ploan_update_brNo(parent_meta.getPloanCaseId(), newBrNo);
			if(!Util.equals(rtnJSON.optString("stat"), "ok")){
				throw new CapMessageException("送「線上貸款平台」失敗，請洽資訊處!", getClass());
			}				
		}
		
		// 更改文件數位化的資料分行別
		try {
			// 1.CALL SP更新上傳資料
			// J-122-0258新增開關判斷
			String changeBranch_FLAG = sysparamService.getParamValue("MI_API_changeBranch_FLAG");
			if ("Y".equals(changeBranch_FLAG)) {
				mEGAImageService.changeBranchCodeByCaseNo(parent_meta.getPloanCaseNo(), newBrNo);
			}
			// 2.更新已完成掃描的上傳紀錄檔
			List<Map<String, Object>> megaImageList = getAttchDoneMegaImageList(parent_meta.getMainId());
			for (Map<String, Object> map : megaImageList) {
				String docFileOid = CapString.trimNull(map.get("docFileOid"));
				List<C900M01M> c900m01ms = c900m01mDao.findByDocFileOid(docFileOid);
				if (c900m01ms != null && !c900m01ms.isEmpty()) {
					for (C900M01M c900m01m : c900m01ms) {
						c900m01m.setBranch(newBrNo);
						c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
						c900m01m.setUserCode(user.getUserId());
					}
					c900m01mDao.save(c900m01ms);
				}
			}

			// 3.刪除待上傳資料
			// 取得待上傳資料
			List<DocFile> undoneDocFiles = getAttchUndoneMEGAImageDocFiles(parent_meta.getMainId());
			for (DocFile docFile : undoneDocFiles) {
				// 尋找已上傳紀錄檔的批號，刪除文件數位化FTP上資料夾
				clsService.delEloanUploadImageDir(docFile.getOid());
			}
			// 3.待上傳資料更改分行後重新上傳
			for (DocFile docFile : undoneDocFiles) {
				List<C900M01M> c900m01ms = c900m01mDao.findByDocFileOid(docFile.getOid());
				if (c900m01ms != null && !c900m01ms.isEmpty()) {
					for (C900M01M c900m01m : c900m01ms) {
						Map<String, File> realFileMap = new HashMap<String, File>();
						File file = docFileService.getRealFile(docFile);
						realFileMap.put(docFile.getOid(), file);
						// 上傳指定項目
						c900m01m.setBranch(newBrNo);
						c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
						c900m01m.setUserCode(user.getUserId());
						// 編制上傳JSON
						JSONObject json = clsService.genJSON4EloanUploadImage(c900m01m);
						// FTP上傳資料
						String batId = mEGAImageService.ftpDirToMegaImage(json, "LMS", parent_meta.getMainId(), realFileMap);
						c900m01m.setBatId(batId);
						c900m01m.setDeletedTime(null);
					}
					c900m01mDao.save(c900m01ms);
				}
			}
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e, getClass());
		}
		
		return c122m01c;
	}

	@Override
	public void changeOwnBrId_notifyT1(String ploanCaseId, String applyKind, C122M01C c122m01c, String custId, String custName){
		if(clsService.is_function_on_codetype("c122_changeOwnBrId_notifyT1_func")){
			String env = "";
			//用參數, 來區分是否能夠寄給T1
			//因 testing 環境，不應該寄給分行T1
			boolean is_test = clsService.is_function_on_codetype("c122_changeBrNo_prodEnvFlag")?false:true;
			
			if(is_test){
				env = "【測試】";
			}
			
			String caseFmtdesc = "線上貸款案件";
			if(Util.equals(UtilConstants.C122_ApplyKind.P, applyKind)){
				caseFmtdesc = "線上信貸案件";
			}else if(Util.equals(UtilConstants.C122_ApplyKind.E, applyKind)){
				caseFmtdesc = "線上房貸案件";
			}else if(Util.equals(UtilConstants.C122_ApplyKind.I, applyKind) || 
					Util.equals(UtilConstants.C122_ApplyKind.J, applyKind)){
				caseFmtdesc = "線上青創案件";
			}
			
			List<String> toAddr = new ArrayList<String>();
			if(true){
				Set<String> mail_IT_list = _mailList("c122m01a_changeBrNo_notifyT1"); //寄送給?
				//~~~~~~~~~~~~~~~~~~
				if(is_test){
					toAddr.addAll(mail_IT_list);
				}else{
					toAddr.addAll(mail_IT_list);
					toAddr.add(c122m01c.getBrNoAft()+"<EMAIL>");
				}
			}
			
			if(toAddr.size()>0){
				String brNameBef = branchService.getBranchName(c122m01c.getBrNoBef());
				String brNameAft = branchService.getBranchName(c122m01c.getBrNoAft());
				
				String subject = env
					+ caseFmtdesc+"改分派通知"
					+"【客戶："+custId +" " + custName + "，案件編號："+ploanCaseId +"】"
					+"由 "+Util.trim(c122m01c.getBrNoBef())+" "+brNameBef
					+" 改分派至 "+Util.trim(c122m01c.getBrNoAft())+" "+brNameAft
					+"。請儘速處理!";					
				
				String body = _id_name(c122m01c.getCreator())+" 輸入之備註："+Util.trim(c122m01c.getMemo());
				emailClient.send(toAddr.toArray(new String[toAddr.size()]), subject, body);
			}
		}		
	}
	
	@Override
	public JSONObject ploan_update_brNo(String ploanCaseId, String newBrNo){
		PLOAN009 ploanObj = new PLOAN009();
		ploanObj.setCaseNo(ploanCaseId);
		ploanObj.setBranchCode(newBrNo);
		JSONObject jsonObj = pLoanGwClient.send_change_brNo(ploanObj);
		return jsonObj;
	} 
	
	@Override
	public JSONObject ploan_discard_loanCase(String empNo, String ploanCaseId){
		PLOAN012 ploanObj = new PLOAN012();
		ploanObj.setCaseNo(ploanCaseId);
		JSONObject jsonObj = pLoanGwClient.send_discard_loan(empNo, ploanObj);
		return jsonObj;
	}
	
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);
		String name = "";
		if(Util.isNotEmpty(id)){
			name = Util.trim(userInfoService.getUserName(id));
		}
		return Util.trim(id+" "+name);
	}
	
	@Override
	public void changeOrgBrId(C122M01A c122m01a, String newBrNo) throws CapMessageException {
		for(C122M01A model : c122m01aDao.findBy_ploanCaseId(Util.trim(c122m01a.getPloanCaseId()))){
			model.setOrgBrId(newBrNo);		
			clsService.daoSave(model);
		}	
	}
	
	@Override
	public C122S01A getC122S01A_byOid(String oid) {
		return c122s01aDao.findByOid(oid);
	}
	
	@Override
	public C122S01A getC122S01A_mainIdBatchNo(String mainId, int batchNo) {
		return c122s01aDao.findByUk(mainId, batchNo);
	}
	
	@Override
	public C122S01B getC122S01B_byOid(String oid){
		return c122s01bDao.findByOid(oid);
	}
	
	
	@Override
	public C122S01B getC122S01B_byMainId(String mainId){
		return c122s01bDao.findByMainId(mainId);
	}
	@Override
	public C122S01B getC122S01B_byUK(String mainId, Integer batchNo, String cntrNoMainId, Integer seq){
		return c122s01bDao.findByUniqueKey(mainId, batchNo, cntrNoMainId, seq);
	}
	
	@Override
	public Map<String, String> getC122S01A_batchNo(String mainId, boolean addNew){
		TreeMap<Integer, C122S01A> sortM = new TreeMap<Integer, C122S01A>();
		for(C122S01A c122s01a:c122s01aDao.findByMainId(mainId)){
			sortM.put(c122s01a.getBatchNo(), c122s01a);
		}
		
		Map<String, String> r = new LinkedHashMap<String,String>();
		
		int notApproveCnt = 0;
		if(true){
			for(Integer batchNo: sortM.keySet()){
				C122S01A c122s01a = sortM.get(batchNo);								
				if(Util.notEquals("Y", c122s01a.getApproveFlag())){
					notApproveCnt++;
				}
			}	
		}
		List<Integer> batchNo_list = new ArrayList<Integer>(sortM.keySet());
		for(int idx=batchNo_list.size()-1; idx>=0; idx--){
			Integer batchNo = batchNo_list.get(idx);
			C122S01A c122s01a = sortM.get(batchNo);
			r.put(String.valueOf(c122s01a.getBatchNo()), "第"+c122s01a.getBatchNo()+"版，"
					+(Util.equals("Y", c122s01a.getApproveFlag())?"已":"未")+"核准");
		}
		
		if(addNew && notApproveCnt==0){
			r.put(String.valueOf("-1"), "新增版本");	
		}
		return r;
	}
	
	@Override
	public L140S02A getL140S02A_byOid(String oid) {
		return l140s02aDao.findByOid(oid);
	}
	@Override
	public L140S02A getL140S02A_byUK(String mainId, Integer seq) {
		return l140s02aDao.findByUniqueKey(mainId, seq);
	}
	
	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C122M01A) {
					C122M01A meta = (C122M01A) model;
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						meta.setRandomCode(IDGenerator.getRandomCode());
					}
					
					if (Util.isNotEmpty(meta.getOid()) && 
							Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
											
						meta.setUpdater(user.getUserId());
						meta.setUpdateTime(CapDate.getCurrentTimestamp());
						//---
						//有一些異動：是由[個金徵信、簽報書、動審表]
						//另一些異動：是由menu 線上申貸>編製中
						//當 A 執行引入資料(受理中→審核中)，當發現不符金管會線上申貸的條件
						//    又執行(審核中→轉臨櫃)
						//若都寫入 DocLogEnum.SAVE 會看不出來
						//docLogService.record(meta.getOid(), DocLogEnum.SAVE);
						
						
						if(Util.equals(UtilConstants.C122_ApplyKind.P, meta.getApplyKind())
							|| Util.equals(UtilConstants.C122_ApplyKind.E, meta.getApplyKind())){
							docLogService.record(meta.getOid(), DocLogEnum.SAVE);	
						}						
					}
					
					c122m01aDao.save(meta);
					
					if (Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(meta.getMainId());						
					}
				}else if (model instanceof C122S01A) {
					C122S01A c122s01a = (C122S01A) model;
					if (Util.isNotEmpty(c122s01a.getOid())) {
						c122s01a.setUpdater(user.getUserId());
						c122s01a.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c122s01aDao.save(c122s01a);
				}else if (model instanceof C122S01B) {
					C122S01B c122s01b = (C122S01B) model;
					if (Util.isNotEmpty(c122s01b.getOid())) {
						c122s01b.setUpdater(user.getUserId());
						c122s01b.setUpdateTime(CapDate.getCurrentTimestamp());
					}
					c122s01bDao.save(c122s01b);
				} else if (model instanceof C122S01C) {
					C122S01C c122s01c = (C122S01C) model;
					c122s01cDao.save(c122s01c);
				}else if (model instanceof C122S01E) {
					C122S01E c122s01e = (C122S01E) model;
					if (Util.isNotEmpty(c122s01e.getOid())) {
					}
					c122s01eDao.save(c122s01e);
				}else if(model instanceof L161S01D){
					L161S01D l161s01d = (L161S01D) model;
					l161s01d.setUpdater(user.getUserId());
					l161s01d.setUpdateTime(CapDate.getCurrentTimestamp());
					l161s01d.setCreator(user.getUserId());
					l161s01d.setCreateTime(CapDate.getCurrentTimestamp());
					l161s01dDao.save(l161s01d);
				}else if(model instanceof C120M01A){
					C120M01A c120m01a = (C120M01A) model;
					c120m01aDao.save(c120m01a);
				}else if(model instanceof C120S01A){
					C120S01A c120s01a = (C120S01A) model;
					c120s01aDao.save(c120s01a);
				}else if(model instanceof C120S01B){
					C120S01B c120s01b = (C120S01B) model;
					c120s01bDao.save(c120s01b);
				}else if(model instanceof C120S01C){
					C120S01C c120s01c = (C120S01C) model;
					c120s01cDao.save(c120s01c);
				}else if(model instanceof C120S01D){
					C120S01D c120s01d = (C120S01D) model;
					c120s01dDao.save(c120s01d);
				}else if(model instanceof C122M01F){
					C122M01F c122m01f = (C122M01F) model;
					c122m01fDao.save(c122m01f);
				}else if (model instanceof C122S01G) {
					C122S01G c122s01g = (C122S01G) model;
					c122s01gDao.save(c122s01g);
				}else if (model instanceof C122S01H) {
					C122S01H c122s01h = (C122S01H) model;
					c122s01hDao.save(c122s01h);
				}else if (model instanceof C122S01Y) {
					C122S01Y c122s01y = (C122S01Y) model;
					c122s01yDao.save(c122s01y);
				} else if (model instanceof C101S02C) {
					C101S02C c101s02c = (C101S02C) model;
					c101s02cDao.save(c101s02c);
				}
			}
		}
	}
	
	@Override
	public void daoSaveC122M01A(C122M01A meta){
		c122m01aDao.save(meta);
	}
	
	@Override
	public void daoSaveC122S01A(C122S01A model){
		c122s01aDao.save(model);
	}
	
	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C122S01Y) {
					c122s01yDao.delete((C122S01Y) model);
				}
				if (model instanceof C101S02C) {
					c101s02cDao.delete((C101S02C) model);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C122M01A.class) {
			return c122m01aDao.findPage(search);
		}else if (clazz == C122M01E.class) {
			return c122m01eDao.findPage(search);
		}else if (clazz == C900M01O.class) {
			return c900m01oDao.findPage(search);
		}
		return null;
	}


	@SuppressWarnings("rawtypes")
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {	
		return null;
	}

	@Override
	public Map<String, String> get_ApplyDocStatusDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_cls1220m01 = MessageBundleScriptCreator.getComponentResource(CLS1220M01Page.class);
		m.put(UtilConstants.C122_ApplyStatus.受理中, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.0A0")));
		m.put(UtilConstants.C122_ApplyStatus.審核中, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.0B0")));
		m.put(UtilConstants.C122_ApplyStatus.不承做, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.Z01")));
		m.put(UtilConstants.C122_ApplyStatus.轉臨櫃, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.Z02")));
		m.put(UtilConstants.C122_ApplyStatus.已核准, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.Z03")));
		m.put(UtilConstants.C122_ApplyStatus.待補件, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.Z04")));
		m.put(UtilConstants.C122_ApplyStatus.動審表已覆核, Util.trim(prop_cls1220m01.get("C122M01A.applyStatus.Z05")));
		return m;
	}
	
	@Override
	public Map<String, String> get_PloanStatFlagDescMap(String applyKind){
		Map<String, String> m = new HashMap<String, String>();
		/*
		   給 客戶 看的訊息，只要出現｛不承作｝即可，不需出現｛票債信不良、申請信用評等未達標準...｝     select * from COM.BCODETYPE where codetype='ploan_c122m01a_statFlag'
		   給 經辦 看的訊息，應該出現｛不承作-票債信不良｝
		*/
		if(Util.equals(UtilConstants.C122_ApplyKind.P, applyKind)){
			Properties prop_cls1220m04 = MessageBundleScriptCreator.getComponentResource(CLS1220M04Page.class);
			m.put("0", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.0")));
			m.put("1", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.1")));
			m.put("2", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.2")));
			m.put("5", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.5")));
			m.put("6", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.6")));
			m.put("A", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.A")));
			m.put("D", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.D")));
			m.put("E", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.E")));
			m.put("X", Util.trim(prop_cls1220m04.get("C122M01A.statFlag.applyKindP.X")));
		}else if(Util.equals(UtilConstants.C122_ApplyKind.E, applyKind)){
			Properties prop_cls1220m05 = MessageBundleScriptCreator.getComponentResource(CLS1220M05Page.class);
			m.put("0", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.0")));
			m.put("1", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.1")));
			m.put("2", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.2")));
			m.put("5", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.5")));
			m.put("6", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.6")));
			m.put("A", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.A")));
			m.put("D", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.D")));
			m.put("E", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.E")));
			m.put("X", Util.trim(prop_cls1220m05.get("C122M01A.statFlag.applyKindE.X")));
		}
		return m;
	}
	
	@Override
	public Map<String, String> get_DocStatusDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_abstractEloan = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
		m.put("01O", Util.trim(prop_abstractEloan.get("docStatus.010")));
		m.put("02O", Util.trim(prop_abstractEloan.get("docStatus.020")));
		return m;
	}
	
	@Override
	public Map<String, String> get_ApplyKindB_statFlagDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_cls1220m01 = MessageBundleScriptCreator.getComponentResource(CLS1220M03Page.class);
		m.put("0", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.0")));
		m.put("1", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.1")));
		m.put("2", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.2")));
		m.put("3", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.3")));
		m.put("4", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.4")));
		m.put("A", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.A")));
		m.put("B", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.B")));
		m.put("C", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.C")));
		m.put("D", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.D")));
		m.put("Q", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.Q")));
		m.put("R", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.R")));
		m.put("S", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.S")));
		m.put("T", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.T")));
		m.put("U", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.U")));
		
		if(true){ //RPA
			m.put("E", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.E")));
			m.put("F", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.F")));
			m.put("G", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.G")));
			m.put("H", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.H")));
			m.put("I", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.I")));
			m.put("J", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.J")));
			m.put("K", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.K")));
			m.put("L", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.L")));
			m.put("M", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.M")));
			m.put("N", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.N")));
			m.put("O", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.O")));
			m.put("P", Util.trim(prop_cls1220m01.getProperty("C122M01A.statFlag.applyKindB.P")));
		}
		if(true){ //作廢
			m.put("X", Util.trim(prop_cls1220m01.get("C122M01A.statFlag.applyKindB.X")));
		}
		return m;
	}
	
	@Override
	public C120S01A findC120S01A(String mainId, String custId, String dupNo){
		return c120s01aDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public C120S01B findC120S01B(String mainId, String custId, String dupNo){
		return c120s01bDao.findByUniqueKey(mainId, custId, dupNo);
	}
	
	@Override
	public C120S01C findC120S01C(String mainId, String custId, String dupNo){
		return c120s01cDao.findByUniqueKey(mainId, custId, dupNo);		
	}
	
	@Override
	public C122M01A findLatestInProgressC122M01A(String ownBrId, String[] applyKind_arr, String custId, String dupNo){
		List<C122M01A> list = c122m01aDao.findInProgressC122M01A(ownBrId, applyKind_arr, custId, dupNo);
		if(list==null || list.size()==0){
			return null;
		}else{
			return list.get(0);
		}		
	}
	
	@Override
	public void addC122S01A_B(C122M01A meta, C122S01A c122s01a, List<L140S02A> l140s02a_list
			, List<String> promptMsg){
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		if(c122s01a.getBatchNo()<0){
			c122s01a.setMainId(meta.getMainId());
			/*
			 不能直接寫 c122s01a.setBatchNo(1);
			 每次選新增版本, 都傳入 -1
			● 原本無資料
			● 原本已有版本1, 再傳入 c122s01a.getBatchNo()==-1時，要取到版本2			  
			 */
			c122s01a.setBatchNo(maxBatchNoInC122S01A(meta.getMainId())+1);
			c122s01a.setApproveFlag("N");
			c122s01a.setCreator(user.getUserId());
			c122s01a.setCreateTime(nowTS);
		}
		List<C122S01B> s01b_list = new ArrayList<C122S01B>();
		for(L140S02A l140s02a :l140s02a_list){		
			String mainId = meta.getMainId();
			int batchNo = c122s01a.getBatchNo();
			String cntrNoMainId = l140s02a.getMainId();
			int seq = l140s02a.getSeq();
			C122S01B c122s01b = getC122S01B_byUK(mainId, batchNo, cntrNoMainId, seq);
			if(c122s01b==null){
				c122s01b = new C122S01B();
				//copy l140s02a to c122s01b
				if(true){				
					c122s01b.setMainId(mainId);
					c122s01b.setBatchNo(batchNo);
					c122s01b.setCntrNoMainId(cntrNoMainId);
					c122s01b.setSeq(seq);
					c122s01b.setCntrNo(l140s02a.getCntrNo());	
					c122s01b.setProdKind(l140s02a.getProdKind());
					c122s01b.setLoanCurr(l140s02a.getLoanCurr());
					c122s01b.setLoanAmt(l140s02a.getLoanAmt());
					String rateDesc = l140s02a.getRateDesc().replaceAll("<BR>", "\r\n")
						.replaceAll("<BR/>", "\r\n")
						.replaceAll("<br>", "\r\n")
						.replaceAll("<br/>", "\r\n");
					c122s01b.setRateDesc(rateDesc);					
					c122s01b.setCreator(user.getUserId());
					c122s01b.setCreateTime(nowTS);				
				}
				s01b_list.add(c122s01b);	
			}else{
				promptMsg.add(l140s02a.getCntrNo()+"-"+seq);
			}
		}		
		for(C122S01B m :s01b_list ){
			save(m);
		}
		
		if(true){
			save(c122s01a);
		}
		save(meta);
	}

	@Override
	public int maxBatchNoInC122S01A(String mainId) {
		return c122s01aDao.maxBatchNoInMainId(mainId);
	}
	
	@Override
	public void delC122S01B(C122M01A meta, List<String> c122s01b_oid_list){
		for(String oid: c122s01b_oid_list){
			C122S01B c122s01b = c122s01bDao.findByOid(oid);
			if(c122s01b!=null){
				c122s01bDao.delete(c122s01b);	
			}
		}
		save(meta);
	}
	
	@Override
	public List<C122M01A> queryUnMatchReason(String custId, String dupNo){
		return c122m01aDao.queryUnMatchReason(custId, dupNo);
	}
	
	@Override
	public L140M01A findL140M01A(String mainId){
		return l140m01aDao.findByMainId(mainId); 
	}

	@Override
	public String getDesc_ploan_basicInfo_serviceAssociateDeptCode(String s){
		if(Util.isNotEmpty(s)){
			if(Util.equals("90000", s)){
				Properties prop_cls1220m04 = MessageBundleScriptCreator.getComponentResource(CLS1220M04Page.class);
				return prop_cls1220m04.getProperty("ploanObj.serviceAssociateDeptCode.90000");
			}
			return LMSUtil.getDesc(clsService.get_codeTypeWithOrder("L140S02A_megaCode"), s); 
		}
		return s;
	}

	@Override
	public C122M01A getPloanParentMeta(C122M01A meta){
		//信貸
		if(Util.equals(UtilConstants.C122_ApplyKind.P, meta.getApplyKind())){
			return null;
		}else if(Util.equals(UtilConstants.C122_ApplyKind.Q, meta.getApplyKind())){
			return c122m01aDao.findPloanParentByApplyKind_ploanCaseId(UtilConstants.C122_ApplyKind.P, meta.getPloanCaseId());
		}	

		//房貸
		if(Util.equals(UtilConstants.C122_ApplyKind.E, meta.getApplyKind())){
			return null;
		}else if(Util.equals(UtilConstants.C122_ApplyKind.F, meta.getApplyKind())){
			return c122m01aDao.findPloanParentByApplyKind_ploanCaseId(UtilConstants.C122_ApplyKind.E, meta.getPloanCaseId());
		}
		
		//其他
		if(Util.equals(UtilConstants.C122_ApplyKind.O, meta.getApplyKind())){
			return null;
		}else if(Util.equals(UtilConstants.C122_ApplyKind.R, meta.getApplyKind())){
			return c122m01aDao.findPloanParentByApplyKind_ploanCaseId(UtilConstants.C122_ApplyKind.O, meta.getPloanCaseId());
		}
		
		return null;
	}

	private Set<String> _mailList(String codeType){
		Set<String> r = new HashSet<String>();
		for(CodeType obj : codetypeService.findByCodeTypeList(codeType)){
			String target = Util.trim(obj.getCodeDesc());
			if(Util.isNotEmpty(target)){
				r.add(target);
			}
		}
		return r;
	}

	@Override
	public String build_loanBrNo(Map<String, String> cache_map, C122M01A model){
		List<C122M01D> c122m01d_list = getC122M01D_byMainIdOrderBySeqAsc(model.getMainId());
		if(c122m01d_list.size()==0){
			return "";
		}else{
			List<String> list = new ArrayList<String>();
			for(C122M01D c122m01d : c122m01d_list){
				String brNo = c122m01d.getBrNo();
				if(!cache_map.containsKey(brNo)){
					cache_map.put(brNo, branchService.getBranchName(brNo));
				}
				list.add(brNo+" "+LMSUtil.getDesc(cache_map, brNo));
			}
			return StringUtils.join(list, ";");
		}
	}

	@Override
	public String build_payrollTransfersBrNo(Map<String, String> cache_map, C122M01A model){
		C120S01B c120s01b = clsService.findC120S01B(model.getMainId(), model.getCustId(), model.getDupNo());
		if(c120s01b!=null && Util.isNotEmpty(Util.trim(c120s01b.getPtaBrNo()))){
			List<String> list = new ArrayList<String>();
			if(true){
				String brNo = c120s01b.getPtaBrNo();
				if(!cache_map.containsKey(brNo)){
					cache_map.put(brNo, branchService.getBranchName(brNo));
				}
				list.add(brNo+" "+LMSUtil.getDesc(cache_map, brNo));
			}
			return StringUtils.join(list, ";");
		}
		return "";
	}

	@Override
	public List<C122M01A> findC122M01A_by_ploanCaseId(String ploanCaseId){
		return c122m01aDao.findBy_ploanCaseId(ploanCaseId);
	}

	@Override
	public Map<String, String> getPloanPlanList(String brno) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		for(CodeType obj : codetypeService.findByCodeTypeList("ploan_plan")){
			
			if("002".equals(brno)){
				// 002港都分行撈中鋼母子公司行銷方案  C開頭
				if(Util.trim(obj.getCodeValue()).startsWith("C")){
					map.put(Util.trim(obj.getCodeValue()),
							Util.trim(obj.getCodeDesc()));
				}
			}
			
		}

		return map;
	}

	@Override
	public List<C122S01E> findTodayRecord(String mainId) {
		Timestamp nowTime = CapDate.getCurrentTimestamp();
		Calendar start = Calendar.getInstance();
		start.setTime(nowTime);
		start.set(Calendar.HOUR_OF_DAY, 0);
		start.set(Calendar.MINUTE, 0);
		start.set(Calendar.SECOND, 0);
		start.set(Calendar.MILLISECOND, 0);
		Calendar end = Calendar.getInstance();
		end.setTime(nowTime);
		end.set(Calendar.HOUR_OF_DAY, 23);
		end.set(Calendar.MINUTE, 59);
		end.set(Calendar.SECOND, 59);
		end.set(Calendar.MILLISECOND, 999);
		Timestamp startTime = new Timestamp(start.getTimeInMillis());
		Timestamp endTime = new Timestamp(end.getTimeInMillis());
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		return c122s01eDao.findByMainIdUserIdIp(mainId, user.getUserId(),
				user.getLoginIP(), startTime, endTime);
	}
	
	@Override
	public Map<String, String> get_ApplyKindDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_cls1220v10 = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		//這邊要改喔~~~~~~
		m.put(UtilConstants.C122_ApplyKind.B, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.B")));
		m.put(UtilConstants.C122_ApplyKind.C, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.C")));
		m.put(UtilConstants.C122_ApplyKind.D, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.D")));
		m.put(UtilConstants.C122_ApplyKind.E, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.E2")));
		m.put(UtilConstants.C122_ApplyKind.F, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.E2")));
		m.put(UtilConstants.C122_ApplyKind.H, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.H")));
		m.put(UtilConstants.C122_ApplyKind.P, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.P2")));
		m.put(UtilConstants.C122_ApplyKind.Q, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.P2")));
		m.put(UtilConstants.C122_ApplyKind.I, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.I")));
		m.put(UtilConstants.C122_ApplyKind.J, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.J")));
		m.put(UtilConstants.C122_ApplyKind.O, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.O")));
		m.put(UtilConstants.C122_ApplyKind.R, Util.trim(prop_cls1220v10.get("C122M01A.applyKind.O")));
		return m;
	}
	
	@Override
	public Map<String, String> get_DocStatusNewDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_c1220v10 = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		m.put(UtilConstants.C122_DocStatus.待派案,Util.trim(prop_c1220v10.get("C122M01A.docStatus.A00")));
		m.put(UtilConstants.C122_DocStatus.已派案,Util.trim(prop_c1220v10.get("C122M01A.docStatus.A01")));
		m.put(UtilConstants.C122_DocStatus.補件通知,Util.trim(prop_c1220v10.get("C122M01A.docStatus.A02")));
		m.put(UtilConstants.C122_DocStatus.待初審,Util.trim(prop_c1220v10.get("C122M01A.docStatus.A99")));
		m.put(UtilConstants.C122_DocStatus.系統徵信,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B00")));
		m.put(UtilConstants.C122_DocStatus.徵信照會,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B01")));
		m.put(UtilConstants.C122_DocStatus.報價中,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B02")));
		m.put(UtilConstants.C122_DocStatus.系統建議核准,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B03")));
		m.put(UtilConstants.C122_DocStatus.人工審核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B04")));
		m.put(UtilConstants.C122_DocStatus.系統建議婉拒,Util.trim(prop_c1220v10.get("C122M01A.docStatus.B05")));
		m.put(UtilConstants.C122_DocStatus.簽案作業,Util.trim(prop_c1220v10.get("C122M01A.docStatus.C00")));
		m.put(UtilConstants.C122_DocStatus.簽案待覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.C01")));
		m.put(UtilConstants.C122_DocStatus.簽案已覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.C02")));
		m.put(UtilConstants.C122_DocStatus.契約作業,Util.trim(prop_c1220v10.get("C122M01A.docStatus.D00")));
		m.put(UtilConstants.C122_DocStatus.契約待覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.D01")));
		m.put(UtilConstants.C122_DocStatus.契約已覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.D02")));
		m.put(UtilConstants.C122_DocStatus.契約已簽定,Util.trim(prop_c1220v10.get("C122M01A.docStatus.D03")));
		m.put(UtilConstants.C122_DocStatus.定約通知,Util.trim(prop_c1220v10.get("C122M01A.docStatus.D04")));
		m.put(UtilConstants.C122_DocStatus.動用作業,Util.trim(prop_c1220v10.get("C122M01A.docStatus.E00")));
		m.put(UtilConstants.C122_DocStatus.動用待覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.E01")));
		m.put(UtilConstants.C122_DocStatus.動用已覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.E02")));
		m.put(UtilConstants.C122_DocStatus.已作廢,Util.trim(prop_c1220v10.get("C122M01A.docStatus.F00")));
		m.put(UtilConstants.C122_DocStatus.不承作,Util.trim(prop_c1220v10.get("C122M01A.docStatus.G00")));
		m.put(UtilConstants.C122_DocStatus.估價作業,Util.trim(prop_c1220v10.get("C122M01A.docStatus.H00")));
		m.put(UtilConstants.C122_DocStatus.估價待覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.H01")));
		m.put(UtilConstants.C122_DocStatus.估價已覆核,Util.trim(prop_c1220v10.get("C122M01A.docStatus.H02")));
		m.put(UtilConstants.C122_DocStatus.取消,Util.trim(prop_c1220v10.get("C122M01A.docStatus.I00")));
		m.put(UtilConstants.C122_DocStatus.系統結案,Util.trim(prop_c1220v10.get("C122M01A.docStatus.Z99")));
		
		return m;
	}
	
	@Override
	public Map<String, String> get_FlowIdDescMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_c1220v10 = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		m.put(UtilConstants.C122s01h_flowId.借保人資料,Util.trim(prop_c1220v10.get("C122S01H.flowId.1")));
		m.put(UtilConstants.C122s01h_flowId.案件簽報書,Util.trim(prop_c1220v10.get("C122S01H.flowId.2")));
		m.put(UtilConstants.C122s01h_flowId.消金契約書,Util.trim(prop_c1220v10.get("C122S01H.flowId.3")));
		m.put(UtilConstants.C122s01h_flowId.動用審核表,Util.trim(prop_c1220v10.get("C122S01H.flowId.4")));
		return m;
	}
	
	
	@Override
	public List<Map<String, Object>> getRealEstateAgentInfo(String mainId){
		C122M01A c122m01a = c122m01aDao.findByMainId(mainId);
		List<Map<String, Object>> list = this.eloandbService.getC126M01AByBranchNoAndCustIdOnly(c122m01a.getCustId(), c122m01a.getOwnBrId());
		return list;
	}
	
	@Override
	public C122M01F findC122M01F(String mainId){
		return c122m01fDao.findByUniqueKey(mainId);
	}
	
	@Override
	public List<C122S01Y> findC122S01YbyMainId(String mainId){
		return c122s01yDao.findByMainId(mainId);
	}
	
	@Override
	public C122S01Y findC122S01Y(String oid){
		return c122s01yDao.findByOid(oid);
	}
	
	@Override
	public List<C122M01G> findC122M01GbyMainId(String mainId){
		return c122m01gDao.findByMainId(mainId);
	}
	
	@Override
	public Map<String, String> get_IncomTypeMap(){
		Map<String, String> m = new HashMap<String, String>();
		Properties prop_cls1220v10 = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		m.put(UtilConstants.C122_IncomType.線下, Util.trim(prop_cls1220v10.get("C122M01A.incomType.1")));
		m.put(UtilConstants.C122_IncomType.線上, Util.trim(prop_cls1220v10.get("C122M01A.incomType.2")));
		return m;
	}
	
	@Override
	public List<C101M01A> findC101M01A(String ownBrId, String custId, String dupNo) {
		return c101m01aDao.findByIndex01(null, ownBrId, custId, dupNo);
	}
	
	
	@Override
	public List<C122S01F> findC122S01F_A02(String mainId, String docStatus) {
		return c122s01fDao.findByIndex01(mainId, docStatus);
	}
	
	@Override
	public Page<C122S01G> getC122S01GPage(ISearch search) {
		return c122s01gDao.findPage(search);
	}
	
	@Override
	public C122S01G getC122S01G(String oid){
		return this.c122s01gDao.findByOid(oid);
	}
	
	@Override
	public void deleteC122S01G(C122S01G c122s01g) {
		c122s01gDao.delete(c122s01g);
	}
	
	@Override
	public List<C122S01G> findC122S01G(String mainId, String docStatus) {
		return c122s01gDao.findByIndex01(mainId, docStatus);
	}
	
	@Override
	public List<C122S01G> findC122S01GbyMainid(String mainId) {
		return c122s01gDao.findByMainId(mainId);
	}
	
	@Override
	public void saveC122S01G(C122S01G c122s01g) {
		c122s01gDao.save(c122s01g);
	}
	
	@Override
	public Page<C122S01H> getC122S01HPage(ISearch search) {
		return c122s01hDao.findPage(search);
	}
	
	@Override
	public C122S01H getC122S01H(String oid){
		return this.c122s01hDao.findByOid(oid);
	}
	@Override
	public void deleteC122S01H(C122S01H c122s01h) {
		c122s01hDao.delete(c122s01h);
	}
	@Override
	public List<C122S01H> findC122S01H(String mainId, String flowId) {
		return c122s01hDao.findByIndex01(mainId, flowId);
	}

	@Override
	public C122S01H findC122S01HbyRefMinId(String refMainId, String flowId) {
		List<C122S01H> c122s01hs = c122s01hDao.findByRefMainId(refMainId, flowId);
		return c122s01hs.size()>0 ? c122s01hs.get(0) : null;
	}
	
	@Override
	public C122S01C getC122S01C(String mainId){
		return c122s01cDao.findByUniqueKey(mainId);
	}
	
	@Override
	public boolean showOneButtonAssignCase(String brNo){
		
		// 1.僅於自動派案分行清單中列表分行有此權限
		ISearch search = c900m01oDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "assigneeBrchId", brNo);
		List<C900M01O> c900m01oList = c900m01oDao.find(search);
		if(c900m01oList != null && c900m01oList.size() > 0){
			return true;
		}
		
		// 2.可直接從系統參數設定的方式
		SysParameter canPassSysparm = sysparamService.findByParam("C900M01N_CAN_PASS_BRNO");
		String[] canPassBrArr = StringUtils.split(Util.trim(canPassSysparm.getParamValue()), ",");
		List<String> canPassBrList = Arrays.asList(canPassBrArr);
		if(canPassBrList.contains(brNo)){
			return true;
		}
		
		return false;
	}
	
	@Override
	public Page<C900M01N> findC900m01nPage(ISearch search){
		return c900m01nDao.findPage(search);
	}
	
	@Override
	public void deleteC900m01nIsLeave(String brNo){
		// 已經離職的人就刪掉!!
		Map<String, ElsUser> userModelMap = userInfoService.getBRUser(MegaSSOSecurityContext.getUnitNo());
		List<String> needRemoveUser = new ArrayList<String>();
		for (Map.Entry<String, ElsUser> entry : userModelMap.entrySet()) {
			if(Util.equals("Y", entry.getValue().getLeaveFlag())){
				// 已經離職的人，需要被從userMap中移除
				needRemoveUser.add(entry.getKey());
			}
		}
		String[] deleteEmpNo = needRemoveUser.toArray(new String[0]);
		
		ISearch search = c900m01nDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		search.addSearchModeParameters(SearchMode.IN, "assignEmpNo", deleteEmpNo);
		search.setMaxResults(Integer.MAX_VALUE);
		
		List<C900M01N> needDeleteC900m01n = c900m01nDao.find(search);
		LOGGER.info("刪除" + brNo + "分行，C900M01N共" + needDeleteC900m01n.size() + "筆");
		LOGGER.info(deleteEmpNo.toString());
		
		c900m01nDao.delete(needDeleteC900m01n);
		
	}
	@Override
	public Page<ElsUser> findUserPage(ISearch search){
		return elsUserDao.findPage(search);
	}
	
	@Override
	public Map<String, C900M01N> findC900m01nByBrNo(String brNo){
		List<C900M01N> existList = c900m01nDao.findByIndex01(brNo);
		LOGGER.info("撈出" + brNo + "分行，C900M01N共" + existList.size() + "筆");
		Map<String, C900M01N> existMap = new HashMap<String, C900M01N>();
		
		// 先撈出已存在的資料，放在map裡好取用，不用一直存取DB
		for(C900M01N existC900m0n : existList){
			existMap.put(existC900m0n.getAssignEmpNo(), existC900m0n);
		}
		return existMap;
	}
	
	@Override
	public String saveAssignCaseEmps(String brNo, JSONArray assignArray) throws CapMessageException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 因為不讓畫面太難，所以要再一個動作裡做到新增/修改/刪除

		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		
		// step1.整理次要新增的userId + assignOrder
		List<Map<String, String>> userList = new ArrayList<Map<String, String>>();
		// List<String> assignOrderList = new ArrayList<String>();
		for (int i = 0; i < assignArray.size(); i++) {
			Map<String, String> oneUserMap = new HashMap<String, String>();
			oneUserMap.put("assignEmpNo", Util.trim(assignArray.getJSONObject(i).get("userId")));
			oneUserMap.put("assignOrder", Util.trim(assignArray.getJSONObject(i).get("assignOrder")));
			userList.add(oneUserMap);
		}
			
		// List<Map.Entry<String, C900M01N>> entries = new ArrayList<Map.Entry<String, C900M01N>>(existMap.entrySet());
		Collections.sort(userList, new Comparator<Map<String, String>>() {
			public int compare(Map<String, String> o1, Map<String, String> o2) {
				String p1 = o1.get("assignEmpNo");
				String p2 = o2.get("assignEmpNo");
				return Integer.valueOf(p1)-Integer.valueOf(p2);
			}
		});
		
		List<String> userIdList = new ArrayList<String>();
		for(Map<String, String> map : userList){
			userIdList.add(map.get("assignEmpNo"));
		}
		LOGGER.info("這次共新增經辦" + userIdList.toString() + "共" + userList.size() + "筆");
		
		// 刪除不在這次打勾的資料，他要留的資料就是要被勾
		// 也可以刪掉一些已離職、調單位的人
		// 直接用下SQL NOT IN的方法做刪除!!
		String[] userIds =  userIdList.toArray(new String[0]);
		int deleteCount = eloandbService.deleteC900m01nByNotInAssignEmpNo(brNo, userIds);
		LOGGER.info("已刪除非勾選經辦共" + deleteCount + "筆");
		
		if(userIds.length == 0){
			return "";
		}
		
		// 判斷是否有重複輸入的派案順序，並梳理需跳過的號碼
		List<String> haveEnterOrder = new ArrayList<String>();
		for(Map<String, String> map : userList){
			String assignOrder = map.get("assignOrder");
			if(Util.isNotEmpty(assignOrder)){
				if(haveEnterOrder.contains(assignOrder)){
					// C900M01N.error1=派案順序號碼不可重複，請調整後再點確定
					throw new CapMessageException(prop.getProperty("C900M01N.error1", "派案順序號碼不可重複，請調整後再點確定"), getClass());
				}
				if(new Integer(assignOrder) < 1){
					// C900M01N.error2=派案順序號碼不可小於1，請調整後再點確定
					throw new CapMessageException(prop.getProperty("C900M01N.error2", "派案順序號碼不可小於1，請調整後再點確定"), getClass());
				}
				haveEnterOrder.add(assignOrder);
			}
		}
		
		// 先撈出已存在的資料，放在map裡好取用，不用一直存取DB
		Map<String, C900M01N> existMap = this.findC900m01nByBrNo(brNo);
		
		// 要新增、修改的list
		List<C900M01N> saveList = new ArrayList<C900M01N>();
		int startOrder = 1;// 起始的派案順序
		
		// 開始處理這次新增/更新的資料
		for(Map<String, String> map : userList){
			String userId = map.get("assignEmpNo");
			String assignOrder = map.get("assignOrder");
			
			if(Util.isNotEmpty(userId)){
				C900M01N c900m01n = existMap.get(userId);// 先去已存在資料裡找
				if(c900m01n == null){
					c900m01n = new C900M01N();
					c900m01n.setLastAssignTime(null);// 最後派案時間	
					c900m01n.setCreator(user.getUserId());// 建立人員號碼
					c900m01n.setCreateTime(CapDate.getCurrentTimestamp());// 建立日期
				}
				c900m01n.setBrNo(brNo);// 分行代號
				c900m01n.setAssignEmpNo(userId);// 行員代號
				
				if(Util.isNotEmpty(assignOrder)){
					// 前端有填順序
					c900m01n.setAssignOrder(new Integer(assignOrder));// 派案順序
				}else{
					// 前端沒填順序，我來依序編
					while(haveEnterOrder.contains(String.valueOf(startOrder))){
						startOrder++;
					}
					c900m01n.setAssignOrder(new Integer(startOrder));// 派案順序
					startOrder++;
				}
				
				c900m01n.setUpdater(user.getUserId());// 異動人員號碼
				c900m01n.setUpdateTime(CapDate.getCurrentTimestamp());// 異動日期
				
				// 加進list中待更新
				saveList.add(c900m01n);
			}
			
		}
		c900m01nDao.save(saveList);
		
		return "";
	}
	
	/**
	 * 準備好C900M01N的各種物件
	 * @param brNo
	 * @param assignEmpNos
	 */
	private Map<String, Object> prepareC900m01nListAndMap(String brNo, String[] assignEmpNos){
		
		// 依照前端勾取的人員，撈出排好派案順序(時間+順序)的員編，因為是下SQL只能抓String
		List<String> orderList = findC900m01nWithFullOrder(brNo, assignEmpNos);
		// 依照前端勾取的人員，撈出對應的C900M01N，真實的JPA物件
		List<C900M01N> existList = c900m01nDao.findByBrNoAndAssignEmpNo(brNo, assignEmpNos);
		// 把JPA物件裝進map哩，後面好操作
		LinkedHashMap<String, C900M01N> tempExistMap = new LinkedHashMap<String, C900M01N>();
		for(C900M01N c900m01n : existList){
			// 初始化計算欄位，非資料庫中的欄位
			c900m01n.setMarketingCount(0);// ，本次案件為行銷人員的案件數
			c900m01n.setShouldCount(0);// 被分配到的案件數
			c900m01n.setMaxCount(0);// 可被分配的最大案件數
			c900m01n.setHaveCount(0);// 已被分配的案件數
			c900m01n.setMarketingC122m01aList(new ArrayList<C122M01A>());// 為自己行銷人員的案件
			tempExistMap.put(c900m01n.getAssignEmpNo(), c900m01n);
		}
		
		// 這個才是真的有排序過的map
		LinkedHashMap<String, C900M01N> existMap = new LinkedHashMap<String, C900M01N>();
		for(String assignEmp : orderList){
			existMap.put(assignEmp, tempExistMap.get(assignEmp));
		}
		
		Map<String, Object> prepareMap = new HashMap<String, Object>();
		prepareMap.put("orderList", orderList);
		prepareMap.put("existList", existList);
		prepareMap.put("existMap", existMap);
		
		return prepareMap;
	}

	/**
	 * 查出該分行下，傳入assignEmp的資料，用最後派案時間 lastAssignTime(null first)、派案順序 assignOrder做排序
	 * @param brNo
	 * @param assignEmpNos
	 * @return
	 */
	private List<String> findC900m01nWithFullOrder(String brNo, String[] assignEmpNos){
		List<String> haveOrderList = new ArrayList<String>();
		List<Map<String, Object>> fullList = eloandbService.findC900m01nByBrNoAndEmpNoOrderByTimeAndOrder(brNo, assignEmpNos);
		for(Map<String, Object> map : fullList){
			haveOrderList.add(Util.trim(map.get("ASSIGNEMPNO")));
		}
		
		return haveOrderList;
	}
	
	@Override
	public Map<String, String> checkHaveNeedAssignCase(String brNo, String[] assignEmpNos) throws CapMessageException{
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		
		Map<String, Object> prepareMap = prepareC900m01nListAndMap(brNo, assignEmpNos);
		// 這該分案順序排過的C900M01N
		LinkedHashMap<String, C900M01N> existMap = (LinkedHashMap<String, C900M01N>) prepareMap.get("existMap");
		
		// 撈出待分案的案件數，這邊未來要抽換成維哲的欄位判斷
		// 有成功維哲初審的案件，
		List<C122M01A> c122m01aList = findNeedAssignCase(brNo);
		Integer totalCount = c122m01aList.size();
		if(totalCount == 0){
			// C900M01N.error3=目前沒有待分案案件需執行分案
			throw new CapMessageException(prop.getProperty("C900M01N.error3", "目前沒有待分案案件需執行分案"), getClass());
		}
		
		String lastC122m01aOid = c122m01aList.get(totalCount - 1).getOid();// 紀錄最後一筆Oid，之後在真的分案的時候要當做斷點 
		
		for(int i = 0;i < c122m01aList.size(); i++){
			String marketingStaff = Util.trim(c122m01aList.get(i).getMarketingStaff());// 該案件的行銷人員
			
			if(Util.isNotEmpty(marketingStaff) && existMap.get(marketingStaff) != null){
				C900M01N c900m01n = existMap.get(marketingStaff);
				c900m01n.setMarketingCount(c900m01n.getMarketingCount() + 1);
			}
		}
		
		Map<String, String> resMap = new HashMap<String, String>();
		resMap.put("needAssignCount", String.valueOf(c122m01aList.size()));// 查出來待分案的筆數
		resMap.put("lastC122m01aOid", lastC122m01aOid);// 紀錄最後一筆Oid
		
		return resMap;
	}
	
	/**
	 * 查詢該分行被派案人員的各案件數
	 * @param brNo
	 * @return
	 * @throws CapMessageException 
	 */
	public List<Map<String, Object>> queryAssignCaseCount(String brNo, String[] assignEmpNos, String lastC122m01aOid) throws CapMessageException{
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		
		Map<String, Object> prepareMap = prepareC900m01nListAndMap(brNo, assignEmpNos);
		// 這該分案順序排過的員編
		List<String> orderList = (List<String>) prepareMap.get("orderList");
		// 參與此次分案的C900M01N(未排序)
		List<C900M01N> existList = (List<C900M01N>) prepareMap.get("existList");
		// 這該分案順序排過的C900M01N
		LinkedHashMap<String, C900M01N> existMap = (LinkedHashMap<String, C900M01N>) prepareMap.get("existMap");
		
		// 撈出待分案的案件數，這邊未來要抽換成維哲的欄位判斷
		// 有成功維哲初審的案件，
		List<C122M01A> c122m01aNotCleanList = findNeedAssignCase(brNo);
		List<C122M01A> c122m01aList = new ArrayList<C122M01A>();// 乾淨的待分案List
		for(C122M01A c122m01a : c122m01aNotCleanList){
			c122m01aList.add(c122m01a);
			if(c122m01a.getOid().equals(lastC122m01aOid)){
				// 碰到了此次分案的最後一筆，break掉
				break;
			}
		}
		Integer totalCount = c122m01aList.size();
		
		
		// 9999為要參數化，誰知道會有什麼新狀況...
		// ex:超多人一起分案，但能被分的只有一兩位
		SysParameter loopCountSysparm = sysparamService.findByParam("C900M01N_LOOP_COUNT");
		int loopCount = Integer.parseInt(loopCountSysparm.getParamValue());
		// 計算出每個人應得的數量
		int start = 0;
		while(totalCount > 0){
			int whichAssignNo = start%orderList.size();// 案件分到誰身上，從orderList取才是有排好順序的
			String assignNo = orderList.get(whichAssignNo);
			C900M01N assignEmp = existMap.get(assignNo);
			
			assignEmp.setShouldCount(assignEmp.getShouldCount() + 1);
			totalCount--;// 有消化案件
			
			// 不管有沒有幫忙消化掉案件，start就是一直往下滾
			// 等於上大家一直輪下去，輪到案件全消化完
			start++;
			
			if(start > loopCount){
				// C900M01N.error5=計算分案數量錯誤
				throw new CapMessageException(prop.getProperty("C900M01N.error5", "計算分案數量錯誤"), getClass());
			}
		}
		LOGGER.info("此次分案的最後一筆C122M01A.oid -> " + lastC122m01aOid);
		LOGGER.info("此次分案的總案件數totalCount -> " + c122m01aList.size());
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		for (Map.Entry<String, C900M01N> entry : existMap.entrySet()) {
			String assignEmpNo = entry.getKey();
			C900M01N c900m01n = entry.getValue();
			
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("assignEmpNo", assignEmpNo);// 行員代號
			data.put("assignEmpName", assignEmpNo);// 透過formatte去處理找名字
			data.put("shouldCount", c900m01n.getShouldCount());// 被分配的案件數
			data.put("maxCount", c900m01n.getMaxCount());// 可被分配的最大案件數
			data.put("totalCount", c122m01aList.size());// 此次分案的總案件數
			data.put("lastC122m01aOid", lastC122m01aOid);// 此次分案的最後一筆C122M01A.oid
			dataList.add(data);
		}
		
		return dataList;
	}
	
	/**
	 * 撈出待分案的案件數，這邊未來要抽換成維哲的欄位判斷
	 * @param brNo
	 * @return
	 */
	private List<C122M01A> findNeedAssignCase(String brNo){
		// 這邊撈出來的案件要有排序的條件，才不會判斷案件數根真的要分案的時候有落差
		ISearch search = c122m01aDao.createSearchTemplete();
		// 符合條件&狀態為待派案
		// 是否符合歡喜信貸初審通過 = "Y" or 初審查詢次數 >=3 次
		// 這種狀況一定是非團貸，且有經過你的初審
		if(true){
			search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", brNo);
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus", UtilConstants.C122_DocStatus.待派案);
			
			// 是否符合歡喜信貸初審通過狀態(Y/N)  initCheckStatus;
			// 初審查詢次數  initCheckTimes
			SearchModeParameter c1 = new SearchModeParameter(SearchMode.EQUALS, "initCheckStatus", "Y");
			SearchModeParameter c2 = new SearchModeParameter(SearchMode.IS_NULL, "initCheckStatus", "");
			
			SysParameter checkTimeSysparm = sysparamService.findByParam("C900M01N_CHECK_TIMES");
			int checkTimes = Integer.parseInt(checkTimeSysparm.getParamValue());
			SearchModeParameter c3 = new SearchModeParameter(SearchMode.GREATER_EQUALS, "initCheckTimes", checkTimes);
			SearchModeParameter c4 = new SearchModeParameter(SearchMode.AND, c2, c3);
			search.addSearchModeParameters(SearchMode.OR, c1, c4);
			
			search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "createTime", "");
			// 從最早進件的案件開始分案
			search.addOrderBy("createTime", false);
			search.setMaxResults(Integer.MAX_VALUE);
		}

		List<C122M01A> c122m01aList = c122m01aDao.findC122M01AList(search);
		
		LOGGER.info("共撈出待分案的案件" + c122m01aList.size() + "筆");
		return c122m01aList;
	}
	
	@Override
	public Map<String, Object> oneButtonAssignCase(String brNo, JSONArray assignCaseArray) throws CapMessageException{
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		
		String lastC122m01aOid = "";// 此次分案的最後一筆C122M01A.oid
		int totalCountFrontEnd = 0;// 此次分案的總案件數totalCount
		int shouldCountTotalFrontEnd = 0;// 此次分案各別案件數加總
		
		// 前端來的資訊，哪些人參與分案、各自分到幾筆案件
		List<String> assignEmpNoList = new ArrayList<String>();
		Map<String, Integer> shouldCountMap = new HashMap<String, Integer>();
		for (int i = 0; i < assignCaseArray.size(); i++) {
			String assignEmpNo = Util.trim(assignCaseArray.getJSONObject(i).get("assignEmpNo"));
			assignEmpNoList.add(assignEmpNo);
			Integer shouldCount = 0;
			try{
				shouldCount = new Integer(Util.trim(assignCaseArray.getJSONObject(i).get("shouldCount")));
			}catch (Exception e) {
				e.printStackTrace();
				// C900M01N.error12=案件數請輸入數字
				throw new CapMessageException(prop.getProperty("C900M01N.error12", "案件數請輸入數字"), getClass());
			}
			shouldCountMap.put(assignEmpNo, shouldCount);
			shouldCountTotalFrontEnd += shouldCount;
			
			if(Util.isEmpty(lastC122m01aOid)){
				// 取一次就好，大家值都一樣
				lastC122m01aOid = Util.trim(assignCaseArray.getJSONObject(i).get("lastC122m01aOid"));
			}
			if(totalCountFrontEnd == 0){
				// 取一次就好，大家值都一樣
				String tempTotalCount = Util.trim(assignCaseArray.getJSONObject(i).get("totalCount"));
				totalCountFrontEnd = Util.isEmpty(tempTotalCount) ? 0 : Integer.valueOf(tempTotalCount);
			}
			
		}
		
		if(Util.isEmpty(lastC122m01aOid)){
			// C900M01N.error6=無法取得此次分案的最後一筆C122M01A.oid
			throw new CapMessageException(prop.getProperty("C900M01N.error6", "無法取得此次分案的最後一筆C122M01A.oid"), getClass());
		}
		
		// 自己填寫後的各別數量!=原來應該要分案的總數量
		if(totalCountFrontEnd != shouldCountTotalFrontEnd){
			// C900M01N.error7=各分案人員加總後案件不等於此次應分案總件數
			throw new CapMessageException(prop.getProperty("C900M01N.error7", "各分案人員加總後案件不等於此次應分案總件數"), getClass());
		}
		
		// 這邊撈出來的List有可能跟前一步準備分案的案件數量有落差
		// 因為pLoan那邊持續有在進件，所以要再用lastC122m01aOid做一次斷點清理
		List<C122M01A> c122m01aNotCleanList = findNeedAssignCase(brNo);
		List<C122M01A> c122m01aList = new ArrayList<C122M01A>();// 乾淨的待分案List
		for(C122M01A c122m01a : c122m01aNotCleanList){
			c122m01aList.add(c122m01a);
			if(c122m01a.getOid().equals(lastC122m01aOid)){
				// 碰到了此次分案的最後一筆，break掉
				break;
			}
		}
		
		Integer totalCount = c122m01aList.size();
		if(totalCount == 0){
			// C900M01N.error8=目前沒有待分案案件需執行分案
			throw new CapMessageException(prop.getProperty("C900M01N.error8", "目前沒有待分案案件需執行分案"), getClass());
		}
		
		// 在這短時間內(計算分配->派案)，照理講不該有人介入手動分案，真的數量不符就重按一次不管他
		if(totalCountFrontEnd != totalCount){
			// C900M01N.error9=應分案總件數與資料庫中待分案總件數不符，請關閉視窗重新點選一鍵分案功能
			throw new CapMessageException(prop.getProperty("C900M01N.error9", 
					"應分案總件數與資料庫中待分案總件數不符，請關閉視窗重新點選一鍵分案功能"), getClass());
		}
		
		// 前端來的資料驗證完畢
		
		// 開始要撈資料做分配計算
		Map<String, Object> prepareMap = prepareC900m01nListAndMap(brNo, assignEmpNoList.toArray(new String[0]));
		// 這該分案順序排過的員編
		List<String> orderList = (List<String>) prepareMap.get("orderList");
		// 參與此次分案的C900M01N(未排序)
		List<C900M01N> existList = (List<C900M01N>) prepareMap.get("existList");
		// 這該分案順序排過的C900M01N
		LinkedHashMap<String, C900M01N> existMap = (LinkedHashMap<String, C900M01N>) prepareMap.get("existMap");
		for(C900M01N c900m01n : existList){
			c900m01n.setShouldCount(shouldCountMap.get(c900m01n.getAssignEmpNo()));
		}
		
		// 9999為要參數化，誰知道會有什麼新狀況...
		// ex:超多人一起分案，但能被分的只有一兩位
		SysParameter loopCountSysparm = sysparamService.findByParam("C900M01N_LOOP_COUNT");
		int loopCount = Integer.parseInt(loopCountSysparm.getParamValue());
		// 是否有發生pass掉案件的狀況
		boolean havePassCase = false;
		
		// start要接著往下用，才可以順著分的概念
		LOGGER.info("要分案的案件數為" + c122m01aList.size() + "筆");
		int start = 0;
		
		for(C122M01A c122m01a : c122m01aList){
			C900M01N assignEmp = null;
			// 選到誰就分給誰
			while(true){
				int whichAssignNo = start%orderList.size();// 案件分到誰身上，從orderList取才是有排好順序的
				String assignNo = orderList.get(whichAssignNo);
				assignEmp = existMap.get(assignNo);

				// 已經分滿案件的人要pass
				Integer haveCount = assignEmp.getHaveCount();// 已被分案數量
				Integer shouldCount = assignEmp.getShouldCount();// 前端傳來要被分案數量
				if(haveCount >= shouldCount){
					start++;
					continue;
				}
				
				if(start > loopCount){
					// C900M01N.error5=計算分案數量錯誤
					throw new CapMessageException(prop.getProperty("C900M01N.error5", "計算分案數量錯誤"), getClass());
				}
				
				// 抓到要被分案的人了
				start++;
				break;
			}
			
			
			String marketingStaff = Util.trim(c122m01a.getMarketingStaff());// 該案件的行銷人員
			if(Util.isNotEmpty(marketingStaff) && assignEmp.getAssignEmpNo().equals(marketingStaff)){
				// 沒分出去的要做紀錄
				havePassCase = true;
				List<C122M01A> marketingList = assignEmp.getMarketingC122m01aList();
				marketingList.add(c122m01a);
				assignEmp.setMarketingC122m01aList(marketingList);
				// 也是照算有分到一案
				assignEmp.setHaveCount(assignEmp.getHaveCount() + 1);
			}else{
				processAssign(c122m01a, assignEmp);
			}
		}
		
		c900m01nDao.save(existList);
		
		String message = "";
		// 有pass掉行銷人員案件的時候，訊息會顯示比較完整
		if(havePassCase){
			StringBuilder passMessage = new StringBuilder();// pass掉的訊息
			StringBuilder successMessage = new StringBuilder();// 正常分案的訊息
			for(C900M01N c900m01n : existList){
				int passCount = c900m01n.getMarketingC122m01aList().size();
				if(passCount > 0){
					for(C122M01A passC122m01a : c900m01n.getMarketingC122m01aList()){
						if(Util.isNotEmpty(passMessage)){
							passMessage.append("<BR/>");	
						}
						passMessage.append("借款人統編:" + passC122m01a.getCustId() + "  借款人姓名:" + passC122m01a.getCustName() + 
								"  案件編號:" + passC122m01a.getPloanCaseId());
					}
				}
				
				if(Util.isNotEmpty(successMessage)){
					successMessage.append("<BR/>");	
				}
				if(passCount > 0){
					int realCount = c900m01n.getHaveCount() - passCount;
					successMessage.append("信調人員" + c900m01n.getAssignEmpNo() + " " + userInfoService.getUserName(c900m01n.getAssignEmpNo()) +
							"應分案" + c900m01n.getShouldCount() + "筆，已分案" + realCount + "筆");
				}else{
					successMessage.append("信調人員" + c900m01n.getAssignEmpNo() + " " + userInfoService.getUserName(c900m01n.getAssignEmpNo()) +
							"已分案" + c900m01n.getHaveCount() + "筆");
				}
			}
			
			// C900M01N.message2=以下案件因信調人員為行銷人員，故未完成一鍵分案，請另行分案
			message = prop.getProperty("C900M01N.message2", "以下案件因信調人員為行銷人員，故未完成一鍵分案，請另行分案") 
					+ "<BR/>" + passMessage + "<BR/><BR/>" + successMessage;
		}else{
			// C900M01N.message1=已完成一鍵分案，共{0}筆
			message = MessageFormat.format(prop.getProperty("C900M01N.message1", "已完成一鍵分案，共{0}筆"),totalCount);
		}
		
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("havePassCase", havePassCase);// 是否有案件因行銷人員而pass
		result.put("message", message);// 分案的訊息
		return result;
	}

	/**
	 * 判斷案件真實分給誰
	 *
	 * @param c122m01a
	 * @param assignEmp
	 * @return
	 * @throws CapMessageException
	 */
	private String processAssign(C122M01A c122m01a, C900M01N assignEmp) throws CapMessageException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1220V10Page.class);
		
		// 進件管理資料檔 
		C122M01F c122m01f = findC122M01F(c122m01a.getMainId());

		if(assignEmp == null){
			LOGGER.info("在分案C122M01A.mainId為" + c122m01a.getMainId() + "的時候找不到可被分案人員");
			// C900M01N.error11=分配被分案人員錯誤
			throw new CapMessageException(prop.getProperty("C900M01N.error11","分配被分案人員錯誤"), getClass());
		}
		
		// 測試先不異動DB
		if(true){
			// ================抄CLS1220M10FormHandler.sendToMegaEmp================
			// 更新C122M01A、C122M01F，新增C122S01F
			// 壓人&壓狀態等等的，
			// 因為是針對信貸案件，所以不去壓設定估價行員等等欄位
			c122m01a.setDocStatus(UtilConstants.C122_DocStatus.已派案);
			c122m01a.setApprover(user.getUserId());// 誰在做分案=執行派案甲級代號
			c122m01a.setApproveTime(CapDate.getCurrentTimestamp());
			
			// 真的被分派的經辦
			c122m01f.setSignMegaEmpNo(assignEmp.getAssignEmpNo());
			// 不用擔心效能問題，userInfoService有userNameCache的機制在(很潮)
			c122m01f.setSignMegaEmpName(Util.trim(userInfoService
					.getUserName(assignEmp.getAssignEmpNo())));
			
			// 第一次案件執行派案的時間，撈報表用
			if(Util.isEmpty(c122m01f.getFirstAssignEmpTime())){
				c122m01f.setFirstAssignEmpTime(CapDate.getCurrentTimestamp());
			}
			
			save(c122m01a);
			save(c122m01f);
			clsService.addC122S01F(c122m01a,c122m01a.getDocStatus(),null,null);
			// ================抄CLS1220M10FormHandler.sendToMegaEmp================
		}
		LOGGER.info("案件分給:" + assignEmp.getAssignEmpNo() + ", 行銷人員為:" + c122m01a.getMarketingStaff());
		// 處理C900M01N
		assignEmp.setHaveCount(assignEmp.getHaveCount() + 1);
		assignEmp.setAssignBoss(user.getUserId());// 執行派案甲級代號
		assignEmp.setLastAssignTime(CapDate.getCurrentTimestamp());// 派案時間
		
		return assignEmp.getAssignEmpNo();
	}
	
	/**
	 * 檢查引介來源，若為地政士引介，需檢查是否有填寫地政士
	 * @param mainId
	 * @return
	 */
	@Override
	public String checkIntroduceSrcLaa(String mainId){
		
		Properties prop_cls1220m04 = MessageBundleScriptCreator.getComponentResource(CLS1220M04Page.class);
		
		StringBuilder errorMessage=new StringBuilder();
		C122M01F c122m01f = findC122M01F(mainId);
		
		if(Util.isEmpty(c122m01f.getIntroduceSrc())){
			errorMessage.append(
					prop_cls1220m04.getProperty("Message.dataCheck.12"));//[進件資料]-[引介來源]尚未填寫
		}else{
			if(Util.equals("4", c122m01f.getIntroduceSrc())){//選擇[地政士引介]
				//檢查是否有地政士清單
				List<C122S01Y> laaList= findC122S01YbyMainId(mainId);
				if(Util.isEmpty(laaList) || laaList.size() < 1){
					errorMessage.append(
							prop_cls1220m04.getProperty("Message.dataCheck.13"));//引介來源為[代書(地政士)引介]，請填寫地政士姓名
				}
			}
		}
		return errorMessage.toString();
	}
	
	/**
	 * 房貸+地政士引介案件取得前一案及後一案相同地政士的負責經辦
	 * @param brId
	 * @param mainId
	 * @return
	 */
	@Override
	public List<String> getSameMegaEmpForLaaCase(String brId, String mainId){
		C122M01A c122m01a = getC122M01A_byMainId(mainId);//主表
		List<String> sameMegaEmpList = new ArrayList<String>();
		String applyKind = Util.trim(c122m01a.getApplyKind());
		//先取得進件資料
		C122M01F c122m01f = c122m01a.getC122m01f();//findC122M01F(mainId);
		//如果是房貸+地政士案件，要檢查地政士
		if( (Util.equals(applyKind, "E") || Util.equals(applyKind, "F")) 
				&& Util.isNotEmpty(c122m01f)
				&& Util.equals("4", c122m01f.getIntroduceSrc()) ){
			
			List<C122S01Y> laaList= findC122S01YbyMainId(mainId);//本案的地政士清單
			
			for(C122S01Y c122s01y : laaList){
				C122S01Y lastMatchLaaCase = 
					c122s01yDao.findLaaCaseBy_brNo(brId, mainId, c122m01a.getApplyTS(), "last", Util.trim(c122s01y.getLaaName()));
				if(Util.isNotEmpty(lastMatchLaaCase)){
					C122M01F lastMatchC122m01f = findC122M01F(lastMatchLaaCase.getMainId());
					sameMegaEmpList.add(Util.trim(lastMatchC122m01f.getSignMegaEmpNo()));
				}
				C122S01Y nextMatchLaaCase = 
					c122s01yDao.findLaaCaseBy_brNo(brId, mainId, c122m01a.getApplyTS(), "next", Util.trim(c122s01y.getLaaName()));
				if(Util.isNotEmpty(nextMatchLaaCase)){
					C122M01F nextMatchC122m01f = findC122M01F(nextMatchLaaCase.getMainId());
					sameMegaEmpList.add(Util.trim(nextMatchC122m01f.getSignMegaEmpNo()));
				}
			}
		}
		return sameMegaEmpList;
	}
	
	/**
	 * 房貸案更新經辦重複分派之地政士案件註記
	 * @param brId
	 * @param mainId
	 * @return
	 */
	@Override
	public void updateIsSameMegaEmpLaaCaseFlag(String brId, String mainId){
		
		if(Util.isEmpty(brId) || Util.isEmpty(mainId)){
			return;
		}
		C122M01A c122m01a = getC122M01A_byMainId(mainId);//主表
		C122M01F c122m01f = null;
		
		if(Util.isNotEmpty(c122m01a)){
			
			String applyKind = Util.trim(c122m01a.getApplyKind());
			c122m01f = c122m01a.getC122m01f();
			
			//房貸案+地政士引介案件+已派案 才需要檢查並更新重複派案註記
			if( (Util.equals(applyKind, UtilConstants.C122_ApplyKind.E) ||
					Util.equals(applyKind, UtilConstants.C122_ApplyKind.F))//F:房貸從債務人，基本上不會是地政士引介
					&& Util.isNotEmpty(c122m01f) && Util.equals("4", c122m01f.getIntroduceSrc())
					&& Util.isNotEmpty(c122m01f.getSignMegaEmpNo()) ){
				String signMegaEmpNo = Util.trim(c122m01f.getSignMegaEmpNo());
				List<String> sameLaaMegaEmpList = getSameMegaEmpForLaaCase(brId,mainId);
				String isSameMegaEmpLaaCase = "N";
				if(Util.isNotEmpty(sameLaaMegaEmpList) && sameLaaMegaEmpList.size()>0){
					for(String megaEmp : sameLaaMegaEmpList){
						if(Util.equals(megaEmp, signMegaEmpNo)){
							//前案or後案為有相同地政士的案件與本案的簽案人員相同，要更新重複派案註記為Y
							isSameMegaEmpLaaCase = "Y";//有重複派案
							break;
						}
					}
				}
				c122m01f.setIsSameMegaEmpLaaCase(isSameMegaEmpLaaCase);
				save(c122m01f);
			}
		}
	}
	
	/**
	 * J-113-0101 調整消金進件管理作業於指派及改派房貸案件時限定為需有甲級授權以上人員
	 * 檢核房貸案件於指派或改派時該執行人員是否有甲級授權(含)以上
	 * @param mainId
	 * @return
	 */
	@Override
	public boolean checkAssignCaseAccessForHouseLoan(String mainId){
		boolean cankAssignCase = true;//先預設可以派
		if(clsService.is_function_on_codetype("J-113-0101")){
			C122M01A c122m01a = this.getC122M01A_byMainId(mainId);
			if(Util.isNotEmpty(c122m01a)){
				String applyKind = Util.trim(c122m01a.getApplyKind());
				// 房貸案件才需要檢查派案/改派是否有甲級權限
				if(Util.equals(applyKind, UtilConstants.C122_ApplyKind.E) || //E:房貸案
						Util.equals(applyKind, UtilConstants.C122_ApplyKind.F)){ //F:房貸從債務人
					cankAssignCase = false;//如果功能啟用就先把房貸案件預設改成不能派
					MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
					ElsUser elsUser = userInfoService.getUser(user.getUserId());//注意在LOCAL端測試會撈出NULL
					if( elsUser != null ){
						int signId = elsUser.getSignId();
						// 參考SignEnum ： 首長(10), 單位主管(20), 甲級主管(30), 乙級主管(40), 經辦人員(50)
						if( signId <= 30 ){ //有甲級主管權限(含)以上者才可以派案
							cankAssignCase = true;
						}
					}
				}
			}
		}
		return cankAssignCase;
	}
	
	@Override
	public byte[] getWordContent(C122M01A meta, C122S01C c122s01c) {
		ByteArrayOutputStream baos = null;
		try {
			baos = genC122M01A(meta, c122s01c);
			return baos.toByteArray();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (baos != null) {
				IOUtils.closeQuietly(baos);
			}
		}
		return null;
	}

	private ByteArrayOutputStream genC122M01A(C122M01A meta, C122S01C c122s01c)
			throws CapMessageException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		SimpleDateFormat sdf = new SimpleDateFormat(CapDate.DEFAULT_DATE_FORMAT);
		SimpleDateFormat sdf1 = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);
		// I:100萬以下,J:100萬以上
		StringBuilder templateName = new StringBuilder();
//		String templateName = Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind()) ? 
//				"CLS1220m06_1.xml" : "CLS1220m06_2.xml";
		
//		if(Util.equals(UtilConstants.C122_ApplyKind.I, meta.getApplyKind())){
//			templateName.append("CLS1220m06_1");
//		}else{
//			templateName.append("CLS1220m06_2");
//		}
		// J-112-0405 申請書改版
		templateName.append(applicationVersion(meta));
		// J-112-0365 如果是高雄市就要改列印有高雄市青年局附件的版本
		boolean isKHH = false;//是否為高雄 (city code = 18)
		if(c122s01c!=null && Util.equals("18", Util.trim(c122s01c.getCity()))
				&& clsService.is_function_on_codetype("J-112-0365_KHH")){
			isKHH = true;
			// J-113-0254 配合113年高雄市青創調整下載檔案(根據進件時間切版本)
//			templateName.append("_KHH");
			templateName.append(applicationVersion_KHH(meta,templateName.toString()));
		}
		templateName.append(".xml");
		
		LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
		OutputStreamWriter outWriter = null;
		Calendar estDate = Calendar.getInstance();
		String estDateYearStr = "";
		String estDateMonthStr = "";
		String estDateDayStr = "";

		if (c122s01c.getN_cdate() != null) {//公司成立日期
			estDate.setTime(c122s01c.getN_cdate());
			estDateYearStr = String.valueOf(estDate.get(Calendar.YEAR));
			estDateMonthStr = String.valueOf(estDate.get(Calendar.MONTH) + 1);
			estDateDayStr = String.valueOf(estDate.get(Calendar.DAY_OF_MONTH));
		}
		
		try {
			CodeTypeFormatter cityFormat = new CodeTypeFormatter(
					codetypeService, "counties",
					CodeTypeFormatter.ShowTypeEnum.Desc);
			
			paramMap.put("P1_caseNo", 
					CapString.trimNull(meta.getPloanCaseNo()));//申請件編號
			
			paramMap.put("P1_custName", meta.getCustName());//中文姓名
			paramMap.put("P2_custName", meta.getCustName());//申貸人姓名
			paramMap.put("P1_custId", meta.getCustId());//身分證字號
			paramMap.put("P2_custId", meta.getCustId());//申貸人身分證
			paramMap.put("P2_relation", "本人");//與申貸人關係
			
			String edu = "";
			if ("1".equals(c122s01c.getElevel())) {
				edu = "國中/小";
			} else if ("2".equals(c122s01c.getElevel())) {
				edu = "高中職";
			} else if ("3".equals(c122s01c.getElevel())) {
				edu = "專科(含)以下";
			} else if ("4".equals(c122s01c.getElevel())) {
				edu = "大學";
			} else if ("5".equals(c122s01c.getElevel())) {
				edu = "碩士";
			} else if ("6".equals(c122s01c.getElevel())) {
				edu = "博士";
			}
			paramMap.put("P1_edu", edu);//最高學歷
			
			String marital = "";//婚姻狀態
			if ("1".equals(CapString.trimNull(c122s01c.getMarital()))){
				marital = "未婚";
			} else if ("2".equals(c122s01c.getMarital())){
				marital = "已婚";
			} else if ("3".equals(c122s01c.getMarital())){
				marital = "已婚(有子女)";
			} else if ("4".equals(c122s01c.getMarital())){
				marital = "離婚";
			} else if ("5".equals(c122s01c.getMarital())){
				marital = "歿";
			}
			paramMap.put("P1_marital", marital);//婚姻狀態
			
			String house = "";//現住房屋持有狀態
			if ("1".equals(CapString.trimNull(c122s01c.getHouse()))){
				house = "自有，無貸款";
			} else if ("2".equals(c122s01c.getHouse())){
				house = "自有，有貸款";
			} else if ("3".equals(c122s01c.getHouse())){
				house = "配偶所有";
			} else if ("4".equals(c122s01c.getHouse())){
				house = "父母親所有";
			} else if ("5".equals(c122s01c.getHouse())){
				house = "宿舍";
			} else if ("6".equals(c122s01c.getHouse())){
				house = "租賃";
			}
			paramMap.put("P1_house", house);//現住房屋持有狀態
			
			paramMap.put("P1_tel", c122s01c.getTel());//手機號碼
			
			paramMap.put("P1_email", 
					CapString.trimNull(c122s01c.getEmail()));//郵件信箱
			
			paramMap.put("P1_Sname", 
					CapString.trimNull(c122s01c.getCname()));//服務單位名稱
			
			paramMap.put("P1_Enjob", CapString.trimNull(c122s01c.getEnjob())); //職稱
			
			paramMap.put("P1_ncnumber", CapString.trimNull(c122s01c.getN_cnumber()));//服務單位統編 n_cnumber
			
			paramMap.put("P1_Eninput", CapString.trimNull(c122s01c.getEninput())); //現職年薪
			
			paramMap.put("P1_ntel", CapString.trimNull(c122s01c.getN_tel()));//服務單位電話 (一百萬以下才會有值)
			
			paramMap.put("P1_Enyear", CapString.trimNull(c122s01c.getEnyear())); //年資
			
			paramMap.put("P1_branchName", 
					branchService.getBranchName(meta.getOwnBrId()));//聯絡分行
			
			paramMap.put("P1_taxRevenue", CapString.isEmpty(c122s01c.getInput2()) ?
					"": formattedAmount(new BigDecimal(CapString.trimNull(c122s01c.getInput2()))));//創業事業報稅營收
			
			paramMap.put("P1_taxYear", CapString.trimNull(c122s01c.getInput3()));//創業事業報稅年份
			
			paramMap.put("P1_contribution", CapString.isEmpty(c122s01c.getInput4()) ?
					"": formattedAmount(new BigDecimal(CapString.trimNull(c122s01c.getInput4()))));//創業事業登記出資額
			
			//-------------------------------------------------
			
			String busAddr = "";
			for (int i = 1; i <= 5; i++) {
				if (String.valueOf(i).equals(c122s01c.getCtype())) {
					if ("1".equals(c122s01c.getApply_type())) {
						paramMap.put("P9_orgType" + i, "■");
					}
				} else {
					if ("1".equals(c122s01c.getApply_type())) {
						paramMap.put("P9_orgType" + i, "□");
					}

				}
			}
			String useOfLoan = "";
			//String useOfLoanOther="";
			StringBuilder useOfLoanOtherStr=new StringBuilder();
			String applyAmt = "";
			String applyYear="";
			if(!CapString.isEmpty(c122s01c.getA3())
					 && c122s01c.getA3().indexOf("1") > -1){
				useOfLoan="準備金及開辦費用";
				applyAmt=formattedAmount(c122s01c.getA3_1());
				applyYear=c122s01c.getA3_2() == null ? "" 
						: String.valueOf(c122s01c.getA3_2().intValue());
			}
			if(!CapString.isEmpty(c122s01c.getA3())
					 && c122s01c.getA3().indexOf("2") > -1){
				useOfLoan="週轉性支出";
				if(Util.equals(UtilConstants.C122_ApplyKind.I,meta.getApplyKind()) &&
						!CapString.isEmpty(c122s01c.getA3_7())){//100萬以下才有
					String a3_7=CapString.trimNull(c122s01c.getA3_7());
					String[] arr=new String[]{"水電燃料費","薪資費用","營業租金","購料費用"};
					for(int i=1; i <= 5 ;i++){
						if(a3_7.indexOf(String.valueOf(i)) > -1){
							if(!CapString.isEmpty(useOfLoanOtherStr.toString())){
								if( i<5 || (i== 5 &&!CapString.isEmpty(c122s01c.getA3_8()))){
									useOfLoanOtherStr.append(",");
									if(i!=5){
										useOfLoanOtherStr.append(arr[i-1]);
									}
									if(i== 5 &&!CapString.isEmpty(c122s01c.getA3_8())){
										useOfLoanOtherStr.append(CapString.trimNull(c122s01c.getA3_8()));
									}
								}
							}else{
								if(i<5){
									useOfLoanOtherStr.append(arr[i-1]);
								}else{
									useOfLoanOtherStr.append(CapString.trimNull(c122s01c.getA3_8()));
								}
							}
						}
					}
				}
				applyAmt=formattedAmount(c122s01c.getA3_4());
				applyYear=c122s01c.getA3_5() == null ? "" 
						: String.valueOf(c122s01c.getA3_5().intValue());
			}
			if(!CapString.isEmpty(c122s01c.getA3())
					 && c122s01c.getA3().indexOf("3") > -1){
				useOfLoan="資本性支出";
				if(Util.equals(UtilConstants.C122_ApplyKind.I,meta.getApplyKind())){
					//100萬以下才有-所購置機器設備
					useOfLoanOtherStr= new StringBuilder("所購置機器設備").append(CapString.trimNull(c122s01c.getA3_12()));
				}
				applyAmt=formattedAmount(c122s01c.getA3_9());
				applyYear=c122s01c.getA3_10() == null ? "" 
						: String.valueOf(c122s01c.getA3_10().intValue());
			}
			
			paramMap.put("P1_ApplyAmt",applyAmt);//貸款額度
			paramMap.put("P1_ApplyYear",applyYear);//貸款期限
			paramMap.put("P1_UseOfLoan",useOfLoan);//貸款用途
			paramMap.put("P1_UseOfLoanOther",useOfLoanOtherStr.toString());//其他用途說明
			
			//身份驗證
			String agreeQueryEJ="";
			String agreeQueryTime="";
			if( Util.equals("4", c122s01c.getOTP_STATUS()) && Util.equals("4001", c122s01c.getOTP_ERRORCODE())
					&& Util.equals("00", c122s01c.getOTP_DATARESULT()) && Util.equals("00", c122s01c.getOTP_ACCTRESULT())
					&& Util.equals("01", c122s01c.getOTP_TYPERESULT()) ){
				agreeQueryEJ= "他行認證";
				if(Util.isNotEmpty(c122s01c.getOTP_Time())){
					Calendar applyDate = Calendar.getInstance();
					applyDate.setTime(c122s01c.getOTP_Time());
					agreeQueryTime=sdf1.format(applyDate.getTime());
				}else{
					agreeQueryTime="";
				}
			}
			paramMap.put("P1_agreeQueryEJ",agreeQueryEJ);//身份認證方式
			
			//申請日期=OTP驗證成功日期+時間
			paramMap.put("P1_ApplyTS", agreeQueryTime );//申請日期
			
			//營業地址
			String city = cityFormat.reformat(CapString.trimNull(c122s01c.getCity()));
			String cityArea = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCity()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityarea()));
			if(Util.equals(city, cityArea)){//[縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
				cityArea="";
			}
			busAddr = city + cityArea
					+ CapString.trimNull(c122s01c.getN_address());
			StringBuilder addr1 = new StringBuilder();
			//戶籍地址
			String citya = cityFormat.reformat(CapString.trimNull(c122s01c.getCitya()));
			String cityAreaa = new CodeTypeFormatter(codetypeService,
					"counties" + CapString.trimNull(c122s01c.getCitya()),
					CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
					.getCityareaa()));
			if(Util.equals(citya, cityAreaa)){//[縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
				cityAreaa="";
			}
			addr1.append(citya).append(cityAreaa)
			.append(CapString.trimNull(c122s01c.getAddress()));
			
			Calendar birthDate = Calendar.getInstance();
			String birthDayStr = "";
			String year = "";
			String month = "";
			String day = "";
			if(c122s01c.getBday() != null){
				birthDate.setTime(c122s01c.getBday());
				birthDayStr = sdf.format(birthDate.getTime());
				year = String.valueOf(birthDate.get(Calendar.YEAR) - 1911);
				month = String.valueOf(birthDate.get(Calendar.MONTH) + 1);
				day = String.valueOf(birthDate.get(Calendar.DAY_OF_MONTH));
			}

			//生日
			paramMap.put("P1_birth", birthDayStr);
			paramMap.put("P9_birthY", year);
			paramMap.put("P9_birthM", month);
			paramMap.put("P9_birthD", day);

			// 青年創業及啟動金貸款切結書
			paramMap.put("P8_custName", CapString.trimNull(c122s01c.getCname()));// 事業體名稱(cname)
			paramMap.put("P8_custId", CapString.trimNull(c122s01c.getN_cnumber()));// 事業體統編(n_cnumber)
			paramMap.put("P1_busAddr", CapString.trimNull(busAddr));//服務單位地址
			paramMap.put("P8_addr", CapString.trimNull(busAddr));//營業地址
			paramMap.put("P8_chairmanId", CapString.trimNull(meta.getCustId())); // 申請人身分證字號
			paramMap.put("P8_chairmanName", CapString.trimNull(meta.getCustName())); // 申請人姓名

			// 青年創業及啟動金貸款創業貸款申請表
			paramMap.put("P9_aType1", "1".equals("2") ? "■" : "□"); // 申請人類別-事業體
			paramMap.put("P9_aType2", "2".equals("2") ? "■" : "□"); // 申請人類別-事業體之負責人
			paramMap.put("P9_chairmanName", CapString.trimNull(meta.getCustName())); // 負責人姓名-帶本案申請人姓名
			paramMap.put("P9_custName", CapString.trimNull(c122s01c.getCname()));// 事業體名稱(cname)

			paramMap.put("P9_idnumber", CapString.trimNull(meta.getCustId())); // 申請人身分證字號
			paramMap.put("P9_tel", CapString.trimNull(c122s01c.getTel()));
			paramMap.put("P9_email", CapString.trimNull(c122s01c.getEmail()));
			paramMap.put("P1_addr1", CapString.trimNull(addr1.toString()));//P1 戶籍地址
			
			paramMap.put("P9_addr1", CapString.trimNull(addr1.toString()));
			paramMap.put("P9_cform", Util.equals("Y", c122s01c.getCform()) ? "■" : "□");
			
			if(!Util.equals("Y", c122s01c.getCform())){//通訊地址與戶籍地址不同
				StringBuilder addr2 = new StringBuilder();//通訊地址
				String cityb = cityFormat.reformat(CapString.trimNull(c122s01c.getCityb()));
				String cityAreab = new CodeTypeFormatter(codetypeService,
						"counties" + CapString.trimNull(c122s01c.getCityb()),
						CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
						.getCityareab()));
				if(Util.equals(cityb, cityAreab)){//[縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
					cityAreab="";
				}
				addr2.append(cityb).append(cityAreab).append(CapString.trimNull(c122s01c.getAddress2()));
				paramMap.put("P1_addr2", CapString.trimNull(addr2.toString()));//P1 通訊地址
				paramMap.put("P9_addr2", CapString.trimNull(addr2.toString()));
				if(isKHH){//J-112-0365 高雄市青創
					paramMap.put("P12_address2", CapString.trimNull(addr2.toString()));//P12 住址(高雄市青年局切結書)
					paramMap.put("P14_address2", CapString.trimNull(addr2.toString()));//P14 住址 (高雄市青年局營運狀況調查表)
				}
			}else{
				paramMap.put("P1_addr2", CapString.trimNull(addr1.toString()));//P1 通訊地址
				if(isKHH && clsService.is_function_on_codetype("CLS_KHH_addr2")){// 高雄市青創
					paramMap.put("P12_address2", CapString.trimNull(addr1.toString()));//P12 住址(高雄市青年局切結書)
					paramMap.put("P14_address2", CapString.trimNull(addr1.toString()));//P14 住址 (高雄市青年局營運狀況調查表)
				}
			}

			for (int i = 1; i <= 6; i++) {
				// 教育程度
				paramMap.put("P9_elevel" + i,
						String.valueOf(i).equals(c122s01c.getElevel()) ? "■"
								: "□");
			}

			for (int i = 1; i <= 4; i++) {
				// 經歷
				paramMap.put("P9_sname" + i,
						CapString.trimNull(c122s01c.get("sname" + i)));
				paramMap.put("P9_sjob" + i,
						CapString.trimNull(c122s01c.get("sjob" + i)));
				paramMap.put("P9_syear" + i,
						CapString.trimNull(c122s01c.get("syear" + i)));
			}

			for (int i = 1; i <= 6; i++) {
				// 輔導課程
				String index = String.valueOf(i == 1 ? "" : i);
				paramMap.put(
						"P9_class" + index + "_ctxt",
						CapString.trimNull(c122s01c.get("class" + index
								+ "_ctxt")));
				paramMap.put(
						"P9_class" + index + "_unit",
						CapString.trimNull(c122s01c.get("class" + index
								+ "_unit")));
				paramMap.put(
						"P9_class" + index + "_hour",
						CapString.trimNull(c122s01c.get("class" + index
								+ "_hour")));
			}
			if (Util.equals(UtilConstants.C122_ApplyKind.I,meta.getApplyKind())) {
				// 100萬以下
				//paramMap.put("P9_a1_A01", !CapString.isEmpty(c122s01c.getA1())
				//		&& c122s01c.getA1().indexOf("1") > -1 ? "■" : "□");
				paramMap.put(
						"P9_bank1",
						CapString.isEmpty(c122s01c.getBank1()) ? "" : CapString
								.trimNull(c122s01c.getBank1())
								+ CapString.trimNull(c122s01c.getBank2()));
				paramMap.put("P9_A1_1Y", "Y".equals(c122s01c.getA1_1_()) ? "■"
						: "□");
				paramMap.put("P9_A1_1N", "N".equals(c122s01c.getA1_1_()) ? "■"
						: "□");
				//paramMap.put("P9_a1_A02", !CapString.isEmpty(c122s01c.getA1())
				//		&& c122s01c.getA1().indexOf("2") > -1 ? "■" : "□");
				paramMap.put("P9_A1_ctxt2",
						CapString.trimNull(c122s01c.getA1_ctxt2()));

				//paramMap.put("P9_a2_A01", !CapString.isEmpty(c122s01c.getA2())
				//		&& c122s01c.getA2().indexOf("1") > -1 ? "■" : "□");
				//paramMap.put("P9_a2_A02", !CapString.isEmpty(c122s01c.getA2())
				//		&& c122s01c.getA2().indexOf("2") > -1 ? "■" : "□");
				//paramMap.put("P9_a2_A03", !CapString.isEmpty(c122s01c.getA2())
				//		&& c122s01c.getA2().indexOf("3") > -1 ? "■" : "□");

				paramMap.put("P9_a2_1", CapString.trimNull(c122s01c.getA2_1()));
				paramMap.put("P9_a2_2", CapString.trimNull(c122s01c.getA2_2()));
				paramMap.put("P9_a2_3", CapString.trimNull(c122s01c.getA2_3()));

				// 準備金及開辦費
				paramMap.put("P9_a31", !CapString.isEmpty(c122s01c.getA3())
						&& c122s01c.getA3().indexOf("1") > -1 ? "■" : "□");
				paramMap.put("P9_a3_1", formattedAmount(c122s01c.getA3_1()));
				paramMap.put("P9_a3_2", formattedAmount(c122s01c.getA3_2()));
				paramMap.put("P9_a3_3", formattedAmount(c122s01c.getA3_3()));

				// 週轉性支出
				paramMap.put("P9_a32", !CapString.isEmpty(c122s01c.getA3())
						&& c122s01c.getA3().indexOf("2") > -1 ? "■" : "□");

				paramMap.put("P9_a3_4", formattedAmount(c122s01c.getA3_4()));
				paramMap.put("P9_a3_5", formattedAmount(c122s01c.getA3_5()));
				paramMap.put("P9_a3_6", formattedAmount(c122s01c.getA3_6()));

				for (int i = 1; i <= 5; i++) {
					// 用途
					paramMap.put("P9_a3_7" + i, !CapString.isEmpty(c122s01c.getA3_7())
							&& c122s01c.getA3_7().indexOf(String.valueOf(i)) > -1 ? "■" : "□");
				}
				paramMap.put("P9_a3_8", CapString.trimNull(c122s01c.getA3_8()));

				// 資本性支出
				paramMap.put("P9_a33", !CapString.isEmpty(c122s01c.getA3())
						&& c122s01c.getA3().indexOf("3") > -1 ? "■" : "□");
				paramMap.put("P9_a3_9", formattedAmount(c122s01c.getA3_9()));
				paramMap.put("P9_a3_10", formattedAmount(c122s01c.getA3_10()));
				paramMap.put("P9_a3_11", formattedAmount(c122s01c.getA3_11()));
				paramMap.put("P9_a3_12",
						CapString.trimNull(c122s01c.getA3_12()));

				// 新創或所營事業資料
				paramMap.put("P10_custId", CapString.trimNull(c122s01c.getN_cnumber()));
				paramMap.put("P10_year", estDateYearStr);
				paramMap.put("P10_month", estDateMonthStr);
				paramMap.put("P10_day", estDateDayStr);
				
				// 營業地址
				paramMap.put("P10_addr1", busAddr);
				paramMap.put("P10_n_tel", CapString.trimNull(c122s01c.getN_tel()));
				String city2 = cityFormat.reformat(CapString.trimNull(c122s01c.getCity2()));//工廠地址
				String cityArea2 = new CodeTypeFormatter(codetypeService,
						"counties" + CapString.trimNull(c122s01c.getCity2()),
						CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
						.getCityarea2()));
				if(Util.equals(city2, cityArea2)){//[縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
					cityArea2="";
				}
				String busAddr2 = city2	+ cityArea2 + CapString.trimNull(c122s01c.getN_address2());
				paramMap.put("P10_addr2", busAddr2);
				paramMap.put("P10_n_phone", CapString.trimNull(c122s01c.getN_phone()));
				if(isKHH){//J-112-0365 高雄市青創
					//工廠地址只有一百萬以下有
					paramMap.put("P14_n_address2", busAddr2);//高雄青創營運狀況調查表-工廠地址
				}
				String city3 = cityFormat.reformat(CapString.trimNull(c122s01c.getCity3()));//事業地址-其他
				String cityArea3 = new CodeTypeFormatter(codetypeService,
						"counties" + CapString.trimNull(c122s01c.getCity3()),
						CodeTypeFormatter.ShowTypeEnum.Desc).reformat(CapString.trimNull(c122s01c
						.getCityarea3()));
				if(Util.equals(city3, cityArea3)){//[縣/市]跟[區/市/鄉/鎮]一樣的時候就不顯示[區/市/鄉/鎮]
					cityArea3="";
				}
				String busAddr3 = city3	+ cityArea3 + CapString.trimNull(c122s01c.getN_addressctxt());
				paramMap.put("P10_addr3", busAddr3);
				paramMap.put("P10_n_fax", CapString.trimNull(c122s01c.getN_fax()));
				paramMap.put("P10_n_ctxt", CapString.trimNull(c122s01c.getN_ctxt()));

				for (int i = 1; i <= 14; i++) {
					String qName = "q" + i;
					String a = CapString.trimNull(c122s01c.get(qName));
					StringBuilder sb = new StringBuilder();
					for (int j = 1; j <= 5; j++) {
						int k = j + j - 1;
						sb.append(String.valueOf(j).equals(a) ? "■" : "□")
								.append(k).append(" 分");
					}
					paramMap.put("P10_" + qName, sb.toString());
				}
				String q15 = CapString.trimNull(c122s01c.get("q15"));
				for (int i = 1; i <= 6; i++) {
					paramMap.put("P10_q15" + i,
							String.valueOf(i).equals(q15) ? "■" : "□");
				}
				for (int i = 16; i <= 20; i++) {
					String a = CapString.trimNull(c122s01c.get("q" + i));
					for (int j = 1; j <= 5; j++) {
						String k = String.valueOf(j);
						paramMap.put("P10_q" + i + k, k.equals(a) ? "■" : "□");
					}
				}
			} else {
				// 100萬以上
				// 四、申請背景
				paramMap.put("P9_a0", !CapString.isEmpty(c122s01c.getA0())
						&& "Y".equals(c122s01c.getA0()) ? "■" : "□");
				paramMap.put("P9_a1", !CapString.isEmpty(c122s01c.getA1())
						&& "Y".equals(c122s01c.getA1()) ? "■" : "□");
				paramMap.put("P9_a1_1", formattedAmount(c122s01c.getA1_1()));
				paramMap.put("P9_a1_1_", c122s01c.getA1_1() != null ? "■" : "□");
				paramMap.put("P9_a1_2", formattedAmount(c122s01c.getA1_2()));
				paramMap.put("P9_a1_2_", c122s01c.getA1_2() != null ? "■" : "□");
				paramMap.put("P9_a1_3", formattedAmount(c122s01c.getA1_3()));
				paramMap.put("P9_a1_3_", c122s01c.getA1_3() != null ? "■" : "□");
				paramMap.put("P9_a1_5", CapString.trimNull(c122s01c.getA1_5()) );

				paramMap.put(
						"P9_bank1",
						CapString.isEmpty(c122s01c.getBank1()) ? "" : CapString
								.trimNull(c122s01c.getBank1())
								+ CapString.trimNull(c122s01c.getBank2()));
				paramMap.put("P9_a2", !CapString.isEmpty(c122s01c.getA2())
						&& "Y".equals(c122s01c.getA2()) ? "■" : "□");

				// 五、本次申請資訊
				String a31 = !CapString.isEmpty(c122s01c.getA3()) 
						&& c122s01c.getA3().indexOf("1") > -1 ? "■" : "□";
				paramMap.put("P9_a31", a31);
				paramMap.put("P9_a31_", a31);
				paramMap.put("P9_a31__", a31);
				String a32 = !CapString.isEmpty(c122s01c.getA3()) 
						&& c122s01c.getA3().indexOf("2") > -1 ? "■" : "□";
				paramMap.put("P9_a32", a32);
				paramMap.put("P9_a32_", a32);
				paramMap.put("P9_a32__", a32);
				String a33 = !CapString.isEmpty(c122s01c.getA3()) 
						&& c122s01c.getA3().indexOf("3") > -1 ? "■" : "□";
				paramMap.put("P9_a33", a33);
				paramMap.put("P9_a33_", a33);
				paramMap.put("P9_a33__", a33);
				paramMap.put("P9_a3_1", formattedAmount(c122s01c.getA3_1()));
				paramMap.put("P9_a3_2", c122s01c.getA3_2() == null ? "" 
						: String.valueOf(c122s01c.getA3_2().intValue()));
				paramMap.put("P9_a3_3", c122s01c.getA3_3() == null ? "" 
						: String.valueOf(c122s01c.getA3_3().intValue()));

				paramMap.put("P9_a3_4", formattedAmount(c122s01c.getA3_4()));
				paramMap.put("P9_a3_5", c122s01c.getA3_5() == null ? "" 
						: String.valueOf(c122s01c.getA3_5().intValue()));
				paramMap.put("P9_a3_6", c122s01c.getA3_6() == null ? "" 
						: String.valueOf(c122s01c.getA3_6().intValue()));

				paramMap.put("P9_a3_9", formattedAmount(c122s01c.getA3_9()));
				paramMap.put("P9_a3_10", c122s01c.getA3_10() == null ? "" 
						: String.valueOf(c122s01c.getA3_10().intValue()));
				paramMap.put("P9_a3_11", c122s01c.getA3_11() == null ? "" 
						: String.valueOf(c122s01c.getA3_11().intValue()));

				// 三、新創或所營事業資料
				paramMap.put("P10_c1", c122s01c.getC1());
				paramMap.put("P10_c2", c122s01c.getC2());
				paramMap.put("P10_c3", formattedAmount(c122s01c.getC3()));

				for (int i = 1; i <= 20; i++) {
					String key = "d1_" + i;
					String val = CapString.trimNull(c122s01c.get(key));
					if (i == 1 || i == 5 || i == 9 || i == 13 || i == 17) {
						paramMap.put("P10_" + key, val);
					} else {
						paramMap.put("P10_" + key, CapString.isEmpty(val) ? ""
								: formattedAmount(new BigDecimal(val)));
					}
				}

				paramMap.put("P10_d1_subtotal",
						formattedAmount(c122s01c.getD1_subtotal()));
				for (int i = 1; i <= 20; i++) {
					String key = "d2_" + i;
					String val = CapString.trimNull(c122s01c.get(key));
					if (i == 13 || i == 17) {
						paramMap.put("P10_" + key, val);
					} else {
						paramMap.put("P10_" + key, CapString.isEmpty(val) ? ""
								: formattedAmount(new BigDecimal(val)));
					}
				}
				paramMap.put("P10_d2_subtotal",
						formattedAmount(c122s01c.getD2_subtotal()));
				paramMap.put("P10_total", formattedAmount(c122s01c.getTotal()));

				// 五、事業或創業經營計畫
				paramMap.put("P10_e1_1", CapString.trimNull(c122s01c.getE1_1()));
				paramMap.put("P10_e1_2", CapString.trimNull(c122s01c.getE1_2()));
				paramMap.put("P10_e1_3", CapString.trimNull(c122s01c.getE1_3()));
				paramMap.put("P10_e1_4", CapString.trimNull(c122s01c.getE1_4()));

				paramMap.put("P10_e2_1", CapString.trimNull(c122s01c.getE2_1()));
				paramMap.put("P10_e2_2", CapString.trimNull(c122s01c.getE2_2()));
				paramMap.put("P10_e2_3", CapString.trimNull(c122s01c.getE2_3()));
				paramMap.put("P10_e2_4", CapString.trimNull(c122s01c.getE2_4()));
				paramMap.put("P10_e2_5", CapString.trimNull(c122s01c.getE2_5()));
				paramMap.put("P10_e2_6", CapString.trimNull(c122s01c.getE2_6()));

				paramMap.put("P10_e3_1", formattedAmount(c122s01c.getE3_1()));
				paramMap.put("P10_e3_2", formattedAmount(c122s01c.getE3_2()));
				paramMap.put("P10_e3_3", formattedAmount(c122s01c.getE3_3()));
				paramMap.put("P10_e3_4", formattedAmount(c122s01c.getE3_4()));
				paramMap.put("P10_e3_4_", formattedAmount(c122s01c.getE3_4()));
				paramMap.put("P10_e3_5", formattedAmount(c122s01c.getE3_5()));
				paramMap.put("P10_e3_6", formattedAmount(c122s01c.getE3_6()));
				paramMap.put("P10_e3_6_", formattedAmount(c122s01c.getE3_6()));
				paramMap.put("P10_e3_7", formattedAmount(c122s01c.getE3_7()));
				paramMap.put("P10_e3_8", formattedAmount(c122s01c.getE3_8()));
				paramMap.put("P10_e3_9", formattedAmount(c122s01c.getE3_9()));
				paramMap.put("P10_e3_10",
						CapString.trimNull(c122s01c.getE3_10()));
				paramMap.put("P10_e3_11",
						CapString.trimNull(c122s01c.getE3_11()));
				if(isKHH){//J-112-0365 高雄市青創
					paramMap.put("P14_n_address2", "");//高雄青創營運狀況調查表-工廠地址(一百萬以上沒有這個填寫項目)
				}	
			}
			//J-112-0365 高雄青創的套表欄位(欄位名稱雖然是P12、P14，但是對上實體檔案頁數會有誤差)
			if(isKHH){
				paramMap.put("P12_custName", CapString.trimNull(c122s01c.getCname()));// 事業體名稱(cname)
				paramMap.put("P12_custId", CapString.trimNull(c122s01c.getN_cnumber()));// 事業體統編(n_cnumber)
				paramMap.put("P12_chairmanId", CapString.trimNull(meta.getCustId())); // 申請人身分證字號
				//P12_address2 高雄市青年局切結書住址欄位(前面有壓了)
				paramMap.put("P14_n_address", CapString.trimNull(busAddr));//營業地址
				//P14_n_address2 工廠地址(工廠地址一百萬以上跟一百萬以下分開給值)
				paramMap.put("P14_n_tel", CapString.trimNull(c122s01c.getN_tel()));//連絡電話-辦公室
				paramMap.put("P14_tel", CapString.trimNull(c122s01c.getTel()));//連絡電話-手機
				paramMap.put("P14_custName", CapString.trimNull(c122s01c.getCname()));// 事業體名稱(cname)
				paramMap.put("P14_custId", CapString.trimNull(c122s01c.getN_cnumber()));// 事業體統編(n_cnumber)
				paramMap.put("P14_chairmanId", CapString.trimNull(meta.getCustId())); // 申請人身分證字號
				//P14_address2 高雄市青年局營運狀況調查表住址欄位(前面有壓了)
			}

			Map<String, String> convert_paramMap = convert_paramValue_for_XML_Predefined_entities(paramMap);
			String templateStr = Util.getFileContent(PropUtil
					.getProperty("loadFile.dir")
					+ "word"
					+ File.separator
					+ templateName);
			String outputStr = join_word_template_param(meta.getCustId()
					+ ",mainId:" + meta.getMainId(), templateStr,
					convert_paramMap);
			outWriter = new OutputStreamWriter(baos, "UTF-8");
			outWriter.write(outputStr);
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
			throw new CapMessageException(e.getMessage(), getClass());
		} finally {
			try {
				if (baos != null) {
					IOUtils.closeQuietly(baos);
				}
				if (outWriter != null) {
					IOUtils.closeQuietly(outWriter);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return baos;
	}
	
	//取得版本
	private String applicationVersion(C122M01A c122m01a){
		
		String chkDateStr = Util.trim(lmsService
				.getSysParamDataValue("CLS_v11208_BGNDATE"));
		String chkDateStr_OC0004 = Util.trim(lmsService
				.getSysParamDataValue("CLS_OC0004_BGNDATE"));
		// 線上申請書 OC0003(啟用日期比照112.08 版啟用日期)
		Date ver_OC0003Date = CapDate.getDate(
				(Util.isEmpty(chkDateStr) ? "2023-09-14" : chkDateStr),"yyyy-MM-dd");
		// J-112-0551 線上申請書 OC0004啟用日期
		Date ver_OC0004Date = CapDate.getDate(
				(Util.isEmpty(chkDateStr_OC0004) ? "2023-12-12" : chkDateStr_OC0004),"yyyy-MM-dd");
		
		String use_ver = "CLS1220m06_1";//預設青創一百萬以下版本
		
		//申請日期
		Date applyDate = (c122m01a.getApplyTS() == null ? Util
				.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")) : c122m01a.getApplyTS());
		if (applyDate.compareTo(ver_OC0004Date) >= 0) {
			if(Util.equals(UtilConstants.C122_ApplyKind.I, c122m01a.getApplyKind())){
				//一百萬以下
				use_ver = "CLS1220m06_1_OC0004";
			}else{
				use_ver = "CLS1220m06_2_OC0004";
			}
		} else if (applyDate.compareTo(ver_OC0003Date) >= 0) {
			if(Util.equals(UtilConstants.C122_ApplyKind.I, c122m01a.getApplyKind())){
				//一百萬以下
				use_ver = "CLS1220m06_1_OC0003";
			}else{
				use_ver = "CLS1220m06_2_OC0003";
			}
		} else {
			if(Util.equals(UtilConstants.C122_ApplyKind.I, c122m01a.getApplyKind())){
				//一百萬以下
				use_ver = "CLS1220m06_1";
			}else{
				use_ver = "CLS1220m06_2";
			}
		}
		return use_ver;
	}
	
	// 取得高雄市青創版本
	private String applicationVersion_KHH(C122M01A c122m01a, String templateName){
		// J-113-0254 配合113年高雄市青創調整下載檔案
		String chkDateStr_v113KHH = Util.trim(lmsService
				.getSysParamDataValue("CLS_v113KHH_BGNDATE"));//高雄市青創113年啟用日期

		// 高雄市青創113年版本預計是2024/07/01啟用
		Date v113KHH_Date = CapDate.getDate(
				(Util.isEmpty(chkDateStr_v113KHH) ? "2024-07-01" : chkDateStr_v113KHH),"yyyy-MM-dd");

		StringBuilder khh_stb = new StringBuilder("_KHH");
		
		// 申請日期
		Date applyDate = (c122m01a.getApplyTS() == null ? Util
				.parseDate(CapDate.getCurrentDate("yyyy-MM-dd")) : c122m01a.getApplyTS());
		
		// 113年的高雄青創申請書
		if (applyDate.compareTo(v113KHH_Date) >= 0 ) {
			//理論上不會出現OC0004之前的，但還是加一個判斷
			if(Util.equals(templateName, "CLS1220m06_1_OC0004") || 
					Util.equals(templateName, "CLS1220m06_2_OC0004")){
				khh_stb.append("_v113");
			}
		} else {
			
		}
		return khh_stb.toString();
	}

	private String formattedAmount(BigDecimal num) {
		if (num == null) {
			return "";
		} else {
			num = num.setScale(0, RoundingMode.HALF_UP);
			if (num.compareTo(new BigDecimal(999)) > 0) {
				return CapCommonUtil.formatAmount(num.toPlainString(), 3);
			} else {
				return num.toPlainString();
			}
		}
	}

	/**
	 * <ul>
	 * <li>第1段 beforeStr</li>
	 * <li>第2段 ＜w:bookmarkStart w:id="0" w:name="zxcv"/＞＜w:r
	 * w:rsidRPr="asdf"＞＜w:rPr＞...＜/w:rPr＞＜w:t＞ qwert ＜/w:t＞
	 * ＜/w:r＞＜w:bookmarkEnd w:id="0"/＞</li>
	 * <li>第3段 afterStr</li>
	 * </ul>
	 */
	private String[] split_ByBookMark(String bookMarkName, String srcStr) {
		String[] arr = null;
		String strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\""
				+ bookMarkName + "\"[^>]*>.*<w:bookmarkEnd\\b" + "[^>]*>";

		/*
		 * 參考 https://stackoverflow.com/questions/53646033
		 * 
		 * 若文字為 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/><w:bookmarkStart w:id="1" w:name="aa2"/><w:bookmarkEnd
		 * w:id="1"/> 在加上 (?s) 之後, 若不加上 (?:(?!<w:bookmarkStart\b).)*? 在 parse 時
		 * ● 不會抓到 <w:bookmarkStart w:id="0" w:name="aa1"/><w:bookmarkEnd
		 * w:id="0"/> ● 而會(夾雜另一個tag)抓到 <w:bookmarkStart w:id="0"
		 * w:name="aa1"/><w:bookmarkEnd w:id="0"/><w:bookmarkStart w:id="1"
		 * w:name="aa2"/><w:bookmarkEnd w:id="1"/>
		 */
		strPattern = "(?s)<w:bookmarkStart\\b[^>]*w:name=\"" + bookMarkName
				+ "\"[^>]*>(?:(?!<w:bookmarkStart\b).)"
				+ "*?<w:bookmarkEnd\\b[^>]*>";
		// if(srcStr.indexOf(bookMarkName)>0){
		arr = split_into_pre_match_aft_byFirstFind(srcStr,
				Pattern.compile(strPattern));
		// }else{
		// arr = new String[]{srcStr};
		// }

		if (arr != null && arr.length == 3) {
			// ok
		} else {
			LOGGER.error("split_ByBookMark[" + bookMarkName + "]【" + strPattern
					+ "】【" + srcStr + "】");
		}
		return arr;
	}

	private String[] split_ByRunText(String srcStr, Pattern pattern_run) {
		List<String> list = new ArrayList<String>();
		Matcher matcher = pattern_run.matcher(srcStr);
		int idx_prev_beg = -1;
		int idx_prev_end = -1;
		while (matcher.find()) { // 若一個 bookmark 包含了N個Run
			int idx_beg = matcher.start();
			int idx_end = matcher.end();
			String part = matcher.group();

			if (idx_prev_beg == -1) {
				list.add(srcStr.substring(0, idx_beg)); // 應抓到 <w:bookmarkStart
				// w:id="0"
				// w:name="zxcv"/>
			}
			// ~~~
			idx_prev_beg = idx_beg;
			idx_prev_end = idx_end;
			// ~~~
			list.add(part);
		}

		if (idx_prev_end != -1) {
			list.add(srcStr.substring(idx_prev_end)); // 應抓到 <w:bookmarkEnd
			// w:id="0"/>
		}
		// ============================
		if (list.size() >= 3) {
			String parseStr = StringUtils.join(list, "");
			if (parseStr.length() == srcStr.length()) {
				// ok
				return list.toArray(new String[list.size()]);
			} else {
				LOGGER.error("diff_length[" + parseStr.length() + " vs "
						+ srcStr.length() + "][" + parseStr + "][" + srcStr
						+ "]");
			}
		}
		return new String[] { srcStr };
	}

	private String addColorAndSetTextWithUnderLine(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
		// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
		// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = split_tag_and_content(org_runTagAndContentStr,
				pattern_tag_w_t, pattern_tag_w_t_end);
		int org_space_cnt = 0;
		if (text_arr.length == 3) {
			org_space_cnt = text_arr[1].length();
		}

		int cnt_empty_bef = 0;
		int cnt_empty_aft = 0;
		int injectStrValCnt = 0;
		String str_empty_bef = "";
		String str_empty_aft = "";
		if (StringUtils.isAsciiPrintable(injectParamVal)) {
			injectStrValCnt = injectParamVal.length();
		} else {
			int sz = injectParamVal.length();
			for (int i = 0; i < sz; i++) {
				if (CharUtils.isAsciiPrintable(injectParamVal.charAt(i))) {
					++injectStrValCnt;
				} else {
					injectStrValCnt = (injectStrValCnt + 2);
				}
			}
		}
		if (true) {
			if (org_space_cnt > injectStrValCnt) {
				int val = (org_space_cnt - injectStrValCnt) / 2;
				cnt_empty_bef = val;
				cnt_empty_aft = val;
			}

			if (cnt_empty_bef == 0) {
				cnt_empty_bef = 1;
			}
			if (cnt_empty_aft == 0) {
				cnt_empty_aft = 1;
			}

			str_empty_bef = StringUtils.repeat(" ", cnt_empty_bef);
			str_empty_aft = StringUtils.repeat(" ", cnt_empty_aft);
		}
		// ====================================
		// 填入資料分為3段
		// 第1段 黑色底線
		// 第2段 藍色底線，且為injectParamVal
		// 第3段 黑色底線
		String p1 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_bef + text_arr[2]) + runTagEnd;
		String p2 = addColorAndSetText(bookmark_name, arr_runPrAndContent,
				injectParamVal, pattern_tag_w_t, pattern_tag_w_t_end,
				pattern_tag_rPr);
		String p3 = runTagBeg_plus_rPr
				+ (text_arr[0] + str_empty_aft + text_arr[2]) + runTagEnd;

		return p1 + p2 + p3;
	}

	private String _add_blueColor_to_rPr(String runTagBeg_plus_rPr,
			Pattern pattern_tag_rPr) {
		/*
		 * 第1段 <w:r w:rsidRPr="...">
		 */
		/*
		 * 第2段 <w:rPr><w:rFonts w:ascii="標楷體" w:eastAsia="標楷體" w:hAnsi="標楷體"
		 * w:hint="eastAsia"/><w:b/><w:color w:val="FF0000"/><w:sz
		 * w:val="28"/><w:szCs w:val="28"/><w:u w:val="single"/></w:rPr>
		 */
		/*
		 * 第3段
		 */
		String[] arr = split_into_pre_match_aft_byFirstFind(runTagBeg_plus_rPr,
				pattern_tag_rPr);
		if (arr.length == 3) {
			String rPr = arr[1];
			if (rPr.indexOf("<w:color ") >= 0) {
				int idx = rPr.indexOf("<w:color ");
				String endTag = ">";
				int idx_end = rPr.indexOf(endTag, idx);
				if (idx_end > idx) {
					rPr = rPr.substring(0, idx)
							+ rPr.substring(idx_end + endTag.length());
				}
			}

			if (rPr.indexOf("<w:color ") < 0) { // no <w:color
				int idx = rPr.lastIndexOf("</w:rPr");
				if (idx > 0) {
					return arr[0]
							+ (rPr.substring(0, idx)
									+ " <w:color w:val=\"0000FF\"/>" + rPr
									.substring(idx)) + arr[2];
				}
			}
		}
		return runTagBeg_plus_rPr;
	}

	private String addColorAndSetText(String bookmark_name,
			String[] arr_runPrAndContent, String injectParamVal,
			Pattern pattern_tag_w_t, Pattern pattern_tag_w_t_end,
			Pattern pattern_tag_rPr) {
		String runTagBeg_plus_rPr = arr_runPrAndContent[0]; // <w:r
		// w:rsidRPr="asdf"><w:rPr>...</w:rPr>
		String org_runTagAndContentStr = arr_runPrAndContent[1]; // <w:t> qwert
		// </w:t>
		String runTagEnd = arr_runPrAndContent[2]; // </w:r>
		// ~~~
		String[] text_arr = split_tag_and_content(org_runTagAndContentStr,
				pattern_tag_w_t, pattern_tag_w_t_end);
		if (text_arr.length == 3) {
			// 為了能儘快找到填入的字串，增加 <!-- bm -->
			String new_runTagAndContentStr = text_arr[0] + injectParamVal
					+ text_arr[2] + "<!--{" + bookmark_name + "}-->";
			return _add_blueColor_to_rPr(runTagBeg_plus_rPr, pattern_tag_rPr)
					+ (new_runTagAndContentStr) + runTagEnd;
		}

		return StringUtils.join(arr_runPrAndContent, "");
	}

	private String join_word_template_param(String traceStr, String raw_srcStr,
			Map<String, String> passed_paramMap) {
		String srcStr = raw_srcStr;

		Pattern pattern_run = Pattern
				.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*?</w:r\\b[^>]*>");

		/*
		 * 增加【Form Fields】判斷 => 插入的 文字控制項，在print時會出現灰底 <w:fldSimple w:instr="">
		 * </w:fldSimple>
		 * 
		 * <w:fldChar w:fldCharType="begin"> <w:ffData> <w:name
		 * w:val="Text999"/> <w:enabled/> <w:calcOnExit w:val="0"/>
		 * <w:textInput/> </w:ffData> </w:fldChar> <w:r><w:fldChar
		 * w:fldCharType="separate"/></w:r> <w:r><w:fldChar
		 * w:fldCharType="end"/></w:r>
		 */
		// if(clsService.is_function_on_codetype("c340m01a_word_FormFields")){
		// pattern_run =
		// Pattern.compile("(?s)<w:r\\b[^>]*>(?:(?!<w:r\b).)*(?:(?!<w:instrText\b).)*(?:(?!<w:fldChar\b).)
		// *?</w:r\\b[^>]*>");
		// }

		Pattern pattern_runTextTagAndContent = Pattern
				.compile("(?s)<w:t\\b[^>]*>(?:(?!<w:t\b).)*?</w:t\\b[^>]*>");
		Pattern pattern_tag_rPr = Pattern
				.compile("(?s)<w:rPr\\b[^>]*>(?:(?!<w:rPr\b).)*?</w:rPr\\b[^>]*>");
		Pattern pattern_tag_w_t = Pattern.compile("<w:t\\b[^>]*>");
		Pattern pattern_tag_w_t_end = Pattern.compile("</w:t\\b[^>]*>");
		// ===========
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> paramMap = new LinkedHashMap<String, String>();
		for (String k : passed_paramMap.keySet()) {
			String injectParamVal = Util.trim(passed_paramMap.get(k));
			if (Util.isEmpty(injectParamVal)) {
				continue;
			}
			paramMap.put(k, injectParamVal);
		}

		if (Util.equals(user.getSsoUnitNo(), "900")) { // 為了 debug
			LOGGER.info("join_word_template_param>>>" + paramMap.toString());
		}

		for (String k : paramMap.keySet()) {
			String injectParamVal = Util.trim(paramMap.get(k));

			String[] arr_bookMark = split_ByBookMark(k, srcStr);
			if (arr_bookMark.length == 3) {
				String befStr_bookmark = arr_bookMark[0];
				String bookmarkTagAndContentStr = arr_bookMark[1];
				String aftStr_bookmark = arr_bookMark[2];
				// =============
				String[] arr_bookmarkTagAndContent = split_ByRunText(
						bookmarkTagAndContentStr, pattern_run);
				if (arr_bookmarkTagAndContent.length == 3) {
					String bookMarkTagBeg = arr_bookmarkTagAndContent[0];
					String runPrAndContentStr = arr_bookmarkTagAndContent[1];
					String bookMarkTagEnd = arr_bookmarkTagAndContent[2];
					// ~~~~~~
					String[] arr_runPrAndContent = split_into_pre_match_aft_byFirstFind(
							runPrAndContentStr, pattern_runTextTagAndContent);
					if (arr_runPrAndContent.length == 3) {
						String new_runPrAndContent = "";
						String debug_str = "";
						int idx_rPr = arr_runPrAndContent[0].indexOf("<w:rPr");
						int idx_u = arr_runPrAndContent[0].indexOf("<w:u");
						if (idx_rPr > 0 && idx_u > idx_rPr) {
							// 有底線, 例如：帳號_________________
							new_runPrAndContent = addColorAndSetTextWithUnderLine(
									k, arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetTextWithUnderLine";
						} else {
							// 無底線，例如：甲方 ○○○
							new_runPrAndContent = addColorAndSetText(k,
									arr_runPrAndContent, injectParamVal,
									pattern_tag_w_t, pattern_tag_w_t_end,
									pattern_tag_rPr);
							debug_str = "addColorAndSetText";
						}
						String chg_flag = Util.equals(runPrAndContentStr,
								new_runPrAndContent) ? "Eq" : "Diff";
						if (Util.equals(chg_flag, "Eq")) {
							LOGGER.info(traceStr + "[" + k + "][new vs old]["
									+ debug_str + "]=[" + chg_flag + "]");
							LOGGER.info(traceStr + "\t" + runPrAndContentStr);
						} else if (Util.equals(chg_flag, "Diff")) {

						}

						srcStr = befStr_bookmark + bookMarkTagBeg
								+ (new_runPrAndContent) + bookMarkTagEnd
								+ aftStr_bookmark;

					} else {
						LOGGER.error(traceStr + "[" + k
								+ "]arr_runPrAndContent.length="
								+ arr_runPrAndContent.length);
						LOGGER.error(traceStr + runPrAndContentStr);
					}
				} else {
					LOGGER.error(traceStr + "[" + k
							+ "]arr_bookmarkTagAndContent.length="
							+ arr_bookmarkTagAndContent.length);
					LOGGER.error(traceStr + bookmarkTagAndContentStr);
				}
			} else {
				LOGGER.error(traceStr + "[" + k + "]arr_bookMark.length="
						+ arr_bookMark.length);
				LOGGER.error(traceStr + srcStr);
			}
		}
		return srcStr;
	}

	public static LinkedHashMap<String, String> convert_paramValue_for_XML_Predefined_entities(
			Map<String, String> input) {
		LinkedHashMap<String, String> output = new LinkedHashMap<String, String>();
		for (Map.Entry<String, String> entry : input.entrySet()) {
			// LOGGER.info("{}:{}", new Object[] { entry.getKey(),
			// entry.getValue() });
			if (entry.getValue() != null) {
				output.put(entry.getKey(),
						convert_string_for_XML_Predefined_entities(entry
								.getValue()));
			}
		}
		return output;
	}

	public static String convert_string_for_XML_Predefined_entities(String input) {
		/*
		 * CMS程式 //避免& 造成 word 語法錯誤 by johnny lin 2020-01-16
		 * c101m08r8.setTarget(Util.trim(dataMap.get("TARGET")).replace('&',
		 * ','));
		 */
		String r = input;
		r = r.replaceAll("<", "＜");
		r = r.replaceAll(">", "＞");
		r = r.replaceAll("&", "＆");
		return r;
	}

	public static String[] split_into_pre_match_aft_byFirstFind(String srcStr,
			Pattern p) {
		List<String> list = new ArrayList<String>();
		Matcher matcher = p.matcher(srcStr);
		if (matcher.find()) {
			list.add(srcStr.substring(0, matcher.start()));
			list.add(matcher.group());
			list.add(srcStr.substring(matcher.end()));
			// ============================
			return list.toArray(new String[list.size()]);
		}
		return new String[] { srcStr };
	}

	public static String[] split_tag_and_content(String srcStr, Pattern p_beg,
			Pattern p_end) {
		List<String> list = new ArrayList<String>();
		Matcher matcher_beg = p_beg.matcher(srcStr);
		if (matcher_beg.find()) {
			int idx_a = matcher_beg.end();
			int idx_b = -1;
			String cotent_endTag = srcStr.substring(idx_a);

			Matcher matcher_end = p_end.matcher(cotent_endTag);
			while (matcher_end.find()) {
				idx_b = idx_a + matcher_end.start();
			}
			if (idx_b > idx_a) {
				list.add(srcStr.substring(0, idx_a));
				list.add(srcStr.substring(idx_a, idx_b));
				list.add(srcStr.substring(idx_b));
				// ============================
				return list.toArray(new String[list.size()]);
			}
		}
		return new String[] { srcStr };
	}

	@Override
	public List<C122M01A> findMetaApplyKind_relateCase(String applyKind, String ploanCaseId) {
		String queryApplyKind = "";
		if (Util.equals(UtilConstants.C122_ApplyKind.P, applyKind)) {
			queryApplyKind = UtilConstants.C122_ApplyKind.Q;
		} else if (Util.equals(UtilConstants.C122_ApplyKind.E, applyKind)) {
			queryApplyKind = UtilConstants.C122_ApplyKind.F;
		} else if (Util.equals(UtilConstants.C122_ApplyKind.O, applyKind)) {
			queryApplyKind = UtilConstants.C122_ApplyKind.R;
		}
		ISearch search = c122m01aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "applyKind", queryApplyKind);
		search.addSearchModeParameters(SearchMode.EQUALS, "ploanCaseId", CapString.trimNull(ploanCaseId));
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "ploanCaseNo", CapString.trimNull(ploanCaseId));
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addOrderBy("applyTS");
		return c122m01aDao.find(search);
	}

	@Override
	public List<DocFile> getAttchUndoneMEGAImageDocFiles(String mainId) throws CapException {
		// 查這份文件的MinId
		C122M01A c122m01a = getC122M01A_byMainId(mainId);
		String ploanCaseNo = "";
		if (c122m01a != null) {
			// 用主債務人的案件編號
			ploanCaseNo = c122m01a.getPloanCaseId();
		}
		ISearch pageSetting = docFileDao.createSearchTemplete();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "contentType", "application/json");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "contentType", "application/x-pkcs7-certificates");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "contentType", "application/x-zip-compressed");
		// 此 method 去查 lms.bdocfile. 該table 只有 mainId,fieldId
		Page<DocFile> page = relatedAccountService.queryfile(pageSetting, false, false);
		List<DocFile> docFiles = page.getContent();
		List<DocFile> newDocFiles = new ArrayList<DocFile>();
		List<Map<String, Object>> megaImageList = mEGAImageService
				.getMEGAImageList(MEGAImageApiEnum.取得影像清單, mainId, ploanCaseNo);
		// 比對目前數位文件化上已有的檔案，顯示未上傳的
		for (DocFile docFile : docFiles) {
			String oid = docFile.getOid();
			boolean flagUploadSucceed = false;
			for (Map<String, Object> map : megaImageList) {
				String fileOid = CapString.trimNull(map.get("DocFileOid"));
				if (oid.equals(fileOid)) {
					flagUploadSucceed = true;
					break;
				}
			}
			if (!flagUploadSucceed) {
				newDocFiles.add(docFile);
			}
		}
		return newDocFiles;
	}

	@Override
	public List<Map<String, Object>> getAttchDoneMegaImageList(String mainId) throws CapException {
		List<Map<String, Object>> megaImageList = new ArrayList<Map<String, Object>>();
		try {
			C122M01A c122m01a = getC122M01A_byMainId(mainId);
			if (c122m01a != null) {
				String caseNo = c122m01a.getPloanCaseId();
				String c122MainId = c122m01a.getMainId();
				List<Map<String, Object>> list = mEGAImageService.getMEGAImageList(MEGAImageApiEnum.取得影像清單, c122MainId, caseNo);
				Map<String, String> formIdcodeMap = codetypeService.findByCodeType("MEGAIMAGE_FormId_ALL");
				// 加入查詢影像的URL
				// 調整顯示欄位
				for (Map<String, Object> map : list) {
					String scanType = CapString.trimNull(map.get("ScanType"));
					String formId = CapString.trimNull(map.get("FormId"));
					if (取得影像清單_ScanType.eLoan上傳文件.equals(scanType)
							&& !formId.matches("ECL0001[0-2]")) {
						// ScanType不為eCL - eLoan上傳文件
						continue;
					}
					String scanDateTime = CapString.trimNull(map.get("ScanDateTime"));
					Timestamp uploadTime = null;
					if (!CapString.isEmpty(scanDateTime)) {
						scanDateTime = scanDateTime.replace("/", "-");
						uploadTime = CapDate.convertStringToTimestamp(scanDateTime);
					}
					String borrower = CapString.trimNull(map.get("Borrower"));
					String stakeholderID = CapString.trimNull(map.get("StakeholderID"));
					//String formId = CapString.trimNull(map.get("FormId"));
					String formIdDesc = "";
					if (formId.length() > 3) {
						char[] formIdChars = formId.toCharArray();
						formIdChars[3] = '0';
						formIdDesc = formIdcodeMap.get(String.valueOf(formIdChars));
					}
					String srcFileName = formIdDesc;
					String fileNameCustId = "";
					if (borrower.equals(stakeholderID) || stakeholderID.isEmpty()) {
						// 主借款人
						fileNameCustId = borrower;
					} else {
						// 從債務人
						fileNameCustId = stakeholderID;
					}
					srcFileName = fileNameCustId + "_" + srcFileName;
					String docId = CapString.trimNull(map.get("DocId"));
					String docFileOid = CapString.trimNull(map.get("DocFileOid"));
					String fieldId = "MEGAImage";
					if (!CapString.isEmpty(docFileOid)) {
						DocFile docFile = docFileService.findByOidAndSysId(docFileOid, docFileService.getSysId());
						// 不等於userUpload表示由PLOAN來的資料，fieldId設為原本資料，提供前端辨識可否刪除
						if (!CapString.trimNull(docFile.getFieldId()).startsWith("userUpload")) {
							fieldId = docFile.getFieldId();
						}
					}
					map.put("flag", formId);
					map.put("srcFileName", srcFileName);
					map.put("uploadTime", uploadTime);
					map.put("fileSrc", "文件數位化系統");
					map.put("fieldId", fieldId);
					map.put("oid", docId);
					map.put("docFileOid", docFileOid);
					megaImageList.add(map);
				}
			}
		} catch (Exception e) {
			throw new CapException("[mainId="+mainId+"]轉換資料發生錯誤", getClass());
		}
		return megaImageList;
	}
	
	@Override
	public List<C122M01A> findPloanCSCNeedSendFARpaList(String createTime, String[] ploanPlan) {
		ISearch search = c122m01aDao.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTime);
		search.addSearchModeParameters(SearchMode.IN, "ploanPlan", ploanPlan);
		search.addSearchModeParameters(SearchMode.IS_NULL, "faRpaJobTs", null);
		
		return c122m01aDao.find(search);
	}
	
	@Override
	public List<C122M01A> findPloanCSCNeedQueryWiseNewsList(String createTime, String[] ploanPlan) {
		ISearch search = c122m01aDao.createSearchTemplete();

		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		search.addSearchModeParameters(SearchMode.IS_NULL, "isClosed", null);
		search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "createTime", createTime);
		search.addSearchModeParameters(SearchMode.IN, "ploanPlan", ploanPlan);
		search.addSearchModeParameters(SearchMode.IS_NULL, "queryWiseNewsTs", null);
		
		return c122m01aDao.find(search);
	}

	@Override
	public void updateAssigneeBrchList(List<Map<String, Object>> gridRowDataList) throws CapException {
		if (!gridRowDataList.isEmpty()) {
			// 將前端User隨意輸入的派案順序如2、8、3，順成1、2、3
			Map<String, Integer> oidAssignOrderMap = new HashMap<String, Integer>();
			for (Map<String, Object> gridRowData : gridRowDataList) {
				oidAssignOrderMap.put((String) gridRowData.get("assigneeBrchId"), Integer.parseInt((String) gridRowData.get("assignOrder")));
			}
			Map<String, Integer> sortedMap = this.sortMapByValue(oidAssignOrderMap);
	        int count = 0;
			for (Map.Entry<String, Integer> entry : sortedMap.entrySet()) {
		        count++;
		        sortedMap.put(entry.getKey(), count);
			}
			c900m01oDao.delete(c900m01oDao.findAll());
			String assigneeBrchIdName;
			String assigneeBrchId;
			MegaSSOUserDetails userInfo = MegaSSOSecurityContext.getUserDetails();
			String userId = userInfo.getUserId();
			Timestamp currTime = CapDate.getCurrentTimestamp();
			for (Map<String, Object> gridRowData : gridRowDataList) {
				C900M01O c900m01o = new C900M01O();
				assigneeBrchIdName = (String) gridRowData.get("assigneeBrchId");
				assigneeBrchId = assigneeBrchIdName.substring(0, 3);
				c900m01o.setAssigneeBrchId(assigneeBrchId);
				c900m01o.setAssignOrder(sortedMap.get(gridRowData.get("assigneeBrchId")));
				c900m01o.setCreator(userId);
				c900m01o.setCreateTime(currTime);
				c900m01o.setUpdater(userId);
				c900m01o.setUpdateTime(currTime);
				c900m01oDao.save(c900m01o);
			}
		}
	}

	private Map<String, Integer> sortMapByValue(Map<String, Integer> map) {
        LinkedHashMap<String, Integer> sortedMap = new LinkedHashMap<String, Integer>();
        ArrayList<Integer> list = new ArrayList<Integer>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        Collections.sort(list, new Comparator<Integer>() {
            public int compare(Integer value, Integer value1) {
                return (value).compareTo(value1);
            }
        });
        for (Integer value : list) {
            for (Entry<String, Integer> entry : map.entrySet()) {
                if (entry.getValue().equals(value)) {
                    sortedMap.put(entry.getKey(), value);
                }
            }
        }
		return sortedMap;
	}

    @Override
    public List<Object[]> findSMEAList(Date sDate, Date eDate){
        return c122m01aDao.findSMEAList(sDate,eDate);
    }
}
