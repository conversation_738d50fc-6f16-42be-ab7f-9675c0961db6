/* 
 * C120S01H.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 個金徵信訊息紀錄表(1個人可能會有2筆 C101S01H) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C120S01H", uniqueConstraints = @UniqueConstraint(columnNames = {"oid" }))
public class C120S01H extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** BANID **/
	@Size(max = 11)
	@Column(name = "BANID", length = 11, columnDefinition = "VARCHAR(11)")
	private String banid;

	/** ID **/
	@Size(max = 11)
	@Column(name = "ID", length = 11, columnDefinition = "VARCHAR(11)")
	private String Id;

	/** PRODID **/
	@Size(max = 2)
	@Column(name = "PRODID", length = 2, columnDefinition = "VARCHAR(02)")
	private String prodid;

	/** TXID（H128, H135） ===> select * from mis.logfile where TXID in ('Q116', 'Q128', 'Q135') **/
	@Size(max = 4)
	@Column(name = "TXID", length = 4, columnDefinition = "VARCHAR(04)")
	private String txid;

	/** HTMLDATA **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name = "HTMLDATA", columnDefinition = "CLOB")
	private String htmlData;

	/** QDATE **/
	@Size(max = 9)
	@Column(name = "QDATE", length = 9, columnDefinition = "VARCHAR(09)")
	private String qDate;

	/** QEMPCODE **/
	@Size(max = 11)
	@Column(name = "QEMPCODE", length = 11, columnDefinition = "VARCHAR(11)")
	private String qEmpCode;

	/** QEMPNAME **/
	@Size(max = 20)
	@Column(name = "QEMPNAME", length = 20, columnDefinition = "VARCHAR(20)")
	private String qEmpName;

	/** QBRANCH **/
	@Size(max = 3)
	@Column(name = "QBRANCH", length = 3, columnDefinition = "VARCHAR(03)")
	private String qBranch;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得BANID **/
	public String getBanid() {
		return this.banid;
	}

	/** 設定BANID **/
	public void setBanid(String value) {
		this.banid = value;
	}

	/** 取得ID **/
	public String getId() {
		return this.Id;
	}

	/** 設定ID **/
	public void setId(String value) {
		this.Id = value;
	}

	/** 取得PRODID **/
	public String getProdid() {
		return this.prodid;
	}

	/** 設定PRODID **/
	public void setProdid(String value) {
		this.prodid = value;
	}

	/** 取得TXID（H128, H135） ===> select * from mis.logfile where TXID in ('Q116', 'Q128', 'Q135') **/
	public String getTxid() {
		return this.txid;
	}

	/** 設定TXID（H128, H135） ===> select * from mis.logfile where TXID in ('Q116', 'Q128', 'Q135') **/
	public void setTxid(String value) {
		this.txid = value;
	}

	/** 取得HTMLDATA **/
	public String getHtmlData() {
		return this.htmlData;
	}

	/** 設定HTMLDATA **/
	public void setHtmlData(String value) {
		this.htmlData = value;
	}

	/** 取得QDATE **/
	public String getQDate() {
		return this.qDate;
	}

	/** 設定QDATE **/
	public void setQDate(String value) {
		this.qDate = value;
	}

	/** 取得QEMPCODE **/
	public String getQEmpCode() {
		return this.qEmpCode;
	}

	/** 設定QEMPCODE **/
	public void setQEmpCode(String value) {
		this.qEmpCode = value;
	}

	/** 取得QEMPNAME **/
	public String getQEmpName() {
		return this.qEmpName;
	}

	/** 設定QEMPNAME **/
	public void setQEmpName(String value) {
		this.qEmpName = value;
	}

	/** 取得QBRANCH **/
	public String getQBranch() {
		return this.qBranch;
	}

	/** 設定QBRANCH **/
	public void setQBranch(String value) {
		this.qBranch = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
