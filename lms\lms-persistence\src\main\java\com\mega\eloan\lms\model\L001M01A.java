/* 
 * L001M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 待辦事項篩選條件檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L001M01A", uniqueConstraints = @UniqueConstraint(columnNames = { "userId" }))
public class L001M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 登入者ID **/
	@Column(name = "USERID", length = 6, columnDefinition = "CHAR(6)")
	private String userId;

	/**
	 * 文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 */
	@Column(name = "CASETYPE", length = 10, columnDefinition = "VARCHAR(10)")
	private String caseType;

	/**
	 * 文件狀態
	 * <p/>
	 * 編製中<br/>
	 * 待覆核...
	 */
	@Column(name = "DOCSTATUS", length = 3, columnDefinition = "CHAR(3)")
	private String docStatus;

	/**
	 * 篩選條件
	 * <p/>
	 * 1.依日期<br/>
	 * 2.依主要借款人統編<br/>
	 * 3.依經辦主管篩選
	 */
	@Column(name = "FILTERTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String filterType;

	/**
	 * 排列方式
	 * <p/>
	 * 升冪<br/>
	 * 降冪
	 */
	@Column(name = "SORTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String sortType;

	/**
	 * 是否不顯示篩選視窗
	 * <p/>
	 * Y:不顯示
	 */
	@Column(name = "CHECKSHOW", length = 1, columnDefinition = "CHAR(1)")
	private String checkShow;

	/** 查詢起日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "STARTDATE", columnDefinition = "DATE")
	private Date startDate;

	/** 查詢迄日 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "DATE")
	private Date endDate;

	/** 統一編號 **/
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 經辦或主管編號 **/
	@Column(name = "MANGERID", length = 6, columnDefinition = "CHAR(6)")
	private String mangerId;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得登入者ID **/
	public String getUserId() {
		return this.userId;
	}

	/** 設定登入者ID **/
	public void setUserId(String value) {
		this.userId = value;
	}

	/**
	 * 取得文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 */
	public String getCaseType() {
		return this.caseType;
	}

	/**
	 * 設定文件種類
	 * <p/>
	 * L120M01A=案件簽報書<br/>
	 * L141M01A=聯行額度明細表<br/>
	 * L160M01A=動用審核表
	 **/
	public void setCaseType(String value) {
		this.caseType = value;
	}

	/**
	 * 取得文件狀態
	 * <p/>
	 * 編製中<br/>
	 * 待覆核...
	 */
	public String getDocStatus() {
		return this.docStatus;
	}

	/**
	 * 設定文件狀態
	 * <p/>
	 * 編製中<br/>
	 * 待覆核...
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/**
	 * 取得篩選條件
	 * <p/>
	 * 1.依日期<br/>
	 * 2.依主要借款人統編<br/>
	 * 3.依經辦主管篩選
	 */
	public String getFilterType() {
		return this.filterType;
	}

	/**
	 * 設定篩選條件
	 * <p/>
	 * 1.依日期<br/>
	 * 2.依主要借款人統編<br/>
	 * 3.依經辦主管篩選
	 **/
	public void setFilterType(String value) {
		this.filterType = value;
	}

	/**
	 * 取得排列方式
	 * <p/>
	 * 升冪<br/>
	 * 降冪
	 */
	public String getSortType() {
		return this.sortType;
	}

	/**
	 * 設定排列方式
	 * <p/>
	 * 升冪<br/>
	 * 降冪
	 **/
	public void setSortType(String value) {
		this.sortType = value;
	}

	/**
	 * 取得是否不顯示篩選視窗
	 * <p/>
	 * Y:不顯示
	 */
	public String getCheckShow() {
		return this.checkShow;
	}

	/**
	 * 設定是否不顯示篩選視窗
	 * <p/>
	 * Y:不顯示
	 **/
	public void setCheckShow(String value) {
		this.checkShow = value;
	}

	/** 取得查詢起日 **/
	public Date getStartDate() {
		return this.startDate;
	}

	/** 設定查詢起日 **/
	public void setStartDate(Date value) {
		this.startDate = value;
	}

	/** 取得查詢迄日 **/
	public Date getEndDate() {
		return this.endDate;
	}

	/** 設定查詢迄日 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得經辦或主管編號 **/
	public String getMangerId() {
		return this.mangerId;
	}

	/** 設定經辦或主管編號 **/
	public void setMangerId(String value) {
		this.mangerId = value;
	}

	/* (non-Javadoc)
	 * @see com.mega.eloan.common.model.IDocObject#getMainId()
	 */
	@Override
	public String getMainId() {
		// TODO Auto-generated method stub
		return null;
	}
}
