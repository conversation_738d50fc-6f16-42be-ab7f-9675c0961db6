/* 
 *MisQuotapprService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

/**
 * <pre>
 * 授信額度檔 ELF383N (MIS.ELF383N)
 * </pre>
 * 
 * @since 2023/07/28
 * @version <ul>
 *          2023/07/28,new
 *          </ul>
 */
public interface MisELF383NService {

	/**
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @param gutType
	 *            送保方式
	 * @param batGutNo
	 *            批次保證額度查覆書案號
	 * @param batGutSeq
	 *            批號
	 */
	void insertForInside(String custId, String dupNo, String cntrNo,
			String sDate, String gutType, String batGutNo, String batGutSeq,
			String MarginFlag);

	/**
	 * 刪除
	 * 
	 * @param custId
	 *            身分證/統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統當天日期
	 */
	void delByUniqueKeyWithoutONLNTIME(String custId, String dupNo,
			String cntrNo, String sDate);

}
