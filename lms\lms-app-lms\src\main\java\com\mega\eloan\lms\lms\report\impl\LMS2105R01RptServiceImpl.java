/* 
 *LMS2105R01RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.lms.service.LMS2105Service;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L210M01A;
import com.mega.eloan.lms.model.L210S01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 *  產生修改資料流程PDF 聯貸案參貸比率一覽表
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("lms2105r01rptservice")
public class LMS2105R01RptServiceImpl extends AbstractReportService {

	@Resource
	LMS2105Service lms2105Service;

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;
	
	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	// private static Logger logger = LoggerFactory
	// .getLogger(LMS2105R01RptServiceImpl.class);

	@Override
	public String getReportTemplateFileName() {
		Locale locale = null;
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		return "report/lms/LMS1605R02_" + locale.toString() + ".rpt";
		// local用
		// return "D:/LMS1605R02_zh_TW.rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * org.apache.wicket.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		// L160M01A．動用審核表主檔
		L210M01A l210m01a = null;
		Locale locale = null;
		try {

			locale = LocaleContextHolder.getLocale();
			if (locale == null) {
				locale = Locale.getDefault();
			}

			dfMoney.set(new DecimalFormat("#,###,###,###,##0"));
			dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));
			l210m01a = lms2105Service.findModelByOid(L210M01A.class, mainOid);

			L161S01A l161s01a = lms1605Service.findL161m01aByMainId(l210m01a
					.getRefMainId(),l210m01a
					.getRefMainId());

			List<L210S01B> l210s01bs = lms2105Service
					.findL210s01bsByMainIdAndChgFlag(l210m01a.getMainId(),
							UtilConstants.editDoc.chanFlag.變動後);
			
			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00",l210m01a.getOwnBrId());
			rptVariableMap.put("LOGOSHOW", logoPath);
			
			
			// 案號
			rptVariableMap
					.put("CASENO", Util.nullToSpace(l210m01a.getCaseNo()));
			String gist = "";
			if (l161s01a != null) {
				gist = l161s01a.getGist();
			}
			rptVariableMap.put("GIST", gist);
			rptVariableMap.put("CURR", Util.nullToSpace(l210m01a.getLtCurr()));
			rptVariableMap.put("QUERYEDATE",
					NumConverter.addComma(l210m01a.getLtAmt()));
			rptVariableMap
					.put("CASEDATE", this.getDate(l210m01a.getCaseDate()));
			rptVariableMap
					.put("CNTRNO", Util.nullToSpace(l210m01a.getCntrNo()));

			titleRows = this.setL161S01BDataList(titleRows, locale, l210s01bs,
					prop);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
		}finally{
			
		}
	}

	/**
	 * 取得日期(XXXX-XX-XX)
	 * 
	 * @param date
	 *            日期
	 * @return 日期
	 */
	private String getDate(Date date) {
		String str = null;
		if (date == null) {
			str = "";
		} else {
			str = TWNDate.toAD(date);
		}
		return str;
	}

	/**
	 * 設定L161S01B資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL161S01BDataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L210S01B> list, Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		int count = 1;
		StringBuffer temp = new StringBuffer();
		for (L210S01B l210s01b : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));
			setBrankName(mapInTitleRows, l210s01b, temp);
			mapInTitleRows.put("ReportBean.column04", UtilConstants.DEFAULT.是
					.equals(l210s01b.getSlMaster()) ? prop.getProperty("yes")
					: prop.getProperty("no"));
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l210s01b.getSlAccNo()));
			mapInTitleRows.put("ReportBean.column06",
					NumConverter.addComma(l210s01b.getSlAmt()));
			count++;
			titleRows.add(mapInTitleRows);
		}

		if (titleRows.isEmpty()) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", "");
			mapInTitleRows.put("ReportBean.column04", "");
			mapInTitleRows.put("ReportBean.column05", "");
			mapInTitleRows.put("ReportBean.column06", "");
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 處理銀行名稱顯示
	 * 
	 * @param mapInTitleRows
	 * @param l210s01b
	 *            聯貸案參貸比率一覽表明細檔
	 * @param temp
	 *            暫存文字用
	 * @return
	 */
	private Map<String, String> setBrankName(
			Map<String, String> mapInTitleRows, L210S01B l210s01b,
			StringBuffer temp) {

		switch (Util.parseInt(l210s01b.getSlBankType())) {
		case 1:
		case 2:
			mapInTitleRows.put(
					"ReportBean.column02",
					temp.append(l210s01b.getSlBank()).append(" ")
							.append(l210s01b.getSlBankCN()).toString());
			temp.setLength(0);
			mapInTitleRows.put(
					"ReportBean.column03",
					temp.append(l210s01b.getSlBranch()).append(" ")
							.append(l210s01b.getSlBranchCN()).toString());
			temp.setLength(0);
			break;
		case 3:
		case 4:
		case 5:
		case 6:
		case 7:
		case 8:
		case 9:
		case 10:
		case 11:
		case 12:
			mapInTitleRows.put(
					"ReportBean.column02",
					temp.append(l210s01b.getSlBank()).append(" ")
							.append(l210s01b.getSlBankCN()).toString());
			temp.setLength(0);
			mapInTitleRows.put("ReportBean.column03", "");

			break;

		}

		return mapInTitleRows;
	}

}
