package tw.com.iisi.cap.context;

/**
 * <pre>
 * 定義頁面
 * </pre>
 */
public class ApplicationParameter {

    /**
     * 主頁
     */
    private String homePage;

    /**
     * 錯誤訊息
     */
    private String errorPage;

    /**
     * 錯誤頁面
     */
    private String errorPageView;

    /**
     * 預設地區語言
     */
    private String defautlLocale;

    /**
     * 取得主頁面
     */
    public String getHomePage() {
        return homePage;
    }

    /**
     * 設定主頁面
     * 
     * @param homePage
     *            主頁面路徑
     */
    public void setHomePage(String homePage) {
        this.homePage = homePage;
    }

    /**
     * 取得錯誤訊息
     * 
     * @return errorPage
     */
    public String getErrorPage() {
        return errorPage;
    }

    /**
     * 設置錯誤訊息要出現的畫面
     * 
     * @param errorPage
     *            錯誤訊息路徑
     */
    public void setErrorPage(String errorPage) {
        this.errorPage = errorPage;
    }

    /**
     * 取得錯誤頁面
     * 
     * @return errorPageView
     */
    public String getErrorPageView() {
        return errorPageView;
    }

    /**
     * 設置錯誤頁面
     * 
     * @param errorPageView
     *            錯誤頁面路徑
     */
    public void setErrorPageView(String errorPageView) {
        this.errorPageView = errorPageView;
    }

    /**
     * 取得地區語言
     * 
     * @return defautlLocale
     */
    public String getDefautlLocale() {
        return defautlLocale;
    }

    /**
     * 設置地區語言
     * 
     * @param defautlLocale
     *            地區語言
     */
    public void setDefautlLocale(String defautlLocale) {
        this.defautlLocale = defautlLocale;
    }

}
