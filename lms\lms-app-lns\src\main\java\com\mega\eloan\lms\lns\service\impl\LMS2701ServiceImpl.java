/*
 * LMS2701ServiceImpl.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.service.impl;

import javax.annotation.Resource;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.dao.L270M01ADao;
import com.mega.eloan.lms.lns.service.LMS2701Service;
import com.mega.eloan.lms.model.L270M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;

import java.util.List;

/**
 * <pre>
 * 企金信保
 * 信保基金送保查詢
 * </pre>
 *
 * @since 2021/6/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/6/1,009301,new
 *          </ul>
 */
@Service
public class LMS2701ServiceImpl extends AbstractCapService implements
		LMS2701Service {

	@Resource
	TempDataService tempDataService;

	@Resource
	DocLogService docLogService;
	
	@Resource
	L270M01ADao l270m01aDao;

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L270M01A) {
					if (((L270M01A) model).getDocStatus().equals(
							CreditDocStatusEnum.海外_編製中.getCode())
							|| ((L270M01A) model).getDocStatus().equals(
							CreditDocStatusEnum.海外_待補件.getCode())) {
						// 當文件狀態為編製中時文件亂碼才變更
						((L270M01A) model).setUpdater(user.getUserId());
						((L270M01A) model).setUpdateTime(CapDate
								.getCurrentTimestamp());
						if (!"Y".equals(SimpleContextHolder
								.get(EloanConstants.TEMPSAVE_RUN))) {
							tempDataService.deleteByMainId(((L270M01A) model)
									.getMainId());
							docLogService.record(((L270M01A) model).getOid(),
									DocLogEnum.SAVE);
						}
					}
					l270m01aDao.save((L270M01A) model);
				}
			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		// TODO Auto-generated method stub
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L270M01A.class) {
			return l270m01aDao.findPage(search);
		}
		return null;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L270M01A.class) {
			return (T) l270m01aDao.findByOid(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
														String mainId) {
		if (clazz == L270M01A.class) {
			return l270m01aDao.findByIndex01(mainId);
		}
		return null;
	}
}
