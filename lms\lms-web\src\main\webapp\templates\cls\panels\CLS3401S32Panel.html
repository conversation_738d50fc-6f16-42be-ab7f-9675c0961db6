<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
        	<script type="text/javascript">
					loadScript('pagejs/cls/CLS3401S32Panel');
			</script>
			
			<table class='tb2 alignTopTab ' width='100%'>
				<tr>
					<td class='hd1' style='width:230px' >立約人
					</td>
					<td>
           				<table width='100%'>
           					<tr>
           						<td class='hd2'>甲方：              							
           						</td>       
								<td><input type="text" id="eloan_p1_contr_name_m" name="eloan_p1_contr_name_m" size="60" />
								</td>
           					</tr>
           					<tr>
           						<td class='hd2'>　連帶保證人：     						
           						</td>       
								<td><input type="text" id="eloan_p1_contr_name_g" name="eloan_p1_contr_name_g" size="90" />
								</td>
           					</tr>
           					<tr>
           						<td class='hd2'>　一般保證人：     							
           						</td>       
								<td><input type="text" id="eloan_p1_contr_name_n" name="eloan_p1_contr_name_n" size="90" />
								</td>
           					</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class='hd1'>貸款金額
					</td>
					<td>新臺幣<input type="text" id="eloan_p1_contr_amt" name="eloan_p1_contr_amt" size="30" />元
					<div th:utext="${S02_Chinese_Number}"></div>
					</td>
				</tr>
				<tr>
					<td class='hd1'><th:block th:text="#{'column.deliv'}">交付方式</th:block>
					</td>
					<td>							
						<table border='0' class='alignTopTab '>
           					<tr>
           						<td class='noborder cbPaddingClass '><input type='checkbox' id='eloan_pa_deliv_A_cb' name='eloan_pa_deliv_A_cb' value='Y'>				
           						</td>       
								<td class='noborder paddingBottomTd' >
									<div>
										<label>憑借款支用書撥付甲方在乙方開設之
										<input type="text" id="eloan_pa_deliv_A_t1" name="eloan_pa_deliv_A_t1" size="6" />存款第
										<input type="text" id="eloan_pa_deliv_A_t2" name="eloan_pa_deliv_A_t2" size="16" />號帳戶。
										</label>
										<button type="button" id="delivActno_imp" >
											<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
										</button>
									</div>
								</td>
           					</tr>
           					<tr>
           						<td class='noborder cbPaddingClass '><input type='checkbox' id='eloan_pa_deliv_B_cb' name='eloan_pa_deliv_B_cb' value='Y'>				
           						</td>       
								<td class='noborder paddingBottomTd'>
									<div>
										<label>由甲方自行動撥或提領，即視為本借款之交付。
										</label>
									</div>
								</td>
           					</tr>
           					<tr>
           						<td class='noborder  '><input type='checkbox' id='eloan_pa_deliv_C_cb' name='eloan_pa_deliv_C_cb' value='Y'>				
           						</td>       
								<td class='noborder paddingBottomTd'>
									<div>
										<label>甲方於乙方第
										<input type="text" id="eloan_pa_deliv_C_t1" name="eloan_pa_deliv_C_t1" size="16" />號支票存款帳戶無存款餘額或餘額不足支付票款時，由乙方先予墊付票款，即視為本借款之交付 。
										</label>
										<button type="button" id="deliv_C_t1Actno_imp" >
											<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
										</button>
									</div>
								</td>
           					</tr>
           					<tr>
           						<td class='noborder  '><input type='checkbox' id='eloan_pa_deliv_D_cb' name='eloan_pa_deliv_D_cb' value='Y'>				
           						</td>       
								<td class='noborder paddingBottomTd'>
									<div>
										<label>撥付甲方在乙方開設之
										<input type="text" id="eloan_pa_deliv_D_t1" name="eloan_pa_deliv_D_t1" size="8" />
										存款第
										<input type="text" id="eloan_pa_deliv_D_t2" name="eloan_pa_deliv_D_t2" size="16" />
										號帳戶並依下列指示匯付款項(適用於轉貸案件)：
										</label>
										<button type="button" id="deliv_D_t2Actno_imp" >
											<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
										</button>
										
										<table border='0' class='alignTopTab '>
				           					<tr>
				           						<td class='noborder  '>(一)
				           						</td>       
												<td class='noborder paddingBottomTd' >
													甲方或擔保物提供人(如甲方配偶、保證人)前已提供擔保之標的物(門牌地址：
													<input type="text" id="eloan_pa_deliv_D1_t1" name="eloan_pa_deliv_D1_t1" size="70" />建物及持分土地)，並設定
													前順位抵押權<input type="text" id="eloan_pa_deliv_D1_t2" name="eloan_pa_deliv_D1_t2" size="16" /> 元整
													予<input type="text" id="eloan_pa_deliv_D1_t3" name="eloan_pa_deliv_D1_t3" size="16" /> (金融機構，以下稱原借款銀行)，截至
													民國<input type="text" id="eloan_pa_deliv_D1_t4" name="eloan_pa_deliv_D1_t4" size="5" />年
													<input type="text" id="eloan_pa_deliv_D1_t5" name="eloan_pa_deliv_D1_t5" size="4" />月
													<input type="text" id="eloan_pa_deliv_D1_t6" name="eloan_pa_deliv_D1_t6" size="4" />日止尚有
													本金<input type="text" id="eloan_pa_deliv_D1_t7" name="eloan_pa_deliv_D1_t7" size="16" /> 元整及其利息(以下稱前順位抵押借款債務)待償，實際代償金額以代償當日尚欠前順位抵押借款債務及匯費之金額為準。

												</td>
				           					</tr>
				           					<tr>
				           						<td class='noborder  '>(二)
				           						</td>       
												<td class='noborder paddingBottomTd' >
													甲方同意就前目之標的物於設定次順位抵押權予乙方，且將塗銷原借款銀行前順位抵押權之有關文件（抵押權塗銷同意書及他項權利證明書除外）備妥並用印交付予乙方後，授權乙方依下列勾選之方式代償：
													<table border='0' class='alignTopTab '>
				           								<tr>
				           									<td class='noborder  '><input type='checkbox' id='eloan_pa_deliv_D2_1cb' name='eloan_pa_deliv_D2_1cb' value='Y'>	
				           									</td>
															<td class='noborder  '> 1、授權乙方以甲方為匯款人及乙方為代理人，將動用之款項匯付原借款銀行償付甲方之前順位抵押借款債務，以塗銷前順位之抵押權登記。
				           									</td>
				           								</tr>
				           								<tr>
				           									<td class='noborder  '><input type='checkbox' id='eloan_pa_deliv_D2_2cb' name='eloan_pa_deliv_D2_2cb' value='Y'>	
				           									</td>
															<td class='noborder  '>	2、授權乙方逕以乙方名義，將動用之款項匯付原借款銀行償付甲方或擔保物提供人(如甲方配偶、保證人)之前順位抵押借款債務，以塗銷前順位之抵押權登記。
				           									</td>
				           								</tr>
													</table>
												</td>
				           					</tr>
				           					<tr>
				           						<td class='noborder  '>(三)
				           						</td>       
												<td class='noborder paddingBottomTd' >
													倘原借款銀行之前順位抵押借款債務及匯費之金額超過乙方借款金額時，甲方於接獲乙方通知後應先將超過乙方核給之借款金額之差額匯入甲方於乙方開立之第<input type="text" id="eloan_pa_deliv_D3_t1" name="eloan_pa_deliv_D3_t1" size="16" />號帳戶，始得向乙方申請撥款代償。
													<button type="button" id="deliv_D3_t1Actno_imp" >
														<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
													</button>
												</td>
				           					</tr>
				           					<tr>
				           						<td class='noborder  '>(四)
				           						</td>       
												<td class='noborder paddingBottomTd' >
													乙方代償並取得第一順位抵押權後，如借款金額高於前順位抵押借款債務時，超過部份甲方得自行提領，不受本章第二條借款用途之限制。
												</td>
				           					</tr>
										</table>	
									</div>
								</td>
           					</tr>
							<tr>
           						<td class='noborder cbPaddingClass '><input type='checkbox' id='eloan_pa_deliv_E_cb' name='eloan_pa_deliv_E_cb' value='Y'>				
           						</td>       
								<td class='noborder paddingBottomTd'>
									<label>
									依甲方與乙方個別約定之撥付方式：<input type="text" id="eloan_pa_deliv_E_t1" name="eloan_pa_deliv_E_t1" size="45" />
									</label>
								</td>
           					</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class='hd1'><th:block th:text="#{'column.purpose'}">借款用途</th:block>
					</td>
					<td>
						本借款用途如下列第<input type="text" id="eloan_pa_purpose_way" name="eloan_pa_purpose_way" size="12" />款之記載：<br/>
						一、個人或家庭理財。<br/>
						二、購買房屋。<br/>
						三、修繕房屋。<br/>
						四、購買汽車。<br/>
						五、繳付甲方或甲方子女學費。<br/>
						六、購置耐久性消費財。<br/>
						七、其他：<input type="text" id="eloan_pa_purpose_G_t1" name="eloan_pa_purpose_G_t1" size="40" />
					</td>
				</tr>
				<tr>
					<td class='hd1'><th:block th:text="#{'column.use'}">動用方式</th:block>、動用期限<br/>及貸款期間
					</td>
					<td>
						甲方應依下列方式支用本借款：<!-- 甲方應依下列第  款方式支用本借款：-->
						<table class='tb2'>
						<tr>
							<td class='noborder'>一、</td>
							<td class='noborder'>自民國<input type="text" id="eloan_pa_use_A_t1" name="eloan_pa_use_A_t1" size="3" />年
								<input type="text" id="eloan_pa_use_A_t2" name="eloan_pa_use_A_t2" size="3" />月
								<input type="text" id="eloan_pa_use_A_t3" name="eloan_pa_use_A_t3" size="3" />日起至民國
								<input type="text" id="eloan_pa_use_A_t4" name="eloan_pa_use_A_t4" size="3" />年
								<input type="text" id="eloan_pa_use_A_t5" name="eloan_pa_use_A_t5" size="3" />月
								<input type="text" id="eloan_pa_use_A_t6" name="eloan_pa_use_A_t6" size="3" />日止之
								<input type="text" id="eloan_pa_use_A_t7" name="eloan_pa_use_A_t7" size="3" />個月內，在
								<input type="text" id="eloan_pa_use_A_t8" name="eloan_pa_use_A_t8" size="16" />元整額度內，憑借款支用書循環支用本借款，上述期間屆滿日即為借款到期日（適用於短期/中期循環借款）。
							</td>
						</tr>	   
						<tr>
							<td class='noborder'>二、</td>
							<td class='noborder'>自民國<input type="text" id="eloan_pa_use_B_t1" name="eloan_pa_use_B_t1" size="3" />年
								<input type="text" id="eloan_pa_use_B_t2" name="eloan_pa_use_B_t2" size="3" />月
								<input type="text" id="eloan_pa_use_B_t3" name="eloan_pa_use_B_t3" size="3" />日起至民國
								<input type="text" id="eloan_pa_use_B_t4" name="eloan_pa_use_B_t4" size="3" />年
								<input type="text" id="eloan_pa_use_B_t5" name="eloan_pa_use_B_t5" size="3" />月
								<input type="text" id="eloan_pa_use_B_t6" name="eloan_pa_use_B_t6" size="3" />日止之
								<input type="text" id="eloan_pa_use_B_t7" name="eloan_pa_use_B_t7" size="3" />個月內，在
								<input type="text" id="eloan_pa_use_B_t8" name="eloan_pa_use_B_t8" size="16" />元整額度內，以金融卡、網路銀行或存摺與取款憑條於乙方第
								<input type="text" id="eloan_pa_use_B_t9" name="eloan_pa_use_B_t9" size="16" />號存款帳戶為存款額以外循環支用本借款，上述期間屆滿日即為借款到期日（適用於短期/中期循環借款）。
								<button type="button" id="use_B_t9Actno_imp" >
									<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
								</button>
							</td>
						</tr>
						<tr>
							<td class='noborder'>三、</td>
							<td class='noborder'>自民國<input type="text" id="eloan_pa_use_C_t1" name="eloan_pa_use_C_t1" size="3" />年
								<input type="text" id="eloan_pa_use_C_t2" name="eloan_pa_use_C_t2" size="3" />月
								<input type="text" id="eloan_pa_use_C_t3" name="eloan_pa_use_C_t3" size="3" />日起至民國
								<input type="text" id="eloan_pa_use_C_t4" name="eloan_pa_use_C_t4" size="3" />年
								<input type="text" id="eloan_pa_use_C_t5" name="eloan_pa_use_C_t5" size="3" />月
								<input type="text" id="eloan_pa_use_C_t6" name="eloan_pa_use_C_t6" size="3" />日止之
								<input type="text" id="eloan_pa_use_C_t7" name="eloan_pa_use_C_t7" size="3" />個月內，在
								<input type="text" id="eloan_pa_use_C_t8" name="eloan_pa_use_C_t8" size="16" />元整額度內，簽發乙方第
								<input type="text" id="eloan_pa_use_C_t9" name="eloan_pa_use_C_t9" size="16" />號存款帳戶之支票委託乙方付款，於該帳戶存款額以外透支，上述期間屆滿日即為借款到期日（適用於短期循環借款）。
								<button type="button" id="use_C_t9Actno_imp" >
									<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
								</button>

							</td>
						</tr>
						<tr>
							<td class='noborder'>四、</td>
							<td class='noborder'>自簽約日起<input type="text" id="eloan_pa_use_D_t1" name="eloan_pa_use_D_t1" size="3" />個月內，在
								<input type="text" id="eloan_pa_use_D_t2" name="eloan_pa_use_D_t2" size="16" />元整額度內憑借款支用書支用本借款。借款期限為
							　　<input type="text" id="eloan_pa_use_D_t3" name="eloan_pa_use_D_t3" size="3" />年，至民國
								<input type="text" id="eloan_pa_use_D_t4" name="eloan_pa_use_D_t4" size="3" />年
								<input type="text" id="eloan_pa_use_D_t5" name="eloan_pa_use_D_t5" size="3" />月
								<input type="text" id="eloan_pa_use_D_t6" name="eloan_pa_use_D_t6" size="3" />日止到期（適用於中、長期不循環借款）。
							</td>
						</tr>
						<tr>
							<td class='noborder'>五、</td>
							<td class='noborder'>（由甲方與乙方個別約定）： <input type="text" id="eloan_pa_use_E_t1" name="eloan_pa_use_E_t1" size="60" />
							</td>
						</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class='hd1'><th:block th:text="#{'column.repay'}">貸款本息攤還方式</th:block>
					</td>
					<td>
						本借款還本付息方式如下列第<input type="text" id="eloan_pa_repay_way" name="eloan_pa_repay_way" size="12" />款：
						<table class='tb2'>
						<tr>
							<td class='noborder'>一、</td>
							<td class='noborder'>自撥款日起，按月付息一次，到期還清本金。
							</td>
						</tr>	    
						<tr>
							<td class='noborder'>二、</td>
							<td class='noborder'>自撥款日起，按年金法，按月攤付本息。
							</td>
						</tr>   
						<tr>
							<td class='noborder'>三、</td>
							<td class='noborder'>自撥款日起，本金按月平均攤還，利息按月計付。
							</td>
						</tr>  
						<tr>
							<td class='noborder'>四、</td>
							<td class='noborder'>自撥款日起，前<input type="text" id="eloan_pa_repay_D_t1" name="eloan_pa_repay_D_t1" size="2" />年
							（<input type="text" id="eloan_pa_repay_D_t2" name="eloan_pa_repay_D_t2" size="2" />個月）按月付息，自第
							<input type="text" id="eloan_pa_repay_D_t3" name="eloan_pa_repay_D_t3" size="2" />年（
							<input type="text" id="eloan_pa_repay_D_t4" name="eloan_pa_repay_D_t4" size="2" />個月）起，再依年金法，按月平均攤還本息。
							</td>
						</tr>
						<tr>
							<td class='noborder'>五、</td>
							<td class='noborder'>自撥款日起，前<input type="text" id="eloan_pa_repay_E_t1" name="eloan_pa_repay_E_t1" size="2" />
							年（<input type="text" id="eloan_pa_repay_E_t2" name="eloan_pa_repay_E_t2" size="2" />個月）按月付息，自第
							<input type="text" id="eloan_pa_repay_E_t3" name="eloan_pa_repay_E_t3" size="2" />年（
							<input type="text" id="eloan_pa_repay_E_t4" name="eloan_pa_repay_E_t4" size="2" />個月）起，本金按月平均攤還，利息按月計付。
							</td>
						</tr>   
						<tr>
							<td class='noborder'>六、</td>
							<td class='noborder'>自撥款日起，以每十四天為一期，分<input type="text" id="eloan_pa_repay_F_t1" name="eloan_pa_repay_F_t1" size="4" />期償還。(每期償還之本息金額為：以
							     <input type="text" id="eloan_pa_repay_F_t2" name="eloan_pa_repay_F_t2" size="4" />年為期，依年金法按月攤還本息計算所得每月應攤付金額之二分之一)。
							</td>
						</tr>  
						<tr>
							<td class='noborder'>七、</td>
							<td class='noborder'>（由甲方與乙方個別約定）：<input type="text" id="eloan_pa_repay_G_t1" name="eloan_pa_repay_G_t1" size="60" />
							</td>
						</tr>
						</table>
						<div>
							<table class='tb2 alignTopTab ' width='100%'>
							<tr>
								<td class='noborder'><input type='checkbox' disabled><!-- 客戶親手勾選 -->
								</td>
								<td class='noborder'>「無限制清償期間」：甲方同意按本章第五條計付借款利息，甲方並得隨時償還借款或結清帳戶，無須支付違約金。
								</td>
							</tr>
							<tr>
								<td class='noborder'><input type='checkbox' disabled><!-- 客戶親手勾選 -->
								</td>
								<td class='noborder'>「限制清償期間」：甲方同意按本章第五條計付借款利息，並同意如於本借款撥款日起二年內提前清償本金時，給付提前清償違約金，但甲方因「提供抵押之不動產遭政府徵收或天災毀損並取得證明」、「借款人死亡或重大傷殘並取得證明」或「乙方主動要求還款」致須提前清償借款者，不在此限。上開提前清償違約金之計算方式如下：
								<table class='alignTopTab'>
    							<tr>
    								<td colspan='2' class='noborder'>一、自撥款日起一年內提前還款者(第一年還款)，得就提前償還之本金按1% 計收。
									</td>
								</tr>
    							<tr>
    								<td colspan='2' class='noborder'>二、自撥款日起超過一年未滿二年內提前還款者(第二年還款)，得就提前償還之本金按0.5%計收。
									</td>
								</tr>
    							<tr>
    								<td class='noborder'>三、其他：
									</td>
    								<td class='noborder'><textarea id="eloan_pa_repay_withPPP_term" name="eloan_pa_repay_withPPP_term" style="width:600px;height:50px" >
										</textarea>
									</td>
								</tr>
								</table>
								</td>
							</tr>
							</table>
						</div>
						<div style='margin-top:2.5em;'>
						甲方為便於日後償付本借款有關之債務及費用，茲授權乙方就甲方於乙方開立之第<input type="text" id="eloan_pa_repay_actNo" name="eloan_pa_repay_actNo" size="16" />號帳戶
						<button type="button" id="repayActno_imp" >
							<span class="text-only"><th:block th:text="#{'button.pullin'}" >引進</th:block></span>
						</button>
						內存款逕行提領轉帳繳付甲方(1)每期(月)應付之本金、利息、遲延利息、違約金等債務及(2)擔保物之保險費(3)下列約定之費用，乙方無需向甲方補徵取款憑條。
						</div>
						<div style='margin-top:2.5em;'>
						本貸款費用約定如下，甲方不得以提前清償或其他理由要求全部或部分退還本項費用：
						<table class='tb2'>
						<tr>
							<td class='noborder'>一、</td>
							<td class='noborder'>開辦手續費：<input type="text" id="eloan_pa_repay_feeNo01" name="eloan_pa_repay_feeNo01" size="8" />元</td>
						</tr>	   
						<tr>
							<td class='noborder'>二、</td>
							<td class='noborder'>信用查詢費：<input type="text" id="eloan_pa_repay_feeNo02" name="eloan_pa_repay_feeNo02" size="8" />元</td>
						</tr>
						<tr>
							<td class='noborder'>三、</td>
							<td class='noborder'>甲方如有就本貸款申請續約、變更授信條件【包括但不限調降利率、更換保證人(包含連帶保證人或一般保證人)或擔保品、展延寬限期或貸款期限等】、貸款餘額證明書或補發抵押權塗銷同意書，甲方並同意支付乙方續約作業費
							<input type="text" id="eloan_pa_repay_feeNo03" name="eloan_pa_repay_feeNo03" size="8" />元、變更授信條件手續費
							<input type="text" id="eloan_pa_repay_feeNo04" name="eloan_pa_repay_feeNo04" size="8" />元、證明書費用
							<input type="text" id="eloan_pa_repay_feeNo06" name="eloan_pa_repay_feeNo06" size="8" />元及補發費用
							<input type="text" id="eloan_pa_repay_feeNo07" name="eloan_pa_repay_feeNo07" size="8" />元。</td>
						</tr>
						</table>
						</div>
					</td>
				</tr>
				<tr>
					<td class='hd1'><th:block th:text="#{'column.intr'}">貸款計息方式 </th:block>
					</td>
					<td>
           				<table width='100%'>
           					<tr><!-- 因 IE, Firefox的寬度不同，這裡不指定第1個 td 的width，而是用 nowrap 去處理 -->
           						<td class='hd2' nowrap><label><input type='checkbox' id='eloan_pa_intr_withPPP_cb' name='eloan_pa_intr_withPPP_cb' value='Y'>限制提前清償<br/>&nbsp;　之利率計算方式</label>						
           						</td>       
								<td>	
			           				<table width='100%' class='tb2 alignTopTab'>
			           					<tr>
			           						<td class='noborder'>(一)
											</td>
			           						<td class='noborder'>
			           							<div>第<input type="text" id="eloan_pa_intr_withPPP_1x1" name="eloan_pa_intr_withPPP_1x1" class='number' size="2" />個月至第
													<input type="text" id="eloan_pa_intr_withPPP_1x2" name="eloan_pa_intr_withPPP_1x2" class='number' size="2" />個月按
													<input type="text" id="eloan_pa_intr_withPPP_1x3" name="eloan_pa_intr_withPPP_1x3" class='number' size="5" />%固定計息。
												</div>
												<div>第<input type="text" id="eloan_pa_intr_withPPP_1y1" name="eloan_pa_intr_withPPP_1y1" class='number' size="2" />個月至第
													<input type="text" id="eloan_pa_intr_withPPP_1y2" name="eloan_pa_intr_withPPP_1y2" class='number' size="2" />個月按乙方公告之消費金融放款指標利率加碼年息百分之
													<input type="text" id="eloan_pa_intr_withPPP_1y3" name="eloan_pa_intr_withPPP_1y3" class='number' size="5" />浮動計息。
												</div>
												<div>第<input type="text" id="eloan_pa_intr_withPPP_1y5" name="eloan_pa_intr_withPPP_1y5" class='number' size="2" />個月至第
													<input type="text" id="eloan_pa_intr_withPPP_1y6" name="eloan_pa_intr_withPPP_1y6" class='number' size="2" />個月按乙方公告之消費金融放款指標利率加碼年息百分之
													<input type="text" id="eloan_pa_intr_withPPP_1y7" name="eloan_pa_intr_withPPP_1y7" class='number' size="5" />浮動計息。
												</div>
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>&nbsp;
											</td>
			           						<td class='noborder'><div style='font-weight:bold;'>訂約時乙方消費金融放款指標利率為年利率	<input type="text" id="eloan_pa_intr_withPPP_baseRate" name="eloan_pa_intr_withPPP_baseRate" class='number' size="5" />%。
												</div>
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>(二)
											</td>
			           						<td class='noborder'>其他：
												<textarea id="eloan_pa_intr_withPPP_2t1" name="eloan_pa_intr_withPPP_2t1" style="width:600px;height:90px" >
												</textarea>	
											</td>
			           					</tr>
									</table>	
								</td>
           					</tr>
           					<tr>
           						<td class='hd2' nowrap><label><input type='checkbox' id='eloan_pa_intr_noPPP_cb' name='eloan_pa_intr_noPPP_cb' value='Y'>無限制清償期間<br/>&nbsp;　之利率計算方式</label>            							
           						</td>       
								<td>
			           				<table width='100%' class='tb2 alignTopTab'>
			           					<tr>
			           						<td class='noborder'>(一)
											</td>
			           						<td class='noborder'>按乙方公告之消費金融放款指標利率加碼年息百分之<input type="text" id="eloan_pa_intr_noPPP_1t1" name="eloan_pa_intr_noPPP_1t1" class='number' size="5" />計算，按日計息；嗣後隨乙方消費金融放款指標利率調整而調整，並自調整之日起按調整後之年利率計算。憑支票、金融卡、網路銀行或存摺與取款憑條動用，以當日透支、支用最高額計息；憑借款支用書動用，以當日最終借款餘額計息（適用於短期/中期循環借款）。
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>(二)
											</td>
			           						<td class='noborder'>按乙方撥款當日公告之消費金融放款指標利率加碼年息百分之<input type="text" id="eloan_pa_intr_noPPP_2t1" name="eloan_pa_intr_noPPP_2t1" class='number' size="5" />計算；嗣後隨乙方消費金融放款指標利率調整而調整，並於每屆滿一個月之日按當日調整後之年利率計算（適用於中、長期不循環借款採浮動利率者）。
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>(三)
											</td>
			           						<td class='noborder'>按乙方撥款當日公告之消費金融放款指標利率加碼年息百分之<input type="text" id="eloan_pa_intr_noPPP_3t1" name="eloan_pa_intr_noPPP_3t1" class='number' size="5" />計算；採固定利率（適用於中、長期借款採固定利率者）。
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>&nbsp;
											</td>
			           						<td class='noborder'><div style='font-weight:bold;'>訂約時乙方消費金融放款指標利率為年利率<input type="text" id="eloan_pa_intr_noPPP_baseRate" name="eloan_pa_intr_noPPP_baseRate" class='number' size="5" />%。
												</div>
											</td>
			           					</tr>
			           					<tr>
			           						<td class='noborder'>(四)
											</td>
			           						<td class='noborder'>其他：
												<textarea id="eloan_pa_intr_noPPP_4t1" name="eloan_pa_intr_noPPP_4t1" style="width:600px;height:90px" >
												</textarea>											
											</td>
			           					</tr>
									</table>
								</td>
           					</tr>
           					<tr>
           						<td class='hd2' nowrap><label><input type='checkbox' id='eloan_pa_intr_other_cb' name='eloan_pa_intr_other_cb' value='Y'>由甲方與乙方<br/>&nbsp;　個別約定：</label>				
           						</td>       
								<td>
									<textarea id="eloan_pa_intr_other_t1" name="eloan_pa_intr_other_t1" style="width:600px;height:60px" >
									</textarea>
								</td>
           					</tr>
						</table>
					</td>
				</tr>
           </table>
        </th:block>
    </body>
</html>