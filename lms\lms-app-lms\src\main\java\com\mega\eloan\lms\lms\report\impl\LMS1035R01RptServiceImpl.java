package com.mega.eloan.lms.lms.report.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;
import com.mega.eloan.lms.lms.panels.LMS1035S04PanelA;
import com.mega.eloan.lms.lms.report.LMS1035R01RptService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.Util;

@Service("lms1035r01rptservice")
public class LMS1035R01RptServiceImpl extends AbstractIISIReportService
		implements LMS1035R01RptService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1035R01RptServiceImpl.class);
	@Resource
	CLSService clsService;

	@Resource
	CodeTypeService codeTypeService;

	
	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		String default_empty_str = "- ";
		
		String oid = Util.trim(params.getString("oid"));
		String cntrNo = Util.trim(params.getString("cntrNo"));
		String loanTP = Util.trim(params.getString("loanTP"));
		C121M01A c121m01a = clsService.findC121M01A_oid(oid);
		C121S01A c121s01a = null;
		if (c121m01a != null) {
			try {
				// 若用 JSONObject.fromObject(...)，遇到有關連的 entity 時，會出錯
				reportData.setAll(DataParse.toJSON(c121m01a));
				
				reportData.setField("cntrNo", cntrNo);
				reportData.setField("loanTP", loanTP);
				reportData.setField("varVer", Util.trim(c121m01a.getVarVer()));
				reportData.setField("mowTypeCountry", Util.trim(c121m01a.getMowTypeCountry()));
			    
				c121s01a = clsService.findC121S01A(c121m01a);
				if (c121s01a != null) {
					reportData.setAll(DataParse.toJSON(c121s01a));

					CodeTypeFormatter cmsType = new CodeTypeFormatter(
							codeTypeService, "c121s01a_cmsType_TH");
					
					CodeTypeFormatter houseAge = new CodeTypeFormatter(
							codeTypeService, "c121s01a_houseAge");

					reportData.setField("cmsType",
							cmsType.reformat(c121s01a.getCmsType()));
					if(true){
						String s = "";
						if(c121s01a.getSecurityRate()==null){
							
						}else{
							s = LMSUtil.pretty_numStr(c121s01a.getSecurityRate())+"%";
						}
						reportData.setField("securityRate", s);
					}
					//location 在上面的 reportData.setAll(DataParse.toJSON(c121s01a)); 填入
					
					if(true){
						if(Util.isEmpty(Util.trim(c121s01a.getLocation()))){
							reportData.setField("location", default_empty_str);	
						}
					}
					if(true){
						String str_houseAge = houseAge.reformat(c121s01a.getHouseAge());
						if(Util.isEmpty(Util.trim(str_houseAge))){
							str_houseAge = default_empty_str;
						}
						reportData.setField("houseAge", str_houseAge);	
					}
					if(true){
						if(c121s01a.getHouseArea()==null){
							reportData.setField("houseArea", default_empty_str);
						}
					}					
				}
				
				if(true){
					Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1035S04PanelA.class);
					reportData.setField("text1_5", build_html_item(prop.getProperty("tab04.factor1.5.a"), prop.getProperty("tab04.factor1.5.b")));
					reportData.setField("text1_4", build_html_item(prop.getProperty("tab04.factor1.4.a"), prop.getProperty("tab04.factor1.4.b")));
					reportData.setField("text1_3", build_html_item(prop.getProperty("tab04.factor1.3.a"), ""));					
					reportData.setField("text1_2", build_html_item(prop.getProperty("tab04.factor1.2.a"), prop.getProperty("tab04.factor1.2.b")));
					reportData.setField("text1_1", build_html_item(prop.getProperty("tab04.factor1.1.a"), prop.getProperty("tab04.factor1.1.b")));					
					
					reportData.setField("text2_4", build_html_item(prop.getProperty("tab04.factor2.4.a"), prop.getProperty("tab04.factor2.4.b")));
					reportData.setField("text2_3", build_html_item(prop.getProperty("tab04.factor2.3.a"), prop.getProperty("tab04.factor2.3.b")));			
					reportData.setField("text2_2", build_html_item(prop.getProperty("tab04.factor2.2.a"), prop.getProperty("tab04.factor2.2.b")));
					reportData.setField("text2_1", build_html_item(prop.getProperty("tab04.factor2.1.a"), prop.getProperty("tab04.factor2.1.b")));
				}
			} catch (Exception e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}

		}
		return reportData;
	}

	private String build_html_item(String s_a, String s_b){
		StringBuffer sb  = new StringBuffer();
		sb.append("<table border='0' width='97%'>");
		if(Util.isNotEmpty(s_a)){
			sb.append(build_html_item_tr_td(s_a));
		}
		if(Util.isNotEmpty(s_b)){
			sb.append(build_html_item_tr_td(s_b));
		}		
		sb.append("</table>");
		return sb.toString();
	}
	
	private String build_html_item_tr_td(String s){
		StringBuffer sb  = new StringBuffer();
		//TODO 在字體 SansSerif 時，看不到◎   ⇒ 要改 '細明體'
		sb.append("<tr>");
		sb.append("<td style='width:16px;vertical-align:top;'>").append("◎").append("</td>");
		sb.append("<td align='left' style='padding-bottom:4px;'>").append(s).append("</td>");
		sb.append("</tr>");
		return sb.toString();
	}
	
	@Override
	public Engine getSubReportData(PageParameters params, Engine engine) {
		String oid = params.getString("oid");
		C121M01A c121m01a = clsService.findC121M01A_oid(oid);
		if (c121m01a != null) {
			try {
				Engine srp = engine.getSubReport(0);
				Engine srpH = engine.getSubReport(1);
				if (true) {
					List<C120M01A> list = clsService
							.filter_shouldRating(clsService
									.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(c121m01a));
					
					Properties prop_lms1035m01Page = MessageBundleScriptCreator
							.getComponentResource(LMS1035M01Page.class);

					Map<String, String> custPosMap = codeTypeService
							.findByCodeType("lms1015_custPos");
					List<String[]> detailList1 = new ArrayList<String[]>();
					for (C120M01A c120m01a : list) {
						C120S01A c120s01a = clsService.findC120S01A(c120m01a);
						
						C121M01D c121m01d = clsService
								.findC121M01D_byC120M01A(c120m01a);						
						// ====================
						String col_01 = LMSUtil.getDesc(custPosMap, Util.equals("Y",c120m01a.getKeyMan()) ? OverSeaUtil.M: Util.trim(c120m01a.getCustPos()));
						String col_02 = Util.trim(c120m01a.getCustId())+"-"+Util.trim(c120m01a.getDupNo());
						String col_03 = Util.trim(c120m01a.getCustName());
						String col_04 = (c120s01a==null?"":Util.trim(c120s01a.getO_grade()));
						String col_05 = Util.trim(c121m01d.getChkItemTHO2());
						String col_06 = Util.trim(c121m01d.getAdjustReason());
						String col_07 = Util.trim(c121m01d.getPRating());
						String col_08 = Util.trim(c121m01d.getSRating());
						String col_09 = Util.trim(c121m01d.getFRating());
						String col_10 = StringUtils.join(OverSeaUtil.build_chkItemInfoNcb(prop_lms1035m01Page, c121m01d), "<br/>");
						
						String varVer = Util.trim(c121m01a.getVarVer());
						String col_11 = "";
						String col_12 = "";
						String col_13 = "";
						
						C121M01H c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
						if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
							if(c121m01h != null){
								col_11 = Util.trim(c121m01h.getPRating());
								col_12 = Util.trim(c121m01h.getSRating());
								col_13 = Util.trim(c121m01h.getFRating());
							}
						}
						
						detailList1.add(new String[] { col_01, col_02, col_03,
								col_04, col_05, col_06, col_07, col_08, col_09, col_10,
								col_11, col_12, col_13 });
					}

					String[] columns1 = { "CommonBean1.field01",
							"CommonBean1.field02", "CommonBean1.field03",
							"CommonBean1.field04", "CommonBean1.field05",
							"CommonBean1.field06", "CommonBean1.field07",
							"CommonBean1.field08", "CommonBean1.field09",
							"CommonBean1.field10", "CommonBean1.field11",
							"CommonBean1.field12", "CommonBean1.field13"};
					int col_cnt = columns1.length;

					// 建立所有欄位的資料，不足筆數放空白
					String[][] data1 = new String[detailList1.size()][col_cnt];
					for (int i = 0; i < detailList1.size(); i++) {
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = "";
						}
					}
					for (int i = 0; i < detailList1.size(); i++) {
						String[] cb1List = detailList1.get(i);
						for (int j = 0; j < col_cnt; j++) {
							data1[i][j] = cb1List[j];
						}
					}
					srp.setData(columns1, data1);
					srpH.setData(columns1, data1);
				}

			} catch (ReportException e) {
				LOGGER.error(StrUtils.getStackTrace(e));
			}
		}
		return engine;
	}
	
	@Override
	public String getReportDefinition() {
		return "report/lms/LMS1035R01";
	}
}
