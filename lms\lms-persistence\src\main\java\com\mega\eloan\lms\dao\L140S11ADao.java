/* 
 * L140S11ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Shen<PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S11A;

/** 敘做條件異動分項 **/
public interface L140S11ADao extends IGenericDao<L140S11A> {

	L140S11A findByOid(String oid);
	
	List<L140S11A> findByMainId(String mainId);

	L140S11A findMaxSeqNumByMainId(String mainId);
}