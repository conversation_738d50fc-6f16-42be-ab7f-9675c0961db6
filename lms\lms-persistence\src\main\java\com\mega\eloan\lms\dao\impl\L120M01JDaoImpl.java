/* 
 * L120M01JDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120M01JDao;
import com.mega.eloan.lms.model.L120M01J;

/** 應收帳款買方額度資訊主檔 **/
@Repository
public class L120M01JDaoImpl extends LMSJpaDao<L120M01J, String> implements
		L120M01JDao {

	@Override
	public L120M01J findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01J> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L120M01J> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L120M01J> findByMainIdAndType(String mainId, String type) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L120M01J> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L120M01J findByMainIdTypeCustIdCntrNo(String mainId, String type,
			String custId, String dupNo, String custId2, String dupNo2,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2",
					custId2);
		if (dupNo2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public L120M01J findByIndex01(String mainId, String type, String custId,
			String dupNo, String custId2, String dupNo2, String cntrNo) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (custId2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2",
					custId2);
		if (dupNo2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo2);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120M01J> findByIndex02(String mainId) {
		ISearch search = createSearchTemplete();
		List<L120M01J> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01J> findByIndex03(String mainId, String type) {
		ISearch search = createSearchTemplete();
		List<L120M01J> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L120M01J> findByMainIdTypeCustId(String mainId, String type,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120M01J> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L120M01J findByMainIdTypeCustIdItemSeq(String mainId, String type,
			String custId, String dupNo, Integer itemSeq) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (itemSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq",
					itemSeq);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L120M01J> findByMainIdTypeCustId2(String mainId, String type,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L120M01J> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId2", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo2", dupNo);

		search.addOrderBy("type", false);
		search.addOrderBy("printSeq", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

}