/* 
 * DWELF490ovsService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dw.service;

import java.util.List;

public interface DWELF490ovsService {

	List<?> findELF490ovsSelNewRule(String dataYm,String branch);
	
	List<?> findELF490ovsSelOldRule(String dataYm,String branch);
}
