/* 
 * L260M01CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01C;

/** 貸後管理控制檔 **/
//
public interface L260M01CDao extends IGenericDao<L260M01C> {

	L260M01C findByOid(String oid);

	List<L260M01C> findByMainId(String mainId, boolean notIncDel);

	List<L260M01C> findByMainIdAndNos(String mainId, String cntrNo,
									  String loanNo, boolean notIncDel, boolean incEmptyLoanNo);

    List<L260M01A> findPageByFilter(StringBuilder condition, List<Object> paramValues, int firstRow,int maxRow);
}