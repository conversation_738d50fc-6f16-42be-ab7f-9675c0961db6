/* 
 * CLSCommomPage.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.base.pages.AbstractCapPage;

/**
 * <pre>
 * 個金共用功能頁面
 * </pre>
 * 
 * @since 2013/1/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/7,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/commompage")
public class CLSCommomPage extends AbstractCapPage {

	private static final long serialVersionUID = -4024257163623646201L;

	// UPGRADE: 待確認是否需要ViewName
	@Override
	protected String getViewName() {
		return null;
	}

	@Override
	protected void execute(ModelMap model, PageParameters params)
			throws Exception {
	}

}
