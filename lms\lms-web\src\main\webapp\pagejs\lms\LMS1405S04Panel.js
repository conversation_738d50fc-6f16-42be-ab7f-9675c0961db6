var init120m01j = {
	fhandle : "lms1205formhandler",
	ghandle : "lms1205gridhandler",
	fhandle140 : "lms1405m01formhandler",
	ghandle140 : "lms1405gridhandler",
	defButton : {
		"close" : function() {
			$.thickbox.close();
		}
	}
};

var initDfd = initDfd || $.Deferred();
initDfd.done(function(auth) {

	// ===================Grid Code=============================
	/** 主表grid */

	$("#gridviewCustARTotal").iGrid({
		handler : init120m01j.ghandle,
        height: 170,
        needPager: false,
        rownumbers: false,
        multiselect: true,
        hideMultiselect: false,
        sortname: 'printSeq',
        sortorder: 'asc',
        shrinkToFit : true,
        postData: {
            formAction: "queryL120m01jCustTotal",
            mainId : responseJSON.mainId,
            type : "A"
        },
        autowidth: false,
        colModel : [{
			colHeader : i18n.lms1405s04["L120M01J.custId"], //申貸戶統一編號
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			formatter : 'click',
			onclick : L120m01jAPI.opendocBox,
			name : 'custId' //col.id
		},{
			colHeader : i18n.lms1405s04["L120M01J.dupNo"], //申貸戶重覆序號
			align : "center",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'dupNo' //col.id
		},{
			colHeader : i18n.lms1405s04["L120M01J.custName"], //申貸戶客戶名稱
			align : "left",
			width : 400, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		},{
			colHeader : i18n.lms1405s04["L120M01J.factorAmtNt"], //調整後實際承購金額合計-TWD
			align : "left",
			width : 150, //設定寬度
			sortable : true, //是否允許排序
			align: "right",
            formatter: GridFormatter.number['addComma'],
			name : 'factorAmtNt' //col.id				
		
		},{
			colHeader : i18n.lms1405s04["L120M01J.dataDate"], //資料日期
			align : "left",
			width : 100, //設定寬度
			//sortable : true, //是否允許排序
			//formatter : 'click',
			name : 'dataDate' //col.id		
		},{
			colHeader : "printSeq", //列印順序
			align : "right",
			width : 5, //設定寬度
			hidden : true, //是否隱藏
			name : 'printSeq'  
		},{
			colHeader : i18n.lms1405s04["L120M01J.type"], //類型
			align : "left",
			width : 5, //設定寬度
			hidden : true, //是否隱藏
			name : 'type' //col.id					
		}],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        	var data = $(L120m01jAPI.mainGridId).getRowData(rowid);
        	L120m01jAPI.opendocBox(null, null, data);
        }
	});

	L120m01jAPI.gridviewitemChildrenA('A');

	// button
	$("#applyARAppendixA").click(function() {
		L120m01jAPI.applyARAppendixA('A');
	});
	 

});

// 額度明細表內程式
var L120m01jAPI = {

	mainGridId : "#gridviewCustARTotal",
	/**
	 * 觸發主檔Grid更新
	 */
	_triggerMainGrid : function() {
		$(L120m01jAPI.mainGridId).trigger('reloadGrid');
	},
	/**
	 * 引進
	 */
	applyARAppendixA : function(type) {
		
		var count=$("#gridviewCustARTotal").jqGrid('getGridParam','records');
    	if(count > 0){
    		//L120M01J.message001=執行引進後會刪除已存在之名單，是否確定執行？
    		CommonAPI.confirmMessage(i18n.lms1405s04["L120M01J.message001"], function(b){
				if (b) {					
					//是的function
					$.ajax({
		    			handler : init120m01j.fhandle,
		    			type : "POST",
		    			dataType : "json",
		    			action : "applyARAppendixA",
		    			data : {
		    				mainId : responseJSON.mainId,
		    				type : type }
		    			}).done(function(obj) {
		    				
		    				$("#gridviewCustARTotal").trigger("reloadGrid");

		    				 
		    		});	
				}				
			});		
    	}else{
    		$.ajax({
    			handler : init120m01j.fhandle,
    			type : "POST",
    			dataType : "json",
    			action : "applyARAppendixA",
    			data : {
    				mainId : responseJSON.mainId,
    				type : type }
    			}).done(function(obj) {
    				
    				$("#gridviewCustARTotal").trigger("reloadGrid");

    				 
    		});	
    	}
		
	},
	/**
	 * 引進
	 */
	gridviewitemChildrenA : function(type) {
		$("#gridviewitemChildrenA").iGrid({
        	handler: init120m01j.ghandle,
            height: 170,
            needPager: false,
            rownumbers: false,
            multiselect: true,
            hideMultiselect: false,
            sortname: 'itemSeq',
            sortorder: 'asc',
            shrinkToFit : false,
            localFirst: true,
            postData: {
                formAction: "queryL120m01jByCustId",
                mainId : responseJSON.mainId,
				type : type
            },
            autowidth: true,
            colModel : [{
				colHeader : i18n.lms1405s04["L120M01J.itemSeq"], //序號
				align : "center",
				width : 50, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'itemSeq' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.custId2"], //賣方統一編號
				align : "left",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custId2' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.dupNo2"], //賣方重覆序號
				align : "center",
				width : 40, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'dupNo2' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.custName2"], //賣方客戶名稱
				align : "left",
				width : 200, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'custName2' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.cntrNo"], //賣方承購額度序號
				align : "center",
				width : 100, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'cntrNo' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.factorCurr"], //承購幣別
				align : "center",
				width : 40, //設定寬度
				sortable : true, //是否允許排序
				//formatter : 'click',
				//onclick : function,
				name : 'factorCurr' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.factorAmt"], //實際承購金額-原幣
				align : "left",
				width : 150, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factorAmt' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.factorAdjAmt"], //調整後實際承購金額-原幣
				align : "left",
				width : 150, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factorAdjAmt' //col.id		
			},{
				colHeader : i18n.lms1405s04["L120M01J.factorAmtNt"], //調整後實際承購金額-TWD
				align : "left",
				width : 150, //設定寬度
				sortable : true, //是否允許排序
				align: "right",
                formatter: GridFormatter.number['addComma'],
				name : 'factorAmtNt' //col.id		
            },{
				colHeader : "oid",
				name : 'oid',
				hidden : true //是否隱藏
			},{
				colHeader : i18n.lms1405s04["L120M01J.mainId"], //文件編號
				align : "left",
				width : 100, //設定寬度
//				sortable : true, //是否允許排序
				hidden : true, //是否隱藏
				name : 'mainId' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.type"], //類型
				align : "left",
				width : 10, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				hidden : true, //是否隱藏
				name : 'type' //col.id
			},{
				colHeader : i18n.lms1405s04["L120M01J.dataDate"], //資料日期
				align : "left",
				width : 10, //設定寬度
				//sortable : true, //是否允許排序
				//formatter : 'click',
				hidden : true, //是否隱藏
				name : 'dataDate' //col.id			
			}],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridviewitemChildrenA").getRowData(rowid);
                opendocBox(null, null, data);
            },
            loadComplete: function(){
            		        	  	
            }    
        });
	},
	/** 主檔 */
	opendocBox : function(type, docOid, data) {

		var buttons = {};
		
		var custId = data.custId;
		var dupNo = data.dupNo;
		var custName = data.custName;
		var type = data.type;
		
		$("#gridviewitemChildrenA").jqGrid("setGridParam", {// 重新設定grid需要查到的資料
			postData : {
				formAction: "queryL120m01jByCustId",
                mainId : responseJSON.mainId,
                type : type,
                custId : custId,
                dupNo : dupNo
			},
			search : true
		}).trigger("reloadGrid");

		buttons["cancel"] = function() {
			$.thickbox.close();
		};

	
        $.ajax({
    		handler : init120m01j.fhandle,
    		type : "POST",
    		dataType : "json",
    		action : "getARAppendixRefRate",
    		data : {
    			mainId : responseJSON.mainId,
    			type:type,
                custId : custId,
                dupNo : dupNo }
    		}).done(function(obj) {
    			$("#detailL120m0jBox").find("#dataDateA").val(obj.dataDate);
    			$("#detailL120m0jBox").find("#refRateA").val(obj.refRate);
    			
    			
    			$("#detailL120m0jBox").thickbox({
    				// L120M01J.title01=本案借款人同時為其他授信戶應收帳款債務人之額度資料
    				title : custId+dupNo+" "+custName,
    				width : 990,
    				height : 380,
    				modal : true,
    				readOnly : thickboxOptions.readOnly,
    				align : "center",
    				i18n : i18n.def,
    				valign : "bottom",
    				buttons : buttons
    			});
    			
    			 
    	});	
        

		

	}

};