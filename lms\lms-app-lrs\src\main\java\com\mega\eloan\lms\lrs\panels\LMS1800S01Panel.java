
package com.mega.eloan.lms.lrs.panels;

import com.mega.eloan.common.panels.Panel;
import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.DocLogPanel;


public class LMS1800S01Panel extends Panel {
	private boolean edt;
	
	private static final long serialVersionUID = 1L;
	
	public LMS1800S01Panel(String id) {
		super(id);
	}
	
	public LMS1800S01Panel(String id, boolean updatePanelName, boolean edt) {
		super(id, updatePanelName);
		this.edt = edt;
	}
	
	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		new DocLogPanel("_docLog").processPanelData(model, params);
		model.addAttribute("editY", edt);
		model.addAttribute("editN", !edt);	
	}

}
