package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisLMS338NService {

	/**
	 * 信用評等資料 (需crdtype參數,再找日期最新的一筆)
	 * 
	 * @param custId
	 * @param dupNo
	 * @param ovUnitNo
	 * @param crdtype1
	 * @param crdtype2
	 * @param crdtype3
	 * @return
	 */
	public List<Map<String, Object>> findLMS338NByCustId(String custId,
			String dupNo, String ovUnitNo, String type1, String type2,
			String type3, String type4, String type5, String type6);

	/**
	 * 找評等等級 By 信評表類別
	 * 
	 * @param custId
	 * @param dupNo
	 * @param crdtype
	 * @return
	 */
	public Map<?, ?> findGradeByCrdtype(String custId, String dupNo,
			String crdtype, String brNo);

	/**
	 * 信評表類別
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	public List<?> findCrdtypeByCustId(String custId, String dupNo, String brNo);

	/**
	 * J-114-XXX1 LGD合格保證人納入境外保證人
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findCrdtypeByCustIdForLgdGuarantor(
			String custId, String dupNo);

}
