class MegaImageUploadAdapter {
    constructor( loader ) {
        // The file loader instance to use during the upload.
        this.loader = loader;
    }

    // Starts the upload process.
    upload() {
        return this.loader.file
            .then( file => new Promise( ( resolve, reject ) => {
                this._initRequest();
                this._initListeners( resolve, reject, file );
                this._sendRequest( file );
            } ) );
    }

    // Aborts the upload process.
    abort() {
        if ( this.xhr ) {
            this.xhr.abort();
        }
    }

    // Initializes the XMLHttpRequest object using the URL passed to the constructor.
    _initRequest() {
        const xhr = this.xhr = new XMLHttpRequest();

        // Note that your request may look different. It is up to you and your editor
        // integration to choose the right communication channel. This example uses
        // a POST request with JSON as a data structure but your configuration
        // could be different.
        let [url, hashtag] = window.location.href.split('#');
        url += '?_pa=' + Properties.ckFileUploadHandler;
        xhr.open( 'POST', hashtag != undefined ? url + '#' + hashtag : url, true );
        xhr.responseType = 'json';
    }

    // Initializes XMLHttpRequest listeners.
    _initListeners( resolve, reject, file ) {
        const xhr = this.xhr;
        const loader = this.loader;
        const genericErrorText = `Couldn't upload file: ${ file.name }.`;

        xhr.addEventListener( 'error', () => reject( genericErrorText ) );
        xhr.addEventListener( 'abort', () => reject() );
        xhr.addEventListener( 'load', () => {
            const response = xhr.response;

            // This example assumes the XHR server's "response" object will come with
            // an "error" which has its own "message" that can be passed to reject()
            // in the upload promise.
            //
            // Your integration may handle upload errors in a different way so make sure
            // it is done properly. The reject() function must be called when the upload fails.
            if ( !response || response.error ) {
                return reject( response && response.error ? response.error.message : genericErrorText );
            }

            // If the upload is successful, resolve the upload promise with an object containing
            // at least the "default" URL, pointing to the image on the server.
            // This URL will be used to display the image in the content. Learn more in the
            // UploadAdapter#upload documentation.
            resolve( {
                default: response.url
            } );
        } );

        // Upload progress when it is supported. The file loader has the #uploadTotal and #uploaded
        // properties which are used e.g. to display the upload progress bar in the editor
        // user interface.
        if ( xhr.upload ) {
            xhr.upload.addEventListener( 'progress', evt => {
                if ( evt.lengthComputable ) {
                    loader.uploadTotal = evt.total;
                    loader.uploaded = evt.loaded;
                }
            } );
        }
    }

    // Prepares the data and sends the request.
    _sendRequest( file ) {
        // Prepare the form data.
        var uploadDatas = $("#mainOid,#mainId,#crYear,#oid");
        const data = new FormData();
        data.append( '_pa', Properties.ckFileUploadHandler );
        data.append( 'fileCheck', [".jpg|.png|.gif"] ),
        data.append( 'fieldId', "upload" ),
        data.append( 'getImgDimension', true),
        data.append( 'oid', uploadDatas.filter("#oid").val()), // for mega eloan
        data.append( 'mainId', uploadDatas.filter("#mainId").val())// , // for mega eloan
        data.append( 'upload', file );

        // Important note: This is the right place to implement security mechanisms
        // like authentication and CSRF protection. For instance, you can use
        // XMLHttpRequest.setRequestHeader() to set the request headers containing
        // the CSRF token generated earlier by your application.
        this.xhr.setRequestHeader('X-CSRF-TOKEN', DOMPurify.sanitize($("meta[name='_csrf']").attr("content")));
        
        // Send the request.
        this.xhr.send( data );
    }
}
function insertImagePlugin(editor) {
    editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
        return new MegaImageUploadAdapter(loader);
    };
}

// ckeditor dialog config
// ClassicEditor.on('dialogDefinition', function(ev){
// var dialogName = ev.data.name;
// var dialogDefinition = ev.data.definition;
//    
// if (dialogName == 'link') {
// dialogDefinition.removeContents('advanced');
// dialogDefinition.removeContents('target');
// }
// if (dialogName == 'image') {
//    
// dialogDefinition.removeContents('advanced');
// // dialogDefinition.removeContents('info');
// dialogDefinition.removeContents('Link');
//        
// }
// // ilog.debug(dialogDefinition)
// });

function isDOM(obj){
	if( typeof HTMLElement === 'object' ){
		return obj instanceof HTMLElement;
	}else{
		return obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string';
	}
}

function removeSelfAndChildStyle(obj){
	// 要移除的font
	var rmTagArr = ['font'];
	$obj = $(obj);
	for(var a = 0; a < rmTagArr.length; a++){
		if($obj.parent().is(rmTagArr[a])){
			$obj.unwrap();
			break;
		}
	}
	// 要移除的css
	var rmAttrArr = ['font-size','font-family','color','background-color'];
	for(var b = 0; b < rmAttrArr.length; b++){
		if(obj.style[rmAttrArr[b]] !== undefined){
			obj.style[rmAttrArr[b]] = '';
		}
	}
	// 找到自己的兒子們
	var allChilds = obj.childNodes;
	for(var j = 0; j < allChilds.length; j++) {
		// 若兒子也是DOM物件跑遞迴把所有向下DOM物件拔掉css、font
		if(isDOM(allChilds[j])){
			removeSelfAndChildStyle(allChilds[j]);
		}
	}
}

