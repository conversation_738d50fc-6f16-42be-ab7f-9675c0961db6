/* 
 * LMS1415V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.Arrays;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel02;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 聯行額度明細表
 * </pre>
 * 
 * @since 2011/12/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/1,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1415v01")
public class LMS1415V01Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CreditDocStatusEnum.海外_編製中);

		if (this.getAuth(AuthType.Accept)) {
			addToButtonPanel(model, Arrays.asList(LmsButtonEnum.View, LmsButtonEnum.Filter));
		} else {
			addToButtonPanel(model,
					Arrays.asList(LmsButtonEnum.View, LmsButtonEnum.Delete, LmsButtonEnum.Filter));
		}
		// 加上Button
		renderJsI18N(LMS1415M01Page.class);
		renderJsI18N(LMS1415V01Page.class);
		
		setupIPanel(new GridViewFilterPanel02(PANEL_ID), model, params);
	}

}
