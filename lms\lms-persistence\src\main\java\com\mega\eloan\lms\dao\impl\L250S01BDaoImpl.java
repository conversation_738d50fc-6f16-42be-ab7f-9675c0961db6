package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;

import com.mega.eloan.lms.dao.L250S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L250S01B;

@Repository
public class L250S01BDaoImpl extends LMSJpaDao<L250S01B, String> implements
		L250S01BDao {

	@Override
	public List<L250S01B> getAll() {

		ISearch search = createSearchTemplete();
		search.addOrderBy("type");
		search.addOrderBy("groupOrder");
		search.addOrderBy("subOrder");
		//若不加 setMaxResults(Integer.MAX_VALUE) 只會抓到100筆 => 有缺漏
		search.setMaxResults(Integer.MAX_VALUE);
		return find(search);

	}

}