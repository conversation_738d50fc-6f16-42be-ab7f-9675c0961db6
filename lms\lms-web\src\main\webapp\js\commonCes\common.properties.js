/* 專案JS設定檔    */
var _docReadonly=0,_openerLockDoc=0;
var Properties = {
	window : {
		closeConfirm : false,
		isChangeTab : false,
		onunload : function(event) {

			try{
				event.stopPropagation();
			} catch(e) {
				// 有的 event 沒有 stopPropagation() 
			}
			
			if((this == window || Properties.window.source == 'window') && !Properties.window.isChangeTab){
				var _mainId;
				try{
					_mainId = $("#mainId").val() || responseJSON && responseJSON.mainId;
				}catch(e){}
				var closePageAsync = false; // 預設為ClosePage為async:false (IE、firefox)
				if (navigator.userAgent.search("MSIE") >= 0) {
					closePageAsync = false;// 若為IE瀏覽器維持flase
				} else if (navigator.userAgent.search("Edg") >= 0) {
					closePageAsync = true;// 若為Edge瀏覽器改為true
				}
		        (_mainId) &&
		        $.ajax({
		            handler: 'checkOpenerhandler',
					global:false,
		            async: closePageAsync,
		            type: 'post',
		            data: {
		            	checkOpenerAction: 'ClosePage',
		                mainId: _mainId
		            }
		        });
			}
		}
	},
	ajaxTimeOut : 60 * 1000 * 3,
	fileUploadTimeOut : 5 * 60 * 1000,
	// 下拉選單handler
	ComboBoxHandler : 'codetypehandler',
	Grid : {
		rowNum : 30,
		rowList : []
	},
	innerPageFrameId : "cap-frame",
	enter2tab : false,
	
	// eloan2
	// ckeditor
	ckFileUploadHandler : 'Simplefileuploadhandler' ,
	logoutURL :'/app/logout'
	//localSaveRowCount : 3
};

//for EDGE 關閉視窗Listener
if (navigator.userAgent.search("Edg") >= 0) {
  if (window.addEventListener) {

    // 監聽pagehide事件取代unload事件
    window.addEventListener("pagehide", function(event) {
      var x = 200;
      var a = (new Date()).getTime() + x;

      Properties.window.source = 'window';
      Properties.window.closeConfirm = false;
      Properties.window.onunload();

      // 避免還沒執行到就關掉頁面了
      while ((new Date()).getTime() < a) {
      }
    }

    )
  }
}
