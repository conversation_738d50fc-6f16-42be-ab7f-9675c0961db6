<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<util:map id="odsSql" map-class="java.util.HashMap" key-type="java.lang.String">

		<!-- MEGAVSAM.TCSFODS : 確認ODS資料同步狀況 -->
		<!-- TCSFODS_STAN_01(年/月/日/時/分/秒) 4/2/2/2/2/2
			 TCSFODS_KEY = '01' => TCSFODS_FLAG1 : #空白，正常營運中	#E 值，ERROR
		     TCSFODS_KEY = '03' => TCSFODS_FLAG1 : #Y 值，主機批次中	#N 值，正常營運中 -->
		<entry key="getODS_Status">
			<value>
				SELECT * FROM MEGAVSAM.TCSFODS WHERE TCSFODS_KEY='01'
			</value>
		</entry>

		<!-- V_P430_3_1PB : 歸戶活存資料-'1PB'、排除警示戶
			 V_P430_3_2CK : 歸戶支存資料-'2CK'、排除警示戶
			 V_P430_3_3CT : 歸戶定存帳號資料-'3CT'
			 V_P430_3_PBCT : 取得CUT TIME 後自動化交易活存轉無單定存-PBFTRCT
			 V_ELOAN_ACCTNO : 虛/實帳號對應
			 MEGAVSAM.LNCURR : 分行放款幣別代號名稱檔
			 MEGAVSAM.CMRETIRE : 退休行員檔

			 不可以查0060 ==>
				AP_CODE :
			 		00 -> 錯誤的AP-CODE
			 		02 -> 行員 [但退休行員可以查]
			 		03、04、16、18 -> 外幣的實帳號AP-CODE，如要查外幣，應該輸入虛帳號
			 		76.77 -> 8OI 													-->
		<entry key="findODS_0320_ById">
			<value>
				SELECT list.*, CURRMAP.LNCU_CURR_CODE AS CURR_CODE,
						CASE WHEN actTrans.REAL_ACT_NO IS NULL THEN list.TP44_ACT_NO ELSE actTrans.REAL_ACT_NO END AS REAL_ACT_NO,
						CASE WHEN SUBSTR(list.TP44_ACT_NO, 4, 2) = '02' AND RE.CMRETIRE_ID_NO IS NOT NULL THEN ''
								WHEN SUBSTR(list.TP44_ACT_NO, 4, 2) IN ('00','02','03','04','16','18','76','77') THEN 'N'
								WHEN SUBSTR(list.TP44_ACT_NO, 4, 2) IN ('53','54','57','58') AND CURRMAP.LNCU_CURR_CODE = '00' THEN 'N' ELSE '' END AS Q0060FLAG
				FROM (
					SELECT TCIA_CUST_ID_NO, TCIA_CUST_DUP_NO, TP44_ACT_NO, TP44_CURR FROM DWADM.V_P430_3_1PB WHERE TCIA_CUST_ID_NO = ? AND TCIA_CUST_DUP_NO = ?
					UNION
					SELECT TCIA_CUST_ID_NO, TCIA_CUST_DUP_NO, TP44_ACT_NO, TP44_CURR FROM DWADM.V_P430_3_2CK WHERE TCIA_CUST_ID_NO = ? AND TCIA_CUST_DUP_NO = ?
					UNION
					SELECT TCIA_CUST_ID_NO, TCIA_CUST_DUP_NO, TP44_ACT_NO, TP44_CURR FROM DWADM.V_P430_3_3CT WHERE TCIA_CUST_ID_NO = ? AND TCIA_CUST_DUP_NO = ?
					UNION
					SELECT TCIA_CUST_ID_NO, TCIA_CUST_DUP_NO, TP44_ACT_NO, TP44_CURR FROM DWADM.V_P430_3_PBCT WHERE TCIA_CUST_ID_NO = ? AND TCIA_CUST_DUP_NO = ?
				) list
				LEFT JOIN MEGAVSAM.LNCURR CURRMAP ON list.TP44_CURR = CURRMAP.LNCU_SWFT_CODE
				LEFT JOIN (SELECT * FROM MEGAVSAM.CMRETIRE WHERE CMRETIRE_ID_NO = ? AND CMRETIRE_ID_DUP_NO = ?) RE ON list.TCIA_CUST_ID_NO = RE.CMRETIRE_ID_NO AND list.TCIA_CUST_DUP_NO = RE.CMRETIRE_ID_DUP_NO
				LEFT JOIN (SELECT * FROM DWADM.V_ELOAN_ACCTNO WHERE ID = ? AND DUPNO = ?) actTrans ON list.TP44_ACT_NO = actTrans.FAKE_ACT_NO AND list.TP44_CURR = actTrans.LNCU_SWFT_CODE
				ORDER BY list.TP44_ACT_NO, list.TP44_CURR
			</value>
		</entry>

		<entry key="findODS_0060_TXN">
			<value>
				SELECT *,
					CASE WHEN LEAD(CAST(TCSTXN_DATE AS Varchar),1,0) OVER (PARTITION BY TCSTXN_DATE
										ORDER BY TCSTXN_DATE, TCIA_TIME_HH, TCIA_TIME_MM, TCIA_TIME_SS) = '0'
						 THEN 'Y' ELSE NULL END AS TODAY_BAL
				FROM DWADM.V_BTT0060_TCSTXN
				WHERE TCSTXN_ACTNO = ?
				ORDER BY TCSTXN_DATE, TCIA_TIME_HH, TCIA_TIME_MM, TCIA_TIME_SS
			</value>
		</entry>

		<entry key="findODS_0060_HIST">
			<value>
				SELECT *,
					CASE WHEN LEAD(CAST(SHIST_PB_CLOCK_DATE_1 AS Varchar),1,0) OVER (PARTITION BY SHIST_PB_CLOCK_DATE_1
										ORDER BY SHIST_PB_CLOCK_DATE_1, SHIST_PB_CLOCK_TIME, SHIST_PB_WATER_NO) = '0'
						 THEN BTT_0060_TXN_AMT_BAL ELSE NULL END AS TODAY_BAL
				FROM DWADM.V_BTT0060_PBHIST
				WHERE BTT_0060_PV_AC_NO = ? AND BTT_0060_CURR_CODE = ? AND BTT_0060_TXN_DATE BETWEEN ? AND ?
				ORDER BY SHIST_PB_CLOCK_DATE_1, SHIST_PB_CLOCK_TIME, SHIST_PB_WATER_NO
			</value>
		</entry>

		<entry key="findODS_8250_I">
			<value>
				SELECT *
				FROM DWADM.V_BTT8250_I
				WHERE AP85_DATE BETWEEN ? AND ? AND IDNO = ? AND DUPNO = ? {0}
				ORDER BY AP85_DATE, AP85_TIME, AP85_RMNO
			</value>
		</entry>

		<entry key="findODS_8250_O">
			<value>
				SELECT *
				FROM DWADM.V_BTT8250_O
				WHERE AP85_DATE BETWEEN ? AND ? AND IDNO = ? AND DUPNO = ? {0}
				ORDER BY AP85_DATE, AP85_TIME, AP85_RMNO
			</value>
		</entry>

        <!-- CMSTKTBL : 股票代號中文名稱對照檔 -->
        <entry key="findODS_CMSTKTBL">
            <value>
                SELECT * FROM MEGAVSAM.CMSTKTBL
            </value>
        </entry>

		<!-- CMSTKTBL : 股票代號中文名稱對照檔 -->
		<entry key="findODS_CMSTKTBL_ByCODE">
			<value>
				SELECT * FROM MEGAVSAM.CMSTKTBL WHERE CMSTK_CODE = ?
			</value>
		</entry>

        <!-- CMMEMTBN : 存摺摘要代號中英文名稱對照檔 -->
        <entry key="findODS_CMMEMTBN">
            <value>
                SELECT * FROM MEGAVSAM.CMMEMTBN
            </value>
        </entry>

		<entry key="findODS_8410_ByAccNo">
			<value>
				SELECT DRCFIN_RIMTXNNO AS SDR4560_RIMTXNNO,
						DATE(CHAR(INT('01' || RIMPDATE_YY || RIMPDATE_MM || RIMPDATE_DD) + 19110000)) AS SDR4560_RIMPDATE,
						RIMTIME_HH || ':' || RIMTIME_MM || ':' || RIMTIME_SS AS SDR4560_RIMTIME,
						RIMSBHID || RIMPBHID || RIMIOFLG || RIMTYPE || RIMYEAR || RIMMMDD || RIMNUM AS SDR4560_RIMKEY,
						RIMSBNK_SBKID || RIMSBNK_SBHID || RIMSBNK_CHKID AS SDR4560_RIMSBNK,
						CASE
							WHEN RIMSBNK_SBKID = '017' THEN AGE_BCH_NAME_0
							WHEN BNM_BCH_NAME_0 IS NOT NULL THEN BNM_BCH_NAME_0
							ELSE (RIMSBNK_SBKID || RIMSBNK_SBHID || RIMSBNK_CHKID)
						END AS SDR4560_RIMSBNK_NAM,
						'' AS SDR4560_ROMKEY,
						RIMSNAM_PV_CNAM AS SDR4560_RIMSNAM,
						RIMAMNT AS SDR4560_RIMAMNT, RIMREMRK AS SDR4560_RIMREMRK,
						RIMRNAM_PV_CNAM AS SDR4560_RIMRNAM, RIMRTEL AS SDR4560_RIMRTEL,
						RIMRACNO_PV_ACNO AS SDR4560_RIMRACNO,
						RIMRADDR AS SDR4560_RIMRADDR,
						DR_PA_TXN_ACT_NO || DR_PA_TXN_SUB_ACT AS SDR4560_ACTLID,
						DR_PA_TXN_NAME AS SDR4560_ACTNAM, DR_PA_TXN_AMT AS SDR4560_ACTAMT,
						DR_PA_TXN_MEMO1 AS SDR4560_DESC1, DR_PA_TXN_MEMO2 AS SDR4560_DESC2, DR_PA_TXN_MEMO3 AS SDR4560_DESC3,
						RIMPRCNT AS SDR4560_RIMPRCNT, RIMADDID AS SDR4560_TELLER
				FROM MEGAVSAM.DRCFIN
				LEFT JOIN MEGAVSAM.DRCFRIM ON DRCFIN_DATA = (RIMSBHID || RIMPBHID || RIMIOFLG || RIMTYPE || RIMYEAR || RIMMMDD)
												AND DRCFIN_SEQNO = RIMNUM
				LEFT JOIN MEGAVSAM.DRCFAGE ON RIMSBNK_SBKID = AGE_ICBC AND RIMSBNK_SBHID = AGE_BCH AND RIMSBNK_CHKID = AGE_CHK
				LEFT JOIN MEGAVSAM.DRCXBNM ON RIMSBNK_SBKID = BNM_BNK AND RIMSBNK_SBHID = BNM_BCH AND RIMSBNK_CHKID = BNM_CHK
				LEFT JOIN MEGAVSAM.DRPATXN ON DRCFIN_DATE = DR_PA_TXN_DATE
												AND RIMSBHID = DR_PA_TXN_BR_NO
												AND RIMTXNRM = DR_PA_TXN_DEPT
												AND RIMADDID = DR_PA_TXN_TELLER
												AND '1' = DR_PA_TXN_S_B
												AND RIMCUR = DR_PA_TXN_CURR
												AND RIMTXNUM = DR_PA_TXN_SET_NO
												AND DRCFIN_PATXN_ENTRY = DR_PA_TXN_ENTRY_NO
												AND '' = DR_PA_E_C
				WHERE DRCFIN_ACC_NO = ? AND DRCFIN_RCVBNK = ? AND DRCFIN_LNPS120_FLAG = 'Y' AND DRCFIN_DATE = RIGHT(INT(REPLACE(CURRENT DATE, '-', '')) - 19110000, 6)
				ORDER BY DRCFIN_RIMTXNNO, DRCFIN_DATA, DRCFIN_SEQNO
			</value>
		</entry>
		
		<entry key="findODS_CMFWARNP_ById">
   			<value>
      			SELECT 19110000+CMFWARNP_WARNING_DATE AS CMFWARNP_WARNING_DATE FROM MEGAVSAM.CMFWARNP 
				where CMFWARNP_WARNING_DATE !='' and CMFWARNP_EXPIRE_DATE !='' 
				and ? between 19110000+CMFWARNP_WARNING_DATE and 19110000+CMFWARNP_EXPIRE_DATE and CMFWARNP_PV_IDNO = ?
   			</value>
		</entry>

		<entry key="findODS_CMFAUDAC_ByAcc">
			<value>
				select *
				from megavsam.cmfaudac
				where CMAUD_ALARM_CLASS In('1','2','3') and CMAUD_STATUS='3' and CMAUD_ACT_BR=? and CMAUD_ACT_OTHER=?
			</value>
		</entry>
	</util:map>
</beans>
