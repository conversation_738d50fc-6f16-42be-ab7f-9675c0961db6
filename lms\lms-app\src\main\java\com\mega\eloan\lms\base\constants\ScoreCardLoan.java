package com.mega.eloan.lms.base.constants;


/**
 * <pre>
 * 卡友貸評等項目
 * </pre>
 * 
 * @since 2019/05/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/05/13,EL08034,new
 *          </ul>
 */
public interface ScoreCardLoan {
	//**********************
	// C101S01R 個金卡友貸信用評等表
	//**********************
	static final String 設定檔_V2_1 = "cls/scoreCardLoan_V2_1.properties"; // J-108-0105
	static final String 設定檔_V3_0 = "cls/scoreCardLoan_V3_0.properties"; // J-108-0266
	static final String 設定檔_V3_1 = "cls/scoreCardLoan_V3_1.properties"; // J-112-0192
	static final String 設定檔_V4_0 = "cls/scoreCardLoan_V4_0.properties"; // J-108-0266
	
	static final String Comma = ",";
	static final String Point = ".";
	static final String Semicolon = ";";

	static final String 分數 = "score";
	static final String 公式 = "formula";
	static final String 欄位 = "columns";

	interface column {
		String 評等建立日期 = "grdCDate";
		String 評等調整日期 = "grdTDate";
		String 聯徵資料日期 = "jcicDDate";
		String 聯徵查詢日期 = "jcicQDate";
		String 票信資料日期 = "etchDDate";
		String 票信查詢日期 = "etchQDate";
		String 報表亂碼 = "randomCode";
		String 合計變量得分 = "scrNum11";
		String 基準底分 = "scrNum12";
		String 初始評分 = "scrNum13";
		String 初始評等 = "grade1";
		String 調整評等 = "grade2";
		String 最終評等 = "grade3";
		String 支援評等 = "sprtRating";
		String 升降等_聯徵J10 = "adj_j10";
		/*
		 	UPDATE mis.kcs003 SET PRODID='ST',SCORE_STAT='0',SCORE=800,PERCENT_LB=80,PERCENT_UB=100,THRESHOLD_SCO=0,THRESHOLD_PCT=0,REASON_CNT=0
			,REASON_CODE_1='',REASON_EXPR_1='',REASON_CODE_2='',REASON_EXPR_2='',REASON_CODE_3='',REASON_EXPR_3='',REASON_CODE_4='',REASON_EXPR_4='' WHERE id in (?)
		*/
		String J10信用評分 = "j10_score";
		String KCS003信用評分狀態 = "kcs003_score_stat";
		String KCS003理由筆數 = "kcs003_reason_cnt";
		String KCS003理由代碼一 = "kcs003_reason_code1";
		String KCS003理由代碼二 = "kcs003_reason_code2";
		String KCS003理由代碼三 = "kcs003_reason_code3";
		String KCS003理由代碼四 = "kcs003_reason_code4";
		String KCS003理由說明一 = "kcs003_reason_expr1";
		String KCS003理由說明二 = "kcs003_reason_expr2";
		String KCS003理由說明三 = "kcs003_reason_expr3";
		String KCS003理由說明四 = "kcs003_reason_expr4";
		String KCS003附加理由代碼 = "kcs003_addl_text_code";
		String KCS003附加理由說明 = "kcs003_addl_text_desc";
		String KCS003_QDATE = "kcs003_qDate";
		String KCS003_QEMPCODE = "kcs003_qEmpCode";
		String KCS003_QEMPNAME = "kcs003_qEmpName";
		String KCS003_QBRANCH = "kcs003_qBranch";
		String 預測壞率 = "pd";
		String 基準底分A = "varA";
		String 基準底分B = "varB";
		String 基準底分常數項 = "varC";
		String 基準底分版本 = "varVer";

		String 調整狀態 = "adjustStatus";
		String 調整註記 = "adjustFlag";
		String 調整理由 = "adjustReason";
		String 未持有信用卡 = "cardFlag";
		

		//卡友貸評等
		String 個人年收入= "yPay";
		String 個人年收入分數= "scrypay";
		String 個人年所得= "pIncome";
		String 個人年所得分數= "scrPIncome";
		String 職稱 = "jobTitle";
		String 年資= "seniority";
		String 年資分數= "scrseniority";
		String 卡友貸學歷= "education";
		String 卡友貸學歷分數= "screducation";
		String 聯徵查詢月份當時無擔保授信往來家數 = "nochkItem01"; 
		String 聯徵查詢月份當時無擔保授信往來家數分數 = "noscrItem01";
		String 近6個月信用卡使用循環信用月份數 = "nochkItem02"; 
		String 近6個月信用卡使用循環信用月份數分數 = "noscrItem02";
		String 近6個月信用卡使用循環信用家數 = "nochkItem03"; 
		String 近6個月信用卡使用循環信用家數分數 = "noscrItem03";
		String 聯徵查詢月份當月授信繳款記錄小於等於6次旗標 = "nochkItem04"; 
		String 聯徵查詢月份當月授信繳款記錄小於等於6次旗標分數 = "noscrItem04";
		String D07因子 = "nochkItemD07";
		String D07分數 = "noscrItemD07";
		String N06因子 = "nochkItemN06";
		String N06分數 = "noscrItemN06";
		String P68因子 = "nochkItemP68";
		String P69因子 = "nochkItemP69";
		String P19因子 = "nochkItemP19";
		String P68_P19分數 = "noscrItemP68P19";
		String P68_P19組別 = "grpP68P19";
		String P69_P19分數 = "noscrItemP69P19";
		String P69_P19組別 = "grpP69P19";
		String 個人負債比率 = "nochkItemDrate";
		String 個人負債比率分數 = "noscrItemDrate";
		String R01因子 = "nochkItemR01";
		String R01分數 = "noscrItemR01";
		String P25因子 = "nochkItemP25";
		String P25分數 = "noscrItemP25";
		
		//J-111-0373非房貸模型 4.0  
		String nv4_學歷因子 = "education";
		String nv4_學歷分數 = "screducation";
		String nv4_dRate因子 = "nochkItemDrate";
		String nv4_dRate分數 = "noscrItemDrate";
		String nv4_P01因子 = "itemP01";
		String nv4_P01分數 = "scrP01";
		String nv4_loanBalSByid因子 = "loanBalSByid";
		String nv4_loanBalSByid分數 = "scrLoanBalSByid";
		String nv4_MaxR01因子 = "itemMaxR01";
		String nv4_MaxR01分數 = "scrMaxR01";
		String 截距 = "interCept";
		String 斜率 = "slope";
		
		String 有退票_拒往_信用卡強停或催收呆帳紀錄 = "chkItem1";
		String 有消債條例信用註記_銀行公會債務協商註記或其他補充註記 = "chkItem2";
		String 近12個月授信帳戶出現延遲二次以上 = "chkItem3";
		String 近12個月信用卡繳款狀況出現_循環信用有延遲_二次以上 = "chkItem4";
		String 近12個月信用卡繳款狀況出現_未繳足最低金額_二次以上 = "chkItem5";
		String 近12個月信用卡繳款狀況出現_全額逾期連續未繳_二次以上 = "chkItem6";
		String 近12個月信用卡有預借現金餘額二次以上 = "chkItem7";
		String 近12個月現金卡有動用紀錄 = "chkItem8";

		// 有退票_拒往_信用卡強停或催收呆帳紀錄 add by fantasy 2013/06/14
		String 退票 = "chkItem1a";
		String 拒往 = "chkItem1b";
		String 信用卡強停 = "chkItem1c";
		String 催收呆帳 = "chkItem1d";
		String 逾期放款 = "chkItem1e";
		// 有消債條例信用註記、銀行公會債務協商註記或其他補充註記 add by fantasy 2013/06/14
		String 消債條例信用註記 = "chkItem2a";
		String 銀行公會債務協商註記 = "chkItem2b";
		String 其他補充註記 = "chkItem2c";
		
		String 引用_卡友貸 = "quote";
		
		String 違約機率_預估2年期_短 = "dr_2YR";
		String 違約機率_預估3年期_中長 = "dr_3YR";
		String 違約機率_預估1年期_短 = "dr_1YR_S";
		String 違約機率_預估1年期_中長 = "dr_1YR_L";
	}

	interface type {
		String 卡友貸基本 = "cardLoanBase";
		String 卡友貸評等 = "cardLoanGrade";
		String 卡友貸違約機率 = "cardLoanDR";
	}
	
	/**
	 * <pre>
	 * 卡友貸評等基本項目
	 * </pre>
	 */
	interface cardLoanBase {
		String 個人年收入分數= "scrypay";		
		String 年資分數= "scrseniority";
		String 卡友貸學歷分數= "screducation"; 
		String 聯徵查詢月份當時無擔保授信往來家數分數 = "noscrItem01";
		String 近6個月信用卡使用循環信用月份數分數 = "noscrItem02";
		String 近6個月信用卡使用循環信用家數分數 = "noscrItem03";
		String 聯徵查詢月份當月授信繳款記錄小於等於6次旗標分數 = "noscrItem04";
	}
	

	/**
	* 名稱要和 score*.properties 中的設定相同 ，由 interfaceName+"."+fieldName
	*/
	interface cardLoanBase_V2_1 {
		String 個人年所得分數= "scrPIncome";		
		String 年資分數= "scrseniority";
		String 聯徵查詢月份當時無擔保授信往來家數分數 = "noscrItem01";
		String 近6個月信用卡使用循環信用家數分數 = "noscrItem03";
		String D07分數 = "noscrItemD07";
		String N06分數 = "noscrItemN06";
		String P68P19分數 = "noscrItemP68P19";
		String P68P19組別 = "grpP68P19";
	}

	interface cardLoanBase_V3_0 {
		String 個人年所得分數 = "scrPIncome";		
		String 個人負債比率分數 = "noscrItemDrate";
		String D07分數 = "noscrItemD07";
		String N06分數 = "noscrItemN06";
		String P69P19分數 = "noscrItemP69P19";
		String P69P19組別 = "grpP69P19";
		String R01分數 = "noscrItemR01";
		String P25分數 = "noscrItemP25";
	}
	
	interface cardLoanBase_V3_1 {
		String 個人年所得分數 = "scrPIncome";		
		String 個人負債比率分數 = "noscrItemDrate";
		String D07分數 = "noscrItemD07";
		String N06分數 = "noscrItemN06";
		String P69P19分數 = "noscrItemP69P19";
		String P69P19組別 = "grpP69P19";
		String R01分數 = "noscrItemR01";
		String P25分數 = "noscrItemP25";
	}
	
	interface cardLoanBase_V4_0 {
		String 學歷分數 = "screducation";		
		String P01 = "scrP01";
		String 個人負債比 = "noscrItemDrate";
		String 擔保餘額歸戶 = "scrLoanBalSByid";
		String 循環信用平均使用率 = "scrMaxR01";
	}
	
	/**
	 * <pre>
	 * 卡友貸評等等級項目
	 * </pre>
	 */
	interface cardLoanGrade {
		String 等級 = "level";
	}
	

	/**
	 * <pre>
	 * 卡友貸評等違約機率項目
	 * </pre>
	 */
	interface cardLoanDR {
		String 違約機率_預估2年期_短 = "dr_2YR";
		String 違約機率_預估3年期_中長 = "dr_3YR";
		String 違約機率_預估1年期_短 = "dr_1YR_S";
		String 違約機率_預估1年期_中長 = "dr_1YR_L";
	}
}
