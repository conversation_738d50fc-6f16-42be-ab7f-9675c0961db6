package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisQuotainfService;

/**
 * <pre>
 * 額度資訊檔 QUOTAINF(MIS.ELV42201)
 * </pre>
 * 
 * @since 2011/10/31
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/31 EL07625 new
 *          </ul>
 */
@Service
public class MisQuotainfServiceImpl extends AbstractMFAloanJdbc implements
		MisQuotainfService {

	@Override
	public Map<String, Object> getByKey(String custId, String dupNo,
			String branch, String cntrNo) {
		return this.getJdbc().queryForMap("QUOTAINF.getByKey",
				new String[] { custId, dupNo, branch, cntrNo });
	}

	// @Override
	// public void insert(String custId, String dupNo, String branch,
	// String cntrNo, String appDate, String aprDate, String custName,
	// String bossId, String bossName, String unId, String newCase,
	// String updater) {
	// this.getJdbc().update(
	// "QUOTAINF.insert",
	// new String[] { custId, dupNo, branch, cntrNo, appDate, aprDate,
	// custName, bossId, bossName, unId, newCase, updater });
	//
	// }
	//

	@Override
	public void insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"QUOTAINF.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR },
				dataList);

	}

	@Override
	public void delByKey(String custId, String dupNo, String branch,
			String cntrNo) {
		this.getJdbc().update("QUOTAINF.delByUniqueKey",
				new String[] { custId, dupNo, branch, cntrNo });

	}

}
