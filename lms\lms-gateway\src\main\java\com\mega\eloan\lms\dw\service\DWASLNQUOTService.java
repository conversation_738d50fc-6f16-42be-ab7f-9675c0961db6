/* 
 * DWASLNQUOTService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dw.service;

/**
 * <pre>
 * 國內 - 放款額度匯集日檔
 * </pre>
 * 
 * @since 2012/4/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/4/5,REX,new
 *          </ul>
 */
public interface DWASLNQUOTService {

	/**
	 * 查詢額度明細表原 額度序號
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public int findByCntrNoAndCustIdAndDupNo(String cntrNo, String custId,
			String dupNo);
}
