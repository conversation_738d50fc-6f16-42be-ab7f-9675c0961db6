L1205m01a.confirm1 =Confirm Return?
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u4e3b\u6a94\u500b\u91d1\u6388\u6b0a\u5916\u932f\u8aa4\u8a0a\u606f
#==================================================
L1205G.error1 =Input exceeded designated length
l120m01a.error1=Please Select
l120m01a.error1a=Please Select Name
l120m01a.error2=Please input reasons for the return or the request for additional documents
l120m01a.error3=$\{colName\}Required field not entered, Please check\u2026
l120m01a.error4=You have not entered Borrower's Group Profile, please complete the input before performing this function
l120m01a.error5=You have not entered Borrower's Related Company Profile, please complete the input before performing this function
l120m01a.error6=You have not entered Related Accounts Banking Transactions With the Bank, please complete the input before performing this function
l120m01a.error7=You have not entered Capital adequacy ratio, please complete the input before performing this function
l120m01a.error8=You have not entered Stakeholders, please complete the input before performing this function
l120m01a.error9=You have not entered Related Documents, please complete the input before performing this function
l120m01a.error10=You have not entered Borrower's Profile, please complete the input before performing this function
l120m01a.error11=You have not entered Credit Limit Type, please complete the input before performing this function
l120m01a.error12=You have not entered Credit Facility Report, please complete the input before performing this function
l120m01a.error13=Agenda can not leave blank
l120m01a.error14=No main Borrower, please confirm whether add main Borrower or not
l120m01a.error15=Not inquiry Banking Relationship Summary; Please confirm whether to query Banking Relationship Summary!
l120v01.error1=Unable to retrieve data
l120v01.error2=Multiple selections are not allowed under this function
l120v01.error3=You have entered an invalid month
l120v01.error4=You have entered an invalid date
l120v01.error5=Meeting session is left blank; please input the meeting session first
l120v01.error6=You have entered an invalid number
l120v01.error7=You have entered an invalid number
l120v01.error8=You have entered an invalid year
l120v01.error9=You have entered an invalid year range
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u4e3b\u6a94
#==================================================
page.title=LMS1215M01 Personal Banking Outside-authority Case Report
l120m01a.oid=oid
l120m01a.uid=uid
l120m01a.mainid=Document Number
l120m01a.typcd=Region/Department
l120m01a.custid=Unified Business Number
l120m01a.dupno=Repeat Serial No.
l120m01a.custname=Principal Borrower
l120m01a.unittype=Handling Unit's Category
l120m01a.ownbrid=Preparing Unit's ID
l120m01a.docstatus=Document Status
l120m01a.randomcode=Random Report Code
l120m01a.docurl=DocUrl
l120m01a.docname=Document Name
l120m01a.txcode=Transaction Code
l120m01a.creator=Document Creator
l120m01a.createtime=Date Created
l120m01a.cesid=Credit Report No.
l120m01a.updater=Last Editor
l120m01a.updatetime=Date Of Change
l120m01a.approver=Approver's ID
l120m01a.approvetime=Approval date
l120m01a.doctype=Corporate/Personal Banking Case
l120m01a.dockind=Authority Type
l120m01a.doccode=Case Type
l120m01a.caseyear=Case No. - Year
l120m01a.casebrid=Case No. - Branch
l120m01a.caseseq=Case No. - Serial No.
l120m01a.caseno=Case No.
l120m01a.casedate=Signature Date
l120m01a.authlvl=Authority Level
l120m01a.areachk=Whether to circulate to joint reviewers
l120m01a.areabrid=Joint Reviewer's ID
l120m01a.caselvl=Case Approval Level
l120m01a.docrslt=Final Approval Decision
l120m01a.enddate=Date Of Final Approval Decision
l120m01a.gist=Agenda
l120m01a.itemofbusi=Main Business Activities
l120m01a.cescustid=Business Overview - Borrower's UBN
l120m01a.cesdupno=Business Overview - Borrower's Repeated Number
l120m01a.longcaseflag=Whether a medium/long term case
l120m01a.longcasedscr=Description Of Medium/Long Term Case
l120m01a.cescase=This case does not require credit assessment
l120m01a.grpid=Group ID
l120m01a.meetingtype=Credit Review Committee/Collection Committee
l120m01a.rpttitle1=Credit Review Committee/Collection Committee Meeting Session
l120m01a.rpttitle2=Board Of Managing Directors Meeting Session
l120m01a.areasendinfo=Regional Headquarter 's Release Time
l120m01a.rpttitlearea1=Regional Headquarter's Credit Review Committee Meeting Session
l120m01a.signno=Approval Reference No.
l120m01a.sendfirst=Branch's First Submission
l120m01a.sendfirsttime=Branch's First Submission
l120m01a.sendlast=Branch's Last Submission
l120m01a.sendlasttime=Branch's Last Submission
l120m01a.rptid=rptid

l120m01a.selectBoss=Number Of Credit Supervisors
l120m01a.bossId=Credit Supervisor
l120m01a.no= 
l120m01a.site= th Persons
l120m01a.managerId=Unit/Authorizer
l120m01a.managerId2=Unit Supervisor
l120m01a.message01=Whether to submit for supervisor's approval
l120m01a.message02=Supervisor's name duplicated; please select again
l120m01a.message03=Please input the approval date
l120m01a.message04=Proceed with confirmation?
l120m01a.message05=The approval date must not be later than today
l120m01a.aoPerson=Account Administrator
print.custName=Borrower's Name
print.rptNo=Report Serial Number
print.rptName=Report Name
print.cntrNo=Credit Limit Serial Number
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u500b\u91d1\u6388\u6b0a\u5916\u9801\u7c64
#==================================================
l120m01a.status=Document Information
l120m01a.borrowdata=Borrower's Profile
l120m01a.l140m01a=Credit Facility Report
l120m01a.casebecause=Agenda
l120m01a.say=Description
l120m01a.other=Others
l120m01a.lookall=Overall Evaluation/Banking Relationship
l120m01a.relationshipdoc=Related Documents
l120m01a.moresay=Supplementary Descriptions
l120m01a.addfile=File Attachment
l120m01a.addfile1=File attachment to the Managing Director's Draft
l120m01a.l140m01b=Credit Limit Approval Sheet
l120m01a.l140m01b1=Head Office's Opinion
l120m01a.decide=Countersign/Meeting Resolution
l120m01a.decide1=Countersign
l120m01a.decide2=Meeting Resolution
l120m01a.decide3=Attachment
l120m01a.admin=Credit Administration Division's Opinion
l120m01a.admin1=Credit Review Opinion
l120m01a.otherplace=OBU/Headquarter's Countersigned Opinion
l120m01a.otherplace1=Countersigned Opinion
l120m01a.otherplace2=Headquarter's Opinion
l120m01a.tag1=Business Unit
l120m01a.tag2=Credit Administration Division
l120m01a.tag3=Headquarter
l120m01a.tag4=OBU
#J-106-0085-001  Web e-Loan\u4f01\u91d1\u6388\u4fe1\u65b0\u589e\u4e3b\u8981\u9084\u6b3e\u4f86\u6e90\u570b\u7b49\u76f8\u95dc\u6b04\u4f4d
l120m01a.paySourceCountry=Country Of Repayment Source
#J-106-0087-001 Web e-Loan \u6d77\u5916\u6388\u4fe1\u7f8e\u570b\u5730\u5340\u65b0\u589e\u4e0d\u7b26\u5408\u6388\u4fe1\u653f\u7b56\u6848\u4ef6\u8cc7\u8a0a
l120m01a.loanPolicyEx=Exceptions to the Loan Policy
l120m01a.countryRiskLimit=Description of national risk limit
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u500b\u91d1\u6388\u6b0a\u5916\u6b04\u4f4d\u540d\u7a31
#==================================================
l120m01a.title0a=Corporate Banking 
l120m01a.title0b=Personal Banking 
l120m01a.title1=Branch Outside-authority Case Report (General)
l120m01a.title1a=Branch Outside-authority Case Report (Others)
l120m01a.title1b=Branch Outside-authority Case Report (Appeals/Explanations)
l120m01a.title1c=Branch Within-authority Case Report (General)
l120m01a.title1d=Branch Within-authority Case Report (Others)
l120m01a.title1e=Branch Within-authority Case Report (Appeals/Explanations)
l120m01a.title1f=Head Office Within-authority Case Report (General)
l120m01a.title1g=Head Office Within-authority Case Report (Others)
l120m01a.title1h=Head Office Within-authority Case Report (Appeals/Explanations)
l120m01a.title1i=\u5340\u57df\u71df\u904b\u4e2d\u5fc3\u6388\u6b0a\u5167\u6848\u4ef6\u7c3d\u5831\u66f8(\u4e00\u822c)
l120m01a.title1j=\u5340\u57df\u71df\u904b\u4e2d\u5fc3\u6388\u6b0a\u5167\u6848\u4ef6\u7c3d\u5831\u66f8(\u5176\u4ed6)
l120m01a.title1k=\u5340\u57df\u71df\u904b\u4e2d\u5fc3\u6388\u6b0a\u5167\u6848\u4ef6\u7c3d\u5831\u66f8(\u9673\u5fa9(\u8ff0)\u6848)
l120m01a.title2=Branch name
l120m01a.title3=Case No.
l120m01a.title4=Document Status
l120m01a.title5=Signature Date
l120m01a.title6=Principal Borrower
l120m01a.title7=Whether to circulate to joint reviewers
l120m01a.title8=Approval Level
l120m01a.title9=Document Creator
l120m01a.title10=Last Editor
l120m01a.title11=Branch's First Submission
l120m01a.title12=Branch's Last Submission
l120m01a.title13=Random Report Code
l120m01a.title14=Final Approval Decision
l120m01a.title15=Approval Date
l120m01a.title16=Unit/Authorizer:
l120m01a.title17=Credit Supervisor/Approver:
l120m01a.title18=Account Administrator:
l120m01a.title19=Clerk:
l120m01a.title20=Credit Application Approval Summary
l120m01a.title21=COO:
l120m01a.title22=Deputy COO:
l120m01a.title23=Assistant Vice President:
l120m01a.title24=Senior VP & GM:
l120m01a.title25=Deputy Division Head:
l120m01a.title26=Verifier:
l120m01a.title27=Select Data Source
l120m01a.title28=\u5217\u5370\u6c7a\u8b70\u4e8b\u9805
l120m01a.sign=Countersigned Opinion
l120m01a.sign2=Input Signature Column Personnel
l120m01a.caselevel=Case Approval Level
l120m01a.looksay=Supplement Opinions & Review Opinions
l120m01a.casetitle=Headquarter's Credit Review Committee Meeting Session
l120m01a.looksay2=Headquarter's Opinions
l120m01a.looksay3=Business Center Meeting Resolution
L1205G.grid1=Within-authority Case Report
L1205G.grid2=Outside-authority Case Report
L1205G.grid3=Compiling In Progress
L1205G.grid4=Common Borrower
L1205G.grid5=Joint Issuer
L1205G.grid6=Guarantor
L1205G.grid7=Ordinary Guarantor
L1205G.grid8=Collateral Provider
L1205G.grid9=General
L1205G.grid10=Others
L1205G.grid11=Appeal/Explanation Case
L1205G.form1=-C-
L1205G.form2=No.
l120m01a.srcFileName=File Name
l120m01a.fileDesc=File Description
l120m01a.uploadTime=Upload Time
l120m01a.otherfile=File Attachment
l120m01a.radio1=No
l120m01a.radio2=Circulate To Other Signatories
l120m01a.radio3=Circulate To Review
l120m01a.radio4=undertake
l120m01a.radio5=Reject
l120v01.thickbox7=Input Credit Review Committee Meeting Date
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u4e3b\u6a94\u500b\u91d1\u6388\u6b0a\u5916Legend\u540d\u7a31
#==================================================
l120m01a.subtitle1=Basic Profile
l120m01a.subtitle2=Document Revision History
l120m01a.subtitle3=Approval Result & Decision
l120m01a.subtitle4=File Attachment
l120m01a.subtitle5=Loan Review Committee's Resolution
l120m01a.subtitle6=Board Of Directors Resolution
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u500b\u91d1\u6388\u6b0a\u5916\u6309\u9215\u540d\u7a31
#==================================================
l120m01a.bt01=Select Files To Attach
l120m01a.bt02=Delete
l120m01a.login=Register
l120m01a.bt04=Input Title
l120m01a.bt05=Copy Form Template
#==================================================
# \u6388\u4fe1\u7c3d\u5831\u66f8\u500b\u91d1\u6388\u6b0a\u5916\u6d41\u7a0b
#==================================================
l120m01a.btnSendTo=Proposal
l120m01a.btnHeadAction=Return For Correction
l120m01a.btnCheck=Verifier
l120m01a.queryButtonType1=Return to handling officer for correction
l120m01a.queryButtonType2=Submit To Credit Administration Division
l120m01a.queryButtonType3=To Headquarter
l120m01a.queryButtonType4=Submit To Head Office
l120m01a.queryButtonType4_JP=Submit To Manager Branch
l120m01a.queryButtonType5=Approve Release
l120m01a.queryButtonType6=Return to branch for correction
l120m01a.queryButtonType7=Return To Regional Center For Correction
l120m01a.queryButtonType8=Confirm
l120m01a.backReason=Reason For Return/Additional Documents
l120m01a.backCaseRadio1=Case Withdrawal
l120m01a.backCaseRadio2=Pending Appeal

l120m01a.sendTo1=Propose To Credit Review Committee
l120m01a.sendTo2=Propose To Collection Committee
l120m01a.sendTo3=Propose To Boards of Directors
l120m01a.sendTo4=Propose To Audit Committee

#==================================================
# \u6388\u4fe1\u5831\u6848\u8003\u6838\u8868\u6388\u6b0a\u6a94
#==================================================
L730A01A.oid=oid
L730A01A.mainId=Document Number
L730A01A.pid=pid
L730A01A.ownUnit=Authorizer
L730A01A.owner=Authorizing Personnel
L730A01A.authTime=Authorized Date
L730A01A.authType=Authority Type
L730A01A.authUnit=Authorized Unit

#==================================================
# \u6388\u4fe1\u5831\u6848\u8003\u6838\u8868\u4e3b\u6a94
#==================================================
L730M01A.oid=oid
L730M01A.uid=uid
L730M01A.mainId=Document Number
L730M01A.typCd=Region/Department
L730M01A.custId=Unified Business Number
L730M01A.dupNo=Repeat Serial No.
L730M01A.custName=Customer Name
L730M01A.unitType=Handling Unit's Category
L730M01A.ownBrId=Preparing Unit's ID
L730M01A.docStatus=Document Status
L730M01A.randomCode=Document Decode Error
L730M01A.docURL=Document URL
L730M01A.txCode=Transaction Code
L730M01A.creator=Originator's ID
L730M01A.createTime=Date Created
L730M01A.updater=Modifier's ID
L730M01A.updateTime=Date Of Change
L730M01A.approver=Approver's ID
L730M01A.approveTime=Approval date
L730M01A.isClosed=Case Closure?
L730M01A.deletedTime=Delete Remarks
L730M01A.branchId=Evaluated Unit
L730M01A.chkYM=Year/Month Of Data
L730M01A.sysType=Corporate/Personal Banking Case
L730M01A.apprId=Inspector
L730M01A.chkId=Verifier
L730M01A.chkUnit=Inspecting Unit
L730M01A.projNo=Case Report No.
L730M01A.curr=Credit Limit (Currency)
L730M01A.loanAmt=Credit Limit (amount)
L730M01A.caseAmt=Individual
L730M01A.tmGrade=Number Of Deductions
L730M01A.creator=Originator's ID
L730M01A.createTime=Date Created
L730M01A.updater=Modifier's ID
L730M01A.updateTime=Date Of Change

#==================================================
# \u6388\u4fe1\u5831\u6848\u8003\u6838\u8868\u660e\u7d30\u6a94
#==================================================
L730S01A.oid=oid
L730S01A.mainId=Document Number
L730S01A.chkYM=Year/Month Of Data
L730S01A.sysType=Corporate/Personal Banking Case
L730S01A.itemType=Type
L730S01A.itemNo=Item
L730S01A.itemScor=Standalone Score
L730S01A.itemCnt=Number Of Findings
L730S01A.itemAll=Score Deductions
L730S01A.itemMemo=Remarks
L730S01A.creator=Originator's ID
L730S01A.createTime=Date Created
L730S01A.updater=Modifier's ID
L730S01A.updateTime=Date Of Change

L730S01A.index1=Document Information
L730S01A.index2=1. Credit Facility Report (1)
L730S01A.index3=1. Credit Facility Report (2)
L730S01A.index4=2. Summary Information Sheet
L730S01A.index5=3. Valuation Report
L730S01A.index6=4. Case Report
L730S01A.index7=Name Of Evaluated Branch
L730S01A.index8=Evaluation Year/Month
L730S01A.index9=Note: The remarks field allows up to 100 words
L730S01A.index10=Notices to business units when making reports
L730S01A.index11=1. Sort Order:
L730S01A.index12=2. Previously Approved Limit:
L730S01A.index13=3. Applied Credit Limit:
L730S01A.index14=4. Restrictions:
L730S01A.index15=5. Number Of Collections & Payments:
L730S01A.index16=6. Drawdown Period:
L730S01A.index17=7. Risk Weight:
L730S01A.index18=8. Branch/Department Overdue Ratio:
L730S01A.index19=9. Collateral:
L730S01A.index20=10. Promissory Note:
L730S01A.index21=11. Other Terms & Conditions:
L730S01A.index22=12. Guarantor Or Third-Party Collateral Providers:
L730S01A.index23=13. Changes:
L730S01A.index24=14. Others:
L730S01A.index25=Remarks:
L730S01A.index26=5. Credit approval procedures, compliance with relevant policies, and deficiency in post-loan approval management.
L730S01A.index27=6. The content of the loan proposal is inadequate or there is no explanation  about the warning of ELOAN system.
L730S01A.index28=*In  same case, errors  of  same topic (for example: the majority decision of the syndicated loan has not been reported for verification) will be calculated by deducting points of an error multipling times of errors were made.
L730S01A.index29=**Such errors will not only  be redressed by the Credit Control Dept. via  electronic sign-off emails  with copy to the Overseas Business Administration Dept., the Corporate Banking Business Dept. and the Supervisory Vice President, but also will be included in the deduction items  of annual management assessment.

L730S01A.itemNo1=If the Credit Facility Report stretches to 2 or more pages, then the list should be sorted in orders such as new, customer, limit increase, change of terms, renewal, cancellation, unchanged etc.
L730S01A.itemNo2=For new cases, this field should be blank (no amounts).
L730S01A.itemNo3=For approved medium/long term loans and other non-revolving cases, this field should contain the original apprived limit or the actual amount disbursed.
L730S01A.itemNo4=For non-revolving loans and credit limits that are no longer utilized, this field should be updated to reflect the current outstanding balance, plus LCs issued but BL not yet received, and plus any available credit limits.
L730S01A.itemNo5=Limits of each credit line and the amount of loans allocated to each syndicated lender should be displayed here, and not stated in terms and conditions.
L730S01A.itemNo6=The difference between collection and payment should be the same as the outstanding credit balance.
L730S01A.itemNo7=The limit is still within the drawdown period; if no more utilization is allowed, please tag accordingly (e.g. for cases reported with issues for which utilization is no longer permitted).
L730S01A.itemNo8=If the period starts from the contract date or the approval date, the date of occurrence needs to be specified.
L730S01A.itemNo9=If the credit facility is about to expire, evaluate whether a facility renewal should be conducted concurrently.
L730S01A.itemNo10=Can not leave blank.
L730S01A.itemNo11=If the borrower is a government institution or is secured by fixed deposit, credit guarantee, TSEC/GTSM listed shares etc, then the amount of risk weight and capital adequacy ratio after risk mitigants must be calculated properly.
L730S01A.itemNo12=Must be an average between DBU and OBU figures.
L730S01A.itemNo13=For real estate properties, please detail the location of the collateral, land (those without partial ownership can not be stated otherwise), building area, purchase price (or market price), loan quantum and the amount charged etc.
L730S01A.itemNo14=For hard-to-value collaterals, please describe the characteristics and approximate values of the collaterals.
L730S01A.itemNo15=If there are multiple collaterals, the amount of charge should be calculated as a total.
L730S01A.itemNo16=Collateral details should not be displayed under Other Terms & Conditions.
L730S01A.itemNo17=Must comply with the bank's policy on obtained promissory notes; take special notice on how promissory notes are obtained from OBU customers and related accounts.
L730S01A.itemNo18=If the approved case involves syndication loans, then the name of the lead arranger, syndication loan limit, and actual lending amount must be specified clearly.
L730S01A.itemNo19=For medium/long term loans or instalment cases, the repayment horizon and the amount of monthly instalments must be stated clearly. (For confirmed cases, the terms and conditions should no longer be expressed as "...from the initial drawdown date or contract date...)
L730S01A.itemNo20=If there are multiple credit lines, the terms & conditions should be listed in the order of the credit lines (A, B, C...).
L730S01A.itemNo21=Provide descriptions for any 3rd party collateral providers.
L730S01A.itemNo22=State clearly the changes made; i.e. changing from ... to ... (e.g. the interest rate on USD loans is changed from 6-month SIBOR + 1% to 3-month SIBOR + 0.5%); do not state "as above".
L730S01A.itemNo23=Complex changes should be elaborated in a separate sheet for easier comprehension.
L730S01A.itemNo24=Even though the credit facilities remain unchanged, contents of the Credit Facility Report still need to be updated; e.g. medium/long term loans with known initial drawdown date and tenor need to be stated accordingly.
L730S01A.itemNo25=The branch's share of the syndication loan needs to be stated in Restrictions.
L730S01A.itemNo26=Do not repeat stating the same information. If the credit limit has already been stated under Restrictions, then it needs not be repeated in Other Terms & Conditions.
L730S01A.itemNo27=Currencies (e.g. TWD, USD, JPY, CNY) and units (e.g. thousand dollars, dollars) must be accurate.
L730S01A.itemNo28=For construction loans, try to state in the contract that all progress billings must be paid into the bank's account, and make it part of Other Terms & Conditions.
L730S01A.itemNo29=Credit terms must be stated in fields where relevant, and do not place them all under Other Terms & Conditions.
L730S01A.itemNo30=For long term investments of larger sums, please provide a consolidated Summary Information Sheet.
L730S01A.itemNo31=Pay attention to inconsistent percentages (e.g. the borrower has bank loans but the financial expense ratio is 0).
L730S01A.itemNo32=Make special remarks and explanations to large differences between the current and the previous period.
L730S01A.itemNo33a=Pay attention to abnormal changes in related information, e.g.:
L730S01A.itemNo33b=Changes in net worth and pre-tax profit.
L730S01A.itemNo34=Changes in current operating profit and pre-tax profit.
L730S01A.itemNo35=Discrepancy between bank loans shown on financial statements and JCIC's data.
L730S01A.itemNo36=Audited financial statements must contain the auditor's name.
L730S01A.itemNo37=For audited financial statements with qualified opinions, evaluate whether such opinions have any impacts on the case.
L730S01A.itemNo38=Accounts with zero deposits need to be supported with reasoning; check whether actual deposit and foreign currency transactions are consistent with the bank's credit ratios.
L730S01A.itemNo39=Use consistent currency and unit.
L730S01A.itemNo40=Credit grade, share capital, date of establishment and other information must be consistent with the Credit Facility Report, Case Report etc.
L730S01A.itemNo41=Provide the latest information (if the borrower has changed its representative, then the information should be reflected accordingly).
L730S01A.itemNo42=Land locations must be clearly stated with land uses.
L730S01A.itemNo43=State clearly the rationale behind property valuations; do not use ambiguous terms.
L730S01A.itemNo44=State according to the required format and order; descriptions must be concise, to the point, and logical; avoid writing out long essays with lack of depth, contradicting arguments or spelling mistakes.
L730S01A.itemNo45=Do not focus solely on positive factors without addressing negative concerns; provide reasonable explanations to justify borrower's weaknesses or issues; apply responsive measures where applicable.
L730S01A.itemNo46=Descriptions or commitments stated in the Case Report should also be listed in the Credit Facility Report; explain whether the previous commitments have been fulfilled.
L730S01A.itemNo47=For cases that involve refinancing loans granted by other banks, please state the main differences between ours and the peer bank's credit terms.
L730S01A.itemNo48=For participations in syndication loans, please describe the reasons for not being able to take the lead arranger role, and state the minimum lending amount.
L730S01A.itemNo49=Try to obtain proof from the customers to support their claims on preferential rates offered by peer banks, and adjust terms & conditions where applicable.
L730S01A.itemNo50=The English versions of Credit Facility Report, Case Report, appraisal report etc used by overseas branches must be consistent with the Chinese version.
L730S01A.itemNo51=Approval date of Case Report  singed by Operations Center or the Credit Management Division  after the previous loan contract expiry date.
L730S01A.itemNo52=Since  last approval by th head office, change(s) of the syndicated loan  was/were approved by a majority of the participating banks.Such change(s) was/were not reported the head office for verification in accordance with the regulatoins.
L730S01A.itemNo53=Policy exception(s) is/are not disclosed in the loan proposal.
L730S01A.itemNo54=The branch  does not disclose information of a loan case that will influence the risk judgment of the Head Office.
L730S01A.itemNo55=The branch fails to inquire about the credit status of related parties in all  branches of the bank, resulting in failure to report in accordance with the relevant authorization regulations.
L730S01A.itemNo56=The Branch did not follow the approved terms and conditions of a loan case,and/or failed to monitor the loan as  per the loan terms after approval of the loan.
L730S01A.itemNo57=Insufficient disclosure of negative information.
L730S01A.itemNo58=Failed to fully clarify the loan, the borrower and guarantor's  industry, country of citizenship ..., etc., resulting in the biased report of loan case, and thus  affecting the decision of approval/disapproval of the case.
L730S01A.itemNo59=When the branch report  a case via  the ELOAN system , it fails to clarify the content of warning  message(s) in time and informs it in advance via  Notes system when the case is submitted.
L730S01A.itemNo60=The organizational structure chart is unclear, including but not limited to unclear indication of shareholding  and investing parties, trustor and trustee, and/or shareholding ratio... etc.
L730S01A.itemNo61=The attached list of changes of terms and conditions does not indicate the reasons and changes of loan terms clearly.
L730S01A.itemNo62=Complete the filing of LGD Calculation and ensure it is right.
#==================================================
# \u6388\u5be9\u6703\uff0f\u50ac\u6536\u6703\u6703\u8b70\u6c7a\u8b70\u6a94
#==================================================
L120M01H.oid=oid
L120M01H.mainId=Document Number
L120M01H.meetingType=Credit Review Committee/Collection Committee
L120M01H.gist=Agenda
L120M01H.negOpinion=\u8ca0\u9762\u610f\u898b
L120M01H.consent=\u540c\u610f\u7406\u7531
L120M01H.approved=\u64ec\u8fa6
L120M01H.another=\u53e6\u56d1
L120M01H.authLvlStr=\u6388\u6b0a\u7b49\u7d1a
L120M01H.dispWord=Resolution
L120M01H.quotaDesrc=Approved Credit Line compared to Previously Approved Limit Increase, Reduction
L120M01H.meetingNote=Meeting Resolution
L120M01H.creator=Originator's ID
L120M01H.createTime=Date Created
L120M01H.updater=Modifier's ID
L120M01H.updateTime=Date Of Change
button.importGist=\u5f15\u9032\u6848\u7531
button.importAuthLvlStr=\u5f15\u9032\u6388\u6b0a\u7b49\u7d1a
#==================================================
# \u5176\u4ed6
#==================================================
_l120m01a.radio1=Agenda
_l120m01a.radio2=Resolution Log (all documents in the same meeting session)
_l120m01a.radio3=Resolution Log (all ticked documents in the same meeting session)
_l120m01a.btnCreate=Generate
_l120m01a.year=Republic Year
_l120m01a.month=Months
_l120m01a.day=Day
_l120m01a.times=Times
_l120m01a.select1=Credit Review Team Meeting
_l120m01a.select2=Credit Review Committee
#==================================================
# \u5217\u5370THICKBOX
#==================================================
THICKBOX.RPTTITLE=Print Statement Category
THICKBOX.RPTNAMEA=Case Report & related attachment
THICKBOX.RPTNAMEB=Head Office's Opinion
THICKBOX.RPTNAMEC=Headquarter's Opinions
THICKBOX.RPTNAMED=Review Opinions & Supplementary Descriptions/Countersign
THICKBOX.RPTNAMEE=Credit Evaluation Committee Resolution of the conference
THICKBOX.RPTNAMEF=Collection Committee Meeting resolution
THICKBOX.RPTNAMEG=Overseas Syndication Loan Circular Views
THICKBOX.RPTNAMEH=Approval Sheet
THICKBOX.RPTNAMEZ=\u5340\u57df\u71df\u904b\u4e2d\u5fc3\u6703\u8b70\u6c7a\u8b70
THICKBOX.unitAuthJobTitle=Unit/Authorizer Job Title

#J-109-0479_05097_B1001 Web e-Loan\u7c3d\u5831\u66f8\u589e\u52a0\u5404\u5225\u6d41\u7a0b\u63a7\u7ba1\u968e\u6bb5\u7684\u6642\u9593\u9ede\u4e26\u63d0\u4f9b\u5217\u5370\u6848\u4ef6\u968e\u6bb5\u9032\u5ea6\u53ca\u7d71\u8a08excel\u4e0b\u8f09
#J-110-0521_05097_B1001 Web e-Loan\u6d77\u5916\u6388\u4fe1\u7cfb\u7d71\u589e\u52a0\u7559\u5b58\u6848\u4ef6\u6d41\u7a0b\u7d00\u9304
THICKBOX.RPTNAMEK=\u6388\u6b0a\u5916\u6848\u4ef6\u5fb5\u6388\u4fe1\u9032\u5ea6\u6642\u7a0b\u8868


#==================================================
# \u4f7f\u7528\u8005\u81ea\u8a02\u8868\u683c\u7bc4\u672c\u6a94
#==================================================
L720M01A.oid=oid
L720M01A.patternNM=Template Name
L720M01A.pattern=Table Content
L720M01A.creator=Originator's ID
L720M01A.createTime=Date Created
L720M01A.updater=Modifier's ID
L720M01A.updateTime=Date Of Change

l720v00.index01=User's Customized Template

l720v00.alert1=No data selected
l720v00.alert2=You can only edit one record, please select again!
l720v00.alert3=Duplicate template name, please input again...

l720v00.other=Description: used mainly for Credit Administration Division's Login granted the Commission resolution, custom template spreadsheet format to introduce the grant of Commission resolution

lmss01.legend1=Branch
lmss01.legend2=Head Office

lmss01a.addnew1=Branch Release Time
lmss01a.addnew2=Credit Review Committee/Collection Committee Meeting Session
lmss01a.addnew3=Board Of Managing Directors Meeting Session
lmss01a.addnew4=Business Center's Release Time 
lmss01a.addnew5=Headquarter's Credit Review Committee Meeting Session
lmss01a.addnew6=Date of return or the request for additional documents
lmss01a.addnew7=Reason For Return/Additional Documents
lmss01a.addnew8=Audit Committee Meeting Session

l120m01a.ngflag=\u5354\u8b70\u6848\u8a3b\u8a18
l120m01a.ngFlag_0=\u7121
l120m01a.ngFlag_1=\u9001\u6388\u7ba1\u8655\u5be9\u6838(\u4e09\u500b\u6708\u4ee5\u5167)
l120m01a.ngFlag_2=\u9001\u50b5\u7ba1\u8655\u5be9\u6838(\u4e09\u500b\u6708\u4ee5\u4e0a)

#J-110-0458 \u4f01\u91d1\u6388\u6b0a\u5167\u5176\u4ed6 - \u300c\u7c21\u6613\u7c3d\u5831\u300d\u9078\u9805\uff0c\u9069\u7528\u65b9\u6848\u300cLIBOR\u9000\u5834\u8b8a\u66f4\u5229\u7387\u689d\u4ef6\u7c21\u6613\u7c3d\u5831\u300d
l120m01a.miniFlag=Simplify Credit Report
l120m01a.caseType=Case Main Category

#J-105-0179-001 Web e-Loan\u4f01\u91d1\u6388\u4fe1\u5efa\u7acb\u300c\u5f80\u4f86\u7570\u5e38\u901a\u5831\u6236\u300d\u7d00\u9304\u67e5\u8a62\u53ca\u65bc\u7c3d\u5831\u66f8\u4e0a\u986f\u793a\u67e5\u8a62\u7d50\u679c\u529f\u80fd
phrase.index1=[\u7247\u8a9e]
phrase.index2=\u672c\u6848\u64ec

#J-110-0493_11557_B1001 \u6aa2\u67e5\u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u984d\u5ea6\u5408\u8a08\u662f\u5426\u9054\u65b0\u53f0\u5e631\u5104\u5143\u4ee5\u4e0a
l120m01i.rtlOver100MillionMessage=\u672c\u6848\u5c6c\u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u984d\u5ea6\u5408\u8a08\u9054\u65b0\u53f0\u5e631\u5104\u5143\u4ee5\u4e0a\uff0c\u4f9d\u898f\u5176\u5be9\u6838\u5c64\u7d1a\u61c9\u70ba\u8463\u4e8b\u6703\u3002\u5982\u672c\u6848\u5c6c\u4f9d\u898f\u53ef\u6392\u9664\u53d7\u9650\u6848\u4ef6\uff0c\u8acb\u9ede\u9078\u9069\u7576\u4e4b\u6392\u9664\u539f\u56e0\u3002
l120m01i.excludeRtlTitle=\u53ef\u6392\u9664\u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u9650\u5236\u539f\u56e0
l120m01i.excludeRtlMessage=\u672c\u6848\u56e0\u4e0b\u5217\u539f\u56e0\u53ef\u6392\u9664\u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u9650\u5236:
l120m01i.excludeRltLimRsn1=\u56e0\u653f\u5e9c\u70ba\u9280\u884c\u6216\u91d1\u63a7\u8ca0\u8cac\u4eba\uff0c\u81f4\u6388\u4fe1\u6236\u70ba\u9280\u884c\u6cd5\u6216\u91d1\u63a7\u6cd5\u7b2c44\u689d\u7b2c\u4e8c\u6b3e\u4e4b\u9650\u5236\u5c0d\u8c61\u3002
l120m01i.excludeRltLimRsn2=\u5c0d\u653f\u5e9c\u6a5f\u95dc\u6216\u672c\u884c\u6301\u80a150%\u4ee5\u4e0a\u4e4b\u6d77\u5916\u5b50\u884c\u6388\u4fe1\u3002
l120m01i.excludeRltLimRsn3=\u5c0d\u91d1\u878d\u6a5f\u69cb\u6240\u7c3d\u767c\u4e4b\u4fe1\u7528\u72c0\u8fa6\u7406\u4fdd\u514c\u3002
l120m01i.excludeRltLimRsn4=\u5176\u4ed6\uff0c\u8acb\u8a73\u8ff0\u4e3b\u7ba1\u6a5f\u95dc\u51fd\u4ee4\u4e4b\u65e5\u671f\u53ca\u6587\u865f

#J-109-0479_05097_B1001 Web e-Loan\u7c3d\u5831\u66f8\u589e\u52a0\u5404\u5225\u6d41\u7a0b\u63a7\u7ba1\u968e\u6bb5\u7684\u6642\u9593\u9ede\u4e26\u63d0\u4f9b\u5217\u5370\u6848\u4ef6\u968e\u6bb5\u9032\u5ea6\u53ca\u7d71\u8a08excel\u4e0b\u8f09
#J-110-0521_05097_B1001 Web e-Loan\u6d77\u5916\u6388\u4fe1\u7cfb\u7d71\u589e\u52a0\u7559\u5b58\u6848\u4ef6\u6d41\u7a0b\u7d00\u9304
l120s17a.caseReceivedDate=Case receipt date(\u6848\u4ef6\u6536\u4ef6\u65e5\u671f)
l120s17a.caseReceivedDateMemo=(\u5206\u884c\u7c3d\u5831\u5167\u5bb9\u6700\u7d42\u5b9a\u7a3f\u9001\u5448\u65e5)
l120s17a.meetingDate=Propose Date(\u63d0\u6703\u65e5\u671f)

l120s17a.caseCancelDate=CaseCancelDate
