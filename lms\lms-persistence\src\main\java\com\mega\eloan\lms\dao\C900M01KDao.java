/* 
 * C900M01KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M01K;

/** 授信業務授權額度檔 **/
public interface C900M01KDao extends IGenericDao<C900M01K> {

	C900M01K findByOid(String oid);
	
	List<C900M01K> findByMainId(String mainId);

	List<C900M01K> findByIndex01(String caseLvl);

	List<C900M01K> findByIndex02(String brNo);

	List<C900M01K> findByIndex03(String brClass);
	
	/**
	 * 透過brNo 某XX分行即項目(A,B)撈取授權額度
	 * 指定AB項時只會撈到一筆
	 * @param brNo
	 * @param type
	 * @return
	 */
	C900M01K findByBrNoAndType(String brNo, String type);
	
	/**
	 * 透過brClass第X組分行撈取授權額度
	 * 只會撈到一筆不分AB項
	 * @param brClass
	 * @return
	 */
	C900M01K findByBrClass(String brClass);
}