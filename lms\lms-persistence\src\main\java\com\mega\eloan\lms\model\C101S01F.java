/* 
 * C101S01F.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.SaveCheck;

/** 個金放款信用評分表(無擔用) **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "C101S01F", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo" }))
public class C101S01F extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 是否填列
	 * <p/>
	 * Y/N
	 */
	@Size(max = 1)
	@Column(name = "CHKFLAG", length = 1, columnDefinition = "CHAR(1)")
	private String chkFlag;

	/** 本人及配偶最近年所得 **/
	@Max(value = 25, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE01", columnDefinition = "DECIMAL(3,0)")
	private Integer score01;

	/** 職　　業 **/
	@Max(value = 10, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE02", columnDefinition = "DECIMAL(3,0)")
	private Integer score02;

	/** 工作年資 **/
	@Max(value = 10, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE03", columnDefinition = "DECIMAL(3,0)")
	private Integer score03;

	/** 不動產狀況 **/
	@Max(value = 10, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE04", columnDefinition = "DECIMAL(3,0)")
	private Integer score04;

	/** 家庭狀況 **/
	@Max(value = 5, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE05", columnDefinition = "DECIMAL(3,0)")
	private Integer score05;

	/** 住宅狀況 **/
	@Max(value = 10, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE06", columnDefinition = "DECIMAL(3,0)")
	private Integer score06;

	/** 負債比率 **/
	@Max(value = 10, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE07", columnDefinition = "DECIMAL(3,0)")
	private Integer score07;

	/** 與銀行往來 **/
	@Max(value = 8, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE08", columnDefinition = "DECIMAL(3,0)")
	private Integer score08;

	/** 綜合加分 **/
	@Max(value = 12, groups = SaveCheck.class)
	@Min(value = 0, groups = SaveCheck.class)
	@Column(name = "SCORE09", columnDefinition = "DECIMAL(3,0)")
	private Integer score09;

	/** 合計 **/
	@Max(value = 100, groups = SaveCheck.class)
	@Column(name = "TTLSCORE", columnDefinition = "DECIMAL(3,0)")
	private Integer ttlScore;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得是否填列
	 * <p/>
	 * Y/N
	 */
	public String getChkFlag() {
		return this.chkFlag;
	}

	/**
	 * 設定是否填列
	 * <p/>
	 * Y/N
	 **/
	public void setChkFlag(String value) {
		this.chkFlag = value;
	}

	/** 取得本人及配偶最近年所得 **/
	public Integer getScore01() {
		return this.score01;
	}

	/** 設定本人及配偶最近年所得 **/
	public void setScore01(Integer value) {
		this.score01 = value;
	}

	/** 取得職　　業 **/
	public Integer getScore02() {
		return this.score02;
	}

	/** 設定職　　業 **/
	public void setScore02(Integer value) {
		this.score02 = value;
	}

	/** 取得工作年資 **/
	public Integer getScore03() {
		return this.score03;
	}

	/** 設定工作年資 **/
	public void setScore03(Integer value) {
		this.score03 = value;
	}

	/** 取得不動產狀況 **/
	public Integer getScore04() {
		return this.score04;
	}

	/** 設定不動產狀況 **/
	public void setScore04(Integer value) {
		this.score04 = value;
	}

	/** 取得家庭狀況 **/
	public Integer getScore05() {
		return this.score05;
	}

	/** 設定家庭狀況 **/
	public void setScore05(Integer value) {
		this.score05 = value;
	}

	/** 取得住宅狀況 **/
	public Integer getScore06() {
		return this.score06;
	}

	/** 設定住宅狀況 **/
	public void setScore06(Integer value) {
		this.score06 = value;
	}

	/** 取得負債比率 **/
	public Integer getScore07() {
		return this.score07;
	}

	/** 設定負債比率 **/
	public void setScore07(Integer value) {
		this.score07 = value;
	}

	/** 取得與銀行往來 **/
	public Integer getScore08() {
		return this.score08;
	}

	/** 設定與銀行往來 **/
	public void setScore08(Integer value) {
		this.score08 = value;
	}

	/** 取得綜合加分 **/
	public Integer getScore09() {
		return this.score09;
	}

	/** 設定綜合加分 **/
	public void setScore09(Integer value) {
		this.score09 = value;
	}

	/** 取得合計 **/
	public Integer getTtlScore() {
		return this.ttlScore;
	}

	/** 設定合計 **/
	public void setTtlScore(Integer value) {
		this.ttlScore = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
