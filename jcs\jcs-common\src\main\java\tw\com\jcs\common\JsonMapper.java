package tw.com.jcs.common;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;

/**
 * <pre>
 * JsonMapper
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public class JsonMapper {

    private static final Logger logger = LoggerFactory.getLogger(JsonMapper.class);

    private static ObjectMapper mapper;

    /**
     * 初始化ObjectMapper
     */
    static {
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        SimpleModule module = new SimpleModule();
        module.addSerializer(TWNDate.class, new JsonSerializer<TWNDate>() {
            public void serialize(TWNDate value, JsonGenerator jgen, SerializerProvider provider) throws IOException, JsonProcessingException {
                jgen.writeString(value.toFullTW('/'));
            }
        });
        mapper.registerModule(module);
    }

    /**
     * 將任何的object透過ObjectMapper轉換成JSON字串
     *
     * @param obj
     *            any Object
     * @return JSON string
     */
    public static String toJSON(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonGenerationException e) {
            logger.error("無法將{}轉換成JSON字串：{}", obj, e.getMessage());
        } catch (JsonMappingException e) {
            logger.error("無法將{}轉換成JSON字串：{}", obj, e.getMessage());
        } catch (IOException e) {
            logger.error("無法將{}轉換成JSON字串：{}", obj, e.getMessage());
        }
        return "{}";
    }

    /**
     * 將JSON字串轉換成物件(忽略多餘的欄位)
     * 
     * @param jsonString
     *            要轉換的JSON字串
     * @param resultClass
     *            要轉換的物件類別
     * @return 轉換後的物件
     */
    public static <T> T fromJSON(String jsonString, Class<T> resultClass) {
        T result = null;
        try {
            result = mapper.readValue(jsonString, resultClass);
        } catch (JsonParseException e) {
            logger.error("無法將JSON字串轉換成{}：{}", resultClass, e.getMessage());
        } catch (JsonMappingException e) {
            logger.error("無法將JSON字串轉換成{}：{}", resultClass, e.getMessage());
        } catch (IOException e) {
            logger.error("無法將JSON字串轉換成{}：{}", resultClass, e.getMessage());
        }
        return result;
    }

    /**
     * 將JSON字串轉換成物件(忽略多餘的欄位)
     *
     * @param jsonString
     *            要轉換的JSON字串
     * @param type
     *            要轉換的物件類別
     * @return 轉換後的物件
     */
    public static <T> T fromJSON(String jsonString, TypeReference<T> type) {
        T result = null;
        try {
            result = mapper.readValue(jsonString, type);
        } catch (JsonParseException e) {
            logger.error("無法將JSON字串轉換成{}：{}", type, e.getMessage());
        } catch (JsonMappingException e) {
            logger.error("無法將JSON字串轉換成{}：{}", type, e.getMessage());
        } catch (IOException e) {
            logger.error("無法將JSON字串轉換成{}：{}", type, e.getMessage());
        }
        return result;
    }

}
