/* 
 * MisELCRTBServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services,String  Inc. 
 * 9F,String  No.30,String  Sec.1,  <PERSON>g E. Rd.,  Taipei,  Taiwan,  R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services,  Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services,  Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELCRTBLService;

/**
 * <pre>
 * 企金費率檔ELCRTBL(MIS.ELV47601)
 * </pre>
 * 
 * @since 2012/01/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/01/01, REX, new
 *          </ul>
 */
@Service
public class MisELCRTBLServiceImpl extends AbstractMFAloanJdbc implements
		MisELCRTBLService {

	@Override
	public void insert(List<Object[]> dataList) {
		this.getJdbc().batchUpdate(
				"ELCRTBL.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.DECIMAL, Types.CHAR, Types.DECIMAL,
						Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.DECIMAL,
						Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR }, dataList);

	}

	@Override
	public void delByKey(String cntrNo, String subject, String sDate,
			String rtype) {
		this.getJdbc().update("ELCRTBL.delByUniqueKey",
				new Object[] { cntrNo, subject, sDate, rtype });

	}

}
