/* 
 * L140M02SDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L140M02SDao;
import com.mega.eloan.lms.model.L140M02S;

/** 應收帳款買方額度資訊主檔 **/
@Repository
public class L140M02SDaoImpl extends LMSJpaDao<L140M02S, String> implements
		L140M02SDao {

	@Override
	public L140M02S findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140M02S> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		search.addOrderBy("type", false);
		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L140M02S> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140M02S findByUniqueKey(String mainId, String type, Integer itemSeq) {
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (itemSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq",
					itemSeq);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140M02S> findByIndex01(String mainId, String type,
			Integer itemSeq) {
		ISearch search = createSearchTemplete();
		List<L140M02S> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (itemSeq != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemSeq",
					itemSeq);

		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M02S> findByMainIdType(String mainId, String type) {
		ISearch search = createSearchTemplete();
		List<L140M02S> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);

		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140M02S> findByMainIdTypeCustId(String mainId, String type,
			String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		List<L140M02S> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		search.addOrderBy("itemSeq", false);
		search.setMaxResults(Integer.MAX_VALUE);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public L140M02S findByMainIdTypeCustIdCntrNo(String mainId, String type,
			String custId, String dupNo, String cntrNo) {
		ISearch search = createSearchTemplete();
		List<L140M02S> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);

		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

}