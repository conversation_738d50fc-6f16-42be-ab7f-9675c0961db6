/* 
 *ObsdbELF383ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.sql.Types;
import java.util.List;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF383Service;

/**
 * <pre>
 * 授信額度檔  ELF383
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF383ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF383Service {

	/**
	 * 未使用--作廢
	 */
	@Override
	public void insertX_NOT_USE(String BRNID, List<Object[]> dataList) {
		this.getJdbc(BRNID).batchUpdate(
				"ELF383.insert",
				new int[] { Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.CHAR, Types.DECIMAL, Types.DECIMAL,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,

						Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.DECIMAL,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.DECIMAL,

						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL,

						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.DECIMAL, Types.CHAR,

						Types.DECIMAL, Types.CHAR, Types.DECIMAL, Types.CHAR,
						Types.CHAR,

						Types.CHAR, Types.DECIMAL, Types.CHAR, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.DECIMAL,
						Types.CHAR, Types.DECIMAL, Types.DECIMAL, Types.CHAR,
						Types.CHAR, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.DECIMAL, Types.DECIMAL, Types.DECIMAL,
						Types.DECIMAL, Types.CHAR, Types.CHAR, Types.CHAR,
						Types.DECIMAL }, dataList);

	}

	@Override
	public void deleteByKey(String BRNID, String custId, String dupNo,
			String cntrNo, String sDate) {
		this.getJdbc(BRNID).update("ELF383.delByKey",
				new Object[] { custId, dupNo, cntrNo, sDate });

	}

	@Override
	public void insert(String BRNID, Object[] dataList) {
		this.getJdbc(BRNID).update("ELF383.insert", dataList);
	}

}
