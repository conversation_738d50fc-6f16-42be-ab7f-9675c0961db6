---------------------------------------------------------
-- LMS.L140M01C_BF 額度授信科目資料檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01C_BF;
CREATE TABLE LMS.L140M01C_BF (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	LOANTP        VARCHAR(4)    not null,
	SUBJSEQ       VARCHAR(2)   ,
	<PERSON><PERSON><PERSON><PERSON>DS<PERSON>      VARCHAR(200)  ,
	LMTDAYS       DECIMAL(5,0) ,
	L<PERSON><PERSON>HER      CHAR(01)     ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01C_BF PRIMARY KEY(OID)
) IN EL_DATA_4KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01C_BF01;
CREATE UNIQUE INDEX LMS.XL140M01C_BF01 ON LMS.L140M01C_BF (MAINID, LOANTP);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01C_BF IS '額度授信科目資料檔';
COMMENT ON LMS.L140M01C_BF (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	LOANTP        IS '科目代碼', 
	SUBJSEQ       IS '科目順序', 
	SUBJDSCR      IS '科目補充說明', 
	LMTDAYS       IS '清償期限－天數', 
	LMTOTHER      IS '清償期限－詳其他敘作條件', 
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
