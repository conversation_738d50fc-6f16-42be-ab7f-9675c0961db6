var initDfd = initDfd || $.Deferred();
var inits = {
	fhandle: "lms2105m01formhandler",
	ghaddle: "lms2105gridhandler"
};

pageJsInit(function() {
	$(function() {
		
		//驗證readOnly狀態
		function checkReadonly() {
			var auth = (responseJSON ? responseJSON.Auth : {}); //權限
			if (auth.readOnly || responseJSON.mainDocStatus != "01O") {
				inits.toreadOnly = true;
				return true;
			}
			inits.toreadOnly = false;
			return false;
		}

		var CntrNoAPI = {
			/** 當現請額度大於攤貸總金額時 會出現 grid選擇  將餘額加至哪筆 */
			l140m01eAmtBox: function(amt) {
				//L140M01a.message70=聯行攤貸金額尚餘 {0}元，請選擇要將餘額加進何筆資料!
				$("#l140m01eAmtMsg").html(i18n.lms1405s02['L140M01a.message70'].replace("{0}", amt))
				$("#l140m01eAmtGrid").setGridParam({//重新設定grid需要查到的資料
					postData: {
						formAction: "queryL210s01a",
						chgFlag: "2"
					},
					search: true
				}).trigger("reloadGrid");

				$("#l140m01eAmtBox").thickbox({
					title: "",
					width: 680,
					height: 350,
					align: "center",
					valign: "bottom",
					modal: true,
					i18n: i18n.def,
					buttons: {
						"sure": function() {
							var $gridAMT = $("#l140m01eAmtGrid");
							var id = $gridAMT.getGridParam('selrow');
							if (id == null || !id) {
								//action_005=請先選取一筆以上之資料列
								return CommonAPI.showErrorMessage(i18n.def['action_005']);
							}
							var oid = $gridAMT.getRowData(id).oid;
							$.thickbox.close();
							$.ajax({
								handler: inits.fhandle,
								action: "saveL140m01eAmt",
								data: {
									oid: oid,
									amt: util.delComma(amt)
								},
								success: function(responseData) {
									if (responseJSON.page == "04") {
										$("#gridviewitemChildren3").trigger("reloadGrid");
									}

								}
							});
						},
						"cancel": function() {
							$.thickbox.close();
						}
					}
				});
			}
		};

		$.form.init({
			formHandler: inits.fhandle,
			formPostData: {//把form上貼上資料
				formAction: "queryL210m01a",
				oid: responseJSON.oid
			},
			loadSuccess: function(json) {
				var auth = (responseJSON ? responseJSON.Auth : {}); //權限
				initDfd.resolve(json, auth);
				if (checkReadonly()) {
					$("fieldset,form").lockDoc();
				}
			}
		});//close form init
		/** 聯行攤貸比例grid  */
		$("#l140m01eAmtGrid").iGrid({
			handler: "lms2105gridhandler",
			rowNum: 10,
			rownumbers: true,
			multiselect: true,
			hideMultiselect: false,
			postData: {
				formAction: "queryL210s01a",
				chgFlag: "2"
			},
			rowNum: 10,
			autowidth: true,
			colModel: [{
				name: 'shareRate2',
				hidden: true
			}, {
				colHeader: i18n.lms2105m01["L210M01A.s03title09"],//"攤貸分行",
				name: 'shareBrId',
				align: "left",
				width: 110,
				sortable: true
			}, {
				colHeader: i18n.lms2105m01["L210M01A.s03title10"],//"攤貸金額",
				name: 'shareAmt',
				width: 160,
				sortable: true,
				align: "right",
				formatter: 'currency',
				formatoptions: {
					thousandsSeparator: ",",
					removeTrailingZero: true,
					decimalPlaces: 2//小數點到第幾位
				}
			}, {
				colHeader: i18n.lms2105m01["L210M01A.s03title11"],//"攤貸比例",
				width: 140,
				name: 'showRate',
				align: "right",
				sortable: true
			}, {
				colHeader: i18n.lms2105m01["L210M01A.s03title12"],//"額度序號",
				width: 140,
				name: 'shareNo',
				sortable: true
			}, {
				name: 'oid',
				hidden: true
			}]
		});


		var btn = $("#buttonPanel");
		btn.find("#btnSave").click(function(showMsg) {
			$.ajax({
				handler: inits.fhandle,
				data: {//把資料轉成json
					formAction: "saveL210m01a",
					page: responseJSON.page,
					txCode: responseJSON.txCode,
					showMsg: showMsg
				},
				success: function(obj) {
					$('body').injectData(obj);
					CommonAPI.triggerOpener("gridview", "reloadGrid");
					//當攤貸時還有餘額，要跳出餘額修改視窗
					if (obj.l140m01eAmt && obj.l140m01eAmt != 0) {
						CntrNoAPI.l140m01eAmtBox(obj.l140m01eAmt);
					}
				}
			});

		}).end().find("#btnDelete").click(function() {
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
				if (b) {
					$.ajax({
						handler: inits.fhandle,
						data: {
							formAction: "delete",
							mainOid: $("#mainOid").val()
						}
					});
				}
			});

		}).end().find("#btnSend").click(function() {
			sendBoss();
		}).end().find("#btnAccept").click(function() {
			flowAction({
				flowAction: true
			});
		}).end().find("#btnCheck").click(function() {
			openCheck();
		}).end().find("#btnPrint").click(function() {
			$.form.submit({
				url: "../../simple/FileProcessingService",
				target: "_blank",
				data: {
					mainId: responseJSON.mainId,
					mainOid: $("#mainOid").val(),
					fileDownloadName: "lms2105r01.pdf",
					serviceName: "lms2105r01rptservice"
				}
			});
		});


		//呈主管 -  編製中
		function sendBoss() {
			$.ajax({
				handler: inits.fhandle,
				data: {
					formAction: "checkData"
				},
				success: function(json) {
					$(".boss").setItems({
						item: json.bossList
					});
					//confirmApply=是否呈主管覆核？
					CommonAPI.confirmMessage(i18n.def["confirmApply"], function(b) {
						if (b) {
							$("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
								//L210M01A.bt05=呈主管覆核
								title: i18n.lms2105m01['L210M01A.bt05'],
								width: 500,
								height: 180,
								modal: true,
								valign: "bottom",
								align: "center",
								readOnly: false,
								i18n: i18n.def,
								buttons: {
									"sure": function() {
										if ($("#manager").val() == "") {
											//L210M01A.message05=請選擇 L210M01A.managerId=單位/授權主管 
											return CommonAPI.showErrorMessage(i18n.lms2105m01['L210M01A.message05'] + i18n.lms2105m01['L210M01A.managerId']);
										}

										flowAction({
											page: responseJSON.page,
											account: $("#accountPerson").val(),
											manager: $("#manager").val()
										});
										$.thickbox.close();
									},

									"cancel": function() {
										$.thickbox.close();
									}
								}
							});
						}
					});
				}
			});
		}

		//待覆核  - 覆核
		function openCheck() {
			$("#openCheckBox").thickbox({ // 使用選取的內容進行彈窗
				//L210M01A.bt04=覆核
				title: i18n.lms2105m01['L210M01A.bt04'],
				width: 100,
				height: 150,
				modal: true,
				readOnly: false,
				valign: "bottom",
				align: "center",
				i18n: i18n.def,
				buttons: {
					"sure": function() {

						var val = $("[name=checkRadio]:checked").val();
						if (!val) {
							//L210M01A.message05=請選擇
							return CommonAPI.showMessage(i18n.lms2105m01['L210M01A.message05']);
						}
						$.thickbox.close();
						switch (val) {
							case "1":
								//一般退回到編製中01O
								//L210M01A.message02=該案件是否退回經辦修改？要退回請按【確定】，不退回請按【取消】
								CommonAPI.confirmMessage(i18n.lms2105m01['L210M01A.message02'], function(b) {
									if (b) {
										flowAction({
											flowAction: "back"
										});
									}
								});
								break;
							case "2":
								//核定

								//L210M01A.message03=該案件是否核准？確定請按【確定】，否則請按【取消】離開
								CommonAPI.confirmMessage(i18n.lms2105m01["L210M01A.message03"], function(b) {
									if (b) {
										checkDate();
									}
								});
								break;
							case "3":
								//駁回
								//L210M01A.message04=該案件是否駁回？確定請按【確定】，否則請按【取消】離開 ！
								CommonAPI.confirmMessage(i18n.lms2105m01["L210M01A.message04"], function(b) {
									if (b) {
										flowAction({
											flowAction: "noCheck"
										});
									}
								});
								break;
						}
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}

		//輸入核定日期視窗
		function checkDate() {
			$("#forCheckDate").val(CommonAPI.getToday());
			$("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
				//L210M01A.message01= 請輸入核定日
				title: i18n.lms2105m01['L210M01A.message01'],
				width: 100,
				height: 100,
				modal: true,
				readOnly: false,
				valign: "bottom",
				align: "center",
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						var forCheckDate = $("#forCheckDate").val();
						if ($.trim(forCheckDate) == "") {
							//L210M01A.message01= 請輸入核定日
							return CommonAPI.showErrorMessage(i18n.lms2105m01['L210M01A.message01']);
						}
						flowAction({
							flowAction: "check",
							checkDate: forCheckDate
						});
						$.thickbox.close();
					},

					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		}

		function flowAction(sendData) {
			$.ajax({
				handler: inits.fhandle,
				data: $.extend({
					formAction: "flowAction",
					mainOid: $("#mainOid").val()
				}, (sendData || {})),
				success: function() {
					CommonAPI.triggerOpener("gridview", "reloadGrid");
					API.showPopMessage(i18n.def["runSuccess"], window.close);
				}
			});
		}

	});
});