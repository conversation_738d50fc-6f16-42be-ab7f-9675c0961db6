/* 
 * L130S01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L130S01A;

/** 異常通報表明細檔 **/
public interface L130S01ADao extends IGenericDao<L130S01A> {

	L130S01A findByOid(String oid);

	List<L130S01A> findByMainId(String mainId);

	/**
	 * 取得可上傳異常通報明細檔群組(授管處核定與分行已辦事項才要上傳)
	 * 
	 * @param mainId
	 * @return
	 */
	List<L130S01A> findInsertData(String mainId);

	List<L130S01A> findByMainIdAndBranchKind(String mainId, String branchKind);

	L130S01A findByUniqueKey(String mainId);

	List<L130S01A> findByIndex01(String mainId);

	// J-GGG-XXXX
	public List<L130S01A> findInsertDataByCaseType(String mainId,
			String whoDecide);
}