<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
 xsi:schemaLocation="
 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
 http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-2.0.xsd ">

<util:map id="eLoanCMSSql" map-class="java.util.HashMap" key-type="java.lang.String">
    
    <!-- ########## -->
    <!-- 擔保品共用 -->
    <!-- ########## -->
	<!-- 取得不動產資料 -->
		<entry key="C101M01.getShowDataCollType1For140">
			<value>
				SELECT 
					T1.CURRCD,T1.LNDNUM + T1.BLDNUM AS COUNTNUM,T1.ESTDATE,T3.LNDTAREAP,T3.BLDAREAP,
					T3.OPTION2,T3.INAMT,T2.SETAMT,T2.SETODR ,T1.BRANCH,T1.LOANAMT 
					FROM CMS.C100M01 T1 LEFT JOIN CMS.C100M03 T2 ON T1.MAINID = T2.MAINID
										LEFT JOIN CMS.C101M06 T3 ON T1.MAINID = T3.MAINID
				WHERE T1.MAINID = ?
				ORDER BY T2.SETODR
			</value>
		</entry>
	<!-- 取得不動產資料-土地跟建物 -->
		<entry key="C101M0304.getlandBuildDataCollType1For140">
			<value>
				SELECT OID,TARGET FROM CMS.C101M03 WHERE MAINID = ?
				UNION ALL
				SELECT OID,TARGET FROM CMS.C101M04 WHERE MAINID = ?
			</value>
		</entry>
	<!-- 取得權利質權需要資料 -->
		<entry key="C100M01.getShowDataFor140">
			<value>
				SELECT COLLTYP1,COLLTYP2,LNDNUM,CURRCD,APPAMT,LOANAMT,BRANCH ,ESTDATE FROM CMS.C100M01 WHERE MAINID = ?
			</value>
		</entry>
		<!-- 取得建物資料 -->
		<entry key="C101M04.getDataByMainId">
			<value>
				SELECT * FROM CMS.C101M04 C1 
					WHERE C1.MAINID= ? 
						ORDER BY SEQNUM,MBAREA ASC 
			</value>
		</entry>
		<!-- 取得土地資料 -->
		<entry key="C101M03.getDataByMainId">
			<value>
				SELECT * FROM CMS.C101M03 C1 
					WHERE C1.MAINID= ? ORDER BY TOTHOLD DESC  

			</value>
		</entry>
		<!-- 取得段小段資料 -->
		<entry key="C101M09.getC101M09ByCityAreaIr48">
			<value>
				SELECT * FROM CMS.C101M09 C1 
					WHERE C1.CITYID=? AND C1.AREAID=? AND C1.IR48=?

			</value>
		</entry>
		
		
		
	<entry key="C100M01.getSetDataByOid">
        <value>
        SELECT 
			DISTINCT M1.typCd, M1.branch, M1.custId, M1.dupNo, M1.collNo, 
        M1.collTyp1, M1.collTyp2, M1.currCd, M1.appAmt, M1.loanAmt, M3.setCurr, 
        M3.setAmt, M3.setOdr, M1.eraDate, 
		
		CASE WHEN M1.eraDate IS NULL THEN 0 
        				ELSE 1 END AS isEra ,m1.oid,m1.mainid
		FROM 
			CMS.C100M01 M1 RIGHT OUTER JOIN CMS.C100S03A S3 
			ON M1.MAINID = S3.MAINID LEFT OUTER JOIN CMS.C100M03 M3 
			ON M1.MAINID = M3.MAINID 
		WHERE 
			M1.collTyp1 != '06' 
			AND M1.DOCSTATUS IN ('15B','16B') 
       		AND M1.DELETEDTIME IS NULL AND M1.OID = ?
        </value>
    </entry>
	
	<entry key="C101S03B.getOwnerByMainId">
        <value>
        SELECT * FROM CMS.C101S03B WHERE MAINID = ?
        </value>
    </entry>
	<entry key="C100M03.getByMainId">
        <value>
        SELECT * FROM CMS.C100M03 WHERE MAINID = ?
        </value>
    </entry>
	
	<entry key="C101M04.getBuildingByMainId">
        <value>
        SELECT * FROM CMS.C101M04 WHERE MAINID = ? ORDER BY SEQNUM ASC
        </value>
    </entry>
	
	<entry key="C101M03.getLandByMainId">
        <value>
        SELECT * FROM CMS.C101M03 WHERE MAINID = ? ORDER BY SEQNUM ASC
        </value>
    </entry>
	
	<entry key="C100M01.getByOid">
        <value>
        SELECT * FROM CMS.C100M01 WHERE OID = ?
        </value>
    </entry>
	
	<entry key="C100M01.getByMainId">
        <value>
        SELECT * FROM CMS.C100M01 WHERE MAINID = ?
        </value>
    </entry>
	
	<entry key="C101M06.getByMainId">
        <value>
        SELECT * FROM CMS.C101M06 WHERE MAINID = ?
        </value>
    </entry>
	
	<entry key="C101M05.getByMainId">
        <value>
        SELECT * FROM CMS.C101M05 WHERE MAINID = ?
        </value>
    </entry>
	
	<entry key="C101M08.getByMainId">
        <value>
        SELECT * FROM CMS.C101M08 WHERE MAINID = ?
        </value>
    </entry>
	
	
	<!-- 抓前准抵押權順位金額-->
	<entry key="C101S05A.getByMainId">
        <value>
        SELECT RIGHTAMT FROM CMS.C101S05A  WHERE RIGHTKIND='1' AND SEQNUM=1 AND mainId=?
        </value>
    </entry>
	
	<!-- J-107-0368 次順位房貸 -->
	<entry key="C101S05A.getByCustIdDupNo">
        <value>
        SELECT * FROM CMS.C101S05A  
		WHERE RIGHTKIND='1' AND SEQNUM=1 
		AND mainId in (select mainId from CMS.C100M01 where custId=? and dupNo=? and docStatus in ('15B','16B') and deletedtime is null)
        </value>
    </entry>
	
	
	<!--
	取得權利質權 各個類別的名稱
	銀行定存單明細檔 C103S01A
	國庫券明細資料檔 C103S01B
	公債明細資料檔 C103S01C
	權利質權金融債券明細資料檔 C103S01D
	權利質權公司債明細資料檔 C103S01F
	權利質權股票明細資料檔 C103S01G
	權利質權受益憑證明細資料檔 C103S01H
	-->
	<entry key="C101M01.getTy03ShowNameByMainId">
        <value>
        SELECT DEPBRCH  AS SHOWNAME FROM CMS.C103S01A  WHERE MAINID = ?
		UNION
		SELECT STKNM  AS SHOWNAME FROM  CMS.C103S01B WHERE MAINID = ?
		UNION
		SELECT BONDNM  AS SHOWNAME FROM  CMS.C103S01C WHERE MAINID = ?
		UNION
		SELECT BONDNM  AS SHOWNAME FROM  CMS.C103S01D WHERE MAINID = ?
		UNION
		SELECT CBNM AS SHOWNAME FROM  CMS.C103S01F WHERE MAINID = ?
		UNION
		SELECT STKNM AS SHOWNAME FROM  CMS.C103S01G WHERE MAINID = ?
		UNION
		SELECT FUNDNM AS SHOWNAME FROM CMS.C103S01H WHERE MAINID = ?
        </value>
    </entry>
	<entry key="C100M01.getSetDataByBranchAndCustId">
        <value>
		select  * from  cms.c100m01 where custid= ? and dupno = ? and  branch= ? and DOCSTATUs in ('15B','16B') and DELETEDTIME is null
		</value>
    </entry>
	
	<entry key="C102S01A.getByMainId">
        <value>
		select  * from  cms.C102S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01A.getByMainId">
        <value>
		select  * from  cms.C103S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01B.getByMainId">
        <value>
		select  * from  cms.C103S01B where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01C.getByMainId">
        <value>
		select  * from  cms.C103S01C where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01D.getByMainId">
        <value>
		select  * from  cms.C103S01D where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01E.getByMainId">
        <value>
		select  * from  cms.C103S01E where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01F.getByMainId">
        <value>
		select  * from  cms.C103S01F where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01G.getByMainId">
        <value>
		select  * from  cms.C103S01G where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01H.getByMainId">
        <value>
		select  * from  cms.C103S01H where mainId = ?
		</value>
    </entry>
	
	<entry key="C103S01I.getByMainId">
        <value>
		select  * from  cms.C103S01I where mainId = ?
		</value>
    </entry>
	
	<entry key="C104S01.getByMainId">
        <value>
		select  * from  cms.C104S01 where mainId = ?
		</value>
    </entry>
	
	<entry key="C105S01A.getByMainId">
        <value>
		select  * from  cms.C105S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C105S01B.getByMainId">
        <value>
		select  * from  cms.C105S01B where mainId = ?
		</value>
    </entry>
	
	<entry key="C105S01C.getByMainId">
        <value>
		select  * from  cms.C105S01C where mainId = ?
		</value>
    </entry>
	
	<entry key="C105S01D.getByMainId">
        <value>
		select  * from  cms.C105S01D where mainId = ?
		</value>
    </entry>
	
	<entry key="C106S01A.getByMainId">
        <value>
		select  * from  cms.C106S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C107S01A.getByMainId">
        <value>
		select  * from  cms.C107S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C108S01A.getByMainId">
        <value>
		select  * from  cms.C108S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C109S01A.getByMainId">
        <value>
		select  * from  cms.C109S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C110S01A.getByMainId">
        <value>
		select  * from  cms.C110S01A where mainId = ?
		</value>
    </entry>
	
	<entry key="C110S01B.getByMainId">
        <value>
		select  * from  cms.C110S01B where mainId = ?
		</value>
    </entry>
	
	<entry key="C110S01C.getByMainId">
        <value>
		select  * from  cms.C110S01C where mainId = ?
		</value>
    </entry>
	
	<entry key="C101S03A.getByMainId">
        <value>
		select  * from  cms.C101S03A where mainId = ?
		</value>
    </entry>
	
	<entry key="C101S03B.getByMainId">
        <value>
		select  * from  cms.C101S03B where mainId = ?
		</value>
    </entry>
	
	<entry key="C102S01D.getByMainId">
        <value>
		select  * from  cms.C102S01D where mainId = ?
		</value>
    </entry>
	<!--  查詢村里 -->
	<entry key="CMS.C101M15ByLOCATE1DESC">
        <value>
		SELECT LOCATE3CODE,LOCATE3DESC FROM CMS.C101M15  WHERE LOCATE1DESC=? AND LOCATE2DESC=?  ORDER BY LOCATE3CODE
		</value>
    </entry>
	<!--  查詢村里2 -->
	<entry key="CMS.C101M15ByLOCATE3CODE">
        <value>
		SELECT * FROM CMS.C101M15  WHERE LOCATE3CODE=?  ORDER BY LOCATE3CODE
		</value>
    </entry>
	<!-- 取得大段-->
	<entry key="CMS.C101M09FindSECTION1">
        <value>
			SELECT 
					A.CODEDESC3 AS CITYID,
					B.CODEDESC AS CITYNAME,
					A.CODEDESC2 AS AREAID,
					A.CODEDESC AS AREANAME,
					D.IR48,
					D.SECTION1 AS SITE3,
					D.SECTION2 AS SITE4
			 FROM 
				 (SELECT * FROM COM.BCODETYPE WHERE CODETYPE like 'counties%' and locale = 'zh_TW' and length(codevalue) >=3 order by CODETYPE asc) AS A
				  LEFT JOIN 
				(SELECT * FROM COM.BCODETYPE WHERE CODETYPE = 'cms1010_cityId' and locale = 'zh_TW') AS B ON A.CODEDESC3=B.CODEVALUE
				  LEFT JOIN CMS.C101M09 AS D ON A.CODEDESC2=D.AREAID
				WHERE B.CODEDESC=? AND A.CODEDESC=?
		</value>
    </entry>
	
	<!-- 取得小段-->
	<entry key="CMS.C101M09FindSECTION2">
        <value>
		SELECT 
					A.CODEDESC3 AS CITYID,
					B.CODEDESC AS CITYNAME,
					A.CODEDESC2 AS AREAID,
					A.CODEDESC AS AREANAME,
					D.IR48,
					D.SECTION1 AS SITE3,
					D.SECTION2 AS SITE4
			 FROM 
				 (SELECT * FROM COM.BCODETYPE WHERE CODETYPE like 'counties%' and locale = 'zh_TW' and length(codevalue) >=3 order by CODETYPE asc) AS A
				  LEFT JOIN 
				(SELECT * FROM COM.BCODETYPE WHERE CODETYPE = 'cms1010_cityId' and locale = 'zh_TW') AS B ON A.CODEDESC3=B.CODEVALUE
				  LEFT JOIN CMS.C101M09 AS D ON A.CODEDESC2=D.AREAID
				WHERE B.CODEDESC=? AND A.CODEDESC=? AND D.SECTION1=?
		</value>
    </entry>
		
	<entry key="C100S03A.CollInfo">
		<!--
		參考 cms-config〉eLoanSQL.xml〉<entry key="MIS.COLLMSTR"> 
		【COLLMSTR、COLLCNTR】的 PK 皆為 【TYPCD + BRANCH + CUSTID + DUPNO + COLLNO】
		C01 的鑑估值 較特別,參考: gfnDB2GetMISColl_Data, gfnGetColl0102Value
		
		SELECT DISTINCT MIS.COLLCNTR.TYPCD,MIS.COLLCNTR.DUPNO,MIS.COLLCNTR.BRANCH,MIS.COLLCNTR.CUSTID,
		MIS.COLLCNTR.CNTRNO,MIS.COLLCNTR.COLLNO,MIS.COLLMSTR.CURR,
		MIS.COLLMSTR.APPAMT,MIS.COLLMSTR.LOANAMT,MIS.COLLMSTR.TIMEVAL,MIS.COLLMSTR.LAWVAL
		FROM MIS.COLLCNTR LEFT OUTER JOIN MIS.COLLMSTR ON
		(MIS.COLLCNTR.COLLNO=MIS.COLLMSTR.COLLNO AND
		MIS.COLLCNTR.CUSTID=MIS.COLLMSTR.CUSTID AND MIS.COLLCNTR.DUPNO=MIS.COLLMSTR.DUPNO
		AND MIS.COLLCNTR.BRANCH=MIS.COLLMSTR.BRANCH) WHERE 
		LTRIM(RTRIM(MIS.COLLCNTR.CNTRNO)) in ({0})
		-->
        <value>
        <![CDATA[
		select A.MAINID,A.TYPCD, A.BRANCH ,A.CUSTID ,A.DUPNO ,A.COLLNO ,A.COLLKIND
		, B.CNTRNO
		, TRIM(A.CURRCD) AS CURR
		, A.COLLTYP1
		, A.APPAMT AS C100M01_APPAMT
		, CASE A.COLLKIND WHEN '2' THEN COALESCE(A.LOANAMT, NULL) ELSE COALESCE(A.LOANTWD, NULL) END AS LOANAMT
		, LOANRT, LOANUSD
		from CMS.C100M01 A JOIN CMS.C100S03A B  ON A.MAINID=B.MAINID
	    where A.docStatus in ('15B','16B','17S') and  B.CNTRNO=?
		AND A.DELETEDTIME IS NULL 
		]]>
		</value>
    </entry>
	
	<entry key="C100S03A.byCustIdDupNo">		
        <value>
        <![CDATA[
		select A.OID,A.MAINID,A.TYPCD, A.BRANCH ,A.CUSTID ,A.DUPNO ,A.COLLNO, B.CNTRNO, A.DOCSTATUS		
		from CMS.C100M01 A JOIN CMS.C100S03A B  ON A.MAINID=B.MAINID
	    where A.docStatus in ('15B','16B','17S') and  A.CUSTID=? and A.DUPNO=?
		AND A.DELETEDTIME IS NULL 
		]]>
		</value>
    </entry>
	<entry key="C101M06.findC01AppAmt">
		<value>
		<!--
		c100m01.loanAmt=c101m06.ttlAmt(本案押品應計放款值 已扣除前順位抵押金額)
		c100m01.priAmt=前順位金額合計
		-->
        <![CDATA[
		  	select (case when D=0       THEN APPVAL
						 when D=LOANAMT THEN LAWVAL
						 ELSE                TIMEVAL
						 END )as APPAMT
				   ,T2.* 
			FROM(
				select T1.*, (T1.A_ADD_B - T1.PRIAMT) as D 
				FROM(
					select T.*
					,(case when OPTION1<OPTION2 then TIMEVAL else LAWVAL end ) as APPVAL
					,COALESCE((select CASE WHEN COLLTYP1='09' AND COLLTYP2='01' THEN MONEYTO 
					                       ELSE PRIAMT END from cms.c100m01 where mainId=T.MAINID)
							 ,0)AS PRIAMT  
					,COALESCE((select LOANAMT from cms.c100m01 where mainId=T.MAINID),0)AS LOANAMT  
					from
					(select MAINID,(COALESCE(LNDMONEY,0) +COALESCE(BLDMONEY,0) ) as A_ADD_B
										,COALESCE(OPTION1,0) as OPTION1
										,COALESCE(OPTION2,0) as OPTION2
										,COALESCE(inAmt,0) as TIMEVAL
										,(COALESCE(lndAmt,0) +COALESCE(bldAmt,0) ) as LAWVAL 
					 from cms.c101m06 where mainid=? ) T
				) T1
			) T2
		]]>
		</value>
	</entry>
				<!-- 從額度明細表MAINID取得引進的不動產擔保品的購價/鑑價/估價/增值稅/預提折舊/房屋稅與地價稅金額 -->
    <entry key="C101M06.findByL140M01A">
        <value>
        	WITH tmp_01 AS (
				SELECT MAINID,SUM(DEAMT) AS SDEAMT 
					FROM CMS.C101M04 WHERE MAINID IN (
                SELECT MAINID FROM CMS.C100M01 WHERE OID IN (
                    SELECT CMSOID FROM LMS.L140M01O WHERE MAINID= ? )) GROUP BY MAINID)
					SELECT SUM(A1.INAMT) AS SINAMT,SUM(A1.LNDAMT) AS SLNDAMT, SUM(A1.BLDAMT) AS SBLDAMT, SUM(A1.LNDTAX) AS SLNDTAX, 
					SUM(A1.DISAMT) AS SDISAMT, SUM(A1.MINAMT) AS SMINAMT, SUM(A1.ttlAmt) as STTLAMT, SUM(A0.SDEAMT) AS SDEAMT 
                FROM tmp_01 as A0 JOIN CMS.C101M06 AS A1 
                    ON A0.MAINID=A1.MAINID
        </value>
    </entry>
	<!-- 取得擔保品座落縣、市、區、段名稱 -->	
	<entry key="C101M09.C101M15.getCollateralLocationByDistrict">
        <value>
        	<![CDATA[
	        	Select  a.IR48 as section_code, CONCAT(a.SECTION1, a.SECTION2) as section_name, a.AREAID as district_code, 
				        b.LOCATE2DESC as district_name, b.LOCATE1DESC as city_county_name, b.locate3code as village_code, 
				        b.LOCATE3DESC as village_name
				From  CMS.C101M09 a
				left outer join  CMS.C101M15 b on a.AREAID = b.AREAID_DESC2 and a.CITYID = b.CITYID_VALUE
				WHERE b.AREAID_DESC2 = ?
			]]>
        </value>
    </entry>

	<entry key="C101M04.C100M01.getHouseStatus">
        <value>
        	<![CDATA[
	        	select m04.PSTATUS2 from cms.C101M04 m04
				Left outer join cms.C100M01 m01 on m04.MAINID = m01.MAINID
				where m01.oid = ?
			]]>
        </value>
    </entry>
	<entry key="getCntrnoByComparisonCollateralOwnerId">
        <value>
        	<![CDATA[
				select distinct m01a.CNTRNO
				From LMS.L140M01A m01a 
				left outer join lms.L140M01O m01o on m01a.MAINID = m01o.MAINID
				left outer join lms.C100M01  a on m01o.CMSOID = a.oid 
				left outer join cms.C101S03B b on b.MAINID = a.MAINID
				Where m01a.mainId != ? and m01a.deletedtime is null and m01a.property != ''7'' and m01a.property != ''8'' and OWNERID in ({0})
			]]>
        </value>
    </entry>
	
	<entry key="C100M01.C103S01H.getSpecificMoneyTrustCollateralRightsPledgeData">
        <value>
        	<![CDATA[
				SELECT b.fundCurr, a.collTyp2
				From cms.C100M01 a
				join cms.C103S01H b on a.MAINID = b.MAINID
				where a.OID = ? and b.TRUSTFG = 'Y'
			]]>
		</value>
    </entry>
	
	<!--J-110-0179 因應決策平台快速審核(信貸) 疑似人頭戶檢核功能 - 檢查保證人id, 是否在別的案件擔保品所有權人id相同-->
    <entry key="getCntrnoOfSameAsCollateralOwnerIdInfo">
        <value>
            <![CDATA[
			select distinct m01a.CNTRNO
			From LMS.L140M01A m01a 
			left outer join lms.L140M01O m01o on m01a.MAINID = m01o.MAINID
			left outer join lms.C100M01  a on m01o.CMSOID = a.oid 
			left outer join cms.C101S03B b on b.MAINID = a.MAINID
			Where m01a.deletedtime is null and m01a.property != ''7'' and m01a.property != ''8'' and OWNERID in ({0})
			]]>
        </value>
    </entry>
	
	<!-- 取得縣市區域名稱 -->
	<entry key="CMS.C101M15.getAllCollateralAreaName">
        <value>
        	<![CDATA[
				select AREAID_DESC2, LOCATE1DESC, LOCATE2DESC from CMS.C101M15 GROUP BY AREAID_DESC2, LOCATE1DESC, LOCATE2DESC
			]]>
		</value>
    </entry>
	
	<entry key="CMS.C101M06.getTSumAmtAdjInRealEstateTypeByC100m01Oid">
        <value>
        	<![CDATA[
				select s.tSumAmtAdj
				from cms.c101m06 s
				inner join cms.c100m01 m on s.mainid = m.mainid
				where NVL(m.RptTypeFlag, '' ) <> '' and m.CollTyp1 = '01' and m.oid = ?
			]]>
		</value>
    </entry>
	
	<entry key="C100M01.C103S01H.getSpecificMoneyTrustCollateralDebitCurrency">
        <value>
        	<![CDATA[
				SELECT s01h.fundCurr
				FROM CMS.C100M01 m01
				join CMS.C103S01H  s01h on m01.mainId = s01h.MAINID
				where m01.collTyp2 = '08' and s01h.trustFG = 'Y' and m01.oid = ?
				group by s01h.fundCurr
			]]>
		</value>
    </entry>
	
	<entry key="C100M01.C101M03.C100S03A.getSpaceDataOfCollateralByCntrNo">
        <value>
        	<![CDATA[
				SELECT m03.landKind
				FROM CMS.C100M01 m01
				join CMS.C101M03 m03 on m01.mainId = m03.MAINID
				join CMS.C100S03A s03a on m01.mainId = s03a.MAINID
				where s03a.CNTRNO = ? group by m03.landKind
			]]>
		</value>
    </entry>
	
	<entry key="C100M01.C100S03A.getCntrNoBeenSetByC100m01Oid">
        <value>
        	<![CDATA[
				select B.CNTRNO
				from CMS.C100M01 A 
				JOIN CMS.C100S03A B  ON A.MAINID=B.MAINID
				where A.OID = ?
				AND A.DELETEDTIME IS NULL
			]]>
		</value>
    </entry>
	
</util:map>

</beans>
