var L901VAction = {
    fhandler: "lms9031m01formhandler",
    ghandler: 'lms9031gridhandler',
    $form: $("#lms9031tabForm"),
    gridId: "#gridfile",
    /**
     * 更新grid
     */
    _tiggerGrid: function(){
        $(L901VAction.gridId).trigger("reloadGrid");
    }
};
$(function(){
    var L901M01aGrid = $(L901VAction.gridId).iGrid({
        handler: L901VAction.ghandler,// "lms9031gridhandle"
        height: 350,
//        rowNum: 15,
        needPager: false,
        postData: {
            formAction: "queryC01m01a"
        },
        colModel: [{
            colHeader: "",
            // name: 'oid',
            hidden: true
        }, {
            colHeader: i18n.lms9031v00["L903M01a.custId"],// 借款人統編
            align: "center",
            width: 90,
            sortable: true,
            name: 'LNF022_CUST_ID'
        }, {
            colHeader: i18n.lms9031v00["L903M01a.brNo"], // 承作分行
            align: "center",
            width: 40,
            sortable: true,
            name: 'LNF022_BR_NO'
        }, {
            colHeader: i18n.lms9031v00["L903M01a.contract"], //
            align: "center",
            width: 70,
            sortable: true,
            name: 'LNF022_CONTRACT'
        }, {
            colHeader: i18n.lms9031v00["L903M01a.quota"], // 額度
            align: "right",
            width: 80,
            sortable: true,
            name: 'QUOTA'
        }, {
            colHeader: i18n.lms9031v00["L903M01a.bal"], // 餘額
            align: "right",
            width: 80,
            sortable: true,
            name: 'BAL'
        }, {
            colHeader: i18n.lms9031v00["L903M01a.beg"], // 撥款日期
            align: "center",
            width: 100,
            sortable: true,
            name: 'beg'
        }]
    });
    // 開啟篩選視窗
    $("#btnFilter").click(function(){
        openQuery();
    });
});
/**
 * 開啟篩選視窗 建立下拉式選單
 */
function openQuery(){
    var $form = $('#lms9031tabForm');
    // 縣市編號
    var cityCode = $form.find('#fCity').val();
    // 鄉鎮市區內容
    var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
    // 選取的縣市名稱
    var cityCodet = $form.find('#fCity').find(":selected").text();
    // 選取的鄉鎮市區名稱
    var ZipCodet = $form.find('#fZip').find(":selected").text();
    // 段名稱
    var SITE3Codet = $form.find('#fSITE3').find(":selected").text();
    // 下拉式選單(縣市)
    $form.find("#fCity").setItems({
        item: combos['counties'],
        format: "{key}",
        value: cityCode,
        fn: function(){
            var $form = $('#lms9031tabForm');
            var cityCode = $form.find('#fCity').val();
            var combos = CommonAPI.loadCombos(['counties', 'counties' + cityCode]);
            $form.find('#fZip').setItems({
                item: combos['counties' + cityCode],
                format: '{key}',
                value: $form.find('#fZip').val()
            });
            $form.find("#fSITE3").setItems({
                item: getSITE3(cityCodet, ZipCodet),
                format: "{key}",
                value: $form.find('#fSITE3').val()
            });
            $form.find("#fSITE4").setItems({
                item: getSITE4(cityCodet, ZipCodet, SITE3Codet),
                format: "{key}",
                value: $form.find('#fSITE4').val()
            });
        }
    });
    
    // 下拉式選單(鄉鎮市區)
    $form.find("#fZip").setItems({
        item: combos['counties' + cityCode],
        format: "{key}",
        value: $form.find('#fZip').val(),
        // -------------------------
        fn: function(){
            var $form = $('#lms9031tabForm');
            var cityCode = $form.find('#fCity').find(":selected").text();
            var ZipCode = $form.find('#fZip').find(":selected").text();
            $form.find('#fSITE3').setItems({
                item: getSITE3(cityCode, ZipCode),
                format: '{key}',
                value: $form.find('#fSITE3').val()
            });
            $form.find("#fSITE4").setItems({
                item: getSITE4(cityCodet, ZipCodet, SITE3Codet),
                format: "{key}",
                value: $form.find('#fSITE4').val()
            });
        }
    });
    // 下拉式選單(段)
    $form.find("#fSITE3").setItems({
        item: getSITE3(cityCodet, ZipCodet),
        format: "{key}",
        value: $form.find('#fSITE3').val(),
        fn: function(){
            var $form = $('#lms9031tabForm');
            var cityCode = $form.find('#fCity').find(":selected").text();
            var ZipCode = $form.find('#fZip').find(":selected").text();
            var SITE3Codet = $form.find('#fSITE3').find(":selected").text();
            $form.find('#fSITE4').setItems({
                item: getSITE4(cityCode, ZipCode, SITE3Codet),
                format: '{key}',
                value: $form.find('#fSITE4').val()
            });
        }
    });
    // 下拉式選單(小段)
    $form.find("#fSITE4").setItems({
        item: getSITE4(cityCodet, ZipCodet, SITE3Codet),
        format: "{key}",
        value: $form.find('#fSITE4').val()
    });
    // 開啟視窗
    $("#lms9031new").thickbox({
        width: 700,
        height: 200,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var fSITE4 = new String();
                if ($form.find('#fSITE4').val() == "") {
                    fSITE4 = "%";
                }
                else {
                    fSITE4 = $form.find('#fSITE4').val();
                }
                if ($("#lms9031tabForm").valid()) {
                    filterGrid({
                        fCity: $form.find('#fCity').find(":selected").text(),
                        fZip: $form.find('#fZip').find(":selected").text(),
                        SITE3: $form.find('#fSITE3').val(),
                        SITE4: fSITE4,
                        LNNO1: $form.find('#LNNO1').val(),
                        LNNO2: $form.find('#LNNO2').val()
                    });
                    $.thickbox.close();
                }
            },
            "cancel": function(){
                filterGrid({
                    type: ""
                });
                $.thickbox.close();
            }
        }
    });
}

/**
 * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip) 傳回段名稱(SITE3)
 */
function getSITE3(fCity, fZip){
    var $form = $('#lms9031tabForm');
    if ($form.find('#fCity').val() === "" || $form.find('#fZip').val() === "") 
        return {};
    var SITE3 = null;
    $.ajax({
        handler: L901VAction.fhandler,// "lms9031m01formhandler"
        action: "querySIET3",
        type: 'post',
        async: false,
        data: {
            fCity: fCity,
            fZip: fZip
        },
        success: function(responseData){
            SITE3 = responseData['SITE3'];
        }
    });
    return SITE3;
}

/**
 * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip)段名稱(SITE3) 傳回小段名稱(SITE4)
 */
function getSITE4(fCity, fZip, fSITE3){
    var $form = $('#lms9031tabForm');
    if ($form.find('#fCity').val() === "" || $form.find('#fZip').val() === "" || $form.find('#fSITE3').val() === "") 
        return {};
    var SITE4 = null;
    $.ajax({
        handler: L901VAction.fhandler,// "lms9031m01formhandler"
        action: "querySIET4",
        type: 'post',
        async: false,
        data: {
            fCity: fCity,
            fZip: fZip,
            fSITE3: fSITE3
        },
        success: function(responseData){
            SITE4 = responseData['SITE4'];
        }
    });
    return SITE4;
}

// grid資料篩選
function filterGrid(sendData){
    $("#gridfile").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "queryL903m01a",
            docStatus: viewstatus,
            type: $("[name=queryData]:checked").val()
        }, sendData || {}),
        search: true
    }).trigger("reloadGrid");
}
