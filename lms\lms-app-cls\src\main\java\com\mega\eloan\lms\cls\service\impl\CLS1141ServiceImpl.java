/*

 * CLS1141ServiceImpl.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.dao.DocOpenerDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.gwclient.Brmp002O;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.model.RelativeMeta_;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.SQLParse;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.common.ContractDocUtil;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.Table;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.constants.UtilConstants.lngeFlag;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.FlowNameService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.base.service.RelatedAccountService;
import com.mega.eloan.lms.base.service.ScoreService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.constants.ClsConstants;
import com.mega.eloan.lms.cls.pages.CLS1141M01Page;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.panels.CLS1201S21Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S23Panel;
import com.mega.eloan.lms.cls.panels.CLS1201S25BPanel;
import com.mega.eloan.lms.cls.panels.CLS1201S25Panel;
import com.mega.eloan.lms.cls.panels.CLSS07APanel;
import com.mega.eloan.lms.cls.report.impl.CLS1141R01RptServiceImpl;
import com.mega.eloan.lms.cls.service.CLS1130Service;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1141Service;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.dao.BRelatedDao;
import com.mega.eloan.lms.dao.C100M01Dao;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.C101S01ADao;
import com.mega.eloan.lms.dao.C101S01BDao;
import com.mega.eloan.lms.dao.C101S01CDao;
import com.mega.eloan.lms.dao.C120M01ADao;
import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.C120S01BDao;
import com.mega.eloan.lms.dao.C120S01CDao;
import com.mega.eloan.lms.dao.C120S01EDao;
import com.mega.eloan.lms.dao.C120S01PDao;
import com.mega.eloan.lms.dao.C120S01SDao;
import com.mega.eloan.lms.dao.C120S01TDao;
import com.mega.eloan.lms.dao.C120S01YDao;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C127M01ADao;
import com.mega.eloan.lms.dao.C140JSONDao;
import com.mega.eloan.lms.dao.C140M01ADao;
import com.mega.eloan.lms.dao.C140M04ADao;
import com.mega.eloan.lms.dao.C140M04BDao;
import com.mega.eloan.lms.dao.C140M07ADao;
import com.mega.eloan.lms.dao.C140S04ADao;
import com.mega.eloan.lms.dao.C140S04BDao;
import com.mega.eloan.lms.dao.C140S04CDao;
import com.mega.eloan.lms.dao.C140S07ADao;
import com.mega.eloan.lms.dao.C140S09ADao;
import com.mega.eloan.lms.dao.C140S09BDao;
import com.mega.eloan.lms.dao.C140S09CDao;
import com.mega.eloan.lms.dao.C140S09DDao;
import com.mega.eloan.lms.dao.C140S09EDao;
import com.mega.eloan.lms.dao.C140S09FDao;
import com.mega.eloan.lms.dao.C140SDSCDao;
import com.mega.eloan.lms.dao.C140SFFFDao;
import com.mega.eloan.lms.dao.L120A01ADao;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01DDao;
import com.mega.eloan.lms.dao.L120M01EDao;
import com.mega.eloan.lms.dao.L120M01FDao;
import com.mega.eloan.lms.dao.L120M01GDao;
import com.mega.eloan.lms.dao.L120M01HDao;
import com.mega.eloan.lms.dao.L120M01IDao;
import com.mega.eloan.lms.dao.L120M01LDao;
import com.mega.eloan.lms.dao.L120S01MDao;
import com.mega.eloan.lms.dao.L120S01RDao;
import com.mega.eloan.lms.dao.L120S03ADao;
import com.mega.eloan.lms.dao.L120S04ADao;
import com.mega.eloan.lms.dao.L120S04BDao;
import com.mega.eloan.lms.dao.L120S04CDao;
import com.mega.eloan.lms.dao.L120S05ADao;
import com.mega.eloan.lms.dao.L120S05BDao;
import com.mega.eloan.lms.dao.L120S05CDao;
import com.mega.eloan.lms.dao.L120S05DDao;
import com.mega.eloan.lms.dao.L120S06ADao;
import com.mega.eloan.lms.dao.L120S06BDao;
import com.mega.eloan.lms.dao.L120S07ADao;
import com.mega.eloan.lms.dao.L120S09ADao;
import com.mega.eloan.lms.dao.L120S19ADao;
import com.mega.eloan.lms.dao.L120S19BDao;
import com.mega.eloan.lms.dao.L120S19CDao;
import com.mega.eloan.lms.dao.L121M01BDao;
import com.mega.eloan.lms.dao.L130M01ADao;
import com.mega.eloan.lms.dao.L130M01BDao;
import com.mega.eloan.lms.dao.L130S01ADao;
import com.mega.eloan.lms.dao.L130S01BDao;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01ODao;
import com.mega.eloan.lms.dao.L140M01PDao;
import com.mega.eloan.lms.dao.L140M01RDao;
import com.mega.eloan.lms.dao.L140M04ADao;
import com.mega.eloan.lms.dao.L140MC1ADao;
import com.mega.eloan.lms.dao.L140MC1BDao;
import com.mega.eloan.lms.dao.L140MC2ADao;
import com.mega.eloan.lms.dao.L140S01ADao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.dao.L140S02GDao;
import com.mega.eloan.lms.dao.L140S02HDao;
import com.mega.eloan.lms.dao.L150M01ADao;
import com.mega.eloan.lms.dao.L720M01ADao;
import com.mega.eloan.lms.dao.L730A01ADao;
import com.mega.eloan.lms.dao.L730M01ADao;
import com.mega.eloan.lms.dao.L730S01ADao;
import com.mega.eloan.lms.dao.L800M01ADao;
import com.mega.eloan.lms.dw.service.DwFxrthovsService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.dw.service.DwRoclistEcusService;
import com.mega.eloan.lms.dw.service.DwdbBASEService;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.mfaloan.bean.LNF130;
import com.mega.eloan.lms.mfaloan.service.LNLNF070Service;
import com.mega.eloan.lms.mfaloan.service.LNLNF130Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF338Service;
import com.mega.eloan.lms.mfaloan.service.MisELF442Service;
import com.mega.eloan.lms.mfaloan.service.MisELF447Service;
import com.mega.eloan.lms.mfaloan.service.MisElcrcoService;
import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;
import com.mega.eloan.lms.mfaloan.service.MisElrelimtService;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisGrpfinService;
import com.mega.eloan.lms.mfaloan.service.MisLN811Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF022Service;
import com.mega.eloan.lms.mfaloan.service.MisLnunIdService;
import com.mega.eloan.lms.mfaloan.service.MisMISLN20Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.*;
import com.mega.eloan.lms.obsdb.service.ObsdbELF404Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Arithmetic;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

/**
 * <pre>
 * 授信簽報書Service Impl
 * </pre>
 * 
 * @since 2012/12/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/5,REX,new
 *          </ul>
 */
@Service
public class CLS1141ServiceImpl extends AbstractCapService implements
		CLS1141Service {
	private final String DATEYYYYMMDD = "yyyy-MM-dd";

	@Resource
	CLSService clsService;
	@Resource
	CLS1151Service cls1151Service;
	@Autowired
	@Qualifier("CLS1130Service")
	CLS1130Service cls1130Service;

	@Resource
	C122M01ADao c122m01aDao;

	@Resource
	L140M01ADao l140m01aDao;
	@Resource
	L140M01PDao l140m01pDao;
	@Resource
	DocOpenerDao docopenerDao;

	@Resource
	L120A01ADao l120a01aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01DDao l120m01dDao;

	@Resource
	L120M01EDao l120m01eDao;

	@Resource
	L120M01FDao l120m01fDao;

	@Resource
	L120M01HDao l120m01hDao;

	@Resource
	L120M01IDao l120m01iDao;

	@Resource
	L120S09ADao l120S09aDao;

	@Resource
	L120S01MDao l120s01mDao;

	@Resource
	L120S03ADao l120s03aDao;

	@Resource
	L120S04ADao l120s04aDao;

	@Resource
	L120S04BDao l120s04bDao;

	@Resource
	L120S04CDao l120s04cDao;

	@Resource
	L120S05ADao l120s05aDao;

	@Resource
	L120S05BDao l120s05bDao;

	@Resource
	L120S05CDao l120s05cDao;

	@Resource
	L120S05DDao l120s05dDao;

	@Resource
	L120S06ADao l120s06aDao;

	@Resource
	L120S06BDao l120s06bDao;

	@Resource
	L120S07ADao l120s07aDao;

	@Resource
	L130M01ADao l130m01aDao;

	@Resource
	L130M01BDao l130m01bDao;

	@Resource
	L130S01ADao l130s01aDao;

	@Resource
	L130S01BDao l130s01bDao;

	@Resource
	L140S01ADao l140s01aDao;

	@Resource
	L730A01ADao l730a01aDao;

	@Resource
	L730M01ADao l730m01aDao;

	@Resource
	L730S01ADao l730s01aDao;

	@Resource
	L800M01ADao l800m01aDao;

	@Resource
	L121M01BDao l121m01bDao;

	@Resource
	C140M01ADao c140m01aDao;
	@Resource
	C140M04ADao c140m04aDao;
	@Resource
	C140M04BDao c140m04bDao;
	@Resource
	C140M07ADao c140m07aDao;
	@Resource
	C140S07ADao c140s07aDao;
	@Resource
	C140JSONDao c140jsonDao;
	@Resource
	C140SDSCDao c140sdscDao;
	@Resource
	C140S04ADao c140s04aDao;
	@Resource
	C140S04BDao c140s04bDao;
	@Resource
	C140S04CDao c140s04cDao;
	@Resource
	DocFileDao docFileDao;
	@Resource
	DocFileService docFileService;
	@Resource
	C140SFFFDao c140SFFFDao;
	@Resource
	BRelatedDao brelatedDao;
	@Resource
	C140S09ADao c140s09aDao;
	@Resource
	C140S09BDao c140s09bDao;
	@Resource
	C140S09CDao c140s09cDao;
	@Resource
	C140S09DDao c140s09dDao;
	@Resource
	C140S09EDao c140s09eDao;
	@Resource
	C140S09FDao c140s09fDao;

	@Resource
	L120S01RDao l120s01rDao;

	@Resource
	L120M01LDao l120m01lDao;
	
	@Resource
	C100M01Dao c100m01Dao;

	@Resource
	DocLogService docLogService;

	@Resource
	FlowService flowService;

	@Resource
	TempDataService tempDataService;

	@Resource
	NumberService number;

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;

	@Resource
	EloandbBASEService r6dbService;
	@Resource
	MisdbBASEService misDBService;
	@Resource
	DwdbBASEService dwdbService;
	@Resource
	DwLnquotovService dwluquotovService;
	@Resource
	DwFxrthovsService dwfxrthovsService;
	@Resource
	MisCustdataService misCustdataService;
	@Resource
	MisELF338Service misELF338Service;
	@Resource
	MisELF442Service misELF442Service;
	@Resource
	MisELF447Service misELF447Service;
	@Resource
	MisElrelimtService misElrelimtService;
	@Resource
	MisGrpcmpService misGrpcmpService;
	@Resource
	MisLN811Service misLN811Service;
	@Resource
	MisLnunIdService misLnunIdService;
	@Resource
	MisElcrcoService misElcrcoService;
	@Resource
	MisGrpfinService misGrpfinService;
	@Resource
	MisElcsecntService misElcsecntService;
	@Resource
	DwRoclistEcusService dwRoclistEcusService;
	@Resource
	DwLnquotovService dwLnquotovService;
	@Resource
	ObsdbELF404Service obsdbELF404Service;
	@Resource
	CodeTypeService codeService;
	@Resource
	MisRatetblService misRateService;

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	L720M01ADao l720m01adao;

	@Resource
	FlowNameService flowNameService;
	@Resource
	C120M01ADao c120m01aDao;

	@Resource
	C120S01PDao c120s01pDao;

	@Resource
	L120M01GDao l120m01gDao;
	@Resource
	C120S01EDao c120s01eDao;
	@Resource
	L140M04ADao l140m04aDao;

	@Resource
	L150M01ADao l150m01aDao;

	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	C101M01ADao c101m01aDao;

	@Resource
	C120S01ADao c120s01aDao;

	@Resource
	RelatedAccountService relatedAccountService;

	@Resource
	L140M01RDao l140m01rDao;

	@Resource
	ProdService prodService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	C120S01SDao c120s01sDao;

	@Resource
	C120S01TDao c120s01tDao;

	@Resource
	EjcicService ejcicService;

	@Resource
	C101S01CDao c101s01cDao;
	@Resource
	C101S01BDao c101s01bDao;
	@Resource
	C101S01ADao c101s01aDao;
	@Resource
	L140M01ODao l140m01oDao;
	@Resource
	BranchService branchService;
	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	@Resource
	MisLNF022Service misLNF022Service;
	@Resource
	CLS1131Service cls1131Service;
	@Resource
	L140S02EDao l140s02eDao;
	@Resource
	L140MC1ADao l140mc1aDao;
	@Resource
	C120S01BDao c120s01bDao;
	@Resource
	C120S01CDao c120s01cDao;
	@Resource
	SysParameterService sysParameterService;
	@Resource
	L120S19ADao l120s19aDao;
	@Resource
	C127M01ADao c127m01adao;
	@Resource
	L120S19BDao l120s19bDao;
	@Resource
	L120S19CDao l120s19cDao;
	@Resource
	C120S01YDao c120s01yDao;

	@Resource
	LNLNF070Service lnlnf070Service;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	LNLNF130Service misLNF130Service;
	
	@Resource
	MisMISLN20Service misMISLN20Service;
	
	@Resource
	L140MC2ADao l140mc2aDao;

	@Resource
	L140S02GDao l140s02gDao;
	@Resource
	L140S02HDao l140s02hDao;

	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	ScoreService scoreService;

	@Resource
	L140MC1BDao l140mc1bDao;

	private static Logger logger = LoggerFactory
			.getLogger(CLS1141ServiceImpl.class);

	private static String IS_HEAD_ACCOUNT_VERSION = "2.0";
	
	private static String [] CHECKED_INCOME_ITEM_IN_VER2 = new String[] {"A01", "A02", "A03", "B01", "B02", "B04", "B05", "C01", "C02", "C03", "D01", "D02", "D03", "D04"};

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByManId(Class clazz, String mainId) {
		if (clazz == L120M01G.class) {
			return (T) l120m01gDao.findByUniqueKey(mainId);
		} else if (clazz == L140S02A.class) {
			return (T) l140s02aDao.findByMainId(mainId);
		} else if (clazz == L140M01P.class) {
			return (T) l140m01pDao.findByMainId(mainId);
		} else if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByMainId(mainId);
		}
		logger.debug("{} is not set", clazz);
		return null;

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == L120M01A.class) {
			return (T) l120m01aDao.findByOid(oid);
		} else if (clazz == L120M01D.class) {
			return (T) l120m01dDao.findByOid(oid);
		} else if (clazz == L120M01E.class) {
			return (T) l120m01eDao.findByOid(oid);
		} else if (clazz == L120M01F.class) {
			return (T) l120m01fDao.findByOid(oid);
		} else if (clazz == L120M01H.class) {
			return (T) l120m01hDao.findByOid(oid);
		} else if (clazz == L120S03A.class) {
			return (T) l120s03aDao.findByOid(oid);
		} else if (clazz == L120S04A.class) {
			return (T) l120s04aDao.findByOid(oid);
		} else if (clazz == L120S04B.class) {
			return (T) l120s04bDao.findByOid(oid);
		} else if (clazz == L120S04C.class) {
			return (T) l120s04cDao.findByOid(oid);
		} else if (clazz == L120S05A.class) {
			return (T) l120s05aDao.findByOid(oid);
		} else if (clazz == L120S05B.class) {
			return (T) l120s05bDao.findByOid(oid);
		} else if (clazz == L120S05C.class) {
			return (T) l120s05cDao.findByOid(oid);
		} else if (clazz == L120S05D.class) {
			return (T) l120s05dDao.findByOid(oid);
		} else if (clazz == L120S06A.class) {
			return (T) l120s06aDao.findByOid(oid);
		} else if (clazz == L120S06B.class) {
			return (T) l120s06bDao.findByOid(oid);
		} else if (clazz == L120S07A.class) {
			return (T) l120s07aDao.findByOid(oid);
		} else if (clazz == L730A01A.class) {
			return (T) l730a01aDao.findByOid(oid);
		} else if (clazz == L730M01A.class) {
			return (T) l730m01aDao.findByOid(oid);
		} else if (clazz == L730S01A.class) {
			return (T) l730s01aDao.findByOid(oid);
		} else if (clazz == L121M01B.class) {
			return (T) l121m01bDao.findByOid(oid);
		} else if (clazz == L720M01A.class) {
			return (T) l720m01adao.findByOid(oid);
		} else if (clazz == C140M04A.class) {
			return (T) c140m04aDao.find(oid);
		} else if (clazz == C140M04B.class) {
			return (T) c140m04bDao.find(oid);
		} else if (clazz == C140M01A.class) {
			return (T) c140m01aDao.find(oid);
		} else if (clazz == C120M01A.class) {
			return (T) c120m01aDao.find(oid);
		} else if (clazz == L120M01G.class) {
			return (T) l120m01gDao.find(oid);
		} else if (clazz == C100M01.class) {
			return (T) c100m01Dao.find(oid);
		} else if (clazz == L150M01A.class) {
			return (T) l150m01aDao.find(oid);
		} else if (clazz == L140S02A.class) {
			return (T) l140s02aDao.find(oid);
		} else if (clazz == L140M01A.class) {
			return (T) l140m01aDao.find(oid);
		} else if (clazz == C120S01T.class) {
			return (T) c120s01tDao.find(oid);
		}
		logger.debug("{} is not set", clazz);
		return null;
	}

	/*
	 * 取得 DataPage (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.service.AbstractService#findPage(java.lang.Class,
	 * tw.com.iisi.cap.dao.utils.ISearch)
	 */
	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == L120M01A.class) {
			return l120m01aDao.findPage(search);
		} else if (clazz == L120M01D.class) {
			return l120m01dDao.findPage(search);
		} else if (clazz == L120M01E.class) {
			return l120m01eDao.findPage(search);
		} else if (clazz == L120M01F.class) {
			return l120m01fDao.findPage(search);
		} else if (clazz == L120M01H.class) {
			return l120m01hDao.findPage(search);
		} else if (clazz == L120S03A.class) {
			return l120s03aDao.findPage(search);
		} else if (clazz == L120S04A.class) {
			return l120s04aDao.findPage(search);
		} else if (clazz == L120S04B.class) {
			return l120s04bDao.findPage(search);
		} else if (clazz == L120S04C.class) {
			return l120s04cDao.findPage(search);
		} else if (clazz == L120S05A.class) {
			return l120s05aDao.findPage(search);
		} else if (clazz == L120S05B.class) {
			return l120s05bDao.findPage(search);
		} else if (clazz == L120S05C.class) {
			return l120s05cDao.findPage(search);
		} else if (clazz == L120S05D.class) {
			return l120s05dDao.findPage(search);
		} else if (clazz == L120S06A.class) {
			return l120s06aDao.findPage(search);
		} else if (clazz == L120S06B.class) {
			return l120s06bDao.findPage(search);
		} else if (clazz == L120S07A.class) {
			return l120s07aDao.findPage(search);
		} else if (clazz == L730A01A.class) {
			return l730a01aDao.findPage(search);
		} else if (clazz == L730M01A.class) {
			return l730m01aDao.findPage(search);
		} else if (clazz == L730S01A.class) {
			return l730s01aDao.findPage(search);
		} else if (clazz == L121M01B.class) {
			return l121m01bDao.findPage(search);
		} else if (clazz == L720M01A.class) {
			return l720m01adao.findPage(search);
		} else if (clazz == C140M01A.class) {
			return c140m01aDao.findPage(search);
		} else if (clazz == C140M04A.class) {
			return c140m04aDao.findPage(search);
		} else if (clazz == C140M04B.class) {
			return c140m04bDao.findPage(search);
		} else if (clazz == C140M07A.class) {
			return c140m07aDao.findPage(search);
		} else if (clazz == C140S07A.class) {
			return c140s07aDao.findPage(search);
		} else if (clazz == L120M01G.class) {
			return l120m01gDao.findPage(search);
		} else if (clazz == C120M01A.class) {
			return c120m01aDao.findPage(search);
		} else if (clazz == L140M01R.class) {
			return l140m01rDao.findPage(search);
		} else if (clazz == L140M01A.class) {
			return l140m01aDao.findPage(search);
		}

		logger.debug("{} is not set", clazz);
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		for (GenericBean model : entity) {
			if (model != null) {
				// set updater and updateTime
				try {
					if (Util.isEmpty(model.get(EloanConstants.OID))) {
						model.set("creator", user.getUserId());
						model.set("createTime", CapDate.getCurrentTimestamp());
					}

					if (model instanceof L120M01A) {
						if (((L120M01A) model).getDocStatus().equals(
								CreditDocStatusEnum.海外_編製中.getCode())
								|| ((L120M01A) model).getDocStatus().equals(
										CreditDocStatusEnum.海外_待補件.getCode())) {
							// 當為簽報書主檔且非 編製中，當文件為編製中或待補件才需要更新
							model.set("updater", user.getUserId());
							model.set("updateTime",
									CapDate.getCurrentTimestamp());
						}
					} else {
						model.set("updater", user.getUserId());
						model.set("updateTime", CapDate.getCurrentTimestamp());
					}

				} catch (CapException e) {
					logger.error("set set updater or creator error ", e);
				}

				if (model instanceof L120A01A) {
					l120a01aDao.save((L120A01A) model);
				} else if (model instanceof L120M01A) {

					if (((L120M01A) model).getDocStatus().equals(
							CreditDocStatusEnum.海外_編製中.getCode())
							|| ((L120M01A) model).getDocStatus().equals(
									CreditDocStatusEnum.海外_待補件.getCode())) {
						String randomCode = IDGenerator.getRandomCode();
						((L120M01A) model).setRandomCode(randomCode);
					}
					l120m01aDao.save((L120M01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01A) model)
								.getMainId());
						docLogService.record(((L120M01A) model).getOid(),
								DocLogEnum.SAVE);
					}
				} else if (model instanceof L120M01D) {
					// ===========
					((L120M01D) model).setUpdater(user.getUserId());
					((L120M01D) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					// ===========
					l120m01dDao.save((L120M01D) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01D) model)
								.getMainId());
					}
				} else if (model instanceof L120M01E) {
					l120m01eDao.save((L120M01E) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01E) model)
								.getMainId());
					}
				} else if (model instanceof L120M01F) {
					l120m01fDao.save((L120M01F) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01F) model)
								.getMainId());
					}
				} else if (model instanceof L120M01H) {
					l120m01hDao.save((L120M01H) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01H) model)
								.getMainId());
					}
				} else if (model instanceof L120M01I) {
					l120m01iDao.save((L120M01I) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120M01I) model)
								.getMainId());
					}
				} else if (model instanceof L120S03A) {
					l120s03aDao.save((L120S03A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S03A) model)
								.getMainId());
					}
				} else if (model instanceof L120S04A) {
					l120s04aDao.save((L120S04A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04A) model)
								.getMainId());

					}
				} else if (model instanceof L120S04B) {
					l120s04bDao.save((L120S04B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04B) model)
								.getMainId());

					}
				} else if (model instanceof L120S04C) {
					l120s04cDao.save((L120S04C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S04C) model)
								.getMainId());
					}
				} else if (model instanceof L120S05A) {
					l120s05aDao.save((L120S05A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S05A) model)
								.getMainId());

					}
				} else if (model instanceof L120S05B) {
					l120s05bDao.save((L120S05B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S05B) model)
								.getMainId());
					}
				} else if (model instanceof L120S05C) {
					l120s05cDao.save((L120S05C) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S05C) model)
								.getMainId());

					}
				} else if (model instanceof L120S05D) {
					l120s05dDao.save((L120S05D) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S05D) model)
								.getMainId());
					}
				} else if (model instanceof L120S06A) {
					l120s06aDao.save((L120S06A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S06A) model)
								.getMainId());

					}
				} else if (model instanceof L120S06B) {
					l120s06bDao.save((L120S06B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L120S06B) model)
								.getMainId());
					}
				} else if (model instanceof L120S07A) {
					l120s07aDao.save((L120S07A) model);
				} else if (model instanceof L730A01A) {
					l730a01aDao.save((L730A01A) model);

				} else if (model instanceof L730M01A) {
					l730m01aDao.save((L730M01A) model);
				} else if (model instanceof L730S01A) {
					l730s01aDao.save((L730S01A) model);
				} else if (model instanceof L121M01B) {
					l121m01bDao.save((L121M01B) model);
				} else if (model instanceof L120M01G) {
					l120m01gDao.save((L120M01G) model);
				} else if (model instanceof C120S01T) {
					c120s01tDao.save((C120S01T) model);
				} else if (model instanceof L130M01A) {
					((L130M01A) model).setUpdater(user.getUserId());
					((L130M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l130m01aDao.save((L130M01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L130M01A) model)
								.getMainId());
					}
				} else if (model instanceof L130M01B) {
					((L130M01B) model).setUpdater(user.getUserId());
					((L130M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l130m01bDao.save((L130M01B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L130M01B) model)
								.getMainId());
					}
				} else if (model instanceof L130S01A) {
					((L130S01A) model).setUpdater(user.getUserId());
					((L130S01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l130s01aDao.save((L130S01A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L130S01A) model)
								.getMainId());
					}
				} else if (model instanceof L130S01B) {
					((L130S01B) model).setUpdater(user.getUserId());
					((L130S01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l130s01bDao.save((L130S01B) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L130S01B) model)
								.getMainId());
					}
				} else if (model instanceof L140M04A) {
					((L140M04A) model).setUpdater(user.getUserId());
					((L140M04A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());
					l140m04aDao.save((L140M04A) model);
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(((L140M04A) model)
								.getMainId());
					}
				} else if (model instanceof L140S02H) {
					tempDataService.deleteByMainId(((L140S02H) model)
							.getMainId());
				} else {
					logger.debug("++++++++++++++++++++++++++++++");
					logger.debug("{} is not set", model.getClass());
					logger.debug("++++++++++++++++++++++++++++++");
				}

			}
		}
	}

	@Override
	public void delete(GenericBean... entity) {
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof L120A01A) {
					l120a01aDao.delete((L120A01A) model);
				} else if (model instanceof L120M01A) {
					l120m01aDao.delete((L120M01A) model);
				} else if (model instanceof L120M01D) {
					l120m01dDao.delete((L120M01D) model);
				} else if (model instanceof L120M01E) {
					l120m01eDao.delete((L120M01E) model);
				} else if (model instanceof L120M01F) {
					l120m01fDao.delete((L120M01F) model);
				} else if (model instanceof L120M01H) {
					l120m01hDao.delete((L120M01H) model);
				} else if (model instanceof L120S03A) {
					l120s03aDao.delete((L120S03A) model);
				} else if (model instanceof L120S04A) {
					l120s04aDao.delete((L120S04A) model);
				} else if (model instanceof L120S04B) {
					l120s04bDao.delete((L120S04B) model);
				} else if (model instanceof L120S04C) {
					l120s04cDao.delete((L120S04C) model);
				} else if (model instanceof L120S05A) {
					l120s05aDao.delete((L120S05A) model);
				} else if (model instanceof L120S05B) {
					l120s05bDao.delete((L120S05B) model);
				} else if (model instanceof L120S05C) {
					l120s05cDao.delete((L120S05C) model);
				} else if (model instanceof L120S05D) {
					l120s05dDao.delete((L120S05D) model);
				} else if (model instanceof L120S06A) {
					l120s06aDao.delete((L120S06A) model);
				} else if (model instanceof L120S06B) {
					l120s06bDao.delete((L120S06B) model);
				} else if (model instanceof L120S07A) {
					l120s07aDao.delete((L120S07A) model);
				} else if (model instanceof L730A01A) {
					l730a01aDao.delete((L730A01A) model);
				} else if (model instanceof L730M01A) {
					l730m01aDao.delete((L730M01A) model);
				} else if (model instanceof L730S01A) {
					l730s01aDao.delete((L730S01A) model);
				} else if (model instanceof L121M01B) {
					l121m01bDao.delete((L121M01B) model);
				} else if (model instanceof L120M01G) {
					l120m01gDao.delete((L120M01G) model);
				} else if (model instanceof L140S02H) {
					l140s02hDao.delete((L140S02H) model);
				}
			}
			logger.debug("{} is not set", model.getClass());
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId) {
		if (clazz == C140M04A.class) {
			return c140m04aDao.findByMainId(mainId);
		} else if (clazz == C140M04B.class) {
			return c140m04bDao.findByMainId(mainId);
		} else if (clazz == L120A01A.class) {
			return l120a01aDao.findByMainId(mainId);
		} else if (clazz == L120M01D.class) {
			return l120m01dDao.findByMainId(mainId);
		} else if (clazz == L120M01E.class) {
			return l120m01eDao.findByMainId(mainId);
		} else if (clazz == L120M01F.class) {
			return l120m01fDao.findByMainId(mainId);
		} else if (clazz == L120M01H.class) {
			return l120m01hDao.findByMainId(mainId);
		} else if (clazz == L120S03A.class) {
			return l120s03aDao.findByMainId(mainId);
		} else if (clazz == L120S04A.class) {
			return l120s04aDao.findByMainId(mainId);
		} else if (clazz == L120S04B.class) {
			return l120s04bDao.findByMainId(mainId);
		} else if (clazz == L120S04C.class) {
			return l120s04cDao.findByMainId(mainId);
		} else if (clazz == L120S05B.class) {
			return l120s05bDao.findByMainId(mainId);
		} else if (clazz == L120S05C.class) {
			return l120s05cDao.findByMainId(mainId);
		} else if (clazz == L120S05D.class) {
			return l120s05dDao.findByMainId(mainId);
		} else if (clazz == L120S06A.class) {
			return l120s06aDao.findByMainId(mainId);
		} else if (clazz == L120S06B.class) {
			return l120s06bDao.findByMainId(mainId);
		} else if (clazz == L730A01A.class) {
			return l730a01aDao.findByMainId(mainId);
		} else if (clazz == L730M01A.class) {
			return l730m01aDao.findByIndex01(mainId);
		} else if (clazz == L730S01A.class) {
			return l730s01aDao.findByMainId(mainId);
		} else if (clazz == L121M01B.class) {
			return l121m01bDao.findByMainId(mainId);
		} else if (clazz == C120M01A.class) {
			return c120m01aDao.findByMainId(mainId);
		} else if (clazz == L120M01G.class) {
			return l120m01gDao.findByMainId(mainId);
		} else if (clazz == C120S01A.class) {
			return c120s01aDao.findByMainId(mainId);
		}
		logger.debug("{} is not set", clazz);
		return null;
	}

	@Override
	public L120M01A findL120m01aByOid(String oid) {
		return l120m01aDao.findByOid(oid);
	}

	@Override
	public L120M01A findL120m01aByMainId(String mainId) {
		return l120m01aDao.findByMainId(mainId);
	}

	@Override
	public L120S05C findL120s05cByMainId(String mainId) {
		return l120s05cDao.findByUniqueKey(mainId);
	}

	@Override
	public L730M01A findL730m01aByMainId(String mainId) {
		return l730m01aDao.findByMainId(mainId);
	}

	@Override
	public C140M01A getC140M01AByMainId(String MainId) {
		return c140m01aDao.findByMainId(MainId);
	}

	@Override
	public void saveByGenericBeanList(List<GenericBean> list) {
		for (GenericBean bean : list) {
			this.save(bean);
		}

	}

	/**
	 * 透過JDBC取得 當抓不到評等資料時才需讀取本行年度淨值來算出限額
	 * 
	 * @param json
	 *            JSONObject
	 * @param netValue
	 *            double
	 * @param lnLimit
	 *            double
	 */
	@SuppressWarnings("rawtypes")
	public void getRptGroupData2(JSONObject json, double netValue,
			double lnLimit) {

		List rows = this.misElrelimtService.findRPTGroupData2();

		Iterator it = rows.iterator();
		if (it.hasNext()) {
			Map dataMap = (Map) it.next();
			// 92/03/20
			// 本行年度淨值的單位為元，需改為仟元才與集團限額單位一致
			netValue = Util.parseDouble(Util.trim((String) dataMap
					.get("REPURE"))) / 1000;
			if (netValue > 0) {
				// 若未評等，則授信限額為該本行淨值之15%
				lnLimit = Math.round(netValue * 0.15);
				// 本行對該集團授信限額(lmtAmt)
				json.put("lmtAmt", Util.nullToSpace(lnLimit));
				// 本行對該集團無擔保授信限額(gcrdAmt)
				json.put("gcrdAmt",
						Util.nullToSpace(Math.round(netValue * 0.08)));
			}
		}
	}

	@Override
	public L120M01D findL120m01dByUniqueKey(String mainId, String itemType) {
		// 透過獨特Key取得資料
		return l120m01dDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public void saveListL120m01e(List<L120M01E> list) {
		if (!list.isEmpty()) {
			l120m01eDao.save(list);
			// for (L120M01E model : list) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
	}

	@Override
	public void delListL120m01e(List<L120M01E> list) {
		l120m01eDao.delete(list);
		// for (L120M01E model : list) {
		// // 記錄文件異動記錄
		// docLogService.record(model.getOid(), DocLogEnum.DELETE);
		// }
	}

	@Override
	public List<L120M01F> findToSaveHq(String mainId, String branchType,
			String branchId, String staffJob) {
		return l120m01fDao.findToSaveHq(mainId, branchType, branchId, staffJob);
	}

	@Override
	public L120M01F findL120m01fByUniqueKey(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		return l120m01fDao.findByUniqueKey(mainId, branchType, branchId,
				staffNo, staffJob);
	}

	@Override
	public void delListL120m01f(List<L120M01F> list) {
		l120m01fDao.delete(list);
	}

	@Override
	public void saveListL120m01f(List<L120M01F> list) {
		if (!list.isEmpty()) {
			l120m01fDao.save(list);
		}
	}

	@Override
	public L120M01H findL120m01hByUniqueKey(String mainId, String meetingType) {
		return l120m01hDao.findByUniqueKey(mainId, meetingType);
	}

	@Override
	public Page<Map<String, Object>> getLihai(String allCustId,
			String caseBrid, ISearch search) {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = "";
		String dupNo = "";
		if (!Util.isEmpty(allCustId)) {
			custId = allCustId.substring(0, allCustId.length() - 1);
			dupNo = allCustId.substring(allCustId.length() - 1);
		}
		List<?> rows = this.r6dbService.L140M01A_selLihai(custId, dupNo,
				Util.trim(caseBrid));
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			data.put("cntrNo",
					Util.trim(Util.nullToSpace(dataMap.get("CNTRNO"))));
			data.put("caseDate",
					Util.trim(Util.nullToSpace(dataMap.get("CASEDATE"))));
			data.put("caseNo", Util.toSemiCharString(Util.trim(Util
					.nullToSpace(dataMap.get("CASENO")))));
			data.put("itemType",
					Util.trim(Util.nullToSpace(dataMap.get("ITEMTYPE"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("currentApplyCurr", Util.trim(Util.nullToSpace(dataMap
					.get("CURRENTAPPLYCURR"))));
			data.put("currentApplyAmt",
					Util.trim(Util.nullToSpace(dataMap.get("CURRENTAPPLYAMT"))));
			String lnSubject = "";// 消金的科目要抓產品, 不能直接抓 dataMap.get("LNSUBJECT")
			L140M01A l140m01a = l140m01aDao.findByMainId(Util.trim(data
					.get("mainId")));
			if (l140m01a != null) {
				lnSubject = get_CLS_l140m01a_subject(l140m01a);
			}

			L120M01A l120m01a = null;
			if (l140m01a != null) {
				L120M01C l120m01c = l140m01a.getL120m01c();
				if (l120m01c != null) {
					l120m01a = l120m01c.getL120m01a();
				}
			}
			data.put(
					"endDate",
					l120m01a == null ? "" : Util.trim(TWNDate.toAD(l120m01a
							.getEndDate())));

			data.put("lnSubject", lnSubject);
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> queryList_for_cls_l120s06b_type2_orderByRate(
			String prodKind, String subj, String lnPurs, ISearch search) {
		String endDateBeg = Util.trim(TWNDate.toAD(CapDate.addMonth(
				CapDate.getCurrentTimestamp(), -12)));
		String c900m01d_subjCode2 = "";
		C900M01D c900m01d = clsService.findC900M01D_subjCode(subj);
		if (c900m01d != null) {
			c900m01d_subjCode2 = Util.trim(c900m01d.getSubjCode2());
		}
		/*
		 * 因為利率會調降, 6R 在2021-12 與 2022-04 可能不同 => 單純用 e-Loan 的 l140s02d.nowRate
		 * 來看, 會不準
		 */
		List<Map<String, Object>> fetch_rows = misDBService
				.queryList_for_cls_l120s06b_type2_orderByRate(prodKind,
						c900m01d_subjCode2, lnPurs, endDateBeg, 30);

		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		String[] l120m01c_itemType_arr = new String[] {
				UtilConstants.Cntrdoc.ItemType.額度批覆表,
				UtilConstants.Cntrdoc.ItemType.額度明細表 };
		Properties prop_CLS1151S01Page = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		Map<String, LinkedHashMap<String, String>> curr_rateBaseMap = new HashMap<String, LinkedHashMap<String, String>>();
		Map<String, LinkedHashMap<String, String>> curr_rateUserType = new HashMap<String, LinkedHashMap<String, String>>();
		for (Map<String, Object> dataMap : fetch_rows) {
			count++;
			String cntrNo = Util.trim(MapUtils.getString(dataMap,
					"LNF154_CONTRACT"));
			/*
			 * String documentNo = Util.trim(MapUtils.getString(dataMap,
			 * "LNF020_DOCUMENT_NO")); //ex: 110076CLS00074
			 * if(Util.isEmpty(documentNo)){ continue; } int case_year = 1911 +
			 * Util.parseInt(Util.getLeftStr(documentNo,3 )); String case_brNo =
			 * StringUtils.substring(documentNo, 3, 6); int case_seq =
			 * Util.parseInt(Util.getRightStr(documentNo, 5)); L120M01A l120m01a
			 * = l120m01aDao.findBycaseYearBridSeq(case_year, case_brNo,
			 * case_seq); if(l120m01a==null){ continue; } String itemType = "";
			 * L140M01A l140m01a = null; for(String current_itemType :
			 * l120m01c_itemType_arr){ l140m01a =
			 * l140m01aDao.findByL120m01cMainIdAndcntrNo(l120m01a.getMainId(),
			 * cntrNo, current_itemType); if(l140m01a!=null){ itemType =
			 * current_itemType; break; } } if(l140m01a==null){ continue; }
			 */
			String lnf154_cust_id = Util.trim(MapUtils.getString(dataMap,
					"LNF154_CUST_ID"));
			String lnf154_cust_dup = Util.trim(MapUtils.getString(dataMap,
					"LNF154_CUST_DUP"));
			// J-111-0182 以最近一份簽報書
			Map<String, Object> l140m01a_old_map = eloandbBASEService
					.findCLSCaseByCutIdAndCntrNoOrdByUpdateTime(lnf154_cust_id,
							lnf154_cust_dup, cntrNo);
			String oldL140M01AProperty = Util.trim(MapUtils.getString(
					l140m01a_old_map, "PROPERTY"));
			if (!(UtilConstants.Cntrdoc.Property.變更條件.equals(oldL140M01AProperty) 
					|| UtilConstants.Cntrdoc.Property.不變.equals(oldL140M01AProperty))) {// J-112-0124 不用顯示性質別為僅不變、僅條件變更的額度明細
				String oldL140M01AmainId = Util.trim(MapUtils.getString(
						l140m01a_old_map, "MAINID"));
				if (Util.isEmpty(oldL140M01AmainId)) {
					continue;
				}
				L140M01A l140m01a = clsService
						.findL140M01A_mainId(oldL140M01AmainId);
				if (l140m01a == null) {
					continue;
				}
				L120M01C l120m01c = l140m01a.getL120m01c();
				if (l120m01c == null) {
					continue;
				}
				L120M01A l120m01a = clsService.findL120M01A_mainId(l120m01c
						.getMainId());
				if (l120m01a == null) {
					continue;
				}
				String itemType = l120m01c.getItemType();
				// ============
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("mainId", l140m01a.getMainId());
				data.put("cntrNo", l140m01a.getCntrNo());
				data.put("caseDate",
						Util.trim(TWNDate.toAD(l140m01a.getCaseDate())));
				data.put("endDate", Util.trim(TWNDate.toAD(l120m01a.getEndDate())));
				data.put("caseNo",
						Util.toSemiCharString(Util.trim(l140m01a.getCaseNo())));
				data.put("itemType", itemType);
				data.put("docStatus", Util.trim(l140m01a.getDocStatus()));
				data.put("currentApplyCurr",
						Util.trim(l140m01a.getCurrentApplyCurr()));
				data.put("currentApplyAmt",
						LMSUtil.pretty_numStr(l140m01a.getCurrentApplyAmt()));
				String lnSubject = "";// 消金的科目要抓產品, 不能直接抓 dataMap.get("LNSUBJECT")
				if (l140m01a != null) {
					lnSubject = get_CLS_l140m01a_subject(l140m01a);
				}

				data.put("lnSubject", lnSubject);
				data.put("custId", _hide_custId(Util.trim(l140m01a.getCustId())));
				data.put("custName",
						_hide_custName(Util.trim(l140m01a.getCustName())));
				// 利率比照 L120S06B.itemType2 去抓
				// CLS1141M01FormHandler :: setL120s06b(L120S06B l120s06b, L140M01A
				// l140m01a, String type, String itemType, L120S06A l120s06a)
				String currentApplyCurr = l140m01a.getCurrentApplyCurr();
				if (!curr_rateBaseMap.containsKey(currentApplyCurr)) {
					// 利率基礎
					HashMap<String, LinkedHashMap<String, String>> map = misMislnratService
							.findBaseRateByCurrs(new String[] { currentApplyCurr });
					for (String key : map.keySet()) {
						// L140S02D.rateUser=自訂利率
						map.get(key).put(
								"01",
								prop_CLS1151S01Page
										.getProperty("L140S02D.rateUser"));
					}
					// 利率
					LinkedHashMap<String, String> rateBaseMap = map
							.get(currentApplyCurr);

					// ========================================================
					// 自訂利率選單
					LinkedHashMap<String, String> rateUserType = lnlnf070Service
							.getPrRate(currentApplyCurr);

					curr_rateBaseMap.put(currentApplyCurr, rateBaseMap);
					curr_rateUserType.put(currentApplyCurr, rateUserType);
				}

				data.put(
						"itemType2_itemDscr",
						_get_CLS_l140s02a_rateWithLatestBaseRate(
								l140m01a.getMainId(), l140m01a.getCustId(),
								l140m01a.getDupNo(), currentApplyCurr, "\r",
								prop_CLS1151S01Page,
								curr_rateBaseMap.get(currentApplyCurr),
								curr_rateUserType.get(currentApplyCurr))
								.replaceAll("(<br>|<br/>|<BR>|<BR/>)", "\r"));
				data.put("ltv_from_l140m01o",
						_build_ltv_from_l140m01o(l140m01a.getMainId()));
				data.put("nowExtendPeriod_from_l140s02e",
						_build_nowExtendPeriod_from_l140s02e(l140m01a.getMainId()));
				beanList.add(data);
			}
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > beanList.size() ? start
				+ (beanList.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, beanListnew.size(),
				search.getMaxResults(), search.getFirstResult());

	}

	@Override
	public Page<Map<String, Object>> getCesMainId1(String caseBrId,
			String custId, String dupNo, ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainId1(caseBrId,
				custId, dupNo);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("sn", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainId(String caseBrId,
			String custId, ISearch search) {
		List<?> rows = this.r6dbService
				.findC120M01A_selMainId(caseBrId, custId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainId2(String caseBrId,
			String custId, String dupNo, ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainId2(caseBrId,
				custId, dupNo);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			data.put("oid", Util.trim(Util.nullToSpace(dataMap.get("OID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainId2s(String caseBrId,
			String mainId1, String mainId2, ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainId2sForCls(
				caseBrId, mainId1, mainId2);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			data.put("oid", Util.trim(Util.nullToSpace(dataMap.get("OID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainId2ss(String caseBrId,
			ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainId2ss(caseBrId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("creator",
					Util.trim(Util.nullToSpace(dataMap.get("CREATOR"))));
			data.put("cesId", Util.trim(Util.nullToSpace(dataMap.get("CESID"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainIda(String caseBrId,
			String mainId1, String mainId2, ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainIdaForCls(caseBrId,
				mainId1, mainId2);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("sn", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainIdb(String caseBrId,
			String custId, ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainIdb(caseBrId,
				custId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("sn", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCesMainIdc(String caseBrId,
			ISearch search) {
		List<?> rows = this.r6dbService.findC120M01A_selMainIdc(caseBrId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("sn", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public String findBusi(String cesMainId) {
		String busi = "";
		List<?> rows = this.r6dbService.findBusi(cesMainId);
		Iterator<?> it = rows.iterator();
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			busi = Util.trim(Util.nullToSpace(dataMap.get("VAL")));
		}
		return busi;
	}

	@Override
	public String findFfbody(String cesMainId) {
		String ffbody = "";
		List<?> rows = this.r6dbService.findC140SFFF_selFfbody(cesMainId);
		Iterator<?> it = rows.iterator();
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			ffbody = Util.trim(Util.nullToSpace(dataMap.get("FFBODY")));
		}
		return ffbody;
	}

	@Override
	public Map<String, String> findOther(String cesMainId, String custName) {
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(CLSS07APanel.class);
		Map<String, String> map = new HashMap<String, String>();
		StringBuilder strB = new StringBuilder();
		StringBuilder strB2 = new StringBuilder();
		// 連保人
		List<?> rows1 = this.r6dbService.selPctitle(cesMainId);
		// 大陸投資概況
		List<?> rows2 = this.r6dbService.selCh6_lstock_inv(cesMainId);
		Iterator<?> it1 = rows1.iterator();
		Iterator<?> it2 = rows2.iterator();
		if (it1.hasNext()) {
			strB.append(custName).append(":");
			Map<?, ?> dataMap1 = (Map<?, ?>) it1.next();
			// 詳細資料內容
			JSONObject json = JSONObject.fromObject(dataMap1.get("JSONOB"));
			// 串出生年度
			if (!Util.isEmpty(Util.trim(Util.nullToSpace(json.get("pcBirth"))))) {
				strB.append(pop.getProperty("L120M01D.ces1"))
						.append(TWNDate
								.toAD(CapDate.parseDate(Util.trim(String
										.valueOf(json.get("pcBirth")))))
								.subSequence(0, 4).toString())
						.append(pop.getProperty("L120M01D.ces2"));
			}
			// 串職稱
			strB.append(pop.getProperty("L120M01D.ces4")).append(
					codeService
							.findByCodeTypeAndCodeValue(
									"Title2",
									Util.trim(Util.nullToSpace(dataMap1
											.get("PCTITLE")))).getCodeDesc());

			// 串有無退票記錄
			if (!Util
					.isEmpty(Util.trim(Util.nullToSpace(json.get("refTick2"))))) {
				strB.append(pop.getProperty("L120M01D.ces5"))
						.append("1".equals(Util.trim(Util.nullToSpace(json
								.get("refTick2")))) ? pop
								.getProperty("L120M01D.ces8") : pop
								.getProperty("L120M01D.ces7"))
						.append(pop.getProperty("L120M01D.ces6"));
			}
			strB.append("。");
		} else {
			strB.append(pop.getProperty("L120M01D.ces7")).append("。");
		}
		map.put("other1", strB.toString());
		if (it2.hasNext()) {
			Map<?, ?> dataMap2 = (Map<?, ?>) it2.next();
			// 有無投資大陸概況內容
			String haveData = Util.trim(Util.nullToSpace(dataMap2
					.get("ch6_LStock_inv")));
			// 大陸概況內容
			strB2.append(Util.trim(Util.nullToSpace(dataMap2.get("ffBody"))))
					.append("。");
			map.put("other2", strB2.toString());
			map.put("haveData", haveData);
		} else {
			strB2.append(pop.getProperty("L120M01D.ces7")).append("。");
			map.put("other2", strB2.toString());
			map.put("haveData", "0");
		}
		return map;
	}

	/**
	 * 字元轉ASCII
	 * 
	 * @param chr
	 *            字元
	 * @return
	 */
	public int charToASCII(char chr) {
		return (int) chr;
	}

	@Override
	public L120S03A findL120s03aByUniqueKey(String mainId, String cntrMainId,
			String cntrNo) {
		return l120s03aDao.findByUniqueKey(mainId, cntrMainId, cntrNo);
	}

	@Override
	public void saveL120s03aList(List<L120S03A> list) {
		if (!list.isEmpty()) {
			l120s03aDao.save(list);
			// for (L120S03A model : list) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
	}

	@Override
	public void saveL120s03a(L120S03A model) {
		if (model != null) {
			l120s03aDao.save(model);
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
		}
	}

	@Override
	public void deleteListL120s03a(List<L120S03A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S03A model : list) {
			listOid.add(model.getOid());
		}
		// 刪除多筆資料
		l120s03aDao.delete(list);
		// for (String oid : listOid) {
		// // 記錄文件異動記錄
		// docLogService.record(oid, DocLogEnum.DELETE);
		// }
	}

	@Override
	public L120S05A findL120s05aByMainId(String mainId) {
		// 透過MainId取得資料
		return l120s05aDao.findByUniqueKey(mainId);
	}

	@Override
	public List<L120S06A> findL120s06aByMainId(String mainId) {
		return l120s06aDao.findByMainId(mainId);
	}

	@Override
	public List<L120S06A> findL120s06aByMainIdOrderPrintMode(String mainId) {
		return l120s06aDao.findByMainIdOrderPrintMode(mainId);
	}

	@Override
	public void deleteListL120s06a(List<L120S06A> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S06A model : list) {
			listOid.add(model.getOid());
		}
		l120s06aDao.delete(list);
		// for (String oid : listOid) {
		// // 記錄文件異動記錄
		// docLogService.record(oid, DocLogEnum.DELETE);
		// }
	}

	@Override
	public void saveListL120s06a(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (!listL120s06a.isEmpty()) {
			for (L120S06A model1 : listL120s06a) {
				model1.setUpdater(user.getUserId());
				model1.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l120s06aDao.save(listL120s06a);
			// for (L120S06A model : listL120s06a) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
		if (!listL120s06b.isEmpty()) {
			for (L120S06B model2 : listL120s06b) {
				model2.setUpdater(user.getUserId());
				model2.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l120s06bDao.save(listL120s06b);
			// for (L120S06B model : listL120s06b) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
	}

	@Override
	public void deleteListL120s06a(String[] oidArray) {
		List<L120S06A> listL120s06a = new ArrayList<L120S06A>();
		List<L120S06B> listL120s06b = new ArrayList<L120S06B>();
		for (String oid : oidArray) {
			L120S06A l120s06a = l120s06aDao.findByOid(oid);
			List<L120S06B> list = l120s06bDao
					.findByMainId(l120s06a.getMainId());
			L120S06B l120s06b1 = null;
			L120S06B l120s06b2 = null;
			L120S06B l120s06b3 = null;
			L120S06B l120s06b4 = null;
			L120S06B l120s06b5 = null;
			L120S06B l120s06b6 = null;
			// L120S06B l120s06b7 = null;
			L120S06B l120s06b8 = null;
			L120S06B l120s06b_type1_itemTypeB = null;
			L120S06B l120s06b_type1_itemTypeC = null;
			L120S06B l120s06b_type2_itemTypeB = null;
			L120S06B l120s06b_type2_itemTypeC = null;
			if (!list.isEmpty()) {
				for (L120S06B model : list) {
					model.getType();
					model.getItemType();
					model.getItemDscr();
					model.getRefMainId();
					if (l120s06a.getRefMainId().equals(model.getRefMainId())) {
						if (UtilConstants.Casedoc.L120s06bType.本案授信戶
								.equals(Util.trim(model.getType()))) {
							// 本案授信戶
							if (UtilConstants.Casedoc.L120s06bItemType.利_費_率
									.equals(Util.trim(model.getItemType()))) {
								// 利費率
								if (l120s06b1 == null) {
									// 防止同樣統編與重複序號的資料重複帶入變數
									l120s06b1 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.擔保品
									.equals(Util.trim(model.getItemType()))) {
								// 擔保品
								if (l120s06b2 == null) {
									l120s06b2 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.期限說明
									.equals(Util.trim(model.getItemType()))) {
								// 期限說明
								if (l120s06b3 == null) {
									l120s06b3 = model;
								}
							} 
//							else if (UtilConstants.Casedoc.L120s06bItemType.其他敘做條件
//									.equals(Util.trim(model.getItemType()))) {
//								// 其他敘做條件
//								if (l120s06b7 == null) {
//									l120s06b7 = model;
//								}
//							} 
							else if (UtilConstants.Casedoc.L120s06bItemType.償還方式
									.equals(Util.trim(model.getItemType()))) {
								// 償還方式
								if (l120s06b_type1_itemTypeB == null) {
									l120s06b_type1_itemTypeB = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.核准日期
									.equals(Util.trim(model.getItemType()))) {
								// 核准日期
								if (l120s06b_type1_itemTypeC == null) {
									l120s06b_type1_itemTypeC = model;
								}
							}
						} else if (UtilConstants.Casedoc.L120s06bType.對照授信戶
								.equals(Util.trim(model.getType()))) {
							// 對照授信戶
							if (UtilConstants.Casedoc.L120s06bItemType.利_費_率
									.equals(Util.trim(model.getItemType()))) {
								// 利費率
								if (l120s06b4 == null) {
									l120s06b4 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.擔保品
									.equals(Util.trim(model.getItemType()))) {
								// 擔保品
								if (l120s06b5 == null) {
									l120s06b5 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.期限說明
									.equals(Util.trim(model.getItemType()))) {
								// 期限說明
								if (l120s06b6 == null) {
									l120s06b6 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.其他敘做條件
									.equals(Util.trim(model.getItemType()))) {
								// 其他敘做條件
								if (l120s06b8 == null) {
									l120s06b8 = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.償還方式
									.equals(Util.trim(model.getItemType()))) {
								// 償還方式
								if (l120s06b_type2_itemTypeB == null) {
									l120s06b_type2_itemTypeB = model;
								}
							} else if (UtilConstants.Casedoc.L120s06bItemType.核准日期
									.equals(Util.trim(model.getItemType()))) {
								// 核准日期
								if (l120s06b_type2_itemTypeC == null) {
									l120s06b_type2_itemTypeC = model;
								}
							}
						}
					}
				}
				if (l120s06b1 != null) {
					listL120s06b.add(l120s06b1);
				}
				if (l120s06b2 != null) {
					listL120s06b.add(l120s06b2);
				}
				if (l120s06b3 != null) {
					listL120s06b.add(l120s06b3);
				}
				if (l120s06b4 != null) {
					listL120s06b.add(l120s06b4);
				}
				if (l120s06b5 != null) {
					listL120s06b.add(l120s06b5);
				}
				if (l120s06b6 != null) {
					listL120s06b.add(l120s06b6);
				}
//				if (l120s06b7 != null) {
//					listL120s06b.add(l120s06b7);
//				}
				if (l120s06b8 != null) {
					listL120s06b.add(l120s06b8);
				}
				if (l120s06b_type1_itemTypeB != null) {
					listL120s06b.add(l120s06b_type1_itemTypeB);
				}
				if (l120s06b_type1_itemTypeC != null) {
					listL120s06b.add(l120s06b_type1_itemTypeC);
				}
				if (l120s06b_type2_itemTypeB != null) {
					listL120s06b.add(l120s06b_type2_itemTypeB);
				}
				if (l120s06b_type2_itemTypeC != null) {
					listL120s06b.add(l120s06b_type2_itemTypeC);
				}
			}
			if (l120s06a != null) {
				listL120s06a.add(l120s06a);
			}
		}
		this.deleteListL120s06a(listL120s06a);
		this.deleteListL120s06b(listL120s06b);
	}

	@Override
	public List<L120S06B> findL120s06bByMainId(String mainId) {
		return l120s06bDao.findByMainId(mainId);
	}

	@Override
	public void deleteListL120s06b(List<L120S06B> list) {
		List<String> listOid = new ArrayList<String>();
		for (L120S06B model : list) {
			listOid.add(model.getOid());
		}
		l120s06bDao.delete(list);
	}

	@Override
	public L120S07A findL120s07aByUniqueKey(String mainId) {
		return l120s07aDao.findByUniqueKey(mainId);
	}

	@Override
	public L730A01A findL730a01aByUniqueKey(String mainId, String ownUnit,
			String authType, String authUnit) {
		return l730a01aDao.findByUniqueKey(mainId, ownUnit, authType, authUnit);
	}

	@Override
	public void deleteListL730s01a(List<L730S01A> list) {
		// 刪除多筆資料
		List<String> listOid = new ArrayList<String>();
		for (L730S01A model : list) {
			listOid.add(model.getOid());
		}
		l730s01aDao.delete(list);
		// for (String oid : listOid) {
		// // 記錄文件異動記錄
		// docLogService.record(oid, DocLogEnum.DELETE);
		// }
	}

	@Override
	public void saveListL730s01a(List<L730S01A> list) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (!list.isEmpty()) {
			for (L730S01A model : list) {
				model.setUpdater(user.getUserId());
				model.setUpdateTime(CapDate.getCurrentTimestamp());
			}
			l730s01aDao.save(list);
			// for (L730S01A model : list) {
			// // 記錄文件異動記錄
			// docLogService.record(model.getOid(), DocLogEnum.SAVE);
			// }
		}
	}

	@Override
	public List<L800M01A> findL800m01aByBrno() {
		return l800m01aDao.findByBrno();
	}

	@Override
	public Map<String, String> findCesCustName(String cesMainId) {
		Map<String, String> map = new HashMap<String, String>();
		List<?> rows = this.r6dbService.C140M01A_selCustname(cesMainId);
		Iterator<?> it = rows.iterator();
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			map.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			map.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			map.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			map.put("cesFDate",
					Util.trim(Util.nullToSpace(dataMap.get("CESFDATE"))));
			map.put("cesId", Util.trim(Util.nullToSpace(dataMap.get("CESID"))));
			map.put("oid", Util.trim(Util.nullToSpace(dataMap.get("OID"))));
			map.put("docURL",
					Util.trim(Util.nullToSpace(dataMap.get("DOCURL"))));
			map.put("txCode",
					Util.trim(Util.nullToSpace(dataMap.get("TXCODE"))));
			map.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			map.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			map.put("uid", Util.trim(Util.nullToSpace(dataMap.get("UID"))));
			map.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			map.put("gaapFlag",
					Util.trim(Util.nullToSpace(dataMap.get("GAAPFLAG"))));
		}
		return map;
	}

	@Override
	public Map<String, String> findCesCustName2(String cesMainId) {
		Map<String, String> map = new HashMap<String, String>();
		List<?> rows = this.r6dbService.C120M01A_selCustname(cesMainId);
		Iterator<?> it = rows.iterator();
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			map.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			map.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			map.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			map.put("cesFDate",
					Util.trim(Util.nullToSpace(dataMap.get("COMPLETEDATE"))));
			map.put("cesId", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			map.put("oid", Util.trim(Util.nullToSpace(dataMap.get("OID"))));
			map.put("docURL",
					Util.trim(Util.nullToSpace(dataMap.get("DOCURL"))));
			map.put("txCode",
					Util.trim(Util.nullToSpace(dataMap.get("TXCODE"))));
			map.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			map.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			map.put("uid", Util.trim(Util.nullToSpace(dataMap.get("UID"))));
			map.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			map.put("gaapFlag",
					Util.trim(Util.nullToSpace(dataMap.get("GAAPFLAG"))));
		}
		return map;
	}

	@Override
	public Page<Map<String, Object>> getFss(String caseBrId, String custId,
			String dupNo, ISearch search) {
		List<?> rows = this.r6dbService
				.F101M01A_selFss(caseBrId, custId, dupNo);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("conso", Util.trim(Util.nullToSpace(dataMap.get("CONSO"))));
			data.put("source",
					Util.trim(Util.nullToSpace(dataMap.get("SOURCE"))));
			data.put("updateTime",
					Util.trim(Util.nullToSpace(dataMap.get("UPDATETIME"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public ISearch getMetaSearch() {
		return l120m01aDao.createSearchTemplete();
	}

	/**
	 * 以下的 printCondition 定義在 CLS1141M01Page.html 中
	 */
	@Override
	public Page<Map<String, Object>> getBorrows(String mainId,
			String printCondition, ISearch search) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop_CLS1141R01RptServiceImpl = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		L120M01A l120m01a = l120m01aDao.findByMainId(mainId);
		List<L140M01A> l140m01aList = l140m01aDao
				.findL140m01aListByL120m01cMainIdForPrint(mainId,
						UtilConstants.Cntrdoc.ItemType.額度明細表, null);
		List<L140M01A> l140m01a2List = l140m01aDao
				.findL140m01aListByL120m01cMainIdForPrint(mainId,
						UtilConstants.Cntrdoc.ItemType.額度批覆表, null);
		List<C120M01A> c120m01as = c120m01aDao.findByMainId(mainId);
		List<L120S03A> l120s03aList = l120s03aDao.findByMainId(mainId);
		List<L120S06A> l120s06aList = l120s06aDao.findByMainId(mainId);

		List<Map<String, String>> keyCustIdList = relatedAccountService
				.getKeyCustId(mainId);

		boolean anotherResult = true;

		boolean l120m01a_simplifyFlag_A = false;
		if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.一般消金,
				l120m01a.getSimplifyFlag())) {
			l120m01a_simplifyFlag_A = true;
		}

		/*
		 * thickbox的資訊 簽報書及相關附表 A 母行法人代表提案意見 B 營運中心說明及意見 C 審查意見及補充說明/會簽 D
		 * 授審會/催收會會議決議 E 批覆書 F
		 */
		// 簽報書及相關附表 A
		if ("A".equals(printCondition)) {
			// 借款人資料
			if (!LMSUtil.isParentCase(l120m01a)) {
				for (C120M01A c120m01a : c120m01as) {
					if (UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a
							.getCustPos())) {
						continue;
					}
					// 因非自然人於個金簽案其並無法弄出徵信資料故於列印時不再列印非自然人的徵信資料表。
					if ("N".equals(c120m01a.getNaturalFlag())) {
						continue;
					}
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(c120m01a.getCustName()) + " "
									+ Util.nullToSpace(c120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(c120m01a.getDupNo()));
					// TITLE.RPTNAME29=借款人基本資料
					data.put("custId", c120m01a.getCustId());
					data.put("dupNo", c120m01a.getDupNo());
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME29"));
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1131R01");
					data.put("rpt", ClsConstants.RptNo.借款人基本資料); // rptNo= "R99"
					data.put("oid", Util.nullToSpace(c120m01a.getOid()));
					beanList.add(data);
				}
			}

			// 其他類型
			if ("2".equals(l120m01a.getDocCode())
					|| "3".equals(l120m01a.getDocCode())
					|| "4".equals(l120m01a.getDocCode())
					|| "5".equals(l120m01a.getDocCode())) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustName())
						+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("cntrNo", "");
				data.put("oid", Util.nullToSpace(l120m01a.getOid()));
				if ("2".equals(l120m01a.getDocCode())
						|| "5".equals(l120m01a.getDocCode())) {
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME6"));
				}
				if ("4".equals(l120m01a.getDocCode())) {
					// L120M01A.DOCCODE4=授信異常通報
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("L120M01A.DOCCODE4"));
				}
				if ("3".equals(l120m01a.getDocCode())) {
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME7"));
				}

				if ("2".equals(l120m01a.getDocKind())) {
					data.put("rptNo", "CLS1201R08");
					data.put("rpt", ClsConstants.RptNo.簽報書_案件別_不為一般_外);
				} else {
					data.put("rptNo", "CLS1201R09");
					data.put("rpt", ClsConstants.RptNo.簽報書_案件別_不為一般_內);
				}
				anotherResult = false;
				beanList.add(data);
			} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
					.getDocType())) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustName())
						+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME2"));
				data.put("cntrNo", "");

				boolean l120m01a_simplifyFlag_B = false;
				boolean l120m01a_simplifyFlag_C = false;
				boolean l120m01a_simplifyFlag_D = false;
				if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.卡友信貸,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_B(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_B = true;
					} else {
						// 照舊
					}
				} else if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.勞工紓困,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_C(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_C = true;
					} else {
						// 照舊
					}
				} else if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.歡喜信貸,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_D(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_D = true;
					} else {
						// 照舊
					}
				}

				if (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a
						.getDocKind()))) { // 分行授權內、營運中心授權內
					if (l120m01a_simplifyFlag_A) {
						data.put("rptNo", "CLS1141R11A");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化A);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R11B");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化B);
					} else if (l120m01a_simplifyFlag_C) {
						data.put("rptNo", "CLS1141R11C");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化C);
					} else if (l120m01a_simplifyFlag_D) {
						data.put("rptNo", "CLS1141R11D");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化D);
					} else {
						data.put("rptNo", "CLS1141R11");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內);
					}
				} else {
					if (l120m01a_simplifyFlag_A) {
						data.put("rptNo", "CLS1141R10A");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化A);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R10B");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化B);
					} else if (l120m01a_simplifyFlag_C) {
						data.put("rptNo", "CLS1141R10C");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化C);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R10D");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化D);
					} else {
						data.put("rptNo", "CLS1141R10");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外);
					}
				}
				data.put("oid", Util.nullToSpace(l120m01a.getOid()));
				beanList.add(data);
			}

			// J-110-0390 案件簽爆書(補充說明)
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date J390date;
			try {
				Map<String, String> map = codeService.findByCodeType(
						"LMS_FUNC_ON_FLAG", "zh_TW");
				String J390_Date = Util.trim(map.get("J-110-0390"));
				if (Util.notEquals(J390_Date, "")) {
					J390date = sdf.parse(J390_Date);
					if (l120m01a.getCaseDate().compareTo(J390date) >= 0) {
						L120M01D l120m01d = l120m01dDao.findByUniqueKey(
								l120m01a.getMainId(), "5"); // 找補充說明
						// J-113-0136 調整列印消金簽報書時於補充說明報表列印[觸及與其他債務人相同資訊警示說明]
						L120M01D l120m01d_b = l120m01dDao.findByUniqueKey(
								l120m01a.getMainId(), "b"); // 找觸及與其他借款人相同資訊警示說明
						boolean haveL20m01d_b = false; // 是否有填寫 觸及與其他借款人相同資訊警示說明
						if (l120m01d_b != null && 
								Util.notEquals(Util.trim(l120m01d_b.getItemDscr()), "")){
							haveL20m01d_b = true;
						}
						if (l120m01d != null || haveL20m01d_b) {
							String itemdscr = Util.trim(l120m01d.getItemDscr());
							if (Util.notEquals(itemdscr, "") || haveL20m01d_b ) {
								data = new HashMap<String, Object>();
								data.put(
										"custName",
										Util.nullToSpace(l120m01a.getCustName())
												+ " "
												+ Util.nullToSpace(l120m01a
														.getCustId())
												+ " "
												+ Util.nullToSpace(l120m01a
														.getDupNo()));
								data.put("rptName",
										prop_CLS1141R01RptServiceImpl
												.getProperty("TITLE.RPTNAME41"));
								data.put("cntrNo", "");

								data.put("rptNo", "CLS1141R43");
								data.put("rpt", ClsConstants.RptNo.簽報書_補充說明);
								data.put("oid",
										Util.nullToSpace(l120m01a.getOid()));
								beanList.add(data);
							}
						}
					}
				}
			} catch (ParseException e) {
				logger.error("[J390date.change]", e);
			}

			// 先印4.1.6_授權外授信案件簽報書(附表：共同借款人基本資料)
			// R08,R09不印此塊
			if (anotherResult && l120m01a_simplifyFlag_A == false) {
				// if ("2".equals(Util.nullToSpace(l120m01a.getDocKind()))) {
				// 印4.1.7_授權外授信案件簽報書(附表：共同借款人基本資料_相關資料)
				// 不包含主要借款人

				int count = 0;
				for (C120M01A c120m01a : c120m01as) {
					if ("Y".equals(c120m01a.getKeyMan())
							|| UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a
									.getCustPos())) {
						continue;
					}
					count++;
				}
				if (count > 0) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME27"));
					data.put("rptNo", "CLS1141R25");
					data.put("rpt", "R25");
					beanList.add(data);
				}

				// }
			}

			// 當 L120M01A.docCode in ('3','4') ...不顯示「額度明細表」項目及「額度批附表」項目
			// L140M01A．額度明細表主檔
			// 4.1.13_資本適足率影響數
			if (!("3".equals(l120m01a.getDocCode()) || "4".equals(l120m01a
					.getDocCode()))) {
				// 4.1.13_資本適足率影響數
				if (l120s03aList != null && l120s03aList.size() > 0) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptNo", "CLS1151R04");
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME5"));
					data.put("cntrNo", "");
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					data.put("rpt", "R04");
					beanList.add(data);
				}

				// L140M01A．額度明細表主檔
				for (L140M01A l140m01a : l140m01aList) {
					data = new HashMap<String, Object>();
					data.put("custName", l140m01a.getCustName() + " "
							+ l140m01a.getCustId() + " " + l140m01a.getDupNo());
					data.put("custId", l140m01a.getCustId());
					data.put("dupNo", l140m01a.getDupNo());
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME8"));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.額度明細表);
					data.put("rptNo", "CLS1151R01");
					beanList.add(data);
				}
				// L140M01A．產品資訊附表
				for (L140M01A l140m01a : l140m01aList) {
					data = new HashMap<String, Object>();
					data.put("custName", l140m01a.getCustName() + " "
							+ l140m01a.getCustId() + " " + l140m01a.getDupNo());
					data.put("custId", l140m01a.getCustId());
					data.put("dupNo", l140m01a.getDupNo());
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME30"));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.產品資訊_附表);
					data.put("rptNo", "CLS1151R03");
					beanList.add(data);
				}
				// L140M01P．敘做條件異動比較表
				for (L140M01A l140m01a : l140m01aList) {
					List<L140M01P> l140m01plist = l140m01pDao
							.findByMainId(l140m01a.getMainId());
					if (l140m01plist.size() > 0) {
						data = new HashMap<String, Object>();
						data.put(
								"custName",
								l140m01a.getCustName() + " "
										+ l140m01a.getCustId() + " "
										+ l140m01a.getDupNo());
						data.put("custId", l140m01a.getCustId());
						data.put("dupNo", l140m01a.getDupNo());
						data.put("rptName", prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME33"));
						data.put("cntrNo",
								Util.nullToSpace(l140m01a.getCntrNo()));
						data.put("oid", Util.nullToSpace(l140m01a.getOid()));
						data.put("rpt", "R93");
						data.put("rptNo", "CLS1151R04");
						beanList.add(data);
					}
				}

				// 檢核表
				for (L140M01A l140m01a : l140m01aList) {
					List<L140M01B> l140m01bs = cls1151Service
							.findL140m01bByMainId(l140m01a.getMainId());
					boolean check = false;

					for (L140M01B l140m01b : l140m01bs) {
						// 當大陸地區控管註記 或 央行房貸註記有值時，且列印於檢核表時，才要顯示
						if (UtilConstants.Cntrdoc.l140m01bItemType.央行購置註記
								.equals(l140m01b.getItemType())
								&& "4".equals(l140m01b.getPageNum())) {
							L140M01M l140m01m = cls1151Service
									.findL140m01mByMainId(l140m01a.getMainId());
							if (l140m01m != null) {
								check = true;
								break;
							}
						}
						if (UtilConstants.Cntrdoc.l140m01bItemType.大陸地區授信業務控管註記
								.equals(l140m01b.getItemType())
								&& "4".equals(l140m01b.getPageNum())) {
							L140M01Q l140m01q = cls1151Service
									.findL140m01qByMainId(l140m01a.getMainId());
							if (l140m01q != null) {
								check = true;
								break;
							}
						}
					}

					// ======================
					// 原本 團貸母戶 不印額度明細檢核表，若有 l140m01y，也應增加列印
					if (check == false && LMSUtil.isParentCase(l120m01a)) {
						if (clsService.findL140M01YOrderDefault(
								l140m01a.getMainId()).size() > 0) {
							check = true;
						}
					}
					if (check) {
						data = new HashMap<String, Object>();
						data.put(
								"custName",
								l140m01a.getCustName() + " "
										+ l140m01a.getCustId() + " "
										+ l140m01a.getDupNo());
						data.put("custId", l140m01a.getCustId());
						data.put("dupNo", l140m01a.getDupNo());
						data.put("rptName", prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME31"));
						data.put("cntrNo",
								Util.nullToSpace(l140m01a.getCntrNo()));
						data.put("oid", Util.nullToSpace(l140m01a.getOid()));
						data.put("rpt", ClsConstants.RptNo.額度明細檢核附表);
						data.put("rptNo", "CLS1151R05");
						beanList.add(data);
					}
				}
			}

			if (keyCustIdList.size() > 0) {
				for (Map<String, String> map : keyCustIdList) {
					String keyCustId = Util.trim(map.get("CUSTID"));
					String keyDupNo = Util.trim(map.get("DUPNO"));
					String keyCustName = Util.trim(map.get("CUSTNAME"));

					List<L120S04A> l120s04aList = relatedAccountService
							.findL120s04aByMainIdKeyCustIdDupNodPrtFlag(mainId,
									keyCustId, keyDupNo, "1");
					// 關係戶於本行各項業務往來明細檔
					if (l120s04aList.size() > 0) {
						data = new HashMap<String, Object>();
						data.put("custName", keyCustName + " " + keyCustId
								+ " " + keyDupNo);
						data.put("rptName", prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME10"));
						data.put("cntrNo", "");
						data.put("oid", Util.nullToSpace(l120m01a.getOid()));// CLS1141R01RptServiceImpl
																				// 裡,未用此欄位
						data.put("rpt", "R14");
						data.put("rptNo", "LMS1201R14");
						data.put("keyCustId", keyCustId);
						data.put("keyDupNo", keyDupNo);
						beanList.add(data);
					}

					// 往來彙總實績主檔
					List<L120S04B> listL120s04b = l120s04bDao
							.findByMainIdKeyCustIdDupNo(mainId, keyCustId,
									keyDupNo);

					// Miller added at 2012/09/17
					// 關係戶於本行往來實績彙總表
					if (!listL120s04b.isEmpty()) {
						L120S04B l120s04b = listL120s04b.get(0);
						data = new HashMap<String, Object>();
						data.put("custName", keyCustName + " " + keyCustId
								+ " " + keyDupNo);
						data.put("rptName", prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME22"));
						data.put("cntrNo", "");
						data.put("oid", Util.trim(l120s04b.getOid()));
						data.put("rpt", "R24");
						data.put("rptNo", "LMS1201R24");
						data.put("keyCustId", keyCustId);
						data.put("keyDupNo", keyDupNo);
						beanList.add(data);
					}
				}
			}
			// 授信戶利害關係對照表
			if (l120s06aList.size() > 0
					&& !"3".equals(Util.trim(l120m01a.getDocCode()))) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustName())
						+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("cntrNo", "");

				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME11"));
				data.put("oid", "");
				data.put("refMainId", "");
				data.put("rpt", ClsConstants.RptNo.授信條件對照表);
				data.put("rptNo", "CLS1201R15");
				data.put("custId", "");
				data.put("dupNo", "");
				beanList.add(data);
			}
			String docCode = Util.trim(l120m01a.getDocCode());
			if (UtilConstants.Casedoc.DocCode.一般.equals(docCode)) {

				List<L140M04A> l140m01as = l140m04aDao.findByMainId(mainId);
				if (!l140m01as.isEmpty()) {
					// 查核事項
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME28"));
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1141R26");
					data.put("rpt", "R94");
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}

			}
			// ===============================================================
			// 授信信用風險管理_遵循檢核表
			if (UtilConstants.Casedoc.DocCode.一般.equals(docCode)
					|| UtilConstants.Casedoc.DocCode.團貸案件.equals(docCode)) {
				/*
				 * 取 出 [1]L140M01A的主借款人 [2]L140S01A.custPos==C.共同借款人
				 */
				HashSet<String> custId_dupNoSet = new HashSet<String>();
				for (L140M01A l140m01a : l140m01aList) {
					custId_dupNoSet.add(LMSUtil.getCustKey_len10custId(
							l140m01a.getCustId(), l140m01a.getDupNo()));
					for (L140S01A l140s01a : l140s01aDao.findByMainId(l140m01a
							.getMainId())) {
						if (Util.equals(l140s01a.getCustPos(),
								UtilConstants.lngeFlag.共同借款人)) {
							custId_dupNoSet.add(LMSUtil.getCustKey_len10custId(
									l140s01a.getCustId(), l140s01a.getDupNo()));
						}
					}
				}

				for (C120M01A c120m01a : c120m01as) {
					if (!custId_dupNoSet.contains(LMSUtil
							.getCustKey_len10custId(c120m01a.getCustId(),
									c120m01a.getDupNo()))) {
						continue;
					}
					L120S01M l120s01m = _findL120s01m(mainId,
							c120m01a.getCustId(), c120m01a.getDupNo());
					if (l120s01m == null) {
						continue;
					}
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(c120m01a.getCustName()) + " "
									+ Util.nullToSpace(c120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(c120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME34"));// 「授信信用風險管理」遵循檢核表
					data.put("cntrNo", "");
					data.put("rptNo", "LMS1201R30");
					data.put("rpt", ClsConstants.RptNo.授信信用風險管理_遵循檢核表);
					data.put("oid", Util.nullToSpace(c120m01a.getOid()));
					beanList.add(data);
				}
			}
			if (true) {
				List<L120S09A> listL120s09a = clsService.findL120S09A(mainId);
				if (listL120s09a.size() > 0) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME35")); // 洗錢防制檢核表
					data.put("cntrNo", "");
					data.put("rptNo", "LMS1201R33");
					data.put("rpt", ClsConstants.RptNo.洗錢防制檢核表);
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}

				// J-109-0178_10702_B1001 Web e-Loan 消金簽報書新增申貸資料核對表頁籤及列印功能
				List<C120S01V> c120s01v_list = clsService.findC120S01VByMainid(
						mainId, l120m01a.getCustId(), l120m01a.getDupNo());
				if (c120s01v_list.size() > 0) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME36")); // 申請資料核對表
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1201R24");
					data.put("rpt", ClsConstants.RptNo.申請資料核對表);
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}
			}

			if (true) {
				List<L120S18A> l120s18a_llist = clsService
						.findL120S18A_byMainId_orderBySeq(mainId);
				if (l120s18a_llist.size() > 0) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME40")); // 同一經濟關係人額度彙總表
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1201R40");
					data.put("rpt", ClsConstants.RptNo.同一經濟關係人額度彙總表);
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}
			}

			// TITLE.RPTNAME42=高齡客戶關懷檢核表
			List<L120S01R> l120s01rList = l120s01rDao.findByMainId(mainId);
			// 有高齡客戶關懷檢核表主檔資料才需要秀出該報表在列印grid
			if (l120s01rList.size() > 0) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustId())
						+ " " + Util.nullToSpace(l120m01a.getDupNo()) + " "
						+ Util.nullToSpace(l120m01a.getCustName()));
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME42"));// 高齡客戶關懷檢核表
				data.put("cntrNo", "");
				data.put("rptNo", "CLS1201R42");
				data.put("rpt", ClsConstants.RptNo.高齡客戶關懷檢核表);
				data.put("oid", "");
				beanList.add(data);
			}
			
			// TITLE.RPTNAME45=消金業務授權層級試算表
			List<L120M01L> l120m01lList = l120m01lDao.findByMainId(mainId);
			/// 有L120M01L簽報書各業務審核層級記錄檔
			// 資料才需要秀出該報表在列印grid
			if (l120m01lList.size() > 0) {
				data = new HashMap<String, Object>();
				data.put(
						"custName",
						Util.nullToSpace(l120m01a.getCustName()) + " "
								+ Util.nullToSpace(l120m01a.getCustId())
								+ " "
								+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME45"));// 消金業務授權層級試算表
				data.put("cntrNo", "");
				data.put("rptNo", "CLS1201R45");
				data.put("rpt", ClsConstants.RptNo.消金業務授權層級試算表);
				data.put("oid", "");
				beanList.add(data);
			}
		} else if ("D".equals(printCondition)) { // 審查意見及補充說明/會簽 D
			if ("3".equals(l120m01a.getAreaChk())) {
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A+B,C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("hasCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_Y");
				data.put("rptHandle", "AB C");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A+B,C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("noCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_N");
				data.put("rptHandle", "AB C");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A+B+C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("hasCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_Y");
				data.put("rptHandle", "ABC");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A+B+C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("noCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_N");
				data.put("rptHandle", "ABC");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A,B+C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("hasCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_Y");
				data.put("rptHandle", "A BC");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(A,B+C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("noCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_N");
				data.put("rptHandle", "A BC");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("hasCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_Y");
				data.put("rptHandle", "C");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
								+ l120m01a.getCustName());
				data.put(
						"rptName",
						prop_CLS1141R01RptServiceImpl
								.getProperty("TITLE.RPTNAME16")
								+ "(C)"
								+ prop_CLS1141R01RptServiceImpl
										.getProperty("noCheck"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R18");
				data.put("rpt", "R18_N");
				data.put("rptHandle", "C");
				beanList.add(data);
			} else {
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustName() + " " + l120m01a.getCustId()
								+ " " + l120m01a.getDupNo());
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME16_Y"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R21");
				data.put("rpt", "R21_Y");
				data.put("rptHandle", "");
				beanList.add(data);
				data = new HashMap<String, Object>();
				data.put("custName",
						l120m01a.getCustName() + " " + l120m01a.getCustId()
								+ " " + l120m01a.getDupNo());
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME16_N"));
				data.put("cntrNo", "");
				data.put("oid", "");
				data.put("custId", Util.nullToSpace(l120m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptNo", "LMS1201R21");
				data.put("rpt", "R21_N");
				data.put("rptHandle", "");
				beanList.add(data);
			}
		} else if ("Z".equals(printCondition)) { // 營運中心會議決議 Z
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("rptName", prop_CLS1141R01RptServiceImpl
					.getProperty("TITLE.RPTNAME25"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R20");
			data.put("rptNo", "LMS1201R20");
			data.put("meetingType", "3");
			beanList.add(data);
		} else if ("E".equals(printCondition)) { // 授審會會議決議 E
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("rptName", prop_CLS1141R01RptServiceImpl
					.getProperty("TITLE.RPTNAME17"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R20");
			data.put("rptNo", "LMS1201R20");
			data.put("meetingType",
					(LMSUtil.isSpecialBranch(user.getUnitNo())) ? "A" : "1");
			beanList.add(data);
		} else if ("F".equals(printCondition)) { // 催收會會議決議 F
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("rptName", prop_CLS1141R01RptServiceImpl
					.getProperty("TITLE.RPTNAME18"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R20");
			data.put("rptNo", "LMS1201R20");
			data.put("meetingType",
					(LMSUtil.isSpecialBranch(user.getUnitNo())) ? "B" : "2");
			beanList.add(data);
		} else if ("F2".equals(printCondition)) { // 常董會會議決議 F2
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustId() + " " + l120m01a.getDupNo() + " "
							+ l120m01a.getCustName());
			data.put("rptName", prop_CLS1141R01RptServiceImpl
					.getProperty("TITLE.RPTNAME18a"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R20");
			data.put("rptNo", "LMS1205R20");
			data.put("meetingType", (UtilConstants.BankNo.授管處.equals(user
					.getUnitNo())) ? "4" : "C");
			beanList.add(data);
		} else if ("C".equals(printCondition)) { // 營運中心說明及意見 C
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("rptName", prop_CLS1141R01RptServiceImpl
					.getProperty("TITLE.RPTNAME19"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R19");
			data.put("rptNo", "LMS1201R19");
			beanList.add(data);
		} else if ("G".equals(printCondition)) { // 海外聯貸案會簽意見 G
			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put(
					"rptName",
					(LMSUtil.isSpecialBranch(Util.trim(l120m01a.getCaseBrId()))) ? prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME26")
							: prop_CLS1141R01RptServiceImpl
									.getProperty("TITLE.RPTNAME20"));
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R22");
			data.put("rptNo", "LMS1201R22");
			// 025 = 國金部
			if ("025".equals(l120m01a.getAreaBrId())) {
				data.put("branchType", "6");
			} else {
				data.put("branchType", "3");
			}
			beanList.add(data);
		} else if ("H".equals(printCondition)) { // 批覆書 H

			// 批覆書
			// L140M01A．額度批覆表主檔
			// J-108-0316_10702_B1001 Web
			// e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
			for (L140M01A l140m01a : l140m01a2List) {
				data = new HashMap<String, Object>();
				String rptName = prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME9");
				if (Util.trim(l120m01a.getDocStatus()).equals(
						CreditDocStatusEnum.會簽後修改編製中.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.會簽後修改待覆核.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.總處營業單位已會簽.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.總處營業單位待覆核.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提授審會.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提催收會.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提常董會.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提審計委員會.getCode())) {
					rptName = rptName + "(稿)";
				}

				data.put("custName",
						l140m01a.getCustName() + " " + l140m01a.getCustId()
								+ " " + l140m01a.getDupNo());
				data.put("rptName", rptName);
				data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
				data.put("oid", Util.nullToSpace(l140m01a.getOid()));
				data.put("rpt", "R13");
				data.put("custId", Util.nullToSpace(l140m01a.getCustId()));
				data.put("dupNo", Util.nullToSpace(l140m01a.getDupNo()));
				data.put("rptNo", "CLS1151R02");
				beanList.add(data);
			}
			for (L140M01A l140m01a : l140m01a2List) {
				data = new HashMap<String, Object>();
				data.put("custName",
						l140m01a.getCustName() + " " + l140m01a.getCustId()
								+ " " + l140m01a.getDupNo());
				data.put("custId", l140m01a.getCustId());
				data.put("dupNo", l140m01a.getDupNo());
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME30"));
				data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
				data.put("oid", Util.nullToSpace(l140m01a.getOid()));
				data.put("rpt", "R12_F");
				data.put("rptNo", "CLS1151R03");
				beanList.add(data);
			}
			for (L140M01A l140m01a : l140m01a2List) {
				// J-108-0316_10702_B1001 Web
				// e-Loan調整國外部、國際金融業務分行與金控總部分行等原總處營業單位會簽流程
				List<L140M01B> l140m01bs = cls1151Service
						.findL140m01bByMainId(l140m01a.getMainId());
				boolean check = false;
				String rptName = prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME32");
				if (Util.trim(l120m01a.getDocStatus()).equals(
						CreditDocStatusEnum.會簽後修改編製中.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.會簽後修改待覆核.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.總處營業單位已會簽.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.總處營業單位待覆核.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提授審會.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提催收會.getCode())
						|| Util.trim(l120m01a.getDocStatus()).equals(
								CreditDocStatusEnum.特殊分行提常董會.getCode())) {
					rptName = rptName + "(稿)";
				}
				for (L140M01B l140m01b : l140m01bs) {
					// 當大陸地區控管註記 或 央行房貸註記有值時，且列印於檢核表時，才要顯示
					if ("X".equals(l140m01b.getItemType())
							&& "4".equals(l140m01b.getPageNum())) {
						L140M01M l140m01m = cls1151Service
								.findL140m01mByMainId(l140m01a.getMainId());
						if (l140m01m != null) {
							check = true;
							break;
						}
					}
					if ("Y".equals(l140m01b.getItemType())
							&& "4".equals(l140m01b.getPageNum())) {
						L140M01Q l140m01q = cls1151Service
								.findL140m01qByMainId(l140m01a.getMainId());
						if (l140m01q != null) {
							check = true;
							break;
						}
					}
				}
				if (check) {
					data = new HashMap<String, Object>();
					data.put("custName", l140m01a.getCustName() + " "
							+ l140m01a.getCustId() + " " + l140m01a.getDupNo());
					data.put("custId", l140m01a.getCustId());
					data.put("dupNo", l140m01a.getDupNo());
					data.put("rptName", rptName);
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("rpt", "R29");
					data.put("rptNo", "CLS1151R05");
					beanList.add(data);
				}

			}
		} else if ("J".equals(printCondition)) { // 異常通報表
			if (UtilConstants.Casedoc.DocCode.異常通報.equals(Util.trim(l120m01a
					.getDocCode()))
					&& (UtilConstants.Casedoc.typCd.DBU.equals(Util
							.trim(l120m01a.getTypCd())) || UtilConstants.Casedoc.typCd.OBU
							.equals(Util.trim(l120m01a.getTypCd())))) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustName())
						+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("cntrNo", "");

				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME23"));
				data.put("oid", "");
				data.put("refMainId", "");
				data.put("rpt", "R26");
				data.put("rptNo", "LMS1205R26");
				data.put("custId", "");
				data.put("dupNo", "");
				beanList.add(data);

				if (user.getUnitNo().equals(UtilConstants.BankNo.授管處)
						|| (CreditDocStatusEnum.海外_已核准.getCode().equals(
								Util.trim(l120m01a.getDocStatus())) || (CreditDocStatusEnum.海外_婉卻
								.getCode().equals(Util.trim(l120m01a
								.getDocStatus()))))) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("cntrNo", "");

					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME24"));
					data.put("oid", "");
					data.put("refMainId", "");
					data.put("rpt", "R27");
					data.put("rptNo", "LMS1205R27");
					data.put("custId", "");
					data.put("dupNo", "");
					beanList.add(data);
				}

			}
		} else if ("I".equals(printCondition)) { // 借款人申請書
			String itemType = lmsService.checkL140M01AItemType(l120m01a);
			if (UtilConstants.Cntrdoc.ItemType.額度批覆表.equals(itemType)) {
				// 批覆表如果還未產生，就用額度明細表的資料
				List<L140M01A> list = l140m01aDao
						.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
								itemType, null);
				if (CollectionUtils.isEmpty(list)) {
					itemType = UtilConstants.Cntrdoc.ItemType.額度明細表;
				}
			}

			List<L140M01A> l140m01a_list = l140m01aDao
					.findL140m01aListByL120m01cMainId(l120m01a.getMainId(),
							itemType, null);
			Set<String> main_borrower_set = new HashSet<String>();
			for (L140M01A l140m01a : l140m01a_list) {
				if ((Util.equals(l140m01a.getProPerty(),
						UtilConstants.Cntrdoc.Property.不變))
						|| Util.equals(l140m01a.getProPerty(),
								UtilConstants.Cntrdoc.Property.取消)) {
					continue;
				}
				main_borrower_set.add(LMSUtil.getCustKey_len10custId(
						l140m01a.getCustId(), l140m01a.getDupNo()));
			}

			if (main_borrower_set.size() == 0) {
				for (C120M01A c120m01a : c120m01as) {
					String keyMan = c120m01a.getKeyMan();
					if ("Y".equals(keyMan)) {
						main_borrower_set.add(LMSUtil.getCustKey_len10custId(
								c120m01a.getCustId(), c120m01a.getDupNo()));
					}
				}
			}

			for (C120M01A c120m01a : c120m01as) {
				if (!main_borrower_set.contains(LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo()))) {
					continue;
				}

				data = new HashMap<String, Object>();
				data.put("custName",
						c120m01a.getCustName() + " " + c120m01a.getCustId()
								+ " " + c120m01a.getDupNo());
				data.put("custId", c120m01a.getCustId());
				data.put("dupNo", c120m01a.getDupNo());
				data.put("rptName", "借款申請書暨個人資料表");
				data.put("cntrNo", "");
				data.put("oid", Util.nullToSpace(c120m01a.getOid()));
				data.put("rpt", ClsConstants.RptNo.借款申請書_申貸人);
				data.put("rptNo", "CLS1171R03");
				beanList.add(data);

			}
			for (C120M01A c120m01a : c120m01as) {
				if (main_borrower_set.contains(LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo()))) {
					continue;
				}
				String naturalFlag = c120m01a.getNaturalFlag();
				String custPos = c120m01a.getCustPos();
				if ("S".equals(custPos)) {
					continue;
				}
				if ("Y".equals(naturalFlag)) {
					data = new HashMap<String, Object>();
					data.put("custName", c120m01a.getCustName() + " "
							+ c120m01a.getCustId() + " " + c120m01a.getDupNo());
					data.put("custId", c120m01a.getCustId());
					data.put("dupNo", c120m01a.getDupNo());
					data.put("rptName", "從債務人資料表");
					data.put("cntrNo", "");
					data.put("oid", Util.nullToSpace(c120m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.借款申請書_從債務人);
					data.put("rptNo", "CLS1171R04");
					beanList.add(data);
				}

			}

			List<String> l140s02fCMSid = eloandbBASEService.getL140s02fCMSid(
					l120m01a.getMainId(), itemType);
			if (CollectionUtils.isNotEmpty(l140s02fCMSid)) {
				for (String s : l140s02fCMSid) {
					data = new HashMap<String, Object>();
					data.put("custName", l120m01a.getCustName() + " "
							+ l120m01a.getCustId() + " " + l120m01a.getDupNo());
					data.put("custId", l120m01a.getCustId());
					data.put("dupNo", l120m01a.getDupNo());
					data.put("rptName", "房屋貸款擔保品用途聲明切結事項");
					data.put("cntrNo", "");
					data.put("oid", Util.nullToSpace(s));
					data.put("rpt", ClsConstants.RptNo.借款申請書_房貸擔保品用途聲明切結事項);
					data.put("rptNo", "CLS1171R06");
					beanList.add(data);
				}
			}

			for (C120M01A c120m01a : c120m01as) {

				String naturalFlag = c120m01a.getNaturalFlag();
				if ("Y".equals(naturalFlag)) {

					List<Map<String, Object>> law333 = misDBService
							.getBankLaw33_3(c120m01a.getCustId(),
									c120m01a.getDupNo());
					boolean has_data = false;
					for (Map<String, Object> item : law333) {
						String sn = Util.trim(MapUtils
								.getString(item, "SN", ""));
						if (Util.equals("01", sn)) {
							// 本人
						} else {
							has_data = true;
						}
					}
					// J-111-0364 改為每一案皆要印出銀行法第33條之3「同一關係人」資料表
					has_data = true;
					// 33-3 無資料就不印
					if (has_data == false) {
						continue;
					}
					data = new HashMap<String, Object>();
					data.put("custName", c120m01a.getCustName() + " "
							+ c120m01a.getCustId() + " " + c120m01a.getDupNo());
					data.put("custId", c120m01a.getCustId());
					data.put("dupNo", c120m01a.getDupNo());
					data.put("rptName", "銀行法第33條之3「同一關係人」資料表");
					data.put("cntrNo", "");
					data.put("oid", Util.nullToSpace(c120m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.借款申請書_銀行法33_3);
					data.put("rptNo", "CLS1171R02");
					beanList.add(data);
				}

			}

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "客戶簽章及附送文件");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R103");
			data.put("rptNo", "CLS1171R05");
			beanList.add(data);

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "附件1");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R107");
			data.put("rptNo", "CLS1171R09");
			beanList.add(data);

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "附表");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R106");
			data.put("rptNo", "CLS1171R08");
			beanList.add(data);

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "附件2");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R108");
			data.put("rptNo", "CLS1171R10");
			beanList.add(data);

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "附表");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R106");
			data.put("rptNo", "CLS1171R08");
			beanList.add(data);

			data = new HashMap<String, Object>();
			data.put("custName",
					l120m01a.getCustName() + " " + l120m01a.getCustId() + " "
							+ l120m01a.getDupNo());
			data.put("custId", l120m01a.getCustId());
			data.put("dupNo", l120m01a.getDupNo());
			data.put("rptName", "房屋貸款特別提醒事項");
			data.put("cntrNo", "");
			data.put("oid", Util.nullToSpace(l120m01a.getOid()));
			data.put("rpt", "R105");
			data.put("rptNo", "CLS1171R07");
			beanList.add(data);

			/*
			 * for (L140M01A l140m01a : l140m01aList) { data = new
			 * HashMap<String, Object>(); data.put("custName",
			 * l140m01a.getCustName() + " " + l140m01a.getCustId() + " " +
			 * l140m01a.getDupNo()); data.put("custId", l140m01a.getCustId());
			 * data.put("dupNo", l140m01a.getDupNo()); data.put("rptName",
			 * "借款人申請書"); data.put("cntrNo",
			 * Util.nullToSpace(l140m01a.getCntrNo())); data.put("oid",
			 * Util.nullToSpace(l140m01a.getOid())); data.put("rpt", "R100");
			 * data.put("rptNo", "CLS1171R01"); beanList.add(data); }
			 */

		} else if ("simplePrint".equals(printCondition)) {
			// 簡易列印，只列印 案件簽報書(個人)，額度明細表，產品資訊(附表)，查核事項
			// 其他類型
			if ("2".equals(l120m01a.getDocCode())
					|| "3".equals(l120m01a.getDocCode())
					|| "4".equals(l120m01a.getDocCode())
					|| "5".equals(l120m01a.getDocCode())) {

			} else if (UtilConstants.Casedoc.DocType.個金.equals(l120m01a
					.getDocType())) {
				data = new HashMap<String, Object>();
				data.put("custName", Util.nullToSpace(l120m01a.getCustName())
						+ " " + Util.nullToSpace(l120m01a.getCustId()) + " "
						+ Util.nullToSpace(l120m01a.getDupNo()));
				data.put("rptName", prop_CLS1141R01RptServiceImpl
						.getProperty("TITLE.RPTNAME2"));
				data.put("cntrNo", "");

				boolean l120m01a_simplifyFlag_B = false;
				boolean l120m01a_simplifyFlag_C = false;
				boolean l120m01a_simplifyFlag_D = false;
				if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.卡友信貸,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_B(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_B = true;
					} else {
						// 照舊
					}
				} else if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.勞工紓困,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_C(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_C = true;
					} else {
						// 照舊
					}
				} else if (Util.equals(UtilConstants.Casedoc.SimplifyFlag.歡喜信貸,
						l120m01a.getSimplifyFlag())) {
					String fmtstr_html = build_html_SimplifyFlag_D(l120m01a);
					if (Util.isNotEmpty(fmtstr_html)) {
						l120m01a_simplifyFlag_D = true;
					} else {
						// 照舊
					}
				}

				if (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(l120m01a
						.getDocKind()))) { // 分行授權內、營運中心授權內
					if (l120m01a_simplifyFlag_A) {
						data.put("rptNo", "CLS1141R11A");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化A);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R11B");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化B);
					} else if (l120m01a_simplifyFlag_C) {
						data.put("rptNo", "CLS1141R11C");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化C);
					} else if (l120m01a_simplifyFlag_D) {
						data.put("rptNo", "CLS1141R11D");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內_簡化D);
					} else {
						data.put("rptNo", "CLS1141R11");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_內);
					}
				} else {
					if (l120m01a_simplifyFlag_A) {
						data.put("rptNo", "CLS1141R10A");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化A);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R10B");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化B);
					} else if (l120m01a_simplifyFlag_C) {
						data.put("rptNo", "CLS1141R10C");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化C);
					} else if (l120m01a_simplifyFlag_B) {
						data.put("rptNo", "CLS1141R10D");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外_簡化D);
					} else {
						data.put("rptNo", "CLS1141R10");
						data.put("rpt", ClsConstants.RptNo.簽報書_案件別_一般_外);
					}
				}
				data.put("oid", Util.nullToSpace(l120m01a.getOid()));
				beanList.add(data);
			}

			if (!("3".equals(l120m01a.getDocCode()) || "4".equals(l120m01a
					.getDocCode()))) {
				// L140M01A．額度明細表主檔
				for (L140M01A l140m01a : l140m01aList) {
					data = new HashMap<String, Object>();
					data.put("custName", l140m01a.getCustName() + " "
							+ l140m01a.getCustId() + " " + l140m01a.getDupNo());
					data.put("custId", l140m01a.getCustId());
					data.put("dupNo", l140m01a.getDupNo());
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME8"));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.額度明細表);
					data.put("rptNo", "CLS1151R01");
					beanList.add(data);
				}
				// L140M01A．產品資訊附表
				for (L140M01A l140m01a : l140m01aList) {
					data = new HashMap<String, Object>();
					data.put("custName", l140m01a.getCustName() + " "
							+ l140m01a.getCustId() + " " + l140m01a.getDupNo());
					data.put("custId", l140m01a.getCustId());
					data.put("dupNo", l140m01a.getDupNo());
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME30"));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("rpt", ClsConstants.RptNo.產品資訊_附表);
					data.put("rptNo", "CLS1151R03");
					beanList.add(data);
				}

			}

			if (!LMSUtil.isParentCase(l120m01a)) {

				boolean print = false;
				for (C120M01A c120m01a : c120m01as) {
					if (UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a
							.getCustPos())) {
						continue;
					}
					// 因非自然人於個金簽案其並無法弄出徵信資料故於列印時不再列印非自然人的徵信資料表。
					if ("N".equals(c120m01a.getNaturalFlag())) {
						continue;
					}
					print = true;
					break;
				}

				if (print) {
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", "簡易列印-借款人連保人基本資料");
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1131R09");
					data.put("rpt", ClsConstants.RptNo.簡易列印_借款人連保人基本資料); // rptNo=R99_S
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}

			}

			String docCode = Util.trim(l120m01a.getDocCode());
			if (UtilConstants.Casedoc.DocCode.一般.equals(docCode)) {

				List<L140M04A> l140m01as = l140m04aDao.findByMainId(mainId);
				if (!l140m01as.isEmpty()) {
					// 查核事項
					data = new HashMap<String, Object>();
					data.put(
							"custName",
							Util.nullToSpace(l120m01a.getCustName()) + " "
									+ Util.nullToSpace(l120m01a.getCustId())
									+ " "
									+ Util.nullToSpace(l120m01a.getDupNo()));
					data.put("rptName", prop_CLS1141R01RptServiceImpl
							.getProperty("TITLE.RPTNAME28"));
					data.put("cntrNo", "");
					data.put("rptNo", "CLS1141R26");
					data.put("rpt", "R94");
					data.put("oid", Util.nullToSpace(l120m01a.getOid()));
					beanList.add(data);
				}

			}

		}

		return new Page<Map<String, Object>>(beanList, beanList.size(),
				search.getMaxResults(), search.getFirstResult());

	}

	@Override
	public List<L120M01A> findL120m01asByOids(String[] oids) {
		return l120m01aDao.findByOids(oids);
	}

	@Override
	public void saveL120m01as(List<L120M01A> l120m01as) {
		if (!l120m01as.isEmpty()) {
			l120m01aDao.save(l120m01as);
		}
	}

	@Override
	public void saveL120m01aAndL120m01d(List<L120M01A> list1,
			List<L120M01D> list2) {
		if (!list1.isEmpty()) {
			l120m01aDao.save(list1);
		}
		if (!list2.isEmpty()) {
			l120m01dDao.save(list2);
		}
	}

	@Override
	public C140M01A getC140M01A(String oid) {
		return c140m01aDao.find(oid);
	}

	@Override
	public C140M07A getC140M07A(String mainId, String pid, String tab,
			String subtab) {
		List<C140M07A> c140m07as = c140m07aDao.findByMainPidTab(pid, mainId,
				tab, subtab);
		if (c140m07as != null && !c140m07as.isEmpty()) {
			return c140m07as.get(0);
		}
		return null;
	}

	@Override
	public C140JSON getC140JSONByMainPidTab(String uid, String mainId,
			String tab, String subTab) {
		return c140jsonDao.findByMainPidTab(uid, mainId, tab, subTab);
	}

	@Override
	public List<C140S07A> getS07aByMetaAndTab(C140M01A m01a, String tab,
			String subtab) {
		return c140s07aDao.findByMainIdAndTab(m01a.getMainId(), tab, subtab);
	}

	@Override
	public List<C140S07A> getS07aBySubDocAndTab(String mainId, String pid,
			String tab, String subtab) {
		return c140s07aDao.findBySubDocAndTab(mainId, pid, tab, subtab);
	}

	@Override
	public List<C140S07A> getS07aByM07aAndTab(C140M07A m07a) {
		return c140s07aDao.findByMainIdAndTab(m07a.getMainId(), m07a.getTab(),
				m07a.getSubtab());
	}

	public C140SFFF getC140SFFF(String mainId, String pid, String filedId,
			String tab) {
		return c140SFFFDao.findC140SFFF(mainId, pid, filedId, tab);
	}

	public List<C140SFFF> getC140SFFF(String mainId, String pid, String tab) {
		return c140SFFFDao.findC140SFFF(mainId, pid, tab);
	}

	public DocFile getDocFile(String oid) {
		return docFileService.read(oid);
	}

	@Override
	public List<C140JSON> getC140JSONByMainPidTab(String uid, String mainId,
			String tab) {
		if (CapString.isEmpty(tab)) {
			return c140jsonDao.findByMainPid(uid, mainId);
		}
		return c140jsonDao.findByMainPidTab(uid, mainId, tab);
	}

	@Override
	public C140SDSC getC140SDSCByMainPidTab(String uid, String mainId,
			String tab, String fieldId) {
		return c140sdscDao.findByMainPidTab(uid, mainId, tab, fieldId);
	}

	@Override
	public List<C140SDSC> getC140SDSCByMainPidTab(String uid, String mainId,
			String tab) {
		if (CapString.isEmpty(tab)) {
			return c140sdscDao.findByMainPid(uid, mainId);
		}
		return c140sdscDao.findByMainPidTab(uid, mainId, tab);
	}

	@Override
	public void saveC140M01A(C140M01A c140m01a) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		c140m01a.setUpdater(user.getUserId());
		c140m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		c140m01aDao.save(c140m01a);
		if (c140m01a.getC140sfffs() != null)
			c140SFFFDao.save(c140m01a.getC140sfffs());
		if (c140m01a.getBrelateds() != null)
			saveBRelated(c140m01a.getBrelateds());
	}

	public void saveC140m01aAndDocFiles(C140M01A c140m01a,
			List<DocFile> docFiles) {

		saveC140M01A(c140m01a);

		if (docFiles != null) {
			saveDocFile(docFiles);
		}
	}

	public void saveDocFile(List<DocFile> docFiles) {
		for (DocFile docFile : docFiles) {
			docFileService.save(docFile);
		}
	}

	// Delete -------------------------------------------------------------

	@Override
	public void deleteC140M04A(String mainOid) {
		C140M04A meta = c140m04aDao.find(mainOid);
		String mainId = meta.getMainId();
		List<L120M01E> list = l120m01eDao.findByMainId(mainId);
		if (!list.isEmpty()) {
			for (L120M01E model : list) {
				if (meta.equals(model.getC140m04a())) {
					l120m01eDao.delete(model);
					break;
				}
			}
		}
		c140m04aDao.delete(meta);
	}

	@Override
	public void deleteC140M07A(List<C140M07A> c140m07as) {
		for (C140M07A m07a : c140m07as) {
			c140s07aDao.deleteByMeta(m07a);
			c140m07aDao.delete(m07a);
		}
	}

	@Override
	public void saveDocument(C140M01A c140m01a, List<DocFile> docFiles) {
		boolean isNew = CapString.isEmpty(c140m01a.getOid());

		c140m01a.setRandomCode(IDGenerator.getRandomCode());
		saveC140m01aAndDocFiles(c140m01a, docFiles);
		if (isNew) {
			// cesFlowService.start(flowName, c140m01a.getOid());
		} else {
			docLogService.record(c140m01a.getOid(), DocLogEnum.SAVE);
		}
		tempDataService.deleteByMainId(c140m01a.getMainId());
	}

	@Override
	public void saveBRelated(List<BRelated> brelateds) {
		for (BRelated brelated : brelateds) {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			brelated.setUpdater(user.getUserId());
			brelated.setUpdateTime(CapDate.getCurrentTimestamp());
			brelatedDao.save(brelated);
		}
	}

	@Override
	public void deleteC140S04A(String subOid) {
		C140S04A meta = c140s04aDao.find(subOid);
		c140s04aDao.delete(meta);
	}

	@Override
	public void deleteC140S04B(String subOid) {
		C140S04B meta = c140s04bDao.find(subOid);
		c140s04bDao.delete(meta);
	}

	@Override
	public void deleteC140S04C(String subOid) {
		C140S04C meta = c140s04cDao.find(subOid);
		c140s04cDao.delete(meta);
	}

	@Override
	public Page<C140S04A> getC140S04APage(ISearch search) {
		return c140s04aDao.findPage(search);
	}

	@Override
	public Page<C140S04B> getC140S04BPage(ISearch search) {
		return c140s04bDao.findPage(search);
	}

	@Override
	public Page<C140S04C> getC140S04CPage(ISearch search) {
		return c140s04cDao.findPage(search);
	}

	@Override
	public C140S04A getC140S04A(String oid) {
		return c140s04aDao.find(oid);
	}

	@Override
	public List<C140S04A> getC140S04A(String mainId, String uid) {
		ISearch search = c140s04aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), uid);
		return c140s04aDao.find(search);
	}

	@Override
	public C140S04B getC140S04B(String oid) {
		return c140s04bDao.find(oid);
	}

	@Override
	public List<C140S04B> getC140S04B(String mainId, String uid) {
		ISearch search = c140s04bDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), uid);
		return c140s04bDao.find(search);
	}

	@Override
	public C140S04C getC140S04C(String oid) {
		return c140s04cDao.find(oid);
	}

	@Override
	public List<C140S04C> getC140S04C(String mainId, String uid) {
		ISearch search = c140s04cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), uid);
		return c140s04cDao.find(search);
	}

	@Override
	public Page<C140S09A> getC140S09APage(ISearch search) {
		return c140s09aDao.findPage(search);
	}

	@Override
	public Page<C140S09B> getC140S09BPage(ISearch search) {
		return c140s09bDao.findPage(search);
	}

	@Override
	public Page<C140S09C> getC140S09CPage(ISearch search) {
		return c140s09cDao.findPage(search);
	}

	@Override
	public Page<C140S09D> getC140S09DPage(ISearch search) {
		return c140s09dDao.findPage(search);
	}

	@Override
	public Page<C140S09E> getC140S09EPage(ISearch search) {
		return c140s09eDao.findPage(search);
	}

	@Override
	public Page<C140S09F> getC140S09FPage(ISearch search) {
		return c140s09fDao.findPage(search);
	}

	@Override
	public C140S09A getC140S09A(String oid) {
		return c140s09aDao.find(oid);
	}

	@Override
	public List<C140S09A> getC140S09A(String mainId, String uid) {
		ISearch search = c140s09aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), uid);
		return c140s09aDao.find(search);
	}

	@Override
	public C140S09B getC140S09B(String oid) {
		return c140s09bDao.find(oid);
	}

	@Override
	public C140S09C getC140S09C(String oid) {
		return c140s09cDao.find(oid);
	}

	@Override
	public C140S09D getC140S09D(String oid) {
		return c140s09dDao.find(oid);
	}

	@Override
	public C140S09E getC140S09E(String oid) {
		return c140s09eDao.find(oid);
	}

	@Override
	public C140S09F getC140S09F(String oid) {
		return c140s09fDao.find(oid);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void saveC140S09A(List c140s09as) {
		c140s09aDao.save(c140s09as);
	}

	@Override
	public void deleteC140S09A(String subOid) {
		C140S09A meta = c140s09aDao.find(subOid);
		c140s09aDao.delete(meta);
	}

	@Override
	public void deleteC140S09B(String subOid) {
		C140S09B meta = c140s09bDao.find(subOid);
		c140s09bDao.delete(meta);
	}

	@Override
	public void deleteC140S09C(String subOid) {
		C140S09C meta = c140s09cDao.find(subOid);
		c140s09cDao.delete(meta);
	}

	@Override
	public void deleteC140S09D(String subOid) {
		C140S09D meta = c140s09dDao.find(subOid);
		c140s09dDao.delete(meta);
	}

	@Override
	public void deleteC140S09B(String mainId, String pid) {
		ISearch search = c140s09bDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), pid);
		c140s09bDao.delete(c140s09bDao.find(search));
	}

	@Override
	public void deleteC140S09C(String mainId, String pid) {
		ISearch search = c140s09cDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.mainId.getName(), mainId);
		search.addSearchModeParameters(SearchMode.EQUALS,
				RelativeMeta_.pid.getName(), pid);
		c140s09cDao.delete(c140s09cDao.find(search));
	}

	@Override
	public String copyL120m01All(String mainId) throws NumberFormatException,
			CapMessageException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// Properties pop = MessageBundleScriptCreator
		// .getComponentResource(LMS1205M01Page.class);
		// 來源端資料
		Map<String, Object> origin = new HashMap<String, Object>();
		// 來源端資料(最新集團企業用)
		// Map<String, Object> origin2 = new HashMap<String, Object>();
		// 來源端資料(連保人用)
		// Map<String, Object> origin3 = new HashMap<String, Object>();
		// 來源端資料(產銷方式)
		// Map<String, Object> origin4 = new HashMap<String, Object>();
		// 來源端資料(中長期)
		// Map<String, Object> origin5 = new HashMap<String, Object>();
		// 授信簽報書用目的端資料(需修改建立者、修改時間)
		Map<String, Object> mainTarget = new HashMap<String, Object>();
		// 目的端資料(需修改建立者、修改時間)
		Map<String, Object> target = new HashMap<String, Object>();
		// 目的端資料(需修改建立者、修改時間)(最新集團企業用)
		Map<String, Object> target2 = new HashMap<String, Object>();
		// // 目的端資料(需修改建立者、修改時間)(連保人用)
		// Map<String, Object> target3 = new HashMap<String, Object>();

		// 複製後新案件簽報書文件編號
		String newMainId = IDGenerator.getUUID();

		/**
		 * "5. 已核准受理->條件變更/續約作業 (1)原案整張簽報書所有欄位除下列敘述欄位外要全數複製。
		 * (2)案號/簽案日期/授權等級/審核層級/文件建立者/最後異動者以上六個欄位要依最新的資料置換。
		 * (3)[文件異動記錄]/[主管批示]二個欄位不要複製。
		 * (4)簽報書->借款人基本資料/額度明細表->借保人資訊如果其徵信資料有最新的資料則需由徵信資料的內容置換
		 * 。(置換內容要包含借款人基本資料的所有內容)。 "
		 */
		// 複製借款人相關
		for (Class<?> clazz : LMSUtil.C120Class) {
			String originTable = clazz.getSimpleName();
			// copy
			String sql = SQLParse.getCopySQLbyMainId(mainId, newMainId, clazz,
					originTable);
			this.r6dbService.update(sql);
		}

		// // 複製後新案件額度明細表編號(額度明細表用)
		// String newMainId2 = IDGenerator.getUUID();
		// 文件亂碼
		String randomCode = IDGenerator.getRandomCode();

		// 依照單位種類決定辦理類別代號
		L120M01A model = this.findL120m01aByMainId(mainId);

		// 案件號碼流水號 -- 無條件給號(因為是複製)
		int caseSeq = Integer.parseInt(number.getNumberWithMax(L120M01A.class,
				user.getUnitNo(), null, 99999));
		// 案件號碼 -- 無條件給號(因為是複製)
		StringBuilder caseNum = new StringBuilder();
		IBranch ibranch = branch.getBranch(user.getUnitNo());
		Date today = new Date();
		caseNum.append(Util.toFullCharString(CapDate.formatDate(today, "yyyy")))
				.append(Util.trim(ibranch.getNameABBR()))
				.append(UtilConstants.Field.兆)
				.append(UtilConstants.Field.授字第)
				.append(Util.toFullCharString(Util.addZeroWithValue(
						Util.trim(caseSeq), 5))).append(UtilConstants.Field.號);

		// 文件狀態
		String docStatus = CreditDocStatusEnum.海外_編製中.getCode();

		origin.put("mainId", mainId);

		// origin2.put("mainId", mainId);
		// origin2.put("tab", "91");
		//
		// origin3.put("mainId", mainId);
		// origin3.put("tab", "41");
		//
		// origin4.put("mainId", mainId);
		// origin4.put("tab", "61");
		//
		// origin5.put("mainId", mainId);
		// origin5.put("tab", "A1");

		mainTarget.put("mainId", newMainId);
		mainTarget.put("RANDOMCODE", randomCode);
		mainTarget.put("CASESEQ", caseSeq);
		mainTarget.put("OWNBRID", user.getUnitNo());
		mainTarget.put("CASEBRID", user.getUnitNo());
		mainTarget.put("CASENO", caseNum.toString());
		mainTarget.put("CASEDATE", CapDate.getDate(
				CapDate.getCurrentDate(DATEYYYYMMDD), DATEYYYYMMDD));
		mainTarget.put("DOCSTATUS", docStatus);
		mainTarget.put("CREATOR", user.getUserId());
		mainTarget.put("UPDATER", user.getUserId());
		mainTarget.put("SENDFIRST", null);
		mainTarget.put("SENDFIRSTTIME", null);
		mainTarget.put("SENDLAST", null);
		mainTarget.put("SENDLASTTIME", null);
		mainTarget.put("CASEYEAR", CapDate.formatDate(today, "yyyy"));

		target.put("mainId", newMainId);
		target.put("CREATOR", user.getUserId());
		target.put("CREATETIME", "current timestamp");
		target.put("UPDATER", user.getUserId());
		target.put("UPDATETIME", "current timestamp");

		target2.put("mainId", newMainId);
		target2.put("pid", newMainId);
		target2.put("CREATOR", user.getUserId());
		target2.put("CREATETIME", "current timestamp");
		target2.put("UPDATER", user.getUserId());
		target2.put("UPDATETIME", "current timestamp");

		// // 案件簽報書授權檔
		// String modelA = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, L120A01A.class);
		// 授信簽報書主檔
		String modelB = SQLParse.getCopySQLbyMap(origin, mainTarget,
				Table.Schema.授信, L120M01A.class);
		// 簽報書敘述說明檔
		String modelC = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, L120M01D.class);

		// ================ 以下為徵信相關Model ==================

		// String modela = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, BRelated.class);
		String modelb = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140A01A.class);
		String modelc = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140JSON.class);
		// String modelc1 = SQLParse.getCopySQLbyMap(origin3, target,
		// Table.Schema.授信, C140JSON.class);
		// String modelc2 = SQLParse.getCopySQLbyMap(origin4, target,
		// Table.Schema.授信, C140JSON.class);
		// String modelc3 = SQLParse.getCopySQLbyMap(origin5, target,
		// Table.Schema.授信, C140JSON.class);
		// String modeld = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140M01A.class);
		// String modele = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140M04A.class);
		// String modelf = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140M04B.class);
		String modelg = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140M07A.class);
		String modelh = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140S04A.class);
		String modeli = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140S04B.class);
		String modelj = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140S04C.class);
		String modelk = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140S07A.class);
		String modell = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140S07B.class);
		// String modelm = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09A.class);
		// String modeln = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09B.class);
		// String modelo = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09C.class);
		// String modelp = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09D.class);
		// String modelq = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09E.class);
		// String modelr = SQLParse.getCopySQLbyMap(origin, target,
		// Table.Schema.授信, C140S09F.class);
		String models = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140SDSC.class);
		// String models1 = SQLParse.getCopySQLbyMap(origin3, target,
		// Table.Schema.授信, C140SDSC.class);
		// String models2 = SQLParse.getCopySQLbyMap(origin4, target,
		// Table.Schema.授信, C140SDSC.class);
		// String models3 = SQLParse.getCopySQLbyMap(origin5, target,
		// Table.Schema.授信, C140SDSC.class);
		String modelt = SQLParse.getCopySQLbyMap(origin, target,
				Table.Schema.授信, C140SFFF.class);

		// ================= 到此結束 ====================

		// this.r6dbService.update(modelA);
		this.r6dbService.update(modelB);
		this.r6dbService.update(modelC);
		// this.r6dbService.update(modelD);
		// this.r6dbService.update(modelE);

		// ================ 以下為徵信相關update ==================

		// this.r6dbService.update(modela); BRelated
		List<BRelated> listBrelated = brelatedDao.findByMainId(mainId);
		List<BRelated> brelateds = new ArrayList<BRelated>();
		if (!listBrelated.isEmpty()) {
			for (BRelated brelated : listBrelated) {
				BRelated newModel = new BRelated();
				try {
					DataParse.copy(brelated, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setMainId1(newMainId);
				newModel.setOid(null);
				brelateds.add(newModel);
			}
			brelatedDao.save(brelateds);
		}

		this.r6dbService.update(modelb);
		this.r6dbService.update(modelc);
		// this.r6dbService.update(modelc1);
		// this.r6dbService.update(modelc2);
		// this.r6dbService.update(modelc3);
		// this.r6dbService.update(modeld); C140M01A
		C140M01A c140m01a = this.getC140M01AByMainId(mainId);
		if (c140m01a != null) {
			C140M01A newModel = new C140M01A();
			try {
				DataParse.copy(c140m01a, newModel);
			} catch (CapException e) {
				logger.error("[DataParse.copy]", e);
			}
			newModel.setOid(null);
			newModel.setMainId(newMainId);
			newModel.setUid(newMainId);
			newModel.setToM5(null);
			newModel.setCreator(user.getUserId());
			newModel.setCreateTime(CapDate.getCurrentTimestamp());
			this.saveC140M01A(newModel);
		}
		// this.r6dbService.update(modele);
		// DataParse.copy(originEntity, targeEntity);
		List<C140M04A> listL140m04a = c140m04aDao.findByMainId(mainId);
		List<L120M01E> listL120m01e = l120m01eDao.findByMainId(mainId);
		if (!listL140m04a.isEmpty() && !listL120m01e.isEmpty()) {
			for (C140M04A c140m04a : listL140m04a) {
				for (L120M01E l120m01e : listL120m01e) {
					if (c140m04a.getOid().equals(l120m01e.getDocOid())) {
						C140M04A newC140m04a = new C140M04A();
						try {
							DataParse.copy(c140m04a, newC140m04a);
						} catch (CapException e) {
							logger.error("[DataParse.copy]", e);
						}
						newC140m04a.setOid(null);
						newC140m04a.setPid(newMainId);
						newC140m04a.setMainId(newMainId);
						c140m04aDao.save(newC140m04a);
						L120M01E newL120m01e = new L120M01E();
						try {
							DataParse.copy(l120m01e, newL120m01e);
						} catch (CapException e) {
							logger.error("[DataParse.copy]", e);
						}
						newL120m01e.setOid(null);
						newL120m01e.setMainId(newMainId);
						newL120m01e.setDocOid(newC140m04a.getOid());
						l120m01eDao.save(newL120m01e);
						break;
					}
				}
			}
		}

		// 非新增連保人複製
		List<L120M01E> listL120m01es = new ArrayList<L120M01E>();
		if (!listL120m01e.isEmpty()) {
			for (L120M01E l120m01e : listL120m01e) {
				if (!"4".equals(l120m01e.getDocType())) {
					L120M01E newL120m01e = new L120M01E();
					try {
						DataParse.copy(l120m01e, newL120m01e);
					} catch (CapException e) {
						logger.error("[DataParse.copy]", e);
					}
					newL120m01e.setOid(null);
					newL120m01e.setMainId(newMainId);
					listL120m01es.add(newL120m01e);
					// l120m01eDao.save(newL120m01e);
				}
			}
			l120m01eDao.save(listL120m01es);
		}
		// this.r6dbService.update(modelf); C140M04B
		List<C140M04B> listC140m04b = c140m04bDao.findByMainId(mainId);
		List<C140M04B> newC140m04bs = new ArrayList<C140M04B>();
		if (!listC140m04b.isEmpty()) {
			for (C140M04B c140m04b : listC140m04b) {
				C140M04B newModel = new C140M04B();
				try {
					DataParse.copy(c140m04b, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setMainId(newMainId);
				newC140m04bs.add(newModel);
			}
			c140m04bDao.save(newC140m04bs);
		}
		this.r6dbService.update(modelg);
		this.r6dbService.update(modelh);
		this.r6dbService.update(modeli);
		this.r6dbService.update(modelj);
		this.r6dbService.update(modelk);
		this.r6dbService.update(modell);
		// this.r6dbService.update(modelm); C140S09A
		List<C140S09A> listC140s09a = c140s09aDao.findByMainId(mainId);
		List<C140S09A> newC140s09as = new ArrayList<C140S09A>();
		if (!listC140s09a.isEmpty()) {
			for (C140S09A c140s09a : listC140s09a) {
				C140S09A newModel = new C140S09A();
				try {
					DataParse.copy(c140s09a, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09as.add(newModel);
			}
			c140s09aDao.save(newC140s09as);
		}
		// this.r6dbService.update(modeln); C140S09B
		List<C140S09B> listC140s09b = c140s09bDao.findByMainId(mainId);
		List<C140S09B> newC140s09bs = new ArrayList<C140S09B>();
		if (!listC140s09b.isEmpty()) {
			for (C140S09B c140s09b : listC140s09b) {
				C140S09B newModel = new C140S09B();
				try {
					DataParse.copy(c140s09b, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09bs.add(newModel);
			}
			c140s09bDao.save(newC140s09bs);
		}
		// this.r6dbService.update(modelo); C140S09C
		List<C140S09C> listC140s09c = c140s09cDao.findByMainId(mainId);
		List<C140S09C> newC140s09cs = new ArrayList<C140S09C>();
		if (!listC140s09c.isEmpty()) {
			C140S09C newModel = new C140S09C();
			for (C140S09C c140s09c : listC140s09c) {
				try {
					DataParse.copy(c140s09c, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09cs.add(newModel);
			}
			c140s09cDao.save(newC140s09cs);
		}
		// this.r6dbService.update(modelp); C140S09D
		List<C140S09D> listC140s09d = c140s09dDao.findByMainId(mainId);
		List<C140S09D> newC140s09ds = new ArrayList<C140S09D>();
		if (!listC140s09d.isEmpty()) {
			for (C140S09D c140s09d : listC140s09d) {
				C140S09D newModel = new C140S09D();
				try {
					DataParse.copy(c140s09d, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09ds.add(newModel);
			}
			c140s09dDao.save(newC140s09ds);
		}
		// this.r6dbService.update(modelq); C140S09E
		List<C140S09E> listC140s09e = c140s09eDao.findByMainId(mainId);
		List<C140S09E> newC140s09es = new ArrayList<C140S09E>();
		if (!listC140s09e.isEmpty()) {
			for (C140S09E c140s09e : listC140s09e) {
				C140S09E newModel = new C140S09E();
				try {
					DataParse.copy(c140s09e, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09es.add(newModel);
			}
			c140s09eDao.save(newC140s09es);
		}
		// this.r6dbService.update(modelr); C140S09F
		List<C140S09F> listC140s09f = c140s09fDao.findByMainId(mainId);
		List<C140S09F> newC140s09fs = new ArrayList<C140S09F>();
		if (!listC140s09f.isEmpty()) {
			for (C140S09F c140s09f : listC140s09f) {
				C140S09F newModel = new C140S09F();
				try {
					DataParse.copy(c140s09f, newModel);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newModel.setOid(null);
				newModel.setPid(newMainId);
				newModel.setMainId(newMainId);
				newC140s09fs.add(newModel);
			}
			c140s09fDao.save(newC140s09fs);
		}
		this.r6dbService.update(models);
		// this.r6dbService.update(models1);
		// this.r6dbService.update(models2);
		// this.r6dbService.update(models3);
		this.r6dbService.update(modelt);

		// ================= 到此結束 ====================

		// 砍掉主管批示內容
		L120M01D bossL120m01d = this.findL120m01dByUniqueKey(newMainId, "6");
		if (bossL120m01d != null) {
			l120m01dDao.delete(bossL120m01d);
		}
		// 營運中心說明及意見
		L120M01D area1L120m01d = this.findL120m01dByUniqueKey(newMainId, "7");
		if (area1L120m01d != null) {
			l120m01dDao.delete(area1L120m01d);
		}
		// 營運中心會議決議
		L120M01D area2L120m01d = this.findL120m01dByUniqueKey(newMainId, "8");
		if (area2L120m01d != null) {
			l120m01dDao.delete(area2L120m01d);
		}
		// 國金部/營運中心會簽意見
		L120M01D area3L120m01d = this.findL120m01dByUniqueKey(newMainId, "9");
		if (area3L120m01d != null) {
			l120m01dDao.delete(area3L120m01d);
		}
		// 授管處補充說明
		L120M01D head1L120m01d = this.findL120m01dByUniqueKey(newMainId, "A");
		if (head1L120m01d != null) {
			l120m01dDao.delete(head1L120m01d);
		}
		// 授管處審查意見
		L120M01D head2L120m01d = this.findL120m01dByUniqueKey(newMainId, "B");
		if (head2L120m01d != null) {
			l120m01dDao.delete(head2L120m01d);
		}
		// 授管處會簽意見(國內授信用)
		L120M01D head3L120m01d = this.findL120m01dByUniqueKey(newMainId, "C");
		if (head3L120m01d != null) {
			l120m01dDao.delete(head3L120m01d);
		}

		// 處理Ckeditor圖檔複製
		List<L120M01D> listL120m01d = l120m01dDao.findByMainId(newMainId);
		if (!listL120m01d.isEmpty()) {
			for (L120M01D l120m01d : listL120m01d) {
				try {
					l120m01d.setItemDscr(docFileService.copyCKEditorImageFile(
							Util.trim(l120m01d.getItemDscr()), newMainId));
				} catch (CapException e) {
					logger.error("[l120m01d.setItemDscr]", e);
				}
			}
			l120m01dDao.save(listL120m01d);
		}

		// 處理附加檔案複製
		List<DocFile> listFile = docFileService.findByIDAndPid(mainId, null);
		if (!listFile.isEmpty()) {
			for (DocFile file : listFile) {
				String fid = file.getOid();
				DocFile newFile = new DocFile();
				try {
					DataParse.copy(file, newFile);
				} catch (CapException e) {
					logger.error("[DataParse.copy]", e);
				}
				newFile.setMainId(newMainId);
				newFile.setCrYear(CapDate.getCurrentDate("yyyy"));
				newFile.setBranchId(user.getUnitNo());
				newFile.setOid(null);
				docFileService.copy(fid, newFile);
			}
		}
		String itemType = lmsService.checkL140M01AItemType(model);
		// 額度明細表與相關複製
		try {
			lmsService.copyCntrdocByCLS_with_newCaseNoCaseDate(mainId,
					itemType, newMainId, caseNum.toString(),
					CapDate.getDate(CapDate.getCurrentDate(DATEYYYYMMDD),
							DATEYYYYMMDD), true, false);
		} catch (CapException e) {
			logger.error("[lmsService.copyCntrdocByCLS]", e);
		}

		L120A01A l120a01a = new L120A01A();
		// 案件簽報書授權檔
		l120a01a.setOid(null);
		l120a01a.setPid("");
		l120a01a.setMainId(newMainId);
		l120a01a.setOwnUnit(user.getUnitNo());
		l120a01a.setAuthTime(CapDate.getCurrentTimestamp());
		l120a01a.setAuthType("1");
		l120a01a.setAuthUnit(user.getUnitNo());
		l120a01a.setOwner(user.getUserId());
		l120a01aDao.save(l120a01a);

		L120M01F l120m01f = new L120M01F();
		// 案件簽報書簽章欄
		l120m01f.setBranchType("1");
		l120m01f.setBranchId(user.getUnitNo());
		l120m01f.setStaffNo(user.getUserId());
		l120m01f.setStaffJob("L1");
		l120m01f.setCreator(user.getUserId());
		l120m01f.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01f.setMainId(newMainId);
		l120m01fDao.save(l120m01f);

		// 更新案件簽報書建立時間與更新時間
		L120M01A l120m01a = this.findL120m01aByMainId(newMainId);
		l120m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l120m01a.setUpdateTime(CapDate.getCurrentTimestamp());

		// 若為授權外則"是否加送會審單位"還原為預設值1
		String areaChk = ("2".equals(Util.trim(l120m01a.getDocKind()))) ? "1"
				: null;
		// 若為授權內則"授權等級"還原為預設值1
		String authLvl = ("1".equals(Util.trim(l120m01a.getDocKind()))) ? "1"
				: null;
		// 清空下面指定的欄位
		String colName[] = { "approver", "approveTime", "areaBrId",
				"areaDocstatus", "areaUpdater", "areaApprover", "areaApprTime",
				"caseLvl", "docRslt", "endDate", "meetingType", "rptTitle1",
				"rptTitle2", "areaAppraiser", "areaSendInfo", "rptTitleArea1",
				"signNo", "sendFirst", "sendFirstTime", "sendLast",
				"sendLastTime", "hqReceiveDate", "hqAppraiser", "hqMeetFlag",
				"returnBHDate", "backReason", "backUnit", "returnFromBH",
				"reEstFlag", "rptId" };
		JSONObject json = new JSONObject();
		for (String key : colName) {
			json.put(key, "");
		}
		json.put("areaChk", areaChk);
		json.put("authLvl", authLvl);
		DataParse.toBean(json, l120m01a);
		l120m01a.setNotesUp(null);
		l120m01a.setPackLoan(UtilConstants.DEFAULT.否);
		l120m01aDao.save(l120m01a);

		// if ("3".equals(Util.trim(l120m01a.getDocCode()))) {
		// // 若為陳復述案則刪除利害關係人(因為沒有頁籤)
		// List<L120S06A> list1 = this.findL120s06aByMainId(newMainId);
		// List<L120S06B> list2 = this.findL120s06bByMainId(newMainId);
		// if (!list1.isEmpty()) {
		// l120s06aDao.delete(list1);
		// }
		// if (!list2.isEmpty()) {
		// l120s06bDao.delete(list2);
		// }
		// }

		return newMainId;
	}

	@SuppressWarnings({ "rawtypes", "unused" })
	private BigDecimal getBraRate(String branchNo) {
		BigDecimal dEndRate = new BigDecimal("0");
		List rows = this.dwfxrthovsService.findDW_DWFXRTHOVS_RATE2(branchNo);
		Iterator it = rows.iterator();
		if (it.hasNext()) {
			Map dataMap = (Map) it.next();
			dEndRate = new BigDecimal(Util.trim(Util.nullToSpace(dataMap
					.get("AGNT_TWD_RT"))));
		} else {
			dEndRate = new BigDecimal("0"); // 無資料
		}
		return dEndRate;
	}

	/**
	 * 
	 * 檢核性質是否有包含該值
	 * 
	 * @param value
	 *            被 | 劃分的值
	 * @param checkData
	 *            要檢查的值
	 * @return
	 */
	@SuppressWarnings({ "unused" })
	private boolean isContainValue(String value, String checkData) {
		String[] prorertyArray = value.split(UtilConstants.Mark.SPILT_MARK);
		for (String data : prorertyArray) {
			if (checkData.equals(data)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public void delLms(String mainId) {

		Timestamp delTs = CapDate.getCurrentTimestamp();
		L120M01A model = this.findL120m01aByMainId(mainId);
		if (true) {
			String ELF447N_UNID = model.getMainId();
			if (misDBService.findELF447NByUnid(ELF447N_UNID) > 0) {
				// 若曾上傳ELF447N，把 delTs 設成半年後，若要救回還有機會
				int addMonth = 6;
				delTs = new Timestamp(CapDate.addMonth(delTs, addMonth)
						.getTime());
			}
		}

		if (true) {
			// 刪除授信簽報書主檔相關Model
			model.setDeletedTime(delTs);
			// J-112-0329 配合消金處，E-LOAN個金授信管理系統清整歡喜信貸案件明細表相關資料欄位導入DW
			model.setIs_uploaded_dw_hpcl("D");// 押D，每日批次上傳非已結案資料至DW時，可被撈出並刪除DW資料
			l120m01aDao.save(model);
		}
		if (true) {
			// 刪除額度明細表
			List<L120M01C> l120m01List = cls1151Service
					.findL120m01cListByMainId(mainId);
			for (L120M01C l120m01c : l120m01List) {
				cls1151Service.deleteL140m01(l120m01c.getRefMainId(), delTs);
			}
		}

		// // 刪除相關附加檔案
		// docFileService.deleteByMainId(mainId);
	}

	@Override
	public void deleteListL120s06ab(List<L120S06A> listL120s06a,
			List<L120S06B> listL120s06b) {
		this.deleteListL120s06a(listL120s06a);
		this.deleteListL120s06b(listL120s06b);
	}

	@Override
	public C140M01A getC140m01a(String oid, String mainId, String cesMainId1) {
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		r6dbService.C140M01A_copy(mainId);
		r6dbService.C140M01A_updateA1(oid, mainId);
		r6dbService.C140JSON_copy(mainId, cesMainId1, "A1");
		r6dbService.C140M04A_copy(mainId, cesMainId1);
		r6dbService.C140M04B_copy(mainId, cesMainId1);
		r6dbService.C140M07A_copy(mainId, cesMainId1, "A1");
		r6dbService.C140S07A_copy(mainId, cesMainId1);
		r6dbService.C140SDSC_copy(mainId, cesMainId1, "A1");
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "A1");
		// 處理附加檔案複製
		// r6dbService.BDOCFILE_copy(mainId, cesMainId1);
		// docFileService.copyFile("CES", cesMainId1, mainId);
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "A1");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("c140sfff.setFfbody", e);
				}
			}
			c140SFFFDao.save(list);
		}
		return this.getC140M01AByMainId(mainId);
	}

	@Override
	public C140M01A updateC140m01a(String oid, String mainId, String cesMainId1) {
		r6dbService.C140M01A_updateA1(oid, mainId);
		r6dbService.C140JSON_copy(mainId, cesMainId1, "A1");
		r6dbService.C140M04A_copy(mainId, cesMainId1);
		r6dbService.C140M04B_copy(mainId, cesMainId1);
		r6dbService.C140M07A_copy(mainId, cesMainId1, "A1");
		r6dbService.C140S07A_copy(mainId, cesMainId1);
		r6dbService.C140SDSC_copy(mainId, cesMainId1, "A1");
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "A1");
		List<DocFile> listFile = docFileService.findByIDAndPid(mainId, null);
		if (!listFile.isEmpty()) {
			for (DocFile file : listFile) {
				docFileService.clean(file.getFieldId());
			}
		}
		// 處理附加檔案複製
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "A1");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("[c140sfff.setFfbody]", e);
				}
			}
			c140SFFFDao.save(list);
		}
		// this.saveC140M01A(model);
		return this.getC140M01AByMainId(mainId);
		// return model;
	}

	@Override
	public C140M01A getC140m01a2(String oid, String mainId, String cesMainId1) {
		r6dbService.C140M01A_copy(mainId);
		r6dbService.C140M01A_update61(oid, mainId);
		r6dbService.C140JSON_copy(mainId, cesMainId1, "61");
		r6dbService.C140SDSC_copy(mainId, cesMainId1, "61");
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "61");
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "61");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("[c140sfff.setFfbody]", e);
				}
			}
			c140SFFFDao.save(list);
		}
		return this.getC140M01AByMainId(mainId);
	}

	@Override
	public C140M01A updateC140m01a2(String oid, String mainId, String cesMainId1) {
		r6dbService.C140JSON_delete(mainId, "61");
		r6dbService.C140SDSC_delete(mainId, "61");
		r6dbService.C140SFFF_delete(mainId, "61");
		// 進行徵信主表更新
		r6dbService.C140M01A_update61(oid, mainId);
		r6dbService.C140JSON_copy(mainId, cesMainId1, "61");
		r6dbService.C140SDSC_copy(mainId, cesMainId1, "61");
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "61");
		List<DocFile> listFile = docFileService.findByIDAndPid(mainId, null);
		if (!listFile.isEmpty()) {
			for (DocFile file : listFile) {
				docFileService.clean(file.getFieldId());
			}
		}
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "61");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("[c140sfff.setFfbody]", e);
				}
			}
			c140SFFFDao.save(list);
		}
		return this.getC140M01AByMainId(mainId);
	}

	@Override
	public C140M01A getC140m01a3(String oid, String mainId, String cesMainId1) {
		r6dbService.C140M01A_copy(mainId);
		r6dbService.C140M01A_update51(oid, mainId);
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "51");
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "51");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("[c140sfff.setFfbody]", e);
				}
			}
			c140SFFFDao.save(list);
		}
		return this.getC140M01AByMainId(mainId);
	}

	@Override
	public C140M01A updateC140m01a3(String oid, String mainId, String cesMainId1) {
		r6dbService.C140SFFF_delete(mainId, "51");
		// 進行徵信主表更新
		r6dbService.C140M01A_update51(oid, mainId);
		r6dbService.C140SFFF_copy(mainId, cesMainId1, "51");
		List<C140SFFF> list = this.getC140SFFF(mainId, mainId, "51");
		if (!list.isEmpty()) {
			for (C140SFFF c140sfff : list) {
				try {
					c140sfff.setFfbody(docFileService.copyCKEditorImageFile(
							Util.trim(c140sfff.getFfbody()), mainId, "ces"));
				} catch (CapException e) {
					logger.error("[c140sfff.setFfbody]", e);
				}
			}
			c140SFFFDao.save(list);
		}
		return this.getC140M01AByMainId(mainId);
	}

	@Override
	public void deleteC140m01a(String mainId) {
		r6dbService.C140JSON_delete(mainId, "A1");
		r6dbService.C140M04A_delete(mainId);
		r6dbService.C140M04B_delete(mainId);
		r6dbService.C140M07A_delete(mainId);
		r6dbService.C140S07A_delete(mainId);
		r6dbService.C140SDSC_delete(mainId, "A1");
		r6dbService.C140SFFF_delete(mainId, "A1");
	}

	@Override
	public void saveL730M01A(L120M01A l120m01a, L730A01A l730a01a,
			L730M01A l730m01a, List<L730S01A> listl730s01a) {
		// 儲存授信案件考核表授權檔及授信案件考核表主檔
		this.save(l120m01a, l730a01a, l730m01a);
		// 儲存授信案件考核表明細檔
		this.saveListL730s01a(listl730s01a);
	}

	@Override
	public void saveL120m01fAndModel(L120M01A model, List<L120M01F> models) {
		this.save(model);
		this.saveListL120m01f(models);
	}

	@Override
	public void saveRel(L120M01A model, List<L120M01E> list) {
		this.saveListL120m01e(list);
		this.save(model);
	}

	@Override
	public void saveRelAll(L120M01A l120m01a, L120M01F l120m01f) {
		this.save(l120m01a, l120m01f);
	}

	@Override
	public L121M01B findL121m01bByUniqueKey(String mainId, String itemType) {
		// 透過獨特Key取得資料
		return l121m01bDao.findByUniqueKey(mainId, itemType);
	}

	@Override
	public Page<Map<String, Object>> getMowTrust(String custId, String dupNo,
			ISearch search) {
		List<?> rows = this.misDBService.findMISMOWTBL1_selMow(custId, dupNo);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			// *評等（公佈）日期
			data.put("crdTYear",
					Util.trim(Util.nullToSpace(dataMap.get("MOWYMD"))));
			// *評等單位
			data.put("crdTBR", branch.getBranchName(Util.trim(Util
					.nullToSpace(dataMap.get("MOWBR")))));
			data.put("_crdTBR",
					Util.trim(Util.nullToSpace(dataMap.get("MOWBR"))));
			// *評等表類型
			data.put(
					"crdType",
					codeService.findByCodeTypeAndCodeValue(
							"CRDType",
							"M"
									+ Util.trim(Util.nullToSpace(dataMap
											.get("MOWTYPE")))).getCodeDesc());
			data.put("_crdType", Util.trim(dataMap.get("MOWTYPE")));
			// 評等財報年度
			data.put(
					"finYear",
					Util.isEmpty(Util.trim(Util.nullToSpace(dataMap
							.get("FINDATE")))) ? Util.trim(Util
							.nullToSpace(dataMap.get("MOWYMD"))) : Util
							.trim(Util.nullToSpace(dataMap.get("FINDATE"))));
			// *評等等級
			data.put("grade", Util.trim(Util.nullToSpace(dataMap.get("FR"))));
			// 評等展望
			data.put("prospect", Util.trim(Util.nullToSpace(dataMap.get("SA"))));
			// 保證企業統一編號
			data.put("prCustId",
					Util.trim(Util.nullToSpace(dataMap.get("PRCUSTID"))));
			// 保證企業重覆序號
			data.put("prDupNo",
					Util.trim(Util.nullToSpace(dataMap.get("PRDUPNO"))));
			// 保證企業名稱
			data.put("prCNAME",
					Util.trim(Util.nullToSpace(dataMap.get("PRCNAME"))));
			// 保證企業最終評等
			data.put("prFR", Util.trim(Util.nullToSpace(dataMap.get("PRFR"))));
			// 保證企業所引用之財報期間
			data.put("prFinDate",
					Util.trim(Util.nullToSpace(dataMap.get("PRFINDATE"))));
			// 保證企業之評等單位
			data.put("prMOWBr",
					Util.trim(Util.nullToSpace(dataMap.get("PRMOWBR"))));
			data.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			data.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void saveC140S09B(List c140s09bs) {
		c140s09bDao.save(c140s09bs);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void saveC140S09C(List c140s09cs) {
		c140s09cDao.save(c140s09cs);
	}

	@Override
	public void deleteL140M01aBis(List<L140M01A> list1, List<L120S03A> list2) {
		cls1151Service.delL140m01aList(list1);
		if (!list2.isEmpty()) {
			// 刪除資本適足率
			this.deleteListL120s03a(list2);
		}
	}

	@Override
	public void saveArea(L120M01A l120m01a, List<L120M01F> list) {
		// 設定不異動update
		l120m01aDao.save(l120m01a);
		this.saveListL120m01f(list);
	}

	@Override
	public void saveSea(L121M01B l121m01b, List<L120M01F> list) {
		if (l121m01b != null) {
			this.save(l121m01b);
		}
		if (!list.isEmpty()) {
			this.saveListL120m01f(list);
		}
	}

	@Override
	public void saveSeaAndDel(L120M01A l120m01a, L121M01B l120m01b,
			L120M01F addL120m01f) {
		if (l120m01a != null) {
			l120m01aDao.save(l120m01a);
		}
		if (l120m01b != null) {
			l121m01bDao.save(l120m01b);
		}
		if (addL120m01f != null) {
			l120m01fDao.save(addL120m01f);
		}
	}

	@Override
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, String next) throws Throwable {
		L120M01A meta = (L120M01A) model;
		if (UtilConstants.Casedoc.DocKind.授權內.equals(Util.trim(meta
				.getDocKind()))) {
			String authLvl = Util.trim(meta.getAuthLvl());
			// 營運中心授權內呈主管覆核，將審核層級設成B:營運中心營運長/副營運長權限
			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(authLvl)) {
				meta.setCaseLvl(UtilConstants.Casedoc.CaseLvl.營運中心營運長);
			} else if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authLvl)) {
				meta.setCaseLvl(UtilConstants.Casedoc.CaseLvl.其他);
			} else {
				meta.setCaseLvl(null);
			}
		}
		try {

			if (Util.isNotEmpty(meta.getEndDate())) {
				// 當最後批示日期不是空的要更新產品種類檔seq
				this.setL140S02ASeq_whenApprove(meta);
			}

			l120m01aDao.save((L120M01A) meta);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				// 判斷flow走哪個流程
				inst = flowService.start(LMSUtil.getFlowCode(branch
						.getBranch(meta.getCaseBrId())), mainOid, user
						.getUserId(), user.getUnitNo());
			}
			String nowDefname = inst.getDefinition().getName();

			logger.info("[flowAction]CLS1141ServiceImpl.flowAction nowDefname ====>"
					+ nowDefname);
			if (setResult) {
				logger.info("[flowAction]CLS1141ServiceImpl.flowAction flowName Key====>"
						+ flowNameService.getKey(next));
				// 當有setResult值才要塞
				inst.setAttribute("result", flowNameService.getKey(next));
			}
			inst.next();
			if (inst.getAttribute("flowCode") != null) {
				logger.info("[flowAction]CLS1141ServiceImpl.flowAction result next flowCode ====>"
						+ inst.getAttribute("result"));
				String flowCode = (String) inst.getAttribute("flowCode");
				inst = flowService
						.start(flowCode, mainOid, user.getUserId(),
								user.getUnitNo(), "result",
								inst.getAttribute("result"));
			}

		} catch (FlowException e) {
			Throwable t1 = e;
			while (t1.getCause() != null) {
				t1 = t1.getCause();
			}
			throw t1;
		}
	}

	/**
	 * 更新產品種類檔seq
	 * 
	 * @param l120m01a
	 */
	private void setL140S02ASeq_whenApprove(L120M01A l120m01a) {
		List<L140M01A> l140m01as = l140m01aDao
				.findL140m01aListByL120m01cMainId(l120m01a.getMainId());
		List<L140S02A> newl140s02as = new ArrayList<L140S02A>();
		HashMap<String, Integer> cntrnoMap = new HashMap<String, Integer>();
		// 先取出所有額度序號底下最大序號
		for (L140M01A l140m01a : l140m01as) {
			if (!FlowDocStatusEnum.已核准.getCode()
					.equals(l140m01a.getDocStatus())) {
				continue;
			}
			String mainId = l140m01a.getMainId();
			List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a
					.getMainId());
			int maxSeq = 0;
			if (cntrnoMap.containsKey(mainId)) {
				maxSeq = cntrnoMap.get(mainId);
			} else {
				cntrnoMap.put(mainId, maxSeq);
			}
			newl140s02as.addAll(l140s02as);
			for (L140S02A l140s02a : l140s02as) {
				Integer secNo = l140s02a.getSecNo();
				if (Util.isNotEmpty(secNo) && secNo != 0) {
					if (secNo > maxSeq) {
						maxSeq = secNo;
						cntrnoMap.put(mainId, maxSeq);
					}
				}
			}
		}

		for (L140S02A l140s02a : newl140s02as) {
			Integer secNo = l140s02a.getSecNo();
			String mainId = l140s02a.getMainId();
			if (Util.isEmpty(secNo) || secNo == 0) {
				l140s02a.setSecNo(cntrnoMap.get(mainId) + 1);
				cntrnoMap.put(mainId, cntrnoMap.get(mainId) + 1);
			}
		}

		for (L140M01A l140m01a : l140m01as) {
			Boolean tRun = true;
			if (UtilConstants.Cntrdoc.DataSrc.個金帳務
					.equals(l140m01a.getDataSrc())
					|| UtilConstants.Cntrdoc.DataSrc.帳務銷戶.equals(l140m01a
							.getDataSrc())) {
				tRun = false;
			}

			if (tRun) {
				String l140m01aCntrNo = Util.trim(l140m01a.getCntrNo());
				String l140m01aMainId = Util.trim(l140m01a.getMainId());
				String grpcntrno = "";

				L120M01G l120m01g = this.findModelByManId(L120M01G.class,
						l140m01aMainId);
				Timestamp tDate = null;

				if (l120m01g != null) {
					grpcntrno = Util.trim(l120m01g.getParentCntrNo());

					if (!"".equals(Util.trim(l140m01a.getCntrNo()))) {
						BigDecimal currentAmt = Util.parseBigDecimal(l140m01a
								.getCurrentApplyAmt());
						String currentCurr = Util.trim(l140m01a
								.getCurrentApplyCurr());
						tDate = l120m01a.getEndDate() == null ? null
								: new Timestamp(l120m01a.getEndDate().getTime());
						cls1151Service.setC900M01GData(l140m01aCntrNo,
								grpcntrno, l140m01a, "2", currentAmt,
								currentCurr, tDate);
					}
				}
			}
		}

		l140s02aDao.save(newl140s02as);

	}

	@Override
	public Map<String, String> findCmsCustName(String cmsMainId) {
		Map<String, String> map = new HashMap<String, String>();
		List<?> rows = this.r6dbService.C100M01_selCustname(cmsMainId);
		Iterator<?> it = rows.iterator();
		if (it.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			map.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			map.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			map.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			// map.put("cesFDate",
			// Util.trim(Util.nullToSpace(dataMap.get("COMPLETEDATE"))));
			// map.put("cesId", Util.trim(Util.nullToSpace(dataMap.get("SN"))));
			map.put("oid", Util.trim(Util.nullToSpace(dataMap.get("OID"))));
			map.put("docURL",
					Util.trim(Util.nullToSpace(dataMap.get("DOCURL"))));
			map.put("txCode",
					Util.trim(Util.nullToSpace(dataMap.get("TXCODE"))));
			map.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			map.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			map.put("uid", Util.trim(Util.nullToSpace(dataMap.get("UID"))));
			map.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			map.put("collKind", Util.trim(dataMap.get("COLLKIND")));
			map.put("collTyp1", Util.trim(dataMap.get("COLLTYP1")));
			map.put("collTyp2", Util.trim(dataMap.get("COLLTYP2")));
			map.put("pndFlag", Util.trim(dataMap.get("PNDFLAG")));
		}
		return map;
	}

	@Override
	public Page<Map<String, Object>> getCmsMainId(String branchId,
			ISearch search) {
		List<?> rows = this.r6dbService.findCms_selMainId(branchId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("estDate",
					Util.trim(Util.nullToSpace(dataMap.get("ESTDATE"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("collKind",
					Util.trim(Util.nullToSpace(dataMap.get("COLLKIND"))));
			data.put("megaAmt",
					Util.trim(Util.nullToSpace(dataMap.get("MEGAAMT"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			data.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			data.put("collTyp1",
					Util.trim(Util.nullToSpace(dataMap.get("COLLTYP1"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCmsMainId2(String mainId, ISearch search) {
		List<?> rows = this.r6dbService.findCms_selMainId2(mainId);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("estDate",
					Util.trim(Util.nullToSpace(dataMap.get("ESTDATE"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("collKind",
					Util.trim(Util.nullToSpace(dataMap.get("COLLKIND"))));
			data.put("megaAmt",
					Util.trim(Util.nullToSpace(dataMap.get("MEGAAMT"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			data.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			data.put("collTyp1",
					Util.trim(Util.nullToSpace(dataMap.get("COLLTYP1"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public Page<Map<String, Object>> getCmsMainId3(String collTyp1,
			ISearch search) {
		List<?> rows = this.r6dbService.findCms_selMainId3(collTyp1);
		Iterator<?> it = rows.iterator();
		List<Map<String, Object>> beanList = new ArrayList<Map<String, Object>>();
		int count = 0;
		while (it.hasNext()) {
			count++;
			Map<?, ?> dataMap = (Map<?, ?>) it.next();
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("createTime",
					Util.trim(Util.nullToSpace(dataMap.get("CREATETIME"))));
			data.put("estDate",
					Util.trim(Util.nullToSpace(dataMap.get("ESTDATE"))));
			data.put("approveTime",
					Util.trim(Util.nullToSpace(dataMap.get("APPROVETIME"))));
			data.put("collKind",
					Util.trim(Util.nullToSpace(dataMap.get("COLLKIND"))));
			data.put("megaAmt",
					Util.trim(Util.nullToSpace(dataMap.get("MEGAAMT"))));
			data.put("docStatus",
					Util.trim(Util.nullToSpace(dataMap.get("DOCSTATUS"))));
			data.put("custName",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTNAME"))));
			data.put("custId",
					Util.trim(Util.nullToSpace(dataMap.get("CUSTID"))));
			data.put("dupNo", Util.trim(Util.nullToSpace(dataMap.get("DUPNO"))));
			data.put("collTyp1",
					Util.trim(Util.nullToSpace(dataMap.get("COLLTYP1"))));
			data.put("mainId",
					Util.trim(Util.nullToSpace(dataMap.get("MAINID"))));
			beanList.add(data);
		}

		int start = search.getFirstResult();
		int pagNumber = search.getMaxResults();
		int end = start + pagNumber > rows.size() ? start
				+ (rows.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		return new Page<Map<String, Object>>(beanListnew, rows.size(),
				search.getMaxResults(), search.getFirstResult());
	}

	@Override
	public void deleteC140S09A(List<C140S09A> c140s09as) {
		c140s09aDao.delete(c140s09as);

	}

	@Override
	public C120M01A findc120m01aByUniqueKey(String mainId, String custId,
			String dupNo) {

		return c120m01aDao.findByUniqueKey(mainId, null, custId, dupNo);

	}

	@Override
	public C120S01E findC120S01EByUniqueKey(String mainId, String custId,
			String dupNo) {
		return c120s01eDao.findByUniqueKey(mainId, custId, dupNo);
	}

	@Override
	public void saveC120M01As(List<C120M01A> c120m01as, GenericBean... bean) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (C120M01A c120m01a : c120m01as) {
			c120m01a.setUpdater(user.getUserId());
			c120m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		c120m01aDao.save(c120m01as);
		if (bean != null) {
			this.save(bean);
		}

	}

	@Override
	public List<L120M01E> findL120M01EByByMainIdAndDocType(String mainId,
			String docType) {
		return l120m01eDao.findByMainIdAndDocType(mainId, docType);
	}

	@Override
	public C120M01A findC120M01AByMainIdAndKeyMan(String mainId) {
		return c120m01aDao.findByMainIdAndKeyMan(mainId,
				UtilConstants.DEFAULT.是);
	}

	@Override
	public List<C101M01A> findC101M01AByMaindIds(String[] mainIds) {
		return c101m01aDao.findByMainIds(mainIds);
	}

	@Override
	public List<L140M01A> findl140m01aByl120m01cMainid(String l120Mainid) {
		return l140m01aDao.findL140m01aListByL120m01cMainId(l120Mainid);
	}

	@Override
	public List<L140S02A> findl140s02aByl140m01aMainid(String l140Mainid) {
		return l140s02aDao.findByMainId(l140Mainid);
	}

	@Override
	public L120M01F findL120m01fByMainIdAndKey(String mainId,
			String branchType, String branchId, String staffJob) {
		return l120m01fDao.findByMainIdAndKey(mainId, branchType, branchId,
				staffJob);
	}

	@Override
	public List<? extends GenericBean> findModelListByMainIdCustIdDupNo(
			@SuppressWarnings("rawtypes") Class clazz, String mainId,
			String custId, String dupNo) {
		if (clazz == C120S01P.class) {
			return c120s01pDao.findByIndex01(mainId, custId, dupNo, null);
		}
		return null;
	}

	@Override
	public void deleteL140M01R(L140M01R l140m01r) {
		l140m01rDao.delete(l140m01r);
	}

	@Override
	public void saveL140M01R(L140M01R l140m01r) {
		l140m01rDao.save(l140m01r);
	}

	@Override
	public L140M01R getL140M01R(String oid) {
		return l140m01rDao.find(oid);
	}

	@Override
	public List<L140M01R> findl140m01rByMainid(String mainId) {
		return l140m01rDao.findByMainId(mainId);
	}

	@Override
	public String get_CLS_l140m01a_subject(L140M01A l140m01a) {
		return get_CLS_l140m01a_subject(l140m01a, "\r");
	}

	@Override
	public String get_CLS_l140m01a_subject(L140M01A l140m01a, String rep) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		List<L140S02A> l140s02aList = l140s02aDao.findByMainId(l140m01a
				.getMainId());
		LinkedList<String> prodKinds = new LinkedList<String>();
		String key = "";

		for (L140S02A l140s02a : l140s02aList) {
			String prodkind = Util.trim(l140s02a.getProdKind());
			String subjCode = Util.trim(l140s02a.getSubjCode());
			if (Util.isNotEmpty(prodkind) && Util.isNotEmpty(subjCode)) {
				// 產品 +^+ 授信科目
				key = StrUtils.concat(prodkind, "^", subjCode, "^", l140s02a
						.getSeq().toString(), "^", Util.addSpaceWithValue(
						Util.trim(l140s02a.getChkUsed()), 1));
				prodKinds.add(key);
			}
		}

		StringBuffer prodStr = new StringBuffer();
		String busCode = "";
		if (l140m01a != null) {
			busCode = clsService.get0024_busCode(l140m01a.getCustId(),
					l140m01a.getDupNo());
		}
		// 印 CLS1151R01 額度明細表,會將消金產品資訊,印在 授信科目(企金無產品)
		HashMap<String, String> prodmap = prodService.getProdKindName(busCode);
		HashMap<String, String> subCodeMap = prodService.getSubCode();

		Map<Integer, String> seq_printStrMap = LMSUtil
				.getPrintStrForProdSeqNo(l140s02aList
						.toArray(new L140S02A[l140s02aList.size()]));
		for (String code : prodKinds) {
			String prodkind = code.split("\\^")[0];
			String subjCode = code.split("\\^")[1];
			int seq = Util.parseInt(code.split("\\^")[2]);
			String checkUsed = code.split("\\^")[3];
			prodStr.append(prodStr.length() > 0 ? rep : "");
			if (l140s02aList.size() > 1 && seq_printStrMap.containsKey(seq)) {
				prodStr.append(seq_printStrMap.get(seq));
				prodStr.append(". ");
			}
			String prodKindStr = Util.trim(prodmap.get(prodkind));
			/**
			 * <pre>
			 * 搭配長擔貸款/長擔額度序號-
			 * 只有在產品種類為02/04 AND科目為202/204 AND動用方式[支票/金融卡]時才會顯示出來。
			 *  符合條件且選擇[是]時
			 *  產品種類為02需將本筆短擔的科目中文置換為"歡喜理財家－行家理財"；
			 * 	如改為否時需再置換為"行家理財－短期"
			 * </pre>
			 */
			if (CrsUtil.is_02(prodkind) || CrsUtil.is_68(prodkind)) {
				if ("Y".equals(checkUsed)) {
					// L140S02A.prodKind02=歡喜理財家-行家理財
					prodKindStr = prop.getProperty("L140S02A.prodKind02");
				}
			}
			if (ProdKindEnum.房貸專案_B案_100億_31.getCode().equals(prodkind)
					&& ClsUtil.is_403_603(subjCode)) {
				// CLS1151R01, CLS1151R02 額度明細表
				prodKindStr += prop.getProperty("L140S02A.prodKind31_403603");
			}
			prodStr.append(prodKindStr);
			prodStr.append("(");
			prodStr.append(Util.trim(subCodeMap.get(subjCode)));
			prodStr.append(")");
		}
		// String l140m01jstr = Util.trim(l140m01a.getL140m01jStr());
		// if (Util.isNotEmpty(l140m01jstr)) {
		// prodStr.append("\r");
		// prodStr.append(l140m01jstr);
		// }
		return prodStr.toString();
	}

	@Override
	public String get_CLS_l140m01a_guarantor(L140M01A l140m01a) {
		StringBuffer guarantorStr = new StringBuffer();
		// 借保人種類
		Map<String, String> custPosMap = codeService
				.findByCodeType("L140S01A_custPos");
		List<L140S01A> l140s01as = l140s01aDao.findByMainId(l140m01a
				.getMainId());
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);
		String proPerty = Util.trim(l140m01a.getProPerty());
		for (L140S01A l140s01a : l140s01as) {
			guarantorStr.append(guarantorStr.length() > 0 ? "、" : "");
			guarantorStr.append(l140s01a.getCustName());
			guarantorStr.append("(");
			guarantorStr
					.append(Util.trim(custPosMap.get(l140s01a.getCustPos())));
			guarantorStr.append(")");
		}

		Set<L140M01B> l140m01bs = l140m01a.getL140m01b();
		String itemDscrD = "";

		if (l140m01bs != null) {
			for (L140M01B l140m01b : l140m01bs) {
				if (UtilConstants.Cntrdoc.l140m01bItemType.國內個金團貸借保人說明
						.equals(l140m01b.getItemType())) {
					itemDscrD = Util.trim(l140m01b.getItemDscr());
				}
			}
		}

		String guarantorResult = "";
		if (Util.isEmpty(itemDscrD) && guarantorStr.length() == 0) {
			// COMMON.CON2=無
			guarantorResult = prop.getProperty("COMMON.CON2");
			// 2013/07/18,Rex,當性質為不變或取消，且連保人欄位為空的則，印出舊案資料
			if (UtilConstants.Cntrdoc.Property.取消.equals(proPerty)
					|| UtilConstants.Cntrdoc.Property.不變.equals(proPerty)) {
				String guarantor = Util.trim(l140m01a.getGuarantor());
				if (Util.isNotEmpty(guarantor)) {
					guarantorResult = guarantor;
				}
			}
		} else {
			guarantorResult = guarantorStr
					.append(guarantorStr.length() > 0 ? "\r" : "")
					.append(itemDscrD).toString();
		}

		return guarantorResult;
	}

	@Override
	public String get_CLS_l120m01a_purpose(L120M01A l120m01a) {
		String[] purposes = Util.trim(l120m01a.getPurpose()).split(
				UtilConstants.Mark.SPILT_MARK);
		// 資金用途
		ArrayList<String> r = new ArrayList<String>();

		Map<String, String> cls1141_purpose = codeService
				.findByCodeType("cls1141_purpose");

		for (String purpose : purposes) {
			if (Util.isEmpty(purpose)) {
				continue;
			}
			// ---
			r.add(Util.trim(cls1141_purpose.get(purpose)));
		}

		return StringUtils.join(r, UtilConstants.Mark.MARKDAN);
	}

	private String _hide_custId(String id) {
		return StringUtils.overlay(id, "***", 4, 7);
	}

	private String _hide_custName(String custName) {
		int name_length = custName.length();
		String hide_char = "＊";
		if (name_length <= 1) {
			return custName;
		} else if (name_length == 2) {
			return StringUtils.overlay(custName, hide_char, 1, 2);
		} else {
			return StringUtils.overlay(custName,
					StringUtils.repeat(hide_char, name_length - 2), 1,
					name_length - 1);
		}
	}

	private BigDecimal _build_ltv_from_l140m01o(String l140m01a_mainId) {
		BigDecimal result = null;
		for (L140M01O l140m01o : clsService.findL140M01O(l140m01a_mainId)) {
			BigDecimal val = l140m01o.getPayPercent();
			if (val == null) {
				continue;
			}

			if (result == null) {
				result = val;
			} else {
				if (result.compareTo(val) < 0) {
					result = val;
				}
			}
		}
		return result;
	}

	private Integer _build_nowExtendPeriod_from_l140s02e(String l140m01a_mainId) {
		Integer result = null;
		for (L140S02E l140s02e : clsService.findL140S02E(l140m01a_mainId)) {
			Integer val = null;
			if (Util.equals("Y", l140s02e.getNowExtend())) {
				// 若是中間變更, 36~48 應顯示 12
				val = (l140s02e.getNowEnd() - l140s02e.getNowFrom() + 1);
			} else {
				continue;
			}

			if (result == null) {
				result = val;
			} else {
				if (result < val) {
					result = val;
				}
			}
		}
		return result;
	}

	private L140S02C _build_l140s02c_preDscrWithLatestBaseRate(String custId,
			String dupNo, L140S02A l140s02a,
			LinkedHashMap<String, String> rateBaseMap,
			LinkedHashMap<String, String> rateUserType) {
		L140S02C case_l140s02c = clsService.findL140S02C(l140s02a.getMainId(),
				l140s02a.getSeq());

		L140S02C l140s02c = new L140S02C();
		l140s02c.setSubmitRate(case_l140s02c.getSubmitRate());
		l140s02c.setPayNum(case_l140s02c.getPayNum());
		l140s02c.setPreDscr(case_l140s02c.getPreDscr());
		l140s02c.setTaxRate(case_l140s02c.getTaxRate());
		l140s02c.setIntWay(case_l140s02c.getIntWay());
		l140s02c.setRIntWay(case_l140s02c.getRIntWay());
		l140s02c.setDecFlag(case_l140s02c.getDecFlag());
		l140s02c.setDesc(case_l140s02c.getDesc());
		l140s02c.setIsInputDesc(case_l140s02c.getIsInputDesc());

		try {
			if (Util.isNotEmpty(Util.trim(l140s02a.getSrcLoanNo()))
					&& Util.equals(UtilConstants.L140S02CIntway.期付金,
							l140s02c.getIntWay())) {
				List<LNF130> lnf130_list_raw = misLNF130Service.findByKey(
						custId, dupNo, l140s02a.getCntrNo(),
						l140s02a.getSrcLoanNo());
				List<LNF130> lnf130_list = new ArrayList<LNF130>();

				List<L140S02D> case_l140s02d_list = clsService
						.findL140S02D_orderByPhase(l140s02a.getMainId(),
								l140s02a.getSeq(), "Y");
				if (case_l140s02d_list.size() > 0) {
					int s02d_bgnNum = case_l140s02d_list.get(0).getBgnNum();
					int passedLastNum = s02d_bgnNum - 1;

					int lnf130_list_raw_size = lnf130_list_raw.size();

					for (int i = 0; i < lnf130_list_raw_size; i++) {
						LNF130 lnf130 = lnf130_list_raw.get(i);

						int lnf130_beg_term = lnf130.getLnf130_beg_term();
						int lnf130_end_term = lnf130.getLnf130_end_term();

						if (passedLastNum >= lnf130_end_term) {
							lnf130_list.add(lnf130);
						} else if (passedLastNum >= lnf130_beg_term
								&& passedLastNum <= lnf130_end_term) {
							lnf130.setLnf130_end_term(passedLastNum);
							lnf130_list.add(lnf130);
							break;
						}
					}

					if (lnf130_list.size() > 0) {
						List<String> new_predscr_list = new ArrayList<String>();
						for (LNF130 lnf130 : lnf130_list) {
							L140S02D l140s02d = new L140S02D();
							l140s02d.setIsUseBox(UtilConstants.DEFAULT.是);// 若缺這行，在ClsUtil.toWordByL140S02D(?)組字會回傳空白
							l140s02d.setBgnNum(lnf130.getLnf130_beg_term());
							l140s02d.setEndNum(lnf130.getLnf130_end_term());
							String rateType = Util.trim(lnf130
									.getLnf130_int_code());
							l140s02d.setRateType(rateType);

							BigDecimal pmRate = Util.parseBigDecimal(
									lnf130.getLnf130_int_sprd()).setScale(4);
							String pmFlag = "";
							if ("01".equals(rateType)) {
								l140s02d.setRateUser(pmRate);
							} else {
								// P.加碼, M.減碼
								if (BigDecimal.ZERO.compareTo(pmRate) == 1) {
									pmFlag = "M";
								} else if (BigDecimal.ZERO.compareTo(pmRate) == -1) {
									pmFlag = "P";
								}
								l140s02d.setPmFlag(pmFlag);
								l140s02d.setPmRate(pmRate.abs());

								BigDecimal baseRate = cls1151Service
										.get_l140s02d_baseRate(l140s02d
												.getRateType());
								if (baseRate == null) {
									// 可能該利率代碼已停用
									throw new CapException("LR_CODE="
											+ l140s02d.getRateType()
											+ " not found", getClass());
								}
								l140s02d.setBaseRate(baseRate);
								l140s02d.setNowRate(ClsUtil
										.calc_L140S02D_nowRate(l140s02d));

							}
							l140s02d.setRateFlag(lnf130.getLnf130_int_type());
							l140s02d.setRateChgWay("1");
							String rateChgWay2 = lnf130.getLnf130_intchg_type();
							if ("Y".equals(rateChgWay2)) {
								rateChgWay2 = "12";
							} else if ("M".equals(rateChgWay2)) {
								// 2013/09/09,Rex,修改變動期間取得方式
								// rateChgWay2 = "1";
								rateChgWay2 = lnf130.getLnf130_intchg_cycl();
							} else if ("F".equals(rateChgWay2)) {
								rateChgWay2 = "6";
							}
							l140s02d.setRateChgWay2(rateChgWay2);
							l140s02c.setDecFlag(lnf130.getLnf130_dec_flag());
							l140s02d.setRateUserType(lnf130
									.getLnf130_int_01_ptr());

							String new_baseDesc = cls1151Service
									.get_l140s02d_baseDesc(l140s02d,
											rateBaseMap, l140s02c, rateUserType);
							if (Util.isEmpty(Util.trim(new_baseDesc))) {
								continue;
							}
							new_predscr_list.add(new_baseDesc);
						}
						if (new_predscr_list.size() > 0) {
							String new_str = StringUtils.join(new_predscr_list,
									"；<br/>");

							l140s02c.setPreDscr(new_str);
						}
					}
				}
			}

		} catch (Exception e) {
			logger.error("_build_l140s02c_preDscrWithLatestBaseRate:"
					+ StrUtils.getStackTrace(e));
		}
		return l140s02c;
	}

	private String _build_l140s02a_rateWithLatestBaseRate(L140S02A l140s02a,
			L140S02C regen_l140s02c, Properties prop_CLS1151S01Page,
			LinkedHashMap<String, String> rateBaseMap,
			LinkedHashMap<String, String> rateUserType) {
		String oldDesc = Util.trim(l140s02a.getRateDesc());
		String rateWithLatestBaseRate = oldDesc;
		try {

			List<L140S02D> case_l140s02d_list = clsService
					.findL140S02D_orderByPhase(l140s02a.getMainId(),
							l140s02a.getSeq(), "Y");

			// ~~~
			List<L140S02D> l140s02dList = new ArrayList<L140S02D>();
			for (L140S02D case_l140s02d : case_l140s02d_list) {
				L140S02D l140s02d = new L140S02D();
				// ====== 先 copy 原資料，再覆蓋 baseRate, nowRate
				// l140s02d.setSeq(case_l140s02d.getSeq()); 欄位Seq 在DB是 Not Null
				l140s02d.setPhase(case_l140s02d.getPhase());
				l140s02d.setIsUseBox(case_l140s02d.getIsUseBox());
				l140s02d.setBgnNum(case_l140s02d.getBgnNum());
				l140s02d.setEndNum(case_l140s02d.getEndNum());
				l140s02d.setRateType(case_l140s02d.getRateType());
				l140s02d.setRateUser(case_l140s02d.getRateUser());
				l140s02d.setRateUserType(case_l140s02d.getRateUserType());
				l140s02d.setBaseRate(case_l140s02d.getBaseRate());
				l140s02d.setPmFlag(case_l140s02d.getPmFlag());
				l140s02d.setPmRate(case_l140s02d.getPmRate());
				l140s02d.setNowRate(case_l140s02d.getNowRate());
				l140s02d.setRateFlag(case_l140s02d.getRateFlag());
				l140s02d.setRateChgWay(case_l140s02d.getRateChgWay());
				l140s02d.setRateChgWay2(case_l140s02d.getRateChgWay2());

				// 當 RateType=01 固定利率時, baseRate==null, nowRate==null => 這種 case
				// 不需去 update NowRate
				if (l140s02d.getBaseRate() != null
						&& l140s02d.getNowRate() != null) {
					// 替換為 latest rate
					BigDecimal baseRate = cls1151Service
							.get_l140s02d_baseRate(l140s02d.getRateType()); // l140s02d.getBaseRate();
					if (baseRate == null) {
						// 可能該利率代碼已停用
						throw new CapException("LR_CODE="
								+ l140s02d.getRateType() + " not found",
								getClass());
					}
					l140s02d.setBaseRate(baseRate);
					l140s02d.setNowRate(ClsUtil.calc_L140S02D_nowRate(l140s02d));
				}

				// String baseDesc = case_l140s02d.getBaseDesc();
				String new_baseDesc = cls1151Service.get_l140s02d_baseDesc(
						l140s02d, rateBaseMap, regen_l140s02c, rateUserType);

				l140s02d.setBaseDesc(new_baseDesc);
				// ===========
				l140s02dList.add(l140s02d);
			}
			rateWithLatestBaseRate = Util.trim(cls1151Service
					.get_l140s02a_rateDesc(regen_l140s02c, l140s02dList,
							prop_CLS1151S01Page));

		} catch (Exception e) {
			logger.error("_build_l140s02a_rateWithLatestBaseRate" + "{cntrNo:"
					+ l140s02a.getCntrNo() + ", s02a_mainId:"
					+ l140s02a.getMainId() + ", s02a_seq:" + l140s02a.getSeq()
					+ "}" + StrUtils.getStackTrace(e));
		}
		return rateWithLatestBaseRate;
	}

	private String _get_CLS_l140s02a_rateWithLatestBaseRate(
			String l140m01a_mainId, String l140m01a_custId,
			String l140m01a_dupNo, String currentApplyCurr, String mark,
			Properties prop_CLS1151S01Page,
			LinkedHashMap<String, String> rateBaseMap,
			LinkedHashMap<String, String> rateUserType) {
		String rates = "";
		List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a_mainId);
		if (l140s02as.size() > 1) {
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(l140s02as
							.toArray(new L140S02A[l140s02as.size()]));
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}

				L140S02C regen_l140s02c = _build_l140s02c_preDscrWithLatestBaseRate(
						l140m01a_custId, l140m01a_dupNo, l140s02a, rateBaseMap,
						rateUserType);

				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());
				String preDscr = Util.trim(regen_l140s02c.getPreDscr());
				preDscr = Util.isNotEmpty(preDscr) ? preDscr + mark : "";

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {
					if (ProdKindEnum.企金科目.getCode().equals(
							l140s02a.getProdKind())) {
						rates = rates + seqStr + ". " + preDscr
								+ Util.trim(l140s02a.getFreeRateDesc()) + mark;
					} else {
						rates = rates
								+ seqStr
								+ ". "
								+ preDscr
								+ _build_l140s02a_rateWithLatestBaseRate(
										l140s02a, regen_l140s02c,
										prop_CLS1151S01Page, rateBaseMap,
										rateUserType) + mark;
					}
				}
			}
		} else {
			if (!l140s02as.isEmpty()) {
				L140S02A l140s02a = l140s02as.get(0);
				L140S02C regen_l140s02c = _build_l140s02c_preDscrWithLatestBaseRate(
						l140m01a_custId, l140m01a_dupNo, l140s02a, rateBaseMap,
						rateUserType);

				if (ProdKindEnum.企金科目.getCode().equals(l140s02a.getProdKind())) {
					rates = Util.trim(l140s02a.getFreeRateDesc());
				} else {
					rates = _build_l140s02a_rateWithLatestBaseRate(l140s02a,
							regen_l140s02c, prop_CLS1151S01Page, rateBaseMap,
							rateUserType);
				}
				String preDscr = Util.trim(regen_l140s02c.getPreDscr());
				preDscr = !Util.isEmpty(preDscr) ? preDscr + mark : "";
				rates = preDscr + rates;
			}
		}
		return rates;
	}

	@Override
	public String get_CLS_l140s02a_rateWithLatestBaseRate(
			String l140m01a_mainId, String mark) {
		L140M01A l140m01a = clsService.findL140M01A_mainId(l140m01a_mainId);

		Properties prop_CLS1151S01Page = MessageBundleScriptCreator
				.getComponentResource(CLS1151S01Page.class);
		String currentApplyCurr = l140m01a.getCurrentApplyCurr();

		Map<String, LinkedHashMap<String, String>> curr_rateBaseMap = new HashMap<String, LinkedHashMap<String, String>>();
		Map<String, LinkedHashMap<String, String>> curr_rateUserType = new HashMap<String, LinkedHashMap<String, String>>();

		if (!curr_rateBaseMap.containsKey(currentApplyCurr)) {
			// 利率基礎
			HashMap<String, LinkedHashMap<String, String>> map = misMislnratService
					.findBaseRateByCurrs(new String[] { currentApplyCurr });
			for (String key : map.keySet()) {
				// L140S02D.rateUser=自訂利率
				map.get(key).put("01",
						prop_CLS1151S01Page.getProperty("L140S02D.rateUser"));
			}
			// 利率
			LinkedHashMap<String, String> rateBaseMap = map
					.get(currentApplyCurr);

			// ========================================================
			// 自訂利率選單
			LinkedHashMap<String, String> rateUserType = lnlnf070Service
					.getPrRate(currentApplyCurr);

			curr_rateBaseMap.put(currentApplyCurr, rateBaseMap);
			curr_rateUserType.put(currentApplyCurr, rateUserType);
		}
		return _get_CLS_l140s02a_rateWithLatestBaseRate(l140m01a.getMainId(),
				l140m01a.getCustId(), l140m01a.getDupNo(), currentApplyCurr,
				"\r", prop_CLS1151S01Page,
				curr_rateBaseMap.get(currentApplyCurr),
				curr_rateUserType.get(currentApplyCurr)).replaceAll(
				"(<br>|<br/>|<BR>|<BR/>)", "\r");
	}

	@Override
	public String get_CLS_l140s02a_rate(String l140m01a_mainId, String mark) {
		String rates = "";
		List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a_mainId);
		if (l140s02as.size() > 1) {
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(l140s02as
							.toArray(new L140S02A[l140s02as.size()]));
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}

				L140S02C l140s02c = l140s02a.getL140S02C();
				l140s02c = Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c;
				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());
				String preDscr = Util.trim(l140s02c.getPreDscr());
				preDscr = Util.isNotEmpty(preDscr) ? preDscr + mark : "";

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {
					if (ProdKindEnum.企金科目.getCode().equals(
							l140s02a.getProdKind())) {
						rates = rates + seqStr + ". " + preDscr
								+ Util.trim(l140s02a.getFreeRateDesc()) + mark;
					} else {
						rates = rates + seqStr + ". " + preDscr
								+ Util.trim(l140s02a.getRateDesc()) + mark;
					}
				}
			}
		} else {
			if (!l140s02as.isEmpty()) {
				L140S02A l140s02a = l140s02as.get(0);
				L140S02C l140s02c = l140s02a.getL140S02C();
				l140s02c = Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c;

				if (ProdKindEnum.企金科目.getCode().equals(l140s02a.getProdKind())) {
					rates = Util.trim(l140s02a.getFreeRateDesc());
				} else {
					rates = Util.trim(l140s02a.getRateDesc());
				}
				String preDscr = Util.trim(l140s02c.getPreDscr());
				preDscr = !Util.isEmpty(preDscr) ? preDscr + mark : "";
				rates = preDscr + rates;
			}
		}
		return rates;
	}

	@Override
	public String get_CLS_l140s02a_lnother(String l140m01a_mainId,
			boolean isParentCase) {
		String payDeadline = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);
		List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a_mainId);
		if (l140s02as.size() > 1) {
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(l140s02as
							.toArray(new L140S02A[l140s02as.size()]));
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}
				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {
					String baseLnStr = "";

					if (isParentCase) {
						baseLnStr = other;
					} else {
						if (Util.isNotEmpty(other)) {
							other = "(" + other + ")";
						}
						// COMMON.year=年
						// COMMON.month=月
						baseLnStr = seqStr + ". " + lnYear
								+ prop.getProperty("COMMON.year") + lnMonth
								+ prop.getProperty("COMMON.month") + other
								+ " ";
					}
					payDeadline = payDeadline + baseLnStr;
				}
			}
		} else {
			if (!l140s02as.isEmpty()) {
				L140S02A l140s02a = l140s02as.get(0);

				Integer lnYear = l140s02a.getLnYear();
				Integer lnMonth = l140s02a.getLnMonth();
				String other = Util.trim(l140s02a.getLnOther());

				if (isParentCase) {
					payDeadline = other;
				} else {
					if (Util.isNotEmpty(other)) {
						other = "(" + other + ")";
					}
					if (Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth)) {
						// COMMON.year=年
						// COMMON.month=月
						payDeadline = lnYear + prop.getProperty("COMMON.year")
								+ lnMonth + prop.getProperty("COMMON.month");
					}
					payDeadline = payDeadline + other;
				}
			}
		}

		return payDeadline;
	}

	@Override
	public L120S06A findL120s06aByOid(String oid) {
		return l120s06aDao.findByOid(oid);
	}

	/**
	 * 呈案前設定各項費用資料之SEQ
	 */
	@Override
	public void setL140M01RSeq(String sourceMainId) {
		HashMap<String, Integer> cntrnoMap = new HashMap<String, Integer>();
		List<L140M01R> l140m01rs = findl140m01rByMainid(sourceMainId);

		int maxSeq = 0;
		cntrnoMap.put(sourceMainId, maxSeq);
		for (L140M01R l140m01r : l140m01rs) {
			String mainId = l140m01r.getMainId();
			l140m01r.setFeeSeq(cntrnoMap.get(mainId) + 1);
			cntrnoMap.put(mainId, cntrnoMap.get(mainId) + 1);
		}
		l140m01rDao.save(l140m01rs);
	}

	@Override
	public void saveL140MC1A_list(List<L140MC1A> l140mc1a_list) {
		l140mc1aDao.save(l140mc1a_list);
	}

	/**
	 * 因應非分行授權內案件總處要調整各項費用值故於分行呈案時先將原分行的登錄內容儲存起來
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public void AuthLvlreSetL140M01R(String mainId) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		// 先只刪除之前已先複製之各項費用值
		List<L140M01R> l140m01rlist = l140m01rDao.findByMainIdFeeSrc(mainId,
				"3");
		for (L140M01R l140m01r : l140m01rlist) {
			l140m01rDao.delete(l140m01r);
		}

		List<L140M01R> newL140m01rs = new ArrayList<L140M01R>();
		List<L140M01R> l140m01rs = l140m01rDao.findByMainIdFeeSrc(mainId, "0");
		for (L140M01R l140m01rold : l140m01rs) {
			L140M01R l140m01r = new L140M01R();
			DataParse.copy(l140m01rold, l140m01r);

			l140m01r.setCreator(user.getUserId());
			l140m01r.setCreateTime(CapDate.getCurrentTimestamp());
			l140m01r.setMainId(mainId);
			l140m01r.setFeeSrc("3");
			newL140m01rs.add(l140m01r);
		}
		l140m01rDao.save(newL140m01rs);
	}

	@Override
	public void deleteListL140M04A(List<L140M04A> list) {
		l140m04aDao.delete(list);
	}

	private L120S01M _findL120s01m(String mainId, String custId, String dupNo) {
		return l120s01mDao.findByIndex01(mainId, custId, dupNo);
	}

	@Override
	public List<C120M01A> findC120M01AByMainIdForOrder(String mainId) {
		return c120m01aDao.findByMainIdForOrder(mainId);
	}

	private String setL140M01RListDataBefore(String mainId, String AuthLvl) {
		List<Map<String, Object>> maps = null;
		// AuthLvl->1為分行授權內->其它皆為授權外
		if ("1".equals(AuthLvl)) {
			maps = eloandbBASEService.findL140M01RByL120M01A(mainId);
		} else {
			maps = eloandbBASEService.findL140M01RByL120M01AInfeeSrc3(mainId);
		}

		StringBuffer collMemo = new StringBuffer();
		if (maps != null) {
			// feeNo,feeswft,count(*) as tCount,SUM(FEEAMT) AS tSum
			String feeNo = "";
			String feeNoName = "";
			String feeswft = "";
			int tCount = 0;
			int ttCount = 1;
			BigDecimal tSum = BigDecimal.ZERO;
			Map<String, String> feeNoMap = null;

			Properties prop = MessageBundleScriptCreator
					.getComponentResource(CLS1141R01RptServiceImpl.class);

			for (Map<String, Object> map : maps) {
				if (map != null) {
					if (ttCount > 1) {
						collMemo.append("；");
					}
					ttCount = ttCount + 1;
					feeNo = Util.trim(map.get("feeNo"));
					feeswft = Util.trim(map.get("feeswft"));
					tCount = (Integer) map.get("tCount");
					tSum = (BigDecimal) map.get("tSum");
					feeNoMap = codeService.findByCodeType("cls1141_feeNo");
					feeNoName = feeNoMap.get(feeNo);
					// L140M01R.A01={0}共{1}筆合計為{2} {3}元
					collMemo.append(MessageFormat.format(
							prop.getProperty("L140M01R.A01"), feeNoName,
							tCount, feeswft, tSum));
				}
			}
			if (!"".equals(collMemo.toString())) {
				collMemo.append("。");
			} else {
				collMemo.append("無。");
			}
		}
		return collMemo.toString();
	}

	@Override
	public String l140m01r_desc(L120M01A l120m01a) {
		StringBuffer collMemo = new StringBuffer();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);

		String mainId = l120m01a.getMainId();
		String tAuthLvl = l120m01a.getAuthLvl();
		// tmpStr1授權外的值tmpStr2原授權內的值
		String tmpStr1 = "";
		String tmpStr2 = "";
		tmpStr1 = setL140M01RListDataBefore(mainId, "1");

		if (!"1".equals(tAuthLvl)) {
			tmpStr2 = setL140M01RListDataBefore(mainId, tAuthLvl);
		}

		if (!"".equals(tmpStr2)) {
			if (!"".equals(Util.trim(l120m01a.getSignNo()))) {
				if (!tmpStr1.equalsIgnoreCase(tmpStr2)) {
					// L140M01R.A02=原簽報內容為{0}已被審查單位批覆異動成為{1}
					collMemo.append(MessageFormat.format(
							prop.getProperty("L140M01R.A02"), tmpStr2, tmpStr1));
				} else {
					collMemo.append(tmpStr1);
				}
			} else {
				collMemo.append(tmpStr1);
			}

		} else {
			collMemo.append(tmpStr1);
		}

		if (!"".equals(collMemo.toString())) {
			return collMemo.toString();
		} else {
			return "無。";
		}
	}

	private String get_seniority_desc(C120S01B c120s01b) {
		String seniority_desc = LMSUtil.pretty_numStr(c120s01b.getSeniority())
				+ "年"; // default
		if (c120s01b.getSeniority() != null) {
			Integer[] seniorityYM_arr = ClsUtility.seniorityYM_decode(c120s01b
					.getSeniority());
			if (c120s01b.getSnrM() == null) {
				seniority_desc = String.valueOf(seniorityYM_arr[0]) + "年";
			} else {
				seniority_desc = String.valueOf(seniorityYM_arr[0]) + "年"
						+ String.valueOf(seniorityYM_arr[1]) + "月";
			}
		}
		return seniority_desc;
	}

	@Override
	public Map<String, String> get_l120s12a_formData(L120M01A l120m01a,
			L140M01A l140m01a, L140S02A l140s02a) {

		Map<String, String> r = new HashMap<String, String>();
		if (l120m01a != null) {
			Map<String, CapAjaxFormResult> codeMap = codeService
					.findByCodeType(new String[] { "cls1141_resource",
							"cls1141_purpose" });

			CapAjaxFormResult cls1141_purpose = codeMap.get("cls1141_purpose");
			CapAjaxFormResult cls1141_resourceMap = codeMap
					.get("cls1141_resource");
			String resourceOthDesc = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getResourceOth()))) {
				resourceOthDesc = "&nbsp;" + "<u>"
						+ Util.trim(l120m01a.getResourceOth()) + "</u>";
			}
			String purposeOthDesc = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getPurposeOth()))) {
				purposeOthDesc = "&nbsp;" + "<u>"
						+ Util.trim(l120m01a.getPurposeOth()) + "</u>";
			}
			r.put("l120s12a_l120m01a_resource",
					ClsUtil.getPurpose(Util.trim(l120m01a.getResource()),
							cls1141_resourceMap) + resourceOthDesc);
			r.put("l120s12a_l120m01a_purpose",
					ClsUtil.getPurpose(Util.trim(l120m01a.getPurpose()),
							cls1141_purpose) + purposeOthDesc);

			r.put("l120s12a_l140m01r_desc", l140m01r_desc(l120m01a));
		}
		if (l140m01a != null) {
			r.put("l120s12a_custName", l140m01a.getCustName());

			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(),
					l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(
					l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if (c120s01a != null) {
				r.put("l120s12a_birthday",
						Util.trim(TWNDate.toAD(c120s01a.getBirthday())));
				r.put("l120s12a_address", Util.toSemiCharString(Util
						.trim(c120s01a.getCoTarget())));
			}
			if (c120s01b != null) {
				r.put("l120s12a_seniority", get_seniority_desc(c120s01b));
				r.put("l120s12a_yearIncome",
						Util.trim(c120s01b.getPayCurr())
								+ " "
								+ NumConverter.addComma(Util.parseInt(c120s01b
										.getPayAmt().add(c120s01b.getOthAmt())))
								+ "萬元");
			}
			if (c120s01c != null) {
				r.put("l120s12a_drate",
						LMSUtil.pretty_numStr(c120s01c.getDRate()) + "%");
			}

			r.put("l120s12a_approveAmt",
					NumConverter.addComma(Arithmetic.div(
							l140m01a.getCurrentApplyAmt(),
							BigDecimal.valueOf(10000), 4)));// 單位:萬元

			if (true) { // 保證人姓名
				Set<String> guarantor_name_set = new LinkedHashSet<String>();
				for (L140S01A l140s01a : clsService.findL140S01A(l140m01a)) {
					if (Util.equals(UtilConstants.lngeFlag.連帶保證人,
							l140s01a.getCustPos())
							|| Util.equals(UtilConstants.lngeFlag.ㄧ般保證人,
									l140s01a.getCustPos())) {
						guarantor_name_set
								.add(Util.trim(l140s01a.getCustName()));
					}
				}
				String guarantor_name = StringUtils.join(guarantor_name_set,
						"、");
				if (Util.isEmpty(guarantor_name)) {
					guarantor_name = "無";
				}
				r.put("l120s12a_guarantor_name", guarantor_name);
			}
		}
		if (l140s02a != null) {
			r.put("l120s12a_lnYearMonth",
					l140s02a.getLnYear() + "年" + l140s02a.getLnMonth() + "月");

		}
		return r;
	}

	@Override
	public Map<String, String> get_l120s13a_formData(L120M01A l120m01a,
			L140M01A l140m01a, L140S02A l140s02a) {

		Map<String, String> r = new HashMap<String, String>();
		if (l120m01a != null) {
			Map<String, CapAjaxFormResult> codeMap = codeService
					.findByCodeType(new String[] { "cls1141_resource",
							"cls1141_purpose" });

			CapAjaxFormResult cls1141_purpose = codeMap.get("cls1141_purpose");
			CapAjaxFormResult cls1141_resourceMap = codeMap
					.get("cls1141_resource");
			String resourceOthDesc = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getResourceOth()))) {
				resourceOthDesc = "&nbsp;" + "<u>"
						+ Util.trim(l120m01a.getResourceOth()) + "</u>";
			}
			String purposeOthDesc = "";
			if (Util.isNotEmpty(Util.trim(l120m01a.getPurposeOth()))) {
				purposeOthDesc = "&nbsp;" + "<u>"
						+ Util.trim(l120m01a.getPurposeOth()) + "</u>";
			}
			r.put("l120s13a_l120m01a_resource",
					ClsUtil.getPurpose(Util.trim(l120m01a.getResource()),
							cls1141_resourceMap) + resourceOthDesc);
			r.put("l120s13a_l120m01a_purpose",
					ClsUtil.getPurpose(Util.trim(l120m01a.getPurpose()),
							cls1141_purpose) + purposeOthDesc);
		}
		if (l140m01a != null) {
			r.put("l120s13a_custName", l140m01a.getCustName());

			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(),
					l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(
					l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if (c120s01a != null) {
				r.put("l120s13a_birthday",
						Util.trim(TWNDate.toAD(c120s01a.getBirthday())));
				r.put("l120s13a_address", Util.toSemiCharString(Util
						.trim(c120s01a.getCoTarget())));
			}
			if (c120s01b != null) {
				r.put("l120s13a_seniority", get_seniority_desc(c120s01b));
				r.put("l120s13a_yearIncome",
						Util.trim(c120s01b.getPayCurr())
								+ " "
								+ NumConverter.addComma(Util.parseInt(c120s01b
										.getPayAmt().add(c120s01b.getOthAmt())))
								+ "萬元");
			}
			if (c120s01c != null) {
				r.put("l120s13a_drate",
						LMSUtil.pretty_numStr(c120s01c.getDRate()) + "%");
			}
			r.put("l120s13a_approveAmt",
					NumConverter.addComma(Arithmetic.div(
							l140m01a.getCurrentApplyAmt(),
							BigDecimal.valueOf(10000), 4)));// 單位:萬元
		}
		if (l140s02a != null) {
			r.put("l120s13a_lnYearMonth",
					l140s02a.getLnYear() + "年" + l140s02a.getLnMonth() + "月");

		}
		return r;
	}

	@Override
	public Map<String, String> get_l120s15a_formData(L120M01A l120m01a,
			L140M01A l140m01a, L140S02A l140s02a) {

		Map<String, String> r = new HashMap<String, String>();
		if (l120m01a != null) {

		}
		if (l140m01a != null) {
			r.put("l120s15a_custName", l140m01a.getCustName());

			String l120m01a_mainId = l120m01a.getMainId();
			String idDup = LMSUtil.getCustKey_len10custId(l140m01a.getCustId(),
					l140m01a.getDupNo());
			Set<String> idDup11Set = new HashSet<String>();
			idDup11Set.add(idDup);
			Map<String, C120S01A> map_c120s01a = clsService.findIdDup_C120S01A(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01B> map_c120s01b = clsService.findIdDup_C120S01B(
					l120m01a_mainId, idDup11Set);
			Map<String, C120S01C> map_c120s01c = clsService.findIdDup_C120S01C(
					l120m01a_mainId, idDup11Set);
			C120S01A c120s01a = map_c120s01a.get(idDup);
			C120S01B c120s01b = map_c120s01b.get(idDup);
			C120S01C c120s01c = map_c120s01c.get(idDup);
			if (c120s01a != null) {

			}
			if (c120s01b != null) {
				r.put("l120s15a_seniority", get_seniority_desc(c120s01b));

			}
			if (c120s01c != null) {
				r.put("l120s15a_drate",
						LMSUtil.pretty_numStr(c120s01c.getDRate()) + "%");
				r.put("l120s15a_drate_atItem",
						LMSUtil.pretty_numStr(c120s01c.getDRate()) + "%");
			}

			r.put("l120s15a_approveAmt",
					NumConverter.addComma(Arithmetic.div(
							l140m01a.getCurrentApplyAmt(),
							BigDecimal.valueOf(10000), 4)));// 單位:萬元

		}
		if (l140s02a != null) {
			r.put("l120s15a_lnYearMonth",
					l140s02a.getLnYear() + "年" + l140s02a.getLnMonth() + "月");

		}
		return r;
	}

	@Override
	public String saveL120S19AForRate(String mainId, String rateDesc,
			String rateRemark) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();

		String docStatus = this.findL120m01aByMainId(mainId).getDocStatus();
		String role = CreditDocStatusEnum.海外_編製中.getCode().equals(docStatus) ? "AO"
				: "RV";

		L120S19A l120s19a = this.l120s19aDao
				.findByMainId_itemType_latest_itemVersion(mainId, "B");

		rateDesc = Util.truncateString(rateDesc, 3000);
		rateRemark = Util.truncateString(rateRemark, 300);

		if (UtilConstants.PaperlessActingRole.AO.equals(role)) {
			l120s19a.setAoUpdRate(rateDesc);
			l120s19a.setAoUpdRateRemark(rateRemark);
		} else {
			l120s19a.setRvUpdRate(rateDesc);
			l120s19a.setRvUpdRateRemark(rateRemark);
		}

		l120s19a.setUpdater(userId);
		l120s19a.setUpdateTime(currentDateTime);

		L120S19B entity = new L120S19B();
		entity.setMainId(mainId);
		entity.setType("rate");
		entity.setContent(rateDesc);
		entity.setRemark(rateRemark);
		entity.setRole(role);
		entity.setCreator(userId);
		entity.setCreateTime(currentDateTime);

		this.l120s19aDao.save(l120s19a);
		this.l120s19bDao.save(entity);

		return rateRemark;
	}

	@Override
	public String build_html_SimplifyFlag_B(L120M01A l120m01a) {
		List<L120S12A> model_list = clsService.findL120S12A(l120m01a
				.getMainId());
		List<L120S12A> choose_list = new ArrayList<L120S12A>();
		for (L120S12A l120s12a : model_list) {
			if (Util.isNotEmpty(l120s12a.getRefMainId())
					&& Util.isNotEmpty(Util.trim(l120s12a.getRefSeq()))) {
				choose_list.add(l120s12a);
			} else {
				continue; // 空的
			}

		}
		if (choose_list.size() == 0) {
			return "";
		}

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1201S21Panel.class);
		String item_match = prop.getProperty("item.match");
		String item_notMatch = prop.getProperty("item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		String html_noBorderTbl_style_width = ""; // " width:9cm; ";
		String html_noBorderTbl_c1_style_width = " width:0.8cm; ";
		String html_noBorderTbl_c2_style_width = " width:3.0cm; ";
		String html_noBorderTbl_c3_style_width = " width:1.5cm; ";
		String html_noBorderTbl_c4_style_width = " width:9.0cm; "; // 為了第
																	// (2),(5)
																	// 折行

		String td_empty = "<td>&nbsp;</td>";
		StringBuffer sb = new StringBuffer();
		for (L120S12A l120s12a : choose_list) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s12a
					.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(
					l120s12a.getRefMainId(), l120s12a.getRefSeq());
			//
			Map<String, String> map_formData = get_l120s12a_formData(l120m01a,
					l140m01a, l140s02a);
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:0px; " + html_table_style_width
					+ "' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("header_basicData")); // header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if (true) {
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("custName")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_custName"),
						prop.getProperty("occupation") + "："
								+ Util.trim(l120s12a.getOccupation()));
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("birthday")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_birthday"),
						prop.getProperty("seniority")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_seniority"));
				if (true) {
					sb.append("<tr>");
					sb.append("<td colspan='2' >")
							.append(prop.getProperty("address")
									+ "："
									+ MapUtils.getString(map_formData,
											"l120s12a_address"))
							.append("</td>");
					sb.append("</tr>");
				}
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("applyAmt")
								+ "：TWD "
								+ NumConverter.addComma(LMSUtil
										.pretty_numStr(l120s12a.getApplyAmt()))
								+ prop.getProperty("curr.unit"),
						prop.getProperty("yearIncome")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_yearIncome"));
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("lnYearMonth")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_lnYearMonth"),
						prop.getProperty("l120m01a_resource")
								+ "： "
								+ MapUtils.getString(map_formData,
										"l120s12a_l120m01a_resource"));
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("l120m01a_purpose")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_l120m01a_purpose"),
						prop.getProperty("drate")
								+ "： "
								+ MapUtils.getString(map_formData,
										"l120s12a_drate") + "&nbsp;&nbsp;"
								+ prop.getProperty("see_attch"));
				_l120s12a_append_sb_column2(
						sb,
						prop.getProperty("l140m01r_desc")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s12a_l140m01r_desc"),
						prop.getProperty("guarantor_name")
								+ "： "
								+ MapUtils.getString(map_formData,
										"l120s12a_guarantor_name"));
			}
			sb.append("</table>");
			// ======================
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:6px; " + html_table_style_width
					+ "' >");
			sb.append("<td colspan='3'>");
			sb.append(prop.getProperty("header_chkItem")); // header_chkItem=審核項目
			sb.append("</td>");

			_l120s12a_append_sb_column3(sb,
					"1." + prop.getProperty("item010.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem010(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem010(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"2." + prop.getProperty("item020.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem020(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem020(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"3." + prop.getProperty("item030.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem030(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem030(), item_notMatch));
			_l120s12a_append_sb_column3(
					sb,
					"4." + prop.getProperty("item040.desc")
							+ prop.getProperty("item040.desc1"),
					_l120s12a_itemval_Y(l120s12a.getItem040(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem040(), item_notMatch));
			_l120s12a_append_sb_column3(
					sb,
					"5." + prop.getProperty("item050.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem050(), item_match)
							+ prop.getProperty("grade.descA")
							+ Util.trim(l120s12a.getGrade1())
							+ prop.getProperty("grade.descB"),
					_l120s12a_itemval_N(l120s12a.getItem050(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"6." + prop.getProperty("item060.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem060(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem060(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"7." + prop.getProperty("item070.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem070(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem070(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"8." + prop.getProperty("item080.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem080(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem080(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"9." + prop.getProperty("item090.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem090(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem090(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"10." + prop.getProperty("item100.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem100(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem100(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"11." + prop.getProperty("item110.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem110(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem110(), item_notMatch));
			_l120s12a_append_sb_column3(
					sb,
					"12." + prop.getProperty("item120.desc")
							+ prop.getProperty("item120.desc1"),
					_l120s12a_itemval_Y(l120s12a.getItem120(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem120(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"13." + prop.getProperty("item130.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem130(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem130(), item_notMatch));
			_l120s12a_append_sb_column3(
					sb,
					"14." + prop.getProperty("item150.desc")
							+ prop.getProperty("item150.desc1"),
					_l120s12a_itemval_Y(l120s12a.getItem150(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem150(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"15." + prop.getProperty("item160.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem160(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem160(), item_notMatch));
			_l120s12a_append_sb_column3(sb,
					"16." + prop.getProperty("item170.desc"),
					_l120s12a_itemval_Y(l120s12a.getItem170(), item_match),
					_l120s12a_itemval_N(l120s12a.getItem170(), item_notMatch));
			if (true) {
				sb.append("<tr>");
				sb.append("<td>")
						.append("17." + prop.getProperty("item140.descA"))
						.append("&nbsp;")
						.append(MapUtils.getString(map_formData,
								"l120s12a_approveAmt"))
						.append(prop.getProperty("item140.descB"));
				if (true) { // 第14項之下包含5個細項
					/*
					 * // 在 span 指定 width:50px; display: inline-block;
					 * 看到的垂直對齊不一致 => 改用 table String span_dbr_style =
					 * " style='width:50px; display: inline-block; text-align:right;' "
					 * ;
					 * sb.append(UtilConstants.Mark.HTMLBR).append("(1)").append
					 * (prop.getProperty("item140.desc1"));
					 * sb.append(UtilConstants
					 * .Mark.HTMLBR).append("(2)").append(
					 * prop.getProperty("item140.desc2A"
					 * )).append("&nbsp;").append
					 * (NumConverter.addComma(LMSUtil.pretty_numStr
					 * (l120s12a.getMonthAmtA
					 * ()))).append(prop.getProperty("curr.unit"))
					 * .append(prop.getProperty("item140.desc2B"))
					 * .append(prop.getProperty
					 * ("item140.desc2C")).append("&nbsp;"
					 * ).append(NumConverter.addComma
					 * (LMSUtil.pretty_numStr(l120s12a
					 * .getMonthAmtB()))).append(prop.getProperty("curr.unit"));
					 * sb
					 * .append(UtilConstants.Mark.HTMLBR).append("(3)").append(
					 * prop.getProperty("item140.desc31A")).append("<span "+
					 * span_dbr_style
					 * +" >").append(LMSUtil.pretty_numStr(l120s12a
					 * .getDbr22A()))
					 * .append("</span>").append(prop.getProperty("item140.desc31B"
					 * )); sb.append(UtilConstants.Mark.HTMLBR).append(
					 * "&nbsp;&nbsp;&nbsp;"
					 * ).append(prop.getProperty("item140.desc32A"
					 * )).append("<span "
					 * +span_dbr_style+" >").append(LMSUtil.pretty_numStr
					 * (l120s12a
					 * .getDbr22B())).append("</span>").append(prop.getProperty
					 * ("item140.desc32B"));
					 * sb.append(UtilConstants.Mark.HTMLBR)
					 * .append("(4)").append(prop.getProperty("item140.desc4"));
					 * sb
					 * .append(UtilConstants.Mark.HTMLBR).append("(5)").append(
					 * prop.getProperty("item140.desc5"));
					 */
					sb.append("<table class='" + html_noBorderTbl_class
							+ "'  style='" + html_noBorderTbl_style_width
							+ "'>");
					sb.append("<tr><td>").append("(1)")
							.append("</td><td colspan='3'>")
							.append(prop.getProperty("item140.desc1"))
							.append("</td>" + td_empty + "</tr>");
					sb.append("<tr><td>")
							.append("(2)")
							.append("</td><td colspan='3'>")
							.append(prop.getProperty("item140.desc2A")
									+ "&nbsp;"
									+ NumConverter.addComma(LMSUtil
											.pretty_numStr(l120s12a
													.getMonthAmtA()))
									+ prop.getProperty("curr.unit"));
					sb.append(prop.getProperty("item140.desc2B"))
							.append(prop.getProperty("item140.desc2C"))
							.append("&nbsp;")
							.append(NumConverter.addComma(LMSUtil
									.pretty_numStr(l120s12a.getMonthAmtB())))
							.append(prop.getProperty("curr.unit"))
							.append("</td>" + td_empty + "</tr>");

					if (l120s12a.getDbr22A() != null
							&& l120s12a.getDbr22B() == null) {
						sb.append(
								"<tr><td style='"
										+ html_noBorderTbl_c1_style_width
										+ "' >")
								.append("(3)")
								.append("</td>")
								.append("<td style='"
										+ html_noBorderTbl_c2_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc31A"))
								.append("</td>")
								.append("<td align='right' style='"
										+ html_noBorderTbl_c3_style_width
										+ "' >")
								.append(LMSUtil.pretty_numStr(l120s12a
										.getDbr22A()))
								.append("&nbsp;</td>")
								.append("<td style='"
										+ html_noBorderTbl_c4_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc31B"))
								.append("</td>" + td_empty + "</tr>");
					} else if (l120s12a.getDbr22A() == null
							&& l120s12a.getDbr22B() != null) {
						sb.append(
								"<tr><td style='"
										+ html_noBorderTbl_c1_style_width
										+ "' >")
								.append("(3)")
								.append("</td>")
								.append("<td style='"
										+ html_noBorderTbl_c2_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc32A"))
								.append("</td>")
								.append("<td align='right' style='"
										+ html_noBorderTbl_c3_style_width
										+ "' >")
								.append(LMSUtil.pretty_numStr(l120s12a
										.getDbr22B()))
								.append("&nbsp;</td>")
								.append("<td style='"
										+ html_noBorderTbl_c4_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc32B"))
								.append("</td>" + td_empty + "</tr>");
					} else { // 兩者都填，或都不填
						sb.append(
								"<tr><td style='"
										+ html_noBorderTbl_c1_style_width
										+ "' >")
								.append("(3)")
								.append("</td>")
								.append("<td style='"
										+ html_noBorderTbl_c2_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc31A"))
								.append("</td>")
								.append("<td align='right' style='"
										+ html_noBorderTbl_c3_style_width
										+ "' >")
								.append(LMSUtil.pretty_numStr(l120s12a
										.getDbr22A()))
								.append("&nbsp;</td>")
								.append("<td style='"
										+ html_noBorderTbl_c4_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc31B"))
								.append("</td>" + td_empty + "</tr>");
						sb.append("<tr><td>")
								.append("&nbsp;")
								.append("</td>")
								.append("<td style='"
										+ html_noBorderTbl_c2_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc32A"))
								.append("</td>")
								.append("<td align='right' style='"
										+ html_noBorderTbl_c3_style_width
										+ "' >")
								.append(LMSUtil.pretty_numStr(l120s12a
										.getDbr22B()))
								.append("&nbsp;</td>")
								.append("<td style='"
										+ html_noBorderTbl_c4_style_width
										+ "' >")
								.append(prop.getProperty("item140.desc32B"))
								.append("</td>" + td_empty + "</tr>");
					}
					sb.append("<tr><td>").append("(4)")
							.append("</td><td colspan='3'>")
							.append(prop.getProperty("item140.desc4"))
							.append("</td>" + td_empty + "</tr>");
					sb.append("<tr><td>").append("(5)")
							.append("</td><td colspan='3'>")
							.append(prop.getProperty("item140.desc5"))
							.append("</td>" + td_empty + "</tr>");
					sb.append("</table>");
					sb.append(prop.getProperty("item140.desc6"));
				}
				sb.append("</td>");
				sb.append("<td>")
						.append(_l120s12a_itemval_Y(l120s12a.getItem140(),
								item_match)).append("</td>");
				sb.append("<td>")
						.append(_l120s12a_itemval_N(l120s12a.getItem140(),
								item_notMatch)).append("</td>");
				sb.append("</tr>");
			}
			if (true) {
				sb.append("<tr>");
				sb.append("<td colspan='3'>")
						.append(prop.getProperty("memo.desc"))
						.append(prop.getProperty("memo.desc2"))
						.append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s12a.getMemo());
				raw_memo = ContractDocUtil
						.convert_string_for_XML_Predefined_entities(raw_memo);
				if (Util.isEmpty(raw_memo)) {
					sb.append("&nbsp;");
				} else {
					sb.append(raw_memo
							.replace("\r\n", UtilConstants.Mark.HTMLBR)
							.replace("\r", UtilConstants.Mark.HTMLBR)
							.replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");
			}
			sb.append("</table>");
		}
		String tbl_str = sb.toString();

		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
				+ "<style>"
				+ "table."
				+ html_table_class
				+ " {border-collapse:collapse; } "
				+ "table."
				+ html_table_class
				+ " td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
				+ "table."
				+ html_noBorderTbl_class
				+ " td{border: 0px; } "
				+ "</style></head>"
				+ "<body>"
				+ tbl_str
				+ "</body>"
				+ "</html>";
	}

	private void _l120s12a_append_sb_column2(StringBuffer sb, String s1,
			String s2) {
		sb.append("<tr>");
		sb.append("<td width='50%' >").append(s1).append("</td>");
		sb.append("<td width='50%' >").append(s2).append("</td>");
		sb.append("</tr>");
	}

	private void _l120s13a_append_sb_column2(StringBuffer sb, String s1,
			String s2) {
		sb.append("<tr>");
		sb.append("<td width='58%' >").append(s1).append("</td>");
		sb.append("<td width='42%' >").append(s2).append("</td>");
		sb.append("</tr>");
	}

	private void _l120s13a_append_sb_column1(StringBuffer sb, String s1) {
		sb.append("<tr>");
		sb.append("<td colspan='2' >").append(s1).append("</td>");
		sb.append("</tr>");
	}

	private void _l120s12a_append_sb_column3(StringBuffer sb, String s1,
			String s2, String s3) {
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:100px;' >").append(s2).append("</td>"); // 在
																			// td
																			// 加上
																			// nowrap
																			// 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");
	}

	private String _l120s12a_itemval_Y(String val, String item_match) {
		return (Util.equals(val, "Y") ? "■" : "□") + item_match;
	}

	private String _l120s12a_itemval_N(String val, String item_notMatch) {
		return (Util.equals(val, "N") ? "■" : "□") + item_notMatch;
	}

	private void _l120s13a_append_sb_column3(StringBuffer sb, String s1,
			String s2, String s3) {
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:60px;' >").append(s2).append("</td>"); // 在
																			// td
																			// 加上
																			// nowrap
																			// 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");
	}

	private void _l120s15a_append_sb_column2(StringBuffer sb, String s1,
			String s2) {
		sb.append("<tr>");
		sb.append("<td width='50%' >").append(s1).append("</td>");
		sb.append("<td width='50%' >").append(s2).append("</td>");
		sb.append("</tr>");
	}

	private void _l120s15a_append_sb_column3(StringBuffer sb, String s1,
			String s2, String s3) {
		sb.append("<tr>");
		sb.append("<td>").append(s1).append("</td>");
		sb.append("<td style='width:60px;' >").append(s2).append("</td>"); // 在
																			// td
																			// 加上
																			// nowrap
																			// 但沒反應
		sb.append("<td style='width:60px;' >").append(s3).append("</td>");
		sb.append("</tr>");
	}

	@Override
	public String build_html_SimplifyFlag_C(L120M01A l120m01a) {
		List<L120S13A> model_list = clsService.findL120S13A(l120m01a
				.getMainId());
		List<L120S13A> choose_list = new ArrayList<L120S13A>();
		for (L120S13A l120s13a : model_list) {
			if (Util.isNotEmpty(l120s13a.getRefMainId())
					&& Util.isNotEmpty(Util.trim(l120s13a.getRefSeq()))) {
				choose_list.add(l120s13a);
			} else {
				continue; // 空的
			}

		}
		if (choose_list.size() == 0) {
			return "";
		}

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1201S23Panel.class);
		String item_match = prop.getProperty("item.match");
		String item_notMatch = prop.getProperty("item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		StringBuffer sb = new StringBuffer();
		for (L120S13A l120s13a : choose_list) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s13a
					.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(
					l120s13a.getRefMainId(), l120s13a.getRefSeq());
			//
			Map<String, String> map_formData = get_l120s13a_formData(l120m01a,
					l140m01a, l140s02a);
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:0px; " + html_table_style_width
					+ "' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("header_basicData")); // header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if (true) {
				_l120s13a_append_sb_column2(
						sb,
						prop.getProperty("custName")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_custName"),
						prop.getProperty("occupation") + "："
								+ Util.trim(l120s13a.getOccupation()));
				_l120s13a_append_sb_column2(
						sb,
						prop.getProperty("birthday")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_birthday"),
						prop.getProperty("yearIncome")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_yearIncome"));
				_l120s13a_append_sb_column1(
						sb,
						prop.getProperty("address")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_address"));
				_l120s13a_append_sb_column1(
						sb,
						prop.getProperty("applyAmt")
								+ "：TWD "
								+ NumConverter.addComma(LMSUtil
										.pretty_numStr(l120s13a.getApplyAmt()))
								+ prop.getProperty("curr.unit"));
				_l120s13a_append_sb_column1(
						sb,
						prop.getProperty("lnYearMonth")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_lnYearMonth"));
				_l120s13a_append_sb_column2(
						sb,
						prop.getProperty("l120m01a_purpose")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s13a_l120m01a_purpose"),
						prop.getProperty("drate")
								+ "： "
								+ MapUtils.getString(map_formData,
										"l120s13a_drate"));
			}
			sb.append("</table>");
			// ======================
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:6px; " + html_table_style_width
					+ "' >");
			sb.append("<tr><td colspan='3'>");
			sb.append(prop.getProperty("header_chkItem")); // header_chkItem=審核項目
			sb.append("</td></td>");

			_l120s13a_append_sb_column3(sb,
					"1." + prop.getProperty("item010.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem010(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem010(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"2." + prop.getProperty("item020.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem020(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem020(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"3." + prop.getProperty("item030.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem030(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem030(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"4." + prop.getProperty("item040.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem040(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem040(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"5." + prop.getProperty("item050.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem050(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem050(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"6." + prop.getProperty("item060.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem060(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem060(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"7." + prop.getProperty("item070.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem070(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem070(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"8." + prop.getProperty("item080.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem080(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem080(), item_notMatch));
			_l120s13a_append_sb_column3(
					sb,
					"9." + prop.getProperty("item090.desc")
							+ prop.getProperty("item090.desc1"),
					_l120s12a_itemval_Y(l120s13a.getItem090(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem090(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"10." + prop.getProperty("item100.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem100(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem100(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"11." + prop.getProperty("item110.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem110(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem110(), item_notMatch));
			_l120s13a_append_sb_column3(sb,
					"12." + prop.getProperty("item120.desc"),
					_l120s12a_itemval_Y(l120s13a.getItem120(), item_match),
					_l120s12a_itemval_N(l120s13a.getItem120(), item_notMatch));

			if (true) {
				sb.append("<tr>");
				sb.append("<td colspan='3'>")
						.append(prop.getProperty("memo.desc"))
						.append(prop.getProperty("memo.desc2"))
						.append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s13a.getMemo());
				raw_memo = ContractDocUtil
						.convert_string_for_XML_Predefined_entities(raw_memo);
				if (Util.isEmpty(raw_memo)) {
					sb.append("&nbsp;");
				} else {
					sb.append(raw_memo
							.replace("\r\n", UtilConstants.Mark.HTMLBR)
							.replace("\r", UtilConstants.Mark.HTMLBR)
							.replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");
			}
			sb.append("</table>");
		}
		String tbl_str = sb.toString();

		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
				+ "<style>"
				+ "table."
				+ html_table_class
				+ " {border-collapse:collapse; } "
				+ "table."
				+ html_table_class
				+ " td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
				+ "table."
				+ html_noBorderTbl_class
				+ " td{border: 0px; } "
				+ "</style></head>"
				+ "<body>"
				+ tbl_str
				+ "</body>"
				+ "</html>";

	}

	@Override
	public String build_html_SimplifyFlag_D(L120M01A l120m01a) {
		List<L120S15A> model_list = clsService.findL120S15A(l120m01a
				.getMainId());
		List<L120S15A> choose_list = new ArrayList<L120S15A>();
		for (L120S15A l120s15a : model_list) {
			if (Util.isNotEmpty(l120s15a.getRefMainId())
					&& Util.isNotEmpty(Util.trim(l120s15a.getRefSeq()))) {
				choose_list.add(l120s15a);
			} else {
				continue; // 空的
			}

		}
		if (choose_list.size() == 0) {
			return "";
		}

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1201S25Panel.class);
		String item_match = prop.getProperty("item.match");
		String item_matchCreditLoanCriterion = prop
				.getProperty("item.matchCreditLoanCriterion");
		String item_notMatch = prop.getProperty("item.notMatch");
		String html_table_class = "fmtTbl";
		String html_noBorderTbl_class = "noBorderTbl";
		String html_table_style_width = " width:18.1cm; ";
		String html_noBorderTbl_style_width = ""; // " width:9cm; ";

		StringBuffer sb = new StringBuffer();
		for (L120S15A l120s15a : choose_list) {
			L140M01A l140m01a = clsService.findL140M01A_mainId(l120s15a
					.getRefMainId());
			L140S02A l140s02a = clsService.findL140S02A(
					l120s15a.getRefMainId(), l120s15a.getRefSeq());
			String l120s15a_rptId = Util.trim(l120s15a.getRptId());
			if (Util.isEmpty(l120s15a_rptId)) {
				l120s15a_rptId = ClsConstants.L120S15A_rptId.V2020;
			}
			//
			Map<String, String> map_formData = get_l120s15a_formData(l120m01a,
					l140m01a, l140s02a);
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:0px; " + html_table_style_width
					+ "' >");
			sb.append("<tr>");
			sb.append("<td colspan='2'>");
			sb.append(prop.getProperty("header_basicData")); // header_basicData=基本資料
			sb.append("</td>");
			sb.append("</tr>");
			if (true) {
				_l120s15a_append_sb_column2(
						sb,
						prop.getProperty("custName")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s15a_custName"),
						prop.getProperty("occupation") + "："
								+ Util.trim(l120s15a.getOccupation()));
				_l120s15a_append_sb_column2(
						sb,
						prop.getProperty("applyAmt")
								+ "：TWD "
								+ NumConverter.addComma(LMSUtil
										.pretty_numStr(l120s15a.getApplyAmt()))
								+ prop.getProperty("curr.unit"),
						prop.getProperty("seniority")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s15a_seniority"));

				_l120s15a_append_sb_column2(
						sb,
						prop.getProperty("lnYearMonth")
								+ "："
								+ MapUtils.getString(map_formData,
										"l120s15a_lnYearMonth"),
						prop.getProperty("avgMincomeAmt")
								+ "：TWD "
								+ NumConverter.addComma(LMSUtil
										.pretty_numStr(l120s15a
												.getAvgMincomeAmt()))
								+ prop.getProperty("curr.unit"));
				_l120s15a_append_sb_column2(
						sb,
						prop.getProperty("pmtAmt")
								+ "：TWD "
								+ NumConverter.addComma(LMSUtil
										.pretty_numStr(l120s15a.getPmtAmt()))
								+ prop.getProperty("curr.unit"),
						prop.getProperty("drate")
								+ "： "
								+ MapUtils.getString(map_formData,
										"l120s15a_drate") + "&nbsp;&nbsp;"
								+ prop.getProperty("see_attch"));
			}
			sb.append("</table>");
			// ======================
			if (true) {
				BigDecimal sum_feeNo01 = BigDecimal.ZERO;
				BigDecimal sum_feeNo02 = BigDecimal.ZERO;
				BigDecimal sum_feeNo03 = BigDecimal.ZERO;
				BigDecimal sum_feeNo04 = BigDecimal.ZERO;
				BigDecimal sum_feeNo05 = BigDecimal.ZERO;
				BigDecimal sum_feeNo06 = BigDecimal.ZERO;
				BigDecimal sum_feeNo07 = BigDecimal.ZERO;
				for (L140M01R l140m01r : clsService
						.findL140M01R_exclude_feeSrc3(l120m01a.getMainId())) {
					String feeNo = l140m01r.getFeeNo();
					BigDecimal amt = l140m01r.getFeeAmt();

					if (Util.equals("01", feeNo)) {
						sum_feeNo01 = sum_feeNo01.add(amt);
					} else if (Util.equals("02", feeNo)) {
						sum_feeNo02 = sum_feeNo02.add(amt);
					} else if (Util.equals("03", feeNo)) {
						sum_feeNo03 = sum_feeNo03.add(amt);
					} else if (Util.equals("04", feeNo)) {
						sum_feeNo04 = sum_feeNo04.add(amt);
					} else if (Util.equals("05", feeNo)) {
						sum_feeNo05 = sum_feeNo05.add(amt);
					} else if (Util.equals("06", feeNo)) {
						sum_feeNo06 = sum_feeNo06.add(amt);
					} else if (Util.equals("07", feeNo)) {
						sum_feeNo07 = sum_feeNo07.add(amt);
					} else {
						continue;
					}
				}

				List<String> m01r_dataStr_list = new ArrayList<String>();
				Map<String, String> feeNoMap = clsService
						.get_codeTypeWithOrder("cls1141_feeNo");
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "01", feeNoMap,
						sum_feeNo01);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "02", feeNoMap,
						sum_feeNo02);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "03", feeNoMap,
						sum_feeNo03);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "04", feeNoMap,
						sum_feeNo04);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "05", feeNoMap,
						sum_feeNo05);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "06", feeNoMap,
						sum_feeNo06);
				proc_SimplifyFlag_D_L140M01R(m01r_dataStr_list, "07", feeNoMap,
						sum_feeNo07);

				String m01r_dataStr = m01r_dataStr_list.size() == 0 ? "無"
						: (StringUtils.join(m01r_dataStr_list, "、") + "。");

				sb.append("<table class='" + html_table_class
						+ "' style='margin-top:6px; " + html_table_style_width
						+ "' >");
				sb.append("<tr>");
				sb.append("<td style='width:90px;'>").append("各項費用")
						.append("</td>");
				sb.append("<td>").append(m01r_dataStr).append("</td>");
				sb.append("</tr>");
				sb.append("</table>");
			}
			// ======================
			sb.append("<table class='" + html_table_class
					+ "' style='margin-top:6px; " + html_table_style_width
					+ "' >");
			sb.append("<tr><td colspan='3'>");
			sb.append(prop.getProperty("header_chkItem")); // header_chkItem=審核項目
			sb.append("</td></tr>");

			if (Util.equals(l120s15a_rptId, ClsConstants.L120S15A_rptId.V202201) 
					|| ClsConstants.L120S15A_rptId.V202301.equals(l120s15a_rptId)) { // V202201
				
				int i = 1;
				
				prop = MessageBundleScriptCreator
						.getComponentResource(CLS1201S25BPanel.class);
				// 1.借款人已成年且具行為能力，且加計借款期間後未超過65歲
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item010.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem010(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem010(),
								item_notMatch));
				
				// 2.借款人工作年資是否符合各專案之年資條件
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item020.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem020(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem020(),
								item_notMatch));
				
				// 3.年薪達新台幣三十萬元(含)以上
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item030.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem030(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem030(),
								item_notMatch));
				
				// 4.無信用不良限制 V202201-(參照本行消金信用評等作業須知) V202301-(參照本行消金授信業務信用評等應用細則)
				String item040Desc1 = ClsConstants.L120S15A_rptId.V202201.equals(l120s15a_rptId) ? prop.getProperty("item040.v202201.desc1") : prop.getProperty("item040.v202301.desc1") ;
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item040.desc")
								+ item040Desc1,
						_l120s12a_itemval_Y(l120s15a.getItem040(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem040(),
								item_notMatch));
				
				// 5.item180.desc=信貸加碼條件：
				if(ClsConstants.L120S15A_rptId.V202301.equals(l120s15a_rptId)){
					
					sb.append("<tr>");
					sb.append("<td colspan='3'>").append((i++) + "." + prop.getProperty("item180.desc")).append("</td>");
					sb.append("</tr>");
					// (1) 無「近 12期借款人出現授信帳戶繳息或還本遲延」之情形
					_l120s15a_append_sb_column3(
						sb,
						"&nbsp;(1) " + prop.getProperty("item181.desc"), 
						_l120s12a_itemval_Y(l120s15a.getItem181(), item_match), 
						_l120s12a_itemval_N(l120s15a.getItem181(), item_notMatch));
					// (2) 無「最近一期信用卡循環比率>50%」之情形
					_l120s15a_append_sb_column3(
						sb,
						"&nbsp;(2) " + prop.getProperty("item182.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem182(), item_match), 
						_l120s12a_itemval_N(l120s15a.getItem182(), item_notMatch));
					// (3) 無「最近12期信用卡繳款出現全額逾期未繳2次(含)以上」之情形
					_l120s15a_append_sb_column3(
						sb,
						"&nbsp;(3) " + prop.getProperty("item183.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem183(), item_match), 
						_l120s12a_itemval_N(l120s15a.getItem183(), item_notMatch));
					// (4) 無「最近一期具預借現金紀錄」之情形
					_l120s15a_append_sb_column3(
						sb,
						"&nbsp;(4) " + prop.getProperty("item184.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem184(), item_match), 
						_l120s12a_itemval_N(l120s15a.getItem184(), item_notMatch));
					// (5) 無「具動用現金卡餘額」之情形
					_l120s15a_append_sb_column3(
						sb,
						"&nbsp;(5) " + prop.getProperty("item185.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem185(), item_match), 
						_l120s12a_itemval_N(l120s15a.getItem185(), item_notMatch));
				}
				
				// 具償債能力(詳個人負債比率分析表)
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item060.desc")
								+ prop.getProperty("item060.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem060(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem060(),
								item_notMatch));
				// 身分證資料相符
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item070.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem070(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem070(),
								item_notMatch));
				// 無受監護輔助宣告
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item080.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem080(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem080(),
								item_notMatch));
				// 無本行利害關係
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item090.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem090(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem090(),
								item_notMatch));
				// 無婉卻及往來信用異常紀錄
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item100.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem100(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem100(),
								item_notMatch));
				// 無證券違約紀錄
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item110.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem110(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem110(),
								item_notMatch));
				// 無疑似人頭戶表徵
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item120.desc")
								+ prop.getProperty("item120.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem120(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem120(),
								item_notMatch));
				// 無近期聯徵中心密集查詢紀錄
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item130.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem130(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem130(),
								item_notMatch));
				// 無欲以本案借款購買本行理財商品
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item140.desc")
								+ prop.getProperty("item140.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem140(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem140(),
								item_notMatch));
				// 已照會確認客戶任職公司無誤
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item150.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem150(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem150(),
								item_notMatch));
				// 已照會確認客戶基本資料及申貸原因與進件來源
				_l120s15a_append_sb_column3(
						sb,
						(i++) + "." + prop.getProperty("item160.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem160(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem160(),
								item_notMatch));
				if (true) {
					sb.append("<tr>");
					sb.append("<td>")
							.append((i++) + "." + prop.getProperty("item170.descA"))
							.append("&nbsp;")
							.append(MapUtils.getString(map_formData,
									"l120s15a_approveAmt"))
							.append(prop.getProperty("item170.descB"));
					if (true) { // 第14項之下包含5個細項
						sb.append("<table class='" + html_noBorderTbl_class
								+ "'  style='" + html_noBorderTbl_style_width
								+ "'>");
						sb.append("<tr><td>").append("(1)").append("</td><td>")
								.append(prop.getProperty("item170.desc1"))
								.append("</td></tr>");
						sb.append("<tr><td>")
								.append("(2)")
								.append("</td><td>")
								.append(prop.getProperty("item170.desc2A")
										+ "&nbsp;"
										+ MapUtils.getString(map_formData,
												"l120s15a_drate_atItem")
										+ "&nbsp;");
						if (Util.equals(l120s15a_rptId,
								ClsConstants.L120S15A_rptId.V2021001)) {

						} else if (Util.equals(l120s15a_rptId,
								ClsConstants.L120S15A_rptId.V2020)) {
							sb.append(prop.getProperty("item170.desc2B"));
						}
						sb.append("</td></tr>");
						sb.append("<tr><td>")
								.append("(3)")
								.append("</td><td>")
								.append(prop.getProperty("item170.desc3A")
										+ "&nbsp;"
										+ (l120s15a.getDbr22A() == null ? "&nbsp;&nbsp;&nbsp;&nbsp;"
												: LMSUtil
														.pretty_numStr(l120s15a
																.getDbr22A()))
										+ "&nbsp;");
						sb.append(prop.getProperty("item170.desc3B")).append(
								"</td></tr>");

						sb.append("<tr><td>").append("(4)").append("</td><td>")
								.append(prop.getProperty("item170.desc4"))
								.append("</td></tr>");
						sb.append("<tr><td>").append("(5)").append("</td><td>")
								.append(prop.getProperty("item170.desc5"))
								.append("</td></tr>");
						sb.append("</table>");
						sb.append(prop.getProperty("item170.desc6"));
					}
					sb.append("</td>");
					String l120s15a_item170_descY = item_match;
					l120s15a_item170_descY = item_matchCreditLoanCriterion;
					sb.append("<td>")
							.append(_l120s12a_itemval_Y(l120s15a.getItem170(),
									l120s15a_item170_descY)).append("</td>");
					sb.append("<td>")
							.append(_l120s12a_itemval_N(l120s15a.getItem170(),
									item_notMatch)).append("</td>");
					sb.append("</tr>");
				}
			} else {
				_l120s15a_append_sb_column3(
						sb,
						"1." + prop.getProperty("item010.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem010(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem010(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"2." + prop.getProperty("item020.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem020(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem020(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"3." + prop.getProperty("item030.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem030(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem030(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"4." + prop.getProperty("item040.desc")
								+ prop.getProperty("item040.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem040(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem040(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"5." + prop.getProperty("item050.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem050(), item_match)
								+ prop.getProperty("grade.descA")
								+ Util.trim(l120s15a.getGrade1())
								+ prop.getProperty("grade.descB"),
						_l120s12a_itemval_N(l120s15a.getItem050(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"6." + prop.getProperty("item060.desc")
								+ prop.getProperty("item060.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem060(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem060(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"7." + prop.getProperty("item070.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem070(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem070(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"8." + prop.getProperty("item080.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem080(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem080(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"9." + prop.getProperty("item090.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem090(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem090(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"10." + prop.getProperty("item100.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem100(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem100(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"11." + prop.getProperty("item110.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem110(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem110(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"12." + prop.getProperty("item120.desc")
								+ prop.getProperty("item120.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem120(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem120(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"13." + prop.getProperty("item130.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem130(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem130(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"14." + prop.getProperty("item140.desc")
								+ prop.getProperty("item140.desc1"),
						_l120s12a_itemval_Y(l120s15a.getItem140(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem140(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"15." + prop.getProperty("item150.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem150(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem150(),
								item_notMatch));
				_l120s15a_append_sb_column3(
						sb,
						"16." + prop.getProperty("item160.desc"),
						_l120s12a_itemval_Y(l120s15a.getItem160(), item_match),
						_l120s12a_itemval_N(l120s15a.getItem160(),
								item_notMatch));
				if (true) {
					sb.append("<tr>");
					sb.append("<td>")
							.append("17." + prop.getProperty("item170.descA"))
							.append("&nbsp;")
							.append(MapUtils.getString(map_formData,
									"l120s15a_approveAmt"))
							.append(prop.getProperty("item170.descB"));
					if (true) { // 第14項之下包含5個細項
						/*
						 * // 在 span 指定 width:50px; display: inline-block;
						 * 看到的垂直對齊不一致 => 改用 table String span_dbr_style =
						 * " style='width:50px; display: inline-block; text-align:right;' "
						 * ;
						 * sb.append(UtilConstants.Mark.HTMLBR).append("(1)").append
						 * (prop.getProperty("item140.desc1"));
						 * sb.append(UtilConstants
						 * .Mark.HTMLBR).append("(2)").append
						 * (prop.getProperty("item140.desc2A"
						 * )).append("&nbsp;").
						 * append(NumConverter.addComma(LMSUtil
						 * .pretty_numStr(l120s12a
						 * .getMonthAmtA()))).append(prop.
						 * getProperty("curr.unit"))
						 * .append(prop.getProperty("item140.desc2B"))
						 * .append(prop
						 * .getProperty("item140.desc2C")).append("&nbsp;"
						 * ).append
						 * (NumConverter.addComma(LMSUtil.pretty_numStr(l120s12a
						 * .
						 * getMonthAmtB()))).append(prop.getProperty("curr.unit"
						 * ));
						 * sb.append(UtilConstants.Mark.HTMLBR).append("(3)")
						 * .append
						 * (prop.getProperty("item140.desc31A")).append("<span "
						 * +span_dbr_style+" >").append(LMSUtil.pretty_numStr(
						 * l120s12a
						 * .getDbr22A())).append("</span>").append(prop.getProperty
						 * ("item140.desc31B"));
						 * sb.append(UtilConstants.Mark.HTMLBR
						 * ).append("&nbsp;&nbsp;&nbsp;"
						 * ).append(prop.getProperty
						 * ("item140.desc32A")).append("<span "
						 * +span_dbr_style+" >"
						 * ).append(LMSUtil.pretty_numStr(l120s12a
						 * .getDbr22B())).
						 * append("</span>").append(prop.getProperty
						 * ("item140.desc32B"));
						 * sb.append(UtilConstants.Mark.HTMLBR
						 * ).append("(4)").append
						 * (prop.getProperty("item140.desc4"));
						 * sb.append(UtilConstants
						 * .Mark.HTMLBR).append("(5)").append
						 * (prop.getProperty("item140.desc5"));
						 */
						sb.append("<table class='" + html_noBorderTbl_class
								+ "'  style='" + html_noBorderTbl_style_width
								+ "'>");
						sb.append("<tr><td>").append("(1)").append("</td><td>")
								.append(prop.getProperty("item170.desc1"))
								.append("</td></tr>");
						sb.append("<tr><td>")
								.append("(2)")
								.append("</td><td>")
								.append(prop.getProperty("item170.desc2A")
										+ "&nbsp;"
										+ MapUtils.getString(map_formData,
												"l120s15a_drate_atItem")
										+ "&nbsp;");
						if (Util.equals(l120s15a_rptId,
								ClsConstants.L120S15A_rptId.V2021001)) {

						} else if (Util.equals(l120s15a_rptId,
								ClsConstants.L120S15A_rptId.V2020)) {
							sb.append(prop.getProperty("item170.desc2B"));
						}
						sb.append("</td></tr>");
						sb.append("<tr><td>")
								.append("(3)")
								.append("</td><td>")
								.append(prop.getProperty("item170.desc3A")
										+ "&nbsp;"
										+ (l120s15a.getDbr22A() == null ? "&nbsp;&nbsp;&nbsp;&nbsp;"
												: LMSUtil
														.pretty_numStr(l120s15a
																.getDbr22A()))
										+ "&nbsp;");
						sb.append(prop.getProperty("item170.desc3B")).append(
								"</td></tr>");

						sb.append("<tr><td>").append("(4)").append("</td><td>")
								.append(prop.getProperty("item170.desc4"))
								.append("</td></tr>");
						sb.append("<tr><td>").append("(5)").append("</td><td>")
								.append(prop.getProperty("item170.desc5"))
								.append("</td></tr>");
						sb.append("</table>");
						sb.append(prop.getProperty("item170.desc6"));
					}
					sb.append("</td>");
					String l120s15a_item170_descY = item_match;
					if (Util.equals(l120s15a_rptId,
							ClsConstants.L120S15A_rptId.V2021001)) {
						l120s15a_item170_descY = item_matchCreditLoanCriterion;
					} else if (Util.equals(l120s15a_rptId,
							ClsConstants.L120S15A_rptId.V2020)) {
						l120s15a_item170_descY = item_match;
					}
					sb.append("<td>")
							.append(_l120s12a_itemval_Y(l120s15a.getItem170(),
									l120s15a_item170_descY)).append("</td>");
					sb.append("<td>")
							.append(_l120s12a_itemval_N(l120s15a.getItem170(),
									item_notMatch)).append("</td>");
					sb.append("</tr>");
				}
			}
			if (true) {
				sb.append("<tr>");
				sb.append("<td colspan='3'>")
						.append(prop.getProperty("memo.desc"))
						.append(prop.getProperty("memo.desc2"))
						.append(UtilConstants.Mark.HTMLBR);
				String raw_memo = Util.trim(l120s15a.getMemo());
				raw_memo = ContractDocUtil
						.convert_string_for_XML_Predefined_entities(raw_memo);
				if (Util.isEmpty(raw_memo)) {
					sb.append("&nbsp;");
				} else {
					sb.append(raw_memo
							.replace("\r\n", UtilConstants.Mark.HTMLBR)
							.replace("\r", UtilConstants.Mark.HTMLBR)
							.replace("\n", UtilConstants.Mark.HTMLBR));
				}
				sb.append("</td>");
				sb.append("</tr>");
			}
			sb.append("</table>");
		}
		String tbl_str = sb.toString();

		return "<html><head><meta http-equiv='Content-Type' content='text/html; charset=utf-8'>"
				+ "<style>"
				+ "table."
				+ html_table_class
				+ " {border-collapse:collapse; } "
				+ "table."
				+ html_table_class
				+ " td{border-style: solid;border-width: 1px; vertical-align:top; font-family:標楷體;font-size:14px}"
				+ "table."
				+ html_noBorderTbl_class
				+ " td{border: 0px; } "
				+ "</style></head>"
				+ "<body>"
				+ tbl_str
				+ "</body>"
				+ "</html>";
	}

	private void proc_SimplifyFlag_D_L140M01R(List<String> m01r_dataStr_list,
			String feeNo, Map<String, String> feeNoMap, BigDecimal feeNoAmt) {
		if (feeNoAmt.compareTo(BigDecimal.ZERO) > 0) {
			m01r_dataStr_list.add(LMSUtil.getDesc(feeNoMap, feeNo) + ":"
					+ NumConverter.addComma(LMSUtil.pretty_numStr(feeNoAmt)));
		}
	}

	@Override
	public String importC120S01T(L120M01A l120m01a) {
		String mainId = l120m01a.getMainId();

		List<C120M01A> l120s01aList = c120m01aDao.findByMainIdForOrder(mainId);
		List<C120S01T> c120s01tList = new ArrayList<C120S01T>();
		int intSeqNo = 1;
		StringBuilder ejcicNoData = new StringBuilder();

		for (C120M01A c120m01a : l120s01aList) {
			if (UtilConstants.lngeFlag.擔保品提供人.equals(c120m01a.getCustPos())) {
				continue;
			}
			C120S01T c120s01t = new C120S01T();
			try {
				DataParse.copy(c120m01a, c120s01t);
			} catch (CapException e) {
				logger.error(StrUtils.getStackTrace(e));
			}
			c120s01t.setSeqNo(intSeqNo);

			// 塞值
			// C120S01A c120s01a =
			// clsService.findC120S01A_idDup_latestIdv(c120m01a.getCustId(),
			// c120m01a.getDupNo());
			// c120s01t.setAge(OverSeaUtil.getAge(c120s01a.getBirthday(),
			// l120m01a.getCaseDate()));
			
			// J-113-0100 借款人明細若為簽報書的主借款人，關係預設輸入「本人」
			if(Util.equals(Util.trim(c120m01a.getCustId()),
								Util.trim(c120s01t.getCustId()) )){
				c120s01t.setRelationship("本人");
			}else{
				c120s01t.setRelationship("");
			}

			C120S01B c120s01b = clsService.findC120S01B(l120m01a.getMainId(),
					c120m01a.getCustId(), c120m01a.getDupNo());
			if (c120s01b != null) {
				String jobTitle = Util.trim(c120s01b.getJobTitle());
				String jobTitleDesc = "";
				if (Util.isNotEmpty(jobTitle)) {
					Map<String, String> jobTitleMap = clsService
							.get_codeTypeWithOrder("lms1205s01_jobTitle");
					jobTitleDesc = LMSUtil.getDesc(jobTitleMap, jobTitle);
				}
				// 職業(職稱)
				c120s01t.setOccupation(Util.truncateString(
						Util.trim(c120s01b.getComName()) + jobTitleDesc, 120));
				// 年所得
				// c120s01t.setYearIncome(c120s01b.getPayAmt().add(c120s01b.getOthAmt()));
			}

			C120S01E c120s01e = clsService.findC120S01E(l120m01a.getMainId(),
					c120m01a.getCustId(), c120m01a.getDupNo());
			if (c120s01e != null) {
				String isQdata2 = c120s01e.getIsQdata2(); // 本行利害關係人
				String isQdata3 = c120s01e.getIsQdata3(); // 金控利害關係人_44條
				String isQdata16 = c120s01e.getIsQdata16(); // 金控利害關係人_45條
				String strIsqdataDesc = "";
				// 利害關係人
				if (Util.equals(isQdata2, "1")) {
					strIsqdataDesc += "本行利害關係人";
				}

				if (Util.equals(isQdata3, "1")) {
					if (!Util.isEmpty(strIsqdataDesc)) {
						strIsqdataDesc += "、";
					}
					strIsqdataDesc += "金控利害關係人_44條";
				}

				if (Util.equals(isQdata16, "1")) {
					if (!Util.isEmpty(strIsqdataDesc)) {
						strIsqdataDesc += "、";
					}
					strIsqdataDesc += "金控利害關係人_45條";
				}

				if (!Util.isEmpty(strIsqdataDesc)) {
					c120s01t.setIsqdata("Y");
					c120s01t.setIsqdataDesc(strIsqdataDesc);
				} else {
					c120s01t.setIsqdata("N");
				}

				// 票交所查覆資料日期
				// c120s01t.setEChkDDate(c120s01e.getEChkDDate());

				// AML重大負面新聞
				// c120s01t.setAmlBadNews(c120s01e.getAmlBadNews());
			}

			// C120S01G c120s01g = clsService.findC120S01G(l120m01a.getMainId(),
			// c120m01a.getCustId(),
			// c120m01a.getDupNo());
			// if(c120s01g != null){
			// //退票
			// if (Util.equals("1", c120s01g.getChkItem1a())){
			// c120s01t.setChkItem1a("Y");
			// } else if(Util.equals("2", c120s01g.getChkItem1a())){
			// c120s01t.setChkItem1a("N");
			// }
			//
			// //拒往
			// if (Util.equals("1", c120s01g.getChkItem1b())){
			// c120s01t.setChkItem1b("Y");
			// } else if(Util.equals("2", c120s01g.getChkItem1b())){
			// c120s01t.setChkItem1b("N");
			// }
			// }

			// //黑名單查詢
			// List<L120S09A> l120s09a =
			// l120S09aDao.findByMainIdAndCustIdDupNo(mainId,
			// c120m01a.getCustId(),
			// c120m01a.getDupNo());
			// if (l120s09a.size() != 0 && l120s09a.get(0) != null) {
			// c120s01t.setBlackListCode(l120s09a.get(0).getBlackListCode());
			// }

			// //J10信評
			// TreeMap<String, String> J10_BREACH_MAP =
			// this.getJ10DefaultRateByType("1");
			// TreeMap<String, String> J10_PERCENTILE_MAP =
			// this.getJ10DefaultRateByType("2");
			//
			// c120s01t = this.importJ10(c120s01t, J10_BREACH_MAP,
			// J10_PERCENTILE_MAP);

			if (clsService.is_function_on_codetype("c120s01t_j10_column")) {
				CapAjaxFormResult j10_data = new CapAjaxFormResult();
				if (true) {
					inject_J10_data(j10_data, c120s01t.getCustId());
				}
				String j10Date = Util.trim(j10_data.get("j10Date"));
				String j10Score = Util.trim(j10_data.get("j10Score"));
				String j10Percentile = Util.trim(j10_data.get("j10Percentile"));
				String j10Breach = Util.trim(j10_data.get("j10Breach"));
				if (Util.isNotEmpty(j10Date)) {
					Date dt_j10Date = CapDate.parseDate(j10Date);
					if (CrsUtil.isNOT_null_and_NOTZeroDate(dt_j10Date)) {
						c120s01t.setJ10Date(dt_j10Date);
						c120s01t.setJ10Score(j10Score);
						c120s01t.setJ10Percentile(j10Percentile);
						c120s01t.setJ10Breach(j10Breach);
					}
				}
			}
			if(clsService.is_function_on_codetype("J-112-0304_importCredit")){
				importCreditFromEjcic(c120s01t,c120s01e,ejcicNoData);
			}
			c120s01tList.add(c120s01t);

			intSeqNo++;
		}

		if (c120s01tList.size() > 0) {
			String mainid = c120s01tList.get(0).getMainId();
			c120s01tDao.deleteByMainId(mainid);
		}

		for (C120S01T bean : c120s01tList) {
			save(bean);
		}
		if(Util.isNotEmpty(ejcicNoData)){
			return "借款人"+ejcicNoData.toString()+"查無聯徵資料，請確認或重查聯徵後重新引入借款人明細";
		}
		
		return ejcicNoData.toString();

	}

	// private C120S01T importJ10(C120S01T c120s01t, TreeMap<String, String>
	// J10_BREACH_MAP,
	// TreeMap<String, String> J10_PERCENTILE_MAP) {
	// String xCustId = c120s01t.getCustId();
	//
	// Map<String, Object> kcs003_map = null;
	// if(MapUtils.isEmpty(kcs003_map)){
	// List<Map<String, Object>> kcs003_list =
	// ejcicService.getKCS003_data_ordByQdateDesc(xCustId);
	// if(kcs003_list.size()>0){
	// kcs003_map = kcs003_list.get(0);
	// }
	// }
	// String j10score = "";
	// String j10Date = "";
	// if(kcs003_map != null && !kcs003_map.isEmpty()){
	// j10score = CapString.trimNull(kcs003_map.get("SCORE"));
	// j10Date = CapString.trimNull(kcs003_map.get("QDATE")); //民國
	// }
	// c120s01t.setJ10Date(TWNDate.valueOf(j10Date)); //轉西元
	//
	// if (Util.isEmpty(j10score)) {
	// c120s01t.setJ10Percentile(null);
	// c120s01t.setJ10Breach(null);
	// } else if(Util.equals("0", j10score)){
	// c120s01t.setJ10Percentile("N.A.");
	// c120s01t.setJ10Breach("N.A.");
	// } else {
	// Entry<String, String> scoreEntry =
	// J10_PERCENTILE_MAP.ceilingEntry(j10score);
	// if (scoreEntry != null) {
	// c120s01t.setJ10Percentile(scoreEntry.getValue());
	// }
	//
	// Entry<String, String> entry = J10_BREACH_MAP.ceilingEntry(j10score);
	// if (entry != null) {
	// c120s01t.setJ10Breach(entry.getValue());
	// }
	// }
	//
	// return c120s01t;
	// }

	@Override
	public TreeMap<String, String> getJ10DefaultRateByType(String type) {
		List<Map<String, Object>> list = eloandbBASEService
				.getJ10DefaultRateByType(type);

		TreeMap<String, String> map = new TreeMap<String, String>();
		BigDecimal hundred = new BigDecimal(100);

		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> jMap = list.get(i);
			BigDecimal score = LMSUtil.toBigDecimal(Util.trim(MapUtils
					.getString(jMap, "SCORE", "0")));
			BigDecimal defaultRate = LMSUtil.toBigDecimal(Util.trim(MapUtils
					.getString(jMap, "DEFAULT_RATE", "0")));
			String rate = hundred.multiply(defaultRate)
					.setScale(2, BigDecimal.ROUND_HALF_UP).toString()
					+ "%";

			if (Util.equals("2", type)) {

				if (i == 0) {
					rate = "0%~" + rate;
				} else {
					Map<String, Object> preMap = list.get(i - 1);
					BigDecimal preDefaultRate = LMSUtil.toBigDecimal(Util
							.trim(MapUtils.getString(preMap, "DEFAULT_RATE",
									"0")));

					rate = hundred.multiply(
							preDefaultRate
									.setScale(2, BigDecimal.ROUND_HALF_UP))
							.toString()
							+ "%" + "~" + rate;
				}
			}

			map.put(score.toPlainString(), rate);
		}
		return map;
	}

	@Override
	public boolean changeC120S01TSeqNo(C120S01T model, boolean upOrDown) {
		ISearch search = c120s01tDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, model.getMainId());
		if (upOrDown) {
			search.addSearchModeParameters(SearchMode.LESS_EQUALS, "seqNo",
					model.getSeqNo());
			search.addOrderBy("seqNo", true);
		} else {
			search.addSearchModeParameters(SearchMode.GREATER_EQUALS, "seqNo",
					model.getSeqNo());
			search.addOrderBy("seqNo", false);
		}
		List<C120S01T> c120s01ts = c120s01tDao.find(search);
		if (Util.isEmpty(c120s01ts) || c120s01ts.size() < 2) {
			return false;
		} else {
			C120S01T c120s01tFocus = c120s01ts.get(0);
			C120S01T c120s01tLess = c120s01ts.get(1);

			Integer focusSeq = c120s01tFocus.getSeqNo();
			if (focusSeq == c120s01tLess.getSeqNo()) {
				if (upOrDown) {
					focusSeq = focusSeq + 1;
				} else {
					focusSeq = focusSeq - 1;
				}
			}
			c120s01tFocus.setSeqNo(c120s01tLess.getSeqNo());
			c120s01tLess.setSeqNo(focusSeq);

			save(c120s01tFocus, c120s01tLess);

			c120s01tDao.flush();

			sequenAllC120S01T(model.getMainId());
			return true;
		}
	}

	/**
	 * 重新排序明細
	 * 
	 * @param mainId
	 */
	private void sequenAllC120S01T(String mainId) {
		ISearch search = c120s01tDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		search.addOrderBy("seqNo");
		List<C120S01T> c120s01ts = c120s01tDao.find(search);
		for (int i = 0, size = c120s01ts.size(); i < size; i++) {
			C120S01T c120s01t = c120s01ts.get(i);
			c120s01t.setSeqNo(i + 1);
		}
		c120s01tDao.save(c120s01ts);
	}

	@Override
	public List<L120S09A> findL120S09AByMainIdAndCustIdDupNo(C120S01T model,
			String custId, String dupNo) {
		List<L120S09A> l120s09a = l120S09aDao.findByMainIdAndCustIdDupNo(
				model.getMainId(), model.getCustId(), model.getDupNo());
		return l120s09a;
	}

	@Override
	public C120M01A findC120M01AByfindByUniqueKey(String mainId, String custId,
			String dupNo) {
		C120M01A c120m01a = c120m01aDao.findByUniqueKey(mainId, custId, dupNo);
		return c120m01a;
	}

	@Override
	public CapAjaxFormResult queryC120S01T(C120S01T model) {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L120M01A l120m01a = this.findModelByManId(L120M01A.class,
				model.getMainId());

		try {
			result = DataParse.toResult(model);
		} catch (CapException e) {
			logger.error(StrUtils.getStackTrace(e));
		}

		C120M01A l120s01a = this.findC120M01AByfindByUniqueKey(
				model.getMainId(), model.getCustId(), model.getDupNo());

		// 姓名
		result.set("custName", l120s01a.getCustName());

		C120S01A c120s01a = clsService.findC120S01A_idDup_latestIdv(
				model.getCustId(), model.getDupNo());
		// 年齡
		if (c120s01a == null || c120s01a.getBirthday() == null) {
			result.set("age", ""); // 若以「企業戶」為從債務人
		} else {
			result.set(
					"age",
					OverSeaUtil.getAge(c120s01a.getBirthday(),
							l120m01a.getCaseDate()));
		}

		// 年所得
		C120S01B c120s01b = clsService.findC120S01B(l120m01a.getMainId(),
				model.getCustId(), model.getDupNo());
		if (c120s01b != null) {
			// String jobTitle = Util.trim(c120s01b.getJobTitle());
			// String jobTitleDesc = "";
			// if(Util.isNotEmpty(jobTitle)){
			// Map<String, String> jobTitleMap =
			// clsService.get_codeTypeWithOrder("lms1205s01_jobTitle");
			// jobTitleDesc = LMSUtil.getDesc(jobTitleMap, jobTitle);
			// }
			// 年所得
			if (c120s01a == null
					|| (c120s01b.getPayAmt() == null || c120s01b.getOthAmt() == null)) {
				result.set("yearIncome", ""); // 若以「企業戶」為從債務人
			} else {
				result.set("yearIncome",
						c120s01b.getPayAmt().add(c120s01b.getOthAmt()));
			}
		}

		C120S01E c120s01e = clsService.findC120S01E(l120m01a.getMainId(),
				model.getCustId(), model.getDupNo());
		if (c120s01e != null) {

			// 票交所查覆資料日期
			result.set("eChkDDate", c120s01e.getEChkDDate());

			// AML重大負面新聞
			result.set("amlBadNews", c120s01e.getAmlBadNews());
		}

		C120S01G c120s01g = clsService.findC120S01G(l120m01a.getMainId(),
				model.getCustId(), model.getDupNo());
		if (c120s01g != null) {
			// 退票
			if (Util.equals("1", c120s01g.getChkItem1a())) {
				result.set("chkItem1a", "Y");
			} else if (Util.equals("2", c120s01g.getChkItem1a())) {
				result.set("chkItem1a", "N");
			}

			// 拒往
			if (Util.equals("1", c120s01g.getChkItem1b())) {
				result.set("chkItem1b", "Y");
			} else if (Util.equals("2", c120s01g.getChkItem1b())) {
				result.set("chkItem1b", "N");
			}
		}

		// 黑名單查詢
		List<L120S09A> l120s09a = this.findL120S09AByMainIdAndCustIdDupNo(
				model, model.getCustId(), model.getDupNo());

		if (l120s09a.size() != 0 && l120s09a.get(0) != null) {
			result.set("blackListCode", l120s09a.get(0).getBlackListCode());
		}

		return result;
	}

	private void inject_J10_data(CapAjaxFormResult result, String xCustId) {
		Map<String, Object> kcs003_map = null;
		if (MapUtils.isEmpty(kcs003_map)) {
			List<Map<String, Object>> kcs003_list = ejcicService
					.getKCS003_data_ordByQdateDesc(xCustId);
			if (kcs003_list.size() > 0) {
				kcs003_map = kcs003_list.get(0);
			}
		}
		String j10score = "";
		String j10Date = "";
		if (kcs003_map != null && !kcs003_map.isEmpty()) {
			j10score = CapString.trimNull(kcs003_map.get("SCORE"));
			j10Date = CapString.trimNull(kcs003_map.get("QDATE")); // 民國
		}
		result.set("j10Score", j10score);
		result.set("j10Date", TWNDate.valueOf(j10Date)); // 轉西元

		if (Util.isEmpty(j10score)) {
			result.set("j10Percentile", "");
			result.set("j10Breach", "");
		} else if (Util.equals("0", j10score)) {
			result.set("j10Percentile", "N.A.");
			result.set("j10Breach", "N.A.");
		} else {
			String j10Percentile = "";
			String j10Breach = "";
			if (kcs003_map != null && !kcs003_map.isEmpty()) {
				if (Util.isNotEmpty(Util.trim(MapUtils.getString(kcs003_map,
						"PERCENT_LB")))) {
					BigDecimal percent_LB = CrsUtil.parseBigDecimal(MapUtils
							.getObject(kcs003_map, "PERCENT_LB"));
					BigDecimal percent_UB = CrsUtil.parseBigDecimal(MapUtils
							.getObject(kcs003_map, "PERCENT_UB"));

					j10Percentile = LMSUtil.pretty_numStr(percent_LB) + "%"
							+ "~" + LMSUtil.pretty_numStr(percent_UB) + "%";
				}
			}

			// J10信評
			TreeMap<String, String> J10_BREACH_MAP = this
					.getJ10DefaultRateByType("1");

			if (Util.isEmpty(j10Percentile)) {
				TreeMap<String, String> J10_PERCENTILE_MAP = this
						.getJ10DefaultRateByType("2");

				Entry<String, String> scoreEntry = J10_PERCENTILE_MAP
						.ceilingEntry(j10score);
				if (scoreEntry != null) {
					j10Percentile = scoreEntry.getValue();
				}
			}
			result.set("j10Percentile", Util.trim(j10Percentile));
			// ~~~~~~~~~~~~~~~~~~
			Entry<String, String> entry = J10_BREACH_MAP.ceilingEntry(j10score);
			if (entry != null) {
				j10Breach = entry.getValue();
			}
			result.set("j10Breach", Util.trim(j10Breach));
		}
	}

	//J-112-0304 簡化授信簽報書借款人資訊引入聯徵相關資訊
	private void importCreditFromEjcic(C120S01T c120s01t, C120S01E c120s01e, StringBuilder ejcicNoData){
		String custId = c120s01t.getCustId();
		String prodId = ejcicService.get_cls_PRODID(custId);//取得產品代碼
		
		if(c120s01e == null){
			c120s01e = new C120S01E();
		}
		Map<String, Object> dateMap = ejcicService.getDate(custId, prodId);
		
		if (dateMap != null) {
			// 取得相關日期
			String QDATE = Util.trim(dateMap.get("QDATE"));

			//1.主債務額度/餘額(本行額度/餘額) clpayNt
			//由聯徵資料主債務欄位進行撈取(聯徵BAM095或同等顯示欄位)，聯徵資料內無資料的話顯示0
			if(true){
				
				//撈全行額度、全行餘額
				Map<String, Object> map_all = ejcicService.getBAM095AllBankAmtData(custId, prodId, QDATE);
				//全行額度
				String totalCont = "0";
				//全行餘額
				BigDecimal loanPlusPass = new BigDecimal("0");
				if(map_all != null){
					if(Util.isNotEmpty(map_all.get("TOT_CONT"))){//全行額度
						totalCont = map_all.get("TOT_CONT").toString();
					}
					//未逾期金額
					BigDecimal totalLoan =  Util.isEmpty(map_all.get("TOT_LOAN")) ?
							new BigDecimal("0") : new BigDecimal(map_all.get("TOT_LOAN").toString());
					//逾期金額
					BigDecimal totalPass = Util.isEmpty(map_all.get("TOT_PASS_DUE")) ?
							new BigDecimal("0") : new BigDecimal(map_all.get("TOT_PASS_DUE").toString());
					//全行餘額=授信未逾期+逾期金額		
					loanPlusPass = totalLoan.add(totalPass);	
				}
				
				//撈本行額度、本行餘額
				Map<String, Object> map_017 = ejcicService.getBAM095Bank017AmtData(custId, prodId, QDATE);
				//本行額度
				String totalCont017 = "0";
				//本行餘額
				BigDecimal loanPlusPass017 = new BigDecimal("0");
				if(map_017 != null){
					if(Util.isNotEmpty(map_017.get("TOT_CONT"))){//本行額度
						totalCont017 = map_017.get("TOT_CONT").toString();
					}
					//未逾期金額
					BigDecimal totalLoan017 =  Util.isEmpty(map_017.get("TOT_LOAN")) ?
							new BigDecimal("0") : new BigDecimal(map_017.get("TOT_LOAN").toString());
					//逾期金額
					BigDecimal totalPass017 = Util.isEmpty(map_017.get("TOT_PASS_DUE")) ?
							new BigDecimal("0") : new BigDecimal(map_017.get("TOT_PASS_DUE").toString());
					//本行餘額=授信未逾期+逾期金額		
					loanPlusPass017 = totalLoan017.add(totalPass017);
				}
				c120s01t.setClpayNt(totalCont+"/"+loanPlusPass.toString()+"("+totalCont017+"/"+loanPlusPass017.toString()+")");
			}
			
			
			//2.共同債務(本行) commonDebt
			//由聯徵資料共同債務欄位(BAM305或同等顯示欄位)進行撈取授信未逾期餘額+逾期未還金額，
			//聯徵資料內無資料的話顯示0
			if(true){
				List<Map<String, Object>> list = ejcicService.getBAM305LoanAmtPassDueAmtByBank(custId, prodId, QDATE);
				BigDecimal commonDebt017Total= new BigDecimal("0");//本行共同債務
				BigDecimal commonDebtTotal= new BigDecimal("0");//全行共同債務
				for(Map<String, Object> map : list){
					String bankCode = map.get("BANKCODE").toString();
					BigDecimal loan = Util.isEmpty(map.get("TOT_LOAN")) ?
							new BigDecimal("0") : new BigDecimal(map.get("TOT_LOAN").toString());
					BigDecimal passDue = Util.isEmpty(map.get("TOT_PASS")) ?
							new BigDecimal("0") : new BigDecimal(map.get("TOT_PASS").toString());
					if(Util.equals("017", bankCode)){//本行
						commonDebt017Total = commonDebt017Total.add(loan).add(passDue);
					}
					commonDebtTotal = commonDebtTotal.add(loan).add(passDue);
				}
				c120s01t.setCommonDebt(getDebtText(commonDebtTotal,commonDebt017Total));
			}
			
			//3.從債務(本行) slaveDebt
			//由聯徵資料從債務欄位(BAM306或同等顯示欄位)進行撈取授信未逾期餘額+逾期未還金額
			//聯徵資料內無資料的話顯示0，無法讀取聯徵資料顯示"無聯徵資料"
			if (true) {
				List<Map<String, Object>> list = ejcicService.getBAM306LoanAmtPassDueAmtByBank(custId, prodId, QDATE);
				BigDecimal slaveDebt017Total= new BigDecimal("0");//本行從債務
				BigDecimal slaveDebtTotal= new BigDecimal("0");//全行從債務
				for(Map<String, Object> map : list){
					String bankCode = map.get("BANKCODE").toString();
					BigDecimal loan = Util.isEmpty(map.get("TOT_LOAN")) ?
							new BigDecimal("0") : new BigDecimal(map.get("TOT_LOAN").toString());
					BigDecimal passDue = Util.isEmpty(map.get("TOT_PASS")) ?
							new BigDecimal("0") : new BigDecimal(map.get("TOT_PASS").toString());
					if(Util.equals("017", bankCode)){//本行
						slaveDebt017Total = slaveDebt017Total.add(loan).add(passDue);
					}
					slaveDebtTotal = slaveDebtTotal.add(loan).add(passDue);
				}
				c120s01t.setSlaveDebt(getDebtText(slaveDebtTotal,slaveDebt017Total));
			}
			
			//4.信用卡使用及繳款狀況 creditState
			//由借保人資料引入(信用卡強停紀錄-isQdata13)，資料來源為聯徵資料，另需加入判斷聯徵資料繳款狀況，
			//如為"XX"或"1N"且無信用卡強停紀錄，顯示"正常"，其餘顯示"異常"
			if (true) {
				String creditState="異常";
				Map<String, Object> map = ejcicService.getKRM040CardPayAbnormalCount(custId, prodId, QDATE);
				if (map != null && Util.parseInt(map.get("Counts")) == 0 //不為"XX"或"1N"的筆數為0
						&& c120s01e != null && Util.equals("2", c120s01e.getIsQdata13())){ //isQdata13=2 無信用卡強停
					creditState="正常";
				}
				c120s01t.setCreditState(creditState);
			}
			
			//5.授信異常紀錄 loanFailRecord
			//由借保人資料引入(主債務逾期、催收、呆帳紀錄-isQdata11)及借款人聯徵資料之共同債務逾期未還金額判斷，
			//無異常紀錄顯示"無"，如有顯示"有"
			if (true) {
				String loanFailRecord = "無";
				Map<String, Object> map = ejcicService.getBAM305PassDueAmtOverZeroCount(custId, prodId, QDATE);
				if ((map != null && Util.parseInt(map.get("Counts")) > 0)||
						Util.equals("1", c120s01e.getIsQdata11()) ){ //isQdata11=1 有主債務逾期、催收、呆帳紀錄
					loanFailRecord = "有";
				}
				c120s01t.setLoanFailRecord(loanFailRecord);	
			}
		}else{
			//查無聯徵資料要出提示
			if(Util.isEmpty(ejcicNoData)){
				ejcicNoData.append(custId);
			}else{
				ejcicNoData.append("、").append(custId);
			}
		}
	}
	
	// J-113-0100 針對簡化授信簽報案件，當風險綜合評估第一題與查核事項90(若有)所勾選結果不一致要出提示訊息
	// 1.風險綜合評估第一題選[是]，查核事項90(若有)要選[否]
	// 2.風險綜合評估第一題選[否]，查核事項90(若有)要選[是]
	// 3.若選不適用就不檢查查核事項90(不管有沒有)
	// msg=簡化授信簽報書/風險綜合評估第一題與查核事項"?"不一致，請再次確認正確性。
	@Override
	public String checkRiskEvaluateQ1(L120M01A l120m01a){
		String checkResult = "";
		if(l120m01a!= null){
			String mainId = Util.trim(l120m01a.getMainId());
			if (UtilConstants.Casedoc.DocCode.一般.equals(l120m01a.getDocCode())
					&& OverSeaUtil.isBranchEditing(l120m01a.getDocStatus())) {// 一般案+編制中
				//SimplifyFlag = A 才要檢查
				if(Util.equals(UtilConstants.Casedoc.SimplifyFlag.一般消金,
						Util.trim(l120m01a.getSimplifyFlag())) && clsService.showRiskEvaluateQA(l120m01a)){
					L120M01I l120m01i = clsService.findL120M01I_mainId(mainId);
					L140M04A l140m04a = l140m04aDao.findByMainIdCheckCode(mainId, "90"); //借款人及保證人之年齡加計貸款年限後是否皆大於75歲
					if(l120m01i != null && l140m04a != null){
						//兩邊都有資料才要檢查
						String riskEvaluateQ1 = Util.trim(l120m01i.getIsRiskQ1());
						String checkCode90 = Util.trim(l140m04a.getCheckYN());
						if( (Util.equals(riskEvaluateQ1, "Y") 
								&& Util.equals(checkCode90, "Y")) ||//風險綜合評估第一題選[是]，查核事項90(若有)要選[否]
								(Util.equals(riskEvaluateQ1, "N") 
										&& Util.equals(checkCode90, "N")) ){//風險綜合評估第一題選[否]，查核事項90(若有)要選[是]
							checkResult = "簡化授信簽報書/風險綜合評估第一題與查核事項" +
										l140m04a.getCheckseq() + "不一致，請再次確認正確性。";
						}
					}
				}
			}
		}
		return checkResult;
	}
	
	private String getDebtText(BigDecimal allDebt,BigDecimal bank017Debt){
		StringBuilder debtText=new StringBuilder();
		
		if(allDebt.compareTo(new BigDecimal(allDebt.intValue())) == 0){
			debtText.append(String.valueOf(allDebt.intValue()));
		}else{
			debtText.append(allDebt.toString());
		}
		debtText.append("(");
		if(bank017Debt.compareTo(new BigDecimal(bank017Debt.intValue())) == 0){
			debtText.append(String.valueOf(bank017Debt.intValue()));
		}else{
			debtText.append(bank017Debt.toString());
		}
		debtText.append(")");
		return debtText.toString();
	}
	
	@Override
	public CapAjaxFormResult getCLS1201S22Panel(L120M01A l120m01a) {
		CapAjaxFormResult CLSForm = new CapAjaxFormResult();
		// 授信簽報書主檔
		// 資金用途,還款來源
		try {
			CLSForm = DataParse.toResult(l120m01a, DataParse.Need,
					new String[] { "purpose", "purposeOth", "resource",
							"resourceOth" });
		} catch (CapException e1) {
			logger.error(StrUtils.getStackTrace(e1));
		}

		// 案件權限
		if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(l120m01a.getAuthLvl())
				|| UtilConstants.Casedoc.AuthLvl.分行授權外.equals(l120m01a
						.getAuthLvl())) {
			CLSForm.set("authLvlN", "N");

			Map<String, String> codeMap_cls1141_docCode = codeService
					.findByCodeType("cls1141_caseLvlReason");

			CLSForm.set(
					"caseLvlReason",
					"(原因："
							+ Util.trim(codeMap_cls1141_docCode.get(l120m01a
									.getCaseLvlReason())) + "，"
							+ Util.trim(l120m01a.getReasonDesc()) + ")");
		} else {
			CLSForm.set("authLvlY", "Y");
		}

		// 個金服務單位檔
		C120S01B c120s01b = clsService.findC120S01B(l120m01a.getMainId(),
				l120m01a.getCustId(), l120m01a.getDupNo());
		if (c120s01b != null) {
			// 借款人年收入合計
			CLSForm.set(
					"c120s01b_payAmtAddothAmt",
					NumConverter.addComma(LMSUtil.pretty_numStr((c120s01b
							.getPayAmt() == null ? BigDecimal.ZERO : c120s01b
							.getPayAmt()).add((c120s01b.getOthAmt() == null ? BigDecimal.ZERO
							: c120s01b.getOthAmt())))));
		}

		// C120S01C．個金償債能力檔
		C120S01C c120s01c = clsService.findC120S01C(l120m01a.getMainId(),
				l120m01a.getCustId(), l120m01a.getDupNo());
		if (c120s01c != null) {
			// 家庭年收入合計
			CLSForm.set("c120s01c_fincome",
					NumConverter.addComma(LMSUtil.pretty_numStr((c120s01c
							.getFincome() == null ? BigDecimal.ZERO : c120s01c
							.getFincome()))));
			// 個人/家庭負債比率
			CLSForm.set("c120s01c_dRate",
					LMSUtil.pretty_numStr(c120s01c.getDRate()));
			CLSForm.set("c120s01c_yRate",
					LMSUtil.pretty_numStr(c120s01c.getYRate()));
		}

		// L120M01D．簽報書敘述說明檔
		// 主管批示
		L120M01D l120m01d_06 = this.findL120m01dByUniqueKey(
				l120m01a.getMainId(),
				UtilConstants.Casedoc.L120m01dItemType.主管批示);
		if (l120m01d_06 != null) {
			CLSForm.set("l120m01d_itemdscr06", l120m01d_06.getItemDscr());
		}

		// 敘做理由
		L120M01D l120m01d_R = this.findL120m01dByUniqueKey(
				l120m01a.getMainId(),
				UtilConstants.Casedoc.L120m01dItemType.敘做理由);
		if (l120m01d_R != null) {
			CLSForm.set("l120m01d_itemdscrR", l120m01d_R.getItemDscr());
		}

		// 簽報書主檔明細
		L120M01I l120m01i = clsService
				.findL120M01I_mainId(l120m01a.getMainId());
		if (l120m01i != null) {
			try {
				CLSForm.putAll(DataParse.toResult(l120m01i));
			} catch (CapException e) {
				logger.error(StrUtils.getStackTrace(e));
			}
		}
		return CLSForm;
	}

	@Override
	public Map<String, Boolean> checkIsSuspectedHeadAccountForL140MC1A(
			L140M01A l140m01a, String l120m01a_mainId)
			throws CapMessageException {

		Map<String, Boolean> rtnMap = new HashMap<String, Boolean>();
		rtnMap.put("IS_SUS_HEAD_ACCOUNT", false);
		rtnMap.put("IS_OUT_OF_AUTH", false);

		if (!UtilConstants.Cntrdoc.Property.新做.equals(l140m01a.getProPerty())) {
			return rtnMap;
		}

		Map<String, L140MC1A> l140mc1aMap = new HashMap<String, L140MC1A>();

		String branchNo = l140m01a.getOwnBrId();

		C120M01A c120m01a = c120m01aDao.findByUniqueKey(l120m01a_mainId,
				l140m01a.getCustId(), l140m01a.getDupNo());// 個金徵信借款人主檔
		C120S01A c120s01a = c120s01aDao.findByUniqueKey(c120m01a.getMainId(),
				c120m01a.getCustId(), c120m01a.getDupNo());

		if (!LMSUtil.isBusCode_060000_130300(c120s01a.getBusCode())
				|| "918".equals(l140m01a.getCntrNo().substring(0, 3))) {// 非為自然人不檢核(ex:企業戶,
																		// 團貸建案母戶)
			return rtnMap;
		}

		List<C100M01> c100m01List = this.checkC100M01(l140m01a.getMainId());

		C120S01B mainLender = this.c120s01bDao
				.findByUniqueKey(c120m01a.getMainId(), c120m01a.getCustId(),
						c120m01a.getDupNo());
		C120S01C c120s01c = this.c120s01cDao
				.findByUniqueKey(c120m01a.getMainId(), c120m01a.getCustId(),
						c120m01a.getDupNo());
		String mainLenderId = mainLender.getCustId();

		BigDecimal othAmt = mainLender.getOthAmt();// 其他收入
		BigDecimal dRate = c120s01c.getDRate();// 個人負債比率

		// A-01 借款人所得與借款金額顯不相稱，且無其他財力文件可供說明
		L140MC1A A = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.A_01_借款人所得與借款金額顯不相稱且無其他財力文件可供說明);
		A.setResult((null == othAmt || othAmt.compareTo(BigDecimal.ZERO) == 0)
				&& dRate.compareTo(new BigDecimal(50)) > 0 ? "1" : "0");

		// F-18 借款人提供之資料與聯徵中心或與本行內部留存資料不相符
		L140MC1A F = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.F_18_借款人提供之資料與聯徵中心或與本行內部留存資料不相符);
		F.setResult(ClsUtility.match_L140MC1A_itemCode_F(c120m01a
				.getIsImportDataMatch()) ? "1" : "0");

		// G-19 年資<=1 年時
		L140MC1A G = new L140MC1A(UtilConstants.L140MC1A_itemCode.G_19_年資小於等於一年);
		String codeG = "0";
		boolean isSeniorityLessThan1Year = false;
		if (null != mainLender.getSeniority()) {
			// codeG = mainLender.getSeniority() <= 1 ? "1" : codeG ;
			isSeniorityLessThan1Year = ClsUtility
					.match_L140MC1A_itemCode_G(mainLender.getSeniority());
			codeG = isSeniorityLessThan1Year ? "1" : codeG;
		}
		G.setResult(codeG);

		// N-04 借款人名下有多筆非自住房貸貸款
		// O-05 借款人於聯徵中心無授信資料
		for (L140MC1A entity : this
				.check_BAM095_LoanData_for_L140MC1A(mainLenderId)) {
			l140mc1aMap.put(entity.getItemCode(), entity);
		}

		// Q-07 借款人於聯徵中心無信用卡記錄或持卡小於 1年
		L140MC1A Q = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.Q_07_借款人於聯徵中心無信用卡記錄或持卡小於一年);
		String codeQ = "1";
		Map<String, Object> cardMap = this.ejcicService
				.getEarliestIssueDateOfUnsuspensionCreditCard(mainLenderId);
		if (null == cardMap.get("START_DATE")) {
			codeQ = "1";
		} else {

			Date issueDateAfter1Year = CapDate.addYears(CapDate.getDate(
					(String) cardMap.get("START_DATE"), "YYYMMDD"), 1);
			Calendar c = Calendar.getInstance();
			c.set(Calendar.DAY_OF_MONTH,
					c.getActualMaximum(Calendar.DAY_OF_MONTH));
			Date currentDate = c.getTime();
			codeQ = currentDate.compareTo(issueDateAfter1Year) >= 0 ? "0" : "1";
		}
		Q.setResult(codeQ);

		// H-19 前工作屬性是否與現工作相同 && G-19 年資<=1 年時
		L140MC1A H = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.H_19_前工作屬性是否與現工作相同);
		H.setResult(ClsUtility.match_L140MC1A_itemCode_H(
				isSeniorityLessThan1Year, mainLender.getIsSameWorkAttributes()) ? "1"
				: "0");

		// I-22 本案是否已確實直接照會借保人本人無誤
		L140MC1A I = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.I_22_本案是否已確實直接照會借保人本人無誤);
		I.setResult(ClsUtility.match_L140MC1A_itemCode_I(c120m01a
				.getIsNoteBorrower()) ? "1" : "0");

		// J-23 已確實核對申貸文件正本資料
		L140MC1A J = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.J_23_已確實核對申貸文件正本資料);
		J.setResult(ClsUtility.match_L140MC1A_itemCode_J(c120m01a
				.getIsCheckOriginalDocument()) ? "1" : "0");

		String cntrNo = l140m01a.getCntrNo();

		List<L140S01A> guarantorList = this.clsService.findL140S01A(l140m01a);

		List<Map<String, String>> buildingDataList = this
				.process_L140M01O_AllBuildingData(c100m01List);

		// K-02 借款人或保證人提供申請資料及證明文件過於完整
		L140MC1A K = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.K_02_借款人或保證人提供申請資料及證明文件過於完整);
		K.setResult(this.checkIsApplyDocumentTooComplete(
				c120m01a.getIsFullApplyDocument(), guarantorList, branchNo,
				l120m01a_mainId));

		// P-06 借款人於聯徵中心被查詢次數密集
		L140MC1A P = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.P_06_借款人於聯徵中心被查詢次數密集);
		P.setResult(this.checkEjcicDataQueryTimesMoreThan2ByOtherBank(
				mainLenderId, guarantorList));

		// B-07 借款人現居地或工作地與案件來源無地緣關係
		L140MC1A B = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.B_07_借款人現居地或工作地與案件來源無地緣關係);
		B.setResult(this.checkGeoRelations(c120m01a, mainLender, branchNo,
				buildingDataList));

		// C-08 保證人財力明顯高於借款人且二者無親屬或共同生活關係
		L140MC1A C = this.checkKinship(guarantorList, mainLender.getPayAmt(),
				c120m01a);

		// D-09 擔保品為法拍屋、建商餘屋、長期空屋
		L140MC1A D = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.D_09_擔保品為法拍屋_建商餘屋_長期空屋);
		D.setResult(this.checkBuildingStatus(buildingDataList));

		String toDate = CapDate.getCurrentDate("yyyy-MM-dd");

		Set<String> collateralOwnerIdSet = this
				.getAllCollateralOwnerInfo(c100m01List);

		/*
		 * L-14 檢查 保證人 及 擔保品所有權人 id, 是否在別的案件擔保品所有權人id相同 及 檢查本案
		 * 主借人、保證人及擔保品所有權人id, 是與別的案件保證人id相同
		 */
		L140MC1A L = this.checkIdIsInOtherCaseByCheckingRelatedPerson(toDate,
				l140m01a.getMainId(), mainLenderId, guarantorList,
				collateralOwnerIdSet);

		// M-14 檢查本案主借人、保證人的聯絡資料（地址/電話），與他案借款人、保證人一樣
		L140MC1A M = this
				.checkContactInfoIsInOtherCaseByCheckingLenderOrGuarantor(
						l140m01a, toDate, guarantorList, c120s01a,
						l120m01a_mainId);

		// 取得個金產品種類檔
		List<L140S02A> l140s02aList = l140s02aDao.findByMainId(l140m01a
				.getMainId());

		// R-行內訂條款A01-評等升等
		L140MC1A R = new L140MC1A(UtilConstants.L140MC1A_itemCode.R_A01_評等升等);
		R.setResult(this.checkIsRatingUpgraded(l120m01a_mainId, l140s02aList));

		/*
		 * 203 12600100 一般短期擔保放款　　 273 12605000 短期房屋擔保放款　　 403 13500100
		 * 一般中期擔保放款　　 473 13506200 中期房屋購置擔保放款 474 13506300 中期房屋修繕擔放　　 603
		 * 14501000 一般長期擔保放款　　 673 14501500 長期房屋購置擔保放款 674 14502000 長期房屋修繕擔放　　
		 */
		String[] housingLoanSubjectCode = new String[] { "12600100",
				"12605000", "13500100", "13506200", "13506300", "14501000",
				"14501500", "14502000" };
		boolean isNotHousingLoan = false;
		for (L140S02A l140s02a : l140s02aList) {
			isNotHousingLoan = isNotHousingLoan
					|| Arrays.asList(housingLoanSubjectCode).contains(
							l140s02a.getSubjCode()) ? false : true;
		}

		// 行內訂條款A02 –有寬限期案件(S)
		// 行內訂條款A03 –有辦房貸壽險案(T)
		L140MC1A S = new L140MC1A(UtilConstants.L140MC1A_itemCode.S_A02_有寬限期案件);
		L140MC1A T = new L140MC1A(UtilConstants.L140MC1A_itemCode.T_A03_有辦房貸壽險案);
		String codeS = "0";
		String codeT = "0";
		for (L140S02A l140s02a : l140s02aList) {
			codeS = "Y".equals(l140s02a.getL140S02E().getNowExtend()) ? "1"
					: codeS;
			codeT = "Y".equals(l140s02a.getL140S02F().getRmbinsFlag()) ? "1"
					: codeT;
		}

		S.setResult(codeS);
		T.setResult(codeT);

		L140MC1A U = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.U_A04_承辦案件的地政士有受警示紀錄);

		L140MC1A W = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.W_借款人_擔保品所有權人與房屋契約書之買方不同人);
		W.setResult("Y".equals(c120m01a.getIsOwnerBuyerNotSamePerson()) ? "1"
				: "0");

		if (isNotHousingLoan) {
			T.setResult("NA");
			U.setResult("NA");
			W.setResult("NA");
		}

		// 行內訂條款A04 –承辦案件的地政士有受警示紀錄(U)
		C120S01E c120s01e = null;
		if (!isNotHousingLoan) {
			c120s01e = this.findC120S01EByUniqueKey(l120m01a_mainId,
					c120m01a.getCustId(), c120m01a.getDupNo());
			List<C120S01Y> c120s01ys = c120s01yDao.findByList(l120m01a_mainId,
					c120m01a.getCustId(), c120m01a.getDupNo());
			Map<String, Object> c900m01h = null;
			for (C120S01Y c120s01y : c120s01ys) {
				Map<String, Object> map = clsService
						.findActiveMajorC900M01HByCertNo2(
								c120s01y.getLaaYear(), c120s01y.getLaaWord(),
								c120s01y.getLaaNo());
				if (map != null) {
					c900m01h = map;
				}
			}
			U.setResult(null != c900m01h ? "1" : "0");
		}

		// E-10 非行員之同一介紹人轉介整批案件之申請案件(單月達三筆)
		L140MC1A E = this.getIsSameIntroductionSourceUpTo3TimesResults(
				l140m01a, c120s01e, toDate);

		L140MC1A V = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.V_買賣契約書_如有_與借款契約_借款申請書簽名不一致);
		V.setResult("Y".equals(c120m01a.getIsDocSignatureNotMatch()) ? "1"
				: "0");

		L140MC1A X = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.X_由非親屬之第三人陪同申辦貸款並回答詢問案件問題);
		X.setResult("Y".equals(c120m01a.getIsWithNonRelatives()) ? "1" : "0");

		L140MC1A Y = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.Y_借款人指定撥款日期及時間且無法提出合理解釋);
		Y.setResult("Y".equals(c120m01a.getIsPointAppropriationDate()) ? "1"
				: "0");

		L140MC1A Z = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.Z_對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念);
		Z.setResult("Y".equals(c120m01a.getIsDontKnowOwnAffairs()) ? "1" : "0");

		L140MC1A item1 = new L140MC1A(
				UtilConstants.L140MC1A_itemCode._1_於聯徵具被查詢紀錄_卻無法說明原因);
		item1.setResult("Y".equals(c120m01a.getIsDontExplainEjcicRecord()) ? "1"
				: "0");

		L140MC1A item2 = new L140MC1A(
				UtilConstants.L140MC1A_itemCode._2_支付銀行收取開辦手續費以外之費用);
		item2.setResult("Y".equals(c120m01a.getIsPayOtherFee()) ? "1" : "0");

		L140MC1A item3 = new L140MC1A(
				UtilConstants.L140MC1A_itemCode._3_借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數);
		item3.setResult(this
				.checkLastThreeDigitsAreZero_ForGeneralSignBookFlow(mainLender,
						guarantorList, c120m01a.getMainId()));

		// 4-借戶所得來自建築業者、代銷、仲介時，或其身分屬前述對象之關係戶(股東、員工)時，是否由其交易資料確認購屋價金來自第三人，且無法佐證其與第三人之關係。
		L140MC1A item4 = new L140MC1A(
				UtilConstants.L140MC1A_itemCode._4_借戶所得來自建築業者_代銷_仲介_是否購屋價金來自第三人_且無法佐證其與第三人之關係);
		item4.setResult("Y".equals(c120m01a.getIsCashFromOthers()) ? "1" : "0");

		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.A_01_借款人所得與借款金額顯不相稱且無其他財力文件可供說明,
						A);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.B_07_借款人現居地或工作地與案件來源無地緣關係, B);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.C_08_保證人財力明顯高於借款人且二者無親屬或共同生活關係,
				C);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.D_09_擔保品為法拍屋_建商餘屋_長期空屋,
				D);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.E_10_非行員之同一介紹人轉介整批之申請案件, E);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.F_18_借款人提供之資料與聯徵中心或與本行內部留存資料不相符,
						F);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.G_19_年資小於等於一年, G);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.H_19_前工作屬性是否與現工作相同, H);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.I_22_本案是否已確實直接照會借保人本人無誤, I);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.J_23_已確實核對申貸文件正本資料, J);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.K_02_借款人或保證人提供申請資料及證明文件過於完整, K);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.L_14_檢查擔保品所有權人_其是否在別的案子也是擔保品提供人,
						L);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.M_14_不同借款人案下保證人_擔保品提供人或聯絡資料相同,
				M);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.P_06_借款人於聯徵中心被查詢次數密集, P);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.Q_07_借款人於聯徵中心無信用卡記錄或持卡小於一年, Q);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.R_A01_評等升等, R);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.S_A02_有寬限期案件, S);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode.T_A03_有辦房貸壽險案, T);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.U_A04_承辦案件的地政士有受警示紀錄, U);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.V_買賣契約書_如有_與借款契約_借款申請書簽名不一致, V);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.W_借款人_擔保品所有權人與房屋契約書之買方不同人, W);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.X_由非親屬之第三人陪同申辦貸款並回答詢問案件問題, X);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode.Y_借款人指定撥款日期及時間且無法提出合理解釋, Y);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode.Z_對自己從事之行業或職業性質與內容及貸款用途不瞭解或毫無概念,
						Z);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode._1_於聯徵具被查詢紀錄_卻無法說明原因,
				item1);
		l140mc1aMap.put(UtilConstants.L140MC1A_itemCode._2_支付銀行收取開辦手續費以外之費用,
				item2);
		l140mc1aMap.put(
				UtilConstants.L140MC1A_itemCode._3_借款人或保證人之扣繳憑單給付總額或收入入帳金額為整數,
				item3);
		l140mc1aMap
				.put(UtilConstants.L140MC1A_itemCode._4_借戶所得來自建築業者_代銷_仲介_是否購屋價金來自第三人_且無法佐證其與第三人之關係,
						item4);

		List<L140MC1A> l140mc1aList = new ArrayList<L140MC1A>(
				l140mc1aMap.values());

		for (L140MC1A l140mc1a : l140mc1aList) {
			l140mc1a.setMainId(l140m01a.getMainId());
			l140mc1a.setCntrNo(cntrNo);
			l140mc1a.setUpdateTime(CapDate.getCurrentTimestamp());
			l140mc1a.setVersion(UtilConstants.IsHeadAccountVersion.VERSION_2_1);
		}

		this.l140mc1aDao.save(l140mc1aList);

		rtnMap.put("IS_SUS_HEAD_ACCOUNT", this.computeIsSuspectedHeadAccount(
				l140mc1aMap, UtilConstants.L140MC1A.STRONG_INDEX_ITEM,
				UtilConstants.L140MC1A.WEAK_INDEX_ITEM));

		rtnMap.put("IS_OUT_OF_AUTH", this.computeIsOutOfAuthorization(
				l140mc1aMap, UtilConstants.L140MC1A.WEAK_INDEX_ITEM));

		return rtnMap;
	}

	private L140MC1A getIsSameIntroductionSourceUpTo3TimesResults(
			L140M01A l140m01a, C120S01E c120s01e, String toDateString) {

		L140MC1A E = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.E_10_非行員之同一介紹人轉介整批之申請案件);

		Date bf1MonthDate = CapDate.addMonth(CapDate.parseDate(toDateString),
				-1);
		String bf1MonthDateString = CapDate.formatDate(bf1MonthDate,
				"yyyy-MM-dd");

		String introCode = l140m01a.getIntroduceSrc();

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		// 3-金控子公司員工引介
		if ("3".equals(introCode)) {
			list = this.eloandbBASEService
					.getSameFinancialHoldingSubsidiaryIntroductionInto(
							bf1MonthDateString, toDateString,
							l140m01a.getMainId(), l140m01a.getMegaCode(),
							l140m01a.getSubEmpNo());
		}

		// 4-代書(地政士)引介
		if ("4".equals(introCode) && c120s01e != null) {
			List<C120S01Y> c120s01ys = c120s01yDao.findByList(
					c120s01e.getMainId(), c120s01e.getCustId(),
					c120s01e.getDupNo());
			for (C120S01Y c120s01y : c120s01ys) {
				List<Map<String, Object>> l = this.eloandbBASEService
						.getSameScrivenerIntroductionInfo(bf1MonthDateString,
								toDateString, l140m01a.getMainId(),
								c120s01y.getLaaName(), c120s01y.getLaaNo());
				list.addAll(l);
			}
		}
		// 6-本行客戶引介
		if ("6".equals(introCode)) {
			list = this.eloandbBASEService
					.getSameMegaBankCustomersIntroductionInto(
							bf1MonthDateString, toDateString,
							l140m01a.getMainId(), l140m01a.getIntroCustId());
		}

		String cntrNoStr = "";
		for (Map<String, Object> m : list) {
			cntrNoStr += m.get("CNTRNO") + ", ";
		}

		E.setResult(list.size() >= 3 ? "1" : "0");
		E.setRemarks(cntrNoStr);

		return E;
	}

	private Boolean computeIsOutOfAuthorization(
			Map<String, L140MC1A> l140mc1aMap, String[] weakIndex) {

		double totalScore = 0;
		double totalCount = 0;

		for (String code : weakIndex) {
			L140MC1A l140mc1a = l140mc1aMap.get(code);
			String score = l140mc1a.getResult();
			if (!"NA".equals(score)) {
				totalScore += Integer.parseInt(score);
				totalCount++;
			}
		}

		L140MC1A U = l140mc1aMap
				.get(UtilConstants.L140MC1A_itemCode.U_A04_承辦案件的地政士有受警示紀錄);
		if (!"NA".equals(U.getResult())) {
			totalScore += Integer.parseInt(U.getResult());
			totalCount++;
		}

		return totalScore > totalCount * 0.4 ? true : false;
	}

	private Boolean computeIsSuspectedHeadAccount(
			Map<String, L140MC1A> l140mc1aMap, String[] strongIndex,
			String[] weakIndex) {

		double weakIndexScore = 0;
		double totalCount = 0;
		String strongIndexScore = "";

		for (String code : strongIndex) {
			L140MC1A l140mc1a = l140mc1aMap.get(code);
			String score = l140mc1a.getResult();
			if (!"NA".equals(score)) {
				strongIndexScore += score;
			}
		}

		for (String code : weakIndex) {
			L140MC1A l140mc1a = l140mc1aMap.get(code);
			String score = l140mc1a.getResult();
			if (!"NA".equals(score)) {
				weakIndexScore += Integer.parseInt(score);
				totalCount++;
			}
		}

		boolean isTouchStrongIndex = strongIndexScore.contains("1") ? true
				: false;

		boolean isSuspectedHeadAccount = false;

		if (isTouchStrongIndex) {
			isSuspectedHeadAccount = true;
		} else {// 計算分數
			isSuspectedHeadAccount = weakIndexScore > totalCount * 0.33 ? true
					: false;
		}

		return isSuspectedHeadAccount;
	}

	@Override
	public void deleteAllL140mc1a(String l140m01a_mainId) {
		this.l140mc1aDao.deleteAll(this.l140mc1aDao
				.findByMainId(l140m01a_mainId));
	}

	private L140MC1A checkIdIsInOtherCaseByCheckingRelatedPerson(String toDate,
			String l140m01a_mainId, String mainLenderId,
			List<L140S01A> guarantorList, Set<String> collateralOwnerIdSet) {

		L140MC1A itemL = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.L_14_檢查擔保品所有權人_其是否在別的案子也是擔保品提供人);
		String codeL = "0";
		String msg = "";

		List<String> idContainer = new ArrayList<String>();
		idContainer.addAll(collateralOwnerIdSet);

		for (L140S01A guarantor : guarantorList) {
			idContainer.add(guarantor.getCustId());
		}

		// 檢查 保證人 及 擔保品所有權人 id, 是否在別的案件擔保品所有權人id相同
		List<Map<String, Object>> cntrNoList = this.eloandbcmsBASEService
				.getCntrnoByComparisonCollateralOwnerId(l140m01a_mainId,
						idContainer);

		String cntrNoStr1 = "";
		for (Map<String, Object> m : cntrNoList) {
			cntrNoStr1 += "'" + String.valueOf(m.get("CNTRNO")) + "',";
		}

		if (!"".equals(cntrNoStr1)) {

			List<String> tmpList = this.misLNF022Service
					.getContractNoWithinValidCreditPeriod(
							cntrNoStr1.substring(0, cntrNoStr1.length() - 1),
							toDate);
			if (!tmpList.isEmpty()) {
				msg += "它案擔保品相同所有權人id相同:"
						+ StringUtils.join(tmpList.toArray(), ",");
			}
		}

		idContainer.add(mainLenderId);

		// 檢查本案 主借人、保證人及擔保品所有權人id, 是與別的案件保證人id相同
		cntrNoList = this.eloandbBASEService
				.getContractNoBySameWithGuarantorIdsInOtherCase(
						l140m01a_mainId, idContainer);

		String cntrNoStr2 = "";
		for (Map<String, Object> m : cntrNoList) {
			cntrNoStr2 += "'" + String.valueOf(m.get("CNTRNO")) + "',";
		}

		if (!"".equals(cntrNoStr2)) {

			List<String> tmpList = this.misLNF022Service
					.getContractNoWithinValidCreditPeriod(
							cntrNoStr2.substring(0, cntrNoStr2.length() - 1),
							toDate);
			if (!tmpList.isEmpty()) {
				msg += "它案保證人id相同:" + StringUtils.join(tmpList.toArray(), ",");
			}
		}

		if (!"".equals(msg)) {
			codeL = "1";
			itemL.setRemarks(CapString.cutString(msg, "UTF-8", 300));
		}

		itemL.setResult(codeL);
		return itemL;
	}

	private String checkIsApplyDocumentTooComplete(String mainLender_isFullDoc,
			List<L140S01A> guarantorList, String branchNo,
			String l120m01a_mainId) {

		if (ClsUtility.match_L140MC1A_itemCode_K(mainLender_isFullDoc)) {
			return "1";
		}

		for (L140S01A l140s01a : guarantorList) {
			C120M01A c120m01a = c120m01aDao.findByUniqueKey(l120m01a_mainId,
					l140s01a.getCustId(), l140s01a.getDupNo());

			if (null != c120m01a
					&& ClsUtility.match_L140MC1A_itemCode_K(c120m01a
							.getIsFullApplyDocument())) {
				return "1";
			}
		}

		return "0";
	}

	private List<L140MC1A> check_BAM095_LoanData_for_L140MC1A(
			String mainLenderId) {

		List<L140MC1A> rtnList = new ArrayList<L140MC1A>();
		L140MC1A itemN = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.N_04_借款人名下有多筆非自住房貸貸款);
		L140MC1A itemO = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.O_05_借款人於聯徵中心無授信資料);
		String codeN = "0";
		String codeO = "0";

		Map<String, Object> map = this.ejcicService
				.getLatestQueryRecordOfCreditInfoById(mainLenderId);

		if (map == null || map.isEmpty() || map.get("PRODID") == null) {
			codeN = "0";
			codeO = "1";
			itemO.setRemarks("[mis.bai001]查無授信紀錄");
		} else {

			String prodId = String.valueOf(map.get("PRODID"));
			String queryPersonId = String.valueOf(map.get("REQID"));
			String queryPersonBranchNo = String.valueOf(map.get("QBRANCH"));
			String queryDate = String.valueOf(map.get("QDATE"));

			// 04借款人名下有多筆非自住房貸貸款(N)
			int count = this.ejcicService.getHousingLoanCountByBAM095(
					mainLenderId, prodId, queryPersonId, queryPersonBranchNo,
					queryDate);
			codeN = ClsUtility.match_L140MC1A_itemCode_N(count) ? "1" : codeN;

			// 05借款人於聯徵中心無授信資料(O)
			List<Map<String, Object>> list = this.ejcicService.getBAM095Data(
					mainLenderId, prodId, queryPersonId, queryPersonBranchNo,
					queryDate);
			codeO = list.isEmpty() ? "1" : codeO;
		}

		itemN.setResult(codeN);
		itemO.setResult(codeO);
		rtnList.add(itemN);
		rtnList.add(itemO);
		return rtnList;
	}

	private String checkIsRatingUpgraded(String l120m01a_mainId,
			List<L140S02A> l140s02aList) {

		for (L140S02A l140s02a : l140s02aList) {

			String modelKind = l140s02a.getModelKind();
			String adjustStatus = "";
			if (StringUtils.isEmpty(l140s02a.getGrade1())
					|| UtilConstants.L140S02AModelKind.免辦.equals(modelKind)) {// 免辦
				continue; // 若1個額度有2個產品, 其中1個免辦, 另1個產品用的評等有升等 return "0";
			} else if (UtilConstants.L140S02AModelKind.房貸.equals(modelKind)) {

				C120S01G c120s01g = this.cls1131Service.findModelByKey(
						C120S01G.class, l120m01a_mainId, l140s02a.getCustId(),
						l140s02a.getDupNo());
				if (c120s01g != null) {
					adjustStatus = c120s01g.getAdjustStatus();
				}
			} else if (UtilConstants.L140S02AModelKind.非房貸.equals(modelKind)) {

				C120S01Q c120s01q = cls1131Service.findModelByKey(
						C120S01Q.class, l120m01a_mainId, l140s02a.getCustId(),
						l140s02a.getDupNo());
				if (c120s01q != null) {
					adjustStatus = c120s01q.getAdjustStatus();
				}
			} else if (UtilConstants.L140S02AModelKind.卡友貸.equals(modelKind)) {

				C120S01R c120s01r = cls1131Service.findModelByKey(
						C120S01R.class, l120m01a_mainId, l140s02a.getCustId(),
						l140s02a.getDupNo());
				if (c120s01r != null) {
					adjustStatus = c120s01r.getAdjustStatus();
				}
			}

			if (ClsUtility.match_L140MC1A_itemCode_R(adjustStatus)) {
				return "1";
			}
		}

		return "0";
	}

	private String checkEjcicDataQueryTimesMoreThan2ByOtherBank(
			String mainLenderId, List<L140S01A> guarantorList) {
		// 取得3個月前民國年月日
		String date = CapDate.getCurrentDate("yyyyMMdd");
		date = CapDate.addMonth(date, -3);
		date = CapDate.formatDateFromF1ToF2(date, "yyyyMMdd", "YYYMMDD");

		String prodId = this.ejcicService.get_cls_PRODID(mainLenderId);
		int itemCode_P_threshold = ClsUtility
				.get_L140MC1A_itemCode_P_threshold();
		if (this.ejcicService.isEjcicDataQueryTimesMoreThan2ByOtherBank(
				mainLenderId, prodId, date, itemCode_P_threshold)) {
			return "1";
		}

		for (L140S01A l140s01a : guarantorList) {

			prodId = this.ejcicService.get_cls_PRODID(l140s01a.getCustId());
			if (this.ejcicService.isEjcicDataQueryTimesMoreThan2ByOtherBank(
					l140s01a.getCustId(), prodId, date, itemCode_P_threshold)) {
				return "1";
			}
		}

		return "0";
	}

	private String checkBuildingStatus(List<Map<String, String>> buildDataList) {

		if (buildDataList.isEmpty()) {
			return "NA";
		}
		// =======================================================
		// select * from com.bcodetype where codetype='cms1010_pStatus2' and
		// locale='zh_TW'
		// 在 擔保品系統 是以 checkbox 呈現，可以｛允許複選｝
		// 0 前手為一年內取得之法拍屋(詳異動索引)
		// 1 建商餘屋
		// 2 空屋
		// 3 以上皆非

		for (Map<String, String> map : buildDataList) {

			List<Map<String, Object>> list = this.eloandbcmsBASEService
					.getCollateralBuildStatus(map.get("CMS_OID"));
			for (Map<String, Object> m : list) {

				int statusNumber = m.get("PSTATUS2") == null ? 0 : (Integer) m
						.get("PSTATUS2");
				if (statusNumber != 8) {
					return "1";
				}
			}
		}

		return "0";
	}

	private L140MC1A checkKinship(List<L140S01A> guarantorList,
			BigDecimal salary_MainLender, C120M01A c120m01a) {

		L140MC1A C = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.C_08_保證人財力明顯高於借款人且二者無親屬或共同生活關係);

		for (L140S01A l140s01a : guarantorList) {

			C120M01A guarantorC120m01a = this.clsService
					.findC120M01A_mainId_idDup(c120m01a.getMainId(),
							l140s01a.getCustId(), l140s01a.getDupNo());

			if (guarantorC120m01a != null) { // 可能為擔保品提供人 l140s01a.getCustPos =
												// "S" 或由總處額度批覆書加入保證人

				C120S01B g = this.clsService.findC120S01B(guarantorC120m01a);

				if (g != null) {

					BigDecimal salary_guarantor = g.getPayAmt() == null ? BigDecimal.ZERO
							: g.getPayAmt();
					/*
					 * boolean s = salary_guarantor.compareTo(salary_MainLender)
					 * > 0; boolean r = !"2".equals(l140s01a.getRKindM()) ||
					 * ("2".equals(l140s01a.getRKindM()) &&
					 * "XL".equals(l140s01a.getRKindD()) &&
					 * !"Y".equals(l140s01a.getIsLiveWithBorrower())); if(s &&
					 * r){
					 */
					if (ClsUtility.match_L140MC1A_itemCode_C(salary_MainLender,
							salary_guarantor, l140s01a.getRKindM(),
							l140s01a.getRKindD(),
							l140s01a.getIsLiveWithBorrower())) {
						C.setRemarks("保證人" + l140s01a.getCustId());
						C.setResult("1");
						return C;
					}
				}
			}
		}

		C.setResult("0");
		return C;
	}

	private List<C100M01> checkC100M01(String l140m01a_mainId)
			throws CapMessageException {

		List<C100M01> rtnList = new ArrayList<C100M01>();

		List<L140M01O> l140m01oList = this.l140m01oDao
				.findByMainId(l140m01a_mainId);
		for (L140M01O entity : l140m01oList) {

			String buildAddress = entity.getBuild();
			if ("01".equals(entity.getCollTyp1())
					&& !CapString.isEmpty(buildAddress)) {// 為不動產包含(土地及建物)

				String cmsOid = entity.getCmsOid();
				C100M01 c100m01 = c100m01Dao.findByOid(cmsOid);

				if (c100m01 == null) {
					throw new CapMessageException(
							"簽報書所引進的擔保品，於擔保品系統不存在，請重新引進擔保品。", getClass());
				}

				rtnList.add(c100m01);
			}
		}

		return rtnList;
	}

	private List<Map<String, String>> process_L140M01O_AllBuildingData(
			List<C100M01> c100m01List) throws CapMessageException {

		List<Map<String, String>> rtnList = new ArrayList<Map<String, String>>();

		for (C100M01 c100m01 : c100m01List) {
			List<Map<String, Object>> buildList = this.eloandbcmsBASEService
					.getBuildingByMainId(c100m01.getMainId());
			Map<String, Object> buildMap = buildList.get(0);// 只取第一筆做比較
			String collateralCity = String.valueOf(buildMap.get("TARGET"))
					.substring(0, 3);

			Map<String, String> map = new HashMap<String, String>();
			map.put("COLLATERAL_CITY", collateralCity);
			map.put("CMS_OID", c100m01.getOid());
			rtnList.add(map);
		}

		return rtnList;
	}

	private Set<String> getAllCollateralOwnerInfo(List<C100M01> c100m01List) {

		Set<String> collateralOwnerIdSet = new HashSet<String>();

		for (C100M01 c100m01 : c100m01List) {
			List<Map<String, Object>> ownerInfoList = this.eloandbcmsBASEService
					.getOwnerByMainId(c100m01.getMainId());
			for (Map<String, Object> m : ownerInfoList) {
				collateralOwnerIdSet.add(String.valueOf(m.get("OWNERID")));
			}
		}

		return collateralOwnerIdSet;
	}

	private String checkGeoRelations(C120M01A c120m01a, C120S01B mainLender,
			String branchNo, List<Map<String, String>> buildDataList) {

		int count = 3;
		Map<String, String> cityMap = codeService.findByCodeType("counties");
		C120S01A c120s01a = c120s01aDao.findByUniqueKey(c120m01a.getMainId(),
				c120m01a.getCustId(), c120m01a.getDupNo());

		String residenceCity = cityMap.get(c120s01a.getFCity()); // 戶籍地址
		String communicationCity = cityMap.get(c120s01a.getCoCity()); // 通訊地址
		String branchCity = this.branchService.getBranch(branchNo).getAddr()
				.substring(0, 3);
		String serviceCity = cityMap.get(mainLender.getComCity()); // 服務單位地址

		boolean isAdd = false;
		List<String> cityList = new ArrayList<String>();
		cityList.add(residenceCity);
		cityList.add(communicationCity);
		cityList.add(branchCity);
		if (StringUtils.isNotEmpty(serviceCity)) {
			cityList.add(serviceCity);
			count++;
			isAdd = true;
		}

		// 非房貸項目 - 無擔保品, 不列入計算
		if (buildDataList.isEmpty()) {
			return new HashSet<String>(cityList).size() == count ? "1" : "0";
		}

		count++;

		// 房貸項目 - 擔保品需列入計算
		for (Map<String, String> map : buildDataList) {
			String collateralCity = map.get("COLLATERAL_CITY");
			Set<String> citySet = new HashSet<String>();
			citySet.add(residenceCity);
			citySet.add(communicationCity);
			citySet.add(branchCity);
			citySet.add(collateralCity);
			if (isAdd) {
				citySet.add(serviceCity);
			}

			if (citySet.size() == count) {
				return "1";
			}
		}

		return "0";
	}

	private L140MC1A checkContactInfoIsInOtherCaseByCheckingLenderOrGuarantor(
			L140M01A l140m01a, String toDate, List<L140S01A> guarantorList,
			C120S01A mainLender_c120s01a, String l120m01a_mainId) {

		List<C120S01A> mainLender_and_guarantor_List = new ArrayList<C120S01A>();
		mainLender_and_guarantor_List.add(mainLender_c120s01a);

		L140MC1A M = new L140MC1A(
				UtilConstants.L140MC1A_itemCode.M_14_不同借款人案下保證人_擔保品提供人或聯絡資料相同);
		String cntrNoStr = "";
		String errorMsg = "";
		String codeM = "0";

		for (L140S01A guarantor : guarantorList) {
			C120S01A c120s01a = this.c120s01aDao.findByUniqueKey(
					l120m01a_mainId, guarantor.getCustId(),
					guarantor.getDupNo());
			if (c120s01a == null) {
				continue; // 在「額度批覆表」營運中心可直接增加「從債務人」
			}
			mainLender_and_guarantor_List.add(c120s01a);
		}

		// 檢查本案主借人、保證人的聯絡資料〔通訊電話、戶籍電話、行動電話、戶籍地址、通訊地址〕，與他案借款人、保證人一樣
		List<String> cntrnoList = this
				.processCntrnoInfoSameWithLenderOrGuarantorData(
						mainLender_and_guarantor_List, l120m01a_mainId);

		for (String cntrNo : cntrnoList) {
			cntrNoStr += "'" + cntrNo + "',";
		}

		if (!"".equals(cntrNoStr)) {

			List<String> tmpList = this.misLNF022Service
					.getContractNoWithinValidCreditPeriod(
							cntrNoStr.substring(0, cntrNoStr.length() - 1),
							toDate);
			if (!tmpList.isEmpty()) {
				codeM = "1";
				errorMsg += "它案借款人或保證人有相同聯絡資訊:"
						+ StringUtils.join(tmpList.toArray(), ",");
			}
		}

		M.setResult(codeM);
		M.setRemarks(CapString.cutString(errorMsg, "UTF-8", 300));
		return M;
	}

	private List<String> processCntrnoInfoSameWithLenderOrGuarantorData(
			List<C120S01A> allList, String l120m01a_mainId) {

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		List<String> cntrnoList = new ArrayList<String>();

		// 借款人或保證人 聯絡資訊 存在於其他簽報書中
		for (C120S01A c120s01a : allList) {
			dataList.addAll(this.eloandbBASEService
					.getC120s01aDataSameWithLenderOrGuarantorInfo(
							l120m01a_mainId, c120s01a.getCustId(),
							c120s01a.getCoTel(), c120s01a.getFTel(),
							c120s01a.getMTel(), c120s01a.getFTarget(),
							c120s01a.getCoTarget()));
		}

		dataList = dataList.size() >= 20 ? dataList.subList(0, 19) : dataList;

		for (Map<String, Object> m : dataList) {

			String mainId = String.valueOf(m.get("MAINID"));
			List<L140M01A> list = this.l140m01aDao
					.findL140m01aListByL120m01cMainIdAndProperty(mainId, null,
							null);

			for (L140M01A entity : list) {
				cntrnoList.add(entity.getCntrNo());
			}
		}

		return cntrnoList;
	}

	@Override
	public Map<String, Object> getDocFileForDeleteBorrower(
			String c120m01a_custId, String c120m01a_dupNo,
			String c120m01a_ownBrId) {
		C101M01A c101m01a = this.clsService.getC101M01AByOid(c120m01a_custId,
				c120m01a_dupNo, c120m01a_ownBrId);
		if (c101m01a == null) {
			return null;
		}
		return this.eloandbBASEService.findDocFileByMainIdAndBranchId(
				c101m01a.getMainId(), c120m01a_ownBrId);
	}

	@Override
	public JSONObject prepare_gen_caseDoc_tabDoc(String brNo, String custId,
			String dupNo, String prodKind) {
		return null;
	}

	@Override
	public List<C122M01A> findC122M01A_by_brNo_custId_applyKind_orderByApplyTSDesc(
			String ownBrId, String custId, String applyKind,
			String createTimeSince) {
		return c122m01aDao.findBy_brNo_custId_applyKind_orderByApplyTSDesc(
				ownBrId, custId, applyKind, createTimeSince);
	}

	@Override
	public String ha_factor_L(String mainLenderId, List<String> guarantorList) {
		return checkIdIsInOtherCaseForSimpleCredit(mainLenderId, guarantorList);
	}

	@Override
	public String ha_factor_M(List<C101S01A> mainLender_and_guarantor_List) {
		return checkContactInfoIsInOtherCaseForSimpleCredit(mainLender_and_guarantor_List);
	}

	@Override
	public String ha_factor_B(C101M01A c101m01a, C101S01A c101s01a,
			C101S01B mainLender, String branchNo) {
		return checkGeoRelationsForSimpleCredit(c101m01a, c101s01a, mainLender,
				branchNo);
	}

	@Override
	public String ha_factor_3(List<C101S01B> mainLender_and_guarantor_List) {
		return this.checkLastThreeDigitsAreZero_ForPaperlessSignBook(mainLender_and_guarantor_List);
	}

	@Override
	public void check_l140m01a_loanTotAmt_3000WAN(String mainId,
			String caseType, CapAjaxFormResult result) {

		// 當為額度明細表時才需要這個
		if (UtilConstants.Cntrdoc.ItemType.額度明細表.equals(caseType)) {
			BigDecimal 三千萬 = new BigDecimal("30000000");
			L120M01A l120m01a = findL120m01aByMainId(mainId);
			if (!LMSUtil.isParentCase(l120m01a)) {
				List<L140M01A> l140m01as = cls1151Service
						.findL140m01aListByL120m01cMainIdForPrint(mainId,
								caseType);
				boolean checkBy3000 = false;
				boolean checkCustPos = false;
				String LongCaseDscr = null;
				for (L140M01A l140m01a : l140m01as) {
					BigDecimal total = Util.parseBigDecimal(l140m01a
							.getLoanTotAmt());
					if (三千萬.compareTo(total) != 1) {
						checkBy3000 = true;
					}
					List<L140S01A> l140s01as = clsService
							.findL140S01A(l140m01a);
					if (!l140s01as.isEmpty()) {
						for (L140S01A l140s01a : l140s01as) {
							if (lngeFlag.ㄧ般保證人.equals(l140s01a.getCustPos())
									|| lngeFlag.連帶保證人.equals(l140s01a
											.getCustPos())) {
								checkCustPos = true;
							}
						}
					}

				}
				if (l120m01a != null) {
					if (checkBy3000) {
						// 是否已至資料建檔建置同一關係人表 longCaseDscr ，帶入預設值
						LongCaseDscr = UtilConstants.DEFAULT.否;
					} else {
						// 當沒有借款人金額大於三千萬則不需，填入此欄位
						LongCaseDscr = null;
					}

					l120m01a.setLongCaseDscr(LongCaseDscr);
					save(l120m01a);
				}
				result.set("checkBy3000", checkBy3000);
				result.set("checkCustPos", checkCustPos);
			}
		}
	}

	@Override
	public Map<String, Object> modifCaseType(L120M01A l120m01a, String docCode,
			String authLvl, String ngFlag, String areaBrid) {
		Map<String, Object> map = new HashMap<String, Object>();
		boolean resetL140M01A = false;
		String caseLvl = "";
		String areaChk = "";
		if (l120m01a != null) {
			l120m01a.setNgFlag(ngFlag);
			l120m01a.setDocCode(docCode);
			l120m01a.setAuthLvl(authLvl); // {1:分行授權內, 2:母行授權內, 3:區域營運中心授權內,
											// 5:分行授權外} select * from
											// com.bcodetype where
											// codetype='cls1141_authLvl'

			if (UtilConstants.Casedoc.DocCode.團貸案件.equals(docCode)) {
				l120m01a.setPackLoan(UtilConstants.DEFAULT.否);
			}

			if (UtilConstants.Casedoc.AuthLvl.分行授權外.equals(authLvl)) {
				l120m01a.setDocKind(UtilConstants.Casedoc.DocKind.授權外);
				MegaSSOUserDetails user = MegaSSOSecurityContext
						.getUserDetails();

				if (LMSUtil.isSpecialBranch(user.getUnitNo())) {
					areaChk = UtilConstants.Casedoc.AreaChk.無;
				} else {
					areaChk = UtilConstants.Casedoc.AreaChk.送審查;
				}
				resetL140M01A = true;
			} else {
				l120m01a.setDocKind(UtilConstants.Casedoc.DocKind.授權內);
				if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(authLvl)) {

					Map<String, String> codeMap = codeService
							.findByCodeType("lms1205m01_caseLvl");

					l120m01a.setCaseLvl(UtilConstants.Casedoc.CaseLvl.其他);
					caseLvl = StrUtils.concat(UtilConstants.Casedoc.CaseLvl.其他,
							"-", codeMap.get(UtilConstants.Casedoc.CaseLvl.其他));
				} else {
					l120m01a.setCaseLvl("");
				}
			}

			if (UtilConstants.Casedoc.AuthLvl.營運中心授權內.equals(authLvl)) {
				resetL140M01A = true;
				l120m01a.setAreaBrId(areaBrid);
			} else {
				l120m01a.setAreaBrId(null);
			}

			l120m01a.setAreaChk(areaChk);
			save(l120m01a);
		}

		map.put("caseLvl", caseLvl); // caseLvl 的可能格式"9-其他(經理)"
		map.put("resetL140M01A", resetL140M01A);
		return map;
	}

	// factor M
	private String checkContactInfoIsInOtherCaseForSimpleCredit(
			List<C101S01A> mainLender_and_guarantor_List) {

		// 借款人或保證人 聯絡資訊 存在於其他簽報書中
		for (C101S01A c101s01a : mainLender_and_guarantor_List) {
			List<Map<String, Object>> list = this.eloandbBASEService
					.getC101s01aDataSameWithLenderOrGuarantorInfo(
							c101s01a.getMainId(), c101s01a.getCustId(),
							c101s01a.getCoTel(), c101s01a.getFTel(),
							c101s01a.getMTel(), c101s01a.getFTarget(),
							c101s01a.getCoTarget());
			if (!list.isEmpty()) {
				return "1";
			}
		}

		return "0";
	}

	// factor B
	private String checkGeoRelationsForSimpleCredit(C101M01A c101m01a,
			C101S01A c101s01a, C101S01B mainLender, String branchNo) {

		int count = 3;
		Map<String, String> cityMap = codeService.findByCodeType("counties");

		String residenceCity = cityMap.get(c101s01a.getFCity()); // 戶籍地址
		String communicationCity = cityMap.get(c101s01a.getCoCity()); // 通訊地址
		String branchCity = this.branchService.getBranch(branchNo).getAddr()
				.substring(0, 3);
		String serviceCity = cityMap.get(mainLender.getComCity()); // 服務單位地址

		List<String> cityList = new ArrayList<String>();
		cityList.add(residenceCity);
		cityList.add(communicationCity);
		cityList.add(branchCity);
		if (StringUtils.isNotEmpty(serviceCity)) {
			cityList.add(serviceCity);
			count++;
		}

		return new HashSet<String>(cityList).size() == count ? "1" : "0";
	}

	// factor L
	private String checkIdIsInOtherCaseForSimpleCredit(String mainLenderId,
			List<String> guarantorList) {

		String toDate = CapDate.getCurrentDate("yyyy-MM-dd");
		List<String> idContainer = new ArrayList<String>();
		List<Map<String, Object>> allCntrNoList = new ArrayList<Map<String, Object>>();

		for (String guarantor : guarantorList) {
			idContainer.add(guarantor);
		}

		// 檢查 保證人 id, 是否在別的案件擔保品所有權人id相同
		List<Map<String, Object>> cntrNoList1 = this.eloandbcmsBASEService
				.getCntrnoOfSameAsCollateralOwnerIdInfo(idContainer);
		allCntrNoList.addAll(cntrNoList1);

		idContainer.add(mainLenderId);

		// 檢查本案 主借人、保證人及擔保品所有權人id, 是與別的案件保證人id相同
		List<Map<String, Object>> cntrNoList2 = this.eloandbBASEService
				.getCntrnoInfoOfSameAsGuarantorIdCase(idContainer);
		allCntrNoList.addAll(cntrNoList2);

		String cntrNoStr = "";
		for (Map<String, Object> m : allCntrNoList) {
			cntrNoStr += "'" + String.valueOf(m.get("CNTRNO")) + "',";
		}

		if (!"".equals(cntrNoStr)) {

			List<String> tmpList = this.misLNF022Service
					.getContractNoWithinValidCreditPeriod(
							cntrNoStr.substring(0, cntrNoStr.length() - 1),
							toDate);
			if (!tmpList.isEmpty()) {
				return "1";
			}
		}

		return "0";
	}

	private String checkLastThreeDigitsAreZero_ForGeneralSignBookFlow(
			C120S01B mainLender, List<L140S01A> guarantorList,
			String c120m01a_mainId) {

		List<C120S01B> c120s01bList = new ArrayList<C120S01B>();
		c120s01bList.add(mainLender);

		for (L140S01A l140s01a : guarantorList) {
			C120S01B guarantor = this.c120s01bDao.findByUniqueKey(
					c120m01a_mainId, l140s01a.getCustId(), l140s01a.getDupNo());
			if (guarantor == null) {
				continue; // 在「額度批覆表」營運中心可直接增加「從債務人」
			}
			c120s01bList.add(guarantor);
		}

		for (C120S01B c120s01b : c120s01bList) {

			BigDecimal incomeDetailVer = c120s01b.getIncomeDetailVer();
			if (incomeDetailVer == null || incomeDetailVer.intValue() == 0) {
				String mainIncomeType = c120s01b.getMainIncomeType();

				BigDecimal total = BigDecimal.ZERO;

				if ("A".equals(mainIncomeType)) {
					total = LMSUtil.nullToZeroBigDecimal(c120s01b
							.getItemAvalue());
				}

				if ("B1".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c120s01b.getItemB1value1())
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB1value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB1value3()));
				}

				if ("B3".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c120s01b.getItemB3value1())
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB3value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB3value3()));
				}

				if ("B4".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c120s01b.getItemB4value1())
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB4value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB4value3()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB4value4()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB4value5()))
							.add(LMSUtil.nullToZeroBigDecimal(c120s01b
									.getItemB4value6()));
				}

				if (total.compareTo(BigDecimal.ZERO) == 0) {

				} else {
					BigDecimal remainder = total
							.remainder(new BigDecimal(1000));
					if (remainder.compareTo(BigDecimal.ZERO) == 0) {
						return "1";
					}
				}

			} 
			else if (incomeDetailVer.intValue() == 1) {
				
				// 新版的消金收入個人明細變為複選，問過消金處，說只要其中一個收入來源為整數，就回傳
				List<C120S01W> c120s01ws = clsService.findC120S01W(
						c120s01b.getMainId(), c120s01b.getCustId(),
						c120s01b.getDupNo());
				if (CollectionUtils.isNotEmpty(c120s01ws)) {

					Map<String, String> maps = new HashMap<String, String>();
					for (C120S01W c120s01w : c120s01ws) {
						maps.put(c120s01w.getKeyString(),
								c120s01w.getValueString());
					}
					BigDecimal total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemA", ""))) {
						total = LMSUtil.nullToZeroBigDecimal(MapUtils
								.getString(maps, "itemAvalue", ""));
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemB", ""))) {
						for (int i = 1; i <= 12; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemBvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemC", ""))) {
						for (int i = 1; i <= 6; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemCvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemD", ""))) {
						for (int i = 1; i <= 12; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemDvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
				}
			}
			else{
				
				List<C120S01W> c120s01ws = this.clsService.findC120S01WBy(c120s01b.getMainId(), c120s01b.getCustId(), c120s01b.getDupNo(), CHECKED_INCOME_ITEM_IN_VER2);
				
				for (C120S01W c120s01w : c120s01ws) {

					BigDecimal total = LMSUtil.nullToZeroBigDecimal(c120s01w.getValue01())
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue02()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue03()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue04()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue05()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue06()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue07()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue08()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue09()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue10()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue11()))
										.add(LMSUtil.nullToZeroBigDecimal(c120s01w.getValue12()));
					
					if (total.compareTo(BigDecimal.ZERO) > 0) {
						
						BigDecimal remainder = total.remainder(new BigDecimal(1000));
						if (remainder.compareTo(BigDecimal.ZERO) == 0) {
							return "1";
						}
					}
				}
				
				return "0";
			}
		
		}

		return "0";
	}

	private String checkLastThreeDigitsAreZero_ForPaperlessSignBook(List<C101S01B> c101s01bList) {

		for (C101S01B c101s01b : c101s01bList) {

			BigDecimal incomeDetailVer = c101s01b.getIncomeDetailVer();
			if (incomeDetailVer == null || incomeDetailVer.intValue() == 0) {
				String mainIncomeType = c101s01b.getMainIncomeType();

				BigDecimal total = BigDecimal.ZERO;

				if ("A".equals(mainIncomeType)) {
					total = LMSUtil.nullToZeroBigDecimal(c101s01b
							.getItemAvalue());
				}

				if ("B1".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c101s01b.getItemB1value1())
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB1value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB1value3()));
				}

				if ("B3".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c101s01b.getItemB3value1())
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB3value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB3value3()));
				}

				if ("B4".equals(mainIncomeType)) {
					total = LMSUtil
							.nullToZeroBigDecimal(c101s01b.getItemB4value1())
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB4value2()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB4value3()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB4value4()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB4value5()))
							.add(LMSUtil.nullToZeroBigDecimal(c101s01b
									.getItemB4value6()));
				}

				if (total.compareTo(BigDecimal.ZERO) == 0) {

				} else {
					BigDecimal remainder = total
							.remainder(new BigDecimal(1000));
					if (remainder.compareTo(BigDecimal.ZERO) == 0) {
						return "1";
					}
				}

			} 
			else if (incomeDetailVer.intValue() == 1) {
				
				// 新版的消金收入個人明細變為複選，問過消金處，說只要其中一個收入來源為整數，就回傳
				List<C101S01W> c101s01ws = clsService.findC101S01W(
						c101s01b.getMainId(), c101s01b.getCustId(),
						c101s01b.getDupNo());
				if (CollectionUtils.isNotEmpty(c101s01ws)) {

					Map<String, String> maps = new HashMap<String, String>();
					for (C101S01W c101s01w : c101s01ws) {
						maps.put(c101s01w.getKeyString(),
								c101s01w.getValueString());
					}
					BigDecimal total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemA", ""))) {
						total = LMSUtil.nullToZeroBigDecimal(MapUtils
								.getString(maps, "itemAvalue", ""));
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemB", ""))) {
						for (int i = 1; i <= 12; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemBvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemC", ""))) {
						for (int i = 1; i <= 6; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemCvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
					total = BigDecimal.ZERO;
					if ("Y".equals(MapUtils.getString(maps, "hasItemD", ""))) {
						for (int i = 1; i <= 12; i++) {
							total = total.add(LMSUtil
									.nullToZeroBigDecimal(MapUtils.getString(
											maps, "itemDvalue" + i, "")));
						}
						if (total.compareTo(BigDecimal.ZERO) != 0) {
							BigDecimal remainder = total
									.remainder(new BigDecimal(1000));
							if (remainder.compareTo(BigDecimal.ZERO) == 0) {
								return "1";
							}
						}
					}
				}
			}
			else{
				
				List<C101S01W> c101s01ws = this.clsService.findC101S01WBy(c101s01b.getMainId(), c101s01b.getCustId(), c101s01b.getDupNo(), CHECKED_INCOME_ITEM_IN_VER2);
				
				for (C101S01W c101s01w : c101s01ws) {

					BigDecimal total = LMSUtil.nullToZeroBigDecimal(c101s01w.getValue01())
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue02()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue03()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue04()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue05()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue06()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue07()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue08()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue09()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue10()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue11()))
										.add(LMSUtil.nullToZeroBigDecimal(c101s01w.getValue12()));
					
					if (total.compareTo(BigDecimal.ZERO) > 0) {
						
						BigDecimal remainder = total.remainder(new BigDecimal(1000));
						if (remainder.compareTo(BigDecimal.ZERO) == 0) {
							return "1";
						}
					}
				}
				
				return "0";
			}
		
		}

		return "0";
	}

	@Override
	public List<C127M01A> saveClsAreaPriceExcel(
			HSSFSheet sheet, String mainId)
			throws CapMessageException {
		List<C127M01A> lists = new ArrayList<C127M01A>();
		// UPGRADE: 待確認Excel取值是否正確
		DataFormatter dataFormatter = new DataFormatter();
		FormulaEvaluator formulaEvaluator = sheet.getWorkbook()
				.getCreationHelper().createFormulaEvaluator();
		for (int row = 1; row < sheet.getLastRowNum(); row++) {
			C127M01A c127m01a = new C127M01A();
			int column = 0;
			if (Util.isEmpty(Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(1), formulaEvaluator)))) {
				break;
			}
			String area = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String brNo = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String brName = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String groupId = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String northPrice = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String middlePrice = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String southPrice = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));
			String memo = Util.trim(dataFormatter.formatCellValue(
					sheet.getRow(row).getCell(column++), formulaEvaluator));

			c127m01a.setMainId(mainId);
			c127m01a.setArea(area);
			c127m01a.setOwnBrId(brNo);
			c127m01a.setOwnBrName(brName);
			c127m01a.setGroupId(groupId);
			c127m01a.setNorthPrice(Util.parseBigDecimal(northPrice));
			c127m01a.setMiddlePrice(Util.parseBigDecimal(middlePrice));
			c127m01a.setSouthPrice(Util.parseBigDecimal(southPrice));
			c127m01a.setCreateTime(CapDate.getCurrentTimestamp());
			c127m01a.setCreator("system");
			c127m01a.setMemo(memo);
			lists.add(c127m01a);
		}
		try {
			List<C127M01A> oldList = c127m01adao.findAll();
			if (oldList.size() > 0) {
				c127m01adao.delete(oldList);
			}
			c127m01adao.save(lists);
		} catch (Exception e) {
			logger.error("save c127m01a EXCEPTION!!", e);
			throw new CapMessageException(e, getClass());
		}
		return lists;
	}

	@Override
	public String checkClsAreaPrice(L120M01A l120m01a) {
		if (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(l120m01a.getAuthLvl())) {
			boolean needCheck = false;
			BigDecimal areaPrice = BigDecimal.ZERO;
			BigDecimal totalCurrentApplyAmt = BigDecimal.ZERO;
			for (L140M01A l140m01a : clsService
					.findL140M01A_l120m01aMainId(l120m01a.getMainId())) {
				String proPerty = Util.trim(l140m01a.getProPerty());
				if (UtilConstants.Cntrdoc.Property.不變.equals(proPerty)
						|| UtilConstants.Cntrdoc.Property.取消.equals(proPerty)) {
					if (UtilConstants.Cntrdoc.Property.不變.equals(proPerty)) {
						totalCurrentApplyAmt = totalCurrentApplyAmt
								.add(l140m01a.getCurrentApplyAmt().divide(
										new BigDecimal(10000)));
					}
					continue;
				} else {
					C127M01A c127m01a = c127m01adao.findByOwnBrId(l120m01a
							.getOwnBrId());
					BigDecimal neaAreaPrice = BigDecimal.ZERO;
					List<L140M01O> l140m01os = clsService.findL140M01O(l140m01a
							.getMainId());
					String area = "";
					if (l140m01os.size() > 0 && Util.isNotEmpty(c127m01a)) {
						for (L140M01O l140m01o : l140m01os) {
							if (Util.equals(UtilConstants.CollTyp1.不動產,
									l140m01o.getCollTyp1())) {
								needCheck = true;
								String taxAddr_City = Util.isNotEmpty(Util
										.trim(l140m01o.getTaxAddr())) ? Util
										.getLeftStr(Util.trim(l140m01o
												.getTaxAddr()), 3) : Util
										.trim(l140m01o.getAreaDetail());
								List<CodeType> codeTypeList = codeService
										.findByCodeTypeList("clsAreaPriceByCity");
								for (CodeType codeType : codeTypeList) {
									for (String city : codeType.getCodeDesc()
											.split("、")) {
										if (StringUtils.contains(taxAddr_City,
												city)) {
											area = codeType.getCodeValue();
											if (Util.equals(area, "N")) {
												neaAreaPrice = c127m01a
														.getNorthPrice();
											} else if (Util.equals(area, "M")) {
												neaAreaPrice = c127m01a
														.getMiddlePrice();
											} else if (Util.equals(area, "S")) {
												neaAreaPrice = c127m01a
														.getSouthPrice();
											}
											if (Util.equals(areaPrice,
													BigDecimal.ZERO)) {
												areaPrice = neaAreaPrice;
											} else if (neaAreaPrice
													.compareTo(areaPrice) == -1) {
												areaPrice = neaAreaPrice;
											}
										}
									}
								}
							}
						}
					}
					totalCurrentApplyAmt = totalCurrentApplyAmt
							.add(l140m01a.getCurrentApplyAmt().divide(
									new BigDecimal(10000)));
				}
			}
			if (needCheck && areaPrice.compareTo(BigDecimal.ZERO) > 0
					&& totalCurrentApplyAmt.compareTo(areaPrice) > 0) {
				return "「本案疑似逾分行授權，請注意確認一下是否有逾授審處訂定之分組授權！」";
			}
		}
		return null;
	}

	@Override
	public String formatContentString(String type, String curr, String content) {

		if (UtilConstants.PaperlessActingType.QUOTA.equals(type)) {
			return curr + " " + NumConverter.addComma(content) + " 元";
		}

		if (UtilConstants.PaperlessActingType.TERM.equals(type)) {
			String year = content.split("-")[0];
			String month = content.split("-")[1];
			return year + "年 " + month + "月";
		}

		if (UtilConstants.PaperlessActingType.FEE.equals(type)) {
			Map<String, String> feeNoMap = this.codeService
					.findByCodeType("paperless_feeNo");
			String code = content.split("-")[0];
			String amount = content.split("-")[1];
			return feeNoMap.get(code) + " " + curr
					+ NumConverter.addComma(amount) + " 元";
		}

		return content;
	}

	@Override
	public Map<String, String> formatDisplayDisplayStringForL120S19C(
			String mainId, String role) {

		Map<String, String> feeNameMap = this.codeService
				.findByCodeType("paperless_feeNo");
		String contentStr = "";
		String remarkStr = "";

		List<L120S19C> l120s19cList = this.clsService.findL120S19CByMainIdRole(
				mainId, role);

		if (l120s19cList.isEmpty()) {
			return new HashMap<String, String>();
		}

		Brmp002O brmpOutput;
		Map<String, BigDecimal> feeMap = null;
		try {
			brmpOutput = (Brmp002O) this.clsService.getBrmpResultParameter(
					mainId).get("BRMP_OUTPUT_DATA");
			feeMap = this.clsService.getSystemComputedFee(brmpOutput);
		} catch (CapException e) {
			feeMap = new HashMap<String, BigDecimal>();
		}

		for (L120S19C entity : l120s19cList) {
			feeMap.put(entity.getFeeNo(), entity.getFeeAmt());
			remarkStr += entity.getRemark() + " / ";
		}

		for (String feeNo : feeMap.keySet()) {
			contentStr += feeNameMap.get(feeNo) + " " + "TWD"
					+ NumConverter.addComma(feeMap.get(feeNo)) + " 元 / ";
		}

		contentStr = !"".equals(contentStr) ? contentStr.substring(0,
				contentStr.length() - 3) : contentStr;
		remarkStr = !"".equals(remarkStr) ? remarkStr.substring(0,
				remarkStr.length() - 2) : remarkStr;
		Map<String, String> map = new HashMap<String, String>();
		map.put("CONTENT", contentStr);
		map.put("REMARK", remarkStr);
		return map;
	}

	@Override
	public void createCreditConditionLog(String l120m01a_mainId,
			String content, String type, String remark) throws Exception {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String userId = user.getUserId();
		Timestamp currentDateTime = CapDate.getCurrentTimestamp();
		String role = this.clsService
				.getActingRoleForPaperlessSigning(l120m01a_mainId);

		L120S19A l120s19a = this.l120s19aDao
				.findByMainId_itemType_latest_itemVersion(l120m01a_mainId, "B");
		l120s19a.setUpdater(userId);
		l120s19a.setUpdateTime(currentDateTime);
		String l140m01a_mainId = l120s19a.getRef140MainId();
		L140S02A l140s02a = this.l140s02aDao
				.findByUniqueKey(l140m01a_mainId, 1);

		// 異動貸款額度
		if (UtilConstants.PaperlessActingType.QUOTA.equals(type)) {
			L140M01A l140m01a = this.l140m01aDao.findByMainId(l140m01a_mainId);
			this.processLoanQuotaModifying(l140m01a, l140s02a, l120s19a,
					content, userId, role, remark, currentDateTime);

			try {
				cls1151Service.findL140m01Count(l120m01a_mainId, "1"); // 重新計算[額度合計]
			} catch (Exception e) {
				// J-111-0343_05097_B1004 Web e-Loan修改消金額度明細表合計之功能
				throw new CapMessageException(e.toString(), getClass());
			}

			this.l140m01aDao.save(l140m01a);
			this.l140s02aDao.save(l140s02a);
			this.l120s19aDao.save(l120s19a);
		}
		// 異動貸款期間
		if (UtilConstants.PaperlessActingType.TERM.equals(type)) {
			this.processLoanRateModifying(l140s02a, l120s19a, content, userId,
					role, remark);
			this.l140s02aDao.save(l140s02a);
			this.l120s19aDao.save(l120s19a);
		}

		// 異動貸款費用
		if (UtilConstants.PaperlessActingType.FEE.equals(type)) {
			L120S19C l120s19c = new L120S19C();
			l120s19c.setMainId(l120m01a_mainId);
			L140M01R l140m01r = new L140M01R();
			l140m01r.setMainId(l120m01a_mainId);
			this.processLoanFeeModifying(l140m01r, l120s19c, l120m01a_mainId,
					content, userId, role, remark, currentDateTime);
			this.saveL140M01R(l140m01r);
			this.l120s19cDao.save(l120s19c);
		}

		L120S19B entity = new L120S19B();
		entity.setMainId(l120m01a_mainId);
		entity.setType(type);
		entity.setContent(this.formatContentString(type, "TWD", content));
		entity.setRemark(remark);
		entity.setRole(role);
		entity.setCreator(userId);
		entity.setCreateTime(currentDateTime);
		this.l120s19bDao.save(entity);
	}

	private void processLoanFeeModifying(L140M01R l140m01r, L120S19C l120s19c,
			String l120m01a_mainId, String content, String userId, String role,
			String remark, Timestamp currentDateTime) {
		String code = content.split("-")[0];
		BigDecimal amount = new BigDecimal(content.split("-")[1]);

		L120M01A l120m01a = this.findL120m01aByMainId(l120m01a_mainId);
		String tCaseNo = Util.toSemiCharString(l120m01a.getCaseNo());
		Integer tCaseYear = l120m01a.getCaseYear();
		String tCaseBrId = l120m01a.getCaseBrId();
		Integer tCaseSeq = l120m01a.getCaseSeq();
		l140m01r.setCaseNo(tCaseNo);
		l140m01r.setCaseYear(tCaseYear);
		l140m01r.setCaseBrId(tCaseBrId);
		l140m01r.setCaseSeq(tCaseSeq);
		l140m01r.setFeeNo(code);
		l140m01r.setFeeSwft("TWD");
		l140m01r.setFeeAmt(amount);
		l140m01r.setFeeMemo(remark);
		l140m01r.setFeeSrc("0");
		l140m01r.setCreator(userId);
		l140m01r.setCreateTime(currentDateTime);
		l140m01r.setUpdater(userId);
		l140m01r.setUpdateTime(currentDateTime);

		l120s19c.setFeeNo(code);
		l120s19c.setFeeCurr("TWD");
		l120s19c.setFeeAmt(amount);
		l120s19c.setRemark(remark);
		l120s19c.setRole(role);
		l120s19c.setCreator(userId);
		l120s19c.setCreateTime(currentDateTime);

		// 刪除決策平台計算產生的費用
		this.clsService.deleteL140M01R(this.l140m01rDao
				.findByMainIdFeeSrcFeeNo(l120m01a_mainId, "0", code));
		// 刪除簡化簽報書前一次新增費用
		this.clsService.deleteL140M01R(this.l140m01rDao
				.findByMainIdFeeSrcFeeNo(l120m01a_mainId, "4", code));
		this.clsService.deleteL120S19C(this.l120s19cDao.findByMainIdFeeNoRole(
				l120m01a_mainId, code, role));
	}

	private void processLoanRateModifying(L140S02A l140s02a, L120S19A l120s19a,
			String content, String userId, String role, String remark) {
		int year = Integer.valueOf(content.split("-")[0]);
		int month = Integer.valueOf(content.split("-")[1]);
		l140s02a.setLnYear(year);
		l140s02a.setLnMonth(month);

		if (UtilConstants.PaperlessActingRole.AO.equals(role)) {
			l120s19a.setAoUpdYear(year);
			l120s19a.setAoUpdMonth(month);
			l120s19a.setAoUpdPeriodRemark(remark);
		} else {
			l120s19a.setRvUpdYear(year);
			l120s19a.setRvUpdMonth(month);
			l120s19a.setRvUpdPeriodRemark(remark);
		}
	}

	private void processLoanQuotaModifying(L140M01A l140m01a,
			L140S02A l140s02a, L120S19A l120s19a, String content,
			String userId, String role, String remark, Timestamp currentDateTime) {
		BigDecimal loanQuota = new BigDecimal(content);
		l140m01a.setCurrentApplyCurr("TWD");
		l140m01a.setCurrentApplyAmt(loanQuota);
		l140m01a.setUpdater(userId);
		l140m01a.setUpdateTime(currentDateTime);
		l140s02a.setDBR22Amt(loanQuota);// 本額度應計入DBR22倍金額
		l140s02a.setLoanAmt(loanQuota);// 分項金額
		l140s02a.setUpdater(userId);
		l140s02a.setUpdateTime(currentDateTime);

		if (UtilConstants.PaperlessActingRole.AO.equals(role)) {
			l120s19a.setAoUpdAmountCurr("TWD");
			l120s19a.setAoUpdAmount(loanQuota);
			l120s19a.setAoUpdAmountRemark(remark);
		} else {
			l120s19a.setRvUpdAmountCurr("TWD");
			l120s19a.setRvUpdAmount(loanQuota);
			l120s19a.setRvUpdAmountRemark(remark);
		}
	}

	/**
	 * 無紙化簽報，變更額度明細表chkYN = Y >> 擬核定
	 * 
	 * @param mainId
	 * @throws CapException
	 */
	@Override
	public void chgL140m01aDocStatus(String mainId) throws CapException {
		L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
		if (l120m01a != null
				&& (UtilConstants.Casedoc.AuthLvl.分行授權內.equals(l120m01a
						.getAuthLvl()) || UtilConstants.Casedoc.AuthLvl.總行授權內
						.equals(l120m01a.getAuthLvl()))) {
			List<L140M01A> l140m01a_list = clsService
					.findL140M01A_l120m01aMainId_itemType(mainId,
							UtilConstants.Cntrdoc.ItemType.額度明細表);
			// int cntrNo_size = l140m01a_list.size();
			// int cntrNo_chkYN_Y_size = 0;
			// for(L140M01A l140m01a : l140m01a_list){
			// if(Util.equals("Y", l140m01a.getChkYN()) ){
			// ++cntrNo_chkYN_Y_size;
			// }
			// }
			// if(cntrNo_chkYN_Y_size!=cntrNo_size){
			// return; //仍有未完成 「計算額度合計」的 cntrNo
			// }
			// ==============================
			// 比照 cls1151m01formhandler :: checkAction(PageParameters params,
			// Component parent)
			for (L140M01A l140m01a : l140m01a_list) {
				l140m01a.setDocStatus(FlowDocStatusEnum.已核准.getCode());
				l140m01a.setCesRjtCause("");
				l140m01a.setCesRjtReason("");
			}
			cls1151Service.saveL140m01aList(l140m01a_list);
		}
	}

	@Override
	public String checkIsNotifiedMessageForPaperlessSigning(
			String l120m01a_mainId, String simpleFlag) {

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141M01Page.class);

		if (!UtilConstants.Casedoc.SimplifyFlag.快速審核信貸.equals(simpleFlag)) {

			L120S19A l120s19a = this.l120s19aDao
					.findByMainId_itemType_latest_itemVersion(l120m01a_mainId,
							"B");
			if (l120s19a != null) {
				ObjectMapper objectMapper = new ObjectMapper();
				Brmp002O brmp002O = null;

				try {
					brmp002O = objectMapper.readValue(
							JSONObject.fromObject(l120s19a.getJsonData())
									.toString(), Brmp002O.class);
				} catch (IOException e) {
					e.printStackTrace();
				}

				if (this.clsService
						.checkIs_outOfBranchAuthorization_ForPaperlessSigning(brmp002O)) {
					return prop
							.getProperty("l120m01a.isSuitablePaperlessSigning");
				}
			}
		}

		return "";
	}

	@Override
	public void importLendCollateral(String mainId, String[] rows, String unitNo)
			throws CapMessageException, CapException, IOException {
		L120M01A l120m01a = this.findL120m01aByMainId(mainId);
		List<C101M01A> c101m01as = this.findC101M01AByMaindIds(rows);

		if (rows != null && rows.length > 0) {
			String errMsg = lmsService.checkClsRatingModel(l120m01a, null);
			if (Util.isNotEmpty(errMsg)) {
				List<String> custIdList = new ArrayList<String>();
				List<String> dupNoList = new ArrayList<String>();
				for (C101M01A c101m01a : c101m01as) {
					custIdList.add(c101m01a.getCustId());
					dupNoList.add(c101m01a.getDupNo());
				}
				String[] custIdArr = custIdList.toArray(new String[custIdList
						.size()]);
				String[] dupNoArr = dupNoList.toArray(new String[dupNoList
						.size()]);
				cls1151Service.saveWhen_VarVer_Chg(mainId, custIdArr, dupNoArr);
			}
		}
		// 在 copy(l120m01a_mainId, rows) 裡去 複製 C101 到 C120
		// 會先 delete 再 insert
		// 從C101M01A copy 到 C120M01A 時, keyMan '預設'會被清掉
		// 下面的程式，再增加判斷，去填入 keyMan
		// service.copy(mainId, rows);
		lmsService.copyC101toC120(mainId, rows);
		
		//模型雙軌，進行資料複製(檔案依舊留存於C101S01X_N)
		boolean scoreDoubleTrack = scoreService.scoreDoubleTrack();
		if(scoreDoubleTrack){
			lmsService.copyC101toC101_forDoubleTrack(mainId, rows);
		}

		lmsService.setCaseNo(l120m01a);
		lmsService.save(l120m01a);

		HashMap<String, C101S01G> grade1Map_markModel_1 = new HashMap<String, C101S01G>();
		HashMap<String, C101S01Q> grade1Map_markModel_2 = new HashMap<String, C101S01Q>();
		HashMap<String, C101S01R> grade1Map_markModel_3 = new HashMap<String, C101S01R>();
		HashSet<String> choose_custIdDup = new HashSet<String>();
		for (C101M01A c101m01a : c101m01as) {
			String idDup = LMSUtil.getCustKey_len10custId(c101m01a.getCustId(),
					c101m01a.getDupNo());
			choose_custIdDup.add(idDup);
			C101S01G c101s01g = c101m01a.getC101s01g();
			if (c101s01g != null) {
				grade1Map_markModel_1.put(idDup, c101s01g);
			}

			C101S01Q c101s01q = c101m01a.getC101s01q();
			if (c101s01q != null) {
				grade1Map_markModel_2.put(idDup, c101s01q);
			}
			C101S01R c101s01r = c101m01a.getC101s01r();
			if (c101s01r != null) {
				grade1Map_markModel_3.put(idDup, c101s01r);
			}
		}
		String existKeyManIdDup = (l120m01a == null) ? "" : LMSUtil
				.getCustKey_len10custId(l120m01a.getCustId(),
						l120m01a.getDupNo());
		if (true) {

			List<C120M01A> c120m01as = (List<C120M01A>) cls1130Service
					.findListByMainId(C120M01A.class, mainId);
			// 2013/07/04,Rex,清空備註欄位
			for (C120M01A c120m01a : c120m01as) {
				String idDup = LMSUtil.getCustKey_len10custId(
						c120m01a.getCustId(), c120m01a.getDupNo());
				if (choose_custIdDup.contains(idDup)) {
					c120m01a.setRmk("");
					c120m01a.setKeyMan((Util.equals(idDup, existKeyManIdDup)) ? "Y"
							: "N");

					if (true) {
						C120S01A c120s01a = clsService.findC120S01A(c120m01a);
						if (c120s01a != null) {
							List<Map<String, Object>> listGrpMap = misGrpcmpService
									.findGrpcmpSelGrpdtl(c120m01a.getCustId(),
											c120m01a.getDupNo());
							Map<String, Object> grpMap = null;
							if (CollectionUtils.isNotEmpty(listGrpMap)) {
								grpMap = listGrpMap.get(0);
							}
							// 參考 L120S01B 的定義
							String groupNo = Util.trim(MapUtils.getString(
									grpMap, "GRPID"));
							String groupName = Util.trim(MapUtils.getString(
									grpMap, "GRPNM"));
							String groupBadFlag = Util.trim(MapUtils.getString(
									grpMap, "BADFLAG"));

							c120s01a.setGroupNo(groupNo);
							c120s01a.setGroupName(groupName);
							c120s01a.setGroupBadFlag(groupBadFlag);
							// J-108-0143_10702_B1001 新增新往來客戶註記並於額度明細表新做加註
							c120s01a.setNewCustFlag(lmsService.applyIsNewCust(
									c120m01a.getCustId(), c120m01a.getDupNo()));
							clsService.save(c120s01a);
						}
					}
				}

				String c120m01aCustId = Util.trim(c120m01a.getCustId());
				String c120m01aDupNo = Util.trim(c120m01a.getDupNo());
				String c120m01aCustName = Util.trim(c120m01a.getCustName());
				List<L140M01A> l140m01as = cls1151Service
						.findL140m01aListByL120m01cMainId(mainId, null);
				for (L140M01A l140m01a : l140m01as) {
					if (c120m01aCustId.equals(l140m01a.getCustId())
							&& c120m01aDupNo.equals(l140m01a.getDupNo())) {
						l140m01a.setCustName(c120m01aCustName);
						cls1151Service.save(l140m01a);
					}
				}
			}
			this.saveC120M01As(c120m01as, l120m01a);
		}
		// 更新評等
		List<L140M01A> l140m01as = cls1151Service
				.findL140m01aListByL120m01cMainId(mainId, null);
		List<L140S02A> tempL140S02A = new ArrayList<L140S02A>();
		for (L140M01A l140m01a : l140m01as) {
			List<L140S02A> l140s02as = (List<L140S02A>) cls1151Service
					.findModelListByMainId(L140S02A.class, l140m01a.getMainId());
			for (L140S02A l140s02a : l140s02as) {
				String modelKind = l140s02a.getModelKind();
				String custIdDup = LMSUtil.getCustKey_len10custId(
						l140s02a.getCustId(), l140s02a.getDupNo());

				if (Util.equals(UtilConstants.L140S02AModelKind.房貸, modelKind)) {
					C101S01G c101s01g = grade1Map_markModel_1.get(custIdDup);
					if (c101s01g != null) {
						l140s02a.setGrade1(c101s01g.getGrade3());
						tempL140S02A.add(l140s02a);
					}
				} else if (Util.equals(UtilConstants.L140S02AModelKind.非房貸,
						modelKind)) {
					C101S01Q c101s01q = grade1Map_markModel_2.get(custIdDup);
					if (c101s01q != null) {
						l140s02a.setGrade1(c101s01q.getGrade3());
						tempL140S02A.add(l140s02a);
					}
				} else if (Util.equals(UtilConstants.L140S02AModelKind.卡友貸,
						modelKind)) {
					C101S01R c101s01r = grade1Map_markModel_3.get(custIdDup);
					if (c101s01r != null) {
						l140s02a.setGrade1(c101s01r.getGrade3());
						tempL140S02A.add(l140s02a);
					}
				}

				//J-112-0205 Web e-Loan重引借款人，將更新代償資訊
//				if (Util.equals(l120m01a.getSimplifyFlag(),UtilConstants.Casedoc.SimplifyFlag.歡喜信貸) ||
//						Util.equals(l120m01a.getSimplifyFlag(),UtilConstants.Casedoc.SimplifyFlag.快速審核信貸)) {
				if(Util.equals(ProdKindEnum.歡喜信貸_71.getCode(),l140s02a.getProdKind())
					|| Util.equals(ProdKindEnum.一般消貸含團體消貸_07.getCode(),l140s02a.getProdKind())){
					if (Util.equals(l140s02a.getProperty(),UtilConstants.Cntrdoc.Property.新做)) {
						MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
						L140S02G l140s02g = cls1151Service.findModelByMainIdAndSeq(L140S02G.class, l140m01a.getMainId(), l140s02a.getSeq());
						List<L140S02H> l140s02h_list = cls1151Service.getL140s02hObject(l120m01a.getMainId(),l120m01a.getCustId(),l120m01a.getDupNo(),l140m01a.getMainId(), l140s02a.getSeq(), user);
						if (l140s02h_list.size()>0) {
							l140s02g.setChgOther(UtilConstants.DEFAULT.是);
							l140s02g.setChgCase(UtilConstants.DEFAULT.是);
							l140s02g.setChgBorrower(UtilConstants.DEFAULT.是);
							this.saveL140S02H(l140m01a.getMainId(), l140s02a.getSeq(),l140s02h_list);
							cls1151Service.save(l140s02g);
						}
					}
				}
			}
		}
		cls1151Service.saveL140S02As(tempL140S02A);
		cls1151Service.syncQuoteToC120M01A_markModel(mainId, false, null);

		if (true) {
			// 寫入 C120S01A.busCode
			for (C120S01A c120s01a : clsService
					.findC120S01A_mainId_busCodeEmpty(mainId)) {
				c120s01a.setBusCode(clsService.get0024_busCode(
						c120s01a.getCustId(), c120s01a.getDupNo()));
				// ========
				clsService.daoSave(c120s01a);
			}
		}

		if (true) {
			clsService.sync_c122m01a_applyKindBorD_statFlag(unitNo,
					choose_custIdDup, "1", "");
		}
	}

	@Override
	public String getL120s19b_content(String l120m01a_mainId, String type) {
		ISearch search = c140s04aDao.createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, l120m01a_mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		search.addOrderBy("createTime", false);
		search.setMaxResults(Integer.MAX_VALUE);

		List<L120S19B> l120s19bs = l120s19bDao.find(search);

		if (l120s19bs.size() > 0) {
			L120S19B l120s19b = l120s19bDao.find(search).get(0);

			if (Util.isNotEmpty(l120s19b)) {
				return l120s19b.getContent().trim();
			}
		}

		return "";
	}

	@Override
	public C122M01A findByPloanCaseNo(String ploanCaseNo){
		return c122m01aDao.findByPloanCaseNo(ploanCaseNo);
	}

	@Override
	public boolean processIsHittingSameBorrowerInfo(L140M01A l140m01a, String l120m01a_mainId){
		
		if (!UtilConstants.Cntrdoc.Property.新做.equals(l140m01a.getProPerty()) || "918".equals(l140m01a.getCntrNo().substring(0, 3))){ // 團貸建案母戶
			return false;
		}

		Map<String, String> checkIdMap = new HashMap<String, String>();
		checkIdMap.put(l140m01a.getCustId(), l140m01a.getDupNo());
		
		List<L140S01A> l140s01aList = this.lmsService.getCoLenderOfPersonalFinance(l140m01a.getMainId(), UtilConstants.lngeFlag.共同借款人);
		for(L140S01A l140s01a : l140s01aList){
			checkIdMap.put(l140s01a.getCustId(), l140s01a.getDupNo());
		}
		
		List<Map<String, Object>> sameInfoList = new ArrayList<Map<String, Object>>();
		for(String custId : checkIdMap.keySet()){
			String dupNo = checkIdMap.get(custId);
			C120S01A c120s01a = this.c120s01aDao.findByUniqueKey(l120m01a_mainId, custId, dupNo);
			
			if (!LMSUtil.isBusCode_060000_130300(c120s01a.getBusCode())) {// 非為自然人不檢核(ex:企業戶,
				continue;
			}

			List<String> contentList = new ArrayList<String>();
			contentList.add(CapString.trimNull(c120s01a.getCoTarget()));
			contentList.add(CapString.trimNull(c120s01a.getMTel()));
			contentList.add(CapString.trimNull(c120s01a.getCoTel()));
			contentList.add(CapString.trimNull(c120s01a.getEmail()));
			contentList.remove("");
				
			sameInfoList.addAll(this.dwdbService.findOtsCustInfoBy(contentList, custId, dupNo));
		}
		
		//sort DW inquiry data
		Map<String, L140MC2A> sameInfoMap = new LinkedHashMap<String, L140MC2A>();
		for(Map<String, Object> map : sameInfoList){
			
			String dataType = String.valueOf(map.get("DATA_TYPE"));
			String name = String.valueOf(map.get("NM"));
			String id = String.valueOf(map.get("ID"));
			String dup = String.valueOf(map.get("DUP"));
			String idAndDup = id.trim() + dup.trim();
			
			L140MC2A l140mc2a = sameInfoMap.get(idAndDup);
			l140mc2a = l140mc2a == null ? new L140MC2A() : l140mc2a;
			String content = CapString.trimNull(map.get("CONTENT"));
			
			if(UtilConstants.DATA_TYPE_OF_DW_OTSCUSTINFO.ADDRESS.equals(dataType)){
				l140mc2a.setAddress(content);
			}
			if(UtilConstants.DATA_TYPE_OF_DW_OTSCUSTINFO.CELLPHONE.equals(dataType)){
				l140mc2a.setCellphone(content);
			}
			if(UtilConstants.DATA_TYPE_OF_DW_OTSCUSTINFO.TELPHONE.equals(dataType)){
				l140mc2a.setTelphone(content);
			}
			if(UtilConstants.DATA_TYPE_OF_DW_OTSCUSTINFO.EMAIL.equals(dataType)){
				l140mc2a.setEmail(content);
			}
			
			l140mc2a.setCustId(id);
			l140mc2a.setDupNo(dup);
			l140mc2a.setCustName(name);
			sameInfoMap.put(idAndDup, l140mc2a);
		}

		//check is canceled account
		List<Map<String, Object>> unCanceledList = this.misMISLN20Service.findByUncanceledAccountCase(new ArrayList<String>(sameInfoMap.keySet()));
		
		if(unCanceledList != null && unCanceledList.size() > 10){
			unCanceledList = unCanceledList.subList(0, 10);
		}
		
		List<L140MC2A> sameInfoUncanceledEntity = new ArrayList<L140MC2A>();
		boolean isHitSameBorrowerInfo = false;
		if(unCanceledList != null && !unCanceledList.isEmpty()){
			
			for(Map<String, Object> map : unCanceledList){
				L140MC2A l140mc2a = sameInfoMap.get(map.get("LNF020_CUST_ID"));
				l140mc2a.setMainId(l140m01a.getMainId());
				l140mc2a.setCntrNo(l140m01a.getCntrNo());
				l140mc2a.setUpdateTime(CapDate.getCurrentTimestamp());
				l140mc2a.setVersion(UtilConstants.IsHittingSameInfoVersion.VERSION_1_0);
				sameInfoUncanceledEntity.add(l140mc2a);
			}

			isHitSameBorrowerInfo = true;
		}
		
		this.l140mc2aDao.save(sameInfoUncanceledEntity);
		
		return isHitSameBorrowerInfo;
	}
	
	@Override
	public void deleteAllL140mc2a(String l140m01a_mainId) {
		this.l140mc2aDao.deleteAll(this.l140mc2aDao.findByMainId(l140m01a_mainId));
	}
	
	@Override
	public List<L140MC2A> findL140MC2ABy(String l140m01a_mainId){
		return this.l140mc2aDao.findByMainId(l140m01a_mainId);
	}
	
	@Override
	public String import_collRealEstateFrom_L140M01O(List<L140M01O> l140m01os){
		String target = "";
		for(L140M01O l140m01o : l140m01os){//理論上適用填寫[簡化授信簽報書]之案件，擔保品只會有一筆
			if(UtilConstants.CollTyp1.不動產.equals(l140m01o.getCollTyp1())){//不動產才引入
				//先抓建物
				String build = Util.trim(l140m01o.getBuild());
				if(Util.isNotEmpty(build)){
					//預設只抓第一個建物
					JSONArray jsons = JSONArray.fromObject(build);
					for (int i = 0, total = jsons.size(); i < total; i++) {
						JSONObject data = JSONObject.fromObject(jsons.get(i));
						if (data == null || "null".equals(Util.trim(data))) {
							continue;
						}
						target = Util.trim(data.get("target"));
						if(Util.isNotEmpty(target)){
							break;
						}
					}
				}else{//沒有建物就抓土地
					String areaDetail = Util.trim(l140m01o.getAreaDetail());
					if(Util.isNotEmpty(areaDetail)){
						JSONArray jsons = JSONArray.fromObject(areaDetail);
						for (int i = 0, total = jsons.size(); i < total; i++) {
							//預設只抓第一個土地
							JSONObject data = JSONObject.fromObject(jsons.get(i));
							if (data == null || "null".equals(Util.trim(data))) {
								continue;
							}
							target = Util.trim(data.get("target"));
							if(Util.isNotEmpty(target)){
								break;
							}
						}
					}
				}
				if(Util.isNotEmpty(target)){
					break;
				}
			}
		}
		return target;
	}

	@Override
	public BigDecimal sumCompensationAmt(JSONObject json) {
		BigDecimal compensationAmt = BigDecimal.ZERO;
		int jcicCount = Util.parseInt(json.optString("jcicCount"));
		int jcicCreditCount = Util.parseInt(json.optString("jcicCreditCount"));
		//貸款代償
		if (jcicCount > 0 ){
			for (int i = 1; i <= jcicCount; i++) {
				String jcic_period_needmegapay = Util.trim(json.optString("jcic_period_needmegapay_"+i));
				if ( Util.equals(jcic_period_needmegapay,UtilConstants.DEFAULT.是)) {
					String loan= NumConverter.delCommaString(Util.trim(json.optString("jcic_loanamt_"+i)));
					String repaymentProductType = Util.trim(json.optString("jcic_item_"+i));
					boolean isMatch = false;
					List<CodeType> jcicCodeTypes = codeTypeService.findByCodeTypeList("repaymentProductId");
					for (CodeType jcicitem : jcicCodeTypes) {
						if (repaymentProductType.equals(jcicitem.getCodeValue())) {
							isMatch = true;
							break;
						}
					}
					if (isMatch) {
						BigDecimal jcic_loan = Util.parseBigDecimal(loan).multiply(new BigDecimal(1000));
						compensationAmt = compensationAmt.add(jcic_loan);
					}
				}
			}
		}

		//信用卡代償
		if (jcicCreditCount > 0 ){
			for (int i = 1; i <= jcicCreditCount; i++) {
				//循環
				String jcic_credit_revol_needmegapay = Util.trim(json.optString("jcic_credit_revol_needmegapay_"+i));
				if ( Util.equals(jcic_credit_revol_needmegapay,UtilConstants.DEFAULT.是)) {
					String credit_revol=NumConverter.delCommaString(Util.trim(json.optString("jcic_credit_revol_"+i)));
					BigDecimal jcic_credit_revol = Util.parseBigDecimal(credit_revol);
					compensationAmt = compensationAmt.add(jcic_credit_revol);
				}
				//分期償還
				String jcic_credit_pre_needmegapay = Util.trim(json.optString("jcic_credit_pre_needmegapay_"+i));
				if ( Util.equals(jcic_credit_pre_needmegapay,UtilConstants.DEFAULT.是)) {
					String credit_pre=NumConverter.delCommaString(Util.trim(json.optString("jcic_credit_pre_"+i)));
					BigDecimal jcic_credit_pre = Util.parseBigDecimal(credit_pre);
					compensationAmt = compensationAmt.add(jcic_credit_pre);
				}
			}
		}


		return compensationAmt;
	}

	private void saveL140S02H(String tabMainid, int seq, List<L140S02H> l140s02h_list){
		//先把舊的代償資訊del在重建
//		Object[] msgFmtParam = new Object[] { "L140S02H",
//				"'"+tabMainid+"'" };
		eloandbBASEService
				.deleteByMainId("L140S02H", tabMainid);

		for (L140S02H l140s02h:l140s02h_list) {
			l140s02hDao.save(l140s02h);
		}
	}

	@Override
	public String promptUsePlan(String l120m01a_mainId){
		String promptMsg = "";
        if (clsService.is_function_on_codetype("J-113-0200_prompt")) {
            for (L140M01A l140m01a :l140m01aDao
					.findL140m01aListByL120m01cMainId(l120m01a_mainId, UtilConstants.Cntrdoc.ItemType.額度明細表, null)) {
                if (Util.equals(l140m01a.getProPerty(),
                        UtilConstants.Cntrdoc.Property.新做)) {
                    List<L140M01Y> l140m01y_list = clsService
                            .findL140M01YOrderDefault(
                                    l140m01a.getMainId(),
                                    UtilConstants.L140M01Y_refType_docCode1.ELF459_SRCFLAG_1);
					List<L140S02A> l140s02as = (List<L140S02A>) cls1151Service
							.findModelListByMainId(L140S02A.class, l140m01a.getMainId());
                    String usePlan = "";
					for (L140S02A l140s02a : l140s02as) {
						if(Util.equals(ProdKindEnum.歡喜信貸_71.getCode(),l140s02a.getProdKind())
								|| Util.equals(ProdKindEnum.一般消貸含團體消貸_07.getCode(),l140s02a.getProdKind())){
							if (l140m01y_list.size() > 0) {
								L140M01Y l140m01y = l140m01y_list.get(0);
								usePlan = l140m01y.getUsePlan();
								if (Util.equals("C122M01A", l140m01y.getRefModel())) {
									C120S01R c120s01r = clsService.findC120S01R(l120m01a_mainId, l140m01a.getCustId(),l140m01a.getDupNo());
									if (c120s01r!=null && usePlan!=null) {
										if (Util.equals(c120s01r.getJ10_score(),0) && usePlan.equals(UtilConstants.usePlan.公教好享貸)) {
											CodeType codetype = codeTypeService.findByCodeTypeAndCodeValue(
													"ploan_plan", usePlan);
											promptMsg = codetype.getCodeDesc3();
										}
									}
									C122M01A c122M01A =c122m01aDao.findByMainId(l140m01y.getRefMainId());
									if (c122M01A !=null) {
										String usePlanErr ="額度序號:"+l140m01a.getCntrNo()+"，歡喜信貸使用專案與進件管理不一致，請重新引進。";
										if (!Util.equals(c122M01A.getUsePlan(),usePlan)) {
											if (Util.isNotEmpty(promptMsg)) {
												promptMsg += "<br>";
												promptMsg += usePlanErr;
											} else {
												promptMsg += usePlanErr;
											}
										}
									}
								}
							}
						}
					}
                }
            }
        }

		return promptMsg;
	}

	@Override
	public void startMortgageRatioValidation(L120M01A l120m01a) throws CapMessageException{

		if(!UtilConstants.Casedoc.AuthLvl.分行授權內.equals(l120m01a.getAuthLvl()) 
				|| LMSUtil.isParentCase(l120m01a) 
				|| Util.isNotEmpty(l120m01a.getSimplifyFlag()) && !UtilConstants.Casedoc.SimplifyFlag.一般消金.equals(l120m01a.getSimplifyFlag())
				){
			
			return;
		}
		
		this.l140mc1bDao.delete(this.l140mc1bDao.findByCaseMainid(l120m01a.getMainId()));

		if(!this.clsService.setMortgageCheckingRatio(l120m01a)){
			return;
		}
		
		this.checkMortgageRatio(l120m01a.getMainId());
	}
	
	public boolean checkMortgageRatio(String l120m01a_mainId) throws CapMessageException {

		List<L140MC1B> l140mc1bList = this.l140mc1bDao.findByCaseMainid(l120m01a_mainId);
		
		boolean isCorrect = true;
		for(L140MC1B l140mc1b : l140mc1bList){
			
			if("F".equals(l140mc1b.getProdKindType())){ //菁英方案(Y1, Y2, Y3)不做檢核一
				continue;
			}
			
			BigDecimal check1Ratio = l140mc1b.getCheck1ProdkindRatio().divide(new BigDecimal(10), 3, RoundingMode.DOWN);
			BigDecimal inAmt = l140mc1b.getInAmt(); // 擔保品時價
			BigDecimal lndTax = l140mc1b.getLndTax() == null ? BigDecimal.ZERO : l140mc1b.getLndTax(); // 擔保品土地應計增值稅
			BigDecimal disAmt = l140mc1b.getDisAmt() == null ? BigDecimal.ZERO : l140mc1b.getDisAmt(); // 擔保品扣除寬限期預提折舊金額
			BigDecimal collateralValue = inAmt.subtract(lndTax).subtract(disAmt);
			BigDecimal loanAmt = l140mc1b.getLoanAmt();// 產品種類分項金額
			
			String check1Result = "Y";
			if("E".equals(l140mc1b.getProdKindType()) && "Y".equals(l140mc1b.getRatioReducedItem3()) 
						|| loanAmt.compareTo(collateralValue.multiply(check1Ratio)) > 0){
				
				check1Result = "N";
				isCorrect = false;
			}
			
			l140mc1b.setCheck1Results(check1Result);
			l140mc1b.setFinalResults(check1Result);
			this.save(l140mc1b);
		}
		
		if(!isCorrect){
			return isCorrect;
		}

		for(Map<String, Object> map : this.eloandbBASEService.findL140mc1bTotalBalanceOrCurrentApplyAmt(l120m01a_mainId)){
			
			String cmsOid = String.valueOf(map.get("CMSOID"));
			BigDecimal totalAmt = LMSUtil.nullToZeroBigDecimal(map.get("TOTALAMT"));
			BigDecimal maxCheck2Ratio = LMSUtil.nullToZeroBigDecimal(map.get("MAXCHECK2RATIO")).divide(new BigDecimal(10), 4, RoundingMode.UP);
			
			String check2Result = null;
			List<L140MC1B> list = this.l140mc1bDao.findExcludeMortgageInsuranceAndY1Y2Y3By(cmsOid, l120m01a_mainId);//排除房貸壽險額度明細表不做檢核2
			for(L140MC1B l140mc1b : list){
					
				BigDecimal inAmt = l140mc1b.getInAmt(); // 擔保品時價
				BigDecimal lndTax = l140mc1b.getLndTax() == null ? BigDecimal.ZERO : l140mc1b.getLndTax(); // 擔保品土地應計增值稅
				BigDecimal disAmt = l140mc1b.getDisAmt() == null ? BigDecimal.ZERO : l140mc1b.getDisAmt(); // 擔保品扣除寬限期預提折舊金額
				BigDecimal collateralValue = inAmt.subtract(lndTax).subtract(disAmt);
				
				check2Result = "Y";
				if(totalAmt.compareTo(collateralValue.multiply(maxCheck2Ratio)) > 0){
					check2Result = "N";
					isCorrect = false;
				}
				
				l140mc1b.setFinalResults(check2Result);
				
				l140mc1b.setCheck2Results(check2Result);
				this.save(l140mc1b);
			}
		}
		
		if(!isCorrect){
			return isCorrect;
		}
		
		
		for(L140MC1B l140mc1b : l140mc1bList){
			
			if(!"F".equals(l140mc1b.getProdKindType())){ //菁英方案(Y1, Y2, Y3)只做檢核三
				continue;
			}
			
			String projClass = l140mc1b.getProjClass();
			String eliteCheckResults = "Y";
			
			BigDecimal inAmt = l140mc1b.getInAmt(); // 擔保品時價
			BigDecimal lndTax = l140mc1b.getLndTax() == null ? BigDecimal.ZERO : l140mc1b.getLndTax(); // 擔保品土地應計增值稅
			BigDecimal disAmt = l140mc1b.getDisAmt() == null ? BigDecimal.ZERO : l140mc1b.getDisAmt(); // 擔保品扣除寬限期預提折舊金額
			BigDecimal collateralValue = inAmt.subtract(lndTax).subtract(disAmt);
			
			if("Y1".equals(projClass) || "Y3".equals(projClass)){
				if(l140mc1b.getLoanAmt().compareTo(l140mc1b.getElitePlanLoanAmt()) > 0){
					eliteCheckResults = "N";
					isCorrect = false;
				}
			}

			List<String> cmsCntrNoList = this.eloandbcmsBASEService.getCntrNoBeenSetByC100m01Oid(l140mc1b.getCmsOid());
			List<L140M01A> l140m01aAll = l140m01aDao.findL140m01aListByL120m01cMainIdExcludeCntrNo(l140mc1b.getCaseMainId(), 
																	UtilConstants.Cntrdoc.ItemType.額度明細表, l140mc1b.getCntrNo());
			Set<String> cntrNoSet = new HashSet<String>();
			for(L140M01A l140m01a : l140m01aAll){
				cntrNoSet.add(l140m01a.getCntrNo());
			}
			
			for(String cmsCntrNo : cmsCntrNoList){
				if(!cntrNoSet.contains(cmsCntrNo)){
					// 抓最早一份新作簽報書
					L140M01A l140m01a = this.l140m01aDao.findLatestApprovedL140m01a(cmsCntrNo);
					if(null != l140m01a){
						l140m01aAll.add(l140m01a);
					}
				}
			}
			
			BigDecimal totalCurrentApplyAmt = BigDecimal.ZERO;
			BigDecimal totalBlAmt = BigDecimal.ZERO;
			for(L140M01A l140m01a : l140m01aAll){
				totalCurrentApplyAmt = totalCurrentApplyAmt.add(LMSUtil.nullToZeroBigDecimal(l140m01a.getCurrentApplyAmt()));
				totalBlAmt = totalBlAmt.add(LMSUtil.nullToZeroBigDecimal(l140m01a.getBLAmt()));
			}
			
			BigDecimal x1Amount = null;
			if("Y1".equals(projClass) || "Y2".equals(projClass)){
				x1Amount = totalCurrentApplyAmt;
			}
			
			if("Y3".equals(projClass)){
				x1Amount = totalBlAmt;
			}
			
			// 不循環且非新作 ? 餘額 : 現請額度
			BigDecimal y1y2y3Amount = "1".equals(l140mc1b.getReUse()) && !"1".equals(l140mc1b.getProPerty()) ? l140mc1b.getBLAmt() : l140mc1b.getCurrentApplyAmt();
			BigDecimal totalAmount = y1y2y3Amount.add(x1Amount);
			BigDecimal collRatioAmt = collateralValue.multiply(l140mc1b.getElitePlanRatioLimit().divide(new BigDecimal(10), 3, RoundingMode.DOWN));
			if(totalAmount.compareTo(collRatioAmt) > 0){
				eliteCheckResults = "N";
				isCorrect = false;
			}
			
			l140mc1b.setEliteCheckResults(eliteCheckResults);
			l140mc1b.setFinalResults(eliteCheckResults);
			this.save(l140mc1b);
		}
		
		return isCorrect;
	}
	
	@Override
	public String getMortgageRatioCheckTipsMessage(L120M01A l120m01a) {

		Map<String, String> map = codeService.findByCodeType("MRCheck_Tips_Msg");
		
		List<String> tipsList = new ArrayList<String>();
		String tipsMsg = "";
		for(L140MC1B l140mc1b : this.l140mc1bDao.findByCaseMainid(l120m01a.getMainId())){
			
			for(String code : l140mc1b.getNotCheckCode().split("")){
				
				String str = map.get(code.trim());
				if(str != null){
					tipsList.add(str);
				}
			}
			
			if(!tipsList.isEmpty()){
				tipsMsg += StringUtils.join(tipsList, "<br/>");
			}
		}
		
		return tipsMsg;
	}
	
	@Override
	public String getMortgageRatioCheckErrorMessage(String l120m01a_mainId){
		
		Map<String, String> map = codeService.findByCodeType("MRCheck_Error_Msg");
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS1141M01Page.class);
		
		List<String> errorList = new ArrayList<String>();
		for(L140MC1B l140mc1b : this.l140mc1bDao.findByCaseMainid(l120m01a_mainId)){
			
			for(String code : l140mc1b.getNotCheckCode().split("")){
				
				String str = map.get(code);
				if(str != null){
					errorList.add(str);
				}
			}
		}
		
		if(!errorList.isEmpty()){
			return StringUtils.join(errorList, "<br/>");
		}
		
		List<Map<String, Object>> list = this.eloandbBASEService.findL140mc1bTotalBalanceOrCurrentApplyAmt(l120m01a_mainId);
		Map<String, Map<String, Object>> cmsOidMap = new HashMap<String, Map<String, Object>>();
		for(Map<String, Object> m : list){
			cmsOidMap.put(String.valueOf(m.get("CMSOID")), m);
		}

		for(L140MC1B l140mc1b : this.l140mc1bDao.findByCaseMainid(l120m01a_mainId)){
			
			if("N".equals(l140mc1b.getCheck1Results()) || "N".equals(l140mc1b.getCheck2Results()) || "N".equals(l140mc1b.getEliteCheckResults())){
				
				BigDecimal totalAmt = LMSUtil.nullToZeroBigDecimal(cmsOidMap.get(l140mc1b.getCmsOid()).get("TOTALAMT"));
				BigDecimal maxCheck2Ratio = LMSUtil.nullToZeroBigDecimal(cmsOidMap.get(l140mc1b.getCmsOid()).get("MAXCHECK2RATIO")).multiply(new BigDecimal(10));

				if("N".equals(l140mc1b.getCheck1Results()) || "N".equals(l140mc1b.getCheck2Results())){
					// 全案現請額度加總(金額{0}：{1})，不可大於擔保品時價金額*可核貸[依規定](最高成數{2}%)。
					return MessageFormat.format(prop.getProperty("L140MC1B.msg.error.totalAmountCantGreaterThanCollateralValue"), 
								l140mc1b.getCurrentApplyCurr(), totalAmt, maxCheck2Ratio);
				}
				
				if("N".equals(l140mc1b.getEliteCheckResults())){
					// 額度序號{0}專案種類為Y1,Y2,Y3菁英方案，額度加總不可大於[擔保品時價*成數]金額
					return MessageFormat.format(prop.getProperty("L140MC1B.msg.error.elitePlanAmountMortThanCollateralValue"), l140mc1b.getCntrNo());
				}
			}
		}
		
		return null;
	}
	
}
