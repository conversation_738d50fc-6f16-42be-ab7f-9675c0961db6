/*
 * L140M01VDao.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140M01V;

/** 連鎖加盟貸款資訊檔 **/
public interface L140M01VDao extends IGenericDao<L140M01V> {

	L140M01V findByUniqueKey(String cntrNo);

	List<L140M01V> findByIndex01(String cntrNo);

	List<L140M01V> findByIndex02(String mainBizId, String mainBizDupNo);
}