/* 
 * LMS1201V03Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import tw.com.jcs.auth.AuthType;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;


/**<pre>
 * 授信簽報書呈授管處/營運中心
 * </pre>
 * @since  2011/11/9
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/11/9,<PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1201v03")
public class LMS1201V03Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		//設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_呈授管處);
		
		if (this.getAuth(AuthType.Modify)) {
			// 經辦權限時要顯示的按鈕...
			addToButtonPanel(model, LmsButtonEnum.View);
		} else {
			// 否則需要顯示的按鈕
			// 加上Button
			addToButtonPanel(model, LmsButtonEnum.View);
		}
		
//		if (this.getAuth(params, AuthType.Accept)) {
//			// 主管權限時要顯示的按鈕...
//			add(new CreditButtonPanel("_buttonPanel", null,CreditButtonEnum.View));			
//		} else {
//			// 否則需要顯示的按鈕
//			// 加上Button
//			add(new CreditButtonPanel("_buttonPanel", null,
//					CreditButtonEnum.View));					
//		}		
		//套用哪個i18N檔案
		renderJsI18N(LMS1201V01Page.class);
		
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/lns/LMS1201V01Page');");
	}// ;

}
