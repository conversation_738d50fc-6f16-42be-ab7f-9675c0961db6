/* 
 *  LMS1401GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.I18NFormatter;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSLgdService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.enums.L140M01NEnum;
import com.mega.eloan.lms.lms.handler.grid.LMS1405GridHandler;
import com.mega.eloan.lms.lms.panels.LMS1405S02Panel;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel;
import com.mega.eloan.lms.lns.panels.LMS1401S02Panel04;
import com.mega.eloan.lms.lns.panels.LMS1401S08Panel;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1411Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01B;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01I;
import com.mega.eloan.lms.model.L140M01J;
import com.mega.eloan.lms.model.L140M01N;
import com.mega.eloan.lms.model.L140M01S;
import com.mega.eloan.lms.model.L140M01T;
import com.mega.eloan.lms.model.L140M02S;
import com.mega.eloan.lms.model.L140S06A;
import com.mega.eloan.lms.model.L140S09A;
import com.mega.eloan.lms.model.L140S09B;
import com.mega.eloan.lms.model.L140S11A;
import com.mega.eloan.lms.model.L140S12A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 額度明細表grid
 * </pre>
 * 
 * @since 2011/9/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,REX,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1401gridhandler")
public class LMS1401GridHandler extends LMS1405GridHandler {
	private static String 額度明細表mainId = "tabFormMainId";
	// 復原TFS J-111-0636_05097_B100X
	@Resource
	LMS1201Service lms1201Service;

	@Resource
	LMS1605Service lms1605Service;

	@Resource
	BranchService branchService;

	@Resource
	LMS1411Service lms1411Service;

	@Resource
	UserInfoService userInfoService;

	Properties prop = MessageBundleScriptCreator
			.getComponentResource(LMS1401S02Panel.class);

	Properties prop0204 = MessageBundleScriptCreator
			.getComponentResource(LMS1401S02Panel04.class);
	@Resource
	CodeTypeService codeTypeService;

	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;

	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	LMSService lmsService;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	DocFileService docFileService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMSLgdService lmsLgdService;

	/**
	 * 查詢徵信報告書
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryC140M01A(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		Page<Map<String, Object>> page = eloandbBASEService.findCESL140M01A(
				pageSetting, user.getUnitNo(), custId, dupNo);

		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			String docstatus = Util.trim(map.get("docStatus"));
			map.put("docStatus", Util.isEmpty(docstatus) ? ""
					: getMessage("docStatus." + docstatus));
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢資信檢表 平均動用率
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesInfo(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(額度明細表mainId));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(caseMainId);
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		String ownBrId = l140m01a.getOwnBrId();
		if (l120m01a != null) {
			ownBrId = l120m01a.getCaseBrId();
		}
		Page<Map<String, Object>> page = eloandbBASEService.findCESL120S01A(
				pageSetting, ownBrId, l140m01a.getCustId(),
				l140m01a.getDupNo(), l140m01a.getCntrNo());
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			String docstatus = Util.trim(map.get("docStatus"));
			map.put("docStatus", Util.isEmpty(docstatus) ? ""
					: getMessage("docStatus." + docstatus));

			String strJson = Util.trim(map.get("JSONOB"));
			if (Util.notEquals(strJson, "")) {
				JSONObject jsObj = JSONObject.fromObject(strJson);
				map.put("UPDATETIME", jsObj.optString("lnDataDate", ""));
			}
		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表結構利率化table
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01n(ISearch pageSetting,	PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.trim(params.getString(額度明細表mainId));
		int rateSeq = params.getInt("rateSeq");

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "rateSeq",
				rateSeq);
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(new String[] { "lms1401s0204_secNoOp" });
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01N.class, pageSetting);
		List<L140M01N> l140m01ns = (List<L140M01N>) page.getContent();
		for (L140M01N l140m01n : l140m01ns) {
			l140m01n.setRateType(this.coverCurr(l140m01n.getRateType()));
			if ("0".equals(Util.trim(l140m01n.getSecNo()))) {
				// l1401s02p04.018=全
				l140m01n.setSecNo(prop0204.getProperty("l1401s02p04.018"));
			}

			l140m01n.setSecNoOp(this.coverSecNoOp(l140m01n,
					codeMap.get("lms1401s0204_secNoOp")));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢共同借款人grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140M01J(ISearch pageSetting,	PageParameters params) throws CapException {
		String tabMainId = Util.trim(params.getString(額度明細表mainId));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, tabMainId);
		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01J.class, pageSetting);
		List<L140M01J> l140m01js = (List<L140M01J>) page.getContent();
		for (L140M01J data : l140m01js) {
			data.setCustId(data.getCustId() + " " + data.getDupNo());

			data.setCustRlt(LMSUtil.changeCustRlt(Util.trim(data.getCustRlt()),
					codeMap));
			data.setNtCode(Util.trim(codeMap.get(CodeTypeEnum.國家代碼.getCode())
					.get(Util.trim(data.getNtCode()))));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢簽報書借款人grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120S01A(ISearch pageSetting,	PageParameters params) throws CapException {

		String mainId = Util.nullToSpace(params.getString("mainId"));
		HashMap<String, String> code = lms1401Service.getCustCounty(mainId);
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);

		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));

		String[] codeType = { "Relation_type1", "Relation_type2",
				"Relation_type31", "Relation_type32", "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1201Service.findPage(
				L120S01A.class, pageSetting);
		List<L120S01A> newlist = new ArrayList<L120S01A>();
		List<L120S01A> list = (List<L120S01A>) page.getContent();

		for (L120S01A l120s01a : list) {
			if ((custId + dupNo).equals(l120s01a.getCustId()
					+ l120s01a.getDupNo())) {
				continue;
			}
			// 排除這份額度明細表的主要借款人
			l120s01a.setCustPos(Util.trim(code.get(l120s01a.getCustId()
					+ l120s01a.getDupNo())));
			l120s01a.setCustId(l120s01a.getCustId() + " " + l120s01a.getDupNo());
			l120s01a.setCustRlt(LMSUtil.changeCustRlt(l120s01a.getCustRlt(),
					codeMap));

			newlist.add(l120s01a);
		}
		return new CapGridResult(newlist, newlist.size());
	}

	/**
	 * 轉換適用期間選項
	 * 
	 * @param L140M01N
	 *            結構化利率主檔
	 * @return 文字
	 * 
	 *         全案 | 1<br/>
	 *         自動用日起迄月| 2<br/>
	 *         YYYYMMDD| 3<br/>
	 *         自簽約日起迄月| 4
	 */

	private String coverSecNoOp(L140M01N l140m01n, CapAjaxFormResult codeMap) {

		String code = Util.trim(l140m01n.getSecNoOp());
		if (Util.isEmpty(code)) {
			return "";
		}
		String result = "";
		switch (L140M01NEnum.SecNoOpEnum.getEnum(code)) {
		case 全案:
			result = codeMap.get(code) + "";
			break;
		case YYYYMMDD:
			result = CapDate.formatDate(l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD)
					+ "~"
					+ CapDate.formatDate(l140m01n.getSecEndDate(),
							UtilConstants.DateFormat.YYYY_MM_DD);
			break;
		case 自動用日起迄月:
		case 自簽約日起迄月:
			// l1401s02p04.005=第
			// l1401s02p04.008=月
			result = prop0204.getProperty("l1401s02p04.005")
					+ Util.trim(l140m01n.getSecBegMon())
					+ prop0204.getProperty("l1401s02p04.008") + "~"
					+ prop0204.getProperty("l1401s02p04.005")
					+ Util.trim(l140m01n.getSecEndMon())
					+ prop0204.getProperty("l1401s02p04.008");
			break;
		case 自動用日起至迄日:
			// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			result = MessageFormat.format(prop0204
					.getProperty("l1401s02p04.msg030"), CapDate.formatDate(
					l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			result = result.replace("(", "").replace(")", "").replace("：", "");
			break;
		case 自簽約日起至迄日:
			// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			result = MessageFormat.format(prop0204
					.getProperty("l1401s02p04.msg031"), CapDate.formatDate(
					l140m01n.getSecEndDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			result = result.replace("(", "").replace(")", "").replace("：", "");
			break;
		case YYYYMMDD至迄日:
			// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			result = MessageFormat.format(prop0204
					.getProperty("l1401s02p04.msg032"), CapDate.formatDate(
					l140m01n.getSecBegDate(),
					UtilConstants.DateFormat.YYYY_MM_DD));
			result = result.replace("(", "").replace(")", "").replace("：", "");
			break;

		}
		return result;
	}

	/**
	 * 轉換幣別顯示訊息
	 * 
	 * @param currCode
	 *            幣別代號
	 * @return 文字
	 */
	private String coverCurr(String currCode) {
		currCode = Util.trim(currCode);
		if (Util.isEmpty(currCode)) {
			return "";
		}
		String result = "";
		switch (L140M01NEnum.RateTypeEnum.getEnum(currCode)) {
		case 新台幣:
			result = prop.getProperty("L140M01f.TWD");
			break;
		case 美金:
			result = prop.getProperty("L140M01f.USD");
			break;
		case 日幣:
			result = prop.getProperty("L140M01f.JPY");
			break;
		case 歐元:
			result = prop.getProperty("L140M01f.EUR");
			break;
		case 澳幣:
			result = prop0204.getProperty("l1401s02p04.001");
			break;
		case 港幣:
			result = prop0204.getProperty("l1401s02p04.002");
			break;
		case 人民幣:
			result = prop0204.getProperty("l1401s02p04.003");
			break;
		case 雜幣:
			result = prop.getProperty("L140M01f.Other");
			break;
		}
		return result;
	}

	/**
	 * 查詢額度明細表Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));

		L120M01A meta = lms1201Service.findL120m01aByMainId(caseMainId);
		String docStatus = Util.trim(meta.getDocStatus());
		String areaChk = Util.trim(meta.getAreaChk());
		String unitNo = user.getUnitNo();
		String caseBrid = Util.trim(meta.getCaseBrId());

		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		// dataReformatter.put("docStatus", new I18NFormatter(getComponent(),
		// "docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01alist) {
			StringBuilder sb = new StringBuilder();
			sb.setLength(0);
			sb.append("docStatus.");
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}

			// J-112-0426_05097_B1001
			// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
			String addCheckBtEsg = "";
			if (Util.equals(l140m01a.getDocStatus(),
					FlowDocStatusEnum.已核准.getCode())
					|| Util.equals(l140m01a.getDocStatus(),
							FlowDocStatusEnum.婉卻.getCode())) {
				String checkBtEsg = Util.trim(l140m01a.getCheckBtEsg());
				addCheckBtEsg = Util.isEmpty(checkBtEsg) ? "" : "("
						+ checkBtEsg + ")";
			}

			if (UtilConstants.BankNo.授管處.equals(unitNo)
					|| UtilConstants.BankNo.中區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.中部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.北一區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.北二區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南區營運中心.equals(unitNo)
					|| UtilConstants.BankNo.南部區域授信中心.equals(unitNo)
					|| UtilConstants.BankNo.桃竹苗區營運中心.equals(unitNo)
					|| CreditDocStatusEnum.海外_待覆核.getCode().equals(docStatus)) {
				// 當起案分行為國外部、金控總部、財務部、國金部，
				// ※會簽資料夾會簽中顯示之案件，文件狀態為（L1H、L2H）且
				// 是否加送會審單位為送會簽(L120M01A.areaChk=”2”)之案件。
				// 將額度明細表文件狀態統一改成待覆核
				// Miller added at 2012/12/10
				if (UtilConstants.BankNo.國外部.equals(caseBrid)
						|| UtilConstants.BankNo.財富管理處.equals(caseBrid)
						|| UtilConstants.BankNo.國金部.equals(caseBrid)
						|| UtilConstants.BankNo.財務部.equals(caseBrid)
						|| UtilConstants.BankNo.金控總部分行.equals(caseBrid)
						|| UtilConstants.BankNo.授信行銷處.equals(caseBrid)
						|| UtilConstants.BankNo.消金業務處.equals(caseBrid)
						|| UtilConstants.BankNo.私銀處作業組.equals(caseBrid)) {

					// (108)第 3230 號
					if ((CreditDocStatusEnum.授管處_審查中.getCode()
							.equals(docStatus)
							|| CreditDocStatusEnum.海外_待覆核.getCode().equals(
									docStatus) || CreditDocStatusEnum.營運中心_審查中
							.getCode().equals(docStatus)
					// ||
					// CreditDocStatusEnum.授管處_已會簽.getCode().equals(docStatus)
					)
							&& (UtilConstants.Casedoc.AreaChk.送會簽
									.equals(areaChk)
									|| UtilConstants.Casedoc.AreaChk.送初審
											.equals(areaChk) || UtilConstants.Casedoc.AreaChk.送審查
									.equals(areaChk))
							&& UtilConstants.Cntrdoc.ItemType.額度明細表
									.equals(itemType)) {

						// J-112-0426_05097_B1001
						// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
						sb.append(FlowDocStatusEnum.待覆核.getCode());
						l140m01a.setDocStatus(getMessage(sb.toString())
								+ addCheckBtEsg);
					} else {
						// 正常顯示文件狀態
						// J-112-0426_05097_B1001
						// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
						sb.append(l140m01a.getDocStatus());
						l140m01a.setDocStatus(getMessage(sb.toString())
								+ addCheckBtEsg);
					}
				} else {
					// 正常顯示文件狀態
					// J-112-0426_05097_B1001
					// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
					sb.append(l140m01a.getDocStatus());
					l140m01a.setDocStatus(getMessage(sb.toString())
							+ addCheckBtEsg);
				}
			} else {
				// 正常顯示文件狀態
				// J-112-0426_05097_B1001
				// 為正確統計涉及ESG風險授信案件之審查結果，於簽報書額度明細表核定時，核定註記改以下拉選單方式，並將審查結果按月產生報表(格式如附檔)。
				sb.append(l140m01a.getDocStatus());
				l140m01a.setDocStatus(getMessage(sb.toString()) + addCheckBtEsg);
			}

			// 用來暫放文件狀態
			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}

		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢L120M01AGrid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL120m01a(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120a01a.authUnit", user.getUnitNo());
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1201Service.findPage(
				L120M01A.class, pageSetting);
		StringBuilder allCust = new StringBuilder();
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());

			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢引進簽報書L120M01A資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            <pre>
	 *            custId:客戶統編
	 *            brid:分行代號
	 * </pre>
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult querySrcByL120M01A(ISearch pageSetting, PageParameters params) throws CapException {
		String custId = Util.trim(params.getString("custId", ""));
		String brNo = Util.trim(params.getString("brNo", ""));
		if (Util.isNotEmpty(custId)) {
			int size = custId.length();
			String dupNo = custId.substring(size - 1, size);
			custId = custId.substring(0, size - 1);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo",
					dupNo);
		}
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "caseBrId", brNo);
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
				UtilConstants.Casedoc.typCd.海外);
		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				new String[] { CreditDocStatusEnum.海外_已核准.getCode(),
						CreditDocStatusEnum.海外_婉卻.getCode() });
		// 判定是否已註記被刪除
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		// 第三個參數為formatting
		Page<? extends GenericBean> page = lms1201Service.findPage(
				L120M01A.class, pageSetting);
		StringBuilder allCust = new StringBuilder();
		List<L120M01A> l120m01as = (List<L120M01A>) page.getContent();
		for (L120M01A model : l120m01as) {
			allCust.setLength(0);
			allCust.append(model.getCustId()).append(" ")
					.append(model.getDupNo()).append(" ")
					.append(model.getCustName());

			model.setCaseNo(Util.toSemiCharString(model.getCaseNo()));
			model.setCustId(allCust.toString());

			if (!Util.isEmpty(Util.trim(model.getDocStatus()))) {
				model.setDocStatus(getMessage("docStatus."
						+ CreditDocStatusEnum.getEnum(model.getDocStatus())
								.getCode()));
			} else {
				model.setDocStatus("");
			}
			model.setUpdater(this.getUserName(model.getUpdater()));
			if (!Util.isEmpty(model.getAreaAppraiser())) {
				model.setAreaAppraiser(this.getUserName(model
						.getAreaAppraiser()));
			} else {
				model.setAreaAppraiser(Util.trim(model.getAreaAppraiser()));
			}
			if (!Util.isEmpty(model.getHqAppraiser())) {
				model.setHqAppraiser(this.getUserName(model.getHqAppraiser()));
			} else {
				model.setHqAppraiser(Util.trim(model.getHqAppraiser()));
			}
			if (!Util.isEmpty(model.getAreaSendInfo())) {
				model.setApproveTime(model.getAreaSendInfo());
			}
			model.setUpdater(this.getUserName(model.getUpdater()));

		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	/**
	 * 查詢額度明細表Grid 資料 在聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL141m01b(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		L141M01A l141m01a = lms1411Service.findL141M01AByMainId(caseMainId);
		L120M01A l120m1a = lms1201Service.findL120m01aByMainId(l141m01a
				.getSrcMainId());
		String itemType = lmsService.checkL140M01AItemType(l120m1a);
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// pageSetting.addOrderBy("custId");
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		for (L140M01A l140m01a : l140m01as) {
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}

			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}
		}

		return result;
	}

	/**
	 * 引進簽報書 查詢額度明細表Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL120M01ASrc(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		pageSetting.addSearchModeParameters(SearchMode.IN,
				EloanConstants.DOC_STATUS,
				new String[] { FlowDocStatusEnum.婉卻.getCode(),
						FlowDocStatusEnum.已核准.getCode() });
		Properties pop2 = MessageBundleScriptCreator
				.getComponentResource(LMS1405S02Panel.class);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);
		// 檢核欄V為通過檢核且經過計算，O為 通過檢核 但尚未計算，X為尚未通過檢核
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		// dataReformatter.put("docStatus", new I18NFormatter(getComponent(),
		// "docStatus."));
		List<L140M01A> l140m01alist = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01alist) {
			if (UtilConstants.DEFAULT.是.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("V");
			} else if (UtilConstants.DEFAULT.否.equals(l140m01a.getChkYN())) {
				l140m01a.setChkYN("O");
			} else {
				l140m01a.setChkYN("X");
			}
			l140m01a.setDocStatus(getMessage("docStatus."
					+ l140m01a.getDocStatus()));
			// 用來暫放文件狀態
			l140m01a.setApprover(l140m01a.getDocStatus());
			if (UtilConstants.Cntrdoc.DataSrc.轉入額度明細表.equals(l140m01a
					.getDataSrc())) {
				// L140M01a.together=聯行
				l140m01a.setDataSrc(pop2.getProperty("L140M01a.together"));
			} else {
				l140m01a.setDataSrc("");
			}

		}
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 查詢MISLNRAT利率By幣別
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapMapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryMisLnratByCurr(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
		// "230");

		String mainId = Util.trim(params.getString(額度明細表mainId));
		String curr = Util.trim(params.getString("curr"));
		Page<Map<String, Object>> page = null;

		List<Map<String, Object>> beanList = misMislnratService
				.findAllBaseRateByCurr(curr);

		int start = pageSetting.getFirstResult();
		int pagNumber = pageSetting.getMaxResults();
		int end = start + pagNumber > beanList.size() ? start
				+ (beanList.size() - start) : start + pagNumber;
		List<Map<String, Object>> beanListnew = new ArrayList<Map<String, Object>>();
		for (int b = start; b < end; b++) {
			Map<String, Object> rowData = beanList.get(b);
			beanListnew.add(rowData);
		}

		Page<Map<String, Object>> returnPage = new Page<Map<String, Object>>(
				beanListnew, beanList.size(), pageSetting.getMaxResults(),
				pageSetting.getFirstResult());

		return new CapMapGridResult(returnPage.getContent(),
				returnPage.getTotalRow());
	}

	/**
	 * 查詢本案無追索買方額度資訊Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01s(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String type = Util.nullToSpace(params.getString("type"));
		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01S.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	/**
	 * 查詢本案借款人有無追索買方額度資訊/無追索買方於本行額度彙總 Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m02s(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String type = Util.nullToSpace(params.getString("type"));
		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M02S.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01t(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "flag",
				params.getString("flag"));

		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01T.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter
				.put("estateType",
						new CodeTypeFormatter(
								codeTypeService,
								"estateType",
								com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.ValSpaceDesc));

		Map<String, String> estateType = codeTypeService
				.findByCodeType("estateType");
		Map<String, String> estateSubType = codeTypeService
				.findByCodeType("estateSubType");

		class EstateTypeFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;
			Map<String, String> estateType;
			Map<String, String> estateSubType;

			public EstateTypeFormatter(Map<String, String> estateType,
					Map<String, String> estateSubType) {
				this.estateType = estateType;
				this.estateSubType = estateSubType;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140M01T l140m01t = (L140M01T) in;
				String estateType2 = l140m01t.getEstateType();
				if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType2)) {
					return estateType2
							+ " "
							+ estateType.get(estateType2)
							+ "-"
							+ Util.trim(estateSubType.get(l140m01t
									.getEstateSubType()));
				} else {
					return estateType2 + " " + estateType.get(estateType2);
				}

			}

		}

		formatter.put("estateType", new EstateTypeFormatter(estateType,
				estateSubType));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {

		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 設定土建融或個人戶額度名細表最終個人評等Grid(只顯示個人戶)
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryL140m01aForLandBuild(ISearch pageSetting,	PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		L120M01A meta = lms1201Service.findL120m01aByMainId(caseMainId);

		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		// pageSetting.addSearchModeParameters(SearchMode.IN, "lnType", new
		// String[] { "33", "34" });
		// pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE, "proPerty",
		// UtilConstants.Cntrdoc.Property.不變);
		// pageSetting.addSearchModeParameters(SearchMode.NOT_LIKE, "proPerty",
		// UtilConstants.Cntrdoc.Property.取消);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);
		List<L140M01A> contents = (List<L140M01A>) page.getContent();

		List<L140M01A> tmps = new ArrayList<L140M01A>();

		Map<String, Boolean> isPersonTmp = new HashMap<String, Boolean>();

		for (L140M01A content : contents) {
			String id = Util.trim(content.getCustId());
			String dup = Util.trim(content.getDupNo());
			String proPerty = content.getProPerty();

			// 使用cache，如果是相同id，就不用再查了
			if (isPersonTmp.containsKey(id + dup) && isPersonTmp.get(id + dup)) {
				tmps.add(content);
				continue;
			} else {
				Map<String, Object> custData = misCustdataService
						.findBUSCDByCustIdANdDupNo(content.getCustId(),
								content.getDupNo());
				String busCd = MapUtils.getString(custData, "BUSCD", "");

				if (busCd.equals("060000") || busCd.equals("130300")) {
					isPersonTmp.put(id + dup, true);
					tmps.add(content);
					continue;
				} else {
					isPersonTmp.put(id + dup, false);
				}

			}

			// if (LMSUtil.isContainValue(Util.trim(proPerty),
			// UtilConstants.Cntrdoc.Property.不變) ||
			// LMSUtil.isContainValue(Util.trim(proPerty),
			// UtilConstants.Cntrdoc.Property.取消)) {
			// } else {
			// String lnType = content.getLnType();
			// if ("33".equals(lnType) || "34".equals(lnType)) {
			// tmps.add(content);
			// }
			// }
		}

		CapGridResult result = new CapGridResult(tmps, tmps.size());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name));
		dataReformatter.put("creditDesc", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L140M01A meta = (L140M01A) in;
				String mainId = meta.getMainId();
				return lms1401Service.buildL140S03AStr(mainId);
			}
		});
		dataReformatter.put("isLandAndBuild", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L140M01A meta = (L140M01A) in;
				String lnType = meta.getLnType();
				if ("33".equals(lnType) || "34".equals(lnType)) {
					return "Y";
				}
				return "N";
			}
		});

		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * 設定土建融或個人戶額度名細表最終個人評等 Grid
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	public CapGridResult queryC120m01aFor140(ISearch pageSetting, PageParameters params) throws CapException {

		Map<String, Boolean> orderByMap = pageSetting.getOrderBy();
		if (orderByMap != null && orderByMap.containsKey("custNumber")) {
			orderByMap.put("custId", orderByMap.get("custNumber"));
			orderByMap.remove("custNumber");
		}

		Set<String> custIds = new HashSet<String>();

		String mainId = Util.trim(params.getString(額度明細表mainId));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(caseMainId);
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		String lnType = l140m01a.getLnType();
		if ("33".equals(lnType) || "34".equals(lnType)) {
			Map<String, Object> custData = misCustdataService
					.findBUSCDByCustIdANdDupNo(l140m01a.getCustId(),
							l140m01a.getDupNo());
			String busCd = MapUtils.getString(custData, "BUSCD", "");

			if (LMSUtil.isBusCode_060000_130300(busCd)) {
				custIds.add(l140m01a.getCustId());

				Set<L140M01I> l140m01is = l140m01a.getL140m01i();
				for (L140M01I l140m01i : l140m01is) {
					String type = l140m01i.getType();
					String rType = l140m01i.getRType();
					if ("1".equals(type) && "G".equals(rType)) {
						custIds.add(l140m01i.getRId());
					}
				}
				Set<L140M01J> l140m01js = l140m01a.getL140m01j();
				for (L140M01J l140m01j : l140m01js) {
					String custId = l140m01j.getCustId();
					String dupNo = l140m01j.getDupNo();
					Map<String, Object> custDataj = misCustdataService
							.findBUSCDByCustIdANdDupNo(custId, dupNo);
					String busCdj = MapUtils.getString(custDataj, "BUSCD", "");

					if (LMSUtil.isBusCode_060000_130300(busCdj)) {
						custIds.add(custId);
					}
				}
			}
		} else {
			Map<String, Object> custData = misCustdataService
					.findBUSCDByCustIdANdDupNo(l140m01a.getCustId(),
							l140m01a.getDupNo());
			String busCd = MapUtils.getString(custData, "BUSCD", "");
			if (LMSUtil.isBusCode_060000_130300(busCd)) {
				custIds.add(l140m01a.getCustId());
			}
		}

		pageSetting.addSearchModeParameters(SearchMode.IN, "custId",
				custIds.toArray(new String[0]));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, caseMainId);
		Page<? extends GenericBean> page = lms1401Service.findPage(
				C120M01A.class, pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s06aList(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140S06A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		formatter
				.put("intReg",
						new CodeTypeFormatter(
								codeTypeService,
								"lms140_intReg",
								com.mega.eloan.common.formatter.CodeTypeFormatter.ShowTypeEnum.Desc));

		formatter.put("checkYN", new IFormatter() {

			@Override
			public String reformat(Object in) throws CapFormatException {
				String txt = (String) in;
				if ("Y".equals(txt)) {
					return "O";
				}
				return "X";
			}
		});

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);

		return result;
	}

	/**
	 * 查詢資信檢表 平均動用率
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryCesWithBankSimpleCreditRatio(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.trim(params.getString(額度明細表mainId));
		String caseMainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L120M01A l120m01a = lms1201Service.findL120m01aByMainId(caseMainId);
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		String ownBrId = l140m01a.getOwnBrId();
		if (l120m01a != null) {
			ownBrId = l120m01a.getCaseBrId();
		}
		Page<Map<String, Object>> page = eloandbBASEService
				.findCesWithBankSimpleCreditRatio(pageSetting, ownBrId,
						l140m01a.getCustId(), l140m01a.getDupNo());
		List<Map<String, Object>> list = page.getContent();
		for (Map<String, Object> map : list) {
			String docstatus = Util.trim(map.get("docStatus"));
			map.put("docStatus", Util.isEmpty(docstatus) ? ""
					: getMessage("docStatus." + docstatus));

			String strJson = Util.trim(map.get("JSONOB"));
			if (Util.notEquals(strJson, "")) {
				JSONObject jsObj = JSONObject.fromObject(strJson);
				map.put("UPDATETIME", jsObj.optString("lnDataDate", ""));
			}

		}
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	@SuppressWarnings("unchecked")
	public CapMapGridResult queryBizCatList(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString(額度明細表mainId));
		List<Map<String, Object>> list = eloandbBASEService
				.queryBizCatList(MainId);
		String[] codeType = { UtilConstants.CodeTypeItem.授信科目, "bizCat" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		Map<String, Object> data = null;
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				String bizCat = Util.trim(MapUtils.getString(map, "BIZCAT"));
				String bizCatId = Util
						.trim(MapUtils.getString(map, "BIZCATID"));
				int bizCatSeqNum = MapUtils.getInteger(map, "SEQNUM", 0);
				String loanTPs = Util.trim(MapUtils.getString(map, "LOANTPS"));
				String loanTPsName = Util.trim(MapUtils.getString(map,
						"LOANTPSNAME"));

				StringBuilder builder = new StringBuilder("");
				String[] subject = Util.trim(loanTPs).split(
						UtilConstants.Mark.SPILT_MARK);
				for (String item : subject) {
					builder.append(builder.length() > 0 ? "、" : "");
					// 當項目值為空時不去找對應項目
					if (!Util.isEmpty(subject[0])) {
						builder.append(codeMap.get(
								UtilConstants.CodeTypeItem.授信科目).get(item));
					}
				}

				data = new HashMap<String, Object>();
				data.put("seq", NumConverter.toChineseNumber(bizCatSeqNum));
				data.put("bizCat", bizCat);
				data.put("bizCatId", bizCatId);
				data.put("bizCatStr", codeMap.get("bizCat").get(bizCat));
				data.put("loanTPs", loanTPs);
				data.put("loanTPsStr", builder.toString());
				data.put("loanTPsName", loanTPsName);
				newList.add(data);
			}
		}
		return new CapMapGridResult(newList, newList.size());
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s09a(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String MainId = Util.nullToSpace(params.getString(額度明細表mainId));
		String bizCat = Util.nullToSpace(params.getString("bizCat", ""));
		Integer bizCatId = Util.parseInt(params.getAsInteger("bizCatId", 1));
		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, MainId);
		if (Util.isNotEmpty(bizCat)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "bizCat",
					bizCat);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "bizCatId",
					bizCatId);
		}
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("seqNum");
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140S09A.class, pageSetting);

		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();

		class LoanTPsFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;
			Map<String, CapAjaxFormResult> codeMap;

			public LoanTPsFormatter(Map<String, CapAjaxFormResult> codeMap) {
				this.codeMap = codeMap;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S09A l140s09a = (L140S09A) in;

				StringBuilder builder = new StringBuilder("");
				String[] subject = Util.trim(l140s09a.getLoanTPs()).split(
						UtilConstants.Mark.SPILT_MARK);
				for (String item : subject) {
					builder.append(builder.length() > 0 ? "、" : "");
					// 當項目值為空時不去找對應項目
					if (!Util.isEmpty(subject[0])) {
						builder.append(codeMap.get(
								UtilConstants.CodeTypeItem.授信科目).get(item));
					}
				}
				return builder.toString();
			}

		}

		class BizCatFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;

			Map<String, CapAjaxFormResult> codeMap;

			public BizCatFormatter(Map<String, CapAjaxFormResult> codeMap) {
				this.codeMap = codeMap;
			}

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S09A l140s09a = (L140S09A) in;
				String bizCat = Util.nullToSpace(l140s09a.getBizCat());
				return codeMap.get("bizCat").get(bizCat).toString();
			}
		}

		class BizItemFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S09A l140s09a = (L140S09A) in;
				String bizCat = Util.nullToSpace(l140s09a.getBizCat());
				String str = "";
				if (bizCat.length() >= 1) {
					str = "bizItem" + bizCat.substring(0, 1);
				}
				Map<String, String> map = codeTypeService.findByCodeType(str);
				if (map == null)
					map = new LinkedHashMap<String, String>();

				String bizItemStr = "";
				String bizItem = Util.nullToSpace(l140s09a.getBizItem());
				if (Util.equals(bizItem, "XX")) { // 其他(自行輸入)
					bizItemStr = l140s09a.getItemStr();
				} else {
					bizItemStr = MapUtils.getString(map, bizItem, "");
				}

				return bizItemStr;
			}
		}

		class ContFormatter implements IBeanFormatter {
			private static final long serialVersionUID = 2501150363189246663L;

			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S09A l140s09a = (L140S09A) in;
				String oid = Util.trim(l140s09a.getOid());
				StringBuffer sb = new StringBuffer("");
				List<L140S09B> s09bs = lmsService.findL140s09bByMainId(oid);
				for (L140S09B l140s09b : s09bs) {
					sb.append(sb.length() > 0 ? "<br/>" : "");
					sb.append(l140s09b.getSeqNum()).append(". ")
							.append(l140s09b.getCont());
				}

				return sb.toString();
			}
		}

		String[] codeType = { UtilConstants.CodeTypeItem.授信科目, "bizCat" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);
		formatter.put("loanTPsStr", new LoanTPsFormatter(codeMap));
		formatter.put("bizCatStr", new BizCatFormatter(codeMap));
		formatter.put("bizItemStr", new BizItemFormatter());
		formatter.put("contStr", new ContFormatter());

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow(), formatter);
		return result;
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s09b(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		pageSetting.addOrderBy("seqNum");
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140S09B.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		return result;
	}

	@SuppressWarnings("unchecked")
	public CapGridResult queryOnL140M01A(ISearch pageSetting, PageParameters params) throws CapException {

		String custId = Util.trim(params.getString("queryCustId", ""));
		String dupNo = Util.trim(params.getString("queryDupNo", ""));
		String cntrNo = Util.trim(params.getString("queryCntrNo", ""));
		Boolean init = params.getAsBoolean("init", false);

		// 建霖說先放寬By ID
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "proPerty",
				UtilConstants.Cntrdoc.Property.不變);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.DOC_STATUS, FlowDocStatusEnum.已核准.getCode());
		pageSetting.setMaxResults(Integer.MAX_VALUE);
		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		/*
		 * if (!init && Util.equals(cntrNo, "")) { Properties pop =
		 * MessageBundleScriptCreator
		 * .getComponentResource(LMSCommomPage.class); //
		 * lmsL120M01A.error006=額度明細表額度序號不得為空白 throw new CapMessageException(
		 * pop.getProperty("lmsL120M01A.error006"), getClass()); }
		 * List<L140M01A> l140m01as = lms1401Service.findL140m01aBycntrNo(
		 * cntrNo, custId, dupNo);
		 */

		// J-110-0234_05097_B1001 Web e-Loan國內企金簽報書小規模RPA修改
		// J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
		String on109 = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_SUBSIDY_109_ON"));
		String on110 = Util.trim(lmsService
				.getSysParamDataValue("LMS_RESCUEITEM_SUBSIDY_110_ON"));

		String onStr = on109
				+ ((Util.notEquals(on109, "") && Util.notEquals(on110, "")) ? ","
						: "") + (on110);

		String[] on109Arr = new String[] {};
		if (Util.notEquals(onStr, "")) {
			on109Arr = onStr.split(",");
		}

		List<L140M01A> l140m01aTemp = new ArrayList<L140M01A>();
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		if (l140m01as != null && !l140m01as.isEmpty()) {
			for (L140M01A model : l140m01as) {
				String isRescue = Util.trim(model.getIsRescue());
				if (Util.equals(isRescue, "Y")) {
					L120M01C l120m01c = model.getL120m01c();
					if (l120m01c != null) {
						L120M01A l120m01a = lms1201Service
								.findL120m01aByMainId(l120m01c.getMainId());
						if (l120m01a != null) {
							/*
							 * 因為 l120m01a.getDocStatus() == "BKL" 的時候 會回傳
							 * approveBackDocStatus 這個欄位 所以用 是否等於 BKL 都會比對不到
							 * ===> approveBackDocStatus is null 代表 沒退回過 select
							 * * from lms.l120m01a where APPROVEBACKDOCSTATUS is
							 * not null and DOCSTATUS <> 'BKL'
							 */
							// if (Util.notEquals(l120m01a.getDocStatus(),
							// CreditDocStatusEnum.已覆核案件退回紀錄文件狀態.getCode())) {
							if (Util.isEmpty(Util.nullToSpace(l120m01a
									.getApproveBackDocStatus()))
									&& Util.equals(l120m01a.getDocStatus(),
											CreditDocStatusEnum.海外_已核准
													.getCode())) {
								if (Arrays.asList(on109Arr).contains(
										model.getRescueItem())) {
									model.setCaseNo(Util.toSemiCharString(model
											.getCaseNo()));
									l140m01aTemp.add(model);
								}
							}
						}
					}
				} else {
					continue;
				}
			}
		}

		CapGridResult result = new CapGridResult(l140m01aTemp,
				l140m01aTemp.size());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("ownBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.Name)); //
		dataReformatter.put("docStatus", new I18NFormatter("docStatus."));
		result.setDataReformatter(dataReformatter);
		return result;
	}

	/**
	 * J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
	 * 
	 * @param pageSetting
	 * @param params
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryAdcList(ISearch pageSetting, PageParameters params) throws CapException {
		String queryType = Util.trim(params.getString("queryType", ""));
		String queryCustId = Util.trim(params.getString("queryCustId", ""));
		String queryDupNo = Util.trim(params.getString("queryDupNo", ""));
		String queryCntrNo = Util.trim(params.getString("queryCntrNo", ""));
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		if (Util.isNotEmpty(queryType)) {
			HashSet<String> adcCaseNoSet = lmsService.getAdcCaseNoList(
					queryType, queryCustId, queryDupNo, queryCntrNo, "");
			for (String no : adcCaseNoSet) {
				data = new HashMap<String, Object>();
				data.put("adcCaseNo", no);
				newList.add(data);
			}
		}
		Page<Map<String, Object>> pages = LMSUtil.setPageMap(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	/**
	 * J-111-0397 RWA
	 **/
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryRwaL140m01a(ISearch pageSetting, PageParameters params) throws CapException {

		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		Boolean init = params.getAsBoolean("init", false);
		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		String itemType = Util.nullToSpace(params.getString("itemType"));
		Map<String, String> proPertyMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", itemType);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		List<L140M01A> l140m01aList = (List<L140M01A>) page.getContent();
		if (l140m01aList != null && l140m01aList.size() > 0) {
			for (L140M01A l140m01a : l140m01aList) {
				/*
				 * boolean chkProPerty = false; if (proPertyArr == null ||
				 * proPertyArr.length == 0) { chkProPerty = true; } else { for
				 * (String chkPP : proPertyArr) { if
				 * (LMSUtil.isContainValue(Util
				 * .nullToSpace(l140m01a.getProPerty()), chkPP)) { chkProPerty =
				 * true; break; } } } if(chkProPerty) {
				 */
				if (lmsService.chkCntrNoNeedRwa(l140m01a)) {
					data = new HashMap<String, Object>();
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("custId", Util.nullToSpace(l140m01a.getCustId())
							+ "　" + Util.nullToSpace(l140m01a.getDupNo()));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					String[] ppArr = Util.nullToSpace(l140m01a.getProPerty())
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer temp = new StringBuffer();
					for (String value : ppArr) {
						temp.append(temp.length() > 0 ? "、" : "");
						temp.append(Util.trim(proPertyMap.get(value)));
					}
					data.put("proPerty", temp.toString());
					String[] strArr = lmsService.getRwaApplyAmt(l140m01a);
					if (strArr.length >= 2) {
						data.put(
								"applyAmt",
								Util.nullToSpace(strArr[0])
										+ "　"
										+ NumConverter.addComma(Util
												.nullToSpace(strArr[1])));
					} else {
						data.put("applyAmt", "");
					}
					data.put("rItemD", Util.nullToSpace(l140m01a.getRItemD()));
					newList.add(data);
				}
			}
		}

		// 排序
		Collections.sort(newList, new Comparator<Map<String, Object>>() {
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				String custId1 = o1.get("custId").toString();
				String custId2 = o2.get("custId").toString();
				int a = custId1.compareTo(custId2);
				if (a != 0) {
					return a;
				}

				String cntrNo1 = o1.get("cntrNo").toString();
				String cntrNo2 = o2.get("cntrNo").toString();
				return cntrNo1.compareTo(cntrNo2);
			}
		});

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	/**
	 * // BIS
	 **/
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryBisL140m01a(ISearch pageSetting, PageParameters params) throws CapException {

		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
		Map<String, Object> data = null;
		Boolean init = params.getAsBoolean("init", false);
		if (init) { // 第一次load畫面先不查
			return new CapMapGridResult(newList, newList.size());
		}
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		Map<String, String> proPertyMap = codeTypeService
				.findByCodeType("lms1405s02_proPerty");
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.mainId", caseMainId);
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l120m01c.itemType", "1");
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Page<? extends GenericBean> page = lmsService.findPage(L140M01A.class,
				pageSetting);
		List<L140M01A> l140m01aList = (List<L140M01A>) page.getContent();
		if (l140m01aList != null && l140m01aList.size() > 0) {
			for (L140M01A l140m01a : l140m01aList) {
				if (lmsService.chkCntrNoNeedBis(l140m01a)) {
					data = new HashMap<String, Object>();
					data.put("oid", Util.nullToSpace(l140m01a.getOid()));
					data.put("custId", Util.nullToSpace(l140m01a.getCustId())
							+ "　" + Util.nullToSpace(l140m01a.getCustName()));
					data.put("cntrNo", Util.nullToSpace(l140m01a.getCntrNo()));
					String[] ppArr = Util.nullToSpace(l140m01a.getProPerty())
							.split(UtilConstants.Mark.SPILT_MARK);
					StringBuffer temp = new StringBuffer();
					for (String value : ppArr) {
						temp.append(temp.length() > 0 ? "、" : "");
						temp.append(Util.trim(proPertyMap.get(value)));
					}
					data.put("proPerty", temp.toString());
					data.put("bisApplyCurr",
							Util.nullToSpace(l140m01a.getCurrentApplyCurr()));
					data.put("bisApplyAmt", NumConverter.addComma(Util
							.nullToSpace(l140m01a.getCurrentApplyAmt())));
					data.put("bisRItemD",
							Util.nullToSpace(l140m01a.getRItemD()));
					newList.add(data);
				}
			}
		}

		// 排序
		Collections.sort(newList, new Comparator<Map<String, Object>>() {
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				String custId1 = o1.get("custId").toString();
				String custId2 = o2.get("custId").toString();
				int a = custId1.compareTo(custId2);
				if (a != 0) {
					return a;
				}

				String cntrNo1 = o1.get("cntrNo").toString();
				String cntrNo2 = o2.get("cntrNo").toString();
				return cntrNo1.compareTo(cntrNo2);
			}
		});

		Page<Map<String, Object>> pages = LMSUtil.getMapGirdDataRow(newList,
				pageSetting);
		return new CapMapGridResult(pages.getContent(), newList.size());
	}

	/**
	 * J-112-0357 新增敘做條件異動比較表
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140s11aList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Map<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
		orderBy.put("seqNum", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lmsService.findPage(L140S11A.class,
				pageSetting);

		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());

		return result;
	}

	public CapMapGridResult queryImportL140s11aList(ISearch pageSetting, PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		String cntrNo = null;
		if (l140m01a != null) {
			cntrNo = Util.nullToSpace(l140m01a.getCntrNo());
		}
		if (cntrNo == null) {
			List<Map<String, Object>> nullList = new ArrayList<Map<String, Object>>();
			return new CapMapGridResult(nullList, nullList.size());
		}
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));

		Page<Map<String, Object>> page = eloandbBASEService
				.queryOtherL140S11AByL140M01A(pageSetting, caseMainId, "1",
						cntrNo);

		/*
		 * pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		 * "l120m01c.mainId", caseMainId);
		 * pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		 * "l120m01c.itemType", "1");
		 * pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
		 * UtilConstants.Field.刪除時間, ""); if (cntrNo != null) {
		 * pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "cntrNo",
		 * cntrNo); } pageSetting.setMaxResults(Integer.MAX_VALUE);
		 * 
		 * 
		 * Page<? extends GenericBean> page = lms1401Service.findPage(
		 * L140M01A.class, pageSetting);
		 * 
		 * CapGridResult result = new CapGridResult(page.getContent(),
		 * page.getTotalRow());
		 * 
		 * return result;
		 */

		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢連保人Grid 資料
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL140m01i_Lgd(ISearch pageSetting, PageParameters params) throws CapException {
		// 建立主要Search 條件
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		String custId_s21b = Util.nullToSpace(params.getString("custId_s21b"));
		String cntrNo_s21b = Util.nullToSpace(params.getString("cntrNo_s21b"));
		String isGuarantorEffect_s21b = Util.nullToSpace(params
				.getString("isGuarantorEffect_s21b"));

		L140M01A l140m01a = lmsService.findL140M01AByL120m01cMainIdAndcntrNo(
				mainId, cntrNo_s21b, UtilConstants.Cntrdoc.ItemType.額度明細表);

		Map<String, Object> data = null;
		List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();

		Map<String, Object> guaMap = new HashMap<String, Object>();

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1401S08Panel.class);

		if (l140m01a != null) {
			// 保證人
			Set<L140M01I> l140m01is = l140m01a.getL140m01i();
			for (L140M01I l140m01i : l140m01is) {
				String type = Util.trim(l140m01i.getType());
				String rType = Util.trim(l140m01i.getRType());
				String rId = Util.trim(l140m01i.getRId());
				String rDupNo = Util.trim(l140m01i.getRDupNo());
				if (Util.equals(type, "2") && Util.equals(rType, "G")
						&& Util.notEquals(rId, "")
						&& Util.notEquals(rDupNo, "")) {
					if (!guaMap.containsKey(rId)) {
						data = new HashMap<String, Object>();
						data.put("rId", rId);
						data.put("rDupNo", rDupNo);
						data.put("rName", Util.trim(l140m01i.getRName()));
						data.put("rType", pop.getProperty("guaLgdView.rType_G"));

						newList.add(data);
						guaMap.put(rId, pop.getProperty("guaLgdView.rType_G"));
					}

				}
			}

			// 共同借款人
			// J-111-0510_05097_B1001 Web e-Loan企金簽報書增修LGD及無擔保回收率估算規則
			// 額度明細表共同借款人
			Set<L140M01J> l140m01js = l140m01a.getL140m01j();
			for (L140M01J l140m01j : l140m01js) {
				String custIdj = l140m01j.getCustId();
				String dupNoj = l140m01j.getDupNo();

				if (Util.isNotEmpty(custIdj) && Util.isNotEmpty(dupNoj)) {
					L120S01B l120s01b = lms1201Service.findL120s01bByUniqueKey(
							mainId, Util.trim(custIdj), Util.trim(dupNoj));
					if (l120s01b != null) {
						String busCode = Util.trim(l120s01b.getBusCode());
						L120S01A l120s01a = l120s01b.getL120s01a();
						if (Util.isNotEmpty(busCode)) {
							if (Util.notEquals(busCode, "060000")
									&& Util.notEquals(busCode, "130300")) {
								// 企業戶
								if (!guaMap.containsKey(custIdj)) {
									data = new HashMap<String, Object>();
									data.put("rId", custIdj);
									data.put("rDupNo", dupNoj);
									data.put("rName",
											Util.trim(l120s01a.getCustName()));
									data.put("rType", pop
											.getProperty("guaLgdView.rType_C"));

									newList.add(data);
									guaMap.put(custIdj, pop
											.getProperty("guaLgdView.rType_C"));
								} else {
									guaMap.put(
											custIdj,
											guaMap.get(custIdj)
													+ "/"
													+ pop.getProperty("guaLgdView.rType_C"));
								}
							}
						}
					}
				}
			}
		}

		// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
		String[] codeType = { "CRDType", "lms1205s01_Unit", "CountryCode",
				"lms1205s01_stockStatus", "lms1408_stkExcNm" };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		if (newList != null && newList.size() > 0) {
			for (Map<String, Object> map : newList) {
				String rId = Util.trim(MapUtils.getString(map, "rId"));
				String rDupNo = Util.trim(MapUtils.getString(map, "rDupNo"));

				// 評等
				List<?> rows1 = new ArrayList<Object>();
				rows1 = misdbBASEService.findMISMOWTBL1_selMow(rId, rDupNo);
				String crdType = "";
				String crdTYear = null;
				String gradeOrg = null;
				String gradeNew = null;
				if (rows1 != null && !rows1.isEmpty()) {
					Iterator<?> it1 = rows1.iterator();
					int count = 0;
					if (it1.hasNext()) {
						Map<?, ?> dataMap = (Map<?, ?>) it1.next();
						if (count == 0) { // 只做第一筆

							// 評等（公佈）日期
							String tCrdTYear = Util.trim(Util
									.nullToSpace(dataMap.get("MOWYMD")));
							if (Util.isNotEmpty(tCrdTYear)) {
								StringBuilder cdate = new StringBuilder();
								int year = Util.parseInt(tCrdTYear.substring(0,
										3)) + 1911;
								String month = tCrdTYear.substring(3, 5);
								String day = tCrdTYear.substring(5);
								cdate.append(String.valueOf(year)).append("-")
										.append(month).append("-").append(day);
								crdTYear = cdate.toString();
							} else {
								crdTYear = null;
							}

							// 最近一年
							if (crdTYear != null) {

								// 判斷二年內
								// [昨天 下午 04:52] 楊竺軒(風險控管處,高級辦事員)
								// 建霖好，麻煩保證人引進的評等範圍比照如下，請改為抓最近二年內最新一筆評等，謝謝~

								int adjDay = 0;
								int addedMonth = 24;
								String calcDate = null;
								calcDate = CapDate.formatDate(CapDate.addMonth(
										Util.parseDate(crdTYear), addedMonth),
										"yyyy-MM-dd");
								if (LMSUtil.cmp_yyyyMM(
										Util.parseDate(calcDate), ">=",
										Util.parseDate(CapDate
												.getCurrentDate("yyyy-MM-dd")))) {

									// 信評表類別
									crdType = "M"
											+ Util.trim(dataMap.get("MOWTYPE"));

									// 評等等級
									gradeOrg = Util.trim(Util
											.nullToSpace(dataMap.get("FR")));
									gradeNew = lmsLgdService.convertMowGrade(
											crdType, gradeOrg);

									// 特殊融資1~4等分別對應至PD等級之第6、7、9、13等
									// if ("MF".equals(crdType)
									// || "M5".equals(crdType)
									// || "MO".equals(crdType)) {
									// // 特殊融資模型評等等級1-2級;
									// if ("1".equals(gradeOrg)) {
									// gradeNew = "6";
									// } else if ("2".equals(gradeOrg)) {
									// gradeNew = "7";
									// } else if ("3".equals(gradeOrg)) {
									// gradeNew = "9";
									// } else {
									// gradeNew = "13";
									// }
									// }
								}
							} else {
								crdTYear = null;
							}

						}
					}
				}

				// J-114-XXX1 LGD合格保證人納入境外保證人
				// 外部評等
				String crdType2 = "";
				String crdTYear2 = null;
				String gradeOrg2 = null;
				String gradeNew2 = null;
				if (Util.equals(isGuarantorEffect_s21b, "2")) {
					Map<String, String> bestGrade = lmsLgdService
							.getCustBestElf338nGrade(rId, rDupNo);
					if (bestGrade != null) {
						crdType2 = Util.trim(MapUtils.getString(bestGrade,
								"CRDTYPE"));
						crdTYear2 = Util.trim(MapUtils.getString(bestGrade,
								"CRDTYEAR"));
						gradeOrg2 = Util.trim(MapUtils.getString(bestGrade,
								"GRADE"));
						gradeNew2 = Util.trim(MapUtils.getString(bestGrade,
								"NEWGRADE"));
					}
				}

				// J-114-XXX1 LGD合格保證人納入境外保證人
				// 股票代號
				String stkKind = "";
				String stockNum = "";
				if (Util.equals(isGuarantorEffect_s21b, "1")) {

					List<Map<String, Object>> listMap = misdbBASEService
							.selStock(rId);
					Map<String, Object> stkMap = new HashMap<String, Object>();
					// {stockAmtDate=2023-02-24, stockNum=1216, stockAmt=67.5,
					// stockStatus=1}

					if (!listMap.isEmpty()) {
						for (Map<String, Object> rstMap : listMap) {
							String tStkKind = Util.trim(rstMap.get("STKKIND"));
							if (Util.equals(tStkKind, "1")
									|| Util.equals(tStkKind, "2")) {
								stkKind = tStkKind;
								stockNum = Util.trim(rstMap.get("STKNO"));
								break;
							}
						}
					}
				}

				// 國別
				String ntCode = "";
				List<Map<String, Object>> rows2 = this.misCustdataService
						.findCustdataForList(rId, rDupNo);
				for (Map<String, Object> map1 : rows2) {

					// 國別註冊地
					// J-105-0233-001 Web
					// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
					// NATIONCODE->MOTHERNCODE
					if (Util.isNotEmpty(Util.trim(map1.get("MOTHERNCODE")))) {
						ntCode = Util.trim(map1.get("MOTHERNCODE"));
					} else {
						if (Util.isNotEmpty(Util.trim(map1.get("NATIONCODE")))) {
							ntCode = Util.trim(map1.get("NATIONCODE"));
						}
					}

					break;
				}

				// Util.trim(codeMap.get(CodeTypeEnum.國家代碼.getCode()).get(Util.trim(data.getNtCode())))

				// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
				// 本行所認可之海外交易所掛牌
				String stkCatNm = "";
				String stkCatNmShow = "";
				Map<String, Object> stkCatNmMap = eloandbBASEService
						.findC120M01A_selectGrarantorStkCatNm(rId);
				if (stkCatNmMap != null && !stkCatNmMap.isEmpty()) {
					String SN = Util
							.trim(MapUtils.getString(stkCatNmMap, "SN"));
					String ISSTK = Util.trim(MapUtils.getString(stkCatNmMap,
							"ISSTK"));
					String GUARANTORSTKCATNM = Util.trim(MapUtils.getString(
							stkCatNmMap, "GUARANTORSTKCATNM"));
					if (Util.equals(ISSTK, "1")) {
						stkCatNm = GUARANTORSTKCATNM;
						if (Util.notEquals(stkCatNm, "")) {
							stkCatNmShow = stkCatNm
									+ "."
									+ Util.trim(codeMap.get("lms1408_stkExcNm")
											.get(Util.trim(stkCatNm))) + "("
									+ SN + ")";
						}
					}
				}

				map.put("ntCode", Util.equals(ntCode, "") ? "" : ntCode);

				map.put("rType", MapUtils.getString(guaMap, rId));
				map.put("crdType", crdType);
				map.put("crdTypeNm",
						Util.equals(crdType, "")
								|| Util.equals(crdType,
										UtilConstants.Casedoc.CrdType.未評等) ? ""
								: Util.trim(codeMap.get("CRDType").get(
										Util.trim(crdType))));
				map.put("crdTYear",
						Util.equals(crdType, "")
								|| Util.equals(crdType,
										UtilConstants.Casedoc.CrdType.未評等) ? ""
								: crdTYear);
				map.put("gradeOrg", gradeOrg);
				map.put("gradeNew", gradeNew);

				// J-114-XXX1 LGD合格保證人納入境外保證人
				map.put("crdType2", crdType2);
				map.put("crdTypeNm2",
						Util.equals(crdType2, "") ? "" : Util.trim(codeMap.get(
								"CRDType").get(Util.trim(crdType2))));
				map.put("crdTYear2", crdTYear2);
				map.put("gradeOrg2", gradeOrg2);
				map.put("gradeNew2", gradeNew2);

				map.put("stockStatus", Util.equals(stkKind, "") ? "" : stkKind);
				map.put("stockStatusNm",
						Util.equals(stkKind, "") ? "" : Util.trim(codeMap.get(
								"lms1205s01_stockStatus").get(
								Util.trim(stkKind))));
				map.put("stockNum", stockNum);

				// J-113-0102_05097_B1001 修改e-Loan LGD之公司保證回收率估算規則
				map.put("stkCatNm", stkCatNm); // 本行所認可之海外交易所掛牌
				map.put("stkCatNmShow", stkCatNmShow);

			}
		}

		return new CapMapGridResult(newList, newList.size());
	}

	/**
	 * J-113-0035 ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意承諾待追蹤ESG連結條款」的登錄機制
	 */
	@SuppressWarnings({ "unchecked", "serial" })
	public CapGridResult queryL140s12aList(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params.getString(額度明細表mainId));

		// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Map<String, Boolean> orderBy = new LinkedHashMap<String, Boolean>();
		orderBy.put("seqNum", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		pageSetting.setMaxResults(Integer.MAX_VALUE);

		Page<? extends GenericBean> page = lmsService.findPage(L140S12A.class,
				pageSetting);
		
		L140M01A l140m01a = lms1401Service.findL140m01aByMainId(mainId);
		String esgSustainLoanType = "";
		if(l140m01a != null){
			esgSustainLoanType = l140m01a.getEsgSustainLoanType();
		}
		List<L140S12A> l140s12as = (List<L140S12A>) page.getContent();
		for (L140S12A data : l140s12as) {
			data.setEsgModel(esgSustainLoanType);//暫放 文件資訊 >> 永續績效連結授信類別	
		}
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("esgType", new IBeanFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				L140S12A meta = (L140S12A) in;
				StringBuilder builder = new StringBuilder("");
				String l140s12a_esgtype = meta.getEsgType();
				String[] formatEsgType = Util.trim(l140s12a_esgtype).split(UtilConstants.Mark.SPILT_MARK);
				Map<String, String> esgTypeMap = codeTypeService.findByCodeType("l140s12a_esgType");
				
				for (String type : formatEsgType) {
					builder.append(builder.length() > 0 ? "<br/>" : "");
					// 當項目值為空時不去找對應項目
					if (Util.isNotEmpty(type)) {
						builder.append(esgTypeMap.get(type));
					}
				}		
				return builder.toString();
			}
		});
		
		
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		result.setDataReformatter(dataReformatter);
		return result;
	}
}