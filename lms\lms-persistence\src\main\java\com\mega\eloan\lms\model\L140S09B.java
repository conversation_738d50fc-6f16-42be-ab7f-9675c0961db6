/* 
 * L140S09B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 其他敘作條件細項資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S09B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140S09B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * L140S09A.oid
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 業務分類<p/>
	 * L140S09A.bizCat
	 */
	@Size(max=3)
	@Column(name="BIZCAT", length=3, columnDefinition="CHAR(3)")
	private String bizCat;

	/**
	 * 項目代號<p/>
	 * L140S09A.bizItem
	 * codeType 規則 => "bizItem" +  bizCat第一碼
	 */
	@Size(max=2)
	@Column(name="BIZITEM", length=2, columnDefinition="CHAR(2)")
	private String bizItem;

	/** 順序 **/
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="SEQNUM", columnDefinition="DECIMAL(3,0)")
	private Integer seqNum;

	/**
	 * 內容代號<p/>
	 * codeType 規則 => "cont" +  bizCat第一碼 + bizItem
	 */
	@Size(max=2)
	@Column(name="CONTNO", length=2, columnDefinition="CHAR(2)")
	private String contNo;

	/** 內容 **/
	@Size(max=4096)
	@Column(name="CONT", length=4096, columnDefinition="VARCHAR(4096)")
	private String cont;

	/** 各欄位取名規則：項目的英文 + bizCat第一碼 + bizItem +"_"+ contNo +"_"+ 第幾個
	 * 用途、授信用途 => purpose
	 * 動用方式 => useWay
	 * 保證期限、授信期限 => period
	 * 承諾事項 => commitments
	 * 動用條件 => useCond
	 * 清償方式 => payOff
	 **/

	/**
	 * 用途<p/>
	 * 業務分類A=>用途
	 */
	@Size(max=300)
	@Column(name="PURPOSEA01_01_1", length=300, columnDefinition="VARCHAR(300)")
	private String purposeA01_01_1;

	/**
	 * 用途<p/>
	 * 業務分類B=>授信用途
	 */
	@Size(max=300)
	@Column(name="PURPOSEB01_01_1", length=300, columnDefinition="VARCHAR(300)")
	private String purposeB01_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類B=>動用方式=> 第一條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYB02_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayB02_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類B=>動用方式=> 第一條
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="USEWAYB02_01_2", columnDefinition="DECIMAL(3,0)")
	private Integer useWayB02_01_2;

	/**
	 * 動用方式<p/>
	 * 業務分類B=>動用方式=> 第二條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYB02_02_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayB02_02_1;

	/**
	 * 動用方式<p/>
	 * 業務分類B=>動用方式=> 第五條
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="USEWAYB02_05_1", columnDefinition="DECIMAL(15,2)")
	private BigDecimal useWayB02_05_1;

	/**
	 * 動用方式<p/>
	 * 業務分類C=>動用方式=> 第一條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYC01_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayC01_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類C=>動用方式=> 第一條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYC01_01_2", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayC01_01_2;

	/**
	 * 動用方式<p/>
	 * 業務分類D=>動用方式=> 第一條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYD02_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayD02_01_1;

	/**
	 * 期限<p/>
	 * 業務分類D=>保證期限=> 第一條
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="PERIODD03_01_1", columnDefinition="DECIMAL(2,0)")
	private Integer periodD03_01_1;

	/**
	 * 期限<p/>
	 * 業務分類D=>保證期限=> 第一條
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="PERIODD03_01_2", columnDefinition="DECIMAL(2,0)")
	private Integer periodD03_01_2;

	/**
	 * 動用方式<p/>
	 * 業務分類E=>動用方式=> 第一條
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="USEWAYE01_01_1", columnDefinition="DECIMAL(2,0)")
	private Integer useWayE01_01_1;

	/**
	 * 用途<p/>
	 * 業務分類G=>用途
	 */
	@Size(max=60)
	@Column(name="PURPOSEG01_01_1", length=60, columnDefinition="VARCHAR(60)")
	private String purposeG01_01_1;

	/**
	 * 用途<p/>
	 * 業務分類G=>用途
	 */
	@Size(max=14)
	@Column(name="PURPOSEG01_01_2", length=14, columnDefinition="VARCHAR(14)")
	private String purposeG01_01_2;

	/**
	 * 期限<p/>
	 * 業務分類H=>授信期限
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="PERIODH02_01_1", columnDefinition="DECIMAL(2,0)")
	private Integer periodH02_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類H=>動用方式=> 第一條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYH04_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayH04_01_1;

	/**
	 * 承諾事項<p/>
	 * 業務分類H=>承諾事項=> 第四條
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="COMMITMENTSH06_04_1", columnDefinition="DECIMAL(3,0)")
	private Integer commitmentsH06_04_1;

	/**
	 * 承諾事項<p/>
	 * 業務分類H=>承諾事項=> 第四條
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="COMMITMENTSH06_04_2", columnDefinition="DECIMAL(3,0)")
	private Integer commitmentsH06_04_2;

	/**
	 * 動用條件<p/>
	 * 業務分類I=>動用條件
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USECONDI03_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useCondI03_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYI04_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayI04_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYI04_01_2", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayI04_01_2;

	/**
	 * 動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYI04_01_3", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayI04_01_3;

	/**
	 * 動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYI04_01_4", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayI04_01_4;

	/**
	 * 承諾事項<p/>
	 * 業務分類I=>承諾事項=> 第五條
	 */
	@Digits(integer=15, fraction=2, groups = Check.class)
	@Column(name="COMMITMENTSI06_05_1", columnDefinition="DECIMAL(15,2)")
	private BigDecimal commitmentsI06_05_1;

	/**
	 * 期限<p/>
	 * 業務分類J=>授信期限
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="PERIODJ02_01_1", columnDefinition="DECIMAL(2,0)")
	private Integer periodJ02_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類J=>動用方式=> 第三條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYJ03_03_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayJ03_03_1;

	/**
	 * 動用方式<p/>
	 * 業務分類J=>動用方式=> 第四條
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYJ03_04_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayJ03_04_1;

	/**
	 * 清償方式<p/>
	 * 業務分類J=>清償方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="PAYOFFJ04_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal payOffJ04_01_1;

	/**
	 * 承諾事項<p/>
	 * 業務分類J=>承諾事項=> 第三條
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="COMMITMENTSJ05_03_1", columnDefinition="DECIMAL(5,0)")
	private Integer commitmentsJ05_03_1;

	/**
	 * 承諾事項<p/>
	 * 業務分類J=>承諾事項=> 第三條
	 */
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="COMMITMENTSJ05_03_2", columnDefinition="DECIMAL(5,0)")
	private Integer commitmentsJ05_03_2;

	/**
	 * 授信期限<p/>
	 * 業務分類N=>授信期限
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="PERIODN02_01_1", columnDefinition="DECIMAL(2,0)")
	private Integer periodN02_01_1;

	/**
	 * 動用方式<p/>
	 * 業務分類N=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYN03_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayN03_01_1;

	/**
	 * 清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="PERIODN04_01_1", columnDefinition="DECIMAL(3,0)")
	private Integer periodN04_01_1;

	/**
	 * 清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="PERIODN04_01_2", columnDefinition="DECIMAL(3,0)")
	private Integer periodN04_01_2;

	/**
	 * 清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="PERIODN04_01_3", columnDefinition="DECIMAL(3,0)")
	private Integer periodN04_01_3;
	
	/**
	 * 清償期限<p/>
	 * 業務分類B=>清償期限
	 */
	@Size(max=100)
	@Column(name="PERIODB03_01_1", length=300, columnDefinition="VARCHAR(100)")
	private String periodB03_01_1;
	
	/**
	 * 用途<p/>
	 * 業務分類O=>用途
	 */
	@Size(max=300)
	@Column(name="PURPOSEO01_01_1", length=300, columnDefinition="VARCHAR(300)")
	private String purposeO01_01_1;
	
	/**
	 * 動用方式<p/>
	 * 業務分類O=>動用方式
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="USEWAYO02_01_1", columnDefinition="DECIMAL(5,2)")
	private BigDecimal useWayO02_01_1;
	
	/** 是否納入貸後追蹤項目 **/
	@Size(max=2048)
	@Column(name="ISPOSTLOAN", length=2048, columnDefinition="VARCHAR(1)")
	private String isPostLoan;


	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * L140S09A.oid
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  L140S09A.oid
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得業務分類 **/
	public String getBizCat() {
		return this.bizCat;
	}
	/** 設定業務分類 **/
	public void setBizCat(String value) {
		this.bizCat = value;
	}

	/** 取得項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public String getBizItem() {
		return this.bizItem;
	}
	/** 設定項目代號<p/>codeType 規則 => "bizItem" +  bizCat第一碼 **/
	public void setBizItem(String value) {
		this.bizItem = value;
	}

	/** 取得順序 **/
	public Integer getSeqNum() {
		return this.seqNum;
	}
	/** 設定順序 **/
	public void setSeqNum(Integer value) {
		this.seqNum = value;
	}

	/** 取得內容代號<p/>codeType 規則 => "cont" +  bizCat第一碼 + bizItem **/
	public String getContNo() {
		return this.contNo;
	}
	/** 設定內容代號<p/>codeType 規則 => "cont" +  bizCat第一碼 + bizItem **/
	public void setContNo(String value) {
		this.contNo = value;
	}

	/** 取得內容 **/
	public String getCont() {
		return this.cont;
	}
	/** 設定內容 **/
	public void setCont(String value) {
		this.cont = value;
	}

	/**
	 * 取得用途<p/>
	 * 業務分類A=>用途
	 */
	public String getPurposeA01_01_1() {
		return this.purposeA01_01_1;
	}
	/**
	 *  設定用途<p/>
	 *  業務分類A=>用途
	 **/
	public void setPurposeA01_01_1(String value) {
		this.purposeA01_01_1 = value;
	}

	/**
	 * 取得用途<p/>
	 * 業務分類B=>授信用途
	 */
	public String getPurposeB01_01_1() {
		return this.purposeB01_01_1;
	}
	/**
	 *  設定用途<p/>
	 *  業務分類B=>授信用途
	 **/
	public void setPurposeB01_01_1(String value) {
		this.purposeB01_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類B=>動用方式=> 第一條
	 */
	public BigDecimal getUseWayB02_01_1() {
		return this.useWayB02_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類B=>動用方式=> 第一條
	 **/
	public void setUseWayB02_01_1(BigDecimal value) {
		this.useWayB02_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類B=>動用方式=> 第一條
	 */
	public Integer getUseWayB02_01_2() {
		return this.useWayB02_01_2;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類B=>動用方式=> 第一條
	 **/
	public void setUseWayB02_01_2(Integer value) {
		this.useWayB02_01_2 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類B=>動用方式=> 第二條
	 */
	public BigDecimal getUseWayB02_02_1() {
		return this.useWayB02_02_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類B=>動用方式=> 第二條
	 **/
	public void setUseWayB02_02_1(BigDecimal value) {
		this.useWayB02_02_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類B=>動用方式=> 第五條
	 */
	public BigDecimal getUseWayB02_05_1() {
		return this.useWayB02_05_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類B=>動用方式=> 第五條
	 **/
	public void setUseWayB02_05_1(BigDecimal value) {
		this.useWayB02_05_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類C=>動用方式=> 第一條
	 */
	public BigDecimal getUseWayC01_01_1() {
		return this.useWayC01_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類C=>動用方式=> 第一條
	 **/
	public void setUseWayC01_01_1(BigDecimal value) {
		this.useWayC01_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類C=>動用方式=> 第一條
	 */
	public BigDecimal getUseWayC01_01_2() {
		return this.useWayC01_01_2;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類C=>動用方式=> 第一條
	 **/
	public void setUseWayC01_01_2(BigDecimal value) {
		this.useWayC01_01_2 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類D=>動用方式=> 第一條
	 */
	public BigDecimal getUseWayD02_01_1() {
		return this.useWayD02_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類D=>動用方式=> 第一條
	 **/
	public void setUseWayD02_01_1(BigDecimal value) {
		this.useWayD02_01_1 = value;
	}

	/**
	 * 取得期限<p/>
	 * 業務分類D=>保證期限=> 第一條
	 */
	public Integer getPeriodD03_01_1() {
		return this.periodD03_01_1;
	}
	/**
	 *  設定期限<p/>
	 *  業務分類D=>保證期限=> 第一條
	 **/
	public void setPeriodD03_01_1(Integer value) {
		this.periodD03_01_1 = value;
	}

	/**
	 * 取得期限<p/>
	 * 業務分類D=>保證期限=> 第一條
	 */
	public Integer getPeriodD03_01_2() {
		return this.periodD03_01_2;
	}
	/**
	 *  設定期限<p/>
	 *  業務分類D=>保證期限=> 第一條
	 **/
	public void setPeriodD03_01_2(Integer value) {
		this.periodD03_01_2 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類E=>動用方式=> 第一條
	 */
	public Integer getUseWayE01_01_1() {
		return this.useWayE01_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類E=>動用方式=> 第一條
	 **/
	public void setUseWayE01_01_1(Integer value) {
		this.useWayE01_01_1 = value;
	}

	/**
	 * 取得用途<p/>
	 * 業務分類G=>用途
	 */
	public String getPurposeG01_01_1() {
		return this.purposeG01_01_1;
	}
	/**
	 *  設定用途<p/>
	 *  業務分類G=>用途
	 **/
	public void setPurposeG01_01_1(String value) {
		this.purposeG01_01_1 = value;
	}

	/**
	 * 取得用途<p/>
	 * 業務分類G=>用途
	 */
	public String getPurposeG01_01_2() {
		return this.purposeG01_01_2;
	}
	/**
	 *  設定用途<p/>
	 *  業務分類G=>用途
	 **/
	public void setPurposeG01_01_2(String value) {
		this.purposeG01_01_2 = value;
	}

	/**
	 * 取得期限<p/>
	 * 業務分類H=>授信期限
	 */
	public Integer getPeriodH02_01_1() {
		return this.periodH02_01_1;
	}
	/**
	 *  設定期限<p/>
	 *  業務分類H=>授信期限
	 **/
	public void setPeriodH02_01_1(Integer value) {
		this.periodH02_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類H=>動用方式=> 第一條
	 */
	public BigDecimal getUseWayH04_01_1() {
		return this.useWayH04_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類H=>動用方式=> 第一條
	 **/
	public void setUseWayH04_01_1(BigDecimal value) {
		this.useWayH04_01_1 = value;
	}

	/**
	 * 取得承諾事項<p/>
	 * 業務分類H=>承諾事項=> 第四條
	 */
	public Integer getCommitmentsH06_04_1() {
		return this.commitmentsH06_04_1;
	}
	/**
	 *  設定承諾事項<p/>
	 *  業務分類H=>承諾事項=> 第四條
	 **/
	public void setCommitmentsH06_04_1(Integer value) {
		this.commitmentsH06_04_1 = value;
	}

	/**
	 * 取得承諾事項<p/>
	 * 業務分類H=>承諾事項=> 第四條
	 */
	public Integer getCommitmentsH06_04_2() {
		return this.commitmentsH06_04_2;
	}
	/**
	 *  設定承諾事項<p/>
	 *  業務分類H=>承諾事項=> 第四條
	 **/
	public void setCommitmentsH06_04_2(Integer value) {
		this.commitmentsH06_04_2 = value;
	}

	/**
	 * 取得動用條件<p/>
	 * 業務分類I=>動用條件
	 */
	public BigDecimal getUseCondI03_01_1() {
		return this.useCondI03_01_1;
	}
	/**
	 *  設定動用條件<p/>
	 *  業務分類I=>動用條件
	 **/
	public void setUseCondI03_01_1(BigDecimal value) {
		this.useCondI03_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	public BigDecimal getUseWayI04_01_1() {
		return this.useWayI04_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類I=>動用方式
	 **/
	public void setUseWayI04_01_1(BigDecimal value) {
		this.useWayI04_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	public BigDecimal getUseWayI04_01_2() {
		return this.useWayI04_01_2;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類I=>動用方式
	 **/
	public void setUseWayI04_01_2(BigDecimal value) {
		this.useWayI04_01_2 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	public BigDecimal getUseWayI04_01_3() {
		return this.useWayI04_01_3;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類I=>動用方式
	 **/
	public void setUseWayI04_01_3(BigDecimal value) {
		this.useWayI04_01_3 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類I=>動用方式
	 */
	public BigDecimal getUseWayI04_01_4() {
		return this.useWayI04_01_4;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類I=>動用方式
	 **/
	public void setUseWayI04_01_4(BigDecimal value) {
		this.useWayI04_01_4 = value;
	}

	/**
	 * 取得承諾事項<p/>
	 * 業務分類I=>承諾事項=> 第五條
	 */
	public BigDecimal getCommitmentsI06_05_1() {
		return this.commitmentsI06_05_1;
	}
	/**
	 *  設定承諾事項<p/>
	 *  業務分類I=>承諾事項=> 第五條
	 **/
	public void setCommitmentsI06_05_1(BigDecimal value) {
		this.commitmentsI06_05_1 = value;
	}

	/**
	 * 取得期限<p/>
	 * 業務分類J=>授信期限
	 */
	public Integer getPeriodJ02_01_1() {
		return this.periodJ02_01_1;
	}
	/**
	 *  設定期限<p/>
	 *  業務分類J=>授信期限
	 **/
	public void setPeriodJ02_01_1(Integer value) {
		this.periodJ02_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類J=>動用方式=> 第三條
	 */
	public BigDecimal getUseWayJ03_03_1() {
		return this.useWayJ03_03_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類J=>動用方式=> 第三條
	 **/
	public void setUseWayJ03_03_1(BigDecimal value) {
		this.useWayJ03_03_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類J=>動用方式=> 第四條
	 */
	public BigDecimal getUseWayJ03_04_1() {
		return this.useWayJ03_04_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類J=>動用方式=> 第四條
	 **/
	public void setUseWayJ03_04_1(BigDecimal value) {
		this.useWayJ03_04_1 = value;
	}

	/**
	 * 取得清償方式<p/>
	 * 業務分類J=>清償方式
	 */
	public BigDecimal getPayOffJ04_01_1() {
		return this.payOffJ04_01_1;
	}
	/**
	 *  設定清償方式<p/>
	 *  業務分類J=>清償方式
	 **/
	public void setPayOffJ04_01_1(BigDecimal value) {
		this.payOffJ04_01_1 = value;
	}

	/**
	 * 取得承諾事項<p/>
	 * 業務分類J=>承諾事項=> 第三條
	 */
	public Integer getCommitmentsJ05_03_1() {
		return this.commitmentsJ05_03_1;
	}
	/**
	 *  設定承諾事項<p/>
	 *  業務分類J=>承諾事項=> 第三條
	 **/
	public void setCommitmentsJ05_03_1(Integer value) {
		this.commitmentsJ05_03_1 = value;
	}

	/**
	 * 取得承諾事項<p/>
	 * 業務分類J=>承諾事項=> 第三條
	 */
	public Integer getCommitmentsJ05_03_2() {
		return this.commitmentsJ05_03_2;
	}
	/**
	 *  設定承諾事項<p/>
	 *  業務分類J=>承諾事項=> 第三條
	 **/
	public void setCommitmentsJ05_03_2(Integer value) {
		this.commitmentsJ05_03_2 = value;
	}

	/**
	 * 取得授信期限<p/>
	 * 業務分類N=>授信期限
	 */
	public Integer getPeriodN02_01_1() {
		return this.periodN02_01_1;
	}
	/**
	 *  設定授信期限<p/>
	 *  業務分類N=>授信期限
	 **/
	public void setPeriodN02_01_1(Integer value) {
		this.periodN02_01_1 = value;
	}

	/**
	 * 取得動用方式<p/>
	 * 業務分類N=>動用方式
	 */
	public BigDecimal getUseWayN03_01_1() {
		return this.useWayN03_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類N=>動用方式
	 **/
	public void setUseWayN03_01_1(BigDecimal value) {
		this.useWayN03_01_1 = value;
	}

	/**
	 * 取得清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	public Integer getPeriodN04_01_1() {
		return this.periodN04_01_1;
	}
	/**
	 *  設定清償期限<p/>
	 *  業務分類N=>清償期限
	 **/
	public void setPeriodN04_01_1(Integer value) {
		this.periodN04_01_1 = value;
	}

	/**
	 * 取得清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	public Integer getPeriodN04_01_2() {
		return this.periodN04_01_2;
	}
	/**
	 *  設定清償期限<p/>
	 *  業務分類N=>清償期限
	 **/
	public void setPeriodN04_01_2(Integer value) {
		this.periodN04_01_2 = value;
	}

	/**
	 * 取得清償期限<p/>
	 * 業務分類N=>清償期限
	 */
	public Integer getPeriodN04_01_3() {
		return this.periodN04_01_3;
	}
	/**
	 *  設定清償期限<p/>
	 *  業務分類N=>清償期限
	 **/
	public void setPeriodN04_01_3(Integer value) {
		this.periodN04_01_3 = value;
	}
	
	/**
	 * 取得清償期限<p/>
	 * 業務分類B=>清償期限
	 */
	public String getPeriodB03_01_1() {
		return this.periodB03_01_1;
	}
	/**
	 *  設定清償期限<p/>
	 *  業務分類B=>清償期限
	 **/
	public void setPeriodB03_01_1(String value) {
		this.periodB03_01_1 = value;
	}
	
	/**
	 * 取得用途<p/>
	 * 業務分類O=>用途
	 */
	public String getPurposeO01_01_1() {
		return this.purposeO01_01_1;
	}
	/**
	 *  設定用途<p/>
	 *  業務分類O=>用途
	 **/
	public void setPurposeO01_01_1(String value) {
		this.purposeO01_01_1 = value;
	}
	
	/**
	 * 取得動用方式<p/>
	 * 業務分類O=>動用方式
	 */
	public BigDecimal getUseWayO02_01_1() {
		return this.useWayO02_01_1;
	}
	/**
	 *  設定動用方式<p/>
	 *  業務分類O=>動用方式
	 **/
	public void setUseWayO02_01_1(BigDecimal value) {
		this.useWayO02_01_1 = value;
	}
	
	/** 取得是否納入貸後追蹤項目 **/
	public String getIsPostLoan() {
		return this.isPostLoan;
	}
	/** 設定是否納入貸後追蹤項目 **/
	public void setIsPostLoan(String value) {
		this.isPostLoan = value;
	}
}
