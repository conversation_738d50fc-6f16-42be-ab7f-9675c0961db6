
package com.mega.eloan.lms.lms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.panels.L120S01MPanel;
import com.mega.eloan.lms.lms.panels.LMS1015S02PanelB1;
import com.mega.eloan.lms.lms.panels.LMS1115S02PanelB5;

/**
 * <pre>
 * 消金信用評等模型（LMS1035V00PageB , LMS1035S02PageB）含相同Panel
 * ⇒ 原始參照 LMS1115S02PageB
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1035v00b/{page}")
public class LMS1035V00PageB extends AbstractOverSeaCLSPage {

	@Override
	public void execute(ModelMap model, PageParameters params) throws Exception {
		renderJsI18N(AbstractOverSeaCLSPage.class);

		// 套用 「日本模型」 的【公司戶-基本資料】頁面
		model.addAttribute("_PanelB1_visible", true);
		new LMS1015S02PanelB1("PanelB1", true).processPanelData(model, params);

		// 套用 「原始畫面」的【公司戶-相關查詢資料】頁面
		model.addAttribute("_PanelB5_visible", true);
		new LMS1115S02PanelB5("PanelB5").processPanelData(model, params);

		Panel panel = new L120S01MPanel("l120s01mPanel");
		panel.processPanelData(model, params);
	}
	
	@Override
	protected String getViewName() {
		return getEloanPagePathByClass(getClass());
	}

}
