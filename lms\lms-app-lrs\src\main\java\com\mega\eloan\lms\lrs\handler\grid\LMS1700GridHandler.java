package com.mega.eloan.lms.lrs.handler.grid;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LrsUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.lrs.pages.LMS1700M01Page;
import com.mega.eloan.lms.lrs.service.LMS1700Service;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L170M01A;
import com.mega.eloan.lms.model.L170M01B;
import com.mega.eloan.lms.model.L170M01F;
import com.mega.eloan.lms.model.L170M01G;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.iisi.cap.dao.utils.SearchParameterUtil;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms1700gridhandler")
public class LMS1700GridHandler extends AbstractGridHandler {

	@Resource
	LMS1700Service lms1700Service;

	@Resource
	RetrialService retrialService;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;

	@Resource
	LMSService lmsService;

	Properties prop_lms1700m01 = MessageBundleScriptCreator
			.getComponentResource(LMS1700M01Page.class);

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapMapGridResult queryL170M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String gridview_param = Util.trim(params.getString("gridview_param"));

		// J-111-0622_05097_B1001 Web
		// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統
		// 1.國外部、金控總部分行之覆審作業改由北區營運中心辦理

		IBranch branch = branchService.getBranch(user.getUnitNo());
		String unitType = "";
		if (branch != null) {
			unitType = Util.trim(branch.getUnitType());
		}

		List<String> addSpecialBrList = new ArrayList<String>();
		addSpecialBrList.add(user.getUnitNo());

		if (Util.equals(unitType, UtilConstants.unitType.營運中心)) {

			// J-111-0622_05097_B1002 Web
			// e-Loan配合本行授信覆審作業須知111.12.1修訂，修改E-Loan系統企金授信覆審作業系統

			// 這段因為要能看到過去 007 201 的案子，L170A01A只有007或201，沒有931
			// 但是如果是931 自己做的覆審報告表，一個覆審報告表的L170A01A 會有 931 與 007兩筆資料，造成GRID 顯示重覆
			// 所以..................改成以DB直接塞授權

			// INSERT INTO LMS.L170A01A
			// SELECT GET_OID(),MAINID,PID,OWNUNIT,OWNER,AUTHTIME,'4','931' FROM
			// LMS.L170A01A
			// WHERE
			// AUTHUNIT In ('007','201') AND
			// MAINID NOT IN
			// (
			// SELECT MAINID FROM
			// LMS.L170M01A AS A
			// LEFT OUTER JOIN
			// LMS.L170A01A AS B
			// ON
			// A.MAINID = B.MAINID
			// WHERE
			// A.OWNBRID IN ('007','201') AND
			// A.CLTTYPE IN ('A','C') AND
			// B.AUTHUNIT In ('931')
			// )

			// 非營運中心單位931覆審
			// String[] branch_K = retrialService.getRetrailSpecialBranch();
			// for (String brnoK : branch_K) {
			// String newBranch = Util.trim(retrialService
			// .getRetrailNewBranch(brnoK));
			// if (Util.notEquals(brnoK, user.getUnitNo())
			// && Util.equals(newBranch, user.getUnitNo())) {
			// addSpecialBrList.add(brnoK);
			// }
			// }
		}

		// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		// "l170a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IN, "l170a01a.authUnit",
				addSpecialBrList.toArray(new String[0]));

		String[] docStatusArr = Util.trim(params.getString("docStatus")).split(
				"\\|");
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArr);

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		// J-106-0145-006 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		boolean ctlTypeA = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
				LrsUtil.CTLTYPE_主辦覆審);

		boolean ctlTypeB = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
				LrsUtil.CTLTYPE_自辦覆審);

		// J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
		boolean ctlTypeC = retrialService.chkCtlTypeByBrNo(user.getUnitNo(),
				LrsUtil.CTLTYPE_價金履約);

		String txCode = params.getString("txCode", "");

		String LMS_RETRIAL_CHG_BRANCH_DT = Util.trim(lmsService
				.getSysParamDataValue("LMS_RETRIAL_CHG_BRANCH_DT_"
						+ user.getUnitNo()));
		String retrialDate = CapDate.getCurrentDate("yyyy-MM-dd");
		if (Util.notEquals(LMS_RETRIAL_CHG_BRANCH_DT, "")
				&& Util.notEquals(LMS_RETRIAL_CHG_BRANCH_DT, "XXX")) {
			retrialDate = LMS_RETRIAL_CHG_BRANCH_DT; // 2023-04-01
		}

		Date retrialDateObj = CapDate.getDate(retrialDate, CapConstants.DEFAULT_DATE_FORMAT);
		if (Util.equals(txCode, "332018") || Util.equals(txCode, "332019")
				|| Util.equals(txCode, "332020")
				|| Util.equals(txCode, "332027")) {
			// 分行覆審報告表VIEW***********************************
			if (ctlTypeA && ctlTypeB) {
				// 國外部
			} else if (ctlTypeA || ctlTypeB) {
				// 分行、營運中心--只顯示主辦
				// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				// "ctlType", LrsUtil.CTLTYPE_主辦覆審);
				//
				pageSetting.addSearchModeParameters(SearchMode.IN, "ctlType",
						new String[] { LrsUtil.CTLTYPE_主辦覆審,
								LrsUtil.CTLTYPE_價金履約 });

			} else {
				// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				// "ctlType", LrsUtil.CTLTYPE_主辦覆審);

				pageSetting.addSearchModeParameters(SearchMode.IN, "ctlType",
						new String[] { LrsUtil.CTLTYPE_主辦覆審,
								LrsUtil.CTLTYPE_價金履約 });

			}

		} else {
			// 覆審組覆審報告表VIEW*********************************
			if (ctlTypeA && ctlTypeB && ctlTypeC) {
				// 金控總部
			} else if (!ctlTypeA && ctlTypeB) {
				// 一般分行 只有自辦覆審
				// 但國外部金控總部要能看到112/4/1以前的所有種類
				// 因為舊案，所以不用考慮149私人銀行處
				if (Util.equals(user.getUnitNo(), UtilConstants.BankNo.國外部)
						|| Util.equals(user.getUnitNo(),
								UtilConstants.BankNo.金控總部分行)) {
					// 112/4/1由北一區覆審，但要能看到過去的舊案
					pageSetting.addSearchModeParameters(SearchMode.OR,
							new SearchModeParameter(SearchMode.EQUALS,
									"ctlType", LrsUtil.CTLTYPE_自辦覆審),

							SearchParameterUtil.getMultiAndParameters(
									new SearchModeParameter(SearchMode.IN,
											"ctlType", new String[] {
													LrsUtil.CTLTYPE_主辦覆審,
													LrsUtil.CTLTYPE_價金履約 }),
									new SearchModeParameter(
											SearchMode.LESS_THAN,
											"retrialDate",retrialDateObj)));

				} else {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"ctlType", LrsUtil.CTLTYPE_自辦覆審);
				}

			} else if (ctlTypeA || ctlTypeB) {
				// 營運中心
				// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				// "ctlType", ctlTypeA ? LrsUtil.CTLTYPE_主辦覆審
				// : LrsUtil.CTLTYPE_自辦覆審);

				pageSetting.addSearchModeParameters(SearchMode.IN, "ctlType",
						new String[] { LrsUtil.CTLTYPE_主辦覆審,
								LrsUtil.CTLTYPE_價金履約 });
			} else {
				// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				// "ctlType", LrsUtil.CTLTYPE_主辦覆審);
				pageSetting.addSearchModeParameters(SearchMode.IN, "ctlType",
						new String[] { LrsUtil.CTLTYPE_主辦覆審,
								LrsUtil.CTLTYPE_價金履約 });
			}

		}

		String filetData = Util.trim(params.getString("filetData"));

		boolean searchWithFilter = false;
		if (Util.isNotEmpty(filetData)) {
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			if (true) {

				String ownBrId = Util.trim(jsoniletData.getString("ownBrId"));

				if (Util.isNotEmpty(ownBrId)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"ownBrId", ownBrId);
				}
			}

			if (true) {
				String DateStart = Util.trim(jsoniletData
						.getString("checkDateStart"));
				String DateEnd = Util.trim(jsoniletData
						.getString("checkDateEnd"));

				if (Util.isNotEmpty(DateStart)) {
					searchWithFilter = true;

					Date checkDateStart = Util.parseDate(DateStart);
					pageSetting.addSearchModeParameters(
							SearchMode.GREATER_EQUALS, "retrialDate",
							checkDateStart);
				}

				if (Util.isNotEmpty(DateEnd)) {
					searchWithFilter = true;

					Date checkDateEnd = Util.parseDate(DateEnd + " 23:59:59");
					pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
							"retrialDate", checkDateEnd);
				}
			}
			if (true) {
				String custId = Util.trim(jsoniletData.getString("custId"));
				if (Util.isNotEmpty(custId)) {
					searchWithFilter = true;

					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"custId", custId);
				}
			}
			if (true) {
				String conFlag = Util.trim(jsoniletData.getString("conFlag"));
				if (Util.isNotEmpty(conFlag)) {
					pageSetting.addSearchModeParameters(SearchMode.EQUALS,
							"l170m01f.conFlag", conFlag);
				}
			}
			// J-108-0163_10702_B1001 配合授審處於企金及消金「授信覆審系統」增加「戶名」搜尋欄位
			String custName = Util.trim(jsoniletData.getString("custName"));
			if (Util.isNotEmpty(custName)) {
				searchWithFilter = true;

				pageSetting.addSearchModeParameters(SearchMode.LIKE,
						"custName", "%" + Util.trim(custName) + "%");
			}
		}
		if (searchWithFilter == false && Util.isNotEmpty(gridview_param)) {
			if (Util.equals(gridview_param, "BEFORE_WEEK")) {
				ISearch my_search = retrialService.getMetaSearch();
				Date nextSunDay = CapDate.getDate(nextSunDay(), CapConstants.DEFAULT_DATE_FORMAT);
				my_search.addSearchModeParameters(SearchMode.OR,
						new SearchModeParameter(SearchMode.IS_NULL,
								"retrialDate", ""), new SearchModeParameter(
								SearchMode.LESS_THAN, "retrialDate",
								nextSunDay));// 小於下一個週日
				pageSetting.addSearchModeParameters(my_search);
			}
		}
		Page page = retrialService.findPage(L170M01A.class, pageSetting);
		List<L170M01A> src_list = page.getContent();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, String> brMap = new HashMap<String, String>();
		for (L170M01A model : src_list) {
			String brNo = model.getOwnBrId();
			brMap.put(brNo, branchService.getBranchName(brNo));
		}

		for (L170M01A model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();

			// J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
			LMSUtil.meta_to_map(row, model, new String[] { "oid", "mainId",
					"retrialDate", "custId", "dupNo", "custName",
					"mLoanPerson", "lastRetrialDate", "nCkdFlag", "ctlType" });
			String upFlag = "N";

			String retrialStaff = "";
			L170M01G l170m01g = retrialService
					.findL170M01G_first_L1_retrialTeam(model);
			if (l170m01g != null) {
				retrialStaff = l170m01g.getStaffName();
			}
			String condition = "";
			String branchComm = "";

			L170M01F l170m01f = retrialService.findL170M01F(model);
			if (l170m01f != null) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01f.getUpDate())) {
					upFlag = "Y";
				}

				if (Util.equals("1", l170m01f.getConFlag())) {
					condition = prop_lms1700m01.getProperty("label.conFlag.1");
				} else {
					condition = l170m01f.getCondition();
				}
				branchComm = l170m01f.getBranchComm();
			}

			row.put("upFlag", upFlag);
			row.put("ownBrId",
					model.getOwnBrId()
							+ LMSUtil.getDesc(brMap, model.getOwnBrId()));
			row.put("docStatus", lms1700Service.l170m01a_docStatusDesc(model));
			row.put("retrialStaff", retrialStaff);
			row.put("mainDocStatus", model.getDocStatus());
			row.put("projectNo", LrsUtil.extractProjectNo(model.getProjectNo()));
			row.put("condition", condition);
			row.put("branchComm", branchComm);

			// RPA狀態*****************************************************
			// J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
			if (!Util.isEmpty(Util.trim(model.getStatus()))) {
				row.put("status",
						prop_lms1700m01.getProperty("rpa.status."
								+ Util.trim(model.getStatus())));
			} else {
				row.put("status", "");
			}

			list.add(row);
		}

		/*
		 * 當資料跨多頁顯示時，此寫法只會出現第1頁的資料而已 Page<Map<String, Object>> n_page =
		 * LMSUtil.getMapGirdDataRow(list, pageSetting); return new
		 * CapMapGridResult(n_page.getContent(), n_page.getTotalRow());
		 */
		return new CapMapGridResult(list, page.getTotalRow());
	}

	private String nextSunDay() {
		Calendar c = Calendar.getInstance();
		c.setLenient(false);
		c.setTime(new Date());
		c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);

		return TWNDate.toAD(CapDate.shiftDays(c.getTime(), 7));
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapMapGridResult queryL170M01AFromL180(ISearch pageSetting,
			PageParameters params) throws CapException {

		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "pid",
				params.getString("pid"));
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);

		Page page = retrialService.findPage(L170M01A.class, pageSetting);
		List<L170M01A> src_list = page.getContent();
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		for (L170M01A model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, model, new String[] { "oid", "mainId",
					"retrialDate", "custId", "dupNo", "custName",
					"mLoanPerson", "lastRetrialDate", "nCkdFlag" });
			String upFlag = "N";

			String retrialStaff = "";
			L170M01G l170m01g = retrialService
					.findL170M01G_first_L1_retrialTeam(model);
			if (l170m01g != null) {
				retrialStaff = l170m01g.getStaffName();
			}
			String condition = "";
			String branchComm = "";

			L170M01F l170m01f = retrialService.findL170M01F(model);
			if (l170m01f != null) {
				if (CrsUtil.isNOT_null_and_NOTZeroDate(l170m01f.getUpDate())) {
					upFlag = "Y";
				}

				if (Util.equals("1", l170m01f.getConFlag())) {
					condition = prop_lms1700m01.getProperty("label.conFlag.1");
				} else {
					condition = l170m01f.getCondition();
				}
				branchComm = l170m01f.getBranchComm();
			}

			row.put("upFlag", upFlag);
			row.put("ownBrId", model.getOwnBrId());
			row.put("docStatus", lms1700Service.l170m01a_docStatusDesc(model));
			row.put("retrialStaff", retrialStaff);
			row.put("mainDocStatus", model.getDocStatus());
			row.put("projectNo", LrsUtil.extractProjectNo(model.getProjectNo()));
			row.put("condition", condition);
			row.put("branchComm", branchComm);
			// RPA狀態*****************************************************
			// J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
			if (!Util.isEmpty(Util.trim(model.getStatus()))) {
				row.put("status",
						prop_lms1700m01.getProperty("rpa.status."
								+ Util.trim(model.getStatus())));
			} else {
				row.put("status", "");
			}
			list.add(row);
		}

		/*
		 * 當資料跨多頁顯示時，此寫法只會出現第1頁的資料而已 Page<Map<String, Object>> n_page =
		 * LMSUtil.getMapGirdDataRow(list, pageSetting); return new
		 * CapMapGridResult(n_page.getContent(), n_page.getTotalRow());
		 */
		return new CapMapGridResult(list, page.getTotalRow());
	}

	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String fieldId = Util.nullToSpace(params.getString("fieldId"));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	public CapMapGridResult queryCESF101(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);

		String brNo = Util.trim(l170m01a.getOwnBrId());
		String custId = Util.trim(l170m01a.getCustId());
		String dupNo = Util.trim(l170m01a.getDupNo());

		List<Map<String, Object>> list = lms1700Service.getCesf101(brNo,
				custId, dupNo);
		return new CapMapGridResult(list, list.size());
	}

	public CapMapGridResult queryL170M01B(ISearch pageSetting,
			PageParameters params) throws CapException {

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String flag = Util.trim(params.getString("flag"));
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<L170M01B> l170m01b_list = retrialService
				.findL170M01B_orderBy(l170m01a);

		for (L170M01B l170m01b : l170m01b_list) {
			Map<String, Object> rows = new HashMap<String, Object>();
			if (Util.equals(flag, "PEO")) {
				if (l170m01b.getLnDataDate() != null) {
					continue;
				}
			}
			// ---
			rows.put("oid", Util.trim(l170m01b.getOid()));
			rows.put("cntrNo", Util.trim(l170m01b.getCntrNo()));
			rows.put("loanTP", Util.trim(l170m01b.getLoanTP()));
			rows.put("subject", Util.trim(l170m01b.getSubject()));
			rows.put("quotaCurr", Util.trim(l170m01b.getQuotaCurr()));
			rows.put(
					"quotaAmt",
					l170m01b.getQuotaAmt() == null ? "" : CrsUtil
							.amtDivide1000(l170m01b.getQuotaAmt()));
			rows.put("balCurr", Util.trim(l170m01b.getBalCurr()));
			rows.put(
					"balAmt",
					l170m01b.getBalAmt() == null ? "" : CrsUtil
							.amtDivide1000(l170m01b.getBalAmt()));
			rows.put("revolve", Util.equals("Y", l170m01b.getRevolve()) ? "Y"
					: "");
			rows.put("newCase",
					Util.equals("Y", l170m01b.getNewCase()) ? "【新貸】" : "");
			rows.put("createBy", (l170m01b.getLnDataDate() == null ? "新增" : ""));
			// ---
			list.add(rows);
		}
		return new CapMapGridResult(list, list.size());
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult query_L180M01A(ISearch pageSetting,
			PageParameters params) throws CapException {
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		L170M01A l170m01a = retrialService.findL170M01A_oid(mainOid);
		// 建立主要Search 條件
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.IN,
				EloanConstants.DOC_STATUS, new String[] {
						RetrialDocStatusEnum.已核准.getCode(),
						RetrialDocStatusEnum.已產生覆審名單報告檔.getCode() });
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l180a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				l170m01a.getOwnBrId());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		// 查一年內
		pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
				"defaultCTLDate",
				CapDate.addMonth(new Date(), -12));

		Page page = retrialService.findPage(L180M01A.class, pageSetting);
		List<L180M01A> list = page.getContent();
		for (L180M01A model : list) {
			model.setBranchId(model.getBranchId()
					+ " "
					+ Util.trim(branchService.getBranchName(model.getBranchId())));
		}

		return new CapGridResult(list, page.getTotalRow());
	}

	// J-110-0505_05097_B1001 Web
	// e-Loan授信覆審系統，新增引進覆審案件最新之授信案件批覆書功能，產生之PDF放置於附加檔案中，以供調閱
	public CapMapGridResult queryL140M01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String parStrArr = Util.trim(params.getString("parStrArr"));
		if (Util.isNotEmpty(parStrArr)) {
			Map<String, String> rptDescMap = _rptNoMap();

			Map<String, L170M01A> oid_l170m01a_map = new HashMap<String, L170M01A>();
			Map<String, L140M01A> oid_l140m01a_map = new HashMap<String, L140M01A>();
			if (true) {
				List<String> oid_list_l170m01a = new ArrayList<String>();
				List<String> oid_list_l140m01a = new ArrayList<String>();
				for (String parStr : parStrArr.split("\\|")) {
					String[] parArr = Util.trim(parStr).split("\\^");
					String l170m01a_oid = parArr[0];
					String l140m01a_oid = parArr[2];
					// ~~~
					oid_list_l170m01a.add(l170m01a_oid);
					oid_list_l140m01a.add(l140m01a_oid);
				}
				if (oid_list_l170m01a.size() > 0) {
					for (L170M01A l170m01a : retrialService
							.findL170M01A_oid(oid_list_l170m01a)) {
						oid_l170m01a_map.put(l170m01a.getOid(), l170m01a);
					}
				}
				if (oid_list_l140m01a.size() > 0) {
					for (L140M01A l140m01a : retrialService
							.findL140M01A_oid(oid_list_l140m01a)) {
						oid_l140m01a_map.put(l140m01a.getOid(), l140m01a);
					}
				}
			}

			for (String parStr : parStrArr.split("\\|")) {
				String[] parArr = Util.trim(parStr).split("\\^");
				String l170m01a_oid = parArr[0];
				String rptNo = parArr[1];
				String l140m01a_oid = parArr[2];

				L170M01A l170m01a = oid_l170m01a_map.get(l170m01a_oid);
				L140M01A l140m01a = oid_l140m01a_map.get(l140m01a_oid);

				Map<String, Object> row = new HashMap<String, Object>();

				row.put("rptNo", rptNo);
				row.put("rptNoDesc", LMSUtil.getDesc(rptDescMap, rptNo));
				row.put("custId", l170m01a.getCustId());
				row.put("dupNo", l170m01a.getDupNo());
				row.put("cName", l170m01a.getCustName());

				row.put("oid", l140m01a.getOid());
				row.put("caseNo", l140m01a.getCaseNo());
				row.put("cntrNo", l140m01a.getCntrNo());
				row.put("cntrCustid", l140m01a.getCustId());
				row.put("cntrDupno", l140m01a.getDupNo());
				row.put("cntrCName", l140m01a.getCustName());
				// ---
				list.add(row);
			}
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	private Map<String, String> _rptNoMap() {
		Map<String, String> m = new HashMap<String, String>();
		m.put("R12", "額度明細表");
		m.put("R13", "額度批覆表");
		return m;
	}
	
	// J-111-0554 配合授審處增進管理效益，修改相關功能程式 add 列印擔保品設定資料表
	public CapMapGridResult queryC100m01ByMainid(ISearch pageSetting,
			PageParameters params) throws CapException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String parStrArr = Util.trim(params.getString("parStrArr"));
		if (Util.isNotEmpty(parStrArr)) {
			Set<String> collmainId_Set = new HashSet<String>();
			for (String coll_mainid : parStrArr.split("\\|")) {
				collmainId_Set.add(coll_mainid);
			}
			
			if (collmainId_Set != null && !collmainId_Set.isEmpty()) {
				List<Map<String, Object>> dataList = retrialService
								.findCMS_C100m01byMainId(collmainId_Set);
				
				for (Map<String, Object> dataMap : dataList) {

					String MAINID = Util.trim(MapUtils.getString(
							dataMap, "MAINID"));
					String BRANCH = Util.trim(MapUtils.getString(
							dataMap, "BRANCH"));
					String CUSTID = Util.trim(MapUtils.getString(
							dataMap, "CUSTID"));
					String CUSTNAME = Util.trim(MapUtils.getString(
							dataMap, "CUSTNAME"));
					String COLLNO = Util.trim(MapUtils.getString(
							dataMap, "COLLNO"));
					String COLLTYP1 = Util.trim(MapUtils.getString(
							dataMap, "COLLTYP1"));
					String CNTRNO = Util.trim(MapUtils.getString(
							dataMap, "CNTRNO"));
					
					Map<String, Object> row = new HashMap<String, Object>();

					row.put("branch", BRANCH);
					row.put("custId", CUSTID);
					row.put("custName", CUSTNAME);
					row.put("collNo", COLLNO);
					row.put("collTyp1", COLLTYP1);
					row.put("cntrNo", CNTRNO);
					row.put("mainId", MAINID);
					
					list.add(row);
				}
			}
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list,
				pageSetting);
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}
}
