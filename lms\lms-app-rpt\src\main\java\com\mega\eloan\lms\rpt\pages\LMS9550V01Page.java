/* 
 * DEB1010V13Page.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.rpt.pages;

import java.util.Properties;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanButtonItem;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.rpt.panels.LMS9550R01Panel;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

/**
 * <pre>
 * DEB1010V13 報送作業-卡務聯徵檔案作業。
 * </pre>
 * 
 * @since 2012/7/16
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2012/7/16,<PERSON><PERSON><PERSON>,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/rpt/lms9550v01")
public class LMS9550V01Page extends AbstractEloanInnerView {

	public LMS9550V01Page() {
		super();

	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		Properties p = MessageBundleScriptCreator.getComponentResource(LMS9550R01Page.class);

		addToButtonPanel(model, LmsButtonEnum.Filter);
		addToButtonPanel(model, new EloanButtonItem("btnCreFile", "ui-icon", p.getProperty("button.creFile")));

		// renderJsI18N(LMS9550R01Page.class, "grid");
		renderJsI18N(LMS9550R01Page.class);
		setJavaScriptVar("branchVar", "");
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/rpt/LMS9550V01Page');");

		setupIPanel(new LMS9550R01Panel(PANEL_ID), model, params);
	}

}
