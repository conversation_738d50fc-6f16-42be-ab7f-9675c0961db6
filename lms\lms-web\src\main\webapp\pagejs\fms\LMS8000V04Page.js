var initDfd = $.Deferred(), inits = {
    fhandle: "lms8000m01formhandler",
    ghandle: "lms8000gridhandler"
};

//
var result_s = CommonAPI.loadCombos(["postLoan_handlingStatus"]);

pageJsInit(function() {
	$(function(){
		initDfd = initDfd || new $.Deferred();
		initAll = initAll || new $.Deferred();
	    $("#handlingStatus").setItems({
	        item: result_s.postLoan_handlingStatus,
	        format: "{key}"
	    });
	
	    openFilterBox();
	
	    function setOverSeaHideUI(overSeaHideVal){
	        if(overSeaHideVal){
	            $("span[class*=overSeaHide]").hide();
	            $("span[class*=overSeaShow]").show();
	        } else {
	            $("span[class*=overSeaHide]").show();
	            $("span[class*=overSeaShow]").hide();
	        }
	    }
	
	    function openFilterBox(){
	        var $filterForm = $("#filterForm");
	        $filterForm.reset();            // 初始化
	
	        setOverSeaHideUI(userInfo.isOverSea);
	
	        var tDate = new Date();
	        tDate.setFullYear(tDate.getFullYear() - 1);
	        $("#startDate").val(tDate.getFullYear() + "-" + (tDate.getMonth() < 9 ? "0" : "") + (tDate.getMonth() + 1) + "-" + (tDate.getDate() < 10 ? "0" : "") + tDate.getDate());
	        $("#endDate").val(API.getToday());
	
	        // 預設 未辦理、辦理中
	        $("input[name='handlingStatus'][value='1']").attr("checked", true);
	        $("input[name='handlingStatus'][value='2']").attr("checked", true);
	
	        $("#filterBox").thickbox({
	            // LMS8000V01.title=請輸入欲查詢項目：
	            title: i18n.lms8000v01["LMS8000V01.title"],
	            width: 600,
	            height: 300,
	            modal: true,
	            valign: "bottom",
	            align: "center",
	            i18n: i18n.def,
	            buttons: {
	                "sure": function(){
	                    if (!$("#filterForm").valid()) {
	                        return;
	                    }
	
	                    $.ajax({
	                        type: "POST",
	                        handler: inits.fhandle,
	                        data: $.extend($filterForm.serializeData(),{
	                            formAction: "queryAndAdd",
	                            openFrom: $("#openFrom").val()
	                        }),
	                        success: function(json){
	                            if(json.hasData){
	                                var urlStr = "../fms/lms8000m01/01";
	                                if($("#openFrom").val() == "lrs"){
	                                    urlStr = "../../fms/lms8000m01/01";
	                                }
	                                $.form.submit({
	                                    url: urlStr,
	                                    data: {
	                                        oid: json.oid,
	                                        custId: json.custId,
	                                        dupNo: json.dupNo,
	                                        custName: json.custName,
	                                        cntrNo: json.cntrNo,
	                                        loanNo: json.loanNo,
	                                        mainId: json.mainId,
	                                        mainOid: json.oid,
	                                        mainDocStatus: viewstatus,
	                                        txCode: txCode
	                                    },
	                                    target: json.mainId
	                                });
	                            } else {
	                                return CommonAPI.showMessage(i18n.def["noData"]);
	                            }
	                        }
	                    });
	
	                    $.thickbox.close();
	                },
	                "cancel": function(){
	                    API.confirmMessage(i18n.def['flow.exit'], function(res){
	                        if (res) {
	                            $.thickbox.close();
	                        }
	                    });
	                }
	            }
	        });
	    }
	}
)});