/* 
 * C241M01EDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C241M01E;


/** 覆審報告表簽章欄檔 **/
public interface C241M01EDao extends IGenericDao<C241M01E> {

	C241M01E findByOid(String oid);
	
	List<C241M01E> findByMainId(String mainId);
	
	C241M01E findByBranchTypeStaffJob(String mainId,String custId,String dupNo,String branchType,String staffJob);
	
	C241M01E findByUniqueKey(String mainId, String custId, String dupNo, String branchType, String branchId, String staffNo, String staffJob);

	List<C241M01E> findByIndex01(String mainId, String custId, String dupNo, String branchType, String branchId, String staffNo, String staffJob);
	
	List<C241M01E> findByCustIdDupId(String custId,String DupNo);
}