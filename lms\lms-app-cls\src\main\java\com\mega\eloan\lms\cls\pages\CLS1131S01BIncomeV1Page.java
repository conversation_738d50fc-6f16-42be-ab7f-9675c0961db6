package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.service.CLSService;


/**
 * J-110-0380 在 CLS1131S01Page.js 查找 cls/cls1131s01bincomev (後面不加 數字) <br/>
 * 因為消金處一直改版, 會把 c101s01b.incomeDetailVer 傳到 js前端 <br/>
 * 在 js 去組合出 url 是 /cls1131s01bincomev1 或 /cls1131s01bincomev2 或 /cls1131s01bincomev3 ...........
 */
@Controller
@RequestMapping("/cls/cls1131s01bincomev1")
public class CLS1131S01BIncomeV1Page extends AbstractEloanForm {

	@Autowired
	CLSService clsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(AbstractEloanPage.class);
		setJavaScriptVar("needReset", params.getString("needReset"));
		model.addAttribute("showBtnDW_OTS_TRPAYLG",
				clsService.is_function_on_codetype("J-111-0128"));
	}
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return null;
	}

	@Override
	public void afterExecute(ModelMap model, PageParameters parameters) {
		super.afterExecute(parameters);
		// UPGRADE: 前端須配合改Thymeleaf的樣式
		// remove("_headerPanel");
		model.addAttribute("showHeader", false); // 不顯示 _headerPanel
	}
}
