package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C340M01A;

/** 消金契約書主檔 **/
public interface C340M01ADao extends IGenericDao<C340M01A> {

	C340M01A findByOid(String oid);
	
	C340M01A findByMainId(String mainId);
	
	List<C340M01A> findByCtrTypeContrNumber_OrderByCreateTimeDesc(String ctrType, String contrNumber);
	List<C340M01A> findByPloanCtrNo(String ploanCtrNo);
	List<C340M01A> findByPloanCtrNo_OrderByCreateTimeAsc(String ploanCtrNo);
	List<C340M01A> findByPloanCtrNo_ctrType(String ploanCtrNo, String ctrType);

	List<C340M01A> findByNotifyT1TS(String[] ctrType_arr);
	List<C340M01A> findByCustId(String custId,String ctrType);
	
	List<C340M01A> findByCustId_ctrTypeA_ploanCtrStatus9_tabMainId_orderBy_ploanCtrBegDateDesc(String custId, String tabMainId);
	List<C340M01A> findByCustId_ctrTypeA_ctrTypeB_ploanCtrStatus9_tabMainId_orderBy_ploanCtrBegDateDesc(String custId, String tabMainId,String[] ctrType_arr);
	C340M01A findByCaseMainid(String caseMainid,String[] ctrType_arr);
	List<C340M01A> findByCtrTypeA_isNeedACH_misFlag_orderBy_ploanCtrBegDateDesc();
	List<C340M01A> findC340M01ACompleteOverExprDate(Date startDate, String[] ctrTypes);
	List<C340M01A> findByPloanCtrBegDate_ctrTypeC(String ploanCtrBegDate);
	C340M01A findByCaseMainid_ctrTypeA(String caseMainid);
	List<C340M01A> findNotifypLoanSendMail_ctrTypeA();
	List<C340M01A> findNotifypLoanSendMail(String[] ctrTypes);
	/**
	 * J-113-0435 動審表新增EMAIL驗證及提醒
	 * 取得最新一筆對保完成簽報書
	 * @param caseMainid
	 * @return
	 */
	C340M01A findByCaseMainid_PloanCtrStatus9(String caseMainid);
}