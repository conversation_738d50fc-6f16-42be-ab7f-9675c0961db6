package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.model.Page;

import com.mega.eloan.lms.model.L140M01ATMP1;

/** 授信簽報書主檔 **/
public interface L140M01ATMP1Dao extends IGenericDao<L140M01ATMP1> {

	public List<L140M01ATMP1> findByUserId(String userId);

	public int delByUserId(String userId);

	public Page<L140M01ATMP1> findL140m01atmp1UserIdForSearch(ISearch search,
			String userId);

	public int delByUid(String uid);

	public Page<L140M01ATMP1> findL140m01atmp1ByUidForSearch(ISearch search,
			String uid);

	public List<L140M01ATMP1> findByUid(String uid);

}