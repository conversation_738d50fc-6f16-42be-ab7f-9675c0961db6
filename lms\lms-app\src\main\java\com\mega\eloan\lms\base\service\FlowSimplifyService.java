package com.mega.eloan.lms.base.service;

import java.util.Map;

import tw.com.iisi.cap.service.ICapService;
import tw.com.jcs.flow.FlowInstance;


public interface FlowSimplifyService extends ICapService {
	/**
	 * 流程啟動
	 * @param flowName
	 * @param mainOid
	 * @param userId
	 * @param unitNo
	 */
	FlowInstance flowStart(String flowName, String mainOid, String userId, String unitNo);
	
	FlowInstance flowStart(String flowName, String mainOid, String userId, String unitNo, Map<String, Object> m);
	/**
	 * 流程控管(抓 default SSO 的資料)
	 * 
	 * @param mainOid
	 * @param decisionExpr 動作(核准||退回)
	 */
	void flowNext(String mainOid, String decisionExpr);
	
	void flowNext(String mainOid, String decisionExpr, Map<String, String> m);
	
	/**
	 * 流程控管(傳入指定的 userId, deptId. 例如:批次執行的 userId 為 BATCH)
	 * 
	 * @param mainOid
	 * @param decisionExpr 動作(核准||退回)
	 */
	void flowNext(String mainOid, String decisionExpr, String userId, String deptId);
	/**
	 * 是否已經有對應的流程
	 * @param mainOid
	 * @return
	 */
	boolean flowExist(String mainOid);
	
	/**
	 * 流程取消
	 * <br>註:在 DeleteBatchServiceImpl 中, 會針對 deleteTable.properties 內設定的 table
	 * <BR>將有 DELETEDTIME 的資料, 呼叫 flowService.cancel(oid)
	 * @param mainOid
	 */
	void flowCancel(String mainOid);
}
