/* 
 *ObsdbELF388ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.lms.obsdb.service.ObsdbELF388Service;

/**
 * <pre>
 * 核准額度資料檔  ELF388
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
@Service
public class ObsdbELF388ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF388Service {
	@Override
	public void insert(String BRNID, Object[] dataList) {
		this.getJdbc(BRNID).update("ELF388.insert", dataList);

	}

	@Override
	public void update(String BRNID, Object[] dataList) {
		this.getJdbc(BRNID).update("ELF388.update", dataList);

	}

	@Override
	public List<Map<String, Object>> selByQuotano(String BRNID, Object[] args) {
		return this.getJdbc(BRNID).queryForList("ELF388.selByQuotano", args);

	}
}
