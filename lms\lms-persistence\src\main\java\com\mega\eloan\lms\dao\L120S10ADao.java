/* 
 * L120S10ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S10A;

/** 微型企業明細檔 **/
public interface L120S10ADao extends IGenericDao<L120S10A> {

	L120S10A findByOid(String oid);
	
	List<L120S10A> findByMainId(String mainId);

	List<L120S10A> findByIndex01(String mainId);
	
	public List<L120S10A> findByMainIdAndCustIdDupNo(String mainId,
			String custId, String dupNo);
	
	public List<L120S10A> findByMainIdAndCustName(String mainId, 
			String custName);
}