package com.mega.eloan.lms.cls.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.report.ReportGenerator;

import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfStamper;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.cls.report.CLS1220R14RptService;

@Service("cls1220r14rptservice")
public class CLS1220R14RptServiceImpl implements CLS1220R14RptService {
	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1220R14RptServiceImpl.class);

	@Override
	public byte[] gen_jcic_pdf_withWaterMark(String htmlStr, String watermarkStr)
			throws Exception {
		// J-113-0068 消金進件管理ixml功能中聯徵資料T50~T52於收回檔案時轉pdf存入
		return getDocumentWithWaterMark(to_byte_array(gen_pdf_jcic(htmlStr)),
				watermarkStr);
	}

	private OutputStream gen_pdf_jcic(String htmlStr) throws Exception {
		Locale locale = LMSUtil.getLocale();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		// =========================
		String rpt_path = "report/cls/CLS1220R14" + "_" + locale.toString()
				+ ".rpt";

		ReportGenerator generator = new ReportGenerator(rpt_path);
		String htmlStr_n = htmlStr.replace("<font size=5>", "<font size=3>")
				.replace("<font size=4>", "<font size=2>");
		String[] strArr = htmlStr_n.split("\r|\n", 0);

		StringBuilder tempHtmlSb = new StringBuilder();
		boolean replaceSpace = false;
		for (String subStr : strArr) {
			tempHtmlSb.append("<p align=left>");
			if (replaceSpace) {
				tempHtmlSb.append(subStr.replaceAll(" ", "&nbsp;"));
				replaceSpace = false;
			} else {
				tempHtmlSb.append(subStr);
			}
			tempHtmlSb.append("</p>");
			if (subStr.contains("電子密章")) {
				replaceSpace = true;
			}
		}
		StringBuilder finalSb = new StringBuilder();
		String subStr1 = (tempHtmlSb.toString()).split("<pre>", 0)[1];
		finalSb.append(subStr1.split("</pre>", 0)[0]);

		rptVariableMap.put("htmlStr", finalSb.toString());

		generator.setVariableData(rptVariableMap);
		OutputStream outputStream = generator.generateReport();

		return outputStream;
	}

	// https://stackoverflow.com/questions/52748248
	private byte[] getDocumentWithWaterMark(byte[] documentBytes,
			String waterMark_text) {
		BaseFont bfont;
		String ttcFile = null;
		float font_size = 24;
		float rotation = 60;
		;
		float opacity_depth_color = 0.1f;
		try {
			ttcFile = PropUtil.getProperty("ttcFile.dir")
					+ PropUtil.getProperty("itext.mingliuFile") + ",0";
			bfont = BaseFont.createFont(ttcFile, BaseFont.IDENTITY_H,
					BaseFont.NOT_EMBEDDED);
			// ===========================
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			// pdf
			PdfReader reader = new PdfReader(documentBytes);
			int n = reader.getNumberOfPages();
			PdfStamper stamper = new PdfStamper(reader, outputStream);
			// text watermark
			// 用 Font.BOLD 的寫法，不能顯示中文字 Font font = new Font(Font.BOLD, 36);
			Font font = new Font(bfont, font_size);
			Phrase phrase = new Phrase(waterMark_text, font);
			// transparency
			PdfGState gs1 = new PdfGState();
			gs1.setFillOpacity(opacity_depth_color);
			// properties
			PdfContentByte over;
			Rectangle pagesize;
			float x, y;
			// loop over every page (in case more than one page)
			for (int i = 1; i <= n; i++) {
				pagesize = reader.getPageSizeWithRotation(i);
				x = (pagesize.getLeft() + pagesize.getRight()) / 2;
				y = (pagesize.getTop() + pagesize.getBottom()) / 2;
				over = stamper.getOverContent(i);
				over.saveState();
				over.setGState(gs1);
				// add text
				ColumnText.showTextAligned(over, Element.ALIGN_CENTER, phrase,
						x, y, rotation);
				over.restoreState();
			}
			stamper.close();
			reader.close();
			return outputStream.toByteArray();
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
			return documentBytes;
		}
	}

	private byte[] to_byte_array(OutputStream os) {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) os;
			return baos.toByteArray();
		} catch (Exception e) {
			LOGGER.error(StrUtils.getStackTrace(e));
		} finally {
			IOUtils.closeQuietly(baos);
		}
		return null;
	}

}
