package tw.com.jcs.flow.node;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import ognl.Ognl;
import ognl.OgnlException;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * ScriptNode
 * TODO
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public class ScriptNode extends FlowNode {

    Object code;

    /**
     * 設定為OGNL表達式
     * 
     * @param code
     */
    public void setCode(String code) {
        try {
            this.code = Ognl.parseExpression(code);
        } catch (OgnlException e) {
            throw new FlowException(e);
        }
    }

    /**
     * 找到目前的流程節點，結束目前的流程，獲取由指定根物件上的給定預編譯表達式表示的值，前往目前節點
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();
        finishCurrentNode(instance);

        // 執行Script
        try {
            // 為了弱掃做此調整, 將 Ognl 取值改用 reflection 方式呼叫
            // Ognl.getValue(code, instance.getData(), createContext(instance));
            Method method = Ognl.class.getDeclaredMethod("getValue", Object.class, Map.class, Object.class);
            method.invoke(null, code, instance.getData(), createContext(instance));
        } catch (Exception e) {
            throw new FlowException("error when ScriptNode.next()", e);
        }

        changeToThisNode(instance);
        instance.next();
    }

    /**
     * 建立一個Map放入傳入的資料
     * 
     * @param instance
     * @return map
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static Map createContext(FlowInstanceImpl instance) {
        Map ctx = new HashMap();
        ctx.put("this", instance);
        ctx.put("data", instance.getData());
        return ctx;
    }

}
