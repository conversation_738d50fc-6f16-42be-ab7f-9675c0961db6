/* 
 * LMS9091GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.fms.handler.grid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.fms.pages.LMS9091V02Page;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("lms9091gridhandler")
public class LMS9091GridHandler extends AbstractGridHandler {
	/**
	 * 試算期付金日期對照表2
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public CapMapGridResult queryL909count2(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "branchId",
				user.getUnitNo());
		BigDecimal balance = Util.parseBigDecimal(Util.trim(params.getString("balance")));
		Integer years = Util.parseInt(Util.trim(params.getString("years")));
		Integer month = Util.parseInt(Util.trim(params.getString("month")));
		Double cycle = Util.parseDouble(params.getString("cycle"));// 繳款週期
		String repaymod = Util.trim(params.getString("repaymod"));// 攤還方式
		int NowEnd=Util.parseInt(params.getString("NowEnd"));//寬限期結束期
		int NowFrom=Util.parseInt(params.getString("NowFrom"));//寬限期起期
		Date date = Util.parseDate(Util.trim(params.getString("startdate")));//起始日期
		Double PayWayAmt=Util.parseDouble(params.getString("PayWayAmt"));//期付金額
		if(PayWayAmt<=0){
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS9091V02Page.class);
			throw new CapMessageException(prop.getProperty("L909V02.fixedpay")+"不可為0", getClass());
		}
		// --------------取得分段利率--------------------------
		Double rates[][] = new Double[5][3];
		for (int i = 0; i < rates.length; i++) {
			rates[i][0] = Util.parseDouble(Util.trim(params.getString("start"
					+ (i + 1))));// 期間起始期數
			rates[i][1] = Util.parseDouble(Util.trim(params.getString("end"
					+ (i + 1))));// 期間結束期數
			rates[i][2] = Util.parseDouble(Util.trim(params.getString("rate"
					+ (i + 1))));// 期間利率
		}
		// -------------設定 寬限期----------------------------

		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		List<Map<String, Object>> ResultList = new ArrayList<Map<String, Object>>();
		list=LMSUtil.getRateList(balance, years, month, cycle, repaymod, NowEnd, NowFrom, date, PayWayAmt, rates);
		for(Map<String, String> map:list){
			Map<String, Object> ResultMap=new HashMap<String, Object>();
			ResultMap.put("periods", map.get("ReportBean.column01"));
			ResultMap.put("paydate",map.get("ReportBean.column02"));
			ResultMap.put("balance", Util.parseDouble(map.get("ReportBean.column03")));
			ResultMap.put("periodprincipal", Util.parseDouble(map.get("ReportBean.column06")));
			ResultMap.put("periodinterest", Util.parseDouble(map.get("ReportBean.column07")));
			ResultMap.put("periodpay", Util.parseDouble(map.get("ReportBean.column08")));
			ResultMap.put("Dbalance",Util.parseDouble(map.get("ReportBean.column09")));
			ResultList.add(ResultMap);
		}
		CapMapGridResult Result = new CapMapGridResult(ResultList, ResultList.size());

		return Result;
	}

}
