package tw.com.jcs.flow;

import java.util.Collection;
import java.util.Map;

import tw.com.jcs.flow.node.FlowNode;
import tw.com.jcs.flow.provider.FlowHandler;

/**
 * <pre>
 *<h1>流程定義資訊</h1> 儲存流程定義的相關資訊，做為流程實體執行的依據
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> Software Inc.
 * @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public interface FlowDefinition {

    /**
     * 取得流程定義名稱
     * 
     * @return
     */
    String getName();

    /**
     * 取得流程定義的處理器
     * 
     * @return
     */
    FlowHandler getHandler();

    /**
     * 取得流程定義的所有節點
     * 
     * @return
     */
    Map<String, FlowNode> getNodes();

    /**
     * 取得流程啟始節點
     * 
     * @return
     */
    FlowNode getStartNode();

    /**
     * 取得流程終止節點
     * 
     * @return
     */
    Collection<FlowNode> getEndNodes();

}
