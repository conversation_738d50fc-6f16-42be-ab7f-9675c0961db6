/* 
 * C122S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.C122S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C122S01A;

/** 線上增貸核貸資訊 **/
@Repository
public class C122S01ADaoImpl extends LMSJpaDao<C122S01A, String>
	implements C122S01ADao {

	@Override
	public C122S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C122S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C122S01A> list = createQuery(search).getResultList();
		return list;
	}
		
	@Override
	public C122S01A findByUk(String mainId, Integer batchNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "batchNo", batchNo);
		return findUniqueOrNone(search);
	}
	
	@Override
	public int maxBatchNoInMainId(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("batchNo", true);		
		C122S01A c122s01a = findUniqueOrNone(search);
		if(c122s01a == null){
			return 0;
		}else{
			return c122s01a.getBatchNo();
		}
	}
}