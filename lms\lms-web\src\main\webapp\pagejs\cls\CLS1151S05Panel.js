var PanelAction05 = {
    isInit: false,
    /**
     *頁面初始化的動作
     * */
    initAction: function(){
        _M.initItem("05");
        this.initGrid();
        this.initEvent();
        this.afterAction();
    },
    L140S02AGrid: null,
    
    initEvent: function(){
        /**
         * 新增產品種類
         */
        $("#addL140S02A").click(function(){
            L140S02Action.addL140S02A();
        });
        /**
         * 複製產品種類
         */
        $("#copyL140S02A").click(function(){
            L140S02Action.copyL140S02A();
        });
        /**
         * 調整產品順序
         */
        $("#uiSeqL140S02A").click(function(){
            L140S02Action.uiSeqL140S02A();
        });
        
        $("#c102m01a_rptId_memo").click(function(){
        	CommonAPI.showMessage( i18n.cls1151s01["page5.243"] ); //J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，報表名稱改為「自用住宅貸款檢核表」
        });
        
        $("#ProdModelPdf").click(function(){
            $.form.submit({
                url: "../simple/FileProcessingService",
                target: "_blank",
                data: {
                    fileId: "ProdModel.pdf",
                    fileDownloadName: "ProdModel.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
        
        $("#EsgGtypeClsDescPdf").click(function(){
            $.form.submit({
                url: "../simple/FileProcessingService",
                target: "_blank",
                data: {
                    fileId: "EsgGtypeClsDesc.pdf",
                    fileDownloadName: "EsgGtypeClsDesc.pdf",
                    serviceName: "lmsfiledownloadservice"
                }
            });
        });
    
        $("#rmCalc").click(function(){
            L140S02Action.rmCalc();
        });
        
        $("#rmCalcSpreadsheet").click(function(){
            L140S02Action.rmCalcSpreadsheet();
        });
        checkRepayment();
    },
    initGrid: function(){
    	ilog.debug("@ajax > initGrid > CLS1151GridHandler::queryL140S02A");
        this.L140S02AGrid = $("#L140S02AGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            rowNum: 10,
            shrinkToFit: false,
            sortname: "uiSeq|seq",
            sortorder: "asc|asc",
            postData: {
                formAction: "queryL140S02A",
                tabFormMainId: _M.tabMainId
            },
            colModel: [{
                colHeader: i18n.cls1151s01["cls1151s01.title05"],//產品種類,
                name: 'prodKind',
                align: "left",
                width: 270,
                sortable: true,
                formatter: "click",
                onclick: L140S02Action.openBox
            }, {
                colHeader: i18n.cls1151s01["page5.045"],//授信科目,
                name: 'subjCode',
                align: "left",
                width: 200,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140M01A.type"],//性質,
                name: 'property',
                align: "left",
                width: 80,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["page5.044"],//期間,
                name: 'lnSelect',
                align: "left",
                width: 70,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["page5.043"],//額度,
                name: 'loanAmt',
                align: "right",
                width: 100,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            }, {
                colHeader: i18n.cls1151s01["L140S02A.grade1"],//最終評等,
                name: 'grade1',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: "&nbsp;",
                name: 'chkYN',
                align: "center",
                width: 60,
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = PanelAction05.L140S02AGrid.getRowData(rowid);
                L140S02Action.openBox(null, null, data);
            }
        });
    },
    /**
     *載入頁面後的動作
     * */
    afterAction: function(){
        var codeMap = API.loadCombos(["lms1405s0202_reUse", "lms1405m01_snoKind"]);
        var $form05 = $("#CLS1151Form05");
        var useStr = "";
        if (_M.AllFormData["04"]["reUse"]) {
            useStr = codeMap["lms1405s0202_reUse"][_M.AllFormData["04"]["reUse"]];
            
        }
        $form05.find("#page05reUse").html(useStr);
        var isGutCut = "";
        if (_M.AllFormData["04"]["snoKind"] && _M.AllFormData["04"]["snoKind"] == "20") {
        
            isGutCut = codeMap["lms1405m01_snoKind"][_M.AllFormData["04"]["snoKind"]];
            
        }
        $form05.find("#page05isGutCut").html(isGutCut);
        if (_M.AllFormData["04"]["cntrNo"]) {
            $form05.find("#page05Cntrno").html(_M.AllFormData["04"]["cntrNo"]);
        }
        this._reloadGrid({
            tabFormMainId: _M.tabMainId
        });
    },
    afterSetFormDataAction: function(){
        ilog.debug("@PanelAction05 :: afterSetFormDataAction()");
    },
    /**
     * 重新整理grid
     */
    _reloadGrid: function(data){
        this.L140S02AGrid.reload(data);
    }
    
};

/**
 * 選擇不動產相關動作
 */
var L140M01OAction = {
    cmsGrid: null,
    isInit: false,
    init: function(){
        if (!this.isInit) {
            this.cmsGrid = $("#selectL140M01OGrid").iGrid({
                handler: _M.ghandle,
                action: "queryL140m01o",
                height: 200,
                rownumbers: true,
                autowidth: true,
                postData: {
                    tabFormMainId: _M.tabMainId,
                    //只抓取不動產
                    type: "01"
                },
                multiselect: false,
                colModel: [{
                    colHeader: i18n.cls1151s01['L140M01O.taxAddr'],//稅籍地址
                    name: 'taxAddr',
                    align: "left",
                    width: 110,
                    sortable: true
                }, {
                    colHeader: i18n.cls1151s01['L140S02Tab.5_01'],//擔保品內容
                    name: 'cmsDesc',
                    align: "left",
                    width: 110,
                    sortable: false
                }, {
                    name: 'oid',
                    hidden: true
                }]
            });
            this.isInit = true;
        }
        else {
            this.cmsGrid.reload({
                tabFormMainId: _M.tabMainId,
                type: "01"
            });
        }
    },
    /**
     * 開啟選擇不動產
     * @param {Object} $form 表單物件
     */
    open: function($form){
        this.init();
        $("#selectL140M01OBox").thickbox({
            title: i18n.cls1151s01["page5.009"],
            width: 750,
            height: 375,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = L140M01OAction.cmsGrid;
                    //單筆
                    var rowData = $grid.getSingleData();
                    if (rowData) {
                        //L140S02Action.formId= "L140S02AForm"
                        $form.find("#showCMSInfo").html(rowData.taxAddr);
                        $form.find("#cmsSrcOid").html(rowData.oid);
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    
    /**
     * 清除不動產
     * @param {Object} $form 表單物件
     */
    cleanL140M01O: function($form){
        $form.find("#showCMSInfo").html("");
        $form.find("#cmsSrcOid").html("");
    }
};
/**
 * 購置房屋貸款表單
 */
var C102M01AAction = {
    formId: "C102M01AForm",
    isInit: false,
    oid: "",
    mainId: "",
    init: function(){
        if (!this.isInit) {
            this.initEvent();
            this.isInit = true;
        }
    },
    initEvent: function(){
        var $form = $("#" + C102M01AAction.formId);
        $form.find("#getcustName").click(function(){
            C102M01AAction.openCustNameBox();
        });
        $form.find("#getaLoanAC").click(function(){
            C102M01AAction.openaLoanACBox();
        });
        $form.find("#getQuest").click(function(){
            C102M01AAction.getExplain();
        });
        $form.find("input:radio[name='chk11']").click(function(){
            C102M01AAction.checkSelfHours($form);
        });
        $form.find("input:radio[name='chk12']").click(function(){
            C102M01AAction.checkSelfHours($form);
        });
        $form.find("input:radio[name='chk13']").click(function(){
            C102M01AAction.checkSelfHours($form);
        });
        $form.find("input:radio[name='chk14']").click(function(){
            C102M01AAction.checkSelfHours($form);
        });
    },
    /**
     * 檢查是否為自用住宅
     * @param {Object} $form
     */
    checkSelfHours: function($form){ 
    	var aLoanDate = $form.find("#aLoanDate").val()||"";
    	
    	var rptId = $form.find("#c102m01a_rptId").val()||'';    	
        var chk11 = $form.find("input:radio[name='chk11']:checked").val();
        var chk12 = $form.find("input:radio[name='chk12']:checked").val();
        var chk13 = $form.find("input:radio[name='chk13']:checked").val();
        var chk14 = $form.find("input:radio[name='chk14']:checked").val();
        var selfChk = "N";
        if (chk11 == "Y" && chk12 == "Y" && chk13 == "Y" && chk14 == "Y") {
        	selfChk = "Y";
        }       
        var isAloanDate_GE_100_04_21 = (aLoanDate == "" 
        		|| aLoanDate >= "2011-04-21");
        ilog.debug("checkSelfHours[rptId="+rptId+"],loanDtBool="+isAloanDate_GE_100_04_21+",selfChk="+selfChk+"["+chk11+" , "+chk12+" , "+chk13+" , "+chk14+"]");
        
        if(rptId=="V202208"){
        	
        }else if(rptId=="V20171231"){
    		if(isAloanDate_GE_100_04_21){
    			if(selfChk=="Y"){
        			//default 35%, 但可改75%
            		$form.find('input:radio[name="rskFlag"][value=3]').attr("checked", "checked");
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=4]').attr("checked", "checked");        			
        		}
    			
    		}else{
    			if(selfChk=="Y"){
        			//default 35%, 但可改45%
            		$form.find('input:radio[name="rskFlag"][value=3]').attr("checked", "checked");
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=1]').attr("checked", "checked");        			
        		}
    		}        	
        }else{
        	if(isAloanDate_GE_100_04_21){
        		if(selfChk=="Y"){
        			//default 45%, 但可改100%
            		$form.find('input:radio[name="rskFlag"][value=1]').attr("checked", "checked");        			
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=2]').attr("checked", "checked");        			
        		}        		
        	}else{
        		//首撥日在2011-04-21之前固定為45%
        		$form.find('input:radio[name="rskFlag"][value=1]').attr("checked", "checked");
        	}        	
        }
    },
    /**
     * 開啟購置房屋擔保放款表
     */
    openBox: function(){
        //新增前先檢查額度序號和放款帳號
        var page04Data = _M.AllFormData["04"];
        var cntrNo = $.trim(page04Data["cntrNo"]);
        var $L140S02Aform = $("#" + L140S02Action.formId);
        var aLoanAC = $.trim($L140S02Aform.find("#loanNo").html()) || "";
        if (cntrNo == "") {
            // L140M01A.msg031=請先登錄額度序號！！
            return API.showMessage(i18n.cls1151s01["L140M01A.msg031"]);
        }
        C102M01AAction.init();
        var $form = $("#" + C102M01AAction.formId);
        $form.reset();
        
        //會 call CLS1151M01FormHandler.java 裡的 public IResult queryC102M01A(...)
        _M.doAjax({
            action: "queryC102M01A",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq,
                cntrNo: cntrNo,
                aLoanAC: aLoanAC
            },
            success: function(obj){
            	var tb_C102M01A_title = i18n.cls1151s01["C102M01A.title01"]; //最原先的 "購置房屋擔保放款風險權數檢核表"
            	if(true){
            		$form.find('.C102M01A_rptId_Ctl').hide();
            		//控制 CLS1151S05Panel.html 的 DOM (不是 CLS1021S01Panel.html)
            		//===============================================
            		//*** 這裡沒有依 rptId 去動態載入 html ***
            		//*** 現有架構，只是在 CLS1151S05Panel.java 儘可能去抓到 rptId 對應的 html (也有可能不準)
            		//===============================================
            		if(obj.c102m01a_rptId=="V202208"){
            			tb_C102M01A_title = i18n.cls1151s01["C102M01A.title01.V202208"]; //更改為 "自用住宅貸款檢核表"
                    }else if(obj.c102m01a_rptId=="V20171231"){
                		$form.find('.C102M01A_rptId_V20171231').show();                    		
                	}else{
                		$form.find('.C102M01A_rptId_old').show();    
                	}
                }
                                
            	$form.injectData(obj);
                $form.find("#aLoanAC").html(aLoanAC);
                //當2011/04/21以前的首撥日都要適用45%的風險權數
                ilog.debug("c102m01a_rptId=["+(obj.c102m01a_rptId||'')+"][rskFlag] disabled="+(obj.lock_rskFlag || _openerLockDoc == "1" || _M.isReadOnly)+",{"+(obj.lock_rskFlag) +" or "+ (_openerLockDoc == "1") +" or "+ (_M.isReadOnly)+"}");
                if (obj.lock_rskFlag || _openerLockDoc == "1" || _M.isReadOnly) {
                    $form.find("[name=rskFlag]").attr("disabled", "disabled");
                }
                else {
                    $form.find("[name=rskFlag]").removeAttr("disabled");
                }
                C102M01AAction.checkSelfHours($form);
                
                if(true){
                    /*
                     * 在 $form.injectData(obj); 之後
                     * 又寫 C102M01AAction.checkSelfHours($form);
                     * 會導致, 當 自用住宅 卻選擇較高的風險權數時(極少數)
                     * 把 UI 關掉後，再重開啟 => 被 js 變更成 "較低的風險權數"
                     */
                    if(obj.rskFlag && obj.rskFlag!="" ){
                    	$form.injectData({'rskFlag':obj.rskFlag});	
                    }
                }                
                
                $("#C102M01ABox").thickbox({
                    //C102M01A.title01=購置房屋擔保放款風險權數檢核表
                    title: tb_C102M01A_title,
                    width: 850,
                    height: 600,
                    modal: true,
                    readOnly: _openerLockDoc == "1" || _M.isReadOnly,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(){
                            if (!$form.valid()) {
                                return false;
                            }
                            _M.doAjax({
                                action: "saveC102M01A",
                                formId: C102M01AAction.formId,
                                cntrNo: cntrNo,								
                                data: {
                                    L140S02ASeq: L140S02Action.L140S02ASeq
                                },
                                success: function(obj2){									
									if (obj2.tReCheckDataMsg) {
										API.showMessage(obj2.tReCheckDataMsg);										
									}
									
                                    obj.c120M01A_oid = obj2.c120M01A_oid;
                                    $form.injectData(obj2);
                                }
                            });
                        },
                        "print": function(){
                            if (!obj.c120M01A_oid) {
                                //requireSave=煩請先儲存後，再執行其動作，謝謝。
                                API.showMessage(i18n.def['requireSave']);
                                return false;
                            }
                            $.form.submit({
                                url: "../simple/FileProcessingService",
                                target: "_blank",
                                data: {
                                    mainId: obj.c120M01A_mainId || "",
                                    mainOid: obj.c120M01A_oid || "",
                                    caseMainId: _M.CaseMainId,
                                    fileDownloadName: "cls1021r01.pdf",
                                    serviceName: "cls1021r01rptservice"
                                }
                            });
                        },
                        "close": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    },
    openCustNameBox: function(){
        if ($("#custId").val() != "" && $("#dupNo").val() != "") {
            $("#openCustNameGrid").jqGrid("setGridParam", {
                postData: {
                    formAction: "queryGetCustName",
                    custId: $("#custId").val(),
                    dupNo: $("#dupNo").val()
                },
                search: true
            }).trigger("reloadGrid");
            $("#openCustNameBox").thickbox({
                title: i18n.cls1151s01["C102M01A.getCustName"],
                modal: true,
                width: 500,
                height: 300,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var id = $("#openCustNameGrid").getGridParam('selrow');
                        var data = $("#openCustNameGrid").getRowData(id);
                        $("#custName").val(data.CNAME);
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
        else {
            API.showMessage(i18n.cls1151s01['C102M01A.error3']);
        }
    },
    openaLoanACBox: function(){
        if ($("#custId").val() != "" && $("#dupNo").val() != "" && $("#cntrNo").val() != "") {
            $("#openaLoanACGrid").jqGrid("setGridParam", {
                postData: {
                    formAction: "queryGetaLoanAC",
                    custId: $("#custId").val(),
                    dupNo: $("#dupNo").val(),
                    cntrNo: $("#cntrNo").val()
                },
                search: true
            }).trigger("reloadGrid");
            $("#openaLoanACBox").thickbox({
                title: i18n.cls1151s01["C102M01A.getLoanAC"],
                modal: true,
                width: 500,
                height: 300,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        var id = $("#openaLoanACGrid").getGridParam('selrow');
                        var data = $("#openaLoanACGrid").getRowData(id);
                        $("#aLoanAC").val(data.aLoanAC);
                        $("#aLoanDate").val(data.aLoanDate);
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        }
        else {
            API.showMessage(i18n.cls1151s01['C102M01A.error4']);
        }
    },
    /**
     * 取得 問與答
     */
    getExplain: function(){
        $.form.submit({
            url: "../simple/FileProcessingService",
            target: "_blank",
            data: {
                fileDownloadName: "CLS1021S01Explain.pdf",
                serviceName: "cls1021s01pdfservice"
            }
        });
    },
    chg_c102m01a_rptId: function(){
    	API.confirmMessage(i18n.def.confirmRun, function(b){
            if (b) {
            	_M.doAjax({
                    action: "chgC102M01A_rptId",
                    formId: C102M01AAction.formId,								
                    data: {
                        L140S02ASeq: L140S02Action.L140S02ASeq
                    },
                    success: function(json){									
                    	var $btn_chg_c102m01a_rptId = $("#btn_chg_c102m01a_rptId");
                    	$btn_chg_c102m01a_rptId.hide();    					
                    }
                });
            }
        });
    }
};
/**
 * 產品相關動作
 */
var L140S02Action = {
    //清除期付金欄位
    cleanPeriodAmt: function(){
        var $form = $("#" + L140S02Action.formId);
        $form.find("#periodAmt").val("");
        $form.find("[name=periodChk]:checked").removeAttr("checked");
    },
    //確認是否為有擔
    isGuarantee: null,
    setGuarantee: function(subCode){
        if (subCode && subCode.length) {
            this.isGuarantee = this.checkGuarantee(subCode);
        }
        else {
            this.isGuarantee = null;
        }
    },
    /**
     *判斷該科目是否為有擔
     * @param {Object} subCode 授信科目
     */
    checkGuarantee: function(subCode){
        var result = false;
        if (subCode && subCode.length) {
            //如果三碼的授信科目第一碼為雙數，為有擔科目
            if (subCode.substring(0, 1) % 2 != 1) {
                return true;
            }
        }
        return result;
    },
    formId: "L140S02AForm",
    itemCode: {},
    L140S02ASeq: 0,
    _L140S02AData: {},
    
    /**
     * 調整產品順序
     */
    uiSeqL140S02A: function(){
    	$("#_div_uiSeq_content").empty();
    	
    	$.ajax({ type: "POST", handler: 'cls1151m01formhandler'
			, data: {'formAction': 'load_uiSeq', 'tabFormMainId': _M.tabMainId }
			, success: function(json_load_uiSeq){
				if(json_load_uiSeq.l140s02a_cnt > 0 ){

					var dyna = [];					
					$.each( json_load_uiSeq.l140s02a_list.arr, function(idx, item){						
						dyna.push("<tr>");						
																				
							dyna.push("<td>"+item.prodKind + " " +item.prodKindName+"</td>");
							dyna.push("<td>"+item.subjCode + " " +item.subjCodeName+"</td>");
							dyna.push("<td class='rt'>"+item.loanAmt+"</td>");
							dyna.push("<td>");
							dyna.push("<input type='hidden' id='l140s02a_oid'   name='l140s02a_oid'   value='"+item.oid+"'>");
							dyna.push("<input type='text'   id='l140s02a_uiSeq' name='l140s02a_uiSeq' value='"+item.uiSeq+"' class='numeric' maxlength='5' integer='5' fraction='0' size='3'>");
							dyna.push("</td>");
													
						dyna.push("</tr>");
					});
					$("#_div_uiSeq_content").html( dyna.join("\n") );
					
			    	$("#_div_uiSeq").thickbox({
			            title: i18n.cls1151s01['button.uiSeq'],
			            width: 600,
			            height: 300,
			            modal: true,
			            i18n: i18n.def,
			            buttons: API.createJSON([{
			                key: i18n.def['saveData'],
			                value: function(){
			                	$.ajax({ type: "POST", handler: 'cls1151m01formhandler'
			            			, data: $.extend( 
			            				{'formAction': 'save_uiSeq' }
			            				, $("#frm_uiSeq").serializeData() 
			            			)
			            			, success: function(json_save_uiSeq){
			            				
			            				L140S02Action._reloadGrid();
			            				
			            				$.thickbox.close();
			            				
			            				API.showMessage(i18n.def.runSuccess);
			            			}
			            		});
			                }
			            }, {
			                key: i18n.def['close'],
			                value: function(){
			                    $.thickbox.close();
			                }
			            }])
			        });
				}else{
					API.showMessage(i18n.def.noData);
				}
			}
    	});
    	
    },
    /**
     * 複製產品
     */
    copyL140S02A: function(){
        var page04Data = _M.AllFormData["04"];
        var cntrNo = page04Data['cntrNo'];
        var snoKind = page04Data['snoKind'];
        var currentApplyAmt = page04Data['currentApplyAmt'];
        var reUse = page04Data['reUse'];
        var currentApplyCurr = page04Data['currentApplyCurr'];
        if (cntrNo == "" || snoKind == "" || currentApplyAmt == "" || reUse == "") {
            //page5.177=額度控管種類/額度序號/現請額度/循環註記需登錄後才能登錄產品資訊
            return API.showMessage(i18n.cls1151s01["page5.177"]);
        }
        var $grid = PanelAction05.L140S02AGrid;
        //單筆
        var rowData = $grid.getSingleData();
        if (rowData) {
            //actoin_001=是否執行此動作?
            API.confirmMessage(i18n.def["actoin_001"], function(b){
                if (b) {
                    _M.verifyCntrNoAction().done(function(json){
                        _M.doAjax({
                            action: "copyL140S02A",
                            data: {
                                oid: rowData.oid,
                                snoKind: snoKind,
                                currentApplyAmt: currentApplyAmt,
                                currentApplyCurr: currentApplyCurr,
                                reUse: reUse
                            },
                            success: function(){
                                L140S02Action._reloadGrid();
                            }
                        });
                    });
                }
            });
        }
    },
    /**
     * 新增產品種類
     */
    addL140S02A: function(){
        var page04Data = _M.AllFormData["04"];
        var cntrNo = page04Data['cntrNo'];
        var snoKind = page04Data['snoKind'];
        var currentApplyAmt = page04Data['currentApplyAmt'];
        var reUse = page04Data['reUse'];
        var currentApplyCurr = page04Data['currentApplyCurr'];
        if (cntrNo == "" || snoKind == "" || currentApplyAmt == "" || reUse == "") {
            //page5.177=額度控管種類/額度序號/現請額度/循環註記需登錄後才能登錄產品資訊
            return API.showMessage(i18n.cls1151s01["page5.177"]);
        }
        else {
            _M.verifyCntrNoAction().done(function(json){
                _M.doAjax({
                    action: "addL140S02A",
                    data: {
                        cntrNo: cntrNo,
                        snoKind: snoKind,
                        currentApplyAmt: currentApplyAmt,
                        currentApplyCurr: currentApplyCurr,
                        reUse: reUse
                    },
                    success: function(obj){
                        L140S02Action.openBox(null, null, obj);
                        L140S02Action._reloadGrid();
                        _M._triggerMainGrid();
                    }
                });
            });
        }
    },
    /**
     * refresh_l140s02a_showModelKind
     */
    refresh_l140s02a_gradeDiv: function(val){
        //選用模型
        if (val == "0") {
            $("#showModelKind").val(i18n.cls1151s01["L140S02A.modelKind.0"]);
        }else if (val == "1") {
            $("#showModelKind").val("「" + i18n.cls1151s01["L140S02A.modelKind.1"] + "」：");
        }else if (val == "2") {
        	$("#showModelKind").val("「" + i18n.cls1151s01["L140S02A.modelKind.2"] + "」：");
        }else if (val == "3") {
        	$("#showModelKind").val("「" + i18n.cls1151s01["L140S02A.modelKind.3"] + "」：");
        }else {
            //之前呈現  評等：
            $("#showModelKind").val(i18n.cls1151s01["L140S02A.grade2"] + "：");
        }
        //------
        if (val == "1" || val == "2" || val == "3") {
            $("#gradeButtonTR").show();
        }
        else {
            $("#gradeButtonTR").hide();
        }
        //------
        var propertyVal = $("#property").val();
        var $form = $("#" + L140S02Action.formId);
        var $gradeTr = $form.find("#gradeTR");
        //非團貸案
        var notDocCode5 = (_M.AllFormData.docCode != "5");
        if (notDocCode5) {
            if (propertyVal == "7" || propertyVal == "8") {
                $gradeTr.hide();
            }
            else {
                $gradeTr.show();
            }
        }
        else {
            $gradeTr.hide();
        }
        //------
    },
    /**
     * refresh_l140s02a_modelKind
     */
    refresh_l140s02a_modelKind: function(prodKindVal, subjCodeVal){
        _M.doAjax({
            action: "refresh_l140s02a_modelKind",
            data: {
                tabFormMainId: _M.tabMainId,
                'prodKind': prodKindVal,
                'subjCode': subjCodeVal
            },
            success: function(obj){
                L140S02Action.refresh_l140s02a_gradeDiv(obj.modelKind);
                $("#cleanGrade").trigger('click');
            }
        });
    },
    rmCalc: function(){
    	var page04Data = _M.AllFormData["04"];
        var currentApplyAmt = page04Data['currentApplyAmt'];
        var currentApplyCurr = page04Data['currentApplyCurr'];
        _M.doAjax({
            action: "rmCalc",
            formId: L140S02Action.formId , 
            data: {
            	'tabFormMainId': _M.tabMainId
            	,'currentApplyCurr' : currentApplyCurr
            	,'currentApplyAmt' : currentApplyAmt
            },
            success: function(json){
            	var $form = $("#" + L140S02Action.formId);
            	if(json.data){
            		$form.injectData(json.data);
            	}
            	if(json.msg){            		
            		API.showMessage(json.msg);	
            	}            	                
            }
        });
    },
    rmCalcSpreadsheet: function(){
    	var page04Data = _M.AllFormData["04"];
        var currentApplyAmt = page04Data['currentApplyAmt'];
        var currentApplyCurr = page04Data['currentApplyCurr'];
        _M.doAjax({
            action: "rmCalcSpreadsheet",
            formId: L140S02Action.formId , 
            data: {
            	'tabFormMainId': _M.tabMainId
            	,'currentApplyCurr' : currentApplyCurr
            	,'currentApplyAmt' : currentApplyAmt
            },
            success: function(json){
            	$.form.submit({
          	        url: "../simple/FileProcessingService",
          	        target: "_blank",
          	        data: $.extend( json, 
          	        		{'fileDownloadName': "data.pdf",
          	            	'serviceName': "lms9091r03rptservice"            
          	            	}
          	        	)
          	     });
            }
        });
    },
    isInitL140S02A: false,
    isprodKindTypeOn: "",//J-113-0036 產品種類類型切換功能啟用
    /**
     *初始化產品畫面
     */
    initL140S02A: function(){
        if (!this.isInitL140S02A) {
            this.initEvent();
            this.initItem(this.formId);
            L140S02HAction.initGrid();
            this.isInitL140S02A = true;
            this.getProdSelect();
			this.getRatePlanSelect();
        }
        var $form = $("#" + L140S02Action.formId);
        $form.reset();
        //隱藏所有產品相關
        $form.find(".aboutProdKind").hide();
        
        //手續費隱藏
        $form.find("#chargeAmtDiv").hide();
    },
    
    /**
     * 初始化事件
     */
    initEvent: function(){
        //儲存金額欄位
        
        //非團貸案
        var notDocCode5 = (_M.AllFormData.docCode != "5");
        var $form = $("#" + L140S02Action.formId);
        $form.find("#loanAmt").blur(function(){
            var oldAMT = parseInt(util.delComma(L140S02Action._L140S02AData.loanAmt || 0), 10);
            var newAMT = parseInt(util.delComma($(this).val()), 10);
            if (oldAMT != newAMT) {
                L140S02Action.cleanPeriodAmt();
            }
            L140S02Action._L140S02AData.loanAmt = newAMT;
        });
        
        //最終評等Tr
        var $gradeTr = $form.find("#gradeTR");
       
        //產品種類
        var $prodKind = $form.find("#prodKind");
        var $disasType = $form.find("#disasType");
        var $property = $form.find("#property");
		
		var $ratePlan = $form.find("#ratePlan");
        
        /**
         * 性質切換
         */
        $property.change(function(k, v){
            if (notDocCode5) {
                $gradeTr.show();
            }
            var $isCreditY = $("[name=isCredit][value=Y]");
            $isCreditY.removeAttr("disabled");
            
            var $isCredit1 = $("[name=isCredit][value=1]");
            $isCredit1.removeAttr("disabled");
            
            var $isCredit2 = $("[name=isCredit][value=2]");
            $isCredit2.removeAttr("disabled");
            
            var $isTakFeeY = $("[name=isTakFee][value=Y]");
            $isTakFeeY.removeAttr("disabled");
            
            var value = $(this).val();
            //控制次要性質
            L140S02Action.controllerSubProperty(value, v);
            switch (value) {
                case "1":
                    //額度性質為「新做」不得選擇抵利房貸
                    $isCreditY.attr("disabled", "disabled").removeAttr("checked");
                    $isCredit1.attr("disabled", "disabled").removeAttr("checked");
                    $isCredit2.attr("disabled", "disabled").removeAttr("checked");
                    // ~~~~~~
                    $isTakFeeY.attr("disabled", "disabled").removeAttr("checked");
                    // J-109-0135 新案且為69勞工紓困貸款時發送簡訊通知客戶
                    prodKind69Display();
                    break;
                case "2":
                case "11":
                    //改在 hs_hgContractTr() 去判斷
                    break;
                case "7":
                case "8":
                    //取消與不變無最終評等 
                    $gradeTr.hide();
                    break;
            }
            // J-108-0283 變更條件Condition Change
            if(value=="3"){
                $("#queryL140S05A").show();
            } else {
                $("#queryL140S05A").hide();
            }
            hs_hgContractTr({'property':value});
            
            if( $form.find("#srcProdKind").val()=='67' || $form.find("#srcProdKind").val()=='70' ){
            	//以房養老｛新作：不鎖住｝但｛由帳務引入的舊案：不能更改[產品、科目] ｝
            	$prodKind.attr("disabled", "disabled");
            	$disasType.attr("disabled", "disabled");
                $subjCode.attr("disabled", "disabled");
            }else{
	            var $subjCode2 = $form.find("#subjCode2");
	            var $loanNo = $form.find("#loanNo");
	            var value1 = $(this).val();
	            
	            if((value1=="7" ||value1=="8") && ($loanNo.val() != "")) {
	            	//[不變、取消]時，鎖住[產品、科目]
	            	$prodKind.attr("disabled", "disabled");
	            	$disasType.attr("disabled", "disabled");
	                $subjCode.attr("disabled", "disabled");
	            }else{
	            	//變更條件，可能改[產品、科目]
	            	$prodKind.removeAttr("disabled");
	            	$disasType.removeAttr("disabled");
	                $subjCode.removeAttr("disabled");
	            }
            }
        });
        
        // J-113-0036 產品種類類型 切換
        var $prodKindType = $form.find("#prodKindType");
        $prodKindType.change(function(k, v){
            var propertyValue = $("#property").val();
            var prodKindTypeValue = $(this).val();
            var tempProdKindValue = "";

            if($prodKind.val() != ""){
        		tempProdKindValue = $prodKind.val();
        	}
            if(prodKindTypeValue && prodKindTypeValue !== "" && L140S02Action.isprodKindTypeOn == "Y"){
            	_M.doAjax({
                    action: "getProdSelectByProdKind",
                    data: {
                    	tabFormMainId: _M.tabMainId,
                        prodKindType: prodKindTypeValue
                    },
                    async: false,
                    success: function(objs){
                        var tempNewProdKind = "<option value=''>" + i18n.def.comboSpace + "</option>";
                        for (var i in objs["obj"]) {
                            var value = objs["obj"][i];
                            var disabled = value["isCanCel"] ? "disabled=disabled isCanCel='Y'" : "";
                            if( (propertyValue == "" || propertyValue == "1") && disabled != ""){
                            	continue;
                            }
                            tempNewProdKind += "<option value='" + value["key"] + "' subjectData='" + JSON.stringify(value["subjectData"]) + "' " + disabled + " >" + value["key"] + "-" + value["name"] + "</option>";
                        }
                        $prodKind.html(tempNewProdKind);
                        if(tempProdKindValue!=""){
                        	$prodKind.val(tempProdKindValue);
                        }
                    }
                });
            }else{
            	if(tempProdKindValue == "" && L140S02Action.isprodKindTypeOn == "Y"){
            		//如果產品種類跟產品都沒有值 就代表是新填寫的案子，要把產品種類給清掉
            		var tempCleanProdKind = "<option value=''>" + i18n.def.comboSpace + "</option>";
            		$prodKind.html(tempCleanProdKind);
            	}
            }
        });
        
        /**
         * 資金來源 切換
         */
        //資金來源大類
        var $fFund = $form.find("#fFund");
        //資金來源小類
        var $dFund = $form.find("#dFund");
        $fFund.change(function(){
            var value = $(this).val();
            //因為5目前沒有項目所以我切換先隱藏
            if (!value || value == "5") {
                var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
                $dFund.hide().html(defaultSelect);
            }
            else {
                $dFund.setItems({
                    item: L140S02Action.getDFund(value),
                    format: "{value} - {key}",
                    sort: "asc",
                    value: L140S02Action._L140S02AData["dFund"] || ""
                }).show();
            }
        });
        
        //跟產品有關的
        var $aboutProdKind = $form.find(".aboutProdKind");
        //for 921購置項目 case 23 | 24 
        var $for921Case = $form.find("#for921Case");
        //授信科目
        var $subjCode = $form.find("#subjCode");
        //貸款額度評等表
        var $L140S02KTr = $form.find("#L140S02KTr");
        //信保成數
        var $gutPercentTr = $form.find("#gutPercentTr");
        
        //費率
        var $freeRateDescFd = $form.find("#L140S02A_freeRateDescFd");
        
        //for07Tr 期付金
        var $for07Tr = $form.find("#for07Tr");
        //授信科目
        var $for673or473Tr = $form.find(".for673or473Tr");
        
        var $for_disasType_Tr = $form.find(".for_disasType_Tr");
        
        var $aboutReverseMortgage = $form.find(".aboutReverseMortgage");
        
        //歡喜優惠房貸違約條款
        var $happyMortgage = $form.find("#happyMortgage");
        
        //外勞貸款頁籤
        var $forForeigner = $form.find("#forForeigner,#L140S02ATabs_4");
        //房屋貸款
        var $forHouse = $form.find("#forHouse,#L140S02ATabs_2");
        //留學生貸款
        var $forStudent = $form.find("#forStudent,#L140S02ATabs_3");
        //償還方式中間說明
        var $for07 = $form.find(".for07")
        var $for32 = $form.find("#for32");
        var $forProdKind49 = $form.find(".forProdKind49");
        var $for16Div = $form.find("#for16Div");
        var $for171819Div = $form.find("#for171819Div");
        //產品為ZZ時需隱藏
        var $ZZProdKindHide = $form.find(".ZZProdKindHide");
        //新舊中古屋
        var $noHouseTr_28_35_56_59 = $form.find("#noHouseTr");
        //優惠房貸申請日
        var $appDateTr_28_35_56_59 = $form.find("#appDateTr");
        //產權登記日
        var $homeRegisterDateTr = $form.find("#homeRegisterDateTr");
        //核准編號
        var $chkNumberTr = $form.find("#chkNumberTr");
        //售屋者統編
        var $sellerIdTr_56 = $form.find("#sellerIdTr");
        //客戶類別
        var $custTypeTr_57 = $form.find("#custTypeTr");
        //是否重新辦理
        var $repeatTr_59 = $form.find("#repeatTr");
        //優惠房貸
        var $L140S02F_forFee_fieldset = $form.find("#L140S02F_forFee");
        //是否興建住宅
        var $residential = $form.find("#residential");
        
        //產品03時隱藏
        var $prodKind03Hide = $form.find(".prodKind03Hide");
        
        //產品07時隱藏
        var $prodKind07Hide = $form.find(".prodKind07Hide");
        
        //產品08時隱藏
        var $prodKind08Hide = $form.find(".prodKind08Hide");
        
        //產品71時隱藏
        var $prodKind71Hide = $form.find(".prodKind71Hide");
        
        //產品72時隱藏
        var $prodKind72Hide = $form.find(".prodKind72Hide");
        
        //天然及重大災害受災戶住宅補貼
        var $disasTypeMortgage = $form.find("#disasTypeMortgage");
        
        /**
         * 產品種類切換
         */
        $prodKind.change(function(k, v){
            $ZZProdKindHide.show();
            $aboutProdKind.hide();
            $for673or473Tr.hide();
            $for_disasType_Tr.hide();
            $for07.hide();
            $forProdKind49.hide();
            //=================
            var value = $(this).val();
            ilog.debug("@ajax > $prodKind.change to "+value);            
            if ($prodKind.is(":visible")) {
                L140S02Action.refresh_l140s02a_modelKind(value, "");
            }
            
            var propertyNot7and8 = ($property.val() != "7" && $property.val() != "8") ? true : false;
            var subjCodeValue = L140S02Action._L140S02AData["subjCode"] || "";
            var subjectTemp = "<option value=''>" + i18n.def.comboSpace + "</option>";
			
            if (!value || !v) {
                _M.cleanTrHideInput($aboutProdKind);
                subjCodeValue = "";
                if (!value) {
                    //清空原本的授信科目選項，讓產品選單 自動重新產生
                    $subjCode.html(subjectTemp);
                    return false;
                }
            }
            
            
            //產生授信科目項目
            var subjectData = JSON.parse($(this).find(":selected").attr("subjectData"));
            for (var i in subjectData) {
            
                var subjCode = subjectData[i].subjCode;
                var subjCode2 = subjectData[i].subjCode2;
                var subjNm = subjectData[i].subjNm;
                var rIntWay = subjectData[i].rIntWay || "";
                var disabled = subjectData[i].isCanCel == "Y" ? "disabled=disabled isCanCel='Y'" : "";
                //額度明細表-產品種類頁面-科目
                //如果其性質為信保戶時，且產品種類為58/60二類其科目皆為擔保科目選項。(不應出現非擔保科目選項)
                if (_M.AllFormData["04"]["snoKind"] == "20") {
                    if (value == "60" || value == "58" || value == "61") {
                        if (!L140S02Action.checkGuarantee(subjCode2)) {
                            continue;
                        }
                    }
                }
				
                subjectTemp += "<option value='" + DOMPurify.sanitize(subjCode) + "' subjCode2='" + DOMPurify.sanitize(subjCode2) + "'  rIntWay='" + DOMPurify.sanitize(rIntWay) 
								+ "' " + DOMPurify.sanitize(disabled) + ">" + DOMPurify.sanitize(subjCode) + "-" + DOMPurify.sanitize(subjNm) + "</option>";
            }
            $subjCode.html(subjectTemp).val(subjCodeValue);
            //房貸頁籤判斷
        	$("#forHouseTitle span").val(i18n.cls1151s01['page5.051']);
            if (L140S02Action.isHouse(value)) {
            
                //優惠房貸 顯示條件，可參考 ProdServiceImpl :: is_policy_house_loan(...)
                //28	35	56	38/39	57/59    66
                if (value == "28" || value == "35" || value == "56" || value == "38" || value == "39" 
                	|| value == "57" || value == "59" || value == "66") {
                    $L140S02F_forFee_fieldset.show();
                }
                //產品種類為03時，沒有[高雄市首購貸款]/[ 房貸壽險方案]。
                var kgAgreeYN = $form.find("input[name=kgAgreeYN]:checked").val();
                
                if (value == "03") {
                    $prodKind03Hide.hide();
                    $("input[name='kgAgreeYN']").attr("checked", '');
                }
                else {
                    $prodKind03Hide.show();
                    if (kgAgreeYN == null) {
                        $("input[name='kgAgreeYN'][value='0']").attr("checked", true);
                    }
                    
                    //					 if ($("input[id=kgAgreeYN]").filter(':checked')) {					 	
                    //					 	$form.find("input[name='kgAgreeYN'][value='0']").attr("checked","checked");
                    //					 }
                
                    //					$("#CMS1010S02G01Form").find("input[type='radio'][name='sectKind'][value="+sectKind+"]").attr("checked","checked");
                    //					$("input[type='radio'][name='kgAgreeYN']:checked").val("0")	;		
                
                }
                
                if (value == "72") {
                	  $prodKind72Hide.hide();
                }
                
                if (notDocCode5) {
                    $forHouse.show();
                    
                    if($property.val() == "1"){
                    	$("input[name='isCredit'][value='N']").attr("checked", true);
                    	// isTakFee 會依「科目」呈現，所以不在 $prodKind.change(...) 裡處理
                	}
                    
                }
            }
            else {
            	if (notDocCode5) {
                    if (value == "03") {
                    	//J-106-0001 產品03可輸入管制條件
                    	//不清空畫面
                    	$forHouse.show();
                    }else if (value == "07") {
                    	$("#forHouseTitle span").val(i18n.cls1151s01['page5.241']);
                    	$forHouse.show();
                    	$prodKind07Hide.hide();
                    }else if (value == "08") {
                    	$("#forHouseTitle span").val(i18n.cls1151s01['page5.240']);
                    	$forHouse.show();
                    	$prodKind08Hide.hide();
                    }else if (value == "71") {
                    	$("#forHouseTitle span").val(i18n.cls1151s01['page5.241']);
                    	$forHouse.show();
                    	$prodKind71Hide.hide();
                    }else{
                    	_M.cleanTrHideInput($forHouse);	
                    }                    
                }else{
                	_M.cleanTrHideInput($forHouse);	
                }                
            }
            
            //最終評等欄位-產品種類10~28/30/31/35/38/39/56/57/59為必要選項
            //產品種類02/03/04為自選項目，其他的產品種類不顯示此欄位。
            $fFund.removeAttr("disabled");
            if ((value >= 10 && value <= 28) || (value >= 63 && value <= 65)) {
            
                //產品種類22~24資金大類固定填4-郵匯局資金
                //921 購置項目
                switch (value) {
                    case "16":
                        $for16Div.show();
                        break;
                    case "17":
                    case "18":
                    case "19":
                        $for171819Div.show();
                        break;
                    case "20":
                        $gutPercentTr.show();
                        break;
                    case "22":
                        
                        break;
                    case "23":
                    case "24":
                        $fFund.val("4").attr("disabled", "disabled").change();
                        $for921Case.show();
                        break;
                    case "63":
                    case "64":
                    case "65":
                        $for_disasType_Tr.show();
                        $disasTypeMortgage.show();
                        break;
                }
            }
            else {
                switch (value) {
                    case "07":
                        $("#periodDiv").show();
                        $for07Tr.show();
                        $for07.show();
                        break;
                    case "36":
                        if (true) { //在 server 端，檢核產品種類、額度控管種類 的關聯，避免在JS寫太複雜的logic
                            //政策性留學生貸款
                            $forStudent.show();
                        }
                        
                        break;
                    case "32":
                        //32 外勞貸款
                        if (notDocCode5) {
                            $forForeigner.show();
                        }
                        $for32.show();
                        break;
                    case "30":
                    case "31":
                        
                        $happyMortgage.show();
                        break;
                    case "02":
                    case "03":
                    case "04":
                    case "35":
                    case "38":
                    case "39":
                    case "56":
                    case "57":
                    case "59":
                    	break;
                    case "58":                        
                        break;
                    case "66":                        
                        break;
                    case "67":       
                    	$("#label_rmRctAmt").val(i18n.cls1151s01['L140S02A.rmRctAmt']);
                    	$aboutReverseMortgage.show();                    	
                    	/* J-109-0271 以房養老 貸款期間屆期後，得予以延長，每次延長3年(核准額度、每月撥貸金額應開放修改)。
                    	if($form.find("#srcProdKind").val()=="67"){
                    		//［每期進帳金額、每期利息上限金額］不可變更 lnf030 
                    		$form.find("#rmRctAmt").attr("disabled", "disabled");
        					$form.find("#rmCalc").hide();
                    	}else{
                    		$form.find("#rmRctAmt").removeAttr("disabled", "disabled");
        					$form.find("#rmCalc").show();
                    	}*/
                        break;    
                    case "70":       
                    	$("#label_rmRctAmt").val(i18n.cls1151s01['L140S02A.rmRctAmt.prodKind70']);
                    	$aboutReverseMortgage.show();                    	
                    	break;
                    case "ZZ":
                        $ZZProdKindHide.hide();
                        $freeRateDescFd.show();
                        break;
                        
                }
            }
            if(value=="49"){
            	$forProdKind49.show();
            }
            hs_hgContractTr({'prodKind':value});
            
            
            //J-105-0266 非31可輸入提前還款管制條件
            if (L140S02Action.isHouse(value)) {
            	$happyMortgage.show();
            }
            
            //J-106-0001 產品03可輸入提前還款管制條件
            if (value=="03") {
            	/*
            	 先有上層 $forHouse ，才有 $happyMortgage
				
				上面的程式，依 notDocCode5 決定 $forHouse.show()
            	*/
            	$happyMortgage.show();
            }
            if (value=="07") {
            	$happyMortgage.show();
            }
            if (value=="08") {
            	$happyMortgage.show();
            }
            if (value=="71") {
            	$happyMortgage.show();
            	checkRepayment();
            }
            //判斷新/中古屋
            if (value == "28" || value == "35" || value == "56" || value == "59") {
//                if (value != "59") {
//                    //判斷優惠房貸額度申請日
//                    $appDateTr_28_35_56_59.show();
//                }
//                else {
//                    _M.cleanTrHideInput($appDateTr_28_35_56_59);
//                }
				$appDateTr_28_35_56_59.show();				
                $noHouseTr_28_35_56_59.show();
            }
            else {
                _M.cleanTrHideInput($appDateTr_28_35_56_59);
                _M.cleanTrHideInput($noHouseTr_28_35_56_59);
            }
            
            //判斷核准編號的出現
            if (value == "38" || value == "39" || value == "57" || value == "66") {
                $chkNumberTr.show();
            }
            else {
                _M.cleanTrHideInput($chkNumberTr);
            }
            //判斷售屋者統編
            if (value == "56") {
                $sellerIdTr_56.show();
            }
            else {
                _M.cleanTrHideInput($sellerIdTr_56);
            }
            //判斷客戶類別
            
            if (value == "57") {
                $custTypeTr_57.show();
            }
            else {
                _M.cleanTrHideInput($custTypeTr_57);
            }
            //判斷產權登記日
            if (value == "57" || value == "59" || value == "66") {
                $homeRegisterDateTr.show();
            }
            else {
                _M.cleanTrHideInput($homeRegisterDateTr);
            }
            //是否重新辦理
            if (value == "59") {
                $repeatTr_59.show()
            }
            else {
                _M.cleanTrHideInput($repeatTr_59);
            }
            //
            if (value == "36" && notDocCode5) {
                $forStudent.show();
            }
            
            //產品種類非33/34時，[是否屬興建住宅]鎖住在N->不提供修改。
            if (value != "33" && value != "34") {
                $residential.val("N").attr("disabled", "disabled");
            }
            else {
                $residential.removeAttr("disabled").removeAttr("readonly");
            }
            
            if(value == "67" || value == "70"){
            	//融資業務分類
                $form.find("#lnPurpose").val("4").attr("disabled", "disabled").trigger('change');
                
                //在 $lnPurpose.change(function(v, k){   裡  → 用途別 
            }else{
            	/*
            	 	當挑選 lnPurpose ，會影響 lnPurs
            	 */
            	$form.find("#lnPurpose").removeAttr("disabled");
            }
            
            //團貸母戶在簽報書新增時就已決定，且不能變更。
            if (_M.AllFormData.docCode == "5") {
                //團帶母戶顯示欄位和隱藏欄位
                $form.find(".docCode5Hide").hide();
                $form.find(".docCode5Show").show();
            }

            // J-109-0135 新案且為69勞工紓困貸款時發送簡訊通知客戶
            prodKind69Display();
        }); //end $prodKind.change(function(k, v){

        //動用方式
        var $useType = $form.find("#useType");
        var $useTypeTr = $form.find("#useTypeTr");
        //長擔額度序號相關
        var $aboutL140S02B = $form.find(".aboutL140S02B");
        
        //科目相關
        
        var $aboutSubCode = $form.find(".aboutSubCode");
        //是否搭配長擔貸款 ［L140S02B：流用(長擔)額度序號檔］
        var $chkUsedTr = $form.find("#chkUsedTr");
        
        //登錄長擔貸款
        var $loginL140S02BTr = $form.find("#loginL140S02BTr");
        
        //是否代付費用
        var $isTakFeeTr = $form.find("#isTakFeeTr");
        //應計入DBR22倍規範額度
        var $DBR22TR = $form.find("#DBR22TR");
        
        /**
         *授信科目切換
         */
        $subjCode.change(function(k, v){
            //產品值
            var prodKindVal = $prodKind.val();
            $for673or473Tr.hide();
            $aboutL140S02B.hide();
            $aboutSubCode.hide();
            $useTypeTr.hide();
            //檢查授信科目
            if (!v) {
                if (!L140S02Action.checkLnSelect($form)) {
                    $(this).val("");
                    return false;
                }
                $form.find("#rateDesc,#L140S02C_PreDscr").html("");
            }
            
            //科目簡碼
            var subjCode2 = $(this).find(":selected").attr("subjCode2");
            //設定科目為有擔還是無擔
            L140S02Action.setGuarantee(subjCode2);
            //有擔無擔顯示
            if (L140S02Action.isGuarantee) {
                //有擔
                $DBR22TR.hide().find("input").val("");
            }
            else {
                //無擔
                $DBR22TR.show();
            }
            
            if ($subjCode.is(":visible")) {
                L140S02Action.refresh_l140s02a_modelKind(prodKindVal, $(this).val());
            }
            
            if (!_M.isReadOnly) {
                $useType.removeAttr("disabled");
                $form.find("#useType option:eq(2)").removeAttr("disabled");
            }
            switch (subjCode2) {
                case "673":
                case "473":
                    $for673or473Tr.show();
                    break;
                case "103":
                case "203":
                case "206":
                case "207":
                    //103/203/206/207為借支書其他時不顯示此欄位。
                    $useTypeTr.show();
                    $useType.val("1");
                    $useType.attr("disabled", "disabled");
                    break;
                case "104":
				 	$useTypeTr.show();
					//$useType.val("1");
					$form.find("#useType option:eq(2)").attr("disabled", "disabled");
					break;
                case "204":
                    //［104,204：存摺存款］為金融卡；
                    $useTypeTr.show();
                    $useType.val("2");
                    $useType.attr("disabled", "disabled");
                    break;
                case "102":
                case "202":
                    //科目為［102,202：支存］固定為支票
                    $useTypeTr.show();
                    $useType.val("3");
                    $useType.attr("disabled", "disabled");
                    break;
            }
            
            /*
              	自 var isTakFeeShowType = ["403", "603", "473", "474", "673", "674"]; 裡的科目, 切換到其它科目
              	在「隱藏」欄位的同時，應把先前勾選的值清空
              	例如： (1)當產品03,科目603, 存檔    (2)再變更為產品03,科目503, 再存檔              		
             */            
            //帶出[不變、變更條件]時的值
//            $("input[name='isTakFee']").removeAttr("checked");
            var isTakFeeShowType = ["403", "603", "473", "474", "673", "674"];
            for (var temp in isTakFeeShowType) {
                if (isTakFeeShowType[temp] == subjCode2) {
                    $isTakFeeTr.show();
                    
                    if($forHouse.is(":visible") && $property.val() == "1"){ //避免團貸母戶時, 也把 isTakFee 塞入 N
                    	$("input[name='isTakFee'][value='N']").attr("checked", true);
                    }
                }
            }
            
            //再跑一次動用方式
            $useType.trigger("change");
            //團貸母戶在簽報書新增時就已決定，且不能變更。
            if (!notDocCode5) {
                //團帶母戶顯示欄位和隱藏欄位
                $form.find(".docCode5Hide").hide();
                $form.find(".docCode5Show").show();
                //*團貸總戶時且會計科目為購置時才顯示建案名稱/土地坐落且為必要輸入
                if ("473" == subjCode2 || "673" == subjCode2) {
                    if (!v) {
                        //page5.193=本案是否為建商推出之整批建案？
                        API.confirmMessage(i18n.cls1151s01["page5.193"], function(b){
                            if (b) {
                                $form.find("#docCode5HouseCase").show();
                                _M.cleanTrHideInput($form.find("#docCode5CaseName"));
                                $form.find("#isBuilder").val("Y");
                            }
                            else {
                                $form.find("#docCode5CaseName").show();
                                _M.cleanTrHideInput($form.find("#docCode5HouseCase"));
                                $form.find("#isBuilder").val("N");
                            }
                        });
                    }
                    else {
                        if (L140S02Action._L140S02AData.isBuilder == "Y") {
                            $form.find("#docCode5HouseCase").show();
                            _M.cleanTrHideInput($form.find("#docCode5CaseName"));
                        }
                        else {
                            $form.find("#docCode5CaseName").show();
                            _M.cleanTrHideInput($form.find("#docCode5HouseCase"));
                        }
                    }
                    
                    
                }
                else {
                    //*團貸案名稱欄位為團貸總戶時且會計科目不為購置時才顯示且為必要輸入
                    $form.find("#docCode5CaseName").show();
                    _M.cleanTrHideInput($form.find("#docCode5HouseCase"));
                    $form.find("#isBuilder").val("");
                }
            }
        
        
            if(prodKindVal=="03" && subjCode2=="403" && $form.find("#secNo").val()==''){
            	API.showMessage(i18n.cls1151s01["page5.232"]);
            }
            
            if(prodKindVal=="49" && subjCode2=="303" && $form.find("#secNo").val()==''){
            	/*
            	次順位房貸
            	● 融資業務分類(1:個人投資理財貸款 or Z:其他個人金融貸款), 優先 Z	
            	● 用途別 (2 or 4), 優先 4            	
            	*/
            	if( $form.find("#lnPurpose").val()==''){
            		$form.find("#lnPurpose").val("Z");
            	}
            	if( $form.find("#lnPurs").val()==''){
            		$form.find("#lnPurs").val("4");
            	}
            }
        });
        /**
         * 動用方式
         */
        $useType.change(function(k, v){
            var subjCode2 = $subjCode.find(":selected").attr("subjCode2");
            //只有在產品種類為02/04 AND科目為［202支存/204存摺存款］ AND動用方式[支票/金融卡]時才會顯示出來
            if ((($prodKind.val() == "02" || $prodKind.val() == "04") && (subjCode2 == "202" || subjCode2 == "204") && ($(this).val() == "2" || $(this).val() == "3")) 
            		|| ($prodKind.val() == "68" && (subjCode2 == "404")) ) {
                $chkUsedTr.show();
                //當為空白預設帶N
                if (!L140S02Action._L140S02AData["chkUsed"]) {
                    $chkUsedTr.find("[name=chkUsed][value=N]").attr("checked", "checked");
                }
                
                //當如果是query 完畫面還需判斷此值，否則無法顯示
                if (v && L140S02Action._L140S02AData["chkUsed"] == "Y") {
                    $loginL140S02BTr.show();
                }
                else {
                    $loginL140S02BTr.hide();
                }
            }
            else {
                _M.cleanTrHideInput($chkUsedTr);
                $loginL140S02BTr.hide();
            }
        });
        
        /**
         * 房貸資訊(二) 房貸產品方案
         */
        /**借款繳保費之金額 Tr**/
        var $inscAmtTr = $form.find("#inscAmtTr");
        $("#prodPlan").change(function(){
            if ($(this).val() == "01") {
                $inscAmtTr.show()
            }
            else {
                _M.cleanTrHideInput($inscAmtTr);
            }
            
        });
        
        /**
         * 建案名稱／土地坐落區
         * 縣市變更
         */
        var $landArea = $form.find("#landArea");
        var $landpart1 = $form.find("#landpart1");
        var $landpart2 = $form.find("#landpart2");
        
        var defaultSelect = "<option value=''>" + i18n.def.comboSpace + "</option>";
        $form.find("#landCity").change(function(){
            var value = $(this).val();
            if (value) {
                var key = "counties" + value;
                var obj = API.loadCombos(key)[key];
                $landArea.setItems({
                    item: obj,
                    value: L140S02Action._L140S02AData["landArea"] || ""
                });
            }
            else {
                $landArea.html(defaultSelect);
            }
            $landpart1.html(defaultSelect);
            $landpart2.html(defaultSelect);
            
        });
        
        /**
         * 鄉鎮切換
         */
        $landArea.change(function(){
            var value = $(this).val();
            if (value) {
                $landpart1.setItems({
                    item: L140S02Action.getSITE3($form.find("#landCity").find(":selected").text(), $landArea.find(":selected").text()),
                    value: L140S02Action._L140S02AData["landpart1"] || ""
                });
            }
            else {
                $landpart1.html(defaultSelect);
                
            }
            $landpart2.html(defaultSelect);
        });
        /**
         * 大段切換
         */
        $landpart1.change(function(){
            var value = $(this).val();
            if (value) {
                var key = "counties" + value;
                var obj = API.loadCombos(key)[key];
                $landpart2.setItems({
                    item: L140S02Action.getSITE4($form.find("#landCity").find(":selected").text(), $landArea.find(":selected").text(), value),
                    value: L140S02Action._L140S02AData["landpart2"] || ""
                });
            }
            else {
                $landpart2.html(defaultSelect);
            }
        });
        
        //是否搭配長擔貸款
        var $chkUsedRadio = $form.find("input[name=chkUsed]");
        
        /**
         *是否搭配長擔貸款
         */
        $chkUsedRadio.click(function(){
            var value = $(this).val();
            if (value == "Y") {
                $loginL140S02BTr.show();
            }
            else {
                $loginL140S02BTr.hide();
                $form.find("#showL140S02BStr").html("");
            }
        });
        
        //融資業務分類
        var $lnPurpose = $form.find("#lnPurpose");
        //用途別
        var $lnPurs = $form.find("#lnPurs");	
        
        $lnPurpose.change(function(v, k){
            $lnPurs.removeAttr("disabled");
            var value = $(this).val();
            if (!k) {
                switch (value) {
                    /**
                     * 「購置住宅貸款(其他)」(代號：2)、
                     * 「房屋修繕貸款(其他)」(代號：3)
                     * 「購置住宅貸款(非自用)」(代號：L)、
                     * 「房屋修繕貸款」(代號：N)、
                     * 融資業務分類「購置住宅貸款(自用)」(代號：M)、
                     之用途別須為「購置不動產」(代號：1)。*/
                    case "2":
                    case "3":
                    case "L":
                    case "N":
                    case "M":
                        $lnPurs.val("1").attr("disabled", "disabled");
                        break;
                    /**產品種類須為「土地融資個人戶」(代號：33)、
                     「建築融資個人戶」(代號：34)時，
                     融資業務分類「建築融資貸款」(代號：U)
                     之用途別須為「購置不動產」(代號：1)。
                     **/
                    case "U":
                        //產品值
                        var prodKindVal = $prodKind.val();
                        if (prodKindVal == "33" || prodKindVal == "34") {
                            $lnPurs.val("1");
                        }
                        else {
                            //是否屬興建房屋
                            $residential.val("N");
                        }
                        break;
                    /**
                     *融資業務分類「個人投資理財貸款」(代號：1)、
                     「職工福利貸款」(代號：P)
                     之用途別須為週轉金(代號：4) 。
                     */
                    case "1":
                        $lnPurs.val("4");
                        //page5.203=個人購買股票等金融商品之理財週轉金貸款！
                        API.showMessage(i18n.cls1151s01["page5.203"]);
                        break;
                    case "P":
                        
                        break;
                    
                    case "4": //逆向抵押貸款, 對應的 用途別 4-週轉金
                    	$lnPurs.val("4").attr("disabled", "disabled");
                        break;    
                    
                    case "O":
                        $lnPurs.val("2");
                        break;
                }
				
				$lnPurs.trigger("change");
            }
        });
		
		$lnPurs.change(function(v, k){
            var value = $(this).val();
			var showStr = "";
            if (!k) {
				switch (value) {
					case "1":
						showStr = i18n.cls1151s01["L140S02A.lnPursStr0101"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0102"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0103"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0104"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0114"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0105"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0106"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0107"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0108"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0109"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0110"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0111"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0112"];
						showStr = showStr + i18n.cls1151s01["L140S02A.lnPursStr0113"];
						API.showMessage(showStr + "<br><br><br>");
						break;
					case "2":
					case "3":
					case "4":
						$("#workingFundPurpose").show();
						showStr = i18n.cls1151s01["L140S02A.lnPursStr0201"];
						API.showMessage(showStr + "<br><br>");
						break;
				}
				
				switch (value) {
					case "4":
						$("#workingFundPurpose").show();
						break;
					default:
						$("#workingFundPurpose").val("").hide();
						break;
				}
			}
		});
		
		$lnPurs.trigger("change");
		
		
		$form.find("[name=esggnLoanFg]").click(function(){       
            if ($(this).val() == "Y") {
            	$("#tr_EsgGtype").show();
            }else if ($(this).val() == "N") {
            	$form.find("#esggtype").val("");
            	$("#esggtypeZ_div").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
            	$("#tr_EsgGtype").hide();
            }else{
            	$("#tr_EsgGtype").show(); //當未勾選時,先預設出現
            }
        });
		$form.find("#esggnLoanFg").trigger("change");
		
		$form.find("#esggtype").change(function(v, k){
            var value = $(this).val();
			
            if (value=="Z") {
            	$("#esggtypeZ_div").show();
			}else{
				$("#esggtypeZ_div").find(':input').not(':button, :submit, :reset, :hidden').not(':checkbox, :radio').val('');
				$("#esggtypeZ_div").hide();
			}
		});
		$form.find("#esggtype").trigger("change");
		
		//J-113-0397 個金簽報書新增社會責任授信欄位
		$form.find("[name=socialLoanFlag]").click(function(){    
            if ($(this).val() == "Y") {
            	$(".tr_socialLoan").show();
            }else if ($(this).val() == "N") {
            	$form.find("#socialKind").val("");
            	$form.find("#socialTa").val("");
            	$form.find("#socialResp").val("");
            	$(".tr_socialLoan").hide();
            }else{
            	$(".tr_socialLoan").show(); //當未勾選時,先預設出現
            }
        });
		$form.find("#socialLoanFlag").trigger("click");
		
        //代付費用管制迄期
        var $tconendTr = $form.find("#tconendTr");
        /**
         * 是否代付費用切換
         */
        $form.find("[name=isTakFee]").click(function(){
        
            if ($(this).val() == "Y") {
                $tconendTr.show();
            }
            else {
                _M.cleanTrHideInput($tconendTr);
            }
        });
        /**
         * 登錄最終評等box
         */
        $("#loginGradeBt").click(function(){
            L140S02Action.loginGrade()
        });
        
        
        /**
         * 清除最終評等
         */
        $("#cleanGrade").click(function(){
            L140S02Action.cleanGrade()
        });
        
        /**
         * 登錄長擔額度序號box
         */
        $("#openL140S02BBox").click(function(){
            L140S02BAction.openL140S02BBox();
        });
        /**
         * 新增長擔額度序號
         */
        $("#addL140S02B").click(function(){
            L140S02BAction.addL140S02B();
        });
        /**
         * 引進行銷分行
         */
        $("#getSellBank").click(function(){
            API.showAllBranch({
                btnAction: function(a, b){
                    $form.find("#sellBank").val(b.brNo);
                    $form.find("#sellBankShowName").html(b.brName);
                    $.thickbox.close();
                }
            });
        });
        /**
         * 團貸案
         */
        $("a#CheckResultLink").click(function(){
            var checkResult = $.trim($form.find("#checkResult").html());
            if (checkResult) {
                API.showMessage(checkResult);
            }
            else {
                //page5.191=尚未執行查詢
                API.showMessage(i18n.cls1151s01["page5.191"]);
            }
        });
        /**
         * 模糊比對建案名稱
         */
        $("#queryBuildNameByName").click(function(){
            var $buildNameSourse = $("#queryBuildNameByNameBoxDiv").find("#queryBuildNameByNameBoxName");
            $buildNameSourse.val("")
            $("#queryBuildNameByNameBox").thickbox({
                //L140M01A.msg091=建案名稱
                title: i18n.cls1151s01["L140M01A.msg091"],
                width: 600,
                height: 150,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if (!$("#queryBuildNameByNameForm").valid()) {
                            return false;
                        }
                        var buildNameSourse = $buildNameSourse.val();
                        if (buildNameSourse == "") {
                            //page5.180=建案名稱不得為空！！
                            return API.showErrorMessage(i18n.cls1151s01["page5.180"]);
                        }
                        var $buildName = $("#" + L140S02Action.formId).find("#buildName");
                        var buildName = $.trim($buildName.val());
                        _M.doAjax({
                            action: "queryBuildName",
                            data: {
                                buildName: buildNameSourse
                            },
                            success: function(obj){
                                if (obj && obj.msg) {
                                    $form.find("#checkResult").html(obj.msg);
                                    API.showMessage(obj.msg, $.thickbox.close());
                                    if (obj.cleanBuildName) {
                                        $buildName.val("");
                                    }
                                    else {
                                        $buildName.val(buildNameSourse);
                                    }
                                }
                                else {
                                    $buildName.val(buildNameSourse);
                                    $.thickbox.close();
                                }
                                
                            }
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
            
        });
        
        //J-109-0304_10702_B1003 Web e-Loan消金新增房仲引介來源 
        landAction.build(null);
        $("#addLandNo").click(function(){
        	$("#landNo1").val("");
        	$("#landNo2").val("");
        	$("#addLandNoBox").thickbox({
                //L140M01A.msg091=建案名稱
                title: i18n.cls1151s01["L140M01A.msg169"],
                width: 300,
                height: 150,
                modal: true,
                align: "center",
                valign: "bottom",
                readOnly: _openerLockDoc == "1",
                i18n: i18n.def,
                buttons: {
                    "sure": function(){
                        if (!$("#queryBuildNameByNameForm").valid()) {
                            return false;
                        }
                        var landNo1 = $("#landNo1").val();
                        var landNo2 = $("#landNo2").val();
                        if (landNo1 == "" || landNo2 == "") {
                            return API.showErrorMessage(i18n.cls1151s01["L140S02M.landNo"]+i18n.cls1151s01["page5.canNotEmpty"]);
                        }
                        var landCity = $form.find("#landCity").val();
                        var landArea = $form.find("#landArea").val();
                        var landpart1 = $form.find("#landpart1").val();
                        var landpart2 = $form.find("#landpart2").val();
                        if (landCity == "" || landArea == "" || landpart1 == "") {
                            return API.showErrorMessage(i18n.cls1151s01["L140S02A.landCity"]+i18n.cls1151s01["page5.canNotEmpty"]);
                        }
                        _M.doAjax({
                            action: "saveL140S02M",
                            data: {
                            	mainId: _M.tabMainId,
                            	landNo1: landNo1,
                            	landNo2: landNo2,
                            	landCity: landCity,
                            	landArea: landArea,
                            	landpart1: landpart1,
                            	landpart2: landpart2
                            },
                            success: function(obj){
                                if (obj.checkResult && obj.msg=="") {
                                	landAction.grid.reload();
                                    $.thickbox.close();
                                }
                                else if(obj.checkResult && obj.msg!=""){
                                	landAction.grid.reload();
                                	$.thickbox.close();
                                	return API.showMessage(obj.msg);
                                }
                                else{
                                	return API.showErrorMessage(obj.msg);
                                    $.thickbox.close();
                                }
                            }
                        });
                    },
                    "cancel": function(){
                        $.thickbox.close();
                    }
                }
            });
        });
        $("#delLandNo").click(function(){
        	var data = landAction.getgridRowData();
        	if (data) {
                MegaApi.confirmMessage(i18n.def["confirmDelete"], function(action){
                    if (action) {
                    	_M.doAjax({
                    		action: 'delL140S02M',
                            data: {
                            	oid :data.oid
                            },
                            success: function(obj){
                                if (obj.msg) {
                                	landAction.grid.reload();
                                    MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["confirmDeleteSuccess"]);
                                }
                            }
                        });
                    }
                });
            };
        });
        /**
         *是否為縣(市)政府首購貸款
         */
        var $kgAgreeTr = $form.find(".kgAgreeTr");
        $("[name=kgAgreeYN]").click(function(){
            //			if ($(this).val() == "Y") {
            if ($(this).val() != "0") {
                $kgAgreeTr.show();
            }
            else {
                _M.cleanTrHideInput($kgAgreeTr);
            }
            
        });
        
        //是否搭配房貸壽險利率優惠方案
        var $rmbintFlagTr = $form.find("#rmbintFlagTr");
        //是否保費融資
        var $insFlagTr = $form.find("#insFlagTr");
        /**房貸壽險方案**/
        $form.find("[name=rmbinsFlag]").click(function(){
            var value = $(this).val();
            if (value == "Y") {
                $rmbintFlagTr.show();
                $insFlagTr.show();
            }
            else {
                _M.cleanTrHideInput($rmbintFlagTr);
                _M.cleanTrHideInput($insFlagTr);
            }
        });
        
        /**是否搭配房貸壽險利率優惠方案**/
        var $rmbintTermDiv = $form.find("#rmbintTermDiv");
        //搭配房貸壽險利率優惠方案之期數
        var $rmbintFlagTr = $form.find("#rmbintFlagTr");
        $form.find("[name=rmbintFlag]").click(function(){
            var value = $(this).val();
            if (value == "N") {
                $rmbintTermDiv.show();
                // "是否保費融資" 與 "是否搭配房貸壽險利率方案且全期優惠 " 獨立
//                $insFlagTr.show();
            }
            else {
                _M.cleanTrHideInput($rmbintTermDiv);
                // "是否保費融資" 與 "是否搭配房貸壽險利率方案且全期優惠 " 獨立
//                _M.cleanTrHideInput($insFlagTr);
            }
        });
        /**是否保費融資**/
        //保費融資金額
        var $insLoanbalDiv = $form.find("#insLoanbalDiv");
        $form.find("[name=insFlag]").click(function(){
            var value = $(this).val();
            if (value == "Y") {
                $insLoanbalDiv.show();
            }
            else {
                _M.cleanTrHideInput($insLoanbalDiv);
            }
        });
        
        //登錄利率
        var $getRateDesc = $form.find("#getRateDesc");
        $getRateDesc.click(function(){
            L140S02CAction.openL140S02C();
        });
        
        //登錄償還方式
        var $getL140S02E = $form.find("#getL140S02E");
        $getL140S02E.click(function(){
            L140S02EAction.openL140S02E();
        });
        /**
         *是否辦理 代償 轉貸
         */
        var $chgCaseTr = $(".chgCaseTr");
        $form.find("#chgOther").click(function(){
            if ($(this).val() == "Y") {
                $chgCaseTr.show();
            }
            else {
                _M.cleanTrHideInput($chgCaseTr);
            }
        });
        //新增-代償轉貸借新還舊明細檔
        var $addL140S02HBt = $form.find("#addL140S02HBt");
        $addL140S02HBt.click(function(){
            L140S02HAction.openL140S02H();
        });
        
        //刪除-代償轉貸借新還舊明細檔
        var $delL140S02HBt = $form.find("#delL140S02HBt");
        $delL140S02HBt.click(function(){
            L140S02HAction.delL140S02H();
        });
        
        //代償收取手續費
        var $chargeFlag = $form.find("[name=chargeFlag]");
        var $chargeAmtDiv = $form.find("#chargeAmtDiv");
        $chargeFlag.click(function(){
        
            if ($(this).val() == "Y") {
                $chargeAmtDiv.show();
            }
            else {
                _M.cleanTrHideInput($chargeAmtDiv);
            }
        });
        //承接之前放款帳號
        var $orgLoanNoTr = $form.find("#orgLoanNoTr");
        //開戶原因
        var $transCode = $form.find("#transCode");
        $transCode.change(function(){
            var value = $(this).val();
            $orgLoanNoTr.hide();
            if (value == "1" || value == "3" || value == "3") {
                $orgLoanNoTr.show();
            }
        });

        // J-108-0283 變更條件Condition Change
        $("#queryL140S05A").click(function(){//readonly: inits.toreadOnly
            LMS140M01MAction.openCondChg(_M.tabMainId, _M.isReadOnly);
        });
        
        /**
         * 貸款評等表
         */
        $("#openL140S02KBox").click(function(){
            L140S02Action.openL140S02KBox();
        });
        
        /**
         引進仲介公司統一編號
         */
        $("#getL140S02JIntroId").click(function(){
            L140S02Action.getL140S02JIntroId();
            /**
             API.openQueryBox({
             defaultCustType: "2",
             forceNewUser: false,
             doNewUser: true,
             divId: L140S02Action.formId,
             autoResponse: { // 是否自動回填資訊
             id: "introId", //   統一編號欄位ID
             dupno: "introDupNo"//   重覆編號欄位ID
             }
             });
             */
        });
        /**
         * 期付金查詢
         */
        $("#queryPeriod").click(function(){
            L140S02Action.queryPeriod();
        });
        
        /**
         * 產生期付金對照表
         */
        $("#createPeriodBt").click(function(){
            L140S02Action.createPeriodBt();
        });
        /**
         *購置房屋擔保放款
         風險權數檢核表
         */
        $("#openC102M01ABox").click(function(){
            C102M01AAction.openBox();
        });
        
        
        $("#btn_chg_c102m01a_rptId").click(function(){
            C102M01AAction.chg_c102m01a_rptId();
        });
        
        /**
         *選擇不動產
         */
        $("#openSelectCMSBt").click(function(){
            L140M01OAction.open($form);
        });
        
        /**
         *清除不動產
         */
        $("#cleanCMSBt").click(function(){
            L140M01OAction.cleanL140M01O($form);
        });
        /**
         *引進帳務資料
         */
        $("#getloanNoBt").click(function(){
            var cntrNo = _M.AllFormData["04"]["cntrNo"];
            if (!cntrNo) {
                //L140M01A.msg007=請輸入額度序號
                return API.showErrorMessage(i18n.cls1151s01["L140M01A.msg007"]);
            }
            _M.doAjax({
                action: "queryLn030Loan",
                data: {
                    cntrNo: cntrNo
                },
                success: function(obj){
                    if (obj.error) {
                        return API.showErrorMessage(obj.error);
                    }
                    else {
                        var $ln030Select = $("#ln030Select");
                        $ln030Select.setItems({
                            space: false,
                            item: obj.loans,
                            format: "{value}"
                        });
                        //btn.loanNo=引進帳務
                        $("#openaLn030LoanBox").thickbox({
                            title: i18n.cls1151s01["btn.loanNo"],
                            width: 200,
                            height: 200,
                            modal: true,
                            align: "center",
                            valign: "bottom",
                            readOnly: _openerLockDoc == "1",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    $("#" + L140S02Action.formId).find("#loanNo").html(DOMPurify.sanitize($ln030Select.val()));
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                    
                }
            });
        });
        //其他詞庫
        $("a.lnOtherWord").click(function(){
            $("#lnOther").val($(this).text());
        });
        /**
         登錄費率
         */
        $("#getFreeRateDesc").click(function(){
            L140M01HAction.openBox();
        });
        /**
         * 售屋者統編登錄
         */
        $("#getSellerId").click(function(){
            API.openQueryBox({
                defaultCustType: "2",
                forceNewUser: false,
                doNewUser: true,
                auto: false,
                fn: function(obj){
                    $("#sellerId").val(obj.custid + obj.dupno);
                    $("#sellerName").val(obj.name);
                }
            });
        });
        
        /**
         * 提前還本違約金免收條件 -其他說明
         */
        var $tnfOther = $form.find("#tnfOther");
        $("[name=tnf][value=3]").click(function(){
            if ($(this).is(":checked")) {
                $tnfOther.removeAttr("disabled");
            }
            else {
                $tnfOther.attr("disabled", "disabled").val("");
            }
        })
        
        //是否重簽契約
        var $chgContract = $form.find("[name=chgContract]");
        //續約次數
        var $chgConTimes = $form.find("#chgConTimes");
        //當點選「是」要重簽契約時，則會顯示請通知客戶重簽契約，且如果02行家理財－短期擔保放款已續約次數>=6次；
        //02行家理財－短期放款已續約次數>=1次者，則一定會顯示[※請通知客戶重簽契約]。)
        $chgContract.click(function(){
            var subjCode2 = $subjCode.find(":selected").attr("subjCode2") || "  ";
            var chgConTime = parseInt($chgConTimes.val(), 10) || 0;
            var chgContractVal = $form.find("[name=chgContract]:checked").val();
            L140S02Action.hgContractThan6Show(subjCode2, chgConTime, chgContractVal);
        });
        
        /**
         * 綁訂在更新續約次數時
         */
        $chgConTimes.blur(function(){
            var subjCode2 = $subjCode.find(":selected").attr("subjCode2") || "  ";
            var chgConTime = parseInt($(this).val(), 10) || 0;
            var chgContractVal = $form.find("[name=chgContract]:checked").val();
            L140S02Action.hgContractThan6Show(subjCode2, chgConTime, chgContractVal);
        });
        
        
        /**
         * 次要性質切換
         * @param {Object} k
         * @param {Object} v
         */
        $("input[name=subProperty]").live("click", function(k, v){
            hs_hgContractTr();
        });
        
        $form.find("[name=ratePlan]").change(function(){
            var value = $(this).val();
            // J-113-0227 配合房貸核貸成數新增檢核邏輯，
			// 當[房貸利率方案]=06-青年安加購屋優惠貸款新增欄位[是否符合自住型房貸成長方案之8成條件]
            $("#isHousePlanEightyPerTr").hide();//預設先隱藏
            if (value == "20") {
                $("#div_L140S02F_ratePlan_house_item").show();
                _M.cleanTrHideInput( $("#isHousePlanEightyPerTr"));
            }else {
            	// J-113-0227 配合房貸核貸成數新增檢核邏輯，
    			// 當[房貸利率方案]=06-青年安加購屋優惠貸款新增欄位[是否符合自住型房貸成長方案之8成條件]
            	var isHousePlanEightyPer_value = $("input[name='isHousePlanEightyPer']:radio:checked").val();
            	if(value == "06"){
            		$("#isHousePlanEightyPerTr").show();
            	}else{
            		_M.cleanTrHideInput( $("#isHousePlanEightyPerTr"));
            	}
            	if(isHousePlanEightyPer_value == "Y"){
            		//如果[是否符合自住型房貸成長方案之8成條件]=Y 不可以清掉
            	}else{
                	_M.cleanTrHideInput( $("#div_L140S02F_ratePlan_house_item"));
            	}
            }
        });
        
        // J-113-0227 配合房貸核貸成數新增檢核邏輯，
		// 當[房貸利率方案]=06-青年安加購屋優惠貸款新增欄位[是否符合自住型房貸成長方案之8成條件]
        // 若[是否符合自住型房貸成長方案之8成條件]=Y，則開放填寫[自住型房貸成長方案成案條件]
        $form.find("#isHousePlanEightyPer").click(function(){
        	var isHousePlanEightyPer_value = $(this).val();
        	if( isHousePlanEightyPer_value == "Y"){
        		$("#div_L140S02F_ratePlan_house_item").show();
        	} else {
        		$("#div_L140S02F_ratePlan_house_item").hide();
        		_M.cleanTrHideInput( $("#div_L140S02F_ratePlan_house_item"));
        	}
        });
        
        $form.find("#imp_L140S02F_house_item3").click(function(){
        	 _M.doAjax({
                 action: "imp_L140S02F_house_item3",
                 data: {
                	 tabFormMainId: _M.tabMainId,
                     L140S02ASeq: L140S02Action.L140S02ASeq
                 },
                 success: function(json){
                	 $("#L140S02AForm").injectData(json);
                 }
        	 });     
        });
        $form.find("#imp_L140S02F_house_item7").click(function(){
       	 _M.doAjax({
             action: "imp_L140S02F_house_item7",
             data: {
            	 tabFormMainId: _M.tabMainId,
                 L140S02ASeq: L140S02Action.L140S02ASeq
             },
             success: function(json){
            	 $("#L140S02AForm").injectData(json);
             }
    	 });     
        });
    },
    /**
     * 控制次要性質
     * @param {Object} value 主要性質
     * @param {Object} isInit 為init動作
     */
    controllerSubProperty: function(value, isInit){
        //次要選項TR
        var $subPropertyTr = $("#subPropertyTr");
        if (!isInit) {
            $subPropertyTr.find("[name=subProperty]:checked").removeAttr("checked");
        }
        switch (value) {
            case "1":
            case "7":
            case "8":
            case "":
                $subPropertyTr.hide();
                $subPropertyTr.find("[name=subProperty]:checked").removeAttr("checked");
                break;
            default:
                $subPropertyTr.show();
                var itemsAll = API.loadCombos("L140S02A_property")["L140S02A_property"];
                var items = $.extend({}, itemsAll);
                delete items["1"];
                delete items["7"];
                delete items["8"];
                delete items[value];
                $("[name=subProperty]").setItems({
                    item: items,
                    value: isInit ? L140S02Action._L140S02AData["subProperty"] || "" : "",
                    size: 5
                });
                break;
        }
        
    },
    getL140S02JIntroIdGrid: null,
    getL140S02JIntroId: function(){
        if (!this.getL140S02JIntroIdGrid) {
            this.getL140S02JIntroIdGrid = $("#getL140S02JIntroIdGrid").iGrid({
                handler: _M.ghandle,
                height: 230,
                rownumbers: true,
                multiselect: false,
                hideMultiselect: false,
                rowNum: 10,
                action: "queryL140S01A",
                postData: {
                    tabFormMainId: _M.tabMainId
                },
                colModel: [{
                    colHeader: i18n.cls1151s01["L140S01A.custPos"],//性質,
                    name: 'custPos',
                    align: "left",
                    width: 60,
                    sortable: true
                }, {
                    colHeader: i18n.def["compID"],//統一編號,
                    name: 'custId',
                    align: "left",
                    width: 60,
                    sortable: true
                }, {
                    colHeader: i18n.cls1151s01["L140M01A.custName"],//姓名/名稱,
                    name: 'custName',
                    align: "left",
                    width: 60,
                    sortable: true
                }, {
                    colHeader: i18n.cls1151s01["L140S01A.type"],//新增
                    name: 'type',
                    align: "left",
                    width: 10,
                    sortable: true
                }, {
                    name: 'oid',
                    hidden: true
                }]
            });
        }
        else {
            this.getL140S02JIntroIdGrid.jqGrid("setGridParam", {
                postData: {
                    formAction: "queryL140S01A",
                    tabFormMainId: _M.tabMainId
                },
                search: true
            }).trigger("reloadGrid");
        }
        
        $("#getL140S02JIntroIdGridBox").thickbox({
            title: i18n.def['import'],
            width: 600,
            height: 380,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = L140S02Action.getL140S02JIntroIdGrid;
                    var obj = $grid.getSingleData();
                    if (obj) {
                        var $form = $("#" + L140S02Action.formId);
                        _M.doAjax({
                            action: "getL140S01AId",
                            data: {
                                oid: obj.oid
                            },
                            success: function(res){
                                $form.find("#introId").html(res.custId);
                                $form.find("#introDupNo").html(res.dupNo);
                                $.thickbox.close();
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 顯示是否重簽契約
     */
    hgContractThan6Show: function(subjCode2, chgConTime, chgContractVal){
        //通知客戶重簽契約訊息
        var $form = $("#" + L140S02Action.formId);
        var $hgContractThan6 = $form.find("#hgContractThan6");
        $hgContractThan6.hide();
        if (chgContractVal == "Y") {
            $hgContractThan6.show();
        }
        else {
            if (subjCode2) {
                if (subjCode2.substr(0, 1) == "1" && chgConTime >= 1) {
                    $hgContractThan6.show();
                }
                else 
                    if (subjCode2.substr(0, 1) == "2" && chgConTime >= 6) {
                        $hgContractThan6.show();
                    }
                    else {
                    }
            }
        }
    },
    /**
     * 查詢續約次數
     */
    find513: function(){
        var $form = $("#" + L140S02Action.formId);
        var $chgConTimes = $form.find("#chgConTimes");
        $chgConTimes.attr("disabled");
        _M.doAjax({
            action: "find513",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            success: function(obj){
            	ilog.debug("call find513, count="+obj.count);
                if (!L140S02Action._L140S02AData["chgConTimes"] || L140S02Action._L140S02AData["chgConTimes"] == 0) {
                    $chgConTimes.val(obj.count);
                }
                if (obj.isNull) {
                    $chgConTimes.removeAttr("disabled");
                }
                else {
                    $chgConTimes.attr("disabled", "disabled");
                }
            }
        });
        
    },
    
    /**
     * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip) 傳回段名稱(SITE3)
     */
    getSITE3: function(fCity, fZip){
        if (!fCity || !fZip) 
            return {};
        var SITE3 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIET3",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                byName: true,
                fCity: fCity,
                fZip: fZip
            },
            success: function(responseData){
                SITE3 = responseData['SITE3'];
            }
        });
        return SITE3;
    },
    
    /**
     * 傳入之縣市名稱(fCity)AND鄉鎮市區名稱(fZip)段名稱(SITE3) 傳回小段名稱(SITE4)
     */
    getSITE4: function(fCity, fZip, fSITE3){
        if (!fCity || !fZip || !fSITE3) 
            return {};
        var SITE4 = null;
        $.ajax({
            handler: "lmscommonformhandler",
            action: "querySIET4",
            type: 'post',
            async: false,
            formId: "empty",
            data: {
                byName: true,
                fCity: fCity,
                fZip: fZip,
                fSITE3: fSITE3
            },
            success: function(responseData){
                SITE4 = responseData['SITE4'];
            }
        });
        return SITE4;
    },
    /**
     * 期付金查詢
     */
    queryPeriod: function(){
        var $form = $("#" + L140S02Action.formId);
        var loanAmt = $form.find("#loanAmt").val();
        var lnYear = $form.find("#lnYear").val();
        var lnMonth = $form.find("#lnMonth").val();
        
        if (loanAmt == "") {
            //page5.156=動撥金額不可為空
            return API.showErrorMessage(i18n.cls1151s01["page5.156"]);
        }
        
        if (_M.AllFormData.docCode != "5") {
            if (lnYear == "" || lnMonth == "") {
                //page5.158=授信期間不可為空！
                return API.showErrorMessage(i18n.cls1151s01["page5.158"]);
            }
        }
        
        _M.doAjax({
            action: "queryPeriod",
            data: {
                oid: L140S02Action._L140S02AData.L140S02AOid,
                loanAmt: loanAmt,
                lnYear: lnYear,
                lnMonth: lnMonth
            },
            success: function(obj){
                if (obj.msg) {
                    return API.showErrorMessage(obj.msg);
                }
                else {
                    $form.injectData(obj);
                }
                
            }
        });
    },
    /**
     * 產生期付金對照表
     */
    createPeriodBt: function(){
        var $form = $("#" + L140S02Action.formId);
        var year = parseInt($form.find("#lnYear").val(), 10);
        var mon = parseInt($form.find("#lnMonth").val(), 10);
        var periodSDate = $form.find("#periodSDate").val();
        var loanAmt = parseInt(util.delComma($form.find("#loanAmt").val()), 10);
        if (loanAmt == "" || $.isNaN(loanAmt)) {
            //page5.156=動撥金額不可為空
            return API.showErrorMessage(i18n.cls1151s01["page5.156"]);
        }
        if (_M.AllFormData.docCode != "5") {
            if ($.isNaN(year) || $.isNaN(mon)) {
                //page5.158=授信期間不可為空！
                return API.showErrorMessage(i18n.cls1151s01["page5.158"]);
            }
        }
        
        
        if (periodSDate == "") {
            //page5.179=期付金起日不得為空！！
            return API.showErrorMessage(i18n.cls1151s01["page5.179"]);
        }
        $.form.submit({
            url: "../simple/FileProcessingService",
            target: "_blank",
            data: {
                tabMainId: _M.tabMainId,
                L140S02ASeq: L140S02Action.L140S02ASeq,
                year: year,
                mon: mon,
                periodSDate: periodSDate,
                loanAmt: loanAmt,
                rptOid: "R95",
                fileDownloadName: "LMS9091R01.pdf",
                serviceName: "cls1141r01rptservice"
            }
        });
    },
    /**
     * 開啟 貸款評等表
     */
    openL140S02KBox: function(){
        var $form = $("#L140S02KForm");
        $form.reset();
        _M.doAjax({
            action: "queryL140S02K",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            success: function(obj){
                $form.injectData(obj);
            }
        });
        
        $("#L140S02KBox").thickbox({
            //貸款額度評等表 
            title: i18n.cls1151s01["page5.011"],
            width: 750,
            height: 500,
            modal: true,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    if ($form.valid()) {
                        _M.doAjax({
                            action: "saveL140S02K",
                            formId: "L140S02KForm",
                            success: function(obj){
                            }
                        });
                    }
                    
                    
                },
                "calculate": function(){
                
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 判斷是否為房貸產品
     * @param {Object} value 產品值
     */
    isHouse: function(value){
        var houseType = ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20"
                         , "21", "22", "23", "24", "25", "26", "27", "28", "30", "31", "35", "38", "39"
                         , "56", "57", "59"
                         , "63", "64", "65", "66"
                         , "72"
                         ];
        for (var key in houseType) {
            if (value == houseType[key]) {
                return true;
            }
        }
        return false;
        
    },
    /**
     * 初始化下拉選單
     * @param {Object} formId
     */
    initItem: function(formId){
    	ilog.debug("@PanelAction05 :: initItem(...) > formId="+(formId||'') +"" );
    	if(true){
    		if(formId=="L140S02AForm"){
    			$("#" + formId+" #isCredit").setItems({
        			item: API.loadOrderCombosAsList("L140S02F_isCredit")["L140S02F_isCredit"],
                    format: "{key}"
    		    });	
    		}else{
    			//...
    		}    		    		
    	}
        //產生下拉選單
        var $div = $("#" + formId).find("[itemType]");
        var allKey = [];
        $div.each(function(){
            allKey.push($(this).attr("itemType"));
        });
        L140S02Action.itemCode = API.loadCombos(allKey);
        $div.each(function(){
            var $obj = $(this);
            var itemType = $obj.attr("itemType");
            if (itemType) {
                var format = $obj.attr("itemFormat") || "{value} - {key}";
                $obj.setItems({
                    space: $obj.attr("space") || true,
                    item: L140S02Action.itemCode[itemType],
                    format: format,
                    //sort: "asc",
                    size: $obj.attr("itemSize")
                });
            }
        });
    },
    DfundSelect: null,
    /**
     * 取得資金來源小類
     * @param {Object} fFund 資金來源大類
     */
    getDFund: function(fFund){
        if (!L140S02Action.DfundSelect) {
        	ilog.debug("@ajax > getDFund");
            _M.doAjax({
                action: "getDFund",
                async: false,
                success: function(objs){
                    L140S02Action.DfundSelect = objs;
                }
            });
        }
        return L140S02Action.DfundSelect[fFund] || "";
    },
    /**
     *產品的相關選單
     */
    getProdSelect: function(){
    	ilog.debug("@ajax > getProdSelect");
        var $form = $("#" + L140S02Action.formId);
        var $prodKind = $form.find("#prodKind");
		var $srcProdKind = $form.find("#srcProdKind");
        _M.doAjax({
            action: "getProdSelect",
            async: false,
            success: function(objs){
                var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                for (var i in objs["obj"]) {
                    var value = objs["obj"][i];
                    var disabled = value["isCanCel"] ? "disabled=disabled isCanCel='Y'" : "";
                    temp += "<option value='" + value["key"] + "' subjectData='" + JSON.stringify(value["subjectData"]) + "' " + disabled + " >" + value["key"] + "-" + value["name"] + "</option>";
                }
                
                $prodKind.html(temp);
                $srcProdKind.html(temp);
            }
        });
        
    },
    /**
     *房貸利率方案的相關選單
     */
    getRatePlanSelect: function(){
    	ilog.debug("@ajax > getRatePlanSelect");
        var $form = $("#" + L140S02Action.formId);
        var $ratePlan = $form.find("#ratePlan");
        _M.doAjax({
            action: "getRatePlanSelect",
            async: false,
            success: function(objs){
            	if(true){
            		var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                    for (var i in objs["obj"]) {
                        var value = objs["obj"][i];
                        var disabled = value["isCanCel"] ? "disabled=disabled isCanCel='Y'" : "";
                        temp += "<option value='" + value["key"] + "' subjectData='" + JSON.stringify(value["subjectData"]) + "' " + disabled + " >" + value["key"] + "-" + value["name"] + "</option>";
                    }
                    
                    $ratePlan.html(temp);	
            	}
            	if(true){
            		var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                    for (var i in objs["objProdPlan"]) {
                        var value = objs["objProdPlan"][i];
                        var disabled = value["isCanCel"] ? "disabled=disabled isCanCel='Y'" : "";
                        temp += "<option value='" + value["key"] + "'  " + disabled + " >" + value["key"] + "-" + value["name"] + "</option>";
                    }
                    
                    $form.find("#prodPlan").html(temp);	
            	}
                
            }
        });
        
    },
    /**
     * 開啟產品種類box
     * @param {Object} clumn
     * @param {Object} options
     * @param {Object} data grid 上的資料
     */
    openBox: function(clumn, options, data){
        //開啟成第一個頁面
        $("#L140S02ATabs").tabs({
            selected: 0
        });
        //====================================================================
        //因沒有 tempSave 在開啟「產品資訊」前
        ilog.debug("@ajax-BEG > openBox-BEG > CLS1151M01FormHandler::tempSaveAtCLS1151");
        $.ajax({
            async: false,
            handler: _M.fhandle,
            data: {
                formAction: "tempSaveAtCLS1151",
                tabFormMainId: _M.tabMainId,
                formData: JSON.stringify(_M.AllFormData),
                packLoan: _M.AllFormData["01"]["packLoan"]
            },
            success: function(obj){
            }
        });
        
        ilog.debug("@ajax-BEG > openBox-BEG > CLS1151M01FormHandler::queryL140S02A");
        var $form = $("#" + L140S02Action.formId);
        
        _M.doAjax({
            action: "queryL140S02A",
            data: {
                oid: data.oid
            },
            success: function(obj){
                L140S02Action.L140S02ASeq = obj.seq;
                L140S02Action._L140S02AData = obj;
                
                //先把值都帶上來才能初始化。
                L140S02Action.initL140S02A();
                //J-113-0036 配合於新增全新產品時，新增產品種類類型功能，每次進來要重新LOAD一次全部的產品
                L140S02Action.getProdSelect();
                L140S02HAction._reloadL140S02HGrid({
                    tabFormMainId: _M.tabMainId,
                    L140S02ASeq: L140S02Action.L140S02ASeq
                });
                
                // 當償還方式為6,7則不可以打期付金的項目
                var $periodDiv = $("#periodDiv");
                
                if (obj.hidePeriodDiv) {
                    $periodDiv.hide();
                }
                else {
                    $periodDiv.show();
                }
                //隱藏代償的選項
                var $chgCaseTr = $(".chgCaseTr");
                if (obj.chgCase == "Y") {
                    $chgCaseTr.show();
                }
                else {
                    $chgCaseTr.hide();
                }
				
                // 填入產品資訊的值
                $form.injectData(obj);
			     
				// 填入舊案產品科目
				
					var attr_subjectData = $("#srcProdKind").find(":selected").attr("subjectData");
					var subjectData = [];
					if(attr_subjectData && attr_subjectData.length>0){
						subjectData = JSON.parse( attr_subjectData );
					}
					var subjectTemp = "";
	            	for (var i in subjectData) {
	                    var subjCode = subjectData[i].subjCode;
	                	var subjCode2 = subjectData[i].subjCode2;
	                	var subjNm = subjectData[i].subjNm;
	                	var rIntWay = subjectData[i].rIntWay || "";
	                	var disabled = subjectData[i].isCanCel == "Y" ? "disabled=disabled isCanCel='Y'" : "";
	                	
	                	subjectTemp += "<option value='" + subjCode + "' subjCode2='" + subjCode2 + "'  rIntWay='" + rIntWay + "' " + disabled + ">" + subjCode + "-" + subjNm + "</option>";
	            	}
					$("#srcSubjCode").html(subjectTemp);
					//---
					$("#srcSubjCode").val( obj.srcSubjCode );
				
				
				// J-113-0036 新增 額度明細表產品資訊 時，將產品種類下拉選單選項做細部分類
				// 如果只有產品有值(產品種類類型沒有) = 舊案 or 複製來的，那就不用開放[產品種類類型]給他選擇 (直接隱藏)
				// 如果1.產品沒有值 or 2.產品種類類型有值
				// 才顯示[產品種類類型]的選單
				if(L140S02Action.isprodKindTypeOn == ""){//[產品種類類型]切換功能是否啟用
					L140S02Action.isprodKindTypeOn = obj.prodKindTypeOn;
				}
				if(($form.find("#prodKind").val() == "" || $form.find("#prodKindType").val() != "")
						&& L140S02Action.isprodKindTypeOn == "Y" ){
					$form.find("#prodKindType").show();
				}else{
					$form.find("#prodKindType").hide();
				}
				
				
                //外勞貸款檔-保證金
                if (obj.guaType) {
                    var value = obj.guaType.split("|");
                    $form.find("[name=guaType]").val(value);
                }
                //外勞貸款檔-償還方式  
                if (obj.subPayWay) {
                    var value = obj.subPayWay.split("|");
                    $form.find("[name=subPayWay]").val(value);
                }
                //承諾書
                if (obj.undertaking) {
                    var value = obj.undertaking.split("|");
                    $form.find("[name=undertaking]").val(value);
                }
                $form.find("select").each(function(v, k){
                    try {
                        $(k).trigger("change", "init");
                    } 
                    catch (e) {
                        alert("error select Id==>" + $(k).attr("id"));
                    }
                });
                
                //團貸母戶在簽報書新增時就已決定，且不能變更。
                if (_M.AllFormData.docCode == "5") {
                    //團帶母戶顯示欄位和隱藏欄位
                    $form.find(".docCode5Hide").hide();
                    $form.find(".docCode5Show").show();
                }
                //當已有帳務資料隱藏按鈕
                //2013_06_05_移除引進帳務按鈕。
                //var $getloanNoBt = $("#getloanNoBt");
                var cond_1 = ((obj.loanNo || _M.isReadOnly || obj.secNo)?"Y":"N");                
                z_debug("(obj.loanNo || _M.isReadOnly || obj.secNo)=="+ cond_1);
                
                
                if (cond_1=="Y") {
                    //$getloanNoBt.hide();                    
                    /**2013-10-18 因為a-loan有提供中長期科目互轉的功能，故原本擋掉舊資料不能改科目的限制先行拿掉
                     條件為1>短和中長不能互換 2>計息方息不能互換。 **/
                    var $subjCode2 = $form.find("#subjCode2");
                    var $prodKind = $form.find("#prodKind");
                    var $subjCode = $form.find("#subjCode");
                    var $property = $form.find("#property");
                    
                    /* 
	                    $subjCode2 代表3碼的授信科目
					           不是寫在 html 裡，而是在 CLS1151M01FormHandler::queryL140S02A 裡
					           當 injectData(...)時，把該欄位塞成
					    <input id="subjCode2" type="hidden" value="..." name="subjCode2">        
	                */
                    
                    var cond_2 = (($subjCode2.length && $subjCode2.val() != "")?"Y":"N");
                    z_debug(" ($subjCode2.length && $subjCode2.val() != '')=="+ cond_2);	
                    
                    
                    if (cond_2=="Y") {
                        //if (("3,4,5,6,7,8".indexOf($subjCode2.val().substring(0, 1)) > -1) && ($property.val() != "7" && $property.val() != "8")) {
                            $prodKind.removeAttr("disabled");
                            $subjCode.removeAttr("disabled");
                        //}
                    }
                    else {
                        $form.find(".oldCaseDisabled").attr("disabled", "disabled");
                    }
                    
                }
                else {
                    //$getloanNoBt.show();
                    $form.find(".oldCaseDisabled").removeAttr("disabled");
					//$form.find("#srcProdKind").attr("disabled", "disabled");
					//$form.find("#srcSubjCode").attr("disabled", "disabled");
                    
                }
                
                if(true){
                	/*
						在 injectData 時, 會 trigger  $property.change(function(k, v){
						裡面已把 prodKind, subCode 給 disabled
						
                	 	但在上面的  if (obj.loanNo || _M.isReadOnly || obj.secNo) {
                	 	又會 cancel  disabled
                	 	
                	 	加強判斷
                	*/
                	if( $form.find("#srcProdKind").val()=='67' || $form.find("#srcProdKind").val()=='70' ){
                    	$prodKind.attr("disabled", "disabled");
                        $subjCode.attr("disabled", "disabled");
                    }
                }
                
                //設定續約文字顯示
                L140S02Action.hgContractThan6Show(obj.subjCode2, obj.chgConTimes, obj.chgContract);
                var buttons = {
                    "saveData": function(){
                        _M.verifyCntrNoAction().done(function(json){
                            L140S02Action.saveL140S02A();
                        });
                    },
                    "del": function(){
                        L140S02Action.delL140S02A();
                        
                    },
                    "close": function(){
                        $.thickbox.close();
                    }
                };
                if (_M.isReadOnly) {
                    delete buttons['del'];
                    delete buttons['saveData'];
                    $form.readOnlyChilds();
                    
                    if(true){
                    	$("button#rmCalc").hide();
                    }
                    
                    $("a.lnOtherWord").hide();
                }
                else {
                    if (_M.isByHeadAndArea()) {
                        if (obj.loanNo) {
                            $(".HeadAndAreaDisabled").attr("disabled", "disabled");
                        }
                        //當非授管處或營運中心新增的，不可以有刪除按鈕
                        if (obj.creatSrc != "2") {
                            delete buttons['del'];
                        }
                        
                    }
                }
                
                //當為舊案轉入，不可以有刪除按鈕
                if (obj.creatSrc == "0") {
                    if(obj.allowDelLnProd && obj.allowDelLnProd=="Y"){
                    	
                    }else{
                    	delete buttons['del'];	
                    }
                }
                
                /**
                 * 是否屬優予核貸對象有值時其后之產品種類只限做60	青年築夢創業啟動金貸款
                 *是否為育成中心輔導有值時其后之產品種類只限做58	青年創業貸款
                 * 屬優予核貸對象/育成中心輔導
                 * <p/>
                 * 1|屬優予核貸對象<br/>
                 * 2|為育成中心輔導
                 **/
                var $prodKind = $form.find("#prodKind");
                var $subjCode = $form.find("#subjCode");
                
                
                if (_M.AllFormData["01"]["younCreatYN"] == "Y") {
                    if (_M.AllFormData["01"]["assisType"] == "1") {
                        $prodKind.val("60").attr("disabled", "disabled").trigger("change", "init");
                        $prodKind.attr("disabled", "disabled");
                    }else if (_M.AllFormData["01"]["assisType"] == "3") {
                        $prodKind.val("61").attr("disabled", "disabled").trigger("change", "init");
                    }else if (_M.AllFormData["01"]["assisType"] == "2") {
                        $prodKind.val("58").attr("disabled", "disabled").trigger("change", "init");
                    }
                }
                var $periodSTr = $form.find("#periodSTr");
                $periodSTr.show();
                //當為按月計息或 舊案 無產生期付金對照表
                if ((obj.L140S02C_IntWay == "1" || obj.L140S02C_IntWay == "P" || obj.L140S02C_IntWay == "Q") 
                		|| L140S02Action._L140S02AData.creatSrc == "0") {
                    $periodSTr.hide();
                }
                var $for07Tr = $form.find("#for07Tr");
                /*
                 * 若要出現 期付金對照表
                 * 可以在 償還方式＞登錄＞在 tb 按儲存後，會顯示
                 */
                ilog.debug("期付金對照表[cond1:"+($periodSTr.is(":hidden"))+"],[cond2:"+(L140S02Action._L140S02AData.prodKind != "07")+"]");
                if ($periodSTr.is(":hidden") && L140S02Action._L140S02AData.prodKind != "07") {
                    $periodDiv.hide();
                }
                /**
                 * 營運中心/授管處的審核狀態時如果是之前已核准過的舊案額度明細表的
                 (2)產品資訊中的[產品種類/產品種類]亦不提供異動
                 */
                if (_M.isByHeadAndArea() && obj.creatSrc != "2") {
                    /**2013-10-18 因為a-loan有提供中長期科目互轉的功能，故原本擋掉舊資料不能改科目的限制先行拿掉
                     條件為1>短和中長不能互換 2>計息方息不能互換。 **/
                    var $subjCode2 = $form.find("#subjCode2");
                    var $property = $form.find("#property");
                    if ($subjCode2.length && $subjCode2.val() != "") {
                        //if (("3,4,5,6,7,8".indexOf($subjCode2.val().substring(0, 1)) > -1) ||
                        //obj.creatSrc != "2" && ($property.val() != "7" && $property.val() != "8")) {
                            if (_M.itemType == "2") {
                                $prodKind.removeAttr("disabled");
                                $subjCode.removeAttr("disabled");
                            }
                        //}
                    }
                    else {
                        $prodKind.attr("disabled", "disabled");
                        $subjCode.attr("disabled", "disabled");
                    }
                }
                
                if(obj.lock_l140s02f_house_item=="Y"){
                	$form.find(".l140s02f_house_item").attr("disabled", "disabled");                    
                }
                //J-113-0106 配合消金處，E-LOAN個金授信管理系統調整自住型房貸成長方案相關文字修
        		$("#house_item1").find("option[value='Y']").hide();
        		$("#house_item1").find("option[value='N']").hide();
                if(typeof L140S02Action._L140S02AData.new_houseItemDate != "undefined"){
                	if(L140S02Action._L140S02AData.createTime < L140S02Action._L140S02AData.new_houseItemDate){
                		$(".tr_old_house_item1").show();
                		$(".tr_new_house_item1").hide();
                	}else{
                		$("input[name='new_house_item1'][value='"+DOMPurify.sanitize(L140S02Action._L140S02AData.house_item1)+"']").attr("checked", true);
                		$(".tr_old_house_item1").hide();
                		$(".tr_new_house_item1").show();
                	}
                	
                }
                
                //當產品種類 或 授信科目沒有值要可以修改                
                if (_M.isReadOnly) {
                    if (!$prodKind.val()) {
                        $prodKind.removeAttr("disabled");
                    }
                    if (!$subjCode.val()) {
                        $subjCode.removeAttr("disabled");
                    }
                }
                
                L140S02Action.refresh_l140s02a_gradeDiv(obj.modelKind);
                //選用模型
                if (obj.modelKind == "0") {
                    //清空
                    $("#cleanGrade").trigger('click');
                }
                
                if(true){
                	ilog.debug("obj.chg_c102m01a_rptId="+obj.chg_c102m01a_rptId);
                	var $btn_chg_c102m01a_rptId = $("#btn_chg_c102m01a_rptId");
                	if(obj.chg_c102m01a_rptId=="Y"){
                		$btn_chg_c102m01a_rptId.show();
                    	if (_M.isReadOnly) {
                    		$btn_chg_c102m01a_rptId.attr("disabled", "disabled");
                    	}else{
                    		$btn_chg_c102m01a_rptId.removeAttr("disabled");	
                    	}
                    }else{
                    	$btn_chg_c102m01a_rptId.hide();
                    }	
                }                

                
                if (_M.AllFormData.docCode == "5") {
            		
            	}else{
            		//補充判斷，一開始只有「綠色支出類型」
            		//後來才增加「綠色授信註記」 => 可能DB中，「綠色支出類型」有值 但「綠色授信註記」空白
            		if(obj.esggtype && obj.esggtype!=""){
            			$("#tr_EsgGtype").show();
            		}
            	}
                
                /*
                  	若不區分[房貸、非房貸]的產品, 一律都強迫寫入 L140S02F.isCredit=N, L140S02F.isTakFee=N
                  	會造成在列印 CLS1151R03 產品資訊(附表)時，印出「房貸資訊」欄位 => 不合理
                	var dataSrc = _M.AllFormData["04"].dataSrc;
                	if(dataSrc=="1" || dataSrc=="2"){
                		$("#L140S02AForm").injectData( {"isCredit":"N", "isTakFee":"N"} );
                		$("#L140S02AForm").find("[name=isCredit]").attr("disabled", "disabled");
                		$("#L140S02AForm").find("[name=isTakFee]").attr("disabled", "disabled");
                	}
                */
                
                $("#L140S02ABox").thickbox({
                    //cls1151s01.title05=產品資訊
                    title: i18n.cls1151s01["cls1151s01.title05"],
                    width: 900,
                    height: 500,
                    modal: true,
                    readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    buttons: buttons
                });
				// 20140328增加處理提前還本違約金免收條件：畫面顯示問題
				if (obj.tnf) {
					var tnfs = obj.tnf.split("|");
					$(tnfs).each(function(index,value){
						$form.find("input[name='tnf'][value='"+ value + "']").attr("checked", true);
					})	
				}

				if(_M.AllFormData.cntrNoBelongCLS=="N"){
					
				}else{
					/*
					 J-106-0228 企業負責人以個人名義借款，資金用途為興建廠房供所
					 		         營企業使用，如確屬供企業擴增生產能量者，得不計入
					 		         銀行法第72條之2計算
					 		         
					 => 個人戶  允許選擇 "融資業務分類 #"		         
					*/
					//為了呈現 企業戶 的 融資業務分類，但 消金不能選					
					$.each( ["@"], function(idx, val){					
						$form.find("#lnPurpose option[value='"+val+"']").attr("disabled", "disabled");	
	            	});
				}
					        
				$("#lnPurs").trigger("change");
				
				ilog.debug("@ajax-END > openBox-END > CLS1151M01FormHandler::queryL140S02A");
            }
        });
        
    },
    
    
    
    
    
    
    saveL140S02A: function(){
        var $form = $("#" + L140S02Action.formId);
        if (!$form.valid()) {
            //page5.151=尚有欄位未填妥，請完成後再執行此動作。
            //   API.showErrorMessage(i18n.cls1151s01["page5.151"]);
            return false;
        }
        //外勞貸款檔-保證金
        var guaTypeData = [];
        $form.find("[name=guaType]:checked").each(function(){
            guaTypeData.push($(this).val());
        });
        
        //外勞貸款檔-償還方式  
        var subPayWayData = [];
        $form.find("[name=subPayWay]:checked").each(function(){
            subPayWayData.push($(this).val());
        });
        //承諾書
        var undertakingData = [];
        $form.find("[name=undertaking]:checked").each(function(){
            undertakingData.push($(this).val());
        });
        if (this.checkLnSelect($form) && this.saveCheck($form)) {
        	this.cfmL140S02A($form).done(function(){

            _M.doAjax({
                action: "saveL140S02A",
                formId: L140S02Action.formId,
                data: {
                    isBuilder: $form.find("#isBuilder").val(),
                    undertaking: undertakingData.join("|"),
                    guaType: guaTypeData.join("|"),
                    subPayWay: subPayWayData.join("|"),
                    oid: L140S02Action._L140S02AData.L140S02AOid
                },
                success: function(obj){
                    if (obj.error) {
                        API.showErrorMessage(obj.error);
                    }
                    if ($form.find("#property").val() == "7") {
                        //L140M01A.msg088=產品資訊之承做性質設為不變則無法續做動審表。
                        API.showMessage(i18n.cls1151s01["L140M01A.msg088"]);
                    }
                    
                    _M.refresh_M_key(obj);
                    
                    L140S02HAction._reloadL140S02HGrid();
                    L140S02Action._reloadGrid();
                    //因為會更新性質所以重新整理額度明細表主檔grid
                    _M._triggerMainGrid();
                }
            });
        	});
        }
    },
    
    /**
     *儲存前檢查
     * @param {Object} $form  表單上的內容
     * return 是否通過檢查
     */
    saveCheck: function($form){
        var result = false;
        //動撥金額
        var loanAmt = parseInt(util.delComma($form.find("#loanAmt").val()), 10);
        //應計入DBR22倍規範額度
        var DBR22Amt = parseInt(util.delComma($form.find("#DBR22Amt").val()), 10);
        if (DBR22Amt > loanAmt) {
            //page5.159=應計入DBR22倍規範額度不可以大於動撥金額。
            API.showErrorMessage(i18n.cls1151s01["page5.159"]);
            return false;
        }
        var prodKind = $form.find("#prodKind").val();
        var isCancelPodeKind = $form.find("#prodKind option:selected").attr("isCanCel") == "Y";
        var property = $form.find("#property").val();
        if (property == "1" && isCancelPodeKind) {
            API.showErrorMessage(i18n.cls1151s01["page5.204"]);
            //page5.204=該產品於性質新作時不可承做！
            return false;
        }
		
		var subjCode = $form.find("#subjCode").val();
		var srcLoanNo = $form.find("#srcLoanNo").val();
		var srcProdKind = $form.find("#srcProdKind").val();
		var srcSubjCode = $form.find("#srcSubjCode").val();
		// NULL或空值不會進入
		if (srcLoanNo) {
			// alert("1 >>" + srcLoanNo + " >> " + subjCode  + " >> " + srcSubjCode + " >> " + prodKind  + " >> " + srcProdKind);
			if (subjCode == srcSubjCode && prodKind == srcProdKind) {
				$form.find("#loanNo").val(srcLoanNo);
			//	alert("2 >>" + srcLoanNo + " >> " + subjCode  + " >> " + srcSubjCode + " >> " + prodKind  + " >> " + srcProdKind);
			} else if (subjCode == srcSubjCode) {
			    $form.find("#loanNo").val(srcLoanNo);
			} else {
				$form.find("#loanNo").val("");
			//	alert("3 >>" + srcLoanNo + " >> " + subjCode  + " >> " + srcSubjCode + " >> " + prodKind  + " >> " + srcProdKind);
			}
		}
		
        /**
         * <pre>
         (2)金額不可>TWD15萬元。
         *如果大於等於10萬且小於等於15萬時則出下列提示訊息
         "【本訊息僅為提醒使用者，不影響文件儲存！】"
         "外勞貸款最高限額為１０萬元，惟資本額達兩億元或經濟部公業局核准投資額達兩億元以上公司之外勞，最高貸款金額得提高為新台幣１５萬元！"
         *如果大於15萬則提示下列訊息
         "外勞貸款最高限額為１５萬元！"
         * </pre>
         **/
        var lnYear = $form.find("#lnYear").val();
        var lnMonth = $form.find("#lnMonth").val();
        var year = parseInt($form.find("#lnYear").val(), 10);
        var mon = parseInt($form.find("#lnMonth").val(), 10);
        var totalMon = year * 12 + mon;
        var $optionSubjCode = $form.find("#subjCode option:selected");
        var isCancelSubject = $optionSubjCode.attr("isCanCel") == "Y";
        if (property == "1" && isCancelSubject) {
            //page5.205=該授信科目於性質新作時不可承做！
            API.showErrorMessage(i18n.cls1151s01["page5.205"]);
            return false;
        }
        var subjCode2 = $optionSubjCode.attr("subjCode2");
        switch (prodKind + "") {
            case "07":
                //額度明細表產品種類07消費性貸款時，
                //其[期付金是否超過薪水1／3]/[期付金]為必要欄位。
                if ($form.find("[name=periodChk]:checked").val() == "" || $form.find("#periodAmt").val() == "") {
                    if (_M.AllFormData.docCode != "5") {
                    //團貸總戶之額度明細表不需控管期付金1/3的作業。
                    //page5.172=[期付金是否超過薪水1／3]/[期付金]為必要欄位。
                    //改為後端檢查
                    //API.showErrorMessage(i18n.cls1151s01["page5.172"]);
                    //return false;
                    }
                }
                break;
            case "32":
                if (loanAmt >= 100000 && loanAmt <= 150000) {
                    //page5.188=【本訊息僅為提醒使用者，不影響文件儲存！】<br/>外勞貸款最高限額為１０萬元，惟資本額達兩億元或經濟部公業局核准投資額達兩億元以上公司之外勞，最高貸款金額得提高為新台幣１５萬元！
                    API.showErrorMessage(i18n.cls1151s01["page5.188"]);
                }
                else 
                    if (loanAmt > 150000) {
                        // page5.189=外勞貸款最高限額為１５萬元！
                        API.showErrorMessage(i18n.cls1151s01["page5.189"]);
                        return false;
                    }
                break;
            case "38":
            case "39":
                if (subjCode2) {
                    //		產品38/39時，科目為473/673(購置)者
                    //    內政部整合住宅方案 - 購置住宅 額度不得超過 220萬！
                    //    內政部整合住宅方案 - 購置住宅 期限不得超過20年！
                    //     內政部整合住宅方案 - 購置住宅寬限期不得超過五年！
                    if (subjCode2 == "473" || subjCode2 == "673") {
//    	                                       移到 server 端判斷
//                        if (loanAmt > 2200000) { 
                            //page5.165=內政部整合住宅方案 - 購置住宅 額度不得超過 220萬！
//                            API.showErrorMessage(i18n.cls1151s01["page5.165"]);
//                            return false;
//                        }             
                        
						var chkNumber = $form.find("#chkNumber").val().substring(0,3);

						
						/* 依 J-111-0019 , (111)第(0135)號程修
						 * ● 109年度前的規定「償還年限」最長20年
						 * ● 110年度的規定，條文把「償還年限」替換成「補貼年限」 => 提案人表示「償還年限」可超過20年，但是只會補貼前20年
						 */
						/* if (chkNumber > 101) {
							if (totalMon > 240) {
                            //page5.166=內政部整合住宅方案 - 依（102）兆銀總授管字第18969號函，102年度起之購置住宅 期限不得超過20年！</br>請再度確認房貸相關>優惠房貸>核准編號之前三碼是否屬102年以前案件。</br>核發證明編號應為10位，除第5碼為英文大寫，其餘皆為數字，如【0991A11111】
                            API.showErrorMessage(i18n.cls1151s01["page5.166"]);
                            return false;
                        }
						} */
                        
                    //page5.167=內政部整合住宅方案 - 購置住宅寬限期不得超過五年！
                    }
                    //   產品38/39時，科目為474/674(修繕)者
                    //    內政部整合住宅方案 - 修繕住宅 額度不得超過 80萬！
                    //    內政部整合住宅方案 - 修繕住宅 期限不得超過15年！
                    //    內政部整合住宅方案 - 購置住宅寬限期不得超過三年！
                    if (subjCode2 == "474" || subjCode2 == "674") {
                        if (loanAmt > 800000) {
                            //page5.170=內政部整合住宅方案 - 修繕住宅 額度不得超過 80萬！
                            API.showErrorMessage(i18n.cls1151s01["page5.170"]);
                            return false;
                        }
                        
                        if (totalMon > 180) {
                            //page5.171=內政部整合住宅方案 - 修繕住宅 期限不得超過15年！
                            API.showErrorMessage(i18n.cls1151s01["page5.171"]);
                            return false;
                        }
                    }
                }
                break;
            case "59":
                //產品59時，產權登記日期必需要為099年12月1日(含)以後!
                if (_M.AllFormData.docCode != "5") {
					var homeRegisterDate = $form.find("#homeRegisterDate").val();
					var appDate = $form.find("#appDate").val();
//                    if (homeRegisterDate < "2010-12-01") {
//                        //page5.164=產權登記日期必需大於2010年12月1日
//                        API.showErrorMessage(i18n.cls1151s01["page5.164"]);
//                        return false;
//                    }
                }
                break;
        }
        
        /**2013-10-18 因為a-loan有提供中長期科目互轉的功能，故原本擋掉舊資料不能改科目的限制先行拿掉
         條件為1>短和中長不能互換 2>計息方息不能互換。 **/
		/** 2014-04-28 舊案皆不用擋不能修改
        var subjCode3 = $form.find("#loanNo").val().substring(4, 7);
        if (subjCode3.length == 3) {
            if ("2,1".indexOf(subjCode2.substring(0, 1)) > -1) {
                if ("3,4,5,6,7,8".indexOf(subjCode3.substring(0, 1)) > -1) {
					//page5.215=因現行a_Loan僅提供中長期科目變更交易（L527），<br/>故本案原科目為 【{0}】屬中長期，不可變為【{1}】短期，請另外新增一筆產品資訊。
                    API.showErrorMessage(i18n.cls1151s01["page5.215"].replace("{0}", subjCode3).replace("{1}", subjCode2));
                    return false;
                }
                if (subjCode2 != subjCode3) {
					//page5.216=因現行a_Loan僅提供中長期科目變更交易（L527），<br/>故本案原科目為 【{0}】屬短期不可執行變更，請另外新增一筆產品資訊。
                    API.showErrorMessage(i18n.cls1151s01["page5.216"].replace("{0}", subjCode3));
                    return false;
                }
            }
            else {
                if ("2,1".indexOf(subjCode3.substring(0, 1)) > -1) {
                    if ("3,4,5,6,7,8".indexOf(subjCode2.substring(0, 1)) > -1) {
						//page5.214=因現行a_Loan僅提供中長期科目變更交易（L527），<br/>故本案原科目為 【{0}】屬短期，不可變為【{1}】中長期，請另外新增一筆產品資訊。
                        API.showErrorMessage(i18n.cls1151s01["page5.214"].replace("{0}", subjCode3).replace("{1}", subjCode2));
                        return false;
                    }
                    if (subjCode2 != subjCode3) {
						//page5.216=因現行a_Loan僅提供中長期科目變更交易（L527），<br/>故本案原科目為 【{0}】屬短期不可執行變更，請另外新增一筆產品資訊。
                        API.showErrorMessage(i18n.cls1151s01["page5.216"].replace("{0}", subjCode3));
                        return false;
                    }
                }
            }
        }
        **/
        
        
        
        
        //是否興建住宅
        var residentialVal = $form.find("#residential").val();
        // 授信科目為 371,471,571,671,111,211,(1-6)03時
        if ("371,471,571,671,111,211,103,203,303,403,503,603".indexOf(subjCode2) > -1) {
            if ("371,471,571,671".indexOf(subjCode2) > -1) {
                if (residentialVal != "1" && residentialVal != "2") {
                    //page5.173=授信科目為371（中期放款-建築融資）、471（中期擔保放款-建築融資）、571（長期放款-建築融資）及671（長期擔保放款-建築融資），其是否屬興建住宅需為１（自用）或２（非自用）
                    API.showErrorMessage(i18n.cls1151s01["page5.173"]);
                    return false;
                }
            }
        }
        else {
        
        }
        
        //用途別
        var lnPursVal = $form.find("#lnPurs").val();
        if (residentialVal && residentialVal != "N") {
            if (lnPursVal != "1" && lnPursVal != "4") {
                //page5.174=是否屬興建房屋不為'N'時，用途別必須為 【購置不動產】 或 【週轉金】。
                API.showErrorMessage(i18n.cls1151s01["page5.174"]);
                return false;
            }
        }
        return true;
    },
    
    /**
     *檢查授信期間
     * @param {Object} $form  表單上的內容
     * return 是否通過檢查
     */
    checkLnSelect: function($form){
        var prodKind = $form.find("#prodKind").val();
        if (prodKind == "ZZ") {
            //產品種類zz者不需檢查
            return true;
        }
        var lnYear = $form.find("#lnYear").val();
        var lnMonth = $form.find("#lnMonth").val();
        if (_M.AllFormData.docCode != "5") {
            if (lnYear == "" || lnMonth == "") {
                //page5.158=授信期間不可為空！
                return API.showErrorMessage(i18n.cls1151s01["page5.158"]);
            }
        }
        
        var year = parseInt($form.find("#lnYear").val(), 10);
        var mon = parseInt($form.find("#lnMonth").val(), 10);
        
        if (mon > 12) {
            //page5.147=授信期間月份不可超過12
            API.showErrorMessage(i18n.cls1151s01["page5.147"]);
            return false;
        }
        
        var totalMon = year * 12 + mon;
        
        
        //外勞貸款期限不得超過兩年，且不得逾期在台尚可工作期間！
        if (prodKind == "32") {
            if (totalMon > 24) {
                //page5.187=外勞貸款期限不得超過兩年，且不得逾期在台尚可工作期間！
                API.showErrorMessage(i18n.cls1151s01["page5.187"]);
                return false;
            }
        }
        
        //取得科目第一碼
        var subjCode2Val = $form.find("#subjCode option:selected").attr("subjCode2");
        var subjCode2 = subjCode2Val ? subjCode2Val.substr(0, 1) : "";
        if (_M.AllFormData.docCode != "5") {
            if (totalMon <= 12) {
                if (subjCode2 != "1" && subjCode2 != "2") {
                    //	page5.148=授信期間小於等於1年0月的其科目只能選擇短期。
                    API.showErrorMessage(i18n.cls1151s01["page5.148"]);
                    return false;
                }
            }
            else 
                if (totalMon > 12 && totalMon <= 84) {
                    if (subjCode2 != "3" && subjCode2 != "4") {
                        //page5.149=授信期間大於1年0月小於等於7年0月的其科目只能選擇中期。
                        API.showErrorMessage(i18n.cls1151s01["page5.149"]);
                        return false;
                    }
                }
                else 
                    if (totalMon > 84) {
                        if (subjCode2 != "5" && subjCode2 != "6") {
                            //page5.150=授信期間大於7年0月的其科目只能選擇長期。
                            API.showErrorMessage(i18n.cls1151s01["page5.150"]);
                            return false;
                        }
                    }
                    else {
                    //ilog.debug("error totalMon ====>[" + totalMon + "]");
                    }
        }
        
        
        return true;
        
    },
    cfmL140S02A: function($form){        
    	var my_dfd = $.Deferred();    	
    	if(true){
    		var lnYear = $form.find("#lnYear").val();
            var lnMonth = $form.find("#lnMonth").val();
            
            var year = parseInt($form.find("#lnYear").val(), 10);
            var mon = parseInt($form.find("#lnMonth").val(), 10);
            
            var prodKind = $form.find("#prodKind").val();
            /*
             * J-106-0059 增加檢核：貸款期間是否>20年
             */
            var showCfmMsg = true;
            if(year>20 || (year==20&&mon>0)){
            	//分行授權內 or 總行授權內 需提示，但若經 總處 核准，可超過20年
            	if(_M.AllFormData.l120m01a_authLvl=="1" ||
            			_M.AllFormData.l120m01a_authLvl=="2" ){
            		// $("#openC102M01ABox").is(":visible"))       	
            	}else{
                	showCfmMsg = false;
                }            	            	
            }else{
            	showCfmMsg = false;
            }
            
            if(prodKind=="67" || prodKind=="70"){
            	//以房養老 貸款期間最長以30年為限
            	showCfmMsg = false;
            }
            
            if(showCfmMsg){
            	var msg = "除(1)屬銀行法第12條之1規範之自用住宅貸款，貸款期間最長為三十年；"+"<br/>"+
            			  "　(2)屬｢永慶信義200億專案」(首購(符合金管會訂定之自用住宅貸款風險權數35%)借款年限最長得為40年，非首購案件最長得為30年)外(兆銀銷字第1070000069號)；"+"<br/>"+
            			  "其他房貸貸款期間最長為二十年。" + "<br/>" +
						  "　(3)屬｢青年安心成家購屋優惠貸款」自民國112年8月1日起，貸款期間得為40年";
        		API.confirmMessage(msg, function(b){
                    if (b) {
                    	my_dfd.resolve();	
                    } else {
                    	my_dfd.reject();
                    }
                });
            }else{
            	my_dfd.resolve();	
            }            
    	};    		
    	return my_dfd.promise();
    },
    delL140S02A: function(){
        //confirmDelete=是否確定刪除?
        API.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                _M.doAjax({
                    action: "delL140S02A",
                    data: {
                        oid: L140S02Action._L140S02AData.L140S02AOid
                    },
                    success: function(obj){
                        $.thickbox.close();
                        L140S02Action._reloadGrid();
                        
                        _M.refresh_M_key(obj);
                        
                        //因為會更新性質所以重新整理額度明細表主檔grid
                        _M._triggerMainGrid();
                        //confirmDeleteSuccess=刪除成功
                        API.showMessage(i18n.def["confirmDeleteSuccess"]);
                    }
                });
                
            }
        });
        
    },
    
    /**
     * 重新整理產品grid
     */
    _reloadGrid: function(data){
		if(PanelAction05.L140S02AGrid){
			PanelAction05.L140S02AGrid.reload(data);	
		}        
    },
    //是否初始化
    isloginGradeInit: false,
    loginGradeGridObj: null,
    loginGradeGrid: function(){
        this.loginGradeGridObj = $("#loginGradeGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: false,
            hideMultiselect: true,
            rowNum: 10,
            action: "queryL140S01AForGrade",
            postData: {
                CaseMainId: _M.CaseMainId,
                tabFormMainId: _M.tabMainId,
                prodKind: $("#prodKind").val(),
                subjCode: $("#subjCode").val()
            },
            colModel: [{
                colHeader: i18n.def["compID"],//統一編號,
                name: 'CUSTNAME',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140S02A.grade2"],//評等,
                name: 'GRADE3',
                align: "center",
                width: 60,
                sortable: true
            }, {
                colHeader: ' ',
                name: 'QUOTE',
                align: "left",
                width: 60,
                formatter: function(value){
                    return value == "Y" ? "" : "未引用";
                }
            }, {
                colHeader: i18n.cls1151s01["page5.varVer"],
                name: 'varVer',
                align: "center",
                width: 40,
                sortable: false
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'CUSTID',
                hidden: true
            }, {
                name: 'DUPNO',
                hidden: true
            }, {
                name: 'showGrade1',
                hidden: true
            }]
        });
    },
    /**
     * 清除最終評等
     */
    cleanGrade: function(){
        var $form = $("#" + L140S02Action.formId);
        $form.find("#custId").html("");
        $form.find("#dupNo").html("");
        $form.find("#grade1").html("");
        $form.find("#showGrade1").html("");
    },
    /**
     * 登錄最終評等
     */
    loginGrade: function(){
        var postParam = {
            tabFormMainId: _M.tabMainId,
            prodKind: $("#prodKind").val(),
            subjCode: $("#subjCode").val()
        };
        
        if (!this.isloginGradeInit) {
            this.loginGradeGrid();
            this.isloginGradeInit = true;
        }
        else {
            L140S02Action.loginGradeGridObj.reload(postParam);
        }
        _M.doAjax({
            action: "queryloginGrade",
            data: postParam,
            success: function(obj){
                var $form = $("#" + L140S02Action.formId);
                if (obj.total == "1") {
                    $form.find("#custId").html(obj.CUSTID);
                    $form.find("#dupNo").html(obj.DUPNO);
                    $form.find("#grade1").html(obj.GRADE3);
                    $form.find("#showGrade1").html(obj.showGrade1);
                }
                else {
                	if(obj.errMsg){
                		$form.find("#custId").html(obj.CUSTID);
                        $form.find("#dupNo").html(obj.DUPNO);
                        $form.find("#grade1").html(obj.GRADE3);
                        $form.find("#showGrade1").html(obj.showGrade1);
                        
                        API.showErrorMessage(obj.errMsg);
                	}else{
                		var showTitle = i18n.cls1151s01["L140S02A.grade1"];
                        if (obj.modelKind == "1") {
                            showTitle = i18n.cls1151s01["L140S02A.modelKind.1"] + " - " + showTitle;
                        }else if (obj.modelKind == "2") {
                            showTitle = i18n.cls1151s01["L140S02A.modelKind.2"] + " - " + showTitle;
                        }else if (obj.modelKind == "3") {
                            showTitle = i18n.cls1151s01["L140S02A.modelKind.3"] + " - " + showTitle;
                        }
                        
                        $("#loginGradeBox").thickbox({
                            //L140S02A.grade1=最終評等
                            title: showTitle,
                            width: 500,
                            height: 400,
                            modal: true,
                            align: "center",
                            valign: "bottom",
                            readOnly: _openerLockDoc == "1",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
                                    var $grid = L140S02Action.loginGradeGridObj;
                                    //單筆
                                    var rowData = $grid.getSingleData();
                                    
                                    $form.find("#custId").html(rowData.CUSTID);
                                    $form.find("#dupNo").html(rowData.DUPNO);
                                    $form.find("#grade1").html(rowData.GRADE3);
                                    $form.find("#showGrade1").html(rowData.showGrade1);
                                    $.thickbox.close();
                                },
                                "cancel": function(){
                                    $.thickbox.close();
                                }
                            }
                        });	
                	}                    
                }
            }
        });
    },
    /**
     * 取得最終評等對應
     * @param {Object} value
     */
    getFinalGrade: function(value){
        var result = "";
        switch ($.trim(value) + "") {
            case "1":
            case "2":
            case "3":
                result = "特A";
                break;
            case "4":
            case "5":
                result = "A";
                break;
            case "6":
            case "7":
                result = "B";
                break;
            case "8":
                result = "C";
                break;
            case "9":
            case "10":
                result = "D";
                break;
        }
        return result;
    }
};
/**
 * 長擔額度序號相關動作
 */
var L140S02BAction = {
    isInit: false,
    init: function(){
        if (!this.isInit) {
            this.initGrid();
            this.isInit = true;
        }
        this._reloadGrid({
            tabFormMainId: _M.tabMainId,
            L140S02ASeq: L140S02Action.L140S02ASeq
        });
    },
    formId: "#L140S02BForm",
    grid: null,
    initGrid: function(){
        this.grid = $("#L140S02BGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            rowNum: 10,
            postData: {
                formAction: "queryL140S02B",
                tabFormMainId: _M.tabMainId,
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140S02B.cntrNo"],//長擔額度序號,
                name: 'cntrNo',
                align: "left",
                width: 100,
                sortable: true
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    },
    openL140S02BBox: function(){
        this.init();
        $("#L140S02BBox").thickbox({
            //L140S02B.cntrNo=長擔額度序號
            title: i18n.cls1151s01["L140S02B.cntrNo"],
            width: 600,
            height: 450,
            modal: true,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "del": function(){
                    var $grid = L140S02BAction.grid;
                    //多筆
                    var rowData = $grid.getSelectData("oid");
                    if (rowData) {
                        //confirmDelete=是否確定刪除?
                        API.confirmMessage(i18n.def["confirmDelete"], function(b){
                            if (b) {
                                _M.doAjax({
                                    action: "delL140S02B",
                                    data: {
                                        L140S02ASeq: L140S02Action.L140S02ASeq,
                                        oids: rowData
                                    },
                                    success: function(obj){
                                        L140S02BAction._reloadGrid();
                                        $("#showL140S02BStr").html(obj.showL140S02BStr);
                                    }
                                });
                            }
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 新增長擔額度序號
     */
    addL140S02B: function(){
        var $form = $(this.formId);
        if ($form.valid()) {
            _M.doAjax({
                action: "addL140S02B",
                data: {
                    L140S02ASeq: L140S02Action.L140S02ASeq,
                    cntrNo: $form.find("#L140S02BCntrNo").val()
                },
                success: function(obj){
                    if (obj.notfund) {
                        return API.showMessage(obj.notfund);
                    }
                    else {
                        $("#showL140S02BStr").html(obj.showL140S02BStr);
                        L140S02BAction._reloadGrid();
                        $form.reset();
                    }
                    
                }
            });
        }
        
        
    },
    /**
     * 重新整理grid
     */
    _reloadGrid: function(data){
        this.grid.reload(data);
    }
}
/**
 * 利率方式
 */
var L140S02CAction = {
    isInit: false,
    formData: null,
    //產品種類
    prodKind: "",
    /**
     * 判斷是否為產品種類02 或04
     */
    isProdKind02And04: function(){
        var result = false;
        if (this.prodKind == "02" || this.prodKind == "04") {
            result = true;
        }
        return result;
    },
    formId: "L140S02CForm",
    /**
     * rateType :利率基礎
     * rateUserType: 自訂利率
     * rate:利率對應
     * */
    AllItem: null,
    init: function(data){
        if (!this.isInit) {
            TEST.start("createTr");
            this.createTr();
            TEST.end("createTr");
            TEST.start("initItem");
            L140S02Action.initItem(this.formId);
            TEST.end("initItem");
            TEST.start("initEvent");
            this.initEvent();
            TEST.end("initEvent");
            this.isInit = true;
        }
        TEST.start("initAllItem");
        this.initAllItem();
        TEST.end("initAllItem");
        
        var $form = $("#" + this.formId);
        if (data.isInputDesc == "Y") {
            $form.find("#desc").show();
        }
        else {
            $form.find("#desc").hide();
        }
        
        $form.reset();
        $form.find("[id^=bgnNum_],[id^=endNum_]").each(function(){
            var $this = $(this);
            $this.attr("readOnly", "readOnly").removeClass("required");
        });
        $form.find("[class^=isUseBox_]").hide();
        if (data) {
            L140S02CAction.formData = data;
            TEST.start("injectData");
            $form.injectData(data);
            TEST.end("injectData");
            //判斷有勾選的才進行觸發
            var target = "";
            for (var keyName in data) {
                if (keyName.match("isUseBox_") && data[keyName] == "Y") {
                    if (target) {
                        target += ",";
                    }
                    target += "." + keyName;
                }
            }
            if (target) {
                TEST.start("selectALLtrigger");
                $form.find(target).find("select.haveEven").trigger("change", "init");
                TEST.end("selectALLtrigger");
            }
            
        }
        var $payNumOldTr = $form.find("#payNumOldTr");
        if (data.payNum) {
            //            if ($("#payNum").val != "0") {
            if (data.payNum != "0") {
                $payNumOldTr.show();
                
                //開放可自行修改前期利率欄位值。
                $("#payNum").dblclick(function(){
                    if ($("#payNum").val() != "0") {
                        $("#preDscr").removeAttr("readonly").addClass("required");
                    }
                });
            }
            else {
                $payNumOldTr.hide();
            }
        }
        else {
            $payNumOldTr.hide();
        }
        //取得產品對應計息方式
        var rIntWay = $("#" + L140S02Action.formId).find("#subjCode :selected").attr("rIntWay");
        if (rIntWay) {
            if (rIntWay.split("|").length > 1) {
                $form.find("#intWay").removeAttr("disabled");
            }
            else {
                $form.find("#intWay").val(rIntWay).attr("disabled", "disabled");
            }
            $form.find("#intWay").trigger("change", "init");
        }
        
        //計息方式─透支
        if(true){
        	var subjCode2 =$("#" + L140S02Action.formId).find("#subjCode :selected").attr("subjCode2");
            //102,202 透支
            //104,204,404 存摺存款透支(ex:金融卡)
            if(        (subjCode2=="102"||subjCode2=="202")
            		|| (subjCode2=="104"||subjCode2=="204")
              ){
            	$form.find("#intWay option[value=1]").removeAttr("disabled"); // 一開始只有{1,2}, 後來才變{1,2,P,Q}, 要能輸入舊資料
            	$form.find("#intWay option[value=2]").attr("disabled", "disabled");
            	$form.find("#intWay option[value=P]").removeAttr("disabled");
            	$form.find("#intWay option[value=Q]").removeAttr("disabled");
            }else if(subjCode2=="404"){
            	$form.find("#intWay option[value=1]").attr("disabled", "disabled");
            	$form.find("#intWay option[value=2]").attr("disabled", "disabled");
            	$form.find("#intWay option[value=P]").attr("disabled", "disabled");
            	$form.find("#intWay option[value=Q]").removeAttr("disabled");
            }else{
            	$form.find("#intWay option[value=1]").removeAttr("disabled"); // 一開始只有{1,2}, 後來才變{1,2,P,Q}, 要能輸入舊資料
            	$form.find("#intWay option[value=2]").removeAttr("disabled");
            	$form.find("#intWay option[value=P]").attr("disabled", "disabled");
            	$form.find("#intWay option[value=Q]").attr("disabled", "disabled");
            }	
        }        
        
        //當7.登錄利率的時候如果該案有已還期數時，其第一段的起期鎖住不要再讓user修改。
        if ($.trim(data.payNum) != '' && data.payNum != 0) {
            $form.find("#isUseBox_1,#bgnNum_1").attr("disabled", "disabled");
        }
        else {
            if (!_M.isReadOnly) {
                $form.find("#isUseBox_1,#bgnNum_1").removeAttr("disabled");
            }
        }
    },
    /**
     * 設定描述
     * @param {Object} seq 欄位序列
     */
    setDesc: function(seq){
        //        var $form = $("#" + this.formId);
        //        $form.find("baseDesc_" + seq).val();
    },
    
    
    
    /**
     * 產生重覆的tr內容
     */
    createTr: function(){
        var tempTr = $("#L140S02C_temp_Div").html();
        var prefix = "#isTempTr_";
        var $form = $("#" + this.formId);
        for (var i = 0; i <= 10; i++) {
            $form.find(prefix + i).html(tempTr.replace(/{seq}/g, i));
        }
        
    },
    /**
     * 取得所有項目
     */
    initAllItem: function(){
        
		var isDisableM2M3N2;
        var $form = $("#" + this.formId);
        _M.doAjax({
            action: "getAllItem",
            async: false,
			data:
				$.extend({}, {currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']}
			),
            success: function(obj){

				$form.find(".rateType").empty();
                $form.find(".rateType").setItems({
                    item: obj.rateType,
                    format: "{value} - {key}",
                    sort: "asc",
                    value: ""
                });
				
				if (!this.AllItem) {
					L140S02CAction.AllItem = obj;
					$form.find(".rateUserType").setItems({
						item: obj.rateUserType,
						format: "{value} - {key}",
						sort: "asc",
						value: ""
					});
				}
				
				isDisableM2M3N2 = obj.isDisableM2M3N2;
            }
        });
		
		if(isDisableM2M3N2 == 'Y'){
			$form.find(".rateType option[value=M2]").attr("disabled", "disabled");
			$form.find(".rateType option[value=M3]").attr("disabled", "disabled");
			$form.find(".rateType option[value=N2]").attr("disabled", "disabled");
		}
    },
    /**
     * 新增事件
     */
    initEvent: function(){
        var $form = $("#" + this.formId);
        /**
         * 是否使用這段利率
         */
        $form.find("[name^=isUseBox_]").click(function(){
            var prefix = ""
            var id = $(this).attr("id");
            var id_Seq = parseInt(id.replace("isUseBox_", ""), 10);
            var $obj = $form.find("." + id);
            var $bgnNum = $form.find("#bgnNum_" + id_Seq);
            var $endNum = $form.find("#endNum_" + id_Seq);
            if (this.checked) {
                $obj.show();
                if (id_Seq != "1") {
                    var beforeVal = $form.find("#endNum_" + (id_Seq - 1)).val();
                    if (beforeVal != "") {
                        $bgnNum.val($.trim(parseInt(beforeVal, 10) + 1));
                    }
                }
                var intWaycheck = !($form.find("#intWay").val() == "1" || $form.find("#intWay").val() == "P" || $form.find("#intWay").val() == "Q");
                if (intWaycheck) {
                    $bgnNum.removeAttr("readonly");
                    $endNum.removeAttr("readonly");
                }
                if (!L140S02CAction.isProdKind02And04() && intWaycheck) {
                    $bgnNum.addClass("required");
                    $endNum.addClass("required");
                }
            }
            else {
                var preDscr = $.trim($form.find("#payNum").html());
                //當無前期記錄才清空首段利率
                if (id_Seq == "1" && preDscr == "") {
                    $form.find("#submitRate").html("");
                }
                $bgnNum.val("").removeClass("required").attr("readonly", "readonly");
                $endNum.val("").removeClass("required").attr("readonly", "readonly");
                _M.cleanTrHideInput($obj);
            }
        });
        //其他說明欄位
        var $desc = $form.find("#desc");
        /**
         * 判斷是否勾選其他欄位
         */
        $form.find("#isInputDesc").click(function(){
            if (this.checked) {
                $desc.show();
            }
            else {
                $desc.hide();
                $desc.val("");
            }
        });
        /**
         * 利率基礎欄位切換
         */
        $form.find(".rateType").change(function(k, v){
            TEST.start("rateType-change");
            var id = $(this).attr("id");
            var id_seq = id.replace("rateType_", "");
            var value = $(this).val();
            //自訂利率相關
            var $othDivId = $form.find("#rateUserDiv_" + id_seq);
            //指標利率相關
            var $rateTypeDiv = $form.find("#rateTypeDiv_" + id_seq);
            //基礎利率相關
            var $baseRate = $rateTypeDiv.find("#baseRate_" + id_seq);
            //加減碼
            var $pmFlagDiv = $rateTypeDiv.find("#pmFlagDiv_" + id_seq);
            //利率方式 
            var $rateFlag = $form.find("#rateFlag_" + id_seq);
            //利率方式變動方式
            var $rateFlagDiv = $form.find("#rateFlagDiv_" + id_seq);
            //利率變動方式
            var $rateChgWay = $form.find("#rateChgWay_" + id_seq);
            //變動周期
            var $rateChgWay2 = $form.find("#rateChgWay2_" + id_seq);
			//計息方式
			var intWay = $form.find("#intWay").val();
			
            if (value) {
                if (value == "01") {
                    //利率代碼01時利率方式不能選2。
                    $othDivId.show();
                    $rateFlag.find("option[value=2]").attr("disabled", "disabled");
                    $rateFlag.removeAttr("disabled", "disabled");
                    _M.cleanTrHideInput($rateTypeDiv);
                    _M.cleanTrHideInput($pmFlagDiv);
                    _M.cleanTrHideInput($rateFlagDiv);
                }
                else {
                
                    _M.cleanTrHideInput($othDivId);
                    $rateFlag.setItems({
                        item: API.loadCombos("lms1405s0204_rateKind")["lms1405s0204_rateKind"],
                        format: "{value} - {key}",
                        sort: "asc",
                        value: L140S02CAction.formData["rateFlag_" + id_seq] || ""
                    });
                    $rateTypeDiv.show();
                    //當欄位非初始動作才需抓DB值
                    if (v != "init") {
                        $baseRate.html(L140S02CAction.AllItem.rate[value]);
                    }
                    //當非文件鎖定時才需要移除欄位的屬性
                    if (!_M.isReadOnly) {
                        $rateFlag.removeAttr("disabled", "disabled");
                        $rateChgWay.removeAttr("disabled", "disabled");
                        $rateChgWay2.removeAttr("disabled", "disabled");
                    }
                    switch (value) {
                        case "ML":
                        case "MM":
                        case "MN":
                        case "M2":
                        case "M3":
                        case "I7":
                        case "P6":
                        //2013/08/02,Rex,明澤說p7改成不要鎖定在機動
                        //case "P7":
                        case "20":
                        case "24":
                        case "31":
                        case "33":
                        case "35":
                        case "77":
                        case "79":
                        case "N2":
                            $rateFlag.val("2").trigger("change");
                            $rateFlag.attr("disabled", "disabled");
                            break;
                        case "23":
                        case "30":
                        case "32":
                        case "34":
                        case "76":
                        case "78":
                            $rateFlag.val("1").trigger("change");
                            $rateFlag.attr("disabled", "disabled");
                            break;
                        case "6R":
                            var $formA = $("#" + L140S02Action.formId);
                            var subcode = $formA.find("#subjCode").val();
                            //[J-098-0065] " 6R"時, 利率方式須預設為”3”(定期浮動)、利率變動週期須預設為”1”、利率變動方式須預設為”M”(月)
                            //[J-098-0156]只限13506200(授信科目:473)、14501500(授信科目:673)、13506300(授信科目:474)、14502000(授信科目:674)
                            if (subcode == "13506200" || subcode == "14501500" || subcode == "13506300" || subcode == "14502000") {
                                $rateFlag.val("3").trigger("change");
                                $rateFlag.attr("disabled", "disabled");
                                $rateChgWay.val("1").trigger("change"); //1 - 每『月/三個月/半年/九個月/年』調整乙次
                                $rateChgWay.attr("disabled", "disabled");
                                $rateChgWay2.val("1"); //1 - 月, 3 - 三個月
                                $rateChgWay2.attr("disabled", "disabled");
                            }
                            /*
                                                                           Ｑ：原本的簽案內容是 2.機動利率。但若依上述的邏輯，會強迫轉 3.定期浮動
                                                                                     造成明明是「不變」，印出的內容卻被改到了
                                  
                            Workaround: 
                                                                                    若要呈現原始資料(舊案)，於 browser 的 console 取消 disable，再更正 	
                                $("#rateFlag_1").attr("disabled", "");
                                $("#rateChgWay_1").attr("disabled", "");
                                $("#rateChgWay2_1").attr("disabled", "");
                            */
                            break;
						case "MR":
							
							L140S02CAction.changeRateFlagByMrRateCode(intWay, $rateFlag);
							break;
                        default:
                            break;
                    }
                    $pmFlagDiv.find("option[value=M]").removeAttr("disabled");
                    //加減年利率呈現條件
                    switch (value) {
                        //case "M2":
                        case "M3":
                        case "M8":
                        case "M9":
                        //J-111-0204內政部貸款的政府補貼利率異動, MI=１１１０３２３新國宅利率(原MK) 
                        case "MI":
                        case "MK":
                        case "ML":
                        case "MM":
                        case "MN":
                        case "MO":
                            _M.cleanTrHideInput($pmFlagDiv);
                            break;
                        case "6C":
                            $pmFlagDiv.show();
                            $pmFlagDiv.find("option[value=M]").attr("disabled", "disabled");
                            break;
                        default:
                            $pmFlagDiv.show();
                            break;
                    }
                }
            }
            else {
                _M.cleanTrHideInput($othDivId);
                _M.cleanTrHideInput($rateTypeDiv);
                _M.cleanTrHideInput($pmFlagDiv);
                _M.cleanTrHideInput($rateFlagDiv);
            }
            TEST.end("rateType-change");
        });
        /**
         * 加減碼欄位切換
         */
        $form.find(".pmFlag").change(function(){
            TEST.start("pmFlag-change");
            var id = $(this).attr("id");
            var id_seq = id.replace("pmFlag_", "");
            var $pmRateSpan = $("#pmRateSpan_" + id_seq);
            if ($(this).val() == "") {
                $("#pmRate_" + id_seq).val("");
                $pmRateSpan.hide();
            }
            else {
                $pmRateSpan.show();
            }
            L140S02CAction.countNowRate(id_seq, $form);
            TEST.end("pmFlag-change");
        });
        
        
        /**
         * 利率方式欄位切換
         */
        $form.find(".rateFlag").change(function(k, v){
            TEST.start("rateFlag-change");
            var id = $(this).attr("id");
            var id_seq = id.replace("rateFlag_", "");
            var value = $(this).val();
            var $rateFlagDiv = $form.find("#rateFlagDiv_" + id_seq);
            if (value == "3") {
                $rateFlagDiv.show();
                if (v) {
                    $form.find("#rateChgWay_" + id_seq).val(L140S02CAction.formData["rateChgWay_" + id_seq] || "");
                }
            }
            else {
                _M.cleanTrHideInput($rateFlagDiv);
            }
            TEST.end("rateFlag-change");
        });
        
        
        /**
         * 利率變動方式切換
         */
        $form.find(".rateChgWay").change(function(k, v){
            TEST.start("rateChgWay-change");
            var id = $(this).attr("id");
            var id_seq = id.replace("rateChgWay_", "");
            var value = $(this).val();
            var $rateChgWay2Div = $form.find("#rateChgWay2Div_" + id_seq);
            if (value == "1") {
                $rateChgWay2Div.show();
                if (v) {
                    $form.find("#rateChgWay2_" + id_seq).val(L140S02CAction.formData["rateChgWay2_" + id_seq] || "");
                }
            }
            else {
                _M.cleanTrHideInput($rateChgWay2Div);
            }
            TEST.end("rateChgWay-change");
        });
        
        /**
         *重新引進匯率 按鈕
         */
        $form.find("[id^=reloadBaseRate_]").click(function(){
            var id = $(this).attr("id");
            var id_seq = id.replace("reloadBaseRate_", "");
            var value = $("#rateType_" + id_seq).val();
            $("#baseRate_" + id_seq).html(L140S02CAction.AllItem.rate[value]);
            L140S02CAction.countNowRate(id_seq, $form);
        });
        /**
         *調整匯率 按鈕
         */
        $form.find("[id^=modfixBaseRate_]").click(function(){
            var id = $(this).attr("id");
            var id_seq = id.replace("modfixBaseRate_", "");
            L140S02CAction.enterRateBox(id_seq);
        });
        /**
         *  當加減年率欄位失去焦點後 要算出其目前利率
         */
        $form.find("[name^=pmRate_]").blur(function(){
            var id = $(this).attr("id");
            var id_seq = id.replace("pmRate_", "");
            L140S02CAction.countNowRate(id_seq, $form);
        });
        /**
         *  當扣稅負擔值欄位失去焦點後 要算出其目前利率
         */
        $form.find("#taxRate").blur(function(){
            L140S02CAction.countNowRate(1, $form);
        });
        
        //扣稅負擔值
        var $taxRateSpan = $form.find("#taxRateSpan");
        //收息方式
        var $rIntWay = $form.find("#rIntWay");
        var $bgnNum1 = $form.find("#bgnNum_1");
        var $endNum1 = $form.find("#endNum_1");
        $rIntWay.change(function(){
            var value = $(this).val();
            var intWaycheck = ($form.find("#intWay").val() == "1" || $form.find("#intWay").val() == "P" || $form.find("#intWay").val() == "Q");
            if (intWaycheck) {
                $bgnNum1.val("").removeClass("required").attr("readonly", "readonly");
                $endNum1.val("").removeClass("required").attr("readonly", "readonly");
            }
        });
        
        //所有非第一段的box內容
        var $notIsOne = $form.find("[class^=isUseBox_]:not(.isUseBox_1)");
        var $notIsOneNum = $form.find(".notIsOneNum");
        //計息方式
        $form.find("#intWay").change(function(k, v){
            TEST.start("intWayChange");
            var value = $(this).val();
			
			$form.find("[class^=isUseBox_]").each(function(){
                var $this = $(this);
                var className = $this.attr("class");
                var className_seq = className.replace("isUseBox_", "");
				var $rateFlag = $form.find("#rateFlag_" + className_seq);
				var rateType = $form.find("#rateType_" + className_seq).val();
				
				if(rateType == 'MR'){
					L140S02CAction.changeRateFlagByMrRateCode(value, $rateFlag);
				}
			});
			
            // 計息方式如果選2-期付金時，收息方式請直接帶出6-期付金。也就是說計息方式為2時收息方式一定為6。
            if (value == "2") {
                //J-102-0226>e-Loan個金業務開放個人戶期付金可由客戶自行負擔稅負修改。
                //                $taxRateSpan.hide().find("input").val("");
                var taxRateValue = $form.find("#taxRate").val();
                if (taxRateValue == '') {
                    //帶入預設值
                    $form.find("#taxRate").val("1");
                }
                //扣稅負擔值
                $taxRateSpan.show();
                
                $rIntWay.val("6");
                $rIntWay.attr("disabled", "disabled");
                $form.find("[name^=isUseBox_]").show();
                //扣稅負擔值
                //                $taxRateSpan.hide().find("input").val("");
                // 計息方式如果選1-按月計息時，僅能選擇第一段利率，且收息方式選1-按月收息時，關閉期間起迄期輸入。
                $bgnNum1.removeAttr("readonly");
                $endNum1.removeAttr("readonly");
            }
            else {
                if (value == "1" || value == "P" || value == "Q") {
                    if (!v) {
                        $rIntWay.val("");
                    }
                    var taxRateValue = $form.find("#taxRate").val();
                    if (taxRateValue == '') {
                        //帶入預設值
                        $form.find("#taxRate").val("1");
                    }
                    //扣稅負擔值
                    $taxRateSpan.show();
                    $rIntWay.find("option[value=6]").attr("disabled", "disabled");
                    TEST.start("cleanTrHideInput");
                    $notIsOneNum.val("").removeClass("required").attr("readonly", "readonly");
                    $notIsOne.hide();
                    //_M.cleanTrHideInput($notIsOne);
                    $form.find("input.notIsOne:checkbox").removeAttr("checked").hide();
                    TEST.end("cleanTrHideInput");
                    $rIntWay.removeAttr("disabled");
                    $bgnNum1.val("").removeClass("required").attr("readonly", "readonly");
                    $endNum1.val("").removeClass("required").attr("readonly", "readonly");
                }
                else {
                //扣稅負擔值
                //                    $taxRateSpan.hide().find("input").val("");
                }
            }
            TEST.end("intWayChange");
        });
    },
    /**
     * 計算目前利率
     * @param {Object} seq 欄位序列號
     */
    countNowRate: function(seq, $form){
        if (!$form) {
            $form = $("#" + this.formId);
        }
        var rateType = $form.find("#rateType_" + seq).val();
        //指標利率
        var baseRate = parseFloat($form.find("#baseRate_" + seq).html(), 10);
        //當為自訂利率抓的欄位不一樣
        if (rateType == "01") {
            baseRate = parseFloat($form.find("#rateUser_" + seq).val(), 10);
        }
        //加減種類
        var pmFlag = $form.find("#pmFlag_" + seq).val();
        //加減碼
        var pmRateValue = $form.find("#pmRate_" + seq).val();
        var pmRate = pmRateValue ? parseFloat(pmRateValue, 10) : 0;
        if (pmFlag == "P") {
            baseRate += pmRate;
        }
        else 
            if (pmFlag == "M") {
                baseRate -= pmRate;
            }
        if (isNaN(baseRate)) {
            baseRate = "";
        }
        else {
            baseRate = Math.round(parseFloat(baseRate, 10).toFixed(4) * 10000) / 10000;
        }
        var taxRate = $form.find("#taxRate").val();
        
        //當有扣稅負擔值才要算值
        //        if ($form.find("#intWay").val() == "1") {
        if (taxRate != '' && taxRate != '0' && baseRate != '') {
            baseRate = Math.round(parseFloat(baseRate / taxRate, 10).toFixed(4) * 10000) / 10000;
        }
        //        }
        $form.find("#nowRate_" + seq).html(baseRate);
        var payNum = $.trim($form.find("#payNum").html());
        //帶入首段利率
        if (seq == "1" && baseRate != "" && (payNum == '' || payNum == "0")) {
            $form.find("#submitRate").html(baseRate);
        }
    },
    /**
     *輸入修改利率thickBox
     * @param {Object} seq 欄位序列號
     */
    enterRateBox: function(seq){
        var $form = $("#" + this.formId);
        var $baseRate = $form.find("#baseRate_" + seq);
        var srcValue = $.trim($baseRate.html());
        if (!srcValue) {
            //grid_selector=請選擇資料
            return API.showMessage(i18n.def.grid_selector);
        }
        var $inputform = $("#cls_enterRateForm");
        $inputform.reset();
        $inputform.find("#cls_enterRateInupt").val(srcValue);
        $("#cls_enterRateBox").thickbox({
            title: i18n.def.confirmTitle,
            width: 200,
            height: 200,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$inputform.valid()) {
                        return false;
                    }
                    var targetValue = $.trim($inputform.find("#cls_enterRateInupt").val());
                    $baseRate.html(targetValue);
                    L140S02CAction.countNowRate(seq, $form);
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 開啟利率視窗
     */
    openL140S02C: function(){
        var $form = $("#" + L140S02Action.formId);
        //請先登錄產品
        var prodKind = $form.find("#prodKind").val();
        if (prodKind == "") {
            //L140M01A.msg010=尚未登錄產品種類
            API.showErrorMessage(i18n.cls1151s01["L140M01A.msg010"]);
            return false;
        }
        
        var subjCode = $form.find("#subjCode").val();
        if (subjCode == "") {
            //L140M01A.msg047=尚未登錄授信科目
            API.showErrorMessage(i18n.cls1151s01["L140M01A.msg047"]);
            return false;
        }
        
        L140S02CAction.prodKind = prodKind;
        var $form = $("#" + L140S02CAction.formId);
        _M.doAjax({
            action: "queryL140S02C",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            success: function(obj){
                TEST.start("L140S02CAction.init");
                L140S02CAction.init(obj);
                TEST.end("L140S02CAction.init");
                TEST.log();
                
            }
        });
        
        $("#L140S02CBox").thickbox({
            //L140S02A.rateDesc=利率
            title: i18n.cls1151s01["L140S02A.rateDesc"],
            width: 800,
            height: 500,
            modal: true,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                
                    if (!$form.valid()) {
                        //page5.151=尚有欄位未填妥，請完成後再執行此動作。
                        API.showErrorMessage(i18n.cls1151s01["page5.151"]);
                        return false;
                    }
                    
                    var haveError = false;
                    var saveData = {};
                    var errorMsg1 = "";
                    var errorMsg2 = "";
                    var count = 0;
                    var endNum;
                    var lnyear = $("#lnYear").val() * 12;
                    var lnmonth = $("#lnMonth").val() * 1;
                    var sumNum = lnyear + lnmonth;
                    //計息方式
                    var intWay = $form.find("#intWay").val();
                    $form.find("[class^=isUseBox_]").each(function(){
                        var $this = $(this);
                        var className = $this.attr("class");
                        var className_seq = className.replace("isUseBox_", "");
                        var allData = $this.serializeData();
                        var isUse = $form.find("#isUseBox_" + className_seq).is(":checked");
                        
                        allData["isUseBox"] = $form.find("#isUseBox_" + className_seq + ":checked").val() || "N";
                        var bgnNum_Val = parseInt($form.find("#bgnNum_" + className_seq).val(), 10);
                        var endNum_Val = parseInt($form.find("#endNum_" + className_seq).val(), 10);
                        
                        if (isUse) {
                            endNum = endNum_Val;
                            count++
                            if (className_seq != count) {
                                //page5.163=利率不能跳段登打！
                                errorMsg1 = i18n.cls1151s01["page5.163"];
                                haveError = true;
                            }
                            if (intWay == "2" && ((bgnNum_Val == 0 || endNum_Val == 0) || ($.isNaN(bgnNum_Val) || $.isNaN(endNum_Val)))) {
                            
                                //page5.209=計息方式為[期付金]且第{0}段有勾時，期數不可空白！
                                errorMsg1 += "<br>" + i18n.cls1151s01["page5.209"].replace("{0}", className_seq);
                                haveError = true;
                            }
                        }
                        //若利率登錄時如果第一段為空值，要檢查至少要勾選其他不能是空白
                        
                        if (className_seq == 1 && !isUse) {
                            if (!$form.find("#isInputDesc").is(":checked")) {
                                //page5.200=請至少勾選第一段利率或勾選其他輸入內容！
                                errorMsg1 += "<br>" + i18n.cls1151s01["page5.200"];
                                haveError = true;
                            }
                        }
                        
                        //檢查起期不可為0
                        if (className_seq == 1 && bgnNum_Val == 0 && isUse) {
                            //page5.207=第一段起期不可為0
                            errorMsg1 += "<br>" + i18n.cls1151s01["page5.207"];
                            haveError = true;
                        }
                        //檢查起訖期間不可顛倒
                        if (endNum_Val < bgnNum_Val) {
                            //page5.087=第{0}段利率迄期不可小於起期！
                            errorMsg1 += "<br>" + i18n.cls1151s01["page5.087"].replace("{0}", className_seq);
                            haveError = true;
                        }
                        
                        if (className_seq != "1" && isUse) {
                            //前一期迄期
                            var exEndNum_Val = parseInt($form.find("#endNum_" + (className_seq - 1)).val(), 10);
                            //page5.152=第{0}段利率起期需接續前期迄期！
                            if ((exEndNum_Val + 1) != bgnNum_Val) {
                                errorMsg2 += "<br>" + i18n.cls1151s01["page5.152"].replace("{0}", className_seq);
                                haveError = true;
                            }
                        }
                        allData["bgnNum"] = bgnNum_Val;
                        allData["endNum"] = endNum_Val;
                        saveData[className_seq] = allData;
                        L140S02CAction.countNowRate(className_seq, $form);
                    });
                    //檢查最後一期是否大於總期數
                    if (endNum < sumNum && $form.find("#isUseBox_1").is(":checked")) {
                        errorMsg1 = i18n.cls1151s01["page5.202"] + sumNum;
                        haveError = true;
                    }
                    if (haveError) {
                        return API.showErrorMessage(errorMsg1 + "<br/>" + errorMsg2);
                    }
                    var taxRate = $form.find("#taxRate").val();
                    
                    //                    if (intWay == "1") {
                    if (parseFloat(taxRate, 10) <= 0 || parseFloat(taxRate, 10) > 1) {
                        //page5.198=扣稅負擔值必須小於1，但不可為0！！
                        return API.showErrorMessage(i18n.cls1151s01["page5.198"]);
                    }
                    //                    }
                    if (!$form.find("#isUseBox_1").is(":checked") && $form.find("#isInputDesc").is(":checked")) {
                        var $formSR = $("#L140S02CsubmitRate");
                        $("#submitRateBox").thickbox({
                            //L140S02A.rateDesc=利率
                            title: i18n.cls1151s01["L140S02C.submitRate"],
                            width: 350,
                            height: 150,
                            modal: true,
                            readOnly: _openerLockDoc == "1",
                            i18n: i18n.def,
                            buttons: {
                                "saveData": function(){
                                    if ($formSR.valid()) {
                                        $form.find("#submitRate").html($formSR.find("#tmpsubmitRate").val());
                                        _M.doAjax({
                                            action: "saveL140S02C",
                                            formId: L140S02CAction.formId,
                                            data: {
                                                L140S02ASeq: L140S02Action.L140S02ASeq,
                                                L140S02ASubjCode: ($("#"+L140S02Action.formId).find("#subjCode").val()||"") ,
                                                L140S02AProdKind: ($("#"+L140S02Action.formId).find("#prodKind").val()||"") ,
                                                saveFormData: JSON.stringify(saveData),
												currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']
                                            },
                                            success: function(obj){
                                                //儲存過清空期付金欄位
                                                L140S02Action.cleanPeriodAmt();
                                                //L140S02Action.formId= "L140S02AForm"
                                                var $l140s02a = $("#" + L140S02Action.formId);
                                                
                                                //開放可自行修改前期利率欄位值。
                                                $l140s02a.find("#L140S02C_PreDscr").html(obj.preDscr);
                                                
                                                $l140s02a.find("#rateDesc").html(obj.L140S02CDesc);
                                                var $periodSTr = $l140s02a.find("#periodSTr");
                                                var $for07Tr = $l140s02a.find("#for07Tr");
                                                
                                                $periodSTr.show();
                                                //當為按月計息或 舊案 無產生期付金對照表
                                                if ((obj.L140S02C_IntWay == "1" || obj.L140S02C_IntWay == "P" || obj.L140S02C_IntWay == "Q") 
                                                		|| L140S02Action._L140S02AData.creatSrc == "0") {
                                                    $periodSTr.hide();
                                                }
                                                
                                                if ($periodSTr.is(":hidden") && $for07Tr.is(":hidden")) {
                                                    $("#periodDiv").hide();
                                                }
                                                
                                                $form.find("#L140S02CDesc").html(obj.L140S02CDesc);
                                                $form.find("#desc").val(obj.desc);
                                                
                                                $("#preDscr").attr("readonly", "readonly");
                                                
                                            }
                                        });
                                        $.thickbox.close();
                                    }
                                },
                                "close": function(){
                                    $.thickbox.close();
                                }
                            }
                        });
                    }
                    else {
                        _M.doAjax({
                            action: "saveL140S02C",
                            formId: L140S02CAction.formId,
                            data: {
                                L140S02ASeq: L140S02Action.L140S02ASeq,
                                L140S02ASubjCode: ($("#"+L140S02Action.formId).find("#subjCode").val()||"") ,
                                L140S02AProdKind: ($("#"+L140S02Action.formId).find("#prodKind").val()||"") ,
                                saveFormData: JSON.stringify(saveData),
								currentApplyCurr: _M.AllFormData["04"]['currentApplyCurr']
                            },
                            success: function(obj){
                                //儲存過清空期付金欄位
                                L140S02Action.cleanPeriodAmt();
                                //L140S02Action.formId= "L140S02AForm"	
                                
                                //開放可自行修改前期利率欄位值。
                                $("#" + L140S02Action.formId).find("#L140S02C_PreDscr").html(obj.preDscr);
                                $("#" + L140S02Action.formId).find("#rateDesc").html(obj.L140S02CDesc);
                                $form.find("#L140S02CDesc").html(obj.L140S02CDesc);
                                $form.find("#desc").val(obj.desc);
                                
                                //開放可自行修改前期利率欄位值。
                                $("#preDscr").attr("readonly", "readonly");
                            }
                        });
                    }
                    
                },
                "close": function(){
                    //開放可自行修改前期利率欄位值。
                    $("#preDscr").attr("readonly", "readonly");
                    $.thickbox.close();
                }
            }
        });
    },
	
	changeRateFlagByMrRateCode: function(intWay, $rateFlag){
		
		if(intWay == '1'){ // 1-按月計息
			$rateFlag.val("2").trigger("change");
			$rateFlag.attr("disabled", "disabled");
		}
		
		if(intWay == '2'){ // 2-期付金
			$rateFlag.val("3").trigger("change");
			$rateFlag.attr("disabled", "disabled");
		}
	}
    
};
/**
 * 償還方式
 */
var L140S02EAction = {
    isInit: false,
    formId: "L140S02EForm",
    initEvent: function(){
        var $form = $("#" + this.formId)
        var $payWayAmtDiv = $form.find("#payWayAmtDiv");
        var $payWayOthDiv = $form.find("#payWayOthDiv");
        var $nowExtendDiv = $form.find("#nowExtendDiv");
        var $aboutPayWay = $form.find(".aboutPayWay");
        var $payWaySpan1 = $form.find("#payWaySpan1");
        var $payWaySpan3 = $form.find("#payWaySpan3");
        // 寬限期
        var $nowExtend = $form.find("[name=nowExtend]");
        //償還方式切換
        $form.find("#payWay").change(function(){
            var value = $(this).val();
            $aboutPayWay.hide();
            switch (value) {
                case "1":
                    $nowExtendDiv.show();
                    $payWaySpan1.show();
                    break;
                case "2":
                    break;
                case "3":
                case "4":
                    if (value == "3") {
                        $nowExtendDiv.show();
                        $payWaySpan3.show();
                    }
                    $payWayAmtDiv.show();
                    break;
                case "5":
                    break;
                case "6":
                    $payWayOthDiv.show();
                    break;
                case "7":
                    break;
                    
            }
            if (value != "1" && value != "3") {
                //當選項非1或3清除寬限期
                $nowExtend.removeAttr("checked");
                $(".forPayWay1or3").val("");
            }
            if (value != "3" && value != "4") {
                //清空輸入金額
                $payWayAmtDiv.find("input").val("");
            }
            //L140S02EAction.toWord();
        });
        var $adjCheckDiv = $form.find(".adjCheckDiv");
        //展延切換
        $form.find("#adjCheck").click(function(){
            $adjCheckDiv.hide();
            if (this.checked) {
                $adjCheckDiv.show();
            }
        });
        if(true){
        	$form.find("#adjKind").change(function(){
                var value = $(this).val();
                if(value=='8'){
                	$form.find(".l140s02e_adjKind8").show();
                }else{
                	$form.find(".l140s02e_adjKind8").hide();
                }
            });
        }
        var $adjItem2Div = $form.find("#adjItem2Div");
        //展延項目切換
        $form.find("[name=adjItem]").click(function(){
            var value = $(this).val();
            $adjItem2Div.hide();
            if (value == "2") {
                $adjItem2Div.show();
            }
        });
        var $nowExtendInput = $("#nowExtendSpan").find("input");
        /**
         * 寬限期 checkbox
         */
        $nowExtend.click(function(){
            if ($(this).is(":checked")) {
                $nowExtendInput.removeAttr("disabled");
            }
            else {
                $nowExtendInput.attr("disabled", "disabled").val("");
            }
        });
    },
    init: function(data){
        if (!this.isInit) {
            L140S02Action.initItem(this.formId);
            this.initEvent();
            this.isInit = true;
        }
        
        var $form = $("#" + this.formId);
        $form.reset();
        $form.find(".aboutPayWay,.adjCheckDiv").hide();
        if (data) {
            $form.injectData(data);
            $form.find("select").trigger("change");
        }
        //取得產品對應計息方式
        var rIntWay = $("#" + L140S02Action.formId).find("#subjCode :selected").attr("rIntWay");
        if (rIntWay) {
            //如果可選的選項大於1
            if (rIntWay.split("|").length > 1) {
                rIntWay = data.L140S02C_IntWay || "";
            }
            var $payWay = $form.find("#payWay");
            $payWay.find("option").each(function(){
                var value = $(this).val();
                if (rIntWay == "1" || rIntWay == "P" || rIntWay == "Q") {
                    //計息方式為1-按月則償還方式只能選7或6。
                    if (value == "7" || value == "6") {
                        $(this).removeAttr("disabled");
                    }
                    else {
                        $(this).attr("disabled", "disabled");
                    }
                }else{
                
                    if (rIntWay == "2") {
                        //計息方式為2-期付金時則償還方式不能選7。
                        if (value == "7") {
                            $(this).attr("disabled", "disabled");
                        }
                        else {
                            $(this).removeAttr("disabled");
                        }
                    }
                    else {
                        $(this).removeAttr("disabled");
                    }
                }     	
            });
        }
    },
    /**
     * 產生組合字串
     **/
    toWord: function(){
        var payWayItem = icombos['L140S02E_payWay'];
        var $form = $("#" + this.formId);
        var payWay = $form.find("#payWay").val();
        var payWayAmt = $form.find("#payWayAmt").val();
        var payWayOth = $form.find("#payWayOth").val();
        var temp = "";
        if (payWay != "") {
            temp += payWayItem[payWay];
            if (payWay == "3" || payWay == "4") {
                temp = temp.replace("：", "：" + util.addComma(payWayAmt));
            }
            
            if (payWay == "6") {
                temp = payWayOth;
            }
        }
        
        
        
        //前次寬限期起始年月
        var lastYM = $.trim($form.find("#lastYM").val());
        //前次寬限期(起
        var extFrom = $.trim($form.find("#extFrom").val());
        //前次寬限期(迄
        var extEnd = $.trim($form.find("#extEnd").val());
        //截至前次剩餘之寬限期
        var overTerm = $.trim($form.find("#overTerm").val());
        if (lastYM != "") {
        
            //L140S02E.lastYM=前次寬限期起始年月
            temp += i18n.cls1151s01["L140S02E.lastYM"] + "：" + lastYM + "，";
            //L140S02E.extFrom=前次寬限期(起)
            temp += i18n.cls1151s01["L140S02E.extFrom"] + "：" + util.addZeroBefore(extFrom, 3) + "，";
            //L140S02E.extEnd=前次寬限期(迄)
            temp += i18n.cls1151s01["L140S02E.extEnd"] + "：" + util.addZeroBefore(extEnd, 3) + "，";
            //L140S02E.overTerm=截至前次剩餘之寬限期
            temp += i18n.cls1151s01["L140S02E.overTerm"] + "：" + overTerm + "。";
        }
        
        var $nowExtend = $form.find("#nowExtend");
        var nowFrom = util.addZeroBefore($form.find("#nowFrom").val(), 3);
        var nowEnd = util.addZeroBefore($form.find("#nowEnd").val(), 3);
        var nowTerm = util.addZeroBefore($form.find("#nowTerm").val(), 3);
        if ($nowExtend.is(":checked")) {
            //寬限期字串
            var nowExtendStr = "";
            if (nowFrom != "" && nowEnd != "") {
                //L140S02E.nowFrom=寬限期
                //page5.054=期
                nowExtendStr = i18n.cls1151s01["L140S02E.nowFrom"] + nowFrom + " － " + i18n.cls1151s01["page5.054"] + nowEnd + i18n.cls1151s01["page5.054"];
                //page5.078=按月付息，餘
                nowExtendStr += "，" + i18n.cls1151s01["page5.078"] + nowTerm + i18n.cls1151s01["page5.054"];
                if (payWay == "1") {
                    //page5.083=本息平均攤還。
                    nowExtendStr += i18n.cls1151s01["page5.083"];
                }
                
                if (payWay == "3") {
                    //page5.079=本金平均攤還。
                    nowExtendStr += i18n.cls1151s01["page5.079"];
                }
                temp += nowExtendStr;
            }
        }
        var $adjCheck = $form.find("#adjCheck");
        if ($adjCheck.attr("checked")) {
            var adjKind = $form.find("[name=adjKind]:checked").val();
            var adjItem = $form.find("[name=adjItem]:checked").val();
            if (icombos['L140S02E_adjKind'][adjKind]) {
                temp += icombos['L140S02E_adjKind'][adjKind];
            }
            if (icombos['L140S02E_adjItem'][adjKind]) {
                temp += icombos['L140S02E_adjItem'][adjItem];
            }
            
            var adjCapSNum = util.addZeroBefore($form.find("#adjCapSNum").val(), 3);
            var adjCapENum = util.addZeroBefore($form.find("#adjCapENum").val(), 3);
            var adjIntSNum = util.addZeroBefore($form.find("#adjIntSNum").val(), 3);
            var adjIntENum = util.addZeroBefore($form.find("#adjIntENum").val(), 3);
            var amorENum = util.addZeroBefore($form.find("#amorENum").val(), 3);
            
            if (adjCapSNum) {
                //L140S02E.adjCapSNum=本金展延起始期
                temp += "<br/>" + i18n.cls1151s01["L140S02E.adjCapSNum"] + "：" + adjCapSNum;
            }
            if (adjCapENum) {
                //L140S02E.adjCapENum=本金展延截止期
                temp += "<br/>" + i18n.cls1151s01["L140S02E.amorENum"] + "：" + adjCapENum;
            }
            if (adjIntSNum) {
                //L140S02E.adjIntSNum=利息展延起始期
                temp += "<br/>" + i18n.cls1151s01["L140S02E.adjIntSNum"] + "：" + adjIntSNum;
            }
            if (adjIntENum) {
                //L140S02E.adjIntENum=利息展延截止期
                temp += "<br/>" + i18n.cls1151s01["L140S02E.adjIntENum"] + "：" + adjIntENum;
            }
            if (amorENum) {
                //i18n.cls1151s01["L140S02E.amorSNum"]=應收利息攤還截止期
                temp += "<br/>" + i18n.cls1151s01["L140S02E.amorENum"] + "：" + amorENum;
            }
        }
        $form.find("#tempShow").html(temp);
        return temp;
    },
    openL140S02E: function(){
        _M.doAjax({
            action: "queryL140S02E",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            success: function(obj){
                if (obj.nowExtend != "Y") {
                    $("#nowExtendSpan").find("input").attr("disabled", "disabled").val("");
                }
                
                L140S02EAction.init(obj);
            }
        });
        $("#L140S02EBox").thickbox({
            //L140S02A.payoffWay=償還方式
            title: i18n.cls1151s01["L140S02A.payoffWay"],
            width: 800,
            height: 550,
            modal: true,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "saveData": function(){
                    var $form = $("#" + L140S02EAction.formId);
                    if (L140S02EAction.saveCheck($form) && $form.valid()) {
                        //var word = L140S02EAction.toWord();
                        //$("#payoffWay").html(L140S02EAction.toWord());
                        _M.doAjax({
                            action: "saveL140S02E",
                            formId: L140S02EAction.formId,
                            data: {
                                L140S02ASeq: L140S02Action.L140S02ASeq
                            },
                            success: function(obj){
                                //當償還方式為6,7則不可以打期付金的項目
                                var $periodDiv = $("#periodDiv");
                                $periodDiv.show();
                                var value = $form.find("#payWay").val();
                                if (value == "6" || value == "7" || _M.AllFormData.docCode == "5") {
                                    $periodDiv.hide();
                                }
                                //儲存過清空期付金欄位
                                L140S02Action.cleanPeriodAmt();
                                
                                $("#payoffWay").html(obj.word);
                                $("#payoffWay")
                                $form.find("#tempShow").html(obj.word);
                                
                                var appendStr = "";
                                if(obj.chg_intrt_cycl_cat=="0"){                        				
                    			}else {
                    				$("#rateDesc").html(obj.rateDesc);
                    				
                    				if(obj.chg_intrt_cycl_cat=="A" || obj.chg_intrt_cycl_cat=="B"){
                    					var _desc = obj.chg_intrt_cycl_cat=="A"?"雙週繳→月繳":"月繳→雙週繳";
                    					appendStr = "<br/>期付金繳款週期："+_desc+"。請於「利率」之「其他」輸入內容";
                        			}                    					
                    			}
                                
                                //saveSuccess=儲存成功
                                API.showMessage((i18n.def['saveSuccess']+appendStr));
                            }
                        });
                    }
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 儲存前檢查
     * @param {Object} $form 表單物件
     *
     */
    saveCheck: function($form){
    
        var $L140S02AForm = $("#" + L140S02Action.formId);
        //授信期間YY年MM月((YY*12)+MM)-寬限止期即是餘期。
        
        //寬限止期不可大於((YY*12)+MM)
        if ($form.find("[name=nowExtend]").is(":checked")) {
            var nowFrom = parseInt($form.find("#nowFrom").val(), 10);
            var nowEnd = parseInt($form.find("#nowEnd").val(), 10);
            if (nowFrom > nowEnd) {
                //page5.155=起期不可大於迄期
                API.showErrorMessage(i18n.cls1151s01["page5.155"]);
                return false;
            }
            var year = parseInt($L140S02AForm.find("#lnYear").val(), 10);
            var mon = parseInt($L140S02AForm.find("#lnMonth").val(), 10);
            var totalMon = (year * 12) + mon;
            if (nowEnd > totalMon) {
                //page5.162=寬限止期不可大於授信年月期間
                API.showErrorMessage(i18n.cls1151s01["page5.162"]);
                return false;
            }
            $form.find("#nowTerm").val($.trim(totalMon - nowEnd));
        }
        
        
        //償還方式
        var payWay = $form.find("#payWay").val();
        //前次寬限期起始年月
        var lastYM = $.trim($form.find("#lastYM").val());
        //截至前次剩餘之寬限期
        var overTerm = $.trim($form.find("#overTerm").val());
        
        //前次寬限期(起
        var extFrom = $.trim($form.find("#extFrom").val());
        //前次寬限期(迄
        var extEnd = $.trim($form.find("#extEnd").val());
        
        if (payWay == "1" || payWay == "3") {
            //當有一個欄位不為空，其他三欄皆需輸入
            if (lastYM != "" || extFrom != "" || extEnd != "" || overTerm != "") {
                if (lastYM == "" || extFrom == "" || extEnd == "" || overTerm == "") {
                    //page5.160=前次寬限期資料不完整！
                    API.showErrorMessage(i18n.cls1151s01["page5.160"]);
                    return false;
                }
            }
        }
        
        var extFromNum = parseInt(extFrom, 10);
        var extEndNum = parseInt(extEnd, 10);
        if (extFromNum > extEndNum) {
            //page5.155=起期不可大於迄期
            API.showErrorMessage(i18n.cls1151s01["page5.155"]);
            return false;
        }
        
        
        if (parseInt(overTerm, 10) > (extEndNum - extFromNum)) {
            //page5.161=截至前次剩餘之寬限期不可以大於前次寬限期(迄)-前次寬限期(起)
            API.showErrorMessage(i18n.cls1151s01["page5.161"]);
            return false;
        }
        
        
        return true;
    }
};
/**
 * 代償轉貸借新還舊明細
 */
var L140S02HAction = {
    isInit: false,
    formId: "L140S02HForm",
    formData: null,
    setChgOther: function(data){
        $("[name=chgALoan][value=" + data + "]").attr("checked", "checked");
    },
    init: function(data){
        if (!this.isInit) {
            L140S02Action.initItem(this.formId);
            this.initEvent();
            this.isInit = true;
        }
        var $form = $("#" + this.formId);
        $form.reset();
        //代償同業房貸其他原因
        var $subReasonOthDiv = $form.find("#subReasonOthDiv");
        var $oLNEndDate = $form.find("#oLNEndDate");
        $oLNEndDate.removeClass("required");
        $subReasonOthDiv.hide();
        if (data) {
            L140S02HAction.formData = data;
            $form.injectData(data);
            var $bankNo = $form.find("#bankNo");
            var $branchNo = $form.find("#branchNo");
            var $branchName = $form.find("#branchName");
            var $L140S02HFor017 = $form.find("#L140S02HFor017");
            if (data.bankNo == "017") {
                $oLNEndDate.addClass("required");
                $branchNo.attr("disabled", "disabled");
                $branchName.attr("disabled", "disabled");
                $L140S02HFor017.show();
            }
            else {
                $branchNo.removeAttr("disabled", "disabled");
                $branchName.removeAttr("disabled", "disabled");
                $L140S02HFor017.hide().find("input").val("");
            }
        }
    },
    initEvent: function(){
        var $form = $("#" + this.formId);
        //代償同業房貸原因
        var $subReasonOthDiv = $form.find("#subReasonOthDiv");
        $form.find("[name=subReason]").click(function(){
            $subReasonOthDiv.hide();
            if ($(this).val() == "4") {
                $subReasonOthDiv.show();
            }
        });
        //登錄轉出之原金融機構代碼
        var $bankNo = $form.find("#bankNo");
        var $bankName = $form.find("#bankName");
        var $branchNo = $form.find("#branchNo");
        var $branchName = $form.find("#branchName");
        var $L140S02HFor017 = $form.find("#L140S02HFor017");
        var $oLNEndDate = $form.find("#oLNEndDate");
        $form.find("#loginL140S02HBrno").click(function(){
            QueryBranch.open({
                fn: function(data){
                    if (data.bankCode == "017") {
                        $oLNEndDate.addClass("required");
                        $branchNo.attr("disabled", "disabled");
                        $branchName.attr("disabled", "disabled");
                        $L140S02HFor017.show();
                        data.branchName = data.bankCodeCn + data.branchName;
                        data.branchCode = data.bankCode + data.branchCode;
                    }
                    else {
                        $oLNEndDate.removeClass("required");
                        $branchNo.removeAttr("disabled", "disabled");
                        $branchName.removeAttr("disabled", "disabled");
                        $L140S02HFor017.hide().find("input").val("");
                    }
                    $bankNo.val(data.bankCode);
                    $bankName.val(data.bankCodeCn);
                    $branchNo.val(data.branchCode);
                    $branchName.val(data.branchName);
                }
            });
        });
        
        /**
         * 引進放款帳號
         */
        $form.find("#pullinSubACNoBt").click(function(){
            L140S02HAction.openInputCustIdByLNF030()
            //L140S02HAction.openLNF030();
        });
        
    },
    openL140S02H: function(clumn, options, data){
    	ilog.debug("@ajax > openL140S02H > queryL140S02H");
        var oid = data ? data.oid : "";
        _M.doAjax({
            action: "queryL140S02H",
            data: {
                oid: oid
            },
            success: function(obj){
                L140S02HAction.init(obj);
            }
        });
        $("#L140S02HBox").thickbox({
            //page5.038=代償/轉貸/借新還舊
            title: i18n.cls1151s01["page5.038"],
            width: 600,
            height: 450,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $form = $("#" + L140S02HAction.formId);
                    if ($form.valid()) {
                        //原貸放日期
                        var oLNAppDate = $form.find("#oLNAppDate").val();
                        //原貸款到期日
                        var oLNEndDate = $form.find("#oLNEndDate").val();
                        //原貸款到期日 不可小於上面的原貸放日期 
                        if (oLNEndDate && oLNAppDate && (oLNEndDate < oLNAppDate)) {
                            //page5.146=原貸款到期日 不可小於原貸放日期
                            return API.showMessage(i18n.cls1151s01["page5.146"]);
                        }
                        var subACNo = $.trim($form.find("#subACNo").val());
                        var bankNo = $form.find("#bankNo").val();
                        var branchNo = $form.find("#branchNo").val();
                        if (bankNo == "017" && subACNo != '' && subACNo.length >= 3) {
                            if (branchNo.substring(3, 6) != subACNo.substring(0, 3)) {
                                //page5.182=代償本行帳號需為轉出之原金融機構代碼之分行！！
                                return API.showMessage(i18n.cls1151s01["page5.182"]);
                            }
                        }
                        _M.doAjax({
                            action: "saveL140S02H",
                            formId: L140S02HAction.formId,
                            data: {
                                L140S02ASeq: L140S02Action.L140S02ASeq,
                                oid: oid
                            },
                            success: function(obj){
                                L140S02HAction.setChgOther(obj.have017);
                                L140S02HAction._reloadL140S02HGrid({
                                    tabFormMainId: _M.tabMainId,
                                    L140S02ASeq: L140S02Action.L140S02ASeq
                                });
                                $.thickbox.close();
                                //儲存成功
                                API.showMessage(i18n.def.saveSuccess);
                            }
                        });
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    L140S02HGrid: null,
    /**
     * 初始化grid
     */
    initGrid: function(){
    	ilog.debug("@ajax > initGrid > queryL140S02H");
        this.L140S02HGrid = $("#L140S02HGrid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            multiselect: true,
            hideMultiselect: false,
            rowNum: 10,
            postData: {
                formAction: "queryL140S02H",
                tabFormMainId: _M.tabMainId,
                L140S02ASeq: L140S02Action.L140S02ASeq
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140S02H.branchNo"],//分行代號,
                name: 'branchNo',
                align: "left",
                width: 60,
                sortable: true,
                formatter: "click",
                onclick: L140S02HAction.openL140S02H
            }, {
                colHeader: i18n.cls1151s01["L140S02H.branchName"],//分行名稱,
                name: 'branchName',
                align: "left",
                width: 60,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140S02H.subAmt"],//代償金額,
                name: 'subAmt',
                align: "right",
                width: 60,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            }, {
                name: 'oid',
                hidden: true
            }],
            ondblClickRow: function(rowid){
                var data = L140S02HAction.L140S02HGrid.getRowData(rowid);
                L140S02HAction.openL140S02H(null, null, data);
            }
        });
    },
    
    /**
     * 重新整代償grid
     */
    _reloadL140S02HGrid: function(data){
    	ilog.debug("@ajax > _reloadL140S02HGrid");
        this.L140S02HGrid.reload(data);
    },
    /**
     *刪除代償轉貸借新還舊明細檔
     */
    delL140S02H: function(){
        var $grid = this.L140S02HGrid;
        //多筆
        var rowData = $grid.getSelectData("oid");
        if (rowData) {
            //confirmDelete=是否確定刪除?
            API.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    _M.doAjax({
                        action: "delL140S02H",
                        data: {
                            L140S02ASeq: L140S02Action.L140S02ASeq,
                            oids: rowData
                        },
                        success: function(obj){
                            L140S02HAction.setChgOther(obj.have017);
                            L140S02HAction._reloadL140S02HGrid({
                                tabFormMainId: _M.tabMainId,
                                L140S02ASeq: L140S02Action.L140S02ASeq
                            });
                        }
                    });
                }
            });
        }
    },
    /**
     * LNF030 取得放款帳號grid
     */
    lnf030Grid: null,
    isLNF030GridInit: false,
    initLNF030Grid: function(custId, dupNo){
        this.lnf030Grid = $("#lnf030Grid").iGrid({
            handler: _M.ghandle,
            height: 230,
            rownumbers: true,
            rowNum: 100,
            postData: {
                formAction: "queryLNF030",
                tabFormMainId: _M.tabMainId,
                custId: custId,
                dupNo: dupNo
            },
            colModel: [{
                colHeader: i18n.cls1151s01["L140S02H.subACNo"],//代償本行帳號,
                name: 'LOANNO',
                align: "left",
                width: 100,
                sortable: true
            }, {
                colHeader: i18n.cls1151s01["L140S02H.subAmt"],//代償金額,
                name: 'BAL',
                align: "right",
                width: 100,
                sortable: true,
                formatter: 'currency',
                formatoptions: {
                    thousandsSeparator: ",",
                    decimalPlaces: 0
                }
            }, {
                name: 'OLNAPPDATE',
                hidden: true
            }, {
                name: 'OLNENDDATE',
                hidden: true
            }],
            ondblClickRow: function(rowid){
            
            }
        });
    },
    /**
     * 開啟輸入custId視窗
     */
    openInputCustIdByLNF030: function(){
        var $form = $("#LNF030BoxForm");
        $form.reset();
        $("#openInputCustIdByLNF030Box").thickbox({
            //L140M01O.custId=統一編號
            title: i18n.cls1151s01["L140M01O.custId"],
            width: 350,
            height: 160,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($form.valid()) {
                        $.thickbox.close();
                        L140S02HAction.openLNF030($form.find("#LNF030Box_custId").val(), $form.find("#LNF030Box_dupNo").val());
                    }
                    
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * LNF030 取得放款帳號grid
     */
    openLNF030: function(custId, dupNo){
        if (!this.isLNF030GridInit) {
            this.initLNF030Grid(custId, dupNo);
            this.isLNF030GridInit = true;
        }
        else {
            this.lnf030Grid.reload({
                tabFormMainId: _M.tabMainId,
                custId: custId,
                dupNo: dupNo
            });
        }
        $("#LNF030Box").thickbox({
            //L140S02H.subACNo=代償本行帳號
            title: i18n.cls1151s01["L140S02H.subACNo"],
            width: 500,
            height: 400,
            modal: true,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var $grid = L140S02HAction.lnf030Grid;
                    //單筆
                    var rowData = $grid.getSingleData();
                    if (rowData) {
                        var $form = $("#" + L140S02HAction.formId);
                        $form.find("#subAmt").val(rowData.BAL);
                        $form.find("#subACNo").val(rowData.LOANNO);
                        $form.find("#oLNAppDate").val(rowData.OLNAPPDATE);
                        $form.find("#oLNEndDate").val(rowData.OLNENDDATE);
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
};
var L140M01HAction = {
    RateFormId: "L140M01HForm",
    isInit: false,
    initEvent: function(){
        $("#rateBox2").find("#businessBT").click(function(){//利費率商業本票保證
            L140M01HAction.openRateChildrenBox('cpType');
        }).end().find("#promiseBT").click(function(){//利費率開發保證函
            L140M01HAction.openRateChildrenBox('cfType');
        }).end().find("#companyBT").click(function(){//利費率承兌費率
            L140M01HAction.openRateChildrenBox('cpyType');
        }).end().find("#thickRateBT").click(function(){//利費率登入新台幣
            L140M01HAction.openRateChildrenBox('paType');
        });
        
        
        $("#wordBaseBt2").click(function(){
            //L140M01h.pa2Rate=費率、L140M01h.wordBase2=依本行規定計收
            $("#othDes").val("ＸＸ" + i18n.cls1151s01["L140M01h.pa2Rate"] + "：" + i18n.cls1151s01["L140M01h.wordBase2"]);
        });
        //針對 [費率]內有X的select做隱藏或顯示
        $(".selectX").change(function(){
            var $thisId = $(this).attr("id");
            if ($(this).val() == "5") {
                $("#" + $thisId + "Select").show();
            }
            else {
                $("#" + $thisId + "Select").hide();
            }
        });
        /**
         * 組成字串
         */
        $("#rateToWordBt").click(function(){
            L140M01HAction.rateToWord();
        });
        /**  清除[費率]欄位內容  */
        $("#clearRate").click(function(){
            $("#" + L140M01HAction.RateFormId).reset();
            $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
        });
    },
    initItem: function(){
        var item = CommonAPI.loadCombos(["lms1405s0204_monType", "lms1405s0204_monType2"]);
        //[費率]預收方式 for 承兌
        $(".lms1405s0204_monType").setItems({
            space: true,
            item: item.lms1405s0204_monType,
            format: "{key}"
        });
        
        //[費率]預收方式2 for開發
        $(".lms1405s0204_monType2").setItems({
            space: true,
            item: item.lms1405s0204_monType2,
            format: "{key}"
        });
    },
    init: function(){
        if (!this.isInit) {
            this.initEvent();
            this.initItem();
            this.isInit = true;
        }
    },
    openBox: function(){
        this.init();
        var $form = $("#" + L140M01HAction.RateFormId).reset();
        $form.reset();
        $(".rateSpan1,.rateSpan2,.rateSpan3,.rateSpan4").hide();
        _M.doAjax({
            action: "queryL140m01h",
            data: {
                L140S02ASeq: L140S02Action.L140S02ASeq,
                showMsg: true
            },
            success: function(obj){
                $form.injectData(obj);
                $("#radioSpan1").find(".rateSpan" + obj.cpType).show();
                $("#radioSpan2").find(".rateSpan" + obj.cfType).show();
                $("#radioSpan3").find(".rateSpan" + obj.cpyType).show();
                $("#radioSpan4").find(".rateSpan" + obj.paType).show();
            }
        });
        
        $("#rateBox2").thickbox({
            // L140S02A.freeRateDesc=費率
            title: i18n.cls1151s01["L140S02A.freeRateDesc"],
            width: 920,
            height: 500,
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            modal: true,
            buttons: {
                "saveData": function(){
                    if (!$form.valid()) {
                        //common.001=欄位檢核未完成，請填妥後再送出
                        API.showMessage(i18n.def['common.001']);
                        return false;
                    }
                    var RateDrc = L140M01HAction.rateToWord();//在按確定的時候幫他組字串
                    _M.doAjax({
                        action: "saveL140m01h",
                        formId: L140M01HAction.RateFormId,
                        data: {//把資料轉成json
                            L140S02ASeq: L140S02Action.L140S02ASeq
                        },
                        success: function(obj){
                            $("#RateDrc").val(RateDrc);
                            ////L140S02Action.formId= "L140S02AForm"
                            $("#" + L140S02Action.formId).find("#freeRateDesc").html(RateDrc);
                        }
                    });
                    
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 組費率字串
     */
    rateToWord: function(){
        var AItemVal = $("[name=cpType]:checked").val();//商業本票保證的值
        var BItemVal = $("[name=cfType]:checked").val();//開發保證函的值
        var CItemVal = $("[name=cpyType]:checked").val();//公司債保證的值
        var DItemVal = $("[name=paType]:checked").val();//承兌費率 的值
        var other = $("#othDes").val();//其他
        var str = '';
        var sign = ",";
        var sign2 = "。";
        var sign3 = "－";
        if (AItemVal && AItemVal != "c") {//當不是空值並且非clear 才組字串
            var Aword = L140M01HAction.toConverWord("#radioSpan1" + " .rateSpan" + AItemVal);
            
            if (Aword != "") {
                //L140M01h.cpType=商業本票保證
                str = i18n.cls1151s01["L140M01h.cpType"] + sign3 + Aword + sign2;
            }
            
            $("#cpDes").val(Aword);
        }
        if (BItemVal && BItemVal != "c") {//當不是空值並且非clear 才組字串
            var Bword = L140M01HAction.toConverWord("#radioSpan2" + " .rateSpan" + BItemVal);
            if (Bword != "") {
                //L140M01h.cfType=開發保證函
                str = str + i18n.cls1151s01["L140M01h.cfType"] + sign3 + Bword + sign2;
            }
            
            switch (BItemVal) {
                case '1':
                    if ($("#cf1MD").val() == '5') {
                        str = str.replace("X", $("#cf1Mon2").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cf2MD").val() == '5') {
                        str = str.replace("X", $("#cf2Mon").val());
                        $("#cfDes").val(Bword.replace("X", $("#cf2Mon").val()));
                    }
                    break;
            }
            
        }
        if (CItemVal && CItemVal != "c") {//當不是空值並且非clear 才組字串
            var Cword = L140M01HAction.toConverWord("#radioSpan3" + " .rateSpan" + CItemVal);
            
            if (Cword != "") {
                //L140M01h.cpyType=公司債保證
                str = str + i18n.cls1151s01["L140M01h.cpyType"] + sign3 + Cword + sign2;
            }
            switch (CItemVal) {
                case '1':
                    if ($("#cpy1MD").val() == '5') {
                        str = str.replace("X", $("#cpy1Mon2").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy1Mon2").val()));
                    }
                    break;
                case '2':
                    if ($("#cpy2MD").val() == '5') {
                        str = str.replace("X", $("#cpy2Mon").val());
                        $("#cpyDes").val(Cword.replace("X", $("#cpy2Mon").val()));
                    }
                    break;
            }
        }
        if (DItemVal && DItemVal != "c") {//當不是空值並且非clear 才組字串
            var Dword = L140M01HAction.toConverWord("#radioSpan4" + " .rateSpan" + DItemVal);
            if (Dword != "") {
                //L140M01h.paType=承兌費率
                str = str + i18n.cls1151s01["L140M01h.paType"] + sign3 + Dword + sign2;
            }
            switch (DItemVal) {
                case '1':
                    if ($("#pa1MD").val() == '5') {
                        str = str.replace("X", $("#pa1Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa1Mon").val()));
                    }
                    break;
                case '2':
                    if ($("#pa2MD").val() == '5') {
                        str = str.replace("X", $("#pa2Mon").val());
                        $("#paDes").val(Dword.replace("X", $("#pa2Mon").val()));
                    }
                    break;
            }
            
        }
        if (other) {
            str = str + other + sign2;
        }
        $("#rateDscr2").val(str);
        return str;
    },
    openRateChildrenBox: function(type){//顯示出現哪一種thickbox
        var rateType = '';
        switch (type) {
            case "cpType":
                //L140M01h.cpType=商業本票
                rateTitleName = i18n.cls1151s01["L140M01h.cpType"];
                rateType = "1";
                break;
            case "cfType":
                //L140M01h.cfType=開發保證函
                rateTitleName = i18n.cls1151s01["L140M01h.cfType"];
                rateType = "2";
                break;
            case "cpyType":
                //L140M01h.cpyType=公司債保證
                rateTitleName = i18n.cls1151s01["L140M01h.cpyType"];
                rateType = "3";
                break;
            case "paType":
                //L140M01h.paType=承兌費率
                rateTitleName = i18n.cls1151s01["L140M01h.paType"];
                rateType = "4";
                break;
        }
        $("#rateChildrenBox" + rateType).show().siblings("table").hide();
        $("#rateChildrenBox").thickbox({
            title: rateTitleName,
            width: 720,
            height: 250,
            align: "center",
            valign: "bottom",
            readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    //cpType-商業本票保證,cfType-開發保證函,cpyType-公司債保證,paType-承兌費率
                    var radioVal = $("[name =" + type + "]:checked").val();
                    if (radioVal != 'c') {
                        $("#radioSpan" + rateType + " .rateSpan" + radioVal).show().siblings().hide().find(":input,select").val("");
                    }
                    else {
                        $("#radioSpan" + rateType).find("[class^=rateSpan]").hide().find(":input,select").val("");
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /** 字串轉換 */
    toConverWord: function(spanName){
        var _str = "";
        var stop = false;
        $(spanName).find("input:text,select,span.word").filter(function(){
            return !($(this).hasClass("intputX"));//當class=intputX就不組入字串內
        }).each(function(){
            _str = $.trim(_str);
            if (!stop) {
                switch (this.nodeName.toLowerCase()) {
                    case 'span':
                        _str += $(this).text();
                        break;
                    case 'select':
                        _str += $(this).find(":selected").text();
                        break;
                    case 'input':
                        if ($(this).val() == "") {
                            stop = true;
                        }
                        _str += $(this).val();
                        break;
                }
            }
        });
        if (_str.match(/\($/)) {
            return _str.replace(/\(/g, "");
        }
        return _str;
    }
}

//J-109-0304_10702_B1003 Web e-Loan消金新增房仲引介來源 
var landAction = {
    grid: null,
    init : function(){

	},
    build: function(obj){
    	var gridview_colModel =  [{
            colHeader: i18n.cls1151s01['L140S02M.landAddress'],//'土地地址',
            name: 'landAddress',
            align: "center",
            sortable: true,
            width: 20
        },{
            colHeader: i18n.cls1151s01['L140S02M.landNo'],//'地號',
            name: 'landNo',
            align: "center",
            sortable: true,
            width: 20
        },{
        	name: 'oid',
            hidden: true
        }];
    	
    	landAction.grid = $("#landgridview").iGrid({
            handler: 'cls1151gridhandler',
            data: {
    	        mainId: _M.tabMainId
    	    },
            height: 150,
            width: 400,
            action: "queryL140S02M",
            rownumbers: true,
            colModel: gridview_colModel
        });
    },
    reloadGrid: function(data){
        if (data) {
        	landAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        } else {
        	landAction.grid.trigger('reloadGrid');
        }
    },
    getgridRowData: function(){
        var datas = landAction.grid.getSelRowDatas();
        if (!datas) {
            MegaApi.showErrorMessage(i18n.def["confirmTitle"], i18n.def["action_005"]);
        }
        return datas;
    }
}

_M.pageInitAcion["05"] = PanelAction05;

/**
 * hide/show hgContractTr 是否重簽契約
 */
function hs_hgContractTr(param){
	if(match_prodKind_propertyStr(param||{})){
		$("#hgContractTr.aboutProdKind").show();
		if (_M.CLSAction_isReadOnly == "Y") {
			//若已核准，不應再 call 513
			/*
			 簽案時, l140s02a.chgConTimes=0
			 核准後, l140s02a.chgConTimes仍=0(但ELF513在核准時 +1)
			 	=> 應抓 l140s02a.chgConTimes 的值, 而非 elf513_renew_cnt
			 */
		}else if (_M.CLSAction_isReadOnly == "N") {
			L140S02Action.find513();
		}		
	}else{
		$("#hgContractTr.aboutProdKind").hide();
	}	
}

function match_prodKind_propertyStr(param){
	
	var prodKind = param.hasOwnProperty('prodKind')?param['prodKind']:$("#prodKind").val();	
	//產品:02,04
	//為了讓產品:68中期循環-續約時，能勾選{本次是否重簽契約=是}
	if (prodKind == "02" || prodKind == "04" || prodKind == "68") {	       
    }else{
    	return false;
    }
	
	
	var property = param.hasOwnProperty('property')?param['property']:$("#property").val();
	//性質:續約-2,提前續約-11
	if(property=='2'||property=='11'){
		return true;
	}
	var subProperty = param.hasOwnProperty('subProperty')?param['subProperty']:$("input[name=subProperty]:checkbox:checked").map(function(){ return $(this).val(); }).get().join("|");
	var arr = subProperty.split("|");	
	for(var i=0;i<arr.length;i++){
		var val = arr[i];
		
		if(val=='2'||val=='11'){			
			return true;
		}
	}
	return false;
}

function z_debug(val){
	ilog.debug( "[z_debug]"+val );
}
function upd_chgConTimes(oldval, newval){
	_M.doAjax({
	    action: "upd_chgConTimes",
	    data: {
	        L140S02ASeq: L140S02Action.L140S02ASeq ,
	        'oldval': oldval ,
	        'newval': newval
	    },
	    success: function(json){
	       alert(json.msg);	        
	    }
	});
}

// J-109-0135 新案且為69勞工紓困貸款時發送簡訊通知客戶
function prodKind69Display(){
	var $form = $("#" + L140S02Action.formId);
	var prodKind = $form.find("#prodKind");
	var property = $form.find("#property");
	$("#prodKind69Tr")["1" == property.val() && "69" == prodKind.val() ? 'show' : 'hide']();
}

//J-109-0304
function autoTab()
{
   if ($("#landNo1").val().length==4)
   {
	   $("#landNo2").focus();
   }
}

function checkRepayment(){
	_M.doAjax({
	    action: "showRepaymentBtn",
	    data: {
	        L140S02ASeq: L140S02Action.L140S02ASeq ,
	        tabFormMainId: _M.tabMainId
	    },
	    success: function(result){
	       if(result.showRepaymentBtn){
	           $("#addL140S02HBt").hide();
	           $("#delL140S02HBt").hide();
	       }
	       else{
	            $("#addL140S02HBt").show();
                $("#delL140S02HBt").show();
	       }
	    }
	});
}