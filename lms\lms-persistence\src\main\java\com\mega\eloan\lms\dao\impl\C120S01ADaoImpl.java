/* 
 * C120S01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C120S01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C120S01A;

/** 個金基本資料檔 **/
@Repository
public class C120S01ADaoImpl extends LMSJpaDao<C120S01A, String> implements
		C120S01ADao {

	@Override
	public C120S01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C120S01A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C120S01A findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C120S01A> findByIndex01(String mainId, String custId,
			String dupNo) {
		ISearch search = createSearchTemplete();
		List<C120S01A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C120S01A> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<C120S01A> list = createQuery(C120S01A.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<C120S01A> findC120S01A_mainId_busCodeEmpty(String mainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		
		search.addSearchModeParameters(SearchMode.OR,
				new SearchModeParameter(SearchMode.IS_NULL, "busCode", ""),
				new SearchModeParameter(SearchMode.EQUALS, "busCode", ""));
		//==============
		List<C120S01A> list = createQuery(C120S01A.class, search).getResultList();
		return list;
	}
	
	@Override
	public C120S01A findC120S01A_idDup_latestIdv(String custId, String dupNo){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.IS_NOT_NULL, "birthday", "");
		search.addSearchModeParameters(SearchMode.IN, "sex", new String[]{"M", "F"});
		
		search.addOrderBy("updateTime", true);
		search.addOrderBy("createTime", true);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<C120S01A> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<C120S01A> list = createQuery(C120S01A.class, search)
				.getResultList();
		return list;
	}
	
	@Override
	public int deleteByOid(String oid) {
		Query query = entityManager.createNamedQuery("C120S01A.deleteOid");
		query.setParameter("OID", oid);
		return query.executeUpdate();
	}
}