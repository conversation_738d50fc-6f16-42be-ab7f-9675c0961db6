/* 
 * L140S02ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S02A;

/** 個金產品種類檔 **/
public interface L140S02ADao extends IGenericDao<L140S02A> {

	L140S02A findByOid(String oid);

	List<L140S02A> findByMainId(String mainId);

	L140S02A findByUniqueKey(String mainId, Integer seq);

	List<L140S02A> findByIndex01(String mainId, Integer seq);

	/**
	 * 
	 * @param mainId
	 *            額度明細表mainId
	 * @param loanNo
	 *            放款帳號
	 * @return
	 */
	List<L140S02A> findByMainIdAndLoanNo(String mainId, String[] loanNo);

	List<L140S02A> findByCntrNo(String CntrNo);

	List<L140S02A> findByCustIdDupId(String custId, String DupNo);

	/**
	 * 核准流水序號
	 * 
	 * @param mainId
	 *            文件編號
	 * @param secNos
	 *            流水號
	 * @return
	 */
	List<L140S02A> findByMainIdAndSecNos(String mainId, Integer[] secNos);

	/**
	 * 取得流水號不為空的
	 * 
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	List<L140S02A> findByMainIdAndSecNosIsNotNull(String mainId);

	/**
	 * 取得產品
	 * 
	 * @param mainId
	 *            文件編號
	 * @param seqs
	 *            序號
	 * @return
	 */
	List<L140S02A> findByMainIdAndSeqs(String mainId, Integer[] seqs);

	/**
	 * 取得產品 舊案轉
	 * 
	 * @param mainId
	 *            文件編號
	 * @param creatSrc
	 *            文件來源
	 * @return
	 */
	List<L140S02A> findByMainIdAndCreatSrc(String mainId, String creatSrc);

	/**
	 * 取得產品 舊案轉
	 * 
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	List<L140S02A> findByMainIds(String[] mainIds);

	List<L140S02A> findByMainIdOrderBySeq(String mainId);

}