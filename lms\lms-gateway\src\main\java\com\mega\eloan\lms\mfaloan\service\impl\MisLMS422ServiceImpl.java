package com.mega.eloan.lms.mfaloan.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisLMS422Service;

@Service
public class MisLMS422ServiceImpl extends AbstractMFAloanJdbc implements
		MisLMS422Service {

	@Override
	public Map<String, Object> findLMS422ByCntrNo(String custId, String dupNo,
			String cntrNo, String brNo) {

		return this.getJdbc().queryForMap("LMS422.selByCntrNo",
				new Object[] { custId, dupNo, cntrNo, brNo });
	}

	

}
