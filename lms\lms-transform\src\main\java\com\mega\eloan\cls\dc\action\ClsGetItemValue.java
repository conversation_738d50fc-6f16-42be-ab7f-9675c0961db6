package com.mega.eloan.cls.dc.action;

import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.action.ParserDB2XML;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.base.IKeyChecker;
import com.mega.eloan.lms.dc.base.OccursHander;
import com.mega.eloan.lms.dc.base.OccursKeyChecker;
import com.mega.eloan.lms.dc.base.SelectKeyChecker;
import com.mega.eloan.lms.dc.base.XMLHandler;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.conf.CountyConfig;
import com.mega.eloan.lms.dc.conf.InsuranceConfig;
import com.mega.eloan.lms.dc.conf.TownConfig;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * ClsGetItemValue
 * </pre>
 * 
 * @since 2013/1/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/30,Bang,new
 *          </ul>
 */
public class ClsGetItemValue {
	private static final boolean DEBUG_MODE = false;
	private Logger logger = LoggerFactory.getLogger(ClsGetItemValue.class);

	private PrintWriter txtWrite;
	private PrintWriter LMSParserlogs = null;// 輸出log
	private PrintWriter xmlErrorLogs = null;// db2Xml屬性欄位值判斷log
	private PrintWriter dxlErrorLst = null;// dxl踢退清單
	private StringBuffer sbItemValue = new StringBuffer();
	private String dxlName = "";// 當前dxl檔名稱
	private String branch = "";// 分行名稱
	private String unid = "";
	private String linkId = "";
	private String keyId = "";
	private String strSeprate = ";";
	private String formName = "";
	private List<String> fieldList = new ArrayList<String>(); // DB2欄位名稱
	private int seqNo = 0;
	private int lthSeprate = strSeprate.length();
	private String occursTimesField = "";
	private String keyMan = null;// for itemType:keyman時使用,紀錄 notes.keyMan之值
	private String gutPercent = "";// for
									// fldName:gutPercent時使用,紀錄db2.gutPercent之值
	private String fFund = "";// for fldName:fFund時使用,紀錄db2.fFund之值
	@SuppressWarnings("unused")
	private String mJobKind = "";// for fldName:mJobKind時使用,紀錄db2.mJobKind之值
	@SuppressWarnings("unused")
	private String assisType = "";// for itemType:assisType時使用,紀錄
									// db2.assisType之值
	@SuppressWarnings("unused")
	private String excellent = "";// for itemType:assisType時使用,紀錄
									// notes.Excellent之值
	@SuppressWarnings("unused")
	private String lnSelect = "";// for 115.715
									// fldName:lnSelect時使用,紀錄db2.LNRange之值

	private HashMap<String, String> nameMap = new HashMap<String, String>();
	private HashMap<String, String> nullMap = new HashMap<String, String>();
	private HashMap<String, String> typeMap = new HashMap<String, String>();
	private HashMap<String, String> defaultMap = new HashMap<String, String>();
	@SuppressWarnings("unused")
	private HashMap<String, String> descMap = new HashMap<String, String>();
	private HashMap<String, String> numMap = new HashMap<String, String>();

	private IKeyChecker skeyChecker = new SelectKeyChecker();
	private XMLHandler xmlHandler = new XMLHandler();
	private IKeyChecker okeyChecker = null;
	private OccursHander occursHander = null;

	/**
	 * 合併dxl與DB2Xml
	 * 
	 * @param txtWrite
	 *            PrintWriter :準備輸出文字檔的物件
	 * @param db2Item
	 *            ParserDB2XML :DB2Xml轉換物件
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 * @param dxlPath
	 *            String :當前分行的dxl路徑
	 * @param dxlName
	 *            String :當前dxl檔名稱
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param branch
	 *            Sring :當前分行名稱
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param LMSParserlogs
	 *            PrintWriter :輸出log的物件
	 * @param xmlErrorLogs
	 *            PrintWriter :db2Xml屬性欄位值判斷log
	 * @param dxlErrorLst
	 *            PrintWriter :dxl踢退清單
	 */
	public void mainProcess(PrintWriter txtWrite, ParserDB2XML db2Item,
			String xmlName, String dxlPath, String dxlName, String dxlXml,
			String branch, Document domDoc, PrintWriter LMSParserlogs,
			PrintWriter xmlErrorLogs, PrintWriter dxlErrorLst) {
		try {
			this.txtWrite = txtWrite;
			this.dxlName = StringEscapeUtils.escapeXml(dxlName);
			this.branch = branch;
			this.LMSParserlogs = LMSParserlogs;
			this.xmlErrorLogs = xmlErrorLogs;
			this.dxlErrorLst = dxlErrorLst;
			long tt1 = System.currentTimeMillis();
			xmlName = StringEscapeUtils.escapeXml(xmlName);
			this.LMSParserlogs.print("---目前正在解析讀取:>" + xmlName
					+ ">資料...dxl檔名為 : >" + this.dxlName + ">");
			initParser(db2Item, xmlName);
			this.seqNo = 1;
			parserAll(dxlXml, domDoc, xmlName);
			this.LMSParserlogs.println("讀取 TOTAL TIME(微秒)===>"
					+ (System.currentTimeMillis() - tt1) + ">");
		} catch (Exception e) {
			String errmsg = this.branch
					+ " 分行 執行CLSGetItemValue之mainProcess發生錯誤:--DB2Xml名稱 : "
					+ xmlName + ", 當前dxl檔名稱 : " + this.dxlName + " 停止讀取";
			logger.error(errmsg, e);
			this.LMSParserlogs.println(errmsg);
			this.xmlErrorLogs.println(errmsg);
			e.printStackTrace(this.xmlErrorLogs);
			throw new DCException(this.branch + " 分行 執行" + errmsg, e);
		}
	}

	/**
	 * db2XML轉換為物件
	 * 
	 * @param db2Item
	 *            ParserDB2XML
	 * @param xmlName
	 *            String :DB2Xml檔名稱
	 */
	private void initParser(ParserDB2XML db2Item, String xmlName) {

		this.formName = db2Item.getFormName();
		this.nameMap = db2Item.getNameMap();
		this.nullMap = db2Item.getNullMap();
		this.typeMap = db2Item.getTypeMap();
		this.defaultMap = db2Item.getDefaultMap();
		this.descMap = db2Item.getDescMap();
		this.numMap = db2Item.getNumMap();
		this.fieldList = db2Item.getFieldList();

		int occursTimes = db2Item.getOccursTimes();
		String occursFields = db2Item.getOccursFields();
		if (StringUtils.isNotEmpty(occursFields)) {
			occursFields += ";";
		}

		String occursKeys = db2Item.getOccursKeys();
		if (StringUtils.isNotEmpty(occursKeys)) {
			occursKeys += ";";
		}
		String occursValues = db2Item.getOccursValues();
		String selectKeys = db2Item.getSelectKeys();
		String selectValues = db2Item.getSelectValues();

		this.skeyChecker.init(xmlHandler, selectKeys, selectValues);
		this.occursHander = new OccursHander(occursFields, occursKeys,
				occursTimes);
		this.okeyChecker = new OccursKeyChecker(this.occursHander);
		this.okeyChecker.init(xmlHandler, occursKeys, occursValues);

	}

	/**
	 * 
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param xmlName
	 *            String :DB2Xml名稱
	 */
	private void parserAll(String dxlXml, Document domDoc, String xmlName)
			throws Exception {
		String strBrk = "firstRecord";
		boolean bFirstRecord = true; // First Record flag,設計保留備用
		String[] f1 = this.dxlName.split("_");// EX:{FLMS120M01
												// ,2E3761E1BB971A2B48257A7D00143D9C.dxl}
		String[] f2 = f1[1].split(TextDefine.ATTACH_DXL); // EX:{2E3761E1BB971A2B48257A7D00143D9C}
		if (this.formName.equals(f1[0])) {
			if (!strBrk.equals(f2[0])) {
				strBrk = f2[0];
				this.seqNo = 1; // Form Name 有Break時,序號從 1 開始
			} else {
				this.seqNo++;
			} // 序號加 1
			parseDxl(dxlXml, domDoc, bFirstRecord, xmlName);
			bFirstRecord = false;
		}
	}

	/**
	 * 
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param domDoc
	 *            Document :已轉換為DOM Document的當前dxl檔
	 * @param bFirstRecord
	 * @param xmlName
	 *            String :DB2Xml名稱
	 */
	@SuppressWarnings("unused")
	private void parseDxl(String dxlXml, Document domDoc, boolean bFirstRecord,
			String xmlName) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = this.dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}

			// 處理欄位資料,同一筆資料沒有occurs時occursTimes是1,有occurs時occursTimes是occurs的次數
			String times = xmlHandler.getItemValue(domDoc,
					this.occursTimesField);
			int maxOccurs = this.occursHander.getOccursValue(times);
			// for log Used
			for (int i = 1; i <= maxOccurs; i++) {
				this.sbItemValue.setLength(0);
				this.linkId = xmlHandler.getItemValue(domDoc, "LinkID");
				this.keyId = xmlHandler.getItemValue(domDoc, "keyid");
				if (k2.length == 2) { // 主檔,如FCLS109M03
					this.unid = k2[1];

				} else { // 明細檔,如FCMS101S01
					this.unid = k2[2];// 明細檔之UNID
				}

				parseProcess(domDoc, dxlXml, i, xmlName, k2[0]);
			}
		} catch (Exception e) {
			String errmsg = this.branch
					+ " 分行 執行CLSGetItemValue之parseDxl發生錯誤:--DB2Xml名稱 : "
					+ xmlName + ", 當前dxl檔名稱 : " + this.dxlName + " 停止讀取";
			logger.error(errmsg, e);
			this.LMSParserlogs.println(errmsg);
			this.xmlErrorLogs.println(errmsg);
			e.printStackTrace(this.xmlErrorLogs);
			throw new DCException(this.branch + " 分行 執行" + errmsg, e);
		} finally {
			if (this.logger.isDebugEnabled() && DEBUG_MODE) {
				this.logger.debug("##### 處理 " + xmlName + "@" + this.dxlName
						+ " COST==>" + (System.currentTimeMillis() - t1));
			}
		}
	}

	/**
	 * 與DB2Xml對應後取得dxl內之值
	 * 
	 * @param domDoc
	 *            :dxl轉換為DOM XML後的xml檔
	 * @param dxlXml
	 *            String :已轉換為String型態之dxl檔
	 * @param occurs
	 *            int :occurs欄位目前執行次數(序號)
	 * @param xmlName
	 *            String :DB2Xml名稱
	 * @param dxlFromName
	 *            String :當前dxl檔對應的FormName名稱
	 */
	private void parseProcess(Document domDoc, String dxlXml, int occurs,
			String xmlName, String dxlFromName) throws Exception {
		if (this.skeyChecker.isUseChecker()) {
			if (!this.skeyChecker.check(domDoc, null)) {
				return;
			}
		}

		if (this.okeyChecker.isUseChecker()) {
			if (!this.okeyChecker.check(domDoc, occurs)) {
				return;
			}
		}

		String tmpXName = xmlName.split(".xml")[0];// FCLS106M01_L120M01A
		String tableName = tmpXName.split("_")[1];// L120M01A

		if ("L140S01A".equalsIgnoreCase(tableName)) {
			String keyMan = Util.trimSpace(xmlHandler.getItemValue(domDoc,
					"keyMan"));
			if ("Y".equals(keyMan)) {
				return;
			}
		}

		for (int i = 0, size = this.fieldList.size(); i < size; i++) {
			String fldName = this.fieldList.get(i);// db2欄位名稱
			String itemName = this.occursHander.chkOccurs(
					this.nameMap.get(fldName).trim(), occurs);// db2Xml中的name屬性欄位
			String itemNull = this.nullMap.get(fldName);
			String itemType = this.typeMap.get(fldName);
			String itemDefault = this.defaultMap.get(fldName);
			String numValue = this.numMap.get(fldName);
			int itemNum = Integer.parseInt(Util.isNumeric(numValue) ? numValue
					: "-1");
			String itemNum_separated = this.numMap.get(fldName);
			String itemValue = "";
			StringBuffer sbItem = new StringBuffer();
			sbItem.setLength(0);
			// step 1
			if ("oid".equalsIgnoreCase(fldName)
					// || "uid".equalsIgnoreCase(fldName)
					|| "mainId".equalsIgnoreCase(fldName)
					|| "refMainId".equalsIgnoreCase(fldName)) {
				if ("unid".equalsIgnoreCase(itemName)) {
					// 使用Form的unid
					itemValue = this.unid;
				} else if ("LinkID".equalsIgnoreCase(itemName)) {
					// 使用Document的LinkID
					itemValue = this.linkId;
				} else if ("LinkMainId".equalsIgnoreCase(itemName)) {
					// 使用LinkID前20碼+Sno_(x)
					String snoValue = xmlHandler.getItemValue(domDoc, "Sno_"
							+ occurs);
					// 若snoValue無值,該筆資料不需輸出
					if (StringUtils.isBlank(snoValue)) {
						return;
					} else {
						itemValue = this.getMainId(this.linkId, snoValue);
					}
				} else if ("keyMainId".equalsIgnoreCase(itemName)) {
					// 使用keyid前20碼+Sno
					String snoValue = this.xmlHandler.getItemValue(domDoc,
							"Sno");
					// 若snoValue無值,該筆資料不需輸出
					if (StringUtils.isBlank(snoValue)) {
						return;
					} else {
						// 若Sno不足12碼，則靠左，右邊補零到12碼
						if (snoValue.length() < 12) {
							snoValue = Util.addZeroWithValue(snoValue, 12);
						}
						itemValue = this.getMainId(this.keyId, snoValue);
					}
				} else if ("LinkSnoId".equalsIgnoreCase(itemName)) {
					// 使用LinkID前20碼+Sno(L140M01L,115(715)_L140S02A)
					String snoValue = this.xmlHandler.getItemValue(domDoc,
							"Sno");
					// 若snoValue無值,該筆資料不需輸出
					if (StringUtils.isBlank(snoValue)) {
						return;
					} else {
						// 若Sno不足12碼，則靠左，右邊補零到12碼
						if (snoValue.length() < 12) {
							snoValue = Util.addZeroWithValue(snoValue, 12);
						}
						// 若LinkID無值改用RPTDocID
						if (StringUtils.isBlank(this.linkId)) {
							String rptValue = this.xmlHandler.getItemValue(
									domDoc, "RPTDocID");
							itemValue = this.getMainId(rptValue, snoValue);
						} else {
							itemValue = this.getMainId(this.linkId, snoValue);
						}
					}
					// 簽報書的mainid取自審核書時，若審核書的RPTID有值，代表該案有簽報書，因此用RPTID為mainid，才能關聯到所有的額度明細表
					// 若RPTID無值，則用LINKID為MAINID
				} else if ("mainid_120".equalsIgnoreCase(itemName)) {
					String rptValue = this.xmlHandler.getItemValue(domDoc,
							"RPTID");
					if (StringUtils.isBlank(rptValue)) {
						itemValue = this.xmlHandler.getItemValue(domDoc,
								"LinkID");
					} else {
						itemValue = rptValue;
					}
				} else {
					if (StringUtils.isBlank(itemName)) {
						itemValue = Util.getOID();// 自行指定oid
					} else {
						itemValue = this.xmlHandler.getItemValue(domDoc,
								itemName);
					}
				}
				this.sbItemValue.append(itemValue + this.strSeprate);
				// 欄位值是否可為null之判斷
				checkNull(xmlName, fldName, itemName, itemValue, itemNull);
				if (itemValue.length() == 0) {
					logger.debug(this.dxlName + "的" + fldName
							+ "為空，此筆資料不parser");
					return;
				}
				continue;
			}

			// step 2 依對應name取回對應的itemvalue
			if (itemName != null && itemName.length() > 0) { // 讀取dxl檔案欄位值
				if ("memo".equalsIgnoreCase(itemType)) {
					int idx = itemName.indexOf("/");
					if (idx > 0) {
						String[] itemList = StringUtils.split(itemName, '/');
						for (String nl : itemList) {
							nl = this.occursHander.chkOccurs(nl.trim(), occurs);
							String tmp = this.xmlHandler
									.getItemValueByRichText(domDoc, nl);
							if (tmp != null && tmp.length() > 0) {
								itemValue = tmp.trim();
								break;
							}
						}
					} else {
						itemValue = this.xmlHandler.getItemValueByRichText(
								domDoc, itemName);
					}
				} else if ("multi-val".equalsIgnoreCase(itemType)) {
					// 特殊itemType:multi-val處理
					// <txtlst><txt>AAAA</txt><txt>BBBB</txt>...</txtlst>
					// 須顯示為 AAAA;BBBB ..... 中間用分隔符號表示
					itemValue = this.xmlHandler.getItemValueByMup(domDoc,
							itemName, itemNum_separated);
				} else {
					itemValue = getAllItem(domDoc, itemName, occurs);
				}
			}

			/*
			 * 2013-04-02 若Table為 L140S02H,db2欄位名為bankNo或branchNo:無值不轉，中文不轉
			 */
			if (tableName.equalsIgnoreCase("L140S02H")
					&& ("branchNo".equalsIgnoreCase(fldName) || "bankNo"
							.equalsIgnoreCase(fldName))) {
				if (StringUtils.isBlank(itemValue)) {
					return;
				} else if (Util.isChineseChr(itemValue)) {
					return;
				}
			}

			// step 4
			// 依fldName欄位名稱有條件取得Notes欄位之值(chkAllFldName要先做才能做接下來的事)
			ClsChkFldName ccfn = new ClsChkFldName();
			try {
				itemValue = ccfn.chkAllFldName(this.xmlHandler, domDoc,
						fldName, dxlFromName, tableName, itemValue, occurs);
			} catch (Exception ex) {
				this.writeErrorLog(xmlName, fldName, itemName, itemValue,
						"格式錯誤", ex);
			}
			// step 5
			// 針對type欄位指定的型態做必要的轉換
			ClsChkType cct = new ClsChkType();
			try {
				itemValue = cct
						.chkAllType(itemType, itemValue, itemNum, dxlXml);
			} catch (Exception ex) {
				this.writeErrorLog(xmlName, fldName, itemName, itemValue,
						"格式錯誤", ex);
			}

			// 序號 seq
			if ("seq".equalsIgnoreCase(itemType)) {
				itemValue = String.valueOf(occurs);
			}

			// 編製單位代號
			if ("ownBrId".equalsIgnoreCase(fldName)
					|| "branch".equalsIgnoreCase(fldName)) {
				if (StringUtils.isBlank(itemValue)) {
					itemValue = this.branch;
				}
			}

			// 保證成數(chkAllFldName要先做取值動作)
			if (tableName.equalsIgnoreCase("L140M01A")) {
				if ("gutPercent".equalsIgnoreCase(fldName)) {
					this.gutPercent = itemValue;
				}

				// 額度控管種類 :假如L140M01A.gutPercent不等於0則填入20否則皆為10
				if ("snoKind".equalsIgnoreCase(fldName)) {
					if (!this.gutPercent.equals("0")) {
						itemValue = "20";
					} else {
						itemValue = "10";
					}
				}
			}

			// type = branch,county,town,insId要讀取config檔轉換
			if ("branch".equalsIgnoreCase(itemType)
					|| "county".equalsIgnoreCase(itemType)
					|| "town".equalsIgnoreCase(itemType)
					|| "insId".equalsIgnoreCase(itemType)) {
				itemValue = getValueByConfig(xmlName, fldName, itemType,
						itemName, occurs, domDoc);
			}

			if (tableName.equalsIgnoreCase("L140S02A")) {
				// 資金來源 :fFund的來源資料太多種(一定要先跑完ClsChkType)
				if ("fFund".equalsIgnoreCase(fldName)) {
					this.fFund = itemValue;
				}
				// 資金來源小類DB2.dFund :假如DB2.fFund為0時則填101，其他則為null
				if ("dFund".equalsIgnoreCase(fldName)) {
					if ("0".equals(this.fFund)) {
						itemValue = "101";
					} else {
						itemValue = "";
					}
				}
			}

			// 特殊itemType:keyman處理
			if ("keyman".equalsIgnoreCase(itemName)
					&& "keyman".equalsIgnoreCase(itemType) && (0 == itemNum)) {
				// 01-18 : if notes.keyMan="Y"，then[0] ="Y" else if
				// notes.keyMan<>Y，則[0] = "N"
				if ("Y".equalsIgnoreCase(itemValue)) {
					this.keyMan = itemValue;
				} else {
					this.keyMan = "N";
					itemValue = "N";
				}
			}
			if ("custPos".equalsIgnoreCase(fldName)
					&& "keyman".equalsIgnoreCase(itemType) && (2 == itemNum)) {
				// if notes.keyMan="Y"，[2](即db2.custPos) =""
				if ("Y".equalsIgnoreCase(this.keyMan)) {
					itemValue = TextDefine.EMPTY_STRING;
				} else {
					// else notes.keyMan<>Y，
					// if notes.lngeflag_type(即db2.custPos)="" then
					// [2](即db2.custPos)="C"
					// else [2](即db2.custPos)
					// =notes.lngeflag_type(即db2.custPos)第1碼
					if (TextDefine.EMPTY_STRING.equals(itemValue)) {
						itemValue = "C";
					} else {
						itemValue = itemValue.substring(0, 1);
					}
				}
			}
			// 20130704 modify by Sandra 建霖提出比照企金加入條件
			if ("desp1".equalsIgnoreCase(fldName)
					&& tableName.equalsIgnoreCase("L140M01A")) {
				// 依以下條件判斷L140M01A.desp1的值
				// 若notes. UseDeadline(db2.useDeadline)=0 則desp1 = "不再動用"(註：字串)
				// 若notes. UseDeadline(db2.useDeadline)=1 則desp1 = notes.
				// FromDate~
				// notes.EndDate(註：字串，用~相接)
				// 若notes. UseDeadline(db2.useDeadline)=2 or 3 or 4 則desp1 =
				// notes.desp1
				// 若notes. UseDeadline(db2.useDeadline)=5 則desp1 = notes.desp2
				String _value = xmlHandler.getItemValue(domDoc, "UseDeadline");
				if ("0".equals(_value)) {
					itemValue = "不再動用";
				} else if ("1".equals(_value)) {
					itemValue = Util.parseDateToString(xmlHandler.getItemValue(
							domDoc, "FromDate"))
							+ "~"
							+ Util.parseDateToString(xmlHandler.getItemValue(
									domDoc, "EndDate"));
				} else if ("2".equals(_value) || "3".equals(_value)
						|| "4".equals(_value)) {
					itemValue = Util.getAllNumString(xmlHandler.getItemValue(
							domDoc, "Desp2"));// 20130509 與建霖重新確認改取desp2
				} else if ("5".equals(_value)) {
					itemValue = xmlHandler.getItemValue(domDoc, "Desp1");// 20130509
																			// 與建霖重新確認改取desp1
				}
			}

			// FCLS104S12 建物地址 特殊轉檔
			if ("build".equalsIgnoreCase(itemType)) {
				String[] targets = this.xmlHandler.getItemValueByMupArray(
						domDoc, "LArea");
				String[] bldnos = this.xmlHandler.getItemValueByMupArray(
						domDoc, "BN_1");
				JSONArray lst = new JSONArray();
				for (int k = 0; k < targets.length; k++) {
					String target = getData(targets, k);
					String bldno = getData(bldnos, k);
					// 有一個不是空白則新增一筆資料
					if (StringUtils.isNotBlank(target)
							|| StringUtils.isNotBlank(bldno)) {
						JSONObject json = new JSONObject();
						json.put("target", target);
						json.put("bldno", bldno);
						lst.add(json);
					}
				}
				if (lst.size() > 0) {
					itemValue = lst.toString();
				} else {
					itemValue = "";
				}
			}

			// FCLS104S12 土地地段 特殊轉檔
			if ("areaDetail".equalsIgnoreCase(itemType)) {
				String[] targets = this.xmlHandler.getItemValueByMupArray(
						domDoc, "LnArea_1");
				String[] landNos = this.xmlHandler.getItemValueByMupArray(
						domDoc, "LnNo_1");
				String[] ttlNums = this.xmlHandler.getItemValueByMupArray(
						domDoc, "cmsDoor_1");
				String[] ttlBals = this.xmlHandler.getItemValueByMupArray(
						domDoc, "cmsAmt_1");

				JSONArray lst = new JSONArray();
				for (int k = 0; k < targets.length; k++) {
					String target = getData(targets, k);
					String landNo = getData(landNos, k);
					String ttlNum = getData(ttlNums, k);
					String ttlBal = getData(ttlBals, k);
					// 有一個不是空白則新增一筆資料
					if (StringUtils.isNotBlank(target)
							|| StringUtils.isNotBlank(landNo)
							|| StringUtils.isNotBlank(ttlNum)
							|| StringUtils.isNotBlank(ttlBal)) {
						JSONObject json = new JSONObject();
						json.put("target", target);
						json.put("landNo", landNo);
						json.put("ttlNum", ttlNum);
						json.put("ttlBal", ttlBal);
						lst.add(json);
					}
				}

				if (lst.size() > 0) {
					itemValue = lst.toString();
				} else {
					itemValue = "";
				}
			}
			// step 3→移到最後
			// 無dxl檔案欄位值,設為預設值
			if (StringUtils.isBlank(itemValue)) {
				itemValue = itemDefault;
			}
			// -------------------------------------------------------------------------
			// 以下針對檢核後有問題的資料做處理
			// -------------------------------------------------------------------------

			// 2013/03/20 C120S01D ,C101S01D:
			// DB2欄位名稱:【mBirthday】
			// Notes欄位名稱:【m_date_1】格式不正確時轉為""
			if (tableName.equalsIgnoreCase("C120S01D")
					|| tableName.equalsIgnoreCase("C101S01D")) {
				if ("mBirthday".equalsIgnoreCase(fldName)) {
					if (itemValue.indexOf("ERR_") > -1) {
						itemValue = "";
					}
				}

				// 行業別(其他):行業別為99時該欄才有值
				if ("mJobOther".equalsIgnoreCase(fldName)) {
					String mJobKind = xmlHandler
							.getItemValue(domDoc, "jobkind");
					if (!"99".endsWith(mJobKind)) {
						itemValue = "";
					}
				}

			}

			// CDate日期格式錯誤之判斷
			if ("cdate".equalsIgnoreCase(itemType)) {
				if (StringUtils.isNotBlank(itemValue)) {
					if (itemValue.indexOf("ERR_") > -1) {
						this.writeErrorLog(xmlName, fldName, itemName,
								itemValue, "日期格式錯誤");
					}
				}
			}

			// 欄位值是否可為null之判斷
			checkNull(xmlName, fldName, itemName, itemValue, itemNull);

			this.sbItemValue.append(itemValue + this.strSeprate);
		} // end of for(int i = 0; i < listSize; i++)

		this.sbItemValue.setLength(this.sbItemValue.length() - this.lthSeprate);
		this.txtWrite.println(this.sbItemValue.toString());
	}

	/**
	 * 檢核內容是否為null
	 * 
	 * @param xmlName
	 * @param fldName
	 * @param itemName
	 * @param itemValue
	 * @param itemNull
	 */
	private void checkNull(String xmlName, String fldName, String itemName,
			String itemValue, String itemNull) {
		// 欄位值是否可為null之判斷
		if (itemNull.equalsIgnoreCase("N") && itemValue.length() == 0) {
			this.writeErrorLog(xmlName, fldName, itemName, itemValue,
					"其值不應為null");
		}
	}

	/**
	 * 取得組成後的MainId
	 * 
	 * @param linkKey
	 *            :LinkID或keyid之值
	 * @param snoValue
	 *            :(sno_當前序號) 之值
	 * @return String
	 */
	private String getMainId(String linkKey, String snoValue) throws Exception {
		// LinkID或keyid 前20碼+Sno_(x)
		if (linkKey.length() < 20) {
			return "";
		}
		return linkKey.substring(0, 20) + snoValue;

	}

	private String getAllItem(org.w3c.dom.Document domDoc, String itemName,
			int occurs) throws Exception {
		int idx = itemName.indexOf("/");

		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, '/');
			for (String nl : itemList) {
				// 20130528 modified by Sandra依Occurs取值
				nl = this.occursHander.chkOccurs(nl.trim(), occurs);
				String itemValue = this.xmlHandler.getItemValue(domDoc, nl);
				if (itemValue != null && itemValue.length() > 0) {
					return itemValue.trim();
				}
			}
			return "";
		}

		idx = itemName.indexOf(":");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, ':');
			StringBuffer sbValue = new StringBuffer();
			String newItemName = null;
			for (String nl : itemList) {
				newItemName = this.occursHander.chkOccurs(nl.trim(), occurs);
				sbValue.append(this.xmlHandler
						.getItemValue(domDoc, newItemName));
			}
			return sbValue.toString().trim();
		}

		idx = itemName.indexOf("+");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, '+');
			StringBuffer sbW1 = new StringBuffer();
			StringBuffer sbW2 = new StringBuffer();
			sbW1.append("0");
			for (String nl : itemList) {
				sbW2.setLength(0);
				sbW2.append(add(sbW1.toString(),
						this.xmlHandler.getItemValue(domDoc, nl)));
				sbW1.setLength(0);
				sbW1.append(sbW2.toString());
			}
			return sbW1.toString();
		}

		idx = itemName.indexOf(";");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, ';');
			StringBuffer sbValue = new StringBuffer();
			for (String nl : itemList) {
				sbValue.append(this.xmlHandler.getItemValue(domDoc, nl))
						.append(";");
			}
			return sbValue.toString().trim().replaceAll(";$", "");
		}
		// 2013/03/14 add By Bang :(L140S02J)欄位值為複選時使用 結果=>Ex: 1|2|3
		idx = itemName.indexOf("|");
		if (idx > 0) {
			String[] itemList = StringUtils.split(itemName, '|');
			StringBuffer sbValue = new StringBuffer();
			for (int i = 0; i < itemList.length; i++) {
				String value = this.xmlHandler
						.getItemValue(domDoc, itemList[i]);
				if (StringUtils.isNotBlank(value)) {
					sbValue.append(i + 1).append("|");
				}
			}
			return sbValue.toString().trim();
		}

		return this.xmlHandler.getItemValue(domDoc, itemName).trim();
	}

	/**
	 * 針對要呼叫config的部分進行轉換
	 * 
	 * @param itemType
	 * @param itemName
	 * @param occurs
	 * @param domDoc
	 * @return
	 */
	private String getValueByConfig(String xmlName, String fldName,
			String itemType, String itemName, int occurs, Document domDoc) {

		String itemValue = "";
		try {
			// 轉換分行代號branch
			if ("branch".equalsIgnoreCase(itemType)) {
				itemValue = BrnoConfig.getInstance().getBrNo(
						getAllItem(domDoc, itemName, occurs));
				if (DEBUG_MODE) {
					logger.debug("#brnoCode :: " + itemValue);
				}
			}

			// 轉換縣市代號county
			else if ("county".equalsIgnoreCase(itemType)) {
				String cityName = getAllItem(domDoc, itemName, occurs);
				if (StringUtils.isNotBlank(cityName)) {
					itemValue = CountyConfig.getInstance()
							.getCityCode(cityName);
					if (StringUtils.isBlank(itemValue)) {
						itemValue = "ERR_" + cityName;
					}
				}
				if (DEBUG_MODE) {
					logger.debug("#countyCode :: " + itemValue);
				}
			}

			// 轉換鄉鎮代號town
			else if ("town".equalsIgnoreCase(itemType)) {
				String townName = getAllItem(domDoc, itemName, occurs);
				if (StringUtils.isNotBlank(townName)) {
					itemValue = TownConfig.getInstance().getTownCode(townName);
					if (StringUtils.isBlank(itemValue)) {
						itemValue = "ERR_" + townName;
					}
				}
				if (DEBUG_MODE) {
					logger.debug("#townCode :: " + itemValue);
				}
			}

			// 轉換保險公司代號insId
			else if ("insId".equalsIgnoreCase(itemType)) {
				String insIdName = getAllItem(domDoc, itemName, occurs);
				if (StringUtils.isNotBlank(insIdName)) {
					itemValue = InsuranceConfig.getInstance().getInsuranceCode(
							insIdName);
					if (StringUtils.isBlank(itemValue)) {
						// itemValue = TextDefine.EMPTY_STRING;
						itemValue = "ERR_" + insIdName;
					}
				}
				if (DEBUG_MODE) {
					logger.debug("#insuranceCode :: " + itemValue);
				}
			}
		} catch (Exception e) {
			this.writeErrorLog(xmlName, fldName, itemName, itemValue,
					"其值不應為null");
		}
		return itemValue;
	}

	private String add(String v1, String v2) {
		if (v1 == null || v1.length() == 0) {
			v1 = "0";
		}
		if (v2 == null || v2.length() == 0) {
			v2 = "0";
		}
		BigDecimal b1 = new BigDecimal(v1);
		BigDecimal b2 = new BigDecimal(v2);
		return b1.add(b2).toString();
	}

	private void writeErrorLog(String xmlName, String fldName, String itemName,
			String itemValue, String errmsg) {
		this.writeErrorLog(xmlName, fldName, itemName, itemValue, errmsg, null);

	}

	private void writeErrorLog(String xmlName, String fldName, String itemName,
			String itemValue, String errmsg, Throwable ex) {
		String errstr = "分行名稱 【" + this.branch + "】;DXL名稱 【" + this.dxlName
				+ "】;" + "當前XML檔名稱 【" + xmlName + "】;" + "DB2欄位名稱 【" + fldName
				+ "】;" + "Notes欄位名稱 【" + itemName + "】;" + "欄位值 【" + itemValue
				+ "】;" + errmsg;
		this.xmlErrorLogs.println(errstr);

		// 寫入踢退清單
		this.dxlErrorLst.println(errstr);

		if (ex != null) {
			this.logger.error(errstr, ex);
		}
	}

	/**
	 * 取得資料
	 * 
	 * @param arys
	 * @param num
	 * @return
	 */
	private String getData(String[] arys, int num) {
		try {
			String value = arys[num].replaceAll(";", "；");
			return value;
		} catch (Exception e) {
			return "";
		}
	}
}