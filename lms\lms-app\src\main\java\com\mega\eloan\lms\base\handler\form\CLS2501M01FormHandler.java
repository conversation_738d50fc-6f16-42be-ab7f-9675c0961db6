/* 
 *  CLS2501M01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.ClsUtility;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.CLS2501M01Page;
import com.mega.eloan.lms.base.service.CLS2501Service;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C250M01A;
import com.mega.eloan.lms.model.C250M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 可疑代辦案件註記作業
 */
@Scope("request")
@Controller("cls2501m01formhandler")
@DomainClass(C250M01A.class)
public class CLS2501M01FormHandler extends AbstractFormHandler {

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	LMSService lmsService;

	@Resource
	BranchService branchService;

	@Resource
	CLS2501Service cls2501Service;

	@Resource
	MisELF500Service misELF500Service;

	@Resource
	UserInfoService userInfoService;

	/** 新增C250M01A 可疑代辦案件註記	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newC250M01A(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String c250m01aMainid = "";
		C250M01A c250m01a = new C250M01A();
		
		c250m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		c250m01a.setOwnBrId(user.getUnitNo());

		c250m01aMainid = IDGenerator.getUUID();
		c250m01a.setMainId(c250m01aMainid);
		String txCode = Util.trim(params.getString(EloanConstants.TRANSACTION_CODE));
		c250m01a.setTxCode(txCode);
		c250m01a.setDocURL(CapWebUtil.getDocUrl(CLS2501M01Page.class));
		c250m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		
		c250m01a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		c250m01a.setCustId(params.getString("custId"));
		c250m01a.setDupNo(params.getString("dupNo"));
		c250m01a.setCustName(params.getString("custName"));
		c250m01a.setCntrNo(params.getString("cntrNo"));
		
		c250m01a.setStatus(params.getString("status"));
		c250m01a.setLnflag(params.getString("lnflag"));	
		c250m01a.setLoanNo(params.getString("loanNo"));
		c250m01a.setYyyymm(params.getString("yyyymm"));
		
		c250m01a.setOthermemo(params.getString("othermemo"));
		c250m01a.setBranchComm(params.getString("branchComm"));		
	
		cls2501Service.save(c250m01a);

		CapAjaxFormResult result = new CapAjaxFormResult();
		return result.set(EloanConstants.OID, c250m01a.getOid());
	}

	/**
	 * 查詢C250M01A 可疑代辦案件註記
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryC250M01A(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		String ownBrId = "";
		Properties prop = MessageBundleScriptCreator.getComponentResource(CLS2501M01Page.class);
		
		if (!Util.isEmpty(oid)) {
			C250M01A c250m01a = cls2501Service.findModelByOid(C250M01A.class, oid);
			result = formatResultShow(result, c250m01a, page);
			result.putAll(DataParse.toResult(c250m01a, true));
			
			ownBrId = Util.trim(c250m01a.getOwnBrId());
			String ownBrName = Util.trim(branchService.getBranchName(ownBrId));
			if (!"".equals(ownBrName)) {
				result.set("ownBrIdName", ownBrName);
			}
			
			// 將文件狀態轉成中文
			if(Util.isNotEmpty(c250m01a.getDocStatus())){
				result.set("docStatus", this.getMessage("docStatus."+ c250m01a.getDocStatus()));	
			}
			
			String tStatus = Util.trim(c250m01a.getStatus());
			if(!"".equals(tStatus)){
				if ("A".equals(tStatus)) {
					result.set("status", prop.getProperty("C250M01A.statusA"));	
				} else if ("N".equals(tStatus)) {
					result.set("status", prop.getProperty("C250M01A.statusN"));	
				} else {
					result.set("status", tStatus);
				}				
			}
			
		} else {
			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
			result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
		}
		return result;
	}

	// /**
	// * 呈主管覆核(呈主管 主管覆核 拆2個method)
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		C250M01A c250m01a = (C250M01A) cls2501Service.findModelByOid(C250M01A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (!ArrayUtils.isEmpty(formSelectBoss)) {
			String manager = Util.trim(params.getString("manager"));
			List<C250M01E> models = (List<C250M01E>) cls2501Service.findListByMainId(C250M01E.class, c250m01a.getMainId());
			if (!models.isEmpty()) {
				cls2501Service.deleteC250M01Es(models, false);
			}
			List<C250M01E> c250m01es = new ArrayList<C250M01E>();
			for (String people : formSelectBoss) {
				C250M01E c250m01e = new C250M01E();
				c250m01e.setCreator(user.getUserId());
				c250m01e.setCreateTime(CapDate.getCurrentTimestamp());
				c250m01e.setMainId(c250m01a.getMainId());
				c250m01e.setBranchType(user.getUnitType());
				c250m01e.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				c250m01e.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				c250m01e.setStaffNo(people);
				c250m01es.add(c250m01e);
			}
			C250M01E managerC250M01E = new C250M01E();
			managerC250M01E.setCreator(user.getUserId());
			managerC250M01E.setCreateTime(CapDate.getCurrentTimestamp());
			managerC250M01E.setMainId(c250m01a.getMainId());
			managerC250M01E.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerC250M01E.setStaffNo(manager);
			managerC250M01E.setBranchType(user.getUnitType());
			managerC250M01E.setBranchId(user.getUnitNo());
			c250m01es.add(managerC250M01E);
			
			C250M01E apprC250M01E = new C250M01E();
			apprC250M01E.setCreator(user.getUserId());
			apprC250M01E.setCreateTime(CapDate.getCurrentTimestamp());
			apprC250M01E.setMainId(c250m01a.getMainId());
			apprC250M01E.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprC250M01E.setStaffNo(user.getUserId());
			apprC250M01E.setBranchType(user.getUnitType());
			apprC250M01E.setBranchId(user.getUnitNo());
			c250m01es.add(apprC250M01E);
			
			cls2501Service.saveC250M01EList(c250m01es);
		}
		Boolean upMis = false;
		C250M01E c250m01eL4 = new C250M01E();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			c250m01a.setApprover(user.getUserId());
			c250m01a.setApproveTime(CapDate.convertStringToTimestamp(params.getString("checkDate") + " 00:00:00"));
			upMis = true;
			C250M01E c250m01e = cls2501Service.findC250M01E(
					c250m01a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (c250m01e == null) {
				c250m01e = new C250M01E();
				c250m01e.setCreator(user.getUserId());
				c250m01e.setCreateTime(CapDate.getCurrentTimestamp());
				c250m01e.setMainId(c250m01a.getMainId());
				c250m01e.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				c250m01e.setStaffNo(user.getUserId());
				c250m01e.setBranchType(user.getUnitType());
				c250m01e.setBranchId(user.getUnitNo());
			}
			c250m01eL4 = c250m01e;
		}

		if (!Util.isEmpty(c250m01a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						C250M01E c250m01e = cls2501Service.findC250M01E(
								c250m01a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (c250m01e != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							cls2501Service.save(c250m01eL4);
							upMis = true;
						}
					}
				}
				cls2501Service.flowAction(c250m01a.getOid(), c250m01a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] cls250mService.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  cls250mService.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/**
	 * 儲存C250M01A 可疑代辦案件註記作業
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveC250M01A(PageParameters params)
			throws CapException {
		// lmsService.uploadELLNSEEK(new L120M01A());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		// String custName=Util.trim(params.getString("custName"));

		String formC250M01A = Util.trim(params.getString("CLS2501M01Form")); // 指定的form
		JSONObject jsonC250M01A = null;
		C250M01A c250m01a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			c250m01a = cls2501Service.findModelByOid(C250M01A.class, oid);		
			c250m01a.setRandomCode(IDGenerator.getRandomCode());
		}
		Properties pop = MessageBundleScriptCreator.getComponentResource(CLS2501M01Page.class);

		c250m01a.setDeletedTime(null);

		String validate = null;
		switch (page) {
		case 1:
			jsonC250M01A = JSONObject.fromObject(formC250M01A);
			DataParse.toBean(jsonC250M01A, c250m01a);
			validate = Util.validateColumnSize(c250m01a, pop, "C250M01A");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}
			cls2501Service.save(c250m01a);
			result.set("randomCode", c250m01a.getRandomCode());
			break;
		}

		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(c250m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c250m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(c250m01a.getMainId()));
		result.set("showTypCd", this.getMessage("typCd." + c250m01a.getTypCd()));
		result.set(
				"showCustId",
				CapString.trimNull(c250m01a.getCustId()) + " "
						+ CapString.trimNull(c250m01a.getDupNo()) + " "
						+ CapString.trimNull(c250m01a.getCustName()));
		return result;
	}

	/**
	 * 格式化顯示訊息
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			C250M01A c250m01a, Integer page) throws CapException {
		String mainId = c250m01a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(c250m01a);
			List<C250M01E> c250m01elist = (List<C250M01E>) cls2501Service
					.findListByMainId(C250M01E.class, mainId);
			if (!Util.isEmpty(c250m01elist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (C250M01E c250m01e : c250m01elist) {
					// 要加上人員代碼
					String type = Util.trim(c250m01e.getStaffJob());
					String userId = Util.trim(c250m01e.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(c250m01a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(c250m01a.getCreator()));
			result.set("updater", lmsService.getUserName(c250m01a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + c250m01a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case
		result.set("docStatusVal", c250m01a.getDocStatus());
		if (!Util.isEmpty(c250m01a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + c250m01a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + c250m01a.getTypCd()));
			result.set("showCustId", StrUtils.concat(c250m01a.getCustId()
					.toUpperCase(), " ", c250m01a.getDupNo().toUpperCase(),
					" ", c250m01a.getCustName()));
		}
		result.set("cntrNo", c250m01a.getCntrNo());
		result.set("randomCode", c250m01a.getRandomCode());
		result.set(EloanConstants.OID, CapString.trimNull(c250m01a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(c250m01a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(c250m01a.getMainId()));
		return result;
	}

	/**
	 * 刪除C250M01A 可疑代辦案件註記作業
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteC250M01A(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (cls2501Service.deleteC250M01As(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	/**
	 * 新增借款人資料
	 */
	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;

	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult fixELF516(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("oid");
		if(Util.isNotEmpty(oid)){
			C250M01A c250m01a = cls2501Service.findModelByOid(C250M01A.class, oid);
			if (c250m01a == null) {
				throw new CapMessageException(RespMsgHelper.getMessage(
						UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
			}
			
			int[] type = {java.sql.Types.CHAR, java.sql.Types.CHAR, java.sql.Types.CHAR, java.sql.Types.CHAR};
			
			List<Object[]> lst = new ArrayList<Object[]>();
			String branchComm = (Util.trimSizeInOS390(Util.trim(ClsUtility.commons_lang3_StringUtils_normalizeSpace(c250m01a.getBranchComm())), 400));
			String LNFLAG = Util.trim(c250m01a.getLnflag());
			String OTHERMEMO = "";
			if ("D".equals(LNFLAG)){
				OTHERMEMO = Util.trim(ClsUtility.commons_lang3_StringUtils_normalizeSpace(c250m01a.getOthermemo()));
				OTHERMEMO = (Util.trimSizeInOS390(OTHERMEMO, 202));
			}
			lst.add(new Object[]{OTHERMEMO, branchComm, c250m01a.getYyyymm(), c250m01a.getCntrNo()});
			
			//
			misdbBASEService.update(new Object[] { "MIS.ELF516", "ELF516_OTHERMEMO=?, ELF516_BRANCHCOMM=? ", "ELF516_YYYYMM=? and ELF516_CNTRNO=? " }, type, lst);
		}
		return result;
	}


	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getEjcicB29InquiryData(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		String b29Html = cls2501Service.getEjcicB29InquiryData(oid);
		result.set("b29Html", b29Html);

		return result;
	}
}
