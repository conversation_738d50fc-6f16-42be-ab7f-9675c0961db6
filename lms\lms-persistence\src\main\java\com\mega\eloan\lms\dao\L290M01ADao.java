/* 
 * C126M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;
import com.mega.eloan.lms.model.L290M01A;

/** ESG 綠色企業資料 **/
public interface L290M01ADao extends IGenericDao<L290M01A> {

	L290M01A findByOid(String oid);
	List<L290M01A> findByMainId(String mainId);
	List<L290M01A> findByReceiveDate(Date receiveDate);
	List<L290M01A> findByStkNo(String stkNo);
}