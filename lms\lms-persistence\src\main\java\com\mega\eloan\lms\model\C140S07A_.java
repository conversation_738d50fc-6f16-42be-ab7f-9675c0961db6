package com.mega.eloan.lms.model;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140S07A database table.
 * </pre>
 * @since  2011/10/04
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/10/04,TimChiang,new
 *          </ul>
 */
@StaticMetamodel(C140S07A.class)
public class C140S07A_ extends RelativeMeta_{
//	public static volatile SingularAttribute<C120S01C, String> crossFlag;
//	public static volatile SingularAttribute<C120S01C, Date> eDate;
//	public static volatile SingularAttribute<C120S01C, String> periodType;
//	public static volatile SingularAttribute<C120S01C, String> tradeType;
//	public static volatile SingularAttribute<C120S01C, Date> sDate;
//	public static volatile SingularAttribute<C120S01C, BigDecimal> sn;
//	public static volatile SingularAttribute<C120S01C, String> year;
//	public static volatile SingularAttribute<C120S01C, BigDecimal> exchange;
//	public static volatile SingularAttribute<C120S01C, String> curr;
//	public static volatile SingularAttribute<C120S01C, BigDecimal> amtUnit;
	public static volatile SingularAttribute<C140S07A, C140M07A> c140m07a;
}
