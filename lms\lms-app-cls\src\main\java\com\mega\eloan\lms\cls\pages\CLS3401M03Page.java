package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS3401S01Panel;
import com.mega.eloan.lms.cls.panels.CLS3401S09Panel;
import com.mega.eloan.lms.cls.panels.CLS3401S32Panel;
import com.mega.eloan.lms.cls.panels.CLS3401S33Panel;
import com.mega.eloan.lms.cls.panels.CLS3401S34Panel;
import com.mega.eloan.lms.model.C340M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
/**
 * <pre>
 * 消金其它貸款契約書(C340M01A_CtrType.Type_3)
 * </pre>
 * 
 * @since 2020/08/04
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/08/04,EL08034,J-109-0282
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls3401m03/{page}")
public class CLS3401M03Page extends AbstractEloanForm { //ref LMS2430M01Page

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CreditDocStatusEnum.海外_編製中));
		
		addAclLabel(model,
				new AclLabel("_btnWAIT_APPROVE", params, getDomainClass(),
						AuthType.Accept, CreditDocStatusEnum.海外_待覆核,
						CreditDocStatusEnum.先行動用_待覆核));
		
		renderJsI18N(CLS3401M03Page.class);
		// tabs
		int page = Util.parseInt(params.getString("page"));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2);
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}
	
	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS3401S01Panel(TAB_CTX, true);
			break;
		case 2:
			//=====================================
			//因為有用到一些原本 CLS3401M01Page 的 Panel.js
			//所以還是加入 CLS3401M01Page
			renderJsI18N(CLS3401M01Page.class);
			
			panel = new CLS3401S32Panel(TAB_CTX, true);
			break;
		case 3:
			panel = new CLS3401S33Panel(TAB_CTX, true);
			break;
		case 4:
			panel = new CLS3401S34Panel(TAB_CTX, true);
			break;
		case 9:
			//=====================================
			//因為有用到一些原本 CLS3401M01Page 的 Panel.js
			//所以還是加入 CLS3401M01Page
			renderJsI18N(CLS3401M01Page.class);
			
			panel = new CLS3401S09Panel(TAB_CTX, true);
			break;
		default:
			panel = new CLS3401S01Panel(TAB_CTX, true);
			break;
		}
		return panel;
	}
	
	
	@Override
	public Class<? extends Meta> getDomainClass() {
		return C340M01A.class;
	}
}
