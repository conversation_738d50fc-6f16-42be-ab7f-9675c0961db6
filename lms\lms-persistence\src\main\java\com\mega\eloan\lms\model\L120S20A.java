/* 
 * L120S20A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** LGD額度共用檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S20A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S20A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 簽報書MAINID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 共用序號 **/
	@Size(max=14)
	@Column(name="CNTRNOCO", length=14, columnDefinition="CHAR(14)")
	private String cntrNoCo;

	/** 共用額度幣別 **/
	@Size(max=3)
	@Column(name="CURRCO", length=3, columnDefinition="CHAR(3)")
	private String currCo;

	/** 共用額度限額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTCO", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtCo;

	/** 共用額度限額台幣 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="FACTAMTCOTWD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal factAmtCoTwd;

	/** 本案授信戶額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 現請額度幣別 **/
	@Size(max=3)
	@Column(name="CURRENTAPPLYCURR", length=3, columnDefinition="CHAR(3)")
	private String currentApplyCurr;

	/** 現請額度金額原幣 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CURRENTAPPLYAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 現請額度金額台幣 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CURRENTAPPLYAMTTWD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal currentApplyAmtTwd;

	/** 餘額幣別 **/
	@Size(max=3)
	@Column(name="BLCURR", length=3, columnDefinition="CHAR(3)")
	private String blCurr;

	/** 餘額金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="BLAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal blAmt;

	/** 餘額金額台幣 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="BLAMTTWD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal blAmtTwd;

	/** 應收利息等值台幣金額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="RCVINTTWD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal rcvIntTwd;

	/** 
	 * 佔比<p/>
	 * ％
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="RATIO", columnDefinition="DECIMAL(5,2)")
	private BigDecimal ratio;

	/** 
	 * 第一次分配結果<p/>
	 * 台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLOCATE1", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allocate1;

	/** 
	 * 分配後金額是否低於餘額<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="ISLOWERBAL", length=1, columnDefinition="CHAR(1)")
	private String isLowerBal;

	/** 
	 * 第二次分配結果<p/>
	 * 台幣
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLOCATE2", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allocate2;

	/** 分配後額度 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLOCATE3", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allocate3;

	/** 分配後額度序號額度 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="ALLOCATEF", columnDefinition="DECIMAL(17,2)")
	private BigDecimal allocateF;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得共用序號 **/
	public String getCntrNoCo() {
		return this.cntrNoCo;
	}
	/** 設定共用序號 **/
	public void setCntrNoCo(String value) {
		this.cntrNoCo = value;
	}

	/** 取得共用額度幣別 **/
	public String getCurrCo() {
		return this.currCo;
	}
	/** 設定共用額度幣別 **/
	public void setCurrCo(String value) {
		this.currCo = value;
	}

	/** 取得共用額度限額 **/
	public BigDecimal getFactAmtCo() {
		return this.factAmtCo;
	}
	/** 設定共用額度限額 **/
	public void setFactAmtCo(BigDecimal value) {
		this.factAmtCo = value;
	}

	/** 取得共用額度限額台幣 **/
	public BigDecimal getFactAmtCoTwd() {
		return this.factAmtCoTwd;
	}
	/** 設定共用額度限額台幣 **/
	public void setFactAmtCoTwd(BigDecimal value) {
		this.factAmtCoTwd = value;
	}

	/** 取得本案授信戶額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定本案授信戶額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得現請額度幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}
	/** 設定現請額度幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 取得現請額度金額原幣 **/
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}
	/** 設定現請額度金額原幣 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 取得現請額度金額台幣 **/
	public BigDecimal getCurrentApplyAmtTwd() {
		return this.currentApplyAmtTwd;
	}
	/** 設定現請額度金額台幣 **/
	public void setCurrentApplyAmtTwd(BigDecimal value) {
		this.currentApplyAmtTwd = value;
	}

	/** 取得餘額幣別 **/
	public String getBlCurr() {
		return this.blCurr;
	}
	/** 設定餘額幣別 **/
	public void setBlCurr(String value) {
		this.blCurr = value;
	}

	/** 取得餘額金額 **/
	public BigDecimal getBlAmt() {
		return this.blAmt;
	}
	/** 設定餘額金額 **/
	public void setBlAmt(BigDecimal value) {
		this.blAmt = value;
	}

	/** 取得餘額金額台幣 **/
	public BigDecimal getBlAmtTwd() {
		return this.blAmtTwd;
	}
	/** 設定餘額金額台幣 **/
	public void setBlAmtTwd(BigDecimal value) {
		this.blAmtTwd = value;
	}

	/** 取得應收利息等值台幣金額 **/
	public BigDecimal getRcvIntTwd() {
		return this.rcvIntTwd;
	}
	/** 設定應收利息等值台幣金額 **/
	public void setRcvIntTwd(BigDecimal value) {
		this.rcvIntTwd = value;
	}

	/** 
	 * 取得佔比<p/>
	 * ％
	 */
	public BigDecimal getRatio() {
		return this.ratio;
	}
	/**
	 *  設定佔比<p/>
	 *  ％
	 **/
	public void setRatio(BigDecimal value) {
		this.ratio = value;
	}

	/** 
	 * 取得第一次分配結果<p/>
	 * 台幣
	 */
	public BigDecimal getAllocate1() {
		return this.allocate1;
	}
	/**
	 *  設定第一次分配結果<p/>
	 *  台幣
	 **/
	public void setAllocate1(BigDecimal value) {
		this.allocate1 = value;
	}

	/** 
	 * 取得分配後金額是否低於餘額<p/>
	 * Y/N
	 */
	public String getIsLowerBal() {
		return this.isLowerBal;
	}
	/**
	 *  設定分配後金額是否低於餘額<p/>
	 *  Y/N
	 **/
	public void setIsLowerBal(String value) {
		this.isLowerBal = value;
	}

	/** 
	 * 取得第二次分配結果<p/>
	 * 台幣
	 */
	public BigDecimal getAllocate2() {
		return this.allocate2;
	}
	/**
	 *  設定第二次分配結果<p/>
	 *  台幣
	 **/
	public void setAllocate2(BigDecimal value) {
		this.allocate2 = value;
	}

	/** 取得分配後額度 **/
	public BigDecimal getAllocate3() {
		return this.allocate3;
	}
	/** 設定分配後額度 **/
	public void setAllocate3(BigDecimal value) {
		this.allocate3 = value;
	}

	/** 取得分配後額度序號額度 **/
	public BigDecimal getAllocateF() {
		return this.allocateF;
	}
	/** 設定分配後額度序號額度 **/
	public void setAllocateF(BigDecimal value) {
		this.allocateF = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
