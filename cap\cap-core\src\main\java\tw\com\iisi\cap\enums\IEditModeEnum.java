/**
 * IEditModeEnum.java
 *
 * Copyright (c) 2009 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.enums;

/**
 * <p>
 * 編輯的模式
 * </p>
 * 
 * <pre>
 * $Date: 2010-08-03 17:38:52 +0800 (星期二, 03 八月 2010) $
 * $Author: iris $
 * $Revision: 26 $
 * $HeadURL: svn://***********/MICB_ISDOC/cap/cap-core/src/main/java/tw/com/iisi/cap/enums/IEditModeEnum.java $
 * </pre>
 *
 * <AUTHOR>
 * @version $Revision: 26 $
 * @version
 *          <ul>
 *          <li>2010/7/20,iristu,new
 *          </ul>
 */
public enum IEditModeEnum {
    /**
     * 查詢
     */
    Search("Q"),

    /**
     * 新增
     */
    Add("A"),

    /**
     * 修改
     */
    Modify("M"),

    /**
     * 刪除
     */
    Delete("D"),

    /**
     * 儲存
     */
    Save("S"),

    /**
     * 更新
     */
    Update("U"),

    /**
     * 閱覽模式
     */
    ViewMode("view"),

    /**
     * 編輯模式
     */
    EditMode("edit");

    /**
     * 列舉內容
     */
    private String code;

    /**
     * 設置列舉內容
     * 
     * @param code
     */
    IEditModeEnum(String code) {
        this.code = code;
    }

    /**
     * 取得列舉內容
     * 
     * @return
     */
    public String getCode() {
        return code;
    }

}
