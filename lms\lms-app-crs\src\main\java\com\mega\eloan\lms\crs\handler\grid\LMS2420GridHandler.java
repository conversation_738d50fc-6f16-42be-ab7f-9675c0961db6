package com.mega.eloan.lms.crs.handler.grid;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.model.C242M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;


@Scope("request")
@Controller("lms2420gridhandler")
public class LMS2420GridHandler extends AbstractGridHandler {
	
	private static final DateFormat S_FORMAT = new SimpleDateFormat(UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);

	@Resource
	RetrialService retrialService;
	
	@Resource
	UserInfoService userInfoService;
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException, ParseException {
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			
			String schTimeBeg = Util.trim(jsoniletData.getString("schTimeBeg"));
			String schTimeEnd = Util.trim(jsoniletData.getString("schTimeEnd"));
			
			if (Util.isNotEmpty(schTimeBeg)) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				pageSetting.addSearchModeParameters(SearchMode.GREATER_EQUALS,
						"schTime", sdf.parse(schTimeBeg));
				pageSetting.addSearchModeParameters(SearchMode.LESS_EQUALS,
						"schTime", sdf.parse(schTimeEnd));
			}
		}
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", user.getUnitNo());	
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				null);
		
		List<C242M01A> src_list = (List<C242M01A>) retrialService.findPage(C242M01A.class, pageSetting).getContent();
		
		for(C242M01A c242m01a : src_list){
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, c242m01a, new String[]{ "oid"
					, "mainId"});
			row.put("approveTime", c242m01a.getApproveTime()==null?"":S_FORMAT.format(c242m01a.getApproveTime()));
			row.put("schTime", c242m01a.getSchTime()==null?"":S_FORMAT.format(c242m01a.getSchTime()));
			row.put("exeBrNo", StringUtils.join(CrsUtil.parse_exeParam(c242m01a.getExeParam()).keySet(), ","));
			row.put("creator", userInfoService.getUserName(c242m01a.getCreator()));
			row.put("updater", userInfoService.getUserName(c242m01a.getUpdater()));
			//---
			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

}
