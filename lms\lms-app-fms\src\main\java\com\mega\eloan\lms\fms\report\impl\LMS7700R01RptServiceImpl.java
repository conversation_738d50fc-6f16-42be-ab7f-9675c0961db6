package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS7700M01Page;
import com.mega.eloan.lms.fms.report.LMS7700R01RptService;
import com.mega.eloan.lms.fms.service.LMS7700Service;
import com.mega.eloan.lms.model.L140MM5A;
import com.mega.eloan.lms.model.L140MM5B;
import com.mega.eloan.lms.model.L140MM5C;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("lms7700r01rptservice")
public class LMS7700R01RptServiceImpl implements LMS7700R01RptService, FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS7700R01RptServiceImpl.class);
	
	@Resource
	BranchService branch;
	
	@Resource
	LMSService lmsService;
	
	@Resource
	LMS7700Service lms7700Service;
	
	@Resource
	CodeTypeService codetypeservice;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)  
			throws FileNotFoundException,IOException, Exception{
		LOGGER.info("into setReportData");
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS7700M01Page.class);
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS7700R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;
		
		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L140MM5A l140mm5a = null;
		List<L140MM5B> l140mm5blist = null;

		String branchName = null;
		try {
			l140mm5a = lms7700Service.findModelByOid(L140MM5A.class, mainOid);
			if (l140mm5a == null) {
				l140mm5a = new L140MM5A();
			}

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l140mm5blist = (List<L140MM5B>)lms7700Service.findListByMainId(L140MM5B.class, mainId);
			if (!Util.isEmpty(l140mm5blist) && Util.notEquals(l140mm5a.getDocStatus(),CreditDocStatusEnum.海外_編製中)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM5B l140mm5b : l140mm5blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm5b.getStaffJob());
					String userId = Util.trim(l140mm5b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {	//L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) {	//L3. 分行授信主管 
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {	//L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) {	//L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}				
			branchName = Util.nullToSpace(branch.getBranchName(Util.nullToSpace(l140mm5a.getOwnBrId())));

			String updater = Util.trim(l140mm5a.getUpdater()) + " " + 
								Util.trim(lmsService.getUserName(l140mm5a.getUpdater()));
			
			Map<String, CapAjaxFormResult> codeTypes = codetypeservice.findByCodeType(
					new String[] { "lms7700_reason"});
			
			List<Map<String, String>> rowData = new ArrayList<Map<String, String>>();
			
			List<L140MM5C> l140mm5clist = lms7700Service.findL140mm5csByMainId(mainId);
			if (l140mm5clist != null && l140mm5clist.size() > 0) {
				for (L140MM5C l140mm5c : l140mm5clist) {
					Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
					rptrowMap.put("ReportBean.column01", Util.trim(l140mm5c.getCustId()));
					rptrowMap.put("ReportBean.column02", Util.trim(l140mm5c.getDupNo()));
					rptrowMap.put("ReportBean.column03", Util.trim(l140mm5c.getCustName()));
					rptrowMap.put("ReportBean.column04", Util.trim(l140mm5c.getCes()));
					rptrowMap.put("ReportBean.column05", Util.trim(l140mm5c.getCms()));
					rptrowMap.put("ReportBean.column06", Util.trim(l140mm5c.getCol()));
					rptrowMap.put("ReportBean.column07", Util.trim(l140mm5c.getLms()));
					rptrowMap.put("ReportBean.column08", Util.trim(l140mm5c.getRps()));
					rptrowMap.put("ReportBean.column09", Util.trim(l140mm5c.getIsDelete()));
					rptrowMap.put("ReportBean.column10", Util.isEmpty(Util.trim(l140mm5c.getReason())) ? "" :
							Util.trim(codeTypes.get("lms7700_reason").get(l140mm5c.getReason())));
					rptrowMap.put("ReportBean.column11", Util.equals("1", Util.trim(l140mm5c.getDataFrom())) ? 
							prop.getProperty("sys") : prop.getProperty("user"));
					rptrowMap.put("ReportBean.column12", CapDate.formatDate(l140mm5c.getCloseDate(), "yyyy-MM-dd"));
					rowData.add(rptrowMap);
				}
			} else {
				Map<String, String> rptrowMap = new LinkedHashMap<String, String>();
				rptrowMap.put("ReportBean.column01", "");
				rowData.add(rptrowMap);
			}
			
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L140MM5A.Mainid", Util.trim(l140mm5a.getMainId()));
			rptVariableMap.put("L140MM5A.UPDATER", updater);
			rptVariableMap.put("L140MM5B.APPRID", apprid);
			rptVariableMap.put("L140MM5B.RECHECKID", recheckid);
			rptVariableMap.put("L140MM5B.BOSSID", bossid);
			rptVariableMap.put("L140MM5B.MANAGERID", managerid);
			
			String tDate = "";
			if (!"".equals(Util.trim(l140mm5a.getApproveTime()))){
				tDate = Util.trim(CapDate.formatDate(l140mm5a.getUpdateTime(),"yyyy-MM-dd")) + "(" + Util.trim(l140mm5a.getApproveTime()) + ")";
			} else {
				tDate = Util.trim(l140mm5a.getUpdateTime());
			}
			rptVariableMap.put("L140MM5A.DATE", tDate);
			
			rptVariableMap.put("L140MM5A.CDATE", Util.trim(CapDate.formatDate(
					l140mm5a.getCreateTime(),"yyyy-MM-dd")));
			rptVariableMap.put("L140MM5A.ADATE", Util.trim(CapDate.formatDate(
					l140mm5a.getApproveTime(),"yyyy-MM-dd")));
			
			rptVariableMap.put("L140MM5A.CNT", Integer.toString(l140mm5a.getCnt()));
			rptVariableMap.put("L140MM5A.RANDOMCODE", l140mm5a.getRandomCode());
			
			rptVariableMap.put("DataCount", Integer.toString(l140mm5clist.size()));
			
			// 子報表設定
			Map<String, String> map= new HashMap<String, String>();
			SubReportParam subReportParam = new SubReportParam();
			subReportParam.setData(0, map, rowData);			
			generator.setSubReportParam(subReportParam);
			
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}
}
