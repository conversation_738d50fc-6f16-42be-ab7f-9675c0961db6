/* 
 * JsI18nAjaxHandler.java
 * 
 * Copyright (c) 2021 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.handler;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.iisigroup.cap.utils.CapAppContext;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * 對來自Ajax的資料做i18n處理
 * </pre>
 * 
 * @since 2021年11月12日
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2021年11月12日,1104263,new
 *          </ul>
 */
@Controller("i18nhandler")
public class JsI18nAjaxHandler extends MFormHandler {

    /*
     * Get Operation Name
     * 
     * @see tw.com.iisi.cap.handler.FormHandler#getOperationName()
     */
    @Override
    public String getOperationName() {
        return "simpleOperation";
    }

    /**
     * 取得i18N
     * 
     * @param params
     *            前端傳入資料
     * @return
     * @throws CapException
     */
    public IResult getI18n(PageParameters params) throws CapException {
        CapAjaxFormResult result = new CapAjaxFormResult();
        result.putAll(CapAppContext.getMessages("JsI18nAjaxHandler", LocaleContextHolder.getLocale()));
        return result;
    }// ;

    /**
     * 切換地區
     * 
     * @param params
     *            前端傳入資料
     * @return
     * @throws CapException
     */
    public IResult switchLocale(PageParameters params) throws CapException {
        return new CapAjaxFormResult();
    }
}
