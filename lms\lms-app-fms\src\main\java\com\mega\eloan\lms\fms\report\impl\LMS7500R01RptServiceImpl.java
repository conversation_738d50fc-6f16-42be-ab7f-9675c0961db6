package com.mega.eloan.lms.fms.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.BranchTypeEnum;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.fms.pages.LMS7500M01Page;
import com.mega.eloan.lms.fms.report.LMS7500R01RptService;
import com.mega.eloan.lms.fms.service.LMS7500Service;
import com.mega.eloan.lms.model.L140MM3A;
import com.mega.eloan.lms.model.L140MM3B;
import com.mega.eloan.lms.model.L140MM3C;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;
import tw.com.jcs.common.report.SubReportParam;

@Service("lms7500r01rptservice")
public class LMS7500R01RptServiceImpl implements LMS7500R01RptService,
		FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS7500R01RptServiceImpl.class);

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;

	@Resource
	LMS7500Service lms7500Service;

	@Resource
	CodeTypeService codetypeservice;

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

	@SuppressWarnings("unchecked")
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		LOGGER.info("into setReportData");
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		Locale locale = null;
		locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		ReportGenerator generator = new ReportGenerator(
				"report/fms/LMS7500R01_" + locale.toString() + ".rpt");
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		OutputStream outputStream = null;

		String mainOid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L140MM3A l140mm3a = null;
		List<L140MM3B> l140mm3blist = null;

		String branchName = null;
		try {
			l140mm3a = lms7500Service.findModelByOid(L140MM3A.class, mainOid);
			if (l140mm3a == null) {
				l140mm3a = new L140MM3A();
			}

			String apprid = "";
			String recheckid = "";
			String bossid = "";
			String managerid = "";
			l140mm3blist = (List<L140MM3B>) lms7500Service.findListByMainId(
					L140MM3B.class, mainId);
			if (!Util.isEmpty(l140mm3blist)
					&& Util.notEquals(l140mm3a.getDocStatus(),
							CreditDocStatusEnum.海外_編製中)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM3B l140mm3b : l140mm3blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm3b.getStaffJob());
					String userId = Util.trim(l140mm3b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) { // L1. 分行經辦
						apprid = userId + " " + value;
					} else if ("L3".equals(type)) { // L3. 分行授信主管
						bossId.append(bossId.length() > 0 ? "\r\n" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) { // L4. 分行覆核主管
						recheckid = userId + " " + value;
					} else if ("L5".equals(type)) { // L5. 經副襄理
						managerid = userId + " " + value;
					}
				}
				bossid = bossId.toString();
			}
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(l140mm3a.getOwnBrId())));
			String custId = Util.nullToSpace(l140mm3a.getCustId()) + " "
					+ Util.nullToSpace(l140mm3a.getDupNo());
			String custname = Util.nullToSpace(l140mm3a.getCustName());
			String cntrno = Util.nullToSpace(l140mm3a.getCntrNo());
			String tDate = "";

			if (!"".equals(Util.trim(l140mm3a.getApproveTime()))) {
				tDate = Util.trim(l140mm3a.getUpdateTime()) + "("
						+ Util.trim(l140mm3a.getApproveTime()) + ")";
			} else {
				tDate = Util.trim(l140mm3a.getUpdateTime());
			}

			String cntrNoBrid = "";
			cntrNoBrid = cntrno.substring(0, 3);
			String brType = branch.getBranch(cntrNoBrid).getUnitType();
			if (BranchTypeEnum.海外分行.isEquals(brType)
					|| BranchTypeEnum.海外總行澳洲加拿大.isEquals(brType)
					|| BranchTypeEnum.海外總行泰國.isEquals(brType)
					|| BranchTypeEnum.大陸分行.isEquals(brType)
					|| BranchTypeEnum.海外分行當地有總行.isEquals(brType)) {// 海外分行
				if (Util.notEquals((l140mm3a.getCntrNoChkExistFlag()), "")) {
					rptVariableMap.put("L140MM3A.cntrNoChkExistFlagDscr", this
							.showYNPic(l140mm3a.getCntrNoChkExistFlag(), prop,
									2));
				} else {
					rptVariableMap.put("L140MM3A.cntrNoChkExistFlagDscr", "");
				}
			} else {
				rptVariableMap.put("L140MM3A.cntrNoChkExistFlagDscr", "");
			}

			// 不動產暨72-2相關資訊註記
			rptVariableMap.put("L140MM3A.UPDATEITEM2",
					Util.trim(l140mm3a.getUpdateItem2()));

			Map<String, String> this722 = new HashMap<String, String>();
			Map<String, String> last722 = new HashMap<String, String>();
			if (Util.notEquals((l140mm3a.getIs722Flag()), "")) {
				this722.put("L140MM3A.is722FlagDscr",
						this.showYNPic(l140mm3a.getIs722Flag(), prop, 2));
			} else {
				this722.put("L140MM3A.is722FlagDscr", "");
			}
			if (Util.notEquals((l140mm3a.getIs722OnFlag()), "")) {
				last722.put("L140MM3A.is722OnFlagDscr",
						this.showYNPic(l140mm3a.getIs722OnFlag(), prop, 2));
			} else {
				last722.put("L140MM3A.is722OnFlagDscr", "");
			}

			if (Util.notEquals((l140mm3a.getIsBuy()), "")) {
				this722.put("L140MM3A.isBuyDscr",
						this.showYNPic(l140mm3a.getIsBuy(), prop, 2));
			} else {
				this722.put("L140MM3A.isBuyDscr", "");
			}
			if (Util.notEquals((l140mm3a.getIsBuyOn()), "")) {
				last722.put("L140MM3A.isBuyOnDscr",
						this.showYNPic(l140mm3a.getIsBuyOn(), prop, 3));
			} else {
				last722.put("L140MM3A.isBuyOnDscr", "");
			}

			if (Util.notEquals((l140mm3a.getIsInstalment()), "")) {
				this722.put("L140MM3A.isInstalmentDscr",
						this.showYNPic(l140mm3a.getIsInstalment(), prop, 2));
			} else {
				this722.put("L140MM3A.isInstalmentDscr", "");
			}
			if (Util.notEquals((l140mm3a.getIsInstalmentOn()), "")) {
				last722.put("L140MM3A.isInstalmentOnDscr",
						this.showYNPic(l140mm3a.getIsInstalmentOn(), prop, 2));
			} else {
				last722.put("L140MM3A.isInstalmentOnDscr", "");
			}

			// this722.put("L140MM3A.lnType",
			// Util.nullToSpace(l140mm3a.getLnType()));
			// this722.put("L140MM3A.adcCaseNo",
			// Util.nullToSpace(l140mm3a.getAdcCaseNo()));

			List<Map<String, String>> this722titleRows = new LinkedList<Map<String, String>>();
			List<Map<String, String>> last722titleRows = new LinkedList<Map<String, String>>();

			List<L140MM3C> currentL140mm3c = lms7500Service
					.findCurrentL140mm3cs(l140mm3a.getMainId());
			List<L140MM3C> lastL140mm3c = lms7500Service
					.findLastL140mm3cs(l140mm3a.getMainId());

			if (currentL140mm3c != null && currentL140mm3c.size() > 0) {
				for (int i = 1; i <= currentL140mm3c.size(); i++) {
					Map<String, String> rowData = new LinkedHashMap<String, String>();

					L140MM3C current = currentL140mm3c.get((i - 1));

					if (i == 1) {
						rowData.put("ReportBean.column03", "Y");
					} else {
						// "不動產暨72-2相關資訊註記" 表頭只印第一次
						rowData.put("ReportBean.column03", "N");
					}

					if (current != null) {
						String txt = this.convertL140MM3cString(current);
						rowData.put("ReportBean.column01", txt);
					} else {
						rowData.put("ReportBean.column01", "");
					}

					rowData.put("ReportBean.column02", i + ".");

					this722titleRows.add(rowData);
				}
			} else {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				rowData.put("ReportBean.column02", "無");
				rowData.put("ReportBean.column03", "Y");
				rowData.put("ReportBean.column01", "");
				this722titleRows.add(rowData);
			}

			if (lastL140mm3c != null && lastL140mm3c.size() > 0) {
				for (int i = 1; i <= lastL140mm3c.size(); i++) {
					Map<String, String> rowData = new LinkedHashMap<String, String>();

					L140MM3C last = lastL140mm3c.get((i - 1));

					if (i == 1) {
						rowData.put("ReportBean.column03", "Y");
					} else {
						// "不動產暨72-2相關資訊註記" 表頭只印第一次
						rowData.put("ReportBean.column03", "N");
					}

					if (last != null) {
						String txt = this.convertL140MM3cString(last);
						rowData.put("ReportBean.column01", txt);
					} else {
						rowData.put("ReportBean.column01", "");
					}

					rowData.put("ReportBean.column02", i + ".");
					last722titleRows.add(rowData);
				}
			} else {
				Map<String, String> rowData = new LinkedHashMap<String, String>();
				rowData.put("ReportBean.column02", "無");
				rowData.put("ReportBean.column03", "Y");
				rowData.put("ReportBean.column01", "");
				last722titleRows.add(rowData);
			}

			// 暴險註記
			rptVariableMap.put("L140MM3A.UPDATEITEM3",
					Util.trim(l140mm3a.getUpdateItem3()));

			Map<String, String> thisSpecialFinRisk = new HashMap<String, String>();
			Map<String, String> lastSpecialFinRisk = new HashMap<String, String>();

			boolean needSpecialFinRisk = lms7500Service
					.needShowIsSpecialFinRisk(l140mm3a);

			// 前次
			if (true) {

				String isCmsAdcRisk = Util.trim(l140mm3a.getIsCmsAdcRiskOn());

				if (needSpecialFinRisk) {
					String isSpecialFinRisk = Util.trim(l140mm3a
							.getIsSpecialFinRiskOn());
					String specialFinRiskType = Util.trim(l140mm3a
							.getSpecialFinRiskTypeOn());
					if (Util.notEquals(isSpecialFinRisk, "")) {
						lastSpecialFinRisk.put("L140M01A.ISSPECIALFINRISK",
								isSpecialFinRisk);
						lastSpecialFinRisk.put("L140M01A.ISSPECIALFINRISKDSCR",
								this.showYNPic4(isSpecialFinRisk, prop));

						if (Util.equals(isSpecialFinRisk, "Y")) {

							// 特殊融資暴險類別
							if (Util.notEquals(specialFinRiskType, "")) {
								Map<String, String> lms140_specialFinRiskType = codetypeservice
										.findByCodeType(
												"lms140_specialFinRiskType",
												locale.toString());
								lastSpecialFinRisk
										.put("L140M01A.SPECIALFINRISKTYPE",
												(specialFinRiskType + "." + lms140_specialFinRiskType
														.get(specialFinRiskType)));

								// J-110-0382_05097_B1001 Web
								// e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
								if (Util.equals(specialFinRiskType, "1")) {
									// 專案融資
									String isProjectFinOperateStag = Util
											.trim(l140mm3a
													.getIsProjectFinOperateStagOn());
									lastSpecialFinRisk.put(
											"L140M01A.ISPROJECTFINOPERATESTAG",
											isProjectFinOperateStag);
									lastSpecialFinRisk
											.put("L140M01A.ISPROJECTFINOPERATESTAGDSCR",
													this.showYNPic4(
															isProjectFinOperateStag,
															prop));
									// J-112-0417 新增高品質專案融資分類
									String isHighQualityProjResult = Util.trim(l140mm3a.getIsHighQualityProjResultOn());
									String isHighQualityProjResultScr = "";
									if("Y".equals(isHighQualityProjResult)){
										isHighQualityProjResultScr = prop.getProperty("L140M01a.hqProjResHighQualityProjectFinace") 
										+ prop.getProperty("L140M01a.hqProjResProject_desc")
										+ " 80% "
										+ prop.getProperty("L140M01a.itemC");
									} else{
										isHighQualityProjResultScr = prop.getProperty("L140M01a.hqProjResProjectFinace") 
										+ prop.getProperty("L140M01a.hqProjResProject_desc")
										+ " 100% "
										+ prop.getProperty("L140M01a.itemC");
									}
									lastSpecialFinRisk.put("L140M01A.ISHIGHQUALITYPROJRESULT", isHighQualityProjResult);
									lastSpecialFinRisk.put("L140M01A.ISHIGHQUALITYPROJRESULTSCR", isHighQualityProjResultScr);
								}

							}
						}

					}
				}

				if (Util.notEquals(isCmsAdcRisk, "")) {
					lastSpecialFinRisk.put("L140M01A.ISCMSADCRISK",
							isCmsAdcRisk);
					lastSpecialFinRisk.put("L140M01A.ISCMSADCRISKDSCR",
							this.showYNPic4(isCmsAdcRisk, prop));
				}
			}

			// 本次
			if (true) {

				String isCmsAdcRisk = Util.trim(l140mm3a.getIsCmsAdcRisk());

				if (needSpecialFinRisk) {
					String isSpecialFinRisk = Util.trim(l140mm3a
							.getIsSpecialFinRisk());
					String specialFinRiskType = Util.trim(l140mm3a
							.getSpecialFinRiskType());
					if (Util.notEquals(isSpecialFinRisk, "")) {
						thisSpecialFinRisk.put("L140M01A.ISSPECIALFINRISK",
								isSpecialFinRisk);
						thisSpecialFinRisk.put("L140M01A.ISSPECIALFINRISKDSCR",
								this.showYNPic4(isSpecialFinRisk, prop));

						if (Util.equals(isSpecialFinRisk, "Y")) {

							// 特殊融資暴險類別
							if (Util.notEquals(specialFinRiskType, "")) {
								Map<String, String> lms140_specialFinRiskType = codetypeservice
										.findByCodeType(
												"lms140_specialFinRiskType",
												locale.toString());
								thisSpecialFinRisk
										.put("L140M01A.SPECIALFINRISKTYPE",
												(specialFinRiskType + "." + lms140_specialFinRiskType
														.get(specialFinRiskType)));

								// J-110-0382_05097_B1001 Web
								// e-Loan國內與海外企金授信新增「BIS信用風險標準法/內評法」相關欄位
								if (Util.equals(specialFinRiskType, "1")) {
									// 專案融資
									String isProjectFinOperateStag = Util
											.trim(l140mm3a
													.getIsProjectFinOperateStag());
									thisSpecialFinRisk.put(
											"L140M01A.ISPROJECTFINOPERATESTAG",
											isProjectFinOperateStag);
									thisSpecialFinRisk
											.put("L140M01A.ISPROJECTFINOPERATESTAGDSCR",
													this.showYNPic4(
															isProjectFinOperateStag,
															prop));
									
									// J-112-0417 新增高品質專案融資分類
									String isHighQualityProjResult = Util.trim(l140mm3a.getIsHighQualityProjResult());
									String isHighQualityProjResultScr = "";
									if("Y".equals(isHighQualityProjResult)){
										isHighQualityProjResultScr = prop.getProperty("L140M01a.hqProjResHighQualityProjectFinace") 
										+ prop.getProperty("L140M01a.hqProjResProject_desc")
										+ " 80% "
										+ prop.getProperty("L140M01a.itemC");
									} else{
										isHighQualityProjResultScr = prop.getProperty("L140M01a.hqProjResProjectFinace") 
										+ prop.getProperty("L140M01a.hqProjResProject_desc")
										+ " 100% "
										+ prop.getProperty("L140M01a.itemC");
									}
									thisSpecialFinRisk.put("L140M01A.ISHIGHQUALITYPROJRESULT", isHighQualityProjResult);
									thisSpecialFinRisk.put("L140M01A.ISHIGHQUALITYPROJRESULTSCR", isHighQualityProjResultScr);
								}

							}
						}

					}
				}

				if (Util.notEquals(isCmsAdcRisk, "")) {
					thisSpecialFinRisk.put("L140M01A.ISCMSADCRISK",
							isCmsAdcRisk);
					thisSpecialFinRisk.put("L140M01A.ISCMSADCRISKDSCR",
							this.showYNPic4(isCmsAdcRisk, prop));
				}
			}

			List<Map<String, String>> thisSpecialFinRisktitleRows = new LinkedList<Map<String, String>>();
			List<Map<String, String>> lastSpecialFinRisktitleRows = new LinkedList<Map<String, String>>();

			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			Map<String, String> thisAdcInfo = new HashMap<String, String>();
			Map<String, String> lastAdcInfo = new HashMap<String, String>();
			List<Map<String, String>> thisAdcInfotitleRows = new LinkedList<Map<String, String>>();
			List<Map<String, String>> lastAdcInfotitleRows = new LinkedList<Map<String, String>>();

			// 暴險註記
			rptVariableMap.put("L140MM3A.UPDATEITEM4",
					Util.trim(l140mm3a.getUpdateItem4()));

			Map<String, String> lnTypeMap = codetypeservice.findByCodeType(
					"lms140_lnType", LMSUtil.getLocale().toString());

			// 前次
			lastAdcInfo.put(
					"L140MM3A.lnTypeOn",
					(Util.isEmpty(l140mm3a.getLnTypeOn()) ? "00" : Util
							.nullToSpace(l140mm3a.getLnTypeOn())));
			lastAdcInfo.put(
					"L140MM3A.lnTypeDscrOn",
					Util.isEmpty(l140mm3a.getLnTypeOn()) ? "N.A." : Util
							.nullToSpace(lnTypeMap.get(Util
									.nullToSpace(l140mm3a.getLnTypeOn()))));
			lastAdcInfo.put("L140MM3A.adcCaseNoOn",
					Util.nullToSpace(l140mm3a.getAdcCaseNoOn()));

			// 本次
			thisAdcInfo.put("L140MM3A.lnType",
					Util.nullToSpace(l140mm3a.getLnType()));
			thisAdcInfo.put("L140MM3A.lnTypeDscr", Util.nullToSpace(lnTypeMap
					.get(Util.nullToSpace(l140mm3a.getLnType()))));
			thisAdcInfo.put("L140MM3A.adcCaseNo",
					Util.nullToSpace(l140mm3a.getAdcCaseNo()));
			
			
			Map<String, String> exceptFlagLastTime = new HashMap<String, String>();
			Map<String, String> exceptFlagThisTime = new HashMap<String, String>();
			List<Map<String, String>> exceptFlagLastTimeTitleRows = new LinkedList<Map<String, String>>();
			List<Map<String, String>> exceptFlagThisTimeTitleRows = new LinkedList<Map<String, String>>();
			
			//前次
			if (!"".equals(l140mm3a.getExceptFlagOn()) && !"_".equals(l140mm3a.getExceptFlagOn())) {
				
				exceptFlagLastTime.put(
						"L140MM3A.exceptDescription",
						prop.getProperty("L140M01a.exceptFlag_" + l140mm3a.getExceptFlagOn(), ""));
				
				String exceptFlagQA = prop.getProperty("L140M01a.exceptFlag_Q" + l140mm3a.getExceptFlagQAisYOn(), "");
				if (Util.isNotEmpty(exceptFlagQA)) {
					exceptFlagQA = this.showYNPic4("Y", prop) + " " + exceptFlagQA;
				}
				exceptFlagLastTime.put("L140MM3A.exceptFlagQA", exceptFlagQA);
				
			} 
			else {
				exceptFlagLastTime.put("L140M01A.exceptDescription", "");
			}
			
			//本次
			if (!"".equals(l140mm3a.getExceptFlagQAisY())) {
				
				if(CapString.isEmpty(l140mm3a.getExceptFlag())){
					exceptFlagThisTime.put("L140MM3A.exceptDescription", "N.A.");
				}
				
				if(Util.isNotEmpty(l140mm3a.getExceptFlag())){
					exceptFlagThisTime.put(
							"L140MM3A.exceptDescription",
							prop.getProperty("L140M01a.exceptFlag_" + l140mm3a.getExceptFlag(), ""));
				}
				
				String exceptFlagQA = prop.getProperty("L140M01a.exceptFlag_Q" + l140mm3a.getExceptFlagQAisY(), "");
				if (Util.isNotEmpty(exceptFlagQA)) {
					exceptFlagQA = this.showYNPic4("Y", prop) + " " + exceptFlagQA;
				}
				exceptFlagThisTime.put("L140MM3A.exceptFlagQA", exceptFlagQA);
			} 

			// 子報表設定
			SubReportParam subReportParam = new SubReportParam();
			subReportParam.setData(0, last722, last722titleRows);
			subReportParam.setData(1, this722, this722titleRows);
			subReportParam.setData(2, lastSpecialFinRisk,
					lastSpecialFinRisktitleRows);
			subReportParam.setData(3, thisSpecialFinRisk,
					thisSpecialFinRisktitleRows);
			// J-111-0633_05097_B1001 Web
			// e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
			subReportParam.setData(4, lastAdcInfo, lastAdcInfotitleRows);
			subReportParam.setData(5, thisAdcInfo, thisAdcInfotitleRows);
			
			subReportParam.setData(6, exceptFlagLastTime, exceptFlagLastTimeTitleRows);
			subReportParam.setData(7, exceptFlagThisTime, exceptFlagThisTimeTitleRows);

			generator.setSubReportParam(subReportParam);

			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("L140MM3A.Mainid",
					Util.trim(l140mm3a.getMainId()));
			rptVariableMap.put("L140MM3A.CUSTID", custId);
			rptVariableMap.put("L140MM3A.CUSTNAME", custname);
			rptVariableMap.put("L140MM3A.CNTRNO", cntrno);
			rptVariableMap.put(
					"L140MM3A.UPDATER",
					Util.trim(l140mm3a.getUpdater())
							+ " "
							+ Util.trim(lmsService.getUserName(l140mm3a
									.getUpdater())));
			rptVariableMap.put("L140MM3B.APPRID", apprid);
			rptVariableMap.put("L140MM3B.RECHECKID", recheckid);
			rptVariableMap.put("L140MM3B.BOSSID", bossid);
			rptVariableMap.put("L140MM3B.MANAGERID", managerid);
			rptVariableMap.put("L140MM3B.TITLEMARK", "");
			rptVariableMap.put("L140MM3A.DATE", tDate);

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setLang(locale);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}

	private String convertL140MM3cString(L140MM3C data) {

		Map<String, CapAjaxFormResult> estateCodeTypes = codetypeservice
				.findByCodeType(new String[] { "estateType", "estateSubType",
						"estateStatus", "buildWay" });
		StringBuffer sb = new StringBuffer();
		String estateType = data.getEstateType();

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);

		if (UtilConstants.L140M01T_estatType.都更危老.equals(estateType)) {

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			String estateSubType = data.getEstateSubType();
			sb.append(prop.getProperty("L140M01T.estateSubType.001")).append(
					estateCodeTypes.get("estateSubType").get(estateSubType));
			if (estateSubType.equals(UtilConstants.L140M01T_estatSubType.都更)
					|| estateSubType
							.equals(UtilConstants.L140M01T_estatSubType.危老)
					|| estateSubType
							.equals(UtilConstants.L140M01T_estatSubType.其它都更危老)) {
				if (estateSubType
						.equals(UtilConstants.L140M01T_estatSubType.其它都更危老)) {
					sb.append("(" + data.getSubTypeNote() + ")");
				}
				sb.append("\r\n");

				if (Util.isNotEmpty(data.getmCntrNo())) {
					sb.append(prop.getProperty("L140M01T.mCntrNo")).append(
							data.getmCntrNo() + "\r\n");
				}

				CodeType counties = codetypeservice.findByTypeAndDesc3(
						"counties", data.getEstateCityId());

				if (counties != null) {
					CodeType counties2 = codetypeservice.findByTypeAndDesc2(
							"counties" + counties.getCodeValue(),
							data.getEstateAreaId());

					if (counties2 != null) {
						String site3 = lmsService.l140m01msite3(
								Util.trim(counties.getCodeDesc2()),
								Util.trim(data.getEstateAreaId()),
								Util.trim(data.getEstateSit3No()),
								Util.trim(data.getEstateSit4No()));

						sb.append(prop.getProperty("L140M01T.address")).append(
								counties.getCodeDesc()
										+ counties2.getCodeDesc() + site3
										+ "\r\n");
					}

				}
				sb.append(prop.getProperty("L140M01T.status")).append(
						estateCodeTypes.get("estateStatus").get(
								data.getEstateStatus()));
				sb.append("\r\n");
				sb.append(prop.getProperty("L140M01T.buildWay"))
						.append(estateCodeTypes.get("buildWay").get(
								data.getBuildWay()));
				if ("02".equals(data.getBuildWay())
						|| "03".equals(data.getBuildWay())) {
					sb.append("，" + prop.getProperty("L140M01T.landlordNum"))
							.append(Util.trim(data.getLandlordNum()));
				}
				//J-112-0460_12473_B1001 重建方式新增 05-其他 選項之自行輸入內容
				if("05".equals(Util.trim(data.getBuildWay()))){
					if(!"".equals(Util.trim(data.getOtherDesc()))){
						sb.append("，").append(Util.trim(data.getOtherDesc()));
					}
				}
				sb.append("\r\n");
				sb.append(prop.getProperty("L140M01T.estateNote")).append(
						Util.trim(data.getEstateNote()));
			}

		} else if (UtilConstants.L140M01T_estatType.公私立各級學校.equals(estateType)) {

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.D02"))
					.append(Util.trim(data.getSubject())).append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.01")).append("");
			if ("0".equals(data.getPosition())) {
				CodeType counties = null;
				List<CodeType> values = codetypeservice
						.findByCodeTypeAndCodeDescs("counties", null, null,
								data.getEstateCityId(), "zh_TW");
				if (values != null && !values.isEmpty()) {
					counties = values.get(0);
				}

				if (counties != null) {
					CodeType counties2 = null;
					List<CodeType> counties2s = codetypeservice
							.findByCodeTypeAndCodeDescs(
									"counties" + counties.getCodeValue(), null,
									Util.trim(data.getEstateAreaId()), null,
									"zh_TW");

					if (counties2s != null && !counties2s.isEmpty()) {
						counties2 = counties2s.get(0);
					}

					if (counties2 != null) {
						String site3 = lmsService.l140m01msite3(
								Util.trim(counties.getCodeDesc2()),
								Util.trim(data.getEstateAreaId()),
								Util.trim(data.getEstateSit3No()),
								Util.trim(data.getEstateSit4No()));

						sb.append(
								counties.getCodeDesc()
										+ counties2.getCodeDesc() + site3)
								.append(prop.getProperty("L140M01T.common.05"))
								.append(Util.trim(data.getSiteNote()));
					}

				}
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06"));
			sb.append(convertEstateStatus(data.getEstateStatus()));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));

		} else if (UtilConstants.L140M01T_estatType.醫療機構.equals(estateType)) {
			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.D03"))
					.append(Util.trim(data.getSubject())).append("\r\n");
			sb.append(prop.getProperty("L140M01T.subjectCode.D03"))
					.append(Util.trim(data.getSubjectCode())).append("\r\n");

			sb.append(prop.getProperty("L140M01T.common.01")).append("");
			if ("0".equals(data.getPosition())) {
				sb.append(locationConvert(data));
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06"));

			sb.append(convertEstateStatus(data.getEstateStatus()));

			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));
		} else if (UtilConstants.L140M01T_estatType.政府廳舍.equals(estateType)) {

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.D04"))
					.append(Util.trim(data.getSubject())).append("\r\n");
			sb.append(prop.getProperty("L140M01T.subjectKind.D04"));
			if ("01".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D04.01"));
			} else if ("02".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D04.02"));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.01")).append("");
			if ("0".equals(data.getPosition())) {
				sb.append(locationConvert(data));
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06")).append(
					convertEstateStatus(data.getEstateStatus()));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));

		} else if (UtilConstants.L140M01T_estatType.長照服務機構.equals(estateType)) {

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.D05")).append(
					Util.trim(data.getSubject()));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.subjectKind.D05"));
			if ("01".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D05.01"));
			} else if ("02".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D05.02"));
			} else if ("03".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D05.03"));
			} else if ("04".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D05.04"));
			} else if ("05".equals(data.getSubjectKind())) {
				sb.append(prop.getProperty("L140M01T.subjectKind.D05.05"));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.01")).append("");
			if ("0".equals(data.getPosition())) {
				sb.append(locationConvert(data));
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06")).append(
					convertEstateStatus(data.getEstateStatus()));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));

		} else if (UtilConstants.L140M01T_estatType.社會住宅.equals(estateType)) {

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.D06")).append(
					Util.trim(data.getSubject()));
			sb.append("\r\n");

			sb.append(prop.getProperty("L14common.01")).append("");
			if ("0".equals(data.getPosition())) {
				sb.append(locationConvert(data));
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06")).append(
					convertEstateStatus(data.getEstateStatus()));

			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));

		} else if (UtilConstants.L140M01T_estatType.A0井.equals(estateType)) {
			Map<String, CapAjaxFormResult> a0Codetype = codetypeservice
					.findByCodeType(new String[] { "LandUse21",
							"cms1010_useKind1", "LandUse22", "cms1010_useKind2" });

			sb.append(estateCodeTypes.get("estateType").get(estateType));
			sb.append("\r\n");
			if ("0".equals(data.getPosition())) {
				sb.append(prop.getProperty("L140M01T.A0#.sectKind"));
				if ("1".equals(data.getSectKind())) {
					sb.append(prop.getProperty("L140M01T.A0#.sectKind.1"));
				} else if ("2".equals(data.getSectKind())) {
					sb.append(prop.getProperty("L140M01T.A0#.sectKind.2"));
				}
				sb.append("\r\n");
				sb.append(prop.getProperty("L140M01T.A0#.useSect"));
				if ("1".equals(data.getSectKind())) {
					sb.append(a0Codetype.get("LandUse21")
							.get(data.getUseSect()));
				} else if ("2".equals(data.getSectKind())) {
					sb.append(a0Codetype.get("LandUse22")
							.get(data.getUseSect()));
				}
				sb.append("\r\n");
				sb.append(prop.getProperty("L140M01T.A0#.useKind"));
				if ("1".equals(data.getSectKind())) {
					sb.append(a0Codetype.get("cms1010_useKind1").get(
							Util.trim(data.getUseKind())));
				} else if ("2".equals(data.getSectKind())) {
					sb.append(a0Codetype.get("cms1010_useKind2").get(
							data.getUseKind()));
				}
				sb.append("\r\n");
			}
			sb.append(prop.getProperty("L140M01T.A0#.subjectCode.A0井")).append(
					Util.trim(data.getSubjectCode()));

			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.01")).append("");
			if ("0".equals(data.getPosition())) {
				sb.append(locationConvert(data));
			} else {
				sb.append(Util.trim(data.getSiteNote()));
			}
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.06"));
			sb.append(convertEstateStatus(data.getEstateStatus()));
			sb.append("\r\n");
			sb.append(prop.getProperty("L140M01T.common.10")).append(
					CapDate.formatDate(data.getOverDate(), "yyyy-MM-dd"));
		}

		else {
			sb.append(estateCodeTypes.get("estateType").get(estateType));
		}

		return sb.toString();

	}

	private String convertEstateStatus(String estateStatus) {
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		if ("1".equals(estateStatus)) {
			return prop.getProperty("L140M01T.common.07");// "規劃中";
		} else if ("2".equals(estateStatus)) {
			return prop.getProperty("L140M01T.common.08");// "興建中";
		} else if ("3".equals(estateStatus)) {
			return prop.getProperty("L140M01T.common.09");// "已完工";
		}
		return "";
	}

	private String locationConvert(L140MM3C data) {
		StringBuffer sb = new StringBuffer();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7500M01Page.class);
		CodeType counties = codetypeservice.findByTypeAndDesc3("counties",
				data.getEstateCityId());

		if (counties != null) {
			CodeType counties2 = codetypeservice.findByTypeAndDesc2("counties"
					+ counties.getCodeValue(), data.getEstateAreaId());
			if (counties2 != null) {
				String site3 = lmsService.l140m01msite3(
						Util.trim(counties.getCodeDesc2()),
						Util.trim(data.getEstateAreaId()),
						Util.trim(data.getEstateSit3No()),
						Util.trim(data.getEstateSit4No()));

				sb.append(
						counties.getCodeDesc() + counties2.getCodeDesc()
								+ site3)
						.append(prop.getProperty("L140M01T.common.05"))
						.append(data.getSiteNote());
			}
		}
		return sb.toString();
	}

	/**
	 * @param type
	 *            Y=是 N=否 X=空白 A=兩種都有
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic(String type, Properties prop, int typeSize) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否

		if (typeSize > 2) {
			if ("A".equals(type)) {
				str.append("■");
			} else {
				str.append("□");
			}
			str.append(prop.getProperty("both")); // 兩種都有
		}

		return str.toString();
	}

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得欄位文字□是□否
	 * 
	 * @param type
	 *            Y=是 N=否
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYNPic4(String type, Properties prop) {
		StringBuffer str = new StringBuffer();
		if ("Y".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("yes")); // 是
		if ("N".equals(type)) {
			str.append("■");
		} else {
			str.append("□");
		}
		str.append(prop.getProperty("no")); // 否

		return str.toString();
	}
}
