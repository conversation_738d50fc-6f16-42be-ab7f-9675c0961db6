/* 
 * L130S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 異常通報表明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L130S01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L130S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	@Column(name="CREATEBY", length=3, columnDefinition="CHAR(3)")
	private String createBY;

	/** 
	 * 單位種類<p/>
	 * 1:分行<br/>
	 *  2:營運中心<br/>
	 *  3:授管處<br/>
	 *  Miller added at 2012/11/23
	 */
	@Column(name="BRANCHKIND", length=1, columnDefinition="CHAR(1)")
	private String branchKind;	
	
	/** 項目代號 **/
	@Column(name="SEQNO", length=3, columnDefinition="CHAR(3)")
	private String seqNo;

	/** 
	 * 項目名稱<p/>
	 * 128個全型字
	 */
	@Column(name="SEQNAME", length=384, columnDefinition="VARCHAR(384)")
	private String seqName;

	/** 
	 * 事項顯示順序<p/>
	 * Miller added at 2012/11/5
	 */
	@Column(name="SEQSHOW", length=3, columnDefinition="CHAR(3)")
	private String seqShow;

	/** 
	 * 事項大類<p/>
	 * Miller added at 2012/11/5
	 */
	@Column(name="BIGKIND", length=3, columnDefinition="CHAR(3)")
	private String bigKind;

	/** 
	 * 停權組字時要加上顯示的字串<p/>
	 * Miller added at 2012/11/5
	 */
	@Column(name="SEQAPPEND", length=384, columnDefinition="VARCHAR(384)")
	private String seqAppend;

	/** 
	 * 擬/已辦<p/>
	 * 2.擬辦<br/>
	 *  1.已辦
	 */
	@Column(name="SEQKIND", length=1, columnDefinition="CHAR(1)")
	private String seqKind;

	/** 
	 * 處理日期<p/>
	 * YYYY-MM-DD
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RUNDATE", columnDefinition="DATE")
	private Date runDate;

	/** 
	 * 金額/設押種類<p/>
	 * 1.金額<br/>
	 *  2.設押金額<br/>
	 *  3.解除曝險金額<br/>
	 *  Miller added at 2012/11/19
	 */
	@Column(name="SETKIND", length=1, columnDefinition="CHAR(1)")
	private String setKind;

	/** 金額/設押幣別 **/
	@Column(name="SETCURR", length=3, columnDefinition="VARCHAR(3)")
	private String setCurr;

	/** 
	 * 金額/設押金額<p/>
	 * 仟元
	 */
	@Column(name="SETAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal setAmt;

	/** 
	 * 停權<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	@Column(name="ISSTOP", length=1, columnDefinition="CHAR(1)")
	private String isStop;

	/** 
	 * 說明<p/>
	 * 最多兩百個中文/全形字
	 */
	@Column(name="DOCDSCR", length=600, columnDefinition="VARCHAR(600)")
	private String docDscr;

	/** 
	 * 營運中心核定事項<p/>
	 * Y/N
	 */
	@Column(name="AREADECIDE", length=1, columnDefinition="CHAR(1)")
	private String areaDecide;

	/** 
	 * 依規定暫停單位主管授權月<p/>
	 * MM
	 */
	@Column(name="AREAMONTH", length=2, columnDefinition="CHAR(2)")
	private String areaMonth;

	/** 
	 * 授管處核定事項<p/>
	 * Y/N
	 */
	@Column(name="HEADDECIDE", length=1, columnDefinition="CHAR(1)")
	private String headDecide;

	/** 
	 * 依規定暫停單位主管授權月<p/>
	 * MM
	 */
	@Column(name="HEADMONTH", length=2, columnDefinition="CHAR(2)")
	private String headMonth;

	/** 
	 * 是否異動往來異常戶<p/>
	 * Y/N<br/>
	 *  Miller added at 2012/11/22
	 */
	@Column(name="ISUNNORMAL", length=1, columnDefinition="CHAR(1)")
	private String isUnNormal;	
	
	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/** 控管訊息 */
	@Column(name="CTLDSCR", length=300, columnDefinition="VARCHAR(300)")
	private String ctlDscr;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得文件產生方式<p/>
	 * 系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 */
	public String getCreateBY() {
		return this.createBY;
	}
	/**
	 *  設定文件產生方式<p/>
	 *  系統產生 | SYS<br/>
	 *  人工產生 | PEO
	 **/
	public void setCreateBY(String value) {
		this.createBY = value;
	}

	/** 
	 * 取得單位種類<p/>
	 * 1:分行<br/>
	 *  2:營運中心<br/>
	 *  3:授管處<br/>
	 *  Miller added at 2012/11/23
	 */
	public String getBranchKind() {
		return this.branchKind;
	}
	/**
	 *  設定單位種類<p/>
	 *  1:分行<br/>
	 *  2:營運中心<br/>
	 *  3:授管處<br/>
	 *  Miller added at 2012/11/23
	 **/
	public void setBranchKind(String value) {
		this.branchKind = value;
	}	
	
	/** 取得項目代號 **/
	public String getSeqNo() {
		return this.seqNo;
	}
	/** 設定項目代號 **/
	public void setSeqNo(String value) {
		this.seqNo = value;
	}

	/** 
	 * 取得項目名稱<p/>
	 * 128個全型字
	 */
	public String getSeqName() {
		return this.seqName;
	}
	/**
	 *  設定項目名稱<p/>
	 *  128個全型字
	 **/
	public void setSeqName(String value) {
		this.seqName = value;
	}

	/** 
	 * 取得事項顯示順序<p/>
	 * Miller added at 2012/11/5
	 */
	public String getSeqShow() {
		return this.seqShow;
	}
	/**
	 *  設定事項顯示順序<p/>
	 *  Miller added at 2012/11/5
	 **/
	public void setSeqShow(String value) {
		this.seqShow = value;
	}

	/** 
	 * 取得事項大類<p/>
	 * Miller added at 2012/11/5
	 */
	public String getBigKind() {
		return this.bigKind;
	}
	/**
	 *  設定事項大類<p/>
	 *  Miller added at 2012/11/5
	 **/
	public void setBigKind(String value) {
		this.bigKind = value;
	}

	/** 
	 * 取得停權組字時要加上顯示的字串<p/>
	 * Miller added at 2012/11/5
	 */
	public String getSeqAppend() {
		return this.seqAppend;
	}
	/**
	 *  設定停權組字時要加上顯示的字串<p/>
	 *  Miller added at 2012/11/5
	 **/
	public void setSeqAppend(String value) {
		this.seqAppend = value;
	}

	/** 
	 * 取得擬/已辦<p/>
	 * 1.擬辦<br/>
	 *  2.已辦
	 */
	public String getSeqKind() {
		return this.seqKind;
	}
	/**
	 *  設定擬/已辦<p/>
	 *  1.擬辦<br/>
	 *  2.已辦
	 **/
	public void setSeqKind(String value) {
		this.seqKind = value;
	}

	/** 
	 * 取得處理日期<p/>
	 * YYYY-MM-DD
	 */
	public Date getRunDate() {
		return this.runDate;
	}
	/**
	 *  設定處理日期<p/>
	 *  YYYY-MM-DD
	 **/
	public void setRunDate(Date value) {
		this.runDate = value;
	}

	/** 
	 * 取得金額/設押種類<p/>
	 * 1.金額<br/>
	 *  2.設押金額<br/>
	 *  3.解除曝險金額<br/>
	 *  Miller added at 2012/11/19
	 */
	public String getSetKind() {
		return this.setKind;
	}
	/**
	 *  設定金額/設押種類<p/>
	 *  1.金額<br/>
	 *  2.設押金額<br/>
	 *  3.解除曝險金額<br/>
	 *  Miller added at 2012/11/19
	 **/
	public void setSetKind(String value) {
		this.setKind = value;
	}

	/** 取得金額/設押幣別 **/
	public String getSetCurr() {
		return this.setCurr;
	}
	/** 設定金額/設押幣別 **/
	public void setSetCurr(String value) {
		this.setCurr = value;
	}

	/** 
	 * 取得金額/設押金額<p/>
	 * 仟元
	 */
	public BigDecimal getSetAmt() {
		return this.setAmt;
	}
	/**
	 *  設定金額/設押金額<p/>
	 *  仟元
	 **/
	public void setSetAmt(BigDecimal value) {
		this.setAmt = value;
	}

	/** 
	 * 取得停權<p/>
	 * Y:是<br/>
	 *  N:否
	 */
	public String getIsStop() {
		return this.isStop;
	}
	/**
	 *  設定停權<p/>
	 *  Y:是<br/>
	 *  N:否
	 **/
	public void setIsStop(String value) {
		this.isStop = value;
	}

	/** 
	 * 取得說明<p/>
	 * 最多兩百個中文/全形字
	 */
	public String getDocDscr() {
		return this.docDscr;
	}
	/**
	 *  設定說明<p/>
	 *  最多兩百個中文/全形字
	 **/
	public void setDocDscr(String value) {
		this.docDscr = value;
	}

	/** 
	 * 取得營運中心核定事項<p/>
	 * Y/N
	 */
	public String getAreaDecide() {
		return this.areaDecide;
	}
	/**
	 *  設定營運中心核定事項<p/>
	 *  Y/N
	 **/
	public void setAreaDecide(String value) {
		this.areaDecide = value;
	}

	/** 
	 * 取得依規定暫停單位主管授權月<p/>
	 * MM
	 */
	public String getAreaMonth() {
		return this.areaMonth;
	}
	/**
	 *  設定依規定暫停單位主管授權月<p/>
	 *  MM
	 **/
	public void setAreaMonth(String value) {
		this.areaMonth = value;
	}

	/** 
	 * 取得授管處核定事項<p/>
	 * Y/N
	 */
	public String getHeadDecide() {
		return this.headDecide;
	}
	/**
	 *  設定授管處核定事項<p/>
	 *  Y/N
	 **/
	public void setHeadDecide(String value) {
		this.headDecide = value;
	}

	/** 
	 * 取得依規定暫停單位主管授權月<p/>
	 * MM
	 */
	public String getHeadMonth() {
		return this.headMonth;
	}
	/**
	 *  設定依規定暫停單位主管授權月<p/>
	 *  MM
	 **/
	public void setHeadMonth(String value) {
		this.headMonth = value;
	}

	/** 
	 * 取得是否異動往來異常戶<p/>
	 * Y/N<br/>
	 *  Miller added at 2012/11/22
	 */
	public String getIsUnNormal() {
		return this.isUnNormal;
	}
	/**
	 *  設定是否異動往來異常戶<p/>
	 *  Y/N<br/>
	 *  Miller added at 2012/11/22
	 **/
	public void setIsUnNormal(String value) {
		this.isUnNormal = value;
	}	
	
	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 取得控管訊息 **/
	public String getCtlDscr() {
		return ctlDscr;
	}
	/** 設定控管訊息 **/
	public void setCtlDscr(String ctlDscr) {
		this.ctlDscr = ctlDscr;
	}
}
