/* 
 * CapForwardFilter.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */

package tw.com.iisi.cap.web.filter;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * CapForwardFilter
 * 頁面轉導過濾
 * </pre>
 * 
 * @since 2011/3/10
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/3/10,iristu,new
 *          <li>2011/3/22,RodesChen,修改filter方式
 *          </ul>
 */
public class CapForwardFilter implements Filter {

    Map<String, String> filterRules;

    /**
     * <pre>
     * 實例化濾器
     * </pre>
     * 
     * @param filterConfig
     *            FilterConfig
     * @throws ServletException
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        filterRules = new HashMap<String, String>();
        Enumeration<String> filter = filterConfig.getInitParameterNames();
        String rule = null;
        while (filter.hasMoreElements()) {
            rule = filter.nextElement();
            filterRules.put(rule, filterConfig.getInitParameter(rule));
        }
    }

    /**
     * <pre>
     * 當url路徑符合filterPath時，則直接導到正確的路徑
     * </pre>
     * 
     * @param request
     *            ServletRequest
     * @param response
     *            ServletResponse
     * @param chain
     *            FilterChain
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest req = ((HttpServletRequest) request);
        String url = req.getRequestURL().toString();
        Iterator<String> rules = filterRules.keySet().iterator();
        String filterPath = null, distPath = null;
        boolean forward = false;
        while (rules.hasNext()) {
            filterPath = rules.next();
            distPath = filterRules.get(filterPath);
            if (CapString.checkRegularMatch(url, filterPath)) {
                forward = true;
                if (CapString.checkRegularMatch(filterPath, "\\*$")) {
                    url = CapString.getRegularMatch(url, "(?<=" + filterPath.replaceAll("(\\*|\\.\\*)$", "") + ").*");
                    forwardToPage((HttpServletRequest) request, (HttpServletResponse) response, distPath + "/" + url.replaceAll("^/", ""));
                } else {
                    forwardToPage((HttpServletRequest) request, (HttpServletResponse) response, distPath);
                }
                break;
            }
        }
        if (!forward) {
            chain.doFilter(request, response);
        }

    }

    /**
     * <pre>
     * destroy
     * </pre>
     */
    @Override
    public void destroy() {
        // do nothing
    }

    /**
     * 轉到指定頁面
     * 
     * @param request
     *            前端請求
     * @param response
     *            伺服器回應
     * @param page
     *            頁面
     * @throws IOException
     * @throws ServletException
     * @see javax.servlet.RequestDispatcher#forward(ServletRequest, ServletResponse) forward
     */
    private void forwardToPage(HttpServletRequest request, HttpServletResponse response, String page) throws IOException, ServletException {
        RequestDispatcher dispatch = request.getRequestDispatcher(page);
        dispatch.forward(request, response);
    }

}
