var initDfd = initDfd || $.Deferred();
initDfd.done(function(json){
	if(json.c120m01a_list){
		var dyna = []
		var showBtn = false;
		if (userInfo && (userInfo.ssoUnitNo || '').indexOf("9") === 0) {
			showBtn = true;
        }
		var varVer = json.varVer;
		$.each(json.c120m01a_list.key, function(idx, jsonItem) {
			if(varVer == "3.0"){
				dyna.push("<table border='1' width='95%' class='tb2'>");
				
				dyna.push("<tr>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.caseBorrower']+(idx+1)+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custPos']+"："+jsonItem.c120_custPos+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custId']+"：<span class='the_link' data-c121m01c_oid='"+jsonItem.c121m01c_oid+"'>"+jsonItem.c120_custId+"</span></td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custName']+"："+jsonItem.c120_custName+"</td>");
				dyna.push("</tr>");
				
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01A.varVer']+"</td>");//模型版本
				dyna.push("<td colspan='2'>"+varVer+"</td>");
				dyna.push("</tr>");
				//---去除調整評等理由欄位，增加房貸/非房貸分隔條
				dyna.push("<tr>");
				dyna.push("<td colspan='2'></td>");
				dyna.push("<td class='hd2' width='25%'>"+i18n.lms1025m01['tab04.mortgage']+"</td>");
				dyna.push("<td class='hd2' width='25%'>"+i18n.lms1025m01['tab04.non-mortgage']+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.pRating']+"</td>");//初始評等
				dyna.push("<td>"+jsonItem.c121m01c_pRating+"</td>");
				dyna.push("<td>"+jsonItem.c121m01g_pRating+"</td>");
				dyna.push("</tr>");
				//---------
				//這個只有澳洲要，加拿大、法國不需要 
				if(jsonItem.showVeda){
					dyna.push("<tr>");
					dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.sRating']+"("+i18n.lms1025m01['tab06.note.sRating']+")"+"</td>");//獨立評等
					dyna.push("<td>"+jsonItem.c121m01c_sRating+"</td>");
					dyna.push("<td>"+jsonItem.c121m01g_sRating+"</td>");
					dyna.push("</tr>");
					dyna.push("<tr>");
				}
				
				//---------
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.fRating']+"</td>");//最終評等
				dyna.push("<td>"+jsonItem.c121m01c_fRating+"</td>");
				dyna.push("<td>"+jsonItem.c121m01g_fRating+"</td>");
				dyna.push("</tr>");
				
				dyna.push("</table>");
			}else{
				dyna.push("<table border='1' width='95%' class='tb2'>");
				
				dyna.push("<tr>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.caseBorrower']+(idx+1)+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custPos']+"："+jsonItem.c120_custPos+"</td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custId']+"：<span class='the_link' data-c121m01c_oid='"+jsonItem.c121m01c_oid+"'>"+jsonItem.c120_custId+"</span></td>");
				dyna.push("<td width='25%' class='hd2'>"+i18n.lms1025m01['label.custName']+"："+jsonItem.c120_custName+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01A.varVer']+"</td>");//模型版本
				dyna.push("<td colspan='2'>"+varVer+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.pRating']+"</td>");//初始評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01c_pRating+"</td>");
				dyna.push("</tr>");
				//---------
				dyna.push("<tr>");
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.sRating']+"("+i18n.lms1025m01['tab06.note.sRating']+")"+"</td>");//獨立評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01c_sRating+"</td>");
				dyna.push("</tr>");
				dyna.push("<tr>");
				//---------
				dyna.push("<td colspan='2'>"+i18n.lms1025m01['C121M01C.fRating']+"("+i18n.lms1025m01['tab06.note.fRating']+")"+"</td>");//最終評等
				dyna.push("<td colspan='2'>"+jsonItem.c121m01c_fRating+"</td>");
				dyna.push("</tr>");
				
				dyna.push("</table>");
			}
			dyna.push("<br/>");
		});	
		
		$("#div06").html(dyna.join(""));
		if(showBtn){
			enable_list();
		}
	}
});

function enable_list(){
	$("span.the_link").addClass('enable_open_link');
	$("span.the_link").click(function(){
		if($(this).hasClass( "enable_open_link" )){
			var c121m01c_oid = $(this).attr("data-c121m01c_oid");
			
			$.form.submit({ url:'../lms1025m02'
				, data:{
						 'noOpenDoc':true
						,'c121m01c_oid': c121m01c_oid
					}				
				, target:c121m01c_oid});
		}
		
	});
}
