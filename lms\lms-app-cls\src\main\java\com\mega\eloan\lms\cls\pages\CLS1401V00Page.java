/* 
 * CLS1401V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 小放會grid畫面(個金)
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1401v00")
public class CLS1401V00Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		if (this.getAuth(AuthType.Accept)) {
			list.add(LmsButtonEnum.View);
		}

		if (this.getAuth(AuthType.Modify)) {
			list.add(LmsButtonEnum.Add);
			list.add(LmsButtonEnum.Delete);
			list.add(LmsButtonEnum.Modify);
		}
		addToButtonPanel(model, list);
		renderJsI18N(CLS1401V00Page.class);

		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1401V00Page');");
	}

}
