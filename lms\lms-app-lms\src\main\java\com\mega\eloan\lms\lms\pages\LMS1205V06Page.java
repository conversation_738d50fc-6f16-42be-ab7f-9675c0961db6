/* 
 * LMS1815V04Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.panels.GridViewFilterPanel01;

import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 授信簽報書呈授管處 / 營運中心
 * </pre>
 * 
 * @since 2011/11/9
 * <AUTHOR> Lin
 * @version <ul>
 *          <li>2011/11/9,Miller Lin,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1205v06")
public class LMS1205V06Page extends AbstractEloanInnerView {


	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 設定文件狀態(交易代碼)
		setGridViewStatus(CreditDocStatusEnum.海外_婉卻);
		// if (this.getAuth(params, AuthType.Accept)) {
		// // 主管權限時要顯示的按鈕...
		// add(new CreditButtonPanel("_buttonPanel",
		// null,CreditButtonEnum.View,CreditButtonEnum.Filter));
		// } else {
		// // 否則需要顯示的按鈕
		// // 加上Button
		// add(new CreditButtonPanel("_buttonPanel", null,
		// CreditButtonEnum.View,CreditButtonEnum.CaseCopy,
		// CreditButtonEnum.Filter));
		// }
		//

		// 加上Button
		List<EloanPageFragment> btns = new ArrayList<EloanPageFragment>();
		// 主管跟經辦都會出現的按鈕
		btns.addAll(Arrays.asList(LmsButtonEnum.View, LmsButtonEnum.Filter));

		// 只有主管出現的按鈕
		if (this.getAuth(AuthType.Accept)) {
		}
		// 只有經辦出現的按鈕
		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.CaseCopy);
		}
		addToButtonPanel(model, btns);
		// 套用哪個i18N檔案
		renderJsI18N(LMS1205V01Page.class);
		
		setupIPanel(new GridViewFilterPanel01(PANEL_ID), model, params);
	}// ;

}
