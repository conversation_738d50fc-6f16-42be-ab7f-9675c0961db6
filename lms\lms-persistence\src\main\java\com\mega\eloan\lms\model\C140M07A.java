package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.lms.model.BRelated;
import com.mega.eloan.common.model.RelativeMeta;

/**
 * <pre>
 * C140M07A model.
 * </pre>
 * 
 * @since 2011/10/04
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/04,TimChiang,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140M07A-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name = "C140M07A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140M07A extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;
	
	public C140M07A() {
	}

	public C140M07A(String mainId, String pid, String uid, String tab,
			String subtab) {
		this.setMainId(mainId);
		this.setPid(pid);
		this.setUid(uid);
		this.setTab(tab);
		this.setSubtab(subtab);
	}


	@Column(length = 32)
	private String uid;

	/**40.保證公司
	 * 42.財務分析結構化表格
	 * 70.柒
	 * 71.比率分析
	 * 73.營運週轉金分析
	 * 80.捌
	 * 81.固定資產
	 * 82.存貨分析*/
	@Column(length = 3)
	private String tab;

	@Column(length = 3)
	private String subtab;

	/**D: DELETE*/
	@Column(length = 1)
	private String flag;
		
	// bi-directional many-to-one association to C140JSON
	@ToStringExclude
	@OneToMany(mappedBy = "c140m07a", cascade=CascadeType.PERSIST, fetch=FetchType.LAZY)
	private List<C140JSON> c140jsons;

	// bi-directional many-to-one association to C140M01A
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	// bi-directional many-to-one association to C140S07A
	@ToStringExclude
	@OneToMany(mappedBy = "c140m07a", cascade=CascadeType.PERSIST, fetch=FetchType.LAZY)
	private List<C140S07A> c140s07as;

	@Transient
	private List<BRelated> brelateds;
	
	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public List<C140JSON> getC140jsons() {
		return this.c140jsons;
	}

	public void setC140jsons(List<C140JSON> c140jsons) {
		this.c140jsons = c140jsons;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
	public List<C140S07A> getC140s07as() {
		return this.c140s07as;
	}

	public void setC140s07as(List<C140S07A> c140s07as) {
		this.c140s07as = c140s07as;
	}

	public String getTab() {
		return tab;
	}

	public void setTab(String tab) {
		this.tab = tab;
	}

	public String getSubtab() {
		return subtab;
	}

	public void setSubtab(String subtab) {
		this.subtab = subtab;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getFlag() {
		return flag;
	}
	
	public List<BRelated> getBrelateds() {
		return brelateds;
	}

	public void setBrelateds(List<BRelated> brelateds) {
		this.brelateds = brelateds;
	}
	
	

}