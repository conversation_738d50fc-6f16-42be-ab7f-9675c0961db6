package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF600;

/**
 * <pre>
 * 空地貸款控制檔
 * </pre>
 * 
 * @since 2019/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4,009301,new
 *          </ul>
 */
public interface MisELF600Service {

	/**
	 * 依id+dupNo取得額度序號
	 * 
	 * @param contract
	 * @return
	 */
	public List<Map<String, Object>> selCntrnoByCustidDupno(String custId, String dupNo);
	
	/**
	 * 依額度序號取得資料
	 * 
	 * @param contract
	 * @return
	 */
	ELF600 findByContract(String contract);
	
	Map<String, Object> getByCntrNo(String cntrNo);

	/**
	 * 刪除資料
	 * 
	 * @param cntrNo
	 */
	void delete(String contract);

	/**
	 * 新增資料
	 * 
	 * @param elf515
	 */
	void insert(ELF600 elf600);
	
	List<Map<String, Object>> getAll();

	void updateByContract(ELF600 elf600);
}
