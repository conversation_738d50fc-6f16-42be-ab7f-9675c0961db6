/* 
 * C121M01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 海外消金評等簽章欄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C121M01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","branchType","branchId","staffNo","staffJob"}))
public class C121M01E extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** mainId **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 單位類型<p/>
	 * 1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行<br/>
	 *  6. 國金部
	 */
	@Size(max=1)
	@Column(name="BRANCHTYPE", length=1, columnDefinition="CHAR(1)")
	private String branchType;

	/** 單位代碼 **/
	@Size(max=3)
	@Column(name="BRANCHID", length=3, columnDefinition="CHAR(3)")
	private String branchId;

	/** 行員代碼 **/
	@Size(max=6)
	@Column(name="STAFFNO", length=6, columnDefinition="CHAR(6)")
	private String staffNo;

	/** 
	 * 人員職稱<p/>
	 * L1. 分行經辦(Appraiser)<br/>
	 *      營運中心經辦(Area_Appraiser)<br/>
	 *      授管處經辦(Head_Appraiser)<br/>
	 *  L2. 帳戶管理員(AO)<br/>
	 *  L3. 分行授信/覆核主管(Boss)<br/>
	 *      母行覆核<br/>
	 *      營運中心襄理(Area_Manager)<br/>
	 *      授管處覆核(Head_Manager)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L4. 分行覆核主管(ReCheck)<br/>
	 *      母行覆核主管<br/>
	 *      營運中心覆核主管<br/>
	 *      授管處覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管(Manager)<br/>
	 *      營運中心副營運長(Area_Sub_Leader)<br/>
	 *      授管處副處長(Head_Sub_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L6. 營運中心營運長(Area_Leader)<br/>
	 *      授管處協理(Head_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L7. 提會登錄經辦<br/>
	 *  L8. 提會放行主管<br/>
	 *  L9.單位主管<br/>
	 *  分行單位主管(UNIT_MANAGERID)<br/>
	 *  母行單位主管<br/>
	 *  營運中心單位主管<br/>
	 *  (UNIT_MANAGERID_A)<br/>
	 *  授管處單位主管<br/>
	 *  (UNIT_MANAGERID_S)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 */
	@Size(max=2)
	@Column(name="STAFFJOB", length=2, columnDefinition="CHAR(2)")
	private String staffJob;

	/** 行員姓名 **/
	@Size(max=30)
	@Column(name="STAFFNAME", length=30, columnDefinition="VARCHAR(30)")
	private String staffName;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 排序序號 **/
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(2,0)")
	private BigDecimal seq;	//Integer seq;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得單位類型<p/>
	 * 1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行<br/>
	 *  6. 國金部
	 */
	public String getBranchType() {
		return this.branchType;
	}
	/**
	 *  設定單位類型<p/>
	 *  1. 分行<br/>
	 *  2. 母行/海外總行<br/>
	 *  3. 營運中心<br/>
	 *  4. 授管處<br/>
	 *  5. 徵信承作分行<br/>
	 *  6. 國金部
	 **/
	public void setBranchType(String value) {
		this.branchType = value;
	}

	/** 取得單位代碼 **/
	public String getBranchId() {
		return this.branchId;
	}
	/** 設定單位代碼 **/
	public void setBranchId(String value) {
		this.branchId = value;
	}

	/** 取得行員代碼 **/
	public String getStaffNo() {
		return this.staffNo;
	}
	/** 設定行員代碼 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/** 
	 * 取得人員職稱<p/>
	 * L1. 分行經辦(Appraiser)<br/>
	 *      營運中心經辦(Area_Appraiser)<br/>
	 *      授管處經辦(Head_Appraiser)<br/>
	 *  L2. 帳戶管理員(AO)<br/>
	 *  L3. 分行授信/覆核主管(Boss)<br/>
	 *      母行覆核<br/>
	 *      營運中心襄理(Area_Manager)<br/>
	 *      授管處覆核(Head_Manager)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L4. 分行覆核主管(ReCheck)<br/>
	 *      母行覆核主管<br/>
	 *      營運中心覆核主管<br/>
	 *      授管處覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管(Manager)<br/>
	 *      營運中心副營運長(Area_Sub_Leader)<br/>
	 *      授管處副處長(Head_Sub_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L6. 營運中心營運長(Area_Leader)<br/>
	 *      授管處協理(Head_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L7. 提會登錄經辦<br/>
	 *  L8. 提會放行主管<br/>
	 *  L9.單位主管<br/>
	 *  分行單位主管(UNIT_MANAGERID)<br/>
	 *  母行單位主管<br/>
	 *  營運中心單位主管<br/>
	 *  (UNIT_MANAGERID_A)<br/>
	 *  授管處單位主管<br/>
	 *  (UNIT_MANAGERID_S)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 */
	public String getStaffJob() {
		return this.staffJob;
	}
	/**
	 *  設定人員職稱<p/>
	 *  L1. 分行經辦(Appraiser)<br/>
	 *      營運中心經辦(Area_Appraiser)<br/>
	 *      授管處經辦(Head_Appraiser)<br/>
	 *  L2. 帳戶管理員(AO)<br/>
	 *  L3. 分行授信/覆核主管(Boss)<br/>
	 *      母行覆核<br/>
	 *      營運中心襄理(Area_Manager)<br/>
	 *      授管處覆核(Head_Manager)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L4. 分行覆核主管(ReCheck)<br/>
	 *      母行覆核主管<br/>
	 *      營運中心覆核主管<br/>
	 *      授管處覆核主管<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L5. 單位/授權主管(Manager)<br/>
	 *      營運中心副營運長(Area_Sub_Leader)<br/>
	 *      授管處副處長(Head_Sub_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L6. 營運中心營運長(Area_Leader)<br/>
	 *      授管處協理(Head_Leader)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  L7. 提會登錄經辦<br/>
	 *  L8. 提會放行主管<br/>
	 *  L9.單位主管<br/>
	 *  分行單位主管(UNIT_MANAGERID)<br/>
	 *  母行單位主管<br/>
	 *  營運中心單位主管<br/>
	 *  (UNIT_MANAGERID_A)<br/>
	 *  授管處單位主管<br/>
	 *  (UNIT_MANAGERID_S)<br/>
	 *     (母行/授管處/營運中心)<br/>
	 *  徵信報告或資信簡表簽核層級<br/>
	 *  C1. 徵信經辦<br/>
	 *  C2. 徵信覆核主管<br/>
	 *  C3. 經理/副理<br/>
	 *  C4. 處長/協理/主任/副主任<br/>
	 *  C5. 徵信單位主管
	 **/
	public void setStaffJob(String value) {
		this.staffJob = value;
	}

	/** 取得行員姓名 **/
	public String getStaffName() {
		return this.staffName;
	}
	/** 設定行員姓名 **/
	public void setStaffName(String value) {
		this.staffName = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得排序序號 **/
	public BigDecimal getSeq() {
		return this.seq;
	}
	/** 設定排序序號 **/
	public void setSeq(BigDecimal value) {
		this.seq = value;
	}
}
