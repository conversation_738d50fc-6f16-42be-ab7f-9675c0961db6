/* 
 * L120S20B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 主要申請敘作內容明細檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S20B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L120S20B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * 簽報書MAINID
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 借款人統編 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)")
	private String custId;

	/** 重複序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 已分配額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 額度最終EAD **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="CNTREAD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal cntrEad;

	/** 分配後擔保品回收合計 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="COLLATERALRECOVERY", columnDefinition="DECIMAL(17,2)")
	private BigDecimal collateralRecovery;

	/** 有無額度明細表 **/
	@Size(max=1)
	@Column(name="HASCNTRDOC", length=1, columnDefinition="CHAR(1)")
	private String hasCntrDoc;

	/** 本額度有無送保 **/
	@Size(max=1)
	@Column(name="HEADITEM1", length=1, columnDefinition="CHAR(1)")
	private String headItem1;

	/** 信保保證成數 **/
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="GUTPERCENT", columnDefinition="DECIMAL(5,2)")
	private BigDecimal gutPercent;

	/** 有無公司保證人 **/
	@Size(max=1)
	@Column(name="HASGUARANTOR", length=1, columnDefinition="CHAR(1)")
	private String hasGuarantor;

	/** 預期擔保品回收 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="EXPECTSECUREDRECOVERY", columnDefinition="DECIMAL(17,2)")
	private BigDecimal expectSecuredRecovery;

	/** 預期無擔保回收 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="EXPECTUNSECUREDRECOVERY", columnDefinition="DECIMAL(17,2)")
	private BigDecimal expectUnsecuredRecovery;

	/** 
	 * 無擔保回收率有公司保證者<p/>
	 * E38 44%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="UNSECUREDRECOVERYRATEY", columnDefinition="DECIMAL(5,2)")
	private BigDecimal unsecuredRecoveryRateY;

	/** 
	 * 無擔保回收率無公司保證者<p/>
	 * E39 21%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="UNSECUREDRECOVERYRATEN", columnDefinition="DECIMAL(5,2)")
	private BigDecimal unsecuredRecoveryRateN;

	/** 
	 * 信保回收率<p/>
	 * E33 95%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="GUTRECOVERYRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal gutRecoveryRate;

	/** 
	 * 清償損失率<p/>
	 * 計算結果
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="PAYOFFLOSSRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal payOffLossRate;

	/** 
	 * 清償路徑比例<p/>
	 * E43 96%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="PAYOFFPATH", columnDefinition="DECIMAL(5,2)")
	private BigDecimal payOffPath;

	/** 
	 * 協商路徑比例<p/>
	 * E44 4%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NEGOTIATEPATH", columnDefinition="DECIMAL(5,2)")
	private BigDecimal negotiatePath;

	/** 
	 * 轉正路徑比例<p/>
	 * E45 0%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="TURNPOSITIVEPATH", columnDefinition="DECIMAL(5,2)")
	private BigDecimal turnPositivePath;

	/** 
	 * 協商損失率<p/>
	 * E46 12%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="NEGOTIATELOSSRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal negotiateLossRate;

	/** 
	 * 轉正損失率<p/>
	 * E47 0%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="TURNPOSITIVELOSSRATE", columnDefinition="DECIMAL(5,2)")
	private BigDecimal turnPositiveLossRate;

	/** 
	 * 間接成本<p/>
	 * E48 0.3%
	 */
	@Digits(integer=5, fraction=2, groups = Check.class)
	@Column(name="INDIRECTCOST", columnDefinition="DECIMAL(5,2)")
	private BigDecimal indirectCost;

	/** 預期LGD **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="EXPECTLGD", columnDefinition="DECIMAL(17,2)")
	private BigDecimal expectLgd;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * 簽報書MAINID
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  簽報書MAINID
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得借款人統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定借款人統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重複序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重複序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得已分配額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定已分配額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得額度最終EAD **/
	public BigDecimal getCntrEad() {
		return this.cntrEad;
	}
	/** 設定額度最終EAD **/
	public void setCntrEad(BigDecimal value) {
		this.cntrEad = value;
	}

	/** 取得分配後擔保品回收合計 **/
	public BigDecimal getCollateralRecovery() {
		return this.collateralRecovery;
	}
	/** 設定分配後擔保品回收合計 **/
	public void setCollateralRecovery(BigDecimal value) {
		this.collateralRecovery = value;
	}

	/** 取得有無額度明細表 **/
	public String getHasCntrDoc() {
		return this.hasCntrDoc;
	}
	/** 設定有無額度明細表 **/
	public void setHasCntrDoc(String value) {
		this.hasCntrDoc = value;
	}

	/** 取得本額度有無送保 **/
	public String getHeadItem1() {
		return this.headItem1;
	}
	/** 設定本額度有無送保 **/
	public void setHeadItem1(String value) {
		this.headItem1 = value;
	}

	/** 取得信保保證成數 **/
	public BigDecimal getGutPercent() {
		return this.gutPercent;
	}
	/** 設定信保保證成數 **/
	public void setGutPercent(BigDecimal value) {
		this.gutPercent = value;
	}

	/** 取得有無公司保證人 **/
	public String getHasGuarantor() {
		return this.hasGuarantor;
	}
	/** 設定有無公司保證人 **/
	public void setHasGuarantor(String value) {
		this.hasGuarantor = value;
	}

	/** 取得預期擔保品回收 **/
	public BigDecimal getExpectSecuredRecovery() {
		return this.expectSecuredRecovery;
	}
	/** 設定預期擔保品回收 **/
	public void setExpectSecuredRecovery(BigDecimal value) {
		this.expectSecuredRecovery = value;
	}

	/** 取得預期無擔保回收 **/
	public BigDecimal getExpectUnsecuredRecovery() {
		return this.expectUnsecuredRecovery;
	}
	/** 設定預期無擔保回收 **/
	public void setExpectUnsecuredRecovery(BigDecimal value) {
		this.expectUnsecuredRecovery = value;
	}

	/** 
	 * 取得無擔保回收率有公司保證者<p/>
	 * E38 44%
	 */
	public BigDecimal getUnsecuredRecoveryRateY() {
		return this.unsecuredRecoveryRateY;
	}
	/**
	 *  設定無擔保回收率有公司保證者<p/>
	 *  E38 44%
	 **/
	public void setUnsecuredRecoveryRateY(BigDecimal value) {
		this.unsecuredRecoveryRateY = value;
	}

	/** 
	 * 取得無擔保回收率無公司保證者<p/>
	 * E39 21%
	 */
	public BigDecimal getUnsecuredRecoveryRateN() {
		return this.unsecuredRecoveryRateN;
	}
	/**
	 *  設定無擔保回收率無公司保證者<p/>
	 *  E39 21%
	 **/
	public void setUnsecuredRecoveryRateN(BigDecimal value) {
		this.unsecuredRecoveryRateN = value;
	}

	/** 
	 * 取得信保回收率<p/>
	 * E33 95%
	 */
	public BigDecimal getGutRecoveryRate() {
		return this.gutRecoveryRate;
	}
	/**
	 *  設定信保回收率<p/>
	 *  E33 95%
	 **/
	public void setGutRecoveryRate(BigDecimal value) {
		this.gutRecoveryRate = value;
	}

	/** 
	 * 取得清償損失率<p/>
	 * 計算結果
	 */
	public BigDecimal getPayOffLossRate() {
		return this.payOffLossRate;
	}
	/**
	 *  設定清償損失率<p/>
	 *  計算結果
	 **/
	public void setPayOffLossRate(BigDecimal value) {
		this.payOffLossRate = value;
	}

	/** 
	 * 取得清償路徑比例<p/>
	 * E43 96%
	 */
	public BigDecimal getPayOffPath() {
		return this.payOffPath;
	}
	/**
	 *  設定清償路徑比例<p/>
	 *  E43 96%
	 **/
	public void setPayOffPath(BigDecimal value) {
		this.payOffPath = value;
	}

	/** 
	 * 取得協商路徑比例<p/>
	 * E44 4%
	 */
	public BigDecimal getNegotiatePath() {
		return this.negotiatePath;
	}
	/**
	 *  設定協商路徑比例<p/>
	 *  E44 4%
	 **/
	public void setNegotiatePath(BigDecimal value) {
		this.negotiatePath = value;
	}

	/** 
	 * 取得轉正路徑比例<p/>
	 * E45 0%
	 */
	public BigDecimal getTurnPositivePath() {
		return this.turnPositivePath;
	}
	/**
	 *  設定轉正路徑比例<p/>
	 *  E45 0%
	 **/
	public void setTurnPositivePath(BigDecimal value) {
		this.turnPositivePath = value;
	}

	/** 
	 * 取得協商損失率<p/>
	 * E46 12%
	 */
	public BigDecimal getNegotiateLossRate() {
		return this.negotiateLossRate;
	}
	/**
	 *  設定協商損失率<p/>
	 *  E46 12%
	 **/
	public void setNegotiateLossRate(BigDecimal value) {
		this.negotiateLossRate = value;
	}

	/** 
	 * 取得轉正損失率<p/>
	 * E47 0%
	 */
	public BigDecimal getTurnPositiveLossRate() {
		return this.turnPositiveLossRate;
	}
	/**
	 *  設定轉正損失率<p/>
	 *  E47 0%
	 **/
	public void setTurnPositiveLossRate(BigDecimal value) {
		this.turnPositiveLossRate = value;
	}

	/** 
	 * 取得間接成本<p/>
	 * E48 0.3%
	 */
	public BigDecimal getIndirectCost() {
		return this.indirectCost;
	}
	/**
	 *  設定間接成本<p/>
	 *  E48 0.3%
	 **/
	public void setIndirectCost(BigDecimal value) {
		this.indirectCost = value;
	}

	/** 取得預期LGD **/
	public BigDecimal getExpectLgd() {
		return this.expectLgd;
	}
	/** 設定預期LGD **/
	public void setExpectLgd(BigDecimal value) {
		this.expectLgd = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
