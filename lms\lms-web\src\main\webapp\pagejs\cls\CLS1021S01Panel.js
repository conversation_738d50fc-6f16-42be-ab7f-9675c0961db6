initDfd.done(function(json){
	$("input:radio[name='chk11']").click(function(){        
    	checkselect();
    });
	$("input:radio[name='chk12']").click(function(){        
    	checkselect();
    });
	$("input:radio[name='chk13']").click(function(){        
    	checkselect();
    });
	$("input:radio[name='chk14']").click(function(){        
    	checkselect();
    });
});
function openCustNameBox(){
    if ($("#custId").val() != "" && $("#dupNo").val() != "") {
        $("#openCustNameGrid").jqGrid("setGridParam", {
            postData: {
                formAction: "queryGetCustName",
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val()
            },
            search: true
        }).trigger("reloadGrid");
        $("#openCustNameBox").thickbox({
            title: i18n.cls1021m01["C102M01A.getCustName"],
            modal: true,
            width: 500,
            height: 300,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#openCustNameGrid").getGridParam('selrow');
                    var data = $("#openCustNameGrid").getRowData(id);
                    $("#custName").val(data.CNAME);
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    else {
        CommonAPI.showMessage(i18n.cls1021m01['C102M01A.error3']);
    }
}

function openaLoanACBox(){
    if ($("#custId").val() != "" && $("#dupNo").val() != "" && $("#cntrNo").val() != "") {
        $("#openaLoanACGrid").jqGrid("setGridParam", {
            postData: {
                formAction: "queryGetaLoanAC",
                custId: $("#custId").val(),
                dupNo: $("#dupNo").val(),
                cntrNo: $("#cntrNo").val()
            },
            search: true
        }).trigger("reloadGrid");
        $("#openaLoanACBox").thickbox({
            title: i18n.cls1021m01["C102M01A.getLoanAC"],
            modal: true,
            width: 500,
            height: 300,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#openaLoanACGrid").getGridParam('selrow');
                    var data = $("#openaLoanACGrid").getRowData(id);
                    $("#aLoanAC").val(data.aLoanAC);
                    $("#aLoanDate").val(data.aLoanDate);
                    checkselect();
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    else {
        CommonAPI.showMessage(i18n.cls1021m01['C102M01A.error4']);
    }
}

function checkselect(){
    var $form = $('#C102M01AForm');
    var aLoanDate = $form.find("#aLoanDate").val()||"";
    
    var datelist = aLoanDate.split('-');
    var checkdate = datelist[0] + datelist[1] + datelist[2];
    
    	var rptId = $("#rptId").val()||'';
    	var chk11 = $form.find("input:radio[name='chk11']:checked").val();
    	var chk12 = $form.find("input:radio[name='chk12']:checked").val();
    	var chk13 = $form.find("input:radio[name='chk13']:checked").val();
    	var chk14 = $form.find("input:radio[name='chk14']:checked").val();
    	var selfChk = "N";
    	if (chk11 == "Y" && chk12 == "Y" && chk13 == "Y" && chk14 == "Y") {
    		selfChk = "Y";
    	}
    	var isAloanDate_GE_100_04_21 = (aLoanDate == "" 
    			|| aLoanDate >= "2011-04-21");
    	//ilog.debug("rptId="+rptId+",loanDtBool="+isAloanDate_GE_100_04_21+",selfChk="+selfChk+"["+chk11+" , "+chk12+" , "+chk13+" , "+chk14+"]");
        
    	if(rptId=="V202208"){
    		
    	}else if(rptId=="V20171231"){
    		if(isAloanDate_GE_100_04_21){
    			if(selfChk=="Y"){
        			//default 35%, 但可改75%
            		$form.find('input:radio[name="rskFlag"][value=3]').prop("checked", "checked");
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=4]').prop("checked", "checked");        			
        		}
    			
    		}else{
    			if(selfChk=="Y"){
        			//default 35%, 但可改45%
            		$form.find('input:radio[name="rskFlag"][value=3]').prop("checked", "checked");
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=1]').prop("checked", "checked");        			
        		}
    		}
        }else{
        	if(isAloanDate_GE_100_04_21){
        		if(selfChk=="Y"){
        			//default 45%, 但可改100%
            		$form.find('input:radio[name="rskFlag"][value=1]').prop("checked", "checked");
        		}else{
            		$form.find('input:radio[name="rskFlag"][value=2]').prop("checked", "checked");        			
        		}
        	}else{
        		//首撥日在2011-04-21之前固定為45%
        		$form.find('input:radio[name="rskFlag"][value=1]').prop("checked", "checked");
        	}
        }
}
