var _handler = "lms1800formhandler";
$(function(){
	//================
    var regYYYYMM = /^\d{4}\-(0?[1-9]|1[0-2])$/;
    if (userInfo.ssoUnitNo == "900") {
    	if(userInfo.unitNo == "900"){
    		$("#only900_5").show();
    		$("#only900_1").show();
    	}else{
    		$("#only900_1").show();
    	}    	
    }
    
    $("[name='radioA']").click(function(){
        var i = $(this).val();
        $("#showradio_1").hide();
        $("#showradio_2").hide();
        
        if (i == 0) {
        }else if (i == 1) {
        	$("#showradio_1").show();
        }else if (i == 2) {
        	$("#showradio_2").show();
        }
    });
    //================
   
    $.ajax({
		type : "POST", handler : _handler,data : { formAction : "queryBranch" },
		success:function(obj){
			
			if(true){//載入 篩選 頁面的分行清單
				var _addSpace = true;
				if(obj.itemOrder.length==1){
					_addSpace = false;
				}
				$.each(obj.itemOrder, function(idx, brNo) {
	        		var currobj = {};
	        		var brName = obj.item[brNo];
	        		currobj[brNo] = brName;
	        		 
					$("#brIdFilter").setItems({ item: currobj, format: "{value} {key}", clear:false, space: (_addSpace?(idx==0):false) });
				});	
			}
			
			//=============
			if(true){//產生 Xls 的分行清單
				var col_cnt = 3;
	        	var elmArr = [];
	        	$.each(obj.itemOrder, function(idx, brNo) {
	        		var brName = obj.item[brNo];
	        		//依 itemOrder, 一個一個append, 把 clear 指為 false
	        		
	        		var tdcol = {};
	        		tdcol['_a'] = "<label style='letter-spacing:0px;cursor:pointer;font-weight:normal;'><input value='"+brNo+"' id='xls_brNo' name='xls_brNo' type='checkbox'>"+brNo +" "+ brName+"</label>";
	        		elmArr.push(tdcol);            		
				});
	        	//===
	        	//補empty col
	        	var addcnt = (col_cnt - (elmArr.length % col_cnt));
	        	if(addcnt==col_cnt){
	        		addcnt = 0;
	        	}
	        	for(var i=0;i<addcnt;i++){
	        		var tdcol = {};
	        		tdcol['_a'] = "&nbsp;";	            		
	        		elmArr.push(tdcol);  
	        	}
	        	
	        	var dyna = [];
	        	dyna.push("<table width='100%' border='0' cellspacing='0' cellpadding='0'>");
	        	dyna.push("<tr>");
	        	for(var i=0;i<elmArr.length;i++){
	        		dyna.push("<td>"+elmArr[i]['_a']+"</td>");	            		
	        		if( (i+1) % col_cnt==0){
	        			dyna.push("</tr><tr>");
	        		}
	        	}
	        	dyna.push("</tr>");
	        	dyna.push("</table>");
	        	$("#div_br_status010_produceXls").append(dyna.join("\n"));
	        	
	        	$('#btn_xls_brNo_1').click(function(){
					$("input[name=xls_brNo]").attr('checked', true);
				});
				$('#btn_xls_brNo_0').click(function(){
					$("input[name=xls_brNo]").attr('checked', false);
				});
					
			}			
		}
	});
    
    var gridview_param = "";
    if( $("#gridview_param").find("[name=grid_flag]").length>0  ){
    	gridview_param = $("#gridview_param").find("[name=grid_flag]").val();
    }
    
    var grid = $("#gridview").iGrid({
        handler: 'lms1800gridhandler',
        height: 350,
        //width: 785,
        autowidth: true,
        shrinkToFit: false,
        sortname: 'dataDate|generateDate|branchId',
        sortorder: "desc|desc|asc",
        postData: {
            docStatus: viewstatus,
            formAction: "query", 
            gridview_param : gridview_param
        },
        multiselect: true,
        colModel: [{
            colHeader: i18n.lms1805v01['L180M01A.dataDate'],//"資料年月",
            name: 'dataDate',
            align: "center",
            width: 75,
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m'
            },
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.branchName'],//"分行名稱",
            name: 'branchId',
            width: 100,
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.lms1805v01['L180M01A.generateDate'],//"名單產生日期",
            name: 'generateDate',
            width: 90,
            sortable: true,
            align: "center"
        }, {
            colHeader: i18n.lms1805v01['L180M01A.batchNO'],//"批號",
            name: 'batchNO',
            align: "center",
            width: 50,
            formatter: addZero,
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.creator'],//"名單產生人員",
            name: 'creator',
            align: "center",
            width: 125,
            sortable: true
        }, {
            colHeader: i18n.lms1805v01['L180M01A.defaultCTLDate'],//"預計覆審日",
            name: 'defaultCTLDate',
            align: "center",
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.lms1805v01['L180M01A.createBy'],//"產生方式",
            name: 'createBy',
            align: "center",
            width: 80,
            sortable: true
        }, {
            colHeader: i18n.abstracteloan['doc.lastUpdater'],//"最後異動人員",
            name: 'updater',
            align: "center",
            width: 80,
            sortable: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms1805v01['L180M01A.status'],//"處理狀態",
            name: 'status',
            align: "center",
            width: 60,
            sortable: true
        }, {
        	//J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
            colHeader: i18n.lms1805v01['L180M01A.rpaDate'],//"RPA執行日期",
            name: 'rpaDate',
            align: "center",
            width: 60,
            sortable: true    
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
    
    function openDoc(cellvalue, options, rowObject){
        $.form.submit({
            url: '../lrs/lms1800m01/01',
            data: {
                formAction: "query",
                mainOid: rowObject.oid,
                mainId: rowObject.mainId,
                branchId: rowObject.branchId,
                mainDocStatus: viewstatus
            },
            target: rowObject.oid
        });
    };
    
    $("#buttonPanel").find("#btnFilter").click(function(){//篩選
        thickBoxFilter();
    }).end().find("#btnProduceList").click(function(){//產生名單
    	var _id = "_div_btnProduceList"
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {'1':"單筆即時產生"
						, '2':"整批即時產生" };
			
			build_submenu(dyna, 'decision_btnProduceList', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 450, height: 200, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	                 if (!$("#"+_form).valid()) {
	                     return;
	                 }
	                
	                 $.thickbox.close();
	                 var mode = $("#"+_form).find("[name='decision_btnProduceList']:checked").val();
	             	
	                 if(mode=="1"){
	                	 //單筆
	                	 check_inExePriod().done(function(){
	                		 produceList_S();
	                	 });
	                 }else if(mode=="2"){
	                	 //整批
	                	 check_inExePriod().done(function(){
	                		 produceList_M();
	                	 });	                	 
	                 }	             	             
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             }
	         }
	    });
		
    }).end().find("#btnProduceExcel").click(function(){//產生Excel
    	
   	 	var _id = "_div_btnProduceExcel_"+viewstatus;
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {}
			if(viewstatus=="010"){
				submenu = {'1':"產生查詢名單Exl"
						, '3':"產生企金戶預計覆審件數查詢表"
						, '4':"產生企金戶「必要」覆審件數查詢表" };
	    	}else if(viewstatus=="020"){
	    		submenu = {'1':"產生待覆核名單Exl" };
	    	}else if(viewstatus=="030"){
	    		submenu = {'1':"產生已覆核名單Exl"
					, '2':"產生覆審名單驗證檔" };
	    	}else if(viewstatus=="0A0"){
	    		submenu = {'1':"產生已覆核名單Exl"
					, '2':"產生覆審名單驗證檔" };
	    	}			
			
			build_submenu(dyna, 'decision_btnProduceExcel', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 450, height: 200, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	                 if (!$("#"+_form).valid()) {
	                     return;
	                 }
	                 var mode = "0";
	                 $.thickbox.close();
	                 mode = $("#"+_form).find("[name='decision_btnProduceExcel']:checked").val();
	             	
	                 if(mode=="1" || mode=="2" ){
	                	 var oid_arr = gridSelectOidArr();
	                	 if(oid_arr.length==0){   	 		
	                	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	             	 			return;
	             	 	 }
	                	 	
	                	 $.ajax({
		                     handler: _handler, type: "POST", dataType: "json",
		                     data: {
		                         formAction: "genExcel",
		                         'mainDocStatus':viewstatus,
		                         'mode': mode,
		                         oids: oid_arr.join("|")
		                     },
		                     success: function(obj){		                     	
		                     }
		                 });	 
	                 }else if(mode=="3" || mode=="4"){
	                	 var tb_w = 450; 
	                	 var tb_h = 140;
	                	 var fileName = "data.xls";
	                	 if(mode=="3"){
	                		 $("#tr_brlist").hide();
	                		 fileName = "CTLCountList.xls";
	                	 }else if(mode=="4"){
	                		 $("#tr_brlist").show();
	                		 tb_w = 760; 
		                	 tb_h = 400;
	                	 }
	                	 $("#div_status010_produceXls").thickbox({
	                         title: "",
	                         width: tb_w, height: tb_h, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
	                         buttons: {
	                             "sure": function(){
	                            	 var $frm = $("#form_status010_produceXls"); 
	                                 if ($frm.valid()) {
	                                	 var dateVal = $("#dataDate_status010_produceXls").val();
	                                     if (dateVal == '') {
	                                         return CommonAPI.showMessage(i18n.lms1805v01["L180M01a.error1"]+"："+i18n.lms1805v01["L180M01A.dataDate"]);
	                                     }else{
	                                     	if (!dateVal.match(regYYYYMM)) {                        
	             	                            //val.date2=日期格式錯誤(YYYY-MM)
	             	                            return CommonAPI.showMessage(i18n.def["val.date2"]);
	             	                        }
	                                     }
	                                    
	                                     var brNoArr = [];
	                                     if($("#tr_brlist").is(":visible")){	                                    	 
	                                    	 var brNoArr = $frm.find("input:checkbox:checked").map(function(){ return $(this).val(); }).get();
	                                    	 if(brNoArr.length==0){
		                     					return CommonAPI.showMessage(i18n.lms1805v01["L180M01a.error1"]+"："+"分行");	 
		                     				 }
	                                     }
	                                     
	                                     //不能直接用 $.capFileDownload(...)，會強制 encode 把  | 轉成 %7C
	                                     $.form.submit({
	                                    	url: __ajaxHandler,
	                                 		target : "_blank",
	                                 		data : {
	                                 			_pa : 'lmsdownloadformhandler',
	                                 			'mode': mode,
	                                            'dataDate': dateVal,
	                                            'brNos': brNoArr.join("|"),
	                                 			'fileDownloadName' : fileName,
	                                 			'serviceName' : "lms1800r03rptservcie"
	                                 		}
	                                 	 });
	                                 	 $.thickbox.close();
	                                 }
	                             },
	                             "cancel": function(){
	                                 $.thickbox.close();
	                             }
	                         }
	                     });
	                	 
	                 }	             	             
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             }
	         }
	    });
    	
    }).end().find("#btnDelete").click(function(){//刪除  	
    	API.confirmMessage(i18n.def["confirmDelete"], function(result){
            if (result) {
            	var oid_arr = gridSelectOidArr();
            	var opts = {};
            	if(oid_arr.length==0){   	 		
           	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
        	 		return;
        	 	}
            	var failmsgarr = [];
            	var cnt = 0;
           	 	$.each(oid_arr, function(idx, oid){
               	 	$.ajax({
                        type: "POST",
                        handler: _handler, action: "deleteMeta",
                        data: $.extend({
                        	mainOid: oid, 
                        	mainDocStatus: viewstatus 
                        }, (opts || {})),                
                        success: function(json){
                        	cnt++;
                        	
                        	if(json.failmsg && json.failmsg.length>1){
                        		failmsgarr.push(json.failmsg);
                        	}
                        	
                        	if(cnt>=oid_arr.length){
                        		grid.trigger("reloadGrid");
                        		$.thickbox.close();
                        		
                        		if(failmsgarr.length==0){
                        			API.showMessage(i18n.def.confirmDeleteSuccess);
                        		}else{
                        			API.showErrorMessage(failmsgarr.join("<br/><br/>"));                        			
                        		}
                        	}
                        }
                    });
           	 	});
        	}
    	});
    }).end().find("#btnMaintain").click(function(){//覆審控制檔維護
    	var v_h = 100;
    	if($("#only900_1").css("display")!="none"){
    		v_h += 100;
    	}
    	if($("#only900_5").css("display")!="none"){
    		v_h += 100;
    	}
    	$("#cover").thickbox({
            //'覆審控制檔維護'
            title: i18n.abstracteloan['button.Maintain'],
            width: 450,
            height: v_h,
            modal: true,
            align: 'center',
            valign: 'bottom',
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                	var val_radioA = $("[name=radioA]:checked").val();
                	if (val_radioA == 0) {
                        //檢查是否更新完畢
                        $.ajax({ handler: _handler, type:"POST", data:{formAction: "checkL180M01Z"}
                        	,success: function(obj){
                        	}
                        });
                    } else if (val_radioA == 1) {//覆審控制檔-海外
                    	$.ajax({
                            handler: "lms1805formhandler",
                            type: "POST",
                            dataType: "json",
                            data: {
                                formAction: "updateElf412",
                                updateOrNot: $("[name=radioB]:checked").val()
                            },
                            success: function(obj){
                                grid.trigger("reloadGrid");
                            }
                        });
                    } else if (val_radioA == 2) {//覆審控制檔-國內
                    	delL180M01ZAction().done(function(){
                    		var my_timeout = 7200000;//ms
                    		if(true){//先延長 timer，不然在處理過程中，會 timeout
                				timer_long_ajax_beg(my_timeout);	
                			}
                    		$.ajax({
                                handler: _handler, action: "callBatch", timeout: my_timeout,
                                data: {'act':'StartGenerateCTLList'
                                	, 'limit_branch': userInfo.unitNo
                                	, 'jq_timeout': (my_timeout/1000)},
                                success: function(json_callBatch){
                                	if(true){//恢復 timer
                                		timer_long_ajax_end();
                  					}
                                	
                                	if(json_callBatch.r.response==="SUCCESS"){
                                		API.showMessage(i18n.def.runSuccess);
                                	}else{
                                		API.showErrorMessage(json_callBatch.r.response);
                                	}
                        		}
                            });
                    	});                    	
                    }
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }).end().find("#btnAllSend").click(function(){//整批覆核
    	var _id = "_div_btnAllSend";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");		
			dyna.push("<form id='"+_form+"'>");
			
			var submenu = {
					  '1':"核定"
					, '2':"退回編製中"
					, '3':"取消"
					};
			build_submenu(dyna, 'decision_btnAllSend', submenu);
			
			dyna.push("</form>");
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 350, height: 200, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!$("#"+_form).valid()) {
                        return;
                    }
                    var val = $("#"+_form).find("[name='decision_btnAllSend']:checked").val();
                                      
                    if(val=="1" || val=="2"){
                    	var oid_arr = gridSelectOidArr();
                    	var opts = {};
                    	if(val=="1"){
                    		opts['decisionExpr'] = '核定';
                    	}else if(val=="2"){
                    		opts['decisionExpr'] = '退回';
                    	}
                   	 	if(oid_arr.length==0){   	 		
                   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
                	 			return;
                	 	}
                   	 	$.each(oid_arr, function(idx, oid){
	                   	 	$.ajax({
	                            type: "POST",
	                            handler: _handler, action: "flowAction",
	                            data: $.extend({
	                            	mainOid: oid, 
	                            	mainDocStatus: viewstatus 
	                            }, (opts || {})),                
	                            success: function(json){
	                            	if(idx==oid_arr.length-1){
	                            		grid.trigger("reloadGrid");
	                            		$.thickbox.close();
	                            	}
	                            }
	                        });
                   	 	});
                    }else if(val=="3"){
                    	$.thickbox.close();	
                    }                
                },
                "cancel": function(){
                	$.thickbox.close();
                }
            }
	    });
    }).end().find("#btnExceptRetrialDate").click(function(){//修改預計覆審日 	
    	var $frm = $("#_div_modify_defaultCTLDate_form");
    	$("#_div_modify_defaultCTLDate").thickbox({ // 使用選取的內容進行彈窗
	        title: "", width: 350, height: 100, align: "center", valign: "bottom", modal: false, i18n: i18n.def,
	        buttons: {
	             "sure": function(){
	                 if (! $frm.valid()) {
	                     return;
	                 }
	                 var defaultCTLDate = $frm.find("#n_defaultCTLDate").val();
	                 
	                 var oid_arr = gridSelectOidArr();
                	 if(oid_arr.length==0){   	 		
                		 API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
             	 		 return;
             	 	 }
                	 	
                	 $.ajax({
	                     handler: _handler, type: "POST", dataType: "json",
	                     data: {
	                         formAction: "saveCtlDate",
	                         'mainDocStatus':viewstatus,
	                         'defaultCTLDate': defaultCTLDate,
	                         'oids': oid_arr.join("|")
	                     },
	                     success: function(obj){
	                    	 grid.trigger("reloadGrid");
	                     }
	                 });
	                 $.thickbox.close();	                 	             	             
	             },
	             "cancel": function(){
	             	$.thickbox.close();
	             }
	         }
	    });
    	
    }).end().find("#btnSendRetrialReport").click(function(){//傳送分行覆審報告表
    	API.confirmMessage(i18n.def["flow.confirmSend"], function(result){
            if (result) {
            	var oid_arr = gridSelectOidArr();
            	if(oid_arr.length==0){   	 		
           	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
        	 			return;
        	 	}
            	var my_dfd = $.Deferred();
            	my_dfd.done(function(json){
            		var theQueue = $({});
            		var my_timeout = 7200000;//ms
            		var cnt = 0;
            		$.each(oid_arr,function(k, oid) {                                            			
            			theQueue.queue('myqueue', function(next) {
            				
            				if(true){//先延長 timer，不然在處理過程中，會 timeout
            					timer_long_ajax_beg(my_timeout);	
            				}
            				$.ajax({
            					handler: _handler, action: "callBatch", timeout: my_timeout
                                , data:{'act':'sendBr_genL170M01A'
                                	, 'mainOid': oid 
                                	, 'autoGetLoanData': json.autoGetLoanData
                                	, 'decisionExpr':'完成結案'
                            		, 'userId': userInfo.userId
                    	        	, 'unitNo': userInfo.unitNo
                    	        	, 'unitType': userInfo.unitType
                                	, 'jq_timeout': (my_timeout/1000)
                                	, mainDocStatus: viewstatus
                                },                
            		            success: function(json){ }
            		        }).complete(function(){//用 complete(不論done, fail)
            		        	cnt++;
              					if(true){//恢復 timer
              						timer_long_ajax_end();
              					}
                  				if(cnt==oid_arr.length){
                  					//把 reload 移到最後再做，避免 reload 時 lock 住
                  					grid.trigger("reloadGrid");
                  					
                  					API.showMessage(i18n.def.runSuccess);
                  				}
              					//---
              					//把 next() 寫在 finish 的 callback 裡
              					//才會 1個 url 抓到 response 後,再get下1個
              					//不然會1次跳 N 個出來
              					next();
            		        });				
            			});                                            			 
            		});
            		theQueue.dequeue('myqueue');
            		
            	});    		
            	
            	/*API.confirmMessage("傳送名單到分行時，是否要自動引進授信帳務明細 (耗時較久)？", function(result){
            		var json = {};
            		//在 server 端定義在 LrsUtil.K_GET_LOAN_DATA
            		json['autoGetLoanData'] = result?"Y":"N";
            		my_dfd.resolve( json );
            	});*/
            	my_dfd.resolve( {'autoGetLoanData':'N'} );
        	}
    	});
    }).end().find("#btnReturnToCompiling").click(function(){//退回編製中    	
    	var oid_arr = gridSelectOidArr();        	
    	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	 		return;
	 	}
    	API.confirmMessage(i18n.def["confirmReturn"], function(result){
            if (result) {
            	var opts = {'decisionExpr':'退回'};
            	
            	if(viewstatus=="030"){//由 已核准 退回    		
            		$.each(oid_arr, function(idx, oid){
                   	 	$.ajax({
                            type: "POST",
                            handler: _handler, action: "flowAction",
                            data: $.extend({
                            	mainOid: oid, 
                            	mainDocStatus: viewstatus 
                            }, (opts || {})),                
                            success: function(json){
                            	grid.trigger("reloadGrid");
                            }
                        });
               	 	}); 	
            	}else if(viewstatus=="0A0"){
            		$.each(oid_arr, function(idx, oid){
                   	 	$.ajax({
                            type: "POST",
                            handler: _handler, action: "end_to_01A",
                            data: $.extend({
                            	mainOid: oid, 
                            	mainDocStatus: viewstatus 
                            }, (opts || {})),                
                            success: function(json_end_to_01A){
                            	if(json_end_to_01A.passedFlag=="Y"){
                            		grid.trigger("reloadGrid");
                            	}
                            }
                        });
               	 	}); 
            	}
            }
        });    	
    }).end().find("#btnSendBtt").click(function(){//重新上傳名單到BTT 	
    	var oid_arr = gridSelectOidArr();
    	var opts = {};
    	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	 			return;
	 	}
   	 	$.each(oid_arr, function(idx, oid){
       	 	$.ajax({
                type: "POST",
                handler: _handler, action: "sendBtt",
                data: $.extend({
                	mainOid: oid, 
                	mainDocStatus: viewstatus 
                }, (opts || {})),                
                success: function(json){
                	API.showMessage(i18n.def.runSuccess);                	
                }
            });
   	 	});
    }).end().find("#btnProduceEvaluateTbl").click(function(){//產生覆審考核表
    	var oid_arr = gridSelectOidArr();
   	 	if(oid_arr.length==0){   	 		
   	 		API.showMessage(i18n.def.action_005);//action_005=請先選取一筆以上之資料列
	 		return;
	 	}
   	 
	    //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
		//確認新增覆審名單種類
		$.ajax({
            type: "POST",
            handler: _handler,
            data: {	 'formAction': 'getCtlTypeByBrNo'
            },
            success: function(responseData){
				 var ctlType =responseData.ctlType;
				 
				//J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
				 build_selItem(responseData.selItem, responseData.selItemOrder, ".", true).done(function(){
					//自辦+主辦都有
					 	$("#_choiceCtlType").thickbox({
					        title: i18n.lms1800m01['L180M01B.choiceCtlType'], //"請選取覆審名單種類",
					        width: 400,
				            height: 100,
				            align: "center",
				            valign: "bottom",
				            modal: false,
				            i18n: i18n.def,
				            buttons: {
				                "sure": function(){
				                	
				                	if(ctlType == "Z"){
										//ctlType = $("input[name='_ctlType']:radio:checked").val();
										ctlType = $("#_ctlType").val();
									}
				                	
									
									$.thickbox.close();
									var fileName = "1.pdf"
							    	$.form.submit({
							        	url: "../simple/FileProcessingService",
							     		target : "_blank",
							     		data : {
							     			'mode':'A', 
							     			'oids':oid_arr.join("|"), 
							     			'fileDownloadName' : fileName,
							     			'serviceName' : "lms1800r03rptservcie",
											'ctlType' : ctlType
							     		}
							     	 });
				                },
				                "cancel": function(){
				                    $.thickbox.close();
				                }
				            }
					    });
				 });
				 
//				 if(ctlType =="Z"){
//				 	
//					
//				 }else{
//				 	var fileName = "1.pdf"
//			    	$.form.submit({
//			        	url: "../simple/FileProcessingService",
//			     		target : "_blank",
//			     		data : {
//			     			'mode':'A', 
//			     			'oids':oid_arr.join("|"), 
//			     			'fileDownloadName' : fileName,
//			     			'serviceName' : "lms1800r03rptservcie",
//							'ctlType' : ctlType
//			     		}
//			     	 });
//					 
//				 }            	

            }
        });   
		
		
		//**********************************
    	
    	
    });
    
    /**
     * 定義在 LMS1805V01Page.html  LMS1805V02Page.html
     */
    function thickBoxFilter(){
        
        $("#filterForm").reset();
        $("#lms180Filter").thickbox({ // '新增覆審報告表',
            title: i18n.lms1805v01['L180M01a.fileter'],
            width: 450, height: 180, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("#filterForm").valid()) {
                        if ($("#dataDateFilter").val() == '') {
                            //return CommonAPI.showMessage(i18n.lms1805v01["L180M01a.error1"]+"："+i18n.lms1805v01["L180M01A.dataDate"]);
                        }else{
                        	if (!$("#dataDateFilter").val().match(regYYYYMM)) {                        
	                            //val.date2=日期格式錯誤(YYYY-MM)
	                            return CommonAPI.showMessage(i18n.def["val.date2"]);
	                        }
                        }
                        
                        $("#gridview").jqGrid("setGridParam", {
                            postData: {
                                dataDate: $("#dataDateFilter").val(),
                                custId: $("#custIdFilter").val(),
                                brId: $("#brIdFilter").val()
                            },
                            page: 1,
                            search: true
                        }).trigger("reloadGrid");
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function build_submenu(dyna, rdoName, submenu){
		$.each(submenu, function(k, v) { 
			dyna.push("   <p ><label id='_itemMenu_"+rdoName+"_"+k+"'><input type='radio' name='"+rdoName+"' value='"+k+"' class='required' />"+v+"</label></p>"); 
        });		
	}
    
    function gridSelectOidArr(){
    	var rowId_arr = $("#gridview").getGridParam('selarrrow');
		var oid_arr = [];
   	 	for (var i = 0; i < rowId_arr.length; i++) {
			var data = $("#gridview").getRowData(rowId_arr[i]);
			oid_arr.push(data.oid);    			
        }
   	 	return oid_arr;
    }
    
    function delL180M01ZAction(){
    	var my_dfd = $.Deferred();
    	if($("[name=updFlg]:checked").val()=="N"){
    		$.ajax({ handler: _handler, type:"POST", data:{formAction: "delL180M01Z"}
        		,success: function(obj){
        			my_dfd.resolve();		
        		}
        	});
    	}else{
    		my_dfd.resolve();
    	}
    	return my_dfd.promise();
    }
    
    function produceList_S(){
    	//借用 篩選 的 div
    	$("#lms180Filter").thickbox({
            title: "",
            width: 450, height: 180, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if ($("#filterForm").valid()) {
                    	var dataDate = $("#dataDateFilter").val();
                        var brId =  $("#brIdFilter").val();
                        
                        if (dataDate == '') {
                            return CommonAPI.showErrorMessage("請輸入：資料年月");
                        }else{
                        	if (! dataDate.match(regYYYYMM)) {                        
	                            //val.date2=日期格式錯誤(YYYY-MM)
	                            return CommonAPI.showErrorMessage(i18n.def["val.date2"]);
	                        }
                        }
                        
                        if(brId == '') {
                        	return CommonAPI.showErrorMessage("請輸入：分行");                        	
                        }
                        genL180M01A(brId+"^"+dataDate).done(function(){
    	    				grid.trigger("reloadGrid");
    	    				API.showMessage(i18n.def.runSuccess);
    	        		});
                           
                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }

    function produceList_M(){
    	$("#tr_brlist").show();
    	var tb_w = 760; 
    	var tb_h = 400;
    	//借用 產生xls 的 div
    	$("#div_status010_produceXls").thickbox({
            title: "",
            width: tb_w, height: tb_h, align: 'center', valign: 'bottom', modal: true, i18n: i18n.def,
            buttons: {
                "sure": function(){
               	 	var $frm = $("#form_status010_produceXls"); 
                    if ($frm.valid()) {
                    	var dateVal = $("#dataDate_status010_produceXls").val();
                        if (dateVal == '') {
                            return CommonAPI.showErrorMessage("請輸入：資料年月");
                        }else{
                        	if (!dateVal.match(regYYYYMM)) {                        
	                            //val.date2=日期格式錯誤(YYYY-MM)
	                            return CommonAPI.showErrorMessage(i18n.def["val.date2"]);
	                        }
                        }
                       
                       	var obj_arr = $frm.find("input:checkbox:checked").map(function(){ 
                       		return ($(this).val()+"^"+dateVal); 
                       	}).get();
                       	if(obj_arr.length==0){
                       		return CommonAPI.showErrorMessage("請輸入：分行");
                       	}
                       	genL180M01A(obj_arr.join("|")).done(function(){
    	    				grid.trigger("reloadGrid");
    	    				API.showMessage(i18n.def.runSuccess);
    	        		});

                        $.thickbox.close();
                    }
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
    function checkExistL180M01Z_010(par_arr){
    	var my_dfd = $.Deferred();
    	$.ajax({
            handler: _handler, action: "checkExistL180M01Z_010",
            data: {'par_arr': par_arr, 'unitNo': userInfo.unitNo},
            success: function(json){
            	if(json.isMsg=="Y"){
            		API.showPopMessage("", json.msg,function(){
            			my_dfd.resolve(json);
      				});
            	}else{
            		my_dfd.resolve(json);	
            	}        	        		
    		}
        });
    	return my_dfd.promise();
    }
    
    function genL180M01A(par_arr){
    	var my_dfd = $.Deferred();
    	
    	checkExistL180M01Z_010(par_arr).done(function(json){
    		if(json.allSkip=="Y"){
    			my_dfd.reject();
    		}else{
    			var my_timeout = 7200000;//ms
    			if(true){//先延長 timer，不然在處理過程中，會 timeout
    				timer_long_ajax_beg(my_timeout);	
    			}
        		$.ajax({
        	        handler: _handler, action: "callBatch", timeout: my_timeout,
        	        data: {'act':'genL180M01A'
        	        	, 'par_arr': par_arr
        	        	, 'userId': userInfo.userId
        	        	, 'unitNo': userInfo.unitNo
        	        	, 'unitType': userInfo.unitType
        	        	, 'jq_timeout': (my_timeout/1000)},
        	        success: function(json_callBatch){
        	        	if(true){//恢復 timer
                    		timer_long_ajax_end();
      					}
        	        	
        	        	if(json_callBatch.r.response==="SUCCESS"){
        	        		my_dfd.resolve();
        	        	}	
        			}
        	    });	
    		}
    	});
		
    	return my_dfd.promise();
    }
    
    function check_inExePriod(){
    	var my_dfd = $.Deferred();    
    	$.ajax({
	        handler: _handler, action: "inExePriod", 
	        data: {},
	        success: function(json_inExePriod){
	        	my_dfd.resolve();
			}
	    });	
    	return my_dfd.promise();
    }
});

function addZero(cellvalue, otions, rowObject){
    var itemName = '';
    if (cellvalue) {
        itemName = '000' + cellvalue;
        itemName = itemName.substr(itemName.length - 3, 3);
    }
    return itemName;
}

function build_selItem(json_selItem, json_selItemOrder, sep, doCopy){

    //J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	var my_dfd = $.Deferred();
	$.each(json_selItem, function(itemName, kvMap) {
		var chooseItem = $("#"+itemName);
		var _addSpace = false;
		if(chooseItem.attr("space")=="true"){
			_addSpace = true;
		}
		
		var _fmt = "{key}";
		if(chooseItem.attr("myShowKey")==="Y"){
			_fmt = "{value}"+sep+"{key}";
		}
		$.each(json_selItemOrder[itemName], function(idx, kVal) {
			var currobj = {};
    		currobj[kVal] = kvMap[kVal];
    		
    		chooseItem.setItems({ item: currobj, format: _fmt, clear:false, space: (_addSpace?(idx==0):false) });	    		
		});			
//		$("#"+itemName).setOptions(kvMap, false);
		//---
		//copy html element
		if(doCopy){
			$("#"+itemName+"_1").html( chooseItem.html() );	
		}

	});
	
	//J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	my_dfd.resolve();
	return my_dfd.promise();
	
		
}
