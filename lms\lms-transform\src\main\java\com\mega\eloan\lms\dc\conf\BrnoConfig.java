package com.mega.eloan.lms.dc.conf;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;

public class BrnoConfig {
	private static Logger logger = LoggerFactory.getLogger(BrnoConfig.class);

	private static final String CONFIG_FILE = "lmsdc/conf/brno.properties";

	private static Map<String, String> mapNameVal = new LinkedHashMap<String, String>();
	private static Map<String, String> mapBrnoVal = new LinkedHashMap<String, String>();

	private static BrnoConfig config = new BrnoConfig();

	public static BrnoConfig getInstance() {
		return config;
	}

	private BrnoConfig() {
		try {
			this.load();
		} catch (Exception ex) {
			throw new DCException("讀取Config設定檔錯誤！", ex);
		}
	}

	private void load() throws Exception {
		Configuration conf = new PropertiesConfiguration(CONFIG_FILE);

		Iterator<String> itor = conf.getKeys();
		String key;
		String value;
		while (itor.hasNext()) {
			key = StringUtils.trimToEmpty(itor.next());
			value = StringUtils.trimToEmpty(conf.getString(key));
			mapNameVal.put(key, value);
			mapBrnoVal.put(value, key);

			if (logger.isDebugEnabled()) {
				logger.debug("key=" + key + ",value=" + value);
			}
		}
	}

	public String getBrNo(String brName) {
		return mapBrnoVal.get(brName);
	}

	public String getBrName(String brNo) {
		return mapNameVal.get(brNo);
	}

	/**
	 * 取得所有分行代碼
	 * 
	 * @return List 2013-01-30 Add By Bang
	 */
	public List<String> getBrnoList() {
		List<String> keyList = new ArrayList<String>(mapBrnoVal.values());
		return keyList;
	}

	@Override
	public String toString() {
		StringBuffer str = new StringBuffer();
		for (Map.Entry<String, String> entry : mapNameVal.entrySet()) {
			str.append(entry.getKey()).append("=").append(entry.getValue())
					.append("\n");
		}
		return str.toString();
	}

}
