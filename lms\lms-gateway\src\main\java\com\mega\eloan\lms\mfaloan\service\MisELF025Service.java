package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * 核准額度資料檔  MIS.ELF447n
 * </pre>
 * 
 * @since 2012/6/8
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/6/8,REX,new
 *          </ul>
 */
public interface MisELF025Service {

	public void insert(String ELF025_SDATE, String ELF025_MAINID,
			String ELF025_BRANCH, String ELF025_CASEDATE,
			String ELF025_DOCUMENT_NO, String ELF025_CONTRACT_CO,
			String ELF025_CONTRACT, String ELF025_SWFT,
			BigDecimal ELF025_FACT_AMT, String ELF025_UPDATER);

	/**
	 * 刪除by mainId
	 * 
	 * @param mainId
	 *            簽報書mainid
	 * 
	 */
	public void deleteByMainId(String mainId);

}
