/* 
 * IGridResult.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package tw.com.iisi.cap.response;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.formatter.IFormatter;

/**
 * <pre>
 * IGridResult for jqGird result
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2011/10/27,iristu,new
 *          </ul>
 * @param <E>
 * @param <T>
 */
public interface IGridResult<T, E> extends IResult {

    /**
     * <pre>
     * 設定頁碼
     * </pre>
     * 
     * @param page
     *            頁碼
     * @return this
     */
    T setPage(int page);

    /**
     * <pre>
     * 設定總頁數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @param pageRows
     *            一頁筆數
     * @return this
     */
    T setPageCount(int rowCount, int pageRows);

    /**
     * <pre>
     * 設定總筆數
     * </pre>
     * 
     * @param rowCount
     *            總筆數
     * @return this
     */
    T setRecords(int rowCount);

    /**
     * <pre>
     * 取得總筆數
     * </pre>
     * 
     * @return 總筆數
     */
    Integer getRecords();

    /**
     * <pre>
     * 設定資料列
     * </pre>
     * 
     * @param rowData
     *            資料
     * @return this
     */
    T setRowData(List<? extends E> rowData);

    /**
     * 取得資料列
     * 
     * @return
     */
    List<? extends E> getRowData();

    /**
     * 設定欄位
     * 
     * @param columns
     */
    void setColumns(String[] columns);

    /**
     * 設置 Data Reformatter
     * 
     * @param dataReformatter
     */
    void setDataReformatter(Map<String, IFormatter> dataReformatter);

    /**
     * 取得 Data Reformatter
     */
    Map<String, IFormatter> getDataReformatter();

}
