// 國內綜合評估敘作理由企個金頁籤共用js

var initS07aJson = { 
    docCode: responseJSON.docCode,
    docType: responseJSON.docType,
    // 個金案件時的頁籤設定
    personSet: function(obj){
        if (obj.docType == '2') {
            $(".personHide").hide();
            //逾催呆報告表應該性質為協商案件才要出現
            if (obj.ngFlag == "0" || responseJSON.docCode == '4') {
                $(".personShow").hide();
            } else {
                $(".personShow").show();
                
            }
        }
    },
    // 一般案件時的讀取頁面
    lms1201Init: function(){
        var pageUrl = "../../cls/clss07aA";
        loadHtml(pageUrl, "7a");
        $("#lmss07a").click(function(){
            pageUrl = "../../cls/clss07aA";
            loadHtml(pageUrl, "7a");
        });        
    },
    // 陳復述案時的讀取頁面
    lms1301Init: function(){
        if (responseJSON.docCode == "3") {
            $("#lms4c").hide();
            $("#lms4d").hide();
        } else {
            $("#lms4c").show();
            $("#lms4d").show();
        }
        
        $("#lms4b").click(function(){
            pageUrl = "../../cls/clss07aC";
            loadHtml(pageUrl, "7c");
        });
        $("#lms4c").click(function(){
            pageUrl = "../../cls/clss07aD";
            loadHtml(pageUrl, "7d");
        });
        $("#lms4d").click(function(){
            pageUrl = "../../cls/clss07aB";
            loadHtml(pageUrl, "7b");
        });
        $("#lmss07f").click(function(){
            pageUrl = "../../cls/clss07aE";
            loadHtml(pageUrl, "7f");
        });
    },
    // 逾催呆報告表相關Grid與程式讀取
    loadFileGrid: function(){
        //上傳檔案按鈕
        $("#uploadFile6").click(function(){
            var limitFileSize = 3145728;
            MegaApi.uploadDialog({
                fieldId: "upFile1305",
                fieldIdHtml: "size='30'",
                fileDescId: "fileDesc",
                fileDescHtml: "size='30' maxlength='30'",
                subTitle: i18n.def('insertfileSize', {
                    'fileSize': (limitFileSize / 1048576).toFixed(2)
                }),
                limitSize: limitFileSize,
                width: 320,
                height: 190,
                data: {
                    mainId: $("#mainId").val()
                },
                success: function(obj){
                    $("#gridfile6").trigger("reloadGrid");
                }
            });
        });
        
        //刪除檔案按鈕
        $("#deleteFile6").click(function(){
            var select = $("#gridfile6").getGridParam('selrow');
            if (select == "" || select == null || select == undefined) {
                // TMMDeleteError=請先選擇需修改(刪除)之資料列
                CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                return;
            }
            // confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    var data = $("#gridfile6").getRowData(select);
                    $.ajax({
                        handler: "lms1301formhandler",
                        type: "POST",
                        dataType: "json",
                        data: {
                            formAction: "deleteUploadFile",
                            fileOid: data.oid
                        },
                        success: function(obj){
                            $("#gridfile6").trigger("reloadGrid");
                        }
                    });
                } else {
                    return;
                }
            });
        });
        
        //檔案上傳grid
        $("#gridfile6").iGrid({
            handler: 'lms1201gridhandler',
            height: 150,
            sortname: 'srcFileName',
            postData: {
                formAction: "queryfile",
                fieldId: "upFile1305",
                mainId: responseJSON.mainId
            },
            caption: "&nbsp;",
            hiddengrid: false,
            rowNum: 15,
            //multiselect : true,
            colModel: [{
                colHeader: i18n.cls1301s05['L120M01D.grid1'],//原始檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: false,
                formatter: 'click',
                onclick: openDocFile
            }, {
                colHeader: i18n.cls1301s05['L120M01D.grid2'],//檔案說明
                name: 'fileDesc',
                width: 140,
                sortable: false
            }, {
                colHeader: i18n.cls1301s05['L120M01D.grid3'],//上傳時間
                name: 'uploadTime',
                width: 140,
                sortable: false
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
};

initDfd.done(function(obj){
    $("#lmss07b").click(function(){
        pageUrl = "../../cls/clss07aB";
        loadHtml(pageUrl, "7b");
    });
    
    $("#lmss07c").click(function(){
        pageUrl = "../../cls/clss07aC";
        loadHtml(pageUrl, "7c");
    });
    

    $("#lmss07d").click(function(){
        pageUrl = "../../cls/clss07aD";
        loadHtml(pageUrl, "7d");
    });
    
    $("#lmss07f").click(function(){
        pageUrl = "../../cls/clss07aE";
        loadHtml(pageUrl, "7f");
    });
    
    setCloseConfirm(true);
    if (initS07aJson.docCode == '1') {
        // 一般案件
        initS07aJson.lms1201Init();
    } else {
        var pageUrl = "../../cls/clss07aA";
        loadHtml(pageUrl, "7a");
        if (initS07aJson.docCode != '5') {//5-團貸案
            $("#lmss07b").hide();
            $("#lmss07c").hide();
        }
        
    }
    initS07aJson.personSet(obj);
    initS07aJson.loadFileGrid();
    
    if(responseJSON.typCd == "1" || responseJSON.typCd == "4"){
		$("#LMS1205S07Form03 #seaHide").show();
	}else{
		$("#LMS1205S07Form03 #seaHide").hide();
	}
});

var haveloadPage = {};

function loadHtml(pageUrl, page){
    responseJSON["handler"] = "cls1141m01formhandler";
    responseJSON["s07page"] = page;
    
    ilog.debug("CLSS07APage.js > prepare_to_loadHtml, haveloadPage[page="+page+"]="+(haveloadPage[page]));
    if (!haveloadPage[page]) {
        haveloadPage[page] = true;
        $("#tab-" + page).load(pageUrl, function(){   
        	
        	ilog.debug("CLSS07APage.js > after loadHtml, call ajax queryL120S07[page="+page+"]");
            //查詢分頁資料
            $.ajax({
                handler: responseJSON["handler"],//cls1141m01formhandler
                type: "POST",
                dataType: "json",
                action: "queryL120S07",
                data: {
                    mainId: responseJSON.mainId,
                    page: page
                },
                success: function(jsonInit){
                    if (page == "7a") {
                        setCkeditor2("ffbody", jsonInit.L120M01dForm04.ffbody, '500', '900');
                        setCkeditor2("clsMinorPurpose", jsonInit.L120M01dForm04.clsMinorPurpose, '500', '200');
                        setCkeditor2("clsMinorLegality", jsonInit.L120M01dForm04.clsMinorLegality, '500', '200');
                        $("#L120M01dForm04").find("#l140s02amessege").val(jsonInit.L120M01dForm04.l140s02amessege);
                        //$("#L120M01dForm04").find("#ffbody").val(jsonInit.L120M01dForm04.ffbody);
                    } else if (page == "7c") {
                    	var $keyCustIdDupNoSel = $("#keyCustIdDupNoSel");
                    	
                    	$("#LMS1205S07Form03").setData(jsonInit.LMS1205S07Form03, false);
                        
                        if(jsonInit.keyCustIdDupNoArr && jsonInit.keyCustIdDupNoArr.length>=1){
                        	$.each( jsonInit.keyCustIdDupNoArr, function( idx,obj ) {
//                            	var tmp = $("<option></option>");
//                                tmp.attr("value", DOMPurify.sanitize(idx));
//                                
//                                	tmp.attr("keyCustId" , DOMPurify.sanitize(obj.CUSTID));
//                                    tmp.attr("keyDupNo" , DOMPurify.sanitize(obj.DUPNO));
//                                    tmp.attr("keyCustName" , DOMPurify.sanitize(obj.CUSTNAME));	
//                                                                                       
//                                tmp.text(DOMPurify.sanitize(obj.CUSTID)+"-"+DOMPurify.sanitize(obj.DUPNO)+"  "+DOMPurify.sanitize(obj.CUSTNAME));
//                                $keyCustIdDupNoSel.append(tmp);
                                $keyCustIdDupNoSel.append("<option "+ 
                                		"value='"+DOMPurify.sanitize(idx)+"' "+
                                		"keyCustId='"+DOMPurify.sanitize(obj.CUSTID)+"' "+
                                		"keyDupNo='"+DOMPurify.sanitize(obj.DUPNO)+"' "+
                                		"keyCustName='"+DOMPurify.sanitize(obj.CUSTNAME)+"' "+
                                		">"+
                                		DOMPurify.sanitize(obj.CUSTID)+"-"+DOMPurify.sanitize(obj.DUPNO)+"  "+DOMPurify.sanitize(obj.CUSTNAME)+"</option>");                        	
                			});
	
                        	if($keyCustIdDupNoSel.find("option:selected").length > 0){
                        		reload_7C_withKeyCustIdDupNo();
                            }else{
                            	//no selected                            	
                            }
                        }else{
                        	$keyCustIdDupNoSel.html("<option>尚未產生額度序號</option>");
                        	disabled7C();
                        }                        
                    } else if (page == "7d") {                    	
                    	if(jsonInit.prodKindArr){
                    		// 參考 CLS1151S05Panel.js 的寫法 
                    		// 把 subjCode 當作 option 的一個 attribute
                    		var temp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                    		$.each(jsonInit.prodKindArr, function(idx, value) {
                    			/*
                    			 * J-111-0182 提案人  郭守明 科長 說，針對「不能簽的產品」直接隱藏，而不是出現但反灰
                    			 */                    			
                    			if(value["isCanCel"] == "Y" ){
                    				
                    			}else{
                    				temp += "<option value='" + value["key"] + "' subjectData='" + JSON.stringify(value["subjectData"]) + "' >" + value["key"] + "-" + value["name"] + "</option>";	
                    			}                                
                    		});
                            
                            $("#formSearch_l120s06b_type2_OrderByRate").find("#formSearch_l120s06b_type2_OrderByRate_prodKind").html(temp);
                    	}
                   	 	if(jsonInit.lnPursArr){        		 	 
                   	 		$.each(jsonInit.lnPursArr, function(idx, obj) {
                   	 			var currobj = {};         	
                   	 			currobj[obj.key] = obj.value ;
                   	 			$("#formSearch_l120s06b_type2_OrderByRate").find("#formSearch_l120s06b_type2_OrderByRate_lnPurs").setItems({ item: currobj, format: "{value} - {key}", clear:(idx==0?true:false), space:(idx==0?true:false) });
                   	 		});        		 
                   	 	}
                    }
                    
                    var $showBorrowData = $("#showBorrowData");
                    $showBorrowData.reset();
                    $showBorrowData.setData(jsonInit.showBorrowData, false);
                }
            });
            
            if (CLSAction.isReadOnly()) {
                if (page == "7a") {
                    var $L120M01dForm04 = $("#L120M01dForm04");
                    $L120M01dForm04.readOnlyChilds(true);
                    $L120M01dForm04.find("button").hide();
                } else if (page == "7b") {
                    var $LMS1205S07Form02 = $("#LMS1205S07Form02");
                    var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                    $LMS1205S07Form02.readOnlyChilds(true);
                    $LMS1205S07Form02.find("button").hide();
                    $tLMS1205S07Form02.readOnlyChilds(true);
                    $tLMS1205S07Form02.find("button").hide();
                } else if (page == "7c") {
                	disabled7C();
                    
                } else if (page == "7d") {
                    var $LMS1205S07Form04 = $("#LMS1205S07Form04");
                    var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
                    $LMS1205S07Form04.readOnlyChilds(true);
                    $LMS1205S07Form04.find("button").hide();
                    $tLMS1205S07Form04.readOnlyChilds(true);
                    $tLMS1205S07Form04.find("button").hide();
                } else if (page == "7f") {
                	var $tLMS1205S07Form06_btn = $("#tLMS1205S07Form06_btn");
                    $tLMS1205S07Form06_btn.find("button").hide();
                }
            }
            
            if (page == "7c") {
            	$("#keyCustIdDupNoSel").change(function(k, v){
            		reload_7C_withKeyCustIdDupNo();
            	});
            }else if (page == "7d") {
            	$("#formSearch_l120s06b_type2_OrderByRate").find("#formSearch_l120s06b_type2_OrderByRate_prodKind").change(function(k, v){
            	//產生授信科目項目
                var subjectData = JSON.parse($(this).find(":selected").attr("subjectData")  || "{}"); //當prodKind=i18n.def.comboSpace 時,讓 subjectData={}
                var subjectTemp = "<option value=''>" + i18n.def.comboSpace + "</option>";
                for (var i in subjectData) {
                    var subjCode = DOMPurify.sanitize(subjectData[i].subjCode);
                    var subjCode2 = DOMPurify.sanitize(subjectData[i].subjCode2);
                    var subjNm = DOMPurify.sanitize(subjectData[i].subjNm);
                    
                    subjectTemp += "<option value='" + subjCode + "' subjCode2='" + subjCode2 + "' >" + subjCode + "-" + subjNm + "</option>";
                }
                $("#formSearch_l120s06b_type2_OrderByRate").find("#formSearch_l120s06b_type2_OrderByRate_subj").html(DOMPurify.sanitize(subjectTemp));
            	});
            }
            
            // 控制ThickBox唯讀狀態
            thickReadOnly();
        });
    }
}
/**
 * 因每一個 grid
 * 在初始化時, 都有傳入 keyCustId
 * 
 * 在切換 keyCustId 時, 不能只用 reloadGrid(會抓到舊的)
 */
function reload_7C_withKeyCustIdDupNo(){
	var chooseCustInfo = {};
	 
	var keyCustId = "";
	var keyDupNo = "";
	var keyCustName = "";
	$keyCustIdDupNoSel = $("#keyCustIdDupNoSel");
	if($keyCustIdDupNoSel.find("option:selected").length > 0){    	
		keyCustId = $keyCustIdDupNoSel.find("option:selected").attr("keyCustId");
		keyDupNo = $keyCustIdDupNoSel.find("option:selected").attr("keyDupNo");
		keyCustName = $keyCustIdDupNoSel.find("option:selected").attr("keyCustName");    	   	
    }
	chooseCustInfo = {
		'keyCustId': keyCustId,
		'keyDupNo': keyDupNo,
		'keyCustName': keyCustName
	}
	 	
	
	$.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        data: $.extend({}, chooseCustInfo, {
            formAction: "initL120s04a",            
            mainId: responseJSON.mainid
        }),
        success: function(json){
        	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
            $LMS1205S07Form03.find("#queryDateS").text(json.LMS1205S07Form03.queryDateS);
            $LMS1205S07Form03.find("#queryDateE").text(json.LMS1205S07Form03.queryDateE);
            $LMS1205S07Form03.find("#custId").text(json.LMS1205S07Form03.custId);
            $LMS1205S07Form03.find("#dupNo").text(json.LMS1205S07Form03.dupNo);
            $LMS1205S07Form03.find("#custName").text(json.LMS1205S07Form03.custName);
            $LMS1205S07Form03.find("#profit01").text(json.LMS1205S07Form03.profit01);
            $LMS1205S07Form03.find("#loanQuota01").text(json.LMS1205S07Form03.loanQuota01);
            $LMS1205S07Form03.find("#loanAvgBal01").text(json.LMS1205S07Form03.loanAvgBal01);
            $LMS1205S07Form03.find("#depTime01").text(json.LMS1205S07Form03.depTime01);
            $LMS1205S07Form03.find("#profit02").text(json.LMS1205S07Form03.profit02);
            $LMS1205S07Form03.find("#loanQuota02").text(json.LMS1205S07Form03.loanQuota02);
            $LMS1205S07Form03.find("#loanAvgBal02").text(json.LMS1205S07Form03.loanAvgBal02);
            $LMS1205S07Form03.find("#depTime02").text(json.LMS1205S07Form03.depTime02);
            
        	var arr = [];
        	arr.push( $("#gridview_A-1-8-1") );
        	arr.push( $("#gridview_A-1-8-2") );
        	arr.push( $("#gridviewPare") );
        	
        	$.each( arr, function( idx,obj ) {
        		obj.jqGrid("setGridParam", "postData"
        				, $.extend(
        						obj.jqGrid("getGridParam", "postData")
        						, getKeyCustIdDupNoItem(), {'fieldId':get_uploadfile_fieldId()}));
        		obj.trigger("reloadGrid");
        	});
                	
        }
    });
    
}

function disabled7C(){
	var $LMS1205S07Form03 = $("#LMS1205S07Form03");
    var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
    $LMS1205S07Form03.readOnlyChilds(true);
    $LMS1205S07Form03.find("button").hide();
    $tLMS1205S07Form03.readOnlyChilds(true);
    $tLMS1205S07Form03.find("button").hide();
    
    $("#keyCustIdDupNoSel").removeAttr("disabled");
}

function getKeyCustIdDupNoItem(){	
	var $F = $("#LMS1205S07Form03");
	var keyCustId = $F.find("#custId").val();
	var keyDupNo = $F.find("#dupNo").val();
	var keyCustName = $F.find("#custName").val();
	var object1 = {
			'keyCustId': keyCustId,
			'keyDupNo': keyDupNo,
			'keyCustName': keyCustName
			};	
	return object1;
}
function gridSelectThick(list){
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        data: {
            formAction: "findFfbody",
            mainId: responseJSON.mainId,
            cesMainId: list
        },
        success: function(json){
            setCkeditor2("ffbody", json.ffbody);
        }
    });
}

function getLocalData(){
    var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
    var oid = $tLMS1205S07Form04.find("#oldOid").val();
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "queryL120s06bl",
        data: {
            oid: oid,
            typCd2: $tLMS1205S07Form04.find("#typCd2").val(),
            custId2: $tLMS1205S07Form04.find("#custId2").val(),
            dupNo2: $tLMS1205S07Form04.find("#dupNo2").val(),
            custName2: $tLMS1205S07Form04.find("#custName2").val(),
            requery: true,
            payDeadline2: $tLMS1205S07Form04.find("#payDeadline2").val(),
            guarantor2: $tLMS1205S07Form04.find("#guarantor2").val(),
            purpose2: $tLMS1205S07Form04.find("#purpose2").val(),
            lnSubject2: $tLMS1205S07Form04.find("#lnSubject2").val(),
            cntrNo2: $tLMS1205S07Form04.find("#cntrNo2").val(),
            property2: $tLMS1205S07Form04.find("#property2").val(),
            currentApplyCurr2: $tLMS1205S07Form04.find("#currentApplyCurr2").val(),
            currentApplyAmt2: $tLMS1205S07Form04.find("#currentApplyAmt2").val(),
            gutPercent2: $tLMS1205S07Form04.find("#gutPercent2").val(),
            guarantorMemo2: $tLMS1205S07Form04.find("#guarantorMemo2").val()
        },
        success: function(obj){
            var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
            $tLMS1205S07Form04.setData(obj.tLMS1205S07Form04, false);
            //setData(..., false) 不reset form,而回傳的資料中 itemDscr3_ , itemDscr4_ 即使空的,也不會傳「無」,手動set
            $("#tLMS1205S07Form04").find("itemDscr3").html(DOMPurify.sanitize(obj.tLMS1205S07Form04.itemDscr3));
            $("#tLMS1205S07Form04").find("itemDscr4").html(DOMPurify.sanitize(obj.tLMS1205S07Form04.itemDscr4));
            // $("#tLMS1205S07Form04").find("itemDscr5").html(obj.tLMS1205S07Form04.itemDscr5);// J-112-0124 取消"其他敘做條件"欄位
            $("#tLMS1205S07Form04").find("itemDscrB").html(DOMPurify.sanitize(obj.tLMS1205S07Form04.itemDscrB));
            $("#tLMS1205S07Form04").find("itemDscrC").html(DOMPurify.sanitize(obj.tLMS1205S07Form04.itemDscrC));
        }
    });
}

function inputSearch(){
	prompt_before_import_data( {'target_action': 'imp_L120S04A'} ).done(function(){
	var nowDate = new Date();
    var MM = nowDate.getMonth();
    var YY = nowDate.getFullYear();
    var SMM;
    var SYY;
    if (MM == 0) {
        MM = 12;
    }
    
    if (MM == 12) {
        SMM = MM - 5;
        YY = YY - 1;
        SYY = YY;
    } else if (MM > 5 && MM < 12) {
        SMM = MM - 5;
        SYY = YY;
    } else {
        SMM = MM + 12 - 5;
        SYY = YY - 1;
    }
    
    var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
    $tLMS1205S07Form03b.find("#queryDateS0").val(SYY);
    $tLMS1205S07Form03b.find("#queryDateS1").val(SMM);
    $tLMS1205S07Form03b.find("#queryDateE0").val(YY);
    $tLMS1205S07Form03b.find("#queryDateE1").val(MM);
    $("#inputSearch").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox8"],
        width: 450,
        height: 210,
        modal: true,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.clss07a,
        buttons: {
            "L1205S07.thickbox1": function(){
                var $tLMS1205S07Form03b = $("#tLMS1205S07Form03b");
                var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                if ($tLMS1205S07Form03b.valid()) {
                    if ($tLMS1205S07Form03b.find("#queryDateS1").val() < 1 ||
                    $tLMS1205S07Form03b.find("#queryDateS1").val() > 12 ||
                    $tLMS1205S07Form03b.find("#queryDateE1").val() < 1 ||
                    $tLMS1205S07Form03b.find("#queryDateE1").val() > 12) {
                        CommonAPI.showMessage(i18n.clss07a["l120v01.error3"]);
                        return;
                    } else if ($tLMS1205S07Form03b.find("#queryDateS0").val() <= 0 ||
                    $tLMS1205S07Form03b.find("#queryDateE0").val() <= 0) {
                        CommonAPI.showMessage(i18n.clss07a["l120v01.error8"]);
                        return;
                    } else if ($tLMS1205S07Form03b.find("#queryDateE0").val() -
                               $tLMS1205S07Form03b.find("#queryDateS0").val() < 0) {
                        CommonAPI.showMessage(i18n.clss07a["l120v01.error9"]);
                        return;
					}else if(($tLMS1205S07Form03b.find("#queryDateE0").val()-
						      $tLMS1205S07Form03b.find("#queryDateS0").val()==0) &&
							 ($tLMS1205S07Form03b.find("#queryDateE1").val()-
						      $tLMS1205S07Form03b.find("#queryDateS1").val()<0)
							 ){
						CommonAPI.showMessage(i18n.clss07a["l120v01.error9"]);
						return;			
                    } else {
                        $.thickbox.close();
                        $.ajax({
                            handler: responseJSON["handler"],
                            type: "POST",
                            dataType: "json",
                            data: $.extend({}, getKeyCustIdDupNoItem(), {
                                formAction: "saveL120s04a2",
                                LMS1205S07Form03: JSON.stringify($LMS1205S07Form03.serializeData()),
                                mainId: responseJSON.mainid,
                                queryDateS0: $tLMS1205S07Form03b.find("#queryDateS0").val(),
                                queryDateS1: $tLMS1205S07Form03b.find("#queryDateS1").val(),
                                queryDateE0: $tLMS1205S07Form03b.find("#queryDateE0").val(),
                                queryDateE1: $tLMS1205S07Form03b.find("#queryDateE1").val()
                            }),
                            success: function(json){
                                var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                                $LMS1205S07Form03.setData(json.LMS1205S07Form03);
                                $LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
                            }
                        });
                    }
                }
            },
            // "刪除本頁": function() {alert("刪除本頁");},
            "L1205S07.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });

	});
}

/**
 * 計算貢獻度(集團)、放款額度(集團)、放款餘額(集團)、活期存款(集團)、
 * 貢獻度(關係)、放款額度(關係)、放款餘額(關係)、活期存款(關係)之後儲存
 */
function setTotal(){
    var count = $("#gridview_A-1-8-1").jqGrid('getGridParam', 'records');
    if (count == 0) {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.error14"]);
        return;
    }
    var $LMS1205S07Form03 = $("#LMS1205S07Form03");
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "saveTotal",
        data: $.extend({}, getKeyCustIdDupNoItem(), {
            mainId: responseJSON.mainid,
            LMS1205S07Form03: JSON.stringify($LMS1205S07Form03.serializeData()),
            queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
            queryDateE: $LMS1205S07Form03.find("#queryDateE").html()
        }),
        success: function(json){
            $LMS1205S07Form03.setData(json.LMS1205S07Form03);
            $LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
        }
    });
}

/**
 * 查詢並開啟往來實績彙總表ThickBox
 */
function tL120s04b(cellvalue, options, rowObject){
    var oid = rowObject.oid;
    // 進行查詢 
    $.ajax({ //查詢主要借款人資料
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "queryL120s04b",
        data: {
            oid: oid,
            mainId: responseJSON.mainId
        },
        success: function(json){
        	/*
            var $formL120s04b = $("#formL120s04b");
            $formL120s04b.setData(json.formL120s04b);
            $("#tL120s04b").thickbox({ // 使用選取的內容進行彈窗
                title: i18n.clss07a["L1205S07.grid43"],
                width: 965,
                height: 480,
                modal: true,
                i18n: i18n.def,
                buttons: {
                    "saveData": function(){
                        if ($formL120s04b.valid()) {
                            // 進行儲存
                            $.ajax({
                                handler: responseJSON["handler"],
                                type: "POST",
                                dataType: "json",
                                action: "saveL120s04b",
                                data: {
                                    oid: oid,
                                    formL120s04b: JSON.stringify($formL120s04b.serializeData())
                                },
                                success: function(json){
                                    $.thickbox.close();
                                    $.thickbox.close();
                                    CommonAPI.showMessage(json.NOTIFY_MESSAGE);
                                }
                            });
                        }
                    },
                    "close": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
            */
        	
        	var $formL120s04b = $("#formL120s04b");
			
			/*
			var grpGrrd = $formL120s04b.find("#grpGrrd option:selected").val();
			// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat 2012/11/27
			if(grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3" || grpGrrd == "4" || grpGrrd == "5"){
				// 顯示屬主要集團企業...
				$formL120s04b.find(".spectialHide").show();
			}else{
				// 隱藏屬主要集團企業...
				$formL120s04b.find(".spectialHide").hide();
			}*/
			
			// J-107-0087-001 Web
			// e-Loan企金授信配合調整集團企業評等修改，將依集團企業規模(大中小)及集團級別(A~G)級分為7個評等分級。
			var grpYear = json.formL120s04b.grpYear;
			var grpGrrd = json.formL120s04b.grpGrrd;
			if (grpYear) {
				// 判斷2017以後為新版，之前為舊版
				if (parseInt(grpYear, 10) >= 2017) {

					var obj = CommonAPI
							.loadCombos([ "GroupGrade2017" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade2017,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5" || grpGrrd == "6"
							|| grpGrrd == "7") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				} else {

					var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

					// 評等等級
					$("#grpGrrd").setItems({
						item : obj.GroupGrade,
						format : "{key}"
					});

					// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller
					// eddedat 2012/11/27
					if (grpGrrd == "1" || grpGrrd == "2"
							|| grpGrrd == "3" || grpGrrd == "4"
							|| grpGrrd == "5") {
						// 顯示屬主要集團企業...
						$formL120s04b.find(".spectialHide").show();
					} else {
						// 隱藏屬主要集團企業...
						$formL120s04b.find(".spectialHide").hide();
					}
				}

			} else {

				var obj = CommonAPI.loadCombos([ "GroupGrade" ]);

				// 評等等級
				$("#grpGrrd").setItems({
					item : obj.GroupGrade,
					format : "{key}"
				});

				// 如果沒有評等年度，以舊版執行
				// 建霖說：grpNo可以不用，只要集團評等(grpGrrd)符合條件就顯示 Miller eddedat
				// 2012/11/27
				if (grpGrrd == "1" || grpGrrd == "2" || grpGrrd == "3"
						|| grpGrrd == "4" || grpGrrd == "5") {
					// 顯示屬主要集團企業...
					$formL120s04b.find(".spectialHide").show();
				} else {
					// 隱藏屬主要集團企業...
					$formL120s04b.find(".spectialHide").hide();
				}
			}
			
			$formL120s04b.setData(json.formL120s04b);
			var grpNo = $formL120s04b.find("#grpNo").html();
			
				$("#tL120s04b").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.clss07a["L1205S07.grid43"],
					width : 965,
					height : 480,
					modal : true,
					i18n:i18n.def,
					buttons : {
						"saveData" : function() {
							var docDate6 = $("#docDate6").val();
							$("#_docDateYear").val(docDate6.substring(0, 4));
							$("#_docDateMonth").val(docDate6.substring(docDate6.indexOf("~")+1, docDate6.length-1));
							
							$("#tL120s04bDate").thickbox({
								title : "",
								width : 800,
								height : 200,
								modal : false,
								i18n:i18n.def,
								buttons: {
									"sure": function(){
										if(!$("#tL120s04bDateTmpForm").find("#_docDateYear,#_docDateMonth").valid()){
											return false;
										}
										var beginMonth = 1;
										var endMonth = parseInt($("#_docDateMonth").val(), 10);
										var year = parseInt($("#_docDateYear").val(), 10);
										$("#docDate3,#docDate6").val(year+"/"+beginMonth+"~"+endMonth+"月");										
										$.thickbox.close();
										
										var demandAmt = RemoveStringComma($formL120s04b.find("#demandAmt").val());
										var megaAvgAmt = RemoveStringComma($formL120s04b.find("#megaAvgAmt").val());
										if(demandAmt == undefined || demandAmt == null || demandAmt == ""){
											// 沒設值自動補零
											demandAmt = 0;
											$formL120s04b.find("#demandAmt").val(demandAmt);
										}
										if(megaAvgAmt == undefined || megaAvgAmt == null || megaAvgAmt == ""){
											// 沒設值自動補零
											megaAvgAmt = 0;
											$formL120s04b.find("#megaAvgAmt").val(megaAvgAmt);
										}							
										if(megaAvgAmt == "0"){
											$formL120s04b.find("#demandAvgRate").val("N.A.");
										}else{
											$formL120s04b.find("#demandAvgRate").val
												(Math.round((demandAmt/megaAvgAmt)*10000)/100);
										}
										if($formL120s04b.valid()){
											$formL120s04b.find(".numeric").each(function(i){
												$(this).val(RemoveStringComma($(this).val()));
											});
											// 進行報酬率計算
											for(var i=1; i<=6; i++){
												// D利潤貢獻(TWD)
												
												//var profitAmt = ($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												//$formL120s04b.find("#profitAmt" + i).val() == null || 
												//$formL120s04b.find("#profitAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												var profitAmt = 0;									
												if($formL120s04b.find("#profitAmt" + i).val() == undefined || 
												$formL120s04b.find("#profitAmt" + i).val() == null || 
												$formL120s04b.find("#profitAmt" + i).val() == ""){
													profitAmt = 0;
													$formL120s04b.find("#profitAmt" + i).val(profitAmt);
												}else {
													profitAmt = parseInt($formL120s04b.find("#profitAmt" + i).val(),10);
												}								
																								
												// A平均授信(TWD)
												//var avgLoanAmt = ($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												//$formL120s04b.find("#avgLoanAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												
												var avgLoanAmt = 0;
												if($formL120s04b.find("#avgLoanAmt" + i).val() == undefined || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == null || 
												$formL120s04b.find("#avgLoanAmt" + i).val() == ""){
													avgLoanAmt = 0;
													$formL120s04b.find("#avgLoanAmt" + i).val(avgLoanAmt);
												} else {
													avgLoanAmt = parseInt($formL120s04b.find("#avgLoanAmt" + i).val(),10);
												}
												
												
												// B應收帳款無追索買方承購平均餘額(TWD)
												//var rcvBuyAvgAmt = ($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												
												var rcvBuyAvgAmt = 0;
												if($formL120s04b.find("#rcvBuyAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvBuyAvgAmt" + i).val() == ""){
													rcvBuyAvgAmt = 0;
													$formL120s04b.find("#rcvBuyAvgAmt" + i).val(rcvBuyAvgAmt);
												}else {
													rcvBuyAvgAmt = parseInt($formL120s04b.find("#rcvBuyAvgAmt" + i).val(),10);
												}						
												
												// C應收帳款無追索權賣方融資平均餘額(TWD)
												//var rcvSellAvgAmt = ($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												//$formL120s04b.find("#rcvSellAvgAmt" + i).val() == "")? 0 : parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												
												var rcvSellAvgAmt = 0;
												if($formL120s04b.find("#rcvSellAvgAmt" + i).val() == undefined || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == null || 
												$formL120s04b.find("#rcvSellAvgAmt" + i).val() == ""){
													rcvSellAvgAmt = 0;
													$formL120s04b.find("#rcvSellAvgAmt" + i).val(rcvSellAvgAmt);
												} else {
													rcvSellAvgAmt = parseInt($formL120s04b.find("#rcvSellAvgAmt" + i).val(),10);
												}
												
												// 報酬率% = D/(A-B+C)
												if((avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) != 0){
													// 四捨五入取到小數點兩位																										
													if(i==3 || i == 6){
														//資料需要年化
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt)/(endMonth - beginMonth + 1)* 12 * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}else{
														var num = new Number(profitAmt / (avgLoanAmt - rcvBuyAvgAmt + rcvSellAvgAmt) * 100);
														$formL120s04b.find("#profitRate" + i).html(parseFloat(num.toFixed(2)));
													}
																																	
												}
											}
											// 進行儲存
											$.ajax({
												handler : responseJSON["handler"],
												type : "POST",
												dataType : "json",
												action : "saveL120s04b",
												data : {
													oid : oid,
													formL120s04b : JSON.stringify($formL120s04b.serializeData())
												},
												success : function(json) {
													//$.thickbox.close();
													$.thickbox.close();
													CommonAPI.showMessage(json.NOTIFY_MESSAGE);
												}
											});
										}
									}
								}
							});
							
							
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});
        }
    });
}

/**
 * 產生往來實績彙總表
 */
function createReport(){
    var count = $("#gridview_A-1-8-1").jqGrid('getGridParam', 'records');
    if (count > 0) {
        // L1205S07.confirm4=執行引進後會刪除已存在之借戶暨關係戶與本行往來實績彙總表，是否確定執行？
        CommonAPI.confirmMessage(i18n.clss07a["L1205S07.confirm4"], function(b){
            if (b) {
                //是的function
                $.ajax({
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    data: $.extend({}, getKeyCustIdDupNoItem(), {
                        formAction: "deleteL120s04b",
                        mainId: responseJSON.mainid
                    }),
                    success: function(json){
                        $.thickbox.close();
                        // 開始產生實績彙總表
                        importReport();
                    }
                });
            }
        });
    }
}

/**
 * 刪除往來實績彙總表
 */
function deleteL120s04a(){
    var id = $("#gridview_A-1-8-2").getGridParam('selrow');
    var data = $("#gridview_A-1-8-2").getRowData(id);
    if (data.oid == "" || data.oid == undefined || data.oid == null) {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
        return;
    }
    CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
        if (b) {
            //是的function
            $.ajax({
                handler: responseJSON["handler"],
                type: "POST",
                dataType: "json",
                data: $.extend({}, getKeyCustIdDupNoItem(), {
                    formAction: "deleteL120s04b",
                    mainId: responseJSON.mainid
                }),
                success: function(json){
                    // 更新實績彙總表Grid
                    $("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
                }
            });
        }
    });
}

/**
 * 執行產生往來實績彙總表
 */
function importReport(){
    var $LMS1205S07Form03 = $("#LMS1205S07Form03");
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        data: $.extend({}, getKeyCustIdDupNoItem(), {
            formAction: "importL120s04b",
            mainId: responseJSON.mainid,
            queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
            queryDateE: $LMS1205S07Form03.find("#queryDateE").html()
        
        }),
        success: function(json){
            // 更新實績彙總表Grid
            $("#LMS1205S07Form03").find("#gridview_A-1-8-2").trigger("reloadGrid");
        }
    });
}

/**
 * 取消列印關係戶名單
 */
function cancelPrint(){
    var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
    var list = "";
    var sign = ",";
    for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
        if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
            var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
            list += ((list == "") ? "" : sign) + data.oid;
        }
    }
    if (list == "") {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
        return;
    }
    
    API.confirmMessage(i18n.clss07a["L1205S07.confirm1"], function(b){
        if (b) {
            //是的function
            $.ajax({
                handler: responseJSON["handler"],
                type: "POST",
                dataType: "json",
                action: "cancelPrint",
                data: {
                    listOid: list,
                    mainId: responseJSON.mainid
                },
                success: function(json){
                    $("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
                }
            });
        } else {
            //否的function
            //CommonAPI.showMessage(i18n.clss07a["L1205S07.alert3"]);
        }
    })
}

/**
 * 恢復已取消列印關係戶名單
 */
function undoPrint(){
    var rows = $("#gridview_A-1-8-1").getGridParam('selarrrow');
    var list = "";
    var sign = ",";
    for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
        if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
            var data = $("#gridview_A-1-8-1").getRowData(rows[i]);
            list += ((list == "") ? "" : sign) + data.oid;
        }
    }
    if (list == "") {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
        return;
    }
    API.confirmMessage(i18n.clss07a["L1205S07.confirm2"], function(b){
        if (b) {
            //是的function
            $.ajax({
                handler: responseJSON["handler"],
                type: "POST",
                dataType: "json",
                action: "undoPrint",
                data: {
                    listOid: list,
                    mainId: responseJSON.mainid
                },
                success: function(json){
                    $("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
                }
            });
        } else {
            //否的function
            //CommonAPI.showMessage(i18n.clss07a["L1205S07.alert4"]);
        }
    })
}

/**
 * 刪除往來彙總
 */
function deleteAllCust(){
    API.confirmMessage(i18n.clss07a["L1205S07.confirm3"], function(b){
        if (b) {
            //是的function
            $.ajax({
                handler: responseJSON["handler"],
                type: "POST",
                dataType: "json",
                action: "deleteL120s04a",
                data:  $.extend({}, getKeyCustIdDupNoItem(), {
                    mainId: responseJSON.mainid
                }),
                success: function(json){
                    var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                    $LMS1205S07Form03.setData(json.LMS1205S07Form03);
                    $LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
                }
            });
        } else {
            //否的function
            //CommonAPI.showMessage(i18n.clss07a["L1205S07.alert5"]);
        }
    })
}

/**
 * 產生利害關係人Grid性質格式化後顯示
 */
function proPertyFormatter(cellvalue, otions, rowObject){
    //登錄授信科目的格式化
    var itemName = '';
    if (cellvalue == null ||
    cellvalue == undefined ||
    cellvalue == "") {
        //授信科目為空!!
    } else {
        var list = cellvalue.split("|");
        if (cellvalue) {
            itemName = i18n.clss07a["L140M01a.type" + list[0]];
            if (cellvalue.length > 1) {
                for (var i = 1; i < list.length; i++) {
                    var itemone = i18n.clss07a["L140M01a.type" + list[i]];
                    itemName = itemName + "、" + itemone;
                }
            }
        }
    }
    return itemName;
}

/**
 * 往來彙總明細ThickBox
 */
function l120s04aThick(oid){
    $("#borrower-data999").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox9"],
        width: 960,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "reQuery": function(){
                var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                $.ajax({
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "rQueryL120s04a",
                    data: $.extend({}, getKeyCustIdDupNoItem(), {
                        oid: oid,
                        mainId: responseJSON.mainid,
                        custId: $("#tLMS1205S07Form03").find("#custId").html(),
                        dupNo: $("#tLMS1205S07Form03").find("#dupNo").html(),
                        custName: $("#tLMS1205S07Form03").find("#custName").html(),
                        queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
                        queryDateE: $LMS1205S07Form03.find("#queryDateE").html()
                    }),
                    success: function(obj){
                        var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
                        $tLMS1205S07Form03.setData(obj.tLMS1205S07Form03);
                        for (o in obj.tLMS1205S07Form03.custRelation) {
                            $tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
                                if ($(this).val() == obj.tLMS1205S07Form03.custRelation[o]) {
                                    $(this).attr("checked", true);
                                }
                            });
                        }
                        oid = obj.tLMS1205S07Form03.oid;
                        $("LMS1205S07Form03").setData(obj.LMS1205S07Form03, false);
                        $("#gridview_A-1-8-1").trigger("reloadGrid");
                    }
                });
            },
            "saveData": function(){
                var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
                var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                var count = $("#gridview_A-1-8-1").jqGrid('getGridParam', 'records');
                if (count == 0) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.error14"]);
                    return;
                }
                var list = "";
                var sign = ",";
                if ($tLMS1205S07Form03.valid()) {
                    var checkSub = false;
                    var kind = 0;
                    var checkCou = 0;
                    $tLMS1205S07Form03.find("[name=custRelation]:checkbox:checked").each(function(i){
                        if ($(this).val() == 3) {
                            checkSub = true;
                            kind = 1;
                        } else if ($(this).val() == 4) {
                            checkSub = true;
                            kind = 2;
                        }
                        list += ((list == "") ? "" : sign) + $(this).val();
                        checkCou++;
                    });
                    if (checkCou == 0) {
                        CommonAPI.showMessage(i18n.clss07a["L1205S07.error15"]);
                        return;
                    }
                    if (checkSub && list.length > 1) {
                        if (kind == 1) {
                            // 集團企業合計
                            CommonAPI.showMessage(i18n.clss07a["L1205S07.error13"]);
                        } else if (kind == 2) {
                            // 關係企業合計
                            CommonAPI.showMessage(i18n.clss07a["L1205S07.error12"]);
                        }
                        return;
                    }
                    $.ajax({ //查詢主要借款人資料
                        handler: responseJSON["handler"],
                        type: "POST",
                        dataType: "json",
                        action: "saveL120s04a",
                        data: $.extend({}, getKeyCustIdDupNoItem(), {
                            tLMS1205S07Form03: JSON.stringify($tLMS1205S07Form03.serializeData()),
                            mainId: responseJSON.mainid,
                            custId: $("#tLMS1205S07Form03").find("#custId").html(),
                            dupNo: $("#tLMS1205S07Form03").find("#dupNo").html(),
                            queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
                            queryDateE: $LMS1205S07Form03.find("#queryDateE").html(),
                            oid: oid,
                            list: list
                        }),
                        success: function(json){
                            $LMS1205S07Form03.find("#gridview_A-1-8-1").trigger("reloadGrid");
                            oid = json.newOid;
                        }
                    });
                    //$.thickbox.close();					
                }
            },
            // "刪除本頁": function() {alert("刪除本頁");},
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 新增往來彙總
 */
function addData(){
    $("#formAdd").reset();
    var count = $("#gridview_A-1-8-1").jqGrid('getGridParam', 'records');
    if (count == 0) {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.error14"]);
        return;
    }
    $("#addData").find("#showSel03").hide();
    $("#addData").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox11"],
        width: 800,
        height: 200,
        modal: true,
        align: 'center',
        valign: 'bottom',
        i18n: i18n.clss07a,
        buttons: {
            "L1205S07.thickbox1": function(){
                var gridCust = $("#gridview_A-1-8-1").getCol("custId");
                var gridName = $("#gridview_A-1-8-1").getCol("custName");
                if (!$("#selCus03").children().is("option")) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert7"]);
                    return;
                } else if ($("#selCus03 option:eq(0)").attr("selected")) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert6"]);
                    return;
                } else {
                    var custIdAll = $("#selCus03 option:selected").val();
                    var custIdOnly = custIdAll.substring(0, custIdAll.length - 1) + " " + custIdAll.substring(custIdAll.length - 1);
                    var custName = $("#selCus03 option:selected").html();
                    var custNameOnly = custName.substring(custIdAll.length + 3);
                    var $LMS1205S07Form03 = $("#LMS1205S07Form03");
                    $.ajax({
                        handler: responseJSON["handler"],
                        type: "POST",
                        dataType: "json",
                        data: $.extend({}, getKeyCustIdDupNoItem(), {
                            formAction: "addL120s04a",
                            custIdAll: custIdAll,
                            custName: custName,
                            mainId: responseJSON.mainId,
                            queryDateS: $LMS1205S07Form03.find("#queryDateS").html(),
                            queryDateE: $LMS1205S07Form03.find("#queryDateE").html()
                        }),
                        success: function(json){
                            var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
                            $tLMS1205S07Form03.setData(json);
                            $tLMS1205S07Form03.find("#createBY2").html(i18n.clss07a["L1205S07.createBY2"]);
                            $("#LMS1205S07Form03").find("#gridview_A-1-8-1").trigger("reloadGrid");
                            l120s04aThick("");
                        }
                    });
                    $.thickbox.close();
                }
            },
            // "刪除本頁": function() {alert("刪除本頁");},
            "L1205S07.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 讀取往來彙總以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc3(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    $("#tLMS1205S07Form03").reset();
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "queryL120s04a",
        data: {
            oid: rowObject.oid
        },
        success: function(obj){
            var $tLMS1205S07Form03 = $("#tLMS1205S07Form03");
            $tLMS1205S07Form03.setData(obj);
            for (o in obj.tLMS1205S07Form03.custRelation) {
                $tLMS1205S07Form03.find("[name=custRelation]").each(function(i){
                    var $this = $(this);
                    if ($this.val() == obj.tLMS1205S07Form03.custRelation[o]) {
                        $this.attr("checked", true);
                    }
                });
            }
            $tLMS1205S07Form03.setData(obj.LMS1205S07Form03, false);
            l120s04aThick(obj.tLMS1205S07Form03.oid);
        }
    });
}

/**
 * 引進額度明細表以讀取資本適足率
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function getBisFromCn(){
	prompt_before_import_data( {'target_action': 'imp_L120S03A'} ).done(function(){
	
    API.iConfirmDialog({
        message: i18n.clss07a["L120S03A.dialog"],
        buttons: API.createJSON([{
            key: i18n.def.yes,
            value: function(){
                getBis(true);
                $.thickbox.close();
            }
        }, {
            key: i18n.def.no,
            value: function(){
                getBis(false);
                $.thickbox.close();
            }
        }])
    });
	});
	
    function getBis(refresh){
        $.ajax({ //查詢主要借款人資料
            handler: responseJSON["handler"],
            type: "POST",
            dataType: "json",
            action: "getBis",
            data: {
                mainId: responseJSON.mainid,
                refresh: refresh
            },
            success: function(obj){
                $("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid"); //更新Grid內容
            }
        });
    }
    
}

function prompt_before_import_data(param){
	var my_dfd = $.Deferred();    	
	$.ajax({ //查詢主要借款人資料
        handler: "cls1141m01formhandler",
        type: "POST",
        dataType: "json",
        action: "prompt_before_import_data",
        data: $.extend( {mainId: responseJSON.mainid}, param||{}),
        success: function(obj){
            if(obj.cfm_msg ){
            	CommonAPI.confirmMessage(obj.cfm_msg , function(b){
                    if (b) {
                    	my_dfd.resolve();
                    }
        		});
            }else{
        		my_dfd.resolve();
        	}            
        }
    });
	return my_dfd.promise();
}
/**
 * 寫回額度明細表
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function saveToL140m01a(){
    $.ajax({ //查詢主要借款人資料
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "saveToL140m01a",
        data: {
            mainId: responseJSON.mainid
        },
        success: function(obj){
        }
    });
}

/**
 * 讀取資本適足率以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    $("#tLMS1205S07Form02").reset();
    $.ajax({ //查詢主要借款人資料
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "queryBis",
        data: {
            oid: rowObject.oid
        },
        success: function(obj){
            $("#tLMS1205S07Form02").setData(obj);
            addBis(DOMPurify.sanitize(obj.tLMS1205S07Form02.oid));
			$("#crdFlag").triggerHandler("change");
			$("#tLMS1205S07Form02").setData(obj);
        }
    });
}

/**
 * 修改資本適足率
 */
function addBis(oid){
	
    $("#thickboxAdd").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox12"],
        width: 900,
        height: 300,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "reQuery": function(){
                var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                $.ajax({ //查詢主要借款人資料
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "queryBis2",
                    data: {
                        oid: oid,
                        crdFlag: $tLMS1205S07Form02.find("#crdFlag").html()
                    },
                    success: function(obj){
                        $tLMS1205S07Form02.setData(obj.tLMS1205S07Form02, false);
                    }
                });
            },
            "calculate": function(){
                var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                $.ajax({ //查詢主要借款人資料
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "calculateBis",
                    formId: 'tLMS1205S07Form02',
                    data: {
                        oid: oid,
                        tLMS1205S07Form02: JSON.stringify($tLMS1205S07Form02.serializeData())
                    },
                    success: function(json){
                        var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                        $tLMS1205S07Form02.setData(json.tLMS1205S07Form02);
                    }
                });
            },
            "saveData": function(){
                var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                $.ajax({ //查詢主要借款人資料
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "calculateBis",
                    data: {
                        oid: oid,
                        tLMS1205S07Form02: JSON.stringify($tLMS1205S07Form02.serializeData())
                    },
                    success: function(json){
                        var $tLMS1205S07Form02 = $("#tLMS1205S07Form02");
                        $tLMS1205S07Form02.setData(json.tLMS1205S07Form02);
                        $.ajax({ //查詢主要借款人資料
                            handler: responseJSON["handler"],
                            type: "POST",
                            dataType: "json",
                            action: "saveBis",
                            data: {
                                oid: oid,
                                mainId: responseJSON.mainId,
                                tLMS1205S07Form02: JSON.stringify($tLMS1205S07Form02.serializeData())
                            },
                            success: function(json){
                                $("#LMS1205S07Form02").find("#gridview_A-1-9-1").trigger("reloadGrid");
                            }
                        });
                    }
                });
                //$.thickbox.close();
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 讀取利害關係人以進行修改
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc2(cellvalue, options, rowObject){
    ilog.debug(rowObject);
    ilog.debug("CLSS07APage.js > openDoc2 , then call ajax queryL120s06b");
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "queryL120s06b",
        data: {
            oid: rowObject.oid,
            requery: false
        },
        success: function(obj){
            var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
            l120s06bThick(obj.tLMS1205S07Form04.oid);
            $tLMS1205S07Form04.setData(obj.tLMS1205S07Form04);
            $tLMS1205S07Form04.find("#oldOid").val(obj.tLMS1205S07Form04.oid);
            if (obj.tLMS1205S07Form04.printMode == '2') {
                $tLMS1205S07Form04.find("[name='printMode']:eq(1)").attr("checked", true);
            } else {
                $tLMS1205S07Form04.find("[name='printMode']:eq(0)").attr("checked", true);
            }
        }
    });
}

/**
 * 利害關係人授信條件對照表-thickBox
 */
function l120s06bThick(oid){
    $("#borrower-data921").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox14"],//'利害關係人授信條件對照表'
        width: 960,
        height: 500,
        modal: true,
        i18n: i18n.def,
        buttons: {
            "saveData": function(){
                var $tLMS1205S07Form04 = $("#tLMS1205S07Form04");
                if ($tLMS1205S07Form04.valid()) {
                    $.ajax({ //查詢主要借款人資料
                        handler: responseJSON["handler"],
                        type: "POST",
                        dataType: "json",
                        action: "saveL120s06b",
                        data: {
                            oid: oid,
                            tLMS1205S07Form04: JSON.stringify($tLMS1205S07Form04.serializeData()),
							printMode : $tLMS1205S07Form04.find("[name='printMode']:radio:checked").val()
                        },
                        success: function(json){
                            $("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
                        }
                    });
                    $.thickbox.close();
                }
            },
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 查詢利害關係人(對照)
 */
function openbox(){
    $("#formSearch").find("#showSel04").hide();
    $("#formSearch").find("#showBrid").hide();
    $("#formSearch").reset();
    $("#showGrid").hide();
    $("#gridviewAA").setGridParam({
        'selrow': null
    }).trigger("reloadGrid");
    $("#LMS1205S07Form04").find("#gridviewAA").trigger("reloadGrid");
    $("#openbox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox14"],//'利害關係人授信條件對照表',
        width: 700,
        height: 500,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.clss07a,
        buttons: {
            "L1205S07.thickbox1": function(){
                var id = $("#gridviewAA").getGridParam('selrow');
                var data = $("#gridviewAA").getRowData(id);
                if (!$("#selCus04").children().is("option")) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert7"]);
                    return;
                } else if ($("#selCus04 option:eq(0)").attr("selected")) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert6"]);
                    return;
                } else if (data.mainId == "" || data.mainId == undefined || data.mainId == null) {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
                    return;
                }
                $.ajax({
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "queryL120s06c",
                    data: {
                        mainId: data.mainId,
                        oldOid: $("#tLMS1205S07Form04").find("#oldOid").val()
                    },
                    success: function(json){
                        $("#showGrid").hide();
                        var tLMS1205S07Form04 = $("#tLMS1205S07Form04"); 
                        $("#tLMS1205S07Form04").setData(json.tLMS1205S07Form04, false);
                        //setData(..., false) 不reset form,而回傳的資料中 itemDscr3_ , itemDscr4_ 即使空的,也不會傳「無」,手動set
                        tLMS1205S07Form04.find("itemDscr3_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscr3_));
                        tLMS1205S07Form04.find("itemDscr4_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscr4_));
                        // $("#tLMS1205S07Form04").find("itemDscr5_").html(json.tLMS1205S07Form04.itemDscr5_);// J-112-0124 取消"其他敘做條件"欄位
                        tLMS1205S07Form04.find("itemDscrB_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscrB_));
                        tLMS1205S07Form04.find("itemDscrC_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscrC_));
                        tLMS1205S07Form04.find("#cmpDate2").html(DOMPurify.sanitize(json.tLMS1205S07Form04.cmpDate2));
                        $("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
                    }
                });
                //gridviewAA
                $.thickbox.close();
            },
            "L1205S07.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $("#showGrid").hide();
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 查詢利害關係人_依利率(對照)
 */
function openboxOrderByRate(){
	var caseMainId = responseJSON.mainId;
	var refMainId = $("#tLMS1205S07Form04").find("#refMainId").val();
	 $.ajax({
         handler: responseJSON["handler"],
         type: "POST",
         action: "getSearchCondFor_LMS_L120S06A_cls",
         data: {
        	 "caseMainId": caseMainId,
        	 "refMainId":refMainId
         },
         success: function(jsonSearchCond){        	 
        	 //========        	 
        	 $frm = $("#formSearch_l120s06b_type2_OrderByRate");
        	 $frm.reset();
        	    
        	 $frm.injectData(
    	    	 { "formSearch_l120s06b_type2_OrderByRate_prodKind":jsonSearchCond.prodKind
    	    	 , "formSearch_l120s06b_type2_OrderByRate_lnPurs":jsonSearchCond.lnPurs
    	    	 }
    	     );
        	 $frm.find("#formSearch_l120s06b_type2_OrderByRate_prodKind").trigger('change');
        	 $frm.injectData(
        	     { "formSearch_l120s06b_type2_OrderByRate_subj":jsonSearchCond.subjCode
        	     }
        	);
        	    
        	 $("#showGrid_l120s06b_type2_OrderByRate").hide();
        	 $("#gridviewAA_l120s06b_type2_OrderByRate").setGridParam({
        	        'selrow': null
        	 }).trigger("reloadGrid");
        	    
        	 
        	 $("#openbox_l120s06b_type2_OrderByRate").thickbox({ // 使用選取的內容進行彈窗
        	        title: i18n.clss07a["L1205S07.btn19"],//L1205S07.btn19=查找最近一年簽准的(非利害關係人)授信案件
        	        width: 980,
        	        height: 500,
        	        modal: true,
        	        align: "center",
        	        valign: "bottom",
        	        i18n: i18n.def,
        	        buttons: {
        	            "sure": function(){
        	                var id = $("#gridviewAA_l120s06b_type2_OrderByRate").getGridParam('selrow');
        	                var data = $("#gridviewAA_l120s06b_type2_OrderByRate").getRowData(id);
        	                if(data && data.mainId){	
        	                }else{
        	                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]); //L1205S07.alert1=尚未選取資料!
        	                    return;
        	                }
        	                
        	                $.ajax({
        	                    handler: responseJSON["handler"],
        	                    type: "POST",
        	                    dataType: "json",
        	                    action: "queryL120s06c",
        	                    data: {
        	                        mainId: data.mainId,
        	                        oldOid: $("#tLMS1205S07Form04").find("#oldOid").val()
        	                    },
        	                    success: function(json){
        	                        $("#showGridrderByRate").hide();
        	                        var tLMS1205S07Form04 = $("#tLMS1205S07Form04");
        	                        $("#tLMS1205S07Form04").setData(json.tLMS1205S07Form04, false);
        	                        //setData(..., false) 不reset form,而回傳的資料中 itemDscr3_ , itemDscr4_ 即使空的,也不會傳「無」,手動set
        	                        tLMS1205S07Form04.find("itemDscr3_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscr3_));
        	                        tLMS1205S07Form04.find("itemDscr4_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscr4_));
        	                        // $("#tLMS1205S07Form04").find("itemDscr5_").html(json.tLMS1205S07Form04.itemDscr5_);// J-112-0124 取消"其他敘做條件"欄位
        	                        tLMS1205S07Form04.find("itemDscrB_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscrB_));
        	                        tLMS1205S07Form04.find("itemDscrC_").html(DOMPurify.sanitize(json.tLMS1205S07Form04.itemDscrC_));
        	                        tLMS1205S07Form04.find("#cmpDate2").html(DOMPurify.sanitize(json.tLMS1205S07Form04.cmpDate2));
        	                        $("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
        	                    }
        	                });
        	                //gridviewAA
        	                $.thickbox.close();
        	            },
        	            "cancel": function(){
        	                API.confirmMessage(i18n.def['flow.exit'], function(res){
        	                    if (res) {
        	                        $("#showGridrderByRate").hide();
        	                        $.thickbox.close();
        	                    }
        	                });
        	            }
        	        }
        	 });
         }
     });
}

/**
 * 刪除利害關係人
 */
function deleteL120s06a(){
    var rows = $("#gridviewShow").getGridParam('selarrrow');
    var list = "";
    var sign = ",";
    for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
        if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
            var data = $("#gridviewShow").getRowData(rows[i]);
            list += ((list == "") ? "" : sign) + data.oid;
        }
    }
    if (list == "") {
        CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
        return;
    }
    $.ajax({
        handler: responseJSON["handler"],
        type: "POST",
        dataType: "json",
        action: "deleteL120s06b",
        data: {
            listOid: list,
            sign: sign
        },
        success: function(json){
            $("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
        }
    });
    $.thickbox.close();
}

/**
 * 新增利害關係人
 */
function openbox222(){
    $("#openbox222").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.clss07a["L1205S07.thickbox15"],//'額度明細表選擇'
        width: 800,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.clss07a,
        buttons: {
            "L1205S07.thickbox1": function(){
                var rows = $("#gridviewCC").getGridParam('selarrrow');
                var list = "";
                var sign = ",";
                for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
                    if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                        var data = $("#gridviewCC").getRowData(rows[i]);
                        list += ((list == "") ? "" : sign) + data.oid;
                    }
                }
                if (list == "") {
                    CommonAPI.showMessage(i18n.clss07a["L1205S07.alert1"]);
                    return;
                }
                $.ajax({
                    handler: responseJSON["handler"],
                    type: "POST",
                    dataType: "json",
                    action: "addL120s06a",
                    data: {
                        listOid: list,
                        mainId: responseJSON.mainid,
                        sign: sign
                    },
                    success: function(json){
                        $("#LMS1205S07Form04").find("#gridviewShow").trigger("reloadGrid");
                    }
                });
                $.thickbox.close();
            },
            "L1205S07.thickbox2": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 * 資本適足率列印(產報表)
 */
function printBIS(){
    if ($("#gridview_A-1-9-1").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    } else {
        var count = 0;
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: "R04" + "^" + "",
                fileDownloadName: "l120r01.pdf",
                serviceName: "cls1141r01rptservice"
            }
        });
    }
}

/**
 * 綜合評估及敘做理由列印(產報表)
 */
function printDscr04(){
    //saveBeforePrint=執行列印將自動儲存資料，是否繼續此動作? 
    CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
        if (b) {
            // 儲存
            $("#buttonPanel").find("#btnSave").click();
            setTimeout(function(){
                var gffbody = getCkeditor("ffbody");
                if (gffbody == "" ||
                gffbody == null ||
                gffbody == undefined) {
                    // 報表無資料
                    CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
                } else {
                    var count = 0;
                    $.form.submit({
                        url: "../../simple/FileProcessingService",
                        target: "_blank",
                        data: {
                            mainId: responseJSON.mainId,
                            rptOid: "R16" + "^" + "",
                            fileDownloadName: "l120r01.pdf",
                            serviceName: "lms1201r01rptservice"
                        }
                    });
                }
            }, 2000);
        }
    });
}

/**
 * 往來彙總(產報表)
 */
function printR14(){
    if ($("#gridview_A-1-8-1").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    } else {
    	//比照 CLSM01APage.js :: showPrint
    	//EX:R14^07DB1B806DF211E39BD0CB5AC0A83B85^^^^^28590267^0    	
    	var json = getKeyCustIdDupNoItem();    	
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: "R14" + "^^^^^^" + json.keyCustId + "^" + json.keyDupNo,
                fileDownloadName: "l120r01.pdf",
                serviceName: "cls1141r01rptservice"
            }
        });
    }
}
