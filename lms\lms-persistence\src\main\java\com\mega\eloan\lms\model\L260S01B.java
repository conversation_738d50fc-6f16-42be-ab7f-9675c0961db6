/* 
 * L260S01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;
import com.mega.eloan.lms.validation.group.Check;

/** 貸後管理餘屋貸款檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260S01B", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L260S01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 建案名稱 **/
	@Size(max=90)
	@Column(name="BUILDNAME", length=90, columnDefinition="VARCHAR(90)")
	private String buildName;

	/** 初貸餘屋戶數 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="BEGFORSELL", columnDefinition="DECIMAL(5,0)")
	private Integer begForSell;

	/** 已售戶數 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SOLDNUMBER", columnDefinition="DECIMAL(5,0)")
	private Integer soldNumber;

	/** 
	 * 進度狀態<p/>
	 * 相符(E)/超前(A)/落後(B)
	 */
	@Size(max=1)
	@Column(name="PROSTATUS", length=1, columnDefinition="VARCHAR(1)")
	private String proStatus;

	/** 落後原因 **/
	@Size(max=900)
	@Column(name="BEHINDDESC", length=900, columnDefinition="VARCHAR(900)")
	private String behindDesc;

	/** 
	 * 是否與兆金子公司辦理同一建案貸款<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="ISSAMECASE", length=1, columnDefinition="VARCHAR(1)")
	private String isSameCase;

	/**
	 * 可修改初貸餘屋戶數<p/>
	 * begForSell >0 則 N<br/>
	 *  begForSell <=0 則 Y
	 */
	@Size(max=1)
	@Column(name="CHECKBEG", length=1, columnDefinition="VARCHAR(1)")
	private String checkBeg;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 取得建案名稱 **/
	public String getBuildName() {
		return this.buildName;
	}
	/** 設定建案名稱 **/
	public void setBuildName(String value) {
		this.buildName = value;
	}

	/** 取得初貸餘屋戶數 **/
	public Integer getBegForSell() {
		return this.begForSell;
	}
	/** 設定初貸餘屋戶數 **/
	public void setBegForSell(Integer value) {
		this.begForSell = value;
	}

	/** 取得已售戶數 **/
	public Integer getSoldNumber() {
		return this.soldNumber;
	}
	/** 設定已售戶數 **/
	public void setSoldNumber(Integer value) {
		this.soldNumber = value;
	}

	/** 
	 * 取得進度狀態<p/>
	 * 相符(E)/超前(A)/落後(B)
	 */
	public String getProStatus() {
		return this.proStatus;
	}
	/**
	 *  設定進度狀態<p/>
	 *  相符(E)/超前(A)/落後(B)
	 **/
	public void setProStatus(String value) {
		this.proStatus = value;
	}

	/** 取得落後原因 **/
	public String getBehindDesc() {
		return this.behindDesc;
	}
	/** 設定落後原因 **/
	public void setBehindDesc(String value) {
		this.behindDesc = value;
	}

	/** 
	 * 取得是否與兆金子公司辦理同一建案貸款<p/>
	 * Y/N
	 */
	public String getIsSameCase() {
		return this.isSameCase;
	}
	/**
	 *  設定是否與兆金子公司辦理同一建案貸款<p/>
	 *  Y/N
	 **/
	public void setIsSameCase(String value) {
		this.isSameCase = value;
	}

	/**
	 * 取得可修改初貸餘屋戶數<p/>
	 * begForSell >0 則 N<br/>
	 *  begForSell <=0 則 Y
	 */
	public String getCheckBeg() {
		return this.checkBeg;
	}
	/**
	 *  設定可修改初貸餘屋戶數<p/>
	 *  begForSell >0 則 N<br/>
	 *  begForSell <=0 則 Y
	 **/
	public void setCheckBeg(String value) {
		this.checkBeg = value;
	}
}
