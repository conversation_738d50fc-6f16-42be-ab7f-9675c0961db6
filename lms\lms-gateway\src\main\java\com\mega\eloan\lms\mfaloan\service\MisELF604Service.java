package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF600;
import com.mega.eloan.lms.mfaloan.bean.ELF604;

/**
 * <pre>
 * 空地貸款控制檔
 * </pre>
 * 
 * @since 2019/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/4,009301,new
 *          </ul>
 */
public interface MisELF604Service {

	public List<Map<String, Object>> getByBrNoAndLoanDate(String brNo,String agntNo,String loanStartDate,String loanEndDate);
	public Map<String, Object> getByLoanNo(String loanNo) ;
	public ELF604 findByLoanNo(String cntrNo);
	public List<Map<String, Object>> getAll();
	public List<Map<String, Object>> getBycntrNo(String brNo,String cntrNo);
}
