<?xml version="1.0" encoding="UTF-8"?>
 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:wicket="http://wicket.apache.org/">
    <body>
        <wicket:panel>
            <form action="" id="CLS1151Form09" name="CLS1151Form09">
                <table width="100%" class="tb2" border="0" cellpadding="0" cellspacing="0">
                    <tr class="hd1" style="text-align:left">
                        <td>
                            <span style="display:none" class="caseSpan"><label>
                                    <input id="tab07" type="checkbox" class="caseBox" />
                                    <wicket:message key="button.modify"><!-- 修改--></wicket:message>
                                </label>
                            </span>
                            <select id="pageNum9" name="pageNum9" class="nodisabled" onchange="changedistanceWord('9','44','53');">
                                <option value="0" selected="selected"><wicket:message key="L140M01b.printMain"><!-- 印於主表--></wicket:message></option>
                                <option value="1"><wicket:message key="L140M01b.print01"><!-- 印於附表--></wicket:message></option>
                            </select>
                        </td>
                    </tr>
                </table>
				<!-- J-112-0451 新增敘做條件異動比較表 -->
				<span class="text-red">
                    <font color="red" style="font-size:18px"><b>
					<wicket:message key="L140S11A.msg01"><!-- 1.對於常董會提報案件或擬採表格方式陳述者，請選用分項表格編輯功能，方可於簽報書之相關文件頁籤產製敘做案件異動比較表(word)。--></wicket:message>
                    <br/>
                    <wicket:message key="L140S11A.msg02"><!-- 2.分項表格編輯與自由格式編輯方式建議請擇一辦理。若同時編輯，先列印自由格式編輯內容再列印分項表格編輯內容。--></wicket:message>
					</b></font>
                </span>
				<br/>
                <button type="button" id="btnOpenL140s11a"><wicket:message key="btn.openL140s11a">開啟分項表格編輯功能</wicket:message></button>
				<button type="button" id="btnPreviewL140s11a"><wicket:message key="btn.previewL140s11a">預覽分項表格編輯之敘做案件異動比較表</wicket:message></button>
				<br/>
                <textarea cols="100" rows="10%" id="itemDscr9" name="itemDscr9" class="tckeditor" showType="b" showNewLineMessage="Y" distanceWord="44" wicket:message="displayMessage:cls1151s01.title09" t_width="800" t_height="500" preview="width:800;height:900"></textarea>
	            <div id="previewL140s11aBox" style="display:none;">
					<!--J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整-->
				    <p id="previewL140s11aSpan"></p>
				</div>
				<div id="l140s11aThickbox" style="display:none;">
					<button type="button" id="addL140s11a"><wicket:message key="button.add">新增</wicket:message></button>
					<button type="button" id="deleteL140s11a"><wicket:message key="button.delete">刪除</wicket:message></button>
					<button type="button" id="upL140s11aSeq"><span class="text-only"><wicket:message key="btn.upSeqno">向上移動</wicket:message></span></button>
					<button type="button" id="downL140s11aSeq"><span class="text-only"><wicket:message key="btn.downSeqno">向下移動</wicket:message></span></button>
					<div id="l140s11aGrid"></div>
				</div>
				<div id="l140s11aDetailThickbox" style="display:none;">
					<table id="l140s11aFormDetail" class="tb2" border="0" cellspacing="0" cellpadding="0" style="width:400px;height:250px;">
						<tbody>
						<span id="l140s11aOid" style="display:none"/>
						<span id="l140s11aSeqNum" style="display:none"/>
						<tr class="hd2">
							<td align="center" style="width:10%">
								<wicket:message key="L140S11A.applyItem">項目</wicket:message>
							</td>
							<td align="center" style="width:35%">
								<wicket:message key="L140S11A.befApply"></wicket:message>
							</td>
							<td align="center" style="width:35%">
								<wicket:message key="L140S11A.aftApply"></wicket:message>
							</td>
							<td align="center" style="width:20%">
								<wicket:message key="L140S11A.applyRemark"></wicket:message>
							</td>
						</tr>
						<tr>
							<td>
								<textarea id="applyItem" name="applyItem" cols="10" rows="2" maxlength="120" maxlengthC="100"></textarea>
							</td>
							<td>
								<!--J-113-0241 ELOAN-額度明細表-敘做條件異動情形-開啟分項表格編輯功能調整-->
								<textarea id="befApply" name="befApply" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
							</td>
							<td>
								<textarea id="aftApply" name="aftApply" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
							</td>
							<td>
								<textarea id="applyRemark" name="applyRemark" cols="40" rows="10" maxlength="1500" maxlengthC="600" class="ickeditor"></textarea>
							</td>
						</tr>
						</tbody>
					</table>
				</div>
			
				<script type="text/javascript" src="pagejs/cls/CLS1151S09Panel.js?ver=20240725"></script>
            </form>
        </wicket:panel>
    </body>
</html>
