/* 
 * L140S02G.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 代償轉貸借新還舊主檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S02G", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","seq"}))
public class L140S02G extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Digits(integer=5, fraction=0, groups = Check.class)
	@Column(name="SEQ", columnDefinition="DECIMAL(5,0)")
	private Integer seq;

	/** 
	 * 是否辦理「代償/轉貸/借新還舊」<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHGCASE", length=1, columnDefinition="CHAR(1)")
	private String chgCase;

	/** 
	 * 本筆借款是否用來償還其他筆貸款<p/>
	 * ※(包含因買賣案而代償前手之案件亦算)<br/>
	 *  Y/N
	 */
	@Size(max=1)
	@Column(name="CHGOTHER", length=1, columnDefinition="CHAR(1)")
	private String chgOther;

	/** 
	 * 該其他筆貸款之主借款人是否和本案主借款人相同 Y/N <br/>
	 * 轉貸有分2種 <br/>
	 * (A)主借人在另一間銀行的貸款 <br/>
	 * (B)買賣案件，償還的貸款是{房屋賣方}為主借人的貸款 <br/>
	 * 目前 LMS.L140S02G.chgOther 有 copy 至 LMS.C160S01E（ELF501_CHGCASE）
	 * 但是 LMS.L140S02G.chgBorrower 未上傳，可以用  LMS.L140S02H.subReason（ELF501_CHGREASON） 是否為{買賣}來判斷
	 */
	@Size(max=1)
	@Column(name="CHGBORROWER", length=1, columnDefinition="CHAR(1)")
	private String chgBorrower;

	/** 
	 * 是否為本行貸款<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHGALOAN", length=1, columnDefinition="CHAR(1)")
	private String chgALoan;

	/** 
	 * 轉貸日期<p/>
	 * ※(即本行代償他行貸款之撥款日！若選擇自動進帳則進帳日期要與轉貸日期相同。)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="ONLENTDATE", columnDefinition="DATE")
	private Date onlentDate;

	/** 
	 * 是否收取手續費<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHARGEFLAG", length=1, columnDefinition="CHAR(1)")
	private String chargeFlag;

	/** 
	 * 手續費金額<p/>
	 * 單位：新台幣元
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="CHARGEAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal chargeAmt;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得序號 **/
	public Integer getSeq() {
		return this.seq;
	}
	/** 設定序號 **/
	public void setSeq(Integer value) {
		this.seq = value;
	}

	/** 
	 * 取得是否辦理「代償/轉貸/借新還舊」<p/>
	 * Y/N
	 */
	public String getChgCase() {
		return this.chgCase;
	}
	/**
	 *  設定是否辦理「代償/轉貸/借新還舊」<p/>
	 *  Y/N
	 **/
	public void setChgCase(String value) {
		this.chgCase = value;
	}

	/** 
	 * 取得本筆借款是否用來償還其他筆貸款<p/>
	 * ※(包含因買賣案而代償前手之案件亦算)<br/>
	 *  Y/N
	 */
	public String getChgOther() {
		return this.chgOther;
	}
	/**
	 *  設定本筆借款是否用來償還其他筆貸款<p/>
	 *  ※(包含因買賣案而代償前手之案件亦算)<br/>
	 *  Y/N
	 **/
	public void setChgOther(String value) {
		this.chgOther = value;
	}

	/** 
	 * 取得該其他筆貸款之主借款人是否和本案主借款人相同<p/>
	 * Y/N
	 */
	public String getChgBorrower() {
		return this.chgBorrower;
	}
	/**
	 *  設定該其他筆貸款之主借款人是否和本案主借款人相同<p/>
	 *  Y/N
	 **/
	public void setChgBorrower(String value) {
		this.chgBorrower = value;
	}

	/** 
	 * 取得是否為本行貸款<p/>
	 * Y/N
	 */
	public String getChgALoan() {
		return this.chgALoan;
	}
	/**
	 *  設定是否為本行貸款<p/>
	 *  Y/N
	 **/
	public void setChgALoan(String value) {
		this.chgALoan = value;
	}

	/** 
	 * 取得轉貸日期<p/>
	 * ※(即本行代償他行貸款之撥款日！若選擇自動進帳則進帳日期要與轉貸日期相同。)
	 */
	public Date getOnlentDate() {
		return this.onlentDate;
	}
	/**
	 *  設定轉貸日期<p/>
	 *  ※(即本行代償他行貸款之撥款日！若選擇自動進帳則進帳日期要與轉貸日期相同。)
	 **/
	public void setOnlentDate(Date value) {
		this.onlentDate = value;
	}

	/** 
	 * 取得是否收取手續費<p/>
	 * Y/N
	 */
	public String getChargeFlag() {
		return this.chargeFlag;
	}
	/**
	 *  設定是否收取手續費<p/>
	 *  Y/N
	 **/
	public void setChargeFlag(String value) {
		this.chargeFlag = value;
	}

	/** 
	 * 取得手續費金額<p/>
	 * 單位：新台幣元
	 */
	public BigDecimal getChargeAmt() {
		return this.chargeAmt;
	}
	/**
	 *  設定手續費金額<p/>
	 *  單位：新台幣元
	 **/
	public void setChargeAmt(BigDecimal value) {
		this.chargeAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
