/** 將原先M01中只用於S01的code搬移至對應的js檔 */
$(function(){
   
    var SignReportGrid = $("#signReportGrid").iGrid({
        handler: "lms2305gridhandler",
        height: 235,
        width: 400,
        postData: {
            formAction: "queryL120M01A"
        },
        rowNum: 10,
        rownumbers: true,
        sortname: 'caseNo|caseDate|endDate',
        sortorder: 'asc|asc|asc',
        colModel: [{
            colHeader: i18n.cls2301m01['l230m01a.title07'],//"主要借款人"
            name: 'custName',
            align: "left",
            width: 60
        }, {
            colHeader: i18n.cls2301m01['l230m01a.title05'],//案號	
            name: 'caseNo',
            align: "left",
            width: 100
        }, {
            colHeader: i18n.cls2301m01['l230m01a.title06'],//"簽案日期"
            name: 'caseDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            align: "center",
            width: 60
        }, {
            colHeader: i18n.cls2301m01['l230m01a.title19'],//"核准日期"
            name: 'endDate',
            formatter: 'date',
            formatoptions: {
                srcformat: 'Y-m-d',
                newformat: 'Y-m-d'
            },
            align: "center",
            width: 60
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true //是否隱藏
        }]
    });
    
    
    /**重新引進簽報書*/
    $("#selectCaseTableBt").click(function(){
        LMS2305Action.SignReportGridReload();
        $("#signReport").thickbox({
            //l230m01a.title18=請選擇1份已核准的案件簽報書
            title: i18n.cls2301m01['l230m01a.title18'],
            width: 600,
            height: 440,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $("#signReportGrid").getGridParam('selrow');
                    if (!id) {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showMessage(i18n.def["action_005"]);
                    }
                    var result = $("#signReportGrid").getRowData(id);
                    LMS2305Action.create_NewDoc_Form_OldDoc(null, null, result);
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    });
});

