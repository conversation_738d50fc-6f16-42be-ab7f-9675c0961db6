---------------------------------------------------------
-- LMS.L140M01L 團貸案資料檔
---------------------------------------------------------

---------------------------------------------------------
-- TABLE
---------------------------------------------------------
--DROP TABLE LMS.L140M01L;
CREATE TABLE LMS.L140M01L (
	OID           CHAR(32)      not null,
	MAINID        CHAR(32)      not null,
	ITEMTY<PERSON><PERSON>      CHAR(1)      ,
	<PERSON>OA<PERSON><PERSON><PERSON>      CHAR(1)      ,
	CASECODE      VARCHAR(10)  ,
	CASENAME      VARCHAR(120) ,
	CASEYEA<PERSON>      DECIMAL(4,0) ,
	CASEBRID      CHAR(3)      ,
	<PERSON><PERSON><PERSON><PERSON>R      CHAR(3)      ,
	LOANAMT       DECIMAL(13,0),
	LOANMASTERNO  CHAR(12)     ,
	CITYID        VARCHAR(10)  ,
	AREAID        VARCHAR(3)   ,
	IR48          CHAR(4)      ,
	LANDPART1     VARCHAR(15)  ,
	LANDPART2     VARCHAR(15)  ,
	EXPMONTHS     DECIMAL(3,0) ,
	EXPBDATE      DATE         ,
	EXPEDATE      DATE         ,
	CHECKRESULT   VARCHAR(1200),
	ISBUILDER     CHAR(1)      ,
	CREATOR       CHAR(6)      ,
	CREATETIME    TIMESTAMP    ,
	UPDATER       CHAR(6)      ,
	UPDATETIME    TIMESTAMP    ,

	constraint P_L140M01L PRIMARY KEY(OID)
) IN EL_DATA_8KTS index in EL_INDEX_4KTS ;

---------------------------------------------------------
-- INDEX
---------------------------------------------------------
--DROP INDEX LMS.XL140M01L01;
CREATE UNIQUE INDEX LMS.XL140M01L01 ON LMS.L140M01L   (MAINID);

---------------------------------------------------------
-- TABLE LABEL
---------------------------------------------------------
COMMENT ON TABLE LMS.L140M01L IS '團貸案資料檔';
COMMENT ON LMS.L140M01L (
	OID           IS 'oid', 
	MAINID        IS '文件編號', 
	ITEMTYPE      IS '團貸案項目', 
	LOANTYPE      IS '團貸案種類', 
	CASECODE      IS '團貸案代碼', 
	CASENAME      IS '團貸案名稱', 
	CASEYEAR      IS '簽案年度', 
	CASEBRID      IS '呈報分行', 
	LOANCURR      IS '總申請額度(幣別)', 
	LOANAMT       IS '總申請額度(金額)', 
	LOANMASTERNO  IS '團貸總戶案號', 
	CITYID        IS '土地座落(縣市)', 
	AREAID        IS '土地座落(鄉鎮市區)', 
	IR48          IS '土地座落(段小段)', 
	LANDPART1     IS '土地坐落區-大段', 
	LANDPART2     IS '土地坐落區-小段', 
	EXPMONTHS     IS '總行額度有效期限（月數）', 
	EXPBDATE      IS '總行額度有效起迄日期－起', 
	EXPEDATE      IS '總行額度有效起迄日期－迄',
	CHECKRESULT   IS '模糊比對結果',
	ISBUILDER     IS '是否為建商貸款',
	CREATOR       IS '建立人員號碼', 
	CREATETIME    IS '建立日期', 
	UPDATER       IS '異動人員號碼', 
	UPDATETIME    IS '異動日期'
);
