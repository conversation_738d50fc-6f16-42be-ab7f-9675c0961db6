package com.mega.eloan.lms.cls.report.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.AbstractReportService;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.ProdService;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.cls.pages.CLS1151S01Page;
import com.mega.eloan.lms.cls.pages.CLS1161S02APage;
import com.mega.eloan.lms.cls.service.CLS1151Service;
import com.mega.eloan.lms.cls.service.CLS1161Service;
import com.mega.eloan.lms.dao.C160M01ADao;
import com.mega.eloan.lms.dao.C160M01BDao;
import com.mega.eloan.lms.dao.C160M01CDao;
import com.mega.eloan.lms.dao.C160M01DDao;
import com.mega.eloan.lms.dao.C160M01EDao;
import com.mega.eloan.lms.dao.C160M01FDao;
import com.mega.eloan.lms.dao.C160S01ADao;
import com.mega.eloan.lms.dao.C160S01BDao;
import com.mega.eloan.lms.dao.C160S01CDao;
import com.mega.eloan.lms.dao.C160S01DDao;
import com.mega.eloan.lms.dao.C160S01EDao;
import com.mega.eloan.lms.dao.C160S01FDao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.dao.L140S02EDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C160M01A;
import com.mega.eloan.lms.model.C160M01B;
import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.C160M01D;
import com.mega.eloan.lms.model.C160M01E;
import com.mega.eloan.lms.model.C160M01F;
import com.mega.eloan.lms.model.C160S01A;
import com.mega.eloan.lms.model.C160S01B;
import com.mega.eloan.lms.model.C160S01C;
import com.mega.eloan.lms.model.C160S01D;
import com.mega.eloan.lms.model.C160S01E;
import com.mega.eloan.lms.model.C160S01F;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 * 產生動審表PDF
 * </pre>
 * 
 * @since 2012/12/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/26,GaryChang ,new
 *          <li>2013/07/16,Rex,明澤說是否為本行貸款不列印
 *          </ul>
 */
@Service("cls1161r01rptservice")
public class CLS1161R01RptServiceImpl extends AbstractReportService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(CLS1161R01RptServiceImpl.class);
	@Resource
	CLS1151Service cls1151Service;
	@Resource
	EloandbBASEService eloanDbService;
	@Resource
	UserInfoService userInfoService;
	@Resource
	C160M01ADao c160m01adao;
	@Resource
	C160M01BDao c160m01bdao;
	@Resource
	C160M01CDao c160m01cdao;
	@Resource
	C160M01DDao c160m01ddao;
	@Resource
	C160M01EDao c160m01edao;
	@Resource
	C160M01FDao c160m01fdao;
	@Resource
	C160S01CDao c160s01cdao;
	@Resource
	C160S01ADao c160s01adao;
	@Resource
	C160S01BDao c160s01bdao;
	@Resource
	C160S01DDao c160s01ddao;
	@Resource
	C160S01EDao c160s01edao;
	@Resource
	C160S01FDao c160s01fdao;
	@Resource
	L140S02EDao l140S02edao;
	@Resource
	BranchService branch;
	@Resource
	ProdService prodService;
	@Resource
	CodeTypeService codeTypeService;
	@Resource
	L140S02ADao l140s02aDao;
	@Resource
	CLSService clsService;
	@Resource
	CLS1161Service cls1161Service;

	@Override
	public String getReportTemplateFileName() {
		LOGGER.info("into getReportTemplateFileName");
		// zh_TW: 正體中文
		// zh_CN: 簡體中文
		// en_US: 英文
		Locale locale = LocaleContextHolder.getLocale();
		if (locale == null)
			locale = Locale.getDefault();
		// 測試用
		// return
		// "D:/work/src.mega/WebELoanCLS1/lms/lms-config/src/main/resources/report/cls/CLS1161R01_zh_TW.rpt";
		return "report/cls/CLS1161R01_" + locale.toString() + ".rpt";
	}

	/*
	 * (non-Javadoc) 設定需要傳入RPT參數
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.AbstractReportService#setReportData(com
	 * .mega.eloan.lms.base.report.ReportGenerator,
	 * com.iisigroup.cap.component.PageParameters)
	 */
	@Override
	public void setReportData(ReportGenerator reportTools, PageParameters params) {
		// 測試用
		// reportTools.setTestMethod(true);
		LOGGER.info("into setReportData");
		Properties prop = null;
		prop = MessageBundleScriptCreator
				.getComponentResource(CLS1161R01RptServiceImpl.class);
		
		Properties prop_CLS1161S02A = MessageBundleScriptCreator.getComponentResource(CLS1161S02APage.class);
		//=================
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		// String mainOid = params.getString(EloanConstants.MAIN_OID);
		String mainId = params.getString(EloanConstants.MAIN_ID);
		
		// UPGRADETODO - 修復升級後 PDF 空白頁面問題，加入完整的異常處理
		try {
			LOGGER.info("開始處理報表資料，mainId: " + mainId);
			
			// L160M01A．動用審核表主檔
			C160M01A c160m01a = null;
			// C160M01B．動審表額度序號資料
			List<C160M01B> c160m01bList = null;
			// C160M01C．動審表查核項目資料
			List<C160M01C> c160m01cList = null;
			// C160S01C．動審表產品種類資料
			List<C160S01C> c160s01cList = null;
			C160M01D c160m01d = null;
			String branchName = null;
			// zh_TW: 正體中文
			// zh_CN: 簡體中文
			// en_US: 英文
			Locale locale = null;
			locale = LocaleContextHolder.getLocale();
			if (locale == null)
				locale = Locale.getDefault();
			c160m01a = c160m01adao.findByMainId(mainId);
			c160m01d = c160m01ddao.findByUniqueKey(mainId);
			if (c160m01d == null)
				c160m01d = new C160M01D();
			if (c160m01a == null)
				c160m01a = new C160M01A();
			branchName = Util.nullToSpace(branch.getBranchName(Util
					.nullToSpace(user.getUnitNo())));
			c160m01bList = c160m01bdao.findByMainId(c160m01a.getMainId());
			c160m01cList = c160m01cdao.findByMainId(c160m01a.getMainId());

			c160s01cList = c160s01cdao.findByMainId(c160m01a.getMainId());
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", branchName);
			rptVariableMap.put("C160M01A.LOANMASTERNO",
					Util.trim(c160m01a.getLoanMasterNo()));
			if (Util.isEmpty(Util.trim(c160m01a.getLoanMasterNo()))) {
				rptVariableMap.put("C160M01A.LOANMASTERNOMARK", "");
			} else {
				rptVariableMap.put("C160M01A.LOANMASTERNOMARK",
						prop.getProperty("C160M01A.TEXT1"));
			}
			rptVariableMap = this.setC160M01AData(rptVariableMap, c160m01a);

			rptVariableMap = this.setC160M01DData(rptVariableMap, c160m01d);

			titleRows = this.setC160M01CDataList(titleRows, c160m01cList);
			// c160m01bList.size()==1&&c160s01cList.size()==1
			if (c160m01bList.size() == 1 && c160s01cList.size() == 1) {
				rptVariableMap = this.setC160S01CData(rptVariableMap,
						c160m01bList, c160s01cList, c160m01a, true, prop, prop_CLS1161S02A);
			} else {
				if (!Util.equals(c160m01a.getCaseType(), "3")) {
					titleRows = this.setC160S01CDataList(c160m01a, titleRows,
							c160m01bList, prop, prop_CLS1161S02A);
				}
				rptVariableMap = this.setC160S01CData(rptVariableMap,
						c160m01bList, c160s01cList, c160m01a, false, prop, prop_CLS1161S02A);
			}
			
			//J-109-0150_10702_B1001 Web e-Loan IVR頁籤由模擬動審移至動審表
			String ivrString="錄音檔：<br>";
			boolean ivrFlag=false;
			for(C160M01B c160m01b:c160m01bList){
				if(!Util.isEmpty(c160m01b.getIVRFlag())){
					ivrString += Util.trim(c160m01b.getCustName()+" "+c160m01b.getIVRFlag())+"<br>";
					ivrFlag =true;
				}
			}
			if(ivrFlag){
				rptVariableMap.put("ivrFlag", ivrString);
			}
			else{
				rptVariableMap.put("ivrFlag", null);
			}
			
			// this.generator.setLang(java.util.Locale.TAIWAN);
			reportTools.setLang(locale);
			reportTools.setVariableData(rptVariableMap);
			reportTools.setRowsData(titleRows);
			// new ReportGenerator().checkVariableExist("C:/test.txt",
			// rptVariableMap);
			
			LOGGER.info("報表資料處理完成");
			
		} catch (Exception e) {
			// UPGRADETODO - 修復升級後 PDF 空白頁面問題
			// 當資料查詢或處理失敗時，提供基本的報表內容避免空白頁面
			LOGGER.error("報表資料處理失敗，使用預設資料: " + e.getMessage(), e);
			
			// 設定基本的錯誤資訊，確保 PDF 有內容
			Map<String, String> errorVariableMap = new LinkedHashMap<String, String>();
			errorVariableMap.put("BRANCHNAME", user != null ? user.getUnitNo() : "系統");
			errorVariableMap.put("ERROR_MESSAGE", "報表資料載入失敗，請聯絡系統管理員");
			errorVariableMap.put("ERROR_DETAILS", "錯誤訊息: " + e.getMessage());
			errorVariableMap.put("MAIN_ID", mainId != null ? mainId : "未提供");
			
			// 設定預設的語言和基本資料
			Locale defaultLocale = LocaleContextHolder.getLocale();
			if (defaultLocale == null) defaultLocale = Locale.getDefault();
			
			reportTools.setLang(defaultLocale);
			reportTools.setVariableData(errorVariableMap);
			
			// 設定空的表格資料避免報表引擎錯誤
			List<Map<String, String>> emptyRows = new LinkedList<Map<String, String>>();
			reportTools.setRowsData(emptyRows);
			
		} finally {

		}
	}

	/**
	 * 塞入變數MAP資料使用(C160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC160M01AData(
			Map<String, String> rptVariableMap, C160M01A c160m01a) {

		rptVariableMap.put("L160M01A.USETYPE",
				Util.nullToSpace(c160m01a.getUseType()));
		rptVariableMap.put("L160M01A.RANDOMCODE",
				Util.nullToSpace(c160m01a.getRandomCode()));
		rptVariableMap.put("L160M01A.CASENO",
				Util.toSemiCharString(Util.nullToSpace(c160m01a.getCaseNo())));
		rptVariableMap.put("C160M01A.RANDOMCODE",
				Util.nullToSpace(c160m01a.getRandomCode()));
		rptVariableMap.put("C160M01A.COMM",
				Util.nullToSpace(c160m01a.getComm()));
		rptVariableMap.put("C160M01A.SIGN",
				Util.nullToSpace(c160m01a.getSign()));
		rptVariableMap.put("C160M01A.CASENO",
				Util.toSemiCharString(Util.nullToSpace(c160m01a.getCaseNo())));
		rptVariableMap.put("C160M01A.MAINAPPRID",
				this.getUserName(Util.nullToSpace(c160m01a.getMainApprId())));
		rptVariableMap
				.put("C160M01A.MAINMANAGERID", this.getUserName(Util
						.nullToSpace(c160m01a.getMainManagerId())));
		rptVariableMap.put(
				"C160M01A.APPRID",
				Util.nullToSpace(c160m01a.getApprId())
						+ " "
						+ Util.nullToSpace(this.getUserName(Util
								.nullToSpace(c160m01a.getApprId()))));
		rptVariableMap.put(
				"C160M01A.RECHECKID",
				Util.nullToSpace(c160m01a.getReCheckId())
						+ " "
						+ Util.nullToSpace(this.getUserName(Util
								.nullToSpace(c160m01a.getReCheckId()))));
		List<C160M01E> c160m01eList = c160m01edao.findByMainId(c160m01a
				.getMainId());
		StringBuilder sb = new StringBuilder();
		for (C160M01E c160m01e : c160m01eList) {
			if (UtilConstants.STAFFJOB.授信主管L3.equals(c160m01e.getStaffJob())) {
				sb.append(sb.length() > 0 ? "<br/>" : "");
				String staffNo = Util.trim(c160m01e.getStaffNo());
				sb.append(staffNo).append(" ");
				sb.append(this.getUserName(staffNo));
			}
		}
		rptVariableMap.put("C160M01A.BOSSID", Util.nullToSpace(sb.toString()));
		rptVariableMap.put(
				"C160M01A.MANAGERID",
				Util.nullToSpace(c160m01a.getManagerId())
						+ " "
						+ Util.nullToSpace(this.getUserName(Util
								.nullToSpace(c160m01a.getManagerId()))));
		rptVariableMap.put("C160M01A.MAINRECHECKID", "");
		rptVariableMap.put("C160S01A.RPTID", Util.trim(c160m01a.getRptId()));
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(L160M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120m01a
	 *            L120M01A資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC160M01DData(
			Map<String, String> rptVariableMap, C160M01D c160m01d) {
		rptVariableMap.put("C160M01D.WAITINGITEM",
				Util.nullToSpace(c160m01d.getWaitingItem()));
		rptVariableMap.put("C160M01D.TWILLFINISHDATE",
				Util.getDate(c160m01d.getWillFinishDate()));
		rptVariableMap.put("C160M01D.FINISHDATE",
				Util.getDate(c160m01d.getFinishDate()));
		rptVariableMap.put("C160M01D.ITEMTRACE",
				Util.nullToSpace(c160m01d.getItemTrace()));
		// managerid為0時表示自行輸入
		String managerid = "";
		if ("0".equals(c160m01d.getManagerId())) {
			managerid = c160m01d.getManagerNm();
		} else {
			managerid = this.getUserName(Util.nullToSpace(c160m01d
					.getManagerId()));
		}
		rptVariableMap.put("C160M01D.MANAGERID", managerid);
		rptVariableMap.put("C160M01D.BOSSID",
				this.getUserName(Util.nullToSpace(c160m01d.getBossId())));
		rptVariableMap.put("C160M01D.APPRAISERID",
				this.getUserName(Util.nullToSpace(c160m01d.getAppraiserId())));
		return rptVariableMap;
	}

	private String _calLoanAmt(C160M01A c160m01a, List<C160M01B> c160m01blist,
			List<C160S01C> c160s01csingle, Properties prop) {
		BigDecimal c160m01bAmt = BigDecimal.ZERO;
		BigDecimal c160s01cAmt = BigDecimal.ZERO;

		BigDecimal srcC160m01bAmt = BigDecimal.ZERO;
		BigDecimal srcC160s01cAmt = BigDecimal.ZERO;
		BigDecimal tmpAmt = BigDecimal.ZERO;

		String c160m01bCurr = "";
		String c160s01cCurr = "";
		List<String> line2_list = new ArrayList<String>();

		for (C160M01B c160m01b : c160m01blist) {
			c160m01bAmt = c160m01bAmt.add(c160m01b.getLoanTotAmt());

			tmpAmt = "".equals(Util.trim(c160m01b.getBfLoanTotAmt())) ? c160m01b.getLoanTotAmt()
					: c160m01b.getBfLoanTotAmt();
			srcC160m01bAmt = srcC160m01bAmt.add(tmpAmt);
			c160m01bCurr = Util.isNotEmpty(c160m01b.getLoanTotCurr()) ? Util.trim(c160m01b.getLoanTotCurr()) : "TWD";
		}
		for (C160S01C c160s01c : c160s01csingle) {
			c160s01cAmt = c160s01cAmt.add(c160s01c.getApprovedAmt());
			srcC160s01cAmt = srcC160s01cAmt.add(c160s01c.getLoanAmt());
			c160s01cCurr = Util.isNotEmpty(c160s01c.getLoanCurr()) ? Util.trim(c160s01c.getLoanCurr()) : "TWD";
		}

		if(c160m01a.getTotAmt()!=null){
			c160m01bAmt = c160m01a.getTotAmt();
		}
		if(c160m01a.getBfTotAmt()!=null){
			srcC160m01bAmt = c160m01a.getBfTotAmt();
		}
		String line1_desc = prop.getProperty("C160S01C.approvedAmt") + "：";
		String line1_content = c160m01bCurr
				+ NumConverter.addComma(c160m01bAmt)
				+ prop.getProperty("C160S01C.TWYen") + "("
				+ prop.getProperty("C160S01C.loanAmt") + "：" + c160s01cCurr
				+ NumConverter.addComma(c160s01cAmt)
				+ prop.getProperty("C160S01C.TWYen") + ")";

		if (srcC160m01bAmt.compareTo(c160m01bAmt) == 0) {

		} else {
			if (srcC160m01bAmt.compareTo(BigDecimal.ZERO) > 0) {
				//C160S01C.srcapprovedAmt=原始核准額度
				line2_list.add(prop.getProperty("C160S01C.srcapprovedAmt") + "："
						+ c160m01bCurr + NumConverter.addComma(srcC160m01bAmt)
						+ prop.getProperty("C160S01C.TWYen"));
			}
		}

		if (srcC160s01cAmt.compareTo(c160s01cAmt) == 0) {

		} else {
			if (srcC160s01cAmt.compareTo(BigDecimal.ZERO) > 0) {
				//C160S01C.srcloanAmt=原始可動撥金額
				line2_list.add("(" + prop.getProperty("C160S01C.srcloanAmt")
						+ "：" + c160s01cCurr
						+ NumConverter.addComma(srcC160s01cAmt)
						+ prop.getProperty("C160S01C.TWYen") + ")");
			}
		}

		//==================
		//第1行
		//若一次動用2個額度
		//TODO 授信科目：產品A(科目甲)、產品B(科目乙)
		//     核准額度：TWD A+B 元(實際動撥金額：TWD A+B元)
		// p.s. 在此看不出A、B的個別額度
		//      要看下一頁(附表)的內容 
		String line1 = line1_desc + line1_content;
		
		//==================
		//第2行
		//當有下調［核准額度 or 產品動撥金額］
		//才會輸出第2行
		String line2 = "";
		if(line2_list.size()>0){
			//5個全型空白
			line2 = "<br/>" + "　　　　　"+("[" + StringUtils.join(line2_list, "") + "]");
		}
		
		String line3 = "";
		if(true){
			List<String> line3_list = new ArrayList<String>();
			for(C160S01C c160s01c: c160s01csingle){
				if(CrsUtil.is_67(c160s01c.getProdKind()) ){
					line3_list.add(CrsUtil.PROD_67_DESC+"："
							+prop.getProperty("CLS1161R01.rmRctAmt")+" "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getRctAMT()))+"元"
							+"("+prop.getProperty("CLS1161R01.rmIntMax")+" "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getRmIntMax()))+"元) "
							+" 共 "+ClsUtil.prod_totalPeriod(c160s01c.getYear(), c160s01c.getMonth())+"期"
							+"，總計 "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getApprovedAmt()))+"元");					
				}else if(CrsUtil.is_70(c160s01c.getProdKind())){
					//L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
					//BigDecimal prodAmt = c160s01c.getApprovedAmt();
					//BigDecimal amt_each_period = Arithmetic.div(prodAmt, new BigDecimal(ClsUtil.prod_totalPeriod(l140s02a)));
					//必須跟傳統型的{每期進帳金額、首次進帳日}一致，但因非自動進帳，不能抓 default資料					
					line3_list.add(CrsUtil.PROD_70_DESC+"："
							//+prop.getProperty("CLS1161R01.rmRctAmt.prodKind70")+" "+NumConverter.addComma(LMSUtil.pretty_numStr(amt_each_period))+"元"
							+prop.getProperty("CLS1161R01.rmRctAmt.prodKind70")+" "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getRctAMT()))+"元"
							+"("+prop.getProperty("CLS1161R01.rmIntMax")+" "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getRmIntMax()))+"元) "
							+" 共 "+ClsUtil.prod_totalPeriod(c160s01c.getYear(), c160s01c.getMonth())+"期"
							+"，總計 "+NumConverter.addComma(LMSUtil.pretty_numStr(c160s01c.getApprovedAmt()))+"元");					
				}
			}
			if(line3_list.size()>0){
				line3 = "<br/>" +  StringUtils.join(line3_list, "<br/>");	
			}
		}
		return line1+line2+line3;		
	}

	/**
	 * 以 boolean singlecheck 來區分 [1個額度 && 1個產品]
	 */
	private Map<String, String> setC160S01CData(
			Map<String, String> rptVariableMap, List<C160M01B> c160m01blist,
			List<C160S01C> c160s01csingle, C160M01A c160m01a,
			boolean singlecheck, Properties prop, Properties prop_CLS1161S02A) {
		Map<String, String> SubjCodeMap = codeTypeService
				.findByCodeType("lms1705m01_SubItme");
		Map<String, String> YesNoMap = codeTypeService
				.findByCodeType("Common_YesNo");
		Map<String, String> prodKindMap = prodService.getProdKindName();
		List<String> SubjCodeList = new ArrayList<String>();
		List<String> CustNameList = new ArrayList<String>();
		BigDecimal c160s01cLoanAmt = new BigDecimal(0);

		BigDecimal c160s01cApprovedAmt = new BigDecimal(0);
		String c160s01cLoanCurr = "";

		String c160s01cCntrNo = "";
		String c160s01csubjCode = "";
		String c160s01cDeadline = "";
		String c160s01crateDesc = "";
		String c160s01cautoRct = "";
		String c160s01caccNo = "";
		String c160s01cAppOtherBranchNo = "";
		String c160s01cAppOtherAccount = "";
		String c160s01cefcBh = "";
		String c160s01crctDate = "";
		String c160s01crctAmt = "";
		String c160s01cautopay = "";
		String c160s01catpayNo = "";
		String c160s01cAchBranchNo = "";
		String c160s01cAchAccount = "";
		String c160s01aCmsDesc = "";
		String c160s01cLnselect = "";
		String c160s01cPmt_1st_rt_dt = "";
		String c160s01cuseline = "";
		String c160s01eChgCase = "";
		String c160s01cdRateAdd = "";
		String c160m01bJoinMarkDesc = "";
		String newc160s01csubjCode = "";
		String c160s01cdcheck = "";
		String c160s01cdMonth1 = "";
		String c160s01cdRate1 = "";
		String c160s01cdMonth2 = "";
		String c160s01cdRate2 = "";
		String c160m01bcustname = "";
		String c160s01fdata = "";
		String joinmarketingdate = "";
		String c160s01bcustdata = "";
		String l140m01r1staffjobl1 = "";		
		String c160s01cAmtDesc = "";
		/*
		 * 組字
		 */
		String PARAM_C160S01C_DelayedIntDRateArea = "";
		if (singlecheck) {
			C160M01B c160m01b = c160m01blist.get(0);
			c160m01b = Util.isEmpty(c160m01b) ? new C160M01B() : c160m01b;
			C160S01C c160s01c = c160s01csingle.get(0);
			L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
			
			String tCaseNo = Util.toSemiCharString(c160m01b.getCaseNo());
			l140m01r1staffjobl1 = setL140M01RListData(c160m01a.getMainId(),
					tCaseNo);

			String mainId = c160s01c.getMainId();
			String refmainId = c160s01c.getRefmainId();
			int seq = c160s01c.getSeq();
			List<C160S01A> c160s01alist = c160s01adao.findByMainIdRefMainId(
					mainId, refmainId);
			// C160S01A c160s01a = c160s01adao.findByMainIdRefMainIdSeq(mainId,
			// refmainId, seq);
			C160S01E c160s01e = c160s01edao.findByMainIdRefMainIdSeq(mainId,
					refmainId, seq);
			c160s01e = Util.isEmpty(c160s01e) ? new C160S01E() : c160s01e;
			c160s01cLoanCurr = c160s01c.getLoanCurr();

			c160s01cAmtDesc = _calLoanAmt(c160m01a, c160m01blist, c160s01csingle, prop);

			// 動審表明細的動撥額度
			if (!Util.isEmpty(c160s01c.getApprovedAmt())) {
				c160s01cApprovedAmt = c160s01cApprovedAmt.add(c160s01c
						.getApprovedAmt());
			}
			// 核准額度
			if (!Util.isEmpty(c160s01c.getLoanAmt())) {
				c160s01cLoanAmt = c160s01cLoanAmt.add(c160s01c.getLoanAmt());
			}

			L140S02E l140s02e = l140S02edao.findByUniqueKey(refmainId,
					c160s01c.getSeq());
			if (Util.isEmpty(l140s02e)) {
				l140s02e = new L140S02E();
			}
			c160s01cCntrNo = c160s01c.getCntrNo();
			// 2013-08-19更改動審表的授信科目顯示方式同額度明細表
			String prodKindNm = prodKindMap.get(Util.trim(c160s01c
					.getProdKind()));
			if(l140s02a!=null){
				String disasType = Util.trim(l140s02a.getDisasType());
				if(Util.isNotEmpty(disasType)){
					Map<String, String> codeMap = clsService.get_disasType_desc();
					prodKindNm = prodKindNm+"-"+LMSUtil.getDesc(codeMap, disasType);
				}
			}
			newc160s01csubjCode = prodKindNm
					+ "("
					+ SubjCodeMap.get(Util.trim(c160s01c.getSubjCode())) + ")";
			c160s01cDeadline = Util.trim(c160s01c.getYear())
					+ prop.getProperty("C160S01C.Year")
					+ Util.trim(c160s01c.getMonth())
					+ prop.getProperty("C160S01C.Month");
			if (!Util.isEmpty(c160s01c.getNowFrom())
					&& !Util.isEmpty(c160s01c.getNowEnd())) {
				c160s01cDeadline = c160s01cDeadline + "("
						+ prop.getProperty("L140S02E.txt1")
						+ c160s01c.getNowFrom()
						+ prop.getProperty("L140S02E.Unit") + "~"
						+ c160s01c.getNowEnd()
						+ prop.getProperty("L140S02E.Unit") + ")";
			}
			c160s01crateDesc = Util.toSemiCharString(Util.trim(c160s01c
					.getRateDesc()));
			c160s01crateDesc = c160s01crateDesc.replaceAll("∼", "~");
			c160s01cautoRct = YesNoMap.get(c160s01c.getAutoRct());
			c160s01caccNo = Util.trim(c160s01c.getAccNo());
			c160s01cAppOtherBranchNo = Util.trim(c160s01c.getAppOtherBranchNo());
			c160s01cAppOtherAccount = Util.trim(c160s01c.getAppOtherAccount());
			c160s01cefcBh = Util.trim(c160s01c.getEfctBH()) + " "
					+ Util.trim(branch.getBranchName(c160s01c.getEfctBH()));
			c160s01crctDate = Util.getDate(c160s01c.getRctDate());
			c160s01crctAmt = NumConverter.addComma(c160s01c.getRctAMT())
					+ prop.getProperty("C160S01C.TWYen");
			c160s01cautopay = YesNoMap.get(c160s01c.getAutoPay());
			c160s01catpayNo = Util.trim(c160s01c.getAtpayNo());
			c160s01cAchBranchNo = Util.trim(c160s01c.getAchBranchNo());
			c160s01cAchAccount = Util.trim(c160s01c.getAchAccount());
			for (C160S01A c160s01a : c160s01alist) {
				c160s01aCmsDesc = c160s01aCmsDesc + getCmsDesc(c160s01a, prop, prop_CLS1161S02A);
			}
			c160s01cLnselect = Util.trim(c160s01c.getLnSelect());
			c160m01bcustname = c160m01b.getCustId() + "  "
					+ c160m01b.getCustName();
			joinmarketingdate = Util.getDate(c160m01b.getJoinMarketingDate());
			c160s01cLnselect = Util.nullToSpace(Util.getDate(c160s01c
					.getLnStartDate()))
					+ " ~ "
					+ Util.nullToSpace(Util.getDate(c160s01c.getLnEndDate()));
			c160s01cPmt_1st_rt_dt = Util.trim(TWNDate.toAD(c160s01c.getPmt_1st_rt_dt()));
			c160s01cuseline = Util.nullToSpace(Util.getDate(c160s01c
					.getUseStartDate()))
					+ " ~ "
					+ Util.nullToSpace(Util.getDate(c160s01c.getUseEndDate()));
			// -------------------------------
			if (Util.equals(c160s01e.getChgOther(), "Y")) {
				c160s01fdata = getc160s01fData(c160s01c);
			} else {
				c160s01fdata = prop.getProperty("C160S01F.txt7"
						+ Util.trim(c160s01e.getChgOther()));
			}
			c160s01eChgCase = c160s01e.getChgOther();
			c160s01cdRateAdd = _get_dRateAdd(c160s01c);
			c160m01bJoinMarkDesc = Util.nullToSpace(c160m01b.getJoinMarkDesc());
			c160s01cdcheck = "Y";// --------------
			c160s01cdMonth1 = Util.nullToSpace(c160s01c.getDMonth1());
			c160s01cdRate1 = Util.nullToSpace(c160s01c.getDRate1());
			c160s01cdMonth2 = Util.nullToSpace(c160s01c.getDMonth2());
			c160s01cdRate2 = Util.nullToSpace(c160s01c.getDRate2());
			
			//在 rpt 中，用 c160s01cdcheck是否==Y，來判斷 隱藏
			if(Util.isNotEmpty(Util.trim(c160s01c.getCtrType()))){
				c160s01cdcheck = _get_dMonthAndRate(c160m01a, c160s01c);				
			}else{
				//舊的動審表
			}
			PARAM_C160S01C_DelayedIntDRateArea = _get_DelayedIntDRateArea_html_tableFormat(c160m01a, prop_CLS1161S02A, c160s01c, null, false);	
			
			c160s01bcustdata = getcustData(c160m01b, prop);
		} else {
			// 詳附表
			String see_addendum = prop.getProperty("C160S01C.text1");
			if (!Util.equals(c160m01a.getCaseType(), "3")) {
				for (C160M01B c160m01b : c160m01blist) {
					List<C160S01C> c160s01clist = c160s01cdao
							.findByMainIdRefMainid(c160m01b.getMainId(),
									c160m01b.getRefmainId());
					CustNameList.add(c160m01b.getCustId() + " "
							+ c160m01b.getCustName());
					for (C160S01C c160s01c : c160s01clist) {

						SubjCodeList.add(c160s01c.getSubjCode()
								+ Util.trim(c160s01c.getProdKind()));

						String prodKind = Util.trim(c160s01c.getProdKind());
						if (CrsUtil.is_02(prodKind) || CrsUtil.is_04(prodKind) || CrsUtil.is_68(prodKind)) {
							L140S02A l140s02a = clsService.findL140S02A_by_C160S01C(c160s01c);
							if (l140s02a != null) {
								if (UtilConstants.DEFAULT.是.equals(l140s02a
										.getChkUsed())) {
									continue;
								}
							}

						}

						c160s01cLoanCurr = c160s01c.getLoanCurr();
						c160s01cLoanAmt = c160s01cLoanAmt.add(c160s01c
								.getLoanAmt());

						c160s01cApprovedAmt = c160s01cApprovedAmt.add(c160s01c
								.getApprovedAmt());

					}
				}
				// 去除重複會計科目
				HashSet<String> hashSet = new HashSet<String>(SubjCodeList);
				SubjCodeList.clear();
				SubjCodeList.addAll(hashSet);
				for (String SubjCode : SubjCodeList) {
					// c160s01csubjCode = c160s01csubjCode
					// + SubjCodeMap.get(Util.getLeftStr(SubjCode, 8))
					// + "("
					// + prodKindMap.get(Util.getRightStr(SubjCode, 2))
					// + ")" + "、";

					c160s01csubjCode = c160s01csubjCode
							+ prodKindMap.get(Util.getRightStr(SubjCode, 2))
							+ "("
							+ SubjCodeMap.get(Util.getLeftStr(SubjCode, 8))
							+ ")" + "、";

				}
				if (!Util.equals(c160s01csubjCode, "")) {
					newc160s01csubjCode = c160s01csubjCode.substring(0,
							c160s01csubjCode.lastIndexOf("、"));

					hashSet = new HashSet<String>(CustNameList);
					CustNameList.clear();
					CustNameList.addAll(hashSet);
					if (CustNameList.size() == 1) {
						c160m01bcustname = CustNameList.get(0);
					} else {
						c160m01bcustname = CustNameList.get(0)
								+ prop.getProperty("C160M01B.sum")
								+ CustNameList.size()
								+ prop.getProperty("C160M01B.people");
					}
				}

				c160s01cAmtDesc = _calLoanAmt(c160m01a, c160m01blist, c160s01csingle,
						prop);
			} else {
				int poplecount = 0;
				List<C160S01D> c160s01dlist = c160s01ddao.findByMainId(c160m01a
						.getMainId());
				C160M01F c160m01f = c160m01fdao.findByUniqueKey(c160m01a
						.getMainId());
				c160m01f = Util.isEmpty(c160m01f) ? new C160M01F() : c160m01f;
				for (C160S01D c160s01d : c160s01dlist) {
					c160s01cLoanAmt = c160s01cLoanAmt.add(c160s01d
							.getLoanTotAmt());
					poplecount++;
				}
				c160m01bcustname = c160m01f.getCustId() + " "
						+ c160m01f.getCustName()
						+ prop.getProperty("C160M01B.sum") + poplecount
						+ prop.getProperty("C160M01B.people");
				newc160s01csubjCode = see_addendum;
			}

			c160s01cCntrNo = see_addendum;
			c160s01cDeadline = see_addendum;
			c160s01crateDesc = see_addendum;
			c160s01cautoRct = see_addendum;
			c160s01caccNo = see_addendum;
			c160s01cAppOtherBranchNo = "";
			c160s01cAppOtherAccount = "";
			c160s01cefcBh = see_addendum;
			c160s01crctDate = see_addendum;
			c160s01crctAmt = see_addendum;
			c160s01cautopay = see_addendum;
			c160s01catpayNo = see_addendum;
			c160s01cAchBranchNo = "";
			c160s01cAchAccount = "";
			c160s01aCmsDesc = see_addendum;
			c160s01cLnselect = see_addendum;
			c160s01cLnselect = ""; //在 rpt 的「欄位隱藏公式」設定有資料，才顯示這一列。 此處就不填入{詳附表}，免得看到2次詳附表
			c160s01cuseline = see_addendum;
			c160s01eChgCase = see_addendum;
			c160s01fdata = see_addendum;
			c160s01cdRateAdd = see_addendum;
			PARAM_C160S01C_DelayedIntDRateArea = _get_DelayedIntDRateArea_html_tableFormat(c160m01a, prop_CLS1161S02A, null, see_addendum, false);
			c160s01cdcheck = see_addendum;
			c160m01bJoinMarkDesc = see_addendum;
			c160s01bcustdata = see_addendum;
			// J-103-0001各項費用變成多筆處理。
			// c160s01cagencyamt = C160S01Ctext1;
			l140m01r1staffjobl1 = see_addendum;
		}
		rptVariableMap.put("C160M01A.CUSTNAME", c160m01bcustname);
		rptVariableMap.put("C160S01C.SUBJCODE", newc160s01csubjCode);// 會計科目
		rptVariableMap.put("C160S01C.CNTRNO", c160s01cCntrNo);// 額度序號
		rptVariableMap.put("C160S01C.AMTDESC", c160s01cAmtDesc); // 21014-10-23額度組字

		rptVariableMap.put("C160S01C.LOANCURR", c160s01cLoanCurr);// 額度幣別
		rptVariableMap.put("C160S01C.APPROVEDAMT",
				NumConverter.addComma(c160s01cApprovedAmt));// 核准金額
		rptVariableMap.put("C160S01C.LOANAMT",
				NumConverter.addComma(c160s01cLoanAmt));// 動撥金額

		rptVariableMap.put("C160S01C.DEADLINE", c160s01cDeadline);// 期限
		rptVariableMap.put("C160S01C.LNSELECT", c160s01cLnselect);// 契約期間
		rptVariableMap.put("C160S01C.PMT_1ST_RT_DT", c160s01cPmt_1st_rt_dt);
		rptVariableMap.put("C160S01C.USELINE", c160s01cuseline);// 動用期間
		rptVariableMap.put("C160S01C.RATEDESC", c160s01crateDesc);// 利率
		rptVariableMap.put("C160S01C.AUTORCT", c160s01cautoRct);// 自動進帳
		rptVariableMap.put("C160S01C.ACCNO", c160s01caccNo);// 進帳帳號
		rptVariableMap.put("C160S01C.APPOTHERBRANCHNO", c160s01cAppOtherBranchNo);// 他行進帳行庫
		rptVariableMap.put("C160S01C.APPOTHERACCOUNT", c160s01cAppOtherAccount);// 他行進帳帳號
		rptVariableMap.put("C160S01C.EFCBH", c160s01cefcBh);// 行銷分行
		rptVariableMap.put("C160S01C.RCTDATE", c160s01crctDate);// 進帳日期
		rptVariableMap.put("C160S01C.RCTAMT", c160s01crctAmt);// 進帳金額
		rptVariableMap.put("C160S01C.AUTOPAY", c160s01cautopay);// 自動扣帳
		rptVariableMap.put("C160S01C.ACHBRANCHNO", c160s01cAchBranchNo);// 他行扣帳行庫
		rptVariableMap.put("C160S01C.ACHACCOUNT", c160s01cAchAccount);// 他行扣帳帳號
		rptVariableMap.put("C160S01C.ATPAYNO", c160s01catpayNo);// 扣帳日期
		rptVariableMap.put("C160S01A.CMSDESC", c160s01aCmsDesc);// 擔保品資訊
		rptVariableMap.put("C160S01E.CHGCASE", c160s01eChgCase);// 是否辦理代償
		rptVariableMap.put("C160S01E.CHGCASEPnt", c160s01fdata);// 代償資訊
		rptVariableMap.put("C160S01C.DRATEADD", c160s01cdRateAdd);// 計收延遲利息加碼
		rptVariableMap.put("C160S01C.CDCHECK", c160s01cdcheck);// 違約金計算條件
		rptVariableMap.put("C160S01C.DMONTH1", c160s01cdMonth1);// 違約金計算條件
		rptVariableMap.put("C160S01C.DRATE1", c160s01cdRate1);// 違約金計算條件
		rptVariableMap.put("C160S01C.DMONTH2", c160s01cdMonth2);// 違約金計算條件
		rptVariableMap.put("C160S01C.DRATE2", c160s01cdRate2);// 違約金計算條件
		rptVariableMap.put("C160S01C.DelayedIntDRateArea", PARAM_C160S01C_DelayedIntDRateArea);// 延遲利息/違約金欄位
		rptVariableMap.put("C160S01B.JOINMARKDESC", c160m01bJoinMarkDesc);// 共同行銷資訊
		rptVariableMap.put("C160S01B.JOINMARKETINGDATE", joinmarketingdate);// 共同行銷資訊
		rptVariableMap.put("C160S01B.CUSTDATA", c160s01bcustdata);// 借保人
		// J-103-0001各項費用變成多筆處理。
		// rptVariableMap.put("C160S01C.AGENCYAMT", c160s01cagencyamt);// 開辦費
		if ("".equals(l140m01r1staffjobl1)) {
			rptVariableMap.put("L140M01R.1STAFFJOBL1", "無。");
		} else {
			rptVariableMap.put("L140M01R.1STAFFJOBL1", l140m01r1staffjobl1);
		}
		
		if(true){
			rptVariableMap.put("checkStr01", build_checkStr01(c160m01blist));
			rptVariableMap.put("checkStr02", build_checkStr02(c160m01blist));
		}
		return rptVariableMap;
	}
	
	private String build_checkStr_by_tmp_map(Map<String, String> tmp_map){
		Map<String, List<String>> map = new TreeMap<String, List<String>>();
		for(String desc: tmp_map.keySet()){
			String date = tmp_map.get(desc);
			if(!map.containsKey(date)){
				map.put(date, new ArrayList<String>());
			}
			map.get(date).add(desc);
		}
		String r = "";
		r+="<table border='0' width='98%'>";
		for(String key: map.keySet()){
			r+="<tr>";
			r+="<td style='text-align:right;'>資料查詢日期"+key+"</td>";
			r+="</tr>";
			r+="<tr>";
			r+="<td>"+StringUtils.join(map.get(key), "<br/>")+"</td>";
			r+="</tr>";
		}
			
		r+="</table>";
		return r;
	}
	
	private String[] split_checkStr(String raws){
		String s = Util.trim(raws);
		String target = "<br/>";
		s = StringUtils.replace(s, "<br>", target);
		s = StringUtils.replace(s, "<BR>", target);
		s = StringUtils.replace(s, "<BR/>", target);
		return StringUtils.split(s, target);		
	}
	
	private String build_checkStr01(List<C160M01B> c160m01blist){
		Map<String, String> tmp_map = new LinkedHashMap<String, String>();
		//有[idDup, date, desc]
		for(C160M01B c160m01b : c160m01blist){
			String date = Util.trim(TWNDate.toAD(c160m01b.getBlackDataDate()));
			if(Util.isEmpty(date)){
				continue;
			}
			for(String raw_desc : split_checkStr(c160m01b.getBlackDesc())){
				String desc = Util.trim(raw_desc);
				if(!tmp_map.containsKey(desc)){
					tmp_map.put(desc, date);	
				}else{
					//若在 2016-01-15有一筆, 在2016-01-16也有一筆
					String memo_date = tmp_map.get(desc);
					if(date.compareTo(memo_date)>0){
						tmp_map.put(desc, date);	
					}
				}					
			}
		}
		return build_checkStr_by_tmp_map(tmp_map);	
	}
	
	private String build_checkStr02(List<C160M01B> c160m01blist){
		Map<String, String> tmp_map = new LinkedHashMap<String, String>();
		//有[idDup, date, desc]
		for(C160M01B c160m01b : c160m01blist){
			String date = Util.trim(TWNDate.toAD(c160m01b.getJoinMarketingDate()));
			if(Util.isEmpty(date)){
				continue;
			}
			for(String raw_desc : split_checkStr(c160m01b.getJoinMarkDesc())){
				String desc = Util.trim(raw_desc);
				if(!tmp_map.containsKey(desc)){
					tmp_map.put(desc, date);	
				}else{
					//若在 2016-01-15有一筆, 在2016-01-16也有一筆
					String memo_date = tmp_map.get(desc);
					if(date.compareTo(memo_date)>0){
						tmp_map.put(desc, date);	
					}
				}					
			}
		}
		return build_checkStr_by_tmp_map(tmp_map);		
	}
	
	private String _get_dRateAdd(C160S01C c160s01c){
		return Util.trim((c160s01c.getDRateAdd()==null
				||BigDecimal.ZERO.compareTo(c160s01c.getDRateAdd())==0)?"0":c160s01c.getDRateAdd()) + "%";
	}
	private String _get_dMonthAndRate(C160M01A c160m01a, C160S01C c160s01c){
		String str_penaltyMaxContTm = "";
		if(Util.equals("1", c160s01c.getCtrType()) || Util.equals("2", c160s01c.getCtrType())){
			str_penaltyMaxContTm = "每次違約狀態最高連續收取期數為"+Util.trim(c160s01c.getPenaltyMaxContTm())+"期。";
		}else if(Util.equals("3", c160s01c.getCtrType())){
			
		}
		
		//J-108-0195 依金管會規定配合修訂「本行計收、減收、免收遲延利息、違約金處理要點」
		if(Util.isNotEmpty(Util.trim(c160s01c.getPenaltyMaxContTm())) 
				&&(Util.equals("3", c160s01c.getCtrType()) || Util.equals("4", c160s01c.getCtrType()))){
			str_penaltyMaxContTm = "每次違約狀態最高連續收取期數為"+Util.trim(c160s01c.getPenaltyMaxContTm())+"期。";
		}
		
		String c160s01cdMonth1 = Util.nullToSpace(c160s01c.getDMonth1());
		String c160s01cdRate1 = Util.nullToSpace(c160s01c.getDRate1());
		String c160s01cdMonth2 = Util.nullToSpace(c160s01c.getDMonth2());
		String c160s01cdRate2 = Util.nullToSpace(c160s01c.getDRate2());
		
		return  
			"借戶如延遲還本或付息時，本金自到期日起，利息自繳息日起，"
			+"逾期在"+c160s01cdMonth1+"個月以內部份，按約定利率百分之"+c160s01cdRate1+"，"
			+"逾期超過"+c160s01cdMonth2+"個月部份，按約定利率百分之"+c160s01cdRate2+"計付違約金。"
			+str_penaltyMaxContTm;
	}
	private String _get_DelayedIntDRateArea_html_tableFormat(C160M01A c160m01a, Properties prop_CLS1161S02A, C160S01C c160s01c, String forceStr, boolean is_addendum){
		if(c160m01a==null){
			return "";
		}
		if(Util.equals(ClsUtil.C160M01A_RPTID_V20190920, c160m01a.getRptId())){
			List<String> list = new ArrayList<String>();
			
			list.add("<table border='0' >");
			if(true){
				list.add("<tr>");
				list.add("<td  style='width:"+(is_addendum?142:122)+"px; ' >"+"計收延遲利息加碼"+"："+"</td>"); //因應字體寬度不同
				list.add("<td>"+(c160s01c==null?forceStr:(LMSUtil.pretty_numStr(c160s01c.getDRateAdd())+"%"))+"</td>");
				list.add("</tr>");
			}
			if(true){
				list.add("<tr>");
				list.add("<td>"+prop_CLS1161S02A.getProperty("label.DelayedInterestCondTitle")+"："+"</td>");
				list.add("<td>"+(c160s01c==null?forceStr:prop_CLS1161S02A.getProperty("label.DelayedInterestCond"))+"</td>");
				list.add("</tr>");
			}
			if(true){
				list.add("<tr style='vertical-align:top;'>");
				list.add("<td style='vertical-align:top;' >"+prop_CLS1161S02A.getProperty("C160S01C.dTitle2")+"："+"</td>");//C160S01C.dTitle2=違約金計算條件
				String outStr = "";
				if(c160s01c!=null){
					outStr = prop_CLS1161S02A.getProperty("label.dMonthdRate.part1")
						+prop_CLS1161S02A.getProperty("label.dMonthdRate.part2A")+" "+c160s01c.getDMonth1()+" "
						+prop_CLS1161S02A.getProperty("label.dMonthdRate.part2B")+" "+c160s01c.getDRate1()
						+prop_CLS1161S02A.getProperty("label.dMonthdRate.part2C")+" "+c160s01c.getDMonth2()+" "
						+prop_CLS1161S02A.getProperty("label.dMonthdRate.part2D")+" "+c160s01c.getDRate2()
						+prop_CLS1161S02A.getProperty("label.dMonthdRate.part2E")
						//=====
						+prop_CLS1161S02A.getProperty("label.PenaltyMaxContTm.part1")+" "+c160s01c.getPenaltyMaxContTm()+" "
						+prop_CLS1161S02A.getProperty("label.PenaltyMaxContTm.part2");
				}
				list.add("<td>"+(c160s01c==null?forceStr:outStr)+"</td>");
				list.add("</tr>");
			}
			list.add("</table>");
			return StringUtils.join(list, "");
		}
		return "";
	}
	/**
	 * 設定C160S01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setC160S01CDataList(C160M01A c160m01a,
			List<Map<String, String>> titleRows, List<C160M01B> c160m01bList,
			Properties prop, Properties prop_CLS1161S02A) {
		// F代表第次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		Map<String, String> prodKindMap = codeTypeService
				.findByCodeType("lms2415m01_lnType");
		Map<String, String> SubjCodeMap = codeTypeService
				.findByCodeType("lms1705m01_SubItme");
		Map<String, String> YesNoMap = codeTypeService
				.findByCodeType("Common_YesNo");

		for (C160M01B c160m01b : c160m01bList) {
			String tCaseNo = Util.toSemiCharString(c160m01b.getCaseNo());

			List<C160S01C> list = c160s01cdao
					.findByMainIdRefMainidOrderByUiSeq(c160m01b.getMainId(),
							c160m01b.getRefmainId());
			boolean listbool = list.size() > 1;
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(list.toArray(new C160S01C[list
							.size()]));
			for (C160S01C c160s01c : list) {
				//TODO 搜尋 Util.setColumnMap()
				//     以此，來區分 i-net 的 隱藏區段 
				
				//********************************
				//開始
				mapInTitleRows = Util.setColumnMap();
				String single = "";
				if (listbool && seq_printStrMap.containsKey(c160s01c.getSeq())) {
					single = seq_printStrMap.get(c160s01c.getSeq());
				}
				mapInTitleRows.put("ReportBean.column05", single);
				mapInTitleRows.put("ReportBean.column08", "F");// column08為"F"時印標題及欄位名稱
				titleRows.add(mapInTitleRows);
				//********************************
								
				int seqNo = c160s01c.getSeq();
				String mainId = Util.nullToSpace(c160s01c.getMainId());
				String refmainId = Util.nullToSpace(c160s01c.getRefmainId());
				String lndate = new String();
				String usedate = new String();
				String pmt_1st_rt_dt = "";
				List<C160S01A> c160s01alist = c160s01adao
						.findByMainIdRefMainId(mainId, refmainId);
				String c160s01adesc = new String();
				for (C160S01A c160s01a : c160s01alist) {
					if (!Util.isEmpty(c160s01a.getOid())) {
						c160s01adesc = c160s01adesc
								+ getCmsDesc(c160s01a, prop, prop_CLS1161S02A) + "<br/>";
					}
					// } else if (!Util.isEmpty(c160s01a.getCmsDesc())) {
					// c160s01adesc = c160s01adesc
					// + Util.trim(c160s01a.getCmsDesc()) + "<br/>";
					// }
				}
				C160S01E c160s01e = c160s01edao.findByMainIdRefMainIdSeq(
						mainId, refmainId, seqNo);
				if (Util.isEmpty(c160s01e)) {
					c160s01e = new C160S01E();
				}
				L140S02E l140s02e = l140S02edao.findByUniqueKey(refmainId,
						c160s01c.getSeq());
				l140s02e = Util.isEmpty(l140s02e) ? new L140S02E() : l140s02e;
				lndate = Util.nullToSpace(Util.getDate(c160s01c
						.getLnStartDate()))
						+ " ~ "
						+ Util.nullToSpace(Util.getDate(c160s01c.getLnEndDate()));
				usedate = Util.nullToSpace(Util.getDate(c160s01c
						.getUseStartDate()))
						+ " ~ "
						+ Util.nullToSpace(Util.getDate(c160s01c
								.getUseEndDate()));
				
				pmt_1st_rt_dt = Util.trim(TWNDate.toAD(c160s01c.getPmt_1st_rt_dt()));
				
				String SubjCode = Util.nullToSpace(prodKindMap.get(c160s01c
						.getProdKind()))
						+ "("
						+ Util.nullToSpace(SubjCodeMap.get(c160s01c
								.getSubjCode())) + ")";
				String c160s01cDeadline = Util.trim(c160s01c.getYear())
						+ prop.getProperty("C160S01C.Year")
						+ Util.trim(c160s01c.getMonth())
						+ prop.getProperty("C160S01C.Month");
				if (!Util.isEmpty(c160s01c.getNowFrom())
						&& !Util.isEmpty(c160s01c.getNowEnd())) {
					c160s01cDeadline = c160s01cDeadline + "("
							+ prop.getProperty("L140S02E.txt1")
							+ Util.trim(c160s01c.getNowFrom())
							+ prop.getProperty("L140S02E.Unit") + "~"
							+ Util.trim(c160s01c.getNowEnd())
							+ prop.getProperty("L140S02E.Unit") + ")";
				}
				
				
				//********************************
				//開始
				mapInTitleRows = Util.setColumnMap();
				mapInTitleRows.put("ReportBean.column08", "N");// column08為"N"時印欄位內容
				mapInTitleRows.put("ReportBean.column09",
						Util.nullToSpace(c160s01c.getCntrNo()));// 額度序號
				mapInTitleRows.put(
						"ReportBean.column10",
						Util.nullToSpace(c160m01b.getCustId() + " "
								+ c160m01b.getCustName()));// 姓名
				
				mapInTitleRows.put("ReportBean.column11", NumConverter.addComma(Util.nullToSpace(c160s01c.getLoanAmt())));// 動撥額度
				mapInTitleRows.put("ReportBean.column29", NumConverter.addComma(Util.nullToSpace(c160s01c.getApprovedAmt())));// 核准額度
				mapInTitleRows.put("ReportBean.column30",(Util.nullToSpace(c160s01c.getLoanCurr())));// 額度幣別

				mapInTitleRows.put("ReportBean.column22", SubjCode);// 產品種類(科目)
				mapInTitleRows.put("ReportBean.column12",
						YesNoMap.get(c160s01e.getChgOther()));// 是否辦理代償
				mapInTitleRows.put("ReportBean.column45",
						Util.nullToSpace(c160s01e.getChgOther()));// 是否辦理代償Check
				mapInTitleRows.put("ReportBean.column13",
						YesNoMap.get(Util.nullToSpace(c160s01c.getAutoRct())));// 是否自動進帳
				mapInTitleRows.put("ReportBean.column14",
						Util.getDate(c160s01c.getRctDate()));// 進帳日期
				mapInTitleRows.put("ReportBean.column18",
						Util.nullToSpace(c160m01b.getJoinMarkDesc()));// 共同行銷資訊
				mapInTitleRows.put("ReportBean.column19",
						Util.nullToSpace(c160s01c.getAccNo()));// 進帳帳號
				mapInTitleRows.put("ReportBean.column52",
						Util.nullToSpace(c160s01c.getAppOtherBranchNo()));// 他行進帳行庫
				mapInTitleRows.put("ReportBean.column53",
						Util.nullToSpace(c160s01c.getAppOtherAccount()));// 他行進帳帳號
				
				mapInTitleRows.put(
						"ReportBean.column20",
						Util.trim(c160s01c.getEfctBH())
								+ " "
								+ Util.trim(branch.getBranchName(c160s01c
										.getEfctBH())));// 行銷分行
				String RctAMT = NumConverter.addComma(c160s01c.getRctAMT());
				RctAMT = Util.isEmpty(RctAMT) ? "     " : RctAMT;
				mapInTitleRows.put("ReportBean.column21", RctAMT);// 進帳金額
				if(true){
					String descRctAmt = "";
					if(CrsUtil.is_67(c160s01c.getProdKind())){
						descRctAmt =  prop.getProperty("rpt.columnDesc.rctAmt");
					}else if(CrsUtil.is_70(c160s01c.getProdKind())){
						descRctAmt =  prop.getProperty("rpt.columnDesc.rctAmt.prodKind70");
					}else{
						descRctAmt =  prop.getProperty("rpt.columnDesc.rctAmt");
					}
					mapInTitleRows.put("ReportBean.column50", descRctAmt);// 進帳金額 or 撥款額度	
				}
				
				mapInTitleRows.put("ReportBean.column15",
						YesNoMap.get(Util.nullToSpace(c160s01c.getAutoPay())));// 自動扣帳
				mapInTitleRows.put("ReportBean.column16",
						Util.nullToSpace(c160s01c.getAtpayNo()));// 扣帳帳號
				
				mapInTitleRows.put("ReportBean.column54",
						Util.nullToSpace(c160s01c.getAchBranchNo()));// 他行扣帳行庫
				mapInTitleRows.put("ReportBean.column55",
						Util.nullToSpace(c160s01c.getAchAccount()));// 他行扣帳帳號
				
				mapInTitleRows.put("ReportBean.column23", c160s01cDeadline);// 期限

				mapInTitleRows.put("ReportBean.column25", lndate);// 契約期間
				mapInTitleRows.put("ReportBean.column26", usedate);// 動用期間
				
				mapInTitleRows.put("ReportBean.column51", pmt_1st_rt_dt);// 首次還款日
				mapInTitleRows.put(
						"ReportBean.column27",
						Util.toSemiCharString(
								Util.nullToSpace(c160s01c.getRateDesc()))
								.replaceAll("∼", "~"));// 利率
				mapInTitleRows.put("ReportBean.column28",
						Util.nullToSpace(c160s01adesc));// 擔保品內容
				// 代償資訊
				String c160s01fdata = "";
				if (Util.equals(c160s01e.getChgOther(), "Y")) {
					c160s01fdata = getc160s01fData(c160s01c);
				} else {
					c160s01fdata = prop.getProperty("C160S01F.txt7"
							+ Util.trim(c160s01e.getChgOther()));
				}
				mapInTitleRows.put("ReportBean.column46", c160s01fdata);// 違約金計算條件-利率百分比
				mapInTitleRows.put("ReportBean.column40", _get_dRateAdd(c160s01c));// 代償同業房貸原因計收延遲利息加碼
				mapInTitleRows.put("ReportBean.column41",
						Util.nullToSpace(c160s01c.getDMonth1()));// 違約金計算條件-逾期
				mapInTitleRows.put("ReportBean.column42",
						Util.nullToSpace(c160s01c.getDRate1()));// 違約金計算條件-利率百分比
				mapInTitleRows.put("ReportBean.column43",
						Util.nullToSpace(c160s01c.getDMonth2()));// 違約金計算條件-逾期超過
				mapInTitleRows.put("ReportBean.column44",
						Util.nullToSpace(c160s01c.getDRate2()));// 違約金計算條件-利率百分比
				if(true){
					String column47 = "";
					if(Util.isNotEmpty(Util.trim(c160s01c.getCtrType()))){
						column47 = _get_dMonthAndRate(c160m01a, c160s01c);						
					}else{
						//舊的動審表
					}
					mapInTitleRows.put("ReportBean.column47", column47);
					//==========================
					String column48 = _get_DelayedIntDRateArea_html_tableFormat(c160m01a, prop_CLS1161S02A, c160s01c, null, true);
					mapInTitleRows.put("ReportBean.column48", column48);
				}
				mapInTitleRows.put("ReportBean.column17",
						Util.getDate(c160m01b.getJoinMarketingDate()));
				mapInTitleRows.put("ReportBean.column06",
						getcustData(c160m01b, prop));
				String AgencyAMT = Util.trim(c160s01c.getAgencyAMT());
				AgencyAMT = Util.isEmpty(AgencyAMT) ? "     " : NumConverter
						.addComma(AgencyAMT);
				mapInTitleRows.put("ReportBean.column24", AgencyAMT);

				String tString = setL140M01RListData(mainId, tCaseNo);
				mapInTitleRows.put("ReportBean.column31", tCaseNo);
				mapInTitleRows.put("ReportBean.column32", tString);

				titleRows.add(mapInTitleRows);
			}
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put("ReportBean.column08", "Y");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 設定C160M01C資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L160M01C List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setC160M01CDataList(
			List<Map<String, String>> titleRows, List<C160M01C> list) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		String rpt_param_column07 = "ReportBean.column07";
		Map<String, String> mapInTitleRows = null;
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put(rpt_param_column07, "F");
		titleRows.add(mapInTitleRows);
		int count = 1;
		int icount = 1;
		boolean result = true;
		for (C160M01C c160m01c : list) {
			if (!Util.isEmpty(Util.trim(c160m01c.getItemContent()))) {
				if (count % 2 == 1) {
					result = false;
					mapInTitleRows = Util.setColumnMap();
					mapInTitleRows.put(rpt_param_column07, "Y");
					mapInTitleRows.put("ReportBean.column01",
							this.getItemCheck(c160m01c.getItemCheck()));
					mapInTitleRows.put("ReportBean.column02", icount + "、"
							+ c160m01c.getItemDscr());
				} else if (count % 2 == 0) {
					result = true;
					mapInTitleRows.put("ReportBean.column03",
							this.getItemCheck(c160m01c.getItemCheck()));
					mapInTitleRows.put("ReportBean.column04", icount + "、"
							+ c160m01c.getItemDscr());
					titleRows.add(mapInTitleRows);
				}
				count++;
				icount++;
			}
		}
		if (!result) {
			titleRows.add(mapInTitleRows);
		}
		if (titleRows.size() == 1) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put(rpt_param_column07, "Y");
			titleRows.add(mapInTitleRows);
		}
		mapInTitleRows = Util.setColumnMap();
		mapInTitleRows.put(rpt_param_column07, "N");
		titleRows.add(mapInTitleRows);
		return titleRows;
	}

	/**
	 * 顯示需要呈現ITEMCHECK的資料
	 * 
	 * @param itemCheck
	 *            itemCheck
	 * @return 呈現到報表文字
	 */
	private String getItemCheck(String itemCheck) {
		String str = null;
		if ("0".equals(itemCheck)) {
			str = "免";
		} else if ("1".equals(itemCheck)) {
			str = "V";
		} else if ("2".equals(itemCheck)) {
			str = "X";
		} else {
			str = "免";
		}
		return str;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

	private String getCmsDesc(C160S01A c160s01a, Properties prop, Properties prop_CLS1161S02A) {
		if(true){
			//前端 UI 上的 "備註說明"
			//	設定抵押權金額若為0，則動用審核表引進時視為無擔保品。
			//
			//判斷當 [擬/已/應]設定第[一/二]順位抵押權新台幣[0]仟元予本行 ===> 第N順位抵押權金額 0，則不印出
			
			//例：額度 008109900143，會設定抵押權，但可不投保
			
			//消金月報表，會抓 c160s01a.loanTwd
			//動審表編輯時, 押值抓 c160s01a.totLnAmt
			if((c160s01a.getSetAmt()!=null && BigDecimal.ZERO.compareTo(c160s01a.getSetAmt())==0)){
				return "";
			}
		} 
		
		String br = "<br/>";
		if (Util.equals(c160s01a.getCollTyp1(), "01")) {// 不動產
			Map<String, String> L140M01O_ins1Map = codeTypeService
					.findByCodeType("L140M01O_ins1");
			String c160s01aCmsDesc = new String();

			String build = Util.trim(c160s01a.getBuild());
			build = "".equals(build) ? "[]" : build;
			JSONArray jscd1Array = JSONArray.fromObject(build);
			String areaDetail = Util.trim(c160s01a.getAreaDetail());
			areaDetail = "".equals(areaDetail) ? "[]" : areaDetail;
			JSONArray jscd2Array = JSONArray.fromObject(areaDetail);

			Map<String, String> BankCode06Map = codeTypeService
					.findByCodeType("BankCode06");
			//==========================================================
			//注意：
			//L140M01O.build 的長度是 VARCHAR(4096) , L140M01O.areaDetail 的長度是 VARCHAR(2048)
			//而 擔保品系統，是用 mainId 去串 CMS.C101M03 , CMS.C101M04
			//若某一份擔保品估價報告書的地號、建號的筆數太多 => L140M01O 只存得進去部分
			if (jscd1Array.size() > 0) {// 建物地址
				c160s01aCmsDesc = c160s01aCmsDesc
						+ prop.getProperty("C160S01A.cmsdesc1");

				JSONObject cmsdesc1 = (JSONObject) jscd1Array.getJSONObject(0);
				c160s01aCmsDesc = c160s01aCmsDesc
						+ cmsdesc1.getString("target")
							//C160S01A.cmsdesc11=，建號：
						+ prop.getProperty("C160S01A.cmsdesc11")
						+ cmsdesc1.getString("bldno")
						+ prop.getProperty("C160M01B.sum") + (c160s01a.getBldNum()!=null?LMSUtil.pretty_numStr(c160s01a.getBldNum()):String.valueOf(jscd1Array.size()))
						+ prop.getProperty("C160S01A.unit") + br;

			} else if (jscd2Array.size() > 0) {
				c160s01aCmsDesc = c160s01aCmsDesc
						+ prop.getProperty("C160S01A.cmsdesc2") + br;
				JSONObject cmsdesc2 = (JSONObject) jscd2Array.getJSONObject(0);
				c160s01aCmsDesc = c160s01aCmsDesc
						+ cmsdesc2.getString("target")
							//C160S01A.cmsdesc31=，地號：
						+ prop.getProperty("C160S01A.cmsdesc31")
						+ cmsdesc2.getString("landNo")
						+ prop.getProperty("C160S01A.cmsdesc21")
						+ cmsdesc2.getString("ttlNum")
						+ prop.getProperty("C160S01A.cmsdesc22")
						+ cmsdesc2.getString("ttlBal")
						+ prop.getProperty("C160M01B.sum") + (c160s01a.getLndNum()!=null?LMSUtil.pretty_numStr(c160s01a.getLndNum()):jscd2Array.size())
						+ prop.getProperty("C160S01A.unit") + br;
			}
			String InsId = Util.trim(BankCode06Map.get(Util.trim(c160s01a
					.getInsId())));
			InsId = Util.isEmpty(InsId) ? prop
					.getProperty("C160S01A.cmsdesc36") : InsId;
			String Set1 = L140M01O_ins1Map.get(Util.trim(c160s01a.getSet1()));
			String Ins1 = L140M01O_ins1Map.get(Util.trim(c160s01a.getIns1()));
			String SetordStr = Util.trim(c160s01a.getSetordStr());
			Set1 = Util.isEmpty(Set1) ? "&nbsp;&nbsp;&nbsp;&nbsp;" : Set1;
			Ins1 = Util.isEmpty(Ins1) ? "&nbsp;&nbsp;&nbsp;&nbsp;" : Ins1;
			SetordStr = Util.isEmpty(SetordStr) ? "&nbsp;&nbsp;&nbsp;&nbsp;"
					: SetordStr;

			String unitStr = prop.getProperty("C160S01A.cmsdesc45"); // 萬
			String totLoanAmtStr = NumConverter.addComma(Util.trim(c160s01a
					.getTotalLoanAmt()));
			if ("Y".equals(c160s01a.getUnitChg())) {
				unitStr = prop.getProperty("C160S01A.cmsdesc46"); // 仟
				totLoanAmtStr = NumConverter.addComma(Util.trim(c160s01a
						.getTotLnAmt())) + unitStr;
			}

			String tPriamtStr = ("".equals(Util.trim(c160s01a.getPriAmt())) || "0"
					.equals(Util.trim(c160s01a.getPriAmt()))) ? prop
					.getProperty("C160S01A.cmsdesc48")
					+ prop.getProperty("C160S01A.cmsdesc47") : prop
					.getProperty("C160S01A.cmsdesc42")
					+ NumConverter.addComma(Util.trim(c160s01a.getPriAmt()))
					+ unitStr + prop.getProperty("C160S01A.cmsdesc49");

			c160s01aCmsDesc = c160s01aCmsDesc
						//C160S01A.cmsdesc4=(1)土地及建物
					+ prop.getProperty("C160S01A.cmsdesc4") + Set1

							//C160S01A.cmsdesc411=設定第
					+ prop.getProperty("C160S01A.cmsdesc411") + SetordStr

							//C160S01A.cmsdesc41=順位抵押權TWD
					+ prop.getProperty("C160S01A.cmsdesc41")
					+ NumConverter.addComma(Util.trim(c160s01a.getSetAmt()))
					+ unitStr + tPriamtStr
					
							//C160S01A.cmsdesc43=(押值:
					+ prop.getProperty("C160S01A.cmsdesc43") + totLoanAmtStr
					+ prop.getProperty("C160S01A.cmsdesc44") + br
					
						//C160S01A.cmsdesc5=(2)建物
					+ prop.getProperty("C160S01A.cmsdesc5") + Ins1
					
							//C160S01A.cmsdesc511=投保以本行為抵押權人之火險金額TWD
					+ prop.getProperty("C160S01A.cmsdesc511")
					+ NumConverter.addComma(Util.trim(c160s01a.getInsAmt1()))
							//C160S01A.cmsdesc51=元整，地震險金額TWD
					+ unitStr + prop.getProperty("C160S01A.cmsdesc51")
					+ NumConverter.addComma(Util.trim(c160s01a.getInsAmt2()))
							//C160S01A.cmsdesc52=元整。
					+ unitStr + prop.getProperty("C160S01A.cmsdesc52") + br
							//C160S01A.cmsdesc521=(由					C160S01A.cmsdesc53=承保)
					+ prop.getProperty("C160S01A.cmsdesc521") + InsId
					+ prop.getProperty("C160S01A.cmsdesc53");
			if(true){
				List<String> ctrTxInfo_list = new ArrayList<String>();
				if(true){
					if(CrsUtil.isNOT_null_and_NOTZeroDate(c160s01a.getCtrTxDate())){
						ctrTxInfo_list.add(prop_CLS1161S02A.getProperty("C160S01A.ctrTxDate")+"："+TWNDate.toAD(c160s01a.getCtrTxDate())+"。"); 
					} 
					if(c160s01a.getCtrTxAmt()!=null){
						ctrTxInfo_list.add(prop_CLS1161S02A.getProperty("C160S01A.ctrTxAmt")+"："+formatBigDecimal_ref_cls1151ServiceImpl(c160s01a.getCtrTxAmt())+unitStr+"元整。");
					}						
				}		
				if(ctrTxInfo_list.size()>0){
					c160s01aCmsDesc = (c160s01aCmsDesc + br + StringUtils.join(ctrTxInfo_list, ""));
				}
			}
			return c160s01aCmsDesc.replaceFirst("1、", "") + br;
		} else {
			if (Util.isNotEmpty(c160s01a.getCmsDesc())) {
				return c160s01a.getCmsDesc() + br;
			} else {
				return c160s01a.getCollNo() + " " + c160s01a.getCollName() + br;
			}
		}
	}

	public String getc160s01fData(C160S01C c160s01c) {
		Map<String, String> subReasonMap = codeTypeService
				.findByCodeType("L140S02H_subReason");
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMSCommomPage.class);
		// 2013/07/16,Rex,明澤說是否為本行貸款不列印
		// String l140s02gtitles[] = { "chgOther", "chgBorrower", "chgALoan",
		// "onlentDate", "chargeFlag", "chargeAmt" };
		String l140s02gtitles[] = { "chgOther", "chgBorrower", "onlentDate",
				"chargeFlag", "chargeAmt" };
		List<C160S01F> c160s01flist = c160s01fdao.findByMainIdSeqRefMainid(
				c160s01c.getMainId(), c160s01c.getSeq(),
				c160s01c.getRefmainId());
		C160S01E c160s01e = c160s01edao.findByMainIdRefMainIdSeq(
				c160s01c.getMainId(), c160s01c.getSeq(),
				c160s01c.getRefmainId());
		String l140s02ghtml = new String();
		if (c160s01e != null && Util.equals(c160s01e.getChgOther(), "Y")) {
			for (String l140s02gtitle : l140s02gtitles) {
				String l140s02gdata = new String();
				try {
					l140s02gdata = Util.trim(c160s01e.get(l140s02gtitle));
				} catch (CapException e) {
					e.printStackTrace();
				}

				if (!Util.equals(l140s02gdata, "")) {
					if (Util.equals(l140s02gtitle, "onlentDate")) {
						l140s02gdata = Util.getDate(c160s01e.getOnlentDate());
					} else if (Util.equals(l140s02gtitle, "chargeAmt")) {
						l140s02gdata = cls1151r03getCurr(null, l140s02gdata,
								pop);
					} else {
						l140s02gdata = showYN4Pic(l140s02gdata, pop);
					}
					l140s02ghtml = l140s02ghtml
							+ pop.getProperty("L140S02G." + l140s02gtitle)
							+ "：" + l140s02gdata.toString() + "<br/>";
				}
			}
			l140s02ghtml = l140s02ghtml
					+ (Util.equals(c160s01e.getChargeFlag(), "Y") ? pop
							.getProperty("L140S02G.subtext7N") + "<br/>" : "")
					+ pop.getProperty("L140S02H.title") + "<br/>";
			int count = 0;
			for (C160S01F c160s01f : c160s01flist) {
				count++;
				String c160s01fdata = new String();
				String subReason = Util.trim(c160s01f.getSubReason());
				if (Util.equals(subReason, "4")) {
					subReason = subReasonMap.get(subReason) + "-"
							+ c160s01f.getSubReaOth();
				} else {
					subReason = subReasonMap.get(subReason);
				}
				String OLNAppDate = Util.getDate(c160s01f.getOLNAppDate());
				String OLNEndDate = Util.getDate(c160s01f.getOLNEndDate());
				String OLNRemYear = Util.trim(c160s01f.getOLNRemYear());
				String TaxNo = Util.trim(c160s01f.getTaxNo());
				String mark1 = "，";
				OLNAppDate = Util.isEmpty(OLNAppDate) ? "" : mark1
						+ pop.getProperty("L140S02G.OLNAppDate") + OLNAppDate;
				OLNEndDate = Util.isEmpty(OLNEndDate) ? "" : mark1
						+ pop.getProperty("L140S02G.OLNEndDate") + OLNEndDate;
				OLNRemYear = Util.isEmpty(OLNRemYear) ? "" : mark1
						+ pop.getProperty("L140S02G.OLNRemYear") + OLNRemYear;
				TaxNo = Util.isEmpty(TaxNo) ? "" : mark1
						+ pop.getProperty("L140S02G.TaxNo") + TaxNo;
				c160s01fdata = c160s01fdata
						+ count
						+ pop.getProperty("L140S02G.subtext1")
						+ Util.trim(c160s01f.getBranchName())
						+ pop.getProperty("L140S02G.subtext2")
						+ NumConverter
								.addComma(Util.trim(c160s01f.getSubAmt()))
						+ pop.getProperty("L140S02G.subtext3") + subReason
						+ OLNAppDate + OLNEndDate + OLNRemYear + TaxNo;
				l140s02ghtml = l140s02ghtml + "  " + c160s01fdata;

			}
		} else {
			l140s02ghtml = l140s02ghtml
					+ pop.getProperty("L140S02G." + l140s02gtitles[0]) + "："
					+ showYN4Pic("N", pop) + "<br/>";
		}

		return l140s02ghtml;
	}

	/**
	 * 取得欄位文字□是□否
	 * 
	 * @param type
	 *            Y=是 N=否 O=不適用
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String showYN4Pic(String type, Properties prop) {
		if (Util.equals(type, "Y")) {
			type = prop.getProperty("COMMON1.YES");
		} else if (Util.equals(type, "N")) {
			type = prop.getProperty("COMMON1.NO");
		} else if (Util.equals(type, "O")) {
			type = prop.getProperty("COMMON1.NOUSE");
		}
		return type;
	}

	/**
	 * 取得 幣別 #,###元 格式
	 * 
	 * @param Curr
	 *            幣別(預設:TWD)
	 * @param Amt
	 *            金額
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String cls1151r03getCurr(String Curr, String Amt, Properties prop) {
		String rCurr = prop.getProperty("CLS1151R03.Curr");
		String rCurrUutil = prop.getProperty("CLS1151R03.CurrUtil");
		if (!Util.isEmpty(Curr)) {
			rCurr = Curr;
		}
		return rCurr + " " + NumConverter.addComma(Amt) + rCurrUutil;
	}

	/**
	 * 格式化bigDecimal 數值 若為null ，回傳五個空白
	 * 
	 * @return
	 */
	private String formatBigDecimal_ref_cls1151ServiceImpl(BigDecimal val) {
		String result = "　　　　　";
		if (val != null) {
			result = NumConverter.addComma(val);
		}
		return result;
	}
	
	/**
	 * 取得 借保人組字
	 * 
	 * @param c160m01b
	 * 
	 * @return string
	 */
	private String getcustData(C160M01B c160m01b, Properties prop) {
		Map<String, String> custPosDescMap = codeTypeService
				.findByCodeType("lms1605s03_rType");
		StringBuffer guarantorStr = new StringBuffer();
		List<C160S01B> c160s01blist = c160s01bdao.findByMainIdRefMainId(
				c160m01b.getMainId(), c160m01b.getRefmainId());
		if (c160s01blist.size() > 0) {
			// 同一個 custId 會有多種身份
			Map<String, String> custNameMap = new HashMap<String, String>();
			for (C160S01B c160s01b : c160s01blist) {
				String key = LMSUtil.getCustKey_len10custId(c160s01b.getRId(),
						c160s01b.getRDupNo());
				if (!custNameMap.containsKey(key)) {
					custNameMap.put(key, c160s01b.getRName());
				}
			}

			Map<String, Set<String>> custPosMap = new HashMap<String, Set<String>>();
			Properties prop_CLS1151S01Page = MessageBundleScriptCreator.getComponentResource(CLS1151S01Page.class);
			for (C160S01B c160s01b : c160s01blist) {
				String key = LMSUtil.getCustKey_len10custId(c160s01b.getRId(),
						c160s01b.getRDupNo());
				if (!custPosMap.containsKey(key)) {
					custPosMap.put(key, new HashSet<String>());
				}
				
				String item = Util.trim(custPosDescMap.get(c160s01b.getRType()));
				if(c160s01b.getGuaPercent()!=null 
						&& c160s01b.getGuaPercent().compareTo(BigDecimal.ZERO)!=0
						&& c160s01b.getGuaPercent().compareTo(new BigDecimal("100"))!=0){
					item+=(MessageFormat.format(prop_CLS1151S01Page.getProperty("L140S01A.guaPercentMemo")
							, cls1151Service.fmt_l140s01a_guaPercent(c160s01b.getGuaPercent())) );
				}
				custPosMap.get(key).add(item);
			}
			if (custNameMap.size() > 0) {
				List<String> _str = new ArrayList<String>();
				for (String key : custNameMap.keySet()) {
					_str.add(Util.trim(custNameMap.get(key))
							+ "("
							+ Util.trim(StringUtils.join(custPosMap.get(key),
									"、")) + ")");
				}
				guarantorStr.append(StringUtils.join(_str, "、"));
			}
		} else {
			guarantorStr.append(prop.getProperty("L140M01A.NOGUARANTOR"));
		}
		return guarantorStr.toString();
	}

	// 組出簽報書中各項費用的欄位值。
	private String setL140M01RListData(String mainId, String caseNo) {

		List<Map<String, Object>> maps = eloanDbService
				.findL140M01RByL120M01AcaseNo(mainId, caseNo);
		// feeNo,feeswft,count(*) as tCount,SUM(FEEAMT) AS tSum
		String feeNo = "";
		String feeNoName = "";
		String feeswft = "";
		int tCount = 0;
		int ttCount = 1;
		BigDecimal tSum = BigDecimal.ZERO;
		Map<String, String> feeNoMap = null;

		Properties prop = MessageBundleScriptCreator
				.getComponentResource(CLS1141R01RptServiceImpl.class);

		StringBuffer collMemo = new StringBuffer();

		for (Map<String, Object> map : maps) {
			if (map != null) {

				if (ttCount > 1) {
					collMemo.append("；");
				}

				ttCount = ttCount + 1;
				feeNo = Util.trim(map.get("feeNo"));
				feeswft = Util.trim(map.get("feeswft"));
				tCount = (Integer) map.get("tCount");
				tSum = (BigDecimal) map.get("tSum");

				feeNoMap = codeTypeService.findByCodeType("cls1141_feeNo");

				feeNoName = feeNoMap.get(feeNo);

				// L140M01R.A01={0}共{1}筆合計為{2} {3}元
				collMemo.append(MessageFormat.format(
						prop.getProperty("L140M01R.A01"), feeNoName, tCount,
						feeswft, tSum));

			}
		}
		if (!"".equals(collMemo.toString())) {
			collMemo.append("。");
		} else {
			collMemo.append("無。");
		}

		return collMemo.toString();

	}
}
