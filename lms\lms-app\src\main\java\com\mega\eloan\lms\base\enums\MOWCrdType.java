/* 
 * MOWCrdType.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.enums;

/**
 * <pre>
 * 內部風險信用 評等種類
 * </pre>
 * 
 * @since 2011/9/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/20,CP,new
 *          </ul>
 */
public enum MOWCrdType {

	/**
	 * 大型企業
	 */
	MOW100("M1"),
	/**
	 * 中型企業
	 */
	MOW200("M2"),
	/**
	 * 中小型企業
	 */
	MOW300("M3"),
	/**
	 * 不動產有建案規劃
	 */
	MOW400("M4"),
	/**
	 * 不動產無建案規劃一案建商
	 */
	MOW410("MA"),
	/**
	 * 不動產無建案規劃非一案建商(擔保或土融)
	 */
	MOW420("MB"),
	/**
	 * 不動產無建案規劃非一案建商(無擔)
	 */
	MOW430("MC"),
	/**
	 * 專案融資
	 */
	MOW500("M5"),
	/**
	 * 本國證券公司
	 */
	MOW600("M6"),
	/**
	 * 投資公司情況一
	 */
	MOW800("MD"),
	/**
	 * 投資公司情況二
	 */
	MOW801("ME"),
	/**
	 * 投資公司一般情況
	 */
	MOW810("M8"),
	/**
	 * 租賃公司
	 */
	MOW900("M9"),
	/**
	 * 境外船舶與航空器
	 */
	MOW510("MF"),

	/**
	 * 境外台商貿易型控股型(境外貿易型控股型)
	 */
	MOW520("MG"),

	/**
	 * 境外大型(國金部本部企業評等模型-實體營運企業)
	 * 
	 */
	MOW530("MH"),

	/**
	 * 境外租賃業(國金部本部企業評等模型-租賃業)
	 */
	MOW531("MI"),

	/**
	 * 國金部本部企業評等模型-信託、基金及其他金融工具
	 */
	@Deprecated
	MOW532("MJ"),

	/**
	 * 不動產租售業內部評等模型(現在僅授權澳洲三家分行)
	 */
	MOW540("MK"),

	/**
	 * 境外中型/中小型企業評等模型
	 */
	MOW550("ML"),

	/**
	 * 日本一般企業評等模型
	 */
	MOW560("MM"),

	/**
	 * 日本不動產租售業評等模型
	 */
	MOW570("MN"),

	/**
	 * 亞太船舶/航空器模型
	 */
	MOW511("MO"),

	/**
	 * 亞太貿易型控股型企業模型
	 */
	MOW521("MP"),

	/**
	 * 亞太大型企業模型
	 */
	MOW533("MQ"),

	/**
	 * 亞太中型/中小型企業模型
	 */
	MOW535("MR"),

	/**
	 * 亞太租賃業評等表
	 */
	MOW534("MS"),

	/**
	 * 買入企業應收帳款
	 */
	MOWA10("MT"),

	/**
	 * 泰子行大型/中型企業
	 */
	MOW580("MU"),

	/**
	 * 泰子行中小型企業
	 */
	MOW590("MV"),

	/**
	 * 澳洲地區分行一般企業
	 */
	MOWA20("MW"),

	/**
	 * 歐洲地區一般企業評等模型
	 */
	MOWA30("MX"),

	/**
	 * 美國地區一般企業評等模型
	 */
	MOWA40("MY"),

	/******************* 下列為特殊模型(最終資料不上傳中心，不上傳DW) ******************************/

	/**
	 * 澳洲大型企業
	 */
	MOW140("A0"),
	/**
	 * 澳洲中型企業
	 */
	MOW141("A1"),
	/**
	 * 澳洲中小型企業
	 */
	MOW142("A2"),
	/**
	 * 澳洲不動產有建案規劃
	 */
	MOW143("A3"),
	/**
	 * 澳洲不動產無建案規劃一案建商
	 */
	MOW144("A4"),
	/**
	 * 澳洲不動產無建案規劃非一案建商(擔保或土融)
	 */
	MOW145("A5"),
	/**
	 * 澳洲不動產無建案規劃非一案建商(無擔)
	 */
	MOW146("A6")

	;

	private String code;

	MOWCrdType(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static MOWCrdType getEnum(String code) {
		for (MOWCrdType enums : MOWCrdType.values()) {
			if (enums.getCode().equals(code)) {
				return enums;
			}
		}
		return null;
	}
}
