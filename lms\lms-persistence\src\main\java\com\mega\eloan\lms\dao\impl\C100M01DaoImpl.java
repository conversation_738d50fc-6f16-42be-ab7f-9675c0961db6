/* 
 * C100M01DaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.C100M01Dao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C100M01;

/** 擔保品主檔 **/
@Repository
public class C100M01DaoImpl extends LMSJpaDao<C100M01, String> implements
		C100M01Dao {

	@Override
	public C100M01 findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public C100M01 findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C100M01> findByDocStatus(String docStatus) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		List<C100M01> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C100M01> findByIndex01(String mainId) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C100M01> findByCustId(String custId) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C100M01> findByCustName(String custName) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (custName != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custName",
					custName);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C100M01> findByIndex02(String branch, String custId,
			String collNo) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (collNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "collNo", collNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C100M01> findByIndex03(String custId, String dupNo,
			String collNo) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (collNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "collNo", collNo);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C100M01> findByIndex04(String branch, String pndFlag,
			String docStatus) {
		ISearch search = createSearchTemplete();
		List<C100M01> list = null;
		if (branch != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		if (pndFlag != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "pndFlag",
					pndFlag);
		if (docStatus != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
					docStatus);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Long findC100m01Count() {
		Long count = null;
		Query query = getEntityManager().createNamedQuery(
				"C100M01.getC100M01Count");
		List<Long> list = query.getResultList();
		for (Long row : list) {
			count = row;
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Long findC100m01Count2() {
		Long count = null;
		Query query = getEntityManager().createNamedQuery(
				"C100M01.getC100M01Count2");
		List<Long> list = query.getResultList();
		for (Long row : list) {
			count = row;
		}
		return count;
	}

	@Override
	public List<C100M01> findByDocStatusAndBranch(String docStatus,
			String branch) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);
		if (branch != null && branch.length() > 0) {
			search.addSearchModeParameters(SearchMode.EQUALS, "branch", branch);
		}
		List<C100M01> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C100M01> findByOids(String[] oids) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.IN, "oid", oids);
		List<C100M01> list = createQuery(search).getResultList();
		return list;
	}
}