/* 
 * Score.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.constants;

/**
 * <pre>
 * 評等項目
 * </pre>
 * 
 * @since 2012/10/30
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/30,Fantasy,new
 *          </ul>
 */
public interface Score {

	static final String 設定檔_V1_3 = "cls/score.properties";
	static final String 設定檔_V2_0 = "cls/score_V2_0.properties";
	static final String 設定檔_V2_1 = "cls/score_V2_1.properties";
	static final String 設定檔_V3_0 = "cls/score_V3_0.properties";
	
	static final String Comma = ",";
	static final String Point = ".";
	static final String Semicolon = ";";

	static final String 分數 = "score";
	static final String 公式 = "formula";
	static final String 欄位 = "columns";

	interface column {
		String 評等建立日期 = "grdCDate";
		String 評等調整日期 = "grdTDate";
		String 聯徵資料日期 = "jcicDDate";
		String 聯徵查詢日期 = "jcicQDate";
		String 票信資料日期 = "etchDDate";
		String 票信查詢日期 = "etchQDate";
		String 報表亂碼 = "randomCode";
		String 合計變量得分 = "scrNum11";
		String 基準底分 = "scrNum12";
		String 初始評分 = "scrNum13";
		String 初始評等 = "grade1";
		String 調整評等 = "grade2";
		String 最終評等 = "grade3";
		String 預測壞率 = "pd";
		String 基準底分A = "varA";
		String 基準底分B = "varB";
		String 基準底分常數項 = "varC";
		String 基準底分版本 = "varVer";

		String 調整狀態 = "adjustStatus";
		String 調整註記 = "adjustFlag";
		String 調整理由 = "adjustReason";
		String 未持有信用卡 = "cardFlag";
		String 用途別 = "purposeCode";
		String 組別 = "grpNum10";

		// hIncome
		String 夫妻年收入 = "yFamAmt";
		String 夫妻年收入分數 = "scrNum01";
		String 婚姻狀況 = "marry";
		String 學歷 = "edu";
		String 婚姻狀況及學歷分數 = "scrNum02";
		String 職業大類 = "jobType1";
		String 職業大類分數 = "scrNum03";
		String D07聯徵查詢月份前一月或二個月之無擔保授信餘額 = "chkAmt01";
		String D07聯徵查詢月份前一月或二個月之無擔保授信餘額分數 = "scrNum04";
		String 近六個月平均的月信用卡循環信用 = "chkAmt02";
		String 近六個月平均的月信用卡循環信用分數 = "scrNum05";
		
		//N06因子在房貸1.3版(inqQty), 非房貸2.0版(nochkItemN06) 都存在
		String N06十二個月新業務申請查詢總家數 = "inqQty";
		String 十二個月新業務申請查詢總家數分數 = "scrNum06";
		String 近十二個月信用卡_每筆_循環信用平均使用率 = "avgRate01";
		String 近十二個月信用卡_每筆_循環信用平均使用率分數 = "scrNum07";
		String 聯徵查詢月份前一月之信用卡循環信用使用率 = "avgRate02";
		String 聯徵查詢月份前一月之信用卡循環信用使用率分數 = "scrNum08";
		String P25近六個月信用卡繳款狀況出現全額繳清無延遲次數 = "chkNum1";
		String P25近六個月信用卡繳款狀況出現全額繳清無延遲次數分數 = "scrNum09";
		String P69近十二個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 = "chkNum2";
		String P19近十二個月信用卡繳款狀況出現全額繳清無延遲次數 = "chkNum3";
		String 近十二個月信用卡繳款狀況出現全額繳清無延遲次數分數 = "scrNum10";
		String 有退票_拒往_信用卡強停或催收呆帳紀錄 = "chkItem1";
		String 有消債條例信用註記_銀行公會債務協商註記或其他補充註記 = "chkItem2";
		String 近12個月授信帳戶出現延遲二次以上 = "chkItem3";
		String 近12個月信用卡繳款狀況出現_循環信用有延遲_二次以上 = "chkItem4";
		String 近12個月信用卡繳款狀況出現_未繳足最低金額_二次以上 = "chkItem5";
		String 近12個月信用卡繳款狀況出現_全額逾期連續未繳_二次以上 = "chkItem6";
		String 近12個月信用卡有預借現金餘額二次以上 = "chkItem7";
		String 近12個月現金卡有動用紀錄 = "chkItem8";

		// 有退票_拒往_信用卡強停或催收呆帳紀錄 add by fantasy 2013/06/14
		String 退票 = "chkItem1a";
		String 拒往 = "chkItem1b";
		String 信用卡強停 = "chkItem1c";
		String 催收呆帳 = "chkItem1d";
		String 逾期放款 = "chkItem1e";
		// 有消債條例信用註記、銀行公會債務協商註記或其他補充註記 add by fantasy 2013/06/14
		String 消債條例信用註記 = "chkItem2a";
		String 銀行公會債務協商註記 = "chkItem2b";
		String 其他補充註記 = "chkItem2c";
		
		String 引用_房貸 = "quote";
		
		String 違約機率_預估3年期 = "dr_3YR";
		String 違約機率_預估1年期 = "dr_1YR";
		
		String 信用卡循環信用次數 = "revol_cnt";
	
		//J-106-0187 房貸模型 2.0 因子
		String 職稱 = "jobTitle";
		String 年資= "seniority";
		String 年資分數_G= "scrSeniority_G"; //房貸、非房貸的 '級距' 可能不同=>年資分數不同
		String 個人年所得= "pIncome";
		String D07_DIV_PINCOME因子 = "itemD07_DIV_PINCOME";
		String D07_DIV_PINCOME分數 = "scrD07_DIV_PINCOME";
		String P68近六個月信用卡繳款狀況出現不良紀錄或使用循環信用次數 = "itemP68";
		String P68_P19分數 = "scrP68P19";
		String N18次數因子 = "itemN18";
		String N18_JobTitle分數 = "scrN18";
		String N22次數因子 = "itemN22";
		String N22分數 = "scrN22";
		String Z03因子 = "itemZ03";
		String Z03分數 = "scrZ03";
		
		
		//J-111-0271房貸模型 3.0 因子
		String v3_N18因子 = "itemN18";
		String v3_職稱因子 = "jobTitle";
		String v3_D42因子 = "itemD42";
		String v3_Edu因子 = "edu";
		String v3_P68因子 = "itemP68";
		String v3_yRate因子 = "yRate";
		String v3_N01因子 = "itemN01";
		String v3_P19因子 = "chkNum3";
		String v3_R01因子 = "avgRate01";
		//J-111-0271房貸模型 3.0 分數
		String v3_N18分數 = "scrN18Only";
		String v3_職稱分數 = "scrJobTitle";
		String v3_D42分數 = "scrD42";
		String v3_Edu分數 = "scrEdu";
		String v3_P68分數 = "scrP68";
		String v3_yRate分數 = "scrYRate";
		String v3_N01分數 = "scrN01";
		String v3_P19分數 = "scrP19";
		String v3_R01分數 = "scrNum07";
		
		String 截距 = "interCept";
		String 斜率 = "slope";
		
		
		// J-112-0376 BAM095.IS_KIND 借款人任一筆非屬「純信用」或「中小企業信用保證基金」時，於ELOAN徵信欄位新增一註記為「Y」
		String 擔保品類別 = "isKind";
		
	}

	interface type {
		String 基本 = "base";
		String 無擔 = "unassure";
		String 評等 = "grade";
		String 房貸違約機率 = "houseLoanDR";
	}

	/**
	 * <pre>
	 * 評等項目(無擔)
	 * </pre>
	 * 
	 * @since 2012/10/30
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/10/30,Fantasy,new
	 *          </ul>
	 */
	interface unassure {
		String 本人及配偶最近年所得 = "score01";
		String 職業 = "score02";
		String 工作年資 = "score03";
		String 不動產狀況 = "score04";
		String 家庭狀況 = "score05";
		String 住宅狀況 = "score06";
		String 負債比率 = "score07";
		String 與銀行往來 = "score08";
		String 綜合加分 = "score09";
	}

	/**
	 * <pre>
	 * 評等基本項目
	 * </pre>
	 * 
	 * @since 2012/10/30
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/10/30,Fantasy,new
	 *          </ul>
	 */
	interface base {
		String 夫妻年收入 = "scrNum01";
		String 婚姻狀況_學歷 = "scrNum02";
		String 職業大類 = "scrNum03";
		String 聯徵查詢月份前一月或二個月之無擔保授信餘額 = "scrNum04";
		String 近六個月平均的月信用卡循環信用 = "scrNum05";
		String 十二個月新業務申請查詢總家數 = "scrNum06";
		String 近十二個月信用卡_每筆_循環信用平均使用率 = "scrNum07";
		String 聯徵查詢月份前一月之信用卡循環信用使用率 = "scrNum08";
		String 近六個月信用卡繳款狀況出現全額繳清無延遲次數 = "scrNum09";
		String 近十二個月信用卡繳款狀況出現不良紀錄 = "scrNum10";
		String 組別 = "grpNum10";
	}
	interface baseG_V2_0 {
		String 因子1_夫妻年收入 = "scrNum01";
		String 因子2_年資分數 = "scrSeniority_G";		
		String 因子3_D07_DIV_PINCOME = "scrD07_DIV_PINCOME";
		String 因子4_N18 = "scrN18";
		String 因子5_P25 = "scrNum09";
		String 因子6_P68_P19 = "scrP68P19";
		String 因子6_組別 = "grpNum10";
		String 因子7_Z03 = "scrZ03";		
	}
	
	interface baseG_V2_1 {
		String 因子1_夫妻年收入 = "scrNum01";
		String 因子2_年資分數 = "scrSeniority_G";		
		String 因子3_D07_DIV_PINCOME = "scrD07_DIV_PINCOME";
		String 因子4_N22 = "scrN22";
		String 因子5_P25 = "scrNum09";
		String 因子6_P68_P19 = "scrP68P19";
		String 因子6_組別 = "grpNum10";
		String 因子7_Z03 = "scrZ03";		
	}
	
	interface baseG_V3_0 { //J-111-0271 消金房貸3.0 (分數)
//		String 因子1_N18 = "scrN18Only";
		String 因子1_N22 = "scrN22";
		String 因子2_職稱 = "scrJobTitle";
		String 因子3_D42 = "scrD42";
		String 因子4_學歷 = "scrEdu";
		String 因子5_P68 = "scrP68";
		String 因子6_夫妻負債比 = "scrYRate";
		String 因子7_N01 = "scrN01";
		String 因子8_P19 = "scrP19";
		String 因子9_R01 = "scrNum07";
	}

	/**
	 * <pre>
	 * 評等等級項目
	 * </pre>
	 * 
	 * @since 2012/11/5
	 * <AUTHOR>
	 * @version <ul>
	 *          <li>2012/11/5,Fantasy,new
	 *          </ul>
	 */
	interface grade {
		String 等級 = "level";
	}
	
	/**
	 * <pre>
	 * 房貸評等違約機率項目
	 * </pre>
	 */
	interface houseLoanDR {		
		String 違約機率_預估3年期 = "dr_3YR";
		String 違約機率_預估1年期 = "dr_1YR";
	}
}
