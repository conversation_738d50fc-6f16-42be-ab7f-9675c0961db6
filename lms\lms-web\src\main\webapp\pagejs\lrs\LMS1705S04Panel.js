$(function(){
    // 預設值
    $('#returnBefore').click(function(){
    	thickboxShow();
    })
    
    function choice(choiceResult){
    	if(choiceResult){
    		CommonAPI.confirmMessage(i18n.lms1705s04["L170S04.ASK02"], function(b){
                if (b) {
                	return;
                }else{
                	CommonAPI.showErrorMessage("", i18n.lms1705s04["L170S04.ASK03"], function(){
                		thickboxShow();
                    });
                }
            })
    	}
    }
    
    function thickboxShow(){
    	var test2 = $("#lms1705s04").thickbox({
            title: i18n.lms1705s04['L170S04.ASKTitle01'],
            width: 340,
            height: 80,
            modal: false,
            valign: "bottom",
            align: "center",
            i18n: i18n.lms1705s04,
            buttons: {
            	"L170M01d.have": function(){
                	returnL170s04Val("Y");
                    $.thickbox.close();
                },
                "L170M01d.nohave": function(){
                	returnL170s04Val("N");
                    $.thickbox.close();
                },
                "cancel": function(){
                    $.thickbox.close();
                	choice(true);
                }//關閉
            }//bottons
        });//thickbox
    }
    
    
    function returnL170s04Val(HASCMSVal){
    	$.ajax({
            handler: "lms1705m01formhandler",
            type: "POST",
            dataType: "json",
            action: "returnL170s04Val",
            data: {                // formAction: "returnL170s04Val"
            	HASCMS : HASCMSVal
            },
		}).done(function(obj){
			// alert(obj.ChkResult5);
			$("#L170M01dForm").injectData(obj);
			var array = obj.L170M01DArray;                
			for (var i = 0; i < array.length; i++) {
			    var json = array[i];
			    var itemCount = json.itemNo;
			    var uuu = json.chkResult;
			    
			    $("input[type='radio'][name='chkResult" + itemCount + "'][value=" + uuu + "]").prop("checked", true);                    
			}

			// J-112-0280  新增企金覆審報告之「附表」項下E-LOAN系統建檔增列3.授信管理系統-「風險權數試算明細」。
			$("input[name=chkResultY124]:radio").trigger('change');
			$("input[name=chkResultY210]:radio").trigger('change');
			$("input[name=chkResultY21A]:radio").trigger('change');
			$("input[name=chkResultY22A]:radio").trigger('change');
			$("input[name=chkResultY230]:radio").trigger('change');
			$("input[name=chkResultY234]:radio").trigger('change');
			$("input[name=chkResultY236]:radio").trigger('change');
			$("input[name=chkResultY238]:radio").trigger('change');
			$("input[name=chkResultY239]:radio").trigger('change');
			$("input[name=chkResultX110]:radio").trigger('change');
			$("input[name=chkResultX210]:radio").trigger('change');
			// J-113-0204  新增及修正說明文句
			$("input[name=chkResultY310]:radio").trigger('change');
		})
    }
})
