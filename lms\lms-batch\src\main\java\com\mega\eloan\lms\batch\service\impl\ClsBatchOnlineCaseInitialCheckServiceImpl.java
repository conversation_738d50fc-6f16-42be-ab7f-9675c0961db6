package com.mega.eloan.lms.batch.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.ETCHGwClient;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.SysParameterService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.EJCICCOMMONDao;
import com.mega.eloan.lms.ejcic.service.EjcicService;
import com.mega.eloan.lms.etch.service.EtchService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122S01G;
import com.mega.eloan.lms.model.EJCICCOMMON;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.annotation.NonTransactional;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 *  J-111-0602 歡喜信貸徵審流程初審批次
 * </pre>
 * 
 * @since 2023/01/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/01/18,new
 *          </ul>
 */
@Service("clsBatchOnlineCaseInitialCheckServiceImpl")
public class ClsBatchOnlineCaseInitialCheckServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory.getLogger(ClsBatchOnlineCaseInitialCheckServiceImpl.class);
	
	private static final String Virtual_Employee_id = "00ZCB2";
	private static final String Virtual_Employee_Name = "信貸初審";
	private static final String Q_Func_Src = "H";
	private static final String Ejcic_TxId_P9 = "P9";
	
	private static final String[] rejectCode = new String[]{"G09", "G12", "G18", "G19", "G21"};
	
	protected static final int timeout = 60;
	
	@Resource
	C122M01ADao c122m01aDao;
	
	@Resource
	BranchService branchService;
	
	@Resource
	EJCICGwClient ejcicClient;
	
	@Resource
	EjcicService ejcicService;
	
	@Resource
	CLSService clsService;
	
	@Resource
	EJCICCOMMONDao ejcicCommonDao;
	
	@Resource
	EtchService etchService;
	
	@Resource
	ETCHGwClient etchClient;

	@Resource
	CLS1220Service cls1220Service;
	
	@Resource
	SysParameterService sysParameterService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	CLS1131Service cls1131Service;

	@Override
	@NonTransactional
	public JSONObject execute(JSONObject json) {
		
		JSONObject result = null;
		String mainId = null;
		String custId = null;
		String dupNo = null;
		String branchNo = null;
		try {
			
			// 判斷今天是不是營業日
			Map<String, Object> lnf320 = misdbBASEService.get_LNF320(CapDate.getCurrentDate("yyyy-MM-dd"));
			if (MapUtils.isEmpty(lnf320)) {
				result = JSONObject.fromObject(WebBatchCode.RC_SUCCESS);
				result.element(WebBatchCode.P_RESPONSE, "ClsLaborReliefBatchServiceImpl 執行成功！今日非營業日");
				return result;
			}
			
			Map<String, String> rejectCodeMap = this.codeTypeService.findByCodeType("c122m01a_docstatus_G");
			
			int j10CheckScore = Integer.parseInt(this.sysParameterService.getParamValue("ONLINE_CASE_INIT_CHECK_SCORE"));
			
			List<C122M01A> c122m01aList = c122m01aDao.findOnlineCasesRequiringInitialCheck();
	
			for(C122M01A c122m01a : c122m01aList){
				
				Integer initCheckTimes = c122m01a.getInitCheckTimes() == null ? 0 : c122m01a.getInitCheckTimes();
				String initCheckStatus = null;
				mainId = c122m01a.getMainId();
				custId = c122m01a.getCustId();
				dupNo = StringUtils.isBlank(c122m01a.getDupNo()) ? "0" : c122m01a.getDupNo();
				branchNo = c122m01a.getOwnBrId();
				String checkStatus = null;

				initCheckTimes++;

				String txId = ClsBatchOnlineCaseInitialCheckServiceImpl.Ejcic_TxId_P9;
				String prodId = null;
				String qDate = null;
				
				Map<String, Object> ejcicLogMap = this.processCombinationEjcicInquiry(mainId, custId, dupNo, branchNo, txId);

				if(!this.isEjcicSuccess(ejcicLogMap, txId, mainId, custId, dupNo)){
					checkStatus = "X";
				}
				
				if(!"X".equals(checkStatus)){
					prodId = String.valueOf(ejcicLogMap.get("PRODID"));
					qDate = String.valueOf(ejcicLogMap.get("QDATE"));
					Map<String, Object> j10Data = this.ejcicService.getKCS003DataBy(custId, prodId, qDate);
					checkStatus = this.checkIsReachingScore(j10Data, j10CheckScore);
				}

				if("Y".equals(checkStatus)){
					prodId = String.valueOf(ejcicLogMap.get("PRODID"));
					qDate = String.valueOf(ejcicLogMap.get("QDATE"));
					checkStatus = this.checkOtherRejectItem(custId, prodId, qDate);
				}
				
				initCheckStatus = checkStatus;
				//婉却
				if(Arrays.asList(rejectCode).contains(checkStatus)){
					initCheckStatus = "N";
					c122m01a.setDocStatus("G00");
					c122m01a.setStatFlag("E");
					//新增C122S01G
					C122S01G entity = new C122S01G();
					entity.setMainId(mainId);
					entity.setUid(mainId);
					entity.setDocStatus("G00");
					entity.setCodeValue(checkStatus);
					entity.setCodeDesc(rejectCodeMap.get(checkStatus));
					entity.setCreator(ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
					entity.setCreateTime(CapDate.getCurrentTimestamp());
					cls1220Service.saveC122S01G(entity);
				}
				
				//成功
				if("Y".equals(initCheckStatus)){
					// 聯徵T70查詢
					this.processEjcicT70(custId, dupNo, branchNo, mainId);
					c122m01a.setDocStatus("A00");
				}
				
				if("Y".equals(initCheckStatus) || "N".equals(initCheckStatus)){
					c122m01a.setInitChkFinishTime(CapDate.getCurrentTimestamp());
				}
				
				c122m01a.setInitCheckStatus(initCheckStatus);
				c122m01a.setInitCheckTimes(initCheckTimes);
				c122m01a.setUpdater(ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
				c122m01a.setUpdateTime(CapDate.getCurrentTimestamp());
				clsService.daoSave(c122m01a);
				LOGGER.debug("歡喜信貸徵審流程初審執行成功 ===> mainId: {}, custId: {}, dupNo: {}, branchNo: {}", new String[]{mainId, custId, dupNo, branchNo});
			}
			
			result = WebBatchCode.RC_SUCCESS;
			result.element(WebBatchCode.P_RESPONSE, "clsBatchOnlineCaseInitialCheckServiceImpl 執行成功！");

		} catch (Exception ex) {
			LOGGER.error(StrUtils.getStackTrace(ex));
			result = WebBatchCode.RC_ERROR;
			String msg = ex.getMessage() + "===> mainId:" + mainId + ", custId:" + custId + ", dupNo:" + dupNo + ", branchNo:" + branchNo;
			result.element(
					WebBatchCode.P_RESPONSE, "clsBatchOnlineCaseInitialCheckServiceImpl 執行失敗！==>" + msg + " - " + ex.getLocalizedMessage());
		}

		return result;
	}
	
//	private Map<String, Object> processStandardEjcicInquiry(String txId, String mainId, String custId, String dupNo, String branchNo) throws ClientProtocolException, IOException, CapException{
//		
//		//get ejcic log data
//		Map<String, Object> logMap = this.ejcicService.getLatestLogFileByTxId(custId, "H" + txId);
//		
//		boolean checkIsQueryEjcic = this.checkIsQueryEjcic(logMap, custId, dupNo);
//		String prodId = null;
//		String qDate = null;
//		
//		//查詢聯徵
//		String httpCode = null;
//		
//		if(checkIsQueryEjcic){
//			prodId = "ST";
//			qDate = CapDate.getCurrentDate("YYY/MM/DD");
//			String ejcicJ10Url = this.getEjcicUrl(branchNo, custId, prodId, txId);
//			this.clsService.keepHtmlDataToEjcicCommonOfStardardInquiry(mainId, custId, dupNo, "H" + txId, ejcicJ10Url, prodId, branchNo, qDate, 
//																		ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id, ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_Name, 
//																		ClsBatchOnlineCaseInitialCheckServiceImpl.Q_Func_Src, ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
//			httpCode = this.queryEjcicByHttpClient(ejcicJ10Url);
//			logMap = this.ejcicService.getLatestLogFileByQKey1_txId_toJcic(custId, "H" + txId);
//		}
//		
//		prodId = String.valueOf(logMap.get("PRODID"));
//		
//		//不須查詢聯徵
//		if(!checkIsQueryEjcic){
//			httpCode = "200";
//		}
//		
//		qDate = String.valueOf(logMap.get("QDATE"));
//		
//		if("P7".equals(prodId) || "P9".equals(prodId)){
//			this.keepP7P9HtmlFromCPXQueryLogToEjcicCommon(mainId, custId, dupNo, prodId, qDate);
//		}
//		
//		logMap.put("HTTPCODE", httpCode);
//		
//		return logMap;
//	}
	
	private String checkIsReachingScore(Map<String, Object> j10Data, int reachScore){
		
		if(j10Data == null){
			return "X";//代表聯徵尚未回傳資料
		}
		
		int score = LMSUtil.nullToZeroBigDecimal(j10Data.get("SCORE")).intValue();
		
		/* 
		 * 排除下列狀況：
		 * 
		 * 信用資料不足：「目前未有持卡滿3個月之有效信用卡」或「近12個月之信用卡應繳金額皆未大於0」；
		 * 「目前未有連續3期之授信資料」或「近12期之授信餘額皆未大於0」。
		 * 
		 */
		if(("002".equals(j10Data.get("REASON_CODE_1")) || "002".equals(j10Data.get("REASON_CODE_2"))) && score == 0){
			return "Y";
		}
		
		if(score <= reachScore){
			//婉拒
			return "G09";
		}
		
		return "Y";
	}
	
	private String checkOtherRejectItem(String custId, String prodId, String qDate){
		
		//判斷 2, 3, 4 項目是否婉拒
		Map<String, Object> vam106Map = this.ejcicService.getVAM106DataExceptMainCode29DF(custId, prodId, qDate);//消債條例信用註記資訊
		Map<String, Object> vam107Map = this.ejcicService.getVAM107DataExceptMainCode29DF(custId, prodId, qDate);//銀行公會消金案件債務協商補充註記
		Map<String, Object> vam108Map = this.ejcicService.getVAM108DataExceptMainCode29DF(custId, prodId, qDate);//其他補充註記
		if(vam106Map != null || vam107Map != null || vam108Map != null){
			return "G18";
		}

		//信用卡強停紀錄
		Map<String, Object> creditCardMap = ejcicService.getKRM001StopCreditCard(custId, prodId, qDate);
		if (creditCardMap != null) {
			return "G19";
		}
		
		//主債務逾期、催收、呆帳紀錄
		Map<String, Object> bam087Map = ejcicService.getBAM087CollectionInfo1(custId, prodId, qDate);
		if (bam087Map != null) {
			BigDecimal overDueAmt = Util.parseBigDecimal(bam087Map.get("TOT_PASS")); // 逾期金額
			if (overDueAmt.compareTo(BigDecimal.ZERO) > 0){
				return "G21";
			}
		}
		
		//聯徵 BAM095,近12個月繳款紀錄有任一次不是O或X或空白
		List<Map<String, Object>> loanDataList = this.ejcicService.getBAM095_data(custId, prodId, qDate);
		for(Map<String, Object> m : loanDataList){
			String payRecord = m.get("PAY_CODE_12") == null ? "" : (String)m.get("PAY_CODE_12");
			
			if(!"".equals(payRecord.trim())){
				payRecord = payRecord.replace("0", "").replace("X", "");
				if(!"".equals(payRecord)){
					return "G12";
				}
			}
		}
		
		return "Y";
	}

	private String getEjcicUrl(String branchNo, String custId, String prodId, String txId) throws ClientProtocolException, IOException{
		
		String cbdeptid = "";
		String deptnm = "";
		IBranch iBranch = branchService.getBranch(branchNo);
		if (iBranch != null) {
			cbdeptid = iBranch.getBrNo() + iBranch.getChkNo();
			deptnm = iBranch.getBrName();
		}
		
		EJCICGwReqMessage ejcicReq = new EJCICGwReqMessage();
		ejcicReq.setSysId(UtilConstants.CaseSchema.個金);
		ejcicReq.setMsgId(IDGenerator.getUUID());
		ejcicReq.setQueryid(custId);
		ejcicReq.setEmpid(ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
		ejcicReq.setEmpname(ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_Name);
		ejcicReq.setDeptid(branchNo);
		ejcicReq.setCbdeptid(cbdeptid);
		ejcicReq.setBranchnm(deptnm);
		ejcicReq.setPur("A4A");  /*  查詢理由:A4A => 
									 *	第一層 A.新業務申請
									 *  第二層 4放款業務(c) 
									 *	第三層 A.取得當事人書面同意
									 */
		ejcicReq.setProdid("ST".equals(prodId) ? "" : prodId);
		ejcicReq.setPurpose("3");/* 查詢目的:
								    1:企業授信,   2:房屋貸款,
								    3:消費性貸款, 4:留學生貸款 */
		if("ST".equals(prodId)){
			ejcicReq.setTxid(txId);
			ejcicReq.setQkey1(custId);//custId
			ejcicReq.setQkey2("");
		}

		return Util.trim(ejcicClient.get_callAPI_URL(ejcicReq));
	}
	
	
	private boolean checkIsQueryEjcic(Map<String, Object> logMap, String custId, String dupNo){
		
		//無log data 需重查
		if(logMap == null){
			return true;
		}

		//J-113-0122 修改歡喜信貸自動徵審-引入30天內聯徵資料庫資料-超過30天再查詢聯徵
		//超過7天資料需重新查詢
		String prodId = String.valueOf(logMap.get("PRODID"));
//		String qDate = String.valueOf(logMap.get("QDATE"));
//		String qDate_yyyyMMdd = CapDate.formatDateFormatToyyyyMMdd(qDate, "YYY/MM/DD");
//
//		Calendar calQdate = Calendar.getInstance();
//		calQdate.setTime(CapDate.parseDate(qDate_yyyyMMdd));
//		calQdate.add(Calendar.DATE, 7); //minus number would decrement the days
//		Date qDateAfter7Days = calQdate.getTime();
//		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyyMMdd"));
//		
//		if(qDateAfter7Days.compareTo(toDate) < 0){
//			return true;
//		}

		if("ST".equals(prodId)){
			
			EJCICCOMMON ejcicCommon = this.ejcicCommonDao.findBy(custId, dupNo, String.valueOf(logMap.get("TXID")), prodId, String.valueOf(logMap.get("QBRANCH")), String.valueOf(logMap.get("QDATE")), ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id, ClsBatchOnlineCaseInitialCheckServiceImpl.Q_Func_Src);
			if(ejcicCommon == null){
				return true;
			}
		}
		
		return false;	
	}
	
	private String queryEjcicByHttpClient(String url){
		
		final String TITLE = StrUtils.concat("queryEjcicByUrl [send][", System.nanoTime(), "] ");
		
		HttpResponse response;
		String rcCode = null;
		String httpMessage = null;
		
		try {
			
			LOGGER.info(TITLE + "[start] url={}", url);
			
			HttpClient httpclient = this.getDefaultHttpClient(url.toLowerCase().startsWith("https"));
			HttpGet httpGet = new HttpGet(url);
			response = httpclient.execute(httpGet);
			rcCode = String.valueOf(response.getStatusLine().getStatusCode());
			httpMessage = response.getStatusLine().getReasonPhrase();
			
		} catch (IOException ioe) {
			
			LOGGER.error(StrUtils.getStackTrace(ioe));
			
		} catch (Exception e) {
			
			LOGGER.error(StrUtils.getStackTrace(e));
			
		} finally{
			
			LOGGER.info(TITLE + "[end] httpCode=" + rcCode + ", httpMessage=" + httpMessage);
		}
		
		return rcCode;
	}
	
	private DefaultHttpClient getDefaultHttpClient(boolean urlStartsWithHttps) {
		boolean useHttps = urlStartsWithHttps;
		
		final HttpParams params = new BasicHttpParams();
		HttpConnectionParams.setStaleCheckingEnabled(params, false);
		HttpConnectionParams.setConnectionTimeout(params, timeout * 1000);
		HttpConnectionParams.setSoTimeout(params, timeout * 1000);
		HttpConnectionParams.setSocketBufferSize(params, 8192 * 5);
		DefaultHttpClient httpClient = new DefaultHttpClient(params);
		
		if(useHttps){
			X509TrustManager tm = new X509TrustManager() {
				public void checkClientTrusted(
						java.security.cert.X509Certificate[] x509Certificates,
						String s)
						throws java.security.cert.CertificateException {
				}

				public void checkServerTrusted(
						java.security.cert.X509Certificate[] x509Certificates,
						String s)
						throws java.security.cert.CertificateException {
				}

				public java.security.cert.X509Certificate[] getAcceptedIssuers() {
					return new java.security.cert.X509Certificate[0];
				}
            };
            
            try { 
				//java 6 可能沒有到 1.2, 會出現 NoSuchAlgorithmException
				//若把 JRE 切換至 JAVA 8 可以執行
				SSLContext ctx = SSLContext.getInstance("TLSv1.2"); // ("TLSv1.2"); 
				
				//傳入的參數 TLS 會丟出 peer not authenticated
				//SSLContext ctx = SSLContext.getInstance("TLS"); 
	            ctx.init(null, new TrustManager[]{tm}, null);	            
	            SSLSocketFactory sf = new SSLSocketFactory(ctx, SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
				Scheme sch = new Scheme("https", 443, sf);
				httpClient.getConnectionManager().getSchemeRegistry().register(sch);
			} catch (NoSuchAlgorithmException e) {
				LOGGER.error("[getDefaultHttpClient]NoSuchAlgorithmException!!");
			} catch (KeyManagementException e) {
				LOGGER.error("[getDefaultHttpClient]KeyManagementException!!");
			}
		}
		
		return httpClient;
	}
	
	private boolean isEjcicSuccess(Map<String, Object> logMap, String txId, String mainId, String custId, String dupNo){
		
		if(logMap == null || !"200".equals(logMap.get("HTTPCODE")) || !"0000".equals(logMap.get("RETCODE"))){
			String httpCode = logMap == null ? null : String.valueOf(logMap.get("HTTPCODE"));
			String retCode = logMap == null ? null : String.valueOf(logMap.get("RETCODE"));
			LOGGER.debug("聯徵"+ txId +" 查詢失敗: mainId={}, custId={}, dupNo={}, httpCode={}, retCode={}", new String[]{mainId, custId, dupNo, httpCode, retCode});
			return false;
		}
		
		return true;
	}
	
	@SuppressWarnings("unused")
	private String get_cbdeptid_totLen4_BrNo_ChkNo(String given_cbdeptid){
		IBranch iBranch_cbdeptid = branchService.getBranch(given_cbdeptid);
		if(iBranch_cbdeptid!=null){
			return iBranch_cbdeptid.getBrNo() + iBranch_cbdeptid.getChkNo();	
		}
		return "";
	}

	private Map<String, Object> processCombinationEjcicInquiry(String mainId, String custId, String dupNo, String branchNo, String prodId) throws ClientProtocolException, IOException, CapException{
		
		//get ejcic log data
		List<Map<String, Object>> list = ejcicService.get_mis_datadate_records(custId, prodId);
		Map<String, Object> logMap = !list.isEmpty() ? list.get(0) : null;
		
		boolean checkIsQueryEjcic = this.checkIsQueryEjcic(logMap, custId, dupNo);
		//查詢聯徵
		String qDate = null;
		String httpCode = null;
		if(checkIsQueryEjcic){
			String ejcicJ10Url = this.getEjcicUrl(branchNo, custId, prodId, "");
			httpCode = this.queryEjcicByHttpClient(ejcicJ10Url);
			list = ejcicService.get_mis_datadate_records(custId, prodId);
			logMap = !list.isEmpty() ? list.get(0) : null;
		}
		
		//不須查詢聯徵
		if(!checkIsQueryEjcic){
			httpCode = "200";
		}
		
		if(logMap != null){
			qDate = String.valueOf(logMap.get("QDATE"));
			
			if("P7".equals(prodId) || "P9".equals(prodId)){
				this.keepP7P9HtmlFromCPXQueryLogToEjcicCommon(mainId, custId, dupNo, prodId, qDate);
			}
			
			logMap.put("HTTPCODE", httpCode);
			logMap.put("RETCODE", "0000");
			logMap.put("PRODID", prodId);
		}
		
		return logMap;
	}
	
	@SuppressWarnings("unused")
	private boolean checkIsQueryEtch(Map<String, Object> logMap, String custId, String dupNo){
		
		//無log data 需重查
		if(logMap == null){
			return true;
		}

		//超過7天資料需重新查詢
		String qDate_yyyyMMdd = String.valueOf(logMap.get("QDATE"));

		Calendar calQdate = Calendar.getInstance();
		calQdate.setTime(CapDate.parseDate(qDate_yyyyMMdd));
		calQdate.add(Calendar.DATE, 7); //minus number would decrement the days
		Date qDateAfter7Days = calQdate.getTime();
		Date toDate = CapDate.parseDate(CapDate.getCurrentDate("yyyyMMdd"));
		
		if(qDateAfter7Days.compareTo(toDate) < 0){
			return true;
		}

		return false;	
	}

	private void keepP7P9HtmlFromCPXQueryLogToEjcicCommon(String mainId, String custId, String dupNo, String prodId, String qDate){
		
		List<Map<String, Object>> htmlList = ejcicService.getCPXQueryLogHtml(custId, prodId, qDate);
		
		for (Map<String, Object> map : htmlList) {
			String txId = Util.trim(map.get("TXID"));
			this.clsService.deleteEjcicCommon(custId, prodId, txId, qDate, ClsBatchOnlineCaseInitialCheckServiceImpl.Q_Func_Src, ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
		}
		
		this.clsService.saveEjcicCommonFromCPXQueryLog(htmlList, mainId, custId, dupNo, ClsBatchOnlineCaseInitialCheckServiceImpl.Q_Func_Src, ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
	}
	
	private void processEjcicT70(String custId, String dupNo, String branchNo, String mainId){
		
		// ELF690_EJ_TMESTAMP 有時間, 但 MIS.TAS700 沒資料, 代表超過15天資料被清掉了
		boolean isQueryT70 = this.cls1131Service.isQueryEjcicT70Info(custId, dupNo, branchNo, mainId);
		
		if(isQueryT70){
			
			/*  查詢理由:A4A => 
			 *	第一層 A.新業務申請
			 *  第二層 4放款業務(c) 
			 *	第三層 A.取得當事人書面同意
			 */
			this.misdbBASEService.insertELF690_EjcicT70Inquiry(custId, dupNo, "A4A", branchNo, ClsBatchOnlineCaseInitialCheckServiceImpl.Virtual_Employee_id);
			this.cls1131Service.deleteC101S02S(mainId, custId, dupNo);
			this.cls1131Service.saveC101S02S(mainId, custId, dupNo, branchNo);
		}
	}

}
