/* 
 * C900M01K_LGD.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信業務授權額度檔_LGD版 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01K_LGD", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C900M01K_LGD extends GenericBean implements IDataObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/**
	 * 企/個金分類
	 * <p/>
	 * 1企金 <br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "VARCHAR(1)")
	private String docType;
	
	/** 
	 * 授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=2)
	@Column(name="CASELVL", length=2, columnDefinition="VARCHAR(2)")
	private String caseLvl;

	/** 
	 * 分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="VARCHAR(3)")
	private String brNo;

	/** 
	 * 分行等級<p/>
	 * COM.BELSBRN.brClass<br/>
	 *  若為Y則為簡易分行<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	@Size(max=1)
	@Column(name="BRCLASS", length=1, columnDefinition="VARCHAR(1)")
	private String brClass;

	/** 
	 * PD分組<p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 *  若此欄位為空，代表授權層級不看PD
	 */
	@Size(max=1)
	@Column(name="PDGROUP", length=1, columnDefinition="VARCHAR(1)")
	private String pdGroup;

	/** 
	 * LGD一<p/>
	 * LGDⅠ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_1", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_1;

	/** 
	 * LGD二<p/>
	 * LGDⅡ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_2", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_2;

	/** 
	 * LGD三<p/>
	 * LGDⅢ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_3", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_3;

	/** 
	 * LGD四<p/>
	 * LGDⅣ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_4", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_4;

	/** 
	 * LGD五<p/>
	 * LGDⅤ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_5", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_5;

	/** 
	 * LGD六<p/>
	 * LGDⅥ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_6", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_6;

	/** 
	 * LGD七<p/>
	 * LGDⅦ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_7", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_7;

	/** 
	 * LGD八<p/>
	 * LGDⅧ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_8", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_8;

	/** 
	 * LGD九<p/>
	 * LGDⅨ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_9", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_9;

	/** 
	 * LGD十<p/>
	 * LGDⅩ限額
	 */
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="LGDTOTAMT_10", columnDefinition="DECIMAL(17,2)")
	private BigDecimal lgdTotAmt_10;

	/** 授信每戶總額 **/
	@Digits(integer=17, fraction=2, groups = Check.class)
	@Column(name="TOTALAMT", columnDefinition="DECIMAL(17,2)")
	private BigDecimal totalAmt;

	/** 
	 * 版本<p/>
	 * 判斷LGD欄位要用到多少欄
	 */
	@Size(max = 13)
	@Column(name = "VERSION", length = 13, columnDefinition = "VARCHAR(13)")
	private String version;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/**
	 * 設定企/個金分類
	 **/
	public String getDocType() {
		return docType;
	}

	/**
	 * 取得企/個金分類
	 **/
	public void setDocType(String docType) {
		this.docType = docType;
	}

	/** 
	 * 取得授權層級<p/>
	 * 參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}
	/**
	 *  設定授權層級<p/>
	 *  參考lms1205m01_caseLvl<br/>
	 *  A.董事會 <br/>
	 *  1.常務董事會<br/>
	 *  6.總經理<br/>
	 *  7.副總經理<br/>
	 *  8.授信審查處處長<br/>
	 *  B.區域營運中心營運長<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/** 
	 * 取得分行代號<p/>
	 * COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getBrNo() {
		return this.brNo;
	}
	/**
	 *  設定分行代號<p/>
	 *  COM.BELSBRN.brNo<br/>
	 *  放特別指定的分行代號<br/>
	 *  007.國外部<br/>
	 *  201.金控總部分行<br/>
	 *  025.國際金融業務分行經理<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrNo(String value) {
		this.brNo = value;
	}

	/** 
	 * 取得分行等級<p/>
	 * COM.BELSBRN.brClass<br/>
	 *  若為Y則為簡易分行<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 */
	public String getBrClass() {
		return this.brClass;
	}
	/**
	 *  設定分行等級<p/>
	 *  COM.BELSBRN.brClass<br/>
	 *  若為Y則為簡易分行<br/>
	 *  <br/>
	 *  caseLvl、brNo、brClass擇一有值
	 **/
	public void setBrClass(String value) {
		this.brClass = value;
	}

	/** 
	 * 取得PD分組<p/>
	 * 可能有A~E五項，也有可能增減 <br/>
	 *  若此欄位為空，代表授權層級不看PD
	 */
	public String getPdGroup() {
		return this.pdGroup;
	}
	/**
	 *  設定PD分組<p/>
	 *  可能有A~E五項，也有可能增減 <br/>
	 *  若此欄位為空，代表授權層級不看PD
	 **/
	public void setPdGroup(String value) {
		this.pdGroup = value;
	}

	/** 
	 * 取得LGD一<p/>
	 * LGDⅠ限額
	 */
	public BigDecimal getLgd1() {
		return this.lgdTotAmt_1;
	}
	/**
	 *  設定LGD一<p/>
	 *  LGDⅠ限額
	 **/
	public void setLgd1(BigDecimal value) {
		this.lgdTotAmt_1 = value;
	}

	/** 
	 * 取得LGD二<p/>
	 * LGDⅡ限額
	 */
	public BigDecimal getLgd2() {
		return this.lgdTotAmt_2;
	}
	/**
	 *  設定LGD二<p/>
	 *  LGDⅡ限額
	 **/
	public void setLgd2(BigDecimal value) {
		this.lgdTotAmt_2 = value;
	}

	/** 
	 * 取得LGD三<p/>
	 * LGDⅢ限額
	 */
	public BigDecimal getLgd3() {
		return this.lgdTotAmt_3;
	}
	/**
	 *  設定LGD三<p/>
	 *  LGDⅢ限額
	 **/
	public void setLgd3(BigDecimal value) {
		this.lgdTotAmt_3 = value;
	}

	/** 
	 * 取得LGD四<p/>
	 * LGDⅣ限額
	 */
	public BigDecimal getLgd4() {
		return this.lgdTotAmt_4;
	}
	/**
	 *  設定LGD四<p/>
	 *  LGDⅣ限額
	 **/
	public void setLgd4(BigDecimal value) {
		this.lgdTotAmt_4 = value;
	}

	/** 
	 * 取得LGD五<p/>
	 * LGDⅤ限額
	 */
	public BigDecimal getLgd5() {
		return this.lgdTotAmt_5;
	}
	/**
	 *  設定LGD五<p/>
	 *  LGDⅤ限額
	 **/
	public void setLgd5(BigDecimal value) {
		this.lgdTotAmt_5 = value;
	}

	/** 
	 * 取得LGD六<p/>
	 * LGDⅥ限額
	 */
	public BigDecimal getLgd6() {
		return this.lgdTotAmt_6;
	}
	/**
	 *  設定LGD六<p/>
	 *  LGDⅥ限額
	 **/
	public void setLgd6(BigDecimal value) {
		this.lgdTotAmt_6 = value;
	}

	/** 
	 * 取得LGD七<p/>
	 * LGDⅦ限額
	 */
	public BigDecimal getLgd7() {
		return this.lgdTotAmt_7;
	}
	/**
	 *  設定LGD七<p/>
	 *  LGDⅦ限額
	 **/
	public void setLgd7(BigDecimal value) {
		this.lgdTotAmt_7 = value;
	}

	/** 
	 * 取得LGD八<p/>
	 * LGDⅧ限額
	 */
	public BigDecimal getLgd8() {
		return this.lgdTotAmt_8;
	}
	/**
	 *  設定LGD八<p/>
	 *  LGDⅧ限額
	 **/
	public void setLgd8(BigDecimal value) {
		this.lgdTotAmt_8 = value;
	}

	/** 
	 * 取得LGD九<p/>
	 * LGDⅨ限額
	 */
	public BigDecimal getLgd9() {
		return this.lgdTotAmt_9;
	}
	/**
	 *  設定LGD九<p/>
	 *  LGDⅨ限額
	 **/
	public void setLgd9(BigDecimal value) {
		this.lgdTotAmt_9 = value;
	}

	/** 
	 * 取得LGD十<p/>
	 * LGDⅩ限額
	 */
	public BigDecimal getLgd10() {
		return this.lgdTotAmt_10;
	}
	/**
	 *  設定LGD十<p/>
	 *  LGDⅩ限額
	 **/
	public void setLgd10(BigDecimal value) {
		this.lgdTotAmt_10 = value;
	}

	/** 取得授信每戶總額 **/
	public BigDecimal getTotalAmt() {
		return this.totalAmt;
	}
	/** 設定授信每戶總額 **/
	public void setTotalAmt(BigDecimal value) {
		this.totalAmt = value;
	}

	/** 
	 * 取得版本<p/>
	 * 判斷LGD欄位要用到多少欄
	 */
	public String getVersion() {
		return this.version;
	}
	/**
	 *  設定版本<p/>
	 *  判斷LGD欄位要用到多少欄
	 **/
	public void setVersion(String value) {
		this.version = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
