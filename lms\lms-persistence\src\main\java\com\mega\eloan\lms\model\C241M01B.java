/* 
 * C241M01B.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 授信帳務資料檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C241M01B", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C241M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 額度序號 **/
	@Column(name="QUOTANO", length=12, columnDefinition="VARCHAR(12)")
	private String quotaNo;

	/** 
	 * 放款帳號<p/>
	 * 101/05/11新增
	 */
	@Column(name="LOANNO", length=14, columnDefinition="VARCHAR(14)")
	private String loanNo;

	/** 
	 * 資料日期<p/>
	 * 101/02/17補充說明<br/>
	 *  新增：<br/>
	 *  若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 *  刪除：<br/>
	 *  若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 */
	@Column(name="LNDATADATE", columnDefinition="TIMESTAMP")
	private Date lnDataDate;

	/** 是否於本次覆審 **/
	@Column(name="YNREVIEW", length=1, columnDefinition="VARCHAR(1)")
	private String ynReview;

	/** 
	 * 前次覆審日期<p/>
	 * (團貸案)
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="DATEOFREVIEW", columnDefinition="DATE")
	private Date dateOfReview;

	/** 
	 * 額度性質<p/>
	 * 海外：無??<br/>
	 *  國內：項目代碼：*註1
	 */
	@Column(name="QUOTATYPE", length=1, columnDefinition="VARCHAR(1)")
	private String quotaType;

	/** 授信科目(代碼) **/
	@Column(name="SUBJECTNO", length=4, columnDefinition="VARCHAR(4)")
	private String subjectNo;

	/** 授信科目 **/
	@Column(name="SUBJECTNAME", length=60, columnDefinition="VARCHAR(60)")
	private String subjectName;

	/** 會計科目 **/
	@Column(name="ACTCD", length=8, columnDefinition="VARCHAR(8)")
	private String actcd;

	/** 案號 **/
	@Column(name="PRIVATENO", length=60, columnDefinition="VARCHAR(60)")
	private String privateNo;

	/** 額度(幣別) **/
	@Column(name="QUOTACURR", length=3, columnDefinition="VARCHAR(3)")
	private String quotaCurr;

	/** 
	 * 額度(匯率)<p/>
	 * 100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 */
	@Column(name="QUOTARATE", columnDefinition="DECIMAL(12,8)")
	private BigDecimal quotaRate;

	/** 額度(金額) **/
	@Column(name="QUOTAAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal quotaAmt;

	/** 前日結欠餘額(幣別) **/
	@Column(name="BALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String balCurr;

	/** 
	 * 前日結欠餘額(匯率)<p/>
	 * 100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 */
	@Column(name="BALRATE", columnDefinition="DECIMAL(12,8)")
	private BigDecimal balRate;

	/** 前日結欠餘額(金額) **/
	@Column(name="BALAMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal balAmt;

	/** 
	 * 循環註記<p/>
	 * Y/N
	 */
	@Column(name="REVOLVE", length=1, columnDefinition="VARCHAR(1)")
	private String reVolve;

	/** 動用期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name="USEFDATE", columnDefinition="DATE")
	private Date useFDate;

	/** 動用期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name="USEEDATE", columnDefinition="DATE")
	private Date useEDate;

	/** 授信期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name="LOANFDATE", columnDefinition="DATE")
	private Date loanFDate;

	/** 授信期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name="LOANEDATE", columnDefinition="DATE")
	private Date loanEDate;

	/** 子戶額度(幣別) **/
	@Column(name="SQUOTACURR", length=3, columnDefinition="VARCHAR(3)")
	private String sQuotaCurr;

	/** 子戶額度(金額) **/
	@Column(name="SQUOTAAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal sQuotaAmt;

	/** 子戶餘額(幣別) **/
	@Column(name="SBALCURR", length=3, columnDefinition="VARCHAR(3)")
	private String sBalCurr;

	/** 子戶餘額(金額) **/
	@Column(name="SBALAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal sBalAmt;

	/** 額度建立日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF020CRTDATE", columnDefinition="DATE")
	private Date LNF020CrtDate;

	/** 帳務建立日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF030CRTDATE", columnDefinition="DATE")
	private Date LNF030CrtDate;

	/** 
	 * 產品種類<p/>
	 * 海外：無<br/>
	 *  國內：項目代碼：*註2
	 */
	@Column(name="LNTYPE", length=2, columnDefinition="VARCHAR(2)")
	private String lnType;

	/** 逾期天數 **/
	@Column(name="OVERDUEDATE", columnDefinition="DECIMAL(4,0)")
	private Integer overDueDate;

	/** 
	 * 融資業務分類<p/>
	 * 海外：<br/>
	 *  國內：項目代碼：*註3
	 */
	@Column(name="LNBUSINESS", length=1, columnDefinition="VARCHAR(1)")
	private String lnBusiness;

	/** 擔保品類別 **/
	@Column(name="GUARANTEEKIND", length=30, columnDefinition="VARCHAR(30)")
	private String guaranteeKind;

	/** 
	 * 擔保品名稱<p/>
	 * 3000個全型字
	 */
	@Column(name="GUARANTEENAME", length=9000, columnDefinition="VARCHAR(9000)")
	private String guaranteeName;

	/** 勘估值(幣別) **/
	@Column(name="ESTCURR", length=3, columnDefinition="VARCHAR(3)")
	private String estCurr;

	/** 勘估值(金額) **/
	@Column(name="ESTAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal estAmt;

	/** 放款值(幣別) **/
	@Column(name="LOANCURR", length=3, columnDefinition="VARCHAR(3)")
	private String loanCurr;

	/** 放款值(金額) **/
	@Column(name="LOANAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal loanAmt;

	/** 
	 * 重要敘做條件摘要<p/>
	 * 1000個全型字
	 */
	@Column(name="MAJORMEMO", length=3000, columnDefinition="VARCHAR(3000)")
	private String majorMemo;

	/** 
	 * 共同借款人<p/>
	 * 64個全型字
	 */
	@Column(name="COBORROWER", length=256, columnDefinition="VARCHAR(256)")
	private String coBorrower;

	/** 
	 * 保證人<p/>
	 * 64個全型字
	 */
	@Column(name="GUARANTOR", length=1536, columnDefinition="VARCHAR(1536)")
	private String guarantor;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;

	/**
	 * 保證編號
	 * (LNF034_LC_NO)
	 */
	@Column(name = "LCNO", length = 20, columnDefinition = "CHAR(20)")
	private String lcNo;
	
	/**
	 * 聯貸型態{1:同業聯貸主辦, 2:同業聯貸參貸, 3:自行聯貸或空白}
	 */
	@Column(name = "SYNDTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String syndType;
	
	/** 履保H類買賣價款(金額) **/
	@Column(name="DOCKINDP_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal docKindP_amt;
	
	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得額度序號 **/
	public String getQuotaNo() {
		return this.quotaNo;
	}
	/** 設定額度序號 **/
	public void setQuotaNo(String value) {
		this.quotaNo = value;
	}

	/** 
	 * 取得放款帳號<p/>
	 * 101/05/11新增
	 */
	public String getLoanNo() {
		return this.loanNo;
	}
	/**
	 *  設定放款帳號<p/>
	 *  101/05/11新增
	 **/
	public void setLoanNo(String value) {
		this.loanNo = value;
	}

	/** 
	 * 取得資料日期<p/>
	 * 101/02/17補充說明<br/>
	 *  新增：<br/>
	 *  若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 *  刪除：<br/>
	 *  若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 */
	public Date getLnDataDate() {
		return this.lnDataDate;
	}
	/**
	 *  設定資料日期<p/>
	 *  101/02/17補充說明<br/>
	 *  新增：<br/>
	 *  若為【產生所有授信資料】所產生時填入日期；若為【手動新增】所產生時則此欄為空。<br/>
	 *  刪除：<br/>
	 *  若為【刪除所有授信資料】時，只刪除本欄不為空的資料(即不含手動新增的資料)；若為【手動新增】所產生的資料需透過【手動刪除】才能刪除。
	 **/
	public void setLnDataDate(Date value) {
		this.lnDataDate = value;
	}

	/** 取得是否於本次覆審 **/
	public String getYnReview() {
		return this.ynReview;
	}
	/** 設定是否於本次覆審 **/
	public void setYnReview(String value) {
		this.ynReview = value;
	}

	/** 
	 * 取得前次覆審日期<p/>
	 * (團貸案)
	 */
	public Date getDateOfReview() {
		return this.dateOfReview;
	}
	/**
	 *  設定前次覆審日期<p/>
	 *  (團貸案)
	 **/
	public void setDateOfReview(Date value) {
		this.dateOfReview = value;
	}

	/** 
	 * 取得額度性質<p/>
	 * 海外：無??<br/>
	 *  國內：項目代碼：*註1
	 */
	public String getQuotaType() {
		return this.quotaType;
	}
	/**
	 *  設定額度性質<p/>
	 *  海外：無??<br/>
	 *  國內：項目代碼：*註1
	 **/
	public void setQuotaType(String value) {
		this.quotaType = value;
	}

	/** 取得授信科目(代碼) **/
	public String getSubjectNo() {
		return this.subjectNo;
	}
	/** 設定授信科目(代碼) **/
	public void setSubjectNo(String value) {
		this.subjectNo = value;
	}

	/** 取得授信科目 **/
	public String getSubjectName() {
		return this.subjectName;
	}
	/** 設定授信科目 **/
	public void setSubjectName(String value) {
		this.subjectName = value;
	}

	/** 取得會計科目 **/
	public String getActcd() {
		return this.actcd;
	}
	/** 設定會計科目 **/
	public void setActcd(String value) {
		this.actcd = value;
	}

	/** 取得案號 **/
	public String getPrivateNo() {
		return this.privateNo;
	}
	/** 設定案號 **/
	public void setPrivateNo(String value) {
		this.privateNo = value;
	}

	/** 取得額度(幣別) **/
	public String getQuotaCurr() {
		return this.quotaCurr;
	}
	/** 設定額度(幣別) **/
	public void setQuotaCurr(String value) {
		this.quotaCurr = value;
	}

	/** 
	 * 取得額度(匯率)<p/>
	 * 100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 */
	public BigDecimal getQuotaRate() {
		return this.quotaRate;
	}
	/**
	 *  設定額度(匯率)<p/>
	 *  100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 **/
	public void setQuotaRate(BigDecimal value) {
		this.quotaRate = value;
	}

	/** 取得額度(金額) **/
	public BigDecimal getQuotaAmt() {
		return this.quotaAmt;
	}
	/** 設定額度(金額) **/
	public void setQuotaAmt(BigDecimal value) {
		this.quotaAmt = value;
	}

	/** 取得前日結欠餘額(幣別) **/
	public String getBalCurr() {
		return this.balCurr;
	}
	/** 設定前日結欠餘額(幣別) **/
	public void setBalCurr(String value) {
		this.balCurr = value;
	}

	/** 
	 * 取得前日結欠餘額(匯率)<p/>
	 * 100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 */
	public BigDecimal getBalRate() {
		return this.balRate;
	}
	/**
	 *  設定前日結欠餘額(匯率)<p/>
	 *  100/11/11新增<br/>
	 *  國內：匯率檔ELF337(RATETBL)<br/>
	 *  折台幣匯率<br/>
	 *  海外：匯率檔(DW_FXRTHOVS)<br/>
	 *  折本位幣匯率
	 **/
	public void setBalRate(BigDecimal value) {
		this.balRate = value;
	}

	/** 取得前日結欠餘額(金額) **/
	public BigDecimal getBalAmt() {
		return this.balAmt;
	}
	/** 設定前日結欠餘額(金額) **/
	public void setBalAmt(BigDecimal value) {
		this.balAmt = value;
	}

	/** 
	 * 取得循環註記<p/>
	 * Y/N
	 */
	public String getReVolve() {
		return this.reVolve;
	}
	/**
	 *  設定循環註記<p/>
	 *  Y/N
	 **/
	public void setReVolve(String value) {
		this.reVolve = value;
	}

	/** 取得動用期間(起) **/
	public Date getUseFDate() {
		return this.useFDate;
	}
	/** 設定動用期間(起) **/
	public void setUseFDate(Date value) {
		this.useFDate = value;
	}

	/** 取得動用期間(迄) **/
	public Date getUseEDate() {
		return this.useEDate;
	}
	/** 設定動用期間(迄) **/
	public void setUseEDate(Date value) {
		this.useEDate = value;
	}

	/** 取得授信期間(起) **/
	public Date getLoanFDate() {
		return this.loanFDate;
	}
	/** 設定授信期間(起) **/
	public void setLoanFDate(Date value) {
		this.loanFDate = value;
	}

	/** 取得授信期間(迄) **/
	public Date getLoanEDate() {
		return this.loanEDate;
	}
	/** 設定授信期間(迄) **/
	public void setLoanEDate(Date value) {
		this.loanEDate = value;
	}

	/** 取得子戶額度(幣別) **/
	public String getSQuotaCurr() {
		return this.sQuotaCurr;
	}
	/** 設定子戶額度(幣別) **/
	public void setSQuotaCurr(String value) {
		this.sQuotaCurr = value;
	}

	/** 取得子戶額度(金額) **/
	public BigDecimal getSQuotaAmt() {
		return this.sQuotaAmt;
	}
	/** 設定子戶額度(金額) **/
	public void setSQuotaAmt(BigDecimal value) {
		this.sQuotaAmt = value;
	}

	/** 取得子戶餘額(幣別) **/
	public String getSBalCurr() {
		return this.sBalCurr;
	}
	/** 設定子戶餘額(幣別) **/
	public void setSBalCurr(String value) {
		this.sBalCurr = value;
	}

	/** 取得子戶餘額(金額) **/
	public BigDecimal getSBalAmt() {
		return this.sBalAmt;
	}
	/** 設定子戶餘額(金額) **/
	public void setSBalAmt(BigDecimal value) {
		this.sBalAmt = value;
	}

	/** 取得額度建立日期 **/
	public Date getLNF020CrtDate() {
		return this.LNF020CrtDate;
	}
	/** 設定額度建立日期 **/
	public void setLNF020CrtDate(Date value) {
		this.LNF020CrtDate = value;
	}

	/** 取得帳務建立日期 **/
	public Date getLNF030CrtDate() {
		return this.LNF030CrtDate;
	}
	/** 設定帳務建立日期 **/
	public void setLNF030CrtDate(Date value) {
		this.LNF030CrtDate = value;
	}

	/** 
	 * 取得產品種類<p/>
	 * 海外：無<br/>
	 *  國內：項目代碼：*註2
	 */
	public String getLnType() {
		return this.lnType;
	}
	/**
	 *  設定產品種類<p/>
	 *  海外：無<br/>
	 *  國內：項目代碼：*註2
	 **/
	public void setLnType(String value) {
		this.lnType = value;
	}

	/** 取得逾期天數 **/
	public Integer getOverDueDate() {
		return this.overDueDate;
	}
	/** 設定逾期天數 **/
	public void setOverDueDate(Integer value) {
		this.overDueDate = value;
	}

	/** 
	 * 取得融資業務分類<p/>
	 * 海外：<br/>
	 *  國內：項目代碼：*註3
	 */
	public String getLnBusiness() {
		return this.lnBusiness;
	}
	/**
	 *  設定融資業務分類<p/>
	 *  海外：<br/>
	 *  國內：項目代碼：*註3
	 **/
	public void setLnBusiness(String value) {
		this.lnBusiness = value;
	}

	/** 取得擔保品類別 **/
	public String getGuaranteeKind() {
		return this.guaranteeKind;
	}
	/** 設定擔保品類別 **/
	public void setGuaranteeKind(String value) {
		this.guaranteeKind = value;
	}

	/** 
	 * 取得擔保品名稱<p/>
	 * 64個全型字
	 */
	public String getGuaranteeName() {
		return this.guaranteeName;
	}
	/**
	 *  設定擔保品名稱<p/>
	 *  64個全型字
	 **/
	public void setGuaranteeName(String value) {
		this.guaranteeName = value;
	}

	/** 取得勘估值(幣別) **/
	public String getEstCurr() {
		return this.estCurr;
	}
	/** 設定勘估值(幣別) **/
	public void setEstCurr(String value) {
		this.estCurr = value;
	}

	/** 取得勘估值(金額) **/
	public BigDecimal getEstAmt() {
		return this.estAmt;
	}
	/** 設定勘估值(金額) **/
	public void setEstAmt(BigDecimal value) {
		this.estAmt = value;
	}

	/** 取得放款值(幣別) **/
	public String getLoanCurr() {
		return this.loanCurr;
	}
	/** 設定放款值(幣別) **/
	public void setLoanCurr(String value) {
		this.loanCurr = value;
	}

	/** 取得放款值(金額) **/
	public BigDecimal getLoanAmt() {
		return this.loanAmt;
	}
	/** 設定放款值(金額) **/
	public void setLoanAmt(BigDecimal value) {
		this.loanAmt = value;
	}

	/** 
	 * 取得重要敘做條件摘要
	 */
	public String getMajorMemo() {
		return this.majorMemo;
	}
	/**
	 *  設定重要敘做條件摘要
	 **/
	public void setMajorMemo(String value) {
		this.majorMemo = value;
	}

	/** 
	 * 取得共同借款人<p/>
	 * 64個全型字
	 */
	public String getCoBorrower() {
		return this.coBorrower;
	}
	/**
	 *  設定共同借款人<p/>
	 *  64個全型字
	 **/
	public void setCoBorrower(String value) {
		this.coBorrower = value;
	}

	/** 
	 * 取得保證人<p/>
	 * 64個全型字
	 */
	public String getGuarantor() {
		return this.guarantor;
	}
	/**
	 *  設定保證人<p/>
	 *  64個全型字
	 **/
	public void setGuarantor(String value) {
		this.guarantor = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/**
	 * 取得保證編號
	 */
	public String getLcNo() {
		return lcNo;
	}

	/**
	 * 設定保證編號
	 */
	public void setLcNo(String lcNo) {
		this.lcNo = lcNo;
	}
	
	/**
	 * 取得聯貸型態{1:同業聯貸主辦, 2:同業聯貸參貸, 3:自行聯貸或空白}
	 */
	public String getSyndType() {
		return syndType;
	}
	/**
	 * 設定聯貸型態{1:同業聯貸主辦, 2:同業聯貸參貸, 3:自行聯貸或空白}
	 */
	public void setSyndType(String syndType) {
		this.syndType = syndType;
	}
	/** 取得履保H類買賣價款(金額) **/
	public BigDecimal getDocKindP_amt() {
		return docKindP_amt;
	}
	/** 設定履保H類買賣價款(金額) **/
	public void setDocKindP_amt(BigDecimal docKindP_amt) {
		this.docKindP_amt = docKindP_amt;
	}
}
      