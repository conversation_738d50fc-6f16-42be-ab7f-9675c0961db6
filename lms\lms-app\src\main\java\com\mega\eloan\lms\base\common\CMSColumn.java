package com.mega.eloan.lms.base.common;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;


/**
 * <pre>
 * CMS自定義欄位性質
 * </pre>
 * 
 * @since 2012/5/28
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/5/28,<PERSON>,new
 *          </ul>
 */
@Target({METHOD, FIELD}) 
@Retention(RUNTIME)
public @interface CMSColumn {
	int dateType() default CMSTypes.DATETYPE1;
	boolean isErase() default false;
}
