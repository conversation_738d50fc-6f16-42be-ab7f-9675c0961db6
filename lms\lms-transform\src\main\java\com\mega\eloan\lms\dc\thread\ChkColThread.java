package com.mega.eloan.lms.dc.thread;

import com.mega.eloan.lms.dc.action.ColumnTruncate;
import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.ConfigData;

/**
 * <pre>
 * ChkColThread
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class ChkColThread extends Thread {

	private String dbType = "";
	private String schema = "";
	private String path = "";
	private ConfigData config = null;

	public ChkColThread() {
		super();
	}

	/**
	 * Constructor
	 * 
	 * @param dbType
	 *            String:連結的DB
	 * @param schema
	 *            String:連結的 schema
	 * @param path
	 *            String:要檢核的文字檔路徑辨識碼Ex:db2 or 分行名
	 * @param pps
	 *            :Properties
	 */
	public ChkColThread(String dbType, String schema, String path) {
		this.dbType = dbType;
		this.schema = schema;
		this.path = path;
	}

	public void run() {
		ColumnTruncate ct = new ColumnTruncate();
		ct.setConfigData(config);
		ct.doChkColumn(this.dbType, this.schema, this.path);
	}

	public void chkColumn(ConfigData config) {
		try {
			this.setConfig(config);
			this.run();
		} catch (DCException e) {
			throw new DCException("ChkColumn 時產生錯誤...", e);
		}
	}

	/**
	 * set the config
	 * 
	 * @param config
	 *            the config to set
	 */
	public void setConfig(ConfigData config) {
		if (config != null) {
			this.config = config;
		}
	}

}
