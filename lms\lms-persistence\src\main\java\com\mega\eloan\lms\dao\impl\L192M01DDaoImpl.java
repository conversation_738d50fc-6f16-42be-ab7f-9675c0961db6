package com.mega.eloan.lms.dao.impl;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.mega.eloan.lms.dao.L192M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01D;;



@Repository
public class L192M01DDaoImpl extends LMSJpaDao<L192M01D, String> implements
L192M01DDao {

	@Override
	public int deleteByMeta(L192M01A meta) {
		Query query = entityManager
				.createNamedQuery("l192m01d.deleteByMainId");
		query.setParameter("MAINID", meta.getMainId());
		return query.executeUpdate();
	}

}
