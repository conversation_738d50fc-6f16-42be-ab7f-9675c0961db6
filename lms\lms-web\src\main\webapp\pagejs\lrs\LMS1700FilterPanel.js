$(function() {
	$("select#conFlag").find("option[value=1]").before(
		"<option value=''>" + i18n.def.comboSpace
		+ "</option>");

	$.ajax({
		type: "POST",
		handler: "lms1800formhandler",
		data: {
			formAction: "queryBranch"
		},
	}).done(function(obj){
		var _addSpace = true;
		if (obj.itemOrder.length == 1) {
			//_addSpace = false;
		}
		$.each(obj.itemOrder, function(idx, brNo) {
			var currobj = {};
			var brName = obj.item[brNo];
			currobj[brNo] = brName;

			$("#ownBrId").setItems(
				{
					item: currobj,
					format: "{value} {key}",
					clear: false,
					space: (_addSpace ? (idx == 0)
						: false)
				});
		});
	});
});

var FilterAction = {
	formId: "#filterForm",
	gridId: "#gridview",
	openBox: function() {
		var $form = $(this.formId).reset();
		//$("#checkDateStart,#checkDateEnd").val(API.getToday());
		$("#filterBox")
			.thickbox(
				{
					//query=查詢
					title: i18n.def["query"],
					width: 400,
					height: 260,
					modal: true,
					i18n: i18n.def,
					readOnly: false,
					align: "center",
					valign: "bottom",
					buttons: {
						"sure": function() {
							if (!$form.valid()) {
								return false;
							}
							var start = $("#checkDateStart")
								.val();
							var end = $("#checkDateEnd")
								.val();
							//當兩個都為空的時候才去檢查起迄日
							if (start != "" && end != "") {
								if (start > end) {
									//EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
									CommonAPI
										.showErrorMessage(i18n
											.msg(
												'EFD3026')
											.replace(
												/\$\\{colName\\}/,
												i18n.lms2415v01['C241M01a.retrialDate']
												+ " "));
									return false;
								}
							}
							FilterAction
								.reloadGrid(JSON
									.stringify($form
										.serializeData()));
							$.thickbox.close();
						},
						"cancel": function() {
							$.thickbox.close();
						}
					}
				});
	},
	/**更新grid
	 *
	 * @param {Object} data 查詢條件
	 */
	reloadGrid: function(data) {
		$(this.gridId).jqGrid("setGridParam", {
			postData: {
				formAction: "queryL170M01A",
				docStatus: viewstatus,
				filetData: data
			},
			page: 1,
			search: true
		}).trigger("reloadGrid");
	}
};


