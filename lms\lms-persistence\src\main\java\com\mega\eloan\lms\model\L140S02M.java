/* 
 * L140S02M.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 整批房貸地號檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140S02M", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L140S02M extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 土地坐落區-縣市 **/
	@Size(max=10)
	@Column(name="LANDCITY", length=10, columnDefinition="VARCHAR(10)")
	private String landCity;

	/** 土地坐落區-區域 **/
	@Size(max=3)
	@Column(name="LANDAREA", length=3, columnDefinition="VARCHAR(3)")
	private String landArea;

	/** 土地坐落區-大段 **/
	@Size(max=15)
	@Column(name="LANDPART1", length=15, columnDefinition="VARCHAR(15)")
	private String landpart1;

	/** 土地坐落區-小段 **/
	@Size(max=15)
	@Column(name="LANDPART2", length=15, columnDefinition="VARCHAR(15)")
	private String landpart2;

	/** 土地坐落區 **/
	@Size(max=100)
	@Column(name="LANDADDRESS", length=100, columnDefinition="VARCHAR(100)")
	private String landAddress;

	/** 地號 **/
	@Size(max=10)
	@Column(name="LANDNO", length=10, columnDefinition="VARCHAR (10)")
	private String landNo;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得土地坐落區-縣市 **/
	public String getLandCity() {
		return this.landCity;
	}
	/** 設定土地坐落區-縣市 **/
	public void setLandCity(String value) {
		this.landCity = value;
	}

	/** 取得土地坐落區-區域 **/
	public String getLandArea() {
		return this.landArea;
	}
	/** 設定土地坐落區-區域 **/
	public void setLandArea(String value) {
		this.landArea = value;
	}

	/** 取得土地坐落區-大段 **/
	public String getLandpart1() {
		return this.landpart1;
	}
	/** 設定土地坐落區-大段 **/
	public void setLandpart1(String value) {
		this.landpart1 = value;
	}

	/** 取得土地坐落區-小段 **/
	public String getLandpart2() {
		return this.landpart2;
	}
	/** 設定土地坐落區-小段 **/
	public void setLandpart2(String value) {
		this.landpart2 = value;
	}

	/** 取得土地坐落區 **/
	public String getLandAddress() {
		return this.landAddress;
	}
	/** 設定土地坐落區 **/
	public void setLandAddress(String value) {
		this.landAddress = value;
	}

	/** 取得地號 **/
	public String getLandNo() {
		return this.landNo;
	}
	/** 設定地號 **/
	public void setLandNo(String value) {
		this.landNo = value;
	}
}
