/* 
 * EAIService.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.tej.service;

import java.util.List;
import java.util.Map;

import tw.com.iisi.cap.exception.CapMessageException;

/**
 * <pre>
 * TEJ
 * </pre>
 * 
 * @since 2018/6/14
 * <AUTHOR>
 * @version <ul>
 *          <li>2018/6/14,007623,new
 *          </ul>
 */
public interface TEJService {

	/**
	 * 以股票代號查詢TCRI
	 * 
	 * @param stockNo
	 *            股票代號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findCrmtabByStockNo(String stockNo)
			throws CapMessageException;

	/**
	 * 用股票代號查詢是否已撤銷公開發行
	 * 
	 * @param stockNo
	 * @return
	 */
	String findRevokePublicOffering(String stockNo) throws CapMessageException;

	/**
	 * 以公司統編查詢基本資料
	 * 
	 * @param custId
	 * @return
	 * @throws CapMessageException
	 */
	public Map<String, Object> findCrmstdByCustId(String custId)
			throws CapMessageException;

	/**
	 * 查詢行員30天內是否有列印TCRI彙總表
	 * 
	 * @param userId
	 *            行員代號
	 * @param custId
	 *            客戶統一編號
	 * @return Map<String, Object>
	 */
	public Map<String, Object> findUserLogByUserIdRemark(String userId,
			String custId) throws CapMessageException;

	public List<Map<String, Object>> findAllTCRIRatingDataByCoId(String coId)
			throws CapMessageException;

	public List<Map<String, Object>> findCompanyGreatEventDataGroupByCoIdAndYymmdd(
			String coIdPlusYymmdd) throws CapMessageException;

	public List<Map<String, Object>> findCompanyGreatEventData(String coId,
			String yymmdd, int seq) throws CapMessageException;

	public List<Map<String, Object>> findCrmstdAllByPubDate2IsEmpty()
			throws CapMessageException;
}
