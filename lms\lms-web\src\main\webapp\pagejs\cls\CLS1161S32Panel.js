var panelAction = {
    handler: 'cls1161m03formhandler',
    gridhandler: 'cls1161gridhandler',
    gridAction: 'queryC160S03A',
    grid: null,
    init: function(){
    },
    build: function(){
        panelAction.grid = $("#gridview").iGrid({
            handler: panelAction.gridhandler,
            height: 400,
            action: panelAction.gridAction,
            sortname: 'seqNo',
            sortorder: "asc",
            rowNum: 20,
            rownumbers: true,
            colModel: [{
                name: 'oid', hidden: true //是否隱藏
            }, {
            	name: 'mainId', hidden: true //是否隱藏
            }, {
                colHeader: i18n.cls1161m03["C160S03A.custId_s"], //身分證統編
                align: "left",
                width: 80,
                name: 'custId_s', //身分證統編
                formatter: 'click',
                onclick: function(cellvalue, options, rowObject){
                	panelAction.editC160S03A(rowObject);
                },
                sortable: true //是否允許排序
            }, {
                colHeader: ' ', //身分證統編
                align: "left",
                width: 10,
                name: 'dupNo_s', //身分證統編重複碼
                sortable: false
            }, {
                colHeader: ' ', //身分證統編
                align: "left",
                width: 10,
                name: 'chkYN', //身分證統編重複碼
                sortable: false
            }],
            ondblClickRow: function(rowid){
                
            }
        });
       
        $("#findBaseDataIdBt").click(function(){
        	$("#findBaseDataId").removeAttr("disabled").removeAttr("readonly");
        	
    		var search_custId = $("#findBaseDataId").val();
    		
    		var my_post_data = {
    			formAction : panelAction.gridAction
    			, search_custId: search_custId
    		};
    		
    		panelAction.grid .jqGrid("setGridParam", {
    			postData : my_post_data,
    			search : true
    		}).trigger("reloadGrid");			
    	});
    },
    editC160S03A: function(rowObject){
    	alert(rowObject.oid);
    }
};


$(function(){
    panelAction.build();
    panelAction.init();
});
