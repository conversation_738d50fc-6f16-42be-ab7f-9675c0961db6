/* 
 *  LMS140MM01FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.handler.form;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.RealEstateLoanUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMS140MM01Page;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.CentralBankControlService;
import com.mega.eloan.lms.base.service.LMS140MService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.mfaloan.bean.MISLN30;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELF500Service;
import com.mega.eloan.lms.mfaloan.service.MisELF517Service;
import com.mega.eloan.lms.mfaloan.service.MisLNF030Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140MM1A;
import com.mega.eloan.lms.model.L140MM1B;
import com.mega.eloan.lms.model.L140S05A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 央行註記異動作業
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms140mm01formhandler")
@DomainClass(L140MM1A.class)
public class LMS140MM01FormHandler extends AbstractFormHandler {

	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;
	
	@Resource
	MisLNF030Service misLNF030Service;
	
	@Resource
	BranchService branchService;

	@Resource
	LMS140MService lms140mService;

	@Resource
	MisELF500Service misELF500Service;

	@Resource
	UserInfoService userInfoService;
	
	@Resource
	MisELF517Service misElf517Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	CentralBankControlService centralBankControlService;
	
	/**
	 * 新增L140MM1A 央行註記異動
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl140mm1a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String l140mm1aMainid = "";
		String editType = params.getString("editType");
		L140MM1A l140mm1a = new L140MM1A();
		l140mm1a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
		l140mm1a.setOwnBrId(user.getUnitNo());

		l140mm1aMainid = IDGenerator.getUUID();

		l140mm1a.setMainId(l140mm1aMainid);
		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l140mm1a.setTxCode(txCode);
		// UPGRADE: 待確認，URL是否正確
		l140mm1a.setDocURL(params.getString("docUrl"));
		l140mm1a.setDeletedTime(CapDate.getCurrentTimestamp());
		l140mm1a.setTypCd(UtilConstants.Casedoc.typCd.DBU);
		l140mm1a.setCustId(params.getString("custId"));
		l140mm1a.setDupNo(params.getString("dupNo"));
		l140mm1a.setCustName(params.getString("custName"));
		l140mm1a.setCntrNo(params.getString("cntrNo"));
		l140mm1a.setSDate(CapDate.parseDate(params.getString("sDate")));
		l140mm1a.setEditType(params.getString("editType"));
		String selectVersion = params.getString("selectVersion");
		selectVersion = UtilConstants.L140m01mVersion.VERSION_OLD.equals(selectVersion) ? "" : selectVersion;
		
		if("C".equals(editType)){
			lms140mService.addL140M01MForBrandNewCntrNo(l140mm1a, RealEstateLoanUtil.latestVersion);
		}
		
		if("U".equals(editType)){
			lms140mService.addL140M01M(l140mm1a, selectVersion);
		}

		CapAjaxFormResult result = new CapAjaxFormResult();
		return result.set(EloanConstants.OID, l140mm1a.getOid());
	}

	/**
	 * 查詢L140MM1A 央行註記異動
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL140mm1a(PageParameters params)
			throws CapException {
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.OID);
		if (!Util.isEmpty(oid)) {
			L140MM1A l140mm1a = lms140mService.findModelByOid(L140MM1A.class,
					oid);
			String tMainId = Util.trim(l140mm1a.getMainId());
			L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class,
					tMainId);
			result = formatResultShow(result, l140mm1a, page);
			// 將L140M01M用putAll及DataParse.toResult填到result
			if (l140m01m == null) {
				l140m01m = new L140M01M();
			}
			//引進 elf517 最新資料
			if(Util.isEmpty(l140m01m.getRemainLoanYN())){
				this.lms140mService.setUnsoldHouseFinancingDataForCentralBankMarkMaintenance(lmsService.call_elf517(l140mm1a.getCntrNo()), l140m01m);
			}
			
			this.lmsService.setMortgageDetailCodeForRealEstateBusinessRule(result, l140m01m.getCbcCase(), l140m01m.getRealEstateLoanLimitReason());
			
			result.set("totalTimeVal", l140m01m.getTimeVal() == null ? BigDecimal.ZERO : l140m01m.getTimeVal());
			result.set("versionSelectedName", this.codeTypeService.findByCodeType("L140M01M_VERSION").get(l140m01m.getVersion()));
			result.putAll(DataParse.toResult(l140m01m, true));
			result.set("editType", l140mm1a.getEditType());
			
		} else {
			// 開啟新案帶入起案的分行和目前文件狀態
			result.set(
					"docStatus",
					this.getMessage("docStatus."
							+ CreditDocStatusEnum.海外_編製中.getCode()));
			result.set("ownBrId", user.getUnitNo());
			result.set(
					"ownBrName",
					StrUtils.concat(" ",
							branchService.getBranchName(user.getUnitNo())));
			result.set("docStatusVal", CreditDocStatusEnum.海外_編製中.getCode());
		}
		return result;
	}

	// /**
	// * 呈主管覆核(呈主管 主管覆核 拆2個method)
	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = params.getString(EloanConstants.MAIN_OID);
		L140MM1A l140mm1a = (L140MM1A) lms140mService.findModelByOid(
				L140MM1A.class, oid);
		String[] formSelectBoss = params.getStringArray("selectBoss");

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		if (!Util.isEmpty(formSelectBoss)) {

			String manager = Util.trim(params.getString("manager"));
			List<L140MM1B> models = (List<L140MM1B>) lms140mService
					.findListByMainId(L140MM1B.class, l140mm1a.getMainId());
			if (!models.isEmpty()) {
				lms140mService.deleteL140mm1bs(models, false);
			}
			List<L140MM1B> l140mm1bs = new ArrayList<L140MM1B>();
			for (String people : formSelectBoss) {
				L140MM1B l140mm1b = new L140MM1B();
				l140mm1b.setCreator(user.getUserId());
				l140mm1b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm1b.setMainId(l140mm1a.getMainId());
				l140mm1b.setBranchType(user.getUnitType());
				l140mm1b.setBranchId(user.getUnitNo());
				// L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理
				l140mm1b.setStaffJob(UtilConstants.STAFFJOB.授信主管L3);
				l140mm1b.setStaffNo(people);
				l140mm1bs.add(l140mm1b);
			}
			L140MM1B managerL140mm1b = new L140MM1B();
			managerL140mm1b.setCreator(user.getUserId());
			managerL140mm1b.setCreateTime(CapDate.getCurrentTimestamp());
			managerL140mm1b.setMainId(l140mm1a.getMainId());
			managerL140mm1b.setStaffJob(UtilConstants.STAFFJOB.單位授權主管L5);
			managerL140mm1b.setStaffNo(manager);
			managerL140mm1b.setBranchType(user.getUnitType());
			managerL140mm1b.setBranchId(user.getUnitNo());
			l140mm1bs.add(managerL140mm1b);
			L140MM1B apprL140mm1b = new L140MM1B();
			apprL140mm1b.setCreator(user.getUserId());
			apprL140mm1b.setCreateTime(CapDate.getCurrentTimestamp());
			apprL140mm1b.setMainId(l140mm1a.getMainId());
			apprL140mm1b.setStaffJob(UtilConstants.STAFFJOB.經辦L1);
			apprL140mm1b.setStaffNo(user.getUserId());
			apprL140mm1b.setBranchType(user.getUnitType());
			apprL140mm1b.setBranchId(user.getUnitNo());
			l140mm1bs.add(apprL140mm1b);
			lms140mService.saveL140mm1bList(l140mm1bs);
		}
		Boolean upMis = false;
		L140MM1B l140mm1bL4 = new L140MM1B();
		// 如果有這個key值表示是輸入chekDate核准日期
		if (params.containsKey("checkDate")) {
			l140mm1a.setApprover(user.getUserId());
			l140mm1a.setApproveTime(CapDate.convertStringToTimestamp(params
					.getString("checkDate") + " 00:00:00"));
			upMis = true;
			L140MM1B l140mm1b = lms140mService.findL140mm1b(
					l140mm1a.getMainId(), user.getUnitType(), user.getUnitNo(),
					user.getUserId(), UtilConstants.STAFFJOB.執行覆核主管L4);
			if (l140mm1b == null) {
				l140mm1b = new L140MM1B();
				l140mm1b.setCreator(user.getUserId());
				l140mm1b.setCreateTime(CapDate.getCurrentTimestamp());
				l140mm1b.setMainId(l140mm1a.getMainId());
				l140mm1b.setStaffJob(UtilConstants.STAFFJOB.執行覆核主管L4);
				l140mm1b.setStaffNo(user.getUserId());
				l140mm1b.setBranchType(user.getUnitType());
				l140mm1b.setBranchId(user.getUnitNo());
			}
			l140mm1bL4 = l140mm1b;
		}

		if (!Util.isEmpty(l140mm1a)) {
			try {
				// 如果有這值表示非呈主管，要檢查覆核主管和文件最後更新者是否相同
				if (params.containsKey("flowAction")) {
					// 退回部檢查
					if (params.getBoolean("flowAction")) {
						L140MM1B l140mm1b = lms140mService.findL140mm1b(
								l140mm1a.getMainId(), user.getUnitType(),
								user.getUnitNo(), user.getUserId(),
								UtilConstants.STAFFJOB.經辦L1);

						if (l140mm1b != null) {
							// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
							throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
						} else {
							lms140mService.save(l140mm1bL4);
							upMis = true;
						}
					}
				}
				lms140mService.flowAction(l140mm1a.getOid(), l140mm1a,
						params.containsKey("flowAction"),
						params.getAsBoolean("flowAction", false), upMis);
			} catch (FlowException t1) {
				logger.error(
						"[flowAction] lms140mService.flowAction FlowException!!",
						t1);
				throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
			} catch (Throwable t1) {
				logger.error(
						"[flowAction]  lms140mService.flowAction EXCEPTION!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}

		return new CapAjaxFormResult();
	}

	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkData(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 查詢所選銀行的甲級主管、乙級主管清單
		SignEnum[] signs = { SignEnum.首長, SignEnum.單位主管, SignEnum.甲級主管,
				SignEnum.乙級主管 };
		Map<String, String> bossList = userInfoService.findByBrnoAndSignId(
				user.getUnitNo(), signs);
		result.set("bossList", new CapAjaxFormResult(bossList));
		return result;

	}

	/**
	 * 儲存L140MM1A 央行註記異動作業
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */

	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL140mm1a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// lmsService.uploadELLNSEEK(new L120M01A());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		// String custName=Util.trim(params.getString("custName"));

		String tMainid = "";

		String formL160m01a = Util.trim(params.getString("LMS140M01MForm")); // 指定的form
		JSONObject jsonL160m01a = null;
		L140MM1A l140mm1a = null;
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		String showMsg1 = "";
		if (Util.isNotEmpty(oid)) {
			l140mm1a = lms140mService.findModelByOid(L140MM1A.class, oid);
			tMainid = Util.trim(l140mm1a.getMainId());
			l140mm1a.setRandomCode(IDGenerator.getRandomCode());
		}
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS140MM01Page.class);

		l140mm1a.setDeletedTime(null);

		L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class,
				tMainid);
		if(l140m01m==null){
		   l140m01m = new L140M01M();
		   l140m01m.setMainId(l140mm1a.getMainId());
		}
		String validate = null;
		switch (page) {
		case 1:
			jsonL160m01a = JSONObject.fromObject(formL160m01a);
			DataParse.toBean(jsonL160m01a, l140m01m);
			
			this.lmsService.setMortgageDetailCodeAndCleanUnusedField(jsonL160m01a, l140m01m, l140m01m.getVersion());
			
			validate = Util.validateColumnSize(l140m01m, pop, "L140M01M");
			if (validate != null) {
				Map<String, String> param = new HashMap<String, String>();
				param.put("colName", validate);
				throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
			}

			List<String> errList = new ArrayList<String>();
			List<String> showList = new ArrayList<String>();
			
			L140M01A l140m01a = null;
			String errMsg = "";
			if("U".equals(l140mm1a.getEditType())){
				l140m01a = this.lmsService.getL140m01aByCntrNoForNew(l140mm1a.getCntrNo());
				errMsg = this.lmsService.checkVersionByRealEstateNoteRule(l140m01m.getVersion(), l140m01a, l140m01m.getCbcCase(), l140m01m.getLandBuildYN(), l140m01m.getProdClass(), false, l140m01m.getPlusReason(), l140m01m.getRemainLoanYN());
			}
			
			String property = "";
			String quantLoan = "";
			if (!UtilConstants.unitType.授管處.equals(Util.trim(user.getUnitType()))) {
				
				if("U".equals(l140mm1a.getEditType())){
					errMsg += this.lmsService.checkEmptyLandLoan(l140m01m, l140m01a.getCntrNo());
					property = l140m01a.getProPerty();
					L140S05A l140s05a = this.lmsService.findL140s05aByMainId(l140m01a.getMainId());
					quantLoan = l140s05a != null ? l140s05a.getQuant_loan() : "";
				}
				
				this.lmsService.gotoSetType(l140m01m.getVersion(), l140m01m, Util.trim(l140mm1a.getCustId()), errList, showList, false, l140mm1a == null ? "" : l140mm1a.getCntrNo(), property, quantLoan);
				this.centralBankControlService.checkAllVersionL140m01mData(errList, pop, l140m01m.getCbcCase(), l140m01m.getPlusReason(), l140m01m.getAppAmt());
				// J-112-0415 修改餘屋-空地貸款-免計入銀行法72-2報表相關修改，開放營業單位可修改土建融案件
				// this.centralBankControlService.checkIsLandOrConstructionFinancingCase(l140m01m.getLandBuildYN(), l140m01m.getProdClass(), pop, errList);
			}
			
			String tipsMsg = this.lmsService.getTipsMsgForCentralBankMortgageMark(l140m01m.getVersion(), l140m01m.getCbcCase(), l140m01m.getKeepYN(), l140m01m.getActStartDate());
			if (!"".equals(tipsMsg)) {
				showList.add(tipsMsg);
			}
			
			if(!"".equals(errMsg)){
				errList.add(errMsg);
			}
			
			if (errList.size() > 0) {
				throw new CapMessageException(
						StringUtils.join(errList, "<br/>"), getClass());
			} else if (showList.size() > 0) {
				showMsg1 = StringUtils.join(showList, "<br/>");
			}
			lmsService.save(l140m01m);
			lms140mService.save(l140mm1a);
			result.set("randomCode", l140mm1a.getRandomCode());
			break;
		}

		if(l140m01m!=null){
			boolean has_67_or_70 = false;
			if(true){
//				for(Map<String, Object> lnf030_map: misLNF030Service.selaLoanNoByCntrno(l140mm1a.getCntrNo())){
				for(MISLN30 lnf030: misLNF030Service.selBykey(l140mm1a.getCustId(), l140mm1a.getDupNo(), l140mm1a.getCntrNo())){
					if(CrsUtil.is_67(lnf030.getLnf030_loan_class()) || CrsUtil.is_70(lnf030.getLnf030_loan_class())){
						has_67_or_70 = true;
						break;
					}
				}
			}
			Properties prop = MessageBundleScriptCreator.getComponentResource(LMS140MM01Page.class);
			
			if(has_67_or_70 && !LMSUtil.is_cls_prod_67(l140m01m)){
				Map<String, String> plusReasonMap = clsService.get_codeTypeWithOrder("L140M01M_plusReason");
				String plusReason = LMSUtil.getDesc(plusReasonMap, "8");
				throw new CapMessageException(MessageFormat.format(prop.getProperty("pageErr.227"), plusReason), getClass());	
			}
			
			if(!has_67_or_70 && LMSUtil.is_cls_prod_67(l140m01m)){
				Map<String, String> plusReasonMap = clsService.get_codeTypeWithOrder("L140M01M_plusReason");
				String plusReason = LMSUtil.getDesc(plusReasonMap, "8");
				//非以房養老額度，其理由不應選擇「以房養老專案」
				throw new CapMessageException(MessageFormat.format(prop.getProperty("pageErr.229"), plusReason), getClass());
			}

			if("Y".equals(l140m01m.getRemainLoanYN()) && UtilConstants.BankNo.授管處.equals(user.getUnitNo())){
				
				//檢核 建案完工未出售房屋融資註記
				String errorMsg = this.lms140mService.checkUnsoldHouseFinancingDataForCentralBankMarkMaintenance(l140m01m);
				if(StringUtils.isNotEmpty(errorMsg)){
					throw new CapMessageException(errorMsg, getClass());
				}
			}
		}
		
		if (Util.isEmpty(showMsg1)) {
			if (showMsg) {
				showMsg1 = RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功);
			}
		}
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, showMsg1);

		result.set(EloanConstants.OID, CapString.trimNull(l140mm1a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l140mm1a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l140mm1a.getMainId()));
		result.set("showTypCd", this.getMessage("typCd." + l140mm1a.getTypCd()));
		result.set(
				"showCustId",
				CapString.trimNull(l140mm1a.getCustId()) + " "
						+ CapString.trimNull(l140mm1a.getDupNo()) + " "
						+ CapString.trimNull(l140mm1a.getCustName()));
		return result;
	}

	/**
	 * 格式化顯示訊息
	 * 
	 * @param mainId
	 *            央行註記異動作業 mainId
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private CapAjaxFormResult formatResultShow(CapAjaxFormResult result,
			L140MM1A l140mm1a, Integer page) throws CapException {
		String mainId = l140mm1a.getMainId();

		switch (page) {
		case 1:
			result = DataParse.toResult(l140mm1a);
			List<L140MM1B> l140mm1blist = (List<L140MM1B>) lms140mService
					.findListByMainId(L140MM1B.class, mainId);
			if (!Util.isEmpty(l140mm1blist)) {
				// 取得人員職稱 L1. 分行經辦 L3. 分行授信主管 L4. 分行覆核主管 L5. 經副襄理L6. 總行經辦
				// L7.總行主管
				StringBuilder bossId = new StringBuilder("");
				for (L140MM1B l140mm1b : l140mm1blist) {
					// 要加上人員代碼
					String type = Util.trim(l140mm1b.getStaffJob());
					String userId = Util.trim(l140mm1b.getStaffNo());
					String value = Util.trim(lmsService.getUserName(userId));
					if ("L1".equals(type)) {
						result.set("showApprId", userId + " " + value);
					} else if ("L3".equals(type)) {
						bossId.append(bossId.length() > 0 ? "<br/>" : "");
						bossId.append(userId);
						bossId.append(" ");
						bossId.append(value);
					} else if ("L4".equals(type)) {
						result.set("reCheckId", userId + " " + value);
					} else if ("L5".equals(type)) {
						result.set("managerId", userId + " " + value);
					} else if ("L6".equals(type)) {
						result.set("mainApprId", userId + " " + value);
					} else if ("L7".equals(type)) {
						result.set("mainReCheckId", userId + " " + value);
					}
				}
				result.set("bossId", bossId.toString());
			}
			result.set("ownBrName",
					" " + branchService.getBranchName(l140mm1a.getOwnBrId()));

			StringBuilder cntrNo = new StringBuilder("");

			result.set("creator", lmsService.getUserName(l140mm1a.getCreator()));
			result.set("updater", lmsService.getUserName(l140mm1a.getUpdater()));
			result.set("docStatus",
					getMessage("docStatus." + l140mm1a.getDocStatus()));
			result.set("cntrNo", cntrNo.toString());
			break;
		}// close switch case
		result.set("docStatusVal", l140mm1a.getDocStatus());
		if (!Util.isEmpty(l140mm1a.getCustId())) {
			result.set("typCd", this.getMessage("typCd." + l140mm1a.getTypCd()));
			result.set("showTypCd",
					this.getMessage("typCd." + l140mm1a.getTypCd()));
			result.set("showCustId", StrUtils.concat(l140mm1a.getCustId()
					.toUpperCase(), " ", l140mm1a.getDupNo().toUpperCase(),
					" ", l140mm1a.getCustName()));
		}
		result.set("cntrNo", l140mm1a.getCntrNo());
		result.set("randomCode", l140mm1a.getRandomCode());
		result.set(EloanConstants.OID, CapString.trimNull(l140mm1a.getOid()));
		result.set(EloanConstants.MAIN_OID,
				CapString.trimNull(l140mm1a.getOid()));
		result.set(EloanConstants.MAIN_ID,
				CapString.trimNull(l140mm1a.getMainId()));
		return result;
	}

	/**
	 * 刪除L140MM1A 央行註記異動作業
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL140mm1a(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		if (oids.length > 0) {
			if (lms140mService.deleteL140mm1as(oids)) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
			}
		}
		return result;
	}

	/**
	 * 新增借款人資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	public IResult echo_custId(PageParameters params)
			throws CapException {
		// 儲存and檢核
		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set("custId", Util.trim(params.getString("custId")));
		result.set("dupNo", Util.trim(params.getString("dupNo")));
		result.set("custName", Util.trim(params.getString("custName")));
		result.set("cntrNo", Util.trim(params.getString("cntrNo")));
		return result;

	}
	
	/**
	 * 檢核資料是否已經有正確的登錄
	 * 
	 * <pre>
	 * @param params PageParameters
	 * @param parent Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult checkContractNo(PageParameters params)
			throws CapException {
		
		String cntrNo = Util.trim(params.getString("cntrNo"));
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String errorMsg = this.lms140mService.checkContractNoIsCorrect(cntrNo);

		if(!"".equals(errorMsg)){
			throw new CapMessageException(errorMsg, getClass());
		}
		
		return result;
	}

}
