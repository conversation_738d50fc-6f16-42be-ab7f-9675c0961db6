/**
 *此頁面所有動作
 *
 */
var ViewAction = {
    $openBox: $("#openfilterForm"),
    $form: $("#openfilterForm"),
    gridId: "#gridview",
    /**
     *觸發主檔Grid更新
     */
    _triggerMainGrid: function(){
        $(ViewAction.gridId).trigger('reloadGrid');
    },
    /**
     *執行退回動作
     * @param {Object} cellvalue 欄位顯示值
     * @param {Object} options   欄位設定值
     * @param {Object} rowObject 欄位數值
     */
    openDoc: function(cellvalue, options, rowObject){
        //backdoc.msg1=確認執行取消覆核?
        CommonAPI.confirmMessage(i18n.def["backdoc.msg1"], function(b){
            if (b) {
                $.ajax({
                    handler: "unapprdocformhandler",
                    data: {
                        formAction: "todoBack",
                        oid: rowObject.oid,
                        caseType: $("#selectCase").val(),
                        randomCode: $("#randomCode").val(),
                        file_oid: $("#file_oid").val()
                    },
                    success: function(obj){
                        CommonAPI.showErrorMessage("", obj.brName, function(){
                            ViewAction._triggerMainGrid();
                        });
                    }
                });
            }
        });
    },
    /**
     *開啟篩選視窗
     */
    openQueryBox: function(){
        //初始化
        ViewAction.$form.reset();
        //        $("#startApproveTime").val(CommonAPI.getToday());
        //        $("#endApproveTime").val(CommonAPI.getToday());
        ViewAction.$openBox.thickbox({ // 使用選取的內容進行彈窗
            title: "",
            width: 400,
            height: 200,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    if (!ViewAction.$form.valid()) {
                        return;
                    }
                    var end = $("#startApproveTime").val()
                    var from = $("#endApproveTime").val()
                    var tempStartDate = $("#startApproveTime").val();
                    var tempEndDate = $("#endApproveTime").val();
                    
                    //如果日期大小顛倒直接幫他調換過來
                    if (from > end) {
                        tempStartDate = $("#docLastDate").val();
                        tempEndDate = $("#docFirstDate").val();
                    }
                    ViewAction.filterGrid({
                        selectCase: $("#selectCase").val(),
                        ownBrId: $("#selectFilterBrno").val(),
                        randomCode: $("#randomCode").val(),
                        file_oid: $("#file_oid").val(),
                        tempStartDate: tempStartDate,
                        tempEndDate: tempEndDate
                    });
                    $.thickbox.close();
                },
                "cancel": function(){
                    ViewAction.$form.reset();
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * grid資料篩選
     * @param {Object} sendData 篩選資料
     */
    filterGrid: function(sendData){
        $(ViewAction.gridId).jqGrid("setGridParam", {
            //url: __ajaxHandler + '&_pa=' + "",
            postData: $.extend({
                formAction: "queryForBack",
                docStatus: viewstatus,
                type: $("[name=docDate]:checked").val()
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    },
    /**
     * 更新個金產品種類項目
     */
    reloadProd: function(){
        $.ajax({
            handler: "unapprdocformhandler",
            data: {
                formAction: "reloadProd"
            },
            success: function(obj){
            }
        });
    }
};
$(document).ready(function(){
    ViewAction.openQueryBox();
    var grid = $(ViewAction.gridId).iGrid({
        handler: 'unapprdocgridhandler',
        height: 350,
        sortname: "approveTime",
        sortorder: "desc",
        rowNum: 15,
        colModel: [{
            colHeader: i18n.def['compID'],//統一編號
            align: "left",
            width: 100,
            sortable: true,
            name: 'custId',
            formatter: 'click',
            onclick: ViewAction.openDoc
        }, {
            colHeader: i18n.unapprdoc['UnapprDoc.name'], //客戶名稱
            align: "left",
            width: 100,
            sortable: true,
            name: 'custName'
        }, {
            colHeader: i18n.def['grid.branchName'], //分行名稱
            align: "left",
            width: 100,
            sortable: true,
            name: 'ownBrId'
        }, {
            colHeader: i18n.unapprdoc['UnapprDoc.approveTime'], //核准日期
            align: "left",
            width: 100,
            sortable: true,
            name: 'approveTime'
        }, {
            colHeader: i18n.unapprdoc['UnapprDoc.docCode'], //報表亂碼
            align: "left",
            width: 100,
            sortable: true,
            name: 'randomCode'
        }, {
            colHeader: "oid",
            name: 'oid',//oid
            sortable: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = grid.getRowData(rowid);
            ViewAction.openDoc(null, null, data);
        }
    });
    /**
     登錄分行代號
     */
    $("#selectCopyBranchBt").click(function(){
        CommonAPI.showAllBranch({
            btnAction: function(a, b){
                $("#selectFilterBrno").val(b.brNo);
                $.thickbox.close();
            }
        });
    });
    $("#buttonPanel").find("#btnBackDoc").click(function(){
        var rowId = grid.getGridParam('selrow');
        if (!rowId) {
            //grid.selrow=請先選擇一筆資料。
            return CommonAPI.showMessage(i18n.def["grid.selrow"]);
        }
        var data = grid.getRowData(rowId);
        ViewAction.openDoc(null, null, data);
    }).end().find("#btnFilter").click(function(){
        ViewAction.openQueryBox();
    }).end().find("#reloadProd").click(function(){
        ViewAction.reloadProd();
    });
});


