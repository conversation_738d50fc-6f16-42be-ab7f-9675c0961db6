/* 
 * L120S06ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L120S06ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L120S06A;


/** 利害關係人授信條件對照表主檔 **/
@Repository
public class L120S06ADaoImpl extends LMSJpaDao<L120S06A, String>
	implements L120S06ADao {

	@Override
	public L120S06A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}
	@Override
	public List<L120S06A> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S06A> list = createQuery(L120S06A.class,search).getResultList();
		return list;
	}
	@Override
	public List<L120S06A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S06A> list = createQuery(L120S06A.class,search).getResultList();
		return list;
	}

	@Override
	public List<L120S06A> findByMainIdOrderPrintMode(String mainId) {
		//Map<String, Boolean> orderMap = new LinkedHashMap<String, Boolean>();
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("printMode");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S06A> list = createQuery(L120S06A.class,search).getResultList();
		return list;
	}
	
	@Override
	public L120S06A findByUniqueKey(String mainId, String custId, String dupNo, String cntrNo, String refMainId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "refMainId", refMainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S06A> findByIndex01(String mainId, String custId, String dupNo, String cntrNo){
		ISearch search = createSearchTemplete();
		List<L120S06A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			search.setMaxResults(Integer.MAX_VALUE);
			list = createQuery(L120S06A.class,search).getResultList();
		}
		return list;
	}
	@Override
	public List<L120S06A> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		search.setMaxResults(Integer.MAX_VALUE);
		List<L120S06A> list = createQuery(L120S06A.class,search).getResultList();
		
		return list;
	}
	@Override
	public int delModel(String mainId){
		Query query = getEntityManager().createNamedQuery("L120S06A.delModel");
		query.setParameter("MAINID", mainId); //設置參數
		return query.executeUpdate();
	}
}