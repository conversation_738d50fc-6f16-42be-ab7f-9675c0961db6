package com.mega.eloan.lms.dc.action;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.lms.dc.util.Column;
import com.mega.eloan.lms.dc.util.Type;
import com.mega.eloan.lms.dc.util.Util;
import com.mega.eloan.lms.eloandb.service.impl.AbstractEloandbJdbc;

@Service("ColumnGetDBInfo")
public class ColumnGetDBInfo extends AbstractEloandbJdbc {

	private Logger logger = LoggerFactory.getLogger(ColumnGetDBInfo.class);

	/**
	 * 取得欄位資訊
	 * 
	 * @param dbType
	 *            資料庫代號
	 * @param schema
	 *            schema
	 * @param table
	 *            table
	 * @return
	 */
	public ArrayList<Column> getInfo(String dbType, String schema, String table) {
		ArrayList<Column> list = new ArrayList<Column>();

		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT COLNAME,TYPENAME,LENGTH,SCALE,REMARKS ");
			sql.append(" FROM SYSCAT.COLUMNS ");
//			sql.append(" WHERE TABSCHEMA='").append(schema).append("' ");
//			sql.append("       AND TABNAME='").append(table).append("' ");
			sql.append(" WHERE TABSCHEMA=?  AND TABNAME=? ");
			sql.append(" ORDER BY COLNO ");

//			List<Map<String, Object>> lst = this.getJdbc().queryAllForList(
//					sql.toString());
			List<Map<String, Object>> lst = this.getJdbc().queryAllForList(
					sql.toString(), new Object[]{schema, table});

			for (Map<String, Object> map : lst) {
				Column col = new Column();
				col.setName(Util.trimSpace((String) map.get("COLNAME")));
				col.setType(Type.valueOf(Util.trimSpace((String) map
						.get("TYPENAME"))));
				col.setLength((Integer) map.get("LENGTH"));
				col.setScale((Integer) map.get("SCALE"));
				col.setDscr(Util.trimSpace((String) map.get("REMARKS")));
				list.add(col);
			}
		} catch (Exception e) {
			this.logger.error("取得資料庫欄位資訊時產生錯誤...", e);
		}

		return list;
	}
}
