package com.mega.eloan.lms.cls.pages;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.EloanPageFragment;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 房貸進件
 * </pre>
 * 
 * @since 2020/6/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/6/23,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1220v09")
public class CLS1220V09Page extends AbstractEloanInnerView {

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(CLSDocStatusEnum.編製中);

		//========================
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();
		String pgmDept = MegaSSOSecurityContext.getPGMDept();
		int transactionCode = Util.parseInt(params
				.getString(EloanConstants.TRANSACTION_CODE));
		
//		boolean _Query = au.auth(pgmDept, eloanRoles, transactionCode,
//				AuthType.Query);
		boolean _Accept = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Accept);
		boolean _Modify = au.auth(pgmDept, eloanRoles, transactionCode,
				AuthType.Modify);
		
		List<EloanPageFragment> list = new ArrayList<EloanPageFragment>();
		list.add(LmsButtonEnum.Filter);
		list.add(LmsButtonEnum.QueryCustLoanRecord); // 借用此btn去做「查詢客戶申貸記錄」
		list.add(LmsButtonEnum.View);
		//list.add(CreditButtonEnum.CreateExcel);
		if(_Accept||_Modify){
//			list.add(CreditButtonEnum.CaseToChange);
		}
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String ssoUnitNo = user.getSsoUnitNo();
		if(Util.equals("900", ssoUnitNo)||Util.equals("943", ssoUnitNo)){
//			list.add(CreditButtonEnum.CaseToChange);	
		}
		// 加上Button
		addToButtonPanel(model, list);
		// build i18n
		renderJsI18N(CLS1220M05Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1220V09Page');");
	}

}
