package com.mega.eloan.lms.model;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140SDSC database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140SDSC.class)
public class C140SDSC_ extends RelativeMeta_{
	public static volatile SingularAttribute<C140SDSC, String> fieldId;
	public static volatile SingularAttribute<C140SDSC, String> tab;
	public static volatile SingularAttribute<C140SDSC, String> val;
	public static volatile SingularAttribute<C140SDSC, String> flag;
	public static volatile SingularAttribute<C140SDSC, C140M01A> c140m01a;
}
