/*header---------------------- */
.header {
	background: url(../img/c/header_bg4.gif) repeat-x left top;
	*position: relative;
	height: 74px
}

.header li {
	margin: 0px;
	display: inline;
}

.header li span {
	display: inline-block;
	vertical-align:middle;
}

.header .header_logo {
	background: url(../img/c/logo.jpg) no-repeat;
	width: 288px;
	height: 71px;
	vertical-align:top;
}

.header .system_info {
	font-size: 16px;
	color: #000000;
	font-weight: bold;
	min-width: 310px; /*330*/
	text-align: center;
	padding-top:25px;
}

.system_info .subinfo {
	font-size: 15px;
	color: #900;
}

.header .system_msg{
	background-color:#FFF;
	margin:6px 2px;
	padding: 2px 2px 3px 2px;
	font-size: 12px;
	width:350px;
	border:#eee 1px solid;
}

.system_msg .title {
	color: #3B7092;
}

.system_msg .title b{
	font-family:Arial,Helvetica,sans-serif;
	margin-right:2px;
}

.system_msg .info {
	color: #494848;
}

/* Clear Fix */
.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clearfix {
    display: inline-block;
}

.clearfix {
    display: block;
}

/* leftarea */
#leftarea {
    padding: 15px 15px 10px 10px;
    background-color: #FFF;
    width: 175px;
    display: inline-block;
}

/* rightarea */
#rightarea {
    padding: 10px 0;
    margin: 0px;
    background-color: #FFF;
    width: 99%;
    min-height: 300px;
}

/** tip BEGIN **/
.tip {
    position: relative;
    color: #000;
    text-decoration: none;
}
.tip:hover {
    background: none;
    color: #000;
}
.tip p {
    display: none;
}
.tip:hover p {
    display: block;
    position: absolute;
    top: 22px;
    left: -34px;
	width:150px;
    border-bottom: 2px solid #eee;
    border-right: 2px solid #eee;
}
.tip:hover p {
    color: #f60;
    text-align: left;
    padding: 5px;
    border: 1px solid #ccc;
    background: #fff;
}
/** tip END **/
