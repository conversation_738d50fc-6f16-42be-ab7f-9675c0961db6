/* 
 * LMS2405V01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.pages;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.crs.panels.LMS2405FilterPanel;

@Controller
@RequestMapping("crs/lms2405v02")
public class LMS2405V02Page extends AbstractEloanInnerView {

	@Autowired
	RetrialService retrialService;
	
	public LMS2405V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.待覆核);
		
		if(retrialService.overSeaProgram()){
			addToButtonPanel(model, LmsButtonEnum.Search,
					LmsButtonEnum.ProduceExcel, LmsButtonEnum.Filter);
		}else{
			addToButtonPanel(model, LmsButtonEnum.Search,
					LmsButtonEnum.ProduceExcel, LmsButtonEnum.Filter);
		}
		renderJsI18N(LMS2405V01Page.class);
		renderJsI18N(AbstractEloanPage.class);
		model.addAttribute("showejcicList", ! retrialService.overSeaProgram() );
		setupIPanel(new LMS2405FilterPanel(PANEL_ID), model, params);
	}
}
