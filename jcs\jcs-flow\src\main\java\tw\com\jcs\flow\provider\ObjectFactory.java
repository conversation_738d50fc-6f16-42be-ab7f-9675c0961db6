package tw.com.jcs.flow.provider;

/**
 * <pre>
 * ObjectFactory
 * </pre>
 * 
 * @since 2023年1月10日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月10日
 *          </ul>
 */
public interface ObjectFactory {

    /**
     * 取得Spring容器中已初始化Bean
     * 
     * @param <T>
     * @param objClass
     * @return
     */
    <T> T create(Class<T> objClass);

    /**
     * 取得Spring容器中已初始化Bean
     * 
     * @param <T>
     * @param className
     * @return
     */
    <T> T create(String className);

    /**
     * 取得classLoader
     * 
     * @return
     */
    ClassLoader getClassLoader();

}
