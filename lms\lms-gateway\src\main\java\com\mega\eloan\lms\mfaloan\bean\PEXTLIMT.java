/* 
 * PEXTLIMT.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;


import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** 寬限期檔 **/

public class PEXTLIMT extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true ,nullable=false)
	private String cntrno;

	/** 
	 * 申請年月<p/>
	 * YYY/MM
	 */
	@Column(name="APPDATE", length=6, columnDefinition="CHAR(06)")
	private String appdate;

	/** 寬限期（起） **/
	@Column(name="EXTFROM", columnDefinition="DECIMAL(3)")
	private Integer extfrom;

	/** 寬限期（迄） **/
	@Column(name="EXTEND", columnDefinition="DECIMAL(3)")
	private Integer extend;

	/** 總寬限期 **/
	@Column(name="TEXTEN", columnDefinition="DECIMAL(2)")
	private Integer texten;

	/** 已用寬限期 **/
	@Column(name="SECEXTEN", columnDefinition="DECIMAL(2)")
	private Integer secexten;

	/** 資料修改人(行員代號) **/
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** 取得額度序號 **/
	public String getCntrno() {
		return this.cntrno;
	}
	/** 設定額度序號 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得申請年月<p/>
	 * YYY/MM
	 */
	public String getAppdate() {
		return this.appdate;
	}
	/**
	 *  設定申請年月<p/>
	 *  YYY/MM
	 **/
	public void setAppdate(String value) {
		this.appdate = value;
	}

	/** 取得寬限期（起） **/
	public Integer getExtfrom() {
		return this.extfrom;
	}
	/** 設定寬限期（起） **/
	public void setExtfrom(Integer value) {
		this.extfrom = value;
	}

	/** 取得寬限期（迄） **/
	public Integer getExtend() {
		return this.extend;
	}
	/** 設定寬限期（迄） **/
	public void setExtend(Integer value) {
		this.extend = value;
	}

	/** 取得總寬限期 **/
	public Integer getTexten() {
		return this.texten;
	}
	/** 設定總寬限期 **/
	public void setTexten(Integer value) {
		this.texten = value;
	}

	/** 取得已用寬限期 **/
	public Integer getSecexten() {
		return this.secexten;
	}
	/** 設定已用寬限期 **/
	public void setSecexten(Integer value) {
		this.secexten = value;
	}

	/** 取得資料修改人(行員代號) **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人(行員代號) **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}
}
