/* 
 * C900M01I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** ID重配號記錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C900M01I", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class C900M01I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** uid **/
	@Size(max=32)
	@Column(name="UID", length=32, columnDefinition="CHAR(32)")
	private String uid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 區部別 **/
	@Size(max=1)
	@Column(name="TYPCD", length=1, columnDefinition="CHAR(1)")
	private String typCd;

	/** 
	 * 統一編號<p/>
	 * 新ID
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 
	 * 重覆序號<p/>
	 * 新ID
	 */
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 
	 * 客戶名稱<p/>
	 * 新ID
	 */
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 辦理單位類別 **/
	@Size(max=1)
	@Column(name="UNITTYPE", length=1, columnDefinition="CHAR(1)")
	private String unitType;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 
	 * 目前文件狀態<p/>
	 * 編製中：010<br/>
	 *  待覆核：020<br/>
	 *  已核准：030
	 */
	@Size(max=3)
	@Column(name="DOCSTATUS", length=3, columnDefinition="CHAR(3)")
	private String docStatus;

	/** 文件亂碼 **/
	@Size(max=32)
	@Column(name="RANDOMCODE", length=32, columnDefinition="CHAR(32)")
	private String randomCode;

	/** 文件URL **/
	@Size(max=40)
	@Column(name="DOCURL", length=40, columnDefinition="VARCHAR(40)")
	private String docURL;

	/** 交易代碼 **/
	@Size(max=6)
	@Column(name="TXCODE", length=6, columnDefinition="CHAR(6)")
	private String txCode;

	/** 是否結案 **/
	@Size(max=1)
	@Column(name="ISCLOSED", length=1, columnDefinition="CHAR(1)")
	private String isClosed;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 核准人員號碼 **/
	@Size(max=6)
	@Column(name="APPROVER", length=6, columnDefinition="CHAR(6)")
	private String approver;

	/** 核准日期 **/
	@Column(name="APPROVETIME", columnDefinition="TIMESTAMP")
	private Timestamp approveTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 原始統一編號<p/>
	 * 原ID
	 */
	@Size(max=10)
	@Column(name="ORGCUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String orgCustId;

	/** 
	 * 原始重覆序號<p/>
	 * 原ID
	 */
	@Size(max=1)
	@Column(name="ORGDUPNO", length=1, columnDefinition="CHAR(1)")
	private String orgDupNo;

	/** 
	 * 原始客戶名稱<p/>
	 * 原ID
	 */
	@Size(max=120)
	@Column(name="ORGCUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String orgCustName;

	/** 來文日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="DOCDATE", columnDefinition="Date")
	private Date docDate;

	/** 來文文號 **/
	@Size(max=40)
	@Column(name="DOCNO", length=40, columnDefinition="VARCHAR(40)")
	private String docNo;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得uid **/
	public String getUid() {
		return this.uid;
	}
	/** 設定uid **/
	public void setUid(String value) {
		this.uid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得區部別 **/
	public String getTypCd() {
		return this.typCd;
	}
	/** 設定區部別 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/** 
	 * 取得統一編號<p/>
	 * 新ID
	 */
	public String getCustId() {
		return this.custId;
	}
	/**
	 *  設定統一編號<p/>
	 *  新ID
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 
	 * 取得重覆序號<p/>
	 * 新ID
	 */
	public String getDupNo() {
		return this.dupNo;
	}
	/**
	 *  設定重覆序號<p/>
	 *  新ID
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得客戶名稱<p/>
	 * 新ID
	 */
	public String getCustName() {
		return this.custName;
	}
	/**
	 *  設定客戶名稱<p/>
	 *  新ID
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得辦理單位類別 **/
	public String getUnitType() {
		return this.unitType;
	}
	/** 設定辦理單位類別 **/
	public void setUnitType(String value) {
		this.unitType = value;
	}

	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 
	 * 取得目前文件狀態<p/>
	 * 編製中：010<br/>
	 *  待覆核：020<br/>
	 *  已核准：030
	 */
	public String getDocStatus() {
		return this.docStatus;
	}
	/**
	 *  設定目前文件狀態<p/>
	 *  編製中：010<br/>
	 *  待覆核：020<br/>
	 *  已核准：030
	 **/
	public void setDocStatus(String value) {
		this.docStatus = value;
	}

	/** 取得文件亂碼 **/
	public String getRandomCode() {
		return this.randomCode;
	}
	/** 設定文件亂碼 **/
	public void setRandomCode(String value) {
		this.randomCode = value;
	}

	/** 取得文件URL **/
	public String getDocURL() {
		return this.docURL;
	}
	/** 設定文件URL **/
	public void setDocURL(String value) {
		this.docURL = value;
	}

	/** 取得交易代碼 **/
	public String getTxCode() {
		return this.txCode;
	}
	/** 設定交易代碼 **/
	public void setTxCode(String value) {
		this.txCode = value;
	}

	/** 取得是否結案 **/
	public String getIsClosed() {
		return this.isClosed;
	}
	/** 設定是否結案 **/
	public void setIsClosed(String value) {
		this.isClosed = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得核准人員號碼 **/
	public String getApprover() {
		return this.approver;
	}
	/** 設定核准人員號碼 **/
	public void setApprover(String value) {
		this.approver = value;
	}

	/** 取得核准日期 **/
	public Timestamp getApproveTime() {
		return this.approveTime;
	}
	/** 設定核准日期 **/
	public void setApproveTime(Timestamp value) {
		this.approveTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得原始統一編號<p/>
	 * 原ID
	 */
	public String getOrgCustId() {
		return this.orgCustId;
	}
	/**
	 *  設定原始統一編號<p/>
	 *  原ID
	 **/
	public void setOrgCustId(String value) {
		this.orgCustId = value;
	}

	/** 
	 * 取得原始重覆序號<p/>
	 * 原ID
	 */
	public String getOrgDupNo() {
		return this.orgDupNo;
	}
	/**
	 *  設定原始重覆序號<p/>
	 *  原ID
	 **/
	public void setOrgDupNo(String value) {
		this.orgDupNo = value;
	}

	/** 
	 * 取得原始客戶名稱<p/>
	 * 原ID
	 */
	public String getOrgCustName() {
		return this.orgCustName;
	}
	/**
	 *  設定原始客戶名稱<p/>
	 *  原ID
	 **/
	public void setOrgCustName(String value) {
		this.orgCustName = value;
	}

	/** 取得來文日期 **/
	public Date getDocDate() {
		return this.docDate;
	}
	/** 設定來文日期 **/
	public void setDocDate(Date value) {
		this.docDate = value;
	}

	/** 取得來文文號 **/
	public String getDocNo() {
		return this.docNo;
	}
	/** 設定來文文號 **/
	public void setDocNo(String value) {
		this.docNo = value;
	}
}
