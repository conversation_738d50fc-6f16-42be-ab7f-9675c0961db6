package com.mega.eloan.lms.enums;

public enum PeriodTypeEnum {
	YEAR("1"),

	HELF_YEAR("2"),

	FIRST_QUARTER("3"),

	SECOND_QUARTER("4"),

	THIRD_QUARTER("5"),

	FOURTH_QUARTER("6"),

	OTHER("9");

	private String code;

	private PeriodTypeEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return this.code;
	}

	public boolean isEquals(Object other) {
		if ((other instanceof String)) {
			return this.code.equals(other);
		}
		return super.equals(other);
	}

	public static PeriodTypeEnum getEnum(String code) {
		for (PeriodTypeEnum enums : values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}
}
