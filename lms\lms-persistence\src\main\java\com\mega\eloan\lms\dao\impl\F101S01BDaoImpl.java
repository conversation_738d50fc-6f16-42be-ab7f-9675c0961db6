/* 
 * F101S01BDaoImpl.java
 * 
 * Copyright (c) 2009-2011 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.F101S01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.F101M01A;
import com.mega.eloan.lms.model.F101S01B;

/**
 * <pre>
 * F101S01B Dao 實作。
 * </pre>
 * 
 * @since 2011/8/18
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/8/18,Sunkist Wang,new
 *          </ul>
 */
@Repository
public class F101S01BDaoImpl extends LMSJpaDao<F101S01B, String> implements
        F101S01BDao {

    /*
     * (non-Javadoc)
     * 
     * @see
     * com.mega.eloan.ces.fss.dao.F101S01BDao#deleteByMeta(com.mega.eloan.ces
     * .fss.model.F101M01A)
     */
    public int deleteByMeta(F101M01A meta) {
        Query query = entityManager
                .createNamedQuery("f101s01b.deleteByMainIdAndPid");

        query.setParameter("mainId", meta.getMainId());
        query.setParameter("pid", meta.getUid());
        return query.executeUpdate();
    }

    /*
     * (non-Javadoc)
     * 
     * @see
     * com.mega.eloan.ces.fss.dao.F101S01BDao#findByMainIdAndRatioNo(java.lang
     * .String, java.lang.String)
     */
    public F101S01B findByMainIdAndRatioNo(String mainId, String ratioNo) {
        ISearch mySearch = createSearchTemplete();
        mySearch.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        mySearch.addSearchModeParameters(SearchMode.EQUALS, "ratioNo", ratioNo);
        return findUniqueOrNone(mySearch);
    }

    @Override
    public List<F101S01B> findByMainIdAndRatioNo(String mainId,
            String[] ratioNos) {
        ISearch mySearch = createSearchTemplete();
        mySearch.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
        if (ratioNos != null) {
            mySearch.addSearchModeParameters(SearchMode.IN, "ratioNo", ratioNos);
        }
        return find(mySearch);
    }

}
