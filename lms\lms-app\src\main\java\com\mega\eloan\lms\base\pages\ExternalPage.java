package com.mega.eloan.lms.base.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.gwclient.EJCICGwClient;
import com.mega.eloan.common.gwclient.EJCICGwReqMessage;
import com.mega.eloan.common.gwclient.ETCHGwClient;
import com.mega.eloan.common.gwclient.ETCHGwReqMessage;
import com.mega.eloan.common.pages.AbstractOutputPage;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * ExternalPage
 * </pre>
 * 
 * @since 2013/01/01
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/01/01,fantasy,new
 *          <li>2013/07/04,fantasy,add check auth
 *          </ul>
 */
@Controller
@RequestMapping("/external")
public class ExternalPage extends AbstractOutputPage {

	@Autowired
	EJCICGwClient ejcicClient;

	@Autowired
	ETCHGwClient etchClient;

	@Override
	public String getOutputString(ModelMap model, PageParameters params) {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		String sysId = Util.trim(params.getString(UtilConstants.External.系統別));
		String queryId = Util.trim(params
				.getString(UtilConstants.External.查詢ID));
		String prodId = Util.trim(params.getString(UtilConstants.External.產品別));
		String queryType = Util.trim(
				params.getString(UtilConstants.External.查詢類別)).toLowerCase();

		String url = null;
		if (UtilConstants.External.聯徵.equals(queryType)) {
			if (user.isEJCICAuth()) {
				EJCICGwReqMessage ejcicReq = new EJCICGwReqMessage();

				ejcicReq.setSysId(Util.isEmpty(sysId) ? UtilConstants.CaseSchema.個金
						: sysId);
				ejcicReq.setMsgId(IDGenerator.getUUID());
				ejcicReq.setEmpid(user.getUserId());
				ejcicReq.setDeptid(user.getUnitNo());
				ejcicReq.setProdid(Util.isEmpty(prodId) ? "P7" : prodId); // P7
				ejcicReq.setQueryid(queryId);

				url = Util.trim(ejcicClient.getURL(ejcicReq));
			}
		}

		if (UtilConstants.External.票信.equals(queryType)) {
			if (user.isETCHAuth()) {
				ETCHGwReqMessage etchReq = new ETCHGwReqMessage();

				etchReq.setSysId(Util.isEmpty(sysId) ? UtilConstants.CaseSchema.個金
						: sysId);
				etchReq.setMsgId(IDGenerator.getUUID());
				etchReq.setEmpid(user.getUserId());
				etchReq.setDeptid(user.getUnitNo());
				etchReq.setProdid(Util.isEmpty(prodId) ? "4111" : prodId); // 4111,4114
				etchReq.setId(queryId);

				url = Util.trim(etchClient.getURL(etchReq));
			}
		}

		if (Util.isEmpty(url)) {
			StringBuilder sb = new StringBuilder();
			sb.append("<center><b><font color=red>");
			sb.append(getI18nMsg("noAuth"));
			sb.append("</font></b></center>");
			url = sb.toString();
		}

		return url;
	}

	@Override
	protected String getViewName() {
		// TODO Auto-generated method stub
		return null;
	}
}
