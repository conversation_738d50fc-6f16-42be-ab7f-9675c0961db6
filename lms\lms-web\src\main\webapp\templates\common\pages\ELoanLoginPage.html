<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></meta>
		<meta name="_csrf" th:content="${_csrf.token}"></meta>
		<meta name="_csrf_header" th:content="${_csrf.headerName}"></meta>
		<link rel="stylesheet" th:href="@{/css/login_css.css}" />
		<script type="text/javascript">
			var jsCache = null;
			var webroot = window.location.pathname.substr(0, window.location.pathname.indexOf("/app"));
			function validPath(inputPath){
				if (/-web/.test(inputPath)) {
					return inputPath;
				}
				return '';
			}
		</script>
		<script type="text/javascript" th:src="@{/js/common/first_check.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/purify/3.0.6/purify.js}"></script>
		<script type="text/javascript" th:src="@{/js/lib/requirejs/2.3.6/require.min.js}"></script>
		<script type="text/javascript" th:src="@{/js/loginjs.js}"></script>
		<title>[[#{ELoanLoginPage.page.title}]]</title>
    </head>
    <body>
    	<div class="body">
			<div id="headerarea">
				<div class="header_wrap" id="_headerPanel">
		            <ul class="block header">
						<li><span class="header_logo"></span></li>
					</ul>
		        </div>
	        </div>
			<div class="formbody">
	            <div th:if="${showLoginDialog}">
	            	<div class="formdiv" th:insert="~{common/pages/ELoanLoginFragment :: loginFragment}" ></div>
	            </div>
				<div id="messageDiv" ></div>
				<div id="imsg" class="errormsg" style="display:block;text-align:center;">
					<th:block th:utext="#{ELoanLoginPage.login.msg01}">請關閉本視窗，重新使用行員專用網登入本系統。</th:block>
				</div>
			</div>
		</div>
    </body>
</html>
