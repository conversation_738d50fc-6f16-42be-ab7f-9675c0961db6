package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;

@Controller
@RequestMapping(path = "/fms/cls9041v00")
public class CLS9041V00Page extends AbstractEloanInnerView {

	public CLS9041V00Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		// UPGRADE: 後續沒有用到就可以刪掉
		// add(new Label("_buttonPanel"));

		renderJsI18N(CLS9041V00Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS9041V00Page.js" };
	}
}
