package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

import com.mega.eloan.lms.mfaloan.bean.ELF691;

/**
 * <pre>
 * 聯徵查調資料介接檔
 * </pre>
 * 
 * @since 2023/5
 * <AUTHOR>
 * @version <ul>
 *          <li>2023/5,009763,new
 *          </ul>
 */
public interface MisELF691Service {

	/**
	 * 取得送查資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @param qItem
	 * @return
	 */
	ELF691 getLastByCustIdQItem(String custId, String dupNo,
			String qItem);

	/**
	 * 新增資料
	 * 
	 * @param elf691
	 */
	int insert(ELF691 elf691);

	/**
	 * 取得送查資料
	 * @param custId
	 * @param dupNo
	 * @param qItem
	 * @param date yyyy-MM-dd
	 * @return
	 */
	List<ELF691> getQueryRecord(String custId, String dupNo, String qItem, String brn, String key1, String key2, String date);

}
