/* 
 * L180M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L180M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/** 覆審名單主檔 **/
@Repository
public class L180M01ADaoImpl extends LMSJpaDao<L180M01A, String> implements
		L180M01ADao {

	@Override
	public L180M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public L180M01A findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01A> findByBranchAnddataDate(String Branch, Date dataDate) {
		ISearch search = createSearchTemplete();
		MegaSSOUserDetails userId = MegaSSOSecurityContext.getUserDetails();
		search.addSearchModeParameters(SearchMode.EQUALS, "ownBrId",
				userId.getUnitNo());
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", Branch);
		search.addSearchModeParameters(SearchMode.EQUALS, "dataDate", dataDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

	@Override
	public L180M01A findById(String mainId, String id, String dupno) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", id);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupno);
		return findUniqueOrNone(search);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findMaxbatchNO() {
		Date dataDateMin = CapDate.getDate(
				TWNDate.toAD(new Date()).substring(0, 4) + "-01-01",
				"yyyy-MM-dd");
		Date dataDateMax = CapDate.getDate(
				Integer.toString((Util.parseInt(TWNDate.toAD(new Date())
						.substring(0, 4)) + 1)) + "-12-31", "yyyy-MM-dd");
		Query query = getEntityManager().createNamedQuery(
				"L180M01A.selMaxbatchNO");
		// query.setParameter("CASEYEAR", Util.parseInt(currYear)); //設置參數
		query.setParameter("dataDateMin", dataDateMin); // 設置參數
		query.setParameter("dataDateMax", dataDateMax);

		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<L180M01A> findMaxbatchNONextYear() {
		String currYear = TWNDate.toAD(new Date()).substring(0, 4);
		Query query = getEntityManager().createNamedQuery(
				"L180M01A.selMaxbatchNO");
		query.setParameter("CASEYEAR", (Util.parseInt(currYear) + 1)); // 設置參數

		return query.getResultList();
	}

	@Override
	public List<L180M01A> findMaxDataDate(String branch, Date retrialDate) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		search.addSearchModeParameters(SearchMode.EQUALS, "defaultCTLDate",
				retrialDate);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

	/**
	 * J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @param branch
	 * @param rpaKey
	 * @return
	 */
	@Override
	public List<L180M01A> findByBranchIdAndLikeMainId(String branch,
			String likeMainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branch);
		search.addSearchModeParameters(SearchMode.LIKE, "mainId", "%"
				+ likeMainId);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

	@Override
	public List<L180M01A> findByDefaultCTLDate(String docStatus,
			String bgnDate, String endDate) {
		ISearch search = createSearchTemplete();

		search.addSearchModeParameters(SearchMode.EQUALS, "docStatus",
				docStatus);

		String[] reasonStr = { bgnDate, endDate };
		search.addSearchModeParameters(SearchMode.BETWEEN, "defaultCTLDate",
				reasonStr);
		search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", null);
		return find(search);
	}

}