<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" ></meta>
    </head>
	<body>
        <th:block th:if="${_isAuthenticationException}">
            <script th:inline="javascript">
                let msg = /*[[${errorMessage}]]*/ "";
                CommonAPI.showPopMessage(msg, function(){
                    logout();
                }); 
            </script>
        </th:block>
        <div class="container" style="font-size: 80%; margin: 0 auto; text-align: center; width: 750px;">
            <div id="lg_head" style="margin-top: 20px;">
                <div id="lg_logo">
                </div>
            </div>
            <div id="lg_body" style="clear: both;">
                <table id="lg_tab" style="width: 70%; text-align: center">
                    <tbody>
                        <tr>
                            <td valign="top">
                                <div class="lg_signon ui-widget-content ui-corner-all">
                                    <p style="font-weight: bold; font-size: 125%; margin-bottom: 20px;">
                                        <br/> <br/> <span style="color: #FF0000;" th:text=${errorMessage}>message</span>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </body>
</html>
