/* 
 * L120S05BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S05B;


/** 借款人集團授信明細檔 **/
public interface L120S05BDao extends IGenericDao<L120S05B> {

	L120S05B findByOid(String oid);
	
	List<L120S05B> findByMainId(String mainId);
	
	L120S05B findByUniqueKey(String mainId, String custId, String dupCode);

	List<L120S05B> findByIndex01(String mainId, String custId, String dupCode);
	
	List<L120S05B> findByCustIdDupId(String custId,String DupNo);

	int delModel(String mainId);
}