package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140S02NDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S02N;

/** 消金產品種類敘述說明檔 **/
@Repository
public class L140S02NDaoImpl extends LMSJpaDao<L140S02N, String> implements
		L140S02NDao {

	@Override
	public L140S02N findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S02N> findByMainIdSeq(String mainId, Integer seq){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		List<L140S02N> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public L140S02N findByUniqueKey(String mainId, Integer seq, String itemType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "seq", seq);
		search.addSearchModeParameters(SearchMode.EQUALS, "itemType", itemType);
		return findUniqueOrNone(search);
	}
	
}