package com.mega.eloan.lms.mfaloan.service;

import java.util.List;
import java.util.Map;

public interface MisCustdataService {
	public Map<String, Object> findCustdataMapByMainId(String id, String dupno);

	public List<Map<String, Object>> findCustdataForList(String id, String dupno);

	public List<?> findCustdataByMainId(String mainId, String dupNo);

	Map<String, Object> findCustdataSelCname(String custId, String dupNo);

	/**
	 * /** <li>00-客戶ID <li>01-客戶ID 重複碼 <li>02-客戶ID 錯誤檢查碼 <li>03-基本資料性質別 <li>
	 * 04-中文戶名 <li>05-英文戶名 <li>06-是否民國前出生 <li>07-生日 <li>08-行業對象別 <li>09-戶籍地址郵遞區號
	 * <li>10-戶籍地址(縣市) <li>11-戶籍地址(區鄉鎮市) <li>12-戶籍地址(村里) <li>13-戶籍地址(鄰) <li>
	 * 14-戶籍地址(其餘部分) <li>15-授信集團代號 <li>16-授信客戶類別 <li>17-最新異動日期 <li>18-是否經覆核 <li>
	 * 19-通訊地址數 <li>20-電子信箱地址數 <li>21-手機號碼個數 <li>22-家中號碼個數 <li>23-公司號碼個數 <li>
	 * 24-傳真機號碼個數 <li>25-學歷 <li>26-配偶姓名 <li>27-配偶身分證字號 <li>28-配偶出生年月日 <li>
	 * 29-個人服務公司 <li>30-職稱 <li>31-父親或監護人名字 <li>32-母親名字 <li>33-父親證號 <li>34-母親證號
	 */
	final String[] CustDataCols = { "CUSTID", "DUPNO", "ERRORCD", "CHARCD",
			"CNAME", "ENAME", "BIRTHFLG", "BIRTHDT", "BUSCD", "ADDRZIP",
			"CITYR", "TOWNR", "LEER", "LINR", "ADDRR", "GRPCD", "CLTYPE",
			"TXDT", "VFLAG", "ADDRCNT", "EMAILCNT", "MPCNT", "HTELCNT",
			"OTELCNT", "FAXCNT", "DEGREECD", "MATENM", "MATEID", "MATEBIRT",
			"SCMP", "POS", "FNAME", "MNAME", "FID", "MID", "VIPCODE",
			"MEGAKEY", "MMAFLAG", "REALTAXNO", "BUSSKIND", "BUSSFLAG",
			"MEGASTAT", "NOTAXNO", "BRNO", "MEGACANDT", "LCNAME" };

	/**
	 * 輸入客戶統一編號，系統取得其姓名及重複序號。
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @return List<客戶資料>
	 */
	public List<Map<String, Object>> findById(String custId);

	/**
	 * 取得客戶基本資料
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, String>
	 */
	public Map<String, Object> findByIdDupNo(String custId, String dupNo);

	/**
	 * 取得客戶行業別
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, String>
	 */
	public Map<String, Object> findBussTypeByIdDupNo(String custId, String dupNo);

	/**
	 * 輸入客戶統一編號，系統取得其姓名及重複序號及電話。
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<客戶資料>
	 */
	public Map<String, Object> findByIdDupNoWithTel(String custId, String dupNo);

	/**
	 * 輸入客戶統一編號，系統取得其姓名。
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return Map<String, Object> CUSTID,DUPNO,CNAME
	 */
	Map<String, Object> findCNameByIdDupNo(String custId, String dupNo);

	/**
	 * 利用統編及重覆序號取得客戶名稱
	 * 
	 * @param custId
	 *            客戶統一編號
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	public List<Map<String, Object>> findCustDataCname(String custId,
			String dupNo);

	/**
	 * 輸入客戶統一編號重複序號 取得客戶行業別
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo重覆序號
	 * @return 行業別 BUSCD,BUSSKIND
	 */
	public Map<String, Object> findBUSCDByCustIdANdDupNo(String custId,
			String dupNo);

	/**
	 * 輸入客戶統一編號重複序號 取得客戶資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return CUSTID , DUPNO, BUSCD, CNAME ,ENAME
	 */
	public Map<String, Object> findAllByByCustIdAndDupNo(String custId,
			String dupNo);

	/**
	 * 輸入客戶統一編號重複序號 取得客戶資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return Cltype
	 */
	public String findCltypeById(String custId, String dupNo);

	/**
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCMFCUS1ByIdAndDup(String custId, String dupNo);

	/**
	 * J-104-0XXX-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	Map<String, Object> findCharCdByIdDupNo(String custId, String dupNo);

	/**
	 * 取得註冊地址 J-105-0233-001 Web
	 * e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
	 * 
	 * @param id
	 * @param dupno
	 * @return
	 */
	public List<Map<String, Object>> findCustRegisteredAddressForList(
			String id, String dupno);

	/**
	 * J-108-0040_05097_B1001 Web e-Loan企金授信新增108年度新核准往來客戶及新增放款額度統計表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCltypeAndBusCdById(String custId,
			String dupNo);

	/**
	 * I-108-0027_05097_B1001 Web
	 * e-Loan國內授信系統配合0024客戶中文檔取消AML-STATUS禁止交易之限制，若為制裁名單將採關戶或凍結
	 * 
	 * 原本AML判斷拒絕交易，是抓MIS.CMFCUS1 的CM1_AML_STATUS欄位 = '3'，現在要改成讀取 MIS.CMFDNGER ，當
	 * CMDNG_REASON_CODE = '3' 且 CMDNG_TF_FLAG = 'Y' 為拒絕交易
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCMFDNGERByIdAndDup(String custId,
			String dupNo);

	/**
	 * J-109-0394_05097_B1001 e-Loan國內企金授信額度明細表新增DBU戶使用OBU額度等相關檢核
	 * 
	 * OBU ID 檢查-mis.cmfcus25 該ID CM25_OSU_FLAG = '3' 且 CM25_DOMEST_TAXNO 有 DBU
	 * ID ，則本案需要改用DBU ID來簽案
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCmfcus25ByCustId(String custId, String dupNo);

	/**
	 * J-109-0394_05097_B1001 e-Loan國內企金授信額度明細表新增DBU戶使用OBU額度等相關檢核
	 * 
	 * DBU ID 檢查-mis.cmfcus25 中要有一筆OBU ID，他的CM25_OSU_FLAG = '3'，且
	 * CM25_DOMEST_TAXNO 為此DBU ID，此時DBU才可以簽OBU額度序號
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findCmfcus25ByDomestIdAndOsuFlag(String custId);
	
	/**
	 * J-111-0207 eloan國內企金管理系統，動用檢核表增加一項目:「信保案件負責人客戶基本資料檔是否已建有配偶資料」
	 * 用負責人查詢是否有配偶資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findPrincipalMate(String custId, String dupNo);

}
