
package com.mega.eloan.lms.mfaloan.service.impl;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.util.CapDate;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF498;
import com.mega.eloan.lms.mfaloan.service.MisELF498Service;

/**
 * <pre>
 * 消金新案紀錄檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
@Service
public class MisELF498ServiceImpl extends AbstractMFAloanJdbc implements
MisELF498Service {

	@Override
	public ELF498 findByPk(String elf498_branch, String elf498_custid,
			String elf498_dupno, Date elf498_newdate) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("ELF498.selByPk", new Object[]{elf498_branch, elf498_custid,
				 elf498_dupno,  CapDate.parseSQLDate(elf498_newdate)});
		List<ELF498> list = toELF498(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}	
	}	
	
	@Override
	public List<ELF498> findNotRetrial(String elf498_branch, String elf498_custid,String elf498_dupno){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF498.findNotRetrial"
				, new Object[]{elf498_branch, elf498_custid, elf498_dupno  });
		return toELF498(rowData);
	}

	@Override
	public List<Map<String,Object>> sel_gfnGetActualReviewData(String elf498_branch, String elf498_latestdate){
		return this.getJdbc().queryForListWithMax("ELF498.sel_gfnGetActualReviewData",
				new String[] { elf498_branch, elf498_latestdate});
	}
	private List<ELF498> toELF498(List<Map<String, Object>> rowData){
		List<ELF498> list = new ArrayList<ELF498>();
		for (Map<String, Object> row : rowData) {
			ELF498 model = new ELF498();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}
}
