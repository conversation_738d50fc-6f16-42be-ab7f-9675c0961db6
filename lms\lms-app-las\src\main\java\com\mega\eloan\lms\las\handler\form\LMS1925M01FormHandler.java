package com.mega.eloan.lms.las.handler.form;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.dw.service.DWAslndavgovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.EloandbcmsBASEService;
import com.mega.eloan.lms.las.report.LMS1925R01RptService;
import com.mega.eloan.lms.las.report.LMS1925R02RptService;
import com.mega.eloan.lms.las.service.LMS1925Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisELCUS21Service;
import com.mega.eloan.lms.mfaloan.service.MisELCUS27Service;
import com.mega.eloan.lms.mfaloan.service.MisELF346Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.mfaloan.service.MisElacnmService;
import com.mega.eloan.lms.mfaloan.service.MisIcbcBrService;
import com.mega.eloan.lms.mfaloan.service.MisLNF150Service;
import com.mega.eloan.lms.mfaloan.service.MisRatetblService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.L192M01A;
import com.mega.eloan.lms.model.L192M01B;
import com.mega.eloan.lms.model.L192M01C;
import com.mega.eloan.lms.model.L192S01A;
import com.mega.eloan.lms.model.L192S02A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * 查詢授信業務工作底稿
 * 
 * <AUTHOR>
 * 
 */
@Scope("request")
@Controller("lms1925m01formhandler")
@DomainClass(L192M01A.class)
public class LMS1925M01FormHandler extends AbstractFormHandler {
	private static final int MAXLEN_L192M01A_STATEMENTADDRTO = StrUtils.getEntityFileldLegth(L192M01A.class, "statementAddrTo", 180);
	private static final int MAXLEN_L192M01A_STATEMENTADDRFROM = StrUtils.getEntityFileldLegth(L192M01A.class, "statementAddrFrom", 180);
	private static final int MAXLEN_L192M01A_TADDR = StrUtils.getEntityFileldLegth(L192M01A.class, "tAddr", 180);
	@Resource
	CLSService clsService;
	
	@Resource
	LMS1925Service lms1925Service;

	@Resource
	BranchService branchService;

	@Resource
	UserInfoService userService;

	@Resource
	MisLNF150Service misLNF150Service;

	@Resource
	MisELF346Service misELF346Service;

	@Resource
	MisRatetblService misRatetblService;

	@Resource
	MisElacnmService misElacnmService;

	@Resource
	DWAslndavgovsService dwAslndavgovsService;

	@Resource
	MisELCUS21Service misELCUS21Service;

	@Resource
	MisELCUS27Service misELCUS27Service;

	@Resource
	MisELLNGTEEService misELLNGTEEService;

	@Resource
	MisCustdataService misCustdataService;

	@Resource
	EloandbBASEService eloandbBASEService;

	@Resource
	MisIcbcBrService misIcbcBrService;

	@Resource
	LMS1925R01RptService lms1925r01RptService;

	@Resource
	LMS1925R02RptService lms1925r02RptService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	DocCheckService docCheckService;

	@Resource
	EloandbcmsBASEService eloandbcmsBASEService;
	
	@Resource
	MisdbBASEService misdbBaseService;

	/**
	 * 檢核對帳單是否需要列印
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult checkPrint(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString(EloanConstants.MAIN_OID);

		L192M01A meta = lms1925Service.getL192M01A(oid);
		Set<L192S01A> l192s01as = meta.getL192s01as();

		Set<L192S01A> returnData = new HashSet<L192S01A>();
		BigDecimal zero = BigDecimal.ZERO;
		StringBuffer returnMessage = new StringBuffer();
		int printSize = 0;
		StringBuffer totalMessage = new StringBuffer();
		totalMessage.append("申請內容共"+l192s01as.size()+"筆，");
		if (!l192s01as.isEmpty()){
			for (L192S01A l192s01a : l192s01as) {
				//確認LNF020寄送函證FLAG若為9:不寄送函證，則不納入列印，且將此筆資料送至前端提示不列印。
				boolean notice = true;
				if ("13".contains(branchService.getBranch(meta.getOwnBrId()).getBrNoFlag())){
					List<Map<String, Object>> noticetypes;
					try {
						noticetypes = misdbBaseService.findLNF020_NOTICE_TYPE(l192s01a.getQuotaNo());
					} catch (Exception e){
						throw new CapMessageException("與中心主機無法連線，請稍後再試。", this.getClass());
					}
					if (noticetypes != null){
						for (Map<String, Object> map : noticetypes){
							if ("9".equals(map.get("LNF020_NOTICE_TYPE"))){
								notice = false;
							}
						}
					}
				}
				if (notice){
					returnData.add(l192s01a);
					printSize++;
				} else {
					returnMessage.append("放款科目:"+l192s01a.getSubject()+" 帳號"+l192s01a.getAccNo()+" 額度序號"+l192s01a.getQuotaNo()+"已設定不寄送函證，不納入對帳單列印資料。<br>");
				}
			}
		}
		totalMessage.append("納入列印筆數共"+printSize+"筆。<br>");
		//2.有資料，前端會留存mainid供列印使用。無資料，會於前端顯示不列印訊息
		String mark;
		if (printSize == 0) {
			mark = "N";
			lms1925Service.printMark(meta, mark);
		} else if (printSize == l192s01as.size()){
			mark = "Y";
			lms1925Service.printMark(meta, mark);
		} else {
			mark = "P";
			lms1925Service.printMark(meta, mark);
		}
		result.set("L192S01A_PRINT_MARK", mark);
		result.set("NO_PRINT_DETAIL",totalMessage.append(returnMessage.toString()).toString());
		
		return result;
	}

	/**
	 * 從view的按鈕整批 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendGAll(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1925Service.getL192M01A(mainOid);
		lms1925Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendAll(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1925Service.getL192M01A(mainOid);
		lms1925Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 從view的按鈕整批傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult sendAllNextG(PageParameters params)
			throws CapException {

		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1925Service.getL192M01A(mainOid);
		lms1925Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 稽核 覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult acceptG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1925Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1925Service.flowControl(meta.getOid(), "稽核_核准");
		return result;
	}

	/**
	 * 分行 覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult accept(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1925Service.getL192M01A(mainOid);
		}

		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}

		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1925Service.flowControl(meta.getOid(), "分行_核准");
		return result;
	}

	/**
	 * 稽核 退回編製中
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult returnG(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1925Service.getL192M01A(mainOid);
		}
		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}
		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1925Service.flowControl(meta.getOid(), "稽核_退回");
		return result;
	}

	/**
	 * 分行 退回編製中
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Accept, CheckDocStatus = true)
	public IResult returnB(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1925Service.getL192M01A(mainOid);
		}
		if (!hasEL02()) {
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0013"), getClass());
		}
		if (user.getUserId().equals(meta.getUpdater())) {
			// EFD0053=WARN|覆核人員不可與「經辦人員或其它覆核人員」為同一人|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0053"), getClass());
		}
		lms1925Service.flowControl(meta.getOid(), "分行_退回");
		return result;
	}

	/**
	 * 傳送稽核室
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendNextG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1925Service.saveAndSendDocument(l192m01a);
		return result;
	}

	/**
	 * 稽核_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult sendG(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1925Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 分行_呈主管覆核
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult send(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1925Service.saveAndSendDocument(l192m01a);

		return result;
	}

	/**
	 * 取得 申請內容資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL192S01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l192s01aoid");
		L192S01A l192s01a = lms1925Service.getL192S01A(oid);

		Map<String, IFormatter> fmt = new HashMap<String, IFormatter>();
		@SuppressWarnings("serial")
		IFormatter decimalFormat = new IFormatter() {
			DecimalFormat df = new DecimalFormat("#,###,###,###,###.##");

			@SuppressWarnings("unchecked")
			@Override
			public String reformat(Object in) throws CapFormatException {
				if (in != null) {
					return df.format(in);
				}
				return "";
			}
		};
		fmt.put("quotaAmt", decimalFormat);
		fmt.put("balAmt", decimalFormat);

		result.add(new CapAjaxFormResult(l192s01a.toJSONObject(new String[] {
				"balDate", "subject", "accNo", "quotaNo", "quotaCurr",
				"quotaAmt", "balCurr", "balAmt", "appDate", "signDate",
				"useDate", "fromDate", "endDate", "way", "appr", "checkDate",
				"checkCurr", "checkAmt", "endorser" }, fmt)));

		return result;
	}

	/**
	 * 取得擔保品內容資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getL192S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("l192s02oid");
		L192S02A l192s02a = lms1925Service.getL192S02A(oid);

		// logger.debug(l192s02a.toString());

		// result.add(new CapAjaxFormResult(l192s02a.toString()));
		result.add(new CapAjaxFormResult(l192s02a.toJSONObject(new String[] {
				"oid", "gteName", "estCurr", "estAmt", "loanCurr", "loanAmt",
				"setCurr", "setAmt", "estDate", "owner", "setDate",
				"setDateFrom", "setDateEnd", "setPosition", "insurance",
				"insDateFrom", "insDateEnd", "insPaper" }, null)));
		// 避免子文件的oid 蓋掉畫面上主件的oid
		String l192s02oid = (String) result.get("oid");
		result.removeField("oid");
		result.set("l192s02oid", l192s02oid);
		// logger.debug(result.get("oid").toString());
		logger.debug(result.get("l192s02oid").toString());

		return result;
	}

	/**
	 * 刪除授信業務工作底稿
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL192M01A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		Map<String, String> lockedUser = docCheckService
				.listLockedDocUser(mainId);
		if (lockedUser == null) {
			lms1925Service.deleteL192M01A(oid);
		} else {
			String message = getPopMessage("EFD0055", lockedUser);
			result.set("deleteMessage", message);
		}

		// Map<String, String> lockedUser =
		// docCheckService.listLockedDocUser(mainId);
		// if (lockedUser == null) {
		// lms1905Service.deleteL192M01A(oid);
		// } else {
		// throw new CapMessageException(getPopMessage("EFD0055", lockedUser),
		// getClass());
		// }

		return result;
	}

	/**
	 * 刪除擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult deleteL192S02A(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString("deleteMainOid");
		String mainId = params.getString("deleteMainId");

		L192S02A l192s02a = new L192S02A();
		l192s02a.setOid(oid);
		l192s02a.setMainId(mainId);
		lms1925Service.deleteL192S02A(l192s02a);

		// throw new CapException();

		return result;
	}

	/**
	 * 更新擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult updateL192S02A(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();

		String l192s02oid = params.getString("l192s02oid");

		L192S02A l192s02a = lms1925Service.getL192S02A(l192s02oid);

		parseL192S02A(params, l192s02a);

		lms1925Service.saveL192S02A(l192s02a);

		return result;
	}

	/**
	 * 新增擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addL192S02A(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = params.getString(EloanConstants.MAIN_ID);
		String oid = params.getString(EloanConstants.MAIN_OID);

		L192S02A l192s02a = new L192S02A();
		l192s02a.setMainId(mainId);

		parseL192S02A(params, l192s02a);

		l192s02a.setCreator(user.getUserId());
		l192s02a.setCreateTime(new Date());

		lms1925Service.saveL192S02A(l192s02a);
		result.set(EloanConstants.MAIN_OID, oid);
		result.set(EloanConstants.MAIN_ID, mainId);

		return result;
	}

	/**
	 * 處理擔保品資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param l192s02a
	 *            L192S02A
	 */
	private void parseL192S02A(PageParameters params, L192S02A l192s02a) {

		String gteName = params.getString("gteName", "").trim();
		l192s02a.setGteName(gteName);

		String estCurr = params.getString("estCurr", "").trim();
		l192s02a.setEstCurr(estCurr);

		String estAmt = params.getString("estAmt", "").trim().replace(",", "");
		l192s02a.setEstAmt("".equals(estAmt) ? null : new BigDecimal(estAmt));

		String loanCurr = params.getString("loanCurr", "").trim();
		l192s02a.setLoanCurr(loanCurr);

		String loanAmt = params.getString("loanAmt", "").trim()
				.replace(",", "");
		l192s02a.setLoanAmt("".equals(loanAmt) ? null : new BigDecimal(loanAmt));

		String setCurr = params.getString("setCurr", "").trim();
		l192s02a.setSetCurr(setCurr);

		String setAmt = params.getString("setAmt", "").trim().replace(",", "");
		l192s02a.setSetAmt("".equals(setAmt) ? null : new BigDecimal(setAmt));

		String estDate = params.getString("estDate").trim();
		l192s02a.setEstDate("".equals(estDate) ? null : CapDate.getDate(
				estDate, "yyyy-MM-dd"));

		String owner = params.getString("owner", "").trim();
		l192s02a.setOwner(owner);

		String setDate = params.getString("setDate", "").trim();
		l192s02a.setSetDate("".equals(setDate) ? null : CapDate.getDate(
				setDate, "yyyy-MM-dd"));

		String setDateFrom = params.getString("setDateFrom", "").trim();
		l192s02a.setSetDateFrom("".equals(setDateFrom) ? null : CapDate
				.getDate(setDateFrom, "yyyy-MM-dd"));

		String setDateEnd = params.getString("setDateEnd", "").trim();
		l192s02a.setSetDateEnd("".equals(setDateEnd) ? null : CapDate.getDate(
				setDateEnd, "yyyy-MM-dd"));

		String setPosition = params.getString("setPosition", "").trim();
		l192s02a.setSetPosition("".equals(setPosition) ? null : new Integer(
				setPosition));

		String insurance = params.getString("insurance", "").trim();
		l192s02a.setInsurance(insurance);

		String insDateFrom = params.getString("insDateFrom", "").trim();
		l192s02a.setInsDateFrom("".equals(insDateFrom) ? null : CapDate
				.getDate(insDateFrom, "yyyy-MM-dd"));

		String insDateEnd = params.getString("insDateEnd", "").trim();
		l192s02a.setInsDateEnd("".equals(insDateEnd) ? null : CapDate.getDate(
				insDateEnd, "yyyy-MM-dd"));

		String insPaper = params.getString("insPaper", "").trim();
		l192s02a.setInsPaper(insPaper);
	}

	/**
	 * 新增授信業務工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addNew(PageParameters params)
			throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		JSONArray custIDs = JSONArray.fromObject(params.getString("custIDs"));
		String innerAudit = params.getString("innerAudit");

		// 回傳json array資料
		JSONArray returnJsonArray = new JSONArray();

		// 新增多筆借款人資料
		for (Object data : custIDs) {
			JSONObject d = (JSONObject) data;
			logger.debug(d.getString("custid"));
			logger.debug(d.getString("name"));

			String custId_dupNo = d.getString("custid").toUpperCase();
			String custId = custId_dupNo
					.substring(0, custId_dupNo.length() - 1);
			String dupNo = custId_dupNo.substring(custId_dupNo.length() - 1,
					custId_dupNo.length()).toUpperCase();
			String custName = d.getString("name");

			L192M01A l192m01a = new L192M01A();
			String uid_mainId = IDGenerator.getUUID();
			l192m01a.setRandomCode(IDGenerator.getRandomCode());
			l192m01a.setUid(uid_mainId);
			l192m01a.setMainId(uid_mainId);
			l192m01a.setCustId(custId);
			l192m01a.setDupNo(dupNo);
			l192m01a.setCustName(custName);

			// 1.查核授信業務稽核工作底稿
			// 2.查核房屋貸款稽核工作底稿
			l192m01a.setShtType(UtilConstants.ShtType.授信業務工作底稿);
			l192m01a.setUnitType("1");
			l192m01a.setOwnBrId("Y".equals(innerAudit) ? user.getUnitNo()
					: params.getString("brId"));

			// l192m01a.setDocStatus(FlowDocStatusEnum.編製中);

			l192m01a.setInnerAudit(innerAudit);// 是否為內部查核

			// 取得地址
			/*
			 * List<Map<String, Object>> custAddrs = misELCUS21Service
			 * .getCustAddress(custId, dupNo);
			 * 
			 * // 再問建霖是否要存多筆 for (Map<String, Object> custAddr : custAddrs) {
			 * String addrzip = custAddr.get("ADDRZIP") == null ? "" : (String)
			 * custAddr.get("ADDRZIP"); String cityr = custAddr.get("CITYR") ==
			 * null ? "" : (String) custAddr.get("CITYR"); String townr =
			 * custAddr.get("TOWNR") == null ? "" : (String)
			 * custAddr.get("TOWNR"); String addrr = custAddr.get("ADDRR") ==
			 * null ? "" : (String) custAddr.get("ADDRR"); String tAddr =
			 * addrzip + " " + cityr.trim() + townr.toLowerCase() +
			 * addrr.trim(); l192m01a.setTAddr(tAddr); }
			 */

			List<Map<String, Object>> custPhones = misELCUS27Service
					.getCustPhone(custId, dupNo);

			for (Map<String, Object> custPhone : custPhones) {
				String areano = custPhone.get("AREANO") == null ? ""
						: (String) custPhone.get("AREANO");
				String telno = custPhone.get("TELNO") == null ? ""
						: (String) custPhone.get("TELNO");
				String tTel = areano.trim() + telno.trim();
				l192m01a.setTTel(tTel);

				// 取第一筆即可
				break;
			}

			l192m01a.setCreator(user.getUserId());
			l192m01a.setCreateTime(CapDate.getCurrentTimestamp());

			if ("Y".equals(innerAudit)) {
				// 如果是內部查核的話，取得登入分行的
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo(user.getUnitNo());
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			} else {
				// 稽核查核的話，取得本身自己的單位
				// Map<String, Object> bankInfo = misIcbcBrService
				// .getBankInfo(user.getSsoUnitNo());
				Map<String, Object> bankInfo = misIcbcBrService
						.getBankInfo("906");
				String statementAddrFrom = (String) (bankInfo == null ? ""
						: bankInfo.get("ADDR"));
				l192m01a.setStatementAddrFrom(statementAddrFrom);
			}

			String statementAddrTo = this.getCustAddr(custId, dupNo);
			statementAddrTo = CapString.halfWidthToFullWidth(statementAddrTo); //轉全型
			if(clsService.is_function_on_codetype("l192m01a_statementAddrTo_nosub")){
			}else{
				statementAddrTo = Util.truncateString(statementAddrTo, MAXLEN_L192M01A_STATEMENTADDRTO);
			}
			l192m01a.setStatementAddrTo(statementAddrTo);	

			L192M01C l192m01c = new L192M01C();

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			}

			if (!CapString.isEmpty(params.getString("checkMan"))) {
				l192m01a.setCheckMan(params.getString("checkMan"));
			}

			if (!CapString.isEmpty(params.getString("leader"))) {
				l192m01a.setLeader(params.getString("leader"));
			}
			if (!CapString.isEmpty(params.getString("userItem1"))) {
				l192m01c.setUserItem1(params.getString("userItem1"));
			}

			l192m01c.setMainId(l192m01a.getMainId());
			l192m01a.setL192m01c(l192m01c);

			// lms1925Service.saveL192M01A(l192m01a);
			lms1925Service.saveNewDocument(l192m01a);
			includeData(l192m01a.getOid());

			JSONObject returnJsonObject = new JSONObject();
			returnJsonObject.put(EloanConstants.MAIN_OID,
					CapString.trimNull(l192m01a.getOid()));
			returnJsonObject.put(EloanConstants.MAIN_DOC_STATUS,
					CapString.trimNull(l192m01a.getDocStatus()));
			returnJsonObject.put(EloanConstants.MAIN_ID,
					CapString.trimNull(l192m01a.getMainId()));
			returnJsonObject.put(EloanConstants.MAIN_UID,
					CapString.trimNull(l192m01a.getUid()));
			returnJsonArray.add(returnJsonObject);
		}
		logger.debug("jsonarray : " + returnJsonArray.toString());
		result.set("custIDs", returnJsonArray.toString());
		return result;

		/*
		 * logger.debug("oid : " + l192m01a.getOid());
		 * 
		 * result.set(EloanConstants.MAIN_OID,
		 * CapString.trimNull(l192m01a.getOid()));
		 * result.set(EloanConstants.MAIN_DOC_STATUS, l192m01a.getDocStatus());
		 * result.set(EloanConstants.MAIN_ID,
		 * CapString.trimNull(l192m01a.getMainId()));
		 * result.set(EloanConstants.MAIN_UID,
		 * CapString.trimNull(l192m01a.getUid()));
		 * 
		 * return result;
		 */
	}

	/**
	 * 查詢授信業務工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));

		L192M01A meta = null;
		if (mainOid != null) {
			meta = lms1925Service.getL192M01A(mainOid);
		}
		switch (page) {
		case 1:
			result.add(new CapAjaxFormResult(meta.toJSONObject(
					new String[] { "ownBrId", "checkBase", "docStatus",
							"checkDate", "checkMan", "tNo", "leader", "wpNo",
							"mtDoc", "randomCode" }, null)));
			// result.set("ownBrName",
			// branchService.getBranchName(meta.getOwnBrId()));
			result.set("ownBrName", new BranchNameFormatter(branchService,
					ShowTypeEnum.IDSpaceName).reformat(meta.getOwnBrId()));

			break;
		case 2:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"custId", "dupNo", "custName", "tTel", "tAddr", "cdQ1",
					"cdQ2", "cdQ3", "statementAddrFrom", "statementAddrTo" },
					null)));

			// 因地址可能會有很多筆，所以先select 出來秀在畫面上，如果資料過長，提示使用者資料要先修正並儲存
			if (meta.getTAddr() == null || "".equals(meta.getTAddr().trim())) {
				String allAddress = this.getCustAddr(meta.getCustId(),
						meta.getDupNo());

				result.set("tAddr", CapString.halfWidthToFullWidth(allAddress));
			}
			if (meta.getStatementAddrTo() == null
					|| "".equals(meta.getStatementAddrTo().trim())) {
				String allAddress = this.getCustAddr(meta.getCustId(),
						meta.getDupNo());

				result.set("statementAddrTo",
						CapString.halfWidthToFullWidth(allAddress));
			}

			result.set("ownBrName",
					branchService.getBranchName(meta.getOwnBrId()));

			// List<C112S01A> datas = service.getC112S01A(meta);
			// String key = null;
			// for (C112S01A data : datas) {
			// key = data.getItem();
			// result.set(key, data.getReqFlag());
			// result.set("g" + key, data.getInFlag());
			// }
			// break;
			break;

		case 3:
			break;
		case 4:
			result.add(new CapAjaxFormResult(meta.toJSONObject(
					new String[] { "estUnit" }, null)));
			break;
		case 5:
			L192M01C l192m01c = meta.getL192m01c();

			Map<String, Object> custData = (Map<String, Object>) misCustdataService
					.findCustdataMapByMainId(meta.getCustId(), meta.getDupNo());
			if (custData != null && !custData.isEmpty()) {
				String buscd = (String) custData.get("BUSCD");
				if (isEnterpriseCustomers(buscd)) {
					// 判斷主要借款人是個人戶還是企業戶
					result.set("enterpriseCustomers", "Y");
				}
			}

			if (l192m01c != null) {
				result.add(new CapAjaxFormResult(l192m01c.toJSONObject(
						new String[] { "ck1", "ck2", "ck3", "ck4Date",
								"ck5Date", "ck5Amt", "ck6Date", "ck7Date",
								"ck8Date", "ck9", "ck10", "ck11Date", "ck12",
								"userItem1", "userCk1" }, null)));
			}

			break;
		case 6:
			result.add(new CapAjaxFormResult(meta.toJSONObject(new String[] {
					"gist", "processComm" }, null)));
			break;
		default:
		}

		// required information

		IBranch iBranch = branchService.getBranch(MegaSSOSecurityContext
				.getUnitNo());

		if (meta.getCreator() == null) {
			result.set("creator", MegaSSOSecurityContext.getUserName());
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
			result.set("updater", MegaSSOSecurityContext.getUserName());
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(CapDate
							.getCurrentTimestamp())));
		} else {
			result.set("creator", userService.getUserName(meta.getCreator()));
			result.set("createTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getCreateTime())));

			result.set("updater", userService.getUserName(meta.getUpdater()));
			result.set("updateTime", new BranchDateTimeFormatter(iBranch)
					.reformat(CapDate.parseToString(meta.getUpdateTime())));
		}
		result.set(EloanConstants.MAIN_OID, CapString.trimNull(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, CapString.trimNull(meta.getMainId()));
		result.set(EloanConstants.MAIN_UID, CapString.trimNull(meta.getUid()));
		result.set("docStatusCN",
				getMessage("docStatus." + meta.getDocStatus()));
		result.set("innerAudit", meta.getInnerAudit());
		return result;
	}

	private void truncate_addr(L192M01A l192m01a){
		l192m01a.setTAddr(Util.truncateString(l192m01a.getTAddr(), MAXLEN_L192M01A_TADDR));
		l192m01a.setStatementAddrFrom(Util.truncateString(l192m01a.getStatementAddrFrom(), MAXLEN_L192M01A_STATEMENTADDRFROM));
		l192m01a.setStatementAddrTo(Util.truncateString(l192m01a.getStatementAddrTo(), MAXLEN_L192M01A_STATEMENTADDRTO));
	}
	/**
	 * 儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult save(PageParameters params)
			throws CapException {

		L192M01A l192m01a = collectionData(params);
		truncate_addr(l192m01a);
		l192m01a.setRandomCode(IDGenerator.getRandomCode());
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "N"));
		lms1925Service.saveL192M01A(l192m01a);
		return query(params);

	}

	/**
	 * TEMP儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		// CapAjaxFormResult result = new CapAjaxFormResult();
		L192M01A l192m01a = collectionData(params);
		truncate_addr(l192m01a);
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		lms1925Service.saveL192M01A(l192m01a);
		return query(params);
	}

	/**
	 * 資料處理，將網頁上所submit的資料做基本轉換
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	private L192M01A collectionData(PageParameters params)
			throws CapException {

		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		L192M01A l192m01a = lms1925Service.getL192M01A(mainOid);

		// params = convertParameters(params);
		l192m01a = CapBeanUtil.map2Bean(params, l192m01a);

		switch (page) {
		case 1:
			if (l192m01a == null) {
				l192m01a = new L192M01A();
			}

			if (!CapString.isEmpty(params.getString("checkBase"))) {
				l192m01a.setCheckBase(CapDate.getDate(
						params.getString("checkBase"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckBase(null);
			}

			if (!CapString.isEmpty(params.getString("checkDate"))) {
				l192m01a.setCheckDate(CapDate.getDate(
						params.getString("checkDate"), "yyyy-MM-dd"));
			} else {
				l192m01a.setCheckDate(null);
			}
			break;
		case 5:
			L192M01C l192m01c = l192m01a.getL192m01c();
			if (l192m01c == null) {
				l192m01c = new L192M01C();
				l192m01c.setMainId(l192m01a.getMainId());
			}
			l192m01c.setCk1(params.getString("ck1"));
			l192m01c.setCk2(params.getString("ck2"));

			l192m01c.setCk3(params.getString("ck3"));

			if (!CapString.isEmpty(params.getString("ck4Date"))) {
				l192m01c.setCk4Date(CapDate.getDate(
						params.getString("ck4Date"), "yyyy-MM-dd"));
			} else {
				l192m01c.setCk4Date(null);
			}

			if (!CapString.isEmpty(params.getString("ck5Date"))) {
				l192m01c.setCk5Date(CapDate.getDate(
						params.getString("ck5Date"), "yyyy-MM-dd"));
			} else {
				l192m01c.setCk5Date(null);
			}

			if (!CapString.isEmpty(params.getString("ck5Amt"))
					&& CapString.isNumeric(params.getString("ck5Amt"))) {
				l192m01c.setCk5Amt(new BigDecimal(params.getString("ck5Amt")));
			} else {
				l192m01c.setCk5Amt(null);
			}

			if (!CapString.isEmpty(params.getString("ck6Date"))) {
				l192m01c.setCk6Date(CapDate.getDate(
						params.getString("ck6Date"), "yyyy-MM-dd"));
			} else {
				l192m01c.setCk6Date(null);
			}

			if (!CapString.isEmpty(params.getString("ck7Date"))) {
				l192m01c.setCk7Date(CapDate.getDate(
						params.getString("ck7Date"), "yyyy-MM-dd"));
			} else {
				l192m01c.setCk7Date(null);
			}

			if (!CapString.isEmpty(params.getString("ck8Date"))) {
				l192m01c.setCk8Date(CapDate.getDate(
						params.getString("ck8Date"), "yyyy-MM-dd"));
			} else {
				l192m01c.setCk8Date(null);
			}

			l192m01c.setCk9(params.getString("ck9"));
			l192m01c.setCk10(params.getString("ck10"));

			if (!CapString.isEmpty(params.getString("ck11Date"))) {
				l192m01c.setCk11Date(CapDate.getDate(
						params.getString("ck11Date"), "yyyy-MM-dd"));
			}

			l192m01c.setCk12(params.getString("ck12"));

			l192m01c.setUserItem1(params.getString("userItem1"));
			l192m01c.setUserCk1(params.getString("userCk1"));

			l192m01a.setL192m01c(l192m01c);

			break;
		default:
			break;
		}

		return l192m01a;

	}

	/**
	 * 引入授信業務工作底稿基本資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult doInclude(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		// MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String custId = params.getString("custId");
		// String dupNo = params.getString("dupNo");
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		// String brNo = user.getUnitNo();
		String mainOid = params.getString(EloanConstants.MAIN_OID);

		includeData(mainOid);
		return result;
	}

	/**
	 * 取得最近一份工作底稿資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getLatestAuditSheet(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String shtType = params.getString("shtType");
		String innerAudit = params.getString("innerAudit");
		String brNo = "Y".equals(innerAudit) ? user.getUnitNo() : params
				.getString("brId");

		L192M01A l192m01a = lms1925Service
				.getLatestL192M01byBrNoShtTypeInnerAudit(brNo, shtType,
						innerAudit);
		L192M01C l192m01c = null;
		if (l192m01a != null) {
			l192m01c = l192m01a.getL192m01c();
		}

		result.set(
				"leader",
				l192m01a == null ? ""
						: CapString.trimNull(l192m01a.getLeader()));
		result.set(
				"userItem1",
				l192m01c == null ? "" : CapString.trimNull(l192m01c
						.getUserItem1()));
		return result;

	}

	/**
	 * 取得客戶中文姓名資料
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult getMisCustData(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String custId_dupNo = CapString.trimNull(params.getString("custId"))
				.toUpperCase();

		if (custId_dupNo.length() == 0) {
			return result;
		}

		String custId = custId_dupNo.substring(0, custId_dupNo.length() - 1);
		String dupNo = custId_dupNo.substring(custId_dupNo.length() - 1,
				custId_dupNo.length());

		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findCustdataSelCname(custId, dupNo);
		if (custData != null && !custData.isEmpty()) {
			result.set("custName", (String) custData.get("CNAME"));
		}
		return result;
	}

	private String addSlash(String txt) {
		String dd = txt.substring(txt.length() - 2, txt.length());
		String mm = txt.substring(txt.length() - 4, txt.length() - 2);
		String yy = txt.substring(0, txt.length() - 4);
		return yy + "-" + mm + "-" + dd;

	}

	/**
	 * 引入授信業務基本資料
	 * 
	 * @param mainOid
	 *            key
	 */
	private void includeData(String mainOid) {

		L192M01A meta = lms1925Service.getL192M01A(mainOid);
		String custId = meta.getCustId();
		String dupNo = meta.getDupNo();
		String brNo = meta.getOwnBrId();
		String mainId = meta.getMainId();

		Map<String, Object> custData = (Map<String, Object>) misCustdataService
				.findCustdataMapByMainId(meta.getCustId(), meta.getDupNo());

		L192M01C l192m01c = null;
		if (custData != null && !custData.isEmpty()) {
			if (!isEnterpriseCustomers((String) custData.get("BUSCD"))) {
				// 企業戶，去撈資信簡表資料
				l192m01c = new L192M01C();
				HashMap<String, Object> cesData = (HashMap<String, Object>) eloandbBASEService
						.findC120M01A_selOrderByUpdateTime(meta.getOwnBrId(),
								meta.getCustId(), meta.getDupNo());
				if (cesData != null && !cesData.isEmpty()) {
					Date completeDate = (Date) cesData.get("COMPLETEDATE");
					l192m01c.setCk4Date(completeDate);
					l192m01c.setCk5Date(completeDate);
					l192m01c.setCk6Date(completeDate);
					l192m01c.setCk7Date(completeDate);
				}
			}
		}

		// 引入資信簡表資料

		Set<String> contractNo = new HashSet<String>();

		// CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// String custId = params.getString("custId");
		// String dupNo = params.getString("dupNo");
		// String mainId = params.getString(EloanConstants.MAIN_ID);
		// String brNo = user.getUnitNo();
		// String mainOid = params.getString(EloanConstants.MAIN_OID);

		logger.debug("custId : " + custId + " dupNo : " + dupNo);

		// 引進主要借款人資料
		// 先取得最新一份簽報書，取得main ID
		// L120M01A l120m01a = lms1925Service.getLatestCaseDateById(custId,
		// dupNo);
		//
		// String l120mainId = "";
		// if (l120m01a != null) {
		// l120mainId = l120m01a.getMainId();
		// }

		// List<L120S01A> l120s01as = lms1925Service.getL120S01A(l120mainId);

		// 借款人及連保人基本資料檔
		List<L192M01B> l192m01bs = new ArrayList<L192M01B>();

		// for (L120S01A l120s01a : l120s01as) {
		// L192M01B l192m01b = new L192M01B();
		// l192m01b.setMainId(mainId);
		// l192m01b.setCustId(l120s01a.getCustId());
		// l192m01b.setDupNo(l120s01a.getDupNo());
		// l192m01b.setCustType("1");
		//
		// //引自動用審核表之「借款人」，若借款人姓名未輸入，則依借款人統編引自客戶檔，國內法人戶一律引法定戶名，OBU客戶用英文名，海外客戶依MEGA
		// ID讀取客戶檔。
		// l192m01b.setCustName(l120s01a.getCustName());
		// //行業別，引自資信簡表
		// l192m01b.setPosi("");
		// l192m01b.setIncomeCurr(null);
		// l192m01b.setIncomeAmt(null);
		// l192m01bs.add(l192m01b);
		// }

		// 連帶保證人從已批覆額度明細表引入

		// 申請內容檔
		List<L192S01A> l192s01as = new ArrayList<L192S01A>();

		// 放款科目資料
		Map<String, String> dpCodes = misElacnmService.getDpCode();

		// 國內放款資料
		List<Map<String, Object>> lnf150data = misLNF150Service
				.findByCustIdDup(custId, dupNo, brNo);

		// 國內放款資料
		List<Map<String, Object>> lnf150data2 = misLNF150Service
				.findByCustIdDup2(custId, dupNo, brNo);

		// 海外放款資料
		// 海外dw資料，如果重覆序號為0就帶空白
		List<Map<String, Object>> asLndAvgOvsData = dwAslndavgovsService
				.getByCustIdBrNo(custId + ("0".equals(dupNo) ? " " : dupNo),
						brNo);

		// BigDecimal

		// boolean runFirst = true;

		// 處理有聯貸的案例
		if (lnf150data2 != null && !lnf150data2.isEmpty()) {

			String tmp_loan_no_m = "";
			BigDecimal tempQUOTAPRV = BigDecimal.ZERO;
			BigDecimal tempLOANBAL = BigDecimal.ZERO;

			String accNo = "";// 帳號
			String quotaNo = "";// 額度序號
			String subject = "";// 科目
			Date balDate = null;// 餘額日期
			String quotaCurr = "";// 額度幣別
			String balCurr = "";// 餘額幣別
			BigDecimal quotaAmt = BigDecimal.ZERO;// 額度金額
			BigDecimal balAmt = BigDecimal.ZERO;// 餘額金額
			Date fromDate = null;// 契約起日(動用日)
			Date endDate = null;// 契約迄日

			for (Map<String, Object> data : lnf150data2) {
				String lnf024_loan_no_m = (String) data.get("LNF024_LOAN_NO_M");
				if ("".equals(tmp_loan_no_m)
						|| tmp_loan_no_m.equals(lnf024_loan_no_m)) {
					accNo = lnf024_loan_no_m;// 帳號
					quotaNo = (String) data.get("QUOTANO");// 額度序號
					subject = (String) data.get("LOANTP");// 科目
					balDate = (Date) data.get("UPDTDT");// 餘額日期
					if (dpCodes.containsKey(subject)) {
						subject = dpCodes.get(subject);
					}
					quotaCurr = (String) data.get("QUOTACURR");// 額度幣別
					balCurr = (String) data.get("CURR");// 餘額幣別
					tempQUOTAPRV = (BigDecimal) data.get("QUOTAPRV");// 額度金額
					tempLOANBAL = (BigDecimal) data.get("LOANBAL");// 餘額金額
					fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
					endDate = (Date) data.get("DUEDT");// 契約迄日

					quotaAmt = quotaAmt.add(tempQUOTAPRV);
					balAmt = balAmt.add(tempLOANBAL);
				} else {

					L192S01A l192s01a = new L192S01A();
					l192s01a.setMainId(mainId);
					l192s01a.setAccNo(accNo);
					l192s01a.setQuotaNo(quotaNo);
					l192s01a.setQuotaCurr(quotaCurr);
					l192s01a.setBalCurr(balCurr);
					l192s01a.setQuotaAmt(quotaAmt);
					l192s01a.setBalAmt(balAmt);
					l192s01a.setFromDate(fromDate);
					l192s01a.setEndDate(endDate);
					l192s01a.setSubject(subject);
					l192s01a.setBalDate(balDate);
					this.getELF346dta(custId, dupNo, quotaNo, l192s01a);
					l192s01a.setCreator(user.getUserId());
					l192s01a.setCreateTime(new Date());
					l192s01as.add(l192s01a);

					contractNo.add(l192s01a.getQuotaNo());

					quotaAmt = BigDecimal.ZERO;
					balAmt = BigDecimal.ZERO;

					accNo = lnf024_loan_no_m;// 帳號
					quotaNo = (String) data.get("QUOTANO");// 額度序號
					subject = (String) data.get("LOANTP");// 科目
					balDate = (Date) data.get("UPDTDT");// 餘額日期
					if (dpCodes.containsKey(subject)) {
						subject = dpCodes.get(subject);
					}
					quotaCurr = (String) data.get("QUOTACURR");// 額度幣別
					balCurr = (String) data.get("CURR");// 餘額幣別
					tempQUOTAPRV = (BigDecimal) data.get("QUOTAPRV");// 額度金額
					tempLOANBAL = (BigDecimal) data.get("LOANBAL");// 餘額金額
					fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
					endDate = (Date) data.get("DUEDT");// 契約迄日
					quotaAmt = quotaAmt.add(tempQUOTAPRV);
					balAmt = balAmt.add(tempLOANBAL);

				}
				tmp_loan_no_m = lnf024_loan_no_m;
			}

			L192S01A l192s01a = new L192S01A();
			l192s01a.setMainId(mainId);
			l192s01a.setAccNo(accNo);
			l192s01a.setQuotaNo(quotaNo);
			l192s01a.setQuotaCurr(quotaCurr);
			l192s01a.setBalCurr(balCurr);
			l192s01a.setQuotaAmt(quotaAmt);
			l192s01a.setBalAmt(balAmt);
			l192s01a.setFromDate(fromDate);
			l192s01a.setEndDate(endDate);
			l192s01a.setSubject(subject);
			l192s01a.setBalDate(balDate);
			this.getELF346dta(custId, dupNo, quotaNo, l192s01a);
			l192s01a.setCreator(user.getUserId());
			l192s01a.setCreateTime(new Date());
			l192s01as.add(l192s01a);

			contractNo.add(l192s01a.getQuotaNo());

		}

		if (lnf150data != null && !lnf150data.isEmpty()) {
			for (Map<String, Object> data : lnf150data) {

				String accNo = (String) data.get("LOANNO");// 帳號
				String quotaNo = (String) data.get("QUOTANO");// 額度序號
				String subject = (String) data.get("LOANTP");// 科目
				Date balDate = (Date) data.get("UPDTDT");// 餘額日期
				if (dpCodes.containsKey(subject)) {
					subject = dpCodes.get(subject);
				}

				// 儲存額度序號 去撈主從債務人檔
				contractNo.add(quotaNo);

				String quotaCurr = (String) data.get("QUOTACURR");// 額度幣別
				String balCurr = (String) data.get("CURR");// 餘額幣別
				BigDecimal quotaAmt = (BigDecimal) data.get("QUOTAPRV");// 額度金額
				BigDecimal balAmt = (BigDecimal) data.get("LOANBAL");// 餘額金額
				Date fromDate = (Date) data.get("APRVDT");// 契約起日(動用日)
				Date endDate = (Date) data.get("DUEDT");// 契約迄日

				L192S01A l192s01a = new L192S01A();
				l192s01a.setMainId(mainId);
				l192s01a.setAccNo(accNo);
				l192s01a.setQuotaNo(quotaNo);
				l192s01a.setQuotaCurr(quotaCurr);
				l192s01a.setBalCurr(balCurr);
				l192s01a.setQuotaAmt(quotaAmt);
				l192s01a.setBalAmt(balAmt);
				l192s01a.setFromDate(fromDate);
				l192s01a.setEndDate(endDate);
				l192s01a.setSubject(subject);
				l192s01a.setBalDate(balDate);

				// 取得擔保品存執本票資料
				this.getELF346dta(custId, dupNo, quotaNo, l192s01a);

				l192s01a.setCreator(user.getUserId());
				l192s01a.setCreateTime(new Date());

				l192s01as.add(l192s01a);
			}
		}

		// 海外放款資料
		if (asLndAvgOvsData != null && !asLndAvgOvsData.isEmpty()) {
			for (Map<String, Object> asLndAvgOvs : asLndAvgOvsData) {
				String accNo = (String) asLndAvgOvs.get("ACCT_KEY");// 帳號
				String quotaNo = (String) asLndAvgOvs.get("FACT_CONTR");// 額度序號
				Date balDate = (Date) asLndAvgOvs.get("DW_DATA_SRC_DT");// 餘額日期
																		// ???
				String subject = (String) asLndAvgOvs.get("GL_AC_KEY");// 海外用會計科目
				if (dpCodes.containsKey(subject)) {
					subject = dpCodes.get(subject);
				}

				// 儲存額度序號 去撈主從債務人檔
				contractNo.add(quotaNo);

				String quotaCurr = (String) asLndAvgOvs.get("FACT_CUR_CD");// 額度幣別
				String balCurr = (String) asLndAvgOvs.get("CUR_CD");// 餘額幣別

				BigDecimal quotaAmt = (BigDecimal) asLndAvgOvs.get("FACT_AMT");// 額度金額
				BigDecimal balAmt = (BigDecimal) asLndAvgOvs.get("LN_BAL");// 餘額金額

				Date fromDate = (Date) asLndAvgOvs.get("CONTR_START_DT");// 契約起日(動用日)
				Date endDate = (Date) asLndAvgOvs.get("CONTR_DUE_DT");// 契約迄日

				L192S01A l192s01a = new L192S01A();
				l192s01a.setMainId(mainId);
				l192s01a.setAccNo(accNo);
				l192s01a.setQuotaNo(quotaNo);
				l192s01a.setQuotaCurr(quotaCurr);
				l192s01a.setBalCurr(balCurr);
				l192s01a.setQuotaAmt(quotaAmt);
				l192s01a.setBalAmt(balAmt);
				l192s01a.setFromDate(fromDate);
				l192s01a.setEndDate(endDate);
				l192s01a.setSubject(subject);
				l192s01a.setBalDate(balDate);

				/**
				 * 海外擔保品資料目前找不到，先不串
				 */

				l192s01a.setCreator(user.getUserId());
				l192s01a.setCreateTime(new Date());

				l192s01as.add(l192s01a);
			}
		}

		// 先篩選出同樣的資料，避免重覆的資料一真去select db
		// L192M01B 有實作hashCode 和 equals ，custid, dupno,custype,main 為unique
		for (String cntrNo : contractNo) {

			List<Map<String, Object>> ellngteeDatas = misELLNGTEEService
					.getByCustIdCntrNo(custId, dupNo, cntrNo);

			for (Map<String, Object> ellngteeData : ellngteeDatas) {
				String lngeId = (String) (ellngteeData.get("LNGEID") == null ? ""
						: ellngteeData.get("LNGEID"));
				String dupNo1 = (String) (ellngteeData.get("DUPNO1") == null ? ""
						: ellngteeData.get("DUPNO1"));
				String lngeFlag = (String) (ellngteeData.get("LNGEFLAG") == null ? ""
						: ellngteeData.get("LNGEFLAG"));
				// String lngeNm = (String) (ellngteeData.get("LNGENM") == null
				// ? ""
				// : ellngteeData.get("LNGENM"));

				L192M01B l192m01b = new L192M01B();
				l192m01b.setMainId(mainId);
				l192m01b.setCustId(lngeId);
				l192m01b.setDupNo(dupNo1);

				// C: 共同借款人
				// D: 共同發票人　
				// E: 票據債務人（指金融交易之擔保背書）
				// G: 連帶保證人，擔保品提供人兼連帶保證人
				// L: 連帶借款人，連帶債務人，擔保品提供人兼連帶債務人
				// S: 擔保品提供人
				// N: ㄧ般保證人
				if ("C".equals(lngeFlag)) {
					// 1.借款人, 2.連保人
					l192m01b.setCustType("1");
				} else {
					l192m01b.setCustType("2");
				}

				// l192m01b.setCustName(lngeNm);
				l192m01bs.add(l192m01b);
			}
		}

		L192M01B main = new L192M01B();
		main.setMainId(mainId);
		main.setCustId(meta.getCustId());
		main.setDupNo(meta.getDupNo());
		main.setCustType("1");
		l192m01bs.add(main);

		// 清除重覆的資料，有實作L192M01B 的hashCode 和equals
		Set<L192M01B> uniqueTmp = new HashSet<L192M01B>(l192m01bs);
		List<L192M01B> l192m01bs2 = new ArrayList<L192M01B>();

		for (L192M01B l192m01b : uniqueTmp) {

			String id = l192m01b.getCustId();
			String dup = l192m01b.getDupNo();

			Map<String, Object> data = (Map<String, Object>) misCustdataService
					.findCustdataMapByMainId(id, dup);

			if (data != null) {
				String buscd = (String) data.get("BUSCD");
				String custName = (String) data.get("CNAME");
				String posi = "";

				if (!isEnterpriseCustomers(buscd)) {

					C120S01B c120s01b = lms1925Service
							.getLatestC120S01BByCustId(id, dup);
					if (c120s01b != null) {
						l192m01b.setIncomeCurr(c120s01b.getPayCurr());
						l192m01b.setIncomeAmt(c120s01b.getPayAmt());
						CodeType codeType = codeTypeService
								.findByCodeTypeAndCodeValue(
										"lms1205s01_jobType1", CapString
												.trimNull(c120s01b
														.getJobType1()));
						String posiName = codeType != null ? CapString
								.trimNull(codeType.getCodeDesc()) : "";

						int posiBtyeLength = posiName.getBytes().length;

						posiName = new String(posiName.getBytes(), 0,
								posiBtyeLength <= 60 ? posiBtyeLength : 60);

						l192m01b.setPosi(posiName);
					}

				} else {
					// 企業戶抓 行業別 就行了
					posi = (String) data.get("ECONM");
					l192m01b.setPosi(posi);
				}
				l192m01b.setCustName(custName);

			}

			l192m01bs2.add(l192m01b);
		}

		// 擔保品資料
		List<L192S02A> l192s02as = this.getCmsData(custId, dupNo, brNo, mainId);

		lms1925Service
				.includeData(meta, l192m01bs2, null, l192s01as, l192s02as);

	}

	/**
	 * 取得客戶地址資料
	 * 
	 * @param custId
	 *            customer id
	 * @param dupNo
	 *            dupNo
	 * @return address
	 */
	private String getCustAddr(String custId, String dupNo) {

		List<Map<String, Object>> custAddrs = misELCUS21Service
				.getCustAddressForLas(custId, dupNo);

		StringBuffer allAddress = new StringBuffer("");
		for (int i = 0; i < custAddrs.size(); i++) {
			Map<String, Object> custAddr = custAddrs.get(i);
			// J-105-0233-001 Web
			// e-Loan授信系統「註冊地址」及「聯絡地址」引進【0024】之「公司所在地」之建檔資料時可引進英文地址
			// String addrzip = custAddr.get("ADDRZIP") == null ? ""
			// : (String) custAddr.get("ADDRZIP");
			// String cityr = custAddr.get("CITYR") == null ? ""
			// : (String) custAddr.get("CITYR");
			// String townr = custAddr.get("TOWNR") == null ? ""
			// : (String) custAddr.get("TOWNR");
//			String addrr = custAddr.get("ADDRR") == null ? ""
//					: (String) custAddr.get("ADDRR");
			
			String addrr = custAddr.get("FULLADDR") == null ? ""
					: (String) custAddr.get("FULLADDR");
			
			allAddress.append(new StringBuffer().append(addrr.trim()));
			if ((i + 1) != custAddrs.size()) {
				allAddress.append(';');
			}
		}
		return allAddress.toString();

	}

	// public IResult print(PageParameters params)
	// throws CapException {
	// CapAjaxFormResult result = new CapAjaxFormResult();
	// lms1925r01RptService.setReportData(params);
	// lms1925r02RptService.setReportData(params);
	// return result;
	// }

	/**
	 * 判斷是否為企業戶
	 * 
	 * @param buscd
	 * @return
	 */
	private boolean isEnterpriseCustomers(String buscd) {
		if (LMSUtil.isBusCode_060000_130300(buscd)) {
			return false;
		}
		return true;
	}

	/**
	 * 取得擔保品存執本票資料
	 * 
	 * @param custId
	 *            customer id
	 * @param dupNo
	 *            dupNo
	 * @param quotaNo
	 *            額度序號
	 * @param l192s01a
	 *            L192S01A
	 */
	private void getELF346dta(String custId, String dupNo, String quotaNo,
			L192S01A l192s01a) {

		List<Map<String, Object>> elf346dpData = misELF346Service
				.getELF346DPChk(custId, dupNo);

		BigDecimal tRate = null;
		BigDecimal tAmt = BigDecimal.ZERO;
		StringBuffer allIsNum = new StringBuffer();
		// String allIsNum = "";

		// 儲存名字，利用set的，只儲不同的名字
		Set<String> allIsNumSet = new HashSet<String>();
		TWNDate checkDate = null;

		if (elf346dpData != null && !elf346dpData.isEmpty()) {

			for (Map<String, Object> elf346 : elf346dpData) {
				String elf346CntrNo = (String) elf346.get("CNTRNO");
				if (quotaNo.equals(elf346CntrNo)) {
					String _checkDate = (String) elf346.get("ISUDT");// 發票日(民國年)
					_checkDate = addSlash(_checkDate);
					checkDate = new TWNDate();
					checkDate.setTime(_checkDate);

					String tCurr = (String) elf346.get("CURR");
					if (tCurr != null && tCurr.length() != 0) {
						Map<String, Object> currMap = misRatetblService
								.getNewestByCurr(tCurr);
						tRate = (BigDecimal) currMap.get("ENDRATE");
					} else {
						// 抓不到幣別資料就預設為1
						tRate = new BigDecimal("1.0");
					}

					BigDecimal amt = ((BigDecimal) elf346.get("AMT"))
							.multiply(tRate);
					tAmt = tAmt.add(amt);
					String name = (String) elf346.get("ISUNM");
					allIsNumSet.add(name);

				}
			}
		}

		int tmpCount = 1;
		int allIsNumSetCount = allIsNumSet.size();
		for (String name : allIsNumSet) {
			if (tmpCount <= 3) {
				allIsNum.append(name);
				if (tmpCount < 3 && tmpCount < allIsNumSetCount) {
					allIsNum.append(',');
				}
			}
			tmpCount++;
		}
		
		//稽核上傳太長造成EXCEPTION
		if (tmpCount > 4) {
			allIsNum.append("...等" + (tmpCount-1) + "筆");
		}

		l192s01a.setCheckDate(checkDate);
		l192s01a.setCheckAmt(tAmt.divide(new BigDecimal(10000)));
		l192s01a.setCheckCurr("TWD");
		l192s01a.setEndorser(Util.truncateToFitUtf8ByteLength(allIsNum.toString(), 120));
		
	}

	/**
	 * 註記是否列印對帳單
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	public IResult printMark(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String printMark = params.getString("printMark");
		L192M01A meta = lms1925Service.getL192M01A(mainOid);
		if (meta != null) {
			lms1925Service.printMark(meta, printMark);
		}
		return result;
	}

	/**
	 * 判斷使用者是否有EL02角色
	 * 
	 * @return
	 */
	private boolean hasEL02() {
		Set<String> eloanRoles = MegaSSOSecurityContext.getEloanRoles();

		for (String role : eloanRoles) {
			if (role != null && role.endsWith("EL02")) {
				return true;
			}
		}

		return false;
	}

	private List<L192S02A> getCmsData(String custId, String dupNo, String brNo,
			String mainId) { // 引入擔保品資料

		// 取得已設定擔保品資料
		List<Map<String, Object>> cmsDatas = eloandbcmsBASEService
				.getSetDataByCustIdAndBranch(custId, dupNo, brNo);

		// 擔保品資料
		List<L192S02A> l192s02as = new ArrayList<L192S02A>();
		for (Map<String, Object> cmsData : cmsDatas) {
			L192S02A l192s02a = new L192S02A();

			String cmsMainId = MapUtils.getString(cmsData, "mainId", "");
			String collTyp1 = MapUtils.getString(cmsData, "collTyp1", "");
			String collTyp2 = MapUtils.getString(cmsData, "collTyp2", "");

			String cmsName = "";

			if ("01".equals(collTyp1)) {
				String target = "";
				int lmsCount = 0;
				List<Map<String, Object>> landDatas = eloandbcmsBASEService
						.getC101M03ByMainId(cmsMainId);
				List<Map<String, Object>> buildDatas = eloandbcmsBASEService
						.getC101M04ByMainId(cmsMainId);
				lmsCount = landDatas.size() + buildDatas.size();
				if (CollectionUtils.isEmpty(landDatas)) {
					if (!CollectionUtils.isEmpty(buildDatas)) {
						for (Map<String, Object> buildData : buildDatas) {
							target = MapUtils.getString(buildData, "target");
						}
					}
				} else {
					for (Map<String, Object> landData : landDatas) {
						target = MapUtils.getString(landData, "target");
						break;
					}
				}

				cmsName += cmsName + target;

				if (lmsCount >= 2) {
					cmsName += "等" + lmsCount + "筆";
				}

				logger.debug("cmsName : " + cmsName);

				String ownerNames = "";
				List<Map<String, Object>> ownerDatas = eloandbcmsBASEService
						.getC101S03BByMainId(cmsMainId);
				Set<String> ownerNameSet = new HashSet<String>();
				for (Map<String, Object> ownerData : ownerDatas) {
					ownerNameSet.add((String) ownerData.get("ownerNm"));
				}

				if (CollectionUtils.isNotEmpty(ownerNameSet)) {
					int allCount = ownerNameSet.size();
					int tmpCount = 0;
					for (String ownerName : ownerNameSet) {
						tmpCount++;
						if (tmpCount > 4) {
							break;
						}

						if (tmpCount < 4 && allCount != tmpCount) {
							ownerNames = ownerNames + ownerName + "、";
						} else {
							ownerNames = ownerNames + ownerName;
						}

					}

					if (allCount > 4) {
						ownerNames += "等" + allCount + "筆";
					}
				}
				// 不動產保險資料
				List<Map<String, Object>> insuranceDatas = eloandbcmsBASEService
						.getC101M08ByMainId(cmsMainId);
				/*
					參考MIS.COLLINS 保險種類，同樣代號(例如32)，代表不同意義{不動產:32其他保險}{動產: 32汽車保險}
					● 不動產的「保險種類」值域select * from com.bcodetype where codetype='cms1010_insType' and locale='zh_TW' order by codeorder
					● 動產的「保險種類」值域select * from com.bcodetype where codetype='insType' and locale='zh_TW' order by codeorder
				*/
				Map<String, String> insType = codeTypeService
						.findByCodeType("cms1010_insType");

				String insDesc = "";
				Date insBDate = null;
				Date insEDate = null;
				if (CollectionUtils.isNotEmpty(insuranceDatas)) {
					for (Map<String, Object> insuranceData : insuranceDatas) {
						int insTypes = (Integer) MapUtils.getObject(
								insuranceData, "insType");

						String insTitle = "";
						List<String> ins = Util.valueOfCheckBoxOptionList(
								insTypes, 11);

						for (int i = 0; i < ins.size(); i++) {
							if (i == 0) {
								insTitle = insTitle + insType.get(ins.get(i));
							} else {
								insTitle = insTitle + ","
										+ insType.get(ins.get(i));
							}
						}
						// System.out.println(insTitle);
						BigDecimal insAmt = (BigDecimal) MapUtils.getObject(
								insuranceData, "insAmt", BigDecimal.ZERO);
						if (insAmt != null) {
							insAmt = insAmt.divide(BigDecimal.valueOf(10000));
						}

						insDesc = insTitle + insAmt + "萬";

						insBDate = (Date) MapUtils.getObject(insuranceData,
								"insBDate", null);
						insEDate = (Date) MapUtils.getObject(insuranceData,
								"insEDate", null);

					}
				}

				l192s02a.setGteName(cmsName);
				l192s02a.setOwner(ownerNames);
				l192s02a.setInsurance(insDesc);
				l192s02a.setInsDateFrom(insBDate);
				l192s02a.setInsDateEnd(insEDate);

			} else if ("02".equals(collTyp1)) {
				Map<String, String> cms1020_collTyp2Map = codeTypeService
						.findByCodeType("cms1020_collTyp2");
				String target = MapUtils.getString(cms1020_collTyp2Map,
						collTyp2, "");
				cmsName += cmsName + target;

				// 動產保險資料
				List<Map<String, Object>> insuranceDatas = eloandbcmsBASEService
						.getC102S01DByMainId(cmsMainId);

				Map<String, String> insType = codeTypeService
						.findByCodeType("insType");

				String insDesc = "";
				Date insBDate = null;
				Date insEDate = null;
				if (CollectionUtils.isNotEmpty(insuranceDatas)) {
					for (Map<String, Object> insuranceData : insuranceDatas) {
						int insTypes = (Integer) MapUtils.getObject(
								insuranceData, "insType");

						String insTitle = "";
						List<String> ins = Util.valueOfCheckBoxOptionList(
								insTypes, 11);

						for (int i = 0; i < ins.size(); i++) {
							if (i == 0) {
								insTitle = insTitle + insType.get(ins.get(i));
							} else {
								insTitle = insTitle + ","
										+ insType.get(ins.get(i));
							}
						}
						// System.out.println(insTitle);
						BigDecimal insAmt = (BigDecimal) MapUtils.getObject(
								insuranceData, "insAmt", BigDecimal.ZERO);
						if (insAmt != null) {
							insAmt = insAmt.divide(BigDecimal.valueOf(10000));
						}

						insDesc = insTitle + insAmt + "萬";

						insBDate = (Date) MapUtils.getObject(insuranceData,
								"insBDate", null);
						insEDate = (Date) MapUtils.getObject(insuranceData,
								"insEDate", null);

					}
				}

				l192s02a.setGteName(cmsName);
				l192s02a.setInsurance(insDesc);
				l192s02a.setInsDateFrom(insBDate);
				l192s02a.setInsDateEnd(insEDate);

			} else if ("03".equals(collTyp1)) {
				Map<String, String> cms1030_collTyp2 = codeTypeService
						.findByCodeType("cms1030_collTyp2");

				String target = MapUtils.getString(cms1030_collTyp2, collTyp2,
						"");

				cmsName += cmsName + target;
				if ("01".equals(collTyp2)) {

					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01AByMainId(cmsMainId);
					// 抓取存單號碼

					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							Map<String, String> cms1030_flg = codeTypeService
									.findByCodeType("cms1030_flg");
							String flag = MapUtils.getString(c103, "flg", "");
							String flagName = MapUtils.getString(cms1030_flg,
									flag, "");
							String depNo = MapUtils
									.getString(c103, "depNo", "");

							cmsName += flagName + "存單號碼" + depNo;

							l192s02a.setGteName(cmsName);
							break;
						}
					}

				} else if ("02".equals(collTyp2)) {
					// 國庫券
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01BByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							String kind = MapUtils.getString(c103, "kind", "");
							// 甲種，乙種代碼
							Map<String, String> C103S01B_kind = codeTypeService
									.findByCodeType("C103S01B_kind");

							String kindName = MapUtils.getString(C103S01B_kind,
									kind, "");

							cmsName += kindName + name;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("03".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String bondNm = "";
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01CByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							bondNm = MapUtils.getString(c103, "bondNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						l192s02a.setGteName(bondNm + ":數量(" + qnty.intValue()
								+ ")");
					}
				} else if ("04".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String bondNm = "";
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01DByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							bondNm = MapUtils.getString(c103, "bondNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");

							break;
						}
						l192s02a.setGteName(bondNm + ":數量(" + qnty.intValue()
								+ ")");
					}
				} else if ("05".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					BigDecimal parVal = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01EByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							parVal = (BigDecimal) MapUtils.getObject(c103,
									"parVal");
							break;
						}
						l192s02a.setGteName(name + ":面額(" + parVal + ")");
					}
				} else if ("06".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String cbNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01FByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							cbNm = MapUtils.getString(c103, "cbNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						l192s02a.setGteName(name + ":(" + cbNm + "數量" + qnty
								+ ")");

					}
				} else if ("07".equals(collTyp2)) {

					String stkNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01GByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							stkNm = MapUtils.getString(c103, "stkNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							cmsName += ":" + stkNm + "(鑑價數量:" + qnty + ")";
							break;
						}
						l192s02a.setGteName(cmsName);
					}

				} else if ("08".equals(collTyp2)) {
					String name = MapUtils.getString(cms1030_collTyp2,
							collTyp2, "");
					String fundNm = null;
					BigDecimal qnty = null;
					List<Map<String, Object>> c103Data = eloandbcmsBASEService
							.getC103S01HByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c103Data)) {
						for (Map<String, Object> c103 : c103Data) {
							fundNm = MapUtils.getString(c103, "fundNm", "");
							qnty = (BigDecimal) MapUtils
									.getObject(c103, "Qnty");
							break;
						}
						cmsName += cmsName + name + ":" + fundNm + "數量:" + qnty
								+ ")";
						l192s02a.setGteName(cmsName);
					}
				}
			} else if ("04".equals(collTyp1)) {
				List<Map<String, Object>> c104Data = eloandbcmsBASEService
						.getC104S01ByMainId(cmsMainId);
				String trstNm = null;
				BigDecimal qnty = null;
				if (CollectionUtils.isNotEmpty(c104Data)) {
					for (Map<String, Object> c104 : c104Data) {
						trstNm = MapUtils.getString(c104, "trstNm", "");
						qnty = (BigDecimal) MapUtils.getObject(c104, "Qnty");
						break;
					}
					cmsName += "倉單:" + trstNm + "(數量" + qnty + ")";
					l192s02a.setGteName(cmsName);
				}

			} else if ("05".equals(collTyp1)) {
				Map<String, String> cms1052_collTyp2 = codeTypeService
						.findByCodeType("cms1052_collTyp2");
				if ("01".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					// String kind = MapUtils.getString(map, key)

					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01AByMainId(cmsMainId);
					String curr = null;
					BigDecimal appVal = null;

					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							Map<String, String> c105s01a_grtKind = codeTypeService
									.findByCodeType("C105S01A_grtKind");
							String kind = MapUtils.getString(c105, "grtKind",
									"");
							String kindName = MapUtils.getString(
									c105s01a_grtKind, kind, "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");

							cmsName += name + "," + "種類:" + kindName + " "
									+ curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("02".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtDept = null;
					String grtKind = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01BByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtDept = MapUtils.getString(c105, "grtDept", "");
							grtKind = MapUtils.getString(c105, "grtKind", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");

							cmsName += name + ":" + grtDept + ",種類:" + grtKind
									+ " " + curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				} else if ("03".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtDept = null;
					String grtKind = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01CByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtDept = MapUtils.getString(c105, "grtDept", "");
							grtKind = MapUtils.getString(c105, "grtKind", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");
							cmsName += name + ":" + grtDept + ",(項目:" + grtKind
									+ " " + curr + appVal;

							break;
						}

					}
				} else if ("04".equals(collTyp2)) {
					String name = MapUtils.getString(cms1052_collTyp2,
							collTyp2, "");
					String grtNm = null;
					String grtItem = null;
					String curr = null;
					BigDecimal appVal = null;
					List<Map<String, Object>> c105Data = eloandbcmsBASEService
							.getC105S01DByMainId(cmsMainId);
					if (CollectionUtils.isNotEmpty(c105Data)) {
						for (Map<String, Object> c105 : c105Data) {
							grtNm = MapUtils.getString(c105, "grtNm", "");
							grtItem = MapUtils.getString(c105, "grtItem", "");
							curr = MapUtils.getString(c105, "curr", "");
							appVal = (BigDecimal) MapUtils.getObject(c105,
									"appVal");
							cmsName += name + ":" + grtNm + ",(方式:" + grtItem
									+ " " + curr + appVal;
							break;
						}
						l192s02a.setGteName(cmsName);
					}
				}
			} else if ("06".equals(collTyp1)) {
				List<Map<String, Object>> c106Data = eloandbcmsBASEService
						.getC106S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c106Data)) {
					for (Map<String, Object> c106 : c106Data) {
						String kind = MapUtils.getString(c106, "kind", "");
						String ntType = MapUtils.getString(c106, "ntType", "");
						String ntNo = MapUtils.getString(c106, "ntNo", "");

						if ("1".equals(kind)) {
							// "額度本票：";
							cmsName += "額度本票：";
						} else if ("2".equals(kind)) {
							// "備償票據：";
							cmsName += "備償票據：";
						}

						if ("1".equals(ntType)) {
							// "本票(票據號碼：";
							cmsName += "本票(票據號碼：" + ntNo + ")";
						} else if ("2".equals(ntType)) {
							// "匯票(票據號碼：";
							cmsName += "匯票(票據號碼：" + ntNo + ")";
						} else if ("3".equals(ntType)) {
							// "支票(票據號碼：";
							cmsName += "支票(票據號碼：" + ntNo + ")";
						}
						l192s02a.setGteName(cmsName);
						break;
					}
				}
			} else if ("07".equals(collTyp1)) {
				List<Map<String, Object>> c107Data = eloandbcmsBASEService
						.getC107S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c107Data)) {
					for (Map<String, Object> c107 : c107Data) {
						String ntType = MapUtils.getString(c107, "ntType", "");
						String isuNm = MapUtils.getString(c107, "isuNm", "");
						String ntNo = MapUtils.getString(c107, "ntNo", "");

						cmsName += ntType.equals("1") ? "本票" : "匯票";
						cmsName += "(發票人名稱：" + isuNm + "　票據號碼：" + ntNo;
						break;
					}
					l192s02a.setGteName(cmsName);

				}
			} else if ("08".equals(collTyp1)) {
				Map<String, String> cms1020_collTyp2Map = codeTypeService
						.findByCodeType("cms1020_collTyp2");
				String target = MapUtils.getString(cms1020_collTyp2Map,
						collTyp2, "");

				List<Map<String, Object>> c108Data = eloandbcmsBASEService
						.getC108S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c108Data)) {
					for (Map<String, Object> c108 : c108Data) {
						String collNm = MapUtils.getString(c108, "collNm", "");
						String unit = MapUtils.getString(c108, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c108,
								"qnty");

						cmsName += "信托佔有(" + target + ")" + collNm + " " + qnty
								+ unit;
						break;
					}
					l192s02a.setGteName(cmsName);
				}

			} else if ("09".equals(collTyp1)) {

				List<Map<String, Object>> c109Data = eloandbcmsBASEService
						.getC109S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c109Data)) {
					for (Map<String, Object> c109 : c109Data) {
						String collNm = MapUtils.getString(c109, "collNm", "");
						String unit = MapUtils.getString(c109, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c109,
								"qnty");
						cmsName += "參貸他行：" + collNm + " " + qnty + unit;
						break;
					}
					l192s02a.setGteName(cmsName);
				}
				// 其它
			} else if ("10".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01AByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {
						String collNm = MapUtils.getString(c110, "collNm", "");
						String unit = MapUtils.getString(c110, "unit", "");
						BigDecimal qnty = (BigDecimal) MapUtils.getObject(c110,
								"qnty");

						cmsName += "其他擔保品：" + collNm + " " + qnty + unit;
						break;
					}

				}

				// 反面承諾
			} else if ("11".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01BByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {

						break;
					}
					cmsName += "反面承諾";
					l192s02a.setGteName(cmsName);
				}

				// 浮動擔保抵押
			} else if ("12".equals(collTyp1)) {
				List<Map<String, Object>> c110Data = eloandbcmsBASEService
						.getC110S01CByMainId(cmsMainId);
				if (CollectionUtils.isNotEmpty(c110Data)) {
					for (Map<String, Object> c110 : c110Data) {
						String collName = MapUtils.getString(c110, "collName",
								"");
						cmsName += "其他擔保品：" + collName;
						break;
					}
					l192s02a.setGteName(cmsName);
				}
			}

			Date estData = (Date) MapUtils.getObject(cmsData, "estDate", null);
			String currCd = MapUtils.getString(cmsData, "currCd", "");
			BigDecimal appAmt = (BigDecimal) MapUtils.getObject(cmsData,
					"appAmt", null);
			BigDecimal loanAmt = (BigDecimal) MapUtils.getObject(cmsData,
					"loanAmt", null);

			// 設定資料檔
			Map<String, Object> setData = eloandbcmsBASEService
					.getC100M03ByMainId(cmsMainId);

			Date setDate = (Date) MapUtils.getObject(setData, "setDate", null);
			Date setDateFrom = (Date) MapUtils.getObject(setData, "SETBDATE",
					null);
			Date setDateEnd = (Date) MapUtils.getObject(setData, "SETEDATE",
					null);
			BigDecimal setOdr = (BigDecimal) MapUtils.getObject(setData,
					"setOdr", null);
			String setCurr = MapUtils.getString(setData, "setCurr", "");
			BigDecimal setAmt = (BigDecimal) MapUtils.getObject(setData,
					"setAmt", null);

			l192s02a.setEstDate(estData);
			l192s02a.setEstCurr(currCd);
			l192s02a.setLoanCurr(currCd);
			l192s02a.setEstAmt(appAmt);
			l192s02a.setLoanAmt(loanAmt);
			l192s02a.setMainId(mainId);
			l192s02a.setSetDate(setDate);
			l192s02a.setSetDateFrom(setDateFrom);
			l192s02a.setSetDateEnd(setDateEnd);
			l192s02a.setSetPosition(setOdr == null ? null : setOdr.intValue());
			l192s02a.setSetCurr(setCurr);
			l192s02a.setSetAmt(setAmt);

			l192s02as.add(l192s02a);
		}
		return l192s02as;
	}

	// private String getCmsName(String typCd, String brId, String custId,
	// String dupNo, String collNo) {
	//
	// String aa = collNo.substring(0, 2);
	// String bb = collNo.substring(3, 5);
	//
	// String cmsName = "";
	//
	// if ("01".equals(aa)) {
	// cmsName = "不動產(土地/建物/地上權)";
	//
	// if ("01".equals(bb) || "04".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0102(
	// typCd, brId, custId, dupNo, collNo);
	//
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// String site1 = CapString.trimNull((String) data
	// .get("SITE1"));
	// String site2 = CapString.trimNull((String) data
	// .get("SITE2"));
	// String site3 = CapString.trimNull((String) data
	// .get("SITE3"));
	// String site4 = CapString.trimNull((String) data
	// .get("SITE4"));
	// String site5 = CapString.trimNull((String) data
	// .get("SITE5"));
	// String site6 = CapString.trimNull((String) data
	// .get("SITE6"));
	// String site7 = CapString.trimNull((String) data
	// .get("SITE7"));
	// String site8 = CapString.trimNull((String) data
	// .get("SITE8"));
	// String site9 = CapString.trimNull((String) data
	// .get("SITE9"));
	// String site10 = CapString.trimNull((String) data
	// .get("SITE10"));
	// String site11 = CapString.trimNull((String) data
	// .get("SITE11"));
	// String site12 = CapString.trimNull((String) data
	// .get("SITE12"));
	//
	// cmsName = cmsName + site1 + site2 + site3 + site4 + site5
	// + site6 + site7 + site8 + site9 + site10 + site11
	// + site12;
	// }
	//
	// } else if ("02".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0101(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String site1 = CapString.trimNull((String) data
	// .get("SITE1"));
	// String site2 = CapString.trimNull((String) data
	// .get("SITE2"));
	// String site3 = CapString.trimNull((String) data
	// .get("SITE3"));
	// String site4 = CapString.trimNull((String) data
	// .get("SITE4"));
	// String immo = CapString
	// .trimNull((String) data.get("IMMNO"));
	//
	// cmsName = cmsName + site1 + site2 + site3 + "段" + site4
	// + "小段" + immo + "地號";
	//
	// }
	//
	// } else if ("03".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0102(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String immo = CapString
	// .trimNull((String) data.get("IMMNO"));
	// cmsName = cmsName + "建號：" + immo;
	//
	// }
	//
	// }
	// } else if ("02".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0102(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// if ("01".equals(bb)) {
	// cmsName = "動產(機器設備)：";
	// } else if ("02".equals(bb)) {
	// cmsName = "動產(工具)：";
	// } else if ("03".equals(bb)) {
	// cmsName = "動產(原料)：";
	// } else if ("04".equals(bb)) {
	// cmsName = "動產(半製品)：";
	// } else if ("05".equals(bb)) {
	// cmsName = "動產(成品)：";
	// } else if ("06".equals(bb)) {
	// cmsName = "動產(車輛)：";
	// } else if ("07".equals(bb)) {
	// cmsName = "動產(船舶)：";
	// } else if ("08".equals(bb)) {
	// cmsName = "動產(航空器)：";
	// } else if ("09".equals(bb)) {
	// cmsName = "動產(其他)：";
	// }
	// String collnm = CapString.trimNull((String) data.get("COLLNM"));
	// cmsName = cmsName + collnm;
	// }
	// } else if ("03".equals(aa)) {
	// if ("01".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0301(
	// typCd, brId, custId, dupNo, collNo);
	// String flg = "";
	// StringBuffer depNo = new StringBuffer();
	// for (Map<String, Object> data : cmsData) {
	// depNo.append(CapString.trimNull((String) data.get("DEPNO"))
	// + " ");
	// flg = CapString.trimNull((String) data.get("FLG"));
	// }
	//
	// if ("1".equals(flg)) {
	// cmsName = cmsName + "定存單，存單號碼" + depNo.toString();
	// } else if ("2".equals(flg)) {
	// cmsName = cmsName + "定儲存單，存單號碼" + depNo.toString();
	// } else if ("3".equals(flg)) {
	// cmsName = cmsName + "可轉讓定存，存單號碼" + depNo.toString();
	// } else if ("4".equals(flg)) {
	// cmsName = cmsName + "他行定存單，存單號碼" + depNo.toString();
	// }
	// } else if ("02".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0302(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String kind = CapString.trimNull((String) data.get("KIND"));
	// if ("1".equals(kind)) {
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "甲種國庫券(數量：" + qnty + ")";
	// } else if ("2".equals(kind)) {
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "乙種國庫券(數量：" + qnty + ")";
	// }
	// }
	// } else if ("03".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0303(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String bondNm = CapString.trimNull((String) data
	// .get("BONDNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "公債：" + bondNm + "(數量：" + qnty + ")";
	//
	// }
	// } else if ("04".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0304(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String bbondNm = CapString.trimNull((String) data
	// .get("BBONDNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	//
	// cmsName = cmsName + "金融債券：" + bbondNm + "(數量：" + qnty + ")";
	// }
	// } else if ("05".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0305(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// BigDecimal parval = (BigDecimal) data.get("PARVAL");
	// cmsName = cmsName + "央行儲蓄券：面額" + parval;
	// }
	// } else if ("06".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0306(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String cbNm = CapString.trimNull((String) data.get("CBNM"));
	//
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "公司債：" + cbNm + "(數量：" + qnty + ")";
	//
	// }
	// } else if ("07".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0307(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String stkNm = CapString.trimNull((String) data
	// .get("STKNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "股票：" + stkNm + "(鑑價數量：" + qnty + ")";
	// }
	// } else if ("08".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0308(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// String fundNm = CapString.trimNull((String) data
	// .get("FUNDNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	//
	// cmsName = cmsName + "開放行基金：：" + fundNm + "(數量：" + qnty
	// + ")";
	// }
	// }
	// } else if ("04".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0401(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String trstNm = CapString.trimNull((String) data.get("TRSTNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// cmsName = cmsName + "倉單：" + trstNm + "(數量：" + qnty + ")";
	//
	// }
	// } else if ("05".equals(aa)) {
	// if ("01".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0501(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String grtBkNm = CapString.trimNull((String) data
	// .get("GRTBKNM"));
	// String grtKind = CapString.trimNull((String) data
	// .get("GRTKIND"));
	// String curr = CapString.trimNull((String) data.get("CURR"));
	// BigDecimal ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	// cmsName = cmsName + "銀行保證：" + grtBkNm;
	// if ("1".equals(grtKind)) {
	// cmsName = cmsName + "(種類：借款保證　" + curr + ogrtAmt + ")";
	// } else if ("2".equals(grtKind)) {
	// cmsName = cmsName + "(種類：Counter Guarantee　" + curr
	// + ogrtAmt + ")";
	// } else if ("3".equals(grtKind)) {
	// cmsName = cmsName + "(種類：其他　" + curr + ogrtAmt + ")";
	// }
	// }
	// } else if ("02".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0502(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String grtDept = CapString.trimNull((String) data
	// .get("GRTDEPT"));
	// String grtKind = CapString.trimNull((String) data
	// .get("GRTKIND"));
	// String curr = CapString.trimNull((String) data.get("CURR"));
	// BigDecimal ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	// cmsName = cmsName + "政府公庫主管機關保證：" + grtDept + "，(種類："
	// + grtKind + " " + curr + ogrtAmt + ")";
	//
	// }
	// } else if ("03".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0503(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String grtNm = CapString.trimNull((String) data
	// .get("GRTNM"));
	// String grtItem = CapString.trimNull((String) data
	// .get("GRTITEM"));
	//
	// String curr = CapString.trimNull((String) data.get("CURR"));
	// BigDecimal ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	//
	// cmsName = cmsName + "信用保證機構保證：" + grtNm + "，(項目：" + grtItem
	// + " " + curr + ogrtAmt + ")";
	// }
	// } else if ("04".equals(bb)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0504(
	// typCd, brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	//
	// String grtNm = CapString.trimNull((String) data
	// .get("GRTNM"));
	// String grtItem = CapString.trimNull((String) data
	// .get("GRTITEM"));
	//
	// String curr = CapString.trimNull((String) data.get("CURR"));
	// BigDecimal ogrtAmt = (BigDecimal) data.get("OGRTAMT");
	//
	// cmsName = cmsName + "母公司保證：" + grtNm + "，(項目：" + grtItem
	// + " " + curr + ogrtAmt + ")";
	// }
	// }
	// } else if ("06".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0601(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// String kind = CapString.trimNull((String) data.get("KIND"));
	// if ("1".equals(kind)) {
	// cmsName = cmsName + "額度本票：";
	// } else if ("2".equals(kind)) {
	// cmsName = cmsName + "備償票據：";
	// }
	//
	// String nttype = CapString.trimNull((String) data.get("NTTYPE"));
	// String ntno = CapString.trimNull((String) data.get("NTNO"));
	// if ("1".equals(nttype)) {
	// cmsName = cmsName + "本票(票據號碼：" + ntno + ")";
	// } else if ("2".equals(nttype)) {
	// cmsName = cmsName + "匯票(票據號碼：" + ntno + ")";
	// } else if ("3".equals(nttype)) {
	// cmsName = cmsName + "支票(票據號碼：" + ntno + ")";
	// }
	//
	// }
	// } else if ("07".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0701(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// cmsName = cmsName + "貼現票據：";
	// String nttype = CapString.trimNull((String) data.get("NTTYPE"));
	// String isuNm = CapString.trimNull((String) data.get("ISUNM"));
	// String ntno = CapString.trimNull((String) data.get("NTNO"));
	// if ("1".equals(nttype)) {
	// cmsName = cmsName + "本票(發票人名稱：" + isuNm + "　票據號碼：" + ntno
	// + ")";
	// } else if ("2".equals(nttype)) {
	// cmsName = cmsName + "匯票(發票人名稱：" + isuNm + "　票據號碼：" + ntno
	// + ")";
	// }
	//
	// }
	// } else if ("08".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl08(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// if ("01".equals(bb)) {
	// cmsName = cmsName + "信託占有(機器設備)：";
	// } else if ("02".equals(bb)) {
	// cmsName = cmsName + "信託占有(工具)：";
	// } else if ("03".equals(bb)) {
	// cmsName = cmsName + "信託占有(原料)：";
	// } else if ("04".equals(bb)) {
	// cmsName = cmsName + "信託占有(半製品)：";
	// } else if ("05".equals(bb)) {
	// cmsName = cmsName + "信託占有(成品)：";
	// } else if ("06".equals(bb)) {
	// cmsName = cmsName + "信託占有(車輛)：";
	// } else if ("07".equals(bb)) {
	// cmsName = cmsName + "信託占有(船舶)：";
	// } else if ("08".equals(bb)) {
	// cmsName = cmsName + "信託占有(航空器)：";
	// } else if ("09".equals(bb)) {
	// cmsName = cmsName + "信託占有(其他)：";
	// }
	// String collNm = CapString.trimNull((String) data.get("COLLNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// String unit = CapString.trimNull((String) data.get("UNIT"));
	// cmsName = cmsName + collNm + " " + qnty + unit;
	// }
	// } else if ("09".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl0901(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// String collNm = CapString.trimNull((String) data.get("COLLNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// String unit = CapString.trimNull((String) data.get("UNIT"));
	// cmsName = cmsName + "參貸他行：" + collNm + " " + qnty + unit;
	// }
	// } else if ("10".equals(aa)) {
	// List<Map<String, Object>> cmsData = misCollXXXX.getColl1001(typCd,
	// brId, custId, dupNo, collNo);
	// if (cmsData != null && !cmsData.isEmpty()) {
	// Map<String, Object> data = cmsData.get(0);
	// String collNm = CapString.trimNull((String) data.get("COLLNM"));
	// BigDecimal qnty = (BigDecimal) data.get("QNTY");
	// String unit = CapString.trimNull((String) data.get("UNIT"));
	// cmsName = cmsName + "其他擔保品：" + collNm + " " + qnty + unit;
	// }
	// }
	//
	// return cmsName;
	// }
	//
	// private String getOwnerName(String typCd, String brId, String custId,
	// String dupNo, String collNo, String collSn) {
	// List<Map<String, Object>> ownerData = misELF369Service.getELF369Data(
	// typCd, brId, custId, dupNo, collNo, collSn);
	// int size = 0;
	// StringBuffer ownerName = new StringBuffer();
	// if (ownerData != null && !ownerData.isEmpty()) {
	// size = ownerData.size();
	// for (int i = 0; i < ownerData.size(); i++) {
	// Map<String, Object> data = ownerData.get(i);
	// String name = CapString.trimNull((String) data.get("OWNERNM"));
	// ownerName.append(name);
	// if (i == 4) {
	// break;
	// }
	// if (size > 1) {
	// ownerName.append("、");
	// }
	// }
	//
	// if (size > 4) {
	// ownerName.append("...等" + size + "人 ");
	// }
	// }
	// return ownerName.toString();
	// }
	//
	// private String getCollInsData(String custId, String duNo, String collNo)
	// {
	// IBranch branch = branchService.getBranch(MegaSSOSecurityContext
	// .getUnitNo());
	//
	// String useSWFT = branch.getUseSWFT();
	//
	// Map<String, String> insuranceKind = new HashMap<String, String>();
	// insuranceKind.put("01", "火險");
	// insuranceKind.put("02", "地震險");
	// insuranceKind.put("03", "颱風險");
	// insuranceKind.put("04", "財產綜合損失險");
	// insuranceKind.put("05", "營建工程綜合損失險");
	// insuranceKind.put("12", "其他保險");
	// Map<String, BigDecimal> insuranceAmt = new HashMap<String, BigDecimal>();
	//
	// List<Map<String, Object>> collInsData = misELF347Service.getELF347Data(
	// custId, duNo, collNo);
	//
	// for (Map<String, Object> data : collInsData) {
	// String inskind1 = CapString.trimNull((String) data.get("INSKND1"));
	// String inskind2 = CapString.trimNull((String) data.get("INSKND2"));
	// String inskind3 = CapString.trimNull((String) data.get("INSKND3"));
	// String insNm = CapString.trimNull((String) data.get("INSNM"));
	// String insbDt = CapString.trimNull((String) data.get("INSBDT"));
	// String inseDt = CapString.trimNull((String) data.get("INSEDT"));
	//
	// if (!"".equals(inskind1) && "".equals(inskind2)
	// && "".equals(inskind3) && "".equals(insNm)) {
	// if (insuranceKind.containsKey(inskind1)) {
	//
	// }
	// } else if (!"".equals(inskind2) || !"".equals(inskind3)
	// || !"".equals(insNm)) {
	//
	// }
	//
	// }
	// return null;
	// }

}
