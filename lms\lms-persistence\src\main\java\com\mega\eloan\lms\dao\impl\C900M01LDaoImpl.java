/* 
 * C900M01LDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.C900M01LDao;
import com.mega.eloan.lms.model.C900M01L;

/** 外匯業務授權額度檔 **/
@Repository
public class C900M01LDaoImpl extends LMSJpaDao<C900M01L, String>
	implements C900M01LDao {

	@Override
	public C900M01L findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01L> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01L> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<C900M01L> findByIndex01(String caseLvl){
		ISearch search = createSearchTemplete();
		List<C900M01L> list = null;
		if (caseLvl != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseLvl", caseLvl);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01L> findByIndex02(String brNo){
		ISearch search = createSearchTemplete();
		List<C900M01L> list = null;
		if (brNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01L> findByIndex03(String brClass){
		ISearch search = createSearchTemplete();
		List<C900M01L> list = null;
		if (brClass != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "brClass", brClass);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public C900M01L findByBrNoAndType(String brNo, String type) {
		ISearch search = createSearchTemplete();
		if (brNo != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "brNo", brNo);
		}
		if (type != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public C900M01L findByBrClass(String brClass) {
		ISearch search = createSearchTemplete();
		if (brClass != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "brClass", brClass);
		}
		return findUniqueOrNone(search);
	}
}