package com.mega.eloan.lms.dc.util;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.lang.CharUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * DXLUtil
 * </pre>
 * 
 * @since 2013/2/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/2/4,Bang,new
 *          </ul>
 */
public class DXLUtil {
	private static Logger logger = LoggerFactory.getLogger(DXLUtil.class);
	// 案件審核層級
	private static Map<String, String> caseLvl_Map = new HashMap<String, String>();
	// 區部別
	private static Map<String, String> typCd_Map = new HashMap<String, String>();
	// 連保人/保證人
	private static Map<String, String> guarantorType_Map = new HashMap<String, String>();
	// 合計金額調整註記
	private static Map<String, String> valueTune_Map = new HashMap<String, String>();
	// 現行額度-是否循環使用
	private static Map<String, String> reUse_Map = new HashMap<String, String>();
	// 有無xxxxx與本行有利害關係人(者)
	private static Map<String, String> yn3_Map = new HashMap<String, String>();
	// 負責人欄類型
	private static Map<String, String> posType_Map = new HashMap<String, String>();
	// 目前文件狀態
	private static Map<String, String> docStatus_Map = new HashMap<String, String>();
	// 類別
	private static Map<String, String> rateType_Map = new HashMap<String, String>();
	// 利率基礎-加減年利率選項
	private static Map<String, String> disYearOp_Map = new HashMap<String, String>();
	// 登記(實收)資本額-（單位）
	private static Map<String, String> unit_Map = new HashMap<String, String>();
	// 按期計收,按X月預收
	private static Map<String, String> numC2E_Map = new HashMap<String, String>();
	// 評等表類型
	private static Map<String, String> crdType_Map = new HashMap<String, String>();
	// 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
	private static Map<String, String> yn5_Map = new HashMap<String, String>();

	public static void init() {
		StringBuffer sbCode = new StringBuffer();
		StringBuffer sbMap = new StringBuffer();
		// 案件審核層級
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("常董會權限;常董會權限簽奉總經理核批;常董會權限簽准由副總經理核批;利費率變更案件由總處經理核定;屬常董會授權總經理逕核案件;總經理權限內;副總經理權限;處長權限;其他(經理);董事會權限;區域營運中心營運長/副營運長權限;利費率變更案件由董事長核定;個金處經理權限");
		sbMap.append("1;2;3;4;5;6;7;8;9;A;B;C;D");
		caseLvl_Map = initMap(sbCode.toString(), sbMap.toString());
		// 區部別
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("DBU;OBU;海外;海外同業");
		sbMap.append("1;4;4;4");
		typCd_Map = initMap(sbCode.toString(), sbMap.toString());
		// 連保人/保證人
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("G;I");// 01-18先將傳入值trim(),無論空白或空白字串均轉為1
		sbMap.append("1;2");
		guarantorType_Map = initMap(sbCode.toString(), sbMap.toString());
		// 合計金額調整註記
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("＊;");// 01-18先將傳入值trim(),無論空白或空白字串均轉為N
		sbMap.append("Y;");
		valueTune_Map = initMap(sbCode.toString(), sbMap.toString());
		// 現行額度-是否循環使用
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("N;Y");
		sbMap.append("1;2");
		reUse_Map = initMap(sbCode.toString(), sbMap.toString());
		// 有無xxxxx與本行有利害關係人(者)
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("有;無;不適用");
		sbMap.append("1;2;3");
		yn3_Map = initMap(sbCode.toString(), sbMap.toString());
		// 負責人欄類型
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("董事長;董事;負責人");
		sbMap.append("1;2;3");
		posType_Map = initMap(sbCode.toString(), sbMap.toString());
		// 目前文件狀態
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("1;2;3;4;9");
		sbMap.append("010;020;030;040;060");
		docStatus_Map = initMap(sbCode.toString(), sbMap.toString());
		// 類別
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("NT;USD;JPY;EUR;OTH;CNY;AUD;HKD");
		sbMap.append("1;2;3;4;Z;5;6;7");
		rateType_Map = initMap(sbCode.toString(), sbMap.toString());
		// 利率基礎-加減年利率選項
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("加;減");
		sbMap.append("1;2");
		disYearOp_Map = initMap(sbCode.toString(), sbMap.toString());
		// 登記(實收)資本額-（單位）
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("元;佰元;千元;仟元;仟圓;萬元;百萬元;佰萬元;仟萬元");
		sbMap.append("1;100;1000;1000;1000;10000;1000000;1000000;10000000");
		unit_Map = initMap(sbCode.toString(), sbMap.toString());
		// 按期計收,按X月預收
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("一;二;三;四;五;六;七;八;九");
		sbMap.append("1;2;3;4;5;6;7;8;9");
		numC2E_Map = initMap(sbCode.toString(), sbMap.toString());
		// 評等表類型
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("1;2;3;4;5;6;7;8;9;A;B;C;D;E;F;G;FMOW100M01;FMOW200M01;FMOW300M01;FMOW400M01;FMOW500M01");
		sbMap.append("M1;M2;M3;M4;M5;M6;M7;M8;M9;MA;MB;MC;MD;ME;MF;MG;M1;M2;M3;M4;M5");
		crdType_Map = initMap(sbCode.toString(), sbMap.toString());
		// 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
		sbCode.setLength(0);
		sbMap.setLength(0);
		sbCode.append("有;無;免查;不適用");// 無論空白或空白字串均轉為2
		sbMap.append("1;2;2;3");
		yn5_Map = initMap(sbCode.toString(), sbMap.toString());
	}

	/**
	 * 轉換字串為Map
	 * 
	 * @param s1
	 *            String: 預計的key值
	 * @param s2
	 *            String: 預計的value值
	 * @return tmpMap HashMap
	 */
	private static Map<String, String> initMap(String s1, String s2) {
		Map<String, String> tmpMap = new HashMap<String, String>();
		String[] map1 = s1.split(";"); // Key
		String[] map2 = s2.split(";"); // value
		for (int i = 0; i < map1.length; i++) {
			tmpMap.put(map1[i], map2[i]);
		}
		return tmpMap;
	}

	/**
	 * 依<itemType>之<itemValue>值取得Map對應的轉換值
	 * 
	 * @param itemType
	 *            String: db2Xml中的type屬性欄位
	 * @param itemValue
	 *            String: 從.dxl中對應到db2XML後取得的值
	 * @return str String :相對應的值
	 */
	public static String codeConvert(String itemType, String itemValue)
			throws Exception {
		if ("caseLvl".equalsIgnoreCase(itemType)) { // 案件審核層級
			return getCodeMap(caseLvl_Map, itemValue, TextDefine.EMPTY_STRING);
		}
		// 若是DBU，則回傳1，其他全數回傳4
		else if ("typCd".equalsIgnoreCase(itemType)) { // 區部別
			return getCodeMapForSpace(typCd_Map, itemValue, "4");
		}
		// 01-18請先將傳入值trim()再轉換,無論空白或空白字串均轉為1
		else if ("guarantorType".equalsIgnoreCase(itemType.trim())) { // 連保人/保證人
			return getCodeMapForSpace(guarantorType_Map, itemValue, "1");
		}
		// 01-18請先將傳入值trim()再轉換,無論空白或空白字串均轉為N
		else if ("valueTune".equalsIgnoreCase(itemType.trim())) { // 合計金額調整註記
			return getCodeMapForSpace(valueTune_Map, itemValue, "N");
		}
		// 01-18 若以上皆非，請回傳原傳入值
		else if ("reUse".equalsIgnoreCase(itemType)) { // 現行額度-是否循環使用
			return getCodeMapForDefault(reUse_Map, itemValue, itemValue);
		}
		// 01-18 若以上皆非，請回傳原傳入值
		else if ("Yn3".equalsIgnoreCase(itemType)) { // 有無xxxxx與本行(金控)有利害關係人(者)
			return getCodeMapForDefault(yn3_Map, itemValue, itemValue);
		}
		// 暫時用..等getCodeMap的"ERR_"拿掉後要挪回getCodeMap
		else if ("posType".equalsIgnoreCase(itemType)) { // 負責人欄類型
			return getCodeMapForDefault(posType_Map, itemValue, "9"); // 不在此範圍內者為9
		} else if ("docStatus".equalsIgnoreCase(itemType)) { // 目前文件狀態
			return getCodeMap(docStatus_Map, itemValue, TextDefine.EMPTY_STRING);
		}
		// 01-18 若以上皆非，請回傳原傳入值
		else if ("rateType".equalsIgnoreCase(itemType)) { // 類別
			return getCodeMapForDefault(rateType_Map, itemValue, itemValue);
		}
		// 01-18 其他資料請回傳原傳入值
		else if ("disYearOp".equalsIgnoreCase(itemType)) { // 利率基礎-加減年利率選項
			return getCodeMapForDefault(disYearOp_Map, itemValue, itemValue);
		}
		// 01-18 若以上皆非，請回傳原傳入值
		else if ("unit".equalsIgnoreCase(itemType)) { // 登記(實收)資本額-（單位）
			return getCodeMapForDefault(unit_Map, itemValue, itemValue);
		}
		// 01-18 不符合者請回傳原值
		else if ("numC2E".equalsIgnoreCase(itemType)) { // 按期計收,按X月預收
			return getCodeMapForDefault(numC2E_Map, itemValue, itemValue);
		}
		// 01-24 其他資料轉為空白
		else if ("crdType".equalsIgnoreCase(itemType)) { // 評等表類型
			return getCodeMapForDefault(crdType_Map, itemValue,
					TextDefine.EMPTY_STRING);
		}
		// 01-22 無論空白或空白字串均轉為2
		else if ("Yn5".equalsIgnoreCase(itemType)) { // 應收帳款承購無追索權－買方，有無銀行法所稱與本行有利害關係人
			return getCodeMapForSpace(yn5_Map, itemValue, "2");
		}

		return itemValue;
	}

	/**
	 * 依Map及key值(itemValue無空白key)取得對應之value值 若值為其他資料回傳"ERR_"+原傳入值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值
	 */
	private static String getCodeMap(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {
			// return defValue;
			// 01-18其他資料請回傳"ERR_"+原傳入值,bang:"ERR_"之後要拿掉 ,否則若無值colChk時會出現長度錯誤
			return "ERR_" + itemValue;
		}
		return str;
	}

	/**
	 * 依Map及key值(itemValue有空白key)取得對應之value值,若值為空白者回傳default值 無對應值則回傳原值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值
	 */
	private static String getCodeMapForSpace(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		if (null != itemValue && itemValue.trim().length() == 0) {// 值為空白者
			return defValue;
		}
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {// 無對應值
			return itemValue;
		}
		return str;
	}

	/**
	 * 依Map及key值(itemValue無空白key)取得對應之value值,若無對應值則回傳default值
	 * 
	 * @param codeMap
	 *            HashMap<String, String>要取值的Map
	 * @param itemValue
	 *            String : key值
	 * @param defValue
	 *            String : 預設值
	 * @return str String :相對應的值 //暫時用..等拿掉ERR_字樣之後所有呼叫此 Method的都要挪回getCodeMap
	 */
	private static String getCodeMapForDefault(Map<String, String> codeMap,
			String itemValue, String defValue) throws Exception {
		String str = codeMap.get(itemValue);
		if (StringUtils.isBlank(str)) {
			return defValue;
		}
		return str;
	}

	/**
	 * 以固定格式(YYY-MM-DD)或DB(YYY/MM/DD)或DB(YYYMMDD)將民國年月日轉為西元年月日
	 * 
	 * @param rocDate
	 *            String :即將被轉換日期
	 * @return result String :轉換過後的日期
	 * @Example 1.097/02/29<br>
	 *          2.87/5/18<br>
	 *          3.100/11/22 17:40:22 2013-01-17
	 *          決議若有錯先丟回原值,至columnTruncate時若檢核有錯則寫出另一份檔案
	 */
	public static final String parseRocDate(String rocDate) {
		try {
			String tmpRocDate = rocDate;
			boolean regFlag = true;// 判斷日期是否含有"/"或"-"
			boolean timeFlag = true;// 判斷日期是否含有":"
			boolean errFlag = false;// 判斷錯誤的日期格式
			if (StringUtils.isEmpty(rocDate)) {
				return "";
			}
			// 目前已知格式有"."-->1997.1.2,"()"-->059/08/20(067/07)
			errFlag = tmpRocDate.indexOf(".") > -1
					|| tmpRocDate.indexOf("(") > -1;
			if (errFlag) {
				return "ERR_" + rocDate;
			}
			regFlag = tmpRocDate.indexOf("/") > -1
					|| tmpRocDate.indexOf("-") > -1;
			timeFlag = tmpRocDate.indexOf(":") > -1;
			if (!regFlag && timeFlag) {
				int spaceD = tmpRocDate.indexOf(TextDefine.SPACE);
				int year = Integer
						.parseInt(tmpRocDate.substring(0, spaceD - 4)) + 1911;
				int month = Integer.parseInt(tmpRocDate.substring((spaceD - 4),
						spaceD - 2));
				int day = Integer.parseInt(tmpRocDate.substring((spaceD - 2),
						spaceD));

				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				} else {
					return String.valueOf(year) + "-"
							+ String.format("%02d", month) + "-"
							+ String.format("%02d", day)
							+ tmpRocDate.substring(spaceD);
				}
			}
			// YYYMMDD,YYYYMMDD
			else if (tmpRocDate.length() <= 8 && !regFlag) {
				int year = 0;
				if (tmpRocDate.length() > 4 && tmpRocDate.length() <= 7) {
					year = Integer.parseInt(tmpRocDate.substring(0,
							tmpRocDate.length() - 4)) + 1911;
				} else if (tmpRocDate.length() == 8) {
					year = Integer.parseInt(tmpRocDate.substring(0,
							tmpRocDate.length() - 4));
				} else {// 不合法的日期
						// return "";
					return "ERR_" + rocDate;
				}
				int month = Integer.parseInt(tmpRocDate.substring(
						tmpRocDate.length() - 4, tmpRocDate.length() - 2));
				int day = Integer.parseInt(tmpRocDate.substring(tmpRocDate
						.length() - 2));

				// 確認日期正確性
				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				} else {
					return String.valueOf(year) + "-"
							+ String.format("%02d", month) + "-"
							+ String.format("%02d", day);
				}
			} else {
				tmpRocDate = tmpRocDate.replaceAll("/", "-");
				String[] strCDate = tmpRocDate.split("-");
				// 確保為年-月-日 型態
				if (strCDate.length < 3) {
					return "ERR_" + rocDate;
				}
				int year = 0;
				if (strCDate[0].length() == 4) { // 西元年
					year = Integer.parseInt(strCDate[0]);
				} else if (strCDate[0].length() < 4) { // 民國年
					year = Integer.parseInt(strCDate[0]) + 1911;
				} else { // 不正確的日期
							// return "";
					return "ERR_" + rocDate;
				}
				int month = Integer.parseInt(strCDate[1]);
				int day = 0;
				// 日期會有兩種格式1.年/月/日 2:年/月/日(空一格)時:分:秒 要各別處理
				int time = strCDate[2].indexOf(":");
				int space = strCDate[2].indexOf(TextDefine.SPACE);
				if (time > 0) {
					// 取strCDate[2] 到" "之間的值,否則萬一day不足2位數會出錯
					day = Integer.parseInt(strCDate[2].substring(0, space));
				} else {
					day = Integer.parseInt(strCDate[2]);
				}
				// 確認日期正確性
				if (!isValidDate(year, month, day)) {
					// return "";
					return "ERR_" + rocDate;
				}
				// 把不足的位數補齊,否則matcher時會比對不到
				String tmpYear = String.format("%04d", year);
				String tmpMonth = String.format("%02d",
						Integer.parseInt(strCDate[1]));
				String tmpday = String.format("%02d", day);
				// 重新再組一次
				tmpRocDate = tmpYear + "-" + tmpMonth + "-" + tmpday;

				// 判斷是否為西元年
				if (strCDate[0].length() == 4) {
					if (time > 0) {
						return tmpRocDate + strCDate[2].substring(space);
					} else {
						return tmpRocDate;
					}
				}

				final Pattern ROC_DATE_PATTERN = Pattern
						.compile("^(\\d{1,5})\\D(\\d{2})\\D(\\d{2})$");
				Matcher matcher = null;
				if (time > 0) {
					matcher = ROC_DATE_PATTERN.matcher(tmpRocDate
							.split(TextDefine.SPACE)[0]);
				} else {
					matcher = ROC_DATE_PATTERN.matcher(tmpRocDate);
				}

				if (matcher.matches()) {
					String rocYear = matcher.group(1);
					String rocMonth = matcher.group(2);
					String rocDay = matcher.group(3);
					if (time > 0) {
						return rocYear + "-" + rocMonth + "-" + rocDay
								+ strCDate[2].substring(space);
					} else {
						return rocYear + "-" + rocMonth + "-" + rocDay;
					}
				}
			}
			// 01-18格式有誤無法正常轉換者，請回傳"ERR_"+原傳入值
			return "ERR_" + rocDate;
		} catch (Exception e) {
			logger.error("DXLUtil.java:parseRocDate【"+rocDate+"】 將民國年轉為合法的日期字串時出現錯誤:"
					+ e);
			return "ERR_" + rocDate;
		}
	}

	/**
	 * 檢核 日期數值正確性
	 * 
	 * @param year
	 *            int:西元年
	 * @param month
	 *            int: 月
	 * @param day
	 *            int: 日
	 * @return boolean
	 */
	private static final boolean isValidDate(int year, int month, int day)
			throws Exception {
		if (month < 1 || month > 12) {
			return false;
		}
		if (day < 1 || day > 31) {
			return false;
		}
		if ((month == 4 || month == 6 || month == 9 || month == 11)
				&& (day == 31)) {
			return false;
		}
		if (month == 2) {
			boolean leap = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0));
			if (day > 29 || (day == 29 && !leap)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 字串代碼是.否.有.無.0.1轉成Y,N
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getYesNo(String itemValue) {
		if ("是".equals(itemValue) || "有".equals(itemValue)
				|| "0".equals(itemValue)) {
			return "Y";
		}
		if ("否".equals(itemValue) || "無".equals(itemValue)
				|| "1".equals(itemValue)) {
			return "N";
		}
		return itemValue;
	}
	/**
	 * 字串代碼是.Y轉成Y,其他轉N
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return 轉換結果
	 */
	public static final String getYesNo1(String itemValue) {
		if ( "Y".equals(itemValue)) {
			return "Y";
		}else {
			return "N";
		}
	}

	/**
	 * 轉換授權
	 * 
	 * @param itemValue
	 *            :被轉換的字串
	 * @return String 轉換結果
	 * @TODO if(code==2) return 3 else if (code==3)return 5 else if
	 *       (code==4)return 6 else return code
	 */
	public static final String getAuthLvl(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			if (Util.isNumeric(itemValue)) {
				switch (Integer.valueOf(itemValue.trim())) {
				case 1:
					return "1";
				case 2:
					return "3";
				case 3:
					return "5";
				case 4:
					return "6";
				case 5:
					return "5";
				case 6:
					return "6";
				}
			}
		}
		return itemValue;
	}

	/*
	 * 095營業部(兆)授字第183號 [0]年度：取前三碼轉為西元年
	 * [1]分行別：去掉前三碼年度，取(兆)前段字串，如例：營業部,無符合的分行代碼，則回傳"ERROR"+字串
	 * [2]流水號：取"授字第"至"號"之前數值:183 依傳入參數num決定回傳第幾個值 96年以前，年度+分行+"授字第"+流水號+"號"
	 * 96年以後，年度+分行+"(兆)授字第"+流水號+"號"
	 */
	/**
	 * 取得案件號碼相關資料:年度 or 分行 or 流水號
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            int:itemNum
	 * @return
	 * @Example 095營業部(兆)授字第183號 or 098營業部授字第203號
	 */
	public static String getProjectNo(String itemValue, int num)
			throws Exception {
		if (StringUtils.isBlank(itemValue)) {
			return itemValue;
		}
		// 年度
		if (num == 0) {
			String tmpValue = itemValue.substring(0, 3);
			if (Util.isNumeric(tmpValue)) {
				return Integer
						.toString((Integer.valueOf(tmpValue.trim()) + 1911));
			} else {
				return itemValue;
			}
		} else if (num == 1) { // 分行
			// 以"("是否存在來判別,年度有可能會不準
			int idx = itemValue.indexOf("(");
			if (idx >= 0) {
				return itemValue.substring(3, idx);
			} else {
				int idx1 = itemValue.indexOf("授");
				return itemValue.substring(3, idx1);
			}
		} else if (num == 2) { // 流水號
			int idx = itemValue.indexOf("第");
			if (idx >= 0) {
				return itemValue.substring(idx + 1, itemValue.length() - 1);
			}
		}
		return itemValue;
	}

	/**
	 * 從dxl中找尋姓名與ID的對照資料
	 * 
	 * @param dxlSB
	 *            String: 已轉成字串的dxl檔
	 * @param strName
	 *            員工姓名
	 * @return rtstr 員工編號 :回查DXL取回員工編號，並於左邊補零至6碼
	 */
	public static String searchId(String dxlSB, String empName) {
		try {
			String rtstr = TextDefine.EMPTY_STRING;
			if (empName == null || empName.length() == 0) {
				return rtstr;
			}
			String tag = "CN=" + empName;
			int idx1 = dxlSB.indexOf(tag);
			if (idx1 > 0) {
				String endTag = "/OU=";
				int idx2 = dxlSB.indexOf(endTag, idx1);
				rtstr = dxlSB.substring(idx1 + tag.length(), idx2).trim();
				rtstr = String.format("%06d", Integer.valueOf(rtstr));
			} else {
				return "ERR_" + empName;
			}
			return rtstr;
		} catch (Exception e) {
			logger.error("DXLUtil.java:searchId 取回員工編號【"+empName+"】時出現錯誤:" + e);
			return "ERR_" + empName;
		}
	}

	/**
	 * 截取員工編號
	 * 
	 * @param itemValue
	 *            String
	 * @return
	 */
	public static String getEmpID(String itemValue) {
		try {
			if (itemValue == null || itemValue.length() == 0) {
				return "";
			}
			String str = itemValue.trim();
			String[] s = str.split(TextDefine.SPACE);
			if (s.length >= 2) {
				String sId = s[s.length - 1];
				if (sId.length() == 5) {
					return sId;
				}
			}
			return "";
		} catch (Exception e) {
			logger.error("DXLUtil.java:getEmpID 截取員工編號【"+itemValue+"】時出現錯誤:" + e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 截取員工姓名
	 * 
	 * @param itemValue
	 *            String
	 * @return
	 */
	public static final String getEmpName(String itemValue) throws Exception {
		if (itemValue == null || itemValue.length() == 0) {
			return "";
		}
		String str = itemValue.trim();
		int idx = str.indexOf("(");
		if (idx >= 0) {
			return str.substring(0, idx).trim();
		}
		String[] s = str.split(" ");
		return s[0].trim();
	}

	/**
	 * 截取日期時間並將之轉換成西元年格式
	 * 
	 * @param input
	 * @return
	 */
	public static final String getDateTime(String input) throws Exception {
		if (input == null || input.length() == 0) {
			return "";
		}
		int idx1 = input.indexOf("(");
		int idx2 = input.indexOf(")");
		String s;
		if (idx1 > 0 && idx2 > 0 && idx2 > idx1) {
			s = input.substring(idx1 + 1, idx2);
		} else {
			return "";
		}
		String[] ss = s.split(TextDefine.SPACE);
		String strDate = parseRocDate(ss[0]);

		String strTime = ss[1].replaceAll(":", ".") + ".000000";
		return strDate + "-" + strTime; // timestamp
	} // end of getDateTime

	/**
	 * 依傳入參數num決定傳回的號碼
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            :號碼分類
	 * @return 若無值則回傳空字串
	 * @TODO 適用於身分證號、統一編號、護照號碼 [0]取第一碼到倒數第二碼 [1]取最後一碼
	 */
	public static String getIdn11(String itemValue, int num) {
		if (StringUtils.isNotBlank(itemValue)) {
			switch (num) {
			case 0:
				return itemValue.substring(0, itemValue.length() - 1);
			case 1:
				return itemValue.substring(itemValue.length() - 1);
			}
		}
		return "";
	}

	/**
	 * 依傳入參數num決定傳回字串的前幾碼
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            :需切割的字數
	 * @return 若無值則回傳itemValue
	 */
	public static String getSubstr(String itemValue, int num) {
		try {
			if (StringUtils.isNotBlank(itemValue)) {
				return itemValue.substring(0, num);
			} else {
				return itemValue;
			}
		} catch (Exception e) {
			logger.error("DXLUtil.java:searchId 【"+itemValue+"】傳入參數【"+num+"】num決定傳回字串的前幾碼時出現錯誤:"
					+ e);
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 將英數字轉成全形
	 * 
	 * @param itemValue
	 *            String
	 * @return 轉成全形後的結果
	 */
	public static String getFullChar(String itemValue) throws Exception {
		if (StringUtils.isNotBlank(itemValue)) {
			String outStr = "";
			char[] chars = itemValue.toCharArray();
			int tranTemp = 0;
			for (int i = 0; i < chars.length; i++) {
				// 中文不處理
				// 作法一 超過這個應該都是中文字了…
				// if((int)chars[i] < '\200'){
				// 作法二:isAsciiAlphanumeric判斷是否為英數字
				if (CharUtils.isAsciiAlphanumeric(chars[i])) {
					tranTemp = (int) chars[i];
					if (tranTemp != 45) { // ASCII碼:45 是減號 -
						tranTemp += 65248; // 此數字是 Unicode編碼轉為十進位 和 ASCII碼的 差
					}
					outStr += (char) tranTemp;
				} else {
					outStr += (char) chars[i];
				}
			}
			return outStr;
		} else {
			return itemValue;
		}
	}

	/**
	 * 將數字中的千分號去除
	 * 
	 * @param itemValue
	 *            String
	 * @return String 2013-01-09 新增去除"元"字
	 */
	public static String getMoney(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			int idx = itemValue.indexOf("元");
			if (idx > -1) {
				itemValue = itemValue.substring(0, idx);
			}
			return itemValue.trim().replace(",", TextDefine.EMPTY_STRING);
		} else {
			return itemValue;
		}
	}

	/**
	 * 將年月轉為年月日
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 * @TODO 統一為月初1日
	 * @Example 資料年月: 2011-10(中間有空白) 2013-01-18:若無法轉換，請回傳"ERR_"+原傳入值
	 */
	public static String getYM2YMD(String itemValue) {
		try {
			if (StringUtils.isNotBlank(itemValue)) {
				itemValue = itemValue.replaceAll("/", "-").trim();
				itemValue = itemValue.replaceAll("\\.", "-").trim();
				itemValue = itemValue.replaceAll("：", ":").trim();
				itemValue = itemValue.replaceAll(" ", "");
				int idx = itemValue.indexOf(":");
				if (idx > -1) {
					itemValue = itemValue.substring(idx+1,itemValue.length());
				}
				if(StringUtils.isNotBlank(itemValue)){
					String[] strDate = itemValue.split(TextDefine.SYMBOL_DASH);
					if (strDate.length == 2) {
						if (strDate[0].length() == 4) {// 西元年
							return itemValue + "-01";
						} else if (strDate[0].length() <= 3) {// 民國年
							return String
									.valueOf((Integer.parseInt(strDate[0]) + 1911)
											+ "-" + strDate[1])
									+ "-01";
						} else {
							return "ERR_" + itemValue;
						}
					} else {// 不應該有日期
						return "ERR_" + itemValue;
					}
				}
			}
/*			if (StringUtils.isNotBlank(itemValue)) {
				itemValue = itemValue.replaceAll("/", "-").trim();
				int idx = itemValue.indexOf(":");
				if (idx > -1) { // 有"資料年月:"字樣
					String value[] = itemValue.split(TextDefine.SYMBOL_COLON);
					String[] strDate = value[1].split(TextDefine.SYMBOL_DASH);
					if (strDate.length == 2) {
						if (strDate[0].trim().length() == 4) {// 西元年
							return value[1].trim() + "-01";
						} else if (strDate[0].trim().length() <= 3) {// 民國年
							return String.valueOf((Integer.parseInt(strDate[0]
									.trim()) + 1911) + "-" + strDate[1])
									+ "-01";
						} else {
							return "ERR_" + itemValue;
						}
					} else {// 不應該有日期
						return "ERR_" + itemValue;
					}
				} else {
					String[] strDate = itemValue.split(TextDefine.SYMBOL_DASH);
					if (strDate.length == 2) {
						if (strDate[0].length() == 4) {// 西元年
							return itemValue + "-01";
						} else if (strDate[0].length() <= 3) {// 民國年
							return String
									.valueOf((Integer.parseInt(strDate[0]) + 1911)
											+ "-" + strDate[1])
									+ "-01";
						} else {
							return "ERR_" + itemValue;
						}
					} else {// 不應該有日期
						return "ERR_" + itemValue;
					}
				}
			}*/
		} catch (Exception e) {
			logger.error("DXLUtil.java:getYM2YMD 將年月【"+itemValue+"】轉為年月日時出現錯誤:" + e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
		return itemValue;
	}

	/**
	 * 依傳入參數num決定傳回的日期型態
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            :號碼分類
	 * @return 若無值則回傳空字串
	 * @TODO 若num=0, 將回傳西元年度；若num=1，請回傳月份； 若num=2請回傳日期；若num=3，請回傳西元年月六碼，如：201303
	 */
	public static String getYMD(String itemValue, int num) {
		try {
			if (StringUtils.isNotBlank(itemValue)) {
				String[] strDate = itemValue.split("/");
				if (strDate.length == 3) {
					String year = "";
					if (strDate[0].length() == 4) {// 西元年
						year = strDate[0];
					} else if (strDate[0].length() <= 3) {// 民國年
						year = String
								.valueOf(Integer.parseInt(strDate[0]) + 1911);
					}
					String month = Util.addZeroWithValue(strDate[1], 2);
					String day = Util.addZeroWithValue(strDate[2], 2);

					if (0 == num) {
						return year;
					} else if (1 == num) {
						return month;
					} else if (2 == num) {
						return day;
					} else if (3 == num) {
						return year + month;
					}
				} else {// 格式有誤的日期
					return "ERR_" + itemValue;
				}
			}
		} catch (Exception e) {
			logger.error("DXLUtil.java:getYMD 【"+itemValue+"】依傳入參數【"+num+"】決定傳回的日期型態時出現錯誤:"
					+ e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
		return itemValue;
	}

	/**
	 * 依傳入值參數num決定回傳的字串須在左方增加幾位零
	 * 
	 * @param itemValue
	 *            String
	 * @param num
	 *            int
	 * @return String
	 * @Example 如傳入值為(3456,2)則回傳003456
	 */
	public static String getFillLeftZero(String itemValue, int num)
			throws Exception {
		if (StringUtils.isNotBlank(itemValue)) {
			for (int i = 0; i < num; i++) {
				StringBuffer sb = new StringBuffer();
				sb.append("0").append(itemValue);
				itemValue = sb.toString();
			}
		}
		return itemValue;
	}

	/**
	 * 若傳入值為"未評等"，則回傳"NA"；若為"免辦"，則回傳 "NO"；
	 * 其他傳入值若為"等"字或"級"字結尾，請去除"等"或"級"後回傳；若以上皆非，請回傳"Error"+原值
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 */
	public static String getGrade(String itemValue) {
		if (StringUtils.isNotBlank(itemValue.trim())) {
			String value = itemValue.trim();
			if ("未評等".equals(value)) {
				return "NA";
			} else if ("免辦".equals(value)) {
				return "NO";
			} else if ("等".equals(value.substring(value.length() - 1))
					|| "級".equals(value.substring(value.length() - 1))) {
				return value.substring(0, value.length() - 1);
			} else {
				return itemValue;
			}
		}
		return itemValue;
	}

	/**
	 * 若傳入值為"或等值其他貨幣"，則回傳"1"；若為"或等值其他外幣"，則回傳"2"；
	 * 若傳入值為"之等值其他外幣"，則回傳"3"；若以上皆非，請回傳原傳入值
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 */
	public static String getOtherCurr(String itemValue) {
		if (StringUtils.isNotBlank(itemValue)) {
			String value = itemValue.trim();
			if ("或等值其他貨幣".equals(value)) {
				return "1";
			} else if ("或等值其他外幣".equals(value)) {
				return "2";
			} else if ("之等值其他外幣".equals(value)) {
				return "3";
			}
		}
		return itemValue;
	}

	/**
	 * 若傳入值為"同前頁"，則回傳"Y"；其他請回傳"N"；
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 */
	public static String getRef(String itemValue) {
		String value = itemValue.trim();
		if ("同前頁".equals(value)) {
			return "1";
		} else {
			return "";
		}
	}

	/**
	 * 若傳入值為"詳"，則回傳"1"；其他則轉為空字串
	 * 
	 * @param itemValue
	 *            String
	 * @return String
	 */
	public static String getLmtOther(String itemValue) {
		String value = itemValue.trim();
		if ("詳".equals(value)) {
			return "1";
		} else {
			return TextDefine.EMPTY_STRING;
		}
	}

	/**
	 * 取得利率基礎-敘做利率最小(大)值
	 * 
	 * @param itemValue
	 * @param num
	 * @return
	 * @Example 傳入值可能有以下兩種：2.845 或1.581～1.691
	 */
	public static String getReRate(String itemValue, int num) {
		try {
			if (StringUtils.isBlank(itemValue)) {
				return itemValue;
			}
			int idx = itemValue.indexOf("～");
			if (num == 0) {
				// 若字串有"～",num[0]為前值Ex:1.581
				// 若字串無"～",=原傳入值Ex:2.845
				if (idx >= 0) {
					return itemValue.substring(0, idx).trim();
				} else {
					return itemValue;
				}
			} else if (num == 1) {
				// 若字串有"～",num[1]為後值Ex:1.691
				// 若字串無"～",=原傳入值Ex:2.845
				if (idx >= 0) {
					return itemValue.substring(idx + 1, itemValue.length())
							.trim();
				} else {
					return itemValue;
				}
			}
			return itemValue;
		} catch (Exception e) {
			logger.error("DXLUtil.java:getReRate 【"+itemValue+"】num=【"+num+"】取得利率基礎-敘做利率最小(大)值時出現錯誤:"
					+ e);
			e.printStackTrace();
			return "ERR_" + itemValue;
		}
	}

	/**
	 * 傳入值trim()後，長度大於零則為Y，否則為N
	 * 
	 * @param itemValue
	 * @return String
	 */
	public static String getYn1(String itemValue) {
		String value = itemValue.trim();
		if (value.length() > 0) {
			return "Y";
		} else {
			return "N";
		}
	}

	/**
	 * <pre>
	 *                                   notes   web 
	 * 建物所有權取得日期在99/6/25以前 |1  ---> 不變  
	 * 建物登記用途無「住」字樣        |2  --->|3
	 * 擔保品非屬土地、建物            |3  --->|4
	 * 非屬購屋/空地／建屋貸款         |4  --->|5
	 * 其他                            |5  --->|0
	 * </pre>
	 * 
	 * @param itemValue
	 * @return String
	 */
	public static String getPlusReason(String itemValue) {
		String value = Util.trimSpace(itemValue);
		if ("2".equals(value)) {
			return "3";
		} else if ("3".equals(value)) {
			return "4";
		} else if ("4".equals(value)) {
			return "5";
		} else if ("5".equals(value)) {
			return "0";
		} else {
			return value;
		}
	}

	/**
	 * 取得 itemValue * itemNum 後之值
	 * 
	 * @param itemValue
	 *            被乘數
	 * @param itemNum
	 *            乘數
	 * @return itemValue * itemNum 後之值，如發生錯誤回傳"ERR_"+原傳入值
	 */
	public static String getMultiple(String itemValue, int itemNum) {
		// 如為空白回傳空字串
		if (Util.isEmpty(itemValue)) {
			return "";
		}
		
		if (Util.isNumeric(itemValue)) {
			BigDecimal bd1 = new BigDecimal(itemValue);
			BigDecimal bd2 = new BigDecimal(itemNum);

			// 數值格式化
			NumberFormat numberFormat = new DecimalFormat(
					"##0.################################################");
			return numberFormat.format(bd1.multiply(bd2));
		}

		// 其他未預期的情況，直接回傳 ERR_ + 原傳入值
		return "ERR_" + itemValue;
	}

	/**
	 * <pre>
	 * 傳入值為1、2、5 其中之一時，回傳1
	 * 傳入值為3、4   其中之一時，回傳 2
	 * 若以上皆非，請回傳原值
	 * </pre>
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getDocKind(String itemValue) {
		String value = Util.trimSpace(itemValue);
		if ("1".equals(value) || "2".equals(value) || "5".equals(value)) {
			return "1";
		} else if ("3".equals(value) || "4".equals(value)) {
			return "2";
		} else {
			return value;
		}
	}
	
	/**
	 * <pre>
	 * 無     | 0  回傳2
	 * 借款人 | Y  回傳1
	 * 本行   | N  回傳0
	 * 若以上皆非，請回傳原值
	 * </pre>
	 * 
	 * @param itemValue
	 * @return
	 */
	public static String getUsdRateTax(String itemValue) {
		String value = Util.trimSpace(itemValue);
		if ("0".equals(value)) {
			return "2";
		} else if ("Y".equals(value)) {
			return "1";
		} else if ("N".equals(value)) {
			return "0";
		} else {
			return value;
		}
	}

	public static String[] list_suffix(File f, String suffix){
		//return f.list(FileFilterUtils.suffixFileFilter(suffix));
		List<String> r = new ArrayList<String>();
		for(File sufFile: FileUtils.listFiles(f, new String[]{suffix}, false)){
			r.add(sufFile.getName());
		}
		return r.toArray(new String[0]);
	}
	
	public static List<File> list_subdir(File f){
		//return f.listFiles();
		return FileFilterUtils.filterList(FileFilterUtils.directoryFileFilter(), f.listFiles());
	}
	
	public static String proc_readline(String s){
		//return StringEscapeUtils.escapeJava(s);
		
		return StringEscapeUtils.escapeHtml(s);
	}
	
	public static void main(String args[]) {
		logger.error(DXLUtil.getYM2YMD(" 2013-04"));
	}
}
