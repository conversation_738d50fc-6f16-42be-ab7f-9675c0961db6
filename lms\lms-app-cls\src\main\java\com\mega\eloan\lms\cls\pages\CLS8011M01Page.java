/* 
 * CLS8011M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.lms.base.flow.enums.CLSDocStatusEnum;
import com.mega.eloan.lms.cls.panels.CLS8011S01Panel;
import com.mega.eloan.lms.cls.panels.CLS8011S02Panel;
import com.mega.eloan.lms.model.C801M01A;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 個金個人資料清冊作業
 * </pre>
 * 
 * @since 2014/04/01
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls8011m01/{page}")
public class CLS8011M01Page extends AbstractEloanForm {

	@Autowired
	DocCheckService docCheckService;

	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		// 依權限設定button
		addAclLabel(model, new AclLabel("_btnDOC_EDITING", params,
				getDomainClass(), AuthType.Modify, CLSDocStatusEnum.編製中));
		
		addAclLabel(model, new AclLabel("_btnDOC_APPROVE", params,
				getDomainClass(), AuthType.Modify, CLSDocStatusEnum.已核准));
		
		renderJsI18N(CLS8011M01Page.class);
		// tabs
		int page = Util.parseInt(params.getString(EloanConstants.PAGE));
		String tabID = TAB_SIGN + Util.addZeroWithValue(page, 2); // 指定ID
		renderJsI18N(CLS8011M01Page.class);
		Panel panel = getPanel(page, params);
		panel.processPanelData(model, params);
		model.addAttribute("tabIdx", tabID);
	}

	public Panel getPanel(int index, PageParameters params) {
		Panel panel = null;
		switch (index) {
		case 1:
			panel = new CLS8011S01Panel(TAB_CTX, true);
			break;
		case 2:
			panel = new CLS8011S02Panel(TAB_CTX, true, "A");
			break;
		case 3:
			panel = new CLS8011S02Panel(TAB_CTX, true, "B");
			break;
		case 4:
			panel = new CLS8011S02Panel(TAB_CTX, true, "C");
			break;
		}

		renderJsI18N(CLS8011M01Page.class);
		return panel;
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C801M01A.class;
	}
	
    
	@Override
    protected String getContentPageName() {
        return "cls/pages/CLS8011M01Page";
    }
}
