var gridDfd = $.Deferred();
$(document).ready(function() {
	var grid = $("#gridview").iGrid({
		handler : 'lms1045gridhandler',
		height : 350,
		sortname: 'caseNo',
		rowNum:17,
		rownumbers:true,
		shrinkToFit: false,
	    postData: {
	        formAction: "queryc123data",
	        docStatus: viewstatus,
	        ownBrId: userInfo.unitNo
	    },
	    
		colModel : 
		[{name : 'oid', hidden : true}
		,{name : 'mainId',hidden : true}
		,{name : 'dupNo',hidden : true}
		,{colHeader : i18n.lms1045v01["C123M01A.caseNo"], width : 180, name : 'caseNo', sortable : true,formatter:'click', onclick : openDoc}
		,{colHeader : i18n.lms1045v01["C123M01A.custId"], width:150, name:'custId'}		
		,{colHeader : i18n.lms1045v01["C123M01A.custName"], width : 120, name : 'custName'}
		]
	});
	
	
	$("#buttonPanel").find("#btnAdd").click(function(){
		var formId = "addborrowForm";
		var $addborrow = $("#"+formId);
		$addborrow.reset();
		$addborrow.find("input[name=rborrowA]:checked").trigger('click');

		$("#thickboxaddborrow").thickbox({		
			title : '', width : 800, height : 380, modal : false, i18n:i18n.def,
			buttons : {				
				"close" : function(){
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
				     });
				}
			}
		});
	}).end().find("#btnDelete").click(function(){
		var rows = $("#gridview").getGridParam('selrow');
        var mainOid = "";
        if (rows != 'undefined' && rows != null && rows != 0) {
            var data = $("#gridview").getRowData(rows);
            mainOid = data.oid;
        }
        if (mainOid == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    handler: "lms1045m01formhandler",
                    type: "POST",
                    dataType: "json",
                    data: {
                        formAction: "deleteMark",
                        mainOid: mainOid,
                        mainDocStatus: viewstatus,
                        docStatus: viewstatus
                    },
                    success: function(obj){
                    	if(obj.saveOkFlag){
                    		API.showMessage(i18n.def.runSuccess);//執行成功
                    	}
                        $("#gridview").trigger("reloadGrid");
                    }
                });
            }
        });		
	}).end().find("#btnFilter").click(function(){
		var _id = "_div_filter";
		var _form = _id+"_form";
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("<table class='tb2'>");
			dyna.push("<tr><td class='hd1' nowrap>"+i18n.lms1045v01["C123M01A.custId"]+"</td><td><input type='text' name='custId' id='custId' value='' maxlength='10'></td></tr>");		
			dyna.push("</table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		     $('body').append(dyna.join(""));
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ title: i18n.def['query'], width: 400, height: 185, modal: true,
	        valign: "bottom", align: "center", i18n: i18n.def,
	        buttons: {
	            "sure": function(){       	
					$("#gridview").jqGrid("setGridParam", {
						postData : $("#"+_form).serializeData(),
						page : 1,
						search : true
					}).trigger("reloadGrid");
					$.thickbox.close();
	            },
	            "cancel": function(){
	            	 $.thickbox.close();
	            }
	        }
	    });
	});
	
	function openDoc(cellvalue, options, rowObject){
    	var postData = {
    			'mainOid': rowObject.oid, 
    			'mainId': rowObject.mainId,
    			'mainDocStatus': viewstatus,
    			'custId': rowObject.custId,
    			'dupNo': rowObject.dupNo,
    			ownBrId: userInfo.unitNo
    	};
    	
    	if (typeof noOpenDoc != 'undefined' && noOpenDoc == 'Y'){
    		postData['noOpenDoc'] = true;
    	};
    	
		$.form.submit({
			url : '../lms/lms1045m01/01',
			data : postData,
			target : '_blank'
		});
	}
	
	function chose_custId(){
		var my_dfd = $.Deferred();
		AddCustAction.open({
	    		handler: 'lms1045m01formhandler',
				action : 'echo_custId',
				data : {
	            },
				callback : function(json){
	            	$.thickbox.close();					
					my_dfd.resolve( json );					
				}
			});
		return my_dfd.promise();
	}
});

$("#getCustData").click(function(){
	var formId = "addborrowForm";
	var $addborrow = $("#"+formId);
	
	var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
	var $custName = $addborrow.find("[name=addborrowForm_custName]").val();
	if(($custId != null && $custId != undefined && $custId != '')
	&& ($custName != null && $custName != undefined && $custName != '')){
		// 統一編號、名稱擇一輸入引進即可
		CommonAPI.showErrorMessage(i18n.lms1045v01["alert.01"]);
	}else if(($custId == null || $custId == undefined || $custId == '')
	&& ($custName == null || $custName == undefined || $custName == '')){
		// 請輸入統一編號或名稱
		CommonAPI.showErrorMessage(i18n.lms1045v01["alert.02"]);
	}else{
	    var defaultOption = {};
		if($custId != null && $custId != undefined && $custId != ''){
			defaultOption = {
				defaultValue: $custId //預設值 
			};
		}else{
			defaultOption = {
				defaultName : $custName
			};				
		}			
		//綁入MegaID
		CommonAPI.openQueryBox(
			$.extend({
				defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
                divId: formId, //在哪個div 底下
                isInSide:false, 
                autoResponse: { // 是否自動回填資訊 
                       id: "addborrowForm_custId", // 統一編號欄位ID 
                       dupno: "addborrowForm_dupNo", // 重覆編號欄位ID 
                       name: "addborrowForm_custName" // 客戶名稱欄位ID 
                },fn:function(obj){	
					if( $addborrow.valid()){
						var $ObjcustId=obj.custid;
						var $Objdupno=obj.dupno;
						var $Objname=obj.name;
						$.ajax({							
							handler: "lms1045m01formhandler",
			                action : 'newc123m01a',
			                type: "POST",
			                dataType: "json",
			                data : {
								custId:obj.custid,
								dupNo:obj.dupno,
								custName:obj.name             
				 			},
				 				
			                success: function(obj){
			        			$.form.submit({
			                		url: '../lms/lms1045m01/01',
			                		data: {
			                    		formAction: "queryc123m01a",
			                    		mainOid: obj.mainOid,
			                    		mainDocStatus: viewstatus,
			                    		txCode: txCode,
			                    		custId:$ObjcustId,
			        					dupNo:$Objdupno,
			        					custName:$Objname
			                		},
			                		target: '_blank'
			           	 		});
								$("#gridview").trigger('reloadGrid');
								$.thickbox.close();	
							}	
						}).fail(function(){ $addborrow.reset(); });						
					}
				}
			},defaultOption)
		);			
	}
});	