package com.mega.eloan.lms.model;


import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

import javax.persistence.*;

import org.apache.commons.lang3.builder.ToStringExclude;

import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 購置房屋擔保放款風險權數檢核表(自用住宅貸款檢核表)
 * <ul>
 * <li>2022/06,EL08034,J-111-0096 Web e-Loan消金因應不動產暴險以貸放比率(LTV)決定適用之風險權數，報表名稱改為「自用住宅貸款檢核表」
 * </li>
 * </ul>
 * select * from lms.c102m01a where mainid in (select refMainId from lms.l140s02f where mainid=?)
 */
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C102M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C102M01A extends Meta implements IDataObject, IDocObject {
	/*
	 CLS1151M01FormHandler :: isC102M01ACase(...)
	  判斷是否授信科目 14501500(673) 13506200(473)
	*/
	private static final long serialVersionUID = 1L;
	
	@ToStringExclude
	@OneToMany(mappedBy = "c102m01a", fetch = FetchType.LAZY)
	private Set<C102A01A> c102a01a;
	
	public Set<C102A01A> getC102a01a() {
		return c102a01a;
	}

	public void setC102a01a(Set<C102A01A> c102a01a) {
		this.c102a01a = c102a01a;
	}	

	/** 額度序號 **/
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 放款帳號 **/
	@Column(name="ALOANAC", length=14, columnDefinition="CHAR(14)")
	private String aLoanAC;

	/** 首撥日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ALOANDATE", columnDefinition="DATE")
	private Date aLoanDate;

	/** 融資分類 **/
	@Column(name="ALOANPURPOSE", length=1, columnDefinition="CHAR(1)")
	private String aLoanPurpose;

	/** 
	 * 借款人為具有完全行為能力之中華民國國民<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	@Column(name="CHK11", length=1, columnDefinition="CHAR(1)")
	private String chk11;

	/** 
	 * 擔保物提供人為借款人本人或其配偶<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	@Column(name="CHK12", length=1, columnDefinition="CHAR(1)")
	private String chk12;

	/** 
	 * 擔保物提供人房屋財產查核清單或財產歸屬資料清單，符合於國內無自用住宅<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	@Column(name="CHK13", length=1, columnDefinition="CHAR(1)")
	private String chk13;

	/** 
	 * 資金用途係購屋供借款人本人、配偶、未成年子女、父母或配偶父母居住使用，且未供出租或營業使用<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	@Column(name="CHK14", length=1, columnDefinition="CHAR(1)")
	private String chk14;

	/** 
	 * 是否為自用住宅<p/>
	 * ※2012--10-25 新增<br/>
	 *  當上述四個條件皆為「是」即為Y<br/>
	 *  Y<br/>
	 *  N
	 */
	@Column(name="SELFCHK", length=1, columnDefinition="CHAR(1)")
	private String selfChk;

	/** 本案適用於風險權數{1：45% , 2：100% , 3：35% , 4：75%} 註:首撥日在100/04/21(2011-04-21)前案件為 */
	@Column(name="RSKFLAG", length=1, columnDefinition="CHAR(1)")
	private String rskFlag;

	/** 
	 * 電子表單列印套版版本ID{'':表示舊版, 'V20171231':表示自2017-12-31生效的新版}
	 */
	@Column(name="RPTID", length=32, columnDefinition="VARCHAR(32)")
	private String rptId;



	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得放款帳號 **/
	public String getALoanAC() {
		return this.aLoanAC;
	}
	/** 設定放款帳號 **/
	public void setALoanAC(String value) {
		this.aLoanAC = value;
	}

	/** 取得首撥日期 **/
	public Date getALoanDate() {
		return this.aLoanDate;
	}
	/** 設定首撥日期 **/
	public void setALoanDate(Date value) {
		this.aLoanDate = value;
	}

	/** 取得融資分類 **/
	public String getALoanPurpose() {
		return this.aLoanPurpose;
	}
	/** 設定融資分類 **/
	public void setALoanPurpose(String value) {
		this.aLoanPurpose = value;
	}

	/** 
	 * 取得借款人為具有完全行為能力之中華民國國民<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	public String getChk11() {
		return this.chk11;
	}
	/**
	 *  設定借款人為具有完全行為能力之中華民國國民<p/>
	 *  1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 **/
	public void setChk11(String value) {
		this.chk11 = value;
	}

	/** 
	 * 取得擔保物提供人為借款人本人或其配偶<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	public String getChk12() {
		return this.chk12;
	}
	/**
	 *  設定擔保物提供人為借款人本人或其配偶<p/>
	 *  1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 **/
	public void setChk12(String value) {
		this.chk12 = value;
	}

	/** 
	 * 取得擔保物提供人房屋財產查核清單或財產歸屬資料清單，符合於國內無自用住宅<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	public String getChk13() {
		return this.chk13;
	}
	/**
	 *  設定擔保物提供人房屋財產查核清單或財產歸屬資料清單，符合於國內無自用住宅<p/>
	 *  1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 **/
	public void setChk13(String value) {
		this.chk13 = value;
	}

	/** 
	 * 取得資金用途係購屋供借款人本人、配偶、未成年子女、父母或配偶父母居住使用，且未供出租或營業使用<p/>
	 * 1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 */
	public String getChk14() {
		return this.chk14;
	}
	/**
	 *  設定資金用途係購屋供借款人本人、配偶、未成年子女、父母或配偶父母居住使用，且未供出租或營業使用<p/>
	 *  1.基本條件檢核：<br/>
	 *  銀行法第12條之1：(自用住宅)<br/>
	 *  Y/N(符合/不符合)
	 **/
	public void setChk14(String value) {
		this.chk14 = value;
	}

	/** 
	 * 取得是否為自用住宅<p/>
	 * ※2012--10-25 新增<br/>
	 *  當上述四個條件皆為「是」即為Y<br/>
	 *  Y<br/>
	 *  N
	 */
	public String getSelfChk() {
		return this.selfChk;
	}
	/**
	 *  設定是否為自用住宅<p/>
	 *  ※2012--10-25 新增<br/>
	 *  當上述四個條件皆為「是」即為Y<br/>
	 *  Y<br/>
	 *  N
	 **/
	public void setSelfChk(String value) {
		this.selfChk = value;
	}

	/** 取得本案適用於風險權數{1：45% , 2：100% , 3：35% , 4：75%} 註:首撥日在100/04/21(2011-04-21)前案件為 */
	public String getRskFlag() {
		return this.rskFlag;
	}
	/** 設定本案適用於風險權數{1：45% , 2：100% , 3：35% , 4：75%} 註:首撥日在100/04/21(2011-04-21)前案件為 */
	public void setRskFlag(String value) {
		this.rskFlag = value;
	}

	/** 
	 * 取得電子表單列印套版版本ID{'':表示舊版, 'V20171231':表示自2017-12-31生效的新版}
	 */
	public String getRptId() {
		return this.rptId;
	}
	/**
	 *  設定電子表單列印套版版本ID{'':表示舊版, 'V20171231':表示自2017-12-31生效的新版}
	 **/
	public void setRptId(String value) {
		this.rptId = value;
	}
}
