var lmsM01Json = {
    docType: responseJSON.docType,
    docCode: responseJSON.docCode,
    docKind: responseJSON.docKind,
    authLvl: responseJSON.authLvl,
    mainId: responseJSON.mainId,
    docStatus: responseJSON.mainDocStatus,
    docStatus2: responseJSON.docStatus,
    areaDocstatus: responseJSON.areaDocstatus,
    unitNo: userInfo.unitNo,
    page: responseJSON.page,
    //控制文件類別顯示
    showHideTitle: function(){
        var $showBorrowData = $("#showBorrowData");
        $showBorrowData.find("#title1301").html("");
        $.ajax({
            handler: "lms1201formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "queryAreaTitle",
                docKind: lmsM01Json.docKind,
                mainId: responseJSON.mainId,
                brnGroup: responseJSON.brnGroup
            }
		}).done(function(obj){
                // 所屬營運中心設定
                responseJSON["brnGroup"] = obj.brnGroup;
                if (obj.areaTitle != undefined && obj.areaTitle != null && obj.areaTitle != "") {
                    $showBorrowData.find("#title1301").html(obj.areaTitle);
                    if (lmsM01Json.docType == "1") {
                        $showBorrowData.find("#title0a").show();
                        $showBorrowData.find("#title0b").hide();
                    }
                    else 
                        if (lmsM01Json.docType == "2") {
                            $showBorrowData.find("#title0a").hide();
                            $showBorrowData.find("#title0b").show();
                        }
                    if (lmsM01Json.docCode == "3") {
                        $showBorrowData.find("#title1y").show();
                    }
                    else 
                        if (lmsM01Json.docCode == "4") {
                            $showBorrowData.find("#title1x").show();
                        }
                }
                else {
                    if (lmsM01Json.docType == "1") {
                        $showBorrowData.find("#title0a").show();
                        $showBorrowData.find("#title0b").hide();
                    }
                    else 
                        if (lmsM01Json.docType == "2") {
                            $showBorrowData.find("#title0a").hide();
                            $showBorrowData.find("#title0b").show();
                        }
                    if (lmsM01Json.docKind == "2") {
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1").show();
                            $showBorrowData.find("#title1a").hide();
                            $showBorrowData.find("#title1b").hide();
                        }
                        else 
                            if (lmsM01Json.docCode == "2") {
                                $showBorrowData.find("#title1a").show();
                                $showBorrowData.find("#title1").hide();
                                $showBorrowData.find("#title1b").hide();
                            }
                            else 
                                if (lmsM01Json.docCode == "3") {
                                    $showBorrowData.find("#title1b").show();
                                    $showBorrowData.find("#title1").hide();
                                    $showBorrowData.find("#title1a").hide();
                                    $showBorrowData.find("#title1y").show();
                                }
                                else 
                                    if (lmsM01Json.docCode == "4") {
                                        // other.msg114=授權外案件簽報書(異常通報案件)
                                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg114"]);
                                        $showBorrowData.find("#title1x").show();
                                    }
                    }
                    else 
                        if (lmsM01Json.docKind == "1") {
                            $showBorrowData.find("#title1").hide();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1e").hide();
                            $showBorrowData.find("#title1f").hide();
                            $showBorrowData.find("#title1g").hide();
                            $showBorrowData.find("#title1h").hide();
                            $showBorrowData.find("#title1i").hide();
                            $showBorrowData.find("#title1j").hide();
                            $showBorrowData.find("#title1k").hide();
                            if (responseJSON.authLvl == "2") {
                                // 總行授權內
                                if (lmsM01Json.docCode == "1") {
                                    $showBorrowData.find("#title1f").show();
                                    $showBorrowData.find("#title1g").hide();
                                    $showBorrowData.find("#title1h").hide();
                                }
                                else 
                                    if (lmsM01Json.docCode == "2") {
                                        $showBorrowData.find("#title1g").show();
                                        $showBorrowData.find("#title1f").hide();
                                        $showBorrowData.find("#title1h").hide();
                                    }
                                    else 
                                        if (lmsM01Json.docCode == "3") {
                                            $showBorrowData.find("#title1h").show();
                                            $showBorrowData.find("#title1f").hide();
                                            $showBorrowData.find("#title1g").hide();
                                            $showBorrowData.find("#title1y").show();
                                        }
                                        else 
                                            if (lmsM01Json.docCode == "4") {
                                                // other.msg115=總行授權內案件簽報書(異常通報案件)
                                                $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg115"]);
                                                $showBorrowData.find("#title1x").show();
                                            }
                            }
                            else 
                                if (responseJSON.authLvl == "1") {
                                    // 分行授權內
                                    if (lmsM01Json.docCode == "1") {
                                        $showBorrowData.find("#title1c").show();
                                        $showBorrowData.find("#title1d").hide();
                                        $showBorrowData.find("#title1e").hide();
                                    }
                                    else 
                                        if (lmsM01Json.docCode == "2") {
                                            $showBorrowData.find("#title1d").show();
                                            $showBorrowData.find("#title1c").hide();
                                            $showBorrowData.find("#title1e").hide();
                                        }
                                        else 
                                            if (lmsM01Json.docCode == "3") {
                                                $showBorrowData.find("#title1e").show();
                                                $showBorrowData.find("#title1c").hide();
                                                $showBorrowData.find("#title1d").hide();
                                                $showBorrowData.find("#title1y").show();
                                            }
                                            else 
                                                if (lmsM01Json.docCode == "4") {
                                                    // other.msg116=分行授權內案件簽報書(異常通報案件)
                                                    $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                                                    $showBorrowData.find("#title1x").show();
                                                }
                                }
                                else 
                                    if (responseJSON.authLvl == "3") {
                                        // 營運中心授權內
                                        if (responseJSON.docCode == "1") {
                                            $showBorrowData.find("#title1i").show();
                                            $showBorrowData.find("#title1j").hide();
                                            $showBorrowData.find("#title1k").hide();
                                        }
                                        else 
                                            if (responseJSON.docCode == "2") {
                                                $showBorrowData.find("#title1j").show();
                                                $showBorrowData.find("#title1i").hide();
                                                $showBorrowData.find("#title1k").hide();
                                            }
                                            else 
                                                if (responseJSON.docCode == "3") {
                                                    $showBorrowData.find("#title1k").show();
                                                    $showBorrowData.find("#title1j").hide();
                                                    $showBorrowData.find("#title1i").hide();
                                                    $showBorrowData.find("#title1y").show();
                                                }
                                                else 
                                                    if (lmsM01Json.docCode == "4") {
                                                        // other.msg117=營運中心授權內案件簽報書(異常通報案件)
                                                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg117"]);
                                                        $showBorrowData.find("#title1x").show();
                                                    }
                                    }
                                    else {
                                        // 預設顯示
                                        if (lmsM01Json.docCode == "1") {
                                            $showBorrowData.find("#title1c").show();
                                            $showBorrowData.find("#title1d").hide();
                                            $showBorrowData.find("#title1e").hide();
                                        }
                                        else 
                                            if (lmsM01Json.docCode == "2") {
                                                $showBorrowData.find("#title1d").show();
                                                $showBorrowData.find("#title1c").hide();
                                                $showBorrowData.find("#title1e").hide();
                                            }
                                            else 
                                                if (lmsM01Json.docCode == "3") {
                                                    $showBorrowData.find("#title1e").show();
                                                    $showBorrowData.find("#title1c").hide();
                                                    $showBorrowData.find("#title1d").hide();
                                                    $showBorrowData.find("#title1y").show();
                                                }
                                                else 
                                                    if (lmsM01Json.docCode == "4") {
                                                        // other.msg116=分行授權內案件簽報書(異常通報案件)
                                                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                                                        $showBorrowData.find("#title1x").show();
                                                    }
                                    }
                        }
                }
        });
    },
    //依照不同文件狀態控制唯讀
    setReadOnly: function(auth){
        //分行編製中、分行待補件可編輯，其他都不可編輯
        if (lmsM01Json.docStatus == "010" ||
        lmsM01Json.docStatus == "01O" ||
        lmsM01Json.docStatus == "01K" ||
        lmsM01Json.docStatus == "07O|0EO" ||
        lmsM01Json.docStatus == "07O") {
            //編製中且沒被鎖定
            if (auth.Modify && !thickboxOptions.readOnly) {
                $("#L120M01dForm06").find("#phraseList").show();
                responseJSON["readOnly"] = false;
                if (responseJSON.page == "02" || responseJSON.page == "10") {
                    setIgnoreTempSave(true);
                }
                else {
                    setIgnoreTempSave(false);
                }
                $(this).find("button").show();
                if (responseJSON.page == "12" && lmsM01Json.docStatus == "01K") {
                    // 會簽會議決議
                    var $L120M01aForm12 = $("#L120M01aForm12");
                    $L120M01aForm12.readOnlyChilds(true);
                    $L120M01aForm12.find("button").hide();
                    // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                    // Miller added at 2012/09/07
                    if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                    (responseJSON.mainDocStatus == "L1H" &&
                    userInfo.unitNo == "918")) {
                        //$L120M01aForm12.find("#filehideS12").show();
                        $L120M01aForm12.find("#filehideS12 button").show();
                    }
                    else {
                        //$L120M01aForm12.find("#filehideS12").hide();
                    }
                }
            }
            else {
                $("#L120M01dForm06").find("#phraseList").hide();
                responseJSON["readOnly"] = true;
                setReadOnly1();
                setIgnoreTempSave(true);
            }
        }
        else {
            //非編製中
            $("#L120M01dForm06").find("#phraseList").hide();
            responseJSON["readOnly"] = true;
            if (lmsM01Json.docCode == "4") {
                setReadOnly3();
            }
            else {
                setReadOnly1();
            }
            setIgnoreTempSave(true);
            // 將確認日期挑選改成非唯讀
            $("#openChecDatekBox").find(":disabled,:input").removeAttr("disabled").removeAttr("readonly").end().find("#forCheckDate").datepicker();
            if (responseJSON.mainDocStatus == "L1H") {
                $("#openLmsCase").readOnlyChilds(false);
            }
            $("#sendTo").readOnlyChilds(false);
            //顯示上方主要標題按鈕
            $("#buttonPanel :button").show();
            //顯示異動文件記錄按鈕
            $("#readDocLog").show();
            if (this.page == "17") {
                $("#L120M01aForm17 :button").show();
            }
        }
    },
    // 依條件顯示呈主管覆核確認訊息內容
    checkConMsg: function(defaultMsg){
        if (responseJSON.docCode == "3" || responseJSON.docCode == "4") {
            // 陳復述案與異常通報不需改變確認訊息內容
            return defaultMsg;
        }
        if (lmsM01Json.isSpectialHead()) {
            // other.msg118=會簽內容回覆前，此份簽報書會先呈送主管覆核，是否確認執行?
            return i18n.lmscommom["other.msg118"];
        }
        else {
            var unitNo = userInfo.unitNo;
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201" || unitNo == "940" || unitNo == "943"  || unitNo == "149") {
                if (responseJSON.docKind == "2") {
                    // 授權外
                    if (responseJSON._areaChk == "3" || responseJSON._areaChk == "1") {
                        // other.msg119=呈授管處審核批覆前此份簽報書會先呈主管覆核，是否確定執行？
                        return i18n.lmscommom["other.msg119"];
                    }
                    else 
                        if (responseJSON._areaChk == "2") {
                            // other.msg120=呈授管處會簽前此份簽報書會先呈主管覆核，是否確定執行？
                            return i18n.lmscommom["other.msg120"];
                        }
                        else {
                            //l120m01a.message01=是否呈主管覆核？
                            return defaultMsg;
                        }
                }
                else {
                    // 授權內
                    //l120m01a.message01=是否呈主管覆核？
                    return defaultMsg;
                }
            }
            else {
                //l120m01a.message01=是否呈主管覆核？
                return defaultMsg;
            }
        }
    },
    // 控制特殊分行(國外部、國金部、金控總部分行、財務部、財富管理處)按鈕顯示
    checkSpecitalBtn: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        var authModify = auth.Modify; //經辦權限
        var unitNo = userInfo.unitNo;
        if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
        unitNo == "011" ||
        unitNo == "201" ||
        unitNo == "940" ||
        unitNo == "943" ||
        unitNo == "149") {
            if (responseJSON.docKind == "2") {
                // 授權外
                // 控制特殊分行已會簽(會簽後修改)按鈕顯示/隱藏	
                if ((responseJSON.mainDocStatus == "03K" || responseJSON.mainDocStatus == "01K" ||
                responseJSON.txCode == "337023" ||
                responseJSON.txCode == "337024" ||
                responseJSON.txCode == "337025") &&
                authModify) {
                    $("#hideSpecialBtn2").show();
                    if (lmsM01Json.docStatus2 == "01K" || responseJSON.mainDocStatus == "01K" ||
                    (responseJSON.txCode == "337023" || responseJSON.txCode == "337024" ||
                    responseJSON.txCode == "337025")) {
                        // 會簽後修改編製中
                        //$("#hideSpecialBtn").hide();
                        $("#hideSpecialBtn").find("#showAfterSignBtn").show();
                        $("#sAfterSign").hide();
                        if ((responseJSON.hqMeetFlag == "" || responseJSON.hqMeetFlag == undefined ||
                        responseJSON.hqMeetFlag == null)) {
                            // 無提會時只有案件審核層級，故直接將登錄按鈕隱藏
                            $("#btnLogeIN").hide();
                        }
                    }
                    if (responseJSON.mainDocStatus == "03K") {
                        $("#hideSpecialBtn").hide();
                    }
                }
                else {
                    $("#hideSpecialBtn2").hide();
                }
                if (responseJSON.mainDocStatus == "01O" || responseJSON.mainDocStatus == "07O|0EO") {
                    if (lmsM01Json.docStatus2 == "01K") {
                        // 會簽後內容修改到編製中後，簽報書沒有顯示【儲存】與【登錄】按鈕
                        //$("#hideSpecialBtn").hide();
                        $("#hideSpecialBtn").find("#showAfterSignBtn").show();
                        
                        $("#hideSpecialBtn2").show();
                        $("#hideSpecialBtn2").find("#btnLogeIN").hide();
                        $("#sAfterSign").hide();
                        if ((responseJSON.hqMeetFlag == "" || responseJSON.hqMeetFlag == undefined ||
                        responseJSON.hqMeetFlag == null)) {
                            // 無提會時只有案件審核層級，故直接將登錄按鈕隱藏
                            $("#btnLogeIN").hide();
                        }
                    }
                    else {
                        $("#hideSpecialBtn").show();
                    }
                }
                else {
                    if (responseJSON.txCode == "337023" || responseJSON.txCode == "337024" || responseJSON.txCode == "337025") {
                        $("#sAfterSign2").show();
                    }
                    if (lmsM01Json.docStatus2 == "01K" || responseJSON.mainDocStatus == "01K") {
                    }
                    else {
                        $("#hideSpecialBtn").hide();
                    }
                }
                // 控制授管處會簽意見按鈕顯示/隱藏
                if (responseJSON.areaChk == "2" && responseJSON.mainDocStatus == "L1H" && authModify) {
                    // 授管處會簽審查中
                    $("#specitalHead").show();
                }
                else {
                    $("#specitalHead").hide();
                }
            }
            else {
                // 授權內
                $("#hideSpecialBtn").hide();
                $("#hideSpecialBtn2").hide();
                $("#specitalHead").hide();
            }
        }
        else {
            $("#hideSpecialBtn").hide();
            $("#hideSpecialBtn2").hide();
            $("#specitalHead").hide();
        }
    },
    // 控制授管處會簽意見顯示/隱藏
    checkSpectialHead: function(){
        var auth = (responseJSON ? responseJSON.Auth : {}); //權限
        var authModify = auth.Modify; //經辦權限		
        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201"  ||
            unitNo == "940"  ||
            unitNo == "943"  ||
            unitNo == "149") {
                if (responseJSON.docKind == "2") {
                    // 授權外
                    // 控制授管處會簽意見按鈕顯示/隱藏
                    if (responseJSON.areaChk == "2" && responseJSON.mainDocStatus == "L1H" && authModify) {
                        // 授管處會簽審查中
                        $("#specitalHead").show();
                        $("#hCkHead").show();
                        if ($("#specitalHead :radio").is(":checked")) {
                            $("#showCkHead").show();
                        }
                    }
                    else {
                        $("#specitalHead").hide();
                        $("#showCkHead").hide();
                        $("#hCkHead").hide();
                    }
                    
                }
                else {
                    // 授權內
                    $("#specitalHead").hide();
                    $("#showCkHead").hide();
                    $("#hCkHead").hide();
                }
            }
            else {
                $("#specitalHead").hide();
                $("#showCkHead").hide();
                $("#hCkHead").hide();
            }
        }
        else {
            $("#specitalHead").hide();
            $("#showCkHead").hide();
            $("#hCkHead").hide();
        }
    },
    // 是否為特殊分行起案授權外送會簽簽報書，且文件呈送至授管處審查中
    isSpectialHead: function(){
        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201" ||
            unitNo == "940" ||
            unitNo == "943" ||
            unitNo == "149") {
                if (responseJSON.docKind == "2") {
                    // 授權外
                    // 控制授管處會簽意見按鈕顯示/隱藏
                    if (responseJSON.areaChk == "2" && responseJSON.mainDocStatus == "L1H") {
                        // 授管處會簽審查中
                        return true;
                    }
                    else {
                        return false;
                    }
                    
                }
                else {
                    // 授權內
                    return false;
                }
            }
            else {
                return false;
            }
        }
        else {
            return false;
        }
    },
    // 是否為特殊分行起案授權外送會簽簽報書，且文件呈送至已會簽
    isSpectial: function(){
        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201" ||
            unitNo == "940" ||
            unitNo == "943" ||
            unitNo == "149") {
                if (responseJSON.docKind == "2") {
                    // 授權外
                    if (responseJSON.areaChk == "2" && responseJSON.mainDocStatus == "03K") {
                        // 已會簽
                        $("#spectialPrint").show();
                        $("#signPrint").hide();
                        return true;
                    }
                    else 
                        if (responseJSON.mainDocStatus == "01K" || responseJSON.mainDocStatus == "02K" || responseJSON.mainDocStatus == "05O") {
                            if (responseJSON.mainDocStatus == "05O" && !(responseJSON.areaChk == "2" || responseJSON.areaChk == "4")) {
                            }
                            else {
                                $("#spectialPrint").show();
                                $("#signPrint").hide();
                            }
                            return false;
                        }
                        else {
                            if (responseJSON.areaChk == "2" || responseJSON.areaChk == "4") {
                                $("#spectialPrint").show();
                                $("#signPrint").hide();
                            }
                            return false;
                        }
                    
                }
                else {
                    // 授權內
                    return false;
                }
            }
            else {
                return false;
            }
        }
        else {
            return false;
        }
    },
    //判斷是否為特殊分行送會簽
    isSpectialAndSign: function(){
        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201" ||
            unitNo == "940" ||
            unitNo == "943" ||
            unitNo == "149") {
                if (responseJSON.areaChk == "2") {
                    return true;
                }
            }
        }
        return false;
    },
    //判斷是否為特殊分行送審查
    isSpectialAndSend: function(){
        if (responseJSON.caseBrId != undefined && responseJSON.caseBrId != null && responseJSON.caseBrId != "") {
            var unitNo = responseJSON.caseBrId.toString().substring(0, 3);
            if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
            unitNo == "011" ||
            unitNo == "201" ||
            unitNo == "940" ||
            unitNo == "943" ||
            unitNo == "149") {
                if (responseJSON.areaChk == "1" || responseJSON.areaChk == "3") {
                    return true;
                }
            }
        }
        return false;
    },
    //判斷是否為特殊分行
    isSpectialBank: function(){
        var unitNo = userInfo.unitNo;
		var unitType = userInfo.unitType;	
        var result = false
        if (unitNo == "007" || unitNo == "009" || unitNo == "025" ||
        unitNo == "011" ||
        unitNo == "201" || unitNo == "940" || unitNo == "943" || unitNo == "149"  || (unitNo=="918" && unitType !="4") ) {
            result = true;
        }
        return result
        
    },
    // 登入單位是否為授管處
    isHead: function(){
        var unitNo = userInfo.unitNo;
        var unitType = userInfo.unitType;	
        if (unitNo == "918" && unitType == "4"  ) {
            return true;
        }
        else {
            return false;
        }
    },
    // 登入單位是否為營運中心
    isArea: function(){
        var unitNo = userInfo.unitNo;
        if (unitNo == "920" || unitNo == "922" ||
        unitNo == "931" ||
        unitNo == "932" ||
        unitNo == "933" ||
        unitNo == "934" ||
        unitNo == "935") {
            return true;
        }
        else {
            return false;
        }
    },
    //分行選擇主管box界面點擊確定後執行指定flow
    selectBossbox2: function(fn, jsonArg){
        $.ajax({
            handler: "lms1201formhandler",
            data: {
                formAction: "setBoss",
                mainId: responseJSON.mainId
            }
		}).done(function(json){
                //alert(JSON.stringify(json));
                $("#selectBossForm").setData(json, false);
                
                // 授信主管設定
                var $newBossSpan = $("#newBossSpan");
                //清空原本的
                $newBossSpan.empty();
                
                var newdiv = "";
                var val = parseInt(json.mainBossCount, 10);
                if (val > 1) {
                    // 設定人數select
                    $("#numPerson").val(val);
                    for (var i = 2, count = val + 1; i < count; i++) {
                        //other.msg165=第
                        //other.msg166=位
                        //other.msg167=授信主管
                        newdiv += "<div>" + i18n.lmscommom['other.msg165'] + i +
                        i18n.lmscommom['other.msg166'] +
                        "&nbsp;" +
                        i18n.lmscommom['other.msg167'] +
                        "&nbsp;&nbsp;&nbsp;<select name=boss" +
                        i +
                        " class='boss'/>&nbsp;" +
                        "<span class='fg-buttonset'>" +
                        "<span class='fg-child'>" +
                        "<button class='ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only fg-button' onclick='lmsM03Json.beforeOpen()' type='button' role='button' aria-disabled='false'>" +
                        "<span class='ui-button-text'>" +
                        "<span class='text-only'>常用主管</span>" +
                        "</span></button></span></span></div>"
                    }
                    $newBossSpan.append(newdiv);
                    var copyOption = $("#mainBoss").html();
                    $newBossSpan.find("[name^=boss]").html(copyOption);
                }
                for (o in json.mainBossList) {
                    $("[name='boss" + (parseInt(o) + 1) + "']").val(json.mainBossList[o]);
                }
                
                
                if (!json.noBossList2) {
                    $(".boss2").setItems({
                        item: json.bossList2,
                        space: true
                    });
                }
                $("#sUnitManager option:eq(1)").prop("selected", true);
                $("#sUnitManager").prop("disabled", true);
                
                $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
                    //other.msg159=覆核
                    title: i18n.lmscommom['other.msg159'],
                    width: 500,
                    height: 300,
                    modal: true,
                    valign: "bottom",
                    align: "center",
                    i18n: i18n.def,
                    readOnly: false,
                    buttons: {
                        "sure": function(){
                        
                            var selectBoss = $("select[name^=boss]").map(function(){
                                return $(this).val();
                            }).toArray();
                            // 驗證主管是否都有選擇到
                            for (var i in selectBoss) {
                                if (selectBoss[i] == "") {
                                    //other.msg160=請選擇+ other.msg161=授信主管
                                    return CommonAPI.showErrorMessage(i18n.lmscommom['other.msg160'] + i18n.lmscommom['other.msg161']);
                                }
                            }
                            // 驗證單位授權主管是否有選擇到
                            if ($("#sManager option:selected").val() == "") {
                                //other.msg160=請選擇+ other.msg162=單位/授權主管
                                return CommonAPI.showErrorMessage(i18n.lmscommom['other.msg160'] + i18n.lmscommom['other.msg162']);
                            }
                            //驗證是否有重複的主管
                            if (checkArrayRepeat(selectBoss)) {
                                //主管人員名單重複請重新選擇
                                return CommonAPI.showErrorMessage(i18n.lmscommom['other.msg164']);
                            }
                            // 驗證單位主管是否有選擇到
                            if ($("#sUnitManager option:selected").val() == "") {
                                //other.msg160=請選擇+ other.msg163=單位主管
                                return CommonAPI.showErrorMessage(i18n.lmscommom['other.msg160'] + i18n.lmscommom['other.msg163']);
                            }
                            //建立簽章欄
                            $.ajax({
                                type: "POST",
                                handler: "lms1201formhandler",
                                data: {
                                    formAction: "saveL120m01f",
                                    mainId: responseJSON.mainId,
                                    selectBoss: selectBoss,
                                    manager: $("#sManager option:selected").val(),
                                    AOPerson: $("#AOPerson option:selected").val(),
                                    sUnitManager: $("#sUnitManager option:selected").val()
                                }
							}).done(function(responseData){
                                    fn(jsonArg);
                            });
                            $.thickbox.close();
                        },
                        
                        "cancel": function(){
                            API.confirmMessage(i18n.def['flow.exit'], function(res){
                                if (res) {
                                    $.thickbox.close();
                                }
                            });
                        }
                    }
                });
        });
    },
    // 顯示往來異常戶更新確認訊息
    checkLnfe0851UpFlag: function(fn, jsonArg){
        $.ajax({
            type: "POST",
            handler: "lms1201formhandler",
            data: {
                formAction: "GetLNFE0851UpFlag",
                mainid: responseJSON.mainId
            }
		}).done(function(responseData){
                //alert(responseData.lnfe0851UpFlag);
                var lnfe0851UpFlag = responseData.lnfe0851UpFlag;
                var allCustName = responseData.allCustName;
                var msg;
                switch (lnfe0851UpFlag) {
                    case "N":
                    case "2":
                        //other.msg168=不會更新
                        //msg = i18n.lmscommom['other.msg168'];
						
						////other.msg168=「不異動是否辦理資產評估現況」
						msg = i18n.lmscommom['other.msg168'];
                        break;
                    case "Y":
                    case "1":
                        //other.msg169=會一併新增
                        //msg = i18n.lmscommom['other.msg169'];
						
						//other.msg169=將同意「需辦理資產評估」
                        msg = i18n.lmscommom['other.msg169'];
                        break;
                    case "D":
                    case "3":
                        //other.msg170=會一併刪除(結案)
                        //msg = i18n.lmscommom['other.msg170'];
						
						//other.msg170=將同意「免予辦理資產評估」
						msg = i18n.lmscommom['other.msg170'];
                        break;
                    default:
                        //other.msg171=會一併新增
                        //msg = i18n.lmscommom['other.msg171'];
						
						//other.msg171=將同意「需辦理資產評估」
                        msg = i18n.lmscommom['other.msg171'];
                };
                // 顯示往來異常戶更新(不會更新/會一併新增/會一併刪除(結案))確認訊息
                //other.msg172=此異常通報戶核准後$\{askMsg\}往來異常戶資訊，是否繼續？
                CommonAPI.confirmMessage(i18n.lmscommom('other.msg172', {
                    'askMsg': msg + "【" + allCustName + "】"
                }), function(b){
                    if (b) {
                        fn(jsonArg);
                    }
                });
        });
    },
	// J-110-0375 常董稿案由 - 授審的常董會階段
	isHeadMd: function(){
	    var unitNo = userInfo.unitNo;
		if(lmsM01Json.isHead() && responseJSON.mainDocStatus == "L1H" 
			&& responseJSON.hqMeetFlag == "3"){
			return true;
        } else if ((unitNo == "007"  || unitNo == "025" || unitNo == "201" || unitNo == "149")
            && (responseJSON.mainDocStatus == "03K" || responseJSON.mainDocStatus == "01K")
            && responseJSON.hqMeetFlag == "C"){
            return true;
		} else {
			return false;
		}
	},
	showMiniFlag: function(){    // J-110-0458 企金授權內其他
        if( (responseJSON.docType == '1' && responseJSON.docKind == '1' && responseJSON.docCode == '2' ) ){
    		// 企金授權內其他
    		return true;
    	}else{
    		return false;
    	}
	},
	showPage1: function(responseData){
	    var $LMS1205S01Form = $("#LMS1205S01Form");
    	if(lmsM01Json.showMiniFlag()){ // 企金授權內其他
            $LMS1205S01Form.find("#mini").show();
            var miniFlag = responseData.LMS1205S01Form.miniFlag;
            if(miniFlag == "Y"){
              $(".showCaseType").show();
            } else if(miniFlag == "N"){
              $(".showCaseType").hide();
            } else {
              $(".showCaseType").hide();
            }
        } else {
            $LMS1205S01Form.find("#mini").hide();
            $LMS1205S01Form.find("[name='miniFlag']").prop("disabled",true);
            $LMS1205S01Form.find("#caseType").prop("disabled",true);
        }
    },
    loadCaseTypeItem: function(docCode){
    	var comboName = "";
        var objs = null;
        if(docCode == "2"){ // 其他   國內為 lms130_2caseType
            comboName = "lms1305_2caseType";
        } else {
            comboName = "";
        }

        var objs = CommonAPI.loadCombos([comboName]);
        $("#LMS1205S01Form").find("#caseType").setItems({
            space: false,
            item: objs[comboName],
            format: "{value} - {key}"
        });
    },
    // J-110-0458 企金授權內其他 - 「簡易簽報」選項，適用方案「LIBOR退場變更利率條件簡易簽報」
    isLiborExitCase: function(){
         var isLiborExitCase = responseJSON.isLiborExitCase;
         if(isLiborExitCase == "Y" ){
             return true;
         }else{
             return false;
         }
    },
	isEuroyenTiborExitCase: function(){
        var isEuroyenTiborExitCase = responseJSON.isEuroyenTiborExitCase;
        if(isEuroyenTiborExitCase == "Y" ){
            return true;
        }else{
            return false;
        }
    },
    //J-110-0485_05097_B1001 於簽報書新增LGD欄位
    showPanelLms140s08: function(){
    	$.ajax({
            type: "POST",
            async: false ,
            handler: "lms1205formhandler",
            data: {
                formAction: "showPanelLms140s08",
                mainid: responseJSON.mainId
            }
		}).done(function(json){
            	 var isShowPanelLms140s08 = json.showPanel;
            	  if(isShowPanelLms140s08 == "Y"){
            		 $("#lms140s08").show();
                 }else{
                	 $("#lms140s08").hide();
                 }
        });
    	
    },
    // J-111-0397 RWA
    showPanelLms140s09: function(){
        $.ajax({
            handler: "lms1405s09formhandler",
            type: "POST",
            data: {
                formAction: "showPanelLms140s09",
                mainid: responseJSON.mainId
            }
		}).done(function(obj){
                if(obj.showPanelLms140s09 && obj.showPanelLms140s09 == "Y"){
                    $("#lms140s09").show();
                } else {
                    $("#lms140s09").hide();
                }
        });
    },
    // J-111-0454 調整e-Loan簽報書中「風險權數」頁籤之計算邏輯
    showPanelLms140s10: function(){
    	$.ajax({
            type: "POST",
            async: false ,
            handler: "lms1405s10formhandler",
            data: {
                formAction: "showPanelLms140s10",
                mainid: responseJSON.mainId
            }
		}).done(function(json){
            	 var isShowPanelLms140s10 = json.showPanelLms140s10;
            	 if(isShowPanelLms140s10 == "Y"){
            		 $("#lms140s10").show();
                 }else{
                	 $("#lms140s10").hide();
                 }
        });
    },
    // BIS
    showPanelLms140s11: function(){
        $.ajax({
            handler: "lms1405s11formhandler",
            type: "POST",
            data: {
            formAction: "showPanelLms140s11",
                mainid: responseJSON.mainId
            }
		}).done(function(obj){
                var showPanelLms140s11 = obj.showPanelLms140s11;
                if(showPanelLms140s11 && showPanelLms140s11 == "Y"){
                    $("#lms140s11").show();
                } else {
                    $("#lms140s11").hide();
                }
        });
    }
};

$(function(){
	//J-105-0131-001 Web e-Loan海外簽報書，呈主管時簽章欄AO欄位開放支行人員可供選擇。
	$("#btnChoiceOthBrNo").click(function(){
         choiceOthBrNo();
    });
	
    // 載入Grid
    if (responseJSON.page == "12") {
        // 會簽會議決議
        //檔案上傳grid
        $("#gridfileS12").iGrid({
            handler: 'lms1205gridhandler',
            height: 150,
            sortname: 'srcFileName',
            postData: {
                formAction: "queryfile",
                fieldId: "upFileS12",
                mainId: responseJSON.mainId
            },
            rowNum: 15,
            caption: "&nbsp;",
            hiddengrid: false,
            //expandOnLoad : true,	//只對subgrid有用
            //multiselect : true,
            colModel: [{
                colHeader: i18n.lmss10['l120m01a.srcFileName'],//原始檔案名稱,
                name: 'srcFileName',
                width: 120,
                align: "left",
                sortable: false,
                formatter: 'click',
                onclick: openDocFile
            }, {
                colHeader: i18n.lmss10['l120m01a.fileDesc'],//檔案說明
                name: 'fileDesc',
                width: 140,
                sortable: false
            }, {
                colHeader: i18n.lmss10['l120m01a.uploadTime'],//上傳時間
                name: 'uploadTime',
                width: 140,
                sortable: false
            }, {
                name: 'oid',
                hidden: true
            }]
        });
    }
    else 
        if (responseJSON.page == "13") {
            // 授管處意見
            //檔案上傳grid
            $("#gridfileS13").iGrid({
                handler: 'lms1205gridhandler',
                height: 150,
                sortname: 'srcFileName',
                postData: {
                    formAction: "queryfile",
                    fieldId: "upFileS13",
                    mainId: responseJSON.mainId
                },
                rowNum: 15,
                caption: "&nbsp;",
                hiddengrid: false,
                //expandOnLoad : true,	//只對subgrid有用
                //multiselect : true,
                colModel: [{
                    colHeader: i18n.lmss10['l120m01a.srcFileName'],//原始檔案名稱,
                    name: 'srcFileName',
                    width: 120,
                    align: "left",
                    sortable: false,
                    formatter: 'click',
                    onclick: openDocFile
                }, {
                    colHeader: i18n.lmss10['l120m01a.fileDesc'],//檔案說明
                    name: 'fileDesc',
                    width: 140,
                    sortable: false
                }, {
                    colHeader: i18n.lmss10['l120m01a.uploadTime'],//上傳時間
                    name: 'uploadTime',
                    width: 140,
                    sortable: false
                }, {
                    name: 'oid',
                    hidden: true
                }]
            });
        }
        else 
            if (responseJSON.page == "14") {
                // 國金部/營運中心會簽意見
                //檔案上傳grid
                $("#gridfileS14").iGrid({
                    handler: 'lms1205gridhandler',
                    height: 150,
                    sortname: 'srcFileName',
                    postData: {
                        formAction: "queryfile",
                        fieldId: "upFileS14",
                        mainId: responseJSON.mainId
                    },
                    rowNum: 15,
                    caption: "&nbsp;",
                    hiddengrid: false,
                    //expandOnLoad : true,	//只對subgrid有用
                    //multiselect : true,
                    colModel: [{
                        colHeader: i18n.lmss10['l120m01a.srcFileName'],//原始檔案名稱,
                        name: 'srcFileName',
                        width: 120,
                        align: "left",
                        sortable: false,
                        formatter: 'click',
                        onclick: openDocFile
                    }, {
                        colHeader: i18n.lmss10['l120m01a.fileDesc'],//檔案說明
                        name: 'fileDesc',
                        width: 140,
                        sortable: false
                    }, {
                        colHeader: i18n.lmss10['l120m01a.uploadTime'],//上傳時間
                        name: 'uploadTime',
                        width: 140,
                        sortable: false
                    }, {
                        name: 'oid',
                        hidden: true
                    }]
                });
            }
            else 
                if (responseJSON.page == "15") {
                    // 營運中心意見
                    //檔案上傳grid
                    $("#gridfileS15").iGrid({
                        handler: 'lms1205gridhandler',
                        height: 150,
                        sortname: 'srcFileName',
                        postData: {
                            formAction: "queryfile",
                            fieldId: "upFileS15",
                            mainId: responseJSON.mainId
                        },
                        rowNum: 15,
                        caption: "&nbsp;",
                        hiddengrid: false,
                        //expandOnLoad : true,	//只對subgrid有用
                        //multiselect : true,
                        colModel: [{
                            colHeader: i18n.lmss10['l120m01a.srcFileName'],//原始檔案名稱,
                            name: 'srcFileName',
                            width: 120,
                            align: "left",
                            sortable: false,
                            formatter: 'click',
                            onclick: openDocFile
                        }, {
                            colHeader: i18n.lmss10['l120m01a.fileDesc'],//檔案說明
                            name: 'fileDesc',
                            width: 140,
                            sortable: false
                        }, {
                            colHeader: i18n.lmss10['l120m01a.uploadTime'],//上傳時間
                            name: 'uploadTime',
                            width: 140,
                            sortable: false
                        }, {
                            name: 'oid',
                            hidden: true
                        }]
                    });
                }
    
    
    //J-110-0493_11557_B1001  檢查利害關係人授信額度合計是否達新台幣1億元以上
    $("#lmsm06_panel_for_thickbox").find('#rtlOver100MillionForm').find('input:checkbox[name="excludeRltLimRsn"][value="4"]').change(function(){
    	var rtlOver100MillionForm = $(this).parents('#rtlOver100MillionForm');
    	var checked = $(this).prop('checked');
    	var excludeRltLimOther = rtlOver100MillionForm.find('#excludeRltLimOther')
    	if(checked){
    		excludeRltLimOther.prop("disabled",false);
    		excludeRltLimOther.addClass("required");
    	}else{
    		excludeRltLimOther.val('');
    		excludeRltLimOther.prop("disabled",true);
    		excludeRltLimOther.removeClass("required");
    		excludeRltLimOther.removeClass("data-error");
    	}
    });
});

/**
 *複製Form來源資料到另一Form上
 *Author : Miller Lin
 *formOrigin : 來源Form Id
 *formTo : 目的Form Id
 *clazz : 來源Form欲複製的內容class
 *arg1 : 前綴字(目的Form Id)
 *arg2 : 後綴字(目的Form Id)
 *chanbgeSpace : 將來源資料數值的某個符號取代成空白
 */
function copyDataByForm(formOrigin, formTo, clazz, arg1, arg2, changeSpace){
    var keys = getColKeys(formOrigin, clazz);
    setValByKeys(formOrigin, formTo, keys, arg1, arg2, changeSpace);
}

// 傳入form,class參數取得符合條件資料所有Id
function getColKeys(form, clazz){
    var keys = [];
    $("#" + form).find("." + clazz).each(function(i){
        var $this = $(this);
        keys.push($this.attr("id"));
    });
    return keys;
}

// 傳入form,keys參數找出符合條件資料設值到另一有綴字Key欄位上
function setValByKeys(formOrigin, formTo, keys, arg1, arg2, changeSpace){
    for (key in keys) {
        var $key = $("#" + formOrigin).find("#" + keys[key]);
        var value = ($key.is("span")) ? $key.html() : $key.val();
        value = value.replace(changeSpace, "");
        $("#" + formTo).find("#" + arg1 + keys[key] + arg2).val((value == null || value == undefined) ? "" : value);
    }
}

// 下載檔案
function openDocFile(cellvalue, options, rowObject){
    $.capFileDownload({
        handler: "simplefiledwnhandler",
        data: {
            fileOid: rowObject.oid
        }
    });
}

var tempKey = {};
//不加空白 responseJSON.docURL 會是object
switch (responseJSON.docURL + "") {
    case "/lms/lms1205m01":
        tempKey = i18n.lms1205m01;
        break;
    case "/lms/lms1105m01":
        tempKey = i18n.lms1105m01;
        break;
    case "/lms/lms1215m01":
        tempKey = i18n.lms1215m01;
        break;
    case "/lms/lms1115m01":
        tempKey = i18n.lms1115m01;
        break;
    case "/lms/lms1305m01":
        tempKey = i18n.lms1305m01;
        break;
}

var _handler = {};
//不加空白 responseJSON.docURL 會是object
switch (responseJSON.docURL + "") {
    case "/lms/lms1205m01":
        _handler = "lms1205formhandler";
        break;
    case "/lms/lms1105m01":
        _handler = "lms1105formhandler";
        break;
    case "/lms/lms1215m01":
        _handler = "lms1215formhandler";
        break;
    case "/lms/lms1115m01":
        _handler = "lms1115formhandler";
        break;
    case "/lms/lms1305m01":
        _handler = "lms1305formhandler";
        break;
}

function upLoadFile(key){
    //上傳檔案按鈕
    var limitFileSize = 3145728;
    MegaApi.uploadDialog({
        fieldId: "upFile" + key,
        fieldIdHtml: "size='30'",
        fileDescId: "fileDesc",
        fileDescHtml: "size='30' maxlength='30'",
        subTitle: i18n.def('insertfileSize', {
            'fileSize': (limitFileSize / 1048576).toFixed(2)
        }),
        limitSize: limitFileSize,
        width: 320,
        height: 190,
        data: {
            mainId: responseJSON.mainId
        },
        success: function(obj){
            $("#gridfile" + key).trigger("reloadGrid");
        }
    });
}

function deleteFile(key){
    //刪除檔案按鈕
    var select = $("#gridfile" + key).getGridParam('selrow');
    // confirmDelete=是否確定刪除?
    CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
        if (b) {
            var data = $("#gridfile" + key).getRowData(select);
            if (data.oid == "" || data.oid == undefined || data.oid == null) {
                // TMMDeleteError=請先選擇需修改(刪除)之資料列
                CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
                return;
            }
            $.ajax({
                handler: _handler,
                type: "POST",
                dataType: "json",
                data: {
                    formAction: "deleteUploadFile",
                    fileOid: data.oid
                }
			}).done(function(obj){
                    $("#gridfile" + key).trigger("reloadGrid");
            });
        }
        else {
            return;
        }
    });
}

// 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法
/**
 * 設值ckeditor(新增內容)
 * name : ckeditor name
 * val : 需要設定的值
 */
function setCkeditor(name, val){
    var oEditor = CKEDITOR.instances[name];
    if (oEditor) {
        oEditor.insertHtml(val);
    }
}

// 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法
/**
 * 設值ckeditor(更改內容)
 * name : ckeditor name
 * val : 需要設定的值
 */
function setCkeditor2(name, val){
    var oEditor = CKEDITOR.instances[name];
    if (oEditor) {
        oEditor.setData(val);
    }
}

// 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法
/**
 * 取值ckeditor
 * name : ckeditor name
 * return : ckeditor內容的值
 */
function getCkeditor(name){
    var oEditor = CKEDITOR.instances[name];
    if (oEditor) {
        return oEditor.getData();
    }
    return '';
}

// 注意：如果CKeditor在Thickbox裡一定要先打開ThickBox再執行此方法
/**
 * 唯讀/非唯讀ckeditor
 * name : ckeditor name
 * isReadOnly : true->唯讀, false->非唯讀
 */
function readOnlyCkeditor(name, isReadOnly){
    var oEditor = CKEDITOR.instances[name];
    if (oEditor) {
        oEditor.setReadOnly(isReadOnly);
    }
}

//全形轉半形
function FtoH(text){
    result = "";
    for (i = 0; i <= text.length; i++) {
        if (text.charCodeAt(i) == 12288) {
            result += " ";
        }
        else {
            if (text.charCodeAt(i) > 65280 && text.charCodeAt(i) < 65375) {
                result += String.fromCharCode(text.charCodeAt(i) - 65248);
            }
            else {
                result += String.fromCharCode(text.charCodeAt(i));
            }
        }
    }
    return result;
}

// 授權外企金唯讀設定(不含分頁)
function setReadOnly1(){
    //$('.tabCtx-warp').readOnlyChilds(true).find("button").not(".forview").hide();	
    if (responseJSON.page == "01") {
        // 文件資訊
        var $LMS1205S01Form = $("#LMS1205S01Form");
        $LMS1205S01Form.readOnlyChilds(true);
        $LMS1205S01Form.find("button").hide();
        thickboxOptions.readOnly = true;
    }
    else 
        if (responseJSON.page == "02") {
            // 借款人
            $("#bowbtn").find("button").hide();
            thickboxOptions.readOnly = true;
        }
        else 
            if (responseJSON.page == "03") {
                // 額度明細表
                var $L120M01BForm = $("#L120M01BForm");
                $L120M01BForm.readOnlyChilds(true);
                $L120M01BForm.find("button").hide();
                $("#lms1405s02PanelBt").find("button").hide();
                thickboxOptions.readOnly = true;
            }
            else 
                if (responseJSON.page == "04") {
                    // 案由
                    $("#LMS1205S04Form").readOnlyChilds(true);
                    $("#LMS1205S04Form").find("button").hide();
                    thickboxOptions.readOnly = true;
                }
                else 
                    if (responseJSON.page == "06") {
                        // 其他
                        var $L120M01dForm03 = $("#L120M01dForm03");
                        $L120M01dForm03.readOnlyChilds(true);
                        $L120M01dForm03.find("button").hide();
                        thickboxOptions.readOnly = true;
                    }
                    else 
                        if (responseJSON.page == "08") {
                            // 相關文件
                            var docDscr1 = $("#formL120m01e").find("#docDscr1").html();
                            var docDscr2 = $("#formL120m01e").find("#docDscr2").html();
                            var docDscr3 = $("#formL120m01e").find("#docDscr3").html();
                            var docDscr4 = $("#formL120m01e").find("#docDscr4").html();
                            var $formL120m01e = $("#formL120m01e");
                            $formL120m01e.readOnlyChilds(true);
                            $formL120m01e.find("button").hide();
                            $formL120m01e.find("#bDocDscrCR").show();
                            if (docDscr3 != null && docDscr3 != undefined && docDscr3 != "") {
                                $formL120m01e.find("#tbDocDscr3").html(i18n.def['look']);
                                $formL120m01e.find("#bDocDscr3").show();
                            }
                            if (docDscr4 != null && docDscr4 != undefined && docDscr4 != "") {
                                $formL120m01e.find("#tbDocDscr4").html(i18n.def['look']);
                                $formL120m01e.find("#bDocDscr4").show();
                            }
                            thickboxOptions.readOnly = true;
                         // J-112-0357_011850 常董會附件按鈕918要看得到
                            if (responseJSON.mainDocStatus == "L1H" &&
                                    userInfo.unitNo == "918") {
                            	$formL120m01e.find(".lmss08jk_button button").show();
                            }
                        }
                        else 
                            if (responseJSON.page == "09") {
                                // 補充說明
                                var $L120M01dForm05 = $("#L120M01dForm05");
                                
                                $L120M01dForm05.readOnlyChilds(true);
                                $L120M01dForm05.find("button").hide();
                            }
                            else 
                                if (responseJSON.page == "10") {
                                    // 附加檔案
                                    var $formfile = $("#formfile");
                                    
                                    $formfile.readOnlyChilds(true);
                                    $formfile.find("button").hide();
                                    $formfile.find("#uploadFile2").show();
                                }
                                else 
                                    if (responseJSON.page == "11") {
                                    // 額度批覆表
                                    //2012_05_15_ Rex__由批覆表頁面控制
                                    //$("#lms1405s02PanelBt").find("button").hide();
                                    //thickboxOptions.readOnly = true;
                                    }
                                    else 
                                        if (responseJSON.page == "12") {
                                            // 會簽會議決議
                                            var $L120M01aForm12 = $("#L120M01aForm12");
                                            $L120M01aForm12.readOnlyChilds(true);
                                            $L120M01aForm12.find("button").hide();
                                            // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                            // Miller added at 2012/09/07
                                            if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                            (responseJSON.mainDocStatus == "L1C" &&
                                            (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                            userInfo.unitNo == "932" ||
                                            userInfo.unitNo == "933" ||
                                            userInfo.unitNo == "934" ||
                                            userInfo.unitNo == "935"))) {
                                                //$L120M01aForm12.find("#filehideS12").show();
                                                $L120M01aForm12.find("#filehideS12 button").show();
                                            }
                                            else {
                                            //$L120M01aForm12.find("#filehideS12").hide();
                                            }
                                        }
                                        else 
                                            if (responseJSON.page == "13") {
                                                // 授管處意見
                                                var $L120M01aForm13 = $("#L120M01aForm13");
                                                $L120M01aForm13.readOnlyChilds(true);
                                                $L120M01aForm13.find("button").hide();
                                                // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                // Miller added at 2012/09/07
                                                if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                (responseJSON.mainDocStatus == "L1H" ||
                                                responseJSON.mainDocStatus == "L2H" ||
                                                responseJSON.mainDocStatus == "L3H" ||
                                                responseJSON.mainDocStatus == "L4H" ||
                                                responseJSON.mainDocStatus == "L5H") &&
                                                userInfo.unitNo == "918") {
                                                    //$L120M01aForm13.find("#filehideS13").show();
                                                    $L120M01aForm13.find("#filehideS13 button").show();
                                                }
                                                else {
                                                //$L120M01aForm13.find("#filehideS13").hide();
                                                }
                                            }
                                            else 
                                                if (responseJSON.page == "14") {
                                                    // 國金部/營運中心會簽意見
                                                    var $L120M01aForm14 = $("#L120M01aForm14");
                                                    $L120M01aForm14.readOnlyChilds(true);
                                                    $L120M01aForm14.find("button").hide();
                                                    // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                    // Miller added at 2012/09/07
                                                    if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                    (responseJSON.areaDocstatus == "LWC" &&
                                                    (userInfo.unitNo == "025" || userInfo.unitNo == "920" || userInfo.unitNo == "931" ||
                                                    userInfo.unitNo == "922" ||
                                                    userInfo.unitNo == "932" ||
                                                    userInfo.unitNo == "933" ||
                                                    userInfo.unitNo == "934" ||
                                                    userInfo.unitNo == "935" ||
                                                    userInfo.unitNo == "940" ||
                                                    userInfo.unitNo == "943"))) {
                                                        //$L120M01aForm14.find("#filehideS14").show();
                                                        $L120M01aForm14.find("#filehideS14 button").show();
                                                    }
                                                    else {
                                                    //$L120M01aForm14.find("#filehideS14").hide();
                                                    }
                                                }
                                                else 
                                                    if (responseJSON.page == "15") {
                                                        // 營運中心意見
                                                        var $L120M01aForm15 = $("#L120M01aForm15");
                                                        $L120M01aForm15.readOnlyChilds(true);
                                                        $L120M01aForm15.find("button").hide();
                                                        // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                        // Miller added at 2012/09/07
                                                        if ($("#buttonPanel").find("#btnLogeIN").is(":visible") && responseJSON.mainDocStatus == "L1C" &&
                                                        (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                                        userInfo.unitNo == "932" ||
                                                        userInfo.unitNo == "933" ||
                                                        userInfo.unitNo == "934" ||
                                                        userInfo.unitNo == "935")) {
                                                            //$L120M01aForm15.find("#filehideS15").show();
                                                            $L120M01aForm15.find("#filehideS15 button").show();
                                                        }
                                                        else {
                                                        //$L120M01aForm15.find("#filehideS15").hide();
                                                        }
                                                    }
                                                    else 
                                                        if (responseJSON.page == "16") {
                                                            // 額度批覆表/批覆書
                                                            var $LMS1205S16Panel = $("#LMS1205S16Panel");
                                                            $LMS1205S16Panel.readOnlyChilds(true);
                                                            $LMS1205S16Panel.find("button").hide();
                                                            thickboxOptions.readOnly = true;
                                                        }
                                                        else 
                                                            if (responseJSON.page == "17") {
                                                                // 常董稿附加檔案
                                                                var $L120M01aForm17 = $("#L120M01aForm17");
                                                                $L120M01aForm17.readOnlyChilds(true);
                                                                $L120M01aForm17.find("button").hide();
                                                            }
														else 
                                                            if (responseJSON.page == "19") {
                                                                // 異常通報
                                                                var $unNormalForm = $("#unNormalForm");
                                                                $unNormalForm.readOnlyChilds(true);
                                                                $unNormalForm.find("button").hide();
                                                                thickboxOptions.readOnly = true;
                                                            }	else 
		                                                            if (responseJSON.page == "20") {
																		 
		                                                                // J-106-0029-002  洗錢防制-新增洗錢防制頁籤  
		                                                                var $LMS1205S20Form01 = $("#LMS1205S20Form01");
		                                                                $LMS1205S20Form01.readOnlyChilds(true);
		                                                                $LMS1205S20Form01.find("button").hide();
		                                                                thickboxOptions.readOnly = true;
																		
																		var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
		                                                                $tLMS1205S20Form01.readOnlyChilds(true);
		                                                                $tLMS1205S20Form01.find("button").hide();
		                                                                $tLMS1205S20Form01.readOnly = true;
																		
		                                                            }	
															
}

// 授權內企金、授權內(外)個金唯讀設定(不含分頁)
function setReadOnly2(){
    if (responseJSON.page == "01") {
        // 文件資訊
        var $LMS1205S01Form = $("#LMS1205S01Form");
        $LMS1205S01Form.readOnlyChilds(true);
        $LMS1205S01Form.find("button").hide();
        thickboxOptions.readOnly = true;
    }
    else 
        if (responseJSON.page == "02") {
            // 借款人
            $("#bowbtn").find("button").hide();
            thickboxOptions.readOnly = true;
        }
        else 
            if (responseJSON.page == "03") {
                // 額度明細表
                var $L120M01BForm = $("#L120M01BForm");
                $L120M01BForm.readOnlyChilds(true);
                $L120M01BForm.find("button").hide();
                $("#lms1405s02PanelBt").find("button").hide();
                thickboxOptions.readOnly = true;
            }
            else 
                if (responseJSON.page == "04") {
                    // 案由
                    var $LMS1205S04Form = $("#LMS1205S04Form");
                    $LMS1205S04Form.readOnlyChilds(true);
                    $LMS1205S04Form.find("button").hide();
                    thickboxOptions.readOnly = true;
                }
                else 
                    if (responseJSON.page == "05") {
                        // 說明
                        $("#CLS1205S05Form").readOnlyChilds(true);
                    }
                    else 
                        if (responseJSON.page == "06") {
                            // 其他
                            var $L120M01dForm03 = $("#L120M01dForm03");
                            $L120M01dForm03.readOnlyChilds(true);
                            $L120M01dForm03.find("button").hide();
                            thickboxOptions.readOnly = true;
                        }
                        else 
                            if (responseJSON.page == "08") {
                                // 相關文件
                                var docDscr1 = $("#formL120m01e").find("#docDscr1").html();
                                var docDscr2 = $("#formL120m01e").find("#docDscr2").html();
                                var docDscr3 = $("#formL120m01e").find("#docDscr3").html();
                                var docDscr4 = $("#formL120m01e").find("#docDscr4").html();
                                var $formL120m01e = $("#formL120m01e");
                                $formL120m01e.readOnlyChilds(true);
                                $formL120m01e.find("button").hide();
                                $formL120m01e.find("#bDocDscrCR").show();
                                if (docDscr3 != null && docDscr3 != undefined && docDscr3 != "") {
                                    $formL120m01e.find("#tbDocDscr3").html(i18n.def['look']);
                                    $formL120m01e.find("#bDocDscr3").show();
                                }
                                if (docDscr4 != null && docDscr4 != undefined && docDscr4 != "") {
                                    $formL120m01e.find("#tbDocDscr4").html(i18n.def['look']);
                                    $formL120m01e.find("#bDocDscr4").show();
                                }
                                thickboxOptions.readOnly = true;
                            }
                            else 
                                if (responseJSON.page == "09") {
                                    // 補充說明		
                                    var $L120M01dForm05 = $("#L120M01dForm05");
                                    $L120M01dForm05.readOnlyChilds(true);
                                    $L120M01dForm05.find("button").hide();
                                }
                                else 
                                    if (responseJSON.page == "10") {
                                        // 附加檔案
                                        var $formfile = $("#formfile");
                                        $formfile.readOnlyChilds(true);
                                        $formfile.find("button").hide();
                                        $formfile.find("#uploadFile2").show();
                                    }
                                    else 
                                        if (responseJSON.page == "11") {
                                        // 額度批覆表
                                        //2012_05_15_ Rex__由批覆表頁面控制
                                        //$("#lms1405s02PanelBt").find("button").hide();
                                        //thickboxOptions.readOnly = true;
                                        }
                                        else 
                                            if (responseJSON.page == "12") {
                                                // 會簽會議決議
                                                var $L120M01aForm12 = $("#L120M01aForm12");
                                                $L120M01aForm12.readOnlyChilds(true);
                                                $L120M01aForm12.find("button").hide();
                                                // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                // Miller added at 2012/09/07
                                                if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                (responseJSON.mainDocStatus == "L1C" &&
                                                (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                                userInfo.unitNo == "932" ||
                                                userInfo.unitNo == "933" ||
                                                userInfo.unitNo == "934" ||
                                                userInfo.unitNo == "935"))) {
                                                    //$L120M01aForm12.find("#filehideS12").show();
                                                    $L120M01aForm12.find("#filehideS12 button").show();
                                                }
                                                else {
                                                //$L120M01aForm12.find("#filehideS12").hide();
                                                }
                                            }
                                            else 
                                                if (responseJSON.page == "13") {
                                                    // 授管處意見
                                                    var $L120M01aForm13 = $("#L120M01aForm13");
                                                    $L120M01aForm13.readOnlyChilds(true);
                                                    $L120M01aForm13.find("button").hide();
                                                    // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                    // Miller added at 2012/09/07
                                                    if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                    (responseJSON.mainDocStatus == "L1H" ||
                                                    responseJSON.mainDocStatus == "L2H" ||
                                                    responseJSON.mainDocStatus == "L3H" ||
                                                    responseJSON.mainDocStatus == "L4H" ||
                                                    responseJSON.mainDocStatus == "L5H") &&
                                                    userInfo.unitNo == "918") {
                                                        //$L120M01aForm13.find("#filehideS13").show();
                                                        $L120M01aForm13.find("#filehideS13 button").show();
                                                    }
                                                    else {
                                                    //$L120M01aForm13.find("#filehideS13").hide();
                                                    }
                                                }
                                                else 
                                                    if (responseJSON.page == "14") {
                                                        // 國金部/營運中心會簽意見
                                                        var $L120M01aForm14 = $("#L120M01aForm14");
                                                        $L120M01aForm14.readOnlyChilds(true);
                                                        $L120M01aForm14.find("button").hide();
                                                        // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                        // Miller added at 2012/09/07
                                                        if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                        (responseJSON.areaDocstatus == "LWC" &&
                                                        (userInfo.unitNo == "025" || userInfo.unitNo == "920" || userInfo.unitNo == "931" ||
                                                        userInfo.unitNo == "922" ||
                                                        userInfo.unitNo == "932" ||
                                                        userInfo.unitNo == "933" ||
                                                        userInfo.unitNo == "934" ||
                                                        userInfo.unitNo == "935" ||
                                                        userInfo.unitNo == "940" ||
                                                        userInfo.unitNo == "943"))) {
                                                            //$L120M01aForm14.find("#filehideS14").show();
                                                            $L120M01aForm14.find("#filehideS14 button").show();
                                                        }
                                                        else {
                                                        //$L120M01aForm14.find("#filehideS14").hide();
                                                        }
                                                    }
                                                    else 
                                                        if (responseJSON.page == "15") {
                                                            // 營運中心意見
                                                            var $L120M01aForm15 = $("#L120M01aForm15");
                                                            $L120M01aForm15.readOnlyChilds(true);
                                                            $L120M01aForm15.find("button").hide();
                                                            // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                            // Miller added at 2012/09/07
                                                            if ($("#buttonPanel").find("#btnLogeIN").is(":visible") && responseJSON.mainDocStatus == "L1C" &&
                                                            (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                                            userInfo.unitNo == "932" ||
                                                            userInfo.unitNo == "933" ||
                                                            userInfo.unitNo == "934" ||
                                                            userInfo.unitNo == "935")) {
                                                                //$L120M01aForm15.find("#filehideS15").show();
                                                                $L120M01aForm15.find("#filehideS15 button").show();
                                                            }
                                                            else {
                                                            //$L120M01aForm15.find("#filehideS15").hide();
                                                            }
                                                        }
                                                        else 
                                                            if (responseJSON.page == "16") {
                                                                // 額度批覆表/批覆書
                                                                var $LMS1205S16Panel = $("#LMS1205S16Panel");
                                                                $LMS1205S16Panel.readOnlyChilds(true);
                                                                $LMS1205S16Panel.find("button").hide();
                                                                thickboxOptions.readOnly = true;
                                                            }
                                                            else 
                                                                if (responseJSON.page == "17") {
                                                                    // 常董稿附加檔案
                                                                    var $L120M01aForm17 = $("#L120M01aForm17");
                                                                    $L120M01aForm17.readOnlyChilds(true);
                                                                    $L120M01aForm17.find("button").hide();
                                                                }
                                                                else 
                                                                    if (responseJSON.page == "18") {
                                                                        // 主管批示
                                                                        var $L120M01dForm06 = $("#L120M01dForm06");
                                                                        $L120M01dForm06.readOnlyChilds(true);
                                                                        $L120M01dForm06.find("button").hide();
                                                                    }
																	else 
	                                                                    if (responseJSON.page == "19") {
	                                                                        // 異常通報
	                                                                        var $unNormalForm = $("#unNormalForm");
	                                                                        $unNormalForm.readOnlyChilds(true);
	                                                                        $unNormalForm.find("button").hide();
	                                                                        thickboxOptions.readOnly = true;
	                                                                    }else 
		                                                                    if (responseJSON.page == "19") {
		                                                                        // 異常通報
		                                                                        var $unNormalForm = $("#unNormalForm");
		                                                                        $unNormalForm.readOnlyChilds(true);
		                                                                        $unNormalForm.find("button").hide();
		                                                                        thickboxOptions.readOnly = true;
		                                                                    }else 
					                                                            if (responseJSON.page == "20") {
																					 
					                                                                // J-106-0029-002  洗錢防制-新增洗錢防制頁籤  
					                                                                var $LMS1205S20Form01 = $("#LMS1205S20Form01");
					                                                                $LMS1205S20Form01.readOnlyChilds(true);
					                                                                $LMS1205S20Form01.find("button").hide();
					                                                                thickboxOptions.readOnly = true;
																					
																					var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
					                                                                $tLMS1205S20Form01.readOnlyChilds(true);
					                                                                $tLMS1205S20Form01.find("button").hide();
					                                                                $tLMS1205S20Form01.readOnly = true;
					                                                            }	
}

// 陳復(述)案唯讀設定(不含分頁)
function setReadOnly3(){
    if (responseJSON.page == "01") {
        // 文件資訊
        var $LMS1205S01Form = $("#LMS1205S01Form");
        $LMS1205S01Form.readOnlyChilds(true);
        $LMS1205S01Form.find("button").hide();
        thickboxOptions.readOnly = true;
    }
    else 
        if (responseJSON.page == "02") {
            // 借款人
            $("#bowbtn").find("button").hide();
            thickboxOptions.readOnly = true;
        }
        else 
            if (responseJSON.page == "03") {
                // 額度明細表
                $("#L120M01BForm").readOnlyChilds(true);
                $("#L120M01BForm").find("button").hide();
                $("#lms1405s02PanelBt").find("button").hide();
                thickboxOptions.readOnly = true;
            }
            else 
                if (responseJSON.page == "04") {
                    // 案由
                    var $LMS1205S04Form = $("#LMS1205S04Form");
                    $LMS1205S04Form.readOnlyChilds(true);
                    $LMS1205S04Form.find("button").hide();
                    thickboxOptions.readOnly = true;
                }
                else 
                    if (responseJSON.page == "05") {
                        // 說明
                        var $L120M01dForm03 = $("#L120M01dForm03");
                        var $L130S05Form01 = $("#L130S05Form01");
                        $L120M01dForm03.readOnlyChilds(true);
                        $L120M01dForm03.find("button").hide();
                        $L130S05Form01.readOnlyChilds(true);
                        $L130S05Form01.find("button").hide();
                        thickboxOptions.readOnly = true;
                    }
                    else 
                        if (responseJSON.page == "08") {
                            // 相關文件
                            var docDscr1 = $("#formL120m01e").find("#docDscr1").html();
                            var docDscr2 = $("#formL120m01e").find("#docDscr2").html();
                            var docDscr3 = $("#formL120m01e").find("#docDscr3").html();
                            var docDscr4 = $("#formL120m01e").find("#docDscr4").html();
                            var $formL120m01e = $("#formL120m01e");
                            $formL120m01e.readOnlyChilds(true);
                            $formL120m01e.find("button").hide();
                            $formL120m01e.find("#bDocDscrCR").show();
                            if (docDscr3 != null && docDscr3 != undefined && docDscr3 != "") {
                                $formL120m01e.find("#tbDocDscr3").html(i18n.def['look']);
                                $formL120m01e.find("#bDocDscr3").show();
                            }
                            if (docDscr4 != null && docDscr4 != undefined && docDscr4 != "") {
                                $formL120m01e.find("#tbDocDscr4").html(i18n.def['look']);
                                $formL120m01e.find("#bDocDscr4").show();
                            }
                            thickboxOptions.readOnly = true;
                        }
                        else 
                            if (responseJSON.page == "09") {
                                // 補充說明
                                var $L120M01dForm05 = $("#L120M01dForm05");
                                $L120M01dForm05.readOnlyChilds(true);
                                $L120M01dForm05.find("button").hide();
                            }
                            else 
                                if (responseJSON.page == "10") {
                                    // 附加檔案
                                    var $formfile = $("#formfile");
                                    $formfile.readOnlyChilds(true);
                                    $formfile.find("button").hide();
                                    $formfile.find("#uploadFile2").show();
                                }
                                else 
                                    if (responseJSON.page == "11") {
                                    // 額度批覆表
                                    //2012_05_15_ Rex__由批覆表頁面控制
                                    //$("#lms1405s02PanelBt").find("button").hide();
                                    //thickboxOptions.readOnly = true;
                                    }
                                    else 
                                        if (responseJSON.page == "12") {
                                            // 會簽會議決議
                                            var $L120M01aForm12 = $("#L120M01aForm12");
                                            $L120M01aForm12.readOnlyChilds(true);
                                            $L120M01aForm12.find("button").hide();
                                            // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                            // Miller added at 2012/09/07
                                            if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                            (responseJSON.mainDocStatus == "L1C" &&
                                            (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                            userInfo.unitNo == "932" ||
                                            userInfo.unitNo == "933" ||
                                            userInfo.unitNo == "934" ||
                                            userInfo.unitNo == "935"))) {
                                                //$L120M01aForm12.find("#filehideS12").show();
                                                $L120M01aForm12.find("#filehideS12 button").show();
                                            }
                                            else {
                                            //$L120M01aForm12.find("#filehideS12").hide();
                                            }
                                        }
                                        else 
                                            if (responseJSON.page == "13") {
                                                // 授管處意見
                                                var $L120M01aForm13 = $("#L120M01aForm13");
                                                $L120M01aForm13.readOnlyChilds(true);
                                                $L120M01aForm13.find("button").hide();
                                                // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                // Miller added at 2012/09/07
                                                if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                (responseJSON.mainDocStatus == "L1H" ||
                                                responseJSON.mainDocStatus == "L2H" ||
                                                responseJSON.mainDocStatus == "L3H" ||
                                                responseJSON.mainDocStatus == "L4H" ||
                                                responseJSON.mainDocStatus == "L5H") &&
                                                userInfo.unitNo == "918") {
                                                    //$L120M01aForm13.find("#filehideS13").show();
                                                    $L120M01aForm13.find("#filehideS13 button").show();
                                                }
                                                else {
                                                //$L120M01aForm13.find("#filehideS13").hide();
                                                }
                                            }
                                            else 
                                                if (responseJSON.page == "14") {
                                                    // 國金部/營運中心會簽意見
                                                    var $L120M01aForm14 = $("#L120M01aForm14");
                                                    $L120M01aForm14.readOnlyChilds(true);
                                                    $L120M01aForm14.find("button").hide();
                                                    // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                    // Miller added at 2012/09/07
                                                    if ($("#buttonPanel").find("#btnLogeIN").is(":visible") &&
                                                    (responseJSON.areaDocstatus == "LWC" &&
                                                    (userInfo.unitNo == "025" || userInfo.unitNo == "920" || userInfo.unitNo == "931" ||
                                                    userInfo.unitNo == "922" ||
                                                    userInfo.unitNo == "932" ||
                                                    userInfo.unitNo == "933" ||
                                                    userInfo.unitNo == "934" ||
                                                    userInfo.unitNo == "935" ||
                                                    userInfo.unitNo == "940" ||
                                                    userInfo.unitNo == "943"))) {
                                                        //$L120M01aForm14.find("#filehideS14").show();
                                                        $L120M01aForm14.find("#filehideS14 button").show();
                                                    }
                                                    else {
                                                    //$L120M01aForm14.find("#filehideS14").hide();
                                                    }
                                                }
                                                else 
                                                    if (responseJSON.page == "15") {
                                                        // 營運中心意見
                                                        var $L120M01aForm15 = $("#L120M01aForm15");
                                                        $L120M01aForm15.readOnlyChilds(true);
                                                        $L120M01aForm15.find("button").hide();
                                                        // 若'登錄'按鈕有顯示，則附加檔案區塊也顯示
                                                        // Miller added at 2012/09/07
                                                        if ($("#buttonPanel").find("#btnLogeIN").is(":visible") && responseJSON.mainDocStatus == "L1C" &&
                                                        (userInfo.unitNo == "920" || userInfo.unitNo == "931" || userInfo.unitNo == "922" ||
                                                        userInfo.unitNo == "932" ||
                                                        userInfo.unitNo == "933" ||
                                                        userInfo.unitNo == "934" ||
                                                        userInfo.unitNo == "935")) {
                                                            //$L120M01aForm15.find("#filehideS15").show();
                                                            $L120M01aForm15.find("#filehideS15 button").show();
                                                        }
                                                        else {
                                                        //$L120M01aForm15.find("#filehideS15").hide();
                                                        }
                                                    }
                                                    else 
                                                        if (responseJSON.page == "16") {
                                                            // 額度批覆表/批覆書
                                                            var $LMS1205S16Panel = $("#LMS1205S16Panel");
                                                            $LMS1205S16Panel.readOnlyChilds(true);
                                                            $LMS1205S16Panel.find("button").hide();
                                                            thickboxOptions.readOnly = true;
                                                        }
                                                        else 
                                                            if (responseJSON.page == "17") {
                                                                // 常董稿附加檔案
                                                                var $L120M01aForm17 = $("#L120M01aForm17");
                                                                $L120M01aForm17.readOnlyChilds(true);
                                                                $L120M01aForm17.find("button").hide();
                                                            }
                                                            else 
                                                                if (responseJSON.page == "18") {
                                                                    // 主管批示
                                                                    var $L120M01dForm06 = $("#L120M01dForm06");
                                                                    $L120M01dForm06.readOnlyChilds(true);
                                                                    $L120M01dForm06.find("button").hide();
                                                                }
																else 
                                                                    if (responseJSON.page == "19") {
                                                                        // 異常通報
																		var $unNormalForm = $("#unNormalForm");
                                                                        $unNormalForm.readOnlyChilds(true);
                                                                        $unNormalForm.find("button").hide();
                                                                        thickboxOptions.readOnly = true;
                                                                    }else 
			                                                            if (responseJSON.page == "20") {
																			 
			                                                                // J-106-0029-002  洗錢防制-新增洗錢防制頁籤  
			                                                                var $LMS1205S20Form01 = $("#LMS1205S20Form01");
			                                                                $LMS1205S20Form01.readOnlyChilds(true);
			                                                                $LMS1205S20Form01.find("button").hide();
			                                                                thickboxOptions.readOnly = true;
																			var $tLMS1205S20Form01 = $("#tLMS1205S20Form01");
			                                                                $tLMS1205S20Form01.readOnlyChilds(true);
			                                                                $tLMS1205S20Form01.find("button").hide();
			                                                                $tLMS1205S20Form01.readOnly = true;
			                                                            }	
}

// 將數字轉金額
function money_format(value, fixed, currency){
    if (value != null && value != undefined && value != "") {
        var fixed = fixed || 0;
        var currency = currency || '';
        isNaN(parseFloat(value)) ? value = 0 : value = parseFloat(value);
        v = value.toFixed(fixed).toString();
        var ps = v.split('.');
        var whole = ps[0];
        var sub = ps[1] ? '.' + ps[1] : '';
        var r = /(\d+)(\d{3})/;
        while (r.test(whole)) {
            whole = whole.replace(r, '$1' + ',' + '$2');
        }
        v = whole + sub;
        if (v.charAt(0) == '-') {
            return currency + '-' + v.substr(1);
        }
        return currency + v;
    }
    else {
        return '';
    }
}


function fixIECKEditorCache4ModelPopup(formId, id, readonly){
    if ($.browser.msie) {
        try {
            try {
                if (CKEDITOR.instances[id]) {
                    CKEDITOR.instances[id].destroy();
                }
                CKEDITOR.replace(id);
            } 
            catch (e) {
                ilog.debug(e);
            }
            $("textarea[name='" + id + "']").val(CKEDITOR.instances[id].getData());
        } 
        catch (e) {
            ilog.debug(e);
        }
        finally {
            $("#" + formId).find("textarea[name='" + id + "']").readOnly(readonly);
        }
    }
}


/**
 * 移除字符串中的逗號
 */
function RemoveStringComma(number){
    if (number == undefined || number == null || number == "") {
        return;
    }
    else {
        number = number.toString();
        if (number != undefined) {
            var pos = number.indexOf(",");
            while (pos != -1) {
                number = number.substring(0, pos) + number.substring(pos + 1, number.length);
                pos = number.indexOf(",");
            }
            return number;
        }
    }
}

/**
 * 移除字符串中的句號
 */
function RemoveStringEnd(number){
    if (number == undefined || number == null || number == "") {
        return;
    }
    else {
        number = number.toString();
        if (number != undefined) {
            var pos = number.indexOf(".");
            while (pos != -1) {
                number = number.substring(0, pos) + number.substring(pos + 1, number.length);
                pos = number.indexOf(".");
            }
            return number;
        }
    }
}

//會簽意見登錄簽章欄(營運中心與國金部)-Thickbox
function signContent(){
    $.ajax({
        handler: _handler,
        data: {
            formAction: "setBoss",
            mainId: responseJSON.mainId,
            querySea: (responseJSON.areaDocstatus == "LWC" && userInfo.unitNo == "025") ? false : true,
            queryArea2: (responseJSON.areaDocstatus == "LWC" &&
            (userInfo.unitNo == "920" ||
            userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935" ||
            userInfo.unitNo == "940" ||
            userInfo.unitNo == "943")) ? true : false
        }
	}).done(function(json){
            $(".boss").setItems({
                item: json.bossList,
                space: true
            });
            if (!json.noBossList2) {
                $(".boss2").setItems({
                    item: json.bossList2,
                    space: true
                });
            }
            $("#signContent").find(".boss").prop("disabled", false);
            $("#sUnitManager1 option:eq(1)").prop("selected", true);
            $("#sUnitManager1").prop("disabled", true);
            $("#sUnitManager3 option:eq(1)").prop("selected", true);
            $("#sUnitManager3").prop("disabled", true);
            var thickboxTitle = "#signContent";
            if (responseJSON.areaDocstatus == "LWC" &&
            (userInfo.unitNo == "920" ||
            userInfo.unitNo == "922" ||
            userInfo.unitNo == "931" ||
            userInfo.unitNo == "932" ||
            userInfo.unitNo == "933" ||
            userInfo.unitNo == "934" ||
            userInfo.unitNo == "935")) {
                // 營運中心
                thickboxTitle = "#signContent3";
                $("#sAreaLeader").val(json.sAreaLeader);
                $("#sAreaSubLeader").val(json.sAreaSubLeader);
                $("#sAreaManager").val(json.sAreaManager);
                $("#sAreaAppraiser").val(json.sAreaAppraiser);
            }
            else {
                // 國金部
                $("#sSeaManager").val(json.sSeaManager);
                $("#sSeaBoss").val(json.sSeaBoss);
                $("#sSeaAoName").val(json.sSeaAoName);
                $("#sSeaAppraiserCN").val(json.sSeaAppraiserCN);
            }
            // 開啟國金部 Thickbox
            $(thickboxTitle).thickbox({ // 使用選取的內容進行彈窗
                title: tempKey['l120m01a.sign2'],
                width: 300,
                height: 300,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                readOnly: false,
                buttons: {
                    "sure": function(){
                        if (responseJSON.areaDocstatus == "LWC" &&
                        (userInfo.unitNo == "920" ||
                        userInfo.unitNo == "922" ||
                        userInfo.unitNo == "931" ||
                        userInfo.unitNo == "932" ||
                        userInfo.unitNo == "933" ||
                        userInfo.unitNo == "934" ||
                        userInfo.unitNo == "935")) {
                            // 營運中心
                            var selectSeaBoss = $("select[name^=sArea]").map(function(){
                                return $(this).val();
                            }).toArray();
                            // 驗證主管是否都有選擇到
                            for (var i in selectSeaBoss) {
                                if (selectSeaBoss[i] == "") {
                                    //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.bossId']);
                                }
                            }
                            // 驗證單位主管是否有選擇到
                            if ($("#sUnitManager3 option:selected").val() == "") {
                                //l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
                                return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.managerId2']);
                            }
                            /*
                             //驗證是否有重複的主管
                             if(checkArrayRepeat(selectSeaBoss)){
                             //主管人員名單重複請重新選擇
                             return CommonAPI.showErrorMessage(tempKey['l120m01a.message02']);
                             }
                             */
                        }
                        else {
                            var selectSeaBoss = $("select[name^=sSea]").map(function(){
                                return $(this).val();
                            }).toArray();
                            var sSeaAoName = $("select[name='sSeaAoName']").val();
                            var checkOk = false;
                            if (sSeaAoName != undefined && sSeaAoName != null && sSeaAoName != "") {
                                checkOk = true;
                            }
                            // 驗證主管是否都有選擇到
                            for (var i in selectSeaBoss) {
                                if (selectSeaBoss[i] == "" && i != "2") {
                                    //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.bossId']);
                                }
                            }
                            // 驗證單位主管是否有選擇到
                            if ($("#sUnitManager1 option:selected").val() == "") {
                                //l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
                                return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.managerId2']);
                            }
                            /*
                             //驗證是否有重複的主管
                             if(checkArrayRepeat(selectSeaBoss)){
                             //主管人員名單重複請重新選擇
                             return CommonAPI.showErrorMessage(tempKey['l120m01a.message02']);
                             }
                             */
                        }
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "saveSignContentF1",
                                itemDscr09: getCkeditor("tItemDscr09"),
                                sSeaManager: $("#sSeaManager").val(),
                                sSeaBoss: $("#sSeaBoss").val(),
                                sSeaAoName: $("#sSeaAoName").val(),
                                sSeaAppraiserCN: $("#sSeaAppraiserCN").val(),
                                sAreaLeader: $("#sAreaLeader").val(),
                                sAreaSubLeader: $("#sAreaSubLeader").val(),
                                sAreaManager: $("#sAreaManager").val(),
                                sAreaAppraiser: $("#sAreaAppraiser").val(),
                                sUnitManager1: $("#sUnitManager1").val(),
                                sUnitManager3: $("#sUnitManager3").val(),
                                queryArea: (responseJSON.areaDocstatus == "LWC" &&
                                (userInfo.unitNo == "920" ||
                                userInfo.unitNo == "922" ||
                                userInfo.unitNo == "931" ||
                                userInfo.unitNo == "932" ||
                                userInfo.unitNo == "933" ||
                                userInfo.unitNo == "934" ||
                                userInfo.unitNo == "935")) ? true : false,
                                mainid: responseJSON.mainId
                            }
						}).done(function(responseData){
                                $("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                //$("#L120M01aForm14").setData(responseData.L120M01aForm14,false);
                                $.thickbox.close();
                                $.thickbox.close();
                                CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                        });
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
    });
}

//會簽意見登錄簽章欄(授管處補充說明+審查意見)-Thickbox
function signContent2(){
    $.ajax({
        handler: _handler,
        data: {
            formAction: "setBoss",
            mainId: responseJSON.mainId,
            queryHead: true
        }
	}).done(function(json){
            $(".boss").setItems({
                item: json.bossList,
                space: true
            });
            if (!json.noBossList2) {
                $(".boss2").setItems({
                    item: json.bossList2,
                    space: true
                });
            }
            $("#signContent2").find(".boss").prop("disabled", false);
            $("#sUnitManager2 option:eq(1)").prop("selected", true);
            $("#sUnitManager2").prop("disabled", true);
            $("#sHeadLeader").val(json.sHeadLeader);
            $("#sHeadSubLeader").val(json.sHeadSubLeader);
            $("#sHeadReCheck").val(json.sHeadReCheck);
            $("#sHeadAppraiser").val(json.sHeadAppraiser);
            
            // 開啟授管處 Thickbox
            $("#tItemDscr0A").readOnly(false);
            $("#tItemDscr0B").readOnly(false);
            $("#signContent2").thickbox({ // 使用選取的內容進行彈窗
                title: tempKey['l120m01a.sign2'],
                width: 300,
                height: 300,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                readOnly: false,
                buttons: {
                    "sure": function(){
                        var selectHBoss = $("select[name^=sHead]").map(function(){
                            return $(this).val();
                        }).toArray();
                        // 驗證主管是否都有選擇到
                        var hasBoss1 = true; //授管處-覆核
                        var hasBoss2 = true; //授管處-副處長
                        for (var i in selectHBoss) {
                            if (selectHBoss[i] == "") {
                                //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                                //授管處102.10.17蘇金柱提組織變更，覆核/副處長選其一即可
                                if (i == 1) {
                                    hasBoss1 = false;
                                }
                                else if (i == 2) {
                                    hasBoss2 = false;
                                }
                                else {
                                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.bossId']);
                                }
                                
                            }
                        }
                        
                        if (hasBoss1 == false && hasBoss2 == false) {
                            return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.bossId']);
                        }
                        
                        // 驗證單位主管是否有選擇到
                        if ($("#sUnitManager2 option:selected").val() == "") {
                            //l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
                            return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.managerId2']);
                        }
                        /*
                         //驗證是否有重複的主管
                         if(checkArrayRepeat(selectHBoss)){
                         //主管人員名單重複請重新選擇
                         return CommonAPI.showErrorMessage(tempKey['l120m01a.message02']);
                         }
                         */
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "saveSignContentF2",
                                itemDscr0A: $("#tItemDscr0A").val(),
                                itemDscr0B: $("#tItemDscr0B").val(),
                                sHeadLeader: $("#sHeadLeader").val(),
                                sHeadSubLeader: $("#sHeadSubLeader").val(),
                                sHeadReCheck: $("#sHeadReCheck").val(),
                                sHeadAppraiser: $("#sHeadAppraiser").val(),
                                sUnitManager2: $("#sUnitManager2").val(),
                                mainid: responseJSON.mainId
                            }
						}).done(function(responseData){
                                $("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                //setCkeditor2("itemDscr0A", responseData.L120M01aForm13.itemDscr0A);
                                //setCkeditor2("itemDscr0B", responseData.L120M01aForm13.itemDscr0B);
                                //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                                //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                        });
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
    });
}

function showCk(obj, idName){ //顯示Ckeditor
    if (obj.checked) {
        $('#' + idName).show();
    }
}

function hideCk(obj, idName){ //隱藏Ckeditor
    if (obj.checked) {
        $('#' + idName).hide();
    }
}

function checkCk(thickId, idName){ //檢查Ckeditor內容是否異動
    var isChanged = false;
    $('#' + idName).find(".tckeditor").each(function(i){
        if ($("#_" + $(this).attr("id")).val() == $(this).val()) {
            // 內容無異動
        }
        else {
            // 內容已異動
            isChanged = true;
        }
    });
    if (isChanged) {
        CommonAPI.confirmMessage(i18n.def["confirmCk"], function(b){
            if (b) {
                if (thickId == "signThick") {
                    $.ajax({
                        type: "POST",
                        handler: _handler,
                        data: {
                            formAction: "saveSignContent",
                            itemDscr09: $("#tItemDscr09").val(),
                            sSeaManager: $("#sSeaManager").val(),
                            sSeaBoss: $("#sSeaBoss").val(),
                            sSeaAoName: $("#sSeaAoName").val(),
                            sSeaAppraiserCN: $("#sSeaAppraiserCN").val(),
                            mainid: responseJSON.mainId
                        }
					}).done(function(responseData){
                            //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);								
                            //$("#L120M01aForm14").setData(responseData.L120M01aForm14,false);
                            setCkeditor2("itemDscr09", responseData.L120M01aForm14.itemDscr09);
                    });
                }
                else 
                    if (thickId == "signThick2") {
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "saveSignContent2",
                                itemDscr0A: $("#tItemDscr0A").val(),
                                itemDscr0B: $("#tItemDscr0B").val(),
                                sHeadLeader: $("#sHeadLeader").val(),
                                sHeadSubLeader: $("#sHeadSubLeader").val(),
                                sHeadReCheck: $("#sHeadReCheck").val(),
                                sHeadAppraiser: $("#sHeadAppraiser").val(),
                                mainid: responseJSON.mainId
                            }
						}).done(function(responseData){
                                //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                setCkeditor2("itemDscr0A", responseData.L120M01aForm13.itemDscr0A);
                                setCkeditor2("itemDscr0B", responseData.L120M01aForm13.itemDscr0B);
                            //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                            //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                        });
                    }
					else 
                        if (thickId == "signThick2a") {
                            $.ajax({
                                type: "POST",
                                handler: _handler,
                                data: {
                                    formAction: "saveSignContent2a",
                                    htItemDscr0C: $("#htItemDscr0C").val(),
                                    sHeadLeader: $("#sHeadLeader").val(),
                                    sHeadSubLeader: $("#sHeadSubLeader").val(),
                                    sHeadReCheck: $("#sHeadReCheck").val(),
                                    sHeadAppraiser: $("#sHeadAppraiser").val(),
                                    mainid: responseJSON.mainId
                                }
							}).done(function(responseData){
                                    //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                    setCkeditor2("itemDscr0C", responseData.L120M01aForm12.itemDscr0C);
                                //$("#L120M01aForm13").find("#itemDscr0A").val(responseData.L120M01aForm13.itemDscr0A);
                                //$("#L120M01aForm13").find("#itemDscr0B").val(responseData.L120M01aForm13.itemDscr0B);
                            });
                        }
                    else 
                        if (thickId == "signThick3") {
                            $.ajax({
                                type: "POST",
                                handler: _handler,
                                data: {
                                    formAction: "saveSignContent3",
                                    itemDscr07: $("#tItemDscr07").val(),
                                    itemDscr08: $("#tItemDscr08").val(),
                                    sAreaLeader: $("#sAreaLeader").val(),
                                    sAreaSubLeader: $("#sAreaSubLeader").val(),
                                    sAreaManager: $("#sAreaManager").val(),
                                    sAreaAppraiser: $("#sAreaAppraiser").val(),
                                    itemTitle: $("#itemTitle").val(),
                                    mainid: responseJSON.mainId
                                }
							}).done(function(responseData){
                                    //$("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                    setCkeditor2("itemDscr07", responseData.L120M01aForm15.itemDscr07);
                                    setCkeditor2("itemDscr08", responseData.L120M01aForm15.itemDscr08);
                                //$("#L120M01aForm15").find("#itemDscr07").val(responseData.L120M01aForm15.itemDscr07);
                                //$("#L120M01aForm15").find("#itemDscr08").val(responseData.L120M01aForm15.itemDscr08);
                            });
                        }
                // 更新原先內容成異動後的內容
                $('#' + idName).find(".tckeditor").each(function(i){
                    $("#_" + $(this).attr("id")).val($(this).val());
                });
            }
            else {
                // 回復原先Ckeditor內容
                $('#' + idName).find(".tckeditor").each(function(i){
                    $(this).val($("#_" + $(this).attr("id")).val());
                });
            }
        });
    }
}

function loginTitle(){
    $.ajax({
        handler: _handler,
        data: {
            formAction: "setBoss",
            mainId: responseJSON.mainId,
            queryArea: true
        }
	}).done(function(json){
            if (json.itemTitle != null && json.itemTitle != undefined && json.itemTitle != "") {
                $("#signContent3a").find("#itemTitle").val(json.itemTitle);
            }
            else {
                $("#signContent3a").find("#itemTitle").val(tempKey['l120m01a.decide2']);
            }
            signContent3a();
    });
}

//會簽意見登錄簽章欄(營運中心說明+決議)-Thickbox
function signContent3(){
    $.ajax({
        handler: _handler,
        data: {
            formAction: "setBoss",
            mainId: responseJSON.mainId,
            queryArea: true
        }
	}).done(function(json){
            $(".boss").setItems({
                item: json.bossList,
                space: true
            });
            if (!json.noBossList2) {
                $(".boss2").setItems({
                    item: json.bossList2,
                    space: true
                });
            }
            $("#signContent3").find(".boss").prop("disabled", false);
            $("#sUnitManager3 option:eq(1)").prop("selected", true);
            $("#sUnitManager3").prop("disabled", true);
            $("#sAreaLeader").val(json.sAreaLeader);
            $("#sAreaSubLeader").val(json.sAreaSubLeader);
            $("#sAreaManager").val(json.sAreaManager);
            $("#sAreaAppraiser").val(json.sAreaAppraiser);
            
            // 開啟營運中心 Thickbox
            $("#tItemDscr07").readOnly(false);
            $("#tItemDscr08").readOnly(false);
            if (json.itemTitle != null && json.itemTitle != undefined && json.itemTitle != "") {
                $("#signContent3").find("#itemTitle").val(json.itemTitle);
            }
            else {
                $("#signContent3").find("#itemTitle").val(tempKey['l120m01a.decide2']);
            }
            $("#signContent3").thickbox({ // 使用選取的內容進行彈窗
                title: tempKey['l120m01a.sign2'],
                width: 300,
                height: 300,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                readOnly: false,
                buttons: {
                    "sure": function(){
                        var selectABoss = $("select[name^=sArea]").map(function(){
                            return $(this).val();
                        }).toArray();
                        // 驗證主管是否都有選擇到
                        for (var i in selectABoss) {
                            if (selectABoss[i] == "") {
                                //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                                return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.bossId']);
                            }
                        }
                        // 驗證單位主管是否有選擇到
                        if ($("#sUnitManager3 option:selected").val() == "") {
                            //l120m01a.error1=請選擇+ l120m01a.managerId2=單位主管
                            return CommonAPI.showErrorMessage(tempKey['l120m01a.error1'] + tempKey['l120m01a.managerId2']);
                        }
                        /*
                         //驗證是否有重複的主管
                         if(checkArrayRepeat(selectABoss)){
                         //主管人員名單重複請重新選擇
                         return CommonAPI.showErrorMessage(tempKey['l120m01a.message02']);
                         }
                         */
                        $.ajax({
                            type: "POST",
                            handler: _handler,
                            data: {
                                formAction: "saveSignContentF3",
                                itemDscr07: $("#tItemDscr07").val(),
                                itemDscr08: $("#tItemDscr08").val(),
                                sAreaLeader: $("#sAreaLeader").val(),
                                sAreaSubLeader: $("#sAreaSubLeader").val(),
                                sAreaManager: $("#sAreaManager").val(),
                                sAreaAppraiser: $("#sAreaAppraiser").val(),
                                sUnitManager3: $("#sUnitManager3").val(),
                                itemTitle: $("#itemTitle").val(),
                                mainid: responseJSON.mainId
                            }
						}).done(function(responseData){
                                $("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                                //setCkeditor2("itemDscr07", responseData.L120M01aForm15.itemDscr07);
                                //setCkeditor2("itemDscr08", responseData.L120M01aForm15.itemDscr08);
                                //$("#L120M01aForm15").find("#itemDscr07").val(responseData.L120M01aForm15.itemDscr07);
                                //$("#L120M01aForm15").find("#itemDscr08").val(responseData.L120M01aForm15.itemDscr08);
                        });
                        $.thickbox.close();
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
    });
}

function LTrim(str){
    //去掉字符串 的頭空格
    var i;
    for (i = 0; i < str.length - 1; i++) {
        if (str.charAt(i) != " " && str.charAt(i) != " ") 
            break;
    }
    str = str.substring(i, str.length);
    return str;
}

function RTrim(str){
    var i;
    for (i = str.length - 1; i >= 0; i--) {
        if (str.charAt(i) != " " && str.charAt(i) != " ") 
            break;
    }
    str = str.substring(0, i + 1);
    return str;
}

function Trim(str){
    return LTrim(RTrim(str));
}


function signContent3a(){
    $("#signContent3a").thickbox({ // 使用選取的內容進行彈窗
        title: tempKey['l120m01a.bt04'],
        width: 480,
        height: 200,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "saveSignContent3",
                        itemDscr07: $("#tItemDscr07").val(),
                        itemDscr08: $("#tItemDscr08").val(),
                        sAreaLeader: $("#sAreaLeader").val(),
                        sAreaSubLeader: $("#sAreaSubLeader").val(),
                        sAreaManager: $("#sAreaManager").val(),
                        sAreaAppraiser: $("#sAreaAppraiser").val(),
                        itemTitle: $("#itemTitle").val(),
                        _itemTitle: $("#_itemTitle").val(),
                        mainid: responseJSON.mainId
                    }
				}).done(function(responseData){
                        $("#LMS1205S01Form").setData(responseData.LMS1205S01Form, false);
                        //$("#L120M01aForm15").find("#itemDscr07").val(responseData.L120M01aForm15.itemDscr07);
                        //$("#L120M01aForm15").find("#itemDscr08").val(responseData.L120M01aForm15.itemDscr08);
                });
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function logTitle(){
    var obj = CommonAPI.loadCombos(["lms1205m01_itemTitle"]);
    // 幣別
    $("#selectItemTitle").find("#sItemTitle").setItems({
        clear: false,
        item: obj.lms1205m01_itemTitle,
        format: "{value}.{key}",
        space: false
    });
    $("#selectItemTitle").thickbox({ // 使用選取的內容進行彈窗
        title: tempKey['l120m01a.title27'],
        width: 300,
        height: 200,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                var sVal = $("#sItemTitle option:selected").val();
                if (sVal == 0) {
                    $("#itemTitle").val("");
                    $("#itemTitle").prop("disabled", true);
                }
                else 
                    if (sVal == 3) {
                        $("#itemTitle").val("");
                        $("#itemTitle").prop("disabled", false);
                    }
                    else {
                        var sHtml = $("#sItemTitle option:selected").html();
                        $("#_itemTitle").val($("#sItemTitle option:selected").val());
                        $("#itemTitle").val(sHtml.substring(2));
                        $("#itemTitle").prop("disabled", true);
                    }
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//-------------------------------------以下為flow相關-------------------

//授管處審核中-退回更正
function btnHeadBackAction(){
    if (responseJSON.docKind == "2") {
        //授權外
        switch ($("#areaChk").val()) {
            case "1":
            case "2":
                controlBtShow([6]);
                break;
            case "3":
                controlBtShow([6, 7]);
                break;
            default:
                controlBtShow([6]);
                break;
        }
    }
    $("#checkBox").thickbox({
        //l120m01a.btnHeadAction=退回更正
        title: tempKey['l120m01a.btnHeadAction'],
        width: 250,
        height: 150,
        modal: true,
        valign: "bottom",
        readOnly: false,
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                $.thickbox.close();
                
                enterBackReason(value);
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//授管處-覆核按鈕
function btnChecActionkHead(){

    controlBtShow([1, 5]);
    
    $("#checkBox").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        readOnly: false,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                rejtMsgWithFlow(value).done(function(){
                	switch (value) {
	                    case "1":
	                        $.thickbox.close();
	                        flowAction({
	                            flowAction: "back"
	                        });
	                        
	                        break;
	                    case "5":
	                        $.thickbox.close();
	                        //l120m01a.message04=該案件是否確定執行確認作業？
	                        CommonAPI.confirmMessage(tempKey["l120m01a.message04"], function(b){
	                            if (b) {
	                                checkDate();
	                            }
	                        });
	                        break;
	                        
	                }
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//區域中心-覆核按鈕
function btnCheckActionArea(){

    //授權內出現放行核定 ，授權外出現呈授管
    if (responseJSON.docKind == "1") {
    
        controlBtShow([1, 5]);
    }
    else {
        controlBtShow([1, 2]);
    }
    
    $("#checkBox").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        readOnly: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                rejtMsgWithFlow(value).done(function(){
                	switch (value) {
	                    case "1":
	                        flowAction({
	                            flowAction: "back"
	                        });
	                        break;
	                    case "2":
	                        flowAction({
	                            flowAction: "sendHead"
	                        });
	                        break;
	                    case "5":
	                        $.thickbox.close();
	                        //l120m01a.message04=該案件是否確定執行確認作業？
	                        CommonAPI.confirmMessage(tempKey["l120m01a.message04"], function(b){
	                            if (b) {
	                                checkDate();
	                            }
	                        });
	                        break;
                	}
                });                                
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function thickReadOnly(){
    // 控制分頁頁籤內容唯讀(不包括下拉式選單)
    if (responseJSON.readOnly == "true") {
        thickboxOptions.readOnly = true;
    }
}

//控制按鈕顯示
function controlBtShow(arg){
    $("[name=queryButtonType]").removeAttr("disabled").removeAttr("checked");
    $("[name=queryButtonType]").parents("tr").hide();
    $.isArray(arg) ? arg : arg = [arg];
    for (var i in arg) {
        $("[name=queryButtonType][value=" + arg[i] + "]").parents("tr").show();
    }
}

// 覆核--海外聯貸案
function btnCheckSea(){
    $("#checkBoxSea").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=qButtonTypeSea]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                switch (value) {
                    case "1":
                        //returnGoBossSea
                        $.ajax({
                            type: "POST",
                            handler: "lms1205formhandler",
                            data: {
                                formAction: "returnGoBossSea",
                                mainId: responseJSON.mainId,
                                frag: "1"
                            }
						}).done(function(responseData){
                                // 更新授信簽報書Grid內容
                                CommonAPI.triggerOpener("gridview", "reloadGrid");
                                API.showPopMessage(i18n.def["runSuccess"], window.close);
                        });
                        //$.thickbox.close();
                        //window.close();
                        break;
                    case "8":
                        //$.thickbox.close();
                        //l120m01a.message04=該案件是否確定執行確認作業？
                        CommonAPI.confirmMessage(tempKey["l120m01a.message04"], function(b){
                            if (b) {
                                $.ajax({
                                    type: "POST",
                                    handler: "lms1205formhandler",
                                    data: {
                                        formAction: "returnGoBossSea",
                                        mainId: responseJSON.mainId,
                                        frag: "8"
                                    }
								}).done(function(responseData){
                                        //alert(JSON.stringify(responseData));
                                        // 更新授信簽報書Grid內容
                                        CommonAPI.triggerOpener("gridview", "reloadGrid");
                                        API.showPopMessage(i18n.def["runSuccess"], window.close);
                                });
                            }
                        });
                        break;
                    default:
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//覆核按鈕
function btnCheckAction(){
    $.ajax({
        handler: "lms1205formhandler",
        data: {
            formAction: "queryBranchData"
        }
	}).done(function(obj){
            //分行-待覆核
            if (obj.showSendBranch) {
                //分行待覆核要判斷該出現的選項
                if (responseJSON.docKind == "1") {
                    //授權內
                    /*	1分行授權內<br/>
                     * 2總行授權內<br/>
                     * 3營運中心授權內
                     */
					var authLvl = $("#authLvl").val();
					if(authLvl != undefined){
						switch ($("#authLvl").val()) {
	                        case "1":
	                            //8確認
	                            controlBtShow([1, 8]);
	                            break;
	                        case "2":
	                        case "3":
	                            /*2總行授權內<br/>
	                         *3營運中心授權內
	                         */
	                            //呈總行
	                            controlBtShow([1, 4]);
	                            break;
	                        default:
	                            controlBtShow([1, 8]);
	                            break;
	                    }
					}
                    
                }
                else {
                    //授權外
                    //分行待覆核只出現呈總行和退回分行
                    controlBtShow([1, 4]);
                }
            }
            else {
                //總行覆核
                switch (obj.country) {
                    case "AU":
                        
                        if (responseJSON.docKind == "1") {
                            //授權內
                            /*	1分行授權內<br/>
                         * 2總行授權內<br/>
                         * 3營運中心授權內
                         */
							var authLvl = $("#authLvl").val();
							if (authLvl != undefined) {
								switch ($("#authLvl").val()) {
	                                case "1":
	                                case "2":
	                                    //8確認
	                                    controlBtShow([1, 8]);
	                                    break;
	                                case "3":
	                                    // 提會待登錄	
	                                    controlBtShow([1, 9]);
	                                    break;
	                                default:
	                                    controlBtShow([1, 8]);
	                                    break;
	                            }
							}                            
                        }
                        else {
                            //授權外
                            controlBtShow([1, 9]);
                        }
                        break;
					case "KH":
					    //J-104-0097-002  金邊分行0C7(魏迎晃)要求總行自簽案件不要走提會待登錄/待覆核 
                        //controlBtShow([1, 9]);
						checkForBranchWithoutCC();
                        break;	
                    case "US":
					    //J-104-0097-002  金邊分行0C7(魏迎晃)要求總行自簽案件不要走提會待登錄/待覆核
						//controlBtShow([1, 9]);
					    checkForBranchWithoutCC();
                        break;	
					case "CA":	
					    //J-104-0097-002  金邊分行0C7(魏迎晃)要求總行自簽案件不要走提會待登錄/待覆核
						//controlBtShow([1, 9]);
					    checkForBranchWithoutCC();				
                        break;
					case "JP":	
					    //J-109-0363_05097_B1001 Web e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
						//controlBtShow([1, 9]);
					    checkForBranchWithoutCC();				
                        break;    
					case "CN":
//                      原寧波分行與吳江支行走不同FLOW，後來企劃處簽准一率走COUNTRY HEAD模式					
//					    if(obj.brno == "0C8" || obj.brno == "0D1"){
//							//蘇州與吳江走子母行
//							controlBtShow([1, 9]);
//						}else{
//							//寧波走一般分行
//							checkForBranch();
//						}
                        //J-104-0097-002  金邊分行0C7(魏迎晃)要求總行自簽案件不要走提會待登錄/待覆核
                        //controlBtShow([1, 9]);
						checkForBranchWithoutCC();
                        break;
                    default:
                        //總行-待覆核
                        checkForBranch();
                        break;
                }
                
                
            }
            
    });
    
    $("#checkBox").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        readOnly: false,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){            	
            	var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
           	
                rejtMsgWithFlow(value).done(function(){
                	switch (value) {
	                    case "1":
	                    	dfd_askBackRatingDoc().done(function(json_askBackRatingDoc){
	                    		flowAction($.extend({flowAction: "backFirst"}, json_askBackRatingDoc||{}));	
            				});
	                        break;
	                    case "2":
	                        flowAction({
	                            flowAction: "sendHead"
	                        });
	                        break;
	                    case "3":
	                        flowAction({
	                            flowAction: "sendArea"
	                        });
	                        break;
	                    case "4":
	                        flowAction({
	                            flowAction: "sendMain"
	                        });
	                        break;
	                    case "8":
	                        $.thickbox.close();
	                        //l120m01a.message04=該案件是否確定執行確認作業？
	                        CommonAPI.confirmMessage(tempKey["l120m01a.message04"], function(b){
	                            if (b) {
	                                checkDate();
	                            }
	                        });
	                        break;
	                    case "9":
	                        flowAction({
	                            flowAction: "ok"
	                        });
	                        break;
	                }
	                if (value != "8") {
	                    $.thickbox.close();
	                }		
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//輸入核定日期視窗
function checkDate(){
    $("#forCheckDate").val(CommonAPI.getToday());
    $("#openChecDatekBox").thickbox({ // 使用選取的內容進行彈窗
        //L120M01A.message03 = 請輸入核定日
        title: tempKey['l120m01a.message03'],
        width: 100,
        height: 100,
        modal: true,
        valign: "bottom",
        align: "center",
        readOnly: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var forCheckDate = $("#forCheckDate").val();
                if ($.trim(forCheckDate) == "") {
                    //L120M01A.message03 = 請輸入核定日
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.message03']);
                }
                if(new Date(forCheckDate) > new Date(CommonAPI.getToday())){
                	return CommonAPI.showErrorMessage(tempKey['l120m01a.message05']);
                }
                flowAction({
                    flowAction: "check",
                    checkDate: forCheckDate
                });
                $.thickbox.close();
            },
            
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//撤件-陳復按鈕
function btnBackCaseBox(){
    $("[name=backCaseRadio]").removeAttr("disabled").removeAttr("checked");
    
    $("#backCaseBox").thickbox({
    
        //l120m01a.backCaseRadio1=撤件 l120m01a.backCaseRadio2=待陳復
        title: tempKey['l120m01a.backCaseRadio1'] + "/" + tempKey['l120m01a.backCaseRadio2'],
        width: 250,
        height: 100,
        modal: true,
        valign: "bottom",
        readOnly: false,
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=backCaseRadio]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                
                switch (value) {
                    case "1":
                        signCaseCancelDate(function(){
							flowAction({
	                            flowAction: "cancelCase"
	                        });
							$.thickbox.close();
						});
                        break;
                    case "2":
                        flowAction({
                            flowAction: "sendDrc"
                        });
						$.thickbox.close();
                        break;
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//判斷中英文長度
function checkLength(str_to_count){
    var countMe = str_to_count;
    var escapedStr = encodeURI(countMe);
    if (escapedStr.indexOf("%") != -1) {
        var count = escapedStr.split("%").length - 1;
        if (count == 0) 
            count++;
        var tmp = escapedStr.length - (count * 3);
        count = count + tmp;
    }
    else {
        count = escapedStr.length;
    }
    return count;
}



//輸入退回原因
function enterBackReason(value){
    $("#backReasonTextarea").removeAttr("disabled").removeAttr("readonly").val("");
    
    $("#backReasonBox").thickbox({
    
        //l120m01a.backReason=退補件原因
        title: tempKey['l120m01a.backReason'],
        width: 300,
        height: 180,
        modal: true,
        readOnly: false,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var backReason = $.trim($("#backReasonTextarea").val());
                
                if (!backReason) {
                
                    //l120m01a.error2=請輸入退補件原因
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error2']);
                }
                
                if (checkLength(backReason) > 1536) {
                
                    //val.maxlength=最多輸入{0}個字元.
                    return CommonAPI.showErrorMessage(i18n.def['val.maxlength'].replace("{0}", "512"));
                }
                
                switch (value) {
                    case "6":
                        flowAction({
                            flowAction: "waitCase",
                            backReason: backReason
                        });
                        break;
                        
                    case "7":
                        flowAction({
                            flowAction: "backArea",
                            backReason: backReason
                        });
                        break;
                }
                $.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//J-111-0551_05097_B1006 Web e-Loan授信之信用風險管理遵循檢核表及借款人暨關係戶與本行授信往來情形及利潤貢獻度納入在途案件之額度
//查詢在途授信額度
function approveUnestablshExl( ){
	$("#approveUnestablshExlTextarea").removeAttr("disabled").removeAttr("readonly").val("");
  
  $("#approveUnestablshExlBox").thickbox({

      title: "在途授信查詢",
      width: 700,
      height: 400,
      modal: true,
      readOnly: false,
      valign: "bottom",
      align: "center",
      i18n: i18n.def,
      buttons: {
          "sure": function(){
              var approveUnestablshExlTextarea = $.trim($("#approveUnestablshExlTextarea").val());
              
              if (!approveUnestablshExlTextarea) {
                  return CommonAPI.showErrorMessage("請輸入查詢統編");
              }
     
              $.ajax({
                  handler: "lmscommonformhandler",
                  data: {
                      formAction: "chkApproveUnestablshed",
                      approveUnestablshExlTextarea :approveUnestablshExlTextarea
                  }
				}).done(function(obj){
                  	if(obj.errMsg != ""){
                  		 return CommonAPI.showErrorMessage(obj.errMsg);
                  	}else{
           
                  		$.form.submit({
                              url: "../../simple/FileProcessingService",
                              target: "_blank",
                              data: {
                                  mainId: responseJSON.mainId,
                                  fileDownloadName: "approveUnestablshed.xls",
                                  serviceName: "lms1302xlsservice",
                                  approveUnestablshExlTextarea : obj.approveUnestablshExlTextarea    //已經清除空白跟換行符號
                              }
                          });
                  		
                  		$.thickbox.close();
                  	}
              });
               
              
          },
          "cancel": function(){
              API.confirmMessage(i18n.def['flow.exit'], function(res){
                  if (res) {
                      $.thickbox.close();
                  }
              });
          }
      }
  });
}


/**
 * 設定主管選單
 */
function selectHeadOfficStaff(){
	var ajax =  $.ajax({
		handler : "lms1205formhandler",
		data : {
			formAction : "setBoss"
		}
	}).done(function(json){
			$(".boss").setItems({
				item:json.bossList,
				space: true
			});
			if(!json.noBossList2){
				$(".boss2").setItems({
					item:json.bossList2,
					space: true
				});
			}
			$("#sUnitManager option:eq(1)").prop("selected",true);
			$("#sUnitManager").prop("disabled",true);
	});
	
	return ajax;
}

//選擇主管box
function selectBossboxForHeadOffice() {
	 var dfd = $.Deferred();
    LMSM05Panel_js_obj.replace_sign_descLabel();

    //檢核通過
    $("#selectBossBox").thickbox({ // 使用選取的內容進行彈窗
        //l120m01a.bt03=覆核
        title: "",
        width: 500,
        height: 300,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function() {

                var selectBoss = $("select[name^=boss]").map(function() {
                    return $(this).val();
                }).toArray();
                // 驗證主管是否都有選擇到
                for (var i in selectBoss) {
                    if (selectBoss[i] == "") {
                        //l120m01a.error1=請選擇+ l120m01a.bossId=授信主管
                        return CommonAPI.showErrorMessage(i18n.lmsm01["select.message01"] + $("#L120M01F_L3").val());
                    }
                }
                // 驗證 副總經理 是否有選擇到
                if ($('#sManager_Y01_LA').is(':visible') && $("#sManager_Y01_LA option:selected").val() == "") {
                    return CommonAPI.showErrorMessage(i18n.lmsm01["select.message01"] + $("#L120M01F_TH_UnitTypeR_LA").val());
                }

                // 驗證單位授權主管是否有選擇到
                if ($("#sManager option:selected").val() == "") {
                    //l120m01a.error1=請選擇+ l120m01a.managerId=單位/授權主管
                    return CommonAPI.showErrorMessage(i18n.lmsm01["select.message01"] + $("#L120M01F_L5").val());
                }

                // 驗證 總經理 是否有選擇到
                if ($('#sManager_Y01_L5').is(':visible') && $("#sManager_Y01_L5 option:selected").val() == "") {
                    return CommonAPI.showErrorMessage(i18n.lmsm01["select.message01"] + $("#L120M01F_TH_UnitTypeP_L5_Y01").val());
                }

                //驗證是否有重複的主管
                if (checkArrayRepeat(selectBoss)) {
                    //主管人員名單重複請重新選擇
                    return CommonAPI.showErrorMessage(i18n.lmsm01["select.message02"]);
                }
                //建立簽章欄
                $.ajax({
                    type: "POST",
                    handler: "lms1205formhandler",
                    data: {
                        formAction: "saveL120m01fForHeadOffice",
                        mainId: responseJSON.mainId,
                        selectBoss: selectBoss,
                        manager: $("#sManager option:selected").val(),
                        AOPerson: $("#AOPerson option:selected").val(),
                        sUnitManager: $("#sUnitManager option:selected").val()
                    }
				}).done(function(responseData) {
                        //alert(JSON.stringify(responseData));
                        dfd.resolve();
                });
                $.thickbox.close();
            },

            "cancel": function() {
                API.confirmMessage(i18n.def['flow.exit'], function(res) {
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
	
	return dfd.promise();
}

//總行提會待登錄
function btnSendWaitLogin(){
	
    //要依據泰國 澳洲 加拿大
    //澳洲 在總行才會有這按鈕  當此案件為 分行傳送上來的案件 要有退回編製中  跟  呈主管覆核 
    //泰國 授管處 核准才有這按鈕
    //加拿大 在分行 和總行 才有這按鈕 
	
	var country = "";
    $.ajax({
        handler: "lms1205formhandler",
        data: {
            formAction: "queryBranchData"
        }
	}).done(function(obj){ //UPGRADETODO 2025/06/20修改，待確認是否正確
			country = obj.country;
            switch (obj.country) {
                case "TH":
                    controlBtShow([1, 10]);
                    break;
                case "AU":
                    controlBtShow([1, 10]);
                    break;
                case "CA":
                case "US":
				case "KH":
				case "JP":	
					//J-109-0363_05097_B1001 Web e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
                    controlBtShow([1, 10]);
                    break;
				case "CN":
//				          原寧波分行與吳江支行走不同FLOW，後來企劃處簽准一率走COUNTRY HEAD模式
//				    if(obj.brno == "0C8" || obj.brno == "0D1"){
//						//蘇州與吳江走子母行
//						controlBtShow([1, 10]);
//					}else{
//						//寧波走一般分行
//					}
					controlBtShow([1, 10]);
                    break;
            }

    $("#checkBox").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        readOnly: false,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
            
                /**
                 1退回經辦修改
                 2呈授管處
                 3呈營運中心
                 4呈總行
                 5核定放行
                 6退回分行更正
                 7退回區域中心更正
                 8確認
                 9 呈主管覆核
                 */
                var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                
                switch (value) {
                    case "1":
                        flowAction({
                            flowAction: "backFirst"
                        });
                        break;
                    case "10":
						switch(country){
							case "CA":
			                case "US":
							case "KH":                               
							case "CN":
							case "JP":	
								//總行自己不需再補簽章欄
								//J-109-0363_05097_B1001 Web e-Loan授信修改日本地區分行大阪分行授權外案件經由東京分行放行後送呈授信審查處(Country Head 模式)。
								var unitNo = responseJSON.caseBrId.toString().substring(0, 3);						
								if(unitNo != responseJSON.ownBrId){
									selectHeadOfficStaff().done(function(){
										selectBossboxForHeadOffice().done(function(){flowAction({flowAction: "ok"});})
									});	
								} else {
									flowAction({flowAction: "ok"});
								}
													
								break;
							default:
								flowAction({flowAction: "ok"});
								break;
						}								                       
                        break;
                        
                }
                //$.thickbox.close();
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
	});
    

}

//總行提會待覆核
function btnCheckWaitApprove(){
    $.ajax({
        handler: "lms1205formhandler",
        data: {
            formAction: "queryBranchData"
        }
	}).done(function(obj){
            if (obj.showSendBranch) {
                //分行待覆核要判斷該出現的選項
                if (responseJSON.docKind == "1") {
                    //授權內
                    /*	1分行授權內<br/>
                     * 2總行授權內<br/>
                     * 3營運中心授權內
                     */
                    switch ($("#authLvl").val()) {
                        case "1":
                            //8確認
                            controlBtShow([1, 8]);
                            break;
                        case "2":
                        case "3":
                            /*2總行授權內<br/>
                         * 3營運中心授權內
                         */
                            //呈總行
                            
                            controlBtShow([1, 4]);
                            break;
                        default:
                            controlBtShow([1, 2]);
                            break;
                    }
                }
                else {
                    //授權外
                    //分行待覆核只出現呈總行和退回分行
                    controlBtShow([1, 4]);
                }
                
            }
            else {
                switch (obj.country) {
                    case "TH":
                        controlBtShow([1, 8]);
                        break;
                    default:
                        //總行待覆核
                        checkForBranch();
                        break;
                }
            }
    });
    
    
    
    $("#checkBox").thickbox({
        //button.check=覆核
        title: tempKey['l120m01a.btnCheck'],
        width: 250,
        height: 150,
        modal: true,
        readOnly: false,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var value = $("[name=queryButtonType]:checked").val();
                if (!value) {
                
                    //l120m01a.error1=請選擇
                    return CommonAPI.showErrorMessage(tempKey['l120m01a.error1']);
                }
                
                switch (value) {
                    case "1":
                        flowAction({
                            flowAction: "back"
                        });
                        break;
                    case "2":
                        flowAction({
                            flowAction: "sendHead"
                        });
                        break;
                    case "3":
                        flowAction({
                            flowAction: "sendArea"
                        });
                        break;
                    case "4":
                        flowAction({
                            flowAction: "sendMain"
                        });
                        break;
                    case "8":
                        //l120m01a.message04=該案件是否確定執行確認作業？
                        CommonAPI.confirmMessage(tempKey["l120m01a.message04"], function(b){
                            if (b) {
                                checkDate();
                            }
                        });
                        break;
                    case "9":
                        flowAction({
                            flowAction: "ok"
                        });
                        break;
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

//總行待覆核 按鈕 判斷
function checkForBranch(){
    if (responseJSON.docKind == "1") {
        //授權內
        /*	1分行授權內<br/>
         * 2總行授權內<br/>
         * 3營運中心授權內
         */
        switch ($("#authLvl").val()) {
            case "1":
            case "2":
                //8確認
                controlBtShow([1, 8]);
                break;
            case "3":
                // 呈營運中心 		
                controlBtShow([1, 3]);
                break;
            default:
                controlBtShow([1, 8]);
                break;
        }
    }
    else {
        //授權外
        switch ($("#areaChk").val()) {
            case "1":
            case "2":
                controlBtShow([1, 2]);
                break;
            case "3":
                controlBtShow([1, 3]);
                break;
            default:
                controlBtShow([1, 2]);
                break;
        }
    }
}

//總行待覆核 按鈕 判斷
//J-104-0097-002  金邊分行0C7(魏迎晃)要求總行自簽案件不要走提會待登錄/待覆核
function checkForBranchWithoutCC(){
    if (responseJSON.docKind == "1") {
        //授權內
        /*	1分行授權內<br/>
         * 2總行授權內<br/>
         * 3營運中心授權內
         */
        switch ($("#authLvl").val()) {
            case "1":
            case "2":
                //8確認
                controlBtShow([1, 8]);
                break;
            case "3":
                // 呈營運中心 		
                controlBtShow([1, 3, 9]);
                break;
            default:
                controlBtShow([1, 8]);
                break;
        }
    }
    else {
        //授權外
        switch ($("#areaChk").val()) {
            case "1":
            case "2":
                controlBtShow([1, 2, 9]);
                break;
            case "3":
                controlBtShow([1, 3, 9]);
                break;
            default:
                controlBtShow([1, 2, 9]);
                break;
        }
    }
}

//待陳復退回審核中
function backToHeadFirst(){
    //actoin_001=是否執行此動作?
    API.confirmMessage(i18n.def['actoin_001'], function(res){
        if (res) {
            flowAction();
        }
    });
}

function flowAction(sendData){
    $.ajax({
        type: "POST",
        handler: "lms1205formhandler",
        data: $.extend({
            formAction: "flowAction",
            //mainOid: $("#mainOid").val()
            mainOid: responseJSON.mainOid
        }, (sendData || {}))
	}).done(function(){
            CommonAPI.triggerOpener("gridview", "reloadGrid");
            API.showPopMessage(i18n.def["runSuccess"], window.close);
            //setCloseConfirm(false);
    });
}

//J-110-0521 為加強授信徵、授信簽報流程管理，海外eloan系統增加留存案件流程紀錄 登錄撤件日期
function signCaseCancelDate(afterfunction){
	$("#formCaseCancelDate").find("#caseCancelDate").val(CommonAPI.getToday());
    $("#divCaseCancelDate").thickbox({ // 使用選取的內容進行彈窗
        title: tempKey['l120s17a.caseCancelDate'],
        width: 300,
        height: 150,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        readOnly: false,
        buttons: {
            "sure": function(){
                $.ajax({
                    type: "POST",
                    handler: "lms1201formhandler",
                    data: {
                        formAction: "setCaseCancelDate",
                        caseReceivedDate: $("#formCaseCancelDate").find("#caseCancelDate").val(),
                        mainid: responseJSON.mainId
                    }
				}).done(function(responseData){
                        $.thickbox.close();
						if (afterfunction) {
							afterfunction();
						}
                });
            },
            "cancel": function(){
                $.thickbox.close();
            }
        }
    });
}

//-------------------------------------以上為flow相關-------------------
/**  
 *檢驗列印順序
 *@param {function } 按下確定後執行的動作
 */
function queryPrintSeq(afterAction){
	var deferred = $.Deferred();

    $.ajax({
        async: false,
        handler: "lms1405m01formhandler",
        action: "queryPrintSeq",
        data: {//把資料轉成json
            //mainId: $("#mainId").val()
			mainId:responseJSON.mainId
        }
	}).done(function(obj){
            if (obj.printSeqMsg && obj.printSeqMsg != "") {
                CommonAPI.confirmMessage(obj.printSeqMsg, function(b){
                    if (b) {
                        afterAction(deferred);
						
                    }
                });
            }
            else {
                afterAction(deferred);
				
            }
    }); //close ajax
    
	 return deferred.promise();
}


//先彈跳出的選擇列印哪種類型報表
function printAction(){
    //alert(JSON.stringify(userInfo));
    $.ajax({
        type: "POST",
        handler: "lms1205formhandler",
        data: {
            formAction: "getPrintCondition",
            mainid: responseJSON.mainId
        }
	}).done(function(json){
            var count = 0;
            var highLen = 0;
            if (json.printA != 'Y') {
                $("#printUnitTypeA").css("display", "none");
            }
            else {
                $("#printUnitTypeA").css("display", "");
                count = count + 1;
            }
            
            if (json.printB != 'Y') {
                $("#printUnitTypeB").css("display", "none");
            }
            else {
                // Miller edited at 2012/07/24
                if ($("#book2").is(":hidden")) {
                    $("#printUnitTypeB").css("display", "none");
                }
                else {
                    $("#printUnitTypeB").css("display", "");
                    count = count + 1;
                }
            }
            
            if (json.printC != 'Y') {
                $("#printUnitTypeC").css("display", "none");
            }
            else {
                $("#printUnitTypeC").css("display", "");
                count = count + 1;
            }
            if (json.printD != 'Y') {
                $("#printUnitTypeD").css("display", "none");
            }
            else {
                $("#printUnitTypeD").css("display", "");
                count = count + 1;
            }
            
            if (json.printZ != 'Y') {
                $("#printUnitTypeZ").css("display", "none");
            }
            else {
                $("#printUnitTypeZ").css("display", "");
                count = count + 1;
            }
            
            if (json.printE != 'Y') {
                $("#printUnitTypeE").css("display", "none");
            }
            else {
                $("#printUnitTypeE").css("display", "");
                count = count + 1;
            }
            if (json.printF != 'Y') {
                $("#printUnitTypeF").css("display", "none");
            }
            else {
                $("#printUnitTypeF").css("display", "");
                count = count + 1;
            }
            if (json.printG != 'Y') {
                $("#printUnitTypeG").css("display", "none");
            }
            else {
                $("#printUnitTypeG").css("display", "");
                count = count + 1;
            }
            if (json.printH != 'Y') {
                $("#printUnitTypeH").css("display", "none");
            }
            else {
                $("#printUnitTypeH").css("display", "");
                count = count + 1;
            }
            if (responseJSON.docCode == "3") {
                $("#printUnitTypeH").css("display", "none");
            }
            
			if (json.printJ != 'Y') {
                $("#printUnitTypeJ").css("display", "none");
            }
            else {
                $("#printUnitTypeJ").css("display", "");
                count = count + 1;
            }
			
			if (json.printY != 'Y') {
                $("#printUnitTypeY").css("display", "none");
            }
            else {
                $("#printUnitTypeY").css("display", "");
                count = count + 1;
            }
			
			if (json.printW != 'Y') {
                $("#printUnitTypeW").css("display", "none");
            }
            else {
                $("#printUnitTypeW").css("display", "");
                count = count + 1;
            }
			
			//J-109-0479_05097_B1001 Web e-Loan簽報書增加各別流程控管階段的時間點並提供列印案件階段進度及統計excel下載
            if (json.printK != 'Y') {
                $("#printUnitTypeK").css("display", "none");
            }
            else {
                $("#printUnitTypeK").css("display", "");
                count = count + 1;
            }
            
			
            if (count <= 2) {
                highLen = 150;
            }
            else 
                if (count > 2 && count <= 4) {
                    highLen = 200;
                }
                else 
                    if (count > 4 && count <= 6) {
                        highLen = 250;
                    }
                    else 
                        if (count > 6 && count <= 8) {
                            highLen = 300;
                        }
                        else 
                            if (count > 8 && count <= 10) {
                                highLen = 350;
                            }
            
            $("input[name=printCondition]").prop("disabled", false);
			
			//J-105-0076-001 BGN Web e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
			var obj = CommonAPI.loadCombos(["unitAuthJobTitle"]);
		    // 單位授權主管職稱
		    $("#printThickBox").find("#unitAuthJobTitle").setItems({
		        clear: false,
		        item: obj.unitAuthJobTitle,
		        format: "{key}",
		        space: false
		    });
			
			//英文版才要跳出來選
			var showUnitAuthJobTitle = json.showUnitAuthJobTitle;
			if(showUnitAuthJobTitle == true){
				
				$("#showUnitAuthJobTitle").show();
				$("#unitAuthJobTitle").val(json.unitAuthJobTitle);
			}else{
				
				$("#showUnitAuthJobTitle").hide();
				$("#unitAuthJobTitle").val("");
			}
			//J-105-0076-001 END Web e-Loan英文介面單位主管簽核之英文職稱請增列開放選項

            $("#printThickBox").thickbox({ // 使用選取的內容進行彈窗
                title: tempKey['THICKBOX.RPTTITLE'],
                width: 350,
                height: highLen,
                modal: true,
                valign: "bottom",
                align: "center",
                i18n: i18n.def,
                readOnly: false,
                buttons: {
                    "sure": function(){
                        $.thickbox.close();

                        //J-105-0076-001 BGN Web e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
						//先儲存單位授權主管職稱
						if(showUnitAuthJobTitle){
							
							var unitAuthJobTitle = $("#unitAuthJobTitle").val();
							
							$.ajax({
						        type: "POST",
						        handler: "lms1205formhandler",
						        data: {
						            formAction: "saveUnitAuthJobTitle",
						            mainid: responseJSON.mainId,
									unitAuthJobTitle : unitAuthJobTitle
						        }
							}).done(function(obj1){
						             if ($("input[type=radio][name=printCondition]:checked").val() == 'Y') {
		                                //異常通報產Excel
		                                $.form.submit({
		                                    url: "../../simple/FileProcessingService",
		                                    target: "_blank",
		                                    data: {
		                                        mainId: responseJSON.mainId,
		                                        fileDownloadName: "LMS1301M01A.xls",
		                                        serviceName: "lms1301xlsservice"
		                                    }
		                                });
						             }else if ($("input[type=radio][name=printCondition]:checked").val() == 'K') {
						            	 //J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
			                        	//授權外案件徵授信進度時程表
			                        	var content =  "R40" + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^" + "" ;
			                        	$.form.submit({
			                                url: "../../simple/FileProcessingService",
			                                target: "_blank",
			                                data: {
			                                    mainId: responseJSON.mainId,
			                                    rptOid: content,
			                                    fileDownloadName: "LMS1201R40.pdf",
			                                    serviceName: "lms1205r01rptservice"
			                                }
			                            });
			                         } else {
										////J-105-0076-001 Wev e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
		                                showPrint($("input[type=radio][name=printCondition]:checked").val());
		                             }
						             
						    });
						}else{
							
							//不用先儲存單位授權主管職稱
							if ($("input[type=radio][name=printCondition]:checked").val() == 'Y') {
                                //異常通報產Excel
                                $.form.submit({
                                    url: "../../simple/FileProcessingService",
                                    target: "_blank",
                                    data: {
                                        mainId: responseJSON.mainId,
                                        fileDownloadName: "LMS1301M01A.xls",
                                        serviceName: "lms1301xlsservice"
                                    }
                                });
							}else if ($("input[type=radio][name=printCondition]:checked").val() == 'K') {
								//J-110-0521_05097_B1001 Web e-Loan海外授信系統增加留存案件流程紀錄
	                        	//授權外案件徵授信進度時程表
	                        	var content =  "R40" + "^" + "" + "^" + "" + "^" + "" + "^" + "" + "^" + "" ;
	                        	$.form.submit({
	                                url: "../../simple/FileProcessingService",
	                                target: "_blank",
	                                data: {
	                                    mainId: responseJSON.mainId,
	                                    rptOid: content,
	                                    fileDownloadName: "LMS1201R40.pdf",
	                                    serviceName: "lms1205r01rptservice"
	                                }
	                            });
	                        } else {
								////J-105-0076-001 Wev e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
                                showPrint($("input[type=radio][name=printCondition]:checked").val(),$("#unitAuthJobTitle").val());
                            }
						}
						//J-105-0076-001 END Web e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
                        
                    },
                    "cancel": function(){
                        API.confirmMessage(i18n.def['flow.exit'], function(res){
                            if (res) {
                                $.thickbox.close();
                            }
                        });
                    }
                }
            });
    });
}

//J-105-0076-001 Wev e-Loan英文介面單位主管簽核之英文職稱請增列開放選項
function showPrint(condition){
    //若是彈跳出審查意見及補充說明 則列印時 帶的條件不同
    if (condition == 'C' || condition == 'D' || condition == 'E' || condition == 'F' || condition == 'G' || condition == 'Z') {
        $("#printGrid2").jqGrid("setGridParam", {
            postData: {
                formAction: "queryPrint",
                mainId: responseJSON.mainId,
                printCondition: condition
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#printView2").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.def['print'],
            width: 700,
            height: 450,
            readOnly: false,
            modal: true,
            buttons: (function(){
                var btn = {};
                btn[i18n.def['print']] = function(){
                    doPrintForR18R20R21();
                }
                btn[i18n.def['close']] = function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
                return btn;
            })()
        });
    }
    else {
        $("#printGrid").jqGrid("setGridParam", {
            postData: {
                formAction: "queryPrint",
                mainId: responseJSON.mainId,
                printCondition: condition
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#printView").thickbox({ // 使用選取的內容進行彈窗
            title: i18n.def['print'],
            width: 700,
            height: 450,
            readOnly: false,
            modal: true,
            buttons: (function(){
                var btn = {};
                btn[i18n.def['print']] = function(){
                    var pdfName = "";
                    var count = 0;
                    var content = "";
                    var id = $("#printGrid").getGridParam('selarrrow');
                    for (var i = 0; i < id.length; i++) {
                        if (id[i] != "") {
                            var datas = $("#printGrid").getRowData(id[i]);
                            content = content + datas.rpt + "^" + datas.oid + "^" + datas.custId + "^" + datas.dupNo + "^" + datas.cntrNo + "^" + datas.refMainId + "|";
                            pdfName = datas.rptNo + ".pdf";
                            count++;
                        }
                    }
                    
                    if (content.length != 0) {
                        content = content.substring(0, content.length - 1);
                    }
                    if (count == 0) {
                        CommonAPI.showMessage(i18n.def['grid.selrow']);
                    }
                    else {
                        if (count != 1) {
                            pdfName = "LMS1205R01.pdf";
                        }
						
						$.form.submit({
                            url: "../../simple/FileProcessingService",
                            target: "_blank",
                            data: {
                                mainId: responseJSON.mainId,
                                rptOid: content,
                                fileDownloadName: pdfName,
                                serviceName: "lms1205r01rptservice",
                                printCondition: condition
                            }
                        });
                    }
                }
                btn[i18n.def['close']] = function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
                return btn;
            })()
        });
    }
    
}

function doPrintForR18R20R21(){
    var content = "";
    var dataOther;
    var printGrid2 = $("#printGrid2");
    var id = printGrid2.getGridParam('selrow');
    if (id != null) {
        var data = printGrid2.getRowData(id);
        if (data.rpt == "R18") {
            dataOther = data.rptHandle;
        }
        else 
            if (data.rpt == "R20") {
                dataOther = data.meetingType;
            }
            else 
                if (data.rpt == "R22") {
                    dataOther = data.branchType;
                }
                else {
                    dataOther = "";
                }
        content = data.rpt + "^^" + data.custId + "^" + data.dupNo + "^^";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                otherData: dataOther,
                fileDownloadName: "LMS1205R18.pdf",
                serviceName: "lms1205r01rptservice"
            }
        });
    }
    else {
        CommonAPI.showMessage(i18n.def['grid.selrow']);
    }
}

// ================== 以下為共用借款人選擇程式================
/**
 *  開啟借款人搜尋畫面供使用者挑選
 */
function cesSelect(i18nTitle, implMethod){
    $("#cesSelect").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lmsm01["commonweb.thickbox1"],
        width: 350,
        height: 200,
        modal: true,
        valign: "bottom",
        align: "center",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $("#formCesSelect .cesGridSel").hide();
                var value = $("#formCesSelect input[name='radioSel']:checked").val();
                if (value == 1) {
                    $("#formCesSelect").find("#hCesGridSel1").show();
                    openCesDbu(value, "", i18nTitle, implMethod);
                }
                else 
                    if (value == 2) {
                        $("#formCesSelect").find("#hCesGridSel2").show();
                        var otherCust = $("#formCesSelect").find("#otherCust").val();
                        if (otherCust != "") {
                            openCesDbu(value, otherCust, i18nTitle, implMethod);
                        }
                        else {
                            //尚未輸入統編
                            CommonAPI.showMessage(i18n.lmsm01["commonweb.alert1"]);
                        }
                    }
                    else {
                        $("#formCesSelect").find("#hCesGridSel3").show();
                        openCesDbu(value, "", i18nTitle, implMethod);
                    }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 *  依照使用者所選選項開啟ThickBox
 *  以供使用者選擇開始進行進一步查詢
 */
function openCesDbu(value, custId, i18nTitle, implMethod){
    //初始化Grid選項(將Grid選項清空)
    $("#gridSelCes1").resetSelection();
    $("#gridSelCes2").resetSelection();
    $("#gridSelCes3").resetSelection();
    if (value == 1) {
        gridUptCes1(custId);
    }
    else 
        if (value == 2) {
            gridUptCes2(custId);
        }
        else {
            gridUptCes3();
        }
    $("#cesGridThick").thickbox({ // 使用選取的內容進行彈窗
        title: i18nTitle,
        width: 640,
        height: 400,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                var $formCesSelect = $("#formCesSelect");
                //取得使用者選擇依借款人Grid資料
                var rows1 = $("#gridSelCes1").getGridParam('selrow');
                var list1 = "";
                var sign1 = ",";
                if (rows1 != 'undefined' && rows1 != null && rows1 != 0) {
                    var data1 = $("#gridSelCes1").getRowData(rows1);
                    list1 = data1.mainId;
                }
                /*
                 for (var i=0;i<rows1.length;i++){	//將所有已選擇的資料存進變數list裡面
                 if (rows1[i] != 'undefined' && rows1[i] != null && rows1[i] != 0){
                 var data1 = $("#gridSelCes1").getRowData(rows1[i]);
                 list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
                 }
                 }
                 */
                //取得使用者選擇依統編Grid資料
                var rows2 = $("#gridSelCes2").getGridParam('selrow');
                var list2 = "";
                var sign2 = ",";
                if (rows2 != 'undefined' && rows2 != null && rows2 != 0) {
                    var data2 = $("#gridSelCes2").getRowData(rows2);
                    list2 = data2.mainId;
                }
                /*
                 for (var j=0;j<rows2.length;j++){	//將所有已選擇的資料存進變數list裡面
                 if (rows2[j] != 'undefined' && rows2[j] != null && rows2[j] != 0){
                 var data2 = $("#gridSelCes2").getRowData(rows2[j]);
                 list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
                 }
                 }
                 */
                //取得使用者選擇所有徵信Grid資料
                var rows3 = $("#gridSelCes3").getGridParam('selrow');
                var list3 = "";
                var sign3 = ",";
                if (rows3 != 'undefined' && rows3 != null && rows3 != 0) {
                    var data3 = $("#gridSelCes3").getRowData(rows3);
                    list3 = data3.mainId;
                }
                /*
                 for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
                 if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
                 var data3 = $("#gridSelCes3").getRowData(rows3[k]);
                 list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
                 }
                 }
                 */
                if (list1 != "" || list2 != "" || list3 != "") {
                    //如果三個Grid資料其中一筆有被選擇
                    var list = "";
                    var data = "";
                    //取得確切選擇的那筆資料文件編號(MainId)
                    if (list1 != "") {
                        list = list1;
                        data = data1;
                    }
                    else 
                        if (list2 != "") {
                            list = list2;
                            data = data2;
                        }
                        else {
                            list = list3;
                            data = data3;
                        }
                    
                    // 開始將欲呼叫的方法當作參數傳入另一方法並呼叫
                    startImpl(implMethod, list);
                    $.thickbox.close();
                    $.thickbox.close();
                }
                else {
                    //並未選擇任何資料
                    CommonAPI.showMessage(i18n.lmsm01["commonweb.alert1"]);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

/**
 *  更新依主要借款人Grid
 */
function gridUptCes1(custId){
    $("#gridSelCes1").jqGrid("setGridParam", {
        postData: {
            formAction: "queryCesMainIds2",
            custId: custId,
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 *  更新依特定統編Grid
 */
function gridUptCes2(custId){
    $("#gridSelCes2").jqGrid("setGridParam", {
        postData: {
            formAction: "queryCesMainIds",
            custId: custId,
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 *  更新顯示所有徵信報告Grid
 */
function gridUptCes3(){
    $("#gridSelCes3").jqGrid("setGridParam", {
        postData: {
            formAction: "queryCesMainIdss2",
            rowNum: 10
        },
        search: true
    }).trigger("reloadGrid");
}

/**
 *  依主要借款人Grid
 */
function gridSelCes1(custId){
    var gridSelCes1 = $("#gridSelCes1").iGrid({
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'createTime',
        postData: {
            formAction: "queryCesMainIds2",
            custId: custId,
            rowNum: 10
        },
        caption: "&nbsp;",
        hiddengrid: false,
        rownumbers: true,
        rowNum: 10,
        /*
         multiselect: true,
         hideMultiselect:false,
         */
        colModel: [{
            colHeader: i18n.lmsm01["commonweb.grid6"], // 建立日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid2"], // 核准日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'approveTime' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid3"], // 文件狀態
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid1"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
}

/**
 *  依特定統編Grid
 */
function gridSelCes2(custId){
    var gridSelCes2 = $("#gridSelCes2").iGrid({
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'createTime',
        postData: {
            formAction: "queryCesMainIds",
            custId: custId,
            rowNum: 10
        },
        caption: "&nbsp;",
        hiddengrid: false,
        rownumbers: true,
        rowNum: 10,
        /*
         multiselect: true,
         hideMultiselect:false,
         */
        colModel: [{
            colHeader: i18n.lmsm01["commonweb.grid6"], // 建立日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'createTime' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid2"], // 核准日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'approveTime' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid3"], // 文件狀態
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid1"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){
            var data = gridCesDbu.getRowData(rowid);
        }
    });
}

/**
 *  顯示所有徵信報告Grid
 */
function gridSelCes3(custId){
    var gridSelCes3 = $("#gridSelCes3").iGrid({
        handler: 'lms1205gridhandler',
        height: 175,
        sortname: 'custName',
        postData: {
            formAction: "queryCesMainIdss2",
            rowNum: 10
        },
        caption: "&nbsp;",
        hiddengrid: false,
        rownumbers: true,
        rowNum: 10,
        /*
         multiselect: true,
         hideMultiselect:false,
         */
        colModel: [{
            colHeader: i18n.lmsm01["commonweb.grid1"], // 主要借款人
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid2"], // 核准日期
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'approveTime' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid3"], // 文件狀態
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'docStatus' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid4"], // 徵信報告編號
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'cesId' // col.id
        }, {
            colHeader: i18n.lmsm01["commonweb.grid5"], // 文件建立者
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'creator' // col.id
        }, {
            colHeader: "mainId",
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
        }
    });
}

/**
 *  將欲呼叫的方法當作參數傳入另一方法並呼叫
 */
function startImpl(fn, arg){
    if (arg != undefined && arg != null && arg != '') {
        fn(arg);
    }
    else {
        fn();
    }
}

// ================== 以上為共用借款人選擇程式================


//取得婉卻控管種類確認訊息
function getRejtMsgData(){
  var result = null;
  $.ajax({
      type: "POST",
      handler: "lms1205formhandler",
      async: false,
      data: {
          formAction: "getRejtMsgData",
          mainid: responseJSON.mainId
      }
  }).done(function(responseData){
          var rejtMsgData = responseData.rejtMsgData;
          if (rejtMsgData != undefined && rejtMsgData != null && rejtMsgData != "") {
              result = rejtMsgData;
          }
  });
  return result;
}

function rejtMsgConfirmAtCheckSend(json_rejtResult){
	var my_dfd = $.Deferred();

	if (json_rejtResult != undefined && json_rejtResult != null && json_rejtResult != '') {
		CommonAPI.confirmMessage(json_rejtResult, function(b){
	        if (b) {
	        	my_dfd.resolve();
	        }else{
	        	my_dfd.reject();
	        }
	    });	
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function rejtMsgWithFlow(flowvalue){
	var my_dfd = $.Deferred();

	var rejtResult = getRejtMsgData();
	if (rejtResult != null && !(flowvalue=="1" || flowvalue=="6" || flowvalue=="7" )) {
		CommonAPI.confirmMessage(rejtResult, function(b){
	        if (b) {
	        	my_dfd.resolve();
	        }else{
	        	my_dfd.reject();
	        }
	    });	
	}else{
		my_dfd.resolve();
	}
	return my_dfd.promise();
}

function dfd_askBackRatingDoc(){
	$.thickbox.close();
	//~~~~~~~~~~~~~~
	var my_dfd = $.Deferred();    	
	$.ajax({type: "POST", handler: "lms1205formhandler",
		data: { formAction: "askBackRatingDoc", mainid: responseJSON.mainId}
	}).done(function(json){
	    	if(json.needAsk=="Y"){
	    		var c121Doc = json.c121Doc||'';
	    		
	    		$("#divOverSeaCLSRatingDocBackTo01O_content").html( i18n.lmsm01['msg_backRatingDoc'].replace("{0}", c121Doc) );
	    		
	    		var btnArr = {};
	    		btnArr['btn_backRatingDoc_N'] = function(){
	    				my_dfd.resolve({'backRatingDoc':'N'});  
	    				//~~~
	    				$.thickbox.close();
	    		};
	    		btnArr['btn_backRatingDoc_Y'] = function(){
    				my_dfd.resolve({'backRatingDoc':'Y'});  
    				//~~~
    				$.thickbox.close();
	    		};
    		
	    		$("#divOverSeaCLSRatingDocBackTo01O").thickbox({
                    title: '', width: 580, height: 160, modal: true, valign: "bottom", align: "center",
                    i18n: i18n.lmsm01,
                    readOnly: false,
                    buttons: btnArr
                });
        	  
	        }else{
	        	my_dfd.resolve();
	        }
	});	    		
	return my_dfd.promise();
}

//J-110-0493_11557_B1001 檢查利害關係人授信額度合計是否達新台幣1億元以上
function rtlOver100MillionBox(afterAction, responseData){
	$.ajax({
		type : "POST",
		handler : "lms1105formhandler",
		data : {
			formAction : "findL120m01iRtl",
			mainId : responseJSON.mainId
		}
	}).done(function(obj) {
			var rtlOver100MillionForm_thickbox = $("#lmsm06_panel_for_thickbox").find(".rtlOver100MillionBox");
			rtlOver100MillionForm_thickbox.attr("id","rtlOver100MillionBox_for_thickbox");
			var rtlOver100MillionForm = $("#lmsm06_panel_for_thickbox").find('#rtlOver100MillionForm');
			// inject資料進thickbox
			rtlOver100MillionForm.injectData(obj);
			rtlOver100MillionForm.find('input:checkbox[name="excludeRltLimRsn"][value="4"]').trigger("change");// 欄位開關重畫
			rtlOver100MillionForm_thickbox.thickbox({
				title: tempKey['l120m01i.excludeRtlTitle'],
				width: 600,
				height: 400,
				modal: true,
				valign: "bottom",
				readOnly: false,
				align: "center",
				i18n: i18n.def,
				buttons: {
					"sure": function(){
						if (!rtlOver100MillionForm.valid()) {
							//common.001=欄位檢核未完成，請填妥後再送出
                            API.showMessage(i18n.def["common.001"]);
							return false;
						}
						var rtlFormData = rtlOver100MillionForm.serializeData();
						$.ajax({
							type : "POST",
							handler : "lms1105formhandler",
							data : {
								formAction : "saveL120m01iRtl",
								mainId : responseJSON.mainId,
								excludeRltLimRsn : rtlFormData.excludeRltLimRsn,
								excludeRltLimOther : rtlFormData.excludeRltLimOther
							}
						}).done(function(obj) {
								$.thickbox.close();
								afterAction(responseData);
						});
					},
					"cancel": function(){
						API.confirmMessage(i18n.def['flow.exit'], function(res){
							if (res) {
								$.thickbox.close();
								return false;
							}
						});
					}
				},
				 close: function(){
					 rtlOver100MillionForm.reset();
                 }
			});
	});
}

function choiceOthBrNo(){
	
	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getSonBrNo",
            momBrNo: userInfo.unitNo
        }
	}).done(function(br){
			$("#selectBrNo").setItems({
                item: br.sonBrNoList,
                space: false,
                format: "{key}"
            });
			
			$("#selectBrNoBox").thickbox({ // 使用選取的內容進行彈窗
		        //val.inelbranch=請選分行
		        title: i18n.def['val.inelbranch'],
		        width: 100,
		        height: 100,
		        modal: true,
		        valign: "bottom",
		        align: "center",
		        i18n: i18n.def,
		        readOnly: false,
		        buttons: {
		            "sure": function(){
		            
		                $.ajax({
					        handler: _handler,
					        type: "POST",
					        dataType: "json",
					        data: {
					            formAction: "choiceOthBrNo",
					            choiceBrNo: $("#selectBrNo option:selected").val()
					        }
						}).done(function(obj){
								 $("#AOPerson").setItems({
					                item: obj.bossList,
					                space: true,
					                format: "{key}"
					            });
					    });
		                $.thickbox.close();
		            },
		            
		            "cancel": function(){
		                $.thickbox.close();
		            }
		        }
		    });
			
    });
	
	
	
    
}

