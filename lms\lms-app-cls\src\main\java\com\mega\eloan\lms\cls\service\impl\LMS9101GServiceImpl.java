/* 
 * LMS0005ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.lms.cls.report.CLS1141R01RptService;
import com.mega.eloan.lms.lms.service.LMS9101GService;

/**
 * <pre>
 * 近期已收
 * </pre>
 * 
 * @since 2012/1/13
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/13,REX,new
 *          </ul>
 */

@Service
public class LMS9101GServiceImpl implements LMS9101GService {

	@Resource
	CLS1141R01RptService cls1141r01rptservice;

	/**
	 * 提供base可以呼叫lns、cls的列印程式
	 */
	@Override
	public OutputStream generateReport(PageParameters params)
			throws FileNotFoundException, IOException, Exception {
		return cls1141r01rptservice.generateReport(params);
	}

}
