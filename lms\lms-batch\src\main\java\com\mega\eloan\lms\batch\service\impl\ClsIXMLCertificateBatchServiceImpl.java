package com.mega.eloan.lms.batch.service.impl;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.base.service.CLSService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

// 此Service未受JPA控管，不具Transaction功能，故把邏輯寫至CLSService
@Service("clsIXMLCertificateBatchServiceImpl")
public class ClsIXMLCertificateBatchServiceImpl extends AbstractCapService implements WebBatchService {
	@Resource
	private CLSService clsService;

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		JSONObject request = json.getJSONObject("request");
		String act = request.optString("act");
		String msg = "";
		try {
			if (Util.equals("sendToFTPServer", act)) {
				result = clsService.uploadFinAgreementToFTPServer(request, result);

			} else if (Util.equals("receiveFromFTPServer", act)) {
				result = clsService.processJCICresponseFromFTPServer(result);

			} else {
				throw new CapException("unknown_act[" + act + "]", getClass());
			}

		} catch (Exception e) {
			msg = e.getMessage();
			logger.error(msg, e);
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
		}

		return result;
	}
}
