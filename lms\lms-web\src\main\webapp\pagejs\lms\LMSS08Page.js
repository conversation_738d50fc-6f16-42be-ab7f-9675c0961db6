var _iHandler = "";
var _handler = "";
$(document).ready(function() {
	//2012_07_20_rex add 取得sso 連線資訊 
	BrowserAction.init();
	setCloseConfirm(true);
	if(responseJSON.docURL == "/lms/lms1205m01"){
		// 授權外企金
		_handler = "lms1205formhandler";
		_iHandler = "lms1205formhandler";
	}else if(responseJSON.docURL == "/lms/lms1105m01"){
		//授權內企金
		_handler = "lms1105formhandler";
		_iHandler = "lms1105formhandler";
	}else if(responseJSON.docURL == "/lms/lms1215m01"){
		_handler = "lms1215formhandler";
		_iHandler = "lms1215formhandler";
	}else if(responseJSON.docURL == "/lms/lms1115m01"){
		_handler = "lms1115formhandler";
		_iHandler = "lms1115formhandler";
	}else{
		_handler = "lms1305formhandler";
		_iHandler = "lms1305formhandler";
	}
	gridCesDbu1("");
	gridCesDbu2("");
	gridCesDbu3("");	
	$("#tformL120m01e1").find("input[name='radioKind1']").click(function(i) {
		$("#tformL120m01e1").find("#other").hide();
		if ($(this).val() == "2") {
			$("#tformL120m01e1").find("#other").show();
		} else {
			$("#tformL120m01e1").find("#other").hide();
		}
	});
	
	var gridCesDbu4 = $("#gridCesDbu4").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 175,
		needPager: false,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIda"
			//rowNum:5
		},
		rownumbers:true,
		//rowNum:5,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
	
	//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
	var gridCesDbu4_1 = $("#gridCesDbu4_1").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 175,
		needPager: false,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIdd"
			//rowNum:5
		},
		//rownumbers:true,
		//rowNum:5,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
	
	
	var gridCesDbu5 = $("#gridCesDbu5").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 175,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIdb",
			rowNum:5
		},
		rownumbers:true,
		rowNum:5,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
	
	var gridCesDbu6 = $("#gridCesDbu6").iGrid({		//借款人基本資料GridView
		handler : 'lms1205gridhandler',
		height : 175,
		multiselect: true,
		hideMultiselect:false,
		caption: "&nbsp;",
		hiddengrid : false,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIdc",
			rowNum:5
		},
		rownumbers:true,
		rowNum:5,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
	
	//J-112-0586_05097_B1001 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
	$("#docDscr1 a").live( "click", function() {
		//徵信報告書
	    getMain(DOMPurify.sanitize(this.title));
	});
	
	$("#docDscr2 a").live( "click", function() {
		//資信簡表
	    getCes(DOMPurify.sanitize(this.title));
	});
	
	
});

function clearContent(docType){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "clearContent",
			mainId : responseJSON.mainId,
			docType : docType
		},
		success : function(json) {
/*
			if(docType == "1"){
				$("#docDscr1").html("");
			}else if(docType == "2"){
				$("#docDscr2").html("");
			}
*/
			if(docType == "1"){
				$("#docDscr1").html("");
				$("#docDscr1").val("");	
			}else if(docType == "2"){
				$("#docDscr2").html("");
				$("#docDscr2").val("");		
			}
		}
	});	
}

function getMain(ces){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "getRelate1",
			mainId : responseJSON.mainId,
			cesMainId : ces
		},
		success : function(json) {
			var userLocale = false;
			if(json.gaapFlag == "1" || json.typCd == "1" || json.typCd == "4"){
				userLocale = "zh_TW";
			}
			
/*
			// 測試用
			userInfo.lightId = 'ISEhfDAwMzk2NnxFTHwxM2FiZTJkMjg0OGNkM2JkYjlhNjM5NDNmMGI3N2U4OHwxMzM3ODU1MzU0NTE3';
			userInfo.unitNo = '005';
			// 徵信報告
			BrowserAction.submit({
				system : "ces",
				url    : "../ces/ces1405m01/02",
				mainId : "15add5aa51424f44a4481aa50b35fe6a",
				txCode : "231184",
				data   : { //其它參數
					fromView: true,
					uid : "9c17d1a4521d44f08eb0fcfdbddd5a67",
					oid : "4C0805E5E2914FF7BD159DA5460D054E",
					mainOid : "4C0805E5E2914FF7BD159DA5460D054E"
				}
			});			
			return;
*/
			// 徵信報告
			BrowserAction.submit({
				system : "ces",
				url    : json.url,
				mainId : json.mainId,
				mainOid : json.mainOid,
				txCode : json.txCode,
				forceChangeLanguage: "Y",
				gaapFlag : json.gaapFlag,
				userLocale :userLocale,
				data   : { //其它參數
					fromView: true,
					uid : json.uid,
					mainDocStatus : json.mainDocStatus,
					oid : json.mainOid,
					mainOid : json.mainOid
				}
			});	
		}
	});	
}

function getCes(ces){
	$.ajax({ // 查詢主要借款人資料
		handler : _handler,
		type : "POST",
		dataType : "json",
		data : {
			formAction : "getRelate2",
			mainId : responseJSON.mainId,
			cesMainId : ces
		},
		success : function(json) {
			var userLocale = false;
			if(json.gaapFlag == "1" || json.typCd == "1" || json.typCd == "4"){
				userLocale = "zh_TW";
			}
			
			// 資信簡表
			BrowserAction.submit({
				system : "ces",
				url    : json.url,
				mainId : json.mainId,
				mainOid : json.mainOid,
				txCode : json.txCode,
				forceChangeLanguage: "Y",
				gaapFlag : json.gaapFlag,
				userLocale :userLocale,
				data   : { //其它參數
					uid : json.uid,
					mainDocStatus : json.mainDocStatus,
					oid : json.mainOid,
					mainOid : json.mainOid
				}
			});			
		}
	});	
}
/*
$("#selectdanbow").one("click", function() {
	griddanbow2();
});

$("#showdanbow").click(function() {
	$("#hidedanbowtr").show();
	$("#hidedanbow2").show();
});
*/

function cesGridDbu1(custId) {
	$("#gridCesDbu1").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds2",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu2(custId) {
	$("#gridCesDbu2").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu3() {
	$("#gridCesDbu3").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu4(custId) {
	$("#gridCesDbu4").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIda",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
function cesGridDbu4_1() {
	$("#gridCesDbu4_1").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdd"
			//custId : custId
			//rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu5(custId) {
	$("#gridCesDbu5").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdb",
			custId : custId,
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function cesGridDbu6() {
	$("#gridCesDbu6").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIdc",
			rowNum : 10
		},
		search : true
	}).trigger("reloadGrid");
}

function gridCesDbu1(custId) {
	var gridCesDbu1 = $("#gridCesDbu1").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		needPager: false,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds2",
			custId : custId
			//rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		//rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], // 建立日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'createTime' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
}

function gridCesDbu2(custId) {
	var gridCesDbu2 = $("#gridCesDbu2").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryCesMainIds",
			custId : custId,
			rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid13"], // 建立日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'createTime' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) {
			var data = gridCesDbu.getRowData(rowid);
		}
	});
}

function gridCesDbu3(custId) {
	var gridCesDbu3 = $("#gridCesDbu3").iGrid({
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'custName',
		postData : {
			formAction : "queryCesMainIdss2",
			rowNum : 10
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers : true,
		rowNum : 10,
		multiselect: true,
		hideMultiselect:false,
		colModel : [ {
			colHeader : i18n.lmss08["L120S08.grid12"], // 主要借款人
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'custName' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid15"], // 核准日期
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'approveTime' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid14"], // 文件狀態
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			// formatter : 'click',
			// onclick : function,
			name : 'docStatus' // col.id
		}, {
			colHeader : i18n.lmss08["ces1405.0203"], // 徵信報告編號
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'cesId' // col.id
		}, {
			colHeader : i18n.lmss08["L120S08.grid16"], // 文件建立者
			align : "left",
			width : 100, // 設定寬度
			sortable : true, // 是否允許排序
			name : 'creator' // col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		} ],
		ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
}
//徵信報告
function openCesDbu(value, custId) {
	//初始化Grid選項(將Grid選項清空)
	$("#gridCesDbu1").resetSelection();
	$("#gridCesDbu2").resetSelection();
	$("#gridCesDbu3").resetSelection();
	if (value == 1) {
		cesGridDbu1(custId);
	} else if (value == 2) {
		cesGridDbu2(custId);
	} else {
		cesGridDbu3();
	}
	$("#openCesDbu").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss08["L120S08.thickbox6"],
		width : 640,
		height : 400,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.lmss08,
		buttons : {
			"L120S08.thickbox1" : function() {
				var $formL120m01e = $("#formL120m01e");
				//取得使用者選擇依借款人Grid資料
				var rows1 = $("#gridCesDbu1").getGridParam('selarrrow');
				var list1 = "";
				var sign1 = ",";
				for (var i=0;i<rows1.length;i++){	//將所有已選擇的資料存進變數list裡面
					if (rows1[i] != 'undefined' && rows1[i] != null && rows1[i] != 0){
						var data1 = $("#gridCesDbu1").getRowData(rows1[i]);
						if(data1.docStatus == i18n.abstracteloan["docStatus.230"]){
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
						}else{
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data1.custName,
								  'ceskind' : i18n.lmss08["L120S08.index4"]
						    }));
							// return;
						}						
					}
				}								
				
				//取得使用者選擇依統編Grid資料
				var rows2 = $("#gridCesDbu2").getGridParam('selarrrow');
				var list2 = "";
				var sign2 = ",";
				for (var j=0;j<rows2.length;j++){	//將所有已選擇的資料存進變數list裡面
					if (rows2[j] != 'undefined' && rows2[j] != null && rows2[j] != 0){
						var data2 = $("#gridCesDbu2").getRowData(rows2[j]);
						if(data2.docStatus == i18n.abstracteloan["docStatus.230"]){
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
						}else{
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data2.custName,
								  'ceskind' : i18n.lmss08["L120S08.index4"]
						    }));
							 //return;
						}						
					}
				}				

				//取得使用者選擇所有徵信Grid資料
				var rows3 = $("#gridCesDbu3").getGridParam('selarrrow');
				var list3 = "";
				var sign3 = ",";
				for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
						var data3 = $("#gridCesDbu3").getRowData(rows3[k]);
						if(data3.docStatus == i18n.abstracteloan["docStatus.230"]){
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
						}else{
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data3.custName,
								  'ceskind' : i18n.lmss08["L120S08.index4"]
						    }));
							 //return;
						}						
					}
				}

				if (list1 != "" || list2 != "" || list3 != "") {
					//如果三個Grid資料其中一筆有被選擇
					var list = "";
					var data = "";
					//取得確切選擇的那筆資料文件編號(MainId)
					if (list1 != "") {
						list = list1;
						data = data1;
					} else if (list2 != "") {
						list = list2;
						data = data2;
					} else {
						list = list3;
						data = data3;
					} 
					$.ajax({ // 查詢主要借款人資料
						handler : _handler,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findRelate1",
							mainId : responseJSON.mainId,
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							cesMainId : list
						},
						success : function(json) {
							//alert(JSON.stringify(json));
							$("#docDscr1").html(DOMPurify.sanitize(json.docDscr1));
							if(json.docDscr1 != "" && json.docDscr1 != undefined && json.docDscr1 != null){
								$("#docDscr1 a").attr({"href":"#"});	
							}							
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage(i18n.lmss08["L120S08.alert1"]);
				}				
			},
			"L120S08.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

//資信簡表
function openCesDbu2(value, custId) {
	//初始化Grid選項(將Grid選項清空)
	$("#gridCesDbu4").resetSelection();
	$("#gridCesDbu5").resetSelection();
	$("#gridCesDbu6").resetSelection();
	
	//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
	$("#gridCesDbu4_1").resetSelection();

	if (value == 1) {
		cesGridDbu4(custId);
	} else if (value == 2) {
		cesGridDbu5(custId);
	} else if (value == 4) {
		//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
		cesGridDbu4_1();	
	} else {
		cesGridDbu6();
	}
	
	$("#openCesDbu2").thickbox({ // 使用選取的內容進行彈窗
		title : i18n.lmss08["L120S08.thickbox6"],
		width : 640,
		height : 400,
		modal : true,
		align : "center",
		valign : "bottom",
		i18n : i18n.lmss08,
		buttons : {
			"L120S08.thickbox1" : function() {
				var $formL120m01e = $("#formL120m01e");
				//取得使用者選擇依借款人Grid資料
				var rows1 = $("#gridCesDbu4").getGridParam('selarrrow');
				var list1 = "";
				var sign1 = ",";
				for (var i=0;i<rows1.length;i++){	//將所有已選擇的資料存進變數list裡面
					if (rows1[i] != 'undefined' && rows1[i] != null && rows1[i] != 0){
						var data1 = $("#gridCesDbu4").getRowData(rows1[i]);
						if(data1.docStatus == i18n.abstracteloan["docStatus.230"]){
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
						}else{
							list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data1.custName,
								  'ceskind' : i18n.lmss08["L120S08.index5"]
						    }));
							 //return;
						}						
						//list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
					}
				}			
				
				//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
				//取得使用者選擇依借款人Grid資料
				var rows4 = $("#gridCesDbu4_1").getGridParam('selarrrow');
				var list4 = "";
				var sign4 = ",";
				for (var i=0;i<rows4.length;i++){	//將所有已選擇的資料存進變數list裡面
					if (rows4[i] != 'undefined' && rows4[i] != null && rows4[i] != 0){
						var data4 = $("#gridCesDbu4_1").getRowData(rows4[i]);
						if(data4.docStatus == i18n.abstracteloan["docStatus.230"]){
							list4 += ((list4 == "") ? "" : sign4 ) + data4.mainId;
						}else{
							list4 += ((list4 == "") ? "" : sign4 ) + data4.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data4.custName,
								  'ceskind' : i18n.lmss08["L120S08.index5"]
						    }));
							 //return;
						}						
						//list1 += ((list1 == "") ? "" : sign1 ) + data1.mainId;
					}
				}			
				
				//取得使用者選擇依統編Grid資料
				var rows2 = $("#gridCesDbu5").getGridParam('selarrrow');
				var list2 = "";
				var sign2 = ",";
				for (var j=0;j<rows2.length;j++){	//將所有已選擇的資料存進變數list裡面
					if (rows2[j] != 'undefined' && rows2[j] != null && rows2[j] != 0){
						var data2 = $("#gridCesDbu5").getRowData(rows2[j]);
						if(data2.docStatus == i18n.abstracteloan["docStatus.230"]){
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
						}else{
							list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data2.custName,
								  'ceskind' : i18n.lmss08["L120S08.index5"]
						    }));
							 //return;
						}						
						//list2 += ((list2 == "") ? "" : sign2 ) + data2.mainId;
					}
				}				

				//取得使用者選擇所有徵信Grid資料
				var rows3 = $("#gridCesDbu6").getGridParam('selarrrow');
				var list3 = "";
				var sign3 = ",";
				for (var k=0;k<rows3.length;k++){	//將所有已選擇的資料存進變數list裡面
					if (rows3[k] != 'undefined' && rows3[k] != null && rows3[k] != 0){
						var data3 = $("#gridCesDbu6").getRowData(rows3[k]);
						if(data3.docStatus == i18n.abstracteloan["docStatus.230"]){
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
						}else{
							list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
							 CommonAPI.showErrorMessage(i18n.lmss08('L120S08.alert3', {
						          'custname': data3.custName,
								  'ceskind' : i18n.lmss08["L120S08.index5"]
						    }));
							 return;
						}						
						//list3 += ((list3 == "") ? "" : sign3 ) + data3.mainId;
					}
				}

				//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
				if (list1 != "" || list2 != "" || list3 != "" || list4 != "") {
					//如果三個Grid資料其中一筆有被選擇
					var list = "";
					var data = "";
					//取得確切選擇的那筆資料文件編號(MainId)
					if (list1 != "") {
						list = list1;
						data = data1;
					} else if (list2 != "") {
						list = list2;
						data = data2;
					} else if (list4 != "") {
						//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
						list = list4;
						data = data4;	
					} else {
						list = list3;
						data = data3;
					} 
					$.ajax({ // 查詢主要借款人資料
						handler : _handler,
						type : "POST",
						dataType : "json",
						data : {
							formAction : "findRelate2",
							formL120m01e : JSON.stringify($formL120m01e.serializeData()),
							mainId : responseJSON.mainId,
							cesMainId : list
						},
						success : function(json) {
							//alert(JSON.stringify(json));
							$("#docDscr2").html(DOMPurify.sanitize(json.docDscr2));
							if(json.docDscr2 != "" && json.docDscr2 != undefined && json.docDscr2 != null){
								$("#docDscr2 a").attr({"href":"#"});
							}							
						}
					});
					$.thickbox.close();
					$.thickbox.close();
				}else{
					//並未選擇任何資料
					CommonAPI.showMessage(i18n.lmss08["L120S08.alert1"]);
				}				
			},
			"L120S08.thickbox2" : function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

function seachKind3(){	
	if($("#lmss08a_panel").attr("open") == "true"){
		$("#lmss08a_panel").load("../../lms/lmss08a",function(){
			$("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
				title : i18n.lmss08["L120S08.thickbox11"],
				width : 960,
				height : 480,
				modal : true,
				i18n : i18n.def,
				buttons : {
					"print" : function() {
						printA41();
					},			
					"close" : function() {
						 API.confirmMessage(i18n.def['flow.exit'], function(res){
								if(res){
									$.thickbox.close();
								}
					        });
					}
				}
			});			
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)			
            if (responseJSON.readOnly == "true") {
                $("#tabForm_1").readOnlyChilds(true);
                $("#s41Form").find("button").hide();
				$("#tabForm_1").find("button").hide();
            }		
		});
		$("#lmss08a_panel").attr("open",false);
	}else{
		$("#thickboxPeo").thickbox({ // 使用選取的內容進行彈窗
			title : i18n.lmss08["L120S08.thickbox11"],
			width : 960,
			height : 480,
			modal : true,
			i18n : i18n.def,
			buttons : {
				"print" : function() {
					printA41();
				},			
				"close" : function() {
					 API.confirmMessage(i18n.def['flow.exit'], function(res){
							if(res){
								$.thickbox.close();
							}
				        });
				}
			}
		});
	}
}

function seachKind4(){
	if($("#lmss08b_panel").attr("open") == "true"){
		$("#lmss08b_panel").load("../../lms/lmss08b",function(){
		 	$.ajax({									
				handler : _handler,
				type : "POST",
				dataType : "json",
				data : 
				{
					formAction : "queryGrp",
					page : "91",
					mainId : responseJSON.mainId
				},	
				success : function(json) {
					var $tabForm08 = $("#tabForm08");
					$tabForm08.reset();
					$tabForm08.setData(json,false);
					if($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1"){
						$tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
					}
					$tabForm08.find("#show_curr5").val("TWD");
					$tabForm08.find("#show_curr5").val("1000");
					$tabForm08.find("#curr5").val("TWD");
					$tabForm08.find("#unit").val("1000");					
					$tabForm08.find("#curr6").val("TWD");
					$tabForm08.find("#unit1").val("1000");
					$tabForm08.find("#curr7").val("TWD");
					$tabForm08.find("#unit2").val("1000");
					
					$tabForm08.find("#grpFinYear").val(json.grpFinYear);
                    $tabForm08.find("#ch9_GFin_SrcDate").val(json.ch9_GFin_SrcDate);
                    $tabForm08.find("input[type='radio'][name='ch9FinInfor1'][value='" + json.ch9FinInfor1 + "']").attr("checked", true);
                    
					
					$("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
						title : i18n.lmss08["L120S08.thickbox10"],
						width : 960,
						height : 480,
						modal : true,
						i18n : i18n.def,
						buttons : {
							"saveData" : function() {
//								$.thickbox.close();
				        	 	$.ajax({									
									handler : _handler,
									type : "POST",
									dataType : "json",
									data : 
									{
										formAction : "save",
										page : "91",
										mainId : responseJSON.mainId,
										toM4 : $("#tabForm08").find("[name='toM4']:radio:checked").val(),
										typem4 : $("#tabForm08").find("#typem4").val(),
										GroupCompanyID1 : $("#tabForm08").find("#GroupCompanyID1").val(),
										GroupCompanyName1 : $("#tabForm08").find("#GroupCompanyName1").val(),
										curr5 : $("#tabForm08").find("#curr5").val(),
										unit : $("#tabForm08").find("#unit").val(),
										grt_IsNgRec : $("#tabForm08").find("#grt_IsNgRec").val(),
										grt_data_src : $("#tabForm08").find("#grt_data_src").val(),
										grt_data_date : $("#tabForm08").find("#grt_data_date").val(),
										curr6 : $("#tabForm08").find("#curr6").val(),
										unit1 : $("#tabForm08").find("#unit1").val(),
										grp_credit_note : $("#tabForm08").find("#grp_credit_note").val(),
										curr7 : $("#tabForm08").find("#curr7").val(),
										unit2 : $("#tabForm08").find("#unit2").val(),
										grp_ov_note : $("#tabForm08").find("#grp_ov_note").val(),
										gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
										gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
										ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                        grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                        ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
										setGrp : true
									},
									success : function(json) {
										$("#formL120m01e").setData(json.formL120m01e,false);
									}
								});
							},"print" : function() {
								CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                                    if (b) {
                                        // 儲存後列印
                                        if ($("#tabForm08").valid()) {
                                            $.ajax({
                                                handler: _handler,
                                                type: "POST",
                                                dataType: "json",
                                                data: {
                                                    formAction: "save",
                                                    page: "91",
                                                    mainId: responseJSON.mainId,
                                                    toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                                    typem4: $("#tabForm08").find("#typem4").val(),
                                                    GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                                    GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                                    curr5: $("#tabForm08").find("#curr5").val(),
                                                    unit: $("#tabForm08").find("#unit").val(),
                                                    grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                                    grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                                    grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                                    curr6: $("#tabForm08").find("#curr6").val(),
                                                    unit1: $("#tabForm08").find("#unit1").val(),
                                                    grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                                    curr7: $("#tabForm08").find("#curr7").val(),
                                                    unit2: $("#tabForm08").find("#unit2").val(),
                                                    grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                                    gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                                    gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                                    ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                                    grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                                    ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
                                                    setGrp: true
                                                },
                                                success: function(json){
                                                    $("#formL120m01e").setData(json.formL120m01e, false);
                                                    printA91();
                                                    printA92();
                                                    printAD4();
                                                }
                                            });
                                        }
                                    }
                                });
							},
							"close" : function() {
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
										if(res){
											$.thickbox.close();
										}
							        });
							}
						}
					});
				}
			});
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)
            if (responseJSON.readOnly == "true") {
                $("#tabForm08").readOnlyChilds(true);
                $("#tabForm08").find("button").hide();
            }			
		});
		$("#lmss08b_panel").attr("open",false);
	}else{
	 	$.ajax({									
			handler : _handler,
			type : "POST",
			dataType : "json",
			data : 
			{
				formAction : "queryGrp",
				page : "91",
				mainId : responseJSON.mainId
			},	
			success : function(json) {
				var $tabForm08 = $("#tabForm08");
				$tabForm08.reset();
				$tabForm08.setData(json,false);
				if($tabForm08.find("input[name=isGroupCompany1]:radio:checked").val() == "1"){
					$tabForm08.find("#isGroupCompany1-1").show().siblings("[id^=isGroupCompany1]").hide();
				}
				$tabForm08.find("#curr6").val("TWD");
				$tabForm08.find("#unit1").val("1000");
				$tabForm08.find("#curr7").val("TWD");
				$tabForm08.find("#unit2").val("1000");
					
				$tabForm08.find("#grpFinYear").val(json.grpFinYear);
                $tabForm08.find("#ch9_GFin_SrcDate").val(json.ch9_GFin_SrcDate);
                $tabForm08.find("input[type='radio'][name='ch9FinInfor1'][value='" + json.ch9FinInfor1 + "']").attr("checked", true);
                    
													
				$("#thickboxGrp").thickbox({ // 使用選取的內容進行彈窗
					title : i18n.lmss08["L120S08.thickbox10"],
					width : 960,
					height : 480,
					modal : true,
					i18n : i18n.def,
					buttons : {
						"saveData" : function() {
							$.thickbox.close();
			        	 	$.ajax({									
								handler : _handler,
								type : "POST",
								dataType : "json",
								data : 
								{
									formAction : "save",
									page : "91",
									mainId : responseJSON.mainId,
									toM4 : $("#tabForm08").find("[name='toM4']:radio:checked").val(),
									typem4 : $("#tabForm08").find("#typem4").val(),
									GroupCompanyID1 : $("#tabForm08").find("#GroupCompanyID1").val(),
									GroupCompanyName1 : $("#tabForm08").find("#GroupCompanyName1").val(),
									curr5 : $("#tabForm08").find("#curr5").val(),
									unit : $("#tabForm08").find("#unit").val(),
									grt_IsNgRec : $("#tabForm08").find("#grt_IsNgRec").val(),
									grt_data_src : $("#tabForm08").find("#grt_data_src").val(),
									grt_data_date : $("#tabForm08").find("#grt_data_date").val(),
									curr6 : $("#tabForm08").find("#curr6").val(),
									unit1 : $("#tabForm08").find("#unit1").val(),
									grp_credit_note : $("#tabForm08").find("#grp_credit_note").val(),
									curr7 : $("#tabForm08").find("#curr7").val(),
									unit2 : $("#tabForm08").find("#unit2").val(),
									grp_ov_note : $("#tabForm08").find("#grp_ov_note").val(),
									gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
									gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
									ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                    grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                    ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
									setGrp : true
								},
								success : function(json) {
									$("#formL120m01e").setData(json.formL120m01e,false);
								}
							});
						},"print" : function() {
							CommonAPI.confirmMessage(i18n.def["saveBeforePrint"], function(b){
                                    if (b) {
                                        // 儲存後列印
                                        if ($("#tabForm08").valid()) {
                                            $.ajax({
                                                handler: _handler,
                                                type: "POST",
                                                dataType: "json",
                                                data: {
                                                    formAction: "save",
                                                    page: "91",
                                                    mainId: responseJSON.mainId,
                                                    toM4: $("#tabForm08").find("[name='toM4']:radio:checked").val(),
                                                    typem4: $("#tabForm08").find("#typem4").val(),
                                                    GroupCompanyID1: $("#tabForm08").find("#GroupCompanyID1").val(),
                                                    GroupCompanyName1: $("#tabForm08").find("#GroupCompanyName1").val(),
                                                    curr5: $("#tabForm08").find("#curr5").val(),
                                                    unit: $("#tabForm08").find("#unit").val(),
                                                    grt_IsNgRec: $("#tabForm08").find("#grt_IsNgRec").val(),
                                                    grt_data_src: $("#tabForm08").find("#grt_data_src").val(),
                                                    grt_data_date: $("#tabForm08").find("#grt_data_date").val(),
                                                    curr6: $("#tabForm08").find("#curr6").val(),
                                                    unit1: $("#tabForm08").find("#unit1").val(),
                                                    grp_credit_note: $("#tabForm08").find("#grp_credit_note").val(),
                                                    curr7: $("#tabForm08").find("#curr7").val(),
                                                    unit2: $("#tabForm08").find("#unit2").val(),
                                                    grp_ov_note: $("#tabForm08").find("#grp_ov_note").val(),
                                                    gcom_SrcDate: $("#tabForm08").find("#gcom_SrcDate").val(),
                                                    gcom_note1: $("#tabForm08").find("#gcom_note1").val(),
                                                    ch9FinInfor1: $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val(),
                                                    grpFinYear: $("#tabForm08").find("#grpFinYear").val(),
                                                    ch9_GFin_SrcDate: $("#tabForm08").find("#ch9_GFin_SrcDate").val(),
                                                    setGrp: true
                                                },
                                                success: function(json){
                                                    $("#formL120m01e").setData(json.formL120m01e, false);
                                                    printA91();
                                                    printA92();
                                                    printAD4();
                                                }
                                            });
                                        }
                                    }
                                });
						},
						"close" : function() {
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
									if(res){
										$.thickbox.close();
									}
						        });
						}
					}
				});
			}
		});
	}
}

function seachKind1() {
	$("#seachKind1").find("#ceskind1").show();
	$("#seachKind1").find("#ceskind2").hide();
	$("#seachKind1")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.lmss08["L120S08.thickbox7"],
						width : 350,
						height : 200,
						modal : true,
						valign : "bottom",
						align : "center",
						i18n : i18n.lmss08,
						buttons : {
							"L120S08.thickbox1" : function() {
								$(function() {
									$("#tformL120m01e0 .cesGrid").hide();
									var value = $(
											"#tformL120m01e1 input[name='radioKind1']:checked")
											.val();
									if (value == 1) {
										$("#tformL120m01e0").find(
												"#hideCesGrid1").show();
										openCesDbu(value, "");
									} else if (value == 2) {
										$("#tformL120m01e0").find(
												"#hideCesGrid2").show();
										var other = $("#tformL120m01e1").find(
												"#other").val();										
										if(other != ""){
											openCesDbu(value, other);
										} else{
											//尚未輸入統編
											CommonAPI.showMessage(i18n.lmss08["L120S08.alert2"]);
										}
									} else {
										$("#tformL120m01e0").find(
												"#hideCesGrid3").show();
										openCesDbu(value, "");
									}
								});
							},
							"L120S08.thickbox2" : function() {
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
										if(res){
											$.thickbox.close();
										}
							        });
							}
						}
					});
}

function seachKind2() {
	$("#seachKind1").find("#ceskind2").show();
	$("#seachKind1").find("#ceskind1").hide();
	$("#seachKind1")
			.thickbox(
					{ // 使用選取的內容進行彈窗
						title : i18n.lmss08["L120S08.thickbox14"],
						width : 350,
						height : 200,
						modal : true,
						valign : "bottom",
						align : "center",
						i18n : i18n.lmss08,
						buttons : {
							"L120S08.thickbox1" : function() {
								$(function() {
									$("#tformL120m01e0a .cesGrid2").hide();
									var value = $(
											"#tformL120m01e1 input[name='radioKind1']:checked")
											.val();
									if (value == 1) {
										$("#tformL120m01e0a").find(
												"#hideCesGrid4").show();
										openCesDbu2(value, "");
									} else if (value == 2) {
										$("#tformL120m01e0a").find(
												"#hideCesGrid5").show();
										var other = $("#tformL120m01e1").find(
												"#other").val();										
										if(other != ""){
											openCesDbu2(value, other);
										} else{
											//尚未輸入統編
											CommonAPI.showMessage(i18n.lmss08["L120S08.alert2"]);
										}
									} else if (value == 4) {
										//J-107-0178_05097_B1001 Web e-loan案件簽報書相關文件之資信簡表增加借保人之資信簡表之勾選(能勾選跨頁之資料)
										    //依借保人 
										
										$("#tformL120m01e0a").find(
												"#hideCesGrid4_1").show();
										
										openCesDbu2(value, "");	
									} else {
										$("#tformL120m01e0a").find(
												"#hideCesGrid6").show();
										openCesDbu2(value, "");
									}
								});
							},
							"L120S08.thickbox2" : function() {
								 API.confirmMessage(i18n.def['flow.exit'], function(res){
										if(res){
											$.thickbox.close();
										}
							        });
							}
						}
					});
}

//連保人(產報表)
function printA41(){
	if($("#s41grid").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var pdfName = "l120r01.pdf";
		var count = 0;
		var content = "";
		content = "R41" + "^" + "";
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
	        	rptOid : content,
				fileDownloadName : pdfName,
				serviceName : "lms1205r01rptservice"
	        }
	    });		
	}
}

//最新集團企業(產報表)
function printA91(){
	if($("#s91t1f1grid").jqGrid('getGridParam','records') <= 0){
		// 報表無資料
		CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
	}else{
		var pdfName = "l120r01.pdf";
		var count = 0;
		var content = "";
		content = "R91" + "^" + "";
		$.form.submit({
	        url: "../../simple/FileProcessingService",
	        target: "_blank",
	        data: {
	        	mainId : responseJSON.mainId,
	        	rptOid : content,
				fileDownloadName : pdfName,
				serviceName : "lms1205r01rptservice"
	        }
	    });		
	}
}

//最新集團企業(產報表)(退票、拒絕往來、逾期催收與呆帳情形)
function printA92(){
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r01.pdf";
        var count = 0;
        var content = "";
        content = "R92" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1205r01rptservice"
            }
        });
    }
}

//最新集團企業財務概況
function printAD4(){

    var $tabForm08 = $("#tabForm08");
    var ch9FinInfor1 = $("#tabForm08").find("input[type='radio'][name='ch9FinInfor1']:checked").val();
    if (ch9FinInfor1 != "1") {
        return;
    }
    if ($("#s91t1f1grid").jqGrid('getGridParam', 'records') <= 0) {
        // 報表無資料
        CommonAPI.showErrorMessage(i18n.msg('EFD0002'));
    }
    else {
        var pdfName = "l120r02.pdf";
        var count = 0;
        var content = "";
        content = "RD4" + "^" + "";
        $.form.submit({
            url: "../../simple/FileProcessingService",
            target: "_blank",
            data: {
                mainId: responseJSON.mainId,
                rptOid: content,
                fileDownloadName: pdfName,
                serviceName: "lms1205r01rptservice"
            }
        });
    }
}