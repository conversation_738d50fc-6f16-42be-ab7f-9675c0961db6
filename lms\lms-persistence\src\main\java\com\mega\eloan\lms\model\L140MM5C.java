/* 
 * L140MM5C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** eLoan電子文件維護作業資訊檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L140MM5C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L140MM5C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 編製單位代號 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="VARCHAR(1)")
	private String dupNo;

	/** 客戶名稱 **/
	@Size(max=120)
	@Column(name="CUSTNAME", length=120, columnDefinition="VARCHAR(120)")
	private String custName;

	/** 徵信 **/
	@Size(max=1)
	@Column(name="CES", length=1, columnDefinition="VARCHAR(1)")
	private String ces;

	/** 擔保品 **/
	@Size(max=1)
	@Column(name="CMS", length=1, columnDefinition="VARCHAR(1)")
	private String cms;

	/** 逾催 **/
	@Size(max=1)
	@Column(name="COL", length=1, columnDefinition="VARCHAR(1)")
	private String col;

	/** 授信 **/
	@Size(max=1)
	@Column(name="LMS", length=1, columnDefinition="VARCHAR(1)")
	private String lms;

	/** 資料建檔 **/
	@Size(max=1)
	@Column(name="RPS", length=1, columnDefinition="VARCHAR(1)")
	private String rps;

	/** 是否刪除 **/
	@Size(max=1)
	@Column(name="ISDELETE", length=1, columnDefinition="VARCHAR(1)")
	private String isDelete;

	/** 不刪除原因 **/
	@Size(max=2)
	@Column(name="REASON", length=2, columnDefinition="VARCHAR(2)")
	private String reason;

	/** 
	 * 資料來源<p/>
	 * 1:系統 2:人工
	 */
	@Size(max=1)
	@Column(name="DATAFROM", length=1, columnDefinition="VARCHAR(1)")
	private String dataFrom;

	/** 徵信系統是否已執行 **/
	@Size(max=1)
	@Column(name="CESDONE", length=1, columnDefinition="VARCHAR(1)")
	private String cesDone;

	/** 擔保品系統是否已執行 **/
	@Size(max=1)
	@Column(name="CMSDONE", length=1, columnDefinition="VARCHAR(1)")
	private String cmsDone;

	/** 逾催系統是否已執行 **/
	@Size(max=1)
	@Column(name="COLDONE", length=1, columnDefinition="VARCHAR(1)")
	private String colDone;

	/** 授信系統是否已執行 **/
	@Size(max=1)
	@Column(name="LMSDONE", length=1, columnDefinition="VARCHAR(1)")
	private String lmsDone;

	/** 資料建檔系統是否已執行 **/
	@Size(max=1)
	@Column(name="RPSDONE", length=1, columnDefinition="VARCHAR(1)")
	private String rpsDone;

	/** 關戶日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CLOSEDATE", columnDefinition="DATE")
	private Date closeDate;

	/** 建立人員 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="VARCHAR(6)")
	private String creator;

	/** 建立時間 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 更新人員 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="VARCHAR(6)")
	private String updater;

	/** 更新時間 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得編製單位代號 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定編製單位代號 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得客戶名稱 **/
	public String getCustName() {
		return this.custName;
	}
	/** 設定客戶名稱 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得徵信 **/
	public String getCes() {
		return this.ces;
	}
	/** 設定徵信 **/
	public void setCes(String value) {
		this.ces = value;
	}

	/** 取得擔保品 **/
	public String getCms() {
		return this.cms;
	}
	/** 設定擔保品 **/
	public void setCms(String value) {
		this.cms = value;
	}

	/** 取得逾催 **/
	public String getCol() {
		return this.col;
	}
	/** 設定逾催 **/
	public void setCol(String value) {
		this.col = value;
	}

	/** 取得授信 **/
	public String getLms() {
		return this.lms;
	}
	/** 設定授信 **/
	public void setLms(String value) {
		this.lms = value;
	}

	/** 取得資料建檔 **/
	public String getRps() {
		return this.rps;
	}
	/** 設定資料建檔 **/
	public void setRps(String value) {
		this.rps = value;
	}

	/** 取得是否刪除 **/
	public String getIsDelete() {
		return this.isDelete;
	}
	/** 設定是否刪除 **/
	public void setIsDelete(String value) {
		this.isDelete = value;
	}

	/** 取得不刪除原因 **/
	public String getReason() {
		return this.reason;
	}
	/** 設定不刪除原因 **/
	public void setReason(String value) {
		this.reason = value;
	}

	/** 
	 * 取得資料來源<p/>
	 * 1:系統 2:人工
	 */
	public String getDataFrom() {
		return this.dataFrom;
	}
	/**
	 *  設定資料來源<p/>
	 *  1:系統 2:人工
	 **/
	public void setDataFrom(String value) {
		this.dataFrom = value;
	}

	/** 取得徵信系統是否已執行 **/
	public String getCesDone() {
		return this.cesDone;
	}
	/** 設定徵信系統是否已執行 **/
	public void setCesDone(String value) {
		this.cesDone = value;
	}

	/** 取得擔保品系統是否已執行 **/
	public String getCmsDone() {
		return this.cmsDone;
	}
	/** 設定擔保品系統是否已執行 **/
	public void setCmsDone(String value) {
		this.cmsDone = value;
	}

	/** 取得逾催系統是否已執行 **/
	public String getColDone() {
		return this.colDone;
	}
	/** 設定逾催系統是否已執行 **/
	public void setColDone(String value) {
		this.colDone = value;
	}

	/** 取得授信系統是否已執行 **/
	public String getLmsDone() {
		return this.lmsDone;
	}
	/** 設定授信系統是否已執行 **/
	public void setLmsDone(String value) {
		this.lmsDone = value;
	}

	/** 取得資料建檔系統是否已執行 **/
	public String getRpsDone() {
		return this.rpsDone;
	}
	/** 設定資料建檔系統是否已執行 **/
	public void setRpsDone(String value) {
		this.rpsDone = value;
	}

	/** 取得關戶日 **/
	public Date getCloseDate() {
		return this.closeDate;
	}
	/** 設定關戶日 **/
	public void setCloseDate(Date value) {
		this.closeDate = value;
	}

	/** 取得建立人員 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立時間 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立時間 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得更新人員 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定更新人員 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得更新時間 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定更新時間 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
