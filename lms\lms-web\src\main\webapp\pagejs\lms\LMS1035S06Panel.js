var initDfd = initDfd || $.Deferred();

initDfd.done(function(json){
	var initControl_lockDoc = json['initControl_lockDoc'];
	
	initAtPage56(json);
	
	$('input[type=radio][name=noAdj]').change(function(){
		var val = this.value;
		
		var debugMsg = "event change";
		if(val=="1"){						
			isS06_show(false, debugMsg);
		}else if(val=="2"){
			isS06_show(true, debugMsg);
		}else{
			isS06_show(true, debugMsg);
		}
		
		var elmArr = ["adjustStatus","adjRating"];
		$.each(elmArr, function(idx, elmId) {
			if( $("#"+elmId).is(":visible") ){
				$("#"+elmId).addClass('required');
			}else{
				$("#"+elmId).removeClass('required');
			}
		});
	});	
	
	
	$('input[type=radio][name=adjustStatus]').change(function(){
		var val = this.value;
		
		if(val=="1"){						
			//調昇
			$("#div_adjustStatus_1").show();
		}else if(val=="2"){
			//調降
			$("#div_adjustStatus_1").hide();
		}		
		
	});	
	
	$('input[type=radio][name=adjustFlag]').change(function(){
		var val = this.value;
		
		$("tr.tr_adjustFlag").hide();
		if(val=="1"){						
			//淨資產
			$("tr.tr_adjustFlag_1").show();
		}else if(val=="2"){
			//職業
			$("tr.tr_adjustFlag_2").show();
		}else if(val=="3"){
			//其它
			$("tr.tr_adjustFlag_3").show();
		}		
		
	});	

	if(initControl_lockDoc){
		
	}else{
		$('#adjustReason').click(function(){
			//初始
			$("#tmp_adjustReason").val( $('#adjustReason').val() );
			
			$("#divEnterAdjustReason").thickbox({
		        title: '', width: 720, height: 280, align: "center", valign: "bottom",
	            modal: true, i18n: i18n.def,
	            buttons: {
	                "cancel": function(){
	                    $.thickbox.close();
	                },
		            "sure": function(){
		            	if( $("#tmp_adjustReasonForm").valid()){
		            		var newval = $("#tmp_adjustReason").val();
			            	
			            	$.ajax({handler : "lms1015m01formhandler",action : 'validateAdjustReason',				
			    				data : {'keyStr':'', 'mowType': 'T', 'adjustReason': newval },
			    				success : function(json) {
			    					procCfmMsg(json.adjRsnFmt_cfmObj).done(function(){
			    		        		alwaysConfirmAdjReason(json.adjRsnFmt_cnt
			    		        				, json.adjRsnFmt_alwaysCfmObj).done(function(){
			    		        			
			    		        			$.thickbox.close();
			    		    	            $('#adjustReason').val( newval );					
			    		        		});
			    		        	});
			    				}
			    			});	
		            	}		            		                	
		            }
	            }
		    });
		});	
	}
	
});