$(function(){
	// 共用借款人引進Grid
	gridSelCes1("");
	gridSelCes2("");
	gridSelCes3("");
			
	gridbox1();	
});

function cesGrid1(){
	   $("#gridbox1").jqGrid("setGridParam", {
		postData : {
			formAction : "queryCesMainIds2",
			rowNum:10
		},
		search: true
	   }).trigger("reloadGrid");
	}

function gridbox1(){	
	if ($("#gridbox1").length > 0) {
		var gridbox1 = $("#gridbox1").iGrid({
				handler : 'lms1205gridhandler',
				height : 175,
				sortname : 'createTime',
				postData : {
					formAction : "queryCesMainIds2",
					rowNum:10
				},
				caption: "&nbsp;",
				hiddengrid : false,
				rownumbers:true,
				rowNum:10,
				//multiselect : true,
				colModel : [ {
					colHeader : i18n.lms1205s05["l120s05.grid26"], //建立日期
					align : "left",
					width : 100, //設定寬度
					sortable : true, //是否允許排序
					name : 'createTime' //col.id
				}, {
					colHeader : i18n.lms1205s05["l120s05.grid28"], //核准日期
					align : "left",
					width : 100, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'approveTime' //col.id
				}, {
					colHeader : i18n.lms1205s05["l120s05.grid27"], //文件狀態
					align : "left",
					width : 100, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'docStatus' //col.id
				}, {
					colHeader : i18n.lms1205s05["l120s05.grid25"], //主要借款人
					align : "left",
					width : 100, //設定寬度
					sortable : true, //是否允許排序
					//formatter : 'click',
					//onclick : function,
					name : 'custName' //col.id
				}, {
					colHeader : "mainId",
					name : 'mainId',
					hidden : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				}],
				ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
				}
		 });
	 }
}
