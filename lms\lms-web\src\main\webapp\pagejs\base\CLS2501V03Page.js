$(function(){
    // 判斷查詢為何種形式
    $("[name=queryData]").click(function(){
        $(".select").hide()
        //J-112-0586_05097_B1002 依據簽會-2023-2192「Web eLoan-Checkmarx弱點改善會議」按季追蹤弱點修正進度
        $("#queryDataTr" + DOMPurify.sanitize($(this).val())).show();
    });
    $("#managerId").change(function(){
        if ($(this).val() == "0") {
            $("#managerNm").show();
        }
        else {
            $("#managerNm").hide();
        }
    });
    // openFilterBox();
    var grid = $("#gridview").iGrid({
        handler: 'cls2501gridhandler',
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryC250M01A",
        postData: {
            docStatus: viewstatus
        },
        rowNum: 15,
        sortname: "custId",
        sortorder: "desc|desc",
        multiselect: true,
        colModel: [{            	
            colHeader: i18n.cls2501v01['C250M01A.custId'],//"借款人統編",
            name: 'custId',
            width: 45,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: i18n.cls2501v01['C250M01A.custName'],//"借款人",
            name: 'custName',
            width: 60,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.cntrNo'],//"額度序號",
            name: 'cntrNo',
            width: 45,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.yyyymm'],//"資料年月",
            name: 'yyyymm',
            width: 35,
            sortable: true
        }, {
            colHeader: i18n.cls2501v01['C250M01A.status'],//"性質",
            name: 'status',
            width: 20,			
            sortable: true,
			formatter: itemFormatter01
        }, {
            colHeader: i18n.cls2501v01['C250M01A.lnflag'],//"疑似代辦案件訊息",
            name:'lnflag', 
            width: 80,
            sortable: true,
			formatter: itemFormatter02
        }, {
            colHeader: i18n.cls2501v01['C250M01A.creator'],//"分行經辦",
            name: 'creator',
            width: 25,
            sortable: true,
            align: "center"
        }, {
                colHeader: i18n.cls2501v01["C250M01A.createTime"], // 建立日期
                align: "left",
                width: 35, // 設定寬度
                sortable: true, // 是否允許排序
                name: 'createTime',
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d H:i'
                }
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'docURL',
            hidden: true
        }],
        ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = $("#gridview").getRowData(rowid);
            openDoc(null, null, data);
        }
    });
	
	function itemFormatter01(cellvalue, otions, rowObject){
		var val = "";
		if(cellvalue!==null && Boolean(cellvalue)){
			val = $.trim(cellvalue);
		}
		
		if (val == 'N') {
            // C250M01A.statusN=新做  
            return i18n.cls2501v01['C250M01A.statusN'];
        }
		else if (val == 'A') {
            // C250M01A.statusA=增額  
            return i18n.cls2501v01['C250M01A.statusA'];
        }
        else {
            return val;
        }
        
    }
	
	function itemFormatter02(cellvalue, otions, rowObject){
		var val = "";
		if(cellvalue!==null && Boolean(cellvalue)){
			val = $.trim(cellvalue);
		}
        if (val == 'A') {
            // C250M01A.lnflagA=撥款後回查有其他金融機構短期內接續撥款情形 (指3個月內) 
            return i18n.cls2501v01['C250M01A.lnflagA'];
        }
		else if (val == 'B') {
            // C250M01A.lnflagB=貸款後不久即延滯情形 (指3個月內) 
            return i18n.cls2501v01['C250M01A.lnflagB'];
        }
		else if (val == 'C') {
            // C250M01A.lnflagC=跨區承作之情形 (對於借款人居住、工作地與案件來源無地源關係之申貸案件) 
            return i18n.cls2501v01['C250M01A.lnflagC'];
        }
		else if (val == 'D') {
            // C250M01A.lnflagD=其他可疑情形 
            return i18n.cls2501v01['C250M01A.lnflagD'];
        }
        else if (val == 'E') {
            // C250M01A.lnflagE=無以上情形 
            return i18n.cls2501v01['C250M01A.lnflagE'];
        }
        else {
            return val;
        }
    }
    
    // 篩選
    function openFilterBox(){

    	var _id = "_div_cls2501v03_filter";
		var _form = _id+"_form";
		 	
		if ($("#"+_id).length == 0){
			var dyna = [];
			dyna.push("<div id='"+_id+"' style='display:none;' >");
			dyna.push("<form id='"+_form+"'>");
			dyna.push("	<table class='tb2' >");
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls2501v01['C250M01A.custId']+"</td><td>"
					+"<input type='text' id='search_custId' name='search_custId'  maxlength='10' class='alphanum' >"					
					+"</td></tr>");
			
			dyna.push("	<tr><td class='hd1' nowrap>"+i18n.cls2501v01['C250M01A.cntrNo']+"</td><td>"
					+"<input type='text' id='search_cntrNo' name='search_cntrNo'  maxlength='12' class='alphanum' >"
					+"</td></tr>");			
			dyna.push(" </table>");
			dyna.push("</form>");
			
			dyna.push("</div>");
			
		    $('body').append(dyna.join(""));		    
		}
		//clear data
		$("#"+_form).reset();
		
		$("#"+_id).thickbox({ // 使用選取的內容進行彈窗
	       title: '篩選',
	       width: 420,
           height: 230,
           align: "center",
           valign: "bottom",
           modal: false,
           i18n: i18n.def,
           buttons: {
               "sure": function(){
            	  if( $("#"+_form).valid() ){ 
	                  $.thickbox.close();
	                  //=============
	                  grid.setGridParam({
	  	                postData: $.extend(
	  	                	{}
	  	                	,$("#"+_form).serializeData()
	  	                ),
	  	                search: true
	                  }).trigger("reloadGrid");
            	  }
               },
               "cancel": function(){
            	   $.thickbox.close();
               }
           }
		});
		
    }
    
    // grid資料篩選
    function filterGrid(sendData){
        $("#gridview").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryC250M01A3",
                docStatus: viewstatus,
                type: $("[name=queryData]:checked").val()
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    }
    
    function openDoc(cellvalue, options, rowObject){
        ilog.debug(rowObject);
        $.form.submit({
            url: '..' + rowObject.docURL + '/01',		// '../lms/cls1021m01/01',
            data: {
                // formAction: "queryC250M01A",
                oid: rowObject.oid,
                mainId: rowObject.mainId,
                mainOid: rowObject.oid,
                mainDocStatus: viewstatus,
                txCode: txCode
            },
            target: rowObject.oid
        });
    }
    
    $("#buttonPanel").find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selarrrow');
        var data = [];
        
        if (rows == "") {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        
        // confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                for (var i in rows) {
                    data.push($("#gridview").getRowData(rows[i]).oid);
                }
                
                $.ajax({
                    handler: "cls2501m01formhandler",
                    data: {
                        formAction: "deleteC250M01A",
                        oids: data
						}
                    }).done(function(obj){
                        $("#gridview").trigger("reloadGrid");
                });
            }
        });
        
    }).end().find("#btnView").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        if (id.length > 1) {
            // C250M01A.error1=此功能不能多選
            CommonAPI.showMessage(i18n.cls2501m01["C250M01A.error1"]);
        }
        else {
            var result = $("#gridview").getRowData(id);
            openDoc(null, null, result);
        }
    }).end().find("#btnFilter").click(function(){
        openFilterBox();
    }).end().find("#btnLogeIN").click(function(){
        openLogeIN();
    }).end().find("#btnDataFix").click(function(){
        var id = $("#gridview").getGridParam('selarrrow');
        if (!id) {
            // action_004=請先選擇需「調閱」之資料列
            return CommonAPI.showMessage(i18n.def["action_004"]);
        }
        var result = $("#gridview").getRowData(id);
        
        openCntrCaseBox(result.oid);
        
    });
});
