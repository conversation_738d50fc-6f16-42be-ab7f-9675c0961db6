/* 
 * LMSS23APanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.panels;

import com.mega.eloan.common.panels.Panel;

/**
 * @since 2019/09
 * <AUTHOR>
 * @version <ul>
 *          <li>2019/09,009301,new
 *          </ul>
 */
public class LMSS23APanel extends Panel {

	public LMSS23APanel(String id) {
		super(id);
	}
	
	public LMSS23APanel(String id, boolean updatePanelName) {
		super(id, updatePanelName);

	}

	/**/
	private static final long serialVersionUID = 1L;

}
