/* 
 * C999M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C999M01A;

/** 個金約據書主檔 **/
public interface C999M01ADao extends IGenericDao<C999M01A> {

	C999M01A findByOid(String oid);

	C999M01A findByMainId(String mainId);

	List<C999M01A> findByDocStatus(String docStatus);
}