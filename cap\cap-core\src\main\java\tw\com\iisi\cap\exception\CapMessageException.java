/*
 * CapMessageException.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.exception;

import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * 設置錯誤訊息
 * extends CapException.
 * </pre>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/12,iristu,new
 *          </ul>
 */
@SuppressWarnings({ "rawtypes", "serial" })
public class CapMessageException extends CapException implements CapShortMessageException {

    /**
     * i18nKey
     */
    String i18nKey;

    /**
     * Instantiates a new cap exception.
     */
    public CapMessageException() {
        super();
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param causeClass
     *            the cause class
     */
    public CapMessageException(Class causeClass) {
        super();
        super.setCauseSource(causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param causeClass
     *            the cause class
     */
    public CapMessageException(String message, Class causeClass) {
        super(message, causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param cause
     *            the throwable
     * @param causeClass
     *            the cause class
     */
    public CapMessageException(Throwable cause, Class causeClass) {
        super(cause, causeClass);
    }

    /**
     * Instantiates a new cap exception.
     * 
     * @param message
     *            the message
     * @param cause
     *            the cause
     * @param causeClass
     *            the cause class
     */
    public CapMessageException(String message, Throwable cause, Class causeClass) {
        super(message, cause, causeClass);
    }

    /*
     * (non-Javadoc)
     * 
     * @see java.lang.Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        return CapString.isEmpty(i18nKey) ? super.getMessage() : i18nKey;
    }

    /**
     * set i18n key
     * 
     * @param i18nKey
     *            the i18n key
     * @return CapMessageException
     */
    public CapMessageException setMessageKey(String i18nKey) {
        this.i18nKey = i18nKey;
        return this;
    }

    /**
     * get i18n key
     * 
     * @return String
     */
    public String getMessageKey() {
        return i18nKey;
    }

}
