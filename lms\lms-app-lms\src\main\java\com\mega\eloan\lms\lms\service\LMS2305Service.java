/* 
 * LMS2305Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.service;

import java.util.List;

import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L230M01A;
import com.mega.eloan.lms.model.L230S01A;

import tw.com.iisi.cap.model.GenericBean;

/**
 * <pre>
 * 簽約未動用授信案件送作業
 * </pre>
 * 
 * @since 2012/1/12
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/12,REX,new
 *          </ul>
 */
public interface LMS2305Service extends AbstractService {

	/**
	 * 刪除多筆簽約未動用授信報送作業
	 * 
	 * @param oids
	 *            oid陣列
	 */
	void deleteL230M01AByOids(String[] oids);

	/**
	 * 啟動Flow
	 * 
	 * @param mainOid
	 *            文件編號
	 */
	void startFlow(String mainOid);

	/**
	 * flow
	 * 
	 * @param mainOid
	 *            文件編號
	 * @param model
	 *            主檔
	 * @param l230s01as
	 *            額度資訊檔
	 * @param setResult
	 *            是否有動作
	 * @param action
	 *            動作
	 * @throws Throwable
	 */
	void flowAction(String mainOid, GenericBean model,
			List<L230S01A> l230s01as, boolean setResult, String action)
			throws Throwable;

	/**
	 * 查詢簽報書主檔
	 * 
	 * @param mainId
	 *            文件編號
	 * @return 簽報書主檔
	 */
	L120M01A findL120M01AByMainId(String mainId);

	/**
	 * 儲存主檔 資訊
	 * 
	 * @param l230m01a
	 *            簽約未動用主檔
	 * @param l230s01a
	 *            額度資訊檔
	 */
	void saveMain(L230M01A l230m01a, List<L230S01A> l230s01as);

	/**
	 * 重新引進額度資訊檔
	 * 
	 * @param l230m01a
	 *            簽約未動用主檔
	 * @return
	 */
	public Boolean reloadL230S01A(L230M01A l230m01a);

	/**
	 * 刪除額度資訊檔
	 * 
	 * @param l230m01a
	 *            簽約未動用主檔
	 * @return
	 */
	public void deleteL230S01A(L230M01A l230m01a);

}
