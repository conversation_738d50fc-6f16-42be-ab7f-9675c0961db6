package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120S01BDao;
import com.mega.eloan.lms.model.L120S01B;

/** 企金基本資料檔 **/
@Repository
public class L120S01BDaoImpl extends LMSJpaDao<L120S01B, String> implements
		L120S01BDao {

	@Override
	public L120S01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120S01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120S01B> list = createQuery(L120S01B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L120S01B findByUniqueKey(String mainId, String custId, String dupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);

		return findUniqueOrNone(search);
	}

	@Override
	public int delModel(String mainId) {
		Query query = getEntityManager().createNamedQuery("L120S01B.delModel");
		query.setParameter("MAINID", mainId); // 設置參數
		return query.executeUpdate();
	}

	@Override
	public List<L120S01B> findByCustIdDupId(String custId, String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L120S01B> list = createQuery(L120S01B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L120S01B> findAllNoNoteUp() {
		ISearch search = createSearchTemplete();
		search.setMaxResults(Integer.MAX_VALUE);
		search.addSearchModeParameters(SearchMode.NOT_LIKE, "updater", "N%");
		List<L120S01B> list = createQuery(L120S01B.class, search)
				.getResultList();
		return list;
	}
}