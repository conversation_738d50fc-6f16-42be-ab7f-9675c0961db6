package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Map;

/**
 * <pre>
 * 對大陸地區授信業務控管註記
 * </pre>
 * 
 * @since 2013/7/15
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/7/15,007625,new
 *          </ul>
 */
public interface ObsdbELF006Service {

	Map<String, Object> getNetValue(String BRNID);

	Map<String, Object> getByBrnTypeLimitType(String BRNID, String brnType,
			String limitType);

}
