<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<!-- JNDI Datasource -->
	<bean id="megaimage-db" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>${megaimagedb.jndiName}</value>
		</property>
	</bean>

	<bean id="megaimageTxManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="megaimage-db" />
	</bean>

	<tx:advice id="megaimageTxAdvice" transaction-manager="megaimageTxManager">
		<!-- the transactional semantics... -->
		<tx:attributes>
			<!-- all methods below are read-only -->
			<tx:method name="list*" read-only="true"
				propagation="NOT_SUPPORTED" />
			<tx:method name="find*" read-only="true"
				propagation="NOT_SUPPORTED" />
			<tx:method name="get*" read-only="true"
				propagation="NOT_SUPPORTED" />

			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="*" timeout="45" rollback-for="Throwable"
				propagation="REQUIRED" />
			<!-- timeout in seconds -->
		</tx:attributes>
	</tx:advice>

	<aop:config proxy-target-class="true">
		<aop:pointcut id="megaimageServiceOperation"
			expression="execution(* com.mega.eloan.lms.megaimage.service.*.*(..))" />
		<aop:advisor advice-ref="megaimageTxAdvice" pointcut-ref="megaimageServiceOperation" />
	</aop:config>

</beans>