/* 
 * QUOTSUB.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import tw.com.iisi.cap.model.GenericBean;


/** ELF384 科(子)目及其限額檔 **/
public class QUOTSUB extends GenericBean{

	private static final long serialVersionUID = 1L;

	/** 借款人統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="CHAR(10)",unique = true)
	private String custid;

	/** 重複序號 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)",unique = true)
	private String dupno;

	/** 
	 * 額度序號<p/>
	 * 額度序號即可區分DBU or OBU
	 */
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)",unique = true)
	private String cntrno;

	/** 
	 * 日期<p/>
	 * 動用審核表確認當日
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="SDATE", columnDefinition="DATE",unique = true)
	private Date sdate;

	/** 
	 * 科(子)目-簡碼<p/>
	 * 一個額度序號會有多個科目, 亦可輸入子目(Ex.應收保證－CP保證)
	 */
	@Column(name="LOANTP", length=8, columnDefinition="CHAR(08)",unique = true)
	private String loantp;

	/** 
	 * 科(子)目變更註記<p/>
	 * 額度不變<br/>
	 *  新增<br/>
	 *  取消<br/>
	 *  增額<br/>
	 *  減額
	 */
	@Column(name="CHGFLAG", length=1, columnDefinition="CHAR(1)")
	private String chgflag;

	/** 
	 * 原限額幣別<p/>
	 * 若變更註記為1.新增則內容為空白
	 */
	@Column(name="OLDCURR", length=3, columnDefinition="CHAR(03)")
	private String oldcurr;

	/** 
	 * 原限額<p/>
	 * 單位：元<br/>
	 *  若變更註記為1.新增則內容為0
	 */
	@Column(name="OLDQUOTA", columnDefinition="DECIMAL(15)")
	private BigDecimal oldquota;

	/** 新限額幣別 **/
	@Column(name="NEWCURR", length=3, columnDefinition="CHAR(03)")
	private String newcurr;

	/** 
	 * 新限額{若該科目無限額則為０, 單位：元, LNF023_UP_LIMIT , C900M01F.LmtAmt}
	 */
	@Column(name="NEWQUOTA", columnDefinition="DECIMAL(15)")
	private BigDecimal newquota;

	/** 
	 * 擔保代號<p/>
	 * S:有擔 N:無擔
	 */
	@Column(name="LNGU", length=1, columnDefinition="CHAR(01)")
	private String lngu;

	/** 資料修改人（行員代號） **/
	@Column(name="UPDATER", length=5, columnDefinition="CHAR(05)")
	private String updater;

	/** 資料修改日期 **/
	@Column(name="TMESTAMP", columnDefinition="TIMESTAMP")
	private Date tmestamp;

	/** a-Loan執行時間（on Line） **/
	@Column(name="ONLNTIME", columnDefinition="TIMESTAMP")
	private Date onlntime;

	/** a-Loan執行時間（Batch） **/
	@Column(name="BTHTIME", columnDefinition="TIMESTAMP")
	private Date bthtime;

	/*
	 * 該欄位只有DEC(3,0), 在 L03E 額度科目別限額維護
	 * 如超過, 則最大輸入 999
	 * 在 e-Loan 消金額度明細表的 "C900M01F-個金授信科目限額檔" lmtDays 卻為DEC(5,0)
	 * 應該是為了避免上傳 ELF384/QUOTSUB 時, 拋出欄位長度超過的錯誤
	 * 所以在 JavaBean 裡, 故意不 assign 此欄位
	 * 
	@Column(name="DURATION", columnDefinition="DECIMAL(3,0)")
	private int duration;
	*/
	
	/** 取得借款人統編 **/
	public String getCustid() {
		return this.custid;
	}
	/** 設定借款人統編 **/
	public void setCustid(String value) {
		this.custid = value;
	}

	/** 取得重複序號 **/
	public String getDupno() {
		return this.dupno;
	}
	/** 設定重複序號 **/
	public void setDupno(String value) {
		this.dupno = value;
	}

	/** 
	 * 取得額度序號<p/>
	 * 額度序號即可區分DBU or OBU
	 */
	public String getCntrno() {
		return this.cntrno;
	}
	/**
	 *  設定額度序號<p/>
	 *  額度序號即可區分DBU or OBU
	 **/
	public void setCntrno(String value) {
		this.cntrno = value;
	}

	/** 
	 * 取得日期<p/>
	 * 動用審核表確認當日
	 */
	public Date getSdate() {
		return this.sdate;
	}
	/**
	 *  設定日期<p/>
	 *  動用審核表確認當日
	 **/
	public void setSdate(Date value) {
		this.sdate = value;
	}

	/** 
	 * 取得科(子)目-簡碼<p/>
	 * 一個額度序號會有多個科目, 亦可輸入子目(Ex.應收保證－CP保證)
	 */
	public String getLoantp() {
		return this.loantp;
	}
	/**
	 *  設定科(子)目-簡碼<p/>
	 *  一個額度序號會有多個科目, 亦可輸入子目(Ex.應收保證－CP保證)
	 **/
	public void setLoantp(String value) {
		this.loantp = value;
	}

	/** 
	 * 取得科(子)目變更註記<p/>
	 * 額度不變<br/>
	 *  新增<br/>
	 *  取消<br/>
	 *  增額<br/>
	 *  減額
	 */
	public String getChgflag() {
		return this.chgflag;
	}
	/**
	 *  設定科(子)目變更註記<p/>
	 *  額度不變<br/>
	 *  新增<br/>
	 *  取消<br/>
	 *  增額<br/>
	 *  減額
	 **/
	public void setChgflag(String value) {
		this.chgflag = value;
	}

	/** 
	 * 取得原限額幣別<p/>
	 * 若變更註記為1.新增則內容為空白
	 */
	public String getOldcurr() {
		return this.oldcurr;
	}
	/**
	 *  設定原限額幣別<p/>
	 *  若變更註記為1.新增則內容為空白
	 **/
	public void setOldcurr(String value) {
		this.oldcurr = value;
	}

	/** 
	 * 取得原限額<p/>
	 * 單位：元<br/>
	 *  若變更註記為1.新增則內容為0
	 */
	public BigDecimal getOldquota() {
		return this.oldquota;
	}
	/**
	 *  設定原限額<p/>
	 *  單位：元<br/>
	 *  若變更註記為1.新增則內容為0
	 **/
	public void setOldquota(BigDecimal value) {
		this.oldquota = value;
	}

	/** 取得新限額幣別 **/
	public String getNewcurr() {
		return this.newcurr;
	}
	/** 設定新限額幣別 **/
	public void setNewcurr(String value) {
		this.newcurr = value;
	}

	/** 
	 * 取得新限額
	 */
	public BigDecimal getNewquota() {
		return this.newquota;
	}
	/**
	 *  設定新限額
	 **/
	public void setNewquota(BigDecimal value) {
		this.newquota = value;
	}

	/** 
	 * 取得擔保代號<p/>
	 * S:有擔 N:無擔
	 */
	public String getLngu() {
		return this.lngu;
	}
	/**
	 *  設定擔保代號<p/>
	 *  S:有擔 N:無擔
	 **/
	public void setLngu(String value) {
		this.lngu = value;
	}

	/** 取得資料修改人（行員代號） **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定資料修改人（行員代號） **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得資料修改日期 **/
	public Date getTmestamp() {
		return this.tmestamp;
	}
	/** 設定資料修改日期 **/
	public void setTmestamp(Date value) {
		this.tmestamp = value;
	}

	/** 取得a-Loan執行時間（on Line） **/
	public Date getOnlntime() {
		return this.onlntime;
	}
	/** 設定a-Loan執行時間（on Line） **/
	public void setOnlntime(Date value) {
		this.onlntime = value;
	}

	/** 取得a-Loan執行時間（Batch） **/
	public Date getBthtime() {
		return this.bthtime;
	}
	/** 設定a-Loan執行時間（Batch） **/
	public void setBthtime(Date value) {
		this.bthtime = value;
	}
}
