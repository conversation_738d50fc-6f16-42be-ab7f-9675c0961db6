package com.mega.eloan.lms.dc.action;

import org.w3c.dom.Document;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.bean.L140M01DBean;
import com.mega.eloan.lms.dc.util.TextDefine;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * Parser140M01D
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          </ul>
 */
public class Parser140M01D extends AbstractLMSCustParser {

	/**
	 * @param pid
	 * @param doViewName
	 * @param formGroup
	 */
	public Parser140M01D(String pid, String doViewName, String formGroup) {
		super(pid, doViewName, formGroup);
	}

	/**
	 * 讀取,處理及轉換
	 * 
	 * @param dxlPath
	 *            String : .dxl檔存放路徑
	 * @param dxlName
	 *            :.dxl列表中的.dxl檔名
	 * @param strBrn
	 *            String:分行名稱
	 * @param domDoc
	 *            DOM Document:已轉為DOM Document的.dxl檔
	 */
	@SuppressWarnings("unused")
	protected void transferDXL(String dxlPath, String dxlName, String strBrn,
			Document domDoc, String dxlXml) {
		long t1 = System.currentTimeMillis();
		try {
			String[] k1 = dxlName.split(TextDefine.ATTACH_DXL);// EX:{FLMS120M01_2E3761E1BB971A2B48257A7D00143D9C,.dxl}
			String[] k2 = k1[0].split("_");// EX:{FLMS120M01,2E3761E1BB971A2B48257A7D00143D9C}
			String tmpMainId = "";
			if (k2.length == 2) {
				tmpMainId = k2[1];// 主檔
			} else {
				tmpMainId = k2[2];// 明細檔之UNID
			}
			//20130417 Sandra若CNTRDOCID有值，以CNTRDOCID為140開頭所有的table的mainid；若無值，則取unid為mainid
			//20130419 Sandra因CNTRDOCID仍會有重覆，建霖mail通知調整使用WEBELOANMAINID
			String cntrDocId  =getItemValue(domDoc, "WEBELOANMAINID");
			tmpMainId = cntrDocId.isEmpty()?tmpMainId:cntrDocId;

			String checkItem1 = getItemValue(domDoc, "CheckItem1");
			String checkItem2 = getItemValue(domDoc, "CheckItem2");
			String checkItem3 = getItemValue(domDoc, "CheckItem3");
			int seq = 1 ;
			// notes. CheckItem1 <> ""時,Type:1,序號lmtSeq1-7
			//科目
			//20130527 modified by Sandra 依建霖來信確認註解判斷
			//if (StringUtils.isNotBlank(checkItem1)) {
				for (int i = 1; i < 8; i++) {
					L140M01DBean L140d = new L140M01DBean();
					L140d.setOid(Util.getOID());
					L140d.setMainId(tmpMainId);
					L140d.setLmtType("1");
					
					String subValue = this.getTextList(domDoc, "LimiSubject"
							+ i);
					L140d.setSubject(subValue);
					String curValue = getItemValue(domDoc, "LimCurr" + i);
					L140d.setLmtCurr(curValue);
					String amtValue = getItemValue(domDoc, "LimitAmt" + i);
					L140d.setLmtAmt(amtValue);

					// 當subject、lmtCurr 、lmtAmt欄位均為空白時不需產生資料
					String subject = L140d.getSubject();
					String lmtCurr = L140d.getLmtCurr();
					String lmtAmt = L140d.getLmtAmt();
					/*if (!subject.trim().equals(TextDefine.EMPTY_STRING)
					|| !lmtCurr.trim().equals(TextDefine.EMPTY_STRING)
					|| !lmtAmt.trim().equals(TextDefine.EMPTY_STRING)) {*/
					//20130424 Sandra只要科目有值就寫入資料
					//若subject為多重值，則拆成多筆寫入，序號要加1
					if (!subject.trim().equals(TextDefine.EMPTY_STRING)){
						if(subject.split("\\|").length>1){
							String[] t = subject.split("\\|");
							for(String tmp:t){
								L140d.setOid(Util.getOID());
								L140d.setLmtSeq(String.valueOf(seq));
								L140d.setSubject(tmp);
								this.txtWrite.println(L140d.toString());
								seq++;
							}
						}else{
							L140d.setLmtSeq(String.valueOf(seq));
							this.txtWrite.println(L140d.toString());
							this.parserTotal++;
							seq++;
						}
						
					}
				}
			//}
			// notes. CheckItem2 <> ""時,Type:1,序號lmtSeq:8-14
				//20130527 modified by Sandra 依建霖來信確認註解判斷
			//if (StringUtils.isNotBlank(checkItem2)) {
				for (int i = 1; i < 8; i++) {
					L140M01DBean L140d = new L140M01DBean();
					L140d.setOid(Util.getOID());
					L140d.setMainId(tmpMainId);
					L140d.setLmtType("1");
					//L140d.setLmtSeq(String.valueOf(i + 7));
					String subValue = this.getTextList(domDoc, "LimiSubtitle"
							+ i);
					L140d.setSubject(subValue);
					String curValue = getItemValue(domDoc, "LimSutCurr" + i);
					L140d.setLmtCurr(curValue);
					String amtValue = getItemValue(domDoc, "SubtitleLimit" + i);
					L140d.setLmtAmt(amtValue);

					// 當subject、lmtCurr 、lmtAmt欄位均為空白時不需產生資料
					String subject = L140d.getSubject();
					String lmtCurr = L140d.getLmtCurr();
					String lmtAmt = L140d.getLmtAmt();

					/*if (!subject.trim().equals(TextDefine.EMPTY_STRING)
					|| !lmtCurr.trim().equals(TextDefine.EMPTY_STRING)
					|| !lmtAmt.trim().equals(TextDefine.EMPTY_STRING)) {*/
					//20130424 Sandra只要科目有值就寫入資料
					if (!subject.trim().equals(TextDefine.EMPTY_STRING)){
						if(subject.split("\\|").length>1){
							String[] t = subject.split("\\|");
							for(String tmp:t){
								L140d.setOid(Util.getOID());
								L140d.setLmtSeq(String.valueOf(seq));
								L140d.setSubject(tmp);
								this.txtWrite.println(L140d.toString());
								seq++;
							}
						}else{
							L140d.setLmtSeq(String.valueOf(seq));
							this.txtWrite.println(L140d.toString());
							this.parserTotal++;
							seq++;
						}
					}

				}
			//}
			seq =1;
			// notes. CheckItem3 <> ""時,Type:2,序號lmtSeq1-7
		
			//20130527 modified by Sandra 依建霖來信確認註解判斷
			//if (StringUtils.isNotBlank(checkItem3)) {
				for (int i = 1; i < 8; i++) {
					L140M01DBean L140d = new L140M01DBean();
					L140d.setOid(Util.getOID());
					L140d.setMainId(tmpMainId);
					L140d.setLmtSeq(String.valueOf(i));
					L140d.setLmtType("2");
					//L140d.setLmtSeq(String.valueOf(i));
					String subValue = this.getTextList(domDoc, "MergSubtitle"
							+ i);
					L140d.setSubject(subValue);
					String curValue = getItemValue(domDoc, "MergCurr" + i);
					L140d.setLmtCurr(curValue);
					String amtValue = getItemValue(domDoc, "SubtitleMerg" + i);
					L140d.setLmtAmt(amtValue);

					// 當subject、lmtCurr 、lmtAmt欄位均為空白時不需產生資料
					String subject = L140d.getSubject();
					String lmtCurr = L140d.getLmtCurr();
					String lmtAmt = L140d.getLmtAmt();

					/*if (!subject.trim().equals(TextDefine.EMPTY_STRING)
							|| !lmtCurr.trim().equals(TextDefine.EMPTY_STRING)
							|| !lmtAmt.trim().equals(TextDefine.EMPTY_STRING)) {*/
					//20130424 Sandra只要科目有值就寫入資料
					//20130525 Sandra科目用串接方式呈現
/*
 					if (!subject.trim().equals(TextDefine.EMPTY_STRING)){
						if(subject.split("\\|").length>1){
							String[] t = subject.split("\\|");
							for(String tmp:t){
								L140d.setOid(Util.getOID());
								L140d.setLmtSeq(String.valueOf(seq));
								L140d.setSubject(tmp);
								this.txtWrite.println(L140d.toString());
								seq++;
							}
						}else{
							L140d.setLmtSeq(String.valueOf(seq));
							this.txtWrite.println(L140d.toString());
							this.parserTotal++;
							seq++;
						}
					}
*/
					if (!subject.trim().equals(TextDefine.EMPTY_STRING)){
						this.txtWrite.println(L140d.toString());
						this.parserTotal++;
					}
					
				}
			//}
		} catch (Exception e) {
			String errmsg = "【" + strBrn
					+ "】分行執行Parser140M01D 之transferDXL時產生錯誤,dxl檔名:" + dxlName
					+ ",dxlPath=" + dxlPath;
			throw new DCException(errmsg, e);
		} finally {
			if (DEBUG && logger.isDebugEnabled()) {
				logger.debug("@@@@@@@@ TOTAL_COST="
						+ (System.currentTimeMillis() - t1));
			}
		}
	}

}
