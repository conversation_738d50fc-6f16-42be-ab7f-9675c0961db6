/* 
 * L120S01N.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 信用風險管理遵循明細檔 **/
@NamedEntityGraph(name = "L120S01N-entity-graph", attributeNodes = { @NamedAttributeNode("l120s01m") })
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L120S01N", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "custId", "dupNo", "dataKind" }))
public class L120S01N extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 資料種類
	 * <p/>
	 * 詳說明
	 */
	@Size(max = 3)
	@Column(name = "DATAKIND", length = 3, columnDefinition = "CHAR(3)")
	private String dataKind;

	/** 查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "QUERYDATE", columnDefinition = "DATE")
	private Date queryDate;

	/** 資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name = "DATADATE", columnDefinition = "DATE")
	private Date dataDate;

	/**
	 * 總餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTALBAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totalBal;

	/** 佔淨值 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "SHAREOFNET", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal shareOfNet;

	/** 法定限額比率 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LAWLIMIT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal lawLimit;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 授信總異動額度(當地) **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOCALCURRENTADJVALT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal localCurrentAdjValT;

	/** 授信總額度(當地額度加異動) **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOCALCURRENTTOTAL", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal localCurrentTotal;

	/** 授信大額曝險比率 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOANLIMIT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal loanLimit;

	/** 額度金額合計 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "TOTALAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totalAmt;

	/** 額度佔淨值 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "SHAREOFNETAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal shareOfNetAmt;
	
	/**
	 * JOIN 關聯檔
	 * 
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
	@JoinColumn(name = "CUSTID", referencedColumnName = "CUSTID", nullable = false, insertable = false, updatable = false),
	@JoinColumn(name = "DUPNO", referencedColumnName = "DUPNO", nullable = false, insertable = false, updatable = false) })
	private L120S01M l120s01m;
	

	public L120S01M getL120s01m() {
		return l120s01m;
	}

	public void setL120s01m(L120S01M l120s01m) {
		this.l120s01m = l120s01m;
	}

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}

	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}

	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得資料種類
	 * <p/>
	 * 詳說明
	 */
	public String getDataKind() {
		return this.dataKind;
	}

	/**
	 * 設定資料種類
	 * <p/>
	 * 詳說明
	 **/
	public void setDataKind(String value) {
		this.dataKind = value;
	}

	/** 取得查詢日期 **/
	public Date getQueryDate() {
		return this.queryDate;
	}

	/** 設定查詢日期 **/
	public void setQueryDate(Date value) {
		this.queryDate = value;
	}

	/** 取得資料日期 **/
	public Date getDataDate() {
		return this.dataDate;
	}

	/** 設定資料日期 **/
	public void setDataDate(Date value) {
		this.dataDate = value;
	}

	/**
	 * 取得總餘額
	 * <p/>
	 * 單位：TWD仟元
	 */
	public BigDecimal getTotalBal() {
		return this.totalBal;
	}

	/**
	 * 設定總餘額
	 * <p/>
	 * 單位：TWD仟元
	 **/
	public void setTotalBal(BigDecimal value) {
		this.totalBal = value;
	}

	/** 取得佔淨值 **/
	public BigDecimal getShareOfNet() {
		return this.shareOfNet;
	}

	/** 設定佔淨值 **/
	public void setShareOfNet(BigDecimal value) {
		this.shareOfNet = value;
	}

	/** 取得法定限額比率 **/
	public BigDecimal getLawLimit() {
		return this.lawLimit;
	}

	/** 設定法定限額比率 **/
	public void setLawLimit(BigDecimal value) {
		this.lawLimit = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 設定授信總異動額度(當地) **/
	public void setLocalCurrentAdjValT(BigDecimal localCurrentAdjValT) {
		this.localCurrentAdjValT = localCurrentAdjValT;
	}

	/** 取得授信總異動額度(當地) **/
	public BigDecimal getLocalCurrentAdjValT() {
		return localCurrentAdjValT;
	}

	/** 設定授信額度合計(當地額度加異動) **/
	public void setLocalCurrentTotal(BigDecimal localCurrentTotal) {
		this.localCurrentTotal = localCurrentTotal;
	}

	/** 取得授信額度合計(當地額度加異動) **/
	public BigDecimal getLocalCurrentTotal() {
		return localCurrentTotal;
	}

	/** 設定授信大額曝險比率 **/
	public void setLoanLimit(BigDecimal loanLimit) {
		this.loanLimit = loanLimit;
	}

	/** 取得授信大額曝險比率 **/
	public BigDecimal getLoanLimit() {
		return loanLimit;
	}

	public void setTotalAmt(BigDecimal totalAmt) {
		this.totalAmt = totalAmt;
	}

	public BigDecimal getTotalAmt() {
		return totalAmt;
	}

	public void setShareOfNetAmt(BigDecimal shareOfNetAmt) {
		this.shareOfNetAmt = shareOfNetAmt;
	}

	public BigDecimal getShareOfNetAmt() {
		return shareOfNetAmt;
	}

}
