$(function(){
	$("select[name='longCaseDscr']").change(function(){
		var value = $(this).children('option:selected').val();
		if(value == "1") {
			$('#chk_radio1-2a1').show(); 
			$('#chk_radio1-2a2').hide();
		} else if (value == "2") {
			$('#chk_radio1-2a1').hide(); 
			$('#chk_radio1-2a2').show();
		} else {
			$('#chk_radio1-2a1').hide(); 
			$('#chk_radio1-2a2').hide();
		}
	});
	var cesGrid2 = $("#cesGrid2").iGrid({		
		handler : 'lms1205gridhandler',
		height : 175,
		sortname : 'createTime',
		postData : {
			formAction : "queryL120s01e3",
			rowNum:5,
			oid: "",
			mainId: ""
		},
		caption: "&nbsp;",
		hiddengrid : false,
		rownumbers:true,
		rowNum:5,
		//multiselect : true,
		colModel : [ {
			colHeader : i18n.lms1205s05["l120s05.grid26"], //建立日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			name : 'createTime' //col.id
		}, {
			colHeader : i18n.lms1205s05["l120s05.grid28"], //核准日期
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'approveTime' //col.id
		}, {
			colHeader : i18n.lms1205s05["l120s05.grid27"], //文件狀態
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'docStatus' //col.id
		}, {
			colHeader : i18n.lms1205s05["l120s05.grid25"], //主要借款人
			align : "left",
			width : 100, //設定寬度
			sortable : true, //是否允許排序
			//formatter : 'click',
			//onclick : function,
			name : 'custName' //col.id
		}, {
			colHeader : "mainId",
			name : 'mainId',
			hidden : true
		}, {
			colHeader : "oid",
			name : 'oid',
			hidden : true
		}, {
			colHeader : "gaapFlag",
			name : 'gaapFlag',
			hidden : true
		}],
		ondblClickRow : function(rowid) { //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
		}
	});
	
	    $("input[name='rkind']").click(function(){
	    if($(this).val() == "1"){
	    	$("#hiderow").show();
	    }else{
	    	$("#hiderow").hide();
	    }
	});
});

function getCes2(){
	//引進徵信資料按鈕ThickBox
	$("#getCes2").thickbox({     
		// 使用選取的內容進行彈窗
		title : i18n.lmss02["l120s02.thickbox9"],
		width : 640,
		height : 350,
		modal : true,
		align : "center",
		valign : "bottom",	
		i18n:i18n.lmss02,
		buttons: {
			"l120s02.thickbox1": function() {								
				var row = $("#cesGrid2").getGridParam('selrow'); 
				var list = "";
				var data = $("#cesGrid2").getRowData(row);
				list = data.oid;
				list = (list == undefined ? "" : list);
				var gaapFlag = data.gaapFlag;
				if (list != "") {
					$.thickbox.close();
					loadCesPage(gaapFlag);
				}else{
					CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
				}							
			},								             
			"l120s02.thickbox2": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

//-----------------------------------------------------------------------------------

function thickselkind() {
	$("#hidetitle").show();
	 if($("input[name='rkind']:radio:checked").val()=="1"){
		 $("#hiderow").show();
	 }else{
		 $("#hiderow").hide();
	 }
	//檢查是否已引進或儲存FreeFormat資料
 	$.ajax({									
		handler : "lms1205formhandler",
		type : "POST",
		dataType : "json",
		data : 
		{
			formAction : "checkExistData",
			mainId : responseJSON.mainId
		}
		}).done(function(json) {
			if(json.exist){
				if(json.rkind == "1"){
					if($("#cesPanel").attr("openFlag") == "true"){
						var url = json.gaapFlag+"" == "0" ? "../../lms/lms1205S05Da" : "../../lms/lms1205S05Db";
						$("#cesPanel").load(url, function(){
							setTimeout(function(){
								$("#cesPanel").attr("openFlag", "false");
								$("#tabForm").buildItem();
								//進行查詢
							 	$.ajax({									
									handler : "lms1205formhandler",
									type : "POST",
									dataType : "json",
									data : 
									{
										formAction : "queryC140M01A",
										page : "A1",
										mainId : responseJSON.mainId
									}
									}).done(function(json) {
										
										if(json.isFreeFormat){
											// 完全自由格式
											// 查詢 FreeFormat
						            	 	$.ajax({									
												handler : "lms1205formhandler",
												type : "POST",
												dataType : "json",
												data : 
												{
													formAction : "queryFreeFormat",
													mainId : responseJSON.mainId
												}
												}).done(function(json) {
													//alert(JSON.stringify(json));
//													$("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
//													freeFormate();
//													setCkeditor2("itemDscrC", json.itemDscr);
                                                    if (navigator.userAgent.search("MSIE") >= 0) {
													    // 若為IE瀏覽器
													    $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
													    freeFormate();
                                                    } else if (navigator.userAgent.search("Edg") >= 0) {
                                                        // 若為Edge瀏覽器
                                                        freeFormate();
                                                        setCkeditor2("itemDscrC", json.itemDscr);
                                                    } else {
                                                        // 採用IE way
                                                        $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
                                                        freeFormate();
                                                    }
											});	
										}else{
											
											//J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
											if(json.gaapFlag+"" == "0"){
												//GAA{
												$("#sA1r1t3btnSel2").remove();
												$("#sA1r1t3btnSel3").remove();
											}else if(json.gaapFlag+"" == "2"){
												//EAS
												$("#sA1r1t3btnSel1").remove();
												$("#sA1r1t3btnSel2").remove();									 
											}else{
												//IFRS
												$("#sA1r1t3btnSel1").remove();
												$("#sA1r1t3btnSel3").remove();
											}
											
											var $tabForm = $("#tabForm");
											$tabForm.reset();
											var table1 = $tabForm.find("#table1");
											$tabForm.setData(json, false);
											if(json.edit_mode3 == "1"){
											   //$("input[name='editMode3']:eq(0)").attr("checked",true);
											   $("input[name='editMode3'][value='1']:radio" ).prop( "checked" , true );
											   //table1.find(".sA1radio1-3,.sA1radio1-2").hide();
											}else if(json.edit_mode3 == "2"){
											   //$("input[name='editMode3']:eq(1)").attr("checked",true);
												$("input[name='editMode3'][value='2']:radio" ).prop( "checked" , true );
											   //table1.find(".sA1radio1-2").show().siblings(".sA1radio1-3").hide();
											}else if(json.edit_mode3 == "3"){
												//完全自由格式
												//$("input[name='editMode3']:eq(2)").attr("checked",true);
												$("input[name='editMode3'][value='3']:radio" ).prop( "checked" , true );
												//table1.find(".sA1radio1-3").show();
											}else if(json.edit_mode3 == "4"){
											   //$("input[name='editMode3']:eq(3)").attr("checked",true);
												$("input[name='editMode3'][value='4']:radio" ).prop( "checked" , true );
											    //table1.find(".sA1radio1-3").show();
											}else{
											   //$("input[name='editMode3']:eq(4)").attr("checked",true);
												$("input[name='editMode3'][value='5']:radio" ).prop( "checked" , true );
											}																
											var list={};
											list[mainId] = mainId;
											$tabForm.find(".totalc").html("100");
											$tabForm.find("#cesTypCd").html(DOMPurify.sanitize(json.cesTypCd));
											thickboxCes10(list);
											if (navigator.userAgent.search("MSIE") >= 0) {
                                                // 若為IE瀏覽器
                                            } else if (navigator.userAgent.search("Edg") >= 0) {
                                                // 若為Edge瀏覽器
                                                setCkeditor2("cp10_cont1", json.cp10_cont1);
                                                setCkeditor2("cp10_cont2", json.cp10_cont2);
                                                setCkeditor2("cp10_cont3", json.cp10_cont3);
                                                setCkeditor2("bfp_note1_1", json.bfp_note1_1);
                                            } else {
                                                // 採用IE way
                                            }
										}
								});								
							},100);			
						});		
					}else{
						$("#tabForm").buildItem();
						//進行查詢
					 	$.ajax({									
							handler : "lms1205formhandler",
							type : "POST",
							dataType : "json",
							data : 
							{
								formAction : "queryC140M01A",
								page : "A1",
								mainId : responseJSON.mainId
							}
							}).done(function(json) {
								
								//J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
								if(json.gaapFlag+"" == "0"){
									//GAA{
									$("#sA1r1t3btnSel2").remove();
									$("#sA1r1t3btnSel3").remove();
								}else if(json.gaapFlag+"" == "2"){
									//EAS
									$("#sA1r1t3btnSel1").remove();
									$("#sA1r1t3btnSel2").remove();									 
								}else{
									//IFRS
									$("#sA1r1t3btnSel1").remove();
									$("#sA1r1t3btnSel3").remove();
								}
								
								//alert(JSON.stringify(json));
								var $tabForm = $("#tabForm");					
								$tabForm.reset();
								var table1 = $tabForm.find("#table1");
								$tabForm.setData(json, false);
								
								if(json.edit_mode3 == "1"){
								   //$("input[name='editMode3']:eq(0)").attr("checked",true);
								   $("input[name='editMode3'][value='1']:radio" ).prop( "checked" , true );
								   //table1.find(".sA1radio1-3,.sA1radio1-2").hide();
								}else if(json.edit_mode3 == "2"){
								   //$("input[name='editMode3']:eq(1)").attr("checked",true);
									$("input[name='editMode3'][value='2']:radio" ).prop( "checked" , true );
								   //table1.find(".sA1radio1-2").show().siblings(".sA1radio1-3").hide();
								}else if(json.edit_mode3 == "3"){
									//完全自由格式
									//$("input[name='editMode3']:eq(2)").attr("checked",true);
									$("input[name='editMode3'][value='3']:radio" ).prop( "checked" , true );
									//table1.find(".sA1radio1-3").show();
								}else if(json.edit_mode3 == "4"){
								   //$("input[name='editMode3']:eq(3)").attr("checked",true);
									$("input[name='editMode3'][value='4']:radio" ).prop( "checked" , true );
								    //table1.find(".sA1radio1-3").show();
								}else{
								   //$("input[name='editMode3']:eq(4)").attr("checked",true);
									$("input[name='editMode3'][value='5']:radio" ).prop( "checked" , true );
								}							
								var list={};
								list[mainId] = mainId;
								$tabForm.find("#cesTypCd").html(DOMPurify.sanitize(json.cesTypCd));							
								thickboxCes10(list);
								if (navigator.userAgent.search("MSIE") >= 0) {
                                    // 若為IE瀏覽器
                                } else if (navigator.userAgent.search("Edg") >= 0) {
                                    // 若為Edge瀏覽器
                                    setCkeditor2("cp10_cont1", json.cp10_cont1);
                                    setCkeditor2("cp10_cont2", json.cp10_cont2);
                                    setCkeditor2("cp10_cont3", json.cp10_cont3);
                                    setCkeditor2("bfp_note1_1", json.bfp_note1_1);
                                } else {
                                    // 採用IE way
                                }
						});							
					}									
				}else{
					// 查詢 FreeFormat
            	 	$.ajax({									
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "queryFreeFormat",
							mainId : responseJSON.mainId
						}
						}).done(function(json) {
							//alert(JSON.stringify(json));
//							$("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
//							freeFormate();
//							setCkeditor2("itemDscrC", json.itemDscr);
                            if (navigator.userAgent.search("MSIE") >= 0) {
                                // 若為IE瀏覽器
                                $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
                                freeFormate();
                            } else if (navigator.userAgent.search("Edg") >= 0) {
                                // 若為Edge瀏覽器
                                freeFormate();
                                setCkeditor2("itemDscrC", json.itemDscr);
                            } else {
                                // 採用IE way
                                $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
                                freeFormate();
                            }
					});					
				}
			}else{
				//完全沒有資料
        	 	$.ajax({									
					handler : "lms1205formhandler",
					type : "POST",
					dataType : "json",
					data : 
					{
						formAction : "getCusSel",
						mainId : responseJSON.mainId
					}
					}).done(function(json) {
					      var selJson = {
						       		item : json.selCus,
						       		format : "{key}",
						       		space: false
						       	};
					      $("#LMS1205S05Form04").find("#selCus").setItems(selJson);
					 	 if($("input[name='rkind']:radio:checked").val()=="1"){
							 $("#hiderow").show();
						 }else{
							 $("#hiderow").hide();
						 }
						 if(responseJSON.readOnly == "true"){
						 	// 若為唯讀狀態則直接顯示查無資料訊息 Miller add 2012/07/18
						 	CommonAPI.showMessage(i18n.def['grid.emptyrecords']);
						 	return;	
						 }						 
						 $("#thickselkind").thickbox({     // 使用選取的內容進行彈窗
							   title : i18n.lms1205s05["l120s05.thickbox11"],
							   width : 520,
							   height : 260,
							   modal : true,
							   valign : "bottom",
							   align : "center",
							   i18n:i18n.def,
							   buttons: {
						             	  "sure": function() {
						             		//判斷所選種類
						             		var rval = $("input[name='rkind']:radio:checked").val();
						             		if(rval == "1"){
						             			//引自徵信報告	             			
						        				//依照使用者選擇借款人查詢徵信報告
						             			var selOid = $("#selCus option:selected").val();
						        				$.thickbox.close();
						        				getCes2();
						        				cesGrid2(selOid);            			
						             		}else{
						             			//Freeformate
						             			$.thickbox.close();
						             			$("#itemDscrC").val("");
						             			freeFormate();
						             		}
						                 },            
						                  "cancel": function() {
						                	  API.confirmMessage(i18n.def['flow.exit'], function(res){
						      					if(res){
						      						$.thickbox.close();
						      					}
						      		        });
						                 }
							           }			  
							    });
				});
			}
	});
}


function loadCesPage(gaapFlag){
	if($("#cesPanel").attr("openFlag") == "true"){
		var url = gaapFlag == "0" ? "../../lms/lms1205S05Da":"../../lms/lms1205S05Db";
		$("#cesPanel").load(url, function(){
			setTimeout(function(){
				$("#tabForm").buildItem();
				loadCesData();
				$("#cesPanel").attr("openFlag", "false");
			},100);
		});		
	}else{
		$("#tabForm").buildItem();
		loadCesData();	
	}
}

function loadCesData(){
	//結構化表格
	//徵信報告
	var row = $("#cesGrid2").getGridParam('selrow'); 
	var list = "";
	var data = $("#cesGrid2").getRowData(row);
	list = data;
	list = (list == undefined ? "" : list);
	if(list != ""){
		//進行引進
		$.ajax({									
			handler : "lms1205formhandler",
			type : "POST",
			dataType : "json",
			data : 
			{
				formAction : "getC140m01a",
				page : "A1",
				mainId : responseJSON.mainId,
				cesOid : list.oid,
				cesMainId : list.mainId
			}
			}).done(function(json) {
				responseJSON["cesMainId"] = json.cesMainId;
				//進行查詢
        	 	$.ajax({									
					handler : "lms1205formhandler",
					type : "POST",
					dataType : "json",
					data : 
					{
						formAction : "queryC140M01A",
						page : "A1",
						mainId : responseJSON.mainId
					}
					}).done(function(json) {
						if(json.isFreeFormat){
							// 完全自由格式
							// 查詢 FreeFormat
		            	 	$.ajax({									
								handler : "lms1205formhandler",
								type : "POST",
								dataType : "json",
								data : 
								{
									formAction : "queryFreeFormat",
									mainId : responseJSON.mainId
								}
								}).done(function(json) {
//									$("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
//									freeFormate();
//									setCkeditor2("itemDscrC", json.itemDscr);
                                    if (navigator.userAgent.search("MSIE") >= 0) {
                                        // 若為IE瀏覽器
                                        $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
                                        freeFormate();
                                    } else if (navigator.userAgent.search("Edg") >= 0) {
                                        // 若為Edge瀏覽器
                                        freeFormate();
                                        setCkeditor2("itemDscrC", json.itemDscr);
                                    } else {
                                        // 採用IE way
                                        $("#LMS1205S05Form04").find("#itemDscrC").val(json.itemDscr);
                                        freeFormate();
                                    }
							});	
						}else{
							
							//J-109-0279_05097_B1001 e-Loan企金簽報書配合徵信IFRS改版與新增EAS會計準則相關修改
							if(json.gaapFlag+"" == "0"){
								//GAA{
								$("#sA1r1t3btnSel2").remove();
								$("#sA1r1t3btnSel3").remove();
							}else if(json.gaapFlag+"" == "2"){
								//EAS
								$("#sA1r1t3btnSel1").remove();
								$("#sA1r1t3btnSel2").remove();									 
							}else{
								//IFRS
								$("#sA1r1t3btnSel1").remove();
								$("#sA1r1t3btnSel3").remove();
							}
							
							var $tabForm = $("#tabForm");
							$tabForm.reset();
							var table1 = $tabForm.find("#table1");
							$tabForm.setData(json, false);
//							if(json.edit_mode3 == "1"){
//							   $("input[name='editMode3']:eq(0)").attr("checked",true);
//							   table1.find(".sA1radio1-3,.sA1radio1-2").hide();
//							}else if(json.edit_mode3 == "2"){
//							   $("input[name='editMode3']:eq(1)").attr("checked",true);
//							   table1.find(".sA1radio1-2").show().siblings(".sA1radio1-3").hide();
//							}else if(json.edit_mode3 == "4"){
//							   $("input[name='editMode3']:eq(2)").attr("checked",true);
//							   table1.find(".sA1radio1-3").show();
//							}else{
//								$("input[name='editMode3']:eq(3)").attr("checked",true);										
//							   null;
//							}	
							
							if(json.edit_mode3 == "1"){
							   //$("input[name='editMode3']:eq(0)").attr("checked",true);
							   $("input[name='editMode3'][value='1']:radio" ).prop( "checked" , true );
							   //table1.find(".sA1radio1-3,.sA1radio1-2").hide();
							}else if(json.edit_mode3 == "2"){
							   //$("input[name='editMode3']:eq(1)").attr("checked",true);
								$("input[name='editMode3'][value='2']:radio" ).prop( "checked" , true );
							   //table1.find(".sA1radio1-2").show().siblings(".sA1radio1-3").hide();
							}else if(json.edit_mode3 == "3"){
								//完全自由格式
								//$("input[name='editMode3']:eq(2)").attr("checked",true);
								$("input[name='editMode3'][value='3']:radio" ).prop( "checked" , true );
								//table1.find(".sA1radio1-3").show();
							}else if(json.edit_mode3 == "4"){
							   //$("input[name='editMode3']:eq(3)").attr("checked",true);
								$("input[name='editMode3'][value='4']:radio" ).prop( "checked" , true );
							   //table1.find(".sA1radio1-3").show();
							}else{
							   //$("input[name='editMode3']:eq(4)").attr("checked",true);
								$("input[name='editMode3'][value='5']:radio" ).prop( "checked" , true );
							}			
							
							var list={};
							list[mainId] = mainId;
							$tabForm.find("#cesTypCd").html(DOMPurify.sanitize(json.cesTypCd));
							thickboxCes10(list);
							if (navigator.userAgent.search("MSIE") >= 0) {
                                // 若為IE瀏覽器
                            } else if (navigator.userAgent.search("Edg") >= 0) {
                                // 若為Edge瀏覽器
                                setCkeditor2("cp10_cont1", json.cp10_cont1);
                                setCkeditor2("cp10_cont2", json.cp10_cont2);
                                setCkeditor2("cp10_cont3", json.cp10_cont3);
                                setCkeditor2("bfp_note1_1", json.bfp_note1_1);
                            } else {
                                // 採用IE way
                            }
						}
				});
		});					
	}else{
		CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
	}
}

function cesGrid2(selOid){
	   $("#cesGrid2").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s01e3",
			rowNum:5,
			oid: selOid,
			mainId: responseJSON.mainId
		},
		search: true
	   }).trigger("reloadGrid");
	}

function getCes3(){
	//引進徵信資料按鈕ThickBox
	$("#getCes2").thickbox({     
		// 使用選取的內容進行彈窗
		title : i18n.lms1205s05["l120s05.thickbox13"],
		width : 640,
		height : 350,
		modal : true,
		align : "center",
		valign : "bottom",	
		i18n:i18n.def,
		buttons: {
			"sure": function() {
				//徵信報告
				var row = $("#cesGrid2").getGridParam('selrow'); 
				var list = "";
				var data = $("#cesGrid2").getRowData(row);
				list = data;
				list = (list.oid == undefined ? "" : list);
				if(list.oid != "" && list.oid != undefined && list.oid != null){
					//進行引進
					$.ajax({									
						handler : "lms1205formhandler",
						type : "POST",
						dataType : "json",
						data : 
						{
							formAction : "getC140m01a3",
							page : "51",
							mainId : responseJSON.mainId,
							cesOid : list.oid,
							cesMainId : list.mainId
						}
						}).done(function(json) {
							//alert(JSON.stringify(json));
							$("#itemDscr02").val(json.buss_situ);
							$.thickbox.close();
							$.thickbox.close();
							CommonAPI.showMessage(json.NOTIFY_MESSAGE);
					});					
				}else{
					CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
				}
			},								             
			"cancel": function() {
				 API.confirmMessage(i18n.def['flow.exit'], function(res){
						if(res){
							$.thickbox.close();
						}
			        });
			}
		}
	});
}

/*
function thickboxCes10(list) {	 
	 $("#thickboxCes10").thickbox({     // 使用選取的內容進行彈窗
		   title : i18n.lms1205s05["l120s05.thickbox11"],
		   width : 960,
		   height : 480,
		   modal : true,
		   i18n:i18n.def,
		   buttons: {
		             "saveData": function() {
		            	 	//儲存
		            	 	//$.thickbox.close();
		            	 	$.ajax({									
								handler : "lms1205formhandler",
								type : "POST",
								dataType : "json",
								data : 
								{
									formAction : "save",
									page : "A1",
									mainId : responseJSON.mainId,
									cesOid : list.oid,
									cesMainId : list.mainId,
									cp10_cont1 : $("#tabForm").find("#cp10_cont1").val(), 
									cp10_cont2 : $("#tabForm").find("#cp10_cont2").val(), 
									bfp_note1_1 : $("#tabForm").find("#bfp_note1_1").val(), 
									cp10_cont3 : $("#tabForm").find("#cp10_cont3").val(),
									curr : $("#tabForm").find("#cp10_cont3").val(), 
									amtUnitFU : $("#tabForm").find("#cp10_cont3").val(), 
									cr_tot : $("#tabForm").find("#cr_tot").val(), 
									cu_tot : $("#tabForm").find("#cu_tot").val(), 
									cr1_1 : $("#tabForm").find("#cr1_1").val(), 
									cu1_1 : $("#tabForm").find("#cu1_1").val(), 
									cr1_2 : $("#tabForm").find("#cr1_2").val(), 
									cu1_2 : $("#tabForm").find("#cu1_2").val(), 
									cr1_3 : $("#tabForm").find("#cr1_3").val(), 
									cu1_3 : $("#tabForm").find("#cu1_3").val(), 
									cr2_1 : $("#tabForm").find("#cr2_1").val(), 
									cu2_1 : $("#tabForm").find("#cu2_1").val(), 
									cr2_2 : $("#tabForm").find("#cr2_2").val(), 
									cu2_2 : $("#tabForm").find("#cu2_2").val(), 
									cr2_3 : $("#tabForm").find("#cr2_3").val(), 
									cu2_3 : $("#tabForm").find("#cu2_3").val(), 
									cr3_1 : $("#tabForm").find("#cr3_1").val(), 
									cu3_1 : $("#tabForm").find("#cu3_1").val(), 
									cr3_2 : $("#tabForm").find("#cr3_2").val(), 
									cu3_2 : $("#tabForm").find("#cu3_2").val(), 
									cr3_3 : $("#tabForm").find("#cr3_3").val(), 
									cu3_3 : $("#tabForm").find("#cu3_3").val(), 
									cr4_1 : $("#tabForm").find("#cr4_1").val(), 
									cu4_1 : $("#tabForm").find("#cu4_1").val(), 
									cr4_2 : $("#tabForm").find("#cr4_2").val(), 
									cu4_2 : $("#tabForm").find("#cu4_2").val(), 
									cr4_3 : $("#tabForm").find("#cr4_3").val(), 
									cu4_3 : $("#tabForm").find("#cu4_3").val(), 
									cr5_1 : $("#tabForm").find("#cr5_1").val(), 
									cu5_1 : $("#tabForm").find("#cu5_1").val(), 
									cr5_2 : $("#tabForm").find("#cr5_2").val(), 
									cu5_2 : $("#tabForm").find("#cu5_2").val(), 
									cr5_3 : $("#tabForm").find("#cr5_3").val(), 
									cu5_3 : $("#tabForm").find("#cu5_3").val(),
									year_21: $("#tabForm").find("#year_21").val(),
									year_22: $("#tabForm").find("#year_22").val(),
									year_23: $("#tabForm").find("#year_23").val(),
									year_24: $("#tabForm").find("#year_24").val(),
									year_25: $("#tabForm").find("#year_25").val(),
									year_26: $("#tabForm").find("#year_26").val(),
									year_27: $("#tabForm").find("#year_27").val(),
									case1_21: $("#tabForm").find("#case1_21").val(),
									r15_21: $("#tabForm").find("#r15_21").val(),
									san1_21: $("#tabForm").find("#san1_21").val(),
									san2_21: $("#tabForm").find("#san2_21").val(),
									san3_21: $("#tabForm").find("#san3_21").val(),
									r15_22: $("#tabForm").find("#r15_22").val(),
									year_28: $("#tabForm").find("#year_28").val(),
									year_29: $("#tabForm").find("#year_29").val(),
									year_210: $("#tabForm").find("#year_210").val(),
									year_211: $("#tabForm").find("#year_211").val(),
									year_212: $("#tabForm").find("#year_212").val(),
									year_213: $("#tabForm").find("#year_213").val(),
									year_214: $("#tabForm").find("#year_214").val(),
									year_215: $("#tabForm").find("#year_215").val(),
									r15_23: $("#tabForm").find("#r15_23").val(),
									r15_24: $("#tabForm").find("#r15_24").val(),
									r15_25: $("#tabForm").find("#r15_25").val(),
									r15_26: $("#tabForm").find("#r15_26").val(),
									r15_27: $("#tabForm").find("#r15_27").val(),
									r15_28: $("#tabForm").find("#r15_28").val(),
									r15_29: $("#tabForm").find("#r15_29").val(),
									r15_210: $("#tabForm").find("#r15_210").val(),
									r15_211: $("#tabForm").find("#r15_211").val(),
									r15_212: $("#tabForm").find("#r15_212").val(),
									r15_213: $("#tabForm").find("#r15_213").val(),
									r15_214: $("#tabForm").find("#r15_214").val(),
									r15_215: $("#tabForm").find("#r15_215").val(),
									san1_22: $("#tabForm").find("#san1_22").val(),
									san1_23: $("#tabForm").find("#san1_23").val(),
									san1_24: $("#tabForm").find("#san1_24").val(),
									san1_25: $("#tabForm").find("#san1_25").val(),
									san1_26: $("#tabForm").find("#san1_26").val(),
									san1_27: $("#tabForm").find("#san1_27").val(),
									san1_28: $("#tabForm").find("#san1_28").val(),
									san1_29: $("#tabForm").find("#san1_29").val(),
									san1_210: $("#tabForm").find("#san1_210").val(),
									san1_211: $("#tabForm").find("#san1_211").val(),
									san1_212: $("#tabForm").find("#san1_212").val(),
									san1_213: $("#tabForm").find("#san1_213").val(),
									san1_214: $("#tabForm").find("#san1_214").val(),
									san1_215: $("#tabForm").find("#san1_215").val(),
									san2_22: $("#tabForm").find("#san2_22").val(),
									san2_23: $("#tabForm").find("#san2_23").val(),
									san2_24: $("#tabForm").find("#san2_24").val(),
									san2_25: $("#tabForm").find("#san2_25").val(),
									san2_26: $("#tabForm").find("#san2_26").val(),
									san2_27: $("#tabForm").find("#san2_27").val(),
									san2_28: $("#tabForm").find("#san2_28").val(),
									san2_29: $("#tabForm").find("#san2_29").val(),
									san2_210: $("#tabForm").find("#san2_210").val(),
									san2_211: $("#tabForm").find("#san2_211").val(),
									san2_212: $("#tabForm").find("#san2_212").val(),
									san2_213: $("#tabForm").find("#san2_213").val(),
									san2_214: $("#tabForm").find("#san2_214").val(),
									san2_215: $("#tabForm").find("#san2_215").val(),
									san3_22: $("#tabForm").find("#san3_22").val(),
									san3_23: $("#tabForm").find("#san3_23").val(),
									san3_24: $("#tabForm").find("#san3_24").val(),
									san3_25: $("#tabForm").find("#san3_25").val(),
									san3_26: $("#tabForm").find("#san3_26").val(),
									san3_27: $("#tabForm").find("#san3_27").val(),
									san3_28: $("#tabForm").find("#san3_28").val(),
									san3_29: $("#tabForm").find("#san3_29").val(),
									san3_210: $("#tabForm").find("#san3_210").val(),
									san3_211: $("#tabForm").find("#san3_211").val(),
									san3_212: $("#tabForm").find("#san3_212").val(),
									san3_213: $("#tabForm").find("#san3_213").val(),
									san3_214: $("#tabForm").find("#san3_214").val(),
									san3_215: $("#tabForm").find("#san3_215").val(),
									case2_21: $("#tabForm").find("#case2_21").val(),
									case3_21: $("#tabForm").find("#case3_21").val()
								},
								success : function(json) {
								}
							});
		             },
		             "del": function() {
		 				CommonAPI.confirmMessage(i18n.lms1205s05["l120s05.confirm1"],function(b){
							if(b){
								//是的function
			            	 	$.thickbox.close();
								//刪除
			            	 	$.ajax({									
									handler : "lms1205formhandler",
									type : "POST",
									dataType : "json",
									data : 
									{
										formAction : "deleteC140m01a",
										mainId : responseJSON.mainId
									},
									success : function(json) {
									   $("input[name='editMode3']:eq(3)").attr("checked",true);
									   $("#tabForm").find(".sA1radio2-5").show().siblings("[class^=sA1radio2]").hide();					
									}
								});
							}else{
								//否的function
								CommonAPI.showMessage(i18n.lms1205s05["l120s05.alert3"]);
							}
						})
			         },
		              "close": function() {
		            	  API.confirmMessage(i18n.def['flow.exit'], function(res){
		  					if(res){
		  						$.thickbox.close();
		  					}
		  		        });
		             }
		           }			  
		    });
	 }
*/

function cesGrid2(selOid){
$("#cesGrid2").jqGrid("setGridParam", {
		postData : {
			formAction : "queryL120s01e3",
			rowNum:5,
			oid: selOid,
			mainId: responseJSON.mainId
		},
		search: true
	}).trigger("reloadGrid");
}

