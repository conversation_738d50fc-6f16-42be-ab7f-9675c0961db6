<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="panelFragmentBody">
            <!-- 個金償債能力檔 -->
			<div id="C101S01CDiv" name="C101S01CDiv" >
				<form id="C101S01CForm" name="C101S01CForm" >
					<table class="tb2" width="100%" style='margin:0;'>
						<tr>
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.yFamAmt'}">夫妻年收入</th:block>&nbsp;&nbsp;</td>
							<td colspan='5' >
								<select id="yFamCurr" name="yFamCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="yFamAmt" name="yFamAmt" class="max numeric required" integer="13" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
							<!--
							<td width="18%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.yIncomeCert'}">夫妻年收入證明文件</th:block>&nbsp;&nbsp;</td>
							<td width="32%" ><select id="yIncomeCert" name="yIncomeCert" class="required" codeType="lms1205s01_inDoc"  ></select></td>
							-->
						</tr>
						<tr>
							<td class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.fincome'}">家庭年收入</th:block>&nbsp;&nbsp;</td>
							<td colspan='5'>
								<select id="fincomeCurr" name="fincomeCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="fincome" name="fincome" class="max numeric required" integer="10" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
						<tr>
							<td width="14%"  class="hd2" align="right" >
								<button type="button" id="btRate" name="btRate" style='margin:0;' >計算</button>
								<button type="button" id="btRateView" name="btRateView" style='margin:0;' class="forview">檢視</button>
								<br>
								<span class="text-red">＊</span><th:block th:text="#{'C101S01C.dRate'}">個人負債比率</th:block>&nbsp;&nbsp;</td>
							<td width="20%" ><input type="text" id="dRate" name="dRate" class="max numeric required" integer="3" fraction="2" size="5" readonly/>％
							<div>
								規定:<span id="href_DRateExplain" class="text-red"><u style="cursor:pointer;"><th:block th:text="#{'C101S01C.personal_debt_ratio.doc.2'}">負債比規定</th:block></u></span>
							</div>
							</td>
							<td width="14%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.yRate'}">夫妻負債比率</th:block>&nbsp;&nbsp;</td>
							<td width="19%" ><input type="text" id="yRate" name="yRate" class="max numeric required" integer="3" fraction="2" size="5" readonly/>％</td>
							
							<td width="14%" class="hd2" align="right" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.fRate'}">家庭負債比率</th:block>&nbsp;&nbsp;</td>
							<td width="19%" ><input type="text" id="fRate" name="fRate" class="max numeric required" integer="3" fraction="2" size="5" readonly/>％</td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><th:block th:text="#{'C101S01C.indAssetTotAmt'}">個人資產總額</th:block>&nbsp;&nbsp;
								<!--div>
									2022-07 消金處 李思瑩 提供新版 負債比 文件
									因「個人負債比貸放控管原則說明」是 「負債比」裡的其中一部分
									=> 只 display 並 update 上面的「負債比規定」文件, 下面的「個人負債比貸放控管原則說明」改為隱藏
									<span id="href_DRatControlRuleDesc" class="text-red"><u style="cursor:pointer;"><th:block th:text="#{'C101S01C.doc.personalDebtRatioControlRule'}">個人負債比貸放控管原則說明</th:block></u></span>
								</div -->
							</td>
							<td colspan='2'>
								<select id="indAssetTotAmtCurr" name="indAssetTotAmtCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<br/>
								<input type="text" id="indAssetTotAmt" name="indAssetTotAmt" class="max numeric" integer="13" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
							<td class="hd2" align="right" ><th:block th:text="#{'C101S01C.indDebtBalance'}">個人負債餘額</th:block>&nbsp;&nbsp;</td>
							<td colspan='2'>
								<select id="indDebtBalanceCurr" name="indDebtBalanceCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<br/>
								<input type="text" id="indDebtBalance" name="indDebtBalance" class="max numeric" integer="13" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" ><th:block th:text="#{'C101S01C.fAssetTotAmt'}">家庭資產總額</th:block>&nbsp;&nbsp;</td>
							<td colspan='2'>
								<select id="fAssetTotAmtCurr" name="fAssetTotAmtCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<br/>
								<input type="text" id="fAssetTotAmt" name="fAssetTotAmt" class="max numeric" integer="13" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
							<td class="hd2" align="right" ><th:block th:text="#{'C101S01C.fDebtBalance'}">家庭負債餘額</th:block>&nbsp;&nbsp;</td>
							<td colspan='2'>
								<select id="fDebtBalanceCurr" name="fDebtBalanceCurr" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<br/>
								<input type="text" id="fDebtBalance" name="fDebtBalance" class="max numeric" integer="13" fraction="0" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
					</table>	
					<table class="tb2" width="100%" style='margin:0;'>	<!-- 因一鍵查詢4項的button文字內容太長，把 td 撐開，分拆3個 table  -->
						<tr>
							<td class="hd2">
								<div>
									<div><th:block th:text="#{'C101S01C.debt_ratio'}">負債比率為月負債除以月平均收入，月負債指各項借款(含本次借款)每月應支付本息，包含信用卡循環信用使用額度之最低應繳金額。</th:block>&nbsp;
									</div>
									<div><button type="button" id="btDataQueryC1_C2_C3_C4" name="btDataQueryC1_C2_C3_C4" style='margin:0;'>一鍵引進: 1.使用信用卡循環信用或現金卡情形 2.是否於本行財富管理有定時定額扣款 3.與本行其他業務往來 4.與本行財富管理三個月平均總資產</button>
									</div>
								</div>																
							</td>							
						</tr>
					</table>					
					<table class="tb2" width="100%" style='margin:0;'>	
						<tr>
							<td width="50%" class="hd2" align="right" colspan="2" ><th:block th:text="#{'C101S01C.credit'}">使用信用卡循環信用或現金卡情形</th:block>&nbsp;&nbsp;<button type="button" id="btDataQueryC1" name="btDataQueryC1">引進</button></td>
							<td width="50%" colspan="2" ><input type="checkbox" id="credit" name="credit" codeType="cls_credit" itemStyle="size:1" /></td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01C.isPeriodFund'}">是否於本行財富管理有定時定額扣款</th:block>&nbsp;&nbsp;<button type="button" id="btDataQueryC2" name="btDataQueryC2">引進</button></td>
							<td colspan="2" ><input type="radio" id="isPeriodFund" name="isPeriodFund" class="" codeType="Common_YesNo" itemStyle="sort:desc" /></td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01C.busi'}">與本行其他業務往來(財富管理業務如基金保險信用卡等)</th:block>&nbsp;&nbsp;<button type="button" id="btDataQueryC3" name="btDataQueryC3">引進</button></td>
							<td colspan="2" ><input type="radio" id="busi" name="busi" class="" codeType="Common_YesNo2" /></td>
						</tr>
						<!-- J-111-0373、J-112-0495，國內消金非房貸模型評等4.0，新增[本行有擔餘額][本行無擔餘額] -->
						<tr>
							<td class="hd2" align="right" colspan="2" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.loanBalSByid'}">本行有擔餘額</th:block>&nbsp;&nbsp;
								<button type="button" id="btLoanBalSByid" name="btLoanBalSByid">引進</button></td>
							<td colspan="2">
								<input type="text" id="loanBalSByid" name="loanBalSByid" class="max number" size="20" style="display:none;" readonly/>
								<input type="text" id="loanBalSByidShow" name="loanBalSByidShow" readonly/>
								<th:block th:text="#{'money.unit.1'}">元</th:block>
								<br/>
								<th:block th:text="#{'l120s01m.item21'}">資料日期</th:block><input type="text" id="loanBalSTime" name="loanBalSTime" class="date ABCD CD" readonly/>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><span class="text-red">＊</span><th:block th:text="#{'C101S01C.loanBalNByid'}">本行無擔餘額</th:block>&nbsp;&nbsp;
								<button type="button" id="btLoanBalNByid" name="btLoanBalNByid">引進</button></td>
							<td colspan="2">
								<input type="text" id="loanBalNByid" name="loanBalNByid" class="max number" size="20" style="display:none;" readonly/>
								<input type="text" id="loanBalNByidShow" name="loanBalNByidShow" readonly/>
								<th:block th:text="#{'money.unit.1'}">元</th:block>
								<br/>
								<th:block th:text="#{'l120s01m.item21'}">資料日期</th:block><input type="text" id="loanBalNTime" name="loanBalNTime" class="date ABCD CD" readonly/>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01C.invMBalAmt'}">與本行財富管理三個月平均總資產</th:block>&nbsp;&nbsp;<button type="button" id="btDataQueryC4" name="btDataQueryC4">引進</button></td>
							<td colspan="2" >
								<select id="invMBalCurr" name="invMBalCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="invMBalAmt" name="invMBalAmt" class="max number " maxlength="13" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01C.invOBalAmt'}">與他行財富管理三個月平均總資產</th:block>&nbsp;&nbsp;</td>
							<td colspan="2" >
								<select id="invOBalCurr" name="invOBalCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="invOBalAmt" name="invOBalAmt" class="max number " maxlength="13" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
						<tr>
							<td class="hd2" align="right" colspan="2" ><!--<span class="text-red">＊</span>--><th:block th:text="#{'C101S01C.branAmt'}">與金融機構存款往來情形(近六個月平均餘額)</th:block>&nbsp;&nbsp;</td>
							<td colspan="2" >
								<select id="branCurr" name="branCurr" class="required" codeType="Common_Currcy" itemStyle="format:{value}-{key}" ></select>
								<input type="text" id="branAmt" name="branAmt" class="max number " maxlength="13" size="13" />
								<th:block th:text="#{'money.unit'}">萬元</th:block>
							</td>
						</tr>
					</table>
					<div id='DataQueryC_memo'>
						引進來源
						<table border='1' >
							<tr>
								<td><th:block th:text="#{'C101S01C.credit'}">使用信用卡循環信用或現金卡情形</th:block>&nbsp;</td>
								<td>判斷本次聯徵查詢結果的 KRM040, BAM095 </td>
							</tr>
							<tr>
								<td><th:block th:text="#{'C101S01C.isPeriodFund'}">是否於本行財富管理有定時定額扣款</th:block>&nbsp;</td>
								<td>判斷是否存在﹝契約到期<!--AC230-->、契約失效<!--AC223>=3次-->、暫停扣款<!--AC210/AC211-->、暫停交易記號<!--AC299(09-09)='Y' -->﹞以外的契約(資料倉儲)&nbsp;&nbsp;&nbsp;</td>
							</tr>
							<tr>
								<td><th:block th:text="#{'C101S01C.busi'}">與本行其他業務往來(財富管理業務如基金保險信用卡等)</th:block>&nbsp;&nbsp;&nbsp;</td>
								<td>判斷0024-23 往來項目<!--CMFLUNVA-->是否勾選[&nbsp;&nbsp;] 財富管理業務 &nbsp;&nbsp;&nbsp;&nbsp;[&nbsp;&nbsp;]信用卡&nbsp;&nbsp;&nbsp;</td>
							</tr>
							<tr>
								<td><th:block th:text="#{'C101S01C.invMBalAmt'}">與本行財富管理三個月平均總資產</th:block>&nbsp;</td>
								<td>以不含存款的理財AUM計算(資料倉儲)</td>
							</tr>
						</table>
					</div>
				</form>
				<div id="rateBox" class="content" style="display:none;">
					<form id="rateForm">
						<table width="98%" class="tb2" id="tb1">
							<tr>
								<td class="hd2" colspan="7">
									【本案資訊】<button type="button" id="btRateReset" name="btRateReset" style='margin:0;'>清除重填</button>
									<br/>
									<input type='checkbox' id="guarantorFlag" name='guarantorFlag' value='Y'>本案為保證人
								</td>
							</tr>
							<tr>
								<td>
									<input type='radio' id="mode_1" name='mode_1' class="mode" value='1'>期付金&nbsp;
									<br/><input type='radio' id="mode_1" name='mode_1' value='2' class="mode">按月計息
								</td>
								<td>
									額度：<input type="text" id="loan_1" name="loan_1" class="numeric loan" maxlength="13" size="6" fraction="2"/>仟元 
								</td>
								<td>
									期間：<input type="text" id="period_1" name="period_1" class="numeric period" maxlength="13" size="2" />期
								</td>
								<td>
									寬限期：<input type="text" id="extPeriod_1" name="extPeriod_1" class="numeric extPeriod" maxlength="13" size="2" />期
								</td>
								<td>
									利率：<input type="text" id="rate_1" name="rate_1" class="max numeric rate" maxlength="6" size="3" integer="3" fraction="3" max="100" />%
								</td>
								<td>
									月付金：<input type="text" id="mPay_1" name="mPay_1" class="numeric mPay" maxlength="13" size="6" disabled="true" />元
								</td>
								<td>
									調整後月付金：<input type="text" id="mPayCh_1" name="mPayCh_1" class="numeric mPayCh" maxlength="13" integer="13" size="6" />元
								</td>
							</tr>
							<tr>
								<td>
									<input type='radio' id="mode_2" name='mode_2' class="mode" value='1'>期付金&nbsp;
									<br/><input type='radio' id="mode_2" name='mode_2' value='2' class="mode">按月計息
								</td>
								<td>
									額度：<input type="text" id="loan_2" name="loan_2" class="numeric loan" maxlength="13" size="6" fraction="2"/>仟元 
								</td>
								<td>
									期間：<input type="text" id="period_2" name="period_2" class="numeric period" maxlength="13" size="2" />期
								</td>
								<td>
									寬限期：<input type="text" id="extPeriod_2" name="extPeriod_2" class="numeric extPeriod" maxlength="13" size="2" />期
								</td>
								<td>
									利率：<input type="text" id="rate_2" name="rate_2" class="max numeric rate" maxlength="6" size="3" integer="3" fraction="3" max="100"/>%
								</td>
								<td>
									月付金：<input type="text" id="mPay_2" name="mPay_2" class="numeric mPay" maxlength="13" size="6" disabled="true"/>元
								</td>
								<td>
									調整後月付金：<input type="text" id="mPayCh_2" name="mPayCh_2" class="numeric mPayCh" maxlength="13" integer="13" size="6" />元
								</td>
							</tr><tr>
								<td>
									<input type='radio' id="mode_3" name='mode_3' value='1' class="mode">期付金&nbsp;
									<br/><input type='radio' id="mode_3" name='mode_3' value='2' class="mode">按月計息
								</td>
								<td>
									額度：<input type="text" id="loan_3" name="loan_3" class="numeric loan" maxlength="13" size="6" fraction="2"/>仟元 
								</td>
								<td>
									期間：<input type="text" id="period_3" name="period_3" class="numeric period" maxlength="13" size="2" />期
								</td>
								<td>
									寬限期：<input type="text" id="extPeriod_3" name="extPeriod_3" class="numeric extPeriod" maxlength="13" size="2" />期
								</td>
								<td>
									利率：<input type="text" id="rate_3" name="rate_3" class="max numeric rate" maxlength="6" size="3" integer="3" fraction="3" max="100"/>%
								</td>
								<td>
									月付金：<input type="text" id="mPay_3" name="mPay_3" class="numeric mPay" maxlength="13" size="6" disabled="true"/>元
								</td>
								<td>
									調整後月付金：<input type="text" id="mPayCh_3" name="mPayCh_3" class="numeric mPayCh" maxlength="13" integer="13" size="6" />元
								</td>
							</tr><tr>
								<td>
									<input type='radio' id="mode_4" name='mode_4' value='1' class="mode">期付金&nbsp;
									<br/><input type='radio' id="mode_4" name='mode_4' value='2' class="mode">按月計息
								</td>
								<td>
									額度：<input type="text" id="loan_4" name="loan_4" class="numeric loan" maxlength="13" size="6" fraction="2"/>仟元 
								</td>
								<td>
									期間：<input type="text" id="period_4" name="period_4" class="numeric period" maxlength="13" size="2" />期
								</td>
								<td>
									寬限期：<input type="text" id="extPeriod_4" name="extPeriod_4" class="numeric extPeriod" maxlength="13" size="2" />期
								</td>
								<td>
									利率：<input type="text" id="rate_4" name="rate_4" class="max numeric rate" maxlength="6" size="3" integer="3" fraction="3" max="100"/>%
								</td>
								<td>
									月付金：<input type="text" id="mPay_4" name="mPay_4" class="numeric mPay" maxlength="13" size="6" disabled="true"/>元
								</td>
								<td>
									調整後月付金：<input type="text" id="mPayCh_4" name="mPayCh_4" class="numeric mPayCh" maxlength="13" integer="13" size="6" />元
								</td>
							</tr><tr>
								<td>
									<input type='radio' id="mode_5" name='mode_5' value='1' class="mode">期付金&nbsp;
									<br/><input type='radio' id="mode_5" name='mode_5' value='2' class="mode">按月計息
								</td>
								<td>
									額度：<input type="text" id="loan_5" name="loan_5" class="numeric loan" maxlength="13" size="6" fraction="2"/>仟元 
								</td>
								<td>
									期間：<input type="text" id="period_5" name="period_5" class="numeric period" maxlength="13" size="2" />期
								</td>
								<td>
									寬限期：<input type="text" id="extPeriod_5" name="extPeriod_5" class="numeric extPeriod" maxlength="13" size="2" />期
								</td>
								<td>
									利率：<input type="text" id="rate_5" name="rate_5" class="max numeric rate" maxlength="6" size="3" integer="3" fraction="3" max="100"/>%
								</td>
								<td>
									月付金：<input type="text" id="mPay_5" name="mPay_5" class="numeric mPay" maxlength="13" size="6" disabled="true"/>元
								</td>
								<td>
									調整後月付金：<input type="text" id="mPayCh_5" name="mPayCh_5" class="numeric mPayCh" maxlength="13" integer="13" size="6" />元
								</td>
							</tr>
						</table>
						<table width="98%" class="tb2">
							<tr>
								<td class="hd2" colspan="2">
									【配偶資訊】
								</td>
							</tr>
							<tr>
								<td width="25%">
									夫妻年收入
								</td>
								<td >
									<input type="text" id="yFamAmtR" name="yFamAmtR" class="max numeric" maxlength="15" size="12"/>元
								</td>
							</tr>
							<tr>
								<td >
									配偶貸款期付金
								</td>
								<td >
									<input type="text" id="yPeriod" name="yPeriod" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
							<tr>
								<td >
									配偶信用卡循環
								</td>
								<td >
									<input type="text" id="yCycle" name="yCycle" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
							<tr>
								<td >
									配偶分期未償還金額
								</td>
								<td >
									<input type="text" id="yPeriodUnpay" name="yPeriodUnpay" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
						</table>
						
						<table width="98%" class="tb2">
							<tr>
								<td class="hd2" colspan="2">
									【家庭資訊】
								</td>
							</tr>
							<tr>
								<td width="25%">
									借款人及家庭年收入
								</td>
								<td >
									<input type="text" id="fFamAmtR" name="fFamAmtR" class="max numeric" maxlength="15" size="12"/>元
								</td>
							</tr>
							<tr>
								<td >
									家庭貸款期付金
								</td>
								<td >
									<input type="text" id="fPeriod" name="fPeriod" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
							<tr>
								<td >
									家庭信用卡循環
								</td>
								<td >
									<input type="text" id="fCycle" name="fCycle" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
							<tr>
								<td >
									家庭分期未償還金額
								</td>
								<td >
									<input type="text" id="fPeriodUnpay" name="fPeriodUnpay" class="max numeric" maxlength="13" size="12" />元
								</td>
							</tr>
						</table>
						
						<table width="98%" class="tb2">
							<tr>
								<td class="hd2" colspan="2">
									【備註】
								</td>
							</tr>
							<tr>
								<td width="100%">
									<th:block th:text="#{'C101S01C.remark.1'}">1.計入家庭年收入及負債僅限符合110.12.6兆銀消金字第1100000335號函規定，須擔任案下保證人且為借款人之「配偶或共同生活設籍之二親等血親」或「一親等之同一經濟關係人」方可計入。</th:block>
									<br>
									<th:block th:text="#{'C101S01C.remark.2'}">2.家庭之貸款期付金、信用卡循環、分期未償還金額，僅計入保證人負債之金額。</th:block>
								</td>
							</tr>
						</table>
						<input type="hidden" id="jcicCount" name="jcicCount" />
						<input type="hidden" id="jcicCreditCount" name="jcicCreditCount" />
						<table width="98%" class="tb2">
							<tr>
								<td class="hd2" colspan="9">
									【聯徵資訊】
									<button type="button" id="btImportJCIC" name="btImportJCIC" style='margin:0;'>
										引進
									</button>
								</td>
							</tr>
							<tr>
								<td class="hd2" align="center">
									項目
								</td>
								<td class="hd2" align="center">
									行庫名稱
								</td>
								<td class="hd2" align="center">
									聯徵<br/>科目
								</td>
								<td class="hd2" align="center">
									中文名稱
								</td>
								<td class="hd2" align="center">
									額度<br/>(仟元)
								</td>
								<td class="hd2" align="center">
									餘額<br/>(仟元)
								</td>
								<td class="hd2" align="center">
									試算月付金(元)
								</td>
								<td class="hd2" align="center">
									調整後月付金(元)
								</td>
								<td class="hd2" align="center">
									代償
								</td>
							</tr>
							<tbody id="tb3">
								<!--
								<tr>
									<td >
										<input type="text" id="jcic_1" name="jcic_1" class="numeric" maxlength="13" size="3" disabled="true"/>
									</td>
									<td >
										<input type="text" id="jcic_item_1" name="jcic_item_1" class="numeric" maxlength="13" size="3" disabled="true"/>
									</td>
									<td >
										<input type="jcic_itemname_1" id="jcic_itemname_1" name="jcic_loan_1" class="numeric" maxlength="13" size="8" disabled="true"/>
									</td>
									<td >
										<input type="text" id="jcic_loan_1" name="jcic_loan_1" class="numeric" maxlength="13" size="8" disabled="true"/>
									</td>
									<td >
										<input type="text" id="jcic_period_1" name="jcic_period_1" class="numeric" maxlength="13" size="8" />
									</td>
									<td >
										<input type="text" id="jcic_period_ch_1" name="jcic_period_ch_1" class="numeric" maxlength="13" size="8" />
									</td>
								</tr>
								-->
							</tbody>
						</table>
						<table width="98%" class="tb2">
							<tr>
								<td class="hd2" colspan="8">
									【聯徵資訊-信用卡】
								</td>
							</tr>
							<tr>
								<td class="hd2" align="center">
									項目
								</td>
								<td class="hd2" align="center">
									行庫名稱
								</td>
								<td class="hd2" align="center">
									信用卡循環
								</td>
								<td class="hd2" align="center">
									調整後(元)
								</td>
								<td class="hd2" align="center">
									代償
								</td>
								<td class="hd2" align="center">
									分期未償還金額
								</td>
								<td class="hd2" align="center">
									調整後(元)
								</td>
								<td class="hd2" align="center">
									代償
								</td>
							</tr>
							<tbody id="tb4">
							<!--
                            <tr>
                                <td >
                                    <input type="text" id="jcic_1" name="jcic_1" class="numeric" maxlength="13" size="3" disabled="true"/>
                                </td>
                                <td >
                                    <input type="text" id="jcic_item_1" name="jcic_item_1" class="numeric" maxlength="13" size="3" disabled="true"/>
                                </td>
                                <td >
                                    <input type="jcic_itemname_1" id="jcic_itemname_1" name="jcic_loan_1" class="numeric" maxlength="13" size="8" disabled="true"/>
                                </td>
                                <td >
                                    <input type="text" id="jcic_loan_1" name="jcic_loan_1" class="numeric" maxlength="13" size="8" disabled="true"/>
                                </td>
                                <td >
                                    <input type="text" id="jcic_period_1" name="jcic_period_1" class="numeric" maxlength="13" size="8" />
                                </td>
                                <td >
                                    <input type="text" id="jcic_period_ch_1" name="jcic_period_ch_1" class="numeric" maxlength="13" size="8" />
                                </td>
                            </tr>
                            -->
							</tbody>
						</table>
						<table width="98%" class="tb2" id="tb5">
							<tr>
								<td class="hd2" width="15%">
									信用卡循環(本行)
								</td>
								<td width="15%">
									<input type="text" id="jcic_credit_017" name="jcic_credit_017" class="numeric" maxlength="13" size="9" disabled="true"/>元
								</td>
								<td class="hd2" width="15%">
									調整後(元)
								</td>
								<td>
									<input type="text" id="jcic_credit_ch_017" name="jcic_credit_ch_017" class="numeric" maxlength="13" size="9" fraction="2" disabled="true"/>元
								</td>
							</tr>
							<tr>
								<td class="hd2">
									信用卡循環(他行)
								</td>
								<td>
									<input type="text" id="jcic_credit_not_017" name="jcic_credit_not_017" class="numeric" maxlength="13" size="9" disabled="true"/>元
								</td>
								<td class="hd2" width="15%">
									調整後(元)
								</td>
								<td>
									<input type="text" id="jcic_credit_not_ch_017" name="jcic_credit_not_ch_017" class="numeric" maxlength="13" size="9" fraction="2" disabled="true"/>元
								</td>
							</tr>
							<tr>
								<td class="hd2">
									分期未償還金額
								</td>
								<td>
									<input type="text" id="jcic_credit_unpay" name="jcic_credit_unpay" class="numeric" maxlength="13" size="9" disabled="true"/>元
								</td>
								<td class="hd2" width="15%">
									調整後(元)
								</td>
								<td>
									<input type="text" id="jcic_credit_ch_unpay" name="jcic_credit_ch_unpay" class="numeric" maxlength="13" size="9" fraction="2" disabled="true"/>元
								</td>
							</tr>
						</table>
						<table width="98%" class="tb2" id="tb6">
							<tr>
								<td class="hd2" colspan="2">
									【負債比】
								</td>
							</tr>
							<tr>
								<td width="35%">
									個人收入
								</td>
								<td >
									<input type="text" id="pAllAmt" name="pAllAmt" class="max numeric" maxlength="17" size="10" disabled="true"/>元
								</td>
							</tr>
							<tr>
								<td >
									個人負債總額
								</td>
								<td >
									<input type="text" id="pDebtAmt" name="pDebtAmt" class="max numeric" maxlength="17" size="10" disabled="true"/>元
								</td>
							</tr>
							<tr>
								<td >
									個人負債比率
								</td>
								<td >
									<input type="text" id="dRateR" name="dRateR" class="numeric rate" maxlength="6" size="8" integer="3" fraction="2"  disabled="true"/>%
								</td>
							</tr>
							<tr>
								<td >
									夫妻負債比率
								</td>
								<td >
									<input type="text" id="yRateR" name="yRateR" class="numeric rate" maxlength="6" size="8" integer="3" fraction="2"  disabled="true"/>%
								</td>
							</tr>
							<tr>
								<td >
									家庭負債比率
								</td>
								<td >
									<input type="text" id="fRateR" name="fRateR" class="numeric rate" maxlength="6" size="8" integer="3" fraction="2"  disabled="true"/>%
								</td>
							</tr>
						</table>
					</form>
				</div>
			</div>
         </th:block>
    </body>
</html>
