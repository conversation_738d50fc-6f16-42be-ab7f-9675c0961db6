
--#select * from COM.BELSPGM where SYSTYP='L' order by PGMCODE;
delete from COM.BELSPGM where SYSTYP='L';

--##################
--(311_0)授信作業
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (311001, '1',      0, 'L', 100, '授信作業', 0, 'app/home/<USER>', '備註(海外分行)', 'system', current timestamp);
--(近期已收案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321001, '2', 311001, 'L', 100, '近期已收案件', 0, 'app/lms/lms0005v00', '備註(海外分行)', 'system', current timestamp);
--(待辦案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321002, '2', 311001, 'L', 200, '待辦案件', 0, 'app/lms/lms0015v00', '備註(海外分行)', 'system', current timestamp);
--(案件簽報書)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321003, '2', 311001, 'L', 300, '案件簽報書', 0, '', '備註lms120', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331001, '3', 321003, 'L', 100, '編製中', 0, 'app/lms/lms1205v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331002, '3', 321003, 'L', 200, '待覆核', 0, 'app/lms/lms1205v02', '備註', 'system', current timestamp);

--(廢除)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331025, '3', 321003, 'L', 201, '提會待登錄', 0, 'app/lms/lms1205v25', '備註(加拿大分行)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331026, '3', 321003, 'L', 202, '提會待覆核', 0, 'app/lms/lms1205v26', '備註(加拿大分行)', 'system', current timestamp);

insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331021, '3', 321003, 'L', 210, '呈總行', 0, 'app/lms/lms1205v21', '備註(有海外總行)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331022, '3', 321003, 'L', 220, '總行待覆核', 0, 'app/lms/lms1205v22', '備註(海外總行)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331023, '3', 321003, 'L', 230, '提會待登錄', 0, 'app/lms/lms1205v23', '備註(加拿大總行多倫多、澳洲總行雪梨)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331024, '3', 321003, 'L', 240, '提會待覆核', 0, 'app/lms/lms1205v24', '備註(加拿大總行多倫多、澳洲總行雪梨)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331003, '3', 321003, 'L', 300, '呈授管處/營運中心', 0, 'app/lms/lms1205v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331027, '3', 321003, 'L', 310, '提會待登錄', 0, 'app/lms/lms1205v27', '備註(泰國總行曼谷)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331028, '3', 321003, 'L', 320, '提會待覆核', 0, 'app/lms/lms1205v28', '備註(泰國總行曼谷)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331004, '3', 321003, 'L', 400, '待補件/撤件', 0, 'app/lms/lms1205v04', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331004, '3', 321003, 'L', 410, '待補件', 0, 'app/lms/lms1205v04', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331004, '3', 321003, 'L', 420, '撤件', 0, 'app/lms/lms1205v04', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331005, '3', 321003, 'L', 500, '已核准受理', 0, 'app/lms/lms1205v05', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331006, '3', 321003, 'L', 600, '婉卻', 0, 'app/lms/lms1205v06', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331007, '3', 321003, 'L', 700, '陳復案/陳述案', 0, 'app/lms/lms1205v07', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331008, '3', 321003, 'L', 800, '授信異常通報', 0, 'app/lms/lms1205v08', '備註', 'system', current timestamp);
--(聯行額度明細表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321004, '2', 311001, 'L', 400, '聯行額度明細表', 0, '', '備註lms141', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331010, '3', 321004, 'L', 100, '聯貸簽核編製中', 0, 'app/lms/lms1415v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331011, '3', 321004, 'L', 200, '聯貸簽核待覆核', 0, 'app/lms/lms1415v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331012, '3', 321004, 'L', 300, '已完成聯貸簽核流程', 0, 'app/lms/lms1415v03', '備註', 'system', current timestamp);
--(小放會會議記錄)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321005, '2', 311001, 'L', 500, '小放會會議記錄', 0, 'app/lms/lms1505v00', '備註lms150', 'system', current timestamp);
--(動用審核表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321006, '2', 311001, 'L', 600, '動用審核表', 0, '', '備註lms160', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331013, '3', 321006, 'L', 100, '編製中', 0, 'app/lms/lms1605v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331014, '3', 321006, 'L', 200, '待覆核', 0, 'app/lms/lms1605v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331015, '3', 321006, 'L', 300, '已覆核', 0, 'app/lms/lms1605v03', '備註', 'system', current timestamp);
--(婉卻紀錄查詢)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321007, '2', 311001, 'L', 700, '婉卻紀錄查詢', 0, 'app/lms/rps4035v00', '備註rps4035q02', 'system', current timestamp);
--(修改資料特殊流程)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321008, '2', 311001, 'L', 800, '修改資料特殊流程', 0, '', '備註lms210', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331016, '3', 321008, 'L', 100, '編製中', 0, 'app/lms/lms2105v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331017, '3', 321008, 'L', 200, '待覆核', 0, 'app/lms/lms2105v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331018, '3', 321008, 'L', 300, '已覆核', 0, 'app/lms/lms2105v03', '備註', 'system', current timestamp);
--(簽約未動用授信案件送報作業)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (321009, '2', 311001, 'L', 900, '簽約未動用授信案件送報作業', 0, '', '備註lms230', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331081, '3', 321009, 'L', 100, '編製中', 0, 'app/lms/lms2305v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331082, '3', 321009, 'L', 200, '待覆核', 0, 'app/lms/lms2305v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (331083, '3', 321009, 'L', 300, '已覆核', 0, 'app/lms/lms2305v03', '備註', 'system', current timestamp);


--##################
--(312_0)企金覆審
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (312001, '1',      0, 'L', 200, '企金覆審', 0, 'app/home/<USER>', '備註(海外,國內)', 'system', current timestamp);
--(覆審報告表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322001, '2', 312001, 'L', 100, '覆審報告表', 0, '', '備註lms170', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332001, '3', 322001, 'L', 100, '編製中', 0, 'app/lrs/lms1705v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332002, '3', 322001, 'L', 200, '待受檢單位回覆', 0, 'app/lrs/lms1705v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332003, '3', 322001, 'L', 300, '編製完成', 0, 'app/lrs/lms1705v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332004, '3', 322001, 'L', 400, '待覆核', 0, 'app/lrs/lms1705v04', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332005, '3', 322001, 'L', 500, '已覆核', 0, 'app/lrs/lms1705v05', '備註', 'system', current timestamp);
--(產生企金戶新增/增額/逾放轉正名單檔)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322002, '2', 312001, 'L', 200, '產生企金戶新增/增額/逾放轉正名單檔', 0, 'app/lrs/lms1835v00', '備註lms184', 'system', current timestamp);
--(分行覆審名單檔)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322003, '2', 312001, 'L', 300, '分行覆審名單檔', 0, '', '備註lms181', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332006, '3', 322003, 'L', 100, '編製中-指定年月查詢', 0, 'app/lrs/lms1805v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332007, '3', 322003, 'L', 200, '待覆核', 0, 'app/lrs/lms1805v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332008, '3', 322003, 'L', 300, '已覆核', 0, 'app/lrs/lms1805v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332009, '3', 322003, 'L', 400, '已產生覆審報告名單檔', 0, 'app/lrs/lms1805v04', '備註', 'system', current timestamp);
--(覆審控制檔維護)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322004, '2', 312001, 'L', 400, '覆審控制檔維護', 0, '', '備註lms182', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332010, '3', 322004, 'L', 100, '編製中', 0, 'app/lrs/lms1815v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332011, '3', 322004, 'L', 200, '待覆核', 0, 'app/lrs/lms1815v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332012, '3', 322004, 'L', 300, '已覆核', 0, 'app/lrs/lms1815v03', '備註', 'system', current timestamp);
--(預約產生覆審名單)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322005, '2', 312001, 'L', 500, '預約產生覆審名單', 0, '', '備註lms183', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332013, '3', 322005, 'L', 100, '未處理', 0, 'app/lrs/lms1825v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332014, '3', 322005, 'L', 200, '處理成功 ', 0, 'app/lrs/lms1825v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332015, '3', 322005, 'L', 300, '處理失敗', 0, 'app/lrs/lms1825v03', '備註', 'system', current timestamp);
--(管理報表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322006, '2', 312001, 'L', 600, '管理報表', 0, '', '備註lms185', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332016, '3', 322006, 'L', 100, '最新資料', 0, 'app/lrs/lms1855v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332017, '3', 322006, 'L', 200, '歷史資料', 0, 'app/lrs/lms1855v02', '備註', 'system', current timestamp);
--(管理)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (322007, '2', 312001, 'L', 700, '管理', 0, '', '備註lms220', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332018, '3', 322007, 'L', 100, '分行執行紀錄', 0, 'app/lrs/lms2205v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (332019, '3', 322007, 'L', 200, '參數設定', 0, 'app/lrs/lms2205v02', '備註', 'system', current timestamp);


--##################
--(313_0)個金覆審
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (313001, '1',      0, 'L', 300, '個金覆審', 0, 'app/home/<USER>', '備註(海外,國內)', 'system', current timestamp);
--(分行覆審報告表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (323001, '2', 313001, 'L', 100, '分行覆審報告表', 0, '', '備註cls241', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333001, '3', 323001, 'L', 100, '編製中', 0, 'app/crs/lms2415v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333002, '3', 323001, 'L', 200, '待覆核', 0, 'app/crs/lms2415v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333003, '3', 323001, 'L', 300, '已覆核未核定', 0, 'app/crs/lms2415v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333004, '3', 323001, 'L', 400, '已覆核已核定', 0, 'app/crs/lms2415v04', '備註', 'system', current timestamp);
----(舊式覆審報告表)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333005, '3', 323001, 'L', 500, '舊式覆審報告表', 0, 'app/crs/lms2415v05', '備註', 'system', current timestamp);
--(覆審工作底稿)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (323002, '2', 313001, 'L', 200, '覆審工作底稿', 0, '', '備註cls240', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333031, '3', 323002, 'L', 100, '編製中', 0, 'app/crs/lms2405v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333032, '3', 323002, 'L', 200, '待覆核', 0, 'app/crs/lms2405v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333033, '3', 323002, 'L', 300, '已覆核', 0, 'app/crs/lms2405v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333034, '3', 323002, 'L', 400, '已傳送分行', 0, 'app/crs/lms2405v04', '備註', 'system', current timestamp);
--(覆審報告表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (323003, '2', 313001, 'L', 300, '覆審報告表', 0, '', '備註cls241', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333006, '3', 323003, 'L', 100, '已覆核未核定', 0, 'app/crs/lms2415v06', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333007, '3', 323003, 'L', 200, '已覆核已核定', 0, 'app/crs/lms2415v07', '備註', 'system', current timestamp);
--(管理報表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (323004, '2', 313001, 'L', 400, '管理報表', 0, 'app/home/<USER>', '備註cls242', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333041, '3', 323004, 'L', 100, '最新資料', 0, 'app/crs/lms2425v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (333042, '3', 323004, 'L', 200, '歷史資料', 0, 'app/crs/lms2425v02', '備註', 'system', current timestamp);


--##################
--(314_0)稽核工作底稿
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (314001, '1',      0, 'L', 400, '稽核工作底稿', 0, 'app/home/<USER>', '備註', 'system', current timestamp);
--(查核房貸工作底稿)
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324001, '2', 314001, 'L', 100, '查核房貸工作底稿', 0, '', '備註lms190', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334001, '3', 324001, 'L', 100, '編製中(當月資料)', 0, 'app/las/lms1905v01', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334002, '3', 324001, 'L', 200, '編製中(歷史資料)', 0, 'app/las/lms1905v02', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334014, '3', 324001, 'L', 300, '已傳送', 0, 'app/las/lms1905v03', '備註', 'system', current timestamp);
--(查核授信業務工作底稿)
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324002, '2', 314001, 'L', 100, '查核授信業務工作底稿', 0, '', '備註lms192', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334003, '3', 324002, 'L', 100, '編製中(當月資料)', 0, 'app/las/lms1925v01', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334004, '3', 324002, 'L', 200, '編製中(歷史資料)', 0, 'app/las/lms1925v02', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334015, '3', 324002, 'L', 300, '已傳送', 0, 'app/las/lms1925v03', '備註', 'system', current timestamp);
--(查核團體消貸工作底稿)
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324003, '2', 314001, 'L', 100, '查核團體消貸工作底稿', 0, '', '備註lms191', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334005, '3', 324003, 'L', 100, '編製中(當月資料)', 0, 'app/las/lms1915v01', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334006, '3', 324003, 'L', 200, '編製中(歷史資料)', 0, 'app/las/lms1915v02', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334016, '3', 324003, 'L', 300, '已傳送', 0, 'app/las/lms1915v03', '備註', 'system', current timestamp);
--(工作底稿(稽核室))
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324004, '2', 314001, 'L', 100, '工作底稿(稽核室)', 0, '', '備註lms193', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334007, '3', 324004, 'L', 100, '編製中-當月(稽核室)', 0, 'app/las/lms1935v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334008, '3', 324004, 'L', 200, '編製中-歷史(稽核室)', 0, 'app/las/lms1935v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334017, '3', 324004, 'L', 300, '待覆核(稽核室)', 0, 'app/las/lms1935v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334019, '3', 324004, 'L', 400, '已覆核(稽核室)', 0, 'app/las/lms1935v04', '備註', 'system', current timestamp);
--(工作底稿(分行查核))
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324005, '2', 314001, 'L', 100, '工作底稿(分行查核)', 0, '', '備註lms194', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334009, '3', 324005, 'L', 100, '編製中(當月資料)', 0, 'app/las/lms1945v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334010, '3', 324005, 'L', 200, '編製中(歷史資料)', 0, 'app/las/lms1945v02', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334011, '3', 324005, 'L', 300, '待覆核', 0, 'app/las/lms1945v03', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334018, '3', 324005, 'L', 400, '已覆核', 0, 'app/las/lms1945v04', '備註', 'system', current timestamp);
--(查核對帳單)
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (324006, '2', 314001, 'L', 100, '查核對帳單', 0, '', '備註lms195', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334012, '3', 324006, 'L', 100, '個案查核對帳單', 0, 'app/las/lms1955v01', '備註', 'system', current timestamp);
--(廢)--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (334013, '3', 324006, 'L', 200, '房貸查核對帳單', 0, 'app/las/lms1955v02', '備註', 'system', current timestamp);


--##################
--(315_0)管理報表
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (315001, '1',      0, 'L', 500, '管理報表', 0, 'app/home/<USER>', '備註(海外,國內)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (325001, '2', 315001, 'L', 100, '最新資料', 0, 'app/rpt/lms9515v01', '備註', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (325002, '2', 315001, 'L', 200, '歷史資料', 0, 'app/rpt/lms9525v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (325003, '2', 315001, 'L', 300, '關係戶往來彙總查詢', 0, 'app/rpt/lms9535v01', '備註', 'system', current timestamp);


--##################
--(316_0)建檔維護
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (316001, '1',      0, 'L', 600, '建檔維護', 0, 'app/home/<USER>', '備註(海外,國內)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326001, '2', 316001, 'L', 100, '案件分案對照表', 0, 'app/fms/lms7005v00', '備註lms700', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326002, '2', 316001, 'L', 200, '使用者自定表格範本', 0, 'app/fms/lms7205v00', '備註lms720', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326003, '2', 316001, 'L', 300, '動用審核表稽核項目', 0, 'app/fms/lms9015v00', '備註lms901', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326997, '2', 316001, 'L', 997, '客戶名稱查詢與建檔', 0, 'app/fms/misMegaID', 'MegaID(0024)建檔', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326998, '2', 316001, 'L', 998, '指定文件取消覆核', 0, 'app/fms/unApprDoc', '', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (326999, '2', 316001, 'L', 999, '指定文件解除鎖定', 0, 'app/mgt/unlockdoc', '', 'system', current timestamp);


--##################
--(317_0)企金授信(第四階段)
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (317001, '1',      0, 'L', 110, '企金授信', 0, 'app/home/<USER>', '備註(國內分行)', 'system', current timestamp);
----(近期已收案件)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327001, '2', 317001, 'L', 100, '近期已收案件', 0, 'app/lms/lms0000v00', '備註(國內企金)', 'system', current timestamp);
----(待辦案件)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327002, '2', 317001, 'L', 200, '待辦案件', 0, 'app/lms/lms0010v00', '備註(國內企金)', 'system', current timestamp);
----(案件簽報書)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327003, '2', 317001, 'L', 300, '案件簽報書', 0, '', '備註lms120', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337001, '3', 327003, 'L', 100, '編製中', 0, 'app/lms/lms1200v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337002, '3', 327003, 'L', 200, '待覆核', 0, 'app/lms/lms1200v02', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337003, '3', 327003, 'L', 300, '呈授管處/營運中心', 0, 'app/lms/lms1200v03', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337004, '3', 327003, 'L', 400, '待補件/撤件', 0, 'app/lms/lms1200v04', '備註', 'system', current timestamp);
----insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337004, '3', 327003, 'L', 400, '待補件', 0, 'app/lms/lms1200v04', '備註', 'system', current timestamp);
----insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337004, '3', 327003, 'L', 400, '撤件', 0, 'app/lms/lms1200v04', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337005, '3', 327003, 'L', 500, '已核准受理', 0, 'app/lms/lms1200v05', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337006, '3', 327003, 'L', 600, '婉卻', 0, 'app/lms/lms1200v06', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337007, '3', 327003, 'L', 700, '陳復案/陳述案', 0, 'app/lms/lms1200v07', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337008, '3', 327003, 'L', 800, '授信異常通報', 0, 'app/lms/lms1200v08', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337021, '3', 327003, 'L', 110, '會簽中', 0, 'app/lms/lms1200v21', '備註(國外,國金,金控)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337022, '3', 327003, 'L', 120, '已會簽', 0, 'app/lms/lms1200v22', '備註(國外,國金,金控)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337023, '3', 327003, 'L', 130, '提授審會', 0, 'app/lms/lms1200v23', '備註(國外,國金,金控)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337024, '3', 327003, 'L', 140, '提催收會', 0, 'app/lms/lms1200v24', '備註(國外,國金,金控)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337025, '3', 327003, 'L', 150, '提常董會', 0, 'app/lms/lms1200v25', '備註(國外,國金,金控)', 'system', current timestamp);
----(聯行額度明細表)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327004, '2', 317001, 'L', 400, '聯行額度明細表', 0, '', '備註lms141', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337010, '3', 327004, 'L', 100, '聯貸簽核編製中', 0, 'app/lms/lms1410v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337011, '3', 327004, 'L', 200, '聯貸簽核待覆核', 0, 'app/lms/lms1410v02', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337012, '3', 327004, 'L', 300, '已完成聯貸簽核流程', 0, 'app/lms/lms1410v03', '備註', 'system', current timestamp);
----(小放會會議記錄)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327005, '2', 317001, 'L', 500, '小放會會議記錄', 0, 'app/lms/lms1500v00', '備註lms150', 'system', current timestamp);
----(動用審核表)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327006, '2', 317001, 'L', 600, '動用審核表', 0, '', '備註lms160', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337013, '3', 327006, 'L', 100, '編製中', 0, 'app/lms/lms1600v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337014, '3', 327006, 'L', 200, '待覆核', 0, 'app/lms/lms1600v02', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337015, '3', 327006, 'L', 300, '已覆核', 0, 'app/lms/lms1600v03', '備註', 'system', current timestamp);
----(婉卻紀錄查詢)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327007, '2', 317001, 'L', 700, '婉卻紀錄查詢', 0, 'app/lms/rps4030v00', '備註rps4035q02', 'system', current timestamp);
----(修改資料特殊流程)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327008, '2', 317001, 'L', 800, '修改資料特殊流程', 0, '', '備註lms210', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337016, '3', 327008, 'L', 100, '編製中', 0, 'app/lms/lms2100v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337017, '3', 327008, 'L', 200, '待覆核', 0, 'app/lms/lms2100v02', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337018, '3', 327008, 'L', 300, '已覆核', 0, 'app/lms/lms2100v03', '備註', 'system', current timestamp);
----(簽約未動用授信案件送報作業)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327009, '2', 317001, 'L', 900, '簽約未動用授信案件送報作業', 0, '', '備註lms230', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337081, '3', 327009, 'L', 100, '編製中', 0, 'app/lms/lms2300v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337082, '3', 327009, 'L', 200, '待覆核', 0, 'app/lms/lms2300v02', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337083, '3', 327009, 'L', 300, '已覆核', 0, 'app/lms/lms2300v03', '備註', 'system', current timestamp);
--(海外聯貸案會簽)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327010, '2', 317001, 'L', 1000, '海外聯貸案會簽', 0, '', '備註lms121(國外,國金,金控)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337091, '3', 327010, 'L', 100, '會簽中', 0, 'app/lms/lms1215v01', '備註(海外案件)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337092, '3', 327010, 'L', 200, '待放行', 0, 'app/lms/lms1215v02', '備註(海外案件)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (337093, '3', 327010, 'L', 300, '已會簽', 0, 'app/lms/lms1215v03', '備註(海外案件)', 'system', current timestamp);
----(特殊案件登記表)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (327011, '2', 317001, 'L', 1100, '特殊案件登記表', 0, 'app/lms/lms7820v00', '備註lms782(國外,國金,金控)', 'system', current timestamp);


--##################
--(318_0)個金授信(第四階段)
--##################
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (318001, '1', 0, 'L', 120, '個金授信', 0, 'app/home/<USER>', '備註(國內分行)', 'system', current timestamp);
----(近期已收案件)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (328001, '2', 318001, 'L', 100, '近期已收案件', 0, 'app/lms/lms0000v00', '備註(國內個金)', 'system', current timestamp);
----(待辦案件)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (328002, '2', 318001, 'L', 200, '待辦案件', 0, 'app/lms/lms0010v00', '備註(國內個金)', 'system', current timestamp);
----(案件簽報書)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (328003, '2', 318001, 'L', 300, '案件簽報書', 0, '', '備註cls114', 'system', current timestamp);


--##################
--(319_0)授信審查
--##################
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (319001, '1', 0, 'L', 130, '授信審查', 0, 'app/home/<USER>', '備註(海外,國內)', 'system', current timestamp);
--(待收案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329090, '2', 319001, 'L', 90, '待收案件', 0, 'app/lms/lms0020v00', '備註lms002(授管處收件員)', 'system', current timestamp);
--(已收案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329091, '2', 319001, 'L', 91, '已收案件', 0, 'app/lms/lms0030v00', '備註lms003(授管處收件員)', 'system', current timestamp);
--(近期已收案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329001, '2', 319001, 'L', 100, '近期已收案件', 0, 'app/lms/lms0005v00', '備註(營運中心)', 'system', current timestamp);
--(待辦案件)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329002, '2', 319001, 'L', 200, '待辦案件', 0, 'app/lms/lms0015v00', '備註(營運中心)', 'system', current timestamp);
--(案件簽報書)
----(營運中心)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329003, '2', 319001, 'L', 300, '案件簽報書', 0, '', '備註lms120', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339031, '3', 329003, 'L', 3100, '審核中', 0, 'app/lms/lms1200v31', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339032, '3', 329003, 'L', 3200, '待放行/核定', 0, 'app/lms/lms1200v32', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339033, '3', 329003, 'L', 3300, '呈總處', 0, 'app/lms/lms1200v33', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339034, '3', 329003, 'L', 3400, '待更正/分行撤件', 0, 'app/lms/lms1200v34', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339035, '3', 329003, 'L', 3500, '已核准', 0, 'app/lms/lms1200v35', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339036, '3', 329003, 'L', 3600, '已婉卻/待陳復', 0, 'app/lms/lms1200v36', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339037, '3', 329003, 'L', 3700, '陳復案/陳述案', 0, 'app/lms/lms1200v37', '備註(營運中心)', 'system', current timestamp);
----insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339038, '3', 329003, 'L', 3800, '授信異常通報', 0, 'app/lms/lms1200v38', '備註(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339041, '3', 329003, 'L', 3650, '所有提會案件', 0, 'app/lms/lms1200v41', '備註(營運中心)', 'system', current timestamp);
----(授管處)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339051, '3', 329003, 'L', 5100, '審核中', 0, 'app/lms/lms1200v51', '備註(授管處)', 'system', current timestamp);
--(第四階段)
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339061, '3', 329003, 'L', 5110, '已會簽', 0, 'app/lms/lms1200v61', '備註(授管處,國外部案件)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339062, '3', 329003, 'L', 5120, '提授審會', 0, 'app/lms/lms1200v62', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339063, '3', 329003, 'L', 5130, '提催收會', 0, 'app/lms/lms1200v63', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339064, '3', 329003, 'L', 5140, '提常董會', 0, 'app/lms/lms1200v64', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339052, '3', 329003, 'L', 5200, '待放行/核定', 0, 'app/lms/lms1200v52', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339054, '3', 329003, 'L', 5400, '待更正/分行撤件', 0, 'app/lms/lms1200v54', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339055, '3', 329003, 'L', 5500, '已核准', 0, 'app/lms/lms1200v55', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339056, '3', 329003, 'L', 5600, '已婉卻/待陳復', 0, 'app/lms/lms1200v56', '備註(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339057, '3', 329003, 'L', 5700, '免批覆案件', 0, 'app/lms/lms1200v57', '備註(授管處)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339058, '3', 329003, 'L', 5800, '授信異常通報', 0, 'app/lms/lms1200v58', '備註(授管處)', 'system', current timestamp);
--(簽約未動用授信案件送報作業)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329009, '2', 319001, 'L', 900, '簽約未動用授信案件送報作業', 0, '', '備註lms230(授管處)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339084, '3', 329009, 'L', 100, '營業單位傳送', 0, 'app/lms/lms2300v04', '備註', 'system', current timestamp);
--(海外聯貸案會簽)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329010, '2', 319001, 'L', 1000, '海外聯貸案會簽', 0, '', '備註lms121(營運中心)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339091, '3', 329010, 'L', 100, '會簽中', 0, 'app/lms/lms1215v01', '備註(海外案件)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339092, '3', 329010, 'L', 200, '待放行', 0, 'app/lms/lms1215v02', '備註(海外案件)', 'system', current timestamp);
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (339093, '3', 329010, 'L', 300, '已會簽', 0, 'app/lms/lms1215v03', '備註(海外案件)', 'system', current timestamp);
--(特殊案件登記表)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329011, '2', 319001, 'L', 1100, '特殊案件登記表', 0, 'app/lms/lms7820v00', '備註lms782', 'system', current timestamp);
--(簽報紀錄查詢)
insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (329012, '2', 319001, 'L', 1200, '簽報紀錄查詢', 0, 'app/lms/lms7830v00', '備註lms783(授管處)', 'system', current timestamp);


--##################
--(310_0)約據書(第四階段)
--##################
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (310001, '1', 0, 'L', 700, '約據書', 0, 'app/home/<USER>', '備註(國內分行)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (320001, '2', 310001, 'L', 100, '企金約據書', 0, 'app/ctr/lms9990v01', '備註', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (320002, '2', 310001, 'L', 200, '個金約據書', 0, 'app/ctr/lms9990v02', '備註', 'system', current timestamp);


--##################
--(310_1)後台管理
--##################
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (310101, '1', 0, 'L', 800, '後台管理', 0, 'app/home/<USER>', '備註(資訊處)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (320101, '2', 310101, 'L', 100, '後台管理功能1', 0, 'app/mgt/lmsXXX0v01', '備註(資訊處)', 'system', current timestamp);
--insert into COM.BELSPGM(PGMCODE, TYPE, PGMTYP, SYSTYP, SEQ, PGMNAME, PGMAUTH, PGMPATH, PGMDESC, UPDATER, UPDTIME) values (320102, '2', 310101, 'L', 200, '後台管理功能2', 0, 'app/mgt/lmsXXX0v02', '備註(資訊處)', 'system', current timestamp);

