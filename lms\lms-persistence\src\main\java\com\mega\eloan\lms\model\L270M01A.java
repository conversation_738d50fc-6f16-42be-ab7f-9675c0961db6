/* 
 * L270M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金信保央行C申請書 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L270M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId"}))
public class L270M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 來源文件編號<p/>
	 * 來源：<br/>
	 * 	1.資信檢表
	 */
	@Size(max=32)
	@Column(name="REFMAINID", length=32, columnDefinition="CHAR(32)")
	private String refMainId;

	/** 案件編號 **/
	@Size(max=50)
	@Column(name="APPROVENO", length=50, columnDefinition="VARCHAR (50)")
	private String approveNo;

	/** 授信經理人代號 **/
	@Size(max=7)
	@Column(name="MANAGERNO", length=7, columnDefinition="VARCHAR (7)")
	private String managerNo;

	/** 
	 * 申請項目<p/>
	 * 固定塞130
	 */
	@Digits(integer=3, fraction=0, groups = Check.class)
	@Column(name="TYPE", columnDefinition="DECIMAL(3,0)")
	private Integer type;

	/** 
	 * 組織代號<p/>
	 * 1：獨資<br/>
	 * 2：合夥<br/>
	 * 3：有限公司<br/>
	 * 4：股份有限公司<br/>
	 * 18：其他-營業(稅籍)登記<br/>
	 * 27：有限合夥
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="ORGAN", columnDefinition="DECIMAL(2,0)")
	private Integer organ;

	/** 
	 * 負責人姓名<p/>
	 * 後端只收NVARCHAR(30)
	 */
	@Size(max=150)
	@Column(name="OWNER", length=150, columnDefinition="VARCHAR (150)")
	private String owner;

	/** 負責人身份證字號 **/
	@Size(max=10)
	@Column(name="OWNERIDN", length=10, columnDefinition="VARCHAR (10)")
	private String ownerIdn;

	/** 負責人統一證號 **/
	@Size(max=10)
	@Column(name="OWNERFOREIGNERID", length=10, columnDefinition="VARCHAR (10)")
	private String ownerForeignerId;

	/**
	 * 適用之對象<p/>
	 * 1：公司登記、商業登記或有限合夥登記<br/>
	 * 2：稅籍登記
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="QUALICODE", columnDefinition="DECIMAL(1,0)")
	private Integer qualiCode;

	/** 申請額度 **/
	@Digits(integer=14, fraction=0, groups = Check.class)
	@Column(name="APPLYLOAN", columnDefinition="DECIMAL(14,0)")
	private BigDecimal applyLoan;

	/** 
	 * 設立日<p/>
	 * 後端須改傳民國年月七碼
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="TXNDAY", columnDefinition="DATE")
	private Date txnDay;

	/** 
	 * 申貸受理日<p/>
	 * 後端須改傳民國年月七碼
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="APPLYLOANDAY", columnDefinition="DATE")
	private Date applyLoanDay;

	/** 六碼行業代碼 **/
	@Digits(integer=6, fraction=0, groups = Check.class)
	@Column(name="TRAD6", columnDefinition="DECIMAL(6,0)")
	private Integer trad6;

	/** 
	 * 六碼行業代碼版次<p/>
	 * 固定8
	 */
	@Digits(integer=2, fraction=0, groups = Check.class)
	@Column(name="TRAD6ID", columnDefinition="DECIMAL(2,0)")
	private Integer Trad6Id;

	/** 聯絡電話_區碼 **/
	@Size(max=2)
	@Column(name="TEL1", length=2, columnDefinition="VARCHAR(2)")
	private String tel1;

	/** 聯絡電話_號碼 **/
	@Size(max=8)
	@Column(name="TEL2", length=8, columnDefinition="VARCHAR(8)")
	private String tel2;

	/** 負責人行動電話區碼 **/
	@Size(max=4)
	@Column(name="OWNERCELLPHONE1", length=4, columnDefinition="VARCHAR(4)")
	private String ownerCellphone1;

	/** 負責人行動電話號碼 **/
	@Size(max=6)
	@Column(name="OWNERCELLPHONE2", length=6, columnDefinition="VARCHAR(6)")
	private String ownerCellphone2;

	/** 
	 * 有無電子郵箱<p/>
	 * 1：有<br/>
	 * 2：無
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISEMAIL", columnDefinition="DECIMAL(1,0)")
	private Integer isEmail;

	/** 企業電子郵箱 **/
	@Size(max=50)
	@Column(name="EMAIL", length=50, columnDefinition="VARCHAR(50)")
	private String email;

	/** 營業登記_郵遞區號 **/
	@Size(max=5)
	@Column(name="ZIP", length=5, columnDefinition="VARCHAR(5)")
	private String zip;

	/** 營業登記_縣市 **/
	@Size(max=2)
	@Column(name="CITY", length=2, columnDefinition="VARCHAR(2)")
	private String city;

	/** 營業登記_鄉鎮市區 **/
	@Size(max=3)
	@Column(name="DIST", length=3, columnDefinition="VARCHAR(3)")
	private String dist;

	/** 
	 * 營業登記_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	@Size(max=150)
	@Column(name="VILLAGENAME", length=150, columnDefinition="VARCHAR(150)")
	private String villageName;

	/** 
	 * 營業登記_里村<p/>
	 * 1,里<br/>
	 * 2,村
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="VILLAGE", columnDefinition="DECIMAL(1,0)")
	private Integer village;

	/** 營業登記_鄰 **/
	@Digits(integer=4, fraction=0, groups = Check.class)
	@Column(name="NEIGHBORHOOD", columnDefinition="DECIMAL(4,0)")
	private Integer neighborhood;

	/** 
	 * 營業登記_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	@Size(max=60)
	@Column(name="ROADNAME", length=60, columnDefinition="VARCHAR(60)")
	private String roadName;

	/** 
	 * 營業登記_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ROAD", columnDefinition="DECIMAL(1,0)")
	private Integer road;

	/** 
	 * 營業登記_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="SEC", length=12, columnDefinition="VARCHAR(12)")
	private String sec;

	/** 
	 * 營業登記_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="LANE", length=24, columnDefinition="VARCHAR(24)")
	private String lane;

	/** 
	 * 營業登記_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	@Size(max=24)
	@Column(name="ALLEY", length=24, columnDefinition="VARCHAR(24)")
	private String alley;

	/** 
	 * 營業登記_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO1", length=12, columnDefinition="VARCHAR(12)")
	private String no1;

	/** 
	 * 營業登記_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	@Size(max=12)
	@Column(name="NO2", length=12, columnDefinition="VARCHAR(12)")
	private String no2;

	/** 
	 * 營業登記_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR1", length=9, columnDefinition="VARCHAR(9)")
	private String floor1;

	/** 
	 * 營業登記_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="FLOOR2", length=9, columnDefinition="VARCHAR(9)")
	private String floor2;

	/** 
	 * 營業登記_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	@Size(max=9)
	@Column(name="ROOM", length=9, columnDefinition="VARCHAR(9)")
	private String room;

	/** 
	 * 申請書鍵入者號碼<p/>
	 * 徵信經辦<br/>整批傳送”銀行經辦”同資料
	 */
	@Size(max=6)
	@Column(name="IMPORTERNO", length=6, columnDefinition="CHAR(6)")
	private String importerNo;

	/** 
	 * 申請書鍵入者姓名<p/>
	 * 前端只能輸10個字(不管全半型)
	 */
	@Size(max=30)
	@Column(name="IMPORTERNAME", length=30, columnDefinition="VARCHAR(30)")
	private String importerName;

	/** 申請書鍵入者行動電話區碼 **/
	@Size(max=4)
	@Column(name="IMPORTERCELLPHONE1", length=4, columnDefinition="VARCHAR(4)")
	private String importerCellphone1;

	/** 申請書鍵入者行動電話號碼 **/
	@Size(max=6)
	@Column(name="IMPORTERCELLPHONE2", length=6, columnDefinition="VARCHAR(6)")
	private String importerCellphone2;

	/** 申請書鍵入者電話區碼 **/
	@Size(max=2)
	@Column(name="IMPORTERTEL1", length=2, columnDefinition="VARCHAR(2)")
	private String importerTel1;

	/** 申請書鍵入者電話號碼 **/
	@Size(max=8)
	@Column(name="IMPORTERTEL2", length=8, columnDefinition="VARCHAR(8)")
	private String importerTel2;

	/** 申請書鍵入者電話分機 **/
	@Size(max=5)
	@Column(name="IMPORTERTELEXT", length=5, columnDefinition="VARCHAR(5)")
	private String importerTelExt;

	/** 申請書鍵入者電子信箱 **/
	@Size(max=50)
	@Column(name="IMPORTEREMAIL", length=50, columnDefinition="VARCHAR (50)")
	private String importerEmail;

	/** 
	 * 本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISCREDITCHECK", columnDefinition="DECIMAL(1,0)")
	private Integer isCreditCheck;

	/** 
	 * 本項額度是/否擬以中央銀行辦理銀行承作受嚴重特殊傳染性肺炎疫情影響之中小企業貸款專案通融作業辦理<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISNCOVPROJECT", columnDefinition="DECIMAL(1,0)")
	private Integer isnCovProject;

	/** 
	 * 本行已評估申請者受疫情形<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISNCOVEFFECT", columnDefinition="DECIMAL(1,0)")
	private Integer isnCovEffect;

	/** 
	 * 申請者有稅籍登記且每月銷售額未達使用統一發票標準<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISTAXBAN", columnDefinition="DECIMAL(1,0)")
	private Integer isTaxBan;

	/** 
	 * 企業及負責人票、債信情形正常<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISCREDITABNORMAL", columnDefinition="DECIMAL(1,0)")
	private Integer isCreditAbnormal;

	/** 
	 * 依中央銀行最近增修之銀行簡易評分表評分達標準者<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISOVERSIMPLE70", columnDefinition="DECIMAL(1,0)")
	private Integer isOverSimple70;

	/** 
	 * 本案屬營運週轉金，且動用方式為一次動用或分批動用<p/>
	 * 1：是<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISOPERATING", columnDefinition="DECIMAL(1,0)")
	private Integer isOperating;

	/** 通融期間內貸款利率 **/
	@Digits(integer=10, fraction=6, groups = Check.class)
	@Column(name="BEFORERATE", columnDefinition="DECIMAL(10,6)")
	private BigDecimal beforeRate;

	/** 通融期間後貸款利率 **/
	@Digits(integer=10, fraction=6, groups = Check.class)
	@Column(name="AFTERRATE", columnDefinition="DECIMAL(10,6)")
	private BigDecimal afterRate;

	/** 
	 * 有無其他實際負責(經營)人<p/>
	 * 1：有<br/>
	 *  2：無
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISREALOWNER", columnDefinition="DECIMAL(1,0)")
	private Integer isRealOwner;

	/** 
	 * 其他實際負責(經營)人姓名<p/>
	 * 前端只能輸10個字(不管全半型)
	 */
	@Size(max=150)
	@Column(name="REALOWNER", length=150, columnDefinition="VARCHAR (150)")
	private String realOwner;

	/** 其他實際負責(經營)人身份證字號 **/
	@Size(max=10)
	@Column(name="REALOWNERIDN", length=10, columnDefinition="VARCHAR (10)")
	private String realOwnerIdn;

	/** 其他實際負責(經營)人統一證號 **/
	@Size(max=10)
	@Column(name="REALOWNERFOREIGNERID", length=10, columnDefinition="VARCHAR (10)")
	private String realOwnerForeignerId;

	/** 
	 * 持有大量閒置工業用地或持有未登記工廠<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISOWNIDLE", columnDefinition="DECIMAL(1,0)")
	private Integer isOwnIdle;

	/** 
	 * 本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 * 1：是<br/>
	 *  2：否<br/>固定1 ?
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="AGREEMENT", columnDefinition="DECIMAL(1,0)")
	private Integer agreement;

	/**
	 * 本行已實地訪查，並確認借戶營業所在地與登記地址或實際營業地址相符<p/>
	 * 1：是<br/>
	 *  2：否<br/>固定1
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISCHECKADDRESS", columnDefinition="DECIMAL(1,0)")
	private Integer isCheckAddress;

	/**
	 * 借戶持有未登記工廠<p/>
	 * 1：是<br/>
	 *  2：否<br/>
	 *  介接限選2；若此欄為1請由網頁進件<br/>
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISUNREGISTEREDFACTORY", columnDefinition="DECIMAL(1,0)")
	private Integer isUnregisteredFactory;

	/**
	 * 借戶符合109.7.9經授中字第10930058390號函說明二之紓困、振興資格<p/>
	 * 1：是<br/>
	 *  2：否<br/>
	 *  ”持有未登記工廠”為”是”時，才需要傳送此欄
	 */
	@Digits(integer=1, fraction=0, groups = Check.class)
	@Column(name="ISELIGIBLE", columnDefinition="DECIMAL(1,0)")
	private Integer isEligible;

	/** 
	 * 保證案號<p/>
	 * 後端回壓欄位
	 */
	@Digits(integer=10, fraction=0, groups = Check.class)
	@Column(name="GRNTPAPER", columnDefinition="DECIMAL(10,0)")
	private Integer grntPaper;

	/** 
	 * 回覆日<p/>
	 * 後端回壓欄位
	 */
	@Column(name="RECEIVEDAY", columnDefinition="TIMESTAMP")
	private Timestamp receiveDay;

	/** 
	 * 回覆結果<p/>
	 * 後端回壓欄位
	 */
	@Size(max=3)
	@Column(name="DATASTATUS", length=3, columnDefinition="VARCHAR(3)")
	private String dataStatus;

	/** 
	 * 處理說明<p/>
	 * 後端回壓欄位
	 */
	@Size(max=600)
	@Column(name="DESCRIPTION", length=600, columnDefinition="VARCHAR(600)")
	private String description;

	/** 
	 * 檔案產生日<p/>
	 * 後端回壓欄位
	 */
	@Column(name="BATCHDATE", columnDefinition="TIMESTAMP")
	private Timestamp batchDate;

	/**
	 * 取得來源文件編號<p/>
	 * 來源：<br/>
	 * 	1.資信檢表
	 */
	public String getRefMainId() {
		return this.refMainId;
	}
	/**
	 *  設定來源文件編號<p/>
	 *  來源：<br/>
	 *  	1.資信檢表
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}

	/** 取得案件編號 **/
	public String getApproveNo() {
		return this.approveNo;
	}
	/** 設定案件編號 **/
	public void setApproveNo(String value) {
		this.approveNo = value;
	}

	/** 取得授信經理人代號 **/
	public String getManagerNo() {
		return this.managerNo;
	}
	/** 設定授信經理人代號 **/
	public void setManagerNo(String value) {
		this.managerNo = value;
	}

	/** 
	 * 取得申請項目<p/>
	 * 固定塞130
	 */
	public Integer getType() {
		return this.type;
	}
	/**
	 *  設定申請項目<p/>
	 *  固定塞130
	 **/
	public void setType(Integer value) {
		this.type = value;
	}

	/** 
	 * 取得組織代號<p/>
	 * 1：獨資<br/>
	 * 2：合夥<br/>
	 * 3：有限公司<br/>
	 * 4：股份有限公司<br/>
	 * 18：其他-營業(稅籍)登記<br/>
	 * 27：有限合夥
	 */
	public Integer getOrgan() {
		return this.organ;
	}
	/**
	 *  設定組織代號<p/>
	 *  1：獨資<br/>
	 *  2：合夥<br/>
	 *  3：有限公司<br/>
	 *  4：股份有限公司<br/>
	 *  18：其他-營業(稅籍)登記<br/>
	 *  27：有限合夥
	 **/
	public void setOrgan(Integer value) {
		this.organ = value;
	}

	/** 
	 * 取得負責人姓名<p/>
	 * 後端只收NVARCHAR(30)
	 */
	public String getOwner() {
		return this.owner;
	}
	/**
	 *  設定負責人姓名<p/>
	 *  後端只收NVARCHAR(30)
	 **/
	public void setOwner(String value) {
		this.owner = value;
	}

	/** 取得負責人身份證字號 **/
	public String getOwnerIdn() {
		return this.ownerIdn;
	}
	/** 設定負責人身份證字號 **/
	public void setOwnerIdn(String value) {
		this.ownerIdn = value;
	}

	/** 取得負責人統一證號 **/
	public String getOwnerForeignerId() {
		return this.ownerForeignerId;
	}
	/** 設定負責人統一證號 **/
	public void setOwnerForeignerId(String value) {
		this.ownerForeignerId = value;
	}

	/**
	 * 取得適用之對象<p/>
	 * 1：公司登記、商業登記或有限合夥登記<br/>
	 * 2：稅籍登記
	 */
	public Integer getQualiCode() {
		return this.qualiCode;
	}
	/**
	 *  設定適用之對象<p/>
	 *  1：公司登記、商業登記或有限合夥登記<br/>
	 *  2：稅籍登記
	 **/
	public void setQualiCode(Integer value) {
		this.qualiCode = value;
	}

	/** 取得申請額度 **/
	public BigDecimal getApplyLoan() {
		return this.applyLoan;
	}
	/** 設定申請額度 **/
	public void setApplyLoan(BigDecimal value) {
		this.applyLoan = value;
	}

	/** 
	 * 取得設立日<p/>
	 * 後端須改傳民國年月七碼
	 */
	public Date getTxnDay() {
		return this.txnDay;
	}
	/**
	 *  設定設立日<p/>
	 *  後端須改傳民國年月七碼
	 **/
	public void setTxnDay(Date value) {
		this.txnDay = value;
	}

	/** 
	 * 取得申貸受理日<p/>
	 * 後端須改傳民國年月七碼
	 */
	public Date getApplyLoanDay() {
		return this.applyLoanDay;
	}
	/**
	 *  設定申貸受理日<p/>
	 *  後端須改傳民國年月七碼
	 **/
	public void setApplyLoanDay(Date value) {
		this.applyLoanDay = value;
	}

	/** 取得六碼行業代碼 **/
	public Integer getTrad6() {
		return this.trad6;
	}
	/** 設定六碼行業代碼 **/
	public void setTrad6(Integer value) {
		this.trad6 = value;
	}

	/** 
	 * 取得六碼行業代碼版次<p/>
	 * 固定8
	 */
	public Integer getTrad6Id() {
		return this.Trad6Id;
	}
	/**
	 *  設定六碼行業代碼版次<p/>
	 *  固定8
	 **/
	public void setTrad6Id(Integer value) {
		this.Trad6Id = value;
	}

	/** 取得聯絡電話_區碼 **/
	public String getTel1() {
		return this.tel1;
	}
	/** 設定聯絡電話_區碼 **/
	public void setTel1(String value) {
		this.tel1 = value;
	}

	/** 取得聯絡電話_號碼 **/
	public String getTel2() {
		return this.tel2;
	}
	/** 設定聯絡電話_號碼 **/
	public void setTel2(String value) {
		this.tel2 = value;
	}

	/** 取得負責人行動電話區碼 **/
	public String getOwnerCellphone1() {
		return this.ownerCellphone1;
	}
	/** 設定負責人行動電話區碼 **/
	public void setOwnerCellphone1(String value) {
		this.ownerCellphone1 = value;
	}

	/** 取得負責人行動電話號碼 **/
	public String getOwnerCellphone2() {
		return this.ownerCellphone2;
	}
	/** 設定負責人行動電話號碼 **/
	public void setOwnerCellphone2(String value) {
		this.ownerCellphone2 = value;
	}

	/** 
	 * 取得有無電子郵箱<p/>
	 * 1：有<br/>
	 *  2：無
	 */
	public Integer getIsEmail() {
		return this.isEmail;
	}
	/**
	 *  設定有無電子郵箱<p/>
	 *  1：有<br/>
	 *  2：無
	 **/
	public void setIsEmail(Integer value) {
		this.isEmail = value;
	}

	/** 取得企業電子郵箱 **/
	public String getEmail() {
		return this.email;
	}
	/** 設定企業電子郵箱 **/
	public void setEmail(String value) {
		this.email = value;
	}

	/** 取得營業登記_郵遞區號 **/
	public String getZip() {
		return this.zip;
	}
	/** 設定營業登記_郵遞區號 **/
	public void setZip(String value) {
		this.zip = value;
	}

	/** 取得營業登記_縣市 **/
	public String getCity() {
		return this.city;
	}
	/** 設定營業登記_縣市 **/
	public void setCity(String value) {
		this.city = value;
	}

	/** 取得營業登記_鄉鎮市區 **/
	public String getDist() {
		return this.dist;
	}
	/** 設定營業登記_鄉鎮市區 **/
	public void setDist(String value) {
		this.dist = value;
	}

	/** 
	 * 取得營業登記_里村名<p/>
	 * 前端只能輸入50個字(不管全半型)
	 */
	public String getVillageName() {
		return this.villageName;
	}
	/**
	 *  設定營業登記_里村名<p/>
	 *  前端只能輸入50個字(不管全半型)
	 **/
	public void setVillageName(String value) {
		this.villageName = value;
	}

	/** 
	 * 取得營業登記_里村<p/>
	 * 1,里<br/>
	 *  2,村
	 */
	public Integer getVillage() {
		return this.village;
	}
	/**
	 *  設定營業登記_里村<p/>
	 *  1,里<br/>
	 *  2,村
	 **/
	public void setVillage(Integer value) {
		this.village = value;
	}

	/** 取得營業登記_鄰 **/
	public Integer getNeighborhood() {
		return this.neighborhood;
	}
	/** 設定營業登記_鄰 **/
	public void setNeighborhood(Integer value) {
		this.neighborhood = value;
	}

	/** 
	 * 取得營業登記_路/街名<p/>
	 * 前端只能輸入20個字(不管全半型)
	 */
	public String getRoadName() {
		return this.roadName;
	}
	/**
	 *  設定營業登記_路/街名<p/>
	 *  前端只能輸入20個字(不管全半型)
	 **/
	public void setRoadName(String value) {
		this.roadName = value;
	}

	/** 
	 * 取得營業登記_路<p/>
	 * 1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 */
	public Integer getRoad() {
		return this.road;
	}
	/**
	 *  設定營業登記_路<p/>
	 *  1,(空白)<br/>
	 *  2,路<br/>
	 *  3,街
	 **/
	public void setRoad(Integer value) {
		this.road = value;
	}

	/** 
	 * 取得營業登記_段<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getSec() {
		return this.sec;
	}
	/**
	 *  設定營業登記_段<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setSec(String value) {
		this.sec = value;
	}

	/** 
	 * 取得營業登記_巷<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getLane() {
		return this.lane;
	}
	/**
	 *  設定營業登記_巷<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setLane(String value) {
		this.lane = value;
	}

	/** 
	 * 取得營業登記_弄<p/>
	 * 前端只能輸入8個字(不管全半型)
	 */
	public String getAlley() {
		return this.alley;
	}
	/**
	 *  設定營業登記_弄<p/>
	 *  前端只能輸入8個字(不管全半型)
	 **/
	public void setAlley(String value) {
		this.alley = value;
	}

	/** 
	 * 取得營業登記_號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo1() {
		return this.no1;
	}
	/**
	 *  設定營業登記_號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo1(String value) {
		this.no1 = value;
	}

	/** 
	 * 取得營業登記_之號<p/>
	 * 前端只能輸入4個字(不管全半型)
	 */
	public String getNo2() {
		return this.no2;
	}
	/**
	 *  設定營業登記_之號<p/>
	 *  前端只能輸入4個字(不管全半型)
	 **/
	public void setNo2(String value) {
		this.no2 = value;
	}

	/** 
	 * 取得營業登記_樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor1() {
		return this.floor1;
	}
	/**
	 *  設定營業登記_樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor1(String value) {
		this.floor1 = value;
	}

	/** 
	 * 取得營業登記_之樓<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getFloor2() {
		return this.floor2;
	}
	/**
	 *  設定營業登記_之樓<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setFloor2(String value) {
		this.floor2 = value;
	}

	/** 
	 * 取得營業登記_室<p/>
	 * 前端只能輸入3個字(不管全半型)
	 */
	public String getRoom() {
		return this.room;
	}
	/**
	 *  設定營業登記_室<p/>
	 *  前端只能輸入3個字(不管全半型)
	 **/
	public void setRoom(String value) {
		this.room = value;
	}

	/** 
	 * 取得申請書鍵入者號碼<p/>
	 * 徵信經辦整批傳送”銀行經辦”同資料
	 */
	public String getImporterNo() {
		return this.importerNo;
	}
	/**
	 *  設定申請書鍵入者號碼<p/>
	 *  徵信經辦整批傳送”銀行經辦”同資料
	 **/
	public void setImporterNo(String value) {
		this.importerNo = value;
	}

	/** 
	 * 取得申請書鍵入者姓名<p/>
	 * 前端只能輸10個字(不管全半型)
	 */
	public String getImporterName() {
		return this.importerName;
	}
	/**
	 *  設定申請書鍵入者姓名<p/>
	 *  前端只能輸10個字(不管全半型)
	 **/
	public void setImporterName(String value) {
		this.importerName = value;
	}

	/** 取得申請書鍵入者行動電話區碼 **/
	public String getImporterCellphone1() {
		return this.importerCellphone1;
	}
	/** 設定申請書鍵入者行動電話區碼 **/
	public void setImporterCellphone1(String value) {
		this.importerCellphone1 = value;
	}

	/** 取得申請書鍵入者行動電話號碼 **/
	public String getImporterCellphone2() {
		return this.importerCellphone2;
	}
	/** 設定申請書鍵入者行動電話號碼 **/
	public void setImporterCellphone2(String value) {
		this.importerCellphone2 = value;
	}

	/** 取得申請書鍵入者電話區碼 **/
	public String getImporterTel1() {
		return this.importerTel1;
	}
	/** 設定申請書鍵入者電話區碼 **/
	public void setImporterTel1(String value) {
		this.importerTel1 = value;
	}

	/** 取得申請書鍵入者電話號碼 **/
	public String getImporterTel2() {
		return this.importerTel2;
	}
	/** 設定申請書鍵入者電話號碼 **/
	public void setImporterTel2(String value) {
		this.importerTel2 = value;
	}

	/** 取得申請書鍵入者電話分機 **/
	public String getImporterTelExt() {
		return this.importerTelExt;
	}
	/** 設定申請書鍵入者電話分機 **/
	public void setImporterTelExt(String value) {
		this.importerTelExt = value;
	}

	/** 取得申請書鍵入者電子信箱 **/
	public String getImporterEmail() {
		return this.importerEmail;
	}
	/** 設定申請書鍵入者電子信箱 **/
	public void setImporterEmail(String value) {
		this.importerEmail = value;
	}

	/** 
	 * 取得本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsCreditCheck() {
		return this.isCreditCheck;
	}
	/**
	 *  設定本行依移送信用保證之程序規定辦理，填寫申請書前已先行辦妥徵信<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsCreditCheck(Integer value) {
		this.isCreditCheck = value;
	}

	/** 
	 * 取得本項額度是/否擬以中央銀行辦理銀行承作受嚴重特殊傳染性肺炎疫情影響之中小企業貸款專案通融作業辦理<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsnCovProject() {
		return this.isnCovProject;
	}
	/**
	 *  設定本項額度是/否擬以中央銀行辦理銀行承作受嚴重特殊傳染性肺炎疫情影響之中小企業貸款專案通融作業辦理<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsnCovProject(Integer value) {
		this.isnCovProject = value;
	}

	/** 
	 * 取得本行已評估申請者受疫情形<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsnCovEffect() {
		return this.isnCovEffect;
	}
	/**
	 *  設定本行已評估申請者受疫情形<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsnCovEffect(Integer value) {
		this.isnCovEffect = value;
	}

	/** 
	 * 取得申請者有稅籍登記且每月銷售額未達使用統一發票標準<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsTaxBan() {
		return this.isTaxBan;
	}
	/**
	 *  設定申請者有稅籍登記且每月銷售額未達使用統一發票標準<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsTaxBan(Integer value) {
		this.isTaxBan = value;
	}

	/** 
	 * 取得企業及負責人票、債信情形正常<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsCreditAbnormal() {
		return this.isCreditAbnormal;
	}
	/**
	 *  設定企業及負責人票、債信情形正常<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsCreditAbnormal(Integer value) {
		this.isCreditAbnormal = value;
	}

	/** 
	 * 取得依中央銀行最近增修之銀行簡易評分表評分達標準者<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsOverSimple70() {
		return this.isOverSimple70;
	}
	/**
	 *  設定依中央銀行最近增修之銀行簡易評分表評分達標準者<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsOverSimple70(Integer value) {
		this.isOverSimple70 = value;
	}

	/** 
	 * 取得本案屬營運週轉金，且動用方式為一次動用或分批動用<p/>
	 * 1：是<br/>固定1
	 */
	public Integer getIsOperating() {
		return this.isOperating;
	}
	/**
	 *  設定本案屬營運週轉金，且動用方式為一次動用或分批動用<p/>
	 *  1：是<br/>固定1
	 **/
	public void setIsOperating(Integer value) {
		this.isOperating = value;
	}

	/** 取得通融期間內貸款利率 **/
	public BigDecimal getBeforeRate() {
		return this.beforeRate;
	}
	/** 設定通融期間內貸款利率 **/
	public void setBeforeRate(BigDecimal value) {
		this.beforeRate = value;
	}

	/** 取得通融期間後貸款利率 **/
	public BigDecimal getAfterRate() {
		return this.afterRate;
	}
	/** 設定通融期間後貸款利率 **/
	public void setAfterRate(BigDecimal value) {
		this.afterRate = value;
	}

	/** 
	 * 取得有無其他實際負責(經營)人<p/>
	 * 1：有<br/>
	 *  2：無
	 */
	public Integer getIsRealOwner() {
		return this.isRealOwner;
	}
	/**
	 *  設定有無其他實際負責(經營)人<p/>
	 *  1：有<br/>
	 *  2：無
	 **/
	public void setIsRealOwner(Integer value) {
		this.isRealOwner = value;
	}

	/** 
	 * 取得其他實際負責(經營)人姓名<p/>
	 * 前端只能輸10個字(不管全半型)
	 */
	public String getRealOwner() {
		return this.realOwner;
	}
	/**
	 *  設定其他實際負責(經營)人姓名<p/>
	 *  前端只能輸10個字(不管全半型)
	 **/
	public void setRealOwner(String value) {
		this.realOwner = value;
	}

	/** 取得其他實際負責(經營)人身份證字號 **/
	public String getRealOwnerIdn() {
		return this.realOwnerIdn;
	}
	/** 設定其他實際負責(經營)人身份證字號 **/
	public void setRealOwnerIdn(String value) {
		this.realOwnerIdn = value;
	}

	/** 取得其他實際負責(經營)人統一證號 **/
	public String getRealOwnerForeignerId() {
		return this.realOwnerForeignerId;
	}
	/** 設定其他實際負責(經營)人統一證號 **/
	public void setRealOwnerForeignerId(String value) {
		this.realOwnerForeignerId = value;
	}

	/** 
	 * 取得持有大量閒置工業用地或持有未登記工廠<p/>
	 * 1：是<br/>
	 *  2：否
	 */
	public Integer getIsOwnIdle() {
		return this.isOwnIdle;
	}
	/**
	 *  設定持有大量閒置工業用地或持有未登記工廠<p/>
	 *  1：是<br/>
	 *  2：否
	 **/
	public void setIsOwnIdle(Integer value) {
		this.isOwnIdle = value;
	}

	/** 
	 * 取得本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 * 1：是<br/>
	 *  2：否<br/>固定1 ?
	 */
	public Integer getAgreement() {
		return this.agreement;
	}
	/**
	 *  設定本行已對個資當事人進行「信保基金蒐集、處理及利用個人資料告知書」聲明<p/>
	 *  1：是<br/>
	 *  2：否<br/>固定1 ?
	 **/
	public void setAgreement(Integer value) {
		this.agreement = value;
	}

	/**
	 * 取得本行已實地訪查，並確認借戶營業所在地與登記地址或實際營業地址相符<p/>
	 * 1：是<br/>
	 *  2：否<br/>固定1
	 */
	public Integer getIsCheckAddress() {
		return this.isCheckAddress;
	}
	/**
	 *  設定本行已實地訪查，並確認借戶營業所在地與登記地址或實際營業地址相符<p/>
	 *  1：是<br/>
	 *  2：否<br/>固定1
	 **/
	public void setIsCheckAddress(Integer value) {
		this.isCheckAddress = value;
	}

	/**
	 * 取得借戶持有未登記工廠<p/>
	 * 1：是<br/>
	 *  2：否<br/>
	 *  介接限選2；若此欄為1請由網頁進件
	 */
	public Integer getIsUnregisteredFactory() {
		return this.isUnregisteredFactory;
	}
	/**
	 *  設定借戶持有未登記工廠<p/>
	 *  1：是<br/>
	 *  2：否<br/>
	 * 	介接限選2；若此欄為1請由網頁進件
	 **/
	public void setIsUnregisteredFactory(Integer value) {
		this.isUnregisteredFactory = value;
	}

	/**
	 * 取得借戶符合109.7.9經授中字第10930058390號函說明二之紓困、振興資格<p/>
	 * 1：是<br/>
	 *  2：否<br/>
	 *  ”持有未登記工廠”為”是”時，才需要傳送此欄
	 */
	public Integer getIsEligible() {
		return this.isEligible;
	}
	/**
	 *  設定借戶符合109.7.9經授中字第10930058390號函說明二之紓困、振興資格<p/>
	 *  1：是<br/>
	 *  2：否<br/>
	 * 	”持有未登記工廠”為”是”時，才需要傳送此欄
	 **/
	public void setIsEligible(Integer value) {
		this.isEligible = value;
	}

	/** 
	 * 取得保證案號<p/>
	 * 後端回壓欄位
	 */
	public Integer getGrntPaper() {
		return this.grntPaper;
	}
	/**
	 *  設定保證案號<p/>
	 *  後端回壓欄位
	 **/
	public void setGrntPaper(Integer value) {
		this.grntPaper = value;
	}

	/** 
	 * 取得回覆日<p/>
	 * 後端回壓欄位
	 */
	public Timestamp getReceiveDay() {
		return this.receiveDay;
	}
	/**
	 *  設定回覆日<p/>
	 *  後端回壓欄位
	 **/
	public void setReceiveDay(Timestamp value) {
		this.receiveDay = value;
	}

	/** 
	 * 取得回覆結果<p/>
	 * 後端回壓欄位
	 */
	public String getDataStatus() {
		return this.dataStatus;
	}
	/**
	 *  設定回覆結果<p/>
	 *  後端回壓欄位
	 **/
	public void setDataStatus(String value) {
		this.dataStatus = value;
	}

	/** 
	 * 取得處理說明<p/>
	 * 後端回壓欄位
	 */
	public String getDescription() {
		return this.description;
	}
	/**
	 *  設定處理說明<p/>
	 *  後端回壓欄位
	 **/
	public void setDescription(String value) {
		this.description = value;
	}

	/** 
	 * 取得檔案產生日<p/>
	 * 後端回壓欄位
	 */
	public Timestamp getBatchDate() {
		return this.batchDate;
	}
	/**
	 *  設定檔案產生日<p/>
	 *  後端回壓欄位
	 **/
	public void setBatchDate(Timestamp value) {
		this.batchDate = value;
	}
}
