package tw.com.jcs.flow.node;

import java.lang.reflect.Method;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ognl.Ognl;
import ognl.OgnlException;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.core.FlowInstanceImpl;

/**
 * <pre>
 * 流程節點(決定)
 * </pre>
 * 
 * @since 2023年1月9日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2023年1月9日
 *          </ul>
 */
public class DecisionNode extends FlowNode {

    static final Logger logger = LoggerFactory.getLogger(DecisionNode.class);

    public static final String DECIDED_NEXT_NODE = "decided_next_node";

    Object expression;

    /**
     * 設定為OGNL表達式
     * 
     * @param expression
     */
    public void setExpression(String expression) {
        if (expression != null) {
            try {
                this.expression = Ognl.parseExpression(expression);
            } catch (OgnlException e) {
                throw new FlowException(e);
            }
        }
    }

    /**
     * 獲取由指定根物件上的給定預編譯表達式表示的值，如果沒有執行結果，則使用預設的路徑
     * 
     * @param instance
     * @return
     */
    public String execute(FlowInstanceImpl instance) {
        String value = null;
        try {
            if (expression != null) {
                // 為了弱掃做此調整, 將 Ognl 取值改用 reflection 方式呼叫
                // value = Ognl.getValue(expression, instance.getData(), ScriptNode.createContext(instance)).toString();
                Method method = Ognl.class.getDeclaredMethod("getValue", Object.class, Map.class, Object.class);
                value = method.invoke(null, expression, instance.getData(), ScriptNode.createContext(instance)).toString();

                logger.debug("[{}#{}] Expression: `{}`, result: `{}`", new Object[] { instance.getId(), instance.getSeq(), expression, value });
            }
            // 如果沒有執行結果，則使用預設的路徑
            if (value == null) {
                value = defaultTransition != null ? defaultTransition : transitions.keySet().iterator().next();
                logger.debug("[{}#{}] No expression, use default path `{}`", new Object[] { instance.getId(), instance.getSeq(), value });
            }
        } catch (Exception e) {
            throw new FlowException(e);
        }
        return value;
    }

    /**
     * 找到目前的流程節點，若有下一個節點則執行下一個節點
     * 
     * @param instance
     */
    @Override
    public void next(FlowInstanceImpl instance) {
        instance.handle();

        String value = execute(instance);

        instance.setAttribute(DECIDED_NEXT_NODE, value);
        FlowNode nextNode = instance.getDefinition().getNodes().get(getTransitions().get(value));
        // 有nextNode才執行finishCurrentNode與changeToThisNode
        // 若無則保留在原node，以避免下一次執行flow時，無step可執行
        if (nextNode != null) {
            finishCurrentNode(instance);
            changeToThisNode(instance);
            nextNode.next(instance);
        }

    }

}
