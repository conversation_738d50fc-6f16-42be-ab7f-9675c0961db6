/* 
 * LMS1505V00Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.pages;

import java.util.ArrayList;
import java.util.Properties;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.auth.AuthService;
import tw.com.jcs.auth.AuthType;

/**
 * <pre>
 * 小放會grid畫面
 * </pre>
 * 
 * @since 2011/10/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/21,REX,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms1505v00")
public class LMS1505V00Page extends AbstractEloanInnerView {

	/*
	 * public LMS1505V00Page(PageParameters parameters) { super(parameters);
	 * 
	 * // J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式 //
	 * eloan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式 // 1.「帳戶管理員」改「覆核」 // 2.「紀錄」改「經辦」 //
	 * 3.取消「遵守法令主管」 Properties prop =
	 * MessageBundleScriptCreator.getComponentResource(LMS1505V00Page.class);
	 * 
	 * MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails(); if
	 * (user.getUnitNo().equals(UtilConstants.BankNo.授管處)) { add(new
	 * Label("L150M01a.accounting", prop.getProperty("L150M01a.approver"))); // 覆核
	 * add(new Label("L150M01a.recorder", prop.getProperty("L150M01a.appraiser")));
	 * // 經辦 } else { add(new Label("L150M01a.accounting",
	 * prop.getProperty("l150m01a.accounting"))); // 帳戶管理員 add(new
	 * Label("L150M01a.recorder", prop.getProperty("l150m01a.recorder"))); // 紀錄 }
	 * 
	 * }
	 */

	@Autowired
	AuthService au;

	@Override
	public void execute(ModelMap model, PageParameters params) {

		setGridViewStatus(CreditDocStatusEnum.海外_編製中);
		// 加上Button
//		if (this.getAuth(AuthType.Accept)) {
//			addToButtonPanel(model, LmsButtonEnum.View);
//		}
//
//		if (this.getAuth(AuthType.Modify)) {
//			addToButtonPanel(model, LmsButtonEnum.Add, LmsButtonEnum.Delete, LmsButtonEnum.Modify);
//		}
//		
//		addToButtonPanel(model, LmsButtonEnum.Filter);

		// J-112-0057_05097_B1001 Web e-Loan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// eloan授信管理系統, 調整授信審查處之小放會會議紀錄欄項名稱及格式
		// 1.「帳戶管理員」改「覆核」
		// 2.「紀錄」改「經辦」
		// 3.取消「遵守法令主管」
		Properties prop = MessageBundleScriptCreator.getComponentResource(LMS1505V00Page.class);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (user.getUnitNo().equals(UtilConstants.BankNo.授管處)) {
			model.addAttribute("L150M01a.accounting", prop.getProperty("L150M01a.approver")); // 覆核
			model.addAttribute("L150M01a.recorder", prop.getProperty("L150M01a.appraiser")); // 經辦
		} else {
			model.addAttribute("L150M01a.accounting", prop.getProperty("l150m01a.accounting")); // 帳戶管理員
			model.addAttribute("L150M01a.recorder", prop.getProperty("l150m01a.recorder")); // 紀錄
		}
		
		// 加上Button
		ArrayList<LmsButtonEnum> btns = new ArrayList<LmsButtonEnum>();
		if (this.getAuth(AuthType.Accept)) {
			btns.add(LmsButtonEnum.View);
		}

		if (this.getAuth(AuthType.Modify)) {
			btns.add(LmsButtonEnum.Add);
			btns.add(LmsButtonEnum.Delete);
			btns.add(LmsButtonEnum.Modify);
		}

		btns.add(LmsButtonEnum.Filter);

		addToButtonPanel(model, btns.toArray(new LmsButtonEnum[] {}));

		renderJsI18N(LMS1505M01Page.class);
		renderJsI18N(LMS1505V00Page.class);
		renderJsI18N(AbstractEloanPage.class);
		renderJsI18N(LMS1205V01Page.class);
		renderJsI18N(LMSCommomPage.class);

		model.addAttribute("loadScript", "require(['pagejs/lms/LMS1505V00Page'], function() { loadScript('pagejs/base/GridViewFilterPanel')});");

	}

	/*
	 * @Override public String getCodeId() { return "13004"; }
	 */
}
