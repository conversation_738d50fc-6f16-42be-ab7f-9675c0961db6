/* 
 * L170M01I.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 海外覆審檢視表檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L170M01I", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L170M01I extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** oid **/
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 統一編號 **/
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 重覆序號 **/
	@Size(max=1)
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(1)")
	private String dupNo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/**
	 * 有無授信合約<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="HASCONTRACT", length=1, columnDefinition="CHAR(1)")
	private String hasContract;

	/** 授信合約簽約日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="CONTRACTDT", columnDefinition="DATE")
	private Date contractDt;

	/**
	 * 有無額度本票<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="HASNOTE", length=1, columnDefinition="CHAR(1)")
	private String hasNote;

	/** 額度本票簽發日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="NOTEDT", columnDefinition="DATE")
	private Date noteDt;

	/**
	 * 有無本票授權書<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="HASATTORNEY", length=1, columnDefinition="CHAR(1)")
	private String hasAttorney;

	/** 本票授權書簽發日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ATTORNEYDT", columnDefinition="DATE")
	private Date attorneyDt;

	/**
	 * 有無連帶保證書<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="HASGUAR", length=1, columnDefinition="CHAR(1)")
	private String hasGuar;

	/** 連帶保證書簽訂日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GUARSIGNDT", columnDefinition="DATE")
	private Date guarSignDt;

	/** 連帶保證書對保日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="GUARVERDT", columnDefinition="DATE")
	private Date guarVerDt;

	/** 應收客票之金額合計 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="RECEAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal receAmt;

	/** 應收客票_不適用 **/
	@Size(max=1)
	@Column(name="RECENA", length=1, columnDefinition="CHAR(1)")
	private String receNA;

	/** 「擔保品估價報告書」最近一次估價日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="VALREPDT", columnDefinition="DATE")
	private Date valRepDt;

	/** 「擔保品估價報告書」最近一次估價金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="VALREPAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal valRepAmt;

	/** 估價報告書_不適用 **/
	@Size(max=1)
	@Column(name="VALREPNA", length=1, columnDefinition="CHAR(1)")
	private String valRepNA;

	/** 擔保品抵押權設定本行之順位 **/
	@Column(name = "MTGORDER", columnDefinition = "DECIMAL(2,0)")
	private Integer mtgOrder;

	/** 擔保品抵押權設定本行之金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="MTGAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal mtgAmt;

	/** 抵押權_不適用 **/
	@Size(max=1)
	@Column(name="MTGNA", length=1, columnDefinition="CHAR(1)")
	private String mtgNA;

	/** 擔保品保險單之到期日 **/
	@Temporal(TemporalType.DATE)
	@Column(name="INSDT", columnDefinition="DATE")
	private Date insDt;

	/** 擔保品保險單之投保金額 **/
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="INSAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal insAmt;

	/** 保費收據號碼 **/
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(name="INSNUM", columnDefinition="CLOB")
	private String insNum;

	/** 保險單_不適用 **/
	@Size(max=1)
	@Column(name="INSNA", length=1, columnDefinition="CHAR(1)")
	private String insNA;

	/** 取得oid **/
	public String getOid() {
		return this.oid;
	}
	/** 設定oid **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得統一編號 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定統一編號 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得重覆序號 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定重覆序號 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/**
	 * 取得有無授信合約<p/>
	 * Y/N
	 */
	public String getHasContract() {
		return this.hasContract;
	}
	/**
	 *  設定有無授信合約<p/>
	 *  Y/N
	 **/
	public void setHasContract(String value) {
		this.hasContract = value;
	}

	/** 取得授信合約簽約日期 **/
	public Date getContractDt() {
		return this.contractDt;
	}
	/** 設定授信合約簽約日期 **/
	public void setContractDt(Date value) {
		this.contractDt = value;
	}

	/**
	 * 取得有無額度本票<p/>
	 * Y/N
	 */
	public String getHasNote() {
		return this.hasNote;
	}
	/**
	 *  設定有無額度本票<p/>
	 *  Y/N
	 **/
	public void setHasNote(String value) {
		this.hasNote = value;
	}

	/** 取得額度本票簽發日期 **/
	public Date getNoteDt() {
		return this.noteDt;
	}
	/** 設定額度本票簽發日期 **/
	public void setNoteDt(Date value) {
		this.noteDt = value;
	}

	/**
	 * 取得有無本票授權書<p/>
	 * Y/N
	 */
	public String getHasAttorney() {
		return this.hasAttorney;
	}
	/**
	 *  設定有無本票授權書<p/>
	 *  Y/N
	 **/
	public void setHasAttorney(String value) {
		this.hasAttorney = value;
	}

	/** 取得本票授權書簽發日期 **/
	public Date getAttorneyDt() {
		return this.attorneyDt;
	}
	/** 設定本票授權書簽發日期 **/
	public void setAttorneyDt(Date value) {
		this.attorneyDt = value;
	}

	/**
	 * 取得有無連帶保證書<p/>
	 * Y/N
	 */
	public String getHasGuar() {
		return this.hasGuar;
	}
	/**
	 *  設定有無連帶保證書<p/>
	 *  Y/N
	 **/
	public void setHasGuar(String value) {
		this.hasGuar = value;
	}

	/** 取得連帶保證書簽訂日期 **/
	public Date getGuarSignDt() {
		return this.guarSignDt;
	}
	/** 設定連帶保證書簽訂日期 **/
	public void setGuarSignDt(Date value) {
		this.guarSignDt = value;
	}

	/** 取得連帶保證書對保日期 **/
	public Date getGuarVerDt() {
		return this.guarVerDt;
	}
	/** 設定連帶保證書對保日期 **/
	public void setGuarVerDt(Date value) {
		this.guarVerDt = value;
	}

	/** 取得應收客票之金額合計 **/
	public BigDecimal getReceAmt() {
		return this.receAmt;
	}
	/** 設定應收客票之金額合計 **/
	public void setReceAmt(BigDecimal value) {
		this.receAmt = value;
	}

	/** 取得應收客票_不適用 **/
	public String getReceNA() {
		return this.receNA;
	}
	/** 設定應收客票_不適用 **/
	public void setReceNA(String value) {
		this.receNA = value;
	}

	/** 取得「擔保品估價報告書」最近一次估價日期 **/
	public Date getValRepDt() {
		return this.valRepDt;
	}
	/** 設定「擔保品估價報告書」最近一次估價日期 **/
	public void setValRepDt(Date value) {
		this.valRepDt = value;
	}

	/** 取得「擔保品估價報告書」最近一次估價金額 **/
	public BigDecimal getValRepAmt() {
		return this.valRepAmt;
	}
	/** 設定「擔保品估價報告書」最近一次估價金額 **/
	public void setValRepAmt(BigDecimal value) {
		this.valRepAmt = value;
	}

	/** 取得估價報告書_不適用 **/
	public String getValRepNA() {
		return this.valRepNA;
	}
	/** 設定估價報告書_不適用 **/
	public void setValRepNA(String value) {
		this.valRepNA = value;
	}

	/** 取得擔保品抵押權設定本行之順位 **/
	public Integer getMtgOrder() {
		return this.mtgOrder;
	}
	/** 設定擔保品抵押權設定本行之順位 **/
	public void setMtgOrder(Integer value) {
		this.mtgOrder = value;
	}

	/** 取得擔保品抵押權設定本行之金額 **/
	public BigDecimal getMtgAmt() {
		return this.mtgAmt;
	}
	/** 設定擔保品抵押權設定本行之金額 **/
	public void setMtgAmt(BigDecimal value) {
		this.mtgAmt = value;
	}

	/** 取得抵押權_不適用 **/
	public String getMtgNA() {
		return this.mtgNA;
	}
	/** 設定抵押權_不適用 **/
	public void setMtgNA(String value) {
		this.mtgNA = value;
	}

	/** 取得擔保品保險單之到期日 **/
	public Date getInsDt() {
		return this.insDt;
	}
	/** 設定擔保品保險單之到期日 **/
	public void setInsDt(Date value) {
		this.insDt = value;
	}

	/** 取得擔保品保險單之投保金額 **/
	public BigDecimal getInsAmt() {
		return this.insAmt;
	}
	/** 設定擔保品保險單之投保金額 **/
	public void setInsAmt(BigDecimal value) {
		this.insAmt = value;
	}

	/** 取得保費收據號碼 **/
	public String getInsNum() {
		return this.insNum;
	}
	/** 設定保費收據號碼 **/
	public void setInsNum(String value) {
		this.insNum = value;
	}

	/** 取得保險單_不適用 **/
	public String getInsNA() {
		return this.insNA;
	}
	/** 設定保險單_不適用 **/
	public void setInsNA(String value) {
		this.insNA = value;
	}
}
