var initDfd = $.Deferred();
var _handler = "cls1161m03formhandler";

pageJsInit(function() {
	$(function() {
		var tabForm = $("#tabForm");
		var btnPanel = $("#buttonPanel");
		var initControl_lockDoc = false;
		$.form.init({
			formHandler: _handler,
			formAction: 'query',
			loadSuccess: function(json) {

				// 控制頁面 Read/Write
				if (!$("#buttonPanel").find("#btnSend").is("button") || json.lock) {
					tabForm.lockDoc();
					initControl_lockDoc = true;
					json['initControl_lockDoc'] = initControl_lockDoc;
				}
				tabForm.buildItem();

				tabForm.injectData(json);
				if (json.page == "01") {
					$("#xlsFrm").injectData(json.xlsFrmData);
				}
				initDfd.resolve(json);
			}
		});

		$("#packNo").change(function() {
			var value = $(this).val();
			$(this).val(util.addZeroBefore(value, 4));
		});

		btnPanel.find("#btnSend").click(function() {
			saveAction().done(function(json) {
				API.confirmMessage(i18n.def.confirmApply, function(result) {
					if (result) {
						flowAction({ 'decisionExpr': '呈主管' });
					}
				});
			});
		}).end().find("#btnSave").click(function() {
			saveAction();
		}).end().find("#btnPrint").click(function() {
			$.form.submit({
				url: webroot + '/app/simple/FileProcessingService',
				target: "_blank",
				data: $.extend(responseJSON, {
					fileDownloadName: 'list.pdf',
					serviceName: 'cls1161r30rptservice'
				})
			});
		}).end().find("#btnDelApprove").click(function() {
			$.ajax({
				type: "POST",
				handler: _handler, action: "doDelApprove",
				data: $.extend({
					mainOid: $("#mainOid").val(),
					mainDocStatus: $("#mainDocStatus").val()
				}
					, ({})
				),
				success: function(json) {
					API.triggerOpener();//gridview.reloadGrid 
					window.close();
				}
			});
		}).end().find("#btnAccept").click(function() {
			var _id = "_div_btnAccept";
			var _form = _id + "_form";
			if ($("#" + _id).length == 0) {
				var dyna = [];
				dyna.push("<div id='" + _id + "' style='display:none;' >");
				dyna.push("<form id='" + _form + "'>");

				dyna.push("		<p><label><input type='radio' name='decisionExpr' value='1' class='required' />" + i18n.def['accept'] + "</label></p>");
				dyna.push("		<p><label><input type='radio' name='decisionExpr' value='2' class='required' />" + i18n.def['return'] + "</label></p>");

				dyna.push("</form>");

				dyna.push("</div>");

				$('body').append(dyna.join(""));
			}
			//clear data
			$("#" + _form).reset();

			$("#" + _id).thickbox({ // 使用選取的內容進行彈窗
				title: i18n.def["confirmApprove"],
				width: 380,
				height: 180,
				align: "center",
				valign: "bottom",
				modal: false,
				i18n: i18n.def,
				buttons: {
					"sure": function() {
						if (!$("#" + _form).valid()) {
							return;
						}
						var val = $("#" + _form).find("[name='decisionExpr']:checked").val();
						if (val == "1") {
							flowAction({ 'decisionExpr': '核定' });
						} else if (val == "2") {
							flowAction({ 'decisionExpr': '退回' });
						}
					},
					"cancel": function() {
						$.thickbox.close();
					}
				}
			});
		});

		var flowAction = function(opts) {
			return $.ajax({
				type: "POST",
				handler: _handler, action: "flowAction",
				data: $.extend({
					mainOid: $("#mainOid").val(),
					mainDocStatus: $("#mainDocStatus").val()
				}
					, (opts || {})
				),
				success: function(json) {
					API.triggerOpener();//gridview.reloadGrid 
					window.close();
				}
			});
		}

	});
});

function saveAction(opts){
	var tabForm = $("#tabForm");
	if(tabForm.valid()){
		return $.ajax({
            type: "POST",
            handler: _handler,
            data:$.extend( {
            	formAction: "saveMain",
                page: responseJSON.page,
                mainOid: responseJSON.mainOid
                }, 
                tabForm.serializeData(),
                ( opts||{} )
            ),                
            success: function(json){
            	tabForm.injectData(json);
            	//更新 opener 的 Grid
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
        });
	}else{
		return $.Deferred();
	}
}

$.extend(window.tempSave,{
	handler: _handler, // handler 名稱
	action: "tempSave", // action Method
	beforeCheck:function(){
		return true;
	},sendData:function(){
		return $("#tabForm").serializeData();
	}
});
