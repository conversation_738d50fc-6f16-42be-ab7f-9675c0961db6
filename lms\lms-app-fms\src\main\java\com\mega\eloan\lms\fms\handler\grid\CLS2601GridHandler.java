package com.mega.eloan.lms.fms.handler.grid;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.CodeTypeFormatter;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.fms.pages.CLS2601V01Page;
import com.mega.eloan.lms.fms.service.CLS2601Service;
import com.mega.eloan.lms.model.C900M01H;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

@Scope("request")
@Controller("cls2601gridhandler")
public class CLS2601GridHandler extends AbstractGridHandler {

	private static final DateFormat S_FORMAT = new SimpleDateFormat(
			UtilConstants.DateFormat.YYYY_MM_DD_HH_MM_SS);

	@Resource
	CLSService clsService;

	@Resource
	CLS2601Service service;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	BranchService branchService;
	
	Properties prop = MessageBundleScriptCreator
			.getComponentResource(CLS2601V01Page.class);

	/**
	 * 查詢Grid 資料
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryMain_error_keepNotDelete(ISearch pageSetting,
			PageParameters params) throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			//Filet查詢條件
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String custName = Util.trim(jsoniletData.getString("custName"));
			String agentCertYear = Util.trim(jsoniletData.getString("agentCertYear"));
			String agentCertWord = Util.trim(jsoniletData.getString("agentCertWord"));
			String agentCertNo = Util.trim(jsoniletData.getString("agentCertNo"));
			String ctlFlag = Util.trim(jsoniletData.getString("ctlFlag"));
			
			if (Util.isNotEmpty(custName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
						"%"+custName+"%");
			}
			if (Util.isNotEmpty(agentCertYear)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"agentCertYear", agentCertYear);
			}
			if (Util.isNotEmpty(agentCertWord)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "agentCertWord",
						"%"+agentCertWord+"%");
			}
			if (Util.isNotEmpty(agentCertNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"agentCertNo", agentCertNo);
			}
			if (Util.isNotEmpty(ctlFlag)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"ctlFlag", ctlFlag);
			}
		}
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		FlowDocStatusEnum docStatusEnum = FlowDocStatusEnum.getEnum(docStatus);
		if (docStatusEnum != null) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);	
			String ownBrId = CapString.trimNull(params.getString("ownBrId"));
			if(!CapString.isEmpty(ownBrId))
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
			
		} else {
			//全行黑名單查詢(已核准+待解除)
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
		}
		
		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");

		Page<? extends GenericBean> raw_page = clsService.findPage(C900M01H.class, pageSetting);
		//若 rs.size()>頁面, {"+raw_page.getPageSize()+"}{"+raw_page.getTotalRow()+"}{"+raw_page.getContent().size()+"}"); 
		//第1頁是 {15, 28, 15}
		//第2頁是 {15, 28, 13}
		List<C900M01H> src_list = (List<C900M01H>) raw_page.getContent();
		Map<String, String> cls260CtlFlagType_map = clsService.get_codeTypeWithOrder("cls260CtlFlagType");
		for (C900M01H model : src_list) {
			Map<String, Object> row = new HashMap<String, Object>();
			LMSUtil.meta_to_map(row, model, new String[] { "oid", "mainId" });
			row.put(EloanConstants.MAIN_OID, model.getOid());
			row.put(EloanConstants.MAIN_DOC_STATUS, model.getDocStatus());
			row.put("custId", model.getCustId());
			row.put("custName", Util.trim(model.getCustName()));
			row.put("agentCert",
					(model.getAgentCertYear() == null ? "" : ( "(" + model
							.getAgentCertYear() + ")")) 
							+ (model.getAgentCertWord() == null ? "" : model
									.getAgentCertWord())
							+ "字第"
							+ (model.getAgentCertNo() == null ? "" : model
									.getAgentCertNo()) + "號");
			row.put("ctlFlag", model.getCtlFlag() == null ? "" : LMSUtil.getDesc(cls260CtlFlagType_map, model.getCtlFlag()));
			row.put("ownBrId", model.getOwnBrId() + branchService.getBranchName(model.getOwnBrId()));
			row.put("updater", userInfoService.getUserName(model.getUpdater()));
			row.put("updateTime",
					model.getUpdateTime() == null ? "" : S_FORMAT.format(model
							.getUpdateTime()));

			list.add(row);
		}
		Page<Map<String, Object>> page = LMSUtil.getMapGirdDataRow(list, pageSetting);		
		//若 rs.size()>頁面, {"+new_page.getPageSize()+"}{"+new_page.getTotalRow()+"}{"+new_page.getContent().size()+"}");
		/* new CapMapGridResult(new_page.getContent(), new_page.getTotalRow())
		  	第1頁是 {15, 28, 15}
		  	無第2頁 ===> 在前端 js 就缺少1頁
		 */
		/* new CapMapGridResult(new_page.getContent(), raw_page.getTotalRow())
	  		第1頁是 {15, 25, 15}
	  		第2頁是 {15, 13, 0}===> 在前端雖有出現第2頁,但裡面的 content 空白
		 */
		/*
		  	似乎 LMSUtil.getMapGirdDataRow(list, pageSetting) 裡的第1個參數 list
			● 只能放全部的資料 EX:eloandbService.find(...)
			● 不能夠放{依第n頁去篩選之後的資料} service.findPage(C900M01H.class, pageSetting);
		*/
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}


	public CapGridResult queryMain(ISearch pageSetting,
			PageParameters params) throws CapException {

		String filetData = Util.trim(params.getString("filetData"));
		if (Util.isNotEmpty(filetData)) {
			//Filet查詢條件
			JSONObject jsoniletData = JSONObject.fromObject(filetData);
			String custName = Util.trim(jsoniletData.getString("custName"));
			String agentCertYear = Util.trim(jsoniletData.getString("agentCertYear"));
			String agentCertWord = Util.trim(jsoniletData.getString("agentCertWord"));
			String agentCertNo = Util.trim(jsoniletData.getString("agentCertNo"));
			String ctlFlag = Util.trim(jsoniletData.getString("ctlFlag"));
			
			if (Util.isNotEmpty(custName)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
						"%"+custName+"%");
			}
			if (Util.isNotEmpty(agentCertYear)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"agentCertYear", agentCertYear);
			}
			if (Util.isNotEmpty(agentCertWord)) {
				pageSetting.addSearchModeParameters(SearchMode.LIKE, "agentCertWord",
						"%"+agentCertWord+"%");
			}
			if (Util.isNotEmpty(agentCertNo)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"agentCertNo", agentCertNo);
			}
			if (Util.isNotEmpty(ctlFlag)) {
				pageSetting.addSearchModeParameters(SearchMode.EQUALS,
						"ctlFlag", ctlFlag);
			}
		}
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		FlowDocStatusEnum docStatusEnum = FlowDocStatusEnum.getEnum(docStatus);
		if (docStatusEnum != null) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, EloanConstants.DOC_STATUS, docStatus);	
			String ownBrId = CapString.trimNull(params.getString("ownBrId"));
			if(!CapString.isEmpty(ownBrId))
				pageSetting.addSearchModeParameters(SearchMode.EQUALS, "ownBrId", ownBrId);
			
		} else {
			//全行黑名單查詢(已核准+待解除)
			String[] _docStatus = docStatus
					.split(UtilConstants.Mark.SPILT_MARK);
			pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
					_docStatus);
		}
		
		
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime",
				"");
		//=============================
		Page<? extends GenericBean> page = clsService.findPage(C900M01H.class, pageSetting);
		for (GenericBean bean : page.getContent()) {
			C900M01H model = (C900M01H) bean;
			model.setAgentCert(
					(model.getAgentCertYear() == null ? "" : ( "(" + model
							.getAgentCertYear() + ")")) 
							+ (model.getAgentCertWord() == null ? "" : model
									.getAgentCertWord())
							+ "字第"
							+ (model.getAgentCertNo() == null ? "" : model
									.getAgentCertNo()) + "號");		
		}
		//=============================
		CapGridResult result = new CapGridResult(page.getContent(), page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		if(true){
			dataReformatter.put("ctlFlag", new CodeTypeFormatter(codetypeservice,"cls260CtlFlagType"));
			dataReformatter.put("ownBrId", new BranchNameFormatter(branchService, ShowTypeEnum.IDSpaceName)); 
			dataReformatter.put("updater", new UserNameFormatter(userInfoService));		
		}
		result.setDataReformatter(dataReformatter);
		//=============================
		return result;
	}
}
