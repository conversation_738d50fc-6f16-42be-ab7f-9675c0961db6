/* 
 *ProdService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.service;

import java.util.HashMap;
import java.util.List;

import org.kordamp.json.JSONArray;

import com.mega.eloan.lms.model.C160M01C;
import com.mega.eloan.lms.model.L140M01O;
import com.mega.eloan.lms.model.L140M04A;
import com.mega.eloan.lms.model.L140S02A;

/**
 * <pre>
 * 產品相關 Service
 * </pre>
 * 
 * @since 2012/12/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/26,REX,new
 *          </ul>
 */
public interface ProdService {

	public enum ProdKindEnum {

		協助在學學生購置電腦設備_01("01"),
		行家理財貸款_短期_02("02"),
		行家理財貸款_中長期_03("03"),
		綜合理財_04("04"),
		汽車貸款GECT及AIG_05("05"),
		留學貸款_06("06"),
		一般消貸含團體消貸_07("07"),
//		信用卡客戶小額消費性款_08("08"),
		信用卡卡友信用貸款_08("08"),    //J-108-0187
		停車位貸款_09("09"),
		一般房貸_購置住宅_10("10"),
		一般房貸_購屋儲蓄_11("11"),
		一般房貸_房屋修繕_12("12"),
		優惠購屋專案_3200億_13("13"),
		青年優惠房貸暨信用保證專案_3200億_14("14"),
		青年購屋低利貸款_15("15"),
		輔助人民自購住宅貸款_16("16"),
		輔助勞工建購住宅貸款_一般建購_17("17"),
		輔助勞工建購住宅貸款_一般修繕_18("18"),
		輔助勞工建購住宅貸款_921建購_19("19"),
		購置新屋_1500億_20("20"),
		無自用住宅購屋_1500億_21("21"),
		新購屋_921貸款專案_22("22"),
		重建_921貸款專案_23("23"),
		貸款專案_921_24("24"),
		修繕_優惠購屋專案_6000億_25("25"),
		輔助原助民建購_修繕住宅貸款_26("26"),
		購置住宅_921原貸展延戶_27("27"),
		優惠購屋專案_8000億_28("28"),
		合作車貸GECT及AIG_29("29"),
		房貸專案_A案_100億_30("30"),
		房貸專案_B案_100億_31("31"),
		外勞貸款_32("32"),
		土地融資_個人戶_33("33"),
		建築融資_個人戶_34("34"),
		優惠購屋專案_93年3000億_35("35"),
		政策性留學生貸款_36("36"),
		個人通信貸款_37("37"),
		內政部整合住宅方案_弱勢戶_第一類_38("38"),
		內政部整合住宅方案_一般戶_第二類_39("39"),
		協助震災災區民眾建購或修繕住宅貸款_40("40"),
	    郵儲金辦理無自用住宅者優惠貸款_41("41"),
	    郵儲金辦理無自用住宅者優惠貸款_42("42"),
	    無自用住宅者購置新屋_無補貼息_1500億_43("43"),
	    一年固定利率優惠房貸_44("44"),
	    兆豐一生康健安家專案_45("45"),
	    五年固定利率優惠房貸專案_46("46"),
	    永慶房屋履約保證優惠房貸專案_47("47"),
	    法拍屋信用貸款修繕住宅貸款_48("48"),
	    次順位房貸_49("49"),
	    自營業主貸款住宅者優惠貸款_50("50"),
	    消費者個人擔保貸款_土地住宅者優惠貸款_51("51"),
	    消費者個人擔保貸款_各類債券及有價證券置新屋_無補貼息_52("52"),
	    軍人信用貸款專案房貸_53("53"),
	    短期個人信貸專案_54("54"),
	    兆豐傳家保_優惠房貸加壽險專案_55("55"),
		優惠購屋專案_97年_2000億_56("56"),
		青年安心成家貸款_57("57"), // (內政部)青安
		青年創業貸款_58("58"),
		青年安心成家貸款_第二案_59("59"), // (財政部)青安 
		青年築夢創業啟動金貸款_60("60"),
		青年創業及啟動金貸款_61("61"),
		一０四年兆豐金控集團現職員工增資認股消貸專案_62("62"),
		天然及重大災害受災戶購屋_63("63"),
		天然及重大災害受災戶重建_64("64"),
		天然及重大災害受災戶修繕_65("65"),
		金門青年安心成家_66("66"),
		以房養老_67("67"),
		行家理財中期循環_68("68"),   //J-107-0008
		勞工紓困貸款_69("69"),
		以房養老累積型_70("70"),    //J-109-0271 
		歡喜信貸_71("71"),   //J-109-0390
		地上權住宅貸款_72("72"),   //J-110-0384            註：「地上權住宅」有建物所有權狀，買屋不買地，仍能自行買賣移轉；「使用權住宅」無建物權狀，不買屋不買地，較像是和 建商 簽訂長期租約 (可參考 MIS.ELF533 華固案)
		// CrsUtil 內 99 的 分類可參考 SELECT * FROM LN.LNF07A where LNF07A_KEY_1 like 'CLS%'
		企金科目("ZZ");

		public static ProdKindEnum getEnum(String code) {
			for (ProdKindEnum enums : ProdKindEnum.values()) {
				if (enums.getCode().equals(code)) {
					return enums;
				}
			}
			return null;
		}

		private String code;

		ProdKindEnum(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 重新載入初始值
	 */
	void reload(boolean cleanAll);

	/**
	 * 取得產品種類下拉選單
	 * 
	 * @return <pre>
	 * [
	 * key:產品代號,
	 * name:產品名稱,
	 * subjectData: 對應授信科目
	 * 		[
	 * 		  {
	 *          subjCode:授信科目代碼,
	 *          subjCode2:授信科目簡碼,
	 *          subjNm:授信科目名稱,
	 *          rIntWay:計息方式
	 *        },
	 *        {
	 *          subjCode:授信科目代碼,
	 *          subjCode2:授信科目簡碼,
	 *          subjNm:授信科目名稱,
	 *          rIntWay:計息方式
	 *        },{
	 *        }......
	 * 
	 * 
	 * 
	 *  	]
	 * 
	 * ]
	 * </pre>
	 */
	public JSONArray getProdKind();
	public JSONArray getProdKind(String busCode);
	
	/**
	 * J-113-0036 新增新產品資訊時，將產品種類下拉選單選項做細部分類
	 * 以貸款類型取得產品種類
	 */
	public JSONArray getProdKind(String busCode,String loanType);
	public JSONArray getProdKindByLoanType(String loanType);

	/**
	 * 取得科目代碼名稱
	 * 
	 * @return
	 */
	public HashMap<String, String> getSubCode();

	/**
	 * 取得產品代碼名稱
	 * 
	 * @return
	 */
	public HashMap<String, String> getProdKindName();
	public HashMap<String, String> getProdKindName(String busCode);
	
	/**
	 * 取得檢附文件
	 * 
	 * @param mainId
	 *            動審表mainId
	 * @return 檢附文件項目
	 */
	public List<C160M01C> getC160M01C(String mainId);

	/**
	 * 取得查核事項
	 * 
	 * @param mainId
	 *            簽報書mainId
	 * @return 查核事項項目
	 */
	public List<L140M04A> getL140M04A(String mainId);

	/**
	 * 確認是否為有擔科目
	 * 
	 * @param subjectCode
	 *            授信科目 8碼授信科目
	 * @return true 有擔 | false 無擔
	 */
	public boolean isGuarantee(String subjectCode);

	/**
	 * 取得8碼授信科目
	 * 
	 * @param subjectCode
	 *            三碼授信科目
	 * @return 8碼授信科目
	 */
	public String getSubject3to8(String subjectCode);

	/**
	 * 優惠房貸 28/35/38/39/56/57/59
	 * 
	 * @param prodKindEnumVal
	 * @return
	 */
	public boolean is_policy_house_loan(ProdKindEnum prodKindEnumVal);

	/**
	 * 取得3碼授信科目
	 * 
	 * @param subjectCode2
	 *            8碼授信科目
	 * @return 3碼授信科目
	 */
	public String getSubject8to3(String subjectCode2);
	
	public String getMatchModelKind(L140S02A l140s02a);
	public String getMatchModelKind(String mainId, String prodKind, String subjCode);
	
	public boolean checkhasCMS01_build(List<L140M01O> l140m01os);
	
	public String[] get_33or34();
	
	/** 規則
	 * <ul>
	 * <li>利害關係人身分Y 行員身分Y，科目限321且產品種類07</li>
	 * <li>利害關係人身分Y 行員身分N，科目限321且產品種類71</li>
	 * <li>利害關係人身份N，科目可321或303，產品種類71</li>
	 * </ul>
	 */
	public String get_brmp_target_prodKind(String isQdata2, String isQdata3, boolean isBankMan_borrower);
	
//	public boolean is_63_64_65(String prodKind);
}
