/* 
 * C160M01BDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C160M01B;

/** 額度明明細檔 **/
public interface C160M01BDao extends IGenericDao<C160M01B> {

	C160M01B findByOid(String oid);
	
	List<C160M01B> findByMainId(String mainId);
	
	List<C160M01B> findByCntrNo(String cntrNo);	
	
	C160M01B findByUniqueKey(String oid);

	List<C160M01B> findByIndex01(String oid);

	C160M01B findByMainIdRefMainId(String mainId, String refmainId);
	
	C160M01B findByMainidCntrno(String Mainid, String Cntrno);

	List<C160M01B> findByRefMainId(String refmainId);
	
	C160M01B findByMainIdUid(String mainId, String uid);
	List<C160M01B> findLastByRefMainId(String refmainId);
	
}