/* 
 * LMS1835GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.handler.grid;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.iisigroup.cap.component.PageParameters;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.lrs.service.LMS1835Service;
import com.mega.eloan.lms.model.L184M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 企金戶【新增/增額/逾放轉正】名單
 * </pre>
 * 
 * @since 2011/11/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/11/21,jessica,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1835gridhandler")
public class LMS1835GridHandler extends AbstractGridHandler {

	@Resource
	LMS1835Service service;

	@Resource
	BranchService branchService;
	
	/**
	 * 查詢Grid 企金戶【新增/增額/逾放轉正】名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public CapGridResult query(ISearch pageSetting, PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l184a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		Map<String, Boolean> orderBy = new LinkedHashMap<String,Boolean>();
		orderBy.put("dataDate", true);
		orderBy.put("ownBrId", false);
		orderBy.put("branchId", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		Page page = service.findPage(L184M01A.class, pageSetting);

		List<L184M01A> list = page.getContent();
		for (L184M01A model : list) {
			model.setBranchId(model.getBranchId()+branchService.getBranchName(model.getBranchId()));
		}
		return new CapGridResult(list, page.getTotalRow());
	}

	/**
	 * 查詢Grid 企金戶【新增/增額/逾放轉正】名單
	 * 
	 * @param pageSetting
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public CapGridResult search(ISearch pageSetting, PageParameters params) throws CapException {

		String kind = Util.nullToSpace(params.getString("kind"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l184a01a.authUnit", user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");

		if ("new".equals(kind)) {
			String searchDate = Util
					.nullToSpace(params.getString("searchDate"));
			searchDate = searchDate + "-01";
			Date parseSearchDate = CapDate.parseDate(searchDate);
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "dataDate",
					parseSearchDate);

		} else {
			String starDate = Util.nullToSpace(params.getString("starDate"));
			starDate = starDate + "-01";
			Date startDate = CapDate.parseDate(starDate);
			String endDate = Util.nullToSpace(params.getString("endDate"));
			endDate = endDate + "-01";
			Date enDate = CapDate.parseDate(endDate);
			Object[] reason = { startDate, enDate };
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "dataDate",
					reason);
		}
		Map<String, Boolean> orderBy = new LinkedHashMap<String,Boolean>();
		orderBy.put("dataDate", true);
		orderBy.put("ownBrId", false);
		orderBy.put("createTime", true);
		pageSetting.setOrderBy(orderBy);
		Page page = service.findPage(L184M01A.class, pageSetting);
		List<L184M01A> list = (List<L184M01A>) page.getContent();
		for (L184M01A model : list) {
			model.setOwnBrId(model.getOwnBrId() + user.getUnitCName());
		}
		return new CapGridResult(list, page.getTotalRow());
	}
}
