/* 
 * L180R42A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金新核准往來客戶月檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L180R42A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L180R42A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max = 32)
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Size(max = 32)
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 額度明細表MAINID **/
	@Size(max = 32)
	@Column(name = "CNTRMAINID", length = 32, columnDefinition = "CHAR(32)")
	private String cntrMainId;

	/**
	 * 核准日期
	 * <p/>
	 * NOT NULL
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "ENDDATE", columnDefinition = "Date")
	private Date endDate;

	/**
	 * 企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	@Size(max = 1)
	@Column(name = "DOCTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String docType;

	/**
	 * 授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	@Size(max = 1)
	@Column(name = "DOCKIND", length = 1, columnDefinition = "CHAR(1)")
	private String docKind;

	/**
	 * 案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	@Size(max = 1)
	@Column(name = "DOCCODE", length = 1, columnDefinition = "CHAR(1)")
	private String docCode;

	/**
	 * 區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 */
	@Size(max = 1)
	@Column(name = "TYPCD", length = 1, columnDefinition = "CHAR(1)")
	private String typCd;

	/**
	 * 客戶統編
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 10)
	@Column(name = "CUSTID", length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/**
	 * 重覆序號
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 1)
	@Column(name = "DUPNO", length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/**
	 * 客戶名稱
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 120)
	@Column(name = "CUSTNAME", length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 簽案分行 **/
	@Size(max = 3)
	@Column(name = "CASEBRID", length = 3, columnDefinition = "CHAR(3)")
	private String caseBrId;

	/** 案件號碼 **/
	@Size(max = 62)
	@Column(name = "CASENO", length = 62, columnDefinition = "VARCHAR(62)")
	private String caseNo;

	/**
	 * 案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	@Size(max = 2)
	@Column(name = "CASELVL", length = 2, columnDefinition = "CHAR(2)")
	private String caseLvl;

	/**
	 * 額度序號
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 12)
	@Column(name = "CNTRNO", length = 12, columnDefinition = "CHAR(12)")
	private String cntrNo;

	/**
	 * 額度分行
	 * <p/>
	 * (額度明細表)
	 */
	@Size(max = 3)
	@Column(name = "CNTRBRID", length = 3, columnDefinition = "CHAR(3)")
	private String cntrBrId;

	/**
	 * 性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	@Size(max = 30)
	@Column(name = "PROPERTY", length = 30, columnDefinition = "VARCHAR(30)")
	private String property;

	/**
	 * 額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	@Size(max = 1)
	@Column(name = "SBJPROPERTY", length = 1, columnDefinition = "CHAR(1)")
	private String sbjProperty;

	/**
	 * 本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM1", length = 1, columnDefinition = "CHAR(1)")
	private String headItem1;

	/**
	 * 借款人是否為中小企業
	 * <p/>
	 * Y/N（是/否）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM2", length = 1, columnDefinition = "CHAR(1)")
	private String headItem2;

	/**
	 * 是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	@Size(max = 1)
	@Column(name = "HEADITEM3", length = 1, columnDefinition = "CHAR(1)")
	private String headItem3;

	/** 現請額度－幣別 **/
	@Size(max = 3)
	@Column(name = "CURRENTAPPLYCURR", length = 3, columnDefinition = "CHAR(3)")
	private String currentApplyCurr;

	/** 現請額度－金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "CURRENTAPPLYAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal currentApplyAmt;

	/** 新作、增額合計幣別 **/
	@Size(max = 3)
	@Column(name = "INCAPPLYTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incApplyTotCurr;

	/** 新作、增額合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "INCAPPLYTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incApplyTotAmt;

	/** 新作、增額擔保額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "INCASSTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String incAssTotCurr;

	/** 新作、增額擔保額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "INCASSTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal incAssTotAmt;

	/** 授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "LOANTOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String LoanTotCurr;

	/** 授信額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "LOANTOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal LoanTotAmt;

	/** 擔保授信額度合計幣別 **/
	@Size(max = 3)
	@Column(name = "ASSURETOTCURR", length = 3, columnDefinition = "CHAR(3)")
	private String assureTotCurr;

	/** 擔保授信額度合計金額 **/
	@Digits(integer = 17, fraction = 2, groups = Check.class)
	@Column(name = "ASSURETOTAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal assureTotAmt;

	/**
	 * 企業規模
	 * <p/>
	 * 大型<br/>
	 * 中小型
	 */
	@Size(max = 1)
	@Column(name = "CLTTYPE", length = 1, columnDefinition = "CHAR(1)")
	private String cltType;

	/** 行業對象別 **/
	@Size(max = 6)
	@Column(name = "BUSCODE", length = 6, columnDefinition = "VARCHAR(6)")
	private String busCode;

	/** 建立人員號碼 **/
	@Size(max = 6)
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max = 6)
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Timestamp updateTime;

	/** 刪除註記 **/
	@Column(name = "DELETEDTIME", columnDefinition = "TIMESTAMP")
	private Timestamp deletedTime;

	/** 合計幣別匯率 **/
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "ENDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal endRate;

	/** 日期 **/
	@Size(max = 10)
	@Column(name = "RATEYMD", length = 10, columnDefinition = "CHAR(10)")
	private String rateYmd;

	/**
	 * 是否為新客戶
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "ISNEW", length = 1, columnDefinition = "CHAR(1)")
	private String isNew;

	/**
	 * 產品種類
	 * <p/>
	 */
	@Size(max = 2)
	@Column(name = "LNTYPE", length = 2, columnDefinition = "CHAR(2)")
	private String lnType;

	/**
	 * 報送狀態
	 * <p/>
	 */
	@Size(max = 1)
	@Column(name = "STATUS", length = 1, columnDefinition = "CHAR(1)")
	private String status;

	/** 現請額度幣別匯率 **/
	@Digits(integer = 9, fraction = 5, groups = Check.class)
	@Column(name = "CURRENDRATE", columnDefinition = "DECIMAL(9,5)")
	private BigDecimal currEndRate;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得額度明細表MAINID **/
	public String getCntrMainId() {
		return this.cntrMainId;
	}

	/** 設定額度明細表MAINID **/
	public void setCntrMainId(String value) {
		this.cntrMainId = value;
	}

	/**
	 * 取得核准日期
	 * <p/>
	 * NOT NULL
	 */
	public Date getEndDate() {
		return this.endDate;
	}

	/**
	 * 設定核准日期
	 * <p/>
	 * NOT NULL
	 **/
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * 取得企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 */
	public String getDocType() {
		return this.docType;
	}

	/**
	 * 設定企/個金案件
	 * <p/>
	 * 1企金<br/>
	 * 2個金
	 **/
	public void setDocType(String value) {
		this.docType = value;
	}

	/**
	 * 取得授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 */
	public String getDocKind() {
		return this.docKind;
	}

	/**
	 * 設定授權別
	 * <p/>
	 * 1授權內<br/>
	 * 2授權外
	 **/
	public void setDocKind(String value) {
		this.docKind = value;
	}

	/**
	 * 取得案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 */
	public String getDocCode() {
		return this.docCode;
	}

	/**
	 * 設定案件別
	 * <p/>
	 * 1一般<br/>
	 * 2其他<br/>
	 * 3陳復/陳述案<br/>
	 * 4異常通報<br/>
	 * 5團貸案件 (※國內個金案件)
	 **/
	public void setDocCode(String value) {
		this.docCode = value;
	}

	/**
	 * 取得區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 */
	public String getTypCd() {
		return this.typCd;
	}

	/**
	 * 設定區部別
	 * <p/>
	 * 無、1.DBU、4.OBU、5.海外(海外同業, 海外客戶)<br/>
	 * (額度明細表)
	 **/
	public void setTypCd(String value) {
		this.typCd = value;
	}

	/**
	 * 取得客戶統編
	 * <p/>
	 * (額度明細表)
	 */
	public String getCustId() {
		return this.custId;
	}

	/**
	 * 設定客戶統編
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/**
	 * 取得重覆序號
	 * <p/>
	 * (額度明細表)
	 */
	public String getDupNo() {
		return this.dupNo;
	}

	/**
	 * 設定重覆序號
	 * <p/>
	 * (額度明細表)
	 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/**
	 * 取得客戶名稱
	 * <p/>
	 * (額度明細表)
	 */
	public String getCustName() {
		return this.custName;
	}

	/**
	 * 設定客戶名稱
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCustName(String value) {
		this.custName = value;
	}

	/** 取得簽案分行 **/
	public String getCaseBrId() {
		return this.caseBrId;
	}

	/** 設定簽案分行 **/
	public void setCaseBrId(String value) {
		this.caseBrId = value;
	}

	/** 取得案件號碼 **/
	public String getCaseNo() {
		return this.caseNo;
	}

	/** 設定案件號碼 **/
	public void setCaseNo(String value) {
		this.caseNo = value;
	}

	/**
	 * 取得案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 */
	public String getCaseLvl() {
		return this.caseLvl;
	}

	/**
	 * 設定案件審核層級
	 * <p/>
	 * 1　常董會權限<br/>
	 * 2　常董會權限簽奉總經理核批<br/>
	 * 3　常董會權限簽准由副總經理核批<br/>
	 * 4　利費率變更案件由總處經理核定<br/>
	 * 5　屬常董會授權總經理逕核案件<br/>
	 * 6　總經理權限內<br/>
	 * 7　副總經理權限<br/>
	 * 8　授管處處長權限<br/>
	 * 9　其他<br/>
	 * A　董事會權限<br/>
	 * B　區域營運中心營運長/副營運長權限<br/>
	 * C　利費率變更案件由董事長核定<br/>
	 * D　個金處經理權
	 **/
	public void setCaseLvl(String value) {
		this.caseLvl = value;
	}

	/**
	 * 取得額度序號
	 * <p/>
	 * (額度明細表)
	 */
	public String getCntrNo() {
		return this.cntrNo;
	}

	/**
	 * 設定額度序號
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/**
	 * 取得額度分行
	 * <p/>
	 * (額度明細表)
	 */
	public String getCntrBrId() {
		return this.cntrBrId;
	}

	/**
	 * 設定額度分行
	 * <p/>
	 * (額度明細表)
	 **/
	public void setCntrBrId(String value) {
		this.cntrBrId = value;
	}

	/**
	 * 取得性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 */
	public String getProperty() {
		return this.property;
	}

	/**
	 * 設定性質
	 * <p/>
	 * 新做|1<br/>
	 * 續約|2<br/>
	 * 變更條件|3<br/>
	 * 流用|4<br/>
	 * 增額|5<br/>
	 * 減額|6<br/>
	 * 不變|7<br/>
	 * 取消|8<br/>
	 * 展期(不良授信案)|9<br/>
	 * 紓困|10<br/>
	 * 提前續約|11<br/>
	 * 協議清償|12<br/>
	 * 報價 | 13
	 **/
	public void setProperty(String value) {
		this.property = value;
	}

	/**
	 * 取得額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 */
	public String getSbjProperty() {
		return this.sbjProperty;
	}

	/**
	 * 設定額度性質
	 * <p/>
	 * S:擔保<br/>
	 * N:無擔保/信保
	 **/
	public void setSbjProperty(String value) {
		this.sbjProperty = value;
	}

	/**
	 * 取得本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 */
	public String getHeadItem1() {
		return this.headItem1;
	}

	/**
	 * 設定本額度有無送保
	 * <p/>
	 * Y/N（有/無）
	 **/
	public void setHeadItem1(String value) {
		this.headItem1 = value;
	}

	/**
	 * 取得是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 */
	public String getHeadItem3() {
		return this.headItem3;
	}

	/**
	 * 設定是否符合送信保基金保證條件
	 * <p/>
	 * Y/N（是/否）
	 **/
	public void setHeadItem3(String value) {
		this.headItem3 = value;
	}

	/** 取得現請額度－幣別 **/
	public String getCurrentApplyCurr() {
		return this.currentApplyCurr;
	}

	/** 設定現請額度－幣別 **/
	public void setCurrentApplyCurr(String value) {
		this.currentApplyCurr = value;
	}

	/** 取得現請額度－金額 **/
	public BigDecimal getCurrentApplyAmt() {
		return this.currentApplyAmt;
	}

	/** 設定現請額度－金額 **/
	public void setCurrentApplyAmt(BigDecimal value) {
		this.currentApplyAmt = value;
	}

	/** 取得新作、增額合計幣別 **/
	public String getIncApplyTotCurr() {
		return this.incApplyTotCurr;
	}

	/** 設定新作、增額合計幣別 **/
	public void setIncApplyTotCurr(String value) {
		this.incApplyTotCurr = value;
	}

	/** 取得新作、增額合計金額 **/
	public BigDecimal getIncApplyTotAmt() {
		return this.incApplyTotAmt;
	}

	/** 設定新作、增額合計金額 **/
	public void setIncApplyTotAmt(BigDecimal value) {
		this.incApplyTotAmt = value;
	}

	/** 取得新作、增額擔保額度合計幣別 **/
	public String getIncAssTotCurr() {
		return this.incAssTotCurr;
	}

	/** 設定新作、增額擔保額度合計幣別 **/
	public void setIncAssTotCurr(String value) {
		this.incAssTotCurr = value;
	}

	/** 取得新作、增額擔保額度合計金額 **/
	public BigDecimal getIncAssTotAmt() {
		return this.incAssTotAmt;
	}

	/** 設定新作、增額擔保額度合計金額 **/
	public void setIncAssTotAmt(BigDecimal value) {
		this.incAssTotAmt = value;
	}

	/** 取得授信額度合計幣別 **/
	public String getLoanTotCurr() {
		return this.LoanTotCurr;
	}

	/** 設定授信額度合計幣別 **/
	public void setLoanTotCurr(String value) {
		this.LoanTotCurr = value;
	}

	/** 取得授信額度合計金額 **/
	public BigDecimal getLoanTotAmt() {
		return this.LoanTotAmt;
	}

	/** 設定授信額度合計金額 **/
	public void setLoanTotAmt(BigDecimal value) {
		this.LoanTotAmt = value;
	}

	/** 取得擔保授信額度合計幣別 **/
	public String getAssureTotCurr() {
		return this.assureTotCurr;
	}

	/** 設定擔保授信額度合計幣別 **/
	public void setAssureTotCurr(String value) {
		this.assureTotCurr = value;
	}

	/** 取得擔保授信額度合計金額 **/
	public BigDecimal getAssureTotAmt() {
		return this.assureTotAmt;
	}

	/** 設定擔保授信額度合計金額 **/
	public void setAssureTotAmt(BigDecimal value) {
		this.assureTotAmt = value;
	}

	/**
	 * 取得企業規模
	 * <p/>
	 * 大型<br/>
	 * 中小型
	 */
	public String getCltType() {
		return this.cltType;
	}

	/**
	 * 設定企業規模
	 * <p/>
	 * 大型<br/>
	 * 中小型
	 **/
	public void setCltType(String value) {
		this.cltType = value;
	}

	/** 取得行業對象別 **/
	public String getBusCode() {
		return this.busCode;
	}

	/** 設定行業對象別 **/
	public void setBusCode(String value) {
		this.busCode = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 取得刪除註記 **/
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}

	/** 設定刪除註記 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 設定借款人是否為中小企業 **/
	public void setHeadItem2(String headItem2) {
		this.headItem2 = headItem2;
	}

	/** 取得借款人是否為中小企業 **/
	public String getHeadItem2() {
		return headItem2;
	}

	/** 取得合計幣別匯率 **/
	public BigDecimal getEndRate() {
		return this.endRate;
	}

	/** 設定合計幣別匯率 **/
	public void setEndRate(BigDecimal value) {
		this.endRate = value;
	}

	/** 取得日期 **/
	public String getRateYmd() {
		return this.rateYmd;
	}

	/** 設定日期 **/
	public void setRateYmd(String value) {
		this.rateYmd = value;
	}

	/** 設定是否為新客戶 **/
	public void setIsNew(String isNew) {
		this.isNew = isNew;
	}

	/** 取得是否為新客戶 **/
	public String getIsNew() {
		return isNew;
	}

	/** 設定產品種類 **/
	public void setLnType(String lnType) {
		this.lnType = lnType;
	}

	/** 取得產品種類 **/
	public String getLnType() {
		return lnType;
	}

	/** 設定報送狀態 **/
	public void setStatus(String status) {
		this.status = status;
	}

	/** 取得報送狀態 **/
	public String getStatus() {
		return status;
	}

	/** 設定現請額度幣別 **/
	public void setCurrEndRate(BigDecimal currEndRate) {
		this.currEndRate = currEndRate;
	}

	/** 取得現請額度幣別 **/
	public BigDecimal getCurrEndRate() {
		return currEndRate;
	}

}
