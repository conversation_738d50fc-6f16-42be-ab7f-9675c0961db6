/* 
 * L140S09CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L140S10C;

public interface L140S10CDao extends IGenericDao<L140S10C> {

	L140S10C findByOid(String oid);
	
	List<L140S10C> findByMainId(String mainId);

	L140S10C findMaxBizContentBy(String mainId, String bizCat, String bizItem);

	List<L140S10C> findByMainIdAndBizCat(String mainId, String bizCat);

	List<L140S10C> findByMainIdAndBizCatAndBizItem(String mainId, String bizCat, String bizItem);

	List<L140S10C> findByMainIdAndBizCatAndBizItemAndSequence(String mainId, String bizCat, String bizItem, int[] seqArray);
}