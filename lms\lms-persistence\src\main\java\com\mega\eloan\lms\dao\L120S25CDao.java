/* 
 * L120S25CDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S25C;

/** BIS評估表參數檔 **/
public interface L120S25CDao extends IGenericDao<L120S25C> {

	L120S25C findByOid(String oid);

	 
	List<L120S25C> findByParamTypeDate(String paramType, String paramDate);

	List<L120S25C> findByParamTypeKeyDate(String paramType, String paramKey,
			String paramDate);
}