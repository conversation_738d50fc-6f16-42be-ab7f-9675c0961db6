package com.mega.eloan.lms.cls.pages;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.cls.service.CLS1220Service;
import com.mega.eloan.lms.model.C122M01A;

/**
 * <pre>
 * 線上勞工紓困貸款
 * </pre>
 * 
 * @since 2020/5/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/5/1,EL08034,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/cls1220m03/{page}")
public class CLS1220M03Page extends AbstractEloanForm {
	
	@Autowired
	CLS1220Service service;
	
	final String TAB_CTX = "_tabCtx";
	final String TAB_SIGN = "tab-";

	@Override
	public void execute(ModelMap model, PageParameters params) {
		renderJsI18N(CLS1220M03Page.class);
		renderJsI18N(CLS1220V01Page.class);
		model.addAttribute("loadScript", "loadScript('pagejs/cls/CLS1220M03Page');");
		String mainId = params.getString(EloanConstants.MAIN_ID);
		// 依權限設定button
//		add(new AclLabel("_btnDOC_EDITING", params, getDomainClass(),
//				AuthType.Modify, CLSDocStatusEnum.編製中));
		model.addAttribute("_btnDOC_EDITING_visible", true);

		C122M01A c122m01a = service.getC122M01A_byMainId(mainId);
		model.addAttribute("agreeQueryEJ_Y_detail_visible", c122m01a!=null&&Util.equals("Y", c122m01a.getAgreeQueryEJ()));
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C122M01A.class;
	}
}
