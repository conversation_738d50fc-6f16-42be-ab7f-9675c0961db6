$(document).ready(function () {
    i18n.def['btnThickBoxDW_OTS_TRPAYLG'] = "薪轉戶年薪查詢";
    var buttons = {
        "saveData": function () {
            saveMain();
        },
        "btnThickBoxDW_OTS_TRPAYLG":function (){
            btnThickBoxDW_OTS_TRPAYLG();
        },
        "close": function () {
            $.thickbox.close();
        }
    }

    if(CLS1131S01.readOnly || CLS1131S01.isC120M01A){
        delete buttons["saveData"];
        delete buttons["btnThickBoxDW_OTS_TRPAYLG"];
    }

    var newOid = "";

    var inputInomeViewThickBox = function (obOid){
        $("#inputInomeView").thickbox({
            modal: false,
            width: 900,
            buttons: {
                "saveData": function () {
                    saveData(obOid).done(function(returnOid){
                        // 將新增的資料oid回傳，免得資料每按一次確定就新增
                        obOid = returnOid;
                        incomeGrid.trigger("reloadGrid");
                    });
                },
                "close": function () {
                    $.thickbox.close();
                    incomeGrid.trigger("reloadGrid");
                }
            }
        });
    }

    var openC101s01wDoc = function (cellvalue, options, rowObject) {
        $.ajax({
            handler: CLS1131S01.handler,
            action: "getPersonalIncomeByFormula",
            data: $.extend({
                custId: CLS1131S01.data.custId,
                dupNo: CLS1131S01.data.dupNo,
                mainId: CLS1131S01.data.mainId,
                sOid: rowObject.oid,
                isC120M01A:CLS1131S01.isC120M01A
            }, {}),
            success: function (json) {
                // alert(json.c101s01w.formulaType);
                $("#incomeContent").find("table[id^='formula_']").hide();
                $("#incomeContent").find("#formula_" + json.c101s01w.formulaType).show();

                $("#inputIncomeViewForm").reset();
                $("#inputIncomeViewForm").injectData(json.c101s01w);
                $("#incomeItemName").val($("#incomeItem").find("option[value='" + $("#incomeItem").val() + "']").text());
                inputInomeViewThickBox(rowObject.oid);
                getFormulaFunction();
            }
        });
    }


    var incomeGrid = $("#incomeGrid").iGrid({
        handler: "cls1131gridhandler",
        height: 350,
        width: 785,
        autowidth: false,
        action: "queryC101S01W",
        postData: {
            custId: CLS1131S01.data.custId,
            dupNo: CLS1131S01.data.dupNo,
            mainId: CLS1131S01.data.mainId,
            isC120M01A:CLS1131S01.isC120M01A
        },
        rowNum: 15,
        colModel: [{
            colHeader: "收入項目",
            name: 'incomeItem',
            width: 140,
            sortable: false,
            formatter: 'click',
            onclick: openC101s01wDoc
        }, {
            colHeader: "收入類別",
            name: 'incomeType',
            width: 80,
            align: "left",
            sortable: false
        }, {
            colHeader: "年化後金額",
            name: 'valueYear',
            width: 150,
            sortable: false,
            formatter: GridFormatter.number['addComma']
        }, {
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }],
        ondblClickRow: function (rowid) {
            openC101s01wDoc(null, null, incomeGrid.getRowData(rowid));
        }
    });


    $("#addIncomeInput").click(function () {
        $("#inputIncomeViewForm").reset();
        $("#incomeItem").trigger("change");
        newOid = "";
        if ($("#personalIncomeDetailFormV2").valid()) {
            inputInomeViewThickBox(newOid);
        }

    });
    $("#removeIncomeInput").click(function () {
        var selrow = incomeGrid.getGridParam('selrow');
        if (selrow) {
            var ret = incomeGrid.getRowData(selrow);
            $.ajax({
                handler: CLS1131S01.handler,
                action: "removePersonalIncomeByFormula",
                data: $.extend({
                    custId: CLS1131S01.data.custId,
                    dupNo: CLS1131S01.data.dupNo,
                    mainId: CLS1131S01.data.mainId,
                    sOid: ret.oid
                }, {}),
                success: function (json) {
                    $("#payAmt").val(json.payAmt);
                    $("#othAmt").val(json.othAmt);
                    incomeGrid.trigger("reloadGrid");
                }
            });
        }

    });

    var codeTypteItems = CommonAPI.loadCombos(["c101s01w_incomeItem"]);
    $("#incomeItem").setItems({
        item: codeTypteItems["c101s01w_incomeItem"],
        format: "{value} - {key}",
        space: true
    });
    $("#incomeItem").find("option:contains(V)").remove();

    $("#incomeItem").change(function () {
        getFormulaFunction();
    });
    $("#actuallyIncomeSum").change(function () {
        if($("#incomeItem").val()!=''){
            getFormulaFunction();
        }
    });
    function getFormulaFunction(){
        if ($("#incomeItem").val() == "") {
            $("#incomeItemName").val("");
        } else {
            $("#incomeItemName").val($("#incomeItem").find("option[value='" + $("#incomeItem").val() + "']").text());
        }
        if($("#incomeItem").val()=="B01" || $("#incomeItem").val()=="B02"
         ||$("#incomeItem").val()=="B04" || $("#incomeItem").val()=="B05"
         ||$("#incomeItem").val()=="C01" || $("#incomeItem").val()=="C02"
         ||$("#incomeItem").val()=="D01" || $("#incomeItem").val()=="D02"
         ||$("#incomeItem").val()=="D03" || $("#incomeItem").val()=="D04"){
            $("#actuallyIncomeSumLabel").show();
        }
        else{
            $("#actuallyIncomeSumLabel").hide();
            $('#actuallyIncomeSum').attr('checked',false);
        }
        $.ajax({
            handler: CLS1131S01.handler,
            action: "getPersonalIncomeFormula",
            data: $.extend({
                incomeItem: $("#incomeItem").val(),
                actuallyIncomeSum:$('#actuallyIncomeSum').attr('checked')?"V":""
            }, {}),
            success: function (json) {
                // $("#incomeContent").empty().append($("#incomeTypeView").clone()).append($("#" + json.formula).clone()) ;
                $("#incomeContent").find("table[id^='formula_']").hide();
                $("#incomeContent").find("#" + json.formula).show();
            }
        });
    }

    var saveData = function (sOid) {
        var deferred = $.Deferred();

        if (CLS1131S01.readOnly || CLS1131S01.isC120M01A) {
            deferred.resolve();
        } else {
            if ($("#inputIncomeViewForm").valid()) {

                $.ajax({
                    handler: CLS1131S01.handler,
                    action: "savePersonalIncomeByFormula",
                    formId: "inputIncomeViewForm",
                    data: $.extend({incomeViewForm: JSON.stringify($("#inputIncomeViewForm").find("table[id^='formula_']:visible,#incomeTypeView,#incomeItemView").serializeData())}, {
                        custId: CLS1131S01.data.custId,
                        dupNo: CLS1131S01.data.dupNo,
                        mainId: CLS1131S01.data.mainId,
                        sOid: sOid,
                        positionType: $("input[name='positionType']:checked").val()
                    }),
                    success: function (json) {
                        API.showPopMessage(i18n.def.saveSuccess);
                        if (json.warnMsg) {
                            API.showPopMessage(json.warnMsg);
                        }
                        $("#inputIncomeViewForm").find("table[id^='formula_']:visible").injectData(json);
                        deferred.resolve(json.sOid);
                    }
                });
            }

            return deferred.promise();
        }
    }

    var saveMain = function () {
        var deferred = $.Deferred();

        if (CLS1131S01.readOnly || CLS1131S01.isC120M01A) {
            deferred.resolve();
        } else {
            if ($("#personalIncomeDetailFormV2").valid()) {
                $.ajax({
                    handler: CLS1131S01.handler,
                    action: "savePersonalIncomeDetailV2",
                    formId: "personalIncomeDetailFormV2",
                    data: $.extend({}, {
                        custId: CLS1131S01.data.custId,
                        dupNo: CLS1131S01.data.dupNo,
                        mainId: CLS1131S01.data.mainId,
                        positionType: $("input[name='positionType']:checked").val()
                    }),
                    success: function (json) {
                        API.showPopMessage(i18n.def.saveSuccess);
                        if (json.warnMsg) {
                            API.showPopMessage(json.warnMsg);
                        }
                        $("#payAmt").val(json.payAmt);
                        $("#othAmt").val(json.othAmt);
                        incomeGrid.trigger("reloadGrid");
                        deferred.resolve();
                    }
                });
            }
            return deferred.promise();
        }
    }

    $("#personalIncomeDetailCust").val(CLS1131S01.data.custName + "(" + CLS1131S01.data.custId + ")");
    $.ajax({
        handler: CLS1131S01.handler,
        action: "getPersonalIncomeDetailWithVersionExt",
        data: {
            custId: CLS1131S01.data.custId,
            dupNo: CLS1131S01.data.dupNo,
            mainId: CLS1131S01.data.mainId
        },
        success: function (response) {
            var pidForm = $("#personalIncomeDetailFormV2,#inputIncomeViewForm");
            $("#personalIncomeDetailFormV2").injectData(response);

            if (needReset) {
                pidForm.reset();
            }
            $("#personalIncomeDetailViewVer").thickbox({
                open: function () {
                    if (CLS1131S01.readOnly || CLS1131S01.isC120M01A) {
                        pidForm.lockDoc();
                        pidForm.find("input[name^='formula_']").filter(":not(.readonly)").css("background-color", "#E0E0E0");
                    }
                },
                width: 950,
                height: 600,
                buttons: buttons
            })
        }
    });

    $("#incomeNote1").click(function () {
//        $("#incomeNote1Desc").thickbox({
//            width: 640,
//            height: 480,
//            modal: false
//        });
        // J-113-0273 調整個金徵信檢視收入內容於點選「相關說明」後開啟PDF檔
        $.form.submit({
            url: webroot + '/app/simple/FileProcessingService',
            target: "_blank",
            data: {
            	markModel: "CreditIncome",
                fileDownloadName: "CreditIncome.pdf",
                serviceName: "cls1131s01pdfservice"
            }
        });
    });

    function build_respDW_OTS_TRPAYLG(json) {
        var dyna = [];

        if (json && json.list_hasData == "Y") {
            dyna.push("<div class=''>" + json.begDate + "~" + json.endDate + " 共有 " + json.list_data.arr_detail.length + " 筆資料</div>");
        }
        if (json && json.list_hasData == "N") {
            dyna.push("<div class='text-red'>" + json.begDate + "~" + json.endDate + " 查無資料</div>");
        }
        //==========
        dyna.push("<div style='overflow:auto' >");
        dyna.push("<table border='1' width='100%' class='tb2 '>");
        dyna.push("<tr class='hd2 ' >");
        dyna.push("<td>交易分行&nbsp;</td>");
        dyna.push("<td style='width:85px;'>交易日期&nbsp;</td>");
        //J-111-0301 調整薪轉戶年薪查詢顯示畫面 
        dyna.push("<td>交易機號&nbsp;</td>");
        dyna.push("<td>入薪帳號&nbsp;</td>");
        dyna.push("<td>交易備註&nbsp;</td>");
        dyna.push("<td style='width:100px;'>金額&nbsp;</td>");
        dyna.push("</tr>");
        if (json && json.list_hasData == "Y") {
            $.each(json.list_data.arr_detail, function (idx, item) {
                dyna.push(item.is_even == "N" ? "<tr>" : "<tr style='background-color:lavender;'>");
                dyna.push("<td>" + item.BR_CD + "&nbsp;</td>");
                dyna.push("<td>" + item.TX_ACCT_DT + "&nbsp;</td>");
                dyna.push("<td>" + item.TRM_ID + "&nbsp;</td>");
                dyna.push("<td>" + item.ACCT_KEY + "&nbsp;</td>");
                dyna.push("<td>" + item.MISC + "&nbsp;</td>");
                dyna.push("<td style='text-align:right;'>" + item.TX_AMT + "&nbsp;</td>");
                dyna.push("</tr>");
            });
        } else {
            dyna.push("<tr>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("</tr>");
        }
        dyna.push("</table>");
        //==========
        dyna.push("&nbsp;");
        //==========
        dyna.push("<table border='1' style='width:440px;' class='tb2 '>");
        dyna.push("<tr class='hd2 ' >");
        dyna.push("<td style='width:200px;'>入薪帳號&nbsp;</td>");
        dyna.push("<td style='width:120px;'>查詢期間加總金額&nbsp;</td>");
        dyna.push("</tr>");
        if (json && json.list_hasData == "Y") {
            $.each(json.list_data.arr_summary, function (idx, item) {
                dyna.push("<tr>");
                //dyna.push("<td>" + item.TAX_NO + "&nbsp;</td>");
                dyna.push("<td>" + item.ACCT_KEY + "&nbsp;</td>");
                dyna.push("<td style='text-align:right;'>" + item.TX_AMT + "&nbsp;</td>");
                dyna.push("</tr>");
            });
        } else {
            dyna.push("<tr>");
            //dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("<td>&nbsp;</td>");
            dyna.push("</tr>");
        }
        dyna.push("</table>");
        //==========
        dyna.push("</div>");
        $("#respDW_OTS_TRPAYLG").html(dyna.join("\n"));
    }

    function btnThickBoxDW_OTS_TRPAYLG(){
        var today = API.getToday();
        var todayArr = today.split('-');
        var begDate = new Date(parseInt(todayArr[0])-1, todayArr[1] - 1, 1);
        begDate = begDate.getFullYear() + "-" + (begDate.getMonth() < 9 ? "0" : "") + (begDate.getMonth() + 1) + "-" + (begDate.getDate() < 10 ? "0" : "") + begDate.getDate();
        $("#formDW_OTS_TRPAYLG").injectData({
            'trpaylg_id': CLS1131S01.data.custId,
            'trpaylg_name': CLS1131S01.data.custName,
            'trpaylg_begDate': begDate,
            'trpaylg_endDate': today
        });
        build_respDW_OTS_TRPAYLG();
        var ptaFlag_val = $("input[name='ptaFlag']:checked").val();
        var ptaFlag_desc = "";
        if (ptaFlag_val == "Y") {
            ptaFlag_desc = "是";
        } else if (ptaFlag_val == "N") {
            ptaFlag_desc = "否";
        }
        $("#divQueryDW_OTS_TRPAYLG").thickbox({
            title: CLS1131S01.data.custId + ' ' + CLS1131S01.data.custName + " " + ("(薪轉戶註記：" + ptaFlag_desc + "，資料日期：" + ($("#ptaDataDt").val() || '') + ")"),
            width: 850,
            height: 480,
            modal: false
        });
    }



    $("#btnQueryDW_OTS_TRPAYLG").click(function () {
        if ($("#formDW_OTS_TRPAYLG").valid()) {
            MegaApi.confirmMessage("是否以「個資查詢理由：08-其他作業前之客戶資料查詢」查詢客戶薪轉記錄？" + "<br/>"
                + "查詢記錄將會留存，並顯示在 LLDCMILG 個資查詢交易記錄報表 ", function (r) {
                if (r) {
                    $.ajax({
                        handler: CLS1131S01.handler,
                        action: 'log_OTS_TRPAYLG',
                        data: $.extend({
                            custId: CLS1131S01.data.custId,
                            dupNo: CLS1131S01.data.dupNo,
                            mainId: CLS1131S01.data.mainId,
                            inqcode: '08'
                        }, $("#formDW_OTS_TRPAYLG").serializeData()),
                        success: function (log_response) {
                            $.ajax({
                                handler: CLS1131S01.handler,
                                action: 'query_OTS_TRPAYLG',
                                data: $.extend({
                                    custId: CLS1131S01.data.custId,
                                    dupNo: CLS1131S01.data.dupNo,
                                    mainId: CLS1131S01.data.mainId
                                }, $("#formDW_OTS_TRPAYLG").serializeData()),
                                success: function (response) {
                                    build_respDW_OTS_TRPAYLG(response);
                                }
                            });
                        }
                    });

                }
            });

        }
    });
});
