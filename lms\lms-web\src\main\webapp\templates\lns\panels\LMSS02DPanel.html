<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="lmss02d_panel">
			<!-- 申貸戶與本行婉卻紀錄共用元件
			
			各 class 用途 
			● hReject 
				此 html 的 radioBtn : IsRejt 來控制 show/hide
			● canEdit
			● hideMemo --- 針對UI欄位「婉卻變更Memo」
					自 server 抓 rejtCaseAdjMemo, 有值,才顯示
					 
			● showBefore --- 針對UI欄位「變更前婉卻控管種類」
				LMSS02BPage01.js :: queryBorrow
					if(obj2.rejtCaseBefore){
						$L120S01aForm.find(".showBefore").show();
				 	}
					
				LMSS02DPage01.js
					var editReject = $("#editReject").thickbox({		
			 -->
 			<!-- 申貸戶與本行婉卻紀錄共用元件js -->
			<script type="text/javascript">
         		loadScript('pagejs/lns/LMSS02DPage01');
         	</script>			
			<tr>
				<td class="hd1"><th:block th:text="#{'LMSS02D.html1'}">申貸戶是否本行有婉卻紀錄</th:block>&nbsp;&nbsp;</td>
				<td colspan="3">
					<div>
						<label><input type="radio" class="canEdit" id="IsRejt" name="IsRejt" value="Y" onclick="$('.hReject').show();" disabled="true" /><th:block th:text="#{'LMSS02D.html2'}">是</th:block></label>&nbsp;<label><input type="radio" class="canEdit" name="IsRejt" value="N" onclick="$('.hReject').hide();" disabled="true" /><th:block th:text="#{'LMSS02D.html3'}">否</th:block></label>
						
						<th:block th:text="#{'LMSS02D.html5'}">查詢日期</th:block>：<span class="field" id="rejtReadDate" name="rejtReadDate" style="width:100px;display: inline-block;">&nbsp;</span>
						
						<button type="button" onclick="getReject()">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html4'}">引進申貸戶婉卻紀錄</th:block></span>
						</button>	
					</div>
					<div class="hReject hide">
						<span class="color-red"><th:block th:text="#{'LMSS02D.html6'}">本申貸戶曾有本行婉卻紀錄，最近一次婉卻紀錄內容如下：</th:block></span>
						<table>						
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html7'}">登錄分行</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtBrNo" name="rejtBrNo"></span></td>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html8'}">婉卻代碼</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtCode" name="rejtCode"></span></td>
							</tr>
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html9'}">婉卻理由</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtReason" name="rejtReason"></span></td>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html10'}">登錄時間</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtDate" name="rejtDate"></span></td>
							</tr>					
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html11'}">婉卻控管種類</th:block>&nbsp;&nbsp;</td>
								<td>
									<div id="erjButton">
										<button type="button" onclick="editReject()">
											<span class="text-only"><th:block th:text="#{'LMSS02D.html12'}">修改婉卻控管種類</th:block></span>
										</button>
									</div>
									<label><input type="radio" class="canEdit" id="rejtCase" name="rejtCase" value="1" disabled="true" /><th:block th:text="#{'LMSS02D.html13'}">維持控管</th:block></label><br/><label><input type="radio" class="canEdit" name="rejtCase" value="2" disabled="true"/><th:block th:text="#{'LMSS02D.html14'}">警示不控管</th:block></label><br/><label><input type="radio" class="canEdit" name="rejtCase" value="D" disabled="true"/><th:block th:text="#{'LMSS02D.html15'}">刪除控管</th:block></label>
								</td>
								<td class="hd1"><span class="hideMemo"><th:block th:text="#{'LMSS02D.html16'}">婉卻變更Memo</th:block></span>&nbsp;&nbsp;</td>
								<td><span class="hideMemo" id="showMomo" name="showMemo"><textarea class="canEdit" id="rejtCaseAdjMemo" name="rejtCaseAdjMemo" cols="25" rows="5%" disabled="true"></textarea></span></td>
							</tr>
							<tr class="showBefore hide">
								<td class="hd1"><th:block th:text="#{'LMSS02D.html17'}">變更前婉卻控管種類</th:block>&nbsp;&nbsp;</td>
								<td colspan="3"><label><input type="radio" id="rejtCaseBefore" name="rejtCaseBefore" value="1" /><th:block th:text="#{'LMSS02D.html13'}">維持控管</th:block></label>&nbsp;<label><input type="radio" name="rejtCaseBefore" value="2" /><th:block th:text="#{'LMSS02D.html14'}">警示不控管</th:block></label>&nbsp;<label><input type="radio" name="rejtCaseBefore" value="D" /><th:block th:text="#{'LMSS02D.html15'}">刪除控管</th:block></label></td>
							</tr>						
						</table>
						<span class="color-red"><th:block th:text="#{'LMSS02D.html18'}">註：維持控管-繼續控管授權外；&nbsp;警示不控管-只會顯示訊息不強迫授權外；&nbsp;刪除控管-不再顯示</th:block></span>
					</div>						
				</td>
			</tr>
			
			<tr ><!--class="hBorrowData"-->
				<td class="hd1"><th:block th:text="#{'LMSS02D.html19'}">負責人是否本行有婉卻紀錄</th:block>&nbsp;&nbsp;</td>
				<td colspan="3">
					<div>
						<span class="field" id="rejtChairmanId" style="width:80px;display: inline-block;"></span><span class="field" id="rejtChairmanDupNo"></span>
					</div>
					<div>
						<label><input type="radio" class="canEdit" id="IsRejt1" name="IsRejt1" value="Y" onclick="$('.hReject1').show();" disabled="true" /><th:block th:text="#{'LMSS02D.html2'}">是</th:block></label>&nbsp;<label><input type="radio" class="canEdit" name="IsRejt1" value="N" onclick="$('.hReject1').hide();" disabled="true" /><th:block th:text="#{'LMSS02D.html3'}">否</th:block></label>
						
						<th:block th:text="#{'LMSS02D.html5'}">查詢日期</th:block>：<span class="field" id="rejtReadDate1" name="rejtReadDate1" style="width:100px;display: inline-block;">&nbsp;</span>
						
						<button type="button" onclick="getReject1()">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html20'}">引進負責人婉卻紀錄</th:block></span>
						</button>						
					</div>
					<div class="hReject1 hide">
						<span class="color-red"><th:block th:text="#{'LMSS02D.html21'}">負責人曾有本行婉卻紀錄，最近一次婉卻紀錄內容如下：</th:block></span>
						<table>						
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html7'}">登錄分行</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtBrNo1" name="rejtBrNo1"></span></td>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html8'}">婉卻代碼</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtCode1" name="rejtCode1"></span></td>
							</tr>
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html9'}">婉卻理由</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtReason1" name="rejtReason1"></span></td>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html10'}">登錄時間</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="rejtDate1" name="rejtDate1"></span></td>
							</tr>					
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html11'}">婉卻控管種類</th:block>&nbsp;&nbsp;</td>
								<td>
									<div id="erjButton1">
										<button type="button" onclick="editReject1()">
											<span class="text-only"><th:block th:text="#{'LMSS02D.html12'}">修改婉卻控管種類</th:block></span>
										</button>
									</div>
									<label><input type="radio" class="canEdit" id="rejtCase1" name="rejtCase1" value="1" disabled="true" /><th:block th:text="#{'LMSS02D.html13'}">維持控管</th:block></label><br/><label><input type="radio" class="canEdit" name="rejtCase1" value="2" disabled="true"/><th:block th:text="#{'LMSS02D.html14'}">警示不控管</th:block></label><br/><label><input type="radio" class="canEdit" name="rejtCase1" value="D" disabled="true"/><th:block th:text="#{'LMSS02D.html15'}">刪除控管</th:block></label>
								</td>
								<td class="hd1"><span class="hideMemo1"><th:block th:text="#{'LMSS02D.html16'}">婉卻變更Memo</th:block></span>&nbsp;&nbsp;</td>
								<td><span class="hideMemo1" id="showMomo1" name="showMemo1"><textarea class="canEdit" id="rejtCaseAdjMemo1" name="rejtCaseAdjMemo1" cols="25" rows="5%" disabled="true"></textarea></span></td>
							</tr>
							<tr class="showBefore1 hide">
								<td class="hd1"><th:block th:text="#{'LMSS02D.html17'}">變更前婉卻控管種類</th:block>&nbsp;&nbsp;</td>
								<td colspan="3"><label><input type="radio" id="rejtCaseBefore1" name="rejtCaseBefore1" value="1" /><th:block th:text="#{'LMSS02D.html13'}">維持控管</th:block></label>&nbsp;<label><input type="radio" name="rejtCaseBefore1" value="2" /><th:block th:text="#{'LMSS02D.html14'}">警示不控管</th:block></label>&nbsp;<label><input type="radio" name="rejtCaseBefore1" value="D" /><th:block th:text="#{'LMSS02D.html15'}">刪除控管</th:block></label></td>
							</tr>						
						</table>
						<span class="color-red"><th:block th:text="#{'LMSS02D.html18'}">註：維持控管-繼續控管授權外；&nbsp;警示不控管-只會顯示訊息不強迫授權外；&nbsp;刪除控管-不再顯示</th:block></span>
					</div>						
				</td>				
			</tr>
			
			<!--J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能-->
			<tr>
				<td class="hd1"><th:block th:text="#{'LMSS02D.html30'}">申貸戶是否有異常通報紀錄</th:block>&nbsp;&nbsp;</td>
				<td colspan="3">
					<div>
						<label><input type="radio" class="canEdit" id="isAbnormal" name="isAbnormal" value="Y" onclick="$('.hAbnormal').show();" disabled="true" /><th:block th:text="#{'LMSS02D.html2'}">是</th:block></label>&nbsp;<label><input type="radio" class="canEdit" name="isAbnormal" value="N" onclick="$('.hAbnormal').hide();" disabled="true" /><th:block th:text="#{'LMSS02D.html3'}">否</th:block></label>
						
						<th:block th:text="#{'LMSS02D.html5'}">查詢日期</th:block>：<span class="field" id="abnormalReadDate" name="abnormalReadDate" style="width:100px;display: inline-block;">&nbsp;</span>
						
						<button type="button" onclick="getAbnormal()">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html31'}">引進申貸戶異常通報紀錄</th:block></span>
						</button>	
						<button type="button" onclick="openDocAbnormal('')">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html42'}">調閱異常通報</th:block></span>
						</button>	
					</div>
					<div class="hAbnormal hide">
						<span class="color-red"><th:block th:text="#{'LMSS02D.html32'}">申貸戶有異常通報紀錄</th:block>，<th:block th:text="#{'LMSS02D.html33'}">最近一次通報資訊如下</th:block>：</span>
						<table>						
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html34'}">通報分行</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="abnormalBrNo" name="abnormalBrNo"></span></td>
							</tr>
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html35'}">通報時間</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="abnormalDate" name="abnormalDate"></span></td>
							</tr>					
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html36'}">目前異常通報狀態</th:block>&nbsp;&nbsp;</td>
								<td>
									<select name="abnormalStatus" id="abnormalStatus" readonly="readonly" disabled="true">
		                                <option value="Y" disabled="true"><th:block th:text="#{'LMSS02D.html37'}">已解除</th:block></option>
		                                <option value="N" disabled="true"><th:block th:text="#{'LMSS02D.html38'}">未解除</th:block></option>		                      
		                            </select>
									<br>
									<span id="abnormalMainId" style="display:none"></span>
								</td>
							</tr>
												
						</table>
					</div>						
				</td>
			</tr>
			<!--J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能-->
			<tr>
				<td class="hd1"><th:block th:text="#{'LMSS02D.html44'}">負責人是否有異常通報紀錄</th:block>&nbsp;&nbsp;</td>
				<td colspan="3">
					<div>
						<span class="field" id="abnormalChairmanId" style="width:80px;display: inline-block;"></span><span class="field" id="abnormalChairmanDupNo"></span>
					</div>
					<div>
						<label><input type="radio" class="canEdit" id="isAbnormal1" name="isAbnormal1" value="Y" onclick="$('.hAbnormal1').show();" disabled="true" /><th:block th:text="#{'LMSS02D.html2'}">是</th:block></label>&nbsp;<label><input type="radio" class="canEdit" name="isAbnormal1" value="N" onclick="$('.hAbnormal1').hide();" disabled="true" /><th:block th:text="#{'LMSS02D.html3'}">否</th:block></label>
						
						<th:block th:text="#{'LMSS02D.html5'}">查詢日期</th:block>：<span class="field" id="abnormalReadDate1" name="abnormalReadDate1" style="width:100px;display: inline-block;">&nbsp;</span>
						
						<button type="button" onclick="getAbnormal1()">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html39'}">引進負責人異常通報紀錄</th:block></span>
						</button>
						<button type="button" onclick="openDocAbnormal('1')">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html42'}">調閱異常通報</th:block></span>
						</button>	
					</div>
					<div class="hAbnormal1 hide">
						<span class="color-red"><th:block th:text="#{'LMSS02D.html40'}">負責人有異常通報紀錄</th:block>，<th:block th:text="#{'LMSS02D.html33'}">最近一次通報資訊如下</th:block>：</span>
						<table>						
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html34'}">通報分行</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="abnormalBrNo1" name="abnormalBrNo1"></span></td>
							</tr>
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html35'}">通報時間</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="abnormalDate1" name="abnormalDate1"></span></td>
							</tr>					
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html36'}">目前異常通報狀態</th:block>&nbsp;&nbsp;</td>
								<td>
									<select name="abnormalStatus1" id="abnormalStatus1" readonly="readonly" disabled="true">
		                                <option value="Y" disabled="true"><th:block th:text="#{'LMSS02D.html37'}">已解除</th:block></option>
		                                <option value="N" disabled="true"><th:block th:text="#{'LMSS02D.html38'}">未解除</th:block></option>		                      
		                            </select>
									<br>
									<span id="abnormalMainId1" style="display:none"></span>
								</td>
							</tr>
												
						</table>
					</div>						
				</td>
			</tr>
			<!--J-111-0535_05097_B1001 Web e-Loan企金授信配合「ESG綜效調查表 」建置，於簽報書增設相對應欄位-->
			<tr>
				<td class="hd1"><th:block th:text="#{'LMSS02D.html45'}">申貸戶是否有因不符ESG而暫緩承作</th:block>&nbsp;&nbsp;</td>
				<td colspan="3">
					<div>
						<label><input type="radio" class="canEdit" id="isEsgRejt" name="isEsgRejt" value="Y" onclick="$('.hEsgReject').show();" disabled="true" /><th:block th:text="#{'LMSS02D.html2'}">是</th:block></label>&nbsp;
						<label><input type="radio" class="canEdit" name="isEsgRejt" value="N" onclick="$('.hEsgReject').hide();" disabled="true" /><th:block th:text="#{'LMSS02D.html3'}">否</th:block></label>
						<th:block th:text="#{'LMSS02D.html5'}">查詢日期</th:block>：<span class="field" id="esgRejtReadDate" name="esgRejtReadDate" style="width:100px;display: inline-block;">&nbsp;</span>
						<button type="button" onclick="getEsgReject()">
							<span class="text-only"><th:block th:text="#{'LMSS02D.html49'}">引進申貸戶ESG暫緩承作紀錄</th:block></span>
						</button>	
					</div>
					<div class="hEsgReject hide">
						<span class="color-red"><th:block th:text="#{'LMSS02D.html48'}">本申貸戶曾有不符ESG而暫緩承作紀錄，最近一次暫緩承作紀錄內容如下：</th:block></span>
						<table>						
							<tr>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html46'}">登錄分行</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="esgRejtBrNo" name="esgRejtBrNo"></span></td>
								<td class="hd1"><th:block th:text="#{'LMSS02D.html47'}">登錄行員及時間</th:block>&nbsp;&nbsp;</td>
								<td><span class="field" id="esgRejtEmpNo" name="esgRejtEmpNo"></span>(<span class="field" id="esgRejtDate" name="esgRejtDate"></span>)</td>
							</tr>
						</table>
					</div>						
				</td>
			</tr>
			
	</th:block>
</body>
</html>
