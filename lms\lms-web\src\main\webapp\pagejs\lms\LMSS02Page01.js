var _handler = "";
var initDfd = initDfd || $.Deferred();
initDfd.done(function(){
    setCloseConfirm(true);
    if (responseJSON.docURL == "/lms/lms1205m01") {
        // 授權外企金
        _handler = "lms1205formhandler";
    }
    else {
        // 授權內企金
        _handler = "lms1105formhandler";
    }
    // 內層GridView
    // alert(JSON.stringify(responseJSON));
    $("#getCustData").click(function(){
		var $addborrow = $("#addborrow");
		var $custId = $addborrow.find("#custId").val();
		var $custName = $addborrow.find("#custName").val();
		if(($custId != null && $custId != undefined && $custId != '')
		&& ($custName != null && $custName != undefined && $custName != '')){
			// 統一編號、名稱擇一輸入引進即可
			CommonAPI.showErrorMessage(i18n.lmss02["l120s02.alert26"]);
		}else if(($custId == null || $custId == undefined || $custId == '')
		&& ($custName == null || $custName == undefined || $custName == '')){
			// 請輸入統一編號或名稱
			CommonAPI.showErrorMessage(i18n.lmss02["l120s02.alert27"]);
		}else{
		    var defaultOption = {};
			if($custId != null && $custId != undefined && $custId != ''){
				defaultOption = {
					defaultValue: $custId //預設值 
				};
			}else{
				defaultOption = {
					defaultName : $custName
				};				
			}
			//綁入MegaID
			CommonAPI.openQueryBox(
				$.extend({
/*
	                defaultValue: $custId, //預設值 
	                defaultName : $custName,
*/	
					defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
	                divId:"addborrow", //在哪個div 底下 
	                isInSide:false,
	                autoResponse: { // 是否自動回填資訊 
	                           id: "custId", // 統一編號欄位ID 
	                       dupno: "dupNo", // 重覆編號欄位ID 
	                      name: "custName" // 客戶名稱欄位ID 
	                },fn:function(obj){
						//alert(obj.buscd);
						$addborrow.find("#buscd").val(obj.buscd);
						
		                if ($("#addborrow").valid()) {
		                    var exist = false;
		                    // 企金
		                    $.ajax({
		                        // 驗證前端資料有無和後端資料重複
		                        type: "POST",
		                        handler: _handler,
		                        data: {
		                            formAction: "checkAddBorrow",
		                            mainId: responseJSON.mainId,
									custId : obj.custid,
									dupNo : obj.dupno,
		                            checkdata: exist
		                        }
							}).done(function(responseData){
		                            $.thickbox.close();
		                            exist = responseData.checkdata;
		                            if (exist == true) {
		                                CommonAPI.showMessage(i18n.lmss02["l120s02.alert8"]);
		                            }
		                            else {
		                                //$("#invMDscr").val("");
		                                // 沒有任何主要借款人
		                                if (!responseData.haseCust) {
		                                    CommonAPI.confirmMessage(i18n.lmss02["l120s02.confirm4"], function(b){
		                                        if (b) {
		                                            addNewBor(obj, true);
		                                        }
		                                        else {
		                                            addNewBor(obj, false);
		                                        }
		                                    });
		                                }
		                                else {
		                                    addNewBor(obj, false);
		                                }
		                                // $.thickbox.close();
		                            }
		                            //$.thickbox.close();
		                    });
		                }						
					}
				},defaultOption)
			);			
		}
    });
    
    var L120S01aGrid = $("#l120s01agrid").iGrid({ // 借款人基本資料GridView
        handler: 'lms1205gridhandler',
        height: 350,
        sortname: 'keyMan',
        postData: {
            formAction: "queryL120s01a",
            rowNum: 10
        },
        rownumbers: true,
        rowNum: 10,
        // multiselect : true,
        colModel: [{
            colHeader: "&nbsp;", // 主要借款人Flag
            align: "center",
            width: 10, // 設定寬度
            sortable: true, // 是否允許排序
            name: 'keyMan' // col.id
        }, {
            colHeader: i18n.lmss02["l120s01a.custid"], // 身分證統編
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'custId' // col.id
        }, {
            colHeader: i18n.lmss02["l120s01a.custname2"], // 借款人名稱
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custName' // col.id
        }, {
            colHeader: i18n.lmss02["l120s01a.custrlt"], // 與主要借款人關係
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custRlt' // col.id
        }, {
            colHeader: i18n.lmss02["l120s01a.custpos"], // 相關身份
            align: "left",
            width: 100, // 設定寬度
            sortable: true, // 是否允許排序
            // formatter : 'click',
            // onclick : function,
            name: 'custPos' // col.id
        }, {
            colHeader: "&nbsp",//"檢核欄位",
            name: 'chkYN',
            width: 20,
            sortable: true,
            align: "center"
        }, {
            colHeader: "docType",
            name: 'docType',
            hidden: true
        }, {
            colHeader: "oid",
            name: 'oid',
            hidden: true
        }],
        ondblClickRow: function(rowid){ // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
            var data = L120S01aGrid.getRowData(rowid);
            openDoc(null, null, data);
        }
    });
});

function thickboxTrust(){
    $("#thickboxTrust").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmss02["l120s02.thickbox16"],
        width: 300,
        height: 200,
        modal: true,
        align: "center",
        valign: "bottom",
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                $(".trust").val("");
                var trustVal = $("input[name='selTrust']:radio:checked").val();
                if (trustVal == 1) {
                    getL120s01c(1);
                    $.thickbox.close();
                }
                else if (trustVal == 2) {
                        //getL120s01c(2);
                        // 內部Mow信評;
                        mowGrid();
                        $.thickbox.close();
                        $("#mowGrid").resetSelection();
                        $("#thickboxMow").thickbox({
                            // 使用選取的內容進行彈窗
                            title: i18n.lmss02["l120s02.thickbox17"],
                            width: 640,
                            height: 380,
                            modal: true,
                            align: "center",
                            valign: "bottom",
                            i18n: i18n.def,
                            buttons: {
                                "sure": function(){
									var $L120S01aForm = $("#L120S01aForm");
                                    var row = $("#mowGrid").getGridParam('selrow');
                                    var crdType = "", crdTBR = "", crdTYear = "", grade = "", finYear = "";
                                    var prospect = "", prCustId = "", prDupNo = "", prCNAME = "", prFR = "";
                                    var prFinDate = "", prMOWBr = "";
                                    var data = $("#mowGrid").getRowData(row);
                                    crdTYear = data.crdTYear;
                                    crdTYear = (crdTYear == undefined ? "" : crdTYear);
                                    if (crdTYear != "") {
                                        $.ajax({
                                            handler: _handler,
                                            type: "POST",
                                            dataType: "json",
                                            data: {
                                                formAction: "saveMowTrust",
                                                mainId: responseJSON.mainId,
                                                crdType: data.crdType,
                                                _crdType: data._crdType,
                                                crdTBR: data.crdTBR,
                                                _crdTBR: data._crdTBR,
                                                crdTYear: crdTYear,
                                                grade: data.grade,
                                                finYear: data.finYear,
                                                prospect: data.prospect,
                                                prCustId: data.prCustId,
                                                prDupNo: data.prDupNo,
                                                prCNAME: data.prCNAME,
                                                prFR: data.prFR,
                                                prFinDate: data.prFinDate,
                                                prMOWBr: data.prMOWBr,
                                                custId: $L120S01aForm.find("#custId").html(),
                                                dupNo: $L120S01aForm.find("#dupNo").html(),
                                                noteId: data.noteId,
                                                pr: data.pr,
                                                sa: data.sa,
                                                spr: data.spr,
                                                fr: data.fr,
                                                warn1: data.warn1,
                                                warn2: data.warn2,
                                                warn3: data.warn3
                                            }
										}).done(function(json){
												var $L120S01aForm = $("#L120S01aForm");
                                                $L120S01aForm.setData(json.L120S01aForm, false);
                                                $.thickbox.close();
                                                $.thickbox.close();
                                                CommonAPI.showMessage(json.NOTIFY_MESSAGE);
                                        });
                                    }
                                    else {
                                        CommonAPI.showMessage(i18n.lmss02["l120s02.alert1"]);
                                    }
                                },
                                "cancel": function(){
                                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                                        if (res) {
                                            $.thickbox.close();
                                        }
                                    });
                                }
                            }
                        });
                    }
                    else {
                        getL120s01c(4);
                        $.thickbox.close();
                    }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function queryBorrow(rowObject){
    //每次開啟都是第一頁
    $("#tabs-w").tabs({
        selected: 0
    });
    // 讀取已有的資料以進行修改
    ilog.debug(rowObject);
    // 企金
    $.ajax({ // 查詢主要借款人資料
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "queryBorrow",
            mainId: responseJSON.mainId,
            oid: rowObject.oid,
            theOid: rowObject.oid,
            isOther: false
        }
	}).done(function(obj2){       
            ilog.debug(obj2);
            var $L120S01aForm = $("#L120S01aForm");
            var $L120S01fForm = $("#L120S01fForm");
            
            /**
             // 利用CodeType取得國別及幣別
             var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy", "lms1205s01_RelClass", "Relation_type31", "Relation_type32", "Relation_type1", "Relation_type2", "lms1205s01_CustPos", "lms1205s01_posType", "lms1205s01_Unit", "lms1205s01_stockStatus"]);
             // 幣別
             $L120S01aForm.find("#rgtCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01aForm.find("#cptlCurr").setItems({
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01aForm.find("#invMCurr").setItems({
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             // 國別
             $L120S01aForm.find("#ntCode").setItems({
             item: obj.CountryCode,
             format: "{value} - {key}",
             space: true
             });
             // 關係類別
             $L120S01aForm.find("#custRlt_main").setItems({
             item: obj.lms1205s01_RelClass,
             format: "{key}",
             space: false
             });
             // 與主要借款人關係1
             $L120S01aForm.find("#custRlt_content1").setItems({
             item: obj.Relation_type1,
             format: "{key}",
             space: false
             });
             // 與主要借款人關係2
             $L120S01aForm.find("#custRlt_content2").setItems({
             item: obj.Relation_type2,
             format: "{key}",
             space: false
             });
             // 與主要借款人關係3
             $L120S01aForm.find("#custRlt_content3").setItems({
             item: obj.Relation_type31,
             format: "{key}",
             space: false
             });
             // 與主要借款人關係4
             $L120S01aForm.find("#custRlt_content4").setItems({
             item: obj.Relation_type32,
             format: "{key}",
             space: false
             });
             // 相關身份
             $L120S01aForm.find("#custPos").setItems({
             item: obj.lms1205s01_CustPos,
             format: "{key}",
             space: false
             });
             // 負責人欄類型
             $L120S01aForm.find("#posType").setItems({
             item: obj.lms1205s01_posType,
             format: "{key}",
             space: false
             });
             // 金額單位
             $L120S01aForm.find("#rgtUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01aForm.find("#cptlUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             // 股票上市上櫃情形
             $L120S01aForm.find("#stockStatus").setItems({
             item: obj.lms1205s01_stockStatus,
             format: "{key}",
             space: false
             });
             
             **/
            $("#L120S01gForm_1").find("#tabs-2_show span").each(function(a){
                $(this).html("");
            });
            $("#L120S01gForm_2").find("#tabs-3_show span").each(function(b){
                $(this).html("");
            });
             $("#L120S01gForm_2").find(".classfinItem").each(function(x){
                // 初始化財務比率Table
                $(this).hide();
            });
            /**
             // 幣別
             $L120S01fForm.find("#dpAvgCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#fxCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#fx2Curr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#imCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#im2Curr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#exCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#ex2Curr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#cntrCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             $L120S01fForm.find("#nonLoanCurr").setItems({
             clear: false,
             item: obj.Common_Currcy,
             format: "{value} - {key}",
             space: true
             });
             
             // 金額單位
             $L120S01fForm.find("#dpAvgUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#fxUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#fx2Unit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#imUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#im2Unit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#exUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#ex2Unit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#cntrUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             $L120S01fForm.find("#nonLoanUnit").setItems({
             item: obj.lms1205s01_Unit,
             format: "{key}",
             space: false
             });
             */
            // 控制CodeType下拉式選單是否唯讀
            if (responseJSON.readOnly.toString() == "true") {
                $("#L120S01aForm select").prop("disabled", true);
                $("#L120S01fForm select").prop("disabled", true);
            }
            // 將讀取好的資料存進前端網頁中
            // $("form").setData(obj2);
            // 初始化前端form
            // 第一頁籤
            $L120S01aForm.reset();
            $L120S01aForm.find(".hideMow1").hide();
            $L120S01aForm.find(".hideMow2").hide();
            $L120S01aForm.find(".hideMow3").hide();
            $L120S01aForm.find(".hideMow4").hide();
            $L120S01aForm.find(".hideMow5").hide();
            $L120S01aForm.find(".hideMow6").hide();
            //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
            $L120S01aForm.find(".hideMow7").hide();
            //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
            $L120S01aForm.find(".hideMow8").hide();
            $L120S01aForm.find(".hideMow9").hide();
            
            if(obj2.rejtCaseBefore){
				// 控制變更前婉卻種類顯示
				$L120S01aForm.find(".showBefore").show();
			}
            if(obj2.rejtCaseBefore1){
				// 控制變更前婉卻種類顯示
				$L120S01aForm.find(".showBefore1").show();
			}
            
            // 第二頁籤
            $("#formIdDscr1").reset();
            // TODO CKeditor無法正常初始化(設定空白卻有殘留值)
            $("#L120S01gForm_1").reset();
            // 第三頁籤
            $("#formIdDscr2").reset();
            // TODO CKeditor無法正常初始化(設定空白卻有殘留值)
            $("#L120S01gForm_2").reset();
            // 第四頁籤
            $L120S01fForm.reset();
            // 開始設定資料到前端...
            // 第一頁籤
            
            
            if (obj2.L120S01aForm.showGrad) {
                $L120S01aForm.find("#showGrade").hide();
            }
            else {
                $L120S01aForm.find("#showGrade").show();
            }
            
            if (obj2.L120S01aForm.invMDscr != undefined || obj2.L120S01aForm.invMDscr != null || obj2.L120S01aForm.invMDscr != "") {
                //$L120S01aForm.find("#invMDscr").val(obj2.L120S01aForm.invMDscr);
            }

            
            $L120S01aForm.find("#typCd").text(obj2.L120S01aForm.typCd);
            if (obj2.L120S01aForm.rgtUnit != null || obj2.L120S01aForm.rgtUnit != undefined || obj2.L120S01aForm.rgtUnit != "") {
                $("#rgtUnit option").each(function(i){
					var $this = $(this);
                    if ($this.val() == RemoveStringComma(obj2.L120S01aForm.rgtUnit)) {
                        $this.prop("selected", true);
                    }
                });
            }
            if (obj2.L120S01aForm.cptlUnit != null || obj2.L120S01aForm.cptlUnit != undefined || obj2.L120S01aForm.cptlUnit != "") {
                $("#cptlUnit option").each(function(i){
					var $this = $(this);
                    if ($this.val() == RemoveStringComma(obj2.L120S01aForm.cptlUnit)) {
                        $this.prop("selected", true);
                    }
                });
            }
            
            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
            if (obj2.L120S01aForm.netAmtUnit != null || obj2.L120S01aForm.netAmtUnit != undefined || obj2.L120S01aForm.netAmtUnit != "") {
            	$("#netAmtUnit option").each(function(i){
					var $this = $(this);
					if ($this.val() == RemoveStringComma(obj2.L120S01aForm.netAmtUnit)) {
                    	$this.prop("selected", true);
                    }
                });
            }
            
            if (obj2.L120S01aForm.posType == "9") {
                $("#posDscr").show();
            }
            else {
                $("#posDscr").hide();
                obj2.L120S01aForm.posDscr = "";
            }
            
            /** 7625
             if ($("#posType").find("option:selected").val() == "9") {
             $("#posDscr").show();
             }
             else {
             $("#posDscr").hide();
             $("#posDscr").val("");
             }
             */
            if (obj2.L120S01aForm.mbRlt == "1") {
                $(".dscr1").show();
            }
            else {
                $(".dscr1").hide();
                obj2.L120S01aForm.mbRltDscr = "";
                obj2.L120S01aForm.mbMhRltDscr = "";
                if ($("input[name='mbRlt']:checked").val() == "0") {
                    $(this).prop("checked", false);
                }
            }
            
            /** 7625
             if ($("input[type='radio'][name='mbRlt']:checked").val() == "1") {
             $(".dscr1").show();
             }
             else {
             $(".dscr1").hide();
             $(".dscr1").find("#mbRltDscr").val("");
             $(".dscr1").find("#mbMhRltDscr").val("");
             if ($("input[name='mbRlt']:checked").val() == "0") {
             $(this).attr("checked", false);
             }
             }
             */
            if (obj2.L120S01aForm.mhRlt44 == "1") {
                $(".dscr2").show();
            }
            else {
                $(".dscr2").hide();
                obj2.L120S01aForm.mhRlt44Dscr = "";
                if ($("input[name='mbRlt44']:checked").val() == "0") {
                    $(this).prop("checked", false);
                }
            }
            /**
             if ($("input[type='radio'][name='mhRlt44']:checked").val() == "1") {
             $(".dscr2").show();
             }
             else {
             $(".dscr2").hide();
             $(".dscr1").find("#mhRlt44Dscr").val("");
             if ($("input[name='mbRlt44']:checked").val() == "0") {
             $(this).attr("checked", false);
             }
             }
             */
            if (obj2.L120S01aForm.mhRlt45 == "1") {
                $(".dscr3").show();
            }
            else {
                $(".dscr3").hide();
                obj2.L120S01aForm.mhRlt45Dscr = "";
                //$(".dscr1").find("#mhRlt45Dscr").val("");
                if ($("input[name='mbRlt45']:checked").val() == "0") {
                    $(this).prop("checked", false);
                }
            }
            /**
             if ($("input[name='mhRlt45']:checked").val() == "1") {
             $(".dscr3").show();
             }
             else {
             $(".dscr3").hide();
             $(".dscr1").find("#mhRlt45Dscr").val("");
             if ($("input[name='mbRlt45']:checked").val() == "0") {
             $(this).attr("checked", false);
             }
             }
             */
            
            //J-108-0195_05097_B1001 Web e-Loan企金戶授信簽報書「借款人基本資料」頁面，有關引進「有無金控法44條所稱與金控有利害關係人」增加提醒字句。
			if(obj2.L120S01aForm.showCaRlt206 == "Y"){
				$(".showCaRlt206").show();
				
				if (obj2.L120S01aForm.caRlt206 == "Y") {
	                $(".dscr206").show();
	            }
	            else {
	                $(".dscr206").hide();
	                if ($("input[name='caRlt206']:checked").val() == "0") {
	                    $(this).prop("checked", false);
	                }
	            }
				
			}else{
				$(".showCaRlt206").hide();
			}
            
            
			//J-109-0060_05097_B1001 e-Loan新加坡分行企金授信簽報書增列有無依當地法規所規範之關係人(含實質關係人)授信
			if(obj2.L120S01aForm.showLocalRlt == "Y"){
				$(".showLocalRlt").show();
				
				if (obj2.L120S01aForm.localRlt == "1") {
	                $(".dscrLocal").show();
	            }
	            else {
	                $(".dscrLocal").hide();
	                if ($("input[name='localRlt']:checked").val() == "0") {
	                    $(this).prop("checked", false);
	                }
	            }
				
			}else{
				$(".showLocalRlt").hide();
			}
            
            
            if (obj2.L120S01aForm.grade1 != undefined) {
                $L120S01aForm.find(".hideMow1").show();
            }
            if (obj2.L120S01aForm.grade2 != undefined) {
                $L120S01aForm.find(".hideMow2").show();
            }
            if (obj2.L120S01aForm.grade3 != undefined) {
                $L120S01aForm.find(".hideMow3").show();
            }
            if (obj2.L120S01aForm.grade4 != undefined) {
                $L120S01aForm.find(".hideMow4").show();
            }
            if (obj2.L120S01aForm.grade5 != undefined) {
                $L120S01aForm.find(".hideMow5").show();
            }
            if (obj2.L120S01aForm.grade6 != undefined) {
                $L120S01aForm.find(".hideMow6").show();
            }	
            
            //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
            if (obj2.L120S01aForm.grade7 != undefined) {
                $L120S01aForm.find(".hideMow7").show();
            }
            
            //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
            if (obj2.L120S01aForm.grade8 != undefined) {
                $L120S01aForm.find(".hideMow8").show();
            }
            if (obj2.L120S01aForm.grade9 != undefined) {
                $L120S01aForm.find(".hideMow9").show();
            }
            
            
            if(obj2.L120S01aForm.stockStatus == "6"){
            	$("#stockStatus").nextAll().hide();
        		$("#spanStock,#stockDate").val('');
            }else{
            	$("#stockStatus").nextAll().show();
            	$("#spanStock").html($("#stockStatus option[" + "value='" + DOMPurify.sanitize(obj2.L120S01aForm.stockStatus) +"']").html());
            }
			
            if (obj2.L120S01aForm.invMFlag != "1") {
                $(".none").hide();
				$("[name='invMFlag']:radio:eq(1)").prop("checked",true);
                obj2.L120S01aForm.aprAmt = "";
            }
            else {
                $(".none").show();
            }
            /** 7625
             if ($("input[name=invMFlag]:checked").val() != "1") {
             $(".none").hide();
             $(".none").find("#aprAmt").val("");
             }
             else {
             $(".none").show();
             }
             */
            
            $L120S01aForm.setData(obj2.L120S01aForm, false);

			gridCreditRiskReLoad();	
			
            //ie7 不知道為何會disabled，故要重設
            $("#L120S01gForm_1").find("#rcdFlag").prop("disabled", false);
            
            if (obj2.L120S01aForm.keyMan == "Y") {
                // 為主要借款人
                $L120S01aForm.find("#keyMan").prop("checked", true);
                $L120S01aForm.find("#keyMan").val("Y");
                $L120S01aForm.find("#custRltShow").val("");
                $L120S01aForm.find("#custRlt").html("");
                $L120S01aForm.find("#custPos option:eq(0)").prop("selected", true);
                $("#chk_radio1").hide();
            }
            else {
                // 為非主要借款人
                $L120S01aForm.find("#keyMan").prop("checked", false);
                $L120S01aForm.find("#keyMan").val("N");
                $("#custRltShow").val("");
                if (obj2.L120S01aForm.custRlt != "" && obj2.L120S01aForm.custPos != "") {
                    if (obj2.L120S01aForm.isOther) {
                        // 其他綜合關係
                        $("#custRlt").text(obj2.L120S01aForm.custRlt);
                        $("#custRlt_content3 option").each(function(i){
							var $this = $(this);
                            if ($this.val() == obj2.L120S01aForm.custRlt1) {
                                $("#custRltShow").val(obj2.L120S01aForm.custRlt1 + obj2.L120S01aForm.custRlt2 + " " + $this.text() + "-");
                            }
                        });
                        $("#custRlt_content4 option").each(function(i){
							var $this = $(this);
                            if ($this.val() == obj2.L120S01aForm.custRlt2) {
                                $("#custRltShow").val($("#custRltShow").val() + " " + $this.text());
                            }
                        });
                    }
                    else {
                        $("[name='custRlt_content'] option").each(function(i){
							var $this = $(this);
                            if ($this.val() == obj2.L120S01aForm.custRlt) {
                                $("#custRlt").text(obj2.L120S01aForm.custRlt);
                                $("#custRltShow").val(obj2.L120S01aForm.custRlt + " " + $this.text());
                            }
                        });
                    }
                    $("#chk_radio1").show();
                }
                else {
                    $("#custRlt").html("");
                    $("#custPos option:eq(0)").prop("selected", true);
                    $("#chk_radio1").show();
                }
            }
            
            if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                if (responseJSON.docType == "1" && responseJSON.docKind == "1") {
                    $(".sBorrowData").show();
                    $("#noInv").hide();
                    $("#noInv2").hide();
                } else if (responseJSON.docType == "1" && responseJSON.docKind == "2") {
//                    $(".sBorrowData").hide();
                    $("#noInv").show();
                    $("#noInv2").show();
                    $("#hInvMDscr").show();
                }
                else {
                    $(".sBorrowData").hide();
                    $("#noInv").show();
                    $("#noInv2").hide();
                }
            }
            else {
                $(".invShow").find("#invMAmt").val("");
                $(".sBorrowData").hide();
               // $("#invMDscr").val("");
            }
            if ($("input[type='radio'][name='fctMhRlt']:checked").val() == "1" || $("input[type='radio'][name='fctMbRlt']:checked").val() == "1") {
                $(".dscr4").show();
            }
            else {
                $(".dscr4").hide();
                $(".dscr4").find("#fctMhRltDscr").val("");
            }

            // J-109-0370 相關評估改版
            // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
            $("#hUnfCon").hide();
            if (responseJSON.docType == "1" && responseJSON.docKind == "2") {
                $("#hUnfCon").show();
                if (obj2.L120S01aForm.unfConFlag == "Y") {
                    $("#L120S01aForm").find(".unfConMemoShow").show();
                } else {
                    $("#L120S01aForm").find(".unfConMemoShow").hide();
                    $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                }
            }
            
            
            
            
            
            
            // 第二頁籤
			var $L120S01gForm_1 = $("#L120S01gForm_1");
			var $L120S01gForm_2 = $("#L120S01gForm_2");
            $("#formIdDscr1").setData(obj2.formIdDscr1, false);
            $L120S01gForm_1.setData(obj2.L120S01gForm_1, false);
			
			// 設定使用者選擇項目
			for(o in obj2.checkList){
				$L120S01gForm_1.find("#" + obj2.checkList[o]).prop("checked",true);
			}
			
            // 控制"引進徵信相關資料隱藏顯示功能"										
            if (obj2.L120S01gForm_1.rcdFlag == "Y") {
                $L120S01gForm_1.find("#rcdFlag").prop("checked", true);
				$L120S01gForm_1.find(".hideThis").show();				
				$("#formIdDscr1").find(".hideThis").show();
				$L120S01gForm_2.find(".hideThis").show();
				$("#formIdDscr2").find(".hideThis").show();
				$("#L120S01fForm").find(".hideThis").show();		
            }
            else {
                $L120S01gForm_1.find("#rcdFlag").prop("checked", false);
				$L120S01gForm_1.find(".hideThis").hide();
				$("#formIdDscr1").find(".hideThis").hide();
				$L120S01gForm_2.find(".hideThis").hide();
				$("#formIdDscr2").find(".hideThis").hide();
				$("#L120S01fForm").find(".hideThis").hide();
            }
            if (obj2.L120S01gForm_1.runFlag == "Y") {
                $L120S01gForm_1.find("#runFlag").prop("checked", true);
                $L120S01gForm_1.find("#tabs-2_show").show();
            }
            else {
                $L120S01gForm_1.find("#runFlag").prop("checked", false);
                $L120S01gForm_1.find("#tabs-2_show").hide();
            }

            // 第三頁籤
            //$("#formIdDscr2").setData(obj2.formIdDscr2, false);
            var formIdDscr2 = $("#formIdDscr2");
            formIdDscr2.setData(obj2.formIdDscr2, false);
            $L120S01gForm_2.setData(obj2.L120S01gForm_2, false);		
            if(obj2.L120S01gForm_1 && obj2.L120S01gForm_2){
            	buildL120s01eKind2(obj2);
            }
            
            if (obj2.L120S01gForm_2.finFlag == "Y") {
                $L120S01gForm_2.find("#finFlag").prop("checked", true);
                $L120S01gForm_2.find("#tabs-3_show").show();
            }
            else {
                $L120S01gForm_2.find("#finFlag").prop("checked", false);
                $L120S01gForm_2.find("#tabs-3_show").hide();
            }
            if (obj2.L120S01gForm_2.rcdFlag == "Y") {
                $L120S01gForm_2.find("#rcdFlag").prop("checked", true);
            }
            else {
                $L120S01gForm_2.find("#rcdFlag").prop("checked", false);
            }
            
            // 第四頁籤
            $L120S01fForm.setData(obj2.L120S01fForm, false);
            if (obj2.L120S01fForm.rcdFlag == "Y") {
                $L120S01fForm.find("#rcdFlag").prop("checked", true);
            }
            else {
                $L120S01fForm.find("#rcdFlag").prop("checked", false);
            }
            // 檢查財務比率有無資料，有資料則顯示並自動勾選其項目
            checkExist();
            
            var $showBorrowData = $("#showBorrowData");
            $showBorrowData.find("#custId").val(obj2.custId);
            $showBorrowData.find("#dupNo").val(obj2.dupNo);
            $L120S01aForm.find(".tab-c-item").show();
            openDocAddBorrow(obj2.oid);
			$L120S01aForm.find("#btnPrintCreditRisk").show();
			setTimeout(function(){
				var invMDscr = $("#invMDscr");
				invMDscr.val(obj2.L120S01aForm.invMDscr);
			},50);
    });
}

/**
 * 讀取已有的資料以進行修改
 *
 * @param cellvalue
 * @param options
 * @param rowObject
 */
function openDoc(cellvalue, options, rowObject){
    if ($("#lmss02_panel").attr("openFlag") == "true") {
        $("#lmss02_panel").load("../../lms/lmss02", function(){
            util.init($("#L120S01aForm"));
            util.init($("#L120S01fForm"));
            if (responseJSON.docCode == "2" ||
            responseJSON.docCode == "3") {
                $(".hBorrowData").hide();
                $(".sBorrowData").hide();
            }
//            if (responseJSON.docType == "1" &&
//            responseJSON.docKind == "1") {
            if (responseJSON.docType == "1") {
                $("#hUnfCon").hide();
                if (responseJSON.docKind == "1") {
                    $(".sBorrowData").show();
                    $("#noInv").hide();
                    $("#noInv2").hide();
                } else if (responseJSON.docKind == "2") {
//                    $(".sBorrowData").show();
                    $("#noInv").show();
                    $("#noInv2").show();
                    if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                        $("#hInvMDscr").show();
                    } else {
                        $("#hInvMDscr").hide();
                    }

                    // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
                    $("#hUnfCon").show();
                    if ($("input[type='radio'][name='unfConFlag']:checked").val() == "Y") {
                        $("#L120S01aForm").find(".unfConMemoShow").show();
                    } else {
                        $("#L120S01aForm").find(".unfConMemoShow").hide();
                        $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                    }
                }
            }
            else {
                $(".sBorrowData").hide();
                $("#noInv").show();
                $("#noInv2").hide();
                $("#hUnfCon").hide();
            }
            
            
            
            
            
            
            var $L120S01aForm = $("#L120S01aForm");
            var $L120S01fForm = $("#L120S01fForm");
            // 利用CodeType取得國別及幣別
			// 新增集團列管註記  J-104-0240-001  Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
            var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy", "lms1205s01_RelClass", "Relation_type31", 
			"Relation_type32", "Relation_type1", "Relation_type2", "lms1205s01_CustPos", "lms1205s01_posType", "lms1205s01_Unit",
			"lms1205s01_stockStatus","lms1201s02_custClass","l1205s01_groupBadFlag"]);
            // 幣別
            $L120S01aForm.find("#rgtCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#cptlCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#invMCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            // 國別
            $L120S01aForm.find("#ntCode").setItems({
                item: obj.CountryCode,
                format: "{value} - {key}",
                space: true
            });
            // 關係類別
            $L120S01aForm.find("#custRlt_main").setItems({
                item: obj.lms1205s01_RelClass,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係1
            $L120S01aForm.find("#custRlt_content1").setItems({
                item: obj.Relation_type1,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係2
            $L120S01aForm.find("#custRlt_content2").setItems({
                item: obj.Relation_type2,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係3
            $L120S01aForm.find("#custRlt_content3").setItems({
                item: obj.Relation_type31,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係4
            $L120S01aForm.find("#custRlt_content4").setItems({
                item: obj.Relation_type32,
                format: "{key}",
                space: false
            });
            // 相關身份
            $L120S01aForm.find("#custPos").setItems({
                item: obj.lms1205s01_CustPos,
                format: "{key}",
                space: false
            });
            // 負責人欄類型
            $L120S01aForm.find("#posType").setItems({
                item: obj.lms1205s01_posType,
                format: "{key}",
                space: false
            });
            // 金額單位
            $L120S01aForm.find("#rgtUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01aForm.find("#cptlUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            // 股票上市上櫃情形
            $L120S01aForm.find("#stockStatus").setItems({
                item: obj.lms1205s01_stockStatus,
                format: "{key}",
                space: false
            });
            
			// 客戶類別
            $L120S01aForm.find("#custClass").setItems({
                item: obj.lms1201s02_custClass,
                format: "{value} - {key}",
                space: true
            });
			
			//J-104-0240-001  Web e-Loan授信簽報書與額度明細表增加列示借款戶所屬集團企業代號、名稱與註記。
			// 集團列管註記
            $L120S01aForm.find("#groupBadFlag").setItems({
                item: obj.l1205s01_groupBadFlag,
                format: "{value} - {key}",
                space: true
            });
            
            // 幣別
            $L120S01fForm.find("#dpAvgCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#fxCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#fx2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#imCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#im2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#exCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#ex2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#cntrCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#nonLoanCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#payCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            // 金額單位
            $L120S01fForm.find("#dpAvgUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#fxUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#fx2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#imUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#im2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#exUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#ex2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#cntrUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#nonLoanUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
            $L120S01aForm.find("#netSwft").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
            $L120S01aForm.find("#netAmtUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
			
			          
            queryBorrow(rowObject);
            $("#lmss02_panel").attr("openFlag", "false");
            // 控制分頁頁籤內容唯讀(不包括下拉式選單)
			var $formIdDscr1 = $("#formIdDscr1");
			var $formIdDscr2 = $("#formIdDscr2");
			var $L120S01gForm_1 = $("#L120S01gForm_1");
			var $L120S01gForm_2 = $("#L120S01gForm_2");
            if (responseJSON.readOnly == "true") {
                $L120S01aForm.readOnlyChilds(true);
                $L120S01aForm.find("button").hide();
                $formIdDscr1.readOnlyChilds(true);
                $formIdDscr1.find("button").hide();
                $L120S01gForm_1.readOnlyChilds(true);
                $L120S01gForm_1.find("button").hide();
                $formIdDscr2.readOnlyChilds(true);
                $formIdDscr2.find("button").hide();
                $L120S01gForm_2.readOnlyChilds(true);
                $L120S01gForm_2.find("button").hide();
                $L120S01fForm.readOnlyChilds(true);
                $L120S01fForm.find("button").hide();
            }
        });
    }
    else {
        if (responseJSON.docCode == "2" ||
        responseJSON.docCode == "3") {
            $(".hBorrowData").hide();
        }
        if (responseJSON.docType == "1" &&
        responseJSON.docKind == "1") {
            $(".hBorrowData2").hide();
        }
        queryBorrow(rowObject);
    }
};

/**
 * 新增借款人所開啟的ThickBox內容
 *
 * @param value
 * @param titleName
 * @param tWidth
 * @param tHeight
 */
function openList(value, titleName, tWidth, tHeight){
	titleName = i18n.lmss02["l120s01a.btn1"];
    $("#addborrow").reset();
    $.ajax({
        // 根據外層授信簽報書企金/個金案件自動替User勾選並改成唯讀狀態
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "checkDocType",
            mainId: responseJSON.mainId,
            oid: responseJSON.oid
        }
	}).done(function(json){
            $("#addborrow").find("[name='docType']").each(function(i){
                if ($(this).val() == json.docType) {
                    $(this).prop("checked", true);
                }
                $(this).prop("disabled", true);
            });
    });
    
    $("#" + value).thickbox({
        // 使用選取的內容進行彈窗
        title: titleName,
        width: 800,
        height:380,
        modal: true,
/*
        valign: "bottom",
        align: "center",
*/
        i18n: i18n.def,
        buttons: {
            "close": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function addBorrowMain(showMsg, check){
	var $addborrow = $("#addborrow");
	var buscd = $addborrow.find("#buscd").val();
	var renCd = "";
	if(buscd != "130300" && buscd != "060000" 
	&& buscd != "130300" && buscd != "60000"
	){
		// 記錄法人或自然人
		renCd = "C";
	}else{
		// 自然人
		renCd = "N";		
	}	
    $.ajax({
        type: "POST",
        handler: _handler,
        data: {
            formAction: "addBorrowMain",
            mainId: responseJSON.mainId,
            docType: $addborrow.find("input[name='docType']:checked").val(),
            typCd: $addborrow.find("input[name='typCd']:checked").val(),
            custId: $addborrow.find("#custId").val(),
            dupNo: $addborrow.find("#dupNo").val(),
            custName: $addborrow.find("#custName").val(),
			buscd : buscd,
			renCd : renCd,
            idDscr1: "",
            idDscr2: "",
            check: check,
            showMsg: showMsg
        }
	}).done(function(responseData){
            var $L120S01aForm = $("#L120S01aForm");
            var $L120S01fForm = $("#L120S01fForm");
//            if (responseJSON.docType == "1" &&
//            responseJSON.docKind == "1") {
            if (responseJSON.docType == "1") {
                $("#hUnfCon").hide();
                if (responseJSON.docKind == "1") {
                    $(".sBorrowData").show();
                    $("#noInv").hide();
                    $("#noInv2").hide();
                } else if (responseJSON.docKind == "2") {
//                     $(".sBorrowData").hide();
                     $("#noInv").show();
                     $("#noInv2").show();
                     if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                        $("#hInvMDscr").show();
                     } else {
                        $("#hInvMDscr").hide();
                     }

                     // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
                     $("#hUnfCon").show();
                     if ($("input[type='radio'][name='unfConFlag']:checked").val() == "Y") {
                         $("#L120S01aForm").find(".unfConMemoShow").show();
                     } else {
                         $("#L120S01aForm").find(".unfConMemoShow").hide();
                         $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                     }
                 }
            }
            else {
                $(".sBorrowData").hide();
                $("#noInv").show();
                $("#noInv2").hide();
                $("#hUnfCon").hide();
            }
            var obj = CommonAPI.loadCombos(["CountryCode", "Common_Currcy", "lms1205s01_RelClass", "Relation_type31", "Relation_type32", "Relation_type1", "Relation_type2", "lms1205s01_CustPos", "lms1205s01_posType", "lms1205s01_Unit", "lms1205s01_stockStatus","lms1201s02_custClass"]);
            // 幣別
            $L120S01aForm.find("#rgtCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#cptlCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#invMCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            // 國別
            $L120S01aForm.find("#ntCode").setItems({
                item: obj.CountryCode,
                format: "{value} - {key}",
                space: true
            });
            // 關係類別
            $L120S01aForm.find("#custRlt_main").setItems({
                item: obj.lms1205s01_RelClass,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係1
            $L120S01aForm.find("#custRlt_content1").setItems({
                item: obj.Relation_type1,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係2
            $L120S01aForm.find("#custRlt_content2").setItems({
                item: obj.Relation_type2,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係3
            $L120S01aForm.find("#custRlt_content3").setItems({
                item: obj.Relation_type31,
                format: "{key}",
                space: false
            });
            // 與主要借款人關係4
            $L120S01aForm.find("#custRlt_content4").setItems({
                item: obj.Relation_type32,
                format: "{key}",
                space: false
            });
            // 相關身份
            $L120S01aForm.find("#custPos").setItems({
                item: obj.lms1205s01_CustPos,
                format: "{key}",
                space: false
            });
            // 負責人欄類型
            $L120S01aForm.find("#posType").setItems({
                item: obj.lms1205s01_posType,
                format: "{key}",
                space: false
            });
            // 金額單位
            $L120S01aForm.find("#rgtUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01aForm.find("#cptlUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            // 股票上市上櫃情形
            $L120S01aForm.find("#stockStatus").setItems({
                item: obj.lms1205s01_stockStatus,
                format: "{key}",
                space: false
            });
            
			// 客戶類別
            $L120S01aForm.find("#custClass").setItems({
                item: obj.lms1201s02_custClass,
                format: "{value} - {key}",
                space: true
            });
			
            // 幣別
            $L120S01fForm.find("#dpAvgCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#fxCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#fx2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#imCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#im2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#exCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#ex2Curr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#cntrCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01fForm.find("#nonLoanCurr").setItems({
                clear: false,
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#payCurr").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            // 金額單位
            $L120S01fForm.find("#dpAvgUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#fxUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#fx2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#imUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#im2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#exUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#ex2Unit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#cntrUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            $L120S01fForm.find("#nonLoanUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            //J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
            $L120S01aForm.find("#netSwft").setItems({
                item: obj.Common_Currcy,
                format: "{value} - {key}",
                space: true
            });
            $L120S01aForm.find("#netAmtUnit").setItems({
                item: obj.lms1205s01_Unit,
                format: "{key}",
                space: false
            });
            
            // 初始化前端form
            $("#showBorrowData").reset();
            // 第一頁籤
            $L120S01aForm.reset();
            $L120S01aForm.find("#posType option:eq(2)").prop("selected", true);
            // 第二頁籤
            $("#formIdDscr1").reset();
            $("#L120S01gForm_1").reset();
            // TODO CKeditor值無法初始化(設成空白會殘留值)
            // 第三頁籤
            $("#formIdDscr2").reset();
            $("#L120S01gForm_2").reset();
            // TODO CKeditor值無法初始化(設成空白會殘留值)
            // 第四頁籤
            $L120S01fForm.reset();
            // 開始設定資料到前端...
            $("#showBorrowData").setData(responseData.showBorrowData, false);
            // 第一頁籤
            $L120S01aForm.setData(responseData.L120S01aForm, false);
            $L120S01aForm.find("#typCd").text(responseData.L120S01aForm.typCd);	
            // 第二頁籤
            $("#formIdDscr1").setData(responseData.formIdDscr1, false);
            // 第三頁籤
            $("#formIdDscr2").setData(responseData.formIdDscr2, false);
            // $("form").setData(responseData);
            $("#custRlt").html("");
            $("#custPos option:eq(0)").prop("selected", true);
            $("#chk_radio1").show();
            $L120S01aForm.find("#custRltShow").val("");
            
            if (check) {
                $L120S01aForm.find("#keyMan:checkbox").prop("checked", true);
                $L120S01aForm.find("#keyMan").val("Y");
                $("#custRltShow").val("");
                $("#custRlt").html("");
                $("#custPos option:eq(0)").prop("selected", true);
                $("#chk_radio1").hide();
                CommonAPI.triggerOpener("gridview", "reloadGrid");
            }
            else {
                $L120S01aForm.find("#keyMan:checkbox").prop("checked", false);
                $L120S01aForm.find("#keyMan").val("N"); // 初始化主要借款人
                if ($("#custRlt").html() != "" &&
                $("#custPos option:selected").val() != "") {
                    $("[name='custRlt_content'] option").each(function(i){
                        if ($(this).val() == $("#custRlt").html()) {
                            $("#custRltShow").val($("#custRlt").html() + " " + $(this).text());
                        }
                    });
                    $("#chk_radio1").show();
                }
                else {
                    $("#custRlt").html("");
                    $("#custPos option:eq(0)").prop("selected", true);
                    $("#chk_radio1").show();
                }
            }
            $("#l120s01agrid").trigger("reloadGrid"); // 更新Grid內容
            $("#spanStock").html($("#stockStatus option:eq(0)").html());
            $(".none").show();
            $L120S01aForm.find(".hideMow1").hide();
            $L120S01aForm.find(".hideMow2").hide();
            $L120S01aForm.find(".hideMow3").hide();
            $L120S01aForm.find(".hideMow4").hide();
            $L120S01aForm.find(".hideMow5").hide();
            $L120S01aForm.find(".hideMow6").hide();
            //J-113-0141_12473_B1001 海外 外部信評之評等機構增列中華信評
            $L120S01aForm.find(".hideMow7").hide();
            //J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
            $L120S01aForm.find(".hideMow8").hide();
            $L120S01aForm.find(".hideMow9").hide();
			
			showHide($("#L120S01gForm_1").find("#rcdFlag"),'.hideThis');
			checkUncheck($("#L120S01gForm_1").find("#rcdFlag"),'.cesCheck');
            
            if (responseJSON.docType == "1" &&
            responseJSON.docKind == "1") {
                $(".hBorrowData2").hide();
            }
			$L120S01aForm.find("#rgtCurr").val("");
			$L120S01aForm.find("#rgtAmt").val("");
			$L120S01aForm.find("#rgtUnit").val("1");
			$L120S01aForm.find("#cptlCurr").val("");
			$L120S01aForm.find("#cptlAmt").val("");
			$L120S01aForm.find("#cptlUnit").val("1");
			$L120S01aForm.find("#cmpAddr").val("");
			$L120S01aForm.find("#factoryAddr").val("");
			
			//J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
			$L120S01aForm.find("#netSwft").val("");
			$L120S01aForm.find("#netAmt").val("");
			$L120S01aForm.find("#netAmtUnit").val("1");
			
            openDocAddBorrow(responseData.oid); // 開啟ThickBox
			setTimeout(function(){
				$("#invMDscr").val("");
				$("#idDscr1").val("");
				$("#idDscr2").val("");
				// 自動點選重新引進按鈕
				getCustData2();
			},50);            
    });
}

function addNewBor(showMsg, check){
	var $hBorrowData = $(".hBorrowData");
	var $sBorrowData = $(".sBorrowData");	
    if ($("#lmss02_panel").attr("openFlag") == "true") {
        $("#lmss02_panel").load("../../lms/lmss02", function(){
            if (responseJSON.docCode == "2" ||
            responseJSON.docCode == "3") {
                $hBorrowData.hide();
                $hBorrowData.hide();
            }
//            if (responseJSON.docType == "1" &&
//            responseJSON.docKind == "1") {
            if (responseJSON.docType == "1") {
                $("#hUnfCon").hide();
                if (responseJSON.docKind == "1") {
                    $(".sBorrowData").show();
                    $("#noInv").hide();
                    $("#noInv2").hide();
                } else if (responseJSON.docKind == "2") {
//                     $(".sBorrowData").hide();
                     $("#noInv").show();
                     $("#noInv2").show();
                     if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                        $("#hInvMDscr").show();
                     } else {
                        $("#hInvMDscr").hide();
                     }

                     // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
                     $("#hUnfCon").show();
                     if ($("input[type='radio'][name='unfConFlag']:checked").val() == "Y") {
                         $("#L120S01aForm").find(".unfConMemoShow").show();
                     } else {
                         $("#L120S01aForm").find(".unfConMemoShow").hide();
                         $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                     }
                 }
            }
            else {
                $sBorrowData.hide();
                $("#noInv").show();
                $("#noInv2").hide();
                $("#hUnfCon").hide();
            }
            addBorrowMain(showMsg, check);
        });
        $("#lmss02_panel").attr("openFlag", "false");
    }
    else {
        if (responseJSON.docCode == "2" ||
        responseJSON.docCode == "3") {
            $hBorrowData.hide();
            $hBorrowData.hide();
        }
//        if (responseJSON.docType == "1" &&
//        responseJSON.docKind == "1") {
        if (responseJSON.docType == "1") {
            $("#hUnfCon").hide();
            if (responseJSON.docKind == "1") {
                $(".sBorrowData").show();
                $("#noInv").hide();
                $("#noInv2").hide();
            } else if (responseJSON.docKind == "2") {
//                 $(".sBorrowData").hide();
                 $("#noInv").show();
                 $("#noInv2").show();
                 if ($("input[type='radio'][name='invMFlag']:checked").val() == "1") {
                    $("#hInvMDscr").show();
                 } else {
                    $("#hInvMDscr").hide();
                 }

                 // 授信戶/集團是否有「已核准未完成簽約」  企金一般授權外才顯示
                  $("#hUnfCon").show();
                  if ($("input[type='radio'][name='unfConFlag']:checked").val() == "Y") {
                      $("#L120S01aForm").find(".unfConMemoShow").show();
                  } else {
                      $("#L120S01aForm").find(".unfConMemoShow").hide();
                      $("#L120S01aForm").find(".unfConMemoShow").find("#unfConMemo").val("");
                  }
             }
        }
        else {
            $sBorrowData.hide();
            $("#noInv").show();
            $("#noInv2").hide();
            $("#hUnfCon").hide();
        }
        addBorrowMain(showMsg, check)
    }
}

function getReject(){
	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getReject",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").html(),
            dupNo: $("#L120S01aForm").find("#dupNo").html()
        }
	}).done(function(json){
			$("#L120S01aForm").setData(json, false);
			var rejtCaseAdjMemo = $("#L120S01aForm").find("#rejtCaseAdjMemo").val();
			if(rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == ""){
				$("#L120S01aForm .hideMemo").hide();
			}else{
				$("#L120S01aForm .hideMemo").show();
			}
    });
}

function getReject1(){
	var val_chairmanId =  $("#L120S01aForm").find("#chairmanId").val();
    var val_chairmanDupNo  = $("#L120S01aForm").find("#chairmanDupNo").val();
	
	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getReject1",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").val(),
            dupNo: $("#L120S01aForm").find("#dupNo").val(),
            chairmanId: val_chairmanId,
            chairmanDupNo: val_chairmanDupNo,
            chairmanVisible : $("#L120S01aForm").find("#chairmanId").is(':visible')
        }
	}).done(function(json){
        	if(json.isPassed){        		
        		$("#L120S01aForm").setData(json, false);
        		if(json.L120S01aForm.IsRejt1==""){
        			$("#L120S01aForm").find("[name=IsRejt1]").prop("checked", false);        			
        		}
    			var rejtCaseAdjMemo1 = $("#L120S01aForm").find("#rejtCaseAdjMemo1").val();
    			if(rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == ""){
    				$("#L120S01aForm .hideMemo1").hide();
    			}else{
    				$("#L120S01aForm .hideMemo1").show();
    			}    			
        	}
			
    });	
}

function editReject(){
	var rejtVal = $("[name='rejtCase']:radio:checked").val();
	var rejtCaseBefore = $("[name='rejtCaseBefore']:radio:checked").val();
	// 婉卻控管Radio設定
	$("[name='_rejtCase']").each(function(i){
		var $this = $(this);
		if(rejtVal == $this.val()){
			$this.prop("checked",true);
		}
	});
	// 婉卻控管變更前Radio設定
	$("[name='_rejtCaseBefore']").each(function(i){
		var $this = $(this);
		if(rejtCaseBefore == $this.val()){
			$this.prop("checked",true);
		}
	});
    var editReject = $("#editReject").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmscommom["other.msg127"],	//other.msg127=修改婉卻控管種類
        width: 500,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "editReject",
                        _rejtCase: $("[name='_rejtCase']:radio:checked").val(),
                        _rejtCaseBefore: rejtVal,
			            custId: $("#L120S01aForm").find("#custId").html(),
			            dupNo: $("#L120S01aForm").find("#dupNo").html()                        
                    }
				}).done(function(responseData){
                        $("#L120S01aForm").setData(responseData.L120S01aForm,false);
						$(".showBefore").show();
						var rejtCaseAdjMemo = $("#L120S01aForm").find("#rejtCaseAdjMemo").val();
						if(rejtCaseAdjMemo == undefined || rejtCaseAdjMemo == null || rejtCaseAdjMemo == ""){
							$("#L120S01aForm").find(".hideMemo").hide();
						}else{
							$("#L120S01aForm").find(".hideMemo").show();
						}
                        $.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function editReject1(){
	var rejtVal1 = $("[name='rejtCase1']:radio:checked").val();
	var rejtCaseBefore1 = $("[name='rejtCaseBefore1']:radio:checked").val();
	// 婉卻控管Radio設定
	$("[name='_rejtCase1']").each(function(i){
		var $this = $(this);
		if(rejtVal1 == $this.val()){
			$this.prop("checked",true);
		}
	});
	// 婉卻控管變更前Radio設定
	$("[name='_rejtCaseBefore1']").each(function(i){
		var $this = $(this);
		if(rejtCaseBefore1 == $this.val()){
			$this.prop("checked",true);
		}
	});
    var editReject1 = $("#editReject1").thickbox({
        // 使用選取的內容進行彈窗
        title: i18n.lmscommom["other.msg127"],	//other.msg127=修改婉卻控管種類
        width: 500,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                $.ajax({
                    type: "POST",
                    handler: _handler,
                    data: {
                        formAction: "editReject1",
                        _rejtCase1: $("[name='_rejtCase1']:radio:checked").val(),
                        _rejtCaseBefore1: rejtVal1,
			            custId: $("#L120S01aForm").find("#custId").html(),
			            dupNo: $("#L120S01aForm").find("#dupNo").html()                        
                    }
				}).done(function(responseData){
                        $("#L120S01aForm").setData(responseData.L120S01aForm,false);
						$(".showBefore1").show();
						var rejtCaseAdjMemo1 = $("#L120S01aForm").find("#rejtCaseAdjMemo1").val();
						if(rejtCaseAdjMemo1 == undefined || rejtCaseAdjMemo1 == null || rejtCaseAdjMemo1 == ""){
							$("#L120S01aForm").find(".hideMemo1").hide();
						}else{
							$("#L120S01aForm").find(".hideMemo1").show();
						}
                        $.thickbox.close();
						$.thickbox.close();
						CommonAPI.showMessage(responseData.NOTIFY_MESSAGE);
                });
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}

function getBlack(inputEname){
    var $L120S01aForm = $("#L120S01aForm");
    // 初始化黑名單
    $L120S01aForm.find("#blackName").html("");
    $L120S01aForm.find("#blackDate").html("");
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getBlack",
            mainId: responseJSON.mainId,
            custId: $L120S01aForm.find("#custId").html(),
            dupNo: $L120S01aForm.find("#dupNo").html(),
            eName: $("#formBlack").find("#eName").val(),
            inputEname: inputEname
        }
	}).done(function(json){
            if (json.needInput) {
                $("#formBlack").reset();
                $("#formBlack").find("#eName").val(json.eName);
                tBlack();
            }
            else {
                $L120S01aForm.setData(json, false);
            }
            if (responseJSON.custErrorMsg != undefined && responseJSON.custErrorMsg != null) {
                return CommonAPI.showErrorMessage(responseJSON.custErrorMsg);
            }
    });
}

/**
 * 開啟手動輸入英文名稱查詢黑名單
 * (當自動查詢英文名稱查不到黑名單時開啟此畫面)
 */
function tBlack(){
    var tBlack = $("#tBlack").thickbox({
        title: i18n.lmscommom["other.msg126"], //other.msg126=黑名單查詢
        width: 640,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: true,
        i18n: i18n.def,
        buttons: {
            "sure": function(){
                if ($("#formBlack").valid()) {
                    $.thickbox.close();
                    getBlack(true);
                }
            },
            "cancel": function(){
                API.confirmMessage(i18n.def['flow.exit'], function(res){
                    if (res) {
                        $.thickbox.close();
                    }
                });
            }
        }
    });
}


/**
 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
 *查詢 申貸戶 異常通報紀錄 
 */
function getAbnormal(){
    $.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getAbnormal",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").html(),
            dupNo: $("#L120S01aForm").find("#dupNo").html()
        }
	}).done(function(json){
			$("#L120S01aForm").setData(json, false);
    });	
}


/**
 *查詢 負責人 婉卻紀錄 
 */
function getAbnormal1(){
	var val_chairmanId =  $("#L120S01aForm").find("#chairmanId").val();
    var val_chairmanDupNo  = $("#L120S01aForm").find("#chairmanDupNo").val();
	
	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "getAbnormal1",
            mainId: responseJSON.mainId,
            custId: $("#L120S01aForm").find("#custId").val(),
            dupNo: $("#L120S01aForm").find("#dupNo").val(),
            chairmanId: val_chairmanId,
            chairmanDupNo: val_chairmanDupNo,
            chairmanVisible : $("#L120S01aForm").find("#chairmanId").is(':visible')
        }
	}).done(function(json){
        	if(json.isPassed){        		
        		$("#L120S01aForm").setData(json, false);
        		if(json.L120S01aForm.isAbnormal1==""){
        			$("#L120S01aForm").find("[name=isAbnormal1]").prop("checked", false);        			
        		}
    			
        	}
			
    });	
	    	
}

/**
 * 查詢使用者點擊的資料
 *
 * @param abnormalKind
 * 空白.申貸戶
 * 2.負責人
 */
function openDocAbnormal(abnormalKind){
     
	var mainId =  $("#L120S01aForm").find("#abnormalMainId"+abnormalKind).val();
	
	if(!mainId){
		//return CommonAPI.showMessage(i18n.def["noData"]);
		
		return CommonAPI.showMessage(i18n.lmscommom["other.msg209"]);
	}
	
	if(abnormalKind =="1"){
		//負責人
		openCustId = $("#L120S01aForm").find("#custId").val();
		openDupNo = $("#L120S01aForm").find("#dupNo").val();
	}else{
		//申貸戶
		openCustId = $("#L120S01aForm").find("#abnormalChairmanId").val();
		openDupNo = $("#L120S01aForm").find("#abnormalChairmanDupNo").val();
		if(!openCustId){
			return CommonAPI.showMessage(i18n.def["noData"]);
		}
	}
	 
	$.ajax({
        handler: _handler,
        type: "POST",
        dataType: "json",
        data: {
            formAction: "openDocAbnormal",
            abnormalMainId: mainId,
            openCustId:openCustId,
            openDupNo:openDupNo
        }
	}).done(function(obj){	
			var typCd = "";
			if(obj.typCd =="5"){
				typCd="5";
			}else{
				typCd="1";
			}
			var pdfName = "LMS120"+typCd+"R27.pdf";
		    var rType = "R27"
		    
		    var content = ""; 
		    content = rType + "^" + obj.oid + "^" + openCustId + "^" + openDupNo + "^" ;
		    
		    $.form.submit({
		        url: "../../../app/simple/FileProcessingService",
		        target: "_blank",
		        data: {
		            mainId: mainId,
		            rptOid: content,
		            fileDownloadName: pdfName,
		            serviceName: "lms120"+typCd+"r01rptservice"
		        }
		    });
			
    });	

}

















