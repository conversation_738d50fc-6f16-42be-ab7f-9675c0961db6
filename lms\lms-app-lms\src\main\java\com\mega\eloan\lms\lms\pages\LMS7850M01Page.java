/* 
 *LMS7110M01Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.lms.pages;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.html.AclLabel;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.pages.AbstractEloanForm;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護明細
 * </pre>
 * 
 * @since 2013/1/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/22,Miller,new
 *          </ul>
 */
@Controller
@RequestMapping("/lms/lms7850m01/{page}")
public class LMS7850M01Page extends AbstractEloanForm {

	@Autowired
	LMSService lmsService;

	@Override
	public void execute(ModelMap model, PageParameters params) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		if (Util.equals(user.getUnitNo(), UtilConstants.BankNo.授管處)) {
			addAclLabel(model, new AclLabel("_btnDOC_EDITING_918", params, getDomainClass(), AuthType.Modify,
					CreditDocStatusEnum.授管處_停權編製中));
			addAclLabel(model, new AclLabel("_btnDOC_EDITING", AuthType.Modify, params, false));
		} else {
			addAclLabel(model, new AclLabel("_btnDOC_EDITING", params, getDomainClass(), AuthType.Modify,
					CreditDocStatusEnum.授管處_停權編製中));
			addAclLabel(model, new AclLabel("_btnDOC_EDITING_918", AuthType.Modify, params, false));
		}

		addAclLabel(model,
				new AclLabel("_btn_APPROVE", params, getDomainClass(), AuthType.Accept, CreditDocStatusEnum.授管處_停權已覆核));

		renderJsI18N(LMS7850M01Page.class);

		Map<String, String> msgs = null;
		msgs = lmsService.getCodeType("Common_Currcy");
		renderJsI18NWithMsgName("Common_Currcy", msgs);

		msgs = lmsService.getCodeType("lms1405m01_SubItem");
		renderJsI18NWithMsgName("lms1405m01_SubItem", msgs);

		msgs = lmsService.getCodeType("lmsUseCms_collTyp1");
		renderJsI18NWithMsgName("lmsUseCms_collTyp1", msgs);

		// J-112-0449_05097_B1003 Web e-Loan企金額度明細表新增主要用途查詢條件
		msgs = lmsService.getCodeType("cms1010_bldUse");
		renderJsI18NWithMsgName("cms1010_bldUse", msgs);

		renderRespMsgJsI18N(UtilConstants.AJAX_RSP_MSG.ERR_MSG); // 多render一個msgi18n
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L785M01A.class;
	}
}
