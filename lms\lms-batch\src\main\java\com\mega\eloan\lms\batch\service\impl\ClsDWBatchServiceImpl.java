package com.mega.eloan.lms.batch.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.L120M01A;

import tw.com.iisi.cap.service.AbstractCapService;

/**
 * <pre>
 * 【SLMS-00013】批次上傳個金分數檔 
 * </pre>
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */

@Service("clsdwbatchserviceimpl")
public class ClsDWBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private static Logger LOGGER = LoggerFactory
			.getLogger(ClsDWBatchServiceImpl.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService eloandbBASEService;
	@Resource
	LMSService lmsService;
	@Resource
	CLSService clsService;
	@Resource
	L120M01ADao l120m01aDao;

	@Override
	public JSONObject execute(JSONObject json) {
		// 可以從json內取得參數
		long t1 = System.currentTimeMillis();
		LOGGER.info("傳入參數==>[{}]", json.toString());
		JSONObject result = new JSONObject();
		int count = 0;
		try {
			LOGGER.info("clsdwbatchserviceimpl[bef]eloandbBASEService.selByCLSUPDW()");
			List<String> oidlist = eloandbBASEService.selByCLSUPDW();
			LOGGER.info("clsdwbatchserviceimpl[aft]eloandbBASEService.selByCLSUPDW(), cnt="+oidlist.size());
			
			if (!oidlist.isEmpty()) {
				LOGGER.info("clsdwbatchserviceimpl[bef]l120m01aDao.findByOids(...)");
				List<L120M01A> l120m01as = l120m01aDao.findByOids(oidlist
						.toArray(new String[oidlist.size()]));
				LOGGER.info("clsdwbatchserviceimpl[aft]l120m01aDao.findByOids(...), cnt="+l120m01as.size());
				for (L120M01A l120m01a : l120m01as) {
					count = count + clsService.L120UploadDW(l120m01a);
				}
			}
			LOGGER.info("執行成功！總執行文件數 ==> " + count);
			
			result = WebBatchCode.RC_SUCCESS;
		} catch (Exception ex) {
			LOGGER.error("[execute] Exception!!", ex);
			result = WebBatchCode.RC_ERROR;
		} finally {
			LOGGER.info("RESULT={}", result.toString());
			LOGGER.info(StrUtils.concat("TOTAL_COST= ",
					(System.currentTimeMillis() - t1), " ms"));
		}

		return result;
	}
}
