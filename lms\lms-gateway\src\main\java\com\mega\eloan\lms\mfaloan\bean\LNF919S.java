package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 下期期付金攤還表檔 **/
public class LNF919S extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 資料產生日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF919S_PROC_DATE", columnDefinition="DATE", unique = true)
	private Date lnf919s_proc_date;

	/** 攤還表FORM ID **/
	@Size(max=8)
	@Column(name="LNF919S_FORMID", length=8, columnDefinition="CHAR(8)", unique = true)
	private String lnf919s_formid;

	/** 放款帳號 **/
	@Size(max=14)
	@Column(name="LNF919S_LOAN_NO", length=14, columnDefinition="CHAR(14)", unique = true)
	private String lnf919s_loan_no;

	/** 償還期數 **/
	@Column(name="LNF919S_RT_TERM", columnDefinition="DECIMAL(3,0)", unique = true)
	private Integer lnf919s_rt_term;

	/** 戶別 **/
	@Size(max=10)
	@Column(name="LNF919S_LOAN_SEQ", length=10, columnDefinition="CHAR(10)")
	private String lnf919s_loan_seq;

	/** 客戶ID **/
	@Size(max=11)
	@Column(name="LNF919S_CUST_ID", length=11, columnDefinition="CHAR(11)")
	private String lnf919s_cust_id;

	/** 客戶姓名 **/
	@Size(max=50)
	@Column(name="LNF919S_CUST_NAME", length=50, columnDefinition="CHAR(50)")
	private String lnf919s_cust_name;

	/** 郵遞區號 **/
	@Size(max=3)
	@Column(name="LNF919S_ZIP", length=3, columnDefinition="CHAR(3)")
	private String lnf919s_zip;

	/** 通訊地址1 **/
	@Size(max=82)
	@Column(name="LNF919S_ADDR1", length=82, columnDefinition="CHAR(82)")
	private String lnf919s_addr1;

	/** 通訊地址2 **/
	@Size(max=82)
	@Column(name="LNF919S_ADDR2", length=82, columnDefinition="CHAR(82)")
	private String lnf919s_addr2;

	/** 剩餘期數 **/
	@Column(name="LNF919S_LEFT_TERM", columnDefinition="DECIMAL(3,0)")
	private Integer lnf919s_left_term;

	/** 上期本金餘額 **/
	@Column(name="LNF919S_B_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_b_bal;

	/** 應還款日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="LNF919S_RT_DATE", columnDefinition="DATE")
	private Date lnf919s_rt_date;

	/** 利率 **/
	@Column(name="LNF919S_INT_RATE", columnDefinition="DECIMAL(8,6)")
	private BigDecimal lnf919s_int_rate;

	/** 當期應繳利息 **/
	@Column(name="LNF919S_RT_INT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_rt_int;

	/** 當期應繳本金 **/
	@Column(name="LNF919S_RT_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_rt_bal;

	/** 累計應收利息 **/
	@Column(name="LNF919S_ACCU_INT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_accu_int;

	/** 應繳期付金合計 **/
	@Column(name="LNF919S_RT_AMT", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_rt_amt;

	/** 本期償還後本金餘額 **/	
	@Column(name="LNF919S_E_BAL", columnDefinition="DECIMAL(15,2)")
	private BigDecimal lnf919s_e_bal;

	/** 存款帳號 **/
	@Size(max=11)
	@Column(name="LNF919S_DP_ACT_NO", length=11, columnDefinition="CHAR(11)")
	private String lnf919s_dp_act_no;

	/** 取得資料產生日期 **/
	public Date getLnf919s_proc_date() {
		return this.lnf919s_proc_date;
	}
	/** 設定資料產生日期 **/
	public void setLnf919s_proc_date(Date value) {
		this.lnf919s_proc_date = value;
	}

	/** 取得攤還表FORM ID **/
	public String getLnf919s_formid() {
		return this.lnf919s_formid;
	}
	/** 設定攤還表FORM ID **/
	public void setLnf919s_formid(String value) {
		this.lnf919s_formid = value;
	}

	/** 取得放款帳號 **/
	public String getLnf919s_loan_no() {
		return this.lnf919s_loan_no;
	}
	/** 設定放款帳號 **/
	public void setLnf919s_loan_no(String value) {
		this.lnf919s_loan_no = value;
	}

	/** 取得償還期數 **/
	public Integer getLnf919s_rt_term() {
		return this.lnf919s_rt_term;
	}
	/** 設定償還期數 **/
	public void setLnf919s_rt_term(Integer value) {
		this.lnf919s_rt_term = value;
	}

	/** 取得戶別 **/
	public String getLnf919s_loan_seq() {
		return this.lnf919s_loan_seq;
	}
	/** 設定戶別 **/
	public void setLnf919s_loan_seq(String value) {
		this.lnf919s_loan_seq = value;
	}

	/** 取得客戶ID **/
	public String getLnf919s_cust_id() {
		return this.lnf919s_cust_id;
	}
	/** 設定客戶ID **/
	public void setLnf919s_cust_id(String value) {
		this.lnf919s_cust_id = value;
	}

	/** 取得客戶姓名 **/
	public String getLnf919s_cust_name() {
		return this.lnf919s_cust_name;
	}
	/** 設定客戶姓名 **/
	public void setLnf919s_cust_name(String value) {
		this.lnf919s_cust_name = value;
	}

	/** 取得郵遞區號 **/
	public String getLnf919s_zip() {
		return this.lnf919s_zip;
	}
	/** 設定郵遞區號 **/
	public void setLnf919s_zip(String value) {
		this.lnf919s_zip = value;
	}

	/** 取得通訊地址1 **/
	public String getLnf919s_addr1() {
		return this.lnf919s_addr1;
	}
	/** 設定通訊地址1 **/
	public void setLnf919s_addr1(String value) {
		this.lnf919s_addr1 = value;
	}

	/** 取得通訊地址2 **/
	public String getLnf919s_addr2() {
		return this.lnf919s_addr2;
	}
	/** 設定通訊地址2 **/
	public void setLnf919s_addr2(String value) {
		this.lnf919s_addr2 = value;
	}

	/** 取得剩餘期數 **/
	public Integer getLnf919s_left_term() {
		return this.lnf919s_left_term;
	}
	/** 設定剩餘期數 **/
	public void setLnf919s_left_term(Integer value) {
		this.lnf919s_left_term = value;
	}

	/** 取得上期本金餘額 **/
	public BigDecimal getLnf919s_b_bal() {
		return this.lnf919s_b_bal;
	}
	/** 設定上期本金餘額 **/
	public void setLnf919s_b_bal(BigDecimal value) {
		this.lnf919s_b_bal = value;
	}

	/** 取得應還款日期 **/
	public Date getLnf919s_rt_date() {
		return this.lnf919s_rt_date;
	}
	/** 設定應還款日期 **/
	public void setLnf919s_rt_date(Date value) {
		this.lnf919s_rt_date = value;
	}

	/** 取得利率 **/
	public BigDecimal getLnf919s_int_rate() {
		return this.lnf919s_int_rate;
	}
	/** 設定利率 **/
	public void setLnf919s_int_rate(BigDecimal value) {
		this.lnf919s_int_rate = value;
	}

	/** 取得當期應繳利息 **/
	public BigDecimal getLnf919s_rt_int() {
		return this.lnf919s_rt_int;
	}
	/** 設定當期應繳利息 **/
	public void setLnf919s_rt_int(BigDecimal value) {
		this.lnf919s_rt_int = value;
	}

	/** 取得當期應繳本金 **/
	public BigDecimal getLnf919s_rt_bal() {
		return this.lnf919s_rt_bal;
	}
	/** 設定當期應繳本金 **/
	public void setLnf919s_rt_bal(BigDecimal value) {
		this.lnf919s_rt_bal = value;
	}

	/** 取得累計應收利息 **/
	public BigDecimal getLnf919s_accu_int() {
		return this.lnf919s_accu_int;
	}
	/** 設定累計應收利息 **/
	public void setLnf919s_accu_int(BigDecimal value) {
		this.lnf919s_accu_int = value;
	}

	/** 取得應繳期付金合計 **/
	public BigDecimal getLnf919s_rt_amt() {
		return this.lnf919s_rt_amt;
	}
	/** 設定應繳期付金合計 **/
	public void setLnf919s_rt_amt(BigDecimal value) {
		this.lnf919s_rt_amt = value;
	}

	/** 取得本期償還後本金餘額 **/
	public BigDecimal getLnf919s_e_bal() {
		return this.lnf919s_e_bal;
	}
	/** 設定本期償還後本金餘額 **/
	public void setLnf919s_e_bal(BigDecimal value) {
		this.lnf919s_e_bal = value;
	}

	/** 取得存款帳號 **/
	public String getLnf919s_dp_act_no() {
		return this.lnf919s_dp_act_no;
	}
	/** 設定存款帳號 **/
	public void setLnf919s_dp_act_no(String value) {
		this.lnf919s_dp_act_no = value;
	}
}
