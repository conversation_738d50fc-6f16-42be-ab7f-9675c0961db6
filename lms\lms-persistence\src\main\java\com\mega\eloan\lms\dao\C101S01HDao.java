/* 
 * C101S01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C101S01H;


/** 個金徵信訊息紀錄表 **/
public interface C101S01HDao extends IGenericDao<C101S01H> {

	C101S01H findByOid(String oid);

	List<C101S01H> findByMainId(String mainId);

	/** 1個人可能會有2筆 C101S01H */
	@Deprecated
	C101S01H findByUniqueKey(String mainId, String custId, String dupNo);

	List<C101S01H> findByIndex01(String mainId, String custId, String dupNo);
	
	List<C101S01H> findByCustIdDupId(String custId,String DupNo);

	int deleteByOid(String oid);

	C101S01H findBy(String custId, String dupNo, String txId, String prodId,
			String qBranch, String qDate, String qEmpCode);
}