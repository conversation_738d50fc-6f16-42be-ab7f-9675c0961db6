/* 
 * L001M01ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L001M01ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L001M01A;

/** 待辦事項篩選條件檔 **/
@Repository
public class L001M01ADaoImpl extends LMSJpaDao<L001M01A, String> implements
		L001M01ADao {

	@Override
	public L001M01A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L001M01A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L001M01A> list = createQuery(L001M01A.class,search).getResultList();
		return list;
	}
	
	@Override
	public L001M01A findByUniqueKey(String userId){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "userId", userId);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L001M01A> findByIndex01(String userId){
		ISearch search = createSearchTemplete();
		List<L001M01A> list = null;
		if (userId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "userId", userId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L001M01A.class,search).getResultList();
		}
		return list;
	}
}