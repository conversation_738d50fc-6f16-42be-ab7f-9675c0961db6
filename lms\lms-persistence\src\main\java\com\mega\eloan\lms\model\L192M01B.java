package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringExclude;

import com.mega.eloan.common.model.IDocObject;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

/**
 * 借款人及連保人基本資料檔
 * 
 * <AUTHOR>
 * 
 */
@NamedEntityGraph(name = "L192M01B-entity-graph", attributeNodes = { 
		@NamedAttributeNode("l192m01a"),
		@NamedAttributeNode("l192m01e")
		})
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = { "mainId",
		"mainCustId", "mainDupNo", "custType", "custId", "dupNo" }))
public class L192M01B extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = -4159414700530841503L;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(unique = true, nullable = false, length = 32, columnDefinition = "CHAR(32)")
	private String oid;

	@Column(length = 32, nullable = false, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 借款人統一編號,配合團貸新增 */
	@Column(length = 10, columnDefinition = "CHAR(10) default ''")
	private String mainCustId = "";

	/** 借款人重覆序號,配合團貸新增 */
	@Column(length = 1, columnDefinition = "CHAR(1) default ''")
	private String mainDupNo = "";

	/** 債務人種類 */
	@Column(length = 1, columnDefinition = "CHAR(1)")
	private String custType;

	/** 身分證統編 */
	@Column(length = 10, columnDefinition = "VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 */
	@Column(length = 1, columnDefinition = "CHAR(1)")
	private String dupNo;

	/** 借款人姓名 */
	@Column(length = 120, columnDefinition = "VARCHAR(120)")
	private String custName;

	/** 職業(行業別) */
	@Column(length = 60, columnDefinition = "VARCHAR(60)")
	private String posi;

	/** 收入(幣別) */
	@Column(length = 3, columnDefinition = "VARCHAR(3)")
	private String incomeCurr;

	/** 收入(金額) */
	@Column(columnDefinition = "DECIMAL(13,0)")
	private BigDecimal incomeAmt;

	/** 建立人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date createTime;

	/** 異動人員號碼 */
	@Column(length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 */
	@Temporal(TemporalType.TIMESTAMP)
	private Date updateTime;

	/** 借款人電話 */
	@Column(length = 16, columnDefinition = "VARCHAR(16)")
	private String tTel;

	/** 借款人地址 */
	@Column(length = 255, columnDefinition = "VARCHAR(255)")
	private String tAddr;

	/** 有無借款人及連保人徵信查詢 */
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String cdQ1;

	/** 有無借款人及連保人票信查詢 */
	@Column(length = 1, columnDefinition = "VARCHAR(1)")
	private String cdQ2;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false) })
	private L192M01A l192m01a;
	
	/**團貸用*/
	@OneToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	@JoinColumns({
			@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "MAINCUSTID", referencedColumnName = "MAINCUSTID", nullable = false, insertable = false, updatable = false),
			@JoinColumn(name = "MAINDUPNO", referencedColumnName = "MAINDUPNO", nullable = false, insertable = false, updatable = false) })
	private L192M01E l192m01e;
	
	/**團貸用*/
	@ToStringExclude
	@OneToMany(mappedBy = "l192m01b", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
	private Set<L192S01A> l192s01as;


	@Override
	public String getOid() {
		return this.oid;
	}

	@Override
	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	/** 債務人種類 1.借款人, 2.連保人 */
	public String getCustType() {
		return custType;
	}

	/** 債務人種類 1.借款人, 2.連保人 */
	public void setCustType(String custType) {
		this.custType = custType;
	}

	/** 身分證統編 */
	public String getCustId() {
		return custId;
	}

	/** 身分證統編 */
	public void setCustId(String custId) {
		this.custId = custId;
	}

	/** 身分證統編重複碼 */
	public String getDupNo() {
		return dupNo;
	}

	/** 身分證統編重複碼 */
	public void setDupNo(String dupNo) {
		this.dupNo = dupNo;
	}

	/** 借款人姓名 */
	public String getCustName() {
		return custName;
	}

	/** 借款人姓名 */
	public void setCustName(String custName) {
		this.custName = custName;
	}

	/** 職業(行業別) */
	public String getPosi() {
		return posi;
	}

	/** 職業(行業別) */
	public void setPosi(String posi) {
		this.posi = posi;
	}

	/** 收入(幣別) */
	public String getIncomeCurr() {
		return incomeCurr;
	}

	/** 收入(幣別) */
	public void setIncomeCurr(String incomeCurr) {
		this.incomeCurr = incomeCurr;
	}

	/** 收入(金額) */
	public BigDecimal getIncomeAmt() {
		return incomeAmt;
	}

	/** 收入(金額) */
	public void setIncomeAmt(BigDecimal incomeAmt) {
		this.incomeAmt = incomeAmt;
	}

	/** 建立人員號碼 */
	public String getCreator() {
		return creator;
	}

	/** 建立人員號碼 */
	public void setCreator(String creator) {
		this.creator = creator;
	}

	/** 建立日期 */
	public Date getCreateTime() {
		return createTime;
	}

	/** 建立日期 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/** 異動人員號碼 */
	public String getUpdater() {
		return updater;
	}

	/** 異動人員號碼 */
	public void setUpdater(String updater) {
		this.updater = updater;
	}

	/** 異動日期 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/** 異動日期 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public L192M01A getL192m01a() {
		return l192m01a;
	}

	public void setL192m01a(L192M01A l192m01a) {
		this.l192m01a = l192m01a;
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(custId).append(dupNo)
				.append(mainCustId).append(mainDupNo).append(custType)
				.append(mainId).toHashCode();
	}

	@Override
	public boolean equals(Object o) {
		boolean equals = false;
		if (o instanceof L192M01B) {
			L192M01B bean = (L192M01B) o;
			equals = (new EqualsBuilder().append(custId, bean.custId)
					.append(dupNo, bean.dupNo)
					.append(mainCustId, bean.mainCustId)
					.append(mainDupNo, bean.mainDupNo)
					.append(custType, bean.custType)
					.append(mainId, bean.mainId)).isEquals();
		}
		return equals;
	}

	/** 借款人統一編號 */
	public String getMainCustId() {
		return mainCustId;
	}

	/** 借款人統一編號 */
	public void setMainCustId(String mainCustId) {
		this.mainCustId = mainCustId;
	}

	/** 借款人重覆序號 */
	public String getMainDupNo() {
		return mainDupNo;
	}

	/** 借款人重覆序號 */
	public void setMainDupNo(String mainDupNo) {
		this.mainDupNo = mainDupNo;
	}

	/** 借款人電話 */
	public String gettTel() {
		return tTel;
	}

	/** 借款人電話 */
	public void settTel(String tTel) {
		this.tTel = tTel;
	}

	/** 借款人地址 */
	public String gettAddr() {
		return tAddr;
	}

	/** 借款人地址 */
	public void settAddr(String tAddr) {
		this.tAddr = tAddr;
	}

	/** 有無借款人及連保人徵信查詢 */
	public String getCdQ1() {
		return cdQ1;
	}

	/** 有無借款人及連保人徵信查詢 */
	public void setCdQ1(String cdQ1) {
		this.cdQ1 = cdQ1;
	}

	/** 有無借款人及連保人票信查詢 */
	public String getCdQ2() {
		return cdQ2;
	}

	/** 有無借款人及連保人票信查詢 */
	public void setCdQ2(String cdQ2) {
		this.cdQ2 = cdQ2;
	}

	public void setL192m01e(L192M01E l192m01e) {
		this.l192m01e = l192m01e;
	}

	public L192M01E getL192m01e() {
		return l192m01e;
	}

	public Set<L192S01A> getL192s01as() {
		return l192s01as;
	}

	public void setL192s01as(Set<L192S01A> l192s01as) {
		this.l192s01as = l192s01as;
	}

}
