package com.mega.eloan.lms.batch.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.io.IOUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.cls.service.CLS1131Service;
import com.mega.eloan.lms.dao.C101M01ADao;
import com.mega.eloan.lms.dao.L161S01DDao;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.model.C101M01A;
import com.mega.eloan.lms.model.C101S01E;
import com.mega.eloan.lms.model.C900M01M;
import com.mega.eloan.lms.model.L161S01D;

import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;

/**
 * <pre>
 * RPA 地政士回傳
 * </pre>
 * 
 *  LOCAL Test URL example ：
 *          http://localhost:9080/cms-web/app/schedulerRPA
 * 
 *          Post Request : {"serviceId":"getAgentInfo",
					"vaildIP":"N",
					"request":{"responseCode":"0","responseMsg":"查詢成功","uniqueID":"17C52AD0767911EBB5B65A0EC0A83B1E",
					            "agentInfo":[{
					                "agentName" : "林丁豐",
					                "agentCertYear" : "82",
					                "agentCertWord" : "台內地登",
					                "agentCertNo" : "011306",
					                "landOffice" : "忠勝地政士事務所",
					                "license" : "109年中市地士字第001994號"
					                }
					            ],
					"prtScrn":""}}
 * 
 * SIT http://*************:9081/lms-web/app/schedulerRPA
 * 
 * @since 2012/7/26
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/26,REX,new
 *          </ul>
 */
@Service("getAgentInfo")
public class RpaGetAgentInfoService extends AbstractCapService implements
		WebBatchService {
	private static Logger logger = LoggerFactory
			.getLogger(RpaGetAgentInfoService.class);

	private static final long serialVersionUID = 1L;

	@Resource
	EloandbBASEService r6dbService;

	@Resource
	EloandbBASEService eloanService;

	@Resource
	DocFileService docFileService;

	@Resource
	DocLogService docLogService;

	@Resource
	CLS1131Service cls1131Service;

	@Resource
	C101M01ADao c101m01Dao;

	@Resource
	L161S01DDao l161s01dDao;

	@Resource
	CLSService clsService;
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject mag = new JSONObject();
		logger.info("getAgentInfo 啟動========================");
		logger.info("傳入參數==>[{}]", json.toString());
		JSONObject req = json.getJSONObject("request");
		String errorMsg = "";
		String uniqueID = req.optString("uniqueID", "");
		String mainId = uniqueID.split("\\|")[0]; // L161S01D.mainid
		String queryAgentNm = uniqueID.split("\\|")[1]; // L161S01D.rpaQueryReason1

		C101M01A c101m01a = c101m01Dao.findByMainIdone(mainId);
		List<L161S01D> l161s01ds = l161s01dDao.findByIndex02(mainId,
				UtilConstants.RPA.TYPE.地政士, UtilConstants.RPA.STATUS.查詢中,
				queryAgentNm);

		for (L161S01D l161s01d : l161s01ds) {

			try {
				byte[] bytes1 = LMSUtil.base64StringToImage(req.getString("prtScrn"));
				String responseCode = req.getString("responseCode");
				String responseMsg = req.getString("responseMsg");
				
				//TODO TEST
//				ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
//				BufferedImage bi1 = ImageIO.read(bais);
//				File f1 = new File("d://out.jpg");
//				ImageIO.write(bi1, "jpg", f1);
				
				// 設定上傳檔案資訊
				DocFile docFile = new DocFile();
				docFile.setBranchId(c101m01a.getOwnBrId());
				docFile.setContentType("application/pdf");
				docFile.setMainId(c101m01a.getMainId());
				docFile.setPid(l161s01d.getOid());
				docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
				docFile.setFieldId("rpa_lms");
				docFile.setDeletedTime(null);
				docFile.setSrcFileName("地政士(" + l161s01d.getRpaQueryReason1() + ").pdf");
				docFile.setUploadTime(CapDate.getCurrentTimestamp());
				docFile.setSysId(docFileService.getSysId());
				docFile.setFileSize(bytes1.length);
				docFile.setFileDesc(responseCode.equals("0") ? "查詢成功" : responseMsg);
				
				InputStream is = new ByteArrayInputStream(bytes1);;
				String fileKey = "";
				int[] dimension = { -1, -1 };
				try {
					//排除附檔欄位，因某些特殊字串造成CLOB存檔失敗
					JSONObject reqSave = json.getJSONObject("request");
					reqSave.remove("prtScrn");
					
					if (responseCode.equals("0")) {
						// 設定上傳檔案處理物件
						docFile.setData(IOUtils.toByteArray(is));

						// 儲存上傳檔案
						fileKey = docFileService.save(docFile);
						
						l161s01d.setStatus("A02"); //查詢完成
						l161s01d.setReason(responseMsg);
						l161s01d.setData(reqSave.toString());
						l161s01d.setDocfileoid(fileKey);
					} else {
						l161s01d.setStatus("A03"); //查詢失敗
						l161s01d.setReason(responseMsg);
						l161s01d.setData(reqSave.toString());
					}

					// 若是圖檔取得其尺寸
					//dimension = docFileService.getImageDimension(docFile);
				} catch (IOException e) {
					logger.error(e.getMessage(), e);
					throw new CapMessageException("file IO ERROR", getClass());
				} finally {
					if (is != null) {
						try {
							is.close();
						} catch (IOException e) {
							logger.debug("inputStream close Error", getClass());
						}
					}
				}
				
				cls1131Service.save(l161s01d);
				// 上傳文件數位化
//				eLoanUploadImageFile(l161s01d, docFile);
				
				//判斷回傳只有一筆地政士時，自動引入
				//匯入相關欄位data
				JSONObject reqImport = JSONObject.fromObject(l161s01d.getData());
				JSONArray agentAry =  reqImport.getJSONArray("agentInfo");
				
				if (agentAry.size() == 1) {
					C101S01E c101s01e = clsService.findC101S01E(c101m01a);
					
					String agentName = agentAry.getJSONObject(0).optString("agentName", "");
					String agentCertYear = agentAry.getJSONObject(0).optString("agentCertYear", "");
					String agentCertWord = agentAry.getJSONObject(0).optString("agentCertWord", "");
					String agentCertNo = agentAry.getJSONObject(0).optString("agentCertNo", "");
					String landOffice = agentAry.getJSONObject(0).optString("landOffice", "");
					String license = agentAry.getJSONObject(0).optString("license", "");
					
					if (c101s01e != null) {
						c101s01e.setLaaName(agentName);
						c101s01e.setLaaYear(agentCertYear);
						c101s01e.setLaaWord(agentCertWord);
						c101s01e.setLaaNo(agentCertNo);
						c101s01e.setLaaOffice(landOffice);
						
						clsService.save(c101s01e);
					}
				}
			} catch (Exception e) {
				errorMsg = e.getMessage();
				logger.error(e.getMessage(), getClass());
				e.printStackTrace();
			}
		}
		logger.info("getAgentInfo 結束========================");

		if (!CapString.isEmpty(errorMsg)) {
			mag = JSONObject
					.fromObject("{\"rc\": 0, \"rcmsg\": \"FAIL\", \"message\":\" "
							+ errorMsg + "\"}");
		} else {
			mag = JSONObject
					.fromObject("{\"rc\": 0, \"rcmsg\": \"SUCCESS\", \"message\":\"執行成功\"}");
		}

		return mag;
	}

	/**
	 * 上傳數位文件化
	 * @param meta
	 * @param docFile
	 */
	private void eLoanUploadImageFile(GenericBean bean, DocFile docFile) {
		String mainId = "";
		String formId = "";
		GenericBean model = null;
		// 1.建立上傳紀錄檔
		C900M01M c900m01m = new C900M01M();
		// 個金徵信借款人主檔 
		L161S01D l161s01d = (L161S01D) bean;
		formId = "ECL00002";
		mainId = l161s01d.getMainId();
		C101M01A c101m01a = c101m01Dao.findByMainIdone(mainId);
		model = c101m01a;
		c900m01m.setApplicationDate(CapDate.getCurrentDate("yyyyMMdd"));
		c900m01m.setBorrower(c101m01a.getCustId());
		c900m01m.setBranch(c101m01a.getOwnBrId());
		c900m01m.setCaseNo("");	// 案件簽報書案件號碼
		c900m01m.setDocFileOid(docFile.getOid());
		c900m01m.setFormId(formId);
		c900m01m.setMainId(l161s01d.getMainId());
		c900m01m.setStakeholderID(c101m01a.getCustId());
		c900m01m.setUpdateTime(CapDate.getCurrentTimestamp());
		c900m01m.setUserCode(l161s01d.getUpdater());
		// 2.依上傳紀錄檔內容與文件類型執行上傳文件數位化作業
		clsService.eLoanUploadImageFile(c900m01m, model);
	}
}
