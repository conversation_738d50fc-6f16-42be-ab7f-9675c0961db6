/**
 * <AUTHOR>
 */
var CapTM = CapTM ||
function(settings){
    var empFunction = function(){
        return true;
    };
    settings = $.extend(true, {
        handler: '',
        gridHandler: '',
        formId: 'TMForm',
        formDefValue: null,
        btnQueryId: 'TMQuery',
        btnInsertId: 'TMNew',
        btnModifyId: 'TMModify',
        btnDelelteId: 'TMDelete',
        gridId: 'TMGrid',
        gridSettings: null,
        onReset: empFunction,
        onActionBefore: empFunction,
        onInsertActionBefore: empFunction,
        // onInsertAction: null,
        onInsertActionAfter: empFunction,
        onModifyActionBefore: empFunction,
        //onModifyAction: null,
        onModifyActionAfter: empFunction,
        onDeleteActionBefore: empFunction,
        //onDeleteAction: null,
        onDeleteActionAfter: empFunction,
        onGridLoadSuccess: empFunction,
        onActionAfter: empFunction
    }, settings ||
    {});
    
    //TableMainTain
    var _capTM = function(s){
        ilog.debug('tm init enter');
        if (!s.handler || !s.gridSettings || !s.gridHandler) {
            CommonAPI.iConfirmDialog({
                message: 'TM init error'
            });
            return
        }
        
        ilog.debug('tm init start');
        //dom cache
        var tform = $("#" + s.formId), btnQ = $("#" + s.btnQueryId), btnI = $("#" + s.btnInsertId), btnM = $("#" + s.btnModifyId), btnD = $("#" + s.btnDelelteId);
        var defValue = s.formDefValue || tform.serializeData();
        
        
        var tmGrid = $("#" + s.gridId).iGrid($.extend({}, s.gridSettings, {
            handler:s.gridHandler,
            onSelectRow: function(rid){
                var gridData = $("#" + this.id).getRowData(rid);
                tform.injectData(defValue).injectData(gridData);
                s.gridSettings.onSelectRow && s.gridSettings.onSelectRow.call(this, rid, gridData);
            },
            postData: s.gridSettings.postData ||
            {},
            
            loadComplete: s.onGridLoadSuccess
        }));
        var self = this;
        var btnAction = function(actionType){
            //如使用onXXXAction 則 onXXXBefore 及  onXXXAfter 無作用
            try {
                return /*s['on' + actionType + 'Action'] ||*/ function(){
                    //暫存資料
                    self.params = {
                        newData: tform.serializeData(),
                        oldData: tmGrid.getRowData(tmGrid.getGridParam("selrow")),
                        actionType: actionType
                    };
                    try {
                        s.onActionBefore.call(self, self.params, s);
                        switch (actionType) {
                            case 'Query':
                                var qData = tform.serializeData(true);
                                for (var k in qData) {
                                    !qData[k] && delete qData[k];
                                }
                                tmGrid.setGridParam({
                                    datatype: 'json',
                                    postData: {
                                        queryData: JSON.stringify(qData, null)
                                    },
                                    page: 1
                                }).trigger('reloadGrid');
                                break;
                            case 'Insert':
                                tform.valid() && action(i18n.def.TMInsert);
                                break;
                            case 'Modify':
                                if (tmGrid.getGridParam("selrow")) {
                                    if (tform.valid()) {
                                        //判斷是否有更改key
                                        var b = true;
                                        //產生比對Method
                                        var tmpF = "return ";
                                        for (var k in self.params.oldData) {
                                            tmpF += " && tfrom.find('#" + k + "').val() == rd['" + k + "']";
                                        }
                                        tmpF = (tmpF += ';').replace("&&", "");
                                        ilog.debug(tmpF);
                                        if (!(new Function('tfrom', 'rd', tmpF))(tform, self.params.oldData)) {
                                            action(i18n.def.TMModify);
                                        }
                                        else {
                                            throw i18n.def.TMNoChangeModify;
                                        }
                                    }
                                }
                                else {
                                    throw i18n.def.TMMDeleteError;
                                }
                                break;
                            case 'Delete':
                                if (tmGrid.getGridParam("selrow")) {
                                    action(i18n.def.TMDelete);
                                }
                                else {
                                    throw i18n.def.TMMDeleteError;
                                }
                                break;
                        }
                        
                    } 
                    catch (err2) {
                        ilog.debug(err2);
                        CommonAPI.iConfirmDialog({
                            id: 'TMError',
                            message: err2
                        });
                    }
                    function action(message){
                        ilog.debug(actionType);
                        s['on' + actionType + 'ActionBefore'].call(self, self.params, s);
                        var defbtnss = {};
                        defbtnss[i18n.def.cancel] = function(){
                            CommonAPI.iConfirmDialog('close');
                        };
                        defbtnss[i18n.def.sure] = function(){
                            $.ajax({
                                type: "POST",
                                data: $.extend({}, actionType == 'Delete' ? self.params.oldData : self.params.newData, {
                                    _pa: s.handler,
                                    formAction: actionType.substr(0, 1)
                                })
                            }).done(function(responseData) {
                              tmGrid.trigger('reloadGrid');
                              CommonAPI.iConfirmDialog('close');
                              s['on' + actionType + 'ActionAfter'].call(self, self.params, s);
                              s.onActionAfter.call(self, self.params);
                            });
                        };
                        CommonAPI.iConfirmDialog({
                            message: message,
                            buttons: defbtnss,
                            width: 400
                        });
                    }
                    
                };
            } 
            catch (err) {
                ilog.debug(err2);
				CommonAPI.showMessage(err);
            }
        };
        
        btnQ.click(btnAction.call(self, "Query"));
        btnI.click(s.onInsertAction || btnAction.call(self, "Insert"));
        btnM.click(s.onModifyAction || btnAction.call(self, "Modify"));
        btnD.click(s.onDeleteAction || btnAction.call(self, "Delete"));
        
        //TM Method
        $.extend(this, {
            getParam: function(){
                return s;
            },
            reset: function(){
                tform.injectData(defValue);
                s.onReset();
                return this;
            },
            setParam: function(newSettings){
                $.extend(s, newSettings);
                return this;
            }
        });
        ilog.debug('tm init end');
    };
    return new _capTM(settings);
};
