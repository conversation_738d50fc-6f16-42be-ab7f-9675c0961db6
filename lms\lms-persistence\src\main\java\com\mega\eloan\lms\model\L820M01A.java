/* 
 * L820M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import org.apache.bval.constraints.NotEmpty;

import tw.com.iisi.cap.model.IDataObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.lms.validation.group.Check3;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 以房養老貸款撥款前查詢檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L820M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L820M01A extends Meta implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * 批示
	 * <p/>
	 * 100個全型字
	 */
	@Size(max = 300)
	@Column(name = "SIGN", length = 300, columnDefinition = "VARCHAR(300)")
	private String sign;

	/**
	 * 審核意見
	 * <p/>
	 * 100個全型字
	 */
	@Size(max = 300)
	@Column(name = "COMM", length = 300, columnDefinition = "VARCHAR(300)")
	private String comm;
	
	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 戶政查詢結果<p/>
	 * 身分證發查通過:Y未通過:N查詢中:I
	 */
	@Size(max=1)
	@Column(name="IDESEARCHRESULT", length=1, columnDefinition="CHAR(1)")
	private String ideSearchResult;

	/** 
	 * 家事查詢結果<p/>
	 * 家事發查<br/>
	 *  通過:Y未通過:N查詢中:I
	 */
	@Size(max=1)
	@Column(name="FASEARCHRESULT", length=1, columnDefinition="CHAR(1)")
	private String FaSearchResult;
	
	/** 
	 * 財富管理查詢結果<p/>
	 * 財富管理<br/>
	 *  通過:Y未通過:N查詢中:I
	 */
	@Size(max=1)
	@Column(name="WEALTHRESULT", length=1, columnDefinition="CHAR(1)")
	private String WealthResult;

	/**
	 * 取得批示
	 * <p/>
	 * 1024個全型字
	 */
	public String getSign() {
		return this.sign;
	}

	/**
	 * 設定批示
	 * <p/>
	 * 1024個全型字
	 **/
	public void setSign(String value) {
		this.sign = value;
	}

	/**
	 * 取得審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 1024個全型字
	 */
	public String getComm() {
		return this.comm;
	}

	/**
	 * 設定審核意見
	 * <p/>
	 * 預設：貸放手續齊全，擬准予動用<br/>
	 * 1024個全型字
	 **/
	public void setComm(String value) {
		this.comm = value;
	}
	
	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得戶政查詢結果<p/>
	 * 身分證發查通過:Y未通過:N查詢中:I
	 */
	public String getIdeSearchResult() {
		return this.ideSearchResult;
	}
	/**
	 *  設定戶政查詢結果<p/>
	 *  身分證發查通過:Y未通過:N查詢中:I
	 **/
	public void setIdeSearchResult(String value) {
		this.ideSearchResult = value;
	}

	/** 
	 * 取得家事查詢結果<p/>
	 * 家事發查<br/>
	 *  通過:Y未通過:N查詢中:I
	 */
	public String getFaSearchResult() {
		return this.FaSearchResult;
	}
	/**
	 *  設定家事查詢結果<p/>
	 *  家事發查<br/>
	 *  通過:Y未通過:N查詢中:I
	 **/
	public void setFaSearchResult(String value) {
		this.FaSearchResult = value;
	}
	/**
	 *  設定財富管理查詢結果<p/>
	 *  財富管理<br/>
	 *  通過:Y未通過:N查詢中:I
	 **/
	public void setWealthResult(String wealthResult) {
		WealthResult = wealthResult;
	}
	/**
	 *  取得財富管理查詢結果<p/>
	 *  財富管理<br/>
	 *  通過:Y未通過:N查詢中:I
	 **/
	public String getWealthResult() {
		return WealthResult;
	}
}
