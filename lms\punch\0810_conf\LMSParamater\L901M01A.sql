delete from LMS.L901M01A where BRANCHID='918';
-- zh_TW --
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  1, '核准敘做文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  2, '綜合授信契約', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCH<PERSON>, ITEMTYPE, LOCAL<PERSON>, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  3, '本票（額度）', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  4, '授信約定書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  5, '連帶保證書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  6, '公司董事會決議錄', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  7, '開發信用狀約定書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  8, '公司登記事項卡', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW',  9, '公司變更登記表(董監事任期建檔)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 10, '身份證遺失檢查', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 11, '查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料', '(本項目自100.09.01起加註借款人及保證人。原項目為：司法院『監護、輔助宣告』查詢紀錄)', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 12, '恐怖份子黑名單查詢紀錄', '請用TABS 交易代號 0015-11查詢', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 13, '銀行法及金控法利害關係人查詢紀錄(簽約時需再查一次)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 14, '同一關係企業/集團企業建檔維護', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 15, '應收帳款承購無追索權--買方,銀行法及金控法利害關係人查詢紀錄', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 16, '調閱借款人「台灣投審會核准投資大陸之核准函」', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 17, '核貸條件其他應辦事項完成（例如：土地信託、地上權設定、具體開發計畫及時程、徵提承諾書或其他切結等）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 18, '檢視境外公司法人資格是否存續（例如：徵提客戶繳交當地政府規費收據，或代辦機構出具之繳費證明）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 19, '不動產抵押同意書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 20, '擔保借款抵押權設定種類約款', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 21, '擔保品投保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 22, '擔保品設定建檔', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 23, '授信約定書對保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_TW', 24, '連帶保證書對保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);

-- en --
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  1, '核准敘做文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  2, '綜合授信契約', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  3, '本票（額度）', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  4, '授信約定書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  5, '連帶保證書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  6, '公司董事會決議錄', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  7, '開發信用狀約定書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  8, '公司登記事項卡', '', '005097',CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en',  9, '公司變更登記表(董監事任期建檔)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 10, '身份證遺失檢查', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 11, '查詢司法院網站借款人及保證人是否為受監護或受輔助宣告資料', '(本項目自100.09.01起加註借款人及保證人。原項目為：司法院『監護、輔助宣告』查詢紀錄)', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 12, '恐怖份子黑名單查詢紀錄', '請用TABS 交易代號 0015-11查詢', '005097', CURRENT TIMESTAMP, '005097',CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 13, '銀行法及金控法利害關係人查詢紀錄(簽約時需再查一次)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 14, '同一關係企業/集團企業建檔維護', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 15, '應收帳款承購無追索權--買方,銀行法及金控法利害關係人查詢紀錄', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 16, '調閱借款人「台灣投審會核准投資大陸之核准函」', '', '005097', CURRENT TIMESTAMP, '005097',CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 17, '核貸條件其他應辦事項完成（例如：土地信託、地上權設定、具體開發計畫及時程、徵提承諾書或其他切結等）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 18, '檢視境外公司法人資格是否存續（例如：徵提客戶繳交當地政府規費收據，或代辦機構出具之繳費證明）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 19, '不動產抵押同意書', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 20, '擔保借款抵押權設定種類約款', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 21, '擔保品投保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 22, '擔保品設定建檔', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 23, '授信約定書對保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'en', 24, '連帶保證書對保', '', '005097',CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);

-- zh_CN --
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  1, '核准叙做文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  2, '综合授信契约', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  3, '本票（额度）', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  4, '授信约定书', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  5, '连带保证书', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  6, '公司董事会决议录', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  7, '开发信用状约定书', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  8, '公司登记事项卡', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN',  9, '公司变更登记表(董监事任期建档)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 10, '身份证遗失检查', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 11, '查询司法院网站借款人及保证人是否为受监护或受辅助宣告数据', '(本项目自100.09.01起加注借款人及保证人。原项目为：司法院『监护、辅助宣告』查询纪录)', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 12, '恐怖份子黑名单查询纪录', '请用TABS 交易代号 0015-11查询', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 13, '银行法及金控法利害关系人查询纪录(签约时需再查一次)', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 14, '同一关系企业/集团企业建档维护', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 15, '应收帐款承购无追索权--买方,银行法及金控法利害关系人查询纪录', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 16, '调阅借款人「台湾投审会核准投资大陆之核准函」', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 17, '核贷条件其他应办事项完成（例如：土地信托、地上权设定、具体开发计划及时程、征提承诺书或其他切结等）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 18, '检视境外公司法人资格是否存续（例如：征提客户缴交当地政府规费收据，或代办机构出具之缴费证明）之文件', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 19, '不动产抵押同意书', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 20, '担保借款抵押权设定种类约款', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 21, '担保品投保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 22, '担保品设定建档', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 23, '授信约定书对保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);
insert into LMS.L901M01A(OID, BRANCHID, ITEMTYPE, LOCALE, ITEMSEQ, ITEMCONTENT, ITEMMEMO, CREATOR, CREATETIME, UPDATER, UPDATETIME) values (GET_OID(), '918', '1', 'zh_CN', 24, '连带保证书对保', '', '005097', CURRENT TIMESTAMP, '005097', CURRENT TIMESTAMP);

