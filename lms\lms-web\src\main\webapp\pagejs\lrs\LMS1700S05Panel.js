var initDfd = initDfd || new $.Deferred();
var initAll = initAll || new $.Deferred();

initDfd.done(function(json){
	 
	//J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	if( json.lock ){		
		$("#addFiles").addClass(" ui-state-disabled ").prop("disabled", "true");
		$("#deleteFiles").addClass(" ui-state-disabled ").prop("disabled", "true");
	}else{
		if(json['initControl_lockDoc']){
			//$("#addFiles").addClass(" ui-state-disabled ").attr("disabled", "true");
			$("#deleteFiles").addClass(" ui-state-disabled ").attr("disabled", "true");
		}
	}
	
	
	
	//檔案上傳grid
    var gridfile = $("#gridfile").iGrid({
        handler: 'lms1700gridhandler',
        height: 80,
        postData: {
            formAction: "queryfile",
            fieldId: "lrs",
            mainId: json.mainId
        },
        rowNum: 15,
        multiselect: true,
        colModel: [{
            colHeader: i18n.def['uploadFile.srcFileName'],//檔案名稱,
            name: 'srcFileName',
            width: 120,
            align: "left",
            sortable: true,
            formatter: 'click',
            onclick: download
        }, {
            colHeader: i18n.def['uploadFile.srcFileDesc'],//檔案說明
            name: 'fileDesc',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.def['uploadFile.uploadTime'],//上傳時間
            name: 'uploadTime',
            width: 140,
            align: "center",
            sortable: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });	
    
    
    if(true){//附加檔案
		//加入檔案按鈕
		$("#addFiles").click(function(){
			//J-110-0304_05097_B1003 Web e-Loan授信覆審配合RPA作業修改
			var limitFileSize = 3145728*100;   //3M * 100 = 300M
	        MegaApi.uploadDialog({
	            fieldId: "lrs",
	            fieldIdHtml: "size='30'",
	            fileDescId: "fileDesc",
	            fileDescHtml: "size='30' maxlength='30'",
	            subTitle: i18n.def('insertfileSize', {
	                'fileSize': (limitFileSize / 1048576).toFixed(2)
	            }),
	            limitSize: limitFileSize,
	            width: 320,
	            height: 190,
	            data: {
	                mainId: $("#mainId").val(),
	                sysId: "LMS"
	            },
	            success: function(){
	            	$("#gridfile").trigger("reloadGrid");
	            }
	        });
	        
		});
		
		//刪除檔案按鈕
	    $("#deleteFiles").click(function(){
	        var select = $("#gridfile").getGridParam('selarrrow');
	        if (select == "") {
	            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
	            return;
	        }
	        
	        // confirmDelete=是否確定刪除?
	        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
	            if (b) {
	                var data = [];
	                for (var i in select) {
	                	var oid = $("#gridfile").getRowData(select[i]).oid ;

	                	$.ajax({
		                    handler: _handler,
		                    data: {
		                        formAction: "deleteUploadFile",
		                        'fileOid': oid
		                    },
						}).done(function(obj){
							$("#gridfile").trigger("reloadGrid");
						});
	                }
	            } 
	        });
	    });	
	}
 
    //檔案下載
    function download(cellvalue, options, data){
        $.capFileDownload({
            handler: "simplefiledwnhandler",
            data: {
                fileOid: data.oid
            }
        });
    }	
});
