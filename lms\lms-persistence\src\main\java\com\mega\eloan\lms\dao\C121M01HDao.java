/* 
 * C121M01HDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01H;

/** 泰國消金評等表 **/
public interface C121M01HDao extends IGenericDao<C121M01H> {

	public C121M01H findByOid(String oid);
	public List<C121M01H> findByMainId(String mainId);
	public List<C121M01H> findByC121M01A(C121M01A meta);
	public C121M01H findByUk(String mainId, String custId, String dupNo);
	public C121M01H findByC120M01A(C120M01A c120m01a);
}