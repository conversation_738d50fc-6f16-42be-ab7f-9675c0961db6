<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
        <th:block th:fragment="innerPageBody">
        	<script type="text/javascript"> 
     			loadScript('pagejs/cls/CLS3501M01Page');
 			</script>
        
            <div class="button-menu funcContainer" id="buttonPanel">
                <!--編製中 -->
                <th:block th:if="${_btnDOC_EDITING_visible}">
                    <button id="btnSave">
                        <span class="ui-icon ui-icon-jcs-04"></span>
                        <th:block th:text="#{'button.save'}">儲存</th:block>
                    </button>
                    <button id="btnSend" >
                        <span class="ui-icon ui-icon-jcs-02"></span>
                        <th:block th:text="#{'button.send'}">呈主管覆核</th:block>
                    </button>
                </th:block>

                <!--待覆核 -->
                <th:block th:if="${_btnWAIT_APPROVE_visible}">
                    <button id="btnCheck" >
                        <span class="ui-icon ui-icon-jcs-106"></span>
                        <th:block th:text="#{'button.check'}">覆核</th:block>
                    </button>
                </th:block>

                <button id="btnPrint" class="forview">
                    <span class="ui-icon ui-icon-jcs-03"></span>
                    <th:block th:text="#{'button.print'}">列印</th:block>
                </button>
                <button id="btnExit" class="forview">
                    <span class="ui-icon ui-icon-jcs-01"></span>
                    <th:block th:text="#{'button.exit'}">離開</th:block> 
                </button>
            </div>
            <div class="tit2 color-black">
                <th:block th:text="#{'title'}"></th:block> - <span id="showCustId" class="color-blue"></span>
            </div>
            <form id="mainPanel">
                <span id="mainId" style="display:none"></span>
                <span id="custId" style="display:none"></span>
                <span id="dupNo" style="display:none"></span>
                <table class='tb2' width='100%'>
                    <tr>
                        <td colspan='2' class='hd2'>
                            <th:block th:text="#{'C124M01A.approveNo'}">案件編號</th:block>：<span id='approveNo'></span>
                        </td>
                        <td colspan='2' class='hd2'>
                            <th:block th:text="#{'C124M01A.grntPaper'}">保證案號</th:block>：<span id='grntPaper'></span>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.agreement'}">聲明</th:block> 
                        </td>
                        <td width="32%" class="notUse">
                            <label><input type="radio" id="agreement" name="agreement" value="1" checked/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.isCreditCheck'}">辦妥徵信</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label><input type="radio" id="isCreditCheck" name="isCreditCheck" value="1" checked/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.qualiCode'}">保證對象資格</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label><input type="radio" id="qualiCode" name="qualiCode" value="1" checked/><th:block th:text="#{'yes'}">是</th:block>&nbsp;</label>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.isCreditAbnormal'}">申請人之票、債信情形</th:block>
                        </td>
                        <td width="32%" class="notUse">
                            <label><input type="radio" id="isCreditAbnormal" name="isCreditAbnormal" value="2" checked/><th:block th:text="#{'noHave'}">無</th:block>&nbsp;</label>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.borrowerBirthDay'}">借款人出生日期</th:block>
                        </td>
                        <td colspan='3'>
                            <input type="text" id="borrowerBirthDay" name="borrowerBirthDay" class="date required" size="11" maxlength="10"/>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.applyLoanDay'}">申貸受理日</th:block>
                        </td>
                        <td width="32%">
                            <input type="text" id="applyLoanDay" name="applyLoanDay" class="date required" size="11" maxlength="10"/>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.applyLoan'}">申請額度</th:block>
                        </td>
                        <td width="32%">
                            <input type='text' id='applyLoan' name='applyLoan' size="14" maxlength="14" integer="14" fraction="0" class="numeric required"/>元
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.tel'}">聯絡電話</th:block>
                        </td>
                        <td width="32%">
                            <input type='text' id='tel1' name='tel1' size="2" maxlength="2" class="phone"/>
                            - <input type='text' id='tel2' name='tel2' size="8" maxlength="8" class="phone"/>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.ownerCellphone'}">申請人行動電話</th:block>
                        </td>
                        <td width="32%">
                            <input type='text' id='ownerCellphone1' name='ownerCellphone1' size="4" maxlength="4" class="phone"/>
                            - <input type='text' id='ownerCellphone2' name='ownerCellphone2' size="6" maxlength="6" class="phone"/>
                            <br>
                            ex. 0912-345678
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.isEmail'}">有無電子郵箱</th:block>
                        </td>
                        <td width="32%">
                            <input type="radio" id="isEmail" name="isEmail" class="required"/>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.email'}">個人電子郵箱</th:block>
                        </td>
                        <td width="32%">
                            <input type="text" id="email" name="email" class="email" maxlength="50"/>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.address'}">地址</th:block>
                        </td>
                        <td colspan='3' width="32%">
                            <!--<span id='zip'/>-->
                            <input type="text" id="zip" name="zip" size="5" maxlength="5" class="digits required"/>
                            &nbsp;<select id="city" name="city" class="required"></select><select id="dist" name="dist" class="required"></select>
                            <br>
                            <input type="text" id="villageName" name="villageName" size="20" maxlength="50"/>
                            <select id="village" name="village">
                                <option value=""><th:block th:text="#{'checkSelect'}">請選擇</th:block></option>
                                <option value="1"><th:block th:text="#{'village_1'}">里</th:block></option>
                                <option value="2"><th:block th:text="#{'village_2'}">村</th:block></option>
                            </select>，
                            <input type="text" id="neighborhood" name="neighborhood" size="4" maxlength="4" class="digits"/>
                            鄰
                            <br>
                            <input type="text" id="roadName" name="roadName" size="20" maxlength="20" class="required"/>
                            <select id="road" name="road">
                                <option value="1"></option>
                                <option value="2"><th:block th:text="#{'road_2'}">路</th:block></option>
                                <option value="3"><th:block th:text="#{'road_3'}">街</th:block></option>
                            </select>，
                            <input type="text" id="sec" name="sec" size="4" maxlength="4"/>段，
                            <input type="text" id="lane" name="lane" size="8" maxlength="8"/>巷，
                            <input type="text" id="alley" name="alley" size="8" maxlength="8"/>弄，
                            <input type="text" id="no1" name="no1" size="4" maxlength="4" class="required"/>之
                            <input type="text" id="no2" name="no2" size="4" maxlength="4"/>號，
                            <br>
                            <input type="text" id="floor1" name="floor1" size="3" maxlength="3"/>樓之
                            <input type="text" id="floor2" name="floor2" size="3" maxlength="3"/>(
                            <input type="text" id="room" name="room" size="3" maxlength="3"/>室)
                            <br>
                            <span class="color-red">註：XX市．．．XX弄 A之B號，倘「B」欄位無資料，門號請填在「A」欄位</span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan='4' class='hd2' align="center">
                            <th:block th:text="#{'C124M01A.importer'}">申請書鍵入者</th:block>
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.importerN'}">員編 / 姓名</th:block>
                        </td>
                        <td width="32%">
                            <select id="importerNo" name="importerNo" class="required"></select>
                            <input type="text" id="importerName" name="importerName" maxlength="10" style="display:none"/>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.importerCellphone'}">行動電話</th:block>
                        </td>
                        <td width="32%">
                            <input type='text' id='importerCellphone1' name='importerCellphone1' size="4" maxlength="4" class="phone required"/>
                            - <input type='text' id='importerCellphone2' name='importerCellphone2' size="6" maxlength="6" class="phone required"/>
                            <br>
                            ex. 0912-345678
                        </td>
                    </tr>
                    <tr>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.importerTel'}">電話</th:block>
                        </td>
                        <td width="32%">
                            <input type='text' id='importerTel1' name='importerTel1' size="2" maxlength="2" class="phone required"/>
                            - <input type='text' id='importerTel2' name='importerTel2' size="8" maxlength="8" class="phone required"/>
                            <th:block th:text="#{'C124M01A.importerTelExt'}">分機</th:block>
                            <input type='text' id='importerTelExt' name='importerTelExt' size="5" maxlength="5" class="phone required"/>
                        </td>
                        <td width="18%" class="hd1">
                            <th:block th:text="#{'C124M01A.importerEmail'}">電子信箱</th:block>
                        </td>
                        <td width="32%">
                            <input type="text" id="importerEmail" name="importerEmail" class="email required" maxlength="50"/>
                        </td>
                    </tr>
                </table>
            </form>

            <div id="docPanel">
                <fieldset>
                    <legend>
                        <b><th:block th:text="#{'doc.docUpdateLog'}">文件異動紀錄</th:block></b>
                    </legend>
                    <div class="funcContainer">
                        <div class="funcContainer">文件異動紀錄<div th:insert="common/panels/DocLogPanel :: DocLogPanel"/></div>
                    </div>
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody>
                        <tr>
                            <td width="35%" class="hd1">
                                <th:block th:text="#{'doc.creator'}">文件建立者</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="15%">
                                <span id='creator'></span>(<span id='createTime'></span>)
                            </td>
                            <td width="30%" class="hd1">
                                <th:block th:text="#{'doc.lastUpdater'}">最後異動者</th:block>&nbsp;&nbsp;
                            </td>
                            <td width="20%">
                                <span id='updater'></span>(<span id='updateTime'></span>)
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                            </td>
                            <td>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'doc.docCode'}">報表亂碼</th:block>&nbsp;&nbsp;
                            </td>
                            <td>
                                <span id="randomCode"></span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </fieldset>

                <fieldset>
                    <div id="tabs-appr" class="tabs" style='width:99%;'>
                        <ul>
                            <li>
                                <a href="#tabs-appr01"><b><th:block th:text="#{'C124M01B.title01'}"></th:block></b></a>
                            </li>
                        </ul>
                        <div class="tabCtx-warp">
                            <div id="tabs-appr01" class="content">
                                <table width="100%">
                                    <tr>
                                        <td width="12%" class="rt">
                                            <b class="text-red">
                                                <th:block th:text="#{'C124M01B.managerId'}">經副襄理</th:block>：
                                            </b>
                                        </td>
                                        <td width="12%" class="lt">
                                            <span id="managerId"></span>
                                        </td>
                                        <td width="12%" class="rt">
                                            <b class="text-red">
                                                <th:block th:text="#{'C124M01B.bossId'}">授信主管</th:block>：
                                            </b>
                                        </td>
                                        <td width="12%" class="lt">
                                            <span id="bossId"></span>
                                        </td>
                                        <td width="12%" class="rt">
                                            <b class="text-red">
                                                <th:block th:text="#{'C124M01B.reCheckId'}">覆核主管</th:block>：
                                            </b>
                                        </td>
                                        <td width="12%" class="lt">
                                            <span id="reCheckId"></span>
                                        </td>
                                        <td width="12%" class="rt">
                                            <b class="text-red">
                                                <th:block th:text="#{'C124M01B.apprId'}">經辦</th:block>：
                                            </b>
                                        </td>
                                        <td width="12%" class="lt">
                                            <span id="showApprId"></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>

            <div id="openCheckBox" style="display:none">
                <div>
				<span id="check1" style="display:none">
				 	<label><input name="checkRadio" type="radio" value="3"><th:block th:text="#{'accept'}">核准</th:block></label><br/>
					<label><input name="checkRadio" type="radio" value="1"><th:block th:text="#{'back'}">退回經辦修改</th:block></label>
				</span>
                </div>
            </div>
            <div id="selectBossBox"  style="display:none;">
                <form id="selectBossForm">
                    <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="hd1" width="60%"><th:block th:text="#{'C124M01B.selectBoss'}">授信主管人數</th:block>&nbsp;&nbsp;</td>
                            <td width="40%"><select id="numPerson" name="numPerson">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                            </select>
                            </td>
                        </tr>
                        <tr >
                            <td class="hd1" ><th:block th:text="#{'C124M01B.bossId'}">授信主管</th:block>&nbsp;&nbsp;</td>
                            <td >
                                <div id="bossItem"></div>
                            </td>
                        </tr>
                        <tr >
                            <td class="hd1"><th:block th:text="#{'C124M01B.managerId'}">經副襄理</th:block>&nbsp;&nbsp;</td>
                            <td><div id="managerItem"></div></td>
                        </tr>
                    </table>
                </form>
            </div>
        </th:block>
    </body>
</html>
