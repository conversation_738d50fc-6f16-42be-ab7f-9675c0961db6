
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.lms.mfaloan.bean.ELF491;
import com.mega.eloan.lms.mfaloan.service.MisELF491Service;

/**
 * <pre>
 * 覆審控制檔
 * </pre>
 * 
 * @since 2013/3/7
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/3/7,EL08034,new
 *          </ul>
 */
@Service
public class MisELF491ServiceImpl extends AbstractMFAloanJdbc implements
MisELF491Service {

	@Override
	public ELF491 findByPk(String elf491_branch, String elf491_custid,
			String elf491_dupno) {
		List<Map<String, Object>> rowData = this.getJdbc().queryForList("ELF491.selByPk", new String[]{elf491_branch,elf491_custid,elf491_dupno});
		List<ELF491> list = toELF491(rowData);
		if(list.size()==1){
			return list.get(0);
		}else{
			return null;
		}		
	}
			
	@Override
	public List<Map<String, Object>> selNewCaseForELF498(
			String elf490_data_ym_beg, String elf490_data_ym_end,
			String brNo) {
		return this.getJdbc().queryForListWithMax("ELF491.selNewCaseForELF498", new String[]{elf490_data_ym_beg,elf490_data_ym_end,brNo});
	}
	
	@Override
	public List<ELF491> selByBranch_activeCrDate(String brNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491.selByBranch_activeCrDate", new String[]{brNo});
		return toELF491(rowData);
	}
	
	@Override
	public ELF491 selWithCustIdXXXX_8_1(String brno){
		return findByPk(brno, "XXXXXXXXXX", "X");
	}
	
	@Override
	public ELF491 selWithCustId_AAAAAAAAAA___dupNo_A(String brno){
		return findByPk(brno, "AAAAAAAAAA", "A");
	}
	
	@Override
	public ELF491 selWithCustId_DDDDDDDDDD___dupNo_D(String brno){
		return findByPk(brno, "DDDDDDDDDD", "D");
	}
	
	@Override
	public int update_R14(String custId, String dupNo, String newCustId, String newDupNo){
		return this.getJdbc().update("ELF491.update_R14", new Object[]{newCustId, newDupNo, custId, dupNo});
	}
	
	@Override
	public int delete_R14(String custId, String dupNo){
		return this.getJdbc().update("ELF491.delete_R14", new Object[]{custId, dupNo});
	}
	
	@Override
	public int selC240M01A_EffectiveCount(String brno){
		Map<String, Object> row = getJdbc().queryForMap("ELF489.selC240M01A_EffectiveCount",
				new String[] { brno });
		return MapUtils.getIntValue(row, "TOTALCOUNT");
	}
	
	@Override
	public Set<String> sel_custIdSet_hasLN(String brNo){
		Set<String> r = new HashSet<String>();
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491.sel_custIdSet_hasLN"
				, new String[]{brNo, brNo+"%",brNo});
		for(Map<String, Object> row : rowData) {
			r.add(Util.trim(row.get("LNF020_CUST_ID")));
		}
		return r;
	}
	
	@Override
	public List<Map<String, Object>> sel_whenProduce(String brNo, Date dataEndDate, String elf491_newflag){
		return this.getJdbc().queryForListWithMax("ELF491.sel_whenProduce", new String[]{brNo, TWNDate.toAD(dataEndDate), elf491_newflag});		
	}

	@Override
	public List<Map<String, Object>> selRuleNo8_1(String brNo) {
		return this.getJdbc().queryForListWithMax("ELF489.selByBrno_ELF489_RULE_NO_8_1", new String[]{brNo });
	}
	
	@Override
	public List<ELF491> selByR98_activeCrDate(){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491.selByR98_activeCrDate", null);
		return toELF491(rowData);
	}
	
	@Override
	public List<Map<String, Object>> sel_gfnGetWillOverReviewData(String elf491_branch, String elf491_crDate_s, String elf491_crDate_e){
		return this.getJdbc().queryForListWithMax("ELF491.sel_gfnGetWillOverReviewData"
				, new String[]{elf491_branch, elf491_crDate_s, elf491_crDate_e });
	}
	
	@Override
	public List<ELF491> selByCustIdDupNo(String custId, String dupNo){
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491.selByCustIdDupNo", new String[]{custId, dupNo});
		return toELF491(rowData);
	}
	
	@Override
	public List<ELF491> chkR1R2R4(String elf491_branch, String elf491_custid, String elf491_lrDate){
		String passDate = elf491_lrDate;
		if(Util.isNotEmpty(elf491_custid) && Util.trim(elf491_custid).length()>=8){
			passDate = TWNDate.toAD(new TWNDate().toDate());
		}
		List<Map<String, Object>> rowData = this.getJdbc().queryForListWithMax("ELF491.chkR1R2R4"
				, new String[]{elf491_branch+"%", elf491_custid+"%", passDate });
		return toELF491(rowData);
	}
	
	@Override
	public List<Map<String, Object>> sel_gfnGenerateCTL_FLMS180R15(String date_s, String date_e){
		return this.getJdbc().queryForListWithMax("ELF491.gfnGenerateCTL_FLMS180R15"
				, new String[]{date_s, date_e, date_s, date_e });
	}
	
	@Override
	public List<Map<String, Object>> proc_crossMonth_loanData(String prevRocDataYM, String baseRocDataYM, String strCmpCrDate){
		return this.getJdbc().queryForListWithMax("ELF491.proc_crossMonth_loanData"
				, new String[]{prevRocDataYM, baseRocDataYM, strCmpCrDate });
	}
	
	@Override
	/**
	 * 當每月客戶還款金額低於門檻時
	 * 修正RULE 8-2=>8-1
	 */
	public int update_remom_from_8_2_to_8_1(){
		return this.getJdbc().update("ELF491.update_remom_from_8_2_to_8_1", new Object[] {});
	}
	
	private List<ELF491> toELF491(List<Map<String, Object>> rowData){
		List<ELF491> list = new ArrayList<ELF491>();
		for (Map<String, Object> row : rowData) {
			ELF491 model = new ELF491();
			DataParse.map2Bean(row, model);
			list.add(model);
		}
		return list;
	}

	@Override
	public List<ELF491> selByBranch_activeCrDate(String[] branchs, String sDate, String eDate) {
		List<Map<String, Object>> rowData = new ArrayList<Map<String, Object>>();
//		StringBuffer temp = new StringBuffer();
//		for (String brNo : branchs) {
//			if (Util.isNotEmpty(Util.trim(brNo))) {
//				temp.append(temp.length() > 0 ? "," : "");
//				temp.append("'");
//				temp.append(brNo);
//				temp.append("'");
//			}
//		}
		if(branchs != null && branchs.length > 0 ){
			String branchSqlParam = Util.genSqlParam(branchs);
			List<Object> params = new ArrayList<Object>();
			params.addAll(Arrays.asList(branchs));
			params.add(sDate);
			params.add(eDate);
			rowData = this.getJdbc().queryForListByCustParam("ELF491.selByBranch_activeCrDate_byBranch",
					new Object[] { branchSqlParam },
					params.toArray(new Object[0]));
		}
		return toELF491(rowData);
	}
}
