<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
	<body>
		<th:block th:fragment="innerPageBody">
			<center><b><font size="4"><span id="msg">報表繪製中 ，請稍候</span></font></b>&nbsp;&nbsp;&nbsp;&nbsp;<img id="loader"></center>
			<script>
				$(function(){
//					$('#headerarea').hide();
					$('#msg').html(i18n.def['printPrcess'] || '報表繪製中 ，請稍候');
//					alert( location.href    ); // 完整的網址  
//					alert( location.protocol); // 協定　　　　　　 http:  
//					alert( location.hostname); // 伺服器名稱　　　 blog.xuite.net  
//					alert( location.host    ); // 伺服器:埠號　　　blog.xuite.net:80  
//					alert( location.port    ); // 埠號　　　　　　 80  
//					alert( location.pathname); // host之後的部份  /ahdaa/blog1/test.html?id=AD&val1=02&val2=22#achorAD  
//					alert( location.search  ); // 含?之後所有字串　?id=AD&val1=02&val2=22#achorAD  
//					alert( location.hash    ); // 含#之後所有字串　#achorAD(通常用於錨點)  

					var pathnames = location.pathname.split("\/");  
//					alert(pathnames[1]);
					
					if(responseJSON.isTwoPhase == "Y" && responseJSON.rptOid){
						/*
						 若用 $(window).bind("beforeunload", function(){
						 → 在 Firefox ok，但IE不行
						 	 
						 window.onbeforeunload = function() {
						     
						 };						
						 */
						
						var my_timeout = 7200000;//ms
						var target = [];
         	            var batchCnt = 20;
         	            if(true){
							var oid_arr = responseJSON.rptOid[0].split("|");
							var first_oid = oid_arr[0];
         	            	var last_oid = oid_arr[oid_arr.length-1];
         	            	var arr_len = oid_arr.length;
         	            	var isBreak = false;
         	            	for(var i = 0; isBreak==false; i++){
         	            	   var beg = i*batchCnt;
	         	               var end = (i+1)*batchCnt;
	         	            		 
	         	               if(end>=arr_len){
	         	            	  isBreak = true;
	         	               }
	         	               target.push( $( oid_arr ).slice( beg, end) );
         	            	}
         	            	if(true){
         	                    var theQueue = $({});								
         	                	var result_pdf_loc = [];
         	                	$.each(target,function(idx) {
         	                		theQueue.queue('myqueue', function(next) {
   	                				  var current_arr = target[idx].get();
									  if(true){//先延長 timer，不然在處理過程中，會 timeout
						        		timer_long_ajax_beg(my_timeout);	
						        	  }
									  $.ajax({
				                       handler: 'lms1800formhandler', action: "callBatch", timeout: my_timeout,
				                       data: $.extend(
										   {'act':'gen_file_for_download'
				                          , 'jq_timeout': (my_timeout/1000)
										   }
										   , responseJSON
										   , {'rptOid': current_arr.join("|") }
									   ), success: function(json_callBatch){
											
											if(true){//恢復 timer
							               		timer_long_ajax_end();
							  				}
												
											var zfile_loc = json_callBatch.zfile_loc;
											if(zfile_loc){
												result_pdf_loc.push(zfile_loc);												
											}	
											
											if((idx+1)==target.length){
												if(window.opener && window.opener['childWin_complete'] ){
													window.opener.childWin_complete(responseJSON.targetWinId);
												}
												if(result_pdf_loc.length > 0){
													$.form.submit({
													url: "FileProcessingPageByLoc?random=" + Math.random(),
							            			data: $.extend(responseJSON
														, {
															'noOpenDoc': true,
															'file_loc': result_pdf_loc.join("|")
														} )
							       					});	
												}													
											}	
											//----------		
         	            					next();				                            	
				                    	}				                        
             	            			
         	                		   });
									 });                                            			 
         	                	});
         	                	theQueue.dequeue('myqueue');
         	                }
         	            }
						
					}else{
						$.form.submit({
							url: "FileProcessingPage?random=" + Math.random(),
				            data: $.extend(responseJSON,{noOpenDoc:true})
				        });	
					}					
					
					$("#loader").attr("src","/" + pathnames[1] + "/img/ajax-loader2.gif");
				});
			</script>
		</th:block>
	</body>
</html>
