package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,jessica,new
 *          </ul>
 */
public interface MisEllnseekservice {

	/**
	 * 3. 信保案件未動用屆期清單 (找登入所在的分行此筆資料)
	 * 
	 * @param ovUnitNo
	 * @param baseDateStr
	 * @param date
	 * @return
	 */
	List<Map<String, Object>> findMisEllnseekforNewReportType3ByBrNo(
			String benDate, String endDate, String ovUnitNo);

	/**
	 * 查詢 MIS.ELLNSEEK
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return 查詢清單
	 */
	List<Map<String, Object>> findByKey(String custId, String dupNo,
			String cntrNo);

	/**
	 * 新增 上傳額度序號狀態至MIS.ELLNSEEK 提供總處查詢
	 * 
	 * @param custId
	 *            客戶統編 (10)
	 * @param dupNo
	 *            重覆序號(1)
	 * @param cntrNo
	 *            額度序號 (12)
	 * @param brNo
	 *            分行代號 (3)
	 * @param status
	 *            文件狀態 (1)
	 * @param apprYY
	 *            年份 (4)
	 * @param apprMM
	 *            月份(2)
	 * @param cType
	 *            (1)
	 * @param updater
	 *            (8) 更新者
	 * @param gutcDate
	 *            (10) 信保日期
	 * @param proJno
	 *            (42) 案號
	 * @param property
	 *            (2) 性質
	 */
	public void insert(String custId, String dupNo, String cntrNo, String brNo,
			String status, String apprYY, String apprMM, String cType,
			String updater, String gutcDate, String proJno, String property,
			String byNewOld, String lnuseNo, String useFmDt, String useEnDt,
			int useFtMn, String hasAmFee, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String ELF461_ISREVIVE_Y,
			String ELF461_MAINID, String ELF461_ISOFCLCGA,
			String ELF461_CGA_COUNTRY, String ELF461_CGA_CRDTYPE,
			String ELF461_CGA_CRDAREA, String ELF461_CGA_CRDPRED,
			String ELF461_CGA_CRDGRAD, BigDecimal ELF461_CGA_RSKRTO,
			BigDecimal ELF461_CGA_GRADSCR, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk, String lnType,
			String yoPurpose, String subSidyut, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3,
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL);

	/**
	 * 更新 上傳額度序號狀態至MIS.ELLNSEEK 提供總處查詢
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param brNo
	 *            分行代號
	 * @param status
	 *            文件狀態
	 * @param apprYY
	 *            年份
	 * @param apprMM
	 *            月份
	 * @param cType
	 * @param updater
	 *            更新者
	 * @param gutcDate
	 *            信保日期
	 * @param proJno
	 *            案號
	 * @param property
	 *            性質
	 */
	public void update(String custId, String dupNo, String cntrNo, String brNo,
			String status, String apprYY, String apprMM, String cType,
			String updater, String gutcDate, String proJno, String property,
			String byNewOld, String lnuseNo, String useFmDt, String useEnDt,
			int useFtMn, String hasAmFee, String experf_fg, String flaw_fg,
			BigDecimal flaw_amt, String ELF461_ISREVIVE_Y,
			String ELF461_MAINID, String ELF461_ISOFCLCGA,
			String ELF461_CGA_COUNTRY, String ELF461_CGA_CRDTYPE,
			String ELF461_CGA_CRDAREA, String ELF461_CGA_CRDPRED,
			String ELF461_CGA_CRDGRAD, BigDecimal ELF461_CGA_RSKRTO,
			BigDecimal ELF461_CGA_GRADSCR, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk, String lnType,
			String yoPurpose, String subSidyut, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3,
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL);

	public void updateGutData(String custId, String dupNo, String cntrNo,
			String gutCutDate, String byNewOld, String property, String projNO);

	public void updateOnlyRevive(String custId, String dupNo, String cntrNo,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID);

	public void updateOnlySpecialFinRisk(String custId, String dupNo,
			String cntrNo, String timeStamp, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk,
			String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult);

	public void insertOnlySpecialFinRisk(String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String updater, String isSpecialFinRisk,
			String specialFinRiskType, String isCmsAdcRisk,
			String isProjectFinOperateStag, 
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult);

	/**
	 * J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param isOperationFee
	 * @param operationFeeCurr
	 * @param operationFeeAmt
	 * @param operationFeeDueDate
	 */
	public void updateOperationFee(String custId, String dupNo, String cntrNo,
			String isOperationFee, String operationFeeCurr,
			BigDecimal operationFeeAmt, String operationFeeDueDate);
}
