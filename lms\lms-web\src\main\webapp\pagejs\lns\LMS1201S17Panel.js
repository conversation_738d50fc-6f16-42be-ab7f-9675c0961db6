var _handler = "";
$(document).ready(function() {
	setCloseConfirm(true);
	if(responseJSON.docURL == "/lms/lms1205m01"){
		// 授權外企金
		_handler = "lms1201formhandler";
		_iHandler = "lms1201formhandler";
	}else if(responseJSON.docURL == "/lms/lms1105m01"){
		//授權內企金
		_handler = "lms1105formhandler";
		_iHandler = "lms1105formhandler";
	}else if(responseJSON.docURL == "/lms/lms1215m01"){
		_handler = "lms1215formhandler";
		_iHandler = "lms1215formhandler";
	}else if(responseJSON.docURL == "/lms/lms1115m01"){
		_handler = "lms1115formhandler";
		_iHandler = "lms1115formhandler";
	}else{
		_handler = "lms1305formhandler";
		_iHandler = "lms1305formhandler";
	}
	//上傳檔案按鈕
	$("#uploadFileA").click(function(){
		var limitFileSize=3145728;
		MegaApi.uploadDialog({
			fieldId:"upFileA",
            fieldIdHtml:"size='30'",
            fileDescId:"fileDesc",
            fileDescHtml:"size='30' maxlength='30'",
			subTitle:i18n.def('insertfileSize',{'fileSize':(limitFileSize/1048576).toFixed(2)}),
            limitSize:limitFileSize,
			width:320,
            height:190,			
			data:{
				mainId:$("#mainId").val()				
			},
			success : function(obj) {
				$("#gridfileA").trigger("reloadGrid");
			}
	   });
	});
	
	//刪除檔案按鈕
	$("#deleteFileA").click(function(){
		var select  = $("#gridfileA").getGridParam('selrow');		
		// confirmDelete=是否確定刪除?
		CommonAPI.confirmMessage(i18n.def["confirmDelete"],function(b){
			if(b){				
				var data = $("#gridfileA").getRowData(select);
				if(data.oid == "" || data.oid == undefined || data.oid == null){		
					// TMMDeleteError=請先選擇需修改(刪除)之資料列
					CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
					return;
				}				
				$.ajax({
					handler : _handler,
					type : "POST",
					dataType : "json",
					data : {
						formAction : "deleteUploadFile",
						fileOid:data.oid
					},
					success : function(obj) {
						$("#gridfileA").trigger("reloadGrid");
					}
				});
			}else{
				return ;
			}
		});
	});
	
	//檔案上傳grid
	$("#gridfileA").iGrid({
		handler : 'lms1201gridhandler',
		height : 150,
		sortname : 'srcFileName',
		postData : {
			formAction : "queryfile",
			fieldId:"upFileA",
			mainId:responseJSON.mainId
		},
		rowNum : 15,
		caption: "&nbsp;",
		hiddengrid : false,
		//expandOnLoad : true,	//只對subgrid有用
		//multiselect : true,
		colModel : [ {
			colHeader :i18n.lms1201s17['l120m01a.srcFileName'],//原始檔案名稱,
			name : 'srcFileName',
			width : 120,
			align: "left",
			sortable : false,
			formatter : 'click',
			onclick : openDoc
		}, {
			colHeader : i18n.lms1201s17['l120m01a.fileDesc'],//檔案說明
			name : 'fileDesc',
			width : 140,
			sortable : false
		}, {
			colHeader : i18n.lms1201s17['l120m01a.uploadTime'],//上傳時間
			name : 'uploadTime',
			width : 140,
			sortable : false
		}, {
			name : 'oid',
			hidden : true
		}]
	});
});

function openDoc(cellvalue, options, rowObject){
    $.capFileDownload({
        handler:"simplefiledwnhandler",
        data : {
            fileOid:rowObject.oid
        }
    });
}