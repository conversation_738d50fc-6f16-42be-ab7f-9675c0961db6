package com.mega.eloan.lms.lms.handler.form;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.constants.SysParamConstants;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.formatter.BranchDateTimeFormatter;
import com.mega.eloan.common.gwclient.EloanBatchClient;
import com.mega.eloan.common.gwclient.EloanSubsysBatReqMessage;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.model.Bstbl;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.DocCheckService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.OverSeaUtil;
import com.mega.eloan.lms.base.constants.ScoreTH;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.AbstractOverSeaCLSPage;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.base.service.RetrialService;
import com.mega.eloan.lms.base.service.ScoreServiceTH;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lms.pages.LMS1035M01Page;
import com.mega.eloan.lms.lms.pages.LMS1205M01Page;
import com.mega.eloan.lms.lms.service.LMS1035Service;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.mfaloan.service.MisGrpcmpService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C120M01A;
import com.mega.eloan.lms.model.C120S01A;
import com.mega.eloan.lms.model.C120S01B;
import com.mega.eloan.lms.model.C120S01C;
import com.mega.eloan.lms.model.C120S01D;
import com.mega.eloan.lms.model.C120S01E;
import com.mega.eloan.lms.model.C121M01A;
import com.mega.eloan.lms.model.C121M01D;
import com.mega.eloan.lms.model.C121M01H;
import com.mega.eloan.lms.model.C121S01A;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.utils.CapBeanUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 泰國消金信用評等表
 * </pre>
 * 
 * @since 2017/2/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2017/2/1,EL08034,new
 *          </ul>
 */
@Scope("request")
@Controller("lms1035m01formhandler")
@DomainClass(C121M01A.class)
public class LMS1035M01FormHandler extends AbstractFormHandler {

	@Resource
	EloanBatchClient eloanBatchClient;
		
	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;
	
	@Resource
	DocLogService docLogService;
	
	@Resource
	DocCheckService docCheckService;
	
	@Resource
	TempDataService tempDataService;
	
	@Resource
	UserInfoService userInfoService;
	
	@Resource
	NumberService numberService;
	
	@Resource
	MisdbBASEService misdbBASEService;
	
	@Resource
	CLSService clsService;

//	@Resource
//	ScoreService scoreService;
	
	@Resource
	ScoreServiceTH scoreServiceTH;
	
	@Resource
	LMS1035Service lms1035Service;
	
	@Resource
	LMS1205Service lms1205Service;

	@Resource
	RetrialService retrialService;
	
	@Resource
	BstblService bstblService;
	
	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	MisGrpcmpService misGrpcmpService;

	@Resource
	LMSService lmsService;
	
	Properties prop_abstractOverSeaCLSPage = MessageBundleScriptCreator.getComponentResource(AbstractOverSeaCLSPage.class);
	Properties prop_lms1205m01Page = MessageBundleScriptCreator.getComponentResource(LMS1205M01Page.class);
	Properties prop_lms1035m01Page = MessageBundleScriptCreator.getComponentResource(LMS1035M01Page.class);
	
	private static final String UI_PRE_C120_OID = "bef_c120m01a_oid";
	private static final String UI_C120_OID = "c120m01a_oid";
	/**
	 * 儲存
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 **/
	@DomainAuth(AuthType.Modify)
	public IResult saveMain(PageParameters params)
			throws CapException {
		return _saveAction(params, "N");
	}

	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = true)
	public IResult tempSave(PageParameters params)
	throws CapException {
		return _saveAction(params, "Y");
	}
	
	private CapAjaxFormResult _saveAction(PageParameters params,String tempSave)
	throws CapException{
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, tempSave);
		boolean allowIncomplete = Util.equals("Y", params.getString("allowIncomplete"));
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		//===
		String KEY = "saveOkFlag";
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set(KEY, false);
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = null;
		String page = params.getString(EloanConstants.PAGE);
		if (Util.isNotEmpty(mainOid)) {
			try{
				meta = clsService.findC121M01A_oid(mainOid);	
				List<GenericBean> saveList = new ArrayList<GenericBean>();
				
				if(true){
					//在 addRatingDoc 時，預先塞入 DeletedTime
					//meta.setDeletedTime(new Timestamp(DateUtils.addDays(CapDate.getCurrentTimestamp(), 1).getTime()));
					
					boolean chkAtLeastOne = true;
					if(meta.getDeletedTime()!=null){
						chkAtLeastOne = false;
					}
					if(chkAtLeastOne){
						List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
						if(CollectionUtils.isEmpty(list)){
							Map<String, String> param = new HashMap<String, String>();
							param.put("colName", "");
							throw new CapMessageException(MessageFormat.format(prop_lms1035m01Page.getProperty("l120m01a.error24"),
								    param.get("colName")), getClass());
						}
					}
				}

				boolean needChangeVarVer = false;
				if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), meta.getDocStatus()) 
						&& Util.isNotEmpty(Util.trim(meta.getVarVer()))){
					//仍在編製中的評等文件，且已引入借款人(用 c121m01d.varVer 回寫 c121m01a)，當模型改版時
					if(Util.notEquals(scoreServiceTH.get_Version_TH(), meta.getVarVer())){
						needChangeVarVer = true;	
					}
				}
				
				if ("01".equals(page)) {
					CapBeanUtil.map2Bean(params, meta, new String[] {	
							"lnYear", "lnMonth"
							, "repaymentSchFmt", "repaymentSchDays"
					});			
					if( ! Util.equals("2", meta.getRepaymentSchFmt())){
						meta.setRepaymentSchDays(null);
					}
				} else if ("02".equals(page)) {
					
				} else if ("03".equals(page)) {	
					//display only
				} else if ("04".equals(page)) {					
					C121S01A c121s01a = clsService.findC121S01A(meta);
					if(c121s01a==null){
						c121s01a = new C121S01A();
						OverSeaUtil.copyC121S01A(c121s01a, meta);
					}
					CapBeanUtil.map2Bean(params, c121s01a, new String[] {	
							"cmsType"
							, "securityRate", "location"
							, "houseAge", "houseArea"
							, "factor1", "factor2"
							
					});
					//~~~
					saveList.add(c121s01a);
				} else if ("05".equals(page) || "06".equals(page)) {
					String bef_c120m01a_oid = Util.trim(params.getString(UI_PRE_C120_OID));
					C120M01A c120m01a = clsService.findC120M01A_oid(bef_c120m01a_oid);
					if(Util.isEmpty(bef_c120m01a_oid)){
						throw new CapMessageException("未選取關係戶ID", getClass());	
					}
					if(c120m01a!=null){
						if ("05".equals(page)) {
							
						} else if ("06".equals(page)) {
							String noAdj = Util.trim(params.getString("noAdj"));
							String adjustStatus = "";
							String adjustFlag = "";
							String adjustReason = "";
							String adjRating = "";
							
							
							C121M01D c121m01_grade = clsService.findC121M01D_byC120M01A(c120m01a);
							String fRating = Util.trim(c121m01_grade.getOrgFr());
							
							if(Util.equals("2", noAdj)){
								//不符直覺：調升 or 調降
								adjustStatus = Util.trim(params.getString("adjustStatus"));							
								adjRating = Util.trim(params.getString("adjRating"));
								adjustReason = Util.trim(params.getString("adjustReason"));
								
								String sRating = c121m01_grade.getSRating();
								if(true){
									//不符直覺，且不為DF
									int i_sRating = Util.parseInt(sRating);								
									// adjustStatus 1.調升 2.調降 3.回復
									if(i_sRating!=0){
										int i_adjRating = Util.parseInt(adjRating);
										int raw_fRating = -1;
										String fmtMsg = "";
										if(Util.equals(adjustStatus, "2")){
											//降-不限
											raw_fRating = i_sRating + i_adjRating;
											fmtMsg = MessageFormat.format(prop_lms1035m01Page.getProperty("fmt.downgrade") , Util.trim(i_adjRating));
										}else if(Util.equals(adjustStatus, "1")){
											//只有升等，才填理由是[1.淨資產 2.職業 3.其它]
											adjustFlag = Util.trim(params.getString("adjustFlag"));
											
											//升-以2等為限
											int maxUpRating = 2;
											if(i_adjRating>maxUpRating){											
												throw new CapMessageException(prop_lms1035m01Page.getProperty("msg.002"), getClass());
											}else{			
												
											}
											raw_fRating = i_sRating - i_adjRating;
											fmtMsg = MessageFormat.format(prop_lms1035m01Page.getProperty("fmt.upgrade") , Util.trim(i_adjRating));
										}else{
											/*
											下拉選單有2個人，第1個人只 click 不符合直覺
											但未選 調升/調降 
											*/
											throw new CapMessageException(MessageFormat.format(prop_lms1035m01Page.getProperty("msg.004")
													, prop_abstractOverSeaCLSPage.getProperty("message.adjustStatus")), getClass());
										}
										if(raw_fRating!=OverSeaUtil.rating_min1_max10(raw_fRating)){
											String baseMsg = prop_lms1035m01Page.getProperty("C121M01D.sRating")+":"+Util.trim(i_sRating);
											throw new CapMessageException(MessageFormat.format(prop_lms1035m01Page.getProperty("msg.007")
													, baseMsg, fmtMsg), getClass());
										}
										fRating = Util.trim(OverSeaUtil.rating_min1_max10(raw_fRating));
									}	
								}							
							}else{
								//符合直覺
								adjustStatus = "3";//回復
								adjustFlag = "";
								adjustReason = "";
								adjRating = "";
								//fRating					
							}
							if(true){
								Timestamp nowTS = CapDate.getCurrentTimestamp();
								if(Util.isEmpty(noAdj)){
									// 若從「有值」變成「被清空」 => ratingDate也要被清空
									c121m01_grade.setRatingDate(null);
								}else{
									if(Util.equals(c121m01_grade.getNoAdj(), noAdj) 
											&& Util.equals(c121m01_grade.getAdjustStatus(), adjustStatus)
											&& Util.equals(c121m01_grade.getAdjRating(), adjRating)
											&& Util.equals(c121m01_grade.getAdjustFlag(), adjustFlag)
											){
												
									}else{
										c121m01_grade.setRatingDate(nowTS);										
									}	
								}
								if(Util.equals(c120m01a.getKeyMan(), "Y")){
									meta.setRatingDate(c121m01_grade.getRatingDate());
								}
								
								c121m01_grade.setGrdTDate(Util.equals(adjustStatus, "3")?null:nowTS);
								c121m01_grade.setAdjRating(adjRating);
								c121m01_grade.setFRating(fRating);
								if(true){
									JSONObject inputJson = new JSONObject();
									inputJson.put("fRating", fRating);
									
									JSONObject outputJson = new JSONObject();									
									//---依 fRating重算 違約機率(調整評等，2.0會開始禁用，因此模型種類參數，固定帶M)
									scoreServiceTH.setTHDR(scoreServiceTH.scoreTH(ScoreTH.type.泰國消金模型違約機率, inputJson, c121m01_grade.getVarVer(),OverSeaUtil.海外評等_房貸, c121m01_grade.getOwnBrId())
											, outputJson);
									DataParse.toBean(outputJson, c121m01_grade);									
								}
								c121m01_grade.setNoAdj(noAdj);
								c121m01_grade.setAdjustStatus(adjustStatus);
								c121m01_grade.setAdjustFlag(adjustFlag);
								c121m01_grade.setAdjustReason(adjustReason);
								if(true){
									c121m01_grade.setUpdater(user.getUserId());
									c121m01_grade.setUpdateTime(nowTS);
								}
								clsService.daoSave(c121m01_grade);
							}
						}
					}
				} else if ("07".equals(page)) {
					//display only					
				}
				//~~~
				saveList.add(meta);
				clsService.save(saveList);
				if(true){
					//為免輸入理由後，忘了 click 儲存
					//即使是 tempSave, 也刪 tempData
					if(Util.equals(params.getString("page"), "06") 
							&& Util.equals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))){
						tempDataService.deleteByMainId(meta.getMainId());
					}					
				}
				//===
				if(Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
					//在tempSave<>Y,若有未填欄位,丟 CapMessageException, 讓 saveOkFlag==false

					List<String> adjReasonCnt = new ArrayList<String>(); 
					LinkedHashMap<String, String> adjReasonCfmMap = new LinkedHashMap<String, String>();
					
					String msg = lms1035Service.checkIncompleteMsg(meta, adjReasonCnt, adjReasonCfmMap);
					if(Util.isNotEmpty(msg)){
						if(allowIncomplete){
							result.set("IncompleteMsg", msg);			
						}else{
							throw new CapMessageException(msg, getClass());	
						}
					}else{
						if(allowIncomplete){
										
						}else{
							String cfmStr = OverSeaUtil.adjustReason_html(adjReasonCfmMap);
							OverSeaUtil.set_adjustReason_fmt_result(result, adjReasonCnt.get(0)
									, cfmStr, prop_abstractOverSeaCLSPage);
						}
					}					
				}
				if(true){
					if(Util.equals("Y", tempSave)){
						
					}else{
						lms1035Service.del_noneRating_score(meta);	
					}
					String varVerNow = scoreServiceTH.get_Version_TH(); //目前適用之評等版本
					if(needChangeVarVer){
						if(Util.equals(varVerNow, OverSeaUtil.V2_0_LOAN_TH)){
							lms1035Service.calc_C121_score_V2_0(meta);
						}else{
							lms1035Service.calc_C121_score(meta);
						}
						//避免呈主管時save同時做版本轉換,清空「主觀評等更新」後, 尚未 keyin 即送出
						String varVerChMsg = "模型版本更新至 "+Util.trim(meta.getVarVer());
						if(Util.notEquals("Y", SimpleContextHolder.get(EloanConstants.TEMPSAVE_RUN))) {
							if(allowIncomplete){
								result.set("IncompleteMsg", varVerChMsg);			
							}else{
								throw new CapMessageException(varVerChMsg, getClass());	
							}	
						}
					}else{
						if(lms1035Service.should_calc_C121_score(Util.parseInt(page), meta)){
							if(Util.equals(varVerNow, OverSeaUtil.V2_0_LOAN_TH)){
								lms1035Service.calc_C121_score_V2_0(meta);
							}else{
								lms1035Service.calc_C121_score(meta);
							}
						}
					}
				}									
				
				result.set(KEY, true);	
			}catch(Exception e){
				logger.error(StrUtils.getStackTrace(e));
				throw new CapException(e, getClass());
			}		
		}		
		result.add(query(params));
		
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult query(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC121M01A_oid(mainOid);	
			String varVer = Util.trim(meta.getVarVer());
			String page = params.getString(EloanConstants.PAGE);		
			if ("01".equals(page)) {
				if(true){
					LMSUtil.addMetaToResult(result, meta, new String[]{ 
							"ratingDate"	
							, "lnYear", "lnMonth"
							, "repaymentSchDays"
							, "varVer"
							, "caseNo" 
							, "randomCode" });
				}				
				result.set("repaymentSchFmt", Util.equals("2", meta.getRepaymentSchFmt())?"2":"1"); //default=1
				result.set("status", _docStatus(meta));
				result.set("creator", _id_name(meta.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(meta.getCreateTime())));
				result.set("updater", _id_name(meta.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(meta.getUpdateTime())));
				
			} else if ("02".equals(page)) {
			
			} else if ("03".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				Set<String> idDup11Set = OverSeaUtil.fetch_idDup11(list);
				BigDecimal item_p3 = null;
				
				Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
				JSONArray jsonAraay = new JSONArray();
				Map<String, C121M01D> idDup_C121M01D_map = clsService.findIdDup_C121M01D(meta.getMainId(), idDup11Set);
				for(C120M01A c120m01a: list){
					if(Util.equals(UtilConstants.lngeFlag.共同借款人, c120m01a.getCustPos())){
						/*
						 在 assign 從債務人身分 時, 可選擇 C-共借人
						 但只採 「借款人與連保人年收入合計」 
						*/
						continue;
					}
					//=====================================
					String idDup11 = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo());
					
					C121M01D c121m01d = idDup_C121M01D_map.get(idDup11);
					
					JSONObject o = new JSONObject();
					o.put("c120_oid", c120m01a.getOid());
					o.put("c120_custId", Util.trim(c120m01a.getCustId()));
					o.put("c120_dupNo", Util.trim(c120m01a.getDupNo()));
					o.put("c120_custName", Util.trim(c120m01a.getCustName()));
					o.put("c120_custPos", _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos())));
					
					o.put("c120_raw_payCurr", Util.trim(c121m01d.getRaw_payCurr()));
					o.put("c120_raw_payAmt", NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_payAmt())));
					o.put("c120_raw_otherCurr", Util.trim(c121m01d.getRaw_otherCurr()));
					o.put("c120_raw_otherAmt", NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_otherAmt())));
					
					o.put("c120_raw_p3_idv", NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getRaw_p3_idv())));
					o.put("c120_item_p4", NumConverter.addComma(LMSUtil.pretty_numStr(c121m01d.getItem_p4())));
					//---
					jsonAraay.add(o);
					
					//挑任一項的 p3
					item_p3 = c121m01d.getItem_p3();
				}
				
				BranchRate branchRate = lmsService.getBranchRate(meta.getCaseBrId());
				String localCurr = branchRate.getMCurr();
				
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				map.put("key", jsonAraay);
				result.set("c120m01a_list", new CapAjaxFormResult(map));
				
				result.set("localCurr", localCurr);
				result.set("item_p3", NumConverter.addComma(LMSUtil.pretty_numStr(item_p3)));
				
			} else if ("04".equals(page)) {
				C121S01A c121s01a = clsService.findC121S01A(meta);
				if(c121s01a==null){
					c121s01a = new C121S01A();
				}
				LMSUtil.addMetaToResult(result, c121s01a, new String[]{ 
						"cmsType"				
						, "securityRate" , "location"
						, "region"  , "houseAge", "houseArea"
						, "factor1", "factor2"  });
				
			} else if ("05".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				_add_borrower_list(result, list);
			} else if ("06".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				_add_borrower_list(result, list);
			} else if ("07".equals(page)) {
				List<C120M01A> list = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
				Set<String> idDup11Set = OverSeaUtil.fetch_idDup11(list);
				Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
				
				Map<String, C120S01A> idDup_C120S01A_map = clsService.findIdDup_C120S01A(meta.getMainId(), idDup11Set);
				Map<String, C121M01D> idDup_C121M01D_map = clsService.findIdDup_C121M01D(meta.getMainId(), idDup11Set);
				
				
				JSONArray jsonAraay = new JSONArray();
				for(C120M01A c120m01a: list){
					String idDup11 = LMSUtil.getCustKey_len10custId(c120m01a.getCustId(), c120m01a.getDupNo());
					
					C120S01A c120s01a = idDup_C120S01A_map.get(idDup11);
					C121M01D c121m01d = idDup_C121M01D_map.get(idDup11);
					//=====================================
					JSONObject o = new JSONObject();
					o.put("c120_oid", c120m01a.getOid());
					o.put("c120_custId", Util.trim(c120m01a.getCustId()));
					o.put("c120_dupNo", Util.trim(c120m01a.getDupNo()));
					o.put("c120_custName", Util.trim(c120m01a.getCustName()));
					o.put("c120_custPos", _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos())));
					o.put("c120_o_grade", c120s01a==null?"":Util.trim(c120s01a.getO_grade()));
					
					String mowTypeCountry = Util.trim(meta.getMowTypeCountry());
					boolean showNCB = true;
					if(Util.equals(mowTypeCountry, OverSeaUtil.C121M01A_MOW_TYPE_COUNTRY_越南) ){
						showNCB = false;
					}
					o.put("showNCB", showNCB);
					
					String c121m01d_oid = "";
					String c121m01d_pRating = "";
					String c121m01d_sRating = "";
					String c121m01d_fRating = "";
					String c121m01d_chkItemTHO2 = "";
					String c121m01d_chkItemInfoNcb = "";
					String c121m01d_adjustReason = "";
					if(c121m01d!=null){
						c121m01d_oid = Util.trim(c121m01d.getOid());
						c121m01d_pRating = Util.trim(c121m01d.getPRating());
						c121m01d_sRating = Util.trim(c121m01d.getSRating());
						c121m01d_fRating = Util.trim(c121m01d.getFRating());
						c121m01d_chkItemTHO2 = Util.trim(c121m01d.getChkItemTHO2());
						c121m01d_chkItemInfoNcb = StringUtils.join(build_chkItemInfoNcb(c121m01d), "<br/>");
						c121m01d_adjustReason = Util.trim(c121m01d.getAdjustReason());
					}
					o.put("c121m01d_oid", c121m01d_oid);
					o.put("c121m01d_pRating", c121m01d_pRating);
					o.put("c121m01d_sRating", c121m01d_sRating);
					o.put("c121m01d_fRating", clsService.convertRatingDF(c121m01d_fRating));
					o.put("c121m01d_chkItemTHO2", c121m01d_chkItemTHO2);
					o.put("c121m01d_chkItemInfoNcb", c121m01d_chkItemInfoNcb);
					o.put("c121m01d_adjustReason", c121m01d_adjustReason);
					
					//非房貸部份
					C121M01H c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
					
					String c121m01h_oid = "";
					String c121m01h_pRating = "";
					String c121m01h_sRating = "";
					String c121m01h_fRating = "";
					String c121m01h_chkItemTHO2 = "";
					String c121m01h_chkItemInfoNcb = "";
//					String c121m01h_adjustReason = "";
					if(c121m01h != null){
						c121m01h_oid = Util.trim(c121m01h.getOid());
						c121m01h_pRating = Util.trim(c121m01h.getPRating());
						c121m01h_sRating = Util.trim(c121m01h.getSRating());
						c121m01h_fRating = Util.trim(c121m01h.getFRating());
						c121m01h_chkItemTHO2 = Util.trim(c121m01h.getChkItemTHO2());
						c121m01h_chkItemInfoNcb = StringUtils.join(build_chkItemInfoNcb(c121m01h), "<br/>");
//						c121m01h_adjustReason = Util.trim(c121m01h.getAdjustReason());
					}
					o.put("c121m01h_oid", c121m01h_oid);
					o.put("c121m01h_pRating", c121m01h_pRating);
					o.put("c121m01h_sRating", c121m01h_sRating);
					o.put("c121m01h_fRating", clsService.convertRatingDF(c121m01h_fRating));
					o.put("c121m01h_chkItemTHO2", c121m01h_chkItemTHO2);
					o.put("c121m01h_chkItemInfoNcb", c121m01h_chkItemInfoNcb);
//					o.put("c121m01h_adjustReason", c121m01h_adjustReason);
					
					//---
					jsonAraay.add(o);
				}
				HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
				map.put("key", jsonAraay);
				result.set("c120m01a_list", new CapAjaxFormResult(map));
				
			}
			result.set("varVer", varVer);
		}
		return defaultResult(params, meta, result);
	}
	
	/**
	 * 參考  _add_fmt_adj(...)
	 * @param c121m01d
	 * @return
	 */
	private List<String> build_chkItemInfoNcb(C121M01D c121m01d){
		return OverSeaUtil.build_chkItemInfoNcb(prop_lms1035m01Page, c121m01d);
	}
	
	private List<String> build_chkItemInfoNcb(C121M01H c121m01h){
		return OverSeaUtil.build_chkItemInfoNcb(prop_lms1035m01Page, c121m01h);
	}
	
	private void _add_borrower_list(CapAjaxFormResult result, List<C120M01A> list){
		JSONArray jsonAraay = new JSONArray();
		for(C120M01A c120m01a: list){
			JSONObject o = new JSONObject();
			o.put("oid", c120m01a.getOid());
			o.put("custId", Util.trim(c120m01a.getCustId()));
			o.put("dupNo", Util.trim(c120m01a.getDupNo()));
			o.put("custName", Util.trim(c120m01a.getCustName()));
			//---
			jsonAraay.add(o);
		}
		HashMap<String, JSONArray> map = new HashMap<String, JSONArray>();
		map.put("key", jsonAraay);
		result.set("c120m01a_list", new CapAjaxFormResult(map));
		if(true){
			String c120m01a_oid_m = "";
			for(C120M01A c120m01a: list){
				if(Util.equals("Y", c120m01a.getKeyMan())){
					c120m01a_oid_m = c120m01a.getOid();
				}
			}
			if(Util.isNotEmpty(c120m01a_oid_m)){
				result.set("c120m01a_oid_m", c120m01a_oid_m);	
			}					
		}
	}
	private String _docStatus(C121M01A meta){
		String docStatus = Util.trim(meta.getDocStatus());
		if(Util.isNotEmpty(docStatus)){
			Properties prop = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			String _code = docStatus;
			if(Util.equals(OverSeaUtil.C121M01A_IMPDOCSTATUS, docStatus)){
				_code = CreditDocStatusEnum.海外_已核准.getCode();
			}
			String desc = Util.trim(prop.get("docStatus."+_code));
			if(Util.isNotEmpty(desc)){
				return desc;
			}
		}
		
		return docStatus;
	}
	private CapAjaxFormResult defaultResult(PageParameters params, C121M01A meta,
			CapAjaxFormResult result) throws CapException {
		// required information
		result.set(EloanConstants.PAGE, Util.trim(params.getString(EloanConstants.PAGE)));
		result.set(EloanConstants.MAIN_OID, Util.trim(meta.getOid()));
		result.set(EloanConstants.MAIN_DOC_STATUS, meta.getDocStatus());
		result.set(EloanConstants.MAIN_ID, Util.trim(meta.getMainId()));
		result.set("ratingId", meta.getRatingId());
		
		String branchName = meta==null?"":branchService.getBranchName(meta.getCaseBrId());
		result.set("branchName", meta.getCaseBrId() + " "+ branchName);
		result.set("custId", Util.trim(meta.getCustId()));
		result.set("dupNo", Util.trim(meta.getDupNo()));
		result.set("custName", Util.trim(meta.getCustName()));
		
		set_titleInfo(result, meta);
		return result;
	}
	
	private void set_titleInfo(CapAjaxFormResult result, C121M01A meta){
		result.set("titleInfo", Util.trim(Util.trim(meta.getCaseNo())+" "+Util.trim(meta.getCustId())
				+"-"+Util.trim(meta.getDupNo())+" "+Util.trim(meta.getCustName())));
	}
	private String _id_name(String raw_id){
		String id = Util.trim(raw_id);		
		return Util.trim(id+" "+Util.trim(userInfoService.getUserName(id)));
	}
	
	@DomainAuth(AuthType.Modify + AuthType.Accept)
	public IResult flowAction(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		String decisionExpr = Util.trim(params.getString("decisionExpr"));
		
		C121M01A meta = null;
		if (Util.isNotEmpty(mainOid)) {
			meta = clsService.findC121M01A_oid(mainOid);
			
			String errMsg = "";
			
			String docStatus = Util.trim(meta.getDocStatus());
			String nextStatus = "";
			DocLogEnum _DocLogEnum = null;
			if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
				nextStatus = CreditDocStatusEnum.海外_待覆核.getCode();
				_DocLogEnum = DocLogEnum.FORWARD;
				
				if(true){
					Set<String> joinVarVerSet = clsService.joinVarVer(meta);
					if(joinVarVerSet.size()>1){
						errMsg = "包含多個模型 "+StringUtils.join(joinVarVerSet ,"、");	
					}					
				}
				
				if(true){
					//泰國有要檢核 ncbQDate 在3個月內
					// ref  LMSM01FormHandler :: checkSend(...)
					if(Util.equals(CreditDocStatusEnum.海外_編製中.getCode(), docStatus)){
						
						List<C120M01A> c120m01aList = clsService.filter_shouldRating(clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta));
						
						Set<String> idDup11Set = OverSeaUtil.fetch_idDup11(c120m01aList);
						
						List<String> ncbQDateFailList = clsService.checkOverSea_c120s01e_qdate(meta.getMainId(), 
								idDup11Set, OverSeaUtil.L120M01A_RatingFlag_TH, "");
						
						if (ncbQDateFailList.size() > 0) {
							errMsg = (MessageFormat.format(prop_abstractOverSeaCLSPage.getProperty("error.11"),
								StringUtils.join(ncbQDateFailList,(UtilConstants.Mark.MARKDAN + "<br/>"))));
						}
					
					}								
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_待覆核.getCode(), docStatus)){
				//核定、退回
				if(Util.equals("核定", decisionExpr)){					
					if(Util.equals(user.getUserId(), meta.getUpdater())){
						errMsg = RespMsgHelper.getMessage("EFD0053");
					}else{
						nextStatus = CreditDocStatusEnum.海外_已核准.getCode();
						_DocLogEnum = DocLogEnum.ACCEPT;	
					}
				}else if(Util.equals("退回", decisionExpr)){
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.BACK;
				}
			}else if(Util.equals(CreditDocStatusEnum.海外_已核准.getCode(), docStatus)){	
				if(Util.equals("退回", decisionExpr)){
					//被簽報書引用，則不能退回
					errMsg = clsService.returnRatingDoc_Approved_to_Editing(meta);
					
					nextStatus = CreditDocStatusEnum.海外_編製中.getCode();
					_DocLogEnum = DocLogEnum.CREATE;
				}
			}
						
			if(Util.isNotEmpty(errMsg)){				
				throw new CapMessageException(errMsg, getClass());
			}else{
				if(Util.isEmpty(nextStatus)){
					throw new CapMessageException("流程異常["+docStatus+"]", getClass());
				}	
			}
			if(true){
				if(Util.equals(nextStatus, CreditDocStatusEnum.海外_已核准.getCode())){
					meta.setApprover(user.getUserId());
					meta.setApproveTime(CapDate.getCurrentTimestamp());
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_編製中.getCode())){
					meta.setApprover(null);
					meta.setApproveTime(null);
				}else if(Util.equals(nextStatus, CreditDocStatusEnum.海外_待覆核.getCode())){
					Date ratingDate = null;
					C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(meta.getMainId(), meta.getCustId(), meta.getDupNo());
					if(c120m01a!=null){
						C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
						if(c121m01d!=null){
							ratingDate = c121m01d.getRatingDate();
						}
					}	

					if(CrsUtil.isNull_or_ZeroDate(ratingDate)){
						throw new CapMessageException(prop_abstractOverSeaCLSPage.getProperty("message.c121m01a_ratingDate_is_empty"), getClass());
					}
					
					//避免更換主借人時，漏掉同步
					meta.setRatingDate(ratingDate);
				}
				meta.setDocStatus(nextStatus);
				clsService.daoSave(meta);
				//===
				if(_DocLogEnum!=null){
					docLogService.record(meta.getOid(), _DocLogEnum);	
				}				
			}
			
			tempDataService.deleteByMainId(meta.getMainId());
			docCheckService.unlockDocByMainIdUser(meta.getMainId(), user.getUserId());
		}
		return defaultResult( params, meta, result);
	}
	
	/**
	 * 比照建立簽報書 LMSM02FormHandler::addL120m01a
	 * 在insert L120M01A 相關 table 時，預先塞入 deletedTime
	 * 在 save 時，再把 deletedTime 清空
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult addRatingDoc(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String uuid = IDGenerator.getUUID();
		
		CapAjaxFormResult result = new CapAjaxFormResult();
		C121M01A meta = new C121M01A();
		if(true){
			Timestamp nowTS = CapDate.getCurrentTimestamp();
			meta.setOwnBrId(user.getUnitNo());		
			OverSeaUtil.setC121M01A(meta, uuid);			
			meta.setCustId("");
			meta.setDupNo("");
			meta.setCustName("");
			meta.setTypCd(TypCdEnum.海外.getCode());
			meta.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			meta.setDeletedTime(new Timestamp(DateUtils.addDays(CapDate.getCurrentTimestamp(), 1).getTime()));
			meta.setRatingDate(null);
			meta.setCaseYear(Integer.parseInt(StringUtils.substring(TWNDate.toAD(nowTS), 0, 4)));
			meta.setCaseBrId(user.getUnitNo());
			meta.setCaseNo("");
			meta.setRandomCode("");
			
			meta.setCreator(user.getUserId());
			meta.setCreateTime(CapDate.getCurrentTimestamp());
			if(true){
				//把 creator, createTime 填入 updater, updateTime
				meta.setUpdater(meta.getCreator());
				meta.setUpdateTime(meta.getCreateTime());	
			}
			if(true){
				String mowType = "";
				mowType = OverSeaUtil.getMowType(meta.getCaseBrId());
				meta.setMowType(mowType);
			}		
			
			//國別碼
			if(true){
				String mowTypeCountry = "";
				mowTypeCountry = OverSeaUtil.getMowTypeCountry(meta.getCaseBrId());
				meta.setMowTypeCountry(mowTypeCountry);
			}
			//======
			clsService.save(meta);	
		}
		return defaultResult(params, meta, result);
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult delRatingDoc(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---		
		CapAjaxFormResult result = new CapAjaxFormResult();
		String failmsg = "";
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		if(true){
			eloandbBASEService.c121m01aclearUnUsedDoc();	
		}		
		
		Map<String, String> lockedUser = docCheckService.listLockedDocUser(meta.getMainId());
		if (lockedUser != null) {
			failmsg = meta.getCustId()+" "+ getPopMessage("EFD0055", lockedUser);
			throw new CapMessageException(failmsg, getClass());
		}
		meta.setDeletedTime(CapDate.getCurrentTimestamp());
		clsService.save(meta);
		
		return result;
	}
	
	/**
	XXX 參考 原海外消金 FormHandler 的程式
	● checkAddBorrow{checkdata:false,  haseCust:true}
	
	● addBorrowMain2 @ LMSS02APage01.js
		openDocAddBorrow @ LMSS02APage02.js
		
	● codetypehandler
	
	● getCustData3 @ LMSS02APage01.js
	
	● customerformhandler::custQueryBy0024ByIdDupNo
 
 	● setCustData3 @ LMSS02APage02.js
 	 	在開啟 thickbox 後{'通訊地址': coAddr, '行動電話': mTel, 'E-mail': email} 來自 getCustData3	
 	
	*/
	@DomainAuth(AuthType.Modify)
	public IResult addRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo", "0"));
		String custName = Util.trim(params.getString("custName"));
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		String ownBrId = meta.getOwnBrId();
		
		if(true){
			List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			if(CollectionUtils.isEmpty(list)){
				custPos = "M";
			}
			
			if(CollectionUtils.isEmpty(list)){
				if(meta.getDeletedTime()!=null 
						&& meta.getDeletedTime().getTime()>CapDate.getCurrentTimestamp().getTime()){
					//先設成主要借款人
					meta.setDeletedTime(null);
					
					//XXX 在引入 C120M01A 時 insert into LMS.NUMBER 且 UPDATE LMS.L120M01A  SET deletedTime=null, CASENO = ?, CASESEQ = ?
					if (Util.isEmpty(meta.getCaseSeq()) && Util.isEmpty(meta.getCaseNo())) {
						meta.setCaseSeq(Integer.parseInt(numberService.getNumberWithMax(C121M01A.class, ownBrId, null, 99999)));
						StringBuilder caseNum = new StringBuilder();
						IBranch ibranch = branchService.getBranch(ownBrId);

						caseNum.append(
								Util.toSemiCharString(meta.getCaseYear().toString()))
								.append(Util.trim(ibranch.getNameABBR()))
								.append("消金評等第")
								.append(Util.toSemiCharString(Util.addZeroWithValue(Util.trim(meta.getCaseSeq()), 5)))
								.append("號");
						meta.setCaseNo(caseNum.toString());
					}
				}				
				
			}else{
				C120M01A c120m01a = lms1205Service.findC120M01AByUniqueKey(meta.getMainId(), custId, dupNo);
				if(c120m01a!=null){
					Map<String, String> map = new HashMap<String, String>();
					map.put("msg", custId+"-"+dupNo);

					throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map),
							getClass());
				}
			}
		}
		
		Map<String, Object> m_busCd_ecoNm = misdbBASEService.findCustBussDataByIdAndDup(custId, dupNo);
		String busCode = Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD"));
		String ecoNm = Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM"));
		if(Util.isEmpty(busCode)){
			/*
			 * 當不存在於 mis.custdata 時，允許 user 從 e-loan 新增
			 */
			busCode = Util.trim(params.getString("busCode"));
			Bstbl bstbl = bstblService.findByEcocd(busCode);
			if(bstbl != null){
				ecoNm = Util.trim(bstbl.getEconm());
			}
		}
		
		if(true){
			C121M01D c121m01d = new C121M01D();
			if(true){
				OverSeaUtil.copyC121M01D(c121m01d, meta, custId, dupNo);
			}
			
			C120M01A c120m01a = new C120M01A();
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();

			if(true){
				_set_common_filed(meta.getMainId(), custId, dupNo
						, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);
				if(true){
					c120m01a.setOwnBrId(ownBrId);
					c120m01a.setTypCd(UtilConstants.Casedoc.typCd.海外);
					c120m01a.setCustName(custName);
					
					if(Util.equals(custPos, "M") && !LMSUtil.isBusCode_060000_130300(busCode)){
						custPos = "";//企業戶不應為主借人
					}
					
					c120m01a.setRatingDesc(meta.getCaseNo());
					
					if(Util.equals(custPos, "M")){
						c120m01a.setKeyMan("Y");
						c120m01a.setCustPos("M");
						c120m01a.setO_custRlt("");
					}else{
						c120m01a.setKeyMan("N");
						c120m01a.setCustPos(custPos);
						c120m01a.setO_custRlt(o_custRlt);
					}
					c120m01a.setNaturalFlag(LMSUtil.isBusCode_060000_130300(busCode)?"Y":"N");
				}
				if(true){
					c120s01a.setBusCode(busCode);
					c120s01a.setEcoNm(ecoNm);					
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();
				savedList.add(c121m01d);
				//泰國2.0模型另外建立C121M01H
				String varVer = scoreServiceTH.get_Version_TH(); //取得目前的評等版本
				if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
					C121M01H c121m01h = new C121M01H();
					if(true){
						OverSeaUtil.copyC121M01H(c121m01h, meta, custId, dupNo);
						savedList.add(c121m01h);
					}
				}
				
				if(true){
					savedList.add(c120m01a);
					savedList.add(c120s01a);
					savedList.add(c120s01b);
					savedList.add(c120s01c);
					savedList.add(c120s01d);
					savedList.add(c120s01e);
				}
				savedList.add(meta);
				//~~~
				clsService.save(savedList);
			}
			
			if(Util.equals(custPos, "M")){
				clsService.syncCustPosM(meta, c120m01a.getOid());	
				set_titleInfo(result, meta);
			}
			result.set("c120m01a_oid", c120m01a.getOid());
		}
		
		if(true){
			Map<String, Map<String, String>> map = lms1205Service.findCustData2ByCustId(custId, dupNo);
			CapAjaxFormResult map1 = new CapAjaxFormResult(map.get("coAddr"));
			CapAjaxFormResult map2 = new CapAjaxFormResult(map.get("mComTel"));
			CapAjaxFormResult map3 = new CapAjaxFormResult(map.get("mTel"));
			CapAjaxFormResult map4 = new CapAjaxFormResult(map.get("email"));
			
			OverSeaUtil.getCustData3(result, map1, map2, map3, map4);
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult impRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		String ownBrId = meta.getOwnBrId();
		C120M01A src_c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		String custId = src_c120m01a.getCustId();
		String dupNo = src_c120m01a.getDupNo();
		if(true){
			List<C120M01A> list = clsService.findC120M01A_ByC121M01A_orderBy_keymanCustposCustid(meta);
			if(CollectionUtils.isEmpty(list)){
				custPos = "M";
			}
			
			if(CollectionUtils.isEmpty(list)){
				if(meta.getDeletedTime()!=null 
						&& meta.getDeletedTime().getTime()>CapDate.getCurrentTimestamp().getTime()){
					//先設成主要借款人
					meta.setDeletedTime(null);
					
					//XXX 在引入 C120M01A 時 insert into LMS.NUMBER 且 UPDATE LMS.L120M01A  SET deletedTime=null, CASENO = ?, CASESEQ = ?
					if (Util.isEmpty(meta.getCaseSeq()) && Util.isEmpty(meta.getCaseNo())) {
						meta.setCaseSeq(Integer.parseInt(numberService.getNumberWithMax(C121M01A.class, ownBrId, null, 99999)));
						StringBuilder caseNum = new StringBuilder();
						IBranch ibranch = branchService.getBranch(ownBrId);

						caseNum.append(
								Util.toSemiCharString(meta.getCaseYear().toString()))
								.append(Util.trim(ibranch.getNameABBR()))
								.append("消金評等")
								.append(Util.toSemiCharString(Util.addZeroWithValue(Util.trim(meta.getCaseSeq()), 5)))
								.append("號");
						meta.setCaseNo(caseNum.toString());
					}
				}	
			}
		}
		
		
		if(true){
			//已存在的 data 要被 replace 掉
			if(true){
				C120M01A exist_c120m01a = lms1205Service.findC120M01AByUniqueKey(meta.getMainId(), custId, dupNo);
				if(exist_c120m01a!=null){
					//已存在的評等表，也應一併刪掉
					lms1035Service.delRatingDocCust(meta, exist_c120m01a);
					//===
					clsService.removeC120M01A_attachFile(exist_c120m01a);
				}
			}
			
			Map<String, Object> colMap = new HashMap<String, Object>();
			colMap.put("o_single", "");
			C120M01A c120m01a = clsService.copyC120Relate(src_c120m01a, meta.getMainId(), colMap);
			clsService.copyC120AttchDocFile("", c120m01a);
			C121M01D c121m01d = new C121M01D();
			if(true){
				OverSeaUtil.copyC121M01D(c121m01d, meta, custId, dupNo);
			}
			if(Util.equals(meta.getVarVer(), OverSeaUtil.V2_0_LOAN_TH)){ //2.0模型，同步新增C121M01H
				C121M01H c121m01h = new C121M01H();
				if(true){
					OverSeaUtil.copyC121M01H(c121m01h, meta, custId, dupNo);
				}
			}

			if(true){				
				if(true){
					C120S01A c120s01a = clsService.findC120S01A(c120m01a);
					if(c120s01a!=null){
						List<Map<String, Object>> listGrpMap = misGrpcmpService.findGrpcmpSelGrpdtl(
								c120m01a.getCustId(), c120m01a.getDupNo());
						Map<String, Object> grpMap = null;				
						if(CollectionUtils.isNotEmpty(listGrpMap)){
							grpMap = listGrpMap.get(0);
						}
						//參考 L120S01B 的定義
						String groupNo = Util.trim(MapUtils.getString(grpMap, "GRPID"));
						String groupName = Util.trim(MapUtils.getString(grpMap, "GRPNM"));
						String groupBadFlag = Util.trim(MapUtils.getString(grpMap, "BADFLAG"));
						
						c120s01a.setGroupNo(groupNo);
						c120s01a.setGroupName(groupName);
						c120s01a.setGroupBadFlag(groupBadFlag);
						clsService.save(c120s01a);
					}	
					
					String busCode = c120s01a.getBusCode();
					if(Util.equals(custPos, "M") && !LMSUtil.isBusCode_060000_130300(busCode)){
						custPos = "";//企業戶不應為主借人
					}
					
					c120m01a.setRatingDesc(meta.getCaseNo());
					
					if(Util.equals(custPos, "M")){
						c120m01a.setKeyMan("Y");
						c120m01a.setCustPos("M");
						c120m01a.setO_custRlt("");
					}else{
						c120m01a.setKeyMan("N");
						c120m01a.setCustPos(custPos);
						c120m01a.setO_custRlt(o_custRlt);
					}
					c120m01a.setNaturalFlag(LMSUtil.isBusCode_060000_130300(busCode)?"Y":"N");
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();
				savedList.add(c121m01d);
				
				//泰國2.0模型另外建立C121M01H
				String varVer = scoreServiceTH.get_Version_TH(); //取得目前的評等版本
				if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
					C121M01H c121m01h = new C121M01H();
					if(true){
						OverSeaUtil.copyC121M01H(c121m01h, meta, custId, dupNo);
						savedList.add(c121m01h);
					}
				}
				if(true){
					savedList.add(c120m01a);					
				}
				savedList.add(meta);
				//~~~
				clsService.save(savedList);
			}
			
			if(Util.equals(custPos, "M")){
				clsService.syncCustPosM(meta, c120m01a.getOid());
				set_titleInfo(result, meta);
			}
			result.set("c120m01a_oid", c120m01a.getOid());
		}
		return result;
	}	
	
	@DomainAuth(AuthType.Modify)
	public IResult delRatingDocCust(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
			
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		boolean calc_C121_score = false;
		C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);		
		if(c121m01d!=null){
			if(Util.equals(OverSeaUtil.M, c121m01d.getRaw_p3_pos()) || Util.equals(UtilConstants.lngeFlag.連帶保證人, c121m01d.getRaw_p3_pos())){
				calc_C121_score = true;
			}			
		}
		
		C121M01A c121m01a = clsService.findC121M01A_oid(Util.trim(params.getString("c121m01a_oid")));
		if(c121m01a!=null){
			lms1035Service.delRatingDocCust(c121m01a, c120m01a);
		}

		
		//泰國P3因子=借款人與連帶保證人之合計資料
		//如刪掉的身分別是[M,G], 也要重算P3
		//參考 calc_C121_score(...)
		//p.s. 匯率的變動, 可能造成 raw_p3_idv 異動
		if(calc_C121_score){
			String varVer = Util.trim(c121m01a.getVarVer());
			//強迫重算
			if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){
				lms1035Service.calc_C121_score_V2_0(c121m01a);	
			}else{
				lms1035Service.calc_C121_score(c121m01a);	
			}
		}
		return result;
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult addBaseData(PageParameters params)
			throws CapException {
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN, "N");
		//---	
		CapAjaxFormResult result = new CapAjaxFormResult();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = OverSeaUtil.trimRightPaddingFullEmpty(params.getString("custName"));
		
		C120M01A c120m01a = clsService.findC120M01A_o_single_Y(user.getUnitNo(), custId, dupNo);
		if(c120m01a!=null){
			Map<String, String> map = new HashMap<String, String>();
			map.put("msg", custId+"-"+dupNo);

			throw new CapMessageException(RespMsgHelper.getMessage(UtilConstants.AJAX_RSP_MSG.資料已存在, map), getClass());
		}else{
			c120m01a = new C120M01A();	
		}
		
		Map<String, Object> m_busCd_ecoNm = misdbBASEService.findCustBussDataByIdAndDup(custId, dupNo);
		String busCode = Util.trim(MapUtils.getString(m_busCd_ecoNm, "BUSCD"));
		String ecoNm = Util.trim(MapUtils.getString(m_busCd_ecoNm, "ECONM"));
		if(Util.isEmpty(busCode)){
			/*
			 * 當不存在於 mis.custdata 時，允許 user 從 e-loan 新增
			 */
			busCode = Util.trim(params.getString("busCode"));
			Bstbl bstbl = bstblService.findByEcocd(busCode);
			if(bstbl != null){
				ecoNm = Util.trim(bstbl.getEconm());
			}
		}
		
		if(!LMSUtil.isBusCode_060000_130300(busCode)){
			String pattern = prop_abstractOverSeaCLSPage.getProperty("error.04");
			throw new CapMessageException(MessageFormat.format(pattern, custId, busCode), getClass());
		}
		
		if(true){			
			C120S01A c120s01a = new C120S01A();
			C120S01B c120s01b = new C120S01B();
			C120S01C c120s01c = new C120S01C();
			C120S01D c120s01d = new C120S01D();
			C120S01E c120s01e = new C120S01E();

			if(true){
				String uuid = IDGenerator.getUUID();
				String ownBrId = user.getUnitNo();
				_set_common_filed(uuid, custId, dupNo
						, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);
				if(true){
					c120m01a.setOwnBrId(ownBrId);
					c120m01a.setTypCd(UtilConstants.Casedoc.typCd.海外);
					c120m01a.setCustName(custName);
					c120m01a.setO_single("Y");
				}
				if(true){
					c120s01a.setBusCode(busCode);
					c120s01a.setEcoNm(ecoNm);					
				}
			}
			if(true){
				List<GenericBean> savedList = new ArrayList<GenericBean>();				
				if(true){
					savedList.add(c120m01a);
					savedList.add(c120s01a);
					savedList.add(c120s01b);
					savedList.add(c120s01c);
					savedList.add(c120s01d);
					savedList.add(c120s01e);
				}
				//~~~
				clsService.save(savedList);
			}
		}		
		result.set("c120m01a_oid", c120m01a.getOid());
		return result;
	}
	
	private void _set_common_filed(
			String mainId, String custId, String dupNo
			, C120M01A c120m01a, C120S01A c120s01a
			, C120S01B c120s01b, C120S01C c120s01c
			, C120S01D c120s01d, C120S01E c120s01e) 
	throws CapFormatException{
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch iBranch = branchService.getBranch(MegaSSOSecurityContext.getUnitNo());
		String creator = user.getUserId();
		String createTime = new BranchDateTimeFormatter(iBranch)
						.reformat(CapDate.parseToString(CapDate.getCurrentTimestamp()));
		
		OverSeaUtil.copyC120_ref(mainId, custId, dupNo, creator, createTime, c120m01a, c120s01a, c120s01b, c120s01c, c120s01d, c120s01e);				
	}
	
	@DomainAuth(AuthType.Modify)
	public IResult loadC121M_items(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);	
		
		if(Util.equals(OverSeaUtil.C121M01A_MOW_TYPE_泰國, meta.getMowType())){
			String varVer = Util.trim(meta.getVarVer());
			C121M01D c121m01d = clsService.findC121M01D_byC120M01A(c120m01a);
			C121M01H c121m01h = null;
			if(varVer.equals(OverSeaUtil.V2_0_LOAN_TH)){
				c121m01h = clsService.findC121M01H_byC120M01A(c120m01a);
			}
			
			String page = params.getString(EloanConstants.PAGE);
			if ("05".equals(page)) {
				LMSUtil.addMetaToResult(result, c121m01d, new String[]{ 
						"chkItemTHG1", "chkItemTHG2"
						, "chkItemTHS1"
						, "chkItemTHO1", "chkItemTHO2", "chkItemTHO3"
						, "sumRiskPt", "adj_pts", "adj_matchS", "adj_ui"
						, "pRating", "sRating"});
				_add_fmt_adj(result, c121m01d);
				if(Util.equals(varVer, OverSeaUtil.V2_0_LOAN_TH)){ //亞洲地區(泰國、越南)評等模型
					result.set("c121m01d_pRating", c121m01d.getPRating());
					result.set("c121m01d_sRating", c121m01d.getSRating());
					if(c121m01h != null){
						result.set("c121m01h_pRating", c121m01h.getPRating());
						result.set("c121m01h_sRating", c121m01h.getSRating());
					}
				}
			} else if ("06".equals(page)) {
				LMSUtil.addMetaToResult(result, c121m01d, new String[]{ 
						"pRating","sRating", "adjRating",
						"noAdj", "adjustStatus"
						, "adjustFlag", "adjustReason" });				
			} 
			if(true){
				//page 5,6都會有的欄位
				Map<String, String> custPosMap = retrialService.get_codeTypeWithOrder("lms1015_custPos");
				result.set("c120_custId", c120m01a.getCustId());
				result.set("c120_dupNo", c120m01a.getDupNo());
				result.set("c120_custName", c120m01a.getCustName());		
				result.set("c120_custPos", _custPosDesc(custPosMap, Util.equals("Y", c120m01a.getKeyMan())?OverSeaUtil.M:Util.trim(c120m01a.getCustPos())));
			}
		}
		
		return result;				
	}	
	
	/**
	 * 參考 build_chkItemInfoNcb(...)
	 * @param result
	 * @param c121m01d
	 */
	private void _add_fmt_adj(CapAjaxFormResult result , C121M01D c121m01d){
		if(true){
			String msg_g = "";
			if(c121m01d.getAdj_pts()==0){
				msg_g = prop_lms1035m01Page.getProperty("fmt.notchange");
			}else{
				msg_g = OverSeaUtil.color_red(MessageFormat.format(prop_lms1035m01Page.getProperty("fmt.downgrade"), Util.trim(c121m01d.getAdj_pts()*-1)));
			}
			result.set("fmt_adj_g", msg_g);	
		}
		
		if(true){
			String msg_s = "";
			if(Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHS1())){
				msg_s = OverSeaUtil.color_red(prop_lms1035m01Page.getProperty("fmt.sws.Y"));
			}else{
				msg_s = prop_lms1035m01Page.getProperty("fmt.sws.N");
			}			
			result.set("fmt_adj_s", msg_s);
		}

		if(true){
			String msg_o = "";
			if(Util.equals(UtilConstants.haveNo.有, c121m01d.getChkItemTHO3())){
				msg_o = OverSeaUtil.color_red(MessageFormat.format(prop_lms1035m01Page.getProperty("fmt.o.Y"), Util.trim(c121m01d.getSRating())));
			}else{
				msg_o = prop_lms1035m01Page.getProperty("fmt.o.N");
			}			
			result.set("fmt_adj_o", msg_o);
		}
	}
	private String _custPosDesc(Map<String, String> custPosMap, String custPos){
		
		String custPosDesc = ""; 
		if(Util.isNotEmpty(custPos)){
			custPosDesc = LMSUtil.getDesc(custPosMap, custPos);
			if(Util.notEquals(custPos, custPosDesc)){
				custPosDesc = (custPos+"."+custPosDesc);	
			}	
		}
		return custPosDesc;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult overSeaCLS_del(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		C120M01A c120m01a = clsService.findC120M01A_oid(params.getString(UI_C120_OID));
		
		String mainId = Util.trim(params.getString("mainId"));
		if(c120m01a==null){
			throw new CapMessageException(RespMsgHelper.getMainMessage(UtilConstants.AJAX_RSP_MSG.查無資料), getClass());
		}
		
		if(true){
			/*
			於「基本資料」刪除：delBaseData
			於「評等文件編製中」刪除：delRatingDocCust
			‧這裡應該是「簽報書」刪除「個別借款人」；「簽報書」刪除「評等文件」另寫 caseDocDelRatingDoc
			*/
			L120M01A l120m01a = clsService.findL120M01A_mainId(mainId);
			if(OverSeaUtil.isCaseDoc_CLS_rawBorrowerPanel(l120m01a)){
				
			}else{
				//陳復(述)、異常通報，沒有引入C121M01A
			}					
			//-------------
			clsService.delC120Relate(c120m01a);
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult calc_C121_score(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);			
		
		lms1035Service.del_noneRating_score(meta);
		
		//強迫重算
		lms1035Service.calc_C121_score(meta);	
				
		return result;
	}
	
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult setC120CustPosCustRlt(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		String mainOid = params.getString(EloanConstants.MAIN_OID);
		C121M01A meta = clsService.findC121M01A_oid(mainOid);
		String custPos = Util.trim(params.getString("custPos"));
		String o_custRlt = Util.trim(params.getString("o_custRlt"));
		
		C120M01A c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		if(meta!=null && c120m01a!=null){
			if(Util.equals(c120m01a.getKeyMan(), "Y")){
				//原本是主借人
			}else{
				c120m01a.setCustPos(custPos);
				c120m01a.setO_custRlt(o_custRlt);
				if(true){
					List<GenericBean> saveList = new ArrayList<GenericBean>();
					saveList.add(c120m01a);
					clsService.save(saveList);						
				}
				
				if(Util.equals(custPos, "M")){
					clsService.syncCustPosM(meta, c120m01a.getOid());
					set_titleInfo(result, meta);
				}	
			}
				
		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult convertToC121Param(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		
		C120M01A c120m01a = clsService.findC120M01A_oid(Util.trim(params.getString(UI_C120_OID)));
		if(c120m01a!=null){
			C121M01A c121m01a = clsService.findC121M01A(c120m01a);
			if(c121m01a!=null){
				result.set("mainOid", c121m01a.getOid());
				result.set("mainId", c121m01a.getMainId());
			}
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryLMS1035M02(PageParameters params)
			throws CapException {

		CapAjaxFormResult result = new CapAjaxFormResult();
		String c121m01d_oid = Util.trim(params.getString("c121m01d_oid"));
		if (Util.isNotEmpty(c121m01d_oid)) {
			C121M01D c121m01d = clsService.findC121M01D_oid(c121m01d_oid);
			if(c121m01d!=null){
				C121M01A c121m01a = clsService.findC121M01AByMainId(c121m01d.getMainId());
				C120M01A c120m01a = clsService.findC120M01A_mainId_idDup(c121m01d.getMainId()
						, c121m01d.getCustId(), c121m01d.getDupNo());
				if(true){
					LMSUtil.addMetaToResult(result, c121m01d, new String[]{
							//~~~~~~~~~~~~~~
							//part1
							"ownBrId"	
							, "custId", "dupNo"
							//~~~~~~~~~~~~~~
							//part2
							, "grdCDate", "jcicQDate", "ncbQDate"
							, "grdTDate"							
							, "item_m5", "scr_m5", "std_m5", "weight_m5"
							, "item_m7", "scr_m7", "std_m7", "weight_m7"							
							, "item_p3", "scr_p3", "std_p3", "weight_p3"
							, "item_p4", "scr_p4", "std_p4", "weight_p4"							 
							, "item_a5", "scr_a5", "std_a5", "weight_a5"
							, "item_z3", "scr_z3", "std_z3", "weight_z3"
							, "item_z1", "scr_z1", "std_z1", "weight_z1"
							, "item_z2", "scr_z2", "std_z2", "weight_z2"
							, "scr_core", "std_core"
							, "pRating"
							//~~~~~~~~~~~~~~
							//part5
							, "chkItemTHG1", "chkItemTHG2"
							, "chkItemTHS1"
							, "chkItemTHO1", "chkItemTHO2", "chkItemTHO3"
							, "sumRiskPt", "adj_pts", "adj_matchS", "adj_ui"
							//~~~~~~~~~~~~~~
							//part6
							, "noAdj", "adjustStatus", "adjRating", "adjustFlag", "adjustReason" 
							//~~~~~~~~~~~~~~
							//part7
							, "pRating", "sRating", "fRating" 
							, "varVer"
							});
				}			
				String caseNo = c121m01a.getCaseNo();
				//part1
				result.set("caseNo", caseNo);
				result.set("custName", c120m01a.getCustName());				
				result.set("creator", _id_name(c121m01d.getCreator()));
				result.set("createTime", Util.trim(TWNDate.valueOf(c121m01d.getCreateTime())));
				result.set("updater", _id_name(c121m01d.getUpdater()));
				result.set("updateTime",Util.trim(TWNDate.valueOf(c121m01d.getUpdateTime())));
				//part2
				result.set("approveTime",Util.trim(TWNDate.toAD(c121m01a.getApproveTime())));
				result.set("initRating", Util.trim(c121m01d.getPRating()));
				//part3
				_add_fmt_adj(result, c121m01d);
				//part4
				//=====================
				
				//=====================
				result.set("titleInfo", caseNo
						+" "+c120m01a.getCustId()+" "+"-"+c120m01a.getDupNo()
						+" "+c120m01a.getCustName());			
			}			
		}
		return result;
	}
	
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult delModelCompareFile(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String docOid = Util.trim(params.getString("docOid"));
		if(Util.isNotEmpty(docOid)){
			docFileService.clean(docOid);
		}
		return result;
	}
	
	public IResult callBatch(PageParameters params)
	throws CapException, IOException {

		int jq_timeout = Util.parseInt(params.getString("jq_timeout"));
		if(jq_timeout==0){
			jq_timeout = 60*60;//default
		}
		
		
		String act = Util.trim(params.getString("act"));		
		List<String> paramList = new ArrayList<String>();
		if(Util.equals(act, "runModelCompare")){
			paramList.add("docOid");
		}
		//---
		EloanSubsysBatReqMessage esbrm = new EloanSubsysBatReqMessage();
		esbrm.setUrl(SysParamConstants.SYS_URL_LMS);
		esbrm.setReqFormat(EloanSubsysBatReqMessage.REQ_FMT_JSON);
		esbrm.setServiceId("clsScoreBatchServiceImpl");
		esbrm.setTimeout(jq_timeout);
		esbrm.setLocalUrl(true);
		
		JSONObject requestJSON = new JSONObject();
		requestJSON.element("act", act);
		for(String k : paramList){
			requestJSON.element(k, params.getString(k));	
		}
		//---
		esbrm.setRequestJSON(requestJSON);
		
		String respStr = eloanBatchClient.send(esbrm);
		logger.debug("send to batch data={}",respStr);
		//=============
		CapAjaxFormResult result = new CapAjaxFormResult();
		result.set("r", respStr);
		return result;
	}
	
}