<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
    	<th:block th:fragment="innerPageBody">
			<style type="text/css">        		 
			 .dt_width{ display: inline-block; width : 70px;}
			</style>      
			<script type="text/javascript">
				loadScript('pagejs/lrs/LMS1700M02Page');
			</script>  	
            <div class="button-menu funcContainer" id="buttonPanelLNDetail">
            	<button type="button" id="btnSaveLNDetail">
					<span class="ui-icon ui-icon-jcs-04" ></span>
					<th:block th:text="#{'button.save'}">儲存</th:block>
				</button>	
				<button type="button" id="btnExitLNDetail" class="forview">
					<span class="ui-icon ui-icon-jcs-01"></span>
					<th:block th:text="#{'button.exit'}">離開</th:block>
				</button>
            </div>
			
			<form id="lnForm">
				<table class="tb2" width='100%' >
					<tr>
						<td class="noborder">
							<th:block th:text="#{'L170M01A.retrialDate'}">覆審日期</th:block>：<span id="retrialDate" ></span>
							&nbsp;&nbsp;
							<th:block th:text="#{'label.custInfo'}">借款人</th:block>：<span id="custInfo"></span>
						</td>
						<td class="noborder rt">
							<th:block th:text="#{'label.metaDocStatus'}">主文件狀態</th:block>：<span id="metaDocStatus" ></span>
						</td>
					</tr>
					<tr>
						<td class="noborder">
							<th:block th:text="#{'label.createBy'}">產生方式</th:block>：<span id="createBy" ></span>
							&nbsp;&nbsp;
						</td>
						<td class="noborder rt">
							<th:block th:text="#{'label.amtUnit'}">單位：仟元</th:block>
						</td>
					</tr>				
				</table>	
				<table class="tb2" width='100%' >
					<tr>
						<td class="hd1"><th:block th:text="#{'L170M01B.subject'}">授信科目</th:block></td>
						<td>
						<th:block th:if="${HS_SUBJECT_R}">	
							<span id="loanTP" ></span>&nbsp;&nbsp;<span id="subject" ></span>						
						</th:block>						
						<th:block th:if="${HS_SUBJECT_W}">
							<input type='text' id="subject" name="subject" size='40' maxlength='300' maxlengthC='100' />							
						</th:block>		
						&nbsp;
						</td>
						<td class="hd1"><th:block th:text="#{'label.newCase'}">新貸</th:block></td>
						<td>
							<select id="newCase" name="newCase" >
								<option value=""></option>
								<option value="Y"><th:block th:text="#{'label.newCase.Y'}"></th:block></option>
							</select>  
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'L170M01B.cntrNo'}">額度序號</th:block></td>
						<td>
						<th:block th:if="${HS_SYS_CNTRNO}">	
							<span id="cntrNo" ></span>						
						</th:block>
						<th:block th:if="${HS_PEO_CNTRNO}">
							<input type='text' id="cntrNo" name="cntrNo" size='15' maxlength='12' />
							<br/><span class='color-red'>※若無額度序號請打N.A.</span>
						</th:block>		
						
						&nbsp;
						</td>
						<td class="hd1" nowrap><th:block th:text="#{'label.revolve'}">循環/不循環動用</th:block></td>
						<td>
							<th:block th:if="${HS_SYS_REVOLVE}">
								<select id="revolve" name="revolve" disabled>
									<option value=""></option>
									<option value="Y"><th:block th:text="#{'label.revolve.Y'}"></th:block></option>
									<option value="N"><th:block th:text="#{'label.revolve.N'}"></th:block></option>
								</select>  
							</th:block>
							
						
							<th:block th:if="${HS_PEO_REVOLVE}">
								<select id="revolve" name="revolve">
										<option value=""></option>
										<option value="Y"><th:block th:text="#{'label.revolve.Y'}"></th:block></option>
										<option value="N"><th:block th:text="#{'label.revolve.N'}"></th:block></option>
									</select>							
							</th:block>	
						&nbsp;
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'label.quotaAmt'}">額度</th:block></td>
						<td>
							<th:block th:if="${HS_SYS_QUOTA}">	
								<span id="quotaCurr" ></span>&nbsp;&nbsp;<span id="quotaAmt" ></span>						
							</th:block>
							<th:block th:if="${HS_PEO_QUOTA}">	
								<select id="quotaCurr" name="quotaCurr" space="true" combokey="Common_Currcy" ></select>&nbsp;&nbsp;
								<input type='text' id="quotaAmt" name="quotaAmt" maxlength="16" integer="10" fraction="0" class="numeric" />						
							</th:block>
		
						
						&nbsp;
						</td>
						<td class="hd1"><th:block th:text="#{'label.balAmt'}">前日結欠餘額</th:block></td>
						<td nowrap>
							<select id="balCurr" name="balCurr" space="true" combokey="Common_Currcy" ></select>&nbsp;&nbsp;
							<!-- 單位為仟元，在DB是DECIMAL(15,2)表13.2，改成 10.5 -->
							<input type='text' id="balAmt" name="balAmt" maxlength="16" integer="10" fraction="0" class="numeric" />
						</td>
					</tr>
					<tr>
						<td class="hd1" nowrap><th:block th:text="#{'label.fromEndDate'}">動用期限或授信期間</th:block></td>
						<td colspan="3">
						<th:block th:if="${HS_SYS_FROMENDDATE}">
							<span id="fromDate" class="dt_width" ></span>&nbsp;～&nbsp;<span id="endDate" class="dt_width" ></span>							
						</th:block>
						<th:block th:if="${HS_PEO_FROMENDDATE}">
							<input id="fromDate" name="fromDate" type="text" size="8" maxlength="10" class="date" _requiredLength="10" />&nbsp;～&nbsp;<input id="endDate" name="endDate" type="text" size="8" maxlength="10" class="date" _requiredLength="10" />							
						</th:block>
		
						&nbsp;
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'L170M01B.guaranteeName'}">擔保品名稱</th:block></td>
						<td colspan="3">
							<textarea id="guaranteeName" name="guaranteeName" style="width:600px; height:75px;" maxlength='4200' maxlengthC='1400'></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'label.estAmt'}">勘估值</th:block></td>
						<td nowrap>
							<select id="estCurr" name="estCurr" space="true" combokey="Common_Currcy" ></select>&nbsp;&nbsp;
							<!-- 單位為仟元，在DB是DECIMAL(13,0)，改成 (10,0) -->
							<input type='text' id="estAmt" name="estAmt" maxlength="13" integer="10" fraction="0" class="numeric" />
						</td>
						<td class="hd1"><th:block th:text="#{'label.loanAmt'}">押值</th:block></td>
						<td nowrap>
							<select id="loanCurr" name="loanCurr" space="true" combokey="Common_Currcy" ></select>&nbsp;&nbsp;
							<input type='text' id="loanAmt" name="loanAmt" maxlength="13" integer="10" fraction="0" class="numeric" />
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'L170M01B.insMemo'}">保險相關資料</th:block></td>
						<td colspan="3">
							<textarea id="insMemo" name="insMemo" style="width:600px; height:75px;" maxlength='900' maxlengthC='300'></textarea>
						</td>
					</tr>
					<tr>
						<td class="hd1"><th:block th:text="#{'label.majorMemo'}">重要敘做條件摘要</th:block></td>
						<td colspan="3">
							<textarea id="majorMemo" name="majorMemo" style="width:600px; height:55px;" maxlength='2400' maxlengthC='800'></textarea>
						</td>
					</tr>
				</table>		
            </form>
    	</th:block>
    </body>
</html>

