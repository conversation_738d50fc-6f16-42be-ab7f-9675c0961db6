package com.mega.eloan.lms.mfaloan.bean;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Size;

import org.apache.wicket.markup.html.form.Check;
import org.springframework.jdbc.core.RowMapper;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.utils.CapBeanUtil;

/** 房仲、代書引介獎金檔 **/
public class ELF604 extends GenericBean {

	private static final long serialVersionUID = 1L;
	
	/** 放款帳號 **/
	@Column(name = "ELF604_LOAN_NO", length = 14, columnDefinition = "CHAR(14)")
	private String elf604_loan_no;
	
	/** 首次獎金登陸時間 **/
	@Column(name = "ELF604_1ST_DATE", columnDefinition = "DATE")
	private Date elf604_1st_date;
	
	/** 首次引介獎金 **/
	@Column(name = "ELF604_1ST_AMT", columnDefinition = "DECIMAL(15, 2))")
	private BigDecimal elf604_1st_amt;

	/** 最後獎金登陸時間 **/
	@Column(name = "ELF604_UPD_DATE", columnDefinition = "DATE")
	private Date elf604_upd_date;
	
	/** 最後引介獎金 **/
	@Column(name = "ELF604_UPD_AMT", columnDefinition = "DECIMAL(15, 2)")
	private BigDecimal elf604_upd_amt;
	
	/** get放款帳號 **/
	public String getElf604_loan_no() {
		return this.elf604_loan_no;
	}

	/** set放款帳號 **/
	public void setElf604_loan_no(String value) {
		this.elf604_loan_no = value;
	}
	
	/** get首次獎金登陸時間 **/
	public Date getElf604_1st_date() {
		return this.elf604_1st_date;
	}

	/** set首次獎金登陸時間 **/
	public void setElf604_1st_date(Date value) {
		this.elf604_1st_date = value;
	}
	
	/** get首次引介獎金 **/
	public BigDecimal getElf604_1st_amt() {
		return this.elf604_1st_amt;
	}

	/** set首次引介獎金 **/
	public void setElf604_1st_amt(BigDecimal value) {
		this.elf604_1st_amt = value;
	}
	
	/** get最後獎金登陸時間 **/
	public Date getElf604_upd_date() {
		return this.elf604_upd_date;
	}

	/** set最後獎金登陸時間 **/
	public void setElf604_upd_date(Date value) {
		this.elf604_upd_date = value;
	}
	
	/** get最後引介獎金 **/
	public BigDecimal getElf604_upd_amt() {
		return this.elf604_upd_amt;
	}

	/** set最後引介獎金 **/
	public void setElf604_upd_amt(BigDecimal value) {
		this.elf604_upd_amt = value;
	}
}
