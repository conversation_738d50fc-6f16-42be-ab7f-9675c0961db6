/* 
 * L140S10BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;

import com.mega.eloan.lms.dao.L140S10BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140S10B;

/** 其他敘作條件細項資訊檔 **/
@Repository
public class L140S10BDaoImpl extends LMSJpaDao<L140S10B, String>
	implements L140S10BDao {

	@Override
	public L140S10B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140S10B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public List<L140S10B> findByMainIdAndBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S10B findByMainIdAndBizCatAndBizItem(String mainId, String bizCat, String bizItem) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizItem", bizItem);
		return findUniqueOrNone(search);
	}
	
	@Override
	public List<L140S10B> findByMainIdAndBizCatAndSequence(String mainId, String bizCat, int[] seqArray) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addSearchModeParameters(SearchMode.IN, "seq", seqArray);
		search.addOrderBy("seq", false);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L140S10B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140S10B findMaxSeqNumByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addOrderBy("seq", true);
		return findUniqueOrNone(search);
	}
	
	@Override
	public L140S10B findMaxBizItemByMainIdAndBizCat(String mainId, String bizCat) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "bizCat", bizCat);
		search.addOrderBy("bizItem", true);
		return findUniqueOrNone(search);
	}
}