/*
 * GenericBean.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.model;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.kordamp.json.JSONArray;
import org.kordamp.json.JSONObject;
import org.springframework.util.ReflectionUtils;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.ADDateFormatter;
import tw.com.iisi.cap.formatter.ADDateTimeFormatter;
import tw.com.iisi.cap.formatter.IBeanFormatter;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapBeanUtil;

/**
 * <p>
 * GenericBean.
 * </p>
 * 
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/19,iristu,new
 *          <li>2011/8/02,sunkistWang,update {@link GenericBean#toJSONString(String[], Map)}, {@link GenericBean#toJSONObject(String[], Map)} for Calendar.
 *          <li>2011/08/08,iristu,新增IBeanFormatter
 *          <li>2011/10/05,iristu,get()新增取得GenericBean欄位值(gbean.field1).
 *          <li>2012/3/8,rodeschen,修改toJSONString 判別方式
 *          </ul>
 */
public class GenericBean {

    /**
     * set值
     * 
     * @param <T>
     *            T extends GenericBean
     * @param field
     *            欄位id
     * @param value
     *            欄位值
     * @return T <T>
     * @throws CapException
     */
    @SuppressWarnings("unchecked")
    public <T> T set(String field, Object value) throws CapException {
        if (CapString.isEmpty(field)) {
            return (T) this;
        }
        try {
            Field f = ReflectionUtils.findField(getClass(), field);
            if (f != null) {
                String setter = new StringBuffer("set").append(String.valueOf(f.getName().charAt(0)).toUpperCase()).append(f.getName().substring(1)).toString();
                Method method = ReflectionUtils.findMethod(this.getClass(), setter, new Class[] { f.getType() });
                if (method == null && Character.isUpperCase(f.getName().charAt(1))) {
                    // eclipse 3.5 以上版本產生的 getter/setter method name 處理為 pName =>
                    // getpName()
                    setter = new StringBuffer("set").append(f.getName()).toString();
                    method = ReflectionUtils.findMethod(this.getClass(), setter, new Class[] { f.getType() });
                }
                if (method != null) {
                    method.invoke(this, value);
                }
            }
        } catch (Exception e) {
            throw new CapException(new StringBuffer("field:").append(field).append(" ").append(e.getMessage()).toString(), e, getClass());
        }
        return (T) this;
    }

    /**
     * 取得某欄位的值
     * 
     * @param fieldId
     *            欄位名稱
     * @return Object
     */
    public Object get(String fieldId) throws CapException {
        if (CapString.isEmpty(fieldId)) {
            throw new CapException("field [" + fieldId + "] is empty!!", getClass());
        }
        try {
            String field = fieldId;
            int index = fieldId.indexOf(".");
            if (index > 0) {
                field = fieldId.substring(0, index);
                Object keyClazz = get(field);
                if (keyClazz instanceof GenericBean) {
                    return ((GenericBean) keyClazz).get(fieldId.substring(index + 1));
                }
            } else {
                String getter = new StringBuffer("get").append(String.valueOf(field.charAt(0)).toUpperCase()).append(field.substring(1)).toString();
                Method method = ReflectionUtils.findMethod(getClass(), getter);
                if (method == null) {
                    getter = "is" + getter.substring(3);
                    method = ReflectionUtils.findMethod(getClass(), getter);
                }
                if (method != null) {
                    return method.invoke(this);
                } else {
                    Field f = ReflectionUtils.findField(getClass(), field);
                    if (f != null) {
                        f.setAccessible(true);
                        return f.get(this);
                    }
                }
            }
            throw new CapException(new StringBuffer("field:").append(field).append(" is not exist!!").toString(), getClass());

        } catch (Exception e) {
            throw new CapException(e, getClass());
        }

    }// ;

    /** column split regularre char **/
    private static final String SPLIT = "\\|";

    /**
     * 取得每個欄位的值
     * 
     * @param columns
     *            顯示欄位
     * @param reformat
     *            Map<String, IFormatter>
     * @return String JsonString
     */
    public List<Object> toJSONString(String[] columns, Map<String, IFormatter> reformat) throws CapException {
        List<Object> row = new JSONArray();
        for (String str : columns) {
            Object val = null;
            try {
                try {
                    String[] s = str.split(SPLIT);
                    val = s.length == 1 ? get(s[0]) : get(s[1]);
                    str = s[0];
                } catch (Exception e) {
                    val = "";
                }
                if (reformat != null && reformat.containsKey(str)) {
                    IFormatter callback = reformat.get(str);
                    if (callback instanceof IBeanFormatter) {
                        val = callback.reformat(this);
                    } else {
                        val = callback.reformat(val);
                    }
                } else if (val instanceof Timestamp) {
                    val = new ADDateTimeFormatter().reformat(val);
                } else if (val instanceof Date || val instanceof Calendar) {
                    val = new ADDateFormatter().reformat(val);
                }
                row.add(String.valueOf(val));
            } catch (Exception e) {
                throw new CapException(e.getMessage(), e, getClass());
            }
        }
        return row;
    }

    /**
     * 取得每個欄位的值
     * 
     * @param columns
     *            顯示欄位
     * @param reformat
     *            Map<String, IFormatter>
     * @return String JsonString
     */
    public JSONObject toJSONObject(String[] columns, Map<String, IFormatter> reformat) throws CapException {
        JSONObject json = new JSONObject();
        if (columns == null) {
            Field[] cols = CapBeanUtil.getField(this.getClass(), true);// this.getClass().getDeclaredFields();
            columns = new String[cols.length];
            for (int i = 0; i < columns.length; i++) {
                columns[i] = cols[i].getName();
            }
        }
        for (String str : columns) {
            Object val = null;
            try {
                try {
                    val = get(str);
                } catch (Exception e) {
                    val = "";
                }
                if (reformat != null && reformat.containsKey(str)) {
                    IFormatter callback = reformat.get(str);
                    if (callback instanceof IBeanFormatter) {
                        val = callback.reformat(this);
                    } else {
                        val = callback.reformat(val);
                    }
                } else if (val instanceof Timestamp) {
                    val = new ADDateTimeFormatter().reformat(val);
                } else if (val instanceof Date || val instanceof Calendar) {
                    val = new ADDateFormatter().reformat(val);
                }
                json.element(str, val);
            } catch (Exception e) {
                throw new CapException(e.getMessage(), e, getClass());
            }
        }
        return json;
    }

    /**
     * 取得字串
     * 
     * @return string
     */
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE, false, false);
    }

}
