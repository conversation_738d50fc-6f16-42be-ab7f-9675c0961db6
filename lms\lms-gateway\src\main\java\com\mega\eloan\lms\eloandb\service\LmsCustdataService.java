package com.mega.eloan.lms.eloandb.service;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * MIS.CUSTDATA
 * </pre>
 * 
 * @since 2012/2/18
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/18,jessica,new
 *          </ul>
 */
public interface LmsCustdataService {

	/**
	 * 找 客戶資料(CNAME,ENAME,BUSCD)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> findCustDataCname(String custId, String dupNo);

	/**
	 * 找客戶資料
	 * 
	 * @param custId
	 *            客戶編號
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> findCustDataByCustId(String custId);

}
