package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.pages.LMS8200M01Page;
import com.mega.eloan.lms.fms.service.LMS8200Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
//import com.mega.eloan.lms.model.L140MM3A;
//import com.mega.eloan.lms.model.L140MM3C;
import com.mega.eloan.lms.model.L820M01A;
import com.mega.eloan.lms.model.L820M01C;
import com.mega.eloan.lms.model.L820M01E;
import com.mega.eloan.lms.model.L820M01S;
import com.mega.eloan.lms.model.L820M01W;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 以房養老貸款撥款前查詢結果
 * </pre>
 * 
 * @since 2022/10/31
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8200gridhandler")
public class LMS8200GridHandler extends AbstractGridHandler {

	@Resource
	LMS8200Service lms8200Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	EloandbBASEService eloandbService;
	
	@Resource
	DwLnquotovService dwLnquotovService;
	
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	/**
	 * 以房養老貸款撥款前查詢結果grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL820m01A(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, 
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms8200Service.findPage(
				L820M01A.class, pageSetting);

		List<L820M01A> l820m01alist = (List<L820M01A>) page.getContent();

		return new CapGridResult(l820m01alist, page.getTotalRow());

	}

	
	public CapGridResult queryFile(ISearch pageSetting, PageParameters params) throws CapException {

		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params.getString("mainId"));
		String fieldId = params.getString("fieldId");
		boolean needCngName = params.getBoolean("needCngName");
		boolean needBranch = params.getBoolean("needBranch");
		pageSetting
				.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "fieldId",
				fieldId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);
		
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}
	
	/**
	 * 以房養老貸款撥款查詢明細grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL820m01C(ISearch pageSetting, PageParameters params) 
	throws CapException {
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		//String custId = Util.trim(params.getString("custId"));
		//String dupNo = Util.trim(params.getString("dupNo"));
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		List<L820M01C> l820m01clist = (List<L820M01C>) lms8200Service.findListByMainId(L820M01C.class, mainId);
		
		if (!Util.isEmpty(l820m01clist)) {
			for(L820M01C l820m01c : l820m01clist){
				String oid = l820m01c.getOid();
				String cmainid = l820m01c.getMainId();
				String custId = l820m01c.getCustId();
				String dupNo = l820m01c.getDupNo();
				String cntrNo = l820m01c.getCntrNo();
				String lnf030_loan_no = l820m01c.getLnf242_loan_no();
				String estimateTime = CapDate.getDateTimeFormat(l820m01c.getEstimateTime()).substring(0, 10);
				String actualTime = CapDate.getDateTimeFormat(l820m01c.getActualTime()).substring(0, 10);
				
				Map<String, Object> row = this.build_caseinfData_row(oid, cmainid, custId, dupNo, cntrNo, lnf030_loan_no, estimateTime, actualTime);
				list.add(row);
			}
		}
		
		return new CapMapGridResult(list, list.size());		
	}
	
	public CapMapGridResult queryDataArchivalRecordData(ISearch pageSetting, PageParameters params) 
	throws CapException {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));		
		String icon_ok = "○";
		String icon_warn = "▲";
		
		Properties prop_lms8200 = MessageBundleScriptCreator.getComponentResource(LMS8200M01Page.class);
		if (Util.isNotEmpty(custId)) {
			if (true) {
				
				if(true){ // 資料建檔相關data
					List<L820M01S> m01s_list = new ArrayList<L820M01S>();
					m01s_list.addAll(lms8200Service.findL820M01S_byIdDupDataType(mainId, custId, dupNo, "5"));//內政部國民身分證領換補資料
					for(L820M01S l820m01s : m01s_list){
						String oid = l820m01s.getOid();						
						String dataType = l820m01s.getDataType();
						String dataTypeDesc = l820m01s_dataTypeDesc(prop_lms8200, l820m01s.getDataType());
						String fileSeq = l820m01s.getFileSeq();
						String queryTime = CapDate.getDateTimeFormat(l820m01s.getDataCreateTime());
						String link = prop_lms8200.getProperty("L820M01S.open");
						String dataStatus = l820m01s_dataStatus(icon_ok, icon_warn, l820m01s.getDataStatus());
						String remark =  "";
						String dataSrcMemo = "L820M01S";
						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");
						row.put("reportFileType", l820m01s.getReportFileType());
						//-----------
						list.add(row);				
					}
				}
				
				// RPA 司法院受監護/輔助宣告資料
				L820M01W l820m01w = this.rpaProcessService.getL820M01WBy(mainId, custId);
				if (null != l820m01w) {
					String oid = l820m01w.getOid();
					String dataType = CrsUtil.RPA_TXID_FA;
					String dataTypeDesc = CrsUtil.RPA_TXID_FA_DESC;
					String fileSeq = "0";
					String queryTime = CapDate.getDateTimeFormat(l820m01w.getQueryTime());
					String link = prop_lms8200.getProperty("L820M01S.open");
					String dataStatus = LMSUtil.getRpaStatusMap().get(l820m01w.getStatus());
					String remark = l820m01w.getMemo();
					String dataSrcMemo = "L820M01W";
					String dataStatusCode = l820m01w.getStatus();
					Map<String, Object> row = build_queryDataArchivalRecordData_row(
							oid, mainId, dataType, dataTypeDesc, fileSeq,
							queryTime, link, dataStatus, remark, dataSrcMemo,
							dataStatusCode);
					// -----------
					list.add(row);
				}
				
				// J-113-0392 財富管理往來查詢
				L820M01E l820m01e = lms8200Service.findL820M01EByUniqueKey(mainId, custId, dupNo);
				if(l820m01e != null){
					String oid = l820m01e.getOid();
					String dataType = CrsUtil.Wealth;
					String dataTypeDesc = CrsUtil.Wealth_DESC;
					String fileSeq = "0";
					String queryTime = CapDate.getDateTimeFormat(l820m01e.getQueryTime());
					String link = prop_lms8200.getProperty("L820M01E.open");
					String dataStatus = l820m01e_dataStatus(icon_ok, icon_warn,
							l820m01e.getDataSearchResult());
					String remark = "";
					String dataSrcMemo = "L820M01E";
					Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");
					// -----------
					list.add(row);
				}
			}

		}
		return new CapMapGridResult(list, list.size());
	}
	
	private Map<String, Object> build_queryDataArchivalRecordData_row(String oid, String mainId,
			String dataType, String dataTypeDesc, String fileSeq, String queryTime, String link, String dataStatus, String remark, String dataSrcMemo, String dataStatusCode){
		Map<String, Object> row = new HashMap<String, Object>();
		row.put("oid", oid);
		row.put("mainId", mainId);
		row.put("dataType", dataType);
		row.put("dataTypeDesc", dataTypeDesc);
		row.put("fileSeq", fileSeq);
		row.put("queryDate", queryTime);
		row.put("link", link);
		row.put("dataStatus", dataStatus);
		row.put("remark", remark);
		row.put("dataSrcMemo", dataSrcMemo);
		row.put("dataStatusCode", dataStatusCode);
		return row;
	}
	
	private Map<String, Object> build_caseinfData_row(String oid, String mainId,
			String custId, String dupNo, String cntrNo, String lnf030_loan_no, String estimateTime, String actualTime){
		Map<String, Object> row = new HashMap<String, Object>();

		row.put("oid", oid);
		row.put("mainId", mainId);
		row.put("custId", custId);
		row.put("dupNo", dupNo);
		row.put("cntrNo", cntrNo);
		row.put("lnf030_loan_no", lnf030_loan_no);
		row.put("estimateTime", estimateTime);
		row.put("actualTime", actualTime);
		return row;
	}
	
	/**
	 * 查詢都更危老註記維護作業grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */	
	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
			PageParameters params) throws CapException {
		String custId = Util.nullToSpace(params.getString("custId"));
		String dupNo = Util.nullToSpace(params.getString("dupNo"));

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<String> cntrnoList = new ArrayList<String>();
		
		if(!custId.isEmpty() && !dupNo.isEmpty()){
			//簽報書的額度明細表額度序號
			//取得 LN.LNF242 以房養老控制檔
			//List<Map<String, Object>> l140m01as = misdbBASEService.selDistinctCntrnoByCustidDupno(custId, dupNo);
			List<Map<String, Object>> l820m01_contractas = misdbBASEService.get_LNF242_Contractno(custId, dupNo, null);
			
			for (Map<String, Object> l820m01a : l820m01_contractas) {
				Map<String, Object> row = new HashMap<String, Object>();
	
				String cntrNo = Util.trim(l820m01a.get("LNF242_CONTRACT"));
				if(!cntrNo.isEmpty()){
					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
						//排除重複
					} else {
						cntrnoList.add(cntrNo);	
						row.put("cntrNo", cntrNo);
						list.add(row);
					}
				}
			}
		
			//排序
			Collections.sort(list, new Comparator<Map<String, Object>>() {
				public int compare(Map<String, Object> o1, Map<String, Object> o2) {
					String name1 = o1.get("cntrNo").toString(); 
					String name2 = o2.get("cntrNo").toString();
	                return name1.compareTo(name2);
				}
			});
		}
		
		return new CapMapGridResult(list, list.size());
	}
	
	private String l820m01s_dataTypeDesc(Properties prop_lms8200, String dataType){
		if(Util.equals("5", dataType)){
			//C101S01S.api.idCardCheckData=內政部國民身分證領換補資料
			return prop_lms8200.getProperty("L820M01S.api.idCardCheckData");
		}
		return dataType;		
	}
	
	/** dataStatus 內 ，0代表正常。把 0去掉後，若有非0的字元，代表需出現「警示」  */
	private String l820m01s_dataStatus(String icon_ok, String icon_warn, String raw_dataStatus){
		String dataStatus = Util.trim(raw_dataStatus);	
		if(Util.isEmpty(dataStatus)){
			return "";
		}else{
			if(dataStatus.replaceAll("0", "").length()>0){
				return icon_warn;
			}else{
				return icon_ok;
			}
		}		
	}
	/** 
	 * l820m01e dataSearchResult Y:通過  N:不通過
	 * @param icon_ok
	 * @param icon_warn
	 * @param raw_dataStatus
	 * @return
	 */
	private String l820m01e_dataStatus(String icon_ok, String icon_warn, String raw_dataStatus){
		String dataStatus = Util.trim(raw_dataStatus);	
		if(Util.equals(UtilConstants.DEFAULT.是, dataStatus)){
			//Y 成功
			return icon_ok;
		}else{
			//N 失敗
			return icon_warn;
		}
	}
}