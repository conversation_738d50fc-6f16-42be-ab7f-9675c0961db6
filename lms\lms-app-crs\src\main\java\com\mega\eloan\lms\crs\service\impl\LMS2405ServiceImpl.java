/* 
 * LMS2405ServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.crs.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.PropUtil;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;
import tw.com.jcs.flow.service.FlowService;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.dao.DocFileDao;
import com.mega.eloan.common.enums.DocLogEnum;
import com.mega.eloan.common.enums.TypCdEnum;
import com.mega.eloan.common.enums.UnitTypeEnum;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.DocLogService;
import com.mega.eloan.common.service.TempDataService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.BranchRate;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.crs.pages.LMS2405M01Page;
import com.mega.eloan.lms.crs.service.LMS2405Service;
import com.mega.eloan.lms.crs.service.LMS2415Service;
import com.mega.eloan.lms.dao.C240A01ADao;
import com.mega.eloan.lms.dao.C240M01ADao;
import com.mega.eloan.lms.dao.C240M01BDao;
import com.mega.eloan.lms.dao.C240M01ZDao;
import com.mega.eloan.lms.dao.C241A01ADao;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dw.service.DWAslndavgovsService;
import com.mega.eloan.lms.dw.service.DwFxrthovsService;
import com.mega.eloan.lms.eloandb.service.Dw_elf490ovsService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.eloandb.service.Lms491Service;
import com.mega.eloan.lms.mfaloan.service.MisCustdataService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.C240A01A;
import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.C240M01B;
import com.mega.eloan.lms.model.C240M01Z;
import com.mega.eloan.lms.model.C241A01A;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

@Service
public class LMS2405ServiceImpl extends AbstractCapService implements
		LMS2405Service {

	private static Logger logger = LoggerFactory
			.getLogger(LMS2405ServiceImpl.class);

	@Resource
	EloandbBASEService eloandbBASEService;
	
	@Resource
	DocFileDao docFileDao;

	@Resource
	C240M01ADao c240m01aDao;

	@Resource
	C240M01BDao c240m01bDao;
	
	@Resource
	C240M01ZDao c240m01zDao;

	@Resource
	C241M01ADao c241m01aDao;

	@Resource
	C241M01BDao c241m01bDao;

	@Resource
	C240A01ADao c240a01aDao;

	@Resource
	C241A01ADao c241a01adao;

	@Resource
	LMS2415Service lms2415service;

	@Resource
	LMSService lmsService;

	@Resource
	NumberService numberService;

	@Resource
	BranchService branch;

	@Resource
	CodeTypeService codeType;

	@Resource
	FlowService flowService;

	@Resource
	DocLogService docLogService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	DWAslndavgovsService dwAslndavgovsService;

	@Resource
	Dw_elf490ovsService dwElf490ovsService;

	@Resource
	MisdbBASEService misdbBaseService;

	@Resource
	MisCustdataService lmsCustdataService;

	@Resource
	DwFxrthovsService dwFxrthovsService;

	@Resource
	Lms491Service lms491Service;

	@Resource
	TempDataService tempDataService;

	@Autowired
	DocFileService fileService;

	@Override
	public void startFlow(String oid, String flow_code) throws FlowException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		FlowInstance inst = flowService.createQuery().id(oid).query();

		if ("".equals(Util.nullToSpace(flow_code))) {
			flow_code = "LMS2405Flow";
		}
		if (inst == null) {
			logger.debug("流程啟動：流程代號[{}]OID[{}]", flow_code, oid);
			flowService.start(flow_code, oid, user.getUserId(),
					user.getUnitNo());
		} else {
			if (flow_code.equals(inst.getDefinition().getName())) {
				logger.warn("欲啟動之流程已存在：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
			} else {
				logger.error("尚未結束原流程：流程名稱[{}]OID[{}]", inst.getDefinition()
						.getName(), oid);
				throw new FlowException("FlowError: oid[" + oid + "]name["
						+ inst.getDefinition().getName() + "]");
			}
		}
	}

	/**
	 * 跑flow
	 * 
	 * @param mainOid
	 * @param model
	 * @param setResult
	 * @param resultType
	 * @throws Throwable
	 */
	public void flowAction(String mainOid, GenericBean model,
			boolean setResult, boolean resultType)
			throws CapException {
		if (model instanceof C240M01A) {
			C240M01A c240m01a = (C240M01A) model;
			if (RetrialDocStatusEnum.編製中.getCode().equals(
					c240m01a.getDocStatus())) {
				save(c240m01a);
			} else {
				c240m01aDao.save(c240m01a);
			}
		}
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			FlowInstance inst = flowService.createQuery().id(mainOid).query();
			if (inst == null) {
				inst = flowService.start("LMS2405Flow", mainOid,
						user.getUserId(), user.getUnitNo());
			}
			if (setResult) {
				inst.setDeptId(user.getUnitNo());
				inst.setUserId(user.getUserId());
				inst.setAttribute("result", resultType ? "核定" : "退回");
			}
			String docStatus = ((C240M01A) model).getDocStatus();
			if (RetrialDocStatusEnum.編製中.getCode().equals(docStatus)) {
				getNumber(mainOid, false);
			}
			inst.next();
			// 傳送分行報告表
			if (RetrialDocStatusEnum.已核准.getCode().equals(docStatus)) {
				C240M01A c240m01a = (C240M01A) model;
				if (c240m01a != null) {
					List<C240M01B> c240m01bs = c240m01bDao
							.findByMainId(c240m01a.getMainId());
					for (C240M01B c240m01b : c240m01bs) {
						C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
								.getRefMainId());
						if (!UtilConstants.DEFAULT.否.equals(c241m01a
								.getRetrialYN())) {
							// c241m01a.setRetrialDate(c240m01a
							// .getExpectedRetrialDate());
							save(c241m01a);
							lms2415service.startFlow(c241m01a.getOid(),
									"LMS2415Flow");
						}
					}
				}
			}
		} finally {

		}
	}

	@Override
	public void deleteC240M01A(String oid) {
		C240M01A c240m01a = c240m01aDao.findByOid(oid);
		if (!c240m01a.getMainId().isEmpty()) {
			String mainId = c240m01a.getMainId();
			c241m01bDao.deleteByC240M01AMainid(mainId);
			c241a01adao.deleteByC240M01AMainid(mainId);
			c241m01aDao.deleteByC240M01AMainid(mainId);
			c240m01bDao.deleteByC240M01AMainid(mainId);
		}
		delete(C240M01A.class, oid);
	}

	@Override
	public void deleteMainMark(String oid) {
		C240M01A c240m01a = c240m01aDao.findByOid(oid);
		if (c240m01a != null) {
			List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
					.getMainId());
			for (C240M01B c240m01b : c240m01bs) {
				C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
						.getRefMainId());
				// C241M01A c241m01a = (C241M01A) c240m01b.getC241m01a();
				c241m01a.setDeletedTime(CapDate.getCurrentTimestamp());
				c241m01aDao.save(c241m01a);
			}
			c240m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			c240m01aDao.save(c240m01a);
		}
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid) {
		if (clazz == C240M01A.class) {
			return (T) c240m01aDao.find(oid);
		} else if (clazz == C241M01A.class) {
			return (T) c241m01aDao.find(oid);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search) {
		if (clazz == C240M01A.class) {
			return c240m01aDao.findPage(search);
		} else if (clazz == C240M01B.class) {
			return c240m01bDao.findPage(search);
		} else if (clazz == C241M01A.class) {
			return c241m01aDao.findPage(search);
		} else if (clazz == DocFile.class) {
			return docFileDao.findPage(search);
		}
		return null;
	}

	@Override
	public void save(GenericBean... entity) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		for (GenericBean model : entity) {
			if (model != null) {
				if (model instanceof C240M01A) {
					C240M01A c240m01a = (C240M01A) model;
					if (RetrialDocStatusEnum.編製中.getCode().equals(
							c240m01a.getDocStatus())) {
						c240m01a.setHqAppraiserId(user.getUserId());
						c240m01a.setUpdater(user.getUserId());
						c240m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					}

					if (c240m01a.getCreator() == null) {
						c240m01a.setCreator(user.getUserId());
						c240m01a.setCreateTime(CapDate.getCurrentTimestamp());
					}
					c240m01a.setRandomCode(IDGenerator.getRandomCode());
					String oid = c240m01a.getOid();
					c240m01aDao.save(c240m01a);
					if (!Util.isEmpty(oid)) {
						docLogService
								.record(c240m01a.getOid(), DocLogEnum.SAVE);
					}
					if (!"Y".equals(SimpleContextHolder
							.get(EloanConstants.TEMPSAVE_RUN))) {
						tempDataService.deleteByMainId(c240m01a.getMainId());
					}
				} else if (model instanceof C240M01B) {
					((C240M01B) model).setUpdater(user.getUserId());
					((C240M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (((C240M01B) model).getCreator() == null) {
						((C240M01B) model).setCreator(user.getUserId());
						((C240M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c240m01bDao.save(((C240M01B) model));
					docLogService.record(((C240M01B) model).getOid(),
							DocLogEnum.SAVE);
				} else if (model instanceof C241M01A) {
					((C241M01A) model).setUpdater(user.getUserId());
					((C241M01A) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (((C241M01A) model).getCreator() == null) {
						((C241M01A) model).setCreator(user.getUserId());
						((C241M01A) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c241m01aDao.save(((C241M01A) model));
					docLogService.record(((C241M01A) model).getOid(),
							DocLogEnum.SAVE);
				} else if (model instanceof C241M01B) {
					((C241M01B) model).setUpdater(user.getUserId());
					((C241M01B) model).setUpdateTime(CapDate
							.getCurrentTimestamp());

					if (((C241M01B) model).getCreator() == null) {
						((C241M01B) model).setCreator(user.getUserId());
						((C241M01B) model).setCreateTime(CapDate
								.getCurrentTimestamp());
					}
					c241m01bDao.save(((C241M01B) model));
					docLogService.record(((C241M01B) model).getOid(),
							DocLogEnum.SAVE);
				} else if (model instanceof C240A01A) {
					c240a01aDao.save(((C240A01A) model));
					docLogService.record(((C240A01A) model).getOid(),
							DocLogEnum.SAVE);
				} else if (model instanceof C241A01A) {
					c241a01adao.save(((C241A01A) model));
					docLogService.record(((C241A01A) model).getOid(),
							DocLogEnum.SAVE);
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void delete(Class clazz, String oid) {
		if (clazz == C240M01A.class) {
			C240M01A c240m01a = c240m01aDao.findByOid(oid);
			c240m01aDao.delete(c240m01a);
			docLogService.record(c240m01a.getOid(), DocLogEnum.DELETE);
		} else if (clazz == C240M01B.class) {
			C240M01B c240m01b = c240m01bDao.findByOid(oid);
			c240m01bDao.delete(c240m01b);
			docLogService.record(c240m01b.getOid(), DocLogEnum.DELETE);
		}
	}

	@Override
	public void deleteC241M01A(String oid) {
		C240M01A c240m01a = c240m01aDao.findByOid(oid);
		List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
				.getMainId());

		for (C240M01B c240m01b : c240m01bs) {
			C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
					.getRefMainId());
			if (c241m01a != null) {
				String remomo = c241m01a.getRetrialKind();
				if (remomo != null && !remomo.isEmpty()
						&& remomo.indexOf("8-1") > -1) {
					lms2415service.deleteC241M01A(c241m01a.getOid());
					delete(C240M01B.class, c240m01b.getOid());
				}
			}
		}
	}

	@Override
	public int deleteC241M01AAndC240M01B(String oid) {
		String mainId = "";
		int count = 0;
		C241M01A c241m01a = c241m01aDao.findByOid(oid);
		List<C240M01B> c240m01bs = c240m01bDao.findByIndex01(null,
				c241m01a.getMainId());

		if (c241m01a != null) {
			for (C240M01B c240m01b : c240m01bs) {
				mainId = c240m01b.getMainId();
			}
			C240M01A c240m01a = c240m01aDao.findByMainId(mainId);
			count = c240m01a.getReQuantity();
			count = count - 1;

			lms2415service.deleteC241M01A(c241m01a.getOid());
			c240m01a.setReQuantity(count);
			this.save(c240m01a);
		}
		if (c240m01bs != null) {
			c240m01bDao.delete(c240m01bs);
		}
		return count;
	}

	@Override
	public boolean getNumber(String oid, boolean reGet)
			throws CapMessageException {
		C240M01A c240m01a = findModelByOid(C240M01A.class, oid);
		if (c240m01a.getBatchNO() == null || c240m01a.getBatchNO() == 0) {
			reGet = true;
			int batchNo = Util.parseInt(numberService.getNumberWithMax(
					C240M01A.class, c240m01a.getBranchId(),
					TWNDate.toAD(c240m01a.getDataEndDate()).substring(0, 4),
					999));
			c240m01a.setBatchNO(batchNo);
			save(c240m01a);
		}
		if (reGet) {
			projectSeq(c240m01a.getMainId());
		} else {
			getNum(c240m01a.getMainId());
		}

		return true;
	}

	@Override
	public void projectSeq(String mainId) {
		C240M01A c240m01a = c240m01aDao.findByMainId(mainId);
		ISearch isearch = c241m01aDao.createSearchTemplete();
		isearch.addSearchModeParameters(SearchMode.EQUALS, "c240m01b.mainId",
				mainId);
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("retrialYN", true);
		map.put("custId", false);
		map.put("lastRetrialDate", false);
		isearch.setOrderBy(map);

		List<C241M01A> c241m01as = c241m01aDao.find(isearch);
		// List<C241M01A> c241m01as = new ArrayList();
		// for (int i = 0, size = c240m01bs.size(); i < size; i++) {
		// C240M01B c240m01b = c240m01bs.get(i);
		// C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
		// .getRefMainId());
		// c241m01as.add(c241m01a);
		// }
		// Collections.sort(c241m01as, new Comparator<C241M01A>() {
		// // 排序規則(依順序)：是否覆審、借款人ID、上次覆審日
		// public int compare(C241M01A o1, C241M01A o2) {
		// if (!o1.getRetrialYN().equals(o2.getRetrialYN())) {
		// if (UtilConstants.DEFAULT.是.equals(o1.getRetrialYN())) {
		// return -1;
		// } else {
		// return 1;
		// }
		// }
		//
		// if (!o1.getCustId().equals(o2.getCustId())) {
		// if (o1.getCustId().length() < o2.getCustId().length()) {
		// return -1;
		// } else if(o1.getCustId().charAt(0) < o2.getCustId().charAt(0)){
		// return -1;
		// } else {
		// return 1;
		// }
		// }
		//
		// if(Util.isEmpty(o1.getLastRetrialDate())){
		// return 0;
		// }else if(Util.isEmpty(o2.getLastRetrialDate())){
		// return 0;
		// }else if (o1.getLastRetrialDate().compareTo(o2.getLastRetrialDate())
		// != 0) {
		// return o1.getLastRetrialDate().compareTo(
		// o2.getLastRetrialDate());
		// }
		// return 0;
		// }
		// });

		int count = 1;
		for (int i = 0, size = c241m01as.size(); i < size; i++) {
			C241M01A c241m01a = c241m01as.get(i);
			String casenumber = "";
			if (UtilConstants.DEFAULT.是.equals(c241m01a.getRetrialYN())) {
				casenumber = numberService
						.getCaseNumber(C240M01A.class, c240m01a
								.getBranchId(),
								TWNDate.toAD(c240m01a.getDataEndDate())
										.substring(0, 4),
								Util.addZeroWithValue(c240m01a.getBatchNO(), 3)
										+ "-" + Util.addZeroWithValue(count, 3));
			}
			c241m01a.setProjectNo(casenumber);
			count++;
		}

		c241m01aDao.save(c241m01as);
	}

	/**
	 * 取最大號續編
	 * 
	 * @param parent
	 * @param mainId
	 */
	private void getNum(String mainId) {
		C240M01A c240m01a = c240m01aDao.findByMainId(mainId);
		List<C240M01B> c240m01bs = c240m01bDao.findByMainId(mainId);
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		int max = 0;
		for (int i = 0, size = c240m01bs.size(); i < size; i++) {
			C240M01B c240m01b = c240m01bs.get(i);
			C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
					.getRefMainId());
			if (!Util.isEmpty(c241m01a.getProjectNo())) {
				String proNo = c241m01a.getProjectNo().substring(
						c241m01a.getProjectNo().length() - 4,
						c241m01a.getProjectNo().length() - 1);
				if (Util.isInteger(proNo)) {
					int seqno = Integer.valueOf(proNo);
					if (seqno > max) {
						max = seqno;
					}
				}
			}
			c241m01as.add(c241m01a);
		}
		ISearch isearch = c241m01aDao.createSearchTemplete();
		isearch.addSearchModeParameters(SearchMode.EQUALS, "c240m01b.mainId",
				mainId);
		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("retrialYN", true);
		map.put("custId", false);
		map.put("lastRetrialDate", false);
		isearch.setOrderBy(map);

		c241m01as = c241m01aDao.find(isearch);
		// Collections.sort(c241m01as, new Comparator<C241M01A>() {
		// // 排序規則(依順序)：是否覆審、借款人ID、上次覆審日
		// public int compare(C241M01A o1, C241M01A o2) {
		// if (!o1.getRetrialYN().equals(o2.getRetrialYN())) {
		// if (UtilConstants.DEFAULT.是.equals(o1.getRetrialYN())) {
		// return -1;
		// } else {
		// return 1;
		// }
		// }
		//
		// if (!o1.getCustId().equals(o2.getCustId())) {
		// if (o1.getCustId().length() < o2.getCustId().length()) {
		// return -1;
		// }else if(o1.getCustId().charAt(0) > o2.getCustId().charAt(0)){
		// return -1;
		// } else {
		// return 1;
		// }
		// }
		//
		// if(Util.isEmpty(o1.getLastRetrialDate())){
		// return 1;
		// }else if(Util.isEmpty(o2.getLastRetrialDate())){
		// return -1;
		// }else if (o1.getLastRetrialDate().compareTo(o2.getLastRetrialDate())
		// != 0) {
		// return o1.getLastRetrialDate().compareTo(
		// o2.getLastRetrialDate());
		// }
		// return 0;
		// }
		// });

		for (int i = 0, size = c241m01as.size(); i < size; i++) {
			C241M01A c241m01a = c241m01as.get(i);
			String casenumber = "";
			if (Util.isEmpty(c241m01a.getProjectNo())
					&& UtilConstants.DEFAULT.是.equals(Util.trim(c241m01a
							.getRetrialYN()))) {
				max++;
				casenumber = numberService
						.getCaseNumber(C240M01A.class, c240m01a
								.getBranchId(),
								TWNDate.toAD(c240m01a.getDataEndDate())
										.substring(0, 4),
								Util.addZeroWithValue(c240m01a.getBatchNO(), 3)
										+ "-" + Util.addZeroWithValue(max, 3));
				c241m01a.setProjectNo(casenumber);
			}
		}

		c241m01aDao.save(c241m01as);
	}

	/**
	 * 比較下次覆審日
	 * 
	 * @param finalDate
	 * @param compare
	 * @return
	 */
	public Date changeDate(Date finalDate, Date compare) {
		if (finalDate.after(compare)) {
			return compare;
		}
		return finalDate;
	}

	/**
	 * 判斷是否循環
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public boolean revovleOrNot(String custId, String dupNo) {
		List a = dwAslndavgovsService
				.findAslndavgovsJoinLnquotov(custId, dupNo);
		Iterator lnf = a.iterator();
		while (lnf.hasNext()) {
			Map map = (Map) lnf.next();
			String revovle = Util.trim(map.get("REVOLVE"));
			if ("Y".equals(revovle)) {
				return true;
			}
		}
		return false;
	}

	public boolean findStatus(String status) {
		// List<?> list = misLNF020Service.findLNF030SelStatus(loanNo);
		// List<?> list = dwAslndavgovsService.findAslndavgovsSelStatus(loanNo);
		// Iterator it = list.iterator();
		// while (it.hasNext()) {
		// Map map = (Map) it.next();

		// String status = Util.trim((String) map.get("STATUS"));
		if ("1".equals(status)) {
			return false;
		} else if ("2".equals(status) || "3".equals(status)
				|| "4".equals(status)) {
			return true;
		}
		// }
		return false;
	}

	/**
	 * 搜尋是否為行員
	 * 
	 * @param custId
	 * @return
	 */
	@Override
	public boolean findstaff(String custId) {
		Map<String, Object> map = misdbBaseService.findMISStaffSelAll(custId);
		int count = (Integer) map.get("COUNT");
		if (count != 0) {
			return true;
		}
		return false;
	}

	@SuppressWarnings("rawtypes")
	public boolean estateOrNot(String custId, String dupNo) {
		List lnf150 = dwAslndavgovsService.findAslndavgovsSelQuotano(custId,
				dupNo);
		Iterator lnf = lnf150.iterator();
		while (lnf.hasNext()) {
			Map map = (Map) lnf.next();
			String lnf150LoanNo = Util.trim(map.get("LOAN_NO"));
			if (!lnf150LoanNo.isEmpty()) {
				// 排除逾催呆戶 fnDB2GetLNF030
				if (findStatus(Util.trim(map.get("STATUS")))) {
					return true;
				}
			}
			String loancode = Util.trim(map.get("LOANCODE"));
			if ("13506200".equals(loancode) || "13506300".equals(loancode)
					|| "14501500".equals(loancode)
					|| "14502000".equals(loancode)
					|| "13500100".equals(loancode)
					|| "14501000".equals(loancode)
					|| "13505000".equals(loancode)
					|| "14502500".equals(loancode)
					|| "13502000".equals(loancode)
					|| "12800000".equals(loancode)
					|| "12600100".equals(loancode)
					|| "12600200".equals(loancode)) {
				// fnDB2GetColl
				if (findColl(Util.trim(map.get("QUOTANO")))) {
					return true;
				} else if ("13501000".equals(loancode)
						|| "12600300".equals(loancode)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * @param custId
	 * @param dupNo
	 * @param branchId
	 * @return [0] 是否為不動產 [1] 是否大於500萬 [2] 3碼幣別
	 */
	private boolean[] checkRetrailYN(String custId, String dupNo,
			String branchId) {

		// List list =
		// dwAslndavgovsService.findLnquotovJoinAslndavgovs(branchId,
		// custId, dupNo);
		// Iterator it = list.iterator();
		// BigDecimal a = new BigDecimal(0);
		// BranchRate branchRate = lmsService.getBranchRate(branchId);
		// String curr =
		// this.branch.getBranch(Util.trim(branchId)).getUseSWFT();
		// while (it.hasNext()) {
		// Map<String, Object> map = (Map<String, Object>) it.next();
		// if (!Util.isEmpty(Util.trim(map.get("REVOVLE")))) {
		// String ti = Util.trim(map.get("REVOVLE"));
		// if ("N".equals(ti)) {
		// if (!Util.isEmpty(Util.trim(map.get("LOAN_BAL")))) {
		// a = a.add(branchRate.toOtherAmt(curr, "TWD",(BigDecimal)
		// map.get("LOAN_BAL")));
		// }
		// } else if ("Y".equals(ti)) {
		// if (!Util.isEmpty(Util.trim(map.get("FACT_AMT")))) {
		// a = a.add(branchRate.toOtherAmt(curr, "TWD",(BigDecimal)
		// map.get("LOAN_BAL")));
		// }
		// }
		// }
		// }
		// if (a.compareTo(new BigDecimal(5000000)) == 1) {
		// over500 = true;
		// }

		boolean over500 = false;
		boolean estateYN = false;
		// 判斷500萬
		if ("1".equals(Util.trim(selSumFactAmt(branchId, custId, dupNo).get(
				"sucess")))) {
			over500 = true;
		}
		;

		// if (!Util.isEmpty(lnf150LoanNo)) {
		// // 排除逾催呆戶 fnDB2GetLNF030
		// if (findStatus(status)) {
		// estateYN = true;
		// }
		// }
		// if ("13506200".equals(maxGlackey) || "13506300".equals(maxGlackey)
		// || "14501500".equals(maxGlackey)
		// || "14502000".equals(maxGlackey)
		// || "13500100".equals(maxGlackey)
		// || "14501000".equals(maxGlackey)
		// || "13505000".equals(maxGlackey)
		// || "14502500".equals(maxGlackey)
		// || "13502000".equals(maxGlackey)
		// || "12800000".equals(maxGlackey)
		// || "12600100".equals(maxGlackey)
		// || "12600200".equals(maxGlackey)) {
		// // fnDB2GetColl
		// if (findColl(quotano)) {
		// estateYN = true;
		// } else if ("13501000".equals(maxGlackey)
		// || "12600300".equals(maxGlackey)) {
		// }
		// }
		// 不判斷是否為不動產
		estateYN = true;

		return new boolean[] { estateYN, over500 };
	}

	public boolean findColl(String cntrno) {
		// 用額度序號找擔保品，如果為不動產則回覆Y
		// TODO 現無擔保品 傳false
		// List cntrs = misCollcntrService.findCollcntrJoinCollstr(cntrno);
		// Iterator lnf = cntrs.iterator();
		// while (lnf.hasNext()) {
		// Map map = (Map) lnf.next();
		// String collno = (String)map.get("COLLNO");
		// if(!collno.isEmpty() && "01".equals(collno.substring(0, 2))){
		// return true;
		// }
		// }
		return false;
	}

	/**
	 * 抓匯率檔
	 * 
	 * @param branch
	 * @return
	 */
	public Map<String, Map<String, Object>> selRate(String branch) {
		Map<String, Map<String, Object>> rateMap = new HashMap<String, Map<String, Object>>();
		List<?> locrts = dwFxrthovsService.findDW_DWFXRTHOVSSelAll(branch);
		if (locrts.isEmpty()) {
			locrts = dwFxrthovsService.findDW_DWFXRTHOVSSelAllMaxDate(branch);
		}
		Iterator<?> locrtsit = locrts.iterator();
		boolean i = true;
		while (locrtsit.hasNext()) {
			Map<?, ?> dataMap = (Map<?, ?>) locrtsit.next();
			String curr = Util.trim(dataMap.get("CUR_CD"));
			String curr3c = Util.trim(dataMap.get("CUR_CD_3C"));
			BigDecimal rate = new BigDecimal(Util.trim(dataMap
					.get("AGNT_LOC_RT")));
			String mulDiv = Util.trim(dataMap.get("MUL_DIV_L"));
			if ("D".equals(mulDiv)) {
				rate = BigDecimal.ONE.divide(rate, 8, BigDecimal.ROUND_HALF_UP);
			}

			Map<String, Object> branchMap = new HashMap<String, Object>();
			if (i) {
				Date dataSrcDt = (Date) dataMap.get("DW_DATA_SRC_DT");
				Map<String, Object> date = new HashMap<String, Object>();
				date.put("date", dataSrcDt);
				rateMap.put("date", date);
				i = false;
			}
			branchMap.put("rate", rate);
			branchMap.put("curr3c", curr3c);
			rateMap.put(curr, branchMap);

			String homeFg = Util.trim(dataMap.get("HOME_FG"));
			if ("Y".equals(homeFg)) {
				Map<String, Object> homeMap = new HashMap<String, Object>();
				BigDecimal rateHome = new BigDecimal(Util.trim(String
						.valueOf(dataMap.get("AGNT_TWD_RT"))));
				String mulDivT = Util.trim(dataMap.get("MUL_DIV_T"));
				if ("D".equals(mulDivT)) {
					rateHome = (new BigDecimal(1)).divide(rateHome);
				}
				homeMap.put("rate", rateHome);
				homeMap.put("curr3c", "TWD");
				rateMap.put("TWD", homeMap);
			}
		}
		return rateMap;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Map selSumFactAmt(String branch, String custId, String dupNo) {
		//
		// Map result = new HashMap();
		// String mainId = IDGenerator.getUUID();
		// Map<String, Object> c241m01bsMap =
		// lms2415service.saveC241m01bByCustIdData(branch, custId, mainId,
		// dupNo);
		// if((Boolean)c241m01bsMap.get("success")){
		// List<C241M01B> c241m01bs =
		// (List<C241M01B>)c241m01bsMap.get("c241m01bList");
		// try {
		// Map<String, String> sum =
		// lms2415service.sumC241M01BAllAndSaveC241M01A(branch, mainId, null,
		// c241m01bs, null, false);
		// BigDecimal totBal =
		// BigDecimal.valueOf(Double.valueOf(sum.get("totBal")));
		// BigDecimal totQuota =
		// BigDecimal.valueOf(Double.valueOf(sum.get("totQuota")));
		//
		// } catch (CapException e) {
		// logger.error(e.getMessage());
		// result.put("sucess", 0);
		// }
		// }else{
		// result.put("sucess", 0);
		// }

		Map result = new HashMap();
		Map<String, Map<String, Object>> rateMap = selRate(branch);
		result.put("rate", rateMap);
		List list = dwAslndavgovsService.findLnquotovJoinAslndavgovs(branch,
				custId, dupNo);
		BranchRate branchRate = lmsService.getBranchRate(branch);
		String curr = this.branch.getBranch(Util.trim(branch)).getUseSWFT();
		Iterator lnf = list.iterator();
		BigDecimal a = new BigDecimal(0);
		while (lnf.hasNext()) {
			Map map = (Map) lnf.next();
			if (!Util.isEmpty(Util.trim(map.get("REVOVLE")))) {
				String ti = Util.trim(map.get("REVOVLE"));
				if ("N".equals(ti)) {
					if (!Util.isEmpty(Util.trim(map.get("LOAN_BAL")))) {
						// BigDecimal rate = new BigDecimal(0);
						// Map<String, Object> rateList = rateMap.get(Util
						// .trim(map.get("FACT_SWFT")));
						// if (rateList == null || rateList.get("rate") == null)
						// {
						// rate = BigDecimal.valueOf(1);
						// }else{
						// rate = (BigDecimal)rateList.get("rate");
						// }
						// BigDecimal rateTW = new BigDecimal(0);
						// Map<String, Object> rateTWList = rateMap.get("TWD");
						// if (rateTWList == null
						// || rateTWList.get("rate") == null) {
						// rateTW = BigDecimal.valueOf(1);
						// }else{
						// rateTW = (BigDecimal)rateTWList.get("rate");
						// }

						a = a.add(branchRate.toOtherAmt(curr, "TWD",
								(BigDecimal) map.get("LOAN_BAL")));

					}
				} else if ("Y".equals(ti)) {
					if (!Util.isEmpty(Util.trim(map.get("FACT_AMT")))) {
						// BigDecimal rate = new BigDecimal(0);
						// Map<String, Object> rateList = rateMap.get(Util
						// .trim(map.get("FACT_SWFT")));
						// if (rateList == null || rateList.get("rate") == null)
						// {
						// rate = BigDecimal.valueOf(1);
						// }
						// BigDecimal rateTW = new BigDecimal(0);
						// Map<String, Object> rateTWList = rateMap.get("TWD");
						// if (rateTWList == null
						// || rateTWList.get("rate") == null) {
						// rateTW = BigDecimal.valueOf(1);
						// }

						a = a.add(branchRate.toOtherAmt(curr, "TWD",
								(BigDecimal) map.get("LOAN_BAL")));
					}
				}
			}
		}
		if (a.compareTo(new BigDecimal(5000000)) == 1) {
			result.put("sucess", 1);
			return result;
		}
		result.put("sucess", 0);
		return result;
	}

	/**
	 * 搜尋主從債務人
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param contract
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Map<String, Object> findGuarantor(String branch, String custId,
			String dupNo, String contract) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<?> list = misdbBaseService.findEllngteeSelLngenm(branch, custId,
				dupNo, contract);
		Iterator it = list.iterator();
		String longStr = "";
		String cogeStr = "";
		String lngeflag = "";
		String lngenm = "";
		while (it.hasNext()) {
			Map map = (Map) it.next();

			lngeflag = Util.trim(map.get("LNGEFLAG"));
			lngenm = Util.trim(map.get("LNGENM"));
			if (lngeflag != null && lngeflag.isEmpty()) {
				if ("G".equals(lngeflag)) {
					if (longStr.isEmpty()) {
						longStr = lngenm + "(連保)";
					} else {
						longStr = longStr + "、" + lngenm + "(連保)";
					}
				} else if ("I".equals(lngeflag)) {
					if (longStr.isEmpty()) {
						longStr = lngenm + "(一般)";
					} else {
						longStr = longStr + "、" + lngenm + "(一般)";
					}
				} else if ("C".equals(lngeflag)) {
					if (cogeStr.isEmpty()) {
						cogeStr = lngenm;
					} else {
						cogeStr = cogeStr + "、" + lngenm;
					}
				}
			}
		}
		result.put("coborrower", cogeStr);
		result.put("guarantor", longStr);
		return result;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Map<String, Object> cauFinalDateNew(Map map490) {
		Map<String, Object> map = new HashMap<String, Object>();
		String newRule = Util.trim(map490.get("RULE_NO_NW"));
		if (Util.isEmpty(newRule)) {
			newRule = "99";
		}
		String[] kind = newRule.split(";");
		String kind1 = "";
		String kind2 = "";
		String kind3 = "";
		String kind4 = "";
		String kind5 = "";
		String kind10 = "";
		String kind11 = "";
		for (String m : kind) {
			if ("1".equals(Util.trim(m))) {
				kind1 = "Y";
			} else if ("2".equals(Util.trim(m))) {
				kind2 = "Y";
			} else if ("3".equals(Util.trim(m))) {
				kind3 = "Y";
			} else if ("4".equals(Util.trim(m))) {
				kind4 = "Y";
			} else if ("5".equals(Util.trim(m))) {
				kind5 = "Y";
			} else if ("8-2".equals(Util.trim(m))) {
				kind10 = "Y";
			} else if ("99".equals(Util.trim(m))) {
				kind11 = "Y";
			}
		}

		Date date490 = (Date) map490.get("CYC_MN");
		Date dataYM = CapDate.parseDate(TWNDate.toAD(date490).substring(0, 4)
				+ "/" + TWNDate.toAD(date490).substring(5, 7) + "/01");
		Date finalDate = CapDate.parseDate("9999-12-31");

		// 99 加一個月
		if ("Y".equals(kind11)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, 1);
			Date nextCTL = calendar.getTime();
			// Date nextCTL = CapDate.parseDate(
			// CapDate.addMonth(TWNDate.toAD(dataYM).replace("-", ""), 1));

			finalDate = changeDate(nextCTL, finalDate);
		}
		// 3;4;8-2 加六個月
		if ("Y".equals(kind3) || "Y".equals(kind4) || "Y".equals(kind10)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, 6);
			Date nextCTL = calendar.getTime();

			finalDate = changeDate(nextCTL, finalDate);
		}
		String custId = Util.trim(map490.get("CUST_ID"));
		String dupNo = Util.trim(map490.get("DUPNO"));
		if (dupNo.isEmpty()) {
			dupNo = "0";
		}
		String branchId = Util.trim(map490.get("BR_NO"));
		// 5 加六個月
		if ("Y".equals(kind5)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			Date nextCTL = CapDate.parseDate(CapDate.addMonth(
					TWNDate.toAD(dataYM).replace("-", ""), 6));

			finalDate = changeDate(nextCTL, finalDate);
		}
		Date rateDate = null;
		StringBuilder locRt = new StringBuilder();
		BigDecimal twdRt = new BigDecimal(0);
		// 1;2
		if ("Y".equals(kind1) || "Y".equals(kind2)) {
			// 判斷是否為不動產(fnDB2GetMISELF150)
			// if (estateOrNot(custId, dupNo)) {
			// 判斷是否大於500萬 fnDB2GetMISData
			Map sumFact = selSumFactAmt(branchId, custId, dupNo);
			if ("1".equals(Util.trim(sumFact.get("sucess")))) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(dataYM);
				calendar.add(Calendar.MONTH, 6);
				Date nextCTL = calendar.getTime();

				finalDate = changeDate(nextCTL, finalDate);
			}
			Map rate = (Map) sumFact.get("rate");
			rateDate = (Date) ((Map) rate.get("date")).get("date");
			Object[] rates = rate.values().toArray();
			for (int i = 0, size = rates.length; i < (size - 1); i++) {
				Object c = rates[i];
				String curr = (String) ((Map) c).get("curr3c");
				if ("TWD".equals(curr)) {
					twdRt = (BigDecimal) ((Map) c).get("rate");
				} else {
					locRt.append(curr + ":"
							+ ((BigDecimal) ((Map) c).get("rate")).toString()
							+ ";");
				}
			}
			// } else {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
		}

		if (finalDate.equals(CapDate.parseDate("9999-12-31"))) {
			finalDate = CapDate.parseDate("0001-01-01");
		}
		map.put("finalDate", finalDate);
		map.put("rateDate", rateDate);
		map.put("twdRt", twdRt);
		map.put("locRt", locRt.toString());
		return map;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Map<String, Object> cauFinalDate(Map map490) {
		String kind2 = "";
		String kind3 = "";
		String kind4 = "";
		String kind5 = "";
		String kind7 = "";
		String kind8 = "";
		String kind10 = "";
		String kind12 = "";
		// kind1="Y" 房貸案件審過一次後免再辦理覆審
		String ruleNos = Util.trim(map490.get("RULE_NO"));
		if (Util.isEmpty(ruleNos)) {
			ruleNos = "99";
		}
		String[] kind = ruleNos.split(";");
		for (String rule : kind) {
			if ("2".equals(Util.trim(rule))) {
				kind2 = "Y";
			} else if ("3".equals(Util.trim(rule))) {
				kind3 = "Y";
			} else if ("4".equals(Util.trim(rule))) {
				kind4 = "Y";
			} else if ("5".equals(Util.trim(rule))) {
				kind5 = "Y";
			} else if ("6-2".equals(Util.trim(rule))) {
				kind7 = "Y";
			} else if ("7".equals(Util.trim(rule))) {
				kind8 = "Y";
			} else if ("8-2".equals(Util.trim(rule))) {
				kind10 = "Y";
			} else if ("99".equals(Util.trim(rule))) {
				kind12 = "Y";
			}
		}

		String custId = Util.trim(map490.get("CUST_ID"));
		String dupNo = Util.trim(map490.get("DUPNO"));
		if (Util.isEmpty(dupNo)) {
			dupNo = "0";
		}
		String branchId = Util.trim(map490.get("BR_NO"));

		String date490 = TWNDate.toAD((Date) map490.get("CYC_MN"));
		Date dataYM = TWNDate.valueOf(date490.substring(0, 7) + "-01");

		Date finalDate = CapDate.parseDate("9999-12-31");

		Date rateDate = null;
		StringBuilder locRt = new StringBuilder();
		BigDecimal twdRt = new BigDecimal(0);

		if ("Y".equals(kind3) || "Y".equals(kind4) || "Y".equals(kind10)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.YEAR, 1);
			Date nextCTL = calendar.getTime();

			finalDate = changeDate(nextCTL, finalDate);
		} else if ("Y".equals(kind2)) {
			// if (!estateOrNot(custId, dupNo)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.YEAR, 1);
			// Date nextCTL = calendar.getTime();
			// finalDate = changeDate(nextCTL, finalDate);
			// } else {
			Map sumFact = selSumFactAmt(branchId, custId, dupNo);
			if ("1".equals(Util.trim(sumFact.get("sucess")))) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(dataYM);
				calendar.add(Calendar.YEAR, 1);
				Date nextCTL = calendar.getTime();

				finalDate = changeDate(nextCTL, finalDate);
			}
			Map rate = (Map) sumFact.get("rate");
			rateDate = (Date) ((Map) rate.get("date")).get("date");
			Object[] rates = rate.values().toArray();
			for (int i = 0, size = rates.length; i < (size - 1); i++) {
				Object c = rates[i];
				String curr = Util.trim(((Map) c).get("curr3c"));
				if ("TWD".equals(curr)) {
					twdRt = ((BigDecimal) ((Map) c).get("rate"));
				} else {
					locRt.append(curr + ":"
							+ ((BigDecimal) ((Map) c).get("rate")) + ";");
				}
			}
			// }
		} else if ("Y".equals(kind5)) {
			// 判斷循環
			int mouth = 0;
			if (revovleOrNot(custId, dupNo)) {
				mouth = 6;
			} else {
				mouth = 12;
			}
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, mouth);
			Date nextCTL = calendar.getTime();

			finalDate = changeDate(nextCTL, finalDate);
		} else if ("Y".equals(kind7) || "Y".equals(kind8)) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, 1);
			Date nextCTL = calendar.getTime();

			finalDate = changeDate(nextCTL, finalDate);
		} else if ("Y".equals(kind12)) {
			// 99舊案覆審看ELF491_MAINCUST
			int mouth = 0;
			if (Util.isEmpty(Util.trim(map490.get("MAINCUST")))) {
				List list491 = lms491Service.findSelMaincust(branchId, custId,
						dupNo);
				Iterator it491 = list491.iterator();
				while (it491.hasNext()) {
					Map map491 = (Map) it491.next();

					String mainCust = Util.trim(map491.get("MAINCUST"));
					if (!Util.isEmpty(map490.get("MAINCUST"))) {
						mainCust = Util.trim(map490.get("MAINCUST"));
					}
					if ("A".equals(mainCust)) {
						mouth = 12;
					} else if (!mainCust.isEmpty() && Util.isInteger(mainCust)) {
						mouth = Integer.parseInt(mainCust);
					} else {
						mouth = 1;
					}
				}
			} else if (Util.isInteger(Util.trim(map490.get("MAINCUST")))) {
				mouth = Integer.valueOf(Util.trim(map490.get("MAINCUST")));
			} else if ("A".equals(Util.trim(map490.get("MAINCUST")))) {
				mouth = 12;
			}

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dataYM);
			calendar.add(Calendar.MONTH, mouth);
			Date nextCTL = calendar.getTime();

			finalDate = changeDate(nextCTL, finalDate);
		}

		if (finalDate.equals(CapDate.parseDate("9999-12-31"))) {
			finalDate = CapDate.parseDate("0001-01-01");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("finalDate", finalDate);
		map.put("rateDate", rateDate);
		map.put("twdRt", twdRt);
		map.put("locRt", locRt.toString());
		return map;
	}

	@Override
	public void update490(String branch, Date date) {
		date = CapDate.parseDate(CapDate.addMonth(
				TWNDate.toAD(date).replace("-", ""), -1));
		List<String> list = new ArrayList<String>();
		update490New(date, branch, list);
		update490Old(date, branch, list);
		//更新擋finishTime
		C240M01Z c240m01z = c240m01zDao.findByUniqueKey(date, branch);
		if(c240m01z == null){
			c240m01z = new C240M01Z();
			c240m01z.setBranchId(branch);
			c240m01z.setDataDate(date);
		}
		c240m01z.setFinishTime(CapDate.getCurrentTimestamp());
		c240m01zDao.save(c240m01z);
	}

	@SuppressWarnings("rawtypes")
	public void update490New(Date date, String branch, List<String> custidList) {
		String dateTW = Util.addZeroWithValue(TWNDate.toTW(date)
				.substring(0, 3), 4)
				+ TWNDate.toTW(date).substring(4, 6);

		dateTW = TWNDate.toAD(date).substring(0, 7);

		List list = new ArrayList();
		try {
			list = dwElf490ovsService.findELF490ovsSelNewRule(dateTW, branch);
		} catch (Exception e) {
			logger.error("LMS2405ServiceImpl update490New  EXCEPTION!!", e);
			return;
		}
		Iterator it = list.iterator();
		while (it.hasNext()) {
			Map map490 = (Map) it.next();

			String newRule = Util.trim(map490.get("RULE_NO_NW"));
			if (Util.isEmpty(newRule)) {
				newRule = "99";
			}
			// String[] kind = newRule.split(";");
			// String kind1 = "";
			// String kind2 = "";
			// String kind3 = "";
			// String kind4 = "";
			// String kind5 = "";
			// String kind10 = "";
			// String kind11 = "";
			// for (String m : kind) {
			// if ("1".equals(Util.trim(m))) {
			// kind1 = "Y";
			// } else if ("2".equals(Util.trim(m))) {
			// kind2 = "Y";
			// } else if ("3".equals(Util.trim(m))) {
			// kind3 = "Y";
			// } else if ("4".equals(Util.trim(m))) {
			// kind4 = "Y";
			// } else if ("5".equals(Util.trim(m))) {
			// kind5 = "Y";
			// } else if ("8-2".equals(Util.trim(m))) {
			// kind10 = "Y";
			// } else if ("9".equals(Util.trim(m))) {
			// kind11 = "Y";
			// }
			// }
			//
			// Date date490 = (Date) map490.get("CYC_MN");
			// Date dataYM =
			// CapDate.parseDate(TWNDate.toAD(date490).substring(0,
			// 4)
			// + "/" + TWNDate.toAD(date490).substring(5, 7) + "/01");
			//
			// Date finalDate = CapDate.parseDate("9999-12-31");
			//
			// if ("Y".equals(kind3) || "Y".equals(kind10) ||
			// "Y".equals(kind11)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			String custId = Util.trim(map490.get("CUST_ID"));
			String dupNo = Util.trim(map490.get("DUPNO"));
			if (dupNo.isEmpty()) {
				dupNo = "0";
			}
			String branchId = Util.trim(map490.get("BR_NO"));
			// if ("Y".equals(kind5)) {
			// // 判斷循環
			// if (revovleOrNot(custId, dupNo)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			// }
			// Date rateDate = null;
			// StringBuilder locRt = new StringBuilder();
			// BigDecimal twdRt = new BigDecimal(0);
			// if ("Y".equals(kind1) || "Y".equals(kind2) || "Y".equals(kind4))
			// {
			// // 判斷是否為不動產(fnDB2GetMISELF150)
			// if (estateOrNot(custId, dupNo)) {
			// // 判斷是否大於500萬 fnDB2GetMISData
			// Map sumFact = selSumFactAmt(branchId, custId, dupNo);
			// if ("1".equals(sumFact.get("sucess"))) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			// Map rate = (Map) sumFact.get("rate");
			// rateDate = (Date) ((Map) rate.get("date")).get("date");
			// Object[] rates = rate.values().toArray();
			// for (int i = 0, size = rates.length; i < (size - 1); i++) {
			// Object c = rates[i];
			// String curr = Util.trim(((Map) c).get("curr3c"));
			// if ("TWD".equals(curr)) {
			// twdRt = (BigDecimal) ((Map) c).get("rate");
			// } else {
			// locRt.append(curr
			// + ":"
			// + ((BigDecimal) ((Map) c).get("rate"))
			// .toString() + ";");
			// }
			// }
			// } else {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			// }
			//
			// if (finalDate.equals(CapDate.parseDate("9999-12-31"))) {
			// finalDate = CapDate.parseDate("0001-01-01");
			// }
			// 重複擋掉
			if (custidList.contains(branchId + "-" + custId + "-" + dupNo)) {
				continue;
			}
			// 計算覆審日
			Map<String, Object> map = cauFinalDateNew(map490);
			Date finalDate = (Date) map.get("finalDate");
			Date rateDate = (Date) map.get("rateDate");
			BigDecimal twdRt = (BigDecimal) map.get("twdRt");
			String locRt = Util.trim(map.get("locRt"));

			if (Util.isEmpty(map490.get("DUPNO491"))
					&& Util.isEmpty(Util.trim(map490.get("DUPNO491")))) {
				try {
					if (finalDate.equals(CapDate.parseDate("0001-01-01"))) {
						finalDate = null;
					}
					lms491Service.saveInsertNew(branchId, custId, dupNo,
							finalDate, "sys", "N", newRule, "Y", rateDate,
							twdRt, locRt.toString());
				} catch (Exception e) {
					logger.error("LMS2405ServiceImpl update490New EXCEPTION!!", e);
				}
			} else {
				Date crdate = null;
				if (map490.get("CRDATE") != null
						&& !Util.trim(map490.get("CRDATE")).isEmpty()) {
					crdate = (Date) map490.get("CRDATE");
				}
				if (crdate == null
						|| crdate.equals(CapDate.parseDate("0001-01-01"))
						|| finalDate.before(crdate)) {
					try {
						if (finalDate.equals(CapDate.parseDate("0001-01-01"))) {
							finalDate = null;
						}
						lms491Service.saveUpdateNew(newRule, finalDate, "sys",
								rateDate, twdRt, locRt.toString(), branchId,
								custId, dupNo);
					} catch (Exception e) {
						logger.error("LMS2405ServiceImpl update490New EXCEPTION!!", e);
					}
				}
			}
			custidList.add(branchId + "-" + custId + "-" + dupNo);
		}
	}

	@SuppressWarnings("rawtypes")
	public void update490Old(Date date, String branch, List<String> custidList) {
		String dateTW = Util.addZeroWithValue(TWNDate.toTW(date)
				.substring(0, 3), 4)
				+ TWNDate.toTW(date).substring(4, 6);

		dateTW = TWNDate.toAD(date).substring(0, 7);
		List<?> list490 = dwElf490ovsService.findELF490ovsSelOldRule(dateTW,
				branch);
		Iterator it = list490.iterator();
		while (it.hasNext()) {
			Map map490 = (Map) it.next();

			// String kind2 = "";
			// String kind3 = "";
			// String kind4 = "";
			// String kind5 = "";
			// String kind7 = "";
			// String kind8 = "";
			// String kind10 = "";
			// String kind11 = "";
			// String kind12 = "";
			// // kind1="Y" 房貸案件審過一次後免再辦理覆審
			String ruleNos = Util.trim(map490.get("RULE_NO"));
			if (Util.isEmpty(ruleNos)) {
				ruleNos = "99";
			}
			// String[] kind = ruleNos.split(";");
			// for (String rule : kind) {
			// if ("2".equals(Util.trim(rule))) {
			// kind2 = "Y";
			// } else if ("3".equals(Util.trim(rule))) {
			// kind3 = "Y";
			// } else if ("4".equals(Util.trim(rule))) {
			// kind4 = "Y";
			// } else if ("5".equals(Util.trim(rule))) {
			// kind5 = "Y";
			// } else if ("6-2".equals(Util.trim(rule))) {
			// kind7 = "Y";
			// } else if ("7".equals(Util.trim(rule))) {
			// kind8 = "Y";
			// } else if ("8-2".equals(Util.trim(rule))) {
			// kind10 = "Y";
			// } else if ("9".equals(Util.trim(rule))) {
			// kind11 = "Y";
			// } else if ("99".equals(Util.trim(rule))) {
			// kind12 = "Y";
			// }
			// }
			//
			String custId = Util.trim(map490.get("CUST_ID"));
			String dupNo = Util.trim(map490.get("DUPNO"));
			if (Util.isEmpty(dupNo)) {
				dupNo = "0";
			}
			String branchId = Util.trim(map490.get("BR_NO"));
			//
			// String date490 = TWNDate.toAD((Date) map490.get("CYC_MN"));
			// Date dataYM = TWNDate.valueOf(date490.substring(0, 7) + "-01");
			//
			// Date finalDate = CapDate.parseDate("9999-12-31");
			//
			// Date rateDate = null;
			// StringBuilder locRt = new StringBuilder();
			// BigDecimal twdRt = new BigDecimal(0);
			//
			// if ("Y".equals(kind3) || "Y".equals(kind10) ||
			// "Y".equals(kind11)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.YEAR, 1);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// } else if ("Y".equals(kind2) || "Y".equals(kind4)) {
			// if (estateOrNot(custId, dupNo)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.YEAR, 1);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// } else {
			// Map sumFact = selSumFactAmt(branchId, custId, dupNo);
			// if ("1".equals(sumFact.get("sucess"))) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.YEAR, 1);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			// Map rate = (Map) sumFact.get("rate");
			// rateDate = (Date) ((Map) rate.get("date")).get("date");
			// Object[] rates = rate.values().toArray();
			// for (int i = 0, size = rates.length; i < (size - 1); i++) {
			// Object c = rates[i];
			// String curr = Util.trim(((Map) c).get("curr3c"));
			// if ("TWD".equals(curr)) {
			// twdRt = ((BigDecimal) ((Map) c).get("rate"));
			// } else {
			// locRt.append(curr + ":"
			// + ((BigDecimal) ((Map) c).get("rate"))
			// + ";");
			// }
			// }
			// }
			// } else if ("Y".equals(kind5)) {
			// if (revovleOrNot(custId, dupNo)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 6);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			// } else if ("Y".equals(kind7) || "Y".equals(kind8)) {
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, 1);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// } else if ("Y".equals(kind12)) {
			// // 99舊案覆審看ELF491_MAINCUST
			// int mouth = 0;
			// List list491 = lms491Service.findSelMaincust(branchId, custId,
			// dupNo);
			// Iterator it491 = list491.iterator();
			// while (it491.hasNext()) {
			// Map map491 = (Map) it491.next();
			// String mainCust = Util.trim((Util.trim(map491
			// .get("MAINCUST"))));
			// if ("A".equals(mainCust)) {
			// mouth = 12;
			// } else if (!mainCust.isEmpty()) {
			// mouth = Integer.parseInt(mainCust);
			// } else {
			// mouth = 1;
			// }
			// }
			//
			// Calendar calendar = Calendar.getInstance();
			// calendar.setTime(dataYM);
			// calendar.add(Calendar.MONTH, mouth);
			// Date nextCTL = calendar.getTime();
			//
			// finalDate = changeDate(nextCTL, finalDate);
			// }
			//
			// if (finalDate.equals(CapDate.parseDate("9999-12-31"))) {
			// finalDate = CapDate.parseDate("0001-01-01");
			// }
			// 重複擋掉
			if (custidList.contains(branchId + "-" + custId + "-" + dupNo)) {
				continue;
			}
			Map<String, Object> map = cauFinalDate(map490);
			Date finalDate = (Date) map.get("finalDate");
			Date rateDate = (Date) map.get("rateDate");
			BigDecimal twdRt = (BigDecimal) map.get("twdRt");
			String locRt = Util.trim(map.get("locRt"));

			if (map490.get("DUPNO491") == null
					&& Util.trim(map490.get("DUPNO491")).isEmpty()) {
				if (finalDate.equals(CapDate.parseDate("0001-01-01"))) {
					finalDate = null;
				}
				lms491Service.saveInsertNew(branchId, custId, dupNo, finalDate,
						"sys", "O", ruleNos, "", rateDate, twdRt,
						locRt.toString());
			} else {
				Date crdate = (Date) map490.get("CRDATE");
				if (crdate == null
						|| crdate.equals(CapDate.parseDate("0001-01-01"))
						|| finalDate.before(crdate)) {
					if (finalDate.equals(CapDate.parseDate("0001-01-01"))) {
						finalDate = null;
					}
					lms491Service.saveUpdateOld(ruleNos, finalDate, "sys",
							rateDate, twdRt, locRt.toString(), branchId,
							custId, dupNo);
				}
			}
			custidList.add(branchId + "-" + custId + "-" + dupNo);
		}
	}

	@Override
	public Map<String, Object> checkAlreadyHave(String branch, String dateYM) {
		Date date = CapDate.parseDate(dateYM + "-01");
		List<C240M01A> c240m01as = c240m01aDao.findByBranchAndDataDate(branch,
				date);
		Map<String, Object> map = new HashMap<String, Object>();
		// Properties lms2405v01 = MessageBundleScriptCreator
		// .getComponentResource(LMS2405V01Page.class);
		if (c240m01as != null && !c240m01as.isEmpty()) {
			for (C240M01A c240m01a : c240m01as) {
				if (RetrialDocStatusEnum.待覆核.getCode().equals(
						c240m01a.getDocStatus())
						|| RetrialDocStatusEnum.已核准.getCode().equals(
								c240m01a.getDocStatus())
						|| RetrialDocStatusEnum.已產生覆審名單報告檔.getCode().equals(
								c240m01a.getDocStatus())) {
					// map.put("success", false);
					// map.put("msg",
					// lms2405v01.getProperty("L180M01A.message2"));
				} else {
					map.put("success", false);
				}
			}
			if (Util.isEmpty(map.get("success"))) {
				map.put("success", true);
			}
		} else {
			map.put("success", true);
		}
		return map;
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public boolean produce(String branch, String dateYM) throws CapException {
		// 本次覆審共計 筆需覆審
		int reQuantityCount = 0;
		Date date = CapDate.parseDate(dateYM + "-01");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		List<C240M01A> c240m01as = c240m01aDao.findByBranchAndDataDate(branch,
				date);
		if (c240m01as != null && !c240m01as.isEmpty()) {
			for (C240M01A c240m01a : c240m01as) {
				if (RetrialDocStatusEnum.待覆核.getCode().equals(
						c240m01a.getDocStatus())
						|| RetrialDocStatusEnum.已核准.getCode().equals(
								c240m01a.getDocStatus())
						|| RetrialDocStatusEnum.已產生覆審名單報告檔.getCode().equals(
								c240m01a.getDocStatus())) {
					// return false;
				} else {
					deleteC240M01A(c240m01a.getOid());
				}
			}
		}

		// 名單
		C240M01A c240m01a = new C240M01A();
		c240m01a.setMainId(IDGenerator.getUUID());
		c240m01a.setTypCd(TypCdEnum.海外.getCode());
		c240m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
		c240m01a.setOwnBrId(user.getUnitNo());
		c240m01a.setCustId("");
		c240m01a.setCustName("");
		c240m01a.setBranchId(branch);
		c240m01a.setDataEndDate(date);
		String year = String.valueOf(Integer.valueOf(TWNDate.toAD(
				CapDate.getCurrentTimestamp()).substring(0, 4)) - 1);
		c240m01a.setYearOfReview(year);

		List<?> list = lms491Service.findSelAll(branch, date);
		Iterator it = list.iterator();
		List<C241M01A> c241m01as = new ArrayList<C241M01A>();
		List<C240M01B> c240m01bs = new ArrayList<C240M01B>();
		while (it.hasNext()) {
			Map map = (Map) it.next();
			String custId = Util.trim(map.get("CUSTID"));
			String custName = Util.trim(map.get("CNAME"));
			String dupNo = Util.trim(map.get("DUPNO"));
			String rePortKind = Util.trim(map.get("REPORTKIND"));
			// 只需產生「不覆審代碼(NCKDFLAG)」為 null 或空白或'A'(改期覆審)案件，其餘不出現在覆審工作底稿
			String nckdFlag = Util.trim(map.get("NCKDFLAG"));
			if (!Util.isEmpty(nckdFlag) && !"A".equals(nckdFlag)) {
				continue;
			} else if (Util.isEmpty(Util.trim(map.get("CRDATE")))
					|| CapDate.parseDate("0001-01-01").equals(
							CapDate.parseDate(Util.trim(map.get("CRDATE"))))) {
				continue;
			}
			String dwDupNo = "";
			if (!"0".equals(dupNo)) {
				dwDupNo = dupNo;
			}

			Map dwFact = dwAslndavgovsService.findLnquotovSelRevovle(custId,
					dwDupNo);
			if (dwFact == null || dwFact.isEmpty()) {
				continue;
			}

			// 如果該戶為聯貸戶母戶或不是該子戶不是本分行則換下一筆
			String lnf020FactType = Util.trim(dwFact.get("FACT_TYPE"));
			if (!lnf020FactType.isEmpty()
					&& ("30".equals(lnf020FactType) || ("31"
							.equals(lnf020FactType) && !branch.equals(Util
							.trim(dwFact.get("BR_NO")))))) {
				continue;
			}

			String mainId = IDGenerator.getUUID();
			// 明細
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(mainId);
			c241m01a.setTypCd(TypCdEnum.海外.getCode());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(custName);
			c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
			c241m01a.setOwnBrId(user.getUnitNo());
			c241m01a.setCreator(user.getUserId());
			c241m01a.setCreateTime(CapDate.getCurrentTimestamp());
			c241m01a.setUpdater(user.getUserId());
			c241m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			c241m01a.setRptId(lms2415service.getOVSLastC241M01ARptId());	//J-107-0128海外改格式

			C241A01A c241a01a = new C241A01A();
			c241a01a.setMainId(mainId);
			// 1.編製/移送
			c241a01a.setOwnUnit(user.getUnitNo());
			c241a01a.setAuthType("1");
			c241a01a.setAuthUnit(user.getUnitNo());

			c241a01adao.save(c241a01a);

			if (findstaff(custId)) {
				c241m01a.setStaff("Y");
			} else {
				c241m01a.setStaff("N");
			}
			Date lrDate = Util.isEmpty(map.get("LRDATE")) ? null : CapDate
					.parseDate(Util.nullToSpace(map.get("LRDATE")));
			c241m01a.setLastRetrialDate(lrDate);
			// 計算下次覆審日
			Date crDate = CapDate.parseDate(Util.trim(map.get("CRDATE")));
			c241m01a.setShouldReviewDate(crDate);
			String remomo = Util.trim(map.get("REMOMO"));
			// 規則為空塞99
			if (Util.isEmpty(remomo)) {
				remomo = "99";
			}
			// 判斷下次覆審日是否有大於產生資料日期
			Date nextMouth = CapDate.parseDate(CapDate.addMonth(
					TWNDate.toAD(date).replace("-", ""), 1));
			// 判斷下次覆審日是否有大於產生資料日期
			if (nextMouth.after(crDate)) {
				// String[] kind = remomo.split(";");
				boolean t = false;
				// for (String a : kind) {
				if ("1".equals(remomo) || "2".equals(remomo)
				// || "4".equals(remomo)
				) {
					t = true;
					// break;
				}
				// }
				if (t) {
					// 判斷是否為不動產(fnDB2GetMISELF150)
					boolean[] yNS = checkRetrailYN(custId, dupNo, branch);
					// if (estateOrNot(custId, dupNo)) {
					if (yNS[0]) {
						// 判斷是否大於500萬 fnDB2GetMISData
						// Map sumFact = selSumFactAmt(branch, custId,
						// dupNo);
						if (yNS[1]) {
							// if ("1".equals(sumFact.get("sucess"))) {
							c241m01a.setSysDel("Y");
							c241m01a.setRetrialYN("Y");
							reQuantityCount = reQuantityCount + 1;
						} else if (UtilConstants.DEFAULT.是.equals(Util.trim(map
								.get("NEWFLAG")))
								&& !crDate.equals(CapDate
										.parseDate("0001-01-01"))) {
							c241m01a.setSysDel("N");
							c241m01a.setRetrialYN("N");
						} else {
							continue;
							// c241m01a.setSysDel("N");
							// c241m01a.setRetrialYN("N");
						}
					} else if (UtilConstants.DEFAULT.是.equals(Util.trim(map
							.get("NEWFLAG")))
							&& !crDate.equals(CapDate.parseDate("0001-01-01"))) {
						c241m01a.setSysDel("N");
						c241m01a.setRetrialYN("N");
					} else {
						continue;
						// c241m01a.setSysDel("N");
						// c241m01a.setRetrialYN("N");
					}
				} else {
					c241m01a.setSysDel("Y");
					c241m01a.setRetrialYN("Y");
					reQuantityCount = reQuantityCount + 1;
				}
				// 新案沒到日期為不覆審
			} else if (UtilConstants.DEFAULT.是.equals(Util.trim(map
					.get("NEWFLAG")))
					&& !crDate.equals(CapDate.parseDate("0001-01-01"))) {
				c241m01a.setSysDel("N");
				c241m01a.setRetrialYN("N");
			} else {
				continue;
				// c241m01a.setSysDel("N");
				// c241m01a.setRetrialYN("N");
			}

			c241m01a.setRetrialKind(remomo);
			c241m01a.setNCreatData("SYS");
			if ("N".equals(rePortKind)) {
				c241m01a.setDocKind("N");
				c241m01a.setNewCase("Y");
			} else if ("O".equals(rePortKind)) {
				c241m01a.setDocKind("N");
				c241m01a.setNewCase("N");
			} else if ("G".equals(rePortKind)) {
				c241m01a.setDocKind("G");
				c241m01a.setNewCase("Y");
			} else if ("H".equals(rePortKind)) {
				c241m01a.setDocKind("G");
				c241m01a.setNewCase("N");
			}
			// 幣別
			c241m01a.setTotBalCurr(this.branch.getBranch(
					Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
			c241m01a.setTotQuotaCurr(this.branch.getBranch(
					Util.trim(c241m01a.getOwnBrId())).getUseSWFT());

			Map<String, String> currMap = this.infoAccount(c240m01a, c241m01a,
					false);
			if (!Util.isEmpty(currMap)) {
				c241m01a.setTotBal(LMSUtil.toBigDecimal(currMap.get("totBal")));
				c241m01a.setTotQuota(LMSUtil.toBigDecimal(currMap
						.get("totQuota")));
			}

			// 覆審關聯檔
			C240M01B c240m01b = new C240M01B();
			c240m01b.setMainId(c240m01a.getMainId());
			c240m01b.setRefMainId(c241m01a.getMainId());

			// save(c240m01b);
			c240m01bs.add(c240m01b);
			c241m01as.add(c241m01a);
		}

		// 授權檔
		C240A01A c240a01a = new C240A01A();
		c240a01a.setMainId(c240m01a.getMainId());
		c240a01a.setOwnUnit(user.getUnitNo());
		c240a01a.setAuthType("1");
		c240a01a.setAuthUnit(user.getUnitNo());

		if (!branch.equals(user.getUnitNo())) {
			C240A01A c240a01a2 = new C240A01A();
			c240a01a2.setMainId(c240m01a.getMainId());
			c240a01a2.setOwnUnit(user.getUnitNo());
			c240a01a2.setAuthType("4");
			c240a01a2.setAuthUnit(branch);
			save(c240a01a2);
		}

		// 搜尋控制檔
		Map count = lms491Service.findSelRemomo(branch);
		if (count != null && !Util.isEmpty(Util.trim(count.get("REMOMO")))
				&& Util.trim(count.get("REMOMO")).length() > 14) {
			String remomo = Util.trim(count.get("REMOMO"));
			String plotOfReview = remomo.substring(0, 13);
			String samplingCount = remomo.substring(14);
			c240m01a.setPlotsOfReview(Integer.getInteger(plotOfReview));
			c240m01a.setSamplingCount(Integer.getInteger(samplingCount));
			c240m01a.setSamplingRate(Integer.getInteger(plotOfReview)
					* Integer.getInteger(samplingCount));
		} else {
			c240m01a.setPlotsOfReview(0);
			c240m01a.setSamplingCount(0);
			c240m01a.setSamplingRate(0);
		}
		// 搜尋C241m01a 計算覆審類型有8-1有多少筆
		int i = 0;
		for (C241M01A c241m01a : c241m01as) {
			String kind = c241m01a.getRetrialKind();
			if (kind != null && !kind.isEmpty() && kind.indexOf("8-1") >= 0) {
				i++;
			}
		}
		c240m01a.setEffectiveCount(i);
		// 本次覆審共計筆需覆審
		c240m01a.setReQuantity(reQuantityCount);
		c241m01aDao.save(c241m01as);
		c240m01bDao.save(c240m01bs);
		save(c240m01a, c240a01a);
		startFlow(c240m01a.getOid(), "LMS2405Flow");

		return true;
	}

	@SuppressWarnings({ "rawtypes" })
	@Override
	public Map<String, String> produceList(C240M01A c240m01a)
			throws CapException {
		// C240M01A c240m01a = c240m01aDao.findByOid(oid);
		// 刪除原有的明細(8-1)
		List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
				.getMainId());
		Map<String, String> currMap = null;
		for (C240M01B c240m01b : c240m01bs) {
			C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
					.getRefMainId());
			if (c241m01a != null) {
				String remomo = c241m01a.getRetrialKind();
				if (remomo != null && !remomo.isEmpty()
						&& remomo.indexOf("8-1") > -1) {
					lms2415service.deleteC241M01A(c241m01a.getOid());
					delete(C240M01B.class, c240m01b.getOid());
				}
			}
		}

		if (c240m01a != null) {
			int count = c240m01a.getPlotsOfReview()
					* c240m01a.getThisSamplingRate() / 100;
			c240m01a.setThisSamplingCount(count);
			save(c240m01a);
			String branchId = c240m01a.getBranchId();
			List<Map<String, Object>> list = lms491Service
					.LMS491selByBranch(branchId);

			if (list.size() > 0) {
				for (int i = 0, size = list.size(); i < size; i++) {
					if (i == count) {
						break;
					}
					Map map = list.get(i);
					MegaSSOUserDetails user = MegaSSOSecurityContext
							.getUserDetails();
					C241M01A c241m01a = new C241M01A();
					c241m01a.setMainId(IDGenerator.getUUID());
					c241m01a.setTypCd(TypCdEnum.海外.getCode());
					String custId = Util.trim(map.get("CUSTID"));
					c241m01a.setCustId(custId);
					String dupNo = Util.trim(map.get("DUPNO"));
					c241m01a.setDupNo(dupNo);
					c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
					c241m01a.setOwnBrId(user.getUnitNo());
					c241m01a.setCreator(user.getUserId());
					c241m01a.setCreateTime(CapDate.getCurrentTimestamp());
					c241m01a.setUpdater(user.getUserId());
					c241m01a.setUpdateTime(CapDate.getCurrentTimestamp());
					c241m01a.setRptId(lms2415service.getOVSLastC241M01ARptId());	//J-107-0128海外改格式
					
					if (findstaff(custId)) {
						c241m01a.setStaff("Y");
					} else {
						c241m01a.setStaff("N");
					}

					c241m01a.setLastRetrialDate(Util.isEmpty(map.get("LRDATE")) ? null
							: CapDate.parseDate(Util.nullToSpace(map
									.get("LRDATE"))));
					c241m01a.setRetrialKind(Util.trim(map.get("REMOMO")));
					c241m01a.setNCreatData("SYS");
					String reportkind = Util.trim(map.get("REPORTKIND"));
					if ("N".equals(reportkind)) {
						c241m01a.setDocKind("N");
						c241m01a.setNewCase("Y");
					} else if ("O".equals(reportkind)) {
						c241m01a.setDocKind("N");
						c241m01a.setNewCase("N");
					} else if ("G".equals(reportkind)) {
						c241m01a.setDocKind("G");
						c241m01a.setNewCase("Y");
					} else if ("H".equals(reportkind)) {
						c241m01a.setDocKind("G");
						c241m01a.setNewCase("N");
					}
					c241m01a.setRetrialYN("Y");
					c240m01a.setCreator(c240m01a.getCreator());
					// 覆審關聯檔
					C240M01B c240m01b = new C240M01B();
					c240m01b.setMainId(c240m01a.getMainId());
					c240m01b.setRefMainId(c241m01a.getMainId());
					currMap = infoAccount(c240m01a, c241m01a, false);
					if (!Util.isEmpty(currMap)) {
						c241m01a.setTotBal(LMSUtil.toBigDecimal(currMap
								.get("totBal")));
						c241m01a.setTotQuota(LMSUtil.toBigDecimal(currMap
								.get("totQuota")));
					}
					save(c240m01b, c241m01a);
				}
			}
		}
		return currMap;
	}

	/**
	 * 引進帳務
	 * 
	 * @param c240m01a
	 * @param c241m01a
	 * @return
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	private Map<String, String> infoAccount(C240M01A c240m01a,
			C241M01A c241m01a, boolean saveResult) throws CapException {
		Map<String, String> map = null;
		Map<String, Object> reMap = lms2415service.saveC241m01bByCustIdData(
				c240m01a.getBranchId(), c241m01a.getCustId(),
				c241m01a.getMainId(), c241m01a.getDupNo());
		if ((Boolean) reMap.get("success")) {
			List<C241M01B> c241m01bList = (List<C241M01B>) reMap
					.get("c241m01bList");
			map = lms2415service.sumC241M01BAllAndSaveC241M01A(
					Util.nullToSpace(c240m01a.getBranchId()),
					c241m01a.getMainId(), c241m01a, c241m01bList, "1",
					saveResult);
		}
		return map;
	}

	@Override
	public Map<String, Object> produceNew(String oid, String custId,
			String dupNo) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		int count = 0;
		C240M01A c240m01a = findModelByOid(C240M01A.class, oid);
		Properties lms2405m01 = MessageBundleScriptCreator
				.getComponentResource(LMS2405M01Page.class);
		if (c240m01a != null) {
			if (Util.isEmpty(c240m01a.getExpectedRetrialDate())) {
				throw new CapMessageException(
						lms2405m01.getProperty("err.noDate"), getClass());
			}
			count = c240m01a.getReQuantity();
		}

		Map<String, Object> returnVal = new HashMap<String, Object>();
		// 檢查是否有相同的明細
		List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
				.getMainId());
		int seqNo = 0;
		for (C240M01B c240m01b : c240m01bs) {
			C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
					.getRefMainId());
			if (!Util.isEmpty(c241m01a.getProjectNo())) {
				int lengh = c241m01a.getProjectNo().length();
				if (Util.isNumeric(c241m01a.getProjectNo().substring(lengh - 4,
						lengh - 1))) {
					Integer proNo = Integer.valueOf(c241m01a.getProjectNo()
							.substring(lengh - 4, lengh - 1));
					if (proNo > seqNo) {
						seqNo = proNo;
					}
				}
			}
			if (c241m01a != null && (c241m01a.getCustId()).equals(custId)
					&& (c241m01a.getDupNo()).equals(dupNo)) {
				returnVal.put("success", false);
				returnVal.put("msg", lms2405m01.getProperty("alreadyHave"));
				return returnVal;
			}
		}

		Map<String, Object> map = lms491Service.findNewSelAll(
				c240m01a.getBranchId(), custId, dupNo);
		if (map != null) {
			C241M01A c241m01a = new C241M01A();
			c241m01a.setMainId(IDGenerator.getUUID());
			c241m01a.setTypCd(TypCdEnum.海外.getCode());
			c241m01a.setCustId(custId);
			c241m01a.setDupNo(dupNo);
			c241m01a.setCustName(Util.trim(map.get("CNAME")));
			c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
			c241m01a.setOwnBrId(user.getUnitNo());
			c241m01a.setCreator(user.getUserId());
			c241m01a.setCreateTime(CapDate.getCurrentTimestamp());
			c241m01a.setUpdater(user.getUserId());
			c241m01a.setUpdateTime(CapDate.getCurrentTimestamp());
			c241m01a.setRptId(lms2415service.getOVSLastC241M01ARptId());	//J-107-0128海外改格式
			
			if (!Util.isEmpty(c240m01a.getBatchNO())) {
				String casenumber = numberService
						.getCaseNumber(C240M01A.class,
								c240m01a.getBranchId(),
								TWNDate.toAD(c240m01a.getDataEndDate())
										.substring(0, 4),
								Util.addZeroWithValue(c240m01a.getBatchNO(), 3)
										+ "-"
										+ Util.addZeroWithValue(seqNo + 1, 3));
				c241m01a.setProjectNo(casenumber);
			}

			if (findstaff(custId)) {
				c241m01a.setStaff("Y");
			} else {
				c241m01a.setStaff("N");
			}
			c241m01a.setLastRetrialDate(Util.isEmpty(map.get("LRDATE")) ? null
					: CapDate.parseDate(Util.nullToSpace(map.get("LRDATE"))));
			String crDate = Util.nullToSpace(map.get("CRDATE"));
			if (crDate == null || crDate.isEmpty()
					|| "0001-01-01".equals(crDate)) {
				c241m01a.setShouldReviewDate(c240m01a.getExpectedRetrialDate());
			} else {
				c241m01a.setShouldReviewDate(CapDate.parseDate(crDate));
			}
			String remomo = Util.trim(map.get("REMOMO"));
			// 規則為空塞99
			if (Util.isEmpty(remomo)) {
				remomo = "99";
			}
			c241m01a.setRetrialKind(remomo);
			c241m01a.setNCreatData("PEO");
			String reportkind = Util.nullToSpace(map.get("REPORTKIND"));
			if ("N".equals(reportkind)) {
				c241m01a.setDocKind("N");
				c241m01a.setNewCase("Y");
			} else if ("O".equals(reportkind)) {
				c241m01a.setDocKind("N");
				c241m01a.setNewCase("N");
			} else if ("G".equals(reportkind)) {
				c241m01a.setDocKind("G");
				c241m01a.setNewCase("Y");
			} else if ("H".equals(reportkind)) {
				c241m01a.setDocKind("G");
				c241m01a.setNewCase("N");
			}
			c241m01a.setRetrialYN("Y");
			c241m01a.setTotBalCurr(this.branch.getBranch(
					Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
			c241m01a.setTotQuotaCurr(this.branch.getBranch(
					Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
			count = count + 1;
			c240m01a.setReQuantity(count);

			// 授權檔
			C241A01A c241a01a = new C241A01A();
			c241a01a.setMainId(c241m01a.getMainId());
			// 1.編製/移送
			c241a01a.setOwnUnit(user.getUnitNo());
			c241a01a.setAuthType("1");
			c241a01a.setAuthUnit(user.getUnitNo());

			// 關聯檔
			C240M01B c240m01b = new C240M01B();
			c240m01b.setMainId(c240m01a.getMainId());
			c240m01b.setRefMainId(c241m01a.getMainId());
			Map<String, String> currMap = infoAccount(c240m01a, c241m01a, false);
			if (!Util.isEmpty(currMap)) {
				c241m01a.setTotBal(LMSUtil.toBigDecimal(currMap.get("totBal")));
				c241m01a.setTotQuota(LMSUtil.toBigDecimal(currMap
						.get("totQuota")));
				for (String key : currMap.keySet()) {
					returnVal.put(key, currMap.get(key));
				}
			}
			save(c240m01a, c240m01b, c241m01a, c241a01a);
			returnVal.put("success", true);
			return returnVal;
		} else {
			List<Map<String, Object>> list = lmsCustdataService
									.findCustDataCname(custId, dupNo);
			Map<String, Object> custdata = null;
			if(!Util.isEmpty(list) && list.size() > 0){
				custdata = list.get(0);
			}
			if (custdata != null) {
				C241M01A c241m01a = new C241M01A();
				c241m01a.setMainId(IDGenerator.getUUID());
				c241m01a.setTypCd(TypCdEnum.海外.getCode());
				c241m01a.setCustId(custId);
				c241m01a.setDupNo(dupNo);
				c241m01a.setCustName(Util.trim(custdata.get("CNAME")));
				c241m01a.setUnitType(UnitTypeEnum.convertToUnitType(user.getUnitType()));
				c241m01a.setOwnBrId(user.getUnitNo());
				c241m01a.setCreator(user.getUserId());
				c241m01a.setCreateTime(CapDate.getCurrentTimestamp());
				c241m01a.setUpdater(user.getUserId());
				c241m01a.setUpdateTime(CapDate.getCurrentTimestamp());
				c241m01a.setRptId(lms2415service.getOVSLastC241M01ARptId());	//J-107-0128海外改格式
				
				if (!Util.isEmpty(c240m01a.getBatchNO())) {
					String casenumber = numberService
							.getCaseNumber(
									C240M01A.class,
									c240m01a.getBranchId(),
									TWNDate.toAD(c240m01a.getDataEndDate())
											.substring(0, 4),
									Util.addZeroWithValue(
											c240m01a.getBatchNO(), 3)
											+ "-"
											+ Util.addZeroWithValue(seqNo + 1,
													3));
					c241m01a.setProjectNo(casenumber);
				}
				if (findstaff(custId)) {
					c241m01a.setStaff("Y");
				} else {
					c241m01a.setStaff("N");
				}
				c241m01a.setShouldReviewDate(c240m01a.getExpectedRetrialDate());
				c241m01a.setNCreatData("PEO");
				c241m01a.setRetrialYN("Y");
				c241m01a.setRetrialKind("99");
				c241m01a.setTotBalCurr(this.branch.getBranch(
						Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
				c241m01a.setTotQuotaCurr(this.branch.getBranch(
						Util.trim(c241m01a.getOwnBrId())).getUseSWFT());
				count = count + 1;
				c240m01a.setReQuantity(count);
				// 授權檔
				C241A01A c241a01a = new C241A01A();
				c241a01a.setMainId(c241m01a.getMainId());
				// 1.編製/移送
				c241a01a.setOwnUnit(user.getUnitNo());
				c241a01a.setAuthType("1");
				c241a01a.setAuthUnit(user.getUnitNo());

				// 關聯檔
				C240M01B c240m01b = new C240M01B();
				c240m01b.setMainId(c240m01a.getMainId());
				c240m01b.setRefMainId(c241m01a.getMainId());

				save(c240m01a, c240m01b, c241m01a, c241a01a);

				// TODO 引進帳務
				returnVal.put("success", true);
				return returnVal;
			} else {
				returnVal.put("success", false);
				returnVal.put("msg", lms2405m01.getProperty("noCustdata"));
				return returnVal;
			}
		}
	}

	@SuppressWarnings("unused")
	@Override
	public boolean produceExcel(String oid) throws Exception {
		boolean st = false;
		String listName = "listExcel";

		C240M01A c240m01a = findModelByOid(C240M01A.class, oid);
		String ranMainId = c240m01a.getMainId();

		if (c240m01a == null) {
			return false;
		}

		try {
			Properties lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);
			DocFile docFile = new DocFile();
			docFile.setBranchId(c240m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(lms2405m01.getProperty(listName)
					+ "_"
					+ lms2405m01.getProperty("c240m01a.docStatus"
							+ c240m01a.getDocStatus()) + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId(fileService.getSysId());
			docFile.setData(new byte[] {});
			fileService.save(docFile, false);

			// Excel Success
			st = this
					.createProduceExcel(listName, c240m01a, ranMainId, docFile);

		} finally {

		}

		return st;
	}

	private boolean createProduceExcel(String listName, C240M01A c240m01a,
			String ranMainId, DocFile docFile) throws Exception {
		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), ranMainId,
				listName);
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();

		Properties lms2405m01 = MessageBundleScriptCreator
				.getComponentResource(LMS2405M01Page.class);
		Map<String, String> lnBusinessMap = null;
		Map<String, String> lnTypeMap = null;
		Map<String, String> yesNoMap = null;
		Map<String, String> subItmeMap = null;
		Label label = null;
		try {
			String path = PropUtil.getProperty("loadFile.dir")
					+ "excel/C240M01Excel.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			cellFormatL.setAlignment(Alignment.LEFT);
			cellFormatL.setWrap(true);
			cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont12);
			cellFormatC.setAlignment(Alignment.CENTRE);
			cellFormatC.setWrap(true);
			cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont12);
			NcellFormatR.setAlignment(Alignment.RIGHT);
			cellFormatL.setWrap(true);
			NcellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);
			WritableCellFormat cellNFormatL = new WritableCellFormat(headFont12);
			cellNFormatL.setWrap(true);
			cellNFormatL.setAlignment(Alignment.LEFT);
			WritableCellFormat cellNFormatR = new WritableCellFormat(headFont12);
			cellNFormatR.setWrap(true);
			cellNFormatR.setAlignment(Alignment.RIGHT);

			// 分行代碼
			label = new Label(0, 1, lms2405m01.getProperty("branchNO")
					+ Util.nullToSpace(c240m01a.getBranchId())
					+ " "
					+ branch.getBranchName(Util.nullToSpace(c240m01a
							.getBranchId())), cellNFormatL);
			sheet.addCell(label);

			// 覆審年月
			Date dataDate = c240m01a.getExpectedRetrialDate();
			String dataStr = "";
			String dateDateLessOne = "";
			if (!Util.isEmpty(dataDate)) {
				String cauDate = CapDate.addMonth(TWNDate.toAD(dataDate)
						.replace("-", ""), -1);
				dateDateLessOne = cauDate.substring(0, 4) + "-"
						+ cauDate.substring(4, 6);
				dataStr = TWNDate.toAD(dataDate).substring(0, 7);
			}
			label = new Label(0, 2, lms2405m01.getProperty("excelTitle1")
					.replace("dataDate1", dataStr)
					.replace("dataDate2", dateDateLessOne), cellNFormatL);
			sheet.addCell(label);

			// List<C240M01B> c240m01bs =
			// c240m01bDao.findByMainId(c240m01a.getMainId());
			List<Map<String, Object>> c240m01bList = eloandbBASEService
					.findC240M01BOrderByC241M01ASEQ(Util.trim(c240m01a
							.getMainId()));
			int total = 0;
			lnBusinessMap = codeType.findByCodeType("lms2415m01_lnBusiness");
			if (lnBusinessMap == null) {
				lnBusinessMap = new LinkedHashMap<String, String>();
			}
			lnTypeMap = codeType.findByCodeType("lms2415m01_lnType");
			if (lnTypeMap == null) {
				lnTypeMap = new LinkedHashMap<String, String>();
			}
			yesNoMap = codeType.findByCodeType("Common_YesNo");
			if (yesNoMap == null) {
				yesNoMap = new LinkedHashMap<String, String>();
			}
			subItmeMap = codeType.findByCodeType("lms1705m01_SubItme");
			if (subItmeMap == null) {
				subItmeMap = new LinkedHashMap<String, String>();
			}
			int i = 0;
			int sizeP = 0;
			int sizeC = 0;
			boolean retrialYNResult = false;
			BigDecimal thr = new BigDecimal(1000);
			for (Map<String, Object> dataMap : c240m01bList) {
				retrialYNResult = false;
				C241M01A c241m01a = c241m01aDao.findByMainId(Util.trim(dataMap
						.get("MAINID")));
				if (((RetrialDocStatusEnum.已核准.getCode().equals(
						Util.trim(c240m01a.getDocStatus())) || RetrialDocStatusEnum.已產生覆審名單報告檔
						.getCode().equals(Util.trim(c240m01a.getDocStatus()))) && "Y"
						.equals(Util.trim(dataMap.get("RETRIALYN"))))
						|| (RetrialDocStatusEnum.編製中.getCode().equals(
								Util.trim(c240m01a.getDocStatus())) || RetrialDocStatusEnum.待覆核
								.getCode().equals(
										Util.trim(c240m01a.getDocStatus())))) {
					if ("Y".equals(c241m01a.getRetrialYN())) {
						sizeP++;
						retrialYNResult = true;
					}
					// 序號
					label = new Label(
							0,
							total + i + 4,
							Util.trim(c241m01a.getProjectNo()).indexOf("-") != -1 ? Util
									.trim(c241m01a.getProjectNo()).split("-")[1]
									.replace("號", "")
									: "", cellFormatL);
					sheet.addCell(label);
					// 客戶id + dupno
					label = new Label(1, total + i + 4,
							Util.nullToSpace(c241m01a.getCustId())
									+ Util.nullToSpace(c241m01a.getDupNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶名稱
					label = new Label(2, total + i + 4,
							Util.nullToSpace(c241m01a.getCustName()),
							cellFormatL);
					sheet.addCell(label);
					// 是否為行員
					label = new Label(20, total + i + 4, Util.trim(yesNoMap
							.get(Util.nullToSpace(c241m01a.getStaff()))),
							cellFormatC);
					sheet.addCell(label);
					// 覆審類別
					label = new Label(21, total + i + 4,
							Util.nullToSpace(c241m01a.getRetrialKind()),
							cellFormatC);
					sheet.addCell(label);
					// 新舊案
					String newadd = "";
					if ("Y".equals(c241m01a.getNewCase())) {
						newadd = lms2405m01.getProperty("new");
					} else {
						newadd = lms2405m01.getProperty("old");
					}
					label = new Label(22, total + i + 4,
							Util.nullToSpace(newadd), cellFormatC);
					sheet.addCell(label);
					// 前次覆審日期
					label = new Label(24, total + i + 4,
							Util.nullToSpace(TWNDate.toAD(c241m01a
									.getLastRetrialDate())), cellFormatC);
					sheet.addCell(label);
					// 最遲應覆審期限
					label = new Label(25, total + i + 4,
							Util.nullToSpace(TWNDate.toAD(c241m01a
									.getShouldReviewDate())), cellFormatC);
					sheet.addCell(label);

					List<C241M01B> c241m01bs = c241m01bDao
							.findByMainId(c241m01a.getMainId());
					int j = 0;
					for (C241M01B c241m01b : c241m01bs) {
						if (retrialYNResult) {
							sizeC++;
						}
						if (j != 0) {
							label = new Label(0, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(1, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(2, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(20, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(21, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(22, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(24, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(25, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
						}
						// 共同借款人
						label = new Label(3, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						// 會計科目 細目代號
						label = new Label(4, total + i + 4,
								Util.nullToSpace(c241m01b.getActcd()),
								cellFormatL);
						sheet.addCell(label);
						// 會計科子細目名稱
						label = new Label(5, total + i + 4,
								Util.trim(subItmeMap.get(Util
										.nullToSpace(c241m01b.getActcd()))),
								cellFormatL);
						sheet.addCell(label);
						// 額度序號
						label = new Label(6, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaNo()),
								cellFormatL);
						sheet.addCell(label);
						// 幣別
						label = new Label(7, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaCurr()),
								cellFormatC);
						sheet.addCell(label);
						// 授信額度
						label = new Label(
								8,
								total + i + 4,
								Util.isEmpty(c241m01b.getQuotaAmt()) ? "0.00"
										: NumConverter
												.addComma(
														c241m01b.getQuotaAmt()
																.divide(thr)
																.setScale(
																		2,
																		BigDecimal.ROUND_HALF_UP),
														",##0.00"),
								NcellFormatR);
						sheet.addCell(label);
						// 幣別
						label = new Label(9, total + i + 4,
								Util.nullToSpace(c241m01b.getBalCurr()),
								cellFormatC);
						sheet.addCell(label);
						// 授信餘額
						label = new Label(
								10,
								total + i + 4,
								Util.isEmpty(c241m01b.getBalAmt()) ? "0.00"
										: NumConverter
												.addComma(
														c241m01b.getBalAmt()
																.divide(thr)
																.setScale(
																		2,
																		BigDecimal.ROUND_HALF_UP),
														",##0.00"),
								NcellFormatR);
						sheet.addCell(label);
						// 循環別
						label = new Label(11, total + i + 4,
								Util.nullToSpace(c241m01b.getReVolve()),
								cellFormatC);
						sheet.addCell(label);
						// 動用起迄日
						label = new Label(12, total + i + 4,
								Util.nullToSpace(TWNDate.toAD(c241m01b
										.getUseFDate()))
										+ "~"
										+ Util.nullToSpace(TWNDate
												.toAD(c241m01b.getUseEDate())),
								cellFormatC);
						sheet.addCell(label);
						// 帳務檔建立日期
						label = new Label(13, total + i + 4,
								Util.nullToSpace(TWNDate.toAD(c241m01b
										.getLNF030CrtDate())), cellFormatC);
						sheet.addCell(label);
						// 授信契約起迄日
						label = new Label(
								14,
								total + i + 4,
								Util.nullToSpace(TWNDate.toAD(c241m01b
										.getLoanFDate()))
										+ "~"
										+ Util.nullToSpace(TWNDate
												.toAD(c241m01b.getLoanEDate())),
								cellFormatC);
						sheet.addCell(label);
						// 融資業務分類代號
						label = new Label(15, total + i + 4,
								Util.nullToSpace(c241m01b.getLnBusiness()),
								cellFormatC);
						sheet.addCell(label);
						// 融資分類名稱
						label = new Label(16, total + i + 4,
								Util.nullToSpace(lnBusinessMap.get(Util
										.trim(c241m01b.getLnBusiness()))),
								cellFormatL);
						sheet.addCell(label);
						// 產品種類代號
						label = new Label(17, total + i + 4,
								Util.nullToSpace(c241m01b.getLnType()),
								cellFormatC);
						sheet.addCell(label);
						// 種類名稱
						label = new Label(18, total + i + 4,
								Util.nullToSpace(lnTypeMap.get(Util
										.trim(c241m01b.getLnType()))),
								cellFormatL);
						sheet.addCell(label);
						// 擔保品類別
						label = new Label(19, total + i + 4,
								Util.nullToSpace(c241m01b.getGuaranteeKind()),
								cellFormatL);
						sheet.addCell(label);
						// 逾期天數
						label = new Label(23, total + i + 4,
								Util.nullToSpace(c241m01b.getOverDueDate()),
								cellFormatL);
						sheet.addCell(label);
						i++;
						j++;
					}
					if (c241m01bs.size() > 0) {
						i--;
					} else {
						label = new Label(3, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(4, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(5, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(6, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(7, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(8, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(9, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(10, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(11, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(12, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(13, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(14, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(15, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(16, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(17, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(18, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(19, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
					}
					i++;
				}

			}

			sheet.mergeCells(0, total + i + 6, 3, total + i + 6);
			label = new Label(0, total + i + 6,
					lms2405m01.getProperty("preCTL") + sizeP
							+ lms2405m01.getProperty("door") + sizeC
							+ lms2405m01.getProperty("item"), cellNFormatL);
			sheet.addCell(label);

			sheet.mergeCells(18, total + i + 6, 22, total + i + 6);
			label = new Label(18, total + i + 6,
					lms2405m01.getProperty("randomCode") + "："
							+ c240m01a.getRandomCode(), cellNFormatR);
			sheet.addCell(label);

			sheet.mergeCells(0, total + i + 7, 22, total + i + 7);
			label = new Label(0, total + i + 7,
					lms2405m01.getProperty("lms2405m01.comment1"), cellNFormatL);
			sheet.addCell(label);

			sheet.mergeCells(0, total + i + 8, 22, total + i + 8);
			label = new Label(0, total + i + 8,
					lms2405m01.getProperty("lms2405m01.comment2"), cellNFormatL);
			sheet.addCell(label);

			test.write();
			test.close();

		} catch (Exception e) {
			logger.error("LMS2405ServiceImpl createProduceExcel EXCEPTION!!", e);
			throw new Exception("createProduceExcel exception!");
		} finally {
			if (lnBusinessMap != null) {
				lnBusinessMap.clear();
			}
			if (lnTypeMap != null) {
				lnTypeMap.clear();
			}
			if (yesNoMap != null) {
				yesNoMap.clear();
			}
		}
		return true;
	}

	@SuppressWarnings("unused")
	@Override
	public boolean producePreExcel(String oid) throws Exception {

		boolean st = false;
		String listName = "listPre";

		C240M01A c240m01a = findModelByOid(C240M01A.class, oid);
		String ranMainId = c240m01a.getMainId();

		if (c240m01a == null) {
			return false;
		}

		try {
			Properties lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);
			DocFile docFile = new DocFile();
			docFile.setBranchId(c240m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(lms2405m01.getProperty(listName)
					+ "_"
					+ lms2405m01.getProperty("c240m01a.docStatus"
							+ c240m01a.getDocStatus()) + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId("lms");
			docFile.setData(new byte[] {});
			fileService.save(docFile, false);

			// Excel Success
			st = this.createProducePreExcel(listName, c240m01a, ranMainId,
					docFile);

		} finally {

		}

		return st;

	}

	private boolean createProducePreExcel(String listName, C240M01A c240m01a,
			String ranMainId, DocFile docFile) throws Exception {

		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), ranMainId,
				listName);
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();
		Label label = null;
		Map<String, String> nckdFlagMap = null;
		try {
			nckdFlagMap = codeType.findByCodeType("lms2405m01_NckdFlag");
			if (nckdFlagMap == null) {
				nckdFlagMap = new LinkedHashMap<String, String>();
			}
			String path = PropUtil.getProperty("loadFile.dir")
					+ "excel/C240M01Pre.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			Properties lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);

			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 10);
			WritableFont headFont18 = new WritableFont(
					WritableFont.createFont("標楷體"), 18);
			WritableCellFormat NcellFormat18 = new WritableCellFormat(
					headFont18);
			NcellFormat18.setAlignment(Alignment.CENTRE);
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont12);
			cellFormatC.setWrap(true);
			cellFormatC.setAlignment(Alignment.CENTRE);
			cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);

			WritableCellFormat cellFormatR = new WritableCellFormat(headFont12);
			cellFormatR.setWrap(true);
			cellFormatR.setAlignment(Alignment.CENTRE);
			cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);

			WritableCellFormat cellFormatL = new WritableCellFormat(headFont12);
			cellFormatL.setWrap(true);
			cellFormatL.setAlignment(Alignment.LEFT);
			cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);

			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont12);
			NcellFormatR.setWrap(true);
			NcellFormatR.setAlignment(Alignment.RIGHT);

			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont12);
			NcellFormatL.setWrap(true);
			NcellFormatL.setAlignment(Alignment.LEFT);

			// 分行名稱
			label = new Label(0, 0, Util.nullToSpace(branch
					.getBranchName(c240m01a.getBranchId()))
					+ "【"
					+ lms2405m01.getProperty("listPreExcel") + "】",
					NcellFormat18);
			sheet.addCell(label);
			// 分行代碼
			label = new Label(0, 1, lms2405m01.getProperty("branchNO")
					+ Util.nullToSpace(c240m01a.getBranchId()), NcellFormatL);
			sheet.addCell(label);
			// 最遲覆審年月
			label = new Label(4, 1, lms2405m01.getProperty("lastCTLDate")
					+ "："
					+ (Util.isEmpty(c240m01a.getDataEndDate()) ? "" : TWNDate
							.toAD(c240m01a.getDataEndDate()).substring(0, 7)),
					NcellFormatR);
			sheet.addCell(label);

			// 覆審日期
			label = new Label(0, 2, lms2405m01.getProperty("CTLDate")
					+ "："
					+ (Util.isEmpty(c240m01a.getExpectedRetrialDate()) ? ""
							: TWNDate.toAD(c240m01a.getExpectedRetrialDate())),
					NcellFormatL);
			sheet.addCell(label);
			// 產生日期
			sheet.mergeCells(4, 2, 6, 2);
			label = new Label(
					4,
					2,
					lms2405m01.getProperty("date")
							+ "："
							+ CapDate
									.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD),
					NcellFormatL);
			sheet.addCell(label);

			// List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
			// .getMainId());
			List<Map<String, Object>> c240m01bList = eloandbBASEService
					.findC240M01BOrderByC241M01ASEQ(Util.trim(c240m01a
							.getMainId()));
			int total = 0;
			int sizeP = 0;
			int sizeC = 0;
			int i = 0;
			BigDecimal thr = new BigDecimal(1000);
			for (Map<String, Object> dataMap : c240m01bList) {
				if (((RetrialDocStatusEnum.已核准.getCode().equals(
						Util.trim(c240m01a.getDocStatus())) || RetrialDocStatusEnum.已產生覆審名單報告檔
						.getCode().equals(Util.trim(c240m01a.getDocStatus()))) && "Y"
						.equals(Util.trim(dataMap.get("RETRIALYN"))))
						|| (RetrialDocStatusEnum.編製中.getCode().equals(
								Util.trim(c240m01a.getDocStatus())) || RetrialDocStatusEnum.待覆核
								.getCode().equals(
										Util.trim(c240m01a.getDocStatus())))) {
					C241M01A c241m01a = c241m01aDao.findByMainId(Util
							.trim(dataMap.get("MAINID")));
					if ("Y".equals(c241m01a.getRetrialYN())) {
						sizeP++;
					}

					// 序號
					label = new Label(
							0,
							total + i + 4,
							Util.trim(c241m01a.getProjectNo()).indexOf("-") != -1 ? Util
									.trim(c241m01a.getProjectNo()).split("-")[1]
									.replace("號", "")
									: "", cellFormatL);
					sheet.addCell(label);
					// 客戶統編+重複序號
					label = new Label(1, total + i + 4,
							Util.nullToSpace(c241m01a.getCustId())
									+ Util.nullToSpace(c241m01a.getDupNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶名稱
					label = new Label(2, total + i + 4,
							Util.nullToSpace(c241m01a.getCustName()),
							cellFormatL);
					sheet.addCell(label);

					List<C241M01B> c241m01bs = c241m01bDao
							.findByMainId(c241m01a.getMainId());
					int j = 0;
					for (C241M01B c241m01b : c241m01bs) {
						if ("Y".equals(c241m01a.getRetrialYN())) {
							sizeC++;
						}
						if (j != 0) {
							label = new Label(0, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(1, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(2, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
						}
						// 額度序號
						label = new Label(3, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaNo()),
								cellFormatL);
						sheet.addCell(label);
						// 科目
						label = new Label(4, total + i + 4,
								Util.nullToSpace(c241m01b.getSubjectName()),
								cellFormatL);
						sheet.addCell(label);
						// 幣別
						label = new Label(5, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaCurr()),
								cellFormatC);
						sheet.addCell(label);
						// 授信額度
						BigDecimal mm = c241m01b.getQuotaAmt();
						label = new Label(
								6,
								total + i + 4,
								Util.isEmpty(mm) ? "0.00"
										: NumConverter
												.addComma(
														mm.divide(thr)
																.setScale(
																		2,
																		BigDecimal.ROUND_HALF_UP),
														",##0.00"), cellFormatR);
						sheet.addCell(label);
						// 契約起日
						String loanFDate = "";
						if (c241m01b.getLoanFDate() != null) {
							loanFDate = TWNDate.toAD(c241m01b.getLoanFDate());
						} else if (c241m01b.getUseFDate() != null) {
							loanFDate = TWNDate.toAD(c241m01b.getUseFDate());
						}
						label = new Label(7, total + i + 4,
								Util.nullToSpace(loanFDate), cellFormatL);
						sheet.addCell(label);
						// TODO 備註
						// StringBuilder memo = new StringBuilder();
						// Rex edit 2012_10_22 註解掉多addCell 的動作
						// sheet.addCell(label);
						label = new Label(
								8,
								total + i + 4,
								Util.isNotEmpty(c241m01b.getMajorMemo()) ? Util
										.trim(c241m01b.getMajorMemo())
										+ "\n"
										+ Util.nullToSpace(nckdFlagMap.get(Util
												.trim(c241m01a.getNCkdFlag())))
										: Util.nullToSpace(nckdFlagMap.get(Util
												.trim(c241m01a.getNCkdFlag()))),
								cellFormatL);
						sheet.addCell(label);
						i++;
						j++;
					}
					if (c241m01bs.size() > 0) {
						i--;
					} else {
						label = new Label(3, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(4, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(5, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(6, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(7, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(8, total + i + 4,
								Util.nullToSpace(nckdFlagMap.get(Util
										.trim(c241m01a.getNCkdFlag()))),
								cellFormatL);
						sheet.addCell(label);
					}
					i++;
				}
			}
			total = total + i;
			// 總共戶數
			sheet.mergeCells(0, total + 6, 2, total + 6);
			label = new Label(0, total + 6, lms2405m01.getProperty("preCTL")
					+ sizeP + lms2405m01.getProperty("door") + sizeC
					+ lms2405m01.getProperty("item"), NcellFormatL);
			sheet.addCell(label);

			// 報表亂碼
			sheet.mergeCells(4, total + 6, 8, total + 6);
			label = new Label(4, total + 6,
					lms2405m01.getProperty("randomCode") + "："
							+ Util.nullToSpace(c240m01a.getRandomCode()),
					NcellFormatR);
			sheet.addCell(label);

			// 註解一
			sheet.mergeCells(0, total + 7, 8, total + 7);
			label = new Label(0, total + 7,
					lms2405m01.getProperty("lms2405m01.comment1"), NcellFormatL);
			sheet.addCell(label);
			// 註解二
			sheet.mergeCells(0, total + 8, 8, total + 9);
			label = new Label(0, total + 8,
					lms2405m01.getProperty("lms2405m01.comment2"), NcellFormatL);
			sheet.addCell(label);

			test.write();
			test.close();

		} catch (Exception e) {
			logger.error("LMS2405ServiceImpl createProducePreExcel EXCEPTION!!", e);
			throw new Exception();
		}
		return true;
	}

	@Override
	public boolean produceChkExcel(String oid) throws CapException {
		boolean st = false;
		String listName = "listChk";

		C240M01A c240m01a = findModelByOid(C240M01A.class, oid);

		if (c240m01a == null) {
			return false;
		}
		String ranMainId = c240m01a.getMainId();
		try {
			Properties lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);
			DocFile docFile = new DocFile();
			docFile.setBranchId(c240m01a.getOwnBrId());
			docFile.setContentType("application/msexcel");
			docFile.setMainId(ranMainId);
			docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
			docFile.setFieldId(listName);
			docFile.setSrcFileName(lms2405m01.getProperty(listName)
					+ "_"
					+ lms2405m01.getProperty("c240m01a.docStatus"
							+ c240m01a.getDocStatus()) + ".xls");
			docFile.setUploadTime(CapDate.getCurrentTimestamp());
			docFile.setSysId("lms");
			docFile.setData(new byte[] {});
			fileService.save(docFile);

			// Excel Success
			st = this.createProduceChkExcel(listName, c240m01a, ranMainId,
					docFile);

		} finally {

		}

		return st;
	}

	private boolean createProduceChkExcel(String listName, C240M01A c240m01a,
			String ranMainId, DocFile docFile) throws CapException {
		String filename = null;
		File file = null;
		File file2 = null;
		filename = LMSUtil.getUploadFilePath(c240m01a.getOwnBrId(), ranMainId,
				listName);
		String xlsOid = docFile.getOid();
		file = new File(filename);
		file.mkdirs();
		Label label = null;
		Map<String, String> nckdFlagMap = null;
		try {
			nckdFlagMap = codeType.findByCodeType("lms2405m01_NckdFlag");
			if (nckdFlagMap == null) {
				nckdFlagMap = new LinkedHashMap<String, String>();
			}
			String path = PropUtil.getProperty("loadFile.dir")
					+ "excel/C240M01Chk.xls";
			URL urlRpt = null;
			urlRpt = Thread.currentThread().getContextClassLoader()
					.getResource(path);
			if (urlRpt == null)
				throw new Exception("get File fail");
			file = new File(urlRpt.toURI());
			Workbook workbook = Workbook.getWorkbook(file);
			file2 = new File(filename + "/" + xlsOid + ".xls");
			WritableWorkbook test = Workbook.createWorkbook(file2, workbook);

			WritableSheet sheet = test.getSheet(0);

			Properties lms2405m01 = MessageBundleScriptCreator
					.getComponentResource(LMS2405M01Page.class);

			WritableFont headFont12 = new WritableFont(
					WritableFont.createFont("標楷體"), 12);
			WritableFont headFont11 = new WritableFont(
					WritableFont.createFont("標楷體"), 11);
			WritableFont headFont9 = new WritableFont(
					WritableFont.createFont("標楷體"), 9);
			WritableCellFormat NcellFormat12 = new WritableCellFormat(
					headFont12);
			NcellFormat12.setAlignment(Alignment.CENTRE);
			WritableCellFormat NcellFormat11 = new WritableCellFormat(
					headFont11);
			WritableCellFormat NcellFormat9 = new WritableCellFormat(headFont9);
			WritableCellFormat NcellFormatC = new WritableCellFormat(headFont9);
			NcellFormatC.setWrap(true);
			NcellFormatC.setAlignment(Alignment.CENTRE);
			WritableCellFormat NcellFormatR = new WritableCellFormat(headFont9);
			NcellFormatR.setWrap(true);
			NcellFormatR.setAlignment(Alignment.RIGHT);
			WritableCellFormat NcellFormatL = new WritableCellFormat(headFont9);
			NcellFormatL.setWrap(true);
			NcellFormatL.setAlignment(Alignment.LEFT);
			WritableCellFormat cellFormatC = new WritableCellFormat(headFont9);
			cellFormatC.setWrap(true);
			cellFormatC.setAlignment(Alignment.CENTRE);
			cellFormatC.setBorder(Border.ALL, BorderLineStyle.THIN);
			WritableCellFormat cellFormatR = new WritableCellFormat(headFont9);
			cellFormatR.setWrap(true);
			cellFormatR.setAlignment(Alignment.RIGHT);
			cellFormatR.setBorder(Border.ALL, BorderLineStyle.THIN);
			WritableCellFormat cellFormatL = new WritableCellFormat(headFont9);
			cellFormatL.setWrap(true);
			cellFormatL.setAlignment(Alignment.LEFT);
			cellFormatL.setBorder(Border.ALL, BorderLineStyle.THIN);

			// 分行名稱
			label = new Label(0, 0, Util.nullToSpace(branch
					.getBranchName(c240m01a.getBranchId()))
					+ "【"
					+ lms2405m01.getProperty("listChkExcel") + "】",
					NcellFormat12);
			sheet.addCell(label);
			// 分行代碼
			label = new Label(0, 1, lms2405m01.getProperty("branchNO")
					+ Util.nullToSpace(c240m01a.getBranchId()), NcellFormat11);
			sheet.addCell(label);
			// 覆審日期
			label = new Label(0, 2, lms2405m01.getProperty("CTLDate")
					+ "："
					+ (Util.isEmpty(c240m01a.getExpectedRetrialDate()) ? ""
							: TWNDate.toAD(c240m01a.getExpectedRetrialDate())
									.substring(0, 10)), NcellFormat9);
			sheet.addCell(label);

			// List<C240M01B> c240m01bs = c240m01bDao.findByMainId(c240m01a
			// .getMainId());
			List<Map<String, Object>> c240m01bList = eloandbBASEService
					.findC240M01BOrderByC241M01ASEQ(Util.trim(c240m01a
							.getMainId()));
			int total = 0;
			int sizeP = 0;
			int sizeC = 0;
			int i = 0;
			BigDecimal thr = new BigDecimal(1000);
			for (Map<String, Object> dataMap : c240m01bList) {
				C241M01A c241m01a = c241m01aDao.findByMainId(Util.trim(dataMap
						.get("MAINID")));
				if ("Y".equals(Util.trim(dataMap.get("RETRIALYN")))) {
					sizeP++;
					// if (c241m01a == null ||
					// Util.isNotEmpty(c241m01a.getNCkdFlag())) {
					// continue;
					// }
					// 序號
					label = new Label(
							0,
							total + i + 4,
							Util.trim(c241m01a.getProjectNo()).indexOf("-") != -1 ? Util
									.trim(c241m01a.getProjectNo()).split("-")[1]
									.replace("號", "")
									: "", cellFormatL);
					sheet.addCell(label);
					// 覆審序號
					label = new Label(1, total + i + 4,
							Util.nullToSpace(c241m01a.getProjectNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶統編
					label = new Label(2, total + i + 4,
							Util.nullToSpace(c241m01a.getCustId())
									+ Util.nullToSpace(c241m01a.getDupNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶名稱+重複序號
					label = new Label(3, total + i + 4,
							Util.nullToSpace(c241m01a.getCustName()),
							cellFormatL);
					sheet.addCell(label);
					// 是否上傳
					String update = "N";
					if (c241m01a != null && c241m01a.getUpDate() != null) {
						update = "Y";
					}
					label = new Label(9, total + i + 4,
							Util.nullToSpace(update), cellFormatC);
					sheet.addCell(label);
					// 不覆審註記
					label = new Label(10, total + i + 4,
							Util.nullToSpace(c241m01a.getNCkdFlag()),
							cellFormatL);
					sheet.addCell(label);
					// 覆審人員
					label = new Label(11, total + i + 4,
							Util.trim(userInfoService.getUserName(Util
									.nullToSpace(c241m01a.getUpdater()))),
							cellFormatL);
					sheet.addCell(label);
					// 備註
					label = new Label(12, total + i + 4,
							Util.nullToSpace(nckdFlagMap.get(Util.trim(c241m01a
									.getNCkdFlag()))), cellFormatL);
					sheet.addCell(label);

					List<C241M01B> c241m01bs = c241m01bDao
							.findByMainId(c241m01a.getMainId());
					int j = 0;
					for (C241M01B c241m01b : c241m01bs) {
						sizeC++;
						if (j != 0) {
							label = new Label(0, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(1, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(2, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(3, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(9, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(10, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(11, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(12, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
						}
						// 額度序號
						label = new Label(4, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaNo()),
								cellFormatL);
						sheet.addCell(label);
						// 科目
						label = new Label(5, total + i + 4,
								Util.nullToSpace(c241m01b.getSubjectName()),
								cellFormatL);
						sheet.addCell(label);
						// 幣別
						label = new Label(6, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaCurr()),
								cellFormatC);
						sheet.addCell(label);
						// 授信額度
						BigDecimal mm = c241m01b.getQuotaAmt();
						label = new Label(
								7,
								total + i + 4,
								Util.isEmpty(mm) ? "0.00"
										: NumConverter
												.addComma(
														mm.divide(thr)
																.setScale(
																		2,
																		BigDecimal.ROUND_HALF_UP),
														",##0.00"), cellFormatR);
						sheet.addCell(label);
						// 契約起迄日
						String loanFDate = "";
						if (c241m01b.getLoanFDate() != null) {
							loanFDate = TWNDate.toAD(c241m01b.getLoanFDate());
						}
						String loanEDate = "";
						if (c241m01b.getLoanEDate() != null) {
							loanEDate = TWNDate.toAD(c241m01b.getLoanEDate());
						}
						label = new Label(8, total + i + 4,
								Util.nullToSpace(loanFDate) + "~"
										+ Util.nullToSpace(loanEDate),
								cellFormatC);
						sheet.addCell(label);
						i++;
						j++;
					}
					if (c241m01bs.size() > 0) {
						i--;
					} else {
						label = new Label(4, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(5, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(6, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(7, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(8, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
					}
					i++;
				}
			}
			for (Map<String, Object> dataMap : c240m01bList) {
				C241M01A c241m01a = c241m01aDao.findByMainId(Util.trim(dataMap
						.get("MAINID")));
				if ("N".equals(Util.trim(dataMap.get("RETRIALYN")))
						&& Util.isNotEmpty(c241m01a.getProjectNo())) {
					// if (c241m01a == null ||
					// Util.isNotEmpty(c241m01a.getNCkdFlag())) {
					// continue;
					// }
					// 序號
					label = new Label(
							0,
							total + i + 4,
							Util.trim(c241m01a.getProjectNo()).indexOf("-") != -1 ? Util
									.trim(c241m01a.getProjectNo()).split("-")[1]
									.replace("號", "")
									: "", cellFormatL);
					sheet.addCell(label);
					// 覆審序號
					label = new Label(1, total + i + 4,
							Util.nullToSpace(c241m01a.getProjectNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶統編
					label = new Label(2, total + i + 4,
							Util.nullToSpace(c241m01a.getCustId())
									+ Util.nullToSpace(c241m01a.getDupNo()),
							cellFormatL);
					sheet.addCell(label);
					// 客戶名稱+重複序號
					label = new Label(3, total + i + 4,
							Util.nullToSpace(c241m01a.getCustName()),
							cellFormatL);
					sheet.addCell(label);
					// 是否上傳
					String update = "N";
					if (c241m01a != null && c241m01a.getUpDate() != null) {
						update = "Y";
					}
					label = new Label(9, total + i + 4,
							Util.nullToSpace(update), cellFormatC);
					sheet.addCell(label);
					// 不覆審註記
					label = new Label(10, total + i + 4,
							Util.nullToSpace(c241m01a.getNCkdFlag()),
							cellFormatL);
					sheet.addCell(label);
					// 覆審人員
					label = new Label(11, total + i + 4,
							Util.trim(userInfoService.getUserName(Util
									.nullToSpace(c241m01a.getUpdater()))),
							cellFormatL);
					sheet.addCell(label);
					// 備註
					label = new Label(12, total + i + 4,
							Util.nullToSpace(nckdFlagMap.get(Util.trim(c241m01a
									.getNCkdFlag()))), cellFormatL);
					sheet.addCell(label);

					List<C241M01B> c241m01bs = c241m01bDao
							.findByMainId(c241m01a.getMainId());
					int j = 0;
					for (C241M01B c241m01b : c241m01bs) {
						if (j != 0) {
							label = new Label(0, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(1, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(2, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(3, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(9, total + i + 4, "", cellFormatL);
							sheet.addCell(label);
							label = new Label(10, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(11, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
							label = new Label(12, total + i + 4, "",
									cellFormatL);
							sheet.addCell(label);
						}
						// 額度序號
						label = new Label(4, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaNo()),
								cellFormatL);
						sheet.addCell(label);
						// 科目
						label = new Label(5, total + i + 4,
								Util.nullToSpace(c241m01b.getSubjectName()),
								cellFormatL);
						sheet.addCell(label);
						// 幣別
						label = new Label(6, total + i + 4,
								Util.nullToSpace(c241m01b.getQuotaCurr()),
								cellFormatC);
						sheet.addCell(label);
						// 授信額度
						BigDecimal mm = c241m01b.getQuotaAmt();
						label = new Label(
								7,
								total + i + 4,
								Util.isEmpty(mm) ? "0.00"
										: NumConverter
												.addComma(
														mm.divide(thr)
																.setScale(
																		2,
																		BigDecimal.ROUND_HALF_UP),
														",##0.00"), cellFormatR);
						sheet.addCell(label);
						// 契約起迄日
						String loanFDate = "";
						if (c241m01b.getLoanFDate() != null) {
							loanFDate = TWNDate.toAD(c241m01b.getLoanFDate());
						}
						String loanEDate = "";
						if (c241m01b.getLoanEDate() != null) {
							loanEDate = TWNDate.toAD(c241m01b.getLoanEDate());
						}
						label = new Label(8, total + i + 4,
								Util.nullToSpace(loanFDate) + "~"
										+ Util.nullToSpace(loanEDate),
								cellFormatC);
						sheet.addCell(label);
						i++;
						j++;
					}
					if (c241m01bs.size() > 0) {
						i--;
					} else {
						label = new Label(4, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(5, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(6, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(7, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(8, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(9, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(10, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(11, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
						label = new Label(12, total + i + 4, "", cellFormatL);
						sheet.addCell(label);
					}
					i++;
				}
			}
			// 總共戶數
			sheet.mergeCells(0, total + i + 6, 3, total + i + 6);
			label = new Label(0, total + i + 6,
					lms2405m01.getProperty("realDoor") + "：" + sizeP
							+ lms2405m01.getProperty("door") + sizeC
							+ lms2405m01.getProperty("item"), NcellFormatL);
			sheet.addCell(label);
			// 產生人員
			sheet.mergeCells(9, total + i + 6, 12, total + i + 6);
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			label = new Label(9, total + i + 6,
					lms2405m01.getProperty("producer") + "："
							+ userInfoService.getUserName(user.getUserId()),
					NcellFormatR);
			sheet.addCell(label);
			// 報表亂碼
			sheet.mergeCells(8, total + i + 7, 12, total + i + 7);
			label = new Label(8, total + i + 7,
					lms2405m01.getProperty("randomCode") + "："
							+ Util.nullToSpace(c240m01a.getRandomCode()),
					NcellFormatR);
			sheet.addCell(label);

			test.write();
			test.close();
		} catch (Exception e) {
			logger.error("LMS2405ServiceImpl createProduceChkExcel EXCEPTION!!", e);
			throw new CapException();
		}
		return true;

	}

	@Override
	public List<DocFile> findDocFile(String mainId, String FieldId) {
		List<DocFile> docFile = docFileDao.findByMainIdAndFieldId(mainId,
				FieldId);
		return docFile;

	}

	@Override
	public int saveCTLCount(String mainId) {
		List<C240M01B> c240m01bs = c240m01bDao.findByMainId(mainId);
		int count = 0;
		for (C240M01B c240m01b : c240m01bs) {
			C241M01A c241m01a = c241m01aDao.findByMainId(c240m01b
					.getRefMainId());
			if (UtilConstants.DEFAULT.是.equals(Util.nullToSpace(c241m01a
					.getRetrialYN()))) {
				count++;
			}
		}
		C240M01A c240m01a = c240m01aDao.findByMainId(mainId);
		c240m01a.setReQuantity(count);
		c240m01aDao.save(c240m01a);
		return count;
	}

	@Override
	public C240M01A findC240M01AMByMainId(String mainId) {
		return c240m01aDao.findByMainId(mainId);
	}
	
	@Override
	public C240M01Z findCycmnBrno(Date cycmn, String brno){
		return c240m01zDao.findByUniqueKey(cycmn, brno);
	}
}
