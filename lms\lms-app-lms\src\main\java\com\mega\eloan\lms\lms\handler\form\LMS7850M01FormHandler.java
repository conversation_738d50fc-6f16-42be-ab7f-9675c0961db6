/* 
 * lms7850FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.ELRoleEnum;
import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.pages.CLS2501M01Page;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.lms.pages.LMS7840M01Page;
import com.mega.eloan.lms.lms.pages.LMS7850M01Page;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.lms.service.LMS7840Service;
import com.mega.eloan.lms.lms.service.LMS7850Service;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisELF511Service;
import com.mega.eloan.lms.mfaloan.service.MisMislnratService;
import com.mega.eloan.lms.model.C900M01D;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L785M01A;
import com.mega.eloan.lms.model.L999LOG01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.iisi.cap.utils.CapWebUtil;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 停權解除維護FormHandler
 * </pre>
 * 
 * @since 2013/1/21
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/21,Miller,new
 *          </ul>
 */
@Scope("request")
@Controller("lms7850m01formhandler")
public class LMS7850M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS7850Service service7850;

	@Resource
	BranchService branch;

	@Resource
	MisELF511Service misElf511Service;

	@Resource
	UserInfoService userinfoservice;

	@Resource
	MisELF506Service misELF506Service;

	@Resource
	LMSService lmsservice;

	@Resource
	NumberService number;

	@Resource
	LMS7840Service lms7840Service;

	@Resource
	MisMislnratService misMislnratService;

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	CodeTypeService codetypeservice;

	/**
	 * 依照使用者選擇刪除停權所有相關資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startDel(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		L785M01A l785m01a = service7850.findL785m01aByMainId(mainId);
		if (isAuthForDel(mainId)) {
			// 具有刪除權限
			// 進行刪除
			// deleteTable.properties 有設L785M01A deletedTime 刪除時一併刪除L140M01Q
			service7850.delete(l785m01a);

		} else {
			// 不具有刪除權限
			Map<String, String> param = new HashMap<String, String>();
			param.put("txCode", UtilConstants.Mark.HTMLSPACE);
			param.put("methodName", UtilConstants.Mark.HTMLSPACE);
			param.put("authType", UtilConstants.Mark.HTMLSPACE);
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0004", param), getClass());
		}
		// 印出刪除成功訊息!
		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));
		return result;
	}

	/**
	 * 判定是否具有刪除權限
	 * 
	 * @param oid
	 *            文件Oid
	 * @return true: 具有權限, false: 不具有權限
	 */
	private boolean isAuthForDel(String mainId) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		// 主管
		if (user.getRoles().containsKey(ELRoleEnum.主管.getCode())) {
			return true;
		} else {
			// 非主管
			L785M01A l785m01a = service7850.findL785m01aByMainId(mainId);
			if (user.getUserId().equals(l785m01a.getUpdater())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 查詢停權明細檔資料並建立停權主檔
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult startQuery(PageParameters params)
			throws CapException {
		// 取得當前使用者資料
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = IDGenerator.getUUID();
		String caseBrId = Util.trim(params.getString("caseBrId"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String custName = Util.trim(params.getString("custName"));
		String cntrNo = Util.trim(params.getString("cntrNo"));

		// 開始設定停權主檔
		L785M01A l785m01a = new L785M01A();
		l785m01a.setMainId(mainId);
		l785m01a.setCustId(custId);
		l785m01a.setDupNo(dupNo);
		l785m01a.setCustName(custName);
		l785m01a.setCaseBrId(caseBrId);
		l785m01a.setOwnBrId(user.getUnitNo());
		l785m01a.setDocStatus(CreditDocStatusEnum.授管處_停權編製中);
		l785m01a.setDocURL("/lms/lms7850m01");
		l785m01a.setUpdater(user.getUserId());
		l785m01a.setCaseDate(new Date());

		service7850.save(l785m01a);

		// 開始設定要拋到前端的參數

		result.set(EloanConstants.MAIN_ID, mainId);
		result.set(EloanConstants.OID, Util.trim(l785m01a.getOid()));
		result.set("docURL", "/lms/lms7850m01");
		result.set("mainDocStatus", CreditDocStatusEnum.授管處_停權編製中.getCode());

		return result;
	}

	/**
	 * 儲存畫面主要資料
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL785m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();

		String oid = Util.trim(params.getString(EloanConstants.OID));
		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
		String filterForm = params.getString("filterForm");

		L785M01A l785m01a = null;
		if (Util.equals(oid, "")) {
			l785m01a = new L785M01A();
			l785m01a.setMainId(IDGenerator.getUUID());
			l785m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l785m01a.setCreator(user.getUserId());
		} else {
			l785m01a = service7850.findL785m01aByMainId(mainId);
			if (l785m01a == null) {
				l785m01a = new L785M01A();
				l785m01a.setMainId(IDGenerator.getUUID());
				l785m01a.setCreateTime(CapDate.getCurrentTimestamp());
				l785m01a.setCreator(user.getUserId());
			}
		}

		// if (Util.equals(Util.trim(l785m01a.getMainId()), "")) {
		// l785m01a.setMainId(IDGenerator.getUUID());
		// }

		l785m01a.setDocStatus(CreditDocStatusEnum.授管處_停權編製中);
		l785m01a.setRandomCode(IDGenerator.getRandomCode());
		// UPGRADE: 待確認，URL是否正確
		l785m01a.setDocURL(CapWebUtil.getDocUrl(LMS7850M01Page.class));
		l785m01a.setOwnBrId(user.getUnitNo());
		l785m01a.setApprover(null);
		l785m01a.setApproveTime(null);

		l785m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l785m01a.setUpdater(user.getUserId());
		l785m01a.setCaseDate(new Date());
		l785m01a.setItemDscr(filterForm);
		l785m01a.setDeletedTime(null);

		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若無案號則進行給號
		if (Util.isEmpty(Util.trim(l785m01a.getCaseSeq()))
				&& Util.isEmpty(Util.trim(l785m01a.getCaseNo()))) {
			l785m01a.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L785M01A.class, unit.getUnitNo(), null, 99999)));
			l785m01a.setCaseYear(Util.parseInt(TWNDate.toAD(new Date())
					.substring(0, 4)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			caseNum.append(
					Util.toFullCharString(Util.trim(l785m01a.getCaseYear())))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(l785m01a.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			l785m01a.setCaseNo(caseNum.toString());
		}

		result.set("caseNo", l785m01a.getCaseNo());

		service7850.save(l785m01a);

		// result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
		// .getMainMessage(this.getComponent(),
		// UtilConstants.AJAX_RSP_MSG.儲存成功));
		// if(true){
		// throw new CapMessageException("TEST", getClass());
		// }
		return result;
	}

	/**
	 * flow案件簽報書
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult flowAction(PageParameters params)
			throws CapException {
		// 儲存and檢核
		String oid = Util.trim(params.getString(EloanConstants.MAIN_OID));
		String list = Util.trim(params.getString("list"));
		if (Util.isNotEmpty(oid)) {
			// 若oid不為空，則走單一流程
			L785M01A l785m01a = (L785M01A) service7850.findL785m01aByOid(oid);
			if (l785m01a == null) {
				logger.info("\n l785m01a=====> null");
			}
			startFlow(params, l785m01a, oid);
		} else {
			// 若list不為空，代表同時有很多案件跑同一流程
			if (Util.isNotEmpty(list)) {
				String oids[] = list.split(",");
				for (String thisOid : oids) {
					L785M01A l785m01a = (L785M01A) service7850
							.findL785m01aByOid(thisOid);
					if (l785m01a == null) {
						logger.info("\n l785m01a=====> null");
					}
					startFlow(params, l785m01a, thisOid);
				}
			} else {
				// 例外情形，正常下不會發生
				logger.info("\n l785m01a=====> null");
			}
		}
		return new CapAjaxFormResult();
	}

	/**
	 * 開始進行流程
	 * 
	 * @param params
	 * @param parent
	 * @param l785m01a
	 * @param oid
	 * @throws CapMessageException
	 */
	private void startFlow(PageParameters params,
			L785M01A l785m01a, String oid) throws CapMessageException {
		if (!Util.isEmpty(l785m01a)) {
			try {
				service7850.flowAction(oid, l785m01a,
						params.containsKey("flowAction"),
						params.getString("flowAction", ""));
			} catch (FlowMessageException t1) {
				logger.error(
						"[flowAction] lms7850Service.flowAction FlowException!!",
						t1);
				if (t1.getExtraMessage() == null
						|| t1.getExtraMessage().isEmpty()) {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage()), getClass());
				} else {
					throw new CapMessageException(RespMsgHelper.getMessage(t1.getMessage(), t1.getExtraMessage()),
							getClass());
				}
			} catch (Throwable t1) {
				logger.error(
						"[flowAction] lms7850Service.flowAction FlowException!!",
						t1);
				throw new CapMessageException(t1.getMessage(), getClass());
			}
		}
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 依照完整客戶統編取得Map資料
	 * 
	 * @param allCust
	 *            完整客戶統編
	 * @return Map資料
	 */
	private Map<String, String> getCustMap(String allCust) {
		Map<String, String> map = new HashMap<String, String>();
		if (Util.isNotEmpty(allCust)) {
			map.put("custId", Util.trim((LMSUtil.checkSubStr(allCust, 0,
					allCust.length() - 1)) ? allCust.substring(0,
					allCust.length() - 1) : UtilConstants.Mark.SPACE));
			map.put("dupNo",
					Util.trim((LMSUtil.checkSubStr(allCust,
							allCust.length() - 1)) ? allCust.substring(allCust
							.length() - 1) : UtilConstants.Mark.SPACE));
		}
		return map;
	}

	/**
	 * 依照使用者id傳回對應名稱，若為空值則仍傳回使用者id
	 * 
	 * @param id
	 *            使用者id
	 * @return 空值: 使用者id 非空值: 使用者id + " " + 使用者名稱
	 */
	private String getPerName(String id) {
		StringBuilder sb = new StringBuilder();
		if (!Util.isEmpty(userinfoservice.getUserName(id))) {
			sb.append(id).append(" ").append(userinfoservice.getUserName(id));
		} else {
			sb.append(id);
		}
		return sb.toString();
	}

	/**
	 * 取得 申貸戶 異常通報戶紀錄 J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 
	 * @param params
	 * @param parent
	 * @return
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult openCntrDocPdf(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String tabFormMainId = Util.trim(params.getString("tabFormMainId"));

		L140M01A l140m01a = lmsservice.findModelByMainId(L140M01A.class,
				tabFormMainId);
		L120M01C l120m01c = l140m01a.getL120m01c();
		String mainId = l120m01c.getMainId();
		L120M01A l120m01a = lmsservice
				.findModelByMainId(L120M01A.class, mainId);

		String isCls = LMSUtil.isClsCase(l120m01a) ? "Y" : "N";

		result.set("itemType", l120m01c.getItemType());
		result.set("rptMainId", l120m01a.getMainId());
		result.set("typCd", l120m01a.getTypCd());
		result.set("tabFormOid", l140m01a.getOid());
		result.set("isCls", isCls);

		return result;
	}

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryFxRateText(PageParameters params)
			throws CapException {

		List<Map<String, Object>> rateList = misMislnratService
				.findMislnratAllRateCode();

		TreeMap<String, String> itemList = new TreeMap<String, String>();

		for (Map<String, Object> rateMap : rateList) {

			itemList.put(Util.trim(MapUtils.getString(rateMap, "LR_CODE")),
					Util.trim(MapUtils.getString(rateMap, "LR_RATE_CNAME")));
		}

		CapAjaxFormResult result = new CapAjaxFormResult(itemList);

		return result;
	}// close queryItemList

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getBatchRunResult(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7850M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();

		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));

		if (Util.equals(mainId, "")) {
			// 新案
			return result;
		}

		L785M01A meta = service7850.findL785m01aByMainId(mainId);

		L999LOG01A logDoc = lms1405Service.findL999log01aByMainId(mainId);

		if (meta != null) {

			if (logDoc != null) {
				result = DataParse.toResult(logDoc, DataParse.Delete,
						new String[] { EloanConstants.MAIN_ID,
								EloanConstants.OID });
			}

			Locale locale = null;
			locale = LMSUtil.getLocale();
			Map<String, String> typCdMap = codetypeservice.findByCodeType(
					"TypCd", locale.toString());

			Map<String, String> l120M01A_docTypeMap = codetypeservice
					.findByCodeType("L120M01A_docType", locale.toString());

			Map<String, String> l120M01A_docKindMap = codetypeservice
					.findByCodeType("L120M01A_docKind", locale.toString());

			Map<String, String> l120M01A_docCodeMap = codetypeservice
					.findByCodeType("L120M01A_docCode", locale.toString());

			String formatItemDscr = LMSUtil.getFormatItemDscr(
					Util.trim(meta.getItemDscr()), prop, typCdMap,
					l120M01A_docTypeMap, l120M01A_docKindMap,
					l120M01A_docCodeMap);

			result.set(
					"caseDate",
					meta.getCaseDate() != null ? CapDate.formatDate(
							meta.getCaseDate(),
							UtilConstants.DateFormat.YYYY_MM_DD) : null);
			result.set("createTime", meta.getCreateTime());

			result.set(
					"creator",
					!Util.isEmpty(userinfoservice.getUserName(meta.getCreator())) ? userinfoservice
							.getUserName(meta.getUpdater()) : Util.trim(meta
							.getCreator()));

			result.set(
					"updater",
					!Util.isEmpty(userinfoservice.getUserName(meta.getUpdater())) ? userinfoservice
							.getUserName(meta.getUpdater()) : Util.trim(meta
							.getUpdater()));

			result.set("updateTime", meta.getUpdateTime());

			result.set(
					"approver",
					!Util.isEmpty(userinfoservice.getUserName(meta
							.getApprover())) ? userinfoservice.getUserName(meta
							.getApprover()) : Util.trim(meta.getApprover()));

			result.set("approveTime", meta.getApproveTime());
			result.set("caseNo", meta.getCaseNo());
			result.set("deletedTime", meta.getDeletedTime());
			result.set("itemDscr", formatItemDscr);
			result.set("itemDscrJson",new CapAjaxFormResult(JSONObject
					.fromObject(meta.getItemDscr())));			
			result.set("logMainId", meta.getMainId());

		}
		return result;
	}

	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getExecMsg(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7840M01Page.class);
		CapAjaxFormResult result = new CapAjaxFormResult();
		String mainId = Util.trim(params.getString("logMainId"));
		L999LOG01A logDoc = lms1405Service.findL999log01aByMainId(mainId);

		if (logDoc != null) {
			if (Util.equals(Util.trim(logDoc.getExecMsg()), "")) {

				// L784M01A.noExecMsg=無執行訊息！
				result.set("execMsg", prop.getProperty("L784M01A.noExecMsg"));
			} else {
				result.set("execMsg", Util.trim(logDoc.getExecMsg()));
			}

		} else {
			// L784M01A.noLogDoc=無查詢紀錄檔L999LOG01A！
			result.set("execMsg", prop.getProperty("L784M01A.noLogDoc"));
		}

		return result;
	}

	@SuppressWarnings({ "unchecked" })
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryClsSubject(PageParameters params)
			throws CapException {

		List<C900M01D> c900m01ds = lms7840Service.queryC900m01ds();

		TreeMap<String, String> itemList = new TreeMap<String, String>();

		for (C900M01D c900m01d : c900m01ds) {

			itemList.put(Util.trim(c900m01d.getSubjCode()),
					Util.trim(c900m01d.getSubjNm()));
		}

		CapAjaxFormResult result = new CapAjaxFormResult(itemList);

		return result;
	}// close queryItemList

	/**
	 * 新增查詢表
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult newl785m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();

		L785M01A l785m01a = null;
		l785m01a = new L785M01A();
		l785m01a.setMainId(IDGenerator.getUUID());
		l785m01a.setCreateTime(CapDate.getCurrentTimestamp());
		l785m01a.setCreator(user.getUserId());

		l785m01a.setDocStatus(CreditDocStatusEnum.授管處_停權編製中);
		l785m01a.setRandomCode(IDGenerator.getRandomCode());
		l785m01a.setDocURL(CapWebUtil.getDocUrl(LMS7850M01Page.class));
		l785m01a.setOwnBrId(user.getUnitNo());
		l785m01a.setApprover(null);
		l785m01a.setApproveTime(null);

		l785m01a.setUpdateTime(CapDate.getCurrentTimestamp());
		l785m01a.setUpdater(user.getUserId());
		l785m01a.setCaseDate(new Date());
		l785m01a.setItemDscr(null);

		String txCode = Util.trim(params
				.getString(EloanConstants.TRANSACTION_CODE));
		l785m01a.setTxCode(txCode);
		l785m01a.setDeletedTime(CapDate.getCurrentTimestamp());
		service7850.save(l785m01a);

		CapAjaxFormResult result = new CapAjaxFormResult();

		result.set(EloanConstants.OID, l785m01a.getOid());
		result.set(EloanConstants.MAIN_ID, l785m01a.getMainId());
		result.set("docURL", l785m01a.getDocURL());

		return result;

	}

}
