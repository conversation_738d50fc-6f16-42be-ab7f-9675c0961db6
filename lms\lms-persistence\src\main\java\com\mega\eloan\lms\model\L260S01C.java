/* 
 * L260S01C.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;
import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 貸後管理實價登錄檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L260S01C", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class L260S01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 
	 * 文件編號<p/>
	 * LMS.L260M01D.oid
	 */
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/**
	 * 分行代碼
	 * CMS.C101M29.branch
	 */
	@Column(name="BRANCH", length=3, columnDefinition="CHAR(3)")
	private String branch;

	/**
	 * 統一編號
	 * CMS.C101M29.custId
	 */
	@Size(max=10)
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/**
	 * 客戶名稱
	 * CMS.C101M29.custName
	 */
	@Size(max=150)
	@Column(name="CUSTNAME", length=150, columnDefinition="VARCHAR(150)")
	private String custName;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="CNTRNO", length=12, columnDefinition="CHAR(12)")
	private String cntrNo;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	@Column(name="DELETEDTIME", columnDefinition="TIMESTAMP")
	private Timestamp deletedTime;

	/** 
	 * 實價登錄PDF_oid<p/>
	 * LMS.BDocFile.oid
	 */
	@Size(max=32)
	@Column(name="RASPFILEOID", length=32, columnDefinition="CHAR(32)")
	private String raspFileOid;

	/** 
	 * 估價報告書_mainId<p/>
	 * CMS.C101M29.mainId
	 */
	@Size(max=32)
	@Column(name="REFMAINID", length=32, columnDefinition="CHAR(32)")
	private String refMainId;

	/** 
	 * 擔保品編號<p/>
	 * CMS.C101M29.collNo
	 */
	@Size(max=9)
	@Column(name="COLLNO", length=9, columnDefinition="CHAR(09)")
	private String collNo;

	/** 
	 * 契約購價<p/>
	 * CMS.C101M29.contractPrice
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="CONTRACTPRICE", columnDefinition="DECIMAL(13,0)")
	private BigDecimal contractPrice;

	/** 
	 * 買賣契約日期<p/>
	 * to CMS.C101M06
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="CNTRDATE", columnDefinition="DATE")
	private Date cntrDate;

	/** 
	 * 查詢實價登錄日期<p/>
	 * CMS.C101M29.createtime
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="QUERYRASPDATE", columnDefinition="DATE")
	private Date queryRaspDate;

	/** 
	 * 實價登錄狀態<p/>
	 * to CMS.C101M06<br/>
	 *  c101m06_rasp_status
	 */
	@Size(max=1)
	@Column(name="RASPSTAT", length=1, columnDefinition="CHAR(01)")
	private String raspStat;

	/** 
	 * 實價登錄因應方案<p/>
	 * to CMS.C101M06<br/>
	 *  c101m06_rasp_way
	 */
	@Size(max=1)
	@Column(name="RASPWAY", length=1, columnDefinition="CHAR(01)")
	private String raspWay;

	/** 
	 * 實價登錄因應方案完成日期<p/>
	 * to CMS.C101M06
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="RWAYDT", columnDefinition="DATE")
	private Date rWayDt;

	/** 
	 * 實價登錄價格<p/>
	 * to CMS.C101M06
	 */
	@Digits(integer=13, fraction=0, groups = Check.class)
	@Column(name="RASPAMT", columnDefinition="DECIMAL(13,0)")
	private BigDecimal raspAmt;

	/** 
	 * 實價登錄說明<p/>
	 * to CMS.C101M06
	 */
	@Size(max=90)
	@Column(name="RASPDSCR", length=90, columnDefinition="VARCHAR(90)")
	private String raspDscr;

	/** 
	 * 佐證資料_oid<p/>
	 * LMS.BDocFile.oid
	 */
	@Size(max=32)
	@Column(name="FILEOID", length=32, columnDefinition="CHAR(32)")
	private String fileOid;

	/**
	 * 是否通過檢核<p/>
	 * Y/N
	 */
	@Size(max=1)
	@Column(name="CHECKYN", length=1, columnDefinition="CHAR(1)")
	private String checkYN;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 
	 * 取得文件編號<p/>
	 * LMS.L260M01D.oid
	 */
	public String getMainId() {
		return this.mainId;
	}
	/**
	 *  設定文件編號<p/>
	 *  LMS.L260M01D.oid
	 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得分行代碼 **/
	public String getBranch() { return this.branch; }

	/** 設定分行代碼 **/
	public void setBranch(String value) { this.branch = value; }

	/** 取得統一編號 **/
	public String getCustId() { return this.custId; }

	/** 設定統一編號 **/
	public void setCustId(String value) { this.custId = value; }

	/** 取得客戶名稱 **/
	public String getCustName() { return this.custName; }
	/** 設定客戶名稱 **/
	public void setCustName(String value) {	this.custName = value; }

	/** 取得額度序號 **/
	public String getCntrNo() {
		return this.cntrNo;
	}
	/** 設定額度序號 **/
	public void setCntrNo(String value) {
		this.cntrNo = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}

	/** 
	 * 取得刪除註記<p/>
	 * 文件刪除時使用(非立即性刪除)
	 */
	public Timestamp getDeletedTime() {
		return this.deletedTime;
	}
	/**
	 *  設定刪除註記<p/>
	 *  文件刪除時使用(非立即性刪除)
	 **/
	public void setDeletedTime(Timestamp value) {
		this.deletedTime = value;
	}

	/** 
	 * 取得實價登錄PDF_oid<p/>
	 * LMS.BDocFile.oid
	 */
	public String getRaspFileOid() {
		return this.raspFileOid;
	}
	/**
	 *  設定實價登錄PDF_oid<p/>
	 *  LMS.BDocFile.oid
	 **/
	public void setRaspFileOid(String value) {
		this.raspFileOid = value;
	}

	/** 
	 * 取得估價報告書_mainId<p/>
	 * CMS.C101M29.mainId
	 */
	public String getRefMainId() {
		return this.refMainId;
	}
	/**
	 *  設定估價報告書_mainId<p/>
	 *  CMS.C101M29.mainId
	 **/
	public void setRefMainId(String value) {
		this.refMainId = value;
	}

	/** 
	 * 取得擔保品編號<p/>
	 * CMS.C101M29.collNo
	 */
	public String getCollNo() {
		return this.collNo;
	}
	/**
	 *  設定擔保品編號<p/>
	 *  CMS.C101M29.collNo
	 **/
	public void setCollNo(String value) {
		this.collNo = value;
	}

	/** 
	 * 取得契約購價<p/>
	 * CMS.C101M29.contractPrice
	 */
	public BigDecimal getContractPrice() {
		return this.contractPrice;
	}
	/**
	 *  設定契約購價<p/>
	 *  CMS.C101M29.contractPrice
	 **/
	public void setContractPrice(BigDecimal value) {
		this.contractPrice = value;
	}

	/** 
	 * 取得買賣契約日期<p/>
	 * to CMS.C101M06
	 */
	public Date getCntrDate() {
		return this.cntrDate;
	}
	/**
	 *  設定買賣契約日期<p/>
	 *  to CMS.C101M06
	 **/
	public void setCntrDate(Date value) {
		this.cntrDate = value;
	}

	/** 
	 * 取得查詢實價登錄日期<p/>
	 * CMS.C101M29.createtime
	 */
	public Date getQueryRaspDate() {
		return this.queryRaspDate;
	}
	/**
	 *  設定查詢實價登錄日期<p/>
	 *  CMS.C101M29.createtime
	 **/
	public void setQueryRaspDate(Date value) {
		this.queryRaspDate = value;
	}

	/** 
	 * 取得實價登錄狀態<p/>
	 * to CMS.C101M06<br/>
	 *  c101m06_rasp_status
	 */
	public String getRaspStat() {
		return this.raspStat;
	}
	/**
	 *  設定實價登錄狀態<p/>
	 *  to CMS.C101M06<br/>
	 *  c101m06_rasp_status
	 **/
	public void setRaspStat(String value) {
		this.raspStat = value;
	}

	/** 
	 * 取得實價登錄因應方案<p/>
	 * to CMS.C101M06<br/>
	 *  c101m06_rasp_way
	 */
	public String getRaspWay() {
		return this.raspWay;
	}
	/**
	 *  設定實價登錄因應方案<p/>
	 *  to CMS.C101M06<br/>
	 *  c101m06_rasp_way
	 **/
	public void setRaspWay(String value) {
		this.raspWay = value;
	}

	/** 
	 * 取得實價登錄因應方案完成日期<p/>
	 * to CMS.C101M06
	 */
	public Date getRWayDt() {
		return this.rWayDt;
	}
	/**
	 *  設定實價登錄因應方案完成日期<p/>
	 *  to CMS.C101M06
	 **/
	public void setRWayDt(Date value) {
		this.rWayDt = value;
	}

	/** 
	 * 取得實價登錄價格<p/>
	 * to CMS.C101M06
	 */
	public BigDecimal getRaspAmt() {
		return this.raspAmt;
	}
	/**
	 *  設定實價登錄價格<p/>
	 *  to CMS.C101M06
	 **/
	public void setRaspAmt(BigDecimal value) {
		this.raspAmt = value;
	}

	/** 
	 * 取得實價登錄說明<p/>
	 * to CMS.C101M06
	 */
	public String getRaspDscr() {
		return this.raspDscr;
	}
	/**
	 *  設定實價登錄說明<p/>
	 *  to CMS.C101M06
	 **/
	public void setRaspDscr(String value) {
		this.raspDscr = value;
	}

	/** 
	 * 取得佐證資料_oid<p/>
	 * LMS.BDocFile.oid
	 */
	public String getFileOid() {
		return this.fileOid;
	}
	/**
	 *  設定佐證資料_oid<p/>
	 *  LMS.BDocFile.oid
	 **/
	public void setFileOid(String value) {
		this.fileOid = value;
	}

	/**
	 * 取得是否通過檢核<p/>
	 * Y/N
	 */
	public String getCheckYN() {
		return this.checkYN;
	}
	/**
	 *  設定是否通過檢核<p/>
	 *  Y/N
	 **/
	public void setCheckYN(String value) {
		this.checkYN = value;
	}
}
