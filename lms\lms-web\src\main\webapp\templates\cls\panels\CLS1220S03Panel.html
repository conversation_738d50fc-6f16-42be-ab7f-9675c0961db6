<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <script type="text/javascript">loadScript('pagejs/cls/CLS1220S03Panel')	</script>
			<!--======================================================-->
            <table class="tb2" width="100%">
            	<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.comName'}">服務單位名稱</th:block>&nbsp;&nbsp;</td>
					<td colspan="3" ><input type="text" id="comName" name="comName" class="max " maxlength="250" size="30" /></td>
				</tr>
            	<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.comAddr'}">服務單位地址</th:block>&nbsp;&nbsp;</td>
					<td colspan="3" >
						<input type="text" id="comCity" name="comCity" style="display:none;" />
						<input type="text" id="comZip" name="comZip" style="display:none;" />
						<input type="text" id="comAddr" name="comAddr" style="display:none;" />
						<a href="#" id="comTargetLink" class="readOnly" ><span id="comTarget" class="field comboSpace" ></span></a>
					</td>
				</tr>
				<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.comTel'}">服務單位電話</th:block>&nbsp;&nbsp;</td>
					<td ><input type="text" id="comTel" name="comTel" class="max" maxlength="150" /></td>
				</tr>
				<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.jobType'}">職業別</th:block>&nbsp;&nbsp;</td>
					<td >
						<select id="jobType1" name="jobType1" class="" codeType="jobType" ></select><br/>
						<select id="jobType2" name="jobType2" class="" style="width:250px" ></select>
					</td>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.jobTitle'}">職稱</th:block>&nbsp;&nbsp;</td>
					<td ><select id="jobTitle" name="jobTitle" class="" codeType="lms1205s01_jobTitle" ></select></td>
				</tr>
				<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.seniority'}">年資(同質性工作連續年資)</th:block>&nbsp;&nbsp;</td>
					<td ><input type="text" id="seniority" name="seniority" class="max number " maxlength="2" size="2" /></td>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.payAmt'}">年薪</th:block>&nbsp;&nbsp;</td>
					<td >
						<input type="text" id="payAmt" name="payAmt" class="max number " maxlength="13" size="13" />
						<th:block th:text="#{'label.twd10000'}"></th:block>
					</td>
				</tr>
            	<tr>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.othType'}">其他收入項目</th:block>&nbsp;&nbsp;</td>
					<td ><input type="checkbox" id="othType" name="othType" class="max"  /></td>
					<td class="hd2" align="right" ><th:block th:text="#{'C101S01B.othAmt'}">其他收入</th:block>&nbsp;&nbsp;</td>
					<td>
						<input type="text" id="othAmt" name="othAmt" class="max number" maxlength="13" size="13" />
						<th:block th:text="#{'label.twd10000'}"></th:block>
					</td>
				</tr>
			</table>
        </th:block>
    </body>
</html>