<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
            <!-- r6的路徑--><!--<script type="text/javascript">
            alert(webroot);
            alert('<script type="text/javascript" src="'+webroot+'/pagejs/crs/LMS2415S02Panel.js" ></scr' + 'ipt>');
            document.write('<script type="text/javascript" src="'+webroot+'/pagejs/crs/LMS2415S02Panel.js" ></scr' + 'ipt>');
            document.write('xxxxxxxxxxxxxxxxxxxxxxxxxx');
            document.write('xxxxxxxxxxxxxxxxxxxxxxxxxx');
            document.write('xxxxxxxxxxxxxxxxxxxxxxxxxx');
            </script>--><!--local的路徑-->
            <div class="forC2415Use"></div>
			<script type="text/javascript">
 				loadScript('pagejs/crs/LMS2415S02Panel');
			</script>
				<input type ="hidden" id="detailMainId" name="detailMainId"/>
				<input type ="hidden" id="detailCustId" name="detailCustId"/>
				<input type ="hidden" id="detailDupNo" name="detailDupNo"/>
				<input type ="hidden" id="detailOid" name="detailOid"/>
				<input type ="hidden" id="detailTxCode" name="detailTxCode"/>
            <!-- <div class="funcContainer">-->
            <fieldset>
                <legend>
                    <b>
                        <th:block th:text="#{'C241M01b.legend1'}">
                            <!--覆審訊息 -->
                        </th:block>
                    </b>
                </legend>
				<form id="C241M01aForm_s02" name="C241M01aForm_s02">
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td width="25%" class="hd1">
                                <th:block th:text="#{'C241M01b.retrialDate'}">
                                    <!-- 實際覆審日期 -->
                                </th:block>
                            </td>
                            <td width="25%">
                            	<input type="text" id="retrialDate" name="retrialDate" class="date"	/>
                            </td>
                            <td width="25%" class="hd1">
                            </td>
                            <td width="25%">
                            </td>
							
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.lastRetrialDate'}">
                                    <!-- 上次覆審日期 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_lastRetrialDate" class="reset" ></span>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.shouldReviewDate'}">
                                    <!-- 最遲應覆審日期 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_shouldReviewDate" class="reset" ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.retrialKind'}">
                                    <!-- 覆審類別 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_retrialKind" class="reset" ></span>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.specifyCycle'}">
                                    <!-- 99類覆審週期 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_specifyCycle" class="reset" ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.nCkdFlag'}">
                                    <!-- 不須覆審原因 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_nCkdFlag" class="reset" ></span>
                            </td>
                            <td class="hd1">
                                <th:block th:text="#{'C241M01b.nCkdMemo'}">
                                    <!-- 不覆審備註 -->
                                </th:block>
                            </td>
                            <td>
                                <span id="show_nCkdMemo" class="reset" ></span>
                            </td>
                        </tr>
						<tr>
	                    	<td class="hd1">
	                            <th:block th:text="#{'label.docFmt'}">覆審報告表格式</th:block>&nbsp;&nbsp;
	                        </td>
	                        <td colspan='3'>
				           		<label><input type="radio" value="A" name="docFmt" id="docFmt" checked="checked"><th:block th:text="#{'label.docFmt.A'}">一般覆審報告表</th:block></label>
				           		<span style='margin-left:50px;'/>
								<label><input type="radio" value="B" name="docFmt" id="docFmt" ><th:block th:text="#{'label.docFmt.B'}">（含土建融實地覆審）覆審報告表</th:block></label>
	                        </td>
	                    </tr>
                    </tbody>
                </table>
				</form>
            </fieldset>
            <fieldset>
                <legend>
                    <b>
                        <th:block th:text="#{'C241M01b.legend2'}">
                            <!--授信帳務資料 -->
                        </th:block>
                    </b>
                </legend>
                <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="hd3" colspan="4">
                                    <!--新增  -->
                                    <button id="_lms2415s02ADD" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'button.add'}">
                                                新增
                                            </th:block>
                                        </span>
                                    </button>
                                    <!--刪除  -->
                                    <button id="_lms2415s02Delete" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'button.delete'}">
                                                刪除
                                            </th:block>
                                        </span>
                                    </button>
                                <button type="button" id="_lms2415s02ButtonImport" name="_lms2415s02ButtonImport" onclick="add()">
                                    <span class="text-only">
                                        <th:block th:text="#{'C241M01b.bun1'}">
                                            <!--重新引進帳務資料 -->
                                        </th:block>
                                    </span>
                                </button>
                                    <button id="_lms2415s02ButtonDel" type="button">
                                        <span class="text-only">
                                            <th:block th:text="#{'C241M01b.bun2'}">
                                                刪除所有授信資料
                                            </th:block>
                                        </span>
                                    </button>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" width="25%">
                                <th:block th:text="#{'C241M01b.quotaAmtSum'}">
                                    <!--額度合計 -->
                                </th:block>
                            </td>
                            <td align="right" width="25%">
                                <span id="show_totQuota" class="reset"></span>
                            </td>
                            <td class="hd1" width="25%">
                                <th:block th:text="#{'C241M01b.balCurrSum'}">
                                    <!--  前日結欠餘額合計-->
                                </th:block>
                            </td>
                            <td align="right" width="25%">
                                <span id="show_totBal" class="reset"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="hd1" colspan="4" width="60%">
                                <!-- 一般授信資料Grid顯示 --><div id="C2415M01bGrid" ></div><!-- 一般授信資料彈跳視窗容 -->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </fieldset>
            <div id="lms2415s02Bom" style="display: none">
            	<div th:insert="~{crs/panels/LMS2415S02Panel01 :: LMS2415S02Panel01}"></div>
            </div>
        </th:block>
    </body>
</html>