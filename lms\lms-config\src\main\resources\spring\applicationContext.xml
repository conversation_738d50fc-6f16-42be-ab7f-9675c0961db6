<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    	   http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

	<!-- <bean id="wicketApplication" class="tw.com.iisi.cap.web.CapWebApplication">
		<property name="resourceFolders">
			<list>
				<value>html</value>
				<value>i18n</value>
			</list>
		</property>
		<property name="mountPackages">
			<list>
				<value>com.mega.eloan.**.pages</value>
				<value>com.mega.eloan.**.reports</value>
			</list>
		</property>
		<property name="mountPages">
			<util:map>
				<entry key="/i18n/js" value="tw.com.iisi.cap.i18n.JsI18nAjaxHandler" />
				<entry key="/error/message" value="com.mega.eloan.common.pages.EloanErrorPage" />
			</util:map>
		</property>
		<property name="defautlLocale" value="TRADITIONAL_CHINESE" />
		<property name="homePage" value="com.mega.eloan.common.pages.EloanHome" />
		<property name="validateFailPage" value="com.mega.eloan.common.pages.EloanErrorPage" />
	</bean> -->

    <context:component-scan base-package="com.iisigroup.cap.component.impl.**" />
    
    <bean class="com.iisigroup.cap.utils.CapAppContext" />
    
    <bean id="messageSource" class="tw.com.iisi.cap.context.SeparateReloadableResourceBundleMessageSource"
		p:basePath="classpath:i18n" p:languages="_zh_CN,_en,_zh_TW" p:useCodeAsDefaultMessage="true" />
    
    <mvc:interceptors>
    	<mvc:interceptor>
    		<mvc:mapping path="/**" />
    		<bean id="localeChangeInterceptor" class="com.iisigroup.cap.mvc.i18n.LocaleChangeInterceptor">
    			<property name="paramName" value="lang" />
    		</bean>
    	</mvc:interceptor>
    </mvc:interceptors>
    
    <bean id="localeResolver" class="com.iisigroup.cap.mvc.i18n.SessionLocaleResolver">
    	<property name="defaultLocale" value="zh_TW" />
    </bean>
    
    <bean id="applicationParameter" class="tw.com.iisi.cap.context.ApplicationParameter">
    	<property name="defautlLocale" value="TRADITIONAL_CHINESE" />
    	<property name="homePage" value="/home/<USER>" />
    	<property name="errorPage" value="/error/message" />
    	<property name="errorPageView" value="common/pages/EloanErrorPage" />
    </bean>
    
	<bean id="placeholderConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="false" />
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE" />
		<property name="ignoreResourceNotFound" value="false" />
		<property name="locations">
			<list>
				<value>classpath:db/database.properties</value>
				<!-- <value>classpath:db/testdatabase.properties</value> -->
				<value>classpath:db/jpa.properties</value>
				<value>classpath:config.properties</value>
				<value>classpath:megasso.properties</value>
			</list>
		</property>
	</bean>

    <bean id="debConfig" class="com.mega.eloan.lms.base.common.DebConfig">
		<constructor-arg index="0">
			<bean class="org.springframework.beans.factory.config.PropertiesFactoryBean">
				<property name="location">
					<value>classpath:config.properties</value>
				</property>
				<property name="properties">
					<props>
						<prop key="downloadFilePath">${docFile.dir}/${systemId}/DWNLOAD</prop>
						<prop key="uploadFilePath">${docFile.dir}/${systemId}</prop>
						<prop key="tempFilePath">${docFile.dir}/${systemId}/TEMP</prop>
					</props>
				</property>
			</bean>
		</constructor-arg>
	</bean>
	

	<bean id="springContext" class="com.mega.eloan.common.utils.SpringContextHelper" />

	<import resource="classpath:db/datasource.xml" />
	<import resource="classpath:db/jpa-com.xml" />
	<import resource="classpath:db/jpa-lms.xml" />
	<import resource="classpath:db/jpa-lms4com.xml" />
	<import resource="classpath:db/jdbc-dwdb.xml" />
    <import resource="classpath:db/jdbc-odsdb.xml" />
	<import resource="classpath:db/jdbc-icbcrdb.xml" />
	
	<import resource="classpath:db/jdbc-etchdb.xml" />
	<import resource="classpath:db/jdbc-ejcicdb.xml" />
	<import resource="classpath:db/jdbc-obsdb.xml" />
	<import resource="classpath:db/jdbc-tejdb.xml" />
	<import resource="classpath:db/jdbc-megaimagedb.xml" />

	<import resource="classpath:common/mega-appContext.xml" />
	<import resource="classpath:spring/dao-lms.xml" />
	<import resource="classpath:spring/services.xml" />
	<import resource="classpath:spring/security.xml" />
	<import resource="classpath:spring/operation.xml" />

	<import resource="classpath:spring/flow.xml" />
	<import resource="classpath:spring/userDefineSQL.xml" />
	<import resource="classpath:spring/gateway.xml" />
	<import resource="classpath:spring/auditlog.xml" />

	<import resource="classpath:spring/schedule.xml" />
	<import resource="classpath:spring/batch.xml" />
	<import resource="classpath:spring/remote-service.xml" />
	<import resource="classpath:common/aml.xml" />
	<import resource="classpath:common/oracle.xml" /> 
	<import resource="classpath:common/emap.xml" /> 
	
</beans>