/* 
 * C101S01IDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import com.mega.eloan.lms.model.C101S02C;

import tw.com.iisi.cap.dao.IGenericDao;


/** 系統初審 **/
public interface C101S02CDao extends IGenericDao<C101S02C> {

	C101S02C findByOid(String oid);

	List<C101S02C> findByMainId(String mainId);

	C101S02C findByUniqueKey(String mainId, String custId, String dupNo);

	List<C101S02C> findByIndex01(String mainId, String custId, String dupNo);

	List<C101S02C> findByCustIdDupId(String custId, String DupNo);

	C101S02C findC101S02C_idDup_latestOne(String mainId);

	C101S02C findC101S02C_idDup_latestOne(String custId, String dupNo);

	int deleteByOid(String oid);
}