/* 
 * AbstractMEGAImageJdbc.java
 */
package com.mega.eloan.lms.megaimage.service.impl;

import javax.annotation.Resource;
import javax.sql.DataSource;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.jdbc.EloanJdbcTemplate;

/**
 * <pre>
 * 文件數位化系統
 * </pre>
 * 
 * @since 2022/12/07
 * <AUTHOR>
 * @version <ul>
 *          <li>2022/12/07,009763,new
 *          </ul>
 */
@Deprecated
public class AbstractMEGAImageJdbc {

	EloanJdbcTemplate jdbc;

	@Resource(name = "megaimageSqlStatement")
	CapParameter sqlp;

	@Resource(name = "megaimage-db")
	public void setDataSource(DataSource dataSource) {
		jdbc = new EloanJdbcTemplate(dataSource, GWException.GWTYPE_TEJDB);
		jdbc.setSqlProvider(sqlp);
		jdbc.setCauseClass(this.getClass());
	}

	/**
	 * getJdbc
	 * 
	 * @return EloanJdbcTemplate
	 */
	public EloanJdbcTemplate getJdbc() {
		return jdbc;
	}

	/**
	 * 取得Sql
	 * 
	 * @param sqlId
	 *            sqlId
	 * @return sqlString
	 */
	public String getSqlBySqlId(String sqlId) {
		return sqlp.getValue(sqlId);
	}

}
