page.title=LMS1805M01 Credit Review
doc.tit01=Document Information
doc.tit02=Case Information
doc.tit03=File Attachment
sap110.custId=Customer's unified business number
sap110.custName=Customer Name
lms160.title=Credit Review Listing
L180M01B.projectSeq=Serial Number
eLF412_LRDATE=Last Review Date
eLF412_CUSTID=<PERSON>rrower's UBN
cNAME=<PERSON>rrower's Name
eLF412_MAINCUST=Main Customer
eLF412_MDFLAG=Account With Issues
eLF412_DBUOBU=Jointly Controlled Limit
eLF412_RCKDLINE=Cycle
eLF412_NEWADD=New/Limit Increase
eLF412_NCKDFLAG=Non-review Code
L180M01B.newBy170M01=Add Remarks To Credit Review Worksheet
docfile.filename=File Name
docfile.uploadTime=Upload Time
#javascript----------------------#
noRetrial=Please select cases that do not require review
retrial=Please select cases that require review
noReason=Reason for no selection
noNextDate=You have not specified the date to resume review
#-----java-----------------------#
listAlreadyHave=No data found in the Credit Review Control File; pls maintain first.
listAlreadyHave2=List data exists.
addFile=Please add relevant files to the Credit Review Control File
unWriteDate=You have not specified a scheduled credit review date
systemProduce=System Generated
peopleProduce=Manually Generated
createByAuto=Generate Monthly
createByPeople=Specify year and month of inquiry
needRetrial=Credit Review Required
noNeedRetrial=Non-review
typCd=Overseas
save=Save
printReport=Print the branch's latest credit limit approval sheet for outside-authority applications
close=Close
produceDetails=Credit Review Listing
newDetails=Add Credit Review Details
reason=Reason For Non-review
updateOK=Update Completed
updateFail=Update Not Completed
produceFail=Failed to generate list
cancelFail=The next review resume date exceeds the next review due date
chooseCTLreport=Please select a credit review list which has already been sent to branch
unusualFail=Failed to update abnormal credit reporting
L180M01A.dataDate=Year/Month Of Data
L180M01A.generateDate=List Generation Date
L180M01A.defaultCTLDate=Scheduled Credit Review Date
L1805G.error1=Input exceeded designated length
listSearch=Handling Officer's Query List File
listWait=List Of Cases Pending Supervisor's Approval
listCTL=Corporate Banking Credit Review
listExcel=Corporate Banking Credit Review Listing
listChk=Corporate Banking Credit Review Validation Listing
listChk2=Verify list
listPre=Scheduled Review List For Corporate Accounts
randomCode=Random Report Code
listChkError1=Credit Review Control File Maintenance
listChkError2=There are unapproved documents in the list; please approve before continuing
newaddC=Increase
newaddN=New
newaddR=Overdue Loan Normalized
mdFlag=Abnormal Credit Reporting
ctlDate=Credit Review Date
assignYM=Specify Year & Month
nckdflagInfo=Non-review Code Description:
searchDate=Inquiry date
branchId=Branch ID
branchName=Branch
preCount=Estimated No. Of Credit Reviews
noCTLcount=No. Of Non-reviews
alreadyCTL=No. Of Credit Reviews
alreadyCTLTotal=The number of
caseType1=Credit Review
caseType2=Credit
err.lockDoc=The document is currently locked, unlocked before to generate excel file
err.hisDate=The next restore a review date should not be less than today
#------\u5217\u5370\u7528-------
print.rptNo=Report Serial Number
print.rptName=Report Name
print.cntrNo=Credit Limit Serial Number
L120M01A.count=No. Of Records
L120M01A.caseDate=Signature Date
L120M01A.approveTime=Approval date
createExcel.title00=Credit Review List
createExcel.title01=Credit Review Serial Number
createExcel.title02=UBN
createExcel.title03=Credit Grade
createExcel.title04=Main Customer
createExcel.title05=Customer Name
createExcel.title06=upload the Credit Review Control File or renew last Credit Review
createExcel.title07=Review Cycles
createExcel.title08=Credit Review Team Member
createExcel.title09=Note
createExcel.title10=Non-review Notes
createExcel.title11=Verify the list generated staff
createExcel.title12=1. Bank or with the owners do the syndicated loan, non-held management line. 2 Kazunari deposit. Pure out of the custody account. On government or government agencies, schools, credit cases.
createExcel.title13=5Placements with banks and other financial institutions or facility of the same industry. 6 has been reported as overdue loans or transferred the cases of delinquent loans. 7 cancel the account. 8 temporarily Review.

createExcel.title14=Last Review Date
createExcel.title15=Old Ratings

createExcel2.title01=Scheduled Review List For Corporate Accounts
createExcel2.title03=Branch Code
createExcel2.title04=Branch
createExcel2.title05=Scheduled Review List 
createExcel2.title06=No Review counts

l180m01a.docStatus010=Compiling
l180m01a.docStatus020=Pending Approve Appraisal
l180m01a.docStatus030=Approved Valuation
l180m01a.docStatus0A0=Review the list has been generated report files

