/* 
 * L141M01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L141M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L141M01D;

/** 聯行額度明細表簽章欄檔 **/
@Repository
public class L141M01DDaoImpl extends LMSJpaDao<L141M01D, String> implements
		L141M01DDao {

	@Override
	public L141M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L141M01D> list = createQuery(L141M01D.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L141M01D findByUniqueKey(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchId", branchId);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffNo", staffNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L141M01D> findByIndex01(String mainId, String branchType,
			String branchId, String staffNo, String staffJob) {
		ISearch search = createSearchTemplete();
		List<L141M01D> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (branchType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
					branchType);
		if (branchId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "branchId",
					branchId);
		if (staffNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffNo",
					staffNo);
		if (staffJob != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "staffJob",
					staffJob);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L141M01D.class, search).getResultList();
		}
		return list;
	}

	@Override
	public List<L141M01D> findByMainIdAndBranchType(String mainId,
			String branchType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		List<L141M01D> list = createQuery(L141M01D.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L141M01D findByUniqueKey(String mainId, String branchType,
			String staffJob) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "branchType",
				branchType);
		search.addSearchModeParameters(SearchMode.EQUALS, "staffJob", staffJob);
		return findUniqueOrNone(search);
	}
}