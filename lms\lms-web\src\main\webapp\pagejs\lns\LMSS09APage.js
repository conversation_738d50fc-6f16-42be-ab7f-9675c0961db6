var initS09aJson = {
	handlerName : null,
	// 設定handler名稱
	setHandler : function(){
		if(responseJSON.docURL == "/lms/lms1201m01"){
			// 授權外企金
			this.handlerName = "lms1201formhandler";
		}else if(responseJSON.docURL == "/lms/lms1101m01"){
			// 授權內企金
			this.handlerName = "lms1101formhandler";
		}else if(responseJSON.docURL == "/lms/lms1211m01"){
			// 授權外個金
			this.handlerName = "lms1211formhandler";
		}else if(responseJSON.docURL == "/lms/lms1111m01"){
			this.handlerName = "lms1111formhandler";
		}else{
			this.handlerName = "lms1301formhandler";
		}		
	}
};

$(document).ready(function() {
	initS09aJson.setHandler();
	$.form.init({
        formHandler: initS09aJson.handlerName,
        formPostData:{
        	formAction : "queryLms1205m01",
        	oid : responseJSON.oid,
        	page : responseJSON.page,
        	mainId : responseJSON.mainId,
        	docType : responseJSON.docType,
        	docCode : responseJSON.docCode,
        	docKind : responseJSON.docKind,
			docStatus : responseJSON.mainDocStatus,
			areaDocstatus : responseJSON.areaDocstatus,
			txCode : responseJSON.txCode,
			itemDscr03 : "",
			itemDscr05 : "",
			ffbody : "",
			brnGroup : responseJSON.brnGroup			
        },
		loadSuccess:function(jsonInit){				
			setCkeditor2("itemDscr05",jsonInit.L120M01DForm05.itemDscr05);
/*
			if(responseJSON.page != "01"){
				var $showBorrowData = $("#showBorrowData");
				$showBorrowData.reset();
				$showBorrowData.setData(jsonInit.showBorrowData,false);	
			}
*/
			for(o in jsonInit.hideBook){
				$(jsonInit.hideBook[o]).hide();
			}
			$(".tabs-warp").scrollToTab();
            // 所屬營運中心設定
			var $showBorrowData = $("#showBorrowData");
            responseJSON["brnGroup"] = jsonInit.brnGroup;
            if (jsonInit.areaTitle) {
                $showBorrowData.find("#title1301").html(jsonInit.areaTitle);
                if (lmsM01Json.docType == "1") {
                    $showBorrowData.find("#title0a").show();
                    $showBorrowData.find("#title0b").hide();
                } else if (lmsM01Json.docType == "2") {
                    $showBorrowData.find("#title0a").hide();
                    $showBorrowData.find("#title0b").show();
                }					
                if (lmsM01Json.docCode == "3") {
                    $showBorrowData.find("#title1y").show();
                } else if (lmsM01Json.docCode == "4") {
                    $showBorrowData.find("#title1x").show();
                }
            } else {
                if (lmsM01Json.docType == "1") {
                    $showBorrowData.find("#title0a").show();
                    $showBorrowData.find("#title0b").hide();
                } else if (lmsM01Json.docType == "2") {
                    $showBorrowData.find("#title0a").hide();
                    $showBorrowData.find("#title0b").show();
                }
                if (lmsM01Json.docKind == "2") {
                    if (lmsM01Json.docCode == "1") {
                        $showBorrowData.find("#title1").show();
                        $showBorrowData.find("#title1a").hide();
                        $showBorrowData.find("#title1b").hide();
                    } else if (lmsM01Json.docCode == "2") {
                        $showBorrowData.find("#title1a").show();
                        $showBorrowData.find("#title1").hide();
                        $showBorrowData.find("#title1b").hide();
                    } else if (lmsM01Json.docCode == "3") {
                        $showBorrowData.find("#title1b").show();
                        $showBorrowData.find("#title1").hide();
                        $showBorrowData.find("#title1a").hide();
                        $showBorrowData.find("#title1y").show();
                    } else if (lmsM01Json.docCode == "4") {
                        // other.msg114=授權外案件簽報書(異常通報案件)
                        $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg114"]);
                        $showBorrowData.find("#title1x").show();
                    }
                } else if (lmsM01Json.docKind == "1") {
                    $showBorrowData.find("#title1").hide();
                    $showBorrowData.find("#title1c").hide();
                    $showBorrowData.find("#title1d").hide();
                    $showBorrowData.find("#title1e").hide();
                    $showBorrowData.find("#title1f").hide();
                    $showBorrowData.find("#title1g").hide();
                    $showBorrowData.find("#title1h").hide();
                    $showBorrowData.find("#title1i").hide();
                    $showBorrowData.find("#title1j").hide();
                    $showBorrowData.find("#title1k").hide();						
                    if (responseJSON.authLvl == "2") {
                        // 總行授權內
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1f").show();
                            $showBorrowData.find("#title1g").hide();
                            $showBorrowData.find("#title1h").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1g").show();
                            $showBorrowData.find("#title1f").hide();
                            $showBorrowData.find("#title1h").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1h").show();
                            $showBorrowData.find("#title1f").hide();
                            $showBorrowData.find("#title1g").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg115=總行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg115"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else if (responseJSON.authLvl == "1") {
                        // 分行授權內
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1c").show();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1d").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1e").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg116=分行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else if (responseJSON.authLvl == "3") {
                        // 營運中心授權內
                        if (responseJSON.docCode == "1") {
                            $showBorrowData.find("#title1i").show();
                            $showBorrowData.find("#title1j").hide();
                            $showBorrowData.find("#title1k").hide();
                        } else if (responseJSON.docCode == "2") {
                            $showBorrowData.find("#title1j").show();
                            $showBorrowData.find("#title1i").hide();
                            $showBorrowData.find("#title1k").hide();
                        } else if (responseJSON.docCode == "3") {
                            $showBorrowData.find("#title1k").show();
                            $showBorrowData.find("#title1j").hide();
                            $showBorrowData.find("#title1i").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg117=營運中心授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg117"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    } else {
                        // 預設顯示
                        if (lmsM01Json.docCode == "1") {
                            $showBorrowData.find("#title1c").show();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "2") {
                            $showBorrowData.find("#title1d").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1e").hide();
                        } else if (lmsM01Json.docCode == "3") {
                            $showBorrowData.find("#title1e").show();
                            $showBorrowData.find("#title1c").hide();
                            $showBorrowData.find("#title1d").hide();
                            $showBorrowData.find("#title1y").show();
                        } else if (lmsM01Json.docCode == "4") {
                            // other.msg116=分行授權內案件簽報書(異常通報案件)
                            $showBorrowData.find("#title1301").html(i18n.lmscommom["other.msg116"]);
                            $showBorrowData.find("#title1x").show();
                        }
                    }
                }
            }								
		}
    });
});
