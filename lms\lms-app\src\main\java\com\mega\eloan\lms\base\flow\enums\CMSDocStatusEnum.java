/* 
 * CMSDocStatusEnum.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow.enums;

import java.util.Properties;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;

import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.pages.AbstractEloanPage;

/**
 * 
 * </pre> 擔保品文件狀態
 * 
 * @since 2012/11/12
 * <AUTHOR> @version <ul>
 *          <li>2012/11/12,Rex,new
 *          </ul>
 */
public enum CMSDocStatusEnum {

	分行_編製中("11B"), 分行_待覆核("12B"), 分行_已覆核("13B"), 分行_待設質("14B"), 分行_已設質("15B"), 分行_待塗銷(
			"16B"), 分行_已塗銷("17B"),

	聯行傳回("18B"), 聯行傳回不採用("19B"),

	代鑑價編製中("1AB"), 代鑑價待覆核("1BB"), 代鑑價已完成("1CB"),

	營運中心_編製中("1AC"), 營運中心_待覆核("1BC"), 營運中心_已覆核("1CC"), 營運中心_已傳回("1DC"), 營運中心_傳回不採用待覆核(
			"1FC"), 營運中心_傳回不採用已覆核("1GC"),營運中心_待收件("1HC"),營運中心_覆核待收件("1IC"),營運中心_覆核編制中("1JC"),
			營運中心_覆核待覆核("1KC"),營運中心_覆核已覆核("1LC"),營運中心_覆核已傳回("1MC"),

	緩辦(""),

	// 股票
	待斷頭("17S"), 已斷頭("19S"), 補提("1AS"), 擔保率不足("1BS"),

	// OBS補登
	OBS待補登塗銷("OBS1"), OBS待覆核("OBS2"), OBS已覆核("OBS3"),

	// 擔保維持率工作單申請作業
	授管處_編製中("91B"), 授管處_待覆核("92B"), 授管處_已覆核("93B");

	private String code;
	private static Properties pop = MessageBundleScriptCreator
			.getComponentResource(AbstractEloanPage.class);

	CMSDocStatusEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return pop.getProperty("status." + this.code,
				EloanConstants.EMPTY_STRING);
	}

	public boolean isEquals(Object other) {
		if (other instanceof String) {
			return code.equals(other);
		} else {
			return super.equals(other);
		}
	}

	public static String getMessage(CMSDocStatusEnum status) {
		return getMessage(status.toString());
	}

	public static String getMessage(String status) {
		return pop.getProperty("status." + status, EloanConstants.EMPTY_STRING);
	}

	public static CMSDocStatusEnum getEnum(String code) {
		for (CMSDocStatusEnum enums : CMSDocStatusEnum.values()) {
			if (enums.isEquals(code)) {
				return enums;
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return code;
	}

}
