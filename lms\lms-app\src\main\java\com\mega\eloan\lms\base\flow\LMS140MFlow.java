/* 
 *  LMS140MFlow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.base.flow;

import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.provider.AnnotationHandler.Transition;
import com.mega.eloan.common.dao.CommonMetaDao;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.L140MM1A;


/**
 * <pre>
 * 央行註記異動作業流程
 * </pre>
 * 
 * @since 2014/08/28
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@SuppressWarnings("unused")
@Component
public class LMS140MFlow extends AbstractFlowHandler {

	@Resource
	CommonMetaDao metaDao;
	

	@Transition(node = "待覆核")
	public void test3(FlowInstance instance) {
	
	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L140MM1A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}