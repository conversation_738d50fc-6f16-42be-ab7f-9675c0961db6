<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<fieldset>
			<legend><b><th:block th:text="#{'title.tab01'}">文件資訊</th:block></b></legend>
			<table class="tb2" width="100%">
				<tr>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.ownBrId'}">編製單位代號</th:block>&nbsp;&nbsp;</td>
					<td width="35%">
						<span id="ownBrId" class="field" ></span>&nbsp;<span id="ownBrIdName" class="field" ></span>
					</td>		
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.docStatus'}">目前文件狀態</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="docStatus" class="field" ></span>&nbsp;
					</td>					
				</tr>
				
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160M03A.custId'}">統一編號</th:block>&nbsp;&nbsp;</td>
					<td ><span id="custId" class="field" ></span>-<span id="dupNo" class="field" ></span></td>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.custName'}">客戶名稱</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="custName" class="field"></span>&nbsp;</td>							
				</tr>
				<tr>
					<td class="hd2" align="right"><th:block th:text="#{'C160M03A.cntrNo'}">額度序號</th:block>&nbsp;&nbsp;</td>
					<td ><span id="cntrNo" class="field" ></span>&nbsp;</td>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.packNo'}">批號</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><input type="text" id="packNo" name="packNo" readonly maxlength="4" size="4" class="digits required" />&nbsp;
					<button type="button" id="btPackNo">
						<span class="text-only"><th:block th:text="#{'button.btPackNo'}">給號</th:block></span>
					</button>
						
					</td>							
				</tr>
				<tr>
					<td class="hd2" align="right">&nbsp;&nbsp;</td>
					<td colspan="3" >
						<button type="button" id="btExcel" class="forview">
							<span class="text-only"><th:block th:text="#{'button.btExcel'}">匯入EXCEL</th:block></span>
						</button>
					</td>
				</tr>
			</table>
		</fieldset>
		<fieldset>
			<legend><b><th:block th:text="#{'title.docLog'}">文件異動記錄</th:block></b></legend>
			<div th:include="common/panels/DocLogPanel :: DocLogPanel"></div>
			<table class="tb2" width="100%">
				<tr>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.creator'}">建立人員號碼</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="creator" class="field" ></span>(<span id="createTime" class="field" ></span>)</td>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160M03A.updater'}">異動人員號碼</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="updater" class="field" ></span>(<span id="updateTime" class="field" ></span>)</td>
				</tr>
				<tr>
					<td class="hd2" align="right">&nbsp;</td>
					<td>&nbsp;</td>
					<td class="hd2" align="right"><th:block th:text="#{'C160M03A.randomCode'}">文件亂碼</th:block>&nbsp;&nbsp;</td>
					<td><span id="randomCode" class="field" ></span>&nbsp;</td>
				</tr>
			</table>
		</fieldset>

		
		<script type="text/javascript">loadScript('pagejs/cls/CLS1161S31Panel');</script>
	</th:block>
</body>
</html>
