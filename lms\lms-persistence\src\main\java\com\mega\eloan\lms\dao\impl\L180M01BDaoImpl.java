/* 
 * L180M01BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.dao.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.L180M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180M01B;

/** 覆審名單明細檔 **/
@Repository
public class L180M01BDaoImpl extends LMSJpaDao<L180M01B, String> implements
		L180M01BDao {

	@Override
	public L180M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01B> findByMainId(String mainId, String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}

		Map<String, Boolean> map = new LinkedHashMap<String, Boolean>();
		map.put("docStatus1", false);
		map.put("custId", false);
		map.put("dupNo", false);
		map.put("newLRDate", false);
		map.put("elfLRDate", false);
		search.setOrderBy(map);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180M01B> list = createQuery(L180M01B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public List<L180M01B> findByCustIdDupId(String custId, String DupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		List<L180M01B> list = createQuery(L180M01B.class, search)
				.getResultList();
		return list;
	}

	@Override
	public L180M01B findByUniqueKey(String mainId, String custId, String dupNo,
			String ctlType) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180M01B> findByIndex01(String mainId, String custId,
			String dupNo, String ctlType) {
		ISearch search = createSearchTemplete();
		List<L180M01B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (custId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		if (dupNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		if (Util.notEquals(Util.trim(ctlType), "")) {
			search.addSearchModeParameters(SearchMode.EQUALS, "ctlType",
					ctlType);
		}
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L180M01B.class, search).getResultList();
		}
		return list;
	}

	@Override
	public void deleteL180M01BList(String mainId, String custId, String dupNo,
			String ctlType) {
		Query query = getEntityManager().createNamedQuery(
				"L180M01B.deleteByMainId");
		query.setParameter("mainId", mainId); // 設置參數
		query.setParameter("custId", custId); // 設置參數
		query.setParameter("dupNo", dupNo); // 設置參數
		query.setParameter("ctlType", ctlType); // 設置參數
		query.executeUpdate();

	}

	@Override
	public L180M01B findByProjectNo(String projectNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "projectNo",
				projectNo);
		return findUniqueOrNone(search);
	}
}