<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<form id="C160S01BForm" name="C160S01BForm">
			<span class="color-red">※：<th:block th:text="#{'C160S01B.warn'}">只有主債務人統編與從債務人統編相同者，其關係可為空白，其餘欄位皆不可空白</th:block></span>
			<table class="tb2" width="100%">
				<tr>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160S01B.custId'}">主債務人統編</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="custId" class="field" ></span>&nbsp;<span id="dupNo" class="field" ></span></td>
					<td width="15%" class="hd2" align="right"><th:block th:text="#{'C160S01B.cntrNo'}">額度序號</th:block>&nbsp;&nbsp;</td>
					<td width="35%"><span id="cntrNo" class="field" ></span>&nbsp;</td>
				</tr>
				<tr>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.rId'}">從債務人統編</th:block>&nbsp;&nbsp;</td>
					<td >
						<input type="text" id="rId" name="rId" class="required max" maxlength="10" size="10"/>&nbsp;
						<input type="text" id="rDupNo" name="rDupNo" class="required max" maxlength="1" size="1"/>&nbsp;
						<button type="button" id="rIdPullin" >
							<span class="text-only"><th:block th:text="#{'button.pullin'}">引進</th:block></span>
						</button>
					</td>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.rName'}">從債務人名稱</th:block>&nbsp;&nbsp;</td>
					<td ><input type="text" id="rName" name="rName" class="max required" maxlength="120" size="20"/></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.rKindM'}">關係類別</th:block>&nbsp;&nbsp;</td>
					<td >
						<select id="rKindM" name="rKindM" class="required" codeType="cls_RelClass" itemStyle="format:{value}-{key}" ></select><br/>
						<select id="rKindD1" name="rKindD1" class="rKindD required" codeType="Relation_type1" itemStyle="format:{value}-{key}" ></select>
						<select id="rKindD2" name="rKindD2" class="rKindD required" codeType="Relation_type2" itemStyle="format:{value}-{key}" ></select>
						<select id="rKindD31" name="rKindD31" class="rKindD required" codeType="Relation_type31" itemStyle="format:{value}-{key}" ></select>
						<select id="rKindD32" name="rKindD32" class="rKindD required" codeType="Relation_type32" itemStyle="format:{value}-{key}" ></select>
						<input type="hidden" id="rKindD" name="rKindD" />
					</td>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.rCountry'}">國別</th:block>&nbsp;&nbsp;</td>
					<td ><span class="field" ></span><select id="rCountry" name="rCountry" class="required" codeType="CountryCode" itemStyle="format:{value}-{key}" ></select></td>
				</tr>
				<tr>
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.rType'}">相關身份</th:block>&nbsp;&nbsp;</td>
					<td ><select id="rType" name="rType" class="required" codeType="lms1605s03_rType" itemStyle="format:{value}-{key}"></select></td>
					<td class="hd2" align="right">
						<th:block th:text="#{'C160S01B.dueDate'}">董監事任期止日</th:block>&nbsp;&nbsp;<br/>
						(<th:block th:text="#{'C160S01B.dueDate1'}">保證人保證迄日</th:block>)
					</td>
					<td ><input type="text" id="dueDate" name="dueDate" class="date"  /></td>
				</tr>
				<tr id="lms1605s03_rType" >
					<td class="hd2" align="right"><span class="text-red">＊</span><th:block th:text="#{'C160S01B.reson'}">借保原因</th:block>&nbsp;&nbsp;</td>
					<td colspan="3" >
						<select id="reson" name="reson" class="required" codeType="cls1161m01_reson" ></select>
						<br/> 
						<input type="text" id="resonOther" name="resonOther" class="max required" maxlength="20" size="50" />
					</td>
				</tr>
				<tr class='area_guaPercent'>
					<td class="hd2" align="right"><th:block th:text="#{'C160S01B.guaPercentStr'}">保證人負担保證責任比率</th:block>&nbsp;</td>
					<td colspan="3" >
						<input type="text" id="guaPercent" name="guaPercent" class="numeric" integer="3" fraction="2" size="6" maxlength="6" vale="" />%
					</td>
				</tr>
				<tr></tr>
			</table>
		</form>
	</th:block>
</body>
</html>
