/* 
 * C801M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C801M01A;

/** 個金個人資料清冊作業主檔 **/
public interface C801M01ADao extends IGenericDao<C801M01A> {

	C801M01A findByOid(String oid);
	
	C801M01A findByMainId(String mainId);
		
	C801M01A findByCntrNo(String cntrNo);
	
	List<C801M01A> findByOwnBrId(String ownBrId);
	
	C801M01A findByCntrNoOwnBrId(String cntrNo,String ownBrId);

}