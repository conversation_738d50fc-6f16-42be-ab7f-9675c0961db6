$(document).ready(function() {
	ilog.debug("<EMAIL>"); 
	$("#cls1201s21_btn_import").click(function(){
		$.ajax({
			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
			data : {
				formAction : "cls1201s21_btn_import",
				mainId : responseJSON.mainid
			},
			success : function(json) {
				if(json.fetchForm){
					$("#CLS1201S21Form").injectData(json.fetchForm);
				}
			}
		});		
	});
	
	$("#cls1201s21_btn_del").click(function(){
		//action_003=是否確定「刪除」此筆資料?
		CommonAPI.confirmMessage(i18n.def["action_003"], function(b){
            if (b) {
            	$.ajax({
        			handler : 'cls1141m01formhandler',type : "POST", dataType : "json",
        			data : {
        				formAction : "cls1201s21_btn_del",
        				mainId : responseJSON.mainid
        			},
        			success : function(json) {
        				if(json.fetchForm){
        					$("#CLS1201S21Form").injectData(json.fetchForm);
        					$("#CLS1201S21Form").reset();
        				}
        			}
        		});		
            }
        });
	});
});

