<!-- 徵信報告引進共用元件 -->
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
    <body>
        <th:block th:fragment="panelFragmentBody">
    		    <!-- thickbox -->
              	<div id="cesSelect" style="display: none;">
                <form id="formCesSelect">
 				    <table border="0" cellspacing="0" cellpadding="0" style="margin-top: 5px;">
                        <tr>
                            <td>
                                <label><input type="radio" name="radioSel" value="1" checked="checked" onclick="$('#otherCust').hide()" />1.
                                <th:block th:text="#{'commonweb.radio1'}">
                                    	依借款人
                                </th:block></label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label><input type="radio" name="radioSel" value="2" onclick="$('#otherCust').show()" />2.
                                <th:block th:text="#{'commonweb.radio2'}">
                                   	 依特定統編
                                </th:block></label>
                                &nbsp; <input class="max hide upText" id="otherCust" type="text" size="10" maxlength="10" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label><input type="radio" name="radioSel" value="3" onclick="$('#otherCust').hide()" />3.
                                <th:block th:text="#{'commonweb.radio3'}">
                                   	 依顯示所有徵信報告
                                </th:block></label>
                            </td>
                        </tr>
                    </table>
				    <div id="cesGridThick" style="display: none;">
						<div class="cesGridSel" id="hCesGridSel1">
							<div id="gridSelCes1" width="100%"
								style="margin-left: 10px; margin-right: 10px">
							</div>
						</div>
						<div class="cesGridSel" id="hCesGridSel2">
							<div id="gridSelCes2" width="100%"
								style="margin-left: 10px; margin-right: 10px">
							</div>
						</div>
						<div class="cesGridSel" id="hCesGridSel3">
							<div id="gridSelCes3" width="100%"
								style="margin-left: 10px; margin-right: 10px">
							</div>
						</div>
				    </div>
                </form>
                </div>
        </th:block>
    </body>
</html>
