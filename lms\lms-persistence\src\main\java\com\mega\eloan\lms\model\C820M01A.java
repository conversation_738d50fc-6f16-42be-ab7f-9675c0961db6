/* 
 * C820M01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 收集批覆書控制表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C820M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C820M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Size(max=32)
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 
	 * 區域中心代碼<p/>
	 * codetype=cls9501v01_area
	 */
	@Size(max=3)
	@Column(name="GROUPID", length=3, columnDefinition="CHAR(3)")
	private String groupId;

	/** 
	 * 是否被選取<p/>
	 * Y|選取；N|未選取
	 */
	@Size(max=1)
	@Column(name="ISSELECTED", length=1, columnDefinition="CHAR(1)")
	private String isSelected;

	/** 分行代碼 **/
	@Size(max=3)
	@Column(name="BRNO", length=3, columnDefinition="CHAR(3)")
	private String brno;

	/** 分行名稱 **/
	@Size(max=63)
	@Column(name="BRNAME", length=63, columnDefinition="VARCHAR(63)")
	private String brName;

	/** 
	 * 欲產生之年月<p/>
	 * YYYY-MM
	 */
	@Size(max=7)
	@Column(name="DATAYM", length=7, columnDefinition="CHAR(7)")
	private String dataYM;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 異動人員號碼 **/
	@Size(max=6)
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Timestamp updateTime;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 
	 * 取得區域中心代碼<p/>
	 * codetype=cls9501v01_area
	 */
	public String getGroupId() {
		return this.groupId;
	}
	/**
	 *  設定區域中心代碼<p/>
	 *  codetype=cls9501v01_area
	 **/
	public void setGroupId(String value) {
		this.groupId = value;
	}

	/** 
	 * 取得是否被選取<p/>
	 * Y|選取；N|未選取
	 */
	public String getIsSelected() {
		return this.isSelected;
	}
	/**
	 *  設定是否被選取<p/>
	 *  Y|選取；N|未選取
	 **/
	public void setIsSelected(String value) {
		this.isSelected = value;
	}

	/** 取得分行代碼 **/
	public String getBrno() {
		return this.brno;
	}
	/** 設定分行代碼 **/
	public void setBrno(String value) {
		this.brno = value;
	}

	/** 取得分行名稱 **/
	public String getBrName() {
		return this.brName;
	}
	/** 設定分行名稱 **/
	public void setBrName(String value) {
		this.brName = value;
	}

	/** 
	 * 取得欲產生之年月<p/>
	 * YYYY-MM
	 */
	public String getDataYM() {
		return this.dataYM;
	}
	/**
	 *  設定欲產生之年月<p/>
	 *  YYYY-MM
	 **/
	public void setDataYM(String value) {
		this.dataYM = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Timestamp getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Timestamp value) {
		this.updateTime = value;
	}
}
