/* 
 * MisdbBASEService.java
 *
 * IBM Confidential
 * GBS Source Materials
 * 
 * Copyright (c) 2011 IBM Corp. 
 * All Rights Reserved.
 */
package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;

import com.mega.eloan.lms.mfaloan.bean.ELF431;
import com.mega.eloan.lms.mfaloan.bean.ELF447N;
import com.mega.eloan.lms.mfaloan.bean.ELF493;
import com.mega.eloan.lms.mfaloan.bean.ELF513;
import com.mega.eloan.lms.mfaloan.bean.ELF601;
import com.mega.eloan.lms.mfaloan.bean.ELF602;
import com.mega.eloan.lms.mfaloan.bean.LNF13E;
import com.mega.eloan.lms.mfaloan.bean.LNF916S;
import com.mega.eloan.lms.mfaloan.bean.LNF917S;
import com.mega.eloan.lms.mfaloan.bean.LNF919S;

/**
 * <pre>
 * MisdbBASEService
 * </pre>
 * 
 * @since 2011/9/29
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/29,UFO,new
 *          </ul>
 */
public interface MisdbBASEService {

	/**
	 * 查詢資料(只抓前xxx筆)
	 * 
	 * @param clazz
	 * @param msgFmtParam
	 * @param data
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Deprecated
	List<Map<String, Object>> find(Class clazz, Object[] msgFmtParam,
			Object[] data);

	@SuppressWarnings("rawtypes")
	List<Map<String, Object>> findWithMax(Class clazz, Object[] msgFmtParam,
			Object[] data);

	/**
	 * 取得MIS.CUSTDATA資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findSYNBANK(String brNo);

	/**
	 * 查詢之額度限額控管種類
	 * 
	 * @param kind
	 *            案件種類
	 * @param loanNo
	 *            放款帳號
	 * @return 筆數 TCOUNT
	 */
	public int countByLNF262_KINDAndLNF262_LOAN_NO(String kind, String loanNo);

	public List<Map<String, Object>> findBySQLForList(String sql,
			String... params);

	public List<Map<String, Object>> findCustDataByCltype(String[] custIds);

	public List<Map<String, Object>> findLNF022V12(String grpNo);

	public List<Map<String, Object>> findLNF022V13(String grpNo);

	public List<Map<String, Object>> findRate(String sCurr1, String sCurr2);

	/**
	 * 海外分行代號轉碼 ，當無此分行代號 回傳丟入代號
	 * 
	 * @param brId
	 *            分行代號
	 * @return CBCBRNO(前三碼)
	 */
	public String findICBCBRByBrId(String brId);

	/**
	 * 用來抓非兆豐銀行的資料 -MIS.SynBank銀行資料檔
	 * 
	 * @param branchCode
	 *            銀行代碼
	 * @return 銀行列表
	 */
	public List<Map<String, Object>> findMISSynBank(String branchCode);

	/**
	 * 用來抓已編碼國外銀行
	 * 
	 * @return 銀行列表
	 */
	public List<Map<String, Object>> findMISELFBKSNOBank();

	/**
	 * 搜尋行員資料
	 * 
	 * @param custId
	 * @return
	 */
	Map<String, Object> findMISStaffSelAll(String custId);

	/**
	 * 引進主從債務人
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param cntrno
	 * @return
	 */
	List<Map<String, Object>> findEllngteeSelLngenm(String branch,
			String custId, String dupNo, String cntrno);

	Map<String, Object> findElchklst(String branch, String custId, String dupNo);

	/**
	 * 查詢該ID + 重覆序號 的 簽報案件紀錄
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重複序號
	 * @return 案件紀錄
	 */
	List<Map<String, Object>> findEllnseek(String custId, String dupNo);

	/**
	 * 個金引進借款人
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findCUSTDATA_selCustData(String custId,
			String dupNo);

	/**
	 * 個金引進配偶資料
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findCUSTDATA_selMCustId(String custId,
			String dupNo);

	/**
	 * 個金引進配偶資料
	 * 
	 * @param custId
	 *            客戶編號
	 * @return
	 */
	List<Map<String, Object>> findCUSTDATA_selMCustId2(String custId);

	/**
	 * 個金引進申貸戶通訊地址
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findELCUS21_selAddrr(String custId, String dupNo);

	/**
	 * 個金引進申貸戶服務單位電話
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findELCUS28_selTelno(String custId, String dupNo);

	/**
	 * 個金引進申貸戶行動電話
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findELCUS23_selMpno(String custId, String dupNo);

	/**
	 * 個金引進申貸戶電子郵件地址
	 * 
	 * @param custId
	 *            客戶編號
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<Map<String, Object>> findELCUS22_selMailaddr(String custId,
			String dupNo);

	/**
	 * 透過JDBC取得借款人關係企業授信明細檔
	 * 
	 * @param allCustId
	 *            完整統編
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param custName
	 *            客戶名稱
	 * @return
	 */
	List<Map<String, Object>> findLNF022_selL120s05d1(String allCustId,
			String custId, String dupNo, String custName);

	/**
	 * 查詢客戶建檔細類
	 * 
	 * @param 四碼的科目key值
	 * @return 系項項目
	 */
	public List<Map<String, Object>> findLNF07AByKey2(String key);

	/**
	 * 查詢舊有額度序號LNF197
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 筆數
	 */
	public int findLNF197BycntrNoAndCustId(String cntrNo, String custId,
			String dupNo);

	/**
	 * 查詢舊有額度序號 MIS.MISLN20
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return 筆數
	 */
	public int findMISLN20BycntrNoAndCustId(String cntrNo, String custId,
			String dupNo);

	/**
	 * 讀取MOW信評
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return Mow信評資料
	 */
	List<Map<String, Object>> findMISMOWTBL1_selMow(String custId, String dupNo);

	/**
	 * 取得99.其他(由國外部徵信系統金融機構資料維護)
	 * 
	 * @return CODE 分行代號(3碼 ) NAME 名稱
	 */
	public List<Map<String, Object>> findMISSynBankBy99();

	/**
	 * 取得本行存款帳戶
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Deprecated
	List<Map<String, Object>> findMisEldpfByCustid(String brno, String custId,
			String dupNo);

	/**
	 * 取得本行存款帳戶
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	@Deprecated
	List<Map<String, Object>> findMisEldpfByCustid(String custId, String dupNo);

	/**
	 * 檢查本行存款帳戶
	 * 
	 * @param brno
	 * @param apcd
	 * @param seqno
	 * @return
	 */
	@Deprecated
	List<Map<String, Object>> findMisEldpfByAccount(String brno, String apcd,
			String seqno);

	/**
	 * 檢查本行存款帳戶
	 * 
	 * @param brno
	 * @param apcd
	 * @param seqno
	 * @return
	 */
	@Deprecated
	List<Map<String, Object>> findMisEldpfByAccount(String[] custIds,
			String brno, String apcd, String seqno);

	/**
	 * 查詢 申貸戶婉卻紀錄
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	Map<String, Object> searchRejectRecord(String custId, String dupNo);

	
	Map<String, Object> getElf603ByKey(String from602SUid,
			String from602SApptime, String cntrno, BigDecimal from602SSeqno);
	
	/**
	 * 取得一額度下，同一ELF602 永續績效分項中，追蹤事項通知日最大的一筆<br/>
	 * <br/>						
	 * 因為 (1)同一天每次撥款，只會寫一筆永續績效的ELF602  <br/>
	 *     (2)退回、辦理狀況為未完成、辦理狀況為辦理中，也都會是同一筆ELF602  <br/>
	 * 所以欲抓取同一ELF602永續績效分項中最新的一筆，會以追蹤事項通知日最大為主  <br/>  
	 * 	
	 * <br/>
	 * J-113-0035 為利ESG案件之貸後管控, <br/>
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制 <br/>
	 * 
	 * @param from602SUid
	 * @param from602SApptime
	 * @param cntrno
	 * @param from602SSeqno
	 * @return
	 */
	List<ELF602> getESGDataMaxFoDate(String cntrno);
	
	/**
	 * 查詢 申貸人、連保人、配偶放款檔
	 * 
	 * @param custId
	 * @return
	 */
	List<Map<String, Object>> findLnf150ByCustid(String custId);

	/**
	 * 查詢 有,無擔餘額
	 * 
	 * @param custId
	 * @return
	 */
	List<Map<String, Object>> findLnf022LoanbalByCustid(String custId);

	/**
	 * 本行淨值
	 * 
	 * @return
	 */
	Map<String, Object> getBankNetValue();

	/**
	 * 取得股票資訊(依照借款人統編查詢)
	 * 
	 * @param custId
	 * @return
	 */
	List<Map<String, Object>> selStock(String custId);

	/**
	 * 取得股票資訊(依照使用者輸入股票代號查詢)
	 * 
	 * @param stockNum
	 * @return
	 */
	List<Map<String, Object>> selStock2(String stockNum);

	/**
	 * 查詢最後一次續約日期
	 * 
	 * @param allCustId
	 *            統編+重覆序號
	 * @return
	 */
	Map<String, Object> selLastDate(String allCustId);

	/**
	 * 查詢異常通報類別
	 * 
	 * @return
	 */
	List<Map<String, Object>> selUnNormalClass();

	/**
	 * 查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @param type
	 * @return
	 */
	List<Map<String, Object>> selUnNormalBC(String type);

	/**
	 * 依照事項代碼查詢異常通報事項(解除異常通報、停權)
	 * 
	 * @param seqNo
	 * @return
	 */
	Map<String, Object> selUnNormalBCa(String type, String seqNo);

	/**
	 * 查詢異常通報事項-分行
	 * 
	 * @return
	 */
	List<Map<String, Object>> selUnNormal1();

	/**
	 * 依照事項代碼查詢異常通報事項-分行
	 * 
	 * @param seqNo
	 * @return
	 */
	Map<String, Object> selUnNormal1a(String seqNo);

	/**
	 * 查詢異常通報事項-營運中心
	 * 
	 * @return
	 */
	List<Map<String, Object>> selUnNormal2();

	/**
	 * 依照事項代碼查詢異常通報事項-營運中心
	 * 
	 * @param seqNo
	 * @return
	 */
	Map<String, Object> selUnNormal2a(String seqNo);

	/**
	 * 查詢異常通報事項-授管處
	 * 
	 * @return
	 */
	List<Map<String, Object>> selUnNormal3();

	/**
	 * 依照事項代碼查詢異常通報事項-授管處
	 * 
	 * @param seqNo
	 * @return
	 */
	Map<String, Object> selUnNormal3a(String seqNo);

	/**
	 * 刪除異常通報追蹤事項主檔(LNFE0854)--授管處核定時刪除
	 * 
	 * @param mainId
	 */
	void delUnNormal1(String mainId);

	/**
	 * 上傳異常通報追蹤事項主檔(LNFE0854)--授管處核定時上傳
	 * 
	 * J-109-0291_05097_B1001 簡化小規模營業人異常通報簽報流程
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @param mainId
	 * @param caseNo
	 * @param caseDate
	 * @param promiseCase
	 * @param promiseRatio
	 * @param firstDate
	 * @param lastDate
	 * @param totRiskAmt
	 * @param lostAmt
	 * @param isClosed
	 * @param closeDate
	 * @param closeCaseNo
	 * @param closeMainId
	 */
	void insertUnNormal1(String custId, String dupNo, String brno,
			String mainId, String caseNo, String caseDate, String promiseCase,
			int promiseRatio, String firstDate, String lastDate,
			long totRiskAmt, long lostAmt, String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String mdClass,
			String isMajor, String majorPt2, String majorPt3, String majorPt4,
			String caseType);

	/**
	 * 更新異常通報追蹤事項主檔結案註記
	 * 
	 * @param isClosed
	 *            要更新結案註記
	 * @param closeDate
	 *            要更新結案日期
	 * @param closeCaseNo
	 *            要更新結案案號
	 * @param closeMainId
	 *            要更新結案mainId
	 * @param mainId
	 *            案件簽報書mainId
	 * @param custId
	 *            案件簽報書主借款人
	 * @param dupNo
	 *            案件簽報書重覆序號
	 * @param caseBrid
	 *            案件簽報書簽案分行
	 */
	void update0854UnNormal5(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String mainId,
			String custId, String dupNo, String caseBrid);

	/**
	 * 更新異常通報追蹤事項主檔結案註記(若本案為結案時做)
	 * 
	 * @param isClosed
	 *            要更新結案註記
	 * @param closeDate
	 *            要更新結案日期
	 * @param closeCaseNo
	 *            要更新結案案號
	 * @param closeMainId
	 *            要更新結案mainId
	 * @param mainId
	 *            案件簽報書mainId
	 * @param custId
	 *            案件簽報書主借款人
	 * @param dupNo
	 *            案件簽報書重覆序號
	 * @param caseBrid
	 *            案件簽報書簽案分行
	 */
	void update0854UnNormal5a(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String custId,
			String dupNo, String caseBrid, boolean canCloseAllBranch);

	/**
	 * 刪除異常通報追蹤事項說明檔(LNFE0855)--授管處核定時刪除
	 * 
	 * @param mainId
	 */
	void delUnNormal2(String mainId);

	/**
	 * 上傳異常通報追蹤事項說明檔(LNFE0855)--授管處核定時上傳
	 * 
	 * @param DateList
	 */
	void insertUnNormal2(List<Object[]> DateList);

	/**
	 * 查詢異常通報追蹤事項明細檔(LNFE0856)--授管處核定時查詢
	 * 
	 * @param mainId
	 * @return
	 */
	List<Map<String, Object>> selectUnNormal3(String mainId);

	/**
	 * 刪除異常通報追蹤事項明細檔(LNFE0856)--授管處核定時刪除
	 * 
	 * @param mainId
	 */
	void delUnNormal3(String mainId);

	/**
	 * 上傳異常通報追蹤事項明細檔(LNFE0856)--授管處核定時上傳
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @param mainId
	 * @param caseNo
	 * @param seqNo
	 */
	void insertUnNormal3(String custId, String dupNo, String brno,
			String mainId, String caseNo, String seqNo, String ndFlag,
			Date runDate, String setCurr, long setAmt, String doMemo,
			int headMonth, String stopFg, String stopCaseNo, String stopMainId,
			String docDscr);

	/**
	 * 查詢異常通報更新LNFE0851註記
	 * 
	 * @param map
	 *            事項參數Map
	 * 
	 * @return
	 */
	List<Map<String, Object>> selLnfe0851Flag(Map<String, String[]> map);

	/**
	 * 更新往來異常戶彙總檔為結案(LNFE0851)--授管處核定時執行
	 * 
	 * @param mainId
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param caseNo
	 */
	void updateUnNormal4(String mainId, String custId, String dupNo,
			String caseBrid, String caseNo, boolean canCloseAllBranch);

	/**
	 * 更新往來異常戶彙總檔結案註記
	 * 
	 * @param isClosed
	 *            要更新結案註記
	 * @param closeDate
	 *            要更新結案日期
	 * @param closeCaseNo
	 *            要更新結案案號
	 * @param closeMainId
	 *            要更新結案mainId
	 * @param mainId
	 *            案件簽報書mainId
	 * @param custId
	 *            案件簽報書主借款人
	 * @param dupNo
	 *            案件簽報書重覆序號
	 * @param caseBrid
	 *            案件簽報書簽案分行
	 */
	public void update0851UnNormal5(String isClosed, String closeDate,
			String closeCaseNo, String closeMainId, String mainId,
			String custId, String dupNo, String caseBrid);

	/**
	 * 查詢往來異常戶彙總檔是否存在(LNFE0851)--授管處核定時執行
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return true:存在 false:不存在
	 */
	boolean selUnNormal4(String custId, String dupNo, String brno);

	/**
	 * 上傳往來異常戶彙總檔(LNFE0851)--授管處核定時執行
	 * 
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @param brno
	 * @param lostAmt
	 * @param collStat
	 * @param process
	 * @param sameIdea
	 * @param createTime
	 * @param mdClass
	 */
	void insertUnNormal4(String custId, String dupNo, String custName,
			String brno, long lostAmt, String collStat, String process,
			String sameIdea, Date createTime, String mdClass, String ndCode);

	/**
	 * J-109-0341_05097_B1001 Web e-Loan異常通報增加授信戶信評大幅貶落事項並傳送國內海外帳務系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param caseBrid
	 * @param ndCode
	 */
	public void update0851UnNormaData(String custId, String dupNo,
			String caseBrid, String ndCode);

	/**
	 * 查詢異常通報Excel內容
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重覆序號
	 * @param brno
	 *            分行代碼
	 * @return Excel內容
	 */
	List<Map<String, Object>> selExcel(String custId, String dupNo, String brno);

	/**
	 * 查詢是否已停權
	 * 
	 * @param brno
	 *            分行
	 * @param docType
	 *            LMS 企金，CLS 消金
	 * @return
	 */
	Map<String, Object> elf510_selStop(String brno, String docType);

	/**
	 * 依照分行別與借款人統編查詢AO人員資料
	 * 
	 * @param brno
	 * @param custId
	 * @return
	 */
	Map<String, Object> selAO(String brno, String custId);

	/**
	 * 刪除MISDB資料
	 * 
	 * @param msgFmtParam
	 *            替換訊息列
	 * @param data
	 *            資料陣列
	 * @return
	 */
	public int delete(Object[] msgFmtParam, Object[] data);

	/**
	 * 新增資料TO mis
	 * 
	 * @param coll
	 *            MISTable
	 * @param msgFmtParam
	 *            替換訊息列
	 * @param type
	 *            型態陣列
	 * @param lst
	 *            資料陣列
	 */
	public void insert(Object[] msgFmtParam, int[] type, List<Object[]> lst);

	/**
	 * 取得管理報表-案件統計表
	 * 
	 * @param bgnDate
	 *            起始時間
	 * @param endDate
	 *            結束時間
	 * @return
	 */
	List<Map<String, Object>> listLMS180R11(String bgnDate, String endDate,
			String[] otherCondition);

	/**
	 * 從ELF500 和MISLN20 取得該客戶統編不重覆的額度序號
	 * 
	 * @param custIdSet
	 *            統編的集合
	 * @return { custId:統編, cntrNo:額度序號 }
	 */
	HashMap<String, HashMap<String, String>> findDISTINCTCntrNoByCustId(
			HashSet<String> custIdSet);

	void update(Object[] msgFmtParam, int[] type, List<Object[]> lst);

	public List<Map<String, Object>> findGroupLoanByContractBrNo(
			String grpCntrNo, String brNo);

	public List<Map<String, Object>> findGroupLoanDetail(String custId,
			String grpCntrNo, String brNo);

	/**
	 * 依照分行代碼從ELF339取得該分行相關資料
	 * 
	 * @param brno
	 *            分行代碼
	 * @return 該分行相關資料
	 */
	public Map<String, Object> findBankData(String brno);

	/**
	 * 取得Pteamapp資料
	 * 
	 * @param custId
	 *            客戶統編
	 * @param DupNo
	 *            重複序號
	 * @param Year
	 *            年分
	 * @param Amtappno
	 *            額度申請序號
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findPteamappData(String custId,
			String DupNo, String Year, String Amtappno);

	public List<Map<String, Object>> findELF447NByBuildname(String Buildname,
			Integer tmod);

	/**
	 * 取得集團代號
	 * 
	 * @param custId
	 *            客戶統編
	 * @param DupNo
	 *            重複序號
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findGRPCMPByCustDup(String CustId,
			String DupNo);

	/**
	 * 行業對象別細分類
	 * 
	 * @param custId
	 *            客戶統編
	 * @param DupNo
	 *            重複序號
	 * 
	 * @return
	 */
	public List<Map<String, Object>> findCUSTDATAByCustDup(String CustId,
			String DupNo);

	/**
	 * 取得ELF431資料
	 * 
	 * @param BRNO
	 *            文件分行代碼
	 * @param DupNo
	 *            客戶統編
	 * @param DUPNO
	 *            重複序號
	 * @param UNDOCID
	 *            文件ID
	 * 
	 * @return
	 */

	public List<Map<String, Object>> findELF431Data(String BRNO, String CUSTID,
			String DUPNO, String CNTRNO, String KINDNO);

	/**
	 * 讀取銀行法利害關係人(是否有資料)
	 * 
	 * @param custId
	 *            借款人統編
	 * @return
	 */
	public List<Map<String, Object>> selRlt(String custId);

	/**
	 * 讀取銀行法利害關係人(詳細資料)
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	public Map<String, Object> selRlt(String custId, String dupNo);

	/**
	 * 續約檔資料 取得ELF513資料
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @return
	 */
	public ELF513 findELF513Data(String cntrNo);

	/**
	 * 查詢優惠房貸額度控管檔
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @return
	 */
	List<ELF431> findELF431Data(String custId, String dupNo);

	/**
	 * 更新續約次數檔
	 * 
	 * @param document
	 *            文件編號 ex:102CLS00011
	 * @param chgConTimes
	 *            續約次數
	 * @param cntrNo
	 *            額度序號
	 */
	public void updateELf513ByCntrNo(String document, int chgConTimes,
			String cntrNo);

	/**
	 * 新增續約次數檔
	 * 
	 * @param document
	 *            文件編號 ex:102CLS00011
	 * @param chgConTimes
	 *            續約次數
	 * @param cntrNo
	 *            額度序號
	 */
	public void insetELf513ByCntrNo(String document, int chgConTimes,
			String cntrNo);

	/**
	 * 查詢該客戶ID 在 行員檔是否存在
	 * 
	 * @param custId
	 *            客戶統編
	 * 
	 * @param staffId
	 *            配偶Id
	 */
	public List<Map<String, Object>> findMISSTAFFByCustId(String custId,
			String staffId);

	/**
	 * 判斷客戶是否行員(含1989之前退休的行員)<br/>
	 * 在「行員手冊」查找 「員工消費性貸款作業要點」及「員工購屋貸款、房屋修繕貸款及理財型房屋貸款」可查看異動歷程
	 * <ul>
	 * <li>098年6月19日：行員優惠利率，可適用 行員或其配偶
	 * <li>101年4月08日：行員優惠利率，可適用 行員本人
	 * </ul>
	 */
	public boolean isBankMan(String custId);

	/**
	 * 新案可以檢核，行員優惠利率 只適用 行員本人 <br/>
	 * 若 98年6月19日承做的案件，現在要來 變更條件、展延，不應阻擋
	 */
	public boolean isBankMan(String custId, String staffId);

	public boolean isBankMan_on_the_job(String givenId);

	public int findELF447NByUnid(String ELF447N_UNID);

	public ELF447N findELF447NByUKey(String ELF447N_UNID,
			String ELF447N_CUSTID, String ELF447N_DUPNO, String ELF447N_CONTRACT);

	/**
	 * 
	 * 全行分配優惠房貸總額度
	 * 
	 * @return
	 * 
	 *         <pre>
	 * key :28,38,56,59
	 * value :總金額 單位元(Table 為萬元 程式轉成元)
	 * </pre>
	 */
	public Map<String, BigDecimal> findMISELGHTAPPByAll();

	/**
	 * 
	 * 簽報書 查詢各優惠房貸 目前已使用額度
	 * 
	 * @return
	 * 
	 *         <pre>
	 * key :28,38,56,59
	 * value :總金額
	 * </pre>
	 */
	public Map<String, BigDecimal> findSUMBYELF431(HashSet<String> cntrNos);

	/**
	 * 
	 * 行員優惠房貸利率者的檢核不可以大於1500萬。 取得已經撥總額度
	 * 
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 總金額
	 */
	public BigDecimal find020ByCheckBankMan1(String custId);

	/**
	 * 
	 * 行員優惠房貸利率者的檢核不可以大於1500萬。減掉長搭短的case
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 總金額
	 */
	public BigDecimal find020ByCheckBankMan2(String custId);

	/**
	 * 行員優惠消貸利率者的檢核不可以大於100萬。
	 * 
	 * @param custId
	 *            客戶統編
	 * @return 總金額
	 */
	public BigDecimal find020ByCheckBankMan3(String custId);

	/**
	 * 抓客戶中文檔行業對象別(代碼與中文)/次產業別(代碼與中文)/企業性質別
	 * 
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重複序號
	 * @return
	 */
	Map<String, Object> findCustBussDataByIdAndDup(String custId, String dupNo);

	public int deleteLNF09G(String lnf09g_key_1, String lnf09g_key_2,
			String lnf09g_key_3);

	public List<Map<String, Object>> getCrsLNFE0854();

	public Map<String, Object> getLrsLNFE0854(String custId, String dupNo);

	/**
	 * J-104-0192-001 修改Web e-Loan企金授信覆審異常通報發生後之覆審周期計算
	 */
	public Map<String, Object> getLrsLNFE0854Next(String custId, String dupNo,
			String elf412_mddt, String elf412_lrdate);

	/**
	 * 透過團貸總戶編號找出資料
	 * 
	 * @param custId
	 * @param DupNo
	 * @param Grpcntrno
	 * @return
	 */
	List<Map<String, Object>> findPteamappDataByGrpcntrno(String Grpcntrno);

	/**
	 * 取得關係企業帳務資訊
	 * 
	 * @param allCustId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	List<Map<String, Object>> findLNF022_selL120s01n030(String allCustId,
			String custId, String dupNo, String custName);

	/**
	 * 取得同一人帳務資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> findLNF022_selL120s01n010(String custId,
			String dupNo);

	/**
	 * 取得同一關係人－自然人帳務資訊
	 * 
	 * @param allCustId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	public List<Map<String, Object>> findLNF022_selL120s01n020(
			String allCustId, String custId, String dupNo, String custName);

	/**
	 * 取得同一關係人－法人及自然人帳務資訊
	 * 
	 * @param allCustId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	public List<Map<String, Object>> findLNF022_selL120s01n080(
			String allCustId, String custId, String dupNo, String custName);

	/**
	 * G-104-0097-001 Web e-Loan
	 * 海外授信管理系統簽報書檢核對同一人、同一關係人、同一關係企業或集團之授信限額規定不得超過泰子行淨值25%。 取得集團授信明細
	 * 
	 * @param grpNo
	 * @return
	 */
	List<Map<String, Object>> findLNF022_selL120s01n090(String grpNo);

	/**
	 * 查詢目前停權起迄
	 * 
	 * @param brno
	 *            分行
	 * @return
	 */
	List<Map<String, Object>> elf510_queryStop(String brno);

	/**
	 * 918透過上傳EXCEL後捉A-LOAN的資再下傳EXCEL給918
	 */
	List<Map<String, Object>> findLNF155_findByLoanNo(String[] loanNos,
			String dataYYmm);

	public List<Map<String, Object>> findLNF155_ELF459_SRCFLAG_1(String DataYYmm);

	public int update_elf459_srcflag(String srcflag, String cntrNo);

	public void batch_update_elf459_srcflag(List<Object[]> batchValues);

	/**
	 * 取得客戶項下a-Loan未銷戶額務序號(企金用)
	 */
	List<Map<String, Object>> findAloanCntrnoByCustId(String custId);

	/**
	 * 取得客戶項下a-Loan已銷戶額務序號(企金用)
	 */
	List<Map<String, Object>> findAloanCancelCntrnoByCntrno(String cntrNo);

	/**
	 * 取得衍生性商品風險係數(信用轉換係數)
	 */
	public Map<String, Object> selCreditConverFactor(String dervKind,
			String dervPeriod);

	/**
	 * 查詢企金elf383央行註記資訊
	 */
	public List<Map<String, Object>> selL140M01MForQUOTAPPR(String custId);

	/**
	 * 查詢企金elf383央行註記資訊byID/CNTRNO/SDATE
	 */
	public Map<String, Object> selL140M01MForQUOTAPPR1(String custId,
			String cntrNo, String sdate);

	/**
	 * 更新企金elf383央行註記資訊byID/CNTRNO/SDATE
	 */
	public void updateL140M01MForQUOTAPPR(String CONTROLCD, BigDecimal LTVRATE,
			String LOCATIONCD, String JCICMARK, BigDecimal APPAMT,
			String PLUSREASON, String REG_PURPOSE, BigDecimal EST_AMT,
			BigDecimal LAWVAL, String COCOLL_FG, String PART_FUND,
			BigDecimal SUM_FACTAMT, String RESTRICT, String HP_HOUSE,
			String PLAN_AREA, String P_USETYPE, String P_LOANUSE,
			String COLL_CHAR, String KEEP_LAWVAL, String PLUS_MEMO,
			String SITE3NO, String SITE4NO, String COLL_CHAR_M, String SUPVNO,
			String VERSION, String HLOANLIMIT, String HLOANLIMIT_2,
			Date ENDDATE, String ISRENEW, String ISPAYOLDQUOTA,
			BigDecimal OLDQUOTA, BigDecimal PAYOLDAMT, String PAYOLDAMTITEM,
			String ISMATCHUNSOLDHOUSEITEM, String ISSALECASE, Date LSTDATE,
			BigDecimal TIMEVAL, String CUSTID, String CNTRNO, String SDATE);

	public List<Map<String, Object>> selC900M01FDataByLNF023(String cntrNo);

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記未編輯
	 */
	List<Map<String, Object>> findELF516_forRptDataNoEdit();

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記已編輯
	 */
	List<Map<String, Object>> findELF516_forRptDataEdit(String begDate,
			String endDate);

	/**
	 * Rpt[可疑代辦案件註記] 由MIS.ELF516中取得可疑代辦案件註記ALL
	 */
	List<Map<String, Object>> findELF516_forRptDataAll(String begDate,
			String endDate);

	Map<String, Object> getELF447N_ForClsByCntrno(String cntrNo);

	ELF447N getLatestELF447N_byCntrno(String cntrNo);

	/**
	 * 企金額度明細表整批引進最新資料作業_引進平均動用率
	 * 
	 * @param cntrNo
	 * @param date
	 * @return
	 */
	List<Map<String, Object>> findMrateByCntrnoAndDate1(String cntrNo,
			String date);

	List<Map<String, Object>> findELF493_docIdList(String branchId);

	List<ELF493> findELF493(String elf493_rptDocId, String elf493_docStus1);

	/**
	 * 依分行代號 統編，起迄年月查詢平均動用率
	 * 
	 * @param custId
	 * @param sDate
	 * @param eDate
	 * @param unitNo
	 * @return
	 */
	BigDecimal importAvgURateByCustIdDate(String custId, String sDate,
			String eDate, String unitNo);

	/**
	 * 查詢LN.LNF023已敘做科目
	 * 
	 * @param cntrNo
	 * @return
	 */
	List<Map<String, Object>> findLNF023_selByContract(String cntrNo);

	Map<String, Object> findLNF035_selByLoanNo(String loanNo);

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 判斷LNF030 該帳號72-2情況
	 * 
	 * @param cntrNo
	 * @return
	 */
	Map<String, Object> flndLNF030_sel722ByCntrNo(String cntrNo);

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 判斷LNF030 該帳號是否為購置不動產
	 * 
	 * @param cntrNo
	 * @return
	 */
	Map<String, Object> flndLNF030_sel722_UseTypeByCntrNo(String cntrNo);

	/**
	 * G-104-0286 加強銀行法72-2條之相關控管 取得排除事項內容
	 * 
	 * @param cntrNo
	 * @return
	 */
	Map<String, Object> flndLNF030_sel722_ExItemByCntrNo(String cntrNo);

	/**
	 * G-104-0333-001 配合萬磅分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權。
	 * 
	 * @param newBrno
	 * @param custId
	 * @param dupNo
	 * @param oldBrno
	 */
	void updateELF085XMdbrnoByCustId(String newBrno, String custId,
			String dupNo, String oldBrno);

	/**
	 * J-105-0078-001 Web e-Loan授信信用風險管理「遵循檢核表」當地限額之關係企業名單，請改依AS400集團建檔資料。
	 * 取得客戶名稱 1.SQL取得CNAME(有LCNAME以LCNANE，沒有以CNAME) 與 ENAME 2.判斷CUSTID，若為OBU
	 * ID，LCNAME 與 ENAME 八成一樣則顯示ENAME
	 */
	public String findCustFinalNameByIdAndDup(String custId, String dupNo);

	public Map<String, Object> findLNF087(String cntrNo);

	/**
	 * J-105-0179-001 Web e-Loan企金授信建立「往來異常通報戶」紀錄查詢及於簽報書上顯示查詢結果功能
	 * 取得最近一次異常通報紀錄(未結案優先顯示)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findLnfe0854LastStatusByCustId(
			String custId, String dupNo);

	/**
	 * 取得ALOAN衍生性金融商品所有期數 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public List<Map<String, Object>> findLnfe07BListByTradeType(
			String lnfe07BTradeType);

	/**
	 * 取得ALOAN衍生性金融商品對應期數之資料 N-105-0127-001 Web e-Loan配合衍生性金融商品風險係數修改
	 */
	public Map<String, Object> findLnfe07BByTradeTypeAndPERIOD(
			String tradeType, String periodType);

	/**
	 * 修改LNUNID 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 */
	public void updateJ1050202_by_custIdAndDupNo_byRptDoc_approve(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo);

	/**
	 * 修改LNUNID 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 */
	public void updateJ1050202_by_custIdAndDupNo_byDrawdown_approve(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo);

	/**
	 * 修改LNUNID 統編 J-105-0202-001 Web e-Loan企金授信修改客戶統編。
	 * 
	 * @param orgCustId
	 * @param orgDupNo
	 * @param newCustId
	 * @param newDupNo
	 * @param documentNo
	 */
	public void updateJ1050202_by_custIdAndDupNo_byRptDoc_reject(
			String orgCustId, String orgDupNo, String newCustId,
			String newDupNo, String documentNo, String cntrNo);

	/**
	 * 取得同業聯貸主辦By統編與分行 J-105-0287-001 修改Web e-Loan國內企金授信覆審系統
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */

	public List<Map<String, Object>> findLNF020SyndTypeOneByCustIdAndBrno(
			String custId, String dupNo, String brno);

	/**
	 * J-105-0263-001 配合a-Loan新增利率比對報表，Web e-Loan企金核准時同步更新ELF500相同額度之資料
	 * 
	 * @param document
	 * @param chgConTimes
	 * @param cntrNo
	 */
	public void updateELf500_NOTVALID_ByCntrNo(String cntrNo, String custId,
			String dupNo);

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	public Map<String, Object> findMaxSdateByCustIdAndCntrNoAndDoucmentNo(
			String custId, String dupNo, String cntrNo, String documentNo);

	/**
	 * J-105-0331-001 新增已核准授信額度辦理狀態通報彙總表
	 */
	public Map<String, Object> findLnf197AmtByContractAndCustId(String custId,
			String dupNo, String cntrNo);

	/**
	 * J-105-0321-001 Web e-Loan授信管理系統增加營運中心轄下分行往來客戶有全行通報異常情形彙總表 LMS180R32
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */
	public List<Map<String, Object>> findLMS180R32Data(String brnoArea);

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findLastQuotapprOrderBySDate(String custId,
			String dupNo, String cntrNo);

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 * @return
	 */
	public List<Map<String, Object>> findLastQuotsubByCustIdCntrNoAndSDate(
			String custId, String dupNo, String cntrNo, String sDate);

	/**
	 * J-105-0340-001 Web e-Loan 交換票據抵用科目調整並上傳a-Loan
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 */
	public void updateQuotsubLoanTp(String custId, String dupNo, String cntrNo,
			String sDate, String orgLoanTp, String newLoanTp);

	/**
	 * J-105-0346-001 Web e-Loan國內企金授信覆審報告表，主要授信戶增加判斷BTT建檔資料。
	 * 
	 * @param brno
	 * @param custId
	 * @return
	 */
	public Map<String, Object> selByBrNoAndCustId(String brno, String custId);

	public Map<String, Object> findCMFLUNVA_byUk(String custId, String dupNo);

	public List<LNF916S> findLNF916S(Date procDateB, Date procDateE,
			String custId, String formId);

	public List<LNF917S> findLNF917S(Date procDateB, Date procDateE,
			String custId, String formId);

	public List<LNF919S> findLNF919S(Date procDateB, Date procDateE,
			String custId, String formId);

	public List<Map<String, Object>> findLNF155_ym_br_cntrNo(
			String lnf155_data_ym, String lnf155_br_no,
			List<String> lnf155_contract_list);

	/**
	 * J-105-0308-001 Web e-Loan國內海外企金授信管理系統，額度明細表產品種類新增「新創重點產業」。
	 */
	public List<Map<String, Object>> findIsBusCdExistItwCode(String busCd);

	/**
	 * J-111-0129_05097_B1001 Web e-Loan企金授信額度明細表新增六大核心戰略產業及附屬細項
	 */
	public List<Map<String, Object>> findIsBusCdExistItwCodeCoreBuss(
			String busCd);

	/**
	 * J-106-0029-003 洗錢防制-新增實質受益人
	 * 
	 * @param brno
	 * @param custId
	 * @return
	 */
	public Map<String, Object> selCMFLUNBNByCustId(String custId, String dupNo);

	/**
	 * J-106-0030 授信簽報715科目轉檔為950
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @return
	 */
	Map<String, Object> findImexEXFFAIT(String custId, String dupNo,
			String cntrNo);

	/**
	 * J-106-0110-001 Web e-Loan國內、海外企金簽報書修改第八章、第九章標題及「授信信用風險管理遵循檢核表」。
	 * 同一關係企業/集團企業建檔資料
	 * 
	 * @param allCustId
	 * @param custId
	 * @param dupNo
	 * @param custName
	 * @return
	 */
	public List<Map<String, Object>> findLNF022_selL120s05b1_A(
			String allCustId, String custId, String dupNo, String custName);

	/**
	 * 取得土建融案件By統編與分行 J-106-0145-001 Web e-Loan 國內、海外企金授信管理系統借款人基本資料新增實地覆審負責分行
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brno
	 * @return
	 */
	public List<Map<String, Object>> findProd33And34ByCustIdAndBrNo(
			String custId, String dupNo, String brno);

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 */
	public void updateElf412_realCkFgAndRealDt1();

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 */
	public void updateElf412_realCkFgAndRealDt2();

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	public List<Map<String, Object>> batchForDoLmsBatch0010_queryElf412();

	/**
	 * J-106-0145-002 Web e-Loan 國內企金授信管理系統修改實地覆審相關功能
	 * 
	 */
	public List<Map<String, Object>> batchForDoLmsBatch0010_queryLnf022(
			String branch, String custId, String dupNo);

	/**
	 * J-106-0117-001 增加金控法45條利害關係人比對範圍-實質關係人(授信以外交易)
	 */
	public Map<String, Object> findRealRltByCustId(String custId, String dupNo);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能 條件無ctlType
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param lrDate_yyyy_MM
	 * @return
	 */
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R12(String branch,
			String custId, String dupNo, String lrDate_yyyy_MM);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能 條件有ctlType
	 * 
	 * @param branch
	 * @param custId
	 * @param dupNo
	 * @param lrDate_yyyy_MM
	 * @return
	 */
	public List<Map<String, Object>> gfnGenerateCTL_FLMS180R12_with_ctlType(
			String branch, String custId, String dupNo, String lrDate_yyyy_MM,
			String ctlType);

	/**
	 * J-106-0145-004 Web e-Loan 國內企金授信管理系統修改分行常董會權限實地覆審相關功能
	 * 
	 */
	public void doLmsBatch0011();

	public List<Map<String, Object>> func_J_106_0170_sql_1A_eq_ratePlan(
			String beginDate, String endDate, String lnf033_company_id,
			String lnf033_ratePlan, boolean changeToLNF13E);

	public List<Map<String, Object>> func_J_106_0170_sql_1A_ne_ratePlan(
			String beginDate, String endDate, String lnf033_company_id,
			String lnf033_ratePlan, boolean changeToLNF13E);

	public List<Map<String, Object>> func_J_106_0170_sql_1B(String beginDate,
			String endDate, String cntrNo);

	public LNF13E findLNF13E_contract(String cntrNo);

	/**
	 * G-106-0333-001 Web e-Loan 授信系統配合加拿大分行改制調整簽報書相關資料
	 */
	public void doLmsBatch0012();

	// J-106-0232-001 Web e-Loan國內、海外企金授信衍生性金融商品額度明細表新增淨值與額外信用增強
	public Map<String, Object> findLNF197AllBycntrNoAndCustId(String custId,
			String dupNo, String cntrNo);

	public List<Map<String, Object>> gen_J_106_0266_RPT();

	public List<Map<String, Object>> gen_J_107_0046_parent(String issuebrno,
			String custId, String grpCntrNo, String mGrpCntrNo,
			String p_dataYM_X, String p_dataYM_Y);

	public List<Map<String, Object>> gen_J_107_0046_child(String issuebrno,
			String custId, String grpCntrNo, String mGrpCntrNo,
			String p_dataYM_X, String p_dataYM_Y, String c_dataYM_X,
			String c_dataYM_Y, String lnf155_data_ym);

	public List<Map<String, Object>> gen_J_107_0046_childfor_factType3031(
			String issuebrno, String custId, String grpCntrNo,
			String mGrpCntrNo, String p_dataYM);

	// J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	List<Map<String, Object>> gfnCTL_Get_SYND_CTLBR_A(String branch,
			String custId, String dupNo);

	// J-106-0278-002 Web e-Loan國內企金授信配合實地覆審作業，額度簽報明細表增加聯貸案件管理行之建檔及修改實地覆審相關檢核
	List<Map<String, Object>> gfnCTL_Get_SYND_CTLBR_B(String branch,
			String custId, String dupNo);

	// J-106-0246-002 Web e-Loan授信系統企金額度明細表產品種類為G1政府機構低利優惠放款時，覆核時通知授信行銷處。
	public Map<String, Object> findProdClassIsG1();

	public Map<String, Object> findEscrowDataByLcNo(String lnf034_lc_no,
			String brNo);

	/**
	 * G-107-0115-001 Web e-Loan 授信系統配合巴箇行整併調整簽報書相關資料
	 */
	public void doLmsBatch0013();

	/**
	 * J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
	 */
	public List<Map<String, Object>> findCMFLUNSRByCustId(String custId,
			String dupNo);

	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findLnf02pByCustIdForAppendixA(
			String custId, String dupNo);

	/**
	 * J-111-0461_11557_B1031 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * 透過設定好關係的額度序號(應該是一買一賣的代號)查LNF02M
	 * 
	 * @param contract
	 * @return
	 */
	public List<Map<String, Object>> findLnf02mByContract(String contract);
	
	/**
	 * J-107-0164_05097_B1001 Web e-Loan國內企金授信額度明細表新增應收帳款簽案資訊
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findLnf02pByBuyerIdForAppendixB(
			String custId, String dupNo);

	/**
	 * J-107-0184_05097_B1001 Web e-loan企金授信簽報時提供以借款人查詢應簽報的額度明細表及該客戶所有的往來分行(
	 * 包含前次簽報書所簽報額度明細表所屬分行及現有有效額度的往來分行)等資訊,並於送呈前進行差異比對, 就存在差異時提供警示訊息,
	 * 以避免錯選授信案件授權層級情事。 取得客戶項下a-Loan所有額務序號(企金用)
	 */
	public List<Map<String, Object>> findAloanCntrnoByCntrnoIgnoreCancel(
			String cntrNo);

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	public List<Map<String, Object>> findLnfe0854UnClosedByCustIdAndBrNo(
			String custId, String dupNo, String brNo);

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	public Map<String, Object> findLnfe0854ByUnid(String mainId);

	/**
	 * J-107-0192_05097_B1001 針對企金異常通報戶之覆審，增加覆審名單之控管措施與報表。
	 */
	public Map<String, Object> findMaxLrDateByBranchAndCustId(String custId,
			String dupNo, String brNo);

	/**
	 * J-107-0263-001 Web e-Loan 授信系統配合國金部企金覆審清單檢核機制修改資料
	 */
	public void doLmsBatch0014();

	/**
	 * J-107-0213_05097_B1001 Web
	 * e-Loan國內企金覆審當同一分行對同一公司同時有自貸案額度及非「任管理行與主辦行」之聯行參貸額度時
	 * ,該自貸額度,覆審主辦單位不得維護為免辦理覆審之案件
	 * 
	 * @param custId
	 * @param dupNo
	 * @param brNo
	 * @return
	 */
	public List<Map<String, Object>> gfnCTL_Import_LNF020_Without_SYND(
			String branch, String custId, String dupNo);

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 集團企業--產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param grpId
	 * @return
	 */
	public List<Map<String, Object>> findLnf022ByGrpId(String grpId);

	/**
	 * J-107-0225_05097_B1001 Web e-Loan企金授信簽報書新增集團關係企業與本行授信往來條件比較表
	 * 關係企業--產生集團／關係企業與本行授信往來條件比較表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findLnf022ByByElcrecomCustId(
			String custId, String dupNo);

	/**
	 * LMS180R39 企金共同行銷報表
	 */
	public List<Map<String, Object>> queryLMS180R39Data();

	/**
	 * LMS180R40 簽報階段都更危老業務統計表
	 */
	public List<Map<String, Object>> queryLMS180R40Data();

	/**
	 * J-107-0337_05097_B1001 Web
	 * e-Loan企金授信簽報書借款人基本資料，調整銀行法利害關係人可列出銀行所指派法人代表之單位、職稱及姓名且可顯示多筆
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> selRltLevel1(String custId, String dupNo);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findElf515ByCntrNoForBt(String cntrNo);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param cntrNo
	 * @return
	 */
	public void updateElf515CntrNoByCntrNoForBt(String newCntrNo,
			String oldCntrNo, String modifyUnit);

	/**
	 * G-107-0642_05097_B1001 配合羅勇分行提升為全功能分行，依客戶移轉名單開放相關授信簽案之授權
	 * 
	 * @param cntrNo
	 * @return
	 */
	public void doLmsBatch0015_3(String lnf07Akey1, String exDate,
			String fBranch, String tBranch);

	public List<Map<String, Object>> get_ELF488_newCust(String elf488_data_date);

	public Map<String, Object> get_LNF320(String lnf320_query_date);

	/**
	 * 新承做消金案件
	 * 
	 * @param brno_area
	 * @param beg_yyyyMMdd
	 *            2018-07-01
	 * @param end_yyyyMMdd
	 *            2018-12-31
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule1(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 新承做跨區消金案件（不包括行員跨區貸款案件）
	 * 
	 * @param brno_area
	 * @param beg_yyyyMM
	 *            2018-07-01
	 * @param end_yyyyMM
	 *            2018-12-31
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule2(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 【近一年內轉催】未結清之消金逾催案件
	 * 
	 * @param brno_area
	 * @param beg_yyyyMMdd
	 *            2018-01-01
	 * @param end_yyyyMMdd
	 *            2018-12-31
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule3(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 首次撥款日後2年內即發生逾催呆之消金案件
	 * 
	 * @param brno_area
	 * @param beg_yyyyMMdd
	 *            2018-07-01
	 * @param end_yyyyMMdd
	 *            2018-12-31
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule4(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 首次撥款日後2年內即發生逾催呆之消金案件
	 * 
	 * @param empNo
	 * @param beg_yyyyMMdd
	 * @param end_yyyyMMdd
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule4_empNo_detail(
			String empNo, String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 前次覆審結果異常之「承辦行員」
	 * 
	 * @param brno_area
	 * @param beg_yyyyMMdd
	 * @param end_yyyyMMdd
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule5(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 新承做消金無擔保放款案件數最多之「承辦行員」
	 * 
	 * @param brno_area
	 * @param beg_yyyyMMdd
	 * @param end_yyyyMMdd
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_Rule6(String brno_area,
			String beg_yyyyMMdd, String end_yyyyMMdd);

	/**
	 * 若行員有調單位, 新舊單位都要出現
	 * 
	 * @param beg_yyyyMMdd
	 * @param end_yyyyMMdd
	 * @return
	 */
	public List<Map<String, Object>> get_ELF490B_findBrNoAndEmpNo(
			String beg_yyyyMMdd, String end_yyyyMMdd, String elf490b_dataYM);

	public List<Map<String, Object>> get_ELF491_notify_cntrNo_lack_loanNo(
			String cancel_beg_date);

	public List<Map<String, Object>> get_ELF491_notify_over_crDate(
			String cr_date);

	public List<Map<String, Object>> get_ELF491_notify_lack_lnf09g(
			String beg_date, String end_date);

	public List<Map<String, Object>> selDistinctCntrnoByCustidDupno(
			String custId, String dupNo);

	/**
	 * J-107-0357_05097_B1002 Web e-Loan授信系統配合工業區及產業園區建廠優惠貸款專案，額度簽報新增「專案種類」與相關報表
	 */
	public void doLmsBatch0016_1();

	/**
	 * J-107-0254_05097_B1001 Web e-Loan 新增對合作房仲業價金履約保證額度覆審報告表
	 */
	public void doLmsBatch0016_2();

	/**
	 * J-108-0023_05097_B1001 修改web e-loan授信覆審系統，南京東路分行授信戶覆審案件為不覆審。
	 */
	public void doLmsBatch0017();

	/**
	 * 銀行法第33條之3「同一關係人」資料
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	List<Map<String, Object>> getBankLaw33_3(String custId, String dupNo);

	public List<Map<String, Object>> getELF402_ECONOMIC_Y(String custId,
			String dupNo);

	/**
	 * J-108-0039_05097_B1001 Web e-Loan
	 * 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
	 */
	public List<Map<String, Object>> findCMFLUNB1ByCustId(String custId,
			String dupNo);

	public List<Map<String, Object>> findSynBankList(String FINKIND,
			String FINCODE);

	/**
	 * J-108-0116 企金處共同行銷 塞 MIS.SYNBANK 資料
	 */
	public void doLmsBatch0021();

	/**
	 * J-108-0212_07625_B1001 修正三多分行********異常通報
	 */
	void doLmsBatch0022();

	public List<Map<String, Object>> findCLS180R23_RatePlan_20();

	/**
	 * J-108-0178_05097_B1001 Web
	 * e-Loan企金授信系統簽報書增列符合公司法第206條第三項「與本行董事具有控制從屬關係之公司」之董監事名單。
	 */
	public List<Map<String, Object>> findELREMAINByElf902RcustId(String custId,
			String dupNo);

	/**
	 * J-108-0166 企業社會責任貸放情形統計表 LMS180R47
	 */
	public Map<String, Object> findLnunidList(String bgn, String end);

	public List<Map<String, Object>> findTPCConsumerCreditDetail(
			Set<String> targetSet);

	/**
	 * J-107-0342_05097_B1003 Web e-Loan授信系統新增覆審考核相關報表
	 * 
	 * @param brno
	 * @param custId
	 * @param dupNo
	 * @param ctlType
	 * @param baseDate
	 * @return
	 */
	public List<Map<String, Object>> findElf494ColsestReChkRpt(String brno,
			String custId, String dupNo, String ctlType, String baseDate);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> findMisRelatedCompany(String custId,
			String dupNo);

	/**
	 * J-108-0288_05097_B1001 Web e-Loan授信系統新增合併關係企業額度彙總表
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> findLnf197SumFactAmtByCustId(String custId,
			String dupNo);

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * 查詢股票資料
	 * 
	 * @param stkNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, Object> findStkDataByStkNo(String stkNo)
			throws CapException;

	/**
	 * J-108-0225_05097_B1001 Web e-Loan企金授信額度明細表新增設質予本行之總股數欄位與檢核
	 * 
	 * 查詢設質總股數
	 * 
	 * @param stkNo
	 * @return
	 * @throws CapException
	 */
	public Map<String, Object> findCOLL0307ByStkNoToSet(String stkNo)
			throws CapException;

	/**
	 * J-109-0050_05097_B1001
	 * 修改國金部簽報書2019國金行(兆)授字第00122號借戶ID由INZ0002372換成INZ0000120
	 * 
	 */
	public void doLmsBatch0027();

	/**
	 * J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
	 * 
	 * @param cntrNo
	 * @return
	 */
	public Map<String, Object> findElf515MomByCntr(String cntrNo);

	public List<Map<String, Object>> getAppropriationDataForLaborReliefLoanSummaryReport();

	List<Map<String, Object>> getELF505UnFinishData();

	List<Map<String, Object>> getELF505UnFinishDataV2();

	void updateELF505(String custId, String grntPaper, String srlNo,
			String srlNoStatus, String ap_dt, String up_dt, String msg);

	void updateELF505withVer(String custId, String grntPaper, String srlNo,
			String srlNoStatus, String ap_dt, String up_dt, String msg,
			int verNo);

	Map<String, Object> getELF505(String custId, int verNo);

	public List<Map<String, Object>> getLandOrBuildLoanCntrNoDataForMonthlyReport(
			String branchNo);

	// 已撥款未建擔保品
	public List<Map<String, Object>> findCLS1220R07_3_1(String brNo,
			String createTimeSince);

	// 已建擔保品尚未撥款
	public List<Map<String, Object>> findCLS1220R07_3_2(String brNo,
			String createTimeSince);

	// 已建額度尚未撥款
	public List<Map<String, Object>> findCLS1220R07_3_3(String brNo,
			String createTimeSince);

	//
	List<Map<String, Object>> getContractAndLoanNo(String custId, String dupNo);

	List<Map<String, Object>> findLnf020(String cntrNo);

	boolean chkFilterData(String custId, String dupNo, String cntrNo,
			String loanNo);

	boolean chkFilterData1(String custId, String dupNo, String cntrNo);

	boolean chkFilterData2(String cntrNo, String loanNo);

	ELF601 getElf601ByUnid(String unid);

	// J-110-0363 By ID
	List<ELF601> getElf601OnlyById(String custId, String dupNo, String brNo);

	List<ELF601> getElf601ByIdCntrNoLoanNo(String custId, String dupNo,
			String cntrNo, String loanNo, String brNo);

	List<ELF601> getElf601ByCntrNoLoanNo(String cntrNo, String loanNo,
			String brNo);

	List<ELF601> getElf601OnlyIdByFilter(String custId, String dupNo,
			String status, String brNo);

	List<ELF601> getElf601ByFilter(String custId, String dupNo, String cntrNo,
			String loanNo, String status, String brNo);

	ELF602 getElf602ByUnid(String unid);

	List<ELF601> getElf601ByUnidLike(String unid);

	List<ELF602> getElf602ByDatasrcLike(String datasrc);

	// J-110-0363 By ID
	List<ELF602> getElf602OnlyById(String custId, String dupNo, boolean undone,
			String brNo);

	List<ELF602> getElf602ByIdCntrNoLoanNo(String custId, String dupNo,
			String cntrNo, String loanNo, boolean undone, String brNo);

	List<ELF602> getElf602ByCntrNoLoanNo(String cntrNo, String loanNo,
			boolean undone, String brNo);

	List<ELF602> getElf602ByDatasrc(String elf601Unid);

	List<ELF602> getElf602OnlyIdByFilter(String custId, String dupNo,
			String startDate, String endDate, String[] handlingStatus,
			String brNo);

	List<ELF602> getElf602ByFilter(String custId, String dupNo, String cntrNo,
			String loanNo, String startDate, String endDate,
			String[] handlingStatus, String brNo);

	void deleteELF601(String unid);

	void deleteELF602(String unid);

	void insertELF601(ELF601 elf601);

	void insertELF602(ELF602 elf602);

    void insertELF602(List<ELF602> elf602List);
	void updateELF601Status(String unid, String status);

	void updateELF602StatusAndMemo(String unid, String status, String memo);

	Map<String, Object> getCountryCrd(String country);

	BigDecimal findLnf25cByType7(String crdType, String crdGrade);

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之附件檔案
	 */
	void updateLatestELF602(String oid, String unid);

	/**
	 * J-108-0345 貸後管理 最新一筆維護資料之證明文件說明
	 */
	List<Map<String, Object>> queryElf602HadMaintainList();

	void updateLatestELF602FileDesc(String fileDesc, String unid);

	List<Map<String, Object>> queryElf602List(ISearch pageSetting,
			String ownBrId, String fo_staffNo, String ao_staffNo);

	public String getMaxDateBetweenDrawdownEndDateAndCreditPeriodEndDateByLnf020(
			String contract);

	public List<Map<String, Object>> getElf517hBetweenStartDateAndEndDate(
			String startDate, String endDate);

	public int job_J_109_0344_step1();

	public int job_J_109_0344_step2();

	public List<Map<String, Object>> crs_chose_J_109_0372_1st();

	public List<Map<String, Object>> crs_chose_J_109_0372_byBatchNo(
			String LNF07A_KEY_1);

	boolean chkHadReview(String custId, String dupNo);

	/**
	 * 更新主從債務人最佳信用品質保證人的國別註記
	 * 
	 * @return
	 */
	public List<Map<String, Object>> getLnf020WithEllngtee();

	/**
	 * 最新內部評等(配合國家風險更新主從債務人信用品質保證人優先註記)
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> getMowtbl1LastFrByCustIdForCountryRisk(
			String custId, String dupNo);

	/**
	 * 內部評等等級轉換外部評等分數
	 * 
	 * @param type
	 * @param fr
	 * @return
	 */
	public Map<String, Object> getLnf25cMowScoreMapping(String type, String fr);

	/**
	 * 取得TYPE所有內部評等等級轉換外部評等分數
	 * 
	 * @return
	 */
	public List<Map<String, Object>> getLnf25cAllMowScoreMappingByType(
			String type);

	public boolean decide_cls_hasHouseLoan(String custId, String dupNo);

	public List<Map<String, Object>> lnf154_cls_all_loan(String custId,
			String dupNo);

	public void batchUpdate_J_111_0022_LNACNTR_CLSA(List<Object[]> batchValues);

	public void batchUpdate_J_111_0022_LNACNTR_CLSB(List<Object[]> batchValues);

	// public List<Map<String, Object>>
	// getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno(String
	// grpCntrnoStr);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 */
	public List<Map<String, Object>> doLmsBatch0037_01(String bgnMonth,
			String endMonth);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0037_02(String cntrNo,
			String dataYm);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0037_03(String cntrNo,
			String dataYm);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0037_04(String cntrNo,
			String dataYm);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param cntrNo
	 * @return
	 */
	public List<Map<String, Object>> doLmsBatch0037_05(String cntrNo,
			String dataYm);

	/**
	 * J-110-0000_05097_B1001 配合2021/02專案金檢，產生擔保品檔案
	 * 
	 * @param upCntrNo
	 * @param upCity
	 * @param upTimeVal
	 * @param upAddr
	 * @param upCount
	 * @param upIsZu
	 */
	void insertDoLmsBatch0037(String dbName, String upCntrNo, String upCity,
			BigDecimal upTimeVal, String upAddr, int upCount, String upIsZu,
			String dataYm);

	/**
	 * LMS180R63 境內法人於國際金融業務分行辦理外幣授信業務報表
	 * 
	 * J-110-0049_05097_B1001 Web e-Loan企金授信增加「境內法人於國際金融業務分行辦理外幣授信業務報表」
	 * 
	 * @return
	 */
	public List<Map<String, Object>> queryLMS180R63Data(String bgnDate,
			String endDate);

	public Map<String, Object> findIndustryObjectCodeByBSTBL(String code);

	/**
	 * @param code
	 * @param mark
	 *            => 在 SELECT mark, count(*) FROM MIS.BSTBL group by mark
	 *            的值域有{A:在0024下拉選單看不到,0:}。例如 {在 0024 不能選 014700}
	 * @return
	 */
	public Map<String, Object> findIndustryObjectCodeByBSTBL_mark(String code,
			String mark);

	// public List<Map<String, Object>>
	// getValidGroupLoanBuildCaseMasterDataByApprovedGrpCntrno(String
	// grpCntrnoStr);

	/**
	 * 南京東路客戶移轉國外部
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param sDate
	 * @return
	 */
	public List<Map<String, Object>> findLnf078T(String aLoanDate);

	public Map<String, Object> findLnf020AndLnf030DataByCntrNo(String cntrNo);

	public List<Map<String, Object>> findSmallBussCDataByCustId(String custId);

	public List<Map<String, Object>> getAppropriationDateForLaborReliefLoanDetailReport(
			String applyDate, String custId);

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號取授信餘額
	 * 
	 * @return
	 */
	public BigDecimal queryLNF022getLOANBAL(String contract);

	/**
	 * J-110-0272 該客戶所有額度下最新簽報的授信科目
	 */
	public Map<String, Object> findQUOTSUBlatestLoanTPs(String custId,
			String dupNo, String date);

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號查找是否有已完成覆核的企金動審表(ELF383)或消金動審表(ELF500)
	 * 
	 * @return
	 */
	public int queryCLS180R42Data1(String contract);

	/**
	 * CLS180R42特定金錢信託案件量統計報表，依額度序號取放款帳號、利率、首次動撥金額
	 * 
	 * @return
	 */
	public List<Map<String, Object>> queryCLS180R42Data2(String codecontract);

	/**
	 * J-110-0304_05097_B1001 Web e-Loan授信覆審配合RPA作業修改
	 * 
	 * @return
	 */
	public List<Map<String, Object>> queryLnf09gByTxnCodeAndKey2(
			String txnCode, String bgnDate, String endDate);

	public List<Map<String, Object>> findlnf025CoByCntrNoForLgd(
			List<String> cntrNos);

	// LGD
	public List<Map<String, Object>> findlnf025ByCntrNoCo(String cntrNoCo);

	public Map<String, Object> findLnf252ByCntrNo(String cntrNo);

	List<Map<String, Object>> selUnNormal4a(String custId, String dupNo);

	public Map<String, String> selStockCompanyId(String stockNum);

	public Map<String, Object> findLnf447nByCntrNoAndStatusAndUuid(String cntrNo);

	public List<Map<String, Object>> getUnsoldHouseDataByUncancelledAndRemainingHouses(
			String toDate);

	public List<Map<String, Object>> getUnsoldHouseTrackReportDataFor918();

	public List<Map<String, Object>> getUnsoldHouseTrackReportDataForBranch(
			String branchCode);

	public List<Map<String, Object>> queryList_for_cls_l120s06b_type2_orderByRate(
			String prodKind, String c900m01d_subjCode2, String lnf030_use_type,
			String endDateBeg, int rtn_cnt);

	public List<Map<String, Object>> getELF516UneditedDataAndCheckTimesLessThan3timesByYYYYMM(
			String yyyyMM, String checkTimeYYYYMM);

	public void updateElf516CheckTimes(Integer checkTimes, String cntrNo,
			String custId, String dupNo, String yyymm, String status);

	public List<Map<String, Object>> getELFDELYDEarliestOverdueDataByCustId_dupNo_Contract(
			String custId, String dupNo, String cntrNo);

	public List<Map<String, Object>> getElf516UneditedDataAndCheckTimesLessThan3times(
			String checkTimeYYYYMM);

	public String getLatestFirstLoanDateByLnf030Cntrno(String cntrNo);

	public List<Map<String, Object>> getLmsPlantLoanTrackingData();

	public Map<String, Object> getLNF320ByNextBusinessDay(String lnf320NextDate);

	/**
	 * J-111-0423_05097_B1001 Web
	 * e-Loan企金授信就海外分行承做永續績效連結授信案(如附件)，於E-Loan「永續績效連結授信」相關註記
	 * 
	 */
	public void updateEsgDataFromLms2105v01ServiceImpl(String cntrNo,
			String esgSustainLoan, String esgSustainLoanType,
			String esgSustainLoanUnReach);

	public List<Map<String, Object>> getELF516EditedDataLastSpecifiedMonth(
			String yyyyMM);

	public List<Map<String, Object>> getUnconEmptyLandDataByActStDateAndUncancelled();

	/**
	 * J-111-0551 在途授信額度
	 */
	public List<Map<String, Object>> selElAmt(String custId, String dupNo);

	/**
	 * J-112-0342 新增產生企金授信簽報案件明細檔
	 */
	List<Map<String, Object>> listLMS180R75_mis(String bgnDate, String endDate);

	// 消金非房貸4.0
	public Map<String, Object> getLNF022_loanBalSByid(String id);

	public Map<String, Object> getLNF022_loanBalNByid(String id);

	List<Map<String, Object>> getIntroductionSrcIsNotNullData();

	public void insertLNF13E(String contractNo, String megaEmpNo,
			String agntNo, String agntChain, String megaCode, String subUnitNo,
			String subEmpNo, String subEmpNm, String introSrc, String megaEmpBrn);
	
	/**
	 * J-112-0438_12473_B1001 刪除LNF13E
	 * @param contractNo
	 */
	public void deleteLNF13EByKey(String contractNo);

	/**
	 * J-111-0633_05097_B1001 Web e-Loan授信系統不動產暨72-2相關資訊註記維護之頁面，增列補鍵產品種類33、34之功能
	 * 
	 * @param cntrNo
	 * @param prodKind
	 * @param adcCaseNo
	 */
	public void updateAdcInfo(String cntrNo, String prodKind, String adcCaseNo);

	public BigDecimal getSingleBankManLimitAmount();

	public List<Map<String, Object>> findJCICSubmitByCntrNo(String CntrNo);

	Map<String, Object> getELF632LastestByCntrNo(String CntrNo);

	void addElf632(Timestamp ELF632_TXN_DATE, String ELF632_CONTRACT,
			String ELF632_BANK_CODE, String ELF632_LN_BR_NO,
			String ELF632_FACT_TYPE, String ELF632_CUST_ID_B,
			String ELF632_SWFT_B, String ELF632_FACT_AMT_B,
			String ELF632_DBR22_AMT_B, String ELF632_CUST_ID_A,
			String ELF632_SWFT_A, String ELF632_FACT_AMT_A,
			String ELF632_DBR22_AMT_A, String ELF632_WRITE_SYS, String ELF632_LOAN_NM_1, String ELF632_LOAN_NM_2);

	public List<Map<String, Object>> getOverdueLoanCaseRecommendedByLandsman(
			String yyyyMM, String fromDate, String toDate);

	public Map<String, Object> getLatestChargeClerkOfSigningBook(String cntrNo,
			String authBranchNo);

	public List<Map<String, Object>> findLNF020_NOTICE_TYPE(String cntrno);

	public BigDecimal getCoupleBankManLimitAmount();

	/**
	 * J-111-0525 取得以房養老控制檔額度序號
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public List<Map<String, Object>> get_LNF242_Contractno(String custId,
			String dupNo, String actualtime);

	public List<Map<String, Object>> get_LNF242ByNxtpdate(String basedate,
			String nxtdate);

	public Map<String, Object> get_LNF320_nextBussdate(String lnf320_query_date);

	public void updateLNF242(String idchg_fg, String ward_fg, String wealth_fg, String custId,
			String dupNo, String contracts);

	public List<Map<String, Object>> getMISAMF_byCustidBrno(String custId,
			String brNo, String toDay);

	public void updateElf516UnfundedAndCanceledCase(String lnFlag,
			Integer checkTimes, String cntrNo, String custId, String dupNo,
			String yyymm, String status);

	/**
	 * J-112-0267勞工紓困還款介接檔
	 * 
	 * @return
	 */
	public List<Map<String, Object>> get_ELF630(String payday_start,
			String payday_end);

	public List<Map<String, Object>> get_TelNNo(String brnno);

	public void updateELF630userName(String BankUserName, String StaffTel1,
			String StaffTel2, String StaffTelExt, String orgSrlNo8,
			String orgSrlNo9, String PayDay);

	public void updateELF630(String dataStatus, String srlNo, String sysMsg,
			String remark, String receiveDay, String orgSrlNo8,
			String orgSrlNo9, String payDay);

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param bgnDate
	 * @param endDate
	 * @return
	 */
	public List<Map<String, Object>> get_ELF492_By_LrDate(String bgnDate,
			String endDate);

	/**
	 * J-111-0554_05097_B1001 Web e-Loan授信修改授信覆審作業系統中之相關事宜
	 * 
	 * @param unid
	 * @param apprId
	 */
	public void updateELF942_ApprId_By_Unid(String unid, String apprId);

	public List<Map<String, Object>> creditLoanCheckLoanDate(String custId, String dupNo, String cntrNo);

	/**
	 * J-112-0134 「關係企業、主從債務人及從債務擔保債務查詢」清單
	 * @param custId 借款人ID
	 * @param dupNo 借款人ID重複序號
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> findRelatedPartyList(String custId, String dupNo);

	public Map<String, Object> findElf500_Elf383_Lnf087ByCntrNo(String cntrNo);

	public void insertELF690_EjcicT70Inquiry(String custId, String dupNo, String queryReason, String branchNo, String staffNo);

	public Map<String, Object> findELF690ByCustIdAndDupNoAndBranchNo(String custId, String dupNo, String branchNo);
	
	/**
	 * J-112-0522 查詢是否存在該額度序號
	 * @param cntrNo 額度序號
	 * @return 筆數
	 */
	int findLNF277ByCntrno(String cntrNo);

	/**
	 * J-113-0519 e-Loan授信覆審作業系統之覆審名單篩選條件修正
	 * @return
	 */
    int updateRetrialDataJ1130519();
}
