package com.mega.eloan.lms.crs.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.CrsUtil;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.C241M01ADao;
import com.mega.eloan.lms.dao.C241M01BDao;
import com.mega.eloan.lms.dao.C241M01CDao;
import com.mega.eloan.lms.dao.C241M01EDao;
import com.mega.eloan.lms.model.C241M01A;
import com.mega.eloan.lms.model.C241M01B;
import com.mega.eloan.lms.model.C241M01C;
import com.mega.eloan.lms.model.C241M01E;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * 產生簽報書PDF
 * 
 * <AUTHOR>
 * 
 */
@Service("lms2415r01rptservice")
public class LMS2415R01RptServiceImpl implements FileDownloadService {

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS2415R01RptServiceImpl.class);

	@Resource
	C241M01ADao C241m01aDao;

	@Resource
	C241M01BDao C241m01bDao;

	@Resource
	C241M01CDao C241m01cDao;

	@Resource
	C241M01EDao C241m01eDao;

	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branch;

	@Resource
	LMSService lmsService;
	
	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception 
	 * @throws IOException 
	 */
	public OutputStream generateReport(PageParameters params) throws IOException, Exception {
		String mainId = params.getString("mainId");
		String rptOid = Util.nullToSpace(params.getString("rptOid"));
		Map<InputStream,Integer> pdfNameMap = new LinkedHashMap<InputStream,Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		String[] dataSplit = rptOid.split("\\|");
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		Properties rptProperties = null;
		int subLine = 7;
		try {
			MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
			
			locale = LMSUtil.getLocale();
			rptProperties = MessageBundleScriptCreator
			.getComponentResource(LMS2415R01RptServiceImpl.class);
			propEloanPage = MessageBundleScriptCreator.getComponentResource(AbstractEloanPage.class);
			for (String temp : dataSplit) {
				outputStream = null;
				String type = temp.split("\\^")[0];

				
				String caseBrId = "";
				C241M01A C241m01a = C241m01aDao.findByMainId(mainId);
				if(C241m01a != null){
					 caseBrId = C241m01a.getOwnBrId();
				}else{
					 caseBrId = user.getUnitNo();
				}

				if ("R01".equals(type)) {
					if(Util.equals("",Util.nullToSpace(C241m01a.getRptId()))){
						outputStream = this.genLMS2415R01(mainId,locale,rptProperties);
					} else if(Util.equals(CrsUtil.V_201809,Util.nullToSpace(C241m01a.getRptId()))){	//J-107-0128海外改格式
						subLine = 13;
						outputStream = this.genLMS2415R01V2(mainId, locale, rptProperties);
					} else if(Util.equals(CrsUtil.V_202209,Util.nullToSpace(C241m01a.getRptId()))){	//J-107-0128海外改格式
						subLine = 13;
						outputStream = this.genLMS2415R01V3(mainId, locale, rptProperties);
					}
				} else if ("R02".equals(type)) {
					outputStream = this.genLMS2415R02(mainId,locale,rptProperties);
				}
				if(outputStream != null){
					pdfNameMap.put(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()),subLine);
				}else{
					pdfNameMap.put(null,subLine);
				}
				
				
				
			}
			if(pdfNameMap != null && pdfNameMap.size() > 0){
				outputStream = new ByteArrayOutputStream();
				PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,propEloanPage.getProperty("PaginationText"), true,locale,subLine);
				list.add(new ByteArrayInputStream(((ByteArrayOutputStream) outputStream).toByteArray()));
			}
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(list, outputStream);
		}finally{
			
		}
		return outputStream;
	}

	/*
	 * (non-Javadoc) 呈現在頁面用的
	 * 
	 * @see
	 * com.mega.eloan.lms.base.service.FileDownloadService#getContent(org.apache
	 * .wicket.PageParameters)
	 */
	@Override
	public byte[] getContent(PageParameters params) throws Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}

	/**
	 * 產生LMS1205R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception 
	 * @throws IOException 
	 * @throws FileNotFoundException 
	 */
	public OutputStream genLMS2415R01(String mainId, Locale locale,Properties rptProperties)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		ReportGenerator generator = new ReportGenerator(
				"report/crs/LMS2415R01_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C241M01A C241m01a = null;
		List<C241M01C> C241m01cList = null;
		List<C241M01B> C241m01bList = null;
		List<C241M01E> C241m01eList = null;
		String branchName = null;
		Map<String, String> crdTypeMap = null;
		Map<String, String> quotaTypeMap = null;
		try {
			C241m01a = C241m01aDao.findByMainId(mainId);
			if(C241m01a == null){
				C241m01a = new C241M01A();
			}
			C241m01cList = C241m01cDao.findByMainId(mainId);
			C241m01bList = C241m01bDao.findByMainId(mainId);
			C241m01eList = C241m01eDao.findByMainId(mainId);
			crdTypeMap = codetypeservice.findByCodeType("CRDType");
			if (crdTypeMap == null) {
				crdTypeMap = new LinkedHashMap<String,String>();
			}
			quotaTypeMap = codetypeservice.findByCodeType("c241m01b_quotaType");
			if (quotaTypeMap == null) {
				quotaTypeMap = new LinkedHashMap<String,String>();
			}

			branchName = branch.getBranchName(Util.nullToSpace(C241m01a
					.getOwnBrId()));
			
			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00","");
			rptVariableMap.put("LOGOSHOW",logoPath );		
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			// 列印今天日期
			rptVariableMap.put("PRINTDAY", TWNDate.toAD(new Date()));
			rptVariableMap = this.setC241M01AData(rptVariableMap, C241m01a, rptProperties);
			rptVariableMap = this.setC241M01CListData(rptVariableMap,
					C241m01cList,rptProperties);
			titleRows = this.setC241M01BListData(titleRows, C241m01bList, true,quotaTypeMap);
			rptVariableMap = this.setC241M01BData(rptVariableMap, C241m01bList);
			rptVariableMap = this.setC241M01AForM01DData(rptVariableMap, C241m01a,rptProperties);
			rptVariableMap = this.setC241M01EData(rptVariableMap, C241m01eList);
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("D:/test_Mega_Report/已完成-LMS/LMS2415R01_zh_TW.rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
			if (quotaTypeMap != null) {
				quotaTypeMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 產生LMS2415R02的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception 
	 * @throws IOException 
	 * @throws FileNotFoundException 
	 */
	public OutputStream genLMS2415R02(String mainId, Locale locale,Properties rptProperties)
			throws FileNotFoundException, IOException, Exception {
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator(
				"report/crs/LMS2415R02_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		C241M01A C241m01a = null;
		List<C241M01B> C241m01bList = null;
		List<C241M01C> C241m01cList = null;
		String branchName = null;
		Map<String, String> quotaTypeMap = null;
		try {
			C241m01a = C241m01aDao.findByMainId(mainId);
			if(C241m01a == null){
				C241m01a = new C241M01A();
			}
			C241m01bList = C241m01bDao.findByMainId(mainId);
			C241m01cList = C241m01cDao.findByMainId(mainId);
			quotaTypeMap = codetypeservice.findByCodeType("c241m01b_quotaType");
			if (quotaTypeMap == null) {
				quotaTypeMap = new LinkedHashMap<String,String>();
			}

			branchName = branch.getBranchName(Util.nullToSpace(C241m01a
					.getOwnBrId()));
			
			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00","");
			rptVariableMap.put("LOGOSHOW",logoPath );		
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			rptVariableMap = this.setC241M01AData(rptVariableMap, C241m01a, rptProperties);
			titleRows = this
					.setC241M01BListData(titleRows, C241m01bList, false,quotaTypeMap);
			rptVariableMap = this.setC241M01CListData(rptVariableMap,
					C241m01cList,rptProperties);
			// generator.setLang(java.util.Locale.TAIWAN);
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);
			// generator.setReportFile("report/lms/LMS1205R01_zh_TW.rpt");
			// generator.setTestMethod(true);
			// generator.checkVariableExist("C:/test.txt", rptVariableMap);
			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (quotaTypeMap != null) {
				quotaTypeMap.clear();
			}
		}

		return outputStream;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01A)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01A
	 *            C241M01A資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01AData(
			Map<String, String> rptVariableMap, C241M01A C241m01a,Properties rptProperties) {
		String ver = Util.nullToSpace(C241m01a.getRptId());	//J-107-0128海外改格式
		rptVariableMap.put("C241M01A.RETRIALDATE",
				Util.nullToSpace(TWNDate.toAD(C241m01a.getRetrialDate())));
		rptVariableMap.put("C241M01A.PROJECTSEQ",
				Util.nullToSpace(C241m01a.getProjectNo()));
		if(Util.equals("",ver)){
			rptVariableMap.put("C241M01A.LASTRETRIALDATE",
					Util.nullToSpace(TWNDate.toAD(C241m01a.getLastRetrialDate())));
		} else {
			if (Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(C241m01a.getLastRetrialDate())) 
					|| C241m01a.getLastRetrialDate() == null){
				rptVariableMap.put("C241M01A.LASTRETRIALDATE", rptProperties.getProperty("RPTSHOW.NO"));	// 無
			} else {
				rptVariableMap.put("C241M01A.LASTRETRIALDATE",
					Util.nullToSpace(TWNDate.toAD(C241m01a.getLastRetrialDate())));
			}
		}
		rptVariableMap.put("C241M01A.CUSTID",
				Util.nullToSpace(C241m01a.getCustId()));
		rptVariableMap.put("C241M01A.DUPNO",
				Util.nullToSpace(C241m01a.getDupNo()));
		rptVariableMap.put("C241M01A.CUSTNAME",
				Util.nullToSpace(C241m01a.getCustName()));
		rptVariableMap.put("C241M01A.RANDOMCODE",
				Util.nullToSpace(C241m01a.getRandomCode()));
		if(Util.equals("",ver)){
			rptVariableMap.put("C241M01A.QUOTAAMT",
					NumConverter.addComma(C241m01a.getTotQuota() == null ? BigDecimal.ZERO : C241m01a.getTotQuota().divide(new BigDecimal(1000)),"#,##0.00"));
			rptVariableMap.put("C241M01A.BALAMT",
					NumConverter.addComma(C241m01a.getTotBal() == null ? BigDecimal.ZERO : C241m01a.getTotBal().divide(new BigDecimal(1000)),"#,##0.00"));
		} else {
			rptVariableMap.put("C241M01A.QUOTAAMT",
					NumConverter.addComma(C241m01a.getTotQuota() == null ? BigDecimal.ZERO : 
						C241m01a.getTotQuota().divide(new BigDecimal(1000))
						.setScale(2, BigDecimal.ROUND_HALF_UP),"#,##0.00"));
			rptVariableMap.put("C241M01A.BALAMT",
					NumConverter.addComma(C241m01a.getTotBal() == null ? BigDecimal.ZERO : 
						C241m01a.getTotBal().divide(new BigDecimal(1000))
						.setScale(2, BigDecimal.ROUND_HALF_UP),"#,##0.00"));
		}
		rptVariableMap.put("C241M01A.QUOTACURR", Util.trim(C241m01a.getTotQuotaCurr()));
		rptVariableMap.put("C241M01A.BALCURR", Util.trim(C241m01a.getTotBalCurr()));
		
		rptVariableMap.put("C241M01A.LNDATADATE", Util.trim(TWNDate.toAD(C241m01a.getLnDataDate())));
		rptVariableMap.put("C241M01A.COMID", Util.trim(C241m01a.getComId()));
		rptVariableMap.put("C241M01A.COMDUP", Util.trim(C241m01a.getComDup()));
		rptVariableMap.put("C241M01A.COMNAME", Util.trim(C241m01a.getComName()));
		rptVariableMap.put("C241M01A.BUYERID", Util.trim(C241m01a.getBuyerId()));
		rptVariableMap.put("C241M01A.BUYERDUP", Util.trim(C241m01a.getBuyerDup()));
		rptVariableMap.put("C241M01A.BUYERNAME", Util.trim(C241m01a.getBuyerName()));
		rptVariableMap.put("C241M01A.RPTID", Util.trim(C241m01a.getRptId()));
		
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param C241m01bList
	 *            C241M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private List<Map<String, String>> setC241M01BListData(
			List<Map<String, String>> titleRows, List<C241M01B> C241m01bList,
			boolean result,Map<String,String> quotaTypeMap) {
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");
		titleRows.add(map);
		for (C241M01B C241m01b : C241m01bList) {
			if (result && count >= 3) {
				count++;
			}else if (!result && count < 3) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02",
						Util.nullToSpace(C241m01b.getSubjectName()));
				map.put("ReportBean.column03",
						Util.nullToSpace(C241m01b.getQuotaNo()));
				map.put("ReportBean.column04",
						Util.nullToSpace(C241m01b.getQuotaCurr())
								+ NumConverter.addComma(C241m01b.getQuotaAmt() == null ? BigDecimal.ZERO : C241m01b.getQuotaAmt().divide(new BigDecimal(1000)),"#,##0.00"));
				map.put("ReportBean.column05",
						Util.nullToSpace(C241m01b.getBalCurr())
								+ NumConverter.addComma(C241m01b.getBalAmt() == null ? BigDecimal.ZERO : C241m01b.getBalAmt().divide(new BigDecimal(1000)),"#,##0.00"));
				map.put("ReportBean.column06",
						Util.nullToSpace(TWNDate.toAD(C241m01b.getUseFDate())) + "~"
								+ Util.nullToSpace(TWNDate.toAD(C241m01b.getUseEDate())));
				map.put("ReportBean.column07",
						Util.nullToSpace(TWNDate.toAD(C241m01b.getLoanFDate())) + "~"
								+ Util.nullToSpace(TWNDate.toAD(C241m01b.getLoanEDate())));
				map.put("ReportBean.column09",
						Util.trim(quotaTypeMap.get(Util.nullToSpace(C241m01b.getQuotaType()))));
				map.put("ReportBean.column10",
						Util.nullToSpace(C241m01b.getGuaranteeName()));
				map.put("ReportBean.column11",
						Util.nullToSpace(C241m01b.getMajorMemo()));
				map.put("ReportBean.column12",
						Util.nullToSpace(C241m01b.getEstCurr())
								+ NumConverter.addComma(C241m01b.getEstAmt() == null ? 0 : C241m01b.getEstAmt().divide(new BigDecimal(1000))));
				map.put("ReportBean.column13",
						Util.nullToSpace(C241m01b.getLoanCurr())
								+ NumConverter.addComma(C241m01b.getLoanAmt() == null ? 0 : C241m01b.getLoanAmt().divide(new BigDecimal(1000))));
				titleRows.add(map);
				count++;
			}
		}
		if (result) {
			for(int i = count ; i < 3 ; i++){
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");
		titleRows.add(map);
		return titleRows;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param C241m01bList
	 *            C241M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private Map<String, String> setC241M01BData(
			Map<String, String> rptVariableMap, List<C241M01B> C241m01bList) {
		StringBuffer str = new StringBuffer();
		HashSet<String> list = new HashSet<String>();
		for (C241M01B C241m01b : C241m01bList) {
			String[] temp = Util.nullToSpace(C241m01b.getGuarantor())
					.split("、");
			for (String temp2 : temp) {
				list.add(temp2);
			}
		}

		for (String temp : list) {
			str.append(temp).append("、");
		}
		if (str.length() == 0) {
			str.append("、");
		}
		rptVariableMap.put("C241M01B.GUARANTOR",
				str.substring(0, str.length() - 1));
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01C
	 *            C241M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01CListData(
			Map<String, String> rptVariableMap, List<C241M01C> C241m01cList,Properties rptProperties) {
		StringBuffer str = new StringBuffer();
		if(C241m01cList.size()  > 0){
			for (C241M01C c241m01c : C241m01cList) {
				if(("B009".equals(c241m01c.getItemNo()) && !"N".equals(c241m01c.getChkResult())) || (!"B009".equals(c241m01c.getItemNo()) && Util.isNotEmpty(c241m01c.getChkText()))){
					if("B009".equals(c241m01c.getItemNo()) && (Util.isEmpty(c241m01c.getChkPreReview()) || "K".equals(Util.trim(c241m01c.getChkPreReview()))) ){
						
					}else{
						if(str.length() > 0){
							str.append("\n");
						}
						str.append(rptProperties.getProperty("C241M01C.SPE")).append(Util.trim(c241m01c.getItemSeq())).append(rptProperties.getProperty("C241M01C.ITEM"));
						if("B003".equals(c241m01c.getItemNo())){
							str.append(".").append(Util.trim(rptProperties.get("C241M01C.ITEMB003")));
						}else if("B009".equals(c241m01c.getItemNo())){
							str.append(".").append(Util.trim(rptProperties.get("C241M01C.ITEMB009" + Util.trim(c241m01c.getChkPreReview()))));
						}
						str.append(":").append(Util.trim(c241m01c.getChkText()));
					}
					
				}
				rptVariableMap = this.getCHKYN(
						Util.nullToSpace(c241m01c.getChkItem()),
						Util.nullToSpace(c241m01c.getChkResult()),
						Util.trim(c241m01c.getChkItem()),
						Util.nullToSpace(c241m01c.getItemNo()),
						Util.nullToSpace(c241m01c.getItemSeq()), rptVariableMap);
			}
		}else{
			List<CodeType> reviewTypeList = codetypeservice.findByCodeTypeList("lms2415s03_reviewType");
			for(CodeType reviewType : reviewTypeList){
				rptVariableMap = this.getCHKYN(
						Util.nullToSpace(reviewType.getCodeDesc()),
						"","",Util.nullToSpace(reviewType.getCodeValue()),
						Util.nullToSpace(reviewType.getCodeOrder()), rptVariableMap);
			}
		}
		rptVariableMap.put("C241M01C.ITEMTOTALTEXT", str.toString());
		return rptVariableMap;
	}

	/**
	 * 塞入變數MAP資料使用(C241M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01E
	 *            C241M01E資料資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01AForM01DData(
			Map<String, String> rptVariableMap, C241M01A c241m01a,Properties rptProperties) {
		if (c241m01a == null)
			c241m01a = new C241M01A();

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		
		if(Util.equals("", Util.nullToSpace(c241m01a.getRptId()))){
			rptVariableMap.put(
					"C241M01A.CONFLAG",
					Util.nullToSpace(rptProperties.getProperty("C241M01D.CONFLAG"
							+ c241m01a.getConFlag())));
			rptVariableMap.put("C241M01A.CONDITION",
					Util.nullToSpace(c241m01a.getCondition()));
		} else if(Util.equals(CrsUtil.V_201809,Util.nullToSpace(c241m01a.getRptId()))){	//J-107-0128海外改格式
			String first = "";
			String second = "";
			String CONFLAG1 = rptProperties.getProperty("C241M01D.CONFLAG1");
			String CONFLAG2 = rptProperties.getProperty("C241M01D.CONFLAG2");
			if(UtilConstants.Country.加拿大.equals(branchtype.getCountryType())
					&& Util.equals(LMSUtil.getLocale(),"en")){
				CONFLAG1 = rptProperties.getProperty("C241M01D.CONFLAG1_CA");
				CONFLAG2 = rptProperties.getProperty("C241M01D.CONFLAG2_CA");
			}
			if(Util.equals("1", c241m01a.getConFlag())){
				first = "■" + CONFLAG1;
				second = "□" + CONFLAG2;
			} else if (Util.equals("2", c241m01a.getConFlag())){
				first = "□" + CONFLAG1;
				second = "■" + CONFLAG2;
			} else {
				first = "□" + CONFLAG1;
				second = "□" + CONFLAG2;
			}
			if(UtilConstants.Country.加拿大.equals(branchtype.getCountryType())){
				rptVariableMap.put("C241M01A.CON", rptProperties.getProperty("label.1") + 
						rptProperties.getProperty("label.C_Rating") + 
						(Util.equals("0", Util.nullToSpace(c241m01a.getCurRate())) ? "" : 
							Util.nullToSpace(c241m01a.getCurRate())) + "　　" +
						rptProperties.getProperty("label.S_Rating") + 
						(Util.equals("0", Util.nullToSpace(c241m01a.getSugRate())) ? "" :
							Util.nullToSpace(c241m01a.getCurRate())) + "\n" + 
						rptProperties.getProperty("label.2") + first + "\n" + 
						rptProperties.getProperty("label.3") + second+ "\n" +
						rptProperties.getProperty("label.4") + rptProperties.getProperty("label.4con")); 
			} else {
				rptVariableMap.put("C241M01A.CON", rptProperties.getProperty("label.1") + first + 
						"\n" + rptProperties.getProperty("label.2") + second ); 
			}
		} else if(Util.equals(CrsUtil.V_202209,Util.nullToSpace(c241m01a.getRptId()))){	//J-111-0405海外改格式
			String first = "";
			String second = "";
			String CONFLAG1 = getProertiesByVer(rptProperties, "C241M01D.CONFLAG1", c241m01a.getRptId());
			String CONFLAG2 = getProertiesByVer(rptProperties, "C241M01D.CONFLAG2", c241m01a.getRptId());
			if(UtilConstants.Country.加拿大.equals(branchtype.getCountryType())
					&& Util.equals(LMSUtil.getLocale(),"en")){
				CONFLAG1 = rptProperties.getProperty("C241M01D.CONFLAG1_CA");
				CONFLAG2 = rptProperties.getProperty("C241M01D.CONFLAG2_CA");
			}
			if(Util.equals("1", c241m01a.getConFlag())){
				first = "■" + CONFLAG1;
				second = "□" + CONFLAG2;
			} else if (Util.equals("2", c241m01a.getConFlag())){
				first = "□" + CONFLAG1;
				second = "■" + CONFLAG2;
			} else {
				first = "□" + CONFLAG1;
				second = "□" + CONFLAG2;
			}
			if(UtilConstants.Country.加拿大.equals(branchtype.getCountryType())){
				StringBuilder sb = new StringBuilder();
				// 1.	No irregularities were found.
				sb.append(rptProperties.getProperty("label.1") + first + "\n");
				// 2.	Irregularities were found. Items for improvement or attention:
				sb.append(rptProperties.getProperty("label.2") + second + "\n");
				// 3.	Current rating:     Suggested rating:
				sb.append(rptProperties.getProperty("label.3") + 
				rptProperties.getProperty("label.C_Rating") + 
				(Util.equals("0", Util.nullToSpace(c241m01a.getCurRate())) ? "" : 
					Util.nullToSpace(c241m01a.getCurRate())) + "　　" +
				rptProperties.getProperty("label.S_Rating") + 
				(Util.equals("0", Util.nullToSpace(c241m01a.getSugRate())) ? "" :
					Util.nullToSpace(c241m01a.getCurRate())) + "\n");
				// 4.	Is it on the watch list?
				// No Yes(reason for no need to take immediate action to secure this credit:)
				sb.append(rptProperties.getProperty("label.4") + rptProperties.getProperty("label.4con"));
				rptVariableMap.put("C241M01A.CON", sb.toString()); 
			} else {
				rptVariableMap.put("C241M01A.CON", rptProperties.getProperty("label.1") + first + 
						"\n" + rptProperties.getProperty("label.2") + second ); 
			}
		}
		rptVariableMap.put("C241M01A.BRANCHCOMM",
				Util.nullToSpace(c241m01a.getBranchComm()));
		
		return rptVariableMap;
	}
	
	/**
	 * 塞入變數MAP資料使用(L120M01F)簽章欄
	 * 
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param L120M01F
	 *            L120M01F資料
	 * @return Map<String,String> rptVariableMap
	 */
	private Map<String, String> setC241M01EData(
			Map<String, String> rptVariableMap, List<C241M01E> c241m01eList) {
		String[] jobs = {"L1","L2","L3","L4","L5","L6"};
		for(int i = 1 ; i <=2 ; i++){
			for(String job : jobs){
				rptVariableMap.put("C241M01E." + i + "STAFFJOB" + job, "");
			}
		}
		for(C241M01E c241m01e : c241m01eList){
			rptVariableMap.put("C241M01E." + c241m01e.getBranchType() + "STAFFJOB" + c241m01e.getStaffJob(), Util.nullToSpace(userInfoService.getUserName(Util.nullToSpace(c241m01e.getStaffNo()))));
		}
		return rptVariableMap;
	}

	/**
	 * 取得複審項目的是否
	 * 
	 * @param chk
	 *            是否
	 * @param no
	 *            編號
	 * @param rptVariableMap
	 *            rptVariableMap
	 * @return Map<String,String>
	 */
	private Map<String, String> getCHKYN(String item, String chk, String text,
			String no, String seq, Map<String, String> rptVariableMap) {
		rptVariableMap.put("C241M01C.ITEM" + no.toUpperCase() + "SEQ", seq);
		rptVariableMap.put("C241M01C.ITEM" + no.toUpperCase() + "ITEM", item);
		rptVariableMap.put("C241M01C.ITEM" + no.toUpperCase() + "TEXT", text);
		if ("Y".equals(chk)) {
			rptVariableMap
					.put("C241M01C.ITEM" + no.toUpperCase() + "CHKY", "V");
			rptVariableMap.put("C241M01C.ITEM" + no.toUpperCase() + "CHKN", "");
		} else if ("N".equals(chk)) {
			rptVariableMap.put("C241M01C.ITEM" + no.toUpperCase() + "CHKY", "");
			rptVariableMap
					.put("C241M01C.ITEM" + no.toUpperCase() + "CHKN", "V");
		} else {
			rptVariableMap
					.put("C241M01C.ITEM" + no.toUpperCase() + "CHKY", "-");
			rptVariableMap
					.put("C241M01C.ITEM" + no.toUpperCase() + "CHKN", "-");
		}
		return rptVariableMap;
	}

	/**
	 * J-107-0128海外改格式
	 * 產生LMS1205R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception 
	 * @throws IOException 
	 * @throws FileNotFoundException 
	 */
	public OutputStream genLMS2415R01V2(String mainId, Locale locale,Properties rptProperties)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		ReportGenerator generator = new ReportGenerator(
				"report/crs/LMS2415R01_201809_" + locale.toString() + ".rpt");
		if(Util.equals(LMSUtil.getLocale(),"en") && 
				Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
			generator = new ReportGenerator(
					"report/crs/LMS2415R01_201809_CA_" + locale.toString() + ".rpt");
		}
		OutputStream outputStream = null;

		C241M01A C241m01a = null;
		List<C241M01C> C241m01cList = null;
		List<C241M01B> C241m01bList = null;
		List<C241M01E> C241m01eList = null;
		String branchName = null;
		Map<String, String> crdTypeMap = null;
		Map<String, String> quotaTypeMap = null;
		try {
			C241m01a = C241m01aDao.findByMainId(mainId);
			if(C241m01a == null){
				C241m01a = new C241M01A();
			}
			C241m01cList = C241m01cDao.findByMainId(mainId);
			C241m01bList = C241m01bDao.findByMainId(mainId);
			C241m01eList = C241m01eDao.findByMainId(mainId);
			crdTypeMap = codetypeservice.findByCodeType("CRDType");
			if (crdTypeMap == null) {
				crdTypeMap = new LinkedHashMap<String,String>();
			}
			quotaTypeMap = codetypeservice.findByCodeType("c241m01b_quotaType");
			if (quotaTypeMap == null) {
				quotaTypeMap = new LinkedHashMap<String,String>();
			}

			branchName = branch.getBranchName(Util.nullToSpace(C241m01a
					.getOwnBrId()));
			
			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00","");
			rptVariableMap.put("LOGOSHOW",logoPath );		
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			rptVariableMap.put("COUNTRYTYPE", branchtype.getCountryType());
			// 列印今天日期
			rptVariableMap.put("PRINTDAY", TWNDate.toAD(new Date()));
			//A一般	B土建
			rptVariableMap.put("DOCFMT", 
					(Util.equals("B", Util.nullToSpace(C241m01a.getDocFmt())) ? 
							rptProperties.getProperty("C241M01A.DOCFMTB") : "" ));
			rptVariableMap = this.setC241M01AData(rptVariableMap, C241m01a, rptProperties);
			rptVariableMap = this.setC241M01CData(rptVariableMap, C241m01cList,rptProperties, 
					Util.nullToSpace(C241m01a.getRptId()), locale, Util.nullToSpace(C241m01a.getDocFmt()));
			titleRows = this.setC241M01BData(titleRows, C241m01bList, true,quotaTypeMap);
			rptVariableMap = this.setC241M01BData(rptVariableMap, C241m01bList);
			rptVariableMap = this.setC241M01AForM01DData(rptVariableMap, C241m01a,rptProperties);
			rptVariableMap = this.setC241M01EData(rptVariableMap, C241m01eList);

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
			if (quotaTypeMap != null) {
				quotaTypeMap.clear();
			}
		}

		return outputStream;
	}
	
	/**
	 * J-111-0405海外改格式202209
	 * 產生LMS1205R01的PDF
	 * 
	 * @param mainId
	 *            mainId
	 * @param lang
	 *            語系
	 * @return outputstream outputstream
	 * @throws Exception 
	 * @throws IOException 
	 * @throws FileNotFoundException 
	 */
	public OutputStream genLMS2415R01V3(String mainId, Locale locale,Properties rptProperties)
			throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = new LinkedList<Map<String, String>>();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		ReportGenerator generator = new ReportGenerator(
				"report/crs/LMS2415R01_202209_" + locale.toString() + ".rpt");
		if(Util.equals(LMSUtil.getLocale(),"en") && 
				Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
			generator = new ReportGenerator(
					"report/crs/LMS2415R01_202209_CA_" + locale.toString() + ".rpt");
		}
		OutputStream outputStream = null;

		C241M01A C241m01a = null;
		List<C241M01C> C241m01cList = null;
		List<C241M01B> C241m01bList = null;
		List<C241M01E> C241m01eList = null;
		String branchName = null;
		Map<String, String> crdTypeMap = null;
		Map<String, String> quotaTypeMap = null;
		try {
			C241m01a = C241m01aDao.findByMainId(mainId);
			if(C241m01a == null){
				C241m01a = new C241M01A();
			}
			C241m01cList = C241m01cDao.findByMainId(mainId);
			C241m01bList = C241m01bDao.findByMainId(mainId);
			C241m01eList = C241m01eDao.findByMainId(mainId);
			crdTypeMap = codetypeservice.findByCodeType("CRDType");
			if (crdTypeMap == null) {
				crdTypeMap = new LinkedHashMap<String,String>();
			}
			quotaTypeMap = codetypeservice.findByCodeType("c241m01b_quotaType");
			if (quotaTypeMap == null) {
				quotaTypeMap = new LinkedHashMap<String,String>();
			}

			branchName = branch.getBranchName(Util.nullToSpace(C241m01a
					.getOwnBrId()));
			
			String logoPath = lmsService.getLogoShowPath(UtilConstants.RPTPicType.兆豐LOGO,"00","");
			rptVariableMap.put("LOGOSHOW",logoPath );		
			
			// 分行名稱
			rptVariableMap.put("BRANCHNAME", Util.nullToSpace(branchName));
			rptVariableMap.put("COUNTRYTYPE", branchtype.getCountryType());
			// 列印今天日期
			rptVariableMap.put("PRINTDAY", TWNDate.toAD(new Date()));
			//A一般	B土建
			rptVariableMap.put("DOCFMT", 
					(Util.equals("B", Util.nullToSpace(C241m01a.getDocFmt())) ? 
							rptProperties.getProperty("C241M01A.DOCFMTB") : "" ));
			rptVariableMap = this.setC241M01AData(rptVariableMap, C241m01a, rptProperties);
			rptVariableMap = this.setC241M01CData(rptVariableMap, C241m01cList,rptProperties, 
					Util.nullToSpace(C241m01a.getRptId()), locale, Util.nullToSpace(C241m01a.getDocFmt()));
			titleRows = this.setC241M01BData(titleRows, C241m01bList, true,quotaTypeMap);
			rptVariableMap = this.setC241M01BData(rptVariableMap, C241m01bList);
			rptVariableMap = this.setC241M01AForM01DData(rptVariableMap, C241m01a,rptProperties);
			rptVariableMap = this.setC241M01EData(rptVariableMap, C241m01eList);

			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);
			generator.setRowsData(titleRows);

			outputStream = generator.generateReport();
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
			if (crdTypeMap != null) {
				crdTypeMap.clear();
			}
			if (quotaTypeMap != null) {
				quotaTypeMap.clear();
			}
		}

		return outputStream;
	}
	
	/**
	 * 塞入變數MAP資料使用(C241M01B)
	 * 
	 * @param titleRows
	 *            存放變數list
	 * @param C241m01bList
	 *            C241M01B資料
	 * @param result
	 *            是否只顯示三筆
	 * @return List<Map<String, String>> titleRows
	 */
	private List<Map<String, String>> setC241M01BData(
			List<Map<String, String>> titleRows, List<C241M01B> C241m01bList,
			boolean result,Map<String,String> quotaTypeMap) {
		BigDecimal thu = new BigDecimal(1000);
		int count = 0;
		Map<String, String> map = null;
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "L");
		titleRows.add(map);
		for (C241M01B C241m01b : C241m01bList) {
			if (result && count >= 2) {
				count++;
			}else if (!result && count < 2) {
				count++;
			}else{
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				map.put("ReportBean.column02", Util.nullToSpace(C241m01b.getSubjectName()));
				map.put("ReportBean.column03", Util.nullToSpace(C241m01b.getQuotaNo()));
				map.put("ReportBean.column04", Util.nullToSpace(C241m01b.getQuotaCurr()));
				map.put("ReportBean.column05", NumConverter.addComma(C241m01b.getQuotaAmt() == null ? 
						BigDecimal.ZERO : C241m01b.getQuotaAmt().divide(thu, 0, 
								BigDecimal.ROUND_HALF_UP),"#,##0.00"));
				map.put("ReportBean.column06",
						Util.nullToSpace(TWNDate.toAD(C241m01b.getUseFDate())) + "~"
								+ Util.nullToSpace(TWNDate.toAD(C241m01b.getUseEDate())));
				
				map.put("ReportBean.column07",
						Util.nullToSpace(TWNDate.toAD(C241m01b.getLoanFDate())) + "~"
								+ Util.nullToSpace(TWNDate.toAD(C241m01b.getLoanEDate())));
				if((C241m01b.getLoanFDate()==null 
						|| Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(C241m01b.getLoanFDate())))
					&& (C241m01b.getLoanEDate()==null 
							|| Util.equals(CapDate.ZERO_DATE, TWNDate.toAD(C241m01b.getLoanEDate())))
					&& (Util.equals(("0001-01-01"+ "~"+"0001-01-01"), map.get("ReportBean.column07")))){
					map.put("ReportBean.column07", map.get("ReportBean.column06"));
				}
				map.put("ReportBean.column09",
						Util.trim(quotaTypeMap.get(Util.nullToSpace(C241m01b.getQuotaType()))));
				map.put("ReportBean.column10",
						Util.nullToSpace(C241m01b.getGuaranteeName()));
				map.put("ReportBean.column11", Util.nullToSpace(C241m01b.getMajorMemo()));
				map.put("ReportBean.column12", Util.nullToSpace(C241m01b.getEstCurr()));
				map.put("ReportBean.column13", NumConverter.addComma(C241m01b.getEstAmt() == null ? 
						BigDecimal.ZERO : C241m01b.getEstAmt().divide(thu, 0, 
								BigDecimal.ROUND_HALF_UP),"#,##0.00"));
				map.put("ReportBean.column14", Util.nullToSpace(C241m01b.getLoanCurr()));
				map.put("ReportBean.column15", NumConverter.addComma(C241m01b.getLoanAmt() == null ? 
						BigDecimal.ZERO : C241m01b.getLoanAmt().divide(thu, 0, 
								BigDecimal.ROUND_HALF_UP),"#,##0.00"));
				if(Util.isNotEmpty(C241m01b.getSBalCurr()) && C241m01b.getSBalAmt() != null){
					map.put("ReportBean.column16", Util.trim(C241m01b.getSBalCurr()));
					map.put("ReportBean.column17", NumConverter.addComma(C241m01b.getSBalAmt() == null ? 
							BigDecimal.ZERO : C241m01b.getSBalAmt().divide(thu, 0, 
									BigDecimal.ROUND_HALF_UP),"#,##0.00"));
				} else {
					map.put("ReportBean.column16", Util.trim(C241m01b.getBalCurr()));
					map.put("ReportBean.column17", NumConverter.addComma(C241m01b.getBalAmt() == null ? 
							BigDecimal.ZERO : C241m01b.getBalAmt().divide(thu, 0, 
									BigDecimal.ROUND_HALF_UP),"#,##0.00"));
				}
				titleRows.add(map);
				count++;
			}
		}
		if (result) {
			for(int i = count ; i < 2 ; i++){
				map = Util.setColumnMap();	
				map.put("ReportBean.column01", "Y");
				titleRows.add(map);
			}
		}
		map = Util.setColumnMap();
		map.put("ReportBean.column01", "N");
		titleRows.add(map);
		return titleRows;
	}
	
	/**
	 * 塞入變數MAP資料使用(C241M01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param C241M01C
	 *            C241M01C資料資料
	 * @return Map<String,String> rptVariableMap
	 */	
	private Map<String, String> setC241M01CData(
			Map<String, String> rptVariableMap, List<C241M01C> C241m01cList,Properties rptProperties, 
			String ver, Locale locale, String docFmt) {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		StringBuilder stab1 = new StringBuilder();
		ArrayList<String> strList = new ArrayList<String>();	//ITEMCONTENTS
		JSONObject typeJson = new JSONObject();

		if(C241m01cList.size() > 0){
			Map<String,C241M01C> map = new HashMap<String,C241M01C>();
			for (C241M01C i : C241m01cList) map.put(i.getItemNo(),i);
			// 10項：應敘明撥貸入借戶指定存款帳戶後，該存款帳戶所提領（含轉出或匯出等）該筆撥貸金額之資金流向是否與申貸用途相符之詳情，並影印資金流向資料存覆審卷備查。
			strList.add(getProertiesByVer(rptProperties, "C241M01C.v2ITEMB003", ver));
			
			for (C241M01C c241m01c : C241m01cList) {
				String itemType = c241m01c.getItemType();
				if(Util.equals("Z",itemType)||Util.equals("Y",itemType)||Util.equals("X",itemType)){
					continue;
				}
				if(Util.equals("A", docFmt) && (Util.equals("B007",c241m01c.getItemNo()))){
					continue;	//是否為土建融	否-->不用輸入B007
				}
				int count = Util.parseInt((String) typeJson.get(itemType));
				typeJson.put(itemType, String.valueOf(++count));
				
				int itemSeq = c241m01c.getItemSeq();
				if(itemSeq >= 14 && itemSeq < 17) {
					itemSeq = itemSeq-1;
			    } else if (itemSeq >= 17) {
			    	itemSeq = itemSeq-2;
			    }
				
				String chkText = StringEscapeUtils.escapeHtml(Util.trim(c241m01c.getChkText()));
				String item = "";
				if(Util.equals("en",locale)){
					item = rptProperties.getProperty("C241M01C.ITEM") + " " + itemSeq;
				} else {
					item = itemSeq + rptProperties.getProperty("C241M01C.ITEM");
				}
				
				if(Util.equals("B006",c241m01c.getItemNo())){	//13項
					C241M01C B007Data = map.get("B007");
					String B007_itemText = "";
					if(B007Data != null && Util.equals("B", docFmt)){
						if(Util.isNotEmpty(Util.trim(B007Data.getChkText()))){
							B007_itemText = (Util.isNotEmpty(chkText) ? "<br>" : "") 
								+ StringEscapeUtils.escapeHtml(
									Util.trim(B007Data.getChkText()));
						}
					}
					// C241M01C.v3ITEMB007=※第13項：土地融資及建築融資案件實地覆審結果，應勾選下列事項是否依核定條件辦理，若有不符者應說明情形。
					StringBuffer B007Memo = new StringBuffer("");
					ArrayList<String> ArrayX = new ArrayList<String>(Arrays.asList("X000", "X100", "X200", "X300"));
					for (String X_data : ArrayX) {
						C241M01C Data = map.get(X_data);
						String X_itemContent = Data.getChkItem();
						String result_fmt = "Y6|N6|K6";
						if(Util.equals("en", locale) && 
								Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
							result_fmt = "Y|N|K6";
						}
						X_itemContent = "　" + X_itemContent + " " + toBox(result_fmt, 
								rptProperties, Data.getChkResult(), 1);
						B007Memo.append(X_itemContent).append("<br>");
					}
					if(B007Data != null && Util.equals("B", docFmt)){
						// 土建融
						strList.add(getProertiesByVer(rptProperties, "C241M01C.v3ITEMB007", ver) + "<br>" + B007Memo.toString() + B007_itemText);
					} else {
						// 一般
						if(Util.isNotEmpty(chkText) || Util.isNotEmpty(B007_itemText)){
							strList.add( item + "：" + chkText + B007_itemText);
						}
					}
				} else if(Util.equals("B007",c241m01c.getItemNo())) {	//於B006顯示
				} else if(Util.equals("B009",c241m01c.getItemNo())){	//15項
					C241M01C Z000Data = map.get("Z000");
					C241M01C Z100Data = map.get("Z100");
					String Z000_itemContent = "";
					String Z100_itemContent = "";
					String item_chkText = "";
					if(Z000Data != null){
						Z000_itemContent = StringUtils.replace(Z000Data.getChkItem(), "有無", " " + 
								toBox("Y2|N2", rptProperties, Z000Data.getChkResult(), 1) + " ");
					}
					if(Z100Data != null){
						if(Util.equals("en", locale) && 
								Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
							Z100_itemContent = "　" + Z100Data.getChkItem() + "<br>　" + toBox("Y|N", 
									rptProperties, Z100Data.getChkResult(), 1) + 
									rptProperties.getProperty("C241M01a.info01_CA");
							item_chkText = "(" + rptProperties.getProperty("label.chkText02_CA") + 
								StringEscapeUtils.escapeHtml(Util.trim(Z100Data.getChkText())) + ")";
						} else {
							Z100_itemContent = "　" + Z100Data.getChkItem() + "<br>　" + toBox("Y|N", 
									rptProperties, Z100Data.getChkResult(), 1) + 
									rptProperties.getProperty("C241M01a.info01");
							item_chkText = "(" + rptProperties.getProperty("label.chkText02") + 
								StringEscapeUtils.escapeHtml(Util.trim(Z100Data.getChkText())) + ")";
						}
					}
					C241M01C B010Data = map.get("B010");
					String B010_itemText = "";
					if(B010Data != null){
						if(Util.isNotEmpty(Util.trim(B010Data.getChkText()))){
							B010_itemText = "<br>" + StringEscapeUtils.escapeHtml(
									Util.trim(B010Data.getChkText()));
						}
					}
					strList.add(rptProperties.getProperty("label.STAR") + item + "：" + 
							Z000_itemContent + "<br>" + Z100_itemContent + "<br>" + item_chkText 
							+ B010_itemText);
				} else if(Util.equals("B010",c241m01c.getItemNo())){	//於B009顯示
				} else if(Util.equals("C001",c241m01c.getItemNo())){	//23項
					StringBuffer B009Memo = new StringBuffer("");
					ArrayList<String> ArrayY = new ArrayList<String>(Arrays.asList("Y000","Y100","Y110",
							"Y111","Y112","Y120","Y121","Y122","Y123","Y124","Y125","Y200","Y210","Y211",
							"Y212","Y213", "Y220", "Y22A", "Y221", "Y222"));
					ArrayList<String> chk_3 = new ArrayList<String>(Arrays.asList("Y125", 
							"Y211", "Y212", "Y213"));	//是 否 －
					ArrayList<String> chk_2 = new ArrayList<String>(Arrays.asList("Y111", "Y112", "Y121", 
							"Y122",	"Y123", "Y124", "Y221"));	//是 否
					for (String Y_data : ArrayY) {
						C241M01C Data = map.get(Y_data);
						String Y_itemContent = Data.getChkItem();
						if(chk_3.contains(Y_data)){
							if(Util.equals("Y125",Y_data)){
								Y_itemContent = "　" + Y_itemContent + " " + toBox("Y4|N4|K5", 
										rptProperties, Data.getChkResult(), 1);
							} else {
								Y_itemContent = "　" + Y_itemContent + " " + toBox("Y|N|K5", 
										rptProperties, Data.getChkResult(), 1);
							}
							B009Memo.append(Y_itemContent).append("<br>");
						} else if(chk_2.contains(Y_data)){
							Y_itemContent = "　" + Y_itemContent + " " + toBox("Y|N", 
									rptProperties, Data.getChkResult(), 1);
							B009Memo.append(Y_itemContent).append("<br>");
						} else if(Util.equals("Y120",Y_data)){
							Y_itemContent = Y_itemContent + "<br>" 
								+ getProertiesByVer(rptProperties, "C241M01a.info02", ver) + "<br>" ;
							B009Memo.append(Y_itemContent).append("<br>");
						} else if(Util.equals("Y22A",Y_data)){
							String Y22A_itemContent = StringUtils.replace(Data.getChkItem(), "有無", " " + 
									toBox("Y3|N3", rptProperties, Data.getChkResult(), 1) + " ");
							B009Memo.append(Y22A_itemContent).append("<br>");
						} else {
							B009Memo.append(Data.getChkItem()).append("<br>");
						}					
					}
					strList.add(rptProperties.getProperty("label.STAR") + item + "：" +	B009Memo);
				} else {
					if(Util.isNotEmpty(chkText)){
						strList.add( item + "：" + chkText);
					}
				}
			}
			int itemRows = typeJson.getInt("A") + typeJson.getInt("B") + typeJson.getInt("C") ;
			
			String width_覆審項目標題 = "80";
			String width_覆審結果標題 = "20";
			String width_項目種類垂直標題 = "2";
			String width_項目編號 = "2";
			String width_項目內容 = "76";
			String width_結果是 = "3";
			String width_結果是勾選 = "3";
			String width_結果否 = "3";
			String width_結果否勾選 = "3";
			String width_結果說明 = "20";
			// J-111-0405 更動覆審系統內以下15式覆審報告表
			// 文字變動，格子寬度調整
			if(Util.equals(CrsUtil.V_202209,Util.nullToSpace(ver))){
				// 不設定表頭寬度，由其他各TD決定寬度，全部加總要=100
				width_覆審項目標題 = "";
				width_覆審結果標題 = "";
				// 覆審項目標題底下的各TD寬度
				width_項目種類垂直標題 = "7";
				width_項目編號 = "2";
				width_項目內容 = "41";
				// 覆審結果標題底下的各TD寬度
				width_結果是 = "2";
				width_結果是勾選 = "2";
				width_結果否 = "2";
				width_結果否勾選 = "2";
				width_結果說明 = "42";
			}

			// Creat stab1 HTML CODE
			stab1.append("<style> table,td { border: 1px solid black; border-collapse: collapse;} </style>");
			stab1.append("<table border=\"1px\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border: 1px #000000 solid; \">");
			stab1.append("<tbody>");
			stab1.append("<tr height=\"1px\">");
			stab1.append("<td cellpadding=\"0\" cellspacing=\"0\" colspan=\"3\" style=\"width: " + width_覆審項目標題 + "%; text-align: center;border: 1px solid rgb(0, 0, 0);\">");
			stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE01") + "</span>");
			stab1.append("</td>");
			stab1.append("<td colspan=\"5\" style=\"width: " + width_覆審結果標題 + "%; border: 1px solid rgb(0, 0, 0); text-align: center;\">");
			stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE02") + "</span>");
			stab1.append("</td>");
			stab1.append("</tr>");
			
			int countTable = 0, countType = 0;
			String nowType="";
			for(C241M01C c241m01c : C241m01cList){
				String itemType = c241m01c.getItemType();
				if(Util.equals("Z",itemType)||Util.equals("Y",itemType)||Util.equals("X",itemType)){
					continue;
				}
				if(Util.equals("A", docFmt) && Util.equals("B007",c241m01c.getItemNo())){
					continue;	//是否為土建融	否-->不用輸入B007
				}
				int itemSeq = c241m01c.getItemSeq();
				if(itemSeq >= 14 && itemSeq < 17) {
					itemSeq = itemSeq-1;
			    } else if (itemSeq >= 17) {
			    	itemSeq = itemSeq-2;
			    }
				
				if(countTable==0){
					stab1.append("<tr height=\"1px\">");
					countTable++;
				} else {
					stab1.append("<tr>");
				}
				
				if(Util.equals("", nowType) || Util.notEquals(nowType, itemType)){
					nowType = itemType;
					stab1.append("<td rowspan=\"" + typeJson.getInt(itemType) +  "\" style=\"text-align:center;border: 1px solid rgb(0, 0, 0); width: " + width_項目種類垂直標題 + "%; vertical-align: middle;text-align: center;\">");
					if(Util.equals("A", itemType)){
						if(Util.equals("en", locale) && Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
							stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE03_CA") + "</span>");
						} else {
							stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE03") + "</span>");
						}
					} else if(Util.equals("B", itemType)) {
						if(Util.equals("en", locale) && Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
							stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE04_CA") + "</span>");
						} else {
							stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE04") + "</span>");
						}
					} else if(Util.equals("C", itemType)) {
						stab1.append("<span style=\"display: inline-block; width: 100%;\">" + rptProperties.getProperty("TITLE.RPTTITLE05") + "</span>");
					}
					stab1.append("</td>");
				}
				
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目編號 + "%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目編號 + "%; vertical-align: middle;text-align: center;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目編號 + "%; vertical-align: middle;text-align: center;\">");
	            }
				if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<span style=\"display: inline-block; width: 100%;\"></span>");
				} else {
					stab1.append("<span style=\"display: inline-block; width: 100%;\">" + itemSeq + "</span>");
				}
				stab1.append("</td>");
				
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目內容 + "%; vertical-align: middle;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目內容 + "%; vertical-align: middle;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_項目內容 + "%; vertical-align: middle;\">");
	            }
				stab1.append("<span style=\"display: inline-block; width: 100%;\">" + c241m01c.getChkItem() + "</span>");
				stab1.append("</td>");
				
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){				
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是 + "%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是 + "%; vertical-align: middle;text-align: center;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是 + "%; vertical-align: middle;text-align: center;\">");
	            }
				String ra = "";
				String r1 = "";
				String rb = "";
				String r2 = "";
				if(Util.equals("B011",c241m01c.getItemNo())){	// 前次覆審有無應行改善事項？ 【無|有|－】
					if(Util.equals("en",locale)){
						ra = rptProperties.getProperty("label.N");
						rb = rptProperties.getProperty("label.Y");
					} else {
						ra = rptProperties.getProperty("label.N2");
						rb = rptProperties.getProperty("label.Y2");
					}
					if(Util.equals("N",c241m01c.getChkResult())){
						r1 = "V";
					} else if(Util.equals("Y",c241m01c.getChkResult())){
						r2 = "V";
					} else if(Util.equals("K",c241m01c.getChkResult())){
						r1 = "&ndash;";		//-
						r2 = "&ndash;";
					}
				} else if(Util.equals("B013",c241m01c.getItemNo()) || 
						Util.equals("C005",c241m01c.getItemNo())){	// 擔保物是否發生變化，致影響本行債權？【否|是|－】
					ra = rptProperties.getProperty("label.N");
					rb = rptProperties.getProperty("label.Y");
					if(Util.equals("N",c241m01c.getChkResult())){
						r1 = "V";
					} else if(Util.equals("Y",c241m01c.getChkResult())){
						r2 = "V";
					} else if(Util.equals("K",c241m01c.getChkResult())){
						r1 = "&ndash;";		//-
						r2 = "&ndash;";
					}
				} else {
					ra = rptProperties.getProperty("label.Y");
					rb = rptProperties.getProperty("label.N");
					if(Util.equals("Y",c241m01c.getChkResult())){
						r1 = "V";
					} else if(Util.equals("N",c241m01c.getChkResult())){
						r2 = "V";
					} else if(Util.equals("K",c241m01c.getChkResult())){
						r1 = "&ndash;";		//-
						r2 = "&ndash;";
					}
				}
				stab1.append("<span>" + ra + "</span>");
				stab1.append("</td>");
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){				
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是勾選 + "%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是勾選 + "%; vertical-align: middle;text-align: center;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果是勾選 + "%; vertical-align: middle;text-align: center;\">");
	            }
				stab1.append("<span>" + r1 + "</span>");
				stab1.append("</td>");
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){				
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否 + "%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否 + "%; vertical-align: middle;text-align: center;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否 + "%; vertical-align: middle;text-align: center;\">");
	            }
				stab1.append("<span>" + rb + "</span>");
				stab1.append("</td>");
				if(Util.equals(13, c241m01c.getItemSeq()) || Util.equals(16, c241m01c.getItemSeq())){				
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否勾選 + "%; vertical-align: middle;text-align: center;border-bottom-style:none;\">");
				} else if (Util.equals(14, c241m01c.getItemSeq()) || Util.equals(17, c241m01c.getItemSeq())){
					stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否勾選 + "%; vertical-align: middle;text-align: center;border-top-style:none;\">");
	            } else {
	    			stab1.append("<td style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果否勾選 + "%; vertical-align: middle;text-align: center;\">");
	            }
				stab1.append("<span>" + r2 + "</span>");
				stab1.append("</td>");
				
				if(Util.equals("A" ,itemType) && countType==0){
					stab1.append("<td rowspan=\"" + itemRows + "\" style=\"border: 1px solid rgb(0, 0, 0); width: " + width_結果說明 + "%; vertical-align: top;\">");
					stab1.append("<span>" + StringUtils.join(strList, "<br><br>") + "</span>");
					stab1.append("</td>");
					countType++;
				}
			}
		}	
		
		stab1.append("</tr>");
		stab1.append("</tbody>");
		stab1.append("</table>");
		rptVariableMap.put("strTable", stab1.toString());
		return rptVariableMap;
	}
	
	private String toBox(String result_fmt, Properties rptProperties, String s, int spaceLen) {
		String addStr = Util.addSpaceWithValue("", spaceLen);

		String[] valSplit = Util.trim(result_fmt).split("\\|");
		if (valSplit == null || valSplit.length == 0) {
			return s;
		} else {
			List<String> r = new ArrayList<String>();
			for (String val : valSplit) {
				String showStr = (Util.equals(s, Util.getLeftStr(val, 1)) ? "■" : "□")
						+ " " + rptProperties.getProperty("label." + val);
				r.add(showStr);
			}
			return StringUtils.join(r, addStr);
		}
	}
	
	/**
	 * 依據版本ID抓取對應值
	 * @param rptProperties
	 * @param key
	 * @param rptId
	 * @return
	 */
	private String getProertiesByVer(Properties rptProperties, String key, String rptId) {
		Locale locale = LMSUtil.getLocale();
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		IBranch branchtype = branch.getBranch(user.getUnitNo());
		String endWith = "";
		if(Util.equals("en", locale) && 
				Util.equals(UtilConstants.Country.加拿大,branchtype.getCountryType())){
			endWith = "_CA";
		}
		String text = rptProperties.getProperty(key + endWith + "." + rptId, "");
		if (text.isEmpty()) {
			text = rptProperties.getProperty(key + endWith, "");
		}
		if (text.isEmpty()) {
			text = rptProperties.getProperty(key, "");
		}
		return text;
	}
}
