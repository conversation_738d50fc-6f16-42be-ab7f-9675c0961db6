package com.mega.eloan.lms.model;

import java.math.BigDecimal;

import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * 財務三表:資產 損益 現流 - The persistent class for the F101S01A database table.
 * </pre>
 * 
 * @since 2011/7/26
 * <AUTHOR> Wang
 * @version <ul>
 *          <li>2011/7/26,Sun<PERSON><PERSON> Wang,new</li>
 *          <li>2011/7/29,Sun<PERSON><PERSON> Wang,update rename pidF101M04A</li>
 *          </ul>
 */
@Generated(value = "Dali", date = "2011-07-26T10:41:48.218+0800")
@StaticMetamodel(F101S01A.class)
public class F101S01A_ extends RelativeMeta_ {
	public static volatile SingularAttribute<F101S01A, BigDecimal> amt;
	public static volatile SingularAttribute<F101S01A, String> pidF101M04A;
	public static volatile SingularAttribute<F101S01A, String> quickflag;
	public static volatile SingularAttribute<F101S01A, BigDecimal> ratio;
	public static volatile SingularAttribute<F101S01A, String> subName;
	public static volatile SingularAttribute<F101S01A, String> subNo;
	public static volatile SingularAttribute<F101S01A, String> tab;
	public static volatile SingularAttribute<F101S01A, F101M01A> f101m01a;
}
