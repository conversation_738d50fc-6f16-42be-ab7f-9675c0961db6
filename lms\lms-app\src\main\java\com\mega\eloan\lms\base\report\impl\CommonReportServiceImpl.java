package com.mega.eloan.lms.base.report.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.ReportGenerator;

import com.mega.eloan.common.model.CodeType;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.report.CommonReportService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.dao.L140M01ADao;
import com.mega.eloan.lms.dao.L140M01XDao;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01M;
import com.mega.eloan.lms.model.L140M01X;

@Service
public class CommonReportServiceImpl implements CommonReportService{
	
	@Resource
	LMSService lmsService;
	@Resource
	CodeTypeService codetypeservice;
	
	@Resource
	L140M01XDao l140m01xDao;
	@Resource
	L140M01ADao l140m01aDao;
	
	static final String spacebox = "□";
	static final String nspacebox = "■";
	
	/**
	 * 列印購置高價住宅貸款檢核表
	 * 
	 * @param mainId
	 * @param locale
	 * @param oid
	 * @param prop
	 * @return
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws Exception
	 */
	@Override
	public OutputStream genCLS1151R06ForHighPricedHouseCheckList(String l120m01a_MainId, Locale locale, String l140m01a_Oid) throws FileNotFoundException, IOException, Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		ReportGenerator generator = new ReportGenerator("report/cls/CLS1151R06_" + locale.toString() + ".rpt");
		OutputStream outputStream = null;

		L120M01A l120m01a = null;
		// L140M01A．額度明細表主檔
		L140M01A l140m01a = null;

		try {
			l120m01a = lmsService.findModelByMainId(L120M01A.class, l120m01a_MainId);
			l120m01a = l120m01a == null ? new L120M01A() : l120m01a;
			l140m01a = l140m01aDao.findByOid(l140m01a_Oid);
			
			if(l140m01a == null){
				return null;
			}

			L140M01M l140m01m = lmsService.findModelByMainId(L140M01M.class, l140m01a.getMainId());
			
			if(l140m01m == null){
				return null;
			}
			
			L140M01X l140m01x = l140m01xDao.findByUniqueKey(l140m01m.getMainId(), l140m01a.getCustId(), l140m01a.getDupNo());
			String isIsHighHouse = Util.nullToSpace(l140m01m.getIsHighHouse());
			
			// 非高價住宅
			if("N".equals(isIsHighHouse) || null == l140m01x){
				return null;
			}
			
			// J-108-0097 購置高價住宅貸款檢核表 ****************************************
			this.setHighPricedHouseCheckListReportParameter(l140m01m.getMainId(), l140m01a.getCustId(), l140m01a.getCustName(), 
																Integer.parseInt(l120m01a.getDocType()), l140m01x, rptVariableMap, 
																l140m01a.getCaseDate(), l140m01a.getCaseNo(), l140m01a.getCntrNo(), 
																l140m01a.getRandomCode(), l140m01m.getVersion());
			generator.setLang(locale);
			generator.setVariableData(rptVariableMap);

			outputStream = generator.generateReport();
			
		} finally {
			if (rptVariableMap != null) {
				rptVariableMap.clear();
			}
		}
		return outputStream;
	}
	
	// J-108-0097 購置高價住宅貸款檢核表
	private void setHighPricedHouseCheckListReportParameter(String l140m01mMainId, String custId, String custName, int docType, 
																L140M01X l140m01x, Map<String, String> rptVariableMap, Date caseDate, 
																String caseNo, String cntrNo, String randomCode, String l140m01mVersion){
		
		Properties prop = MessageBundleScriptCreator.getComponentResource(this.getClass());
		
		Map<String, String> i18nMap = new HashMap<String, String>();
		i18nMap.put("Y", prop.getProperty("yes"));
		i18nMap.put("N", prop.getProperty("no"));
		i18nMap.put("X", prop.getProperty("notApplicable"));
		
		List<String> codeList = new ArrayList<String>();
		List<CodeType> ynxList = codetypeservice.findByCodeTypeList("YNX");
		for(int i=0; i<ynxList.size(); i++){
			
			CodeType codeType = ynxList.get(i);
			if(codeType.getCodeOrder() == i+1){
				codeList.add(codeType.getCodeValue());
			}
		}

		List<Map<String, String>> coLenderList = lmsService.getCoLenderForCorporateOrPersonalFinance(docType, l140m01mMainId);
		String lender = "";
		for(Map<String, String> map : coLenderList){
			lender += map.get("custId") + " " + map.get("custName") + "、";
		}
		
		rptVariableMap.put("L140M01A.CASEDATE", Util.nullToSpace(TWNDate.toAD(caseDate)));
		rptVariableMap.put("L140M01A.CASENO", caseNo);
		rptVariableMap.put("L140M01A.CNTRNO", cntrNo);
		rptVariableMap.put("L140M01A.RANDOMCODE", randomCode);
		rptVariableMap.put("L140M01X.mainLender", custId + " " + custName);//主借款人
		rptVariableMap.put("L140M01X.coLender", lender.length() > 0 ? lender.substring(0, lender.length()-1) : lender);//共同借款人
		rptVariableMap.put("L140M01X.isGracePeriod", this.getCheckResultString(codeList, l140m01x.getIsGracePeriod(), i18nMap));//本案是否無寬限期
		rptVariableMap.put("L140M01X.isGtSpecificInterest", this.getCheckResultString(codeList, l140m01x.getIsGtSpecificInterest(), i18nMap));//本案利率未低於6R+0.88%。(詳消金利率底限彙總表)
		rptVariableMap.put("L140M01X.isCalculatedAtLowerAmount", this.getCheckResultString(codeList, l140m01x.getIsCalculatedAtLowerAmount(), i18nMap));//貸款額度是否以高價住宅鑑價金額或買賣金額較低者之6成核計
		rptVariableMap.put("L140M01X.dealAmount", NumConverter.addComma(l140m01x.getDealAmount() == null ? "" : l140m01x.getDealAmount()));//買賣金額
		rptVariableMap.put("L140M01X.appraisePrice", NumConverter.addComma(l140m01x.getAppraisePrice() == null ? "" : l140m01x.getAppraisePrice()));//鑑估價格
		rptVariableMap.put("L140M01X.approvedLoanQuota", NumConverter.addComma(l140m01x.getApprovedLoanQuota() == null ? "" : l140m01x.getApprovedLoanQuota()));//本案核貸額度
		rptVariableMap.put("L140M01X.isNewOrAddLoan", this.getCheckResultString(codeList, l140m01x.getIsNewOrAddLoan(), i18nMap));//申貸約當期間是否有另案增貸或新貸案件額度	
		rptVariableMap.put("L140M01X.newOrAddLoanQuota", NumConverter.addComma(l140m01x.getNewOrAddLoanQuota() == null ? "" : l140m01x.getNewOrAddLoanQuota()));//增貸或新貸案件額度	
		rptVariableMap.put("L140M01X.isExistingLoan", this.getCheckResultString(codeList, l140m01x.getIsExistingLoan(), i18nMap));//是否有其他既有貸款
		rptVariableMap.put("L140M01X.existingLoanQuota", NumConverter.addComma(l140m01x.getExistingLoanQuota() == null ? "" : l140m01x.getExistingLoanQuota()));//既有貸款額度 
		rptVariableMap.put("L140M01X.isOtherAgencyLoan", this.getCheckResultString(codeList, l140m01x.getIsOtherAgencyLoan(), i18nMap));//是否有其他金融機構貸款
		rptVariableMap.put("L140M01X.otherAgencyLoanQuota", NumConverter.addComma(l140m01x.getOtherAgencyLoanQuota() == null ? "" : l140m01x.getOtherAgencyLoanQuota()));//其他金融機構貸款額度
		rptVariableMap.put("L140M01X.isDepositPledgedLoans", this.getCheckResultString(codeList, l140m01x.getIsDepositPledgedLoans(), i18nMap));//是否提供十成存款設質之貸款
		rptVariableMap.put("L140M01X.isLeOriginalLoanQuota", this.getCheckResultString(codeList, l140m01x.getIsLeOriginalLoanQuota(), i18nMap));//是否小於原貸額度
		rptVariableMap.put("L140M01X.isNotSpecificProjectCommit", this.getCheckResultString(codeList, l140m01x.getIsNotSpecificProjectCommit(), i18nMap));//是否非行家理財-短期擔保案件或循環額度承做
		rptVariableMap.put("L140M01X.isExplainToLender", this.getCheckResultString(codeList, l140m01x.getIsExplainToLender(), i18nMap));//是否有向借戶說明貸款須知
		rptVariableMap.put("L140M01X.description", l140m01x.getDescription());//其他事項說明
		rptVariableMap.put("L140M01M.VERSION", l140m01mVersion);
		rptVariableMap.put("L140M01X.isShareAcquisition", this.getCheckResultString(codeList, l140m01x.getIsShareAcquisition(), i18nMap));//本案資金用途是否屬股權收購
	}
	
	/**
	 * J-108-0097 購置高價住宅貸款檢核表 "檢核結果"欄位文字□是□否□不適用
	 * 
	 * @param type
	 *            Y=是 N=否 X=空白
	 * @param prop
	 *            prop
	 * @return string
	 */
	private String getCheckResultString(List<String> codeList, String ynxContent, Map<String, String> i18nMap) {

		String str = "";
		for(String ynx : codeList){
			str += ynx.equals(ynxContent) ? CommonReportServiceImpl.nspacebox : CommonReportServiceImpl.spacebox;
			str += i18nMap.get(ynx) + " ";
		}
		
		return str;
	}

}
