package com.mega.eloan.lms.eloandb.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import tw.com.iisi.cap.context.CapParameter;

import com.mega.eloan.lms.eloandb.service.EloandbFsBASEService;

@Service("eloandbFsBASEService")
public class EloandbFsBASEServiceImpl extends AbstractEloandbFsJdbc implements
		EloandbFsBASEService {

	@Resource(name = "eLoanFsSqlStatement")
	CapParameter sqlp;

	public int update(String sql) {
		return this.getJdbc().update(sql, new Object[] {});
	}

	@Override
	public int update(String sql, Object... objects) {
		return this.getJdbc().update(sql, objects);
	}

	// 取得取得簽報書敘述說明檔
	@Override
	public List<Map<String, Object>> findL120m01d(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL120m01d",
				new String[] { bgnDate, endDate });
	}

	// 取得額度明細表敘述說明檔
	@Override
	public List<Map<String, Object>> findL140m01b(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL140m01b",
				new String[] { bgnDate, endDate });
	}

	// 取得借款人基本資料
	@Override
	public List<Map<String, Object>> findL120s01ab(String bgnDate,
			String endDate) {
		return this.getJdbc().queryForListWithMax("selL120s01ab",
				new String[] { bgnDate, endDate });
	}

	// 取得額度明細表資料
	@Override
	public List<Map<String, Object>> findL140m01a(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL140m01a",
				new String[] { bgnDate, endDate });
	}

	// 取得異常通報資料
	@Override
	public List<Map<String, Object>> findL130m01a(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL130m01a",
				new String[] { bgnDate, endDate });
	}

	// 取得簽報書主檔資料
	@Override
	public List<Map<String, Object>> findL120m01a(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL120m01a",
				new String[] { bgnDate, endDate });
	}

	// 取得會議決議檔資料
	@Override
	public List<Map<String, Object>> findL120m01h(String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax("selL120m01h",
				new String[] { bgnDate, endDate });
	}

	// 依照SQLNAME 取得資料
	@Override
	public List<Map<String, Object>> findDataListBySqlName(String sqlName,
			String bgnDate, String endDate) {
		return this.getJdbc().queryForListWithMax(sqlName,
				new String[] { bgnDate, endDate });
	}

}
