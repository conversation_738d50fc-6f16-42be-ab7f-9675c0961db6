/* 
 * C240M01ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C240M01A;
import com.mega.eloan.lms.model.L180M01A;

/** 覆審工作底稿主檔 **/
public interface C240M01ADao extends IGenericDao<C240M01A> {

	C240M01A findByOid(String oid);

	C240M01A findByMainId(String mainId);

	List<C240M01A> findByDocStatus(String docStatus);

	List<Object[]> findMaxbatchNO();

	List<C240M01A> findByBranchAndDataDate(String branchId, Date dataDate);

	List<C240M01A> findMaxDataDate(String branch, Date retrialDate);

	List<C240M01A> findByBranchIdAndLikeMainId(String branch, String likeMainId);

	List<C240M01A> findByDefaultCTLDate(String docStatus, String bgnDate,
			String endDate);

}