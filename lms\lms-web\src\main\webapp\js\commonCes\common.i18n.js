// i18n plugin
;
(function($) {
  var s = {};
  window['i18n'] = {
    getBundle : function(f) {
      return $.ajax({
        url : encodeURI(window.location.href),
        headers : {
            'X-CSRF-TOKEN' : $("meta[name='_csrf']").attr("content")
        },
        type : 'POST',
        async : false,
        cache : false,
        dataType : 'json',
        data : {
          _pa : 'i18nhandler',
          formAction : "getI18n",
          f : encodeURI(f)
        }
      }).done(function(res) {
        // i18n.set("def", JSON.parse(res));
        i18n.set(f.match("\\w+$")[0], res);
      });
    },
    set : function(key, jsonValue) {
      $.extend(window['i18n'][key] = (function(value) {
        return function(key, values) {
          var msg = value[key];
          msg && values && $.each(values, function(i, v) {
            msg = msg.replace(new RegExp("\\${" + i + "\\}", "g"), v);
          });
          return msg;
        };
      })(jsonValue), jsonValue);
    },
    reset : function() {
      for ( var key in window['i18n']) {
        if (key == 'load' || key == 'set' || key == 'reset' || key == 'setup' || key == 'def') {
          continue;
        } else {
          window['i18n'][key] = null;
        }
      }
    },
    setup : function(settings) {
      s = $.extend(true, s, settings);
    }
  };
  window.i18n.getBundle('def');
  
  /**
   * fix checkmarx Client JQuery Deprecated Symbols
   */
  window.trimStr = String.prototype.trim ? 
    // Use native String.trim function wherever possible
    function(s){
      return s == null ? "" : String.prototype.trim.call(s);
    } : 
    // Otherwise use our own trimming functionality
    function(s){
      return s == null ? "" : s.toString().replace(/^\s+/, "").replace(/\s+$/, "");
    }
  console.log("i18njs init");
})(jQuery);