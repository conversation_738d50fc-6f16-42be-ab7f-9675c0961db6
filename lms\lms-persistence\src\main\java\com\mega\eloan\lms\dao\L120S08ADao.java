/* 
 * L120S08ADao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L120S08A;

/** 利率定價核理性分析表主檔 **/
public interface L120S08ADao extends IGenericDao<L120S08A> {

	L120S08A findByOid(String oid);

	List<L120S08A> findByMainId(String mainId);

	List<L120S08A> findByIndex01(String mainId);

	List<L120S08A> findByMainIdCurr(String mainId, String curr);

	public List<L120S08A> findL120s08aListByOids(String[] oids);

	public List<L120S08A> findByMainIdCurrCustIdPrintGroup(String mainId,
			String curr, String custId, String dupNo, BigDecimal printGroup);

	public L120S08A findMaxSeqNoByMainIdAndCurr(String mainId, String curr);

	public List<L120S08A> findByMainIdAndVersionDate(String mainId,
			String versionDate);

	public List<L120S08A> findByMainIdCustIdPrintGroup(String mainId,
			String custId, String dupNo, BigDecimal printGroup);
	
	public L120S08A findByMainIdCustIdPrintGroupCurr(String mainId,
			String custId, String dupNo, BigDecimal printGroup, String curr);
}