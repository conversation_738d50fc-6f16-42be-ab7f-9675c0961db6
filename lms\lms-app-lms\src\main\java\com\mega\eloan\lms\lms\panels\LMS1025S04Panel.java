package com.mega.eloan.lms.lms.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.base.common.OverSeaUtil;

import tw.com.jcs.common.Util;

/**
 * <pre>
 * Veda Report 資訊
 * </pre>
 * 
 * @since 2015/3/1
 * <AUTHOR>
 * @version <ul>
 *          <li>2015/3/1,EL08034,new
 *          </ul>
 */
public class LMS1025S04Panel extends Panel {
	private static final long serialVersionUID = 1L;

	private String varVer;

	public LMS1025S04Panel(String id) {
		super(id);
	}

	public LMS1025S04Panel(String id, boolean updatePanelName, String varVer) {
		super(id, updatePanelName);
		this.varVer = varVer;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);
		//title、內容共用
		boolean v1_0 = false; 
		boolean v3_0 = false;
		if(Util.equals(varVer, OverSeaUtil.V3_0_LOAN_AU)){
			v3_0 = true;
		}else{
			v1_0 = true;
		}
		
		model.addAttribute("FACTOR_TITLE_V1_0", v1_0);
		model.addAttribute("FACTOR_TITLE_V3_0", v3_0);
	}
}
