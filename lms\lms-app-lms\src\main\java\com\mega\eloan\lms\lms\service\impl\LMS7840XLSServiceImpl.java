package com.mega.eloan.lms.lms.service.impl;

import java.awt.AlphaComposite;
import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.kordamp.json.JSONException;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.lms.base.common.Html2Text;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.common.LmsExcelUtil;
import com.mega.eloan.lms.base.constants.UtilConstants.Casedoc;
import com.mega.eloan.lms.base.pages.LMSCommomPage;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.ProdService.ProdKindEnum;
import com.mega.eloan.lms.dao.L120M01ADao;
import com.mega.eloan.lms.dao.L120M01CDao;
import com.mega.eloan.lms.dao.L120S01ADao;
import com.mega.eloan.lms.dao.L120S01CDao;
import com.mega.eloan.lms.dao.L140M01ATMP1Dao;
import com.mega.eloan.lms.dao.L140S02ADao;
import com.mega.eloan.lms.lms.pages.LMS7840M01Page;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lms.service.LMS1205Service;
import com.mega.eloan.lms.lms.service.LMS1405Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L120M01C;
import com.mega.eloan.lms.model.L120S01A;
import com.mega.eloan.lms.model.L120S01C;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L140M01ATMP1;
import com.mega.eloan.lms.model.L140M01B;
import com.mega.eloan.lms.model.L140S02A;
import com.mega.eloan.lms.model.L140S02C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.NumConverter;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 異常通報產Excel
 * </pre>
 * 
 * @since 2012/12/3
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/3,Miller
 *          </ul>
 */
@Service("lms7840xlsservice")
public class LMS7840XLSServiceImpl implements FileDownloadService {

	@Resource
	LMS1405Service lms1405Service;

	@Resource
	LMS1205Service lms1205Service;

	@Resource
	MisdbBASEService misDBService;

	@Resource
	BranchService branch;

	@Resource
	L140M01ATMP1Dao l140m01atmp1Dao;

	@Resource
	BranchService branchService;

	@Resource
	L140S02ADao l140s02aDao;

	@Resource
	L120M01ADao l120m01aDao;

	@Resource
	L120M01CDao l120m01cDao;

	@Resource
	LMSService lmsService;

	@Resource
	L120S01CDao l120s01cDao;

	@Resource
	L120S01ADao l120s01aDao;
	@Resource
	CodeTypeService codetypeservice;

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS7840XLSServiceImpl.class);

	// FOR 浮水印測試失敗
	static int width = 300;
	static int height = 50;
	
	 @Override
	    public byte[] getContent(PageParameters params) throws CapException {
	        Properties pop2 = MessageBundleScriptCreator.getComponentResource(LMSCommomPage.class);
	        Properties prop = MessageBundleScriptCreator.getComponentResource(LMS7840M01Page.class);
	        MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
	        ByteArrayOutputStream baos = null;

	        String userId = user.getUserId();
	        try {
	            baos = new ByteArrayOutputStream();
	            HSSFWorkbook workbook = new HSSFWorkbook();
	            HSSFSheet sheet1 = workbook.createSheet("1");//頁籤1
	            HSSFSheet sheetAll = workbook.createSheet("ALL");//頁籤ALL
	            workbook.setSheetOrder("ALL",0);//將ALL頁籤放在第一個

	            sheetAll.getPrintSetup().setLandscape(true); //橫向打印             
	            sheetAll.getPrintSetup().setFitWidth((short)1); //縮放比例頁寬          
	            sheetAll.getPrintSetup().setFitHeight((short)5000); //縮放比例頁高度          

	            sheetAll.setFitToPage(true); 

	            // 設定字型與格式
	            HSSFFont font12 = workbook.createFont();
	            font12.setFontName(pop2.getProperty("other.msg60"));//新細明體
	            font12.setFontHeightInPoints((short) 12);
	            font12.setBold(false);//不粗體

	            // 中央對齊(置中)
	            HSSFCellStyle format12Center = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.CENTER);
	            // 左對齊
	            HSSFCellStyle format12Left = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.LEFT);
	            // 右對齊
	            HSSFCellStyle format12Right = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.RIGHT);
	            // 置中無邊框(POI 預設就無邊框)
	            HSSFCellStyle format12CenterNO = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.CENTER, false, false);
	            // 靠左無邊框
	            HSSFCellStyle format12LeftNO = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.LEFT, false, false);
	            // 靠右無邊框
	            HSSFCellStyle format12RightNO = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.RIGHT, false, false);

	            // 設定欄寬
	            int[] columnWidths = {14, 10, 20, 20, 14, 4, 20, 16, 6, 20, 40, 40, 40, 40, 60, 20, 10};
	            for (int i = 0; i < columnWidths.length; i++) {
	                sheetAll.setColumnWidth(i, columnWidths[i] * 256);
	            }

	            // 列高
	            sheetAll.createRow(0).setHeight((short) 500);
	            //設定標題和欄位
	            sheetAll = setLastTitleLMS7840R01(sheetAll, prop, workbook);

	            int y = 2;
	            String logMainId = Util.trim(params.getString("logMainId"));
	            List<L140M01ATMP1> l140m01atmp1s = l140m01atmp1Dao.findByUid(logMainId);

	            if (!l140m01atmp1s.isEmpty()) {
	                Locale locale = LMSUtil.getLocale();
	                Map<String, String> crdTypeMap = codetypeservice.findByCodeType("CRDType", locale.toString());
	                Properties prop1205r01 = MessageBundleScriptCreator.getComponentResource(LMS1205R01RptServiceImpl.class);

	                for (L140M01ATMP1 l140m01aTmp1 : l140m01atmp1s) {
	                    String mainId = l140m01aTmp1.getMainId();
	                    L140M01A l140m01a = lms1405Service.findL140m01aByMainId(mainId);
	                    if (l140m01a != null) {
	                        setColumnDataLMS7840R01(
	                            sheetAll, y++, l140m01aTmp1, l140m01a,
	                            format12Right, format12Center, format12Left,
	                            prop, crdTypeMap, prop1205r01
	                        );
	                    }
	                }
	            }
	            workbook.setActiveSheet(workbook.getSheetIndex("ALL"));   // 設定預設顯示 ALL 頁
	            workbook.setSelectedTab(workbook.getSheetIndex("ALL"));   // 設定為選取狀態

	            workbook.write(baos);
	            workbook.close();
	            return baos.toByteArray();
	        } catch (Exception ex) {
	            LOGGER.error("[getContent] Exception!!", ex);
	        } finally {
	            if (baos != null) try { baos.close(); } catch (Exception ex) {}
	        }
	        return null;
	    }


	   private HSSFSheet setLastTitleLMS7840R01(HSSFSheet sheet, Properties prop, HSSFWorkbook workbook)
	   throws IllegalArgumentException {
	    // 設定列印與方向
	    sheet.getPrintSetup().setPaperSize(HSSFPrintSetup.A4_PAPERSIZE);
	    sheet.getPrintSetup().setLandscape(true);
	    sheet.setFitToPage(true);
	    sheet.getPrintSetup().setFitWidth((short)1);

	    // 建立標楷體12pt粗體字型
	    HSSFFont font12 = workbook.createFont();
	    font12.setFontName("標楷體");
	    font12.setFontHeightInPoints((short)12);
	    font12.setBold(true);

	    // 置中粗體
	    HSSFCellStyle format12Center = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.CENTER);
	    // 靠右粗體
	    HSSFCellStyle format12Right = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.RIGHT);
	    // 靠左粗體
	    HSSFCellStyle format12Left = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.LEFT);
	    // 不加邊框 (POI 預設就無邊框)
	    HSSFCellStyle format12NoBordLeft = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.LEFT, false);
	   
	    HSSFCellStyle format12NoBordCenter = LmsExcelUtil.setCellFormat(workbook, font12, HorizontalAlignment.CENTER, false);

	    // 預設標題用置中粗體
	    HSSFCellStyle formatFont = format12Center;

	    // 合併 0~16 欄 (第一列全部)
	    sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 16));

	    // 設定欄寬
	    int[] colWidths = {14, 10, 20, 20, 14, 4, 20, 16, 6, 20, 40, 40, 40, 40, 60, 20, 10};
	    for (int i = 0; i < colWidths.length; i++) {
	        sheet.setColumnWidth(i, colWidths[i] * 256);
	    }

	    // 標題列
	    HSSFRow row0 = sheet.getRow(0);
	    if (row0 == null) row0 = sheet.createRow(0);
	    HSSFCell cell0 = row0.createCell(0);
	    cell0.setCellValue("額度明細表查詢結果");
	    cell0.setCellStyle(format12NoBordCenter);

	    // 欄位名稱列
	    String[] headers = {
	        "簽案日期", "簽案分行", "分行名稱", "案號", "客戶統編", "重覆序號", "名稱", "額度序號", "額度幣別",
	        "額度金額", "科目", "清償期限", "利費率", "擔保品", "其他敘做條件", "模型評等", "連保人有無"
	    };
	    HSSFRow row1 = sheet.createRow(1);
	    for (int i = 0; i < headers.length; i++) {
	    	LmsExcelUtil.addCell(row1, i, headers[i], formatFont);
	    }

	    return sheet;
	}


	    /**
	     * 設定LMS7840R01報表的欄位資料
	     *
	     * @param sheet 工作表
	     * @param rowIdx 當前行索引
	     * @param l140m01aTmp1 L140M01ATMP1實體
	     * @param l140m01a L140M01A實體
	     * @param format12Right 右對齊格式
	     * @param format12Center 中央對齊格式
	     * @param format12Left 左對齊格式
	     * @param prop 屬性檔
	     * @param crdTypeMap 信用卡類型映射
	     * @param prop1205r01 屬性檔1205r01
	     */
	    private void setColumnDataLMS7840R01(
	        HSSFSheet sheet, int rowIdx,
	        L140M01ATMP1 l140m01aTmp1, L140M01A l140m01a,
	        HSSFCellStyle format12Right, HSSFCellStyle format12Center, HSSFCellStyle format12Left,
	        Properties prop, Map<String, String> crdTypeMap, Properties prop1205r01
	    )  throws IllegalArgumentException {
	        String CASEDATE = null;
	        String OWNBRID = "";
	        String CASENO = "";
	        String CUSTID = "";
	        String DUPNO = "";
	        String CUSTNAME = "";
	        String CNTRNO = "";
	        String CURRENTAPPLYCURR = "";
	        BigDecimal CURRENTAPPLYAMT = BigDecimal.ZERO;
	        String LNSUBJECT = "";
	        String PAYDEADLINE = "";
	        String ITEMTYPERATE = "";
	        String ITEMTYPECMS = "";
	        String ITEMTYPEOTHER = "";
	        String brName = "";
	        String crdGrad = "";
	        String guarantor = "";

	        char[] newLine = { 13, 10 };
	        String mainId = l140m01a.getMainId();

	        if (l140m01a != null) {
	            boolean isClsCase = false;
	            boolean isParentCase = false;
	            L120M01A l120m01a = null;
	            L120M01C l120m01c = l120m01cDao.findoneByRefMainId(l140m01a.getMainId());
	            if (l120m01c != null) {
	                l120m01a = l120m01aDao.findByMainId(l120m01c.getMainId());
	                isParentCase = LMSUtil.isParentCase(l120m01a);
	                isClsCase = LMSUtil.isClsCase(l120m01a);
	            }

	            if (isClsCase) {
	                PAYDEADLINE = get_CLS_l140s02a_lnother(l140m01a.getMainId(), isParentCase);
	                ITEMTYPERATE = get_CLS_l140s02a_rate(l140m01a.getMainId(), "\n");
	            } else {
	                L140M01B l140m01bRate = lms1405Service.findL140m01bUniqueKey(mainId, "2");
	                PAYDEADLINE = Util.trim(l140m01a.getPayDeadline());
	                ITEMTYPERATE = l140m01bRate == null ? "" : Util.trim(l140m01bRate.getItemDscr()).replaceAll("<br/>", "\n");
	            }

	            L140M01B l140m01bCms = lms1405Service.findL140m01bUniqueKey(mainId, "3");
	            L140M01B l140m01bOther = lms1405Service.findL140m01bUniqueKey(mainId, "4");

	            CASEDATE = Util.equals(Util.trim(l140m01a.getCaseDate()), "") ? "" : TWNDate.toAD(l140m01a.getCaseDate());
	            OWNBRID = Util.trim(l140m01a.getOwnBrId());

	            if (Util.notEquals(OWNBRID, "")) {
	                brName = Util.trim(branchService.getBranch(OWNBRID).getBrName());
	            }

	            CASENO = Util.trim(l140m01a.getCaseNo());
	            CUSTID = Util.trim(l140m01a.getCustId());
	            DUPNO = Util.trim(l140m01a.getDupNo());
	            CUSTNAME = Util.trim(l140m01a.getCustName());
	            CNTRNO = Util.trim(l140m01a.getCntrNo());
	            CURRENTAPPLYCURR = Util.trim(l140m01a.getCurrentApplyCurr());
	            CURRENTAPPLYAMT = l140m01a.getCurrentApplyAmt() == null ? BigDecimal.ZERO : l140m01a.getCurrentApplyAmt();
	            LNSUBJECT = Util.trim(l140m01a.getLnSubject());

	            ITEMTYPECMS = l140m01bCms == null ? "" : Util.trim(l140m01bCms.getItemDscr()).replaceAll("<br/>", "\n");
	            ITEMTYPEOTHER = l140m01bOther == null ? "" : Util.trim(l140m01bOther.getItemDscr()).replaceAll("<br/>", "\n");

	            if (!isClsCase) {
	                L120S01A l120s01a = l120s01aDao.findByUniqueKey(
	                    l120m01a.getMainId(), l140m01a.getCustId(), l140m01a.getDupNo());
	                List<L120S01C> l120s01cList = l120s01cDao.findByCustId(
	                    l120m01a.getMainId(), l140m01a.getCustId(), l140m01a.getDupNo());

	                crdGrad = this.setL120S01CData(l120s01a, l120s01cList, crdTypeMap, prop1205r01, l140m01a);

	                if (Util.equals(Util.trim(l140m01a.getGuarantor()), "")
	                    || Util.equals(Util.trim(l140m01a.getGuarantor()), "無")
	                    || Util.equals(Util.trim(l140m01a.getGuarantor()), "无")
	                    || Util.equals(Util.trim(l140m01a.getGuarantor()), "None")) {
	                    guarantor = "無";
	                } else {
	                    guarantor = "有";
	                }
	            }

	            HSSFRow row = sheet.createRow(rowIdx);
	            int col = 0;
	            LmsExcelUtil.addCell(row, col++, CASEDATE, format12Center);
	            LmsExcelUtil.addCell(row, col++, OWNBRID, format12Center);
	            LmsExcelUtil.addCell(row, col++, brName, format12Left);
	            LmsExcelUtil.addCell(row, col++, CASENO, format12Left);
	            LmsExcelUtil.addCell(row, col++, CUSTID, format12Left);
	            LmsExcelUtil.addCell(row, col++, DUPNO, format12Left);
	            LmsExcelUtil.addCell(row, col++, CUSTNAME, format12Left);
	            LmsExcelUtil.addCell(row, col++, CNTRNO, format12Center);
	            LmsExcelUtil.addCell(row, col++, CURRENTAPPLYCURR, format12Center);
	            LmsExcelUtil.addCell(row, col++, NumConverter.addComma(CURRENTAPPLYAMT.toPlainString(), "#,###,###,###,##0"), format12Right);
	            LmsExcelUtil.addCell(row, col++, LNSUBJECT, format12Left);
	            LmsExcelUtil.addCell(row, col++, PAYDEADLINE, format12Left);
	            LmsExcelUtil.addCell(row, col++, this.trimHtml2Txt(ITEMTYPERATE), format12Left);
	            LmsExcelUtil.addCell(row, col++, this.trimHtml2Txt(ITEMTYPECMS), format12Left);
	            LmsExcelUtil.addCell(row, col++, this.trimHtml2Txt(ITEMTYPEOTHER), format12Left);
	            LmsExcelUtil.addCell(row, col++, crdGrad, format12Left);
	            LmsExcelUtil.addCell(row, col, guarantor, format12Left);//最後一行，不須遞增col
	        }
	    }
	

	public String get_CLS_l140s02a_lnother(String l140m01a_mainId,
			boolean isParentCase) {
		String payDeadline = "";
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS7840M01Page.class);
		List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a_mainId);
		if (l140s02as.size() > 1) {
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(l140s02as
							.toArray(new L140S02A[l140s02as.size()]));
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}
				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {
					String baseLnStr = "";

					if (isParentCase) {
						baseLnStr = other;
					} else {
						if (Util.isNotEmpty(other)) {
							other = "(" + other + ")";
						}
						// COMMON.year=年
						// COMMON.month=月
						baseLnStr = seqStr + ". " + lnYear
								+ prop.getProperty("COMMON.year") + lnMonth
								+ prop.getProperty("COMMON.month") + other
								+ " ";
					}
					payDeadline = payDeadline + baseLnStr;
				}
			}
		} else {
			if (!l140s02as.isEmpty()) {
				L140S02A l140s02a = l140s02as.get(0);

				Integer lnYear = l140s02a.getLnYear();
				Integer lnMonth = l140s02a.getLnMonth();
				String other = Util.trim(l140s02a.getLnOther());

				if (isParentCase) {
					payDeadline = other;
				} else {
					if (Util.isNotEmpty(other)) {
						other = "(" + other + ")";
					}
					if (Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth)) {
						// COMMON.year=年
						// COMMON.month=月
						payDeadline = lnYear + prop.getProperty("COMMON.year")
								+ lnMonth + prop.getProperty("COMMON.month");
					}
					payDeadline = payDeadline + other;
				}
			}
		}

		return payDeadline;
	}

	public String get_CLS_l140s02a_rate(String l140m01a_mainId, String mark) {
		String rates = "";
		List<L140S02A> l140s02as = l140s02aDao.findByMainId(l140m01a_mainId);
		if (l140s02as.size() > 1) {
			Map<Integer, String> seq_printStrMap = LMSUtil
					.getPrintStrForProdSeqNo(l140s02as
							.toArray(new L140S02A[l140s02as.size()]));
			for (L140S02A l140s02a : l140s02as) {
				String seqStr = "";
				if (seq_printStrMap.containsKey(l140s02a.getSeq())) {
					seqStr = seq_printStrMap.get(l140s02a.getSeq());
				}

				L140S02C l140s02c = l140s02a.getL140S02C();
				l140s02c = Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c;
				String lnYear = Util.trim(l140s02a.getLnYear());
				String lnMonth = Util.trim(l140s02a.getLnMonth());
				String other = Util.trim(l140s02a.getLnOther());
				String preDscr = Util.trim(l140s02c.getPreDscr());
				preDscr = Util.isNotEmpty(preDscr) ? preDscr + mark : "";

				if ((Util.isNotEmpty(lnYear) && Util.isNotEmpty(lnMonth))
						|| Util.isNotEmpty(other)) {
					if (ProdKindEnum.企金科目.getCode().equals(
							l140s02a.getProdKind())) {
						rates = rates + seqStr + ". " + preDscr
								+ Util.trim(l140s02a.getFreeRateDesc()) + mark;
					} else {
						rates = rates + seqStr + ". " + preDscr
								+ Util.trim(l140s02a.getRateDesc()) + mark;
					}
				}
			}
		} else {
			if (!l140s02as.isEmpty()) {
				L140S02A l140s02a = l140s02as.get(0);
				L140S02C l140s02c = l140s02a.getL140S02C();
				l140s02c = Util.isEmpty(l140s02c) ? new L140S02C() : l140s02c;

				if (ProdKindEnum.企金科目.getCode().equals(l140s02a.getProdKind())) {
					rates = Util.trim(l140s02a.getFreeRateDesc());
				} else {
					rates = Util.trim(l140s02a.getRateDesc());
				}
				String preDscr = Util.trim(l140s02c.getPreDscr());
				preDscr = !Util.isEmpty(preDscr) ? preDscr + mark : "";
				rates = preDscr + rates;
			}
		}
		return rates;
	}

	/**
	 * 塞入變數MAP資料使用(L120S01C)
	 * 
	 * @param rptVariableMap
	 *            存放變數MAP
	 * @param l120s01a
	 *            L120S01A的資料
	 * @param l120s01cList
	 *            LIST<L120S01C>的資料
	 * @param crdTypeMap
	 *            bcodetype的crdType
	 * @return Map<String,String> rptVariableMap
	 */
	private String setL120S01CData(L120S01A l120s01a,
			List<L120S01C> l120s01cList, Map<String, String> crdTypeMap,
			Properties prop, L140M01A l140m01a) {
		StringBuffer str1 = new StringBuffer();
		StringBuffer str2 = new StringBuffer();
		StringBuffer str3 = new StringBuffer();
		StringBuffer str4 = new StringBuffer();
		StringBuffer str5 = new StringBuffer();// 個人信用評等 J-105-0156-001 Web
												// e-Loan企金額度明細表增加得引入消金個人信用評等

		// 免辦
		boolean noResult = false;
		boolean naResult = false;
		StringBuffer tempGrade = new StringBuffer();
		for (L120S01C l120s01c : l120s01cList) {
			if (Util.nullToSpace(l120s01a.getCustId()).equals(
					Util.nullToSpace(l120s01c.getCustId()))
					&& Util.nullToSpace(l120s01a.getDupNo()).equals(
							Util.nullToSpace(l120s01c.getDupNo()))) {
				String crdType = Util.trim(l120s01c.getCrdType());
				String grade = Util.trim(l120s01c.getGrade());
				tempGrade.setLength(0);
				if ("NA".equals(crdType)) {
					naResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE01"))
					// .append(prop.getProperty("L120S05A.GRPGRRDN"))
					// .append("、");
				} else if ("DB".equals(crdType) || "DL".equals(crdType)
						|| "OU".equals(crdType) || "OB".equals(crdType)
						|| "A0".equals(crdType) || "A1".equals(crdType)
						|| "A2".equals(crdType)) {
					if (str3.length() != 0) {
						str3.append("、");
					}

					if ("NA".equals(grade)) {

					} else {

						if ("A0".equals(crdType) || "A1".equals(crdType)
								|| "A2".equals(crdType)) {

							if (Util.isNumeric(grade)) {
								tempGrade.append(grade)
										.append(prop.getProperty("tempGrade"))
										.append(" ");
							}

							// 取得MOW等級之說明
							tempGrade.append(lmsService.getMowGradeName(prop,
									crdType, grade));

							str3.append(
									Util.nullToSpace(crdTypeMap.get(crdType)))
									.append(" : ")
									.append(tempGrade.toString())
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");

						} else {
							str3.append(prop.getProperty("L120S01C.CRDTITLE01"))
									.append(grade)
									.append("【")
									.append(prop
											.getProperty("L120S01C.CRDTITLE02"))
									.append(Util.nullToSpace(TWNDate
											.toAD(l120s01c.getCrdTYear())))
									.append(" ")
									.append(prop
											.getProperty("L120S01C.CRDTITLE03"))
									.append(" ")
									.append(l120s01c.getCrdTBR())
									.append(" ")
									.append(Util.nullToSpace(branch
											.getBranchName(Util
													.nullToSpace(l120s01c
															.getCrdTBR()))))
									.append("】");
						}

					}

				} else if ("NO".equals(crdType)) {
					noResult = true;
					// str.append(prop.getProperty("L120S01C.CRDTITLE04"))
					// .append(prop.getProperty("L120S01C.NOCRD01"))
					// .append("、");
				} else if ("M".equals(Util.getLeftStr(crdType, 1))) {
					if (Util.isNumeric(grade)) {
						tempGrade.append(grade)
								.append(prop.getProperty("tempGrade"))
								.append(" ");
					}

					// 取得MOW等級之說明
					tempGrade.append(lmsService.getMowGradeName(prop, crdType,
							grade));

					if (str2.length() != 0) {
						str2.append("、");
					}
					str2.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(tempGrade.toString())
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				} else if (Casedoc.CrdType.MOODY.equals(crdType)
						|| Casedoc.CrdType.SAndP.equals(crdType)
						|| Casedoc.CrdType.Fitch.equals(crdType)
						|| Casedoc.CrdType.中華信評.equals(crdType)
						|| Casedoc.CrdType.FitchTW.equals(crdType)
						|| Casedoc.CrdType.KBRA.equals(crdType)) {
					// J-111-0597_05097_B1001 Web e-Loan企金授信增加惠譽台灣信用評等
					if (str1.length() != 0) {
						str1.append("、");
					}
					str1.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(Util.nullToSpace(crdTypeMap.get(l120s01c
									.getCrdType()))).append("】");
				} else if (crdType.startsWith("C")
						&& Util.notEquals(crdType, "CS")) {
					if (str4.length() != 0) {
						str4.append("、");
					}
					str4.append(Util.nullToSpace(crdTypeMap.get(crdType)))
							.append(" : ")
							.append(grade)
							.append("【")
							.append(prop.getProperty("L120S01C.CRDTITLE02"))
							.append(Util.nullToSpace(TWNDate.toAD(l120s01c
									.getCrdTYear())))
							.append(" ")
							.append(prop.getProperty("L120S01C.CRDTITLE03"))
							.append(" ")
							.append(l120s01c.getCrdTBR())
							.append(" ")
							.append(Util.nullToSpace(branch.getBranchName(Util
									.nullToSpace(l120s01c.getCrdTBR()))))
							.append("】");
				}
			}
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		// String buscd = Util.trim(l120s01a.getBusCode());
		// if (Util.equals(buscd, "130300") || Util.equals(buscd, "060000")) {
		// // 個人戶
		// str5.append(service1401.buildL140S03AStr(l140m01a.getMainId()));
		// }

		/*
		 * 狀況1:MX+NA 狀況2:DX+NO 狀況3:NA+NO 狀況4:空 最後在加外部NM,NS,NP
		 */
		// 外部評等一定要串
		boolean result = false;

		StringBuffer total = new StringBuffer();
		// L120S01C.CRDTITLE04=模型評等 :
		if (str2.length() > 0) {

			// MXXX+外部
			// rptVariableMap.put("L120S01C.CRD",str2.toString());
			total.append(prop.getProperty("L120S01C.CRDTITLE04") + " " + str2);
			result = true;
		}
		// L120S01C.CRDTITLE01=信用評等 :
		if (str3.length() > 0) {
			// DXXX+外部
			total.append(total.length() > 0 ? "\r" : "");
			total.append(str3.toString());
			// rptVariableMap.put("L120S01C.CRD",str3.toString() + " " +
			// prop.getProperty("L120S01C.CRDTITLE04"));
			result = true;
		}

		// L120S01C.CRDTITLE05=外部評等 :
		if (str1.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE05")
					+ str1.toString());
		}

		// J-105-0156-001 Web e-Loan企金額度明細表增加得引入消金個人信用評等
		// L120S01C.CRDTITLE07=個金評等 :
		if (str5.length() > 0) {
			total.append(total.length() > 0 ? "\r" : "");
			total.append(prop.getProperty("L120S01C.CRDTITLE07")
					+ str5.toString());
		}

		if (total.length() == 0) {
			// rptVariableMap.put("L120S01C.CRD",prop.getProperty("L120S01C.NOCRD01"));
			total.append(prop.getProperty("L120S01C.NOCRD01"));
			result = true;
		}

		// rptVariableMap.put("L120S01C.CRD",(!result ? "" :
		// (rptVariableMap.get("L120S01C.CRD") + "\n"))+crdtitle05 +
		// str1.toString());

		return total.toString();
	}

	// FOR 浮水印測試失敗
	public static Map createWaterMark1(Map watermarkMessage) throws IOException {
		Map map = new HashMap();
		File file = new File("watermark.png");
		BufferedImage bi = new BufferedImage(width, height,
				BufferedImage.TYPE_INT_RGB);
		int minx = bi.getMinX();
		int miny = bi.getMinY();
		for (int i = minx; i < width; i++) {
			for (int j = miny; j < height; j++) {
				bi.setRGB(i, j, 0xffffff);
			}
		}
		Graphics2D g2d = bi.createGraphics();
		// 设置字体颜色为灰色
		g2d.setColor(Color.LIGHT_GRAY);
		// 设置图片的属性
		g2d.setStroke(new BasicStroke(1));
		// 设置字体
		g2d.setFont(new Font("Serif", Font.ITALIC, 40));
		// 设置字体倾斜度
		g2d.rotate(Math.toRadians(-8));

		// 写入水印文字原定高度过小，所以累计写水印，增加高度
		// g2d.drawString("导出时间 :  " + watermarkMessage.get("time"), 0, 180 + 40
		// * (1+ 2));
		// g2d.drawString("导出工号 :  " + watermarkMessage.get("loginName"),0,180 +
		// 40 * (1+3));
		// g2d.drawString("工号部门 :  " + watermarkMessage.get("domain"), 0,180 +
		// 40 * (1+ 4));

		g2d.drawString("导出时间 :  " + "AAAAAAAAAAAAAAA", 0, 180 + 40 * (1 + 2));
		g2d.drawString("导出工号 :  " + "BBBBBBBBBBBBBBB", 0, 180 + 40 * (1 + 3));
		g2d.drawString("工号部门 :  " + "CCCCCCCCCCCCCCC", 0, 180 + 40 * (1 + 4));

		// 设置透明度
		g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
		// 释放对象
		g2d.dispose();
		// 通过bmp写入文件
		// BMPEncoder.write(bi, file);
		ImageIO.write(bi, "png", file);
		map.put("file", file);
		map.put("width", width);
		map.put("height", height);

		return map;
	}

	// FOR 浮水印測試失敗
	public static Map toMap(String jsonString) throws JSONException {
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		Map result = new HashMap();
		Iterator iterator = jsonObject.keys();
		String key = null;
		String value = null;
		while (iterator.hasNext()) {
			key = (String) iterator.next();
			value = jsonObject.getString(key);
			result.put(key, value);
		}

		return result;
	}

	/**
	 * 去除HTML TAG 及HTML特殊字元
	 * 
	 * @param html
	 * @return
	 */
	public String trimHtml2Txt(String html) {

		String noHTMLString = "";
		// TEST 1
		// String htmlTagPattern = "<{1}[^>]{1,}>{1}"; // HTML Tag Pattern
		// String htmlSplit = html.replaceAll(htmlTagPattern, "");

		// TEST 2
		// noHTMLString = html.replaceAll("\\<.*?\\>", "");
		// noHTMLString = noHTMLString.replaceAll("&#92;&#92;s{2,}", " ");

		// TEST 3
		try {
			StringReader in = new StringReader(html);
			Html2Text parser = new Html2Text();
			parser.parse(in);
			noHTMLString = parser.getText();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return noHTMLString.trim();
	}
    
}
