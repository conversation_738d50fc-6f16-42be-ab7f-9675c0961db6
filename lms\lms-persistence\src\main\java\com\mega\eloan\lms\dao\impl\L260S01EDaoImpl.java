/* 
 * L260S01EDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L260S01EDao;
import com.mega.eloan.lms.model.L260S01E;

/** 公司訪問紀錄表項目檔  **/
@Repository
public class L260S01EDaoImpl extends LMSJpaDao<L260S01E, String>
	implements L260S01EDao {

	@Override
	public L260S01E findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L260S01E> findByMainId(String mainId, boolean notIncDel) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if(notIncDel){
			search.addSearchModeParameters(SearchMode.IS_NULL, "deletedTime", "");
		}
		search.addOrderBy("itemSeq");
		List<L260S01E> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L260S01E findByUniqueKey(String mainId, String itemNo){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L260S01E> findByIndex01(String mainId, String itemNo){
		ISearch search = createSearchTemplete();
		List<L260S01E> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (itemNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "itemNo", itemNo);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
}