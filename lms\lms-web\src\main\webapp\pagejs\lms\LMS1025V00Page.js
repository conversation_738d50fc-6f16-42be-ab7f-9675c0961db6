pageJsInit(function() {
$(function() {
    var grid = $("#gridview").iGrid({
        handler: 'lms1015gridhandler',
        height: 350,
        action: 'queryBaseData',
        sortname: 'custId',
        rowNum: 17,
        rownumbers: true,
        
        colModel: [{
            name: 'oid',
            hidden: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            colHeader: i18n.def["megaID"],
            width: 90,
            name: 'custId',
            formatter: 'click',
            onclick: openDoc
        }, {
            colHeader: ' ',
            width: 10,
            name: 'dupNo',
            sortable: true
        }, {
            colHeader: i18n.def["compName"],
            width: 140,
            name: 'custName',
            sortable: true
        }, {
            colHeader: i18n.lms1015v01["C101S01G.jcicQDate"], // 聯徵查詢日期
            align: "center",
            width: 60,
            sortable: false,
            name: 'eJcicQDate' //原本 c120s01e的欄位，在 c120m01a 建一個 @Transient
        }, {
            colHeader: i18n.lms1015v01["C101S01G.etchQDate"], // 票信查詢日期
            align: "center",
            width: 60,
            sortable: false,
            name: 'eChkQDate' //原本 c120s01e的欄位，在 c120m01a 建一個 @Transient
        }, {
            colHeader: i18n.def["lastUpdater"],
            width: 110,
            name: 'updater',
            sortable: true
        }, {
            colHeader: i18n.def["lastUpdateTime"],
            width: 100,
            name: 'updateTime',
            sortable: true
        }, {
            colHeader: ' ',
            width: 10,
            name: 'o_chkYN',
            sortable: true
        }]
    });
    
    function openDoc(cellvalue, options, rowObject){    
        openWindowDoc(false, rowObject.oid);
        
    };
    
    function openWindowDoc(lockDoc, oid, isNew){
        $.form.submit({
            url: '../lms/lms1025open/01',
            data: {
                oid: oid,
                isNew: isNew,
                lockDoc: lockDoc
            },
            target: oid
        });
    }
    
    $("#buttonPanel").find("#btnFilter").click(function(){
		openFilterBox();
	}).end().find("#btnDelete").click(function(){
        var rows = $("#gridview").getGridParam('selrow');
        var mainOid = "";
        if (rows != 'undefined' && rows != null &&
        rows != 0) {
            var data = $("#gridview").getRowData(rows);
            mainOid = data.oid;
        }
        if (mainOid == "") {
            CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
            return;
        }
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: 'lms1015m01formhandler',
                    data: {
                        formAction: "delBaseData",
                        'c120m01a_oid': mainOid
                    },
                    }).done(function(responseData){
                        $("#gridview").trigger('reloadGrid');
                        $.thickbox.close();
                });
            }
        });
        
    }).end().find("#btnAdd").click(function(){
    
        var formId = "addborrowForm";
        var $addborrow = $("#" + formId);
        $addborrow.reset();
        $addborrow.find("input[name=rborrowA]:checked").trigger('click');
        /*
         *
         * 在 thickbox 的 div 內，包含 addborrowForm
         *
         * addborrowForm 內的 btn 引進，參考
         * $("#getCustData").click(function(){
         *
         */
        $("#thickboxaddborrow").thickbox({
            title: '',
            width: 800,
            height: 380,
            modal: false,
            i18n: i18n.def,
            buttons: {
                "close": function(){
                    API.confirmMessage(i18n.def['flow.exit'], function(res){
                        if (res) {
                            $.thickbox.close();
                        }
                    });
                }
            }
        });
    }).end().find("#btnModify").click(function(){
        var id = $("#gridview").getGridParam('selrow');
        if (!id) {// TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        var result = $("#gridview").getRowData(id);
        openDoc(null, null, result);
        
    });
    
    $("#getCustData").click(function(){
        var formId = "addborrowForm";
        var $addborrow = $("#" + formId);
        
        var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
        var $custName = $addborrow.find("[name=addborrowForm_custName]").val();
        if (($custId != null &&
        $custId != undefined &&
        $custId != '') &&
        ($custName != null &&
        $custName != undefined &&
        $custName != '')) {
            // 統一編號、名稱擇一輸入引進即可
            CommonAPI.showErrorMessage(i18n.lms1015m01["l120s02.alert26"]);
        }
        else 
            if (($custId == null ||
            $custId == undefined ||
            $custId == '') &&
            ($custName == null ||
            $custName == undefined ||
            $custName == '')) {
                // 請輸入統一編號或名稱
                CommonAPI.showErrorMessage(i18n.lms1015m01["l120s02.alert27"]);
            }
            else {
                var defaultOption = {};
                if ($custId != null &&
                $custId != undefined &&
                $custId != '') {
                    defaultOption = {
                        defaultValue: $custId
                    // 預設值
                    };
                }
                else {
                    defaultOption = {
                        defaultName: $custName
                    };
                }
                // 綁入MegaID
                CommonAPI.openQueryBox($.extend({
                    defaultCustType: ($custId != null &&
                    $custId != undefined &&
                    $custId != '') ? "1" : ($custName != null &&
                    $custName != undefined &&
                    $custName != '') ? "2" : "",
                    divId: formId, // 在哪個div
                    // 底下
                    isInSide: false,
                    autoResponse: { // 是否自動回填資訊
                        id: "addborrowForm_custId", // 統一編號欄位ID
                        dupno: "addborrowForm_dupNo", // 重覆編號欄位ID
                        name: "addborrowForm_custName" // 客戶名稱欄位ID
                    },
                    fn: function(obj){
                        /*
                     * obj.custid
                     * "A123456789"
                     * obj.dupno
                     * "0"
                     * obj.name
                     * "TESTT"
                     * obj.buscd
                     * "060000"
                     */
                        if ($addborrow.valid()) {
                            $.ajax({
                                type: "POST",
                                handler: 'lms1015m01formhandler',
                                data: {
                                    formAction: "addBaseData",
                                    custId: obj.custid,
                                    dupNo: obj.dupno,
                                    custName: obj.name, 
                                    busCode: obj.buscd
                                },
                                }).done(function(responseData){
                                    // call_z_grid_openDoc(responseData.c120m01a_oid,
                                    // true);
                                    openWindowDoc(false, responseData.c120m01a_oid, true)
                                    $("#gridview").trigger('reloadGrid');
                                    $.thickbox.close();
                            }).fail(function(){ $addborrow.reset(); });
                        }
                    }
                }, defaultOption));
            }
    });
    
});
});

function openFilterBox(){
	var _id = "_div_filter";
	var _form = _id+"_form";
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		dyna.push("<table class='tb2'>");
		dyna.push("<tr><td class='hd1' nowrap>"+i18n.def["megaID"]+"</td><td><input type='text' name='search_custId' value='' maxlength='10'></td></tr>");		
		dyna.push("</table>");
		dyna.push("</form>");
		
		dyna.push("</div>");
		
	     $('body').append(dyna.join(""));
	}
	//clear data
	$("#"+_form).reset();
	
	$("#"+_id).thickbox({ title: i18n.def['query'], width: 400, height: 185, modal: true,
        valign: "bottom", align: "center", i18n: i18n.def,
        buttons: {
            "sure": function(){            	
            	if (true){	
					$.thickbox.close();
					
					$("#gridview").jqGrid("setGridParam", {
						postData : $("#"+_form).serializeData(),
						page : 1,
						search : true
					}).trigger("reloadGrid");
				}
            },
            "cancel": function(){
            	 $.thickbox.close();
            }
        }
    });
}