var gRescueRateItem = "";
var gOldRescueCaseItem = "";
var gRescueRateItemSub = "";
// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
var lastSel;
var guarantorPriorityOn = "";
// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
var rescueChgRateItem = "";
//J-112-0148_05097_B1002 Web e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
var gRescueNoItem = "";
var gEmpCountItem = "";
var gRescueSnItem = "";


// J-112-0148 疫後振興
function isResueItemCaseF(){
    var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
    var isFVal = false;
    // LMS_RESCUEITEM_CASE_F 參數調整這裡也要改
    var fVals = ["F01", "F02", "F03", "F04", "F05" ,"F06", "F07", "F08", "F09", "F10"];
    // == -1 代表沒找到
    if ($.inArray(rescueItem, fVals) != -1) {
        isFVal = true;
    }
    return isFVal;
}

// J-112-0148 疫後振興
function isResueItemCaseJ(){
	var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
	var isFVal = false;
	// LMS_RESCUEITEM_CASE_J 參數調整這裡也要改
	var fVals = ["J01","J02","J03","J04","J05","J06","J07","J08","J09"];
	// == -1 代表沒找到
	if ($.inArray(rescueItem, fVals) != -1) {
		isFVal = true;
	}
	return isFVal;
}

function isResueItemCaseL(){
	var rescueItem = $("#isRescueSpan").find("#rescueItem").val();
	var isFVal = false;
	// LMS_RESCUEITEM_CASE_L 參數調整這裡也要改
	var fVals = ["L01","L02","L03"];
	// == -1 代表沒找到
	if ($.inArray(rescueItem, fVals) != -1) {
		isFVal = true;
	}
	return isFVal;
}

initDfd
		.done(function(json, auth) {
			// if (!$.isEmptyObject(json.cntSelect)) {
			// if ($("#L161M01AForm").find("#cntrNo").length) {
			// $("#L161M01AForm").find("#cntrNo").setItems({
			// item: json.cntSelect,
			// space: false,
			// value: json.cntrNo || ""
			// });
			// }
			//        
			// }
			// 舊案用

			$.ajax({
				handler : "lms1601m01formhandler",
				data : {
					formAction : "queryInitData"
				},
				success : function(initObj) {
					// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
					gRescueRateItem = initObj.rescueRateItem;
					gOldRescueCaseItem = initObj.oldRescueCaseItem;
					gRescueRateItemSub = initObj.rescueRateItemSub;
					// J-110-0007_05097_B1001 Web
					// e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
					guarantorPriorityOn = initObj.guarantorPriorityOn;

					// J-111-0214_05097_B1001 Web
					// e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
					rescueChgRateItem = initObj.rescueChgRateItem;

					//J-112-0148_05097_B1002 Web e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
					gRescueNoItem = initObj.rescueNoItem;
					gEmpCountItem = initObj.empCountItem;
					gRescueSnItem = initObj.rescueSnItem;
					
					if (guarantorPriorityOn == "Y") {
						$("#setGuarantorCreditPriority").show();
						$("#btnEntireApply").show();

					} else {
						$("#setGuarantorCreditPriority").hide();
						$("#btnEntireApply").hide();
					}

				}
			});

			var gridviewBranchOld = $("#gridviewBranchOld").iGrid({
				localFirst : true,
				needPager : false,
				handler : inits.ghaddle,
				height : "190px",
				width : "100%",
				multiselect : true,
				sortname : 'slBank|slBranch',
				sortorder : 'asc|asc',
				postData : {
					formAction : "queryBranch"
				},
				colModel : [ {
					colHeader : i18n.lms1601m01['L160M01A.slBank'],// "參貸行庫/分行",
					name : 'slBankCN',
					width : '200px',
					sortable : true,
					align : "left",
					formatter : 'click',
					onclick : branchBox
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slMaster'],// "共同主辦行",
					name : 'slMaster',
					width : '80px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slAccNo'],// "同業帳號",
					name : 'slAccNo',
					width : '100px',
					sortable : true,
					align : "right"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slAmt'],// "參貸金額",
					name : 'slAmt',
					width : '100px',
					sortable : true,
					align : "right",
					formatter : 'currency',
					formatoptions : {
						thousandsSeparator : ",",
						removeTrailingZero : true,
						decimalPlaces : 2
					// 小數點到第幾位
					}
				}, {
					name : 'oid',
					hidden : true
				}, {
					name : 'pid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridviewBranchOld.getRowData(rowid);
					branchBox(null, null, data);

				}
			});

			// 新案用L161S01A
			var gridviewBranch = $("#gridviewBranch").iGrid({
				needPager : false,
				localFirst : true,
				handler : inits.ghaddle,
				height : "190px",
				width : "100%",
				multiselect : true,
				sortname : 'seq',
				sortorder : 'asc|asc',
				postData : {
					formAction : "queryBranch"
				},
				colModel : [ {
					colHeader : i18n.lms1601m01['L160M01A.slBank'],// "參貸行庫/分行",
					name : 'slBankCN',
					width : '200px',
					sortable : true,
					align : "left",
					formatter : 'click',
					onclick : branchBox
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slMaster'],// "共同主辦行",
					name : 'slMaster',
					width : '80px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slAccNo'],// "同業帳號",
					name : 'slAccNo',
					width : '100px',
					sortable : true,
					align : "right"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.slAmt'],// "參貸金額",
					name : 'slAmt',
					width : '100px',
					sortable : true,
					align : "right",
					formatter : 'currency',
					formatoptions : {
						thousandsSeparator : ",",
						removeTrailingZero : true,
						decimalPlaces : 2
					// 小數點到第幾位
					}
				}, {
					name : 'seq',
					hidden : true
				}, {
					name : 'oid',
					hidden : true
				}, {
					name : 'pid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridviewBranch.getRowData(rowid);
					branchBox(null, null, data);

				}
			});
			// G-113-0036 簽案額度明細聯行攤貸比例
			var gridviewL140M01E_AF = $(BranchAcitonAF.gridId).iGrid({
			    handler: inits.ghaddle,
			    height: 170,
			    rowNum: 10,
			    rownumbers: true,
			    multiselect: true,
			    hideMultiselect: false,
			    sortname: 'createTime',
			    sortorder: 'asc',
			    postData: {
			        formAction: "queryL140m01e_af",
			        cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
					noOpenDoc: true
			    },
			    autowidth: true,
			    colModel: [{
			        name: 'shareRate2',
			        hidden: true
			    }, {
			        colHeader: i18n.lms1601m01["L140M01e.shareBrId"],//"攤貸分行",
			        name: 'shareBrId',
			        align: "left",
			        width: 110,
			        sortable: true,
			        formatter: 'click',
			        onclick: BranchAcitonAF.query
			    }, {
			        colHeader: i18n.lms1601m01["L140M01e.shareAmt"],//"攤貸金額",
			        name: 'shareAmt',
			        width: 160,
			        sortable: true,
			        align: "right",
			        formatter: 'currency',
			        formatoptions: {
			            thousandsSeparator: ",",
						removeTrailingZero: true,
			            decimalPlaces: 2//小數點到第幾位
			        }
			    }, {
			        colHeader: i18n.lms1601m01["L140M01e.shareRate1"],//"攤貸比例",
			        width: 140,
			        name: 'showRate',
			        align: "right",
			        sortable: true
			    }, {
			        colHeader: i18n.lms1601m01["L140M01e.shareNo"],//"額度序號",
			        width: 140,
			        name: 'shareNo',
			        sortable: true
			    }, {
			        name: 'oid',
			        hidden: true
			    }],
			    ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能           	
			        var data = $(BranchAcitonAF.gridId).getRowData(rowid);
			        BranchAcitonAF.query(null, null, data);
			    }
			});
			//簽案額度明細聯行攤貸比例調整
			$(BranchAcitonAF.amtgrId).iGrid({
				handler: inits.ghaddle,
		        rowNum: 10,
		        postData: {
		        	 formAction: "queryL140m01e_af",
				     cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
		        },
		        rowNum: 10,
		        autowidth: true,
		        colModel: [{
		            name: 'shareRate2',
		            hidden: true
		        }, {
		            colHeader: i18n.lms1601m01["L140M01e.shareBrId"],//"攤貸分行",
		            name: 'shareBrId',
		            align: "left",
		            width: 110,
		            sortable: true
		        }, {
		            colHeader: i18n.lms1601m01["L140M01e.shareAmt"],//"攤貸金額",
		            name: 'shareAmt',
		            width: 160,
		            sortable: true,
		            align: "right",
		            formatter: 'currency',
		            formatoptions: {
		                thousandsSeparator: ",",
						removeTrailingZero: true,
		                decimalPlaces: 2
		            }
		        }, {
		            colHeader: i18n.lms1601m01["L140M01e.shareRate1"],//"攤貸比例",
		            width: 140,
		            name: 'showRate',
		            align: "right",
		            sortable: true
		        }, {
		            colHeader: i18n.lms1601m01["L140M01e.shareNo"],//"額度序號",
		            width: 140,
		            name: 'shareNo',
		            sortable: true
		        }, {
		            name: 'oid',
		            hidden: true
		        }]
		    });
			
			$("#gridviewPeople")
					.iGrid(
							{
								handler : 'lms1601gridhandler',
								width : "100%",
								height : "250px",
								sortname : 'cntrNo|rType|rKindD|rId',
								sortorder : 'asc|asc|asc|asc',
								multiselect : true,
								postData : {
									formAction : "queryPeople"
								},
								colModel : [
										{
											colHeader : i18n.lms1601m01['L162M01A.custId'],// "主債務人統編",
											name : 'custId',
											width : '60px',
											sortable : true,
											formatter : 'click',
											onclick : peopleBox
										},
										{
											colHeader : i18n.lms1601m01['L160M01A.cntrNum'],// "額度序號",
											name : 'cntrNo',
											width : '70px',
											sortable : true
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.rId'],// "從債務人統編",
											name : 'rId',
											width : '60px',
											sortable : true
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.rName'],// "從債務人名稱",
											name : 'rName',
											width : '100px',
											sortable : true
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.rKindM'],// "關係",
											name : 'rKindD',
											width : '80px',
											sortable : true
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.rCountry'],// "國別",
											name : 'rCountry',
											width : '50px',
											sortable : true,
											align : "center"
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.rType'],// "相關身份",
											name : 'rType',
											width : '100px',
											sortable : true

										},
										{
											colHeader : i18n.lms1601m01['L162M01A.guaPercent'],// "保證人負担保證責任比率",
											name : 'guaPercent',
											width : '30px',
											sortable : false
										},
										{
											// J-110-0007_05097_B1001 Web
											// e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
											colHeader : i18n.lms1601m01["L162M01A.priority"],// 信用品質順序
											name : 'priority',
											align : "center",
											width : 20,
											sortable : false
										},
										{
											// J-110-0040_05097_B1001 Web
											// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
											colHeader : i18n.lms1601m01["L162M01A.guaNaExposure"],// 本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
											name : 'guaNaExposure',
											align : "center",
											width : 20,
											sortable : false
										},
										{
											colHeader : i18n.lms1601m01['L162M01A.dueDate']
													+ "<br/>"
													+ i18n.lms1601m01['L162M01A.dueDate2'],// "董監事任期止日<br
																							// />(保證人保證迄日)",
											name : 'dueDate',
											width : '50px',
											sortable : true
										}, {
											name : 'oid',
											hidden : true
										} ],
								ondblClickRow : function(rowid) {
									var data = $("#gridviewPeople").getRowData(
											rowid);
									peopleBox(null, null, data);
								}
							});

			// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
			/** 保證人信用品質順序設定grid */
			$("#gridViewGuarantorSeq").iGrid(
					{
						needPager : false,
						handler : 'lms1601gridhandler',
						postData : {
							formAction : "queryPeopleForSetPriority"
						},
						height : 230,
						cellsubmit : 'clientArray',
						autowidth : true,
						sortname : 'priority|createTime',
						sortorder : 'asc|asc',
						localFirst : true,
						colModel : [ {
							colHeader : i18n.lms1601m01['L162M01A.custId'],// "主債務人統編",
							name : 'custId',
							width : '80px',
							sortable : true,
							formatter : 'click',
							onclick : peopleBox
						}, {
							colHeader : i18n.lms1601m01['L160M01A.cntrNum'],// "額度序號",
							name : 'cntrNo',
							width : '100px',
							sortable : true
						}, {
							colHeader : i18n.lms1601m01['L162M01A.rId'],// "從債務人統編",
							name : 'rId',
							width : '100px',
							sortable : true
						}, {
							colHeader : i18n.lms1601m01['L162M01A.rName'],// "從債務人名稱",
							name : 'rName',
							width : '100px',
							sortable : true
						}, {
							colHeader : i18n.lms1601m01['L162M01A.rKindM'],// "關係",
							name : 'rKindD',
							width : '120px',
							sortable : true
						}, {
							colHeader : i18n.lms1601m01['L162M01A.rCountry'],// "國別",
							name : 'rCountry',
							width : '50px',
							sortable : true,
							align : "center"
						}, {
							colHeader : i18n.lms1601m01['L162M01A.rType'],// "相關身份",
							name : 'rType',
							width : '100px',
							sortable : true
						}, {
							colHeader : i18n.lms1601m01['L162M01A.guaPercent'],// "保證人負担保證責任比率",
							name : 'guaPercent',
							width : '40px',
							sortable : false
						}, {
							colHeader : i18n.lms1601m01["L162M01A.priority"],// 信用品質順序
							name : 'priority',
							align : "center",
							editable : true,
							width : 60,
							sortable : true,
							editrules : {
								number : true
							}
						}, {
							name : 'oid',
							hidden : true
						} ],
						onSelectRow : function(id) {
							if (id && id != lastSel) {
								$("#gridViewGuarantorSeq").saveRow(lastSel,
										false, 'clientArray');
								$('#gridViewGuarantorSeq').restoreRow(lastSel);
								lastSel = id;
							}
							$('#gridViewGuarantorSeq').editRow(id, false);
						}
					});

			var showVersion = json.showVersion;

			if (showVersion == "0") {
				$("#showOldL161M01AForm").show();
				$("#showL161M01CForm").hide();
				if (json.unitLoanCaseOld == "Y") {
					$("#lms160s0301").show();
				}
				var cntrNo = $("#showOldL161M01AForm").find("#cntrNoOld");
				$("#gridviewBranchOld").jqGrid("setGridParam", {
					postData : {
						formAction : "queryBranch",
						cntrNo : cntrNo,
						uid : json.uidOld
					}
				}).trigger("reloadGrid");

			} else {
				$("#lms160s0301").show();
				$("#showOldL161M01AForm").hide();
				$("#showL161M01CForm").show();
			}

			var custDataInfo = json.custIdSelect;
			if (!$.isEmptyObject(custDataInfo)) {
				var temp = "<option value=''>" + i18n.def.comboSpace
						+ "</option>";
				$("#L162M01AForm").find("#cntrNo").html(temp);
				// 先帶出custId
				for ( var key in custDataInfo) {
					temp += "<option value=" + key + ">"
							+ key.substring(0, key.length - 1) + " "
							+ key.substring(key.length - 1, key.length)
							+ "</option>";
				}
				$("#custIdSelect").html(temp);

			}

			// J-105-0135-001 Web e-Loan國內企金授信系統動審表，開放可修改振興經濟非中小企業專案貸款註記與金額。
			$("input[name='isNonSMEProjLoan']").change(
					function() {
						var isNonSMEProjLoan = $(
								"input[name='isNonSMEProjLoan']:radio:checked")
								.val();

						if (isNonSMEProjLoan == "Y") {
							$("#showNonSMEProjLoanAmt").show();
						} else {
							$("#showNonSMEProjLoanAmt").hide();
						}
					});

			// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
			$("input[name='inSmeFg']").change(function() {
				var inSmeFg = $("input[name='inSmeFg']:radio:checked").val();
				if (inSmeFg == "Y") {
					$("#showInSMEAmt").show();
				} else {
					$("#showInSMEAmt").hide();
				}
			});

			// J-109-0077_05097_B1001 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$("input[name='isRescue']").change(
					function() {
						var value = $("input[name=isRescue]:checked").val();
						if (value == "Y") {
							$("#isRescueSpan").show();
						} else {
							$("#isRescueSpan").find(':input').not(
									':button, :submit, :reset, :hidden')
									.removeAttr('checked').removeAttr(
											'selected')
									.not(':checkbox, :radio').val('');
							$("#isRescueSpan").hide();
							$("#rescueRateSpan").hide();
							$("#oldRescueCaseSpan").hide();
							$("#rescueItemSubSpan").hide();
							$("#rescueItemA06Span").hide();
							$("#rescueItemA07Span").hide();

							//J-112-0148_05097_B1002 Web e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
                            $("#rescueNoSpan").hide();
                            $("#empCountSpan").hide();
                            $("#rescueSnSpan").hide();
						}

					});

			// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
			$("input[name='rescueChgRateFg']").change(
					function() {
						var value = $("input[name=rescueChgRateFg]:checked")
								.val();
						if (value == "Y") {
							$("#chgRescueRateYSpan").show();
						} else {
							$("#chgRescueRateYSpan").find(':input').not(
									':button, :submit, :reset, :hidden')
									.removeAttr('checked').removeAttr(
											'selected')
									.not(':checkbox, :radio').val('');
							$("#chgRescueRateYSpan").hide();
						}
					});

			// J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$("#rescueItem")
					.change(
							function() {
								var value = $(this).val();
								value = DOMPurify.sanitize(value);
								// gOldRescueCaseItem = "A01,E01";
								// alert("value="+value);
								// alert("gOldRescueCaseItem="+gOldRescueCaseItem);
								// alert("gRescueRateItem="+gRescueRateItem);
								// alert("gOldRescueCaseItem.indexOf(value)="+gOldRescueCaseItem.indexOf(value));
								// alert("gRescueRateItem.indexOf(value)="+gRescueRateItem.indexOf(value));

								
								//掛件文號改成動態顯示名稱
								reloadRescueItemData(value);
								 
                                // 預設
                                $("#isRescueSpan").find('#rescueItemTitleStr').val(i18n.lms1601m01["L140M01a.rescueItem"]);

								if (gOldRescueCaseItem.indexOf(value) != -1) {
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#rescueItemA06Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									// A01 E01
									$("#oldRescueCaseSpan").show();
									$("#rescueItemSubSpan").hide();
									$("#rescueNoRateSpan").hide();
									$("#rescueItemA06Span").hide();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('#rescueItemSub')
											.val('');
									
									//J-111-0112_05097_B1002 Web e-Loan企金授信管理系統新增111年經濟部紓困方案
									if(value == "A04"){
										$("#rescueItemA04StrSpan").show();
										$("#rescueItemA04StrSpan").siblings().hide();
			                            // $("#rescueItemA01StrSpan").hide();
			                            // $("#rescueItemA04StrSpan").show();
			                            // $("#rescueItemA08StrSpan").hide();
			                        } else if(value == "A08"){
										$("#rescueItemA08StrSpan").show();
										$("#rescueItemA08StrSpan").siblings().hide();
			                            // $("#rescueItemA01StrSpan").hide();
			                            // $("#rescueItemA04StrSpan").hide();
			                            // $("#rescueItemA08StrSpan").show();
			                        } else {
			                            $("#rescueItemA01StrSpan").show();
			                            $("#rescueItemA01StrSpan").siblings().hide();
			                            // $("#rescueItemA04StrSpan").hide();
			                            // $("#rescueItemA08StrSpan").hide();
			                        }
			                        $("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();
									$(".forRescueIbDateOldRescueCase").show().siblings().hide();
									$(".forIsExtendSixMonOldRescueCase").show().siblings().hide();

								} else if (value.substring(0, 1) == "Z") {

									// 要顯示合併申請紓困方案
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#rescueNo,#empCount,#rescueItemSub,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA06Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").show();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").hide();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();
								} else if (value == "C02" || value == "C03") {

									// 要顯示合併申請紓困方案
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#rescueNo,#empCount,#rescueItemSub,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA06Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").show();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").hide();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();

								} else if (value == "A03") {

									// 要顯示合併申請紓困方案
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#rescueItemSub,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA06Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").show();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").hide();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();

								} else if (value == "H01" || value == "H02") {

									// J-110-0258_05097_B1002 Web
									// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
									// 要顯示國發基金加碼保證成數
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,#rescueNdfGutPercent,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").hide();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").show();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();

								} else if (value == "A07" || value == "A11") {

									// J-110-0288_05097_B1002 Web
									// e-Loan配合辦理「行政院國家發展基金協助新創事業紓困融資加碼方案」，修改額度明細表欄位
									// 要顯示國發基金加碼保證成數
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#isSmallBuss,#sbRegistPeriod,#sbPrincipalPeriod,#sbCreditRating,#sbColStatus,#sbBussStatus,#sbScore,#sbHasCreditRating,#isRescueIntroduce,#rescueIntroduceBrNo,#rescueIntroduceUserId,#rescueIntroduceBrNoForShow,#rescueIntroduceUserIdForShow,#isMegaSuperProfitProject,#rescueIndustry,#rescueCity,#rescueNdfGutPercent,#isTurnoverDecreased,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").hide();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").show();
									$("#rescueItemA07Span").show();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();
								} else if (isResueItemCaseF() || isResueItemCaseJ() || value == "K01" || isResueItemCaseL()){

    								// 文字顯示
    								$("#isRescueSpan").find('#rescueItemTitleStr').val(isResueItemCaseF()?i18n.lms1601m01["L140M01a.rescueItem2"]:isResueItemCaseJ()?i18n.lms1601m01["L140M01a.rescueItem3"]:isResueItemCaseL()?i18n.lms1601m01["L140M01a.rescueItem5"]:i18n.lms1601m01["L140M01a.rescueItem4"]);
    								$("#rescueItemSubSpan").hide();
    								$("#rescueItemA06Span").hide();
    								$("#rescueItemA07Span").hide();
    								$("#oldRescueCaseSpan").hide();
									if (value == "J04" || value == "J05") {
										$("#oldRescueCaseSpan").show();
										$(".forRescueAmt" + value).show().siblings().hide();
										$(".forRescueIbDate" + value).show().siblings().hide();
										$(".forIsExtendSixMon" + value).show().siblings().hide();
									} else if (value == "K01") {
										$("#rescueNo,#empCount").val("");
									}
									/*
									$("#rescueRateSpan").hide();
									*/
									$("#isRescueSpan").find('.rescueItemFhide').hide();
			                        $("#isRescueSpan").find('.rescueItemFshow').show();
								} else {
									// 要先清空SPAN內容，再隱藏SPAN顯示
									$("#isRescueSpan")
											.find(':input')
											.not(
													'#rescueItem,#rescueDate,#isCbRefin,#rescueNo,#empCount,:button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA06Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#rescueItemA07Span")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#oldRescueCaseSpan").hide();
									$("#rescueItemSubSpan").hide();
									$("#rescueNoRateSpan").show();
									$("#rescueItemA06Span").hide();
									$("#rescueItemA07Span").hide();
									$("#isRescueSpan").find('.rescueItemFhide').show();
			                        $("#isRescueSpan").find('.rescueItemFshow').hide();

								}

								if (gRescueRateItem.indexOf(value) != -1) {
									// gRescueRateItem = "A01,A02,A03,E01";
									$("#rescueRateSpan").show();
									$("[class^='rescueRateStr_']").hide();
									if (value && $("[class^='rescueRateStr_" + DOMPurify.sanitize(value) + "']").length > 0) {
										$(".rescueRateStr_" + DOMPurify.sanitize(value)).show();					
									} else {
										$(".rescueRateStr_OTH").show();
									}
									
								} else {
									$("#rescueRateSpan").hide();
									$("[class^='rescueRateStr_']").hide();
									$("#rescueItemSub").trigger("change");
								}

								// J-111-0214_05097_B1001 Web
								// e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
								if (rescueChgRateItem.indexOf(value) != -1) {
									// LMS_RESCUEITEM_CHG_RATE
									// A01,A04,A03,A06,A07 可調整並適用新利率計算減免息
									$("#chgRescueRateSpan").show();
									$("#rescueChgRateFg").trigger("change");
								} else {
									$("#chgRescueRateSpan")
											.find(':input')
											.not(
													':button, :submit, :reset, :hidden')
											.removeAttr('checked').removeAttr(
													'selected').not(
													':checkbox, :radio')
											.val('');
									$("#chgRescueRateSpan").hide();
									$("#rescueChgRateFg").trigger("change");
								}

                                //J-112-0148_05097_B1002 Web e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
                                if (gRescueNoItem.indexOf(value) != -1) {
                                    $("#rescueNoSpan").show();
                                }else{
                                    $("#rescueNoSpan").hide();
                                }
                                if (gEmpCountItem.indexOf(value) != -1) {
                                    $("#empCountSpan").show();
                                }else{
                                    $("#empCountSpan").hide();
                                }
                                if (gRescueSnItem.indexOf(value) != -1) {
                                    $("#rescueSnSpan").show();
                                }else{
                                    $("#rescueSnSpan").hide();
                                }

							});

			// J-109-0077_05097_B1005 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
			$("#rescueItemSub")
					.change(
							function() {
								var value = $(this).val();
								var rescueItem = $("#rescueItem").val();
								// J-110-0429_05097_B1001
								// 配合內政部紓困方案，增列內政部紓困方案代號及相關條件
								if (gRescueRateItem.indexOf(rescueItem) == -1
										&& value
										&& gRescueRateItemSub.indexOf(value) != -1) {
									// value != "" && value != undefined &&
									// value != null
									// 紓困貸款類別不用補貼利率，但是符合其他紓困貸款利息補貼要補貼利率
									$("#rescueRateSpan").show();
									$("[class^='rescueRateStr_']").hide();
									$(".rescueRateStr_OTH").show();
								} else {
									if (gRescueRateItem.indexOf(rescueItem) == -1
											&& value
											&& gRescueRateItemSub
													.indexOf(value) == -1) {
										// 都不用補貼利率時，清掉
										$("#rescueRate").val('');
									}
									$("#rescueRateSpan").hide();
									$("[class^='rescueRateStr_']").hide();
								}

							});

			// J-109-0811_05097_B1001
			// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
			$("input[name='isCbRefin']").change(
					function() {
						var value = $("input[name=isCbRefin]:checked").val();
						if (value == "Y") {
							$("#cbRefinSpan").show();
						} else {
							$("#cbRefinSpan").find(':input').not(
									':button, :submit, :reset, :hidden')
									.removeAttr('checked').removeAttr(
											'selected')
									.not(':checkbox, :radio').val('');
							$("#cbRefinSpan").hide();
						}
					});

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			var item = API.loadOrderCombosAsList("lms7600_adoptFg")["lms7600_adoptFg"];
			$("#adoptFg").setItems({
				size : "4",
				item : item,
				clear : true,
				itemType : 'checkbox'
			});

			var $formObject = $("#L161M01AForm");
			$("input[name='isClearLand']").change(
					function() {

						var isClearLand = $(
								"input[name='isClearLand']:radio:checked")
								.val();

						if (isClearLand == "Y") {
							$("#showClearLand").show();
							// alert("AA");
						} else {
							$("#showClearLand").hide();
							// alert("BB");
							initClearLand($formObject);
						}
					});

			$formObject.find("[name=isChgStDate]").change(
					function() {
						var isChgStDate = $(
								"input[name='isChgStDate']:radio:checked")
								.val();
						if (isChgStDate == "Y") {
							$(".showChgStDate").show();
							$(".showIsChgRate").hide();
							$(".showIsLegal").show();

						} else if (isChgStDate == "N") {
							$(".showChgStDate").hide();
							$(".showIsChgRate").show();

							if ($formObject.find("[name=isChgRate]:checked")
									.val() == "Y") {
								$(".showIsLegal").show();
							} else {
								$(".showIsLegal").hide();
							}

						} else {
							$(".showChgStDate").hide();
							$(".showIsChgRate").hide();
							if ($formObject.find("[name=isChgRate]:checked")
									.val() == "Y") {
								$(".showIsLegal").show();
							} else {
								$(".showIsLegal").hide();
							}
						}

						$formObject.find("#showClearLand").find(
								"input:checkbox").trigger("change");
					});

			var $adoptFg = $formObject.find("input[name='adoptFg']");
			$adoptFg
					.change(function() {

						// 增加4.無以上措施(再加碼幅度為0%)
						if ($formObject.find("[name=adoptFg][value=4]").attr(
								"checked")) {
							$formObject.find("[name='adoptFg']").filter(
									"[value !='4']").attr("checked", false)
									.attr("disabled", true);
							$formObject.find(".showChgRate").hide();
							$formObject.find("[name=isChgRate][value=N]")
									.click();
						} else {
							$formObject.find("[name='adoptFg']").attr(
									"disabled", false);
							$formObject.find("[name='adoptFg']").filter(
									"[value='4']").attr("checked", false);
						}

						if ($formObject.find("[name=adoptFg][value=3]").attr(
								"checked")) {
							$formObject.find(".showChgRate").show();
							$formObject.find("[name=isChgRate][value=Y]")
									.click();
						} else {
							$formObject.find(".showChgRate").hide();
							$formObject.find("[name=isChgRate][value=N]")
									.click();

						}
					});

			$formObject
					.find("[name=isChgRate]")
					.change(
							function() {
								var isChgRate = $(
										"input[name='isChgRate']:radio:checked")
										.val();
								if (isChgRate == "Y") {
									$(".showChgRate").show();
									$(".showIsLegal").show();
								} else {
									$(".showChgRate").hide();
									$formObject
											.find(
													"#custRoa,#relRoa,#rateAdd,#roaBgnDate,#roaEndDate")
											.val('');

									if ($formObject.find(
											"[name=isChgStDate]:checked").val() == "Y") {
										$(".showIsLegal").show();
									} else {
										$(".showIsLegal").hide();
									}
								}
							});

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制

			$("#custIdSelect").change(
					function() {
						var value = $(this).val();
						var temp = "";
						if (value) {
							for ( var key in custDataInfo[value]) {
								// 先帶出custId
								temp += "<option value="
										+ custDataInfo[value][key] + ">"
										+ custDataInfo[value][key]
										+ "</option>";
							}
						} else {
							temp = "<option value=''>" + i18n.def.comboSpace
									+ "</option>";
						}
						$("#L162M01AForm").find("#cntrNo").html(temp);
					});

			// 下次檢視日期
			$("#reViewDateKind").change(function() {
				var selectVal = $(this).val();
				switch (selectVal) {
				case "00":// 不檢視
					$("#reViewDate").val("");
					$("#showReViewDate").hide();
					$("#showReViewChgKind").hide();
					break;
				case "01":// YYYY-MM-DD
					$("#showReViewDate").show();
					$("#showReViewChgKind").show();
					break;
				default:
					$("#reViewDate").val("");
					$("#showReViewDate").hide();
					$("#showReViewChgKind").hide();
					break;
				}
			});

			// 檢視週期
			$("#reViewChgKind").change(function() {
				var selectVal = $(this).val();
				switch (selectVal) {
				case "00":// 無
					$("#reViewChg1").val("");
					$("#reViewChg1").hide();
					break;
				case "01":// 每X個月
					$("#reViewChg1").show();
					break;
				default:
					$("#reViewChg1").val("");
					$("#reViewChg1").hide();
					break;
				}
			});

			/**
			 * 取得聯貸案已編碼國外銀行的清單
			 * 
			 * @param {String }
			 *            value 分行種類 12-國外分行 99 -其他(由國外部徵信系統金融機構資料維護)
			 */
			function getBranch(value) {
				$.ajax({
					handler : "lms1601m01formhandler",
					data : {
						formAction : "queryforeignBranch",
						type : value
					},
					success : function(obj) {
						if (!$.isEmptyObject(obj.foreignBranch)) {
							// 新增空白選項
							$("#selBank" + DOMPurify.sanitize(value)).setItems({
								item : obj.foreignBranch,
								format : "{value} - {key}",
								space : false
							});
						}
					}
				});
			}
			// 判斷是否顯示參貸行
			// $("#lms160s0301").show()
			// if (json.unitLoanCase == "Y") {
			// $("#lms160s0301").show()
			// }
			// else {
			// $("#lms160s0302").click();
			// }

			$("#lms160s0302").click();

			// 關係切換
			$('#rKindM').change(function() {
				var value = $(this).val();
				switch (value) {
				case "1":
					$("#the1").show().siblings("#the2,#the3").hide();
					break;
				case "2":
					$("#the2").show().siblings("#the1,#the3").hide();
					break;
				case "3":
					$("#the3").show().siblings("#the1,#the2").hide();
					break;
				default:
					$("#the1,#the2,#the3").hide();
					$("[id^=rationSelect]").val("");
					break;
				}
			});

			// 相關身份
			$("#L162M01AForm").find('#rType')
					.change(
							function() {
								var value = $(this).val();
								if (value == "C" || value == "S") {
									$(".showGuaPercent").hide();
									$("#guaPercent").val(null);
									$("[name=guaNaExposure]:radio").removeAttr(
											"checked");// 移除隱藏選項的radio checked
														// ;
								} else {
									$(".showGuaPercent").show();
									$("#guaPercent").val(100);
								}

								// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
								if (value == "C") {
									$("#L162M01AForm").find(".hBorrowData")
											.show();
									// J-109-0209_05097_B1001
									// e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
									if ($("#L162M01AForm")
											.find("#custIdSelect").val() == $(
											"#L162M01AForm").find("#rId").val()
											+ $("#L162M01AForm")
													.find("#rDupNo").val()) {
										$("#L162M01AForm").find(
												".hBorrowDataMain").show();
									} else {
										$("#L162M01AForm").find(
												".hBorrowDataMain").hide();
									}
								} else {
									$("#L162M01AForm").find(".hBorrowData")
											.hide();
									$("#L162M01AForm").find(".hBorrowDataMain")
											.hide();
								}

							});

			// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
			$("#L162M01AForm").find('#rId').change(function() {
				$("#L162M01AForm").find('#rType').trigger('change');
			});
			// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目
			$("#L162M01AForm").find('#rDupNo').change(function() {
				$("#L162M01AForm").find('#rType').trigger('change');
			});

			// 銀行種類切換
			$('#selBank')
					.change(
							function() {
								var value = $(this).val();

								if ($.trim(value) != "") {

									$("#selBank" + DOMPurify.sanitize(value)).show().siblings(
											"[id^=selBank]").hide();
								} else {

									$("#tdSelBank").find("[id^=selBank]")
											.hide();
								}
								// 當沒有下拉選單時才去抓
								if (value == "12"
										&& $("#selBank12 option").length == 0) {

									getBranch(value);
								} else if (value == "99"
										&& $("#selBank99 option").length == 0) {

									getBranch(value);
								}

							});

			// 動態切換載入銀行
			$('#selBank01,#selBank02').change(function() {
				var value = $(this).val();
				var id = DOMPurify.sanitize($(this).attr("id").slice(7));//selBank01,selBank02
				$("#selBankOther" + id).show();
				if ($.trim(value) == "") {
					$("#selBankOther" + id).hide();
					return false;
				}

				$.ajax({
					handler : inits.fhandle,
					data : {
						formAction : "queryBranch",
						mainBranch : value
					},
					success : function(obj) {
						if (!$.isEmptyObject(obj.brankList)) {
							$("#selBankOther" + id).setItems({
								item : obj.brankList,
								format : "{value} {key}",
								space : false
							});
						} else {
							$("#selBankOther" + id).hide().html("");
						}
					}
				});
			});

			// 新增聯貸案參貸比率一覽表
			$("#openBranchBox").click(function() {
				var amt = $.trim($("#quotaAmt").val());
				if (!$("#L161M01AForm").valid()) {
					return;
				}
				// //檢驗規則
				// if (amt == 0 || amt == '') {
				// //L160M01A.error5=總額度不得為
				// return
				// CommonAPI.showErrorMessage(i18n.lms1601m01["L160M01A.error5"]
				// + "0");
				// }

				branchBox();
			});

			// J-103-0317-001 Web e-Loan企金簽報書上傳承諾事項與追蹤檢視日期
			$("#applyToAloan1").click(function() {
				applyToAloan("1");
			});

			$("#applyToAloan2").click(function() {
				applyToAloan("2");
			});

			// 複製同業參貸比率
			$("#copyBranchBox").click(function() {
				copyBranchBox();
			});

			// 引進案由
			$("#applyGist").click(function() {
				applyGist();
			});

			// 刪除聯貸案參貸比率一覽表
			$("#deleteBranch")
					.click(
							function() {

								var select = gridviewBranch
										.getGridParam('selarrrow');
								if (select == "") {

									// TMMDeleteError=請先選擇需修改(刪除)之資料列
									CommonAPI
											.showMessage(i18n.def["TMMDeleteError"]);
									return;
								}

								// confirmDelete=是否確定刪除?
								CommonAPI
										.confirmMessage(
												i18n.def["confirmDelete"],
												function(b) {
													if (b) {
														var data = [];
														for ( var i in select) {
															data
																	.push(gridviewBranch
																			.getRowData(select[i]).oid);
														}

														$
																.ajax({
																	handler : inits.fhandle,
																	data : {
																		formAction : "deleteL161M01B",
																		oids : data
																	},
																	success : function(
																			obj) {
																		gridviewBranch
																				.trigger("reloadGrid");
																	}
																});
													} else {
														return;
													}
												});
							});

			// 列印聯貸案參貸比率一覽表(舊案用)
			$("#printBranchOld").click(
					function() {
						var count = $("#gridviewBranchOld").jqGrid(
								'getGridParam', 'records');
						var uids = responseJSON.mainId;

						// 先檢查是否有資料
						if (count == 0) {
							// EFD0002=INFO|報表無資料|
							return CommonAPI
									.showErrorMessage(i18n.msg["EFD0002"]);
						}

						$.form.submit({
							url : "../../simple/FileProcessingService",
							target : "_blank",
							data : {
								type : "R02",
								mainId : responseJSON.mainId,
								mainOid : $("#mainOid").val(),
								fileDownloadName : "lms1601r02.pdf",
								serviceName : "lms1601r01rptservice",
								mode : "OLD",
								uids : uids
							}
						});
					});

			// 列印聯貸案參貸比率一覽表(新案用L161S01A模式)
			$("#printBranch").click(
					function() {
						var count = $("#gridviewBranch").jqGrid('getGridParam',
								'records');
						var uids = $("#L161M01AForm").find("#uid").val();

						// 先檢查是否有資料
						if (count == 0) {
							// EFD0002=INFO|報表無資料|
							return CommonAPI
									.showErrorMessage(i18n.msg["EFD0002"]);
						}

						// 先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
						$.ajax({
							handler : inits.fhandle,
							data : {
								formAction : "chkAllPrintHasL161S01B",
								mainId : responseJSON.mainId,
								mainOid : $("#mainOid").val(),
								uids : uids
							},
							success : function(obj) {
								$.form.submit({
									url : "../../simple/FileProcessingService",
									target : "_blank",
									data : {
										type : "R02",
										mainId : responseJSON.mainId,
										mainOid : $("#mainOid").val(),
										fileDownloadName : "lms1601r02.pdf",
										serviceName : "lms1601r01rptservice",
										uids : uids
									}
								});
							}
						});

					});

			// 整批列印聯貸案參貸比率一覽表
			$("#printBranchAll")
					.click(
							function() {
								var $gridviewCntrnoInfo = $("#gridviewCntrnoInfo");
								var count = $gridviewCntrnoInfo.jqGrid(
										'getGridParam', 'records');

								// 先檢查是否有資料
								if (count == 0) {
									// EFD0002=INFO|報表無資料|
									return CommonAPI
											.showErrorMessage(i18n.msg["EFD0002"]);
								}

								var ids = $gridviewCntrnoInfo
										.getGridParam('selarrrow');
								var uids = "";
								if (ids == "") {
									// action_005=請先選取一筆以上之資料列
									return CommonAPI
											.showErrorMessage(i18n.def['action_005']);
								}
								for ( var i in ids) {
									if (uids == "") {
										uids = $gridviewCntrnoInfo
												.getRowData(ids[i]).uid;
									} else {
										uids = uids
												+ ","
												+ $gridviewCntrnoInfo
														.getRowData(ids[i]).uid;
									}

								}

								// 先確認整批勾選列印的額度動用資訊是否都有聯貸參貸比率
								$
										.ajax({
											handler : inits.fhandle,
											data : {
												formAction : "chkAllPrintHasL161S01B",
												mainId : responseJSON.mainId,
												mainOid : $("#mainOid").val(),
												uids : uids
											},
											success : function(obj) {
												$.form
														.submit({
															url : "../../simple/FileProcessingService",
															target : "_blank",
															data : {
																type : "R02",
																mainId : responseJSON.mainId,
																mainOid : $(
																		"#mainOid")
																		.val(),
																fileDownloadName : "lms1601r02.pdf",
																serviceName : "lms1601r01rptservice",
																uids : uids
															}
														});
											}
										});

							});

			// 新增聯貸案參貸比率一覽表
			$("#openCntrnoBox").click(function() {
				var id = $("#gridviewCntrnoInfo").getGridParam('selrow');
				if (!id) {

					// action_004=請先選擇需「調閱」之資料列
					return CommonAPI.showMessage(i18n.def["action_004"]);

				}
				var result = $("#gridviewCntrnoInfo").getRowData(id);
				cntrnoInfoBox(null, null, result);
			});

			// 登錄銀行
			$("#includeBranch")
					.click(
							function() {
								// 初始化選項
								$("#includeBranchBox [id^=selBank]").hide();
								$("#selBank").show();
								$("#includeBranchForm").reset();

								$("#includeBranchBox")
										.thickbox(
												{
													// L160M01A.slBank=參貸行庫/分行
													title : i18n.lms1601m01['L160M01A.slBank'],
													width : 650,
													height : 200,
													modal : true,
													align : "center",
													i18n : i18n.def,
													valign : "bottom",
													buttons : {
														"sure" : function() {

															// 初始化畫面欄位
															$(
																	"#slBankType,#slBranchCN,#slBranch,#slBank,#slBankCN")
																	.val("");
															$(
																	"#showBranch,#showBranchCn")
																	.html("");

															// 參貸行總類
															var value = DOMPurify.sanitize($(
																	"#selBank")
																	.val());
															// 參貸銀行
															var number = DOMPurify.sanitize($(
																	"#selBank"
																			+ value)
																	.val());
															//value= 數字 01,02...
															var name = DOMPurify.sanitize($
																	.trim($(
																			"#selBank"
																					+ value
																					+ " :selected")
																			.text()
																			.slice(
																					5)));

															if ($.trim(number) == ""
																	|| $
																			.trim(value) == "") {
																// grid.selrow=請先選擇一筆資料。
																return CommonAPI
																		.showMessage(i18n.def["grid.selrow"]);
															}
															$("#slBank").val(
																	number);
															$("#slBankCN").val(
																	name);
															$("#showBranch")
																	.html(
																			number
																					+ " "
																					+ name);
															$("#slBankType")
																	.val(value);
															switch (value) {
															case "01":
															case "02":
																var numberCn = $(
																		"#selBankOther"
																				+ value)
																		.val();
																if ($
																		.trim(numberCn) == ""
																		&& !$(
																				"#selBankOther"
																						+ value)
																				.is(
																						":hidden")) {
																	// grid.selrow=請先選擇一筆資料。
																	return CommonAPI
																			.showMessage(i18n.def["grid.selrow"]);
																}
																var numberCnName = $(
																		"#selBankOther"
																				+ value
																				+ " :selected")
																		.text();
																$("#slBranch")
																		.val(
																				numberCn);
																if ("017" == number) {
																	$(
																			"#slBranchCN")
																			.val(
																					numberCnName
																							.slice(4));
																	$(
																			"#showBranchCn")
																			.html(DOMPurify.sanitize(
																					numberCnName));
																} else {
																	if ($(
																			"#selBankOther"
																					+ value)
																			.is(
																					":hidden")) {
																		$(
																				"#showBranchCn")
																				.html(
																						"");
																	} else {
																		$(
																				"#showBranchCn")
																				.html(
																						DOMPurify.sanitize(numberCn
																								+ " "
																								+ numberCnName
																										.slice(8)));
																		$(
																				"#slBranchCN")
																				.val(
																						numberCnName
																								.slice(8));
																	}

																}
																break;
															case "03":
															case "04":
															case "05":
															case "06":
															case "07":
															case "08":
															case "09":
															case "10":
															case "11":
																break;
															case "12":
															case "99":
																$("#slBankCN")
																		.val(
																				$(
																						"#selBank"
																								+ value
																								+ " :selected")
																						.text()
																						.split(
																								" - ")[1]);
																$("#showBranch")
																		.html(DOMPurify.sanitize(
																				$(
																						"#selBank"
																								+ value
																								+ " :selected")
																						.text()));
																break;
															default:
																// grid.selrow=請先選擇一筆資料。
																return CommonAPI
																		.showMessage(i18n.def["grid.selrow"]);
																break;
															}
															$.thickbox.close();
														},
														"cancel" : function() {
															$.thickbox.close();
														}
													}
												});
							});

			function branchBox(cellvalue, options, rowData) {
				var $L161M01BForm = $("#L161M01BForm");
				var $L161M01AForm = $("#L161M01AForm");

				// 初始化表單物件
				$L161M01BForm.reset().attr("formOid", "");
				$L161M01BForm.find("#showBranch,#showBranchCn").html("");

				var cntrNo = $L161M01AForm.find("#cntrNo").val();
				var uid = $L161M01AForm.find("#uid").val();

				if (rowData) {
					$.ajax({
						handler : "lms1601m01formhandler",
						data : {
							formAction : "queryL161s01b",
							oid : rowData.oid
						},
						success : function(obj) {
							$L161M01BForm.setData(obj);
							$("#showBranch").html(
									DOMPurify.sanitize(obj.slBank + " " + obj.slBankCN));
							$("#showBranchCn").html(
									DOMPurify.sanitize(obj.slBranch + " " + obj.slBranchCN));
						}
					});
				}
				$("#branchBox")
						.thickbox(
								{

									// L160M01A.title10=聯貸案參貸比率一覽表
									title : i18n.lms1601m01['L160M01A.title10'],
									width : 620,
									height : 280,
									modal : true,
									i18n : i18n.def,
									buttons : {
										"saveData" : function() {
											if (!$L161M01BForm.valid()) {
												return;
											}
											var amt = $.trim($("#slAmt").val());

											if ($.trim($("#slBankType").val()) == "") {

												// L160M01A.slBank=參貸行庫/分行
												// L160M01A.error7=請登錄
												return CommonAPI
														.showErrorMessage(i18n.lms1601m01["L160M01A.error7"]
																+ i18n.lms1601m01["L160M01A.slBank"]);
											}
											FormAction.open = true;
											$
													.ajax({
														handler : "lms1601m01formhandler",
														data : {
															formAction : "saveL161m01b",
															L161M01AForm:JSON.stringify($("#L161M01AForm").serializeData()),
															L161M01BForm:JSON.stringify($("#L161M01BForm").serializeData()),
															oid : $L161M01BForm
																	.attr("formOid"),
															slCurr : $(
																	"#quotaCurr")
																	.val(),
															cntrNo : cntrNo,
															uid : uid,
															totalAmt : $(
																	"#quotaAmt")
																	.val()
																	.replace(
																			/,/g,
																			"")
														},
														success : function(obj) {
															FormAction.open = false;
															$.thickbox.close();
															$("#gridviewBranch")
																	.trigger(
																			'reloadGrid');
														}
													});
										},
										"close" : function() {
											$.thickbox.close();
										}
									}
								});

			}

			// 引進連保人
			$("#includePeople").click(
					function() {

						// 新增的主從債務人資料將被清空，是否繼續？【確定】(引進連保人資料)
						CommonAPI.confirmMessage(
								i18n.lms1601m01["L160M01A.message26"],
								function(b) {
									if (b) {
										$.ajax({
											handler : "lms1601m01formhandler",
											data : {
												formAction : "includeL140m01I"
											},
											success : function(obj) {
												$("#gridviewPeople").trigger(
														'reloadGrid');
											}
										});
									}
								});
					});

			// 新增主從債務人資料表
			$("#openPeopleBox").click(function() {
				peopleBox();
			});

			// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
			// 信用品質順序設定
			$("#setGuarantorCreditPriority").click(function() {
				setGuarantorSeq();
			});

			// 刪除主從債務人資料表
			$("#deletePeople")
					.click(
							function() {
								var $gridviewPeople = $("#gridviewPeople");
								var select = $gridviewPeople.jqGrid(
										'getGridParam', 'selarrrow');
								if (select == "") {

									// TMMDeleteError=請先選擇需修改(刪除)之資料列
									return CommonAPI
											.showMessage(i18n.def["TMMDeleteError"]);

								}

								// confirmDelete=是否確定刪除?
								CommonAPI
										.confirmMessage(
												i18n.def["confirmDelete"],
												function(b) {
													if (b) {
														var data = [];
														for ( var i in select) {
															data
																	.push($gridviewPeople
																			.getRowData(select[i]).oid);
														}

														$
																.ajax({
																	handler : "lms1601m01formhandler",
																	data : {
																		formAction : "deleteL162M01A",
																		oids : data
																	},
																	success : function(
																			obj) {
																		$gridviewPeople
																				.trigger("reloadGrid");
																	}
																});
													} else {
														return;
													}
												});
							});

			// 列印新增主從債務人資料表
			$("#printPeople").click(
					function() {
						var count = $("#gridviewPeople").jqGrid('getGridParam',
								'records');

						// 先檢查是否有資料
						if (count == 0) {
							// EFD0002=INFO|報表無資料|
							return CommonAPI
									.showErrorMessage(i18n.msg["EFD0002"]);
						}
						$.form.submit({
							url : "../../simple/FileProcessingService",
							target : "_blank",
							data : {
								mainId : responseJSON.mainId,
								mainOid : $("#mainOid").val(),
								fileDownloadName : "lms1601r03.pdf",
								serviceName : "lms1601r01rptservice",
								type : "R03"
							}
						});
					});

			// J-110-0040_05097_B1001 Web
			// e-Loan增加「本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)」註記
			// 整批引進最新資料
			$("#btnEntireApply")
					.click(
							function() {
								// 海外:lms1405s02_EntireApplyNew
								// 國內:lms1401s02_EntireApplyNew
								var raw = API
										.loadOrderCombosAsList("lms1601s03_EntireApplyNew")["lms1601s03_EntireApplyNew"];
										
								var parsed = {};
								try {
								    var obj = JSON.parse(raw);
								    parsed[obj.value] = obj;
								} catch (e) {
								    console.error("JSON parse error:", e);
								}	
								
								$("#entireApply").setItems({
									size : "1",
									item : parsed,
									clear : true,
									itemType : 'checkbox'
								})

								$("[name=entireApply]").removeAttr("disabled")
										.removeAttr("checked");

								$("input[name='entireApply']")
										.change(
												function() {

													var entireApply = $(
															'input:checkbox:checked[name="entireApply"]')
															.val();
													if (entireApply == "A01") {
														$("#showGuaNaExposure")
																.show();
													} else {
														$("#showGuaNaExposure")
																.hide();
													}
												});

								$("input[name='entireApply']")
										.trigger("change");

								var $gridviewC_2 = $("#gridviewPeople");
								var ids = $gridviewC_2
										.getGridParam('selarrrow');
								var oids = [];
								if (ids == "") {
									// action_005=請先選取一筆以上之資料列
									return CommonAPI
											.showErrorMessage(i18n.def['action_005']);
								}

								$("#choiceEntireApply")
										.thickbox(
												{
													// L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
													title : i18n.lms1601m01["L162M01A.guaNaExposure"],
													width : 600,
													height : 200,
													modal : true,
													align : "center",
													valign : "bottom",
													readOnly : false,
													i18n : i18n.def,
													buttons : {
														"sure" : function() {

															var entireApply = $(
																	'input:checkbox:checked[name="entireApply"]')
																	.val();
															var guaNaExposure = "";
															if (entireApply == "A01") {
																guaNaExposure = $(
																		"input[name='guaNaExposureTmp']:radio:checked")
																		.val();

																if ($
																		.trim(guaNaExposure) == "") {

																	// L162M01A.guaNaExposure=本行國家暴險是否以保證人國別為計算基準(取代最終風險國別)
																	// L160M01A.error7=請登錄
																	return CommonAPI
																			.showErrorMessage(i18n.lms1601m01["L160M01A.error7"]
																					+ i18n.lms1601m01["L162M01A.guaNaExposure"]);
																}

															}

															$.thickbox.close();

															var allCheackedVal = [];
															$
																	.each(
																			$("#choiceEntireApply :checkbox[name=entireApply]:checked"),
																			function(
																					i,
																					n) {
																				allCheackedVal[i] = $(
																						n)
																						.val();
																			});

															for ( var i in ids) {
																oids
																		.push($gridviewC_2
																				.getRowData(ids[i]).oid);
															}

															$
																	.ajax({
																		async : false,
																		handler : "lms1601m01formhandler",
																		data : {
																			formAction : "entireApplyNew",
																			allCheackedVal : allCheackedVal
																					.join("|"),
																			oids : oids,
																			guaNaExposure : guaNaExposure
																		},
																		success : function(
																				obj) {
																			// runSuccess=執行成功
																			$gridviewC_2
																					.trigger("reloadGrid");
																			CommonAPI
																					.showMessage(i18n.def["runSuccess"]);

																		}
																	});
														},
														"cancel" : function() {
															$.thickbox.close();
														}
													}
												});
							});
			//G-113-0036 主從債務人excel匯入
			 $("#ExcelDownload").click(function () {// 下載範本
				 $.form.submit({
				 	url: webroot + '/img/lms/L162S01AChkList.xls',
				    target: "_blank"
				 });
			 });
		     $("#ExcelImport").click(function () {// excel匯入
		     	$("#uploadDialog").thickbox({
		        	modal: false,
		            height: 200,
		            width: 555,
		            valign: 'bottom',
		            align: 'center',
		            buttons: API.createJSON([{
		                key: i18n.def.sure,
		                value: function () {
	                       if ($("#filterForm").valid()) {
	                           var fileSize = 10 * 1024 * 1024;
	                           //10MB
	                           $.capFileUpload({
	                        	   handler: 'lms1601fileuploadhandler',
	                               fileElementId: "LMS1602S01",
	                               fileCheck: ['xls'],
		                           timeout: 5 * 60 * 1000,
		                           successMsg: false,
		                           data: {
		                        	   fileSize: fileSize,
									   fieldId: 'LMS1602S01',
		                               fileDesc: "",
		                               mainId: responseJSON.mainId
		                           },
		                           success: function (json) {
		                           		API.showPopMessage('', i18n.def['fileUploadSuccess'], function () {
		                           			$.thickbox.close();
		                           		});
		                           		$("#gridviewPeople").trigger("reloadGrid");
		                           }
		                    	});
		                    }
		                }
		            }, {
		            	key: i18n.def.cancel,
		                value: function () {
		                	$.thickbox.close()
		                }
		            }])
		     	});
		    });

		    //新增額度明細表聯行攤待比率
		    $("#newItemChildren3Bt").click(function(){
		    	BranchAcitonAF.query();
		    });
		 	//刪除額度明細表聯行攤待比率
		    $("#removeGridviewitemChildren3").click(function(){
		    	BranchAcitonAF.remove();
		    });
		    //變更分母
		    $("#changesShareRate2").click(function(){
		    	BranchAcitonAF.changesShareRate();
		    });
		    //變更攤貸行計算方式
		    $("[name=shareFlag]").click(function(){
		    	BranchAcitonAF.controlShow();
		    });
			 
			// 主從債務人視窗
			function peopleBox(cellvalue, options, rowData) {
				var $L162M01AForm = $("#L162M01AForm");

				// 初始化
				$L162M01AForm.reset().attr("formOid", "")
						.find("#custId,#dupNo").html("");
				// 初始化關係
				$("#the1,#the2,#the3").hide();
				$("[id^=rationSelect]").val("");
				$
						.ajax({
							handler : "lms1601m01formhandler",
							data : {
								formAction : "queryL162m01a",
								oid : rowData ? rowData.oid : "",
								mainId : responseJSON.mainId
							},
							success : function(obj) {
								$("#rKindM").val('');
								$L162M01AForm.setData(obj);
								// 設定額度序號是否可選 引進的不可選、新增的可
								if (obj.dataSrc == "1") {
									$L162M01AForm.find("#cntrNo").attr(
											"disabled", true).html(
											"<option value=" + DOMPurify.sanitize(obj.cntrNo) + ">"
													+ DOMPurify.sanitize(obj.cntrNo) + "</option>");
									$L162M01AForm.find("#custIdSelect").attr(
											"disabled", true);
								} else {
									$L162M01AForm.find("#cntrNo").removeAttr(
											"disabled").html(
											"<option value=''>"
													+ DOMPurify.sanitize(i18n.def.comboSpace)
													+ "</option>");
									$L162M01AForm.find("#custIdSelect")
											.removeAttr("disabled");
								}

								// if($L162M01AForm.find("#rId").val() == ""){
								// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
								$L162M01AForm.find("#rType").change();
								// }

								$("#custIdSelect").change();
								$("#rKindM").change();

								$("#cntrNo").children().each(function() {
									// alert("cntrno="+$(this).text());
									if ($(this).text() == obj.cntrNo) {
										// jQuery給法
										$(this).attr("selected", true); // 或是給"selected"也可
										// javascript給法
										// this.selected = true;

									}
								});

								$("#rKindM").children().each(
										function() {
											if (obj.rKindM == undefined
													|| obj.rKindM == "") {

												if ($(this).val() == '') {
													$(this).attr("selected",
															true); // 或是給"selected"也可
													$("#rKindM").change();
												}
											}

										});

								// 設定關係細項的值
								switch (obj.rKindM) {
								case "1":
									$("#rationSelect1").val(obj.rKindD);
									$("#the1").show().siblings("#the2,#the3")
											.hide();
									break;
								case "2":
									$("#rationSelect2").val(obj.rKindD);
									$("#the2").show().siblings("#the1,#the3")
											.hide();
									break;
								case "3":
									$("#rationSelect31").val(
											obj.rKindD.charAt(0));
									$("#rationSelect32").val(
											obj.rKindD.charAt(1));
									$("#the1,#the2").hide().siblings("#the3")
											.show();
									break;
								}

								// J-103-0299 Web e-Loan企金額度明細表保證人新增保證比例
								// $("#rType").change();

								// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
								if (responseJSON.readOnly == "true") {
									$L162M01AForm.find(".noHideBt").show();
								}

								//G-113-0036 新增 保證金額上限、當地客戶識別ID，海外分行才能填
								if(!$.isEmptyObject(obj.banktype)){
					            	if(obj.banktype == "5"){
					        			$(".trGrtAmt").show();
					        		}else{
					        			$(".trGrtAmt").hide();
					        			$("#grtAmt").val("");
					        			$("#localId").val("");
					        		}
								}
								toPeopleBox();

							}
						});
			}// close 開啟主從債務人資料表畫面

			function toPeopleBox() {
				var $L162M01AForm = $("#L162M01AForm");
				$("#peopleBox")
						.thickbox(
								{
									// L160M01A.title11=主從債務人資料表
									title : i18n.lms1601m01['L160M01A.title11'],
									width : 960,
									height : 500, // J-106-0029
									modal : true,
									i18n : i18n.def,
									buttons : {
										"saveData" : function() {
											var mainCustId = $L162M01AForm
													.find("#custIdSelect")
													.val();
											$L162M01AForm
													.find("#custId")
													.val(
															mainCustId
																	.substring(
																			0,
																			mainCustId.length - 1));
											$L162M01AForm
													.find("#dupNo")
													.val(
															mainCustId
																	.substring(
																			mainCustId.length - 1,
																			mainCustId.length));
											if (!$L162M01AForm.valid()) {
												return;
											}

											var rKindD = "";
											var rKindM = $("#rKindM").val();
											// 針對關係細項作處理
											switch (rKindM) {
											case "1":
												rKindD = $("#rationSelect1")
														.val();
												break;
											case "2":
												rKindD = $("#rationSelect2")
														.val();
												break;
											case "3":
												// 選擇關係兩個都要選
												if ($("#rationSelect31").val() == ""
														|| $("#rationSelect32")
																.val() == "") {
													return CommonAPI
															.showErrorMessage(i18n.lms1601m01["L160M01A.error2"]
																	+ i18n.lms1601m01['L162M01A.rKindM']);
												}
												rKindD = $("#rationSelect31")
														.val()
														+ $("#rationSelect32")
																.val();
												break;
											}

											if ($.trim(rKindD) == ""
													&& rKindM != "") {
												return CommonAPI
														.showErrorMessage(i18n.lms1601m01["L160M01A.error2"]
																+ i18n.lms1601m01['L162M01A.rKindM']);
											}

											// 檢查國別不可以為空
											if ($("#rCountry").val() == "") {

												return CommonAPI
														.showErrorMessage(i18n.lms1601m01["L162M01A.rCountry"]
																+ i18n.def['val.required']);
											}

											// J-106-0029-004 洗錢防制-動審表新增洗錢防制頁籤
											var custId = $L162M01AForm.find(
													"#custId").val(), dupNo = $L162M01AForm
													.find("#dupNo").val(), rId = $L162M01AForm
													.find("#rId").val(), rDupNo = $L162M01AForm
													.find("#rDupNo").val();
											// 當只有主債務人統編與從債務人統編相同者，其關係可為空白，其餘欄位皆不可空白
											if (!(custId == rId && dupNo == rDupNo)) {
												if ($.trim(rKindD) == ""
														&& $.trim(rKindM) == "") {
													return CommonAPI
															.showErrorMessage(i18n.lms1601m01['L162M01A.rKindM']
																	+ i18n.def['val.required']);
												}
											} else {
												if ($L162M01AForm
														.find("#rType").val() != "C") {
													// L160M01A.message48=主債務人統編與從債務人統編相同者，其相關身份應為
													// C、共同借款人
													return CommonAPI
															.showErrorMessage(i18n.lms1601m01['L160M01A.message48']);
												}

												if ($.trim(rKindD) != ""
														|| $.trim(rKindM) != "") {
													// L160M01A.message50=主債務人統編與從債務人統編相同者，其關係應為空白
													return CommonAPI
															.showErrorMessage(i18n.lms1601m01['L160M01A.message50']);
												}

											}

											$
													.ajax({
														handler : "lms1601m01formhandler",
														data : {
															formAction : "saveL162m01a",
															oid : $L162M01AForm
																	.attr("formOid"),
															rKindD : rKindD,
															L162M01AForm : JSON
																	.stringify($L162M01AForm
																			.serializeData())
														},
														success : function(obj) {
															if ($L162M01AForm
																	.attr("formOid") == "") {
																if (obj.formOid) {
																	$L162M01AForm
																			.attr(
																					"formOid",
																					obj.formOid);
																}
															}

															var rType = $L162M01AForm
																	.find(
																			"#rType")
																	.val();
															var dueDate = $L162M01AForm
																	.find(
																			"#dueDate")
																	.val();
															if ((rType == "N" || rType == "G")
																	&& dueDate == "") {
																// L160M01A.message62=本訊息僅提醒:配合民法第753條之1條訂，保證人若為「董事、監察人或其他有代表權之人而為該法人擔任保證人」者，必須輸入【董監事任期止日
																// 西元年YYYY-MM-DD
																API
																		.showMessage(i18n.lms1601m01["L160M01A.message62"]);
															}

															// saveSuccess=儲存成功
															API
																	.showMessage(i18n.def["saveSuccess"]);

															$("#gridviewPeople")
																	.trigger(
																			'reloadGrid');

														}
													});
										},
										"close" : function() {
											$.thickbox.close();
										}
									}
								});
				var auth = (responseJSON ? responseJSON.Auth : {}); // 權限
				if (auth.readOnly) {
					$("button#saveData").remove();
				}
			}

			var gridviewCntrnoInfo = $("#gridviewCntrnoInfo").iGrid({
				needPager : false,
				handler : 'lms1601gridhandler',
				height : "190px",
				width : "100%",
				multiselect : true,
				sortname : 'printSeq|custId|cntrNo',
				sortorder : 'asc|asc|asc',
				postData : {
					formAction : "queryCntrnoInfo"
				},
				colModel : [ {
					colHeader : i18n.lms1601m01['L160M01A.custId'],// "借戶統編",
					name : 'custId',
					width : '80px',
					sortable : true,
					align : "left",
					formatter : 'click',
					onclick : cntrnoInfoBox
				}, {
					colHeader : i18n.lms1601m01['L160M01A.cntrNo'],// "額度序號",
					name : 'cntrNo',
					width : '80px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.currentApplyCurr'],// "幣別",
					name : 'currentApplyCurr',
					width : '40px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.currentApplyAmt'],// "現請額度（元）",
					name : 'currentApplyAmt',
					width : '80px',
					sortable : true,
					align : "right",
					formatter : 'currency',
					formatoptions : {
						thousandsSeparator : ",",
						removeTrailingZero : true,
						decimalPlaces : 2
					// 小數點到第幾位
					}
				}, {
					colHeader : i18n.lms1601m01['L160M01A.propertyDscr'],// "額度性質",
					name : 'propertyDscr',
					width : '100px',
					sortable : true,
					align : "left"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.snoKindDscr'],// "額度控管種類",
					name : 'snoKindDscr',
					width : '100px',
					sortable : true,
					align : "left"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.caseTypeDscr'],// "案件性質",
					name : 'caseTypeDscr',
					width : '100px',
					sortable : true,
					align : "left"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.quotaCurr'],// "聯貸幣別",
					name : 'quotaCurr',
					width : '60px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.quotaAmtDscr'],// "聯貸總額度（元）",
					name : 'quotaAmtDscr',
					width : '80px',
					sortable : true,
					align : "left"
				}, {
					colHeader : "&nbsp",// "檢核欄位",
					name : 'chkYN',
					width : 20,
					sortable : true,
					align : "center"
				}, {
					name : 'printSeq',
					hidden : true
				}, {
					name : 'oid',
					hidden : true
				}, {
					name : 'uid',
					hidden : true
				} ],
				ondblClickRow : function(rowid) {
					var data = gridviewCntrnoInfo.getRowData(rowid);
					cntrnoInfoBox(null, null, data);
				}
			});

			function cntrnoInfoBox(cellvalue, options, rowData) {
				var $L161M01AForm = $("#L161M01AForm");

				// 初始化表單物件
				$L161M01AForm.reset();

				initClearLand($L161M01AForm);

				// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要, 配合新增相關作業
				$L161M01AForm.find("#headItem1Tr", "#headItem1Tr_1",
						"#headItem1Tr_1_1").hide();

				//J-111-0506_05097_B1001 Web e-Loan企金授信動審表增加授信作業手續費之欄位
				$L161M01AForm.find(".isOperationFeeTrTr").hide();
				
				$
						.ajax({
							handler : "lms1601m01formhandler",
							data : {
								formAction : "queryInitData"
							},
							success : function(initObj) {
								// J-109-0077_05097_B1008 因應政府嚴重特殊傳染性肺炎紓困方案實施需要,
								// 配合新增相關作業
								gRescueRateItem = initObj.rescueRateItem;
								gOldRescueCaseItem = initObj.oldRescueCaseItem;
								gRescueRateItemSub = initObj.rescueRateItemSub;

								//J-112-0148_05097_B1002 Web e-Loan企金授信新增經濟部協助中小型事業疫後振興專案貸款暨經濟部協助中小企業轉型發展專案貸款
                                gRescueNoItem = initObj.rescueNoItem;
                                gEmpCountItem = initObj.empCountItem;
                                gRescueSnItem = initObj.rescueSnItem;
                                
								if (rowData) {
									$
											.ajax({
												handler : "lms1601m01formhandler",
												data : {
													formAction : "queryL161s01a",
													oid : rowData.oid
												},
												success : function(obj) {

													$L161M01AForm
															.injectData(obj);

													$L161M01AForm
															.find(
																	"input[name=unitCase]:checked")
															.trigger("click");
													$L161M01AForm
															.find(
																	"input[name=unitMega]:checked")
															.trigger("click");
													$L161M01AForm.find(
															"#coKind").trigger(
															"change");
													$(
															"input[name='isNonSMEProjLoan']")
															.trigger("change");
													// J-106-0082-001 Web
													// e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
													$("input[name='inSmeFg']")
															.trigger("change");

													// J-109-0077_05097_B1001
													// 因應政府嚴重特殊傳染性肺炎紓困方案實施需要,
													// 配合新增相關作業
													$("input[name='isRescue']")
															.trigger("change");
													$("#rescueItem").trigger(
															"change");
													// J-109-0811_05097_B1001
													// 配合「嚴重特殊傳染性肺炎防治及妤困振興特別條例」施行期間調整，動審表新增央行優惠利率融通期限
													$("input[name='isCbRefin']")
															.trigger("change");

													if (obj.toAloan2CanShow == "Y") {
														$('#lms160s0301Tab03')
																.show();
														$('#lms160s0301C')
																.show();
													} else {
														$('#lms160s0301Tab03')
																.hide();
														$('#lms160s0301C')
																.hide();
													}

													// J-110-0540_05097_B1001
													// Web
													// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
													var l160m01a_tType = obj.l160m01a_tType;
													if (l160m01a_tType == "3") {
														// 詳額度動用資訊一覽表
														$('#lms160s0301Tab05')
																.show();
														$('#lms160s0301E')
																.show();
													} else {
														$('#lms160s0301Tab05')
																.hide();
														$('#lms160s0301E')
																.hide();
													}
													initTtype3($L161M01AForm);

													// J-103-0202-005
													// 遠匯換匯比照選擇權，以交易額度(名目本金*風險係數)來簽案
													var showDervApplyAmtType = obj.isDerivatives;
													if (showDervApplyAmtType == "Y") {
														$L161M01AForm
																.find(
																		"#showDervApplyAmtType")
																.show();
													} else {
														$L161M01AForm
																.find(
																		"#showDervApplyAmtType")
																.hide();
													}

													var cntrNo = $L161M01AForm
															.find("#cntrNo")
															.val();
													var uid = $L161M01AForm
															.find("#uid").val();
													$("#gridviewBranch")
															.jqGrid(
																	"setGridParam",
																	{
																		postData : {
																			formAction : "queryBranch",
																			cntrNo : cntrNo,
																			uid : uid
																		}
																	})
															.trigger(
																	"reloadGrid");
													// G-113-0036 簽案額度明細聯行攤貸比例
													$(BranchAcitonAF.gridId).jqGrid("setGridParam",{
														postData : {
															formAction : "queryL140m01e_af",
															cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
															cntrNo : $("#L161M01AForm").find("#cntrNo").val()
														}
													}).trigger("reloadGrid");
													$(BranchAcitonAF.amtgrId).jqGrid("setGridParam",{
														postData : {
															formAction : "queryL140m01e_af",
															cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
															cntrNo : $("#L161M01AForm").find("#cntrNo").val()
														}
													}).trigger("reloadGrid");

													var docCode = $("#docCode")
															.val();
													// 當分行代號為025時，要出現#branchShow025_1
													// #branchShow025_2的li
													var brNo = userInfo ? userInfo.unitNo
															: "";
													if (brNo == "025"
															|| brNo == "900") {
														$L161M01AForm
																.find(
																		"#branchShow025_1,#branchShow025_2")
																.show();
													}

													$("#reViewDateKind")
															.change();
													$("#reViewChgKind")
															.change();

													// J-108-0083_05097_B1001
													// 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
													if (obj.adoptFg) {
														var vals = obj.adoptFg
																.split("|");
														for ( var i in vals) {
															$(
																	"[name=adoptFg][value="
																			+ vals[i]
																			+ "]")
																	.attr(
																			"checked",
																			true);
														}
													}

													// $L161M01AForm.find("input:radio[name='isClearLand']").trigger('change');
													// $L161M01AForm.find("#showClearLand").find("input:radio"
													// ).trigger("change");
													// $L161M01AForm.find("#showClearLand").find("input:checkbox").trigger("change");

													// $L161M01AForm.find("#L140M01M_clear_land_ctrl").find("input").triggerHandler("change");
													$L161M01AForm
															.find(
																	"#L140M01M_clear_land_ctrl")
															.find("input:radio")
															.trigger("change");
													$L161M01AForm
															.find(
																	"#L140M01M_clear_land_ctrl")
															.find(
																	"input:checkbox")
															.trigger("change");

													//J-112-0366_12473_B1001 若為批次保證: 1.保證成數欄位隱藏  2.[信保基金保證書發文日期]文字改為[批次保證額度批覆書發文日期],欄位共用
													var l140m01a_gutType = obj.l140m01a_gutType;
													if('3' != l140m01a_gutType){
											    		$(".showGutTypeIsNot3").show();
											    		$(".showGutTypeIs3").hide();
											    	} else {
											    		$(".showGutTypeIsNot3").hide();
											    		$(".showGutTypeIs3").show();
											    	};
													
													$("#cntrnoInfoBox")
															.thickbox(
																	{
																		// L160M01A.title10=額度動用資訊一覽表
																		title : i18n.lms1601m01['L160M01A.title10'],
																		width : 900, // J-110-0540_05097_B1001
																						// Web
																						// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
																		height : 500,
																		modal : true,
																		i18n : i18n.def,
																		buttons : {
																			"saveData" : function() {

																				// var
																				// rescueNo
																				// =
																				// $("#rescueNo").val();
																				// if (
																				// !rescueNo.match(/^[a-zA-Z]{1}[a-zA-Z0-9
																				// ]+$/)
																				// ) {
																				// //J-109-0265_05097_B1001
																				// Web
																				// e-Loan企金動審表掛件文號欄位輸入限制
																				// //L160M01A.message112=掛件文號格式錯誤
																				// ，僅能輸入
																				// 英文字母
																				// 與數字，且第一碼必須為英文。
																				// return
																				// CommonAPI.showMessage(i18n.lms1601m01["L160M01A.message112"]);
																				// }

																				// J-108-0083_05097_B1001
																				// 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
																				var isClearLand = $(
																						"input[name='isClearLand']:radio:checked")
																						.val();
																				var isChgStDate = $(
																						"input[name='isChgStDate']:radio:checked")
																						.val();
																				var isChgRate = $(
																						"input[name='isChgRate']:radio:checked")
																						.val();

																				if (!isClearLand) {
																					return CommonAPI
																							.showMessage(i18n.lms1601m01["L140M01M.isClearLand"]
																									+ i18n.def['val.required']);
																				}

																				if (isClearLand == "Y") {

																					if (!isChgStDate) {
																						return CommonAPI
																								.showMessage(i18n.lms1601m01["L140M01M.isChgStDate"]
																										+ i18n.def['val.required']);
																					}

																					if ($L161M01AForm
																							.find(
																									"#fstDate")
																							.val() == "") {
																						return CommonAPI
																								.showMessage(i18n.lmsl140m01m["L140M01M.fstDate"]
																										+ i18n.def['val.required']);
																					}

																					if ($L161M01AForm
																							.find(
																									"#lstDate")
																							.val() == "") {
																						return CommonAPI
																								.showMessage(i18n.lmsl140m01m["L140M01M.lstDate"]
																										+ i18n.def['val.required']);
																					}

																					if (isChgStDate == "Y") {
																						if ($L161M01AForm
																								.find(
																										"#cstDate")
																								.val() == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.cstDate"]
																											+ i18n.def['val.required']);
																						}

																						if ($L161M01AForm
																								.find(
																										"#cstReason")
																								.val() == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.cstReason"]
																											+ i18n.def['val.required']);
																						}

																						var hasAdoptFg = "";
																						$(
																								"[name=adoptFg]:checked")
																								.each(
																										function(
																												v,
																												k) {
																											hasAdoptFg = "Y";
																										});
																						if (hasAdoptFg == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.adoptFg"]
																											+ i18n.def['val.required']);
																						}
																					} else {
																						if (!isChgRate) {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.isChgRate"]
																											+ i18n.def['val.required']);
																						}
																					}

																					if (isChgRate == "Y"
																							|| (isChgStDate == "Y" && $L161M01AForm
																									.find(
																											"[name=adoptFg][value=3]")
																									.attr(
																											"checked"))) {
																						if ($L161M01AForm
																								.find(
																										"#rateAdd")
																								.val() == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.rateAdd"]
																											+ i18n.def['val.required']);
																						}

																						if ($L161M01AForm
																								.find(
																										"#custRoa")
																								.val() == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.custRoa"]
																											+ i18n.def['val.required']);
																						}

																						if ($L161M01AForm
																								.find(
																										"#relRoa")
																								.val() == "") {
																							return CommonAPI
																									.showMessage(i18n.lms1601m01["L140M01M.relRoa"]
																											+ i18n.def['val.required']);
																						}
																					}

																					// 重新計算是否符合本行規定
																					if (isChgStDate == "Y"
																							|| isChgRate == "Y") {
																						checkIsLegal();
																					}

																				}

																				// J-110-0540_05097_B1001
																				// Web
																				// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
																				if (l160m01a_tType == "3") {
																					if ($(
																							"#useSelect_s01a")
																							.val() == "1"
																							&& ($(
																									"#useFromDate_s01a")
																									.val() > $(
																									"#useEndDate_s01a")
																									.val())) {
																						// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
																						CommonAPI
																								.showErrorMessage(i18n
																										.msg(
																												'EFD3026')
																										.replace(
																												/\$\\{colName\\}/,
																												i18n.lms1601m01['L160M01A.useDate']));
																						return false;
																					}

																					if ($(
																							"#tType_s01a")
																							.val() == "2"
																							&& $(
																									"#lnSelect_s01a")
																									.val() == "1"
																							&& ($(
																									"#lnFromDate_s01a")
																									.val() > $(
																									"#lnEndDate_s01a")
																									.val())) {
																						// EFD3026=ERROR|$\{colName\}起始日期不能大於結束日期|
																						CommonAPI
																								.showErrorMessage(i18n
																										.msg(
																												'EFD3026')
																										.replace(
																												/\$\\{colName\\}/,
																												i18n.lms1601m01['L160M01A.lnDate']));
																						return false;
																					}

																				}

																				if (!$L161M01AForm
																						.valid()) {

																					$(
																							"input.data-error,select.data-error")
																							.eq(
																									0)
																							.focus();

																					return;
																				}

																				var cntrNo = $L161M01AForm
																						.find(
																								"#cntrNo")
																						.val();
																				var uid = $L161M01AForm
																						.find(
																								"#uid")
																						.val();
																				FormAction.open = true;
																				$
																						.ajax({
																							handler : "lms1601m01formhandler",
																							data : {
																								formAction : "saveL161s01a",
																								mainId : responseJSON.mainId,
																								cntrNo : cntrNo,
																								uid : uid,
																								L161M01AForm: JSON.stringify($("#L161M01AForm").serializeData())
																							},
																							success : function(
																									obj) {
																								FormAction.open = false;
																								if (obj.l140m01eAmt != undefined && obj.l140m01eAmt != 0) {
																				                	//當攤貸時還有餘額，要跳出餘額修改視窗
																				                    if (obj.l140m01eAmt != 0) {
																				                       	BranchAcitonAF.l140m01eAmtBox(obj.l140m01eAmt);
																				                    }
																				                }
																								if (obj.errorMsg != "") {
																									CommonAPI
																											.showErrorMessage(obj.errorMsg);
																								}
																								if (obj.suggestMsg != "") {
																									CommonAPI
																											.showErrorMessage(obj.suggestMsg);
																								}
																								$(
																										"#gridviewCntrnoInfo")
																										.trigger(
																												"reloadGrid");

																								if (obj.errorMsg == "") {
																									API
																											.showMessage(i18n.def["saveSuccess"]);
																								}
																							}
																						});
																			},
																			"close" : function() {
																				$.thickbox
																						.close();
																			}
																		}
																	});
												}
											});
								} else {
									CommonAPI
											.showErrorMessage(i18n.lms1601m01["L160M01A.message64"]);

								}
							}
						});

			}

			var copyL161S01BGrid = $("#copyL161S01BGrid").iGrid({
				needPager : false,
				localFirst : true,
				handler : 'lms1601gridhandler',
				height : "190px",
				width : "100%",
				multiselect : false,
				sortname : 'printSeq|custId|cntrNo',
				sortorder : 'asc|asc|asc',
				postData : {
					formAction : "queryCntrnoInfo"
				},
				colModel : [ {
					colHeader : i18n.lms1601m01['L160M01A.custId'],// "借戶統編",
					name : 'custId',
					width : '80px',
					sortable : true,
					align : "left",
					formatter : 'click'
				}, {
					colHeader : i18n.lms1601m01['L160M01A.cntrNo'],// "額度序號",
					name : 'cntrNo',
					width : '80px',
					sortable : true,
					align : "center"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.snoKindDscr'],// "額度控管種類",
					name : 'snoKindDscr',
					width : '150px',
					sortable : true,
					align : "left"
				}, {
					colHeader : i18n.lms1601m01['L160M01A.caseTypeDscr'],// "案件性質",
					name : 'caseTypeDscr',
					width : '150px',
					sortable : true,
					align : "left"
				}, {
					colHeader : "&nbsp",// "檢核欄位",
					name : 'chkYN',
					width : 20,
					sortable : true,
					align : "center"
				}, {
					name : 'printSeq',
					hidden : true
				}, {
					name : 'oid',
					hidden : true
				}, {
					name : 'uid',
					hidden : true
				} ]
			});

			function copyBranchBox() {
				// TODO

				var $copyL161S01BGrid = $("#copyL161S01BGrid");
				var $L161M01AForm = $("#L161M01AForm");
				$copyL161S01BGrid.trigger("reloadGrid");

				$("#selectCntrnoInfoBox")
						.thickbox(
								{
									// L160M01A.title10=額度動用資訊一覽表
									title : i18n.lms1601m01['L160M01A.title10'],
									width : 600,
									height : 400,
									modal : true,
									i18n : i18n.def,
									buttons : {
										"sure" : function() {
											var row1 = $copyL161S01BGrid
													.getGridParam('selrow');

											if (row1 == "") {
												// action_005=請先選取一筆以上之資料列
												return CommonAPI
														.showErrorMessage(i18n.def['action_005']);
											}

											var data1 = $copyL161S01BGrid
													.getRowData(row1);
											var copyFromUid = data1.uid;

											var copyToUid = $L161M01AForm.find(
													"#uid").val();

											FormAction.open = true;
											$
													.ajax({
														handler : "lms1601m01formhandler",
														data : {
															formAction : "copyL161s01b",
															mainId : responseJSON.mainId,
															copyToUid : copyToUid,
															copyFromUid : copyFromUid
														},
														success : function(obj) {
															FormAction.open = false;
															$("#gridviewBranch")
																	.trigger(
																			"reloadGrid");
															$.thickbox.close();
														}
													});
										},
										"close" : function() {
											$.thickbox.close();
										}
									}
								});
			}

			// J-106-0029-003 洗錢防制-新增實質受益人
			/** 關係人-自然人 */
			$("#gridviewNatural").iGrid({
				height : 200,
				handler : "lms1201gridhandler",
				localFirst : true,
				sortname : 'seqNum|createTime',
				sortorder : 'asc|asc',
				postData : {
					formAction : "queryL120s01p",
					mainId : responseJSON.mainId,
					type : "1"
				},
				multiselect : true,
				hideMultiselect : false,
				colModel : [ {
					colHeader : i18n.lms1601m01["L120S01p.custIdAndDupNo"],// "統編(含重覆序號)"
					width : 80,
					name : 'rId',
					sortable : true,
					formatter : 'click',
					onclick : newToglePersonBT
				}, {
					colHeader : i18n.lms1601m01["L120S01p.conPersonName"],// "名稱",
					name : 'rName',
					width : 100,
					sortable : false
				}, {
					colHeader : i18n.lms1601m01["L120S01p.conPersonEName"],// "英文名稱",
					name : 'rEName',
					width : 100,
					sortable : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				}, {
					colHeader : "rType",
					name : 'rType',
					hidden : true
				} ],
				ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
					var data = $("#gridviewNatural").getRowData(rowid);
					newToglePersonBT(null, null, data);
				}
			});

			// J-106-0029-003 洗錢防制-新增實質受益人
			/** 關係人-法人 */
			$("#gridviewCorporate").iGrid({
				height : 200,
				handler : "lms1201gridhandler",
				localFirst : true,
				sortname : 'seqNum|createTime',
				sortorder : 'asc|asc',
				postData : {
					formAction : "queryL120s01p",
					mainId : responseJSON.mainId,
					type : "2"
				},
				multiselect : true,
				hideMultiselect : false,
				colModel : [ {
					colHeader : i18n.lms1601m01["L120S01p.custIdAndDupNo"],// "統編(含重覆序號)"
					width : 80,
					name : 'rId',
					sortable : true,
					formatter : 'click',
					onclick : newToglePersonBT
				}, {
					colHeader : i18n.lms1601m01["L120S01p.conPersonName"],// "名稱",
					name : 'rName',
					width : 100,
					sortable : true
				}, {
					colHeader : i18n.lms1601m01["L120S01p.conPersonEName"],// "英文名稱",
					name : 'rEName',
					width : 100,
					sortable : true
				}, {
					colHeader : "oid",
					name : 'oid',
					hidden : true
				}, {
					colHeader : "rType",
					name : 'rType',
					hidden : true
				} ],
				ondblClickRow : function(rowid) { // 當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
					var data = $("#gridviewCorporate").getRowData(rowid);
					newToglePersonBT(null, null, data);
				}
			});

			// J-106-0029-003 洗錢防制-新增實質受益人
			// 自動帶入使用者姓名
			$("#L120S01PForm").find("#rId,#rDupNo").blur(
					function() {
						var custId = $("#L120S01PForm").find("#rId").val();
						var dupNo = $("#L120S01PForm").find("#rDupNo").val();
						if ($.trim(custId).length > 0
								&& $.trim(dupNo).length > 0) {
							$.ajax({
								handler : "amlrelateformhandler",
								action : "getMisCustData",
								data : {
									custId : custId,
									dupNo : dupNo
								},
								success : function(obj) {
									if (!$.isEmptyObject(obj)) {
										$("#L120S01PForm").find("#rName").val(
												obj.custName);
										$("#L120S01PForm").find("#rEName").val(
												obj.custEName);
									}
								}
							});
						}
					});

			// J-106-0029-003 洗錢防制-新增實質受益人
			/** 登錄實質受益人 */
			$("#L162M01AForm").find("#toglePersonBT").click(function() {
				toglePersonBtInner("7");
			});

			// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
			/** 登錄高階管理人員 */
			$("#L162M01AForm").find("#toglePersonBT_10").click(function() {
				toglePersonBtInner("10");
			});

			// J-108-0039_05097_B1001 Web e-Loan
			// 國內企金授信系統簽報、動審AML頁籤將借戶之「具控制權人」納入應查詢比對黑名單之對象。
			/** 登錄具控制權人 */
			$("#L162M01AForm").find("#toglePersonBT_11").click(function() {
				toglePersonBtInner("11");
			});

			// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
			var $formObject = $("#L161M01AForm");

			$formObject.find("#btnApplyClearLand").click(function() {
				applyClearLand();
			});

			$formObject.find("#btnApplyClearLandRoa").click(function() {
				applyClearLandRoa();
			});

			$formObject.find("#btnApplyIsLegal").click(function() {
				applyIsLegal();
			});

			// J-109-0209_05097_B1001 e-Loan國內企金動審表增加借戶性質註記等進扣帳對象檢核項目

			// J-106-0082-001 Web e-Loan國內企金授信系統，額度明細表新增中小企業創新發展專案貸款
			$("input[name='isSole']").change(function() {
				var isSole = $("input[name='isSole']:radio:checked").val();
				if (isSole == "Y") {
					$(".showSole").show();
				} else {
					$(".showSole").hide();
				}
			});

			// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
			// 授信契約書 中 短期 切換
			$('#useSelect_s01a,#lnSelect_s01a,#tType_s01a').change(
					function() {
						switch ($(this).attr("id")) {
						case "useSelect_s01a": // 動用期限切換
							$("#the" + DOMPurify.sanitize($(this).val()) + "_s01a").show()
									.siblings(".the_s01a").hide()
									.find(":input").val("");
							break;
						case "lnSelect_s01a": // 授信期限切換
							$("#two" + DOMPurify.sanitize($(this).val()) + "_s01a").show()
									.siblings(".two_s01a").hide()
									.find(":input").val("");
							break;
						case "tType_s01a":// 授信契約書
							// J-110-0540_05097_B1001 Web
							// e-Loan企金授信配合調整E-loan系統動用審核表部分內容
							$(this).val() == "1" ? $("#long_s01a").hide() : $(
									"#long_s01a").show();
							break;
						}
					});

			// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
			$("#L161M01AForm").find("#cacuRescueChgRateEffectDate").click(
					function() {
						cacuRescueChgRateEffectDate();
					});
			
			//G-113-0036 額度動用資訊>聯貸案參貸比率一覽>聯貸案參貸比率資訊> 給號新號，舊號的隱藏條件
		    $("input[name=numberType]").change(function(){
		        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
		        var type = $cntrNoForm.find("#cntrNoType").val(type);
		        $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
		        //海外不行選輸入分行 和 是否遠匯 
		        var value = $(this).val();
		        switch (value) {
		            case "1":
		                if (type != "5") {
		                    $cntrNoForm.find(".ForInSide").show();
		                }
		                $("#showBrNoTr").show();
		                break;
		            case "2":
		                $cntrNoForm.find(".ForOriginal").show();
		                break;
		        }
		    });

		});

// J-107-0070-001 Web e-Loan 國內徵信、簽報、動審AML頁籤請將「高階管理人員」納入應查詢比對黑名單之對象。
function toglePersonBtInner(rType) {
	// var rType = "7"; //實質受益人
	// var rType = "10"; //高階管理人員
	// var rType = "11"; //具控制權人

	var $L162M01AForm = $("#L162M01AForm");

	if (!$L162M01AForm.valid()) {
		return;
	}

	var fieldName = "";
	var applyFormActionName = "";
	if (rType == "10") {
		fieldName = "seniorMgr";
		applyFormActionName = "applySeniorMgrData";
	} else if (rType == "11") {
		fieldName = "ctrlPeo";
		applyFormActionName = "applyCtrlPeoData";
	} else {
		fieldName = "beneficiary";
		applyFormActionName = "applyBeneficiaryData";
	}

	// 讓每次開起box都是第一頁
	$("#toglePersonTabs").tabs({
		selected : 0
	});

	$("#gridviewNatural")
			.jqGrid(
					"setGridParam",
					{// 重新設定grid需要查到的資料
						postData : {
							formAction : "queryL120s01p",
							mainId : responseJSON.mainId,
							type : "1",
							custId : $L162M01AForm.find("#rId").val(),
							dupNo : $L162M01AForm.find("#rDupNo").val(),
							rType : rType
						},
						search : true,
						loadComplete : function() {

							$("#gridviewCorporate")
									.jqGrid(
											"setGridParam",
											{// 重新設定grid需要查到的資料
												postData : {
													formAction : "queryL120s01p",
													mainId : responseJSON.mainId,
													type : "2",
													custId : $L162M01AForm
															.find("#rId").val(),
													dupNo : $L162M01AForm.find(
															"#rDupNo").val(),
													rType : rType
												},
												search : true,
												loadComplete : function() {

													$('#gridviewNatural')
															.jqGrid(
																	'setGridParam',
																	{
																		loadComplete : function() {
																			// 執行完後把loadComplete清空，要不然GRID
																			// 的REFRESH也會觸發上面的setSelection
																		}
																	});
													$('#gridviewCorporate')
															.jqGrid(
																	'setGridParam',
																	{
																		loadComplete : function() {
																			// 執行完後把loadComplete清空，要不然GRID
																			// 的REFRESH也會觸發上面的setSelection
																		}
																	});

													var btnAction = API
															.createJSON([ {
																key : i18n.def['close'],
																value : function() {
																	$.thickbox
																			.close();
																}
															} ]);

													if (responseJSON.readOnly != "true") {

														if (userInfo.ssoUnitNo == "900") {
															btnAction = API
																	.createJSON([
																			{
																				// 引進
																				key : i18n.lms1601m01['L120S01p.btnApply'],
																				value : function() {

																					var countN = $(
																							"#gridviewNatural")
																							.jqGrid(
																									'getGridParam',
																									'records');
																					var countC = $(
																							"#gridviewCorporate")
																							.jqGrid(
																									'getGridParam',
																									'records');
																					if (countN
																							+ countC > 0) {
																						// confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
																						CommonAPI
																								.confirmMessage(
																										i18n.def["confirmBeforeDeleteAll"],
																										function(
																												b) {
																											if (b) {
																												// 是的function
																												$
																														.ajax({
																															handler : "amlrelateformhandler",
																															data : {// 把資料轉成json
																																formAction : applyFormActionName,
																																mainId : responseJSON.mainId,
																																custId : $L162M01AForm
																																		.find(
																																				"#rId")
																																		.val(),
																																dupNo : $L162M01AForm
																																		.find(
																																				"#rDupNo")
																																		.val()
																															},
																															success : function(
																																	responseData) {
																																$(
																																		'#gridviewNatural')
																																		.trigger(
																																				'reloadGrid');
																																$(
																																		'#gridviewCorporate')
																																		.trigger(
																																				'reloadGrid');
																																if (responseData.errMsg) {
																																	return CommonAPI
																																			.showMessage(responseData.errMsg);
																																}

																															}
																														});
																											}
																										});
																					} else {
																						$
																								.ajax({
																									handler : "amlrelateformhandler",
																									data : {// 把資料轉成json
																										formAction : applyFormActionName,
																										mainId : responseJSON.mainId,
																										custId : $L162M01AForm
																												.find(
																														"#rId")
																												.val(),
																										dupNo : $L162M01AForm
																												.find(
																														"#rDupNo")
																												.val()
																									},
																									success : function(
																											responseData) {
																										$(
																												'#gridviewNatural')
																												.trigger(
																														'reloadGrid');
																										$(
																												'#gridviewCorporate')
																												.trigger(
																														'reloadGrid');
																										if (responseData.errMsg) {
																											return CommonAPI
																													.showMessage(responseData.errMsg);
																										}
																									}
																								});
																					}

																				}
																			// 只能引進0024
																			},
																			{
																				key : i18n.def['newData'],
																				value : function() {
																					newToglePersonBT(
																							null,
																							null,
																							null,
																							rType);
																				}
																			},
																			{
																				key : i18n.def['del'],
																				value : function() {

																					var id1 = $(
																							"#gridviewNatural")
																							.getGridParam(
																									'selarrrow');
																					var id2 = $(
																							"#gridviewCorporate")
																							.getGridParam(
																									'selarrrow');
																					var data1 = [];
																					var data2 = [];
																					if (id1.length == 0
																							&& id2.length == 0) {
																						// TMMDeleteError=請先選擇需修改(刪除)之資料列
																						return CommonAPI
																								.showMessage(i18n.def["TMMDeleteError"]);
																					}

																					// confirmDelete=是否確定刪除?
																					CommonAPI
																							.confirmMessage(
																									i18n.def["confirmDelete"],
																									function(
																											b) {
																										if (b) {
																											if (id1.length > 0) {
																												for ( var i = 0; i < id1.length; i++) {
																													data1[i] = $(
																															"#gridviewNatural")
																															.getRowData(
																																	id1[i]).oid;
																												}
																											}

																											if (id2.length > 0) {
																												for ( var i = 0; i < id2.length; i++) {
																													data2[i] = $(
																															"#gridviewCorporate")
																															.getRowData(
																																	id2[i]).oid;
																												}
																											}

																											$
																													.ajax({
																														handler : "amlrelateformhandler",
																														data : {// 把資料轉成json
																															formAction : "deleteL120s01p",
																															oids : data1
																																	.concat(data2),
																															mainId : responseJSON.mainId,
																															showMsg : true
																														},
																														success : function(
																																responseData) {
																															$(
																																	'#gridviewNatural')
																																	.trigger(
																																			'reloadGrid');
																															$(
																																	'#gridviewCorporate')
																																	.trigger(
																																			'reloadGrid');
																														}
																													});
																										}
																									});
																				}
																			},
																			{
																				key : i18n.def['close'],
																				value : function() {

																					// 關閉的時候將相關人寫到畫面上
																					// guarantor

																					var corpId = $(
																							"#gridviewCorporate")
																							.jqGrid(
																									'getDataIDs');
																					var natId = $(
																							"#gridviewNatural")
																							.jqGrid(
																									'getDataIDs');
																					var data = "";
																					var sign;
																					var guaIndex = 0;
																					var needIndex = false;
																					if (natId.length
																							+ corpId.length > 1) {
																						needIndex = true;
																					}

																					for ( var i = 0; i < natId.length; i++) {
																						(data.length > 0) ? sign = "、"
																								: sign = "";
																						guaIndex = guaIndex + 1;

																						if (needIndex == true) {
																							data = data
																									+ sign
																									+ guaIndex
																									+ "."
																									+ $(
																											"#gridviewNatural")
																											.getRowData(
																													natId[i]).rName;// +
																																	// guaPercentStr;
																						} else {
																							data = data
																									+ sign
																									+ $(
																											"#gridviewNatural")
																											.getRowData(
																													natId[i]).rName;// +
																																	// guaPercentStr;
																						}
																					}
																					for ( var i = 0; i < corpId.length; i++) {
																						(data.length > 0) ? sign = "、"
																								: sign = "";
																						guaIndex = guaIndex + 1;

																						if (needIndex == true) {
																							data = data
																									+ sign
																									+ guaIndex
																									+ "."
																									+ $(
																											"#gridviewCorporate")
																											.getRowData(
																													corpId[i]).rName;// +
																																		// guaPercentStr;
																						} else {
																							data = data
																									+ sign
																									+ $(
																											"#gridviewCorporate")
																											.getRowData(
																													corpId[i]).rName;// +
																																		// guaPercentStr;
																						}
																					}

																					// J-109-0067_05097_B1001
																					// Web
																					// e-Loan
																					// 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
																					// if
																					// (!data
																					// ||
																					// $.trim(data)
																					// ==
																					// "")
																					// {
																					// data
																					// =
																					// i18n.lms1601m01['nohave']
																					// }

																					$
																							.ajax({
																								handler : "amlrelateformhandler",
																								action : "saveL120s01pBeneficiaryStr",
																								data : {
																									mainId : responseJSON.mainId,
																									custId : $L162M01AForm
																											.find(
																													"#rId")
																											.val(),
																									dupNo : $L162M01AForm
																											.find(
																													"#rDupNo")
																											.val(),
																									beneficiary : data,
																									rType : rType,
																									callFrom : "2" // 動審表
																								},
																								success : function(
																										obj) {
																									// J-109-0067_05097_B1001
																									// Web
																									// e-Loan
																									// 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
																									// $("#"+fieldName).val(data);
																									$(
																											"#"
																													+ fieldName)
																											.val(
																													obj.beneficiary);
																									$.thickbox
																											.close();
																								}
																							});
																				}
																			} ]);
														} else {
															// 一般分行只能引進0024
															btnAction = API
																	.createJSON([
																			{
																				// 引進
																				key : i18n.lms1601m01['L120S01p.btnApply'],
																				value : function() {

																					var countN = $(
																							"#gridviewNatural")
																							.jqGrid(
																									'getGridParam',
																									'records');
																					var countC = $(
																							"#gridviewCorporate")
																							.jqGrid(
																									'getGridParam',
																									'records');
																					if (countN
																							+ countC > 0) {
																						// confirmBeforeDeleteAll=執行時會刪除已存在之資料，是否確定執行？
																						CommonAPI
																								.confirmMessage(
																										i18n.def["confirmBeforeDeleteAll"],
																										function(
																												b) {
																											if (b) {
																												// 是的function
																												$
																														.ajax({
																															handler : "amlrelateformhandler",
																															data : {// 把資料轉成json
																																formAction : applyFormActionName,
																																mainId : responseJSON.mainId,
																																custId : $L162M01AForm
																																		.find(
																																				"#rId")
																																		.val(),
																																dupNo : $L162M01AForm
																																		.find(
																																				"#rDupNo")
																																		.val()
																															},
																															success : function(
																																	responseData) {
																																$(
																																		'#gridviewNatural')
																																		.trigger(
																																				'reloadGrid');
																																$(
																																		'#gridviewCorporate')
																																		.trigger(
																																				'reloadGrid');
																																if (responseData.errMsg) {
																																	return CommonAPI
																																			.showMessage(responseData.errMsg);
																																}

																															}
																														});
																											}
																										});
																					} else {
																						$
																								.ajax({
																									handler : "amlrelateformhandler",
																									data : {// 把資料轉成json
																										formAction : applyFormActionName,
																										mainId : responseJSON.mainId,
																										custId : $L162M01AForm
																												.find(
																														"#rId")
																												.val(),
																										dupNo : $L162M01AForm
																												.find(
																														"#rDupNo")
																												.val()
																									},
																									success : function(
																											responseData) {
																										$(
																												'#gridviewNatural')
																												.trigger(
																														'reloadGrid');
																										$(
																												'#gridviewCorporate')
																												.trigger(
																														'reloadGrid');
																										if (responseData.errMsg) {
																											return CommonAPI
																													.showMessage(responseData.errMsg);
																										}
																									}
																								});
																					}

																				}

																			},
																			{
																				key : i18n.def['close'],
																				value : function() {

																					// 關閉的時候將相關人寫到畫面上
																					// guarantor

																					var corpId = $(
																							"#gridviewCorporate")
																							.jqGrid(
																									'getDataIDs');
																					var natId = $(
																							"#gridviewNatural")
																							.jqGrid(
																									'getDataIDs');
																					var data = "";
																					var sign;
																					var guaIndex = 0;
																					var needIndex = false;
																					if (natId.length
																							+ corpId.length > 1) {
																						needIndex = true;
																					}

																					for ( var i = 0; i < natId.length; i++) {
																						(data.length > 0) ? sign = "、"
																								: sign = "";
																						guaIndex = guaIndex + 1;

																						if (needIndex == true) {
																							data = data
																									+ sign
																									+ guaIndex
																									+ "."
																									+ $(
																											"#gridviewNatural")
																											.getRowData(
																													natId[i]).rName;// +
																																	// guaPercentStr;
																						} else {
																							data = data
																									+ sign
																									+ $(
																											"#gridviewNatural")
																											.getRowData(
																													natId[i]).rName;// +
																																	// guaPercentStr;
																						}
																					}
																					for ( var i = 0; i < corpId.length; i++) {
																						(data.length > 0) ? sign = "、"
																								: sign = "";
																						guaIndex = guaIndex + 1;

																						if (needIndex == true) {
																							data = data
																									+ sign
																									+ guaIndex
																									+ "."
																									+ $(
																											"#gridviewCorporate")
																											.getRowData(
																													corpId[i]).rName;// +
																																		// guaPercentStr;
																						} else {
																							data = data
																									+ sign
																									+ $(
																											"#gridviewCorporate")
																											.getRowData(
																													corpId[i]).rName;// +
																																		// guaPercentStr;
																						}
																					}

																					// J-109-0067_05097_B1001
																					// Web
																					// e-Loan
																					// 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
																					// if
																					// (!data
																					// ||
																					// $.trim(data)
																					// ==
																					// "")
																					// {
																					// data
																					// =
																					// i18n.lms1601m01['nohave']
																					// }

																					$
																							.ajax({
																								handler : "amlrelateformhandler",
																								action : "saveL120s01pBeneficiaryStr",
																								data : {
																									mainId : responseJSON.mainId,
																									custId : $L162M01AForm
																											.find(
																													"#rId")
																											.val(),
																									dupNo : $L162M01AForm
																											.find(
																													"#rDupNo")
																											.val(),
																									beneficiary : data,
																									rType : rType,
																									callFrom : "2" // 動審表
																								},
																								success : function(
																										obj) {
																									// J-109-0067_05097_B1001
																									// Web
																									// e-Loan
																									// 授信對於無需辨識實質受益人之法人主體，簽報書及額度明細表需出現無實質受益人
																									// $("#"+fieldName).val(data);
																									$(
																											"#"
																													+ fieldName)
																											.val(
																													obj.beneficiary);
																									$.thickbox
																											.close();
																								}
																							});
																				}
																			} ]);
														}

													}

													$("#toglePersonBox")
															.thickbox(
																	{ // 使用選取的內容進行彈窗
																		title : i18n.lms1601m01["other.login"]
																				+ i18n.lms1601m01["L120S01p.beneficiary"],// 'other.login=登錄
																															// ',
																															// L120S01p.conPersonNew
																		width : 700,
																		height : 410,
																		readOnly : false,
																		modal : true,
																		buttons : btnAction
																	});

												}
											}).trigger("reloadGrid");
						}
					}).trigger("reloadGrid");
}

function changeSyndicationLoan(obj) {

	var objValue1 = $('input[name=unitCase]:checked').val();
	var objValue2 = $('input[name=unitMega]:checked').val();

	if (objValue1 == "Y") { // || objValue2 == "Y"
		if (objValue1 == "Y") {
			$('#SyndicationLoan1').show();
			$('#unitCase2Div').show();
			$('#SyndicationLoanAddDate').show();
			$('#SyndicationLoanAddDate1').show();
		} else {
			$('#SyndicationLoan1').hide().find(":radio").removeAttr("checked");// 移除隱藏選項的radio
																				// checked
			$('#unitCase2Div').hide().find(":radio").removeAttr("checked");// 移除隱藏選項的radio
																			// checked
		}
		$('#SyndicationLoan2').show();
		$('#SyndicationLoanAddDate').show();
		$('#SyndicationLoanAddDate1').show();
	} else {
		if (objValue1 == "N") { // && objValue2 == "N"
			// $('#SyndicationLoan1').hide();
			// $('#SyndicationLoan2').hide();
			$('#SyndicationLoan1').hide().find(":radio").removeAttr("checked");// 移除隱藏選項的radio
																				// checked
			$('#SyndicationLoan2').hide().find(":radio").removeAttr("checked");// 移除隱藏選項的radio
																				// checked
			$('#SyndicationLoanAddDate').hide().find(":radio").removeAttr(
					"checked");// 移除隱藏選項的radio checked
			$('#SyndicationLoanAddDate1').hide().find(":radio").removeAttr(
					"checked");// 移除隱藏選項的radio checked
		}
	}

	if (objValue1 == "Y" || objValue2 == "Y") {
		$('#lms160s0301Tab02').show();
		$('#lms160s0301B').show();
	} else {
		$('#lms160s0301Tab02').hide();
		$('#lms160s0301B').hide();
	}

	//
	var objValue = $(obj).val();
	var objName = $(obj).attr('name');
	var uCntBranch = $('input[name=uCntBranch]:checked').val();
	var unitCase = $('input[name=unitCase]:checked').val();

	// if (objName == "uCntBranch" || objName == "unitCase") {

	if (uCntBranch == "Y" && unitCase == "Y") {
		$('#SyndicationLoan2_mgr').show();
	} else {
		$('#SyndicationLoan2_mgr').hide();
	}
	// }
}

function changeItem(obj) {
	var objValue = $(obj).val();
	var objName = $(obj).attr('name');
	if (objName == "mCntrt") {
		if (objValue == "Y") {
			$('#coKind_parent').hide().find("[name=sCntrt]:radio").removeAttr(
					"checked");// 移除隱藏選項的radio checked ;
			$('#coKind_son').hide().find(":text").val("");// 清除隱藏選項的內容;
		} else {
			$('#coKind_parent').show();
		}
	}

	if (objName == "sCntrt") {
		if (objValue == "Y") {
			$('#coKind_son').show();
		} else {
			$('#coKind_son').hide().find(":text").val("");// 清除隱藏選項的內容;
		}
	}
	if (objName == "coKind" && objValue == "0") {
		$('#coKindSpan').hide().find(":radio").removeAttr("checked");// 移除隱藏選項的radio
																		// checked
																		// ;;
	} else {
		if (objName == "coKind") {
			$('#coKindSpan').show();
			$('#coKind_1,#coKind_2,#coKind_3,#coKind_4').html(//ex: text=非合作業務
					DOMPurify.sanitize($("[name=coKind]").find(":selected").text()));
		}
	}

}

function applyGist() {

	var $L161M01AForm = $("#L161M01AForm");
	var cntrNo = $L161M01AForm.find("#cntrNo").val();
	var uid = $L161M01AForm.find("#uid").val();
	$.ajax({
		handler : "lms1601m01formhandler",
		data : {
			formAction : "applyGist",
			mainId : responseJSON.mainId,
			cntrNo : cntrNo,
			uid : uid
		},
		success : function(obj) {
			$L161M01AForm.find("#gist").val(obj.gist);
		}
	});
}

function applyToAloan(fId) {
	var $L161M01AForm = $("#L161M01AForm");
	var cntrNo = $L161M01AForm.find("#cntrNo").val();
	var uid = $L161M01AForm.find("#uid").val();
	$.ajax({
		handler : "lms1601m01formhandler",
		data : {
			formAction : "applyToAloan",
			mainId : responseJSON.mainId,
			cntrNo : cntrNo,
			uid : uid,
			fId : fId
		},
		success : function(obj) {
			if (fId == "1") {
				// 注意事項
				if (obj.toAloan != "") {
					$L161M01AForm.find("#toALoan1").val(obj.toAloan);
				} else {
					return CommonAPI.showMessage(i18n.def["noData"]);
				}
			} else if (fId == "2") {
				// 承諾事項
				if (obj.itemDscr != "") {
					$L161M01AForm.find("#toALoan2").val(obj.itemDscr);
				} else {
					return CommonAPI.showMessage(i18n.def["noData"]);
				}
			}
		}
	});
}

// J-106-0029-003 洗錢防制-新增實質受益人
function newToglePersonBT(callValue, setting, data, rType) {// 新增連保人
	// 先將表格初始化
	$("#L120S01PForm").reset();

	// J-106-0029-001 洗錢防制-調整額度明細物上保證人登錄方式
	var thisRType = "";
	if (data && data.rType) {
		thisRType = data.rType;
	} else {
		thisRType = rType;
	}

	var $L162M01AForm = $("#L162M01AForm");

	$
			.ajax({
				handler : "amlrelateformhandler",
				data : {// 把資料轉成json
					formAction : "queryL120s01p",
					oid : (data && data.oid) || "",
					showMsg : true,
					rType : thisRType
				},
				success : function(obj) {
					$('#L120S01PForm').injectData(obj);

					if (!$('#L120S01PForm').find("#rType").val()) {
						// 新增時後端不會傳rType上來，故由前端塞值
						$('#L120S01PForm').find("#rType").val(thisRType);
					}
					$("input[name='toglePersonType']").trigger('change');

					var btnAction = API.createJSON([ {
						key : i18n.def['close'],
						value : function() {
							$.thickbox.close();
						}
					} ]);

					if (userInfo.ssoUnitNo == "900") {
						btnAction = API
								.createJSON([
										{
											key : i18n.def['sure'],
											value : function() {
												var $L120S01PForm = $("#L120S01PForm");
												if (!$L120S01PForm.valid()) {
													return;
												}

												var toglePersonType = $(
														"[name=toglePersonType]:checked")
														.val();

												// 判斷新增的是自然人還是法人，再去分要存到哪個table，並且同時儲存到主檔TABLE裡
												$
														.ajax({
															handler : "amlrelateformhandler",
															action : "saveL120s01p",
															data : {// 把資料轉成json
																type : $(
																		"[name=toglePersonType]:checked")
																		.val(),
																oid : $(
																		"#l120s01pFormOid")
																		.val(),
																mainId : responseJSON.mainId,
																custId : $L162M01AForm
																		.find(
																				"#rId")
																		.val(),
																dupNo : $L162M01AForm
																		.find(
																				"#rDupNo")
																		.val(),
																rType : thisRType,
																L120S01PForm : JSON
																		.stringify($(
																				"#L120S01PForm")
																				.serializeData())
															},
															success : function(
																	responseData) {
																$.thickbox
																		.close();
																$(
																		'#gridviewNatural')
																		.trigger(
																				'reloadGrid');
																$(
																		'#gridviewCorporate')
																		.trigger(
																				'reloadGrid');
															}
														});
											}
										}, {
											key : i18n.def['close'],
											value : function() {
												$.thickbox.close();
											}
										} ]);
					}

					$("#newToglePersonBox").thickbox({ // 使用選取的內容進行彈窗
						title : i18n.lms1601m01["L120S01p.beneficiary"],
						width : 490,
						height : 400,
						modal : true,
						align : "center",
						valign : "bottom",
						readOnly : false,
						i18n : i18n.def,
						buttons : btnAction
					});
				}
			});
}

// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
function applyClearLand() {
	var $L161M01AForm = $("#L161M01AForm");
	var cntrNo = $L161M01AForm.find("#cntrNo").val();
	var uid = $L161M01AForm.find("#uid").val();

	$.ajax({
		handler : "lmscommonformhandler",
		formId : "L161M01AForm",
		action : "applyClearLand",
		data : {
			cntrNo : cntrNo
		},
		success : function(obj) {

			$(
					"input[name='isClearLand'][value='" + obj.isClearLand
							+ "']:radio").attr("checked", "checked"); // 塞值

			initClearLand($L161M01AForm);
			$L161M01AForm.injectData(obj);

			$L161M01AForm.find("input:radio[name='isClearLand']").trigger(
					'change');

		}
	});
}

// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
function applyClearLandRoa() {
	var $L161M01AForm = $("#L161M01AForm");
	var cntrNo = $L161M01AForm.find("#cntrNo").val();
	var uid = $L161M01AForm.find("#uid").val();

	$.ajax({
		handler : "lmscommonformhandler",
		formId : "L161M01AForm",
		action : "applyClearLandRoa",
		data : {
			cntrNo : cntrNo,
			mainMainId : responseJSON.mainId,
			subMainId : uid,
			custId : $L161M01AForm.find("#custId").val(),
			dupNo : $L161M01AForm.find("#dupNo").val(),
			custName : $L161M01AForm.find("#custName").val(),
			callFrom : "L161S01A"
		},
		success : function(obj) {

			var memoObj = {};
			memoObj["custRoa"] = obj.custRoa;
			memoObj["relRoa"] = obj.relRoa;
			memoObj["roaBgnDate"] = obj.roaBgnDate;
			memoObj["roaEndDate"] = obj.roaEndDate;

			$L161M01AForm.injectData(memoObj);
			checkIsLegal();
		}
	});
}

// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
function applyIsLegal() {

	checkIsLegal();
}

// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
function checkIsLegal() {

	var isClearLand = $("input[name='isClearLand']:radio:checked").val();
	var isChgStDate = $("input[name='isChgStDate']:radio:checked").val();
	var isChgRate = $("input[name='isChgRate']:radio:checked").val();

	if (isClearLand == "Y") {
		if (isChgStDate == "Y" || isChgRate == "Y") {
			// 屬於要控管，且有變更預計動工日/調整利率，要檢核是否符合本行規定
		} else {
			return;
		}
	} else {
		return;
	}

	var data = [];
	$("[name=adoptFg]:checked").each(function(v, k) {
		data.push($(k).val());
	});

	$.ajax({
		handler : "lmscommonformhandler",
		action : "checkIsLegal",
		async : false,
		data : {
			isClearLand : isClearLand,
			isChgStDate : isChgStDate,
			isChgRate : isChgRate,
			ctlType : $("#ctlType").val(),
			fstDate : $("#fstDate").val(),
			lstDate : $("#lstDate").val(),
			cstDate : $("#cstDate").val(),
			adoptFg : data.join("|"),
			rateAdd : $("#rateAdd").val(),
			custRoa : $("#custRoa").val(),
			relRoa : $("#relRoa").val()
		},
		success : function(obj) {
			$("[name='isLegal'][value='" + obj.isLegal + "']:radio").attr(
					"checked", "checked");
		}
	});
}

function initClearLand($formObject) {

	// J-108-0083_05097_B1001 國內企金新增撥貸逾一年以上未動工興建之空地貸款控管機制
	$formObject.find("#showClearLand").find("input:radio:checked").attr(
			"checked", false);
	$formObject.find("#showClearLand").find("input:checkbox").removeAttr(
			"checked");
	$formObject.find("#showClearLand").find("input:text").val('');
	$formObject.find("#showClearLand").find(".field").val('');
	$formObject.find("#showClearLand").find("select").val('');

	$formObject.find("#showClearLand").find("input:radio").trigger("change");
	$formObject.find("#showClearLand").find("input:checkbox").trigger("change");
}

// J-110-0007_05097_B1001 Web e-Loan企金授信額度明細表與動審表增加保證人信用品質順序設定
// 信用品質順序設定
function setGuarantorSeq(cellvalue, options, rowData) {

	var $gridviewPeople = $("#gridviewPeople");
	var select = $gridviewPeople.jqGrid('getGridParam', 'selarrrow');
	if (select == "") {
		// grid.selrow=請先選擇一筆資料。
		return CommonAPI.showMessage(i18n.def["grid.selrow"]);
	}

	var data = [];
	var count = 0;
	var cntrNo = "";
	var l162s01aOid = "";
	for ( var i in select) {
		count = count + 1;
		data.push($gridviewPeople.getRowData(select[i]).oid);
		cntrNo = $gridviewPeople.getRowData(select[i]).cntrNo;
		l162s01aOid = $gridviewPeople.getRowData(select[i]).oid;
	}
	if (count > 1) {
		// grid.selrow=請先選擇一筆資料。
		return CommonAPI.showMessage(i18n.def["grid.selrow"]);
	}

	$("#gridViewGuarantorSeq").jqGrid("setGridParam", {
		postData : {
			formAction : "queryPeopleForSetPriority",
			oids : data
		},
		search : true
	}).trigger("reloadGrid");

	// 檢查主從債務人都有在0024建檔，否則沒辦法判斷是不是法人
	$
			.ajax({
				handler : "lms1601m01formhandler",
				action : "chkAllGuarantorHas0024",
				data : {
					mainId : responseJSON.mainId,
					cntrNo : cntrNo
				},
				success : function(obj) {

					$("#gridViewGuarantorSeq").trigger("reloadGrid");
					$("#setGuarantorSeqThickBox")
							.thickbox(
									{
										title : i18n.lms1601m01['btn.setGuarantorCreditPriority'], // 信用品質順序設定
										width : 900,
										height : 500,
										modal : true,
										i18n : i18n.lms1601m01,
										buttons : API
												.createJSON([
														{
															key : i18n.lms1601m01['btn.writeGuarantor'],
															value : function() {
																var $gridviewprint = $("#gridViewGuarantorSeq");
																// 寫回主從債務人資料表
																$gridviewprint
																		.jqGrid(
																				'saveRow',
																				lastSel,
																				false,
																				'clientArray');

																var ids = $gridviewprint
																		.jqGrid('getDataIDs');
																// 用來放列印順序跟oid
																var json = {};
																var checkArray = $gridviewprint
																		.getCol("priority");

																// 檢查列印順序值是否重複
																if (checkArrayRepeat(checkArray)) {
																	// L162M01A.message9=列印順序不可重複
																	return CommonAPI
																			.showMessage(i18n.lms1601m01['L162M01A.message9']);
																}

																for ( var id in ids) {

																	var data = $gridviewprint
																			.jqGrid(
																					'getRowData',
																					ids[id]);
																	json[data.oid] = data.priority;

																}
																FormAction.open = true;
																$
																		.ajax({
																			handler : "lms1601m01formhandler",
																			action : "savePriority",
																			data : {
																				mainId : $(
																						"#mainId")
																						.val(),
																				cntrNo : cntrNo,
																				l162s01aOid : l162s01aOid,
																				data : JSON
																						.stringify(json)
																			},
																			success : function(
																					obj) {
																				FormAction.open = false;
																				$.thickbox
																						.close();
																				$(
																						"#gridviewPeople")
																						.trigger(
																								'reloadGrid');
																			}
																		});
															}
														},
														{
															key : i18n.lms1601m01['btn.applyGuarantorCreditOrder'], // 取得保證人信評順序
															value : function() {
																var $gridviewprint = $("#gridViewGuarantorSeq");
																// 取得保證人信評順序
																$gridviewprint
																		.jqGrid(
																				'saveRow',
																				lastSel,
																				false,
																				'clientArray');

																var ids = $gridviewprint
																		.jqGrid('getDataIDs');
																// 用來放列印順序跟oid
																var json = {};

																for ( var id in ids) {
																	var data = $gridviewprint
																			.jqGrid(
																					'getRowData',
																					ids[id]);
																	json[data.oid] = id;
																}
																FormAction.open = true;
																$
																		.ajax({
																			handler : "lms1601m01formhandler",
																			action : "getGuarantorCreditPriority",
																			data : {
																				mainId : $(
																						"#mainId")
																						.val(),
																				cntrNo : cntrNo,
																				l162s01aOid : l162s01aOid,
																				data : JSON
																						.stringify(json)
																			},
																			success : function(
																					obj) {
																				for ( var id in ids) {
																					var x = parseInt(id) + 1;
																					$(
																							"#gridViewGuarantorSeq")
																							.jqGrid(
																									'setRowData',
																									x,
																									{
																										priority : obj[id]
																									});
																					// $("#gridViewGuarantorSeq").jqGrid('setRowData',
																					// 1,
																					// {priority:10});
																				}
																				// $gridviewprint.trigger('reloadGrid');
																			}
																		});
															}
														},
														{
															key : i18n.lms1601m01['btn.applyEllngteePriority'], // 引進主從債務人建檔資料
															value : function() {
																var $gridviewprint = $("#gridViewGuarantorSeq");
																// 取得保證人信評順序
																$gridviewprint
																		.jqGrid(
																				'saveRow',
																				lastSel,
																				false,
																				'clientArray');

																var ids = $gridviewprint
																		.jqGrid('getDataIDs');
																// 用來放列印順序跟oid
																var json = {};

																for ( var id in ids) {
																	var data = $gridviewprint
																			.jqGrid(
																					'getRowData',
																					ids[id]);
																	json[data.oid] = id;
																}
																FormAction.open = true;
																$
																		.ajax({
																			handler : "lms1601m01formhandler",
																			action : "applyEllngteePriority",
																			data : {
																				mainId : $(
																						"#mainId")
																						.val(),
																				cntrNo : cntrNo,
																				l162s01aOid : l162s01aOid,
																				data : JSON
																						.stringify(json)
																			},
																			success : function(
																					obj) {

																				for ( var id in ids) {
																					// alert("id="+id+"，obj[id]="+obj[id]);
																					var x = parseInt(id) + 1;
																					// alert("x="+x+"，"+"id="+id+"，obj[id]="+obj[id]);

																					$(
																							"#gridViewGuarantorSeq")
																							.jqGrid(
																									'setRowData',
																									x,
																									{
																										priority : obj[id]
																									});

																					// $("#gridViewGuarantorSeq").jqGrid('setRowData',
																					// 1,
																					// {priority:10});
																				}

																				// $gridviewprint.trigger('reloadGrid');
																			}
																		});
															}
														},
														{
															key : i18n.def['close'],
															value : function() {
																$.thickbox
																		.close();
															}
														} ])
									});

				}
			});

}// close 開啟主從債務人資料表畫面

/** 檢查陣列內容是否重複 */
function checkArrayRepeat(arrVal) {
	var newArray = [];
	for ( var i = arrVal.length; i--;) {
		var val = arrVal[i];
		if ($.trim(val) != "") {
			if ($.inArray(val, newArray) == -1) {
				newArray.push(val);
			} else {
				return true;
			}
		}

	}
}

// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
function initTtype3($formObject) {

	// J-110-0540_05097_B1001 Web e-Loan企金授信配合調整E-loan系統動用審核表部分內容
	$('#useSelect_s01a,#lnSelect_s01a,#tType_s01a').trigger("change");

}

// J-111-0214_05097_B1001 Web e-Loan國內企金動用審核表新增可適用新利率計算減免息相關功能
function cacuRescueChgRateEffectDate() {
	var $L161M01AForm = $("#L161M01AForm");
	var cntrNo = $L161M01AForm.find("#cntrNo").val();
	var uid = $L161M01AForm.find("#uid").val();

	$.ajax({
		handler : "lms1601m01formhandler",
		action : "cacuRescueChgRateEffectDate",
		data : {
			cntrNo : cntrNo,
			mainMainId : responseJSON.mainId,
			L161M01AForm : JSON.stringify($("#L161M01AForm").serializeData())
		},
		success : function(obj) {
			if (obj.isOk == "Y") {
				return CommonAPI.showMessage("預估客戶利率調升日「"
						+ obj.rescueChgRateEffectDate + "」，帳務利率調升計息日「"
						+ obj.aloanCacuRateDate + "」，以上日期未考量主管實際核准日");
			} else {
				return CommonAPI.showMessage(obj.errorMsg);
			}

		}
	});
}

//掛件文號改成動態顯示名稱
function reloadRescueItemData(rescueItem){
	
	//預設掛件文號
	$("#L161M01AForm").find('#rescueNo_Name').val(i18n.lms1601m01["L160M01A.rescueNo"]);
	
	$.ajax({
		handler : "lms1601m01formhandler",
		async:  false,
		data : {
			formAction : "reloadRescueItemData",
			rescueItem : rescueItem
		},
		success : function(obj) {
			if (!$.isEmptyObject(obj.rescueNo_Name)) {
				$("#L161M01AForm").find('#rescueNo_Name').val(obj.rescueNo_Name);
			}
		}
	});
}

//參考額度明細表內程式，舊案額度序號檢核
var CntrNoAPI = {
	/**
	 * 查詢原案額度序號
	 * 
	 * @param {String }
	 *            originalText 原案額度序號 return {Object}cntrNo 額度序號",ownBrName
	 *            額度序號前三碼分行名稱
	 */
	 queryOriginalCntrNo: function(originalText){
	     // 驗證舊有額度序號規則
	     if (!originalText.match(/\w{12}/)) {
	         // L140M01a.message68=額度序號長度應為12碼，編碼原則:XXX(分行代號)+X(1:DBU,4:OBU,5:海外)+YYY(年度)+99999(流水號)
	          return CommonAPI.showMessage(i18n.lms1601m01["L140M01a.message68"]);
	     }
	     var queryObj = {};
	     $.ajax({
	         handler: inits.fhandle,
	         async: false,
	         action: "checkCntrno",
	         data: {
	             cntrNo: originalText,
	             snoKind: $("#L161M01AForm").find("#snoKind").val(),
	             cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
	         },
	         success: function(obj){
	         	queryObj.cntrNo = originalText.toUpperCase();
	            queryObj.ownBrName = obj.ownBrName;
	            // 當錯誤碼有值則不帶額度序號資料
	            if (obj.error) {
	                queryObj.cntrNo = "";
	                queryObj.ownBrName = "";
	                queryObj.error = obj.error;
	                // 原始額度控管種類
	                queryObj.snoKindOld = obj.snoKindOld;
	                queryObj.snoKindOldShow = obj.snoKindOldShow;
	                // 原始額度序號
	                queryObj.snoOld = obj.snoOld;     
	             }
	         }
	     });
		 return queryObj;
	}
}
//登錄聯行攤貸比例
var BranchAcitonAF = {
    $form: $('#L140M01E_AFForm'),
    gridId: "#gridviewL140M01E_AF",
    amtgrId: "#gridviewL140m01eAmt",
    /**
     * 查詢
     * @param {Object} cellvalue 欄位顯示值
     * @param {Object} type  欄位選項
     * @param {Object} data  欄位資料
     */
    query: function(cellvalue, type, data){
        if (!data) {
            data = {
                oid: ""
            };
        }
        util.init(BranchAcitonAF.$form);
        BranchAcitonAF.$form.reset();
        
        $.ajax({
            handler: inits.fhandle,
            data: {//把資料轉成json
                formAction: "queryL140m01e_af",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                oid: data.oid,
				noOpenDoc: true
            },
            success: function(obj){
                $("#shareBrId").setItems({
                    item: obj.item,
                    format: "{value} {key}",
                    space: false
                });
                BranchAcitonAF.$form.injectData(obj.formData);
                $("#totalAmt").val(DOMPurify.sanitize(util.addComma(obj.totalAmt)));
                if (data.oid != "") {
                    $("#shareBrId").attr("disabled", true);
                } else {
                    $("#shareBrId").attr("disabled", false);
                }
                $("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
                $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
                if (obj.role != "0") {
                    $("#shareRate2").val(obj.role);
                    $("#shareRate2").attr("readonly", true);
                    $("[name=shareFlag]").attr("disabled", true);
                    $("[name=shareFlag][value=\"" + obj.shareFlag + "\"]").prop("checked", true);
					
                } else {
                    $("[name=shareFlag]").removeAttr("disabled");
                }//close if
                BranchAcitonAF.controlShow();
                BranchAcitonAF.openBox();
            }//close success
        }); //close ajax  
    },
    openBox: function(){
        $("#newItemChildrenBox3").thickbox({
            //title.15=登錄聯行攤貸比例
            title: i18n.lms1601m01["L140M01e.title01"],
            width: 600,
            height: 300,
            modal: true,
            //readOnly: _openerLockDoc == "1",
            i18n: i18n.def,
            open: function(){
                if (_openerLockDoc == "1") {
                    //鎖定box
                    BranchAcitonAF.$form.readOnlyChilds(_openerLockDoc == "1", "#totalAmt,#shareBrId");
                }
            },
            buttons: {
                "saveData": function(){
                    if ($("[name=shareFlag]:checked").val() == "1") {
                        BranchAcitonAF.$form.find("#shareRate1,#shareRate2").removeClass("required");
                    } else {
                        BranchAcitonAF.countByAMT();
                        BranchAcitonAF.$form.find("#shareRate1,#shareRate2").addClass("required");
                        if ($("#shareRate2").val() == 0) {
                            //L140M01a.message81=分母不可為0
                            return CommonAPI.showMessage(i18n.lms1601m01["L140M01a.message81"]);
                        }
                    }
                    
                    if (!BranchAcitonAF.$form.valid()) {
                        return false;
                    }
                    var shareRate1 = parseInt($("#shareRate1").val(), 10), shareRate2 = parseInt($("#shareRate2").val(), 10);
                    if (shareRate1 > shareRate2) {
                        //L140M01e.lmterror=分子總和大於分母無法儲存
                        return CommonAPI.showMessage(i18n.lms1601m01["L140M01e.lmterror"]);
                    }
                    var shareBrId = $("#shareBrId").val();
                    $.ajax({
                        handler: inits.fhandle,
                        data: {
                            formAction: "queryShareBrIdType",//查詢目前分行為海外還是國內，若為海外第四碼為5，若為國內判斷
                            shareBrId: shareBrId,
                            cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
                        },
                        success: function(obj){
                            //先檢查該間分行是否存在，若存在只做save的動作
                            if (obj.have) {
                                //若該分行已存在只執行儲存
                                BranchAcitonAF.save("");
                            } else {
                                //新增案件開起給號視窗
                                if (obj.type == "5") {
                                    BranchAcitonAF.getCntrNo("5", shareBrId);
                                } else {
                                    BranchAcitonAF.save();
                                }
                                
                            }
                        }//close success
                    });
                },
                "close": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**儲存
     *
     * @param {Object} type 1.DBU,4.OBU,5.海外
     */
    save: function(type){
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "saveL140m01e_af",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                L140M01E_AFForm: JSON.stringify(BranchAcitonAF.$form.serializeData()),
                type: type
            },
            success: function(obj){
                if (obj && obj.drc) {
                    $("#itemDscr1").val(obj.drc);
                }
                $.thickbox.close();
                $(BranchAcitonAF.gridId).trigger("reloadGrid");
                
            }
        });
    },
    
    /**
     * 給號畫面
     * @param {String} type 5.海外
     * @param {String} brId 分行代號
     */
    getCntrNo: function(type, brId){
        var $cntrNoForm = $("#cntrNoBoxforItem3Form");
        $("#cntrNoBoxforItem3").thickbox({
            //btn.number=給號
            title: i18n.lms1601m01["btn.number"],
            width: 640,
            height: 320,
            modal: true,
            align: "center",
            //readOnly: _openerLockDoc == "1",
            valign: "bottom",
            i18n: i18n.def,
            open: function(){
                //初始化
                $cntrNoForm.reset();
                //帶入分行預設值
                $cntrNoForm.find("#branchNoItem3").val(brId);
                $cntrNoForm.find("#cntrNoType").val(type);
                $cntrNoForm.find(".ForOriginal,.ForInSide").hide();
            },
            buttons: {
                "sure": function(){
                    if (!$cntrNoForm.valid()) {
                        return false;
                    }
                    var numberType = $cntrNoForm.find("input[name=numberType]:checked").val();
                    var originalCntrNo = $cntrNoForm.find("#originalCntrNo").val();
                    brId = $cntrNoForm.find("#branchNoItem3").val();
                    //給新號
                    if (numberType == "1") {
                        if (type != "5") {
                            //國內的可以自己選分行和DBU or OBU
                            type = $cntrNoForm.find("input[name=typeCd]:checked").val();
                        }
                    } else {
                        //舊號
                        var cntrno = CntrNoAPI.queryOriginalCntrNo(originalCntrNo, "3");
                        if ($.isEmptyObject(cntrno) || !cntrno.cntrNo) {
                            return false;
                        }
                    }
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01e_af",
                        data: {
                        	L140M01E_AFForm: JSON.stringify(BranchAcitonAF.$form.serializeData()),
                            cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                            cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                            numberType: numberType,
                            originalCntrNo: originalCntrNo,
                            type: type,
                            classCD: "0",
                            selectBrNo: brId
                        },
                        success: function(obj){
                            if (obj && obj.drc) {
                                $("#itemDscr1").val(obj.drc);
                            }
                            $.thickbox.close();
                            $.thickbox.close();
                            $(BranchAcitonAF.gridId).trigger("reloadGrid");
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    },
    /**
     * 刪除
     */
    remove: function(){
        var gridData = $(BranchAcitonAF.gridId);
        var gridID = gridData.getGridParam('selarrrow');
        if (gridID == "") {
            //TMMDeleteError=請先選擇需修改(刪除)之資料列
            return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        }
        //confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                var gridIDList = [];
                for (var i = 0; i < gridID.length; i++) {
                    gridIDList[i] = gridData.getRowData(gridID[i]).oid;
                }
                $.ajax({
                    handler: inits.fhandle,
                    data: {
                        formAction: "deleteL140m01e_af",
                        cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                        cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                        Idlist: gridIDList
                    },
                    success: function(obj){
                        if (obj && obj.drc) {
                            $("#itemDscr1").val(obj.drc);
                        }
                        gridData.trigger("reloadGrid");
                    }
                });
            }
        });
    },
    /**
     * 控制計算方式顯示
     */
    controlShow: function(){
        var value = $("[name=shareFlag]:checked").val();
        $("#shareAmt,#shareRate1,#shareRate2").attr("readonly", true);
        $("#shareMoneyCount1,#shareMoneyCount2").hide().parents(".fg-buttonset").hide();
        //顯示隱藏
        switch (value) {
            case "1":
                // 1.依攤貸金額
                $("#shareMoneyCount2").show().parents(".fg-buttonset").show();
                $("#shareAmt").removeAttr("readonly");
                $("#shareRate1,#shareRate2").val("");
                break;
            case "2":
                //2.依攤貸比例          
                $("#shareMoneyCount1").show().parents(".fg-buttonset").show();
                if (!$("[name=shareFlag]:checked").attr("disabled")) {
                    $("#shareAmt").val("");
                }
                $("#shareRate1").removeAttr("readonly");
                var countGrid = $(BranchAcitonAF.gridId).jqGrid('getGridParam', 'records');
                if (countGrid == 0) {
                    $("#shareRate2").removeAttr("readonly");
                }
                break;
            default:
                break;
        }
    },
    /**
     *以比例計算金額
     */
    countByAMT: function(){
        var totel = $("#totalAmt").val().replace(/,/g, ""), value1 = $("#shareRate1").val(), value2 = $("#shareRate2").val(), shareRate1 = parseInt(value1, 10), shareRate2 = parseInt(value2, 10), end = (totel * shareRate1) / shareRate2;
        if (!BranchAcitonAF.isNumber(end)) {
            end = "";
        } else {
            end = util.addComma(parseInt(end, 10));
        }
        $("#shareAmt").val(end);
    },
    /**
     *變更分母
     */
    changesShareRate: function(){
        $("#newSharteNew").val("");
        $.ajax({
            handler: inits.fhandle,
            data: {
                formAction: "queryChangesShareRate",
                cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
            },
            success: function(obj){
                $("#newSharteNewBox").thickbox({
                    title: i18n.lms1601m01["btn.changesShareRate2"],//變更分母
                    width: 200,
                    height: 100,
                    //readOnly: _openerLockDoc == "1",
                    i18n: i18n.def,
                    modal: true,
                    align: "center",
                    valign: "bottom",
                    open: function(){
                    },
                    buttons: {
                        "sure": function(){
                            if ($("#newSharteNew").val() == 0) {
                                //L140M01a.message81=分母不可為0
                                return CommonAPI.showMessage(i18n.lms1601m01["L140M01e.message03"]);
                            }
                            if (!$("#newSharteNewForm").valid()) {
                                return false;
                            }
                            $.ajax({
                                handler: inits.fhandle,
                                data: {
                                    formAction: "saveChangesShareRate",
                                    cntrMainId: $("#L161M01AForm").find("#cntrMainId").val(),
                                    cntrNo : $("#L161M01AForm").find("#cntrNo").val(),
                                    shareRate: $("#newSharteNew").val()
                                },
                                success: function(obj){
                                    if (obj && obj.drc) {
                                        $("#itemDscr1").val(obj.drc);
                                    }
                                    $(BranchAcitonAF.gridId).trigger("reloadGrid");
                                    $.thickbox.close();
                                }
                            });
                        },
                        "cancel": function(){
                            $.thickbox.close();
                        }
                    }
                });
            }
        });
    },
    //驗證是不是數字   
    isNumber: function(val){
        return /\d/.test(val);
    },
    //當現請額度大於攤貸總金額時 會出現 grid選擇  將餘額加至哪筆 
    l140m01eAmtBox: function(amt){
        //L140M01a.message70=聯行攤貸金額尚餘 {0}元，請選擇要將餘額加進何筆資料!
        $("#l140m01eAmtMsg").html(i18n.lms1601m01['L140M01e.message06'].replace("{0}", amt))
        $(BranchAcitonAF.amtgrId).setGridParam({//重新設定grid需要查到的資料
			postData: {
				cntrMainId: $("#L161M01AForm").find("#cntrMainId").val()
            },
            search: true
        }).trigger("reloadGrid");
        
        $("#l140m01eAmtBox").thickbox({
            title: "",
            width: 600,
            height: 350,
            align: "center",
            valign: "bottom",
            modal: true,
            i18n: i18n.def,
            buttons: {
                "sure": function(){
                    var id = $(BranchAcitonAF.amtgrId).getGridParam('selrow');
                    if (id == null || !id) {
                        //action_005=請先選取一筆以上之資料列
                        return CommonAPI.showErrorMessage(i18n.def['action_005']);
                    }
                    var oid = $(BranchAcitonAF.amtgrId).getRowData(id).oid;
                    $.thickbox.close();
                    $.ajax({
                        handler: inits.fhandle,
                        action: "saveL140m01e_afAmt",
                        data: {
                            oid: oid,
                            amt: util.delComma(amt)
                        },
                        success: function(responseData){
                        	$(BranchAcitonAF.gridId).trigger("reloadGrid");
                        }
                    });
                },
                "cancel": function(){
                    $.thickbox.close();
                }
            }
        });
    }
    
};
