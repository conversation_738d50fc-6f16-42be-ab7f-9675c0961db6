package com.mega.eloan.lms.fms.service;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONObject;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.service.AbstractService;
import com.mega.eloan.lms.model.L260M01A;
import com.mega.eloan.lms.model.L260M01B;
import com.mega.eloan.lms.model.L260M01C;
import com.mega.eloan.lms.model.L260M01D;
import com.mega.eloan.lms.model.L260S01A;
import com.mega.eloan.lms.model.L260S01B;
import com.mega.eloan.lms.model.L260S01C;
import com.mega.eloan.lms.model.L260S01D;
import com.mega.eloan.lms.model.L260S01E;
import com.mega.eloan.lms.model.L260S01F;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;

/**
 * <pre>
 * 貸後管理作業
 * 
 * </pre>
 * 
 * @since 2020
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul> 
 */
public interface LMS8000Service extends AbstractService {

	@SuppressWarnings("rawtypes")
	public Page<? extends GenericBean> findPage(Class clazz, ISearch search);

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByOid(Class clazz, String oid);

	@SuppressWarnings("rawtypes")
	public <T extends GenericBean> T findModelByMainId(Class clazz,
			String mainId);

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findListByMainId(Class clazz,
			String mainId);

	public void saveL260m01cList(List<L260M01C> list);

	public void saveL260m01dList(List<L260M01D> list);

	// J-112-0307
	// 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	public void saveL260s01dList(List<L260S01D> list);

	public void saveL260s01eList(List<L260S01E> list);
	
	/**
	 * J-113-0035 為利ESG案件之貸後管控,
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制
	 * 
	 * @param list
	 */
	public void saveL260s01fList(List<L260S01F> list);

	boolean deleteL260m01as(String[] oids) throws CapMessageException;
	
	/** J-112-0569 貸後管理已核准案件新增開放退回修改功能
	 * 編制完成 ，退回修改
	 * @param oids
	 * @param rtnModifyReason
	 * @return
	 * @throws CapException
	 */
	public String rtnModifyL260m01as(String[] oids,String rtnModifyReason) throws CapException;
	
	/**
	 * J-112-0569 貸後管理已核准案件新增開放退回修改功能
	 * 複製退回修改的資料至歷史區
	 * @param mainId
	 * @param rtnModifyReason
	 * @param isOverSea
	 */
	public void copyL260m01ATOHistory(String mainId, String rtnModifyReason, Boolean isOverSea) throws CapException;
	
	public void deleteL260m01bs(List<L260M01B> l260m01bs, boolean isAll);

	public void saveL260m01bList(List<L260M01B> list);

	public L260M01B findL260m01b(String mainId, String branchType,
			String branchId, String staffNo, String staffJob);

	public void flowAction(String mainOid, L260M01A model, boolean setResult,
			boolean resultType, boolean upMis) throws Throwable;

	public Map<String, Object> findLastPrint_L140M01A(String cntrNo);

	/**
	 * J-110-0005 其他敘做條件to貸後管理
	 */
	public List<Map<String, Object>> findL140S09BtoPostLoanByL140M01A(String oid);

	public L260M01A findL260m01a(String custId, String dupNo, String cntrNo,
			String loanNo);

	public L260M01A findL260m01a_notEqualsDocstatus(String custId,
			String dupNo, String cntrNo, String loanNo, String[] docStatusAry,
			String branchId);

	public void saveL260m01aTemp(L260M01A l260m01a);

	public Page<Map<String, Object>> getPrintList(String oid, ISearch search)
			throws CapException;

	public String getLoanKind(String cntrNo);

	public String getStatusForShow(String status, Date nextFollowDate);

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findListByMainIdNotDel(Class clazz,
			String mainId, boolean notIncDel);

	@SuppressWarnings("rawtypes")
	public List<? extends GenericBean> findByMainIdAndNos(Class clazz,
			String mainId, String cntrNo, String loanNo, boolean notIncDel,
			boolean incEmptyLoanNo);

	public boolean isFutureDate(Date d);

	public L260M01D findL260m01dByMainIdAndUnid(String mainId, String unid);

	public void deleteL260m01ds(List<L260M01D> l260m01ds);

	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ById(String custId,
			String dupNo, String bgnDate);

	public List<Map<String, Object>> findOTS_DW_LNWM_MNT_ByKey(String custId,
			String dupNo, String proType, String bankProCode, String accNo);

	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByUnid(String unid);

	public List<Map<String, Object>> findOTS_DW_LNWM_CFM_ByKey(String unid,
			String custId, String dupNo, String proType, String bankProCode,
			String accNo);

	public List<Map<String, Object>> findODS_0320_ById(String custId,
			String dupNo);

	public List<Map<String, Object>> findODS_0060_TXN(String realActNo);

	/**
	 * J-113-0159 已審核未動用額度可於貸後新增 <br/>
	 * <br/>
	 * 因貸後自行新增時，增加引入已審核但未動用的額度資料，<br/>
	 * 故呈主管審核時，判斷該額度的動審表是否已審核完成 <br/>
	 * 已審核完成，回傳FALSE <br/>
	 * 未審核完成，回傳TRUE <br/>
	 * <br/>
	 * 以下情形不檢核：<br/>
	 * (1) ID階層的貸後 <br/>
	 * (2) 額度序號已存在於帳務 <br/>
	 * 
	 * @param l260m01a
	 * @return
	 */
	public boolean cntrNoNotOK(L260M01A l260m01a);
	
	public List<Map<String, Object>> findODS_0060_HIST(String realActNo,
			String currCode, String bgnDate, String endDate);

	public List<Map<String, Object>> findODS_8250(String brNo, String ioFlag,
			String func, String remitType, String bgnDate, String endDate,
			String custId, String dupNo, String begAmt, String endAmt,
			String bankId, String ractNo);

	public List<Map<String, Object>> findODS_8410_ByAccNo(String loanNo,
			String brNo);

	public List<Map<String, Object>> findODS_CMSTKTBL();

	public Map<String, String> findODS_CMSTKTBL_SINGLE(String CMSTK_CODE);

	public List<Map<String, Object>> findODS_CMMEMTBN();

	public String[] getODS_Status();

	public void saveL260s01aList(List<L260S01A> list);

	public void deleteL260s01aList(List<L260S01A> list);

	boolean deleteL260s01as(String[] oids);

	public L260S01A findL260s01aByUniqueKey(String mainId, String custId,
			String dupNo, String proType, String bankProCode, String accNo);

	public void saveL260s01bList(List<L260S01B> list);

	public void saveL260s01fList(L260M01D l260m01d,JSONObject jsonData);
	
	/**
	 * J-110-0363 找所有ID層資料
	 */
	public List<Map<String, Object>> findDataById(String clazz, String ownBrId,
			String custId, String dupNo, String[] docStatusArray);

	/**
	 * J-110-0363 找該筆ID層資料是否有其他維護案修改中
	 */
	public String findDataByIdAndM01A(String clazz, L260M01A l260m01a,
			String unid);

	public String cutStrWithUTF8(Object value, int byteCnt)
			throws UnsupportedEncodingException;

	public String[] checkDatePeriod(String dateBgn, String dateEnd,
			boolean sameDay, String txnCode);

	boolean chkFilterDataDW(String BRNID, String custId, String dupNo,
			String cntrNo, String loanNo);

	/**
	 * J-110-0548 擔保品謄本 追蹤日(追蹤事項通知日期)前最新一筆擔保品
	 */
	public Map<String, Object> findLastC101m01(String brNo, String cntrNo,
			String elf601_unid, String followDate);

	/**
	 * J-110-0548 擔保品謄本 依擔保品申請編號取得謄本資料
	 */
	public Map<String, Object> findC101m01ByApplyNo(String applyNo);

	public boolean isCaseMark(L260M01D l260m01d, String chkCaseMark);

	public List<Map<String, Object>> findC101m01List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate);

	public List<Map<String, Object>> findC100s02aList(String c101m01MainId);

	public Map<String, Object> findC100s02aByOid(String c100s02aOid);

	public String findC100s02aUrlByOid(String c100s02aOid);

	public List<Map<String, Object>> findC101m29List(String brNo,
			String cntrNo, String elf601_unid, String bgnDate, String endDate);

	public Map<String, Object> findC101m29ByOid(String c101m29Oid);

	public String findC101m29UrlByOid(String c101m29Oid);

	public List<L260S01C> findL260s01cByRaspFileOid(String raspFileOid);

	public List<L260S01C> findL260s01cByRaspMainId(String mainId,
			String refMainId);

	public void saveL260s01cList(List<L260S01C> list);

	public boolean deleteL260s01cs(List<L260S01C> list);

	public String getRaspStatus(L260M01D l260m01d);

	/**
	 * J-112-0307
	 * 於eloan貸後管理追蹤檢核表維護功能中，新增一鍵生成公司訪問紀錄表及借戶、負責人、所屬企業集團等關係戶於本行各項業務往來彙總表。
	 */
	public CapAjaxFormResult findL260s01d(String l260m01dOid,String custId,String dupNo,boolean modCompVisitVerFlag)
			throws CapException;
	
	/**
	 * 查詢上次的ESG追蹤紀錄，並儲存<br/>
	 * <br/>
	 * J-113-0035 為利ESG案件之貸後管控,<br/>
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制<br/>
	 * 
	 * @param fromL260M01DList
	 * @throws CapException
	 */
	public void findLastESGDataAndSave(List<L260M01D> fromL260M01DList)
			throws CapException;

	/**
	 * 顯示ESG追蹤紀錄畫面<br/>
	 * <br/>
	 * J-113-0035 為利ESG案件之貸後管控,<br/>
	 * ELOAN國內外企金系統額度明細表其他敘做條件增加「應注意/承諾/待追蹤/ESG連結條款」的登錄機制<br/>
	 * 
	 * @param fromL260M01DList
	 * @throws CapException
	 */
	public CapAjaxFormResult showL260s01f(String l260m01fMainId)
			throws CapException;

	/**
	 * 查詢應注意/承諾/追蹤ESG連結條款明細內容
	 * 
	 * @param oid
	 * @param brNo
	 * @return
	 */
	public Map<String, Object> findESGData(String oid, String brNo);

	public Map<String, String> followKindToMap(String followKind);
	
	public void copyDocFile(String l260m01aMainid, boolean isQuery,
			boolean isRs, List<L260M01D> l260m01dList);
	
	public L260S01E findL260s01eByUniqueKey(String mainId, String itemNo);
	
	public CapAjaxFormResult saveL260S01D(JSONObject jsonData, L260M01D l260m01d)
			throws CapException;
	
	public void findVisitCompData(List<L260M01D> fromL260M01DList) throws CapException;

	List<Map<String,Object>> findByFilter(PageParameters params);

	Page<L260M01A> findPageByFilter(PageParameters params);
}
