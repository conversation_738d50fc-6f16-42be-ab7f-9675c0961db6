/* 
 * C900M02KDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C900M02K;

/** 總授信業務授權額度檔 **/
public interface C900M02KDao extends IGenericDao<C900M02K> {

	C900M02K findByOid(String oid);

	/**
	 * 透過docType、brNo、版本撈取一包含有各PD等級的參數
	 * 
	 * @param brNo
	 * @param type
	 * @return
	 */
	List<C900M02K> findByDocTypeAndVersionAndBrNo(String docType,
			String loanKind, String version, String brNo);

	/**
	 * 透過docType、brClass第X組分行、版本撈取一包參數
	 * 
	 * @param brClass
	 * @return
	 */
	C900M02K findByDocTypeAndVersionAndBrClassNoPd(String docType,
			String loanKind, String version, String brClass);

	/**
	 * 透過docType、brClass第X組分行、版本撈取一包含有各PD等級的參數
	 * 
	 * @param brClass
	 * @return
	 */
	List<C900M02K> findByDocTypeAndVersionAndBrClass(String docType,
			String loanKind, String version, String brClass);

	/**
	 * 透過docType、版本撈取一包有"授權層級"的資料
	 * 
	 * @param brClass
	 * @return
	 */
	List<C900M02K> findByDocTypeAndVersionHaveCaseLvl(String docType,
			String loanKind, String version);
}