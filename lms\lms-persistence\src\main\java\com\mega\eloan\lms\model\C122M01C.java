package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 線上貸款改派歷程檔 **/
@Entity
@Table(name = "C122M01C", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "seq" }))
public class C122M01C extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號(依mainId去串出同一  ploanCaseId 下的案件) **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 序號 **/
	@Column(name = "SEQ", columnDefinition = "DECIMAL(5,0)")
	private Integer seq;

	/** 原分行 **/
	@Column(name = "BRNOBEF", length = 3, columnDefinition = "CHAR(3)")
	private String brNoBef;
	
	/** 新分行 **/
	@Column(name = "BRNOAFT", length = 3, columnDefinition = "CHAR(3)")
	private String brNoAft;
	
	/** 備註說明 **/
	@Column(name = "MEMO", length = 1200, columnDefinition = "VARCHAR(1200)")
	private String memo;
	
	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getMainId() {
		return mainId;
	}

	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public String getBrNoBef() {
		return brNoBef;
	}

	public void setBrNoBef(String brNoBef) {
		this.brNoBef = brNoBef;
	}

	public String getBrNoAft() {
		return brNoAft;
	}

	public void setBrNoAft(String brNoAft) {
		this.brNoAft = brNoAft;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	
}
