/* 
 *  CLS1161Flow.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.flow;

import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.model.C124M01A;
import org.springframework.stereotype.Component;
import tw.com.jcs.flow.FlowInstance;

/**
 * <pre>
 * 中小信保整批申請 - 流程
 * </pre>
 *
 * @since 2020/05/16
 * <AUTHOR>
 * @version <ul>
 *          <li>2020/05/16,EL09301,new
 *          </ul>
 */
@Component
public class CLS3501Flow extends AbstractFlowHandler {

	@Transition(node = "待覆核")
	public void test3(FlowInstance instance) {

	}

	@Override
	public Class<? extends Meta> getDomainClass() {
		return C124M01A.class;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}