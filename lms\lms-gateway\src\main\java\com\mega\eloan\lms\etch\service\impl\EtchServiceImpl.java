/* 
 * EtchServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.etch.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import tw.com.jcs.common.Util;

import com.mega.eloan.lms.etch.service.EtchService;

/**
 * <pre>
 * ETCH ServiceImpl
 * </pre>
 * 
 * @since 2012/10/25
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/25,Fantasy,new
 *          </ul>
 */
@Service
public class EtchServiceImpl extends AbstractETchJdbc implements EtchService {

	@Override
	public Map<String, Object> findById(String id) {
		return this.getJdbc().queryForMap("MSG001.findById",
				new String[] { id });
	}

	@Override
	public Map<String, Object> getDate(String qid) {
		Map<String, Object> result = getDate(qid, "4111");
		if (result == null)
			result = getDate(qid, "4114");
		if (result != null) {
			// 查詢日期 to AD
			String QDATE = Util.trim(result.get("QDATE"));
			if (Util.isNotEmpty(QDATE))
				result.put("QDATE2AD", Util.toAD(Util.parseDate(QDATE)));
			// 資料日期 to AD
			String END_DATE = Util.trim(result.get("END_DATE"));
			if (Util.isNotEmpty(END_DATE)) {
				long value = Util.parseLong(END_DATE);
				String d = String.valueOf(value + 19110000);
				result.put("END_DATE2AD", Util.toAD(Util.parseDate(d)));
			}
		}
		return result;
	}

	@Override
	public Map<String, Object> getDate(String qid, String txid) {
		Map<String, Object> result = null;
		List<Map<String, Object>> list = this.getJdbc().queryForList(
				"MIS.MSG_001.getDate", new String[] { qid, txid });
		if (list != null && !list.isEmpty())
			result = list.get(0);
		return result;
	}

	@Override
	public String getMSG001QDate(String qid) {
		String result = getMSG001QDate(qid, "4111");
		if (result == null)
			result = getMSG001QDate(qid, "4114");

		return result;
	}

	@Override
	public String getMSG001QDate(String qid, String txid) {
		String result = null;
		Map<String, Object> map = this.getJdbc().queryForMap(
				"MIS.MSG_001.getQDate", new String[] { qid, txid });
		if (map != null)
			result = Util.trim(map.get("QDATE"));

		return Util.isEmpty(result) ? null : result;
	}

	@Override
	public Map<String, Object> getMSG004BcDate(String qid) {
		return this.getJdbc().queryForMap("MIS.MSG_004.getBcDate",
				new String[] { qid });
	}

	@Override
	public Map<String, Object> getMSG001Data(String qid) {
		return this.getJdbc().queryForMap("MIS.MSG_001.getData",
				new String[] { qid });
	}

	@Override
	public Map<String, Object> getMSG001RejectDate(String qid) {
		return this.getJdbc().queryForMap("MIS.MSG_001.getRejectDate",
				new String[] { qid });
	}

	@Override
	public List<Map<String, Object>> getMSG001Info1(String qid) {
		return this.getJdbc().queryForList("MIS.MSG_001.getETCHInfo1",
				new String[] { qid });
	}

	@Override
	public List<Map<String, Object>> getHRESULT(String qid, String txid,
			String qDate) {
		return this.getJdbc().queryForList("MIS.H_RESULT.getHtml",
				new String[] { qid, txid, qDate });
	}
	
	@Override
	public Map<String, Object> getLatestEtch4111QueryLog(String custId) {
		return this.getJdbc().queryForMap("MIS.SUCC_KEY_MAP.getLatestEtch4111QueryLog", new String[] { custId });
	}
	
	@Override
	public Map<String, Object> getEtchInqueryData(String seqId, String prodId, String queryDate, String queryCustId) {
		return this.getJdbc().queryForMap("MIS.MSG_001.getEtchInqueryDataBySeqId_TxId_Qdate_Qid", new String[] { seqId, prodId, queryDate, queryCustId });
	}
	
	@Override
	public Map<String, Object> getEtchInqueryData(String prodId, String queryDate, String queryCustId) {
		return this.getJdbc().queryForMap("MIS.MSG_001.getEtchInqueryDataByTxId_Qdate_Qid", new String[] { prodId, queryDate, queryCustId });
	}
}
