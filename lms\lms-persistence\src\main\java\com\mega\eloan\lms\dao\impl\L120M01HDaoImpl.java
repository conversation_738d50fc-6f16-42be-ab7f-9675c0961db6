/* 
 * L120M01HDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.dao.L120M01HDao;
import com.mega.eloan.lms.model.L120M01H;

/** 授審會／催收會會議決議檔 **/
@Repository
public class L120M01HDaoImpl extends LMSJpaDao<L120M01H, String>
	implements L120M01HDao {

	@Override
	public L120M01H findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01H> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L120M01H> list = createQuery(L120M01H.class,search).getResultList();
		return list;
	}
	
	@Override
	public L120M01H findByUniqueKey(String mainId, String meetingType){
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "meetingType", meetingType);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L120M01H> findByIndex01(String mainId, String meetingType){
		ISearch search = createSearchTemplete();
		List<L120M01H> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (meetingType != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "meetingType", meetingType);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(L120M01H.class,search).getResultList();
		}
		return list;
	}
}