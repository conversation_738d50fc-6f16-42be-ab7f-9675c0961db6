/* 
 * L140MC1BDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L140MC1BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L140MC1B;

/** 房貸利率成數條件檢核明細紀錄檔 **/
@Repository
public class L140MC1BDaoImpl extends LMSJpaDao<L140MC1B, String>
	implements L140MC1BDao {

	@Override
	public L140MC1B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L140MC1B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<L140MC1B> list = createQuery(search).getResultList();
		return list;
	}
	
	@Override
	public L140MC1B findByUniqueKey(String mainId){
		ISearch search = createSearchTemplete();
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public List<L140MC1B> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L140MC1B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140MC1B> findByIndex02(String mainId, String cntrno){
		ISearch search = createSearchTemplete();
		List<L140MC1B> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		if (cntrno != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrno);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L140MC1B> findByCaseMainid(String caseMainId){
		ISearch search = createSearchTemplete();
		List<L140MC1B> list = null;
		if (caseMainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainId);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L140MC1B> findByCmsOidsAndSrcMainId(String[] cmsoids, String srcMainId){
		ISearch search = createSearchTemplete();
		List<L140MC1B> list = null;
		if(cmsoids.length != 0){
			search.addSearchModeParameters(SearchMode.IN, "cmsoid", cmsoids);
		}
		if(srcMainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "srcMainId", srcMainId);
		}
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L140MC1B> findExcludeMortgageInsuranceAndY1Y2Y3By(String cmsOid, String caseMainId){
		ISearch search = createSearchTemplete();
		List<L140MC1B> list = null;
		if(cmsOid != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "cmsOid", cmsOid);
		}
		if(caseMainId != null){
			search.addSearchModeParameters(SearchMode.EQUALS, "caseMainId", caseMainId);
		}
		
		search.addSearchModeParameters(SearchMode.OR, 
				new SearchModeParameter(SearchMode.NOT_EQUALS, "insFlag", "Y"), 
				new SearchModeParameter(SearchMode.IS_NULL, "insFlag", null));

		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "projClass", "Y1");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "projClass", "Y2");
		search.addSearchModeParameters(SearchMode.NOT_EQUALS, "projClass", "Y3");
		
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

}