package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.L170M01H;

public interface L170M01HDao extends IGenericDao<L170M01H> {
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS(String[] exclude_debIdArr, String mainId, String custId, String dupNo);
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS_debTypeC(String[] exclude_debIdArr, String mainId, String custId, String dupNo);
	public List<L170M01H> findByMainIdCustIdDupNo_fmtSYS_debTypeGN(String[] exclude_debIdArr, String mainId, String custId, String dupNo);
	
	public L170M01H findByMainIdCustIdDupNo_fmtFree_debTypeG(String _debId, String mainId, String custId, String dupNo);
	public L170M01H findByMainIdCustIdDupNo_fmtFree_debTypeC(String _debId, String mainId, String custId, String dupNo);
}
