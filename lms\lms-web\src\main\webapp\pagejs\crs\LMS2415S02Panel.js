$(function(){
	if($("#detailMainId").val() == ''){
		$("#detailMainId").val(responseJSON.mainId);
	}
	if($("#detailCustId").val() == ''){
		$("#detailCustId").val(responseJSON.custId);
	}
	if($("#detailDupNo").val() == ''){
		$("#detailDupNo").val(responseJSON.dupNo);
	}
	if($("#detailOid").val() == ''){
		$("#detailOid").val(responseJSON.oid);
	}
	if($("#detailTxCode").val() == ''){
		$("#detailTxCode").val(responseJSON.txCode);
	}
    $("#C2415M01bGrid").iGrid({
        rownumbers: true,
        handler: 'lms2415gridhandler',
        height: 350, //設定高度
        //sortname : 'oid', //預設排序
        rownumbers: true,
		multiselect: true,
        postData: {
            formAction: "queryC241m01b",
            mainId: $("#detailMainId").val(),
            custId: $("#detailCustId").val(),
            dupNo: $("#detailDupNo").val()
        },
        colModel: [{
            name: 'oid',
            hidden: true //是否隱藏
        }, {
            name: 'mainId', //col.id
            hidden: true
        }, {
        
            colHeader: i18n.lms2415s02["C241M01b.ynReview"],//"是否要覆審", 
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            //formatter: 'click',
            // onclick: openDoc,
            name: 'ynReview' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.quotaType"], //性質
            align: "left",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            //formatter : 'click',
            //onclick : function,
            name: 'quotaType' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.quotaNo"], //額度序號
            align: "left",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            formatter: 'click',
            onclick: openDoc,
            name: 'quotaNo' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.subjectName"], //科目
            align: "left",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'subjectNo' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.startDate"],// 契約起日
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'loanEDate' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.reVolve"],// "循環"
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'reVolve' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.balCurr"],// "幣別"
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'quotaCurr' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.quotaAmt"],// "額度"
            align: "right",
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                decimalPlaces: 2//小數點到第幾位
            },
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'quotaAmt' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.balCurr"],// "幣別"
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'balCurr' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.balAmt"],// "前日節欠餘額"
            align: "right",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            formatter: 'currency',
            formatoptions: {
                thousandsSeparator: ",",
                decimalPlaces: 2//小數點到第幾位
            },
            name: 'balAmt' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.guaranteeKind"],// "擔保品類別"
            align: "center",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'guaranteeKind' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.retrialMemo"],// "覆審註記"
            align: "left",
            width: 50, //設定寬度
            sortable: true, //是否允許排序
            name: 'retrialMemo' //col.id
        }, {
            colHeader: i18n.lms2415s02["C241M01b.lnDataDateResult"], // 手動新增
            align: "center",
            width: 30, // 設定寬度
            sortable: false, // 是否允許排序
            name: 'creator' // col.id
        }]
    });
    
    //===跳出明細=================================================================
    function openDoc(cellvalue, options, rowObject){
    	thickboxShow(rowObject.mainId,rowObject.oid,$("#detailTxCode").val(),rowObject.creator)
    };
    var tempOid = "";
    function thickboxShow(obj_mainId,obj_oid,obj_txCode,lnDataDateResult){
        $("#C2415M01bForm").reset();
    	if(lnDataDateResult == 'Y'){
        	thickboxLock(false);
    	}else{
        	thickboxLock(true);
    	}
    	tempOid = obj_oid;
    	$.ajax({
            handler: "lms2415m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "queryCredit",
                oid: obj_oid,
                showMsg: true,
                txCode: $("#detailTxCode").val()
            },
            //  target: mainOid,
            success: function(responseData){
//            	alert(JSON.stringify(responseData));
                showRadio01Data(responseData);
                var C2415M01bForm = $("#C2415M01bForm");
                C2415M01bForm.setData(responseData.C2415M01bForm);
                var balAmt_show = responseData.balAmt_show;
                var quotaAmt_show = responseData.quotaAmt_show;
//                alert(balAmt_show);
//                alert(quotaAmt_show);
                var balAmt = $("#balAmt");
                balAmt.val(balAmt_show);
                var quotaAmt = $("#quotaAmt");
                quotaAmt.val(quotaAmt_show);
                var $lms2415s02 = $("#lms2415s02Bom");
                $lms2415s02.thickbox({
                    //'一般授信資料',
                    title: i18n.lms2415s02["C241M01b.title3"],
                    width: 1000,
                    height: 450,
                    align: 'left',
                    modal: false,
                    i18n: i18n.def,
                    buttons: {
                        "saveData": function(showMsg){
                        	var checkResult = true;
                        	if($("#loanAmt").val() != ''){
                    			if($("#loanCurr").val() == ''){
                    				CommonAPI.showMessage(i18n.lms2415s02["C241M01b.warmMsg03"]);
                    				checkResult = false;
                    			}
                    		}
                    		if($("#estAmt").val() != ''){
                    			if($("#estCurr").val() == ''){
                    				CommonAPI.showMessage(i18n.lms2415s02["C241M01b.warmMsg02"]);
                    				checkResult = false;
                    			}
                    		}
                        	if(!$("#C2415M01bForm").valid()){
                        		checkResult = false;
                        	}
                        	if(checkResult){
                        		$.ajax({
                                    handler: "lms2415m01formhandler",
                                    type: "POST",
                                    dataType: "json",
                                    data: {
                                        formAction: "saveC241m01b",
                                        mainId: obj_mainId,
                                        oid: obj_oid,
                                        txCode: $("#detailTxCode").val(),
                                        C2415M01bForm: JSON.stringify($("#C2415M01bForm").serializeData())
                                    },
                                    success: function(responseData){
                                    	obj_oid = responseData.show_oid;
                                    	tempOid = obj_oid;
                                    	$("#show_totBal").val(responseData.show_totBal);
                                    	$("#show_totQuota").val(responseData.show_totQuota);
                                        // 重新載入grid內容
                                        $("#C2415M01bGrid").trigger("reloadGrid");
                                    }
                                });//ajax
                        	}
                        },
                        "close": function(){
                            $.thickbox.close();
                        }//關閉
                    }//bottons
                });//thickbox
            }//success
        });//Ajax
    }
    $("#_lms2415s02ADD").click(function(){
        //新增
        thickboxShow($("#detailMainId").val(),'',$("#detailTxCode").val(),"Y");
    });
    $("#_lms2415s02Delete").click(function(){
    	var $gridview = $("#C2415M01bGrid");
        var id = $gridview.getGridParam('selarrrow');
        var content = [];
        for (var i = 0; i < id.length; i++) {
            if (id[i] != "") {
                var datas = $gridview.getRowData(id[i]);
                content.push(datas.oid);
            }
        }
        if (content.length == 0) {
        	return CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
        } else {
        	// confirmDelete=是否確定刪除?
            CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
                if (b) {
                    $.ajax({
                    	handler: "lms2415m01formhandler",
                        type: "POST",
                        data:{
                            formAction: "delete",
                            oids: content,
                            txCode: $("#detailTxCode").val()
                        },
                        success: function(responseData){
                        	$("#show_totBal").val(responseData.show_totBal);
                        	$("#show_totQuota").val(responseData.show_totQuota);
                            // 重新載入grid內容
                            $("#C2415M01bGrid").trigger("reloadGrid");
                        }
                    });
                }
            });
        }
    	
    });
    $("#_lms2415s02ButtonDel").click(function(){
    	// confirmDelete=是否確定刪除?
        CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b){
            if (b) {
                $.ajax({
                    type: "POST",
                    handler: "lms2415m01formhandler",
                    data: {
                        formAction: "deleteCredit",
                        mainId: $("#detailMainId").val(),
                        txCode: $("#detailTxCode").val()
                    },
                    success: function(responseData){
                    	$("#show_totBal").val(responseData.show_totBal);
                    	$("#show_totQuota").val(responseData.show_totQuota);
                        // 重新載入grid內容
                        $("#C2415M01bGrid").trigger("reloadGrid");
                    }
                });
            }
        });
    });
    
    //====重新引進授信資料=============================================================
    $("#_lms2415s02ButtonImport").click(function add(){
        $.ajax({
            handler: "lms2415m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "addCredit",
                oid: $("#detailMainId").val(),
                mainId: $("#detailMainId").val()
            },
            success: function(responseData){
                /*-- 額度合計 , 前日結欠餘額合計 顯示 --*/
                $("#show_totQuota").val(responseData.show_totQuota);
                $("#show_totBal").val(responseData.show_totBal);
                // 重新載入grid內容
                $("#C2415M01bGrid").trigger("reloadGrid");
            }
        });
    });
    
    //重新引進主從債務人資料
    $("#_lms2415s02ReImportData").click(function(){
        $.ajax({
            handler: "lms2415m01formhandler",
            type: "POST",
            dataType: "json",
            data: {
                formAction: "reImportData",
                mainId: $("#detailMainId").val(),
                oid: tempOid,
                txCode: $("#detailTxCode").val()
            },
            success: function(responseData){
                /*-- 額度合計 , 前日結欠餘額合計 顯示 --*/
                $("#guarantor").val(responseData.guarantor);
                $("#coBorrower").val(responseData.coBorrower);
            }
        });
    });

    function thickboxLock(result){
    	$("#dateOfReview").readOnly(result);
    	$("#subjectNo").readOnly(result);
    	$("#quotaNo").readOnly(result);
    	$("#reVolve").readOnly(result);
    	// 20230111,09763,J-111-0326 
    	// 5.動用期限或授信期間自系統引進可能有誤，須開放可人工修正
    	// 6.額度合計、餘額合計有誤(多算很多)，若無法確保正確，須開放可人工修正
    	//$("#quotaCurr").readOnly(result);
    	//$("#quotaAmt").readOnly(result);
    	//$("#balCurr").readOnly(result);
    	//$("#balAmt").readOnly(result);
    	//$("#useFDate").readOnly(result);
    	//$("#useEDate").readOnly(result);
    	//$("#loanFDate").readOnly(result);
    	//$("#loanEDate").readOnly(result);
    	$("#LNF020CrtDate").readOnly(result);
    	$("#LNF030CrtDate").readOnly(result);
    	$("#lnType").readOnly(result);
    	$("#overDueDate").readOnly(result);
    	$("#guaranteeKind").readOnly(result);
    	$("#lnBusiness").readOnly(result);
    }

    /***輸入查詢ID視窗***/
    function showRadio01Data(json) {
    	var quotaType = "";
    	if(json.C2415M01bForm){
    		if(json.C2415M01bForm.quotaType){
        		quotaType = json.C2415M01bForm.quotaType;
    		}
    	}
    	var data = {
    			width : "100%",
    			border : "none",
    			value : quotaType,
         	    size : 4,
         	   item : json.c241m01b_quotaType
         	};
    	var quotaType = $("#quotaType");
    	quotaType.setItems(data);
    }
});// 最外層



