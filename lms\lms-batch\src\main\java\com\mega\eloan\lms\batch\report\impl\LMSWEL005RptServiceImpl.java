package com.mega.eloan.lms.batch.report.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.Engine;
import com.inet.report.ReportException;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.report.AbstractIISIReportService;
import com.mega.eloan.lms.batch.report.LMSWEL005RptService;
import com.mega.eloan.lms.dao.C122M01ADao;
import com.mega.eloan.lms.dao.C122S01ADao;
import com.mega.eloan.lms.dao.C122S01BDao;
import com.mega.eloan.lms.model.C122M01A;
import com.mega.eloan.lms.model.C122S01B;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.NumericFormatter;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;

@Service("lmswel005rptservice")
public class LMSWEL005RptServiceImpl extends AbstractIISIReportService
		implements LMSWEL005RptService {

	@Resource
	BranchService branchService;
	@Resource
	C122M01ADao c122m01aDao;
	@Resource
	C122S01ADao c122s01aDao;
	@Resource
	C122S01BDao c122s01bDao;

	@Override
	public ReportData getReportParameter(PageParameters params, ReportData reportData,
			Engine engine) {

		String applyKind = UtilConstants.C122_ApplyKind.H;
		NumericFormatter nf = new NumericFormatter("#,###.##");

		Integer[] sumCNT = new Integer[2];
		Arrays.fill(sumCNT, 0);// 將全行件數合計設成0
		BigDecimal[] sumAMT = new BigDecimal[2];
		Arrays.fill(sumAMT, BigDecimal.ZERO); // 將全行金額合計設成0

		String[] applyStatus = { UtilConstants.C122_ApplyStatus.已核准 };

		TreeSet<String> brNoSet = c122m01aDao
				.queryOwnBranchListC122M01AByApplyStatus(applyKind, applyStatus );
		
		Timestamp nowTS = CapDate.getCurrentTimestamp();
		List<List<String>> details = new ArrayList<List<String>>();
		try {
			for (String brNo : brNoSet) {

				// 依月份順序顯示該分行已核貸資料
				List<C122M01A> list = c122m01aDao.queryCreditLineByBranch(applyKind, brNo);
				if (list.isEmpty()) {
					continue;
				}

				int brCNT = 0; // 先將該分行件數合計設成0

				// 處理每筆結案資料
				for (C122M01A c122m01a : list) {

					List<String> detail = new ArrayList<String>();

					if (brCNT==0) {
						detail.add(brNo+ " "+ branchService.getBranchName(brNo));
					} else {
						detail.add("");
					}

					++brCNT;
					detail.add(c122m01a.getCustId());
					detail.add(c122m01a.getCustName());

					int s01a_batchNo = c122s01aDao.maxBatchNoInMainId(c122m01a.getMainId());
					List<C122S01B> s01b_list = c122s01bDao.findByMainIdBatchNo(c122m01a.getMainId(), s01a_batchNo);
					TreeSet<String> cntrNo_set = new TreeSet<String>();
					for(C122S01B c122s01b: s01b_list){
						cntrNo_set.add(c122s01b.getCntrNo());
					}
					detail.add(StringUtils.join(cntrNo_set, "\r\n,")); // 額度序號					

					
					BigDecimal amt = CapMath.bigDecimalIsNullToZero(c122m01a.getApproveAmt());

					if (LMSUtil.cmp_yyyyMM(nowTS, "==", c122m01a.getApplyTS())) {
						detail.add(CapDate.formatDate(c122m01a.getApplyTS(),"yyyy-MM-dd"));

						detail.add(nf.reformat(CapMath.setScale(String.valueOf(amt), 0)));

						detail.add("");
						detail.add(nf.reformat(CapMath.setScale(String.valueOf(amt), 0)));
						sumCNT[0] += 1;
						sumAMT[0] = sumAMT[0].add(amt);

					} else {
						detail.add("");
						detail.add("");
						detail.add(CapDate.formatDate(c122m01a.getApplyTS(),"yyyy-MM-dd"));
						detail.add(nf.reformat(CapMath.setScale(String.valueOf(amt), 0)));

						sumCNT[1] += 1;
					}
					sumAMT[1] = sumAMT[1].add(amt); // 分行已核累計合計金額

					details.add(detail);
				}
			}

			// 全行合計
			for (int i = 0; i < 2; i++) {
				reportData.setField("cnt" + i, sumCNT[i] + "件");
				reportData.setField(
						"amt" + i,
						nf.reformat(CapMath.setScale(
								CapMath.bigDecimalToString(sumAMT[i]), 0)));
			}

			reportData.addDetail(details);
			reportData.setField("printDate",
					CapDate.getCurrentDate("yyyy-MM-dd"));

		} catch (CapFormatException e) {
			e.printStackTrace();
		}
		return reportData;
	}

	@Override
	public String getReportDefinition() {
		return "report/lms/LLMEL005"; //(rpt 上的 FORM:LLMEL021) SLMS-00036
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}
		}
	}

}
