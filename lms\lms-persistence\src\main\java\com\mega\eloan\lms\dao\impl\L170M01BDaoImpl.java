package com.mega.eloan.lms.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L170M01BDao;
import com.mega.eloan.lms.dao.LMSJpaDao;

import com.mega.eloan.lms.model.L170M01B;


/**
 * <pre>
 * 一般授信資料檔
 * </pre>
 * 
 * @since 2011/9/19
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/19,jessica,new
 *          </ul>
 */

@Repository
public class L170M01BDaoImpl extends LMSJpaDao<L170M01B, String> implements
		L170M01BDao {

	@Override
	public L170M01B findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L170M01B> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		List<L170M01B> list = null;
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(L170M01B.class, search).getResultList();
		}
		return list;
	}
	@Override
	public List<L170M01B> findByCustIdDupId(String custId,String DupNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", DupNo);
		List<L170M01B> list = createQuery(L170M01B.class,search).getResultList();
		return list;
	}
	@Override
	public List<L170M01B> findByCntrNo(String CntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", CntrNo);
		search.addOrderBy("cntrNo");
		List<L170M01B> list = createQuery(L170M01B.class,search).getResultList();
		
		return list;
	}
	@Override
	public L170M01B findByUniqueKey(String mainId, String custId, String dupNo,
			String cntrNo) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.addSearchModeParameters(SearchMode.EQUALS, "custId", custId);
		search.addSearchModeParameters(SearchMode.EQUALS, "dupNo", dupNo);
		search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		return findUniqueOrNone(search);
	}

	// @Override
	// public boolean deleteL170m01bList(String mainId) {
	// // Orm.xml
	// Query query = getEntityManager().createNamedQuery(
	// "L170M01B.deleteByMainId");
	// // 設置參數
	// query.setParameter("MAINID", mainId);
	// query.executeUpdate();
	// return true;
	//
	// }

	@Override
	public boolean deleteL170m01bListNotLnDataDate(String mainId) {
		// Orm.xml
		Query query = getEntityManager().createNamedQuery(
				"L170M01B.deleteByMainIdNotLnDataDate");
		// 設置參數
		query.setParameter("MAINID", mainId);
		query.executeUpdate();
		return true;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> findCntrNo(String cntrNo) {
		Query query = getEntityManager().createNamedQuery("L170M01B.selCntrNo");
		query.setParameter("CNTRNO", cntrNo);
		return query.getResultList();
	}

}