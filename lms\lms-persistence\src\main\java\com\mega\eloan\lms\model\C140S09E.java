package com.mega.eloan.lms.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.util.CapMath;

import com.mega.eloan.common.model.RelativeMeta;


/**
 * <pre>
 * C140S09E model.
 * </pre>
 * 
 * @since 2011/10/27
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/10/27,<PERSON>,new</li>
 *          </ul>
 */
@NamedEntityGraph(name = "C140S09E-entity-graph", attributeNodes = { @NamedAttributeNode("c140m01a") })
@Entity
@Table(name="C140S09E", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class C140S09E extends RelativeMeta implements Serializable {
	private static final long serialVersionUID = 1L;
	private static final String NA = "N.A.";

	@Column(name="G_FIN_1", precision=12)
	private BigDecimal gFin1;

	@Column(name="G_FIN_2", precision=12)
	private BigDecimal gFin2;

	@Column(name="G_FIN_3", precision=12)
	private BigDecimal gFin3;

	@Column(name="G_FIN_4", precision=12)
	private BigDecimal gFin4;

	@Column(name="G_FIN_5", precision=12)
	private BigDecimal gFin5;

	@Column(name="G_FIN_6", precision=12)
	private BigDecimal gFin6;
	
	//淨值
	@Column(name="G_FIN_7", precision=12)
	private BigDecimal gFin7;

	@Column(name="G_ID", length=10)
	private String gId;

	/**
	 * J-105-0080-001 Web e-Loan授信管理系統集團轄下公司名稱欄位放大為38個全形字
	 * length=60->length=120
	 */
	@Column(name="G_NA", length=120)
	private String gNa;

	@Column(name="G_RTO_1", precision=11, scale=2)
	private BigDecimal gRto1;

	@Column(name="G_RTO_2", precision=11, scale=2)
	private BigDecimal gRto2;

	@Column(name="G_RTO_3", precision=11, scale=2)
	private BigDecimal gRto3;

	// 幣別
	@Column(name="CURR8", length=3)
	private String curr8;
	// 單位
	@Column(name="UNIT3", precision=12, scale=0)
	private BigDecimal unit3;
	
	//bi-directional many-to-one association to C140M01A
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({ @JoinColumn(name = "MAINID", referencedColumnName = "MAINID", nullable = false, insertable = false, updatable = false),
        @JoinColumn(name = "PID", referencedColumnName = "UID", nullable = false, insertable = false, updatable = false) })
	private C140M01A c140m01a;

	public BigDecimal getGFin1() {
		return this.gFin1;
	}

	public void setGFin1(BigDecimal gFin1) {
		this.gFin1 = gFin1;
	}

	public BigDecimal getGFin2() {
		return this.gFin2;
	}

	public void setGFin2(BigDecimal gFin2) {
		this.gFin2 = gFin2;
	}

	public BigDecimal getGFin3() {
		return this.gFin3;
	}

	public void setGFin3(BigDecimal gFin3) {
		this.gFin3 = gFin3;
	}

	public BigDecimal getGFin4() {
		return this.gFin4;
	}

	public void setGFin4(BigDecimal gFin4) {
		this.gFin4 = gFin4;
	}

	public BigDecimal getGFin5() {
		return this.gFin5;
	}

	public void setGFin5(BigDecimal gFin5) {
		this.gFin5 = gFin5;
	}

	public BigDecimal getGFin6() {
		return this.gFin6;
	}

	public void setGFin6(BigDecimal gFin6) {
		this.gFin6 = gFin6;
	}

	public BigDecimal getGFin7() {
		return this.gFin7;
	}

	public void setGFin7(BigDecimal gFin7) {
		this.gFin7 = gFin7;
	}
	
	public String getGId() {
		return this.gId;
	}

	public void setGId(String gId) {
		this.gId = gId;
	}

	public String getGNa() {
		return this.gNa;
	}

	public void setGNa(String gNa) {
		this.gNa = gNa;
	}

	public String getGRto1() {
		if(this.gRto1 == null){
			return NA;
		}
		return CapMath.bigDecimalToString(this.gRto1);
	}

	public void setGRto1(BigDecimal gRto1) {
		this.gRto1 = gRto1;
	}

	public String getGRto2() {
		if(this.gRto2 == null){
			return NA;
		}
		return CapMath.bigDecimalToString(this.gRto2);
	}

	public void setGRto2(BigDecimal gRto2) {
		this.gRto2 = gRto2;
	}

	public String getGRto3() {
		if(this.gRto3 == null){
			return NA;
		}
		return CapMath.bigDecimalToString(this.gRto3);
	}

	public void setGRto3(BigDecimal gRto3) {
		this.gRto3 = gRto3;
	}

	public C140M01A getC140m01a() {
		return this.c140m01a;
	}

	public void setC140m01a(C140M01A c140m01a) {
		this.c140m01a = c140m01a;
	}
	
	public void setCurr8(String curr8) {
		this.curr8 = curr8;
	}

	public String getCurr8() {
		return curr8;
	}

	public void setUnit3(BigDecimal unit3) {
		this.unit3 = unit3;
	}

	public BigDecimal getUnit3() {
		return unit3;
	}
	
}