package com.mega.eloan.lms.fms.handler.grid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.CLSService;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.base.service.RPAProcessService;
import com.mega.eloan.lms.dw.service.DwLnquotovService;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.fms.service.LMS8300Service;
import com.mega.eloan.lms.mfaloan.service.LNLNF013Service;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L830M01A;
import com.mega.eloan.lms.model.L830M01B;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 客戶帳戶管理員維護作業
 * </pre>
 * 
 * @since 2022/10/31
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
@Scope("request")
@Controller("lms8300gridhandler")
public class LMS8300GridHandler extends AbstractGridHandler {

	@Resource
	LMS8300Service lms8300Service;
	
	@Resource
	CodeTypeService codeTypeService;
	
	@Resource
	DocFileService docFileService;
	
	@Resource
	EloandbBASEService eloandbService;
	
	@Resource
	DwLnquotovService dwLnquotovService;
	
	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	LMSService lmsService;

	@Resource
	CLSService clsService;
	
	@Resource
	RPAProcessService rpaProcessService;
	
	@Resource
	LNLNF013Service lnLNF013Service;
	
	/**
	 * 客戶帳戶管理員維護grid(L830M01B)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL830m01B(ISearch pageSetting,
			PageParameters params) throws CapException {

		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, 
				UtilConstants.Field.目前編製行, user.getUnitNo());

		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		
		
		final Map<String, String> MaintainTypeDescMap = lms8300Service.get_maintainTypeDescMap();
		Map<String, IFormatter> formatter = new HashMap<String, IFormatter>();
		formatter.put("maintainType", new IFormatter() {
			@Override
			public String reformat(Object in) throws CapFormatException {
				String maintainType = Util.trim(in);
				return LMSUtil.getDesc(MaintainTypeDescMap, maintainType);
			}
		});
		
		Page<? extends GenericBean> page = lms8300Service.findPage(
				L830M01B.class, pageSetting);
		List<L830M01B> l830m01blist = (List<L830M01B>) page.getContent();
		return new CapGridResult(l830m01blist, page.getTotalRow(),formatter);

	}
	
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryL830m01A(ISearch pageSetting,PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params.getString(EloanConstants.DOC_STATUS));
		String mainId = Util.trim(params.getString("mainId"));
		List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if (!Util.isEmpty(l830m01aList)) {
			for(L830M01A l830m01a : l830m01aList){
				String LNF013_BR_NO = l830m01a.getOwnBrId();
				String LNF013_STAFF_NO = l830m01a.getOrig_AOId();
				String LNF013_CUST_ID = l830m01a.getALcustId();
				String CNAME = l830m01a.getCustName();
				
				Map<String, Object> row = this.build_caseinfData_row(LNF013_BR_NO, LNF013_STAFF_NO, LNF013_CUST_ID, CNAME);
				list.add(row);
			}
		}
		return new CapMapGridResult(list, list.size(),null);
		
	}
	
	
	//一條件搜尋A-LOAN資料(LNF013)
	@SuppressWarnings("unchecked")
	public CapMapGridResult queryLNF013(ISearch pageSetting,PageParameters params) throws CapException {
		
		String maintainType = Util.trim(params.getString("maintainType"));
		String custId = Util.trim(params.getString("custId"));
		String dupNo = Util.trim(params.getString("dupNo"));
		String fullCustId = custId+dupNo;
		String origAOId = Util.trim(params.getString("origAOId"));
		String mainId = Util.trim(params.getString("mainId"));
		boolean isNew = params.getAsBoolean("isNew");
		
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String caseBrno = user.getUnitNo();
		List<Map<String, Object>> page = new ArrayList<Map<String, Object>>();
		if(maintainType.equals("S")){ //單筆維護
			page = lnLNF013Service.findByUnidAndCustId(caseBrno,fullCustId,UtilConstants.Casedoc.DocType.個金);
		}else if(maintainType.equals("B")){ //批次維護
			page = lnLNF013Service.findByUnidAndAOId(caseBrno,origAOId,UtilConstants.Casedoc.DocType.個金);
		}
		if(isNew && maintainType.equals("S")){ //單筆維護新增 >> 預設勾選
			for(int i=0; i<page.size();i++){
				Map<String, Object> pageMap = page.get(i);
				pageMap.put("CHFLAG", "Y");
				page.get(i).putAll(pageMap);
			}
		}
		
		if(!isNew){
			if(!mainId.isEmpty()){
				List<L830M01A> l830m01aList = (List<L830M01A>) lms8300Service.findListByMainId(L830M01A.class, mainId);
				for(int i=0; i<page.size();i++){
					Map<String, Object> pageMap = page.get(i);
					String custid = (String) pageMap.get("LNF013_CUST_ID");
					for(int j=0;j<l830m01aList.size();j++){
						L830M01A l830m01a = l830m01aList.get(j);
						if(custid.equals(Util.trim(l830m01a.getALcustId()))){//有勾選
							//重新定義CHFLAG
							pageMap.put("CHFLAG", "Y");
							//塞回pageList
							page.get(i).putAll(pageMap);
							//刪除這一筆，減輕下一次迴圈負擔
//							l830m01aList.remove(j);
							//終止本次筆對迴圈
							break;
						}
					}
				}
			}
		}
		
        return new CapMapGridResult(page, page.size(),null);
	}
	private Map<String, Object> build_caseinfData_row(String LNF013_BR_NO, String LNF013_STAFF_NO ,String LNF013_CUST_ID, String CNAME){
		Map<String, Object> row = new HashMap<String, Object>();
		row.put("LNF013_BR_NO", LNF013_BR_NO);
		row.put("LNF013_STAFF_NO", LNF013_STAFF_NO);
		row.put("LNF013_CUST_ID", LNF013_CUST_ID);
		row.put("CNAME", CNAME);
		return row;
	}
	
	/**
	 * 以房養老貸款撥款查詢明細grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */
//	@SuppressWarnings("unchecked")
//	public CapMapGridResult queryL820m01C(ISearch pageSetting, PageParameters params) 
//	throws CapException {
//		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
//		//String custId = Util.trim(params.getString("custId"));
//		//String dupNo = Util.trim(params.getString("dupNo"));
//		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//		
//		List<L820M01C> l820m01clist = (List<L820M01C>) lms8200Service.findListByMainId(L820M01C.class, mainId);
//		
//		if (!Util.isEmpty(l820m01clist)) {
//			for(L820M01C l820m01c : l820m01clist){
//				String oid = l820m01c.getOid();
//				String cmainid = l820m01c.getMainId();
//				String custId = l820m01c.getCustId();
//				String dupNo = l820m01c.getDupNo();
//				String cntrNo = l820m01c.getCntrNo();
//				String lnf030_loan_no = l820m01c.getLnf242_loan_no();
//				String estimateTime = CapDate.getDateTimeFormat(l820m01c.getEstimateTime()).substring(0, 10);
//				String actualTime = CapDate.getDateTimeFormat(l820m01c.getActualTime()).substring(0, 10);
//				
//				Map<String, Object> row = this.build_caseinfData_row(oid, cmainid, custId, dupNo, cntrNo, lnf030_loan_no, estimateTime, actualTime);
//				list.add(row);
//			}
//		}
//		
//		return new CapMapGridResult(list, list.size());		
//	}
	
//	public CapMapGridResult queryDataArchivalRecordData(ISearch pageSetting, PageParameters params) 
//	throws CapException {
//		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//		String mainId = Util.trim(params.getString(EloanConstants.MAIN_ID));
//		String custId = Util.trim(params.getString("custId"));
//		String dupNo = Util.trim(params.getString("dupNo"));		
//		String icon_ok = "○";
//		String icon_warn = "▲";
//		
//		Properties prop_lms8200 = MessageBundleScriptCreator.getComponentResource(LMS8200M01Page.class);
//		if (Util.isNotEmpty(custId)) {
//			if (true) {
//				
//				if(true){ // 資料建檔相關data
//					List<L820M01S> m01s_list = new ArrayList<L820M01S>();
//					m01s_list.addAll(lms8200Service.findL820M01S_byIdDupDataType(mainId, custId, dupNo, "5"));//內政部國民身分證領換補資料
//					for(L820M01S l820m01s : m01s_list){
//						String oid = l820m01s.getOid();						
//						String dataType = l820m01s.getDataType();
//						String dataTypeDesc = l820m01s_dataTypeDesc(prop_lms8200, l820m01s.getDataType());
//						String fileSeq = l820m01s.getFileSeq();
//						String queryTime = CapDate.getDateTimeFormat(l820m01s.getDataCreateTime());
//						String link = prop_lms8200.getProperty("L820M01S.open");
//						String dataStatus = l820m01s_dataStatus(icon_ok, icon_warn, l820m01s.getDataStatus());
//						String remark =  "";
//						String dataSrcMemo = "L820M01S";
//						Map<String, Object> row = build_queryDataArchivalRecordData_row(oid, mainId, dataType, dataTypeDesc, fileSeq, queryTime, link, dataStatus, remark, dataSrcMemo, "");
//						row.put("reportFileType", l820m01s.getReportFileType());
//						//-----------
//						list.add(row);				
//					}
//				}
//				
//			}
//
//		}
//		return new CapMapGridResult(list, list.size());
//	}
	
	
	
	/**
	 * 查詢都更危老註記維護作業grid(已覆核)
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapGridResult
	 * @throws CapException
	 */	
//	public CapMapGridResult queryGetCntrno(ISearch pageSetting,
//			PageParameters params) throws CapException {
//		String custId = Util.nullToSpace(params.getString("custId"));
//		String dupNo = Util.nullToSpace(params.getString("dupNo"));
//
//		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//		List<String> cntrnoList = new ArrayList<String>();
//		
//		if(!custId.isEmpty() && !dupNo.isEmpty()){
//			//簽報書的額度明細表額度序號
//			//取得 LN.LNF242 以房養老控制檔
//			//List<Map<String, Object>> l140m01as = misdbBASEService.selDistinctCntrnoByCustidDupno(custId, dupNo);
//			List<Map<String, Object>> l820m01_contractas = misdbBASEService.get_LNF242_Contractno(custId, dupNo, null);
//			
//			for (Map<String, Object> l820m01a : l820m01_contractas) {
//				Map<String, Object> row = new HashMap<String, Object>();
//	
//				String cntrNo = Util.trim(l820m01a.get("LNF242_CONTRACT"));
//				if(!cntrNo.isEmpty()){
//					if(cntrnoList != null && cntrnoList.contains(cntrNo)){
//						//排除重複
//					} else {
//						cntrnoList.add(cntrNo);	
//						row.put("cntrNo", cntrNo);
//						list.add(row);
//					}
//				}
//			}
//		
//			//排序
//			Collections.sort(list, new Comparator<Map<String, Object>>() {
//				public int compare(Map<String, Object> o1, Map<String, Object> o2) {
//					String name1 = o1.get("cntrNo").toString(); 
//					String name2 = o2.get("cntrNo").toString();
//	                return name1.compareTo(name2);
//				}
//			});
//		}
//		
//		return new CapMapGridResult(list, list.size());
//	}
}