/* 
 *ObsdbELF461Service.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.obsdb.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 授信消金狀態報送  ELF461
 * </pre>
 * 
 * @since 2012/1/4
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/1/4,REX,new
 *          </ul>
 */
public interface ObsdbELF461Service {

	/**
	 * 查詢 MIS.ELLNSEEK
	 * 
	 * @param BRNID
	 *            上傳分行代碼
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @return 查詢清單
	 */
	List<Map<String, Object>> findByKey(String BRNID, String custId,
			String dupNo, String cntrNo);

	/**
	 * 新增 上傳額度序號狀態至MIS.ELLNSEEK 提供總處查詢
	 * 
	 * @param BRNID
	 *            上傳分行代碼
	 * @param custId
	 *            客戶統編 (10)
	 * @param dupNo
	 *            重覆序號(1)
	 * @param cntrNo
	 *            額度序號 (12)
	 * @param brNo
	 *            分行代號 (3)
	 * @param status
	 *            文件狀態 (1)
	 * @param apprYY
	 *            年份 (4)
	 * @param apprMM
	 *            月份(2)
	 * @param cType
	 *            (1)
	 * @param updater
	 *            (8) 更新者
	 * @param gutcDate
	 *            (10) 信保日期
	 * @param proJno
	 *            (42) 案號
	 * @param property
	 *            (2) 性質
	 */
	public void insert(String BRNID, String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String cType, String updater, BigDecimal gutcDate,
			String proJno, String property, BigDecimal timestamp,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID,
			String ELF461_ISOFCLCGA, String ELF461_CGA_COUNTRY,
			String ELF461_CGA_CRDTYPE, String ELF461_CGA_CRDAREA,
			String ELF461_CGA_CRDPRED, String ELF461_CGA_CRDGRAD,
			BigDecimal ELF461_CGA_RSKRTO, BigDecimal ELF461_CGA_GRADSCR,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, 
			String isHighQualityProjOpt_3, String isHighQualityProjOpt_4, 
			String isHighQualityProjOpt_5, String isHighQualityProjResult, 
			String appDate, String appNo, String loancontype,
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL);

	/**
	 * 更新 上傳額度序號狀態至MIS.ELLNSEEK 提供總處查詢
	 * 
	 * @param BRNID
	 *            上傳分行代碼
	 * @param custId
	 *            客戶統編
	 * @param dupNo
	 *            重覆序號
	 * @param cntrNo
	 *            額度序號
	 * @param brNo
	 *            分行代號
	 * @param status
	 *            文件狀態
	 * @param apprYY
	 *            年份
	 * @param apprMM
	 *            月份
	 * @param cType
	 * @param updater
	 *            更新者
	 * @param gutcDate
	 *            信保日期
	 * @param proJno
	 *            案號
	 * @param property
	 *            性質
	 */
	public void update(String BRNID, String custId, String dupNo,
			String cntrNo, String brNo, String status, String apprYY,
			String apprMM, String cType, String updater, BigDecimal gutcDate,
			String proJno, String property, BigDecimal timestamp,
			String ELF461_ISREVIVE_Y, String ELF461_MAINID,
			String ELF461_ISOFCLCGA, String ELF461_CGA_COUNTRY,
			String ELF461_CGA_CRDTYPE, String ELF461_CGA_CRDAREA,
			String ELF461_CGA_CRDPRED, String ELF461_CGA_CRDGRAD,
			BigDecimal ELF461_CGA_RSKRTO, BigDecimal ELF461_CGA_GRADSCR,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, 
			String isHighQualityProjOpt_3, String isHighQualityProjOpt_4, 
			String isHighQualityProjOpt_5, String isHighQualityProjResult, 
			String appDate, String appNo, String loancontype, 
			String curr, BigDecimal curAmt, String currL, BigDecimal curAmtL);

	public void updateOnlyRevive(String BRNID, String custId, String dupNo,
			String cntrNo, String ELF461_ISREVIVE_Y, String ELF461_MAINID);

	public void updateOnlySpecialFinRisk(String BRNID, String custId,
			String dupNo, String cntrNo, BigDecimal timeStamp,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult);

	public void insertOnlySpecialFinRisk(String BRNID, String custId,
			String dupNo, String cntrNo, String brNo, String status,
			String apprYY, String apprMM, String updater, BigDecimal timeStamp,
			String isSpecialFinRisk, String specialFinRiskType,
			String isCmsAdcRisk, String isProjectFinOperateStag,
			String isHighQualityProjOpt_1, String isHighQualityProjOpt_2, String isHighQualityProjOpt_3, 
			String isHighQualityProjOpt_4, String isHighQualityProjOpt_5, String isHighQualityProjResult);

}
