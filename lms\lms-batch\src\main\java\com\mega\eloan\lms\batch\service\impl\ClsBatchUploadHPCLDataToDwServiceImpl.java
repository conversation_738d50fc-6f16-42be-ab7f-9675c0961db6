package com.mega.eloan.lms.batch.service.impl;

import javax.annotation.Resource;

import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.lms.base.service.CLSService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.jcs.common.Util;

// 此Service未受JPA控管，不具Transaction功能，故把邏輯寫至CLSService
@Service("clsBatchUploadHPCLDataToDwServiceImpl")
public class ClsBatchUploadHPCLDataToDwServiceImpl extends AbstractCapService
		implements WebBatchService {
	@Resource
	private CLSService clsService;

	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		JSONObject request = json.getJSONObject("request");
		String act = request.optString("act");
		String isFirstTime = request.optString("isFirstTime");
		String msg = "";
		try {
			if (Util.equals("upload_to_DW_ELOAN_APPLY", act)) {
				result = clsService.uploadHPCLDataToDW_ELOAN_APPLY(isFirstTime);

			} else if (Util.equals("upload_to_DW_ELOAN_REQUEST", act)) {
				result = clsService.uploadHPCLDataToDW_ELOAN_REQUEST(isFirstTime);

			} else {
				throw new CapException("unknown_act[" + act + "]", getClass());
			}

		} catch (Exception e) {
			msg = e.getMessage();
			logger.error(msg, e);
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, msg);
		}

		return result;
	}
}
