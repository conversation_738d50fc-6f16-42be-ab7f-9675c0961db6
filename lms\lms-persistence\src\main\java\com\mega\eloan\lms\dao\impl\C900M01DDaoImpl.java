/* 
 * C900M01DDaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;
import tw.com.jcs.common.Util;

import com.mega.eloan.lms.dao.C900M01DDao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.C900M01D;

/** 會計科子目名稱檔 **/
@Repository
public class C900M01DDaoImpl extends LMSJpaDao<C900M01D, String> implements
		C900M01DDao {

	@Override
	public C900M01D findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<C900M01D> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		List<C900M01D> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public C900M01D findByUniqueKey(String subjCode) {
		ISearch search = createSearchTemplete();
		if (subjCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode",
					subjCode);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public C900M01D findByUniqueKey02(String subjCode2) {
		ISearch search = createSearchTemplete();
		if (subjCode2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode2",
					subjCode2);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			return findUniqueOrNone(search);
		}
		return null;
	}

	@Override
	public String getSubjCode2(String subjCode) {
		C900M01D c900m01d = findByUniqueKey(subjCode);
		if (c900m01d != null) {
			return c900m01d.getSubjCode2();
		}
		return null;
	}

	@Override
	public String getUpMisSubjCode2(String subjCode) {
		String MisSubjCode = getSubjCode2(subjCode);
		if (MisSubjCode != null) {
			return Util.getLeftStr(MisSubjCode, 3);
		}
		return null;
	}

	/** 會計科子細目 **/
	@Override
	public List<C900M01D> findByIndex01(String subjCode) {
		ISearch search = createSearchTemplete();
		List<C900M01D> list = null;
		if (subjCode != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode",
					subjCode);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	/** 授信科目 **/
	@Override
	public List<C900M01D> findByIndex02(String subjCode2) {
		ISearch search = createSearchTemplete();
		List<C900M01D> list = null;
		if (subjCode2 != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "subjCode2",
					subjCode2);
		// 檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0) {
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<C900M01D> getAll() {
		ISearch search = createSearchTemplete();
		search.setFirstResult(0).setMaxResults(Integer.MAX_VALUE);
		return find(search);
	}
}