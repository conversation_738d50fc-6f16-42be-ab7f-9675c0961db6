
package com.mega.eloan.lms.mfaloan.service.impl;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisELF489Service;

/**
 * <pre>
 * 覆審(ID層)
 * </pre>
 * 
 * @since 2021/12/22
 * <AUTHOR>
 * @version <ul>
 *          <li>2021/12/22,EL08034,new
 *          </li>
 *          </ul>
 */
@Service
public class MisELF489ServiceImpl extends AbstractMFAloanJdbc implements
MisELF489Service {
	@Override
	public Map<String, Object> sel_by_brNo_idDup(String brNo, String custId, String dupNo){
		return this.getJdbc().queryForMap("ELF489.sel_by_brNo_idDup", new String[]{brNo+"%", custId, dupNo});
	}
	
}
