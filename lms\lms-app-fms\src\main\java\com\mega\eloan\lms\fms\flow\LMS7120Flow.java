package com.mega.eloan.lms.fms.flow;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import com.mega.eloan.common.exception.FlowMessageException;
import com.mega.eloan.common.flow.AbstractFlowHandler;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.utils.StrUtils;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.NumberService;
import com.mega.eloan.lms.dao.L140M01QDao;
import com.mega.eloan.lms.dao.L712M01ADao;
import com.mega.eloan.lms.dao.L918S01ADao;
import com.mega.eloan.lms.mfaloan.service.MisELF506Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.model.L140M01Q;
import com.mega.eloan.lms.model.L712M01A;
import com.mega.eloan.lms.obsdb.service.ObsdbELF506Service;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.model.IBranch;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.flow.FlowInstance;
import tw.com.jcs.flow.core.FlowException;

/**
 * <pre>
 * 授管處解除停權 - 流程
 * </pre>
 * 
 * @since 2013/1/24
 * <AUTHOR>
 * @version <ul>
 *          <li>2013/1/24,Miller,new
 *          </ul>
 */
@Component
public class LMS7120Flow extends AbstractFlowHandler {
	public static final String FLOW_CODE = "LMS7120Flow";

	@Resource
	L712M01ADao l712m01aDao;

	@Resource
	L918S01ADao l918s01aDao;

	@Resource
	BranchService branch;
	@Resource
	NumberService number;
	@Resource
	MisStoredProcService misStoredProcService;

	@Resource
	L140M01QDao l140m01qDao;
	@Resource
	MisELF506Service misELF506Service;

	@Resource
	ObsdbELF506Service obsdbELF506Service;

	@Resource
	BranchService branchService;

	@Override
	public Class<? extends Meta> getDomainClass() {
		return L712M01A.class;
	}

	/**
	 * 編製中到待覆核
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapMessageException
	 * @throws NumberFormatException
	 */
	@Transition(node = "停權編製中", value = "to停權待覆核")
	public void start(FlowInstance instance) throws NumberFormatException,
			CapMessageException {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L712M01A meta = (L712M01A) l712m01aDao.findByOid(instanceId);
		MegaSSOUserDetails unit = MegaSSOSecurityContext.getUserDetails();
		// 若無案號則進行給號
		if (Util.isEmpty(Util.trim(meta.getCaseSeq()))
				&& Util.isEmpty(Util.trim(meta.getCaseNo()))) {
			meta.setCaseSeq(Integer.parseInt(number.getNumberWithMax(
					L712M01A.class, unit.getUnitNo(), null, 99999)));
			meta.setCaseYear(Util.parseInt(TWNDate.toAD(new Date()).substring(
					0, 4)));
			StringBuilder caseNum = new StringBuilder();
			IBranch ibranch = branch.getBranch(unit.getUnitNo());
			caseNum.append(Util.toFullCharString(Util.trim(meta.getCaseYear())))
					.append(Util.trim(ibranch.getNameABBR()))
					.append(UtilConstants.Field.兆)
					.append(UtilConstants.Field.授字第)
					.append(Util.toFullCharString(Util.addZeroWithValue(
							Util.trim(meta.getCaseSeq()), 5)))
					.append(UtilConstants.Field.號);
			meta.setCaseNo(caseNum.toString());
			meta.setUpdater(unit.getUserId());
			meta.setUpdateTime(CapDate.getCurrentTimestamp());
		}
		l712m01aDao.save(meta);
	}

	@Transition(node = "停權待覆核", value = "to決策")
	public void next(FlowInstance instance) {
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L712M01A meta = (L712M01A) l712m01aDao.findByOid(instanceId);
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String result = (String) instance.getAttribute("result");
		if ("to核定".equals(result)) {
			// 檢查主管與經辦是否為同一人
			if (user.getUserId().equals(Util.trim(meta.getStopUpdater()))) {
				// EFD0053=WARN|覆核人員不可與“經辦人員或其它覆核人員”為同一人|
				throw new FlowMessageException("EFD0053");
			}
			instance.setAttribute("result", "to核定");
		} else {
			instance.setAttribute("result", "to退回停權編製中");
		}
	}

	/**
	 * 退回編製中
	 * 
	 * @param instance
	 *            流程資料
	 */
	@Transition(node = "決策", value = "to退回停權編製中")
	public void back(FlowInstance instance) {
		// String instanceId = instance.getParentInstanceId() != null ? instance
		// .getParentInstanceId().toString() : instance.getId().toString();
		// L120M01A meta = (L120M01A) metaDao.findByOid(getDomainClass(),
		// instanceId);
		// // 新增簽章欄
		// lmsService.deleteL120M01F(meta.getMainId(),
		// UtilConstants.BRANCHTYPE.分行,
		// new String[] { UtilConstants.STAFFJOB.執行覆核主管L4 });
		// // 變更額度明細表為編製中
		// lmsService.resetL140M01A(meta, FlowDocStatusEnum.編製中.getCode());
	}

	/**
	 * 核准
	 * 
	 * @param instance
	 *            流程資料
	 * @throws CapException
	 * @throws FlowException
	 */
	@Transition(node = "決策", value = "to核定")
	public void complete(FlowInstance instance) throws FlowException,
			CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String instanceId = instance.getParentInstanceId() != null ? instance
				.getParentInstanceId().toString() : instance.getId().toString();
		L712M01A meta = (L712M01A) l712m01aDao.findByOid(instanceId);

		String brnId = meta.getCaseBrId();
		Boolean upAs400 = false;

		// 上傳對大陸地區授信業務控管註記
		L140M01Q l140m01q = l140m01qDao.findByMainId(meta.getMainId());
		if (!Util.isEmpty(l140m01q)) {
			String cntrNo = Util.trim(meta.getCntrNo());
			String cnLoanFg = Util.trim(l140m01q.getCnLoanFg());
			String directFg = Util.trim(l140m01q.getDirectFg());
			// J-103-0314-001 Web e-Loan授信管理系統修改額度明細檢核附表有關大陸地區授信業務控管註記。
			String cnBusKind = Util.trim(l140m01q.getCnBusKind());
			String stRadeFg = Util.trim(l140m01q.getsTradeFg());
			BigDecimal guar1Rate = l140m01q.getGuar1Rate();
			BigDecimal guar2Rate = l140m01q.getGuar2Rate();
			BigDecimal guar3Rate = l140m01q.getGuar3Rate();
			BigDecimal coll1Rate = l140m01q.getColl1Rate();
			BigDecimal coll2Rate = l140m01q.getColl2Rate();
			BigDecimal coll3Rate = l140m01q.getColl3Rate();
			BigDecimal coll4Rate = l140m01q.getColl4Rate();
			BigDecimal coll5Rate = l140m01q.getColl5Rate();
			String iGolFlag = Util.trim(l140m01q.getiGolFlag());
			String documentNo = LMSUtil.getUploadCaseNo("1",
					meta.getCaseYear(), user.getUnitNo(), meta.getCaseSeq(),
					true);
			String cnTMUFg = Util.trim(l140m01q.getCnTMUFg());

			// directFg = 12 或 14 cnBusKind 上傳才帶值 否則塞空白
			if (!"12".equals(directFg) && !"14".equals(directFg)) {
				cnBusKind = "";
			}

			// BGN J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別
			String loanTarget = Util.trim(l140m01q.getLoanTarget());
			String isType = Util.trim(l140m01q.getIsType());
			String grntType = Util.trim(l140m01q.getGrntType());
			String grntClass = Util.trim(l140m01q.getGrntClass());
			String tOthCrdType = Util.trim(l140m01q.getOthCrdType());

			String dfOthCrdTypeStr = "";
			if (Util.equals(grntClass, "2")) {
				dfOthCrdTypeStr = "NNNNNNNNNNNNNNNNNNNN";
			} else {
				dfOthCrdTypeStr = "                    ";
			}

			StringBuffer othCrdTypebuff = new StringBuffer(dfOthCrdTypeStr);
			String othCrdType = dfOthCrdTypeStr;

			if (Util.equals(grntClass, "2")) {
				if (Util.notEquals(Util.trim(tOthCrdType), "")) {
					String[] othCrdTypeArr = tOthCrdType.split("\\|");
					for (String strOthCrdType : othCrdTypeArr) {
						int index = Integer.valueOf(strOthCrdType);
						othCrdTypebuff.replace(index - 1, index, "Y");
					}
					othCrdType = othCrdTypebuff.toString();
				}
			}

			String unionArea3 = Util.trim(l140m01q.getUnionArea3());

			// J-105-0074-001 Web e-Loan
			// 授信管理系統額度明細表之大陸地區授信業務控管註記新增「是否由非大陸地區本行聯行開具擔保信用狀十足保證」。
			String nCnSblcFg = Util.trim(l140m01q.getNCnSblcFg());

			// 清除選單殘值欄位
			if (Util.notEquals(Util.trim(iGolFlag), "Y")) {
				isType = "";
			}

			if (Util.equals(Util.trim(loanTarget), "")) {
				grntType = "";
			} else {
				if (Util.notEquals(Util.trim(loanTarget).subSequence(0, 2),
						"12")) {
					grntType = "";
				}
			}

			// loanTarget,isType,grntType,grntClass,othCrdType,unionArea3
			// END J-104-00279-001 Web e-Loan授信管理系統大陸地區授信業務控管註記新增新授信對象別

			Map<String, Object> elf506 = misELF506Service.getByCntrNo(cntrNo);
			String custIdDupNo = "";
			String is722Flag = "";
			String modUnit = "";
			String docNo = "";
			Timestamp modTime = CapDate.getCurrentTimestamp();
			String sDate = "0001-01-01";
			String isBuy = "";
			String exItem = "";
			String isInstalment = "";
			// J-109-0470_05097_B1001 Web e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
			String prodKind = "";
			String adcCaseNo = "";
			String exceptFlag = "";
			String exceptFlagQAisY = "";
			String exceptFlagQAPlus = "";

			if (elf506 != null && !elf506.isEmpty()) {

				// 先把72-2相關資料讀出來再塞回去
				custIdDupNo = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_CUST_ID"));
				is722Flag = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_FLAG"));
				modUnit = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_MODUNIT"));
				docNo = (String) Util.trim(MapUtils.getObject(elf506,
						"ELF506_722_DOC_NO"));
				modTime = (Timestamp) MapUtils.getObject(elf506,
						"ELF506_722_MODTIME");
				sDate = CapDate.formatDate(
						(Date) MapUtils.getObject(elf506, "ELF506_722_SDATE"),
						UtilConstants.DateFormat.YYYY_MM_DD);
				isBuy = Util.trim(MapUtils.getString(elf506,
						"ELF506_722_IS_BUY"));
				exItem = Util.trim(MapUtils.getString(elf506,
						"ELF506_722_EX_ITEM"));

				isInstalment = Util.trim(MapUtils.getString(elf506,
						"ELF506_INSTALMENT"));

				// update
				String createUnit = MapUtils.getString(elf506,
						"ELF506_CREATEUNIT");
				Timestamp createTime = (Timestamp) MapUtils.getObject(elf506,
						"ELF506_CREATETIME");

				// J-109-0470_05097_B1001 Web
				// e-Loan授信簽案配合本行110年施行LTV法，土建融案件新增案件編號
				prodKind = Util.trim(MapUtils.getString(elf506,
						"ELF506_PROD_KIND"));
				adcCaseNo = Util.trim(MapUtils.getString(elf506,
						"ELF506_ADC_CASENO"));
				
				exceptFlag = Util.trim(elf506.get("ELF506_EXCEPT"));
				exceptFlagQAisY = Util.trim(elf506.get("ELF506_EX_QA_Y"));
				exceptFlagQAPlus = Util.trim(elf506.get("ELF506_EX_QA_PLUS"));

				misELF506Service.delete(cntrNo);
				misELF506Service.insert(cntrNo, cnLoanFg, directFg, stRadeFg,
						guar1Rate, guar2Rate, guar3Rate, coll1Rate, coll2Rate,
						coll3Rate, coll4Rate, coll5Rate,
						new Timestamp(System.currentTimeMillis()), createTime,
						createUnit, "ELOAN", documentNo, iGolFlag, cnTMUFg,
						cnBusKind, custIdDupNo, is722Flag, modUnit, docNo,
						modTime, sDate, isBuy, exItem, loanTarget, isType,
						grntType, grntClass, othCrdType, unionArea3, nCnSblcFg,
						isInstalment, prodKind, adcCaseNo, exceptFlag, exceptFlagQAisY, 
						exceptFlagQAPlus);

			} else {

				// insert
				misELF506Service.insert(cntrNo, cnLoanFg, directFg, stRadeFg,
						guar1Rate, guar2Rate, guar3Rate, coll1Rate, coll2Rate,
						coll3Rate, coll4Rate, coll5Rate,
						new Timestamp(System.currentTimeMillis()),
						new Timestamp(System.currentTimeMillis()), "ELOAN",
						"ELOAN", documentNo, iGolFlag, cnTMUFg, cnBusKind,
						custIdDupNo, is722Flag, modUnit, docNo, modTime, sDate,
						isBuy, exItem, loanTarget, isType, grntType, grntClass,
						othCrdType, unionArea3, nCnSblcFg, isInstalment,
						prodKind, adcCaseNo, exceptFlag, exceptFlagQAisY, 
						exceptFlagQAPlus);
			}

			if (UtilConstants.BrNoType.國外.equals(branchService.getBranch(brnId)
					.getBrNoFlag())) {
				upAs400 = true;
			}

			if (upAs400) {

				String createUnit = "ELOAN";
				BigDecimal createTime = LMSUtil.covertAs400Time(new Timestamp(
						System.currentTimeMillis()));

				if (elf506 != null && !elf506.isEmpty()) {
					createUnit = MapUtils
							.getString(elf506, "ELF506_CREATEUNIT"); // 還是以MIS資料為主
					createTime = LMSUtil.covertAs400Time((Timestamp) MapUtils
							.getObject(elf506, "ELF506_CREATETIME")); // 還是以MIS資料為主
				}

				Map<String, Object> elf506As400 = obsdbELF506Service
						.getByCntrNo(brnId, cntrNo);

				String custIdDupNoOVS = "";
				String is722FlagOVS = "";
				String modUnitOVS = "";
				String docNoOVS = "";
				BigDecimal modTimeOVS = BigDecimal.ZERO;
				BigDecimal sDateOVS = BigDecimal.ZERO;
				String isBuyOVS = "";
				String exItemOVS = "";
				String exceptFlagOVS = "";
				String exceptFlagQAisYOVS = "";
				String exceptFlagQAPlusOVS = "";

				if (elf506As400 != null && !elf506As400.isEmpty()) {
					// update

					// 先把72-2相關資料讀出來再塞回去
					custIdDupNoOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_CUST_ID"));
					is722FlagOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_FLAG"));
					modUnitOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_MODUNIT"));
					docNoOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_DOC_NO"));
					modTimeOVS = (BigDecimal) MapUtils.getObject(elf506As400,
							"ELF506_722_MODTIME");
					sDateOVS = (BigDecimal) MapUtils.getObject(elf506As400,
							"ELF506_722_SDATE");
					isBuyOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_IS_BUY"));
					exItemOVS = (String) Util.trim(MapUtils.getObject(
							elf506As400, "ELF506_722_EX_ITEM"));
					exceptFlagOVS = Util.trim(elf506As400.get("ELF506_EXCEPT"));
					exceptFlagQAisYOVS = Util.trim(elf506As400.get("ELF506_EX_QA_Y"));
					exceptFlagQAPlusOVS = Util.trim(elf506As400.get("ELF506_EX_QA_PLUS"));

					obsdbELF506Service.deleteByCntrNo(brnId, cntrNo);

					obsdbELF506Service.insert(brnId, cntrNo, cnLoanFg,
							directFg, stRadeFg, guar1Rate, guar2Rate,
							guar3Rate, coll1Rate, coll2Rate, coll3Rate,
							coll4Rate, coll5Rate, LMSUtil
									.covertAs400Time(new Timestamp(System
											.currentTimeMillis())), createTime,
							createUnit, "ELOAN", documentNo, iGolFlag, cnTMUFg,
							cnBusKind, custIdDupNoOVS, is722FlagOVS,
							modUnitOVS, docNoOVS, modTimeOVS, sDateOVS,
							isBuyOVS, exItemOVS, loanTarget, isType, grntType,
							grntClass, othCrdType, unionArea3, nCnSblcFg,
							isInstalment, prodKind, adcCaseNo, exceptFlagOVS, 
							exceptFlagQAisYOVS, exceptFlagQAPlusOVS);

				} else {
					// insert
					obsdbELF506Service.insert(brnId, cntrNo, cnLoanFg,
							directFg, stRadeFg, guar1Rate, guar2Rate,
							guar3Rate, coll1Rate, coll2Rate, coll3Rate,
							coll4Rate, coll5Rate, LMSUtil
									.covertAs400Time(new Timestamp(System
											.currentTimeMillis())), createTime,
							"ELOAN", "ELOAN", documentNo, iGolFlag, cnTMUFg,
							cnBusKind, custIdDupNoOVS, is722FlagOVS,
							modUnitOVS, docNoOVS, modTimeOVS, sDateOVS,
							isBuyOVS, exItemOVS, loanTarget, isType, grntType,
							grntClass, othCrdType, unionArea3, nCnSblcFg,
							isInstalment, prodKind, adcCaseNo, exceptFlagOVS, 
							exceptFlagQAisYOVS, exceptFlagQAPlusOVS);
				}
			}
		}

		// 設定覆核主管與覆核時間
		meta.setStopApprover(user.getUserId());
		meta.setStopApprTime(CapDate.getCurrentTimestamp());
		meta.setApproveTime(CapDate.getCurrentTimestamp());
		meta.setUpdateTime(CapDate.getCurrentTimestamp());
		l712m01aDao.save(meta);
	}

	/**
	 * 取得完整Id(統編加重覆序號)
	 * 
	 * @param custid
	 * @param dupNo
	 * @return
	 */
	private String getAllCust(String custid, String dupNo) {
		StringBuilder strb = new StringBuilder();
		// if ("0".equals(dupNo)) {
		// dupNo = "";
		// }
		return strb.append(CapString.fillString(custid, 10, false, ' '))
				.append(dupNo).toString();
	}

	/**
	 * 取得上傳案號-> 民國年 + 分行別+{LMS/CLS}+末五碼流水號
	 * 
	 * @param l712m01a
	 *            授管處解除停權主檔
	 * @return
	 */
	private static String getUploadCaseNo(L712M01A l712m01a) {
		String schema = "LMS";
		String custId = Util.trim(l712m01a.getCustId());
		// 統編：第一碼英文、第二~十數字(長度是10)--CLS
		if (custId.length() == 10) {
			char c = custId.charAt(0);
			if (c >= 'A' && c <= 'Z') {
				if (Util.isNumeric(custId.substring(1))) {
					schema = "CLS";
				}
			}
		}
		return StrUtils.concat(l712m01a.getCaseYear() - 1911,
				l712m01a.getOwnBrId(), schema,
				Util.addZeroWithValue(l712m01a.getCaseSeq(), 5));
	}

	@SuppressWarnings("rawtypes")
	@Override
	public Class getDocStatusEnumClass() {
		return CreditDocStatusEnum.class;
	}
}