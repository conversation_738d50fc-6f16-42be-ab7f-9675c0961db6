package com.mega.eloan.lms.mfaloan.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.mega.eloan.lms.mfaloan.service.MisElcsecntService;

@Service
public class MisElcsecntServiceImpl extends AbstractMFAloanJdbc implements
		MisElcsecntService {

	@Override
	public List<Map<String, Object>> findElcsecntforType7ByAllBrNoAndDate(
			String yy, String mm) {

		return this.getJdbc().queryForListWithMax("Elcsecnt.sel7ByDateAllBrNo",
				new Object[] { yy, mm, yy, mm });
	}

	@Override
	public List<Map<String, Object>> findElcsecntforType8ByAllBrNoAndDate(
			String yy, String mm, String ctype) {
		return this.getJdbc().queryForList("Elcsecnt.sel8ByDateAllBrNo",
		// new Object[] { yy, mm,tCaseDept, ctype });
				new Object[] { yy, mm, ctype });
	}

	@Override
	public void insertElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseDept, Integer citem1, Integer citem2,
			Integer citem3, Integer citem4, Integer citem5, BigDecimal appramt,
			String updater, String caseNo, String cntrNo, String ISCOLLRT,
			Double COLLRT, String comID, String comDupNo, String lnType,
			String caseLevel, String newCust) {
		this.getJdbc().update(
				"ELCSECNT.insert",
				new Object[] { brno, appryy, apprmm, ctype, caseDept, citem1,
						citem2, citem3, citem4, citem5, appramt, updater,
						caseNo, cntrNo, ISCOLLRT, COLLRT, comID, comDupNo,
						lnType, caseLevel, newCust });
	}

	@Override
	public void updateElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, Integer citem1,
			Integer citem2, Integer citem3, Integer citem4, Integer citem5,
			BigDecimal appramt, String updater, String caseDept,
			String ISCOLLRT, Double COLLRT, String comID, String comDupNo,
			String lnType, String caseLevel, String newCust) {
		this.getJdbc().update(
				"ELCSECNT.update",
				new Object[] { brno, appryy, apprmm, ctype, caseNo, cntrNo,
						citem1, citem2, citem3, citem4, citem5, appramt,
						updater, caseDept, ISCOLLRT, COLLRT, comID, comDupNo,
						lnType, caseLevel, newCust, brno, appryy, apprmm,
						ctype, caseDept, caseNo, cntrNo });

	}

	@Override
	public boolean selectElcsecnt(String brno, String appryy, String apprmm,
			String ctype, String caseDept, String caseNo, String cntrNo) {
		return this
				.getJdbc()
				.queryForList(
						"ELCSECNT.select",
						new Object[] { brno, appryy, apprmm, ctype, caseDept,
								caseNo, cntrNo }).isEmpty();
	}

	@Override
	public void insertElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String casedept, String comID, String comDupNo,
			String lnType, String caseLevel) {
		this.getJdbc()
				.update("ELCSECNT.insert2",
						new Object[] { brno, appryy, apprmm, ctype, caseNo,
								cntrNo, citem6, citem7, citem8, citem9,
								citem10, appramt, updater, casedept, comID,
								comDupNo, lnType, caseLevel });

	}

	@Override
	public void updateElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo, int citem6, int citem7,
			int citem8, int citem9, int citem10, double appramt,
			String updater, String caseDept, String comID, String comDupNo,
			String lnType, String caseLevel) {
		this.getJdbc().update(
				"ELCSECNT.update2",
				new Object[] { brno, appryy, apprmm, ctype, caseNo, cntrNo,
						citem6, citem7, citem8, citem9, citem10, appramt,
						updater, caseDept, comID, comDupNo, lnType, caseLevel,
						brno, appryy, apprmm, ctype, caseNo, cntrNo });
	}

	@Override
	public boolean selectElcsecnt2(String brno, String appryy, String apprmm,
			String ctype, String caseNo, String cntrNo) {
		return this
				.getJdbc()
				.queryForList(
						"ELCSECNT.select2",
						new Object[] { brno, appryy, apprmm, ctype, caseNo,
								cntrNo }).isEmpty();
	}

	@Override
	public Map<String, Object> findElcsecntforLnType(String comId,
			String comDupNo, String lnType, String caseDept) {
		return this.getJdbc().queryForMap("ELCSECNT.findLnType1",
				new Object[] { comId, comDupNo, lnType, caseDept });
	}

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param appryy
	 * @param apprmm
	 * @return
	 */
	@Override
	public List<Map<String, Object>> selectElcsecnt_doLmsBatch0024(
			String appryy, String apprmm) {

		return this.getJdbc().queryForListWithMax(
				"ELCSECNT.select_doLmsBatch0024",
				new Object[] { appryy, apprmm });
	}

	/**
	 * J-108-0242_05097_B1001 Web e-Loan每月常董會報告事項彙總及申報案件數統計表新做案件之筆數統計再區分為新戶及原授信戶
	 * 
	 * @param brno
	 * @param appryy
	 * @param apprmm
	 * @param ctype
	 * @param caseDept
	 * @param caseNo
	 * @param cntrNo
	 * @param newCust
	 */
	@Override
	public void updateElcsecnt_doLmsBatch0024(String brno, String appryy,
			String apprmm, String ctype, String caseDept, String caseNo,
			String cntrNo, String newCust) {
		this.getJdbc().update(
				"ELCSECNT.update_doLmsBatch0024",
				new Object[] { newCust, brno, appryy, apprmm, ctype, caseDept,
						caseNo, cntrNo });

	}

}
