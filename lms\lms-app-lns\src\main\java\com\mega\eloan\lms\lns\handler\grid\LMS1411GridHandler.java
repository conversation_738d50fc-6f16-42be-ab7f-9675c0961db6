/* 
 *  LMS1411GridHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lns.handler.grid;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.Page;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.CapMapGridResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.Util;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.enums.CodeTypeEnum;
import com.mega.eloan.common.formatter.BranchNameFormatter;
import com.mega.eloan.common.formatter.BranchNameFormatter.ShowTypeEnum;
import com.mega.eloan.common.formatter.UserNameFormatter;
import com.mega.eloan.common.handler.grid.AbstractGridHandler;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.CodeTypeService;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.report.impl.LMS1205R01RptServiceImpl;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.lns.service.LMS1401Service;
import com.mega.eloan.lms.lns.service.LMS1411Service;
import com.mega.eloan.lms.model.L140M01A;
import com.mega.eloan.lms.model.L141M01A;
import com.mega.eloan.lms.model.L141M01C;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

/**
 * <pre>
 * 聯行額度明細表
 * </pre>
 * 
 * @since 2011/12/2
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/2,REX,new
 *          <li>2013/06/28,REX,移除此條件以免海外傳回的聯行額度明細表會看不見
 *          </ul>
 */
@Scope("request")
@Controller("lms1411gridhandler")
public class LMS1411GridHandler extends AbstractGridHandler {

	@Resource
	LMS1411Service lms1411Service;

	@Resource
	LMS1401Service lms1401Service;

	@Resource
	LMS1201Service lms1201Service;

	@Resource
	UserInfoService userInfoService;

	@Resource
	BranchService branchService;

	@Resource
	DocFileService docFileService;

	@Resource
	CodeTypeService codeTypeService;

	@Resource
	LMSService lmsService;

	/**
	 * 查詢聯行額度明細表外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL141m01a(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));

		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		// start 2013/06/28,REX,移除此條件以免海外傳回的聯行額度明細表會看不見
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
		// UtilConstants.Casedoc.typCd.海外);
		// end 2013/06/28,REX,移除此條件以免海外傳回的聯行額度明細表會看不見

		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms1411Service.findPage(
				L141M01A.class, pageSetting);
		List<L141M01A> l141m01as = (List<L141M01A>) page.getContent();
		for (L141M01A l141m01a : l141m01as) {
			l141m01a.setCaseBrId(l141m01a.getCaseBrId() + " "
					+ branchService.getBranchName(l141m01a.getCaseBrId()));
			l141m01a.setCoAppraiser(this.getUserName(l141m01a.getCoAppraiser()));
			l141m01a.setCaseNo(Util.toSemiCharString(l141m01a.getCaseNo()));

		}

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 篩選聯行額度明細表外部的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryL141m01a3(ISearch pageSetting, PageParameters params) throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String docStatus = Util.nullToSpace(params
				.getString(EloanConstants.DOC_STATUS));
		String custId = Util.trim(params.getString("custId"));
		String typCd = Util.trim(params.getString("typCd"));
		// String docType = Util.trim(params.getString("docType"));
		String custName = Util.trim(params.getString("custName"));
		String approveDateS = Util.nullToSpace(Util.trim(params
				.getString("approveDateS")));
		String approveDateE = Util.nullToSpace(Util.trim(params
				.getString("approveDateE")));

		if (Util.isNotEmpty(custId)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "custId",
					custId);
		}
		String[] docStatusArray = docStatus
				.split(UtilConstants.Mark.SPILT_MARK);
		Date fromDate = CapDate.parseDate(Util.nullToSpace(params
				.getString("fromDate")));
		Date endDate = CapDate.parseDate(Util.nullToSpace(params
				.getString("endDate") + " 23:59:59"));
		Object[] reason = { fromDate, endDate };
		// start 2013/06/28,REX,移除此條件以免海外傳回的聯行額度明細表會看不見
		// pageSetting.addSearchModeParameters(SearchMode.NOT_EQUALS, "typCd",
		// UtilConstants.Casedoc.typCd.海外);
		// end 2013/06/28,REX,移除此條件以免海外傳回的聯行額度明細表會看不見

		// 限定只顯示企金案件
		pageSetting.addSearchModeParameters(SearchMode.EQUALS, "docType",
				UtilConstants.Casedoc.DocType.企金);
		pageSetting.addSearchModeParameters(SearchMode.BETWEEN, "caseDate",
				reason);

		if (Util.isNotEmpty(typCd)) {
			pageSetting.addSearchModeParameters(SearchMode.EQUALS, "typCd",
					typCd);
		}
		// if (Util.isNotEmpty(docType)) {
		// pageSetting.addSearchModeParameters(SearchMode.EQUALS,
		// "docType",docType);
		// }
		if (Util.isNotEmpty(custName)) {
			pageSetting.addSearchModeParameters(SearchMode.LIKE, "custName",
					custName + "%");
		}
		if (!Util.isEmpty(approveDateS) && !Util.isEmpty(approveDateE)) {
			pageSetting.addSearchModeParameters(SearchMode.BETWEEN,
					"approveTime", new Object[] { Util.parseDate(approveDateS),
							Util.parseDate(approveDateE + " 23:59:59") });
		}

		pageSetting.addSearchModeParameters(SearchMode.IN, "docStatus",
				docStatusArray);// 取得文件狀態
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141a01a.authUnit", user.getUnitNo());
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Page<? extends GenericBean> page = lms1411Service.findPage(
				L141M01A.class, pageSetting);
		CapGridResult result = new CapGridResult(page.getContent(),
				page.getTotalRow());
		Map<String, IFormatter> dataReformatter = new HashMap<String, IFormatter>();
		dataReformatter.put("caseBrId", new BranchNameFormatter(branchService,
				ShowTypeEnum.IDSpaceName)); // 分行名稱格式化
		dataReformatter.put("coAppraiser", new UserNameFormatter(
				userInfoService)); // 使用者名稱格式化
		result.setDataReformatter(dataReformatter);

		return result;

	}

	/**
	 * 查詢檔案上傳的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapGridResult queryfile(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<DocFile> page = docFileService.readToGrid(pageSetting);

		return new CapGridResult(page.getContent(), page.getTotalRow());

	}

	/**
	 * 查詢共同借款人
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL141m01c(ISearch pageSetting,	PageParameters params) throws CapException {
		// 查這份文件的MinId
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		Page<? extends GenericBean> page = lms1411Service.findPage(
				L141M01C.class, pageSetting);

		String[] codeType = { UtilConstants.CodeTypeItem.企業關係,
				UtilConstants.CodeTypeItem.親屬關係,
				UtilConstants.CodeTypeItem.綜合關係_企業,
				UtilConstants.CodeTypeItem.綜合關係_親屬, "lms1605s03_rType",
				CodeTypeEnum.國家代碼.getCode() };
		Map<String, CapAjaxFormResult> codeMap = codeTypeService
				.findByCodeType(codeType);

		List<L141M01C> l141m01cs = (List<L141M01C>) page.getContent();
		for (L141M01C l140m01c : l141m01cs) {
			// 國別
			l140m01c.setNtCode((String) codeMap
					.get(CodeTypeEnum.國家代碼.getCode()).get(l140m01c.getNtCode()));

			if (Util.isEmpty(Util.trim(l140m01c.getCustRlt()))) {
				continue;
			}
			StringBuilder show = new StringBuilder("");
			int rkindm = l140m01c.getCustRlt().indexOf("X");

			switch (rkindm) {
			case 1:
				l140m01c.setCustRlt((String) codeMap.get(
						UtilConstants.CodeTypeItem.企業關係).get(
						l140m01c.getCustRlt()));
				break;
			case 0:
				l140m01c.setCustRlt((String) codeMap.get(
						UtilConstants.CodeTypeItem.親屬關係).get(
						l140m01c.getCustRlt()));
				break;
			case -1:
				char[] kind = l140m01c.getCustRlt().toCharArray();
				String kind1 = (String) codeMap.get(
						UtilConstants.CodeTypeItem.綜合關係_企業).get(
						String.valueOf(kind[0]));
				String kind2 = (String) codeMap.get(
						UtilConstants.CodeTypeItem.綜合關係_親屬).get(
						String.valueOf(kind[1]));
				show.append(kind1).append(" - ").append(kind2);
				l140m01c.setCustRlt(show.toString());
				break;
			}

		}
		return new CapGridResult(l141m01cs, page.getTotalRow());

	}

	/**
	 * 查詢需列印的grid
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	public CapMapGridResult queryPrint(ISearch pageSetting,	PageParameters params) throws CapException {
		String mainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				EloanConstants.MAIN_ID, mainId);
		pageSetting.setDistinct(true);
		Page<Map<String, Object>> page = lms1411Service.getPringMenu(mainId,
				pageSetting);
		// if ("1".equals(rptType)) {
		// rptType = "LMS1405R01";
		// rptName = rptProperties.getProperty("TITLE.RPTNAME8");
		// } else {
		// rptType = "LMS1405R02";
		// rptName = rptProperties.getProperty("TITLE.RPTNAME9");
		// }
		return new CapMapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 查詢額度明細表Grid 資料 在聯行額度明細表
	 * 
	 * @param pageSetting
	 *            ISearch
	 * @param params
	 *            PageParameters
	 * @return CapGridResult
	 * @throws CapException
	 */
	@SuppressWarnings("unchecked")
	public CapGridResult queryL140m01aByL141m01b(ISearch pageSetting, PageParameters params) throws CapException {
		// 查這份文件的MinId
		String caseMainId = Util.nullToSpace(params
				.getString(EloanConstants.MAIN_ID));
		// 文件的種類的1額度明細表、2額度批覆表、3母行法人提案意見
		// String itemType = Util.nullToSpace(params.getString("itemType",
		// "1"));
		// 第三個參數為formatting
		pageSetting.addSearchModeParameters(SearchMode.EQUALS,
				"l141m01b.mainId", caseMainId);
		pageSetting.addSearchModeParameters(SearchMode.IS_NULL,
				UtilConstants.Field.刪除時間, "");
		Properties rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1205R01RptServiceImpl.class);
		// pageSetting.addOrderBy("custId");
		Page<? extends GenericBean> page = lms1401Service.findPage(
				L140M01A.class, pageSetting);
		StringBuffer temp = new StringBuffer(0);
		List<L140M01A> l140m01as = (List<L140M01A>) page.getContent();
		for (L140M01A l140m01a : l140m01as) {

			String rptType = l140m01a.getL120m01c().getItemType();
			String rptName = "";
			if ("1".equals(rptType)) {
				rptType = "LMS1405R01";
				rptName = rptProperties.getProperty("TITLE.RPTNAME8");
			} else {
				rptType = "LMS1405R02";
				rptName = rptProperties.getProperty("TITLE.RPTNAME9");
			}
			l140m01a.setApprover(rptType);
			l140m01a.setCreator(rptName);
			l140m01a.setCustName(LMSUtil.concat(temp, l140m01a.getCustId(),
					" ", l140m01a.getDupNo(), " ", l140m01a.getCustName()));
		}
		return new CapGridResult(page.getContent(), page.getTotalRow());
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {
		if (Util.isEmpty(userId)) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

}
