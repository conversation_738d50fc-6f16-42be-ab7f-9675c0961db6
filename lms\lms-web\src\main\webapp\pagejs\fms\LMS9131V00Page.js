var pageAction = {
    build: function(){
        $.ajax({
            handler: "lms9131m01formhandler",
            data: {
                formAction: "getbanchId"
            },
            success: function(obj){
                if (obj.userbanchId == '918') {
                    thickBoxOpen();
                }
            }
        });
        
        
        pageAction.grid = $("#gridfile").iGrid({
            handler: 'lms9131gridhandler',
            height: 400,
            action: 'querylms9131v00sel',
            rowNum: 1500,
            sortname: "docStatus|custName",
            sortorder: "asc|desc",
            multiselect: true,
            rownumbers: true,
            colModel: [{
                colHeader: i18n.lms9131v00["LMS913V00.type"], //LMS913V00.type=狀態
                name: 'docStatus',
                align: "left",
                width: 30, //設定寬度
                sortable: false //是否允許排序
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.custId"], //LMS913V00.custId=借款人
                name: 'custName',
                align: "left",
                width: 50, //設定寬度
                sortable: false //是否允許排序
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.projno"], //LMS913V00.projno=案號
                name: 'caseNo',
                align: "left",
                width: 130, //設定寬度
                sortable: false //是否允許排序
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.famingdate"],// "核定日期",
                name: 'caseDate',
                width: 40,
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                },
                align: "left",
                sortable: true
            
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.finadate"], //LMS913V00.finadate=核准日期
                name: 'approveTime', //col.id
                align: "left",
                width: 40, //設定寬度
                formatter: 'date',
                formatoptions: {
                    srcformat: 'Y-m-d H:i:s',
                    newformat: 'Y-m-d'
                },
                align: "left",
                sortable: true
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.updateuser"], //LMS913V00.updateuser=修改人員
                name: 'updater', //col.id
                align: "left",
                width: 35, //設定寬度
                sortable: false //是否允許排序
            }, {
                colHeader: i18n.lms9131v00["LMS913V00.checkuser"], //LMS913V00.checkuser=覆核人員
                name: 'approver', //col.id
                align: "left",
                width: 35, //設定寬度
                sortable: false //是否允許排序
            }, {
                name: 'oid',
                hidden: true
            }, {
                name: 'mainId',
                hidden: true
            }],
            ondblClickRow: function(rowid){ //當使用者在Grid裡面某筆資料上雙點擊滑鼠就觸發修改功能
                var data = $("#gridfile").getRowData(rowid);
                openDoc(null, null, data);
            }
        });
        
        
    },
    /**
     * 開啟畫面
     */
    openWindow: function(data){
        $.form.submit({
            url: "../fms/lms9131m01",
            target: "_blank",
            data: {}
        });
    },
    /**
     * 取得資料表之選擇列
     */
    getRowData: function(){
        var row = pageAction.grid.getGridParam('selrow');
        var data;
        if (row) {
            data = pageAction.grid.getRowData(row);
        }
        else {
            MegaApi.showPopMessage(i18n.def["confirmTitle"], i18n.def["grid.selrow"]);
        }
        return data;
    },
    /**
     * 重整資料表
     */
    reloadGrid: function(data){
        if (data) {
            pageAction.grid.jqGrid("setGridParam", {
                postData: data,
                page: 1,
                search: true
            }).trigger("reloadGrid");
        }
        else {
            pageAction.grid.trigger('reloadGrid');
        }
    }
}

$(function(){
    pageAction.build();
    $("#btnChecks").click(function(){
        var rows = $("#gridfile").getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridfile").getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        if (list == "") {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error1']);
        }
        $.ajax({
            type: "POST",
            handler: "lms9131m01formhandler",
            data: {
                formAction: "UpdateELF447",
                listOid: list,
                sign: sign
            },
            success: function(json){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#gridfile").trigger("reloadGrid");//更新Grid內容						
            }
        });
    });
    $("#btnClear").click(function(){ //當使用者點選清除按鈕時處理清除工作
        var rows = $("#gridfile").getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridfile").getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        if (list == "") {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error1']);
        }
        
        $.ajax({
            type: "POST",
            handler: "lms9131m01formhandler",
            data: {
                formAction: "Clear",
                listOid: list,
                sign: sign,
				flag:''
            },
            success: function(json){
                CommonAPI.triggerOpener("gridview", "reloadGrid");
                $("#gridfile").trigger("reloadGrid");//更新Grid內容						
            }
        });
    });
    $("#btnModify").click(function(){
        var rows = $("#gridfile").getGridParam('selarrrow');
        var list = "";
        var sign = ",";
        for (var i = 0; i < rows.length; i++) { //將所有已選擇的資料存進變數list裡面
            if (rows[i] != 'undefined' && rows[i] != null && rows[i] != 0) {
                var data = $("#gridfile").getRowData(rows[i]);
                list += ((list == "") ? "" : sign) + data.oid;
            }
        }
        if (list == "") {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error1']);
        }
        if (rows.length != 1) {
            return CommonAPI.showMessage(i18n.lms9131v00['LMS913V00.error2']);
        }
        else {
            openDoc(null, null, data);
        }
    });
    
    
});
function openDoc(cellvalue, options, rowObject){
    $.form.submit({
        url: '../lms/lms9131m01/01',//'..' + rowObject.docURL + '/01',//'../lms/lms1605m01/01',
        data: {
            formAction: "queryL120m01a",
            oid: rowObject.oid,
            mainId: rowObject.mainId,
            mainOid: rowObject.oid,
            //            mainDocStatus: viewstatus,
            txCode: txCode
        },
        target: "_blank"
    });
}

function thickBoxOpen(){//負責處理打開ThickBox功能
    $.ajax({
        type: "POST",
        handler: "lms9131m01formhandler",
        data: {
            formAction: "querybranch"
        },
        success: function(responseData){
            var json = {
                format: "{key}({value})",
                item: responseData.itemBranch
            };
            
            $("#branchId").setItems(json);
            
        }
    });
    $("#openThickbox").thickbox({ // 使用選取的內容進行彈窗
        title: i18n.lms9131v00['LMS913V00.selecttitle'],
        width: 300,
        height: 200,
        align: 'center',
        valign: 'bottom',
        modal: false,
        i18n: i18n.def,
        buttons: {
            "sure": function(showMsg){
                // grid資料篩選
                filterGrid({
                    branchId: $("#branchId").val()
                });
                $.thickbox.close();
            },
            "close": function(){
                $.thickbox.close();
            }
        }
    });
}

function filterGrid(sendData){
    $("#gridfile").jqGrid("setGridParam", {
        postData: $.extend({
            formAction: "querylms9131v00sel",
            branchId: $("#branchId").val()
        }, sendData || {}),
        search: true
    }).trigger("reloadGrid");
}
