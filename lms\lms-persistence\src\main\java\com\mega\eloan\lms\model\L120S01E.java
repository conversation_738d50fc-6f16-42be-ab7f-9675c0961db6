/* 
 * L120S01E.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
 
package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 企金營收獲利財務狀況檔 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="L120S01E", uniqueConstraints = @UniqueConstraint(columnNames = {"mainId","custId","dupNo","finKind","finYear","finItem"}))
public class L120S01E extends GenericBean implements IDataObject, IDocObject  {

	private static final long serialVersionUID = 1L;

	/** 
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)", nullable=false)
	private String oid;

	/** 文件編號 **/
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainId;

	/** 身分證統編 **/
	@Column(name="CUSTID", length=10, columnDefinition="VARCHAR(10)")
	private String custId;

	/** 身分證統編重複碼 **/
	@Column(name="DUPNO", length=1, columnDefinition="CHAR(01)")
	private String dupNo;

	/** 
	 * 類別<p/>
	 * 1營運狀況<br/>
	 *  2財務狀況
	 */
	@Column(name="FINKIND", length=1, columnDefinition="CHAR(01)")
	private String finKind;

	/** 
	 * 年度<p/>
	 * YYYY-01-01<br/>
	 *  YYYY-MM-01
	 */
	@Temporal(TemporalType.DATE)
	@Column(name="FINYEAR", columnDefinition="DATE")
	private Date finYear;

	/** 
	 * 項目<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  詳註1
	 */
	@Column(name="FINITEM", length=9, columnDefinition="CHAR(09)")
	private String finItem;

	/** 
	 * 比率<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  DECIMAL(5,2)(DECIMAL(9,2)->DECIMAL(11,2)
	 */
	@Column(name="FINRATIO", columnDefinition="DECIMAL(11,2)")
	private BigDecimal finRatio;

	/** 
	 * 金額<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  DECIMAL(13,2)(DECIMAL(12,0)
	 */
	@Column(name="FINAMT", columnDefinition="DECIMAL(12,0)")
	private Long finAmt;

	/** 建立人員號碼 **/
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name="UPDATER", length=6, columnDefinition="CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name="UPDATETIME", columnDefinition="TIMESTAMP")
	private Date updateTime;
	
	/**比率代碼*/
	@Column(name="FINRATIOCODE", columnDefinition="VARCHAR(10)")
	private String finRatioCode;
	
	/**金額代碼*/
	@Column(name="FINAMTCODE", columnDefinition="VARCHAR(10)")
	private String finAmtCode;

	/** 
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}
	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得身分證統編 **/
	public String getCustId() {
		return this.custId;
	}
	/** 設定身分證統編 **/
	public void setCustId(String value) {
		this.custId = value;
	}

	/** 取得身分證統編重複碼 **/
	public String getDupNo() {
		return this.dupNo;
	}
	/** 設定身分證統編重複碼 **/
	public void setDupNo(String value) {
		this.dupNo = value;
	}

	/** 
	 * 取得類別<p/>
	 * 1營運狀況<br/>
	 *  2財務狀況
	 */
	public String getFinKind() {
		return this.finKind;
	}
	/**
	 *  設定類別<p/>
	 *  1營運狀況<br/>
	 *  2財務狀況
	 **/
	public void setFinKind(String value) {
		this.finKind = value;
	}

	/** 
	 * 取得年度<p/>
	 * YYYY-01-01<br/>
	 *  YYYY-MM-01
	 */
	public Date getFinYear() {
		return this.finYear;
	}
	/**
	 *  設定年度<p/>
	 *  YYYY-01-01<br/>
	 *  YYYY-MM-01
	 **/
	public void setFinYear(Date value) {
		this.finYear = value;
	}

	/** 
	 * 取得項目<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  詳註1
	 */
	public String getFinItem() {
		return this.finItem;
	}
	/**
	 *  設定項目<p/>
	 *  100/10/26配合徵信調整<br/>
	 *  詳註1
	 **/
	public void setFinItem(String value) {
		this.finItem = value;
	}

	/** 
	 * 取得比率<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  DECIMAL(5,2)(DECIMAL(9,2)
	 */
	public BigDecimal getFinRatio() {
		return this.finRatio;
	}
	/**
	 *  設定比率<p/>
	 *  100/10/26配合徵信調整<br/>
	 *  DECIMAL(5,2)(DECIMAL(9,2)
	 **/
	public void setFinRatio(BigDecimal value) {
		this.finRatio = value;
	}

	/** 
	 * 取得金額<p/>
	 * 100/10/26配合徵信調整<br/>
	 *  DECIMAL(13,2)(DECIMAL(12,0)
	 */
	public Long getFinAmt() {
		return this.finAmt;
	}
	/**
	 *  設定金額<p/>
	 *  100/10/26配合徵信調整<br/>
	 *  DECIMAL(13,2)(DECIMAL(12,0)
	 **/
	public void setFinAmt(Long value) {
		this.finAmt = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}
	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}
	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	/** 建構子 **/
	public L120S01E() {
	}

	/** 建構子 **/
	public L120S01E(String mainId, String custId, String dupNo, String finKind) {
		this.mainId = mainId;
		this.custId = custId;
		this.dupNo = dupNo;
		this.finKind = finKind;
	}
	/**
	 * 取得比率代碼
	 */
	public String getFinRatioCode() {
		return finRatioCode;
	}
	/**
	 * 設定比率代碼
	 */
	public void setFinRatioCode(String finRatioCode) {
		this.finRatioCode = finRatioCode;
	}
	/**
	 * 取得金額代碼
	 */
	public String getFinAmtCode() {
		return finAmtCode;
	}
	/**
	 * 設定金額代碼
	 */
	public void setFinAmtCode(String finAmtCode) {
		this.finAmtCode = finAmtCode;
	}
	
	
}
