/* 
 * L180R46ADaoImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.dao.utils.SearchMode;
import tw.com.iisi.cap.dao.utils.SearchModeParameter;

import com.mega.eloan.lms.dao.L180R46ADao;
import com.mega.eloan.lms.dao.LMSJpaDao;
import com.mega.eloan.lms.model.L180R46A;

/** 共同行銷未結案資訊檔 **/
@Repository
public class L180R46ADaoImpl extends LMSJpaDao<L180R46A, String>
	implements L180R46ADao {

	@Override
	public L180R46A findByOid(String oid) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "oid", oid);
		return findUniqueOrNone(search);
	}

	@Override
	public List<L180R46A> findByMainId(String mainId) {
		ISearch search = createSearchTemplete();
		search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		List<L180R46A> list = createQuery(search).getResultList();
		return list;
	}

	@Override
	public List<L180R46A> findByIndex01(String mainId){
		ISearch search = createSearchTemplete();
		List<L180R46A> list = null;
		if (mainId != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "mainId", mainId);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R46A> findByIndex02(String cntrNo){
		ISearch search = createSearchTemplete();
		List<L180R46A> list = null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}

	@Override
	public List<L180R46A> findByIndex03(String result, Integer cnt){
		ISearch search = createSearchTemplete();
		List<L180R46A> list = null;
		if (result != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "result", result);
		if (cnt != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cnt", cnt);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public List<L180R46A> findByIndex04(String type, String result, Integer cnt){
		ISearch search = createSearchTemplete();
		List<L180R46A> list = null;
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		if (result != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "result", result);
		if (cnt != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cnt", cnt);
		search.setMaxResults(Integer.MAX_VALUE);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			list = createQuery(search).getResultList();
		}
		return list;
	}
	
	@Override
	public L180R46A findByCntrNoType(String cntrNo, String type){
		ISearch search = createSearchTemplete();
		L180R46A l180r46a= null;
		if (cntrNo != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "cntrNo", cntrNo);
		if (type != null)
			search.addSearchModeParameters(SearchMode.EQUALS, "type", type);
		//檢查是否有查詢參數
		List<SearchModeParameter> searchList = search.getSearchModeParameters();
		if (searchList.size() != 0){
			l180r46a = findUniqueOrNone(search);
		}
		return l180r46a;
	}
}