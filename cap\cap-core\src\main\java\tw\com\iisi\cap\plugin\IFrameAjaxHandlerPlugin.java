
/**
* IFrameAjaxHandlerPlugin.java
*
* Copyright (c) 2009 International Integrated System, Inc.
* 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
* All Rights Reserved.
*
* Licensed Materials - Property of International Integrated System,Inc.
*
* This software is confidential and proprietary information of
* International Integrated System, Inc. ("Confidential Information").
*/
package tw.com.iisi.cap.plugin;

/**
 * <p>
 * for iframe.
 * </p>
 * 
 * <pre>
 * $Date: 2010-09-07 11:54:45 +0800 (星期二, 07 九月 2010) $
 * $Author: iris $
 * $Revision: 335 $
 * $HeadURL: svn://***********/MICB_ISDOC/cap/cap-core/src/main/java/tw/com/iisi/cap/plugin/IFrameAjaxHandlerPlugin.java $
 * </pre>
 *
 * <AUTHOR>
 * @version $Revision: 335 $
 * @version
 *          <ul>
 *          <li>2010/6/4,iristu,new
 *          </ul>
 */
public interface IFrameAjaxHandlerPlugin extends ICapPlugin {

}
