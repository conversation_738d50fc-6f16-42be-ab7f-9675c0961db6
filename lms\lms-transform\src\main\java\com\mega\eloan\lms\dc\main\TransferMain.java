package com.mega.eloan.lms.dc.main;

import java.util.Properties;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mega.eloan.lms.dc.base.DCException;
import com.mega.eloan.lms.dc.conf.BrnoConfig;
import com.mega.eloan.lms.dc.conf.CountyConfig;
import com.mega.eloan.lms.dc.conf.InsuranceConfig;
import com.mega.eloan.lms.dc.conf.MainConfig;
import com.mega.eloan.lms.dc.conf.TownConfig;
import com.mega.eloan.lms.dc.conf.ViewListConfig;
import com.mega.eloan.lms.dc.conf.XMLConfig;
import com.mega.eloan.lms.dc.thread.ChkColThread;
import com.mega.eloan.lms.dc.thread.CopyTextThread;
import com.mega.eloan.lms.dc.thread.ExportThread;
import com.mega.eloan.lms.dc.thread.GenDelSQLThread;
import com.mega.eloan.lms.dc.thread.ParserThread;
import com.mega.eloan.lms.dc.thread.RejectThread;
import com.mega.eloan.lms.dc.util.Util;

/**
 * <pre>
 * TransferMain : 主程式進入點
 * </pre>
 * 
 * @since 2012/12/20
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/12/20,Bang,new
 *          <li>2013/2/22,UFO,parser可拆解成個別bat執行
 *          <li>2013/2/25,UFO,調整程式參數順序，統一成attr,schema,dbtype,path
 *          <li>2013/3/5,Sandra增加產生delete sql ：genDel
 *          <li>2013/3/12,Bang,新增CountyConfig,TownConfig
 *          <li>2013/3/29,Bang,新增InsuranceConfig
 *          </ul>
 */
public class TransferMain {
	private static final String F_COPY_TEXT = "copyText";
	private static final String F_REJECT = "reject";
	private static final String F_PARSER = "parser";
	private static final String F_EXPORT = "export";
	private static final String F_CHKCOL = "chkCol";
	private static final String F_GENDEL = "genDel";

	private static Logger logger = LoggerFactory.getLogger(TransferMain.class);

	private String attr = "";// 執行動作名稱
	private String dbType = "";// 連結的DB
	private String schema = "";// 連結的 schema or 目前執行的系統名稱
	private String path = "";// 要檢核的文字檔路徑辨識碼Ex:db2 or 分行名
	private String viewListNo;

	private String toDay = "";

	static {
		MainConfig.getInstance();
		XMLConfig.getInstance();
		ViewListConfig.getInstance();
		BrnoConfig.getInstance();
		CountyConfig.getInstance();
		TownConfig.getInstance();
		InsuranceConfig.getInstance();
	}

	public void initParam(String[] args) {
		switch (args.length) {
		case 1:
			attr = args[0];
			break;
		case 2:
			attr = args[0];
			schema = args[1];
			break;
		case 3:
			attr = args[0];
			schema = args[1];
			dbType = args[2];
			break;
		case 4:
			attr = args[0];
			schema = args[1];
			dbType = args[2];
			path = args[3];
			break;
		case 5:
			attr = args[0];
			schema = args[1];
			dbType = args[2];
			path = args[3];
			viewListNo = args[4];
			break;
		default:
			break;
		}

		toDay = MainConfig.getInstance().getConfig().getTODAY();

		logger.info("########################################################");
		logger.info("程式參數 attr=" + attr);
		logger.info("程式參數 schema=" + schema);
		logger.info("程式參數 dbType=" + dbType);
		logger.info("程式參數 path=" + path);
		logger.info("程式參數 viewListNo=" + viewListNo);
		logger.info("程式參數 TODAY=" + toDay);
		logger.info("########################################################");

		// 檢核config.properties中TODAY是否符合格式[NMMDDS]
		if (!Util.checkToDayFormat(toDay)) {
			throw new DCException("TODAY=" + toDay + ", 不符合規則[格式為NMMDDS] 停止轉檔");
		}
	}

	public void doAction() throws Exception {
		if (StringUtils.isBlank(schema)) {
			logger.error("讀取系統名稱錯誤,未指定要執行的系統名稱,請重新確認...");
			return;
		}
		logger.info("轉檔初始化,讀取主要設定檔...");
		Properties props = MainConfig.getInstance().getProperties();
		// 設定要啟動的Thread數目
		int viewListSize = Integer.parseInt(props.getProperty("viewListSize")
				.trim());

		logger.info("啟動mutilThread , 初始化BaseAction,創建主要目錄及設定主要資訊...");

		// 執行DXLExport
		if (F_EXPORT.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始執行輸出.dxl檔...\n ");

			String vname = null;
			if (viewListNo != null) {
				// 指定ViewList模式
				vname = schema + "ViewList" + viewListNo + ".lst";
				new ExportThread(schema, vname).start();
			} else {
				// 全部執行模式
				ExportThread[] dtaArray = new ExportThread[viewListSize];
				for (int i = 0; i < viewListSize; i++) {
					vname = schema + "ViewList" + (i + 1) + ".lst";
					logger.info("\n 正在執行" + vname);
					dtaArray[i] = new ExportThread(schema, vname);
					dtaArray[i].start();
				}
			}
		}
		// 執行DXLParser
		else if (F_PARSER.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始執行 DXLparser...\n ");
			ParserThread[] dtaArray = new ParserThread[viewListSize];
			for (int i = 0; i < viewListSize; i++) {
				dtaArray[i] = new ParserThread(schema, schema + "ViewList"
						+ (i + 1) + ".lst");
				dtaArray[i].setName("Thread" + i);
				dtaArray[i].start();
			}
		}
		// 執行DXLReject
		else if (F_REJECT.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始將需要踢退的dxl檔案移到踢退目錄...\n ");
			RejectThread[] dtaArray = new RejectThread[viewListSize];
			for (int i = 0; i < viewListSize; i++) {
				logger.info("\n 正在執行" + schema + "ViewList" + (i + 1)
						+ ".lst...");
				dtaArray[i] = new RejectThread(schema);
				dtaArray[i].start();
			}
		}
		// 執行CopyAllTextFile
		else if (F_COPY_TEXT.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始匯集各分行的TEXT檔至load_db2目錄下...\n ");
			CopyTextThread ctt = new CopyTextThread(schema);
			ctt.start();
		}
		// 執行ColumnTruncate
		else if (F_CHKCOL.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始執行DB欄位與資料長度格式之檢核...\n ");
			ChkColThread cct = new ChkColThread(dbType, schema, path);
			cct.start();
		}
		// 建立delete sql以便回復轉檔
		else if (F_GENDEL.equalsIgnoreCase(attr)) {
			logger.info("\n\n 開始執行DB欄位與資料長度格式之檢核...\n ");
			GenDelSQLThread cct = new GenDelSQLThread(schema);
			cct.start();
		}
	}

	public static void main(String[] args) {
		try {
			// UPGRADE: Logger改換，config設定移除
			// PropertyConfigurator.configure("conf/log4j.properties");

			TransferMain tm = new TransferMain();
			if (args.length == 0) {
				throw new DCException("請輸入程式參數！");
			}

			tm.initParam(args);
			tm.doAction();
		} catch (Exception e) {
			logger.error("Error: ", e);
		}
	}

}
