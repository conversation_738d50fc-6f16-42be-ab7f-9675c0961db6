pageJsInit(function() {
	$(function() {
		var grid = $("#gridview").iGrid({
			handler: 'lms1015gridhandler',
			height: 350,
			action: 'queryBaseData',
			sortname: 'custId',
			rowNum: 17,
			rownumbers: true,

			colModel: [{ name: 'oid', hidden: true }
				, { name: 'mainId', hidden: true }
				, { colHeader: i18n.def["megaID"], width: 90, name: 'custId', formatter: 'click', onclick: openDoc }
				, { colHeader: ' ', width: 10, name: 'dupNo', sortable: true }
				, { colHeader: i18n.def["compName"], width: 140, name: 'custName', sortable: true }
				, {
				colHeader: i18n.lms1015v01["C101S01G.jcicQDate"], //聯徵查詢日期
				align: "center", width: 60, sortable: false,
				name: 'eJcicQDate' //原本 c120s01e的欄位，在 c120m01a 建一個 @Transient
			}, {
				colHeader: i18n.lms1015v01["C101S01G.etchQDate"], //票信查詢日期
				align: "center", width: 60, sortable: false,
				name: 'eChkQDate' //原本 c120s01e的欄位，在 c120m01a 建一個 @Transient
			}
				, { colHeader: i18n.def["lastUpdater"], width: 110, name: 'updater', sortable: true }
				, { colHeader: i18n.def["lastUpdateTime"], width: 100, name: 'updateTime', sortable: true }
				, { colHeader: ' ', width: 10, name: 'o_chkYN', sortable: true }
			]
		});

		$("#buttonPanel").find("#btnFilter").click(function() {
			openFilterBox();
		}).end().find("#btnAdd").click(function() {
			var formId = "addborrowForm";
			var $addborrow = $("#" + formId);
			$addborrow.reset();
			$addborrow.find("input[name=rborrowA]:checked").trigger('click');
			/*
			
			 在 thickbox 的 div 內，包含 addborrowForm
			
				 addborrowForm 內的  btn 引進，參考 $("#getCustData").click(function(){
			
			*/
			$("#thickboxaddborrow").thickbox({
				title: '', width: 800, height: 380, modal: false, i18n: i18n.def,
				buttons: {
					"close": function() {
						API.confirmMessage(i18n.def['flow.exit'], function(res) {
							if (res) {
								$.thickbox.close();
							}
						});
					}
				}
			});

		}).end().find("#btnDelete").click(function() {
			var rows = $("#gridview").getGridParam('selrow');
			var mainOid = "";
			if (rows != 'undefined' && rows != null && rows != 0) {
				var data = $("#gridview").getRowData(rows);
				mainOid = data.oid;
			}
			if (mainOid == "") {
				CommonAPI.showMessage(i18n.def["TMMDeleteError"]);
				return;
			}
			CommonAPI.confirmMessage(i18n.def["confirmDelete"], function(b) {
				if (b) {
					del_base_data(mainOid);
				}
			});
		});

		function openDoc(cellvalue, options, rowObject) {
			call_z_grid_openDoc(rowObject.oid, false);
		}
	});
});

function del_base_data(c120m01a_oid){
	$.ajax({							
		type : "POST", handler : 'lms1015m01formhandler',
		data : { formAction : "delBaseData", 
			'c120m01a_oid': c120m01a_oid
		},
		}).done(function(responseData){
			$("#gridview").trigger('reloadGrid');
			$.thickbox.close();		
	});
}
function call_z_grid_openDoc(c120m01a_oid, doOpenDoc){
	
	queryC120BusCd(c120m01a_oid).done(function(json_queryC120BusCd){
		if(json_queryC120BusCd.busCode){
			var loadUrl = "";
			if(json_queryC120BusCd.busCode=="060000" || json_queryC120BusCd.busCode=="130300"){
				loadUrl = "../lms/lms1015v00c";
			}else{
				loadUrl = "../lms/lms1015v00b";
			}
						 
			$("#divOverSeaCLSPage_content").load(loadUrl,function(){
				setTimeout(function () {				
				$.ajax({type : "POST", handler : 'lms1015m01formhandler',
					data : $.extend(
						{
							'formAction':'overSeaCLS_query'
							,'c120m01a_oid' : c120m01a_oid
							,'formAttrTxFlag': "lms1015v00cb"
						}
						, {}
					),
					}).done(function(json_overSeaCLS_query){
						var $form = overSeaCLSPage_getForm();
						$form.buildItem();
						overSeaCLSPage_buildItem();
						if($("#oIncome").length>0){
							var item = API.loadOrderCombosAsList("cls1131m01_othType")["cls1131m01_othType"];
					        $("#oIncome").setItems({ size: "1", item: convertItems(item), clear: true, itemType: 'checkbox' })
						}
						$form.injectData(json_overSeaCLS_query);							
						if(true){
							overSeaCLSPage_initEvent(json_overSeaCLS_query);
							overSeaCLSPage_2ndInjectData_afterInitEvent(json_overSeaCLS_query);							 
						}
											
						//~~~~~~~~~~~~
						var tb_btnArr = {};
						overSeaCLSPage_saveAjaxParam(
								{'mainId': json_overSeaCLS_query.c120m01a_mainId
									,'c120m01a_oid' : c120m01a_oid
									//在 save 時，為免寫入  BDocOpener，造成lock其它 user
									,'noOpenDoc':true
								}
						);
						
						//在 View 去開啟 page
						thickboxOptions.lockDoc = false;
						thickboxOptions.readOnly = false;					
						
						if( true ){
							tb_btnArr["saveData"] = function(){
								overSeaCLSPage_runSaveAjax().done(function(json_overSeaCLS_save){
									//c120m01agrid.trigger("reloadGrid");
									if(json_overSeaCLS_save.l120m01a_custId){//更改主借人
										//...
									}
									$("#gridview").trigger("reloadGrid");
									
									if(json_overSeaCLS_save.errMsg){
										CommonAPI.showErrorMessage(json_overSeaCLS_save.errMsg);	
									}else{
										CommonAPI.showMessage(i18n.def.saveSuccess);
									}
						        });					
							};
							
							tb_btnArr["del"] = function(){
								API.confirmMessage(i18n.def['confirmDelete'], function(res){
									if(res){	
										del_base_data(c120m01a_oid);
									}
							     });
							};
						}
						
						tb_btnArr["close"] = function(){
							 API.confirmMessage(i18n.def['flow.exit'], function(res){
								if(res){
									$.thickbox.close();
								}
						     });
						};
						//~~~~~~~~~~~~
						var tb_height = 580;
						var maxHeight = window.screen.height - 200;
						if(tb_height>maxHeight){
							tb_height = maxHeight;
						}
						$("#divOverSeaCLSPage").thickbox({		
							title : (json_overSeaCLS_query.custId||'')
									+'-'+(json_overSeaCLS_query.dupNo||'')
									+' '+(json_overSeaCLS_query.custName||'')
							, width : 950, height : tb_height, modal : true, i18n:i18n.def,						
							buttons : tb_btnArr
						});
						
						$form.closest('div.tabs')[0].scrollIntoView();

					    if (doOpenDoc) {
					    	overSeaCLSPage_reg_imp_custData();
					    }
				});
				}, 500);
			});
		}
	});
}

function queryC120BusCd(c120m01a_oid){
	return $.ajax({							
		type : "POST", 
		handler : 'lms1015m01formhandler',
		data : { formAction : "queryC120BusCd", 'c120m01a_oid':c120m01a_oid
		},
		}).done(function(json_queryC120BusCd){			
		
	});
}

$("#getCustData").click(function(){
	var formId = "addborrowForm";
	var $addborrow = $("#"+formId);
	
	var $custId = $addborrow.find("[name=addborrowForm_custId]").val();
	var $custName = $addborrow.find("[name=addborrowForm_custName]").val();
	if(($custId != null && $custId != undefined && $custId != '')
	&& ($custName != null && $custName != undefined && $custName != '')){
		// 統一編號、名稱擇一輸入引進即可
		CommonAPI.showErrorMessage(i18n.lms1015m01["l120s02.alert26"]);
	}else if(($custId == null || $custId == undefined || $custId == '')
	&& ($custName == null || $custName == undefined || $custName == '')){
		// 請輸入統一編號或名稱
		CommonAPI.showErrorMessage(i18n.lms1015m01["l120s02.alert27"]);
	}else{
	    var defaultOption = {};
		if($custId != null && $custId != undefined && $custId != ''){
			defaultOption = {
				defaultValue: $custId //預設值 
			};
		}else{
			defaultOption = {
				defaultName : $custName
			};				
		}			
		//綁入MegaID
		CommonAPI.openQueryBox(
			$.extend({
				defaultCustType : ($custId != null && $custId != undefined && $custId != '') ? "1" : ($custName != null && $custName != undefined && $custName != '') ? "2" : "",
                divId: formId, //在哪個div 底下
                isInSide:false, 
                autoResponse: { // 是否自動回填資訊 
                       id: "addborrowForm_custId", // 統一編號欄位ID 
                       dupno: "addborrowForm_dupNo", // 重覆編號欄位ID 
                       name: "addborrowForm_custName" // 客戶名稱欄位ID 
                },fn:function(obj){	
                	/*
					obj.custid "A123456789"
					obj.dupno "0"
					obj.name "TESTT"
					obj.buscd 	"060000"
                	*/
					if( $addborrow.valid()){
						$.ajax({							
							type : "POST", handler : 'lms1015m01formhandler',
							data : { formAction : "addBaseData", 
								custId: obj.custid, dupNo: obj.dupno, custName: obj.name, busCode: obj.buscd
							},
							}).done(function(responseData){
								call_z_grid_openDoc(responseData.c120m01a_oid, true);
								$("#gridview").trigger('reloadGrid');
								$.thickbox.close();		
						}).fail(function(){ $addborrow.reset(); });						
					}
				}
			},defaultOption)
		);			
	}
});	

function openFilterBox(){
	var _id = "_div_filter";
	var _form = _id+"_form";
	if ($("#"+_id).length == 0){
		var dyna = [];
		dyna.push("<div id='"+_id+"' style='display:none;' >");
		dyna.push("<form id='"+_form+"'>");
		dyna.push("<table class='tb2'>");
		dyna.push("<tr><td class='hd1' nowrap>"+i18n.def["megaID"]+"</td><td><input type='text' name='search_custId' value='' maxlength='10'></td></tr>");		
		dyna.push("</table>");
		dyna.push("</form>");
		
		dyna.push("</div>");
		
	     $('body').append(dyna.join(""));
	}
	//clear data
	$("#"+_form).reset();
	
	$("#"+_id).thickbox({ title: i18n.def['query'], width: 400, height: 185, modal: true,
        valign: "bottom", align: "center", i18n: i18n.def,
        buttons: {
            "sure": function(){            	
            	if (true){	
					$.thickbox.close();
					
					$("#gridview").jqGrid("setGridParam", {
						postData : $("#"+_form).serializeData(),
						page : 1,
						search : true
					}).trigger("reloadGrid");
				}
            },
            "cancel": function(){
            	 $.thickbox.close();
            }
        }
    });
}