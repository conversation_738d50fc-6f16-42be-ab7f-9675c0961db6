package com.mega.eloan.lms.dao;

import java.util.List;

import tw.com.iisi.cap.dao.IGenericDao;

import com.mega.eloan.lms.model.C120S01U;

/** 個金EJ標準查詢檔 **/
public interface C120S01UDao extends IGenericDao<C120S01U> {

	public C120S01U findByOid(String oid);
	
	public List<C120S01U> findByMainId(String mainId);
	public List<C120S01U> findByMainIdCustIdDupNo(String mainId, String custId, String dupNo);
	public List<C120S01U> findByMainIdCustIdDupNoTxid(String mainId, String custId, String dupNo, String txid);
	
	public int deleteByOid(String oid);
}