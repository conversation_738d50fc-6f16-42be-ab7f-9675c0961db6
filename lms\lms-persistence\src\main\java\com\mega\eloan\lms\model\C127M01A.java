/*
 * C126M01A.java
 *
 * Copyright (c) 2011-2012 JC Software Services, Inc.
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 *
 * Licensed Materials - Property of JC Software Services, Inc.
 *
 * This software is confidential and proprietary information of
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.*;

import tw.com.iisi.cap.model.IDataObject;
import tw.com.iisi.cap.model.GenericBean;
import com.mega.eloan.lms.validation.group.Check;
import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.Meta;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 分組授權金額控管表 **/
@Entity
@EntityListeners({DocumentModifyListener.class})
@Table(name="C127M01A", uniqueConstraints = @UniqueConstraint(columnNames = {"oid"}))
public class C127M01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid<p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name="OID", length=32, columnDefinition="CHAR(32)")
	private String oid;

	@Size(max=32)
	@Column(name="MAINID", length=32, columnDefinition="CHAR(32)")
	private String mainid;

	/** 區域 **/
	@Size(max=30)
	@Column(name="AREA", length=30, columnDefinition="VARCHAR(30)")
	private String Area;

	/** 分行別 **/
	@Size(max=3)
	@Column(name="OWNBRID", length=3, columnDefinition="CHAR(3)")
	private String ownBrId;

	/** 分行名稱 **/
	@Size(max=30)
	@Column(name="OWNBRNAME", length=30, columnDefinition="VARCHAR(30)")
	private String ownBrName;

	/** 組別 **/
	@Size(max=1)
	@Column(name="GROUPID", length=1, columnDefinition="CHAR(1)")
	private String groupId;

	/** 北區承辦金額 **/
	@Digits(integer=6, fraction=2, groups = Check.class)
	@Column(name="NORTHPRICE", columnDefinition="DECIMAL(6,2)")
	private BigDecimal northPrice;

	/** 中區承辦金額 **/
	@Digits(integer=6, fraction=2, groups = Check.class)
	@Column(name="MIDDLEPRICE", columnDefinition="DECIMAL(6,2)")
	private BigDecimal middlePrice;

	/** 南區承辦金額 **/
	@Digits(integer=6, fraction=2, groups = Check.class)
	@Column(name="SOUTHPRICE", columnDefinition="DECIMAL(6,2)")
	private BigDecimal southPrice;

	/** 建立人員號碼 **/
	@Size(max=6)
	@Column(name="CREATOR", length=6, columnDefinition="CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name="CREATETIME", columnDefinition="TIMESTAMP")
	private Timestamp createTime;

	/** 備註 **/
	@Size(max=500)
	@Column(name="MEMO", length=500, columnDefinition="VARCHAR(500)")
	private String memo;

	/**
	 * 取得oid<p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}
	/**
	 *  設定oid<p/>
	 *  ROWID
	 **/
	public void setOid(String value) {
		this.mainid = value;
	}

	public String getMainId() {
		return this.mainid;
	}
	public void setMainId(String value) {
		this.mainid = value;
	}

	/** 取得區域 **/
	public String getArea() {
		return this.Area;
	}
	/** 設定區域 **/
	public void setArea(String value) {
		this.Area = value;
	}

	/** 取得分行別 **/
	public String getOwnBrId() {
		return this.ownBrId;
	}
	/** 設定分行別 **/
	public void setOwnBrId(String value) {
		this.ownBrId = value;
	}

	/** 取得分行名稱 **/
	public String getOwnBrName() {
		return this.ownBrName;
	}
	/** 設定分行名稱 **/
	public void setOwnBrName(String value) {
		this.ownBrName = value;
	}

	/** 取得組別 **/
	public String getGroupId() {
		return this.groupId;
	}
	/** 設定組別 **/
	public void setGroupId(String value) {
		this.groupId = value;
	}

	/** 取得北區承辦金額 **/
	public BigDecimal getNorthPrice() {
		return this.northPrice;
	}
	/** 設定北區承辦金額 **/
	public void setNorthPrice(BigDecimal value) {
		this.northPrice = value;
	}

	/** 取得中區承辦金額 **/
	public BigDecimal getMiddlePrice() {
		return this.middlePrice;
	}
	/** 設定中區承辦金額 **/
	public void setMiddlePrice(BigDecimal value) {
		this.middlePrice = value;
	}

	/** 取得南區承辦金額 **/
	public BigDecimal getSouthPrice() {
		return this.southPrice;
	}
	/** 設定南區承辦金額 **/
	public void setSouthPrice(BigDecimal value) {
		this.southPrice = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}
	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Timestamp getCreateTime() {
		return this.createTime;
	}
	/** 設定建立日期 **/
	public void setCreateTime(Timestamp value) {
		this.createTime = value;
	}

	/** 取得備註 **/
	public String getMemo() {
		return this.memo;
	}
	/** 設定備註 **/
	public void setMemo(String value) {
		this.memo = value;
	}

}
