/* 
 *  LMS1505FormHandler.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.handler.form;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;
import org.kordamp.json.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.annotation.DomainAuth;
import com.mega.eloan.common.annotation.DomainClass;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.exception.GWException;
import com.mega.eloan.common.handler.form.AbstractFormHandler;
import com.mega.eloan.common.response.RespMsgHelper;
import com.mega.eloan.common.service.BstblService;
import com.mega.eloan.common.service.UserInfoService;
import com.mega.eloan.common.service.UserInfoService.SignEnum;
import com.mega.eloan.common.utils.DataParse;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.flow.enums.CreditDocStatusEnum;
import com.mega.eloan.lms.base.service.LMSService;
import com.mega.eloan.lms.lms.pages.LMS1505M01Page;
import com.mega.eloan.lms.lms.service.LMS1505Service;
import com.mega.eloan.lms.mfaloan.service.MisStoredProcService;
import com.mega.eloan.lms.mfaloan.service.MisdbBASEService;
import com.mega.eloan.lms.model.L120M01A;
import com.mega.eloan.lms.model.L150M01A;
import com.mega.sso.context.MegaSSOSecurityContext;
import com.mega.sso.service.BranchService;
import com.mega.sso.userdetails.MegaSSOUserDetails;

import tw.com.iisi.cap.constant.CapConstants;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapMessageException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.iisi.cap.operation.simple.SimpleContextHolder;
import tw.com.iisi.cap.response.CapAjaxFormResult;
import tw.com.iisi.cap.response.IResult;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.auth.AuthType;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 小放會會議記錄
 * </pre>
 * 
 * @since 2011/9/6
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/9/6,REX,new
 *          <li>2013/7/4,REX,修改小放會議新增上deleteTime
 *          </ul>
 */
@Scope("request")
@Controller("lms1505m01formhandler")
@DomainClass(L150M01A.class)
public class LMS1505M01FormHandler extends AbstractFormHandler {

	@Resource
	LMS1505Service lms1505Service;

	@Resource
	BstblService bstblService;

	@Resource
	UserInfoService userInfoService;

	@Resource
	MisdbBASEService misdbBASEService;

	@Resource
	BranchService branchService;
	@Resource
	MisStoredProcService misStoredProcService;
	@Resource
	LMSService lmsService;

	/**
	 * 查詢
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult queryL150m01a(PageParameters params)
			throws CapException {
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		CapAjaxFormResult result = new CapAjaxFormResult();
		String oid = params.getString(EloanConstants.MAIN_OID);
		L150M01A l150m01a = null;
		if (!CapString.isEmpty(oid)) {
			l150m01a = lms1505Service.findL150m01aByOid(oid);
			if (l150m01a != null) {
				result = DataParse.toResult(l150m01a);
				// 重設present ,因為上一句會trim資料
				result.set("present", l150m01a.getPresent());
				result.set("resolution", l150m01a.getResolution());
				result.set(EloanConstants.MAIN_OID, Util.trim(oid));
				result.set("ownBrId", l150m01a.getOwnBrId() + " "
						+ branchService.getBranchName(l150m01a.getOwnBrId()));
				if (Util.isEmpty(l150m01a.getMeetingTime())) {
					Date now = new Date();
					result.set("meetingDate", new SimpleDateFormat(
							UtilConstants.DateFormat.YYYY_MM_DD).format(now));
					result.set("meetingTime",
							new SimpleDateFormat("HH:mm").format(now));
				}
				result.set("updater", this.getUserName(l150m01a.getUpdater()));
				result.set("creator", this.getUserName(l150m01a.getCreator()));
			}
		} else {
			Date now = new Date();
			result.set("meetingDate",
					CapDate.getCurrentDate(UtilConstants.DateFormat.YYYY_MM_DD));
			result.set("meetingTime", new SimpleDateFormat("HH:mm").format(now));
		}
		SignEnum[] signs = { SignEnum.甲級主管, SignEnum.乙級主管, SignEnum.經辦人員,
				SignEnum.單位主管 };
		Map<String, String> allPeopleEls = userInfoService.findByBrnoAndSignIdNotLeave(
				user.getUnitNo(), signs);
		// Map<String, String> allPeople = new TreeMap<String, String>();
		// Map<String, String> mainPeople = new TreeMap<String, String>();

		// 紀錄、帳戶管理員
		result.set("allPeopleMap", new CapAjaxFormResult(allPeopleEls));

		// 主席、遵守法令主管
		result.set("mainPeopleMap", new CapAjaxFormResult(allPeopleEls));

		return result;
	}

	/**
	 * 儲存
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult saveL150m01a(PageParameters params)
			throws CapException {
		L150M01A l150m01a = saveBase(params);

		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1505M01Page.class);
		String validate = Util.validateColumnSize(l150m01a, pop, "L150M01a");
		Boolean showMsg = params.getAsBoolean("showMsg", false);
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		CapAjaxFormResult result = new CapAjaxFormResult();
		try {
			SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
					params.getString("tempSave", "N"));

			// 只有showMsg =true
			if (showMsg) {
				result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
						.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
			}
			l150m01a.setRandomCode(IDGenerator.getRandomCode());
			//2013/7/4,REX,修改小放會議新增上deleteTime
			l150m01a.setDeletedTime(null);
			lms1505Service.saveL150m01a(l150m01a);
		} catch (GWException g) {
			logger.error(g.getMessage());
			HashMap<String, String> param = new HashMap<String, String>();
			param.put("dsName", "LMS.L150M01A");
			// EFD0010=ERROR|系統連接資料庫$\{dsName\}不成功，請洽資訊處|
			throw new CapMessageException(RespMsgHelper.getMessage("EFD0010", param), getClass());
		}

		result = DataParse.toResult(l150m01a);
		result.set(
				"ownBrId",
				l150m01a.getOwnBrId() + " "
						+ branchService.getBranchName(l150m01a.getOwnBrId()));
		if (showMsg) {
			result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
					.getMainMessage(UtilConstants.AJAX_RSP_MSG.儲存成功));
		}
		result.set(EloanConstants.OID, l150m01a.getOid());
		result.set(EloanConstants.MAIN_OID, l150m01a.getOid());
		result.set("updater", this.getUserName(l150m01a.getUpdater()));
		result.set("creator", this.getUserName(l150m01a.getCreator()));
		return result;
	}// ;

	/**
	 * 刪除(多筆)
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return CapAjaxFormResult
	 * @throws CapException
	 */
	@DomainAuth(AuthType.Modify)
	public IResult deleteList(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		String[] oids = params.getStringArray("oids");
		lms1505Service.deleteL150m01aList(oids);

		result.set(CapConstants.AJAX_NOTIFY_MESSAGE, RespMsgHelper
				.getMainMessage(UtilConstants.AJAX_RSP_MSG.刪除成功));

		return result;
	}

	/**
	 * tempSave
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Modify, CheckDocStatus = false)
	public IResult tempSave(PageParameters params)
			throws CapException {
		CapAjaxFormResult result = new CapAjaxFormResult();
		L150M01A mainModel = saveBase(params);
		Properties pop = MessageBundleScriptCreator
				.getComponentResource(LMS1505M01Page.class);
		String validate = Util.validateColumnSize(mainModel, pop, "L150M01a");
		if (validate != null) {
			Map<String, String> param = new HashMap<String, String>();
			param.put("colName", validate);

			throw new CapMessageException(RespMsgHelper.getMessage("EFD0007", param), getClass());
		}
		SimpleContextHolder.put(EloanConstants.TEMPSAVE_RUN,
				params.getString("tempSave", "Y"));
		lms1505Service.saveL150m01a(mainModel);
		return result;
	}

	/**
	 * 儲存要處理的
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return
	 * @throws CapException
	 */
	private L150M01A saveBase(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.MAIN_OID);
		String createType = params.getString("createType");
		MegaSSOUserDetails user = MegaSSOSecurityContext.getUserDetails();
		String formL150m01a = params.getString("L150M01AForm"); // 指定的form
		JSONObject jsonL150m01a = JSONObject.fromObject(formL150m01a);
		L150M01A l150m01a = lms1505Service.findL150m01aByOid(oid);
		
		if (l150m01a == null) {
			l150m01a = new L150M01A();
			l150m01a.setCustId("");
			l150m01a.setCustName("");
			l150m01a.setDupNo("");
			l150m01a.setCreateTime(CapDate.getCurrentTimestamp());
			l150m01a.setCreator(user.getUserId());
			l150m01a.setDocStatus(CreditDocStatusEnum.海外_編製中.getCode());
			l150m01a.setOwnBrId(user.getUnitNo());
			// UPGRADE: 待確認，URL是否正確
			l150m01a.setDocURL(params.getString("docUrl"));
			if (Util.isEmpty(createType)) {
				createType = UtilConstants.Meeting.CreateType.海外;
			}
			/**
			 * 建檔來源
			 * <p/>
			 * 100/08/24新增<br/>
			 * 1.海外<br/>
			 * 2.企金<br/>
			 * 3.消金
			 */

			l150m01a.setCreateType(createType);
		}

		DataParse.toBean(jsonL150m01a, l150m01a);// 將資料內容與資料庫maping

		return l150m01a;
	}

	/**
	 * 複製小放會
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult copyCase(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.OID);
		L150M01A l150m01a = lms1505Service.findL150m01aByOid(oid);
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (l150m01a != null) {
			L150M01A newl150m01a = new L150M01A();
			DataParse.copy(l150m01a, newl150m01a);
			newl150m01a.setMeetingDate(null);
			newl150m01a.setMeetingTime(null);
			newl150m01a.setMainId(IDGenerator.getUUID());
			//2013/7/4,REX,修改小放會議新增上deleteTime
			newl150m01a.setDeletedTime(CapDate.getCurrentTimestamp());
			lms1505Service.saveL150m01a(newl150m01a);
			result = DataParse.toResult(newl150m01a);
		}

		return result;
	}

	/**
	 * 取得案由
	 * 
	 * @param params
	 *            PageParameters
	 * @param parent
	 *            Component
	 * @return IResult
	 * @throws CapException
	 */
	@DomainAuth(value = AuthType.Query, CheckDocStatus = false)
	public IResult getCase(PageParameters params)
			throws CapException {
		String oid = params.getString(EloanConstants.OID);
		L120M01A l120m01a = lms1505Service.findL120M01AByOid(oid);
		CapAjaxFormResult result = new CapAjaxFormResult();
		if (l120m01a != null) {
			result.set("gist", l120m01a.getGist());
		} else {
			result.set("gist", "");
		}

		return result;
	}

	/**
	 * 取得使用者姓名
	 * 
	 * @param userId
	 *            員編
	 * @return 姓名
	 */
	private String getUserName(String userId) {

		if (userId == null) {
			return "";
		}
		String result = userInfoService.getUserName(userId);
		if (Util.isEmpty(result)) {
			return userId;
		} else {
			return result;
		}
	}

}
