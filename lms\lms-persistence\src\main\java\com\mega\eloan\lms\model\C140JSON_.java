package com.mega.eloan.lms.model;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

import com.mega.eloan.common.model.RelativeMeta_;

/**
 * <pre>
 * The persistent class for the C140JSON database table.
 * </pre>
 * @since  2011/9/20
 * <AUTHOR>
 * @version <ul>
 *           <li>2011/9/20,<PERSON>,new
 *          </ul>
 */
@StaticMetamodel(C140JSON.class)
public class C140JSON_  extends RelativeMeta_{
	public static volatile SingularAttribute<C140JSON, String> jsonOb;
	public static volatile SingularAttribute<C140JSON, String> flag;
	public static volatile SingularAttribute<C140JSON, String> subtab;
	public static volatile SingularAttribute<C140JSON, String> tab;
	public static volatile SingularAttribute<C140JSON, C140M01A> c140m01a;
	public static volatile SingularAttribute<C140JSON, C140M04A> c140m04a;
	public static volatile SingularAttribute<C140JSON, C140M07A> c140m07a;
	public static volatile SingularAttribute<C140JSON, C140M04B> c140m04b;
}
