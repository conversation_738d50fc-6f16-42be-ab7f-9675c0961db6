/*
 * GridHandler.java
 *
 * Copyright (c) 2009-2011 International Integrated System, Inc.
 * 11F, No.133, Sec.4, Minsheng E. Rd., Taipei, 10574, Taiwan, R.O.C.
 * All Rights Reserved.
 *
 * Licensed Materials - Property of International Integrated System,Inc.
 *
 * This software is confidential and proprietary information of
 * International Integrated System, Inc. ("Confidential Information").
 */
package tw.com.iisi.cap.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.kordamp.json.JSONArray;

import com.iisigroup.cap.component.PageParameters;

import tw.com.iisi.cap.dao.utils.AbstractSearchSetting;
import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.enums.IGridEnum;
import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.plugin.AjaxHandlerPlugin;
import tw.com.iisi.cap.response.CapGridResult;
import tw.com.iisi.cap.response.IGridResult;
import tw.com.iisi.cap.response.IResult;

/**
 * <pre>
 * GridHandler
 * Grid資料處理
 * </pre>
 * 
 * @since 2010/7/16
 * <AUTHOR>
 * @version
 *          <ul>
 *          <li>2010/7/16,iristu,new
 *          <li>2010/9/15,Sunkist,修改execute() method中組成SearchSetting 時的 sortBy，含asc,desc之間的變換。
 *          <li>2010/9/15,Lancelot,抽出method JSONArray getRows(List<?> it, String[] columns) throws CapException， 新增 method Map<String,IFormatter> getDataReformatter()，讓繼承的 handler 可設定根據 data bean
 *          欄位不同而取得不同的 formatter。
 *          <li>2010/11/25,iristu,重新調整結構.
 *          <li>2011/5/3,RodesChen,add records
 *          </ul>
 *          the model extends GenericBean
 */
public abstract class GridHandler extends AjaxHandlerPlugin {

    /**
     * Gets the row count.
     * 
     * @param search
     *            ISearch
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     * @return int
     * @throws CapException
     */
    public abstract int getRowCount(ISearch search, PageParameters params) throws CapException;

    /**
     * 設定查詢條件及排序條件
     * 
     * @param search
     *            CapJqGridResult
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     * @return Iterator<? extends GenericBean>
     */
    public abstract List<? extends GenericBean> getRowData(ISearch search, PageParameters params) throws CapException;

    /**
     * before execute
     * 
     * @param params
     *            RequestParameters
     * @param parent
     *            Component
     */
    public void beforeExcute(PageParameters params) throws CapException {
        // customize
    }

    /**
     * Gets the equal map.
     * 
     * @param params
     *            RequestParameters
     * 
     * @return the equal map
     */
    public ISearch getSearchRequirement(PageParameters params) throws CapException {
        ISearch search = createSearchTemplete();
        return search;
    }

    /**
     * 以傳入的資料設定Grid
     * 
     * @return {@code IGridResult result}
     * @see {@link AjaxHandlerPlugin#execute(PageParameters) execute}
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public IResult execute(PageParameters params) throws CapException {
        IGridResult result = new CapGridResult();
        ISearch search = getSearchRequirement(params);
        if (search == null) {
            search = createSearchTemplete();
        }
        setParameters(params);
        beforeExcute(params);
        boolean pages = params.containsKey(IGridEnum.PAGE.getCode());
        if (pages) {
            int page = 0, pageRows = 0, rowCount = 0, startRow = 0;
            page = params.getAsInteger(IGridEnum.PAGE.getCode());
            pageRows = params.getAsInteger(IGridEnum.PAGEROWS.getCode());
            rowCount = getRowCount(search, params);
            startRow = (page - 1) * pageRows;
            search.setFirstResult(startRow).setMaxResults(pageRows);
            result.setPage(page);
            result.setRecords(rowCount);
            result.setPageCount(rowCount, pageRows);
        }
        boolean sort = params.containsKey(IGridEnum.SORTCOLUMN.getCode());
        if (sort) {
            String sortBy = params.getString(IGridEnum.SORTCOLUMN.getCode());
            boolean isAsc = IGridEnum.SORTASC.getCode().equals(params.getString(IGridEnum.SORTTYPE.getCode()));
            search.addOrderBy(sortBy, !isAsc);
        }

        List<? extends GenericBean> it = getRowData(search, params);

        String[] columns = getColumns(params.getString(IGridEnum.COL_PARAM.getCode()));

        result.setRowData(convertToJson(it, columns));
        return result;
    }

    /**
     * 取得iGrid中的Column Name
     * 
     * @param params
     *            String
     * @return String string[]
     */
    @SuppressWarnings("unchecked")
    public String[] getColumns(String params) throws CapException {
        JSONArray arr = JSONArray.fromObject(params);
        String[] colNames = new String[arr.size()];
        for (int i = 0; i < arr.size(); i++) {
            Map<String, String> m = (Map<String, String>) arr.get(i);
            colNames[i] = m.containsKey(IGridEnum.COL_INDEX.getCode()) ? m.get(IGridEnum.COL_NAME.getCode()) + "|" + m.get(IGridEnum.COL_INDEX.getCode()) : m.get(IGridEnum.COL_NAME.getCode());
        }
        return colNames;
    };

    /**
     * 取得資料筆數
     * 
     * @param it
     *            List<?>
     * @param columns
     *            欄位
     * @return List<Map<String, Object>>
     * @throws CapException
     */
    protected List<Object> convertToJson(List<? extends GenericBean> it, String[] columns) throws CapException {
        List<Object> rows = new JSONArray();
        Map<String, Object> row = new HashMap<String, Object>();
        if (it != null && !it.isEmpty()) {
            for (GenericBean data : it) {
                row.put(IGridEnum.CELL.getCode(), data.toJSONString(columns, dataReformatter));
                rows.add(row);
            }
        }
        return rows;
    }// ;

    private Map<String, IFormatter> dataReformatter;

    /**
     * get Data Reformatter
     * 
     * @return
     */
    protected Map<String, IFormatter> getDataReformatter() {
        return dataReformatter;
    }

    /**
     * Inits the reformat callback.
     * 
     * @param data
     *            the data
     */
    protected GridHandler addReformatData(String key, IFormatter formatter) throws CapException {
        if (dataReformatter == null) {
            dataReformatter = new HashMap<String, IFormatter>();
        }
        dataReformatter.put(key, formatter);
        return this;
    }

    /**
     * afterPropertiesSet
     */
    public void afterPropertiesSet() throws Exception {
        // do nothing
    }

    /**
     * <pre>
     * version
     * </pre>
     * 
     * @return {@code "1.0.0"}
     */
    @Override
    public String version() {
        return "1.0.0";
    }

    /**
     * 建立查詢模板
     * 
     * @return {@code new GridSearch()}
     */
    public ISearch createSearchTemplete() {
        return new GridSearch();
    }

    /**
     * <pre>
     * GridSearch
     * </pre>
     */
    private class GridSearch extends AbstractSearchSetting {

        private static final long serialVersionUID = 1L;

    }

}
