/* 
 * C122S01FDao.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.mega.eloan.lms.model.C122S01F;

import tw.com.iisi.cap.dao.IGenericDao;

/** 進件狀態明細檔 **/
public interface C122S01FDao extends IGenericDao<C122S01F> {

	C122S01F findByOid(String oid);

	List<C122S01F> findByMainId(String mainId);

	C122S01F findByUniqueKey(String mainId);

	List<C122S01F> findByIndex01(String mainId,String docstatus);
}