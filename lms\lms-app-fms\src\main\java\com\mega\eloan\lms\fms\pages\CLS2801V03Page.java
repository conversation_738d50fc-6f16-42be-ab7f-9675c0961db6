package com.mega.eloan.lms.fms.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.flow.enums.FlowDocStatusEnum;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;


@Controller
@RequestMapping(path = "/fms/cls2801v03")
public class CLS2801V03Page extends AbstractEloanInnerView {

	public CLS2801V03Page() {
		super();
	}

	@Override
	public void  execute(ModelMap model, PageParameters params) {
		setGridViewStatus(FlowDocStatusEnum.已核准);
		setJavaScriptVar("noOpenDoc", "N");
		//---
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(CLS2801V01Page.class);
	}

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/fms/CLS2801V01Page.js" };
	}
	
	/*@Override
	protected void addPanel(String panelId) {
		add(new CLS2601FilterPanel(panelId));
	}*/
}
