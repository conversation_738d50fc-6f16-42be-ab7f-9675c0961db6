<html xmlns="http://www.w3.org/1999/xhtml" 
        xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="panelFragmentBody">
		<fieldset>
			<legend><b><th:block th:text="#{'l120s02.tag1'}">基本資料</th:block></b></legend>
			<table width="100%">
			  <tbody>
				<tr>
					<td valign="top">
						<button type="button" onclick="overSeaCLSPage_reg_imp_custData()"><span class="text-only"><th:block th:text="#{'l120s01a.btn7'}">引進借保人資料</th:block></span></button>
					</td>
					<td align="right">
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<span style="color:#000066"><th:block th:text="#{'l120s02.index1'}">申貸人(1)</th:block></span>
					</td>
				</tr>
			  </tbody>
			</table>	
			<!--===============================-->
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
			  <tbody>
			  	<tr>
					<td class="hd1"><th:block th:utext="#{'l120s02.index2'}">個　人　授　信　戶&nbsp;&nbsp;<br>負責事業體統一編號&nbsp;&nbsp;</th:block></td>
					<td><input type="text" id="cmpId" name="cmpId" maxlength="10" /></td>
					<td class="hd1"><th:block th:utext="#{'l120s02.index3'}">個&nbsp;&nbsp;人&nbsp;&nbsp;授&nbsp;&nbsp;信&nbsp;&nbsp;戶&nbsp;&nbsp;<br>負責事業體名稱&nbsp;&nbsp;</th:block></td>
					<td><input type="text" id="cmpNm" name="cmpNm" maxlength="120" maxlengthC="40" /></td>
				</tr>
				<tr>
					<td class="hd1" width="30%"><b class="star">＊</b><th:block th:text="#{'l120s01a.typcd'}">DBU／OBU／海外&nbsp;&nbsp;</th:block>&nbsp;&nbsp;</td>
					<td width="25%"> <span id="typCd" ></span></td>
					<td class="hd1" width="17%"><th:block th:text="#{'l120s01a.keyman'}">主要借款人</th:block>&nbsp;&nbsp;</td>
					<td width="28%">
						<!--
						p.s. 企業戶在消金簽報書，不能為主要借款人
						-->
						<input type='hidden' id="keyMan" name="keyMan" value="N">&nbsp;
						
						
						<!--
						以下為 hidden 欄位
						-->
						<th:block th:if="${hs_baseData_Y}">
							<input type='hidden' id='o_custRlt' name='o_custRlt' value=''>
							<input type='hidden' id='custPos' name='custPos' value=''>				
						</th:block>	
					</td>
				</tr>
				<th:block th:if="${hs_baseData_N}">
					<tr class='hs_by_keyMan'>
						<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01a.custrlt'}">與主要借款人關係</th:block></td>
						<td>
							<button type="button" onclick="overSeaCLSPage_reg_o_custRlt()"><span class="text-only"><th:block th:text="#{'l120s01a.btn5'}">登錄</th:block></span></button>
							
							<span class="field" id="o_custRlt" name="o_custRlt"></span>&nbsp;
							<span id="o_custRltDesc" name="o_custRltDesc"></span>			
						</td>
						<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01a.custpos'}">相關身份</th:block>&nbsp;&nbsp;</td>
						<td>
							<select class="max" maxlength="1" id="custPos" name="custPos" codetype='lms1015_custPos' itemStyle="format:{value}-{key};space:true" >
							</select>
						</td>
					</tr>
				</th:block>	
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index4'}">身分證統一編號</th:block>&nbsp;&nbsp;</td>
					<td><span class="required max" maxlength="10" id="custId" name="custId"></span>&nbsp;<th:block th:text="#{'l120s01a.dupno'}"><th:block th:text="#{'l120s02.index5'}">重覆序號</th:block></th:block>： 
					<span class="required max" maxlength="1" id="dupNo" name="dupNo"></span></td>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index6'}">姓　　名</th:block>&nbsp;&nbsp;</td>
					<td>
						<!-- 原本用 span class='field' 來呈現 custName
						但海外客戶若名字有 &，會跑出 amp;
						改用 textarea 來避免此問題  
						-->	
						<textarea id="custName" class="max txt_mult" style="line-height:12px;" readonly="readonly" rows="1" cols="30" name="custName">
						</textarea>
					</td>
				</tr>
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index7'}">出生日期</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="birthday" name="birthday" class="date"  /></td>
					<td class="hd1"><th:block th:text="#{'l120s02.index8'}">客戶編號</th:block>&nbsp;&nbsp;</td>
					<td><input type="text" id="custNo" name="custNo" class="max obuText upText" maxlength="60" /></td>
				</tr>
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01b.busCode'}">行業對象別</th:block>&nbsp;&nbsp;</td>
					<td>
						<span id="busCode" name="busCode" class='field'  ></span>
						<span id="ecoNm"   name="ecoNm"   class='field'  ></span>
						<div>
						    <button type="button" onclick="overSeaCLSPage_reg_imp_busCode()">
								<span class="text-only"><th:block th:text="#{'l120s01a.btn4'}">重新引進</th:block></span>
							</button>
						</div>
					</td>
					
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s01b.custClass'}">客戶類別</th:block>&nbsp;&nbsp;</td>
					<td>
					  	<select id="o_custClass" name="o_custClass" codeType="lms1201s02_custClass" itemStyle="format:{value}-{key};space:true;" > 
					  	</select>  
					</td>
				</tr>
				<tr>
                    <td class="hd1"><b class="star">＊</b><th:block th:text="#{'L120S01H.gradetype1'}">信用評等類型</th:block>&nbsp;&nbsp;</td>
                    <td>
                    	<label>
                        	<input id="o_crdType" name="o_crdType" type="radio" value="CK" />
                            <th:block th:text="#{'L120S01H.clsckgrade'}">評等</th:block>
                        </label>
                        <label>
                        	<input id="o_crdType" name="o_crdType" type="radio" value="NA" />
                            <th:block th:text="#{'L120S01H.clsnagrade'}">未評等</th:block>
                        </label>
                        <label>
                        	<input id="o_crdType" name="o_crdType" type="radio" value="NO" />
                            <th:block th:text="#{'L120S01H.clsnograde'}">免辦</th:block>
                        </label>
                    </td>
                    <td class="hd1"><th:block th:text="#{'L120S01H.clsgrade'}">評等等級</th:block>&nbsp;</td>
                    <td>
                    	<input type="text" id="o_grade" name="o_grade" class="max obuText upText" maxlength="10" />
                    </td>
                </tr>
				
				<tr>
					<td class="hd1"><b class="star">＊</b><th:block th:text="#{'l120s02.index15'}">國　　別</th:block>&nbsp;&nbsp;</td>
					<td>
						<select class="max" maxlength="2" id="ntCode" name="ntCode" codetype='CountryCode' itemStyle="format:{value}-{key}">
						</select>
					</td>
					<td colspan='2'>
					</td>
				</tr>				
				<tr>
					<td class="hd1"><th:block th:text="#{'l120s02.index88'}">行員編號</th:block>&nbsp;&nbsp;</td>
					<td colspan="3"><input type="text" id="megaEmpNo" name="megaEmpNo" class="max obuText" maxlength="6"/></td>
				</tr>
			  </tbody>
			</table>
		</fieldset>
		<!--=====================================================================-->		
		<div id="thickboxCustRlt" style="display:none">
				
			<table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td width="30%" class="hd1">
						<th:block th:text="#{'l120s02.other13'}">關係類別</th:block>
					</td>
					<td width="70%">
						<select id="custRlt_main" name="custRlt_main" 
							codetype='lms1205s01_RelClass'  itemStyle="space:false" />
					</td>
				</tr>
				<tr>
					<td  class="hd1">
					&nbsp;
					</td>
					<td>
						<span id="custRlt_main1" class='custRlt_mainV' style="display:none;">
		                	<select name="rationSelect1"  codetype='Relation_type1'  class='custRlt_sel'></select>
		                </span>
		                
		                <span id="custRlt_main2" class='custRlt_mainV' style="display:none;">
		                    <select name="rationSelect2"  codetype='Relation_type2'  class='custRlt_sel'></select>
		                </span>
						
		                <span id="custRlt_main3" class='custRlt_mainV' style="display:none;">                    
		                    <select name="rationSelect31" codetype='Relation_type31' class='custRlt_sel'></select>
		                    <select name="rationSelect32" codetype='Relation_type32' class='custRlt_sel'></select>
		                </span>
									
					</td>
				</tr>
			</table>
		</div>
		<!--=====================================================================-->
	</th:block>
</body>
</html>
