/* 
 * CLS1131V02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.cls.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**
 * <pre>
 * 個金徵信作業 - 基本資料
 * </pre>
 * 
 * @since 2012/10/11
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/10/11,Fantasy,new
 *          </ul>
 */
@Controller
@RequestMapping("/cls/cls1131v02")
public class CLS1131V02Page extends AbstractEloanInnerView {

	@Override
	public void execute(ModelMap model, PageParameters params) {
		addToButtonPanel(model, LmsButtonEnum.Filter, LmsButtonEnum.View);

		// build i18n
		renderJsI18N(CLS1131V01Page.class);
		renderJsI18N(CLS1131V02Page.class);

		// UPGRADE: 待確認JavaScript有無正確讀取
		model.addAttribute("loadScript",
				"loadScript('pagejs/cls/CLS1131V02Page');");
	}// ;

}
