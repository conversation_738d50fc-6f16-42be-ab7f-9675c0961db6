/* 
 * ADEB001101ServiceImpl.java
 * 
 * Copyright (c) 2009-2012 International Integrated System, Inc. 
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of International Integrated System, Inc.
 * 
 * This software is confidential and proprietary information of 
 * International Integrated System, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.batch.service.impl;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.kordamp.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.mega.eloan.common.batch.pages.WebBatchCode;
import com.mega.eloan.common.batch.service.WebBatchService;
import com.mega.eloan.common.constants.EloanConstants;
import com.mega.eloan.common.gwclient.UCCFTPClient;
import com.mega.eloan.common.model.DocFile;
import com.mega.eloan.common.service.DocFileService;
import com.mega.eloan.common.utils.IDGenerator;
import com.mega.eloan.lms.base.common.DebConfig;
import com.mega.eloan.lms.eloandb.service.EloandbBASEService;
import com.mega.eloan.lms.lns.service.LMS1201Service;
import com.mega.eloan.lms.mfaloan.service.MisELLNGTEEService;
import com.mega.eloan.lms.model.L130M01A;
import com.mega.eloan.lms.model.L201S99A;
import com.mega.eloan.lms.model.L201S99B;
import com.mega.eloan.lms.rpt.service.LMS9550R01RptService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.exception.CapFormatException;
import tw.com.iisi.cap.formatter.IFormatter;
import tw.com.iisi.cap.service.AbstractCapService;
import tw.com.iisi.cap.util.CapDate;
import tw.com.iisi.cap.util.CapMath;
import tw.com.iisi.cap.util.CapString;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 產生傳送卡務檔案 Service
 * </pre>
 * 
 * @since 2012/7/17
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/7/17,TammyChen,new
 *          <li>2012/11/29,Sunkist Wang,add interface for spring trasaction aop
 *          config
 *          <li>2012/11/30,Sunkist Wang,but!在LNSPServiceImpl.callSP() throw
 *          Exception 時將會導致這隻批次執行失敗，所以在還沒方案前改回來WebBatchService
 *          <li>2013/1/15,Tammy Chen,#1377 傳送卡務所有檔案內日期，均以YYYY/MM/DD格式顯示
 *          </ul>
 */
@Service("LMS9550R01RptBatchServiceImpl")
public class LMS9550R01RptBatchServiceImpl extends AbstractCapService implements
		WebBatchService {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Resource
	DebConfig debConfig;
	@Resource
	LMS9550R01RptService lms9550r01rptservice; // deb1010GService;

	@Resource
	DocFileService fileService;
	@Resource
	UCCFTPClient ftpClient;
	@Resource
	EloandbBASEService eloandbBaseService;
	@Resource
	MisELLNGTEEService misEllngteeService;
	@Resource
	LMS1201Service service1201;
	@Resource
	EloandbBASEService eloandbBASEService;

	final String BIG5 = "Big5";
	final String ContentType = "application/octet-stream";
	final String success = "Y";
	final String fail = "N";
	final String TW_DATE_FORMAT_STR = "YYYMMDD";
	final String TW_DATE_FORMAT_STR2 = "YYY/MM/DD";
	final String DATE_FORMAT_STR = "yyyy-MM-dd";
	final String DATE_FORMAT_STR_2 = "yyyy/MM/dd";
	final String DATE_FORMAT_STR_3 = "yyyy.MM.dd";
	final String ADD_DATA = "addList";
	final String SYSTEM = "system";
	File fileDir = null;
	final String getBrNoCol = "ICBCBR_BRNO_BRNM";
	boolean isSuccess = true;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.mega.eloan.common.batch.service.WebBatchService#execute(net.sf.json
	 * .JSONObject)
	 */
	@Override
	public JSONObject execute(JSONObject json) {
		JSONObject result = null;
		isSuccess = true;
		Timestamp currentTime = CapDate.getCurrentTimestamp();

		// 產生主檔
		L201S99A meta = new L201S99A();
		String msgId = IDGenerator.getUUID();
		meta.setMainId(msgId);
		meta.setUid(msgId);
		meta.setQryDate(CapDate.getCurrentTimestamp());
		meta.setCreDate(CapDate.getCurrentTimestamp());

		// 2.1、Z92請求回報債權、停催、請求同意清償方案及結案通知 (以日期查詢) (subCreateZ92Excel)

		List<L201S99B> result01 = subCreateA01Excel(meta);
		countAll("A01", meta, result01);

		try {
			List<L201S99B> erResult = new ArrayList<L201S99B>();
			erResult.addAll(result01);

			meta.setL201s99bs(erResult);
			// 傳送檔案至卡務中心
			meta = sendToFTP(meta);

			lms9550r01rptservice.saveS99A(meta);
		} catch (Exception e) {
			isSuccess = false;
			e.printStackTrace();
			logger.error(e.getMessage());
		}

		if (isSuccess) {
			result = WebBatchCode.RC_SUCCESS;
			result.remove(WebBatchCode.P_RC_MSG);
		} else {
			result = WebBatchCode.RC_ERROR;
			result.element(WebBatchCode.P_RC_MSG, "產檔失敗");
		}

		return result;
	}

	/**
	 * 傳送檔案至卡務中心
	 * 
	 * @param meta
	 *            D201S99A
	 * @return D201S99A
	 * @throws CapException
	 */
	public L201S99A sendToFTP(L201S99A meta) throws CapException {
		meta.setExeResult("Y");
		List<DocFile> files = fileService
				.findByIDAndPid(meta.getMainId(), null);
		ArrayList<String> filePath = new ArrayList<String>();
		ArrayList<String> fileName = new ArrayList<String>();
		if (!CollectionUtils.isEmpty(files)) {
			for (DocFile file : files) {
				if (!"Distributionlist".equals(file.getFieldId())) {
					filePath.add(fileService.getFilePath(file));
					fileName.add(file.getSrcFileName());
				}
			}
			try {

				// 後台管理->系統設定維護->LMS_ArCtrlCanShow
				Map<String, Object> onlineDateMap = eloandbBASEService
						.getSysParamData("UCCFTP_DEF_DIR");

				String serverDir = Util.trim(onlineDateMap.get("PARAMVALUE"));

				ftpClient.send(meta.getMainId(),
						filePath.toArray(new String[] {}), serverDir, // dw/ftpdata/ftpucc1/ftpout/
						// debConfig.getUCCFTPUploadPath(), // 切換路徑至：/ftpout/
						fileName.toArray(new String[] {}), true, true, false);
			} catch (Exception e) {
				meta.setExeResult("N");
				e.printStackTrace();
			}
		}
		meta.setSender(SYSTEM);
		meta.setSendTime(CapDate.getCurrentTimestamp());

		return meta;
	}

	/**
	 * Z92請求回報債權、停催、請求同意清償方案及結案通知 (以日期查詢)
	 * 
	 * @param queryDate
	 * @param meta
	 * @return D201S99B
	 */
	public List<L201S99B> subCreateA01Excel(L201S99A meta) {
		String[][] excelA01 = new String[][] { { "客戶統一編號", "CUSTID" },
				{ "登錄分行", "CASEBRID" }, { "UNID", "MAINID" },
				{ "核准日期", "ENDDATE" }, { "本行曝險金額總計", "TOTRISKAMT" },
				{ "資料修改人", "STAFFNO" }, { "資料修改日期", "CASEDATE" },
				{ "結案註記", "CLOSEFG" }, { "異常通報代碼", "MDCLASS" },
				{ "負責人統編", "CHAIRMANID" }, { "從債務人統編", "LNGEID" },{ "客戶名稱", "CUSTNAME" },{ "從債務人名稱", "LNGENAME" } };

		ArrayList<L201S99B> s99bs = new ArrayList<L201S99B>();
		Map<String, IFormatter> format = null;

		// 甲、 產出受理申請暨請求回報債權通知(Z92-ZZM200)處理。
		format = new HashMap<String, IFormatter>();
		format.put("ENDDATE", new DateFormatter());
		format.put("UPDATETIME", new DateFormatter());

		List<Map<String, Object>> misData = eloandbBaseService
				.findL130M01A_NeedSend();

		s99bs.add(createExcelA01("001", "LNFE0854A", "異常通報追蹤事項主檔", excelA01,
				misData, meta, format));

		for (Map<String, Object> data : misData) {
			String l130MainId = MapUtils.getString(data, "MAINID");
			L130M01A l130m01a = service1201.findL130m01aByMainId(l130MainId);
			if (l130m01a != null) {
				l130m01a.setNeedSend("");
				l130m01a.setSendDate(CapDate.getCurrentTimestamp());
				l130m01a.setSendMainId(meta.getMainId());
				service1201.save(l130m01a);
			}

		}

		return s99bs;
	}

	/**
	 * 產生檔案
	 * 
	 * @param dataType
	 *            資料別
	 * @param fileName
	 *            檔案名稱
	 * @param sheetNm
	 *            頁籤名稱
	 * @param cols
	 *            寫入欄位
	 * @param misData
	 *            寫入內容
	 * @param meta
	 *            主檔
	 * @param reformat
	 *            DataReformatter
	 * @return D201S99B
	 */
	public L201S99B createExcel(String dataType, String fileName,
			String sheetNm, String[][] cols, List<Map<String, Object>> misData,
			L201S99A meta, Map<String, IFormatter> reformat) {
		String cntTitle = "本頁總筆數:";

		// 組成匯出文字資料。
		StringBuffer fileString = new StringBuffer();
		fileName = CapDate.getCurrentDate(DATE_FORMAT_STR_3) + fileName
				+ ".csv";
		// 資料結果明細
		L201S99B subMeta = new L201S99B(meta);
		subMeta.setDataType(dataType);

		// 設定上傳檔案資訊
		DocFile docFile = getDocFile(meta.getMainId(), dataType);
		try {
			// 第1行先放筆數資料
			String cnt = CollectionUtils.isEmpty(misData) ? "0" : Integer
					.toString(misData.size());
			fileString.append(cntTitle).append(",").append(cnt);

			// 第2行放欄位TITLE
			fileString.append("\r\n");

			for (String[] t : cols) {
				fileString.append(t[0]).append(",");
			}

			// 第3行開始放查出資料
			if (!CollectionUtils.isEmpty(misData)) {
				for (Map<String, Object> data : misData) {
					fileString.append("\r\n");
					for (int i = 0; i < cols.length; i++) {
						String col = cols[i][1];
						String val = MapUtils.getString(data, col);
						if (reformat != null && reformat.containsKey(col)) {
							IFormatter callback = reformat.get(col);
							if (callback instanceof BrFormatter) {
								val = callback.reformat(data);
							} else {
								val = callback.reformat(val);
							}
						}
						fileString.append(val).append(",");
					}
				}
			}

			byte[] byteArray = null;
			try {
				byteArray = fileString.toString().getBytes(BIG5);
			} catch (UnsupportedEncodingException e) {
				byteArray = fileString.toString().getBytes();
			}

			// 儲存產出檔案
			docFile.setData(byteArray);
			// docFile.setFileSize(FileUtils.sizeOf(relFile));
			docFile.setFileDesc(fileName);
			docFile.setSrcFileName(fileName);
			docFile.setContentType(ContentType);
			fileService.save(docFile);

			subMeta.setDataCnt(Integer.parseInt(cnt));
			subMeta.setSendFlag(success);

		} catch (Exception e) {
			isSuccess = false;
			subMeta.setSendFlag(fail);
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return subMeta;
	}

	public void countAll(String dataType, L201S99A meta, List<L201S99B> s99bs) {
		if (!CollectionUtils.isEmpty(s99bs)) {
			L201S99B count = new L201S99B(meta);
			BigDecimal dataCnt = BigDecimal.ZERO;
			try {
				for (L201S99B s99b : s99bs) {
					if (s99b.getDataCnt() != null) {
						dataCnt = dataCnt.add(s99b.getDataCnt());
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				logger.error(e.getMessage());
			}
			count.setDataType(dataType);
			count.setDataCnt(dataCnt);
			s99bs.add(count);
		}
	}

	class BrFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String custId = "";
		String applyDate = "";

		public BrFormatter(String custId, String applyDate) {
			this.custId = custId;
			this.applyDate = applyDate;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			Map<String, Object> misData = (Map<String, Object>) in;
			StringBuffer sb = new StringBuffer();
			return sb.toString();
		}
	}

	class DateFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = DATE_FORMAT_STR_2;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				if (CapDate.isMatchPattern(val, TW_DATE_FORMAT_STR2)) {
					fromFormat = TW_DATE_FORMAT_STR2;
				} else if (CapDate.isMatchPattern(val, DATE_FORMAT_STR)) {
					fromFormat = DATE_FORMAT_STR;
				}
				return CapDate.formatDateFromF1ToF2(val, fromFormat, toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class DateYMFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		String fromFormat = TW_DATE_FORMAT_STR;
		String toFormat = "yyyy/MM";

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				return CapDate.formatDateFromF1ToF2(val + "01", fromFormat,
						toFormat);
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	class BigDecimalFormatter implements IFormatter {

		private static final long serialVersionUID = 1L;

		/*
		 * (non-Javadoc)
		 * 
		 * @see tw.com.iisi.cap.formatter.IFormatter#reformat(java.lang.Object)
		 */
		@SuppressWarnings("unchecked")
		public String reformat(Object in) throws CapFormatException {
			String val = (String) in;
			if (!CapString.isEmpty(val)) {
				BigDecimal dec = in instanceof BigDecimal ? (BigDecimal) in
						: CapMath.getBigDecimal((String) in);
				String re = dec.stripTrailingZeros().toPlainString();
				if ("0.00".equals(re)) {
					re = "0";
				}
				return re;
			}
			return EloanConstants.EMPTY_STRING;
		}
	}

	private DocFile getDocFile(String mainId, String dataType) {
		// 設定上傳檔案資訊
		DocFile docFile = new DocFile();
		docFile.setBranchId("900");
		docFile.setMainId(mainId);
		docFile.setCrYear(CapDate.getCurrentDate("yyyy"));
		docFile.setDeletedTime(null);
		docFile.setUploadTime(CapDate.getCurrentTimestamp());
		docFile.setFieldId("excel" + dataType);
		docFile.setSysId(fileService.getSysId());

		return docFile;
	}

	/**
	 * 產生檔案
	 * 
	 * @param dataType
	 *            資料別
	 * @param fileName
	 *            檔案名稱
	 * @param sheetNm
	 *            頁籤名稱
	 * @param cols
	 *            寫入欄位
	 * @param misData
	 *            寫入內容
	 * @param meta
	 *            主檔
	 * @param reformat
	 *            DataReformatter
	 * @return D201S99B
	 */
	public L201S99B createExcelA01(String dataType, String fileName,
			String sheetNm, String[][] cols, List<Map<String, Object>> misData,
			L201S99A meta, Map<String, IFormatter> reformat) {
		String cntTitle = "本頁總筆數:";

		// 組成匯出文字資料。
		StringBuffer fileString = new StringBuffer();
		fileName = CapDate.getCurrentDate(DATE_FORMAT_STR_3) + fileName
				+ ".csv";
		// 資料結果明細
		L201S99B subMeta = new L201S99B(meta);
		subMeta.setDataType(dataType);

		// 設定上傳檔案資訊
		DocFile docFile = getDocFile(meta.getMainId(), dataType);
		try {
			// 第1行先放筆數資料
			String cnt = CollectionUtils.isEmpty(misData) ? "0" : Integer
					.toString(misData.size());
			fileString.append(cntTitle).append(",").append(cnt);

			// 第2行放欄位TITLE
			fileString.append("\r\n");

			for (String[] t : cols) {
				fileString.append(t[0]).append(",");
			}

			String lngeid = null;
			String lngename = null;

			// 第3行開始放查出資料
			if (!CollectionUtils.isEmpty(misData)) {
				for (Map<String, Object> data : misData) {
					fileString.append("\r\n");

					for (int i = 0; i < cols.length; i++) {
						String col = cols[i][1];
						String custName = MapUtils.getString(data, "CUSTNAME");
						String custId = MapUtils.getString(data, "CUSTID");
						String dupNo = MapUtils.getString(data, "DUPNO");
						if (Util.equals(col, "LNGEID")) {
							// 塞從債務人統編

							// 依借款人統編抓未銷戶之從債務人
							List<Map<String, Object>> ellngteeData = misEllngteeService
									.findMisellngteeNotCancelAllLngeid(custId,
											dupNo);
							lngeid = "";
							lngename="";
							for (Map<String, Object> ellData : ellngteeData) {
								if (Util.equals(lngeid, "")) {
									lngeid = MapUtils.getString(ellData,
											"LNGEID");
									lngename = MapUtils.getString(ellData,
									"LNGENM");
								} else {
									lngeid = lngeid
											+ ":"
											+ MapUtils.getString(ellData,
													"LNGEID");
									lngename = lngename
									+ ":"
									+ MapUtils.getString(ellData,
											"LNGENM");
								}
							}

							fileString.append(lngeid).append(",");
						} else if (Util.equals(col, "CUSTNAME")) {
							//J-109-0122_10702_B1001 Web e-Loan每日傳送卡務檔案作業，新增主、從債務人名稱欄位
							if(!custName.isEmpty()){
								fileString.append(custName);
							}
							fileString.append(",");
						} else if (Util.equals(col, "LNGENAME")) {
							if(!lngename.isEmpty()){
								fileString.append(lngename);
							}
							fileString.append(",");
						} else {
							String val = MapUtils.getString(data, col);

							if (reformat != null && reformat.containsKey(col)) {
								IFormatter callback = reformat.get(col);
								if (callback instanceof BrFormatter) {
									val = callback.reformat(data);
								} else {
									val = callback.reformat(val);
								}
							}
							if (Util.equals(Util.trim(val), "")) {
								val = "";
							}
							fileString.append(val).append(",");
						}

					}

				}
			}

			byte[] byteArray = null;
			try {
				byteArray = fileString.toString().getBytes(BIG5);
			} catch (UnsupportedEncodingException e) {
				byteArray = fileString.toString().getBytes();
			}

			// 儲存產出檔案
			docFile.setData(byteArray);
			// docFile.setFileSize(FileUtils.sizeOf(relFile));
			docFile.setFileDesc(fileName);
			docFile.setSrcFileName(fileName);
			docFile.setContentType(ContentType);
			fileService.save(docFile);

			subMeta.setDataCnt(Integer.parseInt(cnt));
			subMeta.setSendFlag(success);

		} catch (Exception e) {
			isSuccess = false;
			subMeta.setSendFlag(fail);
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return subMeta;
	}

}
