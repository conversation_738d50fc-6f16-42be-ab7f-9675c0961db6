/* 
 * L160M01D.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;

/** 案件簽章欄檔 **/
@NamedEntityGraph(name = "L160M01D-entity-graph", attributeNodes = { @NamedAttributeNode("l160m01a") })
@Entity
@Table(name = "L160M01D", uniqueConstraints = @UniqueConstraint(columnNames = {
		"mainId", "staffNo", "staffJob" }))
public class L160M01D extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MAINID", referencedColumnName = "MAINID", insertable = false, updatable = false)
	private L160M01A l160m01a;

	public L160M01A getL160m01a() {
		return l160m01a;
	}

	public void setL160m01a(L160M01A l160m01a) {
		this.l160m01a = l160m01a;
	}

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** mainId **/

	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/** 行員代碼 **/
	@Column(name = "STAFFNO", length = 6, columnDefinition = "CHAR(6)")
	private String staffNo;

	/**
	 * 人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * L3. 分行授信主管<br/>
	 * L4. 分行覆核主管<br/>
	 * L5. 經副襄理<br/>
	 * L6. 總行經辦(918)<br/>
	 * L7. 總行主管(918)
	 */
	@Column(name = "STAFFJOB", length = 2, columnDefinition = "CHAR(2)")
	private String staffJob;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String Creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;

	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得mainId **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定mainId **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/** 取得行員代碼 **/
	public String getStaffNo() {
		return this.staffNo;
	}

	/** 設定行員代碼 **/
	public void setStaffNo(String value) {
		this.staffNo = value;
	}

	/**
	 * 取得人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * L3. 分行授信主管<br/>
	 * L4. 分行覆核主管<br/>
	 * L5. 經副襄理<br/>
	 * L6. 總行經辦(918)<br/>
	 * L7. 總行主管(918)
	 */
	public String getStaffJob() {
		return this.staffJob;
	}

	/**
	 * 設定人員職稱
	 * <p/>
	 * L1. 分行經辦<br/>
	 * L3. 分行授信主管<br/>
	 * L4. 分行覆核主管<br/>
	 * L5. 經副襄理<br/>
	 * L6. 總行經辦(918)<br/>
	 * L7. 總行主管(918)
	 **/
	public void setStaffJob(String value) {
		this.staffJob = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.Creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.Creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
}
