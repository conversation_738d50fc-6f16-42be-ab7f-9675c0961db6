package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.util.CapDate;

/**
 * <pre>
 * AO帳戶管理員對應服務顧客檔 LN.LNF013
 */
public interface LNLNF013Service {
	public List<Map<String, Object>> findByUnidAndCustId(String LNF013_BR_NO,
			String LNF013_CUST_ID, String LNF013_BUS_PER_FG);
	
	public List<Map<String, Object>> findByUnidAndAOId(String LNF013_BR_NO,
			String LNF013_STAFF_NO, String LNF013_BUS_PER_FG);
	
	/** 
	 * @param LNF013_BR_NO 分行別
	 * @param LNF013_STAFF_NO 企金行銷行員代號
	 * @param LNF013_BEG_DATE 企金行銷行員上任日
	 * @param LNF013_END_DATE 企金行銷行員下線日
	 * @param LNF013_CUST_ID 企金客戶編號
	 * @param LNF013_CRE_DATE 建檔日期
	 * @param LNF013_CRE_TELLER 建檔櫃員
	 * @param LNF013_CRE_SUPVNO 建檔覆核主管
	 * @param LNF013_UPD_DATE 修改日期
	 * @param LNF013_UPD_TELLER 修改櫃員
	 * @param LNF013_UPD_SUPVNO 修改覆核主管
	 * @param LNF013_DEL_DATE 刪除日期
	 * @param LNF013_DEL_TELLER 刪除櫃員
	 * @param LNF013_DEL_SUPVNO 刪除覆核主管
	 */

	public void insertByCls(
			String LNF013_BR_NO, String LNF013_STAFF_NO,
			String LNF013_BEG_DATE, String LNF013_END_DATE,
			String LNF013_CUST_ID, String LNF013_CRE_DATE,
			String LNF013_CRE_TELLER, String LNF013_CRE_SUPVNO,
			String LNF013_UPD_DATE, String LNF013_UPD_TELLER,
			String LNF013_UPD_SUPVNO, String LNF013_DEL_DATE,
			String LNF013_DEL_TELLER, String LNF013_DEL_SUPVNO, String LNF013_BUS_PER_FG); 
	
	
	/**
	 * @param LNF013_UPD_DATE 更新時間
	 * @param LNF013_UPD_TELLER 更新櫃員
	 * @param LNF013_UPD_SUPVNO 更新覆核主管
	 * @param LNF013_STAFF_NO 帳戶管理員
	 */
	public int updateByBrnoAndCustId(String LNF013_UPD_DATE, String LNF013_UPD_TELLER, String LNF013_UPD_SUPVNO, String LNF013_STAFF_NO,
			String LNF013_BR_NO, String LNF013_CUST_ID, String LNF013_BUS_PER_FG);
	
	
	public int delByCustKey(String custId, String dupNo, String cntrNo);
	
}
