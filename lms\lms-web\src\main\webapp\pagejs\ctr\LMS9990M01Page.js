/**
 *約據書
 * @since
 * <AUTHOR> @version <ul>
 *          <li>
 *          </ul>
 */
var initDfd = $.Deferred();
$(function() {
    $("#contractType").setItems({
        item: API.loadCombos("lms9990m01_contractType").lms9990m01_contractType,
        format: "{value} - {key}",
		size:4
    });
    $.form.init({
        formHandler: "lms9990m01formhandler",
        formPostData: {//把form上貼上資料
            formAction: "queryCustData",
            srcMainId: responseJSON.srcMainId,
            txCode: responseJSON.txCode
        },
        loadSuccess: function(json){
            initDfd.resolve(json);
        }
    });//close form init
    var btn = $("#buttonPanel");
    btn.find("#btnAdd").click(function(){
        openAdd();
    });
    
    /***開啟新增視窗***/
    function openAdd(){
        var addForm = $("#addForm");
        //初始化
        addForm.reset();
        
        filter1Grid({
            mainId: responseJSON.srcMainId,
            itemType: "1"
        });
        
        $("#amountBox").thickbox({
            title: i18n.lms9990m01['L999M01AM01.addTitle'],
            width: 700,
            height: 455,
            modal: true,
            valign: "bottom",
            align: "center",
            i18n: i18n.def,
            buttons: {
                sure: function(){
                    //var contractType = $("#contractType").val();
					var $contractType = $("[name=contractType]:checked");
                    if ($contractType.length == 0) {
                        return CommonAPI.showErrorMessage(i18n.lms9990m01['L999M01AM01.message02']);
                    }
                    var $grid = amountGridViewBox;
                    //多筆
                    var rowData = $grid.getSelectData("oid");
                    if (rowData) {
						var contractTypeValue = [];
                        $contractType.each(function(){
							contractTypeValue.push($(this).val());
                        });
                        addDoc(rowData,contractTypeValue.join("|"));
                    }
                    
                    
                },
                cancel: function(){
                    if (responseJSON.page == "01") {
                        $("#gridViewBox").trigger("reloadGrid");
                    }
                    $.thickbox.close();
                    
                }
            }
        });
    }
    /**
     * 取得對應的Url
     * @param {Object} contractType
     */
    function getUrl(contractType){
        var actionUrl;
        switch (contractType) {
            case "01":
			case "08":
			case "09":
                actionUrl = '../lms9990m02/01';
                break;
            case "02":
                actionUrl = '../lms9990m03/01';
                break;
            case "03":
			    //授信約定書 
                actionUrl = '../lms9990m04/01';
                break;
            case "04":
                actionUrl = '../lms9990m05/01';
                break;
			case "07":
			    //開發信用狀約定書
                actionUrl = '../lms9990m09/01';
                break;	
            case "05":
            case "06":
            case "A1":
                actionUrl = "";
                break;
        }
        
        return actionUrl;
    }
    /**
     * 新增約據書
     */
    function addDoc(oids,contractTypeValue){
        $.ajax({ //載入Form資料(客戶名稱)
            handler: "lms9990m01formhandler",
            data: {
                formAction: "saveL999m01a",
                srcMainId: responseJSON.srcMainId,
                contractType: contractTypeValue,
                l140m01a_Oids: oids,
                txCode: responseJSON.txCode
            },
        }).done(function(json){
			$.thickbox.close();
           if (responseJSON.page == "01") {
               $("#gridViewBox").trigger("reloadGrid");
           }
		}).fail(function(responseData){
			return CommonAPI.showErrorMessage(i18n.lms9990m01['L999M01AM01.message01']);
		});
    }
    
    
    var amountGridViewBox = $("#amountGridViewBox").iGrid({
        handler: 'lms1405gridhandler',
        height: 225,
        sortname: 'cntrNo',
        postData: {
            formAction: ""
        },
        multiselect: true,
        rowNum: 15,
        colModel: [{
            colHeader: i18n.lms9990m01['L140M01A.custId'],// 借款人統編
            name: 'custId',
            width: 120,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.lms9990m01['L140M01A.dupNo'],// 重複序號
            name: 'dupNo',
            width: 60,
            align: "left",
            sortable: true
        }, {
            colHeader: i18n.lms9990m01['L140M01A.custName'],// 借款人名稱
            name: 'custName',
            width: 140,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.lms9990m01['L140M01A.cntrNo'],// 額度序號
            name: 'cntrNo',
            width: 90,
            align: "center",
            sortable: true
        }, {
            colHeader: i18n.lms9990m01['L140M01A.commSno'],// 共用額度序號
            name: 'commSno',
            width: 90,
            align: "center",
            sortable: true
        }, {
            name: 'mainId',
            hidden: true
        }, {
            name: 'oid',
            hidden: true
        }]
    });
    
    //grid資料篩選
    function filter1Grid(sendData){
        $("#amountGridViewBox").jqGrid("setGridParam", {
            postData: $.extend({
                formAction: "queryL140m01a",
                mainId: responseJSON.srcMainId,
                itemType: "1"
            }, sendData || {}),
            search: true
        }).trigger("reloadGrid");
    }
    
});



