<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="http://www.thymeleaf.org">
<body>
	<th:block th:fragment="LMSS05APanel">
		<form id="CLS1205S05Form">
			<script type="text/javascript">
            </script>
	      <fieldset >
	        <legend><b><th:block th:text="#{'cls120s05.title1'}">借款用途</th:block></b></legend>
	        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	          <tr>
	            <td class="hd1" width="20%"><th:block th:text="#{'cls120s05.title1'}">借款用途</th:block>&nbsp;&nbsp;</td>
	            <td width="80%">
	              <label><input type="checkbox" id="purpose1" name="purpose" value="1"/><th:block th:text="#{'cls120s05.checkbox1'}">購料週轉金</th:block></label>
	              <label><input type="checkbox" id="purpose2" name="purpose" value="2"/><th:block th:text="#{'cls120s05.checkbox2'}">營運週轉金</th:block></label>
	              <label><input type="checkbox" id="purpose3" name="purpose" value="3" onClick="if (this.checked) { $('#chkboxA-2-3c1_edit').show(); } else { $('#chkboxA-2-3c1_edit').hide();$('#purposeOth').val(''); } "/>
				  <th:block th:text="#{'cls120s05.checkbox3'}">其他</th:block></label>
	              <span id="chkboxA-2-3c1_edit" style="display:none">
	              	 	 <th:block th:text="#{'cls120s05.title2'}">請說明：</th:block><textarea class="max txt_mult" rows="1" cols="40" id="purposeOth" name="purposeOth" maxlength="450" maxlengthC="150"></textarea>
	              </span>
	            </td>
	          </tr>
	        </table>
	      </fieldset>      
	      <fieldset >
	        <legend><b><th:block th:text="#{'cls120s05.title3'}">還款財源</th:block></b></legend>
	        <table class="tb2" width="100%" border="0" cellspacing="0" cellpadding="0">
	          <tr>
	            <td class="hd1" width="20%"><th:block th:text="#{'cls120s05.title3'}">還款財源</th:block>&nbsp;&nbsp;</td>
	            <td width="80%">
	              <label><input type="checkbox" id="resource1" name="resource" value="1"/><th:block th:text="#{'cls120s05.checkbox4'}">營業收入</th:block></label>
	              <label><input type="checkbox" id="resource2" name="resource" value="2"/><th:block th:text="#{'cls120s05.checkbox5'}">盈餘及折舊</th:block></label>
	              <label><input type="checkbox" id="resource3" name="resource" value="3" onClick="if (this.checked) { $('#chkboxA-2-3c2_edit').show(); } else { $('#chkboxA-2-3c2_edit').hide();$('#resourceOth').val(''); } "/>
				  <th:block th:text="#{'cls120s05.checkbox3'}">其他</th:block></label>
	              <span id="chkboxA-2-3c2_edit" style="display:none">
	                	<th:block th:text="#{'cls120s05.title2'}">請說明：</th:block><textarea class="max txt_mult" rows="1" cols="40" id="resourceOth" name="resourceOth" maxlength="450" maxlengthC="150"></textarea>
	              </span>
	            </td>
	          </tr>
	        </table>
	      </fieldset>
		</form>
		<!--<script type="text/javascript" src="pagejs/lns/LMS1205S04Page.js"></script>-->
	</th:block>
</body>
</html>
