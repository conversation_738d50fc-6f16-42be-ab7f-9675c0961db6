/* 
 *LMS1605R02RptServiceImpl.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E<PERSON> Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lms.report.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.iisigroup.cap.component.PageParameters;
import com.inet.report.ReportException;
import com.mega.eloan.common.pages.AbstractEloanPage;
import com.mega.eloan.lms.base.common.LMSUtil;
import com.mega.eloan.lms.base.constants.UtilConstants;
import com.mega.eloan.lms.base.service.FileDownloadService;
import com.mega.eloan.lms.lms.pages.LMS1605M01Page;
import com.mega.eloan.lms.lms.service.LMS1605Service;
import com.mega.eloan.lms.model.L160M01A;
import com.mega.eloan.lms.model.L161S01A;
import com.mega.eloan.lms.model.L161S01B;
import com.mega.sso.service.BranchService;

import tw.com.iisi.cap.exception.CapException;
import tw.com.iisi.cap.i18n.MessageBundleScriptCreator;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;
import tw.com.jcs.common.report.PdfTools;
import tw.com.jcs.common.report.ReportGenerator;

/**
 * <pre>
 *  產生動審表PDF 聯貸案參貸比率一覽表
 * </pre>
 * 
 * @since 2012/2/9
 * <AUTHOR>
 * @version <ul>
 *          <li>2012/2/9,REX,new
 *          </ul>
 */
@Service("lms1605r02rptservice")
public class LMS1605R02RptServiceImpl implements FileDownloadService {

	@Resource
	LMS1605Service service1605;

	@Resource
	BranchService branch;

	private static ThreadLocal<DecimalFormat> dfMoney = new ThreadLocal<DecimalFormat>();
	private static ThreadLocal<DecimalFormat> dfRate = new ThreadLocal<DecimalFormat>();

	protected static final Logger LOGGER = LoggerFactory
			.getLogger(LMS1605R02RptServiceImpl.class);

	/**
	 * 建立PDF
	 * 
	 * @param params
	 *            params
	 * @return OutputStream OutputStream
	 * @throws Exception
	 */
	public OutputStream generateReport(PageParameters params) throws Exception {
		String mainId = params.getString("mainId");
		String type = params.getString("type", "R02");
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		List<InputStream> list = new LinkedList<InputStream>();
		OutputStream outputStream = null;
		Locale locale = null;
		Properties propEloanPage = null;
		int subLine = 1;
		Properties rptProperties = null;
		locale = LMSUtil.getLocale();
		rptProperties = MessageBundleScriptCreator
				.getComponentResource(LMS1605R01RptServiceImpl.class);
		propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		if ("R02".equals(type)) {
			outputStream = this.genLMS1601R02(params, mainId, locale,
					propEloanPage);
		}

		pdfNameMap.put(new ByteArrayInputStream(
				((ByteArrayOutputStream) outputStream).toByteArray()), subLine);
		if (pdfNameMap != null && pdfNameMap.size() > 0) {
			outputStream = new ByteArrayOutputStream();
			PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
					propEloanPage.getProperty("PaginationText"), true, locale,
					subLine);
			list.add(new ByteArrayInputStream(
					((ByteArrayOutputStream) outputStream).toByteArray()));
		}
		outputStream = new ByteArrayOutputStream();
		PdfTools.mergeReWritePagePdf(list, outputStream);
		return outputStream;
	}

	/**
	 * 列印 動審表 - 聯貸案參貸比率一覽表
	 * 
	 * @param mainId
	 * @param locale
	 * @param rptProperties
	 * @return
	 * @throws Exception
	 */
	public OutputStream genLMS1601R02(PageParameters params, String mainId,
			Locale locale, Properties rptProperties) throws Exception {
		Map<String, String> rptVariableMap = new LinkedHashMap<String, String>();
		List<Map<String, String>> titleRows = null;
		OutputStream outputStream = null;
		ReportGenerator generator = new ReportGenerator(
				"report/lms/LMS1605R02_" + locale.toString() + ".rpt");
		List<InputStream> list = new LinkedList<InputStream>();
		Properties propEloanPage = null;
		propEloanPage = MessageBundleScriptCreator
				.getComponentResource(AbstractEloanPage.class);
		Properties prop = MessageBundleScriptCreator
				.getComponentResource(LMS1605M01Page.class);
		Map<InputStream, Integer> pdfNameMap = new LinkedHashMap<InputStream, Integer>();
		int subLine = 1;
		String rptOid = Util.nullToSpace(params.getString("uids"));
		String mode = Util.nullToSpace(params.getString("mode"));
		String[] dataSplit = rptOid.split(",");
		for (String uid : dataSplit) {
			// L160M01A．動用審核表主檔
			L160M01A l160m01a = service1605.findL160M01AByMaindId(mainId);
			// L160M01B．動審表額度序號資料
			List<L161S01B> l161s01bList = null;
			try {
				titleRows = new LinkedList<Map<String, String>>();

				locale = LocaleContextHolder.getLocale();
				if (locale == null) {
					locale = Locale.getDefault();
				}

				dfMoney.set(new DecimalFormat("#,###,###,###,##0.##"));
				dfRate.set(new DecimalFormat("#,###,###,###,##0.00"));

				L161S01A l161s01a = service1605.findL161m01aByMainIdUid(mainId,
						uid);
				l161s01bList = l161s01a.getL161s01bs();
				
				if (l161s01bList != null && !l161s01bList.isEmpty()) {
					
					if(Util.equals(mode, "OLD") ){
						 
							// 案號

							rptVariableMap.put("CASENO",
									Util.nullToSpace(l160m01a.getCaseNo()));

							 
								rptVariableMap.put("GIST",
										Util.nullToSpace(l161s01a.getGist()));
								rptVariableMap.put("CURR",
										Util.nullToSpace(l161s01a.getQuotaCurr()));
								rptVariableMap.put("QUERYEDATE",
										addThousand(l161s01a.getQuotaAmt()));
								rptVariableMap.put(
										"CASEDATE",
										prop.getProperty("L160M01A.signDate")
												+ "："
												+ this.getDate(l161s01a
														.getSignDate()));
							 
							LOGGER.info(l161s01a.getCntrNo());

							rptVariableMap.put("CNTRNO",
									Util.nullToSpace(l161s01a.getCntrNo()));
							
							titleRows = this.setL161S01BDataList(titleRows, locale,
									l161s01bList, rptProperties);

						 
					}else{
						if (Util.equals(l161s01a.getUnitMega(), "Y")
								|| Util.equals(l161s01a.getUnitCase(), "Y")) {
							// 案號

							rptVariableMap.put("CASENO",
									Util.nullToSpace(l160m01a.getCaseNo()));

							if (Util.equals(l161s01a.getCoBank(), "Y")) {
								rptVariableMap.put("GIST",
										Util.nullToSpace(l161s01a.getGist()));
								rptVariableMap.put("CURR",
										Util.nullToSpace(l161s01a.getQuotaCurr()));
								rptVariableMap.put("QUERYEDATE",
										addThousand(l161s01a.getQuotaAmt()));
								rptVariableMap.put(
										"CASEDATE",
										prop.getProperty("L160M01A.signDate")
												+ "："
												+ this.getDate(l161s01a
														.getSignDate()));
							} else {
								rptVariableMap.put("GIST", "");
								rptVariableMap.put("CURR",
										Util.nullToSpace(l161s01a
												.getCurrentApplyCurr()));
								rptVariableMap.put("QUERYEDATE",
										addThousand(l161s01a.getCurrentApplyAmt()));
								rptVariableMap.put("CASEDATE", "");
							}

							LOGGER.info(l161s01a.getCntrNo());

							rptVariableMap.put("CNTRNO",
									Util.nullToSpace(l161s01a.getCntrNo()));
							
							titleRows = this.setL161S01BDataList(titleRows, locale,
									l161s01bList, rptProperties);

						}
					}
					
					
					

				}
				generator.setLang(locale);
				generator.setVariableData(rptVariableMap);
				generator.setRowsData(titleRows);

				outputStream = generator.generateReport();

				list.add(new ByteArrayInputStream(
						((ByteArrayOutputStream) outputStream).toByteArray()));

				// pdfNameMap.put(new ByteArrayInputStream(
				// ((ByteArrayOutputStream) outputStream).toByteArray()),
				// subLine);

			} finally {
				if (rptVariableMap != null) {
					rptVariableMap.clear();
				}
			}
		}

		// if (pdfNameMap != null && pdfNameMap.size() > 0) {
		// outputStream = new ByteArrayOutputStream();
		// PdfTools.mergeReWritePagePdf(pdfNameMap, outputStream,
		// propEloanPage.getProperty("PaginationText"), true, locale,
		// subLine);
		// list.add(new ByteArrayInputStream(
		// ((ByteArrayOutputStream) outputStream).toByteArray()));
		// }

		// outputStream = new ByteArrayOutputStream();
		// PdfTools.mergeReWritePagePdf(list, outputStream);

		PdfTools.mergeReWritePagePdf(list, outputStream);

		return outputStream;
	}

	/**
	 * 取得日期(XXXX-XX-XX)
	 * 
	 * @param date
	 *            日期
	 * @return 日期
	 */
	private String getDate(Date date) {
		String str = null;
		if (date == null) {
			str = "";
		} else {
			str = TWNDate.toAD(date);
		}
		return str;
	}

	/**
	 * 設定L161S01B資料
	 * 
	 * @param titleRows
	 *            多值MAP
	 * @param list
	 *            L161S01B List
	 * @return titleRows 多值MAP
	 */
	private List<Map<String, String>> setL161S01BDataList(
			List<Map<String, String>> titleRows, Locale locale,
			List<L161S01B> list, Properties prop) {
		// F代表第一次重覆 前面資料都要先印出來 之後才印重複資料(Y) 重複資料印完後才印後面的資料(N)
		Map<String, String> mapInTitleRows = null;
		int count = 1;
		StringBuffer temp = new StringBuffer();
		for (L161S01B l161s01b : list) {
			mapInTitleRows = Util.setColumnMap();
			mapInTitleRows.put("ReportBean.column01", String.valueOf(count));
			setBrankName(mapInTitleRows, l161s01b, temp);
			mapInTitleRows.put("ReportBean.column04", UtilConstants.DEFAULT.是
					.equals(l161s01b.getSlMaster()) ? prop.getProperty("yes")
					: "");
			mapInTitleRows.put("ReportBean.column05",
					Util.nullToSpace(l161s01b.getSlAccNo()));
			mapInTitleRows.put("ReportBean.column06",
					this.addThousand(l161s01b.getSlAmt()));
			count++;
			titleRows.add(mapInTitleRows);
		}
		return titleRows;
	}

	/**
	 * 處理銀行名稱顯示
	 * 
	 * @param mapInTitleRows
	 * @param l161s01b
	 *            聯貸案參貸比率一覽表明細檔
	 * @param temp
	 *            暫存文字用
	 * @return
	 */
	private Map<String, String> setBrankName(
			Map<String, String> mapInTitleRows, L161S01B l161s01b,
			StringBuffer temp) {

		switch (Util.parseInt(l161s01b.getSlBankType())) {
		case 1:
		case 2:
			// mapInTitleRows.put(
			// "ReportBean.column02",
			// temp.append(l161s01b.getSlBank()).append(" ")
			// .append(l161s01b.getSlBankCN()).toString());
			// temp.setLength(0);
			// mapInTitleRows.put(
			// "ReportBean.column03",
			// temp.append(l161s01b.getSlBranch()).append(" ")
			// .append(l161s01b.getSlBranchCN()).toString());
			// temp.setLength(0);

			String str1 = new StringBuffer(l161s01b.getSlBank()).append(" ")
					.append(l161s01b.getSlBankCN()).toString();

			String str2 = new StringBuffer(l161s01b.getSlBranch()).append(" ")
					.append(l161s01b.getSlBranchCN()).toString();

			if (Util.notEquals(Util.trim(str1), "")) {
				temp.append(str1);
			}

			if (Util.notEquals(Util.trim(str2), "")) {
				if (Util.equals(Util.trim(temp.toString()), "")) {
					temp.append(str2);
				} else {
					char[] newLine = { 13, 10 };
					temp.append(newLine).append(str2);
				}
			}
			mapInTitleRows.put("ReportBean.column07", temp.toString());
			temp.setLength(0);

			break;
		case 3:
		case 4:
		case 5:
		case 6:
		case 7:
		case 8:
		case 9:
		case 10:
		case 11:
		case 12:
		case 99:
			// mapInTitleRows.put(
			// "ReportBean.column02",
			// temp.append(l161s01b.getSlBank()).append(" ")
			// .append(l161s01b.getSlBankCN()).toString());
			// temp.setLength(0);
			// mapInTitleRows.put("ReportBean.column03", "");

			mapInTitleRows.put(
					"ReportBean.column07",
					temp.append(l161s01b.getSlBank()).append(" ")
							.append(l161s01b.getSlBankCN()).toString());
			temp.setLength(0);
			break;

		}

		return mapInTitleRows;
	}

	/**
	 * 轉換成千分位
	 * 
	 * @param object
	 *            將數字塞入千分位
	 * @return String String
	 */
	private String addThousand(Object object) {
		return object == null ? "0" : dfMoney.get().format(object);
	}

	@Override
	public byte[] getContent(PageParameters params) throws CapException,
			FileNotFoundException, ReportException, IOException, Exception {
		ByteArrayOutputStream baos = null;
		try {
			baos = (ByteArrayOutputStream) this.generateReport(params);
			return baos.toByteArray();
		} finally {
			if (baos != null) {
				baos.close();
			}

		}
	}
}
