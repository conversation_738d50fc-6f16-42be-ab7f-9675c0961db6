/* 
 * L999S01A.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON>g E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import tw.com.iisi.cap.model.GenericBean;
import tw.com.iisi.cap.model.IDataObject;

import com.mega.eloan.common.model.IDocObject;
import com.mega.eloan.common.model.listener.DocumentModifyListener;

/** 綜合授信契約書檔 **/
@Entity
@EntityListeners({ DocumentModifyListener.class })
@Table(name = "L999S01A", uniqueConstraints = @UniqueConstraint(columnNames = { "oid" }))
public class L999S01A extends GenericBean implements IDataObject, IDocObject {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 * <p/>
	 * ROWID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid-hex")
	@Column(name = "OID", length = 32, columnDefinition = "CHAR(32)", nullable = false)
	private String oid;

	/** 文件編號 **/
	@Column(name = "MAINID", length = 32, columnDefinition = "CHAR(32)")
	private String mainId;

	/**
	 * 授信種類
	 * <p/>
	 * ※可複選<br/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 */
	@Column(name = "ITEMTYPE", length = 8, columnDefinition = "VARCHAR(8)")
	private String itemType;

	/** 授信總額度 **/
	@Column(name = "TOTLOANAMT", columnDefinition = "DECIMAL(17,2)")
	private BigDecimal totLoanAmt;

	/** 動用期間(起) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "USEDSDATE", columnDefinition = "DATE")
	private Date usedSDate;

	/** 動用期間(迄) **/
	@Temporal(TemporalType.DATE)
	@Column(name = "USEDEDATE", columnDefinition = "DATE")
	private Date usedEDate;

	/**
	 * 違約金及遲延利息(遲延還本或付息)-逾期
	 * <p/>
	 * 逾期在XX個月以內部份<br/>
	 * ※預設為6
	 */
	@Column(name = "DMONTH1", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth1;

	/**
	 * 違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX<br/>
	 * ※預設為10
	 */
	@Column(name = "DRATE1", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate1;

	/**
	 * 違約金及遲延利息(遲延還本或付息)-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份<br/>
	 * ※預設為6
	 */
	@Column(name = "DMONTH2", columnDefinition = "DECIMAL(2,0)")
	private Integer dMonth2;

	/**
	 * 違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金<br/>
	 * ※預設為20
	 */
	@Column(name = "DRATE2", columnDefinition = "DECIMAL(2,0)")
	private Integer dRate2;

	/**
	 * 違約金及遲延利息(未依約清償本金)-計收延遲利息加碼
	 * <p/>
	 * 立約人未依約清償本金時，除前項違約金外並應依本借款之約定利率加年利率％計付遲延利息。<br/>
	 * ※預設為1.00
	 */
	@Column(name = "DRATEADD1", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal dRateAdd1;

	/**
	 * 違約金及遲延利息(未依約清償本金)-計收延遲利息加碼(保證債務)
	 * <p/>
	 * 如係保證債務時，自銀行墊款日起，依墊款日銀行新台幣基準利率加年利率XX％計付遲延利息，並依前項之規定計收違約金。<br/>
	 * ※預設為1.00
	 */
	@Column(name = "DRATEADD2", columnDefinition = "DECIMAL(5,2)")
	private BigDecimal dRateAdd2;

	/** 建立人員號碼 **/
	@Column(name = "CREATOR", length = 6, columnDefinition = "CHAR(6)")
	private String creator;

	/** 建立日期 **/
	@Column(name = "CREATETIME", columnDefinition = "TIMESTAMP")
	private Date createTime;

	/** 異動人員號碼 **/
	@Column(name = "UPDATER", length = 6, columnDefinition = "CHAR(6)")
	private String updater;

	/** 異動日期 **/
	@Column(name = "UPDATETIME", columnDefinition = "TIMESTAMP")
	private Date updateTime;
	
	/** 額度幣別 **/
	@Column(name = "TOTLOANCURR", columnDefinition = "CHAR(3)")
	private String totLoanCurr;
	
	/**
	 * 取得oid
	 * <p/>
	 * ROWID
	 */
	public String getOid() {
		return this.oid;
	}

	/**
	 * 設定oid
	 * <p/>
	 * ROWID
	 **/
	public void setOid(String value) {
		this.oid = value;
	}

	/** 取得文件編號 **/
	public String getMainId() {
		return this.mainId;
	}

	/** 設定文件編號 **/
	public void setMainId(String value) {
		this.mainId = value;
	}

	/**
	 * 取得授信種類
	 * <p/>
	 * ※可複選<br/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 */
	public String getItemType() {
		return this.itemType;
	}

	/**
	 * 設定授信種類
	 * <p/>
	 * ※可複選<br/>
	 * A.購料借款<br/>
	 * B.外銷借款<br/>
	 * C.營運週轉借款<br/>
	 * D.貼現<br/>
	 * E.透支<br/>
	 * F.委任票據保證<br/>
	 * G.委任票據承兌<br/>
	 * H.委任保證
	 **/
	public void setItemType(String value) {
		this.itemType = value;
	}

	/** 取得授信總額度 **/
	public BigDecimal getTotLoanAmt() {
		return this.totLoanAmt;
	}

	/** 設定授信總額度 **/
	public void setTotLoanAmt(BigDecimal value) {
		this.totLoanAmt = value;
	}

	/** 取得動用期間(起) **/
	public Date getUsedSDate() {
		return this.usedSDate;
	}

	/** 設定動用期間(起) **/
	public void setUsedSDate(Date value) {
		this.usedSDate = value;
	}

	/** 取得動用期間(迄) **/
	public Date getUsedEDate() {
		return this.usedEDate;
	}

	/** 設定動用期間(迄) **/
	public void setUsedEDate(Date value) {
		this.usedEDate = value;
	}

	/**
	 * 取得違約金及遲延利息(遲延還本或付息)-逾期
	 * <p/>
	 * 逾期在XX個月以內部份<br/>
	 * ※預設為6
	 */
	public Integer getDMonth1() {
		return this.dMonth1;
	}

	/**
	 * 設定違約金及遲延利息(遲延還本或付息)-逾期
	 * <p/>
	 * 逾期在XX個月以內部份<br/>
	 * ※預設為6
	 **/
	public void setDMonth1(Integer value) {
		this.dMonth1 = value;
	}

	/**
	 * 取得違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX<br/>
	 * ※預設為10
	 */
	public Integer getDRate1() {
		return this.dRate1;
	}

	/**
	 * 設定違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX<br/>
	 * ※預設為10
	 **/
	public void setDRate1(Integer value) {
		this.dRate1 = value;
	}

	/**
	 * 取得違約金及遲延利息(遲延還本或付息)-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份<br/>
	 * ※預設為6
	 */
	public Integer getDMonth2() {
		return this.dMonth2;
	}

	/**
	 * 設定違約金及遲延利息(遲延還本或付息)-逾期超過
	 * <p/>
	 * 逾期超過XX個月部份<br/>
	 * ※預設為6
	 **/
	public void setDMonth2(Integer value) {
		this.dMonth2 = value;
	}

	/**
	 * 取得違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金<br/>
	 * ※預設為20
	 */
	public Integer getDRate2() {
		return this.dRate2;
	}

	/**
	 * 設定違約金及遲延利息(遲延還本或付息)-利率百分比
	 * <p/>
	 * 按約定利率百分之XX計違約金<br/>
	 * ※預設為20
	 **/
	public void setDRate2(Integer value) {
		this.dRate2 = value;
	}

	/**
	 * 取得違約金及遲延利息(未依約清償本金)-計收延遲利息加碼
	 * <p/>
	 * 立約人未依約清償本金時，除前項違約金外並應依本借款之約定利率加年利率％計付遲延利息。<br/>
	 * ※預設為1.00
	 */
	public BigDecimal getDRateAdd1() {
		return this.dRateAdd1;
	}

	/**
	 * 設定違約金及遲延利息(未依約清償本金)-計收延遲利息加碼
	 * <p/>
	 * 立約人未依約清償本金時，除前項違約金外並應依本借款之約定利率加年利率％計付遲延利息。<br/>
	 * ※預設為1.00
	 **/
	public void setDRateAdd1(BigDecimal value) {
		this.dRateAdd1 = value;
	}

	/**
	 * 取得違約金及遲延利息(未依約清償本金)-計收延遲利息加碼(保證債務)
	 * <p/>
	 * 如係保證債務時，自銀行墊款日起，依墊款日銀行新台幣基準利率加年利率XX％計付遲延利息，並依前項之規定計收違約金。<br/>
	 * ※預設為1.00
	 */
	public BigDecimal getDRateAdd2() {
		return this.dRateAdd2;
	}

	/**
	 * 設定違約金及遲延利息(未依約清償本金)-計收延遲利息加碼(保證債務)
	 * <p/>
	 * 如係保證債務時，自銀行墊款日起，依墊款日銀行新台幣基準利率加年利率XX％計付遲延利息，並依前項之規定計收違約金。<br/>
	 * ※預設為1.00
	 **/
	public void setDRateAdd2(BigDecimal value) {
		this.dRateAdd2 = value;
	}

	/** 取得建立人員號碼 **/
	public String getCreator() {
		return this.creator;
	}

	/** 設定建立人員號碼 **/
	public void setCreator(String value) {
		this.creator = value;
	}

	/** 取得建立日期 **/
	public Date getCreateTime() {
		return this.createTime;
	}

	/** 設定建立日期 **/
	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	/** 取得異動人員號碼 **/
	public String getUpdater() {
		return this.updater;
	}

	/** 設定異動人員號碼 **/
	public void setUpdater(String value) {
		this.updater = value;
	}

	/** 取得異動日期 **/
	public Date getUpdateTime() {
		return this.updateTime;
	}

	/** 設定異動日期 **/
	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	/** 設定額度幣別 **/
	public void setTotLoanCurr(String totLoanCurr) {
		this.totLoanCurr = totLoanCurr;
	}

	/** 取得額度幣別 **/
	public String getTotLoanCurr() {
		return totLoanCurr;
	}
}
