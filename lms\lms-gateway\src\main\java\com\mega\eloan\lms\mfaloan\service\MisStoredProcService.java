package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

public interface MisStoredProcService {
	/**
	 * 額度序號給號
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param unitCode
	 * @param classCD
	 * @return
	 */
	public Map<String, Object> callLNSP0050(String ownBrId, String unitCode,
			String classCD);

	/**
	 * 黑名單查詢<br/>
	 * 應改用 ICustomerService :: findBlackList
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param eName
	 *            英文名稱
	 * @return
	 */
	@Deprecated
	public Map<String, Object> callLNSP0130(String ownBrId, String eName);

	/**
	 * 建檔給號程式
	 * 
	 * @param ownBrId
	 *            分行代碼
	 * @param idType
	 *            客戶類別 1. 個人戶 2. 公司戶
	 * @param custID
	 *            台灣 ID/ SWIFT ID/ 取號給空白
	 * @param companyCountry
	 *            公司註冊地國別
	 * @param localCountry
	 *            所在地國別
	 * @param licenseType
	 *            證件類型所在地國別為 CN 輸入
	 * 
	 *            <pre>
	 *  * 自然人代碼                  非自然人代碼
	 * 0- 居民身分證               3- 工商登記證
	 * 1- 護照                     4- 稅務登記證
	 * 2- 軍人身分證               7- 組織機構代碼
	 * 5- 股東代碼證               8- 企業營業執照
	 * A- 武警身分證               9- 法人代碼證
	 * B- 港澳身分證               Z- 其他證件
	 * C- 台灣居民身分證
	 * D- 外國公民護照
	 * E- 戶口本
	 * F- 臨時身分證
	 * G- 警察證
	 * Z- 其他證件
	 * </pre>
	 * @param licenseNO
	 *            證件號碼
	 * @param business_CD
	 *            主計處行業對象別
	 * @param cName
	 *            繁體戶名 ( 全形 ) 將轉換為國內 IBM 主機碼 937
	 * @param lName
	 *            當地戶名 ( 全形 ) 將轉換為海外 AS400 IBM 主機碼
	 * @param codePage
	 *            海外 AS400 IBM 主機碼 CODE-PAGE
	 * @param eName
	 *            英文戶名 ( 半形 )
	 * @param birthday
	 *            公司設立日 ( 個人戶出生年月日 )8碼
	 * @param localID
	 *            海外分行當地 AS400 編碼 ID
	 * @param busSubCD
	 *            行業對象別細分類
	 * @param email
	 *            EMAIL
	 * @return <pre>
	 * OUT SP_MEGA_ID      回傳給號
	 * OUT SP_OUT_CNAME    回傳繁體戶名
	 * OUT SP_OUT_LNAME    回傳當地戶名
	 * OUT SP_OUT_ENAME    回傳英文戶名
	 * OUT SP_RETURN       
	 * OUT SP_ERROR_MSG
	 * </pre>
	 */
	public Map<String, Object> callLNSP0270(String ownBrId, String idType,
			String custID, String companyCountry, String localCountry,
			String licenseType, String licenseNO, String business_CD,
			String cName, String lName, String codePage, String eName,
			String birthday, String localID, String busSubCD, String email);

	/**
	 * 查相同英文戶名已存在MEGA-ID資訊
	 * 
	 * 
	 * @return <pre>
	 * COLUMN 1:同英文戶名客戶ID,
	 * COLUMN 2:重複序號,
	 * COLUMN 3:同客戶ID公司戶設立日,
	 * COLUMN 4:負責人ID,
	 * COLUMN 5:SWIFT-ID,
	 * COLUMN 6:公司註冊地國別
	 * </pre>
	 */
	public Map<String, Object> callLNSP0271(String eName);

	/**
	 * 異常通報停權上傳(LNSP0290)
	 * 
	 * @param caseBrid
	 *            分行代號
	 * @param caseNo
	 *            案件編號
	 * @param custId
	 *            客戶統編
	 * @param suspendCode
	 *            1 逾期 2 拒往 3 財異 4 退票
	 * @param suspendMons
	 *            停權月數
	 * @param loanDate
	 *            最近簽案後首撥日
	 * @param ovDate
	 *            逾期設定日
	 * @param setBrno
	 *            設定單位
	 * @param setUser
	 *            設定覆核人員
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	public Map<String, Object> callLNSP0290(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String loanDate, String ovDate, String setBrno, String setUser,
			String mainId);

	/**
	 * 異常通報停權清除(LNSP0291)
	 * 
	 * @param caseBrid
	 *            分行代號
	 * @param caseNo
	 *            案件編號
	 * @param custId
	 *            客戶統編
	 * @param suspendCode
	 *            1 逾期 2 拒往 3 財異 4 退票
	 * @param suspendMons
	 *            停權月數
	 * @param setBrno
	 *            設定單位
	 * @param setUser
	 *            設定覆核人員
	 * @param mainId
	 *            文件編號
	 * @return
	 */
	public Map<String, Object> callLNSP0291(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String mainId);

	/**
	 * 授管處停權維護新增(LNSP0290)
	 * 
	 * @param caseBrid
	 *            分行代號
	 * @param caseNo
	 *            案件編號
	 * @param custId
	 *            客戶統編(含重覆序號)
	 * @param suspendCode
	 *            1 逾期 2 拒往 3 財異 4 退票
	 * @param suspendMons
	 *            停權月數
	 * @param setBrno
	 *            設定單位(目前固定帶918)
	 * @param setUser
	 *            設定覆核人員
	 * @param oid
	 *            oid
	 * @return
	 */
	public Map<String, Object> addStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String oid);

	/**
	 * 授管處停權維護修改(LNSP0291)
	 * 
	 * @param caseBrid
	 *            分行代號
	 * @param caseNo
	 *            案件編號
	 * @param custId
	 *            客戶統編(含重覆序號)
	 * @param suspendCode
	 *            1 逾期 2 拒往 3 財異 4 退票
	 * @param suspendMons
	 *            停權月數
	 * @param setBrno
	 *            設定單位(目前固定帶918)
	 * @param setUser
	 *            設定覆核人員
	 * @param newCaseNo
	 *            新案件編號
	 * @param oid
	 *            oid
	 * @return
	 */
	public Map<String, Object> modifyStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String newCaseNo, String oid);

	/**
	 * 授管處停權維護刪除(LNSP0291)
	 * 
	 * @param caseBrid
	 *            分行代號
	 * @param caseNo
	 *            案件編號
	 * @param custId
	 *            客戶統編(含重覆序號)
	 * @param suspendCode
	 *            1 逾期 2 拒往 3 財異 4 退票
	 * @param suspendMons
	 *            停權月數
	 * @param setBrno
	 *            設定單位(目前固定帶918)
	 * @param setUser
	 *            設定覆核人員
	 * @param newCaseNo
	 *            新案件編號
	 * @param oid
	 *            oid
	 * @return
	 */
	public Map<String, Object> delStop(String caseBrid, String caseNo,
			String custId, String suspendCode, String suspendMons,
			String setBrno, String setUser, String newCaseNo, String oid);

	public Map<String, Object> callLNSP0150(String branch, String custId,
			String dupNo);

	/**
	 * 額度明細表引進收付彙計數
	 * 
	 * @param cntrNo
	 *            額度序號
	 * @param begDate
	 *            查詢起日
	 * @param endDate
	 *            查詢迄日
	 * @return
	 */
	Map<String, Object> callLNSP0330(String cntrNo, String begDate,
			String endDate);

	/**
	 * 配合洗錢防制－檢核是否執行風險評估作業 output X(02){00:檢核OK, 03:客戶未於0024-23建檔,
	 * 04:查詢之業務往來項目無勾選, 05:法人戶無辨識實際受益人} X(01)風險評估等級 {H:高,M:中,L:低} X(70)錯誤訊息
	 * SP_OUTPUT_AREA={01S10039803800203 CMFLUNVA無資料！請執行0024-23洗錢防制專區！ }
	 * SP_OUTPUT_AREA={01A12345678900200L }
	 */
	public Map<String, Object> callSCMLUINQ(String idDup);

	/**
	 * {SP_RETURN=YES, SP_ERROR_MSG= , SP_OUTPUT_AREA={0000A1130334610Y }}
	 */
	public Map<String, Object> callTRPAYSQ1(String idDup);

	/**
	 * 檢核 活期存款 帳號是否存在
	 */
	public Map<String, Object> callLNPS188(String accNo);

	public Map<String, Object> callLNPS188(String ac_type, String acctNo,
			String curr);

	/**
	 * I-107-0260_05097_B1001 Web e-Loan企金授信系統增加提示訊息【該客戶屬於巴拿馬文件名單】
	 * 
	 * @param custId
	 * @param dupNo
	 * @return
	 */
	public Map<String, Object> callCMPEPUPD(String custId, String dupNo);

	public Map<String, Object> callSCMLUINQ(String custId, String dupNo, String dept,
			 String brId, String userId);
	
	public Map<String, Object> getRefundAndRejectedInfo(String custId,
			String dupno, String ejcicVirtualId);
	
	/**
	 * J-113-0035 為利ESG案件之貸後管控
	 * @param cntrNo
	 * @param esgFlag
	 * @return
	 */
	public Map<String, Object> callLNSP0600(String cntrNo, String esgFlag);	

	/**
	 * J-110-0182_05097_B1002 Web e-Loan國內企金授信配合經濟部紓困貸款更改為非紓困案仍需持續補貼，修改相關程式。
	 * 
	 * @param custId
	 * @param dupNo
	 * @param cntrNo
	 * @param rescueItem
	 * @param rescueRate
	 * @return
	 */
	public Map<String, Object> callLNSP0141(String custId, String dupNo,
			String cntrNo, String rescueItem, BigDecimal rescueRate);

	public Map<String, Object> insertLNF087(
			String contract, String controlCd, BigDecimal ltvRate, 
			String locationCd, BigDecimal appAmt, String plusReason, 
			String jcicMark, String cocollFg, BigDecimal sumFactamt,
			String partFund, String plusMemo, String regPurpose, 
			BigDecimal estAmt, BigDecimal lawval, String restrict, 
			String hpHouse, String planArea, String pUsetype, 
			String pLoanuse, String collChar, String keepLawval, 
			BigDecimal site3no, String site4no, String collCharM, 
			BigDecimal houseAge, BigDecimal loanamt, String version, 
			String hloanlimit, String hloanlimit2, Date enddate, 
			Date lstdate, BigDecimal timeval, String custId, String dupNo);
	
	
	/**
	 * J-113-0168 E-LOAN完成動審上送額度介面資料至A-LOAN時，針對屬授信額度者(排除衍生性金融商品業務外)，立即通知0024為本行授信戶
	 * (排除)衍生性金融商品科目961 962 963 964 、 純Z類的虛科目、應收帳款買方 fact_type = 60的不用通知
	 * @param custId
	 * @param dupNo
	 * @param empId
	 * @param ownBrId
	 * @return
	 */
	public Map<String, Object> callLNSP0130ForUpLnFlag(String custId, String dupNo, String empId, String ownBrId);
	
}
