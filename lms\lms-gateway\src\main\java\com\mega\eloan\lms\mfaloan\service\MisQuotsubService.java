/* 
 *MisQuotsubService.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming Sheng E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */

package com.mega.eloan.lms.mfaloan.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * 科(子)目及其限額檔QUOTSUB (MIS.ELV38401)
 * </pre>
 * 
 * @since 2011/12/23
 * <AUTHOR>
 * @version <ul>
 *          <li>2011/12/23,REX,new
 *          </ul>
 */
public interface MisQuotsubService {

	/**
	 * 動審表 -新增 科(子)目及其限額檔
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            當天日期
	 * @param loantp
	 *            科(子)目-簡碼
	 * @param chgflag
	 *            科(子)目變更註
	 * @param oldcurr
	 *            原幣限額幣別
	 * @param oldquota
	 *            原限額
	 * @param newcurr
	 *            新限額幣別
	 * @param newquota
	 *            新限額
	 * @param lngu
	 *            擔保代號
	 * @param updater
	 *            資料修改人
	 * @param lmtDays
	 *            清償期限
	 * @param lnapFlag
	 * 			  合併科目限額註記
	 */
	public void insert(String custId, String dupNo, String cntrNo,
			String sDate, String loantp, String chgflag, String oldcurr,
			Double oldquota, String newcurr, Double newquota, String lngu,
			String updater,BigDecimal lmtDays, String lnapFlag);

	/**
	 * 更新
	 * 
	 * @param chgFlag
	 *            科(子)目變更註記
	 * @param oldCurr
	 *            原幣限額幣別
	 * @param oldQuota
	 *            原限額
	 * @param newCurr
	 *            新限額幣別
	 * @param newQuota
	 *            新限額
	 * @param lngu
	 *            擔保代號
	 * @param updater
	 *            資料修改人
	 * @param custId
	 *            借款人統編 以下為where 條件
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            當天日期
	 * @param loanTp
	 *            科(子)目-簡碼
	 * @param lmtDays
	 *            清償期限
	 * @param lnapFlag
	 *            合併科目限額註記
	 */
	public void update(String chgFlag, String oldCurr, Double oldQuota,
			String newCurr, Double newQuota, String lngu, String updater,
			String custId, String dupNo, String cntrNo, String sDate,
			String loanTp,BigDecimal lmtDays, String lnapFlag);

	/**
	 * 查詢
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @param loanTp
	 *            科(子)目-簡碼
	 * @return
	 */
	public List<Map<String, Object>> selByUniqueKey(String custId,
			String dupNo, String cntrNo, String sDate, String loanTp);

	/**
	 * 查詢 該筆額度明細表最新記錄
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @return <pre>
	 * OLDSDATE: MIS內最新系統時間
	 * TOTAL : 總筆數
	 * </pre>
	 */
	public Map<String, Object> selByUniqueKeyMaxSdate(String custId,
			String dupNo, String cntrNo);

	/**
	 * 查詢 該筆額度明細表系統日期記錄
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @return <pre>
	 * ONLNTIME: 
	 * BTHTIME :
	 * </pre>
	 * 
	 */
	public Map<String, Object> selBySdate(String custId, String dupNo,
			String cntrNo, String sDate);

	/**
	 * 刪除 該筆額度明細表系統日期 資料
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @return <pre>
	 * ONLNTIME: 
	 * BTHTIME :
	 * </pre>
	 * 
	 */
	public int delBySdate(String custId, String dupNo, String cntrNo,
			String sDate);

	/**
	 * 將系統當天同時間的 CHGFLAG更新為取消
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * 
	 */
	public int updateChgflagBySdate(String custId, String dupNo, String cntrNo,
			String sDate);

	/**
	 * 將舊資料複製到新資料 且將變更註記變為取消, a-Loan Batch Time 改成Null
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @param updater
	 *            更新者
	 * 
	 */
	/**
	 * 將舊資料複製到新資料 且將變更註記變為取消, a-Loan Batch Time 改成Null
	 * 
	 * 
	 * @param custId
	 *            借款人統編
	 * @param dupNo
	 *            重複序號
	 * @param cntrNo
	 *            額度序號
	 * @param sDate
	 *            系統時間
	 * @param OLDSDATE
	 *            舊案時間
	 * @param updater
	 *            更新者
	 * 
	 * @return
	 */
	public int insetSelectOldData(String custId, String dupNo, String cntrNo,
			String sDate, String OLDSDATE, String updater);

	public Map<String, Object> selByUniqueKeyMaxSdate2(String custId, String dupNo,
			String cntrNo, String loanTp);

	public Map<String, Object> selByUniqueKey2(String custId, String dupNo,
			String cntrNo, String loanTp, String sDate);

}
