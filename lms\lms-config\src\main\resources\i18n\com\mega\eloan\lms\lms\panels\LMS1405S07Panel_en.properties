#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u932f\u8aa4\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L140S16A.error1 = \u67e5\u8a62\u8d77\u59cb\u5e74\u6708
L140S16A.error2 = \u67e5\u8a62\u8fc4\u81f3\u5e74\u6708
L140S16A.error3 = \u5c0f\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u521d\u59cb\u65e5 (\u76ee\u524d\u7121\u8cc7\u6599)
L140S16A.error4 = \u5c0f\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u521d\u59cb\u65e5 
L140S16A.error5 = \u5927\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u65b0\u65e5\u671f (\u76ee\u524d\u7121\u8cc7\u6599)
L140S16A.error6 = \u5927\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u65b0\u65e5\u671f
L140S16A.error7 = \u203b\u8cc7\u6599\u5009\u5132\u6bcf\u500b\u670810\u865f\u624d\u5f59\u7e3d\u66f4\u65b0\u4e0a\u6708\u4efd\u5f80\u4f86\u8cc7\u6599(\u8207DW C350\u4ea4\u6613\u4f9d\u5404\u696d\u52d9\u8cc7\u6599\u5099\u9f4a\u6642\u9593\u5373\u66f4\u65b0\u65b9\u5f0f\u4e0d\u540c)\u3002
L140S16A.error8 = \u5927\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u521d\u59cb\u65e5 
L140S16A.error9 = \u5927\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u521d\u59cb\u65e5 (\u76ee\u524d\u7121\u8cc7\u6599)
L140S16A.error10 = \u5c0f\u65bcDW\u8cc7\u6599\u7bc4\u570d\u6700\u65b0\u65e5\u671f
L140S16A.error11 = \u6700\u65b0\u96c6\u5718\u4f01\u696d\u6388\u4fe1\u5f80\u4f86\u60c5\u5f62\u70ba\u7a7a\uff0c\u8acb\u81f3"\u76f8\u95dc\u6587\u4ef6"\u5f15\u9032\uff01

l120v01.error3=\u8f38\u5165\u4e0d\u5408\u6cd5\u7684\u6708\u4efd!
l120v01.error8=\u8f38\u5165\u4e0d\u5408\u6cd5\u7684\u5e74\u4efd\uff01
l120v01.error9=\u8f38\u5165\u4e0d\u5408\u6cd5\u7684\u5e74\u4efd\u7bc4\u570d\uff01
#==================================================
# \u5f80\u4f86\u5f59\u7e3dGrid\u6a19\u984c\u540d\u7a31
#==================================================
L140S16A.grida=Borrower's Name
L140S16A.gridb=Borrower
L140S16A.grid3=Credit Items
L140S16A.grid4=Credit Limit Serial Number
L140S16A.grid5=Credit period
L140S16A.grid6=Guarantor
L140S16A.grid7=Reference Credit Limit Serial Number
L140S16A.grid8=Print Mode
L140S16A.grid9=Applicant
L140S16A.grid10=Approval Date
L140S16A.grid11=Document Status
L140S16A.grid12=Credit Report No.
L140S16A.grid13=Handling officer
L140S16A.grid14=Nature
L140S16A.grid15=Printing Required
L140S16A.grid16=Related Party's UBN
L140S16A.grid17=Related Party's Name
L140S16A.grid18=Relationship With Borrower
L140S16A.grid19=Contribution
L140S16A.grid20=Credit Limit
L140S16A.grid21=Outstanding Balance
L140S16A.grid22=Current Deposit
L140S16A.grid23=Applicant
L140S16A.grid24=Approval Date
L140S16A.grid25=Document Status
L140S16A.grid26=Credit Report No.
L140S16A.grid27=Handling officer
L140S16A.grid28=Nature
L140S16A.grid29=Unified Business Number
L140S16A.grid30=Customer Name
L140S16A.grid31=Signature Date
L140S16A.grid32=Case No.
L140S16A.grid33=Credit Limit Serial Number
L140S16A.grid34=Document Status
L140S16A.grid35=Applied Credit Limit
L140S16A.grid36=Credit Line
L140S16A.grid1=Standalone
L140S16A.grid2=Consolidated
L140S16A.grid37=Principal Borrower
L140S16A.grid38=Date Created
L140S16A.grid39=Document Status
L140S16A.grid40=Approval date
L140S16A.grid41=Document Creator
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8b66\u544a\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L140S16A.alert1=\u5c1a\u672a\u9078\u53d6\u8cc7\u6599!
L140S16A.alert2=\u6c92\u6709\u9078\u64c7\u8cc7\u6599\uff01\u8acb\u9078\u64c7\u4e00\u7b46\u8cc7\u6599...
L140S16A.alert3=\u6587\u4ef6\u300c\u6c92\u6709\u300d\u88ab\u53d6\u6d88\uff01
L140S16A.alert4=\u6587\u4ef6\u300c\u6c92\u6709\u300d\u88ab\u6062\u5fa9\uff01
L140S16A.alert5=\u6587\u4ef6\u300c\u6c92\u6709\u300d\u88ab\u522a\u9664\uff01
L140S16A.alert6=\u6c92\u6709\u9078\u64c7\u501f\u6b3e\u4eba\uff01\u8acb\u9078\u64c7\u4e00\u7b46\u501f\u6b3e\u4eba
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u78ba\u8a8d\u8996\u7a97\u8a0a\u606f\u5167\u5bb9
#==================================================
L140S16A.confirm1=\u662f\u5426\u78ba\u5b9a\u8981\u53d6\u6d88\u540d\u55ae\uff1f
L140S16A.confirm2=\u662f\u5426\u78ba\u5b9a\u8981\u6062\u5fa9\u5df2\u53d6\u6d88\u4e4b\u5217\u5370\u95dc\u4fc2\u6236\u540d\u55ae\uff1f
L140S16A.confirm3=\u57f7\u884c\u522a\u9664\u5f8c\u5247\u7121\u6cd5\u518d\u56de\u5fa9\uff0c\u78ba\u5b9a\u8981\u522a\u9664\u300c\u6240\u6709\u300d\u95dc\u4fc2\u6236\u5f80\u4f86\u5f59\u7e3d\u6587\u4ef6\uff1f
#==================================================
# \u5f80\u4f86\u5f59\u7e3dThickBox\u6309\u9215\u8207\u6a19\u984c\u540d\u7a31
#==================================================
L140S16A.thickbox1=\u78ba\u5b9a
L140S16A.thickbox2=\u53d6\u6d88
L140S16A.thickbox3=\u5132\u5b58
L140S16A.thickbox4=\u522a\u9664
L140S16A.thickbox5=\u95dc\u9589
L140S16A.thickbox6=\u501f\u6b3e\u4eba\u9078\u64c7
L140S16A.thickbox7=\u96e2\u958b
L140S16A.thickbox8=\u8acb\u8f38\u5165\u67e5\u8a62\u671f\u9593\u8d77\u8a16\u5e74\u6708(YYYY MM \u683c\u5f0f)
L140S16A.thickbox9=\u767b\u9304\u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86
L140S16A.thickbox10=\u91cd\u65b0\u5f15\u9032
L140S16A.thickbox11=\u65b0\u589e\u5217\u5370\u95dc\u4fc2\u6236\u540d\u55ae
L140S16A.thickbox12=\u8cc7\u672c\u9069\u8db3\u7387
L140S16A.thickbox13=\u8a08\u7b97
L140S16A.thickbox14=\u4e3b\u8981\u6558\u4f5c\u689d\u4ef6\u5167\u5bb9
L140S16A.thickbox15=\u984d\u5ea6\u660e\u7d30\u8868\u9078\u64c7
L140S16A.thickbox16=\u5fb5\u4fe1\u5831\u544a\u66f8\u9078\u64c7
L140S16A.thickbox17=\u8cc7\u4fe1\u7c21\u8868\u9078\u64c7
L140S16A.thickbox18=\u67e5\u8a62\u5206\u884c
L140S16A.thickbox19=\u5206\u884c\u4ee3\u78bc
L140S16A.thickbox20=\u6388\u4fe1\u984d\u5ea6\u7570\u52d5\u60c5\u5f62\u5167\u5bb9
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u8cc7\u6599\u57fa\u671f\u7d30\u90e8\u540d\u7a31
#==================================================
L140S16A.form1= \u5e73\u5747\u9918\u984d
L140S16A.form2= \u6709\u6548\u984d\u5ea6
L140S16A.form3= \u696d\u52d9\u91cf
L140S16A.form4= \u4fe1\u8a17\u8cc7\u7522\u9918\u984d
L140S16A.form5= \u624b\u7e8c\u8cbb\u6536\u5165
L140S16A.form6= \u6236\u6578
L140S16A.form7= \u5237\u5361\u91d1\u984d
L140S16A.form8= \u662f\u5426\u6709\u806f\u540d\u5361
L140S16A.form9=(\u542bEDI\u4ea4\u6613\u91cf)
#==================================================
# \u5f80\u4f86\u5f59\u7e3dLegend\u540d\u7a31
#==================================================
L140S16A.subindex1=\u7d9c\u5408\u8a55\u4f30\u53ca\u6558\u505a\u7406\u7531
L140S16A.subindex2=\u98a8\u96aa\u6b0a\u6578
L140S16A.subindex3=\u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86\u5f59\u7e3d
L140S16A.subindex4=\u95dc\u4fc2\u6236
L140S16A.subindex5=\u767b\u9304\u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86
L140S16A.subindex6=\u4e3b\u8981\u6558\u4f5c\u689d\u4ef6
L140S16A.subindex7=\u6388\u4fe1\u984d\u5ea6\u7570\u52d5\u60c5\u5f62
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6309\u9215\u540d\u7a31
#==================================================
L140S16A.btn1=\u5f15\u9032\u5fb5\u4fe1\u5831\u544a\u7d9c\u5408\u8a55\u4f30
L140S16A.btn2=\u9810\u89bd\u5217\u5370\u7d9c\u5408\u8a55\u4f30\u53ca\u6558\u505a\u7406\u7531
L140S16A.btn3=\u5f15\u9032\u984d\u5ea6\u660e\u7d30\u8868
L140S16A.btn4=\u5beb\u56de\u984d\u5ea6\u660e\u7d30\u8868
L140S16A.btn5=\u5217\u5370\u6b64\u9801
L140S16A.btn6=\u65b0\u589e\u5217\u5370\u95dc\u4fc2\u6236\u540d\u55ae
L140S16A.btn7=\u53d6\u6d88\u5217\u5370\u95dc\u4fc2\u6236\u540d\u55ae
L140S16A.btn8=\u6062\u5fa9\u5df2\u53d6\u6d88\u5217\u5370\u95dc\u4fc2\u6236\u540d\u55ae
L140S16A.btn9=\u5f15\u9032\u5404\u95dc\u4fc2\u6236\u5f80\u4f86\u5f59\u7e3d
L140S16A.btn10=\u8a08\u7b97\u96c6\u5718/\u95dc\u4fc2\u4f01\u696d\u5408\u8a08
L140S16A.btn11=\u5217\u5370
L140S16A.btn12=\u522a\u9664\u6240\u6709\u95dc\u4fc2\u6236\u5f80\u4f86\u5f59\u7e3d\u8cc7\u6599
L140S16A.btn13=\u67e5\u8a62
L140S16A.btn14=Re-import
L140S16A.btn15=\u5f15\u9032\u5c0d\u7167\u6388\u4fe1\u6236\u984d\u5ea6\u8cc7\u6599
L140S16A.btn16=\u6e05\u9664\u5168\u90e8
L140S16A.btn17=\u7522\u751f\u6558\u4f5c\u5167\u5bb9
L140S16A.btn18=\u522a\u9664\u6558\u4f5c\u5167\u5bb9
L140S16A.btn19=\u7522\u751f\u7570\u52d5\u60c5\u5f62
L140S16A.btn20=\u522a\u9664\u7570\u52d5\u60c5\u5f62
L140S16A.btn21=\u540c\u6b65\u984d\u5ea6\u660e\u7d30\u8868\u5217\u5370\u9806\u5e8f
#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u7d30\u90e8\u8cc7\u6599\u5176\u4ed6\u540d\u7a31
#==================================================
L140S16A.other1=\u203bPlease input the reason for change of lending term, industry prospect, overall business and financial evaluation, banking transactions, feasibility and rationality for continuing banking relationship...etc.
L140S16A.other2=Unit: thousand dollars
L140S16A.other3=Description: 1. Non-credit Guaranteed Cases: for every $1 billion of exposure, capital adequacy is calculated at 0.7/10000. (includes accounts receivable financing; the risk weight can be based on buyer's credit rating subject to case-by-case approval, and can be used to calculate exposure)
L140S16A.other4=Field Description: A. Credit Limit: multiple 20% to import negotiation limits. Derivatives need to be multiplied with proper risk conversion ratios.
L140S16A.other5=Description: 1. The data covers all bank customers. 2. If certain groups/companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly by inputting a Modification Notice. 3. If certain related companies do not appear as they should when performing "Import Related Account Banking Summary", please go to the e-Loan system and add accordingly. 4. The source of related company data is the same as Item 9 under the Case Report's Description tab; it only covers controlling and cross-investment relationships. 5. Salary account data was maintained in the data warehouse system since 2011/02. 6. The data warehouse updates last month's banking transactions on the 10th each month (which is different to DWC350, which updates immediately at the time data becomes available).
L140S16A.other6=Please input information on the borrower, representative, group, and related companies into Related Account's Banking Transactions With the Bank.
L140S16A.other7=The credit limit serial number is used as mean of control, and is not shown in printed reports.
L140S16A.other8=Importing is allowed only for within-authority Case Reports; for other types of Case Reports, please input manually
L140S16A.other9=If the Credit Facility Report specifies the repayment period under Other Terms & Conditions, the system will consolidate Other Terms & Conditions into the Tenor field
L140S16A.other10=Please select the Credit Facility Report from which to generate the List
L140S16A.other11=Please select 1 Credit Facility Report
L140S16A.other12=\u5176\u4ed6\u6558\u4f5c\u689d\u4ef6\u50c5\u70ba\u53c3\u8003\uff0c\u5217\u5370\u6642\u4e0d\u986f\u793a
L140S16A.other13=\u8acb\u9078\u64c7\u672c\u6b21\u6b32\u7522\u751f\u6388\u4fe1\u984d\u5ea6\u7570\u52d5\u60c5\u5f62\u4e4b\u501f\u6b3e\u4eba
L140S16A.other14=\u8acb\u65bc\u984d\u5ea6\u660e\u7d30\u8868\u5b8c\u6210\u8a08\u7b97\u6388\u4fe1\u984d\u5ea6\u5408\u8a08\u5f8c\uff0c\u518d\u57f7\u884c\u672c\u529f\u80fd
L140S16A.other15=\u4ee5\u4e0b\u64d4\u4fdd\u54c1\u8cc7\u8a0a\u50c5\u70ba\u5f15\u9032\u984d\u5ea6\u660e\u7d30\u8868\u8cc7\u6599\u4f9b\u53c3\u8003\uff0c\u5217\u5370\u6642\u4e0d\u986f\u793a
#J-110-0327_05097_B1001 Web e-Loan\u570b\u5167\u8207\u6d77\u5916\u6388\u4fe1\u7c3d\u5831\u66f8\u65b0\u589e\u984d\u5ea6\u6aa2\u8996\u8868
L140S16A.other16=\u672c\u6b04\u4f4d\u4fc2\u5448\u73fe\u65bc\u984d\u5ea6\u6aa2\u8996\u8868
L140S16A.other17=\u91dd\u5c0d\u6027\u8cea\u70ba\u300c\u4e0d\u8b8a\u300d\u4e4b\u984d\u5ea6\u5e8f\u865f\uff0c\u50c5\u7dad\u8b77\u8aaa\u660e\u6b04\u4f4d\u5373\u53ef
L140S16A.other18=\u984d\u5ea6\u660e\u7d30\u8868\u6027\u8cea\u9664\u300c\u4e0d\u8b8a\u300d\u4ee5\u5916\u8005\uff0c\u672c\u9805\u7dad\u8b77\u5167\u5bb9\u91d1\u6703\u5217\u5370\u65bc\u5e38\u8463\u6703\u63d0\u6848\u7a3f\u4e4b\u300c\u627f\u8fa6\u55ae\u4f4d\u7533\u8acb\u6558\u505a\u5167\u5bb9\u4e2d\u300d

#==================================================
# \u5f80\u4f86\u5f59\u7e3d\u6a19\u984c\u540d\u7a31
#==================================================
L140S16A.index1=Credit Guarantee
L140S16A.index2=Not Credit Guaranteed
L140S16A.index3=(C denotes credit guarantee)
L140S16A.index4=Related Companies Total
L140S16A.index5=Group Total
L140S16A.index6=Current Deposit
L140S16A.index7=Outstanding Balance
L140S16A.index8=Credit Limit
L140S16A.index9=Contribution
L140S16A.index10=Item
L140S16A.index11=Principal Borrower
L140S16A.index12=Unit: TWD1,000
L140S16A.index13=Data Inquiry Period
L140S16A.index14=Please select a customer
L140S16A.index15=UBN (excluding repeated serial number)
L140S16A.index16=Customer Status
L140S16A.index17=No. Of Records
L140S16A.index18=Amount
L140S16A.index19=Compiling In Progress
L140S16A.index20=Document Status
L140S16A.index21=Starting Year/Month
L140S16A.index22=Ending Year/Month
L140S16A.index23=Document Status
L140S16A.index24=Compiling In Progress
L140S16A.index25=Borrower
L140S16A.index26=Reference Borrower
L140S16A.index27=Collateral
L140S16A.index28=Interest Rate
L140S16A.index29=Credit period
L140S16A.index30=Year
L140S16A.index31=Months
L140S16A.index32=Others Terms
L140S16A.index33=Upload Time
L140S16A.createBY1=System Generated
L140S16A.createBY2=Manually Generated
L140S16A.prtFlag1=Print
L140S16A.prtFlag2=Do Not Print
#==================================================
# \u5f80\u4f86\u5f59\u7e3dradio\u540d\u7a31
#==================================================
L140S16A.radio1=DBU\u5ba2\u6236
L140S16A.radio2=OBU\u5ba2\u6236
L140S16A.radio3=\u6d77\u5916\u540c\u696d
L140S16A.radio4=\u6d77\u5916\u5ba2\u6236
L140S16A.radio5=\u662f
L140S16A.radio6=\u5426
L140S16A.radio7=\u55ae\u7368\u4e00\u9801
L140S16A.radio8=\u5408\u4f75\u5171\u9801
#==================================================
# \u5f80\u4f86\u5f59\u7e3dcheckbox\u540d\u7a31
#==================================================
L140S16A.checkbox1=\u501f\u6236
L140S16A.checkbox2=\u501f\u6236\u8ca0\u8cac\u4eba
L140S16A.checkbox3=\u96c6\u5718\u4f01\u696d\u5408\u8a08
L140S16A.checkbox4=\u95dc\u4fc2\u4f01\u696d\u5408\u8a08
L140S16A.checkbox5=\u96c6\u5718\u4f01\u696d
L140S16A.checkbox6=\u95dc\u4fc2\u4f01\u696d
#==================================================
# \u8cc7\u672c\u9069\u8db3\u7387\u5f71\u97ff\u6578\u8cc7\u6599\u6a94
#==================================================
L120S03A.oid=oid
L120S03A.mainId=Document Number (Case Report)
L120S03A.cntrMainId=Document Number (Credit Facility Report)
L120S03A.cntrNo=Credit Limit Serial Number
L120S03A.crdFlag=Credit Guaranteed/Not Credit Guaranteed
L120S03A.applyAmt=Original Applied Credit Limit
L120S03A.fcltAmt=Credit Limit
L120S03A.collAmt=Eligible Collateral Deduction
L120S03A.rskRatio=Risk Weight (apply remark for guarantor)
L120S03A.rskAmt1=Exposure Net Of Risk Mitigant
L120S03A.rskr1=Risk Weight Net Of Risk Mitigant
L120S03A.camt1=Utilized Capital (not required)
L120S03A.bisr1=% To Capital Adequacy Ratio
L120S03A.costr1=Funding Cost %
L120S03A.crdRatio=Credit Guarantee Percentage
L120S03A.rskMega=Bank's exposure A*(1-B) * B'
L120S03A.rskCrd=Credit Guaranteed Exposure (20% risk weight)
L120S03A.rskAmt2=Total Exposure Net Of Risk Mitigant
L120S03A.rskr2=Risk Weight Net Of Risk Mitigant
L120S03A.camt2=Utilized Capital (not required)
L120S03A.bisr2=% To Capital Adequacy Ratio
L120S03A.costr2=Funding cost for the credit limit (H)
L120S03A.creator=Originator's ID
L120S03A.createTime=Date Created
L120S03A.updater=Modifier's ID
L120S03A.updateTime=Date Of Change
L120S03A.crdRskRatio=Risk Weight(Not Credit Guaranteed)

#==================================================
# \u95dc\u4fc2\u6236\u65bc\u672c\u884c\u5404\u9805\u696d\u52d9\u5f80\u4f86\u6a94
#==================================================
L120S04A.oid=oid
L120S04A.mainId=Document Number
L120S04A.createBY=Document Generation Method
L120S04A.custId=Unified Business Number
L120S04A.dupNo=Repeat Serial No.
L120S04A.custName=Account name please select any Language input
L120S04A.typCd=Region/Department
L120S04A.custRelation=Relationship With The Borrower
L120S04A.prtFlag=Case Report Printing Remarks
L120S04A.queryDateS=Data Inquiry Start Date
L120S04A.queryDateE=Data Inquiry End Date
L120S04A.itemName=Business Transaction
L120S04A.memo=Base Period
L120S04A.dep=Deposit
L120S04A.depMemo=Deposits - Base Period
L120S04A.depTime=Current Deposit
L120S04A.depFixed=Fixed deposits
L120S04A.loan=Loan
L120S04A.loanQMemo=Loans - Credit Limit - Base Period
L120S04A.loanQuota=Balance
L120S04A.loanABMemo=Loans - Average Balance - Base Period
L120S04A.loanAvgBal=Average Outstanding Balance
L120S04A.loanAvgRate=Average Utilization
L120S04A.exchg=Foreign Currency
L120S04A.exchgMemo=Foreign Currency - Base Period
L120S04A.exchgImp=Import (transactions/amount)
L120S04A.exchgImpRec=Foreign Currency - Import (transactions)
L120S04A.exchgImpAmt=Foreign Currency - Import (amount)
L120S04A.exchgExp=Export (transactions/amount)
L120S04A.exchgExpRec=Foreign Currency - Export (transactions)
L120S04A.exchgExpAmt=Foreign Currency - Export (amount)
L120S04A.exchgOut=Outward Remittance (transactions/amount)
L120S04A.exchgOutRec=Foreign Currency - Outward Remittance (transactions)
L120S04A.exchgOutAmt=Foreign Currency - Outward Remittance (amount)
L120S04A.exchgIn=Inward Remittance (transactions/amount)
L120S04A.exchgInRec=Foreign Currency - Inward Remittance (transactions)
L120S04A.exchgInAmt=Foreign Currency - Inward Remittance (amount)
L120S04A.der=Derivative
L120S04A.derMemo=Derivative - Base Period
L120S04A.derOption=Options
L120S04A.derRateExchg=Interest Rate Swap
L120S04A.derCCS=Cross Currency Swap
L120S04A.derDraft=Derivative - Foreard Exchange
L120S04A.derSWAP=Forward Exchange (includes SWAP)
L120S04A.trust=Trust
L120S04A.trustMemo=Trust - Base Period
L120S04A.trustBond=Domestic/Offshore Funds & Bonds
L120S04A.trustFund=Fund Custody
L120S04A.trustSetAcct=Central Depository
L120S04A.trustSecurities=Trust - Securities Trust
L120S04A.trustREITs=Trust - Real Estate Trust
L120S04A.trustWelDep=Trust - Welfare & Savings Trust
L120S04A.trustOther=Other Trust
L120S04A.wealth=Wealth Management
L120S04A.wealthMemo=Wealth Management - Base Period
L120S04A.wealthTrust=Trust
L120S04A.wealthInsCom=Insurance Commission
L120S04A.wealthInvest=Dual Currency Investment
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMemo=Salary Account - Base Period
L120S04A.salaryRec=Number Of Salary Accounts
L120S04A.salaryFixed=Number Of Fixed Deposit Accounts
L120S04A.salary=Number Of Mortgage/Consumer Loan Accounts
L120S04A.salaryMortgage=Mortgage
L120S04A.salaryConsumption=Consumer Loan
L120S04A.salaryCard=Number Of Credit Card Holders
L120S04A.salaryNetwork=Number Of Personal Internet Banking Users
L120S04A.cardComMemo=Cards - Corporate Card - Base Period
L120S04A.card=Cards
L120S04A.cardCommercial=Corporate Card
L120S04A.cardNoneCommercial=Non-business cards
L120S04A.cardNoneCommercialMemo=Cards - Non-business cards - Base Period
L120S04A.cardBrnMemo=Cards - Co-brand Card - Base Period
L120S04A.cardCoBranded=Co-brand Card
L120S04A.GEB=GEB
L120S04A.GEBMemo=GEB - NTD/Foreign Currency - Base Period
L120S04A.GEBTWDRec=Number Of NTD Transactions
L120S04A.GEBOTHRec=Number Of Foreign Currency Transactions
L120S04A.GEBLCMemo=GEB - LC - Base Period
L120S04A.GEBLCRec=Number Of LC Transactions
L120S04A.profitMemo=Profit Contribution - Base Period
L120S04A.profit=Profit Contribution
L120S04A.creator=Originator's ID
L120S04A.createTime=Date Created
L120S04A.updater=Modifier's ID
L120S04A.updateTime=Date Of Change
#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u4e3b\u6a94
#==================================================
L120S16A.oid=oid
L120S16A.mainId=Document No.
L120S16A.custId=Tax ID number of the borrower of the case
L120S16A.dupNo=Duplicate serial number for the borrower of the case
L120S16A.cntrNo=Serial number of credit limit for the borrower of the case
L120S16A.custName=Borrower of the case
L120S16A.custId2=Verify tax ID number of the borrower
L120S16A.dupNo2=Verify duplicate serial number for the borrower
L120S16A.cntrNo2=Verify serial number of credit limit for the borrower
L120S16A.custName2=Verify the borrower
L120S16A.proPerty=Type
L120S16A.currentApplyCurr=Current credit limit-currency
L120S16A.currentApplyAmt=Current credit limit-amount
L120S16A.lnSubject=Credit Items
L120S16A.purpose=Purpose of fund
L120S16A.gutPercent=Guaranteed percentage
L120S16A.payDeadline=Period
L120S16A.guarantor=Guarantor
L120S16A.guarantorMemo=Remark of joint guarantor
L120S16A.proPerty2=Type
L120S16A.currentApplyCurr2=Current credit limit-currency
L120S16A.currentApplyAmt2=Current credit limit-amount
L120S16A.lnSubject2=Credit account
L120S16A.purpose2=Purpose of fund
L120S16A.gutPercent2=Guaranteed percentage
L120S16A.payDeadline2=Period
L120S16A.guarantor2=Joint guarantor
L120S16A.guarantorMemo2=Remark of joint guarantor
L120S16A.printMode=Printing mode
L120S16A.creator=Creator number
L120S16A.createTime=Date of creation
L120S16A.updater=Clerk number for the change
L120S16A.updateTime=Date of change
#J-110-0327_05097_B1001 Web e-Loan\u570b\u5167\u8207\u6d77\u5916\u6388\u4fe1\u7c3d\u5831\u66f8\u65b0\u589e\u984d\u5ea6\u6aa2\u8996\u8868
L120S16A.description=Description
#==================================================
# \u5229\u5bb3\u95dc\u4fc2\u4eba\u6388\u4fe1\u689d\u4ef6\u5c0d\u7167\u8868\u660e\u7d30\u6a94
#==================================================
L120S16B.oid=oid
L120S16B.mainId=\u6587\u4ef6\u7de8\u865f
L120S16B.custId=\u672c\u6848\u6388\u4fe1\u6236\u7d71\u7de8
L120S16B.dupNo=\u672c\u6848\u6388\u4fe1\u6236\u91cd\u8907\u5e8f\u865f
L120S16B.cntrNo=\u672c\u6848\u6388\u4fe1\u6236\u984d\u5ea6\u5e8f\u865f
L120S16B.type=Category
L120S16B.itemType=Category/Item
L120S16B.itemDscr=Description of Item
L120S16B.creator=Creator number
L120S16B.createTime=Date of creation
L120S16B.updater=Clerk number for the change
L120S16B.updateTime=Date of change
L120S16B.CurrentApplyCurrMsg=Credit requested
L120S16B.UseDeadlineMsg=Period
L120S16B.TotalEloanAmt=Total limit on syndicated loan
L120S16B.UsePrerequisites=Prerequisites for drawdown
L120S16B.IncreaseClause=Clause of limit increase

L140M01a.custName=\u501f\u6b3e\u4eba\u540d\u7a31
L140M01a.cntrNo=\u984d\u5ea6\u5e8f\u865f
L140M01a.cntrNoCom=\u5171\u7528\u984d\u5ea6\u5e8f\u865f
L140M01a.moneyAmt=\u73fe\u8acb\u984d\u5ea6
L140M01a.type=\u6027\u8cea
L140M01a.docStatus=\u6587\u4ef6\u72c0\u614b
L140M01a.branchId=\u5206\u884c\u5225

#==================================================
# \u6388\u4fe1\u984d\u5ea6\u7570\u52d5\u60c5\u5f62
#==================================================
L120S16C.custId=Tax ID number of the borrower
L120S16C.dupNo=Duplicate serial number for the borrower
L120S16C.cntrNo=Serial number of credit limit for the borrower
L120S16C.typCd=Borrower by area and department
L120S16C.custName=Account name
L120S16C.lvTotAmt=Previous limit
L120S16C.blTotAmt=Balance
L120S16C.incApplyTotAmt=Net increase/decrease
L120S16C.incApplyMemo=Description of limit increase/decrease
L120S16C.loanTotAmt=Total credit limit
L120S16C.assureTotAmt=Collateral
L120S16C.printSeq=Printing order
L120S16C.chkYN=Input data error inspection completed (Y/N)
L120S16C.creator=Creator number
L120S16C.createTime=Date of creation
L120S16C.updater=Clerk number for the change
L120S16C.updateTime=Date of change
L120S16C.expMemo=Remarks
L120S16C.other=If the amount is reduced, please add brackets, e.g., (TWD10,000,000)
L120S16C.other1=(1) Please give a brief description of the credit account that is not included in the total credit limit;
L120S16C.other2=(2) Please indicate whether the collateral is provided to the credit account to strengthen the security, in addition to the security of the secured limit.  (A brief description is sufficient without exceeding 200 words/characters)
L120S16C.other3=(3) In the case of an interested party, when [total credit limit] is greater than the [collateral], please specify the reasons for whether there is a secured credit account
L120S16C.other4=Reminders:
L120S16C.other5=1. The limit above has not been included in the full fixed deposit guarantee/export bills/account receivable export non-recourse limit of TWDXXX, XXX,000
L120S16C.other6=2. The property collateral for long-term loans is about TWDXXX,000, as strengthened security for short-term comprehensive limits;
L120S16C.other7=3. The guaranteed amount receivable is TWDXXX,000, and 1\u201330% of the amount is pledged as collateral.
L120S16C.other8=4. The land in XXX section, Taichung, pledged as collateral for financing is TWDXXX,000 as the joint collateral for the construction financing limit of the case.
L120S16C.other9=5. 90%\u2013110% of the mid-term limit of TWDXXX,000 drawn down is provided for bills receivable as the source of repayment
L120S16C.other10=6. The date of approving the short-term comprehensive limit (credit account) of TWDXXX,000 on (YYYMMDD) is earlier than the date of becoming a stakeholder on (YYYY/MM/DD)

#==================================================
# \u501f\u6b3e\u4eba\u4e3b\u6a94
#==================================================
l120s01a.oid=oid
l120s01a.mainid=\u6587\u4ef6\u7de8\u865f
l120s01a.custid=\u7d71\u4e00\u7de8\u865f
l120s01a.dupno=\u91cd\u8986\u5e8f\u865f
l120s01a.custname=\u540d\u3000\u3000\u7a31
l120s01a.custname2=\u501f\u6b3e\u4eba\u540d\u7a31
l120s01a.doctype=\u4f01/\u500b\u91d1
l120s01a.typcd=DBU\uff0fOBU\uff0f\u6d77\u5916
l120s01a.localCustId=\u7576\u5730\u8b58\u5225ID
l120s01a.custno=\u5ba2\u6236\u7de8\u865f
l120s01a.keyman=\u4e3b\u8981\u501f\u6b3e\u4eba
l120s01a.custrlt=\u8207\u4e3b\u8981\u501f\u6b3e\u4eba\u95dc\u4fc2
L120S01A.custRlt=\u8207\u4e3b\u8981\u501f\u6b3e\u4eba\u95dc\u4fc2
l120s01a.custpos=\u76f8\u95dc\u8eab\u4efd
L120S01A.custPos=\u76f8\u95dc\u8eab\u4efd
l120s01a.newCustFlag=\u65b0\u5f80\u4f86\u5ba2\u6236\u8a3b\u8a18