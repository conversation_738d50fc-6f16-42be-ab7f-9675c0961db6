/* 
 * CLS1161S02CPanel.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.cls.panels;

import org.springframework.ui.ModelMap;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.panels.Panel;
import com.mega.eloan.lms.cls.common.ClsUtil;
import com.mega.eloan.lms.model.C160M01A;

import tw.com.jcs.common.Util;


/**<pre>
 * 動審表-產品種類Panel
 * </pre>
 * @since  2013/1/3
 * <AUTHOR>
 * @version <ul>
 *           <li>2013/1/3,Fantasy,new
 *          </ul>
 */
public class CLS1161S02CPanel extends Panel {

	private static final long serialVersionUID = 1L;

	private C160M01A c160m01a;

	/**
	 * @param id
	 * @param c160m01a
	 */
	public CLS1161S02CPanel(String id, C160M01A c160m01a) {
		super(id);
		this.c160m01a = c160m01a;
	}

	@Override
	public void processPanelData(ModelMap model, PageParameters params) {
		super.processPanelData(model, params);

		String rptId = "";
		if(c160m01a!=null){
			rptId = Util.trim(c160m01a.getRptId()); 
		}
		
		boolean show_dMonth_dRate_V20190920 = false;
		boolean show_dMonth_dRate_UNKNOWN = false;
		if(Util.equals(ClsUtil.C160M01A_RPTID_V20190920, rptId)){
			show_dMonth_dRate_V20190920 = true;
		}else{
			show_dMonth_dRate_UNKNOWN = true;	
		}
		
		model.addAttribute("show_dMonth_dRate_V20190920",
				show_dMonth_dRate_V20190920);
		model.addAttribute("show_dMonth_dRate_UNKNOWN",
				show_dMonth_dRate_UNKNOWN);
	}
}
