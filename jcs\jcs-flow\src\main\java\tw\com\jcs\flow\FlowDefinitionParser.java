package tw.com.jcs.flow;

import java.io.IOException;
import java.io.InputStream;

import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

/**
 * <pre>
 * 流程定義資訊解析
 * </pre>
 * 
 * @since 2022年12月22日
 * <AUTHOR> @version
 *          <ul>
 *          <li>2022年12月22日
 *          </ul>
 */
public interface FlowDefinitionParser {

    /**
     * 解析流程定義檔
     * 
     * @param source
     *            來源XML實體
     * @return
     * @throws IOException
     * @throws SAXException
     */
    FlowDefinition parse(InputSource source) throws IOException, SAXException;

    /**
     * 解析流程定義檔
     * 
     * @param stream
     *            流程InputStream實體
     * @return
     * @throws IOException
     * @throws SAXException
     */
    FlowDefinition parse(InputStream stream) throws IOException, SAXException;

    /**
     * 解析流程定義檔
     * 
     * @param xml型式的流程定義字串
     */
    FlowDefinition parse(String xml) throws IOException, SAXException;

}