package com.mega.eloan.lms.mfaloan.bean;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import tw.com.iisi.cap.model.GenericBean;

/** 消金內部評等檔 **/
public class ELF675 extends GenericBean {

	private static final long serialVersionUID = 1L;

	/** 額度序號 **/
	@Size(max=12)
	@Column(name="ELF675_CONTRACT", length=12, columnDefinition="CHAR(12)", nullable=false,unique = true)
	private String elf675_contract;

	/** 分行別 **/
	@Size(max=3)
	@Column(name="ELF675_BRN", length=3, columnDefinition="CHAR(03)", nullable=false,unique = true)
	private String elf675_brn;

	/** NOTES文件編號 **/
	@Size(max=32)
	@Column(name="ELF675_NOTEID", length=32, columnDefinition="CHAR(32)", nullable=false,unique = true)
	private String elf675_noteid;

	/** 客戶統編 **/
	@Size(max=10)
	@Column(name="ELF675_CUSTID", length=10, columnDefinition="CHAR(10)", nullable=false,unique = true)
	private String elf675_custid;

	/** 重複序號 **/
	@Size(max=1)
	@Column(name="ELF675_DUPNO", length=1, columnDefinition="CHAR(01)", nullable=false,unique = true)
	private String elf675_dupno;

	/** 評等模型類別 **/
	@Size(max=1)
	@Column(name="ELF675_MOWTYPE", length=1, columnDefinition="CHAR(01)", nullable=false,unique = true)
	private String elf675_mowtype;

	/** 模型版本-大版 **/
	@Column(name="ELF675_MOWVER1", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer elf675_mowver1;

	/** 模型版本-小版 **/
	@Column(name="ELF675_MOWVER2", columnDefinition="DECIMAL(5,0)", nullable=false,unique = true)
	private Integer elf675_mowver2;

	/** JCIC查詢日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF675_JCIC_DATE", columnDefinition="DATE", nullable=false,unique = true)
	private Date elf675_jcic_date;

	/** 帳號 **/
	@Size(max=14)
	@Column(name="ELF675_LOAN_NO", length=14, columnDefinition="CHAR(14)", nullable=false,unique = true)
	private String elf675_loan_no;

	/** 主款人統編 **/
	@Size(max=10)
	@Column(name="ELF675_CUST_KEY", length=10, columnDefinition="CHAR(10)")
	private String elf675_cust_key;

	/** 相關身分 **/
	@Size(max=1)
	@Column(name="ELF675_LNGEFLAG", length=1, columnDefinition="CHAR(01)")
	private String elf675_lngeflag;

	/** varA **/
	@Column(name="ELF675_BASE_A", columnDefinition="DECIMAL(6,2)")
	private BigDecimal elf675_base_a;

	/** varB **/
	@Column(name="ELF675_BASE_B", columnDefinition="DECIMAL(8,4)")
	private BigDecimal elf675_base_b;

	/** 常數項 **/
	@Column(name="ELF675_BASE_S", columnDefinition="DECIMAL(12,8)")
	private BigDecimal elf675_base_s;

	/** 基準底分 **/
	@Column(name="ELF675_BASE_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal elf675_base_score;

	/** 加總各變量得分 **/
	@Column(name="ELF675_TOTAL_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal elf675_total_score;

	/** 初始評分 **/
	@Column(name="ELF675_INIT_SCORE", columnDefinition="DECIMAL(14,8)")
	private BigDecimal elf675_init_score;

	/** 預測壞率 **/
	@Column(name="ELF675_PREDICT_RAT", columnDefinition="DECIMAL(14,8)")
	private BigDecimal elf675_predict_rat;

	/** 初始評等 **/
	@Column(name="ELF675_INIT_RATING", columnDefinition="DECIMAL(2,0)")
	private Integer elf675_init_rating;

	/** 調整評等 **/
	@Column(name="ELF675_ADJ_RATING", columnDefinition="DECIMAL(2,0)")
	private Integer elf675_adj_rating;

	/** 最終評等 **/
	@Column(name="ELF675_FINAL_RATE", columnDefinition="DECIMAL(2,0)")
	private Integer elf675_final_rate;

	/** 出現聯徵特殊負面資訊 **/
	@Size(max=1)
	@Column(name="ELF675_JCIC_WARNFG", length=1, columnDefinition="CHAR(01)")
	private String elf675_jcic_warnfg;

	/** 本案為最終採用之關係人評等 **/
	@Size(max=1)
	@Column(name="ELF675_FINAL_RATFG", length=1, columnDefinition="CHAR(01)")
	private String elf675_final_ratfg;

	/** 刪除本筆紀錄原因 **/
	@Size(max=2)
	@Column(name="ELF675_DEL_REASON", length=2, columnDefinition="CHAR(02)")
	private String elf675_del_reason;

	/** 刪除原因為其他時之理由文字 **/
	@Size(max=210)
	@Column(name="ELF675_REJECT_TXT", length=210, columnDefinition="CHAR(210)")
	private String elf675_reject_txt;

	/** 文件狀態 **/
	@Size(max=2)
	@Column(name="ELF675_DOCSTATUS", length=2, columnDefinition="CHAR(02)")
	private String elf675_docstatus;

	/** 違約機率 **/
	@Column(name="ELF675_DR", columnDefinition="DECIMAL(7,4)")
	private BigDecimal elf675_dr;

	/** 違約機率－預估１年期 **/
	@Column(name="ELF675_DR_1YR", columnDefinition="DECIMAL(7,4)")
	private BigDecimal elf675_dr_1yr;

	/** 上傳資料日期 **/
	@Temporal(TemporalType.DATE)
	@Column(name="ELF675_DATA_SRC_DT", columnDefinition="DATE")
	private Date elf675_data_src_dt;

	/** 更新人員 **/
	@Size(max=8)
	@Column(name="ELF675_UPDATER", length=8, columnDefinition="CHAR(8)")
	private String elf675_updater;

	/** 更新時間 **/
	@Column(name="ELF675_TMESTAMP", columnDefinition="TIMESTAMP")
	private Timestamp elf675_tmestamp;

	/** 卡友貸註記 **/
	@Size(max=1)
	@Column(name="ELF675_C_FLAG", length=1, columnDefinition="CHAR(1)")
	private String elf675_c_flag;
	
	/** 擔保品是否為房屋 **/
	@Size(max=1)
	@Column(name="ELF675_COLL_HOUSE", length=1, columnDefinition="CHAR(1)")
	private String elf675_coll_house;
	
	/** 取得額度序號 **/
	public String getElf675_contract() {
		return this.elf675_contract;
	}
	/** 設定額度序號 **/
	public void setElf675_contract(String value) {
		this.elf675_contract = value;
	}

	/** 取得分行別 **/
	public String getElf675_brn() {
		return this.elf675_brn;
	}
	/** 設定分行別 **/
	public void setElf675_brn(String value) {
		this.elf675_brn = value;
	}

	/** 取得NOTES文件編號 **/
	public String getElf675_noteid() {
		return this.elf675_noteid;
	}
	/** 設定NOTES文件編號 **/
	public void setElf675_noteid(String value) {
		this.elf675_noteid = value;
	}

	/** 取得客戶統編 **/
	public String getElf675_custid() {
		return this.elf675_custid;
	}
	/** 設定客戶統編 **/
	public void setElf675_custid(String value) {
		this.elf675_custid = value;
	}

	/** 取得重複序號 **/
	public String getElf675_dupno() {
		return this.elf675_dupno;
	}
	/** 設定重複序號 **/
	public void setElf675_dupno(String value) {
		this.elf675_dupno = value;
	}

	/** 取得評等模型類別 **/
	public String getElf675_mowtype() {
		return this.elf675_mowtype;
	}
	/** 設定評等模型類別 **/
	public void setElf675_mowtype(String value) {
		this.elf675_mowtype = value;
	}

	/** 取得模型版本-大版 **/
	public Integer getElf675_mowver1() {
		return this.elf675_mowver1;
	}
	/** 設定模型版本-大版 **/
	public void setElf675_mowver1(Integer value) {
		this.elf675_mowver1 = value;
	}

	/** 取得模型版本-小版 **/
	public Integer getElf675_mowver2() {
		return this.elf675_mowver2;
	}
	/** 設定模型版本-小版 **/
	public void setElf675_mowver2(Integer value) {
		this.elf675_mowver2 = value;
	}

	/** 取得JCIC查詢日期 **/
	public Date getElf675_jcic_date() {
		return this.elf675_jcic_date;
	}
	/** 設定JCIC查詢日期 **/
	public void setElf675_jcic_date(Date value) {
		this.elf675_jcic_date = value;
	}

	/** 取得帳號 **/
	public String getElf675_loan_no() {
		return this.elf675_loan_no;
	}
	/** 設定帳號 **/
	public void setElf675_loan_no(String value) {
		this.elf675_loan_no = value;
	}

	/** 取得主款人統編 **/
	public String getElf675_cust_key() {
		return this.elf675_cust_key;
	}
	/** 設定主款人統編 **/
	public void setElf675_cust_key(String value) {
		this.elf675_cust_key = value;
	}

	/** 取得相關身分 **/
	public String getElf675_lngeflag() {
		return this.elf675_lngeflag;
	}
	/** 設定相關身分 **/
	public void setElf675_lngeflag(String value) {
		this.elf675_lngeflag = value;
	}

	/** 取得varA **/
	public BigDecimal getElf675_base_a() {
		return this.elf675_base_a;
	}
	/** 設定varA **/
	public void setElf675_base_a(BigDecimal value) {
		this.elf675_base_a = value;
	}

	/** 取得varB **/
	public BigDecimal getElf675_base_b() {
		return this.elf675_base_b;
	}
	/** 設定varB **/
	public void setElf675_base_b(BigDecimal value) {
		this.elf675_base_b = value;
	}

	/** 取得常數項 **/
	public BigDecimal getElf675_base_s() {
		return this.elf675_base_s;
	}
	/** 設定常數項 **/
	public void setElf675_base_s(BigDecimal value) {
		this.elf675_base_s = value;
	}

	/** 取得基準底分 **/
	public BigDecimal getElf675_base_score() {
		return this.elf675_base_score;
	}
	/** 設定基準底分 **/
	public void setElf675_base_score(BigDecimal value) {
		this.elf675_base_score = value;
	}

	/** 取得加總各變量得分 **/
	public BigDecimal getElf675_total_score() {
		return this.elf675_total_score;
	}
	/** 設定加總各變量得分 **/
	public void setElf675_total_score(BigDecimal value) {
		this.elf675_total_score = value;
	}

	/** 取得初始評分 **/
	public BigDecimal getElf675_init_score() {
		return this.elf675_init_score;
	}
	/** 設定初始評分 **/
	public void setElf675_init_score(BigDecimal value) {
		this.elf675_init_score = value;
	}

	/** 取得預測壞率 **/
	public BigDecimal getElf675_predict_rat() {
		return this.elf675_predict_rat;
	}
	/** 設定預測壞率 **/
	public void setElf675_predict_rat(BigDecimal value) {
		this.elf675_predict_rat = value;
	}

	/** 取得初始評等 **/
	public Integer getElf675_init_rating() {
		return this.elf675_init_rating;
	}
	/** 設定初始評等 **/
	public void setElf675_init_rating(Integer value) {
		this.elf675_init_rating = value;
	}

	/** 取得調整評等 **/
	public Integer getElf675_adj_rating() {
		return this.elf675_adj_rating;
	}
	/** 設定調整評等 **/
	public void setElf675_adj_rating(Integer value) {
		this.elf675_adj_rating = value;
	}

	/** 取得最終評等 **/
	public Integer getElf675_final_rate() {
		return this.elf675_final_rate;
	}
	/** 設定最終評等 **/
	public void setElf675_final_rate(Integer value) {
		this.elf675_final_rate = value;
	}

	/** 取得出現聯徵特殊負面資訊 **/
	public String getElf675_jcic_warnfg() {
		return this.elf675_jcic_warnfg;
	}
	/** 設定出現聯徵特殊負面資訊 **/
	public void setElf675_jcic_warnfg(String value) {
		this.elf675_jcic_warnfg = value;
	}

	/** 取得本案為最終採用之關係人評等 **/
	public String getElf675_final_ratfg() {
		return this.elf675_final_ratfg;
	}
	/** 設定本案為最終採用之關係人評等 **/
	public void setElf675_final_ratfg(String value) {
		this.elf675_final_ratfg = value;
	}

	/** 取得刪除本筆紀錄原因 **/
	public String getElf675_del_reason() {
		return this.elf675_del_reason;
	}
	/** 設定刪除本筆紀錄原因 **/
	public void setElf675_del_reason(String value) {
		this.elf675_del_reason = value;
	}

	/** 取得刪除原因為其他時之理由文字 **/
	public String getElf675_reject_txt() {
		return this.elf675_reject_txt;
	}
	/** 設定刪除原因為其他時之理由文字 **/
	public void setElf675_reject_txt(String value) {
		this.elf675_reject_txt = value;
	}

	/** 取得文件狀態 **/
	public String getElf675_docstatus() {
		return this.elf675_docstatus;
	}
	/** 設定文件狀態 **/
	public void setElf675_docstatus(String value) {
		this.elf675_docstatus = value;
	}

	/** 取得違約機率 **/
	public BigDecimal getElf675_dr() {
		return this.elf675_dr;
	}
	/** 設定違約機率 **/
	public void setElf675_dr(BigDecimal value) {
		this.elf675_dr = value;
	}

	/** 取得違約機率－預估１年期 **/
	public BigDecimal getElf675_dr_1yr() {
		return this.elf675_dr_1yr;
	}
	/** 設定違約機率－預估１年期 **/
	public void setElf675_dr_1yr(BigDecimal value) {
		this.elf675_dr_1yr = value;
	}

	/** 取得上傳資料日期 **/
	public Date getElf675_data_src_dt() {
		return this.elf675_data_src_dt;
	}
	/** 設定上傳資料日期 **/
	public void setElf675_data_src_dt(Date value) {
		this.elf675_data_src_dt = value;
	}

	/** 取得更新人員 **/
	public String getElf675_updater() {
		return this.elf675_updater;
	}
	/** 設定更新人員 **/
	public void setElf675_updater(String value) {
		this.elf675_updater = value;
	}

	/** 取得更新時間 **/
	public Timestamp getElf675_tmestamp() {
		return this.elf675_tmestamp;
	}
	/** 設定更新時間 **/
	public void setElf675_tmestamp(Timestamp value) {
		this.elf675_tmestamp = value;
	}
	
	/** 取得卡友貸註記 **/
	public String getElf675_c_flag() {
		return elf675_c_flag;
	}
	/** 設定卡友貸註記 **/
	public void setElf675_c_flag(String elf675_c_flag) {
		this.elf675_c_flag = elf675_c_flag;
	}
	
	/** 取得擔保品是否為房屋 **/
	public String getElf675_coll_house() {
		return elf675_coll_house;
	}
	/** 設定擔保品是否為房屋 **/
	public void setElf675_coll_house(String elf675_coll_house) {
		this.elf675_coll_house = elf675_coll_house;
	}
}
