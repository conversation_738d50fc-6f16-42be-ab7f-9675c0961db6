package com.mega.eloan.lms.crs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;
import com.mega.eloan.lms.base.flow.enums.RetrialDocStatusEnum;

/**
 * <pre>
 * 個金覆審控制檔 - 已核准
 * </pre>
 * 
 * @since 2024/12/20
 * <AUTHOR> Assistant
 * @version <ul>
 *          <li>2024/12/20,AI Assistant,new - 簡化版本，只保留統編篩選功能
 *          </ul>
 */
@Controller
@RequestMapping("/crs/lms2430v03")
public class LMS2430V03Page extends AbstractEloanInnerView {

	public LMS2430V03Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		setGridViewStatus(RetrialDocStatusEnum.已核准);
		
		// 簡化版本：只保留篩選功能（借款人統編篩選）
		addToButtonPanel(model, LmsButtonEnum.Filter);
		
		renderJsI18N(LMS2430V01Page.class);
		renderJsI18N(LMS2430V03Page.class);
		model.addAttribute("hasHtml", false);
		model.addAttribute("loadScript", "loadScript('pagejs/crs/LMS2430V01Page');");
	}

}
