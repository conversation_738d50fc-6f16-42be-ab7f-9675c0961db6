package com.mega.eloan.lms.obsdb.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.mega.eloan.common.jdbc.AbstractOBSDBJdbcFactory;
import com.mega.eloan.common.jdbc.EloanColumnMapRowMapper;
import com.mega.eloan.lms.obsdb.service.ObsdbELF603Service;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import tw.com.iisi.cap.dao.utils.ISearch;
import tw.com.iisi.cap.util.CapDate;
import tw.com.jcs.common.TWNDate;
import tw.com.jcs.common.Util;

/**
 * <pre>
 * 海外貸後管理	ELF603-貸後追蹤分項紀錄檔
 * </pre>
 */
@Service
public class ObsdbELF603ServiceImpl extends AbstractOBSDBJdbcFactory implements
		ObsdbELF603Service {

	// 動審表覆核用
	@Override
	public void insertForInside(String brNo, String uid, String apptime, String cntrNo, int seqno, 
			String esgtype, String esgmodel, String tracond, String traprofik,
			int tramonth, String content, String updater, BigDecimal updatetime) {

		this.getJdbc(brNo).update(
				"ELF603.insertForInside",
				new Object[] {uid, apptime, cntrNo, seqno, esgtype, esgmodel, tracond, traprofik,
						tramonth, content, updater, updatetime});

	}

	@Override
	public void delByUidIdAppDate(String brNo, String uid, String apptime) {
		this.getJdbc(brNo).update("ELF603.delByUidIdAppDate",
				new Object[] { uid, apptime });
	}
	
	@Override
	public void delByUid(String brNo, String uid) {
		this.getJdbc(brNo).update("ELF603.delBUid",
				new Object[] { uid });
	}

	@Override
	public void delByUidIdAppDateCntrNo(String brNo, String uid, String apptime,
			String cntrNo) {
		this.getJdbc(brNo).update("ELF603.delByUidIdAppDateCntrNo",
				new Object[] { uid, apptime, cntrNo });
	}

	@Override
	public Map<String, Object> getElf603ByKey(String brNo, String from602sUid,
			String from602sApptime, String cntrno, String from602sSeqno) {
		return this.getJdbc(brNo).queryForMap("ELF603.getElf603ByKey",
				new String[] { from602sUid, from602sApptime, cntrno,
				from602sSeqno });
	}


}
