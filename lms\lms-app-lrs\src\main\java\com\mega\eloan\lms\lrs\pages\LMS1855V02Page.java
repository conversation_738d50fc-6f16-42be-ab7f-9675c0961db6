/* 
 * LMS1855V02Page.java
 * 
 * Copyright (c) 2011-2012 JC Software Services, Inc. 
 * 9F, No.30, Sec.1, Ming <PERSON> E. Rd., Taipei, Taiwan, R.O.C
 * All Rights Reserved.
 * 
 * Licensed Materials - Property of JC Software Services, Inc. 
 * 
 * This software is confidential and proprietary information of 
 * JC Software Services, Inc. (&quot;Confidential Information&quot;).
 */
package com.mega.eloan.lms.lrs.pages;

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import com.iisigroup.cap.component.PageParameters;
import com.mega.eloan.common.pages.AbstractEloanInnerView;
import com.mega.eloan.lms.base.enums.LmsButtonEnum;

/**<pre>
 * 
 * </pre>
 * @since  2012/2/15
 * <AUTHOR>
 * @version <ul>
 *           <li>2012/2/15,jessica,new
 *          </ul>
 */
@Controller
@RequestMapping(path = "/lrs/lms1855v02")
public class LMS1855V02Page extends AbstractEloanInnerView {

	public LMS1855V02Page() {
		super();
	}

	@Override
	public void execute(ModelMap model, PageParameters params) {
		addToButtonPanel(model, LmsButtonEnum.View, LmsButtonEnum.Filter);
		renderJsI18N(LMS1855V02Page.class);

	}// ;

	public String[] getJavascriptPath() {
		return new String[] { "pagejs/lrs/LMS1855V02Page.js" };
	}
}
