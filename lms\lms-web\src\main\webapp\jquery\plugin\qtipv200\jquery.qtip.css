/*
* qTip - The jQuery tooltip plugin
* http://craigsworks.com/projects/qtip/
*
* Version: 2.0.0pre
* Copyright 2009 <PERSON> - http://craigsworks.com
*
* Dual licensed under MIT or GPL Version 2 licenses
*   http://en.wikipedia.org/wiki/MIT_License
*   http://en.wikipedia.org/wiki/GNU_General_Public_License
*
* Date: Thu Aug 19 19:28:14 2010 +0100
*/

.ui-tooltip-accessible{
	left: -10000em !important;
	top: -10000em !important;
	display: block !important;
	visibility: hidden !important;
}

	/* IE6 ONLY - Width detection fix */
	* html .ui-tooltip-accessible{
		position: static !important;
		float: left !important;
	}

div.ui-tooltip, div.qtip{
	position: absolute;
	display: none;

	max-width: 280px;
	min-width: 50px;
}

	div.ui-tooltip .ui-tooltip-wrapper{
		position: relative;
		overflow: hidden;

		border-width: 3px;
		border-style: solid;
	}

	div.ui-tooltip .ui-tooltip-content{
		position: relative;
		padding: 5px 9px;

		text-align: left;
		word-wrap: break-word;
		overflow: hidden;
	}

	div.ui-tooltip .ui-tooltip-titlebar{
		position: relative;
		padding: 6px 35px 6px 10px;

		font-weight: bold;
	}

		/*! Default close button class */
		div.ui-tooltip .ui-tooltip-close{
			position: absolute;
			height: 16px;
			width: 16px;
			padding: 1px;
			right: 5px;
			top: 5px;

			cursor: pointer;
			line-height: 0px;
			font-size: 0px;
		}

			div.ui-tooltip .ui-tooltip-close .ui-icon{
				height: 16px;
				width: 16px;
				display: block;

				opacity: 0.8;
				-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
				filter: alpha(opacity=80);
			}
			div.ui-tooltip .ui-tooltip-close:hover .ui-icon{
				opacity: 1;
				-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
				filter: alpha(opacity=100);
			}

		/*! Custom button class */
		div.ui-tooltip .ui-tooltip-button{
			float: right;
		}

	/* Applied to 'focused' tooltips e.g. most recently displayed/interacted with */
	div.ui-tooltip-focus{

	}


/*! Default tooltip style */
div.ui-tooltip-wrapper{
	border-color: #F9E98E;
	background-color: #FBF7AA;
	color: #A27D35;
}

	div.ui-tooltip .ui-tooltip-titlebar{
		background-color: #F0DE7D;
	}

	div.ui-tooltip .ui-tooltip-close .ui-icon{
		background: #A27D35 url('images/close.png') no-repeat 50% 50%;
	}

/*! Plain tooltip style */
div.ui-tooltip-plain .ui-tooltip-wrapper{
	border-color: black;
	border-width: 1px;

	background-color: white;
	color: black;
}

	div.ui-tooltip-plain .ui-tooltip-titlebar{
		background-color: white;
	}

	div.ui-tooltip-plain .ui-tooltip-close .ui-icon{
		background: black;
	}

/*! Light tooltip style */
div.ui-tooltip-light .ui-tooltip-wrapper{
	border-color: #E2E2E2;
	background-color: white;
	color: #454545;
}

	div.ui-tooltip-light .ui-tooltip-titlebar{
		background-color: #f1f1f1;
	}

	div.ui-tooltip-light .ui-tooltip-close .ui-icon{
		background: #454545 url('images/close-light.png') no-repeat 50% 50%;
	}


/*! Dark tooltip style */
div.ui-tooltip-dark .ui-tooltip-wrapper{
	border-color: #303030;
	background-color: #505050;
	color: #f3f3f3;
}

	div.ui-tooltip-dark .ui-tooltip-titlebar{
		background-color: #C0C0C0;
	}

	div.ui-tooltip-dark .ui-tooltip-close .ui-icon{
		background: #f3f3f3 url('images/close-dark.png') no-repeat 50% 50%;
	}


/*! Red tooltip style */
div.ui-tooltip-red .ui-tooltip-wrapper{
	border-color: #D95252;
	background-color: #F78B83;
	color: #912323;
}

	div.ui-tooltip-red .ui-tooltip-titlebar{
		background-color: #F06D65;
	}

	div.ui-tooltip-red .ui-tooltip-close .ui-icon{
		background: #9C2F2F url('images/close-red.png') no-repeat 50% 50%;
	}


/*! Green tooltip style */
div.ui-tooltip-green .ui-tooltip-wrapper{
	border-color: #90D93F;
	background-color: #CAED9E;
	color: #3F6219;
}

	div.ui-tooltip-green .ui-tooltip-titlebar{
		background-color: #B0DE78;
	}

	div.ui-tooltip-green .ui-tooltip-close .ui-icon{
		background: #58792E url('images/close-green.png') no-repeat 50% 50%;
	}


/*! Blue tooltip style */
div.ui-tooltip-blue .ui-tooltip-wrapper{
	border-color: #ADD9ED;
	background-color: #E5F6FE;
	color: #5E99BD;
}

	div.ui-tooltip-blue .ui-tooltip-titlebar{
		background-color: #D0E9F5;
	}

	div.ui-tooltip-blue .ui-tooltip-close .ui-icon{
		background: #4D9FBF url('images/close-blue.png') no-repeat 50% 50%;
	}


/*! Add shadows to your tooltips in all supported browsers */
div.ui-tooltip-shadow{
  -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.3);

  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=5, OffY=5, Color='gray');
  -ms-filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=5, OffY=5, Color='gray')";
}

div.ui-tooltip-tip{
	position: absolute;
	margin: 0 auto;
	overflow: hidden;

	line-height: 0.1px;
	font-size: 1px;

	background: transparent !important;
	border: 0px !important;
	z-index: 10;
}

	div.ui-tooltip-tip *{
		line-height: 0.1px;
		font-size: 0.1px;
	}

	div.ui-tooltip-tip div{
		background: transparent !important;
		border: 0px dashed transparent;
	}

#qtip-blanket{
	position: absolute;
	left: -10000em;
	top: -10000em;

	background-color: black;
	cursor: pointer;

	opacity: 0.7;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
	filter: alpha(opacity=70);


	/* Set this to any below 15000 (default starting z-index for qTips) */
	z-index: 14999;
}

/*! Youtube tooltip style */
div.ui-tooltip-youtube .ui-tooltip-wrapper{
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;

	-webkit-box-shadow: 0 0 3px #333;
	-moz-box-shadow: 0 0 3px #333;
	box-shadow: 0 0 3px #333;

	border-color: #CCCCCC;
	border-width: 1px;

	color: white;
}

	div.ui-tooltip-youtube .ui-tooltip-close .ui-icon{
		background: #4D9FBF url('images/close-dark.png') no-repeat 50% 50%;
	}

	div.ui-tooltip-youtube .ui-tooltip-titlebar,
	div.ui-tooltip-youtube .ui-tooltip-content{
		background-color: rgba(0, 0, 0, 0.85);
	}

	/* IE6 Fallback */
	* html div.ui-tooltip-youtube-wrapper{
		background-color: black;
	}


/* jQuery TOOLS Tooltip style */
div.ui-tooltip-jtools .ui-tooltip-wrapper{
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
	border-radius: 2px;

	border-color: white;
	border-width: 2px;

	-webkit-box-shadow: 0 0 12px #333;
	-moz-box-shadow: 0 0 12px #333;
	box-shadow: 0 0 12px #333;

	background-color: rgba(0, 0, 0, 0.7);
	background-image: -moz-linear-gradient(top, #717171, #232323);
	background-image: -webkit-gradient(linear, left top, left bottom, from(#717171), to(#232323));
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#717171,endColorstr=#232323);
	-ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#717171,endColorstr=#232323)";

	color: white;
}

	div.ui-tooltip-jtools .ui-tooltip-titlebar,
	div.ui-tooltip-jtools .ui-tooltip-content{
		background: none;
	}

	div.ui-tooltip-jtools .ui-tooltip-close .ui-icon{
		background: black url('images/close-dark.png') no-repeat 50% 50%;
	}


/* Cluetip style */
div.ui-tooltip-cluetip .ui-tooltip-wrapper{
	border-width: 0;

	-webkit-box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.4);
	-moz-box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.4);
	box-shadow: 4px 4px 5px rgba(0, 0, 0, 0.4);
}

	div.ui-tooltip-cluetip .ui-tooltip-titlebar{
		background-color: #87876A;
		color: white;
	}
	div.ui-tooltip-cluetip .ui-tooltip-content{
		background-color: #D9D9C2;
		color: #111;
	}

